export interface Config {
  token: string;
  escalation_time: string;
  message_refresh_interval: string;
  session_timeout: string;
  session_timeout_warning: string;
  primary_nurse_id: string;
  primary_pharmacist_id: string;
  patient_approver_role_id: string;
  clinician_approver_role_id: string;
  chat_auto_translate: string;
  ga_tracking: string;
  message_forwarding_behaviour: string;
  watchtower_tracking: string;
  esign_api_baseurl: string;
  esign_client_id: string;
  esign_api_key: string;
  clinician_roles_beyond_working_hours: string;
  clinician_roles_on_working_hours: string;
  show_infusion_support: string;
  show_prototypes: string;
  home_infusion_start_time: string;
  home_infusion_end_time: string;
  show_support_and_feedback_form: string;
  infusion_support_clinician_roles: string;
  default_clinician_roles_available: string;
  message_escalation_behavior: string;
  no_clinician_message: string;
  infusion_support_where_is_my_delivery: string;
  infusion_support_when_is_my_nurse_coming: string;
  working_hour: string;
  default_escalation_users: string;
  default_clinician_roles_available_on_working_hour: string;
  no_clinician_message_on_working_hours: string;
  infusion_support_my_nurse_coming_on_working_hours: string;
  infusion_support_my_delivery_on_working_hours: string;
  infusion_support_clinician_roles_on_working_hours: string;
  toggle_chat_translation: string;
  show_form_features: string;
  allow_virtual_patient: string;
  form_approver_clinician: string;
  show_patient_supply_inventory: string;
  clinical_liaison: string;
  show_user_tagging: string;
  enable_configurable_faq: string;
  show_document_tagging: string;
  show_chat_history_to_new_participant: string;
  patient_signup_with_admin_approval: string;
  clinician_signup_with_admin_approval: string;
  supply_request_recipient_type: string;
  immediate_assistance_message: string;
  enable_filing_center: string;
  conversation_type: string;
  chat_role1: string;
  chat_role2: string;
  chat_role1_displayname: string;
  chat_role2_displayname: string;
  chat_role1_message_template: string;
  chat_role2_message_template: string;
  initiation_message: string;
  show_patient_status_dashboard: string;
  message_reply_timeout: string;
  patient_name_display: string;
  enable_external_integration: string;
  enable_where_is_my_delivery: string;
  enable_when_is_my_nurse_coming: string;
  enable_masked_discussion_group: string;
  masked_discussion_recipient_roles: string;
  wp_enroll_url: string;
  branch_start_time: string;
  branch_end_time: string;
  tenant_timezone: number;
  patient_reminder_time: string;
  patient_reminder_types: string;
  allow_prefilling_during_partial_enrollment: string;
  caregiver_sees_enrollment_invitation_data: string;
  show_chatwith_modal: string;
  staffs_aware_enroll_by_sms: string;
  staffs_aware_enroll_by_email: string;
  partner_patient_referral_initiation_introduction: string;
  partner_referral_form_tags: string;
  web_notification_hide_behaviour: string;
  enrollment_reminder_types: string;
  show_staff_supply_inventory: string;
  enable_forms: string;
  patient_referrals: string;
  url_expiration_days_in_autoenrollment: string;
  enable_double_verification: string;
  enable_user_enrollment: string;
  branch_working_days: string;
  enable_download_citus_health_connect: string;
  menu_display_behaviour_of_disabled_feature: string;
  default_category_of_chat_window_popup: string;
  archive_signature_files_to_fc_after_attachment: string;
  defaulth_fc_for_chatlogs: string;
  defaulth_filenameformat_for_chatlogs_fc: string;
  title_incoming_filing_center: string;
  title_outgoing_filing_center: string;
  enable_video_chat: string;
  enable_patient_intake: string;
  enable_activity_hub: string;
  filename_character_limit_of_fc: string;
  allow_multiple_organization: string;
  new_patient_chat_welcome_message: string;
  weekend_days: string[];
  associate_pdg_with_patient_id: string;
  field_label_of_patient_association_in_pdg: string;
  automatically_create_pdg_on_patient_enroll: string;
  member_roles_for_pdg_which_create_automatically: string;
  enable_form_auto_save: string;
  enable_form_save_draft_patient: string;
  enable_form_save_draft_staff: string;
  patient_message_sms_notifcation_beyond_branch_24hr: string;
  progress_note_integration_data_format: string;
  staff_message_sms_notifcation: string;
  enable_progress_note_integration: string;
  esi_code_for_patient_identity: string;
  esi_code_for_staff_identity: string;
  esi_code_for_staff_name: string;
  add_user_to_group_on_invite_to_chat_session: string;
  message_for_clinical_services_in_feedback_form: string;
  chie_admin_user: string;
  recipient_email_for_support_widget: string;
  view_other_branch_dashboard: string;
  enable_worklist_center: string;
  enable_chat_window_sidebar: string;
  show_education_training: string;
  enable_move_form: string;
  pn_webhook_endpoint: string;
  pn_callback_url: string;
  pn_base_url: string;
  pn_resource_url: string;
  esi_code_for_pn_clientid: string;
  generated_progress_note_content_format: string;
  progress_note_integration_mode: string;
  label_for_file_generation: string;
  enable_nursing_agencies_visibility_restrictions: string;
  cross_tenant_roles_for_pdg_create_automatically: string;
  na_manage_education_material_roles: string;
  na_manage_group_roles: string;
  default_staff_role: string;
  documet_exchange_mode: string;
  wh_endpoint: string;
  wh_autharization_key: string;
  wh_resourse_url: string;
  wh_callback_url: string;
  wh_endpoint_form: string;
  wh_resourse_url_form: string;
  wh_document_reference_integration_base_url: string;
  enable_integration_status_worklist: string;
  show_push_notification_for_chat_room_invite: string;
  na_patients_chat_with_pharmacy: string;
  na_staffs_chat_with_pharmacy: string;
  enable_collaborate_edit: string;
  save_as_draft_message_interval: string;
  organization_model: string;
  esi_code_for_clientid: string;
  esi_code_for_customerid: string;
  enable_delivery_center: string;
  esi_code_for_branchid: string;
  enable_multipart_mrn: string;
  pn_authorization_type: string;
  wh_integration_url: string;
  show_staffid_in_user_invite: string;
  make_mrn_field_mandatory_in_patient_invite: string;
  signature_serverside: string;
  message_for_clinical_services_in_patient_feedback: string;
  alternative_background_color_pdf_generated: string;
  enable_partial_invite_in_auto_enrollment: string;
  make_staffid_field_mandatory_in_staff_invite_page: string;
  consolo_baseurl: string;
  consolo_authorize: string;
  consolo_access_token: string;
  consolo_userInfo: string;
  consolo_clientId: string;
  consolo_scope: string;
  show_manage_alerts: string;
  enable_esi_value_in_document_category: string;
  default_inbound_fc: string;
  access_token_for_reading_inbound_document: string;
  default_staff_user_tag: string;
  default_partner_role: string;
  default_partner_user_tag: string;
  enable_auto_enrollment_integration_for_virtual_staff: string;
  enable_message_center: string;
  enable_message_flagging: string;
  patient_direct_link_callback_url: string;
  restrict_to_branch_hour: string;
  enable_sftp_integration: string;
  fax_queue_show_warning: string;
  flex_site_patients_can_chat_with_internal_staffs: string;
  enable_disclose_PHI: string;
  enable_direct_link: string;
  ignore_duplicate_checking_in_virtual_patient_onboarding: string;
  form_mobile_orientaion: string;
  pdg_member_add_behavior: string;
  enrollMessage: string;
  default_category_of_message_display: string;
  enable_mobile_web_app: string;
  enable_email_for_staff: string;
  enable_email_for_patient: string;
  enable_appless_model: string;
  enable_user_center: string;
  enable_verification_of_cell_and_mobile: string;
  app_short_link: string;
  enable_patient_drieven_flow_forms: string;
  form_assignment_management_source: string;
  allow_export_datatable: string;
  magiclink_token_expiration_time: string;
  elevioStaff: string;
  elevioPatient: string;
  elevioPartner: string;
  showElevio: string;
  ehr_callback_authorization_key: string;
  app_auth_link: string;
  mobilewebapp_auth_type: string;
  default_permission_to_allow_access_to_the_application: string;
  default_outgoing_filing_center_pn_integration: string;
  default_outgoing_filing_center_direct_linking: string;
  default_outgoing_filing_center_faxqueue: string;
  magiclink_verification_expiry_time: string;
  enable_support_widget_branding: string;
  support_widget_from_email: string;
  support_widget_email_color_code: string;
  magiclink_verification_token_expiration_time: string;
  enable_export_data: string;
  app_name: string;
  mobile_app_base_url: string;
  desktop_app_base_url: string;
  SMTP_domain: string;
  enable_patient_info_from_third_party_app: string;
  external_system_integrated: string;
  auxilary_physician_roles_for_manage_security_rules: string;
  esi_code_for_auxilary_physician: string;
  auxilary_physician_identifier_type: string;
  auxilary_nursing_agency_roles_for_manage_security_rules: string;
  esi_code_for_auxilary_nursing_agency: string;
  auxilary_nursing_agency_identifier_type: string;
  auxilary_physician_label: string;
  auxilary_nursing_agency_label: string;
  make_auxilary_physician_identifier_type_mandatory_in_partner_invite: string;
  make_auxilary_nursing_agency_identifier_type_mandatory_in_partner_invite: string;
  worklist_label_for_manage_security_rules: string;
  worklist_selected_for_manage_security_rules: string;
  auxilary_physician_worklist_label_for_manage_security_rules: string;
  auxilary_physician_worklist_selected_for_manage_security_rules: string;
  auxilary_nursing_agency_worklist_label_for_manage_security_rules: string;
  auxilary_nursing_agency_worklist_selected_for_manage_security_rules: string;
  chie_callback_auth_key: string;
  esi_code_for_partner_identity: string;
  enable_app_center: string;
  selected_apps: string;
  default_outgoing_filing_center_file_exchange: string;
  email_domain_name: string;
  enable_visit_schedule: string;
  worklist_selected_for_visit_schedule: string;
  enable_base64_encodedfile: string;
  default_patients_workflow: string;
  esi_code_for_alternate_contact: string;
  make_esi_code_mandatory_in_alternate_contact_invite: string;
  enable_multi_thread_pdg: string;
  staff_api_enable_staff_id_retrieval: string;
  staff_api_authentication_type: string;
  staff_api_authentication_username: string;
  staff_api_authentication_password: string;
  staff_api_staff_id_retrieval_url: string;
  staff_api_authentication_login_url: string;
  staff_api_company_id: string;
  staff_api_client_id: string;
  enable_base64_encodedfile_progress_note: string;
  enable_category_code_to_external_application: string;
  enable_exchange_of_esicode: string;
  response_type: string;
  enable_message_type_id_category_code: string;
  enable_staff_import: string;
  sso_service_type: string;
  worklist_selected_for_PAH: string;
  enable_masterdata_type: string;
  enable_exchange_of_discrete_data: string;
  enable_email_notify: string;
  enable_multisite: string;
  welcome_message_patient: string;
  chat_start_time: string;
  chat_start_period: string;
  chat_end_time: string;
  chat_end_period: string;
  User_escalated_messages: string;
  role_patient_chat: string;
  staff_escalation_members: string;
  member_role_pdg_patient_reg: string;
  available_staff_message: string;
  message_routing_type: string;
  cross_site_communication_for_messages: string;
  site_external_system_integration: string;
  default_outgoing_filing_center_faxq: string;
  default_outgoing_filing_center_notification: string;
  default_outgoing_filing_center_directlinking: string;
  default_outgoing_filing_center_progressnote: string;
  default_outgoing_filing_center_phi: string;
  default_outgoing_filing_center_formdiscretedata: string;
  default_sites: string;
  external_system_integration: string;
  ignore_mandatory_checking_integration: string;
  enable_mobile_pah: string;
  enable_appless_video_chat: string;
  enable_demographic_profile: string;
  enable_document_center: string;
  enable_alternate_username_login: string;
  enable_external_message_category_code_in_message_tag: string;
  enable_external_cateogry_code_in_form_types: string;
  enable_API_based_integration_for_message_type: string;
  document_category_code: string;
  api_integration_authorization_key: string;
  enable_API_based_integration_for_category_code: string;
  enable_API_based_integration_for_message_category: string;
  message_tag_type: string;
  message_tag_category: string;
  enable_external_message_type_id_in_message_tag: string;
  enable_external_message_typeId_in_form_types: string;
  default_file_format_document_exchange: string;
  default_ehr: string;
  form_send_mode: string;
  realm_key: string;
  enable_discrete_integration_status_worklist: string;
  enable_appless_messaging: string;
  esignature_audit_log: string;
  branded_appless_url: string;
  show_first_name_last_name_separate: string;
  branded_appless_forms_url: string;
  enable_external_transport_mode: string;
  external_transport_URL: string;
  external_transport_partner_token: string;
  external_transport_partner_authorization_type: string;
  form_integration_mode: string;
  enable_xss_security: string;
  enable_signature_iframe_workflow: string;
  signature_iFrame_reroute_URL: string;
  default_partner_category: string;
  hipaa_validation_staff_facing: string;
  hipaa_validation_practitioner_facing_send: string;
  default_outgoing_filing_center_cvl: string;
  enable_clinical_visit_log_integration: string;
  api_cvl_integration_end_point: string;
  api_cvl_integration_callback_end_point: string;
  cvl_round_off_value: string;
  enable_visit_entries_marked_complete: string;
  enable_visit_billing_details: string;
  notification_language: string;
  visit_patient_reminder_time: string;
  patient_reminder_checking_type: string;
  inbox_form_archive_remove_self: number;
  tenant_timezone_offset: string;
  tenant_timezoneName: string;
  tenant_timezoneNameValue: string;
  enable_sftp_integration_machform_value: string;
}

export interface ConfigReplica {
  token: string;
  escalation_time: string;
  message_refresh_interval: string;
  session_timeout: string;
  session_timeout_warning: string;
  primary_nurse_id: string;
  primary_pharmacist_id: string;
  patient_approver_role_id: string;
  clinician_approver_role_id: string;
  chat_auto_translate: string;
  ga_tracking: string;
  message_forwarding_behaviour: string;
  watchtower_tracking: string;
  esign_api_baseurl: string;
  esign_client_id: string;
  esign_api_key: string;
  clinician_roles_beyond_working_hours: string;
  clinician_roles_on_working_hours: string;
  show_infusion_support: string;
  show_prototypes: string;
  home_infusion_start_time: string;
  home_infusion_end_time: string;
  show_support_and_feedback_form: string;
  infusion_support_clinician_roles: string;
  default_clinician_roles_available: string;
  message_escalation_behavior: string;
  no_clinician_message: string;
  infusion_support_where_is_my_delivery: string;
  infusion_support_when_is_my_nurse_coming: string;
  working_hour: string;
  default_escalation_users: string;
  default_clinician_roles_available_on_working_hour: string;
  no_clinician_message_on_working_hours: string;
  infusion_support_my_nurse_coming_on_working_hours: string;
  infusion_support_my_delivery_on_working_hours: string;
  infusion_support_clinician_roles_on_working_hours: string;
  toggle_chat_translation: string;
  show_form_features: string;
  allow_virtual_patient: string;
  form_approver_clinician: string;
  show_patient_supply_inventory: string;
  clinical_liaison: string;
  show_user_tagging: string;
  enable_configurable_faq: string;
  show_document_tagging: string;
  show_chat_history_to_new_participant: string;
  patient_signup_with_admin_approval: string;
  clinician_signup_with_admin_approval: string;
  supply_request_recipient_type: string;
  immediate_assistance_message: string;
  enable_filing_center: string;
  conversation_type: string;
  chat_role1: string;
  chat_role2: string;
  chat_role1_displayname: string;
  chat_role2_displayname: string;
  chat_role1_message_template: string;
  chat_role2_message_template: string;
  initiation_message: string;
  show_patient_status_dashboard: string;
  message_reply_timeout: string;
  patient_name_display: string;
  enable_external_integration: string;
  enable_where_is_my_delivery: string;
  enable_when_is_my_nurse_coming: string;
  enable_masked_discussion_group: string;
  masked_discussion_recipient_roles: string;
  wp_enroll_url: string;
  branch_start_time: string;
  branch_end_time: string;
  tenant_timezone: number;
  patient_reminder_time: string;
  patient_reminder_types: string;
  allow_prefilling_during_partial_enrollment: string;
  caregiver_sees_enrollment_invitation_data: string;
  show_chatwith_modal: string;
  staffs_aware_enroll_by_sms: string;
  staffs_aware_enroll_by_email: string;
  partner_patient_referral_initiation_introduction: string;
  partner_referral_form_tags: string;
  web_notification_hide_behaviour: string;
  enrollment_reminder_types: string;
  show_staff_supply_inventory: string;
  enable_forms: string;
  patient_referrals: string;
  url_expiration_days_in_autoenrollment: string;
  enable_double_verification: string;
  enable_user_enrollment: string;
  branch_working_days: string;
  enable_download_citus_health_connect: string;
  menu_display_behaviour_of_disabled_feature: string;
  default_category_of_chat_window_popup: string;
  archive_signature_files_to_fc_after_attachment: string;
  defaulth_fc_for_chatlogs: string;
  defaulth_filenameformat_for_chatlogs_fc: string;
  title_incoming_filing_center: string;
  title_outgoing_filing_center: string;
  enable_video_chat: string;
  enable_patient_intake: string;
  enable_activity_hub: string;
  filename_character_limit_of_fc: string;
  allow_multiple_organization: string;
  new_patient_chat_welcome_message: string;
  weekend_days: string;
  associate_pdg_with_patient_id: string;
  field_label_of_patient_association_in_pdg: string;
  automatically_create_pdg_on_patient_enroll: string;
  member_roles_for_pdg_which_create_automatically: string;
  enable_form_auto_save: string;
  enable_form_save_draft_patient: string;
  enable_form_save_draft_staff: string;
  patient_message_sms_notifcation_beyond_branch_24hr: string;
  progress_note_integration_data_format: string;
  staff_message_sms_notifcation: string;
  enable_progress_note_integration: string;
  esi_code_for_patient_identity: string;
  esi_code_for_staff_identity: string;
  esi_code_for_staff_name: string;
  add_user_to_group_on_invite_to_chat_session: string;
  message_for_clinical_services_in_feedback_form: string;
  chie_admin_user: string;
  recipient_email_for_support_widget: string;
  view_other_branch_dashboard: string;
  enable_worklist_center: string;
  enable_chat_window_sidebar: string;
  show_education_training: string;
  enable_move_form: string;
  pn_webhook_endpoint: string;
  pn_callback_url: string;
  pn_base_url: string;
  pn_resource_url: string;
  esi_code_for_pn_clientid: string;
  generated_progress_note_content_format: string;
  progress_note_integration_mode: string;
  label_for_file_generation: string;
  enable_nursing_agencies_visibility_restrictions: string;
  cross_tenant_roles_for_pdg_create_automatically: string;
  na_manage_education_material_roles: string;
  na_manage_group_roles: string;
  default_staff_role: string;
  documet_exchange_mode: string;
  wh_endpoint: string;
  wh_autharization_key: string;
  wh_resourse_url: string;
  wh_callback_url: string;
  wh_endpoint_form: string;
  wh_resourse_url_form: string;
  wh_document_reference_integration_base_url: string;
  enable_integration_status_worklist: string;
  show_push_notification_for_chat_room_invite: string;
  na_patients_chat_with_pharmacy: string;
  na_staffs_chat_with_pharmacy: string;
  enable_collaborate_edit: string;
  save_as_draft_message_interval: string;
  organization_model: string;
  esi_code_for_clientid: string;
  esi_code_for_customerid: string;
  enable_delivery_center: string;
  esi_code_for_branchid: string;
  enable_multipart_mrn: string;
  pn_authorization_type: string;
  wh_integration_url: string;
  show_staffid_in_user_invite: string;
  make_mrn_field_mandatory_in_patient_invite: string;
  signature_serverside: string;
  message_for_clinical_services_in_patient_feedback: string;
  alternative_background_color_pdf_generated: string;
  enable_partial_invite_in_auto_enrollment: string;
  make_staffid_field_mandatory_in_staff_invite_page: string;
  consolo_baseurl: string;
  consolo_authorize: string;
  consolo_access_token: string;
  consolo_userInfo: string;
  consolo_clientId: string;
  consolo_scope: string;
  show_manage_alerts: string;
  enable_esi_value_in_document_category: string;
  default_inbound_fc: string;
  access_token_for_reading_inbound_document: string;
  default_staff_user_tag: string;
  default_partner_role: string;
  default_partner_user_tag: string;
  enable_auto_enrollment_integration_for_virtual_staff: string;
  enable_message_center: string;
  enable_message_flagging: string;
  patient_direct_link_callback_url: string;
  restrict_to_branch_hour: string;
  enable_sftp_integration: string;
  fax_queue_show_warning: string;
  flex_site_patients_can_chat_with_internal_staffs: string;
  enable_disclose_PHI: string;
  enable_direct_link: string;
  ignore_duplicate_checking_in_virtual_patient_onboarding: string;
  form_mobile_orientaion: string;
  pdg_member_add_behavior: string;
  enrollMessage: string;
  default_category_of_message_display: string;
  enable_mobile_web_app: string;
  enable_email_for_staff: string;
  enable_email_for_patient: string;
  enable_appless_model: string;
  enable_user_center: string;
  enable_verification_of_cell_and_mobile: string;
  app_short_link: string;
  enable_patient_drieven_flow_forms: string;
  form_assignment_management_source: string;
  allow_export_datatable: string;
  magiclink_token_expiration_time: string;
  elevioStaff: string;
  elevioPatient: string;
  elevioPartner: string;
  showElevio: string;
  ehr_callback_authorization_key: string;
  app_auth_link: string;
  mobilewebapp_auth_type: string;
  default_permission_to_allow_access_to_the_application: string;
  default_outgoing_filing_center_pn_integration: string;
  default_outgoing_filing_center_direct_linking: string;
  default_outgoing_filing_center_faxqueue: string;
  magiclink_verification_expiry_time: string;
  enable_support_widget_branding: string;
  support_widget_from_email: string;
  support_widget_email_color_code: string;
  magiclink_verification_token_expiration_time: string;
  enable_export_data: string;
  app_name: string;
  mobile_app_base_url: string;
  desktop_app_base_url: string;
  SMTP_domain: string;
  enable_patient_info_from_third_party_app: string;
  external_system_integrated: string;
  auxilary_physician_roles_for_manage_security_rules: string;
  esi_code_for_auxilary_physician: string;
  auxilary_physician_identifier_type: string;
  auxilary_nursing_agency_roles_for_manage_security_rules: string;
  esi_code_for_auxilary_nursing_agency: string;
  auxilary_nursing_agency_identifier_type: string;
  auxilary_physician_label: string;
  auxilary_nursing_agency_label: string;
  make_auxilary_physician_identifier_type_mandatory_in_partner_invite: string;
  make_auxilary_nursing_agency_identifier_type_mandatory_in_partner_invite: string;
  worklist_label_for_manage_security_rules: string;
  worklist_selected_for_manage_security_rules: string;
  auxilary_physician_worklist_label_for_manage_security_rules: string;
  auxilary_physician_worklist_selected_for_manage_security_rules: string;
  auxilary_nursing_agency_worklist_label_for_manage_security_rules: string;
  auxilary_nursing_agency_worklist_selected_for_manage_security_rules: string;
  chie_callback_auth_key: string;
  esi_code_for_partner_identity: string;
  enable_app_center: string;
  selected_apps: string;
  default_outgoing_filing_center_file_exchange: string;
  email_domain_name: string;
  enable_visit_schedule: string;
  worklist_selected_for_visit_schedule: string;
  enable_base64_encodedfile: string;
  default_patients_workflow: string;
  esi_code_for_alternate_contact: string;
  make_esi_code_mandatory_in_alternate_contact_invite: string;
  enable_multi_thread_pdg: string;
  staff_api_enable_staff_id_retrieval: string;
  staff_api_authentication_type: string;
  staff_api_authentication_username: string;
  staff_api_authentication_password: string;
  staff_api_staff_id_retrieval_url: string;
  staff_api_authentication_login_url: string;
  staff_api_company_id: string;
  staff_api_client_id: string;
  enable_base64_encodedfile_progress_note: string;
  enable_category_code_to_external_application: string;
  enable_exchange_of_esicode: string;
  response_type: string;
  enable_message_type_id_category_code: string;
  enable_staff_import: string;
  sso_service_type: string;
  worklist_selected_for_PAH: string;
  enable_masterdata_type: string;
  enable_exchange_of_discrete_data: string;
  enable_email_notify: string;
  enable_multisite: string;
  welcome_message_patient: string;
  chat_start_time: string;
  chat_start_period: string;
  chat_end_time: string;
  chat_end_period: string;
  User_escalated_messages: string;
  role_patient_chat: string;
  staff_escalation_members: string;
  member_role_pdg_patient_reg: string;
  available_staff_message: string;
  message_routing_type: string;
  cross_site_communication_for_messages: string;
  site_external_system_integration: string;
  default_outgoing_filing_center_faxq: string;
  default_outgoing_filing_center_notification: string;
  default_outgoing_filing_center_directlinking: string;
  default_outgoing_filing_center_progressnote: string;
  default_outgoing_filing_center_phi: string;
  default_outgoing_filing_center_formdiscretedata: string;
  default_sites: string;
  external_system_integration: string;
  ignore_mandatory_checking_integration: string;
  enable_mobile_pah: string;
  enable_appless_video_chat: string;
  enable_demographic_profile: string;
  enable_document_center: string;
  enable_alternate_username_login: string;
  enable_external_message_category_code_in_message_tag: string;
  enable_external_cateogry_code_in_form_types: string;
  enable_API_based_integration_for_message_type: string;
  document_category_code: string;
  api_integration_authorization_key: string;
  enable_API_based_integration_for_category_code: string;
  enable_API_based_integration_for_message_category: string;
  message_tag_type: string;
  message_tag_category: string;
  enable_external_message_type_id_in_message_tag: string;
  enable_external_message_typeId_in_form_types: string;
  default_file_format_document_exchange: string;
  default_ehr: string;
  form_send_mode: string;
  realm_key: string;
  enable_discrete_integration_status_worklist: string;
  enable_appless_messaging: string;
  esignature_audit_log: string;
  branded_appless_url: string;
  show_first_name_last_name_separate: string;
  branded_appless_forms_url: string;
  enable_external_transport_mode: string;
  external_transport_URL: string;
  external_transport_partner_token: string;
  external_transport_partner_authorization_type: string;
  form_integration_mode: string;
  enable_xss_security: string;
  enable_signature_iframe_workflow: string;
  signature_iFrame_reroute_URL: string;
  default_partner_category: string;
  hipaa_validation_staff_facing: string;
  hipaa_validation_practitioner_facing_send: string;
  default_outgoing_filing_center_cvl: string;
  enable_clinical_visit_log_integration: string;
  api_cvl_integration_end_point: string;
  api_cvl_integration_callback_end_point: string;
  cvl_round_off_value: string;
  enable_visit_entries_marked_complete: string;
  enable_visit_billing_details: string;
  notification_language: string;
  visit_patient_reminder_time: string;
  patient_reminder_checking_type: string;
  inbox_form_archive_remove_self: number;
  tenant_timezone_offset: string;
  tenant_timezoneName: string;
  tenant_timezoneNameValue: string;
}

export interface MySite {
  id: number;
  name: string;
  site_regid: string;
  enable_support_widget_branding: string;
  app_name: string;
}

export interface PushVarient {
  dev: Dev;
  prod: Dev;
}

export interface Dev {
  android: Android;
  ios: Ios;
}

export interface Android {
  senderID: string;
  variantID: string;
  variantSecret: string;
}

export interface Ios {
  variantID: string;
  variantSecret: string;
}

export interface UserContactVerification {
  mobileVerified: number;
  emailVerified: number;
}

export interface LoginResponse {
  code: number;
  date: string;
  dayNumber: string;
  isVirtual: boolean;
  appLessSession: boolean;
  userContactVerification: UserContactVerification;
  authenticationToken: string;
  userId: string;
  userCmisId: string;
  tenantCmisId: string;
  displayName: string;
  firstName: string;
  secondName: string;
  avatar: string;
  profileImageUrl: string;
  profileImageThumbUrl: string;
  roleName: string;
  username: string;
  alternate_username: string;
  roleId: string;
  assignedRoles: string;
  tenantId: string;
  tenantType: string;
  organizationMasterId: string;
  crossTenantId: string;
  isMaster: string;
  group: string;
  userStatus: string;
  languages: string;
  notificationSoundName: string;
  supply_menu: any[];
  crossTenantsDetails: any[];
  privileges: string;
  privileges_replica: string;
  enable_auto_hide_web_notifications: string;
  enable_sms_notifications: string;
  enable_email_notifications: string;
  dob: string;
  mobile: string;
  address: null;
  city: null;
  state: null;
  country: null;
  zip: string;
  caregivername: string;
  hasfamilycare: string;
  avatarUploadSize: number;
  primaryCare: string;
  cameraPictureLimit: number;
  allowedFileFormat: null;
  caregiver_userid: null;
  caregiver_username: null;
  caregiver_displayname: null;
  caregiver_firstname: null;
  caregiver_lastname: null;
  caregiver_zip: null;
  caregiver_dob: null;
  caregiver_mobile: null;
  associated_user: any[];
  gender: string;
  countryCode: string;
  countryIsoCode: null;
  referral_code: string;
  patientInventory: boolean;
  staffInventory: boolean;
  tenantKey: string;
  tenantName: string;
  enable_multisite: string;
  default_sites: string;
  default_file_format_document_exchange: string;
  default_outgoing_filing_center_directlinking: string;
  firstInfusionSupportNursesRoleName: null;
  firstDefaultNursesRoleName: null;
  firstDefaultNursesRoleNameDuringWorkingHour: null;
  firstBeyondWorkingNursesRoleName: string;
  firstInfusionSupportNursesRoleDuringWorkingHour: null;
  default_out_fc_dl: string;
  current_si: number;
  nursing_agencies: string;
  config: Config;
  config_replica: ConfigReplica;
  routingPage: null;
  defaultPageMobile: string;
  defaultPage: string;
  schedulerData: any[];
  escalatedSchedulerData: any[];
  avoidScheduleConversion: boolean;
  masterSchedulerData: any[];
  masterEscalatedSchedulerData: any[];
  avoidMasterScheduleConversion: boolean;
  updateAvailable: boolean;
  pushVarient: PushVarient;
  registration_type: string;
  agGridLicenseKey: string;
  helpCenterKey: string;
  masterEnabled: string;
  version: null;
  disabledPrivilegesIdsPatient: string[];
  cmisServiceId: number;
  cmisOperatedBy: string;
  cmisApiBaseUrl: string;
  cmisFileBaseUrl: string;
  cmisDefaultFolderList: null;
  accessSecurityEnabled: boolean;
  accessibleTenantIds: string;
  mySites: MySite[];
  mySiteinfo: string;
  enable_cross_site: number;
  siteConfigs: { [key: string]: string };
  userVisitDefaultView: string;
  userVisitDefaultType: string;
  status: number;
  message: string;
}

export interface OutOfOfficeInfo {
  isOutOfOffice: boolean
  message: string
  startDateTime: string
  endDateTime: string
  endDatePassed: boolean
}
