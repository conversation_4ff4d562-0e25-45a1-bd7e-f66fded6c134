export interface ConfigurationResponse {
  content: Content[]
  pageable: any
  size: number
  number: number
  numberOfElements: number
  totalPages: number
  totalElements: number
  first: boolean
  last: boolean
  sort: Sort
  empty: boolean
}

export interface Content {
  id: number
  configType: string
  dropDownName: string
  label: string
  value: string
  displayOrder: number
  range: number
  visible: number
}

export interface Sort {
  unsorted: boolean
  sorted: boolean
}
