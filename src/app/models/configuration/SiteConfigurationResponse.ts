import { Page } from "app/structure/shared/models/Page.model"

export interface SiteConfigurationResponse {
    content: SiteConfiguration[]
    pageable: Pageable
    totalPages: number
    totalElements: number
    last: boolean
    size: number
    number: number
    sort: Sort2
    numberOfElements: number
    first: boolean
    empty: boolean
    page: Page;
  }
  
  export interface SiteConfiguration {
    id: string
    siteId: number
    siteName: string
    configMeta: string
  }
  
  export interface Pageable {
    sort: Sort
    offset: number
    pageNumber: number
    pageSize: number
    unpaged: boolean
    paged: boolean
  }
  
  export interface Sort {
    empty: boolean
    sorted: boolean
    unsorted: boolean
  }
  
  export interface Sort2 {
    empty: boolean
    sorted: boolean
    unsorted: boolean
  }
  