import { Page } from "app/structure/shared/models/Page.model";

export interface FormMappingResponse {
  content: FormMapping[];
  pageable: Pageable;
  totalPages: number;
  totalElements: number;
  last: boolean;
  size: number;
  number: number;
  sort: Sort;
  numberOfElements: number;
  first: boolean;
  empty: boolean;
  page: Page;
}

export interface FormMapping {
  id: string;
  tenantId: number;
  ngwFormId: number;
  ngwFormName: string;
  classicalFormId: number;
  classicalFormName: string;
}

export interface Pageable {
  sort: Sort;
  offset: number;
  pageNumber: number;
  pageSize: number;
  paged: boolean;
  unpaged: boolean;
}

export interface Sort {
  empty: boolean;
  sorted: boolean;
  unsorted: boolean;
}
