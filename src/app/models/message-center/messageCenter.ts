import { DateRangeSelected } from '../../structure/shared/ch-daterange-picker/date-range-selected.interface';

export interface GetChatMessages {
  loaderStatus?: boolean;
  pageCount?: number;
  fromInboxPagination?: boolean;
  messagePriority?: number;
  tagIds?: string;
  mentionedUsers?: string;
  siteIds?: string | number;
  dateRange?: DateRangeSelected;
  archived?: number;
  searchKeyword?: string;
  viewInventory?: boolean;
  showChatHistory?: boolean;
  startDate?: string;
  endDate?: string;
  flagValue?: number;
  unread?: boolean;
  chatroomId?: number;
  chatThreadTypes?: number[];
}
interface PatientChatroomData {
  userId: number | [number];
  associatedPatient?: number;
}
interface StaffPartnerChatroom {
  userIds: number | [number];
}
interface MessageGroupChatroom {
  groupId: number;
  topic: string;
}

interface PatientGroupChatroom {
  patientId: number;
  topic: string;
}

interface RoleChatroom {
  roleId: number;
  associatedPatient: number;
}

export type ChatroomPayloadData = PatientChatroomData | StaffPartnerChatroom | MessageGroupChatroom | PatientGroupChatroom | RoleChatroom;

export interface CreateChatroomData {
  chatWith: string;
  data: ChatroomPayloadData;
  siteIds?: [number];
  admissionId?: string;
}

export interface CreateChatroomParameters {
  createdWith: number | [number];
  messageGroupId?: number;
  topic?: string;
  createdWithAssociatedId?: number;
  createdByAssociatedId?: number;
  siteIds?: [number];
  roleId?: number;
  chatWith: string;
  admissionId?: string;
}
export interface FilterItems {
  search?: string;
  searchType?: number;
  showMemberLessGroup?: boolean;
}
export interface PaginationItems {
  page?: number;
  limit?: number;
}
export interface SortItems {
  order?: string;
  field?: string;
}
export interface AdmissionItem {
  id?: number;
  admissionId?: string;
}
export interface GroupItems {
  groupId?: number;
  filter?: FilterItems;
  pagination?: PaginationItems;
  sort?: SortItems;
  patientId?: number;
  patientIds?: number[];
  groupIds?: number[];
  admissionId?: string;
  patients?: [{ id: number; admissionId?: string }];
}
export interface GroupsListingRequest {
  data?: GroupItems;
  siteIds?: number[];
}
export interface GroupRequest {
  name?: string;
  isPublic?: boolean;
  allowMultiThreadChat?: boolean;
  members?: number[];
  roles?: number[];
}
export interface MessageGroupRequest {
  data?: GroupRequest;
  siteIds?: number[];
  id?: number;
}
export const defaultCreateChatroomParameters: CreateChatroomParameters = {
  createdWith: 0,
  messageGroupId: 0,
  topic: '',
  createdWithAssociatedId: 0,
  createdByAssociatedId: 0,
  siteIds: [0],
  roleId: 0,
  chatWith: ''
};
export interface Status {
  code?: number;
  message?: string;
}
export interface MemberDetails {
  id?: string;
  displayName?: string;
  tenantId?: string;
  tenantName?: string;
  status?: string;
  roleIds?: string;
}
export interface RoleDetails {
  roleId?: string;
  name?: string;
  tenant_id?: string;
}
export class MessageGroup {
  allowMultiThreadChat: string;
  branch: string;
  chatRoomCreatedBy: string;
  chatRoomId: string;
  createdAt: string;
  createdBy: string;
  groupId: string;
  groupName: string;
  isParticipant: string;
  isPublic: string;
  tenantId: string;
  topic: string;
  patientId: string;
  id: string;
  site_id: string;
  group_id: string;
  status: string;
  created_at: string;
  updated_at: string;
  created_by: string;
  updated_by: string;
  last_modified: string;
  tenant_id: string;
  name: string;
  address: string;
  modified_by: string;
  modified_at: string;
  start_time: string;
  end_time: string;
  working_days: string;
  deleted: string;
  logo: string;
  registration_id: string;
  office_email: string;
  contact_email: string;
  office_phone: string;
  helpline_phone: string;
  office_phone_country_code: string;
  helpline_country_code: string;
  office_phone_country_iso_code: string;
  helpline_country_iso_code: string;
  sites: string;
  DOB: string;
  MRN: string;
  firstName: string;
  lastName: string;
  siteId: string;
  memberDetails: MemberDetails[];
  selectedRoles: RoleDetails[];
  constructor(data) {
    if (data) {
      this.allowMultiThreadChat = data.allowMultiThreadChat || '';
      this.branch = data.branch || '';
      this.chatRoomCreatedBy = data.chatRoomCreatedBy || '';
      this.chatRoomId = data.chatRoomId || '';
      this.createdAt = data.createdAt || '';
      this.createdBy = data.createdBy || '';
      this.groupId = data.groupId || '';
      this.groupName = data.groupName || '';
      this.isParticipant = data.isParticipant || '';
      this.isPublic = data.isPublic || '';
      this.tenantId = data.tenantId || '';
      this.topic = data.topic || '';
      this.patientId = data.patientId || '';
      this.id = data.id || '';
      this.site_id = data.site_id || '';
      this.group_id = data.group_id || '';
      this.status = data.status || '';
      this.created_at = data.created_at || '';
      this.updated_at = data.updated_at || '';
      this.created_by = data.created_by || '';
      this.updated_by = data.updated_by || '';
      this.last_modified = data.last_modified || '';
      this.tenant_id = data.tenant_id || '';
      this.name = data.name || '';
      this.address = data.address || '';
      this.modified_by = data.modified_by || '';
      this.modified_at = data.modified_at || '';
      this.start_time = data.start_time || '';
      this.end_time = data.end_time || '';
      this.working_days = data.working_days || '';
      this.deleted = data.deleted || '';
      this.logo = data.logo || '';
      this.registration_id = data.registration_id || '';
      this.office_email = data.office_email || '';
      this.contact_email = data.contact_email || '';
      this.office_phone = data.office_phone || '';
      this.helpline_phone = data.helpline_phone || '';
      this.office_phone_country_code = data.office_phone_country_code || '';
      this.helpline_country_code = data.helpline_country_code || '';
      this.office_phone_country_iso_code = data.office_phone_country_iso_code || '';
      this.helpline_country_iso_code = data.helpline_country_iso_code || '';
      this.sites = data.sites || '';
      this.siteId = data.siteId || '';
    }
  }
}
export interface GroupData {
  messageGroups?: MessageGroup[];
  totalMessageGroupsCount?: string;
  patientGroups?: MessageGroup[];
}
export interface GroupListItem<T> {
  data?: T;
  status?: Status;
  success?: boolean;
}

export interface PdgItems {
  isPublic?: boolean;
  allowMultiThreadChat?: boolean;
  members?: number[];
  roles?: number[];
}
export interface PatientDiscussionGroup {
  data?: PdgItems;
  id?: number;
  admissionId?: string;
}
interface GetInboxMessagesFilter {
  tagIds?: number[];
  flagId?: number;
  priority?: number;
  mention?: boolean;
  unread?: boolean;
  dateRange?: { start: string; end: string };
  searchKeyword?: string;
  siteIds?: number[];
  chatroomId?: number;
  chatThreadTypes?: number[];
}
export interface GetInboxMessagesPayload {
  archived?: boolean;
  page?: number;
  filter?: GetInboxMessagesFilter;
}

export interface MessageTag {
  id: number;
  name: string;
  bgColor: string;
  fontColor: string;
  type: string;
}

export interface ChatParticipants {
  avatar: string;
  userId: number;
  displayName: string;
}

export interface GetChatMessagesResponseData {
  chatAvatar: string;
  chatHeading: string;
  chatSubject: string;
  chatSubHeading: string;
  chatroomFlag: number;
  chatroomId: string;
  chatroomTag: number;
  chatParticipants: any[];
  chatParticipantCount: number;
  deliveryTime: string | null;
  department: string;
  hasUnreadMessages: boolean;
  initiatorName: string;
  isAcExisting: boolean;
  isAcLogin: boolean;
  isSelfMessage: string;
  isSelfArchived: boolean;
  isPdg: boolean;
  maskedUnreadCount: number | null;
  mentionedUsers: string[] | null;
  message: string;
  messageForwarded: string;
  messageCategory: string;
  messageFlag: number;
  messageMentionRead: number;
  messageMentionUnread: number;
  messageUnreadCount: number;
  messageTagList: [MessageTag];
  messageOrder: string;
  messagePriorityRead: number;
  messagePriorityUnread: number;
  messageTag: number;
  messagesCount: number;
  messageType: number;
  patientId: string | null;
  pinnedStatus: number;
  priorityId: number;
  repliedTo: string | null;
  unreadCount: number;
  maskedSubCount: number;
}
export interface GetChatMessagesResponse {
  success: boolean;
  status: { code: number; message: string };
  data: {
    messages?: [GetChatMessagesResponseData];
    totalChatRoomsCount?: number;
    totalUnreadMessagesCount?: number;
    message?: GetChatMessagesResponseData;
  };
}

export interface FilterTopicItems {
  groupType?: string;
  search?: string;
}

export interface TopicItems {
  id?: number;
  filter?: FilterTopicItems;
  pagination?: PaginationItems;
}

export interface GroupTopicsListRequest {
  data?: TopicItems;
}

export interface GetMaskedRepliesPayload {
  chatRoomId: number;
  pageCount?: number;
  archived?: number;
}

export const defaultGetMaskedRepliesPayload: GetMaskedRepliesPayload = {
  chatRoomId: 0,
  pageCount: 0,
  archived: 0
};

export type RestoreType = 'role' | 'roleUser';
export type RestoreAction = 'restore';
export interface ManageChatParticipantsPayload {
  data: {
    type: RestoreType;
    id: number;
    chatRoomId: number;
  };
  action: RestoreAction;
}
