import { Component, OnInit, HostListener } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { DatePipe } from '@angular/common';
import { ISubscription } from 'rxjs/Subscription';
import { isBlank, getUserPreferenceSiteIds } from 'app/utils/utils';
import { ROUTES } from 'app/constants/routes';
import { PermissionService } from 'app/services/permission/permission.service';
import { IntakeService } from 'app/structure/intake/intake.service';
import { CHECKED, CONSTANTS, Filter, MessageOperationType, messageTypeAndCategory, UserRoles } from 'app/constants/constants';
import { StoreService, Store } from 'app/structure/shared/storeService';
import { MessageService } from 'app/services/message-center/message.service';
import { GetChatMessages, GetChatMessagesResponse } from 'app/models/message-center/messageCenter';
import { Subscription } from 'rxjs';
import { ToolTipService } from '../../structure/tool-tip.service';
import { CommonVideoService } from '../../../assets/lib/universal-video/common-video.service';
import { WorkListService } from '../../structure/worklists/worklist.service';
import { FormsService } from '../../structure/forms/forms.service';
import { GlobalDataShareService } from '../../structure/shared/global-data-share.service';
import { InboxService } from '../../structure/inbox/inbox.service';
import { SharedService } from '../../structure/shared/sharedServices';
import { StructureService } from '../../structure/structure.service';

declare const $: any;
declare const jQuery: any;
declare const swal: any;
declare const notifyMe: any;
declare const serviceWorkerReg: any;
@Component({
  selector: 'app-left-sidebar',
  templateUrl: './menu-left-vertical.component.html',
})
// TODO: Revamp CHP-7022
export class MenuLeftComponent implements OnInit {
  privileges = [];
  public menuShow = true;
  userInfo;
  manageConfig;
  accountId;
  userRole;
  arr = [];
  audio: any;
  previlages: any = {};
  inboxData;
  CSRMessages = [];
  supplyMenu: any;
  supplyMenuItems = [];
  supplyMenuItemsManagement = [];
  inboxCountLoading = true;
  inboxLoadingError = false;
  inboxUnreadMessageCount = 0;
  inboxUnreadFormCount = 0;
  signatureCountLoading = true;
  signatureLoadingError = false;
  enableFilingCenter = true;
  enableSecurityDashboard = false;
  accessSecurityWorklistLabel;
  accessSecurityWorklistSelected;
  accessSecurityWorklistActionLink;
  actionLink;
  visitSchedulerWorklistSelected;
  visitSchedulerWorklistActionLink;
  showDashboard = false;
  signatureRequest = [];
  userDataConfig;
  showNewSignature = true;
  nursingAgencyUser = false;
  menuSettings = {
    allowPatientDiscussionGroup: false,
  };
  tenantId;
  ringTimmer: any;
  config;
  userDetails;
  configData;
  userData;
  crossTenantCommunicationEnabled = false;
  patientTopic = {
    count: localStorage.getItem('patientTopicCount')
  };
  videoChatData: any;
  crossTenantId;
  acceptedPush = false;
  visitSchedulerWorklistRoleSelected = false;
  appCenterData = [];
  crossTenantChangeSubscriber: any;
  public onInboxDataSubscription: ISubscription;
  public updateUserInboxCountsSubscription: ISubscription;
  public unreadCountSubsc: ISubscription;
  public updateInboxCountsSubscription: ISubscription;
  public updateInboxDataOnmessagePollingSelfUpdate: ISubscription;
  public reInitiateMessagePollingSubscription: ISubscription;
  public updateSignatureRequestUnreadCountSub: ISubscription;
  public reloadConfigSubs: ISubscription;
  stateInitActivatedByTenantChange = false;
  userInboxCounts: any = {
    forms: {
      loading: true,
      error: false,
      totalCount: 0
    }
  };
  canNaUserManageEdm = true;
  canNaUserManageGroup = true;
  appVersion = this.sharedService.appVersion.split('-')[0];
  showWorklistMenu = '';
  toggleMenu = false;
  imgUrl = '';
  showTenantLogo: boolean;
  routes;
  unreadCountUpdateOnPollingInInbox = true;
  private socketEventSubscriptions: Subscription[] = [];
  showAppCenterWorklistMenu = '';
  toggleAppSubMenu = false;
  toggleAppMenu = false;
  appName = '';
  constructor(
    public structureService: StructureService,
    private route: ActivatedRoute,
    private router: Router,
    public _inboxService: InboxService,
    public sharedService: SharedService,
    private _formsService: FormsService,
    public _commonVideoService: CommonVideoService,
    public _GlobalDataShareService: GlobalDataShareService,
    private _worklistService: WorkListService,
    private _ToolTipService: ToolTipService,
    private permissionService: PermissionService,
    private datePipe: DatePipe,
    private storeService: StoreService,
    private messageService: MessageService,
    private intakeService : IntakeService
  ) {
    this.routes = ROUTES;
    this.updateInboxDataOnmessagePollingSelfUpdate = this.sharedService.messagePollingSelfUpdate.subscribe((data) => {
      this.setInboxDataOnPolling(data);
    });
    this.updateInboxCountsSubscription = this.sharedService.readInboxData.subscribe((clickedItem) => {
      this.inboxUnreadMessageCount = this.structureService.inboxUnreadMessageCount - +clickedItem.unreadCount;
      if (!this.inboxUnreadMessageCount || this.inboxUnreadMessageCount < 0) {
        this.inboxUnreadMessageCount = 0;
      }
      this.structureService.inboxUnreadMessageCount = this.inboxUnreadMessageCount;
      const itemIndex = this.structureService.inboxData.length > 0 ? this.structureService.inboxData.indexOf(clickedItem) : 0;
      if (this.structureService.inboxData.length > 0 && itemIndex > -1) {
        this.structureService.inboxData[itemIndex].unread = 0;
        this.structureService.inboxData[itemIndex].unreadCount = 0;
        this.structureService.inboxData[itemIndex].hasUnreadMessages = false;
        if (this.structureService.inboxData[itemIndex].messageType === 2) {
          this.structureService.inboxData[itemIndex].maskedUnreadCount = 0;
        }
      }
      let unreadCount = 0;
      let unreadFormCount = 0;
      this.structureService.inboxData.map((inputMessage) => {
        if (inputMessage.chatroomId === clickedItem.chatroomId && clickedItem.messageType === 2) {
          inputMessage.maskedUnreadCount -= clickedItem.unreadCount;
        }
        if (
          (inputMessage.chatroomId && +inputMessage.chatroomId === +clickedItem.chatroomId) ||
          (inputMessage.formId && inputMessage.formId.toString() === clickedItem.formId.toString())
        ) {
          inputMessage.unreadCount = 0;
          inputMessage.unread = 0;
          inputMessage.hasUnreadMessages = false;
        }
        const maskedUnreadCount = +inputMessage.maskedUnreadCount || 0;
        const msgUnreadCount = +inputMessage.unreadCount || 0;
        const unread = +inputMessage.unread || 0;
        unreadCount += +inputMessage.messageType === 2 ? maskedUnreadCount : msgUnreadCount || unread;
        if (inputMessage.category === 'structured-forms') {
          unreadFormCount += +inputMessage.unread;
        }
        return inputMessage;
      });
      let countUpdated = false;
      this.structureService.inboxDataFirstPage.map((inputMessage) => {
        if (!countUpdated) {
          if (+inputMessage.messageType === 2 || +inputMessage.baseChatroomId !== 0) {
            if (+inputMessage.messageType === 2) {
              if (inputMessage.maskedSubCount && +inputMessage.chatroomId === +clickedItem.baseId) {
                inputMessage.maskedUnreadCount -= clickedItem.unreadCount;
                if (+inputMessage.maskedUnreadCount < 0) {
                  inputMessage.maskedUnreadCount = 0;
                }
                inputMessage.hasUnreadMessages = !!inputMessage.maskedUnreadCount;
                countUpdated = true;
                if (inputMessage.subList && inputMessage.subList.length) {
                  let subChatroomExist = inputMessage.subList.findIndex(
                    (chatmessage) =>
                      +chatmessage.chatroomid === +clickedItem.chatroomid
                  );
                  if (subChatroomExist === -1) {
                    inputMessage.subList.push({ chatroomid: clickedItem.chatroomid, baseId: inputMessage.chatroomId });
                    subChatroomExist = inputMessage.subList.length - 1;
                  }
                  if (subChatroomExist !== -1) {
                    const unreadCount = parseInt(
                      inputMessage.subList[subChatroomExist].unreadCount
                    ) || 0;
                    inputMessage.maskedUnreadCount =
                      parseInt(inputMessage.maskedUnreadCount) - unreadCount;
                    inputMessage.hasUnreadMessages = inputMessage.maskedUnreadCount === 0 ? false : inputMessage.maskedUnreadCount;
                    if (parseInt(inputMessage.maskedUnreadCount) < 0) {
                      inputMessage.maskedUnreadCount = 0;
                    }
                    inputMessage.subList[subChatroomExist].unreadCount = 0;
                    inputMessage.subList[
                      subChatroomExist
                    ].maskedUnreadCount = 0;
                    if (
                      parseInt(inputMessage.subList[subChatroomExist].unread)
                    ) {
                      inputMessage.subList[subChatroomExist].unread = 0;
                    }
                  }
                }
              } else if (+inputMessage.chatroomId === +clickedItem.chatroomId) {
                inputMessage.unreadCount = 0;
                inputMessage.maskedUnreadCount = 0;
                inputMessage.hasUnreadMessages = false;
                if (parseInt(inputMessage.unread)) {
                  inputMessage.unread = 0;
                }
                countUpdated = true;
              }
            } else if (+inputMessage.chatroomId === +clickedItem.chatroomId) {
              inputMessage.unreadCount = 0;
              inputMessage.hasUnreadMessages = false;
              inputMessage.maskedUnreadCount = 0;
              if (parseInt(inputMessage.unread)) {
                inputMessage.unread = 0;
              }
              countUpdated = true;
            }
          } else if (+inputMessage.chatroomId === +clickedItem.chatroomId) {
            inputMessage.unreadCount = 0;
            inputMessage.hasUnreadMessages = false;
            inputMessage.maskedUnreadCount = 0;
            if (parseInt(inputMessage.unread)) {
              inputMessage.unread = 0;
            }
            countUpdated = true;
          }
        }
      });

      this.structureService.inboxUnreadFormCount = unreadFormCount;
      this.inboxUnreadFormCount = unreadFormCount;
      this.inboxData = this.structureService.inboxData;
    });
    this.sharedService.signatureRequestInboxData.subscribe((onInboxData) => {
      this.structureService.signatureRequestInboxData = onInboxData;
      this.signatureRequest = onInboxData;
    });
    this.sharedService.readSignatureRequest.subscribe((clickedItem) => {
      const itemIndex = this.signatureRequest.indexOf(clickedItem);
      if (itemIndex > -1) {
        this.signatureRequest[itemIndex].isRead = true;
        this.structureService.signatureRequestInboxData[itemIndex].isRead = true;
      }
    });
    this.sharedService.playAudioForCall.subscribe((onData) => {
      this.playAudio(onData.playSound, onData.action);
    });
    this.structureService.displayProgress.subscribe((data) => {
      this.menuShow = data;
    });
    this.crossTenantChangeSubscriber = this.sharedService.crossTenantChange.subscribe((onInboxData) => {
      this.sharedService.configuringTenantData = true;
      if (onInboxData.tenantdataconfigured) {
        this.tenantId = this.structureService.getCookie('tenantId');
        this.crossTenantId = this.structureService.getCookie('crossTenantId');
        this.manageConfig = JSON.parse(this.structureService.userDataConfig);
        if (this.manageConfig.enable_patient_info_from_third_party_app) {
          this.enableSecurityDashboard = true;
          if (
            this.manageConfig.worklist_label_for_manage_security_rules &&
            this.manageConfig.worklist_selected_for_manage_security_rules &&
            !this.userData.accessSecurityEnabled
          ) {
            this.accessSecurityWorklistLabel = this.manageConfig.worklist_label_for_manage_security_rules;
            this.accessSecurityWorklistSelected = this.manageConfig.worklist_selected_for_manage_security_rules;
            this.structureService.getAccessSecurityWorklistActionLink(this.crossTenantId, this.accessSecurityWorklistSelected).then((data: any) => {
              if (data.length) {
                this.accessSecurityWorklistActionLink = data[0]['worklistActionLink'];
              } else {
                this.accessSecurityWorklistActionLink = '';
                this.enableSecurityDashboard = false;
              }
            });
          } else if (
            this.userData.accessSecurityType === 'physicianOrPrescriber' &&
            this.manageConfig.auxilary_physician_worklist_selected_for_manage_security_rules &&
            this.manageConfig.auxilary_physician_worklist_label_for_manage_security_rules &&
            this.userData.accessSecurityEnabled
          ) {
            this.accessSecurityWorklistLabel = this.manageConfig.auxilary_physician_worklist_label_for_manage_security_rules;
            this.accessSecurityWorklistSelected = this.manageConfig.auxilary_physician_worklist_selected_for_manage_security_rules;
            this.structureService.getAccessSecurityWorklistActionLink(this.crossTenantId, this.accessSecurityWorklistSelected).then((data: any) => {
              if (data.length) {
                this.accessSecurityWorklistActionLink = data[0]['worklistActionLink'];
              } else {
                this.accessSecurityWorklistActionLink = '';
                this.enableSecurityDashboard = false;
              }
            });
          } else if (
            this.userData.accessSecurityType === 'nursingAgency' &&
            this.manageConfig.auxilary_nursing_agency_worklist_selected_for_manage_security_rules &&
            this.manageConfig.auxilary_nursing_agency_worklist_label_for_manage_security_rules &&
            this.userData.accessSecurityEnabled
          ) {
            this.accessSecurityWorklistLabel = this.manageConfig.auxilary_nursing_agency_worklist_label_for_manage_security_rules;
            this.accessSecurityWorklistSelected = this.manageConfig.auxilary_nursing_agency_worklist_selected_for_manage_security_rules;
            this.structureService.getAccessSecurityWorklistActionLink(this.crossTenantId, this.accessSecurityWorklistSelected).then((data: any) => {
              if (data.length) {
                this.accessSecurityWorklistActionLink = data[0].worklistActionLink;
              } else {
                this.accessSecurityWorklistActionLink = '';
                this.enableSecurityDashboard = false;
              }
            });
          }
        } else {
          this.enableSecurityDashboard = false;
        }
        if (this.manageConfig.worklist_selected_for_visit_schedule && this.manageConfig.worklist_selected_for_visit_schedule !== '') {
          this.visitSchedulerWorklistSelected = this.manageConfig.worklist_selected_for_visit_schedule;
          this.structureService.getVisitSchedulerWorklistActionLink(this.crossTenantId, this.visitSchedulerWorklistSelected).then((data: any) => {
            if (data.length) {
              this.visitSchedulerWorklistActionLink = data[0].worklistActionLink;
              const metaData = data[0].worklistMeta;
              let secRoleMatch = false;
              if (metaData.includeSecRole && !isBlank(metaData.secondaryRoles)) {
                const roles = metaData.secondaryRoles.split(',');
                const userRoles = this.userData.assignedRoles.split(',');
                secRoleMatch = roles.some((r) => userRoles.includes(r) && +r !== +this.userData.roleId);
              }
              if (
                (metaData.visibleToRoles && metaData.visibleToRoles.split(',').indexOf(this.userData.roleId) !== -1) ||
                (metaData.allowCrossTenant &&
                  metaData.visibleToOtherRoles &&
                  metaData.visibleToOtherRoles.split(',').indexOf(this.userData.roleId) !== -1) ||
                secRoleMatch
              ) {
                this.visitSchedulerWorklistRoleSelected = true;
              } else {
                this.visitSchedulerWorklistRoleSelected = false;
              }
            } else {
              this.visitSchedulerWorklistActionLink = '';
              this.visitSchedulerWorklistSelected = '';
              this.visitSchedulerWorklistRoleSelected = false;
            }
          });
        } else {
          this.visitSchedulerWorklistActionLink = '';
          this.visitSchedulerWorklistSelected = '';
          this.visitSchedulerWorklistRoleSelected = false;
        }
        this.sharedService.configuringTenantData = false;
        if (this.structureService.previousUrlNow && this.structureService.previousUrlNow !== '/login') {
          this.stateInitActivatedByTenantChange = true;
        }
        this.ngOnInit();
      }
    });

    this.sharedService.acceptPushNotify.subscribe((data) => {
      if (data === 'accepted') {
        this.acceptedPush = true;
        this.acceptCall();
      } else if (data === 'rejected') {
        this.rejectCall();
      }
      clearTimeout(this.ringTimmer);
    });
    if (!this.structureService.signatureListServerSidePagination) {
      this.sharedService.signatureRequestPollingInboxData.subscribe((onSignaturerequestData) => {
        if (onSignaturerequestData.isRead === false) {
          this.structureService.signatureRequestUnreadCount += 1;
        }
        if (onSignaturerequestData.signatureStatus !== 'SIGNED') {
          onSignaturerequestData.archivedUsers = '';
          onSignaturerequestData.accountLevelArchived = false;
          if (!this.structureService.signatureRequestInboxData) {
            this.structureService.signatureRequestInboxData = [];
          }
          if (this.structureService.signatureRequestInboxData.indexOf(onSignaturerequestData) === -1) {
            this.structureService.signatureRequestInboxData.push(onSignaturerequestData);
          }
          this.sharedService.signatureRequestInboxData.emit(this.structureService.signatureRequestInboxData);
        } else {
          let directFlag = true;
          for (let i = 0; i < this.structureService.signatureRequestInboxData.length; i += 1) {
            const value = this.structureService.signatureRequestInboxData[i];
            if (value.id === onSignaturerequestData.documentId) {
              directFlag = false;
              this.structureService.signatureRequestInboxData[i].signatureStatus = onSignaturerequestData.signatureStatus;
              const date: Date = new Date();
              const datetostring = Date.parse(date.toString()) / 1000;
              this.structureService.signatureRequestInboxData[i].signedOn = datetostring;
            }
          }
          if (directFlag === true) {
            if (this.structureService.signatureRequestInboxData.indexOf(onSignaturerequestData) === -1) {
              this.structureService.signatureRequestInboxData.push(onSignaturerequestData);
            }
          }
          this.sharedService.signatureRequestInboxData.emit(this.structureService.signatureRequestInboxData);
        }
      });
    }
    if (!this.unreadCountSubsc) {
      this.unreadCountSubsc = this.sharedService.unreadCounts.subscribe((unreadCountsData) => {
        if (unreadCountsData && unreadCountsData.types) {
          const types: any = unreadCountsData.types.split(',');
          if (types.length) {
            if (unreadCountsData.counts && 'forms' in unreadCountsData.counts) {
              this.userInboxCounts.forms.pending = unreadCountsData.counts.forms.pending;
              this.userInboxCounts.forms.completed = unreadCountsData.counts.forms.completed;
              this.userInboxCounts.forms.totalCount = +unreadCountsData.forms.pending + +unreadCountsData.forms.completed;
              this.structureService.userInboxCounts = this.userInboxCounts;
            }
            if (unreadCountsData.counts && 'messages' in unreadCountsData.counts) {
              this.inboxUnreadMessageCount = +unreadCountsData.counts.messages;
              if (!this.inboxUnreadMessageCount || this.inboxUnreadMessageCount < 0) {
                this.inboxUnreadMessageCount = 0;
              }
              this.inboxCountLoading = false;
              this.structureService.inboxUnreadMessageCount = this.inboxUnreadMessageCount;
            }
          }
        }
      });
    }
    if (!this.reloadConfigSubs) {
      this.reloadConfigSubs = this.sharedService.reloadConfig.subscribe((userData) => {
        this.userData = userData;
        this.manageConfig = userData.config;
        this.reloadCurrentController();
      });
    }
    this._worklistService.getWorklistMenu();
  }
  @HostListener('click', ['$event',])
  onClick(event) {
    if (event.target.id !== 'worklist-group' && event.target.id !== 'worklist-menu') {
      this.toggleMenu = false;
    } else {
      $('.cat__menu-left__submenu--toggled .cat__menu-left__list').css('display', 'none');
      $('li').removeClass('cat__menu-left__submenu--toggled');
      if (event.target.id === 'worklist-group' && (isBlank(this.showWorklistMenu) || this.showWorklistMenu === event.target.innerText)) {
        this.showWorklistMenu = event.target.innerText;
        this.toggleMenu = !this.toggleMenu;
      } else if (event.target.id === 'worklist-group' && this.showWorklistMenu !== event.target.innerText) {
        this.showWorklistMenu = event.target.innerText;
        this.toggleMenu = true;
      }
    }
    this.handleAppCenterLeftMenuItems(event);
  }
  playAudio(play, condition = 'default', data: any = {}) {
    if (play) {
      let src = './assets/audio/videocall.mp3';
      if (condition == 'notaccept') {
        src = `./assets/audio/${  data.src}`;
      } else if (condition == 'forcall' || condition === 'forcallPlay') {
        src = './assets/audio/calltone.mp3';
      }
      if (!this.audio) {
        this.audio = new Audio();
      } else {
        this.audio.pause();
      }
      this.audio.src = src;
      this.audio.load();
      if (typeof this.audio.loop === 'boolean') {
        this.audio.loop = condition !== 'notaccept';
      } else {
        this.audio.addEventListener(
          'ended',
          function () {
            if (condition !== 'notaccept') {
              this.audio.play();
            }
          },
          false
        );
      }
      if (this.audio) {
        if (data.callStatus !== 'onOnlineUsers') {
          this.audio.play();
        }
        if (condition === 'notaccept') {
          data.title = this._ToolTipService.getTranslateData('TITLES.CALL_NOT_ANSWERED');
          if (!isBlank(data.callStatus) && data.callStatus === 'onOnlineUsers')
            data.title = this._ToolTipService.getTranslateData('TITLES.NO_USERS_ONLINE');
          this.sharedService.videoCallEnd.emit({
            action: 'allUsersNotAccept',
            data: data,
          });
        }
      }
    } else if (this.audio) {
      if (condition === 'forcallPause') {
        this.audio.pause();
      } else {
        this.audio.pause();
        this.audio.remove();
        this.audio = null;
      }
    }
  }
  ngOnDestroy() {
    if (this.onInboxDataSubscription) {
      this.onInboxDataSubscription.unsubscribe();
    }
    if (this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
    if (this.unreadCountSubsc) {
      this.unreadCountSubsc.unsubscribe();
    }
    if (this.updateInboxCountsSubscription) {
      this.updateInboxCountsSubscription.unsubscribe();
    }
    if (this.updateInboxDataOnmessagePollingSelfUpdate) {
      this.updateInboxDataOnmessagePollingSelfUpdate.unsubscribe();
    }
    if (this.updateUserInboxCountsSubscription) {
      this.updateUserInboxCountsSubscription.unsubscribe();
    }
    if (this.reInitiateMessagePollingSubscription) {
      this.reInitiateMessagePollingSubscription.unsubscribe();
    }
    if (this.updateSignatureRequestUnreadCountSub) {
      this.updateSignatureRequestUnreadCountSub.unsubscribe();
    }
    if (this.reloadConfigSubs) {
      this.reloadConfigSubs.unsubscribe();
    }
    this.structureService.socket.off('messagePolling');
    /**Unsubscribe all the socket event subscriptions */
    this.socketEventSubscriptions.forEach(subscription => {
      if(subscription) subscription.unsubscribe();
    });
  }
  ngOnInit() {
    this.userData = this.structureService.getUserdata();
    if (this.userData.config.signature_serverside == '1') {
      this.structureService.signatureListServerSidePagination = true;
    }
    if (!this.updateSignatureRequestUnreadCountSub && this.structureService.signatureListServerSidePagination) {
      this.updateSignatureRequestUnreadCountSub = this.sharedService.updateSignatureRequestUnreadCount.subscribe((data) => {
        if (data.type === 'decrement' && this.structureService.signatureRequestUnreadCount > 0) {
          this.structureService.signatureRequestUnreadCount -= 1;
        } else if (data.type === 'increment') {
          this.structureService.signatureRequestUnreadCount += 1;
        }
      });
    }
    if (!isBlank(this.userData.mySiteinfo) && !isBlank(this.userData.mySiteinfo.logo)) {
      this.showTenantLogo = false;
      this.imgUrl = `${this.structureService.apiBaseUrl}citus-health/site-logos/${this.userData.mySiteinfo.logo}`;
    } else {
      this.showTenantLogo = true;
    }
    if (!this.onInboxDataSubscription) {
      this.onInboxDataSubscription = this.sharedService.onInboxData.subscribe((onMainData) => {
        this.structureService.inboxData = onMainData.inboxData;
        this.inboxData = onMainData.inboxData;
        if (+localStorage.getItem('pageCountMessage')) {
          this.structureService.inboxDataFirstPage = JSON.parse(JSON.stringify(this.inboxData));
          onMainData.inboxDataFirstPage = this.inboxData;
        }
        if (onMainData.emitUpdateInboxData) {
          this.sharedService.updateInboxData.emit({
            inboxData: this.inboxData,
            showFilterAppliedMessage: onMainData.showFilterAppliedMessage || false,
            disablescroll: onMainData.onNewmessageRecieved || false,
            inboxDataFirstPage: onMainData.inboxDataFirstPage || false,
            inboxTotalMessageCount: onMainData.inboxTotalMessageCount || false,
            totalUnreadMessagesCount: +onMainData.totalUnreadMessagesCount || 0
          });
        }
      });
    }
    if (!this.updateUserInboxCountsSubscription) {
      this.updateUserInboxCountsSubscription = this.sharedService.updateUserInboxCounts.subscribe((userInboxCountsData) => {
        if (userInboxCountsData.getCount) {
          switch (userInboxCountsData.type) {
            case 'messages':
              this.getUserInboxCounts('messages');
              break;
            case 'forms':
              if (this.userData.config.enable_forms === '1') {
                this.getUserInboxCounts('forms');
              }
              break;
            default:
              if (this.userData.config.enable_forms === '1') {
                this.getUserInboxCounts();
              } else {
                this.getUserInboxCounts('messages');
              }
              break;
          }
        }
      });
    }

    const privilages = this.userData.privileges.split(',');
    if (privilages.indexOf('requestSignature') > -1) {
      this.structureService.showNewSignature = true;
    } else {
      this.structureService.showNewSignature = false;
    }

    this.setLocalStorageParticipants('false');
    this.config = this.structureService.userDataConfig;
    this.userDetails = this.structureService.userDetails;
    this.configData = JSON.parse(this.config);
    this.userData = JSON.parse(this.userDetails);
    this.tenantId = this.structureService.getCookie('tenantId');
    this.crossTenantId = this.structureService.getCookie('crossTenantId');
    this.supplyMenu = this.userData.supply_menu;
    if (this.userData.privileges.indexOf('allowOrganizationSwitching') > -1 && +this.userData.organizationMasterId !== 0) {
      this.crossTenantCommunicationEnabled = true;
    }

    this.showNewSignature = this.structureService.showNewSignature;
    for (let i = 0; i < this.supplyMenu.length; i += 1) {
      const val = JSON.parse(this.supplyMenu[i]);
      if (val.isdesktop) {
        if (!isBlank(val.nameOfSendMenu)) {
          this.supplyMenuItems[i] = val.nameOfSendMenu;
        }
        this.supplyMenuItemsManagement[i] = val.nameOfManagementMenu;
      }
    }

    this.activateNow(false);
    this.userDataConfig = JSON.parse(this.structureService.userDataConfig);
    if (+this.userDataConfig.enable_nursing_agencies_visibility_restrictions === 1 && !isBlank(this.userData.nursing_agencies)) {
      this.nursingAgencyUser = true;

      if (!this.userData.nursing_agencies_can_manage_education || +this.userData.nursing_agencies_can_manage_education !== 1) {
        this.canNaUserManageEdm = false;
      }

      if (!this.userData.nursing_agencies_can_manage_groups || +this.userData.nursing_agencies_can_manage_groups !== 1) {
        this.canNaUserManageGroup = false;
      }
    }
    this.enableFilingCenter = +this.userDataConfig.enable_filing_center === 1;
    if (this.userDataConfig.enable_patient_info_from_third_party_app && +this.userData.group !== 3) {
      this.enableSecurityDashboard = true;
      if (
        this.userDataConfig.worklist_label_for_manage_security_rules &&
        this.userDataConfig.worklist_selected_for_manage_security_rules &&
        !this.userData.accessSecurityEnabled
      ) {
        this.accessSecurityWorklistLabel = this.userDataConfig.worklist_label_for_manage_security_rules;
        this.accessSecurityWorklistSelected = this.userDataConfig.worklist_selected_for_manage_security_rules;
        this.structureService.getAccessSecurityWorklistActionLink(this.crossTenantId, this.accessSecurityWorklistSelected).then((data: any) => {
          if (data.length) {
            this.accessSecurityWorklistActionLink = data[0].worklistActionLink;
          } else {
            this.accessSecurityWorklistActionLink = '';
            this.enableSecurityDashboard = false;
          }
        });
      } else if (
        this.userData.accessSecurityType === 'physicianOrPrescriber' &&
        this.userDataConfig.auxilary_physician_worklist_selected_for_manage_security_rules &&
        this.userDataConfig.auxilary_physician_worklist_label_for_manage_security_rules &&
        this.userData.accessSecurityEnabled
      ) {
        this.accessSecurityWorklistLabel = this.userDataConfig.auxilary_physician_worklist_label_for_manage_security_rules;
        this.accessSecurityWorklistSelected = this.userDataConfig.auxilary_physician_worklist_selected_for_manage_security_rules;
        this.structureService.getAccessSecurityWorklistActionLink(this.crossTenantId, this.accessSecurityWorklistSelected).then((data: any) => {
          if (data.length) {
            this.accessSecurityWorklistActionLink = data[0].worklistActionLink;
          } else {
            this.accessSecurityWorklistActionLink = '';
            this.enableSecurityDashboard = false;
          }
        });
      } else if (
        this.userData.accessSecurityType === 'nursingAgency' &&
        this.userDataConfig.auxilary_nursing_agency_worklist_selected_for_manage_security_rules &&
        this.userDataConfig.auxilary_nursing_agency_worklist_label_for_manage_security_rules &&
        this.userData.accessSecurityEnabled
      ) {
        this.accessSecurityWorklistLabel = this.userDataConfig.auxilary_nursing_agency_worklist_label_for_manage_security_rules;
        this.accessSecurityWorklistSelected = this.userDataConfig.auxilary_nursing_agency_worklist_selected_for_manage_security_rules;
        this.structureService.getAccessSecurityWorklistActionLink(this.crossTenantId, this.accessSecurityWorklistSelected).then((data: any) => {
          if (data.length) {
            this.accessSecurityWorklistActionLink = data[0].worklistActionLink;
          } else {
            this.accessSecurityWorklistActionLink = '';
            this.enableSecurityDashboard = false;
          }
        });
      }
    } else {
      this.enableSecurityDashboard = false;
    }
    if (this.userDataConfig.worklist_selected_for_visit_schedule && this.userDataConfig.worklist_selected_for_visit_schedule !== '') {
      this.visitSchedulerWorklistSelected = this.userDataConfig.worklist_selected_for_visit_schedule;
      this.structureService.getVisitSchedulerWorklistActionLink(this.crossTenantId, this.visitSchedulerWorklistSelected).then((data: any) => {
        if (data.length) {
          this.visitSchedulerWorklistActionLink = data[0].worklistActionLink;
          localStorage.setItem('visitSchedulerWorklistActionLink', this.visitSchedulerWorklistActionLink);
          const metaData = data[0].worklistMeta;
          let secRoleMatch = false;
          if (metaData.includeSecRole == true && metaData.secondaryRoles && !isBlank(metaData.secondaryRoles)) {
            const roles = metaData.secondaryRoles.split(',');
            const userRoles = this.userData.assignedRoles.split(',');
            secRoleMatch = roles.some((r) => userRoles.includes(r) && +r !== +this.userData.roleId);
          }
          if (
            (metaData.visibleToRoles && metaData.visibleToRoles.split(',').indexOf(this.userData.roleId) !== -1) ||
            (metaData.allowCrossTenant.toString() === 'true' &&
              metaData.visibleToOtherRoles &&
              metaData.visibleToOtherRoles.split(',').indexOf(this.userData.roleId) !== -1) ||
            secRoleMatch
          ) {
            this.visitSchedulerWorklistRoleSelected = true;
          } else {
            this.visitSchedulerWorklistRoleSelected = false;
          }
        } else {
          this.visitSchedulerWorklistActionLink = '';
          this.visitSchedulerWorklistSelected = '';
          this.visitSchedulerWorklistRoleSelected = false;
        }
      });
    } else {
      this.visitSchedulerWorklistActionLink = '';
      this.visitSchedulerWorklistSelected = '';
      this.visitSchedulerWorklistRoleSelected = false;
    }
    this.socketEventSubscriptions.push(
      this.structureService.subscribeSocketEvent('roomNameChangeNotification').subscribe((data) => {
        /*Dont remove this console*/
        console.log(`roomNameChangeNotification : notification received - ${JSON.stringify(data)}`);
      })
    );
    this.socketEventSubscriptions.push(
      this.structureService.subscribeSocketEvent('messagePolling').subscribe((data) => {
        const advanceSearchFilter = this.storeService.getStoredData(Store.ACTIVE_MESSAGE_ADVANCE_SEARCH_FILTER);

        if (data.args.chatThreadType && advanceSearchFilter && !isBlank(advanceSearchFilter.chatThreadTypes)) {
          this.handleMessagePolling(data, advanceSearchFilter);
        } else {
          this.unreadCountUpdateOnPollingInInbox = true;
          this.messagePolling(data);
        }
      })
    );
    this.socketEventSubscriptions.push(
    this.structureService.subscribeSocketEvent('redirectToInbox').subscribe((data) => {
      let activeStrucuredFormData: any;
      const storedFormData = localStorage.getItem('formData');
      if (!isBlank(storedFormData)) {
        activeStrucuredFormData = JSON.parse(decodeURIComponent(storedFormData));
      } else if (typeof this.structureService.getCookie('formData') === 'object') {
        activeStrucuredFormData = this.structureService.getCookie('formData');
      } else {
        activeStrucuredFormData = JSON.parse(decodeURIComponent(this.structureService.getCookie('formData')));
      }

      let patientId = '';
      let patient_name = '';
      if (
        activeStrucuredFormData.recipient_role_name === UserRoles.patient &&
        activeStrucuredFormData.patientName &&
        isBlank(activeStrucuredFormData.patientName.trim())
      ) {
        patientId = activeStrucuredFormData.recipient_id;
      } else if (activeStrucuredFormData.patientName) {
        patient_name = activeStrucuredFormData.patientName;
      } else {
        patientId = this.userData.userId;
      }
      const page = activeStrucuredFormData.page || '';
      const formData = {
        userid: this.userData.userId,
        tenantId: data.formData.tenant_id,
        recipient_id: data.formData.recipient_id,
        formId: data.formData.form_id,
        staffFacing: data.formData.staffFacing,
        form_submission_id: data.formData.form_submission_id,
        form_id: data.formData.form_id,
        form_name: data.formData.formName,
        patient_id: '',
        id: '',
        patient_name: patient_name,
        patientUser: patientId
      };
      if (data.formData.form_submission_id) {
        this._formsService.filingCenterSubmittedFormdata(formData);
      }
      if (this.router.url.indexOf('/forms/edit-form/') !== -1 && data.formData.fromMob.toString() === 'false') {
        const notifyContent = this._ToolTipService.getTranslateDataWithParam('MESSAGES.THANK_FORM_COMPLETE', { content: data.formData.formName });
        const notify = $.notify(notifyContent);
        setTimeout(function () {
          notify.update({
            type: 'success',
            message: `<strong>${notifyContent}</strong>`
          });
        }, 1000);
        if (data.formData.page === 'formWorlists' || page === 'formWorlists') {
          this.router.navigate(['/forms/list']);
        } else if (data.formData.page === 'myFormWorlists' || page === 'myFormWorlists') {
          this.router.navigate(['/forms/worklist']);
        } else if (this.sharedService.FormsPage) {
          this.router.navigate([this.sharedService.FormsPage]);
        } else {
          this.router.navigate(['/inbox']);
        }
      }
    })
    );
    this.socketEventSubscriptions.push(
    this.structureService.subscribeSocketEvent('activityTrack').subscribe((data) => {
      if (
        !(
          this.router.url.indexOf('/forms/send/list') !== -1 &&
          data.formData.fromMob.toString() === 'false' &&
          data.formData.staffFacing.toString === 'true'
        )
      ) {
        $('#textmessage').text('');
        $('#newloader').hide();
      }
      const displayname = this.userData.displayName;
      setTimeout(() => {
        swal({
          title: data.formData.form_Success_message,
          text: '',
          icon: 'success',
          showCancelButton: false,
          showConfirmButton: false,
          timer: 3000
        });
        if (
          (this.router.url.indexOf('/forms/view-form') !== -1 || this.router.url.indexOf('/forms/view-form') !== -1) &&
          data.formData.fromMob.toString() === 'false'
        ) {
          const activityData = {
            activityName: data.formData.action === 'edit' ? 'Edit Form' : 'Submit Form',
            activityType: 'forms',
            activityDescription: ''
          };
          if (!isBlank(data.formData.staffFacing) && data.formData.staffFacing.toString() === 'true') {
            activityData.activityDescription = `${displayname} has ${data.formData.action === 'edit' ? 'updated' : 'completed'} staff facing form ${
              data.formData.formName
            } (${data.formData.form_id})`;
          } else {
            let resp_name = data.formData.toName;
            if (data.formData.action !== 'edit' && data.formData.recipient_name) {
              resp_name = data.formData.recipient_name;
            }
            activityData.activityDescription = `${displayname} has ${data.formData.action === 'edit' ? 'updated ' : 'completed '}${
              data.formData.formName
            } (${data.formData.form_id}) sent by ${resp_name}`;
          }

          //Add configuration in form type Default Outgoing Filing Center (From Citus Health) after Form Submit.
          let formData = {
            userid: this.userData.userId,
            tenantId: data.formData.tenant_id,
            recipient_id: data.formData.recipient_id,
            formId: data.formData.form_id,
            staffFacing: data.formData.staffFacing,
            form_submission_id: data.formData.submissionId
          };
          this._formsService.filingCenterSubmittedFormdata(formData);
          this.structureService.trackActivity(activityData);
          if (this.sharedService.FormsPage) {
            this.router.navigate([this.sharedService.FormsPage]);
          } else if (data.formData.staffFacing.toString() !== 'true') {
            this.router.navigate(['/inbox']);
          }
        }
      }, 100);
    })
    );
    this.socketEventSubscriptions.push(
    this.structureService.subscribeSocketEvent('formAllowEditPolling').subscribe(() => {
      this.getUserInboxCounts('forms');
    })
    );
    this.socketEventSubscriptions.push(
    this.structureService.subscribeSocketEvent('formPolling').subscribe(() => {
      if (this.router.url != '/forms/worklist' && this.router.url !== '/forms/list') {
        this.getUserInboxCounts('forms');
      }
    })
    );
    this.socketEventSubscriptions.push(
    this.structureService.subscribeSocketEvent('sendFormsToRecipients').subscribe(() => {
      $('#textmessage').text('');
      $('#newloader').hide();
    })
    );
    this.socketEventSubscriptions.push(
    this.structureService.subscribeSocketEvent('userBusy').subscribe((data) => {
      const notifyMsg = this._ToolTipService.getTranslateDataWithParam('MESSAGES.BUSY_WITH_OTHER_CALL', { content: data.busyUserName });
      const notify = $.notify(notifyMsg);
      setTimeout(function () {
        notify.update({
          type: 'warning',
          message: `<strong>${notifyMsg}</strong>`
        });
      }, 1000);
    })
    );
    this.socketEventSubscriptions.push(
    this.structureService.subscribeSocketEvent('videoChatToUser').subscribe((data) => {
      this.videoChatData = data;
      localStorage.setItem('videoChatData', JSON.stringify(data));
      if (document.getElementById('video_container').style.display === 'block') {
        document.getElementById('video-call-notify').style.display = 'none';
        this.playAudio(false);
        this.structureService.socket.emit('busyUser', {
          busyUserId: this.userData.userId,
          busyUserName: this.userData.displayName,
          roomId: this.videoChatData.roomId,
          fromId: data.fromId
        });
        return false;
      }
      if (+this.sharedService.callInitiatedRoomId === +data.roomId) {
        return false;
      }

      // Show notification in browser
      const notifyMessage = this._ToolTipService.getTranslateDataWithParam('MESSAGES.INCOMING_VIDEO_CALL', {
        content: data.tenantName || data.from
      });
      let sourceTenantId = '0';
      let title = 'CitusHealth';
      if (localStorage.getItem('sourceTenantId')) {
        sourceTenantId = localStorage.getItem('sourceTenantId');
        title = this.userData.config.app_name;
      }
      const iconPath = `${this.sharedService.assetsUrl}/a/${sourceTenantId}/img/account-notification-logo.png`;
      const { userDataConfig } = this.structureService;
      const userDetail = this.structureService.userDetails;
      notifyMe(title, notifyMessage, iconPath, `reroute?${data.roomId}?${window.location.href}`, null, userDetail, userDataConfig, () => {
        localStorage.setItem('enterdOninit', 'false');
        this.sharedService.pushNotification = true;
        localStorage.setItem('targetId', data.roomId);
        localStorage.setItem('targetName', 'group-chat');
        localStorage.setItem('chatWithHeading', `Chat with ${data.from}`);
        localStorage.setItem('activeMessage', data.activeMessage);
        localStorage.setItem('archived', 'false');
      });
      this.sharedService.joincall.emit();
      document.getElementById('video-call-notify').style.display = 'block';
      document.getElementById('video-notify-cntnt').innerHTML = `Incoming Call From ${data.from}`;
      this.playAudio(true);
      this.ringTimmer = setTimeout(() => {
        this.ringNotificationClose();
      }, this._commonVideoService.ringWaitingTime); //1 min
    })
    );
    this.socketEventSubscriptions.push(
    this.structureService.subscribeSocketEvent('userAccpetYourCall').subscribe((data) => {
      this.sharedService.inviteUserVideoTile.emit({ joineeId: data.joineeId });
      this.playAudio(false);
    })
    );
    this.socketEventSubscriptions.push(
    this.structureService.subscribeSocketEvent('videoChatReInitializeFromUser').subscribe((data) => {
      this.hideVideoCallNotify(data);
      if (data.showPopup.toString() === 'true') {
        this.sharedService.initiatorReload.emit(data);
      } else {
        data.action = 'endcall';
        data.initiatorName = data.userName;
        this.sharedService.endVideoCall.emit(data);
      }
    })
    );
    this.socketEventSubscriptions.push(
    this.structureService.subscribeSocketEvent('videoChatRejectFromUser').subscribe((data) => {
      this.sharedService.muteInitiatorAudio.emit({ data: true });
      if (data.action !== 'endcall') {
        this.setLocalStorageParticipants('false');
      } else {
        this.sharedService.endVideoCall.emit(data);
        if (+this.videoChatData.roomId === +data.roomId) {
          this.hideVideoCallNotify(data);
        }
      }
      if (data.action === 'initiator') {
        // close popup
        this.hideVideoCallNotify(data);
        this.sharedService.videoCallEndByInitiator.emit({ chatroomId : +data.roomId});
      } else if (data.action === 'user') {
        if (+this.userData.userId !== +data.rejectUser && !isBlank(this.sharedService.onGoingChatrooom)) {
          const notifyMsg = this._ToolTipService.getTranslateDataWithParam('MESSAGES.REJECTED_VIDEO_CHAT', { content: data.RejectUserName });
          const notify = $.notify(notifyMsg);
          setTimeout(function () {
            notify.update({
              type: 'warning',
              message: `<strong>${notifyMsg}</strong>`
            });
          }, 1000);
        }
        this.sharedService.inviteUserVideoTile.emit({ rejectUser: data.rejectUser });
      } else if (data.action === 'notAccept') {
        if (+this.userData.userId !== +data.rejectUser && !isBlank(this.sharedService.onGoingChatrooom)) {
          const notifyMsg = this._ToolTipService.getTranslateDataWithParam('MESSAGES.NOT_ATTEND_VIDEO_CHAT', { content: data.RejectUserName });
          const notify = $.notify(notifyMsg);
          setTimeout(function () {
            notify.update({
              type: 'warning',
              message: `<strong>${notifyMsg}</strong>`
            });
          });
        }
        this.sharedService.inviteUserVideoTile.emit({ rejectUser: data.rejectUser});
      }
    })
    );
    this.socketEventSubscriptions.push(
    this.structureService.subscribeSocketEvent('allUsersNotAccept').subscribe((data) => {
      if (!isBlank(this.sharedService.onGoingChatrooom)) {
        this.sharedService.muteInitiatorAudio.emit();
        if (isBlank(data.callStatus)) {
          data.src = 'busy.mp3';
          this.playAudio(true, 'notaccept', data);
        } else if (!this.sharedService.applessVideo) {
          const audio = { src: 'nouser.mp3', callStatus: 'onOnlineUsers', };
          this.playAudio(true, 'notaccept', audio);
        }
      }
    })
    );
    this.socketEventSubscriptions.push(
    this.structureService.subscribeSocketEvent('updateConfigPolling').subscribe((data) => {
      const activityData = {
        activityName: 'Preference Refresh/Privilege Refresh/Schedule Refresh',
        activityType: 'settings update',
        activityDescription: ''
      };
      if (data.configurationType === 'updatePrivilage') {
        activityData.activityName = 'Privilege Refresh';
        activityData.activityDescription = `Privileges of ${this.userInfo.roleName} Role updated by ${data.updatedBy.displayName}`;

        this.userData.privileges = '';
        this.userData.privileges = data.privilageData;
        this.manageConfig = this.userData.config;
        let defaultPage = '/profile';
        const enablePages = [];
        enablePages.push('/profile');
        let nursingAgencyUser = false;
        if (
          this.manageConfig.enable_nursing_agencies_visibility_restrictions &&
          +this.manageConfig.enable_nursing_agencies_visibility_restrictions === 1 &&
          !isBlank(this.manageConfig.nursing_agencies)
        ) {
          nursingAgencyUser = true;
        }
        if (this.manageConfig.enable_message_center && +this.manageConfig.enable_message_center === 1) {
          defaultPage = '/inbox';
        } else if (this.manageConfig.show_document_tagging && +this.manageConfig.show_document_tagging === 1) {
          if (
            (this.userData.privileges.indexOf('viewAllSignedDocs') === -1 && this.userData.privileges.indexOf('manageTenants') === -1) ||
            nursingAgencyUser
          ) {
            defaultPage = '/signature/signature-requests-list';
          } else if (
            (this.userData.privileges.indexOf('viewAllSignedDocs') !== -1 || this.userData.privileges.indexOf('manageTenants') !== -1) &&
            !nursingAgencyUser
          ) {
            defaultPage = '/signature/signed-documents-list';
          } else {
            defaultPage = this.permissionService.getDefaultPageFormCenter(this.userData.config, this.userData.privileges);
          }
        } else {
          defaultPage = this.permissionService.getDefaultPageFormCenter(this.userData.config, this.userData.privileges);
        }
        this.manageConfig.defaultPage = defaultPage;
        this.userData.defaultPage = defaultPage;
        this.userData.config.defaultPage = defaultPage;

        //get enablled pages.
        if (this.manageConfig.enable_message_center && +this.manageConfig.enable_message_center === 1) {
          enablePages.push('/inbox');
        }
        if (this.manageConfig.show_document_tagging && this.manageConfig.show_document_tagging === 1) {
          if (
            (this.userData.privileges.indexOf('viewAllSignedDocs') === -1 && this.userData.privileges.indexOf('manageTenants') === -1) ||
            nursingAgencyUser
          ) {
            enablePages.push('/signature/signature-requests-list');
          }
          if (
            (this.userData.privileges.indexOf('viewAllSignedDocs') !== -1 || this.userData.privileges.indexOf('manageTenants') !== -1) &&
            !nursingAgencyUser
          ) {
            enablePages.push('/signature/signed-documents-list');
          }

          enablePages.push(this.permissionService.getDefaultPageFormCenter(this.manageConfig, this.userData.privileges));
        }
        enablePages.push(this.permissionService.getDefaultPageFormCenter(this.manageConfig, this.userData.privileges));

        if (this.userData.routingPage && isBlank(this.userData.routingPage)) {
          if (enablePages.indexOf(this.userData.routingPage) !== -1) {
            if (this.userData.accessSecurityEnabled && this.userData.accessSecurityWorklistActionLink) {
              this.manageConfig.defaultPage = this.userData.accessSecurityWorklistActionLink;
              this.userData.defaultPage = this.userData.accessSecurityWorklistActionLink;
            } else {
              this.manageConfig.defaultPage = this.userData.routingPage;
              this.userData.defaultPage = this.userData.routingPage;
            }
          } else {
            this.manageConfig.defaultPage = defaultPage;
            this.userData.defaultPage = defaultPage;
          }
        }

        this.structureService.loginUserDetails = this.userData;
        this.structureService.loginUserDetailsForRouteCheck['defaultPage'] = this.userData.defaultPage;
        this.structureService.loginUserDetailsForRouteCheck['privileges'] = this.userData.privileges;

        this.previlages = {};
        this.structureService.setCookie('userPrivileges', this.userData.privileges, 1);
        this.sharedService.sessionRefresh.emit(this.userData);

        this.structureService.userDetails = JSON.stringify(this.userData);
        this.structureService.userDataConfig = JSON.stringify(this.userData.config);
        this.reloadCurrentController();
      } else if (data.configurationType === 'updateTenant') {
        this.structureService.getAllTimeZones().then((timezones: any) => {
          activityData.activityName = 'Preference Refresh';
          activityData.activityDescription = `Tenant Preferences updated by ${data.updatedBy.displayName}`;
          let updated_configuration_key;
          this.structureService.loginUserDetails = this.userData;
          let checkFnStatus = 0;
          const tempUserConfig = this.structureService.loginUserDetails['config'];
          data.tenantData.forEach((value) => {
            updated_configuration_key = value.configuration_key;
            tempUserConfig[updated_configuration_key] = value.configuration_value;
            checkFnStatus += 1;

            if (checkFnStatus === data.tenantData.length) {
              let weekend_days = [];
              if ('weekend_days' in tempUserConfig) {
                weekend_days = tempUserConfig['weekend_days'].split(',');
                if (!(weekend_days.length > 1)) {
                  weekend_days = ['6', '0'];
                }
              } else {
                weekend_days = ['6', '0'];
              }
              tempUserConfig['weekend_days'] = weekend_days;
              if ('tenant_timezone' in tempUserConfig) {
                let selectedTimeZoneData: any = {};
                if (timezones && timezones.length) {
                  selectedTimeZoneData = timezones.find((zone, i) => {
                    return zone.offset === tempUserConfig['tenant_timezone'];
                  });
                  if (selectedTimeZoneData && selectedTimeZoneData.current_offset) {
                    tempUserConfig['tenant_timezone_offset'] = tempUserConfig['tenant_timezone'];
                    tempUserConfig['tenant_timezone'] = selectedTimeZoneData.current_offset;
                    tempUserConfig['tenant_timezoneName'] = selectedTimeZoneData.city;
                  } else {
                    tempUserConfig['tenant_timezone'] = tempUserConfig.tenant_timezone.split(',')[0];
                  }
                } else {
                  tempUserConfig.tenant_timezone = tempUserConfig.tenant_timezone.split(',')[0];
                }
              }
              const timezoneConfig = JSON.parse(this.structureService.userDetails).config_replica;
              tempUserConfig['tenant_timezoneNameValue'] = timezoneConfig['tenant_timezoneNameValue'];
              if (data && 'siteConfigs' in data && data.siteConfigs && this.userData.config['enable_multisite'] !== '1') {
                const siteConfig = JSON.parse(data.siteConfigs);
                const objKeys = Object.keys(siteConfig);
                objKeys.forEach((elem) => {
                  this.userData['siteConfigs'][elem] = siteConfig[elem];
                });
              }
              this.userData.config = tempUserConfig;
              if (+this.userData.config.enable_filing_center === 1) {
                this.enableFilingCenter = true;
              } else {
                this.enableFilingCenter = false;
              }
              let patientTopicCount: any = 0;
              if (+this.userData.config.show_infusion_support === 1) {
                patientTopicCount += 1;
              }
              if (+this.userData.config.enable_when_is_my_nurse_coming === 1) {
                patientTopicCount += 1;
              }
              if (+this.userData.config.enable_where_is_my_delivery === 1) {
                patientTopicCount += 1;
              }
              this.patientTopic.count = patientTopicCount;
              localStorage.setItem('patientTopicCount', patientTopicCount);
              this.structureService.loginUserDetails['config'] = this.userData.config;
              this.structureService.loginUserDetailsForRouteCheck['config'] = this.userData.config;
              if (this.userData && 'siteConfigs' in this.userData && this.userData.siteConfigs) {
                this.structureService.loginUserDetails['siteConfigs'] = this.userData.siteConfigs;
                this.structureService.loginUserDetailsForRouteCheck['siteConfigs'] = this.userData.siteConfigs;
              }
              this.manageConfig = this.userData.config;
              let defaultPage = '/profile';
              let nursingAgencyUser = false;
              if (
                this.manageConfig.enable_nursing_agencies_visibility_restrictions &&
                +this.manageConfig.enable_nursing_agencies_visibility_restrictions === 1 &&
                this.manageConfig.nursing_agencies != ''
              ) {
                nursingAgencyUser = true;
              }
              if (this.manageConfig.enable_message_center && +this.manageConfig.enable_message_center === 1) {
                defaultPage = '/inbox';
              } else if (this.manageConfig.show_document_tagging && +this.manageConfig.show_document_tagging === 1) {
                if (
                  (this.userData.privileges.indexOf('viewAllSignedDocs') === -1 && this.userData.privileges.indexOf('manageTenants') === -1) ||
                  nursingAgencyUser
                ) {
                  defaultPage = '/signature/signature-requests-list';
                } else if (
                  (this.userData.privileges.indexOf('viewAllSignedDocs') !== -1 || this.userData.privileges.indexOf('manageTenants') !== -1) &&
                  !nursingAgencyUser
                ) {
                  defaultPage = '/signature/signed-documents-list';
                } else {
                  defaultPage = this.permissionService.getDefaultPageFormCenter(this.userData.config, this.userData.privileges);
                }
              } else {
                defaultPage = this.permissionService.getDefaultPageFormCenter(this.userData.config, this.userData.privileges);
              }
              this.manageConfig.defaultPage = defaultPage;
              this.userData.defaultPage = defaultPage;
              this.userData.config.defaultPage = defaultPage;
              if (+this.manageConfig.enable_message_center !== 1) {
                this.structureService.closeChatWithModal();
              }
              //get enablled pages.
              const enablePages = [];
              enablePages.push('/profile');
              if (this.manageConfig.enable_message_center && +this.manageConfig.enable_message_center === 1) {
                enablePages.push('/inbox');
              }
              if (this.manageConfig.show_document_tagging && this.manageConfig.show_document_tagging == '1') {
                if (
                  (this.userData.privileges.indexOf('viewAllSignedDocs') === -1 && this.userData.privileges.indexOf('manageTenants') === -1) ||
                  nursingAgencyUser
                ) {
                  enablePages.push('/signature/signature-requests-list');
                }
                if (
                  (this.userData.privileges.indexOf('viewAllSignedDocs') !== -1 || this.userData.privileges.indexOf('manageTenants') !== -1) &&
                  !nursingAgencyUser
                ) {
                  enablePages.push('/signature/signed-documents-list');
                }
                enablePages.push(this.permissionService.getDefaultPageFormCenter(this.manageConfig, this.userData.privileges));
              }
              enablePages.push(this.permissionService.getDefaultPageFormCenter(this.manageConfig, this.userData.privileges));
              if (this.userData.routingPage && !isBlank(this.userData.routingPage)) {
                if (enablePages.indexOf(this.userData.routingPage) !== -1) {
                  if (this.userData.accessSecurityEnabled && this.userData.accessSecurityWorklistActionLink) {
                    this.manageConfig.defaultPage = this.userData.accessSecurityWorklistActionLink;
                    this.userData.defaultPage = this.userData.accessSecurityWorklistActionLink;
                  } else {
                    this.manageConfig.defaultPage = this.userData.routingPage;
                    this.userData.defaultPage = this.userData.routingPage;
                  }
                } else {
                  this.manageConfig.defaultPage = defaultPage;
                  this.userData.defaultPage = defaultPage;
                }
              }
              this.structureService.loginUserDetailsForRouteCheck.defaultPage = this.userData.defaultPage;
              this.structureService.userDataConfig = JSON.stringify(this.userData.config);
              this.structureService.userDetails = JSON.stringify(this.userData);
              this.structureService.setCookie('userConfig', this.userData.config, 1);
              localStorage.setItem('tenantTimezoneName', this.userData.config.tenant_timezoneName);
              this.reloadCurrentController();
            }
          });
        });
      } else if (data.configurationType === 'updateSchedule') {
        const scheduleKeys = data.scheduleData;
        activityData.activityName = 'Schedule Refresh';
        activityData.activityDescription = `Schedule of user ${scheduleKeys.scheduledata.displayName} updated by ${data.updatedBy.displayName}`;
        const todaysDate = new Date();
        const yyyy = todaysDate.getFullYear().toString();
        const mm = (todaysDate.getMonth() + 1).toString();
        const dd = todaysDate.getDate().toString();
        const mmChars = mm.split('');
        const ddChars = dd.split('');
        const date = yyyy + (mmChars[1] ? mm : `0${mmChars[0]}`) + (ddChars[1] ? dd : `0${ddChars[0]}`);

        const clientSideDate = date;
        const clientSideDateNumber = todaysDate.getDay();
        const params = {
          userId: scheduleKeys.scheduledata.userId,
          tenantId: scheduleKeys.scheduledata.tenantId,
          formattedDate: clientSideDate,
          getDay: clientSideDateNumber,
          escalated_schedule: scheduleKeys.scheduledata.escalated_schedule,
          userName: scheduleKeys.scheduledata.userName,
        };

        this.structureService.getUserDaySchedule(params).then((scheduleData) => {
          let schedulerData;
          if (+scheduleKeys.scheduledata.escalated_schedule === 0) {
            schedulerData = this.structureService.convertSchedulerDataToClientTZ(
              JSON.stringify(scheduleData['scheduleOutput']),
              null,
              this.structureService
            );
          } else {
            schedulerData = this.structureService.convertSchedulerDataToClientTZ(
              JSON.stringify(scheduleData['scheduleOutput']),
              -4,
              this.structureService
            );
          }
          let updatedSchedulerData = JSON.stringify(schedulerData);
          if (+updatedSchedulerData === 0) {
            updatedSchedulerData = '[]';
          }
          if (+scheduleKeys.scheduledata.escalated_schedule === 0) {
            this.userData.schedulerData[scheduleKeys.scheduledata.userId] = updatedSchedulerData;
            this.structureService.userDetails = JSON.stringify(this.userData);
          } else {
            this.userData.escalatedSchedulerData[scheduleKeys.scheduledata.userId] = updatedSchedulerData;
            this.structureService.userDetails = JSON.stringify(this.userData);
          }
          this.reloadCurrentController();
        });
      }
    })
    );
    this.socketEventSubscriptions.push(
    this.structureService.subscribeSocketEvent('updateMasterConfigPolling').subscribe((data) => {
      this.structureService.getAllTimeZones().then((timezones: any) => {
        let updatedConfigurationKey;
        this.structureService.loginUserDetails = this.userData;
        let checkFnStatus = 0;
        const tempUserConfig = this.structureService.loginUserDetails['master_config'];
        data.tenantData.forEach((value) => {
          updatedConfigurationKey = value.configuration_key;
          tempUserConfig[updatedConfigurationKey] = value.configuration_value;
          checkFnStatus += 1;
          if (checkFnStatus === data.tenantData.length) {
            let weekend_days = [];
            if ('weekend_days' in tempUserConfig) {
              weekend_days = tempUserConfig['weekend_days'].split(',');
              if (!(weekend_days.length > 1)) {
                weekend_days = ['6', '0'];
              }
            } else {
              weekend_days = ['6', '0'];
            }
            tempUserConfig['weekend_days'] = weekend_days;
            if ('tenant_timezone' in tempUserConfig) {
              let selectedTimeZoneData: any = {};
              if (timezones && timezones.length) {
                selectedTimeZoneData = timezones.find((zone) => {
                  return zone.offset.toString() === tempUserConfig.tenant_timezone.toString();
                });
                if (selectedTimeZoneData && selectedTimeZoneData.current_offset) {
                  tempUserConfig['tenant_timezone_offset'] = tempUserConfig['tenant_timezone'];
                  tempUserConfig['tenant_timezone'] = selectedTimeZoneData.current_offset;
                  tempUserConfig['tenant_timezoneName'] = selectedTimeZoneData.city;
                } else {
                  tempUserConfig['tenant_timezone'] = tempUserConfig.tenant_timezone.split(',')[0];
                }
              } else {
                tempUserConfig['tenant_timezone'] = tempUserConfig['tenant_timezone'].split(',')[0];
              }
            }
            this.userData.master_config = tempUserConfig;
            this.structureService.loginUserDetails['master_config'] = this.userData.master_config;
            this.structureService.loginUserDetailsForRouteCheck.master_config = this.userData.master_config;
            this.structureService.userDetails = JSON.stringify(this.userData);
            this.reloadCurrentController();
          }
        });
      });
    })
    );
    
    if (this.structureService.signatureListServerSidePagination) {
      this.socketEventSubscriptions.push(
      this.structureService.subscribeSocketEvent('obtainSignResendPollingToClient').subscribe((data) => {
        if (data.documentId) {
          this.sharedService.signatureRequestPendingInboxData.emit(data);
          for (let i = 0; i < this.structureService.signatureRequestInboxData.length; i += 1) {
            const value = this.structureService.signatureRequestInboxData[i];
            if (+value.id === +data.documentId) {
              this.structureService.signatureRequestInboxData[i].createdOn = data.createdOn;
              if (+data.owner !== +this.userData.userId) this.structureService.signatureRequestInboxData[i].isRead = false;
              this.structureService.signatureRequestInboxData[i].archivedUsers = '';
              this.sharedService.signatureRequestInboxData.emit(this.structureService.signatureRequestInboxData);
            }
          }
        }
      })
      );
      this.socketEventSubscriptions.push(
      this.structureService.subscribeSocketEvent('obtainSignPolling').subscribe((data) => {
        if (data.message) {
          const itemData = data.message;
          itemData.isRead = false;
          this.sharedService.signatureRequestPollingInboxData.emit(itemData);
          if (this.structureService.signatureRequestInboxData.indexOf(itemData) === -1)
            this.structureService.signatureRequestInboxData.push(itemData);
          this.signatureRequest = this.structureService.signatureRequestInboxData;
          this.showNotificationWindow(data.signatureStatus === CONSTANTS.documentFilterKeys.SIGNED);
        } else if (data.documentId) {
          const notifyUser = data.data && data.data.user ? data.data.user : '';
          const loginedUser = this.userData.userId;
          if (this.structureService.signatureRequestInboxData && this.structureService.signatureRequestInboxData.length > 0) {
            for (let i = 0; i < this.structureService.signatureRequestInboxData.length; i += 1) {
              const value = this.structureService.signatureRequestInboxData[i];
              if (+value.id === +data.documentId) {
                this.structureService.signatureRequestInboxData[i].archivedOn = data.data && data.data.archivedOn ? data.data.archivedOn : '';
                if (data.accountLevelArchived || data.accountLevelArchived) {
                  this.structureService.signatureRequestInboxData[i].archivedUsers = data.archivedUsers;
                  this.structureService.signatureRequestInboxData[i].archivedOn = data.archivedOn;
                  this.structureService.signatureRequestInboxData[i].accountLevelArchived = data.accountLevelArchived;
                  this.structureService.signatureRequestInboxData[i].accountLevelArchivedUser = data.accountLevelArchivedUser;
                } else {
                  const date = new Date();
                  const datetostring = Date.parse(date.toString()) / 1000;
                  this.structureService.signatureRequestInboxData[i].signatureStatus = data.signatureStatus;
                  this.structureService.signatureRequestInboxData[i].signedOn = data.signedOn ? data.signedOn : datetostring;
                  this.structureService.signatureRequestInboxData[i].isRead = false;
                  if (isBlank(notifyUser) || parseInt(notifyUser) !== parseInt(loginedUser)) {
                    this.showNotificationWindow(data.signatureStatus === CONSTANTS.documentFilterKeys.SIGNED);
                  }
                }
               }
            }
          } else {
            this.getDocumentUnreadCount(false);
          }
          this.sharedService.signatureRequestInboxData.emit(this.structureService.signatureRequestInboxData);
        }
      })
      );
    }
    this.socketEventSubscriptions.push(
    this.structureService.subscribeSocketEvent('chatWithPolling').subscribe((data) => {
      if (data && data.type == 'group') {
        let newMessageGroupData;
        if (data.data && data.data.status) {
          newMessageGroupData = data.data;
        } else {
          newMessageGroupData = {
            status: 'signUp',
            userId: this.userData.userId,
            tenantId: this.userData.tenantId,
          };
        }
        this.sharedService.newMessageGroup.emit(newMessageGroupData);
      } else if (data && data.type == 'user') {
        this.sharedService.userDataUpdate.emit(data.data);
      } else {
      }
    })
    );
    this.socketEventSubscriptions.push(
    this.structureService.subscribeSocketEvent('userJoinedToVideoChat').subscribe((data) => {
      if (data.joineeId == this.userData.userId) {
        document.getElementById('video-call-notify').style.display = 'none';
        this.playAudio(false);
        clearTimeout(this.ringTimmer);
        this.sharedService.onGoingChatrooom = data.roomId;
        this.sharedService.joincall.emit();
      }
    })
    );

    $(function () {

      // scripts for "menu-left" module

      /* ///////////////////////////////////////////////////////////////////////////////////////
          add backdrop */

      $('.cat__menu-left').after(
        '<div class="cat__menu-left__backdrop cat__menu-left__action--backdrop-toggle"><!-- --></div>'
      );

      /* ///////////////////////////////////////////////////////////////////////////////////////
          submenu */

      $('.cat__menu-left__submenu > a')
        .off('click')
        .on('click', function () {
          if (
            $('body').hasClass('cat__config--vertical') ||
            $('body').width() < 768
          ) {
            let parent = $(this).parent();
            let opened = $('.cat__menu-left__submenu--toggled');

            if (
              !parent.hasClass('cat__menu-left__submenu--toggled') &&
              !parent.parent().closest('.cat__menu-left__submenu').length
            )
              opened
                .removeClass('cat__menu-left__submenu--toggled')
                .find('> .cat__menu-left__list')
                .slideUp(200);

            parent.toggleClass('cat__menu-left__submenu--toggled');
            parent.find('> .cat__menu-left__list').slideToggle(200);
          }
        });

      // remove submenu toggle class when viewport back to full view
      $(window).on('resize', function () {
        if (
          $('body').hasClass('cat__config--horizontal') ||
          $('body').width() > 768
        ) {
          $('.cat__menu-left__submenu--toggled')
            .removeClass('cat__menu-left__submenu--toggled')
            .find('> .cat__menu-left__list')
            .attr('style', '');
        }
      });

      /* ///////////////////////////////////////////////////////////////////////////////////////
          custom scroll init */

      if ($('body').hasClass('cat__config--vertical')) {
        if (!/Mobi/.test(navigator.userAgent) && jQuery().jScrollPane) {
          $('.cat__menu-left__inner').each(function () {
            $(this).jScrollPane({
              contentWidth: '0px',
              autoReinitialise: true,
              autoReinitialiseDelay: 100,
            });
            let api = $(this).data('jsp');
            let throttleTimeout;
            $(window).bind('resize', function () {
              if (!throttleTimeout) {
                throttleTimeout = setTimeout(function () {
                  api.reinitialise();
                  throttleTimeout = null;
                }, 50);
              }
            });
          });
        }
      }

      /* ///////////////////////////////////////////////////////////////////////////////////////
          toggle menu */

      $('.cat__menu-left__action--menu-toggle').on('click', function () {
        if ($('body').width() < 768) {
          $('body').toggleClass('cat__menu-left--visible--mobile');
        } else {
          $('body').toggleClass('cat__menu-left--visible');
          if (!$('body').hasClass('cat__menu-left--visible')) {
            $('body .cat__menu-left').addClass('toggleMenuClick');
          }
        }
      });
      $('body .cat__menu-left').on('mouseenter', function () {
        $('body .cat__menu-left').removeClass('toggleMenuClick');
      });

      $('.cat__menu-left__action--backdrop-toggle').on('click', function () {
        $('body').removeClass('cat__menu-left--visible--mobile');
      });

      /* ///////////////////////////////////////////////////////////////////////////////////////
          colorful menu */

      let colorfulClasses =
        'cat__menu-left--colorful--primary cat__menu-left--colorful--pulse cat__menu-left--colorful--secondary cat__menu-left--colorful--primary cat__menu-left--colorful--pulse cat__menu-left--colorful--default cat__menu-left--colorful--info cat__menu-left--colorful--success cat__menu-left--colorful--warning cat__menu-left--colorful--danger cat__menu-left--colorful--yellow';
      let colorfulClassesArray = colorfulClasses.split(' ');

      function setColorfulClasses() {
        $('.cat__menu-left__list--root > .cat__menu-left__item').each(
          function () {
            let randomClass =
              colorfulClassesArray[
                Math.floor(Math.random() * colorfulClassesArray.length)
              ];
            $(this).addClass(randomClass);
          }
        );
      }

      function removeColorfulClasses() {
        $('.cat__menu-left__list--root > .cat__menu-left__item').removeClass(
          colorfulClasses
        );
      }

      if ($('body').hasClass('cat__menu-left--colorful')) {
        setColorfulClasses();
      }

      $('body').on('setColorfulClasses', function () {
        setColorfulClasses();
      });

      $('body').on('removeColorfulClasses', function () {
        removeColorfulClasses();
      });
    });
    const currentTime = new Date().getTime();

    if (this.structureService.getCookie('showPrototype') === '1') {
      if (
        this.structureService
          .getCookie('userPrivileges')
          .indexOf('patientSurveys') !== -1
      ) {
       
      }
      if (
        this.structureService
          .getCookie('userPrivileges')
          .indexOf('viewVerbalOrder') !== -1
      ) {
        
      }
      if (
        this.structureService
          .getCookie('userPrivileges')
          .indexOf('startSurvey') !== -1
      ) {
        
      }
    }
    if (this.userData.config.show_document_tagging === '1') {
      if (!this.structureService.signatureListServerSidePagination) {
        this.getSignatureDocument();
      } else {
        this.getDocumentUnreadCount();
      }
    } else {
      this.signatureCountLoading = false;
    }
    this.getUserInboxCounts('forms');

    if (
      !this.structureService.previousUrlNow ||
      this.structureService.previousUrlNow == '/login'
    ) {
      const data = this.structureService.checkDefaultPageExistsInUserData(
        this.userData
      );
      this.userData['defaultPage'] = data;
      if (this.userData.defaultPage == '/inbox') this.activate(true);
    }

    //signatureCountLoading
    this.stateInitActivatedByTenantChange = false;
    //Get the App Center data: Apps and worklists
    if( !isBlank(this.manageConfig.enable_app_center_left_menu) &&
     this.manageConfig.enable_app_center_left_menu === '1') {
      this.intakeService.getAllAppNames().then(data => {
        if(!isBlank(data['apps'])) {
          data['apps'].forEach(item => {
            this.intakeService.parentWorklistMenu = '';
            this.intakeService.getWorklistMenuPulse(item.guid, null).then(worklistData => {
              if(!isBlank(worklistData)) {
                const worklistDetails = worklistData.reduce((result, worklistItem) => 
                [...result, ...worklistItem.worklists], []);
                this.appCenterData.push({appName: item.name, worklists: worklistDetails});                
              }
            });
          });
        }
      });
    }
    // Message delete/restore socket subscription
    this.socketEventSubscriptions.push(
      this.structureService.subscribeSocketEvent('updateChatMessageDeleteStatus').subscribe((args) => {
        if (args && args.chatroomId && args.messageId) {
          if(args.isMaskedChatThread) {
            this.sharedService.$maskedMessageDeleteRestoreSubject.next(args);
          }
          this.messageService.getChatMessages({ chatroomId: args.chatroomId }).subscribe((response: any) => {
            const data = response && response.data || []; 
            // Push data.message into data.args
            if(data.message.isSelfMessage) {
              data.message.selfUpdate = true;
            }
            if (data.message) {
             data.args = data.message ;
             data.args.type = 'messages';
             data.args.senderUserDetails = [data.args.senderInfo];
             data.args.removeChatroomFromListOnPolling = '';
             data.args.notUpdateCount = true;
             }
             //IF filter applied,check for the message category, then update
             const messageCategory = data.message.messageCategory;
             const advanceSearchFilter = this.storeService.getStoredData(Store.ACTIVE_MESSAGE_ADVANCE_SEARCH_FILTER);
             if (!isBlank(messageCategory) && advanceSearchFilter && !isBlank(advanceSearchFilter.chatThreadTypes)) {
              const argsChatThreadTypeNumber = messageTypeAndCategory[messageCategory];
              const advanceSearchChatThreadTypeNumbers = advanceSearchFilter.chatThreadTypes.toString().split(',').map(Number);  
              if (advanceSearchChatThreadTypeNumbers.includes(argsChatThreadTypeNumber)) {
                this.setInboxDataOnPolling(data);
              };
            } else {
              this.setInboxDataOnPolling(data);
            }          
          });
          this.sharedService.$messageDeleteRestore.next(args);       
        }
      })
    );
  }
  hideVideoCallNotify(data) {
    this.playAudio(false);
    $('#video-call-notify').hide();
    clearTimeout(this.ringTimmer);
    this.clearPushNotification(data);
  }
  showNotificationWindow(signed=false){
    const popUpMessage = signed ? 'SUCCESS_MESSAGES.NEW_SIGNED_SIGNATURE_RECEIVED' : 'SUCCESS_MESSAGES.NEW_SIGNATURE_RECEIVED';
    this.structureService.notifyMessage({
      messge: this._ToolTipService.getTranslateData(popUpMessage),
      type: CONSTANTS.notificationTypes.success
    });
  }
  setInboxDataOnPolling(data) {
    if (data.args && data.args.type) {
      if (data.args.type === 'forms') {
        if (this.userData.config.enable_forms === '1') {
          this.getUserInboxCounts('forms');
        }
      } else if (data.args.customData && data.args.customData.action === 'updateInboxTags') {
        this.updateInboxTags(data.args);
      } else if (data.args.type === 'messages') {
        let updateInboxCountsCalled = data.args.notUpdateCount || false;
        
          if (
            data &&
            data.args &&
            data.args.chatroomId &&
            ((data.args.senderUserDetails && data.args.senderUserDetails.length) ||
              data.args.selfUpdate ||
              data.args.firstUserMessageFromChatroom ||
              data.args.topic)
          ) {
            let callAPIToUpdate = true;
            let isMaskedMainThreadUpdateNeeded = false;
            let currentInboxData = JSON.parse(JSON.stringify(this.structureService.inboxDataFirstPage));
            if (
              this.router.url !== '/inbox/chatroom' ||
              (this.router.url === '/inbox/chatroom' && +localStorage.getItem('targetId') !== +data.args.chatroomId)
            ) {
              let countUpdated = false;
              currentInboxData.map((inboxmessage) => {
                if (!countUpdated) {
                  if (+inboxmessage.messageType === 2 || +inboxmessage.baseChatroomId !== 0) {
                    if (+inboxmessage.messageType === 2) {
                      if (inboxmessage.maskedSubCount && +inboxmessage.chatroomId === +data.args.baseId) {
                        callAPIToUpdate = false;
                        if (inboxmessage.subList && inboxmessage.subList.length) {
                          let subChatroomExist = inboxmessage.subList.findIndex(
                            (chatmessage) =>
                              +chatmessage.chatroomid === +data.args.chatroomId
                          );
                          
                          if (subChatroomExist === -1) {
                            inboxmessage.subList.push({chatroomid: data.args.chatroomId, baseId: inboxmessage.chatroomId});
                            subChatroomExist = inboxmessage.subList.length - 1;
                          }
                          if (subChatroomExist !== -1) {
                            callAPIToUpdate = false;
                            if (data.args.senderUserDetails) {
                              if (
                                !parseInt(
                                  inboxmessage.subList[subChatroomExist].unread
                                )
                              ) {
                                inboxmessage.subList[subChatroomExist].unread = 1;
                              } 
                              inboxmessage.subList[subChatroomExist].unreadCount = 
                              inboxmessage.subList[subChatroomExist].unreadCount 
                              ? parseInt(inboxmessage.subList[subChatroomExist].unreadCount) + 1 : 1;
                              inboxmessage.hasUnreadMessages = true;
                              inboxmessage.subList[
                                subChatroomExist
                              ].maskedUnreadCount = parseInt(
                                inboxmessage.subList[subChatroomExist].unreadCount
                              );                         
                              if (
                                inboxmessage.fromUserId !=
                                data.args.senderUserDetails[0].userid
                              ) {
                                inboxmessage.subList[
                                  subChatroomExist
                                ].chatWithAvatar =
                                  inboxmessage.subList[
                                    subChatroomExist
                                  ].fromAvatar;
                                inboxmessage.subList[subChatroomExist].fname =
                                  inboxmessage.subList[subChatroomExist].f_fname;
                                inboxmessage.subList[subChatroomExist].lname =
                                  inboxmessage.subList[subChatroomExist].f_lname;
                                inboxmessage.subList[subChatroomExist].chatWith =
                                  inboxmessage.subList[subChatroomExist].fromName;
                                inboxmessage.subList[subChatroomExist].cgrp =
                                  inboxmessage.subList[subChatroomExist].grp;
                                inboxmessage.subList[
                                  subChatroomExist
                                ].chatWithUserId =
                                  inboxmessage.subList[subChatroomExist].userid;
                                inboxmessage.subList[
                                  subChatroomExist
                                ].chatWithDob =
                                  inboxmessage.subList[subChatroomExist].dob;
                                inboxmessage.subList[
                                  subChatroomExist
                                ].chatWithRoleId =
                                  inboxmessage.subList[subChatroomExist].grp;
                                inboxmessage.subList[
                                  subChatroomExist
                                ].chatWithRole =
                                  inboxmessage.subList[subChatroomExist].role;
  
                                inboxmessage.subList[subChatroomExist].userid =
                                  data.args.senderUserDetails[0].userid;
                                inboxmessage.subList[
                                  subChatroomExist
                                ].fromUserId =
                                  data.args.senderUserDetails[0].userid;
                                inboxmessage.subList[subChatroomExist].fromName =
                                  data.args.senderUserDetails[0].displayname;
                                inboxmessage.subList[subChatroomExist].f_fname =
                                  data.args.senderUserDetails[0].firstname;
                                inboxmessage.subList[subChatroomExist].f_lname =
                                  data.args.senderUserDetails[0].lastname;
                                inboxmessage.subList[subChatroomExist].grp =
                                  data.args.senderUserDetails[0].grp;
                                inboxmessage.subList[subChatroomExist].dob =
                                  data.args.senderUserDetails[0].dob;
                                if (data.args.senderUserDetails[0].avatar) {
                                  inboxmessage.subList[
                                    subChatroomExist
                                  ].fromAvatar =
                                    this.structureService.apiBaseUrl +
                                    'citus-health/avatars/thumbs/' +
                                    data.args.senderUserDetails[0].avatar;
                                } else {
                                  inboxmessage.subList[
                                    subChatroomExist
                                  ].fromAvatar =
                                    this.structureService.apiBaseUrl +
                                    'citus-health/avatars/profile-pic-patient.png';
                                }
                                inboxmessage.subList[subChatroomExist].role =
                                  data.args.senderUserDetails[0].roleName;
                                if (
                                  data.args.senderUserDetails[0].roleName ==
                                  'Alternate Contact'
                                ) {  
                                  inboxmessage.subList[
                                    subChatroomExist
                                  ].relation =
                                    data.args.senderUserDetails[0].relation;
                                }
                              }
                              countUpdated = true;
                              if (!updateInboxCountsCalled) {
                                updateInboxCountsCalled = true;
                                this.updateInboxCounts();
                              }
                            }
                            if (data.args.selfUpdate) {
                              inboxmessage.maskedUnreadCount =
                                parseInt(inboxmessage.maskedUnreadCount) -
                                (parseInt(
                                  inboxmessage.subList[subChatroomExist]
                                    .maskedUnreadCount
                                ) ||
                                  parseInt(
                                    inboxmessage.subList[subChatroomExist]
                                      .unreadCount
                                  ));
                              if (parseInt(inboxmessage.maskedUnreadCount) < 0) {
                                inboxmessage.maskedUnreadCount = 0;
                              }
                              inboxmessage.subList[
                                subChatroomExist
                              ].unreadCount = 0;
                              inboxmessage.subList[
                                subChatroomExist
                              ].maskedUnreadCount = 0;
                              inboxmessage.subList[subChatroomExist].unread = 0;
  
                              inboxmessage.subList[
                                subChatroomExist
                              ].chatWithAvatar =
                                inboxmessage.subList[subChatroomExist].fromAvatar;
                              inboxmessage.subList[subChatroomExist].fname =
                                inboxmessage.subList[subChatroomExist].f_fname;
                              inboxmessage.subList[subChatroomExist].lname =
                                inboxmessage.subList[subChatroomExist].f_lname;
                              inboxmessage.subList[subChatroomExist].chatWith =
                                inboxmessage.subList[subChatroomExist].fromName;
                              inboxmessage.subList[subChatroomExist].cgrp =
                                inboxmessage.subList[subChatroomExist].grp;
                              inboxmessage.subList[
                                subChatroomExist
                              ].chatWithUserId =
                                inboxmessage.subList[subChatroomExist].userid;
                              inboxmessage.subList[subChatroomExist].chatWithDob =
                                inboxmessage.subList[subChatroomExist].dob;
                              inboxmessage.subList[
                                subChatroomExist
                              ].chatWithRoleId =
                                inboxmessage.subList[subChatroomExist].grp;
                              inboxmessage.subList[
                                subChatroomExist
                              ].chatWithRole =
                                inboxmessage.subList[subChatroomExist].role;
  
                              inboxmessage.subList[subChatroomExist].userid =
                                this.userData.userId;
                              inboxmessage.subList[subChatroomExist].fromUserId =
                                this.userData.userId;
                              inboxmessage.subList[subChatroomExist].fromAvatar =
                                this.userData.profileImageThumbUrl;
                              inboxmessage.subList[subChatroomExist].f_fname =
                                this.userData.firstName;
                              inboxmessage.subList[subChatroomExist].f_lname =
                                this.userData.secondName;
                              inboxmessage.subList[subChatroomExist].fromName =
                                this.userData.displayName;
                              inboxmessage.subList[subChatroomExist].grp =
                                this.userData.group;
                              inboxmessage.subList[subChatroomExist].dob =
                                this.userData.dob;
                              inboxmessage.subList[subChatroomExist].role =
                                this.userData.roleName;
                            }
                            inboxmessage.subList[subChatroomExist].message =
                              data.args.message;
                            inboxmessage.subList[subChatroomExist].unReadPriority = this.getUnreadPriority(inboxmessage.subList[subChatroomExist].unReadPriority, data.args.priorityId);
                            inboxmessage.subList[subChatroomExist].unReadMention = this.getUnreadMention(data.args.mentionedUsers);
                            inboxmessage.subList[subChatroomExist].messagesCount =
                              parseInt(
                                inboxmessage.subList[subChatroomExist]
                                  .messagesCount
                              ) + 1;
                            inboxmessage.subList[subChatroomExist].sent =
                              data.args.sentTime;
                            inboxmessage.subList[subChatroomExist].maskedSent =
                              data.args.sentTime;
                            inboxmessage.maskedSent = data.args.sentTime;
                          }
                        } else {
                          countUpdated = true;
                          this.updateInboxCounts();
                        }
                        if (data.args.senderUserDetails) {
                          inboxmessage.messageUnreadCount = +inboxmessage.messageUnreadCount + 1;
                          inboxmessage.hasUnreadMessages = true;
                          inboxmessage.maskedUnreadCount = parseInt(inboxmessage.maskedUnreadCount) + 1;
                        }
                        if (data.args.selfUpdate) {
                          inboxmessage.isSelfMessage = true;
                          if (parseInt(inboxmessage.maskedUnreadCount) < 0) {
                            inboxmessage.maskedUnreadCount = 0;
                          }
                        } else {
                          inboxmessage.isSelfMessage = false;
                        }
                        inboxmessage.maskedSent = data.args.sentTime;
                        inboxmessage.messageOrder = data.args.sentTime;
                      } else if (+inboxmessage.chatroomId === +data.args.chatroomId) {
                        callAPIToUpdate = false;
                        if (data.args.senderUserDetails) {
                          if (!parseInt(inboxmessage.unread)) {
                            inboxmessage.unread = 1;
                          }
                          if(!data.args.notUpdateCount) {
                            inboxmessage.unreadCount = parseInt(inboxmessage.unreadCount) + 1;
                          }
                          inboxmessage.messageUnreadCount = +inboxmessage.messageUnreadCount + 1;
                          inboxmessage.hasUnreadMessages = true;
                          inboxmessage.maskedUnreadCount = parseInt(inboxmessage.unreadCount);
                          if (inboxmessage.fromUserId != data.args.senderUserDetails[0].userid) {
                            inboxmessage.chatWithAvatar = inboxmessage.fromAvatar;
                            inboxmessage.fname = inboxmessage.f_fname;
                            inboxmessage.lname = inboxmessage.f_lname;
                            inboxmessage.chatWith = inboxmessage.fromName;
                            inboxmessage.cgrp = inboxmessage.grp;
                            inboxmessage.chatWithUserId = inboxmessage.userid;
                            inboxmessage.chatWithDob = inboxmessage.dob;
                            inboxmessage.chatWithRoleId = inboxmessage.grp;
                            inboxmessage.chatWithRole = inboxmessage.role;
                            inboxmessage.userid = data.args.senderUserDetails[0].userid;
                            inboxmessage.fromUserId = data.args.senderUserDetails[0].userid;
                            inboxmessage.fromName = data.args.senderUserDetails[0].displayname;
                            inboxmessage.f_fname = data.args.senderUserDetails[0].firstname;
                            inboxmessage.f_lname = data.args.senderUserDetails[0].lastname;
                            inboxmessage.grp = data.args.senderUserDetails[0].grp;
                            inboxmessage.dob = data.args.senderUserDetails[0].dob;
                            if (data.args.senderUserDetails[0].avatar) {
                              inboxmessage.fromAvatar =
                                this.structureService.apiBaseUrl + Number('citus-health/avatars/thumbs/') + data.args.senderUserDetails[0].avatar;
                            } else {
                              inboxmessage.fromAvatar = this.structureService.apiBaseUrl + Number('citus-health/avatars/profile-pic-patient.png');
                            }
                            inboxmessage.role = data.args.senderUserDetails[0].roleName;
                            if (data.args.senderUserDetails[0].roleName == 'Alternate Contact') {
                              inboxmessage.relation = data.args.senderUserDetails[0].relation;
                            }
                          }
                          countUpdated = true;
                          if (!updateInboxCountsCalled) {
                            updateInboxCountsCalled = true;
                            this.updateInboxCounts();
                          }
                        }

                        if (data.args.selfUpdate) {
                          inboxmessage.maskedUnreadCount = 0;
                          inboxmessage.unreadCount = 0;
                          inboxmessage.hasUnreadMessages = false;
                          inboxmessage.unread = 0;
                          inboxmessage.chatWithAvatar = inboxmessage.fromAvatar;
                          inboxmessage.fname = inboxmessage.f_fname;
                          inboxmessage.lname = inboxmessage.f_lname;
                          inboxmessage.chatWith = inboxmessage.fromName;
                          inboxmessage.cgrp = inboxmessage.grp;
                          inboxmessage.chatWithUserId = inboxmessage.userid;
                          inboxmessage.chatWithDob = inboxmessage.dob;
                          inboxmessage.chatWithRoleId = inboxmessage.grp;
                          inboxmessage.chatWithRole = inboxmessage.role;
                          inboxmessage.userid = this.userData.userId;
                          inboxmessage.fromUserId = this.userData.userId;
                          inboxmessage.fromAvatar = this.userData.profileImageThumbUrl;
                          inboxmessage.f_fname = this.userData.firstName;
                          inboxmessage.f_lname = this.userData.secondName;
                          inboxmessage.fromName = this.userData.displayName;
                          inboxmessage.grp = this.userData.group;
                          inboxmessage.dob = this.userData.dob;
                          inboxmessage.role = this.userData.roleName;
                          inboxmessage.isSelfMessage = true;
                        }  else {
                            inboxmessage.isSelfMessage = false;
                          }
                          inboxmessage.messagesCount =
                            parseInt(inboxmessage.messagesCount) + 1;
                          inboxmessage.message = data.args.message;
                          inboxmessage.messagePriorityUnread = this.getUnreadPriority(inboxmessage.unReadPriority, data.args.priorityId);
                          inboxmessage.messageMentionUnread = this.getUnreadMention(data.args.mentionedUsers);
                          inboxmessage.sent = data.args.sentTime;
                          inboxmessage.maskedSent = data.args.sentTime;
                          inboxmessage.deliveryTime = data.args.sentTime;
                          inboxmessage.messageOrder = data.args.sentTime;
                          if(isBlank(inboxmessage.messageOrder) && !isBlank(data.args.messageOrder)) {
                            inboxmessage.messageOrder = data.args.messageOrder;
                          }
                          inboxmessage.messageStatus = data.args.messageStatus || '1';
                          inboxmessage.messageDeletedTime = (+data.args.messageStatus === 0 && !isBlank(data.args.messageDeletedTime))? data.args.messageDeletedTime: '';
                      } else if (+inboxmessage.chatroomId === +data.args.baseId && data.args.meessageFromMaskedChild) {
                        isMaskedMainThreadUpdateNeeded = true;
                        inboxmessage.deliveryTime = data.args.sentTime;
                        inboxmessage.hasUnreadMessages = true;
                        inboxmessage.maskedUnreadCount = +inboxmessage.maskedUnreadCount ? +inboxmessage.maskedUnreadCount + 1 : 1;
                        countUpdated = true;
                        this.updateInboxCounts();
                      }
                    } else {
                      if (+inboxmessage.chatroomId === +data.args.chatroomId) {
                        callAPIToUpdate = false;
                        if (data.args.senderUserDetails) {
                          if (!parseInt(inboxmessage.unread)) {
                            inboxmessage.unread = 1;
                          }
                          inboxmessage.unreadCount =
                            parseInt(inboxmessage.unreadCount) + 1;
                          inboxmessage.messageUnreadCount = +inboxmessage.messageUnreadCount + 1;
                          inboxmessage.hasUnreadMessages = true;  
                          inboxmessage.maskedUnreadCount = parseInt(
                            inboxmessage.unreadCount
                          );
                          if (
                            inboxmessage.fromUserId !=
                            data.args.senderUserDetails[0].userid
                          ) {
                            inboxmessage.chatWithAvatar =
                              inboxmessage.fromAvatar;
                            inboxmessage.fname = inboxmessage.f_fname;
                            inboxmessage.lname = inboxmessage.f_lname;
                            inboxmessage.chatWith = inboxmessage.fromName;
                            inboxmessage.cgrp = inboxmessage.grp;
                            inboxmessage.chatWithUserId = inboxmessage.userid;
                            inboxmessage.chatWithDob = inboxmessage.dob;
                            inboxmessage.chatWithRoleId = inboxmessage.grp;
                            inboxmessage.chatWithRole = inboxmessage.role;

                            inboxmessage.userid =
                              data.args.senderUserDetails[0].userid;
                            inboxmessage.fromUserId =
                              data.args.senderUserDetails[0].userid;
                            inboxmessage.fromName =
                              data.args.senderUserDetails[0].displayname;
                            inboxmessage.f_fname =
                              data.args.senderUserDetails[0].firstname;
                            inboxmessage.f_lname =
                              data.args.senderUserDetails[0].lastname;
                            inboxmessage.grp =
                              data.args.senderUserDetails[0].grp;
                            inboxmessage.dob =
                              data.args.senderUserDetails[0].dob;
                            if (data.args.senderUserDetails[0].avatar) {
                              inboxmessage.fromAvatar =
                                this.structureService.apiBaseUrl +
                                'citus-health/avatars/thumbs/' +
                                data.args.senderUserDetails[0].avatar;
                            } else {
                              inboxmessage.fromAvatar =
                                this.structureService.apiBaseUrl +
                                'citus-health/avatars/profile-pic-patient.png';
                            }
                            inboxmessage.role =
                              data.args.senderUserDetails[0].roleName;
                            if (
                              data.args.senderUserDetails[0].roleName ==
                              'Alternate Contact'
                            ) {

                              //inboxmessage.patient_chatWith_relation = data.args.senderUserDetails[0].relation;
                              inboxmessage.relation =
                                data.args.senderUserDetails[0].relation;
                            }
                          }
                          countUpdated = true;
                          if (!updateInboxCountsCalled) {
                            updateInboxCountsCalled = true;
                            this.updateInboxCounts();
                          }
                        } else if (data.args.topic) {
                          inboxmessage.title = data.args.topic;
                          countUpdated = true;
                        }
                        if (data.args.selfUpdate) {
                          inboxmessage.maskedUnreadCount = 0;
                          inboxmessage.unreadCount = 0;
                          inboxmessage.hasUnreadMessages = false;
                          inboxmessage.unread = 0;
                          inboxmessage.chatWithAvatar = inboxmessage.fromAvatar;
                          inboxmessage.fname = inboxmessage.f_fname;
                          inboxmessage.lname = inboxmessage.f_lname;
                          inboxmessage.chatWith = inboxmessage.fromName;
                          inboxmessage.cgrp = inboxmessage.grp;
                          inboxmessage.chatWithUserId = inboxmessage.userid;
                          inboxmessage.chatWithDob = inboxmessage.dob;
                          inboxmessage.chatWithRoleId = inboxmessage.grp;
                          inboxmessage.chatWithRole = inboxmessage.role;

                          inboxmessage.userid = this.userData.userId;
                          inboxmessage.fromUserId = this.userData.userId;
                          inboxmessage.fromAvatar =
                            this.userData.profileImageThumbUrl;
                          inboxmessage.f_fname = this.userData.firstName;
                          inboxmessage.f_lname = this.userData.secondName;
                          inboxmessage.fromName = this.userData.displayName;
                          inboxmessage.grp = this.userData.group;
                          inboxmessage.dob = this.userData.dob;
                          inboxmessage.role = this.userData.roleName;
                          inboxmessage.isSelfMessage = true;
                        }  else {
                          inboxmessage.isSelfMessage = false;
                        }
                        inboxmessage.messagesCount =
                          parseInt(inboxmessage.messagesCount) + 1;
                        inboxmessage.message = data.args.message;
                        inboxmessage.messagePriorityUnread = this.getUnreadPriority(inboxmessage.unReadPriority, data.args.priorityId);
                        inboxmessage.messageMentionUnread = this.getUnreadMention(data.args.mentionedUsers);
                        inboxmessage.deliveryTime = data.args.sentTime;
                        inboxmessage.messageOrder = data.args.sentTime;
                        inboxmessage.messageDeletedTime = data.args.messageDeletedTime || '';
                        inboxmessage.messageStatus = !isBlank(data.args.messageStatus) ? data.args.messageStatus : '1';
                      }
                    }
                  } else {
                    if (+inboxmessage.chatroomId === +data.args.chatroomId) {
                      callAPIToUpdate = false;
                      inboxmessage.messageStatus = data.args.messageStatus;
                      if (data.args.senderUserDetails) {
                        if (!parseInt(inboxmessage.unread)) {
                          inboxmessage.unread = 1;
                        }
                        if (data.args.topic) {
                          inboxmessage.title = data.args.topic;
                        }
                        if(!data.args.notUpdateCount){
                          inboxmessage.unreadCount =
                          parseInt(inboxmessage.unreadCount) + 1;
                        inboxmessage.messageUnreadCount = +inboxmessage.messageUnreadCount + 1;
                        inboxmessage.hasUnreadMessages = true;
                        inboxmessage.maskedUnreadCount = parseInt(
                          inboxmessage.unreadCount
                        );
                        }                        
                        if (
                          inboxmessage.fromUserId !=
                          data.args.senderUserDetails[0].userid
                        ) {
                          inboxmessage.chatWithAvatar = inboxmessage.fromAvatar;
                          inboxmessage.fname = inboxmessage.f_fname;
                          inboxmessage.lname = inboxmessage.f_lname;
                          inboxmessage.chatWith = inboxmessage.fromName;
                          inboxmessage.cgrp = inboxmessage.grp;
                          inboxmessage.chatWithUserId = inboxmessage.userid;
                          inboxmessage.chatWithDob = inboxmessage.dob;
                          inboxmessage.chatWithRoleId = inboxmessage.grp;
                          inboxmessage.chatWithRole = inboxmessage.role;

                          inboxmessage.userid =
                            data.args.senderUserDetails[0].userid;
                          inboxmessage.fromUserId =
                            data.args.senderUserDetails[0].userid;
                          inboxmessage.fromName =
                            data.args.senderUserDetails[0].displayname;
                          inboxmessage.f_fname =
                            data.args.senderUserDetails[0].firstname;
                          inboxmessage.f_lname =
                            data.args.senderUserDetails[0].lastname;
                          inboxmessage.grp = data.args.senderUserDetails[0].grp;
                          inboxmessage.dob = data.args.senderUserDetails[0].dob;
                          if (data.args.senderUserDetails[0].avatar) {
                            inboxmessage.fromAvatar =
                              this.structureService.apiBaseUrl +
                              'citus-health/avatars/thumbs/' +
                              data.args.senderUserDetails[0].avatar;
                          } else {
                            inboxmessage.fromAvatar =
                              this.structureService.apiBaseUrl +
                              'citus-health/avatars/profile-pic-patient.png';
                          }
                          inboxmessage.role =
                            data.args.senderUserDetails[0].roleName;
                          if (
                            data.args.senderUserDetails[0].roleName ==
                            'Alternate Contact'
                          ) {

                            //inboxmessage.patient_chatWith_relation = data.args.senderUserDetails[0].relation;
                            inboxmessage.relation =
                              data.args.senderUserDetails[0].relation;
                          }
                        }

                        countUpdated = true;
                        // No need to update the unread count if the user in chat session with patient in schedule center visit
                        const visitChatRoomId = !isBlank(this.sharedService.visitChatDetails) ? this.sharedService.visitChatDetails.data.chatroomId : '';
                        if(visitChatRoomId !== data.args.chatroomId && !updateInboxCountsCalled && !data.args.notUpdateCount) {
                          updateInboxCountsCalled = true;
                          this.updateInboxCounts();
                        }
                      } else if (data.args.topic) {
                        inboxmessage.title = data.args.topic;
                        countUpdated = true;
                      }
                      if (data.args.selfUpdate) {
                        inboxmessage.unreadCount = 0;
                        inboxmessage.hasUnreadMessages = false;
                        inboxmessage.messageUnreadCount = 0;
                        inboxmessage.maskedUnreadCount = 0;
                        if (inboxmessage.fromUserId != this.userData.userId) {
                          inboxmessage.chatWithAvatar = inboxmessage.fromAvatar;
                          inboxmessage.fname = inboxmessage.f_fname;
                          inboxmessage.lname = inboxmessage.f_lname;
                          inboxmessage.chatWith = inboxmessage.fromName;
                          inboxmessage.cgrp = inboxmessage.grp;
                          inboxmessage.chatWithUserId = inboxmessage.userid;
                          inboxmessage.chatWithDob = inboxmessage.dob;
                          inboxmessage.chatWithRoleId = inboxmessage.grp;
                          inboxmessage.chatWithRole = inboxmessage.role;

                          inboxmessage.userid = this.userData.userId;
                          inboxmessage.fromUserId = this.userData.userId;
                          inboxmessage.fromAvatar =
                            this.userData.profileImageThumbUrl;
                          inboxmessage.f_fname = this.userData.firstName;
                          inboxmessage.f_lname = this.userData.secondName;
                          inboxmessage.fromName = this.userData.displayName;
                          inboxmessage.grp = this.userData.group;
                          inboxmessage.dob = this.userData.dob;
                          inboxmessage.role = this.userData.roleName;
                        }
                        inboxmessage.isSelfMessage = true;
                      }  else {
                        inboxmessage.isSelfMessage = false;
                      }
                      inboxmessage.messagesCount =
                        parseInt(inboxmessage.messagesCount) + 1;
                      inboxmessage.message = data.args.message;
                      inboxmessage.messagePriorityUnread = this.getUnreadPriority(inboxmessage.unReadPriority, data.args.priorityId);
                      inboxmessage.messageMentionUnread = this.getUnreadMention(data.args.mentionedUsers);
                      inboxmessage.deliverytime = data.args.sentTime;
                      inboxmessage.messageOrder = data.args.sentTime;
                      if(isBlank(inboxmessage.messageOrder) && !isBlank(data.args.messageOrder)) {
                        inboxmessage.messageOrder = data.args.messageOrder;
                      }
                      inboxmessage.messageStatus = data.args.messageStatus || '1';
                      inboxmessage.messageDeletedTime = (+data.args.messageStatus === 0 && !isBlank(data.args.messageDeletedTime))? data.args.messageDeletedTime: '';
                    }
                  }
                }
              });
            } else {
              let countUpdated = false;
              currentInboxData.map((inboxmessage) => {
                if (!countUpdated) {
                  if (inboxmessage.messageType == '2') {
                    if (+inboxmessage.chatroomId === +data.args.chatroomId) {
                      callAPIToUpdate = false;
                      if (data.args.senderUserDetails) {
                        if (
                          inboxmessage.fromUserId !=
                          data.args.senderUserDetails[0].userid
                        ) {
                          inboxmessage.chatWithAvatar = inboxmessage.fromAvatar;
                          inboxmessage.fname = inboxmessage.f_fname;
                          inboxmessage.lname = inboxmessage.f_lname;
                          inboxmessage.chatWith = inboxmessage.fromName;
                          inboxmessage.cgrp = inboxmessage.grp;
                          inboxmessage.chatWithUserId = inboxmessage.userid;
                          inboxmessage.chatWithDob = inboxmessage.dob;
                          inboxmessage.chatWithRoleId = inboxmessage.grp;
                          inboxmessage.chatWithRole = inboxmessage.role;

                          inboxmessage.userid =
                            data.args.senderUserDetails[0].userid;
                          inboxmessage.fromUserId =
                            data.args.senderUserDetails[0].userid;
                          inboxmessage.fromName =
                            data.args.senderUserDetails[0].displayname;
                          inboxmessage.f_fname =
                            data.args.senderUserDetails[0].firstname;
                          inboxmessage.f_lname =
                            data.args.senderUserDetails[0].lastname;
                          inboxmessage.grp = data.args.senderUserDetails[0].grp;
                          inboxmessage.dob = data.args.senderUserDetails[0].dob;
                          if (data.args.senderUserDetails[0].avatar) {
                            inboxmessage.fromAvatar =
                              this.structureService.apiBaseUrl +
                              'citus-health/avatars/thumbs/' +
                              data.args.senderUserDetails[0].avatar;
                          } else {
                            inboxmessage.fromAvatar =
                              this.structureService.apiBaseUrl +
                              'citus-health/avatars/profile-pic-patient.png';
                          }
                          inboxmessage.role =
                            data.args.senderUserDetails[0].roleName;
                          if (
                            data.args.senderUserDetails[0].roleName ==
                            'Alternate Contact'
                          ) {

                            //inboxmessage.patient_chatWith_relation = data.args.senderUserDetails[0].relation;
                            inboxmessage.relation =
                              data.args.senderUserDetails[0].relation;
                          }
                        }
                        this.sharedService.updateActiveMessageInChatroom.emit(
                          inboxmessage
                        );
                        countUpdated = true;
                      }
                      if (data.args.selfUpdate) {
                        inboxmessage.chatWithAvatar = inboxmessage.fromAvatar;
                        inboxmessage.fname = inboxmessage.f_fname;
                        inboxmessage.lname = inboxmessage.f_lname;
                        inboxmessage.chatWith = inboxmessage.fromName;
                        inboxmessage.cgrp = inboxmessage.grp;
                        inboxmessage.chatWithUserId = inboxmessage.userid;
                        inboxmessage.chatWithDob = inboxmessage.dob;
                        inboxmessage.chatWithRoleId = inboxmessage.grp;
                        inboxmessage.chatWithRole = inboxmessage.role;

                        inboxmessage.userid = this.userData.userId;
                        inboxmessage.fromUserId = this.userData.userId;
                        inboxmessage.fromAvatar =
                          this.userData.profileImageThumbUrl;
                        inboxmessage.f_fname = this.userData.firstName;
                        inboxmessage.f_lname = this.userData.secondName;
                        inboxmessage.fromName = this.userData.displayName;
                        inboxmessage.grp = this.userData.group;
                        inboxmessage.dob = this.userData.dob;
                        inboxmessage.role = this.userData.roleName;
                        inboxmessage.isSelfMessage = true;
                      }  else {
                        inboxmessage.isSelfMessage = false;
                      }
                      inboxmessage.message = data.args.message;
                      inboxmessage.messagePriorityUnread = this.getUnreadPriority(inboxmessage.unReadPriority, data.args.priorityId);
                      inboxmessage.messageMentionUnread = this.getUnreadMention(data.args.mentionedUsers);
                      inboxmessage.sent = data.args.sentTime;
                      inboxmessage.maskedSent = data.args.sentTime;
                      inboxmessage.messagesCount =
                        parseInt(inboxmessage.messagesCount) + 1;
                      inboxmessage.deliveryTime = data.args.sentTime;
                      inboxmessage.messageOrder = data.args.sentTime;
                    }
                  } else {
                    if (+inboxmessage.chatroomId === +data.args.chatroomId) {
                      callAPIToUpdate = false;
                      if (data.args.senderUserDetails) {
                        if (data.args.topic) {
                          inboxmessage.title = data.args.topic;
                        }
                        if (
                          inboxmessage.fromUserId !=
                          data.args.senderUserDetails[0].userid
                        ) {
                          inboxmessage.chatWithAvatar = inboxmessage.fromAvatar;
                          inboxmessage.fname = inboxmessage.f_fname;
                          inboxmessage.lname = inboxmessage.f_lname;
                          inboxmessage.chatWith = inboxmessage.fromName;
                          inboxmessage.cgrp = inboxmessage.grp;
                          inboxmessage.chatWithUserId = inboxmessage.userid;
                          inboxmessage.chatWithDob = inboxmessage.dob;
                          inboxmessage.chatWithRoleId = inboxmessage.grp;
                          inboxmessage.chatWithRole = inboxmessage.role;

                          inboxmessage.userid =
                            data.args.senderUserDetails[0].userid;
                          inboxmessage.fromUserId =
                            data.args.senderUserDetails[0].userid;
                          inboxmessage.fromName =
                            data.args.senderUserDetails[0].displayname;
                          inboxmessage.f_fname =
                            data.args.senderUserDetails[0].firstname;
                          inboxmessage.f_lname =
                            data.args.senderUserDetails[0].lastname;
                          inboxmessage.grp = data.args.senderUserDetails[0].grp;
                          inboxmessage.dob = data.args.senderUserDetails[0].dob;
                          if (data.args.senderUserDetails[0].avatar) {
                            inboxmessage.fromAvatar =
                              this.structureService.apiBaseUrl +
                              'citus-health/avatars/thumbs/' +
                              data.args.senderUserDetails[0].avatar;
                          } else {
                            inboxmessage.fromAvatar =
                              this.structureService.apiBaseUrl +
                              'citus-health/avatars/profile-pic-patient.png';
                          }
                          inboxmessage.role =
                            data.args.senderUserDetails[0].roleName;
                          if (
                            data.args.senderUserDetails[0].roleName ==
                            'Alternate Contact'
                          ) {

                            //inboxmessage.patient_chatWith_relation = data.args.senderUserDetails[0].relation;
                            inboxmessage.relation =
                              data.args.senderUserDetails[0].relation;
                          }
                        }
                        this.sharedService.updateActiveMessageInChatroom.emit(
                          inboxmessage
                        );
                        countUpdated = true;
                      } else if (data.args.topic) {
                        countUpdated = true;
                        inboxmessage.title = data.args.topic;
                        if (
                          this.router.url == '/inbox/chatroom' &&
                          localStorage.getItem('targetId') ==
                            data.args.chatroomId
                        ) {
                          this.sharedService.updateActiveMessageInChatroom.emit(
                            { title: data.args.topic, updateTopic: true, }
                          );
                        }
                      }
                      if (data.args.selfUpdate) {
                        inboxmessage.chatWithAvatar = inboxmessage.fromAvatar;
                        inboxmessage.fname = inboxmessage.f_fname;
                        inboxmessage.lname = inboxmessage.f_lname;
                        inboxmessage.chatWith = inboxmessage.fromName;
                        inboxmessage.cgrp = inboxmessage.grp;
                        inboxmessage.chatWithUserId = inboxmessage.userid;
                        inboxmessage.chatWithDob = inboxmessage.dob;
                        inboxmessage.chatWithRoleId = inboxmessage.grp;
                        inboxmessage.chatWithRole = inboxmessage.role;

                        inboxmessage.userid = this.userData.userId;
                        inboxmessage.fromUserId = this.userData.userId;
                        inboxmessage.fromAvatar =
                          this.userData.profileImageThumbUrl;
                        inboxmessage.f_fname = this.userData.firstName;
                        inboxmessage.f_lname = this.userData.secondName;
                        inboxmessage.fromName = this.userData.displayName;
                        inboxmessage.grp = this.userData.group;
                        inboxmessage.dob = this.userData.dob;
                        inboxmessage.role = this.userData.roleName;
                        inboxmessage.isSelfMessage = true;
                      }  else {
                        inboxmessage.isSelfMessage = false;
                      }
                      inboxmessage.message = data.args.message;
                      inboxmessage.messagePriorityUnread = this.getUnreadPriority(inboxmessage.unReadPriority, data.args.priorityId);
                      inboxmessage.messageMentionUnread = this.getUnreadMention(data.args.mentionedUsers);
                      inboxmessage.sent = data.args.sentTime;
                      inboxmessage.maskedSent = data.args.sentTime;
                      inboxmessage.messagesCount =
                        parseInt(inboxmessage.messagesCount) + 1;
                      inboxmessage.deliveryTime = data.args.sentTime;
                      inboxmessage.messageOrder = data.args.sentTime;
                      if(isBlank(inboxmessage.messageOrder) && !isBlank(data.args.messageOrder)){
                        inboxmessage.messageOrder = data.args.messageOrder;
                      }                      
                      inboxmessage.messageStatus = !isBlank(data.args.messageStatus) ? data.args.messageStatus : '1';
                      if( +inboxmessage.messageStatus === 0 ){
                        inboxmessage.messageDeletedTime = data.args.messageDeletedTime || '';
                      } 
                    }
                  }
                }
              });
            }
            if (callAPIToUpdate || isMaskedMainThreadUpdateNeeded) {
              if((isMaskedMainThreadUpdateNeeded && data.args.baseId)
              || (callAPIToUpdate && !isMaskedMainThreadUpdateNeeded && data.args.meessageFromMaskedChild)) {
                data.args.chatroomId = data.args.baseId;
              } 
              this.activateForNewChatroomPolling(data);
            } else {
              currentInboxData = currentInboxData.filter(function (
                filteredData
              ) {
                return filteredData.category == 'chat-messages'
                  ? parseInt(filteredData.baseChatroomId) == 0 ||
                      !filteredData.movedToSublist
                  : true;
              });
              this.structureService.inboxDataFirstPage = JSON.parse(
                JSON.stringify(currentInboxData)
              );
              if (
                this.router.url == '/inbox' ||
                this.router.url == '/inbox/chatroom'
              ) {
                let pageCount = parseInt(
                  localStorage.getItem('pageCountMessage')
                );
                if (pageCount <= 1) {
                  this.structureService.inboxData = JSON.parse(
                    JSON.stringify(this.structureService.inboxDataFirstPage)
                  );
                  this.sharedService.onInboxData.emit({
                    inboxData: this.structureService.inboxData,
                    emitUpdateInboxData: true,
                    inboxDataFirstPage:
                      this.structureService.inboxDataFirstPage,
                  });
                }
              }
            }
          } else if (
            data &&
            data.args &&
            data.args.chatroomId &&
            !data.args.removeChatroomFromListOnPolling
          ) {
            this.activateForNewChatroomPolling(data);
          } else if (data.args.removeChatroomFromListOnPolling) {
            let callAPIToUpdate = true;
            let countUpdated = false;
            let currentInboxData = JSON.parse(
              JSON.stringify(this.structureService.inboxDataFirstPage)
            );
            currentInboxData.map((inboxmessage, index) => {
              if (!countUpdated) {
                if (
                  +inboxmessage.messageType === 2||
                  (+inboxmessage.baseChatroomId !== 0)
                ) {
                  if (+inboxmessage.messageType === 2) {
                    if (+inboxmessage.chatroomId === +data.args.chatroomId) {
                      const unreadCount =
                        parseInt(inboxmessage.maskedUnreadCount) ||
                        parseInt(inboxmessage.unreadCount);
                      callAPIToUpdate = false;
                      currentInboxData.splice(index, 1);
                      countUpdated = true;
                      if (unreadCount > 0 && !updateInboxCountsCalled) {
                        updateInboxCountsCalled = true;
                        this.updateInboxCounts({ operationType: MessageOperationType.SUB, count: unreadCount });
                      }
                    }
                  } else {
                    if (+inboxmessage.chatroomId === +data.args.chatroomId) {
                      const unreadCount =
                        parseInt(inboxmessage.maskedUnreadCount) ||
                        parseInt(inboxmessage.unreadCount);
                      callAPIToUpdate = false;
                      currentInboxData.splice(index, 1);
                      countUpdated = true;
                      if (unreadCount > 0 && !updateInboxCountsCalled) {
                        updateInboxCountsCalled = true;
                        this.updateInboxCounts({ operationType: MessageOperationType.SUB, count: unreadCount });
                      }
                    }
                  }
                } else {
                  if (+inboxmessage.chatroomId === +data.args.chatroomId) {
                    let unreadCount =
                      parseInt(inboxmessage.maskedUnreadCount) ||
                      parseInt(inboxmessage.unreadCount);
                    callAPIToUpdate = false;
                    currentInboxData.splice(index, 1);
                    countUpdated = true;
                    if (unreadCount > 0 && !updateInboxCountsCalled) {
                      updateInboxCountsCalled = true;
                      this.updateInboxCounts({ operationType: MessageOperationType.SUB, count: unreadCount });
                    }
                  }
                }
              }
            });
            this.structureService.inboxDataFirstPage = JSON.parse(
              JSON.stringify(currentInboxData)
            );
            if (callAPIToUpdate) {
              this.activate(true, 0, 1, data, true);
            } else {
              if (
                this.structureService.inboxData &&
                this.structureService.inboxData.length
              ) {
                let callAPIToUpdate = true;
                let currentInboxData = this.structureService.inboxData;
                currentInboxData.map((inboxmessage, index) => {
                  if (!countUpdated) {
                    if (
                      inboxmessage.messageType == '2' ||
                      (inboxmessage.baseChatroomId !== 0)
                    ) {
                      if (inboxmessage.messageType == '2') {
                        if (+inboxmessage.chatroomId === +data.args.chatroomId) {
                          let unreadCount =
                            parseInt(inboxmessage.maskedUnreadCount) ||
                            parseInt(inboxmessage.unreadCount);
                          callAPIToUpdate = true;
                          currentInboxData.splice(index, 1);
                          countUpdated = true;
                          if (unreadCount > 0 && !updateInboxCountsCalled) {
                            updateInboxCountsCalled = true;
                            this.updateInboxCounts({ operationType: MessageOperationType.SUB, count: unreadCount });
                          }
                        }
                      } else {
                        if (+inboxmessage.chatroomId === +data.args.chatroomId) {
                          let unreadCount =
                            parseInt(inboxmessage.maskedUnreadCount) ||
                            parseInt(inboxmessage.unreadCount);
                          callAPIToUpdate = true;
                          currentInboxData.splice(index, 1);
                          countUpdated = true;
                          if (unreadCount > 0 && !updateInboxCountsCalled) {
                            updateInboxCountsCalled = true;
                            this.updateInboxCounts({ operationType: MessageOperationType.SUB, count: unreadCount });
                          }
                        }
                      }
                    } else {
                      if (+inboxmessage.chatroomId === +data.args.chatroomId) {
                        let unreadCount =
                          parseInt(inboxmessage.maskedUnreadCount) ||
                          parseInt(inboxmessage.unreadCount);
                        callAPIToUpdate = true;
                        currentInboxData.splice(index, 1);
                        countUpdated = true;
                        if (unreadCount > 0 && !updateInboxCountsCalled) {
                          updateInboxCountsCalled = true;
                          this.updateInboxCounts({ operationType: MessageOperationType.SUB, count: unreadCount });
                        }
                      }
                    }
                  }
                });
                currentInboxData = currentInboxData.filter(function (
                  filteredData
                ) {
                  return filteredData.category == 'chat-messages'
                    ? parseInt(filteredData.baseChatroomId) === 0 ||
                        !filteredData.movedToSublist
                    : true;
                });
                this.structureService.inboxData = currentInboxData;
                if (
                  this.router.url == '/inbox' ||
                  this.router.url == '/inbox/chatroom'
                ) {
                  let pageCount = parseInt(
                    localStorage.getItem('pageCountMessage')
                  );
                  if (pageCount <= 1) {
                    let inboxDataFirstPage = JSON.parse(
                      JSON.stringify(this.structureService.inboxDataFirstPage)
                    );
                    this.structureService.inboxDataFirstPage =
                      inboxDataFirstPage.filter(function (filteredData) {
                        return filteredData.category == 'chat-messages'
                          ? +filteredData.baseChatroomId == 0 ||
                              !filteredData.movedToSublist
                          : true;
                      });
                    this.structureService.inboxData = JSON.parse(
                      JSON.stringify(this.structureService.inboxDataFirstPage)
                    );
                  }
                  this.sharedService.onInboxData.emit({
                    inboxData: this.structureService.inboxData,
                    emitUpdateInboxData: true,
                    inboxDataFirstPage:
                      this.structureService.inboxDataFirstPage,
                  });
                }
              }
            }
          } else {
            this.activate(true, 0, 1, data);
          }
          if (this.router.url == '/inbox/chatroom') {
            setTimeout(() => {
              $('.inbox-data-container').animate({ scrollTop: 0, }, 1500);
            });
          }
      }
    } else {
      let types = 'messages';
      if (this.userData.config.enable_forms === '1') {
        types = 'messages,forms';
      }
      this.getUserInboxCounts(types);
    }
  }
  
  getUnreadPriority(previuosPriorityId: number, currentPriorityId: number) {
    return Number(previuosPriorityId) !== 0 && Number(previuosPriorityId) < currentPriorityId ? Number(previuosPriorityId) : currentPriorityId;
  }

  updateInboxTags(data) {
    const searchInboxkeyword = localStorage.getItem(
      'searchInboxkeywordForPolling'
    );
    const pageCount = parseInt(
      localStorage.getItem('pageCountMessage')
    );
    if (
      (this.router.url == '/inbox' || this.router.url == '/inbox/chatroom') &&
      (data && data.chatroomId) &&
      pageCount <= 1
    ) {
        const currentInboxData = JSON.parse(
          JSON.stringify(this.structureService.inboxDataFirstPage)
        );
        const index = currentInboxData.findIndex((inboxmessage) => +inboxmessage.chatroomId === +data.chatroomId);
        if (index !== -1) {
          let threadTagList = !isBlank(currentInboxData[index].threadTagList) ? currentInboxData[index].threadTagList : [];
          let messageTagList = !isBlank(currentInboxData[index].messageTagList) ? currentInboxData[index].messageTagList : [];
          if (data.customData && (data.customData.inboxTags || data.customData.removedInboxTagId)) {
            if (data.customData.inboxTags) {
              threadTagList = [...threadTagList, ...data.customData.inboxTags];
            } else {
              threadTagList = threadTagList.filter((tag) => tag.id !== Number(data.customData.removedInboxTagId));
            }
          }
          if (data.customData && (data.customData.messageTags || data.customData.removedMessageTagId)) {
            if (data.customData.messageTags) {
              messageTagList = [...messageTagList, ...data.customData.messageTags];
            } else {
              messageTagList = messageTagList.filter((tag) => tag.id !== Number(data.customData.removedMessageTagId));
            }
          }
          currentInboxData[index].threadTagList = threadTagList;
          currentInboxData[index].messageTagList = messageTagList;
        }
        this.structureService.inboxDataFirstPage = JSON.parse(
          JSON.stringify(currentInboxData)
        );
        this.structureService.inboxData = JSON.parse(
          JSON.stringify(this.structureService.inboxDataFirstPage)
        );
        this.sharedService.onInboxData.emit({
          inboxData: this.structureService.inboxData,
          emitUpdateInboxData: true,
          inboxDataFirstPage:
            this.structureService.inboxDataFirstPage,
        });
    }
  }
  
  getUnreadMention(mentionedUsers: Number[]) {
    return mentionedUsers && mentionedUsers.includes(Number(this.userData.userId)) ? CHECKED.TRUE : CHECKED.FALSE;
  }

  activateForNewChatroomPolling(pollingData, qlimit = 0, pageCountMessage = 0) {
    this.messageService
      .getChatMessages(
        {chatroomId: +pollingData.args.chatroomId}
      )
      .subscribe((response: any) => {
        const message = response && response.data && response.data.message ? response.data.message : [];
        let data = [];
        if (message && message.chatroomId) {         
          message.messageMentionUnread = this.getUnreadMention(message['messageMentionUnread']);
          if (
            pollingData &&
            pollingData.arg &&
            (pollingData.arg.selfSendMessage || pollingData.arg.selfUpdate)
          ) {
            message.unreadCount = 0;
            message.messageUnreadCount = 0;
            if (parseInt(message.maskedUnreadCount)) {
              message.maskedUnreadCount = 0;
            }
          }
          let chatroomExistsInList =
            this.structureService.inboxDataFirstPage.findIndex((
              messageinarray
            ) => {
              return +messageinarray.chatroomId === +message.chatroomId;
            });
          data.push(message);
          if (chatroomExistsInList == -1) {
            data = [...this.structureService.inboxDataFirstPage, ...data,];
          } else {
            if (pollingData.args.maskedMessageReplyClick || pollingData.args.meessageFromMaskedChild) {
              this.structureService.inboxDataFirstPage.splice(
                chatroomExistsInList,
                1
              );
              data = [...this.structureService.inboxDataFirstPage, ...data,];
            } else {
              data = JSON.parse(
                JSON.stringify(this.structureService.inboxDataFirstPage)
              );
            }
          }
          let activeMessage = JSON.parse(localStorage.getItem('activeMessage'));
          if (
            activeMessage &&
            'chatroomId' in activeMessage &&
            activeMessage.chatroomId &&
            message.chatroomId == activeMessage.chatroomId
          ) {
            activeMessage.id = message.id;
            localStorage.setItem(
              'activeMessage',
              JSON.stringify(activeMessage)
            );
          }
          let totalCount = parseInt(
            localStorage.getItem('inboxTotalMessageCount').replace(/"/g, '')
          );
          if (totalCount) {
            totalCount = totalCount + 1;
          } else {
            totalCount = 1;
          }
          localStorage.setItem(
            'inboxTotalMessageCount',
            JSON.stringify(totalCount)
          );
          this.sharedService.updateInboxData.emit({
            stopLoader: true,
            totalCount: totalCount,
          });
        }
        console.log(data);
        if (data.length) {
                  
          if(pollingData &&  pollingData.args &&  pollingData.args.maskedMessageReplyClick) {
            data = data.filter((item) => {
              return item.chatroomId !== +pollingData.args.maskedMsgBaseId;
            });
          }       
          
          this.structureService.inboxDataFirstPage = JSON.parse(
            JSON.stringify(data)
          );
          if (parseInt(message.unreadCount) > 0) {
            this.updateInboxCounts({ count: parseInt(message.unreadCount) });
          }
          if (
            this.router.url == '/inbox' ||
            this.router.url == '/inbox/chatroom'
          ) {
            let pageCount = parseInt(localStorage.getItem('pageCountMessage'));
            if (this.structureService.inboxDataCurrentPage == 1) {
              this.structureService.inboxData = JSON.parse(
                JSON.stringify(this.structureService.inboxDataFirstPage)
              );
              this.sharedService.onInboxData.emit({
                inboxData: this.structureService.inboxData,
                emitUpdateInboxData: true,
                inboxDataFirstPage: this.structureService.inboxDataFirstPage,
              });
            }
          }
        }
      });
  }
  activate(
    loaderStatus,
    qlimit = 0,
    pageCountMessage = 1,
    fromPolling: any = false,
    removeChatroomFromListOnPolling: any = false
  ) {
    let siteId : any = 0;
    let dateRange = !isBlank(this.storeService.getStoredData(Store.DATE_RANGE_FILTER_INBOX))? JSON.parse(this.storeService.getStoredData(Store.DATE_RANGE_FILTER_INBOX)) : '';
    let searchKeyword = localStorage.getItem("searchInboxkeywordInbox") || '';
    let showFilterAppliedMessage = !!searchKeyword;
    const userData = JSON.parse(this.structureService.userDetails);
    const config = JSON.parse(this.structureService.userDataConfig);
    if(!isBlank(userData.defaultSitesFilter) && userData.defaultSitesFilter != 0 && config.enable_multisite == 1) {
      siteId = getUserPreferenceSiteIds(userData.defaultSitesFilter);
    }
    const storedData = this.storeService.getStoredData(Store.ACTIVE_MESSAGE_ADVANCE_SEARCH_FILTER);
    let flagValue = 0;
    let messagePriority = 0;
    let tagIds = [];
    let chatThreadTypes = [];
    if (!isBlank(storedData)) {
      flagValue = +storedData.flag || 0;
      messagePriority = +storedData.priority || 0;
      tagIds = storedData.tags.split(',')
        ? storedData.tags
        : [];
        chatThreadTypes = storedData.chatThreadTypes
        ? storedData.chatThreadTypes.split(',').map(Number)
        : [];
      this.structureService.priorityFilterValue = messagePriority;
      this.structureService.flagFilterValue = flagValue;
      this.structureService.filteredTags = tagIds;
      this.structureService.inboxFilterApplied = Filter.ADVANCE;
      showFilterAppliedMessage = true;
    }
    const params: GetChatMessages = {
      loaderStatus: loaderStatus,
      archived: 0,
      flagValue,
      messagePriority,
      searchKeyword,
      fromInboxPagination: true,
      pageCount: pageCountMessage,
      tagIds: tagIds.toString(),
      dateRange,
      mentionedUsers: '',
      siteIds: 0,
      chatThreadTypes
  };
    this.messageService.getChatMessages(params)
      .subscribe((response: GetChatMessagesResponse) => {
        let dataRes: any = response.data.messages;
        this.sharedService.inboxUnreadMessageCount.emit(
          { totalUnreadMessagesCount: response.data.totalUnreadMessagesCount}
        );
        if (!removeChatroomFromListOnPolling) {
          if (pageCountMessage) {
            this.structureService.inboxTotalMessageCount = +response.data.totalChatRoomsCount;
            localStorage.setItem(
              'inboxTotalMessageCount',
              JSON.stringify(response.data.totalChatRoomsCount)
            );
          }
          if (this.router.url === '/inbox') {
            if (
              !fromPolling ||
              (fromPolling &&
                fromPolling.args &&
                fromPolling.args.type == 'messages' &&
                fromPolling.args.setCount &&
                fromPolling.args.setCount == 1)
            ) {
              const types = 'messages';
              this.getUserInboxCounts(types);
            } else if (
              fromPolling &&
              fromPolling.args &&
              fromPolling.args.type == 'messages' &&
              fromPolling.args.setCount &&
              fromPolling.args.setCount == 2
            ) {
              this.updateInboxCounts();
            }
          } else if (this.router.url == '/inbox/chatroom') {
            if (
              !fromPolling ||
              (fromPolling &&
                fromPolling.args &&
                fromPolling.args.type == 'messages' &&
                fromPolling.args.setCount &&
                fromPolling.args.setCount == 1)
            ) {
              const types = 'messages';
              this.getUserInboxCounts(types);
            } else if (
              fromPolling &&
              fromPolling.args &&
              fromPolling.args.type == 'messages' &&
              fromPolling.args.setCount &&
              fromPolling.args.setCount == 2 &&
              fromPolling.args.chatroomId
            ) {
              if (
                localStorage.getItem('targetId') != fromPolling.args.chatroomId
              ) {
                this.updateInboxCounts();
              }
            }
          } else if (!fromPolling) {
              this.getUserInboxCounts('messages');
          }
          this.structureService.inboxMessageResultsCount = dataRes.length;
        }
        if (dataRes.length) {
          if (removeChatroomFromListOnPolling) {
            dataRes = [
              ...this.structureService.inboxDataFirstPage,
              ...dataRes,
            ];
          }
          const userData = this.userData;
          dataRes.map((message) => {
            message.sent = message.sent ? message.sent : message.requestedOn;
            message.maskedSent = message.sent;
            if (!message.subList) {
              message.subList = [];
              message.subListIDs = [];
            }
            if (!parseInt(message.baseChatroomId)) {
              if (!message.maskedUnreadCount) {
                message.maskedUnreadCount = 0;
              }
              let maskedUnreadCountSet = false;
              dataRes.filter((filteredData) => {
                if (message.messageType == '2') {
                  filteredData.initiatedBaseId = filteredData.baseChatroomId;
                }
                if (
                  message.messageType === '2' &&
                  message.chatroomId == filteredData.baseChatroomId &&
                  parseInt(message.messagesCount) <=
                    parseInt(filteredData.messagesCount)
                ) {
                  if (!message.isSelfMessage) {
                    message.baseChatroomId = 0;
                    message.maskedUnreadCount =
                      parseInt(message.maskedUnreadCount) +
                      parseInt(filteredData.unreadCount);
                    message.unreadCount = 0;
                    filteredData.baseChatroomId = 0;
                    filteredData.messageType = '2';
                    filteredData.hiddenChatRoomId = message.chatroomId;
                    message.movedToSublist = true;
                  } else {
                    filteredData.showmaskedlogo = false;
                    message.subList.push(filteredData);
                    message.subListIDs.push(filteredData.chatroomId);
                    message.maskedUnreadCount =
                      parseInt(message.maskedUnreadCount) +
                      parseInt(filteredData.unreadCount);
                    filteredData.sent = filteredData.sent
                      ? filteredData.sent
                      : filteredData.requestedOn;
                    if (filteredData.sent > message.maskedSent) {
                      message.maskedSent = filteredData.sent;
                    }
                    filteredData.movedToSublist = true;
                  }
                  maskedUnreadCountSet = true;
                }
              });
              if (
                message.messageType == '2' &&
                !maskedUnreadCountSet &&
                !message.maskedUnreadCount
              ) {
                message.maskedUnreadCount = message.unreadCount;
              }
              if (message.updated_date) {
                message.maskedSent = message.updated_date;
              }
            } else {
              if (message.baseChatroomId) {
                message.showmaskedlogo = true;
              }
            }
          });
          dataRes = dataRes.filter(function (filteredData) {
            return filteredData.category === 'chat-messages'
              ? +filteredData.baseChatroomId === 0 ||
                  !filteredData.movedToSublist
              : true;
          });
          this.structureService.inboxDataFirstPage = JSON.parse(
            JSON.stringify(dataRes)
          );
          if (
            this.router.url === '/inbox' ||
            this.router.url === '/inbox/chatroom'
          ) {
            let pageCount = parseInt(localStorage.getItem('pageCountMessage'));
            if (pageCount <= 1) {
              this.structureService.inboxData = dataRes;
              this.sharedService.onInboxData.emit({
                inboxData: this.structureService.inboxData,
                showFilterAppliedMessage,
                emitUpdateInboxData: true,
                inboxDataFirstPage: this.structureService.inboxDataFirstPage,
                inboxTotalMessageCount:
                  this.structureService.inboxTotalMessageCount,
                totalUnreadMessagesCount: response.data.totalUnreadMessagesCount
              });
            }
          }
        } else {
          this.sharedService.updateInboxData.emit({
            stopLoader: true,
            showFilterAppliedMessage,
            totalCount: response.data.totalChatRoomsCount,
            totalUnreadMessagesCount: response.data.totalUnreadMessagesCount,
          });
        }
      }, ()  => {
        this.sharedService.updateInboxData.emit({
          stopLoader: true,
          totalCount: 0,
        });      
      });
  }
  updateInboxCounts(params: { operationType?: MessageOperationType; updateInbox?: boolean; count?: number } = {}) {
    let { operationType, updateInbox, count } = Object.assign({ operationType: MessageOperationType.ADD, updateInbox: true, count: 1 } ,params);
    count = operationType === MessageOperationType.ADD ? count : -count;
    this.inboxUnreadMessageCount += count;
    if (!this.inboxUnreadMessageCount || this.inboxUnreadMessageCount < 0) {
      this.inboxUnreadMessageCount = 0;
    }
    this.structureService.inboxUnreadMessageCount = this.inboxUnreadMessageCount;
    if (updateInbox && this.unreadCountUpdateOnPollingInInbox) {
      this.sharedService.inboxUnreadMessageCount.emit({ incrementCount: count });
    }
  }
  getInboxCountsBasedOnChatroom(chatroomId) {
    this.structureService
      .getInboxCounts('messages', chatroomId)
      .then((inboxCounts: any) => {
        if (inboxCounts.messages && parseInt(inboxCounts.messages) > 0) {
          this.inboxUnreadMessageCount =
            this.inboxUnreadMessageCount + parseInt(inboxCounts.messages);
          if (
            !this.inboxUnreadMessageCount ||
            this.inboxUnreadMessageCount < 0
          ) {
            this.inboxUnreadMessageCount = 0;
          }
          this.structureService.inboxUnreadMessageCount =
            this.inboxUnreadMessageCount;
        }
      });
  }
  getUserInboxCounts(type = '') {
    let types: any = [];
    switch (type) {
      case 'messages':
        types.push('messages');
        break;
      case 'forms':
        if (this.userData.config.enable_forms === '1') {
          types.push('forms');
        }
        break;
      default:
        types.push('messages');
        if (this.userData.config.enable_forms === '1') {
          types.push('forms');
        }
        break;
    }

    if (types.length) {
      if (types.indexOf('forms') != -1) {
        this.userInboxCounts.forms.loading = true;
      } else {
        this.userInboxCounts.forms.loading = false;
      }

      types = types.join(',');

      this.structureService.getInboxCounts(types).then(
        (inboxCounts: any) => {
          if (inboxCounts.forms) {
            this.userInboxCounts.forms.totalCount =
              parseInt(inboxCounts.forms.pending) +
              parseInt(inboxCounts.forms.completed);
            this.userInboxCounts.forms.loading = false;
            this.userInboxCounts.forms.error = false;
            this.structureService.userInboxCounts = this.userInboxCounts;
          }

          if (inboxCounts.messages) {
            this.inboxUnreadMessageCount = parseInt(inboxCounts.messages);
            if (
              !this.inboxUnreadMessageCount ||
              this.inboxUnreadMessageCount < 0
            ) {
              this.inboxUnreadMessageCount = 0;
            }
            this.structureService.inboxUnreadMessageCount = this.inboxUnreadMessageCount;
          }

          this.inboxCountLoading = false;
          this.inboxLoadingError = false;
        },
        () => {
          this.userInboxCounts.forms.error = true;
          this.inboxCountLoading = false;
          this.inboxLoadingError = true;
        }
      );
    }
  }
  getSignatureDocument() {
    this.structureService
      .getSignatureRequest()
      .then((data) => {
        data = JSON.parse(JSON.stringify(data));
        if (data['signatureRequest']) {
          this.signatureRequest = data['signatureRequest'];
        }
        this.signatureCountLoading = false;
        this.signatureLoadingError = false;
        this.structureService.signatureRequestInboxDataInitialLoading = false;
        this.structureService.signatureRequestInboxData =
          this.signatureRequest;
        this.sharedService.signatureRequestInboxData.emit(
          this.signatureRequest
        );
      })
      .catch((ex) => {
        this.signatureLoadingError = true;
        console.log('error-getInboxData---', ex);
      });
  }

  getDocumentUnreadCount(notFromPolling=true) {
    console.log("getDocumentUnreadCount: notFromPolling => "+ notFromPolling)
    this.structureService
      .getMySignatureRequestCount(null,notFromPolling)
      .then((data) => {
        data = JSON.parse(JSON.stringify(data));
        if (data['mySignatureRequestCount']) {
          this.structureService.signatureRequestUnreadCount =
            data['mySignatureRequestCount'].totalPendingCount + data['mySignatureRequestCount'].totalSignedCount;
        }
        this.signatureCountLoading = false;
        this.signatureLoadingError = false;
      })
      .catch((ex) => {
        this.signatureLoadingError = true;
        this.signatureCountLoading = false;
        console.log('error-getInboxData---', ex);
      });
  }

  reloadCurrentController() {
    setTimeout(() => {
      console.log('Reloading...', this.router.url);

      this.config = this.structureService.userDataConfig;
      this.userDetails = this.structureService.userDetails;
      this.configData = JSON.parse(this.config);
      this.userData = JSON.parse(this.userDetails);
      this.activateNow(true);
      console.log('IS NEW:::: ', this.configData, this.userData);
    }, 1000);
  }

  activateNow(reload) {
    this.tenantId = this.structureService.getCookie('tenantId');
    this.route.params.subscribe((val) => {
      this.userInfo = this.structureService.loginUserDetails;

      if (this.userInfo.length == 0) {
        this.structureService.getConfigDetails().then((data) => {
          if (data['getSessionTenant']) {

            this.menuSettings.allowPatientDiscussionGroup =
              data['getSessionTenant'].patientDiscussionGroup;
          }
        });
      } else {
        this.menuSettings.allowPatientDiscussionGroup =
          +this.userInfo.config.allow_virtual_patient === 1;
      }

      this.accountId = this.userInfo.tenantId;
      this.accountId =
        typeof this.accountId === 'undefined'
          ? this.structureService.getCookie('tenantId')
          : this.accountId;
      this.userRole =
        typeof this.userInfo.roleName === 'undefined'
          ? this.structureService.getCookie('userRole')
          : this.userInfo.roleName;

      if (
        this.configData.enable_nursing_agencies_visibility_restrictions ==
          '1' &&
        this.userData.nursing_agencies != ''
      ) {
        this.nursingAgencyUser = true;
        if (
          this.configData.na_manage_education_material_roles &&
          this.configData.na_manage_education_material_roles == 1
        ) {
          this.canNaUserManageEdm = true;
        } else {
          this.canNaUserManageEdm = false;
        }

        if (
          this.configData.na_manage_group_roles &&
          this.configData.na_manage_group_roles == 1
        ) {
          this.canNaUserManageGroup = true;
        } else {
          this.canNaUserManageGroup = false;
        }

        console.log('Updated Config', this.configData);
      }

      if (this.userRole === 'Super Admin') {
        this.previlages['superAdmin'] = true;

        let manageTenants = this.userInfo.privileges;
        manageTenants =
          typeof manageTenants === 'undefined'
            ? this.structureService.getCookie('userPrivileges')
            : manageTenants;
        manageTenants = manageTenants.split(',');
        for (let i = 0; i < manageTenants.length; i += 1) {
          this.previlages[manageTenants[i]] = true;
        }

        this.manageConfig =
          typeof this.manageConfig === 'undefined'
            ? JSON.parse(this.structureService.userDataConfig)
            : this.manageConfig;
      } else {

        this.previlages['tenantConfig'] = true;

        let manageTenants = this.userInfo.privileges;
        manageTenants =
          typeof manageTenants === 'undefined'
            ? this.structureService.getCookie('userPrivileges')
            : manageTenants;

        manageTenants = manageTenants.split(',');
        for (let i = 0; i < manageTenants.length; i += 1) {
          this.previlages[manageTenants[i]] = true;
        }

        this.manageConfig =
          typeof this.manageConfig === 'undefined'
            ? JSON.parse(this.structureService.userDataConfig)
            : this.manageConfig;
      }
      this.showDashboard =
        (this.structureService.domainName != 'Prod' &&
          this.previlages['showDashboard']) ||
        (this.structureService.domainName == 'Prod' &&
          ((['1', '17', '49',].includes(this.tenantId) &&
            this.previlages['showDashboard']) ||
            ['100508', '41', '15',].includes(
              this.structureService.getCookie('userId')
            )));
    });
    if (reload) {
      const currentUrl = this.router.url;
      let patientTopicCount = localStorage.getItem('patientTopicCount');
      this.structureService.routingUrl = this.structureService.currentUrlNow;
      const showWarning = !(this.structureService.previousUrlNow === '/' || this.structureService.previousUrlNow.includes('authtoken')); // Loading default page or routing from ground control
      if (this.structureService.canActivateRouteCheck(showWarning)) {
        if (currentUrl == '/faq') {
          this.router.navigate(['inbox',]);
        } else if (currentUrl == '/inbox/chatroom' || currentUrl == '/inbox') {
          this.sharedService.reloadOnConfigChange.emit(true);
        } else if (
          (currentUrl == '/message/tag-definitions' ||
            currentUrl == '/message/tag-type' ||
            currentUrl == '/message/logs' ||
            currentUrl == '/documents/tagged-forms') &&
          this.userData.defaultPage != '/inbox'
        ) {
          this.router.navigate([this.userData.defaultPage || '/profile',]);
        } else {
          $('.remove-ul').css('display', 'none');
          $('li').removeClass('cat__menu-left__submenu--toggled');
        }
      }
      this.sharedService.onPatientTopicCountChange.emit(patientTopicCount);
    }
    this.structureService.privileges = this.previlages;
  }

  acceptCall() {
    console.log('accept click call');
    if (!this.videoChatData) {
      this.videoChatData = JSON.parse(localStorage.getItem('videoChatData'));
    }
    if (this.sharedService.pushNotification) {
      this.clearPushNotification(this.videoChatData);
    }
    this.sharedService.pushNotification = false;
    this.sharedService.muteInitiatorAudio.emit();
    console.log(this.videoChatData);
    console.log(JSON.parse(localStorage.getItem('videoChatData')));
    this.sharedService.onGoingChatrooom = this.videoChatData.roomId;
    this.sharedService.acceptCallData.emit(this.videoChatData);
    document.getElementById('video-call-notify').style.display = 'none';
    this.sharedService.videoCallReceived = true;
    this.sharedService.videoCall = true;
    console.log('acceptCall------and set localStorage------ Called.');
    this.setLocalStorageParticipants('true');
    if (!this.acceptedPush) {
      localStorage.setItem('targetId', this.videoChatData.roomId);
      localStorage.setItem('targetName', 'group-chat');
      localStorage.setItem(
        'chatWithHeading',
        'Chat with ' + this.videoChatData.from
      );
      const activeMessageIndex = this.structureService.inboxData.findIndex(
        (inboxData) => inboxData.chatroomId == this.videoChatData.roomId
      );
      if (activeMessageIndex > -1) {
        localStorage.setItem(
          'activeMessage',
          this.structureService.inboxData[activeMessageIndex]
        );
        this.gotoChatRoom(this.structureService.inboxData[activeMessageIndex]);
      } else {
        localStorage.setItem('archived', 'false');
        localStorage.setItem('activeMessage', this.videoChatData.activeMessage);
      }
    }
    this.sharedService.joincall.emit();
    const emitDataJoinee = {
      joineeId: this.userData.userId,
      joineeName: this.userData.displayName,
      roomId: this.videoChatData.roomId,
    };
    this._GlobalDataShareService.setVideoChatDetails(emitDataJoinee, 'joinee');
    this.structureService.socket.emit('videoChatJoin', emitDataJoinee);
    clearTimeout(this.ringTimmer);

    //console.log(this.activeMessage);
    this.playAudio(false);
    console.log('accepted push', this.acceptedPush);

    // this.sharedService.videoChatInitialize.emit();
    if (!this.acceptedPush) {
      if (this.router.url.includes('/inbox/chatroom')) {
        console.log('Same Url');
        this.sharedService.reloadOnConfigChange.emit(true);
      } else this.router.navigate(['/inbox/chatroom',]);
    }
    let activityData = {
      activityName: 'Video Call Accept',
      activityType: 'video communication',
      activityLinkageId: this.videoChatData.roomId,
      activityDescription:
        this.userData.displayName +
        ' (' +
        this.userData.userId +
        ') accepted the video call from chatroom - ' +
        this.videoChatData.roomId,
    };
    this.structureService.trackActivity(activityData);
  }

  gotoChatRoom(message) {
    let setChatWithHeading: any = 0;
    if (message.chatWithRole == 'Caregiver') {
      setChatWithHeading = 1;
    }
    localStorage.setItem('setChatWithHeading', setChatWithHeading);

    const unreadMessageStatus = message.unread;
    if (parseInt(message.unread) || parseInt(message.unreadCount)) {

      //console.log("emit inbox click-------------------------------");
      this.sharedService.readInboxData.emit(message);
    }

    const activeMessage = message; //Forwad option from chat window.
    let chatWithDob = '';
    let fromDob = '';
    let chatwithDay = new Date(message.chatWithDob).getDay();
    let messageDay = new Date(message.dob).getDay();
    if (
      message.chatWithDob &&
      !isNaN(chatwithDay) &&
      message.chatWithRole != 'Caregiver'
    ) {
      chatWithDob =
        ' DOB ' + this.datePipe.transform(message.chatWithDob, 'MM/dd/yy');
    }
    if (
      message.dob &&
      !isNaN(messageDay) &&
      message.chatWithRole != 'Caregiver'
    ) {
      fromDob = ' DOB ' + this.datePipe.transform(message.dob, 'MM/dd/yy');
    }
    let chatwith = '';
    if (message.chatWithRole == 'Caregiver' || message.role == 'Caregiver') {
      const dob = message.caregiver_dob || message.chatWith_caregiver_dob;
      fromDob = ' DOB ' + this.datePipe.transform(dob, 'MM/dd/yy');
      chatWithDob = ' DOB ' + this.datePipe.transform(dob, 'MM/dd/yy');
      chatwith =
        this.userData.userId === message.userid
          ? message.chatWithRoleId === '3'
            ? message.caregiver_userid || message.chatWith_caregiver_userid
              ? (message.caregiver_displayname ||
                  message.chatWith_caregiver_displayname) +
                chatWithDob +
                ' (' +
                message.chatWith +
                ')'
              : message.chatWith + chatWithDob
            : message.chatWith + ', ' + message.chatWithRole
          : message.grp === '3'
          ? message.caregiver_userid || message.chatWith_caregiver_userid
            ? (message.caregiver_displayname ||
                message.chatWith_caregiver_displayname) +
              fromDob +
              ' (' +
              message.fromName +
              ')'
            : message.fromName + fromDob
          : message.fromName + ', ' + message.role;
    } else {
      chatwith =
        this.userData.userId === message.userid
          ? message.chatWithRoleId === '3'
            ? message.chatWith + chatWithDob
            : message.chatWith + ', ' + message.chatWithRole
          : message.grp === '3'
          ? message.fromName + fromDob
          : message.fromName + ', ' + message.role;
    }

    let chatWithHeading =
      parseInt(message.message_group_id) && message.groupName
        ? message.groupName
        : parseInt(message.messageType) && parseInt(message.messageType) != 2
        ? 'Message from ' +
          (message.userid !== this.userData.userId ? message.fromName : 'Me')
        : parseInt(message.messageType) != 2
        ? 'Chat with ' + chatwith
        : message.title;
    if (message.messageType == 1) {
      chatWithHeading = 'Broadcast ' + chatWithHeading;
    }
    let targetID = message.chatroomId;
    if (
      !message.isSelfMessage && message.isPatientInitiatedChat && message.messageUnreadCount
    ) {
      let pushMessage = 'Your message is getting reviewed';
      const sentTime = message.messageOrder;
      this.structureService.socket.emit(
        'confirmReviewPushNotification',
        { chatRoomId: targetID.toString(), messageUserId: message.userid || message.chatCreatedBy},
        (data, type) => {
          if (type && data < sentTime) {
            let deeplinking = {
              state: 'eventmenu.group-chat',
              stateParams: {
                targetID: targetID,
                targetName: 'group-chat',
              },
              activeMessage: {
                sent: sentTime,
                messageType: activeMessage.messageType || 0,
                baseChatroomId: activeMessage.baseChatroomId || 0,
                userid: activeMessage.userid,
                fromName: `"${activeMessage.chatHeading}"`,
                message_group_id: activeMessage.message_group_id || 0,
                createdby: activeMessage.createdby || 0,
              },
            };
            const notificationData = {
              sourceId: CONSTANTS.notificationSource.message,
              sourceCategoryId: CONSTANTS.notificationSourceCategory.staffReviewingPatientMessage
            };
            this.structureService.sentPushNotification(
              message.userid || message.chatCreatedBy.toString(),
              0,
              pushMessage,
              '',
              deeplinking,
              '',
              '',
              undefined,
              undefined,
              undefined,
              undefined,
              notificationData
            );
          }
        }
      );
    }
    activeMessage.chatWith = message.chatHeading;
    activeMessage.chatWithUserId = message.userid || message.chatCreatedBy;

    let activityData = {
      activityName: 'Start Chat Session',
      activityType: 'messaging',
      activityLinkageId: targetID,
      activityDescription:
        'Chat With - ' +
        chatwith +
        ' in Chatroom ' +
        targetID +
        '.' +
        ' Client Time:' +
        new Date().getHours() +
        ':' +
        new Date().getMinutes() +
        ' ' +
        new Date().toString()
.match(/([A-Z]+[\+-][0-9]+.*)/)[1],
    };
    this.structureService.trackActivity(activityData);
    localStorage.setItem('targetId', targetID);
    localStorage.setItem('targetName', 'group-chat');
    localStorage.setItem('chatWithHeading', chatWithHeading);
    localStorage.setItem('activeMessage', JSON.stringify(activeMessage));
    localStorage.setItem('archived', 'false');
  }
  setLocalStorageParticipants(condition) {
    console.log('local storage set condition---->' + condition);
    localStorage.setItem('participant', condition);
  }

  rejectCall() {
    if (!this.videoChatData) {
      this.videoChatData = JSON.parse(localStorage.getItem('videoChatData'));
    }
    if (this.sharedService.pushNotification) {
      this.clearPushNotification(this.videoChatData);
    }
    this.sharedService.pushNotification = false;
    this.playAudio(false);
    document.getElementById('video-call-notify').style.display = 'none';
    this.structureService.socket.emit('videoChatReject', {
      initiator: this.videoChatData.fromId,
      rejectUser: this.userData.userId,
      RejectUserName: this.userData.displayName,
      action: 'user',
      roomId: this.videoChatData.roomId,
    });
    clearTimeout(this.ringTimmer);
    this.setLocalStorageParticipants('false');

    const activityData = {
      activityName: 'Video Call Reject',
      activityType: 'video communication',
      activityLinkageId: this.videoChatData.roomId,
      activityDescription:
        this.userData.displayName +
        ' (' +
        this.userData.userId +
        ') rejected the video call from chatroom - ' +
        this.videoChatData.roomId,
    };
    this.structureService.trackActivity(activityData);
    this.sharedService.pushNotification = false;
  }
  ringNotificationClose() {
    console.log('ringNotificationClose function trigger', this.videoChatData);
    this.structureService.socket.emit('videoChatReject', {
      initiator: this.videoChatData.fromId,
      rejectUser: this.userData.userId,
      RejectUserName: this.userData.displayName,
      action: 'notAccept',
      roomId: this.videoChatData.roomId,
    });
    document.getElementById('video-call-notify').style.display = 'none';
    document.getElementById('video-notify-cntnt').innerHTML = '';
    this.playAudio(false);
    this.videoChatData = '';
    clearTimeout(this.ringTimmer);
  }

  linkToInbox() {
    if (this.structureService.currentUrlNow == '/inbox') {
      this.structureService.activeMessageClicked = true;
    }
    this.structureService.inboxClicked(true);
  }

  clearPushNotification(data) {
    if (serviceWorkerReg.__proto__.hasOwnProperty('getNotifications')) {
      serviceWorkerReg.getNotifications().then((notifications) => {
        console.log(notifications);
        for (let i = 0 ; i < notifications.length; i += 1) {
          if (notifications[i].tag.toString() === data.roomId.toString()) {
            notifications[i].close();
          }
        }
      });
    }
  }

  chatWithModel(groupId) {
    this.sharedService.chatWith.emit(groupId);
  }
  /**
   * To expand and close app center submenu items on click, For dynamic menus items
   * @param event click event data
   */
  handleAppCenterLeftMenuItems(event : any){
    if (
      event.target.id !== 'app-worklist-group' &&
      event.target.id !== 'app-worklist-menu'
    ) {
      this.toggleAppSubMenu = false; 
      this.showAppCenterWorklistMenu = '';
      this.toggleAppMenu = false;
    } else {
      this.toggleAppMenu = true;
      if (
        event.target.id === 'app-worklist-group' &&
        (this.showAppCenterWorklistMenu === '' ||
          this.showAppCenterWorklistMenu === event.target.innerText)
      ) {
        this.showAppCenterWorklistMenu = event.target.innerText;
        this.toggleAppSubMenu = !this.toggleAppSubMenu;
      } else if (
        event.target.id === 'app-worklist-group' &&
        this.showAppCenterWorklistMenu !== event.target.innerText
      ) {
        this.showAppCenterWorklistMenu = event.target.innerText;
        this.toggleAppSubMenu = true;
      }
    }
  }
  messagePolling(data){
    const curChatRoomId = localStorage.getItem('targetId');
    if (data.args.removeChatroomFromListOnPolling && this.router.url === '/inbox/chatroom' && +curChatRoomId === +data.args.chatroomId) {
      swal(
        {
          title: '',
          text: this._ToolTipService.getTranslateData('MESSAGES.PDG_CHAT_MEMBER_ALERT_MESSSAGE'),
          showCancelButton: false,
          type: 'success',
          confirmButtonText:this._ToolTipService.getTranslateData('BUTTONS.GO_TO_INBOX') ,
          html: true,
          confirmButtonClass: 'btn-success'
        },
        () => {
          this.router.navigate(['/inbox']);
        }
      );
    }
    this.structureService.socket.emit('messagePollingAck', {
      userid: this.userData.userId,
      displayName: this.userData.displayName,
      chatroomId: data && data.args && data.args.chatroomId ? data.args.chatroomId : 0,
      messageId: data && data.args && data.args.messageId ? data.args.messageId : 0,
      type: data && data.args && data.args.type ? data.args.type : ''
    });
    if (this.userData.defaultPage === '/inbox') this.setInboxDataOnPolling(data);
  }
  private handleMessagePolling(data: any, advanceSearchFilter: any) {
    const chatThreadTypeFromArgs = data.args.chatThreadType;
    const argsChatThreadTypeNumber = messageTypeAndCategory[chatThreadTypeFromArgs];
    const advanceSearchChatThreadTypeNumbers = advanceSearchFilter.chatThreadTypes.toString().split(',').map(Number);
  
    if (advanceSearchChatThreadTypeNumbers.includes(argsChatThreadTypeNumber)) {
      this.unreadCountUpdateOnPollingInInbox = true;
      this.messagePolling(data);
    } else {
      this.unreadCountUpdateOnPollingInInbox = false;
      if (!data.args.removeChatroomFromListOnPolling) {
        this.updateInboxCounts({ operationType: MessageOperationType.ADD});
      }
    }
  }
}
