// import { Component, OnInit } from '@angular/core';
// import { StructureService } from '../../structure/structure.service';
// declare var $: any;
// declare var jQuery: any;

// @Component({
//   selector: 'app-menu-sidebar',
//   templateUrl: './menu-sidebar.component.html',
// })
// export class MenuSidebarComponent implements OnInit {
//   menuShow: boolean = true;
//   constructor(
    
//     public _structureService: StructureService
//   ) { console.log("SIDE MENU --------- : ")}
//   ngOnInit() {
//     console.log("SIDE MENU --------- : ")
//     this._structureService.displayProgress.subscribe(
//       (displayProgress) => {
//         if (displayProgress) {
//           // this.reloadCurrentController();

//           this.menuShow = true;
//         } else {
//           this.menuShow = false;
//           // console.log(localStorage.getItem('showWorklistMenu'));
//           // console.log(localStorage.getItem('toggleMenu'));

//         }
//         //   console.log("hiiiii",this._structureService.displayProgress);
//         //     this.menuShow = false
//         console.log(this.menuShow);
//         console.log(localStorage.getItem('appName'));
//       })
//   }

// }

import { Component, OnInit, Renderer2 } from '@angular/core';
import { StructureService } from '../../structure/structure.service';
import { Router, NavigationEnd } from '@angular/router'; // Add Router

@Component({
  selector: 'app-menu-sidebar',
  templateUrl: './menu-sidebar.component.html',
})
export class MenuSidebarComponent implements OnInit {
  menuShow: boolean = true;
  hideMenuRoutes = ['/login', '/external-form']; // Routes where menu should hide

  constructor(
    public _structureService: StructureService,
    private renderer: Renderer2,
    private router: Router // Inject Router
  ) {}

  ngOnInit() {
    this._structureService.hideMenu$.subscribe((shouldHide) => {
      if (shouldHide) {
        this.renderer.addClass(document.body, 'external-user-form-landing');
        this.menuShow = false;
      } else {
        this.renderer.removeClass(document.body, 'external-user-form-landing');
        this.menuShow = true;
      }
    });
  }

  checkRouteAndHideMenu(url: string) {
    const shouldHideMenu = this.hideMenuRoutes.some(route => url.includes(route));
    
    if (shouldHideMenu) {
      this.renderer.addClass(document.body, 'external-user-form-landing');
      this.menuShow = false;
    } else {
      this.renderer.removeClass(document.body, 'external-user-form-landing');
      this.menuShow = true;
    }
  }
}