export interface ActiveMessageData {
  chatWith: string;
  chatWithGrp: string;
  chatWithRoleId: string;
  chatWithUserId: string;
  chatroomid: string;
  chatwithDob: string;
  chatwithUserRole: string;
  createdby: string;
  is_patient_discussion_group: string;
  message_group_id: number;
  selectedTenantId: string;
  patient_caregiver_displayname?: string;
  patient_caregiver_passwordStatus?: string;
}

export interface Status {
  code: number;
  message: string;
}
export interface CreateChatroomResponse {
  success: boolean;
  status: Status;
  data: any;
}
