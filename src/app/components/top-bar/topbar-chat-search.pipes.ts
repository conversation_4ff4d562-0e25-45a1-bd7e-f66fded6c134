import { Component, OnInit, Pipe, PipeTransform } from '@angular/core';
import { DatePipe } from '@angular/common';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Pipe({
  name: 'TopBarSearchFilter',
  pure: false
})
export class TopBarSearchFilterPipe implements PipeTransform {
    transform(searchItems:any, criteria:any, keyItems:any): any {
      if (!searchItems)
      return searchItems;
      
      return searchItems.filter(item =>{
        for (let key in item ) {
          if(keyItems) {
           if(keyItems.includes(key) && (item[key]+ "").toLowerCase().includes(criteria.toLowerCase())){
             return true;
           }
          } else {
           if((item[key]+ "").toLowerCase().includes(criteria.toLowerCase())){
             return true;
           }
          }
        }
        return false;
     });
    }
}
 