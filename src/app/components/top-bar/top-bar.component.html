<ejs-toast #element showCloseButton=true width=400 timeOut=0 [position]='position'>
    <ng-template #content>
        <div><i class="fa fa-exclamation-triangle"></i> Import job completed successfully. Please click <a href='{{importDownloadUrl}}' target='_blank'>here</a> to download the processed file</div>
    </ng-template>
</ejs-toast>
<div class="info-overlay" *ngIf="infoBottomOverlay && +userData.group === 3 && initiateChatPermission">
    <div class="info-overlay overlay-content">
        <div class="info-overlay-bottom">
            <div class="chatbox-container">
                <p ng-bind-html="noActiveTopicOverlayMessage" class="ng-binding">{{'MESSAGES.MESSAGE_CONTACT_STAFF' | translate}}</p>
                <div class="chatwith-icon-button overlay-content" (click)="openChatWithModal(userData.group)">
                    <i class="chat-overlay-icon fa fa-comments"></i>{{'LABELS.CHAT' | translate}} </div>
                <p></p>
            </div>
            <i class="fa  fa-close close-button-overlay" (click)="infoBottomHide()"> </i>
        </div>
    </div>
</div>
<div class="cat__top-bar">
    <!-- left aligned items -->
    <div class="cat__top-bar__left">
        <div class="cat__top-bar__logo">
            <a (click)="navigateToPage('/inbox')" >
                <img *ngIf="showTenantLogo" src="{{_SharedService.assetsUrl}}/a/{{userData.tenantId}}/img/account-logo-on-white-bg.png" onerror="this.src='assets/modules/dummy-assets/common/img/account-logo-on-white-bg.png'" />
                <img *ngIf="!showTenantLogo" [src]="imgUrl"
                     onerror="this.src='assets/modules/dummy-assets/common/img/account-logo-on-white-bg.png'" />
            </a>
        </div>
        <div class="cat__top-bar__item hidden-sm-down">
            <div class="dropdown">
            </div>
        </div>
        <div class="cat__top-bar__item hidden-sm-down">
            <div class="dropdown">
            </div>
        </div>
        <div class="cat__top-bar__item hidden-lg-down">
            <div class="cat__top-bar__search">
            </div>
        </div>
    </div>
    <!-- right aligned items -->
    <div class="cat__top-bar__right">
        <div class="cat__top-bar__item hidden-sm-down">
           </div>
        <div class="cat__top-bar__item">
            <div class="chatwith-container">
                <div class="chatwith-cnt">
                    &nbsp;<button [disabled]="chatWithUsersLoading" class="btn btn-default btn-sm btn-rounded" id="chatwith-button-topbar" (click)="checkMultiplePatient(userData.group)" [hidden]=" !userData.tenantId || ((userData.group =='3' || (privileges.indexOf('chatWithClinician') ==-1 && privileges.indexOf('chatWithPatients') ==-1 && privileges.indexOf('chatWithMessageGroups') ==-1)) && ((userData.group !='3' || privileges.indexOf('chatWithClinician') ==-1 ) && (userData.group !='3' || patientTopic.count != '0'))) "
                        style="cursor:pointer;display:none;"><span *ngIf="chatWithUsersLoading"><i class='fa fa-spinner fa-spin '></i> Loading</span><span *ngIf="!chatWithUsersLoading"><i class="fa fa-comments" aria-hidden="true"></i> Chat</span></button>
                        &nbsp;<button (click)="isVideoChatMinimized=false;muteInitiatorTune()" [ngClass]="{isVideoChatMinimized:'shake animated'}" class="btn btn-primary btn-sm btn-rounded shake animated" *ngIf="isVideoChatMinimized">Show Video Chat</button>
                </div> 
                <div class="status-label" *ngIf="!organizationSwitchPrivilege">
                    {{tenantName}}
                    <span *ngIf="domainName!='Prod'">
                        {{domainName}}
                    </span>
                </div>
                <div class="dropdown  status-label" *ngIf="(organizationSwitchPrivilege && userData.enable_multisite == '1')">
                    <div class="row">
                        <div class="col col-5 text-left domain_name">
                            <a href="javascript: void(0);" class="dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                {{selectedTenant.tenantName}}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-zindex" aria-labelledby="" role="menu" id="crossTenant">
                                <li class="dropdown-item change-cross-tenant-value" *ngFor="let item of crossTenantOptions"
                                    value="{{item.id}}" [ngClass]="{'tenentnamehide':selectedTenant.tenantName==item.tenantName}"
                                    (click)="newCrossTenantSelected(item)"> {{item.tenantName}} </li>
                            </ul>
                            <span *ngIf="domainName!='Prod'">{{domainName}}</span>
                        </div>
                    </div>
                </div>
                <div class="dropdown  status-label" *ngIf="(organizationSwitchPrivilege && userData.enable_multisite != '1')">
                    <div class="row">
                        <div class="col col-5 text-left domain_name">
                            <a href="javascript: void(0);" class="dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                {{selectedTenant.siteName}}
                            </a>
                            <ul class="dropdown-menu dropdown-menu-zindex" aria-labelledby="" role="menu" id="crossTenant">
                                <li [hidden]="!item.siteName" class="dropdown-item change-cross-tenant-value" *ngFor="let item of crossTenantOptions"
                                    value="{{item.id}}" [ngClass]="{'tenentnamehide':selectedTenant.siteName==item.siteName}"
                                    (click)="newCrossTenantSelected(item)"> {{item.siteName}} </li>
                            </ul>
                            <span *ngIf="domainName!='Prod'">{{domainName}}</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="elevio_button_div">
                <button class="btn btn-success btn-sm btn-rounded" (click)="startNewChat()" [hidden]="!initiateChatPermission">{{'BUTTONS.START_CHAT' | translate}}</button>
                <button *ngIf="(userData && userData.config && userData.config.showElevio == '1' && userData.helpCenterKey)" class="btn btn-success btn-sm btn-rounded" (click)="openElevio()">{{'BUTTONS.HELP_CENTER' | translate}}</button>
                <a *ngIf="showTutorialButton" [href]="tutorialLink" target="_blank">
                    <button class="btn btn-success btn-sm btn-rounded">{{'BUTTONS.ACADEMY' | translate}}</button>
                </a>
            </div>
            <div class="intro-btn-container">
                <button id="intro-btn" type="button" [ngClass]="{'element-hide':userData.tenantId != userData.crossTenantId}" class="btn btn-success btn-sm btn-rounded" hidden="true" (click)="startIntro()"><span>Train Me</span></button>
            </div>
            <div class="dropdown cat__top-bar__avatar-dropdown">
                <a href="javascript: void(0);" class="dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                    
                    <span class="cat__top-bar__avatar" href="javascript:void(0);">
                        <img [src] = "profileImageUrl!=='' ? profileImageUrl : './assets/modules/dummy-assets/common/img/avatars/6.png'" alt="Profile" onerror="this.src='assets/img/file-404.png'" outOfOfficeStatus [oooInfo]="oooInfo" [customClass]="'profile-circle-badge'"/>
                    </span>
                    <span class="profile-name"><span class="displayNameProfile">{{displayname}}</span>
                        <span class="profile-role">{{userRole}}</span>
                    </span>
                </a>
                <ul class="dropdown-menu dropdown-menu-right dropdown-menu-zindex" aria-labelledby="" role="menu">
                    <a [routerLink]="['profile']" class="dropdown-item"><i class="dropdown-icon icmn-user"></i> Profile</a>
                    <div class="dropdown-divider"></div>
                    <a [routerLink]="['inbox']" class="dropdown-item"><i class="dropdown-icon icmn-home"></i>Home</a>
                    <div class="dropdown-divider"></div>
                    <a class="dropdown-item" (click)="logout()" href="javascript:void(0)"><i class="dropdown-icon icmn-exit"></i> Logout</a>
                </ul>
            </div>
        </div>
    </div>
</div>
<!-- **Logout Modal if unsend message**-->
<div class="modal fade bd-example-modal-lg unsend-msg-modal" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" id="unsend_msgs">
    <div class="modal-dialog modal-lg">
        <div class="modal-content unsend-msg">
            <p>There are unsent messages in your inbox. Do you want to Close the Application or Wait until the unsent messages are sent?</p>
          <span>  
            <button type="button" class="btn btn-primary btn-sm spaceBtn" data-dismiss="modal">Wait</button>
            <button type="button" class="btn btn-secondary btn-sm session-cancel-btn" data-dismiss="modal" (click)="logout_with_unsend_msg()">Close</button>
        </span>
        </div>
    </div>
</div>
<!-- ******************** Chatwith Model ************************* -->
<div class="modal bd-example-modal-lg forward-modal chatWithModel" id="chatWithModel" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="exampleModalLabel">{{'LABELS.CHAT_WITH' | translate}}</h4>
                <div class="row chat-with-wrapper-filter" id="chatWithWrapperFilter">
                    <div [hidden]="!hideSiteSelection" *ngIf="((optionShow === 'staff' || optionShow === 'partner' || optionShow === 'patient')  && !multiSiteEnable && chatWithTenantFilter.filterEnabled && chatWithTenantFilter.tenants.length) || (multiSiteEnable)">
                        <div class="row pt-4">                                
                            <div class="site-label">
                                <span>{{'LABELS.SITE_S' | translate}}:&nbsp;</span>
                            </div>
                            <div class="site-width" *ngIf="((enableCrossSite && multiSiteEnable ) && (optionShow=='staff' || optionShow=='partner'))">
                                <app-select-sites [events]="eventsSubject.asObservable()" [filterType]=true  [hideApplyFilter]="!multiSiteEnable"  [popupSelectSite]=true (siteIds)="getSiteIds($event)" [crossSiteCommunication]=true [crossSite]=true [selectedSiteIds]="siteSelected" (hideDropdown)="hideDropdown($event)" [dynamic]="dynamic" [newChatTabChange]="tabChange">
                                </app-select-sites>
                            </div>
                            <div class="site-width" *ngIf="(!enableCrossSite || (enableCrossSite && optionShow !='staff' && optionShow !='partner') )">
                                <app-select-sites [events]="eventsSubject.asObservable()" [filterType]=true  [hideApplyFilter]="!multiSiteEnable"  [popupSelectSite]=true (siteIds)="getSiteIds($event)" [crossSite]=true [selectedSiteIds]="siteSelected" (hideDropdown)="hideDropdown($event)" [dynamic]="dynamic" [newChatTabChange]="tabChange"></app-select-sites>
                            </div>
                        </div>
                    </div>
                </div>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="closeModal()">
                  <span aria-hidden="true">&times;</span>
                  </button>
            </div>
            <div class="modal-body top-bar">
                <form>
                        <div class="chatwith-modal-tab" id="chatWithModalTab">
                                <div class="chatwith-model-head col" id="PDGs"
                                *ngIf="privileges.indexOf('chatWithMessageGroups')!==-1 && configData.allow_virtual_patient && configData.allow_virtual_patient != 0" (click)="optionShow!== 'groups' && showData('groups')"
                                [class.cat__apps__messaging__tab--selected]="optionShow=='groups'">
                                 {{'LABELS.PDG' | translate}}
                                </div>
                                <div class="chatwith-model-head col" id="message_groups"
                                    *ngIf="privileges.indexOf('chatWithMessageGroups')!==-1" (click)="optionShow!== 'msggroups' && showData('msggroups')"
                                    [class.cat__apps__messaging__tab--selected]="optionShow=='msggroups'">
                                    {{'LABELS.MESSAGE_GROUPS' | translate}} 
                                </div>
                                <div class="chatwith-model-head col" id="staff"
                                *ngIf="privileges.indexOf('chatWithClinician')!==-1" (click)="optionShow!== 'staff' && showData('staff',true)"
                                [class.cat__apps__messaging__tab--selected]="optionShow=='staff'"> {{'LABELS.STAFF' | translate}} 
                                </div>
                                <div class="chatwith-model-head col" id="partners"
                                *ngIf="privileges.indexOf('chatWithClinician')!==-1" (click)="optionShow!== 'partner' && showData('partner',true)"
                                [class.cat__apps__messaging__tab--selected]="optionShow=='partner'">
                                {{'LABELS.PARTNERS' | translate}}  
                                </div>
                                
                                <div class="chatwith-model-head col" id="patients"
                                *ngIf="privileges.indexOf('chatWithPatients')!==-1" (click)="optionShow!== 'patient' && showData('patient',true)"
                                [class.cat__apps__messaging__tab--selected]="optionShow=='patient'">
                                {{'LABELS.PATIENTS' | translate}}   
                                </div>
                            </div>
                    <div class="row">
                        <div class="col-md-12 mt-5 pl-5" *ngIf="optionShow === 'msggroups'">
                            <div class="search-container">
                                <div class="row">
                                    <div class="col-md-2">{{'LABELS.MESSAGE_GROUPS' | translate}}</div>
                                    <div class="col-md-8">
                                        <app-search-select [siteIds]="searchPdgSiteIds" [itemList]="formattedMessageList" (loadMoreItems)="searchMsgGrp($event)"
                                            (resetListItems)="resetGroup()" [settings]="searchComponentGroupSettings" [reset]="resetSearchSelect" (selectedItem)="onSelectMsgGrp($event)" 
                                            [totalCount]="totalMessageCount"></app-search-select>
                                    </div>
                                </div>
                            </div>
                            
                        </div>
                        <div class="col-md-12 mt-5 pl-5" *ngIf="optionShow === 'groups'">
                            <app-patient-dropdown
                                    [searchType]="'patient'"
                                    [siteIds]="searchPdgSiteIds"
                                    (selectedItem)="setPatientId($event)"
                                    [requestParams]="requestParams"
                                    [hideSubList]="true"
                                    [includePDGInfo]="true"
                                    [customClass]="'col-md-2 pt-1'"
                                    [listType]="'pdg'"
                                ></app-patient-dropdown>                           
                            </div> 
                        <div class="col-md-12 mt-2 pl-5" *ngIf="showSubject">
                            <div class="search-container">
                                <div class="row">
                                    <label class="col-md-2">{{'LABELS.SUBJECT' | translate}}</label>
                                    <div class="col-md-8">
                                        <app-search-select [itemList]="messageGroupTopic" (loadMoreItems)="searchForMessageGroupSubjects($event)" (resetListItems)="searchForMessageGroupSubjects($event)"
                                        [settings]="searchComponentSettings"  [reset]="resetSearchSelectMsg" (selectedItem)="onMsgGrpSubjectChange($event)" [totalCount]="totalSubjectCount" ></app-search-select>
                                    </div>
                                </div>
                            </div>    
                        </div>
                        <div class="col-md-12 mt-5 ml-5" *ngIf="optionShow === userTypes.staff">
                            <div class="search-container">
                                <div class="row">
                                    <div class="col-md-2">{{'LABELS.STAFF' | translate}}</div>
                                    <div class="col-md-8">
                                        <app-user-dropdown [siteIds]="userSiteIds" [disableContactlessUsers]="true" [multiSelection]="true" [requestParams]="requestParams" [selectedUsers]="chatRoomUsers" [userType]="optionShow" (selectedUserDetails)="onSelectStaff($event)"> </app-user-dropdown>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-12 mt-5 ml-5" *ngIf="optionShow === userTypes.partner">
                            <div class="search-container">
                                <div class="row">
                                    <div class="col-md-2">{{'LABELS.PARTNER' | translate}}</div>
                                    <div class="col-md-8">
                                        <app-user-dropdown [siteIds]="userSiteIds" [disableContactlessUsers]="true" [multiSelection]="true" [requestParams]="requestParams" [selectedUsers]="chatRoomUsers" [userType]="optionShow" (selectedUserDetails)="onSelectStaff($event)"> </app-user-dropdown>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <ul class="treeview treeview-section-ui col-md-12" *ngIf="optionShow === 'otherTenantstaff'">
                            <li *ngFor="let cliniciansRole of (otherTenantClinicianDataTenantWise | filterUsersList:userData.userId | searchRolefilter : 'roleData.tenantName' : userSearch.value : ['name','role','displayname'])"  [hidden]="cliniciansRole.searchHideStatus" class="role-{{cliniciansRole.tenantId}}" >
                                <i class="fa fa-plus expand-icon-{{cliniciansRole.tenantId}}" (click)="callAccordion(cliniciansRole.tenantId)" [ngClass]="{'fa-minus':userSearch.value , 'fa-plus':!userSearch.value}" id="staffAccordion{{cliniciansRole.tenantId}}"></i>
                                <label>{{cliniciansRole.tenantName}}</label>
                                <ul class="sub-item-panel sub-item-panel-{{cliniciansRole.tenantId}}" [ngClass]="{'showall':userSearch.value}">
                                    <li *ngFor="let cliniciansRoleUser of cliniciansRole.userList | searchfilterroletreeview: ['name','role'] : (cliniciansRole.filterUserStatus ? userSearch.value : '')" [hidden]="cliniciansRoleUser.searchHideStatus">
                                        <label (click)="selectedModal(cliniciansRoleUser)">{{cliniciansRoleUser.name}} <span *ngIf="cliniciansRoleUser.naTagNames && cliniciansRoleUser.naTagNames != ''">({{cliniciansRoleUser.naTagNames}}) </span> &nbsp;[{{cliniciansRoleUser.role}}]</label>
                                    </li>
                                </ul>
                            </li>
                            <div class="chat-with-empty-data" *ngIf="optionShow ==='otherTenantstaff' && !(otherTenantClinicianDataTenantWise | filterUsersList:userData.userId | searchRolefilter : 'roleData.tenantName' : userSearch.value : ['name','role','displayname']).length && !chatWithLoader.otherTenantstaff" >No Other Branch Clinicians Available</div>
                            <div *ngIf="chatWithLoader.otherTenantstaff" class="loading-container">
                                <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                <div class="loading-text">Loading Other Branch Clinicians...</div>
                            </div>
                        </ul>
                        <div class="col-md-12 mt-5 pl-5" *ngIf="optionShow === 'patient'">
                            <app-patient-dropdown
                                [searchType]="'patient'"
                                [siteIds]="requestParams['siteIds']"
                                (selectedItem)="setPatientId($event)"
                                [requestParams]="requestParams"
                                [disableContactlessUsers]="true"
                                [customClass]="'col-md-2 pt-1'"
                            ></app-patient-dropdown>  
                        </div>
                         <!-- Message Routing -->
                         <div class="col-md-12" *ngIf = "userRoleList.length > 0">
                            <div class="forwarding-behaviour-container">
                               <div class="forward-model-option-user-list" *ngFor="let user of userRoleList">
                                    <div class="forward-user-role" (click)="createChatroomWithRole(user)">
                                        <p>{{user.roleName}} <span *ngIf="user.tenant_id != userData.tenantId">[ {{user.tenant_name}} ]</span> </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <div class="row" *ngIf="(optionShow === userTypes.staff || optionShow === userTypes.partner) && chatRoomUsers.length">
                    <div class="col-md-11 ml-3">
                        <app-accordion [userList]="chatRoomUsers" [showListWhenUsersSelected]="true"
                        (removedUser)="onStaffPartnerUserChange($event)">                            
                        </app-accordion>
                    </div>
                </div>
            </div>
            <div class="modal-footer mt-5">
                <span class="chatwith-modal-tip" [hidden]="userRoleList.length > 0">
                     <img src="./assets/modules/dummy-assets/common/img/chatwith-tip.png" />
                     <span class="modal-footer-text">{{'LABELS.CHAT_WITH_FOOTER_CONTENT' | translate}}</span>
                </span>
                <button type="button" class="btn btn-primary" (click)="createChatRoom()" *ngIf="showChatBtn">{{'BUTTONS.START_CHAT' | translate}}</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="closeModal()">{{'BUTTONS.CLOSE' | translate}}</button>
            </div>
        </div>
    </div>
</div>
<!-- *********************End Section*************************** -->
<div class="modal fade bd-example-modal-lg session-out-modal" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" id="sessionout">
    <div class="modal-dialog modal-lg">
        <div class="modal-content sessionout-msg">
            <p>{{timeoutMsg}}</p>
            <button type="button"(click)='trackCancelActivity()' class="btn btn-secondary btn-sm session-cancel-btn" data-dismiss="modal">Click to Cancel</button>
        </div>
    </div>
</div>
<!-- video element starts -->
<!-- *ngIf="showVideoElement" -->
<div id="permission_dialog" class="permission-box">
    {{dialogMessage}}
<i class="fa fa-times cls-prmsn" aria-hidden="true" (click)="closeDialogBox()"></i>
</div>
<div [ngClass]="_SharedService.videoFull ? 'video-large' : 'video-samll'" id="video_container" [hidden]="isVideoChatMinimized">
    <div class="info-maxremote-source" id='remotebandwith' [hidden]="maxRemoteSourcesChangedStatus">{{maxRemoteSourcesAccomadateMessageUpdate}}</div>
<div class="chat-top-left-icons">
    <ul>
        <li (click)="toggleMute()">
            <b>
                <i class="fa fa-microphone-slash mute" aria-hidden="true" *ngIf="microphonePrivacy"></i>
                <i class="fa fa-microphone" aria-hidden="true" *ngIf="!microphonePrivacy"></i>
            </b>
            <a><i id="btn-mute">Mute</i></a>
        </li>
        <li (click)="toggleCameraPrivacy()">
            <b>
                <i class="fa fa-video-camera" aria-hidden="true" *ngIf="!cameraPrivacy"></i>
                <i class="fa fa-video-camera mute" aria-hidden="true" *ngIf="cameraPrivacy"></i>
            </b>
            <a><i id="btn-cam">Camera Off</i></a>
        </li>
        <li (click)="toggleFullScreen()">
            <b>
                <i class="fa fa-expand" aria-hidden="true" *ngIf="!_SharedService.videoFull"></i>
                <i class="fa fa-compress" aria-hidden="true" *ngIf="_SharedService.videoFull"></i>
            </b>
            <a><i>Resize</i></a>
        </li>
        <span class="video-right-buttons"><!--[hidden]="hideInitiatorAudioBtn"-->
            <li (click)="muteInitiatorTune();" style="color: rgb(82, 179, 170);" class="mute-section" *ngIf="!hideInitiatorAudioBtn">
                    <b>
                    <i class="fa fa-volume-off mute" aria-hidden="true" data-toggle="tooltip" data-animation="false" title="Unmute caller tune" [hidden]="!initiatorCallTune"></i>
                    <i class="fa fa-volume-up" aria-hidden="true" data-toggle="tooltip" data-animation="false" title="Mute caller tune" [hidden]="initiatorCallTune"></i>
                </b>
                </li>
                <li class="minimize-btn" (click)="minimizeChat()" style="color: rgb(82, 179, 170);margin-left: 10px;">
                    <i data-toggle="tooltip" data-animation="false" title="Minimize" class="fa fa-window-minimize"></i>
                </li>
            <li class="close-chat" (click)="disconnect()" [ngClass]="!_SharedService.disconnectEnable ? 'disconnectblock':''">
                <i class="fa fa-phone" aria-hidden="true" id="disconct-btn"></i>
            </li>
        </span>
    </ul>
</div>
<div id="wait_msg" class="user_wait">{{connectionMessage}} <i class="fa fa-spinner fa-spin" style="font-size:18px"></i></div>
 <div id="video-render"></div>
 <div class="clear-div"></div>
</div>
<!-- video element ends -->
<div class="modal fade modal-size-large" id="top-bar-patient-list-modal" tabindex="-1" role="dialog" aria-labelledby="" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="myModalLabel">
                    <ng-container *ngIf="!showAdmissionsList; else admissionList">
                      {{ 'LABELS.SELECT_PATIENT_FOR_INITIATE_CHAT' | translate }}
                    </ng-container>
                    <ng-template #admissionList>
                      {{ 'ADMISSION.MESSAGES.PLEASE_CHOOSE_ADMISSION' | translate }}
                    </ng-template>
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row" *ngIf="!showAdmissionsList; else admissionListData">
                    <ng-container *ngFor="let user of userData.alternate_contact_patient">
                        <div class="col-md-11 ml-5">
                            <div class="cat__core__widget hand-pointer" (click)="_structureService.isMultiAdmissionsEnabled ? getAdmissionsList(user.userId, false) : chatWithModel(userGroupId, user.userId)">
                                <div class="cat__core__step cat__core__step--success">
                                    <div class="cat__core__step__desc">
                                        <h5>{{ user.firstName }} {{ user.lastName }}</h5>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </ng-container>
                </div>
          
                <ng-template #admissionListData>
                    <div class="row mb-5">
                        <div class="col-sm-6"></div>
                        <div class="col-sm-4">
                            <input class="form-control withoutfilter" placeholder="{{ 'PLACEHOLDERS.SEARCH_HERE' | translate }}" #admissionSearchInput (keyup.enter)="getAdmissionsList('', false, admissionsSearchText = admissionSearchInput.value)">
                        </div>
                        <div class="col-sm-2">
                            <button class="btn btn-sm btn-primary srchBtn" id="search_btn_tenant" type="button" [disabled]="!admissionSearchInput.value" (click)="getAdmissionsList('', false, admissionsSearchText = admissionSearchInput.value)">
                              {{ 'BUTTONS.SEARCH' | translate }}
                            </button>
                            <button class="btn btn-sm btn-default resetBtn" id="reset_btn_tenant" type="button" (click)="resetAdmissionsSearch(); admissionSearchInput.value = ''">
                              {{ 'BUTTONS.RESET' | translate }}
                            </button>
                        </div>
                    </div>
                    <div class="row admission-list-div" *ngIf="admissionsList.length && !admissionsLoading">
                        <ng-container *ngFor="let admission of admissionsList">
                            <div [ngClass]="admissionsList.length > 3 ? 'col-md-6' : 'col-md-11 ml-5'">
                                <div class="cat__core__widget hand-pointer" (click)="chatWithModel(userGroupId, associatedUserId, admission.admissionId)">
                                    <div class="cat__core__step cat__core__step--success p-3">
                                        <div class="cat__core__step__desc">
                                            <h5>{{ 'ADMISSION.LABELS.ADMISSION_START_DATE' | translate }}: {{ admission.startDate | date:'MM/dd/yyyy' }}</h5>
                                            <h5>{{ 'LABELS.LINE_OF_BUSINESS' | translate }}: {{ admission.lineOfService }}</h5>
                                            <h5>{{ 'LABELS.SITE' | translate }}: {{ admission.siteName }}</h5>
                                            <h5>{{ 'LABELS.STATUS' | translate }}: {{ admission.status }}</h5>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </ng-container>
                    </div>
                    <div class="col-md-12 text-center pt-2">
                        <span *ngIf="admissionsLoading; else noAdmissionMsg">{{ 'MESSAGES.PLEASE_WAIT_MSG' | translate }}</span>
                        <ng-template #noAdmissionMsg>
                            <span *ngIf="!admissionsList.length">
                                <b>{{ 'ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE' | translate }}</b>
                            </span>
                            <button *ngIf="admissionsList.length && admissionsList.length !== totalAdmissionsCount" class="btn btn-sm btn-default mt-4" id="loading_more_staff" type="button" (click)="getAdmissionsList('', true)">
                                {{ 'BUTTONS.LOAD_MORE' | translate }}
                            </button>
                        </ng-template>
                    </div>
                </ng-template>
            </div>
            <div class="modal-footer" *ngIf="showAdmissionsList">
                <button class="btn btn-default" (click)="showPatientList()" > {{'BUTTONS.CANCEL' | translate}}</button>
            </div>
        </div>
    </div>
</div>
 <!-- ******************** Machform history modal *************************-->
 <div class="modal fade bd-example-modal-lg forward-modal" id="historyWithModel" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
        <div class="modal-header">
            <h4 class="modal-title" id="exampleModalLabel">Form History</h4>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="modal-body historyBody">
        </div>
        </div>
    </div>
</div>
