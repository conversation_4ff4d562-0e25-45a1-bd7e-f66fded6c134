span {
    .click-link {
        color: #0088ff;
        cursor: pointer;
    }
}

.sessionout-msg {
    text-align: center;
    padding: 15px;
}

.sessionout-msg p {
    margin-bottom: 0 !important;
}

.session-out-modal .modal-dialog {
    top: 200px !important;
}

.session-out-modal .btn {
    margin: 25px auto auto auto;
    width: 200px;
}

.row .domain_name {
    margin: auto;
    padding: 10px;
}

.unsend-msg {
    text-align: center;
    margin-top: 20px;
    padding: 20px;
}

.unsend-msg p {
    margin-bottom: 0 !important;
    margin-top: 10px;
}

.unsend-msg-modal .modal-dialog {
    top: 200px !important;
}

.unsend-msg-modal .btn {
    margin: 25px auto auto auto;
    width: 150px;
}

.unsend-msg-modal .spaceBtn {
    margin-right: 10px;
}

.top-bar .filter-enabled .groups-srch-width {
    width: 59% !important;
}

#fullscreen_name {
    bottom: 0px;
    position: absolute;
    text-align: center;
    width: 100%;
}

.on-hidetile-participant-name {
    text-align: center;
}

.load-more-btn {
    text-align: center;
    width: 100%;
    margin-top: 14px;
    float: left;
}
.icon-clear {
    font-size: 1.5rem;
    font-weight: lighter;
    line-height: 1;
    color: #000;
    text-shadow: 0 1px 0 #fff;
    opacity: .5;
    position: relative;
    top: 20.5%;
    right: 7%;
    cursor: pointer;
}
.site-width {
    width: 300px;
}

.admission-list-div {
    max-height: 400px;
    overflow-y: auto;
}
