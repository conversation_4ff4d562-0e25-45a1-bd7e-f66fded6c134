import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>derer2, <PERSON><PERSON><PERSON>d, ElementRef, <PERSON><PERSON><PERSON>, Inject, Renderer } from '@angular/core';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { DatePipe } from '@angular/common';
import { ToastComponent, ToastPositionModel } from '@syncfusion/ej2-angular-notifications';
import { ISubscription } from 'rxjs/Subscription';
import Elevio from 'elevio/lib/client';
import { Subject, Subscription } from 'rxjs';
import { isBlank, getUserPreferenceSiteIds } from 'app/utils/utils';
import { DocumentInterruptSource, Idle, StorageInterruptSource } from '@ng-idle/core';
import { DOCUMENT } from '@angular/common';
import { CONSTANTS, MessagePriority, UserGroup, ChatWithTypes } from 'app/constants/constants';
import { NGXLogger } from 'ngx-logger';
import { ROUTES } from 'app/constants/routes';
import { PermissionService } from 'app/services/permission/permission.service';
import { SearchComponentSettings, ScrollType, LoadMoreItems } from 'app/structure/shared/search-select/search-select.interface';
import { CreateChatroomParameters, FilterItems, GroupData, GroupItems, GroupListItem, GroupsListingRequest, MessageGroup, PaginationItems, SortItems, GroupTopicsListRequest } from 'app/models/message-center/messageCenter';
import { NotifyService } from 'app/services/notify/notify-service';
import * as moment from 'moment';
import { UserListParams } from 'app/structure/shared/user-dropdown/user-dropdown.interface'; 
import { StructureService } from '../../structure/structure.service';
import { WorkListService } from '../../structure/worklists/worklist.service';
import { InboxService } from '../../structure/inbox/inbox.service';
import { userFilterPipe } from '../../structure/inbox/inbox-modal-filter';
import { SearchFilterPipe } from '../../structure/inbox/inbox-search.pipes';
import { SharedService } from '../../structure/shared/sharedServices';
import { CommonVideoService } from '../../../assets/lib/universal-video/common-video.service';
import { GlobalDataShareService } from '../../structure/shared/global-data-share.service';
import { CrossTenantService } from '../../structure/shared/cross-tenant.service';
import { BannerAlertTagComponent } from '../../structure/manage-alert/banner-alert-tag.component';
import { configTimeZone } from '../../../environments/environment';
import { ToolTipService } from '../../structure/tool-tip.service';
import { IntakeService } from '../../structure/intake/intake.service';
import { ActiveMessageData, CreateChatroomResponse } from './top-bar.interface';
import { MessageService } from 'app/services/message-center/message.service';
import { OktaAuthService } from '@okta/okta-angular';
import { HttpService } from 'app/services/http/http.service';
import { APIs } from 'app/constants/apis';

declare const $: any;
declare const NProgress: any;
declare const swal: any;

@Component({
  providers: [BannerAlertTagComponent],
  selector: 'cat-top-bar',
  templateUrl: './top-bar.component.html',
  styleUrls: ['./top-bar.component.scss']
})
export class TopBarComponent implements OnDestroy {
  @ViewChild('element') element: ToastComponent;
  @ViewChild('admissionSearchInput') admissionSearchInput: ElementRef;
  profileImageUrl;
  userRole;
  displayname;
  rememberMe = true;
  secondTick = 0;
  timeInMinutesForSessionOut;
  timeInSecondsAfterSessionOut;
  showSelected = false;
  selectedIndex: any;
  flagstop = true;
  timeoutAfter = false;
  stoplogout = false;
  sessionSelf: any;
  timezone;
  timeoutMsg: any;
  sessionTimeout: number;
  warningTimeout: number;
  userLastActivityTimeStamp: number;
  /** Chat with data declarations */
  privileges = this._structureService.getCookie('userPrivileges');
  privilegesReplica = this._structureService.getCookie('userPrivilegesReplica');
  config = this._structureService.userDataConfig;
  userDetails = this._structureService.userDetails;
  crossTenantId = this._structureService.getCookie('crossTenantId');
  configData = JSON.parse(this.config);
  userData = JSON.parse(this.userDetails);
  userList: any;
  scheduledPrimaryCar: any;
  primaryCareClinicians: any;
  primaryClinicalUsers: any;
  usersListScheduleCheck: any = [];
  machformHistory: any = [];
  optionShow: any = 'staff';
  usersList: any;
  userGroupId;
  prevText;
  ispdgs;
  clinicianLoad: any = false;
  allowEdited = false;
  defaultNurses: any;
  maxRemoteSourcesAccomadateMessage = 'Per your network bandwidth, we can support {count} remote participants';
  maxRemoteSourcesAccomadateMessageUpdate = '';
  maxRemoteSourcesChangedStatus = false;
  userListChatwith: any = [];
  usersListByRoleId: any;
  clinicalUserDetails;
  messageGroup: any = [];
  dropdownList: any = [];
  multipleDrop: any = false;
  userId = '';
  hideLoadMore = false;
  chatWithHeading;
  column3 = false;
  column6 = false;
  loadMoreSearchValue: any = undefined;
  chatWith;
  hasChatWithGroup;
  isOtherTenantPatients = false;
  modalHead;
  domainName;
  tenantName;
  page = 0;
  limit = CONSTANTS.contentLimit;
  userGroup = UserGroup;
  otherTenantPatientsList;
  patientTopic = {
    count: localStorage.getItem('patientTopicCount')
  };
  messageGroupTopic: any = [];
  selectedGroup: any= {};
  selectedBranch;
  selectedTopic: any = {};
  chatWithUsersLoading = true;
  infoBottomOverlay = false;
  selectedItems = [];
  selectedAlternates = [];
  dropdownSettings = {};
  endVideoCall: any;
  joincall;
  allMembers = [];
  videoTiles = [];
  enableVideoButton = false;
  microphonePrivacy = false;
  videoFull = false;
  cameraPrivacy = false;
  dialogMessage = '';
  joinChat = false;
  videoSocketDisconnect;
  chatroomUsersList: any;
  allMessageGroups = [];
  currentChatroomId;
  pageHeading;
  activeMessage;
  chtRoomUsers;
  initialtorCallTune = false;
  avatarBasePath = `${this._structureService.getApiBaseUrl()}citus-health/avatars/`;
  videoChatInitialize;
  showVideoChatEvent = null;
  currentVideoChatRoomId;
  acceptCallData = null;
  hideInitiatorAudioBtn = false;
  vidyoClientSubscribe = null;
  initiatorCallTune = false;
  initiatorEnd = false;
  onVideoCall = false;
  muteInitiatorAudio;
  videoInitiatorEnd;
  videoChanged = null;
  connectorAvailable = null;
  maxRemoteSourcesChanged = null;
  rendered = null;
  isVideoChatMinimized = false;
  allowVideoChat;
  UsergroupIds;
  videoChatRoomId;
  socketDisconnect;
  connectionMessage = 'Connecting';
  initialLoad = true;
  selectedTenant: any;
  crossTeneantLoadingOption = true;
  crossTenantOptions = [];
  crossTenantOptionsChatWith = [];
  chatWithSubs = null;
  sessionRefreshSubs = null;
  newMessageGroupSubs = null;
  userDataUpdateSubs = null;
  crossTenantChangeSubs = null;
  reloadSubscriber = null;
  videoCallEndSubs = null;
  crossTenantName: string;
  chatWithTenantFilterDetails: any;
  otherTenantStaffList = [];
  otherTenantClinicianDataTenantWise = [];
  selectSiteId;
  masterData;
  masterConfigData;
  masterSchedulerData;
  masterEscalatedSchedulerData;
  masterStaffsAvalable;
  defaultMasterStaffsAvalable;
  masterClinicianRolesAvaiable = '';
  masterDefaultClinicianRolesAvaiable = '';
  chatroomUserCount = [];
  chatWithLoader: any = {
    groups: true,
    topics: true,
    staffs: true,
    otherTenantstaff: true,
    patients: true,
    otherTenantPatients: true
  };
  clinicianRolesAvaiable = null;
  chatWithModalShown = false;
  callFromInitialCountChatWithUserlist = false;
  noMoreItemsAvailable = {
    users: false
  };
  chatwithPageCount = {
    staffs: 0,
    patients: 0,
    partner: 0
  };
  loadMoremessage = {
    users: 'Load more'
  };
  chatWithTenantFilter: any = {
    selectedTenant: null,
    tenants: [],
    filterEnabled: false,
    enabledReset: false
  };
  organizationSwitchPrivilege = false;
  internalSiteId = null;
  validVidyoTokenSubs = null;
  mobileoremailverification = null;
  vidyoCallRetryCount = 0;
  groups = [];
  inviteUserTiles = [];
  importDownloadUrl = '';
  position: ToastPositionModel = { X: 'Right', Y: 'Top' };
  reloadConfigSubs: ISubscription;
  hideSiteSelection: boolean;
  eventsSubject: Subject<void> = new Subject<void>();
  tabSelectedData: any;
  showTenantLogo: boolean;
  imgUrl: string;
  emitToTopBar: any;
  multiSiteEnable: boolean;
  enableCrossSite: boolean;
  userRoleList: any = [];
  associatedPatientId: any;
  noStaffsAvailable: any;
  preventMultipleCall = false;
  siteSelected;
  dynamic = false;
  preventMultipleCallBranchSwitch: boolean;
  doubleVerificationStatus = false;
  changeTab: boolean;
  previoustabSelected: any;
  currentPath = '';
  loadMoreFlag = false;
  tabChange = false;
  chatRoomUsers = [];
  userTypes = CONSTANTS.userTypes;
  boundHandler;
  msgGrpSubjectOffset = CONSTANTS.contentOffset;
  searchComponentSettings: SearchComponentSettings;
  searchComponentGroupSettings: SearchComponentSettings;
  resetSearchSelect: boolean;
  totalSubjectCount = 0;
  private socketEventSubscriptions: Subscription[] = [];
  searchApplied = false;
  initiateChatPermission = false;
  selectedStaff;
  requestParams:  UserListParams;
  formattedMessageList = [];
  totalMessageCount = 0;
  showSubject = false;
  showChatBtn = false;
  resetSearchSelectMsg: boolean;
  searchPdgSiteIds = '';
  selectedAdmissionId = '';
  userSiteIds = 0;
  selectedPatientId = '';
  admissionsList = [];
  admissionsListPage = 0;
  showAdmissionsList = false;
  admissionsLoading = false;
  associatedUserId = '';
  admissionsSearchText = '';
  totalAdmissionsCount = 0;
  showTutorialButton = false;
  tutorialLink = '';
  /** End Section */
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private _ToolTipService: ToolTipService,
    public _structureService: StructureService,
    private _worklistService: WorkListService,
    public _inboxService: InboxService,
    public modalFilter: userFilterPipe,
    public SearchFilter: SearchFilterPipe,
    public datePipe: DatePipe,
    public _SharedService: SharedService,
    public _commonVideoService: CommonVideoService,
    private renderer2: Renderer2,
    public _GlobalDataShareService: GlobalDataShareService,
    private _crossTenantService: CrossTenantService,
    private _ngZone: NgZone,
    elementRef: ElementRef,
    private idle: Idle,
    renderer: Renderer,
    @Inject(DOCUMENT) private _document: Document,
    private logger: NGXLogger,
    public permissionService: PermissionService,
    public _intakeService: IntakeService,
    private notifyService: NotifyService,
    private messageService: MessageService,
    public oktaAuth: OktaAuthService,
    private httpService: HttpService
  ) {
    // Added event listener for visibilitychange
    if (!this.boundHandler) {
      this.boundHandler = this.handleVisibilityChange.bind(this);
      document.addEventListener('visibilitychange', this.boundHandler);
    }
    this.timezone = configTimeZone();
    if (this._structureService.userDetails) {
      this.userDetails = this._structureService.userDetails;
      this.userData = JSON.parse(this.userDetails);
    }
    this.userTypes = CONSTANTS.userTypes;
    // Listen the click after the popup and set the user is active
    renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
      if (($('#sessionout').data('bs.modal') || {})._isShown) {
        $('#sessionout').modal('hide');
        this.trackCancelActivity();
      }
    });

    router.events.subscribe((event) => {
      $('#intro-btn').attr('hidden', true);
      this._structureService.introJsBtnHidden = true;
      if (event instanceof NavigationEnd) {
        if (event.url === '/blank') {
          this.router.navigate(['/inbox/chatroom']);
        }
      }
    });

    this.activateNow();

    if (isBlank(this.currentVideoChatRoomId) || (this.currentVideoChatRoomId && this.currentVideoChatRoomId.closed)) {
      this.currentVideoChatRoomId = this._SharedService.currentVideoChatRoomId.subscribe((data) => {
        this.currentChatroomId = data.chatRoomId;
        this.pageHeading = data.pageHeading;
        if (localStorage.getItem('activeMessage')) {
          this.activeMessage = JSON.parse(localStorage.getItem('activeMessage'));
        }
      });
    }

    if (isBlank(this.videoChatInitialize) || (this.videoChatInitialize && this.videoChatInitialize.closed)) {
      this.videoChatInitialize = this._SharedService.videoChatInitialize.subscribe(() => {
        this.initializeVideoChat();
      });
    }

    if (isBlank(this.acceptCallData) || (this.acceptCallData && this.acceptCallData.closed)) {
      this.acceptCallData = this._SharedService.acceptCallData.subscribe((data) => {
        // Executes while incoming call arrives
        // For video call Endsubscribe activate
        this.vidyoCallEndSubscribe();
        this.videoChatRoomId = data.roomId;
        this.chatroomUsersList = data.chatroomUsersList;
        this.pageHeading = data.heading;
        this.joinChat = true;
        this.activeMessage = data.activeMessage;
        this.initializeVideoChat();
      });
    }

    if (isBlank(this.socketDisconnect) || (this.socketDisconnect && this.socketDisconnect.closed)) {
      this.socketDisconnect = this._SharedService.videoChatDisconnect.subscribe(() => {
        if (document.getElementById('video_container') && document.getElementById('video_container').style.visibility === 'visible') {
          const videoChatRoomDetails = this._GlobalDataShareService.getVideoChatDetails();
          if (videoChatRoomDetails.initiator != null) {
            this.chatroomUsersList = videoChatRoomDetails.initiator.chatroomUsersList;
            const emitData = videoChatRoomDetails.initiator;
            emitData.reconnect = true;
            this._structureService.socket.emit('videoChatInitiate', emitData);
          }

          if (videoChatRoomDetails.joinee != null) {
            const emitData = videoChatRoomDetails.joinee;
            emitData.reconnect = true;
            this._structureService.socket.emit('videoChatJoin', emitData);
          }
        }
      });
    }

    if (isBlank(this.showVideoChatEvent) || (this.showVideoChatEvent && this.showVideoChatEvent.closed)) {
      this.showVideoChatEvent = this._SharedService.showVideoChat.subscribe((data) => {
        // For video call Endsubscribe activate
        this.vidyoCallEndSubscribe();
        this._structureService
          .createVideoRoom({ chatroomId: data.chatRoomId })
          .then((roomdata: any) => {
            roomdata = roomdata.createVidyoRoom;
            this._SharedService.applessVideoChatroomDetails = {
              roomKey: roomdata.roomKey,
              roomPin: roomdata.roomPin,
              host: roomdata.host,
              roomName: roomdata.roomName,
              roomEntity: roomdata.roomEntity
            };
            if (roomdata.roomKey && roomdata.roomName) {
              if (this._SharedService.applessVideo) {
                const params = {
                  chatroomId: data.chatRoomId,
                  roomKey: roomdata.roomKey,
                  roomPin: roomdata.roomPin,
                  host: roomdata.host,
                  roomName: roomdata.roomName,
                  roomEntity: roomdata.roomEntity,
                  chatroomPatientSiteId: data.chatroomPatientSiteId
                };
                this._structureService.sendApplessData(params).then((response: any) => {
                  this._SharedService.videoId = response.sendApplessData && response.sendApplessData.videoId ? response.sendApplessData.videoId : 0;
                });
              }
              let deeplinking: any = {};
              this.chatroomUsersList = data.chatRoomList;
              this.pageHeading = data.pageHeading;
              this.videoChatRoomId = data.chatRoomId;
              this.currentChatroomId = data.chatRoomId;
              this.joinChat = data.joinChat;
              this.activeMessage = data.activeMessage;
              this._SharedService.callInitiatedRoomId = data.joinChat == true ? '' : data.chatRoomId;
              this.allowVideoChat = data.allowVideoChat;
              this._SharedService.onVideoChat = true;
              this.showVideoChat(roomdata);
              if (!this.joinChat) {
                const users = [];
                const usersForPush = [];
                for (let i = 0; i < this.chatroomUsersList.length; i += 1) {
                  if (this.chatroomUsersList[i].userId != this.userData.userId) {
                    users.push(this.chatroomUsersList[i]);
                    usersForPush.push({ userid: this.chatroomUsersList[i].userId });
                  }
                  if (i === this.chatroomUsersList.length - 1) {

                    if (!this.joinChat) {
                      const emitData = {
                        roomData: roomdata,
                        reconnect: false,
                        users: users,
                        roomId: this.videoChatRoomId,
                        from: this.userData.displayName,
                        fromId: this.userData.userId,
                        heading: this.pageHeading,
                        activeMessage: JSON.stringify(this.activeMessage),
                        chatroomUsersList: this.chatroomUsersList,
                        applessVideo: this._SharedService.applessVideo
                      };
                      this._GlobalDataShareService.setVideoChatDetails(emitData, 'initiator');
                      if (this._structureService.socket.connected) {
                        this._structureService.socket.emit("videoChatInitiate", emitData);
                        deeplinking = {
                          state: 'eventmenu.group-chat',
                          stateParams: {
                            targetID: data.chatRoomId,
                            targetName: 'group-chat'
                          },
                          activeMessage: {
                            sent: data.activeMessage.sent,
                            messageType: data.activeMessage.messageType || 0,
                            baseId: data.activeMessage.baseId || 0,
                            userid: this.userData.userId,
                            fromName: `"${this.userData.displayName}"`,
                            message_group_id: data.activeMessage.message_group_id || 0,
                            createdby: data.activeMessage.createdby || 0
                          }
                        };
                        if (data.activeMessage.message_group_id && data.activeMessage.groupName) {
                          deeplinking.activeMessage.chatWithHeading = data.activeMessage.groupName;
                        }
                        if (!this._SharedService.applessVideo) {
                          const notificationData = {
                            sourceId: CONSTANTS.notificationSource.message,
                            sourceCategoryId: CONSTANTS.notificationSourceCategory.videoCallNotification
                          };
                          const eventId = roomdata.roomKey || '';
                         this._structureService.sentPushNotification(
                            usersForPush, 0, 'Incoming video call from {{tenantName}}. Tap to open the App and Join Call.', undefined, deeplinking, '', '', false, true, MessagePriority.NORMAL, eventId, notificationData );
                        }
                      } else {
                        this._structureService.notifyMessage({
                          messge: 'Unexpected issue with initiating the video call. Please try again',
                          type: 'warning'
                        });
                      }
                    } else {
                      const emitData = { reconnect: false, joineeId: this.userData.userId, joineeName: this.userData.displayName, roomId: this.videoChatRoomId };
                      this._GlobalDataShareService.setVideoChatDetails(emitData, 'joinee');
                      if (this._structureService.socket.connected) {
                        this._structureService.socket.emit('videoChatJoin', emitData);
                      } else {
                        this._structureService.notifyMessage({
                          messge: 'Unexpected issue with initiating the video call. Please try again',
                          type: 'warning'
                        });
                      }
                    }
                  }
                }
                this.chtRoomUsers = users;
              }
              if(this.joinChat){
                const emitDataJoinee = { joineeId: this.userData.userId, joineeName: this.userData.displayName, roomId: data.chatRoomId };
                this._structureService.socket.emit("videoChatJoin", emitDataJoinee);
              }
            } else {
              this.notifyCustomErrorMessage('Video room creation failed.');
              this._SharedService.enableVideoChatButton.emit(true);
            }
          })
          .catch(() => {
            this.notifyCustomErrorMessage('Video room creation failed.');
            this._SharedService.enableVideoChatButton.emit(true);
          });
      });
    }

    if (isBlank(this.joincall) || (this.joincall && this.joincall.closed)) {
      this.joincall = this._SharedService.joincall.subscribe(() => {
        this.joinChat = true;
     });
    }

    if (isBlank(this.videoCallEndSubs) || (this.videoCallEndSubs && this.videoCallEndSubs.closed)) {
      this.videoCallEndSubs = this._SharedService.videoCallEnd.subscribe((userData) => {
        if (!this._SharedService.applessVideo) {
          this.nouserOnlineSwal(userData.data.title);
        }
        this._SharedService.inviteUserVideoTile.emit({ title: userData.data.title });
      });
    }
    this.socketEventSubscriptions.push(
        this._structureService.subscribeSocketEvent('validVidyoToken').subscribe((data) => {
        this._commonVideoService.token = data.token;
        if (data.from && data.from.source === 'topbar') {
            this.connectVideo();
        }
        })
    );
    this.socketEventSubscriptions.push(
        this._structureService.subscribeSocketEvent('userImportCompleted').subscribe((data) => {
        this.importDownloadUrl = data.data.fileUrl;
        this.element.show();
        })
    );

    if (isBlank(this.videoInitiatorEnd) || (this.videoInitiatorEnd && this.videoInitiatorEnd.closed)) {
      this.videoInitiatorEnd = this._SharedService.initiatorReload.subscribe((data) => {
        swal({
            title: `Unfortunately ${data.userName} left`,
            text: 'Do you want to continue call with other(s)?',
            type: 'warning',
            showCancelButton: true,
            cancelButtonClass: 'btn-default',
            confirmButtonClass: 'btn-warning',
            confirmButtonText: 'No',
            cancelButtonText: 'Yes',
            closeOnConfirm: true
          },
          (isConfirm) => {
            if (isConfirm) {
              document.getElementById('disconct-btn').click();
            }
          }
        );
      });
    }

    if(isBlank(this.crossTenantChangeSubs) || (this.crossTenantChangeSubs && this.crossTenantChangeSubs.closed)) {
      this.crossTenantChangeSubs = this._SharedService.crossTenantChange.subscribe((onInboxData) => {
        this._SharedService.configuringTenantData = true;
        if (onInboxData.tenantdataconfigured) {
          this._SharedService.configuringTenantData = false;
          this.unsubscInitSubscriptions();
          this.ngOnInit();
        }
      });
    }

    if (isBlank(this.reloadSubscriber) || (this.reloadSubscriber && this.reloadSubscriber.closed)) {
      this.reloadSubscriber = this._SharedService.reloadOnConfigChange.subscribe(() => {
        this.config = this._structureService.userDataConfig;
        this.userDetails = this._structureService.userDetails;
        this.configData = JSON.parse(this.config);
        this.userData = JSON.parse(this.userDetails);
        this.ngOnInit();
      });
    }
    this.socketEventSubscriptions.push(
        this._structureService.subscribeSocketEvent('activeEditFormsResponse').subscribe((msg) => {
        if (msg.concurrentEditing == true) {
            this._structureService.notifyMessage({
            messge: 'This form is being editted by another user now!!!!!!!',
            type: 'danger',
            allow_dismiss: true,
            delay: 0
            });
        }
        })
    );
    if (isBlank(this.validVidyoTokenSubs) || (this.validVidyoTokenSubs && this.validVidyoTokenSubs.closed)) {
      this.validVidyoTokenSubs = this._SharedService.validVidyoToken.subscribe((data) => {
        this._commonVideoService.token = data.token;
        if (data.from && data.from.source === 'topbar') {
          this.connectVideo();
        }
      });
    }
    if (isBlank(this.mobileoremailverification) || (this.mobileoremailverification && this.mobileoremailverification.closed)) {
      this.mobileoremailverification = this._SharedService.mobileoremailverification.subscribe((value) => {
        this.verifyEmailorMobile(value);
      });
    }
    this._SharedService.inviteUserVideoTile.subscribe((data) => {
      if (!isBlank(data.inviteTileData)) {
        this.chatroomUsersList = data.inviteTileData.chatroomUsersList;
        this.chtRoomUsers = [];
        this.chatroomUsersList.forEach((user) => {
          if (+user.userId !== +this.userData.userId) {
            this.chtRoomUsers.push(user);
          }
        });
        this.inviteUserTiles = data.inviteTileData.users;
      }
      if (data.joineeId || data.rejectUser) {
        this.inviteUserTiles.map((title) => {
          if (data.rejectUser && title.userid.toString() === data.rejectUser.toString()) {
            title.viewTile = false;
          } else if (data.joineeId && +title.userid === +data.joineeId) {
            title.message = 'Wait for user ';
          }
          return title;
        });
      } else if (data.title === 'No users online') {
        this.inviteUserTiles = [];
      } else if (data.action === 'initiatorClose') {
        this.inviteUserTiles = [];
      } else if (data && data.type && data.type === 'removeTile' && this.inviteUserTiles.length > 0) {
        const removeIndex = this.inviteUserTiles.findIndex((tile) => +tile.userid === +data.userId);
        if (removeIndex !== -1) {
          this.inviteUserTiles[removeIndex].viewTile = false;
        }
      } else if (data && data.userList) {
        if (this.inviteUserTiles == undefined) {
          this.inviteUserTiles = [];
        }
        this.inviteUserTiles = data.userList;
      }
    });
    this.socketEventSubscriptions.push(
        this._structureService.subscribeSocketEvent('userNotOnline').subscribe((data) => {
        this._SharedService.inviteUserVideoTile.emit({ joineeId: data.userId });
        })
    );
    this.socketEventSubscriptions.push(
        this._structureService.subscribeSocketEvent('callRejectedRemoveInviteTile').subscribe((data) => {
        if (this.inviteUserTiles && this.inviteUserTiles.length > 0) {
            const removedUserIndex = this.inviteUserTiles.findIndex((user) => +user.userid === +data.rejectUser);
            if(removedUserIndex !== -1){
            this.inviteUserTiles.splice(removedUserIndex, 1);
            }
        }
        })
    );
    if (!this.reloadConfigSubs) {
      this.reloadConfigSubs = this._SharedService.reloadConfig.subscribe((userData) => {
        this.userData = userData;
        this.configData = userData.config;
      });
    }
    if (isBlank(this.emitToTopBar) || (this.emitToTopBar && this.emitToTopBar.closed)) {
      this.emitToTopBar = this._SharedService.emitToTopBar.subscribe(() => {
        this.emitEventToSelectSites('dynamicSiteUpdate');
        setTimeout(() => {
          const userSiteDetails = JSON.parse(this._structureService.userDetails);
          if (userSiteDetails.mySites.length === 0) {
            this.logout();
        }
          this._crossTenantService.setInboxData(true);
          this.tenantName = userSiteDetails.tenantName;
          if(userSiteDetails.mySites.length == 1){
            this.tenantName = userSiteDetails.mySites[0].name;
          }
          if (+userSiteDetails.group === UserGroup.PATIENT) {
            this.tenantName = userSiteDetails.mySites[0].name;
          }
        }, 3000);
      });
    }
    this.searchComponentSettings = {
      allowLazyLoading: true,
      enableTagging: true,
      lazyLoadType: ScrollType.INFINITE,
      fieldName: 'LABELS.SUBJECT',
      placeHolderText: 'PLACEHOLDERS.SEARCH_CHOOSE_SUBJECT'
    };
  }

  // video call End  subscribe
  vidyoCallEndSubscribe() {
    if (isBlank(this.endVideoCall)) {
        this.endVideoCall = this._SharedService.endVideoCall.subscribe(
            (data) => {
                console.log('End video call ---------------', data);
                if (data.action == 'endcall') {
                    var notify = $.notify('<strong> Call ended by ' + data.initiatorName + '</strong>');
                    setTimeout(() => {
                        notify.update({ 'type': 'success', 'message': '<strong> Call ended by ' + data.initiatorName + '</strong>' });
                    }, 1000);

                    var activityData = {
                        activityName: "Ending Video Chat",
                        activityType: "video communication",
                        activityLinkageId: this.videoChatRoomId,
                        activityDescription: data.initiatorName + " (" + data.initiator + ") (initiator) disconnected video call from chat room " + this.videoChatRoomId
                    };

                    this._structureService.trackActivity(activityData);
                    this.initiatorEnd = true
                    this.joinChat = false;
                    localStorage.removeItem('videoChatData');
                    this.disconnect();
                }

            }
        );
    }

  }
  //

  //Video call End unsubscribe 
  vidyoCallEndUnSubscribe(){
    console.log("vidyoCallEndUnSubscribe fucntion called");
    if(this.endVideoCall) {
        console.log("inside vidyoCallEndUnSubscribe");
        this.endVideoCall.unsubscribe();
        this.endVideoCall=null;
    }
  }
 //

   emitEventToSelectSites(status): void {
    this.eventsSubject.next(status);
    this.selectSiteId = '0';
    if(this.multiSiteEnable) {
        const userData = JSON.parse(this._structureService.userDetails);
        this.selectSiteId = getUserPreferenceSiteIds(userData.defaultSitesFilter);
    }
    }
   getSiteIds(data:any){
    this.selectSiteId = data['siteId'].toString();
    this.searchPdgSiteIds = this.selectSiteId;
    this._SharedService.chatroomSiteId = this.selectSiteId;
    this.previoustabSelected = '';
    if(this.optionShow === CONSTANTS.userTypes.staff || this.optionShow === CONSTANTS.userTypes.partner || this.optionShow === CONSTANTS.userTypes.patient || this.optionShow === 'groups') {
        this.initialiseStaffOrPatientList(this.optionShow);
    }
    this.preventMultipleCall = true;
    this.chatRoomUsers = [];//Reset on site change
   }
    hideDropdown(hideItem : any){
        this.hideSiteSelection = hideItem.hideItem;
    }
    closeModal() {
        this.showChatBtn = false;
        this.showSubject = false;
        this.resetSearchSelect = true;
        this.resetSearchSelectMsg = false;
        this.clearData();
        this.emitEventToSelectSites('closePopup');
    }
    /* Track Activity for the "Click To Cancel" Button */
    trackCancelActivity(){
        var activityData = {
            activityName: "Click to cancel",
            activityType: "Session Expiry",
            activityDescription: this.userData.displayName + " has clicked the Click To ancel button to avoid expiring the session"
        };        
        this._structureService.trackActivity(activityData);
        this.flagstop = true;
        this.idle.watch();
    }
   nouserOnlineSwal(title){
    swal({
        title: title,
        text: "Would you like to wait or cancel the call?",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Cancel",
        cancelButtonText: "Wait",
        closeOnConfirm: true
    },
    (isConfirm) =>{
        if (isConfirm) {
            this.disconnect();
        }
    });
   }
   verifyEmailorMobile(params){
        var paramsData:any = {
            userId: this.userData.userId,
            tenantId: this.userData.tenantId,
            environment:this._structureService.getEnvironment(),
            serverBaseUrl:this._structureService.serverBaseUrl,
            category: 'verifyCellOrEmail',
            apiVersion:this._structureService.getVersion()
        }
        if(params.mobile){
            paramsData.mobile =true;
            paramsData.code=params.code;
            paramsData.mobileNumber = params.mobileNumber;
            paramsData.countryCode =params.countryCode;
            this._structureService.socket.emit("verifyEmailOrMobile", paramsData);
            var fiveMinutes = 60 * 5;

        }else if(params.email){
            paramsData.email =true;
            paramsData.code=params.code;
            paramsData.emailId = params.emailId;
            this._structureService.socket.emit("verifyEmailOrMobile", paramsData);
        }
   }

    formatAMPM() {
    var date=new Date;
    var hours = date.getHours();
    var minutes = date.getMinutes();
    var ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    let minute = minutes < 10 ? '0'+minutes : minutes;
    var strTime = hours + ':' + minute + ' ' + ampm;
    
    
    var year=date.getFullYear();
    var month=(date.getMonth()+1).toString().length==1? "0"+(date.getMonth()+1):(date.getMonth()+1);
    var todaydate=(date.getDate()).toString().length==1? "0"+date.getDate():date.getDate();
    let LocalDateTime=todaydate+'-'+month+'-'+year+' '+strTime;
    return LocalDateTime;
  }
convertToFormatedDateTime(timestamp){

    
   
    // Months array
    var months_arr = ['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'];
   
    // Convert timestamp to milliseconds
    var date = new Date(timestamp*1000);
   
    // Year
    var year = date.getFullYear();
   
    // Month
    var month = months_arr[date.getMonth()];
   
    // Day
    var day = date.getDate();
   
    // Hours
    var hours = date.getHours();
   
    // Minutes
    var minutes = "0" + date.getMinutes();
    var ampm = hours >= 12 ? 'PM' : 'AM';
    hours = hours % 12;
    hours = hours ? hours : 12; // the hour '0' should be '12'
    // Seconds
    var seconds = "0" + date.getSeconds();
   
    // Display date time in MM-dd-yyyy h:m:s format

    var convdataTime = month+' '+day+' '+year+' '+hours + ':' + minutes.substr(-2)+' '+ampm;

   
    
   return convdataTime;
    
   }
   getMachformActivityData(form_id){
    $(".historyBody").empty();
    $(".historyBody").append('<div>Loading... </div>');
    $('#historyWithModel').modal('show'); 
    if(form_id){
         this._structureService.getMachformActivityData(form_id).then((response) => {
            this.machformHistory=[];
            this.machformHistory=response;
            if(this.machformHistory.length){
                let htmlData='';
                htmlData+='<table class="table table-striped"><tbody>'; 
                const reversedKeys = Object.keys(this.machformHistory).reverse(); // For descending sort
                reversedKeys.forEach((key, index) => {
                console.log(`${key}:${this.machformHistory[key]}`);
                let formatedTime= this.convertToFormatedDateTime(this.machformHistory[key].created_at); 
                htmlData+='<tr><th scope="row">'+(parseInt(key) + 1)+'</th><td>'+this.machformHistory[key].user+' '+this.machformHistory[key].activity_name+' on '+formatedTime+'</td></tr>';
                    
               });
               htmlData+='</tbody></table>';
                $(".historyBody").empty();
                $(".historyBody").append(htmlData);
            }else{
                $(".historyBody").empty();
                $(".historyBody").append('<div>No Activity Data Found..</div>');  
            }
         });   
       }
   }
   setheight (event) {
    try {
      if (event.data) {
       
        if(event.data["MachFormHistory"]){
               this.machformHistory=[];
               this.getMachformActivityData(event.data["MachFormHistory"]);
                    
        }
        else if(event.data["activeEditForm"]){
            this._structureService.activeEditFormId=event.data["activeEditForm"];
            this._structureService.socket.emit("activeEditForms", { userId: this.userData.userId , formId: event.data["activeEditForm"],actionType:'insert'});
              
        }else if(event.data["manageFormsPopUp"]){
            if(this._structureService.activeEditFormId&& this._structureService.activeEditFormId!=''){
                this._structureService.socket.emit("activeEditForms", { userId: this.userData.userId , formId: this._structureService.activeEditFormId,actionType:"delete"});
                this._structureService.activeEditFormId='';
                
              }
        }else if(event.data["AllowEditFormSubmitted"] && event.data["AllowEditFormSubmitted"]==true){
             $("#textmessage").text("");
             $("#newloader").hide();
               this._structureService.allowEditFormSuccess=true;
               if(this._structureService.allowEditFrom && this._structureService.allowEditFrom=='/forms/list'){
                   this._structureService.allowEditFrom='';
                   this.router.navigate(['/forms/list']);
               }else if(this._structureService.allowEditFrom !=='pah') {
                   this.router.navigate(['/forms/worklist']);
               }
        } else if(event.data=='!_{"h":""}'){
        console.log('-----pdfIframeLoaded--true');
        this._structureService.pdfIframeLoaded = true;
        this._structureService.pdfLoaded = true;
     } else{
          console.log("My event Data",event.data);
          if (typeof event.data === 'string') {
          var height = (event.data).split("=");
          if(height.length&&height[0]=="scrollTopformsubmit"){
            console.log("6666666666666");
            console.log($('.structured-form-modal').scrollTop())
            localStorage.setItem('scrollTopback',$('.structured-form-modal').scrollTop());
            localStorage.setItem('autosavecalledformid',"0");
            localStorage.setItem('autosavecalled',"false");
            $(".structured-form-modal").scrollTop(0);
            console.log('notify when submit*****************************');
            window.scrollTo(0, 0);
          } else if(height.length&&height[0]=="internetConnectionerror"){
            $(".structured-form-modal").scrollTop(0);
            window.scrollTo(0, 0);
          }
          else if(height.length&&height[0]=="gotocreatesendform"){
            console.log("5555555555555");
           $(".searchbar input").val("");
           $(".send-form-list ").find('.formname').first().trigger('click');
          }else if(height.length&&height[0]=="autosavecalled"){
                localStorage.setItem('autosavecalled', height[1]);
               console.log("4444444");

          }else if(height.length&&height[0]=="autosavecalledformid"){
            console.log("33333333333");
            localStorage.setItem('autosavecalledformid', height[1]);
         }
         else if (height.length && height[0] == "saveasdraftidtodeleted") {
            console.log("ccccccccccccccccccccccccccccc");
            localStorage.setItem('saveasdraftidtodeleted', height[1]);
          }
          else if (height.length && height[0] == "saveAsDraftCall") {
            console.log("ccccccccccccccccccccccccccccc");
            $("#textmessage").text("Saving Draft....");
            $("#newloader").show();
           
          }
          else if (height.length && height[0] == "saveAsDraftCallCompleted") {
            $("#textmessage").text("");
            $("#newloader").hide();
            // save as draft msg
            var downloadTime = moment((moment().unix()) * 1000).format('h:mm a');
            var downloadDateTime = moment((moment().unix()) * 1000).format('MM-DD-YYYY h:mm a');
            localStorage.setItem('saveasdraftCompleted', height[1]);
            $(".saveAsDraft-message").removeClass('faildraft');
            $(".saveAsDraft-message").html("This form was Saved as Draft at "+downloadTime+" and can also be found in the Drafts category of your Form Worklist.");
            $("#last_save_draft").val(downloadDateTime);
            // save as draft msg
            
          }else if (height.length && height[0] == "loaderOnSubmitForm") {
            console.log("ccccccccccccccccccccccccccccc");
            $("#textmessage").text("Submitting the Form. Please wait...");
            $("#newloader").show();
            // save as draft msg
            $(".saveAsDraft-message").html("");
            // save as draft msg
           
          }
          else if (height.length && height[0] == "hideFormSubmitLoader") {
            $("#textmessage").text("");
            $("#newloader").hide();
            
          }
          else if(height.length && height[0] == "formSendLoader"){
            $("#textmessage").text("Sending the Form. Please wait...");
            $("#newloader").show();
          }
          else if(height.length && height[0] == "formSendLoaderRemove"){
            $("#textmessage").text("");
            $("#newloader").hide();
           this._SharedService.formSubmissionEvent.emit(true);
          }else if(height.length && height[0] == "dosUpdate"){
            if($(".dosUpdate").length && typeof height[1]!='undefined' && height[1]){
               
                $(".dosUpdate").text(decodeURI(height[1]));

            }
            
          }
          else if(height.length && height[0] == "formCorruptionError"){
            $("#btm-send-form-list").prop("disabled", true);
          }
          else if(height.length&&height[0]=="saveasdraftCompleted"){
            console.log("111111111111");
             // save as draft msg
             var downloadTime = moment((moment().unix()) * 1000).format('hh:mm A');
             var downloadDateTime =  moment((moment().unix()) * 1000).format('MMM DD hh:mm A');
             localStorage.setItem('saveasdraftCompleted', height[1]);
       }
       else if(height.length&&height[0]=="scrollTopformsubmitback"){
        console.log("111111111111");
        $('.structured-form-modal').scrollTop(localStorage.getItem('scrollTopback'));
        }
         else {
           if(height[0]!="saveasdraftCompleted"&&height[0]!="saveasdraftidtodeleted"&&height[0]!="autosavecalledformid") {
            if (typeof (parseInt(height[1])) == "number") {
                console.log("222222222222222222222222"+height[0]);
                $("#structured-form-dekstop").height(height[1] + "px")
              } else {
                $("#structured-form-dekstop").height("5000px")
              }
           }
     
          $('#btm-send-form-list').removeAttr('disabled');
        }
        }
    }

   
      }
    } catch (error) {
      console.log(error);
    }
  }
 




    showdraft (event) {
    try {
      if (event.data) {
       
            console.log(event.data);
            var height = (event.data).split("=");
            console.log(height);
            console.log(height[0]);
            if(height.length&&height[0]=="showDraftMessage"){
            var data ={
                messge: 'It is a good practice to save your work frequently. Please click Save As Draft to save your work and avoid any data loss if your session times out.',
                delay: 7500,
                type: 'warning'
          
              };
                let notify = $.notify(data.messge,{delay:10000});
                let type = data.type ? data.type : 'danger';
                let deley = data.delay ? data.delay : 1000;
                setTimeout(function () {
                  notify.update({ 'type': type, 'message': '<strong>' + data.messge + '</strong>' });
                }, deley);
            }
      }
    } catch (error) {
      console.log(error);
    }
  }

  showmessage(){
    var data ={
      messge: 'It is a good practice to save your work frequently. Please click Save As Draft to save your work and avoid any data loss if your session times out.',
      delay: 100000,
      type: 'success'

    };
      let notify = $.notify(data.messge);
      let type = data.type ? data.type : 'danger';
      let deley = data.delay ? data.delay : 1000;
      setTimeout(function () {
        notify.update({ 'type': type, 'message': '<strong>' + data.messge + '</strong>' });
      }, deley);
  }
   ngOnDestroy() {
    this.unsubscConstructorSubscriptions();
    this.unsubscInitSubscriptions();
    if (this.boundHandler) {
        document.removeEventListener('visibilitychange', this.boundHandler);
    }
    if(this._structureService.currentUrlNow == "/login"){
        $(".telemetryAgentFeedbackLabel.centerRight").removeClass("enabled");
    } 
    window.removeEventListener("message", this.setheight);
    this._commonVideoService.disconnect(true);
    this._SharedService.pushNotification = false;
    console.log('ngondestroy -- this.allowVideoChat---',this.allowVideoChat,)
    console.log('ngondestroy -- this.onVideoCall----',this.onVideoCall)
    /**Unsubscribe all the socket event subscriptions */
    this.socketEventSubscriptions.forEach(subscription => {
        if(subscription) subscription.unsubscribe();
    });
    if(this.allowVideoChat == false && this.onVideoCall == true){
        console.log('participant local storage ------',localStorage.getItem('participant'))
        if (localStorage.getItem('participant') != "true") {
            console.log("initiator reloaded emitting ------- videoChatReInitialize")
            this._structureService.socket.emit("videoChatReInitialize", { initiator: this.userData.userId ,action:'initiator', users: this.chtRoomUsers, roomId:this.videoChatRoomId });
            this._SharedService.playAudioForCall.emit({"playSound":false,"action":""});
            this.onVideoCall = false;
            this._SharedService.callInitiatedRoomId = ''
        }
    }
}

    unsubscConstructorSubscriptions() {
        console.log("unsubscribe called");       
        if(this.endVideoCall) {
            this.endVideoCall.unsubscribe();
            this.endVideoCall=null;
        }
        if(this.joincall) {
            this.joincall.unsubscribe();
        }
        if(this.crossTenantChangeSubs) {
            this.crossTenantChangeSubs.unsubscribe();
        }
        if(this.reloadSubscriber) {
            this.reloadSubscriber.unsubscribe();
        }
        if(this.videoCallEndSubs) {
            this.videoCallEndSubs.unsubscribe();
        }
        if(this.videoInitiatorEnd) {
            this.videoInitiatorEnd.unsubscribe();
        }
        if(this.socketDisconnect) {
            this.socketDisconnect.unsubscribe();
        }
        if(this.showVideoChatEvent) {
            this.showVideoChatEvent.unsubscribe();
        }
        if(this.acceptCallData) {
            this.acceptCallData.unsubscribe();
        }
        if(this.videoChatInitialize) {
            this.videoChatInitialize.unsubscribe();
        }
        if(this.currentVideoChatRoomId) {
            this.currentVideoChatRoomId.unsubscribe();
        }
        if(this.emitToTopBar) {
            this.emitToTopBar.unsubscribe();
        }
    }
    unsubscInitSubscriptions() {
        if(this.muteInitiatorAudio) {
            this.muteInitiatorAudio.unsubscribe();
        }
        if(this.chatWithSubs) {
            this.chatWithSubs.unsubscribe();
        }
        if(this.sessionRefreshSubs) {
            this.sessionRefreshSubs.unsubscribe();
        }
        if(this.newMessageGroupSubs) {
            this.newMessageGroupSubs.unsubscribe();
        }
        if(this.videoChanged) {
            this.videoChanged.unsubscribe();
        }
        if(this.connectorAvailable) {
            this.connectorAvailable.unsubscribe();
        }
        if(this.rendered) {
            this.rendered.unsubscribe();
        }
        if(this.vidyoClientSubscribe) {
            this.vidyoClientSubscribe.unsubscribe();
        }
        if(this.validVidyoTokenSubs) {
            this.validVidyoTokenSubs.unsubscribe();
        }
        if(this.mobileoremailverification) {
            this.mobileoremailverification.unsubscribe();
        }
        if(this.reloadConfigSubs) {
            this.reloadConfigSubs.unsubscribe();
        }
        if(this.maxRemoteSourcesChanged){
            this.maxRemoteSourcesChanged.unsubscribe();
        }
    }
    get oooInfo() {
        const userData = this._structureService.getUserdata();
        return userData && userData.oooInfo ? userData.oooInfo : null;
    }
    ngOnInit() {
        console.log("TOP BAR --------- : ")
    this.hasChatPermission();
    this.searchComponentGroupSettings = {
        allowLazyLoading: true,
        enableTagging: false,
        lazyLoadType: ScrollType.INFINITE,
        fieldName: 'LABELS.MESSAGE_GROUP',
        placeHolderText: 'LABELS.SEARCH_MESSAGE_GROUP'
    };
    if($("#newloader").length==0){  
    $("body").append('<div id="newloader" style="display:none;"><div id="light" class="white_content" style="position: fixed;top: 28%;left: 42%;height: auto;padding: 16px;z-index: 1050;overflow: auto; text-align:center;color:#FFF;"><img style="width:50%;" src="./assets/modules/dummy-assets/common/img/spinner.svg" alt="loading"><div><span id="textmessage" style="background: #7f7f7f;"></span></div></div><div id="fade" class="black_overlay" style="position: fixed;top: 0%;left: 0%;width: 100%;height: 100%;background-color: black;z-index: 1050;-moz-opacity: 0.5;opacity: .5;filter: alpha(opacity=80);"></div></div>');
    }
    
    this.config = this._structureService.userDataConfig;
    this.userDetails = this._structureService.userDetails;
    this.crossTenantId  = this._structureService.getCookie('crossTenantId');
    this.configData = JSON.parse(this.config);
    this.userData = JSON.parse(this.userDetails);
    this.multiSiteEnable = this.userData.config.enable_multisite == 1 ? true : false;
    this.enableCrossSite= this.userData.enable_cross_site == 1 ? true : false;

    if(!isBlank(this.userData.mySiteinfo) && !isBlank(this.userData.mySiteinfo.logo)) {
        this.showTenantLogo = false;
        this.imgUrl = this._structureService.apiBaseUrl+'citus-health/site-logos/'+this.userData.mySiteinfo.logo;
      } else {
        this.showTenantLogo = true
      }
    const authToken = this._structureService.getCookie('authenticationToken');
    var self=this;
    if(localStorage.getItem('logoutSessionOut')=="true"){
        $("#sessionoutlogin").modal("show");
        $(".sessionout-msg p").html("Your session has expired due to inactivity");
        localStorage.removeItem('logoutSessionOut');
      }
    this.flagstop=true;
    self.setSessionTimeout();
    
    var isRoleAvailable = true;
    var clinicianRolesAvaiable = null; 
    // window.addEventListener("message", this.showdraft);
    window.addEventListener("message", this.setheight.bind(this));
    if(this.userData.group === "3" && this.userData.isMaster === "0" && this.userData.master_details){
        this.masterData = this.userData.master_details;
        this.masterConfigData = this.userData.master_config;
        this.masterSchedulerData = this.userData.masterSchedulerData;
        this.masterEscalatedSchedulerData = this.userData.masterEscalatedSchedulerData;

        if (this._inboxService.checkMasterInfusionHours(false).isWorkingHours) {
            console.log('Master is Working');
            if (this.userData.master_config.clinician_roles_on_working_hours) {
                this._inboxService.getUsersListByRoleId(this.userData.master_config.clinician_roles_on_working_hours, this.userData.master_details.id, true,this.selectSiteId).then((users: any[]) => {
                    this.masterStaffsAvalable = users;
                    console.log("users", users);

                    var filteredMasterStaffs = [];
                    for (var _i = 0; _i < this.masterStaffsAvalable.length; _i++) {
                        if (this._inboxService.masterScheduleSelectionFilter(this.masterStaffsAvalable[_i])) {
                            filteredMasterStaffs.push(this.masterStaffsAvalable[_i]);
                        }
                    }
                    this.masterStaffsAvalable = filteredMasterStaffs;
          
                    let chatWithUserListData = {
                      "variableKey": "masterStaffsAvalable",
                      "data": filteredMasterStaffs
                    };
                    this._SharedService.chatWithUserListData.emit(chatWithUserListData);
                });
            }
    
            if (this.userData.master_config.default_clinician_roles_available_on_working_hour) {
                console.log(this.userData.master_config.default_clinician_roles_available_on_working_hour);
                console.log("this.userData.master_details.id", this.userData.master_details.id);
                this._inboxService.getUsersListByRoleId(this.userData.master_config.default_clinician_roles_available_on_working_hour, this.userData.master_details.id, true,this.selectSiteId).then((users1) => {
                    this.defaultMasterStaffsAvalable = users1;
                    console.log("users1", users1);
          
                    let chatWithUserListData = {
                      "variableKey": "defaultMasterStaffsAvalable",
                      "data": users1
                    };
                    this._SharedService.chatWithUserListData.emit(chatWithUserListData);
                  });
            }
          } else {
            console.log('Master is not Working');
            if (this.userData.master_config.clinician_roles_beyond_working_hours) {
                this._inboxService.getUsersListByRoleId(this.userData.master_config.clinician_roles_beyond_working_hours, this.userData.master_details.id, true,this.selectSiteId).then((users2) => {
                    this.masterStaffsAvalable = users2;
                    console.log("users2", users2);

                    var filteredMasterStaffs = [];
                    for (var _i = 0; _i < this.masterStaffsAvalable.length; _i++) {
                        if (this._inboxService.masterScheduleSelectionFilter(this.masterStaffsAvalable[_i])) {
                            filteredMasterStaffs.push(this.masterStaffsAvalable[_i]);
                        }
                    }
                    this.masterStaffsAvalable = filteredMasterStaffs;
          
                    let chatWithUserListData = {
                      "variableKey": "masterStaffsAvalable",
                      "data": filteredMasterStaffs
                    };
                    this._SharedService.chatWithUserListData.emit(chatWithUserListData);
                  });
            }
    
            if (this.userData.master_config.default_clinician_roles_available) {
                this._inboxService.getUsersListByRoleId(this.userData.master_config.default_clinician_roles_available, this.userData.master_details.id, true,this.selectSiteId).then((users3) => {
                    this.defaultMasterStaffsAvalable = users3;
                    console.log("users3", users3);
                    let chatWithUserListData = {
                      "variableKey": "defaultMasterStaffsAvalable",
                      "data": users3
                    };
                    this._SharedService.chatWithUserListData.emit(chatWithUserListData);
                  });
            }
          }   
   
    }

    
    if (this.userData.group == 3) {
        this.defaultNurses = [];
        this.usersList = [];
        this.usersListByRoleId = [];

        if (this._inboxService.checkInfusionHours(false).isWorkingHours) {
            if (this.configData.clinician_roles_on_working_hours) {
                clinicianRolesAvaiable = this.configData.clinician_roles_on_working_hours;
            } else {
                isRoleAvailable = false;
            }
        } else {
            if (this.configData.clinician_roles_beyond_working_hours) {
                clinicianRolesAvaiable = this.configData.clinician_roles_beyond_working_hours;
            } else {
                isRoleAvailable = false;
            }
        }

        /** For Patient Login User Selection Condition -- Need To Generalize */
        this.scheduledPrimaryCar = 0;
        if (this._inboxService.checkInfusionHours(false).isWorkingHours) {
            if (this.configData.clinician_roles_on_working_hours && this.configData.clinician_roles_on_working_hours !== "") {
                this.clinicianLoad = false;
                this._inboxService.getUsersListByRoleId(this.configData.clinician_roles_on_working_hours, this.configData.tenantId, true,this.selectSiteId).then((users1: any[]) => {
                    this.usersList = users1;
                    let chatWithUserListData = {
                        "variableKey": "usersList",
                        "data": this.usersList
                    };
                    this._SharedService.chatWithUserListData.emit(chatWithUserListData);
                    this.clinicianLoad = true;
                });                                
            } else {                
                let chatWithUserListData = {
                    "variableKey": "usersList",
                    "data": this.usersList
                };
                this._SharedService.chatWithUserListData.emit(chatWithUserListData);
                this.clinicianLoad = true;
            }

            if (this.configData.default_clinician_roles_available_on_working_hour && this.configData.default_clinician_roles_available_on_working_hour !== "") {
                this.clinicianLoad = false;
                this._inboxService.getUsersListByRoleId(this.configData.default_clinician_roles_available_on_working_hour, this.configData.tenantId, true,this.selectSiteId).then((users2: any[]) => {
                    this.defaultNurses = users2;
                    let chatWithUserListData = {
                        "variableKey": "defaultNurses",
                        "data": this.defaultNurses
                    };
                    this._SharedService.chatWithUserListData.emit(chatWithUserListData);                    
                });                                
            } else {
                let chatWithUserListData = {
                    "variableKey": "defaultNurses",
                    "data": this.defaultNurses
                };
                this._SharedService.chatWithUserListData.emit(chatWithUserListData);                
            }
        } else {
            if (this.configData.clinician_roles_beyond_working_hours && this.configData.clinician_roles_beyond_working_hours !== "") {
                this.clinicianLoad = false;
                this._inboxService.getUsersListByRoleId(this.configData.clinician_roles_beyond_working_hours, this.configData.tenantId, true,this.selectSiteId).then((users3: any[]) => {
                    this.usersListByRoleId = users3;
                    let chatWithUserListData = {
                        "variableKey": "usersListByRoleId",
                        "data": this.usersListByRoleId
                    };
                    this._SharedService.chatWithUserListData.emit(chatWithUserListData);                    
                });                
            } else {                
                let chatWithUserListData = {
                    "variableKey": "usersListByRoleId",
                    "data": this.usersListByRoleId
                };
                this._SharedService.chatWithUserListData.emit(chatWithUserListData);                
            }

            if (this.configData.default_clinician_roles_available && this.configData.default_clinician_roles_available !== "") {
        
                this._inboxService.getUsersListByRoleId(this.configData.default_clinician_roles_available, this.configData.tenantId, true,this.selectSiteId).then((users4: any[]) => {
                    this.defaultNurses = users4;
                    let chatWithUserListData = {
                        "variableKey": "defaultNurses",
                        "data": this.defaultNurses
                    };

                    this._SharedService.chatWithUserListData.emit(chatWithUserListData);                    
                });                                
            } else {
                let chatWithUserListData = {
                    "variableKey": "defaultNurses",
                    "data": this.defaultNurses
                };

                this._SharedService.chatWithUserListData.emit(chatWithUserListData);                
            }
        }        
        /** ***********************End Section ****************** */
    }


 

    /**
     * Default chat
     */
    if (isRoleAvailable) {
        let chatWithUserListData = {
            "variableKey": "clinicalUserDetails",
            "data": this.clinicalUserDetails
        };

        this._SharedService.chatWithUserListData.emit(chatWithUserListData);
    }                   
        
    if(!authToken){
       this.router.navigate(['/login']);
    }
    this.socketEventSubscriptions.push(
        this._structureService.subscribeSocketEvent('onGoingVideoCall').subscribe((data) => {
            console.log('*****************onGoingVideoCall**************')
            console.log(data);
            this.joinChat = true;
            this.setLocalStorageParticipants('true');
            localStorage.setItem('videoChatData',JSON.stringify(data));

        })
    );
    this.socketEventSubscriptions.push(
        this._structureService.subscribeSocketEvent('userKicked').subscribe((data) => {
            console.log('*****************userKicked**************')
            console.log(data);
            var activity = this.userData.displayName + " (" + this.userData.userId + ") has been removed from chatroom - " + this.videoChatRoomId

            this.allowVideoChat = true;
            this.isVideoChatMinimized = false;
            this._SharedService.videoCallReceived = false
            this._SharedService.videoCall = false
            this._SharedService.callInitiatedRoomId = ''
            this._SharedService.onVideoChat = false;
            if (this._commonVideoService.checkConnectionStatus()) {
                this._commonVideoService.disconnect(false);
                document.getElementById('video_container').style.visibility = 'hidden';
                const notifyMsg = 'You have been removed from the video call.';
                var notify = $.notify(notifyMsg);
                setTimeout(() => {
                    notify.update({ 'type': 'success', 'message': '<strong>' + notifyMsg + '</strong>' });
                }, 1000);
                var activityData = {
                    activityName: "Ending Video Chat",
                    activityType: "video communication",
                    activityLinkageId: this.videoChatRoomId,
                    activityDescription: activity
                };
            }
            this._SharedService.listUpdateOnKicked.emit({data})
            this._structureService.socket.emit("disconnectOnKicked", { userId: this.userData.userId, roomId: this.videoChatRoomId });
            console.log('emit disconnectOnKicked')
            this.onVideoCall = false;
            this.setLocalStorageParticipants('false');
            this.initiatorEnd = false;
            this.cameraPrivacy = false
            this.microphonePrivacy = false
            this._structureService.trackActivity(activityData);

        })
    );
    this.socketEventSubscriptions.push(
        this._structureService.subscribeSocketEvent('userDeactivatedPolloing').subscribe((data) => {
            console.log("This user has been inactivated ");
            console.log("data", data);
            this.logout();
            var notify = $.notify({'message':this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')},
                {
                    allow_dismiss: true,
                    delay: 0
                });
            setTimeout(function() {
                notify.update({'type': 'danger', 'message': '<strong>'+this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')+'</strong>', 'delay':0});
            }, 5000);
        })
    );

    

    this.muteInitiatorAudio = this._SharedService.muteInitiatorAudio.subscribe(
        (userData) => {
           this.hideInitiatorAudioBtn = true;
        }
    );

	console.log("==========================>",this.crossTenantId)
    
    this.profileImageUrl = this._structureService.getCookie('profileImageUrl');
    this.userRole = this._structureService.getCookie('userRole');
    this.displayname = this._structureService.getCookie('displayname');
    this.domainName = this._structureService.domainName;
    if (this.userData && this.userData.mySiteinfo && this.userData.mySiteinfo.name) {
        this.tenantName = this.userData.mySiteinfo.name;
      }   else {
        this.tenantName = this._structureService.getCookie('tenantname');
    }
    this.showTutorialButton = this.userData.config.enable_talentlms === '1' && +this.userData.group !== UserGroup.PATIENT;
    this.tutorialLink = this.userData.config.talentlms_url;
    console.log("this.privileges ====>",this.privileges);
    console.log("this.userData",this.userData);
    setTimeout(()=>{
        
        
        console.log(" feedback Form :",this.userData.config);
        if(this.userData.config && this.userData.config.show_support_and_feedback_form && this.userData.config.show_support_and_feedback_form == '1' && this._structureService.currentUrlNow != "/login"){
            $(".telemetryAgentFeedbackLabel.centerRight").addClass("enabled");
        } else {
            $(".telemetryAgentFeedbackLabel.centerRight").removeClass("enabled");
        }
    },1000);

    if (this.userData.organizationMasterId != 0 
        && this.userData.crossTenantsDetails.length > 1 
        && this.privileges.indexOf('allowOrganizationSwitching') != -1 
        && this.privileges.indexOf('allowRoleTenantSwitching') != -1
        && this.configData.allow_multiple_organization == 1 
        && this.userData.masterEnabled == '0'
        && this.userData.config.enable_multisite != '1'
        && this.configData.enable_nursing_agencies_visibility_restrictions != 1       
    ) {
        // Internal site OFF and user is a staff role with allowRoleTenantSwitching privilege
        this.organizationSwitchPrivilege = true;
        this.crossTenantOptions = this.userData.crossTenantsDetails;

        this.crossTenantOptions.forEach((tenant) => {
            if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
                this.selectedTenant = tenant;
                this.chatWithTenantFilter.selectedTenant = tenant.id;
                this._GlobalDataShareService.setSelectedTenantDetails(tenant);
            }
        });
    } else if(this.userData.organizationMasterId != 0
        && this.userData.crossTenantsDetails.length > 1
        && this.userData.isMaster =='1' 
        && this.userData.masterEnabled == '1'
        && this.userData.config.enable_multisite != '1'
        && this.privileges.indexOf('allowOrganizationSwitching') != -1 && this.privileges.indexOf('allowRoleTenantSwitching') != -1
    ) {
            // Internal site ON and user is a Internal internal staff role with allowRoleTenantSwitching privilege
        this.organizationSwitchPrivilege = true;
        this.crossTenantOptions = this.userData.crossTenantsDetails;
        this.crossTenantOptions.forEach((tenant) => {
            if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
                this.selectedTenant = tenant;
                this.chatWithTenantFilter.selectedTenant = tenant.id;
                this._GlobalDataShareService.setSelectedTenantDetails(tenant);
            }
        });

    } else if(this.userData.organizationMasterId != 0
        && this.userData.crossTenantsDetails.length > 1
        && this.userData.isMaster =='0' 
        && this.userData.masterEnabled == '1'
        
    ) {
        this.organizationSwitchPrivilege = false;
        // Master enabled and user is a flex site user in with or without allowRoleTenantSwitching privilege
        this.crossTenantOptions = this.userData.crossTenantsDetails;
        this.crossTenantOptions.forEach((tenant) => {
            if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
                this.selectedTenant = tenant;
                this.chatWithTenantFilter.selectedTenant = tenant.id;
                this._GlobalDataShareService.setSelectedTenantDetails(tenant);
            }
        });
    } else if(this.configData.enable_nursing_agencies_visibility_restrictions == 1) {
        if(this.userData.nursing_agencies == "") {
            if (this.userData.organizationMasterId != 0 
                && this.userData.crossTenantsDetails.length > 1 
                && this.privileges.indexOf('allowOrganizationSwitching') != -1 && this.privileges.indexOf('allowRoleTenantSwitching') != -1
                && this.configData.allow_multiple_organization == 1 
                && this.userData.masterEnabled == '0'    
                && this.userData.config.enable_multisite != '1'   
            ) {
                this.organizationSwitchPrivilege = true;
                this.crossTenantOptions = this.userData.crossTenantsDetails;

                this.crossTenantOptions.forEach((tenant) => {
                    if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
                        this.selectedTenant = tenant;
                        this.chatWithTenantFilter.selectedTenant = tenant.id;
                        this._GlobalDataShareService.setSelectedTenantDetails(tenant);
                    }
                });
            }
            else { // TODO : Need to make this as a common function 
                    this.organizationSwitchPrivilege = false;
                    this.crossTenantOptions = this.userData.crossTenantsDetails;
                    this.crossTenantOptions.forEach((tenant) => {
                        if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
                            this.selectedTenant = tenant;
                            this.chatWithTenantFilter.selectedTenant = tenant.id;
                            this._GlobalDataShareService.setSelectedTenantDetails(tenant);
                        }
                    });
                }

        } else {
            this.organizationSwitchPrivilege = false;
            this.crossTenantOptions = this.userData.crossTenantsDetails;

            this.crossTenantOptions.forEach((tenant) => {
                if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
                    this.selectedTenant = tenant;
                    this.chatWithTenantFilter.selectedTenant = tenant.id;
                    this._GlobalDataShareService.setSelectedTenantDetails(tenant);
                }
            });
        }
    }
    
    if (this.userData.organizationMasterId != 0 
        && this.userData.crossTenantsDetails.length > 1
        && this.privileges.indexOf('allowOrganizationSwitching') != -1
        && this.privileges.indexOf('allowMultipleOrganizationsPatientCommunication') != -1 
        && this.configData.allow_multiple_organization == 1 
        && this.userData.masterEnabled != 1
        && this.configData.enable_nursing_agencies_visibility_restrictions != 1
    ) {
        //Master is not enabled and user with allowMultipleOrganizationsPatientCommunication privilege
        this.isOtherTenantPatients = true;        
    } else if(this.userData.organizationMasterId != 0
        && this.userData.crossTenantsDetails.length > 1
        && this.userData.masterEnabled == '1'
        && this.userData.isMaster =='1') {
        this.isOtherTenantPatients = true;      
    } else if((this.userData.organizationMasterId != 0
        && this.userData.crossTenantsDetails.length > 1
        && this.userData.isMaster =='0' 
        && this.userData.masterEnabled == '1'
        && this.userData.group != '3')) {
        this.isOtherTenantPatients = false;
    } else if(this.configData.enable_nursing_agencies_visibility_restrictions == 1) {
        if(this.userData.nursing_agencies == "") {
            if (this.userData.organizationMasterId != 0 
                && this.userData.crossTenantsDetails.length > 1 
                && this.privileges.indexOf('allowOrganizationSwitching') != -1 && this.privileges.indexOf('allowRoleTenantSwitching') != -1
                && this.configData.allow_multiple_organization == 1 
                && this.userData.masterEnabled == '0'       
            ) {
                this.isOtherTenantPatients = true;
            }
        } else {
            this.isOtherTenantPatients = false;
            this.chatWithLoader.otherTenantPatients = false;
        }
    } else {
		this.isOtherTenantPatients = false;
        this.chatWithLoader.otherTenantPatients = false;
    }



    this.column6 = false;

    if (this.userData.organizationMasterId != 0 
        && this.userData.crossTenantsDetails 
        && this.userData.crossTenantsDetails.length > 1) {
        this.column6 = false;
        this.crossTenantOptionsChatWith = this.userData.crossTenantsDetails;
        this.chatWithTenantFilter.tenants = this.crossTenantOptionsChatWith;
        this.crossTenantOptionsChatWith.forEach((tenant) => {
            if (tenant.id == this._structureService.getCookie("crossTenantId")) {
                this.crossTenantName = tenant.tenantName;
            }
        });

        if ((this.crossTenantOptionsChatWith.length > 1
            && this.privileges.indexOf('allowOrganizationSwitching') != -1
            && this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication') !== -1 
            && this.configData.allow_multiple_organization == 1
            && this.userData.masterEnabled == '0'
            && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
        || (this.crossTenantOptionsChatWith.length > 1 
            && this.userData.isMaster =='1' 
            && this.userData.masterEnabled == '1')
        || (this.crossTenantOptionsChatWith.length > 1 
            && this.userData.isMaster =='0' 
            && this.userData.masterEnabled == '1'
            && this.userData.group !='3')
        || (this.crossTenantOptionsChatWith.length > 1
            && this.privileges.indexOf('allowOrganizationSwitching') != -1
            && this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication') !== -1 
            && this.configData.allow_multiple_organization == 1
            && this.userData.masterEnabled == '0'
            && this.configData.enable_nursing_agencies_visibility_restrictions == 1
            && this.userData.nursing_agencies == "")
        ) {
            console.log('444444444444444444444');
            this.column3 = true;
            if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1') {
                this.internalSiteId = this.userData.master_details.id;
            }
            this.chatWithLoader.otherTenantstaff = true;
            this.chatWithTenantFilter.tenants = this.chatWithTenantFilter.tenants.filter((tenant)=> {
                if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1'){
                    if(this.internalSiteId == tenant.id) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return true;
                }
            });
            if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1') {
                if(this.userData.master_details.id != this.userData.tenantId) {
                    this.crossTenantOptionsChatWith.map((tenant)=> {
                        if(tenant.id == this.userData.tenantId) {
                            this.chatWithTenantFilter.tenants.unshift(tenant);
                        }
                    });
                }
            }
            this._structureService.getOtherTenantStaffUsers().then((data) => {
                if (data['getSessionTenant']) {
                    this.otherTenantStaffList = data['getSessionTenant'].otherTenantStaffUsers;
                    this.setOtherTenantClinicianDataTenantWise();
                } else {
                    this._structureService.deleteCookie('authenticationToken');
                    this.router.navigate(['/login']);
                    this.chatWithLoader.otherTenantstaff = false;
                }
            }).catch((ex) => {
                this.chatWithLoader.otherTenantstaff = false;
            });
        } else {
            this.column3 = false;
        }
    } else {
        this.crossTenantOptionsChatWith = [];
        this.chatWithTenantFilter.tenants = [];
        this.column3 = false;

        if (this.privileges.indexOf('chatWithPatients') == -1) {
            this.column6 = true;
        }
    }

    if (this._structureService.previousUrlNow == "/login" && this._structureService.currentUrlNow == "/inbox" && this.userData.group == '3') {
        this.infoBottomOverlay = true;
    }

    if (isBlank(this.chatWithSubs) || (this.chatWithSubs && this.chatWithSubs.closed)) {
        this.chatWithSubs = this._SharedService.chatWith.subscribe(
            (userGroup) => {
                this.checkMultiplePatient(userGroup);
            }
        );
    }

    if (isBlank(this.sessionRefreshSubs) || (this.sessionRefreshSubs && this.sessionRefreshSubs.closed)) {
        this.sessionRefreshSubs = this._SharedService.sessionRefresh.subscribe(
            (userData) => {
                this.privileges = userData.privileges;
                this.hasChatPermission();
                if ((this.userData.crossTenantsDetails.length > 1 
                    && this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication') !== -1
                    && this.privileges.indexOf('allowOrganizationSwitching') != -1 
                    && this.configData.allow_multiple_organization == 1
                    && this.userData.masterEnabled == '0') 
                || (this.userData.crossTenantsDetails.length > 1
                    && this.userData.isMaster =='1' 
                    && this.userData.masterEnabled == '1' 
                    && this.privileges.indexOf("allowMultipleOrganizationsStaffCommunication") > -1)
                    && this.privileges.indexOf('allowOrganizationSwitching') != -1
                || (this.crossTenantOptionsChatWith.length > 1 
                    && this.userData.isMaster =='0' 
                    && this.userData.masterEnabled == '1'
                    && this.privileges.indexOf('allowOrganizationSwitching') != -1
                    && this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication') > -1)) {
                    this.column3 = true;
                    if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1' && this.userData.master_details) {
                        this.internalSiteId = this.userData.master_details.id;
                    }
                    this.chatWithLoader.otherTenantstaff = true;
                    this._structureService.getOtherTenantStaffUsers().then((data) => {
                        if (data['getSessionTenant']) {
                            this.otherTenantStaffList = data['getSessionTenant'].otherTenantStaffUsers;
                            this.setOtherTenantClinicianDataTenantWise();
                        } else {
                            this._structureService.deleteCookie('authenticationToken');
                            this.router.navigate(['/login']);
                            this.chatWithLoader.otherTenantstaff = false;
                        }
                    }).catch((ex) => {
                        this.chatWithLoader.otherTenantstaff = false;
                    });
                } else {
                    this.column3 = false;
                }
            }
        );
    }

    if (isBlank(this.newMessageGroupSubs) || (this.newMessageGroupSubs && this.newMessageGroupSubs.closed)) {
        this.newMessageGroupSubs = this._SharedService.newMessageGroup.subscribe(
            (clickedItem) => {
                console.log("Event emitter For Message Groupppp---", clickedItem);
                console.log("this.messageGroup===>>>", this.messageGroup);
                if (clickedItem.status == "new") {
                    this.messageGroup.unshift(clickedItem);
                    this.messageGroup = this.messageGroup.slice();
                    this._structureService.messageGroups = this.messageGroup;
                }else if(clickedItem.status=="up"){
                    if(!clickedItem.ispublic && clickedItem.memberIds && clickedItem.memberIds.split(',').indexOf(this.userData.userId)==-1 ) {
                        console.log(this.messageGroup)
                        this.messageGroup = this.messageGroup.filter((group)=>{group.id == clickedItem.id});
                    }
                    for(let i=0;i<this.messageGroup.length;i++){
                        if(this.messageGroup[i].id==clickedItem.id){
                            this.messageGroup[i].name = clickedItem.name;
                        }
                    }

                    this._structureService.messageGroups = this.messageGroup;
                    if(!this.messageGroup || !this.messageGroup.length){
                        this._SharedService.newMessageGroupUp.emit({"status":"Up"});
                    }

                     

                } else if (clickedItem.status == "delete") {
                    for (let i = 0; i < this.messageGroup.length; i++) {
                        if (this.messageGroup[i] != undefined && clickedItem.id.split(',').indexOf(this.messageGroup[i].id) != -1) {
                            delete this.messageGroup[i];
                        }
                    }

                    this._structureService.messageGroups = this.messageGroup;

                } else if (clickedItem.status == "signUp") {
                    this.getAllMessageGroupDetails(clickedItem.userId, clickedItem.tenantId);
                }

                this._SharedService.patientDiscussionGroup.emit(this._structureService.messageGroups);
            }
        );
    }

    if (isBlank(this.userDataUpdateSubs) || (this.userDataUpdateSubs && this.userDataUpdateSubs.closed)) {
        this.userDataUpdateSubs = this._SharedService.userDataUpdate.subscribe((userData) => {
            console.log('----------userDataUpdate.subscribe----------', userData);
            var isRoleAvailable = true;
            var clinicianRolesAvaiable = null;
            var setCliniciansRoleAvailableResponse;

            if (this.userData.group == 3) {
                if ((userData.role == 'Patient' 
                    && this.userData.organizationMasterId != 0 
                    && this.userData.crossTenantsDetails.length > 1 
                    && this.privileges.indexOf('allowMultipleOrganizationsPatientCommunication') != -1 
                    && this.privileges.indexOf('allowOrganizationSwitching') != -1
                    && this.configData.allow_multiple_organization == 1)
                || (this.userData.isMaster =='1' 
                    && this.userData.masterEnabled == '1'
                    && this.privileges.indexOf('allowOrganizationSwitching') != -1
                    && this.privileges.indexOf('allowMultipleOrganizationsPatientCommunication') != -1)
                || (this.userData.isMaster =='0' 
                    && this.userData.masterEnabled == '1'
                    && this.privileges.indexOf('allowOrganizationSwitching') != -1
                    && this.privileges.indexOf('allowMultipleOrganizationsPatientCommunication') != -1)) {
                    if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1' && this.userData.master_details) {
                        this.internalSiteId = this.userData.master_details.id;
                    }
                    console.log(this.otherTenantPatientsList, this.userData, this.privileges, this.configData);
                    console.log('-----true---');
                    if (userData && userData.status == 'update' && userData.fetch_data) {
                        this.isOtherTenantPatients = false;
                        this._structureService.getAllTenantUsersListByRoleId(this.userData.organizationMasterId).then((result) => {
                            this.otherTenantsPatients(result);
                        });
                    } else {
                        if (userData.tenantId && userData.tenantId != '0' && this.otherTenantPatientsList && this.otherTenantPatientsList.length) {
                            this.otherTenantPatientsList.map((tenantData) => {
                                if (tenantData.tenantId == userData.tenantId && tenantData.userList.length) {
                                    console.log(tenantData)
                                    tenantData.userList = tenantData.userList.filter((user) => { return user.userId != userData.userId })
                                }
                            });
                        }
                    }
                } 
                /** ***********************End Section ****************** */
            }

        })
    }
    /** End Data Fetching Section */
    $('#chatWithModel').on('hidden.bs.modal', ()=> {
        this.selectedGroup = {};
        this.selectedTopic = {};
        this.chatWithModalShown = false;
        this.chatwithPageCount = {
            staffs: 0,
            patients: 0,
            partner:0
        }
        this.noMoreItemsAvailable = {
            users: false
        };
        this.userListChatwith = [];
        this.clinicalUserDetails = [];
        this.usersList = [];
        this.loadMoreSearchValue = undefined;
    });

    $('#chatWithModel').on('show.bs.modal', ()=> {
        this.chatWithModalShown = true;
        this.loadMoreSearchValue = undefined;
    });

    $('body').on('change','#chatWithMessageGroup',(event)=>{
        if(event.target.value) {
            this.messageGroup.filter( (group) => {
                 if(group.groupId == event.target.value){
                    this.selectedGroup = group;
                    this.msgGrpSubjectOffset = CONSTANTS.contentOffset;
                    return true;
                 }
            });
            this.selectedTopic = {
                id:-1
            };
            this.showSubject = false;
            //Reset the search and select component
            this.resetSearchSelect = true;
            this.messageGroupTopic = [...[]];
        }
    });
    $('body').on('change','#chatWithTenantFilter',(event)=>{
        if(event.target.value)  {
            var previousSelectedTenant = this.chatWithTenantFilter.tenants.find((tenant) => tenant.id == this.chatWithTenantFilter.selectedTenant);
            this.chatWithTenantFilter.selectedTenant = event.target.value;
            if(this.chatWithTenantFilter.enabledReset) {
                var currentSelectedTenant = this.chatWithTenantFilter.tenants.find((tenant) => tenant.id == this.chatWithTenantFilter.selectedTenant);
                var activityData = {
                    activityName: "Chat With " + ((this.optionShow == 'staff') ? 'Staff ' : ((this.optionShow == 'patient') ? 'Patient ' : ' ')) +"Tenant Switching",
                    activityType: "messaging",
                    activityDescription: this.userData.displayName + " (" + this.userData.userId + ") has selected tenant " + currentSelectedTenant.tenantName + '(' + currentSelectedTenant.id +')' + ((previousSelectedTenant) ? (' from tenant '+ previousSelectedTenant.tenantName + '(' + previousSelectedTenant.id +')') : ''),
                    tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
                };
                this._structureService.trackActivity(activityData);
            } else {
                this.chatWithTenantFilter.enabledReset = true;
            }
        }
    });
    
    let userDetails =JSON.parse(this._structureService.userDetails);
    let isPatient = userDetails.group;
    let helpCenterKey = atob(userDetails.helpCenterKey);
    if(userDetails && userDetails.config && userDetails.config.showElevio == '1'){
        if(isPatient == '3' ){
            this.groups = ['platform-user','app-type:desktop','user-type:patient'];
            if(userDetails && userDetails.config && userDetails.config.elevioPatient){
                console.log("userDetails.elevio",userDetails.config.elevioPatient)
                var configGroups = userDetails.config.elevioPatient.split(",");
                this.groups = this.groups.concat(configGroups);
                this.groups = this.groups.filter((item, pos) => this.groups.indexOf(item) === pos);
            }
        }
        if(isPatient != '3'&& isPatient != '20'){
            this.groups = ['platform-user','app-type:desktop','user-type:staff'];
            if(userDetails && userDetails.config && userDetails.config.elevioStaff){
                console.log("userDetails.elevio",userDetails.config.elevioStaff)
                configGroups = userDetails.config.elevioStaff.split(",");
                this.groups = this.groups.concat(configGroups);
                this.groups = this.groups.filter((item, pos) => this.groups.indexOf(item) === pos);
            }
        }
        if(isPatient == '20' ){
            this.groups = ['platform-user','app-type:desktop','user-type:partner'];
            if(userDetails && userDetails.config && userDetails.config.elevioPartner){
                console.log("userDetails.elevio",userDetails.config.elevioPartner)
                configGroups = userDetails.config.elevioPartner.split(",");
                this.groups = this.groups.concat(configGroups);
                this.groups = this.groups.filter((item, pos) => this.groups.indexOf(item) === pos);
            }
        }
        var self = this;
        var userIdE = userDetails.userId+"-"+userDetails.tenantName;
        console.log("Elevio isPatient",self.groups,userIdE);
        if(helpCenterKey){
        this._ngZone.runOutsideAngular(() => {
            this._ngZone.run(() => { 
			console.log("wwwwwwwwwwwwwwwwwwwwwwwwwwww");
        Elevio.load('5e850b04459a6').then(() => {
            console.log('Elevio has loaded, ready to use!');
            Elevio.on('load', function(_elev) {
                _elev.setSettings({ hideLauncher: true, disableDevelopmentWarnings: true });
                _elev.setUser({
                    first_name: userIdE,
                    groups: self.groups
                });
            });
            });
            });
        });
        }
    }
    window.addEventListener('message',  (event)=> {
        try {
          if (event.data && event.data.message =="logout") {
            console.log("Topbar =event Listner message===>",event.data)
            localStorage.setItem('logoutSessionOut',"true");
            this.router.navigate(['/login']);
          }
           /******* session interrupt alert *****/
          if (event.data && event.data.message =="interruptok") {
           $("#newloader").hide();
           setTimeout(function(){  
                    $(".swal-modal").css("vertical-align", "top"); 
                        $(".swal-button--confirm").css("background-color", "#f39834"); 
                        $(".swal-footer").css("text-align", "center");
                        $(".swal-button--confirm:not([disabled]):hover").css("background-color", "#f39834"); 
                    }, 200);
                    $("html,body").scrollTop(0);
            
             swal({
                title: 'Are you sure?',
                text: "Your session was interrupted unexpectedly. Please check the Draft category of your Forms Worklist for your saved Forms and resume your work from there and resubmit the Form again",
                 type: "warning",
                        showCancelButton: false,
                        cancelButtonClass: "btn-default",
                        confirmButtonClass: "btn-warning",
                        confirmButtonText: "Ok",
                        closeOnConfirm: true
               }, (isConfirm) => {
                if (isConfirm) {
                  this._structureService.setCookie('tabname','DRAFTS', 1);
                  if(localStorage.getItem('enableCollaborateEdit') == '1'){
                    this.router.navigate(['/forms/list']);
                  }else{
                    this.router.navigate(['/forms/worklist']);
                  }

                }
            });
          }
          /******* session interrupt alert *****/
        } catch (error) {
          console.log('error');
        }
      }, false);
  }
setCliniciansRoleAvailableOnClick(setParams, init, loadmore, optionShow, searchValue) {
    console.log(setParams)
    if (this._inboxService.checkInfusionHours(false).isWorkingHours) {
        if (this.configData.clinician_roles_on_working_hours) {
            setParams.clinicianRolesAvaiable = this.configData.clinician_roles_on_working_hours;
        } else {
            setParams.isRoleAvailable = false;
        }
    } else {
        if (this.configData.clinician_roles_beyond_working_hours) {
            setParams.clinicianRolesAvaiable = this.configData.clinician_roles_beyond_working_hours;
        } else {
            setParams.isRoleAvailable = false;
        }
    }
    this.scheduledPrimaryCar = 0;
    this.defaultNurses=[];
    localStorage.setItem("defaultNurses",JSON.stringify(this.defaultNurses));
    if (this._inboxService.checkInfusionHours(false).isWorkingHours) {
        this.usersList = [];
        if (this.configData.clinician_roles_on_working_hours) {
            
            this.loadMoremessage.users = "Loading ....";
            this._inboxService.getUsersListByRoleId(this.configData.clinician_roles_on_working_hours, 0, null,null,(init ? 0 : ((optionShow == 'staff') ? this.chatwithPageCount.staffs :((optionShow == 'partner') ? this.chatwithPageCount.partner :this.chatwithPageCount.patients))),((optionShow == 'staff' || optionShow == 'partner') ? undefined : false),searchValue, null, true,this.selectSiteId).then((users:any) => {
                this.chatWithUsersLoading = false;
                this.loadMoreSearchValue = searchValue;
                if(!loadmore) {
                    this.usersList = users;
                } else {
                    this.usersList = [...this.usersList, ...users];
                }
                if(users.length != 20)
                this.noMoreItemsAvailable.users = true;

                if(optionShow=='patient') {
                    this.chatwithPageCount.patients += 1;
                } else if(optionShow=='staff') {
                    this.chatwithPageCount.staffs += 1;
                } else if(optionShow=='partner') {
                    this.chatwithPageCount.partner += 1;
                }
                this.loadMoremessage.users = "Load more";
                this.userListChatwith = this.modalFilter.transform(this.usersList, this.optionShow);
                this.clinicianLoad=true;
                
            }).catch((ex) => {
                
            });
        } else {
            this.clinicianLoad=true;
            if (this.configData.default_clinician_roles_available_on_working_hour) {
                this._inboxService.getUsersListByRoleId(this.configData.default_clinician_roles_available_on_working_hour, 0, true,this.selectSiteId).then((result) => {   
                    this.chatWithUsersLoading = false;
                    this.defaultNurses = result;
                    let chatWithUserListData = {
                        "variableKey": "defaultNurses",
                        "data": this.defaultNurses
                    };
                    this._SharedService.chatWithUserListData.emit(chatWithUserListData);
                    localStorage.setItem("defaultNurses",JSON.stringify(this.defaultNurses));
                }).catch((ex) => {
                });
            }
        }
    } else {
        this.usersListByRoleId = [];
        if (this.configData.clinician_roles_beyond_working_hours) {
            this._inboxService.getUsersListByRoleId(this.configData.clinician_roles_beyond_working_hours, 0, true,this.selectSiteId).then((users) => {   
                this.chatWithUsersLoading = false;
                this.usersListByRoleId = users;
                let chatWithUserListData = {
                    "variableKey": "usersListByRoleId",
                    "data": this.usersListByRoleId
                };
                this._SharedService.chatWithUserListData.emit(chatWithUserListData);
                localStorage.setItem("usersListByRoleId",JSON.stringify(this.usersListByRoleId));
            }).catch((ex) => {
            });
        } else {
            if (this.configData.default_clinician_roles_available) {
                this._inboxService.getUsersListByRoleId(this.configData.default_clinician_roles_available, 0, true,this.selectSiteId).then((result) => {   
                    this.chatWithUsersLoading = false;
                    this.defaultNurses = result;
                    let chatWithUserListData = {
                        "variableKey": "defaultNurses",
                        "data": this.defaultNurses
                    };
                    this._SharedService.chatWithUserListData.emit(chatWithUserListData);
                    localStorage.setItem("defaultNurses",JSON.stringify(this.defaultNurses));
                }).catch((ex) => {
                });
            }
        }
    }
    const promise = new Promise((resolve, reject) => {
        resolve(setParams);
    });
    return promise;
}


setuserListParams(clinicianRolesAvaiable,init, loadMore, optionShow, searchValue) {
    let chatWithFilterTenantId = null;
    if(init) {
        if(optionShow === CONSTANTS.userTypes.patient) {
            this.UsergroupIds = clinicianRolesAvaiable;
        } else if(optionShow === CONSTANTS.userTypes.staff) {
            this.UsergroupIds = 3;
        } else if(optionShow === CONSTANTS.userTypes.partner) {
            this.UsergroupIds = 20;
        }
    }
    if(optionShow == CONSTANTS.userTypes.staff || optionShow == CONSTANTS.userTypes.partner || optionShow == CONSTANTS.userTypes.patient) {
        chatWithFilterTenantId = this.chatWithTenantFilter.selectedTenant;
    }
    this.loadMoremessage.users = "Loading ....";
    let params = {
        status : 'notRejected',
        roleId : this.UsergroupIds ? this.UsergroupIds : 3,
        optionShow : optionShow === 'groups' ? 'patient' : optionShow,
        excludeLogginedUser : this.userData.userId,
        isTenantRoles : 'null',
        isFromChat : 1,
        userGroup : this.userData.group,
        isSchedule : false,
        pageCount : 0
    };
    if (!isBlank(this.selectSiteId) && this.selectSiteId !== '0') {
        params['siteIds'] = this.selectSiteId;
    } else {
        params['siteIds'] = this.userData.mySites.map((item) => item.id);
    }
    this.userSiteIds = params['siteIds'];
    if(optionShow == 'staff' && !clinicianRolesAvaiable) {
        params['excludeRoleId'] = true;
    } else if (optionShow !== 'staff' && (clinicianRolesAvaiable || !clinicianRolesAvaiable)) {
        params['excludeRoleId'] = false;
    } 
    if(!isBlank(searchValue)) {
        params['searchKeyword'] = searchValue;
    }
    if(!isBlank(chatWithFilterTenantId)) {
        params['chatWithFilterTenantId'] = chatWithFilterTenantId;
    }
    params['needVirtualPatients'] = optionShow === 'groups' || this.userData.config.enable_appless_video_chat === '1'; 
    if(optionShow === 'groups' && !this._structureService.isMultiAdmissionsEnabled) {  
        params['includePDGInfo'] = true;
    }
    this.requestParams = params;
}


callAccordion(tenantID, eve) {
    if ($(".expand-icon-" + tenantID).hasClass("fa-plus")) {
        $(".expand-icon-" + tenantID).removeClass("fa-plus");
        $(".expand-icon-" + tenantID).addClass("fa-minus");
        $(".sub-item-panel-" + tenantID).addClass("showall");
    } else {
        $(".expand-icon-" + tenantID).removeClass("fa-minus");
        $(".expand-icon-" + tenantID).addClass("fa-plus");
        $(".sub-item-panel-" + tenantID).removeClass("showall");
    }
    console.log("callAccordion===========", tenantID, eve);
}

setOtherTenantClinicianDataTenantWise() {
    this.otherTenantClinicianDataTenantWise = [];

    if (this.otherTenantStaffList.length) {
        this.otherTenantStaffList.forEach(value => {
            var user = { id: value.id, userId: value.id, displayname: value.displayName, name: value.displayName, roleId: value.role.id, role: value.role.displayName, dob: value.dateOfBirth, tenantId: value.role.tenantId, tenantName: value.role.tenantName };
            var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id, tenantId: value.role.tenantId, tenantName: value.role.tenantName };
            
            if (value.role.tenantId) {
                if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1'){
                    if(this.internalSiteId == value.role.tenantId) {
                        if (!this.otherTenantClinicianDataTenantWise[value.role.tenantId]) {
                            this.otherTenantClinicianDataTenantWise[value.role.tenantId] = {};
                        }
                        if (!('userList' in this.otherTenantClinicianDataTenantWise[value.role.tenantId])) {
                            this.otherTenantClinicianDataTenantWise[value.role.tenantId]['userList'] = [];
                        }
                        if (!('roleData' in this.otherTenantClinicianDataTenantWise[value.role.tenantId])) {
                            this.otherTenantClinicianDataTenantWise[value.role.tenantId]['roleData'] = {};
                        }
                        if (!('tenantId' in this.otherTenantClinicianDataTenantWise[value.role.tenantId])) {
                            this.otherTenantClinicianDataTenantWise[value.role.tenantId]['tenantId'] = null;
                        }
                        this.otherTenantClinicianDataTenantWise[value.role.tenantId]['roleData'] = roleData;
                        this.otherTenantClinicianDataTenantWise[value.role.tenantId]['userList'].push(user);
                        this.otherTenantClinicianDataTenantWise[value.role.tenantId]['tenantId'] = value.role.tenantId;
                        this.otherTenantClinicianDataTenantWise[value.role.tenantId]['tenantName'] = value.role.tenantName;
                    }
                } else {
                    if (!this.otherTenantClinicianDataTenantWise[value.role.tenantId]) {
                        this.otherTenantClinicianDataTenantWise[value.role.tenantId] = {};
                    }
                    if (!('userList' in this.otherTenantClinicianDataTenantWise[value.role.tenantId])) {
                        this.otherTenantClinicianDataTenantWise[value.role.tenantId]['userList'] = [];
                    }
                    if (!('roleData' in this.otherTenantClinicianDataTenantWise[value.role.tenantId])) {
                        this.otherTenantClinicianDataTenantWise[value.role.tenantId]['roleData'] = {};
                    }
                    if (!('tenantId' in this.otherTenantClinicianDataTenantWise[value.role.tenantId])) {
                        this.otherTenantClinicianDataTenantWise[value.role.tenantId]['tenantId'] = null;
                    }
                    this.otherTenantClinicianDataTenantWise[value.role.tenantId]['roleData'] = roleData;
                    this.otherTenantClinicianDataTenantWise[value.role.tenantId]['userList'].push(user);
                    this.otherTenantClinicianDataTenantWise[value.role.tenantId]['tenantId'] = value.role.tenantId;
                    this.otherTenantClinicianDataTenantWise[value.role.tenantId]['tenantName'] = value.role.tenantName;
                }
                
            }
        });

        this.otherTenantClinicianDataTenantWise = this.otherTenantClinicianDataTenantWise.filter(function (item) {
            return true;
        });

        this.otherTenantClinicianDataTenantWise.sort(function (a, b) {
            if (a.tenantName < b.tenantName) return -1;
            if (a.tenantName > b.tenantName) return 1;
            return 0;
        });

        setTimeout(() => {
            if (this.otherTenantClinicianDataTenantWise.length) {
                let id = this.otherTenantClinicianDataTenantWise[this.otherTenantClinicianDataTenantWise.length - 1].tenantId;
                if (!$('#staffAccordion' + id).hasClass('fa-minus')) {
                    this.callAccordion(this.otherTenantClinicianDataTenantWise[this.otherTenantClinicianDataTenantWise.length - 1].tenantId, '');
                }
            }
        }, 500);
    }

    this.chatWithLoader.otherTenantstaff = false;
}

otherTenantsPatients(otherTenantPatients) {
    this.otherTenantPatientsList = [];
    if (otherTenantPatients.length) {
        otherTenantPatients.forEach(value => {
            var user = { id: value.userId, userId: value.userId, displayname: value.displayname, name: value.displayname, roleId: value.roleId, role: value.role, dob: value.dob, caregiver_displayname: value.caregiver_displayname, caregiver_userid: value.caregiver_userid,c_grp:value.c_grp };
            var roleData = { tenantRoleId: value.roleId, tenantId: value.tenantId, tenantName: value.tenantName };
            
            if (value.tenantId) {
                if (!this.otherTenantPatientsList[value.tenantId]) {
                    this.otherTenantPatientsList[value.tenantId] = {};
                }

                if (!('userList' in this.otherTenantPatientsList[value.tenantId])) {
                    this.otherTenantPatientsList[value.tenantId]['userList'] = [];
                }

                if (!('roleData' in this.otherTenantPatientsList[value.tenantId])) {
                    this.otherTenantPatientsList[value.tenantId]['roleData'] = {};
                }

                if (!('tenantId' in this.otherTenantPatientsList[value.tenantId])) {
                    this.otherTenantPatientsList[value.tenantId]['tenantId'] = null;
                }

                this.otherTenantPatientsList[value.tenantId]['roleData'] = roleData;
                this.otherTenantPatientsList[value.tenantId]['userList'].push(user);
                this.otherTenantPatientsList[value.tenantId]['tenantId'] = value.tenantId;
                this.otherTenantPatientsList[value.tenantId]['tenantName'] = value.tenantName;
            }
        });

        let currentTenant;

        this.otherTenantPatientsList = this.otherTenantPatientsList.filter((item) => {
            if (this.selectedTenant && item.tenantId == this.selectedTenant.id) {
                currentTenant = item;
            } else {
                return true;
            }
        });

        this.otherTenantPatientsList.sort((a, b) => {
            if (a.tenantName < b.tenantName) return -1;
            if (a.tenantName > b.tenantName) return 1;
            return 0;
        });

        this.otherTenantPatientsList.push(currentTenant);

        setTimeout(() => {
            console.log(this.selectedTenant)
            if (this.selectedTenant) {
                if (!$('#patientAccordion' + this.selectedTenant.id).hasClass('fa-minus')) {
                    this.callAccordion(this.selectedTenant.id, '');
                }
            } else {
                if (!$('#patientAccordion' + this.userData.id).hasClass('fa-minus')) {
                    this.callAccordion(this.userData.tenantId, '');
                }
            }
        }, 100);
    }
    this.chatWithLoader.otherTenantPatients = false;
}

newCrossTenantSelected(crossTenant) {
    this._structureService.setCookie('siteCrossBranchTenantId', crossTenant.id, 1);
    this._structureService.setCookie('siteCrossId', crossTenant.siteId, 1);
    this.siteSelected = this._structureService.getCookie('siteCrossId');
    this.dynamic = (this.siteSelected) ? true : false;
    this.preventMultipleCallBranchSwitch = true; 
    this._structureService.branchSwitched = true;
    $(document).ready(()=>{
        $("#userSearchTxt").val("");
        });
        localStorage.setItem("searchInboxkeywordForPolling", "");
        localStorage.setItem("searchInboxkeywordInbox", "");
    if (crossTenant.id != this.selectedTenant.id) {
        if (this._SharedService.onVideoChat) {
            swal({
                title: 'Are you sure?',
                text: "Would you like to continue with the call or contine changing tenant?",
                type: "warning",
                showCancelButton: true,
                cancelButtonClass: "btn-default",
                confirmButtonClass: "btn-warning",
                confirmButtonText: "Change tenant",
                cancelButtonText: "Continue with call",
                closeOnConfirm: true
            }, (isConfirm) => {
                if (isConfirm) {
                    this.disconnect();
                    this.updateSelectedTenantDetails(crossTenant);
                }
            });
        } else {
            this.updateSelectedTenantDetails(crossTenant)
        }
    }
}

    updateSelectedTenantDetails(crossTenant) {
        this._SharedService.configuringTenantData = true;
        this._structureService.setCookie('crossTenantId', crossTenant.id, 1);
        this._structureService.setCookie('crossTenantName', crossTenant.tenantName, 1);
        this._structureService.setCookie('organizationName', crossTenant.tenantName, 1);

        var activityData = {
            activityName: "Tenant Switching",
            activityType: "crosstenant communication",
            activityDescription: "user (" + this.userData.userId + ") switched tenant from " + this.selectedTenant.id + "  to " + crossTenant.id
        };

        this.crossTenantId = crossTenant.id;
        this.selectedTenant = crossTenant;
        this.chatWithTenantFilter.selectedTenant = crossTenant.id;
        this._GlobalDataShareService.setSelectedTenantDetails(crossTenant);
        console.log("this.crossTenantId===>", this.crossTenantId);
        this._structureService.callMethodOfSecondComponent();
        this._structureService.updateCrosstenantInsessionTable(this.crossTenantId).then((response) => {
            this._worklistService.getWorklistMenu();
        },
        () => {   
            this._worklistService.getWorklistMenu();
        });
        this._structureService.getTenantConfigData(this.crossTenantId).then((response) => {
            
            var userData = JSON.parse(this._structureService.userDetails);
            userData.config = response['config'];
            userData.mySites = response['mySites'];
            userData.crossSiteId = response['crossSiteId'];
            userData.crossTenantId = this.crossTenantId;
            this._structureService.loginUserDetailsForRouteCheck['config'] = response['config'];
            this._structureService.loginUserDetailsForRouteCheck['crossTenantId'] = this.crossTenantId;
            var manageConfig = userData.config;
            localStorage.setItem('tenantTimezoneName', response['config'].tenant_timezoneName);
	    /**
         * Worklist Settings for Visit Scheduler
         * 
         */
        if(manageConfig.enable_visit_schedule) {
            manageConfig = response['config'];

            if (manageConfig.worklist_selected_for_visit_schedule && manageConfig.worklist_selected_for_visit_schedule !== "") {
                userData.visitSchedulerWorklistSelected = manageConfig.worklist_selected_for_visit_schedule;
                this._structureService.getVisitSchedulerWorklistActionLink(userData.crossTenantId, userData.visitSchedulerWorklistSelected).then((worklist: any) => {
                    if (worklist.length > 0) {
                        userData.visitSchedulerWorklistActionLink = worklist[0]['worklistActionLink'];
                    } else {
                        userData.visitSchedulerWorklistSelected = '';
                        userData.visitSchedulerWorklistActionLink = '';
                    }
    
                });
            } else {
                userData.visitSchedulerWorklistSelected = '';
                userData.visitSchedulerWorklistActionLink = '';
            }
        }

            /**
             * Manage Security Settings for Eversana
             * 
             */
            console.log(userData.isNursingAgency);
            if (manageConfig.enable_patient_info_from_third_party_app && manageConfig.enable_patient_info_from_third_party_app != 0) {
                userData.accessSecurityEnabled = false;
                userData.accessSecurityIdentifierType = '';
                userData.accessSecurityType = '';
                userData.accessSecurityWorklistSelected = '';
                userData.accessSecurityWorklistLabel = '';
                userData.accessSecurityWorklistActionLink = '';

                var defaultPage = '/profile';
                var nursingAgencyUser = false;
                var enablePages = [];

                enablePages.push('/profile');
                if (manageConfig.enable_nursing_agencies_visibility_restrictions && manageConfig.enable_nursing_agencies_visibility_restrictions == '1' && manageConfig.nursing_agencies != "") {
                    nursingAgencyUser = true;
                }
                if (manageConfig.enable_message_center && manageConfig.enable_message_center == '1') {
                    defaultPage = '/inbox';
                } else if (manageConfig.show_document_tagging && manageConfig.show_document_tagging == '1') {
                    if ((userData.privileges.indexOf('viewAllSignedDocs') == -1 && userData.privileges.indexOf('manageTenants') == -1) || nursingAgencyUser) {
                        defaultPage = '/signature/signature-requests-list';
                    } else if ((userData.privileges.indexOf('viewAllSignedDocs') != -1 || userData.privileges.indexOf('manageTenants') != -1) && !nursingAgencyUser) {
                        defaultPage = '/signature/signed-documents-list';                        
                    } else {
                        defaultPage = this.permissionService.getDefaultPageFormCenter(manageConfig, userData.privileges);                             
                    }
                } else {
                    defaultPage = this.permissionService.getDefaultPageFormCenter(manageConfig, userData.privileges);                             
                }

                //get enablled pages.
                if (manageConfig.enable_message_center && manageConfig.enable_message_center == '1') {
                    enablePages.push('/inbox');
                }

                if (manageConfig.show_document_tagging && manageConfig.show_document_tagging == '1') {
                    if ((userData.privileges.indexOf('viewAllSignedDocs') == -1 && userData.privileges.indexOf('manageTenants') == -1) || nursingAgencyUser) {
                        enablePages.push('/signature/signature-requests-list');
                    }
                    if ((userData.privileges.indexOf('viewAllSignedDocs') != -1 || userData.privileges.indexOf('manageTenants') != -1) && !nursingAgencyUser) {
                        enablePages.push('/signature/signed-documents-list');
                    }
                    enablePages.push(this.permissionService.getDefaultPageFormCenter(manageConfig, userData.privileges));
                }

                enablePages.push(this.permissionService.getDefaultPageFormCenter(manageConfig, userData.privileges));

                //get enablled pages end.


                if (userData.isNursingAgency && userData.isNursingAgency === '1') {
                    
                    manageConfig = response['config'];
                    userData.accessSecurityEnabled = true;

                    if (manageConfig.auxilary_nursing_agency_roles_for_manage_security_rules && manageConfig.auxilary_nursing_agency_roles_for_manage_security_rules != "" && manageConfig.esi_code_for_auxilary_nursing_agency && manageConfig.esi_code_for_auxilary_nursing_agency != "" && manageConfig.auxilary_nursing_agency_identifier_type && manageConfig.auxilary_nursing_agency_identifier_type != "") {
                        userData.accessSecurityType = 'nursingAgency';
                        if (manageConfig.auxilary_nursing_agency_identifier_type == 'auxilary_nursing_agency_npi') {
                            userData.accessSecurityIdentifierType = 'NPI';
                        } else if (manageConfig.auxilary_nursing_agency_identifier_type == 'auxilary_nursing_agency_id') {
                            userData.accessSecurityIdentifierType = 'staffId';
                        } else {
                            userData.accessSecurityIdentifierType = '';
                        }
                    }

                    if (manageConfig.auxilary_nursing_agency_worklist_label_for_manage_security_rules && manageConfig.auxilary_nursing_agency_worklist_label_for_manage_security_rules != "" && manageConfig.auxilary_nursing_agency_worklist_selected_for_manage_security_rules && manageConfig.auxilary_nursing_agency_worklist_selected_for_manage_security_rules != "") {
                        userData.accessSecurityWorklistLabel = manageConfig.auxilary_nursing_agency_worklist_label_for_manage_security_rules;
                        userData.accessSecurityWorklistSelected = manageConfig.auxilary_nursing_agency_worklist_selected_for_manage_security_rules;
                        this._structureService.getAccessSecurityWorklistActionLink(userData.crossTenantId, userData.accessSecurityWorklistSelected).then((worklist: any) => {
                            if(worklist.length > 0) {
                                userData.accessSecurityWorklistActionLink = worklist[0]['worklistActionLink'];
                                manageConfig.defaultPage = userData.accessSecurityWorklistActionLink;
                                userData.defaultPage = userData.accessSecurityWorklistActionLink;
                                this._structureService.loginUserDetailsForRouteCheck['defaultPage'] = userData.accessSecurityWorklistActionLink;
                                
                                this._structureService.userDetails = JSON.stringify(userData);                           
                                this._structureService.userDataConfig = JSON.stringify(userData.config);
                                this._structureService.setTelemetryObjectData();
                                this._crossTenantService.setInboxData(true);
                                this._SharedService.configuringTenantData = false;
                                if (userData.accessSecurityEnabled && userData.accessSecurityWorklistActionLink) {
                                    this.router.navigate([userData.accessSecurityWorklistActionLink]);
                                }
                            } else {
                                if (this.userData.routingPage && this.userData.routingPage != "") {
                                    if (enablePages.indexOf(this.userData.routingPage) != -1) {
                                        manageConfig.defaultPage = this.userData.routingPage;
                                        userData.defaultPage = this.userData.routingPage;
                                    } else {
                                        manageConfig.defaultPage = defaultPage;
                                        userData.defaultPage = defaultPage;
                                    }
                                }

                                this._structureService.loginUserDetailsForRouteCheck['defaultPage'] = defaultPage;
                                
                                this._structureService.userDetails = JSON.stringify(userData);                            
                                this._structureService.userDataConfig = JSON.stringify(userData.config);
                                this._structureService.setTelemetryObjectData();
                                this._crossTenantService.setInboxData(true);
                                this._SharedService.configuringTenantData = false;
                            }
                        });
                    } else {
                        
                        manageConfig = response['config'];
                        if (manageConfig.worklist_label_for_manage_security_rules && manageConfig.worklist_label_for_manage_security_rules != "" && manageConfig.worklist_selected_for_manage_security_rules && manageConfig.worklist_selected_for_manage_security_rules != "") {
                            userData.accessSecurityWorklistLabel = manageConfig.worklist_label_for_manage_security_rules;
                            userData.accessSecurityWorklistSelected = manageConfig.worklist_selected_for_manage_security_rules;
                            this._structureService.getAccessSecurityWorklistActionLink(userData.crossTenantId, userData.accessSecurityWorklistSelected).then((worklist: any) => {
                                if (worklist.length > 0) {
                                    userData.accessSecurityWorklistActionLink = worklist[0]['worklistActionLink'];
                                    manageConfig.defaultPage = userData.accessSecurityWorklistActionLink;
                                    userData.defaultPage = userData.accessSecurityWorklistActionLink;
                                    this._structureService.loginUserDetailsForRouteCheck['defaultPage'] = userData.accessSecurityWorklistActionLink;
                                    
                                    this._structureService.userDetails = JSON.stringify(userData);                                   
                                    this._structureService.userDataConfig = JSON.stringify(userData.config);
                                    this._structureService.setTelemetryObjectData();
                                    this._crossTenantService.setInboxData(true);
                                    this._SharedService.configuringTenantData = false;
                                    if (userData.accessSecurityEnabled && userData.accessSecurityWorklistActionLink) {
                                        this.router.navigate([userData.accessSecurityWorklistActionLink]);
                                    }
                                } else {
                                    if (this.userData.routingPage && this.userData.routingPage != "") {
                                        if (enablePages.indexOf(this.userData.routingPage) != -1) {
                                            manageConfig.defaultPage = this.userData.routingPage;
                                            userData.defaultPage = this.userData.routingPage;
                                        } else {
                                            manageConfig.defaultPage = defaultPage;
                                            userData.defaultPage = defaultPage;
                                        }
                                    }

                                    this._structureService.loginUserDetailsForRouteCheck['defaultPage'] = defaultPage;
                                    
                                    this._structureService.userDetails = JSON.stringify(userData);                           
                                    this._structureService.userDataConfig = JSON.stringify(userData.config);
                                    this._structureService.setTelemetryObjectData();
                                    this._crossTenantService.setInboxData(true);
                                    this._SharedService.configuringTenantData = false;
                                }
                            });

                        } else {
                            this._structureService.loginUserDetailsForRouteCheck['defaultPage'] = defaultPage;
                            
                            this._structureService.userDetails = JSON.stringify(userData);
                            this._structureService.userDataConfig = JSON.stringify(userData.config);
                            this._structureService.setTelemetryObjectData();
                            this._crossTenantService.setInboxData(true);
                            this._SharedService.configuringTenantData = false;
                        }
                    }                 
                    
                } else if (userData.isNursingAgency === '0') {
                    
                    manageConfig = response['config'];
                    userData.accessSecurityEnabled = true;

                    if (manageConfig.esi_code_for_auxilary_physician && manageConfig.esi_code_for_auxilary_physician != "" && manageConfig.auxilary_physician_roles_for_manage_security_rules && manageConfig.auxilary_physician_roles_for_manage_security_rules != "" && manageConfig.auxilary_physician_identifier_type && manageConfig.auxilary_physician_identifier_type != "") {
                        userData.accessSecurityType = 'physicianOrPrescriber';
                        if (manageConfig.auxilary_physician_identifier_type == 'auxilary_physician_npi') {
                            userData.accessSecurityIdentifierType = 'NPI';
                        } else if (manageConfig.auxilary_physician_identifier_type == 'auxilary_physician_id') {
                            userData.accessSecurityIdentifierType = 'staffId';
                        } else {
                            userData.accessSecurityIdentifierType = '';
                        }

                    }
                    if (manageConfig.auxilary_physician_worklist_label_for_manage_security_rules && manageConfig.auxilary_physician_worklist_label_for_manage_security_rules != "" && manageConfig.auxilary_physician_worklist_selected_for_manage_security_rules && manageConfig.auxilary_physician_worklist_selected_for_manage_security_rules != "") {
                        userData.accessSecurityWorklistLabel = manageConfig.auxilary_physician_worklist_label_for_manage_security_rules;
                        userData.accessSecurityWorklistSelected = manageConfig.auxilary_physician_worklist_selected_for_manage_security_rules;
                        this._structureService.getAccessSecurityWorklistActionLink(userData.crossTenantId, userData.accessSecurityWorklistSelected).then((worklist: any) => {
                            if (worklist.length > 0) {
                                userData.accessSecurityWorklistActionLink = worklist[0]['worklistActionLink'];
                                manageConfig.defaultPage = userData.accessSecurityWorklistActionLink;
                                userData.defaultPage = userData.accessSecurityWorklistActionLink;
                                this._structureService.loginUserDetailsForRouteCheck['defaultPage'] = userData.accessSecurityWorklistActionLink;
                                
                                this._structureService.userDetails = JSON.stringify(userData);                            
                                this._structureService.userDataConfig = JSON.stringify(userData.config);
                                this._structureService.setTelemetryObjectData();
                                this._crossTenantService.setInboxData(true);
                                this._SharedService.configuringTenantData = false;
                                if (userData.accessSecurityEnabled && userData.accessSecurityWorklistActionLink) {
                                    this.router.navigate([userData.accessSecurityWorklistActionLink]);
                                }
                            } else {
                                if (this.userData.routingPage && this.userData.routingPage != "") {
                                    if (enablePages.indexOf(this.userData.routingPage) != -1) {
                                        manageConfig.defaultPage = this.userData.routingPage;
                                        userData.defaultPage = this.userData.routingPage;
                                    } else {
                                        manageConfig.defaultPage = defaultPage;
                                        userData.defaultPage = defaultPage;
                                    }
                                }

                                this._structureService.loginUserDetailsForRouteCheck['defaultPage'] = defaultPage;
                                
                                this._structureService.userDetails = JSON.stringify(userData);                            
                                this._structureService.userDataConfig = JSON.stringify(userData.config);
                                this._structureService.setTelemetryObjectData();
                                this._crossTenantService.setInboxData(true);
                                this._SharedService.configuringTenantData = false;
                            }
                        });
                    } else {
                        
                        manageConfig = response['config'];
                        if (manageConfig.worklist_label_for_manage_security_rules && manageConfig.worklist_label_for_manage_security_rules != "" && manageConfig.worklist_selected_for_manage_security_rules && manageConfig.worklist_selected_for_manage_security_rules != "") {
                            userData.accessSecurityWorklistLabel = manageConfig.worklist_label_for_manage_security_rules;
                            userData.accessSecurityWorklistSelected = manageConfig.worklist_selected_for_manage_security_rules;
                            this._structureService.getAccessSecurityWorklistActionLink(userData.crossTenantId, userData.accessSecurityWorklistSelected).then((worklist:any) => {
                                if (worklist.length > 0) {
                                    userData.accessSecurityWorklistActionLink = worklist[0]['worklistActionLink'];
                                    manageConfig.defaultPage = userData.accessSecurityWorklistActionLink;
                                    userData.defaultPage = userData.accessSecurityWorklistActionLink;
                                    this._structureService.loginUserDetailsForRouteCheck['defaultPage'] = userData.accessSecurityWorklistActionLink;
                                    
                                    this._structureService.userDetails = JSON.stringify(userData);                          
                                    this._structureService.userDataConfig = JSON.stringify(userData.config);
                                    this._structureService.setTelemetryObjectData();
                                    this._crossTenantService.setInboxData(true);
                                    this._SharedService.configuringTenantData = false;
                                    if (userData.accessSecurityEnabled && userData.accessSecurityWorklistActionLink) {
                                        this.router.navigate([userData.accessSecurityWorklistActionLink]);
                                    }
                                } else {
                                    if (this.userData.routingPage && this.userData.routingPage != "") {
                                        if (enablePages.indexOf(this.userData.routingPage) != -1) {
                                            manageConfig.defaultPage = this.userData.routingPage;
                                            userData.defaultPage = this.userData.routingPage;
                                        } else {
                                            manageConfig.defaultPage = defaultPage;
                                            userData.defaultPage = defaultPage;
                                        }
                                    }

                                    this._structureService.loginUserDetailsForRouteCheck['defaultPage'] = defaultPage;
                                    
                                    this._structureService.userDetails = JSON.stringify(userData);                            
                                    this._structureService.userDataConfig = JSON.stringify(userData.config);
                                    this._structureService.setTelemetryObjectData();
                                    this._crossTenantService.setInboxData(true);
                                    this._SharedService.configuringTenantData = false;
                                }
                            });
                        } else {
                            this._structureService.loginUserDetailsForRouteCheck['defaultPage'] = defaultPage;
                            
                            this._structureService.userDetails = JSON.stringify(userData);                           
                            this._structureService.userDataConfig = JSON.stringify(userData.config);
                            this._structureService.setTelemetryObjectData();
                            this._crossTenantService.setInboxData(true);
                            this._SharedService.configuringTenantData = false;
                        }
                    }                   
                   
                } else {
                    
                    manageConfig = response['config'];
                    if (manageConfig.enable_patient_info_from_third_party_app && manageConfig.enable_patient_info_from_third_party_app != 0) {
                        userData.accessSecurityEnabled = false;
                    }
                    if (manageConfig.worklist_label_for_manage_security_rules && manageConfig.worklist_label_for_manage_security_rules != "" && manageConfig.worklist_selected_for_manage_security_rules && manageConfig.worklist_selected_for_manage_security_rules != "") {
                        userData.accessSecurityWorklistLabel = manageConfig.worklist_label_for_manage_security_rules;
                        userData.accessSecurityWorklistSelected = manageConfig.worklist_selected_for_manage_security_rules;
                        this._structureService.getAccessSecurityWorklistActionLink(userData.crossTenantId, userData.accessSecurityWorklistSelected).then((worklist: any) => {
                            if (worklist.length > 0) {
                                userData.accessSecurityWorklistActionLink = worklist[0]['worklistActionLink'];
                                this._structureService.userDetails = JSON.stringify(userData);                              
                                this._structureService.userDataConfig = JSON.stringify(userData.config);
                                this._structureService.setTelemetryObjectData();
                                this._crossTenantService.setInboxData(true);
                                this._SharedService.configuringTenantData = false;
                            } 
                            if (this.userData.routingPage && this.userData.routingPage != "") {
                                if (enablePages.indexOf(this.userData.routingPage) != -1) {
                                    manageConfig.defaultPage = this.userData.routingPage;
                                    userData.defaultPage = this.userData.routingPage;
                                } else {
                                    manageConfig.defaultPage = defaultPage;
                                    userData.defaultPage = defaultPage;
                                }
                            }

                            this._structureService.loginUserDetailsForRouteCheck['defaultPage'] = defaultPage;
                            
                            this._structureService.userDetails = JSON.stringify(userData);                          
                            this._structureService.userDataConfig = JSON.stringify(userData.config);
                            this._structureService.setTelemetryObjectData();
                            this._crossTenantService.setInboxData(true);
                            this._SharedService.configuringTenantData = false;
                        });
                    }  else {
                        this._structureService.loginUserDetailsForRouteCheck['defaultPage'] = defaultPage;
                        
                        this._structureService.userDetails = JSON.stringify(userData);                      
                        this._structureService.userDataConfig = JSON.stringify(userData.config);
                        this._structureService.setTelemetryObjectData();
                        this._crossTenantService.setInboxData(true);
                        this._SharedService.configuringTenantData = false;
                    }                   
                }
            } else {
                manageConfig.defaultPage = this.userData.routingPage;
                if(this.userData.routingPage && this.userData.routingPage != '')
                userData.defaultPage = this.userData.routingPage;
                var defaultPage = '/profile';
                var nursingAgencyUser = false;
                var enablePages = [];

                enablePages.push('/profile');
                if (manageConfig.enable_nursing_agencies_visibility_restrictions && manageConfig.enable_nursing_agencies_visibility_restrictions == '1' && manageConfig.nursing_agencies != "") {
                    nursingAgencyUser = true;
                }
                if (manageConfig.enable_message_center && manageConfig.enable_message_center == '1') {
                    defaultPage = '/inbox';
                } else if (manageConfig.show_document_tagging && manageConfig.show_document_tagging == '1') {
                    if ((userData.privileges.indexOf('viewAllSignedDocs') == -1 && userData.privileges.indexOf('manageTenants') == -1) || nursingAgencyUser) {
                        defaultPage = '/signature/signature-requests-list';
                    } else if ((userData.privileges.indexOf('viewAllSignedDocs') != -1 || userData.privileges.indexOf('manageTenants') != -1) && !nursingAgencyUser) {
                        defaultPage = '/signature/signed-documents-list';
                    } else {
                        defaultPage = this.permissionService.getDefaultPageFormCenter(manageConfig, userData.privileges);
                    }
                } else {
                    defaultPage = this.permissionService.getDefaultPageFormCenter(manageConfig, userData.privileges);
                }
                //get enablled pages.

                if (manageConfig.enable_message_center && manageConfig.enable_message_center == '1') {
                    enablePages.push('/inbox');
                }

                if (manageConfig.show_document_tagging && manageConfig.show_document_tagging == '1') {
                    if ((userData.privileges.indexOf('viewAllSignedDocs') == -1 && userData.privileges.indexOf('manageTenants') == -1) || nursingAgencyUser) {
                        enablePages.push('/signature/signature-requests-list');
                    }
                    if ((userData.privileges.indexOf('viewAllSignedDocs') != -1 || userData.privileges.indexOf('manageTenants') != -1) && !nursingAgencyUser) {
                        enablePages.push('/signature/signed-documents-list');
                    }
                    enablePages.push(this.permissionService.getDefaultPageFormCenter(manageConfig, userData.privileges));

                }
                enablePages.push(this.permissionService.getDefaultPageFormCenter(manageConfig, userData.privileges));

                //get enablled pages end.
                if (this.userData.routingPage && this.userData.routingPage != "") {
                    if (enablePages.indexOf(this.userData.routingPage) != -1) {
                        manageConfig.defaultPage = this.userData.routingPage;
                        userData.defaultPage = this.userData.routingPage;
                    } else {
                        manageConfig.defaultPage = defaultPage;
                        userData.defaultPage = defaultPage;
                    }
                }

                this._structureService.loginUserDetailsForRouteCheck['defaultPage'] = defaultPage;
                
                this._structureService.userDetails = JSON.stringify(userData);               
                this._structureService.userDataConfig =  JSON.stringify(userData.config);
                this._structureService.setTelemetryObjectData();
                this._crossTenantService.setInboxData(true);
                this._SharedService.configuringTenantData = false;                
            }

        });

        this._crossTenantService.setInboxData(false);
        this._structureService.trackActivity(activityData);
        /**for clearing data in pah */
        this._structureService.activePatientActivityDetails = [];
        this._structureService.activePatientActivityHub = false;
        this._structureService.activePatientActivityHubPatientId = '';
        this._structureService.activePatientActivityHubTabId = '';
        this._structureService.activePatientActivityHubFilterPahDetails = [];
        this._structureService.inboxMessageResultsCount = 0;
        localStorage.setItem("messageInboxPagination", '1');
        localStorage.setItem("inboxTotalMessageCount", '0');
        localStorage.setItem("pageCountMessage", '0');
        $('.cat__menu-left__action--menu-toggle').on('click', function () {
            if ($('body').width() < 768) {
                $('body').toggleClass('cat__menu-left--visible--mobile');
            } else {
                $('body').toggleClass('cat__menu-left--visible');
                if (!$('body').hasClass('cat__menu-left--visible')) {
                    $('body .cat__menu-left').addClass("toggleMenuClick");
                }
            }
        });
        this._SharedService.patientActivityHubTab = {};
    }

infoBottomHide() {
    $(".info-overlay").css("display", "none");
}

startIntro() {
    this._SharedService.startIntro.emit({ value: true });
}

showPatientListModal() {
    $('#top-bar-patient-list-modal').modal('show');
    if(this.admissionSearchInput) this.admissionSearchInput.nativeElement.value = '';
}

checkMultiplePatient(groupId) {
    this.userGroupId = groupId;
    this.clearAdmissionsData();
    if (this.userData.alternate_contact_patient && this.userData.alternate_contact_patient.length > 1) {
        this.showAdmissionsList = false;
        this.showPatientListModal();
    } else {
        this.associatedUserId = null;
        if (this.userData.alternate_contact_patient && this.userData.alternate_contact_patient.length == 1) {
            this.associatedUserId = this.userData.alternate_contact_patient[0].userId;
        }
        if(this._structureService.isMultiAdmissionsEnabled && +groupId === 3) {
            if(!isBlank(this.userData.alternate_contact_patient)) {
                this.getAdmissionsList(this.userData.alternate_contact_patient[0]['userId'], false, '', true);
            } else {
                this.getAdmissionsList(this.userData.userId, false, '', true);
            }
        } else {
            this.chatWithModel(groupId, this.associatedUserId);
        }
    }
}

chatWithModel(groupId, associatedUserId = '', admissionId = '') {
    this.selectedAdmissionId = admissionId;
    this.selectSiteId = '0';
    if ((this.userData.alternate_contact_patient && this.userData.alternate_contact_patient.length > 1) || !isBlank(admissionId)) {
        if ($('#top-bar-patient-list-modal').hasClass('show')) {
          $('#top-bar-patient-list-modal').modal('hide');
        }
    }

    if (!groupId) {
        groupId = this.userGroupId;
    }

    this.infoBottomHide();
    if (groupId != 3) {
        $('#chatWithModel').modal('show');
        $('#chat-with-modal-search-box').val('');
        $('#chat-with-modal-search-box').focus();
        console.log('Modal Popup Triggered');
        console.log('initalised');
        this.showData('');
       

    } else {
        var self = this;
        var filteredMasterStaffs = [];
        this.usersListScheduleCheck = [];
//  Message Routing
if (this.userData.config.enable_multisite == "1" || this.userData.config.enable_multisite == "0" || this.userData.config.enable_multisite == "") {
    this.associatedPatientId = associatedUserId;
    if (isBlank(this.associatedPatientId) && this.userData.alternate_contact_patient && this.userData.alternate_contact_patient.length == 1) {
        this.associatedPatientId = this.userData.alternate_contact_patient[0].userId;
    }
    const requestParams = {};
    if(!isBlank(this.associatedPatientId)) {
        requestParams['associatedPatientId'] = this.associatedPatientId;
    }
    if(this._structureService.isMultiAdmissionsEnabled) {
        requestParams['admissionId'] = admissionId;
    }
    this.httpService.doPost(APIs.getPatientMessageRouting, requestParams).subscribe((response: any) => {
        const data = response.data;
        if (data.status == 'success' && data.chatRoomId) {
            this.noMoreItemsAvailable.users = false;
            let targetId: any = data.chatRoomId;
            let activeMessageData: { [k: string]: any } = {};
            let userId = self.userData.userId;
            activeMessageData = {
                chatWithUserId: 0,
                chatWith: '',
                chatWithGrp: '',
                createdby: userId,
                chatroomid: data.chatRoomId,
                chatwithUserRole: '',
                chatwithDob: '',
                selectedTenantId: '',
                is_patient_discussion_group: '',
                chatWithRoleId: '',
                message_group_id: 0
            };
            let targetName = 'group-chat';
            var activityData = {
                activityName: "Start Chat Session",
                activityType: "messaging",
                activityLinkageId: targetId,
                activityDescription: "Chat With - " + data.chatHeading + " in Chatroom " + targetId
            };
    
            self._structureService.trackActivity(activityData);
            localStorage.setItem('targetId', targetId);
            localStorage.setItem('targetName', targetName);
            localStorage.setItem('chatWithHeading', "");
            localStorage.setItem('activeMessage', JSON.stringify(activeMessageData));
            localStorage.setItem('archived', 'false');
            if (this._structureService.currentUrlNow != '/inbox/chatroom') {
                this.router.navigate(['/inbox/chatroom']);
            } else {
                this._SharedService.reloadChatroomWithNewThread.emit();
            }
            setTimeout(() => {
            if(data.dobuleVarifiacaion && data.dobuleVarifiacaion == 1){
                this._SharedService.setDoubleVarification.emit();
            }
        },1000);
        } else if (data && data.roleList && data.roleList.length) {
            this.optionShow = '';
            this.doubleVerificationStatus = false;
            $('#chatWithModel').modal('show');
            $('#chatWithModalTab').hide();
            $('#chatWithWrapperFilter').hide();
            this.userRoleList = data.roleList;
            if(data.dobuleVarifiacaion && data.dobuleVarifiacaion == 1){
                this.doubleVerificationStatus = true;
            }
            if(this.userRoleList.length != 20) {
            this.noMoreItemsAvailable.users = true;
            }
            this.chatWithLoader.role = false;
        } else if(data.status == 'no staffs') {
            this.noStaffsAvailable = data.message;
            var notify = $.notify(data.message);
            setTimeout(function () {
                notify.update({ 'type': 'warning', 'message': data.message });
            }, 1000)
        }
    });
    } else {
        this.masterStaffsAvalable = JSON.parse(localStorage.getItem('masterStaffsAvalable'))?JSON.parse(localStorage.getItem('masterStaffsAvalable')):[];
        this.defaultMasterStaffsAvalable = JSON.parse(localStorage.getItem('defaultMasterStaffsAvalable'))?JSON.parse(localStorage.getItem('defaultMasterStaffsAvalable')):[];
        if(this.userData.group === "3" && this.userData.isMaster === "0" && this.userData.master_details){
            filteredMasterStaffs = this.masterStaffsAvalable;      

            if(!filteredMasterStaffs.length) {
                filteredMasterStaffs = this.defaultMasterStaffsAvalable;
            }
        }
        
        if (this.clinicianLoad) {
            if (this._inboxService.checkInfusionHours(false).isWorkingHours && !this.scheduledPrimaryCar) {
                if (this.usersList && this.usersList.length) {
                    for (var _i = 0; _i < this.usersList.length; _i++) {
                        if (this._inboxService.scheduleSelectionFilter(this.usersList[_i])) {
                            this.usersListScheduleCheck.push(this.usersList[_i]);
                        }
                    }
                    this.usersList = this.usersListScheduleCheck;
                    this.userListChatwith = this.modalFilter.transform(this.usersList, 'staff');
                    console.log("this.userListChatwith", this.userListChatwith);
                    if (this.userListChatwith.length === 0) {

                        
                        this._inboxService.trackUserUnableToContact('chat with us', 'no Clinicians scheduled', associatedUserId);
                    } else {
                        if (this.configData.show_chatwith_modal == '1') {                            
                            this.chatWith = true;
                            $('#chatWithModel').modal('show');
                            console.log('initalised2');
                            $('#chat-with-modal-search-box').val('');
                            $('#chat-with-modal-search-box').focus();
                            this.showData('');
                        }else{
                            if (!this.userData.master_config || (this.userData.onfig && this.userData.config.flex_site_patients_can_chat_with_internal_staffs == 1)) {
                                for (var _i = 0; _i < filteredMasterStaffs.length; _i++) {                                    
                                    this.usersList.push(filteredMasterStaffs[_i]);
                                }
                            }

                            let chatWithHeading:any = '';
                            if(this.usersList[0].displayname){
                                chatWithHeading = 'Chat with ' + this.usersList[0].displayname;
                                chatWithHeading = chatWithHeading+", "+this.usersList[0].role;
                            } else {
                                chatWithHeading = 'Chat with ' + this.usersList[0].role;
                            }

                            var chatWithRoleSelected = this.usersList[0].role;
                            localStorage.setItem('chatWithHeading', chatWithHeading);
                            localStorage.setItem('chatWithHeadingHistory', chatWithHeading);
                            chatWithRoleSelected = chatWithRoleSelected.replace(/^Chat with+/i, '');
                            var createdWithUserIds: any = [];
                            var createdWithUserNames: any = [];
                            for (var i = 0; i < this.usersList.length; i++) {
                                createdWithUserIds.push(this.usersList[i].userId);
                                createdWithUserNames.push(this.usersList[i].displayname);
                            }
                            createdWithUserIds = createdWithUserIds.join();
                            createdWithUserNames = createdWithUserNames.join();
                            const params: CreateChatroomParameters = {
                                chatWith: this.getChatWithOption(),
                                createdWith: this.usersList,
                            }
                            this._inboxService.createChatroom(params).subscribe((response: any) => {
                                if(response.success && response.data && response.data.roomId) {
                                const chatroomId = response.data.roomId;
                                let activeMessageData: { [k: string]: any } = {};
                                activeMessageData = {
                                    chatWithUserId: createdWithUserIds,
                                    chatWith: createdWithUserNames,
                                    chatroomid: chatroomId,
                                    chatwithUserRole: chatWithRoleSelected,
                                    chatwithDob: ''
                                };
                                localStorage.setItem('activeMessage', JSON.stringify(activeMessageData));
                                let targetID: any = chatroomId;
                                localStorage.setItem('targetId', targetID);
                                localStorage.setItem('targetName', 'group-chat');
                                localStorage.setItem('archived', 'false');
                                this.router.navigate(['/blank']);
                                } else {
                                    this._structureService.notifyMessage({messge: response.status.message});
                                }
                            },() => {
                                this.notifyService.notifyAPIException();
                            });
                        }
                    }
                } else {
                    console.log('No Clinicians');
                    
                    this._inboxService.trackUserUnableToContact('chat with us', 'no Clinicians scheduled', associatedUserId);                    
                }
            } else {
                console.log('No Clinicians');
               
                this._inboxService.trackUserUnableToContact('chat with us', 'no Clinicians scheduled', associatedUserId);
            }
        } else if(this.scheduledPrimaryCar){
                this.usersList = this.primaryCareClinicians;
                this.userListChatwith = this.modalFilter.transform(this.usersList, 'staff');
                this.chatWith = true;
                this.modalHead = 'Chat With';
                $('#chatWithModel').modal('show');
                console.log('initalised');
                $('#chat-with-modal-search-box').val('');
                $('#chat-with-modal-search-box').focus();
                this.showData('');
        } else {
            this.usersListByRoleId = localStorage.getItem("usersListByRoleId") ? JSON.parse(localStorage.getItem("usersListByRoleId")) : this.usersListByRoleId;
            
            if (this.usersListByRoleId && this.usersListByRoleId.length) {
                for (var _i = 0; _i < this.usersListByRoleId.length; _i++) {
                    if (this._inboxService.scheduleSelectionFilter(this.usersListByRoleId[_i])) {
                        this.usersListScheduleCheck.push(this.usersListByRoleId[_i]);
                    } else {
                    }
                }

                this.usersListByRoleId = [];
                this.usersListByRoleId = this.usersListScheduleCheck;
                if (this.usersListByRoleId.length === 0) {
                   
                    console.log('this.usersListByRoleId.length = 0');
                    this._inboxService.trackUserUnableToContact('chat with us', 'no Clinicians scheduled', associatedUserId);
                } else {
                    let chatWithHeading = "Chat with " + this.userData.firstBeyondWorkingNursesRoleName ? this.userData.firstBeyondWorkingNursesRoleName : "On-Call Nurse";
                    let chatWithRoleSelected = this.userData.firstBeyondWorkingNursesRoleName ? this.userData.firstBeyondWorkingNursesRoleName : "On-Call Nurse";
                    localStorage.setItem('chatWithHeading', '');
                    localStorage.setItem('chatWithHeadingHistory', chatWithHeading);
                    chatWithRoleSelected = chatWithRoleSelected.replace(/^Chat with+/i, '');
                    let createdWithUserIds: any = [];
                    let createdWithUserNames: any = [];

                    for (let _i = 0; _i < filteredMasterStaffs.length; _i += 1) {                                    
                        this.usersListByRoleId.push(filteredMasterStaffs[_i]);
                    }
                    
                    for (var i = 0; i < this.usersListByRoleId.length; i++) {
                        createdWithUserIds.push(this.usersListByRoleId[i].userId);
                        createdWithUserNames.push(this.usersListByRoleId[i].displayname);
                    }

                    createdWithUserIds = createdWithUserIds.join();
                    createdWithUserNames = createdWithUserNames.join();
                    const params: CreateChatroomParameters = {
                        chatWith: this.getChatWithOption(),
                        createdWith: this.usersListByRoleId,
                        createdByAssociatedId: +associatedUserId,
                    }
                    this._inboxService.createChatroom(params).subscribe((response: any) => {
                        if(response.success && response.data && response.data.roomId) {
                            const chatroomId = response.data.roomId;
                            let activeMessageData: { [k: string]: any } = {};                             
                            activeMessageData = {
                                chatWithUserId: createdWithUserIds,
                                chatWith: createdWithUserNames,
                                chatroomid: chatroomId,
                                chatwithUserRole: chatWithRoleSelected,
                                chatwithDob: '',
                                message_group_id: 0
                            };
    
                            localStorage.setItem('activeMessage', JSON.stringify(activeMessageData));
                            let targetID: any = chatroomId;
                            localStorage.setItem('targetId', targetID);
                            localStorage.setItem('targetName', 'group-chat');
                            localStorage.setItem('archived', 'false');
                            if(self._structureService.currentUrlNow != '/inbox/chatroom') {
                                self.router.navigate(['/inbox/chatroom']);
                            } else {
                                self._SharedService.reloadChatroomWithNewThread.emit();
                            } 
                        } else {
                            if(response.status) {
                                this._structureService.notifyMessage({
                                    messge: response.status.message,
                                });
                            } else {
                                this.notifyService.notifyAPIException();
                            }
                        }
                    }, () => {
                        this.notifyService.notifyAPIException();
                    });
                }
            } else {
                
                this._inboxService.trackUserUnableToContact('chat with us', 'no Clinicians assigned', associatedUserId);
            }
        }
    }
    }
}        
setChatWithTenantFilterSelect() {
    if(this.selectedTenant) {
        this.chatWithTenantFilter.selectedTenant =  this.selectedTenant.id;
    } else {
        this.chatWithTenantFilter.selectedTenant = this._structureService.getCookie('tenantId');
    }
    switch(this.optionShow) {
        case 'staff': {
            if (this.userData.organizationMasterId != 0 && this.userData.crossTenantsDetails && this.userData.crossTenantsDetails.length > 1) {
                if((this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
                && this.configData.allow_multiple_organization==1 
                && this.userData.crossTenantsDetails.length > 1 
                && this.userData.masterEnabled == '0'
                && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
            || (this.userData.masterEnabled == '1' 
                && this.userData.isMaster == '1')
            || (this.userData.masterEnabled == '1' 
                && this.userData.isMaster == '0'
                && this.userData.group !='3')
            || (this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
                && this.configData.allow_multiple_organization==1 
                && this.userData.crossTenantsDetails.length > 1 
                && this.userData.masterEnabled == '0'
                && this.configData.enable_nursing_agencies_visibility_restrictions == '1'
                && this.userData.nursing_agencies == '')) {
                    this.chatWithTenantFilter.tenants = this.userData.crossTenantsDetails;
                    if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1') {
                        this.internalSiteId = this.userData.master_details.id;
                    }
                    this.chatWithTenantFilter.tenants = this.chatWithTenantFilter.tenants.filter((tenant)=> {
                        if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1'){
                            if(this.internalSiteId == tenant.id) {
                                return true;
                            } else {
                                return false;
                            }
                        } else {
                            return true;
                        }
                    });
                    if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1') {
                        if(this.userData.master_details.id != this.userData.tenantId) {
                            this.crossTenantOptionsChatWith.map((tenant)=> {
                                if(tenant.id == this.userData.tenantId) {
                                    this.chatWithTenantFilter.tenants.unshift(tenant);
                                }
                            });
                        }
                    }
                    this.assignChatWithTenantFilterData(); 
                } else {
                    this.chatWithTenantFilter.filterEnabled = false;
                    this.chatWithTenantFilter.tenants = [];
                }
            } else {
                this.chatWithTenantFilter.filterEnabled = false;
                this.chatWithTenantFilter.tenants = [];
            }
            break;
        }
        case 'partner': {
            if (this.userData.organizationMasterId != 0 && this.userData.crossTenantsDetails && this.userData.crossTenantsDetails.length > 1) {
                if((this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
                && this.configData.allow_multiple_organization==1 
                && this.userData.crossTenantsDetails.length > 1 
                && this.userData.masterEnabled == '0'
                && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
            || (this.userData.masterEnabled == '1' 
                && this.userData.isMaster == '1')
            || (this.userData.masterEnabled == '1' 
                && this.userData.isMaster == '0'
                && this.userData.group !='3')
            || (this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
                && this.configData.allow_multiple_organization==1 
                && this.userData.crossTenantsDetails.length > 1 
                && this.userData.masterEnabled == '0'
                && this.configData.enable_nursing_agencies_visibility_restrictions == '1'
                && this.userData.nursing_agencies == '')) {
                    this.chatWithTenantFilter.tenants = this.userData.crossTenantsDetails;
                    if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1') {
                        this.internalSiteId = this.userData.master_details.id;
                    }
                    this.chatWithTenantFilter.tenants = this.chatWithTenantFilter.tenants.filter((tenant)=> {
                        if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1'){
                            if(this.internalSiteId == tenant.id) {
                                return true;
                            } else {
                                return false;
                            }
                        } else {
                            return true;
                        }
                    });
                    if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1') {
                        if(this.userData.master_details.id != this.userData.tenantId) {
                            this.crossTenantOptionsChatWith.map((tenant)=> {
                                if(tenant.id == this.userData.tenantId) {
                                    this.chatWithTenantFilter.tenants.unshift(tenant);
                                }
                            });
                        }
                    }
                    this.assignChatWithTenantFilterData(); 
                } else {
                    this.chatWithTenantFilter.filterEnabled = false;
                    this.chatWithTenantFilter.tenants = [];
                }
            } else {
                this.chatWithTenantFilter.filterEnabled = false;
                this.chatWithTenantFilter.tenants = [];
            }
            break;
        }
        case 'patient': {
            if(
                (this.userData.organizationMasterId != 0 
                && this.userData.crossTenantsDetails.length > 1 
                && this.privileges.indexOf('allowOrganizationSwitching') != -1 
                && this.privileges.indexOf('allowMultipleOrganizationsPatientCommunication') != -1 
                && this.configData.allow_multiple_organization == 1 
                && this.userData.masterEnabled != 1 
                && this.configData.enable_nursing_agencies_visibility_restrictions != 1
                )
                ||
                (this.userData.organizationMasterId != 0 
                && this.userData.crossTenantsDetails.length > 1 
                && this.userData.masterEnabled == '1' 
                && this.userData.isMaster =='1')
                ||
                (this.userData.organizationMasterId != 0 
                && this.userData.crossTenantsDetails.length > 1 
                && this.userData.isMaster =='0' 
                && this.userData.masterEnabled == '1' 
                && this.userData.group != '3')
                ||
                (this.configData.enable_nursing_agencies_visibility_restrictions == 1 
                && this.userData.nursing_agencies == "" 
                && this.userData.organizationMasterId != 0 
                && this.userData.crossTenantsDetails.length > 1 
                && this.privileges.indexOf('allowOrganizationSwitching') != -1 
                && this.privileges.indexOf('allowRoleTenantSwitching') != -1 
                && this.configData.allow_multiple_organization == 1 
                && this.userData.masterEnabled == '0'       
                )
            ) {
                if(this.isOtherTenantPatients) {
                    this.assignChatWithTenantFilterData();
                    this.chatWithTenantFilter.tenants = this.userData.crossTenantsDetails;
                    let tenantToBeSelectedIndex = this.chatWithTenantFilter.tenants.findIndex(tenant => tenant.id == this.chatWithTenantFilter.selectedTenant);
                    if(tenantToBeSelectedIndex != -1) {
                        let tenantToBeSelected = this.chatWithTenantFilter.tenants[tenantToBeSelectedIndex];
                        this.chatWithTenantFilter.tenants.splice(tenantToBeSelectedIndex, 1);
                        this.chatWithTenantFilter.tenants.unshift(tenantToBeSelected);
                    }
                } else {
                    this.chatWithTenantFilter.filterEnabled = false;
                    this.chatWithTenantFilter.tenants = [];
                    if(this.chatWithTenantFilter.tenants.length) {
                        this.chatWithTenantFilter.filterEnabled = true;
                        this.assignChatWithTenantFilterData();
                    }
                }
            } else {
                this.chatWithTenantFilter.filterEnabled = false;
                this.chatWithTenantFilter.tenants = [];    
            }
            break;
        }
        default: {
            this.chatWithTenantFilter.filterEnabled = false;
            this.chatWithTenantFilter.tenants = [];
            break;
        } 
    }
}
assignChatWithTenantFilterData() {
    if(this.userData.config && this.userData.config.enable_multisite && this.userData.config.enable_multisite != 1){
    this.chatWithTenantFilter.filterEnabled = true;
    }

    if($("#chatWithTenantFilter").length) {
        setTimeout( ()=> {
            $('#chatWithTenantFilter').select2({
                dropdownParent: $("#chatWithModel")
            });
            $("#chatWithTenantFilter").css("text-overflow", "ellipsis");
            $("#chatWithTenantFilter").val(this.chatWithTenantFilter.selectedTenant).trigger("change");
        });
    } else {
        this.chatWithTenantFilter.setTenantDropDown = true;
    }
}
   showData(data,changetab=false){
      this.showChatBtn = false;
      this.showSubject = false;
      this.resetSearchSelect = true;
      this.resetSearchSelectMsg = false;
      if(changetab){
          this.preventMultipleCall = true;
       }
        this.tabSelectedData = data;
        if(this.tabSelectedData == "msggroups" || this.tabSelectedData == "groups"){
            this.searchPdgSiteIds = this.userData.mySites.map(item => item.id).toString();
            if(+this.selectSiteId !== 0) {
                this.searchPdgSiteIds = this.selectSiteId.toString();
            }
            this.previoustabSelected = this.tabSelectedData;
            this.tabChange = true;
            this.emitEventToSelectSites('closePopup'); // Emit event to site component for resetting site filter on tab change
            this.msgGrpSubjectOffset = CONSTANTS.contentOffset;
            this.page = 0;
        }
        this.userListChatwith = [];
        this.clinicalUserDetails = [];
        this.usersList = [];
        var initialData = data;
        var tabSelectedFrom = '';
        this.chatRoomUsers = [];
        if(data) {
            tabSelectedFrom = this.optionShow;
        }
        this.chatWithTenantFilter.enabledReset = true;
        if(data==''){
            if(this.privileges.indexOf('chatWithClinician')!==-1) {
                data = 'staff'; 
            } else if(this.privileges.indexOf('chatWithMessageGroups')!==-1) {
                data = 'groups';            
            } else if(this.privileges.indexOf('chatWithPatients') != -1) {
                data = 'patient';
            }
        }
        console.log('data', data);
        this.optionShow=data;
        if(initialData == '') {       
            if(!this.preventMultipleCall)
                this.preventMultipleCall = false; 
            if(this.userData.roleId != '3') {
                if(this.configData.default_category_of_chat_window_popup == 'patient' 
                    && this.privileges.indexOf('chatWithPatients') != -1) {
                    this.optionShow = 'patient';
                }else if(this.configData.default_category_of_chat_window_popup == 'staff' 
                    && this.privileges.indexOf('chatWithClinician') != -1){
                    this.optionShow = 'staff'; 
                }else if(this.configData.default_category_of_chat_window_popup == 'groups' 
                    && this.privileges.indexOf('chatWithMessageGroups')!==-1){
          this.optionShow = 'msggroups';
        } else if (
          this.configData.default_category_of_chat_window_popup === 'pdg' &&
          this.privileges.indexOf('chatWithMessageGroups') !== -1 &&
          +this.configData.allow_virtual_patient === 1
        ) {
          this.optionShow = 'groups';
                }
            }
        }
        this.tabSelectedData = this.optionShow;
        if(this.optionShow == 'staff' || this.optionShow == 'partner' || this.optionShow == 'patient') {
            this.chatWithTenantFilter.enabledReset = false;
            this.setChatWithTenantFilterSelect();
            this.chatWithTenantFilterDetails = [];
            this.chatWithTenantFilter.tenants.map((tenantData) => {
                console.log("chatWithTenantFilter", tenantData);

             let chatWithTenantFilterDetails = {
                id: null,
                name: "",                
                tenantId: null,                
             }  
              chatWithTenantFilterDetails.id = tenantData.siteId;
              chatWithTenantFilterDetails.name = tenantData.siteName;
              chatWithTenantFilterDetails.tenantId = tenantData.id;              
             this.chatWithTenantFilterDetails.push(chatWithTenantFilterDetails);

            });            
            
            this._GlobalDataShareService.setchatWithTenantFilterDetails(this.chatWithTenantFilterDetails);
          //  console.log("chatWithTenantFilter", this.chatWithTenantFilterDetails);
          this.tabChange = true;
          this.emitEventToSelectSites('closePopup'); // Emit event to site component for resetting site filter on tab change
        }
    
        console.log("this.optionShow", this.optionShow);
        if(this.optionShow=='staff'){
             if(((this.previoustabSelected != 'msggroups') && (this.previoustabSelected != 'groups')) || this.multiSiteEnable || (!this.multiSiteEnable && !this.chatWithTenantFilter.filterEnabled)){
            this.initialiseStaffOrPatientList(this.optionShow);
        }
            
        }else if(this.optionShow=='partner'){
            if(((this.previoustabSelected != 'msggroups') && (this.previoustabSelected != 'groups')) || this.multiSiteEnable || (!this.multiSiteEnable && !this.chatWithTenantFilter.filterEnabled)){
                this.initialiseStaffOrPatientList(this.optionShow);
            }
           
        }else if(this.optionShow=='otherTenantstaff'){
            setTimeout(() => {
                if (this.otherTenantClinicianDataTenantWise.length) {
                    console.log(this.otherTenantClinicianDataTenantWise);
                    let id = this.otherTenantClinicianDataTenantWise[this.otherTenantClinicianDataTenantWise.length-1].tenantId;
                    if(!$('#staffAccordion'+id).hasClass('fa-minus')){
                        this.callAccordion(this.otherTenantClinicianDataTenantWise[this.otherTenantClinicianDataTenantWise.length-1].tenantId,'');
                    }
                }
            }, 500);            
        } else {
            if(((this.previoustabSelected != 'msggroups') && (this.previoustabSelected != 'groups')) || this.multiSiteEnable || (!this.multiSiteEnable && !this.chatWithTenantFilter.filterEnabled)){
            if((this.userData.organizationMasterId != 0
                && this.userData.crossTenantsDetails.length > 1 
                && this.privileges.indexOf('allowMultipleOrganizationsPatientCommunication') != -1 
                && this.configData.allow_multiple_organization==1 
                && this.userData.masterEnabled == '0')
                && this.configData.enable_nursing_agencies_visibility_restrictions != 1
            || (this.crossTenantOptionsChatWith.length > 1 
                && this.userData.isMaster =='1' 
                && this.userData.masterEnabled == '1')){
                
                            
                this.initialiseStaffOrPatientList(this.optionShow);
            } else if(this.configData.enable_nursing_agencies_visibility_restrictions == 1){
                if(this.userData.nursing_agencies == "") {
                    if((this.userData.organizationMasterId != 0 
                        && this.userData.crossTenantsDetails.length > 1 
                        && this.privileges.indexOf('allowMultipleOrganizationsPatientCommunication') != -1 
                        && this.configData.allow_multiple_organization==1 
                        && this.userData.masterEnabled == '0')) {
                            setTimeout(()=>{
                                this.initialiseStaffOrPatientList(this.optionShow);
                                if(this.chatWithTenantFilter.setTenantDropDown && this.chatWithTenantFilter.selectedTenant) {
                                    this.chatWithTenantFilter.setTenantDropDown = false;
                                    setTimeout( ()=> {
                                        $('#chatWithTenantFilter').select2({
                                            dropdownParent: $("#chatWithModel")
                                        });
                                        $("#chatWithTenantFilter").css("text-overflow", "ellipsis");
                                        $("#chatWithTenantFilter").val(this.chatWithTenantFilter.selectedTenant).trigger("change");
                                    });
                                }                
                            },100);
                    } else {
                        this.initialiseStaffOrPatientList(this.optionShow);
                    }

                } else {
                    this.initialiseStaffOrPatientList(this.optionShow);
                }
                
            } else{
                this.initialiseStaffOrPatientList(this.optionShow);
            }
        }
        }        
        var activityData = {
            activityName: "Chat With Tab Switching",
            activityType: "messaging",
            activityDescription: this.userData.displayName + " (" + this.userData.userId + ") has selected tab " + this.optionShow + ((tabSelectedFrom) ? (' from tab '+ tabSelectedFrom) : ''),
            tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
        };
        this._structureService.trackActivity(activityData);
  }
    closedropdownDiv(Rowindex) {
        $('#' + Rowindex).hide();
        this.selectedIndex = "";

    }
    /**
     * To initiate chat with patient who does have alternate contact
     * Chat can be initiated if the user does have any contact method.
     * @param user json object with user's data
     */ 
    initiateChatWithPatient(user) {        
        if (user) {
            this.ChatAlternate(user, !isBlank(user.contactId) ? user.contactId : '');
        }
    }
       
    expand(Rowindex,direct){

        if(direct){
            $(".expand-me").show();
            $(".shrink-me").hide();
            $(".allsub").hide();
            $(".expand-"+Rowindex).hide();
            $(".shrink-"+Rowindex).show();
            $(".subul-"+Rowindex).show();
        }
        
    }
    shrink(Rowindex){
       
        $(".allsub").hide();
        $(".shrink-me").hide();
        $(".expand-me").show();
        $(".shrink-"+Rowindex).hide();
        $(".expand-"+Rowindex).show();
        $(".subul-"+Rowindex).hide(); 
    }



    ChatAlternate(selectedUser, Rowindex) {
        const alternateUser = selectedUser.alternateContacts
        .find((item) => { return item.contactId === Rowindex });
        if(!isBlank(alternateUser) && alternateUser.noContactAvailable) {
            return false;
        }
        let selectedAlternates = [];
         if(Rowindex!='')
         {
            selectedAlternates.push(Rowindex);
         }
        this.selectedModal(selectedUser, selectedAlternates)
    }
    ToggleButton(ind, selectedUser) {

        setTimeout(() => {
            this.ToggleButtonCall(ind, selectedUser);

        }, 2000);
        $("#alternateContacts"+this.selectedIndex).select2({
            tags: true,
            dropdownParent: $("#chatWithModel"),
            allowClear: true,
            multiple: true,
            placeholder: 'Choose Contacts',
            dropdownCssClass: 'search-msg-grp'
        });

    }
    ToggleButtonCall(ind, selectedUser) {
        this.showSelected = !this.showSelected;
        this.selectedIndex = ind;
        if (this.showSelected) {
            $('#' + ind).show();
        }
        else {
            $('#' + ind).hide();
        }
        console.log('user is');
        console.log(selectedUser);
        var self = this;
        var res= [];
        res ['data'] = selectedUser.alternateContacts;
            console.log('response is');
            console.log(res['data']);
            let tmp = [];
            if (res['data']) {
                for (let i = 0; i < Object.keys(res['data']).length; i++) {
                    console.log('data is');
                    console.log(res['data'][i]);
                    tmp.push({ contactId: res['data'][i].contactId, name: res['data'][i].firstName + ' ' + res['data'][i].lastName });
                    console.log('tmp is');
                    console.log(tmp);

                }
                self.dropdownList = tmp;
            }
            console.log('dropdownlist printing');
            console.log(self.dropdownList);
            self.multipleDrop = true;

    }

  initialiseStaffOrPatientList(optionShow) {
    if(this.chatWithTenantFilter.setTenantDropDown && this.chatWithTenantFilter.selectedTenant) {
        this.chatWithTenantFilter.setTenantDropDown = false;
        setTimeout( ()=> {
            $('#chatWithTenantFilter').select2({
                dropdownParent: $("#chatWithModel")
            });
            $("#chatWithTenantFilter").val(this.chatWithTenantFilter.selectedTenant).trigger("change");
            $(".top-bar #chatWithTenantFilter").css("text-overflow", "ellipsis");
        }, 2000);
    }
    console.log('initialiseStaffOrPatientList.optionShow',optionShow);
    this.loadMoreSearchValue = undefined;
    this.callFromInitialCountChatWithUserlist = true;
    this.chatwithPageCount = {
        staffs: 0,
        patients: 0,
        partner: 0
    }
    this.noMoreItemsAvailable = {
        users: false
    };
    this.userListChatwith = [];
    this.clinicalUserDetails = [];
    this.usersList = [];
    var isRoleAvailable = true;
    var clinicianRolesAvaiable = null;
    var setCliniciansRoleAvailableResponse;
    if (this.userData.group == 3) {
        this.setCliniciansRoleAvailableOnClick({isRoleAvailable:isRoleAvailable,clinicianRolesAvaiable:clinicianRolesAvaiable},true,false,optionShow,undefined).then( (res)=>{
            setCliniciansRoleAvailableResponse = res;
            isRoleAvailable = setCliniciansRoleAvailableResponse.isRoleAvailable;
            clinicianRolesAvaiable = setCliniciansRoleAvailableResponse.clinicianRolesAvaiable;
            if(isRoleAvailable) {
                this.setuserListParams(clinicianRolesAvaiable,true,false, optionShow, undefined)
            } else {
                this.chatWithUsersLoading = false;
            }
        })                
        /** ***********************End Section ****************** */
    } else {
        if(isRoleAvailable) {
            this.setuserListParams(clinicianRolesAvaiable,true,false, optionShow, undefined);
        } else {
            this.chatWithUsersLoading = false;
        }
    }
  }
    selectedAlternate(selectedUser) {
       
      console.log("selectedUser================> ", selectedUser);
       var self = this;
        this._inboxService.getAlternatecontacts(selectedUser.tenantId, selectedUser.userid).then(function (res) {
            console.log('response is');
            console.log(res['data']);
            let tmp =[];
            if(res['data'])
            {
                for(let i=0; i < Object.keys(res['data']).length; i++) {
                    console.log('data is');
                    console.log(res['data'][i]);
                    tmp.push({ contactId: res['data'][i].contactId, name:res['data'][i].firstName+' '+res['data'][i].lastName });
                    console.log('tmp is');
                    console.log(tmp);

                }
               self.dropdownList = tmp;
            }
            console.log('dropdownlist printing');
            console.log(self.dropdownList);
            self.multipleDrop = true;
            console.log(self.multipleDrop);
              
        });
    }

selectedModal(selectedUser,alternates=null){
    if(isBlank(alternates) && selectedUser.noContactAvailable){
        return false;
    }
    let userDob = "";
    let patientDobForCaregiverDisplay = '';
    let userId = this.userData.userId;
    if (isBlank(selectedUser.caregiver_dob) && !isBlank(selectedUser.dob) 
    && !isNaN(new Date(selectedUser.dob).getDay())) {
        userDob = ` ${this._ToolTipService.getTranslateData('LABELS.DOB')} ${this.datePipe.transform(selectedUser.dob, CONSTANTS.dateFormats.MMddyy)}`;
    }
    if (selectedUser.caregiver_dob) {
        patientDobForCaregiverDisplay = ` ${this._ToolTipService.getTranslateData('LABELS.DOB')} ${this.datePipe.transform(selectedUser.caregiver_dob, CONSTANTS.dateFormats.MMddyy)}`;
    }
    let chatWithRole = +selectedUser.roleId === CONSTANTS.userGroupIds.patient ? (selectedUser.caregiver_userid ? (selectedUser.caregiver_displayname  + patientDobForCaregiverDisplay) : (selectedUser.displayname + userDob)) : (selectedUser.displayname + ", " + selectedUser.role);
    let createdWithUserId = selectedUser.userId;
    const createdWithUserName = selectedUser.displayname;
    const createdWithUserGrp = selectedUser.roleId;
    let createdWithAssociatedId = selectedUser.caregiver_userid;
    if (this.userData.config.default_patients_workflow === 'alternate_contacts' && !isBlank(selectedUser.alternateContacts) && !isBlank(alternates)) {
        createdWithUserId = [];
        if (Array.isArray(alternates)) {
            createdWithUserId = alternates[0];
            createdWithAssociatedId = selectedUser.userid || selectedUser.userId;
        } else {
            createdWithUserId = selectedUser.userId;
        }
    }
    if ((!createdWithAssociatedId) || isBlank(createdWithAssociatedId)) { 
        createdWithAssociatedId = 0; 
    }
    //For staff and partners, mulitiple users can be added to chat room
    if((this.optionShow === CONSTANTS.userTypes.staff 
        || this.optionShow === CONSTANTS.userTypes.partner)
        && !isBlank(this.chatRoomUsers)) {
        //Push the userIds to the createdWithUserId property
        createdWithUserId = this.chatRoomUsers.map(({userId})=>(+userId));
    }
    const params: CreateChatroomParameters = {
        chatWith: this.getChatWithOption(),
        createdWith: createdWithUserId,
        createdWithAssociatedId: +createdWithAssociatedId
    }
    if(this._structureService.isMultiAdmissionsEnabled && this.optionShow === CONSTANTS.userTypes.patient) {
        params.admissionId = this.selectedAdmissionId;
    }
    this._inboxService.createChatroom(params).subscribe((response: CreateChatroomResponse) => {
        if(response.success && response.data && response.data.roomId) {
            const chatroomId = response.data.roomId;
            const activeMessageData : ActiveMessageData = {
                chatWithUserId: createdWithUserId,
                chatWith: createdWithUserName,
                chatWithGrp:createdWithUserGrp,
                createdby: userId,
                chatroomid: chatroomId,
                chatwithUserRole: selectedUser.role,
                chatwithDob: selectedUser.dob,
                selectedTenantId: Number(selectedUser.roleId) === CONSTANTS.userGroupIds.patient ?  selectedUser.tenantid : '',
                is_patient_discussion_group:selectedUser.is_patient_discussion_group,
                chatWithRoleId:selectedUser.roleId,
                message_group_id: 0
            };

            if(selectedUser.caregiver_userid && Number(selectedUser.c_grp) === CONSTANTS.userGroupIds.patient){
                chatWithRole = `${chatWithRole} (${selectedUser.displayname})`;
                activeMessageData.patient_caregiver_displayname = selectedUser.caregiver_displayname;
                activeMessageData.patient_caregiver_passwordStatus = selectedUser.caregiver_passwordStatus;
            }

            const chatWithHeading = this._ToolTipService.getTranslateDataWithParam('MESSAGES.CHAT_WITH',{chatWith: chatWithRole});
            localStorage.setItem('targetId', chatroomId);
            localStorage.setItem('targetName', 'group-chat');
            localStorage.setItem('chatWithHeading', chatWithHeading);
            localStorage.setItem('activeMessage', JSON.stringify(activeMessageData));
            let crossTenantActivity = "";
            if (selectedUser.tenantId && selectedUser.tenantName) {
                crossTenantActivity = `[${selectedUser.tenantName}(${selectedUser.tenantId})]`;
            }
            let chatWithUsers = `${selectedUser.displayname} (${createdWithUserId})`;
            if(this._structureService.isMultiAdmissionsEnabled) {
                chatWithUsers += ` Admission - ${this.selectedAdmissionId},`;
            }
            if((this.optionShow === CONSTANTS.userTypes.staff 
                || this.optionShow === CONSTANTS.userTypes.partner)
                && !isBlank(this.chatRoomUsers) && !isBlank(createdWithUserId)) {
                chatWithUsers = '';
                createdWithUserId.forEach(element => {
                    const matchedUser = this.chatRoomUsers.find((item) => {
                        return item.userId === element.userId                      
                        });
                    if(matchedUser){
                        chatWithUsers += `${matchedUser.displayname} (${matchedUser.userId}),`;
                    } 
                });
            }
            const activityData = {
                activityName: "Start Chat Session",
                activityType: "messaging",
                activityLinkageId: chatroomId,
                activityDescription: `Chat With - ${chatWithUsers}${crossTenantActivity} in Chatroom ${chatroomId}`
            };

            this._structureService.trackActivity(activityData);
            $('#chatWithModel').modal('hide');
	    this.clearData();
            localStorage.setItem('archived', 'false');

            if(this._structureService.currentUrlNow !== ROUTES.chatRoom) {
                this.emitEventToSelectSites('removeSelectedSite');
                this.router.navigate([ROUTES.chatRoom]);
            } else {
                this.emitEventToSelectSites('removeSelectedSite');
                this._SharedService.reloadChatroomWithNewThread.emit();
            }
        } else {
            const data = { messge: response.status.message, type: 'warning'};
            this._structureService.notifyMessage(data);
        }
    }, () => {
        this.notifyService.notifyAPIException();
    });
}

goToLastPage() {
    $('#chatWithModel').modal('hide');
}
clearData(){
    this.optionShow = '';
    this.selectedAdmissionId = '';
}
messageGroupSubmit() {
    this.selectedTopic.createMessgeGroupChatroomClicked = true;
    if ('id' in this.selectedTopic && !this.selectedTopic.id) {
        var subjectExists = false;
        this.messageGroupTopic.forEach((value) => {
            if (value.subject == this.selectedTopic.subject) {
                subjectExists = true;
                this.selectedTopic = value;
            }
        });

        if (subjectExists) {
            if (+this.selectedTopic.isParticipant === 0) {
                this.selectedTopic.creating = true;
                this.addUserToMessageGroup(this.selectedTopic.id,() => {
                    this.selectedTopic.creating = false;
                })
            } else {
                this.reRouteToChatroomOnSuccess(this.selectedTopic.id);
            }
        } else {
            this.selectedBranch = this._structureService.getCookie('organizationName');
            this.selectedTopic.creating = true;
            const params: CreateChatroomParameters = {
                createdWith: +this.selectedGroup.userId || 0,
                messageGroupId: +this.selectedGroup.groupId,
                topic: this.selectedTopic.subject,
                chatWith: this.getChatWithOption()
            }
            if(this.optionShow === 'groups' && this._structureService.isMultiAdmissionsEnabled) {
                params.admissionId = this.selectedAdmissionId;
            }
            this._inboxService.createChatroom(params).subscribe((response: any) => {
                if(response.success && response.data && response.data.roomId) {
                    const chatroomId = response.data.roomId;
                    this.selectedTopic.creating = false;
                    if(chatroomId) {
                        this.reRouteToChatroomOnSuccess(chatroomId, this.userData.userId);
                    } else {
                        this.goToLastPage();
                        const notify = $.notify(this._ToolTipService.getTranslateData('ERROR_MESSAGES.CHATROOM_CREATION_FAILED'));
                        setTimeout(()=> {
                            notify.update({ 'type': 'danger', 'message': `<strong>${this._ToolTipService.getTranslateData('ERROR_MESSAGES.CHATROOM_CREATION_FAILED')}</strong>` });
                        }, 1000);
                    }
                } else {
                    this._structureService.notifyMessage({message: response.status.message});
                }                
            });
        }
    } else {
        if (+this.selectedTopic.isParticipant === 0) {
            this.selectedTopic.creating = true;
            this.addUserToMessageGroup(this.selectedTopic.id, () => {
                this.selectedTopic.creating = false;
            })
        } else {
            this.reRouteToChatroomOnSuccess(this.selectedTopic.id);
        }

    }
}
showGroupSubjects(groupData, admissionData:any = {}) {
    this.selectedGroup = groupData;
    if(this.optionShow === 'groups') {
        if(this._structureService.isMultiAdmissionsEnabled) this.selectedGroup = {...groupData, ...admissionData};
        const patientData = {
            firstName: this.selectedGroup.firstname, 
            lastName: this.selectedGroup.lastname, 
            dob: !isBlank(this.selectedGroup.dob) ? `${this._ToolTipService.getTranslateData('LABELS.DOB')} ${moment(this.selectedGroup.dob).format('MM/DD/YYYY')}` : '', 
            mrn: this.selectedGroup.IdentityValue, 
            admissionName: admissionData.name ? admissionData.name : ''
        };
        this.selectedGroup['name'] = this._ToolTipService.getTranslateDataWithParam(!this._structureService.isMultiAdmissionsEnabled ? 'LABELS.PDG_TOPIC' : 'LABELS.PDG_TOPIC_ADMISSION', patientData);
    }
    if(+this.selectedGroup.allowMultiThreadChat === 1) {
        this.showChatBtn = false;
        this.showSubject = true;
        this.getAllMessageGroupTopicsByGroup({searchText: '', loadMore: false});
    } else {
        this.showSubject = false;
        this.showChatBtn = true;
    }
}

addUserToMessageGroup(chatRoomId, callBack?) {
    const apiURL = APIs.addUserToMessageGroup;
    const params = {
        roomId: +chatRoomId,
        type: this.optionShow === 'groups' ? 'patientGroup' : 'messageGroup'
    };
    this.httpService.doPost(apiURL, params).subscribe(() => {
        this.reRouteToChatroomOnSuccess(chatRoomId);
        if(callBack) {
           callBack();
        }
    });
}

createMessgeGroupChatroom(messageGroup) {
    if(+messageGroup.allowMultiThreadChat !== 1) {
        if(+messageGroup.chatRoomId) {
            this.selectedGroup = messageGroup;
            this.selectedTopic = {
                id: messageGroup.chatRoomId,
                subject: messageGroup.topic,
                branch:messageGroup.branch,
                message_group_id: messageGroup.groupId,
                isParticipant: +messageGroup.isParticipant,
                userInfoMsg : true,
                createdby: messageGroup.chatRoomCreatedBy
            }
            if (this.selectedTopic.isParticipant === 0) {
                this.addUserToMessageGroup(messageGroup.chatRoomId);
            } else {
                this.reRouteToChatroomOnSuccess(messageGroup.chatRoomId);
            }
        } else {
            this.selectedGroup = messageGroup;
            let topicExists = false;
            const subject = this.optionShow === 'groups' ? this.selectedGroup['name'] : this.selectedGroup['groupName'];
            for (let key in this.messageGroupTopic) {
                if (this.messageGroupTopic[key].subject === subject) {
                    topicExists = true;
                    break;
                }
            }
            this.selectedTopic = {
                id: 0,
                subject: subject,
                message_group_id: this.selectedGroup['groupId'],
                isParticipant: 0,
                userInfoMsg: topicExists ? true : false
            }
            this.messageGroupSubmit();
        }
    } else {
        this.messageGroupSubmit();
    }
}

reRouteToChatroomOnSuccess(chatroomId, createdBy=this.selectedTopic.createdby) {
    this.selectedTopic.createMessgeGroupChatroomClicked = false;
    var chatWithHeading = this.selectedGroup['groupName'];
    var targetID = chatroomId;
    var targetName = 'message-group-chat';
    var activeMessageData: { [k: string]: any } = {};
    var branch = null;
    if(this.optionShow == 'groups') {
    this.messageGroupTopic.filter((group) => {
        if (group.message_group_id == this.selectedGroup['groupId']) {
            branch = group.branch;
        }
    });
    if (!branch) {
        branch = this.selectedBranch;
    }
    }
    activeMessageData = {
        chatWithUserId: this.selectedGroup['groupId'],
        chatWith: this.selectedGroup['groupName'] + ' [Message Group]',
        message_group_id: this.selectedGroup['groupId'],
        createdby: createdBy,
        chatroomid: targetID,
        message_group_name: this.selectedGroup['groupName'],
        branch: branch,
        siteName: branch,
        title: this.selectedTopic['subject'],
        invited_status: 0,
        selectedTenantId: this.optionShow === 'groups' ?  this.selectedGroup['tenantId'] : '',
        is_patient_discussion_group: this.optionShow === 'groups' ? 1 : 0,
    };
    this.selectedGroup = {};
    this.selectedTopic = {};
    localStorage.setItem('targetId', targetID);
    localStorage.setItem('targetName', targetName);
    localStorage.setItem('chatWithHeading', chatWithHeading);
    localStorage.setItem('activeMessage', JSON.stringify(activeMessageData));

    
    var activityData = {
        activityName: "Start Chat Session",
        activityType: "messaging",
        activityLinkageId: targetID,
        activityDescription: "Chat With - " + activeMessageData.chatWith + " (" + activeMessageData.message_group_id + ") with Subject " + activeMessageData.title+" in Chatroom " + targetID
    };

    this._structureService.trackActivity(activityData);



    console.log(activeMessageData);
    $('#chatWithModel').modal('hide');
    localStorage.setItem('archived', 'false');
    if(this._structureService.currentUrlNow != '/inbox/chatroom') {
        this.router.navigate(['/inbox/chatroom']);
    } else {
        this._SharedService.reloadChatroomWithNewThread.emit();
    } 
}
logout_with_unsend_msg(){
    if(this._structureService.flag == 1){
        this._structureService.flag = 0;
        console.log("this.flag-if",this._structureService.flag);
        }
        if(this.userData && this.userData.config && this.userData.config.showElevio == '1' && Elevio){
            this._ngZone.runOutsideAngular(() => {
                this._ngZone.run(() => { 
            Elevio.disable();
            });
            });
        }
    this._structureService.logout();
    this._intakeService.blogStaffList = [];
}

logout() {
    if(window.localStorage.getItem('chUserMessages')){
        $('#unsend_msgs').modal('show');
    }
    else{
        this.logoutUser();
    }
    this._SharedService.playAudioForCall.emit({"playSound":false,"action":"forcallPause"});
}

activateNow() {
    console.log('----------------Top Menu activateNow----------------');
    this.privileges = this._structureService.getCookie('userPrivileges');
    this.privilegesReplica = this._structureService.getCookie('userPrivilegesReplica');
}



/* Video Chat functions starts */
initializeVideoChat() {
    window.onbeforeunload = () => this.ngOnDestroy();

    console.log('enable video chat -------->', this.userData.config.enable_video_chat);
    console.log(this.userData);
    this.socketEventSubscriptions.push(
        this._structureService.subscribeSocketEvent('userJoinedToRoom').subscribe((data) => {
            console.log('userJoinedToRoom')
            this.hideInitiatorAudioBtn = true;
            if (data.joineeId != this.userData.userId) {
                const notifyMsg = data.joineeName + ' has joined the video call.';
                var notify = $.notify(notifyMsg);
                setTimeout(() => {
                    notify.update({ 'type': 'success', 'message': '<strong>' + notifyMsg + '</strong>' });
                }, 1000);
            }
            var joineeId = data.joineeId;
            this._SharedService.inviteUserVideoTile.emit({ joineeId });
        })
    );
    this.socketEventSubscriptions.push(
        this._structureService.subscribeSocketEvent('userLeftRoom').subscribe((data) => {
            console.log('userLeftRoom');
            this.hideInitiatorAudioBtn = true;
            if (data.userId != this.userData.userId) {
                const notifyMsg = data.userName + ' has left the video call.';
                var notify = $.notify(notifyMsg);
                setTimeout(() => {
                    notify.update({ 'type': 'success', 'message': '<strong>' + notifyMsg + '</strong>' });
                }, 1000);
            }
        })
    );



    console.log('init this._sharedService.videoCallReceived', this._SharedService.videoCallReceived)
    console.log('init this._sharedService.videoCall', this._SharedService.videoCall)
    if (document.getElementById('video-text'))
        document.getElementById('video-text').style.display = 'none'
    if (document.getElementById('video-spin'))
        document.getElementById('video-spin').style.display = 'inline-block'
    console.log('init vidyo')
    let options = {
        maxParticipants: 5,
        userData: this.userData
    }

    if (!this._commonVideoService.checkVideoPluginLoaded()) {
        console.log('Chatroom vidyo init ----- SUCCESS')
        this._commonVideoService.init('vidyo', options);
    }
    else {
        this.enableVideoButton = (this._SharedService.videoCallReceived) ? false : true;
        this._SharedService.enableVideoChatButton.emit(this.enableVideoButton);
        console.log('after setting this._sharedService.videoCallReceived', this._SharedService.videoCallReceived)
        console.log('after setting this._sharedService.videoCall', this._SharedService.videoCall)
        if (document.getElementById('video-text'))
            document.getElementById('video-text').style.display = 'inline-block'
        if (document.getElementById('video-spin'))
            document.getElementById('video-spin').style.display = 'none'
    }

    if (isBlank(this.videoChanged) || (this.videoChanged && this.videoChanged.closed)) {
        this.videoChanged = this._SharedService.videoChanged.subscribe((eventData) => {
            console.log('vidyoClient:videoChanged ----listener', eventData)
            this._SharedService.playAudioForCall.emit({ "playSound": false, "action": "" });
            console.log("local storage participant value",localStorage.getItem('participant'));
            if(eventData && eventData[0] == 'no-remote-participant' && localStorage.getItem('participant') == "false"){
                this.nouserOnlineSwal("All users left this conference");
            }
        });
    }

    if (isBlank(this.connectorAvailable) || (this.connectorAvailable && this.connectorAvailable.closed)) {
        this.connectorAvailable = this._SharedService.connectorAvailable.subscribe(() => {
            console.log('connectorAvailable ---- subscribe')
            this.enableVideoButton = this._SharedService.videoCallReceived ? false : true;
            if (this.initialLoad)
                this._SharedService.enableVideoChatButton.emit(this.enableVideoButton);
            if (document.getElementById('video-text'))
                document.getElementById('video-text').style.display = 'inline-block'
            if (document.getElementById('video-spin')) {
                if (document.getElementById('video-spin').style.display == 'inline-block') {
                    document.getElementById('video-spin').style.display = 'none'
                }
            }
            this.initialLoad = false;
        })
    }
    if (isBlank(this.maxRemoteSourcesChanged) || (this.maxRemoteSourcesChanged && this.maxRemoteSourcesChanged.closed)) {
        this.maxRemoteSourcesChanged = this._SharedService.maxRemoteSourcesChanged.subscribe((data) => {
            console.log('maxRemoteSourcesChanged ---- subscribe',data);
            this.maxRemoteSourcesAccomadateMessageUpdate = this.maxRemoteSourcesAccomadateMessage.replace('{count}',data.maxRemote);
        })
    }
    if (isBlank(this.rendered) || (this.rendered && this.rendered.closed)) {
        this.rendered = this._SharedService.rendered.subscribe(() => {
            console.log('------------ Rendered ----------- Progress bar stops-------------')
            NProgress.done();
        })
    }


    if (isBlank(this.vidyoClientSubscribe) || (this.vidyoClientSubscribe && this.vidyoClientSubscribe.closed)) {

        this.vidyoClientSubscribe = this._SharedService.vidyoClient.subscribe(
            (data) => {
                if(data && data.assignConnectionData){
                    NProgress.done();
                    this.onVideoCall = true;
                    this._SharedService.onVideoChat = true;
                    console.log("vidyoClient:connected", data);
                    this.connectionMessage = 'Waiting for users '
                    console.log('emit call')
                    console.log("this.chatroomUsersList==>", this.chatroomUsersList);
                }
                if (data && data.playAudio == true) {
                    console.log("play caller sound")
                    this.playAudio();
                }
            }
        );

    }
    console.log('shared video', this._SharedService.videoCallReceived)
    if (this._SharedService.videoCallReceived && this._SharedService.videoCall) {
        console.log('clicked accept');
        var videoChatData = JSON.parse(localStorage.getItem('videoChatData'))
        this.showVideoChat(videoChatData.roomData);
    }
}
           
registerActivity() {
    let sessionSelf=this;
     $("body").off().on({
         click: function() {
             // Handle mouseenter...
             sessionSelf.secondTick = 0;
             clearTimeout(sessionSelf._structureService.startThisSessionTimerPromise);
              if(($("#sessionout").data('bs.modal') || {})._isShown){
                  if(sessionSelf.flagstop==false){
                  $('#sessionout').modal('hide');
                }
              }
            sessionSelf.flagstop=true;
           },
         keypress: function() {
             // Handle mouseleave...
            // Handle mouseenter...
             sessionSelf.secondTick = 0;
             clearTimeout(sessionSelf._structureService.startThisSessionTimerPromise);
              if(sessionSelf.flagstop==false){
                  $('#sessionout').modal('hide');
              }
              sessionSelf.flagstop=true;
               
         }
     });

 }
  startThisSessionTimer() {
    let userData;
    
    userData = JSON.parse(this._structureService.userDetails);
    console.log("startThisSessionTimer Inside========");
     this.timeInMinutesForSessionOut = userData.config.session_timeout;
    console.log("=====timeInMinutesForSessionOut===============",this.timeInMinutesForSessionOut);
    localStorage.setItem('timeInMinutesForSessionOut',this.timeInMinutesForSessionOut)
    this.timeInSecondsAfterSessionOut = userData.config.session_timeout_warning;
    console.log("=====timeInSecondsAfterSessionOut===============",this.timeInSecondsAfterSessionOut);
    localStorage.setItem('timeInSecondsAfterSessionOut',this.timeInSecondsAfterSessionOut)
    this.secondTick = 0;
    this.flagstop = true;

    this.startDayCountDown();
 }
 
 startDayCountDown() {
    console.log("===== Inside startDayCountDown");
   let userDetails = JSON.parse(this._structureService.userDetails);
   console.log(userDetails.tenantId);
   let activeTimeZone = ((userDetails && userDetails.config && "tenant_timezone_offset" in userDetails.config) ? userDetails.config.tenant_timezone_offset.split(',')[0]/60 : 0);
   let timeToPassDay = [];
   let timeDifference;
   if(activeTimeZone > this.timezone) {
     timeDifference = -1 * Math.abs(this.timezone - activeTimeZone);
   } else {
     timeDifference = Math.abs(this.timezone - activeTimeZone);
   }
   if(((timeDifference) + '').split('.').length == 2) {
     timeToPassDay = ((timeDifference) + '').split('.');
   } else {
     timeToPassDay = ((timeDifference) + '').split('.');
     timeToPassDay.push("0");
   }
   
   if(timeToPassDay[1] == '5') {
     if(timeDifference < 0) {
       timeToPassDay[1] = '-30';
      } else {
        timeToPassDay[1] = '30';
      }
   } else {
     timeToPassDay[1] = '0';
   }
   
   if(timeToPassDay[0] == '-0') {
     timeToPassDay[0] = '0';
   }
   
   timeToPassDay = timeToPassDay.map(function(time) {
     return parseInt(time, 10);
   });
  console.log('timeToPassDay-->',timeToPassDay);
  var currentServerTime = moment().add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
  var nextDay = moment().add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').add(1, 'days');
  var midNightServerTime = currentServerTime.clone().endOf('day');
  var diffSeconds = midNightServerTime.diff(currentServerTime, 'seconds');
  var clientLoginedTimeZone = (new Date().getTimezoneOffset()) * -1;
  var data = { date: nextDay.format('YYYYMMDD'), dayNumber: nextDay.day(), tenantId: userDetails.tenantId, dayChange: true, clientLoginedTimeZone: clientLoginedTimeZone, tenantTimeZone: userDetails.config.tenant_timezone, currentTime: null};
  console.log('diffSeconds-->',diffSeconds);
  let self = this;
  setTimeout( function() {
console.log('Inside 1-->');
    data.currentTime = moment().add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').add(10, 's').format('HH:mm');
console.log('Inside 2--> ',data.currentTime);
    self._structureService.updateSchedulesOnDayChange(data).then( (response) => {

      if(response) {
console.log('Inside 3--> ',response);
        for (let userId in response['schedulerData']) { console.log('Inside 4--> schedulerData ');
          let schedulerData = this.convertSchedulerDataToClientTZ(response['schedulerData'][userId], null, this);
          if (schedulerData) {
            response['schedulerData'].schedulerData[userId] = JSON.stringify(schedulerData);
          }
        }
        for (let userId in response['escalatedSchedulerData']) { console.log('Inside 5--> escalatedSchedulerData ');
          let escalatedSchedulerData = this.convertSchedulerDataToClientTZ(response['escalatedSchedulerData'][userId], -4, this);
          if (escalatedSchedulerData) {
            response['escalatedSchedulerData'][userId] = JSON.stringify(escalatedSchedulerData);
          }
        }
console.log('Inside 6--> ',response['escalatedSchedulerData']);
        userDetails.escalatedSchedulerData = response['escalatedSchedulerData'];
        this.setCookie('schedule', response['schedulerData'], 1);
console.log('Inside 7--> escalatedSchedulerData ');       
        this._structureService.userDetails = JSON.stringify(userDetails);
console.log('Inside 8--> escalatedSchedulerData ');           
        let activityData = {
          activityName: "Schedule Refresh",
          activityType: 'day change',
          activityDescription: "Staff schedules refreshed for " + userDetails.displayName + " when day changed"
        };
        this.trackActivity(activityData);
      } else {
        let activityData = {
          activityName: "Failure Schedule Refresh",
          activityType: 'day change',
          activityDescription: "Staff schedules refreshed for " + userDetails.displayName + " when day changed"
        };
        this.trackActivity(activityData);
      }  
    });
  }, diffSeconds * 1000);
} 
 getAllMessageGroupDetails(tenantId, userId, searchKeyword = {searchText : '', loadMore:false}, isPdg = ""){
    const paginationItems: PaginationItems = {
        limit: this.limit,
        page: searchKeyword.loadMore ? this.page += 1 : this.page = 1,
    }
    const groupItems: GroupItems = {
        pagination: paginationItems,
    }
    if (searchKeyword.searchText) {
        const fiilterItems: FilterItems = {
            search: searchKeyword.searchText
        }
        groupItems.filter = fiilterItems;
    }

    const reqObj: GroupsListingRequest = {
        data: groupItems
    }
    if(this.selectSiteId != 0) {
        reqObj.siteIds = this.selectSiteId.split(',').map(item => Number(item.trim()));
    }
    if(tenantId && userId){
        switch (this.optionShow) {
            case 'msggroups':
                this.messageService.fetchGroupData(reqObj,'messageGroup').subscribe((resp: GroupListItem<GroupData>) => {
                    const data = resp.data.messageGroups.map(item => {
                        return new MessageGroup(item);
                    });
                    const messageGroupList = resp.data.messageGroups.map(({name, ...user})=> {
                        let formattedName = user.groupName;
                        return {id: user.groupId, name: formattedName, ...user};
                    });
                    this.formattedMessageList = searchKeyword.loadMore ? this.formattedMessageList.concat(messageGroupList) : messageGroupList;
                    this.totalMessageCount = +resp.data.totalMessageGroupsCount;
                    this.executeData(searchKeyword.searchText,data);
                },error => {
                });
                break;
            case 'groups':
                this.messageService.fetchGroupData(reqObj,'patientGroup').subscribe((resp: GroupListItem<GroupData>) => {
                    const data = resp.data.patientGroups.map(item => {
                        return new MessageGroup(item);
                    });
                    this.executeData(searchKeyword.searchText,data);
                },error => {
                });
                break;
        }
    }
}




muteInitiatorTune(){
    if(!this.hideInitiatorAudioBtn){
        this.initiatorCallTune = !this.initiatorCallTune;
        if(this.initiatorCallTune){
            console.log('Pause audio');
            this._SharedService.playAudioForCall.emit({"playSound":false,"action":"forcallPause"});
        }else{
            console.log('play audio')
            this._SharedService.playAudioForCall.emit({"playSound":true,"action":"forcallPlay"});
        }
    }
}

showVideoChat(params:any={}) {
    console.log("showVideoChat Enter with params",params);
    var users = [];
    for (let i = 0; i < this.chatroomUsersList.length; i++) {
        if (this.chatroomUsersList[i].userId == this.userData.userId) {
            users.push(this.chatroomUsersList[i]);
            this.chatroomUserCount = users;
        }
    }
    if (this._commonVideoService.checkConnectionStatus()) {//checking whether connected to another group or not
        this.dialogMessage = 'You Cant Establish Multiple Video Calls'
    }else{
        console.log("showVideoChat : connectvideo called..");
        this.connectVideo(params);
    }
    NProgress.start();
    let activityName = '';
    if ((this._SharedService.videoCallReceived && this._SharedService.videoCall) || this.joinChat) {
        var type = 'has joined via notification';
        activityName = 'Joining Video Chat';
    }
    else {
        var type = 'initiated';
        activityName = 'Initiating Video Chat';
        if (this.joinChat) {
            type = 'joined to';
            activityName = 'Joining Video Chat';
        }
    }
    if (this.joinChat) {
        this.setLocalStorageParticipants('true');
    } else {
        this.setLocalStorageParticipants('false');
    }
    var activityData = {
        activityName: activityName,
        activityType: "video communication",
        activityLinkageId: this.videoChatRoomId,
        activityDescription: this.userData.displayName + " (" + this.userData.userId + ") " + type + " video chat in chatroom - " + this.videoChatRoomId
    };



    this._structureService.trackActivity(activityData);
}

connectVideo(params: any = {}) {
    this._SharedService.disconnectEnable = false
    let options = {
        host:params.host,//this._commonVideoService.vidyoHost
        token: this._commonVideoService.token,
        displayName: this.userData.displayName,
        resourceId: this.videoChatRoomId,
        sendEvent: !(this._SharedService.videoCallReceived || this.joinChat),
        users: this.chatroomUsersList,
        avatarBasePath: this.avatarBasePath,
        renderer: this.renderer2,
        userData: this.userData,
        roomKey: params.roomKey,
        roomName: params.roomName,
        roomPin: params.roomPin,
        roomEntity : params.roomEntity
    }
    var self = this;
    self.vidyoCallRetryCount = self.vidyoCallRetryCount + 1;
    self._commonVideoService.connect(options, function (data) {
        console.log("vidyo: vidyo connect status===>", data);
        if (!data.status){
            if(data.connection == "VIDYO_CONNECTORFAILREASON_InvalidToken") {
                if(self.vidyoCallRetryCount < 5) {
	                self._structureService.generateVidyoTocken({ source: 'topbar', userName:  self.userData.displayName+'-'+self.userData.tenantName }).then((data)=> {
	                    console.log('---generateVidyoTocken----response-'+JSON.stringify(data));
	                    self._SharedService.validVidyoToken.emit(data);
	                });
	            } else {
	                self.notifyCustomErrorMessage();
	                self.disconnect({message:"Disconnected after maximum retry attempt by "+ self.userData.displayName + " (" + self.userData.userId + ") in chatroom - " + self.videoChatRoomId+" due to invalide token."});
	            }
            }else if(data.connection == "VIDYO_CONNECTORFAILREASON_MiscLocalError"){
                //Unknown error while joining the room
                self.notifyCustomErrorMessage("Unknown technical error occurred on your device. Please try again or contact CitusHealth support");
            }else if(data.connection == "VIDYO_CONNECTORFAILREASON_MiscRemoteError"){
                //The server rejected the user's request to enter the resource, due to a miscellaneous problem 
                self.notifyCustomErrorMessage("Unknown technical error occurred on Video Service side. Please try again or contact CitusHealth support");
            }else if(data.connection =="VIDYO_CONNECTORFAILREASON_MiscError"){
                //Unknown error during room creation or The login failed for some other miscellaneous reason
                self.notifyCustomErrorMessage("The connection with Video Service has been lost by unkown reason. Please try again or contact CitusHealth support");
            }else if(data.connection =="VIDYO_CONNECTORDISCONNECTREASON_ConnectionLost"){
                //The transport connection was lost.   
                self.notifyCustomErrorMessage("Connection with Video Service Failed. Please try again or contact CitusHealth support");         
            }else if(data.connection =="VIDYO_CONNECTORDISCONNECTREASON_ConnectionTimeout"){
                //The signaling connection timed-out.   
                self.notifyCustomErrorMessage("Connection request to Video Service timed out. Please try again or contact CitusHealth support");         
            }else if(data.connection =="VIDYO_CONNECTORDISCONNECTREASON_NoResponse"){
                //The service did not respond in a reasonable amount of time to a request by the user.
                self.notifyCustomErrorMessage("Video Service is not responding. Please try again or contact CitusHealth support");
            }else if(data.connection =="VIDYO_CONNECTORDISCONNECTREASON_Terminated"){
                //The service closed the connection or otherwise terminated the login session.
                self.notifyCustomErrorMessage("Video Service connection has been lost. Please try again or contact CitusHealth support");
            }else if(data.connection =="VIDYO_CONNECTORFAILREASON_ConnectionFailed"){
                self.notifyCustomErrorMessage("Connection with Video Service Failed. Please try again or contact CitusHealth support");         
                self.disconnect({message:"Disconnected after maximum retry attempt by "+ self.userData.displayName + " (" + self.userData.userId + ") in chatroom - " + self.videoChatRoomId+" due to vidyo connection failed."});
            } else if (['VIDYO_CONNECTORDISCONNECTREASON_Disconnected', 'VIDYO_CONNECTORFAILREASON_InvalidResourceId'].includes(data.connection)) {      
                self.disconnect();
            }
        }
        else if (data.status && data.connection == "Succcess") {
            setTimeout(() => {
                self._SharedService.disconnectEnable = true
            }, 2000);
        }
    });
    this.enableVideoButton = false;
    this._SharedService.enableVideoChatButton.emit(this.enableVideoButton);
}
notifyCustomErrorMessage(message='') {
    this._structureService.notifyMessage({
        messge: message ? message : 'Video Chat is not currently available.',
        type: 'danger',
        allow_dismiss: true,
        delay: 0
    });
}
disconnect(disconnectedDueToError=null) {
    console.log('disconect click')
    this.inviteUserTiles = [];
    this.vidyoCallRetryCount = 0;
    this.allowVideoChat = true;
    this.isVideoChatMinimized = false;
    this._SharedService.pushNotification =false;
    this.hideInitiatorAudioBtn = false;
    this.initiatorCallTune = false;
    if(!disconnectedDueToError) {
        if(this.initiatorEnd){
            var activity = "Initiator ended the video call"
        }
        else{
            var activity = this.userData.displayName + " (" + this.userData.userId + ") clicked on disconnect button in chatroom - " + this.videoChatRoomId
        }
    } else {
        activity = disconnectedDueToError.message;
    }

    
    this._SharedService.videoCallReceived = false
    this._SharedService.videoCall = false
    this._SharedService.callInitiatedRoomId = ''
    this._SharedService.onVideoChat = false;
    this._commonVideoService.disconnect(true);
    document.getElementById('video_container').style.visibility = 'hidden';
    console.log("localStorage.getItem('participant')==>" + localStorage.getItem('participant'));
    var activityData = {
        activityName: "Ending Video Chat",
        activityType: "video communication",
        activityLinkageId: this.videoChatRoomId,
        activityDescription: activity
    };
    if (localStorage.getItem('participant') == "true") {
        console.log("participant participant")
        this._structureService.socket.emit("videoChatReject", { initiator: this.userData.userId, rejectUser: null, RejectUserName: null, action: 'participant', users: this.chtRoomUsers, roomId: this.videoChatRoomId });
        
    } else {
        console.log("initiator closeeeeeeeeeeeeeeeeeeeeeeeeeeee")
        this._SharedService.onGoingChatrooom = ''
        this._structureService.socket.emit("videoChatReject", { initiator: this.userData.userId, rejectUser: null, RejectUserName: null,action:'initiator', users: this.chtRoomUsers, roomId:this.videoChatRoomId });
        this._SharedService.playAudioForCall.emit({"playSound":false,"action":""});
        this._SharedService.inviteUserVideoTile.emit({ action: 'initiatorClose' });
        if (this._SharedService.applessVideo) {
            var updateData = { "action": "initiatorLeft", "chatroom": this.videoChatRoomId, "status": 2 };
            this._inboxService.updateApplessStatus(updateData).then((result) => {
                console.log("*******************Appless call Result****************");
                console.log(result);
            });
        }
        this._SharedService.applessVideo = false;
        this._SharedService.applessVideoChatroomDetails = {};
        var chatroomvidoDetails = this._GlobalDataShareService.getVideoChatDetails();
        var roomId=chatroomvidoDetails['initiator']['roomData']['roomEntity'];
        this.showVideoChatEvent = null;
        this._structureService.deleteVidyoRoom(roomId).then((response) => {
              console.log("delete-room-response"+JSON.stringify(response));
              console.log(this.videoChatRoomId);
               var activityDataDeleteRoom={
                activityName: "Delete Vidyo Room",
                activityType: "video communication",
                activityLinkageId: this.videoChatRoomId,
                activityDescription: activity
               }
              this._structureService.trackActivity(activityDataDeleteRoom);
        });
        

    }
    this.onVideoCall = false;
    console.log("disconnect------and set localStorage------ Called.");
    this.setLocalStorageParticipants('false');
    console.log('disconnect this._sharedService.videoCallReceived', this._SharedService.videoCallReceived)
    console.log('disconnect this._sharedService.videoCall', this._SharedService.videoCall)
    this._structureService.trackActivity(activityData);
    this.initiatorEnd = false;
    this.cameraPrivacy = false
    this.microphonePrivacy = false
    this.connectionMessage = "Connecting"
    this.vidyoCallEndUnSubscribe();
}

toggleMute() {
    this.microphonePrivacy = !this.microphonePrivacy
    let muteButton = document.getElementById("btn-mute");
    this._commonVideoService.toggleMute(this.microphonePrivacy);
    muteButton.innerHTML = this.microphonePrivacy ? "Unmute" : "Mute";
}
toggleFullScreen() {
    this._SharedService.videoFull = !this._SharedService.videoFull
    for (let i = 1; i < this.videoTiles.length; i++) {
        console.log('tilemute' + i)
        let tile = document.getElementById('tilemute' + i)
        if (tile) {
            tile.className = this._SharedService.videoFull ? 'tile_mute_max' : 'tile_mute'
        }
    }
    var activityData = {
        activityName: "Video Chat Window Resize",
        activityType: "video communication",
        activityLinkageId: this.videoChatRoomId,
        activityDescription: this.userData.displayName + " (" + this.userData.userId + ") clicked on video screen resizing button in chatroom - " + this.videoChatRoomId
    };
    this._structureService.trackActivity(activityData);
}

toggleCameraPrivacy() {
    console.log("toggleCameraPrivacy ---> function called");
    if (this._commonVideoService.checkLocalCameraRendered()) {
        this.cameraPrivacy = !this.cameraPrivacy;
        this._commonVideoService.changeCameraPrivacy(this.cameraPrivacy);
    }
}
css(element, style) {
    for (const property in style)
        element.style[property] = style[property];
}

closeDialogBox() {
    let permissionElement = document.getElementById('permission_dialog')
    permissionElement.style.display = 'none'
    var activityData = {
        activityName: "Closed User Media Permission Popup",
        activityType: "video communication",
        activityLinkageId: this.videoChatRoomId,
        activityDescription: this.userData.displayName + " (" + this.userData.userId + ") closed user media permission popup in chatroom - " + this.videoChatRoomId
    };
    this._structureService.trackActivity(activityData);
}
setLocalStorageParticipants(condition) {
    console.log("setLocalStorageParticipants----- Called.===>" + condition);
    localStorage.setItem('participant', condition);
}
playAudio(){
    if(!this.joinChat){
        this._SharedService.playAudioForCall.emit({"playSound":true,"action":"forcall"});
    }        
}

minimizeChat(){
    this.muteInitiatorTune()
    this.isVideoChatMinimized = true;
    this.initialtorCallTune = true;
    this._SharedService.playAudioForCall.emit({"playSound":false,"action":"forcallPause"});
}
  /* Video chat functions ends */
  openElevio(){
    this._ngZone.runOutsideAngular(() => {
        // reenter the Angular zone and display done
        this._ngZone.run(() => { 
            console.log("wwwwwwwwwwwwwwwwwwwwwwwwwwww");
            Elevio.enable();
            (window as any)._elev.open();
    });
    });
      var activityData = {
        activityName: "Help Center",
        activityType: "Open Help Center",
        activityDescription: this.userData.firstName+" "+this.userData.secondName+" with username "+this.userData.username+" opened Help center from desktop"
       };
       this._structureService.trackActivity(activityData);
  }

  //  Message Routing 

createChatroomWithRole(user) {
    let createdWithAssociatedId = '0';
    let self = this;
    const param: CreateChatroomParameters = {
        chatWith: ChatWithTypes.ROLE,
        createdWithAssociatedId: +createdWithAssociatedId,
        createdByAssociatedId: +this.associatedPatientId || +this.userData.userId,
        roleId: user.roleId,
        createdWith: 0
    };
    if(this._structureService.isMultiAdmissionsEnabled) {
        param.admissionId = this.selectedAdmissionId;
    }
    this._inboxService.createChatroom(param).subscribe((response) => {
        if(response.success && response.data && response.data.roomId) {
        const data = response.data.roomId;
        const targetId: any = data;
        let activeMessageData: { [k: string]: any } = {};
        let userId = self.userData.userId;
        activeMessageData = {
            chatWithUserId: 0,
            chatWith: user.roleName,
            chatWithGrp:'',
            createdby: userId,
            chatroomid: data,
            chatwithUserRole: user.roleId,
            chatwithDob: '',
            selectedTenantId: '',
            is_patient_discussion_group:'',
            chatWithRoleId:user.roleId,
            message_group_id: 0
        };

        let targetName = 'group-chat';
        localStorage.setItem('targetId', targetId);
        localStorage.setItem('targetName', targetName);
        localStorage.setItem('chatWithHeading', "");
        localStorage.setItem('activeMessage', JSON.stringify(activeMessageData));
        const activityData = {
            activityName: "Start Chat Session",
            activityType: "messaging",
            activityLinkageId: targetId,
            activityDescription: "Chat With - " + user.roleName + " (" + user.roleId + ") in Chatroom " + targetId
        };

        this._structureService.trackActivity(activityData);
        $('#chatWithModel').modal('hide');
        localStorage.setItem('archived', 'false');
        if(this._structureService.currentUrlNow != '/inbox/chatroom') {
            this.router.navigate(['/inbox/chatroom']);
        } else {
            this._SharedService.reloadChatroomWithNewThread.emit();
        } 
            if(this.doubleVerificationStatus){
                this._SharedService.setDoubleVarification.emit();
            }
    } else {       
        this._structureService.notifyMessage({
            messge: response.status.message
        });
    }
    }, () => {
        this.notifyService.notifyAPIException();
    });
}
    getAllMessageGroupTopicsByGroup(event: LoadMoreItems){
        let groupType = 'mg';
        let groupId = Number(this.selectedGroup.groupId);
        if (this.optionShow === 'groups') {
            groupType = 'pdg';
            groupId = Number(this.selectedGroup.userId);
        }
        let parameter: GroupTopicsListRequest = {
            data: {
                id: groupId,
                filter: {
                    groupType: groupType
                },
                pagination: { 
                    limit: CONSTANTS.contentLimit, 
                    page: event.loadMore ? this.msgGrpSubjectOffset += CONSTANTS.contentLimit : this.msgGrpSubjectOffset = CONSTANTS.contentOffset === 0 ? 1 : CONSTANTS.contentOffset
                }
            }
        };
        if(this.optionShow === 'groups' && this._structureService.isMultiAdmissionsEnabled) {
            parameter.data['admissionId'] = this.selectedAdmissionId;
        }
        if (event && event.searchText !== '') {
            parameter.data.filter['search'] = event.searchText;
        }
        this.messageService.getAllMessageGroupTopicsList(parameter).subscribe(
            (data) => {
                const groupTopics = groupType === 'pdg' ? data.data.patientGroupTopics : data.data.messageGroupTopics;
                this.totalSubjectCount = 0;
                if (event.loadMore) {
                    this.messageGroupTopic = [...this.messageGroupTopic, ...groupTopics];
                } else {
                    this.messageGroupTopic = [...groupTopics];
                }
                this.totalSubjectCount = data.data.totalMessageGroupTopicsCount || 0;
                this.messageGroupTopic.forEach((item) => {
                    item.name = item.subject;
                    item.id = item.chatRoomId;
                });
                this.selectedTopic = {
                    id: -1
                }
            },
            () => {
                this.messageGroupTopic = [];
            });
    }
    /**
     * Opens modal to select associated patients
     * @param groupId user group Id. e.g 3
     */
    openChatWithModal(groupId){
        this._SharedService.chatWith.emit(groupId);
        this.infoBottomHide();
    }

    /**
     * Set session timeout settings using ng-idle
     */
    setSessionTimeout(): void {
        //get session timeout warning time and session timeout time from the user data
        this.sessionTimeout = Number(this.userData.config.session_timeout);
        this.warningTimeout = Number(this.userData.config.session_timeout_warning);
        //check the user is loged in or not
        if (this.isUserLoggedIn()) {
            //set session timeout warning time and session timeout time in idle
            this.idle.setIdle((this.sessionTimeout * 60) - this.warningTimeout);
            this.idle.setTimeout(this.warningTimeout);
            this.setUserLastActivityTimeStamp();
        }
        //set interrupts to check the user is idle or not
        this.idle.setInterrupts(this.createCustomInterruptSources(null));

        //on timeout of session time
        this.idle.onTimeout.subscribe(() => {
            if (this.stoplogout) {
                this._structureService.logout(true);
            }
        });

        this.idle.onInterrupt.subscribe(()=> {
            if (this.isUserLoggedIn()) {
                //set session timeout warning time and session timeout time in idle
                this.idle.setIdle((this.sessionTimeout * 60) - this.warningTimeout);
                this.idle.setTimeout(this.warningTimeout);
                this.setUserLastActivityTimeStamp();
                this.idle.watch();
            }
        })
        //on timeout of session warning time 
        this.idle.onTimeoutWarning.subscribe((countdown) => {
            if (!($("#sessionout").data('bs.modal') || {})._isShown) {
                //popup session timeout warning
                $('#sessionout').modal({ show: true });
                this.stoplogout = true;
                this.flagstop = false;
            }
            //show countdown message
            this.timeoutMsg = this._ToolTipService.getTranslateDataWithParam(
                'MESSAGES.SESSION_TIMEOUT',
                { countdown }
            );
        });
        //start/reset the idle process
        this.idle.watch();
    }

    /**
     * Check the user is logged in or not by checking the authenticationToken exist in local storage
     * @returns boolean - true if the user is logged in, and false if the user is logged out
     */
    isUserLoggedIn(): boolean {
        if (!isBlank(localStorage.getItem('authenticationToken'))) {
            return true;
        }
        return false;
    }

    /**
     * Create custom interrupts to determine the user is active or inactive
     * @param options EventTargetInterruptSource 
     */
    createCustomInterruptSources(options) {
        return [
            new DocumentInterruptSource('keydown touchstart touchmove scroll click', options),
            new StorageInterruptSource(options)
        ];
    }
/**
* To optimize the ngFor directive performance
*/
  trackByUserId(index: number, users: any) {
    return users.userId;
  }
/**
 * To get and set chatroom users. Expects staff and partners only.
 * @param user user data type of any
 * @returns void
 */
onStaffPartnerUserChange(user): void {    
    if(user.selected) {
        //Either staff/partner. Not staff && partner
        //Below condition will check for any other roles are selected
        if((this.optionShow === CONSTANTS.userTypes.partner
            && +user.roleId === CONSTANTS.userGroupIds.partner)
            ||(this.optionShow === CONSTANTS.userTypes.staff
            && +user.roleId !== CONSTANTS.userGroupIds.partner 
            && +user.roleId !== CONSTANTS.userGroupIds.patient)) {
                this.chatRoomUsers.push(user);
            }
    } else {
        this.chatRoomUsers = this.chatRoomUsers.filter((item)=> {
            return item.userId !== user.userId;
        });
    }
    if(!this.chatRoomUsers.length) {
        this.showChatBtn = false;
    }
  }
  /**
   * @returns void
   */
    createChatRoom(): void {
        if(this.optionShow === 'msggroups' ||   this.optionShow === 'groups' && 
            (!isBlank(this.selectedAdmissionId) || !this._structureService.isMultiAdmissionsEnabled)) {
            this.createMessgeGroupChatroom(this.selectedGroup);
        }
        if((this.optionShow === CONSTANTS.userTypes.partner || this.optionShow === CONSTANTS.userTypes.staff) && !isBlank(this.chatRoomUsers)) {
            this.selectedModal(this.chatRoomUsers[0]);
        }
        if(this.optionShow === CONSTANTS.userTypes.patient && 
            (!isBlank(this.selectedAdmissionId) || !this._structureService.isMultiAdmissionsEnabled)) {
            this.initiateChatWithPatient(this.chatRoomUsers[0]);
        }
    }
    /**
   * handleVisibilityChange to detect browser tab changes. Used for PWA.
   */
    handleVisibilityChange(): void {
        if (document.hidden) {
            this.pause();
        } else {
            this.resume();
        }
    }
    pause(): void {
        this.idle.stop();
    }
    resume(): void {
        if (this.isUserLoggedIn()) {
            const appResumeTimeStamp = Math.round((new Date() as any) / 1000);
            const sessionRemaining = this.sessionTimeout * 60 - (appResumeTimeStamp - this.userLastActivityTimeStamp);
            if (sessionRemaining > 0) {
                const idle = sessionRemaining - this.warningTimeout;
                const timeOut = sessionRemaining < this.warningTimeout ? sessionRemaining - 1 : this.warningTimeout;
                this.logger.log('#sessiontimeout',{sessionRemaining,appResumeTimeStamp,idle,timeOut});
                this.idle.clearInterrupts();
                this.watchIdle(idle, timeOut);
            } else {
                this._structureService.logout(true);
            }
        }
    }
  /**
   * watchIdle
   * @param idleTime number
   * @param timeOut number
   */
  watchIdle(idleTime: number, timeOut: number): void {
    idleTime = idleTime > CONSTANTS.defaultTimerValue ? idleTime : CONSTANTS.defaultTimerValue;
    timeOut = timeOut > CONSTANTS.defaultTimerValue ? timeOut : CONSTANTS.defaultTimerValue;
    if (this.isUserLoggedIn()) {
      this.idle.setInterrupts(this.createCustomInterruptSources(null));
      this.idle.setIdle(idleTime);
      this.idle.setTimeout(timeOut);
      this.idle.watch();
    } else {
      this.idle.stop();
    }
  }
  setUserLastActivityTimeStamp() {
    this.userLastActivityTimeStamp = Math.round((new Date() as any) / 1000);
  }
  /**
   * Search group subjects based on the search text
   * @param event type of LoadMoreItems; E.g {searchText:'', loadMore: false}
   */
  searchForMessageGroupSubjects(event: LoadMoreItems) {
    this.getAllMessageGroupTopicsByGroup(event);
    this.resetSearchSelect = false;
    this.showChatBtn = false;
  }
  /**
   * To improve the *ngFor directive performance
   * @param item an object, which should contain prop 'id'
   */
  trackByMsgGrpSubjectId(index,item) {
    return item.patientId || item.groupId
  }
  /**
   * On user selects the subject
   * @param event {id: '', name: ''}
   */
  onMsgGrpSubjectChange(event) {
    this.showChatBtn = true;
    if(!isBlank(event) && !isBlank(event.id)) {
        // If already existing subject is selected
        if(+event.id !== -1) {
            this.selectedTopic = this.messageGroupTopic.find((topic)=> { return topic.id === event.id });
            if (this.selectedTopic) this.selectedTopic.userInfoMsg = true;
        } else {
            // If new subject is selected       
            this.selectedTopic = {
                id: 0,
                subject: event.name,
                message_group_id: this.selectedGroup.groupId,
                isParticipant: 0,
                userInfoMsg: false
            }
            this.msgGrpSubjectOffset = CONSTANTS.contentOffset;
        }
    } else {
        // If selection is cleared
        this.selectedTopic = {
            id: -1,
            userInfoMsg: false
        }
        this.msgGrpSubjectOffset = CONSTANTS.contentOffset;
        this.showChatBtn = false;
    }   
  }
  
  /**
   * Function to start new chant anywhere from the application
   */
  startNewChat(){
    this._SharedService.chatWith.emit(this.userData.group);
  }
/**To get the chat with options for create chatroom */
  private getChatWithOption(): string {
    const chatWithOptions = {
        patient: ChatWithTypes.PATIENT,
        staff: ChatWithTypes.STAFF,
        partner: ChatWithTypes.PARTNER,
        groups: ChatWithTypes.PATIENT_GROUP,
        msggroups: ChatWithTypes.MESSAGE_GROUP,
        role: ChatWithTypes.ROLE
    };
    return chatWithOptions[this.optionShow] || '';
  }
executeData(searchKeyword: string,data: MessageGroup[]): void {
    if(searchKeyword){
        this.prevText = searchKeyword;
    }
    if(!data || !data.length) {
        this.hideLoadMore = true;
    } else {
        if(data.length === 25) {
            this.hideLoadMore = false;
        } else {
            this.hideLoadMore = true;
        }

        if(!this.loadMoreFlag){
            this.allMessageGroups = [];
        }
        this.allMessageGroups = [...this.allMessageGroups, ...data];
        this.messageGroup = this.allMessageGroups;
    }
    this._structureService.messageGroups = this.messageGroup;
    this.loadMoreFlag = false;
}
logoutUser() {
    if(this._structureService.flag == 1){
        this._structureService.flag = 0;
        }
        if(this.userData && this.userData.config && this.userData.config.showElevio == '1' && Elevio){
            this._ngZone.runOutsideAngular(() => {
                this._ngZone.run(() => { 
                Elevio.disable();
            });
            });
        }
        let activityData = {
            activityName: "User Logout",
            activityType: "user access",
            activityDescription: "User logged out from " + this.currentPath + " (" +  this.userData.displayName  + ")"
          };  
    this._structureService.trackActivity(activityData);
    this._structureService.logout();
    this._intakeService.blogStaffList = [];
}
navigateToPage(url) {
    this._structureService.navigateToPage(url);
}
hasChatPermission() {
    this.initiateChatPermission = this._structureService.hasInitiateChatPermission();
    if (!this.initiateChatPermission) {
        this.infoBottomHide();
    }
}
    onSelectStaff(item) {
        if((!isBlank(item['users']) && !isBlank(item['users'][0].id)) || !item['users'].length) {
            this.chatRoomUsers = item['users'];         
        } else if (!isBlank(item.reset) && item.reset) {
            this.chatRoomUsers = [];
        }
        this.showChatBtn = this.chatRoomUsers.length ? true : false;
    }
    onSelectMsgGrp(item) {
        if  (!isBlank(item.id)) { 
            this.showGroupSubjects(item);
        } else {
            this.showChatBtn = false;
            this.showSubject = false;
            this.formattedMessageList = [];
        }
    }
    searchMsgGrp(searchData) {
        this.getAllMessageGroupDetails(this.userData.tenantId, this.userData.userId, searchData, '0');  
    }
    resetGroup() {
        this.showSubject = false;
        this.showChatBtn = false;
    }
    setPatientId(item) {
        const patientDetails = item.users;
        this.selectedAdmissionId = item.admissions.length ? item.admissions[0].id : '';
        const selectedAdmissionDetails = item.admissions.length ? item.admissions[0] : {};
        const showChatButton = !isBlank(patientDetails) && !(isBlank(this.selectedAdmissionId) && this._structureService.isMultiAdmissionsEnabled);
        if  (this.optionShow === 'groups' && showChatButton) { 
            this.showGroupSubjects(patientDetails[0], selectedAdmissionDetails);
        } else if(this.optionShow === 'patient' && showChatButton) {
            this.chatRoomUsers = patientDetails;
            this.showChatBtn = true;
        } else if(this.optionShow === 'groups' && !showChatButton) {
            this.resetGroup();
        } else if(this.optionShow === 'patient' && !showChatButton) {
            this.showChatBtn = false;
        }
    }
    getAdmissionsList(userid, loadMore = false, searchText = '', openModal = false) {
        NProgress.start();
        this.admissionsLoading = true;
        this.selectedPatientId = userid ? String(userid) : this.selectedPatientId;
        if (this.userData.alternate_contact_patient && this.userData.alternate_contact_patient.length > 1) {
            this.associatedUserId = String(userid);
        }
        this.admissionsListPage = loadMore ? this.admissionsListPage + 1 : 0;
        const requestBody =  {
            currentPage: this.admissionsListPage,
            patientIds: [this.selectedPatientId],
            rowsPerPage: this.limit,
            searchKey: !isBlank(this.admissionsSearchText) ? `%${this.admissionsSearchText}%` : '',
            sortDirection: 'DESC',
            sortBy: 'dateCreated',
        };
        this.admissionsListPage = requestBody.currentPage;
        const url = `${APIs.getAdmissionsListEndPoint}?requestedFields=admissionId,admissionName,siteId,siteName,startDate,lineOfService,status`;        
        this.httpService.doPost(url, requestBody).subscribe(
          (res: any) => {
            NProgress.done();
            this.admissionsList = loadMore ? [...this.admissionsList, ...res.content] : res.content;
            if (this.admissionsList.length === 1 && !loadMore && isBlank(searchText)) {
              this.chatWithModel(this.userGroupId, this.associatedUserId, res.content[0].admissionId);
            } else {
              if (openModal) {
                this.showPatientListModal();
              }
              this.showAdmissionsList = true;
              this.totalAdmissionsCount = (res.page && res.page.totalElements) || 0;
              this.admissionsLoading = false;
            }
          },
          () => {
            NProgress.done();
            this.showAdmissionsList = true;
            this.admissionsLoading = false;
            this.admissionsList = [];
            this.totalAdmissionsCount = 0;
            if (openModal) {
              this.showPatientListModal();
            }
          }
        );
    }
    showPatientList() {
        if (this.userData.alternate_contact_patient && this.userData.alternate_contact_patient.length > 1) {
            this.showAdmissionsList = false;
        } else {
            $('#top-bar-patient-list-modal').modal('hide');
        }
        this.clearAdmissionsData();
    }
    resetAdmissionsSearch() {
        this.clearAdmissionsData();
        this.getAdmissionsList(this.selectedPatientId, false, '');
    }
    clearAdmissionsData() {
        this.admissionsList = [];
        this.totalAdmissionsCount = 0;
        this.admissionsSearchText = '';
        this.admissionsListPage = 0;
    }   
}
