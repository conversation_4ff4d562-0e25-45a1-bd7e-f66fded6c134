import { Component, OnInit, Pipe, PipeTransform } from '@angular/core';
import { DatePipe } from '@angular/common';
import { InboxService } from './../../structure/inbox/inbox.service';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Pipe({
  name: 'TopBarfilterUsersList'
})
export class TopBarfilterUsersListPipe implements PipeTransform {
    transform(items:any[], exponent: string): any {
        if (!items)
        return items;        
        return items.filter(item =>{
           for (let key in item ) {
             //console.log(item,exponent);
             if((""+item.userId!=exponent)){
                return true;
             }
           }
           return false;
        });
    }
}

@Pipe({
  name: 'searchRolefilter'
})

export class SearchRoleFilterPipe implements PipeTransform {
  transform(items: any[], field: string, value: string, keyItems:any): any[] {
    if (!items){
      return [];
    }else {
      var result = items.filter( (item)=> {
        //console.log(field,item,value)
        if(item[field.split('.')[0]][field.split('.')[1]].toLowerCase().indexOf(value.toLowerCase()) != -1) {
          item.filterUserStatus = false;
          return item;
        } else {
          item.filterUserStatus = true;
          var status = false;
          if(keyItems) {
            item['userList'].forEach((user) => {
              for (var key in keyItems) {
                if(keyItems[key] in user) {
                  if(user[keyItems[key]].toLowerCase().indexOf(value.toLowerCase()) != -1) {
                    status = true;
                    break;
                  }
                }
              }
            });
          } else {
            item['userList'].forEach((user) => {
              if("displayname" in user) {
                if(user.displayname.toLowerCase().indexOf(value.toLowerCase()) != -1) {              
                  status = true
                }
              }
              if("name" in user) {
                if(user.name.toLowerCase().indexOf(value.toLowerCase()) != -1) {
                  status = true
                }
              }
            });
          }
          if(status) {
            return item
          }
        }
      });
      return result;
    }
  }
}

@Pipe({
  name: 'filterUsersList'
})
export class filterUsersListPipe implements PipeTransform {
    transform(items:any[], exponent: string): any {
        if (!items)
        return items;        
        return items.filter(item =>{
           for (let key in item ) {
             //console.log(item,exponent);
             if((""+item.userId!=exponent)){
                return true;
             }
           }
           return false;
        });


    }
}

@Pipe({
  name: 'searchfilterroletreeview'
})

export class SearchFilterRoleTreeViewPipe implements PipeTransform {
  transform(items: any[], field: any, value: string): any[] {
    if (!items) {
      return [];
    } else {
      if(value) {
        var result = [];
        items.filter( (user, key)=> {
          if(field.length) {
            for(var index in field) {
              if(user[field[index]].toLowerCase().indexOf(value.toLowerCase()) != -1) {
                result.push(user);
                break;
              }
            }
          } else {
            if(user[field].toLowerCase().indexOf(value.toLowerCase()) != -1) {
              result.push(user);
            }
          }
        });
        return result;
      } else {
        return items;
      }
    }
  }
}

@Pipe({
  name: 'searchRolefilterpatient'
})

export class SearchRoleFilterPatientPipe implements PipeTransform {
  transform(items: any[], field: string, value: string): any[] {
    if (!items){
      return [];
    }else {
      var result = items.filter( (item)=> {
        //console.log(field,item,value)
        if(item[field.split('.')[0]][field.split('.')[1]].toLowerCase().indexOf(value.toLowerCase()) != -1) {
          item.filterUserStatus = false;
          return item;
        } else {
          item.filterUserStatus = true;
          var status = false;
          item['userList'].forEach((user) => {
            if("displayname" in user) {
              if(user.displayname.toLowerCase().indexOf(value.toLowerCase()) != -1) {              
                status = true
              }
            }
            if("name" in user) {
              if(user.name.toLowerCase().indexOf(value.toLowerCase()) != -1) {
                status = true
              }
            }
            if("caregiver_displayname" in user) {
              if(user.caregiver_displayname && user.caregiver_displayname !=null && user.caregiver_displayname.toLowerCase().indexOf(value.toLowerCase()) != -1) {
                status = true
              }
            }
          });
          if(status) {
            return item
          }
        }
      });
      return result;
    }
  }
}

@Pipe({
  name: 'searchfilterroletreeviewpatient'
})

export class SearchFilterRoleTreeViewPatientPipe implements PipeTransform {
  transform(items: any[], field: string, value: string): any[] {
    if (!items) {
      return [];
    } else {
      if(value) {
        var result = [];
        items.filter( (user, key)=> {
          if(user[field].toLowerCase().indexOf(value.toLowerCase()) != -1 || (user['caregiver_displayname'] && user['caregiver_displayname'] !=null && user['caregiver_displayname'].toLowerCase().indexOf(value.toLowerCase()) != -1)) {
            result.push(user);
          }
        })
        return result;
      } else {
        return items;
      }
    }
  }
}

@Pipe({  
  name: 'orderBy'
 })
export class OrderByPipe implements PipeTransform {
transform(users: any[], field: string): any[] {
    if (!users)
    return users;
    users.sort((a: any, b: any) => {
      var nameA=a.groupName.toLowerCase(), nameB=b.groupName.toLowerCase();
      if (nameA < nameB) {
        return -1;
      } else if (nameA > nameB) {
        return 1;
      } else {
        return 0;
      }
    });
    return users;
  }
}

@Pipe({  
  name: 'filterPatientDiscussionGroup'
 })
export class filterPatientDiscussionGroupPipe implements PipeTransform {
transform(items:any[], exponent: string): any {
        if (!items)
        return items;        
        return items.filter(item =>{
           for (let key in item ) {
             exponent = (exponent==undefined ? '1' : exponent);
             if((!exponent)){
               return item.pdgroup!='1';
             }else{
               return true;
             }
           }
           return false;
        });
    }
}

@Pipe({  
  name: 'scheduleSelectionFilter',
  pure: false
 })
export class scheduleSelectionFilterPipe implements PipeTransform {
  constructor(       
        private _inboxService: InboxService
  ) {}
transform(items:any[], exponent: string): any {
        if (!items)
        return items;        
        return items.filter(item =>{
           for (let key in item ) {
             if(exponent=='3'){
                if(this._inboxService.scheduleSelectionFilter(item)){
                  return true;
                }
             }else{
                  return true;
             }
           }
           return false;
        });
    }
}

@Pipe({
  name: 'virtualStaffFilter'
})
export class virtualStaffFilterPipe implements PipeTransform {
    transform(arr: any[], exponent: string): any {
        //console.log("NEW FILTERRRRRRRRRRRRRRRRR",arr);
         /*for (var i = 0; i < arr.length; i++) {
            return arr[i].status != '7';
         }*/
        if (!arr)
          return arr;        
        return arr.filter(item =>{
           for (let key in item ) {
             //console.log(item,exponent);
             if((item.tenantRoleId != null)){
                return true;
             }
           }
           return false;
        });
    }
}

