import {
  TestBed,
  async,
  inject,
  ComponentFixture,
} from '@angular/core/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Apollo, ApolloModule } from 'apollo-angular';
import { AuthService } from 'app/structure/shared/auth.service';
import { SharedService } from 'app/structure/shared/sharedServices';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { provideClients } from 'test-utils';
import { PermissionService } from 'app/services/permission/permission.service';
import { StoreService } from 'app/structure/shared/storeService';
import { StructureService } from '../structure/structure.service';
import { AuthGuard } from './auth.guard';

describe('AuthGuard', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        StructureService,
        Apollo,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        TranslateService,
        AuthGuard,
        AuthService,
        PermissionService,
        StoreService
      ],
      declarations: [],
      imports: [
        RouterTestingModule,
        HttpClientTestingModule,
        HttpModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF,
        }),
        TranslateModule.forRoot(),
      ],
    });
  });

  it('should create the authguard', inject([AuthGuard], (guard: AuthGuard) => {
    expect(guard).toBeTruthy();
  }));
});
