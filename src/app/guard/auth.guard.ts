import { Injectable } from '@angular/core';
import { Router, CanActivate, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs/Observable';
import { StructureService } from '../structure/structure.service';

@Injectable()
export class AuthGuard implements CanActivate {
  userDetails:any = [];
  privileges = [];
  config = [];
  constructor(private router: Router, private _structureService: StructureService) {
    
    const userDetails = this._structureService.userDetails ? JSON.parse(this._structureService.userDetails) : this._structureService.userDetails;

    this.userDetails = userDetails;
    if(userDetails && userDetails.config)
    this.config = userDetails.config;
    const accountId = (!userDetails || typeof (userDetails.tenantId) === 'undefined') ? (this._structureService ? this._structureService.getCookie('tenantId') : '') : userDetails.tenantId;
    const userRole = (!userDetails || typeof (userDetails.roleName) === 'undefined') ? (this._structureService ? this._structureService.getCookie('userRole') : '') : userDetails.roleName;
    if (userRole === 'Super Admin') {

      this.privileges['superAdmin'] = true;

    } else {

      /*if (userDetails.config.enable_external_integration == '1') {
        this.privileges['externalIntegration'] = true;
      } else {
        this.privileges['externalIntegration'] = false;
      }*/
      this.privileges['tenantConfig'] = true;

    }
    let manageTenants:any = '';
    if(userDetails && userDetails.privileges)
    manageTenants =  userDetails.privileges;
    manageTenants = (!manageTenants || typeof (manageTenants) === 'undefined') ? (this._structureService ? this._structureService.getCookie('userPrivileges') : '') : manageTenants;
    manageTenants = manageTenants.split(',');
    for (let i = 0; i < manageTenants.length; i++) {
      this.privileges[manageTenants[i]] = true;
    }
  }

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
      console.log(this.config)
      console.log('--------------can activate---------------'+ JSON.stringify(this.userDetails) + '---' + state.url)
      console.log(route, state)
        const expression = route.data.expression;
        const privileges = this.privileges;
        const config = this.config;
        this._structureService.routingUrl = state.url;
        if(this._structureService.canActivateRouteCheck(true, route))
        return true;
        else

        return false;
    }
}
