/** This class should contain routes only.
 * import this class and access the routes where needed. E.g. ROUTES.vistscheduler
*/

export class ROUTES{

    /**Routes in Visit Schedule section  */
    /** In service-worker.js file use link with out using constant variable. 
    * So need to update there also if any change happend in the following link.
    * visit-scheduler/staff/view-visits, visit-scheduler/patient/view-visits, visit-scheduler/my-visits
    */
    static readonly payor = '/visit-scheduler/payor';
    static readonly addPayor = '/visit-scheduler/payor/add';
    static readonly editPayor = '/visit-scheduler/payor/edit';
    static readonly administrationRoute = '/visit-scheduler/administration-route';
    static readonly addAdministrationRoute = '/visit-scheduler/administration-route/add';
    static readonly editAdministrationRoute = '/visit-scheduler/administration-route/edit';
    static readonly visitTask = '/visit-scheduler/visit-task';
    static readonly addVisitTask = '/visit-scheduler/visit-task/add';
    static readonly editVisitTask = '/visit-scheduler/visit-task/edit';
    static readonly visitType = '/visit-scheduler/visit-type';
    static readonly addVisitType = '/visit-scheduler/visit-type/add';
    static readonly editVisitType = '/visit-scheduler/visit-type/edit';
    static readonly visitLocation = '/visit-scheduler/visit-location';
    static readonly addVisitLocation = '/visit-scheduler/visit-location/add';
    static readonly editVisitLocation = '/visit-scheduler/visit-location/edit';
    static readonly resource = '/visit-scheduler/resource';
    static readonly addResource = '/visit-scheduler/resource/add';
    static readonly editResource = '/visit-scheduler/resource/edit';
    static readonly therapyType = '/visit-scheduler/therapy-type';
    static readonly addTherapyType = '/visit-scheduler/therapy-type/add';
    static readonly editTherapyType = '/visit-scheduler/therapy-type/edit';
    static readonly resourceUnavailability = '/visit-scheduler/resource-unavailability';
    static readonly addResourceUnavailability = '/visit-scheduler/resource-unavailability/add';
    static readonly editResourceUnavailability = '/visit-scheduler/resource-unavailability/edit';
    static readonly addResourceAvailability = '/visit-scheduler/resource-availability/add';
    static readonly viewVisitScheduler = '/visit-scheduler/view-visits/scheduler';
    static readonly createVisit = '/visit-scheduler/create-visit/create';
    static readonly myVisits = '/visit-scheduler/my-visits';
    static readonly allVisits = '/visit-scheduler/all-visits';
    static readonly staffUnavailability = '/visit-scheduler/staff-unavailability';
    static readonly addStaffUnavailability = '/visit-scheduler/staff-unavailability/add';
    static readonly editStaffUnavailability = '/visit-scheduler/staff-unavailability/edit';
    static readonly addStaffAvailability = '/visit-scheduler/staff-availability/add';
    static readonly otherStaffUnavailability = '/visit-scheduler/other-staff-unavailability';
    static readonly addOtherStaffUnavailability = '/visit-scheduler/other-staff-unavailability/add';
    static readonly editOtherStaffUnavailability = '/visit-scheduler/other-staff-unavailability/edit';
    static readonly addOtherStaffAvailability = '/visit-scheduler/other-staff-availability/add';
    static readonly modifyVisit = '/visit-scheduler/modify-visit';
    static readonly modifyVisitId = '/visit-scheduler/modify-visit/:id';
    static readonly patient = '/visit-scheduler/patient';
    static readonly staffViewVisits = '/visit-scheduler/staff/view-visits';
    static readonly staffViewVisitsId = '/visit-scheduler/staff/view-visits/:id';
    static readonly staffViewVisitsAll = '/visit-scheduler/staff/view-visits/all';
    static readonly patientViewVisits = '/visit-scheduler/patient/view-visits';
    static readonly patientViewVisitsId = '/visit-scheduler/patient/view-visits/:id';
    static readonly administrationMode = '/visit-scheduler/administration-mode';
    static readonly addAdministrationMode = '/visit-scheduler/administration-mode/add';
    static readonly editAdministrationMode = '/visit-scheduler/administration-mode/edit';
    static readonly chatRoom = '/inbox/chatroom';
    static readonly myformworklist = '/forms/worklist';
    static readonly allformworklist = '/forms/list';
    static readonly editPatient = '/users/patients/edit-patient/{patientId}';
  static readonly manageForms = '/forms/manage';
  static readonly formTypes = '/forms/form-tags';
  static readonly formList = '/forms/list/view';
    static readonly editAdmission = `${ROUTES.editPatient}/admission/{admissionId}`;
}