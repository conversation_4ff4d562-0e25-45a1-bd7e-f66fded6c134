import gql from 'graphql-tag';

export const Queries = {
  getAccounts: gql`
    query getAccounts {
      getAccounts
    }
  `,
  getInventorySubcategoryNames: gql`
    query getSessionTenant($sessionToken: String!, $templateId: Int, $groupId: Int, $itemId: Int) {
      getSessionTenant(sessionToken: $sessionToken) {
        inventoryRequestTemplates(params: { templateId: $templateId, groupId: $groupId, itemId: $itemId }) {
          groups {
            id
            name
          }
        }
      }
    }
  `,
  getInventorySubCategories: gql`
    query getInventorySubCategories($categoryId: Int!) {
      getInventorySubCategories(categoryId: $categoryId)
    }
  `,

  getInventories: gql`
    query getInventories($subCategoryId: Int!) {
      getInventories(subCategoryId: $subCategoryId)
    }
  `,

  getDefaultUserMessageEscalations: gql`
    query getDefaultUserMessageEscalations($tenantId: Int!) {
      getDefaultUserMessageEscalations(tenantId: $tenantId) {
        id
        displayName
        name
      }
    }
  `,

  getAccount: gql`
    query getSessionTenant($sessionToken: String!, $status: String, $privilegeKey: String) {
      getSessionTenant(sessionToken: $sessionToken) {
        name
        escalationTimeInSec
        defaultUserToReceiveEscalatedMessage {
          displayName
          id
        }
        messageEscalationBehavior
        patientsToInitiateChatDuringDay
        configurationSettings {
          configuration
          disable
        }
        noClinicianMessage
        noClinicianMessageDuringWorkingHours
        formPartnerRefferalTags {
          id
          tagName
          tagMeta
        }
        staffUsers(status: $status, privilegeKey: $privilegeKey) {
          displayName
          lastName
          firstName
          status
          id
          naTags
          naTagNames
          companyNursingAgency
          userEMverification {
            mobileVerified
            emailVerified
          }
        }
      }
    }
  `,

  getConfig: gql`
    query getSessionTenant($sessionToken: String!) {
      getSessionTenant(sessionToken: $sessionToken) {
        patientDiscussionGroup
      }
    }
  `,

  getPreferencesQuery: gql`
    query getSessionTenant($sessionToken: String!, $status: String, $privilegeKey: String, $crossTenantId: Int!) {
      getSessionTenant(sessionToken: $sessionToken, crossTenantId: $crossTenantId) {
        name
        escalationTimeInSec
        defaultUserToReceiveEscalatedMessage {
          displayName
          id
        }
        clinicalLiaison {
          displayName
          id
        }
        staffsAwareEnrollBySms {
          displayName
          id
        }
        staffsAwareEnrollByEmail {
          displayName
          id
        }
        messageEscalationBehavior
        patientsToInitiateChatDuringDay
        configurationSettings {
          configuration
          disable
        }
        noClinicianMessage
        noClinicianMessageDuringWorkingHours
        staffUsers(status: $status) {
          displayName
          lastName
          firstName
          status
          id
          naTags
          naTagNames
          companyNursingAgency
          userEMverification {
            mobileVerified
            emailVerified
          }
        }
        privilegedStaffUsers(status: $status, privilegeKey: $privilegeKey) {
          displayName
          lastName
          firstName
          status
          id
        }
        sessionTimeoutInSec
        sessionTimeoutWarningInSec
        partnerPatientReferralIntroductiontext
        defaultOutgoingFilingCenterChatLog
        fileSavingFormatChatLogFilingCenter
        titleIncomingFilingCenter
        titleOutgoingFilingCenter
        chatAutoTranslate
        showPatientStatusDashboard
        enableDisplayName
        patientMessageSmsNotifcationBeyondBranch24hr
        enableEmailForPatient
        toggleChatTranslation
        gATracking
        watchTowerTracking
        messageForwardBehavior
        messageEscalationBehavior
        showPrototypes
        infusionSupport
        enableWhereIsMyDelivery
        enableWhenIsMyNurseComing
        messageTagging
        userTagging
        documentTagging
        patientDiscussionGroup
        patientSelfInventory
        patientsToInitiateChatDuringDay
        showChatHistoryToNewParticipant
        patientSignupWithAdminApproval
        clinicianSignupWithAdminApproval
        modifiedAt
        createdAt
        supportAndFeedback
        noClinicianMessage
        conversationType
        chatRole1DisplayName
        chatRole2DisplayName
        chatRole1MessageTemplate
        chatRole2MessageTemplate
        initiationMessage
        enableFilingCenter
        EnableNursingAgencyVisibilityRestrictions
        nursingAgenciesforManageMessageGroups
        nursingAgenciesforManageEducationMaterials
        nursingAgenciesThatPatientsAllowedToChatWithPharmacy
        nursingAgenciesThatStaffsAllowedToChatWithPharmacy
        allowPrefillingPartialEnrollment
        showPatientChatwithModal
        caregiverSeesEnrollmentInvitationData
        enableMaskedDiscussionGroup
        tenantTimezone
        newPatientChatWelcomeMessage
        defaultStaffRole
        defaultPartnerRole
        defaultPartnerCategory
        enrollMessage
        defaultStaffTags
        defaultSites
        defaultPartnerTags
        flexSitePatientsCanChatWithInternalSiteStaffs
        isMaster
        masterEnabled
        enableApplessWorkflow
        enableMultiThreadPdg
        tokenExpiryTime
        verificationRememberTime
        verificationTokenExpiryTime
        chatRole1 {
          id
        }
        partnerReferralFormTag {
          id
        }
        chatRole2 {
          id
        }
        maskedDiscussionGroupReceiptRole {
          id
        }
        configurationSettings {
          configuration
          disable
        }
        taggedMessageApprover {
          displayName
          id
        }
        patientReminderTime
        patientReminderTypes {
          id
        }
        enrollmentReminderTypes {
          id
        }
        branchDays {
          id
        }
        messageDestinationConfig {
          endTime
          startTime
          branchStartTime
          branchEndTime
          chatwithUsNowRolesDuringDay {
            id
          }

          chatwithUsNowRolesAfterDay {
            id
          }
          iNeedHelpOrIHaveAQuestionRolesDuringDay {
            id
          }
          iNeedHelpOrIHaveAQuestionRolesAfterDay {
            id
          }
          whereIsMyDeliveryRolesDuringDay {
            id
          }
          whereIsMyDeliveryRolesAfterDay {
            id
          }
          whenWillMyNurseArriveRolesDuringDay {
            id
          }
          whenWillMyNurseArriveRolesAfterDay {
            id
          }
          defaultClinicianRolesDuringDay {
            id
          }
          defaultClinicianRolesAfterDay {
            id
          }
        }
      }
    }
  `,

  getPreferences: gql`
    query getSessionTenant($sessionToken: String!, $status: String, $privilegeKey: String) {
      getSessionTenant(sessionToken: $sessionToken) {
        name
        escalationTimeInSec
        defaultUserToReceiveEscalatedMessage {
          displayName
          id
        }
        clinicalLiaison {
          displayName
          id
        }
        staffsAwareEnrollBySms {
          displayName
          id
        }
        staffsAwareEnrollByEmail {
          displayName
          id
        }
        messageEscalationBehavior
        patientsToInitiateChatDuringDay
        configurationSettings {
          configuration
          disable
        }
        noClinicianMessage
        noClinicianMessageDuringWorkingHours
        staffUsers(status: $status) {
          displayName
          lastName
          firstName
          status
          id
          naTags
          naTagNames
          companyNursingAgency
          userEMverification {
            mobileVerified
            emailVerified
          }
        }
        privilegedStaffUsers(status: $status, privilegeKey: $privilegeKey) {
          displayName
          lastName
          firstName
          status
          id
        }
        sessionTimeoutInSec
        sessionTimeoutWarningInSec
        partnerPatientReferralIntroductiontext
        defaultOutgoingFilingCenterChatLog
        fileSavingFormatChatLogFilingCenter
        titleIncomingFilingCenter
        titleOutgoingFilingCenter
        chatAutoTranslate
        showPatientStatusDashboard
        enableDisplayName
        patientMessageSmsNotifcationBeyondBranch24hr
        enableEmailForPatient
        toggleChatTranslation
        gATracking
        watchTowerTracking
        messageForwardBehavior
        messageEscalationBehavior
        showPrototypes
        infusionSupport
        enableWhereIsMyDelivery
        enableWhenIsMyNurseComing
        messageTagging
        userTagging
        documentTagging
        patientDiscussionGroup
        patientSelfInventory
        patientsToInitiateChatDuringDay
        showChatHistoryToNewParticipant
        patientSignupWithAdminApproval
        clinicianSignupWithAdminApproval
        modifiedAt
        createdAt
        supportAndFeedback
        noClinicianMessage
        conversationType
        chatRole1DisplayName
        chatRole2DisplayName
        chatRole1MessageTemplate
        chatRole2MessageTemplate
        initiationMessage
        enableFilingCenter
        EnableNursingAgencyVisibilityRestrictions
        nursingAgenciesforManageMessageGroups
        nursingAgenciesforManageEducationMaterials
        nursingAgenciesThatPatientsAllowedToChatWithPharmacy
        nursingAgenciesThatStaffsAllowedToChatWithPharmacy
        allowPrefillingPartialEnrollment
        showPatientChatwithModal
        caregiverSeesEnrollmentInvitationData
        enableMaskedDiscussionGroup
        tenantTimezone
        enableMultiThreadPdg
        newPatientChatWelcomeMessage
        defaultStaffRole
        defaultPartnerRole
        defaultPartnerCategory
        enrollMessage
        defaultStaffTags
        defaultSites
        defaultPartnerTags
        flexSitePatientsCanChatWithInternalSiteStaffs
        isMaster
        masterEnabled
        enableApplessWorkflow
        tokenExpiryTime
        verificationRememberTime
        verificationTokenExpiryTime
        chatRole1 {
          id
        }
        partnerReferralFormTag {
          id
        }
        chatRole2 {
          id
        }
        maskedDiscussionGroupReceiptRole {
          id
        }
        configurationSettings {
          configuration
          disable
        }
        taggedMessageApprover {
          displayName
          id
        }
        patientReminderTime
        patientReminderTypes {
          id
        }
        enrollmentReminderTypes {
          id
        }
        branchDays {
          id
        }
        messageDestinationConfig {
          endTime
          startTime
          branchStartTime
          branchEndTime
          chatwithUsNowRolesDuringDay {
            id
          }

          chatwithUsNowRolesAfterDay {
            id
          }
          iNeedHelpOrIHaveAQuestionRolesDuringDay {
            id
          }
          iNeedHelpOrIHaveAQuestionRolesAfterDay {
            id
          }
          whereIsMyDeliveryRolesDuringDay {
            id
          }
          whereIsMyDeliveryRolesAfterDay {
            id
          }
          whenWillMyNurseArriveRolesDuringDay {
            id
          }
          whenWillMyNurseArriveRolesAfterDay {
            id
          }
          defaultClinicianRolesDuringDay {
            id
          }
          defaultClinicianRolesAfterDay {
            id
          }
        }
      }
    }
  `,
  getExternalSystems: (isMultiAdmissionsEnabled = false) => gql`
    query getSessionTenant($sessionToken: String!,$id:Int, $admissionId:String) {
           getSessionTenant(sessionToken:$sessionToken) {
            externalSystems(id:$id, admissionId:$admissionId){
              externalSystemId
              ${isMultiAdmissionsEnabled ? 'externalAdmissionId' : ''}
              uniqueIdentifier
              filenameExpression
              guId
              code
              name
              siteId
              accountId
              labelShowInEditPage
              }
          }
          
       }  
  `,

  getExternalIntegrationSystems: gql`
    query getSessionTenant($sessionToken: String!, $crossTenantId: Int) {
      getSessionTenant(sessionToken: $sessionToken) {
        externalIntegrationSystems(crossTenantId: $crossTenantId) {
          accountId
          externalSystemId
          uniqueIdentifier
          name
          code
          createdOn
          createdBy
          filenameExpression
          showEditor
          showInInvite
          requiredInInvite
          labelShowInEditPage
          extSysValue
          esiValues {
            id
            esiValue
            status
          }
          tenantAssoc
          userType
          siteId
          site {
            name
          }
        }
      }
    }
  `,
  checkExternalIntegrationSystems: gql`
    query getSessionTenant(
      $sessionToken: String!
      $crossTenantId: Int
      $IdentityValue: String
      $externalSystemId: String
      $messageGroup: String
      $messageGroupName: String
      $messageGroupLastName: String
      $messageGroupDob: String
      $branch: String
    ) {
      getSessionTenant(sessionToken: $sessionToken) {
        checkExternalIntegrationSystem(
          crossTenantId: $crossTenantId
          IdentityValue: $IdentityValue
          externalSystemId: $externalSystemId
          messageGroup: $messageGroup
          messageGroupName: $messageGroupName
          messageGroupLastName: $messageGroupLastName
          messageGroupDob: $messageGroupDob
          branch: $branch
        ) {
          patientId
          firstName
          lastName
          IdentityValue
          dateOfBirth
          pmstatus
          message
          status
          pstatus
        }
      }
    }
  `,

  getSignatureDocuments: gql`
    query getSessionTenant($sessionToken: String!) {
      getSessionTenant(sessionToken: $sessionToken) {
        signedDocuments {
          id
          message
          documentUrl
          sentOn
          isSigned

          from {
            id
            dateOfBirth
            avatar
            displayName
            firstName
            lastName
          }
          to {
            id
            dateOfBirth
            avatar
            displayName
            firstName
            lastName
          }
        }
      }
    }
  `,

  getMessageGroupMembers: gql`
    query getMessageGroupMembers($groupId: Int!) {
      getMessageGroupMembers(groupId: $groupId) {
        id
        displayName
        name
      }
    }
  `,
  getClinicianRoles: gql`
    query getClinicianRoles($tenantId: Int!) {
      getClinicianRoles(tenantId: $tenantId) {
        id
        displayName
        roleId
        userRole {
          roleId
          roleName
        }
      }
    }
  `,

  getMessagesByUser: gql`
    query getSessionTenant($sessionToken: String!) {
      getSessionTenant(sessionToken: $sessionToken) {
        chatRoomInitiators {
          id
          displayName
          avatar
        }
      }
    }
  `,
  getMessagesByUserwithSearch: gql`
    query getSessionTenant($sessionToken: String!, $search: String) {
      getSessionTenant(sessionToken: $sessionToken) {
        chatRoomInitiators(search: $search) {
          id
          displayName
          avatar
        }
      }
    }
  `,

  getAllActivities: gql`
    query getAllActivities($sessionToken: String!, $start: String, $end: String, $id: Int) {
      getAllActivities(sessionToken: $sessionToken, duration: { startDateUnix: $start, endDateUnix: $end }, id: $id) {
        activityTime
        activityName
        activityType
        activityDescription
        linkId
      }
    }
  `,

  getPatientsCountsDashboardWidget: gql`
    query getPatientsCountsDashboardWidget($sessionToken: String!, $start: String, $end: String) {
      getPatientsCountsDashboardWidget(sessionToken: $sessionToken, duration: { startDateUnix: $start, endDateUnix: $end }) {
        total
        patientsThatHaveLoggedIn
        patientsWithAccountsButHaveNotLoggedIn
        patientsWithoutPasswords
        patientsDischarged
        patientsRequiringAdminActivation
        patientsDeleted
      }
    }
  `,
  getStaffCountsDashboardWidget: gql`
    query getStaffCountsDashboardWidget($sessionToken: String!, $start: String, $end: String) {
      getStaffCountsDashboardWidget(sessionToken: $sessionToken, duration: { startDateUnix: $start, endDateUnix: $end }) {
        total
        staffThatHaveLoggedIn
        staffWithAccountsButHaveNotLoggedIn
        staffWithoutPasswords
        staffDeactivated
        staffRequiringAdminActivation
        staffDeleted
      }
    }
  `,
  getMessagesCountsDashboardWidget: gql`
    query getMessagesCountsDashboardWidget($sessionToken: String!, $start: String, $end: String) {
      getMessagesCountsDashboardWidget(sessionToken: $sessionToken, duration: { startDateUnix: $start, endDateUnix: $end }) {
        total
        created
        responded
        patientsEscalated
        staffEscalated
        archived
        forwared
        tagged
        signed
        broadcast
        translated
      }
    }
  `,

  getOutcomeMeasuresDashboardWidget: gql`
    query getOutcomeMeasuresDashboardWidget($sessionToken: String!, $start: String, $end: String) {
      getOutcomeMeasures(sessionId: $sessionToken, duration: { startDateUnix: $start, endDateUnix: $end }) {
        type
        name {
          text
        }
        value {
          type
          text
          cardinal
        }
        groups {
          name
          description
        }
      }
    }
  `,

  getChatIntiatedTopicWidget: gql`
    query getChatIntiatedTopicWidget($sessionToken: String!, $start: String, $end: String) {
      getChatIntiatedTopicWidget(sessionToken: $sessionToken, duration: { startDateUnix: $start, endDateUnix: $end }) {
        nurseComing
        deliveryComing
      }
    }
  `,
  getMessageTagUsageDashboardWidget: gql`
    query getMessageTagUsageDashboardWidget($sessionToken: String!, $start: String, $end: String) {
      getMessageTagUsageDashboardWidget(sessionToken: $sessionToken, duration: { startDateUnix: $start, endDateUnix: $end }) {
        tagName
        total
      }
    }
  `,
  getRoleBasedMessageTagUsageDashboardWidget: gql`
    query getRoleBasedMessageTagUsageDashboardWidget($sessionToken: String!, $start: String, $end: String) {
      getRoleBasedMessageTagUsageDashboardWidget(sessionToken: $sessionToken, duration: { startDateUnix: $start, endDateUnix: $end }) {
        tagName
        roleName
        total
      }
    }
  `,
  getRoleBasedMessageUsageDashboardWidget: gql`
    query getRoleBasedMessageUsageDashboardWidget($sessionToken: String!, $start: String, $end: String) {
      getRoleBasedMessageUsageDashboardWidget(sessionToken: $sessionToken, duration: { startDateUnix: $start, endDateUnix: $end }) {
        activityName
        total
        roleName
      }
    }
  `,
  getMessagesTranslatedDashboardWidget: gql`
    query getMessagesTranslatedDashboardWidget($sessionToken: String!, $start: String, $end: String) {
      getMessagesTranslatedDashboardWidget(sessionToken: $sessionToken, duration: { startDateUnix: $start, endDateUnix: $end }) {
        name
        count
      }
    }
  `,
  getPlatformUsageDashboardWidget: gql`
    query getPlatformUsageDashboardWidget($sessionToken: String!, $start: String, $end: String) {
      getPlatformUsageDashboardWidget(sessionToken: $sessionToken, duration: { startDateUnix: $start, endDateUnix: $end }) {
        android
        desktopApp
        ios
        hybrid
      }
    }
  `,

  getAllFormsSendAndSubmitted: gql`
    query getAllFormsSendAndSubmitted(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $formIds: String
      $formTypes: String
      $userIds: String
      $siteIds: String
    ) {
      getAllFormsSendAndSubmitted(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        formIds: $formIds
        formTypes: $formTypes
        userIds: $userIds
        siteIds: $siteIds
      ) {
        totalFormsSent
        totalFormsSubmitted
        totalFormsSentDelete
        totalFormsSubmittedDeleted
        totalFormsSubmittedStaffFacing
        totalFormsSubmittedPendingStaffFacing
        totalFormsSentP
        totalFormsSubmittedP
        totalFormsSentDeleteP
        totalFormsSubmittedDeletedP
        totalFormsPendingPatient
      }
    }
  `,

  getAllFormsSendAndSubmittedbyWeek: gql`
    query getAllFormsSendAndSubmittedbyWeek(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $formIds: String
      $formTypes: String
      $durationType: String
      $siteIds: String
    ) {
      getAllFormsSendAndSubmittedbyWeek(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        formIds: $formIds
        formTypes: $formTypes
        durationType: $durationType
        siteIds: $siteIds
      ) {
        common
        totalFormsSent
        totalFormsSubmitted
        totalFormsSentDelete
        totalFormsSubmittedDeleted
        totalFormsSubmittedStaffFacing
        totalFormsSentP
        totalFormsSubmittedP
        totalFormsSentDeleteP
        totalFormsSubmittedDeletedP
        totalFormsSubmittedStaffFacingTrend
        totalFormspendingStaffFacingTrend
        totalFormsSubmittedCompleted
        totalFormsSubmittedCompleteddeleted
        totalFormsPendingPatient
      }
    }
  `,
  getAllFormsSendPatientbyWeek: gql`
    query getAllFormsSendPatientbyWeek(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $formIds: String
      $formTypes: String
      $durationType: String
      $siteIds: String
    ) {
      getAllFormsSendPatientbyWeek(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        formIds: $formIds
        formTypes: $formTypes
        durationType: $durationType
        siteIds: $siteIds
      ) {
        common
        totalFormsSentbyStaff
      }
    }
  `,
  getAllFormsSendPatientbyWeekPractitioner: gql`
    query getAllFormsSendPatientbyWeekPractitioner(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $formIds: String
      $formTypes: String
      $durationType: String
      $siteIds: String
    ) {
      getAllFormsSendPatientbyWeekPractitioner(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        formIds: $formIds
        formTypes: $formTypes
        durationType: $durationType
        siteIds: $siteIds
      ) {
        common
        totalFormsSentbyStaff
      }
    }
  `,
  getAllFormsSendPatientbyWeekStaff: gql`
    query getAllFormsSendPatientbyWeekStaff(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $formIds: String
      $formTypes: String
      $durationType: String
      $siteIds: String
    ) {
      getAllFormsSendPatientbyWeekStaff(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        formIds: $formIds
        formTypes: $formTypes
        durationType: $durationType
        siteIds: $siteIds
      ) {
        common
        totalFormsSentbyStaff
      }
    }
  `,

  getAllFormsSendTitlebyWeek: gql`
    query getAllFormsSendTitlebyWeek(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $formIds: String
      $formTypes: String
      $durationType: String
      $siteIds: String
    ) {
      getAllFormsSendTitlebyWeek(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        formIds: $formIds
        formTypes: $formTypes
        durationType: $durationType
        siteIds: $siteIds
      ) {
        common
        totalFormsSentbyStaff
      }
    }
  `,
  getAllFormsSendTypebyWeek: gql`
    query getAllFormsSendTypebyWeek(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $formIds: String
      $formTypes: String
      $durationType: String
      $siteIds: String
    ) {
      getAllFormsSendTypebyWeek(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        formIds: $formIds
        formTypes: $formTypes
        durationType: $durationType
        siteIds: $siteIds
      ) {
        common
        totalFormsSentbyStaff
      }
    }
  `,
  getAllPatientChat: gql`
    query getAllPatientChat($sessionToken: String!, $start: String, $end: String, $siteIds: String, $selectedTenantId: String) {
      getAllPatientChat(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        siteIds: $siteIds
        selectedTenantId: $selectedTenantId
      ) {
        sent
        received
      }
    }
  `,

  getAllPatientInvite: gql`
    query getPatientsSignUpCount(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $siteId: String
    ) {
      getPatientsSignUpCount(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        siteId: $siteId
      ) {
        normalInvite
        cprInvite
        importUsers
        manualUsers
        userCenterInvite
      }
    }
  `,

  getAllPatientFrmAvrgTime: gql`
    query getPatientsFormAvrgTime(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $formIds: String
      $formTypes: String
      $siteIds: String
    ) {
      getPatientsFormAvrgTime(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        formIds: $formIds
        formTypes: $formTypes
        siteIds: $siteIds
      ) {
        averageTime
        patientCount
        tenantName
      }
    }
  `,

  getAllactivePatient: gql`
    query getAllactivePatient($sessionToken: String!, $start: String, $end: String, $crossTenantId: Int) {
      getAllactivePatient(sessionToken: $sessionToken, duration: { startDateUnix: $start, endDateUnix: $end }, crossTenantId: $crossTenantId) {
        count
      }
    }
  `,

  getSignatureRequestPatient: gql`
    query getSignatureRequestPatient($sessionToken: String!, $start: String, $end: String, $crossTenantId: Int, $selectedTenantId: String) {
      getSignatureRequestPatient(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
      ) {
        pending
        submitted
      }
    }
  `,
  getSignatureRequestHistory: gql`
    query signatureRequestHistory($sessionId: String, $id: Int) {
      signatureRequestHistory(sessionId: $sessionId, id: $id) {
        id
        activity
        activityTime
        activityDescription
      }
    }
  `,
  getSignatureRequestPatientList: gql`
    query getSignatureRequestPatientList(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $siteIds: String
    ) {
      getSignatureRequestPatientList(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        siteIds: $siteIds
      ) {
        pending
        submitted
      }
    }
  `,
  getSignatureRequestTextDisplaySearch: (showVirtualOnly = false, list = false) => gql`
    query signatureRequestTextsDisplaySearch(
      $sessionId: String!
      $roles: String!
      $search: String!
      $site_id: String
      $admissionId: String
      $filterByPatientId: Int
    ) {
      signatureRequestTextsDisplaySearch(
        sessionId: $sessionId
        roles: $roles
        ${showVirtualOnly ? 'virtual: true' : ''}
        search: $search
        site_id: $site_id
        admissionId: $admissionId
        filterByPatientId: $filterByPatientId
      ) {
        id
        userId
        displayName
        firstName
        lastName
        dateOfBirth
        patientIdentity {
          IdentityValue
        }
        avatar
        cmisId
        role
        ${
          list
            ? `
        userName
        mobile
        countryCode
        careGiver {
          id
          displayName
          userName
          firstName
          lastName
          mobile
          dob
        }
          `
            : ''
        }
        isVirtual
        userEMverification {
          mobileVerified
          emailVerified
        }
        oooInfo {
          isOutOfOffice
          message
          startDateTime
          endDateTime
        }
        alternateContacts {
          id
          roleId
          status
          displayName
          firstName
          lastName
          relation
          username
          ESIValue
          patientMrn
          patientId
          mobile
          countryCode
          password
          created_at
          modified_at
          cmisid
          patientId
          patientFirstName
          patientLastName
          patientStatus
          patientDob
          patientPassword
          patientDisplayName
          tenantId
          tenantName
          tenantRoleId
        }
      }
    }
  `,
  getSignatureRequestPieChartData: gql`
    query dashboardPieChart(
      $sessionToken: String!
      $start: String
      $end: String
      $type: ChartType
      $filterType: DashboardDurationType
      $selectedTenantId: String
      $siteId: String
    ) {
      dashboardPieChart(
        sessionToken: $sessionToken
        args: { startDateUnix: $start, endDateUnix: $end, type: $type, filterType: $filterType }
        selectedTenantId: $selectedTenantId
        siteId: $siteId
      ) {
        values
        labels
        fillColors
      }
    }
  `,
  getSignatureRequestLineChartData: gql`
    query dashboardLineChart(
      $sessionToken: String!
      $start: String
      $end: String
      $type: ChartType
      $filterType: DashboardDurationType
      $selectedTenantId: String
      $siteId: String
    ) {
      dashboardLineChart(
        sessionToken: $sessionToken
        args: { startDateUnix: $start, endDateUnix: $end, type: $type, filterType: $filterType }
        selectedTenantId: $selectedTenantId
        siteId: $siteId
      ) {
        label
        xAxisValues
        yAxisValues
        strokeColor
      }
    }
  `,

  getMessageCenterPieChartData: gql`
    query messageCenterPieChart(
      $sessionToken: String!
      $start: String
      $end: String
      $type: ChartType
      $filterType: DashboardDurationType
      $siteIds: String
      $selectedTenantId: String
    ) {
      messageCenterPieChart(
        sessionToken: $sessionToken
        args: { startDateUnix: $start, endDateUnix: $end, type: $type, filterType: $filterType }
        siteIds: $siteIds
        selectedTenantId: $selectedTenantId
      ) {
        values
        labels
        fillColors
      }
    }
  `,

  getMessageCenterLineChartData: gql`
    query messageCenterLineChart(
      $sessionToken: String!
      $start: String
      $end: String
      $type: ChartType
      $filterType: DashboardDurationType
      $siteIds: String
      $selectedTenantId: String
    ) {
      messageCenterLineChart(
        sessionToken: $sessionToken
        args: { startDateUnix: $start, endDateUnix: $end, type: $type, filterType: $filterType }
        siteIds: $siteIds
        selectedTenantId: $selectedTenantId
      ) {
        label
        xAxisValues
        yAxisValues
        strokeColor
      }
    }
  `,
  getenrolltrends: gql`
    query getAllInvitesByStaff(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $durationType: String
      $siteIds: String
    ) {
      getAllInvitesByStaff(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        durationType: $durationType
        siteId: $siteIds
      ) {
        common
        result
      }
    }
  `,

  getAllInvitesByenroll: gql`
    query getAllInvitesByenroll(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $durationType: String
    ) {
      getAllInvitesByenroll(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        durationType: $durationType
      ) {
        common
        resultpatient
        resultstaff
        resultpartner
      }
    }
  `,

  getenrolltrendspatient: gql`
    query getAllInvitesByPatientenroll(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $durationType: String
      $siteIds: String
    ) {
      getAllInvitesByPatientenroll(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        durationType: $durationType
        siteId: $siteIds
      ) {
        common
        result
      }
    }
  `,
  getenrolltrendsstaff: gql`
    query getAllInvitesByStaffenroll(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $durationType: String
      $siteIds: String
    ) {
      getAllInvitesByStaffenroll(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        durationType: $durationType
        siteId: $siteIds
      ) {
        common
        result
      }
    }
  `,

  getenrolltrendspartner: gql`
    query getAllInvitesByPartnerenroll(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $durationType: String
      $siteIds: String
    ) {
      getAllInvitesByPartnerenroll(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        durationType: $durationType
        siteId: $siteIds
      ) {
        common
        result
      }
    }
  `,
  getenrolltrendsstaffpatients: gql`
    query getAllInvitesBystaffPatient(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $durationType: String
      $userIds: String
      $siteIds: String
    ) {
      getAllInvitesBystaffPatient(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        durationType: $durationType
        userIds: $userIds
        siteId: $siteIds
      ) {
        common
        result
      }
    }
  `,
  getSignatureRequestUnreadCount: gql`
    query signatureRequestUnreadCount($sessionId: String!, $tenantId: Int, $site_id: String) {
      signatureRequestUnreadCount(sessionId: $sessionId, tenantId: $tenantId, site_id: $site_id) {
        totalCount
      }
    }
  `,

  getAllFormsSubmitted: gql`
    query getAllFormsSubmitted(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $siteIds: String
    ) {
      getAllFormsSubmitted(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        siteIds: $siteIds
      ) {
        sendFormCount
        FormName
        receipientName
      }
    }
  `,
  getAllFormsSend: gql`
    query getAllFormsSend($sessionToken: String!, $start: String, $end: String, $crossTenantId: Int, $selectedTenantId: String, $siteIds: String) {
      getAllFormsSend(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        siteIds: $siteIds
      ) {
        sendFormCount
        FormName
        FromName
      }
    }
  `,

  getAllFormsNotSend: gql`
    query getFormsNotSend($sessionToken: String!, $crossTenantId: Int, $selectedTenantId: String, $siteIds: String) {
      getFormsNotSend(sessionToken: $sessionToken, crossTenantId: $crossTenantId, selectedTenantId: $selectedTenantId, siteIds: $siteIds) {
        formId
        formName
      }
    }
  `,

  getinviteDashboardWidget: gql`
    query getAllInvites(
      $sessionToken: String!
      $start: String
      $end: String
      $crossTenantId: Int
      $selectedTenantId: String
      $userIds: String
      $siteId: String
    ) {
      getAllInvites(
        sessionToken: $sessionToken
        duration: { startDateUnix: $start, endDateUnix: $end }
        crossTenantId: $crossTenantId
        selectedTenantId: $selectedTenantId
        userIds: $userIds
        siteId: $siteId
      ) {
        patientInvited
        patientEnroll
        patientLoggedIn
        patientInviteNotOpen
        patientRecurring
        patientInviteNotSubmitted
        staffInvited
        staffEnroll
        staffLoggedIn
        staffInviteNotOpen
        staffRecurring
        staffInviteNotSubmitted
        partnerInvited
        partnerLoggedIn
        partnerEnroll
        partnerInviteNotOpen
        partnerRecurring
        partnerInviteNotSubmitted
        tenantToken
        failedInvites
        patientFailedInvites
        staffFailedInvites
        partnerFailedInvites
      }
    }
  `,

  getSignatureRequestDashboardWidget: gql`
    query getSignatureRequestDashboardWidget($sessionToken: String!, $start: String, $end: String) {
      getSignatureRequestDashboardWidget(sessionToken: $sessionToken, duration: { startDateUnix: $start, endDateUnix: $end }) {
        total
        signed
        pending
        type {
          name
          signed
          pending
        }
      }
    }
  `,

  getFAQPageVisitsDashboardWidget: gql`
    query getFAQPageVisitsDashboardWidget($sessionToken: String!, $start: String, $end: String) {
      getFAQPageVisitsDashboardWidget(sessionToken: $sessionToken, duration: { startDateUnix: $start, endDateUnix: $end }) {
        needHelp {
          visits
          type
          patients
        }
        airBubblesinIV {
          visits
          type
          patients
        }
        extensionTubingOnCatheter {
          visits
          type
          patients
        }
        extensionTubingOnCatheterResolved {
          visits
          type
          patients
        }
        extensionTubingOnCatheterNotResolved {
          visits
          type
          patients
        }
        bloodUnderIVDressing {
          visits
          type
          patients
        }
        bloodUnderIVDressingResolved {
          visits
          type
          patients
        }
        bloodUnderIVDressingNotResolved {
          visits
          type
          patients
        }
        bloodInExtensionTube {
          visits
          type
          patients
        }
        bloodInExtensionTubeResolved {
          visits
          type
          patients
        }
        bloodInExtensionTubeNotResolved {
          visits
          type
          patients
        }
        flushingDifficulties {
          visits
          type
          patients
        }
        flushingDifficultiesLine {
          visits
          type
          patients
        }
        flushingDifficultiesResolved {
          visits
          type
          patients
        }
        flushingDifficultiesLineInner {
          visits
          type
          patients
        }
        flushingDifficultiesNotResolved {
          visits
          type
          patients
        }
        medicationNotDripping {
          visits
          type
          patients
        }
        medicationNotDrippingResolved {
          visits
          type
          patients
        }
        medicationNotDrippingNotResolved {
          visits
          type
          patients
        }
        pumpAlarming {
          visits
          type
          patients
        }
        pumpAlarmingCadd {
          visits
          type
          patients
        }
        pumpAlarmingCurlin {
          visits
          type
          patients
        }
        pumpAlarmingResolved {
          visits
          type
          patients
        }
        pumpAlarmingNotListed {
          visits
          type
          patients
        }
        pumpAlarmingNotResolved {
          visits
          type
          patients
        }
      }
    }
  `,

  getRolesCountsDashboardWidgetComponent: gql`
    query getRolesCountsDashboardWidgetComponent($sessionToken: String!) {
      getRolesCountsDashboardWidgetComponent(sessionToken: $sessionToken) {
        role
        count
      }
    }
  `,

  getNotificationAndLocationUsageDashboardWidget: gql`
    query getNotificationAndLocationUsageDashboardWidget($sessionToken: String!, $start: String, $end: String) {
      getNotificationAndLocationUsageDashboardWidget(sessionToken: $sessionToken, duration: { startDateUnix: $start, endDateUnix: $end }) {
        notificationsEnabled
        LocationEnabled
      }
    }
  `,

  getInventoryCategory: gql`
    query getInventoryCategory($id: Int!) {
      getInventoryCategory(id: $id)
    }
  `,
  getInventorySubCategory: gql`
    query getInventorySubCategory($id: Int!) {
      getInventorySubCategory(id: $id)
    }
  `,

  qrySignatureRequests: gql`
    query signatureRequest($sessionId: String!, $crossTenantCmisId: Int) {
      signatureRequest(sessionId: $sessionId, crossTenantCmisId: $crossTenantCmisId) {
        id
        ownerId
        owner
        archivedUsers
        accountLevelArchived
        associateSignatureProcess
        associateSignatureByUsers {
          userId
          displayName
        }
        signatureByUsers {
          userId
          displayName
        }
        isRead
        document {
          associatePatient
        }
        displayText {
          text
        }
        createdOn
        signedOn
        archivedOn
        type {
          id
          name
          obtainSignature
          allowPendingApproveSignature
          allowRecipientRoles
          notifyOnSubmitSignatureRoles
          notifyOnSubmitSignatureUsers
          enableApplessWorkflow
          applessDevices
        }
        signatureStatus
      }
    }
  `,

  qryPartnerReferralForms: gql`
    query getSessionTenant($sessionId: String!) {
      getSessionTenant(sessionToken: $sessionId) {
        partnerReferalForms(tags: "Partner Patient Referral") {
          id
          formId
          tagName
          name
        }
        partnerPatientReferralIntroductiontext
      }
    }
  `,

  qrySupplyCountForms: gql`
    query getSessionTenant($sessionId: String!, $roleid: Int) {
      getSessionTenant(sessionToken: $sessionId) {
        supplyForms(roleid: $roleid) {
          id
          tagId
          formId
          tagName
          name
          facing
          category
          patientAssociation
          visibleToRoles
        }
      }
    }
  `,

  qryPartnerReferrals: gql`
    query getSessionTenant($sessionId: String!, $id: Int) {
      getSessionTenant(sessionToken: $sessionId) {
        partnerReferals(id: $id) {
          formSubmissionId
          formID
          therapyType
          fromID
          patientName
          createdOn
          updateOn
          updatedby
        }
        partnerPatientReferralIntroductiontext
        formPartnerRefferalTags {
          id
          tagName
          tagMeta
        }
      }
    }
  `,

  qrySignatureRequestsTenant: gql`
    query signatureRequestTenant($sessionId: String!, $access: Boolean!) {
      signatureRequestTenant(sessionId: $sessionId, access: $access) {
        id
        displayText {
          text
        }
        archivedUsers
        accountLevelArchived
        document {
          displayText
          pageCount
          associatePatient
        }
        ownerId
        owner
        signatureByUsers {
          userId
          displayName
        }
        signedOn
        createdOn
        type {
          id
          name
          patientAssociateRole
        }
        notifyOnSubmitSignatureUsers
        notifyOnSubmitSignatureRoles
        signatureStatus
      }
    }
  `,

  qryTenantFilingCenterContent: gql`
    query getTenantFilingCenterContent($sessionId: String!, $type: SyncFolderType!, $folder: String!, $crossTenantId: Int, $fromCrud: Boolean) {
      getTenantFilingCenterContent(sessionId: $sessionId, type: $type, folder: $folder, crossTenantId: $crossTenantId, fromCrud: $fromCrud) {
        folder
        name
        type
        size
        preview
        addedOn
        fileId
      }
    }
  `,

  qrySiteTenantFilingCenterContent: gql`
    query getSiteTenantFilingCenterContent(
      $sessionId: String!
      $type: SyncFolderType!
      $folder: String!
      $guid: String
      $crossTenantId: Int
      $fromCrud: Boolean
    ) {
      getSiteTenantFilingCenterContent(
        sessionId: $sessionId
        type: $type
        folder: $folder
        guid: $guid
        crossTenantId: $crossTenantId
        fromCrud: $fromCrud
      ) {
        folder
        name
        type
        size
        preview
        addedOn
        fileId
      }
    }
  `,

  getWorklistFormEntries: gql`
    query getFormData($sessionId: String!, $formID: Int, $submissionId: Int) {
      getFormData(sessionId: $sessionId, formID: $formID, submissionId: $submissionId) {
        submissionID
        elements {
          id
          label
          labelText
          value
          valueType
        }
      }
    }
  `,

  qryGetVirtualPatientOptions: gql`
    query getVirtualPatientOptions($sessionId: String!) {
      getVirtualPatientOptions(sessionId: $sessionId)
    }
  `,

  getThisPrivileges: gql`
    query getPrivileges($sessionToken: String!) {
      getPrivileges(sessionToken: $sessionToken) {
        id
        privilegeKey
        privilegeName
      }
    }
  `,

  getThisHtmlPage: gql`
    query posts($where: queryArgs) {
      posts(where: $where) {
        edges {
          node {
            id
            slug
            postId
            title
            content
            tags {
              edges {
                node {
                  name
                }
              }
            }
          }
        }
      }
    }
  `,

  qryFormTags: gql`
    query getSessionTenant($sessionToken: String!, $id: Int) {
      getSessionTenant(sessionToken: $sessionToken) {
        formTags(id: $id) {
          id
          tagName
          tagMeta
        }
      }
    }
  `,
  getAllCountries: gql`
    query getSessionTenant($sessionToken: String!) {
      getSessionTenant(sessionToken: $sessionToken) {
        allCountries {
          countryId
          countryName
          iso3
          iso2
        }
      }
    }
  `,
  getStates: gql`
    query getSessionTenant($sessionToken: String!, $id: Int) {
      getSessionTenant(sessionToken: $sessionToken) {
        allStates(id: $id) {
          stateId
          stateName
          postalCode
          countryId
        }
      }
    }
  `,
  patientUsers: (crossTenant) => gql`
    query getSessionTenant($sessionToken: String!, $id: Int${crossTenant ? ', $crossTenantId : Int!' : ''}) {
      getSessionTenant(sessionToken: $sessionToken${crossTenant ? ', crossTenantId: $crossTenantId' : ''}) {
        patientUsers(id: $id) {
          dateOfBirth
          avatar
          displayName
          lastName
          firstName
          status
          id
          tenantId
          gender
          userTags {
            id
            tagName
          }
          activeSince
          lastLogin
          password
          emails {
            value
            type
            primary
          }
          comments {
            comment
          }
          role {
            roleName
            id
            displayName
          }
          zip
          associatePatient {
            id
            firstName
            lastName
            displayName
            dateOfBirth
            zip
            status
          }
          associatePatients {
            id
            firstName
            lastName
            displayName
            dateOfBirth
            identityValue
            pName
            code
          }
          associateCaregivers {
            id
            firstName
            lastName
            displayName
            dateOfBirth
            identityValue
            pName
            code
            userName
            countryCode
            mobile
            registrationType
            source
            tenantId
            activeSince
            lastLogin
            status
            createdAt
            tenantName
          }
          mobile
          countryCode
          country
          state
          city
          address
          allAddresses {
            addrId
            addressType
            city
            state
            country
            address
            zipCode
            userstate {
              stateName
              postalCode
              stateId
            }
            usercountry {
              countryId
              countryName
              iso3
            }
          }
          patientIdentity {
            externalSystemId
            patientId
            IdentityValue
            guId
          }
          naTags
          naTagNames
          userEMverification {
            mobileVerified
            emailVerified
          }
          alternateContacts {
            id
            firstName
            lastName
            status
            username
            relation
            ESIValue
            mobile
            countryCode
            isVirtual
            createdAt
            modifiedAt
            activeSince
          }
          patientCustomFields {
            id
            patientId
            fieldName
            fieldValue
            fieldType
            isRequired
          }
        }
      }
    }
  `,
  patientExternalAddress: gql`
    query getSessionTenant(
      $sessionToken: String!
      $patientId: Int
      $tenantId: Int
      $startRow: Int
      $endRow: Int
      $sorting: [SortModel]
      $filter: [FilterModel]
      $admissionId: String
    ) {
      getSessionTenant(sessionToken: $sessionToken) {
        patientExternalAddress(
          patientId: $patientId
          tenantId: $tenantId
          startRow: $startRow
          endRow: $endRow
          sorting: $sorting
          filter: $filter
          admissionId: $admissionId
        ) {
          data {
            id
            patientId
            category
            line1
            line2
            city
            district
            state
            country
            zipCode
            type
          }
          meta {
            firstItem
            lastItem
            hasMorePages
            count
            total
            perPage
          }
        }
      }
    }
  `,
  getTenantConfigData: gql`
    query getSessionTenant($sessionToken: String!) {
      getSessionTenant(sessionToken: $sessionToken) {
        getTenantConfigData(configs: [{ configKey: "message_routing_type" }]) {
          configs {
            key
            value
          }
        }
      }
    }
  `,
  staffUsers: gql`
    query staffUsers($id: Int) {
      staffUsers(id: $id) {
        ssoId
        emails {
          value
          type
          primary
        }
        userTags {
          id
          tagName
        }
        enableCrossSite
        dateOfBirth
        displayName
        lastName
        firstName
        languages
        status
        id
        activeSince
        comments {
          comment
        }
        crossSites {
          siteId
        }
        dualRoles {
          id
          tenantUsersRoleId
          roleId
          roleName
          isPrimary
        }
        role {
          roleName
          id
          displayName
          privileges
        }
        mobile
        countryCode
        countryIsoCode
        tenantId
        naTags
        naTagNames
        companyNursingAgency
        userEMverification {
          mobileVerified
          emailVerified
        }
        userCategoryId
        userJobType
        originalStatus
        siteId
        alternateName
        registrationType
        guId
        homeSiteId
      }
    }
  `,
  folders: gql`
    query folders($sessionId: String, $searchText: String, $orderBy: OrderData, $orderData: String, $limit: Int, $offset: Int) {
      folders(sessionId: $sessionId, params: { searchText: $searchText, orderBy: $orderBy, orderData: $orderData, limit: $limit, offset: $offset }) {
        status
        totalCount
        data {
          id
          folderName
          folderDesc
          createdAt
          __typename
        }
        __typename
      }
    }
  `,
  documents: (retrieveUnLinked = false) => gql`
    query documents(
      $sessionId: String
      ${retrieveUnLinked ? '$retrieveUnLinked: Boolean' : ''}
      $searchText: String
      $orderBy: OrderData
      $orderData: String
      $limit: Int
      $page: Int
      $siteIds: String
      $tagId: String
    ) {
      documents(
        sessionId: $sessionId
        ${retrieveUnLinked ? 'retrieveUnLinked: $retrieveUnLinked' : ''}
        siteIds: $siteIds
        tagId: $tagId
        params: { searchText: $searchText, orderBy: $orderBy, orderData: $orderData, limit: $limit, page: $page }
      ) {
        totalCount
        data {
          id
          fileType
          fileSize
          createdOn
          documentPagesCount
          documentName
          documentOriginalName
          documentCategoryName
          documentCategoryGuid
          enableFile
          documentUniqueName
          documentTypes {
            id
            name
            __typename
          }
          source
          sites {
            id
            name
            __typename
          }
          __typename
        }
        __typename
      }
    }
  `,
  getTenantFilingCenterFolders: gql`
    query getTenantFilingCenterFolders($sessionId: String!, $type: SyncFolderType!, $fromCrud: Boolean) {
      getTenantFilingCenterFolders(sessionId: $sessionId, type: $type, fromCrud: $fromCrud) {
        id
        folderName
        type
        folderPath
      }
    }
  `,
  signatureRequestTypesDisplay: gql`
    query signatureRequestTypesDisplay($sessionId: String!, $nursingAgencyTagIds: String) {
      signatureRequestTypesDisplay(sessionId: $sessionId, device: "desktop", nursingAgencyTagIds: $nursingAgencyTagIds) {
        id
        name
        allowDesignView
        tagTypeId
        maxSignatoriesAllowed
        allowArchiveSignature
        nursingAgency
        toFilingCenter
        tagGuid
        fromFilingCenter
        documentpageSize
        allowPersonalSignature
        allowPendingApproveSignature
        allowFileNameEditing
        fileNameFormatText
        allowRecipientRoles
        allowAssociateRoles
        associateUserSignatureByRoles
        obtainSignature
        fromCrud
        signatureByRoles
        roleRecipientNoficationMessageTmpl
        userRecipientNoficationMessageTmpl
        notifyOnSubmitSignatureRoles
        notifyOnSubmitSignatureUsers
        allowAssociatePatient
        patientAssociateRole
        sendDocumentWithoutSignature
        checkBoxpaletteAllowed
        multipleCheckBoxAllowed
        checkBoxAreMandatory
        checkBoxGroupAllowed
        checkBoxGroupAreMandatory
        checkboxImageBehavior
        signaturepaletteAllowed
        signatureAreMandatory
        textBoxpaletteAllowed
        textBoxAreMandatory
        textFieldMoreProperty
        dateWithSign {
          sender
          recipient
        }
        signatureImageInfo {
          orginalWIdth
          orginalHeight
        }
        allowAssociatePatientAutomatically
        filingCenterFilenameFormt
        signaturePalette {
          textFieldActAs
          textPlaceholder
          checkboxLabel
          mandatory
          checkBoxPaletteLabel
          associate
          fieldForUser
          pendingApproveUser
          signature
          signatureDocumentPageNumber
          signaturePaletteWidth
          signaturePaletteHeight
          horizontalSpaceLeft
          verticalSpaceTop
          paletteType
          group
          groupName
          groupColor
          fieldorder
          samegroupOder
        }
        enableApplessWorkflow
        applessDevices
        patientDirectLink
        signedDocumentIntegration
        sendCompletedDocument
      }
    }
  `,
  documentTagsPaginated: gql`
    query getSessionTenant(
      $sessionToken: String!
      $excludeCategoryAssociatedType: Boolean
      $searchText: String
      $orderBy: OrderData
      $orderData: String
      $limit: Int
      $page: Int
    ) {
      getSessionTenant(sessionToken: $sessionToken) {
        documentTags(
          excludeCategoryAssociatedType: $excludeCategoryAssociatedType
          params: { searchText: $searchText, orderBy: $orderBy, orderData: $orderData, limit: $limit, page: $page }
        ) {
          totalCount
          data {
            id
            tagGuid
            tagName
          }
          __typename
        }
        __typename
      }
    }
  `,
  signatureRequestDocument: (isMaEnabled: boolean) => gql`
  query signatureRequest($sessionId: String!, $id: Int!) {
    signatureRequest(sessionId: $sessionId, id: $id) {
      id
      ownerId
      ${isMaEnabled ? 'admissionId' : ''}
      patientName
      prepopulationData
      caregiverAssociatePatient
      crossTenant
      senderTenant
      confirmSaveSignToProfile
      patientDetails {
        userId
        displayName
        firstName
        lastName
      }
      signatureByUsers {
        userId
        displayName
        firstName
        lastName
        userType
      }
      associateSignatureByUsers {
        userId
        displayName
      }
      isRead
      associateSignatureProcess
      downloadUrl
      displayText {
        text
        document
      }
      signatureStatus
      createdOn
      type {
        id
        fromFilingCenter
        patientDirectLink
        allowPendingApproveSignature
        patientInformationExchange
        name
        maxSignatoriesAllowed
        sendDocumentWithoutSignature
        allowPersonalSignature
        allowFileNameEditing
        fileNameFormatText
        fileSavingFormatText
        allowRecipientRoles
        obtainSignature
        signatureByRoles
        checkboxImageBehavior
        roleRecipientNoficationMessageTmpl
        userRecipientNoficationMessageTmpl
        allowAssociateRoles
        dateWithSign {
          sender
          recipient
          associate
          patient
        }
        checkBoxGroupAllowed
        checkBoxGroupAreMandatory
        documentpageSize
        enableApplessWorkflow
        applessDevices
        editablePrePopulatedFields
        textFieldMoreProperty
      }
      document {
        displayText
        pageCount
        associatePatient
        associatePatientFirstName
        associatePatientLastName
        fileshareId
        signaturePalette {
          id
          paletteType
          checkboxLabel
          textPlaceholder
          pendingApproveUser
          signatureDocumentPageNumber
          signaturePaletteHeight
          signaturePaletteWidth
          horizontalSpaceLeft
          verticalSpaceTop
          signaturePaletteLock
          fieldForUser
          fieldorder
          textFieldActAs
          associateUser
          checkBoxPaletteLabel
          mandatory
          signature {
            signatureId
            signatureImage
          }
          group
          groupColor
          groupName
          savedUserSignature
        }
      }
    }
  }
  `,
  documentTagTypes: gql`
    query getSessionTenant($sessionToken: String!) {
      getSessionTenant(sessionToken: $sessionToken) {
        documentTagTypes {
          id
          typeCode
          typeName
          tagMeta
          status
        }
      }
    }
  `
};
