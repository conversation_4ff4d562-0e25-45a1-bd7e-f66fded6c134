import gql from 'graphql-tag';

export const Mutations = {
  mutGetPdf: gql`
    mutation chatSignatureApproveTaggedMessageDownload(
      $sessionToken: String!
      $tenantId: Int
      $tenantName: String
      $messageId: Int
      $timeZone: String
      $msgrpIds: String
    ) {
      chatSignatureApproveTaggedMessageDownload(
        sessionId: $sessionToken
        timeZone: $timeZone
        params: { tenantId: $tenantId, tenantName: $tenantName, messageId: $messageId, flag: 1, msgrpIds: $msgrpIds }
      ) {
        signatureImage
      }
    }
  `,
  mutApproveMsg: gql`
    mutation chatSignatureApproveTaggedMessage(
      $sessionToken: String!
      $moveFilingCenter: Boolean
      $tenantId: Int
      $tenantName: String
      $filename: String
      $folder: String
      $folderType: String
      $messageId: Int
      $userID: Int
      $sign: String!
      $timeZone: String
      $msgrpIds: String
    ) {
      chatSignatureApproveTaggedMessage(
        sessionId: $sessionToken
        timeZone: $timeZone
        moveFilingCenter: $moveFilingCenter
        params: {
          messageId: $messageId
          flag: 1
          userId: $userID
          sign: $sign
          tenantId: $tenantId
          tenantName: $tenantName
          filename: $filename
          folder: $folder
          folderType: $folderType
          msgrpIds: $msgrpIds
        }
      ) {
        signatureImage
        identityValuePatient
        patientName
        enableIntegration
        integrationFC
        enableIntegration
        approvalRequired
        staffName
        CPRUSERNO
        CPRUSERNAME
        triggerOn
      }
    }
  `,
  mutDeleteTagForm: gql`
    mutation chatSignatureApproveTaggedMessageArchive($sessionId: String!, $id: Int, $messageId: String, $tagStatus: String) {
      chatSignatureApproveTaggedMessageArchive(sessionId: $sessionId, params: { id: $id, messageId: $messageId, tagStatus: $tagStatus, type: 0 }) {
        signatureImage
      }
    }
  `,
  updateAccount: gql`
    mutation updateTenant(
      $sessionToken: String!
      $name: String!
      $escalationTime: String
      $noClinicianMessage: String
      $noClinicianMessageDuringWorkingHours: String
      $defaultUser: [TenantStaffUserInput]
      $messageEscalationBehavior: String
    ) {
      updateTenant(
        sessionToken: $sessionToken
        params: {
          name: $name
          escalationTimeInSec: $escalationTime
          noClinicianMessage: $noClinicianMessage
          defaultUserToReceiveEscalatedMessage: $defaultUser
          noClinicianMessageDuringWorkingHours: $noClinicianMessageDuringWorkingHours
          messageEscalationBehavior: $messageEscalationBehavior
        }
      ) {
        id
      }
    }
  `,

  deleteMessageGroup: gql`
    mutation deleteMessageGroup($sessionToken: String!, $id: Int!) {
      deleteMessageGroup(sessionToken: $sessionToken, id: $id) {
        id
      }
    }
  `,
  deleteMessageGroups: gql`
    mutation deleteMessageGroups($sessionToken: String!, $id: [Int!]) {
      deleteMessageGroups(sessionToken: $sessionToken, id: $id) {
        id
      }
    }
  `,
  deleteEducationMaterials: gql`
    mutation deleteEducationMaterials($sessionToken: String!, $id: [Int!]) {
      deleteEducationMaterials(sessionToken: $sessionToken, id: $id) {
        id
      }
    }
  `,
  updateMessageEscalation: gql`
    mutation updateMessageEscalation(
      $id: Int!
      $escalationTimeInMin: Int!
      $messageEscalationBehavior: String!
      $defaultUserReceivesEscalatedMessages: Int
    ) {
      updateMessageEscalation(
        id: $id
        params: {
          escalationTimeInMin: $escalationTimeInMin
          messageEscalationBehavior: $messageEscalationBehavior
          defaultUserReceivesEscalatedMessages: $defaultUserReceivesEscalatedMessages
        }
      ) {
        id
      }
    }
  `,

  updateInventoryReportStatus: gql`
    mutation updateInventoryReportStatus($sessionToken: String!, $status: ReadStatus!, $messageId: Int!) {
      updateInventoryReportStatus(sessionToken: $sessionToken, params: { status: $status, messageId: $messageId }) {
        messageId
        status
      }
    }
  `,
  createAccount: gql`
    mutation createAccount(
      $name: String!
      $key: String!
      $officeEmail: String
      $helplinePhone: String
      $contactEmail: String
      $registrationId: String
    ) {
      createAccount(
        params: {
          name: $name
          key: $key
          officeEmail: $officeEmail
          helplinePhone: $helplinePhone
          contactEmail: $contactEmail
          registrationId: $registrationId
        }
      ) {
        id
      }
    }
  `,

  addPatientToExternalIntegrationSystem: gql`
    mutation addPatientToExternalIntegrationSystem(
      $sessionToken: String!
      $externalSystemId: Int
      $patientId: Int
      $IdentityValue: String
      $messageGroup: String
      $userType: String
    ) {
      addPatientToExternalIntegrationSystem(
        sessionToken: $sessionToken
        params: {
          externalSystemId: $externalSystemId
          patientId: $patientId
          IdentityValue: $IdentityValue
          messageGroup: $messageGroup
          userType: $userType
        }
      ) {
        id
        result
      }
    }
  `,

  addPatientToExternalIntegrationSystemPatient: gql`
    mutation addPatientToExternalIntegrationSystemPatient(
      $sessionToken: String!
      $externalSystemId: Int
      $patientId: Int
      $IdentityValue: String
      $messageGroupId: String
    ) {
      addPatientToExternalIntegrationSystemPatient(
        sessionToken: $sessionToken
        params: { externalSystemId: $externalSystemId, patientId: $patientId, IdentityValue: $IdentityValue, messageGroupId: $messageGroupId }
      ) {
        id
        result
      }
    }
  `,

  createExternalIntegrationSystem: gql`
    mutation createExternalIntegrationSystem(
      $sessionToken: String!
      $name: String
      $code: String
      $siteId: Int
      $uniqueIdentifier: String
      $filenameExpression: String
      $showEditor: Int
      $showInInvite: Int
      $requiredInInvite: Int
      $labelShowInEditPage: String
      $tenantAssoc: Int
      $extSysValue: String
      $esiValues: [ESIValues]
      $userType: String
    ) {
      createExternalIntegrationSystem(
        sessionToken: $sessionToken
        params: {
          name: $name
          code: $code
          uniqueIdentifier: $uniqueIdentifier
          filenameExpression: $filenameExpression
          showEditor: $showEditor
          showInInvite: $showInInvite
          requiredInInvite: $requiredInInvite
          labelShowInEditPage: $labelShowInEditPage
          tenantAssoc: $tenantAssoc
          extSysValue: $extSysValue
          esiValues: $esiValues
          userType: $userType
          siteId: $siteId
        }
      ) {
        name
      }
    }
  `,

  updatePatientToExternalIntegrationSystem: gql`
    mutation updatePatientToExternalIntegrationSystem(
      $sessionToken: String!
      $id: Int!
      $externalSystemId: Int
      $IdentityValue: String
      $tenantId: String
      $deleted: Int
      $messageGroup: String
      $patientId: Int
      $userType: String
    ) {
      updatePatientToExternalIntegrationSystem(
        sessionToken: $sessionToken
        params: {
          externalSystemId: $externalSystemId
          IdentityValue: $IdentityValue
          deleted: $deleted
          tenantId: $tenantId
          patientId: $patientId
          messageGroup: $messageGroup
          userType: $userType
        }
        id: $id
      ) {
        id
        result
      }
    }
  `,

  updateExternalIntegrationSystem: gql`
    mutation updateExternalIntegrationSystem(
      $sessionToken: String!
      $id: Int!
      $name: String
      $code: String
      $siteId: Int
      $uniqueIdentifier: String
      $filenameExpression: String
      $deleted: Int
      $showEditor: Int
      $showInInvite: Int
      $requiredInInvite: Int
      $labelShowInEditPage: String
      $tenantAssoc: Int
      $extSysValue: String
      $esiValues: [ESIValues]
      $userType: String
    ) {
      updateExternalIntegrationSystem(
        sessionToken: $sessionToken
        params: {
          name: $name
          code: $code
          uniqueIdentifier: $uniqueIdentifier
          filenameExpression: $filenameExpression
          deleted: $deleted
          showEditor: $showEditor
          showInInvite: $showInInvite
          requiredInInvite: $requiredInInvite
          labelShowInEditPage: $labelShowInEditPage
          tenantAssoc: $tenantAssoc
          extSysValue: $extSysValue
          esiValues: $esiValues
          userType: $userType
          siteId: $siteId
        }
        id: $id
      ) {
        name
      }
    }
  `,

  updateMessageTagType: gql`
    mutation updateMessageTagType(
      $sessionToken: String!
      $id: Int!
      $typeName: String
      $typeDescription: String
      $userType: String
      $fontColor: String
      $bgColor: String
      $deleted: Int
    ) {
      updateMessageTagType(
        sessionToken: $sessionToken
        params: {
          typeName: $typeName
          typeDescription: $typeDescription
          userType: $userType
          fontColor: $fontColor
          bgColor: $bgColor
          deleted: $deleted
        }
        id: $id
      ) {
        typeName
      }
    }
  `,
  updateUserTagType: gql`
    mutation updateUserTagType(
      $sessionToken: String!
      $id: Int!
      $typeName: String
      $typeDescription: String
      $fontColor: String
      $bgColor: String
      $deleted: Int
      $userType: String
      $tagTypeCategory: String
      $isNursingAgencyTagType: Int
    ) {
      updateUserTagType(
        sessionToken: $sessionToken
        params: {
          typeName: $typeName
          typeDescription: $typeDescription
          fontColor: $fontColor
          bgColor: $bgColor
          deleted: $deleted
          userType: $userType
          tagTypeCategory: $tagTypeCategory
          isNursingAgencyTagType: $isNursingAgencyTagType
        }
        id: $id
      ) {
        typeName
      }
    }
  `,

  deleteInventoryRequestTemplate: gql`
    mutation deleteInventoryRequestTemplate($sessionToken: String!, $id: Int!) {
      deleteInventoryRequestTemplate(sessionToken: $sessionToken, id: $id) {
        id
      }
    }
  `,
  deleteInventoryItemsGroupTemplate: gql`
    mutation deleteInventoryItemsGroupTemplate($sessionToken: String!, $id: Int!) {
      deleteInventoryItemsGroupTemplate(sessionToken: $sessionToken, id: $id) {
        id
      }
    }
  `,
  deleteInventoryItemTemplate: gql`
    mutation deleteInventoryItemTemplate($sessionToken: String!, $id: Int!) {
      deleteInventoryItemTemplate(sessionToken: $sessionToken, id: $id) {
        id
      }
    }
  `,

  deleteTag: gql`
    mutation deleteTag($sessionToken: String!, $id: Int!, $tagType: Int!) {
      deleteTag(sessionToken: $sessionToken, id: $id, tagType: $tagType) {
        id
        __typename
      }
    }
  `,
  updateUserTag: gql`
    mutation updateUserTag($sessionToken: String!, $id: Int!, $tagName: String!, $type: TagType!) {
      updateUserTag(sessionToken: $sessionToken, id: $id, params: { tagName: $tagName, type: $type }) {
        id
      }
    }
  `,
  updateMessageTag: gql`
    mutation updateMessageTag($sessionToken: String!, $id: Int!, $tagName: String!, $type: TagType!) {
      updateMessageTag(sessionToken: $sessionToken, id: $id, params: { tagName: $tagName, type: $type }) {
        id
      }
    }
  `,
  updateDocumentTag: gql`
    mutation updateDocumentTag($sessionToken: String!, $id: Int!, $tagName: String!, $type: TagType!) {
      updateDocumentTag(sessionToken: $sessionToken, id: $id, params: { tagName: $tagName, type: $type }) {
        id
      }
    }
  `,
  createMessageTag: gql`
    mutation createMessageTag($sessionToken: String!, $tagName: String!, $type: TagType!) {
      createMessageTag(sessionToken: $sessionToken, params: { tagName: $tagName, type: $type }) {
        id
      }
    }
  `,
  createUserTag: gql`
    mutation createUserTag($sessionToken: String!, $tagName: String!, $type: TagType!) {
      createUserTag(sessionToken: $sessionToken, params: { tagName: $tagName, type: $type }) {
        id
      }
    }
  `,
  createDocumentTag: gql`
    mutation createDocumentTag($sessionToken: String!, $tagName: String!, $type: TagType!) {
      createDocumentTag(sessionToken: $sessionToken, params: { tagName: $tagName, type: $type }) {
        id
      }
    }
  `,
  createInventoryItemsGroupTemplate: gql`
    mutation createInventoryItemsGroupTemplate(
      $sessionToken: String!
      $name: String!
      $inventoryRequestTemplate: Int
      $manualEntry: Boolean
      $order: Int
      $isDeleted: Boolean
    ) {
      createInventoryItemsGroupTemplate(
        sessionToken: $sessionToken
        params: { name: $name, inventoryRequestTemplate: $inventoryRequestTemplate, manualEntry: $manualEntry, order: $order, isDeleted: $isDeleted }
      ) {
        id
      }
    }
  `,

  updateInventoryItemsGroupTemplate: gql`
    mutation updateInventoryItemsGroupTemplate(
      $sessionToken: String!
      $id: Int!
      $name: String!
      $inventoryRequestTemplate: Int
      $manualEntry: Boolean
      $order: Int
      $isDeleted: Boolean
    ) {
      updateInventoryItemsGroupTemplate(
        sessionToken: $sessionToken
        id: $id
        params: { name: $name, inventoryRequestTemplate: $inventoryRequestTemplate, manualEntry: $manualEntry, order: $order, isDeleted: $isDeleted }
      ) {
        id
      }
    }
  `,
  createInventoryItemTemplate: gql`
    mutation createInventoryItemTemplate($sessionToken: String!, $name: String!, $inventorySubCategory: Int, $enabled: Boolean, $isDeleted: Boolean) {
      createInventoryItemTemplate(
        sessionToken: $sessionToken
        params: { name: $name, inventorySubCategory: $inventorySubCategory, enabled: $enabled, isDeleted: $isDeleted }
      ) {
        id
      }
    }
  `,
  updateInventoryItemTemplate: gql`
    mutation updateInventoryItemTemplate(
      $sessionToken: String!
      $id: Int!
      $name: String!
      $inventorySubCategory: Int
      $enabled: Boolean
      $isDeleted: Boolean
    ) {
      updateInventoryItemTemplate(
        sessionToken: $sessionToken
        id: $id
        params: { name: $name, inventorySubCategory: $inventorySubCategory, enabled: $enabled, isDeleted: $isDeleted }
      ) {
        id
      }
    }
  `,
  mutRenameFilingCenterContent: gql`
    mutation renameFilingCenterContent(
      $sessionId: String!
      $folderName: String!
      $oldName: String!
      $newName: String!
      $type: String!
      $ext: String
    ) {
      renameFilingCenterContent(
        sessionId: $sessionId
        params: { folder: { folderName: $folderName, type: $type }, oldName: $oldName, newName: $newName, ext: $ext }
      ) {
        folder
        name
      }
    }
  `,

  mutRenameSiteFilingCenterContent: gql`
    mutation renameSiteFilingCenterContent(
      $sessionId: String!
      $folderName: String!
      $oldName: String!
      $newName: String!
      $type: String!
      $guid: String
      $ext: String
    ) {
      renameSiteFilingCenterContent(
        sessionId: $sessionId
        params: { folder: { folderName: $folderName, type: $type }, oldName: $oldName, newName: $newName, guid: $guid, ext: $ext }
      ) {
        folder
        name
        addedOn
      }
    }
  `,

  qryDocumnetSignaturePaletteTypes: gql`
    mutation signatureDocumentTypeTemplate(
      $sessionId: String!
      $documentId: Int!
      $signaturePalette: [SignaturePaletteInput]
      $signatureImageInfo: signatureRequestImageInfoInput
    ) {
      signatureDocumentTypeTemplate(
        sessionId: $sessionId
        params: { documentId: $documentId, signaturePalette: $signaturePalette, signatureImageInfo: $signatureImageInfo }
      ) {
        id
      }
    }
  `,

  mutCompleteSignatureRequest: gql`
    mutation completeDocumentSignatureRequest($sessionId: String!, $documentId: Int!, $signaturePalette: [SignaturePaletteInput]) {
      completeDocumentSignatureRequest(
        sessionId: $sessionId
        params: { document: { documentId: $documentId, signaturePalette: $signaturePalette } }
      ) {
        id
        createdOn
        owner
        ownerId
        isRead
        archivedUsers
        accountLevelArchived
        associateSignatureProcess
        notifyOnSubmitSignatureRoles
        notifyOnSubmitSignatureUsers
        document {
          displayText
          pageCount
          associatePatient
          associateUser
        }
        ownerId
        owner
        signatureByUsers {
          userId
          displayName
        }
        associateSignatureByUsers {
          userId
          displayName
        }
        displayText {
          text
          document
        }
        type {
          id
          name
          obtainSignature
          allowRecipientRoles
          allowPendingApproveSignature
        }
        signatureStatus
      }
    }
  `,
  mutArchiveSignatureDocumentLocal: gql`
    mutation archiveSignatureDocument($sessionId: String!, $id: Int, $action: String) {
      archiveSignatureDocument(sessionId: $sessionId, params: { id: $id, level: 1, action: $action }) {
        id
        archivedUsers
        accountLevelArchived
        archivedOn
      }
    }
  `,
  mutCancelSignatureDocumentLocal: gql`
    mutation cancelSignatureDocument($sessionId: String!, $id: Int, $action: String) {
      cancelSignatureDocument(sessionId: $sessionId, params: { id: $id, level: 1, action: $action }) {
        id
        cancelledUsers
        cancelledOn
      }
    }
  `,
  cancelSignatureDocumentTenant: gql`
    mutation cancelSignatureDocument($sessionId: String!, $id: Int, $action: String, $crossTenantId: Int!) {
      cancelSignatureDocument(sessionId: $sessionId, crossTenantId: $crossTenantId, params: { id: $id, level: 1, action: $action }) {
        id
        cancelledUsers
        cancelledOn
      }
    }
  `,

  archiveOrRestoreInventory: gql`
    mutation updateSupplyChainItem($sessionToken: String!, $id: Int, $deleted: Int, $deletedBy: Int, $restoredBy: Int) {
      updateSupplyChainItem(sessionToken: $sessionToken, params: { id: $id, deleted: $deleted, deletedBy: $deletedBy, restoredBy: $restoredBy }) {
        id
      }
    }
  `,
  moveFilingCenterToArchive: gql`
    mutation moveFilingCenterToArchive(
      $sessionId: String!
      $documentId: Int!
      $folderType: String!
      $folderName: String!
      $filename: String!
      $enable_multisite: String
      $guid: String
    ) {
      moveFilingCenterToArchive(
        sessionId: $sessionId
        params: {
          folder: { type: $folderType, folderName: $folderName }
          name: $filename
          documentId: $documentId
          enableMultisite: $enable_multisite
          guid: $guid
        }
      ) {
        folder
        name
      }
    }
  `,
  mutgenerateIntroJS: gql`
    mutation generateIntroJS($tenantKey: String, $fileName: String) {
      generateIntroJS(tenantKey: $tenantKey, fileName: $fileName) {
        status
        jsonData
      }
    }
  `,
  mutsendToEhr: gql`
    mutation sendToEhrDocument($sessionId: String!, $documentId: Int, $crossTenantId: Int, $multiSite: Int, $enableFax: Boolean) {
      sendToEhrDocument(
        params: { sessionId: $sessionId, documentId: $documentId, crossTenantId: $crossTenantId, multiSite: $multiSite, enableFax: $enableFax }
      ) {
        status
        message
      }
    }
  `,
  mutSignDocument: gql`
    mutation signDocument(
      $sessionId: String!
      $contentID: Int!
      $documentOwner: Int!
      $documentName: String!
      $signatureData: [signatureDataInput]
      $checkboxImageBehavior: Int!
      $completeSignatureProcess: Boolean
      $approveSignatureProcess: Boolean
      $pageCount: Int
      $associateSignatureProcess: Boolean
      $documentpageSize: String!
      $signatureCrossTenant: Int!
    ) {
      signDocument(
        sessionId: $sessionId
        params: {
          contentID: $contentID
          pageCount: $pageCount
          associateSignatureProcess: $associateSignatureProcess
          checkboxImageBehavior: $checkboxImageBehavior
          completeSignatureProcess: $completeSignatureProcess
          approveSignatureProcess: $approveSignatureProcess
          documentName: $documentName
          documentOwner: $documentOwner
          signatureData: $signatureData
          documentpageSize: $documentpageSize
          signatureCrossTenant: $signatureCrossTenant
        }
      ) {
        id
        createdOn
        associatedP
        associatedU
        signedOn
        owner
        ownerId
        archivedUsers
        accountLevelArchived
        associateSignatureProcess
        signatureByUsers {
          userId
          displayName
        }
        associateSignatureByUsers {
          userId
          displayName
        }
        isRead
        notifyOnSubmitSignatureRoles
        notifyOnSubmitSignatureUsers
        document {
          displayText
          pageCount
          associatePatient
        }
        displayText {
          text
          document
        }
        type {
          id
          name
          allowPendingApproveSignature
          obtainSignature
          allowRecipientRoles
          allowAssociateRoles
          documentpageSize
        }
        signatureStatus
        exchangeData
      }
    }
  `,

  sendScheduledForms: gql`
    mutation sendScheduleForms(
      $recipients: [User]
      $formName: String!
      $formId: Int!
      $message: String!
      $sessionId: String!
      $scheduleName: String!
      $time: String!
      $timeZone: String!
      $scheduleDate: String!
      $scheduleRepeat: Boolean!
      $repeatOption: Int!
      $scheduleAdvanced: Boolean!
      $endDate: String!
    ) {
      sendScheduleForms(
        params: {
          recipients: $recipients
          formName: $formName
          formId: $formId
          message: $message
          scheduleName: $scheduleName
          time: $time
          timeZone: $timeZone
          scheduleDate: $scheduleDate
          scheduleRepeat: $scheduleRepeat
          scheduleAdvanced: $scheduleAdvanced
          repeatOption: $repeatOption
          endDate: $endDate
        }
        sessionId: $sessionId
      ) {
        id
        scheduleName
        formName
        message
        recipients {
          id
          name
          email
          siteId
        }
        userTags {
          id
          tagName
        }
        scheduledOn
        scheduleDate
        endDate
        scheduleRepeat
        nextScheduleDate
        createdUser
        createdUserName
        createdOn
        modifiedOn
        modifiedOn
        status
      }
    }
  `,
  updateScheduledForms: gql`
    mutation updateScheduledForms(
      $scheduleId: Int!
      $recipients: [User]
      $formName: String!
      $formId: Int!
      $message: String!
      $sessionId: String!
      $scheduleName: String!
      $time: String!
      $timeZone: String!
      $scheduleDate: String!
      $scheduleRepeat: Boolean!
      $repeatOption: Int!
      $scheduleAdvanced: Boolean!
      $endDate: String!
    ) {
      updateScheduledForms(
        scheduleId: $scheduleId
        params: {
          recipients: $recipients
          formName: $formName
          formId: $formId
          message: $message
          scheduleName: $scheduleName
          time: $time
          timeZone: $timeZone
          scheduleDate: $scheduleDate
          scheduleRepeat: $scheduleRepeat
          scheduleAdvanced: $scheduleAdvanced
          repeatOption: $repeatOption
          endDate: $endDate
        }
        sessionId: $sessionId
      ) {
        id
        scheduleName
        formName
        message
        recipients {
          id
          name
          email
          siteId
        }
        userTags {
          id
          tagName
        }
        scheduledOn
        scheduleDate
        endDate
        scheduleRepeat
        nextScheduleDate
        createdUser
        createdUserName
        createdOn
        modifiedOn
        modifiedOn
        status
      }
    }
  `,
  deleteScheduledFormMutation: gql`
    mutation deleteScheduledForm($sessionId: String!, $scheduledId: Int!) {
      deleteScheduledForm(sessionId: $sessionId, scheduledId: $scheduledId) {
        id
      }
    }
  `,
  stopScheduledFormMutation: gql`
    mutation stopOrStartScheduledForm($sessionId: String!, $scheduledId: Int!, $status: Int!, $timeZone: String!) {
      stopOrStartScheduledForm(sessionId: $sessionId, scheduledId: $scheduledId, status: $status, params: { timeZone: $timeZone }) {
        id
      }
    }
  `,

  deleteAlertMessage: gql`
    mutation deleteAlertMessage($sessionToken: String!, $id: [Int!]) {
      deleteAlertMessage(sessionToken: $sessionToken, id: $id) {
        id
      }
    }
  `,

  publishAlertMessage: gql`
    mutation publishAlertMessage($sessionToken: String!, $id: [Int]!, $isPublish: Boolean!) {
      publishAlertMessage(sessionToken: $sessionToken, id: $id, isPublish: $isPublish) {
        id
      }
    }
  `,
  mergeDuplicatePatient: gql`
    mutation mergeDuplicatePatient($sessionToken: String!, $mergeData: String!, $deleteData: String!, $selectedMrn: String, $tenantId: Int) {
      mergeDuplicatePatient(
        sessionToken: $sessionToken
        mergeData: $mergeData
        deleteData: $deleteData
        selectedMrn: $selectedMrn
        tenantId: $tenantId
      ) {
        status
      }
    }
  `,

  createDocFolder: gql`
    mutation createFolder($sessionId: String!, $folderName: String!, $folderDesc: String) {
      createFolder(sessionId: $sessionId, params: { folderName: $folderName, folderDesc: $folderDesc }) {
        status
        resourceId
        statusMessage
        __typename
      }
    }
  `,

  updateDocFolder: gql`
    mutation renameFolder($sessionId: String!, $id: String!, $folderName: String!, $folderDesc: String) {
      renameFolder(sessionId: $sessionId, id: $id, params: { folderName: $folderName, folderDesc: $folderDesc }) {
        status
        resourceId
        statusMessage
        __typename
      }
    }
  `,

  deleteFolder: gql`
    mutation deleteFolder($sessionId: String!, $id: String!) {
      deleteFolder(sessionId: $sessionId, id: $id) {
        status
        resourceId
        statusMessage
        __typename
      }
    }
  `,
  createEducationMaterial: gql`
    mutation createEducationMaterial(
      $sessionToken: String!
      $materialName: String!
      $description: String
      $tenantId: Int
      $createdBy: Int
      $createdOn: String
      $updatedOn: String
      $fileName: String
      $uploadedFileName: String
      $nursingAgencyUserTags: String
      $shareUserData: [shareUserDataInput]
    ) {
      createEducationMaterial(
        sessionToken: $sessionToken
        materialName: $materialName
        description: $description
        tenantId: $tenantId
        createdBy: $createdBy
        createdOn: $createdOn
        updatedOn: $updatedOn
        fileName: $fileName
        uploadedFileName: $uploadedFileName
        nursingAgencyUserTags: $nursingAgencyUserTags
        shareUserData: $shareUserData
      )
    }
  `,
  updateEducationMaterial: gql`
    mutation updateEducationMaterial(
      $sessionToken: String!
      $materialId: String
      $materialName: String!
      $description: String
      $tenantId: Int
      $createdBy: Int
      $updatedBy: Int
      $createdOn: String
      $updatedOn: String
      $fileName: String
      $uploadedFileName: String
      $nursingAgencyUserTags: String
      $shareUserData: [shareUserDataInput]
    ) {
      updateEducationMaterial(
        sessionToken: $sessionToken
        materialId: $materialId
        materialName: $materialName
        description: $description
        tenantId: $tenantId
        createdBy: $createdBy
        updatedBy: $updatedBy
        createdOn: $createdOn
        updatedOn: $updatedOn
        fileName: $fileName
        uploadedFileName: $uploadedFileName
        nursingAgencyUserTags: $nursingAgencyUserTags
        shareUserData: $shareUserData
      )
    }
  `,
  updateVisitPatientReminderTime: gql`
    mutation UpdateTenant($sessionToken: String!, $reminderTime: Int) {
      updateTenant(sessionToken: $sessionToken, params: { visitPatientReminderTime: $reminderTime }) {
        id
        __typename
      }
    }
  `,
  resendDownloadDocumentApplessLink: gql`
    mutation resendDownloadDocumentApplessLink($sessionId: String!, $documentId: Int!, $recipients: String!) {
      resendDownloadDocumentApplessLink(sessionId: $sessionId, documentId: $documentId, recipients: $recipients) {
        success
        message
        recipients
        error
      }
    }
  `,
  mapVisitLocationRelation: gql`
    mutation mapVisitLocationRelation(
      $sessionToken: String!
      $params: [VisitLocationTypeInput]
      $typeAll: Boolean
      $typeManage: Boolean
      $roleId: Int
    ) {
      mapVisitLocationRelation(sessionToken: $sessionToken, params: $params, typeAll: $typeAll, typeManage: $typeManage, roleId: $roleId) {
        status
      }
    }
  `,
  deleteDocumentTemplate: gql`
    mutation deleteDocumentTemplate($sessionId: String!, $id: ID!) {
      deleteDocumentTemplate(sessionId: $sessionId, id: $id) {
        status
        resourceId
        statusMessage
      }
    }
  `,
  designDocumentTemplate: gql`
    mutation designDocumentTemplate($sessionId: String!, $metaData: String, $documentUniqueName: String, $id: ID!) {
      designDocumentTemplate(sessionId: $sessionId, params: { metaData: $metaData, documentUniqueName: $documentUniqueName, id: $id }) {
        status
        statusMessage
        resourceId
      }
    }
  `,
  updateDocumentTemplate: gql`
    mutation updateDocumentTemplate(
      $sessionId: String!
      $siteIds: String
      $folderId: Int!
      $documentName: String!
      $metaData: String
      $enableFile: Boolean!
      $fileName: String
      $fileSize: String
      $fileType: String
      $documentUniqueName: String
      $documentPagesCount: Int
      $id: ID!
    ) {
      updateDocumentTemplate(
        sessionId: $sessionId
        siteIds: $siteIds
        params: {
          folderId: $folderId
          documentName: $documentName
          metaData: $metaData
          enableFile: $enableFile
          fileName: $fileName
          fileSize: $fileSize
          fileType: $fileType
          documentUniqueName: $documentUniqueName
          documentPagesCount: $documentPagesCount
        }
        id: $id
      ) {
        status
        resourceId
        statusMessage
      }
    }
  `,
  createDocumentTagType: gql`
    mutation createDocumentTagType(
      $sessionToken: String!
      $typeName: String
      $typeCode: String
      $tagMeta: String
      $createdBy: Int
      $status: Int!
      $esiValue: String
      $extDocType: String
    ) {
      createDocumentTagType(
        sessionToken: $sessionToken
        params: {
          typeName: $typeName
          typeCode: $typeCode
          tagMeta: $tagMeta
          createdBy: $createdBy
          status: $status
          esiValue: $esiValue
          extDocType: $extDocType
        }
      ) {
        typeCode
      }
    }
  `,
  deleteDocumentTagType: gql`
    mutation updateDocumentTagType($sessionToken: String!, $status: Int, $id: Int!, $esiValue: String, $extDocType: String) {
      updateDocumentTagType(sessionToken: $sessionToken, params: { status: $status, esiValue: $esiValue, extDocType: $extDocType }, id: $id) {
        typeName
      }
    }
  `,
  updateDocumentTagType: gql`
    mutation updateDocumentTagType(
      $sessionToken: String!
      $typeName: String
      $tagMeta: String
      $createdBy: Int
      $id: Int!
      $status: Int!
      $esiValue: String
      $extDocType: String
    ) {
      updateDocumentTagType(
        sessionToken: $sessionToken
        params: { typeName: $typeName, tagMeta: $tagMeta, createdBy: $createdBy, status: $status, esiValue: $esiValue, extDocType: $extDocType }
        id: $id
      ) {
        typeName
      }
    }
  `
};
