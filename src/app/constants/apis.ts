/** This class should contain APIs only.
 * import this class and access the APIs where needed. E.g. APIs.saveUserApi
 */

export class APIs{
    static readonly saveUserApi  = 'create-user.php';
    static readonly checkPatientForCaregiver  = 'check-patient-for-caregiver.php';
    static readonly getIntegrationStatusAPI : string = 'nextgen-get-integration-status.php';
    static readonly sendNotificationEmail = 'citus-health/v5/send-notification-email.php';
    static alternateContactSave = 'citus-health/v4/alternate-contact-save.php';
    static readonly sendReminderApi = 'hook/web/drip/v2/notify';
    
    // Schedule center APIs
    static readonly updateVisitSchedule = 'update-visit-schedule.php';
    static readonly manageVisits = '/api/v1/visits';
    static readonly getPatientVisitDetails = 'get-patient-visit-details.php';
    static readonly saveResourceUnavailabilityDetails = 'save-resource-unavailability-details.php';
    static readonly getAvailabilityInCalender = 'get-availability.php';
    static readonly getVisitUserResourceData = 'get-visit-user-resource-data.php';
    static readonly getVisitAvailableUserResource = 'get-visit-available-user-resource-data.php';
    
    static readonly getVisitLocationTypeDetails = 'get-visit-location-type-details.php';
    static readonly getVisitTypeDetails = 'get-visit-type-details.php';
    static readonly saveVisitTypeDetails = 'save-visit-type-details.php';
    static readonly getTimezonesWithDst = 'get-timezones-with-dst.php';
    static readonly getVisitLocationDetails = 'get-visit-location-details.php';
    static readonly saveVisitLocations = 'save-visit-locations.php';
    static readonly getResourcesDetails = 'get-resource-details.php';
    static readonly saveResourcesDetails = 'save-resource-details.php';
    static readonly getAccessoryDetails = 'get-accessory-details.php';
    static readonly getResourcesAvailabilityDetails = 'get-resource-availability-details.php';
    static readonly saveResourcesAvailabilityDetails = 'save-resource-availability-details.php';
    static readonly getPayerDetails = 'get-payor-details.php';
    static readonly savePayerDetails = 'save-payor-details.php';
    static readonly getRouteAdministrationDetails = 'get-route-administration-details.php';
    static readonly saveRouteAdministrationDetails = 'save-route-administration-details.php';
    static readonly getVisitTasksDetails = 'get-visit-tasks-details.php';
    static readonly saveVisitTasksDetails = 'save-visit-tasks-details.php';
    static readonly getVisitAvailableResources = 'get-visit-available-resource.php';
    static readonly getVisitTrackHistory = 'get-visit-track-history.php';
    static readonly saveVisitScheduleByStaff = 'save-visit-schedule-by-staff.php';
    static readonly saveVisitAccept = 'save-visit-accept.php';
    static readonly getVisitDetails = 'get-visit-details.php';
    static readonly getVisitsById = 'get-visits-by-id.php';
    static readonly viewAvailabilityResource = 'view-availability-resource.php';
    static readonly viewAvailabilityStaff = 'view-availability-staff.php';
    static readonly getVisits = 'get-visits.php';
    static readonly visitScheduleActivity = 'visit-schedule-activity.php';
    static readonly saveVisitLocationDetails = 'save-visit-location-details.php';
    static readonly manageAvailability = '/api/v1/availability';
    static readonly getVisitUserResource = '/api/v1/userList';
    static readonly getVisitType = '/api/v1/visitTypes';
    static readonly checkAvailability  = '/api/v1/visits/check-availability';
    static readonly uploadVisitScheduleFiles = '/api/v1/attachment';
    static readonly userSettings = '/api/v1/userSettings';
    static readonly generateDraftPendingFormPdf = 'citus-health/v4/GeneratePendingDraftPdf.php';
    static readonly loaderImageUrl = 'webapp/www/img/gif-loader.gif';
    static readonly downloadFormDetails = 'citus-health/v4/form-download.php';
    static visitSummary ='/api/v1/visits/{visitKey}/summary';
    static clinicalVisitEntry ='/api/v1/visits/{visitKey}/clinicalVisitEntry';
    static readonly visitDescriptions = '/api/v1/descriptions';
    static readonly visitTherapies = '/api/v1/therapies';
    static readonly visitOrders = '/api/v1/orders';
    static readonly visitDepartments = '/api/v1/departments';
    static clinicalVisitLogIntegration = '/api/v1/visits/{visitKey}/clinicalVisitLogIntegration';
    static VisitEndpoint = '/api/v1/visits/{visitKey}';
    static readonly addUserToMessageGroup = 'citus-health/v4/add-user-to-message-group.php';
    static readonly getAlternateContactCaregiverPatient = 'citus-health/v4/get-alternate-contacts-caregiver-for-patient.php';
    static readonly sendCompletedFormsToRecipients = 'citus-health/v4/SendCompletedFormToRecipients.php';
    static readonly getTenantUsers = 'citus-health/v4/get-tenant-users-by-roleid.php';
    static readonly getAllMessageGroupTopics = 'citus-health/v4/get-all-message-group-topics.php';
    static readonly deleteChatroomMessage = 'citus-health/v4/delete-chatroom-messages.php';
    static readonly chatPinUnpin =  'citus-health/v4/handle_message_pin.php';
    static readonly generateNgwFormDataPdf = 'citus-health/v4/generate-ngw-form-data-pdf.php';
    static readonly generateFormDataPdf = 'citus-health/v4/generate-structured-form-data-pdf.php';
    static readonly cancelForm = 'citus-health/v4/cancel-form.php';
    static readonly moveFormToComplete =  'citus-health/v4/move-form-to-complete.php';
    static readonly getChatInboxMessages = 'citus-health/v5/message-inbox-list.php';
    static readonly getTenantAssociatedPatient = 'citus-health/v4/get-tenant-associated-patient.php';
    static readonly externalPatientIntegration = 'citus-health/v5/external-patient-integration.php';
    static readonly createChatroom = 'citus-health/v4/create-chatroom.php';
    static readonly getAllMessageGroup = 'citus-health/v5/getMessageGroups.php';
    static readonly getPatientGroups  = 'citus-health/v5/getPatientGroups.php';
    static readonly updatePatientDiscussionGroup = 'citus-health/v4/update-pdg.php';
    static readonly createMessageGroup = 'citus-health/v4/create-message-group.php';
    static readonly updateMessageGroup = 'citus-health/v4/update-message-group-details.php';
    static readonly getAllMessageGroupTopicsList = 'citus-health/v5/getGroupTopics.php';
    static readonly getMaskedReplyMessages = 'citus-health/v4/get-masked-reply-messages.php';
    static readonly removeChatroomRoleParticipant = 'citus-health/v5/remove-chatroom-role-participant.php';
    static readonly getRoomUsers = 'citus-health/v4/get-room-users.php';
    static readonly getTagDetails = 'citus-health/v4//get-tag-details.php';
    static readonly removeChatRoomParticipants = 'citus-health/v4/remove-chatroom-participants.php';
    static readonly getChatroomMessages = 'citus-health/v5/get-chatroom-messages.php';
    static readonly chatroomMessages = 'citus-health/v4/socket-message.php';
    static readonly checkMsgIntegrationStatusEndpoint = 'citus-health/v4/check-message-tag-integration-status.php';
    static readonly checkDocIntegrationStatusEndpoint = 'citus-health/v4/check-doc-type-integration-status.php';
    static readonly checkFormIntegrationStatusEndpoint = 'citus-health/v4/check-integration-status.php';
    static readonly getPatientMessageRouting = 'citus-health/v4/start-new-chat.php';
    static readonly manageChatParticipants = 'citus-health/v5/manageChatParticipants.php';
    static readonly machFormEmbed = 'embed.php';
    static readonly machFormIndex = 'index.php';
    static readonly machFormEditEntry = 'edit_entry.php';
    static readonly machFormFileDownload = 'download.php';
    static readonly updateAccessToken = 'citus-health/v4/manage-idm-token.php';
    static readonly generateChatReport = 'citus-health/v5/generate-chat-report.php';
    static readonly documentSelfServiceFileManagement = 'citus-health/v5/document-self-service-file-management.php';
  static readonly formWorkListAdvancedSearch = 'citus-health/v4/FormWorklistAdvancedSearch.php';

     //* JAVA APIS START //
     static readonly userLookupEndpoint = 'coreservice/api/unauthorized/users/lookup';
     static readonly sendOtpEndpoint = 'coreservice/api/unauthorized/users/send-otp';
     static readonly reSendOtpEndpoint = 'coreservice/api/unauthorized/users/resend-otp';
     static readonly otpEnabledEndpoint = 'coreservice/api/unauthorized/users/{username}/otp-2fa-enabled';

     static readonly userSearchEndpoint = 'coreservice/api/users/search';
     static readonly usersNotContactableEndpoint = 'coreservice/api/users/non-contactable/exists';
     static readonly activityTypesEndpoint = 'coreservice/api/activities/activity-types';
     static readonly activityTrailEndpoint = 'coreservice/api/activities/{activityId}/activity-trail';
     static readonly activityLogsEndpoint = 'coreservice/api/activities/search';
     static readonly downloadActivityLogsEndpoint = 'coreservice/api/activities/{outputType}/{language}';
     static readonly rolesAndPrivileges = 'coreservice/api/data/roles-privileges';
    static readonly oldToNewSeriesEndpoint = 'coreservice/api/visits/mapping/old-to-new-series';
    static readonly visitLocationByIdEndpoint = 'coreservice/api/visits/visit-locations/{id}';
    static readonly visitChairByIdEndpoint = 'coreservice/api/visits/visit-chairs/{id}';
    static readonly visitLocationSaveEndpoint = 'coreservice/api/visits/visit-locations';
    static readonly visitChairSaveEndpoint = 'coreservice/api/visits/visit-chairs';
    static readonly visitLocationsEndpoint = 'coreservice/api/visits/visit-locations/search';
    static readonly visitLocationChairsEndpoint = 'coreservice/api/visits/visit-locations/{visitLocationId}/visit-chairs/search';
    static readonly deliveryStatusFetchApi = 'coreservice/api/chatroom-message-delivery';
    static readonly deleteMessageChatroom = 'coreservice/api/chatroom/message-status';
    static readonly getAdmissionsEndPoint = 'coreservice/api/admissions/search';
    static readonly admissionByIdEndPoint = 'coreservice/api/admissions/{admissionId}';
    static readonly admissionStatusByIdEndPoint = 'coreservice/api/admissions/{admissionId}/{status}';
    static readonly getAdmissionsListEndPoint = 'coreservice/api/admissions/search/custom-fields';
    static readonly getAdmissionSitesEndPoint = 'coreservice/api/admissions/admission-sites';
  static readonly searchCriteriaEndpoint = 'coreservice/api/search-criteria';
  static readonly getSearchCriteriaEndpoint = 'coreservice/api/search-criteria/search';
  static readonly deleteSearchCriteriaEndpoint = 'coreservice/api/search-criteria/{id}';
    static readonly getWorkListFormsListEndpoint = 'coreservice/api/data/work-list-forms';
    static readonly getClassicFormsListEndpoint = 'coreservice/api/data/classical-forms';
    static readonly postFormMappingSearchEndpoint = 'coreservice/api/forms/form-mapping/search';
    static readonly formMappingEndpoint = 'coreservice/api/forms/form-mapping';
    static readonly deleteFormMappingEndpoint = 'coreservice/api/forms/form-mapping/{formMappingId}';
    static readonly getFormFieldsListEndpoint = 'coreservice/api/forms/form-field-mapping/form-fields/{formIds}';
    static readonly formFieldsMappingEndpoint = 'coreservice/api/forms/{formMappingId}/form-field-mapping';
    static readonly deleteFormFieldsMappingEndpoint = 'coreservice/api/forms/form-field-mapping/{formFieldMappingId}';
    static readonly getSiteLevelConfiguration = 'coreservice/api/work-list-configuration/search';
    static readonly getConfigurationEndpoint = 'coreservice/api/work-list-configuration/configurations';
    static readonly getSitesEndpoint = 'coreservice/api/sites?excludeSites={excludeSites}';
    static readonly postSiteLevelConfigurationEndpoint = 'coreservice/api/work-list-configuration';
    static readonly deleteSiteConfigurationEndpoint = 'coreservice/api/work-list-configuration/{referralConfigId}';
    static readonly getPatientReferralDetails = 'coreservice/api/users/{userId}';
    //* JAVA APIS END //

}
