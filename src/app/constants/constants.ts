/** This class should contain CONSTANTS only.
 * import this class and access the CONSTANTS where needed.
 */
import { NgbModalOptions } from '@ng-bootstrap/ng-bootstrap';
import { configServerBaseUrl, configBaseUrl } from 'environments/environment';

export class CONSTANTS {
  static readonly siteLogoUrl: string = 'citus-health/site-logos/';
  static readonly fileUploadsUrl = 'writable/filetransfer/uploads/';
  static readonly allDays = ' All Days ';
  static readonly perPage = 25;
  static readonly contentLimit = 25;
  static readonly currentPage = 1;
  static readonly contentOffset = 0;
  static readonly defaultDateRangeFilterDays = 30;
  static readonly formViewNewLineLimit = 200;
  static readonly defaultExportDataCount = 1000;
  static readonly defaultAvatarImageUrl: string = `${configBaseUrl()}citus-health/avatars/profile-pic-clinician.png`;
  static readonly avatarImagePath: string = `${configBaseUrl()}citus-health/avatars/thumbs/`;

  // Validation for semicolon-delimited list of emails
  static readonly multiEmailValidationRegex: RegExp =
    /^((([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]{2,}\s*?;?\s*?)+$/;
  static readonly emailValidationRegex: RegExp =
    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  static readonly phoneMask = ['(', /[0-9]/, /\d/, /\d/, ')', ' ', /\d/, /\d/, /\d/, '-', /\d/, /\d/, /\d/, /\d/, /\d/, /\d/];
  static readonly phoneUnmask = /\D+/g;
  static readonly textMessageSpace = /(?:\r\n|\r|\n)/g;
  static readonly allowedFileDownloads: RegExp = /([^'="]+\.(xlsx|xls|xl|pdf|docx|doc|word|png|jpeg|jpg|jpe|bmp|PNG|XLSX|XLS|XL|PDF|DOCX|DOC|WORD|JPEG|JPG|JPE|BMP))/;
  static readonly downloadFileLink: RegExp = /(?:data-src|ng-src)=['"]([^'"]+?\.json(?:\?[^'"]*)?)['"]/g;
  static readonly allowedFileExtensions = 'pdf doc docx word xl xls xlsx odt jpg JPG jpeg JPEG png PNG';
  static readonly allowedFileSize = 26214400;
  static readonly repeatOptions = [
    { option: 'Does Not Repeat', value: 'none' },
    { option: 'Daily', value: 'daily' },
    { option: 'Weekly', value: 'weekly' },
    { option: 'Monthly', value: 'monthly' }
  ];
  static readonly timeZoneAsiaCalcutta = 'Asia/Calcutta';
  static readonly timeZoneAsiaKolkata = 'Asia/Kolkata';
  static readonly weekDays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
  static readonly repeatIntervals: (string | number)[] = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20];
  static readonly scheduleAvailabilities = [
    { value: '', type: 'Select' },
    { value: '1', type: 'Available' },
    { value: '2', type: 'Unavailable' },
    { value: '3', type: 'On Call' }
  ];
  static readonly staffVisitStatusList = [
    { value: 0, text: 'Pending Staff/Partner Assignment' },
    { value: 1, text: 'Not Confirmed' },
    { value: 2, text: 'Confirmed' },
    { value: 3, text: 'Declined' },
    { value: 4, text: 'Completed' }
  ];
  static readonly ModalConfig: NgbModalOptions = {
    backdrop: false,
    keyboard: false
  };
  static readonly visitActions = {
    complete: 'complete',
    delete: 'delete'
  };
  static readonly visitOccurrence = {
    single: 'single',
    multiple: 'multiple'
  };
  static readonly visitUserListTypes = {
    staff: 'staffs',
    partner: 'partners',
    staffPartner: 'staff-partner',
    patients: 'patients'
  };
  static readonly defaultDepartment = '345';
  static readonly defaultTherapy = '788';
  static readonly singleEditType = 1;
  static readonly visitDateLimit = '01/01/2000';
  static readonly dateFormatOptions = [
    { value: 'm-d-Y', option: 'MM-DD-YYYY' },
    { value: 'Y-m-d', option: 'YYYY-MM-DD' },
    { value: 'd-m-Y', option: 'DD-MM-YYYY' }
  ];
  static readonly dateRangePickerOptions = {
    dateFormat: 'YYYY-MM-DD',
    minDate: '2014-01-01T00:00:00'
  };
  static readonly notificationLanguages = [
    { value: 'en', text: 'English' },
    { value: 'es', text: 'Spanish' }
  ];
  static readonly startTime = '00:00:00';
  static readonly endTime = '23:59:59';
  static readonly defaultNotificationLanguage = 'en';
  static readonly dateRangePicker = { 
    dateFormat: 'YYYY-MM-DD',
    minDate: '2014-01-01T00:00:00'
  };
  static readonly joinChatroomEventSourceComponent = {
    resendFailedWhenInChatroom: 'Failed Message Resend On Reconnect When User In Any Chat',
    resendFailedWhenNotInChatroom: 'Failed Message Resend On Reconnect When User Not In Any Chat',
    chatPageInitialize: 'Message Center Chat Component Initialize',
    chatPageChatSwitch: 'Message Center Chat Switching',
    scheduleCenterChat: 'Schedule Center Chat'
  };
  static readonly patientDobFormat = 'MM/dd/yyyy';
  /** @deprecated Instead use UserGroup */
  static readonly userGroupIds = {
    patient: 3,
    partner: 20
  };
  static readonly enterKeyCode = 13;
  static readonly regexFileName = /[^A-Za-z0-9\- _]/;
  static readonly userTypes = {
    staff: 'staff',
    patient: 'patient',
    partner: 'partner'
  };
  static readonly userRoles = {
    staff: 'Staff',
    patient: 'Patient',
    caregiver: 'Caregiver',
    partner: 'Partner'
  };
  static readonly dateFormats = { MMddyy: 'MM/dd/yy' };
  static readonly badgeCountMultiSelect = 5;
  static readonly clearAll = 'Clear All';
  static readonly contentType = {
    forms: 'Forms',
    form: 'Form',
    documents: 'Documents',
    document: 'Document'
  };
  static readonly elementIds = {
    documentCenter: { resendDocument: 'resend-document' }
  };
  static readonly documentFilterKeys = {
    SIGNED: 'SIGNED',
    PENDING: 'PENDING',
    ARCHIVE: 'ARCHIVE'
  };
  static readonly notificationSource = {
    message: '10001',
    form: '10002',
    document: '10003',
    workList: '10005',
    other: '100010'
  };
  static readonly notificationSourceCategory = {
    messageSendNotification: '10001_001',
    messageEscalationReminderNotification: '10001_002',
    messageEscalationNotification: '10001_003',
    messagePatientReminderNotification: '10001_004',
    videoCallNotification: '10001_005',
    formSentNotification: '10002_001',
    formSubmitNotificationToSender: '10002_003',
    formReSendNotification: '10002_005',
    formSubmitNotificationToNotifyOnSubmitUsers: '10002_004',
    formAllowEditNotification: '10002_006',
    documentSendNotification: '10003_001',
    documentReSendNotification: '10003_005',
    userForwardNotificationForRecipient: '100010_001',
    userForwardNotificationForInitiator: '100010_002',
    userInviteNotification: '100010_003',
    OTPNotification: '100010_004',
    userRegistrationNotification: '100010_005',
    deliveryUpdate: '10006_001',
    custom: 'custom',
    staffReviewingPatientMessage: '10001_006'
  };
  static readonly notificationTypes = {
    success: 'success',
    warning: 'warning',
    danger: 'danger',
    info: 'info',
    input: 'input',
    error: 'error'
  };
  static readonly defaultTimerValue = 1;
  static readonly isMultiSiteEnabledInt = {
    enabled: 1,
    disabled: 0
  };
  static readonly patternForNewline = /^<div><br><\/div>(<div><br><\/div>)*$/;
  static readonly patternForBreak = /^<br>(<br>)+$/;
  static readonly patternForMultipleBreaks = /^((<div>(<br>)*<\/div>|<div> +<\/div>)(<div>(<br>)*<\/div>|<div> +<\/div>)*)$/;
  static readonly audioPath = '../assets/audio/';
  static readonly audioType = '.mp3';
  static readonly defaultAudio = 'glass';
  static readonly defaultCountryCode = '+1';
  static readonly defaultCountryFlags = ['us', 'ca'];
  static readonly citusHealth = 'citus-health';
  static readonly magiclinkTokenExpirationTime = '2880';
  static readonly magiclinkVerificationExpiryTime = '60';
  static readonly magiclinkVerificationTokenExpirationTime = '10';
  static readonly displayAddressType = {
    shippingAddress: 'OPTIONS.ADDRESS.SHIPPING_ADDRESS',
    homeAddress: 'OPTIONS.ADDRESS.HOME_ADDRESS',
    mainAddress: 'OPTIONS.ADDRESS.MAIN_ADDRESS',
    otherAddress: 'OPTIONS.ADDRESS.OTHER_ADDRESS'
  };
}
export enum AdmissionButtonActions {
  DELETE,
  EDIT,
  INACTIVATE,
  ACTIVATE,
  VIEW
}
export enum AdmissionRecordStatus {
  DELETED = 0,
  ACTIVE = 1,
  INACTIVE = 2
}

export enum DocumentTypes {
  EXCEL = 'excel'
}

export enum GroupType {
  MSGGROUP = 'messageGroup',
  PATIENTGROUP = 'patientGroup'
}

export enum Languages {
  EN = 'en'
}

export enum VisitType {
  Home = '1',
  AIC = '2',
  Facility = '3'
}

export enum Status {
  Pending = 'pending',
  Completed = 'completed',
  Archived = 'archived',
  Draft = 'drafts',
  Canceled = 'canceled',
  DRAFT = 'draft'
}

export enum FormType {
  Patient = 'patient facing',
  Staff = 'staff facing'
}

export enum DateFormat {
  YYMMDD_FORMAT_HYPHEN = 'YYYY-MM-DD',
  YYMMDD_FORMAT_SLASH = 'YYYY/MM/DD',
  DDMMYY_FORMAT_HYPHEN = 'DD-MM-YYYY',
  DDMMYY_FORMAT_SLASH = 'DD/MM/YYYY',
  MMDDYY_FORMAT_HYPHEN = 'MM-DD-YYYY',
  MMDDYY_FORMAT_SLASH = 'MM/DD/YYYY',
  TIME_24_HR = 'HH:mm:ss',
  YYMMDD_FORMAT_HYPHEN_TIME_24_HR = 'YYYY-MM-DD HH:mm:ss',
  MMDDYY_HMA = 'MM/DD/YYYY hh:mm A',
  MMDDYY_HHMM_A = 'MM/dd/yyyy hh:mma'
}

export enum DateRanges {
  ALL = 'ALL',
  TODAY = 'TODAY',
  YESTERDAY = 'YESTERDAY',
  THIS_WEEK = 'THIS_WEEK',
  LAST_SEVEN_DAYS = 'LAST_SEVEN_DAYS',
  LAST_THIRTY_DAYS = 'LAST_THIRTY_DAYS',
  LAST_NINETY_DAYS = 'LAST_NINETY_DAYS',
  THIS_MONTH = 'THIS_MONTH',
  LAST_SIX_MONTHS = 'LAST_SIX_MONTHS',
  LAST_MONTH = 'LAST_MONTH'
}

export const LISTING_PAGE_DATERANGE_OPTIONS_MESSAGE = [DateRanges.LAST_SIX_MONTHS, DateRanges.LAST_THIRTY_DAYS];
export const LISTING_PAGE_DATERANGE_OPTIONS = [DateRanges.ALL, DateRanges.LAST_THIRTY_DAYS];

export const NOTIFY_DELAY_TIME_COMMON = 1000;

export enum UserRoles {
  staff = 'Staff',
  patient = 'Patient',
  caregiver = 'Caregiver',
  partner = 'Partner'
}

export enum ConfigStatus {
  ENABLED = '1',
  DISABLED = '0'
}

export enum TagType {
  messageTag = 1,
  userTag = 2,
  documentTag = 3
}

export enum CHECKED {
  TRUE = '1',
  FALSE = '0'
}

export enum MessagePriority {
  HIGH = 1,
  NORMAL = 2,
  LOW = 3
}

export enum Filter {
  ADVANCE = 'advance',
  QUICK = 'quick'
}

export enum UserGroup {
  PATIENT = 3,
  PARTNER = 20
}

export enum PaletteType {
  TEXT = 'Text',
  SIGN = 'Sign',
  CHECKBOX = 'Checkbox'
}

export enum UserFacingType {
  PATIENT = 'patient-facing',
  STAFF_PATIENT = 'staff-patient-facing',
  STAFF = 'staff-facing'
}

export enum FormSendMode {
  APPLESS = 'appless',
  INAPP = 'inapp',
  BOTH = 'both'
}

export const messageFilterOptions = [
  { value: 'all', text: 'OPTIONS.MESSAGE_FILTER_OPTIONS.ALL' },
  { value: '1', text: 'OPTIONS.MESSAGE_FILTER_OPTIONS.USER' },
  { value: '0', text: 'OPTIONS.MESSAGE_FILTER_OPTIONS.SYSTEM' }
];

export const defaultCategoryOfMessage = '1';

export enum MessageArchiveType {
  SELF = 1,
  ALL_USER = 2
}

export enum PrivilegeKeys {
  ARCHIVE_MESSAGE_FOLL_ALL_USER = 'ArchiveChatMessagesForAllUser',
  TAG_MESSAGE = 'TagMessage'
}

export enum CharCodes {
  SPACE = 32,
  NBSP = 160,
  NO_BREAK = 8288
}
export enum VisitAvailabilityView {
  LIST = 'list',
  CALENDAR = 'calendar'
}

export const userDobCalendarMinDate = {
  year: 1900,
  month: 1,
  day: 1
} 
export const localeOptions = {
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: 'numeric',
  minute: '2-digit',
  hour12: true
}

export enum PatientWorkflowType {
  CAREGIVER = 'caregiver',
  ALETRNATE_CONTACT = 'alternate_contacts'
}
export enum LabelValues {
  ASSOCIATE_PATIENT_CLICK = "associatePatientClick",
  ASSOCIATE_PATIENT = "Associate Patient"
}
export enum DataLoadingType {
  SERVERSIDE = 'serverSide',
  CLIENTSIDE = 'clientSide'
}
export enum MessageDeliveryStatus {
  DELIVERED = 0,
  READ = 1,
  ALL = -1
}
export const avatarUrlDefault = `${configServerBaseUrl()}/cometchat/citus-health/avatars/`;
export const avatarUrlThumbs = `${configServerBaseUrl()}/cometchat/citus-health/avatars/thumbs/`;

export const messageDeliveryStatusAll = '-1';

export const msgHistoryDateTimeFormat = 'MM/DD/YYYY hh:mm A';

export enum VisitStatus {
  DRAFT = '2'
}

export enum Platform {
  WEB = 'web',
  MOBILE = 'mobile',
  DESKTOP = 'desktop'
}

export enum AccessLevel {
  ALL = -1,
  ENABLED = 1,
  DISABLED = 0
}

export enum ContentTypes {
  FORM = 'application/x-www-form-urlencoded; charset=UTF-8',
  JSON = 'application/json'
}

export enum MessageOperationType {
  ADD = 'add',
  SUB = 'sub'
}

export enum DefaultPatientWorkFlow {
  ALTERNATE_CONTACTS = 'alternate_contacts'
}

export enum ChatWithTypes {
  STAFF = 'staff',
  PATIENT = 'patient',
  PARTNER = 'partner',
  ROLE = 'role',
  MESSAGE_GROUP = 'messageGroup',
  PATIENT_GROUP = 'patientGroup'
}

export enum ChatAvatarOptions {
  CLINICIAN = 'clinician',
  PATIENT = 'patient',
  BROADCAST = 'broadcast',
  MASKED = 'masked'
}

export enum MessageCategory {
  GENERAL = 'general',
  BROADCAST = 'broadcast',
  MASKED = 'masked',
  PDG = 'pdg',
  MESSAGE_GROUP = 'message-group'
}

export enum MessageType {
  GENERAL = 0,
  BROADCAST = 1,
  MASKED = 2,
  PDG = 3,
  MESSAGE_GROUP = 4,
  PATIENT_INITIATED = 5,
  STAFF_TO_PATIENT = 6,
  STAFF_TO_STAFF = 7
}
// Mapping of file extensions to Font Awesome icons
export const fileIcons = {
  pdf: 'fa-file-pdf-o',
  doc: 'fa-file-word-o',
  docx: 'fa-file-word-o',
  xls: 'fa-file-excel-o',
  xlsx: 'fa-file-excel-o',
  ppt: 'fa-file-powerpoint-o',
  pptx: 'fa-file-powerpoint-o',
  jpg: 'fa-file-image-o',
  jpeg: 'fa-file-image-o',
  png: 'fa-file-image-o',
  gif: 'fa-file-image-o',
  bmp: 'fa-file-image-o',
  mp3: 'fa-file-audio-o',
  wav: 'fa-file-audio-o',
  mp4: 'fa-file-video-o',
  avi: 'fa-file-video-o',
  zip: 'fa-file-archive-o',
  rar: 'fa-file-archive-o',
  html: 'fa-file-code-o',
  css: 'fa-file-code-o',
  js: 'fa-file-code-o',
  json: 'fa-file-code-o',
  csv: 'fa-file-csv',
  txt: 'fa-file-alt',
  default: 'fa-file-o' // Fallback icon
};
export enum DefaultPics {
  PROFILE = './assets/modules/dummy-assets/common/img/profile-pic-default.png',
  BROADCAST = './assets/modules/dummy-assets/common/img/broadcast-message-icon.png',
  MASKED = './assets/modules/dummy-assets/common/img/masked-message.png',
  GROUP = './assets/modules/dummy-assets/common/img/people-group-icon.jpg',
  MSGGROUP = './assets/modules/dummy-assets/common/img/msg-group-icon.png',
  PDG = './assets/modules/dummy-assets/common/img/pdg-group-icon.png',
}

export enum MessageTagType {
  MESSAGE = 'message',
  THREAD = 'thread'
}
export enum DocumentButtons {
  START_SIGNING = 'Start Signing',
  NEXT = 'Next'
}

export enum AddressActions {
  EDIT = 'Edit',
  DELETE = 'Delete'
}
export enum OooStatusColor {
  outOfOffice = '#f9081a',
  messageOnly = '#818080'
}
export enum SignatureStatus {
  SIGN_APPROVAL = 'SIGN_APPROVAL',
  PENDING = 'PENDING',
  DRAFT = 'DRAFT'
}

export enum IntegrationType {
  FORM_ARCHIVE = 'formArchive',
  ADD_MESSAGE_TAG = 'addMessageTag',
  SEND_TO_EHR = 'sendToEHR',
  DOCUMENT_SUBMIT = 'documentSubmit'
}

export enum FilterOptions {
  EQUALS = 'equals',
  LESSTHANOREQUAL = 'lessThanOrEqual',
  INRANGE = 'inRange',
  CONTAINS = 'contains'
}

export enum FilterTypes {
  NUMBER = 'number',
  TEXT = 'text',
  DATE = 'date'
}

export enum PopulatePatientData {
  DISPLAYNAME = 'displayname',
  DOB = 'dob',
  USERID = 'userId',
  FIRSTNAME = 'firstname',
  LASTNAME = 'lastname',
  EMAIL = 'email',
  MOBILE = 'mobile',
  IDENTITYVALUE = 'IdentityValue',
  SITEID = 'siteId',
  TENANTID = 'tenantId',
  ADDRESS = 'address',
  HEALTHCARD = 'Health Card',
  BAYSHOREBRANCHID = 'Bayshore Branch ID',
  CITUSBRANCHID = 'Citus Branch ID',
  PATIENTID = 'Patient ID',
  ADDRESS1 = 'address1',
  ADDRESS2 = 'address2',
  STATE = 'state',
  CITY = 'city',
  COUNTRY = 'country',
  ZIPCODE = 'zip_code'
}
export const advancedFilterCount = 20;
export const messageTypeAndCategory = {
  general: 0,
  broadcast: 1,
  masked: 2,
  pdg: 3,
  'message-group': 4,
  'patient-staff': 5,
  'staff-patient': 6,
  staff: 7
};

export enum VisitStatusOptions {
  PATIENT_NOT_CONFIRMED = 'Patient Not Confirmed',
  PATIENT_CONFIRMED = 'Patient Confirmed',
  PATIENT_CANCELLED = 'Patient Cancelled',
  PATIENT_DECLINED = 'Patient Declined',
  STAFF_NOT_CONFIRMED = 'Staff Not confirmed',
  STAFF_CONFIRMED = 'Staff Confirmed',
  STAFF_COMPLETED = 'Staff Completed',
  STAFF_CANCELLED = 'Staff Cancelled',
  STAFF_DECLINED = 'Staff Declined',
  SAVED_AS_DRAFTS = 'Saved As Drafts',
  REVIEW_COMPLETED = 'Review Completed'
}

export enum PatientStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  VIRTUAL_USER = 'virtual_user',
  DISCHARGED = 'discharged'
}
export enum ModuleName {
  FORM_CENTER = 'formCenter',
  DOCUMENT_CENTER = 'documentCenter'
}
export const PATTERNS = {
  idmPasswordPattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>~+\-=_'`/\\[\];’])[A-Za-z\d!@#$%^&*(),.?":{}|<>~+\-=_'`/\\[\];’\s]{9,}$/,
  usernameDelimiters: /[.,\-_#@]/
}

export enum SignatureUserType {
  ALTERNATE_CONTACT = 4
}
export enum FormLandingFlows {
  MANAGE_FORM = 'WebFormConfiguration',
  FORM_TYPES = 'WebFormTypeConfiguration',
  COMPLETE_FORM = 'ViewForm'
}
