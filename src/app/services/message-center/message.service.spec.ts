import { TestBed } from '@angular/core/testing';
import { MessageService } from './message.service';
import { HttpService } from '../http/http.service';
import { StructureService } from '../../structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { of } from 'rxjs/observable/of';
import { catchError } from 'rxjs/operators';
import { GroupType } from 'app/constants/constants';

describe('MessageService', () => {
  let service: MessageService;
  let httpServiceMock;
  let structureServiceMock;
  let toolTipServiceMock;

  beforeEach(() => {
    httpServiceMock = {
      doPost: jasmine.createSpy('doPost'),
      doGet: jasmine.createSpy('doGet'),
      doPut: jasmine.createSpy('doPut')
    };
    structureServiceMock = {
      showAlertMessagePopup: jasmine.createSpy('showAlertMessagePopup'),
      userDetails: JSON.stringify({ defaultSitesFilter: '1' })
    };
    toolTipServiceMock = {
      getTranslateData: jasmine.createSpy('getTranslateData'),
      getTranslateDataWithParam: jasmine.createSpy('getTranslateDataWithParam')
    };
    TestBed.configureTestingModule({
      providers: [
        MessageService,
        { provide: HttpService, useValue: httpServiceMock },
        { provide: StructureService, useValue: structureServiceMock },
        { provide: ToolTipService, useValue: toolTipServiceMock }
      ]
    });

    service = TestBed.get(MessageService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should get chat messages', () => {
    const mockResponse = { data: { messages: [], totalChatRoomsCount: 0 } };
    httpServiceMock.doPost.and.returnValue(of(mockResponse));
    service.getChatMessages({ siteIds: 1, pageCount: 1, chatroomId: 100 , dateRange: {startDate: '', endDate: ''} }).subscribe((response) => {
      expect(response).toEqual(mockResponse);
    });
    expect(httpServiceMock.doPost).toHaveBeenCalled();
  });

  it('should handle message delete undo history data', () => {
    const mockData = [
      {
        messageStatus: 0,
        deleteUndoHistory: [
          { actionTime: '2023-01-01T00:00:00Z', actionType: 0 },
          { actionTime: '2023-01-02T00:00:00Z', actionType: 1 }
        ]
      }
    ];
    toolTipServiceMock.getTranslateData.and.returnValue('Deleted');
    toolTipServiceMock.getTranslateDataWithParam.and.returnValue('Message deleted at 2023-01-01 00:00:00');
    const result = service.handleMessageDeleteUndoHistoryData(mockData);
    expect(result[0].deleteUndoHistory[0].actionHistory).toBe('Message deleted at 2023-01-01 00:00:00');
  });

  it('should fetch group data', () => {
    const mockResponse = { data: [] };
    httpServiceMock.doGet.and.returnValue(of(mockResponse));
    service.fetchGroupData({ siteIds: [1] }, GroupType.MSGGROUP).subscribe((response) => {
      expect(response).toEqual(mockResponse);
    });
    expect(httpServiceMock.doGet).toHaveBeenCalled();
  });

  it('should update patient discussion group', () => {
    const mockResponse = { success: true };
    httpServiceMock.doPut.and.returnValue(of(mockResponse));
    service.updatePdg({}).subscribe((response) => {
      expect(response).toEqual(mockResponse);
    });
    expect(httpServiceMock.doPut).toHaveBeenCalled();
  });

  it('should create message group', () => {
    const mockResponse = { success: true };
    httpServiceMock.doPost.and.returnValue(of(mockResponse));
    service.createMessageGroup({}).subscribe((response) => {
      expect(response).toEqual(mockResponse);
    });
    expect(httpServiceMock.doPost).toHaveBeenCalled();
  });

  it('should update message group', () => {
    const mockResponse = { success: true };
    httpServiceMock.doPut.and.returnValue(of(mockResponse));
    service.updateMessageGroup({}).subscribe((response) => {
      expect(response).toEqual(mockResponse);
    });
    expect(httpServiceMock.doPut).toHaveBeenCalled();
  });

  it('should get all message group topics list', () => {
    const mockResponse = { data: [] };
    httpServiceMock.doGet.and.returnValue(of(mockResponse));
    service.getAllMessageGroupTopicsList({}).subscribe((response) => {
      expect(response).toEqual(mockResponse);
    });
    expect(httpServiceMock.doGet).toHaveBeenCalled();
  });

  it('should handle message delete undo history data', () => {
    const mockData = [
      {
        messageStatus: 0,
        deleteUndoHistory: [
          { actionTime: '2023-01-01T00:00:00Z', actionType: 0 },
          { actionTime: '2023-01-02T00:00:00Z', actionType: 1 }
        ]
      }
    ];
    toolTipServiceMock.getTranslateData.and.returnValue('Deleted');
    toolTipServiceMock.getTranslateDataWithParam.and.returnValue('Message deleted at 2023-01-01 00:00:00');
    const result = service.handleMessageDeleteUndoHistoryData(mockData);
    expect(result[0].deleteUndoHistory[0].actionHistory).toBe('Message deleted at 2023-01-01 00:00:00');
  });

it('should check message integration status and handle error', () => {
    const mockError = { success: true, error: { status: { message: 'Error' } } };
    httpServiceMock.doPost.and.returnValue(of(mockError));    
    structureServiceMock.showAlertMessagePopup.and.returnValue(Promise.resolve(true));
    service.checkMessageIntegrationStatus({}).pipe(catchError(() => {
      expect(structureServiceMock.showAlertMessagePopup).toHaveBeenCalled();
      return of({ success: false });
    }));
});
});
