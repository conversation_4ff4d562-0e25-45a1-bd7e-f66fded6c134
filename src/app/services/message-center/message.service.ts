import { Injectable } from '@angular/core';
import * as moment from 'moment';
import { Observable } from 'rxjs';
import { HttpParams } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { APIs } from '../../../app/constants/apis';
import {
  GetChatMessages,
  GroupsListingRequest,
  GetInboxMessagesPayload,
  GetChatMessagesResponse,
  MessageGroupRequest,
  GroupTopicsListRequest,
  PatientDiscussionGroup
} from '../../models/message-center/messageCenter';
import { StructureService } from '../../structure/structure.service';
import { isBlank, convertTimeZoneDateTimeToUTCIso, getUserPreferenceSiteIds } from '../../utils/utils';
import { CONSTANTS, ContentTypes, GroupType, msgHistoryDateTimeFormat } from '../../constants/constants';
import { HttpService } from '../http/http.service';

type formDataObj = GetChatMessages;
@Injectable()
export class MessageService {
  private userData: any;
  constructor(private structureService: StructureService, private httpService: HttpService, private toolTipService: ToolTipService) {
    this.userData = JSON.parse(this.structureService.userDetails);
  }

  /**
   * @description This method is used to get chat messages
   * @param params
   * @returns {Observable<any>}
   */
  getChatMessages(params: GetChatMessages): Observable<GetChatMessagesResponse> {
    const siteIds = params.siteIds || getUserPreferenceSiteIds(this.userData.defaultSitesFilter);
    let dateRange: { start: string; end: string };
    if (!isBlank(params.dateRange)) {
      dateRange = {
        start: !isBlank(params.dateRange.startDate)
          ? convertTimeZoneDateTimeToUTCIso(params.dateRange.startDate, CONSTANTS.startTime, moment.tz.guess())
          : params.dateRange.startDate,
        end: !isBlank(params.dateRange.endDate)
          ? convertTimeZoneDateTimeToUTCIso(params.dateRange.endDate, CONSTANTS.endTime, moment.tz.guess())
          : params.dateRange.endDate
      };
    }
    let inboxPayload: GetInboxMessagesPayload;
    if (params.chatroomId) {
      inboxPayload = { filter: { chatroomId: params.chatroomId } };
    } else {
      inboxPayload = {
        archived: !!params.archived,
        page: params.pageCount || 1,
        filter: {
          tagIds: params.tagIds ? params.tagIds.split(',').map(Number) : [],
          flagId: params.flagValue || 0,
          priority: params.messagePriority || 0,
          mention: !!params.mentionedUsers,
          unread: params.unread,
          dateRange,
          searchKeyword: params.searchKeyword || '',
          siteIds: siteIds.toString().split(',').map(Number) || [],
          chatThreadTypes: params.chatThreadTypes || []
        }
      };
    }
    return this.httpService.doPost(APIs.getChatInboxMessages, inboxPayload, ContentTypes.JSON).catch((error) => {
      return Observable.throw(error);
    });
  }

  /**
   * @description This method is used to fetch group data
   * @param params
   * @param groupType
   * @returns {Observable<any>}
   */
  fetchGroupData(params: GroupsListingRequest, groupType: string): Observable<any> {
    const urlEncodedJson = encodeURIComponent(JSON.stringify(params));
    const apiUrl = (groupType === GroupType.MSGGROUP) ? APIs.getAllMessageGroup : APIs.getPatientGroups;
    return this.httpService.doGet(apiUrl, {
      params: new HttpParams().set('payload', urlEncodedJson)
    });
  }
  updatePdg(params: PatientDiscussionGroup): Observable<any> {
    const apiUrl = APIs.updatePatientDiscussionGroup;
    return this.httpService.doPut(apiUrl, params);
  }
  createMessageGroup(params: MessageGroupRequest): Observable<any> {
    return this.httpService.doPost(APIs.createMessageGroup, params);
  }
  updateMessageGroup(params: MessageGroupRequest): Observable<any> {
    return this.httpService.doPut(APIs.updateMessageGroup, params);
  }
  getAllMessageGroupTopicsList(params: GroupTopicsListRequest): Observable<any> {
    const urlEncodedJson = encodeURIComponent(JSON.stringify(params));
    return this.httpService.doGet(APIs.getAllMessageGroupTopicsList, { params: new HttpParams().set('payload', urlEncodedJson) }).catch((error) => {
      return Observable.throw(error);
    });
  }

  checkMessageIntegrationStatus(data) {
    return this.httpService.doPost(APIs.checkMsgIntegrationStatusEndpoint, data, undefined, { skipErrorHandling: true }).pipe(
      catchError((error) => {
        return new Observable((observer) => {
          this.structureService
            .showAlertMessagePopup({
              text: `${error.error.status.message} \n ${this.toolTipService.getTranslateData('WARNING.YOU_CAN_CONTINUE_ANYWAY')}`,
              type: CONSTANTS.notificationTypes.error,
              title: '',
              confirmButtonText: this.toolTipService.getTranslateData('BUTTONS.CONTINUE_ANYWAY'),
              cancelButtonText: this.toolTipService.getTranslateData('BUTTONS.GO_BACK')
            })
            .then((isConfirm) => {
              if (isConfirm) {
                const newError = { ...error.error, success: true };
                observer.next(newError);
              } else {
                observer.next(error);
              }
              observer.complete();
            });
        });
      })
    );
  }
  /**
   * To get the delete undo history for chat messages with revers order. Need to pass the chat messages array
   * Use the actionHistory property to display the delete undo history in the UI with date and time
   * @param data an array of chat messages
   * @returns an array of chat messages with delete undo history, in reverse order
   */
  handleMessageDeleteUndoHistoryData(data): Array<any> {
    const chats:any[] = data || [];
    chats.forEach((chat) => {
      if (chat.messageStatus === 0 && Array.isArray(chat.deleteUndoHistory)) {
          chat.deleteUndoHistory.forEach((item) => {
              const actionTime = moment(item.actionTime).format(msgHistoryDateTimeFormat);
              const action =  this.toolTipService.getTranslateData(item.actionType === 0 ? 'MESSAGES.MSG_HISTORY.DELETED': 'MESSAGES.MSG_HISTORY.RESTORED');
              item.actionHistory = this.toolTipService.getTranslateDataWithParam('MESSAGES.MSG_HISTORY.MESSAGE', { actionTime, action });
          });
      }          
    });
    return chats;
  }
}
