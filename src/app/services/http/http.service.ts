/* eslint-disable consistent-return */
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse, HttpParams, HttpRequest } from '@angular/common/http';
import { apiCoreBaseUrl, configBaseUrl } from 'environments/environment';
import { Observable } from 'rxjs';
import { ContentTypes } from 'app/constants/constants';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { StructureService } from '../../structure/structure.service';
import { catchError } from 'rxjs/operators';

@Injectable()
export class HttpService {
  constructor(private httpClient: HttpClient, private structureService: StructureService, private toolTipService: ToolTipService) {}

  get headers(): HttpHeaders {
    const headers = new HttpHeaders({
      'Content-Type': ContentTypes.JSON,
      'authentication-token': this.structureService.getCookie('authenticationToken') ? this.structureService.getCookie('authenticationToken') : ''
    });
    return headers;
  }

  getFullServiceUrl(url: string): string {
    const baseUrl = url.includes('.php') ? configBaseUrl() : apiCoreBaseUrl;
    if (baseUrl.endsWith('/')) {
      return `${baseUrl}${url}`;
    }
    return `${baseUrl}/${url}`;
  }

  doPost(url, data, contentType?: ContentTypes, settings?: any) {
    const options = this.getOptions(contentType, data);
    const { skipErrorHandling = false } = settings || {};
    return this.httpClient.post(this.getFullServiceUrl(url), data, options).catch((error) => this.handleError(error, skipErrorHandling));
  }
  doPostForm(url, data: FormData, contentType?: ContentTypes, settings?: any) {
    const options = this.getOptions(contentType, data);
    const { skipErrorHandling = false } = settings || {};
    return this.httpClient.post(this.getFullServiceUrl(url), data, {
      headers: new HttpHeaders({
        'authentication-token': this.structureService.getCookie('authenticationToken')
      })
    }).catch((error) => this.handleError(error, skipErrorHandling));
  }

  doGet(
    url,
    options?: {
      headers?:
        | HttpHeaders
        | {
            [header: string]: string | string[];
          };
      observe?: 'body';
      params?:
        | HttpParams
        | {
            [param: string]: string | string[];
          };
      reportProgress?: boolean;
      responseType?: any;
      withCredentials?: boolean;
    },
    settings?: any
  ) {
    const { skipErrorHandling = false } = settings || {};
    return this.httpClient
      .get(this.getFullServiceUrl(url), {
        headers: this.headers,
        params: options && options.params ? options.params : new HttpParams(),
        responseType: options && options.responseType ? options.responseType : 'json'
      })
      .catch((error) => this.handleError(error, skipErrorHandling));
  }

  doPut(url, data) {
    return this.httpClient
      .put(this.getFullServiceUrl(url), data, {
        headers: this.headers
      })
      .catch((error) => this.handleError(error));
  }

  doDelete(url, data?) {
    if (data) {
      return this.httpClient
        .request('DELETE', this.getFullServiceUrl(url), {
          body: data,
          headers: this.headers
        })
        .catch((error) => this.handleError(error));
    }
    return this.httpClient
      .delete(this.getFullServiceUrl(url), {
        headers: this.headers
      })
      .catch((error) => this.handleError(error));
  }

  private handleError(error: HttpErrorResponse, skipErrorHandling?: boolean) {
    if (!this.isValidErrorCode(error.status) && !skipErrorHandling) {
      this.notifyErrorMsg(error);
    }
    return Observable.throw(error);
  }

  public notifyErrorMsg(error: HttpErrorResponse) {
    const errorMsg = this.getErrorMsg(error);
    this.structureService.notifyMessage({
      messge: errorMsg,
      type: 'danger'
    });
  }

  public isValidErrorCode(status: number) {
    return status === 404 || status === 409;
  }

  public getErrorMsg(error: HttpErrorResponse): string {
    const customError: any = error.error;
    let errorMsg = '';
    if (customError.customerMessages && customError.customerMessages.length > 0) {
      errorMsg = customError.customerMessages[0];
    } else if (customError.data && customError.data.errors && customError.data.errors.length > 0) {
      errorMsg = customError.data.errors[0].message;
    } else if (customError.status && customError.status.message) {
      errorMsg = customError.status.message;
    } else {
      errorMsg = customError.message || this.toolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG');
    }
    return errorMsg;
  }

  private getOptions(contentType?: ContentTypes, data?: any): { headers: HttpHeaders; params?: HttpParams } {
    let options: { headers: HttpHeaders; params?: HttpParams };
    if (contentType && contentType === ContentTypes.FORM) {
      let headers = new HttpHeaders();
      headers = headers.append('Content-Type', contentType);
      headers = headers.append('authentication-token', this.structureService.getCookie('authenticationToken'));
      options = { headers, params: new HttpParams({ fromObject: data }) };
    } else {
      options = { headers: this.headers };
    }
    return options;
  }
}
