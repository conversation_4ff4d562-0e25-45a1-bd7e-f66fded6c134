import { Injectable } from '@angular/core';
import { ROUTES } from 'app/constants/routes';
import { isBlank } from 'app/utils/utils';

@Injectable()
export class PermissionService {
  constructor() {}
  getDefaultPageFormCenter(config, privileges): string {
    let defaultPage = ROUTES.myformworklist;
    if (!isBlank(config.enable_forms) && config.enable_forms == '1') {
      if (privileges.indexOf('viewFormEntries') !== -1) {
        defaultPage = ROUTES.allformworklist;
      }
    }
    return defaultPage;
  }

}
