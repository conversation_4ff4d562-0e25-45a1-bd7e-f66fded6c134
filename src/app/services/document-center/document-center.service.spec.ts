import { HttpParams } from '@angular/common/http';
import { IntegrationType } from 'app/constants/constants';
import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs/observable/of';
import { APIs } from '../../../app/constants/apis';
import { StructureService } from '../../structure/structure.service';
import { CommonTestingModule } from '../../utils/common-testing.module';
import { HttpService } from '../http/http.service';
import { DocumentCenterService } from './document-center.service';

describe('DocumentCenterService', () => {
  let documentCenterService: DocumentCenterService;
  let httpService: HttpService;
  let structureService: StructureService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [CommonTestingModule]
    });
    httpService = TestBed.get(HttpService);
    structureService = TestBed.get(StructureService);
    documentCenterService = TestBed.get(DocumentCenterService);
  });

  describe('checkDocumentIntegrationStatus', () => {
    const patientId = '123';
    const documentTypeId = '456';
    const admissionId = '789';
    let params;
    beforeEach(() => {
      params = new HttpParams().set('patient_id', patientId).set('documentTypeId', documentTypeId).set('action', IntegrationType.DOCUMENT_SUBMIT);
    });
    it('should call httpService.doGet with the correct parameters', () => {
      params = params.set('admissionId', admissionId);
      structureService.userDetails = JSON.stringify({ config: { enable_multi_admissions: '1' } });
      spyOn(httpService, 'doGet').and.returnValue(of({}));
      documentCenterService.checkDocumentIntegrationStatus(patientId, documentTypeId, admissionId);
      expect(httpService.doGet).toHaveBeenCalledWith(APIs.checkDocIntegrationStatusEndpoint, { params }, { skipErrorHandling: true });
    });

    it('should call httpService.doGet without admissionId if multi admissions are not enabled', () => {
      structureService.userDetails = JSON.stringify({ config: { enable_multi_admissions: '0' } });
      spyOn(httpService, 'doGet').and.returnValue(of({}));
      documentCenterService.checkDocumentIntegrationStatus(patientId, documentTypeId, admissionId);
      expect(httpService.doGet).toHaveBeenCalledWith(APIs.checkDocIntegrationStatusEndpoint, { params }, { skipErrorHandling: true });
    });
  });
});
