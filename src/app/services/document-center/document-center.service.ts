import { Injectable } from '@angular/core';
import { HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { catchError } from 'rxjs/operators';

import { CONSTANTS, IntegrationType } from 'app/constants/constants';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { APIs } from '../../../app/constants/apis';
import { StructureService } from '../../structure/structure.service';
import { HttpService } from '../http/http.service';

@Injectable()
export class DocumentCenterService {
  constructor(private httpService: HttpService, private structureService: StructureService, private toolTipService: ToolTipService) {}

  checkDocumentIntegrationStatus(patientId: string, documentTypeId: string, admissionId: string, skipErrorPopup = false) {
    let params = new HttpParams().set('patient_id', patientId).set('documentTypeId', documentTypeId).set('action', IntegrationType.DOCUMENT_SUBMIT);
    if (this.structureService.isMultiAdmissionsEnabled) {
      params = params.set('admissionId', admissionId);
    }

    return this.httpService.doGet(APIs.checkDocIntegrationStatusEndpoint, { params }, { skipErrorHandling: true }).pipe(
      catchError((error) => {
        return new Observable((observer) => {
          if (!skipErrorPopup) {
            this.structureService
              .showAlertMessagePopup({
                text: `${error.error.status.message} \n ${this.toolTipService.getTranslateData('WARNING.YOU_CAN_CONTINUE_ANYWAY')}`,
                type: CONSTANTS.notificationTypes.error,
                title: '',
                confirmButtonText: this.toolTipService.getTranslateData('BUTTONS.CONTINUE_ANYWAY'),
                cancelButtonText: this.toolTipService.getTranslateData('BUTTONS.GO_BACK')
              })
              .then((isConfirm) => {
                const newError = { ...error.error, isContinueAnyway: isConfirm };
                observer.next(newError);
                observer.complete();
              });
          } else {
            observer.next(error);
            observer.complete();
          }
        });
      })
    );
  }
}
