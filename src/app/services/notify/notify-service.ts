/**
 * Service for handling notifications.
 * This file is used for the notification service.
 */
import { Injectable } from '@angular/core';
import { StructureService } from '../../structure/structure.service';
import { ToolTipService } from '../../structure/tool-tip.service';

@Injectable()
export class NotifyService {
  constructor(private tooltipService: ToolTipService, private structureService: StructureService) {}

  /**
   * Notifies a common error message.
   */
  notifyCommonError() {
    this.structureService.notifyMessage({ messge: this.tooltipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG') });
  }
  /** Notify API exception */
  notifyAPIException() {
    this.structureService.notifyMessage({ messge: this.tooltipService.getTranslateData('MESSAGES.ERROR_MSG_EXCEPTION_CATCH') });
  }
}
