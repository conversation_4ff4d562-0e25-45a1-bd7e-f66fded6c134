import { Injectable } from '@angular/core';
import { StructureService } from 'app/structure/structure.service';
import { CONSTANTS, ModuleName } from 'app/constants/constants';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { isBlank } from 'app/utils/utils';
import { SaveSearch, UpdateSearch } from 'app/models/configuration/searchCriteria';
import { HttpService } from '../http/http.service';
import { APIs } from '../../constants/apis';

@Injectable()
export class SearchService {
  userData: any = {};
  constructor(
    private httpService: HttpService,
    private readonly structureService: StructureService,
    private readonly toolTipService: ToolTipService
  ) {
    this.userData = JSON.parse(this.structureService.userDetails);
  }

  /**
   * Deletes an advanced search filter criteria after user confirmation.
   * @param selectedFilter The filter to be deleted.
   * @returns An observable with the deletion response.
   */
  deleteAdvanceSearchFilterCriteria(selectedFilter, moduleName): Observable<any> {
    const activityData: any = { activityType: moduleName === 'formCenter' ? 'Form Worklist' : 'Document Worklist' };
    return new Observable((observer) => {
      this.structureService
        .showAlertMessagePopup({
          text: this.toolTipService.getTranslateData('MESSAGES.ADV_FILTER_DELETE_CONFIRM')
        })
        .then((isConfirm) => {
          if (isConfirm) {
            this.httpService
              .doDelete(APIs.deleteSearchCriteriaEndpoint.replace(/{id}/g, selectedFilter.id), {})
              .pipe(
                tap(
                  (response) => {
                    if (response.status) {
                      this.structureService.notifyMessage({
                        message: this.toolTipService.getTranslateData('SUCCESS_MESSAGES.ADV_FILTER_DELETE'),
                        type: CONSTANTS.notificationTypes.success
                      });
                    }
                    activityData.activityName = 'Delete Filter';
                    activityData.activityDescription = `Filter(${selectedFilter.itemName}) deleted by ${this.userData.displayName}`;
                    this.structureService.trackActivity(activityData);
                    observer.next(response);
                    observer.complete();
                  },
                  (error) => {
                    activityData.activityName = 'Delete Filter';
                    activityData.activityDescription = `Filter(${selectedFilter.itemName}) delete failed for the user ${this.userData.displayName}`;
                    this.structureService.trackActivity(activityData);
                    this.structureService.notifyMessage({
                      message: this.toolTipService.getTranslateData('ERROR_MESSAGES.ADV_FILTER_DELETE'),
                      type: CONSTANTS.notificationTypes.error
                    });
                    observer.error(error);
                  }
                )
              )
              .subscribe();
          } else {
            observer.complete();
          }
        });
    });
  }
  updateSearch(saveSearchParam: any): Observable<any> {
    const saveSearch: UpdateSearch = {
      id: saveSearchParam.id,
      moduleName: saveSearchParam.module,
      searchFilterName: saveSearchParam.searchFilterName,
      searchFilterCriteria: JSON.stringify({ filters: saveSearchParam.searchFilterCriteria }),
      isDefault: saveSearchParam.searchFilterCriteria.isDefault,
      columns: saveSearchParam.columns
    };
    return new Observable((observer) => {
      this.structureService
        .showAlertMessagePopup({ text: this.toolTipService.getTranslateData('MESSAGES.ADV_FILTER_UPDATE_CONFIRM') })
        .then((confirm) => {
          if (!confirm) {
            observer.complete();
            return;
          }
          this.httpService.doPut(APIs.searchCriteriaEndpoint, saveSearch).subscribe(
            (response) => {
              const isSuccess = response && response.id;
              const activityType = saveSearchParam.module === ModuleName.FORM_CENTER ? 'forms worklist' : 'document worklist';
              this.structureService.notifyMessage({
                message: this.toolTipService.getTranslateData(isSuccess ? 'SUCCESS_MESSAGES.ADV_FILTER_UPDATE' : 'ERROR_MESSAGES.ADV_FILTER_UPDATE'),
                type: isSuccess ? CONSTANTS.notificationTypes.success : CONSTANTS.notificationTypes.warning
              });
              const activityData: any = {
                activityType,
                activityName: isSuccess ? 'Filter updated successfully' : 'Filter update failed',
                activityDescription: isSuccess
                  ? `Filter updated by ${this.userData.displayName}`
                  : `Filter update failed for the user ${this.userData.displayName}`
              };
              this.structureService.trackActivity(activityData);
              observer.next(response);
              observer.complete();
            },
            (error) => {
              this.structureService.notifyMessage({
                message: this.toolTipService.getTranslateData('ERROR_MESSAGES.ADV_FILTER_UPDATE'),
                type: CONSTANTS.notificationTypes.warning
              });
              const activityType = saveSearchParam.module === ModuleName.FORM_CENTER ? 'forms worklist' : 'document worklist';
              const activityData = {
                activityType,
                activityName: 'Filter update failed',
                activityDescription: `Filter update failed for the user ${this.userData.displayName}`
              };
              this.structureService.trackActivity(activityData); // Track failure activity
              observer.error(error);
            }
          );
        });
    });
  }
  saveSearch(saveSearchParam) {
    const { searchFilterCriteria } = saveSearchParam;
    if ('signatureColumns' in searchFilterCriteria) {
      delete searchFilterCriteria.signatureColumns;
    }
    if ('isDefault' in searchFilterCriteria) {
      delete searchFilterCriteria.isDefault;
    }
    const saveSearch: SaveSearch = {
      moduleName: saveSearchParam.module,
      searchFilterName: saveSearchParam.searchFilterName,
      searchFilterCriteria: JSON.stringify({ filters: searchFilterCriteria }),
      isDefault: saveSearchParam.searchFilterCriteria.isDefault,
      columns: saveSearchParam.columns
    };
    const activityType = saveSearch.moduleName === saveSearchParam.module ? 'forms worklist' : 'documents worklist';
    const activityData: any = { activityType };
    return new Observable((observer) => {
      this.httpService.doPost(APIs.searchCriteriaEndpoint, saveSearch).subscribe(
        (response) => {
          if (response && response.id) {
            this.structureService.notifyMessage({
              message: this.toolTipService.getTranslateData('SUCCESS_MESSAGES.ADV_FILTER_SAVE'),
              type: CONSTANTS.notificationTypes.success
            });

            activityData.activityName = 'Filter saved successfully';
            activityData.activityDescription = `Filter saved by ${this.userData.displayName}`;
            this.structureService.trackActivity(activityData);
            observer.next(response);
          } else {
            this.structureService.notifyMessage({
              message: this.toolTipService.getTranslateData('ERROR_MESSAGES.ADV_FILTER_SAVE'),
              type: CONSTANTS.notificationTypes.warning
            });

            activityData.activityName = 'Filter save failed';
            activityData.activityDescription = `Filter save failed for the user  ${this.userData.displayName}`;
            this.structureService.trackActivity(activityData);

            observer.error(null);
          }
        },
        () => {
          activityData.activityName = 'Filter save failed';
          activityData.activityDescription = `Filter save failed for the user ${this.userData.displayName}`;
          this.structureService.trackActivity(activityData);

          observer.error(null);
        }
      );
    });
  }
  /** To check for duplicate filter name
   * @returns true if duplicate filter name found
   */
  validateAdvancedFilterOptions(advanceSearchInput, advancedFilterOptions, selectedFilterItem, selectedFilterName, isNewFilter): boolean {
    if (isNewFilter && advancedFilterOptions.length >= 20) {
      return this.showValidationMessage('VALIDATION_MESSAGES.ADV_FILTER_LIMIT');
    }
    if (Object.keys(advanceSearchInput).length === 0) {
      return this.showValidationMessage('VALIDATION_MESSAGES.ADV_FILTER_OPTIONS');
    }
    const selectedFilterId = Array.isArray(selectedFilterItem) && selectedFilterItem.length ? selectedFilterItem[0].id : selectedFilterItem.id;
    const defaultLabel = this.toolTipService.getTranslateData('LABELS.DEFAULT');
    const isDuplicate = advancedFilterOptions.some((item) => {
      const itemNameNormalized = this.normalizeFilterName(item.itemName, defaultLabel);
      const selectedNameNormalized = this.normalizeFilterName(selectedFilterName, defaultLabel);
      if (isBlank(selectedFilterItem)) {
        return itemNameNormalized === selectedNameNormalized;
      }
      return item.id !== selectedFilterId && itemNameNormalized === selectedNameNormalized;
    });

    if (isDuplicate) {
      return this.showValidationMessage('VALIDATION_MESSAGES.ADV_FILTER_DUPLICATE_NAME');
    }
    return true;
  }
  normalizeFilterName(name: string, suffixToRemove?: string): string {
    const defaultSuffixRegex = new RegExp(`\\s*\\(${suffixToRemove}\\)$`, 'i');
    return name
      .replace(defaultSuffixRegex, '')
      .trim()
      .toLowerCase();
  }
  showValidationMessage(messageKey: string): boolean {
    this.structureService.notifyMessage({
      message: this.toolTipService.getTranslateData(messageKey),
      type: CONSTANTS.notificationTypes.warning
    });
    return false;
  }
}
