import { fakeAsync, TestBed, tick } from '@angular/core/testing';
import { of } from 'rxjs/observable/of';
import { CommonTestingModule } from 'app/utils/common-testing.module';
import { TagService } from './tag.service';
import { StructureService } from '../../structure/structure.service';
import { ToolTipService } from '../../structure/tool-tip.service';
import { TagType } from '../../constants/constants';

describe('TagService', () => {
  let service: TagService;
  let structureServiceSpy: StructureService;
  let toolTipServiceSpy: ToolTipService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [TagService, StructureService, ToolTipService],
      imports: [CommonTestingModule]
    });

    service = TestBed.get(TagService);
    structureServiceSpy = TestBed.get(StructureService);
    toolTipServiceSpy = TestBed.get(ToolTipService);
    spyOn(structureServiceSpy, 'trackActivity').and.returnValue(of({}));
    spyOn(structureServiceSpy, 'notifyMessage').and.returnValue(of({}));
  });
  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should remove a tag successfully', fakeAsync(() => {
    const tagId = 1;
    const tagType = TagType.documentTag;
    const tagName = 'Test Tag';

    spyOn(structureServiceSpy, 'showAlertMessagePopup').and.returnValue(Promise.resolve(true));
    spyOn(structureServiceSpy, 'deleteTag').and.returnValue(of({ data: { deleteTag: { id: tagId } } }));
    spyOn(toolTipServiceSpy, 'getTranslateData').and.callFake((msg) => msg);
    spyOn(structureServiceSpy, 'getUserdata').and.returnValue({ displayName: 'Test User', userId: 123 });

    let result;
    service.removeTag({ tagId, tagType, tagName }).then((res) => {
      result = res;
    });
    tick();
    expect(result).toBe(true);
    expect(structureServiceSpy.deleteTag).toHaveBeenCalledWith(tagId, tagType);
    expect(structureServiceSpy.trackActivity).toHaveBeenCalled();
    expect(structureServiceSpy.notifyMessage).toHaveBeenCalled();
  }));

  it('should handle tag removal failure', fakeAsync(() => {
    const tagId = 1;
    const tagType = TagType.documentTag;

    spyOn(structureServiceSpy, 'showAlertMessagePopup').and.returnValue(Promise.resolve(true));
    spyOn(structureServiceSpy, 'deleteTag').and.returnValue(of({ data: { deleteTag: { id: 2 } } }));
    spyOn(toolTipServiceSpy, 'getTranslateData').and.callFake((msg) => msg);

    let result;
    service.removeTag({ tagId, tagType }).then((res) => {
      result = res;
    });
    tick();
    expect(result).toBe(false);
    expect(structureServiceSpy.deleteTag).toHaveBeenCalledWith(tagId, tagType);
    expect(structureServiceSpy.notifyMessage).toHaveBeenCalled();
  }));

  it('should show error on tag removal failure', () => {
    const tagType = TagType.documentTag;
    spyOn(toolTipServiceSpy, 'getTranslateData').and.callFake((msg) => msg);

    service.showErrorOnRemoveTag(tagType);

    expect(toolTipServiceSpy.getTranslateData).toHaveBeenCalledWith('MESSAGES.DOCUMENT_TYPE_REMOVE_FAILED');
    expect(structureServiceSpy.notifyMessage).toHaveBeenCalledWith({
      message: 'MESSAGES.DOCUMENT_TYPE_REMOVE_FAILED',
      type: 'error'
    });
  });

  it('should get the correct confirm message', () => {
    const tagType = TagType.documentTag;
    const message = service.getConfirmMessage(tagType);

    expect(message).toBe('MESSAGES.DOCUMENT_TYPE_REMOVE_CONFIRM');
  });

  it('should show success on tag removal for messageTag', () => {
    const tagType = TagType.messageTag;
    const tagId = 2;
    const tagName = 'Message Tag';
    const userData = { displayName: 'Test User', userId: 123 };

    spyOn(structureServiceSpy, 'getUserdata').and.returnValue(userData);
    spyOn(toolTipServiceSpy, 'getTranslateData').and.callFake((msg) => msg);

    service.showSuccessOnRemoveTag(tagType, tagId, tagName);

    expect(structureServiceSpy.trackActivity).toHaveBeenCalled();
    expect(structureServiceSpy.notifyMessage).toHaveBeenCalledWith({
      message: 'SUCCESS_MESSAGES.MESSAGE_TAG_REMOVED',
      type: 'success'
    });
  });

  it('should show success on tag removal for documentTag', () => {
    const tagType = TagType.documentTag;
    const tagId = 2;
    const tagName = 'Document Tag';
    const userData = { displayName: 'Test User', userId: 123 };

    spyOn(structureServiceSpy, 'getUserdata').and.returnValue(userData);
    spyOn(toolTipServiceSpy, 'getTranslateData').and.callFake((msg) => msg);

    service.showSuccessOnRemoveTag(tagType, tagId, tagName);
    expect(structureServiceSpy.trackActivity).toHaveBeenCalled();
    expect(structureServiceSpy.notifyMessage).toHaveBeenCalledWith({
      message: 'MESSAGES.DOCUMENT_TYPE_REMOVE_SUCCESS',
      type: 'success'
    });
  });

  it('should show success on tag removal for userTag', () => {
    const tagType = TagType.userTag;
    const tagId = 3;
    const tagName = 'User Tag';
    const userData = { displayName: 'Test User', userId: 123 };

    spyOn(structureServiceSpy, 'getUserdata').and.returnValue(userData);
    spyOn(toolTipServiceSpy, 'getTranslateData').and.callFake((msg) => msg);

    service.showSuccessOnRemoveTag(tagType, tagId, tagName);

    expect(structureServiceSpy.trackActivity).toHaveBeenCalled();
    expect(structureServiceSpy.notifyMessage).toHaveBeenCalledWith({
      message: 'MESSAGES.USER_TAG_REMOVE_SUCCESS',
      type: 'success'
    });
  });
});
