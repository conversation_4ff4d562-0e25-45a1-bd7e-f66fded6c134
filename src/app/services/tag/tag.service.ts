import { Injectable } from '@angular/core';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { TagType } from 'app/constants/constants';

declare const swal: any;
declare const $: any;
@Injectable()
export class TagService {
  constructor(private structureService: StructureService, private toolTipService: ToolTipService) {}
  removeTag(params: { tagId: number; tagType; tagName?: string }) {
    const { tagId, tagType, tagName } = params;
    return new Promise((resolve) => {
      let isEnterClicked = false;
      $(document).keypress((event) => {
        if (event.keyCode === 13) {
          isEnterClicked = true;
        }
      });
      this.structureService
        .showAlertMessagePopup({
          text: this.toolTipService.getTranslateData(this.getConfirmMessage(tagType))
        })
        .then((confirm) => {
          if (isEnterClicked) {
            swal.close();
          } else if (confirm) {
            this.structureService.deleteTag(tagId, tagType).subscribe(
              (response: any) => {
                if (response && response.data && response.data.deleteTag && +response.data.deleteTag.id === +tagId) {
                  this.showSuccessOnRemoveTag(tagType, tagId, tagName);
                  resolve(true);
                } else {
                  this.showErrorOnRemoveTag(tagType);
                  resolve(false);
                }
              },
              () => {
                this.showErrorOnRemoveTag(tagType);
                resolve(false);
              }
            );
          }
        });
    });
  }

  showErrorOnRemoveTag(tagType) {
    const errorMessages = {
      [TagType.documentTag]: 'MESSAGES.DOCUMENT_TYPE_REMOVE_FAILED',
      [TagType.userTag]: 'MESSAGES.USER_TAG_REMOVE_FAILED',
      [TagType.messageTag]: 'MESSAGES.MESSAGE_TAG_REMOVE_FAILED'
    };
    if (errorMessages[tagType]) {
      this.structureService.notifyMessage({
        message: this.toolTipService.getTranslateData(errorMessages[tagType]),
        type: 'error'
      });
    }
  }
  getConfirmMessage(tagType) {
    const confirmMessages = {
      [TagType.documentTag]: 'MESSAGES.DOCUMENT_TYPE_REMOVE_CONFIRM',
      [TagType.userTag]: 'MESSAGES.USER_TAG_REMOVE_CONFIRM',
      [TagType.messageTag]: 'MESSAGES.ARE_YOU_SURE_REMOVE_TAG'
    };
    return confirmMessages[tagType];
  }
  showSuccessOnRemoveTag(tagType, tagId, tagName = '') {
    let successMessage;
    let activityName;
    let activityDescription;
    let activityType;
    const userData = this.structureService.getUserdata();
    if (tagType === TagType.documentTag) {
      successMessage = 'MESSAGES.DOCUMENT_TYPE_REMOVE_SUCCESS';
      activityName = 'Delete Document Tag';
      activityType = 'manage tag';
      activityDescription = `${userData.displayName} deleted doc tag with id ${tagId}`;
    } else if (tagType === TagType.userTag) {
      successMessage = 'MESSAGES.USER_TAG_REMOVE_SUCCESS';
      activityName = 'Delete User Tag';
      activityType = 'manage tag';
      activityDescription = `${userData.displayName} (User-${userData.userId}) deleted the User Tag ${tagName}(${tagId})`;
    } else if (tagType === TagType.messageTag) {
      successMessage = 'SUCCESS_MESSAGES.MESSAGE_TAG_REMOVED';
      activityName = 'delete message tag';
      activityType = 'manage message tag';
      activityDescription = `${userData.displayName} deleted message tag - ${tagName} (${tagId})`;
    }

    if (successMessage) {
      const activityData = {
        activityName,
        activityType,
        activityDescription
      };
      this.structureService.trackActivity(activityData);
      this.structureService.notifyMessage({
        message: this.toolTipService.getTranslateData(successMessage),
        type: 'success'
      });
    }
  }
}
