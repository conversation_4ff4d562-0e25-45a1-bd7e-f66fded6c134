import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpParams } from '@angular/common/http';
import { HttpService } from '../http/http.service';
import { APIs } from '../../constants/apis';
import { isBlank } from '../../utils/utils';
import { StructureService } from '../../structure/structure.service';

@Injectable()
export class UserService {
  userData: any;
  constructor(private httpService: HttpService, private structureService: StructureService) {
    this.userData = JSON.parse(this.structureService.userDetails);
  }
  // TODO: CHP-13760 Once the revamped API is ready, this method will be handled with that API and to fetch all users like patients, staffs and partners.
  /**
   * To fetch the list of associated patients.
   * @param form
   * @param search
   * @param chatRoomId
   * @param siteIds
   * @returns
   */
  getAssociatedPatientsLists(form, search = '', chatRoomId = 0, siteIds = 0, externalUserId = ''): Observable<any> {
    const userData = JSON.parse(this.structureService.userDetails);
    const apiURL = APIs.getTenantAssociatedPatient;
    let params = new HttpParams().set('siteIds', siteIds.toString());
    if (externalUserId) {
      params = params.set('externalPatientId', externalUserId);
    } else {
      if (!isBlank(search)) {
        params = params.set('searchKeyword', encodeURIComponent(search));
      }
      if (!isBlank(chatRoomId) && +chatRoomId !== 0) {
        params = params.set('chatRoomId', chatRoomId.toString());
      }
      if (+userData.config.enable_nursing_agencies_visibility_restrictions === 1 && !isBlank(form) && !isBlank(form.tagId)) {
        params = params.set('tagId', form.tagId);
      }
      if (userData.accessSecurityEnabled) {
        params = params.set('accessSecurityEnabled', userData.accessSecurityEnabled);
        params = params.set('accessSecurityEsiValue', userData.accessSecurityEsiValue);
        params = params.set('accessSecurityIdentifierType', userData.accessSecurityIdentifierType);
        params = params.set('accessSecurityType', userData.accessSecurityType);
      }
    }
    return this.httpService.doGet(apiURL, { params }).catch((error) => {
      return Observable.throw(error);
    });
  }
  /**
   * Hide/show general privileges based on tenant configuration in the roles and privileges page
   * @param tenantConfig Tenant configuration
   * @param privilegeKeys Privilege keys to hide
   * @param generalPrivileges General privileges, an array with privilege keys and ids
   * @returns an array of general privileges
   */
  handleUserGeneralPrivileges(tenantConfig: string, privilegeKeysToHide: string[], generalPrivileges: any[]): any[] {
    let privileges = [...generalPrivileges];
    if (this.userData.config && this.userData.config[tenantConfig] && +this.userData.config[tenantConfig] === 0) {
      privileges = generalPrivileges.map((prv) => {
        if (privilegeKeysToHide.includes(prv.privilegeKey)) {
          return { ...prv, hide: true };
        }
        return prv;
      });
    }
    return privileges;
  }
}
