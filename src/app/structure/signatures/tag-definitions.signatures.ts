import { Component, OnInit } from '@angular/core';
import { StructureService } from '../structure.service';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Component({
  selector: 'app-tag',
  templateUrl: './tag-definitions.html'
})

export class TagDefinitionsComponent implements OnInit
{
  constructor(
    public _structureService: StructureService
  )
  {
   
  }
  ngOnInit() {

    $(function () {

      ///////////////////////////////////////////////////////////
      // datatables
      $('#example1').DataTable({
        responsive: true
      });


    });

  }
}

