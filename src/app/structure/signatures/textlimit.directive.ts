import { Directive, ElementRef, Input,HostListener,Output,EventEmitter,Renderer } from '@angular/core';

@Directive({
  selector: '[appTextlimit]'
})
export class TextlimitDirective {


  textval:string='';

  constructor(private el: ElementRef,private ren:Renderer) {


   }

 
   @Input() ngModel;
   @Output() ngModelChange = new EventEmitter();


   @HostListener('window:keydown', ['$event'])
   keyEventDown(event: KeyboardEvent) {   
    if(this.el.nativeElement.offsetHeight+1<this.el.nativeElement.scrollHeight){
    
       event.stopPropagation();
          this.ngModel = this.ngModel.substring(0, this.ngModel.length-2);
          this.ngModelChange.emit(this.ngModel);
          this.ren.setElementStyle(this.el.nativeElement, 'overflow', 'hidden');
         
    

       
     } 
   

   }

   @HostListener('window:keyup', ['$event'])
   keyEvent(event: KeyboardEvent) {   
      if(this.el.nativeElement.offsetHeight+1<this.el.nativeElement.scrollHeight){
        event.stopPropagation();
           this.ngModel = this.ngModel.substring(0, this.ngModel.length-2);
           this.ngModelChange.emit(this.ngModel);
           this.ren.setElementStyle(this.el.nativeElement, 'overflow', 'hidden');
      } 

   }

  




}
