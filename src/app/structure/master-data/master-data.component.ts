import { Component, OnInit, ElementRef, Renderer } from '@angular/core';
import { Router, ActivatedRoute, Params, NavigationEnd } from '@angular/router';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { StructureService } from '../structure.service';
import { MasterDataService } from './master-data.service';
import { ToolTipService } from '../../structure/tool-tip.service';
import { e } from '@angular/core/src/render3';
import { isInteger } from '@ng-bootstrap/ng-bootstrap/util/util';
import { Subject } from 'rxjs';
import { isBlank } from 'app/utils/utils';
import { DateTimePipe } from '../forms/formp.pipe';


let moment = require('moment/moment');
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var swal: any;
declare var NProgress: any;

@Component({
  selector: 'app-master-data',
  templateUrl: './master-data.component.html',
  styleUrls: ['./master-data.component.css']
})
export class MasterDataComponent implements OnInit {
  userDetails;
  masterDataTypeList;
  selectedMasterId;
  searchText;
  optionShow;
  totalMasterDataTypeCount = 0;
  statusMessage;
  hideLoadMore = true;
  masterDatam: any;
  public showHide = true;
  optionShowMenu;
  changeArrowButton = false;
  dataLoadingMsg = true;
  show_msg=false;
  startRow;
  endRow;
  totalCount;
  searchMasterData = '';
  resetMasterDataType = false;
  masterDataForm;
  fieldForm;
  dataValueForm;
  masterDataEntryForm: FormGroup;
  masterDataTypeDetails: FormGroup;
  enableMasterDataTypeEdit = true;
  s_submitted = false;
  dTable;
  dTable1;
  searchResetFlag= 0;
  masterDataFieldList = [];
  masterDataValueFields = [];
  activeFieldDetail;
  activeDataDetail;
  activeEntryDetail;
  currentFieldName;
  currentCssClass;
  currentMasterCssClass;
  currentMasterCssClassFinal;
  finalCss;
  activeGroup = null;
  datam;
  masterDataEntryFieldList = [];
  masterEntryListTemp = [];
  totalDataCount = 0;
  my_columns = [];
  fieldEdit = false;
  valueEdit = false;
  entryEdit = false;
  masterDataName;
  masterDataCss;
  masterDataFinalCss;
  showLoading;
  editSubmit;
  disableEntryButton = false;
  activeDetailTem;
  hideTable = false;
  showAlert = false;
  FieldModalLabel = 'Add New Field';
  userData = JSON.parse(this._structureService.userDetails);
  localMasterDataId;
  activeFieldName;
  siteIds : any;
  masterSiteIds : any;
  showSiteFilter : boolean;
  showSiteFilterMasterData : boolean;
  editSiteData:any;
  multiSiteEnable : boolean;
  singleSiteLength : boolean;
  dynamic=false;
  siteRequired : boolean = false;
  addEntry : boolean;
  columnVisible=[];
  siteColumnSort=[];
  eventsSubject: Subject<void> = new Subject<void>();


  constructor(
    private router: Router, 
    private _formBuild: FormBuilder,
    private route: ActivatedRoute, 
    private _structureService: StructureService, 
    private _masterDataService: MasterDataService,
    private _ToolTipService : ToolTipService,
    private dateTimePipe: DateTimePipe,
    elementRef: ElementRef,
    renderer: Renderer) {
    this.optionShow = "MasterDataDef";
    renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
      if(event.target.id == 'deleteFieldValue') {
        this.deleteFieldValue();
      } else if(event.target.id=='editFieldValue'){
        this.editFieldValue();             
      } else if(event.target.id=='editEntryValue'){
        const targetElement = $(event.target);
        const editSiteId = targetElement.data('siteid');
        const editDataId = targetElement.data('id');
        this.editEntry(editSiteId,editDataId);            
      } else if(event.target.id=='deleteEntryValue'){
        this.deleteEntry();            
      }
      if(event.target.id == 'more_recipients') {
        $("body .openmore_recipients").css("display","block");
        $("body .more-recipients").css("display","none");
        $("body .closemore_recipients").css("display","none");

        var attributes =$(event.target).attr('data-moreId');
        console.log("attributes===>"+attributes);
        $("body #"+attributes).css("display","block");
        $('table tr td').find('.more-recipients#'+attributes).attr('style','display:block')
        $("body .openmore_recipients"+attributes).css("display","none");
        $("body .closemore_recipients"+attributes).css("display","block");
      }else if(event.target.id == "closemore_recipients"){
        var attributes =$(event.target).attr('data-moreId');
        $("body #"+attributes).css("display","none");
        $('table tr td').find('.more-recipients#'+attributes).attr('style','display:none');
        $("body .openmore_recipients"+attributes).css("display","block");
        $("body .closemore_recipients"+attributes).css("display","none");
      }
    });
  }
  ngOnInit() {
    this.multiSiteEnable = this.userData.config.enable_multisite == 1 ? true : false;
    this.singleSiteLength = this.userData.mySites.length == 1 ? true : false;
    this.dynamic = (!isBlank(this.editSiteData)) ? true : false;
    $(document).ready(function () {
      $('#sidebarCollapse').on('click', function () {
        $('#sidebar').toggleClass('active');
      });
      function sticktothetop() {
        var window_top = $(window).scrollTop();
        if($('#stick-here').length){
          var top = $('#stick-here').offset().top;
        
        // alert(window_top); alert(top);
          if (window_top > top) {
              document.getElementById('stickThis' ).style.display = 'block';
              $('#stickThis').addClass('stick');
              $('#stick-here').height($('#stickThis').outerHeight());
              $('#stickThis').width($('.tab-content').outerWidth());
          } else {
              $('#stickThis').removeClass('stick');
              $('#stick-here').height(0);
                document.getElementById('stickThis' ).style.display = 'none';
          }
        }
      }
      $(function() {
        $(window).scroll(sticktothetop);
        if($('#stick-here').length){
          sticktothetop();
          }
      });
    });
    if(localStorage.getItem('masterDataTypeId')){
    localStorage.removeItem('masterDataTypeId');
    }
    this.getMasterDataList();
    var page = 'master-data';
    $(".data-type-name").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSDTA000001') });
    $(".data-type-cssClass").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSDTA000002') });
    $(".field-name").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSDTA000003') });
    $(".cssClass").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSDTA000004') });
    $(".final-cssClass").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSDTA000005') });
    $(".data-value").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSDTA000006') });
    $(".field-required").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSDTA000007') });
    $(".data-type-master-cssClass").tooltip({html:true, title: this._ToolTipService.getToolTip(page, 'MSDTA000008') });
    
    this.masterDataForm = this._formBuild.group({
      dataTypeName: ['', [Validators.required]],
      dataTypeCssClass: ['', [Validators.required]],
      dataTypeDes: [''],
      dataTypeFinalCssClass: ['']
      });
    this.fieldForm = this._formBuild.group({
      fieldName: ['', [Validators.required]],
      cssClass: ['', [Validators.required]],
      isRequired: [false],
      finalCssClass: ['']
    });
    this.dataValueForm = this._formBuild.group({
      dataValue: ['', [Validators.required]]
    });
    this.masterDataTypeDetails = this._formBuild.group({
      dataTypeName: ['', Validators.required],
      dataTypeCss: ['', Validators.required],
      dataTypeDes: [''],
      dataTypeCssFinal: ['', Validators.required]
    });

    this.masterDataEntryForm = new FormGroup({
      firstName: new FormControl()
   });
   
    $(function () {
      $('.data-type-name-form').tooltip({
        title: 'Message Escalation Behavior'
      });

    });
  }

  createNewControl(masterDataFieldList){
    let group: any = {};
    if (masterDataFieldList) {
      masterDataFieldList.forEach(fields => {
        if(fields.required == true) {
          group[fields.cssClass] = new FormControl('', Validators.compose([
            Validators.required]));
        } else {
          group[fields.cssClass] = new FormControl('');
        }
      });
    }
    return new FormGroup(group);
  }

  getCssClass(value) {
    this.currentCssClass = this.fieldForm.controls['cssClass'].value;
    this.fieldForm.patchValue({
      finalCssClass: this.masterDatam.cssClass+'--'+this.currentCssClass,
    });
  }
  getMasterCssClass(value) {
    this.currentMasterCssClass = this.masterDataForm.controls['dataTypeCssClass'].value;
    this.masterDataForm.patchValue({
      dataTypeFinalCssClass: 'mdm_'+this.currentMasterCssClass,
    });
  }
  getMasterCssClassEdit(value) {
    this.currentMasterCssClassFinal = this.masterDataTypeDetails.controls['dataTypeCss'].value;
    this.masterDataTypeDetails.patchValue({
        dataTypeCssFinal: 'mdm_'+this.currentMasterCssClassFinal,
    });
  }

  resetMasterDataForm(){
    this.masterDataForm.reset();
    this.masterDataForm.get('dataTypeFinalCssClass').setValue('mdm_');
  }

  resetMasterDataFieldForm() {
    this.fieldForm.reset();
    this.FieldModalLabel="Add New Field";
    this.s_submitted = false;
    this.activeFieldDetail = undefined;
    this.activeFieldDetail = '';
    this.fieldEdit = false;
    this.fieldForm.get('finalCssClass').setValue(this.masterDatam.cssClass+'--');
    this.masterDataCss = this.masterDatam.cssClass.replace('mdm_','');
  }

  resetMasterDataEntryForm() {
    this.masterDataEntryForm.reset();
    this.activeEntryDetail = undefined;
    this.activeEntryDetail = '';
    this.activeDetailTem = undefined;
    this.activeDetailTem = '';
    this.editSiteData = [];
    this.addEntry = true;
    this.siteRequired = false;
    this.emitEventToSelectSites('');
  }

  cancelMasterDataTypeDetails() {
    this.enableMasterDataTypeEdit = true;
    var cssClass = this.masterDatam.cssClass.replace('mdm_','');
    this.masterDataTypeDetails.patchValue({
      dataTypeName: this.masterDatam.name,
      dataTypeCss: cssClass,
      dataTypeDes: this.masterDatam.description,
      dataTypeCssFinal: this.masterDatam.cssClass
    });
    $('.editable-field-profile').prop("disabled", true);
  }

  closeDataEntryModal(){
    $('#data-entry-modal').modal('hide');
  }

  submitMasterDataType(s){
    const userDetails = JSON.parse(this._structureService.userDetails);
      if (s.valid) {
          const formData = s.value;
              const masterDataTypeFields = {
                  'name': formData.dataTypeName,
                  'description': formData.dataTypeDes,
                  'cssClass': formData.dataTypeFinalCssClass,
                  'formId': 0,
                  'tenantId': parseInt(this._structureService.getCookie('tenantId')),
                  'siteId': 0,
                  'createdBy': parseInt(userDetails.userId)
              };
              this.masterDataForm.reset();
              this.s_submitted = false;
              $('#master-data-modal').modal('hide');
              this._masterDataService.saveMasterDataType(masterDataTypeFields).then((data) => {
              if (data['createMasterDataType']['status'] == 200) {
                this.getMasterDataList();
                setTimeout(function () {
                  $.notify({ message: 'Success! Master Data Type Created' }, { type: 'success' });
                  }, 1000);
                  localStorage.removeItem('masterDataTypeId');
                  var activityData = {
                    activityName: 'Create Master Data Type',
                    activityType: 'Manage Master Data',
                    activityDescription: this.userData.displayName + ' created a new master data type with name = ' + formData.dataTypeName,
                };
                this._structureService.trackActivity(activityData);
                } else if (data['createMasterDataType']['statusMessage'] == "CSS Class exists") {
                  var IsEnterClicked=false;
                  $(document).keypress(function(event){
                      if (event.keyCode == 13) {
                          IsEnterClicked=true;
                         
                      }
                  });
                  swal({
                    title: "Warning",
                    text: "This CSS Class is already used for another Master Data Type",
                    type: "warning",
                    showCancelButton: false,
                    cancelButtonClass: "btn-default",
                    confirmButtonClass: "btn-warning",
                    confirmButtonText: 'Ok',
                    closeOnConfirm: true,
                    allowOutsideClick: false
               }, (confirm) => {
                 if(IsEnterClicked){
                   IsEnterClicked=false;
                     swal.close();   
                     return false;
                    }
                  });
                } else {
                  setTimeout(function () {
                    $.notify({ message: 'Error! Something went wrong' }, { type: 'danger' });
                    }, 1000);
                }
      });
  }
}

submitMasterDataEntry(e){
    this.addEntry = false;
    this.siteRequired = ((isBlank(this.masterSiteIds) || this.masterSiteIds=='0') && (this.multiSiteEnable && !this.singleSiteLength));
            if(this.siteRequired){
                return false;
            }
  var buttonName = document.activeElement.getAttribute("Name");
  let masterDataEntries;
  const userDetails = JSON.parse(this._structureService.userDetails);
  let array1 = e.value;
  let array2 = this.masterDataFieldList;
  if(this.activeDetailTem === undefined){
    masterDataEntries = array2.map(el => 
    ({fieldId: Number(el.id), fieldValue: array1[el.cssClass] ? array1[el.cssClass]: '', dataTypeId:this.selectedMasterId, createdBy: Number(userDetails.userId)}));
  } else {
    masterDataEntries = array2.map(el => 
    ({id: Number(this.activeDetailTem), fieldId: Number(el.id), fieldValue: array1[el.cssClass] ? array1[el.cssClass]: '', dataTypeId:this.selectedMasterId, createdBy: Number(userDetails.userId)}));
  }
    array2.forEach((value) => {
      let index = masterDataEntries.findIndex(x => x.fieldId== value['id']);
      if(value['id'] == "") {
        masterDataEntries[index].dataValue = true;
      } else {
        masterDataEntries[index].dataValue = false;
      }
    })
  if(this.activeEntryDetail === undefined){
  } else {
    array2.forEach((value) => {
    let i = masterDataEntries.findIndex(x => x.fieldId== value['id']);
    if(value['id'] != "") {
      delete masterDataEntries[i]['id'];
    }
    });
  }
    if (e.valid) {
        const formData = e.value;
            if(buttonName == "saveClose"){
              $('#master-data-field-entry-modal').modal('hide');
              this.entryEdit = false;
              }
              if(buttonName == "saveNew"){
                this.masterDataEntryForm.reset();
                $('#master-data-field-entry-modal').modal('show');
              }
            let masterDataEntriesValueList = masterDataEntries.filter(x => x.fieldValue != '');
            if(masterDataEntriesValueList.length > 0) {
                var siteParam = ((this.multiSiteEnable) ? (this.singleSiteLength ? (this.userData.mySites[0].id).toString() : this.masterSiteIds): (this.userData.mySites[0].id).toString());
            this._masterDataService.saveMasterDataEntries(masterDataEntries, siteParam).then((data) => {
            if (data['createDataEntries']['status'] == 200 && this.activeDetailTem == 0) {
              this.getMasterDataFieldEntriesList(this.selectedMasterId);
              setTimeout(function () {
                $.notify({ message: 'Success! Master Data Entries Created' }, { type: 'success' });
                }, 1000);
                var activityData = {
                  activityName: 'Create Master Data Entry',
                  activityType: 'Manage Master Data',
                  activityDescription: this.userData.displayName + ' created a new master data entry',
              };
              this._structureService.trackActivity(activityData);                
              } else {
                this.getMasterDataFieldEntriesList(this.selectedMasterId);
                setTimeout(function () {
                  $.notify({ message: 'Success! Master Data Entries Updated' }, { type: 'success' });
                  }, 1000);
                  var activityData = {
                    activityName: 'Update Master Data Entry',
                    activityType: 'Manage Master Data',
                    activityDescription: this.userData.displayName + ' updated master data entry',
                };
                this._structureService.trackActivity(activityData);   
              }
          });
        } else {
          this.showAlert = true;
          setTimeout(function () {
            $.notify({ message: 'Error! Enter atleast one field' }, { type: 'danger' });
            }, 1000);   
        }
  }
}

closeMasterDataModal() {
$('#master-data-modal').modal('hide');
this.s_submitted = false;
}

updateMasterDataTypeDetails() {
  const userDetails = JSON.parse(this._structureService.userDetails);
  const masterDataTypeFields = {
     'id': this.selectedMasterId,
     'name': this.masterDataTypeDetails.value['dataTypeName'],
     'description': this.masterDataTypeDetails.value['dataTypeDes'],
     'cssClass':this.masterDataTypeDetails.value['dataTypeCssFinal'],
     'formId': '0',
     'tenantId': this._structureService.getCookie('tenantId'),
     'siteId': '0',
     'createdBy': parseInt(userDetails.userId)
  };
  let filterObj = [];
  if(this.masterDatam.cssClass != this.masterDataTypeDetails.value['dataTypeCssFinal']) {
    var txt = 'This change will affect the form where the master data is being used and you have to change the old CSS Class with the new CSS Class in all the form fields where it is applied.';
    var IsEnterClicked=false;
    $(document).keypress(function(event){
        if (event.keyCode == 13) {
            IsEnterClicked=true;
           
        }
    });
    swal({
         title: "Are you sure, \nyou want to change the CSS Class ?",
         text: txt,
         type: "error",
         showCancelButton: true,
         cancelButtonClass: "btn-default",
         confirmButtonClass: "btn-warning",
         confirmButtonText: "Go Back",
         cancelButtonText: "Continue Anyway",
         allowOutsideClick: false,
         closeOnConfirm: true
    }, (confirm) => {
      if(IsEnterClicked){
        IsEnterClicked=false;
          swal.close();   
          return false;
      }
         if (!confirm) {
          this._masterDataService.saveMasterDataType(masterDataTypeFields).then((data) => {
          if (data['createMasterDataType']['status'] == 200) {
            this._masterDataService.getAllMasterDataType(0, 0, this.selectedMasterId,filterObj).then((data) => {
              if (data['getMasterDataType']['data']) {
                  this.masterDatam = data['getMasterDataType']['data'][0];
                  if (this.masterDatam) {
                      var cssClass = this.masterDatam.cssClass.replace('mdm_','');
                     this.masterDataTypeDetails.patchValue({
                      dataTypeName: this.masterDatam.name,
                      dataTypeCss: cssClass,
                      dataTypeDes: this.masterDatam.description,
                      dataTypeCssFinal: this.masterDatam.cssClass
                    });
                    this.selectedMasterId = this.masterDatam.id;
                  }
              }
            });
              setTimeout(function () {
                $.notify({ message: 'Success! Master Data Type Updated' }, { type: 'success' });
                }, 1000);
                this.enableMasterDataTypeEdit = true;
                this.getMasterDataList();
                $('.editable-field-profile').prop("disabled", true);
                var activityData = {
                  activityName: 'Update Master Data Type',
                  activityType: 'Manage Master Data',
                  activityDescription: this.userData.displayName + ' updated master data type with name = ' + this.masterDatam.name,
              };
              this._structureService.trackActivity(activityData);
                this.getMasterDataFieldList(this.selectedMasterId);
              } else if (data['createMasterDataType']['statusMessage'] == "CSS Class exists") {
                swal({
                  title: "Warning",
                  text: "This CSS Class is already used for another Master Data Type",
                  type: "warning",
                  showCancelButton: false,
                  cancelButtonClass: "btn-default",
                  confirmButtonClass: "btn-warning",
                  confirmButtonText: 'Ok',
                  closeOnConfirm: true,
                  allowOutsideClick: false
             }, (confirm) => {
               if(IsEnterClicked){
                 IsEnterClicked=false;
                   swal.close();   
                   return false;
                  }
                });
              }  else {
                setTimeout(function () {
                  $.notify({ message: 'Error! Something went wrong' }, { type: 'danger' });
                  }, 1000);
              }
          });
         }
    })
  } else {
    this._masterDataService.saveMasterDataType(masterDataTypeFields).then((data) => {
      if (data['createMasterDataType']['status'] == 200) {
            this._masterDataService.getAllMasterDataType(0, 0, this.selectedMasterId,filterObj).then((data) => {
            if (data['getMasterDataType']['data']) {
                this.masterDatam = data['getMasterDataType']['data'][0];
                if (this.masterDatam) {
                    var cssClass = this.masterDatam.cssClass.replace('mdm_','');
                   this.masterDataTypeDetails.patchValue({
                    dataTypeName: this.masterDatam.name,
                    dataTypeCss: cssClass,
                    dataTypeDes: this.masterDatam.description,
                    dataTypeCssFinal: this.masterDatam.cssClass
                  });
                  this.selectedMasterId = this.masterDatam.id;
                }
            }
          });
          setTimeout(function () {
            $.notify({ message: 'Success! Master Data Type Updated' }, { type: 'success' });
            }, 1000);
            this.getMasterDataList();
            this.enableMasterDataTypeEdit = true;
            $('.editable-field-profile').prop("disabled", true);
            var activityData = {
              activityName: 'Update Master Data Type',
              activityType: 'Manage Master Data',
              activityDescription: this.userData.displayName + ' updated master data type with name = ' + this.masterDatam.name,
          };
          this._structureService.trackActivity(activityData);
          }
      });
  }
}

  submitFieldDetails(f) {
    var buttonName = document.activeElement.getAttribute("Name");
    const formData = f.value;
    const userDetails = JSON.parse(this._structureService.userDetails);
      if (f.valid) {
          const formData = f.value;
          if(this.activeFieldDetail === undefined || this.activeFieldDetail.id === undefined){
            const fieldDetails = {
              'masterTypeId': this.selectedMasterId,
              'createdBy': parseInt(userDetails.userId),
              'modifiedBy':parseInt(userDetails.userId),
              'fields': formData
          };
          this.s_submitted = false;
          if(buttonName == "saveClose"){
          $('#master-data-field-modal').modal('hide');
          }
          if(buttonName == "saveNew"){
            this.fieldForm.reset();
            $('#master-data-field-modal').modal('show');
          }
          this._masterDataService.saveMasterDataTypeFields(fieldDetails).then((data) => {
          if (data['createMasterDataFields']['status'] == 200) {
            this.getMasterDataFieldList(this.selectedMasterId);
            setTimeout(function () {
              $.notify({ message: 'Success! Field Details Added' }, { type: 'success' });
              }, 1000);
              var activityData = {
                activityName: 'Create Master Data Field',
                activityType: 'Manage Master Data',
                activityDescription: this.userData.displayName + ' created master data field with name = ' + formData.fieldName,
            };
            this._structureService.trackActivity(activityData);                  
            } else if (data['createMasterDataFields']['statusMessage'] == "Field exists") {
              setTimeout(function () {
                $.notify({ message: 'Error! Field Name already exists' }, { type: 'danger' });
                }, 1000);   
            } else {
              setTimeout(function () {
                $.notify({ message: 'Error! Something went wrong' }, { type: 'danger' });
                }, 1000);   
              }
        });
        } else {
        formData.id = this.activeFieldDetail.id;
        if(formData.cssClass != this.activeFieldDetail.cssClass){
          var txt = 'This change will affect the form where the master data is being used and you have to change the old CSS Class with the new CSS Class in all the form fields where it is applied.';
          var IsEnterClicked=false;
    $(document).keypress(function(event){
        if (event.keyCode == 13) {
            IsEnterClicked=true;
           
        }
    });
    swal({
         title: "Are you sure, \nyou want to change the CSS Class ?",
         text: txt,
         type: "error",
         showCancelButton: true,
         cancelButtonClass: "btn-default",
         confirmButtonClass: "btn-warning",
         confirmButtonText: "Go Back",
         cancelButtonText: "Continue Anyway",
         allowOutsideClick: false,
         closeOnConfirm: true
    }, (confirm) => {
      if(IsEnterClicked){
        IsEnterClicked=false;
          swal.close();   
          return false;
      }
         if (!confirm) {
                  const fieldDetails = {
                    'masterTypeId': this.selectedMasterId,
                    'createdBy': parseInt(userDetails.userId),
                    'modifiedBy':parseInt(userDetails.userId),
                    'fields': formData
                    };
                    this.s_submitted = false;
                    if(buttonName == "saveClose"){
                      $('#master-data-field-modal').modal('hide');
                      this.fieldEdit = false;
                    }
                        this._masterDataService.saveMasterDataTypeFields(fieldDetails).then((data) => {
                          if (data['createMasterDataFields']['status'] == 200) {
                            this.getMasterDataFieldList(this.selectedMasterId);
                            setTimeout(function () {
                            $.notify({ message: 'Success! Field Details Updated' }, { type: 'success' });}, 1000);
                            var activityData = {
                              activityName: 'Update Master Data Field',
                              activityType: 'Manage Master Data',
                              activityDescription: this.userData.displayName + ' updated master data field with name = ' + formData.fieldName,
                          };
                          this._structureService.trackActivity(activityData);                  
                          } else {
                            setTimeout(function () {
                              $.notify({ message: 'Error! Something went wrong' }, { type: 'danger' });
                              }, 1000);   
                            }
                        });
                  } 
          })
          } else {
            const fieldDetails = {
              'masterTypeId': this.selectedMasterId,
              'createdBy': parseInt(userDetails.userId),
              'fields': formData,
              'status': 1
          };
          this.s_submitted = false;
          if(buttonName == "saveClose"){
          $('#master-data-field-modal').modal('hide');
          }
          if(buttonName == "saveNew"){
            this.fieldForm.reset();
            $('#master-data-field-modal').modal('show');
          }
          this._masterDataService.saveMasterDataTypeFields(fieldDetails).then((data) => {
          if (data['createMasterDataFields']['status'] == 200) {
            this.getMasterDataFieldList(this.selectedMasterId);
            setTimeout(function () {
              $.notify({ message: 'Success! Field Details Updated' }, { type: 'success' });
              }, 1000);
              var activityData = {
                activityName: 'Update Master Data Field',
                activityType: 'Manage Master Data',
                activityDescription: this.userData.displayName + ' updated master data field with name = ' + formData.fieldName,
            };
            this._structureService.trackActivity(activityData);                    
            } else {
            setTimeout(function () {
              $.notify({ message: 'Error! Something went wrong' }, { type: 'danger' });
              }, 1000);   
            }
        });
          }
        }
     }
  }

  closeMasterDataFieldModal(){
  this.FieldModalLabel="Add New Field";
  this.s_submitted = false;
  $('#master-data-field-modal').modal('hide');
  this.activeFieldDetail = undefined;
  this.activeFieldDetail = '';
  this.fieldEdit = false;
  }

  closeMasterDataValueModal(){
  this.s_submitted = false;
  $('#master-data-value-modal').modal('hide');
  this.activeDataDetail = undefined;
  this.activeDataDetail = '';
  this.valueEdit = false;
  }

  closeMasterDataEntryModal(){
    this.s_submitted = false;
    this.showAlert = false;
    $('#master-data-field-entry-modal').modal('hide');
    // this.getMasterDataFieldList(this.selectedMasterId);
    this.entryEdit = false;
    this.masterSiteIds = '0';
    //this.getMasterDataFieldEntriesList(this.selectedMasterId);
  }

  getMasterDataList() {
    let filterObj = [];
    let masterDataList = [];
    this.startRow = 0;
    this.endRow = 10;
    this._masterDataService.getAllMasterDataType(this.startRow, this.endRow, '', filterObj).then((data) => {
      this.dataLoadingMsg = false;
      this.masterDataTypeList = data['getMasterDataType']['data'];
      this.totalCount = data['getMasterDataType'].meta.totalcount ? data['getMasterDataType'].meta.totalcount : 0;
      this.statusMessage = "";
      if (typeof (this.totalCount) == "number") {
        this.totalDataCount = this.totalCount;
      }
      if (this.totalDataCount == 0) {
        this.hideLoadMore = true;
        this.statusMessage = "No Master Data Type Found";
      } else if (this.totalCount > 10) {
        this.hideLoadMore = false;
      }
      if ((this.startRow + this.endRow) >= this.totalDataCount) {
        this.hideLoadMore = true;
      }
      if (this.masterDataTypeList !== null) {
          if (this.masterDataTypeList.length > 0) {
            console.log('masterDataTypeId', localStorage.getItem('masterDataTypeId'));
            if(localStorage.getItem('masterDataTypeId')){
              this.localMasterDataId = localStorage.getItem('masterDataTypeId');
              this.selectedMasterId = this.localMasterDataId;
              this.updateMasterDataType(this.selectedMasterId, event);
              this.getMasterDataFieldList(this.selectedMasterId);
            } else {
              this.selectedMasterId = this.masterDataTypeList[0].id;
              this.updateMasterDataType(this.selectedMasterId, event);
              this.getMasterDataFieldList(this.selectedMasterId);
            }
          }
      }
      this.dataLoadingMsg = false;
    });
  }

  getMasterDataFieldList(masterDataId) {
    this._masterDataService.getAllMasterDataType(0, 0, masterDataId,[]).then((data) => {
      if (data['getMasterDataType']['data']) {
          this.masterDatam = data['getMasterDataType']['data'][0];
          if (this.masterDatam) {
            this.masterDataName = this.masterDatam.name;
            this.masterDataCss = this.masterDatam.cssClass.replace('mdm_','');
            this.masterDataFinalCss = this.masterDatam.cssClass;
            this.selectedMasterId = this.masterDatam.id;
          }
     
    this.masterDataFieldList = [];
    let filterObj = {
      'column': 'type_id',
      'filter': masterDataId.toString(),
      'type': 'equals',
      'filterCondition': 'OR'
    };
      this._masterDataService.getMasterDataFields(masterDataId, filterObj).then((data) => {
      this.masterDataValueFields = data['getMasterDataFields'];
      if (data && (data['getMasterDataFields']['fields']).length > 0) {
        this.disableEntryButton = false;
        this.masterDataFieldList = data['getMasterDataFields']['fields'];
        this.masterDataFieldList = JSON.parse(JSON.stringify(this.masterDataFieldList));
        this.populateMasterDataFieldList();
        this.masterDataFieldList.unshift({'id': '', 'name': this.masterDatam.name, 'required': true, 'cssClass': this.masterDatam.cssClass, 'typeId': this.masterDatam.id,'dataId': this.masterDatam.id});
        this.masterDataEntryForm = this.createNewControl(this.masterDataFieldList);
        } else {
            this.masterDataFieldList = [];
            this.populateMasterDataFieldList();
            this.disableEntryButton = true;
            }
        });
      }
    });
  }

  populateMasterDataFieldList() {
    if (this.dTable1) {
      this.dTable1.destroy();
    }
    var isTrue = false;    
    if(this.masterDataFieldList.length > 99){
      isTrue = true;
    }
  this.dTable1 = $('#fieldDetails').DataTable({
    autoWidth: false,
    "language": {
      "emptyTable": "There is no data available."
    },
    responsive: true,
    retrieve: true,
    searching: false,  
    "bPaginate": false,  
    "bInfo" : false,  
    data: this.masterDataFieldList,
    fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
      $(nRow).on('click', () => {
        this.activeFieldDetail = aData;
      });
    },
    columns: [
      {title: "#"},
      {title: "Field Name", data: 'name'},           
      {title: "CSS Class", data: 'cssClass'},
      {title: "Required", data: 'required'},
      {title: "Actions"}
    ],
    columnDefs: [
    {
      data: null,
      orderable: false,
      width: "5%",
      targets: 0
     },
     { 
      data: null,
      targets: 1,
      width: "25%"     
    },
    { 
      data: null,
      targets: 2,
      width: "25%"     
    },
    { 
      data: null,
      targets: 3,
      render: (data, type, row) => {
        if(row && row.required == 1){
          return "Yes";
        }else{
        return "No";
        }
     },      
      width: "10%"     
    },
    { 
      data: null,
      targets: 4,
      width: "35%",
      sortable: false,
      render: (data, type, row) => {        
        let actions = '';
        actions += `
        <a id="editFieldValue" href="javascript: void(0);" class="cat__core__link--underlined mr-3">
          <i id="editFieldValue" class="icmn-pencil"></i> Edit</a>
        <a id="deleteFieldValue" href="javascript: void(0);" class="cat__core__link--underlined mr-3">
          <i id="deleteFieldValue" class="icmn-cross"></i> Delete</a>
        `;
       return actions;
      }
    }]
  });

  this.dTable1.on( 'order.dt search.dt', () => {
    if(this.dTable1.column(0, {search:'applied', order:'applied'}).nodes() && this.dTable1.column(0, {search:'applied', order:'applied'}).nodes().length) {
      this.dTable1.column(0, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
          cell.innerHTML = i+1;
      });
    }
  }).draw();
  this.dataLoadingMsg=false;
  }


  deleteFieldValue(){
    this.activeFieldName = this.activeFieldDetail.name;
    const userDetails = JSON.parse(this._structureService.userDetails);
    var paramsDelete = {
      'id': this.activeFieldDetail.id,
      'type': 'field',
      'modifiedBy': parseInt(userDetails.userId)
      };
      if(this.activeFieldDetail.id != ''){
        var txt = 'This change will affect the form where the master data is being used and you have to change the CSS Class in all the form fields where it is applied.';
        var IsEnterClicked=false;
        $(document).keypress(function(event){
            if (event.keyCode == 13) {
                IsEnterClicked=true;
               
            }
        });
        swal({
          title: "Are you sure?",
          text: txt,
          type: "error",
          showCancelButton: true,
          cancelButtonClass: "btn-default",
          confirmButtonClass: "btn-warning",
          confirmButtonText: "Go Back",
          allowOutsideClick: false,
          cancelButtonText: "Continue Anyway",
          closeOnConfirm: true
          }, (confirm) => {
        if(IsEnterClicked){
          IsEnterClicked=false;
            swal.close();   
            return false;
        }
            if (!confirm) {
              this._masterDataService.deleteMasterDetail(paramsDelete).then((data)=>{
                if (data['deleteMasterDetails']['status'] == 200) {
                  this.getMasterDataFieldList(this.selectedMasterId);
                  this.activeFieldDetail = undefined;
                  this.activeFieldDetail = '';
                  var notify = $.notify('Success! Field Detail deleted');
                  setTimeout(()=>{
                    notify.update({'type': 'success', 'message': '<strong>Success! Field Detail deleted</strong>'});
                    }, 1000);
                    var activityData = {
                      activityName: 'Delete Master Data Field',
                      activityType: 'Manage Master Data',
                      activityDescription: this.userData.displayName + ' deleted master data field with name = ' + this.activeFieldName,
                  };
                  this._structureService.trackActivity(activityData);  
                    } else{
                    setTimeout(function () {
                      $.notify({ message: 'Error! Something went wrong' }, { type: 'danger' });
                      }, 1000);  
                  }
              });
            }
        }); 
      }
  }

  deleteMasterType(){
    const userDetails = JSON.parse(this._structureService.userDetails);
    var paramsDelete = {
      'id': this.selectedMasterId,
      'type': 'type',
      'modifiedBy': parseInt(userDetails.userId)
      };
        var txt = 'This change will affect the form where the master data is being used and you have to change the CSS Class in all the form fields where it is applied.';
        var IsEnterClicked=false;
        $(document).keypress(function(event){
            if (event.keyCode == 13) {
                IsEnterClicked=true;
               
            }
        });
        swal({
          title: "Are you sure?",
          text: txt,
          type: "error",
          showCancelButton: true,
          cancelButtonClass: "btn-default",
          confirmButtonClass: "btn-warning",
          confirmButtonText: "Go Back",
          allowOutsideClick: false,
          cancelButtonText: "Continue Anyway",
          closeOnConfirm: true
          }, (confirm) => {
            if(IsEnterClicked){
            IsEnterClicked=false;
            swal.close();   
            return false;
        }
            if (!confirm) {
              this._masterDataService.deleteMasterDetail(paramsDelete).then((data)=>{
                if (data['deleteMasterDetails']['status'] == 200) {
                  localStorage.removeItem('masterDataTypeId');
                  this.getMasterDataList();
                  var notify = $.notify('Success! Master Data Type deleted');
                  setTimeout(()=>{
                    notify.update({'type': 'success', 'message': '<strong>Success! Master Data Type deleted</strong>'});
                    }, 1000);
                    var activityData = {
                      activityName: 'Delete Master Data Type',
                      activityType: 'Manage Master Data',
                      activityDescription: this.userData.displayName + ' deleted master data type with name = ' + this.masterDatam.name,
                  };
                  this._structureService.trackActivity(activityData);
                    } else{
                    setTimeout(function () {
                      $.notify({ message: 'Error! Something went wrong' }, { type: 'danger' });
                      }, 1000);  
                  }
              });
            }
        }); 
  }

  editFieldValue() {
    this.masterDataCss = this.masterDatam.cssClass.replace('mdm_','');
    let cssClass;
    let newCssClass;
    this.fieldEdit = true;
    if(this.activeFieldDetail === undefined || this.activeFieldDetail.id === undefined){
      this.FieldModalLabel="Add New Field";
    } else {
      this.FieldModalLabel="Edit Field";
    }
    this.fieldForm.controls['fieldName'].setValue(this.activeFieldDetail.name);
    this.fieldForm.controls['isRequired'].setValue(this.activeFieldDetail.required);
    this.fieldForm.get('finalCssClass').setValue(this.activeFieldDetail.cssClass);
    cssClass = this.activeFieldDetail.cssClass;
    newCssClass = cssClass.split('--').pop();
    this.fieldForm.controls['cssClass'].setValue(newCssClass);
    $('#master-data-field-modal').modal({ backdrop: 'static', keyboard: false });
  }

  loadMoreData() {
    if ((this.masterDataTypeList.length - 1) < this.totalCount) {
      this.startRow = this.startRow + this.endRow;
      this.endRow = this.endRow + this.endRow;
      // this.endRow = this.startRow + this.endRow;
      let masterDataTypeList = [];
      let filterObj = [];
      this.hideLoadMore = true;
      this._masterDataService.getAllMasterDataType(this.startRow, this.endRow, '', filterObj).then((data) => {
        this.hideLoadMore = false;
        if ((this.startRow + this.endRow) >= this.totalMasterDataTypeCount) {
          this.hideLoadMore = true;
        }
        masterDataTypeList = JSON.parse(JSON.stringify(data['getMasterDataType']['data']));
        masterDataTypeList = masterDataTypeList.filter(item => {
          if (item && item.name && (item.name).toLowerCase().indexOf('cancelled') == -1) {
            return true;
          }
        });
        let allList = this.masterDataTypeList;
        this.masterDataTypeList = allList.concat(masterDataTypeList);
      });
    }
  }
  searchMasterDataType(searchText) {
    this.resetMasterDataType = false;
    this.show_msg=false;
    this.searchMasterData = searchText;
    this.hideLoadMore = true;
    let masterDataList = [];
    this.startRow = 0;
    let finalFilterModel = {};
    if (this.searchMasterData != '') {
      finalFilterModel= [{ column: 'name', filter: this.searchMasterData, type: 'contains', filterCondition: 'OR' },
                         { column: 'cssClass', filter: this.searchMasterData, type: 'contains', filterCondition: 'OR' }];
      
    }
    this._masterDataService.getAllMasterDataType(this.endRow, this.startRow, '', finalFilterModel).then((data) => {
      this.hideLoadMore = false;
      this.statusMessage = '';
      this.totalCount = data['getMasterDataType'].meta.totalcount ? data['getMasterDataType']['meta'].totalcount : 0;
      if (typeof (this.totalCount) == 'number') {
        this.totalMasterDataTypeCount = this.totalCount;
      }
      if (this.totalMasterDataTypeCount == 0 || this.totalCount == null) {
        this.hideLoadMore = true;
        this.statusMessage = 'No Master Data Type Found';
        this.resetMasterDataType = true;
      } else if (this.totalMasterDataTypeCount > 10) {
        this.hideLoadMore = false;
        this.resetMasterDataType = false;
        this.show_msg=true;
      }
      else if (this.totalMasterDataTypeCount > 1) {	
        this.resetMasterDataType = false;	
        this.show_msg=true;	
      }
      if ((this.startRow + this.endRow) >= this.totalMasterDataTypeCount) {
        this.hideLoadMore = true;
  
      }
      masterDataList = JSON.parse(JSON.stringify(data['getMasterDataType']['data']));
      masterDataList = masterDataList.filter(item => {
        if (item && item.name && (item.name).toLowerCase().indexOf('cancelled') == -1) {
          return true;
        }
      });
      this.masterDataTypeList = masterDataList;
      if (this.totalMasterDataTypeCount == 1 )	
      {	
        this.resetMasterDataType = true;	
      if(this.masterDataTypeList && this.masterDataTypeList[0] && this.masterDataTypeList[0].id)	
      this.updateMasterDataType(this.masterDataTypeList[0].id,event);	
      }
    });
  }
  showTabData(data) {
    if (data == "MasterDataDef") {
    this.getMasterDataFieldList(this.selectedMasterId);
    this.enableMasterDataTypeEdit = true;
    $('.editable-field-profile').prop("disabled", true);
    this._structureService.resetDataTable();
    } else {
   // this.getMasterDataFieldEntriesList(this.selectedMasterId);
    }
    this.optionShow = data;
  }
  updateMasterDataTypeForm() {
    this.enableMasterDataTypeEdit = false;
    $('.editable-field-profile').prop("disabled", false);
  }
  changeArrow() {
    this.changeArrowButton = !this.changeArrowButton;
    if (this.changeArrowButton == true) {
      $('.custom-content').addClass('custom-search-width');
    }
    if (this.changeArrowButton == false) {
      $('.custom-content').removeClass('custom-search-width');
    }
    $('#sidebar').toggleClass('active');
  }

  updateMasterDataType(masterDataId, event) {
    this._structureService.resetDataTable();
    let filterObj = [];
    this.resetMasterDataType=false;	
    this.show_msg=false;
    $('.editable-field').prop("disabled", true);
    localStorage.setItem('masterDataTypeId',masterDataId);
    if( this.optionShow = "MasterDataDef") {
    this.getMasterDataFieldList(masterDataId);
    this._masterDataService.getAllMasterDataType(0, 0, masterDataId,filterObj).then((data) => {
      if (data['getMasterDataType']['data']) {
          this.masterDatam = data['getMasterDataType']['data'][0];
          if (this.masterDatam) {
              this.masterDatam.cssClass.replace('mdm_','');
             var cssClass = this.masterDatam.cssClass.replace('mdm_','');
             this.masterDataTypeDetails.patchValue({
              dataTypeName: this.masterDatam.name,
              dataTypeCss: cssClass,
              dataTypeDes: this.masterDatam.description,
              dataTypeCssFinal: this.masterDatam.cssClass
            });
            this.selectedMasterId = this.masterDatam.id;
          }
      }
    });
    this.enableMasterDataTypeEdit = true;
    $('.editable-field-profile').prop("disabled", true);
  } else {
    this.getMasterDataFieldEntriesList(masterDataId);
  }
  }

  populateMasterDataFieldEntryList(): void{
    (this.multiSiteEnable) ? ((this.singleSiteLength) ? this.columnVisible=[0,1,3] : this.columnVisible=[0,1]) : this.columnVisible=[0];
    (this.multiSiteEnable) ? ((this.singleSiteLength) ? this.siteColumnSort=[0] : this.siteColumnSort=[3]) : this.siteColumnSort=[0];
    this.dataLoadingMsg = true;
    var self = this;
		let datas: any;
    let len = this.my_columns.length;
    if (this.dTable) {
      this.dTable.destroy();
    }else{
      this._structureService.resetDataTable();
    }
    var isTrue = false;    
    if(this.masterDataEntryFieldList.length > 99){
      isTrue = true;
    }
      this.dTable = $('#masterDataEntries').DataTable({
        autoWidth: false,
        responsive: false,
        bprocessing: true,
        bServerSide: true,
        bpagination: true,
        bsorting: true,
        retrieve: true,
        bInfo: true,
        scrollX: true,
        scrollY: 900,
        bsearching: true,
        stateSave: true,
        columns: this.my_columns,
        fixedColumns: true,
        lengthMenu: [ [ 25, 50 ], [ 25, 50 ] ],
        fnDrawCallback: function(oSettings) {
          if (
            oSettings._iRecordsTotal == 0 ||
            oSettings._iRecordsTotal < oSettings._iDisplayLength ||
            oSettings.aoData.length == 0
          ) {
            $('.dataTables_paginate').hide();
          } else {
            $('.dataTables_paginate').show();
          }
          if (oSettings.aoData.length == 0) {
            $('.dataTables_info').hide();
          }
        },
        fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
          $(nRow).on('click', () => {
            this.activeEntryDetail = aData;
          });
        },
        dom:
        "<'row'<'col-sm-4'l><'col-sm-4'f><'col-sm-2 searchButton'>>" +
        "<'row'<'col-sm-12'tr>>" +
        "<'row'<'col-sm-5'i><'col-sm-7'p>>",
          buttons: [
          {
            extend: 'excel',
            text: 'All Pages',
            title: 'Master Data entry List',
            exportOptions: {
            columns:[1,2,3,4,5,6]
            },
            action: function ( e, dt, node, config ) {
            var selfButton = this;
            var oldStart = dt.settings()[0]._iDisplayStart;
            dt.one('preXhr', function (e, s, data) {
              data.start = 0;
              data.length = 2147483647;
              dt.one('preDraw', function (e, settings) {
                $.fn.dataTable.ext.buttons.excelHtml5.action.call(selfButton, e, dt, node, config);
                dt.one('preXhr', function (e, s, data) {
                  settings._iDisplayStart = oldStart;
                  data.start = oldStart;
                });
                setTimeout(dt.ajax.reload, 0);
                return false;
              });
            });
            dt.ajax.reload();
            }
        }],
        initComplete: function(){
          $('.dataTables_filter label input').attr('placeholder','Search');
          $('.dataTables_filter label input').attr('id','search_area');
          $('.dataTables_filter label input').unbind();
          $("div.dataTables_filter input").on('keydown', function(e) {
          if (e.which == 13) {
            self.searchResetFlag = 0;
            var value = $("div.dataTables_filter input").val();
            if(value)
            {
            value = value.replace('”','"');
            value = value.replace("‘","'");
            value = value.replace("’","'");
            self.dTable.search(value).draw();
          }
          else
          {
            self.dTable.search('').draw();
            }
          }
          });
          $("div.dataTables_filter input").on('keypress', function(e) {
          $(".SearchEntryView").prop('disabled', false);
          });
          $("div.dataTables_filter input").on('keyup', function(e) {
          var value = $("div.dataTables_filter input").val();
          if(value)
          {
          }
          else
          $(".SearchEntryView").prop('disabled', true);
          });
          $("div.searchButton")
          .html('<button disabled="true" class="btn btn-sm btn-info SearchEntryView" title="Search" type="submit">Search</button>'+
          '<button style="margin-left:10px;" class="btn btn-sm btn-default resetBView" title="Reset" type="submit">Reset</button>');
          var value =  $("div.dataTables_filter input").val();
          if(value)
          {
          $(".SearchEntryView").prop('disabled', false);
          }
          $("div.dataTables_filter input").on('paste', function(event) {
          var element = this;
          var text ;
          setTimeout(function () {
            text = $(element).val();
            if(text)
            {
            $(".SearchEntryView").prop('disabled', false);
            }
          }, 100);
          });
          $(".buttons-collection").click(function(event) {
            setTimeout(function () {
              if($(".buttons-collection").attr("aria-expanded") === "true" && $(".dt-button-collection").find("button").length == 0){
              $(".dt-button-collection").remove();
              $(".dt-button-background").remove();
              $(".buttons-collection").attr("aria-expanded","false");
              }
            },500);
          });
          },
        ajax: function(dat, callback, settings) {
          let orderData;
          let searchText;
          let orderby;
          let limit;
          let label = '';
  
          var i = dat.order[0].column ? dat.order[0].column : '';
          orderby = dat.order[0].dir ? dat.order[0].dir : '';
          if (isInteger(i)) {
            orderData = dat.columns[i].data ? dat.columns[i].data : '';
            orderData !='createdAt' ? orderData = 'name' : '';
          } else {
            orderData = 'name';
           }
          searchText = dat.search.value ? dat.search.value : '';
          if(searchText &&  self.searchResetFlag == 0){
            settings.oAjaxData.search.value = searchText;
            self.searchResetFlag = 1;
          }
          var siteParamList = (self.multiSiteEnable ? (self.singleSiteLength ? ((self.userData.mySites[0].id).toString()) : self.siteIds) : (self.userData.mySites[0].id).toString());
          self._masterDataService
            // .getMasterDataFieldEntries(self.selectedMasterId, dat.start, i, searchText, orderData, orderby)
            .getMasterDataFieldEntries(self.selectedMasterId, dat.start, dat.length, searchText, orderData, orderby,siteParamList)
            .then((resultData) => {
              self.dataLoadingMsg = false;
              datas = {};
              self.datam = {};
              if (dat.start == 0) {
                this.totalCt = resultData['totalCount'];
              }
              datas = [];
              datas = resultData['data']
                ? resultData['data']
                : [];
              
                self.masterDataEntryFieldList = datas;
                self.masterDataEntryFieldList.forEach((val) => {
                  if(self.multiSiteEnable && !self.singleSiteLength){
                       val.createdAt = self.dateTimePipe.transform(val.createdAt);
                       if(val.siteName){
                        let siteName = val.siteName.split(',');
                             var siteNam = ""
                             var labelHtml="";
                             var count =val.siteName.split(',').length;
                               for(let k=0;k<count;k++){
                                   siteNam=siteName[k];
                                   if(k==0){
                                    labelHtml +=siteNam;
                                  }
                                   if(count>1 && k==1){
                                       labelHtml+=`<a class="pull-right btn btn-sm openmore_recipients openmore_recipients`+val.dataId+`" href="javascript: void(0);" title="More recipients" data-moreId=`+val.dataId+` id="more_recipients"  (click)="moreRecipients()"><i id="more_recipients" data-moreId=`+val.dataId+` class="fa fa-plus" aria-hidden="true"></i></a>`;
                                       labelHtml+=`<a class="pull-right btn btn-sm closemore_recipients closemore_recipients`+val.dataId+`" href="javascript: void(0);" title="Close recipients" style="display:none" data-moreId=`+val.dataId+` id="closemore_recipients"  (click)="moreRecipients()"><i id="closemore_recipients" data-moreId=`+val.dataId+` class="fa fa-close" aria-hidden="true"></i></a>`;
                                       labelHtml+=`<div class="more-recipients" style="display:none" id="`+val.dataId+`">`;
                                     }
                                     if(k>=1){
                                       labelHtml+=`<label>`+siteNam+`</label>`;
                                      }
                                      if(k==(count-1)){
                                       labelHtml+=`</div>`;
                                      }
                                      val.siteName = labelHtml;
                               }
                            }else{
                                val.siteName =  '';
                            }
                   }else if((self.multiSiteEnable && self.singleSiteLength) || !self.multiSiteEnable){
                      val.createdAt = self.dateTimePipe.transform(val.createdAt);  
                   }
                });
              let draw;
              let total;
              if (datas && datas.length == 0 && searchText == '') {
                draw = 0;
                total = 0;
              } else {
                draw = dat.draw;
                total = this.totalCt;
              }
  
              self.datam = {
                draw: draw,
                recordsTotal: total,
                recordsFiltered: total,
                aaData: self.masterDataEntryFieldList
              };
              callback(self.datam);
            });
        },
        
    "aoColumnDefs": [
      {
        "aTargets": [-1], 
        "bSortable": false,
        "mRender": function (a, b, data, d) {
              return "<a id='editEntryValue' data-siteid="+data.siteId+" data-id="+data.dataId+" href='javascript: void(0);' class='cat__core__link--underlined mr-3'>" +
                "<i id='editEntryValue' class='icmn-pencil'></i> Edit</a>" +
              "<a id='deleteEntryValue' href='javascript: void(0);' class='cat__core__link--underlined mr-3'>" +
              "<i id='deleteEntryValue' class='icmn-cross'></i> Delete</a>"
            
        }
    },
    { "sClass": "dpass", "aTargets": this.columnVisible},
    { "sClass": "wpass", "aTargets": [ -1 ]},
    { "aTargets": this.siteColumnSort, "bSortable":false}]
	  

  });
  this.dataLoadingMsg=false;
     $('#staff-list_filter label input').attr('id', 'search_area');
		$('#masterDataEntries').on('click', 'tr .child', function (e) {
			var parentRow = $(this).closest("tr").prev()[0];
			self.activeEntryDetail = self.dTable.row( parentRow ).data();
});
		$(document).on('click', '.resetBView',(event)=> {
           // self.getMasterDataFieldEntriesList(self.selectedMasterId);
            self.dTable.search('').draw();
			$(".SearchEntryView").prop('disabled', true);
		  });
		  $(document).on('click', '.SearchEntryView',(event)=> {
		  var value = $('#masterDataEntries_wrapper #masterDataEntries_filter label input').val();
		  if(value)
		  {
		  value = value.replace('”','"');
		  value = value.replace("‘","'");
          value = value.replace("’","'");
          self.searchResetFlag = 0;
		  self.dTable.search(value).draw();
		  }
		  else
		  {
			self.dTable.search('').draw();
		  }
		   });
  }
  
  deleteEntry(){
    const userDetails = JSON.parse(this._structureService.userDetails);
    swal({
      title: "Are you sure?",
      text: "You are going to remove this entry",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      allowOutsideClick: false,
      confirmButtonText: "Ok",
      closeOnConfirm: true
    },(confirm) => {
      if(confirm) {
      var paramsDelete = {
        'id': this.activeEntryDetail.dataId,
        'type': 'entry',
        'modifiedBy': parseInt(userDetails.userId)
      };
      this._masterDataService.deleteMasterDetail(paramsDelete).then((data)=>{
        if (data['deleteMasterDetails']['status'] == 200) {
          this.getMasterDataFieldEntriesList(this.selectedMasterId);
          var notify = $.notify('Success! Entry Value deleted');
          setTimeout(()=>{
              notify.update({'type': 'success', 'message': '<strong>Success! Entry Value deleted</strong>'});            
          }, 1000);
          var activityData = {
            activityName: 'Delete Master Data Entry',
            activityType: 'Manage Master Data',
            activityDescription: this.userData.displayName + ' deleted master data entry',
        };
        this._structureService.trackActivity(activityData);
        }
        else{
          setTimeout(function () {
            $.notify({ message: 'Error! Something went wrong' }, { type: 'danger' });
            }, 1000);  
        }
      });
    }
    }) 
  }
  editEntry(editSiteId: any="", editDataId: any=""){
    this.entryEdit = true;
    this.activeDetailTem = this.activeEntryDetail.dataId;
    var tempItem = this.masterDataEntryFieldList;
    if(!isBlank(this.activeDetailTem)){
       var newItem = tempItem.find(item => item.dataId === this.activeEntryDetail.dataId);
    }else{
        var temp = this.masterEntryListTemp;
        var newItem = this.masterEntryListTemp.find(item => item.dataId === editDataId);
        this.masterEntryListTemp = temp;
    }
    if(this.activeEntryDetail && this.activeEntryDetail.siteId){
        this.editSiteData = this.activeEntryDetail.siteId.split(',').map(Number);
    }else if(editSiteId!=""){
          if(typeof(editSiteId) == 'number'){
              editSiteId = (editSiteId.toString()).split(',').map(Number);
          }else{
            this.editSiteData = editSiteId.split(',').map(Number);
          }
    }else{
        this.editSiteData = [];
    }
    this.dynamic = true;
    delete newItem['dataId'];
    delete newItem['siteId'];
    delete newItem['siteName'];
    delete newItem['createdAt'];
    this.masterDataEntryForm.setValue(newItem);
    newItem['dataId'] = editDataId;
    newItem['siteId'] = (typeof(editSiteId) == 'number') ? editSiteId.toString(): editSiteId;
    newItem['siteName'] = '';
    newItem['createdAt'] = '';
    $('#master-data-field-entry-modal').modal({ backdrop: 'static', keyboard: false });
  }
  emitEventToSelectSites(status): void {
    this.eventsSubject.next(status);
  }
  
  getMasterDataFieldEntriesList(masterDataId) {
    this.startRow = 0;
    this.endRow = 10;
    var siteParamList = (this.multiSiteEnable ? (this.singleSiteLength ? ((this.userData.mySites[0].id).toString()) :  this.siteIds) : (this.userData.mySites[0].id).toString());
    this._masterDataService.getMasterDataFieldEntries(masterDataId, this.startRow, this.endRow, '','', '', siteParamList).then((result: any) => {
      this.my_columns = [];
      let keys = Object.keys(result['header']);
      let values = result['header'];
      keys.forEach(element => {
      this.my_columns.push({'data': values[element], 'title': element});
      });
      this.my_columns.push({'data': 'Actions', 'title': 'Actions'})
      this.masterDataEntryFieldList = result['data'];
      this.masterEntryListTemp = this.masterDataEntryFieldList;
    if(result['data'].length > 0){
      this.hideTable = false;
      this.populateMasterDataFieldEntryList();
    } else {
      this.hideTable = true;
    }
    });
  }
  getSiteIds(data) : any{
  this.siteIds = data['siteId'].toString();
   this.getMasterDataFieldEntriesList(this.selectedMasterId);
  }
  getSiteIdsMasterData(data) : any {
      this.masterSiteIds = data['siteId'].toString();
  this.siteRequired = ((isBlank(this.masterSiteIds) || this.masterSiteIds=='0') && !this.addEntry);
  }
  hideDropdown(hideItem) : any {
      this.showSiteFilter = hideItem.hideItem;
  }
  hideDropdownMasterData(hideItem) : any {
    this.showSiteFilterMasterData = hideItem.hideItem;
}

}
