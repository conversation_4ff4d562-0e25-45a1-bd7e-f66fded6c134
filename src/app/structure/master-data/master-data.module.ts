import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { MasterDataComponent } from './master-data.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '../shared/sharedModule';
import { AuthGuard } from 'app/guard/auth.guard';

export const routes: Routes = [
  { path: 'master-data/manage', component: MasterDataComponent , canActivate:[AuthGuard], data:{ checkRoutingPrivileges : 'manageMasterDataForms'} },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    RouterModule.forChild(routes)
  ],
  declarations: [
    MasterDataComponent
  ]

})

export class  MasterDataModule { }