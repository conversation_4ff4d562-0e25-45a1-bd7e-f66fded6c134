import { Injectable, OnInit } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import { Subject } from 'rxjs/Subject';
import { ApolloClient, createNetworkInterface } from 'apollo-client';
import { Apollo } from 'apollo-angular';
import { StructureService } from '../structure.service';
import 'rxjs/add/operator/toPromise';
import { Router, ActivatedRoute, Params, NavigationEnd } from '@angular/router';
import gql from 'graphql-tag';
declare var NProgress: any;
declare var $: any;
declare var jQuery: any;
enum masterTypesEnum {
  type,
  field,
  value,
  entries
};

const getMasterDataType = gql`
query getMasterDataType(
  $sessionToken: String
  $startRow: Int
  $endRow: Int
  $id: ID
  $sorting: [sortArray]
  $filter: [FilterModel]
  $tenantIds: [Int]
  ) {
    getMasterDataType(sessionToken:$sessionToken, startRow:$startRow, endRow:$endRow, id:$id, sorting:$sorting, filter:$filter, tenantIds:$tenantIds) 
    {
        data {
          id 
          name 
          description
          cssClass
        }
        meta {
          totalcount
          totalPages
          hasNextPage
          rowsPerPage
        }
    }
  }  
`;

const getMasterDataField = gql`
query getMasterDataFields(
  $sessionToken: String
  $id: ID
  $sorting: [sortArray]
  $filter: [FilterModel]
  ) {
    getMasterDataFields(sessionToken:$sessionToken, id:$id, sorting:$sorting, filter:$filter)
    {
        fields {
          id
          name
          required
          cssClass
          typeId
          dataId
        }
        dataValue {
          id
          name
        }
        status
        statusMessage
    } 
  }
`;

const getMasterDataValue = gql`
query getMasterDataValues(
  $sessionToken: String
  $startRow: Int
  $endRow: Int
  $id: ID
  $sorting: [sortArray]
  $filter: [FilterModel]
  $dataTypeId: Int
  ) {
    getMasterDataValues(sessionToken:$sessionToken, startRow:$startRow, endRow:$endRow, id:$id, sorting:$sorting, filter:$filter, dataTypeId:$dataTypeId) 
    {
        data {
          id 
          name
          status
          typeId
          createdAt
        }
        meta {
          totalcount
          totalPages
          hasNextPage
          hasPreviousPage
          rowsPerPage
        }
    }
  }  
`;


const createMasterDataType =  gql`
mutation createMasterDataType(
  $sessionToken: String
  $params: MasterDataTypeInput!
) {
  createMasterDataType(sessionToken:$sessionToken, params:$params)
  {
      status
      statusMessage
  }
}
`;

const createMasterDataFields =  gql`
mutation createMasterDataFields(
  $sessionToken: String
  $params: MasterDataFieldInput!
) {
  createMasterDataFields(sessionToken:$sessionToken, params:$params)
  {
      status
      statusMessage
  }
}
`;

const createMasterDataValues =  gql`
mutation createMasterDataValues(
  $sessionToken: String
  $params: masterDataValue
) {
  createMasterDataValues(sessionToken:$sessionToken, params:$params)
  {
      status
      statusMessage
  }
}
`;

const createMasterDataEntries =  gql`
mutation createDataEntries(
  $sessionToken: String,
  $siteIds: String!,
  $params: [masterDataInput!]
) {
  createDataEntries(sessionToken:$sessionToken ,siteIds:$siteIds,params:$params)
  {
      status
      statusMessage
  }
}
`;

const deleteMasterDetail = gql`
mutation deleteMasterDetails(
  $sessionToken: String
  $entitytype: masterTypesEnum!
  $id: [ID!]!
  $modifiedBy: Int!
  ) {
    deleteMasterDetails(sessionToken:$sessionToken, entitytype:$entitytype, id:$id, modifiedBy:$modifiedBy)
   {
       status
       statusMessage
   }
  }
`;

@Injectable()
export class MasterDataService {
    constructor(
        private _http: Http,
        private router: Router,
        public _structureService: StructureService,
        private apollo: Apollo
    ) {
    }

    saveMasterDataType(params) {
      const variables = {
        sessionToken: this._structureService.getCookie('authID'),
        params: {
          "name": params.name,
          "description": params.description ? params.description: '',
          "cssClass": params.cssClass,
          "formId": parseInt(params.formId),
          "tenantId": parseInt(params.tenantId),
          "siteId": parseInt(params.siteId),
          "createdBy": parseInt(params.createdBy)
        }
        };
        if(params.id != ''){
          variables['params']['id'] = params.id
        }
        const apiConfig = {
          method: 'POST',
          data: createMasterDataType,
          variables: variables,
          requestType: 'gql',
          use: '',
        };
        const response = this._structureService.requestData(apiConfig);
        return response;
    }

    updateMasterDataType(params) {
      const variables = {
        sessionToken: this._structureService.getCookie('authID'),
        params: {
          "id": params.id,
          "name": params.name,
          "description": params.description ? params.description: '',
          "cssClass": params.cssClass,
          "formId": params.formId,
          "tenantId": params.tenantId,
          "siteId": params.siteId,
          "createdBy": parseInt(params.createdBy)
        }
        };
        const apiConfig = {
          method: 'POST',
          data: createMasterDataType,
          variables: variables,
          requestType: 'gql',
          use: '',
        };
        const response = this._structureService.requestData(apiConfig);
        return response;
     
    }

  getAllMasterDataType(startRow,endRow,dataTypeId,filterModel){
    let sortArray = {
      'colId':'id',
      'sort': 'desc'
    };
    let variables: any = {
      sessionToken: this._structureService.getCookie('authID'),
      tenantIds: parseInt(this._structureService.getCookie('tenantId')),
      startRow: startRow,
      endRow: endRow,
      sorting: sortArray,
      filter: filterModel
    };
    if(dataTypeId != ''){
      variables['id'] = dataTypeId
    }
    const apiConfig = {
      method: 'GET',
      data: getMasterDataType,
      variables: variables,
      requestType: 'gql',
      use: '',
    };
    return this._structureService.requestData(apiConfig);
  }

  saveMasterDataTypeFields(params){
    let fields;
    if(params['fields'].id){
      fields= {
        "id": params['fields'].id,
        "name": params['fields'].fieldName,
        "cssClass": params['fields'].finalCssClass,
        "required": params['fields'].isRequired == true ? true : false,
        "typeId": params.masterTypeId,
        "createdBy": parseInt(params.createdBy),
        "modifiedBy":parseInt(params.modifiedBy)
        }
    } else {
      fields= {
        "name": params['fields'].fieldName,
        "cssClass": params['fields'].finalCssClass,
        "required": params['fields'].isRequired == true ? true : false,
        "typeId": params.masterTypeId,
        "createdBy": parseInt(params.createdBy),
        "modifiedBy": parseInt(params.modifiedBy)
        }
    }
      const variables = {
        sessionToken: this._structureService.getCookie('authID'),
        params: fields
        };
      console.log(variables);
      const apiConfig = {
        method: 'POST',
        data: createMasterDataFields,
        variables: variables,
        requestType: 'gql',
        use: '',
      };
      const response = this._structureService.requestData(apiConfig);
      return response;
  
  }

  saveMasterDataValues(params){
    let values;
    if(params['values'].id){
      values= {
        "id": params['values'].id,
        "name": params['values'].dataValue,
        "typeId": params.masterTypeId,
        "status": params.status,
        "createdBy": params.createdBy
        }
    } else {
      values= {
        "name": params['values'].dataValue,
        "typeId": params.masterTypeId,
        "status": params.status,
        "createdBy": parseInt(params.createdBy)
        }
    }
    const variables = {
      sessionToken: this._structureService.getCookie('authID'),
      params: values
      };
    console.log(variables);
    const apiConfig = {
      method: 'POST',
      data: createMasterDataValues,
      variables: variables,
      requestType: 'gql',
      use: '',
    };
    const response = this._structureService.requestData(apiConfig);
    return response;
  }

  saveMasterDataEntries(params, siteId=0){
    const variables = {
      sessionToken: this._structureService.getCookie('authID'),
      siteIds : siteId,
      params: params
      };
    console.log(variables);
    const apiConfig = {
      method: 'POST',
      data: createMasterDataEntries,
      variables: variables,
      requestType: 'gql',
      use: '',
    };
    const response = this._structureService.requestData(apiConfig);
    return response;
  }

  getMasterDataFields(id, filterModel) {
    let sortArray = [];
    let variables: any = {
      sessionToken: this._structureService.getCookie('authID'),
      sorting: sortArray, 
      filter: filterModel
    };
    const apiConfig = {
      method: 'GET',
      data: getMasterDataField,
      variables: variables,
      requestType: 'gql',
      use: '',
    };
    return this._structureService.requestData(apiConfig);
  }

  getMasterDataValuesList(masterTypeId,endRow,startRow, filterModel) {
      let sortArray = [];
      let variables: any = {
        sessionToken: this._structureService.getCookie('authID'),
        startRow: startRow,
        endRow: endRow,
        sorting: sortArray, 
        filter: filterModel
      };
      const apiConfig = {
        method: 'GET',
        data: getMasterDataValue,
        variables: variables,
        requestType: 'gql',
        use: '',
      };
      return this._structureService.requestData(apiConfig);
  }

  deleteMasterDetail(params) {
    console.log(params);
    const variables = {
      sessionToken: this._structureService.getCookie('authID'),
      id: params.id,
      modifiedBy: parseInt(params.modifiedBy)
      };
      if(params.type == 'field'){
        variables['entitytype'] = masterTypesEnum[1]
      } else if(params.type == 'value'){
        variables['entitytype'] = masterTypesEnum[2]
      } else if(params.type == 'entry'){
        variables['entitytype'] = masterTypesEnum[3]
      } else if(params.type == 'type'){
        variables['entitytype'] = masterTypesEnum[0]
      }
      console.log(variables);
      const apiConfig = {
        method: 'POST',
        data: deleteMasterDetail,
        variables: variables,
        requestType: 'gql',
        use: '',
      };
      const response = this._structureService.requestData(apiConfig);
      return response;
  }
  
  getMasterDataFieldEntries(masterTypeId, startRow, endRow, searchText, orderData, orderby,siteIds=0) {
    let params: any = {
      "masterTypeId": masterTypeId,
      "type": "masterDataEntries",
      "operation": "list",
      "limit": endRow,
      "offset": startRow,
      "searchText": searchText ? searchText : '',
      "orderData": orderData ? orderData : '',
      "orderby":orderby ? orderby : '',
      "siteIds" : siteIds
    }
    let headers = {
			"Authentication-Token": this._structureService.getCookie('authID'),
			"Access-Control-Allow-Origin": "*"
		}
    const getMasterDataEntries = this._structureService.apiBaseUrl + "citus-health/v4/manage-master-data.php";
    var apiConfig = { url: getMasterDataEntries, method: 'POST', data: params, headers: headers, requestType: 'http' };
    return this._structureService.requestData(apiConfig);
  }

}
