<style>
    
    img#profileImageSmall {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .information-edit i {
        float: right;
        cursor: pointer;
        color: #0190fe;
        width: 30px;
        height: 30px;
        text-align: center;
        border-radius: 50%;
        padding-top: 6px;
    }
    
    .personal-information-edit-icon {
        float: left;
        position: absolute;
        padding-left: 5px;
        top: 90px;
    }
    
    .information-edit i:hover {
        background: #0190fe !important;
        color: #fff;
    }
    
    .personal-info-title {
        height: 35px;
    }
    
    .personal-info-submit {
        display: none;
    }
 
    .cat__apps__profile__card {
        border: none;
        background: #fff !important;
        margin-top: -8.58rem;
        background-size: cover;
        padding: 2.5rem .5rem .5rem 0.5rem;
    }
    
    .pah-icon-set {
        width: 40px;
        height: 40px;
        display: block;
        text-align: center;
        color: #fff;
        padding-top: 10px;
    }
    
    .pah-messages {
        background: rgb(184, 19, 19);
        cursor: pointer;
    }
    
    .pah-other {
        background: rgb(223, 188, 33);
        cursor: pointer;
    }
    
    .pah-form {
        background: rgb(5, 160, 64);
        cursor: pointer;
    }
    
    .pah-inventory {
        background: rgb(235, 84, 39);
        cursor: pointer;
    }
    
    .pah-videocommunication {
        background: rgb(70, 34, 39);
        cursor: pointer;
    }
    
    .outer-patients {
        height: 662px;
        overflow-y: scroll;
    }
    
    .pah-dropdown {
        float: right;
        position: relative;
    }
    
    .pah-dropdown .applyBtn {
        margin-left: 10px;
    }
    
    .pah-dropdown .applyBtn:hover {
        background-color: #46be8a !important;
        border-color: #46be8a !important
    }
    
    .dropdown-toggle::after {
        display: none !important;
    }
    
    .pah-dropdown ul {
        margin-right: 100px !important;
        right: -117px !important;
        padding: 10px !important;
    }
    
    .pah-dropdown label {
        display: block;
    }
    
    .pah-profile-img img {
        width: 75px;
    }
    
    .pah-profile-details label span {
        font-weight: bold;
    }
    
    .display-name {
        font-size: 18px;
    }
    
    .pah-dropdown ul {
        left: inherit !important;
        width: 330px;
    }
    .left-left-patient {
        cursor: pointer;
    }
    
    .cat__core__step__desc {
        padding: 10px;
    }
    
    .cat__core__step__desc span {
        color: #ffff !important;
        font-size: 18px !important;
    }
    
    .cat__core__step__desc p {
        color: #ffff !important;
        font-size: 14px !important;
    }
    
    .pah-FilingCenter {
        background: rgb(100, 134, 39);
        cursor: pointer;
    }
    
    .pah-supply {
        background: rgb(43, 153, 216);
        cursor: pointer;
    }
    
    .activity-section .col-lg-3 {
        margin-top: 10px;
    }
    
    .pah-menu-left-active {
        background-color: #e7e7e7;
    }
    
    .btn.btn-success,
    .show>.btn.btn-success {
        background-color: #46be8a !important;
        border-color: #46be8a !important;
    }
    
    .chatroom-message-min-list {
        max-height: inherit !important;
    }
    
    .date-filter-pah {
        position: absolute;
        right: 90px;
        top: auto;
        margin-top: 6px;
        padding: 10px;
    }
    .cat__apps__messaging__tab__content {
    margin-left: 0 !important;
    }

    /* //sideBar NAV */
    /* Shrinking the sidebar from 250px to 80px and center aligining its content*/
#sidebar.active {
    min-width: 120px;
    max-width: 120px;
}
/* Toggling the sidebar header content, hide the big heading [h3] and showing the small heading [strong] and vice versa*/
#sidebar .sidebar-header p {
    display: none;
}
#sidebar.active .sidebar-header span {
    display: none;
}
#sidebar.active .sidebar-header p {
    display: block;
}
    /* //sideBar NAV */
.search-hide-btn{
    color: #fff;
    position: absolute;
    right: -8px;
    top: 1px;
    z-index: 98;
    background: teal;
    border: 0;
    border-radius: 0;
    padding: 6px;
}
.custom-search-width {
    min-width: 87% !important;
    overflow:auto;
}

#stickThis {
    padding: 5px;
    background-color: #ccc;
    font-size: 1.5em;
    text-align: center;
    font-weight: bold;
    border: 2px solid #444;
    -webkit-border-radius: 10px;
    border-radius: 10px;
}
#stickThis.stick {
    margin-top: 60px;
    position: fixed;
    top: 0;
    z-index: 9;
    -webkit-border-radius: 0 0 10px 10px;
    border-radius: 0 0 10px 10px;
}


div#stickThis{
    background-color: #fff !important;
    border: 0 !important;
    border-radius: 0 !important;
    top: 60px;
    color: #292929 !important;
    text-align: left !important;
    padding-left: 15px !important;
    margin-left: 8px;
    -webkit-box-shadow: 0px 2px 6px -2px rgba(0,0,0,0.75);
    -moz-box-shadow: 0px 2px 6px -2px rgba(0,0,0,0.75);
    box-shadow: 0px 2px 6px -2px rgba(0,0,0,0.75);
    font-size: 17px !important;
}
.head-mod {
    margin: 9px 0px;
}
.modal-width {
    margin: 175px auto !important;
}
.dataTables_filter {
    margin-left: 0px !important;
}
.modal-dialog-width{
    max-width: 580px !important;
    /* margin: 175px auto !important; */
}
.disabled-content {
  pointer-events: none;
  /* opacity: 0.4; */
}
::ng-deep .sweet-overlay {
    z-index: 2000 !important;
}
::ng-deep .pagination{
    margin: 10px 0 !important;
}

</style>

<section class="card">

    <div class="card-header">
        <span class="cat__core__title">
            <strong>Manage Master Data Type</strong>
            <button *ngIf="optionShow=='MasterDataDef'" class="btn btn-primary btn-sm pull-right" type="button" 
            data-toggle="modal" data-target="#master-data-modal" data-backdrop="static" data-keyboard="false"
            (click)="resetMasterDataForm()">Add Master Data Type</button> 
            <button *ngIf="optionShow=='MasterDataFieldEntries'" [disabled]="disableEntryButton" class="btn btn-primary btn-sm pull-right" type="button" 
            data-toggle="modal" data-target="#master-data-field-entry-modal" data-backdrop="static" data-keyboard="false"
            (click)="resetMasterDataEntryForm()">Add New Fields Entry</button>

        </span>
    </div>

        <section class="card" *ngIf="dataLoadingMsg">
            <div class="card-block mb-2 mt-2">
                <div class="wait-loading">
                    <img src="assets/img/loader/loading.gif" />
                </div>
            </div>
        </section>
    
<div class="row" *ngIf="!dataLoadingMsg">
    <nav class="col-lg-3" id="sidebar">
        <button type="button" id="sidebarCollapse" class="btn btn-sm search-hide-btn" (click)="changeArrow()">
            <i title="Expand" [hidden]="!changeArrowButton" class="fa fa-angle-double-right"></i>
            <i  title="Collapse" [hidden]="changeArrowButton" class="fa fa-angle-double-left"></i>
        </button>
        <section class="card">
            <div class="card-block"> 
                <div class="cat__core__card-sidebar">
                    <div class="row" >
                        <div class="col-md-12 searchbar">
                            <div class="cat__apps__messaging__header sidebar-header" [hidden]="changeArrowButton">
                                <span><input class="form-control cat__apps__messaging__header__input" (keyup.enter)="searchMasterDataType(searchText)" style="width: 75%;margin-left: -10px;" placeholder="Search"
                                        [(ngModel)]="searchText" /></span>
                                <p><input class="form-control cat__apps__messaging__header__input" placeholder="S"
                                        [(ngModel)]="searchText" /></p>

                                <button type="button" [disabled]="!searchText" style="margin-right:10px;" class="btn btn-sm btn-primary" title="Search" (click)="searchMasterDataType(searchText)"><span style="margin-left:-3px;" class="fa fa-search"></span></button>
                                <button type="button" class="btn btn-sm btn-default" style="margin-right:-16px;" title="Reset" (click)="resetMasterDataType = true;searchMasterData='';searchText='';getMasterDataList()"><span style="margin-left:-3px;" class="fa fa-refresh"></span></button>
                            </div>
                        </div>
                    </div>
                    <div class="cat__apps__messaging__list inbox-data-container outer-patients">
                        <div *ngFor="let masterDataType of masterDataTypeList">
                            <div class="cat__apps__messaging__tab messages-tab hand-pointer" (click)="updateMasterDataType(masterDataType.id);" [class.cat__apps__messaging__tab--selected]="masterDataType.id==selectedMasterId" >
                                <div class="cat__apps__messaging__tab__content" [hidden]="changeArrowButton">
                                    <div class="left-left-patient" id="{{masterDataType.id}}">
                                        <span style="word-wrap: break-word;">{{masterDataType.name}}</span>
                                            <br>
                                        <span class="badge badge-info left-menu-unread-count">CSS Class: {{masterDataType.cssClass}}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div *ngIf="statusMessage" style="text-align: center;margin-top: 33px;">{{statusMessage}}</div>
                        <div [hidden]="hideLoadMore" class="cat__apps__messaging__tab messages-tab hand-pointer" >
                            <div class="cat__apps__messaging__tab__content" >
                                <div class="cat__apps__messaging__tab__name left-left-patient">
                                    <button (click)="loadMoreData()" class="btn btn-sm btn-default"  [hidden]="hideLoadMore">Load More</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </nav>

    <div class="col-xl-9 col-md-9 custom-content">
       
        <section class="card pah-custom-section" *ngIf="(totalCount > 0  && resetMasterDataType == false && show_msg==false) || (totalCount >= 1  && searchText !=''  && resetMasterDataType ==true && show_msg==false) " >      
                  <div class="row" style="padding-left: 10px; padding-right: 10px;">
            </div>
            <div class="main-tabs">
                <div class="chatwith-modal-tab pah-tab">

                    <div class="chatwith-model-head-pah" (click)="showTabData('MasterDataDef')"
                        [class.cat__apps__messaging__tab_pah--selected]="optionShow=='MasterDataDef'"><i class="fa fa-th-large"
                            aria-hidden="true"></i> Master Data Type Definition</div>
                            <div class="chatwith-model-head-pah" (click)="showTabData('MasterDataFieldEntries')"
                        [class.cat__apps__messaging__tab_pah--selected]="optionShow=='MasterDataFieldEntries'"><i class="fa fa-list"
                            aria-hidden="true"></i> Master Data Field Entries</div>
                </div>
            </div>

            <div class="row sub-tabs mt-2" style="padding-left:20px;min-height:550px;">

                <div class="col-md-12" *ngIf="optionShow=='MasterDataDef'">
                    
        <h5 class="head-mod">Master Data Type Details 
            <span data-placement="top"  title="Edit" data-original-title="Edit Master Data Type Details"
            (click)="updateMasterDataTypeForm()" class="information-edit personal-information-edit-icon"
            style="color:rgb(62, 172, 216);"><i style="margin-top: -87px;" class="icmn-pencil"></i></span>
            <span class="message-information-edit"><i style="cursor:pointer;margin-left: 225px;margin-top: -31px;" 
                class="fa fa-trash-o delete-ext-disable disabled-icon" (click)="deleteMasterType()" 
                data-original-title="Delete" title="Delete"></i>
            </span>
        </h5>
        <hr>
        <form class="new-form" [formGroup]="masterDataTypeDetails">
          <div class="row">
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Data Type Name * <i chToolTip="MSDTA000001"></i></label>
                <input type="text" class="form-control editable-field-profile" formControlName="dataTypeName" disabled>
              </div>
              <div
                *ngIf="masterDataTypeDetails.controls.dataTypeName.hasError('required')&&(masterDataTypeDetails.controls.dataTypeName?.dirty ||masterDataTypeDetails.controls.dataTypeName?.touched)"
                class="alert alert-danger">
                Field cannot be empty
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l1">Data Type Description</label>
                <input type="text" class="form-control editable-field-profile" formControlName="dataTypeDes" disabled>
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Data Type CSS Class * <i chToolTip="MSDTA000002"></i></label>
                <input type="text" class="form-control editable-field-profile" formControlName="dataTypeCss" (keyup)="getMasterCssClassEdit($event)" disabled>              
              </div>
              <div
                *ngIf="masterDataTypeDetails.controls['dataTypeCss'].hasError('required')&&(masterDataTypeDetails.controls.dataTypeCss?.dirty ||masterDataTypeDetails.controls.dataTypeCss?.touched)"
                class="alert alert-danger">
                Field cannot be empty
              </div>
            </div> 
            <div class="col-lg-5">
                <div class="form-group">
                    <label class="form-control-label">{{ 'LABELS.FINAL_CSS_CLASS' | translate }}  <i chToolTip="MSDTA000008"></i></label>
                  <input type="text" class="form-control" formControlName="dataTypeCssFinal" disabled>              
                </div>
              </div>           
          </div>
          <div class="form-actions" [hidden]="enableMasterDataTypeEdit">
            <div class="form-group">
              <button type="submit" class="btn btn-primary submit-profile" (click)="updateMasterDataTypeDetails()"
                [disabled]="!masterDataTypeDetails.valid">Submit</button>
              <button type="button" (click)="cancelMasterDataTypeDetails()" class="btn btn-default">Cancel</button>
            </div>
          </div>
        </form>
        <h5 class="head-mod">Field Details
        </h5>
        <button class="btn btn-primary btn-sm pull-right" type="button"  style="margin-top: -38px; margin-right: 80px;"
        data-toggle="modal" data-target="#master-data-field-modal" data-backdrop="static" data-keyboard="false"
        (click)="resetMasterDataFieldForm()">Add Field</button>
        <div class="mb-5">
            <div class="wait-loading" *ngIf="dataLoadingMsg">
                <img src="./assets/img/loader/loading.gif" />
            </div>
            <table class="table table-hover" id="fieldDetails" width="100%"></table>
        </div>
                </div>
                    <div class="col-md-12" *ngIf="optionShow=='MasterDataFieldEntries'">
                        <div class="row col-md-12">
                            <span [ngClass]="{'col-md-4' : showSiteFilter}"><h5 class="head-mod">Master Data Field Entries - {{masterDatam?.name}}</h5></span>
                            <span class="col-md-8 row" [hidden]="!showSiteFilter">
                                <span class="site-label user-sites">{{ 'LABELS.SITE_S' | translate }}:&nbsp;</span>
                               
                                    <app-select-sites [events]="eventsSubject.asObservable()"  [filterType]=true (siteIds)="getSiteIds($event)" (hideDropdown)="hideDropdown($event)" class="site-filter-class">
                                    </app-select-sites>
                                
                                
                            </span>
                            
                        </div>
                        
                        <hr>
                        <div class="" [hidden]="hideTable">
                            <div class="wait-loading" *ngIf="dataLoadingMsg">
                                <img src="./assets/img/loader/loading.gif" />
                            </div>
                            <table class="table masterDataEntryTable table-hover" id="masterDataEntries" width="100%">
                            </table>
                        </div>
                        <div style="text-align: center;" [hidden]="!hideTable">
                        <label>There is no data available.</label>
                        </div>
                        </div>
                </div>
        </section>
        <section class="card pah-custom-section" *ngIf="totalCount == 0 && resetMasterDataType == true">
            <div class="row" style="padding-left: 10px; padding-right: 10px;">
                <div class="" style="width:100%;">
                    <div class="row pah-profile-custom">
                        <div class="col-lg-12 pah-profile-details" style="text-align: center;">
                            No Master Data Type to see the details.
                        </div>
                    </div>
                </div>
            </div>
            <div class="row sub-tabs mt-2" style="padding-left:20px;min-height: 700px;">                
            </div>
        </section>
        <section class="card pah-custom-section" *ngIf="totalCount > 1 &&  searchText != '' && resetMasterDataType == false && show_msg==true" >
            <div class="row" style="padding-left: 10px; padding-right: 10px;">
                <div class="" style="width:100%;">
                    <div class="row pah-profile-custom">
                        <div class="col-lg-12 pah-profile-details" style="text-align: center;">
                            Please choose a Master Data Type to see the details.
                        </div>
                    </div>
                </div>
            </div>
            <div class="row sub-tabs mt-2" style="padding-left:20px;min-height: 700px;">                
            </div>
        </section>
      
    </div>
</div>
</section>
<div class="modal data-modal" id="master-data-modal" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
<div class="modal-dialog modal-dialog-width">
    <div class="modal-content">
        <div class="modal-header">
            <h4 class="modal-title" id="exampleModalLabel">Add Master Data Type</h4>
            <button title="Close" type="button" class="close" (click)="closeMasterDataModal()" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <span *ngIf="showLoading"
                style="color:teal;text-align: center;padding:20px 20px;">Please wait while we process
                your request... <i class="fa fa-refresh fa-spin"></i></span>
        </div>
        <div class="modal-body">
            <h4 class="modal-title" id="exampleModalLabel">Master Data Type Details</h4>
            <form action="#" [formGroup]="masterDataForm" (ngSubmit)="submitMasterDataType(s)"
            id="masterDataForm" #s="ngForm">
            <div class="modal-body">
                <div class=" row form-group">
                    <div class="col-md-4">
                        <label>Data Type Name * <i class="data-type-name icmn-info" data-animation="false" data-toggle="tooltip" data-placement="top"></i></label>
                    </div>
                    <div class="col-md-8">
                        <input type="text" class="form-control" formControlName="dataTypeName"
                            placeholder="Name" />
                        <div *ngIf="masterDataForm.controls['dataTypeName'].errors && (masterDataForm.controls.dataTypeName?.dirty || masterDataForm.controls.dataTypeName?.touched || s_submitted)"
                            class="alert alert-danger">
                            Data Type Name cannot be empty
                        </div>
                    </div>
                </div>
                <div class=" row form-group">
                    <div class="col-md-4">
                        <label>Data Type Description</label>
                    </div>
                    <div class="col-md-8">
                        <textarea class="form-control" formControlName="dataTypeDes" rows="2" cols="36" placeholder="Description"></textarea>
                    </div>
                </div>
                <div class=" row form-group">
                    <div class="col-md-4">
                        <label>Data Type CSS Class * <i class="data-type-cssClass icmn-info" data-animation="false" data-toggle="tooltip" data-placement="top"></i></label>
                    </div>
                    <div class="col-md-8">
                        <input type="text" class="form-control" formControlName="dataTypeCssClass" (keyup)="getMasterCssClass($event)"
                            placeholder="CssClass"/>
                        <div *ngIf="masterDataForm.controls['dataTypeCssClass'].errors && (masterDataForm.controls.dataTypeCssClass?.dirty || masterDataForm.controls.dataTypeCssClass?.touched || s_submitted)"
                            class="alert alert-danger">
                            Data Type CSS Class cannot be empty
                        </div>
                    </div>
                </div>
                <div class=" row form-group">
                    <div class="col-md-4">
                        <label>{{ 'LABELS.FINAL_CSS_CLASS' | translate }} <i class="data-type-master-cssClass icmn-info" data-placement="top" data-animation="false"></i></label>
                    </div>
                    <div class="col-md-8">
                        <input type="text" class="form-control" formControlName="dataTypeFinalCssClass"
                            disabled/>
                        
                    </div>
                </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary" style="cursor:pointer;"
                    [disabled]="editSubmit" (click)="s_submitted = true;" [disabled]="!masterDataForm.valid">Submit</button>
                    <button type="button" class="btn btn-secondary"
                    (click)="closeMasterDataModal()">Close</button>
                </div>
                </form>
        </div>
    </div>
</div>
</div>


<div class="modal data-modal" id="master-data-field-modal" role="dialog"
aria-labelledby="exampleModalLabel" aria-hidden="true" data-backdrop="false">
<div class="modal-dialog  modal-dialog-width">
    <div class="modal-content">
        <div class="modal-header">
            <h4 class="modal-title" id="exampleModalLabel">{{FieldModalLabel}}</h4>
            <button title="Close" type="button" class="close" (click)="closeMasterDataFieldModal()" aria-label="Close"><span aria-hidden="true">&times;</span></button>
            <span *ngIf="showLoading"
                style="color:teal;text-align: center;padding:20px 20px;">Please wait while we process
                your request... <i class="fa fa-refresh fa-spin"></i></span>
        </div>
        <div class="modal-body">
            <h4 class="modal-title" id="exampleModalLabel">Master Data Type Details</h4>
           
            <div class="modal-body">
                <div class=" row form-group">
                    <div class="col-md-4">
                        <label>Data Type Name <i class="data-type-name icmn-info" data-animation="false" data-toggle="tooltip" data-placement="top"></i></label>
                    </div>
                    <div class="col-md-8">
                        <input type="text" class="form-control" value={{masterDatam?.name}} disabled/>
                    </div>
                </div>
                <div class=" row form-group">
                    <div class="col-md-4">
                        <label>Data Type Description</label>
                    </div>
                    <div class="col-md-8">
                        <textarea class="form-control" rows="2" cols="36" value={{masterDatam?.description}} disabled></textarea>
                    </div>
                </div>
                <div class=" row form-group">
                    <div class="col-md-4">
                        <label>Data Type CSS Class <i class="data-type-cssClass icmn-info" data-animation="false" data-toggle="tooltip" data-placement="top"></i></label>
                    </div>
                    <div class="col-md-8">
                        <input type="text" class="form-control" value={{masterDataCss}} disabled/>
                    </div>
                </div>
                <div class=" row form-group">
                    <div class="col-md-4">
                        <label>{{ 'LABELS.FINAL_CSS_CLASS' | translate }} <i class="data-type-master-cssClass icmn-info" data-placement="top" data-animation="false"></i></label>
                    </div>
                    <div class="col-md-8">
                        <input type="text" class="form-control" value={{masterDatam?.cssClass}} disabled/>
                    </div>
                </div>
                </div>
                <h4 class="modal-title" id="exampleModalLabel">Field Details</h4>
                <form action="#" [formGroup]="fieldForm" id="fieldForm" #f="ngForm">
                    <div class="modal-body">
                        <div class=" row form-group">
                            <div class="col-md-4"><label>Field Name* <i class="field-name icmn-info" data-animation="false" data-toggle="tooltip" data-placement="top"></i></label></div>
                            <div class="col-md-8"><input type="text" class="form-control" formControlName="fieldName"/>
                                <div *ngIf="fieldForm.controls['fieldName'].errors && 
                                (fieldForm.controls.fieldName?.dirty || fieldForm.controls.fieldName?.touched || s_submitted)" class="alert alert-danger">
                                Field Name cannot be empty
                                </div>
                            </div>
                        </div>
                        <div class=" row form-group">
                            <div class="col-md-4"><label>Required <i class="field-required icmn-info" data-animation="false" data-toggle="tooltip" data-placement="top"></i></label></div>
                            <div class="col-md-8"><input type="checkbox" formControlName="isRequired"/></div>
                        </div>
                        <div class=" row form-group">
                            <div class="col-md-4"><label>CSS Class* <i class="cssClass icmn-info" data-animation="false" data-toggle="tooltip" data-placement="top"></i></label></div>
                            <div class="col-md-8"><input (keyup)="getCssClass($event)" type="text" class="form-control" formControlName="cssClass"/>
                                <div *ngIf="fieldForm.controls['cssClass'].errors && 
                                (fieldForm.controls.cssClass?.dirty || fieldForm.controls.cssClass?.touched || s_submitted)" class="alert alert-danger">
                                CSS Class cannot be empty
                                </div>
                            </div>
                        </div>
                        <div class=" row form-group">
                            <div class="col-md-4"><label>{{ 'LABELS.FINAL_CSS_CLASS' | translate }} <i class="final-cssClass icmn-info" data-animation="false" data-toggle="tooltip" data-placement="top"></i></label></div>
                                <div class="col-md-8"><input type="text" class="form-control"  formControlName="finalCssClass" [ngModel]="finalCss" disabled/></div>
                        </div>
                    </div>
                    <div class="modal-footer">
                    <button *ngIf="!fieldEdit" type="button" name="saveNew" class="btn btn-primary" style="cursor:pointer;" [disabled]="!fieldForm.valid" (click)="submitFieldDetails(f);s_submitted = true;">Save & New</button>
                    <button *ngIf="!fieldEdit" type="button" name="saveClose" class="btn btn-primary" style="cursor:pointer;" [disabled]="!fieldForm.valid" (click)="submitFieldDetails(f);s_submitted = true;">Save & Close</button>
                    <button *ngIf="fieldEdit" type="button" name="saveClose" class="btn btn-primary" style="cursor:pointer;" (click)="submitFieldDetails(f);s_submitted = true;">Save</button>
                    <button type="button" class="btn btn-secondary" (click)="closeMasterDataFieldModal()">Cancel</button></div>
                </form>
        </div>
    </div>
</div>
</div>
                                                
<div class="modal forward-modal" id="master-data-field-entry-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true" data-backdrop="false">
    <div class="modal-dialog modal-lg modal-width" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="exampleModalLabel">Master Data Entry - {{masterDatam?.name}}</h4>
                <button title="Close" type="button" class="close" (click)="closeMasterDataEntryModal()" aria-label="Close">
            <span aria-hidden="true">&times;</span></button>
            <span *ngIf="showLoading"
                style="color:teal;text-align: center;padding:20px 20px;">Please wait while we process
                your request... <i class="fa fa-refresh fa-spin"></i></span>
            </div>
            <div class="modal-body">
                <form action="#" [formGroup]="masterDataEntryForm" (ngSubmit)="submitMasterDataEntry(e)"
            id="masterDataEntryForm" #e="ngForm">
                <div class="modal-body">
                <div class="row form-group">
                    <div class="col-md-12 row">
                        <div class="col-md-6 row site-filter-position" [hidden]="!multiSiteEnable  || (multiSiteEnable && singleSiteLength)">
                            <span class="site-label-class">{{ 'LABELS.SITE_S' | translate }} * : </span>
                            <span class="col-md-9">
                                <app-select-sites [events]="eventsSubject.asObservable()" [filterType]=dynamic [hideApplyFilter]=true [selectedSiteIds]="editSiteData" [dynamic]=dynamic (siteIds)="getSiteIdsMasterData($event)"  (hideDropdown)="hideDropdownMasterData($event)">
                                </app-select-sites>
                                <div class="alert alert-danger site-position" *ngIf="siteRequired">
                                    Please select site.
                                </div>
                            </span>
                             
                           
                        </div><br>
                    </div>
                    
                    <div class="col-md-6 row" style="padding: 10px;" *ngFor="let fields of masterDataFieldList; let i = index">
                    <div class="col-md-4">
                        <label>{{fields.name}}<span [hidden]="!fields.required">*</span></label>
                    </div>
                    <div class="col-md-8">
                        <input type="text" class="form-control" formControlName = "{{fields.cssClass}}"/>
                        <div *ngIf="masterDataEntryForm.controls[fields.cssClass]?.errors && 
                        (masterDataEntryForm.controls[fields.cssClass]?.dirty || masterDataEntryForm.controls[fields.cssClass]?.touched)"  class="alert alert-danger">
                            {{fields.name}} is required.
                         </div>
                    </div>
                </div>
                </div>
                </div>
            <div class="modal-footer">
                <!-- <div *ngIf="showAlert">At least enter value in one field!</div> -->
                <button *ngIf="!entryEdit" type="submit" name="saveNew" class="btn btn-primary" style="cursor:pointer;" [disabled]="!masterDataEntryForm.valid">Save & New</button>
                <button *ngIf="!entryEdit" type="submit" name="saveClose" class="btn btn-primary" style="cursor:pointer;" [disabled]="!masterDataEntryForm.valid">Save & Close</button>
                <button *ngIf="entryEdit" type="submit" name="saveClose" class="btn btn-primary" style="cursor:pointer;" (click)="s_submitted = true;">Save</button>
                <button type="button" class="btn btn-secondary" (click)="closeMasterDataEntryModal()">Cancel</button>
            </div>
        </form>
        </div>
        </div>
    </div>
</div>
