import { HttpService } from './../../services/http/http.service';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';

import { LoginComponent } from './login.login';
import { ResetPassword } from './login.resetpassword';
import { TranslateModule } from '@ngx-translate/core';
import { ForgotUsernameComponent } from '../forgot-username/forgot-username.component';
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';
import { SharedModule } from '../shared/sharedModule';
export const routes: Routes = [
  { path: 'login', component: LoginComponent},
  { path: 'reset-password', component: ResetPassword },
  { path: 'reset-password/:enrol', component: ResetPassword },
  { path: 'performsp', component: LoginComponent},
  { path: 'matrixcare', component: LoginComponent},
  { path: 'brightree', component: LoginComponent},
  { path: 'azzly', component: LoginComponent},
  { path: 'sso', component: LoginComponent},
  { path: 'id_token', component: LoginComponent},
  { path: 'code', component: LoginComponent}
];

@NgModule({
  providers: [HttpService],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    TranslateModule,
    NgbDatepickerModule,
    SharedModule
  ],
  declarations: [
    LoginComponent,
    ForgotUsernameComponent,
    ResetPassword
  ]

})

export class LoginModule { }
