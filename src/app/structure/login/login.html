<style>
    .cat__pages__login {
        margin-top: -65px !important;
    }
    
    .valid-email {
        display: none;
    }
    
    .resetpass-text {
        font-size: 13px;
    }
    
    .forgotpass-form {
        margin-top: 15px;
    }
    
    .sessionout-msg {
        text-align: center;
        padding: 15px;
    }
    
    .sessionout-msg p {
        margin-bottom: 0 !important;
    }
    
    .session-out-modal .modal-dialog {
        top: 200px !important;
    }
    
    .session-out-modal .btn {
        margin: 25px auto auto auto;
        width: 200px;
    }
    
    .color-white {
        color: #ffffff!important;
    }
    
    .session-out-modal {
        background-color: rgba(0, 0, 0, 0.5);
    }
    .cat-loader-message{
        text-align: center;
        color: #fff;
        font-weight: 800;
        font-size: 1.5vw;
        
    }
    .idp-login{
        height: 100%;
        width: 100%;
        position: absolute;
    }
</style>
<!-- START: pages/login-beta -->
<div class="cat__pages__login login-page-block" [ngClass]="{'has-custom-nav-bar':whiteLabel.topNavbar,'idp-login':idpWorkflow}" >
    <div class="custom-nav-bar" *ngIf="whiteLabel.topNavbar">
        <div class="custom-nav-bar-logo" *ngIf="whiteLabel.topNavbar.logo"><img src="{{whiteLabel.topNavbar.logo}}"></div>
        <div class="custom-nav-bar-menu" *ngIf="whiteLabel.topNavbar.menu">
            <ul *ngIf="whiteLabel.topNavbar.menu.length">
                <li *ngFor="let navMenu of whiteLabel.topNavbar.menu">
                    <a [routerLink]="[navMenu.link]">{{navMenu.label}}</a>
                </li>
            </ul>
        </div>
    </div>
    <div class="cat__pages__login__header">
        <div class="row" *ngIf="!idpWorkflow">
            <div class="col-lg-6 logo-citus">
                <div class="side-logo">
                <img *ngIf="sourceTenantId==1" src="./assets/modules/pages/common/img/login/account-logo-on-dark-bg.png"/>
                <div class="login-logo-bg" *ngIf="sourceTenantId!=1">
                    <img src="{{_sharedService.assetsUrl}}/a/{{sourceTenantId}}/img/account-logo-on-white-bg.png" onerror="this.src='assets/modules/pages/common/img/account-logo-on-white-bg.png'"/>
                </div>
                </div>
            </div>
            <div class="col-lg-6 header-info">
                <p>
                    <i *ngIf="whiteLabel.contact.phone" class="fa fa-phone-square" aria-hidden="true"></i> <a class="color-white" href="tel:{{whiteLabel.contact.phone}}">{{whiteLabel.contact.phone}}</a>
                    <span><i *ngIf="whiteLabel.contact.email" class="fa fa-envelope" aria-hidden="true"></i><a class="color-white" href="mailto:{{whiteLabel.contact.email}}">{{whiteLabel.contact.email}}</a></span></p>
            </div>
        </div>
    </div>

    <app-ch-modal-loader [message]="idmMigrationLoaderMsg" *ngIf="showIdmMigrationLoader"></app-ch-modal-loader>
    <div *ngIf="!idpWorkflow && !isOktaWorkflow" class="cat__pages__login__block cat__pages__login__block--extended" [ngStyle]="{'display:none': isOktaWorkflow}">
        <div class="row">
            <div class="col-xl-12">
                <div class="cat__pages__login__block__inner main-login-box">
                    <!-- Login Starts Here-->
                    <div [ngClass]="{ 'hide-username-box': enableForgotUsername || enableForgotPassword }" class="cat__pages__login__block__form login-form">
                        <img _ngcontent-c2="" src="{{whiteLabel.banner.logo}}" *ngIf="whiteLabel.banner.logo" class="main-logo">
                        <img _ngcontent-c2="" src="{{_sharedService.assetsUrl}}/a/{{sourceTenantId}}/img/account-logo-on-white-bg.png" *ngIf="!whiteLabel.banner.logo" class="main-logo">
                        <div class="service-maintenance">Scheduled Maintenance Alert: System upgrade is in progress and will be complete by 8am ET on Saturday. The system will not be available during this time for any activity, including patient chats.</div>
                        <h4 class="text-uppercase">
                            <strong *ngIf="!oktaForgotPassword && !isOktaMfaEnabled">{{'LABELS.LOG_IN' | translate}}</strong>
                            <strong *ngIf="oktaForgotPassword && !hideForgotPasswordLabels">{{'LABELS.FORGOT_PASSWORD_TITLE' | translate}}</strong>
                        </h4>
                        <span class="resetpass-text" *ngIf="oktaForgotPassword && !hideForgotPasswordLabels">
                            {{'LABELS.FORGOT_PASSWORD' | translate}}
                        </span>
                        <form class="form-horizontal" (ngSubmit)="login(f)" [formGroup]="userLogin" novalidate #f="ngForm">
                           <div class="form-group" [hidden]="isEmailCheckingDone" *ngIf="normalLoginFlow"> 
                                <label class="form-label login-username-label">{{'LABELS.LOGIN_USERNAME' | translate}}</label>                                
                                <input type="text" class="form-control" id="userName" value="{{userName}}" (keyup.enter)="checkUsername(f)" name="userName" placeholder="{{'PLACEHOLDERS.USERNAME' | translate}}" [formControl]="userLogin.controls['userName']" autocomplete="username">
                                <div class="alert alert-danger" *ngIf="userLogin.controls.userName.errors && userLogin.controls.userName.errors.required && (userLogin.controls.userName.dirty || userLogin.controls.userName.touched || f.submitted)">
                                       {{'VALIDATION_MESSAGES.LOGIN_USERNAME' | translate}}
                                </div>
                           </div>
                           <div class="form-group okta-login" *ngIf="isEmailCheckingDone && !isOktaMfaEnabled">
                                <label class="form-label" *ngIf="!oktaForgotPassword">{{'LABELS.LOGIN_AS' | translate}}&nbsp;<strong>{{userName}}</strong><span class="not-you" (click)="goBackUpdateEmail(f)" *ngIf="!oktaForgotPassword">{{'LABELS.NOT_YOU' | translate}}</span></label>  
                           </div>
                           <div *ngIf="!normalLoginFlow" id="okta-login-container"></div>
                           <div class="form-group" [ngClass]="{ 'hide-password-box': !(isEmailCheckingDone || !hasEmailDomainFlow) ? true : false }">
                                <div class="alert alert-danger" *ngIf="otpInfo.otpErrorMsg">
                                    {{ otpInfo.otpErrorMsg }}
                                </div>
                                <ng-container *ngIf="!otpInfo.loginWithOtp; else loginWithOtp;">
                                    <label class="form-label login-password-label" *ngIf="normalLoginFlow">{{'LABELS.LOGIN_PASSWORD' | translate}}</label>   
                                    <div class="input-group">                             
                                    <input *ngIf="normalLoginFlow" #passwordField [type]="fieldTextType ? 'text' : 'password'" class="form-control" id="password" name="password" placeholder="Password" [formControl]="userLogin.controls['password']" autocomplete="current-password">
                                    <i *ngIf="normalLoginFlow" data-placement="top" data-toggle="tooltip"  data-original-title="Show password" data-animation="false" id="toggle_icon"  class="fa toggle-eye" [ngClass]="{'fa-eye-slash': !fieldTextType,'fa-eye': fieldTextType }" (click)="toggleFieldTextType()"></i>
                                    </div>
                                    <div class="alert alert-danger" *ngIf="normalLoginFlow && userLogin.controls.password.errors && userLogin.controls.password.errors.required && (userLogin.controls.password.dirty || userLogin.controls.password.touched || f.submitted)">
                                        {{'VALIDATION_MESSAGES.ENTER_PASSWORD' | translate}}
                                    </div>
                                </ng-container>
                                <div *ngIf="otp2faInfo.twoFaEnabled" class="mt-3">
                                    <ng-container *ngTemplateOutlet="loginWithOtp"></ng-container>
                                </div>
                                <ng-template #loginWithOtp>
                                    <div class="alert alert-success" *ngIf="otpInfo.otpSuccessMsg">
                                        {{ otpInfo.otpSuccessMsg }}
                                    </div>
                                    <label class="form-label w-100">{{ 'LABELS.OTP' | translate }}
                                        <span class="pull-right">
                                            <a *ngIf="otpInfo.resendOtp; else displayExpiry;" href="javascript: void(0);" (click)="sendOTP()" class="pull-right cat__core__link--blue cat__core__link--underlined"><span *ngIf="otpLoader" class="fa fa-circle-o-notch fa-spin"></span>&nbsp;{{ 'LABELS.RESEND_OTP_Q' | translate }}</a>
                                            <ng-template #displayExpiry><strong *ngIf="otpInfo.otpExpiryInMins !== ''">{{ 'MESSAGES.OTP_EXPIRES_IN_SECONDS' | translate:{'expiry': otpInfo.otpExpiryInMins} }}</strong></ng-template>
                                        </span>
                                    </label>
                                    <div id="otp" class="d-flex flex-row justify-content-between">
                                        <input *ngFor="let input of formOtpInput; index as i" #formOtpRow type="text" formControlName="{{input}}" class="m-2 text-center form-control rounded" maxlength="1" (keyup)="otpKeyUpEvent($event, i)">
                                    </div>
                                    <div class="alert alert-danger" *ngIf="isOtpHasErrors && (isOtpDirtyOrTouched || f.submitted)">
                                        {{ 'MESSAGES.INVALID_OTP_MESSAGE' | translate }}
                                    </div>
                                </ng-template>
                            </div>
              <div class="form-check ml-4" *ngIf="!oktaForgotPassword && !isOktaMfaEnabled">
                <input type="checkbox" class="form-check-input" id="rememberUsername" [checked]="rememberMe" (click)="rememberLogin()" />
                <label class="form-check-label" for="rememberUsername">{{ 'LABELS.REMEMBER_USERNAME' | translate }}</label>
              </div>
                         <div class="form-group" *ngIf="!oktaForgotPassword && !isOktaMfaEnabled">
                                <button id="next" *ngIf="!isEmailCheckingDone && hasEmailDomainFlow" type="button" (click)="checkUsername(f)" class="btn btn-primary btn-block mr-3">
                                    <span *ngIf="emailCheckingLoader" class="fa fa-circle-o-notch fa-spin"></span>
                                    <span *ngIf="!emailCheckingLoader" >Next</span>
                                </button>
                                <button id="signInButton" *ngIf="isEmailCheckingDone || !hasEmailDomainFlow" class="btn btn-primary btn-block mr-3">{{signInButtonVal}}  <i *ngIf="loading" class="fa fa-spinner fa-spin"></i> </button>
                           </div>
                           <div class="form-group d-flex" [ngClass]="!isEmailCheckingDone || (otp2faInfo.otpEnabled && !otp2faInfo.twoFaEnabled) ? 'justify-content-between' : 'justify-content-end'" *ngIf="!oktaForgotPassword && !isOktaMfaEnabled">
                                <a *ngIf="!isEmailCheckingDone; else loginWith;" href="javascript: void(0);" (click)="forgotUsername()" class="pull-left cat__core__link--blue cat__core__link--underlined">{{ 'LABELS.FORGOT_USERNAME_Q' | translate }}</a>
                                <ng-template #loginWith><a *ngIf="otp2faInfo.otpEnabled && !otp2faInfo.twoFaEnabled" href="javascript: void(0);" (mousedown)="loginWithOtpOrPassword()" class="pull-left cat__core__link--blue cat__core__link--underlined"><span *ngIf="otpLoader && !otpInfo.loginWithOtp"><i class='fa fa-circle-o-notch fa-spin'></i>&nbsp;</span>{{ (otpInfo.loginWithOtp ? 'LABELS.LOGIN_WITH_PASSWORD_Q' : 'LABELS.LOGIN_WITH_OTP_Q') | translate }}</a></ng-template>
                                <a href="javascript: void(0);" (click)="forgotPassword()" class="pull-right cat__core__link--blue cat__core__link--underlined">Forgot Password?</a>
                            </div>
                           <div class="form-actions" *ngIf="!oktaForgotPassword && !isOktaMfaEnabled">
                               <div class="accountMessage">Do not have an account and need help with Signup? <a class="cat__core__link--blue" data-toggle="modal" href="#myModal">Click here</a>  </div>  
                                <span class="register-link" *ngIf="!isFromBranchLink">
                                    Don't have an account?<a href="javascript: void(0);" [routerLink]="['/registration']" class="cat__core__link--blue cat__core__link--underlined"> Sign Up Now</a>  
                                </span>
                            </div>                           
                        </form>
                    </div>
                    <!-- Login Ends Here -->
                    <!-- Forgot Username Starts Here-->
                    <app-forgot-username [enableForgotUsername]="enableForgotUsername" (backToLogin)="enableForgotUsername = $event;"  *ngIf="enableForgotUsername"></app-forgot-username>
                    <!-- Forgot Username Ends Here-->
                    <!-- Forgot Password Starts Here-->
                    <div *ngIf="enableForgotPassword && !isOktaMfaEnabled" class="cat__pages__login__block__form forgot-password-form">
                        <h4 class="text-uppercase">
                            <strong>Forgot Password</strong>
                        </h4>
                        <br>
                        <span class="resetpass-text">
                            {{'LABELS.FORGOT_PASSWORD' | translate}}
                        </span>

                        <br />
                        <form class="new-form forgotpass-form" [formGroup]="userLogin">
                            <div class="form-group">
                                <label class="form-label">{{'LABELS.LOGIN_USERNAME' | translate}}</label>
                                <input class="form-control" type="text" placeholder="{{'PLACEHOLDERS.LOGIN_USERNAME' | translate}}" formControlName="userName" type="email" pattern="^\w+([\+.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,6})+$">
                                <div *ngIf="userLogin.controls['userName'].hasError('required')&&(userLogin.controls.userName?.dirty ||userLogin.controls.userName?.touched)" class="alert alert-danger">
                                    {{'VALIDATION_MESSAGES.LOGIN_USERNAME' | translate}}
                                </div>
                                <div class="alert alert-danger valid-email">
                                    {{'VALIDATION_MESSAGES.USERNAME' | translate}}
                                </div>
                            </div>
                            <div class="clear-div"></div>
                            <div class="form-actions">
                                <button [disabled]="forgotPasswordLoader" (click)="forgotPasswordSend()" type="submit" class="btn btn-primary mr-3"><span *ngIf="forgotPasswordLoader"><i class='fa fa-spinner fa-spin '></i> </span>Submit</button>
                                <a href="javascript: void(0);" (click)="getUserLogin()" class="pull-right cat__core__link--blue cat__core__link--underlined reset-btn-right">Log In</a>
                            </div>
                            
                            <div class="create-account">
                                <span class="register-link">Don't have an account? <a href="javascript: void(0);" [routerLink]="['/registration']" class="cat__core__link--blue cat__core__link--underlined">Sign Up Now</a> 
                                </span>
                            </div>
                            <div class="alert alert-danger" role="alert" *ngIf="loginFailed">
                                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                                <strong>Failure!</strong> <a href="#" class="alert-link">Invalid username or password </a>
                            </div>
                        </form>
                    </div>
                    <!-- Forgot Password Ends Here -->
                    <div class="cat__pages__login__block__sidebar">
                        <h4 class="cat__pages__login__block__sidebar__title" [innerHTML]="whiteLabel.banner.heading"></h4>
                        <div class="cat__pages__login__block__sidebar__item">
                            {{whiteLabel.banner.body}}
                        </div>
                        <div class="cat__pages__login__block__sidebar__place" [innerHTML]="whiteLabel.copyright"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div *ngIf="idpWorkflow && !isOktaWorkflow" class="cat__pages__login__block">
        <div class="row">
            <div class="col-xl-12">
                <div class="cat__pages__login__block__inner main-login-box idp-box">
                    <div class="idp-customer-logo" *ngIf="_sharedService.tenantIdFromSSO">
                        <img src="{{_sharedService.assetsUrl}}/a/{{_sharedService.tenantIdFromSSO}}/img/account-logo-on-white-bg.png" onerror="this.src='assets/modules/dummy-assets/common/img/account-logo-on-white-bg.png'" />
                    </div>
                    <h4>
                        <strong>{{isReturnSSOScreen?'Taking you to the App':'Taking you to your organization\'s sign-in page'}}</strong>
                    </h4>
                    <span class="fa fa-circle-o-notch fa-spin"></span> {{idpLoginMessages}}
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: pages/login-beta -->
<div class="modal fade bd-example-modal-lg session-out-modal" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" id="sessionoutlogin" data-backdrop="false">
    <div class="modal-dialog modal-lg">
        <div class="modal-content sessionout-msg">

            <p> Your session has expired due to inactivity</p>
            <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal" (click)="loginAftertimeout()">Login Again</button>
            <button type="button" class="btn btn-secondary close-btn" data-dismiss="modal" style="display:none;">Close</button>
        </div>
    </div>
</div>
<div class="modal fade" id="myModal" role="dialog">
    <div class="modal-dialog modal-dialog">
  
      <!-- Modal content-->
      <div class="modal-content">
        <div class="modal-header noborder">
              <div class="messageModelheader"><h4 class="modal-title "><strong>ACCOUNT CREATION</strong></h4></div>  
          <button type="button" class="close" data-dismiss="modal">&times;</button>
          
        </div>
        <div class="modal-body accountModelContent" [innerHTML]="whiteLabel.accountCreationHelp"></div>
        <div class="modal-footer messageModelFooter">
          <button type="button" class="btn btn-primary" data-dismiss="modal">OK</button>
        </div>
      </div>
  
    </div>
  </div>    
