/* eslint-disable no-underscore-dangle */
import { HttpService } from './../../services/http/http.service';
import { Component, OnInit, ViewChildren, ElementRef, Renderer2, NgZone, OnDestroy, ChangeDetectorRef } from '@angular/core';
import { Router, ActivatedRoute, Params, NavigationExtras } from '@angular/router';
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { RegistrationService } from '../registration/registration.service';
import { StructureService } from '../structure.service';
import { CommonVideoService } from "../../../assets/lib/universal-video/common-video.service";
import { configTimeZone, oktaFlow, oktaIssuer, oktaPostLogoutRedirectUri} from "../../../environments/environment";
import { SharedService } from '../shared/sharedServices';
import { ToolTipService } from './../../structure/tool-tip.service';
import { APIs } from 'app/constants/apis';
import { finalize } from 'rxjs/operators';
import { Subject, Subscription } from 'rxjs';
import { OktaService } from '../shared/okta/okta.service';
import { OktaAuthService } from '@okta/okta-angular';
import * as OktaSignIn from '@okta/okta-signin-widget';
import { isBlank, isPresent } from 'app/utils/utils';
import { CONFIG } from 'custom-configs';
import * as CryptoJS from 'crypto-js';

declare const notifyMe:any;
declare var $: any;
declare var moment: any;

export interface OtpResponse {
  status: boolean;
  message: string;
  sessionId: string;
  otpExpiryTime: number;
}

@Component({
  selector: 'app-oncall-login',
  templateUrl: './login.html'
})

export class LoginComponent implements OnInit, OnDestroy {
  userLogin: FormGroup;
  userName: string;
  password: string;
  errorMessageForUsername = 'Please enter your username or email';
  errorMessageForPassword = 'Please enter your password';
  errorMessageForEmail = 'Please enter your Email';
  errorMessageForValidEmail = 'Please Enter a Valid Email';
  loginDetails: any;
  enableForgotUsername = false;
  otpInfo = this.defaultOtpInfo;
  otp2faInfo = { otpEnabled: false, twoFaEnabled: false };
  loginWithOtp = false;
  otpLoader = false;
  expiryInterval;
  enableForgotPassword;
  isEmailCheckingDone = false;
  hasEmailDomainFlow = true;
  enableLogin;
  validemail;
  rememberMe=true;
  secondTick=0;
  timeInMinutesForSessionOut;
  timeInSecondsAfterSessionOut;
  flagstop=true;
  timeoutAfter=false;
  stoplogout=false;
  emailCheckingLoader = false;
  dataResponse:any;
  startThisSessionTimerPromise;
  sessionSelf:any;
  timezone;
  isFromBranchLink:any=false;
  sourceTenantId:any=1;
  idpLoginSuccess;
  idpLoginMessageEvent;
  idpLoginMessages;
  idpWorkflow = false;
  forgotPasswordLoader = false;
  currentYear =new Date().getFullYear();
  fieldTextType: boolean;
  btnTextVal="Sign In";
  signInButtonVal=this.btnTextVal;
  loading=false;
  whiteLabel:any = {
    contact: {
      email: "<EMAIL>",
      phone: "(*************"
    },
    banner: {
      heading: "When we are better connected, everyone wins",
      body: ""
    },
    accountCreationHelp : "<p>If you are a patient looking to create an account, please contact your Care Provider. Access to the App has to be provided by your Care Provider.</p><p>If you are a staff member looking to create an account, please contact your Organization. Access to the App has to be provided by your Organization.</p>",
    copyright: "© "+this.currentYear+" All Rights Reserved."
  };
  formOtpInput = ['first', 'second', 'third', 'fourth', 'fifth', 'sixth'];
  user = '';
  oktaSignIn;
  otp = '';
  public elementId = 'okta-login-container';
  screenNumber = 1;
  forceCheckOktaSession = false;
  @ViewChildren('formOtpRow') otpFields;
  normalLoginFlow = false;
  lastUserName = '';
  private readonly destroy$ = new Subject();
  isOktaFlowEnabled: boolean;
  idmMigrationLoaderMsg = '';
  showIdmMigrationLoader = false;
  isOktaWorkflow = false;
  isOktaFirstLogin = false;
  private socketEventSubscriptions: Subscription[] = [];
  oktaForgotPassword = false;
  hideForgotPasswordLabels = false;
  oktaForgotUserName = '';
  isOktaMfaEnabled = false;
  constructor(
    private router: Router,
    private readonly structureService: StructureService,
    private readonly toolTipService: ToolTipService,
    public _sharedService: SharedService,
    private renderer: Renderer2,
    private elementRef: ElementRef,
    private httpService: HttpService,
    private ngZone: NgZone,
    private registrationservice: RegistrationService, public _commonVideoService: CommonVideoService,
    private okta: OktaService,
    private oktaAuth: OktaAuthService,
    private cdRef: ChangeDetectorRef
  ) {
    this.isOktaFlowEnabled = oktaFlow;
    if (this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) {
      this.oktaSignIn = okta.getStepWidget();
    }
    this.timezone = configTimeZone();
    this.idpLoginSuccess = this._sharedService.idpLoginSuccess.subscribe(
      (data) => {
        console.log("idp Login success and subscribe get...");
          this.loginSuccess(data);
      }
  );
  
  renderer.listen(elementRef.nativeElement, 'click', (event) => {
  //show/hide password  tool-tip change
    if(event.target.id =='toggle_icon'){
      if($(event.target).hasClass('fa-eye')){
        $('#toggle_icon').attr('data-original-title', "Hide password").tooltip('show');
       }else{
        $('#toggle_icon').attr('data-original-title', "Show password").tooltip('show');
      }
    }
  //End of show/hide password tool-tip change
  });

  this.idpLoginMessageEvent = this._sharedService.idpLoginMessage.subscribe(
    (data) => {
      console.log("idp Login success and subscribe get...");
      if(data.idpWorkflow !=undefined && (data.idpWorkflow===true||data.idpWorkflow===false)){
        this.idpWorkflow =data.idpWorkflow;
        if(data.idpWorkflow === false){
          this.router.navigate(['login']);
        }
      }else{
        this.idpLoginMessages =data.message;
      }
    }
  );
  this.isOktaFirstLogin = JSON.parse(sessionStorage.getItem('isOktaFirstLogin')) ? JSON.parse(sessionStorage.getItem('isOktaFirstLogin')) : '';
  const oktaStorage = localStorage.getItem('okta-token-storage');
  if (!isBlank(oktaStorage)  && (this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow')))) {
    this.isOktaWorkflow = true;
    if (this.isOktaFirstLogin) {
      sessionStorage.setItem("isOktaFirstLogin",JSON.stringify(false));
      this._structureService.logout();
    }
  }
   }
  ngOnDestroy() {
    /**Unsubscribe all the socket event subscriptions */
    this.socketEventSubscriptions.forEach(subscription => {
      if(subscription) subscription.unsubscribe();
    });
    this._structureService.deleteCookie('fromEnrol');
    this.idpLoginSuccess.unsubscribe();
    this.idpLoginMessageEvent.unsubscribe();
    if (this.expiryInterval) {
      clearInterval(this.expiryInterval);
    }
    this.destroy$.next();
    this.destroy$.complete();
    
  }
  ngOnInit() {
    if (this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow') && isBlank(this._structureService.userDetails))) {
      this.oktaWidgetInit();
    } else {
      this.normalLoginFlow = true;
    }
    this.idpLoginMessages ="Loading the Configuration...";
  //Service under maintenance - Start
  $('link[id="live-style"]').remove();
  var liveStyle = $('<link href="'+this._sharedService.assetsUrl+'/a/0/css/live-style.css?v='+new Date().getTime()+'" rel="stylesheet" id="live-style">');
  $("head").append(liveStyle);
  //Service under maintenance - End

  if(localStorage.getItem('loadingUrl') && localStorage.getItem('loadingUrl').indexOf("?_branch_match_id")!=-1){
    this.isFromBranchLink = true;
  }
  if(localStorage.getItem('sourceTenantId')){
    this.sourceTenantId = localStorage.getItem('sourceTenantId');
  }
  setTimeout(()=>{  
    if(localStorage.getItem('sourceTenantLabel')){
      this.whiteLabel = JSON.parse(localStorage.getItem('sourceTenantLabel'));
    }
  },  200);
  if(localStorage.getItem('logoutSessionOut')=="true"){
     $("#sessionoutlogin").modal("show");
     $(".sessionout-msg p").html("Your session has expired due to inactivity");
     localStorage.removeItem('logoutSessionOut');
   }
    const authToken = this._structureService.getCookie('authenticationToken');
    const fromEnrol = this._structureService.getCookie('fromEnrol');
    if(authToken){
      
      const userData = JSON.parse(this._structureService.userDetails);
      if(fromEnrol!='true'){
        /*if (userData.group === '3' && localStorage.getItem('patientTopicCount') != '0') {
          this.router.navigate(['/faq']);
        } else {*/
          this.router.navigate(['/inbox']);
        //} 
      }else{
        this.router.navigate(['/login']);
      }
    } else {
      this._structureService.displayProgress.emit(false);
    }
    // Initialize remember me state from cookie before okta widget init
    const userName = this.structureService.rememberMeUsername;
    this.rememberMe = isPresent(userName);
    this.userName = this.rememberMe ? userName : '';
    const group = {
      userName: new FormControl(this.userName, Validators.required),
      password: new FormControl('', Validators.required)
    };
    this.formOtpInput.forEach((key) => {
      group[key] = new FormControl('');
    });   
    this.userLogin = new FormGroup(group);

    if (!window.location.href.includes(this._structureService.commonIdpURL) && !window.location.href.includes('/login') && !window.location.href.includes('/id_token') && !window.location.href.includes('/code')) {
      sessionStorage.removeItem('emailDomainName');
    }
    // tooltip initialization 
    setTimeout(function () {
      $('body').tooltip({selector: '[data-toggle="tooltip"]'});
      $('[data-toggle="tooltip"]').tooltip();
    },100);
    this._structureService.isLoginSuccess.subscribe(isLogined => {
      if ((this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow')))) {
        if (!isLogined) {
          this.normalLoginFlow = false;
          this.showIdmMigrationLoader = false;
        } else {
          this.showIdmMigrationLoader = false;
        }
      }
    });
  }
  rememberLogin(){
    this.rememberMe = !this.rememberMe;
    this.manageUsernamePersistence(this.userName);
  }

  goBackUpdateEmail(f){
    this.resetOtpInfo();
    this.isEmailCheckingDone = false;
    const formControls = f.controls ? f.controls : f.form.controls ;
    formControls.password.setValue('');
    f.submitted = false;
    this.fieldTextType = false;
    if(this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) {
      this.handleBackClick();
      this.signInButtonVal = this.btnTextVal;
      this.loading=false;
    }
    this.cdRef.detectChanges();
  }
  manageUsernamePersistence(username: string) {
    // Save username cookie if remember me is checked AND we have a username
    if (this.rememberMe) {
      this.structureService.setRememberMeUsername(username);
    } else {
      this.structureService.removeRememberMeUsername();
    }
  }

  checkUsername(f) {
    const formControls = this.getFormControlValues(f);
    let selectedUserName;
      if(this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) {
        selectedUserName = this.getUsernameControl().value.trim();
        this.userLogin.patchValue({ userName: selectedUserName });
      } else {
        selectedUserName = formControls.userName.value.trim();
      }
      if(formControls.userName.invalid || this.isEmailCheckingDone || !this.hasEmailDomainFlow){
        return;
      }
   
    this.emailCheckingLoader = true;
    this._structureService.userNameCheck(selectedUserName).subscribe((data:any) => {
      // TODO: handle the login and redirection
      // 1. SSO enabled (app_auth_link + tenantId) --> SSO redirection flow
      // 2. OKTA Enabled + no ssoId --> User Migration to OKTA and Normal ICAMPP flow
      // 3. OKTA Enabled + ssoId --> OKTA login
      // 4. Normal ICAMPP flow
      sessionStorage.setItem('isOktaEnabled',data.oktaEnabled);
      sessionStorage.setItem('ssoId',data.ssoId ? data.ssoId : '');
      const enableTlms = true;
      if (data.app_auth_link) {
        if (enableTlms) {
          const idpButton = document.querySelector('.social-auth-button') as HTMLElement;
          if (idpButton) {
            // Update the Okta widget with the new IDP ID
            this.okta.updateIdpConfiguration('0oa2bv08nlqsFoGmO0h8');
            idpButton.click();
          }
        } else {
          this._sharedService.tenantIdFromSSO = data.tenant_id;
          this._sharedService.ssoEmailId = selectedUserName;
          if (selectedUserName.lastIndexOf('@') > 0) {
            const emailDomainName = selectedUserName.substring(selectedUserName.lastIndexOf('@') + 1);
            sessionStorage.setItem('emailDomainName', emailDomainName);
          }
          formControls.password.setValue('');
          if (data.app_auth_link.includes('#')) {
            this.router.navigateByUrl(data.app_auth_link.split('/#')[1]);
          } else {
            const url = new URL(data.app_auth_link);
            this.router.navigateByUrl(url.pathname);
          }
        }
      } else {
        formControls.password.markAsUntouched();
        formControls.password.setErrors({});
        formControls.password.markAsPristine();
        formControls.password.updateValueAndValidity();
        this.userName = selectedUserName;
        // TODO Need to update the condition after okta authentication enabled
        if((data.oktaEnabled && data.ssoId) || this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) {
          this.isOtpAnd2faEnabledForTenant(true);
        } else {
          this.isOtpAnd2faEnabledForTenant();
        }
      }
      
    });
  }
  // isOtpAnd2faEnabledForTenant(isOktaFlow?) {
  //   this.httpService
  //     .doGet(APIs.otpEnabledEndpoint.replace(/{username}/g, this.userName))
  //     .subscribe((value: { otpEnabled: boolean; twoFaEnabled: boolean; otpResponse?: any }) => {
  //       this.emailCheckingLoader = false;
  //       this.isEmailCheckingDone = true;
  //       this.otp2faInfo = value;
  //       if (this.otp2faInfo.twoFaEnabled) {
  //         this.showExpiryTimer(value.otpResponse);
  //       }
  //       setTimeout(() => {
  //         this.setFocusOnFirstVisibleElm();
  //       }, 0);
  //       if(isOktaFlow) {
  //         console.log("OKTA FLOW -----> ",oktaFlow);
  //         this.screenNumber = 2;
  //         this.showPassword();
  //         this.hideUsername();
  //       }
  //     },error => {
  //       this.emailCheckingLoader = false;
  //       this.isEmailCheckingDone = false;
  //     });
  // }

  isOtpAnd2faEnabledForTenant(isOktaFlow?) {
    // Hardcoded response
    const hardcodedResponse = {
      otpEnabled: true,
      twoFaEnabled: true,
      otpResponse: {
        status: true,
        message: "You will receive a One-Time Password via the Email or SMS within a few minutes if the user exists in our system. Thank you for your patience!",
        otpExpiryTime: 150,
        sessionId: "3ff8f8ee-7249-4b40-b500-286ea70aba06"
      }
    };

    // Simulate the observable response
    this.emailCheckingLoader = false;
    this.isEmailCheckingDone = true;
    this.otp2faInfo = hardcodedResponse;
    
    if (this.otp2faInfo.twoFaEnabled) {
      this.showExpiryTimer(hardcodedResponse.otpResponse);
    }
    
    setTimeout(() => {
      this.setFocusOnFirstVisibleElm();
    }, 0);
    
    if(isOktaFlow) {
      console.log("OKTA FLOW -----> ", isOktaFlow);
      this.screenNumber = 2;
      this.showPassword();
      this.hideUsername();
    }
}

  setFocusOnFirstVisibleElm() {
    const input = Array.from(document.querySelectorAll('input')).filter((x) => x.offsetWidth !== 0 || x.offsetHeight !== 0);
    if (input) {
      input[0].focus();
    }
  }

  // password show/hide toggle
  toggleFieldTextType() {
    this.fieldTextType = !this.fieldTextType;
  }
  //End of password show/hide toggle
  
  login(f) {
    const formControls = this.getFormControlValues(f);
    let currentPassword;
    if (this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) {
      currentPassword = this.getPasswordControl().value;
      this.userLogin.patchValue({ password: currentPassword });
    } else {
      currentPassword = formControls.password.value.trim();
    }
    this.checkUsername(f);
    if (!f.valid) {
      return false;
    }
    $('#signInButton').attr('disabled', 'disabled');
    this.signInButtonVal = this._ToolTipService.getTranslateData('BUTTONS.PROCESSING');
    this.loading = true;
    if (this.hasEmailDomainFlow) {
      formControls.userName.setValue(this.userName);
    } else {
      this.userName = formControls.userName.value;
    }
    this.password = currentPassword;
    sessionStorage.setItem('usr',this.userName);
    this.manageUsernamePersistence(this.userName);
    this._structureService.resetLocalStorageDataCommon();
    if (this.otpInfo.loginWithOtp || this.otp2faInfo.twoFaEnabled) {
      this.password = this.otp2faInfo.twoFaEnabled ? this.password : 'KIran';
      this.formOtpInput.forEach((key) => {
        this.otp += this.userLogin.get(key).value;
      });
    }
    if(!isBlank(this.password)) {
        const encryptedPassword = CryptoJS.AES.encrypt(this.password, CONFIG.CRYPTO_SECRET_KEY).toString();
        sessionStorage.setItem('pwd',encryptedPassword);
      }
    // TODO: Need to remove this condition once OKTA configuration is enabled
    if (this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) {
      const isOktaEnabled = sessionStorage.getItem('isOktaEnabled') ? JSON.parse(sessionStorage.getItem('isOktaEnabled')) : '';
      const isSSOID = sessionStorage.getItem('ssoId') ? sessionStorage.getItem('ssoId') : '';
      if(isOktaEnabled && !this.otpInfo.loginWithOtp) {
        if (isSSOID) {
          this.showIdmMigrationLoader = true;
          this.idmMigrationLoaderMsg = '';
          this.oktaSignClick();
        } else {
          this.showIdmMigrationLoader = true;
          this.idmMigrationLoaderMsg = this._ToolTipService.getTranslateData('MESSAGES.IDM_MIGRATION_LOADER_MESSAGE');
          this.handleUserLogin();
        }
      } else {
        this.handleUserLogin();
      }
    } else {
      this.handleUserLogin();
    }
  }
  handleUserLogin() {
    let idToken;
    if (this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) {
      let decodedPassword = sessionStorage.getItem('pwd');
      if (decodedPassword) {
        const bytes = CryptoJS.AES.decrypt(decodedPassword, CONFIG.CRYPTO_SECRET_KEY);
        decodedPassword = bytes.toString(CryptoJS.enc.Utf8);
      }
      this.userName = this.userName || sessionStorage.getItem('usr') || '';
      this.password = this.password || decodedPassword || '';
      const oktaStorage = localStorage.getItem('okta-token-storage');
      if (!isBlank(oktaStorage) && typeof(oktaStorage) === 'string') {
        idToken = JSON.parse(oktaStorage).idToken.idToken;
      }
    }
    this._structureService.userLogin(this.userName, this.password, this.otp, this.otpInfo.sessionId).then((data) => {
        // Save username cookie if remember me is checked AND we have a username
        this.manageUsernamePersistence(this.userName);
      $('#signInButton').removeAttr("disabled");
      this.loginDetails = data;
      if (this.loginDetails.idmMigrated && this.loginDetails.ssoId && !this.loginDetails.first_login && (this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow')))) {
        sessionStorage.setItem('ssoId',this.loginDetails.ssoId ? this.loginDetails.ssoId : '');
        const message = this._ToolTipService.getTranslateData('MESSAGES.IDM_MIGRATION_COMPLETED');
        let notify = $.notify(message);
        setTimeout(function() {
        notify.update({'type': 'success', 'message': `<strong>${message}</strong>`});
        }, 1000);
        this.showIdmMigrationLoader = false;
        this.idmMigrationLoaderMsg = '';
      } else {
        this.showIdmMigrationLoader = false;
        if ((this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) && this.loginDetails.first_login) {
          sessionStorage.setItem("isOktaFirstLogin",JSON.stringify(this.loginDetails.first_login));
        }
        if (!this.loginDetails.first_login || this.loginDetails.status === 0) {
          sessionStorage.removeItem('pwd');
        }
        if (this.loginDetails.status === 0 || this.loginDetails.status === 6) {
          sessionStorage.removeItem('usr');
          sessionStorage.removeItem('isOktaEnabled');
          sessionStorage.removeItem('ssoId');
          this._structureService.socketConnect();
          console.log(this.loginDetails.cmisApiBaseUrl,"this.loginDetails.cmisApiBaseUrl");
          localStorage.setItem('cmisApiBaseUrl', this.loginDetails.cmisApiBaseUrl);
          let activityData = {
            activityName: "User Login",
            activityType: 'user access',
            activityDescription: "User (" + this.loginDetails.username + ") logged in successfully. Browser ServiceWorker plugin is "+((navigator.serviceWorker)?"enabled":"Disabled")
          };
          this._structureService.trackActivity(activityData);
          if (this.loginDetails.status === 0 && (this.loginDetails.registration_type == "1" || this.loginDetails.registration_type == "2" ) && this.loginDetails.first_login && this.loginDetails.referral_code != null) {
            const reqData = {
              "appUsage": {
                "tenantId": this.loginDetails.config.token,
                "userInfo": {
                  "userId": this.loginDetails.userId,
                  "email": this.userName,
                  "referalToken": this.loginDetails.referral_code
                },
                "events": {
                  "appId": "callbell",
                  "eventName": "first-login",
                  "eventDescription": "user first login",
                  "eventDateTime": moment().format("DD-MM-YYYY hh:mm A")
                }
              }
            };
            this.registrationservice.updateFirstLogin(reqData, this.loginDetails.config.wp_enroll_url).then((data: any) => {
              if (data.status['statusMessage'] != 'Success'){
                const activityData = {
                  activityName: "Failure First Login Update - Referral",
                  activityType: "manage user enrollment",
                  activityDescription: "Failed updated of first login details of user " + this.loginDetails.username + ". Full response is : " + JSON.stringify(data),
                };
                this._structureService.trackActivity(activityData);
              }
            });
          }
            let self=this;
            var sourceTenantId = '0';
            var title = 'CitusHealth';
            if(localStorage.getItem('sourceTenantId')) {
              sourceTenantId = localStorage.getItem('sourceTenantId');
              title = self.loginDetails.config.app_name;
            }
            console.log('_structureService.userDetails',self._structureService.userDetails);
            var userDataConfig = self._structureService.userDataConfig;
            var userDetail = self._structureService.userDetails;
            var iconPath = self._sharedService.assetsUrl+'/a/'+sourceTenantId+'/img/account-notification-logo.png';
            notifyMe(title,'',iconPath, 'Web push notification enabled successfully', null,userDetail,userDataConfig,function(response) {
              console.log("enter to login notifyme...");
              console.log('#notifyme response');
              console.log(response)
              if(!response.NotificationAvailable) {
                self._structureService.notifyMessage({
                  messge:response.status ? 'Web push notifications are disabled/unsupported in this browser' : 'Web push notifications are unsupported in this browser',
                  delay:1000,
                  type:'warning'
                });  
              }
            });
            //console.log('++++++++++++++++++');
            //console.log('---------------------')
            if(self.loginDetails.config.enable_video_chat=="1"){
              this._structureService.generateVidyoTocken({source:'login', userName:  self.loginDetails.displayName+'-'+self.loginDetails.tenantName}).then((data)=> {
                console.log('---generateVidyoTocken----response-'+JSON.stringify(data));
                this._sharedService.validVidyoToken.emit(data);
              });
            }
            //this._structureService.socket.emit("generateVidyoTocken",{source:'login', userName:  self.loginDetails.displayName+'-'+self.loginDetails.tenantName});
            this.socketEventSubscriptions.push(
              this._structureService.subscribeSocketEvent('pushNotifyToUser').subscribe((messageData) => {
                console.log('-----pushNotifyToUser-111---', messageData);
                var userDataConfig = self._structureService.userDataConfig;
                var userDetail = self._structureService.userDetails;
                  if(messageData.type == "visit") {
                    console.log("enter to appcomponent notifyme fun..."); 
                                
                    console.log('_structureService.userDetails',self._structureService.userDetails);
                    let userData = JSON.parse(self._structureService.userDetails);
                    messageData.group = userData.group;
                    console.log("messageData:",messageData);
                    notifyMe(title, messageData.data,iconPath,'visitdeepLink?'+messageData.visitId,messageData,userDetail,userDataConfig, function(response) {
                      console.log("enter to login notifyme2...");
                      console.log("response val:",response);
                      console.log('#notifyme response');
                      console.log(response);
                      if(response.redirectionValue == 1){
                          console.log("redirectionValue",response.redirectionValue);
                          self._sharedService.pushNotificationCall.emit(messageData.visitId);
                      }
                    });
                  } else {
                    notifyMe(title,messageData.data,iconPath, '', messageData,userDetail,userDataConfig,function(response) {
                      console.log("enter to login notifyme...");
                      console.log('#notifyme response');
                      console.log(response)
                    }); 
                  }
                              
              })
            );
            //  this.registerActivity();
             this.flagstop=true;
          /* setTimeout(function(){  
            self.startThisSessionTimer()
         },  5000); */
         if(this.loginDetails.status !== 6){
            let options = {
              maxParticipants: 5,
              userData: this.loginDetails
          }
            if(!this._commonVideoService.checkVideoPluginLoaded()){
              console.log('Login vidyo init ----- SUCCESS')
              this._commonVideoService.init('vidyo', options);
            }
            else{
              console.log('Login vidyo init not called')
            }
            this._structureService.inboxUnreadMessageCount = 0;
            this._structureService.inboxDataFirstPage = [];
            if (this.loginDetails.group === '3'){
  
              var patientTopicCount:any = 0;
              if(this.loginDetails.config.show_infusion_support == '1'){
                  patientTopicCount += 1;
              }
              if(this.loginDetails.config.enable_when_is_my_nurse_coming == '1'){
                  patientTopicCount += 1;
              }
              if(this.loginDetails.config.enable_where_is_my_delivery == '1'){
                  patientTopicCount += 1;
              }
    
              console.log("patientTopicCount login:::", patientTopicCount);
              localStorage.setItem('patientTopicCount',patientTopicCount);
              /*if(patientTopicCount){
                this.router.navigate(['faq']);
              }else{*/
                this.router.navigate([this.loginDetails.defaultPage || '/profile']);
              //}
              
            }else{
              this.router.navigate([this.loginDetails.defaultPage || '/profile']);
            }
          }else{
            this.router.navigate(['reset-password/enrol']);
          }
          if (this.otpInfo.loginWithOtp) {
            this.resetOtpFields();
          }
          console.log("===================1================");
        } else if(this.loginDetails.status === 2) {
          console.log("================2===================");
          var notify = $.notify('Please contact administrator to activate this account');
          setTimeout(function() {
              notify.update({'type': 'danger', 'message': '<strong>Please contact administrator to activate this account</strong>'});
          }, 1000);
          
          var activityData = {
                activityName: "Failure User Login",
                activityType: "user access",
                activityDescription: "User account activation pending (" +  this.userName + ")"
              };
          this._structureService.trackActivity(activityData);
          if ((this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) && idToken) {
            this._structureService.oktaLogout(idToken);
          }
        } else if(this.loginDetails.status === 10) {
          var notify = $.notify({'message':this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')},
              {
                  allow_dismiss: true,
                  delay: 0
              });
          setTimeout(function() {
              notify.update({'type': 'danger', 'message': '<strong>'+this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')+'</strong>', 'delay':0});
          }, 5000);
  
          var activityData = {
            activityName: "Failure User Login",
            activityType: "user access",
            activityDescription: "Account Disabled"
          };
          if ((this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) && idToken) {
            this._structureService.oktaLogout(idToken);
          }
        } else if(this.loginDetails.status === 8) {
          var notify = $.notify({'message':this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')},
              {
                  allow_dismiss: true,
                  delay: 0
              });
          setTimeout(function() {
              notify.update({'type': 'danger', 'message': '<strong>'+this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')+'</strong>', 'delay':0});
          }, 5000);
  
          var activityData = {
            activityName: "Failure User Login",
            activityType: "user access",
            activityDescription: "Account Deleted"
          };
          if ((this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) && idToken) {
            this._structureService.oktaLogout(idToken);
          }
        } else if(this.loginDetails.status === 1) {
          var notify = $.notify({'message':this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')},
              {
                  allow_dismiss: true,
                  delay: 0
              });
          setTimeout(function() {
              notify.update({'type': 'danger', 'message': '<strong>'+this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')+'</strong>', 'delay':0});
          }, 5000);
  
          var activityData = {
            activityName: "Failure User Login",
            activityType: "user access",
            activityDescription: "Account Disabled"
          };
          if ((this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) && idToken) {
            this._structureService.oktaLogout(idToken);
          }
        } else if(this.loginDetails.status === 5) {
          console.log("===============4====================");
          var notify = $.notify('User has been discharged');
          setTimeout(function() {
              notify.update({'type': 'danger', 'message': '<strong>User has been discharged</strong>'});
          }, 1000);
  
          var activityData = {
            activityName: "Failure User Login",
            activityType: "user access",
            activityDescription: "User discharged (" +this.userName + ")"
          };
          this._structureService.trackActivity(activityData);
          if ((this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) && idToken) {
            this._structureService.oktaLogout(idToken);
          }
        } else if(this.loginDetails.code === 40 || this.loginDetails.code === 50 || this.loginDetails.code === 60) {
          const notify = $.notify(this.loginDetails.message);
          setTimeout(() => {
              notify.update({'type': 'danger', 'message': this.loginDetails.message});
          }, 1000);
  
          const activityData = {
            activityName: "Failure User Login",
            activityType: "user access",
            activityDescription: "User authentication failed (" + this.userName + "). " + (this.loginDetails.message ? this.loginDetails.message : "")
          };
          this._structureService.trackActivity(activityData);
          if ((this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) && idToken) {
            this._structureService.oktaLogout(idToken);
          }
        } else if(this.loginDetails.code === 0 || this.loginDetails.code === 401 || this.loginDetails.status === 1) {
            console.log("==================3=================");
            var notify = $.notify('Invalid username or password');
            setTimeout(function() {
                notify.update({'type': 'danger', 'message': '<strong>Invalid username or password</strong>'});
            }, 1000);
    
            var activityData = {
              activityName: "Failure User Login",
              activityType: "user access",
              activityDescription: "User authentication failed (" + this.userName + "). " + (this.loginDetails.message ? this.loginDetails.message : "")
            };
          this._structureService.trackActivity(activityData);
        } else {
          console.log("================5===================");
          var notify = $.notify(this.loginDetails.message);
          setTimeout(function() {
              notify.update({'type': 'danger', 'message': '<strong>'+this.loginDetails.message+'</strong>'});
          }, 1000);
  
          var activityData = {
            activityName: "Failure User Login",
            activityType: "user access",
            activityDescription:  "Server error (" + this.userName + ")"
          };
          this._structureService.trackActivity(activityData);
          if ((this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) && idToken) {
            this._structureService.oktaLogout(idToken);
          }
        }
      }
      this.signInButtonVal = this.btnTextVal;
      this.loading=false;
    }).catch((ex) => {
       this.showIdmMigrationLoader = false;
        var notify = $.notify('Invalid username or password');
        setTimeout(function() {
            notify.update({'type': 'danger', 'message': '<strong>Invalid username or password</strong>'});
        }, 1000);
        this.signInButtonVal = this.btnTextVal;
        this.loading=false;
    });
  }
  forgotPasswordSend(){
      this.forgotPasswordLoader = true;
      this.fieldTextType=false;
      if (this.oktaForgotPassword) {
        this.userName = this.userName || '';
      } else {
        this.userName = this.userLogin.value['userName'];
      }
      this.validateEmail(this.userName) ;
      if(this.validemail){
       var userData = { "input": { "queryFilter": "uid eq " + "'" + this.userName + "'" } }
       console.log(userData);
      this.registrationservice.forgotPassword(userData).then((data) => {
        this.forgotPasswordLoader = false;
        this.dataResponse=data;
         if (this.dataResponse.status === 40) {          
            //var notify = $.notify('Forgot Password Request Failed');
            var notify = $.notify('This username does not exist in our system. Please check your username and try again.');
            setTimeout(function() {
            notify.update({'type': 'danger', 'message': '<strong>This username does not exist in our system. Please check your username and try again.</strong>'});
            }, 1000);

            const activityData = {
              activityName: "Failure Forgot Password",
              activityType: "user access",
              activityDescription: "Forgot password request failed for " + this.userName + ". The username is not exists"
            };
            this._structureService.trackActivity(activityData);

            //this.router.navigate(['/login']);
            console.log(data)   ;
         }
         else{
            var notify = $.notify('Please check your email for a link to reset your password.');
            setTimeout(function() {
            notify.update({'type': 'success', 'message': '<strong>Please check your email for a link to reset your password.</strong>'});
            }, 1000);
            this.enableForgotPassword = false;
            if (this.oktaForgotPassword) {
              const oktaBackButtonLink = document.querySelector('a[data-se="back-link"]') as HTMLAnchorElement;
              if (oktaBackButtonLink) {
                this.oktaForgotPassword = false;
                oktaBackButtonLink.click();
              }
              // this.goBackUpdateEmail(this.userLogin);
            }
            this.router.navigate(['/login']);
            const activityData = {
              activityName: "Forgot Password",
              activityType: "user access",
              activityDescription: this.userName + " requested forgot password"
            };
            this._structureService.trackActivity(activityData);
            console.log(data)   ;
         }

      }).catch((ex) => {});
    }
    else{
      // var notify = $.notify(' Please Enter Valid Email');
      $(".valid-email").css("display","block");
      setTimeout(function() {
      $(".valid-email").css("display","none");
      }, 3000);
      this.forgotPasswordLoader = false;
    }

  }

 validateEmail(email) {
  var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  if(re.test(email)){
    this.validemail =true;
  }
  else{
    this.validemail =false;
  }
}

forgotUsername() {
  this.enableForgotUsername = true;
}

loginWithOtpOrPassword() {
  this.loginWithOtp = !this.loginWithOtp;
  if (!this.loginWithOtp) {
    setTimeout(() => {
      this.setFocusOnFirstVisibleElm();
    }, 0);
    this.resetOtpInfo();
    if (this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) {
      this.showPassword();
    }
  } else {
    this.sendOTP();
  }
}

sendOTP() {
  this.userLogin.get('password').clearValidators();
  this.userLogin.get('password').updateValueAndValidity();
  this.otpLoader = true;
  if (!this.otpInfo.sessionId) {
    this.httpService
      .doPost(APIs.sendOtpEndpoint, { username: this.userName })
      .pipe(finalize(() => this.stopLoaderAndSetFocus() ))
      .subscribe(
        (res: OtpResponse) => {
          this.showExpiryTimer(res);
        },
        (error) => {
          this.loginWithOtp = !this.loginWithOtp;
          if (error.status === 404) {
            this.otpInfo.otpErrorMsg = this.httpService.getErrorMsg(error);
          }
        }
      );
  } else {
    this.httpService
      .doPut(APIs.reSendOtpEndpoint, {
        username: this.userName,
        sessionId: this.otpInfo.sessionId,
      })
      .pipe(finalize(() => this.stopLoaderAndSetFocus() ))
      .subscribe(
        (res: OtpResponse) => {
          this.showExpiryTimer(res);
        },
        (error) => {
          if (error.status === 404) {
            this.otpInfo.otpErrorMsg = this.httpService.getErrorMsg(error);
          }
        }
      );
  }
}

stopLoaderAndSetFocus() {
  this.otpLoader = false;
  setTimeout(() => {
    this.setFocusOnFirstVisibleElm();
  }, 0);
}

showExpiryTimer(res) {
  if(this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) {
    this.hidePassword();
  }
  this.formOtpInput.forEach((key) => {
    this.userLogin.get(key).setValidators([Validators.required]);
    this.userLogin.get(key).updateValueAndValidity();
  });
  this.otpInfo = {
    loginWithOtp: this.loginWithOtp,
    resendOtp: false,
    otpExpiryInMins: '',
    sessionId: res.sessionId,
    otpSuccessMsg: res.message,
    otpErrorMsg: ''
  };
  let otpExpiryTime = res.otpExpiryTime;
  this.ngZone.runOutsideAngular(() => {
    this.ngZone.run(() => {
      this.expiryInterval = setInterval(() => {
        otpExpiryTime -= 1;
        const minutes: number = Math.floor(otpExpiryTime / 60);
        this.otpInfo.otpExpiryInMins =
          this.addZeroForMmSs(minutes) +
          ':' +
          this.addZeroForMmSs(otpExpiryTime - minutes * 60);
        if (otpExpiryTime === 0) {
          this.otpInfo.resendOtp = true;
          this.otpInfo.otpSuccessMsg = '';
          clearInterval(this.expiryInterval);
        }
      }, 1000);
    });
  });
}

addZeroForMmSs(value) {
  return value < 10 ? '0' + value : value;
}

otpKeyUpEvent(event, index) {
  let pos = index;
  if (event.keyCode === 8 && event.which === 8) {
    pos = index - 1;
  } else {
    pos = index + 1;
  }
  if (pos > -1 && pos < this.formOtpInput.length) {
    this.otpFields._results[pos].nativeElement.focus();
  }
}

resetOtpInfo() {
  this.loginWithOtp = false;
  this.otpInfo = this.defaultOtpInfo;
  if (this.expiryInterval) {
    clearInterval(this.expiryInterval);
  }
  this.resetOtpFields();
}

resetOtpFields() {
  this.formOtpInput.forEach((key) => {
    this.userLogin.get(key).setValue('');
    this.userLogin.get(key).clearValidators();
    this.userLogin.get(key).updateValueAndValidity();
  });
  this.userLogin.get('password').setValidators([Validators.required]);
  this.userLogin.get('password').updateValueAndValidity();
}

get isOtpHasErrors(): boolean {
  return this.formOtpInput.some((field) => this.userLogin.get(field).errors && this.userLogin.get(field).hasError('required'))
}

get isOtpDirtyOrTouched(): boolean {
  return this.formOtpInput.every((field) => this.userLogin.get(field).dirty || this.userLogin.get(field).touched);
}

private get defaultOtpInfo() {
  return {
    loginWithOtp: false,
    resendOtp: false,
    otpExpiryInMins: '',
    sessionId: '',
    otpSuccessMsg: '',
    otpErrorMsg: '',
  };
}

  forgotPassword(){
    if(this.isOktaFlowEnabled || (!this.isOktaFlowEnabled && localStorage.getItem('authFlow'))) {
      this.handleOktaForgotPassword();
    } else {
      if (!this.otp2faInfo.twoFaEnabled) {
        this.resetOtpInfo();
      }
      this.enableForgotPassword = true;
    }
      const activityData = {
        activityName: "Forgot Password",
        activityType: "user access",
        activityDescription: 'accessed forgot password page',
      };
      this._structureService.trackActivity(activityData);
  }
  getUserLogin(){
    this.enableForgotPassword = false;
    this.fieldTextType=false;
  }
    stopThisSessionTimer() {
      console.log("==stopThisSessionTimer==");
      clearTimeout(this.startThisSessionTimerPromise);
    }
  
loginAftertimeout(){
   // $("body").removeClass("modal-open");
  //   $("body div").removeClass("fade");
       // $(".close-btn").trigger("click");
   $("#sessionoutlogin").modal("hide");
   $(".modal-backdrop").removeClass("fade");
  $(".modal-backdrop").removeClass("modal-backdrop");
   localStorage.removeItem('logoutSessionOut');
}
loginSuccess(data){
  console.log("loginsuccess enter....");
    $('#signInButton').removeAttr("disabled");
   
    console.log("===================================");
    console.log(data);
    console.log("===================================");
    this.loginDetails = data;
    if (this.loginDetails.status === 200 || this.loginDetails.status === 6) {
      //console.log(this.loginDetails.cmisApiBaseUrl,"this.loginDetails.cmisApiBaseUrl");
      this._structureService.socketConnect();
      localStorage.setItem('cmisApiBaseUrl', this.loginDetails.cmisApiBaseUrl);
      this._sharedService.idpLoginMessage.emit({message:"Login success and redirect to inbox.."});
      if (this.loginDetails.status === 200 && (this.loginDetails.registration_type == "1" || this.loginDetails.registration_type == "2" ) && this.loginDetails.first_login && this.loginDetails.referral_code != null) {
        const reqData = {
          "appUsage": {
            "tenantId": this.loginDetails.config.token,
            "userInfo": {
              "userId": this.loginDetails.userId,
              "email": this.userName,
              "referalToken": this.loginDetails.referral_code
            },
            "events": {
              "appId": "callbell",
              "eventName": "first-login",
              "eventDescription": "user first login",
              "eventDateTime": moment().format("DD-MM-YYYY hh:mm A")
            }
          }
        };
        this.registrationservice.updateFirstLogin(reqData, this.loginDetails.config.wp_enroll_url).then((data: any) => {
          if (data.status['statusMessage'] != 'Success'){
            const activityData = {
              activityName: "Failure First Login Update - Referral",
              activityType: "manage user enrollment",
              activityDescription: "Failed updated of first login details of user " + this.loginDetails.username + ". Full response is : " + JSON.stringify(data),
            };
            this._structureService.trackActivity(activityData);
          }
        });
      }
      this.loginDetails.status = (this.loginDetails.status==200)?0:this.loginDetails.status;
        let self=this;
        var sourceTenantId = '0';
        var title = 'CitusHealth';
        if(localStorage.getItem('sourceTenantId')) {
          sourceTenantId = localStorage.getItem('sourceTenantId');
          title = self.loginDetails.config.app_name;
        }
        console.log('_structureService.userDetails',self._structureService.userDetails);
        var userDataConfig = self._structureService.userDataConfig;
        var userDetail = self._structureService.userDetails;
        var iconPath = self._sharedService.assetsUrl+'/a/'+sourceTenantId+'/img/account-notification-logo.png';
        notifyMe(title,'',iconPath, 'Web push notification enabled successfully', null,userDetail,userDataConfig,function(response) {
          console.log("enter to login notifyme...");
          console.log('#notifyme response');
          console.log(response)
          if(!response.NotificationAvailable) {
            self._structureService.notifyMessage({
              messge:response.status ? 'Web push notifications are disabled/unsupported in this browser' : 'Web push notifications are unsupported in this browser',
              delay:1000,
              type:'warning'
            });  
          }
        });
        //console.log('++++++++++++++++++');
        //console.log('---------------------')
        if(this.loginDetails.config.enable_video_chat=="1"){
          this._structureService.generateVidyoTocken({source:'login', userName:  self.loginDetails.displayName+'-'+self.loginDetails.tenantName}).then((data)=> {
            //console.log('---generateVidyoTocken----response-'+JSON.stringify(data));
            this._sharedService.validVidyoToken.emit(data);
          });
        }
        //this._structureService.socket.emit("generateVidyoTocken",{source:'login', userName:  self.loginDetails.displayName+'-'+self.loginDetails.tenantName});
        this.socketEventSubscriptions.push(
          this._structureService.subscribeSocketEvent('pushNotifyToUser').subscribe((messageData) => {
            console.log('-----pushNotifyToUser-222---', messageData);
            var userDataConfig = self._structureService.userDataConfig;
            var userDetail = self._structureService.userDetails;
              if(messageData.type == "visit") {
                console.log("enter to appcomponent notifyme fun...");
                console.log('_structureService.userDetails',self._structureService.userDetails);
                let userData = JSON.parse(self._structureService.userDetails);
                messageData.group = userData.group;
                console.log("messageData:",messageData);
                notifyMe(title, messageData.data,iconPath,'visitdeepLink?'+messageData.visitId,messageData,userDetail,userDataConfig,function(response) {
                  console.log("enter to login notifyme2...");
                  console.log('#notifyme response');
                  console.log(response)
                });
              } else {
                notifyMe(title,messageData.data,iconPath, '', messageData,userDetail,userDataConfig,function(response) {
                  console.log("enter to login notifyme...");
                  console.log('#notifyme response');
                  console.log(response)
                });
              }
              
          })
        );
      if(this.loginDetails.status !== 6){
        let options = {
          maxParticipants: 5,
          userData: this.loginDetails
      }
        if(!this._commonVideoService.checkVideoPluginLoaded()){
          console.log('Login vidyo init ----- SUCCESS')
          this._commonVideoService.init('vidyo', options);
        }
        else{
          console.log('Login vidyo init not called')
        }
        this._structureService.inboxUnreadMessageCount = 0;
        this._structureService.inboxDataFirstPage = [];
        if (this.loginDetails.group === '3'){

          var patientTopicCount:any = 0;
          if(this.loginDetails.config.show_infusion_support == '1'){
              patientTopicCount += 1;
          }
          if(this.loginDetails.config.enable_when_is_my_nurse_coming == '1'){
              patientTopicCount += 1;
          }
          if(this.loginDetails.config.enable_where_is_my_delivery == '1'){
              patientTopicCount += 1;
          }

          //console.log("patientTopicCount login:::", patientTopicCount);
          localStorage.setItem('patientTopicCount',patientTopicCount);
          /*if(patientTopicCount){
            this.router.navigate(['faq']);
          }else{*/
            this.router.navigate([this.loginDetails.defaultPage || '/profile']);
          //}
          
        }else{
          this.router.navigate([this.loginDetails.defaultPage || '/profile']);
        }
        this._structureService.idpWorkflow=false;
        this.idpWorkflow =false;
      }else{
        this.router.navigate(['reset-password/enrol']);
      }
      //console.log("===================1================");
    } else if(this.loginDetails.status === 0){
      console.log("================0===================");
      $.notify({ 'message': this.loginDetails.message }, { delay: 0, type: 'danger' });
      setTimeout(function() {
          notify.update({'type': 'danger', 'message': '<strong>'+this.loginDetails.message+'</strong>'});
      }, 1000);
      this.idpWorkflow =false;
    } else if(this.loginDetails.status === 2) {
      console.log("================2===================");
      var notify = $.notify('Please contact administrator to activate this account');
      setTimeout(function() {
          notify.update({'type': 'danger', 'message': '<strong>Please contact administrator to activate this account</strong>'});
      }, 1000);
      
      var activityData = {
            activityName: "Failure User Login",
            activityType: "user access",
            activityDescription: "User account activation pending (" +  this.userName + ")"
          };
      this._structureService.trackActivity(activityData);

    } else if(this.loginDetails.status === 10) {
      console.log("================10===================");
      var notify = $.notify({'message':this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')},
          {
              allow_dismiss: true,
              delay: 0
          });
      setTimeout(function() {
          notify.update({'type': 'danger', 'message': '<strong>'+this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')+'</strong>', 'delay':0});
      }, 5000);

      var activityData = {
        activityName: "Failure User Login",
        activityType: "user access",
        activityDescription: "Account Disabled"
      };
    } else if(this.loginDetails.status === 8) {
      var notify = $.notify({'message':this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')},
          {
              allow_dismiss: true,
              delay: 0
          });
      setTimeout(function() {
          notify.update({'type': 'danger', 'message': '<strong>'+this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')+'</strong>', 'delay':0});
      }, 5000);

      var activityData = {
        activityName: "Failure User Login",
        activityType: "user access",
        activityDescription: "Account Deleted"
      };
    } else if(this.loginDetails.status === 1) {
      var notify = $.notify({'message':this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')},
          {
              allow_dismiss: true,
              delay: 0
          });
      setTimeout(function() {
          notify.update({'type': 'danger', 'message': '<strong>'+this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')+'</strong>', 'delay':0});
      }, 5000);

      var activityData = {
        activityName: "Failure User Login",
        activityType: "user access",
        activityDescription: "Account Disabled"
      };
    } else if(this.loginDetails.status === 5) {
      console.log("===============4====================");
      var notify = $.notify('User has been discharged');
      setTimeout(function() {
          notify.update({'type': 'danger', 'message': '<strong>User has been discharged</strong>'});
      }, 1000);

      var activityData = {
        activityName: "Failure User Login",
        activityType: "user access",
        activityDescription: "User discharged (" +this.userName + ")"
      };
      this._structureService.trackActivity(activityData);
    } else if(this.loginDetails.code === 0 || this.loginDetails.code === 401 || this.loginDetails.status === 1) {
        console.log("==================3=================");
        var notify = $.notify('Invalid username or password');
        setTimeout(function() {
            notify.update({'type': 'danger', 'message': '<strong>Invalid username or password</strong>'});
        }, 1000);

        var activityData = {
          activityName: "Failure User Login",
          activityType: "user access",
          activityDescription: "User authentication failed (" + this.userName + "). " + (this.loginDetails.message ? this.loginDetails.message : "")
        };
      this._structureService.trackActivity(activityData);
    } else {
      console.log("================5===================");
      var notify = $.notify(this.loginDetails.message);
      setTimeout(function() {
          notify.update({'type': 'danger', 'message': '<strong>'+this.loginDetails.message+'</strong>'});
      }, 1000);

      var activityData = {
        activityName: "Failure User Login",
        activityType: "user access",
        activityDescription:  "Server error (" + this.userName + ")"
      };
      this._structureService.trackActivity(activityData);
    }

}

  get isReturnSSOScreen() {
    return this._structureService.isTokenOrCodeinURL;
  }
  /**
   * OKTA functions start from here
   */
  // TODO: okta widget supports Promise, need to check with observable
  async oktaWidgetInit(): Promise<void> {
    try {
      this.user = await this.oktaSignIn.authClient.token.getUserInfo();
      if(this.user && isBlank(this._structureService.userDetails)) {
        this.showIdmMigrationLoader = true;
        this.handleUserLogin();
      } else {
        // redirect to inbox
        this.router.navigate(['/inbox']);
      }
    } catch (error) {
      this.showStepLogin();
    }
  }
  showStepLogin() {
    // TODO: okta widget supports Promise, need to check with observable
    return new Promise((resolve, reject) => {
      if (this.oktaSignIn) {
        this.oktaSignIn.off();
        this.oktaSignIn.remove();
      }

      // When the authorization flow is complete there will be a redirect to Okta.
      // Okta's servers will process the information and then redirect back to your application's `redirectUri`
      // If successful, an authorization code will exist in the URL as the "code" query parameter
      // If unsuccesful, there will be an "error" query parameter in the URL
      if (this.oktaSignIn.showSignInAndRedirect) {
        this.oktaSignIn.showSignInAndRedirect({
          el: `#${this.elementId}`
        }).then((res) => {
          // Check if authentication was successful
          if (res.status === 'SUCCESS') {
            this.user = res.tokens.idToken.claims.email;
            // Do something with the successful response
            resolve(res);
          } else {
            // Handle unsuccessful authentication
            console.error('Authentication failed:', res);
            reject(res);
          }
        }).catch((error) => {
          console.error('Error during Okta widget display:', error);
          // Reject the Promise if there was an error
          reject(error);
          this.normalLoginFlow = true;
        });
      }
      this.oktaSignIn.on('error', (context, error) => {
        console.error(error);
        //reject(error);
      });
      this.oktaSignIn.on('pageRendered', (data) => {
      });
      this.oktaSignIn.on('afterRender', (context) => {
        this.ngZone.run(() => {
        if (context.controller == "primary-auth" && this.screenNumber == 1) {
          this.showIdmMigrationLoader = false;
          this.setOktaScreenWithBranding();
          this.afterWidgetRender();
        }
        if (context.controller === 'enroll-choices' || context.controller === 'mfa-verify') {
          this.showIdmMigrationLoader = false;
          this.isOktaMfaEnabled = true;
          const signoutLink = document.querySelector('[data-se="signout-link"]') as HTMLAnchorElement;
          if (signoutLink) {
            signoutLink.addEventListener('click', () => {  
              this.showIdmMigrationLoader = true;
                this.screenNumber = 1;
                this.isOktaMfaEnabled = false;
              this.goBackUpdateEmail(this.userLogin);
            });
          }
        }
        if (context.controller === 'forgot-password') {
          const label = document.querySelector('[for="account-recovery-username"]');
          if (label) {
            label.textContent =this._ToolTipService.getTranslateData('LABELS.LOGIN_USERNAME');
          }
        }
          if (context.controller === 'password-reset-email-sent') {
            const resetPassTextElement = document.querySelector('.resetpass-text');
            if (resetPassTextElement) {
              resetPassTextElement.remove();
            }
            const backButton = document.querySelector('[data-se="back-button"]') as HTMLAnchorElement;
            if (backButton) {
              this.oktaForgotPassword = false;
              backButton.click();
              this.goBackUpdateEmail(this.userLogin);
              this.showIdmMigrationLoader = false;
              this._structureService.notifyMessage({
                messge: this._ToolTipService.getTranslateData('SUCCESS_MESSAGES.RESET_PASSWORD_MESSAGE'),
                delay: 1000,
                type: 'success'
              });
            }
        }
        });
      });
      this.oktaSignIn.on('afterError', (context,error) => {
        let notifyMessage;
        this.showIdmMigrationLoader = false;
        if(error.statusCode) {
          const errorCode = error.xhr && error.xhr.responseJSON && error.xhr.responseJSON.errorCode;
          if (error.xhr && errorCode === 'E0000011') {
            notifyMessage = error.xhr.responseJSON.errorSummary;
            this.screenNumber = 1;
            this.isOktaMfaEnabled = false;
            this.goBackUpdateEmail(this.userLogin);
          } else if(error.statusCode === 401 && errorCode === 'E0000004') {
            this._ToolTipService.getTranslateDataPipe('ERROR_MESSAGES.INVALID_USERNAME_PASSWORD').subscribe((data) => {
              notifyMessage = data;
            });
            this.signInButtonVal = this.btnTextVal;
            const signInButtonId = document.getElementById('signInButton');
            if(signInButtonId) {
              signInButtonId.removeAttribute('disabled');
            }
            this.loading=false;
          } else {
            notifyMessage = error.message;
            this.goBackUpdateEmail(this.userLogin);
          }
        } else {
          if (error.xhr && error.xhr.responseJSON.errorCode) {
            notifyMessage = error.xhr.responseJSON.errorSummary;
          }
          this.goBackUpdateEmail(this.userLogin);
        }
        const activityData = {
          activityName: "Failure OKTA User Login",
          activityType: "Authentication",
          activityDescription: `${notifyMessage}` || `${this._ToolTipService.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG')}`,
        };
        this._structureService.trackActivity(activityData);
        this._structureService.notifyMessage({
          messge: notifyMessage || this._ToolTipService.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG'),
          delay: 1000,
          type: 'danger'
        });
      });
      
      this.oktaSignIn.on('ready', (context) => {
        resolve(null);
      });
    });
  }
  getUsernameControl(): HTMLInputElement {
    return <HTMLInputElement>document.getElementById("okta-signin-username");
  }
  getPasswordControl(): HTMLInputElement {
    return <HTMLInputElement>document.getElementById("okta-signin-password");
  }
  hidePassword() {
    const passwordInput = document.getElementById('okta-signin-password') as HTMLInputElement;
    if (passwordInput) {
      passwordInput.value = '';
    }
    const passwordContainerControl = this.getPasswordContainerControl();
    if (passwordContainerControl) {
      passwordContainerControl.style.display = "none";
    }
  }
  showPassword() {
    const passwordContainerControl = this.getPasswordContainerControl();
    if (passwordContainerControl) {
      passwordContainerControl.style.display = "block";
    }
    const passwordInput = document.getElementById('okta-signin-password') as HTMLInputElement;
    if (passwordInput) {
      passwordInput.value = '';
      this._ToolTipService.getTranslateDataPipe('LABELS.LOGIN_PASSWORD').subscribe((data) => {
        passwordInput.setAttribute('placeholder', data);
      });
    }
  }
  getPasswordContainerControl() {
    const containerValue = document.getElementsByClassName("o-form-fieldset-container");
    if (containerValue && containerValue[0]) {
      return containerValue[0].children[1] as HTMLDivElement;
    }
  }
  hideSigninButton() {
    if (document.getElementById("okta-signin-submit")) {
      document.getElementById("okta-signin-submit").style.display = "none";
    }
  }
  showSigninButton() {
    document.getElementById("okta-signin-submit").style.display = "block";
  }
  handleBackClick() {
    this.screenNumber = 1;
    this.hideStepOneButtons();
  }
  getRememeberMeControl(): HTMLInputElement {
    let elements = document.getElementsByClassName("o-form-input-name-remember");
    if (elements.length) {
      return (<HTMLInputElement>elements[0]);
    }
    return null;
  }


  hideUsername() {
    const usernameControl = this.getUsernameControl();
    if (usernameControl) {
      usernameControl.setAttribute('readonly', 'readonly');
    }
    const formFieldsetContainer = document.getElementsByClassName("o-form-fieldset-container")[0];
    if (formFieldsetContainer) {
      formFieldsetContainer.children[0]['style'].display = "none";
    }
  }
  showUsername() {
    const formFieldsetContainer = document.getElementsByClassName("o-form-fieldset-container")[0];
    if (formFieldsetContainer) {
      formFieldsetContainer.children[0]['style'].display = "block";
    }
    const usernameInput = this.getUsernameControl();
    if (usernameInput) {
      usernameInput.removeAttribute('readonly');
      this._ToolTipService.getTranslateDataPipe('LABELS.LOGIN_USERNAME').subscribe((data) => {
        usernameInput.setAttribute('placeholder', data);
      });
      if (!isBlank(this.oktaForgotUserName)) {
        usernameInput.value = this.oktaForgotUserName;
        usernameInput.setAttribute('value', this.oktaForgotUserName);
        const inputEvent = new Event('input', { bubbles: true });
        const changeEvent = new Event('change', { bubbles: true });
        usernameInput.dispatchEvent(inputEvent);
        usernameInput.dispatchEvent(changeEvent);
        this.oktaForgotUserName = '';
      }
      if(this.getUsernameControl().disabled) {
        this.getUsernameControl().disabled = false;
      }
    }
  }
  setOktaScreenWithBranding() {
    if(document.getElementsByClassName("auth-footer")[0]) {
      document.getElementsByClassName("auth-footer")[0]['style'].display = "none";
    }
    if(document.getElementsByClassName("o-form-fieldset-container")[0]) {
      document.getElementsByClassName("o-form-fieldset-container")[0].children[0].children[0].children[0].classList.add("form-label");
      document.getElementsByClassName("o-form-fieldset-container")[0].children[1].children[0].children[0].classList.add("form-label");
    }
    const authHead = document.querySelector('div.okta-sign-in-header.auth-header');
    if(authHead && authHead.classList) {
      authHead.classList.add('src-auth-header');
    }
    const authContent = document.querySelector('div.auth-content');
    if(authContent && authContent.classList) {
      authContent.classList.add('src-auth-content');
    }
    const authContainer = document.querySelector('main.auth-container.main-container');
    if(authContainer && authContainer.classList) {
      authContainer.classList.add('src-auth-container');
    }
  }
  captureEnterKeyOnUsername() {
    const username = this.getUsernameControl();
    if(username) {
      username.addEventListener("keypress", e => {
        if (e.key == "Enter") {
          e.preventDefault();
          this.checkUsername(this.userLogin);
        }
      });
    }
    const password = this.getPasswordControl();
    if(password) {
      password.addEventListener("keypress", e => {
        if (e.key == "Enter") {
          e.preventDefault();
          this.login(this.userLogin);
        }
      });
    }
  }

  triggerClick(control: HTMLInputElement) {
    if (document.all) {
      control.click();
    } else {
      let evObj = document.createEvent('MouseEvents');
      evObj.initMouseEvent('click', true, true, window, 1, 12, 345, 7, 220, false, false, true, false, 0, null);
      control.dispatchEvent(evObj);
    }
  }

  triggerKeysEvent() {
    const keyboardEvent = document.createEvent('KeyboardEvent');
    const initMethod = typeof keyboardEvent.initKeyboardEvent !== 'undefined' ? 'initKeyboardEvent' : 'initKeyEvent';

    keyboardEvent[initMethod](
      'keydown', // event type: keydown, keyup, keypress
      true, // bubbles
      true, // cancelable
      window, // view: should be window
      false, // ctrlKey
      false, // altKey
      false, // shiftKey
      false, // metaKey
      40, // keyCode: unsigned long - the virtual key code, else 0
      0, // charCode: unsigned long - the Unicode character associated with the depressed key, else 0
    );
    const element = this.getUsernameControl();
    element.dispatchEvent(keyboardEvent);
  }

  hideStepOneButtons() {
    this.hidePassword();
    this.hideSigninButton();
    this.showUsername();
    const container = document.getElementsByClassName("primary-auth-container");
    if (container.length > 0) {
      (<HTMLDivElement>container[0]).style.display = "none";
    }
    this.hideErrorMessage();

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation, e) => {
        if (mutation.attributeName == "disabled" && this.screenNumber == 2) {
          const input = <HTMLInputElement>mutation.target;
          if (!input.disabled) {
            input.disabled = true;
          }
        }
      });
    });

    let usernameControl = this.getUsernameControl();

    if (usernameControl) {
      observer.observe(usernameControl, {
        attributes: true //configure it to listen to attribute changes
      });
      usernameControl.disabled = false;
      if (this.lastUserName) {
        usernameControl.value = this.lastUserName;
      }
      usernameControl.focus();
      usernameControl.setSelectionRange(0, 0);
      const usernameInput = this.getUsernameControl();
      if (usernameInput) {
        this._ToolTipService.getTranslateDataPipe('LABELS.LOGIN_USERNAME').subscribe((data) => {
          usernameInput.setAttribute('placeholder', data);
        });
      }
    }
  }

  afterWidgetRender() {
    this.captureEnterKeyOnUsername();
    this.hideStepOneButtons();
    // Set remembered username if exists
    const usernameInput = this.getUsernameControl();
    const username = this.structureService.rememberMeUsername;
    if (isPresent(username) && usernameInput) {
      usernameInput.value = username;
      const inputEvent = new Event('input', { bubbles: true });
      const changeEvent = new Event('change', { bubbles: true });
      usernameInput.dispatchEvent(inputEvent);
      usernameInput.dispatchEvent(changeEvent);
    }
  }

  private hideErrorMessage() {
    const errorContainer = document.getElementsByClassName('o-form-error-container')[0];
    errorContainer.innerHTML = '';

  }

  private displayErrorMessage(message: string) {
    const errorContainer = document.getElementsByClassName('o-form-error-container')[0];
    errorContainer.className = 'o-form-error-container o-form-has-errors';
    const errorHtml = `<div>	    <div class="okta-form-infobox-error infobox infobox-error" role="alert">	      <span class="icon error-16"></span>	      	        <p>${message}</p>	      	    </div>	  </div>`;
    errorContainer.innerHTML = errorHtml;
  }
  oktaSignClick(){
    const username = this.getUsernameControl().value;
    this.manageUsernamePersistence(username);
    document.getElementById('okta-signin-submit').click();
  }
  getFormControlValues(data) {
    if (data.form) {
      return data.form.controls;
    } else {
      return data.controls;
    }
  }
  handleOktaForgotPassword() {
    let isClicking = false;
    const existingUserName = this.getUsernameControl().value;
    const forgotPasswordLink = document.querySelector('.js-forgot-password') as HTMLElement;
    if (forgotPasswordLink) {
      forgotPasswordLink.click();
      this.hideForgotPasswordLabels = false;
      this.oktaForgotPassword = true;
      setTimeout(() => {
        const resetButton = document.querySelector('[data-se="email-button"]') as HTMLAnchorElement;
        if (resetButton) {
          resetButton.textContent = 'Submit'; 
          const usernameInput = document.querySelector('input[name="username"]') as HTMLInputElement; 
          if (usernameInput) {
            usernameInput.value = existingUserName; 
            usernameInput.setAttribute('value', existingUserName);
            const inputEvent = new Event('input', { bubbles: true });
            const changeEvent = new Event('change', { bubbles: true });
            usernameInput.dispatchEvent(inputEvent);
            usernameInput.dispatchEvent(changeEvent);
            this.oktaForgotUserName = usernameInput.value;
          }
          resetButton.classList.add('reset-password');
          const backToSignInLink = document.createElement('a');
          backToSignInLink.href = '';
          backToSignInLink.innerText = 'Log In';
          backToSignInLink.classList.add('back-to-signin');
          resetButton.insertAdjacentElement('afterend', backToSignInLink);
          const oktaBackButtonLink = document.querySelector('a[data-se="back-link"]') as HTMLAnchorElement;
          backToSignInLink.addEventListener('click', (event: MouseEvent) => {
            event.preventDefault();
            if (oktaBackButtonLink) {
              oktaBackButtonLink.click();
              this.oktaForgotPassword = false;
            }
        });
        const handleClick = (event: Event) => {
          if (event instanceof KeyboardEvent && event.key === 'Enter' || event instanceof MouseEvent) {
            if (isClicking) {
              return;
            }
            event.stopImmediatePropagation();
            event.preventDefault();
           if (usernameInput && usernameInput.value) {
              this._structureService.userNameCheck(usernameInput.value).subscribe((data: any) => {
                if (data.oktaEnabled && data.ssoId) {
                  this.showIdmMigrationLoader = true;
                  isClicking = true;
                  resetButton.removeEventListener('click', handleClick);
                  resetButton.click();
                } else {
                  this.userName = usernameInput.value;
                  this.forgotPasswordSend();
                }
              });
            }
          }
        };
        
        resetButton.addEventListener('click', handleClick, true);
        if (usernameInput) {
          usernameInput.addEventListener('keydown', handleClick, true);
        }
        oktaBackButtonLink.addEventListener('click', (event: MouseEvent) => {
         this.goBackUpdateEmail(this.userLogin);
        });
      }
      }, 500);
    }
  }
  // Need to remove this method once all _structureService & _ToolTipService usages is removed
  // eslint-disable-next-line no-underscore-dangle
  get _structureService() {
    return this.structureService;
  }
  // eslint-disable-next-line no-underscore-dangle
  get _ToolTipService() {
    return this.toolTipService;
  }
}
