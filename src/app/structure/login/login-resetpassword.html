<div class="cat__pages__login" [ngClass]="whiteLabel.topNavbar?'has-custom-nav-bar':''" style="background-image: url(assets/modules/pages/common/img/login/login-bg.jpg)">
    <div class="custom-nav-bar" *ngIf="whiteLabel.topNavbar">
        <div class="custom-nav-bar-logo" *ngIf="whiteLabel.topNavbar.logo"><img src="{{whiteLabel.topNavbar.logo}}"></div>
        <div class="custom-nav-bar-menu" *ngIf="whiteLabel.topNavbar.menu">
            <ul *ngIf="whiteLabel.topNavbar.menu.length">
                <li *ngFor="let navMenu of whiteLabel.topNavbar.menu">
                    <a [routerLink]="[navMenu.link]">{{navMenu.label}}</a>
                </li>
            </ul>
        </div>
    </div>
    <div class="cat__pages__login__header">
        <div class="row">
            <div class="col-lg-6 logo-citus">
                    <div class="side-logo">
                        <img *ngIf="sourceTenantId==1" src="./assets/modules/pages/common/img/login/account-logo-on-dark-bg.png"/>
                        <div class="login-logo-bg" *ngIf="sourceTenantId!=1">
                            <img src="{{_sharedService.assetsUrl}}/a/{{sourceTenantId}}/img/account-logo-on-white-bg.png" onerror="this.src='assets/modules/pages/common/img/account-logo-on-white-bg.png'"/>
                        </div>
                    </div>
            </div>
            <div class="col-lg-6 header-info">
                <p> <i class="fa fa-phone-square" aria-hidden="true"></i> <a class="color-white" href="tel:{{whiteLabel.contact.phone}}">{{whiteLabel.contact.phone}}</a><span><i class="fa fa-envelope" aria-hidden="true"></i> <a class="color-white" href="mailto:{{whiteLabel.contact.email}}">{{whiteLabel.contact.email}}</a></span></p>
            </div>
        </div>
    </div>
    <div class="cat__pages__login__block cat__pages__login__block--extended">

        <div class="row">
            <div class="col-xl-12">
                <div class="cat__pages__login__block__inner main-login-box">

                    <!-- Login Starts Here-->
                    <div *ngIf="!enableForgotPassword" class="cat__pages__login__block__form login-form">
                        <img _ngcontent-c2="" src="{{whiteLabel.banner.logo}}" *ngIf="whiteLabel.banner.logo" class="main-logo">
                        <img _ngcontent-c2="" src="{{_sharedService.assetsUrl}}/a/{{sourceTenantId}}/img/account-logo-on-white-bg.png" *ngIf="!whiteLabel.banner.logo" class="main-logo">
                        <h4 class="text-uppercase ml-2">
                            <strong>{{'LABELS.RESET_PASSWORD' | translate}}</strong>
                        </h4>
                        <br />
            <div class="w-100" id="reset-password">
              <reset-password
                (passwordDetails)="fromEnrol ? setpassword($event) : resetpassword($event)"
                [hideCurrentPassword]="true"
              ></reset-password>
            </div>
          </div>
          <!-- Login Ends Here -->
                    <div class="cat__pages__login__block__sidebar">
                        <h4 class="cat__pages__login__block__sidebar__title" [innerHTML]="whiteLabel.banner.heading"></h4>
                        <div class="cat__pages__login__block__sidebar__item">
                            {{whiteLabel.banner.body}}    
                        </div>
                        <div class="cat__pages__login__block__sidebar__place" [innerHTML]="whiteLabel.copyright"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
<!-- END: pages/login-beta -->