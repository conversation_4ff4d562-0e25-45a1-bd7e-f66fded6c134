import { Component, OnInit, ElementRef, Renderer2, ViewChild, ViewEncapsulation } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { RegistrationService } from '../registration/registration.service';
import { StructureService } from '../structure.service';
import { SharedService } from '../shared/sharedServices';
import { ToolTipService } from '../tool-tip.service';
import * as CryptoJS from 'crypto-js';
import { CONFIG } from 'custom-configs';
declare var $: any;
declare var moment: any;
@Component({
  selector: 'app-oncall-login',
  templateUrl: './login-resetpassword.html',
  styleUrls: ['./login-resetpassword.scss'],
  encapsulation: ViewEncapsulation.None
})
export class ResetPassword implements OnInit {
  password: string;
  @ViewChild('passwordField') passwordField: ElementRef;
  enableForgotPassword;
  result;
  fromEnrol: any = false;
  userDetails: any;
  resetPasswordDetails: any = '';
  currentYear = new Date().getFullYear();
  whiteLabel: any = {
    contact: {
      email: "<EMAIL>",
      phone: "(*************"
    },
    banner: {
      heading: "When we are better connected, everyone wins",
      body: ""
    },
    accountCreationHelp : "<p>If you are a patient looking to create an account, please contact your Care Provider. Access to the App has to be provided by your Care Provider.</p><p>If you are a staff member looking to create an account, please contact your Organization. Access to the App has to be provided by your Organization.</p>",
    copyright: "© "+this.currentYear+" All Rights Reserved."
  };
  sourceTenantId:any=1;
  constructor(private route: ActivatedRoute,
    private router: Router,
    private _formBuild: FormBuilder,
    private _structureService: StructureService,
    public _sharedService: SharedService,
    private registrationservice: RegistrationService,
    private renderer: Renderer2,
    private elementRef: ElementRef,
    private tooltipService: ToolTipService
  ) {

      renderer.listen(elementRef.nativeElement, 'click', (event) => {
       //show/hide password  tool-tip change
        if(event.target.id =='toggle_icon'){
          if($(event.target).hasClass('fa-eye')){
            $('#toggle_icon').attr('data-original-title', "Hide password").tooltip('show');
           }else{
            $('#toggle_icon').attr('data-original-title', "Show password").tooltip('show');
          }
        }
      //End of show/hide password tool-tip change
      });
   }
  ngOnInit() {
    this._structureService.displayProgress.emit(false);
    var userData = '""';
    if(this._structureService.userDetails){
        userData = this._structureService.userDetails;
    }
    this.userDetails = JSON.parse(userData);
    if(localStorage.getItem('sourceTenantId')){
      this.sourceTenantId = localStorage.getItem('sourceTenantId');
    }
    setTimeout(()=>{  
      if(localStorage.getItem('sourceTenantLabel')){
        this.whiteLabel = JSON.parse(localStorage.getItem('sourceTenantLabel'));
      }
    },  200);
    this.route.params.subscribe((params: Params) => {
      if(params['enrol'] == 'enrol'){
        this.fromEnrol = true;
        this._structureService.setCookie('fromEnrol', 'true', 1);
      }
    });
    const userLogin = this._structureService.getCookie('userLogin');
    const authToken = this._structureService.getCookie('authenticationToken');

    // tooltip initialization 
    setTimeout(function () {
      $('body').tooltip({selector: '[data-toggle="tooltip"]'});
      $('[data-toggle="tooltip"]').tooltip();
    },100);

  }
  setpassword(passwordDetails) {
    let oldPassword = sessionStorage.getItem('pwd');
    if (oldPassword) {
      const bytes = CryptoJS.AES.decrypt(oldPassword, CONFIG.CRYPTO_SECRET_KEY);
      oldPassword = bytes.toString(CryptoJS.enc.Utf8);
    }
    passwordDetails.oldPassword = oldPassword ? oldPassword : '';
    this.registrationservice
      .setPassword(passwordDetails)
      .then((data) => {
        this.resetPasswordDetails = data;
        if (this.resetPasswordDetails.status === 0) {
          sessionStorage.removeItem('pwd');
          const reqData = {
            appUsage: {
              tenantId: this.userDetails.config.token,
              userInfo: {
                userId: this.userDetails.userId,
                email: this.userDetails.username,
                referalToken: this.userDetails.referral_code
              },
              events: {
                appId: 'callbell',
                eventName: 'first-login',
                eventDescription: 'user first login',
                eventDateTime: moment().format('DD-MM-YYYY hh:mm A')
              }
            }
          };
          this.registrationservice.updateFirstLogin(reqData, this.userDetails.config.wp_enroll_url).then((data: any) => {
            if (data.status['statusMessage'] !== 'Success') {
              const activityData = {
                activityName: 'Failure First Login Update - Referral',
                activityType: 'manage user enrollment',
                activityDescription:
                  'Failed updated of first login details of user ' + this.userDetails.username + '. Full response is : ' + JSON.stringify(data)
              };
              this._structureService.trackActivity(activityData);
            }
          });
          this._structureService.notifyMessage({
            messge: this.tooltipService.getTranslateData('SUCCESS_MESSAGES.PASSWORD_RESET_SUCCESSFULLY'),
            delay: 1000,
            type: 'success'
          });
          // this._structureService.setCookie('status', this.resetPasswordDetails.status, 1);
          setTimeout(() => {
            this._structureService.logout();
          }, 2000);
        } else {
          this._structureService.notifyMessage({
            messge: this.resetPasswordDetails.errorMessage || this.tooltipService.getTranslateData('ERROR_MESSAGES.PASSWORD_RESET_FAILED'),
            delay: 0
          });
        }
      })
      .catch((ex) => {});
  }
  resetpassword(passwordDetails) {
    const resetCode = this.getParameterByName('code');
    const resetToken = this.getParameterByName('token');
    const realm = this.getParameterByName('realm').split('/')[2];
    const resetPasswordData = { input: { code: resetCode }, token: resetToken };
    if (realm) {
      resetPasswordData['realm'] = realm;
    }
    this.password = passwordDetails.password;
    this.registrationservice
      .resetPassword(resetPasswordData)
      .then((data) => {
        this.result = data;
        if (this.result.results && this.result.results.token) {
          const userData = { input: { password: this.password }, token: this.result.results.token, emailToken: resetToken };
          if (realm) {
            userData['realm'] = realm;
          }
          this.registrationservice
            .resetPassword(userData)
            .then((data) => {
              const result = JSON.parse(JSON.stringify(data));
              if (result.results && result.results.status && result.results.status.success) {
                const activityData = {
                  activityName: 'Reset Password',
                  activityType: 'user access',
                  activityDescription: 'User has reset the password'
                };
                this._structureService.trackActivity(activityData);
                this._structureService.notifyMessage({
                  messge: this.tooltipService.getTranslateData('SUCCESS_MESSAGES.PASSWORD_RESET_SUCCESSFULLY'),
                  delay: 3000,
                  type: 'success'
                });
                this.router.navigate(['/login']);
              } else {
                const activityData = {
                  activityName: 'Failure Reset Password',
                  activityType: 'user access',
                  activityDescription: `Failed the reset password process of user with code: ${resetCode}, 1st token: ${resetToken}, 2nd token: ${
                    this.result.results.token
                  } and the response is: ${JSON.stringify(data)}`
                };
                this._structureService.trackActivity(activityData);
                this._structureService.notifyMessage({
                  messge: this.tooltipService.getTranslateData('ERROR_MESSAGES.PASSWORD_RESET_FAILED'),
                  delay: 2000
                });
              }
            })
            .catch((ex) => {});
        } else {
          const activityData = {
            activityName: 'Failure Reset Password',
            activityType: 'user access',
            activityDescription: `Failed the reset password process of user with code: ${resetCode}, 1st token: ${resetToken} and the response is: ${JSON.stringify(
              data
            )}`
          };
          this._structureService.trackActivity(activityData);
          this._structureService.notifyMessage({
            type: 'danger',
            messge: this.tooltipService.getTranslateData('ERROR_MESSAGES.PASSWORD_RESET_FAILED'),
            delay: 6000
          });
          this.router.navigate(['/login']);
        }
      })
      .catch((ex) => {});
  }

  getParameterByName(name) {
    var url = window.location.href;
    name = name.replace(/[\[\]]/g, "\\$&");
    var regex = new RegExp("[?&]" + name + "(=([^&#]*)|&|#|$)"),
        results = regex.exec(url);
    if (!results) return null;
    if (!results[2]) return '';
    return decodeURIComponent(results[2].replace(/\+/g, " "));
  }
}
