import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;
declare var TelemetryAgentEventWidget: any;
@Component({
  selector: 'app-watchtower',
  templateUrl: './watchtower.html'
})

export class WatchTowerComponent implements OnInit {
  constructor(private route: ActivatedRoute,
    private router: Router
  ) { }
  ngOnInit() {
    TelemetryAgentEventWidget.getInstance({
      EventContainerId: 'eventDiv',
      ContainerWidth: 'auto',
      Heading: 'Events',
      ProjectApiKey: 'e2ea01a6-71c3-3664-96b2-08e60be64e26',
      ApiUrl: 'https://wt.opsfol.io/api/wtapp/',
      PartyId: 65,
      ReleaseStage: 'Development',
      defaultDays: '7',
      recordLimit: 20
    });
  }

}

