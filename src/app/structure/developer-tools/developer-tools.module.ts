import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';

import { GraphQLComponent } from './graphql.citushealth';
import { PowerBIComponent } from './powerbi.citushealth';
import { RoverComponent } from './rover.citushealth';
import { WatchTowerComponent } from './watchtower.citushealth';


export const routes: Routes = [
  { path: 'developer-tools/graphiql', component: GraphQLComponent },
  { path: 'developer-tools/powerbi-reports', component: PowerBIComponent },
  { path: 'developer-tools/graphql-rover', component: RoverComponent },
  { path: 'developer-tools/watchtower-telemetry', component: WatchTowerComponent }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes)
  ],
  declarations: [
    GraphQLComponent,
    PowerBIComponent,
    RoverComponent,
    WatchTowerComponent
  ]

})

export class DeveloperToolsModule { }
