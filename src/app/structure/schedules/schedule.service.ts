import { Injectable } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import { Subject } from 'rxjs/Subject';
import 'rxjs/add/operator/toPromise';
import { StructureService } from '../structure.service';
@Injectable()
export class ScheduleService {

  userDetails:any;
  userData:any = {};
  getUserSchedulesApi = '';
  constructor(
    private _http: Http,
    private _structureService:StructureService
  ) {
    this.userDetails = this._structureService.userDetails;
    this.userData = JSON.parse(this.userDetails);
    this.getUserSchedulesApi = this._structureService.apiBaseUrl+'citus-health/'+this._structureService.version;
  }
  getUserSchedules(userId) {
    const apiURL = this.getUserSchedulesApi + '/get-user-schedules-by-schedule-type.php';
    
    this.userDetails = this._structureService.userDetails;
    this.userData = JSON.parse(this.userDetails);
    let data = '';
    if(userId) {
      data =  "?userId=" + userId + "&tenantId=" + this._structureService.getCookie('tenantId');
    } else {
      data =  "?tenantId=" + this._structureService.getCookie('tenantId');
    }
    if(this.userData.config.enable_nursing_agencies_visibility_restrictions == 1) {
      data += "&nursingAgencyEnabled=1";
    }    
    let postData = {};
    if(this.userData.config.weekend_days && this.userData.config.weekend_days.length && this.userData.config.weekend_days.length > 1) {
      postData = {
        "weekendDays" : this.userData.config.weekend_days
      }
    }
    let apiConfig = { url: apiURL+data, requestType: 'http', data: postData};
    let response = this._structureService.requestData(apiConfig);
    return response;
  }
  saveUserSchedules(param, data) {
    const apiURL = this.getUserSchedulesApi + '/save-user-schedule-desktop.php';
    let dataParam =  '?tenantId='+ param.tenantId + '&scheduleFormat=' + param.scheduleFormat + '&formattedDate=' + param.formattedDate + '&dayStatus=' + param.dayStatus + '&weekStatus=' + param.weekStatus + '&typeOfSchedule=' + param.typeOfSchedule + '&mergeRecurring=' + param.mergeRecurring + '&getDay=' + param.getDay + '&escalated_schedule='+ param.escalated_schedule + '&formattedEndDate=' + param.formattedEndDate + '&timeSpan=' + param.timeSpan + '&savedTimeZone=' + param.savedTimeZone;
    if(param.isNightShift) {
      dataParam = dataParam + '&isNightShift=' + param.isNightShift + '&formattedDateNightShift=' + param.formattedDateNightShift + '&dayNumberNightShift=' + param.dayNumberNightShift; 
    }
    let apiConfig = { url: apiURL + dataParam, requestType: 'http', data: data };
    let response = this._structureService.requestData(apiConfig);
    return response;
  }

  editUserSchedule(param, data) {
    const apiURL = this.getUserSchedulesApi + '/edit-user-schedule-based-on-timezone.php';
    let dataParam = '';
    if(param.action == 'clearSlot') {
      dataParam =  '?userId='+ param.userId +'&tenantId='+ param.tenantId + "&action=" + param.action + '&scheduleFormat=' + param.scheduleFormat + '&formattedDate=' + param.formattedDate + '&dayStatus=' + param.dayStatus + '&weekStatus=' + param.weekStatus + '&typeOfSchedule=' + param.typeOfSchedule + '&mergeRecurring=' + param.mergeRecurring + '&getDay=' + param.getDay + '&schedulesToClear='+ param.schedulesToClear;
    } else {
      var count = 0;
      for(var key in param) {
        if(count == 0) {
          dataParam = '?' + key + '=' + param[key];
        } else {
          dataParam += '&' + key + '=' + param[key];
        }
        count++;
      }
    }
    let apiConfig = { url: apiURL + dataParam, requestType: 'http', data: data };
    let response = this._structureService.requestData(apiConfig);
    return response;
  }
}

