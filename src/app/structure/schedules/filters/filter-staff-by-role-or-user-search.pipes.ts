import { Component, OnInit, Pipe, PipeTransform, Injectable } from '@angular/core';
import { DatePipe } from '@angular/common';
declare var $: any;
declare var jQuery: any;

@Pipe({
    name: 'searchRolefilter'
  })
  
@Injectable()
export class SearchRoleFilterPipe implements PipeTransform {
transform(items: any[], field: string, value: string): any[] {
    if (!items){
    return [];
    }else {
    var result = items.filter( (item)=> {
        if(item[field.split('.')[0]][field.split('.')[1]].toLowerCase().indexOf(value.toLowerCase()) != -1) {
        item.filterUserStatus = false;
        return item;
        } else {
        item.filterUserStatus = true;
        var status = false;
        item['userList'].forEach((user) => {
            if("displayname" in user) {
            if(user.displayname.toLowerCase().indexOf(value.toLowerCase()) != -1) {              
                status = true
            }
            }
            if("name" in user) {
            if(user.name.toLowerCase().indexOf(value.toLowerCase()) != -1) {
                status = true
            }
            }
        });
        if(status) {
            return item
        }
        }
    });
    return result;
    }
}
}
@Pipe({
    name: 'searchfilterroletreeview'
  })
  
@Injectable()
export class SearchFilterRoleTreeViewPipe implements PipeTransform {
transform(items: any[], field: string, value: string): any[] {
    if (!items) {
    return [];
    } else {
    if(value) {
        var result = [];
        items.filter( (user, key)=> {
        if(user[field].toLowerCase().indexOf(value.toLowerCase()) != -1) {
            result.push(user);
        }
        })
        return result;
    } else {
        return items;
    }
    }
}
}