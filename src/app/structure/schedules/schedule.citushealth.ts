import { <PERSON>mpo<PERSON>, OnInit, <PERSON>ementRef, <PERSON>derer } from '@angular/core';
import { Router, ActivatedRoute, ParamMap } from '@angular/router';
import { HostListener } from '@angular/core';
import { Location } from '@angular/common';
let moment = require('moment/moment');
import { StructureService } from '../structure.service';
import { ScheduleService } from './schedule.service';
import * as io from "socket.io-client"; 
import { InboxService } from '../inbox/inbox.service';
import { FilterStaffByStatusWithSearchPipe, SearchFilterPipe, TimeZoneFormatPipe, ResetSelectedUsersPipe } from './filters/filter-staff-by-status-with-search.pipes';
import { configTimeZone } from "../../../environments/environment";
import { SearchFilterRoleTreeViewPipe, SearchRoleFilterPipe } from './filters/filter-staff-by-role-or-user-search.pipes';
import { SharedService } from '../../structure/shared/sharedServices';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;
declare var swal: any;


@Component({
  selector: 'app-schedule',
  templateUrl: './schedule.html'
})

export class ScheduleComponent implements OnInit {
  @HostListener('window:beforeunload') beforeunload() {
    if(this.selectedStaff) {
      localStorage.setItem('schedule-ActiveSchedule', this.selectedStaff['id']);
    }
    if(this.selectedScheduleType) {
      localStorage.setItem('schedule-SelectedScheduleType', this.selectedScheduleType);
    }
  }
  userData;
  days = ['Sun','Mon','Tue','Wed','Thu','Fri','Sat'];
   staffList = [];
   scheduleSelections = [];
   selectedStaff:any = {};
   selectedTimeZone = 0;//720
   selectedTimeZoneData:any = {};
   addNewSchedule:boolean = false;
   addNewScheduleByDate:boolean = false;
   previousPage;

   scheduleBehaviour:object = {
    recurence: 2,
    noRecurence: 3
   };
   recipientSearchName: '';
   timezone;
   nightSchedule = false;

   selectedEditSchedule:any = {
     title: '',
     event: {},
     startTime: '',
     endTime: '',
     validationErrorStatus: false,
     validation: {} ,
     selectedScheduleType: 2
   };
   scheduleTimeRanges:object = {
     startTime: '',
     endTime: ''
   };

  
  clearSlot:any = {
     action: false,
     confirmView: false
   };
   selectedScheduleType:any = 3;
   fcDayClick: boolean = false;
   fcDatePickerChange: boolean = false;
   scheduleRecurrenceType:any = '0';
   userScheduleDetails:any;
   profileImageBaseUrl = this._structureService.apiBaseUrl+'citus-health/avatars/';
   showClinicians:any=false;
   clinicianDataRoleWise:any;
   clinicianDataRoleWiseInitial:any;
   allUsersList:any;
   clinicianDataRoleWiseByPrivilege:any;
   allUsersListByPrivilege:any;
   checkComponentStatus = false;
   order = [];
   orderMain = [];
   selectedUserRoles = [];
   rolesContainingAnySelection = [];
   setupComponentStatus:any;
   fromDatemodalChange = false;
   allUsersListForMaskedMessage:any;
   resetIntroElements = false;
   contentLimit = 25;
   currentPage = 1;
   contentOffset=0;
   messageLoader:any = {
    messages : true
   }
  pageCountMessage: any = 0;
  totalCount;
  searchText;
  loadingData;
  allstaffhide = false;

  timeZones = [];
   calendarLoaderVisible:any = {
     fullcalendarEventsActivated: false,
     status: false
   };
  fullcalendarView:any = {
    intervalStart: null,
    intervalEnd: null
  }
  filteredSchedulesBeforeRender:any
  fetchUserScheduleFailureCount = 0;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private _structureService: StructureService,
    private _scheduleService:ScheduleService,
    private _sharedService: SharedService,
    private location: Location,
    private _inboxService: InboxService,
    private elementRef: ElementRef,
    private renderer: Renderer,
  ) {
    this.timezone = configTimeZone();
    renderer.listen(elementRef.nativeElement, 'click', (event) => {
      if (!( ($(event.target).hasClass('fc-event-container') || $(event.target).parents(".fc-event-container").length) || ($(event.target).hasClass('event-action-confirmation') || $(event.target).parents(".event-action-confirmation").length))) {
        if($(".event-action-confirmation").css("visibility")) {
          $(".event-action-confirmation").css("visibility","hidden");
        }
      }
    })
   }
  ngOnInit() {

    var config            = this._structureService.userDataConfig;
    var userDetails       = this._structureService.userDetails;
    var configData        = JSON.parse(config);
    this.userData = JSON.parse(userDetails);
    //this.selectedTimeZone = this.userData.config.tenant_timezone_offset; 
    this._structureService.introJsBtnHidden = false;

    $(window).resize( function() {
      if(parseInt($('#card-title').css('width'),10) - parseInt($('#card-clinicians').css('width'),10) == 0) {
        var widthset = "width: " + $('#card-title').css('width') + " !important;";
        //$("#cat__apps__calendar__container").css("cssText", widthset);
      } else {
        var widthset = "width: " + ((parseInt($('#card-title').css('width'),10) - parseInt($('#card-clinicians').css('width'),10)) - 20) + "px !important;";
       // $("#cat__apps__calendar__container").css("cssText", widthset);
      }
    });
    /**************Start */
    $('.js-example-basic-multiple').select2();
    function checkboxChanged() {
      var $this = $(this),
        checked = $this.prop("checked"),
        container = $this.parent(),
        siblings = container.siblings();

      container.find('input[type="checkbox"]')
        .prop({
          indeterminate: false,
          checked: checked
        })
        .siblings('label')
        .removeClass('custom-checked custom-unchecked custom-indeterminate')
        .addClass(checked ? 'custom-checked' : 'custom-unchecked');

      checkSiblings(container, checked);
    }

    function checkSiblings($el, checked) {
      var parent = $el.parent().parent(),
        all = true,
        indeterminate = false;

      $el.siblings().each(function () {
        return all = ($(this).children('input[type="checkbox"]').prop("checked") === checked);
      });

      if (all && checked) {
        parent.children('input[type="checkbox"]')
          .prop({
            indeterminate: false,
            checked: checked
          })
          .siblings('label')
          .removeClass('custom-checked custom-unchecked custom-indeterminate')
          .addClass(checked ? 'custom-checked' : 'custom-unchecked');

        checkSiblings(parent, checked);
      }
      else if (all && !checked) {
        indeterminate = parent.find('input[type="checkbox"]:checked').length > 0;

        parent.children('input[type="checkbox"]')
          .prop("checked", checked)
          .prop("indeterminate", indeterminate)
          .siblings('label')
          .removeClass('custom-checked custom-unchecked custom-indeterminate')
          .addClass(indeterminate ? 'custom-indeterminate' : (checked ? 'custom-checked' : 'custom-unchecked'));

        checkSiblings(parent, checked);
      }
      else {
        $el.parents("li").children('input[type="checkbox"]')
          .prop({
            indeterminate: true,
            checked: false
          })
          .siblings('label')
          .removeClass('custom-checked custom-unchecked custom-indeterminate')
          .addClass('custom-indeterminate');
      }
    }
    /*****************End */

    this.scheduleRecurrenceType = '0';
    $("#schedule-date-picker").datetimepicker({
      format:'MMMM D, YYYY',
      icons: {
        previous: 'fa fa-chevron-left',
        next: 'fa fa-chevron-right'
      },
      ignoreReadonly: true
      //minDate: moment().format('MMMM D, YYYY')
    });
    $("#schedule-start-date-picker-modal").datetimepicker({
      format:'MMMM D, YYYY',
      icons: {
        previous: 'fa fa-chevron-left',
        next: 'fa fa-chevron-right'
      },
      ignoreReadonly: true
      //minDate: moment().format('MMMM D, YYYY')
    });
    $("#schedule-end-date-picker-modal").datetimepicker({
      format:'MMMM D, YYYY',
      icons: {
        previous: 'fa fa-chevron-left',
        next: 'fa fa-chevron-right'
      },
      ignoreReadonly: true
      //minDate: moment().format('MMMM D, YYYY')
    });
    $("#schedule-from-date-picker-modal").datetimepicker({
      format:'MMMM D, YYYY',
      icons: {
        previous: 'fa fa-chevron-left',
        next: 'fa fa-chevron-right'
      },
      //showClear: true,
      ignoreReadonly: true
      //minDate: moment().format('MMMM D, YYYY')
    });
    $("#schedule-to-date-picker-modal").datetimepicker({
      format:'MMMM D, YYYY',
      icons: {
        previous: 'fa fa-chevron-left',
        next: 'fa fa-chevron-right',
        clear: 'fa fa-trash'
      },
      showClear: true,
      ignoreReadonly: true
      //minDate: moment().format('MMMM D, YYYY')
    });
    $("#schedule-date-picker").on("dp.change", (e) => {
      this.fcDatePickerChange = true;
      if(this.scheduleSelections && this.scheduleSelections.length) {
        this.scheduleSelections.forEach( function(schedule, index) {
          $('.cat__apps__calendar').fullCalendar('removeEvents', schedule.id);
        });
        this.scheduleSelections = [];
      }
      $('.cat__apps__calendar').fullCalendar('gotoDate', e.date);
      $('#schedule-type-2').html('Recurring on all ' + $('.cat__apps__calendar').fullCalendar('getDate').format('dddd'));
    });
    $("#schedule-start-date-picker-modal").on("dp.change", (e) => {
      if(e.date) {
        if(this.selectedEditSchedule['startTime'] && $('#edit-schedule-modal').is(':visible')) {
          this.selectedEditSchedule['startTime'] = '';  
        }
        if($( "#schedule-start-date-picker-modal" ).data("DateTimePicker").date() && $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").date()) {
          if($( "#schedule-start-date-picker-modal" ).data("DateTimePicker").date().format('YYYY-MM-DD') == $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").date().format('YYYY-MM-DD')) {
            $('#schedule-edit-end-time option').css('display', 'block');
          }
        }
        // if(!this.selectedEditSchedule['notCheckAllDay']) {
        //   this.selectedEditSchedule['startTime'] = '';
        // }
        if(moment($( "#schedule-end-date-picker-modal" ).data("DateTimePicker").minDate()).isSameOrAfter(e.date)) {
          $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").minDate(e.date.format('MMMM D, YYYY') );
          if(this.scheduleRecurrenceType != 1) {
            $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").maxDate(moment(e.date).add(1, 'days').format('MMMM D, YYYY') );
          }
        } else {
          if(this.scheduleRecurrenceType != 1) {
            $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").maxDate(moment(e.date).add(1, 'days').format('MMMM D, YYYY') );
          }
          $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").minDate(e.date.format('MMMM D, YYYY') );
        }
        if(this.selectedEditSchedule['action'] != 'edit') {
          $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").date(e.date.format('MMMM D, YYYY'));
        }
      }
      $('#modal-schedule-type-2').html('Recurring on all ' + $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('dddd'));
      if(this.selectedEditSchedule['startTime'] && this.selectedEditSchedule['endTime']) {
        let start, end;
        if(!this.selectedEditSchedule['allDay']) {
          start = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']);
          end = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD')+ ' ' + this.selectedEditSchedule['endTime']);
        } else {
          start = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 00:00');
          end = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD')+ ' 24:00');  
        }
        if(moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date()).isAfter($('#schedule-start-date-picker-modal').data("DateTimePicker").date())) {
          this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + end.format('LT');
          this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + end.format('LT');    
        } else {
          this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + end.format('LT');    
          this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + end.format('LT');    
        }
        if($('#schedule-start-date-picker-modal').data("DateTimePicker").date() && $('#schedule-end-date-picker-modal').data("DateTimePicker").date()) {
          start = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']);
          end = moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['endTime']);
          
          if(!start.isBefore(end)) {
            this.selectedEditSchedule['validation']['timeRange'] = true;
          } else {
            this.selectedEditSchedule['validation']['timeRange'] = false;        
          }
          if(this.scheduleRecurrenceType != 0) {
            if(this.selectedEditSchedule['validation']['timeRange']) {
              this.selectedEditSchedule['validationErrorStatus'] = true;
            } else {
              this.selectedEditSchedule['validationErrorStatus'] = false;
            }
            this.selectedEditSchedule['validation']['recurrenceType'] = false;  
          } else {
            this.selectedEditSchedule['validationErrorStatus'] = true;
            this.selectedEditSchedule['validation']['recurrenceType'] = true;
          }
        }
      } else {
        if(!this.selectedEditSchedule['allDay']) {
          this.selectedEditSchedule['validationErrorStatus'] = true;
          this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY');
          this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY');
        } else {
          if(moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date()).isAfter($('#schedule-start-date-picker-modal').data("DateTimePicker").date())) {
            this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 AM - ' + $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 PM';  
            this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 AM - ' + $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 PM';  
          } else {
            this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 AM - 12:00 PM';    
            this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 AM - 12:00 PM';    
          }  
        }
      }
      if(moment($( "#schedule-end-date-picker-modal" ).data("DateTimePicker").date()).isAfter(e.date)) {
        this.selectedEditSchedule['disableAllDaySelector'] = true;
        this.selectedEditSchedule['allDay'] = false;
      } else {
        this.selectedEditSchedule['disableAllDaySelector'] = false;
      }
      // if(!this.selectedEditSchedule['notCheckAllDay']) {
      //   if($("#schedule-start-date-picker-modal" ).data("DateTimePicker").date().format('YYYY-MM-DD') != $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").date().format('YYYY-MM-DD')) {
      //     this.selectedEditSchedule['disableAllDaySelector'] = true;
      //     if(this.selectedEditSchedule['allDay'] == true) {
      //       this.selectedEditSchedule['allDay'] = false;
      //       this.selectedEditSchedule['startTime'] = '';
      //       this.selectedEditSchedule['endTime'] = '';
      //     }
      //   } else {
      //     this.selectedEditSchedule['disableAllDaySelector'] = false;
      //   }
      // }
    });
    $("#schedule-end-date-picker-modal").on("dp.change", (e) => {
      if(this.selectedEditSchedule['endTime'] && $('#edit-schedule-modal').is(':visible')) {
        this.selectedEditSchedule['endTime'] = '';
      }
      if($( "#schedule-start-date-picker-modal" ).data("DateTimePicker").date() && $( "#schedule-end-date-picker-modal" ).data("DateTimePicker") && $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").date()) {
        if($( "#schedule-start-date-picker-modal" ).data("DateTimePicker").date().format('YYYY-MM-DD') == $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").date().format('YYYY-MM-DD')) {
          $('#schedule-edit-end-time option').css('display', 'block');
          if(this.selectedEditSchedule['startTime']) {
            let scheduleDurationCheckStart = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 00:30');   
            let scheduleDurationCheckEnd = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']);
            $('#schedule-edit-end-time option').css('display', 'block');
            while(scheduleDurationCheckEnd.isSameOrAfter(scheduleDurationCheckStart)) {
              $("#schedule-edit-end-time option:contains('" + scheduleDurationCheckStart.format('hh:mm A') + "')").css("display","none");//reverse is 'block'
              scheduleDurationCheckStart = scheduleDurationCheckStart.add(30, 'm');
            }
            if(this.selectedEditSchedule['endTime'] && $('#edit-schedule-modal').is(':visible') ) {
              this.selectedEditSchedule['endTime'] = '';
            }
          }
        } else {
          if(this.selectedEditSchedule['startTime'] && this.scheduleRecurrenceType != 1) {            
            let scheduleDurationCheckStart = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']).add(24, 'hours');
            let scheduleDurationCheckEnd = moment(moment(scheduleDurationCheckStart.format()).add(1, 'days').format('YYYY-MM-DD') + ' 00:00');
            scheduleDurationCheckStart = scheduleDurationCheckStart.add(30, 'm');
            $('#schedule-edit-end-time option').css('display', 'block');
            while(scheduleDurationCheckStart.isSameOrBefore(scheduleDurationCheckEnd)) {
              $("#schedule-edit-end-time option:contains('" + scheduleDurationCheckStart.format('hh:mm A') + "')").css("display","none");//reverse is 'block'
              scheduleDurationCheckStart = scheduleDurationCheckStart.add(30, 'm');
            }
          } else {
            if(this.scheduleRecurrenceType == 1) {
              $('#schedule-edit-end-time option').css('display', 'block');
            }
          }
        }
      }
      // if(!this.selectedEditSchedule['notCheckAllDay']) {
      //   this.selectedEditSchedule['endTime'] = '';
      // }
      this.selectedEditSchedule['validationErrorStatus'] = false;
      let start, end;
      if(this.selectedEditSchedule['startTime'] && this.selectedEditSchedule['endTime']) {
        if(!this.selectedEditSchedule['allDay']) {
          start = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']);
          end = moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['endTime']);
        } else {
          start = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 00:00');
          end = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD')+ ' 24:00');  
        }        
        if(moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date()).isAfter($('#schedule-start-date-picker-modal').data("DateTimePicker").date())) {
          this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + end.format('LT');  
          this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + end.format('LT');  
        } else {
          this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + end.format('LT');    
          this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + end.format('LT');    
        }

        if(!start.isBefore(end)) {
          this.selectedEditSchedule['validation']['timeRange'] = true;
        } else {
          this.selectedEditSchedule['validation']['timeRange'] = false;        
        }
        if(this.scheduleRecurrenceType != 0) {
          if(this.selectedEditSchedule['validation']['timeRange']) {
            this.selectedEditSchedule['validationErrorStatus'] = true;
          } else {
            this.selectedEditSchedule['validationErrorStatus'] = false;
          }
          this.selectedEditSchedule['validation']['recurrenceType'] = false;  
        } else {
          this.selectedEditSchedule['validationErrorStatus'] = true;
          this.selectedEditSchedule['validation']['recurrenceType'] = true;
        }
      } else {
        if(!this.selectedEditSchedule['allDay']) {
          this.selectedEditSchedule['validationErrorStatus'] = true;
          this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY');  
          this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY');  
        } else {
          if(moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date()).isAfter($('#schedule-start-date-picker-modal').data("DateTimePicker").date())) {
            this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 AM - ' + $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 PM';  
            this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 AM - ' + $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 PM';  
          } else {
            this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 AM - 12:00 PM';    
            this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 AM - 12:00 PM';    
          }
        }
      }
      if(moment($( "#schedule-start-date-picker-modal" ).data("DateTimePicker").date()).isBefore(e.date)) {
        this.selectedEditSchedule['disableAllDaySelector'] = true;
        this.selectedEditSchedule['allDay'] = false;
      } else {
        this.selectedEditSchedule['disableAllDaySelector'] = false;
      }
      // if(!this.selectedEditSchedule['notCheckAllDay']) {
      //   if($("#schedule-start-date-picker-modal" ).data("DateTimePicker").date().format('YYYY-MM-DD') != $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").date().format('YYYY-MM-DD')) {
      //     this.selectedEditSchedule['disableAllDaySelector'] = true;
      //     if(this.selectedEditSchedule['allDay'] == true) {
      //       this.selectedEditSchedule['allDay'] = false;
      //       this.selectedEditSchedule['startTime'] = '';
      //       this.selectedEditSchedule['endTime'] = '';
      //     }
      //   } else {
      //     this.selectedEditSchedule['disableAllDaySelector'] = false;
      //   }
      // }
    });
    $('#schedule-from-date-picker-modal').on("dp.change", (e) => {
      if(e.date) {
        $('#schedule-to-date-picker-modal').data('DateTimePicker').minDate(moment(e.date).add(1, 'days').format('MMMM D, YYYY'));
        if($( "#schedule-start-date-picker-modal" ).data("DateTimePicker") && $( "#schedule-start-date-picker-modal" ).data("DateTimePicker").date() && $( "#schedule-end-date-picker-modal" ).data("DateTimePicker") &&  $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").date() && $('#schedule-from-date-picker-modal').data("DateTimePicker") && $('#schedule-from-date-picker-modal').data("DateTimePicker").date()) {
          if($( "#schedule-start-date-picker-modal" ).data("DateTimePicker").date().format('YYYY-MM-DD') == $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").date().format('YYYY-MM-DD')) {
            $( "#schedule-start-date-picker-modal" ).data("DateTimePicker").date($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('MMMM D, YYYY'));
            $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").date($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('MMMM D, YYYY'));  
          } else {
            $( "#schedule-start-date-picker-modal" ).data("DateTimePicker").date($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('MMMM D, YYYY'));
            $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").date(moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date()).add(1, 'days').format('MMMM D, YYYY'));  
          }
        }
      }
      if(!this.selectedEditSchedule['event']['schedule_end']) {
        $('#schedule-to-date-picker-modal').data('DateTimePicker').clear();
      }
    })
    $('#edit-schedule-modal').on('shown.bs.modal', (e) => {
      this.recipientSearchName = '';
      if(this.selectedEditSchedule['userEvent'] == 'select') {
        $('#modal-schedule-type-2').html('Recurring on all ' + $('.cat__apps__calendar').fullCalendar('getDate').format('dddd'));  
      } else {
        if($('#schedule-start-date-picker-modal').data("DateTimePicker").date())
        $('#modal-schedule-type-2').html('Recurring on all ' + $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('dddd'));
      }
      if(this.selectedEditSchedule['action'] != 'edit') {
        this.scheduleRecurrenceTypeChange(this.scheduleRecurrenceType);
      }
      this.clearSelectedRoles();
    });
    $('#edit-schedule-modal').on('hide.bs.modal', (e) => {
      if($("#collapse-schedule-add-role").hasClass('show')) {
        $("#collapse-schedule-add-role").removeClass('show');
      }
      if($(".collapse-button").attr('aria-expanded') == "true") {
        $(".collapse-button").attr('aria-expanded', "false");
      }
      if(!$(".collapse-button").hasClass('collapsed')) {
        $(".collapse-button").addClass('collapsed')
      }
      if($(".collapse-button i").hasClass('fa fa-angle-double-up')) {
        $(".collapse-button i").removeClass('fa fa-angle-double-up');
        $(".collapse-button i").addClass('fa fa-angle-double-down')
      }
      this.clearSelectedRoles();
      this.clearScheduleSelection();
      this.addNewScheduleByDate = false;
      this.scheduleRecurrenceType = 0;
      this.clearSlot['confirmView'] = false;
      this.clearSlot['action'] = false;
      this.selectedEditSchedule['allDay'] = false;
      if(this.selectedEditSchedule['event']['reference_id']) {
        this.scheduleBehaviour['recurence'] = 2;
        delete this.selectedEditSchedule['startDate']; 
        delete this.selectedEditSchedule['currentDate'];
        delete this.selectedEditSchedule['edit_type']; 
        delete this.selectedEditSchedule['action'];
        this.fetchUserSchedules(this.selectedStaff['id'], false, $('.cat__apps__calendar').fullCalendar( 'getView' ).type);
      }
      $('#schedule-from-date-picker-modal').data("DateTimePicker").clear();
      $('#schedule-to-date-picker-modal').data("DateTimePicker").clear();
      this.selectedEditSchedule['validationErrorStatus'] = false;
      this.selectedEditSchedule['validation'] = {};
      $('#schedule-from-date-picker-modal').data("DateTimePicker").destroy();
      $('#schedule-to-date-picker-modal').data("DateTimePicker").destroy();
      $( "#schedule-start-date-picker-modal" ).data("DateTimePicker").maxDate(false);
      $("#schedule-from-date-picker-modal").datetimepicker({
        format:'MMMM D, YYYY',
        icons: {
          previous: 'fa fa-chevron-left',
          next: 'fa fa-chevron-right'
        },
        //showClear: true,
        ignoreReadonly: true
        //minDate: moment().format('MMMM D, YYYY')
      });
      $("#schedule-to-date-picker-modal").datetimepicker({
        format:'MMMM D, YYYY',
        icons: {
          previous: 'fa fa-chevron-left',
          next: 'fa fa-chevron-right',
          clear: 'fa fa-trash'
        },
        showClear: true,
        ignoreReadonly: true
        //minDate: moment().format('MMMM D, YYYY')
      });
    });
    $('#collapse-schedule-behaviour').on('shown.bs.collapse', function() {
      if($(".collapse-button i").hasClass('fa fa-angle-double-down')) {
        $(".collapse-button i").removeClass('fa fa-angle-double-down');
        $(".collapse-button i").addClass('fa fa-angle-double-up');
      }
    });
    $('#collapse-schedule-behaviour').on('hidden.bs.collapse', function () {
      if($(".collapse-button i").hasClass('fa fa-angle-double-up')) {
        $(".collapse-button i").removeClass('fa fa-angle-double-up');
        $(".collapse-button i").addClass('fa fa-angle-double-down')
      }
    });
    $('#collapse-schedule-add-role').on('shown.bs.collapse', function() {
      if($(".collapse-button i").hasClass('fa fa-angle-double-down')) {
        $(".collapse-button i").removeClass('fa fa-angle-double-down');
        $(".collapse-button i").addClass('fa fa-angle-double-up');
      }
    });
    $('#collapse-schedule-add-role').on('hidden.bs.collapse', function () {
      if($(".collapse-button i").hasClass('fa fa-angle-double-up')) {
        $(".collapse-button i").removeClass('fa fa-angle-double-up');
        $(".collapse-button i").addClass('fa fa-angle-double-down')
      }
    });
    if(localStorage.getItem('schedule-SelectedScheduleType')) {
      this.selectedScheduleType = localStorage.getItem('schedule-SelectedScheduleType');
    } else {
      this.selectedScheduleType = 3;
    }
    this.calendarLoaderVisible.status = true;
    this.initialiseCalendar([], this);
    if(this.route.snapshot.paramMap.get('staffId')) {
      localStorage.setItem('schedule-ActiveSchedule', this.route.snapshot.paramMap.get('staffId'));
      this.location.replaceState("/schedule");
    }
	this.calendarLoaderVisible.status = true;
  this._structureService.getAllTimeZones().then((timezones:any)=> {
    this.timeZones = timezones;
    console.log(this.timeZones);
    this.selectedTimeZoneData = this.timeZones.find((zone, i)=>{return zone.offset == this.userData.config.tenant_timezone_offset});
    this.selectedTimeZone = this.selectedTimeZoneData.offset;
    console.log(this.timeZones);
    this.getUserScehduleType('init', true);
    this._inboxService.getUsersListByRoleId(0, 0, '',true).then((users) => {
      this.clinicianDataRoleWise = [];
      this.allUsersListForMaskedMessage = users;
      console.log("users ::: ", this.allUsersListForMaskedMessage, this.allUsersListForMaskedMessage.length)
      this.allUsersListForMaskedMessage.forEach(value => {
        if(value.roleId != '3' && value.roleId != '20') {
          var role = { id: value.userid, name: value.displayname };
          var roleData = { id: value.tenantRoleId, name: value.role, tenantRoleId: value.tenantRoleId };
          if (value.userid != this.userData.userId && value.password) {
            if (value.tenantRoleId) {
              if (!this.clinicianDataRoleWise[value.tenantRoleId]) {
                this.clinicianDataRoleWise[value.tenantRoleId] = {};
              }
              if (!('userList' in this.clinicianDataRoleWise[value.tenantRoleId])) {
                this.clinicianDataRoleWise[value.tenantRoleId]['userList'] = [];
              }
              if (!('roleData' in this.clinicianDataRoleWise[value.tenantRoleId])) {
                this.clinicianDataRoleWise[value.tenantRoleId]['roleData'] = {};
              }
              this.clinicianDataRoleWise[value.tenantRoleId]['roleData'] = roleData;
              this.clinicianDataRoleWise[value.tenantRoleId]['userList'].push(role);
            }
          }
        }

      });
      this.clinicianDataRoleWise = this.clinicianDataRoleWise.filter(function (item) {
        return true;
      });
      this.checkComponentStatus = true;
      this.clinicianDataRoleWise.sort(function (a, b) {
        if (a.roleData.name < b.roleData.name) return -1;
        if (a.roleData.name > b.roleData.name) return 1;
        return 0;
      });
      console.log(this.clinicianDataRoleWise,"this.clinicianDataRoleWise");
    }).catch((ex) => {
    });
    this._structureService.getUsersListwithRolePrivilege(0, 0, '').then((users) => {
      console.log(users)
      this.clinicianDataRoleWiseByPrivilege = [];
      this.allUsersListByPrivilege = users;
      console.log("users ::: ", this.allUsersListByPrivilege, this.allUsersListByPrivilege.length)
      this.allUsersListByPrivilege.forEach(value => {
        if(value.roleId != '3'  && value.roleId != '20' && value.privileges.indexOf('autoMessageEscalation') != -1) {
          var role = { id: value.userid, name: value.displayname };
          var roleData = { id: value.tenantRoleId, name: value.role, tenantRoleId: value.tenantRoleId };
          if (value.userid != this.userData.userId && value.password) {
            if (value.tenantRoleId) {
              if (!this.clinicianDataRoleWiseByPrivilege[value.tenantRoleId]) {
                this.clinicianDataRoleWiseByPrivilege[value.tenantRoleId] = {};
              }
              if (!('userList' in this.clinicianDataRoleWiseByPrivilege[value.tenantRoleId])) {
                this.clinicianDataRoleWiseByPrivilege[value.tenantRoleId]['userList'] = [];
              }
              if (!('roleData' in this.clinicianDataRoleWiseByPrivilege[value.tenantRoleId])) {
                this.clinicianDataRoleWiseByPrivilege[value.tenantRoleId]['roleData'] = {};
              }
              this.clinicianDataRoleWiseByPrivilege[value.tenantRoleId]['roleData'] = roleData;
              this.clinicianDataRoleWiseByPrivilege[value.tenantRoleId]['userList'].push(role);
            }
          }
        }

      });
      this.clinicianDataRoleWiseByPrivilege = this.clinicianDataRoleWiseByPrivilege.filter(function (item) {
        return true;
      });
      this.checkComponentStatus = true;
      this.clinicianDataRoleWiseByPrivilege.sort(function (a, b) {
        if (a.roleData.name < b.roleData.name) return -1;
        if (a.roleData.name > b.roleData.name) return 1;
        return 0;
      });
      console.log(this.clinicianDataRoleWiseByPrivilege,"this.clinicianDataRoleWisebyprivilege");
    }).catch((ex) => {
    });
  });
    var arrayOfElements = ["schedule-header","search-bar","staff-users-list-to-schedule$$right","schedule-type-filter-btn-group","calendar-toolbar-right","btn-next-prev-grp","calendar-toolbar-left-edit"];
    this._structureService.setIntroOptions('schedule',arrayOfElements,true,(conditionalParams)=>{
      delete this._structureService.introSteps;
      if(($("#edit-schedule-modal").data('bs.modal')||{})._isShown) {
        this._structureService.introSteps = JSON.parse(JSON.stringify(this._structureService.introStepsCopy));
        let emitData = {};
        if(conditionalParams.setPositionAfterStart) {
          emitData = {value:true, setPositionAfterStart:conditionalParams.setPositionAfterStart, step: conditionalParams.step ? conditionalParams.step : 0 }
        } else {
          emitData = {value:true, step: conditionalParams.step ? conditionalParams.step : 0};
        }
        this._sharedService.startIntroCallBack.emit(emitData);
      } else {
        if(this.resetIntroElements) {
          var IntroElements = ["schedule-header","search-bar","staff-users-list-to-schedule$$right","schedule-type-filter-btn-group","calendar-toolbar-right","btn-next-prev-grp","calendar-toolbar-left-edit"];
          this._structureService.resetIntroOptions({introElements: IntroElements,setPositionAfterStart:'schedule',emit:'startIntroCallBack'});
          this.resetIntroElements = false;
        } else {
          this._structureService.introSteps = JSON.parse(JSON.stringify(this._structureService.introStepsCopy));  
          this._structureService.introSteps = this._structureService.introSteps.filter( (step)=> {
            if($(step.element).length && !$(step.element)[0].hidden && $(step.element + ':visible').length) {
              return true;
            } else {
              return false
            }
          })
          this._sharedService.startIntroCallBack.emit({value:true,setPositionAfterStart:'schedule'});
        }
      }
      this._structureService.introJsObject.onafterchange((targetElement)=> {
        if(($("#edit-schedule-modal").data('bs.modal')||{})._isShown) {
          let scrollTop = $(window).scrollTop();
          let elementOffset = $('#'+targetElement.id).offset().top;
          let distance = (elementOffset - scrollTop)-3;
          if($('.introjs-helperLayer').length && $('.introjs-helperLayer')[0].style.top) {
            $('.introjs-helperLayer')[0].style.top = distance + 'px';
          }
          if($('.introjs-tooltipReferenceLayer').length && $('.introjs-tooltipReferenceLayer')[0].style.top) {
            $('.introjs-tooltipReferenceLayer')[0].style.top = distance + 'px';
          }
        } else {
          if(this._structureService.introJsObject._currentStep == 0 ) {
            if($('.introjs-arrow')) {
              setTimeout(()=> {
                $('.introjs-arrow')[0].attributes['class'].nodeValue = "introjs-arrow top";
                $('.introjs-tooltip').css({"top":$('#schedule-header')[0].offsetTop+40+'px',"left":parseInt($('.introjs-tooltip').css('left'))-20 + 'px'})
              },500);
            }
          }
        }
      });
      this._structureService.introJsObject.oncomplete(() => {
        this._structureService.introJsObject.exit();
        delete this._structureService.introJsObject;
        delete this._structureService.introSteps;
      });
      this._structureService.introJsObject.onexit(()=> {
        delete this._structureService.introJsObject;
        delete this._structureService.introSteps;
      });
    })
    $('.introJs-Bs3-modal').on('click', function() {
      var containerClosest = $(this).closest('div.modal.sch').prop('id');
      setTimeout( ()=> {
        $('.introjs-overlay, .introjs-helperLayer, .introjs-tooltipReferenceLayer').appendTo('#'+containerClosest);}, 0);
      });
  }
  ngOnDestroy() {
    if(localStorage.getItem('schedule-SelectedScheduleType')) {
      localStorage.removeItem('schedule-SelectedScheduleType');
    }
    if(localStorage.getItem('schedule-ActiveSchedule')) {
      localStorage.removeItem('schedule-ActiveSchedule')
    }
    //$('#intro-btn').attr('hidden',true);
    this._structureService.unSubsc();
  }
  startIntro() {
    this.resetIntroElements = true;
    var IntroElements = [];
    if(this.selectedEditSchedule['action'] == 'save') {
      if($('.sch-edit-from:visible').length) {
        if($('#calendar-toolbar-left-edit:visible')) {
          IntroElements = ['schedule-settings-sch-type-time$$right','sch-event-type-recurring','sch-edit-add-action-btn'];
        } else {
          IntroElements = ['schedule-settings-sch-type-time$$right','sch-event-type-recurring'];
        }
      } else {
        if($('#calendar-toolbar-left-edit:visible')) {
          IntroElements = ['schedule-settings-sch-type-time$$right','sch-event-type','sch-edit-add-action-btn'];
        } else {
          IntroElements = ['schedule-settings-sch-type-time$$right','sch-event-type'];
        }
      }
    } else {
      if(this.selectedEditSchedule['edit_type'] == 1) {
        if($('#calendar-toolbar-left-edit:visible')) {
          IntroElements = ['schedule-settings-sch-type-time$$right','sch-event-type','sch-edit-add-action-btn'];
        } else {
          IntroElements = ['schedule-settings-sch-type-time$$right','sch-event-type'];
        }
      } else {
        if($('#calendar-toolbar-left-edit:visible')) {
          IntroElements = ['schedule-settings-sch-type-time$$right','sch-event-type-recurring','sch-edit-add-action-btn'];
        } else {
          IntroElements = ['schedule-settings-sch-type-time$$right','sch-event-type-recurring'];
        }
      }
    }
    this._structureService.resetIntroOptions({introElements: IntroElements,setPositionAfterStart:'schedule-add',emit:'startIntro'});
  }
  initialiseCalendar(events, scheduleClassData) {
    //let date = new Date();
    let eventAfterAllRenderOnEditCount = 0;
    $('.cat__apps__calendar').fullCalendar({
      height: 800,
      header: {
        left: 'prev, next edit',//edit
        center: 'title',
        right: 'month, agendaWeek, agendaDay'
      },
      buttonIcons: {
        prev: 'none fa fa-arrow-left',
        next: 'none fa fa-arrow-right',
        prevYear: 'none fa fa-arrow-left',
        nextYear: 'none fa fa-arrow-right'
      },
      nextDayThreshold : "00:00:00",
      //defaultDate: new moment().format('YYYY-MM-DD'),
      fixedWeekCount: false,
      customButtons: {
        edit: {
          text: 'Add Schedule',
          icon: 'none fa fa-plus',
          click: function() {
            if(scheduleClassData._structureService.introJsObject) {
              scheduleClassData._structureService.introJsObject.exit();
            }
            scheduleClassData.selectedEditSchedule['userEvent'] = 'select';
            scheduleClassData.addNewScheduleByDate = true;
            scheduleClassData.selectedEditSchedule['action'] = 'save';
            scheduleClassData.selectedEditSchedule['selectedScheduleType'] = (scheduleClassData.selectedScheduleType == 2 || scheduleClassData.selectedScheduleType == 3) ? 2 : 1;
            scheduleClassData.selectedEditSchedule['startTime'] = "";
            scheduleClassData.selectedEditSchedule['endTime'] = "";
            //scheduleClassData.selectedEditSchedule['notCheckAllDay'] = false;
            //scheduleClassData.selectedStaff.autoMessageEscalationPrivilege = true;
            $( "#schedule-start-date-picker-modal" ).data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
            if(moment($('.cat__apps__calendar').fullCalendar('getDate').format('YYYY-MM-DD') + ' 00:00').isSameOrAfter(moment(moment().format('YYYY-MM-DD') + ' 00:00'))) {
              $( "#schedule-from-date-picker-modal" ).data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
              $('#schedule-start-date-picker-modal').data("DateTimePicker").date($('.cat__apps__calendar').fullCalendar('getDate').format('MMMM D, YYYY'));
              if(moment($('.cat__apps__calendar').fullCalendar('getDate')).isAfter($( "#schedule-end-date-picker-modal" ).data("DateTimePicker").maxDate())) {
                $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").maxDate($('.cat__apps__calendar').fullCalendar('getDate').format('MMMM D, YYYY'));
              }
              $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").minDate($('.cat__apps__calendar').fullCalendar('getDate').format('MMMM D, YYYY'));
              $('#schedule-end-date-picker-modal').data("DateTimePicker").date($('.cat__apps__calendar').fullCalendar('getDate').format('MMMM D, YYYY'));
            } else {
              $( "#schedule-from-date-picker-modal" ).data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
              $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment().format('MMMM D, YYYY'));
              if(moment().isAfter($( "#schedule-end-date-picker-modal" ).data("DateTimePicker").maxDate())) {
                $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").maxDate(moment().format('MMMM D, YYYY'));
              }
              $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
              $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").maxDate(moment().add(1, 'days').format('MMMM D, YYYY'));
              $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment().format('MMMM D, YYYY'));
            }
            scheduleClassData.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY'); // + ' ' + scheduleClassData.selectedEditSchedule['startTime'].format('HH:mm') + ' - ' + scheduleClassData.selectedEditSchedule['endTime'].format('HH:mm');
            scheduleClassData.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY');
            scheduleClassData.selectedEditSchedule['event'] = {};
            //scheduleClassData.selectedEditSchedule['actualStartTime'] = eventData.start.format('HH:mm');
            //scheduleClassData.selectedEditSchedule['actualEndTime'] = eventData.end.format('HH:mm');            
            if(scheduleClassData.scheduleRecurrenceType == 0) {
              scheduleClassData.selectedEditSchedule['validationErrorStatus'] = true;
              scheduleClassData.selectedEditSchedule['validation']['recurrenceType'] = true;
            } else {
              scheduleClassData.selectedEditSchedule['validationErrorStatus'] = false;
              scheduleClassData.selectedEditSchedule['validation']['recurrenceType'] = false;
            }
            scheduleClassData.selectedEditSchedule['validation']['timeRange'] = true;
            scheduleClassData.selectedEditSchedule['validationErrorStatus'] = true;
            $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").minDate($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('MMMM D, YYYY') );
            $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").maxDate(moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date()).add(1, 'days').format('MMMM D, YYYY') );
            $('#edit-schedule-modal').modal('show');
            $('#modal-schedule-type-2').html('Recurring on all ' + $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('dddd'));
            scheduleClassData.scheduleRecurrenceType = 1;
          }
        }
      },
      eventLimit: true,
      events: events,
      selectOverlap: function(event) {
        if(event.id) {
          $('.cat__apps__calendar').fullCalendar('option', 'selectable', false);
          $('.cat__apps__calendar').fullCalendar('option', 'selectable', true);
          return false;
        } else {
          return true;
        }
      },
      selectable: true,
      select: function(start, end, jsEvent, view) {
        $(".event-action-confirmation").css("visibility", "hidden");
        if(view.name == 'agendaDay' && scheduleClassData.selectedStaff['id'] != 0) {
          if(moment($('.cat__apps__calendar').fullCalendar('getDate').format('MM-DD-YYYY') + ' 00:01', "MM-DD-YYYY HH:mm").isSameOrAfter(moment(moment().format('MM-DD-YYYY') + ' 00:01', "MM-DD-YYYY HH:mm"))) {
            scheduleClassData.selectedEditSchedule['selectedScheduleType'] = (scheduleClassData.selectedScheduleType == 2 || scheduleClassData.selectedScheduleType == 3) ? 2 : 1;
            scheduleClassData.selectedEditSchedule['userEvent'] = 'select';
            scheduleClassData.selectedEditSchedule['action'] = 'save';
            scheduleClassData.scheduleRecurrenceType = 0;
            let allDay = !start.hasTime() && !end.hasTime();
            var eventData = {
              id: (scheduleClassData.scheduleSelections.length) + 1,
              title: '[New Schedule]',
              start: allDay ? moment(start.format('YYYY-MM-DD') + " 00:00", "YYYY-MM-DD HH:mm") : start,
              end: allDay ? moment(end.format('YYYY-MM-DD') + " 24:00", "YYYY-MM-DD HH:mm") : end,
              color: '#acb7bf',
              recurrence_type: scheduleClassData.scheduleRecurrenceType,
              schedule_type: scheduleClassData.selectedEditSchedule['selectedScheduleType'] == 2 ? 'ONC' : 'ESC',
              allDaySelected: allDay ? true : false
            };
            $('.cat__apps__calendar').fullCalendar('renderEvent', eventData, true);
            if(allDay) {
              scheduleClassData.selectedEditSchedule['allDay'] = true;
            } else {
              scheduleClassData.selectedEditSchedule['allDay'] = false;
            }
            scheduleClassData.scheduleSelections.push(eventData);
            scheduleClassData.selectedEditSchedule['title'] = $('.cat__apps__calendar').fullCalendar('getDate').format('ddd MM-DD-YYYY') + ' ' + eventData.start.format('LT') + ' - ' + eventData.end.format('LT');
            scheduleClassData.selectedEditSchedule['actionModalTitle'] = $('.cat__apps__calendar').fullCalendar('getDate').format('ddd MM-DD-YYYY') + ' ' + eventData.start.format('LT') + ' - ' + eventData.end.format('LT');
            scheduleClassData.selectedEditSchedule['event'] = eventData;
            scheduleClassData.selectedEditSchedule['actualStartTime'] = eventData.start.format('HH:mm');
            scheduleClassData.selectedEditSchedule['actualEndTime'] = eventData.end.format('HH:mm');
            scheduleClassData.selectedEditSchedule['startTime'] = eventData.start.format('HH:mm');
            scheduleClassData.selectedEditSchedule['endTime'] = eventData.end.format('HH:mm') == '00:00' ? '24:00' : eventData.end.format('HH:mm');
            if(scheduleClassData.scheduleRecurrenceType == 0) {
              scheduleClassData.selectedEditSchedule['validationErrorStatus'] = true;
              scheduleClassData.selectedEditSchedule['validation']['recurrenceType'] = true;
            } else {
              scheduleClassData.selectedEditSchedule['validationErrorStatus'] = false;
              scheduleClassData.selectedEditSchedule['validation']['recurrenceType'] = false;
            }
            //scheduleClassData.selectedEditSchedule['notCheckAllDay'] = true;
            $( "#schedule-start-date-picker-modal" ).data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
            $( "#schedule-from-date-picker-modal" ).data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
            $('#schedule-start-date-picker-modal').data("DateTimePicker").date($('.cat__apps__calendar').fullCalendar('getDate').format('MMMM D, YYYY'));
            $('#schedule-end-date-picker-modal').data("DateTimePicker").date($('.cat__apps__calendar').fullCalendar('getDate').format('MMMM D, YYYY'));
            
            $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").minDate($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('MMMM D, YYYY') );
            $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").maxDate(moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date()).add(1, 'days').format('MMMM D, YYYY') );
            //scheduleClassData.selectedEditSchedule['notCheckAllDay'] = false;
            if(scheduleClassData.selectedEditSchedule['startTime'] && scheduleClassData.selectedEditSchedule['startTime'] == '00:00') {
              $('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('MMMM D, YYYY'));  
            } else {
              $('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate(moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date()).add(1, 'days').format('MMMM D, YYYY'));  
            }
            if(scheduleClassData.selectedEditSchedule['startTime']) {
              let scheduleDurationCheckStart = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 00:30');   
              let scheduleDurationCheckEnd = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['startTime']);
              $('#schedule-edit-end-time option').css('display', 'block');
              while(scheduleDurationCheckEnd.isSameOrAfter(scheduleDurationCheckStart)) {
                $("#schedule-edit-end-time option:contains('" + scheduleDurationCheckStart.format('hh:mm A') + "')").css("display","none");//reverse is 'block'
                scheduleDurationCheckStart = scheduleDurationCheckStart.add(30, 'm');
              }
            }
            $('#edit-schedule-modal').modal('show');
            scheduleClassData.scheduleRecurrenceType = 1;
            $('#modal-schedule-type-2').html('Recurring on all ' + $('.cat__apps__calendar').fullCalendar('getDate').format('dddd'));
          } else {
            $('.cat__apps__calendar').fullCalendar('unselect');
            scheduleClassData._structureService.notifyMessage({
              messge:'Past schedule cannot be modified',
              delay:1000,
              type:'warning'
            });
          }
        } else {
          $('.cat__apps__calendar').fullCalendar('unselect');
        }
      },
      eventClick: function(calEvent, jsEvent, view) {
        if(!scheduleClassData.calendarLoaderVisible.status) {
        $(".event-action-confirmation").css("visibility", "hidden");
        if((moment(jsEvent.currentTarget.attributes[1].value + ' 00:01', 'YYYY-MM-DD HH:mm').isSameOrAfter(moment(moment().format('MM-DD-YYYY') + ' 00:01', 'MM-DD-YYYY HH:mm'))) || (calEvent.ranges.start.indexOf('T') != -1 && moment(calEvent.ranges.end.split('T')[0] + ' 00:01', 'YYYY-MM-DD HH:mm').isSameOrAfter(moment(moment().format('MM-DD-YYYY') + ' 00:01', 'MM-DD-YYYY HH:mm')))) {
          if(calEvent.disableAllDaySelector) {
            scheduleClassData.selectedEditSchedule['disableAllDaySelector'] = true;
          }
          scheduleClassData.selectedEditSchedule['userEvent'] = 'edit';
          scheduleClassData.selectedEditSchedule['title'] = moment().format('ddd MM-DD-YYYY') + ' ' + calEvent.start.format('LT') + ' - ' + calEvent.end.format('LT');
          scheduleClassData.selectedEditSchedule['actionModalTitle'] = moment().format('ddd MM-DD-YYYY') + ' ' + calEvent.start.format('LT') + ' - ' + calEvent.end.format('LT');
          scheduleClassData.selectedEditSchedule['event'] = calEvent;
          if(calEvent.reference_id && !calEvent.schedule_split_conversion && !calEvent.schedule_merge_conversion) {
            scheduleClassData.scheduleBehaviour['recurence'] = 1;
            scheduleClassData.selectedEditSchedule['startDate'] = calEvent.ranges.start.replace(/-/g, '');
            scheduleClassData.selectedEditSchedule['currentDate'] = moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('YYYYMMDD');//$('.cat__apps__calendar').fullCalendar('getDate').format('YYYYMMDD');
            scheduleClassData.selectedEditSchedule['actualStartTime'] = calEvent.start.format('HH:mm');
            scheduleClassData.selectedEditSchedule['actualEndTime'] = calEvent.end.format('HH:mm');
            scheduleClassData.scheduleRecurrenceType = calEvent.schedule_recurrence_type;
            if(calEvent.schedule_type == 'ONC') {
              scheduleClassData.selectedEditSchedule['selectedScheduleType'] = 2;
            } else {
              scheduleClassData.selectedEditSchedule['selectedScheduleType'] = 1;
            }
            if(scheduleClassData.selectedEditSchedule['event']['time_span']) {
              if(scheduleClassData.selectedEditSchedule['event']['parent_reference_id'] != 0) {
                scheduleClassData.selectedEditSchedule['title'] = moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').add(-1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment(moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').add(-1, 'days').format('YYYY-MM-DD') + ' ' +scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[0].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT') + ' - ' + moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + moment(moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[1].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT');//$('.cat__apps__calendar').fullCalendar('getDate').add(-1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment($('.cat__apps__calendar').fullCalendar('getDate').add(-1, 'days').format('YYYY-MM-DD') + ' ' +scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[0].split(' ')[1]).format('LT') + ' - ' + moment($('.cat__apps__calendar').fullCalendar('getDate')).format('ddd MM-DD-YYYY') + ' ' + moment($('.cat__apps__calendar').fullCalendar('getDate').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[1].split(' ')[1]).format('LT');
                scheduleClassData.selectedEditSchedule['actionModalTitle'] = moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').add(-1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment(moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').add(-1, 'days').format('YYYY-MM-DD') + ' ' +scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[0].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT') + ' - ' + moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + moment(moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[1].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT');//$('.cat__apps__calendar').fullCalendar('getDate').add(-1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment($('.cat__apps__calendar').fullCalendar('getDate').add(-1, 'days').format('YYYY-MM-DD') + ' ' +scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[0].split(' ')[1]).format('LT') + ' - ' + moment($('.cat__apps__calendar').fullCalendar('getDate')).format('ddd MM-DD-YYYY') + ' ' + moment($('.cat__apps__calendar').fullCalendar('getDate').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[1].split(' ')[1]).format('LT');
              } else {
                scheduleClassData.selectedEditSchedule['title'] = moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + moment(moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[0].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT') + ' - ' + moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').add(1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment(moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').add(1, 'days').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[1].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT');//$('.cat__apps__calendar').fullCalendar('getDate').format('ddd MM-DD-YYYY') + ' ' +  moment($('.cat__apps__calendar').fullCalendar('getDate').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[0].split(' ')[1]).format('LT') + ' - ' + $('.cat__apps__calendar').fullCalendar('getDate').add(1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment($('.cat__apps__calendar').fullCalendar('getDate').add(1, 'days').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[1].split(' ')[1]).format('LT');
                scheduleClassData.selectedEditSchedule['actionModalTitle'] = moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + moment(moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[0].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT') + ' - ' + moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').add(1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment(moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').add(1, 'days').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[1].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT');//$('.cat__apps__calendar').fullCalendar('getDate').format('ddd MM-DD-YYYY') + ' ' +  moment($('.cat__apps__calendar').fullCalendar('getDate').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[0].split(' ')[1]).format('LT') + ' - ' + $('.cat__apps__calendar').fullCalendar('getDate').add(1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment($('.cat__apps__calendar').fullCalendar('getDate').add(1, 'days').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[1].split(' ')[1]).format('LT');
              }
            } else {
              if(scheduleClassData.selectedEditSchedule['event']['ranges']['start'].indexOf('T') == -1) {
                scheduleClassData.selectedEditSchedule['title'] = moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + calEvent.start.format('LT') + ' - ' + calEvent.end.format('LT');//$('.cat__apps__calendar').fullCalendar('getDate').format('ddd MM-DD-YYYY') + ' ' + calEvent.start.format('LT') + ' - ' + calEvent.end.format('LT');
                scheduleClassData.selectedEditSchedule['actionModalTitle'] = moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + calEvent.start.format('LT') + ' - ' + calEvent.end.format('LT');//$('.cat__apps__calendar').fullCalendar('getDate').format('ddd MM-DD-YYYY') + ' ' + calEvent.start.format('LT') + ' - ' + calEvent.end.format('LT');
              } else {
                scheduleClassData.selectedEditSchedule['title'] = moment(scheduleClassData.selectedEditSchedule['event']['ranges']['start']).format('ddd MM-DD-YYYY') + ' ' + calEvent.start.format('LT') + ' - ' + moment(scheduleClassData.selectedEditSchedule['event']['ranges']['end']).format('ddd MM-DD-YYYY') + ' ' + calEvent.end.format('LT');//$('.cat__apps__calendar').fullCalendar('getDate').format('ddd MM-DD-YYYY') + ' ' + calEvent.start.format('LT') + ' - ' + calEvent.end.format('LT');
                scheduleClassData.selectedEditSchedule['actionModalTitle'] = moment(scheduleClassData.selectedEditSchedule['event']['ranges']['start']).format('ddd MM-DD-YYYY') + ' ' + calEvent.start.format('LT') + ' - ' + moment(scheduleClassData.selectedEditSchedule['event']['ranges']['end']).format('ddd MM-DD-YYYY') + ' ' + calEvent.end.format('LT');//$('.cat__apps__calendar').fullCalendar('getDate').format('ddd MM-DD-YYYY') + ' ' + calEvent.start.format('LT') + ' - ' + calEvent.end.format('LT');
              }
            }
            let popUpWidth = $(".event-action-confirmation").width();
            if(popUpWidth < 265) {
              popUpWidth = 265;
            }
            if((jsEvent.clientX + popUpWidth) > window.innerWidth) {
              $(".event-action-confirmation").css("left",(jsEvent.clientX - popUpWidth)+"px");
            } else {
              $(".event-action-confirmation").css("left",jsEvent.clientX+"px");
            }
            let popUpHeight = $(".event-action-confirmation").height();
            if(popUpHeight < 132) {
              popUpHeight = 132;
            }
            if((jsEvent.clientY + popUpHeight + 100) > window.innerHeight) {
              $(".event-action-confirmation").css("top", (jsEvent.clientY - popUpHeight - 100)+"px");
            } else {
              $(".event-action-confirmation").css("top", jsEvent.clientY+"px");
            }
            $(".event-action-confirmation").css("visibility", "visible");
          } else {
            scheduleClassData.scheduleBehaviour['recurence'] = 1;
            scheduleClassData.selectedEditSchedule['startDate'] = calEvent.ranges.start.replace(/-/g, '');
            scheduleClassData.selectedEditSchedule['currentDate'] = moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('YYYYMMDD');//$('.cat__apps__calendar').fullCalendar('getDate').format('YYYYMMDD');
            scheduleClassData.selectedEditSchedule['actualStartTime'] = calEvent.start.format('HH:mm');
            scheduleClassData.selectedEditSchedule['actualEndTime'] = calEvent.end.format('HH:mm');
            scheduleClassData.scheduleRecurrenceType = calEvent.schedule_recurrence_type;
            if(calEvent.schedule_type == 'ONC') {
              scheduleClassData.selectedEditSchedule['selectedScheduleType'] = 2;
            } else {
              scheduleClassData.selectedEditSchedule['selectedScheduleType'] = 1;
            }
            if(scheduleClassData.selectedEditSchedule['event']['converted_timeSpan']) {
              if(scheduleClassData.selectedEditSchedule['event']['schedule_split_conversion_child']) {
                scheduleClassData.selectedEditSchedule['title'] = moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').add(-1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment(moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').add(-1, 'days').format('YYYY-MM-DD') + ' ' +scheduleClassData.selectedEditSchedule['event']['converted_timeSpan'].split('$')[0].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT') + ' - ' + moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + moment(moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['converted_timeSpan'].split('$')[1].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT');
                scheduleClassData.selectedEditSchedule['actionModalTitle'] = moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').add(-1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment(moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').add(-1, 'days').format('YYYY-MM-DD') + ' ' +scheduleClassData.selectedEditSchedule['event']['converted_timeSpan'].split('$')[0].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT') + ' - ' + moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + moment(moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['converted_timeSpan'].split('$')[1].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT');
              } else {
                scheduleClassData.selectedEditSchedule['title'] = moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + moment(moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['converted_timeSpan'].split('$')[0].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT') + ' - ' + moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').add(1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment(moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').add(1, 'days').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['converted_timeSpan'].split('$')[1].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT');
                scheduleClassData.selectedEditSchedule['actionModalTitle'] = moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + moment(moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['converted_timeSpan'].split('$')[0].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT') + ' - ' + moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').add(1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment(moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').add(1, 'days').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['converted_timeSpan'].split('$')[1].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT');
              }
            } else {
              scheduleClassData.selectedEditSchedule['title'] = moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + calEvent.start.format('LT') + ' - ' + calEvent.end.format('LT');
              scheduleClassData.selectedEditSchedule['actionModalTitle'] = moment(jsEvent.currentTarget.attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + calEvent.start.format('LT') + ' - ' + calEvent.end.format('LT');
            }
            let popUpWidth = $(".event-action-confirmation").width();
            if(popUpWidth < 265) {
              popUpWidth = 265;
            }
            if((jsEvent.clientX + popUpWidth) > window.innerWidth) {
              $(".event-action-confirmation").css("left",(jsEvent.clientX - popUpWidth)+"px");
            } else {
              $(".event-action-confirmation").css("left",jsEvent.clientX+"px");
            }
            let popUpHeight = $(".event-action-confirmation").height();
            if(popUpHeight < 132) {
              popUpHeight = 132;
            }
            if((jsEvent.clientY + popUpHeight + 100) > window.innerHeight) {
              $(".event-action-confirmation").css("top", (jsEvent.clientY - popUpHeight - 100)+"px");
            } else {
              $(".event-action-confirmation").css("top", jsEvent.clientY+"px");
            }
            $(".event-action-confirmation").css("visibility", "visible");  
          }
        } else {
          scheduleClassData._structureService.notifyMessage({
            messge:'Past schedule cannot be modified',
            delay:1000,
            type:'warning'
          });
        }
        } else {
          return false;
        }      
      },
      viewRender: function( view, element ) {
        $(".event-action-confirmation").css("visibility", "hidden");
        scheduleClassData.selectedScheduleType = 3;
        if(view.name == 'agendaDay') {
          scheduleClassData.addNewSchedule = true;
          if(!scheduleClassData.fcDayClick && !scheduleClassData.fcDatePickerChange && !scheduleClassData.datetimepickerManualChange) {
            scheduleClassData.datetimepickerManualChange = false;
            $('.cat__apps__calendar').fullCalendar('gotoDate', new Date());            
          }
          scheduleClassData.fcDayClick = false;
          scheduleClassData.fcDatePickerChange = false;
          scheduleClassData.datetimepickerManualChange = true;
          $('#schedule-date-picker').data("DateTimePicker").date($('.cat__apps__calendar').fullCalendar('getDate').format('MMMM D, YYYY'));
        } else {
          scheduleClassData.addNewSchedule = false;
        }  
        scheduleClassData.fullcalendarView.intervalStart = view.start;
        scheduleClassData.fullcalendarView.intervalEnd = view.end;  
      },
      eventAfterAllRender: function(view) {
        if(!scheduleClassData.calendarLoaderVisible.fullcalendarEventsActivated && scheduleClassData.calendarLoaderVisible.status) {
          scheduleClassData.calendarLoaderVisible.status = false;
        }
        if(scheduleClassData.calendarLoaderVisible.fullcalendarEventsActivated) {
          scheduleClassData.calendarLoaderVisible.fullcalendarEventsActivated = false;
        }
        $(".event-action-confirmation").css("visibility", "hidden");
      },
      eventAfterRender: function(event,element) {
          var tdColor = element[0].attributes[0].value, recurringAllDayTdColor, recurringAllWeekDayTdColor;
          var uniqueDate=element[0].attributes[1].value;
          if($("."+uniqueDate+".ONC").length > 1) {
            var oldUpdatedOncDate = '', oldupdatedOncId;
            $("."+uniqueDate+".ONC").each(function(i, obj) {
              if(oldUpdatedOncDate) {
                if(moment(oldUpdatedOncDate).isBefore(moment(obj.attributes[2].value))) {
                  oldUpdatedOncDate = obj.attributes[2].value;
                  oldupdatedOncId = obj.id;
                } else if(moment(oldUpdatedOncDate).isSame(moment(obj.attributes[2].value))) {
                  if(!obj.attributes[3].value) {
                    oldUpdatedOncDate = obj.attributes[2].value;
                    oldupdatedOncId = obj.id;
                  }
                }
              } else {
                oldUpdatedOncDate = obj.attributes[2].value;
                oldupdatedOncId = obj.id;
              }
            });

            var schedulecondition = $("#"+oldupdatedOncId).attr("data-schedule");
            var scheduleList = schedulecondition.split("-");
            if(parseInt(scheduleList[2])==1) {
            } else {
              if($("#" + uniqueDate+"-1-ONC").length>0) {
                if(parseInt(scheduleList[1])==0) {
                  $("#"+uniqueDate+"-3-ONC").remove();
                  $("#"+uniqueDate+"-2-ONC").remove();
                  $("#"+uniqueDate+"-4-ONC").remove();  
                } else if(parseInt(scheduleList[1])==2) {
                  $("#" + uniqueDate+"-1-ONC").remove();  
                }
              } else {
                if(parseInt(scheduleList[0]) == 3) {
                  $("#"+uniqueDate+"-3-ONC").remove();
                  $("#"+uniqueDate+"-1-ONC").remove();
                  $("#"+uniqueDate+"-4-ONC").remove();   
                }
              }
            }
          }

          if($("."+uniqueDate+".ESC").length > 1) {
            var oldUpdatedEscDate = '', oldupdatedEscId;
            $("."+uniqueDate+".ESC").each(function(i, obj) {
              if(oldUpdatedEscDate) {
                if(moment(oldUpdatedEscDate).isBefore(moment(obj.attributes[2].value))) {
                  oldUpdatedEscDate = obj.attributes[2].value;
                  oldupdatedEscId = obj.id;
                } else if(moment(oldUpdatedEscDate).isSame(moment(obj.attributes[2].value))) {
                  if(!obj.attributes[3].value) {
                    oldUpdatedEscDate = obj.attributes[2].value;
                    oldupdatedEscId = obj.id;
                  }
                }
              } else {
                oldUpdatedEscDate = obj.attributes[2].value;
                oldupdatedEscId = obj.id;
              }
            });

            var schedulecondition = $("#"+oldupdatedEscId).attr("data-schedule");
            var scheduleList = schedulecondition.split("-");
            if(parseInt(scheduleList[2])==1) {
              // $("#"+uniqueDate+"-3-ESC").remove();//removed last saved specific day behaviour
              // $("#"+uniqueDate+"-2-ESC").remove();
              // $("#"+uniqueDate+"-4-ESC").remove();
            } else {
              if($("#" + uniqueDate+"-1-ESC").length>0) {
                if(parseInt(scheduleList[1])==0) {
                  $("#"+uniqueDate+"-3-ESC").remove();
                  $("#"+uniqueDate+"-2-ESC").remove();
                  $("#"+uniqueDate+"-4-ESC").remove();  
                } else if(parseInt(scheduleList[1])==2) {
                  $("#" + uniqueDate+"-1-ESC").remove();  
                }
              } else {
                if(parseInt(scheduleList[0]) == 3) {
                  $("#"+uniqueDate+"-3-ESC").remove();
                  $("#"+uniqueDate+"-1-ESC").remove();
                  $("#"+uniqueDate+"-4-ESC").remove();   
                }
              }
            }
          }
      },
      eventRender: function(event, element, view) {
        let title = '';
        if(moment(event.start).format().indexOf('T') == '-1') {
          title = moment().format('ddd MM-DD-YYYY') + ' ' + event.start.format('LT') + ' - ' + event.end.format('LT');
          if(event.reference_id && !event.schedule_split_conversion && !event.schedule_merge_conversion) {
            if(event.time_span) {
              if(event.parent_reference_id != 0) {
                title = moment(element[0].attributes[1].value, 'YYYY-MM-DD').add(-1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment(moment(element[0].attributes[1].value, 'YYYY-MM-DD').add(-1, 'days').format('YYYY-MM-DD') + ' ' +event.time_span.split('$')[0].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT') + ' - ' + moment(element[0].attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + moment(moment(element[0].attributes[1].value, 'YYYY-MM-DD').format('YYYY-MM-DD') + ' ' + event.time_span.split('$')[1].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT');//$('.cat__apps__calendar').fullCalendar('getDate').add(-1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment($('.cat__apps__calendar').fullCalendar('getDate').add(-1, 'days').format('YYYY-MM-DD') + ' ' +scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[0].split(' ')[1]).format('LT') + ' - ' + moment($('.cat__apps__calendar').fullCalendar('getDate')).format('ddd MM-DD-YYYY') + ' ' + moment($('.cat__apps__calendar').fullCalendar('getDate').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[1].split(' ')[1]).format('LT');
              } else {
                title = moment(element[0].attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + moment(moment(element[0].attributes[1].value, 'YYYY-MM-DD').format('YYYY-MM-DD') + ' ' + event.time_span.split('$')[0].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT') + ' - ' + moment(element[0].attributes[1].value, 'YYYY-MM-DD').add(1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment(moment(element[0].attributes[1].value, 'YYYY-MM-DD').add(1, 'days').format('YYYY-MM-DD') + ' ' + event.time_span.split('$')[1].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT');//$('.cat__apps__calendar').fullCalendar('getDate').format('ddd MM-DD-YYYY') + ' ' +  moment($('.cat__apps__calendar').fullCalendar('getDate').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[0].split(' ')[1]).format('LT') + ' - ' + $('.cat__apps__calendar').fullCalendar('getDate').add(1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment($('.cat__apps__calendar').fullCalendar('getDate').add(1, 'days').format('YYYY-MM-DD') + ' ' + scheduleClassData.selectedEditSchedule['event']['time_span'].split('$')[1].split(' ')[1]).format('LT');
              }
            } else {
              title = moment(element[0].attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + event.start.format('LT') + ' - ' + event.end.format('LT');//$('.cat__apps__calendar').fullCalendar('getDate').format('ddd MM-DD-YYYY') + ' ' + calEvent.start.format('LT') + ' - ' + calEvent.end.format('LT');
            }
          } else {
            if(event.converted_timeSpan) {
              if(event.schedule_split_conversion_child) {
                title = moment(element[0].attributes[1].value, 'YYYY-MM-DD').add(-1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment(moment(element[0].attributes[1].value, 'YYYY-MM-DD').add(-1, 'days').format('YYYY-MM-DD') + ' ' +event.converted_timeSpan.split('$')[0].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT') + ' - ' + moment(element[0].attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + moment(moment(element[0].attributes[1].value, 'YYYY-MM-DD').format('YYYY-MM-DD') + ' ' + event.converted_timeSpan.split('$')[1].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT');
              } else {
                title = moment(element[0].attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + moment(moment(element[0].attributes[1].value, 'YYYY-MM-DD').format('YYYY-MM-DD') + ' ' + event.converted_timeSpan.split('$')[0].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT') + ' - ' + moment(element[0].attributes[1].value, 'YYYY-MM-DD').add(1, 'days').format('ddd MM-DD-YYYY') + ' ' + moment(moment(element[0].attributes[1].value, 'YYYY-MM-DD').add(1, 'days').format('YYYY-MM-DD') + ' ' + event.converted_timeSpan.split('$')[1].split(' ')[1], 'YYYY-MM-DD HH:mm').format('LT');
              }
            } else {
              title = moment(element[0].attributes[1].value, 'YYYY-MM-DD').format('ddd MM-DD-YYYY') + ' ' + event.start.format('LT') + ' - ' + event.end.format('LT');
            }
          }
        } else {
          title = moment(event.start).format('ddd MM-DD-YYYY') + ' ' + event.start.format('LT') + ' - ' + moment(event.end).format('ddd MM-DD-YYYY') + ' ' + event.end.format('LT');
        }
        if(event.schedule_type == 'ESC') {
          var myvar = '<br><img class=\'schedule-calender-icon\' src=\'assets/modules/dummy-assets/common/img/calender-lady.png\'/>';
          element.find(".fc-title").prepend("<img class='schedule-calender-icon' src='assets/modules/dummy-assets/common/img/calender-lady.png'/>");
          $(element).tooltip({
            title: title + myvar + event.title,//event.start.format('hh:mm a') + '-' + event.end.format('hh:mm a') + myvar + event.title,
            html:true
          });
        } else {
          var myvar = '<br><img class=\'schedule-calender-icon\' src=\'assets/modules/dummy-assets/common/img/calender-call.png\'/>';          
          element.find(".fc-title").prepend("<img class='schedule-calender-icon' src='assets/modules/dummy-assets/common/img/calender-call.png'/>");
          $(element).tooltip({
            title: title + myvar + event.title,//event.start.format('hh:mm a') + '-' + event.end.format('hh:mm a') + myvar +event.title,
            html:true
          });
        }
        if(event.ranges && event.ranges.start.indexOf('T') != -1) {
          return true;
        }
        if(event.ranges) {
          if(event.ranges.start == event.ranges.end) {
            if(scheduleClassData.selectedScheduleType == 1) {
              return (event.start.format().substring(0, event.start.format().lastIndexOf('T')) == event.ranges.start && event.schedule_type == 'ESC');
            } else if(scheduleClassData.selectedScheduleType == 2) {
              return (event.start.format().substring(0, event.start.format().lastIndexOf('T')) == event.ranges.start && event.schedule_type == 'ONC');
            } else {
              return event.start.format().substring(0, event.start.format().lastIndexOf('T')) == event.ranges.start;
            }
          } else if(!event.ranges.end) {
            let currentDate = new moment(event.start.format('YYYY-MM-DD') + ' 00:01');
            if(scheduleClassData.selectedScheduleType == 1) {
              if(event.occurrence_affected_dates) {
                let occurrence_affected_dates = event.occurrence_affected_dates.split(',');
                if(occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD')) > -1) {
                  return false;
                } else {
                  return (currentDate.isAfter(event.ranges.start) && event.schedule_type == 'ESC');
                }
              } else {
                return (currentDate.isAfter(event.ranges.start) && event.schedule_type == 'ESC');
              }
            } else if(scheduleClassData.selectedScheduleType == 2) {
              if(event.occurrence_affected_dates) {
                let occurrence_affected_dates = event.occurrence_affected_dates.split(',');
                if(occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD')) > -1) {
                  return false;
                } else {
                  return (currentDate.isAfter(event.ranges.start) && event.schedule_type == 'ONC');
                }
              } else {
                return (currentDate.isAfter(event.ranges.start) && event.schedule_type == 'ONC');
              }
            } else {
              if(event.occurrence_affected_dates) {
                let occurrence_affected_dates = event.occurrence_affected_dates.split(',');
                if(occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD')) > -1) {
                  return false;
                } else {
                  return currentDate.isAfter(event.ranges.start);
                }
              } else {
                return currentDate.isAfter(event.ranges.start);
              }
            }
          } else {
            let currentEndDate = new moment(event.start.format('YYYY-MM-DD') + ' 23:59');
            let currentStartDate = new moment(event.start.format('YYYY-MM-DD') + ' 00:01');
            if(scheduleClassData.selectedScheduleType == 1) {
              if(event.schedule_recurrence_type == 6 && !event.time_span) {
                if(event.occurrence_affected_dates) {
                  let occurrence_affected_dates = event.occurrence_affected_dates.split(',');
                  let occurrence_affected_dates_time = event.occurrence_affected_dates_time.split('#');
                  if(occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD')) > -1) {
                    if(event.schedule_split_conversion) {
                      if(occurrence_affected_dates_time[occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD'))].indexOf('child') != -1) {
                        if(event.schedule_split_conversion_child) {
                          return false;
                        } else {
                          return true;
                        }
                      } else {
                        if(event.schedule_split_conversion_child) {
                          return true;
                        } else {
                          return false;
                        }
                      }
                    } else {
                      return false
                    }
                  } else {
                    return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start)  && event.schedule_type == 'ESC');
                  }
                } else {
                  return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start)  && event.schedule_type == 'ESC');
                }
              } else {
                if(event.occurrence_affected_dates) {
                  let occurrence_affected_dates = event.occurrence_affected_dates.split(',');
                  if(event.parent_reference_id) {
                    if(occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD')) > -1) {
                      return false;
                    } else {
                      return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start)  && event.schedule_type == 'ESC');
                    }
                  } else {
                    if(event.schedule_split_conversion_child == true) {
                      if(occurrence_affected_dates.indexOf(moment(event.start).add(-1,'days').format('YYYYMMDD')) > -1) {
                        return false;
                      } else {
                        return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start) && event.schedule_type == 'ESC');
                      }
                    } else {
                      if(occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD')) > -1) {
                        return false;
                      } else {
                        return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start)  && event.schedule_type == 'ESC');
                      }
                    }
                  }
                  // if(event.schedule_split_conversion_child == true) {
                  //   if(occurrence_affected_dates.indexOf(moment(event.start).add(-1,'days').format('YYYYMMDD')) > -1) {
                  //     return false;
                  //   } else {
                  //     return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start) && event.schedule_type == 'ESC');
                  //   }
                  // } else {
                  //   if(occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD')) > -1) {
                  //     return false;
                  //   } else {
                  //     return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start)  && event.schedule_type == 'ESC');
                  //   }
                  // }
                } else {
                  return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start)  && event.schedule_type == 'ESC');
                }
              }
            } else if(scheduleClassData.selectedScheduleType == 2) {
              if(event.schedule_recurrence_type == 6 && !event.time_span) {
                if(event.occurrence_affected_dates) {
                  let occurrence_affected_dates = event.occurrence_affected_dates.split(',');
                  let occurrence_affected_dates_time = event.occurrence_affected_dates_time.split('#');
                  if(occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD')) > -1) {
                    if(event.schedule_split_conversion) {
                      if(occurrence_affected_dates_time[occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD'))].indexOf('child') != -1) {
                        if(event.schedule_split_conversion_child) {
                          return false;
                        } else {
                          return true;
                        }
                      } else {
                        if(event.schedule_split_conversion_child) {
                          return true;
                        } else {
                          return false;
                        }
                      }
                    } else {
                      return false
                    }
                  } else {
                    return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start)  && event.schedule_type == 'ONC');
                  }
                } else {
                  return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start)  && event.schedule_type == 'ONC');
                }
              } else {
                if(event.occurrence_affected_dates) {
                  let occurrence_affected_dates = event.occurrence_affected_dates.split(',');
                  if(event.parent_reference_id) {
                    if(occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD')) > -1) {
                      return false;
                    } else {
                      return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start)  && event.schedule_type == 'ONC');
                    }
                  } else {
                    if(event.schedule_split_conversion_child == true) {
                      if(occurrence_affected_dates.indexOf(moment(event.start).add(-1,'days').format('YYYYMMDD')) > -1) {
                        return false;
                      } else {
                        return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start) && event.schedule_type == 'ONC');
                      }
                    } else {
                      if(occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD')) > -1) {
                        return false;
                      } else {
                        return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start)  && event.schedule_type == 'ONC');
                      }
                    }
                  }
                  // if(event.schedule_split_conversion_child == true) {
                  //   if(occurrence_affected_dates.indexOf(moment(event.start).add(-1,'days').format('YYYYMMDD')) > -1) {
                  //     return false;
                  //   } else {
                  //     return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start) && event.schedule_type == 'ONC');
                  //   }
                  // } else {
                  //   if(occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD')) > -1) {
                  //     return false;
                  //   } else {
                  //     return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start)  && event.schedule_type == 'ONC');
                  //   }
                  // }
                } else {
                  return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start)  && event.schedule_type == 'ONC');
                }
              }
            } else {
              if(event.schedule_recurrence_type == 6 && !event.time_span) {//event.schedule_recurrence_type == 3 && !event.time_span changed as occurence affected concept chnaged only taking start date
                if(event.occurrence_affected_dates) {
                  let occurrence_affected_dates = event.occurrence_affected_dates.split(',');
                  let occurrence_affected_dates_time = event.occurrence_affected_dates_time.split('#');
                  if(occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD')) > -1) {
                    if(event.schedule_split_conversion) {
                      if(occurrence_affected_dates_time[occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD'))].indexOf('child') != -1) {
                        if(event.schedule_split_conversion_child) {
                          return false;
                        } else {
                          return true;
                        }
                      } else {
                        if(event.schedule_split_conversion_child) {
                          return true;
                        } else {
                          return false;
                        }
                      }
                    } else {
                      return false
                    }
                  } else {
                    return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start));
                  }
                } else {
                  return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start));
                }
              } else {
                if(event.occurrence_affected_dates) {
                  let occurrence_affected_dates = event.occurrence_affected_dates.split(',');
                  let child_occurrence_affected_dates;
                  if(event.parent_reference_id) {
                    if(occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD')) > -1) {
                      return false;
                    } else {
                      return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start));
                    }
                  } else {
                    if(event.schedule_split_conversion_child == true) {
                      if(occurrence_affected_dates.indexOf(moment(event.start).add(-1,'days').format('YYYYMMDD')) > -1) {
                        return false;
                      } else {
                        return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start));
                      }
                    } else {
                      if(occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD')) > -1) {
                        return false;
                      } else {
                        return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start));
                      }
                    }
                  }
                  // if(event.schedule_split_conversion_child == true) {
                  //   if(occurrence_affected_dates.indexOf(moment(event.start).add(-1,'days').format('YYYYMMDD')) > -1) {
                  //     return false;
                  //   } else {
                  //     return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start));
                  //   }
                  // } else {
                  //   if(occurrence_affected_dates.indexOf(event.start.format('YYYYMMDD')) > -1) {
                  //     return false;
                  //   } else {
                  //     return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start));
                  //   }
                  // }
                } else {
                  return ((currentStartDate.isBefore(event.ranges.end) || currentStartDate.format('YYYY-MM-DD') == event.ranges.end) && currentEndDate.isAfter(event.ranges.start));
                }
              }
            }
          }
        }
      },
      dayClick: function(date, jsEvent, view) {
        scheduleClassData.fcDayClick = true;
        if (view.name != 'agendaDay') {
          $('.cat__apps__calendar').fullCalendar('gotoDate', date);
          $('.cat__apps__calendar').fullCalendar('changeView', 'agendaDay');
        }
      },
    });
    $('.cat__apps__calendar').fullCalendar('gotoDate', new Date());
    if(parseInt($('#card-title').css('width'),10) - parseInt($('#card-clinicians').css('width'),10) == 0) {
      var widthset = "width: " + $('#card-title').css('width') + " !important;";
      //$("#cat__apps__calendar__container").css("cssText", widthset);
    } else {
      var widthset = "width: " + ((parseInt($('#card-title').css('width'),10) - parseInt($('#card-clinicians').css('width'),10)) - 20) + "px !important;";
     // $("#cat__apps__calendar__container").css("cssText", widthset);
    }
    scheduleClassData.addNewSchedule = false;
    if(!$('.fc-toolbar .fc-right')[0].id) {
      $('.fc-toolbar .fc-right')[0].id = 'calendar-toolbar-right';
    }
    var html = "<div id='btn-next-prev-grp'>";
    for(var i=0; i < $('.fc-left')[0].childNodes.length; i++){
      if($('.fc-left')[0].childNodes[i].className.indexOf('fc-prev') != -1 || $('.fc-left')[0].childNodes[i].className.indexOf('fc-next') != -1) {    
        html = html + $('.fc-left')[0].childNodes[i].outerHTML;
      } else if($('.fc-left')[0].childNodes[i].className.indexOf('fc-edit') != -1) {
        html = html+'</div>' + $('.fc-left')[0].childNodes[i].outerHTML;
      }
    }
    $('.fc-left').addClass('not-intro');
    $('.fc-left').css('opacity', '0');
    $('.fc-left').css('pointer-events','none');

    $('.fc-toolbar.fc-header-toolbar').prepend( "<div class='fc-left intro'></div>" );
    $('.fc-left.intro')[0].innerHTML = html
    $(".fc-left.intro #btn-next-prev-grp").find(".fc-prev-button").click( ()=> {
      $(".fc-left.not-intro .fc-prev-button").click();
    })
    $(".fc-left.intro #btn-next-prev-grp").find(".fc-next-button").click( ()=> {
      $(".fc-left.not-intro .fc-next-button").click();
    })
    $(".fc-left.intro").find(".fc-edit-button").click( ()=> {
      $(".fc-left.not-intro .fc-edit-button").click();
    })
    $('#btn-next-prev-grp button').css({'margin-left':'.75em'})
    if(!$('.fc-left.intro .fc-edit-button')[0].id) {
      $('.fc-left.intro .fc-edit-button')[0].id = 'calendar-toolbar-left-edit';
    }
    $('.fc-next-button').click( function(){
      scheduleClassData.renderSchedulesOnNextPrev(scheduleClassData.selectedStaff.id)
    });
    $('.fc-prev-button').click( function(){
        scheduleClassData.renderSchedulesOnNextPrev(scheduleClassData.selectedStaff.id)
    });
    $('.fc-month-button').click( function(){
      scheduleClassData.renderSchedulesOnNextPrev(scheduleClassData.selectedStaff.id)
    });
    $('.fc-agendaWeek-button').click( function(){
        scheduleClassData.renderSchedulesOnNextPrev(scheduleClassData.selectedStaff.id)
    });
    $('.fc-agendaDay-button').click( function(){
      scheduleClassData.renderSchedulesOnNextPrev(scheduleClassData.selectedStaff.id)
    });
    if(!events.length) {
      if($('.fc-edit-button').length && !$('.fc-edit-button').hasClass('element-hide')) {
        $('.fc-edit-button').addClass('element-hide')
      }
    }
  }

  renderSchedulesOnNextPrev(staff) {
    this.calendarLoaderVisible.status = true;
    let intervalStart = this.fullcalendarView.intervalStart.format('YYYY-MM-DD');
    let intervalEnd = this.fullcalendarView.intervalEnd.format('YYYY-MM-DD');
    var results = this.userScheduleDetails;
    let filteredSchedulesBeforeRender = results.filter((schedule) => {
      if(schedule && ("ranges" in schedule) && Object.keys(schedule.ranges).length) {
        var renderEvent = false;
        if(schedule.ranges.start && (moment(intervalEnd) >= moment(schedule.ranges.start))) {
          renderEvent = true
        }
        if(renderEvent && schedule.ranges.end && (moment(intervalStart) <= moment(schedule.ranges.end))) {
          renderEvent = true
        } else {
          if(schedule.ranges.end) {
            renderEvent = false
          }
        }
        if(renderEvent) {
          if(schedule.ranges.start.indexOf('T')!=-1) {
            schedule.start = schedule.ranges.start;
            schedule.end = schedule.ranges.end;
            schedule.schedule_end = "";
          }
          return true
        } else{
          return false
        }
      } else {
        return false;
      }
    });
    this.filteredSchedulesBeforeRender = filteredSchedulesBeforeRender;
    this.calendarLoaderVisible.fullcalendarEventsActivated = true;
    $('.cat__apps__calendar').fullCalendar( 'removeEvents');
    $('.cat__apps__calendar').fullCalendar( 'addEventSource', filteredSchedulesBeforeRender);  
    $('.cat__apps__calendar').fullCalendar( 'rerenderEvents' );
  }
  getUserSchedule(staff) {
    this.calendarLoaderVisible.status = true;
    if(staff) {
      this.selectedStaff = staff;
      $('.fc-edit-button').removeClass('element-hide');
      $('.fc-agendaWeek-button').removeClass('element-hide')
    } else {
      this.selectedStaff = {id: '0'};
      if(!$('.fc-edit-button').hasClass('element-hide')) {
        $('.fc-edit-button').addClass('element-hide')
      }
      $('.fc-agendaWeek-button').addClass('element-hide')
    }
    localStorage.setItem('schedule-ActiveSchedule', this.selectedStaff['id']);
    this.calendarLoaderVisible.fullcalendarEventsActivated = true;    
    $('.cat__apps__calendar').fullCalendar( 'removeEvents');
    if(staff) {
      this.fetchUserSchedules(staff.id, false, 'month');
    } else {
      this.fetchUserSchedules(0, false, 'month');
    }
    this.addNewSchedule = false;
    $('.cat__apps__calendar').fullCalendar('changeView', 'month');
    $('.fc-header-toolbar').removeClass('element-hide');
    this.selectedScheduleType = 3;
  }

  updateImageUrl(target) {
    target.src = this.profileImageBaseUrl + 'profile-pic-patient.png';
  }
  selectAllRoleUsers(data) {
    var allUsersUnderRole = [];
    if(this.selectedEditSchedule['selectedScheduleType'] == 1) {
      allUsersUnderRole = this.clinicianDataRoleWiseByPrivilege.filter(role=>role.roleData.id == data);
      if(allUsersUnderRole.length) {
        allUsersUnderRole = allUsersUnderRole[0]['userList'];
      } else {
        allUsersUnderRole = [];
      }
    } else {
      allUsersUnderRole = this.clinicianDataRoleWise.filter(role=>{
        return role['roleData']['id'] == data
      });
      if(allUsersUnderRole.length) {
        allUsersUnderRole = allUsersUnderRole[0]['userList'];
      } else {
        allUsersUnderRole = [];
      }
    }
    if(this.orderMain[data]) {
      allUsersUnderRole.forEach( (user)=> {
        this.order[user.id] = true;
      })
    } else {
      allUsersUnderRole.forEach( (user)=> {
        this.order[user.id] = false;
      })
    }
    if(this.rolesContainingAnySelection.indexOf(data) == -1) {
      this.rolesContainingAnySelection.push(data)
    } else{
      this.rolesContainingAnySelection.splice(this.rolesContainingAnySelection.indexOf(data),1)
    }
    setTimeout(() => {
        this.formated(null);
    }, 100);
  }
  formated(roleid) {
    var order = null;
    order = this.order;
    this.selectedUserRoles = [];
    for (var key in order) {
      if (order.hasOwnProperty(key) && order[key]) {
        this.selectedUserRoles.push(key);
      }
    }
    var allUsersUnderRole = [];
    if(roleid) {
      if(this.selectedEditSchedule['selectedScheduleType'] == 1) {
        allUsersUnderRole = this.clinicianDataRoleWiseByPrivilege.filter(role=>role.roleData.id == roleid);
        if(allUsersUnderRole.length) {
          allUsersUnderRole = allUsersUnderRole[0]['userList'];
        } else {
          allUsersUnderRole = [];
        }
      } else {
        allUsersUnderRole = this.clinicianDataRoleWise.filter(role=>{
          return role['roleData']['id'] == roleid
        });
        if(allUsersUnderRole.length) {
          allUsersUnderRole = allUsersUnderRole[0]['userList'];
        } else {
          allUsersUnderRole = [];
        }
      }
      var selectedmebersinroles = allUsersUnderRole.filter(user=>this.selectedUserRoles.indexOf(user.id)!=-1);
      if(this.rolesContainingAnySelection.indexOf(roleid) == -1) {
        if(selectedmebersinroles.length) {
          this.rolesContainingAnySelection.push(roleid);
          if(selectedmebersinroles.length == allUsersUnderRole.length) {
            this.orderMain[roleid] = true;
          }
        }
      } else {
        if(!selectedmebersinroles.length) {
          this.rolesContainingAnySelection.splice(this.rolesContainingAnySelection.indexOf(roleid),1);
          this.orderMain[roleid] = false;
        } else {
          if(selectedmebersinroles.length == allUsersUnderRole.length) {
            this.orderMain[roleid] = true;
          } else{
            this.orderMain[roleid] = false;
          }
        }
      }
    }

  }
  clearSelectedRoles() {
    this.selectedUserRoles = [];
    this.order = [];
    this.orderMain = [];
    this.rolesContainingAnySelection = [];
  }
  selectScheduleType(type) {
    this.selectedScheduleType = type;
    if(this.addNewSchedule) {
      $('.cat__apps__calendar').fullCalendar('clientEvents', (event) => {
        if(event.id) {
          event.schedule_type = this.selectedScheduleType == 2 ? 'ONC' : 'ESC';
          $('.cat__apps__calendar').fullCalendar('updateEvent', event);  
        }
      });
    }
    $('.cat__apps__calendar').fullCalendar('rerenderEvents' );    
  }

  sortByDateAsc (lhs, rhs) {
    return lhs > rhs ? 1 : lhs < rhs ? -1 : 0;
  }

  calculateTimeSlots(startTime, endTime) {
    let timeSlots = [];
    var startTime = startTime;
    while (startTime < endTime) {
        timeSlots.push(new moment(startTime));
        startTime.add(30, 'm');
    }
    return timeSlots;
  }
  allDayChange() {
    if(this.selectedEditSchedule['allDay']) {
      if(moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date()).isAfter($('#schedule-start-date-picker-modal').data("DateTimePicker").date())) {
        this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 AM - ' + $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 PM';    
        this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 AM - ' + $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 PM';    
      } else {
        this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 AM - 12:00 PM';    
        this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' 12:00 AM - 12:00 PM';    
      }
    } else {
      if(this.selectedEditSchedule['startTime'] && this.selectedEditSchedule['endTime']) {
        let start = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']);
        let end = moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['endTime']);
        if(moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date()).isAfter($('#schedule-start-date-picker-modal').data("DateTimePicker").date())) {
          this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + end.format('LT');
          this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + end.format('LT');    
        } else {
          this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + end.format('LT');    
          this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + end.format('LT');    
        }
      } else {
        this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY');      
        this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY');      
      }
    }
  }
  loadPage(page: number){ 
    localStorage.setItem("messageInboxPagination",JSON.stringify(page));
    console.log("page",page);
        this.previousPage = page;
        this.currentPage = page;
        this.contentOffset = (page - 1) * this.contentLimit + 1;
        this.messageLoader.messages = false;
        this.pageCountMessage = page;
        page = page - 1;
        console.log("pageCountMessage loadPage",page);
        localStorage.setItem('pageCountMessage', JSON.stringify(page));
        this.getUserScehduleType('edit',false,page);   

 }


  saveScheduleSelection() {
    let scheduleBehaviour:any = {};
    this.fromDatemodalChange = false;
    if(this.scheduleBehaviour['recurence'] == 1) {
      scheduleBehaviour.recurence = 1;
      scheduleBehaviour.noRecurence = this.scheduleBehaviour['noRecurence'];
    } else {
      scheduleBehaviour.recurence = this.scheduleBehaviour['recurence'];
      scheduleBehaviour.noRecurence = this.scheduleBehaviour['noRecurence'];
    }
    if(this.scheduleRecurrenceType != '0') {
      let timeslots = [];
      if(this.scheduleSelections.length) {
        if(this.selectedEditSchedule['allDay']) {
          if(this.scheduleRecurrenceType == 1 || this.scheduleRecurrenceType == 2) {
            timeslots = this.calculateTimeSlots(new moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 00:00'), new moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 24:00'));  
          } else {
            if($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') == $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD')) {
              timeslots = this.calculateTimeSlots(new moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 00:00'), new moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 24:00'));
            } else {
              timeslots = this.calculateTimeSlots(new moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 00:00'), new moment(new moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date()).add(1, 'days').format('YYYY-MM-DD') + ' 24:00'));
            }
          }
        } else {
          if(this.scheduleRecurrenceType == 1 || this.scheduleRecurrenceType == 2) {
            timeslots = this.calculateTimeSlots(new moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']), new moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['endTime']));
          } else {
            if($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') == $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD')) {
              timeslots = this.calculateTimeSlots(new moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']), new moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['endTime']));
            } else {
              timeslots = this.calculateTimeSlots(new moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']), new moment(new moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date()).add(1, 'days').format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['endTime']));
            }
          }
          
        }
        var a = timeslots;
        if(timeslots.length) {
          //timeslots.sort(this.sortByDateAsc);
        } else {
          timeslots = ["00:00","00:30","01:00","01:30","02:00","02:30","03:00","03:30","04:00","04:30","05:00","05:30","06:00","06:30","07:00","07:30","08:00","08:30","09:00","09:30","10:00","10:30","11:00","11:30","12:00","12:30","13:00","13:30","14:00","14:30","15:00","15:30","16:00","16:30","17:00","17:30","18:00","18:30","19:00","19:30","20:00","20:30","21:00","21:30","22:00","22:30","23:00","23:30"];
        }
      } else {
        if(this.selectedEditSchedule['allDay']) {
          if(this.scheduleRecurrenceType == 1 || this.scheduleRecurrenceType == 2) {
            timeslots = this.calculateTimeSlots(new moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 00:00'), new moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 24:00'));  
          } else {
            if($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') == $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD')) {
              timeslots = this.calculateTimeSlots(new moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 00:00'), new moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 24:00'));
            } else {
              timeslots = this.calculateTimeSlots(new moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 00:00'), new moment(new moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date()).add(1, 'days').format('YYYY-MM-DD') + ' 24:00'));
            }
          }
        } else {
          if(this.scheduleRecurrenceType == 1 || this.scheduleRecurrenceType == 2) {
            timeslots = this.calculateTimeSlots(new moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']), new moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['endTime']));
          } else {
            if($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') == $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD')) {
              timeslots = this.calculateTimeSlots(new moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']), new moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['endTime']));
            } else {
              timeslots = this.calculateTimeSlots(new moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']), new moment(new moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date()).add(1, 'days').format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['endTime']));
            }
          }
        }
      }
      this.selectedEditSchedule['timeZone'] = this.selectedTimeZoneData.offset.split(',')[0];
      let activeTimeZone = (new Date().getTimezoneOffset()) * -1;
      let scheduleSelectedTimezone = (((this.selectedEditSchedule['timeZone']) * -1) / 60) * -1;

      let timeToPassDay = [];
      let timeDifference;
      if(scheduleSelectedTimezone > this.timezone) {
          timeDifference = -1 * Math.abs(this.timezone - scheduleSelectedTimezone);
      } else {
          timeDifference = Math.abs(this.timezone - scheduleSelectedTimezone);
      }
      if(((timeDifference) + '').split('.').length == 2) {
          timeToPassDay = ((timeDifference) + '').split('.');
      } else {
          timeToPassDay = ((timeDifference) + '').split('.');
          timeToPassDay.push("0");
      }
      
      if (timeToPassDay[1] == '5') {
          if(timeDifference < 0) {
              timeToPassDay[1] = '-30';       
          } else {
              timeToPassDay[1] = '30';
          }                
      } else {
          timeToPassDay[1] = '0';
      }
      if(timeToPassDay[0] == '-0') {
          timeToPassDay[0] = '0';
      }
      
      timeToPassDay = timeToPassDay.map(function(time) {
          return parseInt(time, 10);
      });
      let nightShiftTimeSlot = [];
      let nightShiftTimeSlotStartIndex = 0;
      let convertedDate, currentDate = '';
      let actualStartTime:any;
      let formattedDate, formattedDateNightShift, formattedEndDate, startTime, endTime = '';
      timeslots.map( (scheduledTime, key) => {
        if(scheduledTime) {
          convertedDate = moment(scheduledTime.format('YYYY-MM-DD') + ' ' + scheduledTime.format('HH:mm')).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY-MM-DD');
          if(key == 0) {
            currentDate = moment(scheduledTime.format('YYYY-MM-DD') + ' ' + scheduledTime.format('HH:mm')).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY-MM-DD');
          }
          timeslots[key] = moment( currentDate + ' ' + scheduledTime.format('HH:mm')).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('HH:mm');
          if(key != 0 && key!= timeslots.length-1) {
            if(convertedDate != currentDate && timeslots[key] == '00:00') {
              nightShiftTimeSlotStartIndex = key;
              formattedDate = moment(currentDate).format('YYYYMMDD');
              formattedDateNightShift = moment(convertedDate).format('YYYYMMDD');
            }
          } else {
            if(key == 0) {
              if(convertedDate != currentDate && timeslots[key] == '00:00') {
                formattedDate = moment(convertedDate).format('YYYYMMDD');
                formattedDateNightShift = '';
              }
            }
            if(key == timeslots.length-1) {
              if(convertedDate != currentDate && timeslots[key] == '00:00') {
                formattedDate = moment(currentDate).format('YYYYMMDD');
                nightShiftTimeSlotStartIndex = key;
                formattedDateNightShift = moment(convertedDate).format('YYYYMMDD');
              }
            }
          }
          if(key == 0) {
            startTime = timeslots[key];
            actualStartTime = scheduledTime;
          }
          if(key == timeslots.length-1) {
            endTime = timeslots[key];
            if($('#schedule-to-date-picker-modal').data("DateTimePicker").date() && this.scheduleRecurrenceType != 1) {
              let time = moment($('#schedule-to-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + actualStartTime.format('HH:mm')).add(30, 'm').format('HH:mm');
              formattedEndDate = moment($('#schedule-to-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + time).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
            } else {
              formattedEndDate = '';
            }
          }
        }
      });
      if(!formattedDate) {
        formattedDate = moment(currentDate).format('YYYYMMDD')
      }
      if(!formattedDateNightShift) {
        formattedDateNightShift = '';
      }
      if(nightShiftTimeSlotStartIndex) {
        nightShiftTimeSlot.push(timeslots.splice(0,nightShiftTimeSlotStartIndex));
        nightShiftTimeSlot.push(timeslots);
        timeslots = nightShiftTimeSlot;
      }
      let param = {
        userId: this.selectedStaff['id'],
        tenantId: this._structureService.getCookie('tenantId'),
        scheduleFormat: 30,
        formattedDate: formattedDate,
        formattedDateNightShift: formattedDateNightShift,
        formattedEndDate: formattedEndDate != '' ? formattedEndDate : '',
        dayStatus: 0,
        weekStatus: 0,
        typeOfSchedule: this.scheduleRecurrenceType,
        mergeRecurring: 0,
        getDay: moment(formattedDate).day(),
        escalated_schedule: this.selectedEditSchedule['selectedScheduleType'] == 1 ? 1 : 0,
        recurringTypeStatus: [],
        isNightShift: formattedDateNightShift != '' ? true : false,
        dayNumberNightShift: formattedDateNightShift != '' ? moment(formattedDateNightShift).day(): '',
        edit: false,
        timeSpan: '',
        savedTimeZone: this.selectedEditSchedule['timeZone'],
        longEvent: false
      }
      if(param.isNightShift == true) {
        param.timeSpan = moment(param.formattedDate).format('YYYY-MM-DD') + ' ' + startTime + '$' + moment(param.formattedDateNightShift).format('YYYY-MM-DD') + ' ' + endTime;
      }
      let addedByDate = this.addNewScheduleByDate;
      let scheduledDate = null;
      if(addedByDate) {
        scheduledDate = $('#schedule-start-date-picker-modal').data("DateTimePicker").date();
        $('.cat__apps__calendar').fullCalendar('changeView', 'agendaDay');
      }
      if(param.typeOfSchedule == 1) {
        param.recurringTypeStatus = [{"noRecurence":1, "recurence":1}];
      } else {
        param.recurringTypeStatus = [scheduleBehaviour];//{"noRecurence":3, "recurence":2}
        param.recurringTypeStatus = [{"noRecurence":3, "recurence":2}]
      }
      /* fetch selected users starts*/
      var val:any = [];
      var userSelected = '';
      /*setTimeout(()=>{*/
        var j = 0;
          $(':checkbox:checked').each(function(i){
            var name = this.name;
            if(name == "cliniciansRoleUser[]"){
              val[j] = $(this).val();
              userSelected = userSelected? userSelected+", "+$(this).attr('title'): $(this).attr('title');
              j++;
            } 
          });
        if(val.indexOf(param.userId) == -1) {
          val.push(param.userId);
          userSelected = userSelected ? userSelected+", "+this.selectedStaff.displayName: this.selectedStaff.displayName;
        }
        /*},100);*/
      /* fetch selected users ends*/
      $('#edit-schedule-modal').modal('toggle');
      if(param.typeOfSchedule == 3) {
        param.mergeRecurring = 1;
      }
      if(param.typeOfSchedule == 1 && param.timeSpan) {
        let endDate = param.timeSpan.split('$')[1];
        let startDate = param.timeSpan.split('$')[0];
        let diff = moment(endDate,"YYYY-MM-DD HH:mm").diff(moment(startDate,"YYYY-MM-DD HH:mm"));
        let duration = moment.duration(diff);
        if(duration.asHours() >= 24) {
          param.longEvent = true;
          param.dayNumberNightShift = "";
          param.formattedEndDate = param.formattedDateNightShift;
          param.formattedDateNightShift = "";
          param.isNightShift = false;
          timeslots = [];
          timeslots.push(param.timeSpan.split('$')[0].split(' ')[1]);
          timeslots.push(param.timeSpan.split('$')[1].split(' ')[1]);
        }
      }
      //console.log("timeslots =================>>>>>>>> ", timeslots);
      let data = {"daySchedule": timeslots,"recuringDates": [],"dayScheduleOnLoad": [],"recuringTypeStatus": param.recurringTypeStatus, "userId": val};
      var activityData = {};
      this._scheduleService.saveUserSchedules(param, data).then((status) => {
        if(status['status']) {
          activityData = {
            activityName: "Success Schedule Assign",
            activityType: "Manage Schedules",
            activityDescription: "Schedule assign of "+(val.length>1 ? 'users' : 'user') + userSelected+"["+val.join(',')+"] by user " + this.userData.displayName+"["+this.userData.userId+"]  has succeeded for schedule having schedule reference id as "+status['scheduleId']+", schedule event type as "+param.typeOfSchedule+", selected type of schedule for rendering as "+this.selectedScheduleType+" and selected timezone as "+JSON.stringify(this.selectedTimeZoneData)+".  Details: params- " + JSON.stringify(param)+", data- " + JSON.stringify(data) + ", API response- " + JSON.stringify(status) ,
            tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
          };
          this._structureService.notifyMessage({
            messge:'Schedule saved successfully',
            delay:1000,
            type:'success'
          });
        } else {
          activityData = {
            activityName: "Failure Schedule Assign",
            activityType: "Manage Schedules",
            activityDescription: "Schedule assign of " +(val.length>1 ? 'users' : 'user') + userSelected+"["+val.join(',')+"] by user " + this.userData.displayName+"["+this.userData.userId+"]  has failed for schedule having schedule reference id as "+status['scheduleId']+", schedule event type as "+param.typeOfSchedule+", selected type of schedule for rendering as "+this.selectedScheduleType+" and selected timezone as "+JSON.stringify(this.selectedTimeZoneData)+".  Details: params- " + JSON.stringify(param)+", data- " + JSON.stringify(data) + ", API response- " + JSON.stringify(status) ,
            tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
          };
          this._structureService.notifyMessage({
            messge:'Schedule save Failed',
            delay:1000,
            type:'danger'
          });
        }
        this.scheduleRecurrenceType = '0';

        this._structureService.trackActivity(activityData);
        var updatedBy = {
          displayName : this.userData.displayName,
          userid : this.userData.userId ?this.userData.userId : 0,
        };

        var updateConfigPollingtoServer = {
          configurationType: "updateSchedule",
          role: 0,
          tenantid: param.tenantId,
          userId: param.userId,
          updatedBy: updatedBy,
          scheduledata:{
              userId: param.userId,
              tenantId: param.tenantId,
              formattedDate: param.formattedDate,
              getDay: param.getDay,
              escalated_schedule: param.escalated_schedule,
              userName: this.selectedStaff['displayName'],
              displayName: this.selectedStaff['displayName'],
          },
          keepUpdateHistoryForAPPResume: true
        };
        var todayFormat = moment().format('YYYYMMDD');
        if(todayFormat == param.formattedDate || (parseInt(todayFormat) == parseInt(param.formattedDate)+1) || (parseInt(todayFormat) == parseInt(param.formattedDate)-1)){
          this._structureService.socket.emit("updateConfigPollingtoServer", updateConfigPollingtoServer);
        }
        val.forEach(user => {
          this.staffList.map( (staff, key) => {
            if(staff.id == user) {
              if(staff.schedulesAssigned != 'all') {
                if(staff.schedulesAssigned == 'empty') {
                  staff.schedulesAssigned = param.escalated_schedule ? 'esc' : 'oncall'
                } else if(staff.schedulesAssigned == 'oncall') {
                  if(param.escalated_schedule) {
                    staff.schedulesAssigned = 'all';  
                  }
                } else {
                  if(!param.escalated_schedule) {
                    staff.schedulesAssigned = 'all';
                  }
                }
              }
            }
          });
        });
        if(addedByDate) {
          this.fetchUserSchedules(this.selectedStaff['id'], false, $('.cat__apps__calendar').fullCalendar( 'getView' ).name);
          $('.cat__apps__calendar').fullCalendar('gotoDate', scheduledDate);
        } else {
          this.fetchUserSchedules(this.selectedStaff['id'], false, 'agendaDay');
        }
        this.scheduleSelections = [];
        this.scheduleBehaviour = {
          recurence: 2,
          noRecurence: 3
        };
        this.scheduleTimeRanges = {
          startTime: '',
          endTime: ''
        }
        if($('.schedule-end-time').hasClass('recurrence-type-error')) {
          $('.schedule-end-time').removeClass('recurrence-type-error'); 
        }
        $('.fc-header-toolbar').removeClass('element-hide');
      },(err)=> {
        activityData = {
          activityName: "Failure Schedule Assign",
          activityType: "Manage Schedules",
          activityDescription: "Schedule assign of " +(val.length>1 ? 'users' : 'user') + userSelected+"["+val.join(',')+"] by user " + this.userData.displayName+"["+this.userData.userId+"]  has failed for schedule having schedule reference id as "+err['scheduleId']+", schedule event type as "+param.typeOfSchedule+", selected type of schedule for rendering as "+this.selectedScheduleType+" and selected timezone as "+JSON.stringify(this.selectedTimeZoneData)+".  Details: params- " + JSON.stringify(param)+", data- " + JSON.stringify(data) + ", API response- " + JSON.stringify(err) ,
          tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
        };
        this._structureService.trackActivity(activityData);
        this.scheduleRecurrenceType = '0';
        this._structureService.notifyMessage({
          messge:'Schedule save Failed',
          delay:1000,
          type:'danger'
        });
        this.scheduleSelections = [];
        this.scheduleBehaviour = {
          recurence: 2,
          noRecurence: 3
        };
        this.scheduleTimeRanges = {
          startTime: '',
          endTime: ''
        }
        if($('.schedule-end-time').hasClass('recurrence-type-error')) {
          $('.schedule-end-time').removeClass('recurrence-type-error'); 
        }
        $('.fc-header-toolbar').removeClass('element-hide');
      });
    } else {
      $('.cat__apps__calendar').fullCalendar('unselect');
      $('.recurrence-type-error').removeClass('element-hide');
      $('.recurrence-type-container').addClass('recurrence-type-error');
    }
  }

  SearchOnEnter(e) {
    var code = (e.keyCode ? e.keyCode : e.which);
   
    if(code == 13) {
      this.allstaffhide = true;
      this.currentPage = 1;
      this.searchText = $("#SearchTxt").val();
      this.getUserScehduleType('edit',false);

    }
}
searchFn(){
  this.allstaffhide = true;
  this.searchText = $("#SearchTxt").val();
  this.getUserScehduleType('edit',false);

}

resetSearch()
    {
      this.allstaffhide = false;

        this.searchText = undefined;
        this.currentPage = 1;
        $('#SearchTxt').val("");
        this.getUserScehduleType('edit',false);
    }

  getUserScehduleType(type, getClinicians,pagecount=undefined) {
    var offsetfrom = pagecount? parseInt(pagecount)*this.contentLimit:0;
    var limit = this.contentLimit;
    var searchemail = false;
    var searchKeyword = '';
    searchKeyword = this.searchText ? this.searchText : '';
    this.loadingData = true;

    this._structureService.getUserScehduleType(offsetfrom,limit,searchKeyword).then(( data ) => {
    this.loadingData = false;
    data = JSON.parse(JSON.stringify(data));
    this.staffList = data['getSessionTenant'].staffUsers;
    if(offsetfrom == 0){
    this.totalCount =  data['getSessionTenant'].staffUsersPagination.totalCount;
    }
   
      let oncall, esc;
      var staffListTemp = this.staffList.filter( (staff, key) => {
        oncall = false;
        esc = false;
        if(staff.role.privileges && staff.role.privileges.indexOf('Auto_Message_Escalation') > -1) {
          staff.autoMessageEscalationPrivilege = true;
        } else {
          staff.autoMessageEscalationPrivilege = false;
        }
        staff.schedules.map( (schedules, key) => {
          if(schedules.type == 'On_Call') {
            oncall = true;
          } else {
            esc = true;
          }
        });
        if(oncall && !esc) {
          staff.schedulesAssigned = 'oncall';
        } else if(!oncall && esc) {
          staff.schedulesAssigned = 'esc';
        } else if(!oncall && !esc) {
          staff.schedulesAssigned = 'empty';
        } else {
          staff.schedulesAssigned = 'all';
        }
        return (staff.status == 'Active') ;
      });
      this.staffList = staffListTemp;
      let clickAllStaff = false;
      if(type == 'delete') {
        this.fetchUserSchedules(this.selectedStaff['id'], false, $('.cat__apps__calendar').fullCalendar( 'getView' ).type);
      } else if(type == 'init') {
        if(localStorage.getItem('schedule-ActiveSchedule')) {
          let index = this.staffList.findIndex(staff => staff.id == localStorage.getItem('schedule-ActiveSchedule'));
          if(index != -1) {
            this.selectedStaff = this.staffList[index];
          } else {
            //this.selectedStaff = this.staffList[0];
            this.selectedStaff = {id: 0};
          }        
        } else {
          //this.selectedStaff = this.staffList[0];
          this.selectedStaff = {id: 0};
        }
        clickAllStaff = true;
        //this.fetchUserSchedules(this.selectedStaff['id'], true, 'month');
      } else {

      }
      if(getClinicians) {
        this._inboxService.getUsersListByRoleId(0, 0, undefined).then((users) => {
          this.clinicianDataRoleWise = [];
          this.allUsersList = JSON.parse(JSON.stringify(users));
          this.allUsersList.forEach(value => {
            var role = { id: value.userid, name: value.displayname, privileges: value.privileges };
            var roleData = { id: value.tenantRoleId, name: value.role, tenantRoleId: value.tenantRoleId };
            if (value.roleId != 3 && value.password) {
              if (value.tenantRoleId) {
                if (!this.clinicianDataRoleWise[value.tenantRoleId]) {
                  this.clinicianDataRoleWise[value.tenantRoleId] = {};
                }
                if (!('userList' in this.clinicianDataRoleWise[value.tenantRoleId])) {
                  this.clinicianDataRoleWise[value.tenantRoleId]['userList'] = [];
                }
                if (!('roleData' in this.clinicianDataRoleWise[value.tenantRoleId])) {
                  this.clinicianDataRoleWise[value.tenantRoleId]['roleData'] = {};
                }
                this.clinicianDataRoleWise[value.tenantRoleId]['roleData'] = roleData;
                this.clinicianDataRoleWise[value.tenantRoleId]['userList'].push(role);
                if (!('HideNoEscalationPrivilege' in this.clinicianDataRoleWise[value.tenantRoleId])) {
                  if(role['privileges'] && role['privileges'].indexOf('autoMessageEscalation') != -1) {
                    this.clinicianDataRoleWise[value.tenantRoleId]['HideNoEscalationPrivilege'] = false;
                  } else {
                    this.clinicianDataRoleWise[value.tenantRoleId]['HideNoEscalationPrivilege'] = true;  
                  }
                }
              }
            }
          });
          this.clinicianDataRoleWise = this.clinicianDataRoleWise.filter(function (item) {
            return true;
          });
          this.checkComponentStatus = true;
          this.clinicianDataRoleWise.sort(function (a, b) {
            if (a.roleData.name < b.roleData.name) return -1;
            if (a.roleData.name > b.roleData.name) return 1;
            return 0;
          });
          this.clinicianDataRoleWiseInitial = this.clinicianDataRoleWise;
          this.clinicianDataRoleWise = this.clinicianDataRoleWiseInitial.map(roleDetails => {
            roleDetails['userList'] = roleDetails['userList'].map(user => {
              if(user.id != this.selectedStaff['id']) {
                user.HideSelectedStaff = false;
              } else {
                user.HideSelectedStaff = true;
              }
              return user
            });
            return roleDetails;
          });
          if(clickAllStaff) {
            setTimeout(()=> {
              if(this.selectedStaff['id']) {
                $('.cat__apps__messaging__tab__'+this.selectedStaff['id']).trigger('click');
              } else {
                $('.cat__apps__messaging__tab__0').trigger('click');
              }
            }, 100); 
          }
        }).catch((ex) => {
        });
      } else {
        if(clickAllStaff) {
          setTimeout(()=> {
            if(this.selectedStaff['id']) {
              $('.cat__apps__messaging__tab__'+this.selectedStaff['id']).trigger('click');
            } else {
              $('.cat__apps__messaging__tab__0').trigger('click');
            }
          }, 100); 
        }
      }
    });

  }

  fetchUserSchedules(userId, initialise, view) {
    this._scheduleService.getUserSchedules(userId ? this.selectedStaff['id'] : 0).then((data:any) => {
      if(this.selectedStaff && "id" in this.selectedStaff && data && "requestedUserId" in data && this.selectedStaff.id == data.requestedUserId) {
        this.fetchUserScheduleFailureCount = 0;
        let results:any = JSON.parse(JSON.stringify(data));
        let scheduleSelectedTimeZone = this.selectedTimeZoneData.offset.split(',')[0];
        this.userScheduleDetails = [];
        let scheduleSelectedTimezone = (((scheduleSelectedTimeZone) * -1) / 60) * -1;

        let timeToPassDay = [];
        let timeDifference;
        if(scheduleSelectedTimezone > this.timezone) {
            timeDifference = Math.abs(this.timezone - scheduleSelectedTimezone);
        } else {
            timeDifference = -1 * Math.abs(this.timezone - scheduleSelectedTimezone);
        }
        if(((timeDifference) + '').split('.').length == 2) {
            timeToPassDay = ((timeDifference) + '').split('.');
        } else {
            timeToPassDay = ((timeDifference) + '').split('.');
            timeToPassDay.push("0");
        }
        
        if (timeToPassDay[1] == '5') {
            if(timeDifference < 0) {
                timeToPassDay[1] = '-30';       
            } else {
                timeToPassDay[1] = '30';
            }                
        } else {
            timeToPassDay[1] = '0';
        }
        if(timeToPassDay[0] == '-0') {
            timeToPassDay[0] = '0';
        }
        
        timeToPassDay = timeToPassDay.map(function(time) {
            return parseInt(time, 10);
        });

        let scheduleSavedTimeZone = (((this.userData.config.tenant_timezone) * -1) / 60) * -1;//
        let timeToPassDay_scheduleSavedTimeZone = [];
        let timeDifference_scheduleSavedTimeZone;
        if(scheduleSavedTimeZone > this.timezone) {
          timeDifference_scheduleSavedTimeZone = Math.abs(this.timezone - scheduleSavedTimeZone);
        } else {
          timeDifference_scheduleSavedTimeZone = -1 * Math.abs(this.timezone - scheduleSavedTimeZone);
        }
        if(((timeDifference_scheduleSavedTimeZone) + '').split('.').length == 2) {
          timeToPassDay_scheduleSavedTimeZone = ((timeDifference_scheduleSavedTimeZone) + '').split('.');
        } else {
          timeToPassDay_scheduleSavedTimeZone = ((timeDifference_scheduleSavedTimeZone) + '').split('.');
            timeToPassDay_scheduleSavedTimeZone.push("0");
        }
        
        if (timeToPassDay_scheduleSavedTimeZone[1] == '5') {
            if(timeDifference_scheduleSavedTimeZone < 0) {
              timeToPassDay_scheduleSavedTimeZone[1] = '-30';       
            } else {
              timeToPassDay_scheduleSavedTimeZone[1] = '30';
            }                
        } else {
          timeToPassDay_scheduleSavedTimeZone[1] = '0';
        }
        if(timeToPassDay_scheduleSavedTimeZone[0] == '-0') {
          timeToPassDay_scheduleSavedTimeZone[0] = '0';
        }
        
        timeToPassDay_scheduleSavedTimeZone = timeToPassDay_scheduleSavedTimeZone.map(function(time) {
            return parseInt(time, 10);
        });
        results['singleDaySchedule'].map( (schedule) => {
          if(schedule && ("ranges" in schedule) && Object.keys(schedule.ranges).length) {

            let sch1 = JSON.parse(JSON.stringify(schedule));
            let convertedStartDate = null;
            let convertedStartDate__scheduleSavedTimeZone = null;
            if(schedule.ranges.start.indexOf('T') != -1) {
              convertedStartDate = moment(schedule.ranges.start).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
              convertedStartDate__scheduleSavedTimeZone = moment(schedule.ranges.start).add(timeToPassDay_scheduleSavedTimeZone[0], 'hours').add(timeToPassDay_scheduleSavedTimeZone[1], 'm');
            } else {
              convertedStartDate = moment(schedule.ranges.start + ' ' +  schedule.start).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
              convertedStartDate__scheduleSavedTimeZone = moment(schedule.ranges.start + ' ' +  schedule.start).add(timeToPassDay_scheduleSavedTimeZone[0], 'hours').add(timeToPassDay_scheduleSavedTimeZone[1], 'm');
            }

            let nextMonday = null;
            var dayINeed = 1;
            if (moment(convertedStartDate__scheduleSavedTimeZone).isoWeekday() <= dayINeed) {
              nextMonday = moment(convertedStartDate__scheduleSavedTimeZone).isoWeekday(dayINeed);
            } else {
              nextMonday = moment(convertedStartDate__scheduleSavedTimeZone).add(1, 'weeks').isoWeekday(dayINeed);
            }
            let nextWeekEndStartDay = null;
            let weekEndDays = JSON.parse(JSON.stringify(this.userData.config.weekend_days));
            let WeekEndStartDay =  weekEndDays[0]*1;
            if (moment(convertedStartDate__scheduleSavedTimeZone).isoWeekday() <= WeekEndStartDay) {
              nextWeekEndStartDay = moment(convertedStartDate__scheduleSavedTimeZone).isoWeekday(WeekEndStartDay);
            } else {
              nextWeekEndStartDay = moment(convertedStartDate__scheduleSavedTimeZone).add(1, 'weeks').isoWeekday(WeekEndStartDay);
            }
            let convertedEndDate:any;
            if(schedule.schedule_recurrence_type == 3 || schedule.schedule_recurrence_type == 2 || schedule.schedule_recurrence_type == 4 || schedule.schedule_recurrence_type == 7) {
              convertedEndDate = moment(schedule.ranges.start + ' ' +  schedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
            } else {
              if(schedule.ranges.start.indexOf('T') != -1) {
                convertedEndDate = moment(schedule.ranges.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
              } else {
                if(schedule.ranges.end) {
                  convertedEndDate = moment(schedule.ranges.end + ' ' +  schedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                } else {
                  convertedEndDate = moment(schedule.ranges.start + ' ' +  schedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                }
              }
            }
            if(moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59').diff(moment(convertedEndDate),'m') == -1) {
              convertedEndDate = moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59');
            }
            if(schedule.ranges.start.indexOf('T') == -1 && convertedEndDate.isAfter(moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59'))) {
              if(schedule.schedule_recurrence_type == 3 || schedule.schedule_recurrence_type == 2 || schedule.schedule_recurrence_type == 4 || schedule.schedule_recurrence_type == 7) {
                if(schedule.ranges.end) {
                  convertedEndDate = moment(schedule.ranges.end + ' ' +  schedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                  if(convertedEndDate.format('HH:mm') == '00:00') {
                    convertedEndDate = convertedEndDate.add(-1, 'm');
                  }
                }            
              }
              let a = JSON.parse(JSON.stringify(schedule));
              let scheduleContinuity = JSON.parse(JSON.stringify(schedule));
              if(schedule.schedule_recurrence_type == 3 || schedule.schedule_recurrence_type == 2 || schedule.schedule_recurrence_type == 4 || schedule.schedule_recurrence_type == 7) {
                schedule.ranges.start = convertedStartDate.format('YYYY-MM-DD');
                if(schedule.ranges.end) {
                  schedule.ranges.end = convertedEndDate.add(-1, 'days').format('YYYY-MM-DD');
                }
              } else {
                schedule.ranges.start = convertedStartDate.format('YYYY-MM-DD');
                if(schedule.ranges.end) {
                  schedule.ranges.end = convertedStartDate.format('YYYY-MM-DD');
                }
              }
              if(schedule.schedule_end) {
                var b = schedule.schedule_end;
                schedule.schedule_end = moment(moment(schedule.schedule_end).format('YYYY-MM-DD') + ' ' + schedule.start).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');          
              }
              if(schedule.occurrence_affected_dates) {
                let occurrence_affected_dates_time = schedule.occurrence_affected_dates_time.split('#');
                let converted_occurrence_affected_dates = new Array();
                let converted_occurrence_affected_dates_time = new Array();
                let converted_child_occurrence_affected_dates = new Array();
                let converted_child_occurrence_affected_dates_time = new Array();
                schedule.occurrence_affected_dates = schedule.occurrence_affected_dates.split(',');
                occurrence_affected_dates_time.map( (time, key) => {
                  if(time.indexOf('child-') != -1) {
                    time = time.substring(7, time.indexOf(']'));
                    time = time.split(',');
                    time = time[time.length-1];
                    time = time.substring(1, time.length-1);
                    converted_child_occurrence_affected_dates.push(moment(moment(schedule.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + time).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD'));
                    converted_child_occurrence_affected_dates_time.push(occurrence_affected_dates_time[key]);
                  } else {
                    if(time.indexOf('[delete]') == -1) {
                      time = time.substring(1, time.indexOf(']'));
                      time = time.split(',');
                      let startTime, endTime;
                      startTime = time[0];
                      endTime = time[time.length-1];
                      startTime = startTime.substring(1, startTime.length-1);
                      endTime = endTime.substring(1, endTime.length-1);
                      startTime = moment(moment(schedule.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + startTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                      endTime = moment(moment(schedule.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + endTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                      if(startTime != endTime) {
                        var occ = converted_occurrence_affected_dates;
                        var con_occ = converted_child_occurrence_affected_dates;
                        converted_occurrence_affected_dates.push(startTime);
                        converted_occurrence_affected_dates_time.push(occurrence_affected_dates_time[key]);
                        converted_child_occurrence_affected_dates.push(endTime);
                        converted_child_occurrence_affected_dates_time.push('child-' + occurrence_affected_dates_time[key]);
                      } else {
                        var occ1 = converted_occurrence_affected_dates;
                        var con_occ = converted_child_occurrence_affected_dates;
                        converted_occurrence_affected_dates.push(startTime);
                        converted_occurrence_affected_dates_time.push(occurrence_affected_dates_time[key]);
                        converted_child_occurrence_affected_dates.push(moment(moment(startTime).format('YYYY-MM-DD')).add(1,'days').format('YYYYMMDD'));
                        converted_child_occurrence_affected_dates_time.push('child-' + occurrence_affected_dates_time[key]);
                      }
                    } else {
                      let startTime, endTime;
                      startTime = moment(moment(schedule.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + schedule.start).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                      endTime = moment(moment(schedule.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + schedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                      if(startTime != endTime) {
                        converted_occurrence_affected_dates.push(startTime);
                      } else {
                        converted_occurrence_affected_dates.push(startTime);
                      }
                    }
                  }              
                });
                schedule.occurrence_affected_dates = converted_occurrence_affected_dates.join(',');
                schedule.occurrence_affected_dates_time = converted_occurrence_affected_dates_time.join('#');
                schedule.child_occurrence_affected_dates = converted_child_occurrence_affected_dates.join(',');
                schedule.child_occurrence_affected_dates_time = converted_occurrence_affected_dates_time.join('#');
              }
              if(scheduleContinuity.occurrence_affected_dates) {
                let occurrence_affected_dates_time = scheduleContinuity.occurrence_affected_dates_time.split('#');
                let converted_occurrence_affected_dates = new Array();
                let converted_occurrence_affected_dates_time = new Array();
                let converted_child_occurrence_affected_dates = new Array();
                let converted_child_occurrence_affected_dates_time = new Array();
                scheduleContinuity.occurrence_affected_dates = scheduleContinuity.occurrence_affected_dates.split(',');
                occurrence_affected_dates_time.map( (time, key) => {
                  if(time.indexOf('child-') != -1) {
                    time = time.substring(7, time.indexOf(']'));
                    time = time.split(',');
                    time = time[time.length-1];
                    time = time.substring(1, time.length-1);
                    converted_child_occurrence_affected_dates.push(moment(moment(scheduleContinuity.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + time).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD'));
                    converted_child_occurrence_affected_dates_time.push(occurrence_affected_dates_time[key]);
                  } else {
                    if(time.indexOf('[delete]') == -1) {            
                      time = time.substring(1, time.indexOf(']'));
                      time = time.split(',');                
                      let startTime, endTime;
                      startTime = time[0];
                      endTime = time[time.length - 1];
                      startTime = startTime.substring(1, startTime.length-1);
                      endTime = endTime.substring(1, endTime.length-1);
                      startTime = moment(moment(scheduleContinuity.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + startTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                      endTime = moment(moment(scheduleContinuity.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + endTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                      if(startTime != endTime) {
                        var occ = converted_occurrence_affected_dates;
                        var con_occ = converted_child_occurrence_affected_dates;
                        converted_occurrence_affected_dates.push(startTime);
                        converted_occurrence_affected_dates_time.push(occurrence_affected_dates_time[key]);
                        converted_child_occurrence_affected_dates.push(endTime);
                        converted_child_occurrence_affected_dates_time.push('child-' + occurrence_affected_dates_time[key]);
                      } else {
                        var occ1 = converted_occurrence_affected_dates;
                        var con_occ = converted_child_occurrence_affected_dates;
                        converted_occurrence_affected_dates.push(startTime);
                        converted_occurrence_affected_dates_time.push(occurrence_affected_dates_time[key]);
                        converted_child_occurrence_affected_dates.push(moment(moment(startTime).format('YYYY-MM-DD')).add(1,'days').format('YYYYMMDD'));
                        converted_child_occurrence_affected_dates_time.push('child-' + occurrence_affected_dates_time[key]);
                      }
                    } else {
                      let startTime, endTime;
                      startTime = moment(moment(scheduleContinuity.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + scheduleContinuity.start).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                      endTime = moment(moment(scheduleContinuity.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + scheduleContinuity.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                      if(startTime != endTime) {
                        converted_occurrence_affected_dates.push(startTime);
                      } else {
                        converted_occurrence_affected_dates.push(startTime);
                      }
                    }
                  }              
                });
                scheduleContinuity.occurrence_affected_dates = converted_occurrence_affected_dates.join(',');
                scheduleContinuity.occurrence_affected_dates_time = converted_occurrence_affected_dates_time.join('#');
                scheduleContinuity.child_occurrence_affected_dates = converted_child_occurrence_affected_dates.join(',');
                scheduleContinuity.child_occurrence_affected_dates_time = converted_occurrence_affected_dates_time.join('#');
              }
              schedule.start = convertedStartDate.format('HH:mm');
              schedule.end = '24:00';
              schedule.day_number = moment(schedule.ranges.start).day() + '';
              if(schedule.schedule_recurrence_type == 2) {
                var dow = [];
                dow.push(moment(schedule.ranges.start).day() + '');
                schedule.dow = dow;
                schedule.title = 'Recurring on all ' + moment(schedule.ranges.start).format('dddd') + 's'; 
              }
              if(schedule.schedule_recurrence_type == 4) {
                var dow = [];
                let diffDays_scheduleSavedTimeZone = nextMonday.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                for (var i = 0; i < 5; i++) {
                  dow.push(moment(schedule.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                }
                schedule.dow = dow;
                schedule.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']';
              }
              if(schedule.schedule_recurrence_type == 5) {
                if(schedule.reference_recurence_type == 4) {
                  var dow = [];
                  let diffDays_scheduleSavedTimeZone = nextMonday.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                  for (var i = 0; i < 5; i++) {
                    dow.push(moment(schedule.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                  }
                  schedule.dow = dow;
                  schedule.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']';
                }
              }
              if(schedule.schedule_recurrence_type == 7) {
                var dow = [];
                let diffDays_scheduleSavedTimeZone = nextWeekEndStartDay.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                for (var i = 0; i < weekEndDays.length; i++) {
                  dow.push(moment(schedule.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                }
                schedule.dow = dow;
                schedule.title = 'Recurring on all weekend days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']';
              }
              // if(schedule.schedule_recurrence_type == 7) {//handled paste weekend day as it is
              //   var dow = [];
              //   for (var i = 0; i < this.userData.config.weekend_days.length; i++) {
              //     if(i == 0) {
              //       dow.push(moment(schedule.ranges.start).day() + '');
              //     } else {
              //       var prev = dow[i-1]*1;
              //       if(prev==6) {
              //         dow.push(0+'');
              //       } else {
              //         dow.push((prev+1)+'')
              //       }
              //     }
              //   }
              //   schedule.dow = dow;
              //   schedule.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']'; 
              // }
              schedule.schedule_split_conversion = true;
              schedule.schedule_split_conversion_child = false;
              if(schedule.schedule_recurrence_type == 3 || schedule.schedule_recurrence_type == 2 || schedule.schedule_recurrence_type == 4 || schedule.schedule_recurrence_type == 7) {
                scheduleContinuity.ranges.start = convertedStartDate.add(1, 'days').format('YYYY-MM-DD');
                if(schedule.ranges.end) {
                  scheduleContinuity.ranges.end = convertedEndDate.add(1, 'days').format('YYYY-MM-DD');
                }
              } else {
                scheduleContinuity.ranges.start = convertedEndDate.format('YYYY-MM-DD');
                if(schedule.ranges.end) {
                  scheduleContinuity.ranges.end = convertedEndDate.format('YYYY-MM-DD');
                }
              }
              if(scheduleContinuity.schedule_end) {
                var b = scheduleContinuity;
                scheduleContinuity.schedule_end = moment(moment(scheduleContinuity.schedule_end).format('YYYY-MM-DD') + ' ' + scheduleContinuity.start).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');                        
              }
              scheduleContinuity.start = '00:00';
              if(convertedEndDate.format('HH:mm') == '23:59') {
                scheduleContinuity.end = '24:00';
              } else {
                scheduleContinuity.end = convertedEndDate.format('HH:mm');
              }          
              scheduleContinuity.day_number = moment(scheduleContinuity.ranges.start).day() + '';
              if(scheduleContinuity.schedule_recurrence_type == 2) {
                var dow = [];
                dow.push(moment(scheduleContinuity.ranges.start).day() + '');
                scheduleContinuity.dow = dow;
                scheduleContinuity.title = 'Recurring on all ' +  moment(scheduleContinuity.ranges.start).format('dddd') + 's'; 
              }
              if(scheduleContinuity.schedule_recurrence_type == 4) {
                var dow = [];
                let diffDays_scheduleSavedTimeZone = nextMonday.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                for (var i = 0; i < 5; i++) {
                  dow.push(moment(scheduleContinuity.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                }
                scheduleContinuity.dow = dow;
                scheduleContinuity.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']';
              }
              if(schedule.schedule_recurrence_type == 5) {
                if(scheduleContinuity.reference_recurence_type == 4) {
                  var dow = [];
                  let diffDays_scheduleSavedTimeZone = nextMonday.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                  for (var i = 0; i < 5; i++) {
                    dow.push(moment(scheduleContinuity.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                  }
                  scheduleContinuity.dow = dow;
                  scheduleContinuity.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']';
                }
              }
              if(scheduleContinuity.schedule_recurrence_type == 7) {
                var dow = [];
                let diffDays_scheduleSavedTimeZone = nextWeekEndStartDay.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                for (var i = 0; i < weekEndDays.length; i++) {
                  dow.push(moment(scheduleContinuity.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                }
                scheduleContinuity.dow = dow;
                scheduleContinuity.title = 'Recurring on all weekend days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']';
              }
              // if(scheduleContinuity.schedule_recurrence_type == 7) {//handled paste weekend day as it is
              //   var dow = [];
              //   for (var i = 0; i < this.userData.config.weekend_days.length; i++) {
              //     if(i == 0) {
              //       dow.push(moment(scheduleContinuity.ranges.start).day() + '');
              //     } else {
              //       var prev = dow[i-1]*1;
              //       if(prev==6) {
              //         dow.push(0+'');
              //       } else {
              //         dow.push((prev+1)+'')
              //       }
              //     }
              //   }
              //   scheduleContinuity.dow = dow;
              //   scheduleContinuity.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']'; 
              // }
              scheduleContinuity.schedule_split_conversion = true;
              scheduleContinuity.schedule_split_conversion_child = true;
              schedule.converted_timeSpan = scheduleContinuity.converted_timeSpan = schedule['ranges']['start'] + ' ' + schedule.start + '$' + scheduleContinuity['ranges']['start'] + ' ' + scheduleContinuity.end;
              if(schedule.schedule_recurrence_type == 5) {
                let convertedStartDate, convertedEndDate;
                if(schedule.seriesStartScheduleData && schedule.seriesEndScheduleData) {
                  var parentStartDate = schedule.seriesStartScheduleData.split('-')[0];
                  var scheduleParentTimes = schedule.seriesStartScheduleData.split('-')[1].replace(/["']/g, "").split(',');
                  var ChildStartDate = schedule.seriesEndScheduleData.split('-')[0];
                  var scheduleChildTimes = schedule.seriesEndScheduleData.split('-')[1].replace(/["']/g, "").split(',');
                  schedule.seriesStartDate = scheduleContinuity.seriesStartDate = moment(schedule.seriesStartDate + ' ' +  scheduleParentTimes[0].substring(1,scheduleParentTimes[0].length)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY-MM-DD');
                  if(schedule.seriesEndDate) {
                    schedule.seriesEndDate = scheduleContinuity.seriesEndDate = moment(schedule.seriesEndDate + ' ' +  scheduleChildTimes[scheduleChildTimes.length-1].substring(0,scheduleChildTimes[scheduleChildTimes.length-1].length-1)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                    if(moment(moment(schedule.seriesEndDate).add(-1, 'days').format('YYYY-MM-DD') + ' 23:59').diff(moment(schedule.seriesEndDate),'m') == -1) {
                      schedule.seriesEndDate = scheduleContinuity.seriesEndDate = moment(schedule.seriesEndDate).add(-1, 'days').format('YYYY-MM-DD');
                    } else {
                      schedule.seriesEndDate = scheduleContinuity.seriesEndDate = moment(schedule.seriesEndDate).format('YYYY-MM-DD');
                    }              
                  }

                  convertedStartDate = moment(moment(parentStartDate).format('YYYY-MM-DD') + ' ' +  scheduleParentTimes[0].substring(1,scheduleParentTimes[0].length)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                  convertedEndDate = moment(moment(ChildStartDate).format('YYYY-MM-DD') + ' ' +  scheduleChildTimes[scheduleChildTimes.length-1].substring(0,scheduleChildTimes[scheduleChildTimes.length-1].length-1)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').add(30, 'm');
                  if(moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59').diff(moment(convertedEndDate),'m') == -1) {
                    convertedEndDate = moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59');
                  }
                  if(moment(convertedStartDate).format('YYYY-MM-DD') == moment(convertedEndDate).format('YYYY-MM-DD')) {
                    schedule.parent_schedule_split_conversion = false;
                    schedule.parent_schedule_merge_conversion = true;
                  } else {
                    schedule.parent_schedule_split_conversion = true;
                    schedule.parent_schedule_merge_conversion = false;
                  }              
                } else {
                  var startDate = schedule.seriesStartScheduleData.split('-')[0];
                  var scheduleTimes = schedule.seriesStartScheduleData.split('-')[1].replace(/["']/g, "").split(',');
                  var sch = schedule;
                  schedule.seriesStartDate = scheduleContinuity.seriesStartDate = moment(schedule.seriesStartDate + ' ' +  scheduleTimes[0].substring(1,scheduleTimes[0].length)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY-MM-DD');
                  if(schedule.seriesEndDate) {
                    schedule.seriesEndDate = scheduleContinuity.seriesEndDate = moment(schedule.seriesEndDate + ' ' +  scheduleTimes[scheduleTimes.length-1].substring(0,scheduleTimes[scheduleTimes.length-1].length-1)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').add(30, 'm');
                    if(moment(moment(schedule.seriesEndDate).add(-1, 'days').format('YYYY-MM-DD') + ' 23:59').diff(moment(schedule.seriesEndDate),'m') == -1) {
                      schedule.seriesEndDate = scheduleContinuity.seriesEndDate = moment(schedule.seriesEndDate).add(-1, 'days').format('YYYY-MM-DD');
                    } else {
                      schedule.seriesEndDate = scheduleContinuity.seriesEndDate = moment(schedule.seriesEndDate).format('YYYY-MM-DD');
                    }
                  }
                  
                  convertedStartDate = moment(moment(startDate).format('YYYY-MM-DD') + ' ' +  scheduleTimes[0].substring(1,scheduleTimes[0].length)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                  convertedEndDate = moment(moment(startDate).format('YYYY-MM-DD') + ' ' +  scheduleTimes[scheduleTimes.length-1].substring(0,scheduleTimes[scheduleTimes.length-1].length-1)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').add(30, 'm');
                  if(moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59').diff(moment(convertedEndDate),'m') == -1) {
                    convertedEndDate = moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59');
                  }
                  if(moment(convertedStartDate).format('YYYY-MM-DD') == moment(convertedEndDate).format('YYYY-MM-DD')) {
                    schedule.parent_schedule_split_conversion = false;
                  } else {
                    schedule.parent_schedule_split_conversion = true;
                  }              
                }
              }
              this.userScheduleDetails.push(scheduleContinuity);
            } else {
              if(schedule.schedule_recurrence_type == 3 || schedule.schedule_recurrence_type == 2 || schedule.schedule_recurrence_type == 4 || schedule.schedule_recurrence_type == 7) {
                if(schedule.ranges.end) {
                  convertedEndDate = moment(schedule.ranges.end + ' ' +  schedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');              
                } else {
                  convertedEndDate = moment(schedule.ranges.start + ' ' +  schedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');              
                }
                if(convertedEndDate.format('HH:mm') == '00:00') {
                  convertedEndDate = convertedEndDate.add(-1, 'm');
                }
              }
              if(schedule.ranges.start.indexOf('T') != -1) {
                schedule.ranges.start = convertedStartDate.format('YYYY-MM-DDTHH:mm');
                if(schedule.ranges.end) {
                  schedule.ranges.end = convertedEndDate.format('YYYY-MM-DDTHH:mm');
                }
              } else {
                schedule.ranges.start = convertedStartDate.format('YYYY-MM-DD');
                if(schedule.ranges.end) {
                  schedule.ranges.end = convertedEndDate.format('YYYY-MM-DD');
                }
              }
              var a = schedule.schedule_end;
              if(schedule.schedule_end) {
                schedule.schedule_end =  moment(moment(schedule.schedule_end).format('YYYY-MM-DD') + ' ' +  schedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');   
                if(schedule.schedule_end.format('HH:mm') == '00:00') {
                  schedule.schedule_end = schedule.schedule_end.add(-1, 'm');
                  schedule.schedule_end = schedule.schedule_end.format('YYYY-MM-DD');
                }
              }
              if(schedule.occurrence_affected_dates) {
                let occurrence_affected_dates_time = schedule.occurrence_affected_dates_time.split('#');
                let converted_occurrence_affected_dates = new Array();
                schedule.occurrence_affected_dates = schedule.occurrence_affected_dates.split(',');
                occurrence_affected_dates_time.map( (time, key) => {
                  if(time.indexOf('child-') != -1) {
                    time = time.substring(7, time.indexOf(']'));
                    time = time.split(',');
                    time = time[time.length-1];
                    time = time.substring(1, time.length-1);
                  } else {
                    if(time.indexOf('[delete]') == -1) {
                      time = time.substring(1, time.indexOf(']'));
                      time = time.split(',');                
                      let startTime, endTime;
                      startTime = time[0];
                      endTime = time[time.length - 1];
                      startTime = startTime.substring(1, startTime.length-1);
                      endTime = endTime.substring(1, endTime.length-1);
                      startTime = moment(moment(schedule.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + startTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                      endTime = moment(moment(schedule.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + endTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                      if(startTime != endTime) {
                        converted_occurrence_affected_dates.push(startTime)
                      } else {
                        converted_occurrence_affected_dates.push(startTime)
                      }
                    } else {
                      let startTime, endTime;
                      startTime = moment(moment(schedule.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + schedule.start).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                      endTime = moment(moment(schedule.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + schedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                      if(startTime != endTime) {
                        converted_occurrence_affected_dates.push(startTime);
                        converted_occurrence_affected_dates.push(endTime)
                      } else {
                        converted_occurrence_affected_dates.push(startTime);
                      }
                    }
                  }
                });
                schedule.occurrence_affected_dates = converted_occurrence_affected_dates.join(',');
              }
              if(schedule.ranges.start.indexOf('T') == -1) {
                schedule.start = convertedStartDate.format('HH:mm');
                if(convertedEndDate.format('HH:mm') == '23:59') {
                  schedule.end = '24:00';
                } else {
                  schedule.end = convertedEndDate.format('HH:mm');
                }
              }
              schedule.day_number =  moment(schedule.ranges.start).day();
              if(schedule.schedule_recurrence_type == 2) {
                var dow = [];
                dow.push(moment(schedule.ranges.start).day() + '');
                schedule.dow = dow;
                schedule.title = 'Recurring on all ' + moment(schedule.ranges.start).format('dddd') + 's'; 
              }
              if(schedule.schedule_recurrence_type == 4) {
                let diffDays_scheduleSavedTimeZone = nextMonday.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                var dow = [];            
                for (var i = 0; i < 5; i++) {
                  dow.push(moment(schedule.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                }
                schedule.dow = dow;
                schedule.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']';
              }
              if(schedule.schedule_recurrence_type == 5) {
                if(schedule.reference_recurence_type == 4) {
                  let diffDays_scheduleSavedTimeZone = nextMonday.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                  var dow = [];
                  for (var i = 0; i < 5; i++) {
                    dow.push(moment(schedule.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                  }
                  schedule.dow = dow;
                  schedule.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']';
                }
              }
              if(schedule.schedule_recurrence_type == 7) {
                let diffDays_scheduleSavedTimeZone = nextWeekEndStartDay.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                var dow = [];
                for (var i = 0; i < weekEndDays.length; i++) {
                  dow.push(moment(schedule.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                }
                schedule.dow = dow;
                schedule.title = 'Recurring on all weekend days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']';
              }
              // if(schedule.schedule_recurrence_type == 7) {//handled paste weekend day as it is
              //   var dow = [];
              //   for (var i = 0; i < this.userData.config.weekend_days.length; i++) {
              //     if(i == 0) {
              //       dow.push(moment(schedule.ranges.start).day() + '');
              //     } else {
              //       var prev = dow[i-1]*1;
              //       if(prev==6) {
              //         dow.push(0+'');
              //       } else {
              //         dow.push((prev+1)+'')
              //       }
              //     }
              //   }
              //   schedule.dow = dow;
              //   schedule.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']'; 
              // }
              if(schedule.schedule_recurrence_type == 5) {
                let convertedStartDate, convertedEndDate;
                if(schedule.seriesStartScheduleData && schedule.seriesEndScheduleData) {
                  var parentStartDate = schedule.seriesStartScheduleData.split('-')[0];
                  var scheduleParentTimes = schedule.seriesStartScheduleData.split('-')[1].replace(/["']/g, "").split(',');
                  var ChildStartDate = schedule.seriesEndScheduleData.split('-')[0];
                  var scheduleChildTimes = schedule.seriesEndScheduleData.split('-')[1].replace(/["']/g, "").split(',');
                  schedule.seriesStartDate = moment(schedule.seriesStartDate + ' ' +  scheduleParentTimes[0].substring(1,scheduleParentTimes[0].length)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY-MM-DD');
                  if(schedule.seriesEndDate) {
                    schedule.seriesEndDate = moment(schedule.seriesEndDate + ' ' +  scheduleChildTimes[scheduleChildTimes.length-1].substring(0,scheduleChildTimes[scheduleChildTimes.length-1].length-1)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').add(30, 'm');
                    if(moment(moment(schedule.seriesEndDate).add(-1, 'days').format('YYYY-MM-DD') + ' 23:59').diff(moment(schedule.seriesEndDate),'m') == -1) {
                      schedule.seriesEndDate = moment(schedule.seriesEndDate).add(-1, 'days').format('YYYY-MM-DD');
                    } else {
                      schedule.seriesEndDate = moment(schedule.seriesEndDate).format('YYYY-MM-DD');
                    }
                  }

                  convertedStartDate = moment(moment(parentStartDate).format('YYYY-MM-DD') + ' ' +  scheduleParentTimes[0].substring(1,scheduleParentTimes[0].length)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                  convertedEndDate = moment(moment(ChildStartDate).format('YYYY-MM-DD') + ' ' +  scheduleChildTimes[scheduleChildTimes.length-1].substring(0,scheduleChildTimes[scheduleChildTimes.length-1].length-1)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').add(30, 'm');
                  if(moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59').diff(moment(convertedEndDate),'m') == -1) {
                    convertedEndDate = moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59');
                  }
                  if(moment(convertedStartDate).format('YYYY-MM-DD') == moment(convertedEndDate).format('YYYY-MM-DD')) {
                    schedule.parent_schedule_split_conversion = false;
                    schedule.parent_schedule_merge_conversion = true;
                  } else {
                    schedule.parent_schedule_split_conversion = true;
                    schedule.parent_schedule_merge_conversion = false;
                  }              
                } else {
                  var startDate = schedule.seriesStartScheduleData.split('-')[0];
                  var scheduleTimes = schedule.seriesStartScheduleData.split('-')[1].replace(/["']/g, "").split(',');
                  var a = schedule;
                  schedule.seriesStartDate = moment(schedule.seriesStartDate + ' ' +  scheduleTimes[0].substring(1,scheduleTimes[0].length)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY-MM-DD');
                  if(schedule.seriesEndDate) {
                    schedule.seriesEndDate = moment(schedule.seriesEndDate + ' ' +  scheduleTimes[scheduleTimes.length-1].substring(0,scheduleTimes[scheduleTimes.length-1].length-1)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').add(30, 'm');
                    if(moment(moment(schedule.seriesEndDate).add(-1, 'days').format('YYYY-MM-DD') + ' 23:59').diff(moment(schedule.seriesEndDate),'m') == -1) {
                      schedule.seriesEndDate = moment(schedule.seriesEndDate).add(-1, 'days').format('YYYY-MM-DD');
                    } else {
                      schedule.seriesEndDate = moment(schedule.seriesEndDate).format('YYYY-MM-DD');
                    }
                  }

                  convertedStartDate = moment(moment(startDate).format('YYYY-MM-DD') + ' ' +  scheduleTimes[0].substring(1,scheduleTimes[0].length)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                  convertedEndDate = moment(moment(startDate).format('YYYY-MM-DD') + ' ' +  scheduleTimes[scheduleTimes.length-1].substring(0,scheduleTimes[scheduleTimes.length-1].length-1)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').add(30, 'm');
                  if(moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59').diff(moment(convertedEndDate),'m') == -1) {
                    convertedEndDate = moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59');
                  }
                  if(moment(convertedStartDate).format('YYYY-MM-DD') == moment(convertedEndDate).format('YYYY-MM-DD')) {
                    schedule.parent_schedule_split_conversion = false;
                  } else {
                    schedule.parent_schedule_split_conversion = true;
                  }              
                }            
              }
            }
          }
        });
        if(this.userScheduleDetails.length) {
          this.userScheduleDetails.forEach( function(schedule) {
            results['singleDaySchedule'].push(schedule);
          });
        }

        this.userScheduleDetails = [];
        
        results['multipleDaySchedule']['parent'].map(schedule => {
          if(schedule && ("ranges" in schedule) && Object.keys(schedule.ranges).length) {
            let a = JSON.parse(JSON.stringify(schedule));
            let convertedParentStartDate = moment(schedule.ranges.start + ' ' +  schedule.start).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
            let convertedParentEndDate:any;
            let convertedStartDate__scheduleSavedTimeZone = moment(schedule.ranges.start + ' ' +  schedule.start).add(timeToPassDay_scheduleSavedTimeZone[0], 'hours').add(timeToPassDay_scheduleSavedTimeZone[1], 'm');
            let nextMonday = null;
            var dayINeed = 1;
            if (moment(convertedStartDate__scheduleSavedTimeZone).isoWeekday() <= dayINeed) {
              nextMonday = moment(convertedStartDate__scheduleSavedTimeZone).isoWeekday(dayINeed);
            } else {
              nextMonday = moment(convertedStartDate__scheduleSavedTimeZone).add(1, 'weeks').isoWeekday(dayINeed);
            }
            let nextWeekEndStartDay = null;
            let weekEndDays = JSON.parse(JSON.stringify(this.userData.config.weekend_days));
            let WeekEndStartDay =  weekEndDays[0]*1;
            if (moment(convertedStartDate__scheduleSavedTimeZone).isoWeekday() <= WeekEndStartDay) {
              nextWeekEndStartDay = moment(convertedStartDate__scheduleSavedTimeZone).isoWeekday(WeekEndStartDay);
            } else {
              nextWeekEndStartDay = moment(convertedStartDate__scheduleSavedTimeZone).add(1, 'weeks').isoWeekday(WeekEndStartDay);
            }
            if(schedule.schedule_recurrence_type == 3 || schedule.schedule_recurrence_type == 2 || schedule.schedule_recurrence_type == 4 || schedule.schedule_recurrence_type == 7) {
              convertedParentEndDate = moment(schedule.ranges.start + ' ' +  schedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
            } else {
              if(schedule.ranges.end) {
                convertedParentEndDate = moment(schedule.ranges.end + ' ' +  schedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
              } else {
                convertedParentEndDate = moment(schedule.ranges.start + ' ' +  schedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
              }
            }
            if(moment(convertedParentStartDate.format('YYYY-MM-DD') + ' 23:59').diff(moment(convertedParentEndDate),'m') == -1) {
              convertedParentEndDate = moment(convertedParentStartDate.format('YYYY-MM-DD') + ' 23:59');
            }
            
            results['multipleDaySchedule']['child'] = results['multipleDaySchedule']['child'].filter(childSchedule => {
              if(childSchedule && ("ranges" in childSchedule) && Object.keys(childSchedule.ranges).length) {
                if(childSchedule['parent_reference_id'] == schedule['reference_id']) {
                  let convertedchildStartDate = moment(childSchedule.ranges.start + ' ' +  childSchedule.start).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                  let convertedchildEndDate:any;
                  if(childSchedule.schedule_recurrence_type == 3 || schedule.schedule_recurrence_type == 2 || schedule.schedule_recurrence_type == 4 || schedule.schedule_recurrence_type == 7) {
                    convertedchildEndDate = moment(childSchedule.ranges.start + ' ' +  childSchedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                  } else {
                    if(childSchedule.ranges.end) {
                      convertedchildEndDate = moment(childSchedule.ranges.end + ' ' +  childSchedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                    } else {
                      convertedchildEndDate = moment(childSchedule.ranges.start + ' ' +  childSchedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                    }
                  }
                  
                  if(moment(convertedchildStartDate.format('YYYY-MM-DD') + ' 23:59').diff(moment(convertedchildEndDate),'m') == -1) {
                    convertedchildEndDate = moment(convertedchildStartDate.format('YYYY-MM-DD') + ' 23:59');
                  }

                  if(convertedchildEndDate.isAfter(moment(convertedParentStartDate.format('YYYY-MM-DD') + ' 23:59'))) {
                    if(childSchedule.schedule_recurrence_type == 3 || schedule.schedule_recurrence_type == 2 || schedule.schedule_recurrence_type == 4 || schedule.schedule_recurrence_type == 7) {
                      if(childSchedule.ranges.end) {
                        convertedchildEndDate = moment(childSchedule.ranges.end + ' ' +  childSchedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                      } else {
                        convertedchildEndDate = moment(childSchedule.ranges.start + ' ' +  childSchedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                      }
                      if(convertedchildEndDate.format('HH:mm') == '00:00') {
                        convertedchildEndDate = convertedchildEndDate.add(-1, 'm');
                      }
                      if(schedule.ranges.end) {
                        convertedParentEndDate = moment(schedule.ranges.end + ' ' +  schedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                      } else {
                        convertedParentEndDate = moment(schedule.ranges.start + ' ' +  schedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                      }
                      if(convertedParentEndDate.format('HH:mm') == '00:00') {
                        convertedParentEndDate = convertedParentEndDate.add(-1, 'm');
                      }
                    }
                    let addDayCountOnOccurence = convertedParentStartDate.diff(moment(schedule.ranges.start), 'days');
                    let occurrence_affected_dates = [];
                    if(schedule.occurrence_affected_dates) {
                      occurrence_affected_dates = schedule.occurrence_affected_dates.split(',');
                      occurrence_affected_dates.map( (date, key) => {
                        occurrence_affected_dates[key] = moment(moment(date).format('YYYY-MM-DD') + ' ' + schedule.start).add(addDayCountOnOccurence, 'days').format('YYYYMMDD');
                      });
                      schedule.occurrence_affected_dates = occurrence_affected_dates.join(',');
                    }
                    let scheduleContinuity = JSON.parse(JSON.stringify(childSchedule));
                    if(schedule.schedule_recurrence_type == 3 || schedule.schedule_recurrence_type == 2 || schedule.schedule_recurrence_type == 4 || schedule.schedule_recurrence_type == 7) {
                      schedule.ranges.start = convertedParentStartDate.format('YYYY-MM-DD');
                      if(schedule.ranges.end) {
                        schedule.ranges.end = convertedchildEndDate.add(-1, 'days').format('YYYY-MM-DD');
                      }
                    } else {
                      schedule.ranges.start = convertedParentStartDate.format('YYYY-MM-DD');
                      if(schedule.ranges.end) {
                        schedule.ranges.end = convertedParentStartDate.format('YYYY-MM-DD');
                      }
                    }
                    if(schedule.schedule_end) {
                      schedule.schedule_end = moment(moment(schedule.schedule_end).format('YYYY-MM-DD') + ' ' + schedule.start).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');          
                    }
                    schedule.start = convertedParentStartDate.format('HH:mm');
                    schedule.end = '24:00';
                    schedule.day_number = moment(schedule.ranges.start).day() + '';
                    if(schedule.schedule_recurrence_type == 2) {
                      var dow = [];
                      dow.push(moment(schedule.ranges.start).day() + '');
                      schedule.dow = dow;
                      schedule.title = 'Recurring on all ' + moment(schedule.ranges.start).format('dddd') + 's';
                    }
                    if(schedule.schedule_recurrence_type == 4) {
                      var dow = [];
                      let diffDays_scheduleSavedTimeZone = nextMonday.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                      for (var i = 0; i < 5; i++) {
                        dow.push(moment(schedule.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                      }

                      schedule.dow = dow;
                      schedule.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']';
                    }
                    if(schedule.schedule_recurrence_type == 5) {
                      if(schedule.reference_recurence_type == 4) {
                        var dow = [];
                        let diffDays_scheduleSavedTimeZone = nextMonday.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                        for (var i = 0; i < 5; i++) {
                          dow.push(moment(schedule.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                        }

                        schedule.dow = dow;
                        schedule.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']';
                      }
                    }
                    if(schedule.schedule_recurrence_type == 7) {
                      var dow = [];
                      let diffDays_scheduleSavedTimeZone = nextWeekEndStartDay.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                      for (var i = 0; i < weekEndDays.length; i++) {
                        dow.push(moment(schedule.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                      }
                      schedule.dow = dow;
                      schedule.title = 'Recurring on all weekend days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']';
                    }
                    // if(schedule.schedule_recurrence_type == 7) {//handled paste weekend day as it is
                    //   var dow = [];
                    //   for (var i = 0; i < this.userData.config.weekend_days.length; i++) {
                    //     if(i == 0) {
                    //       dow.push(moment(schedule.ranges.start).day() + '');
                    //     } else {
                    //       var prev = dow[i-1]*1;
                    //       if(prev==6) {
                    //         dow.push(0+'');
                    //       } else {
                    //         dow.push((prev+1)+'')
                    //       }
                    //     }
                    //   }
                    //   schedule.dow = dow;
                    //   schedule.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']'; 
                    // }
                    schedule.schedule_split_conversion = true;
                    schedule.schedule_split_conversion_child = false;
                    occurrence_affected_dates = [];
                    if(childSchedule.occurrence_affected_dates) {
                      occurrence_affected_dates = childSchedule.occurrence_affected_dates.split(',');
                      occurrence_affected_dates.map( (date, key) => {
                        occurrence_affected_dates[key] = moment(moment(date).format('YYYY-MM-DD') + ' ' + schedule.start).add(addDayCountOnOccurence, 'days').format('YYYYMMDD');
                      });
                      scheduleContinuity.occurrence_affected_dates =   occurrence_affected_dates.join(',');
                    }             
                    if(scheduleContinuity.schedule_recurrence_type == 3 || schedule.schedule_recurrence_type == 2 || schedule.schedule_recurrence_type == 4 || schedule.schedule_recurrence_type == 7) {
                      scheduleContinuity.ranges.start = convertedParentStartDate.add(1, 'days').format('YYYY-MM-DD');
                      if(scheduleContinuity.ranges.end) {
                        scheduleContinuity.ranges.end = convertedchildEndDate.add(1, 'days').format('YYYY-MM-DD');
                      }
                    } else if(scheduleContinuity.schedule_recurrence_type == 5) {
                      scheduleContinuity.ranges.start = convertedchildEndDate.format('YYYY-MM-DD');
                      if(scheduleContinuity.ranges.end) {
                        scheduleContinuity.ranges.end = convertedchildEndDate.format('YYYY-MM-DD');
                      }
                    } else {
                      scheduleContinuity.ranges.start = convertedchildEndDate.format('YYYY-MM-DD');
                      if(scheduleContinuity.ranges.end) {
                        scheduleContinuity.ranges.end = convertedchildEndDate.format('YYYY-MM-DD');
                      }
                    }
                    if(scheduleContinuity.schedule_end) {
                      var a = scheduleContinuity;
                      scheduleContinuity.schedule_end = moment(moment(scheduleContinuity.schedule_end).format('YYYY-MM-DD') + ' ' + scheduleContinuity.start).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');                        
                    }
                    scheduleContinuity.start = '00:00';
                    if(convertedchildEndDate.format('HH:mm') == '23:59') {
                      scheduleContinuity.end = '24:00';
                    } else {
                      scheduleContinuity.end = convertedchildEndDate.format('HH:mm');
                    }
                    scheduleContinuity.reference_id = childSchedule.reference_id;
                    scheduleContinuity.parent_reference_id = childSchedule.parent_reference_id;         
                    scheduleContinuity.schedule_end = schedule.schedule_end;
                    scheduleContinuity.day_number = moment(scheduleContinuity.ranges.start).day() + '';
                    if(scheduleContinuity.schedule_recurrence_type == 2) {
                      var dow = [];
                      dow.push(moment(scheduleContinuity.ranges.start).day() + '');
                      scheduleContinuity.dow = dow;
                      scheduleContinuity.title = 'Recurring on all ' + moment(scheduleContinuity.ranges.start).format('dddd') + 's'; 
                    }
                    if(scheduleContinuity.schedule_recurrence_type == 4) {
                      var dow = [];
                      let diffDays_scheduleSavedTimeZone = nextMonday.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                      for (var i = 0; i < 5; i++) {
                        dow.push(moment(scheduleContinuity.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                      }
                      scheduleContinuity.dow = dow;
                      scheduleContinuity.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']';
                    }
                    if(scheduleContinuity.schedule_recurrence_type == 5) {
                      if(scheduleContinuity.reference_recurence_type == 4) {
                        var dow = [];
                        let diffDays_scheduleSavedTimeZone = nextMonday.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                        for (var i = 0; i < 5; i++) {
                          dow.push(moment(scheduleContinuity.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                        }
                        scheduleContinuity.dow = dow;
                        scheduleContinuity.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']';
                      }
                    }
                    if(scheduleContinuity.schedule_recurrence_type == 7) {
                      var dow = [];
                      let diffDays_scheduleSavedTimeZone = nextWeekEndStartDay.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                      for (var i = 0; i < weekEndDays.length; i++) {
                        dow.push(moment(scheduleContinuity.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                      }
                      scheduleContinuity.dow = dow;
                      scheduleContinuity.title = 'Recurring on all weekend days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']';
                    }
                    // if(scheduleContinuity.schedule_recurrence_type == 7) {//handled paste weekend day as it is
                    //   var dow = [];
                    //   for (var i = 0; i < this.userData.config.weekend_days.length; i++) {
                    //     if(i == 0) {
                    //       dow.push(moment(scheduleContinuity.ranges.start).day() + '');
                    //     } else {
                    //       var prev = dow[i-1]*1;
                    //       if(prev==6) {
                    //         dow.push(0+'');
                    //       } else {
                    //         dow.push((prev+1)+'')
                    //       }
                    //     }
                    //   }
                    //   scheduleContinuity.dow = dow;
                    //   scheduleContinuity.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']'; 
                    // }              
                    scheduleContinuity.schedule_split_conversion = true;
                    scheduleContinuity.schedule_split_conversion_child = true;
                    schedule.converted_timeSpan = scheduleContinuity.converted_timeSpan = schedule['ranges']['start'] + ' ' + schedule.start + '$' + scheduleContinuity['ranges']['start'] + ' ' + scheduleContinuity.end;

                    if(schedule.schedule_recurrence_type == 5) {
                      let convertedStartDate, convertedEndDate;
                      if(schedule.seriesStartScheduleData && schedule.seriesEndScheduleData) {
                        var parentStartDate = schedule.seriesStartScheduleData.split('-')[0];
                        var scheduleParentTimes = schedule.seriesStartScheduleData.split('-')[1].replace(/["']/g, "").split(',');
                        var ChildStartDate = schedule.seriesEndScheduleData.split('-')[0];
                        var scheduleChildTimes = schedule.seriesEndScheduleData.split('-')[1].replace(/["']/g, "").split(',');
                        schedule.seriesStartDate = scheduleContinuity.seriesStartDate = moment(schedule.seriesStartDate + ' ' +  scheduleParentTimes[0].substring(1,scheduleParentTimes[0].length)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY-MM-DD');
                        if(schedule.seriesEndDate) {
                          schedule.seriesEndDate = scheduleContinuity.seriesEndDate = moment(schedule.seriesEndDate + ' ' +  scheduleChildTimes[scheduleChildTimes.length-1].substring(0,scheduleChildTimes[scheduleChildTimes.length-1].length-1)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                          if(moment(moment(schedule.seriesEndDate).add(-1, 'days').format('YYYY-MM-DD') + ' 23:59').diff(moment(schedule.seriesEndDate),'m') == -1) {
                            schedule.seriesEndDate = scheduleContinuity.seriesEndDate = moment(schedule.seriesEndDate).add(-1, 'days').format('YYYY-MM-DD');
                          } else {
                            schedule.seriesEndDate = scheduleContinuity.seriesEndDate = moment(schedule.seriesEndDate).format('YYYY-MM-DD');
                          }
                        }
          
                        convertedStartDate = moment(moment(parentStartDate).format('YYYY-MM-DD') + ' ' +  scheduleParentTimes[0].substring(1,scheduleParentTimes[0].length)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                        convertedEndDate = moment(moment(ChildStartDate).format('YYYY-MM-DD') + ' ' +  scheduleChildTimes[scheduleChildTimes.length-1].substring(0,scheduleChildTimes[scheduleChildTimes.length-1].length-1)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').add(30, 'm');
                        if(moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59').diff(moment(convertedEndDate),'m') == -1) {
                          convertedEndDate = moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59');
                        }
                        if(moment(convertedStartDate).format('YYYY-MM-DD') == moment(convertedEndDate).format('YYYY-MM-DD')) {
                          schedule.parent_schedule_split_conversion = false;
                          schedule.parent_schedule_merge_conversion = true;
                        } else {
                          schedule.parent_schedule_split_conversion = true;
                          schedule.parent_schedule_merge_conversion = false;
                        }              
                      } else {
                        var startDate = schedule.seriesStartScheduleData.split('-')[0];
                        var scheduleTimes = schedule.seriesStartScheduleData.split('-')[1].replace(/["']/g, "").split(',');
                        var sch = schedule;
                        schedule.seriesStartDate = scheduleContinuity.seriesStartDate = moment(schedule.seriesStartDate + ' ' +  scheduleTimes[0].substring(1,scheduleTimes[0].length)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY-MM-DD');
                        if(schedule.seriesEndDate) {
                          schedule.seriesEndDate = scheduleContinuity.seriesEndDate = moment(schedule.seriesEndDate + ' ' +  scheduleTimes[scheduleTimes.length-1].substring(0,scheduleTimes[scheduleTimes.length-1].length-1)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').add(30, 'm');
                          if(moment(moment(schedule.seriesEndDate).add(-1, 'days').format('YYYY-MM-DD') + ' 23:59').diff(moment(schedule.seriesEndDate),'m') == -1) {
                            schedule.seriesEndDate = scheduleContinuity.seriesEndDate = moment(schedule.seriesEndDate).add(-1, 'days').format('YYYY-MM-DD');
                          } else {
                            schedule.seriesEndDate = scheduleContinuity.seriesEndDate = moment(schedule.seriesEndDate).format('YYYY-MM-DD');
                          }
                        }
                        
                        convertedStartDate = moment(moment(startDate).format('YYYY-MM-DD') + ' ' +  scheduleTimes[0].substring(1,scheduleTimes[0].length)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                        convertedEndDate = moment(moment(startDate).format('YYYY-MM-DD') + ' ' +  scheduleTimes[scheduleTimes.length-1].substring(0,scheduleTimes[scheduleTimes.length-1].length-1)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').add(30, 'm');
                        if(moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59').diff(moment(convertedEndDate),'m') == -1) {
                          convertedEndDate = moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59');
                        }
                        if(moment(convertedStartDate).format('YYYY-MM-DD') == moment(convertedEndDate).format('YYYY-MM-DD')) {
                          schedule.parent_schedule_split_conversion = false;
                        } else {
                          schedule.parent_schedule_split_conversion = true;
                        }              
                      }
                    }
                    
                    this.userScheduleDetails.push(scheduleContinuity);
                  } else {
                    if(childSchedule.schedule_recurrence_type == 3 || schedule.schedule_recurrence_type == 2 || schedule.schedule_recurrence_type == 4 || schedule.schedule_recurrence_type == 7) {
                      if(childSchedule.ranges.end) {
                        convertedchildEndDate = moment(childSchedule.ranges.end + ' ' +  childSchedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                      } else {
                        convertedchildEndDate = moment(childSchedule.ranges.start + ' ' +  childSchedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                      }
                      if(convertedchildEndDate.format('HH:mm') == '00:00') {
                        convertedchildEndDate = convertedchildEndDate.add(-1, 'm');
                      }
                      if(schedule.ranges.end) {
                        convertedParentEndDate = moment(schedule.ranges.end + ' ' +  schedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                      }
                      if(convertedParentEndDate.format('HH:mm') == '00:00') {
                        convertedParentEndDate = convertedParentEndDate.add(-1, 'm');
                      }
                    } else if(childSchedule.schedule_recurrence_type == 5) {
                      convertedchildEndDate = moment(childSchedule.ranges.end + ' ' +  childSchedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');  
                      if(convertedchildEndDate.format('HH:mm') == '00:00') {
                        convertedchildEndDate = convertedchildEndDate.add(-1, 'm');
                      }
                    }
                    schedule.schedule_merge_conversion = true;
                    schedule.merge_parent_reference_id = childSchedule.parent_reference_id;
                    schedule.ranges.start = convertedParentStartDate.format('YYYY-MM-DD');
                    if(schedule.ranges.end) {
                      if(childSchedule.schedule_recurrence_type == 5) {
                        schedule.ranges.end = convertedchildEndDate.format('YYYY-MM-DD');
                      } else if(childSchedule.schedule_recurrence_type == 3) {
                        schedule.ranges.end = convertedchildEndDate.format('YYYY-MM-DD');
                      } else if(childSchedule.schedule_recurrence_type != 1) {
                        if(childSchedule.schedule_recurrence_type != 4) {
                          schedule.ranges.end = convertedchildEndDate.add(-1,'days').format('YYYY-MM-DD');
                        } else {
                          schedule.ranges.end = convertedchildEndDate.format('YYYY-MM-DD');
                        }
                      } else {
                        schedule.ranges.end = convertedchildEndDate.format('YYYY-MM-DD');
                      }
                    }
                    if(schedule.occurrence_affected_dates) {
                      let occurrence_affected_dates_time = schedule.occurrence_affected_dates_time.split('#');
                      schedule.occurrence_affected_dates = schedule.occurrence_affected_dates.split(',');                
                      occurrence_affected_dates_time.map( (time, key) => {
                        if(time.indexOf('child-') != -1) {
                          time = time.substring(7, time.indexOf(']'));
                          time = time.split(',');
                          time = time[time.length-1];
                          time = time.substring(1, time.length-1);
                          schedule.occurrence_affected_dates[key] = moment(moment(schedule.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + time).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                        } else {
                          if(time.indexOf('[delete]') == -1) {
                            time = time.substring(1, time.indexOf(']'));
                            time = time.split(',');                
                            let startTime, endTime;
                            startTime = time[0];
                            endTime = time[time.length - 1];
                            startTime = startTime.substring(1, startTime.length-1);
                            endTime = endTime.substring(1, endTime.length-1);
                            startTime = moment(moment(schedule.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + startTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                            endTime = moment(moment(schedule.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + endTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                            if(startTime != endTime) {
                              schedule.occurrence_affected_dates[key] = startTime;
                              schedule.occurrence_affected_dates.push(endTime);
                            } else {
                              schedule.occurrence_affected_dates[key] = startTime;
                            }
                          } else {
                            let startTime, endTime;
                            startTime = moment(moment(schedule.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + schedule.start).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                            endTime = moment(moment(schedule.occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + schedule.end).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                            if(startTime != endTime) {
                              schedule.occurrence_affected_dates[key] = startTime;
                              schedule.occurrence_affected_dates.push(endTime);
                            } else {
                              schedule.occurrence_affected_dates[key] = startTime;
                            }
                          }
                        }
                      });
                      let child_occurrence_affected_dates = [];
                      if(childSchedule.occurrence_affected_dates)
                      child_occurrence_affected_dates = childSchedule.occurrence_affected_dates.split(',');
                      let child_occurrence_affected_dates_time=[];
                      if(childSchedule.occurrence_affected_dates_time)
                      child_occurrence_affected_dates_time = childSchedule.occurrence_affected_dates_time.split('#');
                      child_occurrence_affected_dates_time.map( (time, key) => {
                        if(time.indexOf('child-[]') == -1) {
                          if(time.indexOf('child-') != -1) {
                            if(time.indexOf('child-[delete]') == -1) {            
                              time = time.substring(7, time.indexOf(']'));
                              time = time.split(',');
                              time = time[time.length-1];
                              time = time.substring(1, time.length-1);
                              child_occurrence_affected_dates[key] = moment(moment(child_occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + time).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                            } else{
                              let startTime, endTime;
                              child_occurrence_affected_dates[key] = moment(moment(child_occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + childSchedule.start).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                            }
                          } else {
                            time = time.substring(1, time.indexOf(']'));
                            time = time.split(',');                
                            let startTime, endTime;
                            startTime = time[0];
                            endTime = time[time.length - 1];
                            startTime = startTime.substring(1, startTime.length-1);
                            endTime = endTime.substring(1, endTime.length-1);
                            startTime = moment(moment(child_occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + startTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                            endTime = moment(moment(child_occurrence_affected_dates[key]).format('YYYY-MM-DD') + ' ' + endTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                            if(startTime != endTime) {
                              child_occurrence_affected_dates[key] = startTime;
                              child_occurrence_affected_dates.push(endTime);
                            } else {
                              child_occurrence_affected_dates[key] = startTime;
                            }
                          }
                        } else {
                          child_occurrence_affected_dates[key] = 'empty';
                        }
                      });
                      child_occurrence_affected_dates = child_occurrence_affected_dates.filter(function(date) {
                        return date!='empty'
                      });
                      schedule.occurrence_affected_dates = schedule.occurrence_affected_dates.concat(child_occurrence_affected_dates);
                      schedule.occurrence_affected_dates = schedule.occurrence_affected_dates.join(',');
                    }
                    if(schedule.schedule_end) {
                      var a = childSchedule;
                      schedule.schedule_end = moment(moment(childSchedule.schedule_end).format('YYYY-MM-DD') + ' ' + childSchedule.start).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
                    }
                    schedule.start = convertedParentStartDate.format('HH:mm');              
                    if(convertedchildEndDate.format('HH:mm') == '23:59') {
                      schedule.end = '24:00';
                    } else {
                      schedule.end = convertedchildEndDate.format('HH:mm');
                    }
                    schedule.day_number = moment(schedule.ranges.start).day();
                    if(schedule.schedule_recurrence_type == 2) {
                      var dow = [];
                      dow.push(moment(schedule.ranges.start).day() + '');
                      schedule.dow = dow;
                      schedule.title = 'Recurring on all ' + moment(schedule.ranges.start).format('dddd') + 's';  
                    }
                    if(schedule.schedule_recurrence_type == 4) {
                      let diffDays_scheduleSavedTimeZone = nextMonday.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                      var dow = [];            
                      for (var i = 0; i < 5; i++) {
                        dow.push(moment(schedule.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                      }
                      schedule.dow = dow;
                      schedule.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']' ;
                    }
                    if(schedule.schedule_recurrence_type == 5) {
                      if(schedule.reference_recurence_type == 4) {
                        let diffDays_scheduleSavedTimeZone = nextMonday.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                        var dow = [];
                        for (var i = 0; i < 5; i++) {
                          dow.push(moment(schedule.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                        }
                        schedule.dow = dow;
                        schedule.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']' ;
                      }
                    }
                    if(schedule.schedule_recurrence_type == 7) {
                      var dow = [];
                      let diffDays_scheduleSavedTimeZone = nextWeekEndStartDay.diff(convertedStartDate__scheduleSavedTimeZone, 'days');
                      for (var i = 0; i < weekEndDays.length; i++) {
                        dow.push(moment(schedule.ranges.start).add(diffDays_scheduleSavedTimeZone, 'days').add(i,'days').day() + '');
                      }
                      schedule.dow = dow;
                      schedule.title = 'Recurring on all weekend days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']';
                    }
                    // if(schedule.schedule_recurrence_type == 7) {//handled paste weekend day as it is
                    //   var dow = [];
                    //   for (var i = 0; i < this.userData.config.weekend_days.length; i++) {
                    //     if(i == 0) {
                    //       dow.push(moment(schedule.ranges.start).day() + '');
                    //     } else {
                    //       var prev = dow[i-1]*1;
                    //       if(prev==6) {
                    //         dow.push(0+'');
                    //       } else {
                    //         dow.push((prev+1)+'')
                    //       }
                    //     }
                    //   }
                    //   schedule.dow = dow;
                    //   schedule.title = 'Recurring on all week days [' + moment().isoWeekday(parseInt(dow[0])).format('ddd') + '-' + moment().isoWeekday(parseInt(dow[dow.length-1])).format('ddd') + ']'; 
                    // }              
                    if(schedule.schedule_recurrence_type == 5) {
                      let convertedStartDate, convertedEndDate;
                      if(schedule.seriesStartScheduleData && schedule.seriesEndScheduleData) {
                        var parentStartDate = schedule.seriesStartScheduleData.split('-')[0];
                        var scheduleParentTimes = schedule.seriesStartScheduleData.split('-')[1].replace(/["']/g, "").split(',');
                        var ChildStartDate = schedule.seriesEndScheduleData.split('-')[0];
                        var scheduleChildTimes = schedule.seriesEndScheduleData.split('-')[1].replace(/["']/g, "").split(',');
                        schedule.seriesStartDate = moment(schedule.seriesStartDate + ' ' +  scheduleParentTimes[0].substring(1,scheduleParentTimes[0].length)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY-MM-DD');
                        if(schedule.seriesEndDate) {
                          schedule.seriesEndDate = moment(schedule.seriesEndDate + ' ' +  scheduleChildTimes[scheduleChildTimes.length-1].substring(0,scheduleChildTimes[scheduleChildTimes.length-1].length-1)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').add(30, 'm');
                          if(moment(moment(schedule.seriesEndDate).add(-1, 'days').format('YYYY-MM-DD') + ' 23:59').diff(moment(schedule.seriesEndDate),'m') == -1) {
                            schedule.seriesEndDate = moment(schedule.seriesEndDate).add(-1, 'days').format('YYYY-MM-DD');
                          } else {
                            schedule.seriesEndDate = moment(schedule.seriesEndDate).format('YYYY-MM-DD');
                          }
                        }
          
                        convertedStartDate = moment(moment(parentStartDate).format('YYYY-MM-DD') + ' ' +  scheduleParentTimes[0].substring(1,scheduleParentTimes[0].length)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                        convertedEndDate = moment(moment(ChildStartDate).format('YYYY-MM-DD') + ' ' +  scheduleChildTimes[scheduleChildTimes.length-1].substring(0,scheduleChildTimes[scheduleChildTimes.length-1].length-1)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').add(30, 'm');
                        if(moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59').diff(moment(convertedEndDate),'m') == -1) {
                          convertedEndDate = moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59');
                        }
                        if(moment(convertedStartDate).format('YYYY-MM-DD') == moment(convertedEndDate).format('YYYY-MM-DD')) {
                          schedule.parent_schedule_split_conversion = false;
                          schedule.parent_schedule_merge_conversion = true;
                        } else {
                          schedule.parent_schedule_split_conversion = true;
                          schedule.parent_schedule_merge_conversion = false;
                        }              
                      } else {
                        var startDate = schedule.seriesStartScheduleData.split('-')[0];
                        var scheduleTimes = schedule.seriesStartScheduleData.split('-')[1].replace(/["']/g, "").split(',');
                        var a = schedule;
                        schedule.seriesStartDate = moment(schedule.seriesStartDate + ' ' +  scheduleTimes[0].substring(1,scheduleTimes[0].length)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY-MM-DD');
                        if(schedule.seriesEndDate) {
                          schedule.seriesEndDate = moment(schedule.seriesEndDate + ' ' +  scheduleTimes[scheduleTimes.length-1].substring(0,scheduleTimes[scheduleTimes.length-1].length-1)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').add(30, 'm');
                          if(moment(moment(schedule.seriesEndDate).add(-1, 'days').format('YYYY-MM-DD') + ' 23:59').diff(moment(schedule.seriesEndDate),'m') == -1) {
                            schedule.seriesEndDate = moment(schedule.seriesEndDate).add(-1, 'days').format('YYYY-MM-DD');
                          } else {
                            schedule.seriesEndDate = moment(schedule.seriesEndDate).format('YYYY-MM-DD');
                          }
                        }
          
                        convertedStartDate = moment(moment(startDate).format('YYYY-MM-DD') + ' ' +  scheduleTimes[0].substring(1,scheduleTimes[0].length)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm');
                        convertedEndDate = moment(moment(startDate).format('YYYY-MM-DD') + ' ' +  scheduleTimes[scheduleTimes.length-1].substring(0,scheduleTimes[scheduleTimes.length-1].length-1)).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').add(30, 'm');
                        if(moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59').diff(moment(convertedEndDate),'m') == -1) {
                          convertedEndDate = moment(convertedStartDate.format('YYYY-MM-DD') + ' 23:59');
                        }
                        if(moment(convertedStartDate).format('YYYY-MM-DD') == moment(convertedEndDate).format('YYYY-MM-DD')) {
                          schedule.parent_schedule_split_conversion = false;
                        } else {
                          schedule.parent_schedule_split_conversion = true;
                        }              
                      }            
                    }
                  }
                  this.userScheduleDetails.push(schedule);
                  return false;
                } else {
                  return true;
                }
              }
            });
          }
        });      
        results['multipleDaySchedule'] = [];

        if(this.userScheduleDetails.length) {
          this.userScheduleDetails.forEach( function(schedule) {
            results['multipleDaySchedule'].push(schedule);
          });
        }

        this.userScheduleDetails = [];

        results['singleDaySchedule'].forEach(schedule => {
          schedule.disableAllDaySelector = false;
          this.userScheduleDetails.push(schedule);
        });
        
        results['multipleDaySchedule'].forEach(schedule => {
          schedule.disableAllDaySelector = true;
          this.userScheduleDetails.push(schedule);
        });
        console.log('------multipleDaySchedule-----');
        console.log(results['multipleDaySchedule']);

        let intervalStart = this.fullcalendarView.intervalStart.format('YYYY-MM-DD');
        let intervalEnd = this.fullcalendarView.intervalEnd.format('YYYY-MM-DD');
        let filteredSchedulesBeforeRender = this.userScheduleDetails.filter((schedule) => {
          if(schedule && ("ranges" in schedule) && Object.keys(schedule.ranges).length) {
            var renderEvent = false;
            if(schedule.ranges.start && (moment(intervalEnd) >= moment(schedule.ranges.start))) {
              renderEvent = true
            }
            if(renderEvent && schedule.ranges.end && (moment(intervalStart) <= moment(schedule.ranges.end))) {
              renderEvent = true
            } else {
              if(schedule.ranges.end) {
                renderEvent = false
              }
            }
            if(renderEvent) {
              if(schedule.ranges.start.indexOf('T')!=-1) {
                schedule.start = schedule.ranges.start;
                schedule.end = schedule.ranges.end;
                schedule.schedule_end = "";
              }
              return true
            } else{
              return false
            }
          } else {
            return false
          }
        });
        this.filteredSchedulesBeforeRender = filteredSchedulesBeforeRender;
        this.selectedScheduleType = 3;
        $('.cat__apps__calendar').fullCalendar('changeView', view);
        if(initialise) {
          if(localStorage.getItem('schedule-ActiveSchedule')) {
            localStorage.removeItem('schedule-ActiveSchedule');
          }
          // if($('.cat__apps__calendar')) {
          //   $('.cat__apps__calendar').fullCalendar('destroy');
          // }
          //this.initialiseCalendar(results, this);
        } else {
          this.calendarLoaderVisible.fullcalendarEventsActivated = true;
          $('.cat__apps__calendar').fullCalendar( 'removeEvents');
          $('.cat__apps__calendar').fullCalendar( 'addEventSource', filteredSchedulesBeforeRender);  
          $('.cat__apps__calendar').fullCalendar( 'rerenderEvents' );
        }
      }
    }, (error) =>{
      if(this.fetchUserScheduleFailureCount < 3) {
        this.fetchUserScheduleFailureCount++;
        this.fetchUserSchedules(userId, initialise, view);
      } else {
        this.fetchUserScheduleFailureCount = 0;
        this.calendarLoaderVisible.status = false;
        this.calendarLoaderVisible.fullcalendarEventsActivated = false;
        $(".event-action-confirmation").css("visibility", "hidden");
        this._structureService.notifyMessage({
          messge: "Fetching routing schedules failed.",
          delay: 1000,
          type: 'danger'
        });
      }
    }).catch( (exception)=>{
      if(this.fetchUserScheduleFailureCount < 3) {
        this.fetchUserScheduleFailureCount++;
        this.fetchUserSchedules(userId, initialise, view);
      } else {
        this.fetchUserScheduleFailureCount = 0;
        this.calendarLoaderVisible.status = false;
        this.calendarLoaderVisible.fullcalendarEventsActivated = false;
        $(".event-action-confirmation").css("visibility", "hidden");
        this._structureService.notifyMessage({
          messge: "Fetching routing schedules failed.",
          delay: 1000,
          type: 'danger'
        });
      }
    });
  }

  clearScheduleSelection() {
    this.scheduleTimeRanges = {
      startTime: '',
      endTime: ''
    }
    if($('.schedule-end-time').hasClass('recurrence-type-error')) {
      $('.schedule-end-time').removeClass('recurrence-type-error'); 
    }
    this.scheduleSelections.forEach( function(schedule, index) {
      $('.cat__apps__calendar').fullCalendar('removeEvents', schedule.id);
    })
    this.scheduleSelections = [];
  }

  clearSlots(confirm) {
    if(!confirm) {
      this.clearSlot['action'] = true;
      this.selectedEditSchedule['selectedScheduleType'] = 3;
      this.scheduleRecurrenceType = 1;
      this.selectedEditSchedule['title'] = $('.cat__apps__calendar').fullCalendar('getDate').format('ddd MM-DD-YYYY') ;
      this.selectedEditSchedule['event'] = {};
      this.selectedEditSchedule['actualStartTime'] = '';
      this.selectedEditSchedule['actualEndTime'] = '';
      this.selectedEditSchedule['startTime'] = '';
      this.selectedEditSchedule['endTime'] = '';    
      $('#edit-schedule-modal').modal('show');
    } else {
      let param = {
        userId: this.selectedStaff['id'],
        tenantId: this._structureService.getCookie('tenantId'),
        scheduleFormat: 30,
        formattedDate: $('.cat__apps__calendar').fullCalendar('getDate').format('YYYYMMDD'),
        dayStatus: 0,
        weekStatus: 0,
        typeOfSchedule: 1,
        mergeRecurring: 0,
        getDay: $('.cat__apps__calendar').fullCalendar('getDate').day(),
        schedulesToClear: this.selectedEditSchedule['selectedScheduleType'],
        action: 'clearSlot',
        recurringTypeStatus: [{"noRecurence":1, "recurence":1}]
      }
      let data = {"daySchedule": [],"recuringDates": [],"dayScheduleOnLoad": [],"recuringTypeStatus": param.recurringTypeStatus};
      this._scheduleService.editUserSchedule(param, data).then((status) => {
        if(status['status']) {
          $('#edit-schedule-modal').modal('toggle');
          this._structureService.getUserScehduleType().then(({ data }) => {
            data = JSON.parse(JSON.stringify(data));
            this.staffList = data['getSessionTenant'].staffUsers;
            let oncall, esc;
            var staffListTemp = this.staffList.filter( (staff, key) => {
              oncall = false;
              esc = false;
              if(staff.role.privileges && staff.role.privileges.indexOf('Auto_Message_Escalation') > -1) {
                staff.autoMessageEscalationPrivilege = true;
              } else {
                staff.autoMessageEscalationPrivilege = false;
              }
              staff.schedules.map( (schedules, key) => {
                if(schedules.type == 'On_Call') {
                  oncall = true;
                } else {
                  esc = true;
                }
              });
              if(oncall && !esc) {
                staff.schedulesAssigned = 'oncall';
              } else if(!oncall && esc) {
                staff.schedulesAssigned = 'esc';
              } else if(!oncall && !esc) {
                staff.schedulesAssigned = 'empty';
              } else {
                staff.schedulesAssigned = 'all';
              }
              return (staff.status == 'Active' && staff.grp != 20);
            });
            this.staffList = staffListTemp;
            this.fetchUserSchedules(this.selectedStaff['id'], false, 'agendaDay');
          });
        }
      });
    }

  }
  cancelScheduleSelection() {
    this.scheduleTimeRanges = {
      startTime: '',
      endTime: ''
    }
    if($('.schedule-end-time').hasClass('recurrence-type-error')) {
      $('.schedule-end-time').removeClass('recurrence-type-error'); 
    }
    this.scheduleRecurrenceType = '0';
    this.scheduleSelections.forEach( function(schedule, index) {
      $('.cat__apps__calendar').fullCalendar('removeEvents', schedule.id);  
    })
    this.scheduleSelections = [];
    $('.cat__apps__calendar').fullCalendar('option', 'selectable', false);
    this.addNewSchedule = false;
    this.selectedScheduleType = 3;
    $('.cat__apps__calendar').fullCalendar( 'removeEvents');
    $('.cat__apps__calendar').fullCalendar('changeView', 'month');
    $('.cat__apps__calendar').fullCalendar( 'addEventSource', this.userScheduleDetails);  
    $('.cat__apps__calendar').fullCalendar( 'rerenderEvents' );
    $('.fc-header-toolbar').removeClass('element-hide');
  }

  scheduleRecurrenceTypeChange(value) {
    if(value == 1) {
      $("#schedule-end-date-picker-modal").datetimepicker('destroy');
      $("#schedule-end-date-picker-modal").datetimepicker({
        format:'MMMM D, YYYY',
        icons: {
          previous: 'fa fa-chevron-left',
          next: 'fa fa-chevron-right'
        },
        ignoreReadonly: true
        //minDate: moment().format('MMMM D, YYYY')
      });
      if(moment($('.cat__apps__calendar').fullCalendar('getDate').format('YYYY-MM-DD') + ' 00:00').isSameOrAfter(moment(moment().format('YYYY-MM-DD') + ' 00:00'))) {
        $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").minDate($('.cat__apps__calendar').fullCalendar('getDate').format('MMMM D, YYYY'));
        $('#schedule-end-date-picker-modal').data("DateTimePicker").date($('.cat__apps__calendar').fullCalendar('getDate').format('MMMM D, YYYY'));
      } else {
        $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
        $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment().format('MMMM D, YYYY'));
      }
      $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").minDate($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('MMMM D, YYYY') );
    } else {
      if(this.selectedEditSchedule['startTime'] && this.selectedEditSchedule['startTime'] == '00:00') {
        if(!$('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate()) {
          $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").maxDate(moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date()).format('MMMM D, YYYY'));
        }
        $('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('MMMM D, YYYY'));
      } else {
        $('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate(moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date()).add(1, 'days').format('MMMM D, YYYY'));
      }
      if(moment($('.cat__apps__calendar').fullCalendar('getDate').format('YYYY-MM-DD') + ' 00:00').isSameOrAfter(moment(moment().format('YYYY-MM-DD') + ' 00:00'))) {
        if(moment($('.cat__apps__calendar').fullCalendar('getDate')).isAfter($( "#schedule-end-date-picker-modal" ).data("DateTimePicker").maxDate())) {
          $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").maxDate($('.cat__apps__calendar').fullCalendar('getDate').format('MMMM D, YYYY'));
        }
        $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").minDate($('.cat__apps__calendar').fullCalendar('getDate').format('MMMM D, YYYY'));
        $('#schedule-end-date-picker-modal').data("DateTimePicker").date($('.cat__apps__calendar').fullCalendar('getDate').format('MMMM D, YYYY'));
      } else {
        if(moment().isAfter($( "#schedule-end-date-picker-modal" ).data("DateTimePicker").maxDate())) {
          $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").maxDate(moment().format('MMMM D, YYYY'));
        }
        $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
        $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").maxDate(moment().add(1, 'days').format('MMMM D, YYYY'));
        $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment().format('MMMM D, YYYY'));
      }

      $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").minDate($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('MMMM D, YYYY') );
      if(!this.selectedEditSchedule['startTime'] || (this.selectedEditSchedule['startTime'] && this.selectedEditSchedule['startTime'] != '00:00')) {
        $( "#schedule-end-date-picker-modal" ).data("DateTimePicker").maxDate(moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date()).add(1, 'days').format('MMMM D, YYYY') );
      }
    }
    $("#schedule-from-date-picker-modal").datetimepicker('destroy');
    $("#schedule-from-date-picker-modal").datetimepicker({
      format:'MMMM D, YYYY',
      icons: {
        previous: 'fa fa-chevron-left',
        next: 'fa fa-chevron-right'
      },
      //showClear: true,
      ignoreReadonly: true
    });
    $('#schedule-from-date-picker-modal').data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
    if(this.selectedEditSchedule['event']['reference_id']) {
      this.scheduleBehaviour = {
        recurence: 1,
        noRecurence: 1
      };
    } else {
      this.scheduleBehaviour = {
        recurence: 2,
        noRecurence: 1
      };
    }
    if(value != '0' ) {
      if(this.selectedEditSchedule['validation']['timeRange']) {
        this.selectedEditSchedule['validationErrorStatus'] = true;
      } else {
        this.selectedEditSchedule['validationErrorStatus'] =false;
      }
      this.selectedEditSchedule['validation']['recurrenceType'] = false;
      $('.recurrence-type-container').removeClass('recurrence-type-error');
      if(!this.selectedEditSchedule['event']['reference_id']) {
        if(value == 1) {
          if(this.scheduleSelections.length) {
            this.scheduleSelections.map( (schedule) => {
              let scheduleTitles = [" Non Recurring [Day Schedule][New Schedule]", " Recurring on all " + $('.cat__apps__calendar').fullCalendar('getDate').format('dddd') + ' [New Schedule]', " Recurring on all days [New Schedule]", " Recurring on all week days [Mon-Fri][New schedule]", "", "", "Recurring on all weekend days ["+ this.days[this.userData.config.weekend_days[0]]+"-"+this.days[this.userData.config.weekend_days[this.userData.config.weekend_days.length-1]] +"][New schedule]" ];
              let colors = ["Red", "Green", "Blue", "#336797", "", "Yellow"];
              if(schedule.id) {
                schedule.title = scheduleTitles[value - 1];
                schedule.color = colors[value - 1];
                schedule.recurrence_type = value;
              }
            });
          }
        } else {
          if(this.scheduleSelections.length) {
            this.scheduleSelections.map( (schedule) => {
              let scheduleTitles = [" Non Recurring [Day Schedule][New Schedule]", " Recurring on all " + $('.cat__apps__calendar').fullCalendar('getDate').format('dddd') + ' [New Schedule]', " Recurring on all days [New Schedule]", " Recurring on all week days [Mon-Fri][New Schedule]", "", "", "Recurring on all weekend days ["+ this.days[this.userData.config.weekend_days[0]]+"-"+this.days[this.userData.config.weekend_days[this.userData.config.weekend_days.length-1]] +"][New schedule]" ];
              let colors = ["Red", "Green", "Blue", "#336797", "", "Yellow"];
              if(schedule.id) {
                schedule.title = scheduleTitles[value - 1];
                schedule.color = colors[value - 1];
                schedule.recurrence_type = value;
              }  
            });
          }
        }
      } else {
        let tempUserSchedules = this.userScheduleDetails;
        let scheduleTitles = [" Non Recurring [Day Schedule][New Schedule]", " Recurring on all " + $('.cat__apps__calendar').fullCalendar('getDate').format('dddd') + ' [New Schedule]', " Recurring on all days [New Schedule]", " Recurring on all week days [Mon-Fri][New Schedule]", "", "", "Recurring on all weekend days ["+ this.days[this.userData.config.weekend_days[0]]+"-"+this.days[this.userData.config.weekend_days[this.userData.config.weekend_days.length-1]] +"][New schedule]" ];
        let colors = ["Red", "Green", "Blue", "#336797", "", "Yellow"];
        tempUserSchedules.map( (schedule) => {
          if(schedule.reference_id == this.selectedEditSchedule['event']['reference_id'] && this.selectedEditSchedule['actualStartTime'] == schedule.start && this.selectedEditSchedule['actualEndTime'] == schedule.end) {
            schedule.title = scheduleTitles[value - 1];
            schedule.color = colors[value - 1];
            schedule.schedule_recurrence_type = value;
          }
        });
      }
    }
    if(value == '0' ) {
      this.selectedEditSchedule['validation']['recurrenceType'] = true;
      this.selectedEditSchedule['validationErrorStatus'] = true;
      if(!$('.recurrence-type-container').hasClass('recurrence-type-error')) {
        $('.recurrence-type-container').addClass('recurrence-type-error');  
      }
      if($('.recurrence-type-error').hasClass('element-hide')) {
        $('.recurrence-type-error').removeClass('element-hide');
      }
    }
    if(value > 1) {
      if(!$('#schedule-from-date-picker-modal').data("DateTimePicker").date()) {
        $('#schedule-from-date-picker-modal').data("DateTimePicker").date(moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('MMMM D, YYYY')));
        $('#schedule-to-date-picker-modal').data("DateTimePicker").clear();
      }
      // if(value == 7) {//handled paste weekend day as it is
      //   let nextWeekEndDay = null;
      //   var weekEndDays = JSON.parse(JSON.stringify(this.userData.config.weekend_days));
      //   var weekEndStartDay = weekEndDays[0]*1;
        
      //   if($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('MMMM') == moment().format('MMMM')) {
      //     if(moment().isoWeekday() <= weekEndStartDay) {
      //       nextWeekEndDay = moment().isoWeekday(weekEndStartDay);
      //     } else {
      //       nextWeekEndDay = moment().add(1, 'weeks').isoWeekday(weekEndStartDay);
      //     }
      //   } else {
      //     if(moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('MMMM ') + '1, '+$('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY')).isoWeekday() <= weekEndStartDay) {
      //       nextWeekEndDay = moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('MMMM ') + '1, '+$('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY')).isoWeekday(weekEndStartDay);
      //     } else {
      //       nextWeekEndDay = moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('MMMM ') + '1, '+$('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY')).add(1, 'weeks').isoWeekday(weekEndStartDay);
      //     }
      //   }
      //   $("#schedule-from-date-picker-modal").datetimepicker('destroy');
      //   var days = [0,1,2,3,4,5,6];
      //   days = days.filter( function(day) {
      //     return  day != moment(nextWeekEndDay.format('MMMM D, YYYY')).isoWeekday()
      //   });
      //   $("#schedule-from-date-picker-modal").datetimepicker({
      //     format:'MMMM D, YYYY',
      //     icons: {
      //       previous: 'fa fa-chevron-left',
      //       next: 'fa fa-chevron-right'
      //     },
      //     ignoreReadonly: true,
      //     daysOfWeekDisabled: days
      //   });
      //   $('#schedule-from-date-picker-modal').data("DateTimePicker").minDate(moment(nextWeekEndDay.format('MMMM D, YYYY')).format('MMMM D, YYYY'));
      //   $('#schedule-from-date-picker-modal').data("DateTimePicker").date(moment(nextWeekEndDay.format('MMMM D, YYYY')));
      // }
    }
    if(this._structureService.introJsObject) {
      var IntroElements = [];
      if(value == 1 || value == 0) {
        IntroElements = ['schedule-settings-sch-type-time$$right','sch-event-type','sch-edit-add-action-btn'];
      } else {
        IntroElements = ['schedule-settings-sch-type-time$$right','sch-event-type-recurring','sch-edit-add-action-btn'];        
      }
      setTimeout(()=> {
        this._structureService.resetIntroOptions({introElements: IntroElements,setPositionAfterStart:'schedule-add',emit:'startIntro',step:this._structureService.introJsObject._currentStep});
      });
    }
  }

  recurrenceBehaviourChange(value) {
    if(value != 2) {
      $('.cat__apps__calendar').fullCalendar('removeEvents');
      if(this.scheduleSelections.length) {
        this.scheduleSelections.map( (schedule) => {
          $('.cat__apps__calendar').fullCalendar('renderEvent', schedule, true);
        });
      }
    } else {
      $('.cat__apps__calendar').fullCalendar( 'removeEvents');
      $('.cat__apps__calendar').fullCalendar( 'addEventSource', this.userScheduleDetails);  
      $('.cat__apps__calendar').fullCalendar( 'rerenderEvents' );
      if(this.scheduleSelections.length) {
        this.scheduleSelections.map( (schedule) => {
          $('.cat__apps__calendar').fullCalendar('renderEvent', schedule, true);  
        });
      }
    }
  }
  scheduleEditTimeRangeChange(time, type) {
    if(type == 'start') {
      if($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') !=  $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD')) {
        if(this.selectedEditSchedule['startTime'] && this.selectedEditSchedule['startTime'] != '00:00') {
          if(this.scheduleRecurrenceType != 1) {
            $('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate(moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date()).add(1, 'days').format('MMMM D, YYYY'));  
            let scheduleDurationCheckStart = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']).add(24, 'hours');
            let scheduleDurationCheckEnd = moment(moment(scheduleDurationCheckStart.format()).add(1, 'days').format('YYYY-MM-DD') + ' 00:00');
            scheduleDurationCheckStart = scheduleDurationCheckStart.add(30, 'm');
            $('#schedule-edit-end-time option').css('display', 'block');
            while(scheduleDurationCheckStart.isSameOrBefore(scheduleDurationCheckEnd)) {
              $("#schedule-edit-end-time option:contains('" + scheduleDurationCheckStart.format('hh:mm A') + "')").css("display","none");//reverse is 'block'
              scheduleDurationCheckStart = scheduleDurationCheckStart.add(30, 'm');
            }
          }
        } else {
          if(this.scheduleRecurrenceType != 1) {
            $('#schedule-end-date-picker-modal').data("DateTimePicker").date($('#schedule-start-date-picker-modal').data("DateTimePicker").date());
            $('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('MMMM D, YYYY'));  
          }
        }
        if(this.selectedEditSchedule['endTime'] && $('#edit-schedule-modal').is(':visible')) {
          this.selectedEditSchedule['endTime'] = '';
        }
      } else {
        if(this.selectedEditSchedule['startTime'] && this.selectedEditSchedule['startTime'] == '00:00') {
          if(this.scheduleRecurrenceType != 1) {
            $('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('MMMM D, YYYY'));  
          }
        } else {
          if(this.scheduleRecurrenceType != 1) {
            $('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate(moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date()).add(1, 'days').format('MMMM D, YYYY'));  
          }
        }
        if(this.selectedEditSchedule['startTime']) {
          let scheduleDurationCheckStart = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 00:30');   
          let scheduleDurationCheckEnd = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']);
          $('#schedule-edit-end-time option').css('display', 'block');
          while(scheduleDurationCheckEnd.isSameOrAfter(scheduleDurationCheckStart)) {
            $("#schedule-edit-end-time option:contains('" + scheduleDurationCheckStart.format('hh:mm A') + "')").css("display","none");//reverse is 'block'
            scheduleDurationCheckStart = scheduleDurationCheckStart.add(30, 'm');
          }
          if(this.selectedEditSchedule['endTime'] && $('#edit-schedule-modal').is(':visible')) {
            this.selectedEditSchedule['endTime'] = '';
          }
        }
      }
    }
    this.selectedEditSchedule['validationErrorStatus'] = false;
    let start, end;
    if(this.selectedEditSchedule['startTime'] && this.selectedEditSchedule['endTime']) {
      start = moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']);
      end = moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['endTime']);

      let diff = moment(end,"YYYY-MM-DD HH:mm").diff(moment(start,"YYYY-MM-DD HH:mm"));
      let duration = moment.duration(diff);

      if(this.addNewScheduleByDate) {
        this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + end.format('LT');
        if(this.scheduleRecurrenceType != 1 || duration.asHours() <= 24) {
          this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + end.format('LT');
        } else {
          this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + end.format('LT');
        }
      } else {
        if(moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date()).isAfter($('#schedule-start-date-picker-modal').data("DateTimePicker").date())) {
          this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + end.format('LT');
          this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + $('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + end.format('LT');    
        } else {
          this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + end.format('LT');    
          this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY') + ' ' + start.format('LT') + ' - ' + end.format('LT');    
        }
      }

      if(!start.isBefore(end)) {
        this.selectedEditSchedule['validation']['timeRange'] = true;
      } else {
        this.selectedEditSchedule['validation']['timeRange'] = false;        
      }
      if(this.scheduleRecurrenceType != 0) {
        if(this.selectedEditSchedule['validation']['timeRange']) {
          this.selectedEditSchedule['validationErrorStatus'] = true;
        } else {
          this.selectedEditSchedule['validationErrorStatus'] = false;
        }
        this.selectedEditSchedule['validation']['recurrenceType'] = false;  
      } else {
        this.selectedEditSchedule['validationErrorStatus'] = true;
        this.selectedEditSchedule['validation']['recurrenceType'] = true;
      }
    } else {
      this.selectedEditSchedule['validationErrorStatus'] = true;
      this.selectedEditSchedule['validation']['timeRange'] = true;
      if(this.addNewScheduleByDate) {
        this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY');
        this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY');
      } else {
        this.selectedEditSchedule['title'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY');    
        this.selectedEditSchedule['actionModalTitle'] = $('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('ddd MM-DD-YYYY');
      }
    }
  }

  editSchedule(action, type) {
    if(action == 'delete') {
      this.selectedEditSchedule['timeZone'] = this.selectedTimeZoneData.offset.split(',')[0];
      let activeTimeZone = (new Date().getTimezoneOffset()) * -1;
      let scheduleSelectedTimezone = (((this.selectedEditSchedule['timeZone']) * -1) / 60) * -1;

      let timeToPassDay = [];
      let timeDifference;
      if(scheduleSelectedTimezone > this.timezone) {
          timeDifference = -1 * Math.abs(this.timezone - scheduleSelectedTimezone);
      } else {
          timeDifference = Math.abs(this.timezone - scheduleSelectedTimezone);
      }
      if(((timeDifference) + '').split('.').length == 2) {
          timeToPassDay = ((timeDifference) + '').split('.');
      } else {
          timeToPassDay = ((timeDifference) + '').split('.');
          timeToPassDay.push("0");
      }
      
      if (timeToPassDay[1] == '5') {
          if(timeDifference < 0) {
              timeToPassDay[1] = '-30';       
          } else {
              timeToPassDay[1] = '30';
          }                
      } else {
          timeToPassDay[1] = '0';
      }
      if(timeToPassDay[0] == '-0') {
          timeToPassDay[0] = '0';
      }
      
      timeToPassDay = timeToPassDay.map(function(time) {
          return parseInt(time, 10);
      });
      let param = {
        userId: ((this.selectedStaff['id'] && this.selectedStaff['id'] != '0') ? this.selectedStaff['id'] : this.selectedEditSchedule['event']['userid']),
        tenantId: this._structureService.getCookie('tenantId'),
        action: action,//determines delete or edit operation from API
        type: type,//0-edit non recurring schedule,1-edit occurence operation,2-edit series operation
        endDate: null,
        referenceId: this.selectedEditSchedule['event']['reference_id'],
        isNightShift: false,//based on converted schedule data to identify night shift schedule or not
        parentReferenceId: 0,//identify parent part or child part(0-parent, not zero-child)
        startDateIsSame: false,
        endDateIsSame: false,
        dateRangeStart: null,
        dateRangeEnd: null,
        occurrence_edited_schedule: false,//to identify occurrence edited schedule in series
        getDay: 0,
        formattedDate:'',
        formattedDateNightShift:'',
        dayNumberNightShift:0,
        recurrenceType: this.selectedEditSchedule['event']['schedule_recurrence_type'],//identify schedule type
        savedTimeZone: this.selectedEditSchedule['timeZone'],
        parentScheduleType: 'null',
        parentOfOccurrenceId: 'null',
        escalated_schedule: this.selectedEditSchedule['selectedScheduleType'] == 1 ? 1 : 0,
        continuous_schedule: this.selectedEditSchedule['event']['continuous_schedule'] ? this.selectedEditSchedule['event']['continuous_schedule'] : false
      };
      if(param.recurrenceType == '5') {
        param.parentScheduleType = this.selectedEditSchedule['event']['parent_schedule_type'];
        param.parentOfOccurrenceId = this.selectedEditSchedule['event']['parent_of_occurrence_id'];
      }
      if(type == 3) {
        param.dateRangeStart = moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date()).add(-1, 'days').format('YYYYMMDD');
        param.dateRangeEnd = moment($('#schedule-to-date-picker-modal').data("DateTimePicker").date()).add(1, 'days').format('YYYYMMDD');
        if(moment(this.selectedEditSchedule['event']['ranges']['start']).format('YYYY-MM-DD') == $('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD')) {
          param.startDateIsSame = true;
        }
        if(this.selectedEditSchedule['event']['schedule_end'] && moment(this.selectedEditSchedule['event']['schedule_end']).format('YYYY-MM-DD') == $('#schedule-to-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD')) {
          param.endDateIsSame = true;
        }
      }
      if(this.selectedEditSchedule['event']['parent_reference_id'] && this.selectedEditSchedule['event']['parent_reference_id'] != 0) {
        param.parentReferenceId = this.selectedEditSchedule['event']['parent_reference_id'];
        param.isNightShift = true;
      } else {
        if(this.selectedEditSchedule['event']['time_span']) {
          param.isNightShift = true;
        }
      }
      if(type == 2) {
        param.endDate = moment().add(-1, 'days').format('YYYYMMDD');
      }
      if(type == 1) {
        let endTime, startTime = '';
        if(this.selectedEditSchedule['event']['converted_timeSpan']) {
          endTime = this.selectedEditSchedule['event']['converted_timeSpan'].split('$')[1].split(' ')[1];
          startTime = this.selectedEditSchedule['event']['converted_timeSpan'].split('$')[0].split(' ')[1];
        } else {
          startTime = moment(this.selectedEditSchedule['event']['start']).format('HH:mm');
          endTime = moment(this.selectedEditSchedule['event']['end']).format('HH:mm');
        }
        if(this.selectedEditSchedule['event']['time_span'] && this.selectedEditSchedule['event']['parent_reference_id'] != 0) {
          param.dateRangeStart = moment(this.selectedEditSchedule['currentDate']).add(-1, 'days').format('YYYYMMDD');          
          param.formattedDate = moment(moment(moment(this.selectedEditSchedule['currentDate']).add(-1, 'days').format('YYYY-MM-DD')).format('YYYY-MM-DD') + ' ' + startTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
          param.getDay = new Date(moment(moment(moment(this.selectedEditSchedule['currentDate']).add(-1, 'days').format()).format('YYYY-MM-DD') + ' ' + startTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY/MM/DD')).getDay();
          param.formattedDateNightShift = moment(moment(this.selectedEditSchedule['currentDate']).format('YYYY-MM-DD') + ' ' + endTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
          param.dayNumberNightShift = new Date(moment(moment(this.selectedEditSchedule['currentDate']).format('YYYY-MM-DD') + ' ' + endTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY/MM/DD')).getDay();
        } else {
          if(this.selectedEditSchedule['event']['time_span'] && !this.selectedEditSchedule['event']['schedule_merge_conversion']) {
            param.isNightShift = true;
            param.formattedDateNightShift = moment(moment(moment(this.selectedEditSchedule['currentDate']).add(1, 'days').format('YYYY-MM-DD')).format('YYYY-MM-DD') + ' ' + endTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
            param.dayNumberNightShift = new Date(moment(moment(moment(this.selectedEditSchedule['currentDate']).add(1, 'days').format()).format('YYYY-MM-DD') + ' ' + endTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY/MM/DD')).getDay();            
          }
          if(this.selectedEditSchedule['event']['time_span'] && this.selectedEditSchedule['event']['schedule_merge_conversion']) {
            param.formattedDateNightShift = moment(moment(this.selectedEditSchedule['currentDate']).format('YYYY-MM-DD') + ' ' + endTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
            param.dayNumberNightShift = new Date(moment(moment(this.selectedEditSchedule['currentDate']).format('YYYY-MM-DD') + ' ' + endTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY/MM/DD')).getDay();
          }
          param.dateRangeStart = moment(this.selectedEditSchedule['currentDate']).format('YYYYMMDD');
          param.formattedDate = moment(moment(this.selectedEditSchedule['currentDate']).format('YYYY-MM-DD') + ' ' + startTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
          param.getDay = new Date(moment(moment(this.selectedEditSchedule['currentDate']).format('YYYY-MM-DD') + ' ' + startTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY/MM/DD')).getDay();
        }
      }
      if(this.selectedEditSchedule['event']['occurrence_parent_id']) {
        param.occurrence_edited_schedule = true;
      }
      let data = {};
      this.closeEventActionConfirmation();
      swal({
        title: "Are you sure?",
        text: type == '0' ? "All events associated with this schedule will be deleted." : "All events associated with this schedule will be deleted.",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true
      }, () => {        
        this._scheduleService.editUserSchedule(param, data).then((status) => {
          if(status['status']) {
            this._structureService.notifyMessage({
              messge:'Schedule deleted successfully',
              delay:1000,
              type:'success'
            });
            var activityData = {
              activityName: "Success Schedule Deletion",
              activityType: "Manage Schedules",
              activityDescription: "Schedule"+ (type ? ((type == 1) ? ' occurence' : ' series'): '')+" deletion of user " + this.selectedStaff.displayName+"["+this.selectedStaff.id+"] by user " + this.userData.displayName+"["+this.userData.userId+"]  has succeeded for schedule having schedule reference id as "+(param.parentReferenceId ? param.referenceId+" and "+param.parentReferenceId : param.referenceId )+", schedule event type as "+param.recurrenceType+", selected type of schedule for rendering as "+this.selectedScheduleType+" and selected timezone as "+JSON.stringify(this.selectedTimeZoneData)+".  Details: " + JSON.stringify(param),
              tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
            };
            this._structureService.trackActivity(activityData);
            var updatedBy = {
              displayName : this.userData.displayName,
              userid : this.userData.userId ?this.userData.userId : 0,
            };

            var updateConfigPollingtoServer = {
              configurationType: "updateSchedule",
              role: 0,
              tenantid: param.tenantId,
              userId: param.userId,
              updatedBy: updatedBy,
              scheduledata:{
                  userId: param.userId,
                  tenantId: param.tenantId,
                  formattedDate: param.formattedDate,
                  getDay: param.getDay,
                  escalated_schedule: this.selectedEditSchedule['selectedScheduleType'] == 1 ? 1 : 0,
                  userName: ((this.selectedStaff['id'] && this.selectedStaff['id'] != '0') ? this.selectedStaff['displayName'] : this.selectedEditSchedule['event']['username']),
                  displayName: ((this.selectedStaff['id'] && this.selectedStaff['id'] != '0') ? this.selectedStaff['displayName'] : this.selectedEditSchedule['event']['username']),
              },
              keepUpdateHistoryForAPPResume: true
            };
            var todayFormat = moment().format('YYYYMMDD');
            this._structureService.socket.emit("updateConfigPollingtoServer", updateConfigPollingtoServer);
            

            if(!(type==2 || type ==1|| type ==0)) {
              $('#edit-schedule-modal').modal('toggle');
            }
            this.getUserScehduleType('delete', false);
          } else {
            this._structureService.notifyMessage({
              messge:'Schedule deletion failed',
              delay:1000,
              type:'danger'
            });
            var activityData = {
              activityName: "Failure Schedule Deletion",
              activityType: "Manage Schedules",
              activityDescription: "Schedule"+ (type ? ((type == 1) ? ' occurence' : ' series'): '')+" deletion of user " + this.selectedStaff.displayName+"["+this.selectedStaff.id+"] by user " + this.userData.displayName+"["+this.userData.userId+"]  has failed for schedule having schedule reference id as "+(param.parentReferenceId ? param.referenceId+" and "+param.parentReferenceId : param.referenceId )+", schedule event type as "+param.recurrenceType+", selected type of schedule for rendering as "+this.selectedScheduleType+" and selected timezone as "+JSON.stringify(this.selectedTimeZoneData)+".  Details: " + JSON.stringify(param),
              tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
            };
            this._structureService.trackActivity(activityData);
            if(!(type==2 || type ==1|| type ==0)) {
              $('#edit-schedule-modal').modal('toggle');
            }
          }
        }, (err)=> {
          this._structureService.notifyMessage({
            messge:'Schedule deletion failed',
            delay:1000,
            type:'danger'
          });
          var activityData = {
            activityName: "Failure Schedule Deletion",
            activityType: "Manage Schedules",
            activityDescription:"Schedule"+ (type ? ((type == 1) ? ' occurence' : ' series'): '')+" deletion of user " + this.selectedStaff.displayName+"["+this.selectedStaff.id+"] by user " + this.userData.displayName+"["+this.userData.userId+"]  has failed for schedule having schedule reference id as "+(param.parentReferenceId ? param.referenceId+" and "+param.parentReferenceId : param.referenceId )+", schedule event type as "+param.recurrenceType+", selected type of schedule for rendering as "+this.selectedScheduleType+" and selected timezone as "+JSON.stringify(this.selectedTimeZoneData)+".  Details: " + JSON.stringify(param),
            tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
          };
          this._structureService.trackActivity(activityData);
	        if(!(type==2 || type ==1|| type ==0)) {
	          $('#edit-schedule-modal').modal('toggle');
	        }
        });
      });      
    } else {
      swal({
        title: "Are you sure?",
        text: type == 0 ? "Schedule will be changed."  :  type == 1 ? "" : "All events associated with this schedule will be changed.",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true
      }, () => {
        let timeslots =  [];
        if(this.selectedEditSchedule['allDay']) {
          timeslots = this.calculateTimeSlots(new moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 00:00'), new moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' 24:00'));  
        } else {
          timeslots = this.calculateTimeSlots(new moment($('#schedule-start-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']), new moment($('#schedule-end-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['endTime']));
        }
        this.selectedEditSchedule['timeZone'] = this.selectedTimeZoneData.offset.split(',')[0];
        let activeTimeZone = (new Date().getTimezoneOffset()) * -1;
        let scheduleSelectedTimezone = (((this.selectedEditSchedule['timeZone']) * -1) / 60) * -1;

        let timeToPassDay = [];
        let timeDifference;
        if(scheduleSelectedTimezone > this.timezone) {
            timeDifference = -1 * Math.abs(this.timezone - scheduleSelectedTimezone);
        } else {
            timeDifference = Math.abs(this.timezone - scheduleSelectedTimezone);
        }
        if(((timeDifference) + '').split('.').length == 2) {
            timeToPassDay = ((timeDifference) + '').split('.');
        } else {
            timeToPassDay = ((timeDifference) + '').split('.');
            timeToPassDay.push("0");
        }
        
        if (timeToPassDay[1] == '5') {
            if(timeDifference < 0) {
                timeToPassDay[1] = '-30';       
            } else {
                timeToPassDay[1] = '30';
            }                
        } else {
            timeToPassDay[1] = '0';
        }
        if(timeToPassDay[0] == '-0') {
            timeToPassDay[0] = '0';
        }
        
        timeToPassDay = timeToPassDay.map(function(time) {
            return parseInt(time, 10);
        });
        let nightShiftTimeSlot = [];
        let nightShiftTimeSlotStartIndex = 0;
        let convertedDate, currentDate = '';
        let convertedEditSeries, convertedNightShiftEditSeries = '';
        let dayChange = false;
        let formattedDate, formattedDateNightShift, formattedEndDate, startTime, endTime, formattedDateEditSeries, formattedDateNightShiftEditSeries = '';
        timeslots.map( (scheduledTime, key) => {
          if(scheduledTime) {
            convertedDate = moment(scheduledTime.format('YYYY-MM-DD') + ' ' + scheduledTime.format('HH:mm')).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY-MM-DD');            
            if(type == 2) {
              if(scheduledTime.format('HH:mm') == '00:00' && key != 0 && !dayChange) {
                dayChange = true;                
              }
              if(dayChange) {
                convertedNightShiftEditSeries = moment(moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD')).add(1,'days').format('YYYY-MM-DD') + ' ' + scheduledTime.format('HH:mm')).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
              } else {
                convertedNightShiftEditSeries = moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + scheduledTime.format('HH:mm')).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
              }              
            }
            if(key == 0) {
              currentDate = moment(scheduledTime.format('YYYY-MM-DD') + ' ' + scheduledTime.format('HH:mm')).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY-MM-DD');
              if(type == 2) {
                convertedEditSeries = moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + scheduledTime.format('HH:mm')).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
              }
            }
            timeslots[key] = moment( currentDate + ' ' + scheduledTime.format('HH:mm')).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('HH:mm');
            if(key != 0 && key!= timeslots.length-1) {
              if(convertedDate != currentDate && timeslots[key] == '00:00') {
                nightShiftTimeSlotStartIndex = key;
                formattedDate = moment(currentDate).format('YYYYMMDD');
                formattedDateNightShift = moment(convertedDate).format('YYYYMMDD');
                formattedDateEditSeries = convertedEditSeries;
                formattedDateNightShiftEditSeries = convertedNightShiftEditSeries;
              }
            } else {
              if(key == 0) {
                if(convertedDate != currentDate && timeslots[key] == '00:00') {
                  formattedDate = moment(convertedDate).format('YYYYMMDD');
                  formattedDateNightShift = '';
                  formattedDateEditSeries = convertedEditSeries;
                  formattedDateNightShiftEditSeries = '';
                }
              }
              if(key == timeslots.length-1) {
                if(convertedDate != currentDate && timeslots[key] == '00:00') {
                  formattedDate = moment(currentDate).format('YYYYMMDD');
                  formattedDateEditSeries = convertedEditSeries;
                  formattedDateNightShift = moment(convertedDate).format('YYYYMMDD');
                  nightShiftTimeSlotStartIndex = key;
                }
              }
            }
            if(key == 0) {
              startTime = timeslots[key];
            }
            if(key == timeslots.length-1) {
              endTime = timeslots[key];
              if($('#schedule-to-date-picker-modal').data("DateTimePicker").date() && this.scheduleRecurrenceType != 1) {
                formattedEndDate = moment($('#schedule-to-date-picker-modal').data("DateTimePicker").date().format('YYYY-MM-DD') + ' ' + this.selectedEditSchedule['startTime']).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD');
              } else {
                formattedEndDate = '';
              }
            }
          }
        });

        if(!formattedDate) {
          formattedDate = moment(currentDate).format('YYYYMMDD');
          formattedDateEditSeries = convertedEditSeries;
        }
        if(!formattedDateNightShift) {
          formattedDateNightShift = '';
          formattedDateNightShiftEditSeries = '';
        }
        if(nightShiftTimeSlotStartIndex) {
          nightShiftTimeSlot.push(timeslots.splice(0,nightShiftTimeSlotStartIndex));
          nightShiftTimeSlot.push(timeslots);
          timeslots = nightShiftTimeSlot;
        }
        if(this.selectedEditSchedule['event']['schedule_recurrence_type'] != 1 && type == 2) {
          if(this.selectedEditSchedule['event']['schedule_recurrence_type'] != 4) {
            formattedDate = formattedDateEditSeries;
            formattedDateNightShift = formattedDateNightShiftEditSeries;
          }
        }

        let param = {
          userId: this.selectedStaff['id'],
          tenantId: this._structureService.getCookie('tenantId'),
          scheduleFormat: 30,
          action: action,
          type: type,
          referenceId: this.selectedEditSchedule['event']['reference_id'],
          recurrenceType: this.selectedEditSchedule['event']['schedule_recurrence_type'],
          parentReferenceId: 0,
          isNightShift: formattedDateNightShift != '' ? true : false,
          timeSpan: '',
          formattedDate: formattedDate,
          formattedDateNightShift: formattedDateNightShift,
          dayNumberNightShift: formattedDateNightShift != '' ? moment(formattedDateNightShift).day(): '',
          getDay:  moment(formattedDate).day(),
          formattedEndDate: formattedEndDate != '' ? formattedEndDate : '',
          escalated_schedule: this.selectedEditSchedule['selectedScheduleType'] == 1 ? 1 : 0,
          scheduleEndDate: type == 2 ? moment().add(-1, 'days').format('YYYYMMDD') : '',
          occurrence_edited_schedule: false,
          timeSpanChange: 0,
          scheduleSplitConversion: false,
          scheduleMergeConversion: false,
          multipleDaySchedule: false,
          childReferenceId:'',
          savedTimeZone: this.selectedEditSchedule['timeZone'],
          parentScheduleType: 'null',
          parentOfOccurrenceId: 'null',
          longEvent: false,
          saveAsNewSchedule: false
        };
        if(param.recurrenceType == '5') {
          param.parentScheduleType = this.selectedEditSchedule['event']['parent_schedule_type'];
          param.parentOfOccurrenceId = this.selectedEditSchedule['event']['parent_of_occurrence_id'];
        }
        let data = {'schedule':[]};
        if(this.selectedEditSchedule['event']['time_span']) {
          param.multipleDaySchedule = true;
        }
        if(this.selectedEditSchedule['event']['schedule_split_conversion']) {
          param.scheduleSplitConversion = true;
        }
        if(this.selectedEditSchedule['event']['schedule_merge_conversion']) {
          param.scheduleMergeConversion = true;
        }
        if(this.selectedEditSchedule['event']['parent_reference_id']) {
          param.parentReferenceId = this.selectedEditSchedule['event']['parent_reference_id'];
        }
        if(this.selectedEditSchedule['event']['child_reference_id']) {
          param.childReferenceId = this.selectedEditSchedule['event']['child_reference_id'];
        }
        if(param.isNightShift == true) {
          param.timeSpan = moment(param.formattedDate).format('YYYY-MM-DD') + ' ' + startTime + '$' + moment(param.formattedDateNightShift).format('YYYY-MM-DD') + ' ' + endTime;
          if(!this.selectedEditSchedule['event']['time_span']) {
            param.timeSpanChange = 1;//day schedule to night schedule identifier while edit
          }
        } else {
          if(this.selectedEditSchedule['event']['time_span']) {
            param.timeSpanChange = 2;//chnaged from night to day schedule identifier while edit
          } 
        }
        if(action == 'edit' && type == 1) {
          if(this.selectedEditSchedule['event']['occurrence_parent_id']) {
            param.occurrence_edited_schedule = true;
          }
        }

        data['schedule'] = timeslots;
        var self = this;
        if(param.type == 2) {
          param.formattedDate = formattedDateEditSeries;          
          if(this.selectedEditSchedule['event']['schedule_recurrence_type'] != 4) {
            param.formattedDateNightShift = formattedDateNightShiftEditSeries;
          }          
        }
        if(param.recurrenceType == "1" && param.timeSpan) {
          let endDate = param.timeSpan.split('$')[1];
          let startDate = param.timeSpan.split('$')[0];
          let diff = moment(endDate,"YYYY-MM-DD HH:mm").diff(moment(startDate,"YYYY-MM-DD HH:mm"));
          let duration = moment.duration(diff);
          if(duration.asHours() >= 24) {
            param.longEvent = true;
            param.dayNumberNightShift = "";
            param.formattedEndDate = param.formattedDateNightShift;
            param.formattedDateNightShift = "";
            param.isNightShift = false;
            if(this.selectedEditSchedule['event']['time_span']) {
              param.timeSpanChange = 2;
            } else {
              param.timeSpanChange = 0;
            }
            //param.timeSpanChange = 0;
            timeslots = [];
            timeslots.push(param.timeSpan.split('$')[0].split(' ')[1]);
            timeslots.push(param.timeSpan.split('$')[1].split(' ')[1]);
            data['schedule'] = timeslots;
            param.timeSpan = ''
            if(moment(this.selectedEditSchedule['event']['ranges']['start'].split('T')[0] + ' 00:00:00', 'YYYY-MM-DD HH:mm:ss') < moment(moment().format('YYYY-MM-DD') + ' 00:00:00') &&  moment(param.formattedDate + ' 00:00:00', 'YYYYMMDD HH:mm:ss') > moment(this.selectedEditSchedule['event']['ranges']['start'].split('T')[0] + ' 00:00:00', 'YYYY-MM-DD HH:mm:ss')) {
              param.saveAsNewSchedule = true;  
            }
          }
        }
        var selectedEditSchedule = { ...this.selectedEditSchedule };
        delete selectedEditSchedule.event;
        selectedEditSchedule.event = {
          allDay: this.selectedEditSchedule.event.allDay,
          day_number: this.selectedEditSchedule.event.day_number,
          disableAllDaySelector: this.selectedEditSchedule.event.disableAllDaySelector,
          end: this.selectedEditSchedule.event.end.format(),
          parent_reference_id: this.selectedEditSchedule.event.parent_reference_id,
          ranges: this.selectedEditSchedule.event.ranges,
          reference_id: this.selectedEditSchedule.event.reference_id,
          saved_time_zone: this.selectedEditSchedule.event.saved_time_zone,
          schedule_end: this.selectedEditSchedule.event.schedule_end,
          schedule_recurrence_type: this.selectedEditSchedule.event.schedule_recurrence_type,
          schedule_type: this.selectedEditSchedule.event.schedule_type,
          start: this.selectedEditSchedule.event.start.format(),
          time_span: this.selectedEditSchedule.event.time_span,
          title: this.selectedEditSchedule.event.title,
          updated_date: this.selectedEditSchedule.event.updated_date
        }
        this._scheduleService.editUserSchedule(param, data).then((status) => {
          if(status['status']) {
            $('#edit-schedule-modal').modal('toggle');
            this._structureService.notifyMessage({
              messge:'Schedule updated successfully',
              delay:1000,
              type:'success'
            });
            var activityData = {
              activityName: "Success Schedule Updation",
              activityType: "Manage Schedules",
              activityDescription: "Schedule"+ (type ? ((type == 1) ? ' occurence' : ' series'): '')+" updation of user " + this.selectedStaff.displayName+"["+this.selectedStaff.id+"] by user " + this.userData.displayName+"["+this.userData.userId+"]  has succeeded for schedule having schedule reference id as "+((param.parentReferenceId && param.parentReferenceId != 0) ? param.referenceId+" and "+param.parentReferenceId : param.referenceId )+", schedule event type as "+param.recurrenceType+", selected type of schedule for rendering as "+this.selectedScheduleType+" and selected timezone as "+JSON.stringify(this.selectedTimeZoneData)+".  Details: params- " + JSON.stringify(param)+", data- " + JSON.stringify(data) + ", previous schedule data- " + JSON.stringify(selectedEditSchedule) ,
              tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
            };
            this._structureService.trackActivity(activityData);
            this.getUserScehduleType('edit', false);
            var updatedBy = {
              displayName : this.userData.displayName,
              userid : this.userData.userId ?this.userData.userId : 0,
            };

            var updateConfigPollingtoServer = {
              configurationType: "updateSchedule",
              role: 0,
              tenantid: param.tenantId,
              userId: param.userId,
              updatedBy: updatedBy,
              scheduledata:{
                  userId: param.userId,
                  tenantId: param.tenantId,
                  formattedDate: param.formattedDate,
                  getDay: param.getDay,
                  escalated_schedule: param.escalated_schedule,
                  userName: ((this.selectedStaff['id'] && this.selectedStaff['id'] != '0') ? this.selectedStaff['displayName'] : this.selectedEditSchedule['event']['username']),
                  displayName: ((this.selectedStaff['id'] && this.selectedStaff['id'] != '0') ? this.selectedStaff['displayName'] : this.selectedEditSchedule['event']['username'])
              },
              keepUpdateHistoryForAPPResume: true
            };
            
            var todayFormat = moment().format('YYYYMMDD');
            if(todayFormat == param.formattedDate || (parseInt(todayFormat) == parseInt(param.formattedDate)+1) || (parseInt(todayFormat) == parseInt(param.formattedDate)-1) ){
              this._structureService.socket.emit("updateConfigPollingtoServer", updateConfigPollingtoServer);
            }
          } else {
            this._structureService.notifyMessage({
              messge:'Schedule updation failed',
              delay:1000,
              type:'danger'
            });
            var activityData = {
              activityName: "Failure Schedule Updation",
              activityType: "Manage Schedules",
              activityDescription: "Schedule"+ (type ? ((type == 1) ? ' occurence' : ' series'): '')+" updation of user " + this.selectedStaff.displayName+"["+this.selectedStaff.id+"] by user " + this.userData.displayName+"["+this.userData.userId+"]  has failed for schedule having schedule reference id as "+(param.parentReferenceId ? param.referenceId+" and "+param.parentReferenceId : param.referenceId )+", schedule event type as "+param.recurrenceType+", selected type of schedule for rendering as "+this.selectedScheduleType+" and selected timezone as "+JSON.stringify(this.selectedTimeZoneData)+".  Details: " + JSON.stringify(selectedEditSchedule),
              tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
            };
            this._structureService.trackActivity(activityData);
          }
        }, (err)=> {
          this._structureService.notifyMessage({
            messge:'Schedule updation failed',
            delay:1000,
            type:'danger'
          });
          var activityData = {
            activityName: "Failure Schedule Updation",
            activityType: "Manage Schedules",
            activityDescription: "Schedule"+ (type ? ((type == 1) ? ' occurence' : ' series'): '')+" updation of user " + this.selectedStaff.displayName+"["+this.selectedStaff.id+"] by user " + this.userData.displayName+"["+this.userData.userId+"]  has failed for schedule having schedule reference id as "+(param.parentReferenceId ? param.referenceId+" and "+param.parentReferenceId : param.referenceId )+", schedule event type as "+param.recurrenceType+", selected type of schedule for rendering as "+this.selectedScheduleType+" and selected timezone as "+JSON.stringify(this.selectedTimeZoneData)+".  Details: " + JSON.stringify(selectedEditSchedule),
            tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
          };
          this._structureService.trackActivity(activityData);
        });
        
      })
    }  
  }

  clearToDateRangeSelection() {
    $('#schedule-to-date-picker-modal').data('DateTimePicker').clear();
  }
  
  closeEventActionConfirmation() {
    $(".event-action-confirmation").css("visibility", "hidden");
  }
  setEditRangeConstraints(action, type) {
    $(".event-action-confirmation").css("visibility", "hidden");
    this.selectedEditSchedule['edit_type'] = type;
    this.selectedEditSchedule['action'] = action;
    this.selectedEditSchedule['startTime'] = this.selectedEditSchedule['event']['start'].format('HH:mm');
    
    if(this.selectedEditSchedule['event']['end'].format('HH:mm') == '00:00') {
      this.selectedEditSchedule['endTime'] = '24:00';
    } else {
      this.selectedEditSchedule['endTime'] = this.selectedEditSchedule['event']['end'].format('HH:mm');
    }

    if(this.selectedEditSchedule['event']['time_span'] && this.selectedEditSchedule['event']['parent_reference_id'] != 0) {      
      $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment($('.cat__apps__calendar').fullCalendar('getDate')).add(-1,'days').format('MMMM D, YYYY'));      
      $('#schedule-end-date-picker-modal').data("DateTimePicker").date($('.cat__apps__calendar').fullCalendar('getDate').format('MMMM D, YYYY'));              
    } else if(this.selectedEditSchedule['event']['time_span'] && this.selectedEditSchedule['event']['parent_reference_id'] == 0 ) {
      $('#schedule-start-date-picker-modal').data("DateTimePicker").date($('.cat__apps__calendar').fullCalendar('getDate').format('MMMM D, YYYY'));
      $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment($('.cat__apps__calendar').fullCalendar('getDate')).add(1,'days').format('MMMM D, YYYY'));
    } else {
      $('#schedule-start-date-picker-modal').data("DateTimePicker").date($('.cat__apps__calendar').fullCalendar('getDate').format('MMMM D, YYYY'));
      $('#schedule-end-date-picker-modal').data("DateTimePicker").date($('.cat__apps__calendar').fullCalendar('getDate').format('MMMM D, YYYY'));
    }

    if(this.selectedEditSchedule['event']['reference_id'] && this.scheduleRecurrenceType > 1) {
      if(this.selectedEditSchedule['event']['parent_reference_id'] != 0) {
        $('#schedule-from-date-picker-modal').data("DateTimePicker").minDate(moment(this.selectedEditSchedule['event']['ranges']['start']).add(-1, 'days').format('MMMM D, YYYY'));
      } else {
        $('#schedule-from-date-picker-modal').data("DateTimePicker").minDate(moment(this.selectedEditSchedule['event']['ranges']['start']).format('MMMM D, YYYY'));
      }

      if($('.cat__apps__calendar').fullCalendar('getDate').format('YYYYMMDD') == moment(this.selectedEditSchedule['event']['schedule_end']).format('YYYYMMDD')) {            
        $('#schedule-to-date-picker-modal').data("DateTimePicker").minDate(moment($('.cat__apps__calendar').fullCalendar('getDate')).format('MMMM D, YYYY'));
      } else {
        $('#schedule-to-date-picker-modal').data("DateTimePicker").minDate(moment($('.cat__apps__calendar').fullCalendar('getDate')).add(1, 'days').format('MMMM D, YYYY'));
      }

      if(this.selectedEditSchedule['event']['schedule_end']) {
        if(this.selectedEditSchedule['event']['parent_reference_id'] != 0) {
          $('#schedule-from-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['event']['schedule_end']).add(-1, 'days').format('MMMM D, YYYY'));
          $('#schedule-to-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['event']['schedule_end']).format('MMMM D, YYYY'));
          $('#schedule-to-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['schedule_end']).format('MMMM D, YYYY'));
        } else {
          $('#schedule-from-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['event']['schedule_end']).format('MMMM D, YYYY'));
          $('#schedule-to-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['event']['schedule_end']).add(1, 'days').format('MMMM D, YYYY'));
          $('#schedule-to-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['schedule_end']).add(1, 'days').format('MMMM D, YYYY'));
        }
      } else {
        $('#schedule-to-date-picker-modal').data("DateTimePicker").clear();
      }

      if(this.selectedEditSchedule['event']['time_span'] && this.selectedEditSchedule['event']['parent_reference_id'] != 0) {
        $('#schedule-from-date-picker-modal').data("DateTimePicker").date(moment($('.cat__apps__calendar').fullCalendar('getDate')).add(-1,'days').format('MMMM D, YYYY'));
      } else {
        $('#schedule-from-date-picker-modal').data("DateTimePicker").date(moment($('.cat__apps__calendar').fullCalendar('getDate')).format('MMMM D, YYYY'));
      }
    }
    $('#edit-schedule-modal').modal('show');

  }
  timeZoneChange(value) {
    this.selectedTimeZoneData == this.timeZones.find((zone, i)=>{return zone.offset == value});
    this.selectedTimeZone = this.selectedTimeZoneData.offset;
    this.fetchUserSchedules(this.selectedStaff['id'], false, $('.cat__apps__calendar').fullCalendar( 'getView' ).type);
  }
  setModalConstraints(action, type) {
    console.log('-----------setModalConstraints----------');
    $(".event-action-confirmation").css("visibility", "hidden");

    let scheduleSelectedTimeZone = this.selectedTimeZoneData.offset.split(',')[0];
    let scheduleSelectedTimezone = (((scheduleSelectedTimeZone) * -1) / 60) * -1;
    const scheduleSelectedTimezoneGap = ((scheduleSelectedTimezone) + '').split('.');
    let timeZoneGap = [];
    let dateChanged = false;
    if(this.timezone == -4) {
      timeZoneGap = ['-4','0'];
    } else {
      timeZoneGap = ['5','30'];
    }
    const serverTimeZoneGap = timeZoneGap;
    if (scheduleSelectedTimezoneGap[1] == '5') {
      scheduleSelectedTimezoneGap[1] = '30';
    } else {
      scheduleSelectedTimezoneGap[1] = '0';
    }
    let timeToPassDay = [];
    if(parseInt(scheduleSelectedTimezoneGap[0]) > parseInt(serverTimeZoneGap[0])) {
      timeToPassDay[0] = Math.abs(parseInt(serverTimeZoneGap[0]) -parseInt(scheduleSelectedTimezoneGap[0]));
      timeToPassDay[1] = Math.abs(parseInt(serverTimeZoneGap[1]) - parseInt(scheduleSelectedTimezoneGap[1]));
      if(parseInt(scheduleSelectedTimezoneGap[0]) < 0) {
        if(timeToPassDay[1] == 30) {
          if(parseInt(scheduleSelectedTimezoneGap[0]) < 0) {
            if(timeToPassDay[0] > 0) {
              timeToPassDay[0] = timeToPassDay[0] - 1;
            }              
          }  
        }  
      }           
    } else {
        timeToPassDay[0] = -1 * Math.abs(parseInt(serverTimeZoneGap[0]) -parseInt(scheduleSelectedTimezoneGap[0]));
        timeToPassDay[1] = Math.abs(parseInt(serverTimeZoneGap[1]) - parseInt(scheduleSelectedTimezoneGap[1]));
        if(timeToPassDay[1] != 0) {
          timeToPassDay[1] = -1 * timeToPassDay[1];
        }
        if(timeToPassDay[0] == -0) {
          timeToPassDay[0] = 0;
        }
    }
    this.selectedEditSchedule['edit_type'] = type;
    this.selectedEditSchedule['action'] = action;
    this.selectedEditSchedule['startTime'] = this.selectedEditSchedule['event']['start'].format('HH:mm');
    if(this.selectedEditSchedule['event']['end'].format('HH:mm') == '00:00') {
      this.selectedEditSchedule['endTime'] = '24:00';
    } else {
      this.selectedEditSchedule['endTime'] = this.selectedEditSchedule['event']['end'].format('HH:mm');
    }
    if(this.selectedEditSchedule['event']['schedule_split_conversion'] && this.selectedEditSchedule['event']['converted_timeSpan']) {
      
      this.selectedEditSchedule['startTime'] = this.selectedEditSchedule['event']['converted_timeSpan'].split('$')[0].split(' ')[1];
      this.selectedEditSchedule['endTime'] = this.selectedEditSchedule['event']['converted_timeSpan'].split('$')[1].split(' ')[1];
      
      if(this.selectedEditSchedule['event']['schedule_split_conversion_child']) {
        if(type != 1) {
          this.selectedEditSchedule['currentDate'] = this.selectedEditSchedule['event']['converted_timeSpan'].split('$')[1].split(' ')[0];
        }
      }
      if(this.selectedEditSchedule['endTime'].format('HH:mm') == '00:00') {
        this.selectedEditSchedule['endTime'] = '24:00';        
      } else {
        this.selectedEditSchedule['endTime'] = this.selectedEditSchedule['endTime'].format('HH:mm');
      }  
    }    
    let actionModalTitle = this.selectedEditSchedule['actionModalTitle'];
    console.log('----------this.selectedEditSchedule-----------');
    console.log(this.selectedEditSchedule);
    if(this.selectedEditSchedule['event']['time_span'] && this.selectedEditSchedule['event']['parent_reference_id'] != 0 && type == 1) {
      if(this.selectedEditSchedule['event']['schedule_split_conversion']) {
        if(this.selectedEditSchedule['event']['schedule_split_conversion_child']) {
          $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment(this.selectedEditSchedule['currentDate']).add(-1,'days').format('MMMM D, YYYY'));
          console.log('111111111maxdate set'+moment(this.selectedEditSchedule['currentDate']).add(-1,'days').format('MMMM D, YYYY'));
          $('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['currentDate']).add(-1,'days').format('MMMM D, YYYY'));
          $('#schedule-end-date-picker-modal').data("DateTimePicker").minDate(moment(this.selectedEditSchedule['currentDate']).add(-1,'days').format('MMMM D, YYYY'));
          $('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
          $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['currentDate']).add(-1,'days').format('MMMM D, YYYY'));          
          $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
        }
        this.selectedEditSchedule['actionModalTitle'] = actionModalTitle;  
      }
    } else if(this.selectedEditSchedule['event']['time_span'] && this.selectedEditSchedule['event']['parent_reference_id'] == 0 && type == 1) {
      if(this.selectedEditSchedule['event']['schedule_merge_conversion']) {
        $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
        console.log('2222222maxdate set'+moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
        $('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
        $('#schedule-end-date-picker-modal').data("DateTimePicker").minDate(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
        $('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['currentDate']).add(1,'days').format('MMMM D, YYYY'));
        $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));        
        $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
      } else {
        $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
        console.log('33333333maxdate set'+moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
        $('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
        $('#schedule-end-date-picker-modal').data("DateTimePicker").minDate(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
        $('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['currentDate']).add(1,'days').format('MMMM D, YYYY'));
        $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));        
        $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['currentDate']).add(1,'days').format('MMMM D, YYYY'));
        this.selectedEditSchedule['actionModalTitle'] = actionModalTitle;
      }
    } else {
      if(type == 1 || type ==0) {
        if(this.selectedEditSchedule['event']['schedule_split_conversion']) {
          if(this.selectedEditSchedule['event']['schedule_split_conversion_child']) {
            $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
            $('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate(false);
            if(moment(this.selectedEditSchedule['event']['converted_timeSpan'].split('$')[0].split(' ')[0] + ' 00:00:00') < moment(moment().format('YYYY-MM-DD') + ' 00:00:00')) {
              $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment().format('MMMM D, YYYY'));
              dateChanged = true;
              this.selectedEditSchedule['startTime'] = "00:00";
            } else {
              $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['currentDate']).add(-1,'days').format('MMMM D, YYYY'));
            }
            $('#schedule-end-date-picker-modal').data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
            $('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate(false);            
            $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
          } else {
            $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
            $('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate(false);
            $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
            $('#schedule-end-date-picker-modal').data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
            $('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate(false);            
            $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['currentDate']).add(1,'days').format('MMMM D, YYYY'));
          }
          this.selectedEditSchedule['actionModalTitle'] = actionModalTitle;
        } else {
          if(this.selectedEditSchedule['event']['ranges']['start'].indexOf('T') == -1) {
            if(this.selectedEditSchedule['event']['schedule_recurrence_type'] == "1") {
              $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
              $('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate(false);
              $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
              $('#schedule-end-date-picker-modal').data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
              $('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate(false);          
              $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
            } else {
              $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
              console.log('666666maxdate set'+moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
              $('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
              $('#schedule-end-date-picker-modal').data("DateTimePicker").minDate(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
              $('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['currentDate']).add(1,'days').format('MMMM D, YYYY'));
              $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));          
              $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
            }
          } else {
            $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
            $('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate(false);
            if(moment(this.selectedEditSchedule['event']['ranges']['start'].split('T')[0] + ' 00:00:00') < moment(moment().format('YYYY-MM-DD') + ' 00:00:00')) {
              $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment().format('MMMM D, YYYY'));
              dateChanged = true;
              this.selectedEditSchedule['startTime'] = "00:00";
            } else {
              $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['ranges']['start']).format('MMMM D, YYYY'));
            }
            $('#schedule-end-date-picker-modal').data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
            $('#schedule-end-date-picker-modal').data("DateTimePicker").maxDate(false);
            if(moment(this.selectedEditSchedule['event']['ranges']['end'],'YYYY-MM-DDTHH:mm').format('HH:mm') == '00:00') {
              $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['ranges']['end']).add(-1, 'days').format('MMMM D, YYYY'));
            } else {
              $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['ranges']['end']).format('MMMM D, YYYY'));
            }
          }
        }
      }
    }
    if(this.selectedEditSchedule['event']['time_span'] && !this.selectedEditSchedule['event']['schedule_merge_conversion'] && !this.selectedEditSchedule['event']['schedule_split_conversion']) {      
      if(dateChanged) {
        this.selectedEditSchedule['startTime'] = "00:00";
      } else {
        this.selectedEditSchedule['startTime'] = this.selectedEditSchedule['event']['time_span'].split('$')[0].split(' ')[1];
      }
      this.selectedEditSchedule['endTime'] = this.selectedEditSchedule['event']['time_span'].split('$')[1].split(' ')[1];      
    }
    if(this.selectedEditSchedule['event']['converted_timeSpan'] && this.selectedEditSchedule['event']['time_span']) {      
      if(dateChanged) {
        this.selectedEditSchedule['startTime'] = "00:00";
      } else{
        this.selectedEditSchedule['startTime'] = this.selectedEditSchedule['event']['converted_timeSpan'].split('$')[0].split(' ')[1];
      }
      this.selectedEditSchedule['endTime'] = this.selectedEditSchedule['event']['converted_timeSpan'].split('$')[1].split(' ')[1];      
      if(this.selectedEditSchedule['endTime'].format('HH:mm') == '00:00') {
        this.selectedEditSchedule['endTime'] = '24:00';        
      } else {
        this.selectedEditSchedule['endTime'] = this.selectedEditSchedule['endTime'].format('HH:mm');        
      }
    }
    if(action == 'edit') {
      if(this.selectedEditSchedule['event']['reference_recurence_type']) {
        this.scheduleRecurrenceType = this.selectedEditSchedule['event']['reference_recurence_type'];
      }
    }
    if(this.scheduleRecurrenceType == 2) {
      if(this.selectedEditSchedule['event']['parent_reference_id'] != '0') {
        $('#modal-schedule-type-2-label').html('Recurring on all ' + moment(this.selectedEditSchedule['currentDate']).add(-1, 'days').format('dddd'));
      } else {
        $('#modal-schedule-type-2-label').html('Recurring on all ' + moment(this.selectedEditSchedule['currentDate']).format('dddd'));
      }
    }
    if(this.selectedEditSchedule['event']['reference_id'] && this.scheduleRecurrenceType > 1) {      
      $('#schedule-from-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['currentDate']).format('MMMM D, YYYY'));
      if(action == 'edit') {
        if(type == 2) {
          if(this.selectedEditSchedule['event']['schedule_recurrence_type'] == 5) {
            if(moment(this.selectedEditSchedule['event']['seriesStartDate'] + ' 00:01').isBefore(moment(moment().format('YYYY-MM-DD') + ' 00:01' ))) {
              if(this.selectedEditSchedule['event']['seriesStartScheduleData'] && this.selectedEditSchedule['event']['seriesEndScheduleData']) {                
                var scheduleOnParentDate = this.selectedEditSchedule['event']['seriesStartScheduleData'].split('-')[0];
                var scheduleParentTimes = this.selectedEditSchedule['event']['seriesStartScheduleData'].split('-')[1].replace(/["']/g, "").split(',');
                var scheduleParentStartTime = scheduleParentTimes[0].substring(1,scheduleParentTimes[0].length);
                var scheduleOnChildtDate = this.selectedEditSchedule['event']['seriesEndScheduleData'].split('-')[0];
                var scheduleChildTimes = this.selectedEditSchedule['event']['seriesEndScheduleData'].split('-')[1].replace(/["']/g, "").split(',');
                var scheduleChildEndTime = scheduleChildTimes[scheduleChildTimes.length-1].substring(0,scheduleChildTimes[scheduleChildTimes.length-1].length-1);
                if(moment(moment(scheduleOnParentDate).format('YYYY-MM-DD') + ' ' + scheduleParentStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD') != moment(moment(scheduleOnChildtDate).format('YYYY-MM-DD') + ' ' + scheduleChildEndTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD') ) {                  
                  $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment().add(1, 'days').format('MMMM D, YYYY'));  
                } else {                  
                  $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment().format('MMMM D, YYYY'));
                }                
                this.selectedEditSchedule['startTime'] = moment(moment(scheduleOnParentDate).format('YYYY-MM-DD') + ' ' + scheduleParentStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('HH:mm');
                this.selectedEditSchedule['endTime'] = moment(moment(scheduleOnChildtDate).format('YYYY-MM-DD') + ' ' + scheduleChildEndTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('HH:mm');                
                $('#schedule-from-date-picker-modal').data("DateTimePicker").date(moment(moment(scheduleOnParentDate).format('YYYY-MM-DD') + ' ' + scheduleParentStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('MMMM D, YYYY'));
                $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment(moment(scheduleOnParentDate).format('YYYY-MM-DD') + ' ' + scheduleParentStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('MMMM D, YYYY'));
                $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment(moment(scheduleOnParentDate).format('YYYY-MM-DD') + ' ' + scheduleParentStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('MMMM D, YYYY'));
              } else {                
                var scheduleOnDate = this.selectedEditSchedule['event']['seriesStartScheduleData'].split('-')[0];
                var scheduleTimes = this.selectedEditSchedule['event']['seriesStartScheduleData'].split('-')[1].replace(/["']/g, "").split(',');
                var scheduleStartTime = scheduleTimes[0].substring(1,scheduleTimes[0].length);
                var scheduleEndTime = scheduleTimes[scheduleTimes.length-1].substring(0,scheduleTimes[scheduleTimes.length-1].length-1);
                
                if(moment(moment(scheduleOnDate).format('YYYY-MM-DD') + ' ' + scheduleStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD') != moment(moment(scheduleOnDate).format('YYYY-MM-DD') + ' ' + scheduleEndTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD') ) {                  
                  $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment().add(1, 'days').format('MMMM D, YYYY'));  
                } else {                  
                  $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment().format('MMMM D, YYYY'));
                }                
                this.selectedEditSchedule['startTime'] = moment(moment(scheduleOnDate).format('YYYY-MM-DD') + ' ' + scheduleStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('HH:mm');
                this.selectedEditSchedule['endTime'] = moment(moment(scheduleOnDate).format('YYYY-MM-DD') + ' ' + scheduleEndTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('HH:mm');
                
                $('#schedule-from-date-picker-modal').data("DateTimePicker").date(moment(moment(scheduleOnDate).format('YYYY-MM-DD') + ' ' + scheduleStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('MMMM D, YYYY'));
                $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment(moment(scheduleOnDate).format('YYYY-MM-DD') + ' ' + scheduleStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('MMMM D, YYYY'));
                $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment(moment(scheduleOnDate).format('YYYY-MM-DD') + ' ' + scheduleStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('MMMM D, YYYY'));
              }
            } else {
              if(this.selectedEditSchedule['event']['seriesStartScheduleData'] && this.selectedEditSchedule['event']['seriesEndScheduleData']) {
                
                var scheduleOnParentDate = this.selectedEditSchedule['event']['seriesStartScheduleData'].split('-')[0];
                var scheduleParentTimes = this.selectedEditSchedule['event']['seriesStartScheduleData'].split('-')[1].replace(/["']/g, "").split(',');
                var scheduleParentStartTime = scheduleParentTimes[0].substring(1,scheduleParentTimes[0].length);
                var scheduleOnChildtDate = this.selectedEditSchedule['event']['seriesEndScheduleData'].split('-')[0];
                var scheduleChildTimes = this.selectedEditSchedule['event']['seriesEndScheduleData'].split('-')[1].replace(/["']/g, "").split(',');
                var scheduleChildEndTime = scheduleChildTimes[scheduleChildTimes.length-1].substring(0,scheduleChildTimes[scheduleChildTimes.length-1].length-1);
                
                if(moment(moment(scheduleOnParentDate).format('YYYY-MM-DD') + ' ' + scheduleParentStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD') != moment(moment(scheduleOnChildtDate).format('YYYY-MM-DD') + ' ' + scheduleChildEndTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD') ) {                  
                  $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(moment(moment(scheduleOnParentDate).format('YYYY-MM-DD') + ' ' + scheduleParentStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY-MM-DD') + ' 00:01').add(1, 'days').format('MMMM D, YYYY'));  
                } else {                  
                  $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(moment(moment(scheduleOnParentDate).format('YYYY-MM-DD') + ' ' + scheduleParentStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY-MM-DD') + ' 00:01').format('MMMM D, YYYY'));
                }                
                this.selectedEditSchedule['startTime'] = moment(moment(scheduleOnParentDate).format('YYYY-MM-DD') + ' ' + scheduleParentStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('HH:mm');
                this.selectedEditSchedule['endTime'] = moment(moment(scheduleOnChildtDate).format('YYYY-MM-DD') + ' ' + scheduleChildEndTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('HH:mm');
                
                $('#schedule-from-date-picker-modal').data("DateTimePicker").date(moment(moment(scheduleOnParentDate).format('YYYY-MM-DD') + ' ' + scheduleParentStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('MMMM D, YYYY'));
                $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment(moment(scheduleOnParentDate).format('YYYY-MM-DD') + ' ' + scheduleParentStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('MMMM D, YYYY'));
                $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment(moment(scheduleOnParentDate).format('YYYY-MM-DD') + ' ' + scheduleParentStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('MMMM D, YYYY'));
              } else {                
                var scheduleOnDate = this.selectedEditSchedule['event']['seriesStartScheduleData'].split('-')[0];
                var scheduleTimes = this.selectedEditSchedule['event']['seriesStartScheduleData'].split('-')[1].replace(/["']/g, "").split(',');
                var scheduleStartTime = scheduleTimes[0].substring(1,scheduleTimes[0].length);
                var scheduleEndTime = scheduleTimes[scheduleTimes.length-1].substring(0,scheduleTimes[scheduleTimes.length-1].length-1);
                if(scheduleEndTime == '23:30') {
                  scheduleEndTime = '24:00'   
                } else {
                  scheduleEndTime = moment(moment().format('YYYY-MM-DD') + ' ' + scheduleEndTime).add(30, 'm').format('HH:mm');
                }
                
                if(moment(moment(scheduleOnDate).format('YYYY-MM-DD') + ' ' + scheduleStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD') != moment(moment(scheduleOnDate).format('YYYY-MM-DD') + ' ' + scheduleEndTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD') ) {                  
                  $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(moment(moment(scheduleOnDate).format('YYYY-MM-DD') + ' ' + scheduleStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY-MM-DD') + ' 00:01').add(1, 'days').format('MMMM D, YYYY'));  
                } else {                  
                  $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(moment(moment(scheduleOnDate).format('YYYY-MM-DD') + ' ' + scheduleStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYY-MM-DD') + ' 00:01').format('MMMM D, YYYY'));
                }                
                this.selectedEditSchedule['startTime'] = moment(moment(scheduleOnDate).format('YYYY-MM-DD') + ' ' + scheduleStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('HH:mm');
                this.selectedEditSchedule['endTime'] = moment(moment(scheduleOnDate).format('YYYY-MM-DD') + ' ' + scheduleEndTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('HH:mm');
                
                $('#schedule-from-date-picker-modal').data("DateTimePicker").date(moment(moment(scheduleOnDate).format('YYYY-MM-DD') + ' ' + scheduleStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('MMMM D, YYYY'));
                $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment(moment(scheduleOnDate).format('YYYY-MM-DD') + ' ' + scheduleStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('MMMM D, YYYY'));
                $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment(moment(scheduleOnDate).format('YYYY-MM-DD') + ' ' + scheduleStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('MMMM D, YYYY'));
              }
            }
            if(this.selectedEditSchedule['event']['seriesEndDate']) {
              var scheduleParentTimes = this.selectedEditSchedule['event']['seriesStartScheduleData'].split('-')[1].replace(/["']/g, "").split(',');
              var scheduleParentStartTime = scheduleParentTimes[0].substring(1,scheduleParentTimes[0].length);                          
              if(this.selectedEditSchedule['event']['parent_schedule_type'] == 'multiple') {
                $('#schedule-to-date-picker-modal').data("DateTimePicker").date(moment(moment(moment(this.selectedEditSchedule['event']['seriesEndDate']).format('YYYY-MM-DD')).add(-1, 'days').format('YYYY-MM-DD') + ' ' + scheduleParentStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('MMMM D, YYYY'));  
              } else {
                $('#schedule-to-date-picker-modal').data("DateTimePicker").date(moment(moment(this.selectedEditSchedule['event']['seriesEndDate']).format('YYYY-MM-DD') + ' ' + scheduleParentStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('MMMM D, YYYY'));               
              }
            } else {
              $('#schedule-to-date-picker-modal').data("DateTimePicker").clear();
            }
          } else {
            if(this.selectedEditSchedule['event']['time_span']) {         
              if(this.selectedEditSchedule['event']['parent_reference_id'] != 0) {
                if(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').add(-1,'days').isBefore(moment(moment().format('YYYY-MM-DD') + ' 00:01' ))) {
                  $('#schedule-from-date-picker-modal').data("DateTimePicker").date(moment().format('MMMM D, YYYY'));
                  if(moment().isAfter($('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate())) {
                    console.log('777777maxdate set'+moment().format('MMMM D, YYYY'));
                    $('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate(moment().format('MMMM D, YYYY'));
                  }
                  $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
                  $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment().format('MMMM D, YYYY'));
                } else {
                  $('#schedule-from-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').add(-1,'days').format('MMMM D, YYYY'));
                  if(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').isAfter($('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate())) {
                    console.log('888888maxdate set'+moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').add(-1,'days').format('MMMM D, YYYY'));
                    $('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').add(-1,'days').format('MMMM D, YYYY'));
                  }
                  $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').add(-1,'days').format('MMMM D, YYYY'));
                  $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').add(-1,'days').format('MMMM D, YYYY'));
                }
              } else {
                if(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').add(-1,'days').isBefore(moment(moment().format('YYYY-MM-DD') + ' 00:01' ))) {
                  $('#schedule-from-date-picker-modal').data("DateTimePicker").date(moment().format('MMMM D, YYYY'));
                  if(moment().isAfter($('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate())) {
                    console.log('9999999maxdate set'+moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').format('MMMM D, YYYY'));
                    $('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').format('MMMM D, YYYY'));
                  }
                  $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
                  $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment().format('MMMM D, YYYY'));
                } else {
                  $('#schedule-from-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').format('MMMM D, YYYY'));
                  if(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').isAfter($('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate())) {
                    console.log('99999991111111maxdate set'+moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').format('MMMM D, YYYY'));
                    $('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').format('MMMM D, YYYY'));
                  }
                  $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').format('MMMM D, YYYY'));
                  $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').format('MMMM D, YYYY'));
                }                
              }
              if(this.selectedEditSchedule['event']['converted_timeSpan']) {
                $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['converted_timeSpan'].split('$')[1].split(' ')[0] + ' 00:01').format('MMMM D, YYYY'));
              } else {
                $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['time_span'].split('$')[1].split(' ')[0] + ' 00:01').format('MMMM D, YYYY'));
              }
              
            } else {
              if(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').isBefore(moment(moment().format('YYYY-MM-DD') + ' 00:01' ))) {
                $('#schedule-from-date-picker-modal').data("DateTimePicker").date(moment().format('MMMM D, YYYY'));  
                $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
                $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment().format('MMMM D, YYYY'));
                if(this.selectedEditSchedule['event']['converted_timeSpan']) {
                  $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment().add(1, 'days').format('MMMM D, YYYY'))
                } else {
                  $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment().format('MMMM D, YYYY'));
                }
              } else {
                if(this.selectedEditSchedule['event']['converted_timeSpan']) {
                  $('#schedule-from-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['converted_timeSpan'].split('$')[0].split(' ')[0] + ' 00:01').format('MMMM D, YYYY'));
                  $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment(this.selectedEditSchedule['event']['converted_timeSpan'].split('$')[0].split(' ')[0] + ' 00:01').format('MMMM D, YYYY'));
                  $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['converted_timeSpan'].split('$')[0].split(' ')[0] + ' 00:01').format('MMMM D, YYYY'));
                  $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['converted_timeSpan'].split('$')[1].split(' ')[0] + ' 00:01').format('MMMM D, YYYY'));
                } else {
                  $('#schedule-from-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').format('MMMM D, YYYY'));
                  if(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').isAfter($('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate())) {
                    console.log('9999999222222maxdate set'+moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').format('MMMM D, YYYY'));
                    $('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').format('MMMM D, YYYY'));
                  }
                  $('#schedule-start-date-picker-modal').data("DateTimePicker").minDate(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').format('MMMM D, YYYY'));
                  $('#schedule-start-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').format('MMMM D, YYYY'));
                  $('#schedule-end-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['ranges']['start'] + ' 00:01').format('MMMM D, YYYY'));
                }                
              }             
              
            }
            if(this.selectedEditSchedule['event']['schedule_end']) {
              $('#schedule-to-date-picker-modal').data("DateTimePicker").date(moment(this.selectedEditSchedule['event']['schedule_end']).format('MMMM D, YYYY'));
              console.log('999999933333maxdate set'+moment(this.selectedEditSchedule['event']['schedule_end']).format('MMMM D, YYYY'));
              $('#schedule-start-date-picker-modal').data("DateTimePicker").maxDate(moment(this.selectedEditSchedule['event']['schedule_end']).format('MMMM D, YYYY'));
            }
          }
          $('#schedule-from-date-picker-modal').data("DateTimePicker").minDate(moment().format('MMMM D, YYYY'));
          $('#schedule-to-date-picker-modal').data("DateTimePicker").minDate(moment($('#schedule-from-date-picker-modal').data("DateTimePicker").date()).add(1, 'days').format('MMMM D, YYYY'));
          if(this.selectedEditSchedule['event']['schedule_merge_conversion'] == true) {
            $('#schedule-end-date-picker-modal').data("DateTimePicker").date ($('#schedule-start-date-picker-modal').data("DateTimePicker").date());
          }
        }
      } 
    }
    $('#edit-schedule-modal').modal('show');
  }
  showOtherClinicians(){
    this.showClinicians = true;
  }
  callAccordion(roleID, eve){
    if ( $(".expand-icon-"+roleID).hasClass("fa-plus") ) {
        $( ".expand-icon-"+roleID ).removeClass( "fa-plus" );
        $( ".expand-icon-"+roleID ).addClass( "fa-minus" );
        $( ".sub-item-panel-"+roleID ).addClass( "showall" );
    } else {
        $( ".expand-icon-"+roleID ).removeClass( "fa-minus" );
        $( ".expand-icon-"+roleID ).addClass( "fa-plus" );
        $( ".sub-item-panel-"+roleID ).removeClass( "showall" );
    } 
  }
}
