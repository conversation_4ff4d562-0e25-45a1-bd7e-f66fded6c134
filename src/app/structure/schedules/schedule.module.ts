import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { ScheduleComponent } from './schedule.citushealth';
import { AuthGuard } from '../../guard/auth.guard';
import { FilterStaffByStatusWithSearchPipe,SearchFilterPipe, TimeZoneFormatPipe, ResetSelectedUsersPipe } from './filters/filter-staff-by-status-with-search.pipes';
import { SharedModule } from '../shared/sharedModule';
import { SearchFilterRoleTreeViewPipe, SearchRoleFilterPipe } from './filters/filter-staff-by-role-or-user-search.pipes';
import { EscalationScheduleComponent } from './escalation.citushealth';
import {NgbModule} from '@ng-bootstrap/ng-bootstrap';
export const routes: Routes = [
  { path: 'schedule/:staffId', component: ScheduleComponent },
  {
    path: 'schedule', component: ScheduleComponent, canActivate: [AuthGuard], data: {
      checkRoutingPrivileges: 'manageTenants,superAdmin',
      checkRoutingConfig: 'enable_message_center'
    } 
}
  
  
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    FormsModule,
    SharedModule,
    NgbModule.forRoot()
  ],
  declarations: [
    ScheduleComponent,
    FilterStaffByStatusWithSearchPipe,
    SearchFilterRoleTreeViewPipe,
    SearchRoleFilterPipe,
    SearchFilterPipe, 
    TimeZoneFormatPipe,
    ResetSelectedUsersPipe,
    EscalationScheduleComponent
  ]

})

export class ScheduleModule { }
