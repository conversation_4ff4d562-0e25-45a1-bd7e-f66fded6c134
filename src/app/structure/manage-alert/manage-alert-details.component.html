<section class="card">
    <div class="card-header">
        <div class="row">
            <div class="col-md-4">
                <span class="cat__core__title">
                    <strong>Banner Alerts</strong>
            </span>
            </div>
            <div class="col-md-7">
                <div class="filter-site row" style="margin-left: 20%;" [hidden]="!hideSiteSelection">
                    <div class="site-label">
                        <span>{{ 'LABELS.FILTER_BY_SITES' | translate }}</span>
                    </div>
                    <div class="col-md-8" style="width: 73%">
                        <app-select-sites [events]="eventsSubject.asObservable()" [filterType]=true (hideDropdown)="hideDropdown($event)" (siteIds)="getSiteIds($event)" > 
                        </app-select-sites>
                    </div>
                </div>
            </div>
            <div class="col-md-1">
                <a [routerLink]="['/new-banner-alerts']" class="pull-right btn btn-sm btn-primary">Add <PERSON> Alert
                    <i class="ml-1"></i></a>
            </div>
        </div>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/banner-alerts']">Banner Alerts</a></li>
        </ol>

        <div class="row">
            <div class="col-lg-12">
                <div class="mb-5 pdg-list">
                    <button class="btn-delete btn btn-sm btn-primary" id="archive-alert-material"
                        [disabled]="disableDeleteBtn">Delete</button>
                    <div class="wait-loading" *ngIf="dataLoadingMsg">
                        <img src="./assets/img/loader/loading.gif" />
                    </div>
                    <!-- <a class="btn btn-sm btn-primary" id="publish-all-alert-material" disabled="true">Publish</a>
                    <a class="btn btn-sm btn-primary" id="unpublish-all-alert-material" disabled="true">Unpublish</a> -->
                    <table class="table table-hover" id="bannerAlertTableId" width="100%"></table>
                </div>
            </div>
        </div>
    </div>
</section>