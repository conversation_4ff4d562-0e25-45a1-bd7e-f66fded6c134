
.alert-close{
    float: right;
    position: absolute;
    right: 10px;
    top: 10px;
}
.next-button{
    float: right;
}
.alert-section-default{
    padding: 10px 50px;
    background:red;
     position: fixed;
    height: 80px;
    top: 65px;
    z-index: 99;
    right: 20px;
    left: 270px;
}
.alert-section-warning{
    padding: 10px 50px;
    background: #f49b3b;
    position: fixed;
    height: 80px;
    top: 65px;
    z-index: 99;
    right: 20px;
    left: 270px;
}

.alert-section-information{
    padding: 10px 50px;
    background:#26d091;
    position: fixed;
    height: 80px;
    top: 65px;
    z-index: 99;
    right: 20px;
    left: 270px;
}
.banner-alert-full-width {
  left: 70px !important;  
}
.alert-section-error{
    padding: 10px 50px;
    background: #fe4748;
    position: fixed;
    height: 80px;
    top: 65px;
    z-index: 99;
    right: 20px;
    left: 270px;
}
.alert-icon-section img{
    width: 32px;
}
.showmore-btn{
    color: #fff !important;
    font-size: 13px !important;
    font-weight: bold;
}

button.previous-button ,button.next-button {
background: none;  
    color: #fff;
    border-radius: 3px;
    cursor: pointer;
    border: 0;
    padding: 0;
}
.alert-main h2{
    font-size: 16px;
    color: #fff;
}
.alert-main p{
    color: #fff;
    font-size: 13px;
    margin-bottom: 0;
}
.alert-close i{
    color: #fff;
}
.alert-close .close{
    background: none;
    border: none;
}
button.next-button i, button.previous-button i {
    font-size: 15px;
}
/* .alert-main img {
    width: 100%;
} */

button.previous-button {
    position: absolute;
    left: 15px;
    height: 100px;
    top: -5px;
}
button.next-button {
    position: absolute;
    right: 30px;
    height: 100px;
    top: -5px;
}

.more-load-text {
 color: white;
 font-weight: bold;
}

.accountModelContent{
    padding-top: 70px;
    padding-bottom: 10px;
    margin:30px;
    font-size: 15px;
}

.show-banner-alert-btn {
    position: fixed;
    z-index: 9;
    right: 20px;
    top: 64px;
    padding: 2px 10px !important;
    line-height: 15px;
}

.show-banner-on-scroll {
    position: fixed;
    z-index: 99;
    right: 19px;
    left: 269px;
}

.show-banner-on-close {
    /* position: fixed; */
    z-index: 99;
    right: 10px;
    left: 200px;
}

a.show-banner-alert-btn {
    color: #fff !important;
    border-radius: 3px;
    margin-right: 8px;
    font-size: 15px;
}
.alert-icon-section {
    /*display: inline-block;
    width: 6%;*/
    padding: 5px 10px;
}
.alert-right-section {
    width: 90%;
    display: inline-block;
}