<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>{{title}}</strong>
        </span>
    </div>

    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/banner-alerts']">Banner Alerts</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/new-banner-alerts']">Add Banner Alert</a></li>
        </ol>


        <form [formGroup]="alertForm" id="tagForm">
            <div class="form-body">

                <div class="form-group row">
                    <label class="col-md-3 control-label"> {{'LABELS.BANNER_ALERT_MESSAGE_TYPE' | translate}}*</label>
                    <div class="col-md-9">
                        <select class="form-control select2" formControlName="alerttype" id="alertTypeId">
                            <option *ngFor="let alerttype of alertTypes" value="{{alerttype.id}}">
                                {{alerttype.name}}
                            </option>
                        </select>
                        <div *ngIf="submitted && inValidalertType" class="alert alert-danger">
                             <div *ngIf="inValidalertType">Type of Message cannot be empty</div>
                        </div>
                    </div>
                </div>

              

                <div class="form-group row">
                    <label class="col-md-3 control-label">{{'LABELS.BANNER_ALERT_SUBJECT' | translate}}*</label>
                    <div class="col-md-9">
                        <input xssInputValidate="{{'LABELS.BANNER_ALERT_SUBJECT' | translate}}" class="form-control" placeholder="{{'LABELS.BANNER_ALERT_SUBJECT' | translate}}" type="text" formControlName="subject">
                            <div *ngIf="submitted && f.subject.errors" class="alert alert-danger">
                                <div *ngIf="f.subject.errors.required">Subject cannot be empty</div>
                            </div>
                        </div>
                </div>

                <div class="form-group row">
                    <label class="col-md-3 control-label">
                    {{'LABELS.BANNER_ALERT_BODY' | translate}}* </label>
                    <div class="col-md-9">
                        <textarea xssInputValidate="{{'LABELS.BANNER_ALERT_BODY' | translate}}" fieldType ="html" class="form-control"  placeholder="{{'LABELS.BANNER_ALERT_BODY' | translate}}" formControlName="description"></textarea>
                        <div *ngIf="submitted && f.description.errors" class="alert alert-danger">
                           <div *ngIf="f.description.errors.required">Body cannot be empty</div>
                        </div>
                    </div>
                  
                </div>

                <!-- <div class="form-group row">
                    <label class="col-md-3 control-label"> Tenants to be Shown*</label>
                    <div class="col-md-9">
                        <select  class="form-control select2" formControlName="branches" id="branchesSelectId" multiple>
                            <option *ngFor="let tenant of tenantDetails" value="{{tenant.id}}"> {{tenant.tenantName}}
                            </option>
                        </select>
                    <div *ngIf="inValidSelectedType" class="alert alert-danger">
                        <div *ngIf="inValidSelectedType">Tenants cannot be empty</div>
                    </div>
                    </div>
                </div>
               -->
                <div class="form-group row" id="select-site" [hidden]="!hideSiteSelection">
                    <label class="col-md-3 control-label">{{'LABELS.BANNER_ALERT_SITES' | translate}}*
                        <i chToolTip="site"></i>
                    </label>
                    <div class="col-md-9">
                        <app-select-sites [events]="eventsSubject.asObservable()" [hideApplyFilter]=true (hideDropdown)="hideDropdown($event)" (siteIds)="getSiteIds($event)" [siteSelection]="true" [bannerAlert]=true [crossSite]=true>
                        </app-select-sites>
                        <div *ngIf="submitted && invalidSite" class="alert alert-danger">
                            <div *ngIf="invalidSite">Sites cannot be empty</div>
                        </div>
                    </div>
                </div>

                <div class="form-group row">
                    <label class="col-md-3 control-label">
                       {{'LABELS.BANNER_ALERT_START_DATE' | translate}}* </label>
                        <div class="col-md-9">
                            <div class='input-group date' id="start-date-picker">
                                <input placeholder="Pick the Start Date" readonly="readonly"  id='start-date-picker-value' formControlName="startdate" type='text' class="form-control" />
                                <span class="input-group-addon">
                                    <span class="fa fa-calendar"></span>
                                </span>
                            </div>
                        <div *ngIf="submitted && inValidstartDate" class="alert alert-danger">
                            <div *ngIf="inValidstartDate">Start Date cannot be empty</div>
                        </div>
                        </div>
                   
                </div>
                <div class="form-group row">
                    <label class="col-md-3 control-label">
                        {{'LABELS.BANNER_ALERT_END_DATE' | translate}}* </label>
                        <div class="col-md-9">
                    <div class='input-group date'  id='end-date-picker'>
                        <input placeholder="Pick the End Date" readonly="readonly"  id='end-date-picker-value' formControlName="enddate" type='text' class="form-control" />
                        <span class="input-group-addon">
                            <span class="fa fa-calendar"></span>
                        </span>
                    </div>
                <div *ngIf="submitted && inValidendDate" class="alert alert-danger">
                    <div *ngIf="inValidendDate">End Date cannot be empty</div>
                </div>
                <div *ngIf="inValidStartandEndDate" class="alert alert-danger">
                    <div *ngIf="inValidStartandEndDate">End Date cannot be less than Start Date</div>
                </div>
                        </div>
                   
                </div>
                <div class="form-group row" >
                    <label class="col-md-3 control-label">{{'LABELS.BANNER_ALERT_PUBLISH' | translate}}
                        <!-- <i chToolTip="MSGES00001"></i> -->
                    </label>
                    <div class="col-md-9">
                        <div class="btn-group col-md-2">
                            <div class="ng-hide checkbox checkbox-primary">
                                <input type="hidden" id="publish_id" name="publishName"
                                    formControlName="publishControlName" />
                
                            </div>
                
                            <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                [ngClass]="{'active': publishEnable  }"
                                (click)="publishChange(true)">
                                Yes
                            </button>
                            <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                [ngClass]="{'active': !publishEnable}"
                                (click)="publishChange(false)">
                                No
                            </button>
                
                        </div>
                    </div>
                
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary" (click)="createAlertMessage()">Submit</button>
                    <button type="button" (click)="resetMessageAlertForm()" class="btn btn-default">Cancel</button>
                </div>
            </div>
        </form>


    </div>
</section>
