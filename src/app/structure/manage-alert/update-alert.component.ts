import { Component, OnInit, Input, ViewChild, ElementRef } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { RegistrationService } from '../registration/registration.service';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { InboxService } from '../inbox/inbox.service';
import { SharedService } from '../shared/sharedServices';
import { isNull } from "util";
import {
    FormsModule,
    FormBuilder,
    FormGroup,
    Validators,
    FormControl,
    FormArray,
    ReactiveFormsModule
} from '@angular/forms';
import { Http, Headers, RequestOptions } from '@angular/http';
import { ViewOrContainerState } from '@angular/core/src/render3/interfaces';
import { share } from 'rxjs/operator/share';
import { DatePipe } from "@angular/common";
import { Subject } from 'rxjs';
import { isBlank } from 'app/utils/utils';
declare var $: any;
declare var NProgress: any;
@Component({
    selector: 'app-manage-alert',
    templateUrl: './update-alert.component.html',
    styleUrls: ['./manage-alert.component.css'],
    providers: [DatePipe]
})
export class UpdateAlertComponent implements OnInit {
    alertForm;
    alertItems;
    notifier;
    title = 'Edit Banner Alerts';
    alertClass = "alert alert-warning alert-dismissible fade show";
    alertTypes = [];
    userTypes = [];
    userDetails: any;
    userData: any;;
    tenantDetails: any;;
    selectedBranches: any;
    selectedAlertType;
    selectedAlertMessageId;
    alertMessageData: any;
    previousassignedTenants;
    previousSiteIds;
    startDate;
    endDate;
    submitted = false;
    inValidalertType = false;
    inValidSelectedType = false;
    inValidstartDate = false;
    inValidendDate = false;
    inValidsubjectValue = false;
    inValidBodyValue = false;
    subjectValue = null;
    bodyValue = null;
    publishEnable = false
    params;
    inValidStartandEndDate = false;
    tenantsShowBannerId ="5000";
    hideSiteSelection:Boolean;
    siteId:any;
    filterType=false;
    editSiteData:any = [];
    invalidSite = false;
    siteChoosed : boolean = false;
    eventsSubject: Subject<void> = new Subject<void>();
    @ViewChild('alert') alert: ElementRef;

    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private _structureService: StructureService,
        private _ToolTipService: ToolTipService,
        private _inboxService: InboxService,
        private _formBuild: FormBuilder,
        public _SharedService: SharedService,
        private registrationservice: RegistrationService,
        private _http: Http,
        private datePipe: DatePipe,
    ) { this.ngOnInit();  }

    ngOnInit() {

        $("#start-date-picker").datetimepicker({
            format: 'MM/DD/YYYY',
            minDate: new Date().setHours(0, 0, 0, 0),
            icons: {
                previous: 'fa fa-chevron-left',
                next: 'fa fa-chevron-right'
            },
            ignoreReadonly: true
        });

        $('#start-date-picker-value').datetimepicker({
            minDate: new Date().setHours(0, 0, 0, 0)
        })

        $("#end-date-picker").datetimepicker({
            format: 'MM/DD/YYYY',
            minDate: new Date().setHours(0, 0, 0, 0),
            icons: {
                previous: 'fa fa-chevron-left',
                next: 'fa fa-chevron-right'
            },
            ignoreReadonly: true
        });


        $('#end-date-picker-value').datetimepicker({
            minDate: new Date().setHours(0, 0, 0, 0),
        });

        $("#start-date-picker").on("dp.change", (e) => {
            this.startDate = $('#start-date-picker-value').val();
            this.inValidstartDate = (this.startDate === null || this.startDate === '') ? true: false;
        });

        $('#alertTypeId').on('change', (e) => {
            this.selectedAlertType = $('#alertTypeId').val();
            this.inValidalertType = (this.selectedAlertType === null || this.selectedAlertType === '') ? true: false;
        });

        $('#subjectNameId').on('change', (e) => {
            console.log(e);
            this.subjectValue = $('#subjectNameId').val();
            this.inValidsubjectValue = (this.subjectValue === null || this.subjectValue === '') ? true: false;
        });

        $('#bodyNameId').on('change', (e) => {
            this.bodyValue = $('#bodyNameId').val();
            this.inValidBodyValue = (this.bodyValue === null || this.bodyValue === '') ? true: false;
        });

        $('#branchesSelectId').on('change', (e) => {
            this.selectedBranches = $('#branchesSelectId').val();
            this.inValidSelectedType = this.selectedBranches.length === 0 ? true: false;
        });

        $("#end-date-picker").on("dp.change", (e) => {
            this.endDate = $('#end-date-picker-value').val();
            this.inValidendDate = (this.endDate === null || this.endDate === '') ? true: false;
        });

        this.selectedAlertMessageId = this._structureService.getCookie(
            "alertMessageId"
        );

        this.getAlertMessageData();

        
        this.userDetails = this._structureService.userDetails;
        this.userData = JSON.parse(this.userDetails);
        // this.tenantDetails = this.userData.crossTenantsDetails;
        if (this.userData.crossTenantsDetails.length > 0) {
            this.tenantDetails = this.userData.crossTenantsDetails;

        }
        else {
            this.tenantDetails = [{
                id: this.userData.tenantId,
                tenantName: this.userData.tenantName
            }]

        }

        this.alertTypes = [
            { id: 1, name: "Informative" },
            { id: 2, name: "Warning" },
            { id: 3, name: "Error" }
        ];

        this.userTypes = [
            { id: 1, name: "Staff" },
            { id: 2, name: "Patient" },
            { id: 3, name: "Partner" }
        ];
        this.initForm();

    }



    initForm() {

        $('#branchesSelectId').val('');
        $('#alertTypeId').val('');
        $("#branchesSelectId").select2({});
        $("#alertTypeId").select2({});

        this.alertForm = this._formBuild.group({
            alerttype: [''],
            branches: [''],
            subject: [''],
            description: [''],
            startdate: [''],
            enddate: [''],
            publishControlName: [false]
        });
    }

    updateManageAlerts(): void {
        this.submitted = true;
        NProgress.start();
        this.startDate = $('#start-date-picker-value').val();
        this.endDate = $('#end-date-picker-value').val();
        let selectedBranches = [];
        this.selectedBranches = $('#branchesSelectId').val();
        
         
/*         if (this.selectedBranches) {
            this.selectedBranches.forEach(element => {
                console.log(element);
                var member = { id: "" };
                var id = element.substr(element.indexOf(":") + 1);
                id = id.replace(/'/g, "");
                member.id = id.replace(/\s/g, '');
                this.tenantsShowBannerId = member.id + "," + this.tenantsShowBannerId;
                console.log(this.tenantsShowBannerId);
                selectedBranches.push(Number(member.id));
            });
            console.log(this.tenantsShowBannerId.slice(0, this.tenantsShowBannerId.length - 5));
            this.tenantsShowBannerId = this.tenantsShowBannerId.slice(0, this.tenantsShowBannerId.length - 5);
        } */
        this.selectedAlertType = $('#alertTypeId').val();
        this.inValidalertType = (this.selectedAlertType === null || this.selectedAlertType === '') ? true : false;
        // this.inValidSelectedType = this.selectedBranches.length === 0 ? true : false;
        this.invalidSite = (isBlank(this.siteId) || this.siteId=='0');
        this.inValidstartDate = (this.startDate === null || this.startDate === '') ? true : false;
        this.inValidendDate = (this.endDate === null || this.endDate === '') ? true : false;
        this.startDate = Date.parse( this.startDate.replace(/-/g, ' ') ) ;
        //this.startDate = Date.parse(this.startDate);
        this.startDate = this.startDate / 1000;
        console.log(this.inValidStartandEndDate);
        const interval = 1000 * 60 * 60 * 24; // 24 hours in milliseconds

        this.endDate = (Date.parse(this.endDate.replace(/-/g, ' ')) + interval - 1);
        this.endDate = Math.floor(this.endDate / 1000);
        
        this.inValidStartandEndDate = (this.startDate > this.endDate) ? true : false;
        
        let params = {
            alertMessageId: parseInt(this.selectedAlertMessageId),
            alertType: Number(this.selectedAlertType),
            branches: selectedBranches,
            subject: this.alertForm.value.subject,
            description: escape(this.alertForm.value.description),
            startDate: this.startDate.toString(),
            endDate: this.endDate.toString(),
            createdAt: Math.floor((new Date()).getTime() / 1000).toString(),
            tenantId: Number(this._structureService.getCookie('crossTenantId')),
            sessionToken: this._structureService.getCookie('authenticationToken'),
            siteIds: this.siteId
        };

        if (this.inValidBodyValue === true || this.inValidsubjectValue === true  || this.inValidalertType === true || this.inValidendDate === true || this.inValidstartDate === true ||  this.inValidStartandEndDate === true || this.invalidSite === true) {
            NProgress.done();
            return;
        }

        this._structureService.updateAlerts(params).then(response => {

            const updatedAlertMessageId = response['updateAlerts'];
            if( !isNull(updatedAlertMessageId)){
            var activityLogMessage =
                this.userData.displayName +
                " updated Alert Messsage with id " +
                updatedAlertMessageId + " in tenants" + selectedBranches;

            var activityData = {
                activityName: "Manage Alert Message Update",
                activityType: "Manage Alert Message",
                activityDescription: activityLogMessage
            };

            this._structureService.trackActivity(activityData);

            this._structureService.notifyMessage({
                messge: 'Alert Message Updated successfully.',
                delay: 1000,
                type: 'success'
            });

            NProgress.done();
            setTimeout(() => {
                this.resetMessageAlertForm();
            }, 1000);
            if (this.publishEnable == true) {
                let Publish = {
                    id: updatedAlertMessageId,
                    isPublish: true,
                    selectedBranches:selectedBranches
                };
                console.log("publishhhhhhhhhhhhhhhhh param", Publish);
                if(updatedAlertMessageId)
                this.publishAlertMessage(Publish);
            }
            else if (this.publishEnable == false) {
                let Publish = {
                    id: updatedAlertMessageId,
                    isPublish: false,
                    selectedBranches:selectedBranches
                };
                console.log("unpublishhhhhhhhhhhhhhhhh param", Publish);
                if(updatedAlertMessageId)
                this.publishAlertMessage(Publish);
            }
        } else {
            let activityLogMessage = this.userData.displayName + " An error occurred while updating an alert message due to invalid input in tenants" + selectedBranches;              

            let activityData = {
                activityName: "Manage Alert Message Update",
                activityType: "Manage Alert Message",
                activityDescription: activityLogMessage
            };

            this._structureService.trackActivity(activityData);

            this._structureService.notifyMessage({
                messge: this._ToolTipService.getTranslateData('MESSAGES.ERROR_MSG_INVALID_INPUT'),
                delay: 1000,
                type: 'warning'
            });
        }
        });
    }

    getAlertMessageData(): void {
        this._structureService
            .getAlertMessageData(this.selectedAlertMessageId)
            .then(resultData => {
                let startDate
                this.alertMessageData = resultData["getSessionTenant"].alertMessageSingleData;
                if (this.alertMessageData.startDate && this.alertMessageData.startDate != null && this.alertMessageData.startDate != 'null') {
                    var datePipe = new DatePipe("en-US");
                    startDate = datePipe.transform(this.alertMessageData.startDate * 1000, "MM/dd/yyyy");
                }
                console.log(this.alertMessageData);
                var datePipe = new DatePipe("en-US");
                let endDate = datePipe.transform(this.alertMessageData.endDate * 1000, "MM/dd/yyyy");
                this.publishEnable = (this.alertMessageData.isPublished == 0) ? false : true;
                this.previousSiteIds = this.alertMessageData.siteIds;
this.previousassignedTenants =this.alertMessageData.assignedTenants.join();
                this.alertForm.patchValue({
                    alerttype: this.alertMessageData.alertType,
                    subject: String(this.alertMessageData.subject),
                    description: String(this.alertMessageData.description),
                    branches: this.alertMessageData.assignedTenants.map(String),
                    startdate: startDate,
                    enddate: endDate
                });
                this.editSiteData = (this.alertMessageData.siteIds) ? this.alertMessageData.siteIds.split(',').map(Number) : "";
                if(this.editSiteData.length > 0){
                    this.filterType = true;
                }
                setTimeout(() => {
                    $("#branchesSelectId").select2({});
                    $("#alertTypeId").select2({});
                });

            });
    }

    resetMessageAlertForm() {
        this.initForm();
        this.router.navigate(['/banner-alerts']);
    }

    get f() { return this.alertForm.controls; }


    publishChange(status) {
        this.publishEnable = status;

    }

    publishAlertMessage(publish) {
        let messageData = 'published';
        if (publish.isPublish == true) {
             messageData = "published";   
        }
        else {
            messageData = "Unpublished";   
        }




        // this.dataLoadingMsg = true;
        this._structureService.publishAlertMessage(publish).subscribe(data => {
            //    this.dataLoadingMsg = true;
            const deletedObj = Object.keys(data).map(key => data[key]);
            let deletedMessageId = deletedObj[0].publishAlertMessage[0].id;
            console.log("deleeeeeeeeeee", deletedObj, deletedMessageId);


            var activityLogMessage = `${this.userData.displayName}  ${messageData} Alert Message with id  ${deletedMessageId}`;
            console.log("activvvvvvvvvvvvvvvvvvvvvvvvv", activityLogMessage);
            var activityData = {
                activityName: `${messageData} Alert Message`,
                activityType: "manage alert message",
                activityDescription: activityLogMessage
            };
            this._structureService.trackActivity(activityData);
            this.params = {
                tenantId:this.tenantsShowBannerId
            };
            let branchesToShow = (this.userData.config.enable_multisite == 1)? this.previousassignedTenants:this.previousSiteIds;           
            let previousParams = {        
                userType:this.userData.group,
                branchesToShow:branchesToShow,
                bannerId:deletedMessageId,
                tenantId: publish.selectedBranches.join(),
                siteIds: this.previousSiteIds
              };
              let detaildParams = {        
                userType:this.userData.group,
                branchesToShow:publish.selectedBranches.join(),
                bannerId:deletedMessageId,
                tenantId: publish.selectedBranches.join(),
                siteIds: this.siteId
              };
              
              console.log(detaildParams);
              //this.bannerAlertDetails(this.params);
              if(publish.isPublish){  

                this.removeBannerAlertDetails(previousParams);    
                this.addBannerAlertDetails(detaildParams);
              } else{
                this.removeBannerAlertDetails(detaildParams);
              }
           // this.bannerAlertDetails(this.params);
            // $("#pdg-select-all").prop("checked", false);
        });

        var notify = publish.isPublish
            ? $.notify(`Success! Alert Message published`)
            : $.notify(`Success! Alert Message unpublished`);
        // document
        //     .getElementById("archive-alert-material")
        //     .setAttribute("disabled", "true");
        setTimeout(function () {
            notify.update({
                type: "success",
                message: publish.isPublish
                    ? `<strong>Success! Alert Message published </strong>`
                    : `<strong>Success! Alert Message unpublished</strong>`
            });
        }, 1000);

    }

    bannerAlertDetails(params) {
        this._structureService.getAllAlertMessageBytenantId(params).then(response => {


            this.alertItems = response;
      //     if (this.alertItems.length) {
                let param = {
                    status: "new",
                    id: this.tenantsShowBannerId,
                    siteIds: params.siteIds
                };


                this._structureService.socket.emit(
                    "newBannerAlert",
                    param,
                    this.alertItems
                );

       //     }
            console.log("ALLLLLLLLLLLLLLLLLLLLALERTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTT");
            console.log(this.alertItems);
            console.log("ALLLLLLLLLLLLLLLLLLLLALERTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTT");

            //    this.nextAlertShow();
        });
    }
    removeBannerAlertDetails(params) {
        console.log(params);
        let param = {
          status: "remove",
          id: params.branchesToShow,          
          bannerId:params.bannerId,
          siteIds: params.siteIds
      };
    
    
      this._structureService.socket.emit(
          "newBannerAlert",
          param,
          {}
      );
    
        
      }
      addBannerAlertDetails(params) {
        console.log(this.selectedAlertMessageId);
         console.log(params);
        this._structureService.getAllAlertMessageBytenantId(params,params.bannerId).then(response => {
          console.log("iddddddddddddddddddddddddddddddddddddddd",params);
          this.alertItems = response;    
                            let param = {
                                status: "new",
                                id:  params.branchesToShow,                                
                                bannerId:params.bannerId,
                                siteIds: params.siteIds
                            };
            
            
                            this._structureService.socket.emit(
                                "newBannerAlert",
                                param,
                                this.alertItems
                            );
                          });
      }
      getSiteIds(siteId: any) {
        this.siteId = siteId['siteId'].toString();
        this.invalidSite = (((isBlank(this.siteId) || this.siteId=='0') && this.submitted) ||  ((isBlank(this.siteId) || this.siteId=='0') && this.siteChoosed));
        this.siteChoosed = true;
        console.log('site ids', this.siteId);
     }
     hideDropdown(hideItem : any){
      console.log('hide emit');
      this.hideSiteSelection = hideItem.hideItem;
    }
}
