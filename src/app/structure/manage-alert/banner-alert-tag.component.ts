import { Component, OnInit, OnDestroy, Input, ViewChild, ElementRef } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { RegistrationService } from '../registration/registration.service';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { InboxService } from '../inbox/inbox.service';
import { SharedService } from '../shared/sharedServices';
import {
    FormsModule,
    FormBuilder,
    FormGroup,
    Validators,
    FormControl,
    FormArray,
    ReactiveFormsModule
} from '@angular/forms';
import { Http, Headers, RequestOptions } from '@angular/http';
import { ViewOrContainerState } from '@angular/core/src/render3/interfaces';
import { share } from 'rxjs/operator/share';
import { HostListener } from '@angular/core';
import { isBlank } from 'app/utils/utils';
import { Banner } from './banner.interface';
import { Subscription } from 'rxjs';
declare var $: any;
declare var NProgress: any;

@Component({
    selector: 'banner-alert-tag',
    templateUrl: './banner-alert-tag.component.html',
    styleUrls: ['./banner-alert-tag.component.css']
})
export class BannerAlertTagComponent implements OnInit, OnDestroy {
    alertForm;
    notifier;
    bannerAlertSubject;
    bannerAlertBody;
    title = 'New Banner Alerts';
    alertClass = "alert alert-warning alert-dismissible fade show";
    alertTypes = [];
    userTypes = [];
    userDetails: any;
    userData: any;;
    tenantDetails: any;;
    selectedBranches: any;
    selectedAlertType;
    startDate;
    showMore:boolean = false;
    endDate;
    alertItems;
    alertBannerMsg = [];
    alertNextCont = 0;
    disablenextbtn = true;
    disablePrevBtn = false;
    showMorelink:boolean =false;
    alertMessageData ;
    hasAlertMessage;
    params;
    showViewAlertBtn = false;
    yScrolloffset;
    @ViewChild('alert') alert: ElementRef;
    tenantId;
    siteIds;
	private socketEventSubscriptions: Subscription;
    constructor(
        private route: ActivatedRoute,
        private router: Router,
        private _structureService: StructureService,
        private _ToolTipService: ToolTipService,
        private _inboxService: InboxService,
        private _formBuild: FormBuilder,
        public _SharedService: SharedService,
        private registrationservice: RegistrationService,
        private _http: Http
        // private toastr: ToastrService
    ) {
        this._structureService.invokeEvent.subscribe(value => {
               this.ngOnInit();
        }); 
     
    }

    ngOnInit() {
        this.alertItems = [];
        this.alertNextCont = 0;
        this.params = {
            tenantId: this._structureService.getCookie("crossTenantId")
        };        
        this.userDetails = this._structureService.userDetails;
        this.userData = JSON.parse(this.userDetails);
        this.tenantId = this.userData.tenantId.toString();
        this.siteIds = this.userData.mySites.map((x) => {
            return x.id.toString();
        });
        this.tenantDetails = this.userData.crossTenantsDetails;
        this.bannerAlertDetails(this.params);
        function cleanBreak(str, str1) {
            return str
                .replace(/<br >/g, str1)
                .replace(/<br \/>/g, str1)
                .replace(/<br\/>/g, str1)
                .replace(/<br>/g, str1);
        }
        $('.cat__menu-left .cat__menu-left__action--menu-toggle').on('click', function () {   
            $('#banner-wrapper .alert-main').removeClass('banner-alert-full-width');
        });
        
        $(document).on("click", ".toggleMenuClick", function () {
            $('#banner-wrapper .alert-main').addClass('banner-alert-full-width');
        });
        this.socketEventSubscriptions = this._structureService.subscribeSocketEvent('BannerAlertWithPolling').subscribe((data) => {
            this.showViewAlertBtn = false;
            var isHTML = RegExp.prototype.test.bind(/(<([^>]+)>)/i);
            this.alertNextCont = 0;

            if(data.bannerId && data.bannerId != ''){
                if(data.status=="remove"){
                    this.alertItems =  this.alertItems.filter(function (bannerData) {
                              return bannerData.bannerid != data.bannerId;
                           }); 
                           console.log("alertItems after ",this.alertItems);
                }else {
                    if(data.data && data.data.length > 0){
                        if(this.alertItems && this.alertItems.length > 0){
                                this.alertItems =  this.alertItems.filter(function (bannerData) {
                                        return bannerData.bannerid != data.bannerId;
                                    }); 
                        }
                        //Tenant Id and Site Ids checking for the banner Polling results.
                        const newBanner = data.data.filter(obj => {
                            const isTenantMatch = !isBlank(obj.branchesToShow) ? obj.branchesToShow.split(',').includes(this.tenantId): false;
                            const isSiteMatch = !isBlank(obj.assignedSites) ? obj.assignedSites.split(',').some(sites => this.siteIds.includes(sites)): false;
                            if(!isTenantMatch || !isSiteMatch) {
                                this.bannerTrackActivity(obj);
                            }
                            return isTenantMatch && isSiteMatch;
                        });
                        if(!isBlank(newBanner)){
                            this.alertItems.push(newBanner[0]);
                        }
                     }
                    }               
            } else{
                this.alertItems = [];
                this.alertItems = data.data; 
            }
            this.alertItems = this.alertItems.sort(this.generateSortFn([{name: 'start_date_formated', reverse: true}, {name: 'alert_type',reverse: true}]));
          
            this.alertItems .forEach( (message) => {                
                var desctTemp = cleanBreak(message.description, "");
                message.description = cleanBreak(message.description, "<br>");
                message.multiline = false;
                if (isHTML(message.description)) {     
                        message.description = message.description.replace(/^<br>|<br>$/g, "");
                    if (!isHTML(desctTemp)) {
                        if (message.description.split("<br>").length > 2) {
                            message.showMore = true;
                            message.multiline = true;
                        }
                        else {
                            message.showMore = false;                            
                        }
                        message.descriptionValue = message.description.slice(0, 125) + '....';
                    } else {
                        message.showMore = false;
                        message.descriptionValue = message.description;
                    }


                } else {                     
                    message.showMore = true;
                    message.description = message.description.replace(/\r?\n/g, '<br>');                  
                    if (isHTML(message.description)) {                        
                        message.description = message.description.replace(/^<br>|<br>$/g, "");
                        
                        if (message.description.split("<br>").length > 2) { 
                            message.multiline = true;                                                       
                        }
                        message.descriptionValue = message.description;
                    } else {                        
                        message.descriptionValue = '';
                    }


                }
                
               

                //message.descriptionValue = decodeURIComponent(message.description);
              });
             // console.log('hiiiiiiiiiiiiiiiii',this.alertItems);
           // console.log(this.alertItems);
            //  this.ngOnInit();
            if (this.alertItems.length > 0) {
                $('.card').eq(0).addClass('card card_banner');
                $(document).ready(function () {
                    if ($(".cat__menu-left").hasClass("toggleMenuClick")) {
                        $('#banner-wrapper .alert-main').addClass('banner-alert-full-width');
                    }
                    if(!$('body').hasClass('cat__menu-left--visible')){
                        $('#banner-wrapper .alert-main').addClass('banner-alert-full-width');
                      }  
                });
            }
            else {
                $('.card').eq(0).removeClass('card_banner');
            }
        });
       this.router.events.subscribe((val) => {
           // this.bannerAlertDetails(this.params);
           if (this.alertItems.length > 0) {
               $('.card').eq(0).addClass('card card_banner');
           }
           if (this.showViewAlertBtn == true) {
               $('.card').eq(0).removeClass('card_banner');
           }
       });
        $('[data-toggle="tooltip"]').tooltip();
    }
    ngOnDestroy() {
       /**Unsubscribe all the socket event subscriptions */
        if(this.socketEventSubscriptions) this.socketEventSubscriptions.unsubscribe();
    }
    
    closeAlert() {
        console.log("hai");
        this.alertItems = [];
       // this.alert.nativeElement.classList.add('hide');
        this.showViewAlertBtn = true;
        $('.card').eq(0).removeClass('card_banner');
        // $('[data-toggle="tooltip"]').tooltip();

    }
    checkShowMore(id){
       // console.log("fsdfsdf",$( "div.banner-alert-"+id ).height()); 
        let height = $( "div.banner-alert-"+id ).show().height(); 
       // console.log(height);
        if(height > 44){
            this.showMorelink = true;
       } else {
        this.showMorelink = false;
      }
     $( "div.banner-alert-"+id ).hide(); 
     // console.log(this.showMorelink)
      //return false;
        
    }

    showAlert() {
        this.alert.nativeElement.classList.add('show');
    }

   
    cleanBreakWord(str, str1) {
        return str
            .replace(/<br >/g, str1)
            .replace(/<br \/>/g, str1)
            .replace(/<br\/>/g, str1)
            .replace(/<br>/g, str1);
    }
    generateSortFn(props) {
        return function (a, b) {
            for (var i = 0; i < props.length; i++) {
                var prop = props[i];
                var name = prop.name;
                var reverse = prop.reverse;
                if (a[name] < b[name])
                    return reverse ? 1 : -1;
                if (a[name] > b[name])
                    return reverse ? -1 : 1;
            }
            return 0;
        };
    };
    
    




    bannerAlertDetails(params) {
        console.log(this.userData.config.show_manage_alerts);
        this._structureService.getAllAlertMessageBytenantId(params).then((response:Banner[]) => {
            this.alertNextCont = 0;
            //Tenant Id and Site Ids checking for the banner API results.
            this.alertItems = response.filter(obj => {
                const isTenantMatch = !isBlank(obj.branchesToShow) ? obj.branchesToShow.split(',').includes(this.tenantId): false;
                const isSiteMatch = !isBlank(obj.assignedSites) ? obj.assignedSites.split(',').some(sites => this.siteIds.includes(sites)): false;
                if(!isTenantMatch || !isSiteMatch) {
                    this.bannerTrackActivity(obj);
                }
                return isTenantMatch && isSiteMatch;
            });
            this.alertItems = this.alertItems.sort(this.generateSortFn([{name: 'start_date_formated', reverse: true}, {name: 'alert_type',reverse: true}]));

            var isHTML = RegExp.prototype.test.bind(/(<([^>]+)>)/i);
            this.alertItems.forEach((message) => {
                var desctTemp = this.cleanBreakWord(message.description, "");
                message.description = this.cleanBreakWord(message.description, "<br>");
                message.multiline = false;
                if (isHTML(message.description)) {
                    console.log(message.description);
                    message.description = message.description.replace(/^<br>|<br>$/g, "");
                    console.log(message.description);
                    if (!isHTML(desctTemp)) {
                        if (message.description.split("<br>").length > 2) {
                            message.showMore = true;
                            message.multiline = true;
                        } else {
                            message.showMore = false;                           
                        }
                        message.descriptionValue = message.description.slice(0, 125) + '....';

                    } else {
                        message.descriptionValue = message.description;
                        message.showMore = false;
                    }
                } else {
                    message.showMore = true;
                    message.description = message.description.replace(/\r?\n/g, '<br>');
                    if (isHTML(message.description)) {
                        console.log(message.description);
                        message.description = message.description.replace(/^<br>|<br>$/g, "");
                        console.log(message.description);
                        console.log(message.description.split("<br>").length)
                        if (message.description.split("<br>").length > 2) {
                            console.log('hiiiiiiiiiiiiiiiii',555555);
                            message.multiline = true;                           
                        } else {   
                            console.log('hiiiiiiiiiiiiiiiii',66666666);
                        }
                        message.descriptionValue = message.description;
                    } else {
                        message.descriptionValue = '';                       
                    }
                }
            });

            if (this.alertItems.length) {
                this.hasAlertMessage = true;
            }
            console.log("ALLLLLLLLLLLLLLLLLLLLALERTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTT");
            console.log(this.alertItems);
            console.log("ALLLLLLLLLLLLLLLLLLLLALERTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTT");
            if (this.alertItems.length > 0) {
                $('.card').eq(0).addClass('card_banner');
                $(document).ready(function () {
                    if(!$('body').hasClass('cat__menu-left--visible')){
                        $('#banner-wrapper .alert-main').addClass('banner-alert-full-width');
                      }   
                    if ($(".cat__menu-left").hasClass("toggleMenuClick")) {
                        $('#banner-wrapper .alert-main').addClass('banner-alert-full-width');
                    }
                });
            }
        });
    }

   


    next() {
        ++this.alertNextCont;
    }

    previous() {
        --this.alertNextCont;
    }

    loadMoreAlert(sub, des) {
        console.log(sub);
        console.log(des);
        this.bannerAlertSubject = sub;
        this.bannerAlertBody = des;


    }

    showViewAlert() {
        this.showViewAlertBtn = false;
        this.params = {
            tenantId: this._structureService.getCookie("crossTenantId")
        };
        $("[data-toggle='tooltip']").tooltip('hide');
        this.bannerAlertDetails(this.params); 
    }

    @HostListener('window:scroll', ['$event'])
    onWindowScroll($event) {
        this.yScrolloffset = window.pageYOffset;
    }
    bannerTrackActivity(bannerData: any){
        const activityData = {
            activityName: 'Banner Data Site ID/Tenant ID Mismatch',
            activityType: 'banner data mismatch',
            activityLinkageId: bannerData.bannerid,
            activityDescription: 'Banner Data Site ID/Tenant ID Mismatch occured with User Id: '+this.userData.userId+' Site Id: '+this.siteIds+' Tenant ID: '+this.tenantId+' Banner Data: '+JSON.stringify(bannerData)
        };
        this._structureService.trackActivity(activityData);
    }

}
