import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>derer } from "@angular/core";
import { DatePipe } from "@angular/common";
import { Router, ActivatedRoute, Params } from "@angular/router";
import { StructureService } from "../structure.service";
import { SharedService } from "./../shared/sharedServices";
import { DomSanitizer } from "@angular/platform-browser";
import { FormBuilder } from "@angular/forms";
import { isInteger } from "@ng-bootstrap/ng-bootstrap/util/util";
import { Subject } from 'rxjs';
import { Store, StoreService } from 'app/structure/shared/storeService';
import { isBlank } from 'app/utils/utils';

declare var $: any;
declare var swal: any;
declare var NProgress: any;

interface Publish {
  id: number[];
  isPublish: Boolean;
}

@Component({
  selector: "app-manage-alert-details",
  templateUrl: "./manage-alert-details.component.html",
  styleUrls: ["./manage-alert.component.css"],
  providers: [DatePip<PERSON>]
})
export class ManageAlertDetailsComponent implements OnInit {
  groupList = [];
  dTable;
  datam;
  searchText = "";
  selectedData;
  dataLoadingMsg = true;

  userDetails:any;
  userData:any = {};
  crossTenantChangeSubscriber: any;
  contentLimit = 25;
  totalCount = 50;
  perPage = 25;
  currentPage = 1;
  loadingGroups = true;
  selectedAlertMessageId: any;
  newDataTenantDetails;
  alertItems;
  params;
  selectSiteId:any = "0";
  hideSiteSelection:Boolean;
  eventsSubject: Subject<void> = new Subject<void>();
  disableDeleteBtn = true;
  initialLoad = false;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private _structureService: StructureService,
    private _formBuild: FormBuilder,
    private datePipe: DatePipe,
    elementRef: ElementRef,
    renderer: Renderer,
    public _SharedService: SharedService,
    private sanitizer: DomSanitizer,
    private storeService: StoreService
  ) { 
    this.userDetails = this._structureService.userDetails;
    this.userData = JSON.parse(this.userDetails);
    renderer.listen(elementRef.nativeElement, "click", (event, target) => {
      if (event.target.id == "editgrp" || event.target.id == "editgrp-icon") {
        this._structureService.setCookie(
          "alertMessageId",
          this.selectedAlertMessageId.id,
          1
        );
        this.router.navigate(["/edit-alerts"]);
      } else if (
        event.target.id == "deletegrp" ||
        event.target.id == "deletegrp-icon"
      ) {
        this.deleteAlertMessage(this.selectedAlertMessageId.id);
      } else if (event.target.id == "publish-message") {
        let publish = {
          id: this.selectedAlertMessageId.id,
          isPublish: true
        };
        this.publishAlertMessage(publish);
      } else if (event.target.id == "unpublish-message") {
        let unPublish = {
          id: this.selectedAlertMessageId.id,
          isPublish: false
        };
        this.publishAlertMessage(unPublish);
      }

      // Handle click on "Select all" control.
      if (event.target.id == "pdg-select-all") {
        var rows = this.dTable.rows({ search: "applied" }).nodes();
        $('input[type="checkbox"]', rows)
          .not(":disabled")
          .prop("checked", event.target.checked);
      }
      if (event.target.className.split(" ").indexOf("pdg-select-cls") != -1) {
        let count = 0;
        $(".pdg-select-cls").each(function() {
          if ($(this).prop("checked") == true) {
            count = 1;
          }
        });
        if (count > 0) {
          console.log("haida");
       //   document.getElementById("archive-alert-material").setAttribute("disabled", "false");
          this.disableDeleteBtn = false;
        } else {
          console.log("haida mone");
      //    document.getElementById("archive-alert-material").setAttribute("disabled", "disabled");
          this.disableDeleteBtn = true;
        }
      }

      //Handle Archive all
      if (event.target.id == "archive-alert-material") {
        swal(
          {
            title: "Are you sure?",
            text: "You will not be able to recover this alert message(s).",
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            confirmButtonText: "Ok",
            closeOnConfirm: true
          },
          () => {
            var allIds = [];
            var unreadCountList = false;

            $('input[name="pdgid[]"]').each(function() {
              if (this.checked) {
                allIds.push($(this).val());
                unreadCountList = true;
              }
            });

            if (!unreadCountList) {
              this._structureService.notifyMessage({
                messge: "Select at least one Alert Message",
                delay: 1000,
                type: "warning"
              });
            } else {
              this.archiveAllAlertMessages(allIds);
            }
          }
        );
      }

      //Handle Publish.
      if (event.target.id == "publish-all-alert-material") {
        swal(
          {
            title: "Are you sure?",
            text: "Are you sure to puiblish all selected alert message(s)",
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            confirmButtonText: "Ok",
            closeOnConfirm: true
          },
          () => {
            var allIds = [];
            var unreadCountList = false;

            $('input[name="pdgid[]"]').each(function() {
              if (this.checked) {
                allIds.push($(this).val());
                unreadCountList = true;
              }
            });

            if (!unreadCountList) {
              this._structureService.notifyMessage({
                messge: "Select at least one Alert Message",
                delay: 1000,
                type: "warning"
              });
            } else {
              let publish = {
                id: allIds,
                isPublish: true
              };
              this.publishAllAlertMessages(publish);
            }
          }
        );
      }

      if (event.target.id == "unpublish-all-alert-material") {
        swal(
          {
            title: "Are you sure?",
            text: "Are you sure to unpuiblish all selected alert message(s)",
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            confirmButtonText: "Ok",
            closeOnConfirm: true
          },
          () => {
            var allIds = [];
            var unreadCountList = false;
            $('input[name="pdgid[]"]').each(function() {
              if (this.checked) {
                allIds.push($(this).val());
                unreadCountList = true;
              }
            });

            if (!unreadCountList) {
              this._structureService.notifyMessage({
                messge: "Select at least one Alert Message",
                delay: 1000,
                type: "warning"
              });
            } else {
              let publish = {
                id: allIds,
                isPublish: false
              };
              this.publishAllAlertMessages(publish);
            }
          }
        );
      }
      if(event.target.id == 'more_sitenames') {
        $("body .opernmore_sitesnames").css("display","block");
        $("body .more-sitenames").css("display","none");
        $("body .closemore_sitenames").css("display","none");

        var attributes =$(event.target).attr('data-moreId');
        console.log("attributes===>"+attributes);
        $("body #"+attributes).css("display","block");
        $('table tr td').find('.more-sitenames#'+attributes).attr('style','display:block')
        $("body .opernmore_sitesnames"+attributes).css("display","none");
        $("body .closemore_sitenames"+attributes).css("display","block");
      }else if(event.target.id == "closemore_sitenames"){
        var attributes =$(event.target).attr('data-moreId');
        $("body #"+attributes).css("display","none");
        $('table tr td').find('.more-sitenames#'+attributes).attr('style','display:none');
        $("body .opernmore_sitesnames"+attributes).css("display","block");
        $("body .closemore_sitenames"+attributes).css("display","none");
      } 
    });
  }

  ngOnInit(): void {
    // this.populateAlertMessageData();
    this.initialLoad = true;
  }

  ngOnDestroy() {
    if (this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
  }

  populateAlertMessageData(): void {
    this.dataLoadingMsg = true;

    var self = this;
    let datas: any;
    if (this.dTable) {
      this.dTable.destroy();
    }
    var isTrue = false;
    if (this.groupList.length > 99) {
      isTrue = true;
    }
    // Array holding selected row IDs
    $(()=>{
    this.dTable = $("#bannerAlertTableId").DataTable({
      autoWidth: false,
      responsive: true,
      bprocessing: true,
      bServerSide: true,
      bpagination: true,
      bsorting: true,
      retrieve: true,
      bsearching: true,
      bInfo: true,
      lengthMenu: [
        [25, 50],
        [25, 50]
      ],
      fnDrawCallback: function(oSettings) {
        console.log("oSettings", oSettings);
        if (
          oSettings._iRecordsTotal == 0 ||
          oSettings._iRecordsTotal < oSettings._iDisplayLength ||
          oSettings.aoData.length == 0
        ) {
          $(".dataTables_paginate").hide();
        } else {
          $(".dataTables_paginate").show();
        }
        if (oSettings.aoData.length == 0) {
          $(".dataTables_info").hide();
        } else {
          $(".dataTables_info").show();
        }
      },
      fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
        $(nRow).on("click", () => {


          console.log("KKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK");
          console.log(aData);
          console.log("KKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK");


          this.selectedAlertMessageId = aData;
        });
      },
      dom:
			"<'row'<'col-sm-4 'l>B<'col-sm-4'f><'col-sm-2 searchButton'>>" +
			"<'row'<'col-sm-12'tr>>" +
			"<'row'<'col-sm-5'i><'col-sm-7'p>>",
			buttons :[{
			  extend: 'collection',
			  text: 'Export Data',
			  autoClose: true,
			  className: 'buttonStyle',
			  buttons: [
				{
				  extend: 'excel',
				  text: 'Current Page',
				  title: 'Banner Alerts',
				  exportOptions: {
					columns:[0,1,2,3,4,5,6,7]
				  }
				},
				{
				  extend: 'excel',
				  text: 'All Pages',
				  title: 'Banner Alerts',
				  exportOptions: {
         			 columns:[0,1,2,3,4,5,6,7]
				  },
				  action: function ( e, dt, node, config ) {
				  var selfButton = this;
				  var oldStart = dt.settings()[0]._iDisplayStart;
				  dt.one('preXhr', function (e, s, data) {
					  data.start = 0;
					  data.length = this.totalCt;
					  dt.one('preDraw', function (e, settings) {
						  $.fn.dataTable.ext.buttons.excelHtml5.action.call(selfButton, e, dt, node, config);
						  dt.one('preXhr', function (e, s, data) {
							  settings._iDisplayStart = oldStart;
							  data.start = oldStart;
						  });
						  setTimeout(dt.ajax.reload, 0);
						  return false;
					  });
				  });
				  dt.ajax.reload();
				  }
			  }]
		  }],
      initComplete: function() {
        $(".dataTables_filter label input").attr("placeholder", "Search");
        $(".dataTables_filter label input").unbind();
        $("div.dataTables_filter input").on("keydown", function(e) {
          if (e.which == 13) {
            var value = $("div.dataTables_filter input").val();
            if (value) {
              value = value.replace("”", '"');
              value = value.replace("‘", "'");
              value = value.replace("’", "'");
              self.dTable.search(value).draw();
            } else {
              self.dTable.search("").draw();
            }
          }
        });
        $("div.dataTables_filter input").on("keypress", function(e) {
          $(".searchBView").prop("disabled", false);
        });
        $("div.dataTables_filter input").on("keyup", function(e) {
          var value = $("div.dataTables_filter input").val();
          if (value) {
          } else $(".searchBView").prop("disabled", true);
        });
        $("div.searchButton").html(
          `<button disabled="true" class="btn btn-sm btn-info searchBView" title="Search" type="submit">Search</button>
            <button style="margin-left:10px;" class="btn btn-sm btn-default resetBView" title="Reset" type="submit">Reset</button>`
        );
        var value = $("div.dataTables_filter input").val();
        if (value) {
          $(".searchBView").prop("disabled", false);
        }
        $("div.dataTables_filter input").on("paste", function(event) {
          console.log("eeeeeeeeeeeeeeeeee", event);
          var element = this;
          var text;
          setTimeout(function() {
            text = $(element).val();
            if (text) {
              $(".searchBView").prop("disabled", false);
            }
          }, 100);
        });
        $(".buttons-collection").click(function(event) {
					setTimeout(function () {
					  if($(".buttons-collection").attr("aria-expanded") === "true" && $(".dt-button-collection").find("button").length == 0){
						$(".dt-button-collection").remove();
						$(".dt-button-background").remove();
						$(".buttons-collection").attr("aria-expanded","false");
					  }
					},500);
				});
      },
      //data: this.groupList,
      ajax: function (dat, callback, settings) {
        self.dataLoadingMsg = true;
        NProgress.start();
        let orderData;
        let searchText = '';
        let orderby;
        let limit;
        var i = dat.order[0].column ? dat.order[0].column : "";
        orderby = dat.order[0].dir ? dat.order[0].dir : "";
        if (isInteger(i)) {
          orderData = dat.columns[i].data ? dat.columns[i].data : "";
        } else {
          orderData = "id";
        }
        if(!isBlank(dat.search.value)) {
          searchText = dat.search.value;
          self.storeService.storeData(Store.SEARCH_BANNER_ALERT, searchText);
        } else if(!isBlank(self.storeService.getStoredData(Store.SEARCH_BANNER_ALERT)) ){
          searchText = self.storeService.getStoredData(Store.SEARCH_BANNER_ALERT);
          $("div.dataTables_filter input").val(searchText);
          if(self.initialLoad) self._structureService.notifySearchFilterApplied(true);
        }
        
        self._structureService
          .getAlertMessageList(
            dat.length ? dat.length:this.totalCt,
            dat.start,
            orderData,
            orderby,
            searchText,
            self.selectSiteId
          )
          .then(resultData => {
            self.initialLoad = false;
            self.dataLoadingMsg = false;
            NProgress.done();
            datas = {};
            self.datam = {};
            if (dat.start == 0) {
              this.totalCt =
                resultData[
                  "getSessionTenant"
                ].alertMessageListPagination.totalCount;
            }
            datas = [];
            datas = resultData["getSessionTenant"]
              ? resultData["getSessionTenant"]["alertMessageList"]["data"]
              : [];

            self.groupList = datas;
            console.log("datas to list in data table ", self.groupList);
            let draw;
            let total;
            if (datas && datas.length == 0 && searchText == '') {
              draw = 0;
              total = 0;
            } else {
              draw = dat.draw;
              total = this.totalCt;
            }

            self.datam = {
              draw: draw,
              recordsTotal: total,
              recordsFiltered: total,
              aaData: datas
            };
            callback(self.datam);
          });
      },
      columns: [
        {
          title:
            '<input type="checkbox" name="select_all" value="1" style="float:left" class="pdg-select-all-indicator pdg-select-cls" id="pdg-select-all" title="Select all for Delete">'
        },
        { title: "Subject", data: "subject" },
        { title: "Message", data: "description" },
        { title: "Start Date", data: "startDate" },
        { title: "End Date", data: "endDate" },
        { title: "Created On", data: "createdOn" },
        { title: "Sites to be Shown", data: "siteNames" },
        { title: "Alert Type", data: "alertTypeName"},
        { title: "Actions" }
      ],
      columnDefs: [
        {
          targets: 0,
          searchable: false,
          orderable: false,
          width: "3%",
          className: "dt-body-center",
          render: (document, type, row) => {
            return `<input type="checkbox" name="pdgid[]" class="pdg-select-all-indicator pdg-select-cls" value="${row.id}">`;
          }
        },
        {
          data: null,
          targets: 1,
          width: "10%",
          orderable: true,
          render: function(data, type, row) {
            if (row.subject) {
              return row.subject;
            } else {
              return "";
            }
          }
        },
        {
          data: null,
          targets: 2,
          width: "15%",
          orderable: true,
          render: function(data, type, row) {
            if (row.description) {
              return row.description;
            } else {
              return "";
            }
          }
        },
        {
          data: null,
          targets: 3,
          width: "10%",
          orderable: true,
          //orderData: 5,
          render: function(data, type, row) {
            if (
              row.startDate &&
              row.startDate != null &&
              row.startDate != "null"
            ) {
              var datePipe = new DatePipe("en-US");
              return datePipe.transform(row.startDate * 1000, "MM/dd/yyyy");
            } else {
              return "";
            }
          }
        },
        {
          data: null,
          targets: 4,
          width: "10%",
          orderable: true,
          //orderData: 5,
          render: function(data, type, row) {
            if (row.endDate && row.endDate != null && row.endDate != "null") {
              var datePipe = new DatePipe("en-US");
              return datePipe.transform(row.endDate * 1000, "MM/dd/yyyy");
            } else {
              return "";
            }
          }
        },
        {
          data: null,
          targets: 5,
          width: "10%",
          orderable: true,
          //orderData: 5,
          render: function(data, type, row) {
            if (row.createdOn) {
              var datePipe = new DatePipe("en-US");
              return datePipe.transform(
                row.createdOn * 1000,
                "MMM dd y hh:mm a"
              );
            } else {
              return "";
            }
          }
        },
        {
          data: null,
          targets: 6,
          width: "10%",
          orderable: true,
          render: function (data, type, row) {
            if ((row.siteNames && row.siteNames != null && row.siteNames != "null")||(row.branchNames && row.branchNames != null && row.branchNames != "null")) {
              let siteName = (self.userData.config.enable_multisite == 1)? row.siteNames.split(','):row.branchNames.split(','),siteNam = "",labelHtml="",count =(self.userData.config.enable_multisite == 1)?row.siteNames.split(',').length:row.branchNames.split(',').length;
              for(let k=0;k<count; ++k){
                    siteNam=siteName[k];
                    if(k === 0){
                      labelHtml += siteNam;
                    }
                    if(count > 1 && k === 1){
                      labelHtml+=`<a class="pull-right btn btn-sm opernmore_sitesnames opernmore_sitesnames`+row.id+`" href="javascript: void(0);" title="Show more sites" data-moreId=`+row.id+` id="more_sitenames" ><i id="more_sitenames" data-moreId=`+row.id+` class="fa fa-plus" aria-hidden="true"></i></a>`;
                      labelHtml+=`<a class="pull-right btn btn-sm closemore_sitenames closemore_sitenames`+row.id+`" href="javascript: void(0);" title="Close" style="display:none" data-moreId=`+row.id+` id="closemore_sitenames" ><i id="closemore_sitenames" data-moreId=`+row.id+` class="fa fa-close" aria-hidden="true"></i></a>`;
                      labelHtml+=`<div class="more-sitenames" style="display:none" id="`+row.id+`">`;
                    }
                    if(k >= 1){
                      labelHtml+=`<label>`+siteNam+`</label>`;
                    }
                    if(k === (count-1) ){
                      labelHtml+=`</div>`;
                    }
                }
              return labelHtml;
            } else {
              return "";
            }
          }
        },
        {
          data: null,
          targets: 7,
          width: "10%",
          orderable: true,
          render: function (data, type, row) {
            if (row.alertTypeName && row.alertTypeName != null && row.alertTypeName != "null") {
              return row.alertTypeName;
            } else {
              return "";
            }
          }
        },
        {
          data: null,
          orderable: false,
          render: (data, type, row) => {
            let actions = "";
            actions += `<a class="banner-alert-table-delete pull-right btn btn-sm" title="Delete" href="javascript: void(0);" data-rowId ="${row.id}" id="deletegrp" ><i data-rowId ="${row.id}" id="deletegrp-icon" class="fa fa-trash" aria-hidden="true"></i></a>`;

            actions += `<a class="banner-alert-table-edit pull-right btn btn-sm" title="Edit" href="javascript: void(0);" data-rowId ="${row.id}" id="editgrp"  ><i data-rowId ="${row.id}" id="editgrp-icon" class="fa fa-pencil" aria-hidden="true"></i></a>`;

            if (row.isPublished == 0) {
              actions += `<a class="btn btn-sm btn-primary btn-publish" type= "button" data-rowId ="${row.id}" id="publish-message" title="Publish"> Publish </a>`;
            } else if (row.isPublished == 1) {
              actions += `<a class="btn btn-sm btn-primary btn-unpublish" type= "button"  data-rowId ="${row.id}" id="unpublish-message" title="UnPublish"> Unpublish </a>`;
            }
            return actions;
          },
          width: "13%",
          targets: 8
        }
      ],
      language: {
        emptyTable: "No Alert Message found."
      },
      order: [[5, "desc"]]
    });

    $(document).on("click", ".resetBView", event => {
      self.storeService.removeData(Store.SEARCH_BANNER_ALERT);
      self._structureService.notifySearchFilterApplied(false);
      self.dTable.search("").draw();
      $(".searchBView").prop("disabled", true);
    });
    $(document).on("click", ".searchBView", event => {
      var value = $(
        "#bannerAlertTableId_wrapper #bannerAlertTableId_filter label input"
      ).val();
      if (value) {
        value = value.replace("”", '"');
        value = value.replace("‘", "'");
        value = value.replace("’", "'");
        self.dTable.search(value).draw();
      } else {
        self.dTable.search("").draw();
      }
    });
        });

  }

  /**
   *  Delete Alert Message permanent.
   */
  deleteAlertMessage(id: number): void {
    var Ids = [];
    Ids.push(id);
    swal(
      {
        title: "Are you sure?",
        text: "You will not be able to recover this Alert Message",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true
      },
      () => {
        this.dataLoadingMsg = true;
        this._structureService.deleteAlertMessage(Ids).subscribe(data => {
          const deletedObj = Object.keys(data).map(key => data[key]);
          let deletedMessageId = deletedObj[0].deleteAlertMessage.id;
          console.log("activityyyyyyyyyyyy", deletedObj, deletedMessageId);
          var notify = $.notify("Success! Alert Message deleted");
          setTimeout(function() {
            notify.update({
              type: "success",
              message: "<strong>Success! Alert Message deleted</strong>"
            });
          }, 1000);
          // this.params = {
          //   tenantId: this._structureService.getCookie("crossTenantId")
          // };
          // this.bannerAlertDetails(this.params);
          this.populateAlertMessageData();

          // document
          //   .getElementById("archive-alert-material")
          //   .setAttribute("disabled", "true");
          this.disableDeleteBtn = true;
          var activityLogMessage = `${this.userData.displayName} deleted Alert Message with id(s) ${deletedMessageId}`;
          var activityData = {
            activityName: "Delete Alert Message",
            activityType: "manage alert message",
            activityDescription: activityLogMessage
          };
          this._structureService.trackActivity(activityData);
        });
      }
    );
  }

  publishAlertMessage(publish: Publish) {
    let messageData = " published";
    if (!publish.isPublish) {
      messageData = " unpublished";
    }

    // this.newDataTenantDetails = {
    //   status: "new",
    //   id: this._structureService.getCookie("crossTenantId")
    // };
    this.dataLoadingMsg = true;
    this._structureService.publishAlertMessage(publish).subscribe(data => {
      this.dataLoadingMsg = true;
      const deletedObj = Object.keys(data).map(key => data[key]);
      let deletedMessageId = deletedObj[0].publishAlertMessage[0].id;
      console.log("dataaaaaaaaa", deletedObj, deletedMessageId);
      // Emit alert message on puiblish / unpublish.
      // this._structureService.socket.emit(
      //   "newBannerAlert",
      //   this.newDataTenantDetails
      // );

      this.params = {
        tenantId: this._structureService.getCookie("crossTenantId")
      };
      let detaildParams = {        
        userType:this.userData.group,
        branchesToShow:this.selectedAlertMessageId.branchesToShow,
        bannerId:deletedMessageId,
        tenantId: this._structureService.getCookie("crossTenantId"),
        siteIds: (this.selectedAlertMessageId.siteIds !== undefined)?this.selectedAlertMessageId.siteIds:''
      };
      //this.bannerAlertDetails(this.params);
      if(publish.isPublish){    
        this.removeBannerAlertDetails(detaildParams);    
        this.addBannerAlertDetails(detaildParams);
      } else{
        this.removeBannerAlertDetails(detaildParams);
      }
      
     
      var activityLogMessage = `${this.userData.displayName}  ${messageData} Alert Message with id  ${deletedMessageId}`;

      var activityData = {
        activityName: `${messageData} Alert Message`,
        activityType: "manage alert message",
        activityDescription: activityLogMessage
      };
      this._structureService.trackActivity(activityData);
      $("#pdg-select-all").prop("checked", false);
    });

    var notify = publish.isPublish
      ? $.notify(`Success! Alert Message published`)
      : $.notify(`Success! Alert Message unpublished`);
    // document
    //   .getElementById("archive-alert-material")
    //   .setAttribute("disabled", "true");
    this.disableDeleteBtn = true;
    setTimeout(function() {
      notify.update({
        type: "success",
        message: publish.isPublish
          ? `<strong>Success! Alert Message published </strong>`
          : `<strong>Success! Alert Message unpublished</strong>`
      });
    }, 1000);
    this.populateAlertMessageData();
  }

  /**
   * Delete education materials- multiple rows.
   */
  archiveAllAlertMessages(allIds) {
    this.dataLoadingMsg = true;
    this._structureService.deleteAlertMessage(allIds).subscribe(data => {
      const deletedObj = Object.keys(data).map(key => data[key]);
      let deletedMaterialId = deletedObj[0].deleteAlertMessage.id;
      console.log("activityyyyyyyyyyyy", deletedObj, deletedMaterialId);
      var notify = $.notify("Success! Alert Message deleted");
      setTimeout(function() {
        notify.update({
          type: "success",
          message: "<strong>Success! Alert Message deleted</strong>"
        });
      }, 1000);
      // document
      //   .getElementById('archive-alert-material')
      //   .setAttribute('disabled', 'disabled');
      this.disableDeleteBtn = true;
      this.populateAlertMessageData();
      var activityLogMessage = `${this.userData.displayName} deleted Alert Message(s) with id's ${deletedMaterialId}`;

      var activityData = {
        activityName: "Delete Alert message",
        activityType: "manage alert message",
        activityDescription: activityLogMessage
      };
      this._structureService.trackActivity(activityData);
      // document
      //   .getElementById('archive-alert-material')
      //   .setAttribute('disabled', 'disabled');
      this.disableDeleteBtn = true;
      $("#pdg-select-all").prop("checked", false);
    });
  }

  publishAllAlertMessages(publish: Publish): void {
    let publishStringObj = [];
    let messageData = " published";
    if (!publish.isPublish) {
      messageData = " unpublished";
    }

    // this.newDataTenantDetails = {
    //   status: "new",
    //   id: this._structureService.getCookie("crossTenantId")
    // };

    this.dataLoadingMsg = true;
    this._structureService.publishAlertMessage(publish).subscribe(data => {
      const deletedObj = Object.keys(data).map(key => data[key]);
      console.log(deletedObj);
      let alertMessageId = deletedObj[0].publishAlertMessage[0].id;
      alertMessageId.forEach(element => {
        publishStringObj.push(element.id);
      });
      let alertMessageIds = publishStringObj.join(",");

      // this._structureService.socket.emit(
      //   "newBannerAlert",
      //   this.newDataTenantDetails
      // );

      var activityLogMessage = `${this.userData.displayName} ${messageData} Alert Message with id ${alertMessageIds}`;

      var activityData = {
        activityName: `${messageData} Alert Message`,
        activityType: "manage alert message",
        activityDescription: activityLogMessage
      };
      this._structureService.trackActivity(activityData);
      $("#pdg-select-all").prop("checked", false);

      this.params = {
        tenantId: this._structureService.getCookie("crossTenantId")
      };
      let detaildParams = {    
        branchesToShow:this.selectedAlertMessageId.branchesToShow,
        userType:this.userData.group,
        bannerId:alertMessageId,
        tenantId: this.selectedAlertMessageId.branchesToShow,
        siteIds : (this.selectedAlertMessageId.siteIds !== undefined)?this.selectedAlertMessageId.siteIds:''
      };
      this.removeBannerAlertDetails(detaildParams)
      this.addBannerAlertDetails(detaildParams);
    });

    var notify = publish.isPublish
      ? $.notify(`Success! Alert Message(s) published`)
      : $.notify(`Success! Alert Message(s) unpublished`);
    document
      .getElementById("publish-all-alert-material")
      .setAttribute("disabled", "true");
    setTimeout(function() {
      notify.update({
        type: "success",
        message: publish.isPublish
          ? `<strong>Success! Alert Message(s) published </strong>`
          : `<strong>Success! Alert Message(s) unpublished</strong>`
      });
    }, 1000);
    this.populateAlertMessageData();
  }
  removeBannerAlertDetails(params) {
    console.log(params);
    let param = {
      status: "remove",
      id: params.branchesToShow,
      bannerId:params.bannerId,
      siteIds :params.siteIds
  };


  this._structureService.socket.emit(
      "newBannerAlert",
      param,
      {}
  );

    
  }
  addBannerAlertDetails(params) {
    console.log(this.selectedAlertMessageId);
     console.log(params);
    this._structureService.getAllAlertMessageBytenantId(params,params.bannerId).then(response => {
      console.log("iddddddddddddddddddddddddddddddddddddddd",params);
      this.alertItems = response;    
                        let param = {
                            status: "new",
                            id:  params.branchesToShow,
                            bannerId:params.bannerId,
                            siteIds: params.siteIds
                        };
        
        
                        this._structureService.socket.emit(
                            "newBannerAlert",
                            param,
                            this.alertItems
                        );
                      });
  }

  bannerAlertDetails(params) {
    console.log(this.selectedAlertMessageId);
    this._structureService.getAllAlertMessageBytenantId(params).then(response => {
      console.log("iddddddddddddddddddddddddddddddddddddddd",this.selectedAlertMessageId.branchesToShow);
      this.alertItems = response;
      // if (this.alertItems.length) {
        let param = {
          status: "new",
          //id: this._structureService.getCookie("crossTenantId")
          id: this.selectedAlertMessageId.branchesToShow
        };

        
        this._structureService.socket.emit(
          "newBannerAlert",
          param,
          this.alertItems
        );

      // }
      console.log("ALLLLLLLLLLLLLLLLLLLLALERTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTT");
      console.log(this.alertItems);
      console.log("ALLLLLLLLLLLLLLLLLLLLALERTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTT");


    });
  }
  getSiteIds(data:any){
    this.selectSiteId = data['siteId'].toString();
    this.populateAlertMessageData();
  }
  hideDropdown(hideItem : any){
    console.log('hide emit 1 ',this.hideSiteSelection);
    this.hideSiteSelection = hideItem.hideItem;
  }
}
