import { AuthGuard } from './../../guard/auth.guard';
import { NgModule } from "@angular/core";
import { CommonModule } from "@angular/common";
import { Routes, RouterModule } from "@angular/router";
import { BrowserModule } from "@angular/platform-browser";
import { ReactiveFormsModule } from "@angular/forms";
import { FormsModule } from "@angular/forms";
import { ColorPickerModule } from "ngx-color-picker";
import { NgUploaderModule } from "ngx-uploader";
import { NgbModule } from "@ng-bootstrap/ng-bootstrap";
import { ManageAlertComponent } from "./manage-alert.component";
import { UpdateAlertComponent } from "./update-alert.component";
import { ManageAlertDetailsComponent } from "./manage-alert-details.component";
import { SharedModule } from '../shared/sharedModule';


export const routes: Routes = [
    { path: "new-banner-alerts", component: ManageAlertComponent, canActivate:[AuthGuard] , data: {checkRoutingPrivileges : 'superAdmin,showManageAlert',checkUserGroupPermission : '3'} },
    { path: "banner-alerts", component: ManageAlertDetailsComponent, canActivate:[AuthGuard] , data: {checkRoutingPrivileges : 'superAdmin,showManageAlert'} },
    { path: "edit-alerts",  component: UpdateAlertComponent, canActivate:[AuthGuard] , data: {checkRoutingPrivileges : 'superAdmin,showManageAlert',checkUserGroupPermission : '3'} }
];

@NgModule({
    imports: [
        CommonModule,
        FormsModule,
        BrowserModule,
        ReactiveFormsModule,
        RouterModule.forChild(routes),
        NgUploaderModule,
        ColorPickerModule,
        //BrowserAnimationsModule,
        // ToastrModule.forRoot(),
        // NotifierModule.withConfig(),
        NgbModule.forRoot(),
        SharedModule
    ], 
    declarations: [ManageAlertComponent, ManageAlertDetailsComponent, UpdateAlertComponent],
    providers: []
})
export class ManageAlertModule { }
