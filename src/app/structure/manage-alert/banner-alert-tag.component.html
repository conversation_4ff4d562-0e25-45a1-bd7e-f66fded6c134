<div id="banner-wrapper">
    <div [class]="alertItems[alertNextCont] == alertBanner ? 'show': 'hide'"  *ngFor="let alertBanner of alertItems;">
        <div #alert class="fade show alert-main"
            [ngClass]="{'autoHeight':alertBanner.descriptionValue && !alertBanner.showMore,'alert alert-warning alert-section-warning alert-warning-dismissible': alertBanner.alert_type=='2', 'alert alert-danger alert-section-error alert-danger-dismissible': alertBanner.alert_type=='3', 'alert alert-info alert-section-information alert-info-dismissible': alertBanner.alert_type=='1'}"
            role="alert">
            <div class="row">
                <div class="alert-icon-section" *ngIf="alertBanner.alert_type =='2'"><img src="./assets/img/warning-icon.png">
                </div>
                <div class="alert-icon-section" *ngIf="alertBanner.alert_type =='3'"><img src="./assets/img/error-icon.png"></div>
                <div class="alert-icon-section" *ngIf="alertBanner.alert_type =='1'"><img src="./assets/img/information-icon.png">
                </div>
                <div class="alert-right-section">
                    <h2><strong>{{alertBanner.subject|slice:0:59}}<span class="more-load-text" *ngIf="alertBanner.subject.length > 59">...</span></strong>
                        <span class="alert-close">
                            <button type="button" class="close" aria-label="Close" (click)="closeAlert()">
                                <i class="fa fa-times" aria-hidden="true"></i>
                            </button>
                        </span>
                    </h2>
                    <div *ngIf="!alertBanner.descriptionValue" >
                <p>{{alertBanner.description|slice:0:124}}<span *ngIf = "alertBanner.description.length > 125">...</span></p>
                    <span  *ngIf="alertBanner.description.length > 125 || alertBanner.subject.length > 60"><a   data-toggle="modal" data-target="#myBannerModal"
                            id="showMoreLink" class="showmore-btn" (click)="loadMoreAlert(alertBanner.subject,alertBanner.description)"><u>Show More</u></a>
                    </span>
                    </div>
					 <div *ngIf="alertBanner.descriptionValue && !alertBanner.showMore" >
                        <div style="width: 80%;
                        float: left;
                        height: 44px;
                        overflow: hidden;
                        text-overflow: ellipsis;color:#fff" [innerHTML]="alertBanner.descriptionValue"></div>   
                    
                        <div style="width: 20%;margin-top: 22px;float:right" *ngIf="checkShowMore(alertBanner.bannerid) || showMorelink || alertBanner.subject.length > 60"><a   data-toggle="modal" data-target="#myBannerModal"
                            id="showMoreLink" class="showmore-btn" (click)="loadMoreAlert(alertBanner.subject,alertBanner.description)"><u>Show More</u></a>
                        </div>                        
                        </div>
                        <div class="banner-alert-{{alertBanner.bannerid}}" *ngIf="alertBanner.descriptionValue " style="width: 80%;
                            float: left;display: none;" [innerHTML]="alertBanner.descriptionValue">
                        </div>   
                    <div *ngIf="alertBanner.descriptionValue && alertBanner.showMore" >
                        <div style="width: 80%;
                        float: left;
                        height: 44px;
                        overflow: hidden;
                        text-overflow: ellipsis;color:#fff" [innerHTML]="alertBanner.descriptionValue"><span *ngIf = "alertBanner.descriptionValue.length > 125">...</span></div>
                        <div style="width: 20%;
                        float: right;
                        margin-top: 22px;"  *ngIf="alertBanner.multiline || alertBanner.subject.length > 60" ><a   data-toggle="modal" data-target="#myBannerModal"
                                id="showMoreLink" class="showmore-btn" (click)="loadMoreAlert(alertBanner.subject,alertBanner.description)"><u>Show More</u></a>
                        </div>
                            </div>                
                </div>
                <div class="row">
                    <div>
                        <button *ngIf ="alertNextCont > 0" type="button" class="previous-button" aria-label="Next" (click)="previous()">
                            <i class="fa fa-chevron-left" aria-hidden="true"></i>
                        </button>
                    </div>
                    <div>
                        <button *ngIf ="alertItems.length-1 > alertNextCont" type="button" class="next-button" aria-label="Next" (click)="next()">
                            <i class="fa fa-chevron-right" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div> 
    <div [hidden]="showViewAlertBtn != true">
    <a  (click) ="showViewAlert()" class="show-banner-alert-btn">
        <i id="bannertooltip" data-toggle="tooltip" data-animation="false" data-placement="left" data-original-title="Show Banner Alert(s)">
            <img _ngcontent-c0="" src="./assets/img/alert.png" style="width:16px;">
        </i>
    </a>
    </div>
</div>

<div class="modal fade" id="myBannerModal" role="dialog">
    <div class="modal-dialog modal-lg">

        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">{{bannerAlertSubject}}</h4>
                <button type="button" class="close" data-dismiss="modal">&times;</button>
            </div>
            <div class="modal-body"  [innerHTML]="bannerAlertBody">
            </div>    
        </div>
    </div>
</div>