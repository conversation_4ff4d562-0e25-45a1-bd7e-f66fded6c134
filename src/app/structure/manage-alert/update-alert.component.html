<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>{{title}}</strong>
        </span>
    </div>

    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/banner-alerts']">Banner Alerts</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/edit-alerts']">Edit Banner Alert</a></li>
        </ol>

        <form [formGroup]="alertForm" id="tagForm">
            <div class="form-body">

                <div class="form-group row">
                    <label class="col-md-3 control-label"> {{'LABELS.BANNER_ALERT_MESSAGE_TYPE' | translate}}</label>
                    <div class="col-md-9">
                        <select class="form-control select2" formControlName="alerttype" id="alertTypeId">
                            <option *ngFor="let alerttype of alertTypes" value="{{alerttype.id}}">
                                {{alerttype.name}}
                            </option>
                        </select>
                        <div *ngIf="submitted && inValidalertType" class="alert alert-danger">
                            <div *ngIf="inValidalertType">Type Of Message cannot be empty</div>
                        </div>
                    </div>
                </div>
                <div class="form-group row">
                    <label class="col-md-3 control-label">{{'LABELS.BANNER_ALERT_SUBJECT' | translate}}</label>
                    <div class="col-md-9">
                        <input xssInputValidate="{{'LABELS.BANNER_ALERT_SUBJECT' | translate}}" class="form-control" type="text" formControlName="subject" id="subjectNameId">
                        <div *ngIf="submitted && inValidsubjectValue" class="alert alert-danger">
                            <div *ngIf="inValidsubjectValue">Subject cannot be empty</div>
                        </div>
                    </div>
                </div>


                <div class="form-group row">
                    <label class="col-md-3 control-label">
                    {{'LABELS.BANNER_ALERT_BODY' | translate}} </label>
                    <div class="col-md-9">
                        <textarea xssInputValidate="{{'LABELS.BANNER_ALERT_BODY' | translate}}" fieldType="html" class="form-control" formControlName="description" id="bodyNameId"
                            ></textarea>
                        <div *ngIf="submitted && inValidBodyValue" class="alert alert-danger">
                            <div *ngIf="inValidBodyValue">Body cannot be empty</div>
                        </div>
                    </div>
                </div>

                <!-- <div class="form-group row">
                    <label class="col-md-3 control-label"> Tenants To Be Shown</label>
                    <div class="col-md-9">
                        <select class="form-control select2" formControlName="branches" id="branchesSelectId" multiple>
                            <option *ngFor="let tenant of tenantDetails" value="{{tenant.id}}"> {{tenant.tenantName}}
                            </option>
                        </select>
                        <div *ngIf="submitted && inValidSelectedType" class="alert alert-danger">
                            <div *ngIf="inValidSelectedType">Tenants cannot be empty</div>
                        </div>
                    </div>
                </div> -->
                <div class="form-group row" id="select-site" [hidden]="!hideSiteSelection">
                    <label class="col-md-3 control-label">{{'LABELS.BANNER_ALERT_SITES' | translate}}*
                        <i chToolTip="site"></i>
                    </label>
                    <div class="col-md-9">
                        <app-select-sites [events]="eventsSubject.asObservable()" [hideApplyFilter]=true [filterType]="filterType" (hideDropdown)="hideDropdown($event)" [selectedSiteIds]="editSiteData" (siteIds)="getSiteIds($event)" [siteSelection]="true" [bannerAlert]=true [crossSite]=true  [dynamic]=true>
                        </app-select-sites>
                        <div *ngIf="submitted && invalidSite" class="alert alert-danger">
                            <div *ngIf="invalidSite">Sites cannot be empty</div>
                        </div>
                    </div>
                </div>


                <div class="form-group row">
                    <label class="col-md-3 control-label">
                        {{'LABELS.BANNER_ALERT_START_DATE' | translate}} </label>
                    <div class="col-md-9">
                        <div class='input-group date' id="start-date-picker">
                            <input readonly="readonly" id='start-date-picker-value' formControlName="startdate"
                                type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="fa fa-calendar"></span>
                            </span>
                        </div>
                        <div *ngIf="submitted && inValidstartDate" class="alert alert-danger">
                            <div *ngIf="inValidstartDate">Start Date cannot be empty</div>
                        </div>
                    </div>

                </div>
                <br>
                <div class="form-group row">
                    <label class="col-md-3 control-label">
                        {{'LABELS.BANNER_ALERT_END_DATE' | translate}} </label>
                    <div class="col-md-9">
                        <div class='input-group date' id='end-date-picker'>
                            <input readonly="readonly" id='end-date-picker-value' formControlName="enddate" type='text'
                                class="form-control" />
                            <span class="input-group-addon">
                                <span class="fa fa-calendar"></span>
                            </span>
                        </div>
                        <div *ngIf="submitted && inValidendDate" class="alert alert-danger">
                            <div *ngIf="inValidendDate">End Date cannot be empty</div>
                        </div>
                        <div *ngIf="inValidStartandEndDate == true" class="alert alert-danger">
                            <div *ngIf="inValidStartandEndDate == true">End Date cannot be less than Start Date</div>
                        </div>
                    </div>

                </div>
                            <div class="form-group row">
                                <label class="col-md-3 control-label">{{'LABELS.BANNER_ALERT_PUBLISH' | translate}}
                                    <!-- <i chToolTip="MSGES00001"></i> -->
                                </label>
                                <div class="col-md-9">
                                    <div class="btn-group col-md-2">
                                        <div class="ng-hide checkbox checkbox-primary">
                                            <input type="hidden" id="publish_id" name="publishName" formControlName="publishControlName" />
                            
                                        </div>
                            
                                        <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                            [ngClass]="{'active': publishEnable  }" (click)="publishChange(true)">
                                            Yes
                                        </button>
                                        <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                            [ngClass]="{'active': !publishEnable}" (click)="publishChange(false)">
                                            No
                                        </button>
                            
                                    </div>
                                </div>
                            
                            </div>
                <br>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary" (click)="updateManageAlerts()">Update</button>
                    <button type="button" (click)="resetMessageAlertForm()" class="btn btn-default">Cancel</button>
                </div>
            </div>
        </form>
    </div>
</section>