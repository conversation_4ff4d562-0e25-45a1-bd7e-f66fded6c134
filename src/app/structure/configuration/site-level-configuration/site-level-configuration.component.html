<div class="row">
  <div class="col-lg-12">
    <div class="mb-5">
      <table class="table table-hover" id="dtSiteLevelConfig" width="100%">
      </table>
    </div>
  </div>
</div>

<ng-template #siteLevelConfig>
  <div [formGroup]="siteConfigForm">
    <div class="modal-header">
      <h5 class="modal-title">{{ 'CONFIGURATION.SITE_LEVEL_CONFIGURATION.TITLE' | translate }} <a class="icmn-info"
          data-toggle="tooltip" data-placement="bottom"
          title="{{ 'CONFIGURATION.SITE_LEVEL_CONFIGURATION.SITE_LEVEL_CONFIG_INFO' | translate }}"></a>
      </h5>
      <button type="button" class="close" (click)="modalRef.close()" aria-label="Close">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <div class="form-group row d-flex align-items-center">
        <div class="col-sm-5">
          <label class="control-label">{{ 'CONFIGURATION.SITE_LEVEL_CONFIGURATION.SITE' | translate }} * <a
              class="icmn-info" data-toggle="tooltip" data-placement="bottom"
              title="{{ 'CONFIGURATION.SITE_LEVEL_CONFIGURATION.SITE_INFO' | translate }}"></a></label>
        </div>
        <div class="col-sm-6">
          <select class="form-control" formControlName="siteId" *ngIf="!editMode">
            <option [ngValue]="''">{{ 'CONFIGURATION.SITE_LEVEL_CONFIGURATION.SELECT' | translate }}</option>
            <option *ngFor="let site of (sitesFilteredList | async)" [ngValue]="site.id">
              {{site.name}}</option>
          </select>
          <input *ngIf="editMode" disabled class="form-control" type="text" formControlName="siteName">
        </div>
      </div>

      <div formGroupName="configurations">
        <div *ngFor="let label of configurationLabels">
          <div formArrayName="{{ label }}">
            <div *ngFor="let config of siteConfigForm.get('configurations').get(label).controls; let i = index"
              formGroupName="{{i}}">
              <div class="form-group row d-flex align-items-center">
                <div class="col-sm-5">
                  <label class="control-label">{{config.get('label').value}} *</label>
                </div>
                <ng-container *ngIf="config.get('range').value === 1; then range; else noRange">
                </ng-container>
                <ng-template #range>
                  <div class="col-sm-6">
                    <input *ngIf="i !== siteConfigForm.get('configurations').get(label).controls.length -1"
                      class="form-control" type="number" formControlName="to_value" min="1"
                      (ngModelChange)="updateLastRangeOnFormControlChange($event, config, i);">
                    <input *ngIf="i === siteConfigForm.get('configurations').get(label).controls.length -1"
                      class="form-control" type="text" formControlName="displayValue">
                    <div class="alert alert-danger"
                      *ngIf="config.get('to_value').hasError('min') && config.touched; else onlyNumbers">
                      {{ 'CONFIGURATION.SITE_LEVEL_CONFIGURATION.FIELD_NON_ZERO_ERROR' | translate: {label:
                      config.get('label').value} }}
                    </div>
                    <ng-template #onlyNumbers>
                      <div class="alert alert-danger"
                        *ngIf="config.get('to_value').hasError('pattern') && config.touched">
                        {{'CONFIGURATION.SITE_LEVEL_CONFIGURATION.FIELD_NUMERICS_ERROR' | translate}}
                      </div>
                    </ng-template>

                    <div class="alert alert-danger"
                      *ngIf="config.get('to_value').hasError('minValueError') && config.touched">
                      {{config.get('to_value').errors.message}}
                    </div>
                  </div>
                </ng-template>
                <ng-template #noRange>
                  <div class="col-sm-6">
                    <input class="form-control" type="number" formControlName="value" min="1">
                  </div>
                </ng-template>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button type="button" data-dismiss="modal" (click)="modalRef.close()"
        class="btn btn-sm btn-default float-left cancel" [translate]="'BUTTONS.CLOSE'"></button>
      <button type="button" (click)="saveSiteConfig()" class="btn btn-sm btn-info float-left"
        [disabled]="!siteConfigForm.valid" [translate]="'BUTTONS.SAVE'"></button>
    </div>
  </div>
</ng-template>