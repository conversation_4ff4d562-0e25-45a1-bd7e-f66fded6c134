import { cloneAbstractControl, groupByKey } from './../../../utils/utils';
import { ConfigurationResponse } from './../../../models/configuration/ConfigurationResponse';

import {
  SiteConfigurationResponse,
  SiteConfiguration,
} from './../../../models/configuration/SiteConfigurationResponse';

import { SitesResponse } from './../../../models/configuration/SitesResponse';

import { APIs } from 'app/constants/apis';
import { HttpService } from './../../../services/http/http.service';
import {
  Component,
  OnInit,
  OnDestroy,
  ChangeDetectionStrategy,
  ViewChild,
  TemplateRef,
  ChangeDetectorRef,
} from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormGroup,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import {
  NgbModal,
  NgbModalOptions,
  NgbModalRef,
} from '@ng-bootstrap/ng-bootstrap';
import { map } from 'rxjs/operators';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { StructureService } from 'app/structure/structure.service';

declare const $: any;

@Component({
  selector: 'app-site-level-configuration',
  templateUrl: './site-level-configuration.component.html',
  styleUrls: ['./site-level-configuration.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SiteLevelConfigurationComponent implements OnInit, OnDestroy {
  dtSiteLevelConfig;
  siteConfigForm: FormGroup;
  @ViewChild('siteLevelConfig') siteLevelConfigModalRef: TemplateRef<any>;
  selectedSiteConfigData: SiteConfiguration;
  columns = [];
  columnDefs = [];
  sitesFilteredList$;
  editMode = false;
  modalRef: NgbModalRef;
  modalOptions: NgbModalOptions = {
    backdrop: 'static',
    keyboard: false,
  };

  configurationData = [];
  configurationLabels = [];

  constructor(
    private fb: FormBuilder,
    private modalService: NgbModal,
    private httpService: HttpService,
    private cd: ChangeDetectorRef,
    private toolTipService: ToolTipService,
    private structureService: StructureService
  ) {}

  siteConfig = this.fb.group({
    id: '',
    siteId: [{ value: '', disabled: this.editMode }, Validators.required],
    siteName: '',
    configurations: this.fb.group({}),
  });

  ngOnInit() {
    this.siteConfigForm = cloneAbstractControl(this.siteConfig);
    this.getColumns();
  }

  get sitesFilteredList() {
    if (!this.sitesFilteredList$) {
      this.sitesFilteredList$ = this.httpService
        .doGet(APIs.getSitesEndpoint.replace(/{excludeSites}/g, 'true'))
        .pipe(
          map((res: SitesResponse) => {
            return res.content.sort((a, b) => {
              if (a.name > b.name) {
                return 1;
              }
              if (a.name < b.name) {
                return -1;
              }
              return 0;
            });
          })
        );
    }
    return this.sitesFilteredList$;
  }

  getColumns() {
    this.httpService
      .doGet(APIs.getConfigurationEndpoint)
      .subscribe((res: ConfigurationResponse) => {
        this.configurationData = res.content;
        this.createColumnsBasedOnConfig();
      });
  }

  createColumnsBasedOnConfig() {
    //Constant fields
    this.columns = [
      { title: '#' },
      {
        title: this.toolTipService.getTranslateData(
          'CONFIGURATION.SITE_LEVEL_CONFIGURATION.SITE'
        ),
      },
      {
        title: this.toolTipService.getTranslateData(
          'CONFIGURATION.SITE_LEVEL_CONFIGURATION.ACTIONS'
        ),
      },
    ];
    this.columnDefs = [
      {
        data: null,
        orderable: false,
        targets: 0,
        render: (data, type, row, meta) => {
          return meta.row + 1 + meta.settings._iDisplayStart;
        },
      },
      {
        data: null,
        orderable: true,
        targets: 1,
        render: (data, type, row) => {
          return row.siteName;
        },
      },
      {
        data: null,
        orderable: false,
        width: '20%',
        render: (data, type, row) => {
          const actions = `<a id="edit-site-config" href="javascript: void(0);" data-rowId ="${
            row.id
          }"  class="cat__core__link--underlined mr-3"><i id="edit" data-rowId ="${
            row.id
          }" class="icmn-pencil"></i>&nbsp;${this.toolTipService.getTranslateData(
            'BUTTONS.EDIT'
          )}</a>
          &nbsp;<a id="delete-site-config" href="javascript: void(0);" data-rowId ="${
            row.id
          }"  class="cat__core__link--underlined mr-3"><i id="delete" data-rowId ="${
            row.id
          }" class="fa fa-trash"></i>&nbsp;${this.toolTipService.getTranslateData(
            'BUTTONS.DELETE'
          )}</a>`;
          return actions;
        },
      },
    ];

    //Add Dynamic columns and Form control for each group
    const groupedData = groupByKey(this.configurationData, 'configType');
    const configurationGroupedData = [];
    Object.keys(groupedData).forEach((key) => {
      //Sort based on display order
      configurationGroupedData[key] = groupedData[key].sort((a, b) => {
        return a.displayOrder - b.displayOrder;
      });
      this.addDynamicFormControls(configurationGroupedData[key], key);
      this.addDynamicColumns(configurationGroupedData[key]);
    });
    this.initializeSiteConfigTable();
  }

  addDynamicFormControls(columns, name) {
    const configurations = this.siteConfig.get('configurations') as FormGroup;
    configurations.addControl(
      name,
      this.fb.array([], this.formRangeValidator(this.cd))
    );
    this.configurationLabels.push(name);
    const config = configurations.get(name) as FormArray;
    columns.forEach((item, index) => {
      let configGroup = {};
      if (item.range === 1) {
        configGroup = {
          configType: item.configType,
          displayOrder: item.displayOrder,
          range: item.range,
          label: item.label,
          code: item.value,
          from_value: '',
          displayValue: [{ value: '', disabled: columns.length - 1 === index }],
          to_value: [
            '',
            [
              Validators.required,
              Validators.min(1),
              Validators.pattern('^[0-9]*$'),
            ],
          ],
        };
      } else {
        configGroup = {
          configType: item.configType,
          displayOrder: item.displayOrder,
          range: item.range,
          label: item.label,
          value: ['', Validators.required],
        };
      }
      config.push(this.fb.group(configGroup));
      this.cd.markForCheck();
    });
    this.siteConfigForm = cloneAbstractControl(this.siteConfig);
  }

  addDynamicColumns(columns) {
    columns.forEach((item) => {
      this.columns.splice(this.columns.length - 1, 0, {
        title: `${item.label}`,
        type: item.configType,
      });
      this.columnDefs.splice(this.columnDefs.length - 1, 0, {
        data: null,
        orderable: false,
        render: (data, type, row) => {
          return row[item.label];
        },
      });
      this.columnDefs.map((column, index) => {
        column.targets = index;
        return column;
      });
    });
  }

  updateLastRangeOnFormControlChange(value, control, i) {
    if (control.parent.length - 2 === i) {
      control.parent
        .at(i + 1)
        .get('displayValue')
        .patchValue(
          value &&
            this.toolTipService.getTranslateDataWithParam(
              'CONFIGURATION.SITE_LEVEL_CONFIGURATION.RANGE_PLUS_DAYS',
              { range: parseInt(value) + 1 }
            )
        );
      this.cd.markForCheck();
    }
  }

  initializeSiteConfigTable() {
    this.dtSiteLevelConfig = $('#dtSiteLevelConfig').DataTable({
      columns: this.columns,
      lengthMenu: [
        [25, 50],
        [25, 50],
      ],
      processing: true,
      searching: true,
      serverSide: true,
      order: [[1, 'asc']],
      dom:
        "<'row'<'col-sm-4 'l>B<'col-sm-4'f><'col-sm-4 ml-0 siteSearchButton'>>" +
        "<'row'<'col-sm-12'tr>>" +
        "<'row'<'col-sm-5'i><'col-sm-7'p>>",
      buttons: [],
      initComplete: () => {
        $('#dtSiteLevelConfig_filter label input').attr(
          'placeholder',
          this.toolTipService.getTranslateData(
            'CONFIGURATION.SITE_LEVEL_CONFIGURATION.SEARCH_SITE'
          )
        );
        $('#dtSiteLevelConfig_filter label input').unbind();
        $('div#dtSiteLevelConfig_filter input').on('keydown', (e) => {
          if (e.which == 13) {
            let value = $('div#dtSiteLevelConfig_filter input').val();
            if (value) {
              value = value.replace('”', '"');
              value = value.replace('‘', "'");
              value = value.replace('’', "'");
              this.dtSiteLevelConfig.search(value).draw();
            } else {
              this.dtSiteLevelConfig.search('').draw();
            }
          }
        });
        $('div#dtSiteLevelConfig_filter input').on('keypress', () => {
          $('.searchSiteConfigView').prop('disabled', false);
        });
        $('div#dtSiteLevelConfig_filter input').on('keyup', () => {
          const value = $('div#dtSiteLevelConfig_filter input').val();
          if (!value) {
            $('.searchSiteConfigView').prop('disabled', true);
          }
        });
        $('.dataTables_filter label input').before(
          `&nbsp;<a class="icmn-info" data-toggle="tooltip" data-placement="bottom" title="${this.toolTipService.getTranslateData('TOOLTIPS.SEARCH_WITH_WILDCARD')}"></a>`
        );
        $('div.siteSearchButton').html(
          `<button disabled="true" class="btn btn-sm btn-info searchSiteConfigView" title="${this.toolTipService.getTranslateData(
            'BUTTONS.SEARCH'
          )}" type="submit">${this.toolTipService.getTranslateData(
            'BUTTONS.SEARCH'
          )}</button>
            <button style="margin-left:10px;" class="btn btn-sm btn-default resetSiteConfigView" title="${this.toolTipService.getTranslateData(
              'BUTTONS.RESET'
            )}" type="submit">${this.toolTipService.getTranslateData(
            'BUTTONS.RESET'
          )}</button>
            <button class="btn btn-sm btn-primary float-right mr-2" id="new-site-config" title="${this.toolTipService.getTranslateData(
              'BUTTONS.NEW'
            )}" type="submit">${this.toolTipService.getTranslateData(
            'BUTTONS.NEW'
          )}</button>`
        );
        const value = $('div#dtSiteLevelConfig_filter input').val();
        if (value) {
          $('.searchSiteConfigView').prop('disabled', false);
        }
        $('div#dtSiteLevelConfig_filter input').on('paste', () => {
          const value = $('div#dtSiteLevelConfig_filter input').val();
          setTimeout(() => {
            if (value) {
              $('.searchSiteConfigView').prop('disabled', false);
            }
          }, 100);
        });
      },
      ajax: (d, cb) => {
        this.httpService
          .doPost(APIs.getSiteLevelConfiguration, {
            currentPage: d.start / d.length,
            siteName: d.search.value ? d.search.value : '',
            rowsPerPage: d.length,
            sortBy: 'siteName',
            sortDirection: d.order[0].dir,
          })
          .subscribe(
            (res: SiteConfigurationResponse) => {
              res.content.forEach((data) => {
                this.columns.forEach((column) => {
                  if (column.type) {
                    data[column.title] = '';
                  }
                });
                const configMeta = JSON.parse(data.configMeta);
                Object.keys(configMeta).forEach((key) => {
                  configMeta[key].forEach((item, i) => {
                    if (item.range === 1) {
                      data[item.label] =
                        configMeta[key].length - 1 !== i
                          ? item.to_value
                          : item.from_value;
                    } else {
                      data[item.label] = item.value;
                    }
                  });
                });
              });
              cb({
                recordsTotal: res.page.totalElements,
                recordsFiltered: res.page.totalElements,
                data: res.content,
              });
            },
            (error) => {
              if (error.status === 404) {
                cb({
                  recordsTotal: 0,
                  recordsFiltered: 0,
                  data: [],
                });
              }
            }
          );
      },
      fnRowCallback: (nRow, aData) => {
        $(nRow).on('click', () => {
          this.selectedSiteConfigData = aData;
        });
      },
      columnDefs: this.columnDefs,
    });

    $(document).on('click', '#new-site-config', () => {
      this.editMode = false;
      this.siteConfigForm = cloneAbstractControl(this.siteConfig);
      this.modalRef = this.modalService.open(this.siteLevelConfigModalRef, {
        windowClass: 'site-config-class',
        ...this.modalOptions,
      });
    });

    $(document).on('click', '#edit-site-config', () => {
      this.editMode = true;
      const configMeta = JSON.parse(this.selectedSiteConfigData.configMeta);
      const configurations = this.siteConfigForm.get(
        'configurations'
      ) as FormGroup;
      this.siteConfigForm.patchValue({
        id: this.selectedSiteConfigData.id,
        siteId: this.selectedSiteConfigData.siteId,
        siteName: this.selectedSiteConfigData.siteName,
      });
      configMeta.colors = configMeta.colors.map((color) => {
        color.displayValue = this.toolTipService.getTranslateDataWithParam(
          'CONFIGURATION.SITE_LEVEL_CONFIGURATION.RANGE_PLUS_DAYS',
          { range: color.from_value }
        );
        return color;
      });
      configurations.patchValue(configMeta);
      this.modalRef = this.modalService.open(this.siteLevelConfigModalRef, {
        windowClass: 'site-config-class',
        ...this.modalOptions,
      });
      this.cd.markForCheck();
    });

    $(document).on('click', '#delete-site-config', () => {
      this.deleteSiteConfig();
    });

    $(document).on('click', '.resetSiteConfigView', () => {
      this.dtSiteLevelConfig.search('').draw();
      $('.searchSiteConfigView').prop('disabled', true);
    });

    $(document).on('click', '.searchSiteConfigView', () => {
      let value = $('div#dtSiteLevelConfig_filter input').val();
      if (value) {
        value = value.replace('”', '"');
        value = value.replace('‘', "'");
        value = value.replace('’', "'");
        this.dtSiteLevelConfig.search(value).draw();
      } else {
        this.dtSiteLevelConfig.search('').draw();
      }
    });
  }

  deleteSiteConfig() {
    this.structureService
      .showAlertMessagePopup({
        text: this.toolTipService.getTranslateData(
          'CONFIGURATION.SITE_LEVEL_CONFIGURATION.DELETE_SITE_CONFIG_CONFORMATION_MESSAGE'
        ),
      })
      .then((confirm) => {
        if (confirm) {
          this.httpService
            .doDelete(
              APIs.deleteSiteConfigurationEndpoint.replace(
                /{referralConfigId}/g,
                this.selectedSiteConfigData.id
              )
            )
            .subscribe(() => {
              this.dtSiteLevelConfig.ajax.reload(null, false);
            });
        }
      });
  }

  saveSiteConfig() {
    if (!this.siteConfigForm.valid) return;
    const siteConfigData = this.siteConfigForm.getRawValue();
    const colorMeta = siteConfigData.configurations.colors;
    siteConfigData.configurations.colors = colorMeta.map((color, i) => {
      if (i === 0) color.from_value = -50000;
      else color.from_value = parseInt(colorMeta[i - 1].to_value) + 1;
      if (i === colorMeta.length - 1) color.to_value = 50000;
      color.from_value = parseInt(color.from_value);
      color.to_value = parseInt(color.to_value);
      return color;
    });

    siteConfigData.configMeta = JSON.stringify(siteConfigData.configurations);
    let subscriber;
    if (this.editMode) {
      subscriber = this.httpService.doPut(
        APIs.postSiteLevelConfigurationEndpoint,
        siteConfigData
      );
    } else {
      subscriber = this.httpService.doPost(
        APIs.postSiteLevelConfigurationEndpoint,
        siteConfigData
      );
    }
    subscriber.subscribe(() => {
      this.modalRef.close();
      this.dtSiteLevelConfig.ajax.reload(null, false);
    });
  }

  formRangeValidator(cd: ChangeDetectorRef): ValidatorFn {
    return (formArray: FormArray): { [key: string]: null } | null => {
      const filteredForms = formArray.controls.filter(
        (fg: FormGroup) => fg.get('range').value === 1
      );
      filteredForms.forEach((fg: FormGroup, index) => {
        if (index !== 0) {
          const prevValue = parseInt(
            formArray.at(index - 1).get('to_value').value || 0
          );
          const currentValue = parseInt(fg.get('to_value').value || 0);
          if (
            index !== filteredForms.length - 1 &&
            !(currentValue && prevValue && currentValue > prevValue)
          ) {
            fg.get('to_value').setErrors({
              minValueError: true,
              message: this.toolTipService.getTranslateDataWithParam(
                'CONFIGURATION.SITE_LEVEL_CONFIGURATION.FIELD_MAX_VALUE_ERROR',
                {
                  current: fg.get('label').value,
                  previous: formArray.at(index - 1).get('label').value,
                }
              ),
            });
          } else {
            fg.get('to_value').setErrors(null);
          }
          cd.markForCheck();
        }
      });
      return null;
    };
  }

  ngOnDestroy() {
    $(document).off('click', '#new-site-config');
    $(document).off('click', '#edit-site-config');
    $(document).off('click', '#delete-site-config');
    $(document).off('click', '.resetSiteConfigView');
    $(document).off('click', '.searchSiteConfigView');
    if (this.dtSiteLevelConfig) {
      this.dtSiteLevelConfig.destroy();
    }
  }
}
