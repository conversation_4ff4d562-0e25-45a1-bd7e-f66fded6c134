import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { SiteLevelConfigurationComponent } from './site-level-configuration.component';

describe('SiteLevelConfigurationComponent', () => {
  let component: SiteLevelConfigurationComponent;
  let fixture: ComponentFixture<SiteLevelConfigurationComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [SiteLevelConfigurationComponent],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SiteLevelConfigurationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
