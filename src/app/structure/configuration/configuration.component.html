<section class="card">
  <div class="card-header cat__core__title">
    <strong [translate]="'CONFIGURATION.REFILL_DASHBOARD_CONFIGURATION'"></strong>
  </div>
  <div class="card-block">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a [routerLink]="['/inbox']" [translate]="'MENU.INBOX_AS_HOME'"></a></li>
      <li class="breadcrumb-item"><a [translate]="'LABELS.APP_CENTER_SETTINGS'"></a></li>
      <li class="breadcrumb-item" [translate]="'CONFIGURATION.REFILL_DASHBOARD_CONFIGURATION'"></li>
    </ol>
    <div class="nav-tabs-horizontal">
      <ul class="nav nav-tabs" role="tablist">
        <li class="nav-item"><a class="nav-link active" id="formMapping" href="javascript: void(0);" data-toggle="tab"
            data-target="#formMappingContent" role="tab" aria-expanded="true">
            <i class="fa fa-files-o" aria-hidden="true"></i>
            <span [translate]="'CONFIGURATION.FORM_MAPPING.TITLE'"></span></a></li>
        <li class="nav-item"><a class="nav-link" id="siteLevelConfiguration" href="javascript: void(0);" data-toggle="tab"
            data-target="#siteLevelConfigurationContent" role="tab" aria-expanded="true">
            <i class="fa fa-cog" aria-hidden="true"></i>
            <span [translate]="'CONFIGURATION.SITE_LEVEL_CONFIGURATION.TITLE'"></span></a></li>
      </ul>
      <div class="row">
        <div class="col-lg-12">
          <div class="mt-4">
            <div class="tab-content">
              <div class="tab-pane active" id="formMappingContent" role="tabcard">
                <app-form-mapping *ngIf="activeTab == 'formMapping'"></app-form-mapping>
              </div>
              <div class="tab-pane" id="siteLevelConfigurationContent" role="tabcard">
                <app-site-level-configuration *ngIf="activeTab == 'siteLevelConfiguration'"></app-site-level-configuration>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>