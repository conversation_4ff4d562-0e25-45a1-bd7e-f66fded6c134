<div class="row">
    <div class="col-lg-12">
        <div class="mb-5">
            <table class="table table-hover" id="dtFormMapping" width="100%">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>{{ 'CONFIGURATION.FORM_MAPPING.CLASSIC_FORM' | translate }}</th>
                        <th>{{ 'CONFIGURATION.FORM_MAPPING.WORKLIST_FORM' | translate }}</th>
                        <th>{{ 'ACTION.TITLE' | translate }}</th>
                    </tr>
                </thead>
            </table>
        </div>
    </div>
</div>

<ng-template #formsMapping>
    <div class="modal-header">
        <h5 class="modal-title">{{'CONFIGURATION.FORM_MAPPING.SELECT_FORM' | translate }}</h5>
        <button type="button" class="close" (click)="modalRef.close();" data-dismiss="modal"
            attr.aria-label="{{'BUTTONS.CLOSE' | translate}}">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body" [formGroup]="formMappingForm">
        <div class="form-group row d-flex align-items-center">
            <div class="col-sm-4">
                <label class="control-label">{{'CONFIGURATION.FORM_MAPPING.CLASSIC_FORM' | translate }}&nbsp;*&nbsp;<a
                        class="icmn-info" data-toggle="tooltip"
                        title="{{'CONFIGURATION.FORM_MAPPING.CLASSIC_FORM_INFO' | translate }}"></a></label>
            </div>
            <div class="col-sm-6">
                <select class="form-control" formControlName="classicalFormId">
                    <option [ngValue]="''">{{'ACTION.SELECT' | translate}}</option>
                    <option *ngFor="let classicForm of (classicForms | async)" [ngValue]="classicForm.key">
                        {{classicForm.value}}</option>
                </select>
            </div>
        </div>
        <div class="form-group row d-flex align-items-center">
            <div class="col-sm-4">
                <label class="control-label">{{'CONFIGURATION.FORM_MAPPING.WORKLIST_FORM' | translate }}&nbsp;*&nbsp;<a
                        class="icmn-info" data-toggle="tooltip"
                        title="{{'CONFIGURATION.FORM_MAPPING.WORKLIST_FORM_INFO' | translate }}"></a></label>
            </div>
            <div class="col-sm-6">
                <select class="form-control" formControlName="ngwFormId">
                    <option [ngValue]="''">{{'ACTION.SELECT' | translate}}</option>
                    <option *ngFor="let workListForm of (workListForms | async)" [ngValue]="workListForm.key">
                        {{workListForm.value}}</option>
                </select>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button type="button" data-dismiss="modal" (click)="modalRef.close();" class="btn btn-sm btn-default float-left"
            [translate]="'BUTTONS.CLOSE'"></button>
        <button type="button" (click)="saveFormMapping()" class="btn btn-sm btn-info float-left"
            [disabled]="!formMappingForm.valid" [translate]="'BUTTONS.SAVE'"></button>
    </div>
</ng-template>

<ng-template #formFieldsMapping>
    <div class="modal-header">
        <h5 class="modal-title">{{ 'CONFIGURATION.FIELD_MAPPING.TITLE' | translate }}&nbsp;<a class="icmn-info"
                data-toggle="tooltip" data-placement="bottom" title="{{'CONFIGURATION.FIELD_MAPPING.FIELD_MAPPING_INFO' | translate }}"></a>
        </h5>
        <button type="button" class="close" (click)="closeFormFieldsMapping()" data-dismiss="modal"
            attr.aria-label="{{'BUTTONS.CLOSE' | translate}}">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
    <div class="modal-body" [formGroup]="formFieldsMappingForm">
        <table>
            <thead>
                <tr class="d-flex">
                    <th>{{'CONFIGURATION.FORM_MAPPING.CLASSIC_FORM' | translate}}:
                        {{selectedFormMappingData.classicalFormName}}</th>
                    <th>{{'CONFIGURATION.FORM_MAPPING.WORKLIST_FORM' | translate}}:
                        {{selectedFormMappingData.ngwFormName}}</th>
                    <th></th>
                </tr>
            </thead>
            <tbody formArrayName="formFields">
                <tr *ngFor="let mapping of formFields.controls; let i=index;let last = last;" class="d-flex">
                    <ng-container [formGroup]="mapping">
                        <td>
                            <select class="form-control" formControlName="classicalFormFieldId"
                                (ngModelChange)="onFormElementSelected(i, 'classicalFormFieldType', $event)">
                                <option [ngValue]="''">{{'ACTION.SELECT' | translate}}</option>
                                <option *ngFor="let formElement of classicFormElementsData"
                                    [ngValue]="formElement.elementId">{{formElement.elementTitle}}</option>
                            </select>
                            <div *ngIf="mapping.hasError('notSame')" class="alert alert-danger">
                                {{'CONFIGURATION.FIELD_MAPPING.FIELD_TYPE_MAPPING_ERROR' | translate}}
                            </div>
                        </td>
                        <td>
                            <select class="form-control" formControlName="ngwFormFieldId"
                                (ngModelChange)="onFormElementSelected(i, 'ngwFormFieldType', $event)">
                                <option [ngValue]="''">{{'ACTION.SELECT' | translate}}</option>
                                <option *ngFor="let formElement of workListFormElementsData"
                                    [ngValue]="formElement.elementId">{{formElement.elementTitle}}</option>
                            </select>
                            <div *ngIf="mapping.get('ngwFormFieldId').hasError('duplicate')" class="alert alert-danger">
                                {{'CONFIGURATION.FIELD_MAPPING.FIELD_TYPE_UNIQUE_ERROR' | translate}}
                            </div>
                            <div *ngIf="mapping.hasError('notSame')" class="alert alert-danger">
                                {{'CONFIGURATION.FIELD_MAPPING.FIELD_TYPE_MAPPING_ERROR' | translate}}
                            </div>
                        </td>
                        <td>
                            <a class="banner-alert-table-delete pull-right btn btn-sm"
                                title="{{'BUTTONS.DELETE' | translate}}" href="javascript: void(0);"
                                (click)="deleteFormFieldMapping(i, mapping.get('id').value)" id="deleteFieldsMapping"><i
                                    id="deleteFieldsMapping-icon" class="fa fa-trash" aria-hidden="true"></i></a>
                        </td>
                    </ng-container>
                </tr>
                <tr>
                    <td colspan="3">
                        <a class="banner-alert-table-delete btn btn-sm" title="{{'BUTTONS.ADD' | translate}}"
                            href="javascript: void(0);" (click)="addFormFieldsMapping()" id="addFieldsMapping"><i
                                id="addFieldsMapping-icon" class="fa fa-plus" aria-hidden="true"></i></a>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="modal-footer">
        <button type="button" (click)="closeFormFieldsMapping()" data-dismiss="modal"
            class="btn btn-sm btn-default float-left" [translate]="'BUTTONS.CLOSE'"></button>
        <button type="button" (click)="saveFormFieldsMapping()" class="btn btn-sm btn-info float-left"
            [disabled]="!formFieldsMappingForm.valid" [translate]="'BUTTONS.SAVE'"></button>
    </div>
</ng-template>