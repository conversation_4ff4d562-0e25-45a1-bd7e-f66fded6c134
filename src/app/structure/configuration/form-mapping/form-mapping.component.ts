import { FormsResponse } from './../../../models/configuration/FormsResponse';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from './../../tool-tip.service';
import { isBlank } from './../../../utils/utils';
import { FormFieldsMappingResponse } from './../../../models/configuration/FormFieldsMappingResponse';
import {
  FormField,
  FormFieldsResponse,
} from './../../../models/configuration/FormFieldsResponse';
import { APIs } from 'app/constants/apis';
import {
  FormMapping,
  FormMappingResponse,
} from './../../../models/configuration/FormMappingResponse';
import { HttpService } from './../../../services/http/http.service';
import {
  Component,
  OnInit,
  ChangeDetectionStrategy,
  ViewChild,
  TemplateRef,
  <PERSON><PERSON><PERSON>roy,
} from '@angular/core';
import {
  FormArray,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import {
  NgbModal,
  NgbModalOptions,
  NgbModalRef,
} from '@ng-bootstrap/ng-bootstrap';
import { map } from 'rxjs/operators';

declare const $: any;

@Component({
  selector: 'app-form-mapping',
  templateUrl: './form-mapping.component.html',
  styleUrls: ['./form-mapping.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class FormMappingComponent implements OnInit, OnDestroy {
  dtFormMapping;
  formMappingForm: FormGroup;
  formFieldsMappingForm: FormGroup;
  @ViewChild('formsMapping') formsMappingModalRef: TemplateRef<any>;
  @ViewChild('formFieldsMapping') formFieldsMappingModalRef: TemplateRef<any>;
  modalRef: NgbModalRef;
  modalOptions: NgbModalOptions = {
    backdrop: 'static',
    keyboard: false,
  };
  classicForms$;
  workListForms$;
  classicFormElementsData: FormField[];
  workListFormElementsData: FormField[];
  selectedFormMappingData: FormMapping;
  duplicateFormElements = [];

  constructor(
    private fb: FormBuilder,
    private modalService: NgbModal,
    private httpService: HttpService,
    private toolTipService: ToolTipService,
    private structureService: StructureService
  ) {}

  ngOnInit() {
    this.formMappingForm = this.formMapping;
    this.formFieldsMappingForm = this.fb.group({
      formFields: this.fb.array(
        [this.formFieldsMappingGroup],
        this.noDuplicates('ngwFormFieldId')
      ),
    });
    this.initializeFormMappingTable();
  }

  get formMapping(): FormGroup {
    return this.fb.group({
      id: '',
      ngwFormId: ['', Validators.required],
      classicalFormId: ['', Validators.required],
    });
  }

  get formFieldsMappingGroup(): FormGroup {
    const group = this.fb.group({
      id: '',
      ngwFormFieldId: ['', Validators.required],
      ngwFormFieldType: '',
      classicalFormFieldId: ['', [Validators.required]],
      classicalFormFieldType: '',
    });
    group.setValidators(this.sameTypes);
    return group;
  }

  get formFields(): FormArray {
    return this.formFieldsMappingForm.get('formFields') as FormArray;
  }

  sameTypes: ValidatorFn = (group: FormGroup): ValidationErrors | null => {
    let ngwFormFieldType = group.get('ngwFormFieldType').value;
    let classicFormFieldType = group.get('classicalFormFieldType').value;
    if (isBlank(ngwFormFieldType)) {
      const ngwFormFieldId = group.get('ngwFormFieldId').value;
      ngwFormFieldType = !isBlank(ngwFormFieldId)
        ? this.getFieldTypeById(ngwFormFieldId, 'ngw')
        : '';
    }
    if (isBlank(classicFormFieldType)) {
      const classicalFormFieldId = group.get('classicalFormFieldId').value;
      classicFormFieldType = !isBlank(classicalFormFieldId)
        ? this.getFieldTypeById(classicalFormFieldId, 'classic')
        : '';
    }
    return !isBlank(ngwFormFieldType) &&
      !isBlank(classicFormFieldType) &&
      ngwFormFieldType !== classicFormFieldType
      ? { notSame: true }
      : null;
  };

  noDuplicates(controlName: string): ValidatorFn {
    return (formArray: FormArray): { [key: string]: any } | null => {
      if (this.duplicateFormElements) {
        for (let i = 0; i < this.duplicateFormElements.length; i++) {
          const errors =
            (this.formFields.at(this.duplicateFormElements[i]).get(controlName)
              .errors as any) || {};
          delete errors['duplicate'];
          this.formFields
            .at(this.duplicateFormElements[i])
            .get(controlName)
            .setErrors(isBlank(errors) ? null : (errors as ValidationErrors));
        }
      }

      const dict = {};
      formArray.value.forEach((item, index) => {
        if (!isBlank(item[controlName])) {
          dict[item[controlName]] = dict[item[controlName]] || [];
          dict[item[controlName]].push(index);
        }
      });
      let duplicates = [];
      for (const key in dict) {
        if (dict[key].length > 1) duplicates = duplicates.concat(dict[key]);
      }
      this.duplicateFormElements = duplicates;

      for (const index of duplicates) {
        formArray.at(+index).get(controlName).setErrors({ duplicate: true });
      }

      return null;
    };
  }

  get classicForms() {
    if (!this.classicForms$) {
      this.classicForms$ = this.httpService
        .doGet(APIs.getClassicFormsListEndpoint)
        .pipe(map((res: FormsResponse) => res.content));
    }

    return this.classicForms$;
  }

  get workListForms() {
    if (!this.workListForms$) {
      this.workListForms$ = this.httpService
        .doGet(APIs.getWorkListFormsListEndpoint)
        .pipe(map((res: FormsResponse) => res.content));
    }

    return this.workListForms$;
  }

  getFormFields(formIds: number[]) {
    this.httpService
      .doGet(
        APIs.getFormFieldsListEndpoint.replace(/{formIds}/g, formIds.toString())
      )
      .subscribe(
        (res: FormFieldsResponse[]) => {
          const classicFormFields = res.find(
            (item) =>
              item.formId === this.selectedFormMappingData.classicalFormId
          );
          this.classicFormElementsData = !isBlank(classicFormFields)
            ? classicFormFields.formFields.sort((a, b) =>
                a.elementTitle.localeCompare(b.elementTitle)
              )
            : [];
          const worklistFormFields = res.find(
            (item) => item.formId === this.selectedFormMappingData.ngwFormId
          );
          this.workListFormElementsData = !isBlank(worklistFormFields)
            ? worklistFormFields.formFields.sort((a, b) =>
                a.elementTitle.localeCompare(b.elementTitle)
              )
            : [];
        },
        () => {
          this.classicFormElementsData = [];
          this.workListFormElementsData = [];
        }
      );
  }

  getFieldTypeById(id: number, type: string) {
    const formFields =
      type === 'classic'
        ? this.classicFormElementsData
        : this.workListFormElementsData;
    return !isBlank(formFields)
      ? formFields.find((field) => field.elementId === id).elementType
      : '';
  }

  initializeFormMappingTable() {
    this.dtFormMapping = $('#dtFormMapping').DataTable({
      lengthMenu: [
        [25, 50],
        [25, 50],
      ],
      responsive: true,
      processing: true,
      searching: true,
      serverSide: true,
      order: [[1, 'desc']],
      dom:
        "<'row'<'col-sm-4 'l>B<'col-sm-4'f><'col-sm-4 ml-0 searchButton'>>" +
        "<'row'<'col-sm-12'tr>>" +
        "<'row'<'col-sm-5'i><'col-sm-7'p>>",
      buttons: [],
      language: {
        emptyTable: this.toolTipService.getTranslateData(
          'INFO_MESSAGES.NO_DATA_AVAILABLE'
        ),
      },
      initComplete: () => {
        $('.dataTables_filter label input').attr(
          'placeholder',
          this.toolTipService.getTranslateData('BUTTONS.SEARCH')
        );
        $('.dataTables_filter label input').unbind();
        $('div.dataTables_filter input').on('keydown', (e) => {
          if (e.which == 13) {
            let value = $('div.dataTables_filter input').val();
            if (value) {
              value = value.replace('”', '"');
              value = value.replace('‘', "'");
              value = value.replace('’', "'");
              this.dtFormMapping.search(value).draw();
            } else {
              this.dtFormMapping.search('').draw();
            }
          }
        });
        $('div.dataTables_filter input').on('keypress', () => {
          $('.searchBView').prop('disabled', false);
        });
        $('div.dataTables_filter input').on('keyup', () => {
          const value = $('div.dataTables_filter input').val();
          if (!value) {
            $('.searchBView').prop('disabled', true);
          }
        });
        $('.dataTables_filter label input').before(
          `&nbsp;<a class="icmn-info" data-toggle="tooltip" data-placement="bottom" title="${this.toolTipService.getTranslateData('TOOLTIPS.SEARCH_WITH_WILDCARD')}"></a>`
        );
        $('div.searchButton').html(
          `<button disabled="true" class="btn btn-sm btn-info searchBView" title="${this.toolTipService.getTranslateData(
            'BUTTONS.SEARCH'
          )}" type="submit">${this.toolTipService.getTranslateData(
            'BUTTONS.SEARCH'
          )}</button>
            <button style="margin-left:10px;" class="btn btn-sm btn-default resetBView" title="${this.toolTipService.getTranslateData(
              'BUTTONS.RESET'
            )}" type="submit">${this.toolTipService.getTranslateData(
            'BUTTONS.RESET'
          )}</button>
            <button id="new-form-mapping" class="btn btn-sm btn-primary newBView float-right mr-2" title="${this.toolTipService.getTranslateData(
              'BUTTONS.NEW'
            )}" type="submit">${this.toolTipService.getTranslateData(
            'BUTTONS.NEW'
          )}</button>`
        );
        const value = $('div.dataTables_filter input').val();
        if (value) {
          $('.searchBView').prop('disabled', false);
        }
        $('div.dataTables_filter input').on('paste', () => {
          const value = $('div.dataTables_filter input').val();
          setTimeout(() => {
            if (value) {
              $('.searchBView').prop('disabled', false);
            }
          }, 100);
        });
      },
      ajax: (d, cb) => {
        this.httpService
          .doPost(APIs.postFormMappingSearchEndpoint, {
            currentPage: d.start / d.length,
            formName: d.search.value ? d.search.value : '',
            rowsPerPage: d.length,
            sortBy:
              d.order[0].column === 2 ? 'ngwFormName' : 'classicalFormName',
            sortDirection: d.order[0].dir,
          })
          .subscribe(
            (res: FormMappingResponse) => {
              cb({
                recordsTotal: res.page.totalElements,
                recordsFiltered: res.page.totalElements,
                data: res.content,
              });
            },
            (error) => {
              if (error.status === 404) {
                cb({
                  recordsTotal: 0,
                  recordsFiltered: 0,
                  data: [],
                });
              }
            }
          );
      },
      fnRowCallback: (nRow, aData) => {
        $(nRow).on('click', () => {
          this.selectedFormMappingData = aData;
        });
      },
      columnDefs: [
        {
          data: null,
          orderable: false,
          targets: 0,
          render: (data, type, row, meta) => {
            return meta.row + 1 + meta.settings._iDisplayStart;
          },
        },
        {
          data: null,
          orderable: true,
          targets: 1,
          render: (data, type, row) => {
            return row.classicalFormName;
          },
        },
        {
          data: null,
          orderable: true,
          targets: 2,
          render: (data, type, row) => {
            return row.ngwFormName;
          },
        },
        {
          data: null,
          orderable: false,
          targets: 3,
          width: '30%',
          render: (data, type, row) => {
            const actions = `<a id="edit-form-mapping" href="javascript: void(0);" data-rowId ="${
              row.id
            }"  class="cat__core__link--underlined mr-3"><i id="edit" data-rowId ="${
              row.id
            }" class="icmn-pencil"></i>&nbsp;${this.toolTipService.getTranslateData(
              'BUTTONS.EDIT'
            )}</a>
            &nbsp;<a id="delete-form-mapping" href="javascript: void(0);" data-rowId ="${
              row.id
            }"  class="cat__core__link--underlined mr-3"><i id="delete" data-rowId ="${
              row.id
            }" class="fa fa-trash"></i>&nbsp;${this.toolTipService.getTranslateData(
              'BUTTONS.DELETE'
            )}</a>
            &nbsp;<a id="edit-form-fields-mapping" href="javascript: void(0);" data-rowId ="${
              row.id
            }"  class="cat__core__link--underlined mr-3"><i id="field-mapping" data-rowId ="${
              row.id
            }" class="fa fa-file-text-o"></i>&nbsp;${this.toolTipService.getTranslateData(
              'BUTTONS.FIELD_MAPPING'
            )}</a>`;
            return actions;
          },
        },
      ],
    });

    $(document).on('click', '#new-form-mapping', () => {
      this.formMappingForm.reset(this.formMapping.value);
      this.modalRef = this.modalService.open(
        this.formsMappingModalRef,
        this.modalOptions
      );
    });

    $(document).on('click', '#edit-form-mapping', () => {
      this.formMappingForm.patchValue({
        id: this.selectedFormMappingData.id,
        ngwFormId: this.selectedFormMappingData.ngwFormId,
        classicalFormId: this.selectedFormMappingData.classicalFormId,
      });
      this.modalRef = this.modalService.open(
        this.formsMappingModalRef,
        this.modalOptions
      );
    });

    $(document).on('click', '#delete-form-mapping', () => {
      this.deleteFormMapping();
    });

    $(document).on('click', '#edit-form-fields-mapping', () => {
      this.getFormFieldsMapping(this.selectedFormMappingData.id);
      this.getFormFields([
        this.selectedFormMappingData.classicalFormId,
        this.selectedFormMappingData.ngwFormId,
      ]);
    });

    $(document).on('click', '.resetBView', () => {
      this.dtFormMapping.search('').draw();
      $('.searchBView').prop('disabled', true);
    });

    $(document).on('click', '.searchBView', () => {
      let value = $('div.dataTables_filter input').val();
      if (value) {
        value = value.replace('”', '"');
        value = value.replace('‘', "'");
        value = value.replace('’', "'");
        this.dtFormMapping.search(value).draw();
      } else {
        this.dtFormMapping.search('').draw();
      }
    });
  }

  saveFormMapping() {
    if (!this.formMappingForm.valid) return;
    this.modalRef.close();
    if (isBlank(this.formMappingForm.value.id)) {
      this.httpService
        .doPost(APIs.formMappingEndpoint, this.formMappingForm.value)
        .subscribe(() => {
          this.dtFormMapping.ajax.reload(null, false);
        });
    } else {
      this.httpService
        .doPut(APIs.formMappingEndpoint, this.formMappingForm.value)
        .subscribe(() => {
          this.dtFormMapping.ajax.reload(null, false);
        });
    }
  }

  deleteFormMapping() {
    this.structureService
      .showAlertMessagePopup({
        text: this.toolTipService.getTranslateData(
          'CONFIGURATION.FORM_MAPPING.DELETE_FORM_MAPPING'
        ),
      })
      .then((confirm) => {
        if (confirm) {
          this.httpService
            .doDelete(
              APIs.deleteFormMappingEndpoint.replace(
                /{formMappingId}/g,
                this.selectedFormMappingData.id
              )
            )
            .subscribe(() => {
              this.dtFormMapping.ajax.reload(null, false);
            });
        }
      });
  }

  getFormFieldsMapping(formMappingId: string) {
    this.httpService
      .doGet(
        APIs.formFieldsMappingEndpoint.replace(
          /{formMappingId}/g,
          formMappingId
        )
      )
      .subscribe((res: FormFieldsMappingResponse[]) => {
        if (res.length > 0) {
          if (res.length > this.formFields.length) {
            for (let i = this.formFields.length; i < res.length; i++) {
              this.formFields.push(this.formFieldsMappingGroup);
            }
          }
          this.formFields.patchValue(res);
        } else if (this.formFields.length === 0) {
          this.formFields.push(this.formFieldsMappingGroup);
        }
        this.modalRef = this.modalService.open(this.formFieldsMappingModalRef, {
          windowClass: 'fields-mapping-class',
          size: 'lg',
          ...this.modalOptions,
        });
      });
  }

  addFormFieldsMapping() {
    this.formFields.push(this.formFieldsMappingGroup);
  }

  onFormElementSelected(i: number, elementType: string, event: number) {
    let formElement: FormField;
    if (elementType === 'ngwFormFieldType') {
      formElement = this.workListFormElementsData.find(
        (element) => element.elementId === event
      );
    } else {
      formElement = this.classicFormElementsData.find(
        (element) => element.elementId === event
      );
    }
    this.formFields
      .get(`${i}.${elementType}`)
      .setValue(formElement ? formElement.elementType : '');
  }

  saveFormFieldsMapping() {
    if (!this.formFieldsMappingForm.valid) return;
    if (this.formFields.value.length === 0) {
      this.closeFormFieldsMapping();
      return;
    }
    this.httpService
      .doPut(
        APIs.formFieldsMappingEndpoint.replace(
          /{formMappingId}/g,
          this.selectedFormMappingData.id
        ),
        this.formFields.value
      )
      .subscribe(() => {
        this.closeFormFieldsMapping();
      });
  }

  deleteFormFieldMapping(index: number, id: string) {
    if (!isBlank(id)) {
      this.structureService
        .showAlertMessagePopup({
          text: this.toolTipService.getTranslateData(
            'CONFIGURATION.FORM_MAPPING.DELETE_FIELD_MAPPING'
          ),
        })
        .then((confirm) => {
          if (confirm) {
            this.httpService
              .doDelete(
                APIs.deleteFormFieldsMappingEndpoint.replace(
                  /{formFieldMappingId}/g,
                  id
                )
              )
              .subscribe(() => {
                this.formFields.removeAt(index);
              });
          }
        });
    } else {
      this.formFields.removeAt(index);
    }
  }

  closeFormFieldsMapping() {
    this.modalRef.close();
    while (this.formFields.length > 1) {
      this.formFields.removeAt(0);
    }
    this.formFields.patchValue([this.formFieldsMappingGroup.value]);
  }

  ngOnDestroy() {
    $(document).off('click', '#new-form-mapping');
    $(document).off('click', '#edit-form-mapping');
    $(document).off('click', '#delete-form-mapping');
    $(document).off('click', '#edit-form-fields-mapping');
    $(document).off('click', '.resetBView');
    $(document).off('click', '.searchBView');
    if (this.dtFormMapping) {
      this.dtFormMapping.destroy();
    }
  }
}
