import { HttpService } from './../../services/http/http.service';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConfigurationComponent } from './configuration.component';
import { AuthGuard } from 'app/guard/auth.guard';
import { RouterModule, Routes } from '@angular/router';
import { FormMappingComponent } from './form-mapping/form-mapping.component';
import { SharedModule } from '../shared/sharedModule';
import { ReactiveFormsModule } from '@angular/forms';
import { SiteLevelConfigurationComponent } from './site-level-configuration/site-level-configuration.component';

export const routes: Routes = [
  {
    path: 'app-center/configuration',
    component: ConfigurationComponent,
    canActivate: [AuthGuard],
  },
];

@NgModule({
  providers: [HttpService],
  imports: [
    CommonModule,
    SharedModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
  ],
  declarations: [ConfigurationComponent, FormMappingComponent, SiteLevelConfigurationComponent],
})
export class ConfigurationModule {}



