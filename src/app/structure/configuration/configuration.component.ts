import { Component, AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';

declare const $: any;
@Component({
  selector: 'app-configuration',
  templateUrl: './configuration.component.html',
  styleUrls: ['./configuration.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ConfigurationComponent implements AfterViewInit {
  constructor(private cd: ChangeDetectorRef) {}
  activeTab = 'formMapping';
  ngAfterViewInit() {
    $('a[data-toggle="tab"]').on('shown.bs.tab', (e) => {
      this.activeTab = $(e.target).attr('id');
      this.cd.markForCheck();
    });
  }
}
