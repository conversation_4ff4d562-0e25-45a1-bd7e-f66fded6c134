import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { InfusionSupportComponent } from './infusion-support.citushealth';


export const routes: Routes = [
  { path: 'infusion-support', component: InfusionSupportComponent },

  
  
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes)
  ],
  declarations: [
    InfusionSupportComponent,
  ]

})

export class InfusionSupportModule { }
