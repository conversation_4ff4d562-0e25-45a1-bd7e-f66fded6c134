import { of } from 'rxjs/observable/of';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { NO_ERRORS_SCHEMA, Component } from '@angular/core';
import { RouterModule } from '@angular/router';
import { APP_BASE_HREF, CommonModule } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpService } from 'app/services/http/http.service';
import { TranslateModule } from '@ngx-translate/core';
import { Observable } from 'rxjs/Observable';
import { NgbModal, NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { ActivityLogsComponent } from './activity-logs.component';
import { ToolTipService } from '../tool-tip.service';
import { StaticDataService } from '../static-data.service';

@Component({
  selector: 'app-advance-search',
  template: ''
})
class AdvanceSearchStubComponent {}
describe('ActivityLogsComponent', () => {
  let component: ActivityLogsComponent;
  let fixture: ComponentFixture<ActivityLogsComponent>;

  beforeEach(async(() => {
    const httpServiceMock = {
      doGet: () =>
        of({
          content: []
        }),
      doPost: (url, data) => {
        if (url.endsWith('/search')) {
          if (data.startDate !== '' && data.endDate !== '') {
            return Observable.throw({ status: 404 });
          }
          return of({ content: [{ activityName: '', activityDescription: '', platform: '', createdDate: '' }] });
        }
        if (url.endsWith('/user-search')) {
          return of({ content: [{ userid: '', firstname: '', lastname: '' }] });
        }
        return of({ content: [{ key: '', value: '' }] });
      }
    };

    const staticServiceMock = {
      getRolesAndPrivileges: () => of({ content: [] })
    };

    TestBed.configureTestingModule({
      declarations: [ActivityLogsComponent, AdvanceSearchStubComponent],
      imports: [RouterModule.forRoot([]), CommonModule, HttpClientTestingModule, TranslateModule.forRoot(), NgbModule.forRoot()],
      providers: [
        {
          provide: APP_BASE_HREF,
          useValue: '/'
        },
        {
          provide: HttpService,
          useValue: httpServiceMock
        },
        {
          provide: StaticDataService,
          useValue: staticServiceMock
        },
        {
          provide: ToolTipService,
          useValue: jasmine.createSpyObj('ToolTipService', ['getTranslateData'])
        },
        NgbModal
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ActivityLogsComponent);
    component = fixture.componentInstance;
    component.showAdvanceSearchForm = true;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('provide activity type in advance search and get activity logs', () => {
    component.applyAdvanceSearch({
      activityLogsType: 123,
      activityLogsUser: '',
      activityLogsDate: ''
    });
  });

  it('provide user in advance search and get activity logs', () => {
    component.applyAdvanceSearch({
      activityLogsType: '',
      activityLogsUser: 456,
      activityLogsDate: ''
    });
  });

  it('provide date in advance search and get empty activity logs', () => {
    const date = new Date().toISOString().slice(0, 10);
    component.applyAdvanceSearch({
      activityLogsType: 123,
      activityLogsUser: 456,
      activityLogsDate: `${date} - ${date}`
    });
  });
});
