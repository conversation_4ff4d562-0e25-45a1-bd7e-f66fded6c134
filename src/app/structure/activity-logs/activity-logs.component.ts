import { map } from 'rxjs/operators';
import { APIs } from 'app/constants/apis';
import { HttpService } from 'app/services/http/http.service';
import { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ChangeDetectorRef, ViewChild, TemplateRef } from '@angular/core';
import { CONSTANTS, DocumentTypes, Languages } from 'app/constants/constants';
import { NgbModal, NgbModalOptions, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { HttpParams } from '@angular/common/http';
import { DatePipe } from '@angular/common';
import { ActivityTrailResponse, ActivityTypesResponse, ContentActivityResponse, UserSearchResponse } from './activity-logs.model';
import { ToolTipService } from '../tool-tip.service';
import { ControlType } from '../shared/advance-search/control-type';
import { StaticDataService } from '../static-data.service';

declare const $: any;

@Component({
  selector: 'app-activity-logs',
  templateUrl: './activity-logs.component.html',
  styleUrls: ['./activity-logs.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ActivityLogsComponent implements OnInit, OnDestroy {
  dtActivityLogs;
  loadMsg = false;
  modalRef: NgbModalRef;
  modalOptions: NgbModalOptions = {
    backdrop: 'static',
    keyboard: false,
    size: 'lg'
  };
  @ViewChild('viewActivityLog') viewActivityLogRef: TemplateRef<any>;
  rolesAndPrivileges;
  selectedActivityId: number;
  activityLogData: ActivityTrailResponse;
  msgContent = this.toolTipService.getTranslateData('MESSAGES.NO_DATA_AVAILABLE');
  activitySearchData = { activityTypeId: 0, userId: 0, startDate: '', endDate: '' };
  sortColumnNames = {
    1: 'activityName',
    5: 'createdAt',
    6: 'activityName'
  };
  advanceSearchControls = [
    new ControlType({
      key: 'activityLogsType',
      label: this.toolTipService.getTranslateData('ACTIVITY_LOGS.ACTIVITY_TYPE'),
      value: '',
      controlType: 'single-select-dropdown',
      options: [],
      defaultOptions: [],
      order: 6,
      class: 'col-md-4'
    }),
    new ControlType({
      key: 'activityLogsUser',
      label: this.toolTipService.getTranslateData('LABELS.USER'),
      value: '',
      controlType: 'dropdown-search',
      options: [],
      defaultOptions: [],
      order: 6,
      class: 'col-md-4'
    }),
    new ControlType({
      key: 'activityLogsDate',
      label: this.toolTipService.getTranslateData('LABELS.DATE'),
      value: '',
      controlType: 'date-range-picker',
      options: [],
      defaultOptions: [],
      order: 6,
      class: 'col-md-4'
    })
  ];
  showAdvanceSearchForm = false;
  dateTimeFormat = 'MMM dd y hh:mm a';
  loadingActivityDetails = false;
  constructor(
    private httpService: HttpService,
    private staticService: StaticDataService,
    private toolTipService: ToolTipService,
    private modalService: NgbModal,
    private cd: ChangeDetectorRef
  ) {}

  ngOnInit() {
    this.initializeActivityLogsTable();
    this.updateOptionsList(
      {
        index: 0,
        searchText: ''
      },
      true
    );
    this.updateOptionsList(
      {
        index: 1,
        searchText: ''
      },
      true
    );
  }

  initializeActivityLogsTable() {
    this.dtActivityLogs = $('#dtActivityLogs').DataTable({
      lengthMenu: [
        [25, 50],
        [25, 50]
      ],
      responsive: true,
      processing: true,
      searching: true,
      serverSide: true,
      autoWidth: false,
      order: [[5, 'desc']],
      dom: "<'row'<'col-sm-4 'l>B<'col-sm-4'f><'col-sm-4 ml-0 searchButton'>><'row'<'col-sm-12'tr>><'row'<'col-sm-5'i><'col-sm-7'p>>",
      buttons: [],
      initComplete: () => {
        $('div.searchButton').html(
          `<button id="download-activity-logs" class="btn btn-sm btn-primary downloadBView float-right mr-2" title="
          ${this.toolTipService.getTranslateData('BUTTONS.DOWNLOAD')}" type="submit">
          ${this.toolTipService.getTranslateData('BUTTONS.DOWNLOAD')}</button>`
        );
        const value = $('div.dataTables_filter input').val();
        if (value) {
          $('.searchBView').prop('disabled', false);
        }
      },
      ajax: (d, cb) => {
        if (this.activitySearchData.activityTypeId > 0 || this.activitySearchData.userId > 0) {
          this.httpService
            .doPost(APIs.activityLogsEndpoint, {
              activityTypeId: this.activitySearchData.activityTypeId,
              userId: this.activitySearchData.userId,
              startDate: this.activitySearchData.startDate,
              endDate: this.activitySearchData.endDate,
              currentPage: d.start / d.length,
              rowsPerPage: d.length,
              sortBy: this.sortColumnNames[d.order[0].column],
              sortDirection: d.order[0].dir
            })
            .subscribe(
              (res: ContentActivityResponse) => {
                cb({
                  recordsTotal: res.page.totalElements,
                  recordsFiltered: res.page.totalElements,
                  data: res.content
                });
              },
              () => {
                cb({
                  recordsTotal: 0,
                  recordsFiltered: 0,
                  data: []
                });
              }
            );
        } else {
          cb({
            recordsTotal: 0,
            recordsFiltered: 0,
            data: []
          });
        }
      },
      fnRowCallback: (nRow, aData) => {
        $(nRow).on('click', () => {
          this.selectedActivityId = aData.activityId;
        });
      },
      columnDefs: [
        {
          className: 'dt-control',
          data: null,
          orderable: false,
          targets: 0,
          width: '5%',
          defaultContent: ''
        },
        {
          data: null,
          orderable: true,
          targets: 1,
          render: (data, type, row) => {
            return row.activityName;
          }
        },
        {
          data: null,
          orderable: false,
          targets: 2,
          render: (data, type, row) => {
            return row.value;
          }
        },
        {
          data: null,
          orderable: false,
          targets: 3,
          render: (data, type, row) => {
            return `${row.firstname} ${row.lastname}`;
          }
        },
        {
          data: null,
          orderable: false,
          targets: 4,
          render: (data, type, row) => {
            return row.platform;
          }
        },
        {
          data: null,
          orderable: true,
          targets: 5,
          render: (data, type, row) => {
            const datePipe = new DatePipe('en-US');
            return row.createdDate ? datePipe.transform(`${row.createdDate}Z`, this.dateTimeFormat) : '';
          }
        },
        {
          data: null,
          orderable: true,
          targets: 6,
          render: (data, type, row) => {
            return row.actionType;
          }
        },
        {
          data: null,
          orderable: false,
          targets: 7,
          render: (data, type, row) => {
            const actions = `<a id="view-activity-log" href="javascript: void(0);" data-rowId ="${
              row.id
            }"  class="cat__core__link--underlined mr-3 ${(row.actionType !== 'Updated' ? 'disabled' : '')}"><i id="view" data-rowId ="${
              row.id
            }" class="icmn-eye"></i>&nbsp;${this.toolTipService.getTranslateData('BUTTONS.VIEW')}</a>`;
            return actions;
          }
        }
      ]
    });

    this.dtActivityLogs.on('click', 'td.dt-control', (e) => {
      const tr = e.target.closest('tr');
      const row = this.dtActivityLogs.row(tr);

      if (row.child.isShown()) {
        // This row is already open - close it
        row.child.hide();
      } else {
        // Open this row
        row.child(this.format(row.data())).show();
      }
    });

    $(document).on('click', '#download-activity-logs', () => {
      if (this.activitySearchData.activityTypeId > 0 || this.activitySearchData.userId > 0) {
        let params = new HttpParams();
        params = params.append('activityTypeId', String(this.activitySearchData.activityTypeId));
        params = params.append('userId', String(this.activitySearchData.userId));
        params = params.append('startDate', this.activitySearchData.startDate);
        params = params.append('endDate', this.activitySearchData.endDate);
        let endpoint = APIs.downloadActivityLogsEndpoint;
        endpoint = endpoint.replace(/{language}/g, Languages.EN);
        endpoint = endpoint.replace(/{outputType}/g, DocumentTypes.EXCEL);
        const downloadBtn = $('#download-activity-logs');
        downloadBtn.append('&nbsp;<i id="download-spinner" class="fa fa-spinner fa-spin"></i>');
        this.httpService.doGet(endpoint, { params, responseType: 'blob' }).subscribe((data) => {
          const link = document.createElement('a');
          link.href = window.URL.createObjectURL(data);
          link.download = 'activity-logs';
          link.click();
          downloadBtn.find('#download-spinner').remove();
        });
      }
    });

    $(document).on('click', '#view-activity-log', () => {
      if (this.selectedActivityId) {
        this.loadingActivityDetails = true;
        this.modalRef = this.modalService.open(this.viewActivityLogRef, this.modalOptions);
        this.staticService.getRolesAndPrivileges().subscribe((res) => {
          this.rolesAndPrivileges = res.reduce((acc, { key, value }) => ({ ...acc, [key]: value }), {});
          this.cd.markForCheck();
        });

        this.httpService
          .doGet(`${APIs.activityTrailEndpoint.replace(/{activityId}/g, `${this.selectedActivityId}`)}`)
          .subscribe((res: ActivityTrailResponse) => {
            this.loadingActivityDetails = false;
            this.activityLogData = res;
          });
      }
    });
  }

  format(d) {
    return `<dl><dt>${this.toolTipService.getTranslateData('ACTIVITY_LOGS.ACTIVITY_DESC')}</dt><dd>${d.activityDescription}</dd></dl>`;
  }

  updateOptionsList(event, isDefault?: boolean) {
    const payload = {
      currentPage: CONSTANTS.contentOffset,
      rowsPerPage: CONSTANTS.contentLimit,
      searchKey: event.searchText,
      sortBy: '',
      sortDirection: 'ASC'
    };
    this.loadMsg = true;
    this.msgContent = this.toolTipService.getTranslateData('MESSAGES.LOADING_DATA');
    if (event.index === 0) {
      this.httpService
        .doPost(`${APIs.activityTypesEndpoint}`, payload)
        .pipe(
          map((res: ActivityTypesResponse) => {
            const data = res.content;
            return data;
          })
        )
        .subscribe((res: any) => {
          this.updateOptions(event.index, res, isDefault);
        });
    } else {
      this.httpService
        .doPost(`${APIs.userSearchEndpoint}`, payload)
        .pipe(
          map((res: UserSearchResponse) => {
            const data = res.content.map((item) => ({ id: item.userid, itemName: item.displayname }));
            return data;
          })
        )
        .subscribe((res: any) => {
          this.updateOptions(event.index, res, isDefault);
        });
    }
  }

  updateOptions(index, res, isDefault) {
    if (index === 0) this.showAdvanceSearchForm = true;
    if (res.length === 0) {
      this.msgContent = this.toolTipService.getTranslateData('MESSAGES.NO_DATA_AVAILABLE');
    } else {
      this.loadMsg = false;
      this.advanceSearchControls[index].options = res;
      if (index === 0 && res.length === 1) {
        this.advanceSearchControls[index].value = res;
        this.applyAdvanceSearch({ activityLogsType: res[0].key });
      }
      if (isDefault) this.advanceSearchControls[index].defaultOptions = res;
    }
    this.cd.detectChanges();
  }

  applyAdvanceSearch(event) {
    let activityLogsDateRange = ['', ''];
    if (event.activityLogsDate) {
      activityLogsDateRange = event.activityLogsDate.split(' - ');
    }
    this.activitySearchData = {
      activityTypeId: event.activityLogsType ? Number(event.activityLogsType) : 0,
      userId: event.activityLogsUser ? Number(event.activityLogsUser) : 0,
      startDate: activityLogsDateRange[0],
      endDate: activityLogsDateRange[1]
    };
    this.dtActivityLogs.ajax.reload(null, false);
  }

  ngOnDestroy() {
    $(document).off('click', '#download-activity-logs');
    $(document).off('click', '#view-activity-log');
    if (this.dtActivityLogs) this.dtActivityLogs.destroy();
  }
}
