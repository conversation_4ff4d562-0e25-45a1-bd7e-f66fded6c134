<section class="card">
  <div class="card-header cat__core__title">
    <strong [translate]="'MENU.ACTIVITY_LOGS'"></strong>
  </div>
  <div class="card-block">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a [routerLink]="['/inbox']" [translate]="'MENU.INBOX_AS_HOME'"></a></li>
      <li class="breadcrumb-item"><a [translate]="'MENU.ACTIVITY_LOGS'"></a></li>
    </ol>
    <div class="row">
      <div class="col-lg-12">
        <app-advance-search
          *ngIf="showAdvanceSearchForm"
          [showClose]="false"
          [loadMsg]="loadMsg"
          [msgContent]="msgContent"
          [dynamicControls]="advanceSearchControls"
          (updateOptions)="updateOptionsList($event)"
          (advanceSearch)="applyAdvanceSearch($event)"
        >
        </app-advance-search>
      </div>
      <div class="col-lg-12">
        <div class="mb-5">
          <table class="table table-hover" id="dtActivityLogs" width="100%">
            <thead>
              <tr>
                <th></th>
                <th>{{ 'ACTIVITY_LOGS.ACTIVITY_TYPE' | translate }}</th>
                <th>#</th>
                <th>{{ 'LABELS.USER' | translate }}</th>
                <th>{{ 'ACTIVITY_LOGS.PLATFORM' | translate }}</th>
                <th>{{ 'ACTIVITY_LOGS.ACTIVITY_DATE' | translate }}</th>
                <th>{{ 'GENERAL.ACTION' | translate }}</th>
                <th>{{ 'GENERAL.ACTIONS' | translate }}</th>
              </tr>
            </thead>
          </table>
        </div>
      </div>
    </div>
  </div>
</section>

<ng-template #viewActivityLog>
  <div class="modal-header">
    <h5 class="modal-title">{{ 'ACTIVITY_LOGS.ACTIVITY_DETAILS' | translate }}</h5>
    <button type="button" class="close" (click)="modalRef.close()" data-dismiss="modal" attr.aria-label="{{ 'BUTTONS.CLOSE' | translate }}">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <dl class="row" *ngIf="!this.loadingActivityDetails; else loadingData">
      <dt class="col-2">{{ 'LABELS.TYPE' | translate }}</dt>
      <dd class="col-10">{{ activityLogData.activityName }}</dd>
      <dt class="col-2">{{ 'LABELS.USER' | translate }}</dt>
      <dd class="col-10">{{ activityLogData.firstname }}&nbsp;{{ activityLogData.lastname }}</dd>
      <dt class="col-2">{{ 'GENERAL.VALUE' | translate }}</dt>
      <dd class="col-10">{{ activityLogData.value }}</dd>
      <dt class="col-2">{{ 'LABELS.DATE_AND_TIME' | translate }}</dt>
      <dd class="col-10">{{ activityLogData.createdDate ? (activityLogData.createdDate + 'Z' | date : dateTimeFormat) : '' }}</dd>
      <dt class="col-2">{{ 'GENERAL.ACTION' | translate }}</dt>
      <dd class="col-10">{{ activityLogData.actionType }}</dd>
      <dt class="col-2">{{ 'GENERAL.CURRENT' | translate }}</dt>
      <dd class="col-10">
        <div class="row px-2">
          <div
            class="col-4 border-right border-bottom"
            [ngClass]="{
              'border-left': i % 3 === 0,
              'border-top': [0, 1, 2].includes(i)
            }"
            *ngFor="let privilege of activityLogData.addedPrivileges; index as i"
          >
            {{ activityLogData.activityName === 'Update Tenant Role Privilege' ? rolesAndPrivileges[privilege] : privilege }}
          </div>
          <span *ngIf="!activityLogData.addedPrivileges || activityLogData.addedPrivileges.length === 0">{{ 'GENERAL.NONE' | translate }}</span>
        </div>
      </dd>
      <dt class="col-2">{{ 'GENERAL.PREVIOUS' | translate }}</dt>
      <dd class="col-10">
        <div class="row px-2">
          <div
            class="col-4 border-right border-bottom"
            [ngClass]="{ 'border-left': i % 3 === 0, 'border-top': [0, 1, 2].includes(i) }"
            *ngFor="let privilege of activityLogData.deletedPrivileges; index as i"
          >
            {{ activityLogData.activityName === 'Update Tenant Role Privilege' ? rolesAndPrivileges[privilege] : privilege }}
          </div>
          <span *ngIf="!activityLogData.deletedPrivileges || activityLogData.deletedPrivileges.length === 0">{{ 'GENERAL.NONE' | translate }}</span>
        </div>
      </dd>
    </dl>
    <ng-template #loadingData>
      <h5><i class="fa fa-spinner fa-spin"></i>&nbsp;{{ 'MESSAGES.LOADING_DATA' | translate }}</h5>
    </ng-template>
  </div>
</ng-template>
