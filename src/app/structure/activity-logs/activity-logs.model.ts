import { Page } from "../shared/models/Page.model";

export interface ActivityLog {
  activityId: number;
  activityName: string;
  activityType: string;
  activityDescription: string;
  rootId: number;
  parentId: number;
  hierarchyPath: null;
  platform: string;
  environment: string;
  createdBy: number;
  createdFor: number;
  createdDate: string;
  appVersion: string;
  firstname: string;
  lastname: string;
  username: string;
}

export interface ActivityType {
  key: number;
  value: string;
}

export interface User {
  userid: number;
  firstname: string;
  lastname: string;
  displayname: string;
}

export interface Sort {
  empty: boolean;
  sorted: boolean;
  unsorted: boolean;
}

export interface Pageable {
  sort: Sort;
  offset: number;
  pageNumber: number;
  pageSize: number;
  paged: boolean;
  unpaged: boolean;
}

export interface ContentActivityResponse {
  content: ActivityLog[];
  pageable: Pageable;
  last: boolean;
  sort: Sort;
  numberOfElements: number;
  first: boolean;
  empty: boolean;
  page: Page;
}

export interface ActivityTypesResponse {
  content: ActivityType[];
  pageable: null;
  numberOfElements: number;
  first: boolean;
  last: boolean;
  sort: Sort;
  empty: boolean;
  page: Page;
}

export interface UserSearchResponse {
  content: User[];
  pageable: Pageable;
  last: boolean;
  sort: Sort;
  numberOfElements: number;
  first: boolean;
  empty: boolean;
  page: Page;
}
export interface ActivityTrailResponse {
  activityId: number;
  previousActivityId: number;
  activityName: string;
  activityType: string;
  currentActivityDescription: string;
  previousActivityDescription: string;
  actionType: string;
  currentPrivileges: string[];
  previousPrivileges: string[];
  addedPrivileges: string[];
  deletedPrivileges: string[];
}
