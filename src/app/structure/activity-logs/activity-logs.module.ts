import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { RouterModule, Routes } from '@angular/router';
import { ActivityLogsComponent } from './activity-logs.component';
import { AuthGuard } from '../../guard/auth.guard';
import { SharedModule } from '../shared/sharedModule';

export const routes: Routes = [{ path: 'activity-logs', component: ActivityLogsComponent, canActivate: [AuthGuard], data: {checkUserGroupPermission : '3'} }];

@NgModule({
  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)],
  declarations: [ActivityLogsComponent]
})
export class ActivityLogsModule {}
