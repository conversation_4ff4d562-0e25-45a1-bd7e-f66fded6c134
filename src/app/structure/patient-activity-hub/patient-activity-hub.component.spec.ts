import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpService } from 'app/services/http/http.service';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Apollo, ApolloModule } from 'apollo-angular';
import { PermissionService } from 'app/services/permission/permission.service';
import { AuthService } from 'app/structure/shared/auth.service';
import { SharedService } from 'app/structure/shared/sharedServices';
import { StoreService } from 'app/structure/shared/storeService';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { provideClients } from 'test-utils';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { HttpModule } from '@angular/http';
import { PatientActivityHubComponent } from './patient-activity-hub.component';
import $ from 'jquery';
import { PahService } from './pah.service';
import { WorkListService } from '../worklists/worklist.service';
import { FormsService } from '../forms/forms.service';
import { of } from 'rxjs/observable/of';

class MockFormsService {
  // Mock the methods and properties used in the component
  getFormsData() {
    return JSON.stringify({ key: 'value' }); // Return valid JSON data
  }
}

describe('PatientActivityHubComponent', () => {
  let component: PatientActivityHubComponent;
  let fixture: ComponentFixture<PatientActivityHubComponent>;
  let structureService: StructureService;
  let formservice: FormsService;
  let workservice: WorkListService;
  const userDetails = {
    config: {
      enable_multisite: '1'
    }
  };

  beforeEach(async () => {
    structureService = jasmine.createSpyObj('StructureService', ['userDetails', 'trackActivity', 'getShowInPahTabs']);
    structureService.userDetails = JSON.stringify(userDetails);
    structureService.trackActivity = jasmine.createSpy('trackActivity');
    structureService.getShowInPahTabs = jasmine.createSpy('getShowInPahTabs').and.returnValue(Promise.resolve([])); // Correctly mock the promise
    workservice = jasmine.createSpyObj('WorkListService', ['worklistForPah']);
    workservice.worklistForPah = false; // Mock the worklistForPah property
  

    await TestBed.configureTestingModule({
      declarations: [PatientActivityHubComponent],
      providers: [
        HttpService,
        TranslateService,
        Apollo,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        PermissionService,
        StoreService,
        PahService,
        { provide: StructureService, useValue: structureService },
        { provide: WorkListService, useValue: workservice },
        { provide: FormsService, useClass: MockFormsService },
      ],
      imports: [
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        FormsModule,
        ReactiveFormsModule,
        RouterTestingModule,
        HttpModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF
        })
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PatientActivityHubComponent);
    component = fixture.componentInstance;
    structureService = TestBed.get(StructureService);
    fixture.detectChanges();
  });

  it('should open the send form modal when openSendFormModal is called', () => {
    spyOn(component, 'openSendFormModal');
    component.openSendFormModal();
    expect(component.openSendFormModal).toHaveBeenCalled();
  });

  it('should close the send form modal when closeSendFormModal is called', () => {
    spyOn(component, 'closeSendFormModal');
    component.closeSendFormModal();
    expect(component.closeSendFormModal).toHaveBeenCalled();
  });
});
