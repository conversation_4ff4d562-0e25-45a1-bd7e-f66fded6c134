<style>
    .profile-avatar {
        width: 150px;
        height: 150px;
        border: 1px solid #ddd;
        border-radius: 50%;
        overflow: hidden;
        margin-top: 20px;
    }
    
    .profile-avatar img {
        height: 100%;
        object-fit: cover;
    }
    
    img#profileImageSmall {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .profile-avatar a {
        z-index: 999;
        position: absolute;
        left: 122px;
        font-size: 23px;
        top: 100px;
        color: #5394a2;
    }
    
    .profile-avatar a i {
        background: #fff;
        border-radius: 50%;
        height: 20px;
        cursor: pointer;
    }
    
    .information-edit i {
        float: right;
        cursor: pointer;
        color: #0190fe;
        width: 30px;
        height: 30px;
        text-align: center;
        border-radius: 50%;
        padding-top: 6px;
    }
    
    .personal-information-edit-icon {
        float: left;
        position: absolute;
        padding-left: 5px;
        top: 90px;
    }
    
    .information-edit i:hover {
        background: #0190fe !important;
        color: #fff;
    }
    
    .personal-info-title {
        height: 35px;
    }
    
    .personal-info-submit {
        display: none;
    }
    
    .profile-avatar-disabled img {
        opacity: 0.3;
        cursor: no-drop;
    }
    
    .cat__apps__profile__card {
        border: none;
        background: #fff !important;
        margin-top: -8.58rem;
        background-size: cover;
        padding: 2.5rem .5rem .5rem 0.5rem;
    }
    
    .pah-icon-set {
        width: 40px;
        height: 40px;
        display: block;
        text-align: center;
        color: #fff;
        padding-top: 10px;
    }
    
    .pah-messages {
        background: rgb(184, 19, 19);
        cursor: pointer;
    }
    
    .pah-other {
        background: rgb(223, 188, 33);
        cursor: pointer;
    }
    
    .pah-form {
        background: rgb(5, 160, 64);
        cursor: pointer;
    }
    
    .pah-inventory {
        background: rgb(235, 84, 39);
        cursor: pointer;
    }
    
    .pah-videocommunication {
        background: rgb(70, 34, 39);
        cursor: pointer;
    }
    
    .outer-patients {
        height: 662px;
        overflow-y: scroll;
    }
    
    .pah-dropdown {
        float: right;
        position: relative;
    }
    
    .pah-dropdown .applyBtn {
        /* border: 0 !important;
        background-color: transparent !important;
        background: none !important; */
        margin-left: 10px;
    }
    
    .pah-dropdown .applyBtn:hover {
        background-color: #46be8a !important;
        border-color: #46be8a !important
    }
    /*   .pah-dropdown .btn:hover {
        background-color: transparent !important; 
    }*/
    
    .dropdown-toggle::after {
        display: none !important;
    }
    
    .pah-dropdown ul {
        margin-right: 100px !important;
        right: -117px !important;
        padding: 10px !important;
    }
    
    .pah-dropdown label {
        display: block;
    }
    
    .pah-profile-img img {
        width: 75px;
    }
    
    .pah-profile-details label span {
        font-weight: bold;
    }
    
    .display-name {
        font-size: 18px;
    }
    
    .pah-dropdown ul {
        left: inherit !important;
        width: 330px;
    }
    /*     
    .activity-section .cat__core__step__desc:first-child {
        background: rgb(184, 19, 19);
    }
    
    .activity-section .cat__core__step__desc:nth-child(2) {
        background: rgb(5, 160, 64);
    }
    
    .activity-section .cat__core__step__desc:nth-child(3) {
        background: rgb(223, 188, 33);
    }
    
    .activity-section .cat__core__step__desc:nth-child(4) {
        background: rgb(235, 84, 39);
    } */
    
    .left-left-patient {
        cursor: pointer;
    }
    
    .cat__core__step__desc {
        padding: 10px;
    }
    
    .cat__core__step__desc span {
        color: #ffff !important;
        font-size: 18px !important;
    }
    
    .cat__core__step__desc p {
        color: #ffff !important;
        font-size: 14px !important;
    }
    
    .pah-FilingCenter {
        background: rgb(100, 134, 39);
        cursor: pointer;
    }
    
    .pah-supply {
        background: rgb(43, 153, 216);
        cursor: pointer;
    }
    
    .activity-section .col-lg-3 {
        margin-top: 10px;
    }
    
    .pah-menu-left-active {
        background-color: #e7e7e7;
    }
    
    .btn.btn-success,
    .show>.btn.btn-success {
        background-color: #46be8a !important;
        border-color: #46be8a !important;
    }
    
    .chatroom-message-min-list {
        max-height: inherit !important;
    }
    
    .date-filter-pah {
        position: absolute;
        right: 90px;
        top: auto;
        margin-top: 6px;
        padding: 10px;
    }

    /* //sideBar NAV */
    /* Shrinking the sidebar from 250px to 80px and center aligining its content*/
#sidebar.active {
    min-width: 120px;
    max-width: 120px;
}
/* Toggling the sidebar header content, hide the big heading [h3] and showing the small heading [strong] and vice versa*/
#sidebar .sidebar-header p {
    display: none;
}
#sidebar.active .sidebar-header span {
    display: none;
}
#sidebar.active .sidebar-header p {
    display: block;
}
    /* //sideBar NAV */
.search-hide-btn{
    color: #fff;
    position: absolute;
    right: -8px;
    top: 1px;
    z-index: 98;
    background: teal;
    border: 0;
    border-radius: 0;
    padding: 6px;
}
.custom-search-width {
    min-width: 88% !important;
    overflow:auto;
}

#stickThis {
    padding: 5px;
    background-color: #ccc;
    font-size: 1.5em;
   /* width: 550px;*/
    text-align: center;
    font-weight: bold;
    border: 2px solid #444;
    -webkit-border-radius: 10px;
    border-radius: 10px;
}
#stickThis.stick {
    margin-top: 60px;
    position: fixed;
    top: 0;
    z-index: 9;
    -webkit-border-radius: 0 0 10px 10px;
    border-radius: 0 0 10px 10px;
}


div#stickThis{
    background-color: #fff !important;
    border: 0 !important;
    border-radius: 0 !important;
    top: 60px;
    color: #292929 !important;
    text-align: left !important;
    padding-left: 15px !important;
    /*right: 19px;
    left: 540px;
    width: auto !important;*/
    margin-left: 8px;
    -webkit-box-shadow: 0px 2px 6px -2px rgba(0,0,0,0.75);
    -moz-box-shadow: 0px 2px 6px -2px rgba(0,0,0,0.75);
    box-shadow: 0px 2px 6px -2px rgba(0,0,0,0.75);
    font-size: 17px !important;
}
.cuppa-dropdown .selected-list .c-list .c-token {
background: #e6e6e6 !important;
}
.cuppa-dropdown .selected-list .c-list .c-token .c-label{
color: #2d2d2d;
}
.cuppa-dropdown .selected-list .c-list .c-token .c-remove svg {
fill: #2d2d2d;
}
.cuppa-dropdown .pure-checkbox input[type="checkbox"]:checked + label:before {
background: #8d94a6;
border-color: #8d94a6;
}
.cuppa-dropdown .pure-checkbox input[type="checkbox"] + label:before {
border: 1px solid #8c94a5;
}
.cuppa-dropdown .pure-checkbox input[type="checkbox"] + label {
color: #343434 !important;
text-transform: none;
}
.card-header {
        margin-left: 3px;
        margin-right: 3px;
    }
</style>
<!-- <nav class="cat__core__top-sidebar cat__core__top-sidebar--bg">
    <div class="row">
        <h2 style="padding-left: 14px;">
            <span class="text-black">
                <strong>Patient Activity Hub</strong>
            </span>

        </h2>
        <p class="mb-1"></p>
    </div>
</nav> -->

<section class="card">


    <div class="card-header row">
        <div class="col-md-9 row">
            <span class="cat__core__title col-md-5">
                <strong>Patient Activity Hub </strong>
                <!--<a [routerLink]="['/message/messagegroup']" class="pull-right btn btn-sm btn-primary">Add Message Group<i class="ml-1"></i></a>-->
            </span>
    
    
            <div class="filter-sites-wrapper col-md-7" [hidden]="!hideSiteSelection || (_worklistService.worklistForPah == true && pahWorklistId != '')">
                <div class="row">
                    <div class="site-label">
                        <span>{{ 'LABELS.FILTER_BY_SITES' | translate }}</span>
                    </div>
                    <div style="width: 100%" class="col-md-9">
                        <app-select-sites [events]="eventsSubject.asObservable()" [filterType]=true
                            (hideDropdown)="hideDropdown($event)" (siteIds)="getSiteIds($event)">
                        </app-select-sites>
                    </div>
                </div>
            </div>
        </div>
        <span class="pull-right col-md-3">
            <a (click)="goBack()" *ngIf="_worklistService.worklistForPah == true && pahWorklistId != ''" class="btn btn-sm btn-primary float-sm-right">
                <i class="fa fa-long-arrow-left" aria-hidden="true"></i>&nbsp;Back
            </a>
            <a (click)="reload()" class="btn btn-sm btn-primary float-sm-right mr-1">
                <i class="fa fa-refresh" aria-hidden="true"></i>&nbsp;Reload
            </a>
        </span>
    </div>

        <section class="card" *ngIf="dataLoadingMsg">
            <div class="card-block mb-2 mt-2">
                <div class="wait-loading">
                    <img src="assets/img/loader/loading.gif" />
                </div>
            </div>
        </section>
    
<div class="row" *ngIf="!dataLoadingMsg">
        <nav *ngIf="_worklistService.worklistForPah == false || pahWorklistId == ''" class="col-lg-3" id="sidebar">
                <button type="button" id="sidebarCollapse" class="btn btn-sm search-hide-btn" (click)="changeArrow()">
                    <i title="Expand" [hidden]="!changeArrowButton" class="fa fa-angle-double-right"></i>
                    <i  title="Collapse" [hidden]="changeArrowButton" class="fa fa-angle-double-left"></i>
                </button>
                <section class="card">
                    <div class="card-block"> 
                        <div class="cat__core__card-sidebar">
                            <div class="row" >
                                <div class="col-md-12 searchbar">
                                    <div class="cat__apps__messaging__header sidebar-header" [hidden]="changeArrowButton">
                                        <span><input class="form-control cat__apps__messaging__header__input" (keyup.enter)="searchUser(searchText)" style="width: 75%;margin-left: -10px;" placeholder="Search"
                                                [(ngModel)]="searchText" /></span>
                                        <p><input class="form-control cat__apps__messaging__header__input" placeholder="S"
                                                [(ngModel)]="searchText" /></p>
                                       <!--  <i class="icmn-search"></i>
                                        <button type="button" (click)="searchUser(searchText)"></button> -->
                                        <button type="button" [disabled]="!searchText" style="margin-right:10px;" class="btn btn-sm btn-primary" title="Search" (click)="searchUser(searchText)"><span style="margin-left:-3px;" class="fa fa-search"></span></button>
                                        <button type="button" class="btn btn-sm btn-default" style="margin-right:-16px;" title="Reset" (click)="resetPatient = true;searchPatient='';searchText='';setPatientList()"><span style="margin-left:-3px;" class="fa fa-refresh"></span></button>
                                    </div>
                                </div>
                            </div>
                            <div class="cat__apps__messaging__list inbox-data-container outer-patients">
                                <div *ngFor="let patient of patientList">
                                    <div class="cat__apps__messaging__tab messages-tab hand-pointer" (click)="updateActivityHub(patient.id);" [class.cat__apps__messaging__tab--selected]="patient.id==userPatientId" >
                                        <div class="cat__apps__messaging__tab__avatar">
                                            <a class="cat__core__avatar cat__core__avatar--50" href="javascript:void(0);"
                                                (click)="updateActivityHub(patient.id);">
                                                <img *ngIf="!patient.avatar && patient.dateOfBirth != ''" src="{{baseuri}}/cometchat/citus-health/avatars/profile-pic-clinician-nurse-default.png"
                                                    alt="Alternative text to the image" title="{{patient.displayName}} ({{patient.dateOfBirthFormated| date:'MM/dd/yyyy'}})">
                                                <img *ngIf="patient.avatar && patient.dateOfBirth != ''" src="{{baseuri}}/cometchat/citus-health/avatars/{{patient.avatar}}"
                                                    alt="Alternative text to the image" title="{{patient.displayName}} ({{patient.dateOfBirthFormated| date:'MM/dd/yyyy'}})">
                                                <img *ngIf="!patient.avatar && patient.dateOfBirth == ''" src="{{baseuri}}/cometchat/citus-health/avatars/profile-pic-clinician-nurse-default.png"
                                                    alt="Alternative text to the image" title="{{patient.displayName}}">
                                                <img *ngIf="patient.avatar && patient.dateOfBirth == ''" src="{{baseuri}}/cometchat/citus-health/avatars/{{patient.avatar}}"
                                                    alt="Alternative text to the image" title="{{patient.displayName}}">
                                            </a>
                                        </div>
                                        <div class="cat__apps__messaging__tab__content" [hidden]="changeArrowButton">
                                            <div class="left-left-patient" id="{{patient.id}}"
                                                (click)="updateActivityHub(patient.id);">
                                                <span style="word-wrap: break-word;" *ngIf = "patient.dateOfBirth != ''">{{patient.displayName}}</span>
                                                <!-- <span style="word-wrap: break-word;" *ngIf = "patient.dateOfBirthFormated != ''">({{patient.dateOfBirthFormated}}) </span> -->
        
                                                   <span *ngIf="patient.role && patient.role.displayName != 'Caregiver'"> - {{patient.dateOfBirthFormated| date:'MM/dd/yyyy'}}</span>
                                                   <span style="word-wrap: break-word;" *ngIf = "patient.role && patient.role.displayName != 'Caregiver' && patient.patientExternalSystemIntegration && patient.patientExternalSystemIntegration.IdentityValue">[MRN:{{patient.patientExternalSystemIntegration.IdentityValue}}]</span>
         
                                                   <span style="word-wrap: break-word;" *ngIf = "patient.dateOfBirth == ''">{{patient.displayName}}
                                                    </span>
                                                    <br>
                                                <span class="badge badge-info left-menu-unread-count" *ngIf="patient.password != true">Virtual Patient</span>
                                                <span class="badge badge-info left-menu-unread-count" *ngIf="patient.password == true && patient.role.displayName == 'Patient'">Enrolled Patient</span>
                                                <span class="badge badge-info left-menu-unread-count" *ngIf="patient.role && patient.role.displayName == 'Caregiver'">Enrolled Caregiver</span>
                                                <span class="badge badge-success mr-2 mb-2" *ngIf="patient.status == 'Active' || patient.status == 'Virtual_User'">Active</span>
                                                <span class="badge badge-warning mr-2 mb-2" *ngIf="patient.status == 'Pending'">Pending</span>
                                                <span class="badge badge-info mr-2 mb-2" *ngIf="patient.status == 'Discharged'">Discharged</span>
                                                <span class="badge badge-danger mr-2 mb-2" *ngIf="patient.status != 'Virtual_User' && patient.status != 'Discharged' && patient.status != 'Active' && patient.status != 'Pending'">{{patient.status}}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div *ngIf="statusMessage" style="text-align: center;margin-top: 33px;">{{statusMessage}}</div>
                                <div [hidden]="hideLoadMore" class="cat__apps__messaging__tab messages-tab hand-pointer" >
                                    <div class="cat__apps__messaging__tab__content" >
                                        <div class="cat__apps__messaging__tab__name left-left-patient">
                                            <button (click)="loadMorePatient()" class="btn btn-sm btn-default"  [hidden]="hideLoadMore">Load More</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </nav>
    <div  [ngClass] ="_worklistService.worklistForPah == true && pahWorklistId != '' ? 'col-md-12': 'col-md-9'">
       
        <section class="card pah-custom-section" *ngIf="(totalPatientCount > 0  && resetPatient == false && showpat_msg==false) || (totalPatientCount >= 1  && searchText !=''  && resetPatient ==true && showpat_msg==false) ||  (_worklistService.worklistForPah == true && pahWorklistId != '')" >
            <!--<section class="card pah-custom-section" [hidden]="totalPatientCount == 0 &&            
                
                resetPatient == false" >-->          
                  <div class="row" style="padding-left: 10px; padding-right: 10px;">
                <div class="" style="width:100%;">
                    <div class="row pah-profile-custom">
                        <div class="col-lg-2 pah-profile-img">
                            <img class="ml-3" src="{{avatarselected}}" alt="Alternative text to the image" style = "background-color: #eef0f4;">
                        </div>
                        <div id="stick-here"></div>
                        <div id="stickThis" style="display:none;"> {{userDatam?.displayName}}</div>  
                        
                        <div class="col-lg-10 pah-profile-details mb-3">
                            <div class="row" style="    padding-left: 10px;">
                                <div class="col-sm-12 display-name display-flex">
                                <label>
                                    <span>
                                        <span>{{userDatam?.displayName}}</span><span *ngIf="userDatam?.dateOfBirth != ''">-{{userDatam?.dateOfBirth | date:'MM/dd/yyyy'}}</span><span *ngIf="userDatam.patientIdentity && userDatam.patientIdentity.IdentityValue">[MRN:{{userDatam.patientIdentity.IdentityValue}}]</span>
                                        <span *ngIf="userDatam?.password != true" class="badge badge-info left-menu-unread-count">Virtual Patient</span>
                                        <span *ngIf="userDatam?.password == true && userDatam?.role && userDatam.role.displayName == 'Patient'" class="badge badge-info left-menu-unread-count">Enrolled Patient</span>

                                        <span class="badge badge-info left-menu-unread-count" *ngIf="userDatam && userDatam.role.displayName == 'Caregiver'">Enrolled Caregiver</span>
                                    </span>
                                    <span class="user-not-contactable-outline mt-1 bg-danger icon-scale" *ngIf="userDatam && !userDatam.isContactable" data-toggle='tooltip' data-placement='right' chToolTip="noContactInfoOrOptedOut" ></span>
                                </label>
                                <button class="btn btn-sm btn-primary float-sm-right mr-3 mb-1 ml-auto" id="send-form" type="button" (click)="openSendFormModal();" *ngIf="isSendFormEnable">
                                    <span class="fa fa-list-alt" aria-hidden="true"></span> {{ 'TITLES.SEND_FORM' | translate }}
                                </button>
                                </div>
                                <div class="row col-sm-12">
                                    <div class="col-sm-4"><label><span>First Name :</span> {{userDatam?.firstName}} </label></div>
                                    <div class="col-sm-4"><label><span>Last Name :</span> {{userDatam?.lastName}}</label></div>
                                </div>
                                <div class="row col-sm-12">
                                    <div class="col-sm-4">
                                        <label class="row" *ngIf="userDatam?.mobile" class="row col-sm-12">
                                            <span>Mobile :</span> 
                                            <span>{{userDatam?.mobile}}</span>
                                            <span class="sms-opt-out-outline ml-1 bg-danger icon-scale" *ngIf="userDatam && userDatam.enableSmsNotifications !== 1" data-toggle='tooltip' data-placement='right' chToolTip="smsOptOutTooltip" ></span> 
                                        </label>
                                    </div>
                                    <div class="col-sm-4"><label *ngIf="userDatam && userDatam.role.displayName != 'Caregiver'"><span>DOB :</span> {{userDatam?.dateOfBirth | date:
                                            'MM/dd/yyyy'}}</label></div>
                                </div>
                                <div class="row col-sm-12">
                                    <div class="col-sm-4">
                                        <label [hidden]="emailselected === ''" class="row col-sm-12">
                                            <span>Email :</span>
                                            <span>{{emailselected}}</span>
                                            <span class="email-opt-out-outline ml-1 bg-danger icon-scale" *ngIf="userDatam && userDatam.enableEmailNotifications !== 1" data-toggle='tooltip' data-placement='right' chToolTip="emailOptOutTooltip" ></span> 
                                        </label>
                                    </div>
                                    <!-- <div class="col-sm-12"><label><span>ESI :</span> 123456</label></div> -->
                                    <div class="col-sm-4" ><label *ngIf="enabledMultisite"><span>Site :</span> {{userDatam?.siteName}}</label></div>
                                </div>
                                <div class="row col-sm-12">
                                    
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- <div class="col-sm-4 card chatwith-modal-custom">
                    <div class="chatwith-modal-tab main-tabs">
                        <div class="chatwith-model-head-pah" (click)="showDataMenu('status')"
                            [class.cat__apps__messaging__tab_pah--selected]="optionShowMenu=='status'">STATUS</div>
                        <div class="chatwith-model-head-pah" (click)="showDataMenu('todo')"
                            [class.cat__apps__messaging__tab_pah--selected]="optionShowMenu=='todo'">TO
                            DO</div>
                    </div>
                    <div class="chatwith-modal-description">
                        <div *ngIf="optionShowMenu=='status'">
                            STATUS
                        </div>
                        <div *ngIf="optionShowMenu=='todo'">
                            TODO
                        </div>
                    </div>
                </div> -->
            </div>
            <div class="main-tabs">
                <div class="chatwith-modal-tab pah-tab">

                    <div class="chatwith-model-head-pah" (click)="showTabData('Profile')"
                        [class.cat__apps__messaging__tab_pah--selected]="optionShow=='Profile'"><i class="fa fa-user"
                            aria-hidden="true"></i> Profile</div>

                    <div *ngFor="let metaData of metaArray" class="chatwith-model-head-pah" id="metaData.id" (click)="showTabData(metaData.id)"
                        [class.cat__apps__messaging__tab_pah--selected]="optionShow==metaData.id">
                        <i class="{{metaData.tabIcon}}" aria-hidden="true"></i>
                        {{metaData.tabName}}</div>
                </div>
            </div>

            <div class="row sub-tabs mt-2" style="padding-left:20px;min-height:550px;">

                <div class="col-md-12" *ngIf="userDetails && optionShow=='Profile'">
                    <app-profile  [user]="userDetails" [patientId]="userPatientId" [dynamicData]="dynamicData" 
                    [worklistList] = "profileTabs" (updateCaregiverDetails)="updateActivityHub($event)"></app-profile>
                </div>
                <ng-container *ngFor="let metaDat of metaArray" class="col-md-12">
                    <div class="col-md-12" *ngIf="optionShow==metaDat.id">
                        <app-dynamic [userDetails]="userDetails" [patientSiteId]="userDatam.siteId"  [userPatientId]="userPatientId" [selectedSiteIds]="siteId"
                            [dynamicData]="dynamicData"></app-dynamic>
                    </div>
                </ng-container>
            </div>
        </section>
      
        <section class="card pah-custom-section" *ngIf="totalPatientCount == 0 && resetPatient == true">
            <div class="row" style="padding-left: 10px; padding-right: 10px;">
                <div class="" style="width:100%;">
                    <div class="row pah-profile-custom">
                        <div class="col-lg-12 pah-profile-details" style="text-align: center;">
                            No patient to see the details.
                        </div>
                    </div>
                </div>
            </div>
            <div class="row sub-tabs mt-2" style="padding-left:20px;min-height: 700px;">                
            </div>
        </section>
        <section class="card pah-custom-section" *ngIf="totalPatientCount > 1 &&  searchText != '' && resetPatient == false && showpat_msg==true" >
            <div class="row" style="padding-left: 10px; padding-right: 10px;">
                <div class="" style="width:100%;">
                    <div class="row pah-profile-custom">
                        <div class="col-lg-12 pah-profile-details" style="text-align: center;">
                            Please choose a patient to see the details.
                        </div>
                    </div>
                </div>
            </div>
            <div class="row sub-tabs mt-2" style="padding-left:20px;min-height: 700px;">                
            </div>
        </section>
    </div>
</div>

  <div class="modal fade forward-modal" id="send-form-modal" tabindex="-1" role="dialog" *ngIf="showFormSend"
        aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" style="max-width: 98% !important;">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="exampleModalLabel">{{ 'TITLES.SEND_FORM' | translate }}</h4>
                    <button type="button" class="close" data-dismiss="modal" attr.aria-label="{{ 'BUTTONS.CLOSE' | translate }}" (click)="closeSendFormModal()">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form_send_form [loadFormFrom]="'PAH'" [selectedPatient]="userDatam" (submitForPAH)="closeSendFormModal()"></form_send_form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="closeSendFormModal()">{{ 'BUTTONS.CLOSE' | translate }}</button>
                </div>
            </div>
        </div>
        </div>
</section>
