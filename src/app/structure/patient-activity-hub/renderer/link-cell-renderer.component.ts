import { Component } from '@angular/core';
import { INoRowsOverlayAngularComp } from 'ag-grid-angular';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { PahService } from '../pah.service';
declare var $: any;
declare var moment: any;
var momentTz = require('moment-timezone');
var jstz = require('jstz');
const timezone = jstz.determine();

@Component({
    selector: 'app-link-cell-renderer',
    template: `
        <span (click)="onClick($event)" style="cursor:pointer;">
        {{params.value}}   
  </span>  
    `
})
export class LinkCellRenderer implements INoRowsOverlayAngularComp {

    public params;
    linkDetails;
    userData;
    lockConfig;
    lockedAt;
    url; 
    fileExt;
    lockedBy;
    patientName;
    data;
    workflowType;
    enableLockingMode;
    lockTime;
    constructor(
        private route: ActivatedRoute,
        private router: Router,
        public _intakeService: PahService
	) {
    }
    agInit(params): void {

        this.params = params; 
        console.log(this.params);
        this.linkDetails = params.linkDetails;
        this.workflowType = params.workflowType;
        this.enableLockingMode = params.enableLockingMode;
        this.lockTime = params.lockTime;
        this.userData = params.userData;
        console.log(this.linkDetails);
        // this._intakeService.cellCount++;
        // if(this._intakeService.cellCount == 1) {
        //     this._intakeService.unlockPatients = [];
        // }
       
    }
    
    onClick($event) {
        console.log($event);
        if (this.linkDetails.onClick instanceof Function ) {
            console.log("hiii click");
            const params = {
                event: $event,
                rowData: this.params.node.data,
                actionField: this.linkDetails.actionFields
            };
            console.log(params);
            let actionFields =[];
            let privileges = this.params.privileges;
            if (
                (this.linkDetails.actionPrivileges != '' && privileges.indexOf(this.linkDetails.actionPrivileges) != -1) ||
                this.linkDetails.actionPrivileges == '' ||
                typeof this.linkDetails.actionPrivileges == 'undefined'
            ) {
                if (this.linkDetails.actionField && this.linkDetails.actionField != '') {
                    actionFields.push({ associatedField: this.linkDetails.actionField, fieldValues: this.linkDetails.fieldValues });
                } else if (this.linkDetails.actionFields) {
                    actionFields = this.linkDetails.actionFields;
                }

                if (actionFields.length > 0) {
                    let loginUserId = this.userData.userId;
                    let loginDisplayName = this.userData.displayName;
                    let trueCount = 0;
                    actionFields.forEach((field) => {
                        if (this.params.node.data.hasOwnProperty(field.associatedField)) {
                            if (
                                field.fieldValues &&
                                field.fieldValues
                                    .split(',')
                                    .findIndex((x) => x.trim().toUpperCase() == this.params.node.data[field.associatedField].toString().trim().toUpperCase()) != -1
                            ) {
                                if (this.linkDetails.showOnlyLoginUser == true) {
                                    if (
                                        this.params.node.data[this.linkDetails.loginUserMatchField] == loginUserId ||
                                        this.params.node.data[this.linkDetails.loginUserMatchField] == loginDisplayName
                                    ) {
                                        trueCount++;
                                    }
                                } else {
                                    trueCount++;
                                }
                            } 
                        }
                        if (field.fieldValues && field.fieldValues == 'Any') {
                            if (this.params.node.data[field.associatedField]) {
                                trueCount++;
                            }
                        }
                        if (field.fieldValues && field.fieldValues == 'not_null') {
                            if (this.params.node.data[field.associatedField] != '') {
                                trueCount++;
                            }
                        }
                    });
                    if(actionFields.length == trueCount) {
                        this.linkDetails.disable = false;
                    } else {
                        this.linkDetails.disable = true;
                    }
                } else {
                    this.linkDetails.disable = false;
                }
            }
            if(this.linkDetails.disable == false) {
                console.log("hii view");
                //this.linkDetails.onClick(params);
                // if(this.linkDetails.callbackfunction == 'viewPrescriptionOrders') {
                //     console.log(this.params.node.data);
                //     this.editDetails(this.params.node.data);
                // }
            }
        }
    }
    editDetails(e){
        console.log("hiii",e);
    // const editRowDetails = e;
    //     console.log(editRowDetails);
        // let currentWidget = localStorage.getItem('currentWidget');
        // this._intakeService.activePatientDetails = e;
        // if(currentWidget != 'All'){
        //     localStorage.setItem('selectedWidget', currentWidget);
        //     localStorage.setItem('currentDashboard', this.route.snapshot.paramMap.get('worklistid'));
        // }
        // this._intakeService.currentPatient = editRowDetails['id'];
            // console.log(e);
    // this.fileExt = e.fileName;
    // var s = e.poFileUrl;
    // console.log(this.fileExt);
    // console.log(s);
    // s = s.substring(0, s.indexOf('?'));
    // this.url = s;
    // if(this.fileExt !== 'document') {
    // $('#prescriptionOrdersModal').modal('show');
    // }
    // }
    // closePrescriptionOrdersModal() {
    // $('#prescriptionOrdersModal').modal('hide');
    // this.url = '';
		// this.router.navigate([
		// 	`apps/${this.route.snapshot.paramMap.get('guid')}/${this.route.snapshot.paramMap.get('worklist')}/details/${this.route.snapshot.paramMap.get('worklistid')}/${editRowDetails['id']}`
		// ]);
    }
    // unlockPatient() {
    //     let variable = {
    //         id: this._intakeService.unlockPatients,
    //         workflowType: this.workflowType
    //     }
    //     console.log(variable);
    //     this._intakeService.unlockPatient(variable).subscribe(({ data: response }) => {
    //         console.log(response);
    //     });
    // }
}
