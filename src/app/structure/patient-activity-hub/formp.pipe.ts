import { Pipe, PipeTransform,Inject,forwardRef } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser'
import { DatePipe } from '@angular/common'; 
import { ScheduledFormComponent } from './../forms/scheduledForms/scheduledForms.citushealth';

@Pipe({ name: 'safeHtml'})
export class SafeHtmlPipe implements PipeTransform  {
  constructor(private sanitized: DomSanitizer) {}
  transform(value) {
    return this.sanitized.bypassSecurityTrustHtml(value);
  }
}

@Pipe({
  name: 'formp'
})
export class FormpPipe implements PipeTransform {
  /*  transform(items: any[], exponent: string): any {
    return items.filter(items => items.signatureStatus === exponent);
  } */

  constructor(public datepipe: DatePipe){}
  transform(input: any): any {
    var shortFormat = (new Date(input).toDateString() === new Date().toDateString()) ? 'hh:mm a' : 'MMM dd hh:mm a';
    var fdate = parseInt(input) * 1;
    return this.datepipe.transform(fdate, shortFormat);
  } 
}
@Pipe({
  name: 'formSearch'
})
export class formSearchPipe implements PipeTransform {
  transform(forms:any, searchKey:any): any {
    if (!forms) { 
      return [];
    } else {
      if(searchKey) {
        return forms.filter(item => item['name'].toLowerCase().indexOf(searchKey.toLowerCase()) != -1);  
      } else {
        return forms;
      }
    }
  }
}
@Pipe({
  name: 'formRecipient',
})
export class formRecipientPipe implements PipeTransform {
  app;
  constructor(@Inject(forwardRef(() => ScheduledFormComponent)) app:ScheduledFormComponent) {
    this.app = app;
  }
  transform(scheduledFormRecipients: any[], filter: any): any {

    if (!scheduledFormRecipients || !filter) {
      return scheduledFormRecipients;
    }
    let filteredItems = scheduledFormRecipients.filter(item =>item.status==filter.status);
    this.app.setRecipientMultipleValue();
    return filteredItems;
    
  }
}
@Pipe({
  name: 'formFilter',
})
export class formFilterPipe implements PipeTransform {
  transform(items: any[]): any {

    if (!items) {
      return items;
    }
    return items.filter(item => item.stafFacing=="false"); 
  }
}

@Pipe({
  name: 'patientRole',
})
export class patientRolePipe implements PipeTransform {
  transform(items: any[]): any {

    if (!items) {
      return items;
    }
    return items.filter(item => item.citus_role_id=='3'); 
  }
}

@Pipe({
  name: 'excludePatientCaregiver',
})
export class exPatientCaregiverPipe implements PipeTransform {
  transform(items: any[]): any {

    if (!items) {
      return items;
    }
    return items.filter(item => item.citus_role_id != '3'); 
  }
}








