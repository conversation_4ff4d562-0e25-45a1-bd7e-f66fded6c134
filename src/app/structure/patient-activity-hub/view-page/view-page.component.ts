import { Component, OnInit, Input, Output, EventEmitter, SimpleChange, ViewChild } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { FormsService } from './../../forms/forms.service';
import { DomSanitizer } from '@angular/platform-browser';
import { analyzeAndValidateNgModules } from '@angular/compiler';
@Component({
  selector: 'app-view-page',
  templateUrl: './view-page.component.html',
  styleUrls: ['./view-page.component.css']
})
export class ViewPageComponent implements OnInit {
  strucuredFormsData;
  dataLoadingMsg = true;
  formName = '';
  constructor(private http: HttpClient,
    private router: Router,
    private route: ActivatedRoute,
    private sanitizer: DomSanitizer,
    private _formsService: FormsService) {

  }
  ngOnInit() {
    this.route.params.subscribe((params: Params) => {
      console.log(params);
      console.log(params.formId);
      this._formsService.getStrucuturedFormResults(params.formId, params.entryId,params.worklistType).then((result) => {
        console.log(result);
        this.strucuredFormsData = result['structuredFormResult'];
        this.formName = result['formName'];
        this.strucuredFormsData.forEach(element => {
          if(element.label.indexOf('{{') != -1) {
            let labelArray = element.label.split('{{');
            element.label = labelArray[0];
          }
        });
        console.log(this.strucuredFormsData);
        this.dataLoadingMsg = false;
      });
    });
  }
}
