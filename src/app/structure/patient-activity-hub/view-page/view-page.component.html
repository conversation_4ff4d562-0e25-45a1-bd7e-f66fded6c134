
  <section class="card" *ngIf="dataLoadingMsg"> 
    <div class="card-block mb-2 mt-2">
      <div class="wait-loading">
        <img src="assets/img/loader/loading.gif" />
      </div>
    </div>
  </section>
  <section class="card" *ngIf="!dataLoadingMsg">
    <div class="card-header">
       <span class="cat__core__title">
        <strong>Form - {{formName}}</strong>
       
         <!-- <a (click)="goToBack()" class="pull-right btn btn-sm btn-primary">Back<i class="ml-1"></i></a>  -->
      </span> 
    </div>
    <div class="card-block">
      <div class="row">
        <div class="col-md-12 row" >
          <table class="table table-hover nowrap" id="example1" width="100%">
            <!-- <thead>
                <tr class="row">
                    <th class="col-sm-1"> </th>
                    <th class="col-sm-6">  </th>
                    <th class="col-sm-5">  </th>
                </tr>
            </thead> -->
            <tbody>
              <tr class="row" *ngFor="let formsData of strucuredFormsData" [hidden]= "formsData.element_css_class.indexOf('hidden') != -1">
                <td class="col-sm-1" *ngIf = "formsData.label != ''" style="border:none;">
                </td>
                <td class="col-sm-6" *ngIf = "formsData.element_type != 'section' && formsData.label != ''" style="border:none;">
                  {{formsData.label}}
                </td>
                <td class="col-sm-5" *ngIf = "formsData.element_type != 'section' && formsData.label != ''" style="border:none;">
                  <label [innerHtml]="formsData.value"></label>
                </td>
                <td colspan="2" class="col-sm-11" *ngIf = "formsData.element_type == 'section' && formsData.label != ''" style="border:none; font-size: 18px;
                font-weight: 600;">
                  {{formsData.label}}
                </td>
                <td colspan="3" class="col-sm-12" *ngIf = "formsData.element_type != 'section' && formsData.label == ''" style="border:none; text-align:center;">
                  <label [innerHtml]="formsData.value"></label>
                </td>
              </tr>
            </tbody>
        </table>
        <div *ngIf="strucuredFormsData.length > 0">
          No data available.
        </div>
        </div>
       
      </div>
    </div>
  
  </section>
  
  <!-- END: tables/datatables -->
