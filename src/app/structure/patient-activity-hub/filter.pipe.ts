import { Pipe, PipeTransform } from '@angular/core';
@Pipe({
  name: 'filter'
}) 
export class FilterPipe implements PipeTransform {
  transform(items: any[], searchText: string): any[] {
    if(!items) return [];
    if(!searchText) return items;
searchText = searchText.toLowerCase();
// return items.filter( it => {
//       return it.toLowerCase().includes(searchText);
//     });
return items.filter(item => item['displayName'].toLowerCase().indexOf(searchText.toLowerCase()) != -1);  
   }
}

@Pipe({
  name: 'SearchFilter'
})
export class AssociatePatientSearchFilterPipe implements PipeTransform {
    transform(searchItems:any, criteria:any): any {
      if (!searchItems)
        return searchItems;
        if(searchItems!=undefined &&  Object.keys(searchItems).length != 0){
        return searchItems.filter(item =>{
           for (let key in item ) {
             if((item[key]+ "").toLowerCase().includes(criteria.toLowerCase())){
                return true;
             }
           }
           return false;
        });
      }
    }
}

