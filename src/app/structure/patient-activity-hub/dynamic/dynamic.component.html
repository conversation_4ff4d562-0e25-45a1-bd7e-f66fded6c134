<style>
  .search-afterfilter {
    padding-right: 30px;
    box-sizing: border-box;
  }

  .data-cnt {
    /* width: 600px;
    margin: auto; */
  }

  .data-cnt label {
    color: #1d6472;
  }

  .data-cnt p {
    color: #818181;
  }

  .data-cnt .form-group {
    clear: both;
  }

  .custom-filter-select {
    -webkit-appearance: menulist !important;
    background-color: white;
    width: 156px;
    height: 27px;
    border-radius: 3px 3px 3px 3px;
    border-color: #d3d3d3a3;
    color: #717070;
    font-size: 12px;
  }
</style>
<section *ngIf="dataLoadingMsg">
  <div class="card-block mb-2 mt-2">
    <div class="wait-loading">
      <img src="assets/img/loader/loading.gif" />
    </div>
  </div>
</section>
<section *ngIf="!dataLoadingMsg">
  <div class="row form-status-custom" id="grid-wrapper">
    <!-- Widgets and Search-->
  <div class="row form-status-view" [hidden]="subHeading == ''">
    <div class="col-sm-12" >
      <label style="border-bottom: 1px solid #dedede;width:100%;">
        <h5>{{subHeading}} <i class="{{subHeadIcon}}" style="color:#5ab7fe;"></i></h5> 
      </label>
    </div>
  </div>
    <div class="row form-status-view" [hidden]="hideWidget == true">
      <div class="col-sm-10">
        <div [hidden]="rowData.length == 0  && rowModelType  !='serverSide'">
          <ng-container *ngFor="let widget of dashboardWidgets">
            <button class="widget-btn" [class.cat__apps__messaging__tab_pah_button--selected]="optionShowButton==widget.widgetValue" (click)="updateColumnData(widget.widgetValue, widget.formField, widget.hideColumns, 'false', widget.count,widget.id)" [class.disable-widget]="disableWidget" [disabled]= "disableWidget"><i
                [ngStyle]="{'color': widget.iconColor}" class="{{widget.displayIcon}} pt-1 pb-1"></i>
              {{widget.count}}
              {{widget.displayText}}</button>
          </ng-container>
        </div>
      </div>
    </div>
    <div class="row form-status-view" >
      <div class="col-md-10">
        <div *ngIf="hasUniqueId" class="mr-3 history-records">
          <label class="mr-3"><strong>Show</strong></label>
          <input type="radio" class="mr-2" name="list" value="listall"
          (click)="showAllEntries('all',$event)" [checked]="showAll">History Records
          <input type="radio" class="mr-2 ml-2" name="list" value="listall"
          (click)="showAllEntries('less',$event)" [checked]="showLess">Most Recent
          </div>
      </div>
      <div class="col-sm-2">
        <div id="btn-action-patient" class="btn-group pull-right mr-4" [hidden]="rowData.length == 0  && rowModelType  !='serverSide'" *ngIf="filterCheck == true">
          <span class="mr-2" style="line-height: 28px;">Filter</span>
          <button aria-expanded="false" [disabled]= "disableWidget" class="btn btn-sm btn-default dropdown-toggle action-btn-user-settings-patient pt-1 pb-1" data-toggle="dropdown" type="button"><i class="fa fa-filter"></i> {{filterLabel}}</button>
          <ul class="dropdown-menu">
            <a class="dropdown-item " href="javascript: void(0);" (click)="updateColumnDataFilter('all','All')">All</a>
            <a class="dropdown-item" href="javascript: void(0);" (click)="updateColumnDataFilter('me',selectName + ' Me')">{{selectName}} Me</a>
            <a class="dropdown-item" href="javascript: void(0);" (click)="updateColumnDataFilter('others',selectName + ' Others')">{{selectName}} Others</a>
          </ul>
        </div>
        <div id="btn-action-patient" class="btn-group" [hidden]="!showCustomExport">
          <button aria-expanded="false" class="btn btn-sm btn-default dropdown-toggle action-btn-user-settings-patient" data-toggle="dropdown" type="button"><i class="fa fa-file-excel-o"></i> Export</button>
          <ul class="dropdown-menu">
            <a class="dropdown-item " href="javascript: void(0);" (click)="exportCSV()">Export CSV</a>
            <a class="dropdown-item" href="javascript: void(0);" (click)="exportExcel('xlsx')">Export Excel(.xlsx)</a>
            <a class="dropdown-item" href="javascript: void(0);" (click)="exportExcel('xml')">Export Excel(.xml)</a>
          </ul>
        </div>
      </div>
    </div>
    <!-- Actions from form worklist -->
    <div class="row form-status-view" style="height: 35px;" >
      <div class="col-md-12">
          <div [hidden]="hideActionButtons == true || singleRowActions.length === 0">
            <label class="mr-2">Actions:</label>
            <span *ngFor="let action of singleRowActions">
              <a class="mr-3 {{action.cssClass}}" [ngClass]="{'disabled':action.disable}" *ngIf="action.type == 'single'" style="color:#0190fe;"
                (click)="singleBtnAction(action.callbackfunction, action.actionLink, action.actionField, action.actionMode, action.selectionModel, action)">
                <i class="{{action.iconClass}} mr-1"></i>{{action.label}}</a>
              <!-- <a class="mr-3 {{action.cssClass}}" [ngClass]="{'disabled':action.disable}" *ngIf="action.type == 'single' && action.downloadStatus" style="color:#0190fe;"
              href="{{action.downloadLink}}" target="_blank" download="{{action.docMessage}}">
                <i class="{{action.iconClass}} mr-1"></i>{{action.label}}</a> -->
            </span>
          </div>
      </div>
    </div>
    <!-- search field-->
    <div class="row form-status-view" >
      <div class="col-md-3">
        <label *ngIf="metaData.showPageSize">Page Size</label>
        <select *ngIf="metaData.showPageSize" (change)="changePaginationSize($event)" class="form-control page-list" [(ngModel)]="pageSize">
            <option *ngFor="let size of pageList" [hidden]="size == ''" value="{{size}}">{{size}}</option>
        </select>
      </div>
      <div class="col-md-9" >
        <div class="pull-right batch-action-search" *ngIf="rowModelType == 'clientSide'">
            <label>Search:</label>
            <label>
                <input type="text" #search class="input-sm form-control" placeholder="Search.."
                    (keyup.enter)="searchFilterData(search.value)" [(ngModel)]="searchFieldText"/>
            </label>
            <label>
                <button class="btn btn-primary btn-sm reset-btn" [disabled]="searchFieldText == ''"
                    (click)="searchFilterData(search.value)">
                    Search
                </button>
                <button class="btn btn-default btn-sm reset-btn" (click)="searchFilterData('')">
                    Reset
                </button>
            </label>
        </div>
        <div class="pull-right" *ngIf="rowModelType == 'serverSide' && filterEnabledFields.length > 0">
            <label>Search:</label>
            <label class="button-group custom-search">
                <button type="button" class="btn btn-default-outline btn-sm dropdown-toggle"
                    style="border-top-right-radius:0px;border-bottom-right-radius:0px;margin-bottom: 0px;"
                    data-toggle="dropdown"><span class="fa fa-search"></span> <span
                        class="caret"></span>
                </button>
                <ul class="dropdown-menu filter-worklist">
                    <li class="" *ngFor="let field of filterEnabledFields;">
                        <span (change)="checkboxSelection(field)">
                            <label>
                              <input class="form-check-input" type="checkbox" id="checkbox{{field.fieldId}}" value="{{field.fieldId}}" (change)="onChangeSearchField($event, field)" [checked]="field.enableAutoSelect"/>&nbsp;{{field.headerName}}
                            </label>
                        </span>
                    </li>
                </ul>
            </label>
            <label>
                <input type="text"
                    style="padding-top: .3rem;padding-bottom: .47rem;margin-left: -14px;border-bottom-left-radius: 0px;border-top-left-radius: 0px;border-color:#d8d8d8;"
                    [(ngModel)]="searchFieldText" class="input-sm form-control" placeholder="Search"
                    (keyup.enter)="searchBasedField(searchFieldText)" />
            </label>
            <label>
                <button class="btn btn-primary btn-sm reset-btn" [disabled]="searchFieldText == '' || selectedSearchFields.length == 0"
                    (click)="searchBasedField(searchFieldText)" style="margin-bottom: 0px;">
                    Search
                </button>
                <button class="btn btn-default btn-sm reset-btn" (click)="clearSearch()" style="margin-bottom: 0px;">
                    Reset
                </button>
            </label>
        </div>
        <div class="pull-right">
          <div id="btn-action-patient" class="btn-group pull-right mb-2" [hidden]="!showStateBtn" style="padding-right: inherit">
            <div class="dropdown">
                <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    {{ 'LABELS.WORKLIST_SESSION_STATE' | translate }}
                </button>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                  <a class="dropdown-item" (click)="saveStatePrefrence()" href="javascript: void(0);">{{ 'BUTTONS.SAVE' | translate }}</a>
                  <a class="dropdown-item" (click)="clearStatePrefrence()" href="javascript: void(0);">{{ 'BUTTONS.RESET' | translate }}</a>
                </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- AG-GRID -->

    <div class="row col-md-12 pah-ag-grid">
      <!-- <span *ngIf="showLoading && rowModelType  !='serverSide'" style="color:teal;text-align: center;padding:20px 20px;">Please wait while we process
        your request... <i class="fa fa-refresh fa-spin" ></i></span> -->
      <ag-grid-angular *ngIf="dataLoadingMsg == false" style="width: 99%; height: 500px;" class="ag-theme-balham" [animateRows]="true"
        [pagination]="pagination" 
        [paginationPageSize]="paginationPageSize" 
        [rowData]="rowData" 
        [columnDefs]="columnDefs" 
        [defaultColDef]="defaultColDef"
        [enableFilter]="true" 
        [enableSorting]="true" 
        [frameworkComponents]="frameworkComponents"
        (gridReady)="onGridReady($event)" 
        [enableColResize]="true" 
        [overlayLoadingTemplate]="overlayLoadingTemplate" 
        [overlayNoRowsTemplate]="overlayNoRowsTemplate"
        (cellClicked)="onCellClicked($event)"
        (columnResized)="onColumnResized($event)" 
        [icons]="icons" 
        [enableRangeSelection]="true" 
        [rowSelection]="rowSelection"
        [autoGroupColumnDef]="autoGroupColumnDef" 
        [groupRowInnerRenderer]="groupRowInnerRenderer"
        [components]="components" 
        [groupSelectsChildren]="groupSelectsChildren" 
        [suppressRowClickSelection]="true" 
        [rowGroupPanelShow]="rowGroupPanelShow"
        (gridSizeChanged)="onGridSizeChanged($event)" 
        (selectionChanged)="onSelectionChanged($event)" 
        [rowModelType]="rowModelType"
        [cacheBlockSize]="cacheBlockSize" 
        [maxBlocksInCache]="maxBlocksInCache" 
        [sideBar]="sideBar" 
        [suppressCsvExport]="suppressCsvExport"
        [suppressExcelExport]="suppressExcelExport"
        (sortChanged)="sortChanged($event)"
        [excelStyles]="excelStyles"
        [context]="metaData.dataSource"
        [suppressNoRowsOverlay]='suppressNoRow'
        (dragStopped)="onColumnDragStopped($event)" 
        (cellValueChanged)="onCellValueChanged($event)"
        (filterChanged)="onFilterChanged($event)"
        (columnPinned)="columnStateChanged()"
        (columnVisible)="columnStateChanged()"
        (columnPivotChanged)="columnStateChanged()">
      </ag-grid-angular>
      <!-- Modal for tagged Messages -->
      <div class="modal fade data-modal" id="data-modal-tagged" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h4 class="modal-title" id="exampleModalLabel">Tagged Messages</h4>
              <button style="float:right;" type="button" class="close" (click)="closeDetailTagView()" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
              <!-- <app-sign-pad (newcomp)="sendSignature($event);"></app-sign-pad> -->
              <app-tagged-forms [tagDetails] = "tagDetails" *ngIf="showTagMsg" (reload)="reloadTaggedMessage()"></app-tagged-forms>
              

            </div>
            <div class="modal-footer">
              <button type="button" [ngClass]="{'btn-default':isApproveShow,'btn-primary':!isApproveShow}" class="btn btn-secondary" (click)="closeDetailTagView()">Close</button>
            </div>
          </div>
        </div>
      </div>
      <!-- <span *ngIf="showNoData && rowModelType  !='serverSide'" style="color:teal;text-align: center;padding:20px 20px;"> There are no {{worklistName.toLowerCase()}} with this patient. </span> -->
    </div>
    <!-- Modal for view Form in Froms -->
    <div class="modal fade data-modal" id="data-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content">
          <div class="modal-header">
              <h4 class="modal-title" id="exampleModalLabel">Form</h4>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">

            <div class="dataFormView">

            </div>

          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
    <!-- Modal -->
    <div class="modal fade data-modal" id="view-form-modal"  tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg">
        <div class="modal-content" *ngIf = "showFormView">
          <div class="modal-header" >
              <h4 class="modal-title" id="exampleModalLabel">Form - {{strucuredFormsName}}</h4>
              <!-- <button type="button" class="close" title="{{toolTipText['closeModal']}}" data-dismiss="modal" aria-label="Close"> -->
            <button title="Close" type="button" class="close"  aria-label="Close" data-dismiss="modal">
               
                <span aria-hidden="true">&times;</span>
              </button>
          </div>
          <div class="modal-body" id="view-body" style="overflow-y: scroll;height: 500px;">
            <table class="table table-hover nowrap" id="view-table" width="100%">
              <!-- <thead>
                  <tr class="row">
                      <th class="col-sm-1"> </th>
                      <th class="col-sm-6">  </th>
                      <th class="col-sm-5">  </th>
                  </tr>
              </thead> -->
              <tbody>
                <tr class="row" *ngFor="let formsData of strucuredFormsData" [hidden]= "formsData.element_css_class.indexOf('hidden') != -1">
                  <td class="col-sm-1" *ngIf = "formsData.label != ''" style="border:none;">
                  </td>
                  <td class="col-sm-6" *ngIf = "formsData.element_type != 'section' && formsData.label != ''" style="border:none;">
                    {{formsData.label}}
                  </td>
                  <td class="col-sm-5" *ngIf = "formsData.element_type != 'section' && formsData.label != ''" style="border:none;">
                    <label [innerHtml]="formsData.value"></label>
                  </td>
                  <td colspan="2" class="col-sm-11" *ngIf = "formsData.element_type == 'section' && formsData.label != ''" style="border:none; font-size: 18px;
                  font-weight: 600;">
                    {{formsData.label}}
                  </td>
                  <td colspan="3" class="col-sm-12" *ngIf = "formsData.element_type != 'section' && formsData.label == ''" style="border:none; text-align:center;">
                    <label [innerHtml]="formsData.value"></label>
                  </td>
                </tr>
              </tbody>
          </table>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
  <div class="modal fade chat-modal" id="chat-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-bg" style="height:600px !important;">
      <div class="modal-content">
        <div class="modal-header">
            <h3 class="modal-title" id="exampleModalLabel">Chat Logs</h3>
            <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="closeChatModel()">
              <span aria-hidden="true">&times;</span>
            </button>
        </div>
        <div class="">
          <app-message-viewer [chatDetails] = "chatDetails" *ngIf="showChat" ></app-message-viewer>
        </div>
        <div class="">
          <button type="button" class="btn btn-secondary pull-right"  data-dismiss="modal" (click)="closeChatModel()">Close</button>
        </div>
      </div>
    </div>
  </div>
  <div class="modal fade edit-modal" id="edit-modal" tabindex="-1" role="dialog" data-backdrop="static" data-keyboard="false" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg" style="height:600px !important;">
          <div class="modal-content">
              <div class="">
                  <edit-form [editDetails]="editDetails" *ngIf="showEdit"></edit-form>
              </div>
              <div class="">
                  <button type="button" class="btn btn-secondary pull-right" data-dismiss="modal" (click)="closeEditModel()">Close</button>
              </div>
          </div>
      </div>
  </div>
  <app-update-assoc-patient *ngIf="showUpdateAssocPatient" [data]="{ documentID: documentID, activeSignedDocument: activeSignedDocument, hidePatientName: true, hideSiteFilter: true }" [showUpdateAssocPatient]="showUpdateAssocPatient" (updateAssocPatient)="onUpdatingAssocPatient($event)"></app-update-assoc-patient>
  <app-form-history [showHistory]="showHistory" [showDraftHistory]="showDraftHistory" [isActive]="formDetails.formStatus" (closeModal)="closeHistoryModal($event)" (closeDraftModal)="closeDraftHistoryModal($event)" [formDetails]="formDetails"></app-form-history>
  <app-resend-document-form *ngIf="showResendModal" [showModal]="showResendModal" (onClose)="popUpRecipientsList()"
  [getRecipientsFor]="contentType" [patientId]="userPatientId" [extraData]="{ admissionId: userPatientId && ((activeStrucuredForms && activeStrucuredForms.admission_id) || (activeSignedDocument && activeSignedDocument.admissionId)) }" [entityId]="entityId" 
  (eventEmitterSelectedRecipients)="resendToRecipients($event)">
  </app-resend-document-form>
  </div>
  <div class="modal refill-modal" id="refillEditModal" data-backdrop="static" tabindex="-1" role="dialog"
    aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-bg">
      <div class="modal-content">
          <div class="modal-header">
              <h4 class="modal-title" id="exampleModalLabel">{{worklistName}}</h4>
              <!-- <button type="button" class="close" title="{{toolTipText['closeModal']}}" data-dismiss="modal" aria-label="Close"> -->
            <button title="Close" type="button" class="close"  aria-label="Close" data-dismiss="modal">
              
                <span aria-hidden="true">&times;</span>
              </button>
              </div>
        <div class="modal-body" >
          <app-patient-intake-form [refillEditVariable]="refillEditVariable" [hideBreadcrumb]="hideBreadcrumb" *ngIf="showRefillEdit">
          </app-patient-intake-form>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary pull-right" data-dismiss="modal"
            (click)="closeRefillEditModalal()">Close</button>
        </div>
      </div>
    </div>
  </div>
    <div class="modal edoc-modal" id="prescriptionOrdersModal" data-backdrop="static"
     tabindex="-1" role="dialog"
    aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" style="max-width:1024px;">
      <div class="modal-content">
        <div class="modal-header">
          <h4 class="modal-title" id="exampleModalLabel">View Prescription Orders</h4>
          <button title="Close" type="button" class="close" data-dismiss="modal" 
          aria-label="Close"
            (click)="closePrescriptionOrdersModal()">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body" style="min-height: 500px;">
            <!-- <div *ngIf="fileExt == 'image'">
              <img src="{{url}}" class="responsive" width="600" height="400">
            </div> -->
            <div>
           <ngx-doc-viewer url="{{url}}" viewer="url" style="width:100%;height:93vh;">
            </ngx-doc-viewer>
          </div>
         
          </div>
          <div class="modal-footer">
              <button type="button" class="btn btn-secondary note-close-btn" (click)="closePrescriptionOrdersModal()">Close</button>
              
            </div>
      </div>
    </div>
  </div>
  <div class="modal edoc-modal" id="docModal" data-backdrop="static" tabindex="-1" role="dialog"
  aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" style="max-width:1024px;">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="exampleModalLabel">View Docs</h4>
        <button title="Close" type="button" class="close" data-dismiss="modal" aria-label="Close"
          (click)="closeDocModal()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" style="min-height: 250px;">
        <!-- <div *ngIf="fileExt == 'image'">
          <img src="{{url}}" class="responsive" width="600" height="400">
        </div> -->
        <div>
          <ngx-doc-viewer url="{{url}}" viewer="url" style="width:100%;height:93vh;">
          </ngx-doc-viewer>
        </div>
      <!-- <div *ngIf= "url == ''">
          <label>No attachment found.</label>
      </div> -->
      </div>
      <div class="modal-footer">
          <button type="button" class="btn btn-secondary note-close-btn" (click)="closeDocModal()">Close</button>
          
        </div>
    </div>
  </div>
</div>
<!-- </div> -->
<modal class="loading-container" [show-modal]="isModalOpen" [title]="'View doc'" [sub-title]="subTitle" [modal-retry]=true
    [modal-body]="modalBody" [show-header]="showHeader" [show-footer]="showFooter" [cancel-label]="cancelLabel"
    (closed)="closeModal()" [show-iframe]="showIframe">
</modal>
</section>
