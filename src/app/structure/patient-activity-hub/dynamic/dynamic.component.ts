import { Component, OnInit, OnDestroy, Input, Output, EventEmitter, SimpleChange, ViewChild } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { WorkListService } from './../../worklists/worklist.service';
import { FormGroup } from '@angular/forms';
import { DatePipe } from '@angular/common';
import { LinkCellRenderer } from '../renderer/link-cell-renderer.component';
import { IconCellRenderer } from './../../worklists/renderer/icon-cell-renderer.component';
import { SignPadComponent } from './../../shared/signPad/sign-pad.component';
import { Router, ActivatedRoute } from '@angular/router';
import { StructureService } from './../../structure.service';
import { InboxService } from './../../../structure/inbox/inbox.service';
import { userFilterPipe } from './../../../structure/inbox/inbox-modal-filter';
import { SharedService } from '../../../structure/shared/sharedServices';
import { FormsService } from './../../forms/forms.service';
import { DomSanitizer } from '@angular/platform-browser';
import { FormpPipe } from './../formp.pipe';
import { SignService } from './../../signatures/sign.service';
import { LicenseManager } from "ag-grid-enterprise";
import { ToolTipService } from './../../tool-tip.service';
import { WorklistIndexdbService } from './../../worklists/worklist-indexdb.service';
import { PatientActivityHubComponent } from '../patient-activity-hub.component';
import 'ag-grid-enterprise';
import 'ag-grid-angular';
import { PahService } from '../pah.service'; 
import { Modal } from '../../education/educationModal';
import { isBlank } from 'app/utils/utils';
import { CONSTANTS,Status, FormType, NOTIFY_DELAY_TIME_COMMON, FormSendMode, DataLoadingType, IntegrationType } from 'app/constants/constants';
import { RecipientList } from "../../shared/resend-document-form-modal/resend-document-form-modal.interface";
import { PdfViewService } from 'app/structure/pdfview.service';
import { FilterOptions, FilterTypes } from 'app/constants/constants';
import { Store, StoreService } from 'app/structure/shared/storeService';
const jstz = require('jstz');
declare const moment: any;
declare const swal: any;
declare const $: any;
declare const NProgress: any;
const timezone = jstz.determine();
import { NGXLogger } from 'ngx-logger';
import { Subscription } from 'rxjs';
import { APIs } from 'app/constants/apis';
@Component({
  selector: 'app-dynamic',
  templateUrl: './dynamic.component.html',
  styleUrls: ['./dynamic.component.css'],
  providers: [FormpPipe, DatePipe]
})

export class DynamicComponent implements OnInit, OnDestroy {

  @Input('dynamicData') dynamicData: any;
  //Input to Get Variables from Parent Component
  @Input('userDetails') userDetails;
  @Input('userPatientId') userPatientId;
  @Input('selectedSiteIds') selectedSiteIds;
  @Input('patientSiteId') patientSiteId;
  @Output() referalDetails: EventEmitter<any> = new EventEmitter<any>();
  gridApi;
  hideWidget  = false;
  gridColumnApi;
  overlayLoadingTemplate;
  overlayNoRowsTemplate;
  reminderDetailsLength = 0;
  toolTipText: any;
  widgetData =[];
  senderId;
  initiatorFromLogs;
  userDet;
  userId;
  pageList = [];
  pageSize;
  docType;
  parameters;
  selectedfilterval;
  displayname;
  reverseFlag;
  selectedRowDetails;
  strucuredForms;
  completedCount;
  isActive;
  strucuredFormsCopy;
  strucuredFormsPendingArchived;
  archiveCount;
  pendingCount;
  pendingall;
  strucuredFormsPending;
  strucuredFormsArchive;
  actualstrucuredForms;
  value;
  buttonName;
  created_date;
  allow_edit;
  formStatus;
  form_submission_id;
  form_id;
  activeStrucuredForms;
  activeTaggedForm;
  taggedFormsList;
  optionShowButton;
  folderLists;
  folderName;
  folderNameDefault;
  filingCenters;
  showClose;
  isFilingCenter;
  isDetail = false;
  fileNameFormat;
  dob;
  createdOn;
  patientName;
  tagsList: any = [];
  noTags;
  dateOfBirth;
  isApproveShow;
  imgUrl;
  pdfUrl;
  createdBy;
  approvedBy;
  approveSignUrl;
  sendType;
  description;
  msgId;
  tags;
  signature;
  moveFilingCenter;
  folderType;
  filename;
  filterField;
  filterCheck;
  cellClickFn;
  url; 
  fileExt;
  caregiverfname;
  caregiverlname;

  patientAssociateRole;
  rowGroupPanelShow;
  rowData: any = [];
  columnDefs: any = [];
  icons;
  frameworkComponents: any;
  dashboardWidgets: any;
  reportFields: any;
  actionFields: any;
  defaultColDef: any;
  formRowData = [];
  formId: number;
  worklistDetails: any;
  formDataList = [];
  heading: string;
  //helpMsg: string;
  //addNewBtn = true;
  //addNewBtnText: string;
  //addNewBtnLink: string;
  worklistName: string;
  worklistid: any;
  dataLoadingMsg: boolean;
  structureFormContent: any = '';
  uniqueClass = '';
  showAll = false;
  showLess = true;
  hasUniqueId;
  staffList = [];
  clinicalUserDetails: any;
  staffLanguage = '';
  selectedEntry = '';
  searchTexts = '';
  singleActionButtons = [];
  singleRowAllActions = [];
  singleRowActions = [];
  multipleActionButtons = [];
  batchSingleActionButtons = [];
  batchMultipleActionButtons = [];
  autoGroupColumnDef;
  groupRowInnerRenderer;
  components;
  staffNames = [];
  machFormIds = [];
  machFormKeys = [];
  sideBar: any;
  assignType = 'single';
  modalType = 'staff';
  nurseList = [];
  list = [];
  refillDateType = 'due';
  selectedDays = '';
  updateData = [];
  shortLinkActions = [];
  pivotPanelShow;
  rowModelType;
  cacheBlockSize;
  maxBlocksInCache;
  pagination = true;
  paginationPageSize = 0;
  btnBehaviour = true;
  hideActionButtons = true;
  rowCount = 0;
  
  userData:any = {};
  actionColumnPosition = '';
  formFieldFrom = '';
  metaData;
  rowSelection = '';
  // colState;
  // groupState;
  // sortState;
  // filterState;
  formField;
  hideBulkEdit = true;
  bulkEditFields = [];
  questions: any[];
  form: FormGroup;
  enableBulkEdit = false;
  fieldValueList = [];
  updateProperty = '';
  formFiles = [];
  fileContent;
  selectName;
  showLoading = false;
  showNoData = false;
  mapFields = [];
  linkFields = [];
  documentID;
  datalist: any = [];
  totalRowData = [];
  hideBreadcrumb= false;
  activeSignedDocument;
  archiveDocumentList = [];
  SignedDocumentList = [];
  pendingDocumentList = [];
  chatDetails = {};
  editDetails = {};
  showChat = false;
  showEdit = false;
  disableWidget = true;
  filterText = '';
  previlages;
  suppressExcelExport = false;
  suppressCsvExport = false;
  previousHideColumns = [];
  hideColumns = [];
  associatePatientId;
  showCustomExport = false;
  excelStyles;
  columnWidth;
  totalCount = 0;
  groupSelectsChildren;
  filterLabel = 'All';
  reminderDetailsLoaded: boolean;
  reminderDetails: any = [];
  tagDetails = {
    show: false,
    activeForm: ''
  };
  showTagMsg = false;
  filterEnabledFields = [];
  selectedSearchFields = [];
  customSearch = false;
  searchFieldText = '';
  savedSearchText = '';
  selectedWidgetField = '';
  selectedWidget = '';
  userDataConfig: any;
  strucuredFormsData;
  strucuredFormsName = '';
  showFormView = false;


  enableCellEditHistory = '';
  formMetaData;
  selectedLoadingType = '';
  dashboardWidgetField = '';
  widgetCounts = [];
  refillEditVariable = {};
  showRefillEdit = false;
  suppressNoRow = true;
  clickhistoryFilter = false;

  subHeading = '';
  subHeadIcon = '';
  allMessageTemplate = [];
  formName: string;

  pdfOrDocSrc = '';
  isModalOpen = false;
  cancelLabel = false;
  showIframe = false;
  modalBody;
  showHeader = false;
  showFooter = false;
  title = '';
  pdfOrDoc = "";
  showHistory: Boolean = false;
  showDraftHistory: Boolean = false;
  showHistoryModal: Boolean = false;
  formDetails = {};

  showResendModal = false;
  contentType = '';
  entityId = '';
  showUpdateAssocPatient = false;
  private socketEventSubscriptions: Subscription[] = [];
  userPreferenceId = 0;
  defaultColumnState;
  defaultSortState;
  defaultFilterState;
  defaultSelectedSearchFields = [];
  selectedWidgetId = 0;
  worklistState: any;
  setColumnState = true;
  setSortState = false;
  setFilterState = false;
  showStateBtn = false;
  selectedPreviousWidget = '';
  dashboardWidgetFieldId = '';
  setSessionState = false;
  constructor(
    private http: HttpClient,
    private _workListService: WorkListService,
    private router: Router,
    private _structureService: StructureService,
    private _pahService: PahService,
    private route: ActivatedRoute,
    private formpPipe: FormpPipe,
    private _ToolTipService: ToolTipService,
    private sanitizer: DomSanitizer,
    public _inboxService: InboxService,
    private datePipe: DatePipe,
    private _formsService: FormsService,
    public modalFilter: userFilterPipe,
    private _sharedService: SharedService,
    private signService: SignService,
    private modals: Modal,
    public _worklistIndexdbService: WorklistIndexdbService,
    private PdfViewService : PdfViewService,
    private storeService: StoreService,
    private ngxLogger: NGXLogger ) {
      this.userData = JSON.parse(this._structureService.userDetails);
      console.log('this.userData-->'+this._structureService.userDetails);
    // Set License key for ag grid enterprose version  
    let agGridLicenseKey = !isBlank(this._structureService.agGridLicenceKey) ? atob(this._structureService.agGridLicenceKey) : this._structureService.agGridLicenceKey;
    if (agGridLicenseKey) {
      LicenseManager.setLicenseKey(agGridLicenseKey);
    }
    /**ag-grid loading message */
    this.overlayLoadingTemplate =
      '<span class="ag-overlay-loading-center">Please wait while we process your request</span>';
    this.overlayNoRowsTemplate = "<span>No data available to show.</span>";
    this.frameworkComponents = {
      iconCellRenderer: IconCellRenderer,
      linkCellRenderer: LinkCellRenderer
    };
    this.groupRowInnerRenderer = "groupRowInnerRenderer";
    /**Set icons of ag-grid */
    // this.icons = {
    //   filter: ' '
    // };
    /**Default configuration of column */
    this.defaultColDef = {
      resizable: true,
      enableValue: true,
      enableRowGroup: true,
      //cellClass: 'ag-grid-cell',
      headerComponentParams: {
        template:
          '<div class="ag-cell-label-container" role="presentation">' +
          '  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>' +
          '  <div ref="eLabel" class="ag-header-cell-label" role="presentation">' +
          '    <span ref="eSortOrder" class="ag-header-icon ag-sort-order" ></span>' +
          '    <span ref="eSortAsc" class="ag-header-icon ag-sort-ascending-icon" ></span>' +
          '    <span ref="eSortDesc" class="ag-header-icon ag-sort-descending-icon" ></span>' +
          '    <span ref="eSortNone" class="ag-header-icon ag-sort-none-icon" ></span>' +
          '    <span ref="eText" class="ag-header-cell-text" role="columnheader" ' +
          '    style="font-family: PT Sans, sans-serif;font-size: 1.02rem;' +
          '    font-weight: bold;color:#514d6a"></span>' +
          '    <span ref="eFilter" class="ag-header-icon ag-filter-icon"></span>' +
          '  </div>' +
          '</div>'
      },
      autoHeight: true
    };

    // this.helpMsg = 'Click on a tab to see the Patient Details under a category';
    // this.addNewBtn = true;
    // this.addNewBtnText = 'Add New';

    this.autoGroupColumnDef = {
      headerName: 'Group',
      width: 200,
      cellRenderer: "agGroupCellRenderer",
      cellRendererParams: { checkbox: true }
    };
    this.components = { datePicker: this.getDatePicker() };
  }
  setheight(event) {
    try {
      if (event.data) {
        if (event.data["AllowEditFormSubmitted"] && event.data["AllowEditFormSubmitted"] == true) {
          this.ngxLogger.log('submit form after allow edit', event.data);
          $("#textmessage").text("");
          $("#newloader").hide();
          $('#edit-modal').modal('hide')
          var datasourcee = ServerSideDatasourceApi(this, '');
          this.gridApi.setServerSideDatasource(datasourcee);
          this.gridApi.deselectAll();
          window.removeEventListener("message", this.setheight);
          this.showEdit = false;
          this._structureService.pahEdit=false;
          this._structureService.notifyMessage({
            messge: 'Your form has been sent to Completed',
            type: 'success',
            allow_dismiss: true,
            delay: 0
          });
        }
        if (event.data.toString().includes('=')) {
          var height = (event.data).split('=');
          if (height.length && height[0] == 'scrollTopformsubmit') {
            $('#refillEditModal').scrollTop(0);
            //$('.structured-form-dekstop').scrollTop(0);
          }
        }
      }
    } catch (error) {
      console.log(error);
    }
  }
  @ViewChild(SignPadComponent) childpad: SignPadComponent;
  // Onclick each Patient from left Menu Change all data in the page 
  ngOnChanges(changes: { [propKey: string]: SimpleChange }) {
    if (changes['dynamicData']) {
      //this.showLoading = true;
      this.showNoData = false;
      this.userDet = this.userDetails;
      this.userId = this.userPatientId;
      this.suppressNoRow = true;
      this.worklistName = this.dynamicData.tabName;
      this.worklistid = this.dynamicData.pahworklistId;
      this.optionShowButton = 'All';
      this.hideActionButtons = true;
      this.metaData = this.dynamicData.filterPahDetails[0];
      let metaData = this.metaData;
      this._structureService.activePatientActivityHubTabId = this.dynamicData.pahworklistId;
      this._structureService.activePatientActivityHubPatientId = this.userPatientId;
      this._structureService.activePatientActivityHubFilterPahDetails = this.dynamicData;
      const index = this._structureService.activePatientActivityDetails.findIndex(x => x.tabId == this.dynamicData.pahworklistId);
      this.cacheBlockSize = metaData.InitCountVal;
      this.maxBlocksInCache = metaData.blocksize;
      this.paginationPageSize = Number(metaData.pageSize);

      if (this.dynamicData && this.dynamicData.tabName) {
        this.worklistName = this.dynamicData.tabName;
        this.worklistid = this.dynamicData.pahworklistId;
        if (metaData.rowModel == 'server') {
          this.rowModelType = "serverSide";
          if (metaData.dataLoadingType && metaData.dataLoadingType == 'pagination') {
            this.pagination = true;
            this.paginationPageSize = Number(metaData.pageSize);
          } else {
            this.pagination = false;
          }
        } else {
          this._structureService.activePatientActivityDetails.push({
            'tabId': this.dynamicData.pahworklistId
          });
          this.rowModelType = "clientSide";
        }
        if(this.dynamicData.reloadTab == true) {
          this.getformData();
        } else {
          this.dataLoadingMsg = true;
          this.getWorklistdetails();
        }
        
      }
      //}
    }
  }
  refreshFormAfterEdit(){
    $("#textmessage").text("");
    $("#newloader").hide();
    $('#edit-modal').modal('hide')
    var datasourcee = ServerSideDatasourceApi(this, '');
    this.gridApi.setServerSideDatasource(datasourcee);
    this.gridApi.deselectAll();
    this.showEdit = false;
    this._structureService.pahEdit=false;
  }
  ngOnInit() {
    
    console.log(this.userDetails);
    window.addEventListener("message", this.setheight.bind(this));
    this.socketEventSubscriptions.push(
      this._structureService.subscribeSocketEvent('submittedData').subscribe((data) => {
        this.ngxLogger.log('submitted Data -Edit', data);
        this.refreshFormAfterEdit();
      })
    );
    this.socketEventSubscriptions.push(
      this._structureService.subscribeSocketEvent('saveAsDraft').subscribe((data) => {
        this.ngxLogger.log('Save as draft-Edit', data);
        this.refreshFormAfterEdit();
      })
    );
    this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
    $('#sfilingCenterss').on(
      'change',
      (e) => {
        this.showClose = true;
        this.folderName = { folderName: $('#sfilingCenterss').val(), type: 'OUTGOING' }

      }
    );
    this.displayname = this._structureService.getCookie('displayname');
    /**Avoid duplicate entry while edit machform- authorization */
    
    const userdata = JSON.parse(this._structureService.userDetails);
    const formsUrl = this._structureService.machFormUrl + APIs.machFormIndex + '?fromCallBell=1&tenantId=' + this._structureService.getCookie("crossTenantId") + '&userId=' + userdata.userId;
    const iFrameContent = '<iframe onload="javascript:parent.scrollTo(0,0);" height="10" allowTransparency="true" frameborder="0" scrolling="no" style="width:100%;border:none" src="' + formsUrl + '" ></iframe>';
    this.structureFormContent = this.sanitizer.bypassSecurityTrustHtml(iFrameContent);
    /**For checking whether add cancel form action or not */
    this.previlages = [];
    let manageTenants = this.userData.privileges;
    manageTenants = typeof (manageTenants) === 'undefined' ? this._structureService.getCookie('userPrivileges') : manageTenants;
    manageTenants = manageTenants.split(',');
    for (var i = 0; i < manageTenants.length; i++) {
      this.previlages[manageTenants[i]] = true;
    }
    this.excelStyles = [
      {
        id: "header",
        interior: {
          color: "#CCCCCC",
          pattern: "Solid"
        }
      }
    ];
  }
  ngOnDestroy() {
    /**Unsubscribe all the socket event subscriptions */
    this.socketEventSubscriptions.forEach(subscription => {
        if(subscription) subscription.unsubscribe();
    });
  }
  ngAfterViewInit() {
    // this returns null
  }
  onColumnResized(event) {
    if (event.finished) {
      this.gridApi.resetRowHeights();
    }
  }
  /**For make the ag grid responsive */
  onGridSizeChanged(params) {
    var gridWidth = document.getElementById("grid-wrapper").offsetWidth;
    var columnsToShow = [];
    var columnsToHide = [];
    var totalColsWidth = 0;
    var allColumns = params.columnApi.getAllColumns();
    for (var i = 0; i < allColumns.length; i++) {
      let column = allColumns[i];
      totalColsWidth += column.getMinWidth();
      if (totalColsWidth > gridWidth) {
        columnsToHide.push(column.colId);
      } else {
        columnsToShow.push(column.colId);
      }
    }
    params.api.sizeColumnsToFit();
  }
  /**Grid ready function */
  onGridReady(params) {
    autosizeHeaders(params);
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    /**Show our own loading message */
    this.gridApi.showLoadingOverlay();
    setTimeout(function () {
      params.api.resetRowHeights();
    }, 500);
    window.addEventListener('resize', function () {
      setTimeout(function () {
        params.api.sizeColumnsToFit();
        params.api.resetRowHeights();
      });
    });
    let self = this;
    setTimeout(function () {
      self.reportFields.forEach(element => {
        if (element.clearFilter == true) {
          let filterInstance = self.gridApi.getFilterInstance(element.fieldName);
          if (filterInstance) {
            filterInstance.eClearButton.addEventListener("click", () => {
              self.gridApi.onFilterChanged();
            });
          }
        }
      });
    }, 5000);
    this.gridApi.setHeaderHeight(35);
    this.gridApi.setColumnDefs(this.columnDefs);
    this.gridApi.sizeColumnsToFit();
    if (this.metaData.enableSavingState) {
      this.defaultColumnState = this.gridColumnApi.getColumnState();
      this.defaultSortState = this.gridApi.getSortModel();
      this.defaultFilterState = this.gridApi.getFilterModel();
      const index = this._sharedService.worklistState.findIndex(worklist => 
        +worklist.worklistId === +this.worklistid && +worklist.widgetId === +this.selectedWidgetId && worklist.worklistSource === 'PAH');
      if(index === -1) {
        if(this.metaData.stateSavingPreference && this.metaData.stateSavingPreference === 'userProfile') {
          const variable : any = {userId: Number(this.userData.userId), object_id: Number(this.worklistid)};
          this._workListService.getWorklistStatePreference(variable).then((data)=> {
            if(data['getSessionTenant'] && data['getSessionTenant'].getUserPeference) {
              const response = data['getSessionTenant'].getUserPeference;
              if(response.status === 200) {
                if(response.data && response.data.length > 0) {
                  //If widget level preference is enabled, get the details for the default widget
                  if(this.metaData.widgetState) {
                    const widgetPreferrence = response.data.find(widget => widget.widgetId ===  this.selectedWidgetId);
                    response.data.forEach((item) => {
                      this._sharedService.userWorklistPrefrenceIds.push({worklistId: item.object_id,
                        userPreferenceId: item.id, widgetId: item.widgetId});
                      this._sharedService.worklistState.push({worklistId: item.object_id,
                        worklistState: JSON.parse(item.meta), widgetId: item.widgetId, worklistSource: 'PAH'});
                    });
                    if(!isBlank(widgetPreferrence)) {
                      this.worklistState = JSON.parse(widgetPreferrence.meta);                
                      this.userPreferenceId = widgetPreferrence.id;
                      this.setStatePreferencetoGrid(this.worklistState,'userProfile', variable.object_id);
                    } else {
                      this.getformData();
                    }                    
                  } else {
                    this.worklistState = JSON.parse(response.data[0].meta);                  
                    this.userPreferenceId = response.data[0].id;
                    this._sharedService.userWorklistPrefrenceIds.push({worklistId: variable.object_id,
                    userPreferenceId: response.data[0].id, widgetId: this.selectedWidgetId});
                    this.setStatePreferencetoGrid(this.worklistState,'userProfile', variable.object_id);
                    
                  }                 
                } else {
                  this.getformData();
                }
              } else {
                this.getformData();
              }
            } else {
              this.getformData();
            }
          },()=> {
            this.getformData();
          });
        } else {
          this.getformData();
        }
      } else {
        const pIndex = this._sharedService.userWorklistPrefrenceIds.findIndex(item => 
          item.worklistId === +this.worklistid && item.widgetId === this.selectedWidgetId);
        if(pIndex !== -1) {
          this.userPreferenceId = this._sharedService.userWorklistPrefrenceIds[pIndex].userPreferenceId;
        }
        if (!isBlank(this.storeService.getStoredData(Store.SELECTED_FORM_TAB))) {
          const widgetIndex = this.dashboardWidgets.findIndex(widget => widget.widgetValue === this.storeService.getStoredData(Store.SELECTED_FORM_TAB));
          this.storeService.removeData(Store.SELECTED_FORM_TAB);
          this.optionShowButton = this.dashboardWidgets[widgetIndex].widgetValue;
          this.updateColumnData(this.dashboardWidgets[widgetIndex].widgetValue, this.dashboardWidgets[widgetIndex].formField, this.dashboardWidgets[widgetIndex].hideColumns, true, this.dashboardWidgets[widgetIndex].count, this.dashboardWidgets[widgetIndex].id);
        } else {
          this.setStatePreferencetoGrid(this._sharedService.worklistState[index].worklistState,'cache',this.worklistid);
        }
      }
    } 
    else {
      this.getformData();
    }
  }
 
  /**Get worklist details from cache */
  async getCacheData(worklistid) {
    console.log('Time: start cache checking', moment().format('LTS') +' ' + moment().millisecond());
    if (this._pahService.browserCache == 'localstorage') {
      if (localStorage.getItem('worklistDetails') && localStorage.getItem('worklistDetails') != '') {
        let details = JSON.parse(localStorage.getItem('worklistDetails')).filter(x => Number(x.worklistId) == Number(worklistid) && x.edited == false);
        return details.length > 0 ? details[0].worklistDetails : [];
      } else {
        return [];
      }
    } else if (this._pahService.browserCache == 'indexdb') {
      let dataFromIdb: any = [];
      await this._worklistIndexdbService.getByKeyFromIDb(this._workListService.indexDbConfig).then(
        (response) => {
          dataFromIdb = response;
        });
      if (dataFromIdb && !dataFromIdb.length) {
        return [];
      } else {
        let details = dataFromIdb.filter(x => Number(x.worklistId) == Number(worklistid) && x.edited == false);
        return details.length > 0 ? details[0].worklistDetails : [];
      }
    } else if(this._pahService.browserCache == 'sharedvariable') {
      const defIndex = this._sharedService.worklistDetails.findIndex(x => x.worklistId == this.worklistid && x.edited == false);
      if (defIndex == -1) {
        return [];
      } else {
        return this._sharedService.worklistDetails[defIndex].worklistDetails;
      }
    }
  }
  /**Set worklist details to the cache */
  setCacheData(worklistid, worklistDetails) {
    console.log('Time: set cache start', moment().format('LTS') +' ' + moment().millisecond());
    var promise = new Promise((resolve, reject) => {
      let worklistData = [{ 'worklistId': worklistid, 'worklistDetails': worklistDetails, 'edited': false }];
      if (this._pahService.browserCache == 'localstorage') {
        if (localStorage.getItem('worklistDetails') && localStorage.getItem('worklistDetails') != '') {
          let details = JSON.parse(localStorage.getItem('worklistDetails'));
          let index = details.findIndex(x=> x.worklistId == worklistid);
          if(index != -1) {
            details[index].worklistDetails = worklistDetails;
            details[index].edited = false;
          } else {
            details.push({ 'worklistId': worklistid, 'worklistDetails': worklistDetails, 'edited': false });
          }
          localStorage.setItem('worklistDetails', JSON.stringify(details));
        } else {
          localStorage.setItem('worklistDetails', JSON.stringify(worklistData));
        }
      } else if (this._pahService.browserCache == 'indexdb') {
        let dataFromIdb: any = [];
        this._worklistIndexdbService.getByKeyFromIDb(this._workListService.indexDbConfig).then(
          (response) => {
            dataFromIdb = response;
            if (dataFromIdb && !dataFromIdb.length) {
              this._worklistIndexdbService.addToIDb(this._workListService.indexDbConfig, worklistData);
            } else {
              let details = dataFromIdb;
              let index = details.findIndex(x=> x.worklistId == worklistid);
              if(index != -1) {
                details[index].worklistDetails = worklistDetails;
                details[index].edited = false;
              } else {
                details.push({ 'worklistId': worklistid, 'worklistDetails': worklistDetails, 'edited': false });
              }
              this._worklistIndexdbService.addToIDb(this._workListService.indexDbConfig, details);
            }
          });
      } else if(this._pahService.browserCache == 'sharedvariable') {
        console.log('worklist id', worklistid);
        console.log('details', worklistDetails);
        const defIndex = this._sharedService.worklistDetails.findIndex(x => x.worklistId == worklistid);
        console.log('defindex', defIndex);
        if (defIndex == -1) {
          this._sharedService.worklistDetails.push({ 'worklistId': worklistid, 'worklistDetails': worklistDetails, 'edited': false });
        } else {
          this._sharedService.worklistDetails[defIndex].worklistId = worklistid;
          this._sharedService.worklistDetails[defIndex].worklistDetails = worklistDetails;
        }
      }
      resolve();
      console.log('Time: set cache timeout', moment().format('LTS') +' ' + moment().millisecond());
    });
    console.log('Time: set cache end', moment().format('LTS') +' ' + moment().millisecond());
    return promise;
  }
  /**Get the configuration details of worklist and build ag grid */
  getWorklistdetails() {
    this.columnDefs = [];
    this.heading = '';
    this.formId = 0;
    this.dashboardWidgets = [];
    this.filterField = '';
    this.selectName = '';
    //this.rowData = [];
    //this.addNewBtnLink = '';
    this.uniqueClass = '';
    this.batchMultipleActionButtons = [];
    this.actionFields = [];
    this.batchSingleActionButtons = [];
    this.singleRowActions = [];
    this.singleRowAllActions = [];
    this.btnBehaviour = true;
    this.hideActionButtons = true;
    this.formMetaData = [];
    this.filterEnabledFields = [];
    this.formName = '';
    this._structureService.notifySearchFilterApplied(false);
    this.getCacheData(this.worklistid).then((data) => {
      console.log('Time: end cache checking', moment().format('LTS') +' ' + moment().millisecond());
      this.worklistDetails = data;
      console.log('Time: worklist details', this.worklistDetails);
      if (this.worklistDetails.length == 0) {
        this._workListService.getWorklistDetails(this.worklistid).then((data) => {
          if (data['getSessionTenant']) {
            console.log('Time: get worklist details from api', moment().format('LTS') +' ' + moment().millisecond());
            this.worklistDetails = data['getSessionTenant'].formWorklists;
            this.setCacheData(this.worklistDetails[0].id, this.worklistDetails).then(()=>console.log('cache'));
            console.log('Time: next line call set cache', moment().format('LTS') +' ' + moment().millisecond());
            this.getWorklistMetaDetails();
          }
        });
      } else {
        console.log('Time: get worklist details from cache', moment().format('LTS')+' ' + moment().millisecond());
        this.getWorklistMetaDetails();
      }
    });
    
  }
  getWorklistMetaDetails() {
    /**Get details of particular worklist */
        let metaData;
        console.log('worklistDetails'); console.log(this.worklistDetails);

        this.heading = this.worklistDetails[0].name;
        if (this.worklistDetails[0].reportForms.length > 0) {
          this.formId = this.worklistDetails[0].reportForms[0].id;
        }
        let columnObj = {};
        this.actionFields = [];
        /**Get the meta data of worklist*/
        let newJson = this.worklistDetails[0].description;
        newJson = newJson.replace(/'/g, '"');
        metaData = JSON.parse(newJson);
        this.metaData = metaData;
        this.filterCheck = this.metaData.enableDynamicFilter;
        /**Condition for showing the button to save ag grid state */
        if (this.metaData.stateSavingMode == 'manualSave' && this.metaData.enableSavingState &&
          (this.metaData.sortState || this.metaData.filterState || this.metaData.columnState || this.metaData.searchState)) {
          this.showStateBtn = true;
        } else {
          this.showStateBtn = false;
        }
        if (this.filterCheck == true) {
          this.selectName = this.metaData.prefixText;
          this.filterField = this.metaData.filterField;
        }
        if(this.metaData.callbackFunctionName == 'documents') {
          this.subHeading = 'My Documents';
          this.subHeadIcon = 'fa fa-file';
        }
        this.hasUniqueId = metaData.hasUniqueId;
        if (metaData.hasUniqueId) {
          this.uniqueClass = metaData.uniqueIdClass;
          this.showAll = false;
          this.showLess = true;
          this.selectedLoadingType = '';
        } else {
          this.showAll = true;
          this.showLess = false;
          this.selectedLoadingType = 'allRecords';
        }
        if(this.metaData.showWidgetField != undefined) {
        if(this.metaData.showWidgetField == true) {
          this.hideWidget  = false;
        } else {
          this.hideWidget  = true;
        }
        }
        if (this.metaData.reportFormName) {
          this.formName = this.metaData.reportFormName;
        }
        if (this.metaData.enableNotification == true) {
          /**Get all message template if notification is enabled */
          this._workListService.getWorklistMessageTemplate().then((data) => {
            this.allMessageTemplate = JSON.parse(JSON.stringify(data['getSessionTenant'].formWorklistMessageTemplate));
          });
        }
        /**Default column width */
        this.columnWidth = this.metaData.defaultColumnWidth;
        /** Single/Multiple row selection */
        this.rowSelection = this.metaData.checkboxSelectionType;
        if (this.metaData.checkboxSelectionType == 'multiple') {
          this.groupSelectsChildren = true;
        } else {
          this.groupSelectsChildren = false;
        }
        this.enableCellEditHistory = metaData.hasUniqueId == true ? metaData.enableCellEditHistory : '';
        /**Worklist details form localstorage if exist */
        if (localStorage.getItem('worklistMetaData') && localStorage.getItem('worklistMetaData') != '') {
          let worklistMetaArray = JSON.parse(localStorage.getItem('worklistMetaData'));
          let index = worklistMetaArray.findIndex(x => x.worklistId == this.worklistid);
          if (index != -1) {
            this.formMetaData = worklistMetaArray[index].worklistMeta;
          }
        }
        /** form label from either label text or from css class */
        this.formFieldFrom = metaData.formFieldFrom;
        /**determine the position of action button */
        this.btnBehaviour = metaData.btnBehaviour;
        this.actionColumnPosition = metaData.btnPosition;
        /**Show page size list */
        if (this.metaData.pageSizeCount) {
          this.pageList = this.metaData.pageSizeCount.split(',');
          this.pageList.splice(0, 0, Number(metaData.pageSize));
        }
        /**Page size */
        this.paginationPageSize = Number(metaData.pageSize);
        this.pageSize = this.paginationPageSize;
        if (localStorage.getItem('worklistPageSize') && localStorage.getItem('worklistPageSize') != '') {
          let pageSizeList = JSON.parse(localStorage.getItem('worklistPageSize'));
          if (pageSizeList.worklistId == this.worklistid) {
            this.paginationPageSize = pageSizeList.pageSize;
            this.pageSize = this.paginationPageSize;
          } else {
            localStorage.setItem('worklistPageSize', '');
          }
        }
        /**set Lazy loading parameters */
        this.cacheBlockSize = Number(metaData.InitCountVal);
        this.maxBlocksInCache = Number(metaData.blocksize);
        //this.paginationPageSize = 0;
        /**Page size */
	this.paginationPageSize = Number(metaData.pageSize);
	this.pageSize = this.paginationPageSize;
        /**Show page size list */
	if (this.metaData.pageSizeCount) {
		this.pageList = this.metaData.pageSizeCount.split(',');
		this.pageList.splice(0, 0, Number(metaData.pageSize));
	}
        /*show row group panel*/
        if (this.metaData.enableRowGroup == true) {
          this.rowGroupPanelShow = 'always';
        } else {
          this.rowGroupPanelShow = '';
        }
        /**Get the reportFields and sort  */
        this.reportFields = JSON.parse(JSON.stringify(this.worklistDetails[0].reportFields));
        // this.setDynamicFields();
        this.reportFields.sort(function (a, b) {
          if (a.displayIndex < b.displayIndex) { return -1; }
          if (a.displayIndex > b.displayIndex) { return 1; }
          return 0;
        });
        /**Filter field */
        this.selectedSearchFields = this.defaultSelectedSearchFields = this.reportFields.filter(x => x.allowQuickSearch == true && x.enableAutoSelect == true);
        this.filterEnabledFields = this.reportFields.filter(x => x.allowQuickSearch == true && x.visibility == true);
        this.setColumnDefinition(this.actionColumnPosition);
        /**Showing add new button on right top corner */
        //this.addNewBtn = this.metaData.addNewButton;
        /**Set the dashboard widgets */
        this.dashboardWidgets = JSON.parse(JSON.stringify(this.worklistDetails[0].dashboardWidgets));
        this.dashboardWidgets.sort(function (a, b) {
          if (a.displayIndex < b.displayIndex) { return -1; }
          if (a.displayIndex > b.displayIndex) { return 1; }
          return 0;
        });
        if (this.dashboardWidgets.length > 0) {
          /**Set selected widget for showing that widget highlighted */
          this.selectedWidget = this.dashboardWidgets[0].widgetValue;
          this.selectedWidgetField = this.dashboardWidgets[0].formField;
          this.dashboardWidgetField = this.dashboardWidgets[0].formField;
          this.selectedWidgetId = !isBlank(this.dashboardWidgets[0].id) 
          && this.metaData.widgetState ? 
          this.dashboardWidgets[0].id : 0;
        }
        /**Set the cell click Function */
        this.cellClickFn = metaData.cellClickFn;

        const index = this._structureService.activePatientActivityDetails.findIndex(x => x.tabId == this.worklistid);
        if (index != -1) {
          this._structureService.activePatientActivityDetails[index].columnDefs = this.columnDefs;
          this._structureService.activePatientActivityDetails[index].reportFields = this.reportFields;
          this._structureService.activePatientActivityDetails[index].dashboardWidgets = this.dashboardWidgets;
          this._structureService.activePatientActivityDetails[index].metaData = this.metaData;
        }
        if (this.gridApi) {
          this.gridApi.setColumnDefs(this.columnDefs);
          this.gridApi.sizeColumnsToFit();
        }
        /**Set export option */
        if (metaData.enableExcelExport == false) {
          this.suppressExcelExport = true;
        }
        if (metaData.enableCsvExport == false) {
          this.suppressCsvExport = true;
        }
        if (metaData.enableCustomExport == true) {
          this.showCustomExport = true;
        }
        /**Set the right side panel of ag grid */
        this.sideBar = {
          toolPanels: []
        };
        let panelObj = {};
        if (metaData.filterPanel == 'true') {
          panelObj = {
            id: 'filters',
            labelDefault: 'Filters',
            labelKey: 'filters',
            iconKey: 'filter',
            toolPanel: 'agFiltersToolPanel'
          };
          this.sideBar.toolPanels.push(panelObj);
        }
        if (metaData.columnPanel == 'true') {
          panelObj = {
            id: 'columns',
            labelDefault: 'Columns',
            labelKey: 'columns',
            iconKey: 'columns',
            toolPanel: 'agColumnsToolPanel',
          };
          this.sideBar.toolPanels.push(panelObj);
        }
        if (metaData.rowModel == 'server') {
          this.rowModelType = "serverSide";
          this.pagination = true;
          if (metaData.dataLoadingType && metaData.dataLoadingType == 'pagination') {
            this.pagination = true;
            this.paginationPageSize = Number(metaData.pageSize);
          } else {
            this.pagination = false;
          }
        } else {
          this.rowModelType = "clientSide";
          this.paginationPageSize = Number(metaData.pageSize);
          this.pagination = true;
        }
        this.dataLoadingMsg = false;
  }
  /**Show recent data and history data */
  showAllEntries(type, event) {
    NProgress.start();
    this.uniqueClass = '';
    if (type == 'less') {
      this.showLess = true;
      this.showAll = false;
      this.uniqueClass = this.metaData.uniqueIdClass;
      this.selectedLoadingType = '';
    } else {
      this.showAll = true;
      this.showLess = false;
      this.selectedLoadingType = 'allRecords';
    }
    this.searchFieldText = '';
    this.gridApi.setFilterModel(null);
    this.selectedDays = '';
    if (this.dashboardWidgets.length > 0) {
      this.selectedWidget = this.dashboardWidgets[0].widgetValue;
      this.optionShowButton = this.dashboardWidgets[0].widgetValue;
      this.selectedWidgetField = this.dashboardWidgets[0].formField;
      this.selectedWidgetId = !isBlank(this.dashboardWidgets[0].id) 
      && this.metaData.widgetState ? 
      this.dashboardWidgets[0].id : 0;
    }
    this.clickhistoryFilter = true;
    if (this.rowModelType == 'clientSide') {
      this.getformData();
    }
    NProgress.done();
  }
  getDatePicker() {
    function Datepicker() { }
    Datepicker.prototype.init = function (params) {
      // create the cell
      this.eInput = document.createElement('input');
      this.eInput.value = params.value;
      // https://jqueryui.com/datepicker/
      $(this.eInput).datepicker({
        dateFormat: 'mm/dd/yy',
        changeMonth: true,
        changeYear: true
      });
    };
    Datepicker.prototype.getGui = function () {
      return this.eInput;
    };
    Datepicker.prototype.afterGuiAttached = function () {
      this.eInput.focus();
      this.eInput.select();
    };
    Datepicker.prototype.getValue = function () {
      return this.eInput.value;
    };
    Datepicker.prototype.destroy = function () { };
    Datepicker.prototype.isPopup = function () {
      return false;
    };
    return Datepicker;
  }
  onCellClicked(params) {
    if (params.colDef['cellEvent'] === 'showActivityHistory') {
      let e = { rowData: params.data, actionField: "", actionMode: "", selectionMode: "", link: "" };
      this.showDraftActivityHistory(e);
    }
    if (this.cellClickFn === 'rowClickinMessages') {
      this.rowClickinMessages(params);
    }
    	if (params.colDef['cellEvent'] === 'prescriptionOrders') {
      let e = { rowData: params.data, actionField: "", actionMode: "", selectionMode: "", link: "" };
    	this.viewPrescriptionOrders(e);
    }
    if (params.colDef['cellEvent'] === 'documents') {
      let e = { rowData: params.data, actionField: "", actionMode: "", selectionMode: "", link: "" };
				this.viewDoc(e);
		}
  }
  rowClickinMessages(params) {
    this.showChat = false;
    let type = '';
    let groupname = '';
    if (params.data.group.name) {
      groupname = params.data.group.name;
      type = 'Group Name';
    } else {
      type = 'Initiator';
      groupname = 'nill';
    }
    this.senderId = params.data.sender.id.split(",");
    this.initiatorFromLogs = parseInt(this.senderId[0]);
    //let link = `#/message/logs/chat-logs?id=${params.data.id}&type=${type}&initiator=${this.initiatorFromLogs}&groupname=${groupname}`;
    //this.router.navigate([]).then(result => {  window.open(link, '_blank'); });
    this.chatDetails = {
      id: params.data.id,
      type: type,
      initiator: this.initiatorFromLogs,
      groupname: groupname
    }
    this.showChat = true;
   

    var activityData = {
      activityName: "Start Chat Session",
      activityType: "messaging",
      activityDescription: " Chat With - "+ this.userData.displayName+"("+this.initiatorFromLogs+") in Chatroom "+params.data.id+".",
    };

    this._structureService.trackActivity(activityData);



    $('#chat-modal').modal('show');
    //this.router.navigate(['/message/logs/chat-logs'], { queryParams: { id: params.data.id, type: type, initiator: this.initiatorFromLogs, groupname: groupname } });
  }
   /**Reload after edit data using bulk edit */
   getFormDataBasedRowModel() {
    if(this.enableCellEditHistory == 'true'){
      this.gridApi.deselectAll();
    }
    if (this.rowModelType == 'clientSide') {
      this.getformData();
    } else {
      var datasourcee = ServerSideDatasource(this, true, true, this._workListService);
      this.gridApi.setServerSideDatasource(datasourcee);
    }
  }
  setColumnDefinition(position) {
    /**Action button cofiguration - single and multiple */
    let columnObj = {};
    this.actionFields = JSON.parse(JSON.stringify(this.worklistDetails[0].singleWorklistAction));
    if ((this.actionFields['actionButton']).length > 0) {
      this.actionFields['actionButton'].sort(function (a, b) {
        if (a.buttonIndex < b.buttonIndex) { return -1; }
        if (a.buttonIndex > b.buttonIndex) { return 1; }
        return 0;
      });
    }
    this.actionFields['actionButton'].forEach(element => {
      console.log('element', element);
      const obj = {
        label: element.actionLabel,
        labelType: element.labelType,
        actionCallbackFunctionName: element.actionCallbackFunctionName,
        defaultLabel: element.defaultLabel,
        iconClass: element.actionIcon,
        disable: false,
        toolTip: element.tooltiptext,
        onClick: '',
        type: '',
        buttonStyle: '',
        itemElements: [],
        callbackfunction: element.actionCallbackFunction,
        actionLink: element.actionLink,
        actionField: element.formField ? element.formField : '',
        actionMode: element.actionMode ? element.actionMode : '',
        selectionMode: element.selectionMode ? element.selectionMode : '',
        fieldValues: element.fieldValues,
        downloadLink: '',
        downloadStatus: false,
        docMessage: '',
        actionRole: element.actionRoles,
        actionPrivileges: element.actionPrivileges == null ? '' : element.actionPrivileges,
        actionFields: element.actionFields,
        showOnlyLoginUser: element.showOnlyLoginUser,
        loginUserMatchField: element.loginUserMatchField,
        showEditForm: element.showEditForm ? element.showEditForm : 'popupWindow',
        // formViewType: element.formViewType ? element.formViewType : 'viewType',
        // formEditMode: element.formEditMode ? element.formEditMode : 'submit',
        rolesForEditViewForm: element.rolesForEditViewForm
      };
      if (element.actionCallbackFunction === 'refillEntry') {
        obj.onClick = this.refillEntry.bind(this);
      }
      if (element.actionCallbackFunction === 'sendForm') {
        obj.onClick = this.sendFormAction.bind(this);
      }
      if (element.actionCallbackFunction === 'taggedView') {
        obj.onClick = this.viewMsgTag.bind(this);
      }
      if (element.actionCallbackFunction === 'taggedDelete') {
        obj.onClick = this.deleteMsgTag.bind(this);
      }
      if (element.actionCallbackFunction === 'viewPrescriptionOrders') {
        obj.onClick = this.viewPrescriptionOrders.bind(this);
      }
      if(element.labelType == 'dynamic') {
        if (element.actionCallbackFunctionName === 'generateFile') {
          if (this.userDataConfig.label_for_file_generation != "") {
            obj.label = this.userDataConfig.label_for_file_generation;
          } else {
            obj.label = element.defaultLabel;
          }
        } else {
          obj.label = element.defaultLabel;
        }
      }
      if (element.actionButtonType == 'single') { 
        obj.type = 'single';
        // this.singleRowActions.push(obj);
        if (element.shortLink == true) {
          this.shortLinkActions.push(obj);
        } else {
          this.singleRowActions.push(obj);
          console.log(this.singleRowActions);
          this.singleRowAllActions.push(obj);
        }
        //this.shortLinkActions.push(obj);
      } else {
        obj.type = 'multiple';
        obj.buttonStyle = element.buttonStyle;
        obj.onClick = this.multipleButtonAction.bind(this);
        element.actionMenu.forEach(item => {
          const itemObj = {
            label: item.itemName,
            disable: false,
            action: '',
            itemField: item.itemFormField,
          };
          if (item.itemActionType == 'link') {
            itemObj.action = item.itemActionLink;
          } else {
            itemObj.action = item.itemActionCallbackFunction;
          }
          obj.itemElements.push(itemObj);
        });
        this.singleRowActions.push(obj);
      }
    });
    if (position == 'first') {
      if (this.btnBehaviour) {
        this.columnDefs.push(
          {
            headerName: 'Actions',
            field: 'action',
            width: 300,
            sortable: false,
            filter: false,
            cellRenderer: 'buttonRenderer',
            cellRendererParams: {
              label: 'Label',
              iconElements: this.singleRowActions
            }
          }
        );
      }
    }
    /**Configure column definition */
    this.reportFields.forEach(element => {

      columnObj = {};
      /**Table column header name */
      columnObj['headerName'] = element.headerName;
      /**form field corresponding to each column */
      columnObj['field'] = element.fieldName;
      /**Set minimum width for column */
      if (element.columnWidth && element.columnWidth != '') {
        columnObj['minWidth'] = Number(element.columnWidth);
      } else {
        if (this.columnWidth && this.columnWidth != '') {
          columnObj['minWidth'] = Number(this.columnWidth);
        } else {
          columnObj['minWidth'] = 200;
        }
      }
      if (element.cellEvent) {
        columnObj['cellEvent'] = element.cellEvent;
      }
      /**check whether the column is visible or not*/
      if (element.visibility === false || element.visibility === null) {
        columnObj['hide'] = true;
      }
      /**check whether the cell can be edit*/
      if (element.allowEdit == true && this.metaData.disableInlineEdit == false) {
        columnObj['editable'] = true;
      }
      if(element.enableHyperlink == true) {
        console.log("hyperlinkkkkkk");

        let index = this.singleRowActions.findIndex(x=> x.actionLabel == element.associatedAction);
        columnObj['cellRenderer'] = 'linkCellRenderer';
        console.log(columnObj);

        columnObj['cellRendererParams'] = {
        linkDetails: this.singleRowActions[0],
        privileges: Object.keys(this._structureService.privileges),
        userData : this.userData,
        // workflowType: this.workflowType,
        lockTime: this.metaData.lockTime,
        enableLockingMode: this.metaData.enableLockingMode
        }
        console.log(columnObj['cellRendererParams']);
        }
      if (element.mapField) {
        this.mapFields.push({ fieldName: element.fieldName, mapField: element.mapField, type: element.valueType });
      }
      if (element.linkField) {
        this.linkFields.push({ fieldName: element.fieldName, mapField: element.fieldId, linkField: element.linkField, type: element.valueType });
      }
      /**Set the column group feature*/
      let columnGroups = [];
      if (element.groupName != '' || element.groupName != '') {
        columnGroups.push(element.groupName);
        let obj = {
          headerName: element.headerName,
          field: element.fieldName,
        }
      }
      if (element.allowSort === false) {
        columnObj['sortable'] = false;
      }
      if (element.allowFilter === false) {
        columnObj['filter'] = false;
      } else {
        let obj = {
          newRowsAction: 'keep',
          suppressRemoveEntries: true,
          applyButton: element.applyFilter ? element.applyFilter : false,
          clearButton: element.clearFilter ? element.clearFilter : false,
          suppressAndOrCondition: true
        };
        if (element.valueType == 'checkbox' || (element.fieldValueDelimiter && element.fieldValueDelimiter != '')) {
          obj['filterOptions'] = ['contains'];
          columnObj['filterParams'] = obj;
        } else if (element.valueType == 'radio' || element.valueType == 'select') {
          obj['filterOptions'] = ['contains', 'equals'];
          columnObj['filterParams'] = obj;
        } else {
          columnObj['filterParams'] = obj;
        }
        /**Set different kind of filterings */
        if (element.filterType === 'Number_Filter') {
          columnObj['filter'] = 'agNumberColumnFilter';
        }
        if (element.filterType === 'Text_Filter') {
          columnObj['filter'] = 'agTextColumnFilter';
        }
        if (element.filterType === 'Date_filter') {
          columnObj['filter'] = 'agDateColumnFilter';
        }
      }
      /**Process date value*/
      if (element.valueType === 'date') {
        /**Show datepicker while editing */
        columnObj['cellEditor'] = 'datePicker';
        /**Format date to a particular format*/
        columnObj['valueFormatter'] = this.convertDateFormat;
        /**Implement date filter functionality */
        columnObj['comparator'] = this.dateComparator;
        columnObj['getQuickFilterText'] = this.convertDateFormat;
        columnObj['filterParams'] = {
          inRangeInclusive: true,
          newRowsAction: 'keep',
          suppressRemoveEntries: true,
          suppressAndOrCondition: true,
          applyButton: element.applyFilter ? element.applyFilter : false,
          clearButton: element.clearFilter ? element.clearFilter : false,
          comparator: function (filterLocalDateAtMidnight, cellValue) {
            let cellDate = moment.unix(Number(cellValue)).toLocaleString();
            if (moment(cellDate).format('MMM DD YYYY') < moment(filterLocalDateAtMidnight).format('MMM DD YYYY')) {
              return -1;
            } else if (moment(cellDate).format('MMM DD YYYY') > moment(filterLocalDateAtMidnight).format('MMM DD YYYY')) {
              return 1;
            } else {
              return 0;
            }
          }
        };
      }
      if (element.valueType === 'time') {
        columnObj['valueFormatter'] = this.convertTimeFormat;
      }
      // if (this.rowModelType == 'clientSide') {
      //   this.rowGroupPanelShow = 'always';
      // }

      if (element.allowRowGrouping) {
        columnObj['enableRowGroup'] = true;
      } else {
        columnObj['enableRowGroup'] = false;
      }
      /**Enable checkbox selection for each column */
      if (element.checkboxSelection) {
        columnObj['checkboxSelection'] = true;
        if (this.rowModelType == 'ClientSide') {
          if (this.rowSelection == 'multiple') {
            /**for adding checkbox in header also */
            columnObj['headerCheckboxSelection'] = true;
            /**Set the property of header check box */
            if (this.metaData.headerCheckboxMode == 'filtered') {
              columnObj['headerCheckboxSelectionFilteredOnly'] = true;
            } else {
              columnObj['headerCheckboxSelectionFilteredOnly'] = false;
            }
          }
        }
        /**For Master detail grid view */
        if (this.metaData.worklistType == 'master/detail') {
          columnObj['cellRenderer'] = 'agGroupCellRenderer';
        }
      }
      if (element.allowPrefillData == true && this.metaData.formApiEndpoint == '' && this.metaData.disableInlineEdit == false) {
        /**prefill option in inline cell editing */
        if (element.prefillMethod == 'formOptions') {
          let formElementId: number;
          let formId: number;
          let formType = 'internal';
          let extraOptions = [];
          if (element.prefillFormOption == 'internal') {
            formId = this.formId;
            if (element.fieldId) {
              formElementId = element.fieldId;
            }
          } else {
            formType = 'external';
            formId = element.prefillOptionForm;
            formElementId = element.prefillOptionFormField;
            /**Add extra option*/
            if (element.prefillExtraOptions && element.prefillExtraOptions != '') {
              extraOptions = element.prefillExtraOptions.split('||');
            }
          }
          /**Show values in dropdown while double click on cell */
          columnObj['cellEditor'] = 'agRichSelectCellEditor';
          columnObj['cellEditorParams'] = {
            values: undefined
          };
          /**Get the options of a machform field*/
          this._workListService.getMachformFields(formId, Number(formElementId)).refetch().then(({ data: response }) => {
            this.list[element.fieldName] = JSON.parse(JSON.stringify(response['getFormElementDetails'].options));
            this.list[element.fieldName].push({ 'type': formType });
            const elementList = [];
            response['getFormElementDetails'].options.forEach(element => {
              elementList.push(element.optionValue);
            });
            extraOptions.forEach(element => {
              elementList.push(element.trim());
            });
            /**set dropdown list while editing cell */
            this.setDropdownList(elementList, element.fieldName, element.fieldType);
          });
        }
        if (element.prefillMethod == 'sharedService') {
          /**Show values in dropdown while double click on cell */
          columnObj['cellEditor'] = 'agRichSelectCellEditor';
          columnObj['cellEditorParams'] = {
            values: undefined
          };
          let elementList = [];
          let mySiteUserData = JSON.parse(this._structureService.userDetails);
          mySiteUserData.mySites.forEach(element => {
            elementList.push(element.name);
          });
          this.setDropdownList(elementList, element.fieldName, element.fieldType);
        }
      }
      if (element.allowPrefillData == true && this.metaData.formApiEndpoint != '' && this.metaData.disableInlineEdit == false) {
        if (element.prefillMethod == 'formOptions') {
          /**Show values in dropdown while double click on cell */
          columnObj['cellEditor'] = 'agRichSelectCellEditor';
          columnObj['cellEditorParams'] = {
            values: undefined
          };
        }
        if (element.prefillMethod == 'sharedService') {
          /**Show values in dropdown while double click on cell */
          columnObj['cellEditor'] = 'agRichSelectCellEditor';
          columnObj['cellEditorParams'] = {
            values: undefined
          };
        }
      }

      /**CEll style property */
      if (element.allowCellStyle == true && element.cellStyles) {
        let cssStyles = element.cellStyles.filter(x => x.contentType == "text");
        if (cssStyles.length > 0) {
          columnObj['cellStyle'] = function (params) {
            let columnValue;
            if (typeof params.value == 'string') {
              columnValue = params.value.toLowerCase();
            } else {
              columnValue = params.value;
            }
            for (let i = 0; i < cssStyles.length; i++) {
              let fieldValue;
              let allowRelatedFeild;
              allowRelatedFeild = cssStyles[i].allowRelatedFeild;
              if (allowRelatedFeild) {
                let relatedField = cssStyles[i].formField;
                if (params.data) {
                  columnValue = params.data[`${relatedField}`] != null ? params.data[`${relatedField}`].toString() : '';
                }
              }
              if (typeof params.value == 'string') {
                fieldValue = cssStyles[i].fieldValue.toLowerCase();
              } else {
                fieldValue = cssStyles[i].fieldValue;
              }
              if (cssStyles[i].cellStyleValueType == 'columnValue') {
                return { color: cssStyles[i].fontColour, backgroundColor: columnValue };
              }
              if (cssStyles[i].expression == '==' && columnValue == fieldValue) {
                return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
              }
              if (cssStyles[i].expression == '!=' && columnValue != fieldValue) {
                return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
              }
              if (cssStyles[i].expression == '>' && columnValue > fieldValue) {
                return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
              }
              if (cssStyles[i].expression == '>=' && columnValue >= fieldValue) {
                return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
              }
              if (cssStyles[i].expression == '<' && columnValue < fieldValue) {
                return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
              }
              if (cssStyles[i].expression == '<=' && columnValue <= fieldValue) {
                return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
              }
              if (cssStyles[i].expression == 'contains' && columnValue && columnValue.includes(fieldValue)) {
                return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
              }
              if (cssStyles[i].expressionType && cssStyles[i].expressionType == 'day') {
                var date = moment(params.value);
                let dayValues = cssStyles[i].day.split(',');
                if (dayValues.indexOf(date.day().toString()) != -1) {
                  return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                } else if (dayValues.indexOf('today') != -1) {
                  var today = moment(new Date()).format('MM/DD/YYYY');
                  if (date.isSame(today) == true) {
                    return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                  }
                } else if (dayValues.indexOf('yesterday') != -1) {
                  var yesterday = moment().subtract(1, 'days').format('MM/DD/YYYY');
                  if (date.isSame(yesterday) == true) {
                    return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                  }
                } else if (dayValues.indexOf('tomorrow') != -1) {
                  var tomorrow = moment().add(1, 'days').format('MM/DD/YYYY');
                  if (date.isSame(tomorrow) == true) {
                    return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                  }
                }
              }
              if (cssStyles[i].expressionType && cssStyles[i].expressionType == 'month') {
                var date = moment(params.value);
                let monthValue = cssStyles[i].month.split(',');
                if (monthValue.indexOf(date.month().toString()) != -1) {
                  return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                }
              }
              if (cssStyles[i].expressionType && cssStyles[i].expressionType == 'year') {
                var date = moment(params.value);
                if (cssStyles[i].year == date.year()) {
                  return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                }
              }
              if (cssStyles[i].expressionType && cssStyles[i].expressionType == 'dateDiff') {
                let columnValue = moment(params.value);
                let today = moment(new Date()).format("MM-DD-YYYY");
                let diffDays = columnValue.diff(today, 'days');
                let obj = cssStyles[i].expressionConditions;
                if (obj.length > 1 && obj[1].operator == 'and') {
                  let count = 0;
                  obj.forEach(element => {
                    if (element.expression == '=' && diffDays == element.value) {
                      count++;
                    }
                    if (element.expression == '!=' && diffDays != element.value) {
                      count++;
                    }
                    if (element.expression == '<' && diffDays < element.value) {
                      count++;
                    }
                    if (element.expression == '<=' && diffDays <= element.value) {
                      count++;
                    }
                    if (element.expression == '>' && diffDays > element.value) {
                      count++;
                    }
                    if (element.expression == '>=' && diffDays >= element.value) {
                      count++;
                    }
                  });
                  if (count == 2) {
                    return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                  }
                } else {
                  let count = 0;
                  obj.forEach(element => {
                    if (element.expression == '=' && diffDays == element.value) {
                      count++;
                    }
                    if (element.expression == '!=' && diffDays != element.value) {
                      count++;
                    }
                    if (element.expression == '<' && diffDays < element.value) {
                      count++;
                    }
                    if (element.expression == '<=' && diffDays <= element.value) {
                      count++;
                    }
                    if (element.expression == '>' && diffDays > element.value) {
                      count++;
                    }
                    if (element.expression == '>=' && diffDays >= element.value) {
                      count++;
                    }
                  });
                  if (count == 1 || count == 2) {
                    return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                  }
                }
              }
            }
          }
        }
        let iconStyles = element.cellStyles.filter(x => x.contentType == "icon" || x.contentType == "both");
        if (iconStyles.length > 0) {
          columnObj['cellRenderer'] = 'iconCellRenderer';
          columnObj['cellStyle'] = { textAlign: 'left' };
          let iconArray = [];
          for (let i = 0; i < iconStyles.length; i++) {
            let iconObj;
            iconObj = {
              iconColour: iconStyles[i].iconColor, iconClass: iconStyles[i].iconClass,
              fieldValue: iconStyles[i].fieldValue, expression: iconStyles[i].expression,
              expressionType: iconStyles[i].expressionType, day: iconStyles[i].day,
              month: iconStyles[i].month, year: iconStyles[i].year,
              contentType: iconStyles[i].contentType,
              allowRelatedField: iconStyles[i].allowRelatedField,
              relatedField: iconStyles[i].allowRelatedField == true ? iconStyles[i].formField : ''
            }
            iconArray.push(iconObj);
          }
          columnObj['cellRendererParams'] = {
            iconElements: iconArray
          }
        }
      }
      /**set grouping for ag grid */
      if (element.groupName != '' && element.groupName != null && element.visibility == true) {
        const groupIndex = this.columnDefs.findIndex(x => x.headerName == element.groupName && x.children.length != 0);
        if (groupIndex == -1) {
          const childObj = {
            children: []
          };
          childObj['headerName'] = element.groupName;
          childObj['openByDefault'] = true;
          childObj['marryChildren'] = true;
          childObj['children'].push(columnObj);
          this.columnDefs.push(childObj);
        } else {
          columnObj['columnGroupShow'] = 'open';
          this.columnDefs[groupIndex]['children'].push(columnObj);
        }
      } else {
        this.columnDefs.push(columnObj);
      }
    });

    if (position == 'last') {
      if (this.btnBehaviour) {
        this.columnDefs.push(
          {
            headerName: 'Actions',
            field: 'action',
            width: 300,
            sortable: false,
            filter: false,
            cellRenderer: 'buttonRenderer',
            cellRendererParams: {
              label: 'Label',
              iconElements: this.singleRowActions
            }
          }
        );
      }
    }
  }
  messageChatWithRenderer(params) {
    if (params.value) {
      var chatPersons = '';
      let paramLength = params.value.length;
      let pLength = 0;
      params.value.forEach(element => {
        pLength = pLength + 1;
        chatPersons += element.participant.displayName;
        if (pLength < paramLength - 1) {
          chatPersons += chatPersons + ',';
        }
      });
      return chatPersons;
    }
  }
  tagsInTaggedMessagesRenderer(params) {
    if (params.value) {
      var chatPersons = '';
      let paramLength = params.value.length;
      let pLength = 0;
      params.value.forEach(element => {
        pLength = pLength + 1;
        chatPersons += element.tagName;
        if (pLength < paramLength - 1) {
          chatPersons += chatPersons + ',';
        }
      });
      return chatPersons;
    }
  }
  onSelectionChanged(event) {

    this.rowCount = event.api.getSelectedNodes().length;
    this.selectedRowDetails = event.api.getSelectedRows();



    if (this.rowCount == 0) {
      this.hideActionButtons = true;
    } else {
      let rowData = event.api.getSelectedNodes()[0].data;
      let filterAction = [];
      let privileges = Object.keys(this._structureService.privileges);
      let userRole = this._structureService.getCookie('userRole');
      this.singleRowAllActions.forEach(element => {
        /**Check privilege for showing action button */
        if ((element.actionPrivileges != '' && privileges.indexOf(element.actionPrivileges) != -1) || element.actionPrivileges == '') {
          let actionFields = [];
          if (element.actionField && element.actionField != '') {
            actionFields.push({ 'associatedField': element.actionField, 'fieldValues': element.fieldValues });
          } else if (element.actionFields) {
            actionFields = element.actionFields;
          }
          if (element.labelType == 'dynamic') {
            if (element.actionCallbackFunctionName === 'generateFile') {
              if (this.userDataConfig.label_for_file_generation != "") {
                element.label = this.userDataConfig.label_for_file_generation;
              }
              else {
                element.label = element.defaultLabel;
              }
            } else {
              element.label = element.defaultLabel;
            }
          }
          if (actionFields.length > 0) {
            let count = 0;
            let loginUserId = this.userData.userId;
            let loginDisplayName = this.userData.displayName;
            let fieldAssociate;            
            actionFields.forEach(field => {            
              let hasActionField = false;
              let fieldArray = field.associatedField.split('.');
              if(fieldArray.length > 1) { 
                if(rowData.hasOwnProperty(fieldArray[0]) && rowData[fieldArray[0]].hasOwnProperty(fieldArray[1])){     hasActionField = true;
                     fieldAssociate = rowData[fieldArray[0]][fieldArray[1]]; }
                } else { hasActionField = rowData.hasOwnProperty(field.associatedField) == true ? true : false; fieldAssociate = rowData[field.associatedField];}
                if (hasActionField == true) {       
                  if (field.fieldValues && field.fieldValues.split(',').findIndex(x => x == fieldAssociate.toString()) != -1) {
                    if (element.showOnlyLoginUser == true) {
                      if (rowData[element.loginUserMatchField] == loginUserId || rowData[element.loginUserMatchField] == loginDisplayName) {
                      count++;
                      }
                    } else {
                       count++;
                    }
                  }
                }
                if (field.fieldValues && field.fieldValues == 'Any') {
                  if (fieldAssociate) {
                    count++;
                  }
                }
            });
            if (count == actionFields.length) {
              filterAction = this.setActionColumn(element, rowData, filterAction, userRole);
            }
          } else {
            filterAction = this.setActionColumn(element, rowData, filterAction, userRole);
          }
        }
      });
      this.singleRowActions = [];
      if (filterAction.length > 0) {
        this.singleRowActions = filterAction;
      }
      this.created_date = rowData.created_date;
      this.allow_edit = rowData.allow_edit;
      this.form_id = rowData.form_id;
      this.formStatus = rowData.formStatus;
      this.form_submission_id = rowData.form_submission_id;
      this.hideActionButtons = false;
      let actvitydesc= "Choose "+ this.dynamicData.tabName +"For Action: ";
      if (rowData.form_id){
        actvitydesc  ="Choose "+ this.dynamicData.tabName +"For Action: Form ID - : " + rowData.form_id;   
      }
       var activityData = {
        activityName: "Choose "+ this.dynamicData.tabName +" For Action: ",
        activityType: "Choose "+ this.dynamicData.tabName +" For Action: ",
        activityDescription: actvitydesc,
      };
      this._structureService.trackActivity(activityData);


    }
  }
  setActionColumn(element, rowData, filterAction, userRole) {
    if (element.callbackfunction == 'view') {
      if (rowData.created_date) {
        filterAction.push(element);
      } else {
        if (rowData.formStatus != 'Archived') {
          if (rowData.allow_edit == 1 && rowData.formStatus == "Pending" && ((this.userData.userId == rowData.recipient_id && rowData.staffFacing != 1) || (this.userData.userId == rowData.from_id && rowData.staffFacing == 1) || (rowData.form_submission_id))) {
            filterAction.push(element);
          } else {
            filterAction.push(element);
          }
        }
      }
    } else if (element.callbackfunction == 'download') {
      if (rowData.created_date) {
        filterAction.push(element);
      }
    } else if (element.callbackfunction == 'cancel-form') {
      if (rowData.formStatus == 'Pending') {
        if (rowData.allow_edit != 1 && (this.previlages.viewFormEntries || (rowData.from_id == this.userData.userId))) {
          filterAction.push(element);
        }
      } else {
        filterAction.push(element);
      }
    } else if (element.callbackfunction == 'edit') {
      if (rowData.formStatus == "Drafts") {
        filterAction.push(element);
      }
      if (rowData.allow_edit == 1 && rowData.formStatus == "Pending" && ((this.userData.userId == rowData.recipient_id && rowData.facing_new != 1) || (this.userData.userId == rowData.from_id && rowData.facing_new == 1))) {
        filterAction.push(element);
      }
    } else if (element.callbackfunction == 'allow_edit') {
      if (rowData.created_date && rowData.formStatus != 'archived' && Number(rowData.allow_edit) != 1) {
        filterAction.push(element);
      }
    }
    else if (element.callbackfunction == 'reviewStatusChange') {
      if (rowData.formStatus != 'Drafts' && rowData.formStatus != 'Pending' && userRole != 'Patient') {
        filterAction.push(element);
      }
    } else if (element.callbackfunction == 'deny_edit') {
      if (rowData.formStatus == 'Pending' && Number(rowData.allow_edit) == 1 && userRole != 'Patient') {
        filterAction.push(element);
      } else if (rowData.created_date && rowData.formStatus != 'archived' && Number(rowData.allow_edit) == 1 && userRole != 'Patient') {
        filterAction.push(element);
      }
    } else if (element.callbackfunction == 'restore-forms') {
      if (userRole != 'Patient') {
        filterAction.push(element);
      }
    } else if (element.callbackfunction == 'archive-form') {
      if (userRole != 'Patient') {
        filterAction.push(element);
      }
    } else if (element.callbackfunction == 'resend-form') {
      if (rowData.formStatus !== Status.Archived && userRole !== CONSTANTS.userRoles.patient) {
        filterAction.push(element);
      }
    } else {
      filterAction.push(element);
    }
    if (element.callbackfunction == 'signature_download') {
      let data = [];
      data['rowData'] = rowData;
      this.getActiveDocumentDetail(data);
      element.downloadLink = this.activeSignedDocument.documentUrl;
      element.downloadStatus = true;
      element.docMessage = this.activeSignedDocument.message;
    }
    return filterAction;
  }
  showDraftActivityHistory(e) {
    this.formDetails = e.rowData;
    this.showHistoryModal = true;
    this.showDraftHistory = true;
  }
  showAllActivityHistory(e) {
    this.formDetails = e.rowData;
    this.formDetails['recipient_id'] = this.formDetails['recipient_id']
       ? this.formDetails['recipient_id']
       : this.formDetails['from_id'];
    this.formDetails['formStatus'] = this.formDetails['formStatus'].toLowerCase();
    this.showHistoryModal = true;
    this.showHistory = true;
  }
  /**Single button action */
  singleBtnAction(callback, link, field, mode, selection, actionConfig) {
    const selectedData = this.selectedRowDetails;
    if (selectedData.length > 0) {
      this.hideActionButtons = false;
      const actionDetails = { 
        rowData: selectedData[0], 
        actionField: field, 
        actionMode: mode, 
        selectionMode: selection, 
        link: link, 
        actionConfig: actionConfig 
      };
      if (link == '' || link == null) {
        const formStatus = !isBlank(actionDetails.rowData.formStatus) ? actionDetails.rowData.formStatus.toLowerCase() : '';
        switch (callback) {
          case 'refillEntry':
            this.refillEntry(actionDetails);
            break;
          case 'sendForm':
            this.sendFormAction(actionDetails);
            break;
          case 'taggedView':
            this.viewMsgTag(actionDetails);
            break;
          case 'taggedDelete':
            this.deleteMsgTag(actionDetails);
            break;
          case 'history':
            if (formStatus === Status.Draft) {
              this.showDraftActivityHistory(actionDetails);
            } else {
              this.showAllActivityHistory(actionDetails);
            }
            break;
          case 'view':
            const formType = actionDetails.rowData.form_type.toLowerCase();
            if (
              formStatus === Status.Pending &&
              actionDetails.rowData.allow_edit === 1 &&
              (
                (this.userData.userId === actionDetails.rowData.recipient_id && formType.toLowerCase() !== FormType.Staff) ||
                (this.userData.userId === actionDetails.rowData.from_id && formType.toLowerCase() === FormType.Staff) ||
                actionDetails.rowData.form_submission_id
              )
            ) {
              this.viewStrucuturedForm(actionDetails);
            } else if (formStatus === Status.Pending || formStatus === Status.Canceled) {
              this.viewForm(actionDetails);
            } else {
              this.viewStrucuturedForm(actionDetails);
            }
            break;
          case 'viewPrescriptionOrders':
            this.viewPrescriptionOrders(actionDetails);
            break;
          case 'viewDoc':
            this.viewDoc(actionDetails);
            break;
          case 'download':
            this.getPdfTaggedForm(actionDetails);
            break;
          case 'reviewStatusChange':
            this.reviewStatusChange(actionDetails);
            break;
          case 'archive-form':
            this._structureService.showAlertMessagePopup({
              text: this._ToolTipService.getTranslateData('MESSAGES.ARCHIVE_FORM'),
            }).then((confirm) => {
                if(confirm){
                  this.archiveSubmitedForm(actionDetails);
                }
              });
            break;
          case 'restore-forms':
            this._structureService.showAlertMessagePopup({
              text: this._ToolTipService.getTranslateData('MESSAGES.RESTORE_FORM'),
            }).then((confirm) => {
                if(confirm){
                  this.restoreForms(actionDetails);
                }
              });
            break;
          case 'resend-form':
            this.resendForm(actionDetails);
            break;
          case 'allow_edit':
            this.allowEdit(actionDetails);
            break;
          case 'deny_edit':
            this.allowEdit(actionDetails);
            break;
          case 'cancel-form':
            if(actionDetails.rowData.formStatus.toLowerCase() === 'pending') 
              this.cancelForm(actionDetails);
            else
              this.cancelDraftForm(actionDetails);
            break;
          case 'cancelForm':
            this.cancelForm(actionDetails);
            break;
          case 'signature_view':
            this.viewdocumentCheck(actionDetails);
            break;
          case 'signature_download':
            this.downloadSignatureDoc(actionDetails);
            break;
          case 'signature_archive':
            this._structureService.showAlertMessagePopup({
              text: this._ToolTipService.getTranslateData('MESSAGES.ARCHIVE_DOCUMENT'),
            }).then((confirm) => {
                if(confirm){
                  this.archiveOrRestoreSignatureDocument('ARCHIVE', actionDetails);
                }
              });
            break;
          case 'signature_resend':
            this._structureService.showAlertMessagePopup({
              text: this._ToolTipService.getTranslateData('MESSAGES.RESEND_DOCUMENT'),
            }).then((confirm) => {
                if(confirm){
                  this.resendSignatureDocument(actionDetails);
                }
              });
            break;
          case 'signature_restore':
            this._structureService.showAlertMessagePopup({
              text: this._ToolTipService.getTranslateData('MESSAGES.RESTORE_DOCUMENT'),
            }).then((confirm) => {
                if(confirm){
                  this.archiveOrRestoreSignatureDocument('RESTORE', actionDetails);
                }
              });
            break;
          case 'signature_patient':
            this.selectModalPatient(actionDetails);
            break;
          case 'genarate-files':
            this.moveToFC(actionDetails);
            break;
          case 'sendToEHR':
            var sender = 0;
            if (this.activeSignedDocument.senderTenant ) {
              sender = this.activeSignedDocument.senderTenant;
            }
            this._structureService.showAlertMessagePopup({
              text: this._ToolTipService.getTranslateData('MESSAGES.SEND_TO_EHR'),
            }).then((confirm) => {
                if(confirm){
                  this.sendToEHRSignatureDocument(sender,actionDetails);
                }
              });
            break;
          case 'signature_cancel':
            this._structureService.showAlertMessagePopup({
              text: this._ToolTipService.getTranslateData('MESSAGES.CANCEL_DOCUMENT'),
            }).then((confirm) => {
                if(confirm){
                  this.cancelSignatureDocument('CANCEL', actionDetails);
                }
              });
            break;
          case 'edit':
            this.editForm(actionDetails);
            break;
          case 'resendToRecipients':
            this.contentType = CONSTANTS.contentType.forms.toLowerCase();
            this.entityId = actionDetails.rowData.sent_id;
            this.activeStrucuredForms = actionDetails.rowData;
            this.popUpRecipientsList();
            break;
          case 'resendDocumentToRecipients':
            this.contentType = CONSTANTS.contentType.documents.toLowerCase();
            this.entityId = actionDetails.rowData.id;
            this.getActiveDocumentDetail(actionDetails);
            this.popUpRecipientsList();
            break;
        }
        let activityAction = `Perform Action : ${callback}`;
        if(actionDetails.rowData.submissionID){  
          activityAction = `${this.dynamicData.tabName} Perform Action : ${callback} with id -:(${actionDetails.rowData.submissionID})`;
        }
        if(actionDetails.rowData.messageId){  
          activityAction = `${this.dynamicData.tabName} Perform Action : ${callback} with id -:(${actionDetails.rowData.messageId})`;
        }
        if(actionDetails.rowData.form_id){ 
          activityAction = `${this.dynamicData.tabName} Perform Action : ${callback} Form ID - :(${actionDetails.rowData.form_id})`; 
        }
        if(actionDetails.rowData.ownerId){
          activityAction = `${this.dynamicData.tabName} Perform Action : ${callback} ${actionDetails.rowData.displayText.text} with id - :(${actionDetails.rowData.id}) Owner ${actionDetails.rowData.owner} (${actionDetails.rowData.ownerId})`; 
        }
        const activityData = {
          activityName: `${this.dynamicData.tabName} Perform Action : ${callback}`,
          activityType: `${this.dynamicData.tabName} Perform Action : ${callback}`,
          activityDescription: activityAction,
        };
        this._structureService.trackActivity(activityData);
      } else {
        this.goToActionLink(link);
      }
    } else {
      this.hideActionButtons = true;
    }
  }

  /**
  * Function to close and open Choose Recipient(s) modal
  */
  popUpRecipientsList() {
      this.showResendModal = !this.showResendModal;
  }

  /**
   * On send clicked from the modal, it will emit the selected recipients
   * @param event will have list of recipients of type RecipientList[]
   * @returns void
   */
  resendToRecipients(recipients){
    if(this.contentType === CONSTANTS.contentType.forms.toLowerCase()) {
      this.resendFormToRecipients(recipients);
    } else if(this.contentType === CONSTANTS.contentType.documents.toLowerCase()) {
      this.resendDocumentToRecipients(recipients);
    }
  }
  /**
  * Function to resend appless link to selected recipients
  * @param recipients Recipients list choose from the model as array of objects
  */
  resendFormToRecipients(recipients) {
    this._formsService.resendFormToRecipients(recipients, this.activeStrucuredForms, this.userPatientId);
  }
  
  resendDocumentToRecipients(recipientList: RecipientList[]): void {
    if(!isBlank(recipientList)) {

      const recipients = recipientList.map((item) =>  item.userId ).join(',');
      let message = this._ToolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG');
      let type = CONSTANTS.notificationTypes.warning;

      let activityData = {
        activityName: 'Resend document to recipients',
        activityType: 'Signature worklist',
        activityDescription: '',
        activityLinkageId: this.entityId
      };

      //Call the graphQl API 
      this._structureService.resendDownloadDocumentApplessLink(
        this.entityId,recipients).then(
        (result) => {
          if(!isBlank(result['resendDownloadDocumentApplessLink'])) {
            if( result['resendDownloadDocumentApplessLink'].success) {
              message = this._ToolTipService.getTranslateDataWithParam(
                'SUCCESS_MESSAGES.FORM_DOCUMENT_RESENT_SUCCESS',
               {type: CONSTANTS.contentType.document});
              type = CONSTANTS.notificationTypes.success;
            } else {
              message = this._ToolTipService.getTranslateData('MESSAGES.ERROR_MSG_EXCEPTION_CATCH');
            }
          }          
          this._structureService.notifyMessage({messge: message, type: type});
          //Get the message from the API response
          if(!isBlank(result['resendDownloadDocumentApplessLink'].error)) {
            message = result['resendDownloadDocumentApplessLink'].error;
          } else if(!isBlank(result['resendDownloadDocumentApplessLink'].message)){
            message = result['resendDownloadDocumentApplessLink'].message;
          }
          activityData.activityDescription = `${this.userData.displayName}(User- ${this.userData.userId}) resend the ` +
          `${this.activeSignedDocument.documentName} Document (Status: ${this.activeSignedDocument.signatureStatus}) to recipients(${recipients}),`+
          `status: ${result['resendDownloadDocumentApplessLink'].success}, status-message: ${message}`;
          this._structureService.trackActivity(activityData);
        })
        .catch((error) => {
          this._structureService.notifyMessage({messge: error.toString(), type: type});
          activityData.activityDescription = `${this.userData.displayName}(User- ${this.userData.userId}) resend the ` +
          `${this.activeSignedDocument.documentName} Document (Status: ${this.activeSignedDocument.signatureStatus}) `+
          `to recipients(${recipients}), status: Failed,${error.toString()}`;
          this._structureService.trackActivity(activityData);
       });      
    }
  }
  goToActionLink(actionLink) {
    this.router.navigate([`${actionLink}`]);
  }
  selectModalPatient(data) {
    this.getActiveDocumentDetail(data);
    this.documentID = this.activeSignedDocument.id;
    this.showUpdateAssocPatient = true;
  }
  onUpdatingAssocPatient(event) {
    this.showUpdateAssocPatient = false;
    if (!isBlank(event)) {
      const obj = event;
      this.activeSignedDocument.associatePatient = obj.displayname;
      this.gridApi.deselectAll();
      this.getformData();
    }
  }
  resendSignatureDocument(data) {
    this.getActiveDocumentDetail(data);
    $("body .resend_" + this.activeSignedDocument.id).attr("disabled", true);
    this._structureService.resendSignatureDocument({ id: this.activeSignedDocument.id }).subscribe((result) => {
      let resultData = result['data']['resendSignatureDocument'];
      let index = this.rowData.findIndex(x => x == data.rowData);
      this.rowData[index].createdOn = resultData.createdOn;
      this.gridApi.refreshCells({ force: true });
      // let allreqIndex = -1;
      // allreqIndex = this.pendingDocumentList.indexOf(this.activeSignedDocument);
      // if (allreqIndex > -1) {
      //   this.pendingDocumentList[allreqIndex].create = resultData.createdOn;
      // }
      $("body .resend_" + resultData.id).attr("disabled", false);
      // this.loadDataPendingDocs();
      var users = [];
      if (this.activeSignedDocument.signatureRequestData.signatureByUsers) {
        var usersDetails = { userId: this.activeSignedDocument.signatureRequestData.signatureByUsers.userId, displayName: this.activeSignedDocument.signatureRequestData.signatureByUsers.displayName };
        users.push(usersDetails);
        var ownerDetails = { userId: this.activeSignedDocument.signatureRequestData.ownerId, displayName: this.activeSignedDocument.owner };
        users.push(ownerDetails);
      }
      if (this.activeSignedDocument.signatureRequestData.associateSignatureByUsers) {
        var usersDetails = { userId: this.activeSignedDocument.signatureRequestData.associateSignatureByUsers.userId, displayName: this.activeSignedDocument.signatureRequestData.associateSignatureByUsers.displayName };
        users.push(usersDetails);
      }
      var ur = this._structureService.getApiBaseUrl();
      var url = ur.replace(/\/$/, "");
      var urlrest = url.substring(0, url.lastIndexOf("/") + 1);
      var furl = urlrest.replace(/\/$/, "");
      var pollingData = {
        environment: this._structureService.environment,
        serverBaseUrl: furl,
        apiVersion: this._structureService.version,
        documentId: resultData.id,
        createdOn: resultData.createdOn,
        ownerId: parseInt(this.activeSignedDocument.callbellOwner),
        notifyUsers: users,
        notifyOnSubmitSignatureRoles: this.activeSignedDocument.signatureRequestData.notifyOnSubmitSignatureRoles,
        notifyOnSubmitSignatureUsers: this.activeSignedDocument.signatureRequestData.notifyOnSubmitSignatureUsers
      }
      this._structureService.socket.emit("obtainSignResendPollingToServer", pollingData);
      var notify = $.notify('Success! Signature document resend');

      var activityData = {
        activityName: "Signature Resend",
        activityType: "Signature Request",
        activityDescription: this.userData.displayName + " (User-" + this.userData.userId + ") resend the " + this.activeSignedDocument.signatureRequestData.displayText.text + " Document ( Status : " + this.activeSignedDocument.signatureRequestData.signatureStatus + ")",
        activityLinkageId: this.activeSignedDocument.id
      };
      this._structureService.trackActivity(activityData);
    });
  }
  getActiveDocumentDetail(data) {
    let rowData = data.rowData;
    let signedObject = {
      id: '',
      from: '',
      message: '',
      documentType: '',
      documentUrl: '',
      envelopeId: '',
      documentId: '',
      sent: '',
      create: '',
      archivedOn: '',
      isSigned: false,
      fromName: '',
      toName: '',
      docDownloadStatus: false,
      url: '',
      downloadFileName: '',
      associatePatient: '',
      timestamp: '',
      signatureRequest: true,
      signatureRequestData: '',
      userId: '',
      callbellOwner: '',
      cmisApi: true,
      cmisDownload: false,
      archivedUsers: "",
      accountLevelArchived: false,
      signatureStatus: rowData.signatureStatus,
      documentName:'',
      admissionId: ''
    };
    if (rowData.type && rowData.type.patientAssociateRole) {
      this.patientAssociateRole = rowData.type.patientAssociateRole;
    }
    let fileName = '';
    if (rowData.document && rowData.document.displayText) {
      fileName = rowData.document.displayText + "-" + rowData.id + ".pdf";
    }
    if (rowData.type && rowData.type.sendDocumentWithoutSignature) {
      fileName = rowData.document.displayText + ".pdf";
    }
    let fileUrl = this._structureService.getApiBaseUrl() + "writable/filetransfer/uploads/" + rowData.ownerId + "/document/signatureRequest/signedpdf/" + fileName + "?" + Math.floor((1 + Math.random()) * 0x1000);
    if (rowData.signatureStatus == "SIGNED" && rowData.downloadUrl) {
      fileUrl = rowData.downloadUrl;
      signedObject.cmisDownload = true;
    }
    signedObject.id = rowData.id;
    if (rowData.displayText && rowData.displayText.text) {
      signedObject.message = rowData.displayText.text;
    }
    else {
      signedObject.message = "";
    }
    signedObject.documentType = "";
    if (rowData.type) {
      signedObject.documentType = rowData.type.name;
    }
    signedObject.associatePatient = rowData.document.associatePatient;
    signedObject.from = rowData.ownerId;
    signedObject.callbellOwner = rowData.ownerId;
    signedObject.userId = rowData.signatureByUsers.userId;;
    signedObject.envelopeId = rowData.id;
    signedObject.documentId = rowData.id;
    signedObject.sent = rowData.signedOn;
    signedObject.create = rowData.createdOn;
    signedObject.timestamp = rowData.signedOn;
    signedObject.archivedOn = rowData.archivedOn;
    signedObject.archivedUsers = rowData.archivedUsers;
    signedObject.accountLevelArchived = rowData.accountLevelArchived;
    signedObject.signatureRequestData = rowData;
    signedObject.fromName = rowData.owner;
    signedObject.toName = rowData.signatureByUsers.displayName ? rowData.signatureByUsers.displayName : this._structureService.getCookie('tenantname');;
    signedObject.documentUrl = fileUrl;
    signedObject.documentName = rowData.displayText.text;
    signedObject.admissionId = rowData.admissionId;
    if (rowData.signatureStatus == "SIGNED") {
      signedObject.isSigned = true;
      if (rowData.accountLevelArchived) {
        this.archiveDocumentList.push(signedObject);
      } else {
        this.SignedDocumentList.push(signedObject);
      }
    }
    else if (rowData.signatureStatus == "PENDING" || rowData.signatureStatus == "SIGN_APPROVAL") {
      signedObject.isSigned = false;
      if (rowData.accountLevelArchived) {
        this.archiveDocumentList.push(signedObject);
      } else {
        this.pendingDocumentList.push(signedObject);
      }
    }
    this.activeSignedDocument = signedObject;
  }
  sendToEHRSignatureDocument(sender,data) {
    this.getActiveDocumentDetail(data);    
    let enable_multisite= this.userData.config.enable_multisite;
    let enable_fax = (this.userData.config.enable_multisite == 1 && this.userData.config.enable_configurable_faq != 1 ? false : true);
    this._structureService.sendToEhr(this.activeSignedDocument.id,sender,enable_multisite,enable_fax).subscribe((responseData: any)=>{   
      if(responseData['data']['sendToEhrDocument']['status'] ==1) {
          var message ='Document for ' + this.activeSignedDocument.documentName + ' generated successfully';
          var type ='success';
      } else if(responseData['data']['sendToEhrDocument']['status'] ==0) {
          message =responseData['data']['sendToEhrDocument']['message'];
          type ='danger';
      } else if(responseData.errors.length > 0) {
        message = responseData.errors[0].message;
        type ='danger';
      } else {
        message ="Something went wrong ! Please try again";
        type ='danger';
      }
      this._structureService.notifyMessage({
                              messge: message,
                              delay: 1000,
                              type: type
                          });      
     });  
  }
  archiveOrRestoreSignatureDocument(action, data) {
    this.getActiveDocumentDetail(data);
    this._structureService.archiveSignatureDocumentTenant({
      id: this.activeSignedDocument.id, action: action, from: this.activeSignedDocument.cmisApi,
      senderTenant: this.activeSignedDocument.signatureRequestData.senderTenant}).then((result) => {
      if (action == "ARCHIVE") {
        if (this.activeSignedDocument.signatureStatus == "PENDING" || this.activeSignedDocument.signatureStatus == "SIGN_APPROVAL") {
          let index = this.rowData.findIndex(x => x == data.rowData);
          this.rowData[index].status = "ARCHIVED";
          this.gridApi.refreshCells({ force: true });
          this.dashboardWidgets.forEach(element => {
            if (element.widgetValue == 'ARCHIVED') {
              element.count = element.count + 1;
            }
            if (element.widgetValue == 'PENDING') {
              element.count = element.count - 1;
            }
          });
          this.gridApi.deselectAll();
          this.gridApi.onFilterChanged();
          var notify = $.notify('Success! Document Archived');
          setTimeout(function () {
            notify.update({ 'type': 'success', 'message': '<strong>Success! Document Archived.</strong>' });
          }, 1000);
          var obtainSignPollingData = { tenantid: 0, documentId: 0, archiveOrRestore: false, archivedUsers: "", archivedOn: "", accountLevelArchived: false };
          obtainSignPollingData.tenantid = this.userData.tenantId;
          obtainSignPollingData.documentId = this.activeSignedDocument.id;
          obtainSignPollingData.archiveOrRestore = true;
          obtainSignPollingData.archivedOn = result['archiveSignatureDocument'].archivedOn;
          obtainSignPollingData.archivedUsers = result['archiveSignatureDocument'].archivedUsers;
          obtainSignPollingData.accountLevelArchived = result['archiveSignatureDocument'].accountLevelArchived;
          this._structureService.socket.emit("obtainSignPollingToServer", obtainSignPollingData);
          var activityData = {
            activityName: "Signature - " + action,
            activityType: "Signature Request",
            activityDescription: this.userData.displayName + " (Tenant-" + this.userData.tenantId + ") (User-" + this.userData.userId + ")" + action + " " + this.activeSignedDocument.message + " Document ( Status : " + this.activeSignedDocument.signatureStatus + ")",
            activityLinkageId: this.activeSignedDocument.id
          };
          this._structureService.trackActivity(activityData);
          // }
        } else if (this.activeSignedDocument.signatureStatus == "SIGNED") {
          // allreqIndex = this.SignedDocumentList.indexOf(this.activeSignedDocument);
          let index = this.rowData.findIndex(x => x == data.rowData);
          this.rowData[index].status = "ARCHIVED";
          this.gridApi.refreshCells({ force: true });
          this.dashboardWidgets.forEach(element => {
            if (element.widgetValue == 'ARCHIVED') {
              element.count = element.count + 1;
            }
            if (element.widgetValue == 'SIGNED') {
              element.count = element.count - 1;
            }
          });
          this.gridApi.deselectAll();
          this.gridApi.onFilterChanged();
          var notify = $.notify('Success! Document Archived');
          setTimeout(function () {
            notify.update({ 'type': 'success', 'message': '<strong>Success! Document Archived.</strong>' });
          }, 1000);
          var obtainSignPollingData = { tenantid: 0, documentId: 0, archiveOrRestore: false, archivedUsers: "", archivedOn: "", accountLevelArchived: false };
          obtainSignPollingData.tenantid = this.userData.tenantId;
          obtainSignPollingData.documentId = this.activeSignedDocument.id;
          obtainSignPollingData.archiveOrRestore = true;
          obtainSignPollingData.archivedOn = result['archiveSignatureDocument'].archivedOn;
          obtainSignPollingData.archivedUsers = result['archiveSignatureDocument'].archivedUsers;
          obtainSignPollingData.accountLevelArchived = result['archiveSignatureDocument'].accountLevelArchived;
          this._structureService.socket.emit("obtainSignPollingToServer", obtainSignPollingData);
          var activityData = {
            activityName: "Signature - " + action,
            activityType: "Signature Request",
            activityDescription: this.userData.displayName + " (Tenant-" + this.userData.tenantId + ") (User-" + this.userData.userId + ")" + action + " " + this.activeSignedDocument.message + " Document ( Status : " + this.activeSignedDocument.signatureStatus + ")",
            activityLinkageId: this.activeSignedDocument.id
          };
          this._structureService.trackActivity(activityData);
          // }
        }
      } else {
        if (this.activeSignedDocument.signatureStatus == "PENDING" || this.activeSignedDocument.signatureStatus == "SIGN_APPROVAL") {
          let index = this.rowData.findIndex(x => x == data.rowData);
          this.rowData[index].status = "PENDING";
          this.gridApi.refreshCells({ force: true });
          this.dashboardWidgets.forEach(element => {
            if (element.widgetValue == 'PENDING') {
              element.count = element.count + 1;
            }
            if (element.widgetValue == 'ARCHIVED') {
              element.count = element.count - 1;
            }
          });
          this.gridApi.deselectAll();
          this.gridApi.onFilterChanged();
          var notify = $.notify('Success! Document Restored');
          setTimeout(function () {
            notify.update({ 'type': 'success', 'message': '<strong>Success! Document Restored.</strong>' });
          }, 1000);
          var obtainSignPollingData = { tenantid: 0, documentId: 0, archiveOrRestore: false, archivedUsers: "", archivedOn: "", accountLevelArchived: false };
          obtainSignPollingData.tenantid = this.userData.tenantId;
          obtainSignPollingData.documentId = this.activeSignedDocument.id;
          obtainSignPollingData.archiveOrRestore = true;
          obtainSignPollingData.archivedOn = result['archiveSignatureDocument'].archivedOn;
          obtainSignPollingData.archivedUsers = result['archiveSignatureDocument'].archivedUsers;
          obtainSignPollingData.accountLevelArchived = result['archiveSignatureDocument'].accountLevelArchived;
          this._structureService.socket.emit("obtainSignPollingToServer", obtainSignPollingData);
          var activityData = {
            activityName: "Signature - " + action,
            activityType: "Signature Request",
            activityDescription: this.userData.displayName + " (Tenant-" + this.userData.tenantId + ") (User-" + this.userData.userId + ")" + action + " " + this.activeSignedDocument.message + " Document ( Status : " + this.activeSignedDocument.signatureStatus + ")",
            activityLinkageId: this.activeSignedDocument.id
          };
          this._structureService.trackActivity(activityData);
          // }
        } else if (this.activeSignedDocument.signatureStatus == "SIGNED") {

          let index = this.rowData.findIndex(x => x == data.rowData);
          this.rowData[index].status = "SIGNED";
          this.gridApi.refreshCells({ force: true });
          this.dashboardWidgets.forEach(element => {
            if (element.widgetValue == 'SIGNED') {
              element.count = element.count + 1;
            }
            if (element.widgetValue == 'ARCHIVED') {
              element.count = element.count - 1;
            }
          });
          this.gridApi.deselectAll();
          this.gridApi.onFilterChanged();
          var notify = $.notify('Success! Document Restored');
          setTimeout(function () {
            notify.update({ 'type': 'success', 'message': '<strong>Success! Document Restored.</strong>' });
          }, 1000);
          var obtainSignPollingData = { tenantid: 0, documentId: 0, archiveOrRestore: false, archivedUsers: "", archivedOn: "", accountLevelArchived: false };
          obtainSignPollingData.tenantid = this.userData.tenantId;
          obtainSignPollingData.documentId = this.activeSignedDocument.id;
          obtainSignPollingData.archiveOrRestore = true;
          obtainSignPollingData.archivedOn = result['archiveSignatureDocument'].archivedOn;
          obtainSignPollingData.archivedUsers = result['archiveSignatureDocument'].archivedUsers;
          obtainSignPollingData.accountLevelArchived = result['archiveSignatureDocument'].accountLevelArchived;
          this._structureService.socket.emit("obtainSignPollingToServer", obtainSignPollingData);
          var activityData = {
            activityName: "Signature - " + action,
            activityType: "Signature Request",
            activityDescription: this.userData.displayName + " (Tenant-" + this.userData.tenantId + ") (User-" + this.userData.userId + ")" + action + " " + this.activeSignedDocument.message + " Document ( Status : " + this.activeSignedDocument.signatureStatus + ")",
            activityLinkageId: this.activeSignedDocument.id
          };
          this._structureService.trackActivity(activityData);
          // }
        }
      }
    });
  }

  cancelSignatureDocument(action, data) {
    this.getActiveDocumentDetail(data);
    this._structureService.cancelSignatureDocument({ id: this.activeSignedDocument.id, action: action })
      .subscribe(result => {
        if (action == "CANCEL") {
          if (this.activeSignedDocument.signatureStatus == "PENDING") {
            let index = this.rowData.findIndex(x => x == data.rowData);
            this.rowData[index].status = "CANCELLED";
            this.gridApi.refreshCells({ force: true });
            this.dashboardWidgets.forEach(element => {
              if (element.widgetValue == 'CANCELLED') {
                element.count = element.count + 1;
              }
              if (element.widgetValue == 'PENDING') {
                element.count = element.count - 1;
              }
            });
            this.gridApi.deselectAll();
            this.gridApi.onFilterChanged();
            var notify = $.notify('Success! Document Cancelled');
            setTimeout(function () {
              notify.update({ 'type': 'success', 'message': '<strong>Success! Document Cancelled.</strong>' });
            }, 1000);
            var activityData = {
              activityName: "Signature - CANCEL",
              activityType: "Signature Request",
              activityDescription: this.userData.displayName + " (User-" + this.userData.userId + ") CANCEL " + this.activeSignedDocument.documentName + " Document ( Status : " + this.activeSignedDocument.signatureStatus + ")",
              activityLinkageId: this.activeSignedDocument.id
            };
          }
        }
      });
  }
  downloadSignatureDoc(data) {
    this.getActiveDocumentDetail(data);
    let document = this.activeSignedDocument;
    if (document.docDownloadStatus) {
      // this.router.navigate[document.url];
      var newWindow: any = window.open(this._structureService.serverBaseUrl + "/webapp/www/img/gif-loader.gif");
      var self = this;
      // console.log("form-download",this.fileUrlDownload);
      var t = Math.round(new Date().getTime() / 1000);
      if (document.url && document.url) {
        var fileName = t + '---' + document.url + '---' + this._structureService.getCookie('authenticationToken');
        // console.log("form-download",fileName);
        var filenameEncrypted = btoa(fileName);
        // console.log("form-download",filenameEncrypted);
        setTimeout(function () {
          var fileUrl = self._structureService.apiBaseUrl + '/citus-health/v4/document-download.php?filetoken=' + filenameEncrypted;
          // console.log("form-download",fileUrl);
          newWindow.location = fileUrl;
        });
      }
      //actions += `<a class="pull-right btn btn-sm" title="Download" *ngIf="document.docDownloadStatus" href="${document.url}" target="_blank"><i class="fa fa-download"></i></a>`;
    }
    if (!document.docDownloadStatus) {
      if (!document.cmisDownload) {
        //actions += `<a class="pull-right btn btn-sm" title="Download" *ngIf="!document.docDownloadStatus"  id="download-document"><i id="download-document" class="fa fa-download"></i></a>`;
      } else {
        // actions += `<a class="pull-right btn btn-sm" title="Download" *ngIf="!document.docDownloadStatus"  id="download-document" target="_blank" download="${document.message}" href="${document.documentUrl}" ><i id="download-document" class="fa fa-download"></i></a>`;
        // this.router.navigate[document.documentUrl];
        var newWindow: any = window.open(this._structureService.serverBaseUrl + "/webapp/www/img/gif-loader.gif");
        var self = this;
        // console.log("form-download",this.fileUrlDownload);
        var t = Math.round(new Date().getTime() / 1000);
        if (document.documentUrl && document.documentUrl) {
          var fileName = t + '---' + document.documentUrl + '---' + this._structureService.getCookie('authenticationToken');
          // console.log("form-download",fileName);
          var filenameEncrypted = btoa(fileName);
          // console.log("form-download",filenameEncrypted);
          setTimeout(function () {
            var fileUrl = self._structureService.apiBaseUrl + '/citus-health/v4/document-download.php?filetoken=' + filenameEncrypted;
            // console.log("form-download",fileUrl);
            newWindow.location = fileUrl;
          });
        }
      }
    }

  }
  viewdocumentCheck(data) {
    this.getActiveDocumentDetail(data);
    this._structureService.pahDocView = true;
    if (!this.activeSignedDocument.docDownloadStatus) {
      this._sharedService.goToInnerPage = true;
      this._sharedService.selectedSiteForDoc = this.activeSignedDocument.siteId;
      if (this.activeSignedDocument.signatureRequestData.isRead == false) {
        this._sharedService.readSignatureRequest.emit(this.activeSignedDocument);
        this._sharedService.updateSignatureRequestUnreadCount.emit({
          type: "decrement"
        });
      }
      let sender = 0;
      if (this.activeSignedDocument.senderTenant && this.activeSignedDocument.senderTenant != this.userData.tenantId) {
        sender = this.activeSignedDocument.senderTenant;
      }
      this._sharedService.applayDatatablesaveState = true;
      this.router.navigate(["/signature/signature-requests/" + this.activeSignedDocument.id + "/" + sender]);
    } else {
      this.router.navigate([this.activeSignedDocument.documentUrl]);
    }
  }
  viewMsgTag(e) {
    this.tagDetails = {
      show: true,
      activeForm: e.rowData
    };
    this.showTagMsg = true;
    $('#data-modal-tagged').modal('show');
    // this.userData = this._structureService.getUserdata();
    // this.tagsList = [];

    // if (this.userData.config.enable_filing_center == "1") {
    //   this.isFilingCenter = true;
    // } else {
    //   this.isFilingCenter = false;
    // }
    // this.getAlldocumentsSigned();
    // if (this.isFilingCenter) {
    //   this.getFolderLists(e);
    // } else {
    //   let flag = false;
    //   flag = this.detailsView(e);
    //   if (flag == true)
    //     setTimeout(function () {
    //       $('#data-modal-tagged').modal('show');
    //     }, 250);
    // }
  }
  closeDetailTagView() {
    $('#data-modal-tagged').modal('hide');
    this.showTagMsg = false;
  }
  detailsViewClose(e, close) {
    this.isDetail = !this.isDetail;
  }
  // detailsView(e) {
  //   this.activeTaggedForm = e.rowData;
  //   if (this.activeTaggedForm.patient.id != '0') {
  //     this.patientName = this.activeTaggedForm.patient.displayName;
  //     let dateVar = this.activeTaggedForm.patient.dateOfBirth.split('-');
  //     this.dateOfBirth = dateVar[1] + '/' + dateVar[2] + '/' + dateVar[0];
  //     this.dob = dateVar[1] + '-' + dateVar[2] + '-' + dateVar[0];
  //   }
  //   this.createdOn = new Date(this.activeTaggedForm.sentOn).toLocaleString('en-US', { timeZone: timezone.name() }).replace(/:\d{2}\s/, ' ');
  //   this.createdOn = this.createdOn.replace(/,/g, "");
  //   if (this.activeTaggedForm.sentOn.indexOf('-') > -1) {
  //     this.createdOn = new Date(this.activeTaggedForm.sentOn).toLocaleString('en-US', { timeZone: timezone.name() }).replace(/:\d{2}\s/, ' ');
  //     this.createdOn = this.createdOn.replace(/,/g, "");
  //   } else {

  //     this.createdOn = this.datePipe.transform(this.activeTaggedForm.sentOn * 1000, "MM/dd/yyyy") + ' ' + this.datePipe.transform(this.activeTaggedForm.sentOn * 1000, "shortTime");
  //   }
  //   if (this.activeTaggedForm.tags && this.activeTaggedForm.tags.length > 0) {
  //     this.activeTaggedForm.tags.filter((data) => {
  //       if (data.tagName.trim() != '') {
  //         this.tagsList.push(data.tagName);
  //       }
  //     });

  //     let tagMeta = this.activeTaggedForm.tags[0].tagMeta;
  //     this.fileNameFormat = "";
  //     this.folderName = "Select Filing Center";
  //     if (tagMeta && tagMeta != 'null' && tagMeta != null) {
  //       this.noTags = false;
  //       let metaValues = JSON.parse(tagMeta);
  //       this.fileNameFormat = metaValues.fileSaveFormat;
  //       if (this.fileNameFormat.trim() == "") {
  //         this.fileNameFormat = "";
  //       } else {
  //         this.generateFilename();
  //       }

  //       if (this.isFilingCenter) {
  //         if (this.folderLists && this.folderLists.length > 0) {
  //           this.folderNameDefault = this.folderLists.filter((row) => {
  //             if (row.folderName == metaValues.outgoingFilingCenter) {
  //               return true;
  //             }
  //           })
  //         } else {
  //           var notify = $.notify('Error in loading filing centers.');
  //           setTimeout(function () {
  //             notify.update({ 'type': 'warning', 'message': '<strong>Error in loading filing centers.</strong>' });
  //           }, 1000);
  //         }

  //         if (this.folderNameDefault && this.folderNameDefault[0]) {
  //           $('#sfilingCenterss').val(this.folderNameDefault[0].folderName).trigger('change');
  //           // $('#sfilingCenterss').val(this.folderNameDefault[0].folderName);
  //           this.folderName = this.folderNameDefault[0];
  //         } else {
  //           this.clearFilingCenter();
  //           this.folderName = "Select Filing Center";
  //         }
  //       }

  //     }
  //     else {
  //       /*this.noTags = true;
  //       this.folderName = "Select Filing Center";
  //       this.fileNameFormat="";*/
  //     }
  //   }
  //   this.msgId = this.activeTaggedForm.messageId;
  //   this.tags = "Progress Note";
  //   this.description = this.activeTaggedForm.message;
  //   if (this.activeTaggedForm.signedStatus == "Pending") {
  //     this.isApproveShow = true;
  //   }
  //   else {
  //     this.isApproveShow = false;
  //   }
  //   this.createdBy = this.activeTaggedForm.createdUser.displayName;
  //   this.approvedBy = (this.activeTaggedForm.signedStatus == "AutoApproved") ? "Auto Approved" : this.activeTaggedForm.approvedUserDisplayName;
  //   this.approveSignUrl = "";
  //   if (this.activeTaggedForm.sign != null && this.activeTaggedForm.sign != 'null') {
  //     this.approveSignUrl = this._structureService.getApiBaseUrl() + "writable/filetransfer/uploads/" + this.activeTaggedForm.sign;
  //   }
  //   let outGoingFileCenter = '';
  //   //this.fileNameFormat="{tagname}-{FirstName}-{LastName}-{DOB}";
  //   this.isDetail = !this.isDetail;
  //   this.imgUrl = "";
  //   this.pdfUrl = "";
  //   if (this.activeTaggedForm.createdSIGN != null) {
  //     this.imgUrl = this._structureService.getApiBaseUrl() + "writable/filetransfer/uploads/" + this.activeTaggedForm.createdSIGN;

  //   }
  //   return true;
  // }
  isArray(obj: any) {
    return Array.isArray(obj)
  }
  generateFilename() {
    this.fileNameFormat = this.fileNameFormat.replace('{tagname}', this.activeTaggedForm.tags[0].tagName);
    if (this.activeTaggedForm.patient.firstName != null) {
      this.fileNameFormat = this.fileNameFormat.replace('{FirstName}', this.activeTaggedForm.patient.firstName);
    }
    else {
      this.fileNameFormat = this.fileNameFormat.replace('{FirstName}', '');
    }
    if (this.activeTaggedForm.patient.lastName != null) {
      this.fileNameFormat = this.fileNameFormat.replace('{LastName}', this.activeTaggedForm.patient.lastName);
    }
    else {
      this.fileNameFormat = this.fileNameFormat.replace('{LastName}', '');
    }
    if (this.dob && this.dob != null) {
      this.fileNameFormat = this.fileNameFormat.replace('{DOB}', this.dob);
    }
    else {
      this.fileNameFormat = this.fileNameFormat.replace('{DOB}', '');
    }
    if (this.createdOn != null) {
      let crOn = this.createdOn;
      crOn = crOn.replace(/\//g, '').replace(/:/g, '').replace(/-/g, '').replace(/ /g, '');
      this.fileNameFormat = this.fileNameFormat.replace('{createdOn}', crOn);
    }
    else {
      this.fileNameFormat = this.fileNameFormat.replace('{createdOn}', '');
    }
    var date: Date = new Date();
    var datetostring = Date.parse(date.toString()) / 1000;
    var cdate = this.datePipe.transform(datetostring * 1000, "MM/dd/yyyy") + ' ' + this.datePipe.transform(datetostring * 1000, "shortTime");
    cdate = cdate.replace(/\//g, '').replace(/:/g, '').replace(/-/g, '').replace(/ /g, '');
    this.fileNameFormat = this.fileNameFormat.replace('{signedOn}', cdate);
    this.fileNameFormat = this.fileNameFormat.replace(/ /g, "-");
  }
  clearFilingCenter() {
    this.showClose = false;
    this.folderName = '';
    $('#sfilingCenterss').val('');
    $('#sfilingCenterss').select2({
      allowClear: false,
      placeholder: 'Select Filing Center',
      data: this.filingCenters
    });
  }
  // getFolderLists(e) {

  //   this._structureService.getTenantFilingCenterFolders('OUTGOING').then(
  //     (data) => {
  //       if (data['getTenantFilingCenterFolders'] && data['getTenantFilingCenterFolders'].length) {
  //         this.folderLists = data['getTenantFilingCenterFolders'];
  //         this.folderName = '';

  //         this.filingCenters = [];

  //         for (let i = 0; i < this.folderLists.length; i++) {
  //           var fname = this.folderLists[i].folderName;
  //           var ftype = this.folderLists[i].type;
  //           var id = '{"folderName":"' + fname + '","type":"' + ftype + '"}';
  //           var item = { id: fname, text: fname }
  //           this.filingCenters.push(item);
  //         }

  //         $('#sfilingCenterss').select2({
  //           allowClear: true,
  //           placeholder: 'Select Filing Center',
  //           data: this.filingCenters
  //         });
  //       }
  //       else {
  //         console.log("No folders");
  //       }
  //       let flag = false;
  //       flag = this.detailsView(e);
  //       if (flag == true)
  //         setTimeout(function () {
  //           $('#data-modal-tagged').modal('show');
  //         }, 250);
  //     });
  // }
  openSignpad(sendType) {
    this.sendType = sendType;
    if (this.sendType == "ApproveFile") {
      if (this.noTags) {
        var notify = $.notify('No tags available.');
        setTimeout(function () {
          notify.update({ 'type': 'warning', 'message': '<strong>No tags available.</strong>' });
        }, 1000);
        return false;
      }
      if (this.folderName == "Select Filing Center" || this.folderName == '') {
        var notify = $.notify('Please select a filing center.');
        setTimeout(function () {
          notify.update({ 'type': 'warning', 'message': '<strong>Please select a filing center.</strong>' });
        }, 1000);

        return false;
      }

      if (this.fileNameFormat.trim() == "") {

        /*var notify = $.notify('Specify Filename.');
        setTimeout(function () {
          notify.update({ 'type': 'warning', 'message': '<strong>Specify Filename.</strong>' });
        }, 1000);
        
        return false;*/
        swal({
          title: "Are you sure?",
          text: "Do you want to use default filename format?",
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default",
          confirmButtonClass: "btn-warning",
          confirmButtonText: "Ok",
          closeOnConfirm: true
        }, () => {

          this.fileNameFormat = "{tagname}-{FirstName}-{LastName}-{DOB}-{createdOn}-{signedOn}";
          this.generateFilename();
          this.childpad.show_popup();

        });
      } else {
        this.childpad.show_popup();
      }

    } else {
      this.childpad.show_popup();
    }

  }
  reloadTaggedMessage() {
    if (this.rowModelType == 'serverSide') {
      var datasourcee = ServerSideDatasourceApi(this, '');
      this.gridApi.setServerSideDatasource(datasourcee);
      this.gridApi.deselectAll();
    } else {
      this.getformData();
    }
  }
  viewPrescriptionOrders(e) {
    this.fileExt = e.rowData.fileName;
    const s = e.rowData.docFileUrl;
    this.url = s.substring(0, s.indexOf('?'));
    if (this.url == '') {
      swal(
        {
          title: '',
          text: this._ToolTipService.getTranslateData('MESSAGES.NO_ATTACHMENT_FOUND'),
          type: 'warning',
          confirmButtonClass: 'btn-warning',
          confirmButtonText: this._ToolTipService.getTranslateData('BUTTONS.OK'),
          closeOnConfirm: true
        },
        () => {

        });

    } else {
      this.showPdf(this.url);
    }
    const activityData = {
      activityName: "View document from Prescription Order worklist",
      activityType: "View Document",
      activityDescription: this.userData.displayName + " viewed the document " + this.fileExt + " from Prescription Order worklist in patient activity hub."
    };
    this._structureService.trackActivity(activityData);
  }
 showPdf(url) {
  this.pdfOrDoc = url;
  this.isModalOpen = true;
  this.cancelLabel = false;
  this.showIframe = true;
  this.showHeader = true;
  this.title = this._ToolTipService.getTranslateData('TITLES.VIEW_DOC');
  this.showFooter = false;
  this.PdfViewService.showPdfViewer(this.pdfOrDoc).then((modalBody) => {
      this.modalBody = modalBody;
      this.modals.show();
  });
}


  closePrescriptionOrdersModal() {
    this.modals.hide();
    this.isModalOpen = false;
    this.modalBody = '';
    this.pdfOrDocSrc = '';
    this.title = '';
    this.showIframe = false;
    this.cancelLabel = false;
  }
  viewDoc(e) {
    this.fileExt = e.rowData.fileName;
    this.url = e.rowData.docFileUrl;
    if (this.url == '') {
      swal(
        {
          title: '',
          text: this._ToolTipService.getTranslateData('MESSAGES.NO_ATTACHMENT_FOUND'),
          type: 'warning',
          confirmButtonClass: 'btn-warning',
          confirmButtonText: this._ToolTipService.getTranslateData('BUTTONS.OK'),
          closeOnConfirm: true
        },
        () => {

        });
    } else {
      this.showPdf(this.url);
    }
    const activityData = {
      activityName: "View document from document worklist",
      activityType: "View Document",
      activityDescription: this.userData.displayName + " viewed the document " + this.fileExt + " from Document worklist in patient activity hub."
    };
    this._structureService.trackActivity(activityData);
  }
  closeModal(): void {
    this.modals.hide();
    this.isModalOpen = false;
    this.modalBody = '';
    this.title = '';
    this.pdfOrDocSrc = '';
    this.showIframe = false;
    this.cancelLabel = false;
  }
  sendSignature(sign) {
    if ((sign.close == true) || (sign.close == "true")) {
      return false;
    }
    this.signature = sign.signature;
    if (this.sendType == "ApproveFile") {
      if (this.noTags) {
        var notify = $.notify('No tags available.');
        setTimeout(function () {
          notify.update({ 'type': 'warning', 'message': '<strong>No tags available.</strong>' });
        }, 1000);
        return false;
      }
      this.moveFilingCenter = true;
      this.folderType = 'OUTGOING';
      if (this.folderName == "Select Filing Center") {
        var notify = $.notify('Select Outgoing Filing Center.');
        setTimeout(function () {
          notify.update({ 'type': 'warning', 'message': '<strong>Select Outgoing Filing Center.</strong>' });
        }, 1000);

        return false;
      }
      else {
        this.folderName = this.folderName.folderName;
      }
      this.approveMessage();
    } else {
      this.folderName = '';
      this.filename = '';
      this.folderType = '';
      this.moveFilingCenter = false;
      this.approveMessage();
    }
  }
  approveMessage() {
    this._structureService.approveMessage({ sign: this.signature, messageId: this.msgId, userID: this.userData.userId, tenantId: this.userData.tenantId, tenantName: this.userData.tenantName, filename: this.fileNameFormat, folder: this.folderName, folderType: this.folderType, moveFilingCenter: this.moveFilingCenter }).subscribe((data) => {
      if (data['data']['chatSignatureApproveTaggedMessage'].signatureImage && data['data']['chatSignatureApproveTaggedMessage'].signatureImage !== 'failed') {
        this.isApproveShow = false;
        this.approveSignUrl = this._structureService.getApiBaseUrl() + "writable/filetransfer/uploads/" + data['data']['chatSignatureApproveTaggedMessage'].signatureImage;
        this.approvedBy = this.userData.displayName;
        let indexVar = this.rowData.findIndex(x => x.id == this.activeTaggedForm.id);
        if (indexVar > -1) {
          this.rowData[indexVar].approvedUserDisplayName = this.approvedBy;
          this.rowData[indexVar].sign = data['data']['chatSignatureApproveTaggedMessage'].signatureImage;
          this.rowData[indexVar].signedStatus = "Approved";
          this.gridApi.deselectAll();
          this.gridApi.onFilterChanged();
          var notify = $.notify('Success! Message approved.');
          setTimeout(function () {
            notify.update({ 'type': 'success', 'message': '<strong>Success! Message approved.</strong>' });
          }, 1000);
          // this.loadDataSignedDocs();

          if (this.moveFilingCenter) {
            var activityDat = {
              activityName: "Copied the Tagged Message to Filing center",
              activityType: "Filing Center",
              activityDescription: this.userData.displayName + " approved message tagged by " + this.createdBy + " copied to filing center named " + this.folderName + "(" + this.folderType + ") as " + this.fileNameFormat
            };
            this._structureService.trackActivity(activityDat);
          }
          var activityData = {
            activityName: "Approve Tagged Message",
            activityType: "forms",
            activityDescription: this.userData.displayName + " approved message(" + this.msgId + ") tagged by " + this.createdBy

          };
          this._structureService.trackActivity(activityData);

        }
        this.gridApi.deselectAll();
        this.gridApi.onFilterChanged();
      }
      else {
        let errorMsg = this._ToolTipService.getTranslateData('ERROR_MESSAGES.MESSAGE_APPROVAL');
          if(this._structureService.isMultiAdmissionsEnabled && data['errors'] && data['errors'].message === 'Invalid admissionId') {
            errorMsg = this._ToolTipService.getTranslateData('ERROR_MESSAGES.USER_NO_ACCESS_TO_PATIENT_SITE');
          }
          this._structureService.notifyMessage({ messge: errorMsg });
      }

    }, () => {
      this._structureService.notifyMessage({
        messge: this._ToolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG')
      });
    });
  }
  deleteMsgTag(e) {
    this.activeTaggedForm = e.rowData;

    swal({
      title: "Are you sure?",
      text: "You are going to delete the tagged message.",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Ok",
      closeOnConfirm: true
    }, () => {
      var messageIds = '';
      var taggedMessageDetails = JSON.parse(this.activeTaggedForm.taggedMessageGroups);
      for (let i = 0; i < taggedMessageDetails.length; i++) {
        messageIds += taggedMessageDetails[i].message_id + ',';
      }
      messageIds = messageIds.replace(/,$/, "");
      console.log("this.activeTaggedForm", this.activeTaggedForm, messageIds)
      this._structureService.deleteTagForm(this.activeTaggedForm.id, messageIds, this.activeTaggedForm.tagStatus).subscribe((data) => {
        if (data['data']['chatSignatureApproveTaggedMessageArchive'].signatureImage === 'success') {
          this._structureService.notifyMessage({message: this._ToolTipService.getTranslateData('SUCCESS_MESSAGES.TAGGED_MSG_DELETED'), type: CONSTANTS.notificationTypes.success});
        } else {
          let errorMsg = this._ToolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG');
          if(this._structureService.isMultiAdmissionsEnabled) {
            errorMsg = this._ToolTipService.getTranslateData('ERROR_MESSAGES.USER_NO_ACCESS_TO_PATIENT_SITE');
          }
          this._structureService.notifyMessage({message: errorMsg});
        }
        if (this.rowModelType == 'serverSide') {
          var datasourcee = ServerSideDatasourceApi(this, '');
          this.gridApi.setServerSideDatasource(datasourcee);
          this.gridApi.deselectAll();
        } else {
          const filterList = this.rowData.filter(x => Number(x.id) !== Number(e.rowData.id));
          this.rowData = filterList;
          this.setDefaultWidgetConfig(this.rowData);
        }
      });
    });
  }
  getAlldocumentsSigned() {
    this._structureService.getTaggedFormsAll(timezone.name()).then((res) => {
      this.taggedFormsList = res['getSessionTenant'] ? JSON.parse(JSON.stringify(res['getSessionTenant']['taggedFormsAll'])) : [];
      // this.loadDataSignedDocs();
    });

  }
  viewForm(e) {
    let formsUrl: any = `${this._structureService.machFormUrl}${APIs.machFormEmbed}?id=${e.rowData.form_id}&staffSubmissionId=${e.rowData.staff_submission_id}&patientId=${e.rowData.recipient_id}&tenantId=${e.rowData.tenant_id}`;
    if(this._structureService.isMultiAdmissionsEnabled) {
      formsUrl += `&admissionId=${e.rowData.admissionId}`;
    }
    const repData = `<div style="width:100%;" class="structured-form-modal">
    <iframe onload="$('.structured-form-modal').scrollTop(0);" allowTransparency="true" class="structured-form-dekstop" id="structured-form-dekstop" frameborder="0" scrolling="no" style="width:100%;border:none;pointer-events: none;" src="${ formsUrl}" ></iframe>
    </div>`;
    $(".dataFormView").html(repData);
    $('#data-modal').modal('show');
  }
  editForm(e) {
    this.showEdit = true;
    this._structureService.pahEdit = true;
    this.activeStrucuredForms = e.rowData;
    this._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)), 1);
    localStorage.setItem('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)));
    if (this.activeStrucuredForms.form_submission_id) {
      // this.showEdit = false;
      this.editDetails = {
        formId: this.activeStrucuredForms.form_id ? this.activeStrucuredForms.form_id : this.activeStrucuredForms.formId,
        entryId: this.activeStrucuredForms.form_submission_id,
        userId: this.userData.userId,
        staffFacing: this.activeStrucuredForms.facing_new,
        formName: this.activeStrucuredForms.form_name,
        fromId: this.activeStrucuredForms.from_id,
        toName: this.activeStrucuredForms.createdUser,
        roleId: this.activeStrucuredForms.fgrp,
        displayName: this.activeStrucuredForms.createdUser,
        tenantId: this.activeStrucuredForms.tenant_id,
        fromUsername: this.activeStrucuredForms.username,
        sentId: this.activeStrucuredForms.sent_id,
        formStatus: this.activeStrucuredForms.formStatus
      }
      $('#edit-modal').modal('show');
    }
  }
  allowEdit(e) {
    this.activeStrucuredForms = e.rowData;
    var swal_options;
    if (this.activeStrucuredForms.formStatus.toString().toLowerCase() === Status.Completed) {
      swal_options = {
        text: `${this._ToolTipService.getTranslateData('MESSAGES.FORM_EDIT_PERMISSION_GRANT')} ${this.activeStrucuredForms.form_name}`,
        type: CONSTANTS.notificationTypes.input,
        inputPlaceholder: this._ToolTipService.getTranslateData('PLACEHOLDERS.ENTER_OPTIONAL_MSG')
      }
    } else {
      swal_options = {
        text: `${this._ToolTipService.getTranslateData('MESSAGES.FORM_EDIT_PERMISSION_DENY')} ${this.activeStrucuredForms.form_name}`,
      }
    }
    this._structureService.showAlertMessagePopup(swal_options).then((data) => {
      if (data !== false) {
        $('[id*="allow_edit"]').bind('click', false);
        this.allowEditConfirm(e, data);
      }
    })
  }
  allowEditConfirm(e, msg) {
    e.rowData.recipient_id = e.rowData.from_id;
    this.activeStrucuredForms = e.rowData;
    
    this.userData = JSON.parse(this._structureService.userDetails);
    this.activeStrucuredForms.loggedinuserid = this.userData.userId;
    this._formsService.allowEditForm(this.activeStrucuredForms,msg).then((result: any) => {
      const nameeditallow = "edit-" + this.activeStrucuredForms.form_id + this.activeStrucuredForms.form_submission_id;
      this.activeStrucuredForms.allow_edit = result.allowedit;
      this.allow_edit = result.allowedit;
      let activityData = {
        activityName: "Allow Edit-pah",
        activityType: "structured forms",
        activityDescription: ""
      };
      if (result.allowedit == 0) {
        const title = this._ToolTipService.getTranslateData('BUTTONS.ALLOWEDIT');
        $("[name=" + nameeditallow + "]").attr('title', title).html(`<i data-edit class="icmn-pencil2"></i> ${title}`);
        this._structureService.notifyMessage({
          messge: this._ToolTipService.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_EDIT_PERMISSION_DENIED', {formName: this.activeStrucuredForms.form_name}),
          delay: NOTIFY_DELAY_TIME_COMMON,
          type: CONSTANTS.notificationTypes.success
        });
        activityData['activityDescription'] = "Edit has been denied for " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") by " + this.userData.displayName;
      }
      if (result.allowedit == 1) {
        if ((this.activeStrucuredForms.facing_new == 1 && this.activeStrucuredForms.userid == this.userData.userId) || (this.activeStrucuredForms.facing_new == 0 && this.activeStrucuredForms.from_id == this.userData.userId) || (this.activeStrucuredForms.facing_new == 2 && this.activeStrucuredForms.from_id == this.userData.userId)) {
          this._structureService.notifyMessage({
            messge: this._ToolTipService.getTranslateData('MESSAGES.FROM_SENT_FOR_EDIT_TO_PENDING'),
            delay: 5000,
            type: CONSTANTS.notificationTypes.success
          });
        } else {
          $("[name=" + nameeditallow + "]").attr('title', this._ToolTipService.getTranslateData('TITLES.DENY_EDIT')).html('<i data-edit class="icmn-undo2"></i>');

          this._structureService.notifyMessage({
            messge: this._ToolTipService.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_EDIT_PERMISSION_GRANTED', { formName: this.activeStrucuredForms.form_name }),
            delay: NOTIFY_DELAY_TIME_COMMON,
            type: CONSTANTS.notificationTypes.success
          });
        }
        this._formsService.notifyAfterAllowEdit(this.activeStrucuredForms, result, 'pah', msg);
        activityData['activityDescription'] = "Edit has been granted for " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") by " + this.userData.displayName;
      }
      this._structureService.trackActivity(activityData);
      var data = { id: this.activeStrucuredForms.form_id, allowEdit: this.activeStrucuredForms.allow_edit, toId: this.activeStrucuredForms.patient_id != "0" ? (this.activeStrucuredForms.userid || this.activeStrucuredForms.recipient_id) : ((result.allowedit == 1) ? this.activeStrucuredForms.from_id : this.activeStrucuredForms.recipient_id), form_submission_id: this.activeStrucuredForms.form_submission_id, message: msg };
      this._structureService.socket.emit("updateFormAllowEdit", data);
      var datasourcee = ServerSideDatasourceApi(this, '');
      this.gridApi.setServerSideDatasource(datasourcee);
      this.gridApi.deselectAll();
    });
  }
  populateData(strucuredFormss) {
    this.rowData = [];
    var counttoshow = $("#forms-to-show").val();
    var arrLength = strucuredFormss.length;
    var arr = strucuredFormss.sort(function (a, b) { if (a.senton - b.senton) return true });

    if (this.reverseFlag && arrLength > 0) {
      if (arr[0].senton < arr[(arr.length - 1)].senton && this.isActive == 'Pending')
        arr = arr.reverse();
      if (this.isActive != 'Pending') {
        arr = arr.reverse();
      }
      this.reverseFlag = false;
    }

    this.actualstrucuredForms = arr;

    if (this.isActive == 'Completed' && arrLength > 0) {
      var tt = arr.sort(function (a, b) { return b.senton - a.senton });
      arr = tt;
    }
    if (arr.length > (counttoshow - 1)) {
      var strucuredForms = arr.slice(0, counttoshow);
    }
    else {
      var strucuredForms = arr;

    }
    var self = this;
    function format(row) {
      var formsUrl: any = self._structureService.machFormUrl + APIs.machFormEmbed + '?id=' + row.form_id;
      return `<div style="width:100%;" class="structured-form-modal">
      <iframe onload="$('.structured-form-modal').scrollTop(0);" allowTransparency="true" class="structured-form-dekstop" id="structured-form-dekstop" frameborder="0" scrolling="no" style="width:100%;border:none;pointer-events: none;" src="${ formsUrl}" ></iframe>
      </div>`;
    }
    var self = this;
    this.rowData = strucuredForms;
  }
  resendForm(e) {
    this.activeStrucuredForms = e.rowData;
    console.log(this.activeStrucuredForms);
    var recipients = this.activeStrucuredForms.recipient_id;
    var message = this.activeStrucuredForms.message
    var selectedRecipients = [];
    var selectedRecipientsPolling = [];
    
    selectedRecipientsPolling.push({
    userid:this.activeStrucuredForms.facing_new === 1 ? this.activeStrucuredForms.from_id : this.activeStrucuredForms.recipient_id,
    organizationMasterId: this.userData.organizationMasterId,
      formSendMode: this.userData.config.enable_appless_model == 1 && this.activeStrucuredForms.applessMode == '1' ? 'appless' : 'inapp',
      applessMode: 'both'
    });
    
    var practitioner = 0;
    if (this.activeStrucuredForms.facing_new == 2) {
    practitioner = 1;
    }
    
    var practitionerAssociatePatient = [];
    
    if (this.activeStrucuredForms.facing_new == 2) {
    practitionerAssociatePatient.push(this.activeStrucuredForms.caregiver_userid);
    }
    
    if (this.activeStrucuredForms.caregiver_userid) {
    selectedRecipients.push(this.activeStrucuredForms.recipient_id + '--' + this.activeStrucuredForms.caregiver_userid);
    } else {
    selectedRecipients.push(this.activeStrucuredForms.recipient_id);
    }
    
    var formSendMode = '';
    
    if (this.userData.config.enable_appless_model == 1 && this.activeStrucuredForms.applessMode == '1') {
      formSendMode = FormSendMode.APPLESS;
    }
    
    var data = {
    "form_id": this.activeStrucuredForms.form_id,
    "recipients": selectedRecipients,
    "userId": this.activeStrucuredForms.from_id,
    "message": message,
    "previousSendId": this.activeStrucuredForms.sent_id,
    "resend": true,
    "practitioner": practitioner,
    "practitionerAssociatePatient": practitionerAssociatePatient,
    "formSendMode": (formSendMode && formSendMode == FormSendMode.APPLESS) ? FormSendMode.APPLESS : FormSendMode.INAPP,
    "applessMode": FormSendMode.BOTH
    };
    let deepLinking = {
    pushType: "",
    state: "eventmenu.forms",
    stateParams: {},
    sentId: ''
    };
    
    let pushMessage = "New Form to fill out";//"[Reminder] "+
    swal({
    title: "Are you sure?",
    text: "Do you want to resend this form?",
    type: "warning",
    showCancelButton: true,
    cancelButtonClass: "btn-default",
    confirmButtonClass: "btn-warning",
    confirmButtonText: "Ok",
    closeOnConfirm: true
    }, () => {
    this._formsService.resendForms(data).then((result: any) => {
    var self = this;
    if (this.rowModelType != 'serverSide') {
    this.rowData.find(x => Number(x.rowNumber) == Number(e.rowData.rowNumber)).senton = Date.now();
    this.rowData.find(x => Number(x.rowNumber) == Number(e.rowData.rowNumber)).lastRemindedOn = '';
    this.rowData.find(x => Number(x.rowNumber) == Number(e.rowData.rowNumber)).sentDate = '';
    this.gridApi.setRowData(this.rowData);
    this.setDefaultWidgetConfig(this.rowData);
    }
    this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", selectedRecipientsPolling);//Need to commnd after finish form inbox
    this._structureService.socket.emit("sendFormsToRecipients", selectedRecipientsPolling);
    
    /*patient reminder for form starts*/
    var otherDatas = {
    patientReminderTime: this.userData.config.patient_reminder_time * 1,
    patientReminderTypes: this.userData.config.patient_reminder_types,
    messageReplyTimeout: this.userData.config.message_reply_timeout,
    sentId: result.sentId,
    senderName: this.userData.displayName,
    tenantId: this.userData.tenantId,
    serverBaseUrl: this._structureService.serverBaseUrl,
    apiVersion: this._structureService.version,
    message: "You have new form to fillout",//[Reminder] 
    formReminderType: this.userData.config.patient_reminder_checking_type,
    environment: this._structureService.environment
    }
    this._structureService.socket.emit("ReminderForForm", selectedRecipientsPolling, otherDatas);
    /*patient reminder for form ends*/
    deepLinking.sentId = result.sentId;
    const formResendNotificationData = {
      sourceId: CONSTANTS.notificationSource.form,
      sourceCategoryId: CONSTANTS.notificationSourceCategory.formReSendNotification
    };
    this._structureService.sentPushNotification(selectedRecipientsPolling, 0, pushMessage, '', deepLinking, '', '', undefined, undefined, undefined, undefined, formResendNotificationData);
    if (result.status == 1) {
    var activityData = {
    activityName: "Resend Form Success",
    activityType: "structured forms",
    activityDescription: this.userData.displayName + " successfully resend form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") to " + selectedRecipients.toString()
    };
    this._structureService.trackActivity(activityData);
    this._structureService.notifyMessage({
    messge: this.activeStrucuredForms.form_name + ' has been resend successfully',
    delay: 1000,
    type: 'success'
    });
    } else {
    var activityData = {
    activityName: "Resend Form Fail",
    activityType: "structured forms",
    activityDescription: this.userData.displayName + " form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") to " + selectedRecipients.toString() + " resending failed"
    };
    this._structureService.trackActivity(activityData);
    this._structureService.notifyMessage({
    messge: this.activeStrucuredForms.form_name + ' senting failed',
    delay: 1000
    });
    }
    // if (this.isActive == "pending") {
    // $(this.dTable.column(7).header()).text('Sent On');
    // this.dTable.column(6).visible(false);
    // }
    });
    var datasourcee = ServerSideDatasourceApi(this, '');
    this.gridApi.setServerSideDatasource(datasourcee);
    this.gridApi.deselectAll();
    });
    }
  sendPolling(type, notifyUsers) {
    var selectedRecipientsPolling = [];
    if (type == 'Completed') {
      if (this.userData.userId != this.activeStrucuredForms.from_id) {
        notifyUsers.push({ userid: this.activeStrucuredForms.from_id });
      }
      if (this.userData.userId != this.activeStrucuredForms.userid) {
        notifyUsers.push({ userid: this.activeStrucuredForms.userid });
      }
      selectedRecipientsPolling = notifyUsers;
    } else {
      if (this.userData.userId != this.activeStrucuredForms.from_id) {
        selectedRecipientsPolling.push({ userid: this.activeStrucuredForms.from_id });
      }
      if (this.userData.userId != this.activeStrucuredForms.recipient_id) {
        selectedRecipientsPolling.push({ userid: this.activeStrucuredForms.recipient_id });
      }
    }
    this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", selectedRecipientsPolling);
  }
  restoreForms(e) {
    this.activeStrucuredForms = e.rowData;
    if (this.activeStrucuredForms.created_date) {
      this.activeStrucuredForms.isActive = 'completed';
    } else {
      this.activeStrucuredForms.isActive = 'pending';
    }
    this.activeStrucuredForms.deleted_by = this.userData.userId;
    NProgress.start();
    this._formsService.restoreArchivedForm(this.activeStrucuredForms).then((result: any) => {
      var activityData: any = {};
      if (result.status == '1') {
        NProgress.done();
        let self = this;
        if (this.activeStrucuredForms.isActive == 'Completed') {
          this.sendPolling('Completed', result.notifyUsers);
          activityData.activityName = "Restore Completed Form";
          if (this.rowModelType != 'serverSide') {
            this.rowData.find(x => Number(x.rowNumber) == Number(e.rowData.rowNumber)).formStatus = 'Completed';
            this.rowData.find(x => Number(x.rowNumber) == Number(e.rowData.rowNumber)).is_deleted = '0';
            this.gridApi.setRowData(this.rowData);
            this.setDefaultWidgetConfig(this.rowData);
          }
          this._structureService.notifyMessage({
            messge: 'Successfully restored the submitted form - ' + this.activeStrucuredForms.form_name,
            delay: 1000,
            type: 'success'
          });
        } else {
          this.sendPolling('Pending', result.notifyUsers);
          activityData.activityName = "Restore Pending Form";
          if (this.rowModelType != 'serverSide') {
            this.rowData.find(x => Number(x.rowNumber) == Number(e.rowData.rowNumber)).formStatus = 'Pending';
            this.rowData.find(x => Number(x.rowNumber) == Number(e.rowData.rowNumber)).is_deleted = '0';
            this.gridApi.setRowData(this.rowData);
            this.setDefaultWidgetConfig(this.rowData);
          }
          this._structureService.notifyMessage({
            messge: 'Successfully restored the submitted form - ' + this.activeStrucuredForms.form_name,
            delay: 1000,
            type: 'success'
          });
          activityData.activityType = "structured forms";
          activityData.activityDescription = this.userData.displayName + " restored form " + this.activeStrucuredForms.form_name + " (formId " + this.activeStrucuredForms.form_id + ",sendId " + this.activeStrucuredForms.sent_id + ") successfully";
          this._structureService.trackActivity(activityData);
        }
      } else {
        NProgress.done();
        activityData = {
          activityName: "Restore Completed Form",
          activityType: "structured forms",
          activityDescription: this.userData.displayName + " restored form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") failed"
        };
        this._structureService.trackActivity(activityData);
      }
      var datasourcee = ServerSideDatasourceApi(this, '');
      this.gridApi.setServerSideDatasource(datasourcee);
      this.gridApi.deselectAll();
    });
  }
  archiveSubmitedForm(e) {
    NProgress.start();
    this.activeStrucuredForms = e.rowData;
    this.isActive = e.rowData.formStatus;
    if (this.isActive == "Completed") {
      this.activeStrucuredForms.isActive = "completed";
    }
    else {
      this.activeStrucuredForms.isActive = "pending";
    }
    this.activeStrucuredForms.downloadTime = new Date().toLocaleString();
    var createdontime;
    if (this.activeStrucuredForms.senton.toString().length == 13) {
      createdontime = this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton));
    } else {
      createdontime = this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton) * 1000);
    }
    this.activeStrucuredForms.createdOn = createdontime;
      this._formsService.archiveSubmittedForm(this.activeStrucuredForms).then((result: any) => {
      var activityData: any = {};
      var notifyResult: any = result;
      if (result.status == '1') {
        if (notifyResult.defaultfilingCenter) {
          activityData.activityName = "Form Copied to Filing Center";
          activityData.activityType = "structured forms";
          activityData.activityDescription = this.userData.displayName + " archived form " + this.activeStrucuredForms.form_name + " (formId " + this.activeStrucuredForms.form_id + ",sendId " + this.activeStrucuredForms.sent_id + ") successfully and copied to filing center named " + notifyResult.defaultfilingCenter;
          this._structureService.trackActivity(activityData);
        }
        NProgress.done();
        if (this.isActive == 'Completed') {
          if (this.rowModelType == 'serverSide') {
            var datasourcee = ServerSideDatasourceApi(this, '');
            this.gridApi.setServerSideDatasource(datasourcee);
            this.gridApi.deselectAll();
          } else {
            this.getformData();
            this.removeScopeFormOnArchive(this.activeStrucuredForms);
          }
          // this._formsService.getAllTaggedForms(this._structureService.getCookie('tenantId'), this.userData.roleId, timezone.name(), true, false).then((result: any) => {
          //   this.strucuredFormsCopy = result;
          this._structureService.notifyMessage({
            messge: 'Successfully archived the completed form - ' + this.activeStrucuredForms.form_name,
            delay: 1000,
            type: 'success'
          });
          activityData.activityName = "Archive Completed Form";
          this.sendPolling('completed', notifyResult.notifyUsers);
          // });
        } else {
          if (this.rowModelType == 'serverSide') {
            var datasourcee = ServerSideDatasourceApi(this, '');
            this.gridApi.setServerSideDatasource(datasourcee);
            this.gridApi.deselectAll();
          } else {
            this.getformData();
            this.removeScopeFormOnArchive(this.activeStrucuredForms);
          }
          activityData.activityName = "Archive Pending Form";
          this._structureService.notifyMessage({
            messge: 'Successfully archived the pending form - ' + this.activeStrucuredForms.form_name,
            delay: 1000,
            type: 'success'
          });
          this.sendPolling('Pending', result.notifyUsers);
        }
        activityData.activityType = "structured forms";
        activityData.activityName = "Archived Form successfully";
        activityData.activityDescription = this.userData.displayName + " archived form " + this.activeStrucuredForms.form_name + " (formId " + this.activeStrucuredForms.form_id + ",sendId " + this.activeStrucuredForms.sent_id + ") successfully";
        this._structureService.trackActivity(activityData);

      } else {
        NProgress.done();
        activityData = {
          activityName: "Archive Completed Form",
          activityType: "structured forms",
          activityDescription: this.userData.displayName + " archived form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") failed"
        };
        this._structureService.trackActivity(activityData);

      }
    });
  }

  cancelDraftForm(e) {
    let self = this;
    this.activeStrucuredForms = e.rowData;
    swal({
      title: "Are you sure?",
      text: "You are going to cancel " + this.activeStrucuredForms.form_name,
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Ok",
      closeOnConfirm: true
    },
      function (isConfirm) {
        if (isConfirm) {
          NProgress.start();
          self.isActive = e.rowData.formStatus;
          self.activeStrucuredForms.isActive = self.isActive.toLowerCase();

          var createdontime;
          if (e.rowData.formStatus == "Drafts") {
            self.activeStrucuredForms.downloadTime = new Date().toLocaleString();
            var createdontime;
            if (self.activeStrucuredForms.senton.toString().length == 13) {
              createdontime = self.formpPipe.transform(parseInt(self.activeStrucuredForms.senton));
            } else {
              createdontime = self.formpPipe.transform(parseInt(self.activeStrucuredForms.senton) * 1000);
            }
            self.activeStrucuredForms.createdOn = createdontime;
            // TODO: esi_code_for_patient_identity decommisioned, remove usage if it is not causing any issue
            self.activeStrucuredForms.config_identity_value = self.userData.config.esi_code_for_patient_identity;
            self.activeStrucuredForms.config_staff_identity = self.userData.config_replica.esi_code_for_staff_identity;
            self.activeStrucuredForms.config_staff_name = self.userData.config_replica.esi_code_for_staff_name;
            self.activeStrucuredForms.enable = self.userData.config.enable_progress_note_integration;
            self.activeStrucuredForms.deleted_by = self.userData.userId;
            self._formsService.archiveDraftForm(self.activeStrucuredForms).then((result: any) => {
              if (result.status == 1 || result.status == "1") {
                if (self.rowModelType == 'serverSide') {
                  var datasourcee = ServerSideDatasourceApi(self, '');
                  self.gridApi.setServerSideDatasource(datasourcee);
                  self.gridApi.deselectAll();
                } else {
                  self.getformData();
                  self.removeScopeFormOnArchive(this.activeStrucuredForms);
                }
                self._structureService.notifyMessage({
                  messge: 'Successfully canceled the draft of ' + self.activeStrucuredForms.form_name,
                  delay: 1000,
                  type: 'success'
                });
                console.log("resultN", result);
                var activityData: any = {};
                activityData = {
                  activityName: "Cancel Draft Form - collaborate enabled",
                  activityType: "structured forms"
                };
                activityData.activityDescription = self.userData.displayName + " canceled draft form " + self.activeStrucuredForms.form_name + " (formId " + self.activeStrucuredForms.form_id + ",sendId " + self.activeStrucuredForms.sent_id;
                self._structureService.trackActivity(activityData);
              } else {
                var activityData: any = {};
                activityData = {
                  activityName: "Cancel Draft Form -collaborate enabled",
                  activityType: "structured forms"
                };
                activityData.activityDescription = self.userData.displayName + " failed canceled draft form " + self.activeStrucuredForms.form_name + " (formId " + self.activeStrucuredForms.form_id + ",sendId " + self.activeStrucuredForms.sent_id;
                self._structureService.trackActivity(activityData);
                self._structureService.notifyMessage({
                  messge: 'Failed Cancel from draft forms',
                  delay: 1000,
                  type: 'warning'
                });
              }
              self._structureService.socket.emit("sendFormsToRecipients", [{
                senderId: self.userData.userId
              }]);
              NProgress.done();
            });
          } else {
            if (self.activeStrucuredForms.senton.toString().length == 13) {
              createdontime = self.formpPipe.transform(parseInt(self.activeStrucuredForms.senton));
            } else {
              createdontime = self.formpPipe.transform(parseInt(self.activeStrucuredForms.senton) * 1000);
            }
            self.activeStrucuredForms.createdOn = createdontime;
            self.activeStrucuredForms.cancelForm = true;
            self._formsService.archiveSubmittedForm(self.activeStrucuredForms).then((result: any) => {
              var activityData: any = {};
              if (result.status == '1') {
                if (self.rowModelType == 'serverSide') {
                  var datasourcee = ServerSideDatasourceApi(self, '');
                  self.gridApi.setServerSideDatasource(datasourcee);
                  self.gridApi.deselectAll();
                } else {
                  self.getformData();
                  self.removeScopeFormOnArchive(this.activeStrucuredForms);
                }
                NProgress.done();
                self._structureService.notifyMessage({
                  messge: 'Successfully Canceled the pending form - ' + self.activeStrucuredForms.form_name,
                  delay: 1000,
                  type: 'success'
                });
                activityData.activityType = "structured forms";
                activityData.activityName = "Cancel Pending Form";
                activityData.activityDescription = self.userData.displayName + " canceled form " + self.activeStrucuredForms.form_name + " (formId " + self.activeStrucuredForms.form_id + ",sendId " + self.activeStrucuredForms.sent_id + ",receipientId " + self.activeStrucuredForms.recipient_id + ") successfully";
                self._structureService.trackActivity(activityData);
                // this.removeScopeFormOnArchive(this.activeStrucuredForms);
                self._structureService.socket.emit("sendFormsToRecipients", [{ userid: self.activeStrucuredForms.recipient_id, senderId: self.activeStrucuredForms.from_id }]);

              } else {
                NProgress.done();
                activityData = {
                  activityName: "Cancel Pending Form",
                  activityType: "structured forms",
                  activityDescription: self.userData.displayName + " canceled form " + self.activeStrucuredForms.form_name + " (" + self.activeStrucuredForms.form_id + ",sendId " + self.activeStrucuredForms.sent_id + ",receipientId " + self.activeStrucuredForms.recipient_id + ") failed"
                };
                self._structureService.trackActivity(activityData);
              }
            })
          }
        }
      });
  }
  cancelForm(e) {
    let rowData = e.rowData;
    rowData.recipient_id = rowData['recipient_id'] ? rowData['recipient_id']: rowData['from_id'];
    rowData.id = rowData.AFR_id ? rowData.AFR_id : rowData.id;
    this._formsService.cancelForm(rowData, 'patient activity hub').subscribe((res) => {
      if(res[rowData.id].success) {
        if (this.rowModelType === DataLoadingType.SERVERSIDE) {
          const datasourcee = ServerSideDatasourceApi(this, '');
          this.gridApi.setServerSideDatasource(datasourcee);
          this.gridApi.deselectAll();
        } else {
          this.getformData();
          this.removeScopeFormOnArchive(rowData);
        }
        this._structureService.socket.emit("sendFormsToRecipients", [{ userid: rowData.recipient_id, senderId: rowData.from_id }]);
      }
    });
  }
  
 /** 
   form is not Send to CPR 
    generate files
  */
noConfirmSendToCpr()
{
    $('[id*="move_fc"]').bind('click', false);
    let formData;
    if (this.activeStrucuredForms.patient_id != '0' && (this.isActive == 'completed' || this.isActive == 'archived') && this.activeStrucuredForms.created_date) {
        // staff
        formData = {
            "userid": this.activeStrucuredForms.userid,
            "tenantId": this.activeStrucuredForms.tenant_id,
            "recipient_id": this.activeStrucuredForms.userid,
            "formId": this.activeStrucuredForms.form_id,
            "staffFacing": "true",
            "form_submission_id": this.activeStrucuredForms.form_submission_id,
            "form_id": this.activeStrucuredForms.form_id,
            "form_name": this.activeStrucuredForms.form_name,
            "patient_id": "",
            "id": "",
            "regenerate":"true",
            "regenerateUser":this.userData.userId,
            "patientUser": this.activeStrucuredForms.from_id,
            "patient_user": this.activeStrucuredForms.patient_id,
            "generate_send_id": this.activeStrucuredForms.sent_id
        }
    } else {
        if (this.activeStrucuredForms.facing_new == 2) {
            formData = {
                "userid": this.activeStrucuredForms.from_id,
                "tenantId": this.activeStrucuredForms.tenant_id,
                "recipient_id": this.activeStrucuredForms.userid,
                "formId": this.activeStrucuredForms.form_id,
                "staffFacing": null,
                "form_submission_id": this.activeStrucuredForms.form_submission_id,
                "form_name": this.activeStrucuredForms.form_name,
                "id": "",
                "regenerate":"true",
                "regenerateUser":this.userData.userId,
                "associate_patient_id": this.activeStrucuredForms.patient_associated_id,
                "loggedinUserId": this.userData.userId,
                "patient_user": this.activeStrucuredForms.associated_user_id,
                "generate_send_id": this.activeStrucuredForms.sent_id
            }
        } else {
            formData = {
                "userid": this.activeStrucuredForms.from_id,
                "tenantId": this.activeStrucuredForms.tenant_id,
                "recipient_id": this.activeStrucuredForms.userid,
                "formId": this.activeStrucuredForms.form_id,
                "staffFacing": null,
                "regenerate":"true",
                "regenerateUser":this.userData.userId,
                "form_submission_id": this.activeStrucuredForms.form_submission_id,
                "patient_user": this.activeStrucuredForms.from_id,
                "generate_send_id": this.activeStrucuredForms.sent_id
            }
        }
    }

    this._formsService.filingCenterSubmittedForm(formData).then((data: any) => {
        if (data.filename) {
            this._structureService.notifyMessage({
                messge: 'Files for ' + this.activeStrucuredForms.form_name + ' generated successfully',
                delay: 1000,
                type: 'success'
            });
            $('[id*="move_fc"]').unbind('click', false);
        }
    });
}
 /** 
    Send the form to CPR and 
    generate files
  */
sendToCpr(){
   
    swal({
        title: "Are you sure?",
        text: "Do you want to generate files for this form?",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true
    }, (confirm) => {
        if (confirm) {
            $('[id*="move_fc"]').bind('click', false);
            let formData;
            if (this.activeStrucuredForms.patient_id != '0' && (this.isActive == 'completed' || this.isActive == 'archived') && this.activeStrucuredForms.created_date) {
                // staff
                console.log(this.activeStrucuredForms);
                formData = {
                    "userid": this.activeStrucuredForms.userid,
                    "tenantId": this.activeStrucuredForms.tenant_id,
                    "recipient_id": this.activeStrucuredForms.userid,
                    "formId": this.activeStrucuredForms.form_id,
                    "staffFacing": "true",
                    "form_submission_id": this.activeStrucuredForms.form_submission_id,
                    "form_id": this.activeStrucuredForms.form_id,
                    "form_name": this.activeStrucuredForms.form_name,
                    "patient_id": "",
                    "id": "",
                    "regenerate":"true",
                    "regenerateUser":this.userData.userId,
                    "patientUser": this.activeStrucuredForms.from_id,
                    "patient_user": this.activeStrucuredForms.patient_id,
                    "generate_send_id": this.activeStrucuredForms.sent_id
                }
            } else {
                if (this.activeStrucuredForms.facing_new == 2) {
                    formData = {
                        "userid": this.activeStrucuredForms.from_id,
                        "tenantId": this.activeStrucuredForms.tenant_id,
                        "recipient_id": this.activeStrucuredForms.userid,
                        "formId": this.activeStrucuredForms.form_id,
                        "staffFacing": null,
                        "form_submission_id": this.activeStrucuredForms.form_submission_id,
                        "form_name": this.activeStrucuredForms.form_name,
                        "id": "",
                        "regenerate":"true",
                        "regenerateUser":this.userData.userId,
                        "associate_patient_id": this.activeStrucuredForms.patient_associated_id,
                        "loggedinUserId": this.userData.userId,
                        "patient_user": this.activeStrucuredForms.associated_user_id,
                        "generate_send_id": this.activeStrucuredForms.sent_id
                    }
                } else {
                    formData = {
                        "userid": this.activeStrucuredForms.from_id,
                        "tenantId": this.activeStrucuredForms.tenant_id,
                        "recipient_id": this.activeStrucuredForms.userid,
                        "formId": this.activeStrucuredForms.form_id,
                        "staffFacing": null,
                        "regenerate":"true",
                        "regenerateUser":this.userData.userId,
                        "form_submission_id": this.activeStrucuredForms.form_submission_id,
                        "patient_user": this.activeStrucuredForms.from_id,
                        "generate_send_id": this.activeStrucuredForms.sent_id
                    }
                }
            }

            this._formsService.filingCenterSubmittedForm(formData).then((data: any) => {
                if (data.filename) {
                    this._structureService.notifyMessage({
                        messge: 'Files for ' + this.activeStrucuredForms.form_name + ' generated successfully',
                        delay: 1000,
                        type: 'success'
                    });
                    $('[id*="move_fc"]').unbind('click', false);
                }
            });
        }
    });
}
 /** 
    Send the form to EHR system and 
    generate files
  */
  moveToFC(e) {
    var IsEnterClicked=false;
    $(document).keypress((event)=> {
        if (event.keyCode == 13) {
            console.log("Enter pressed");
            IsEnterClicked=true;           
        }
    }); 
    var rowDetails = e.rowData;
    var patientName;
    var patientID;
    if (rowDetails['caregiver_userid'] != null) {
        patientName = rowDetails['caregiver_displayname'];
        patientID = rowDetails['caregiver_userid'];
    } else {
        patientName = rowDetails['patientName'];
        patientID = rowDetails['from_id'];
    }
    this.activeStrucuredForms = e.rowData;
    if(this.userDataConfig.enable_sftp_integration == false) {
        const checkIntegrationInFromWorklist: any = { "form_id": this.activeStrucuredForms.form_id, "tenant_id": this.userData.tenantId, "patient_id": patientID, "staff_id": this.userData.userId,
          "admissionId": this._structureService.isMultiAdmissionsEnabled? this.activeStrucuredForms.admission_id: undefined,
          "action": IntegrationType.SEND_TO_EHR
         };
         
        this._formsService.checkIntegration(checkIntegrationInFromWorklist).subscribe((data) => {
          if (data.success) {
            setTimeout(() => {
              this.sendToCpr();
            }, 300);
          }
        });      
    } else{
        this.sendToCpr();
    }
}
  removeScopeFormOnArchive(rowData) {
    // const filterListReplace = this.rowData.filter(x => Number(x.id) !== Number(rowData.id));
    if (rowData.formStatus == "Completed") {
      this.optionShowButton = "Completed";
    }
    else if (rowData.formStatus == "Pending") {
      this.optionShowButton = "Pending";
    }
    else {
      this.optionShowButton = "All";
    }
    if (this.rowModelType == 'serverSide') {
      this.rowData.find(x => Number(x.rowNumber) == Number(rowData.rowNumber)).formStatus = 'Archived';
      this.rowData.find(x => Number(x.rowNumber) == Number(rowData.rowNumber)).is_deleted = '1';
      this.gridApi.setRowData(this.rowData);
      this.setDefaultWidgetConfig(this.rowData);
      const index = this._structureService.activePatientActivityDetails.findIndex(x => x.tabId == this.worklistid);
      if (index != -1) {
        this._structureService.activePatientActivityDetails[index].rowData = this.rowData;
        this._structureService.activePatientActivityDetails[index].dashboardWidgets = this.dashboardWidgets;
      }
    } else {
      let index = this.rowData.findIndex(x => x == rowData);
      this.rowData[index].formStatus = 'Archived';
      this.rowData[index].is_deleted = '1';
      this.gridApi.refreshCells({ force: true });
      // var datasourcee = ServerSideDatasourceApi(this, '');
      // this.gridApi.setServerSideDatasource(datasourcee);
    }
  }
  getPdfTaggedForm(e) {
    this.activeStrucuredForms = e.rowData;
    var patientIdForPdf = this.activeStrucuredForms.patient_id;
    if(typeof this.activeStrucuredForms.associated_user_id !== 'undefined' && this.activeStrucuredForms.associated_user_id !='')
    {
          patientIdForPdf = this.activeStrucuredForms.associated_user_id;
    }
    if(this.activeStrucuredForms.facing_new ==0 && this.activeStrucuredForms.patient_id ==0)
    {
          patientIdForPdf = this.activeStrucuredForms.from_id;
    }
    var newWindow: any = window.open(this._structureService.serverBaseUrl + "/webapp/www/img/gif-loader.gif");
    var downloadTime = moment((moment().unix()) * 1000).format('MMMM DD, YYYY h:mm a');
    if (this.activeStrucuredForms.updatedtimestamp == null) {
      var data = { "patient_id": this.userPatientId, "patient_user": patientIdForPdf,"generate_send_id": this.activeStrucuredForms.sent_id, "userid": this.userData.userId, "formId": this.activeStrucuredForms.form_id, "submissionId": this.activeStrucuredForms.form_submission_id, "tenantId": this.userData.tenantId, "tenantName": this.userData.tenantName, "createdOn": moment(this.activeStrucuredForms.senton * 1000).format('MMMM DD, YYYY h:mm a'), "sendOn": moment(this.activeStrucuredForms.sentDate * 1000).format('MMMM DD, YYYY h:mm a'), "downloadTime": downloadTime, "createdOnNew": this.activeStrucuredForms.senton };//this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton) * 1000)
    }
    else {
      var data = { "patient_id": this.userPatientId, "patient_user": patientIdForPdf,"generate_send_id": this.activeStrucuredForms.sent_id, "userid": this.userData.userId, "formId": this.activeStrucuredForms.form_id, "submissionId": this.activeStrucuredForms.form_submission_id, "tenantId": this.userData.tenantId, "tenantName": this.userData.tenantName, "createdOn": moment(this.activeStrucuredForms.senton * 1000).format('MMMM DD, YYYY h:mm a'), "sendOn": moment(this.activeStrucuredForms.updatedtimestamp * 1000).format('MMMM DD, YYYY h:mm a'), "downloadTime": downloadTime, "createdOnNew": this.activeStrucuredForms.senton };//this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton) * 1000)
    }
    this._formsService.generateTaggedFormReportPdf(data, 2, timezone.name()).then((result) => {
      var fileName: any = '';
      fileName = result;
      //var fileUrl = this._structureService.apiBaseUrl + '/writable/filetransfer/uploads/tagged-form-reports/' + fileName._body + '.pdf';
      var fileUrl = this._structureService.apiBaseUrl + '/citus-health/v4/form-download.php?filetoken=' + fileName._body;

      newWindow.location = fileUrl;
      //this.download(fileUrl,fileName);
    }).catch((ex) => {
      //this.loginFailed = true;
    });
  }
  reviewStatusChange(e) {
    if (e.rowData) {
      NProgress.start();
      this.activeStrucuredForms = e.rowData;
      var activeDashboardWidget = e.rowData.formStatus.toLowerCase();
      var new_status = e.rowData.Isreviewed == 1 ? 2 : 1;
      var formName = e.rowData.form_name;
      var statusChangedTime = new Date();
      var form_id = e.rowData.form_id;
      var submission_id = e.rowData.form_submission_id;
      var formData = { form_id: form_id, submission_id: submission_id, status: new_status, user_id: this.userData.userId }
      this._formsService.formReviewStatusChange(formData).then((data) => {
        var messge = '';
        if (new_status == 1) messge = "Form " + formName + "  has been marked as reviewed";
        else messge = "Review is canceled from the Form " + formName;
        let activity = new_status == 1 ? "reviewed" : "reviewed canceled";
        var activityData = {
          activityName: "Form marked as " + activity,
          activityType: "forms",
          activityDescription: messge + " by " + this.userData.displayName + " on " + statusChangedTime + "(form id:" + form_id + ",form submission id:" + submission_id + ")"
        };
        this._structureService.trackActivity(activityData);
        // NProgress.done();
        // this._structureService.notifyMessage({
        //   messge: messge,
        //   delay: 1000,
        //   type: 'success'
        // });

        // let widgetIndex = 0;
        // if(this.dashboardWidgets.length > 0 && this.dashboardWidgets[widgetIndex].widgetValue != 'All' && activeDashboardWidget){
        // this.dashboardWidgets.forEach((item, index)=>{ 
        //    if(item.widgetValue && item.widgetValue.toLowerCase() ==activeDashboardWidget) widgetIndex = index;
        // });
        // this.updateColumnData(this.dashboardWidgets[widgetIndex].widgetValue,this.dashboardWidgets[widgetIndex].formField,this.dashboardWidgets[widgetIndex].hideColumns,true, this.dashboardWidgets[widgetIndex].count);
        // }

        if (activeDashboardWidget == 'completed' || activeDashboardWidget == 'archived') {

          if (this.rowModelType == 'serverSide') {
            var datasourcee = ServerSideDatasourceApi(this, '');
            this.gridApi.setServerSideDatasource(datasourcee);
            this.gridApi.deselectAll();
          } else {
            this.getformData();
            this.removeScopeFormOnArchive(this.activeStrucuredForms);
          }
          NProgress.done();
          this._structureService.notifyMessage({
            messge: messge,
            delay: 1000,
            type: 'success'
          });
        }



      });

    }
  }
  viewStrucuturedForm(e) {
    this.activeStrucuredForms = e.rowData;
    this._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)), 1);
    localStorage.setItem('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)));
    this.storeService.storeData(Store.SELECTED_FORM_TAB, this.selectedWidget);
    this.router.navigate(['/forms/list/view']);
  }
  convertDateFormat(params) {
    if (!params.value) {
      return '';
    }
    if (params.context == 'form') {
      return params.value;
    } else {
      if (params.value && params.value != '' && params.value.toString().length != 10) {
        let newDate = new Date(params.value).getTime();
        return moment.utc(newDate).local().format('lll');
      } else if (params.value && params.value != '' && params.value.toString().length == 10) {
        let newDate = new Date(Number(params.value) * 1000);
        return moment.utc(newDate).local().format('lll');
        //var time = moment.unix(Number(params.value)).format("MM/DD/YYYY");
      } else {
        return '';
      }
    }

  }
  convertTimeFormat(params) {
    if (!params.value) {
      return '';
    }
    if (params.value || params.value != '') {
      return moment(params.value, ['HH:mm']).format('h:mm A');
    }
  }
  dateComparator(date1, date2) {
    date1 = moment.unix(Number(date1)).format("DD-MM-YYYY");
    date2 = moment.unix(Number(date2)).format("DD-MM-YYYY");
    let date1Number: number;
    if (date1 === undefined || date1 === null || date1.length !== 10) {
      date1Number = null;
    }
    var result = date1.substring(6, 10) * 10000 + date1.substring(3, 5) * 100 + date1.substring(0, 2);
    date1Number = result;
    let date2Number: number;
    if (date2 === undefined || date2 === null || date2.length !== 10) {
      date2Number = null;
    }
    var result = date2.substring(6, 10) * 10000 + date2.substring(3, 5) * 100 + date2.substring(0, 2);
    date2Number = result;
    if (date1Number === null && date2Number === null) {
      return 0;
    }
    if (date1Number === null) {
      return -1;
    }
    if (date2Number === null) {
      return 1;
    }
    return date1Number - date2Number;
  }
  multipleButtonAction(e) {
    if (e.action == 'editEntry') {
      // this.editEntry(e);
    } else if (e.action == 'deleteEntry') {
      // this.deleteEntry(e);
    } else if (e.action == 'refillEntry') {
      // this.refillEntry(e);
    } else if (e.action == 'assignStaff') {
      // this.assignStaff(e);
    } else if (e.action == 'newSchedule') {
      // this.newSchedule(e);
    } else if (e.action == 'assignNurse') {
      // this.assignNurse(e);
    }
  }
  setDropdownList(list, field, gridType) {
    /**List staff name while edit cell */
    this.columnDefs.forEach(element => {
      if (element.field == field) {
        element.cellEditorParams.values = list;
      } else {
        if (element.children && element.children.length > 0) {
          let index = element.children.findIndex(x => x.field == field);
          if (index != -1) {
            element.children[index].cellEditorParams.values = list;
          }
        }
      }
    });
  }
  searchFilterData(params) {
    this.gridApi.hideOverlay();
    const filterModel = this.gridApi.getFilterModel();
    this.gridApi.setFilterModel(null);
    if (this.selectedWidget != 'All') {
      if (filterModel.hasOwnProperty(this.selectedWidgetField) == true) {
        const statusFilterComponent = this.gridApi.getFilterInstance(this.selectedWidgetField);
        statusFilterComponent.setModel({
          type: 'equals',
          filter: filterModel[this.selectedWidgetField].filter
        });
      }
    }
    if (params == '') {
      this.searchFieldText = '';
      this.resetAppliedFilterNotification([], true);
    }
    this.gridApi.setQuickFilter(params);
    if (this.gridApi.getDisplayedRowCount() == 0) {
      this.suppressNoRow = false;
      this.gridApi.suppressNoRowsOverlay = false;
      this.gridApi.showNoRowsOverlay();
    }
    if(this.metaData.searchState && this.metaData.stateSavingMode == 'autoSave') {
      this.saveStatePrefrence();
    }
  }
  prepareHeaderVariables() {
		let headers = {};
		if (this.metaData.enableAuthorization === true) {
			if (this.metaData.authtype === "token") {
				headers = {
					authorizationtoken: this._structureService.getCookie('authID')
				}
				// if (this.metaData.authsource === "callback") { 
				// 	headers = this[`${this.metaData.authCallbackFunction}`]();

				// }
			}
		}
		return headers;
  }
  prepareApiData(data) {
		let rowData: any = data;
		if (this.metaData.graphqlApi) {
      let keyArray :any;
      if (this.metaData.parameters.includes('getSessionTenant')) {
      keyArray = Object.keys(data['data']['getSessionTenant']);
      rowData = JSON.parse(JSON.stringify(data['data']['getSessionTenant'][keyArray[0]].data)); 
      console.log(rowData);
      this.widgetData = data['data']['getSessionTenant'][keyArray[0]].filterCount ? data['data'][keyArray[0]].filterCount : [];
    } else {
      keyArray = Object.keys(data['data']);
      rowData = JSON.parse(JSON.stringify(data['data'][keyArray[0]].data));
      this.widgetData = data['data'][keyArray[0]].filterCount ? data['data'][keyArray[0]].filterCount : [];
    }
      }
		if (this.metaData.worklistType == 'single') {
			this.rowData = rowData;
      //this.showLoading = false;
		} else {
			rowData.forEach(element => {
				if (element.formData) {
					let formDataList = [];
					let objKeys = Object.keys(element.formData);
					objKeys.forEach(keys => {
						if (element.formData[keys]) {
							let formObj = {};
							let i = 1;
							let obj = {};
							element.formData[keys].forEach(elem => {
								let labelText = '';
								if (elem.label.lastIndexOf('{{') == -1) {
									labelText = elem.label;
									if (i == 1) {
										obj[elem.label] = { 'id': elem.element_id, 'elementType': elem.element_type };
									}
								} else {
									let key = elem.label.substring(elem.label.lastIndexOf('{{') + 2, elem.label.lastIndexOf('}}'));
									labelText = key;
									if (i == 1) {
										obj[key] = { 'id': elem.element_id, 'elementType': elem.element_type };
									}
								}
								if (elem.element_type == 'date' && elem.value && elem.value != null && elem.value != '') {
									let newDate = new Date(Number(elem.value) * 1000);
									formObj[labelText] = moment.utc(newDate).format('MM/DD/YYYY');
								} else {
									formObj[labelText] = '';
								}
							});
							this.machFormIds.push({ 'type': 'child' });
							this.machFormIds.push(obj);
							i++;
							formObj['submissionID'] = Number(keys);
							formDataList.push(formObj);
						}
					});
					element.callRecords = formDataList;
				} else {
					element.callRecords = element.day_wise_time_range;
				}
			});
			this.rowData = rowData;
      //this.showLoading = false;
		}
		this.setDefaultWidgetConfig(this.rowData);
    if (this.rowData) {
      this.setDefaultGrid();
    }
  }
  loadWorklistData() {
    if (this.rowModelType === 'serverSide') {
      if (this.metaData.dataSource && this.metaData.dataSource === 'API') {
        let datasourcee = ServerSideDatasourceApi(this, '');
        this.gridApi.setServerSideDatasource(datasourcee);
      } else {
        const datasource = ServerSideDatasource(this, true, true, this._workListService);
        if (this.gridApi) this.gridApi.setServerSideDatasource(datasource);
      }
    } else {
      this.getformData();
    }
  }
  getformData(setState = false) {
    if (this.metaData.dataSource && this.metaData.dataSource == 'API') {
      if (this.rowModelType == 'serverSide') {
        let widgetIndex = 0;
        if (!isBlank(this.storeService.getStoredData(Store.SELECTED_FORM_TAB))) {
          widgetIndex = this.dashboardWidgets.findIndex(widget => widget.widgetValue === this.storeService.getStoredData(Store.SELECTED_FORM_TAB));
          this.storeService.removeData(Store.SELECTED_FORM_TAB);
        }
        if (this.dashboardWidgets.length > 0 && this.dashboardWidgets[widgetIndex].widgetValue != 'All' && !this.customSearch && !setState) {
          this.optionShowButton = this.dashboardWidgets[widgetIndex].widgetValue;
          this.updateColumnData(this.dashboardWidgets[widgetIndex].widgetValue, this.dashboardWidgets[widgetIndex].formField, this.dashboardWidgets[widgetIndex].hideColumns, true, this.dashboardWidgets[widgetIndex].count,this.dashboardWidgets[widgetIndex].id);
        } else {
          var datasourcee = ServerSideDatasourceApi(this, '');
          this.gridApi.setServerSideDatasource(datasourcee);
        }
        //this.showLoading = false;
        if (this.rowData && this.rowData.length == 0) {
          this.suppressNoRow = false;
          this.showNoData = true;
        }
      } else {
        this.gridApi.showLoadingOverlay();
        this.parameters = [];
        this.metaData.parameters.split('&').forEach(element => {
          let key = element.substring(
            element.lastIndexOf('{{') + 2,
            element.lastIndexOf('}}')
          );
          if (key == 'userId') {
            element = element.replace('{{' + key + '}}', this.userId);
          }
          if (key == 'logginUserId') {
            element = element.replace('{{' + key + '}}', this.userData.userId);
          }
          if (key == 'tenantId') {
            element = element.replace('{{' + key + '}}', this._structureService.getCookie('tenantId'));
          }
          if (key == 'crossTenantId') {
            element = element.replace('{{' + key + '}}', this._structureService.getCookie("crossTenantId"));
          }
          if (key == 'roleid') {
            element = element.replace('{{' + key + '}}', this.userData.roleId);
          }
          if (key == 'zone') {
            element = element.replace('{{' + key + '}}', timezone.name());
          }
          if (key == 'isForms') {
            element = element.replace('{{' + key + '}}', true);
          }
          if (key == 'isPrivilege') {
            element = element.replace('{{' + key + '}}', true);
          }
          if (key == 'searchText') {
            element = element.replace('{{' + key + '}}', null);
          }
          if (key == 'pah_patient_id') {
            element = element.replace('{{' + key + '}}', this.userPatientId);
          }
          if (key == 'limit') {
            element = element.replace('{{' + key + '}}', 0);
          }
          if (key == 'offset') {
            element = element.replace('{{' + key + '}}', 0);
          }
          this.parameters.push(element);
        });
        this.disableWidget = true;
        let graphqlQuery = '';
        let url = this.metaData.endpoint;
        if (this.metaData.graphqlApi) {
          let url = this.metaData.graphqlEndpoint;
          let fieldList = this.metaData.fieldList.split(',');
          let fieldString = '';
          fieldList.forEach(field => {
            if (field.includes('.')) {
              let colArray = field.split('.');
              let endString = '';
              colArray.forEach((element, index) => {
                fieldString = ` ${fieldString} ${element} `;
                if (index !== colArray.length - 1) {
                  fieldString = ` ${fieldString} { `;
                  endString = ` ${endString} } `;
                }

              });
              fieldString = ` ${fieldString} ${endString} `;
            } else {
              fieldString = `${fieldString} ${field}`;
            }
          });
          let newQuery = this.metaData.parameters.replace('$fields', fieldString);
          let variables = {};

          if (this.metaData.parameters.includes('patientDocuments')) {
            variables = {
              patientId: Number(this.userPatientId),
              // id:68972,
              // sessionToken:"AQIC5wM2LY4SfcwZQF1lXaJtcbbF3SgHW24F_s5XfzBcqKE.*AAJTSQACMDEAAlNLABItMTM0NDM3NzMzOTgwMTMxNTkAAlMxAAA.*"};
              sessionToken: this._structureService.getCookie('authID'),
              // startRow:start,
              // endRow:end,
              // filter:filterModelArray,
              sorting: [],
            };
          }
          console.log(variables);
          this._pahService.getWorklistDataUsingGraphQLAPI(url,
            newQuery, variables, this.prepareHeaderVariables()).then((data) => {
              this.prepareApiData(data);
            });
        } else {
          this._workListService.getWorklistDataUsingAPI(this.metaData.endpoint,
            this.parameters.join('&')).then((data) => {
              this.rowData = data['response'];
              this.disableWidget = false;
              this.setDefaultWidgetConfig(this.rowData);
            
          if (this.rowData) {
            this.setDefaultGrid();
          }
          //if (this.gridApi) {
            //this.gridApi.hideOverlay();
          //}
          //this.showLoading = false;
          if (this.rowData && this.rowData.length == 0) {
            this.showNoData = true;
            this.overlayNoRowsTemplate = "<span>There are no "+ this.worklistName.toLowerCase()+" with this patient.</span>";
            this.suppressNoRow = false;
          } else {
            let widgetIndex = 0;
            if (this.dashboardWidgets.length > 0 && this.dashboardWidgets[widgetIndex].widgetValue != 'All') {
              this.optionShowButton = this.dashboardWidgets[widgetIndex].widgetValue;
              this.updateColumnData(this.dashboardWidgets[widgetIndex].widgetValue, this.dashboardWidgets[widgetIndex].formField, this.dashboardWidgets[widgetIndex].hideColumns, false, this.dashboardWidgets[widgetIndex].count);
            }
          }
        });
        }
      }
    } else {
      if (this.rowModelType == 'serverSide') {
        var datasource = ServerSideDatasource(this, true, true, this._workListService);
        if (this.gridApi)
          this.gridApi.setServerSideDatasource(datasource);
      } else {
        this.gridApi.showLoadingOverlay();
        this.disableWidget = true;
        this._workListService.getWorkListFormData(this.formId, this.uniqueClass, '', '', this.formFieldFrom).refetch().then(({ data: response }) => {
          if (this.uniqueClass !== '') {
            this.formRowData = response['getFormDataWithUniqueID'];
          } else {
            this.formRowData = response['getFormData'];
          }
          this.disableWidget = false;
          if (this.formRowData.length > 0) {
            this.formDataList = [];
            let i = 1;
            let obj = {};
            this.formRowData.forEach(element => {
              const formObj = {};
              element.elements.forEach(elem => {
                formObj[elem.labelText] = elem.value;
                if (elem.valueType == 'radio' && elem.valueOther != '') {
                  formObj[elem.labelText] = elem.valueOther;
                }
                if (elem.valueType == 'checkbox' && elem.valueOther != '') {
                  if (formObj[elem.labelText] == '') {
                    formObj[elem.labelText] = elem.valueOther;
                  } else {
                    formObj[elem.labelText] = formObj[elem.labelText] + ',' + elem.valueOther;
                  }
                }
                if (i == 1) {
                  obj[elem.labelText] = elem.tid;
                  this.machFormKeys.push(elem.labelText);
                }
                if (elem.valueType == 'date') {
                  if (elem.timestampForDate && elem.timestampForDate != null && elem.timestampForDate != '') {
                    let newDate = new Date(Number(elem.timestampForDate));
                    formObj[elem.labelText] = moment.utc(newDate).format('DD-MM-YYYY');
                  } else {
                    formObj[elem.labelText] = '';
                  }
                }
                formObj['keyname'] = elem.keyname;
              });
              formObj['submissionID'] = element.submissionID;
              formObj['slno'] = i;
              formObj['action'] = '';
              this.formDataList.push(formObj);
              i++;
            });
            this.machFormIds.push(obj);
            this.rowData = this.formDataList;
            this.gridApi.hideOverlay();
            this.setDefaultWidgetConfig(this.rowData);
          } else {
            this.rowData = [];
            this.suppressNoRow = false;
            this.gridApi.hideOverlay();
            this.setDefaultWidgetConfig(this.rowData);
          }
        });
      }
    }
  }
  setDefaultGrid() {
    const index = this._structureService.activePatientActivityDetails.findIndex(x => x.tabId == this.worklistid);
    if (index != -1) {
      this._structureService.activePatientActivityDetails[index].gridApi = this.gridApi;
      this._structureService.activePatientActivityDetails[index].sideBar = this.sideBar;
      this._structureService.activePatientActivityDetails[index].cellClickFn = this.cellClickFn;
      this._structureService.activePatientActivityDetails[index].rowGroupPanelShow = this.rowGroupPanelShow;
      // this._structureService.activePatientActivityDetails[index].maxBlocksInCache = this.maxBlocksInCache;
      // this._structureService.activePatientActivityDetails[index].cacheBlockSize = this.cacheBlockSize;
      this._structureService.activePatientActivityDetails[index].filterCheck = this.filterCheck;
      this._structureService.activePatientActivityDetails[index].selectName = this.selectName;
      this._structureService.activePatientActivityDetails[index].filterField = this.filterField;
      this._structureService.activePatientActivityDetails[index].singleRowActions = this.singleRowAllActions;
      // this._structureService.activePatientActivityDetails[index].pagination = this.pagination;
      // this._structureService.activePatientActivityDetails[index].paginationPageSize = this.paginationPageSize;
      this._structureService.activePatientActivityDetails[index].rowData = this.rowData;
      this._structureService.activePatientActivityDetails[index].patientId = this.userPatientId;
    }
  }
  getDefaultGrid() {
    const index = this._structureService.activePatientActivityDetails.findIndex(x => x.tabId == this.dynamicData.pahworklistId);
    if (index != -1) {
      this.gridApi = this._structureService.activePatientActivityDetails[index].gridApi;
      this.gridApi.showLoadingOverlay();
      this.dashboardWidgets = this._structureService.activePatientActivityDetails[index].dashboardWidgets;
      this.cellClickFn = this._structureService.activePatientActivityDetails[index].cellClickFn;
      this.rowGroupPanelShow = this._structureService.activePatientActivityDetails[index].rowGroupPanelShow;
      this.sideBar = this._structureService.activePatientActivityDetails[index].sideBar;
      this.filterCheck = this._structureService.activePatientActivityDetails[index].filterCheck;
      this.selectName = this._structureService.activePatientActivityDetails[index].selectName;
      this.filterField = this._structureService.activePatientActivityDetails[index].filterField;
      this.singleRowActions = this._structureService.activePatientActivityDetails[index].singleRowActions;
      this.singleRowAllActions = this._structureService.activePatientActivityDetails[index].singleRowActions;
      this.columnDefs = this._structureService.activePatientActivityDetails[index].columnDefs;
      this.reportFields = this._structureService.activePatientActivityDetails[index].reportFields;
      this.metaData = this._structureService.activePatientActivityDetails[index].metaData;
      // this.maxBlocksInCache = this._structureService.activePatientActivityDetails[index].maxBlocksInCache;
      // this.cacheBlockSize = this._structureService.activePatientActivityDetails[index].cacheBlockSize;
      // this.pagination = this._structureService.activePatientActivityDetails[index].pagination;
      // this.paginationPageSize = this._structureService.activePatientActivityDetails[index].paginationPageSize;
      this.userPatientId = this._structureService.activePatientActivityDetails[index].patientId;
      if(this.dynamicData.reloadTab == true) {
        this.getformData();
      } else {
        //this.rowData = this._structureService.activePatientActivityDetails[index].rowData;
        //this.showLoading = false;
      }
      this.setDefaultWidgetConfig(this.rowData);
      this.disableWidget = false;
    }
  }
  setDefaultWidgetConfigApi() {
    this.dashboardWidgets.forEach(element => {
      let widgetValue = element.widgetValue.replace(/\s/g, "");
      if (this.totalRowData[widgetValue]) {
        element.count = this.totalRowData[widgetValue];
      } else {
        element.count = 0;
      }
    });
  }
  setDefaultWidgetConfig(rowData) {
    this.dashboardWidgets.forEach(element => {
      if (element.displayText == 'Auto Approved') {
        element.displayText = 'Auto Approved';
      } else {
        element.displayText = element.displayText;
      }
      element.count = this.getFilterCount(element.widgetValue, element.formField, rowData);
    });
  }
  getFilterCount(filterText, filterField, rowData) {
    if (filterText === 'All') {
      return rowData.length;
    } else {
      const filteredData = rowData.filter(x => x[filterField] === filterText);
      return filteredData.length;
    }
  }
  /**Set the count of filering result in dashboard widget in server side pagination */
  setDefaultWidgetConfigServerSide(fieldDetails, action) {
    let i = 0;
    this.dashboardWidgetField = '';
    this.dashboardWidgetFieldId = '';
    let customWidgetProperty;
    let allWidgetIndex = 0;
    this.dashboardWidgets.forEach(element => {
      if (element.widgetValue.toLowerCase() === 'all' && element.customWidgetCount) {
        customWidgetProperty = true;
        allWidgetIndex = i;
      }
      if (i == 0) {
        this.dashboardWidgetField = element.formField;
        let elementDetails = this.getElementIdType(this.dashboardWidgetField);
        this.dashboardWidgetFieldId = elementDetails.id;
      }
      i++;
      if (this.widgetCounts[0].hasOwnProperty(element.widgetValue.toLowerCase().trim())) {
        element.count = this.widgetCounts[0][element.widgetValue.toLowerCase().trim()];
      } else {
        element.count = 0;
      }
    });
    if (action == 'edit' && fieldDetails != '' && this.dashboardWidgetField == fieldDetails.field && fieldDetails.oldValue != fieldDetails.newValue) {
      let newIndex = this.dashboardWidgets.findIndex(x => x.widgetValue.toLowerCase().trim() == fieldDetails.newValue.toLowerCase().trim());
      if (newIndex != -1) {
        this.dashboardWidgets[newIndex].count = this.dashboardWidgets[newIndex].count + 1;
        this.widgetCounts[0][fieldDetails.newValue.toLowerCase()] = this.dashboardWidgets[newIndex].count;
      }
      let oldIndex = this.dashboardWidgets.findIndex(x => x.widgetValue.toLowerCase().trim() == fieldDetails.oldValue.toLowerCase().trim());
      if (oldIndex != -1) {
        let changeCount = this.dashboardWidgets[oldIndex].count - 1;
        if (changeCount >= 0) {
          this.dashboardWidgets[oldIndex].count = changeCount;
        } else {
          this.dashboardWidgets[oldIndex].count = 0;
        }
        this.widgetCounts[0][fieldDetails.oldValue.toLowerCase()] = this.dashboardWidgets[oldIndex].count;
      }
      let index = this.dashboardWidgets.findIndex(x => x.widgetValue === this.selectedWidget);
      if (index !== -1 && (this.selectedWidget !== 'All' || (this.selectedWidget === 'All' && customWidgetProperty))) {
        this.updateColumnData(this.dashboardWidgets[index].widgetValue, this.dashboardWidgets[index].formField, this.dashboardWidgets[index].hideColumns, false, this.dashboardWidgets[index].count, this.selectedWidgetId);
      }
    }
    if (action == 'delete') {
      const oldCount = this.widgetCounts[0][fieldDetails.field.toLowerCase()];
      const allCount = this.widgetCounts[0]['all'];
      let newIndex = this.dashboardWidgets.findIndex(x => x.widgetValue == fieldDetails.field);
      if (newIndex != -1) {
        this.dashboardWidgets[newIndex].count = this.dashboardWidgets[newIndex].count - 1;
      }
      let oldIndex = this.dashboardWidgets.findIndex(x => x.widgetValue == 'All');
      if (oldIndex != -1) {
        this.dashboardWidgets[oldIndex].count = this.dashboardWidgets[oldIndex].count - 1;
      }
      this.widgetCounts[0][fieldDetails.field.toLowerCase()] = oldCount - 1;
      this.widgetCounts[0]['all'] = allCount - 1;
    }
    if (customWidgetProperty) {
      const allWidgetDetails = this.dashboardWidgets[allWidgetIndex];
      const selectWidgetData = allWidgetDetails.selectWidget.split(",");
      let countAll = 0;
      selectWidgetData.forEach(res => {
        const index = this.dashboardWidgets.findIndex(x => x.widgetValue === res);
        countAll = countAll + this.dashboardWidgets[index].count;
      })
      this.dashboardWidgets[allWidgetIndex].count = countAll;
    }
  }
  /**custom filtering function of dashboard widget */
  updateColumnData(filterText, filterField, columns, check, count, widgetId?: number) {
    this.selectedWidgetId = !isBlank(widgetId) && this.metaData.widgetState? widgetId : 0;
    /**show columns according to widget selection */
    this.selectedWidgetField = filterField;
    this.selectedWidget = filterText;
    var activityData = {
      activityName: "Tab Navigation:",
      activityType: "Tab access",
      activityDescription: "Current Page PAH -. Tab "+this.dynamicData.tabName+" - : "+filterText,
    };
    this._structureService.trackActivity(activityData);
    if (this.previousHideColumns.length > 0) {
      this.previousHideColumns.forEach(element => {
        this.gridColumnApi.setColumnVisible(element, true);
      });
    }
    this.hideColumns = (columns && columns != null) ? columns.split(',') : [];
    if (this.hideColumns.length > 0) {
      this.hideColumns.forEach(element => {
        this.gridColumnApi.setColumnVisible(element, false);
      });
      this.previousHideColumns = this.hideColumns;
    } else {
      this.previousHideColumns = [];
    }
    /***/
    this.filterText = '';
    this.gridApi.deselectAll();
    this.hideActionButtons = true;
    this.optionShowButton = filterText;
    if (filterText == 'All') {
      filterText = '';
    }
    if(this.metaData.widgetState) {
      this.applyWidgetLevelFilter();
    }
    
    if (filterField !== '') {
      let filterModel = this.gridApi.getFilterModel();
      const statusFilterComponent = this.gridApi.getFilterInstance(filterField);
      if (this.metaData.rowModel == 'server') {
        filterModel[filterField] = { type: 'equals', filter: filterText, filterType: 'text' };
        this.filterText = filterText;
        if (check == true) {
          /**In forms worklist list entries with status completed initially */
          this.gridApi.setFilterModel(filterModel);
          var datasourcee = ServerSideDatasourceApi(this, '');
          this.gridApi.setServerSideDatasource(datasourcee);
        } else {
          this.gridApi.setFilterModel(filterModel);
        }
      } else {
        this.gridApi.hideOverlay();
        if (filterText != '') {
          statusFilterComponent.setModel({
            type: 'equals',
            filter: filterText
          });
          this.gridApi.onFilterChanged();
          if (count == 0) {
            this.gridApi.showNoRowsOverlay();
          }
          /**Go to first page after applying filtering */
          this.gridApi.paginationGoToFirstPage();
        } else {
          this.gridApi.setFilterModel(null);
          this.gridApi.onFilterChanged();
        }
      }
      if (this.metaData.filterState && filterText !== '' && this.metaData.stateSavingMode === 'autoSave') {
        this.saveStatePrefrence();
      }
    } else {
      this.gridApi.setFilterModel(null);
    }
  }
  private applyWidgetLevelFilter() {
    if (
      this._sharedService.worklistState.some(
        (widget) => +widget.widgetId === +this.selectedWidgetId 
        && +widget.worklistId === +this.worklistid && widget.worklistSource === 'PAH'
      )
    ) {
      const worklistStateData = this._sharedService.worklistState.find(
        (widget) => +widget.widgetId === +this.selectedWidgetId 
        && +widget.worklistId === +this.worklistid && widget.worklistSource === 'PAH'
      );
      this.setStatePreferencetoGrid(worklistStateData.worklistState, 'clear', this.worklistid);
      this.userPreferenceId = this._sharedService.userWorklistPrefrenceIds.find(
        (worklist) =>
          +worklist.worklistId === +this.worklistid &&
          +worklist.widgetId === +this.selectedWidgetId
      );
    } else {
      let worklistState = {
        columnState: this.defaultColumnState,
        filterState: this.defaultFilterState,
        sortState: this.defaultSortState,
        searchState: {
          selectedSearchFieldText: '',
          selectedSearchFields: this.defaultSelectedSearchFields,
        },
      };
      this.setStatePreferencetoGrid(worklistState, 'clear', this.worklistid);
    }
  }
  resetAppliedFilterNotification(filter, reset = false) {
    const showNotify = !isBlank(filter) && filter.some(item => 
       (item.column && item.column !== this.selectedWidgetField) || 
       (item.fieldName && item.fieldName !== this.selectedWidgetField)
    );
    if(!showNotify || reset) {
      this._structureService.notifySearchFilterApplied(false);
    }
  }
  updateColumnDataFilter(filterText, filterLabel) {
    this.filterLabel = filterLabel;
    let filterModel = this.gridApi.getFilterModel();
    let filterType;
    let filterValue = this.displayname;
    if (filterText == "others") {
      filterType = 'notEqual';
    } else if (filterText == "all") {
      filterValue = '';
    } else {
      filterType = 'equals';
    }
    const statusFilterComponent = this.gridApi.getFilterInstance(this.filterField);
    if (this.metaData.rowModel == 'server') {
      filterModel[this.filterField] = { type: filterType, filter: filterValue, filterType: 'text' };
      this.gridApi.setFilterModel(filterModel);
      // this.gridApi.onFilterChanged();
    } else {
      this.gridApi.setFilterModel(null);
      // this.gridApi.onFilterChanged();
      statusFilterComponent.setModel({
        type: filterType,
        filter: filterValue
      });
      this.gridApi.onFilterChanged();
    }
    this.gridApi.deselectAll();
  }
  sendFormAction(e) {
    let sendFormData = e;
    this._structureService.activePatientActivityHub = true;
    let patient;
    if (this._structureService.activePatientActivityHubPatientId) {
      patient = this._structureService.activePatientActivityHubPatientId;
    } else {
      patient = this.userPatientId;
    }
    this.router.navigate(['forms/send/list'], { queryParams: { patient: patient } });
  }
  closeChatModel() {
    $('#chat-modal').modal('toggle');
    $('#chat-modal').removeClass('fade');
    let self = this;
    var activityData = {
      activityName: "Close Chat Session",
      activityType: "messaging",
      activityDescription: " Close Chat Session - "+ this.userData.displayName+"("+this.initiatorFromLogs+") in Chatroom "+this.chatDetails["id"]+".",
    };
    this._structureService.trackActivity(activityData);
    setTimeout(function () {
      self.showChat = false;
    }, 500);
  }
  closeEditModel() {
    $('#edit-modal').modal('toggle');
    $('#edit-modal').removeClass('fade');
    var datasourcee = ServerSideDatasourceApi(this, '');
    this.gridApi.setServerSideDatasource(datasourcee);
    this.gridApi.deselectAll();
    let self = this;
    setTimeout(function () {
      self.showEdit = false;
      localStorage.removeItem('formData');
      self._structureService.deleteCookie('formData');
    }, 500);
    this._structureService.pahEdit = false;
  }
  exportCSV() {
    let self = this;
    let columnkey = [];
    this.reportFields.forEach(element => {
      if ((element.includeInExport == null || element.includeInExport == true) && this.hideColumns.indexOf(element.fieldName) == -1 && element.visibility == true) {
        columnkey.push(element.fieldName);
      }
    });
    let params: any = {
      columnKeys: columnkey,
      allColumns: false,
      fileName: self.heading,
      sheetName: self.heading
    };
    params.processCellCallback = function (params) {
      let index = self.reportFields.findIndex(x => x.fieldName == params.column.colDef.field);
      if (index != -1 && self.reportFields[index].valueType == 'date') {
        return self.convertDateFormat(params);
      } else {
        return params.value;
      }
    }
    this.gridApi.exportDataAsCsv(params);
  }
  exportExcel(mode) {
    let self = this;
    let columnkey = [];
    this.reportFields.forEach(element => {
      if ((element.includeInExport == null || element.includeInExport == true) && this.hideColumns.indexOf(element.fieldName) == -1 && element.visibility == true) {
        columnkey.push(element.fieldName);
      }
    });
    let params: any = {
      columnKeys: columnkey,
      allColumns: false,
      fileName: self.heading,
      exportMode: mode,
      sheetName: self.heading
    };
    params.processHeaderCallback = function (params) {
      return params.column.getColDef().headerName.toUpperCase();
    };
    params.processCellCallback = function (params) {
      let index = self.reportFields.findIndex(x => x.fieldName == params.column.colDef.field);
      if (index != -1 && self.reportFields[index].valueType == 'date') {
        return self.convertDateFormat(params);
      } else {
        return params.value;
      }
    }
    this.gridApi.exportDataAsExcel(params);
  }
  /**search function in server side pagination */
  searchBasedField(searchText) {
    if (this.searchFieldText !== '') {
      localStorage.setItem('selectedSearchFieldText', this.searchFieldText);
      if(this.metaData.searchState && this.metaData.stateSavingMode === 'autoSave') {
        this.saveStatePrefrence();
      }
    }
    if (this.selectedSearchFields.length > 0) {
      this.customSearch = true;
      this.getformData();
    }
  }
  clearSearch() {
    if (this.searchFieldText != '') {
      this.searchFieldText = '';
      this.customSearch = false;
      this.getformData(true);
    }
    if (this.metaData.searchState && this.metaData.stateSavingMode === 'autoSave') {
      this.saveStatePrefrence();
    }
  }
  checkboxSelection(field) {
    if (this.selectedSearchFields.findIndex(x => x.fieldId == field.fieldId) == -1) {
      this.selectedSearchFields.push(field);
    } else {
      this.selectedSearchFields = this.selectedSearchFields.filter(x => x.fieldId != field.fieldId);
    }
  }
  onChangeSearchField(event, field) {

  }
  /** inline cell editing and update the corresponding value */
  onCellValueChanged(e) {
    if (e.column.colId != 'submissionID') {
      const field = e.colDef.field;
      const configIndex = this.reportFields.findIndex(x => x.fieldName == field);
      const config = this.reportFields[configIndex];
      let elementDetails = this.getElementIdType(field);
      console.log('elementDetails', elementDetails);
      let dateValue;
      let isDataError = false;
      let fieldId = elementDetails.id;
      let newValue = e.newValue;
      if (elementDetails.type == 'date') {
        if (newValue == '') {
          isDataError = false;
        } else {
          let match = newValue.match(/^(\d{2}|\d{1})\/(\d{2}|\d{1})\/\d{4}$/);
          if (!match) {
            isDataError = true;
          } else if (match && match[0] !== newValue) {
            isDataError = true;
          } else {
            const cellDate = new Date(newValue);
            dateValue = cellDate instanceof Date && !isNaN(cellDate.valueOf());
            if (dateValue == true) {
              /*date format should be in yyyy-mm-dd for saving data to machform db */
              newValue = moment(cellDate).format('YYYY-MM-DD');
            } else {
              isDataError = true;
            }
          }
        }
      }
      let params = [];
      let fieldValueList = this.list[e.colDef.field];
      if (fieldValueList) {
        let count: number;
        count = fieldValueList.length - 1;
        if (fieldValueList[Number(count)].type == 'internal') {
          const index = fieldValueList.findIndex(x => x.optionValue == newValue);
          if (index != -1) {
            newValue = fieldValueList[index].optionId;
            if (elementDetails.type == 'checkbox') {
              params.push({ id: fieldId + '_' + newValue, value: '1' });
              fieldValueList.forEach(element => {
                if (element.optionValue && element.optionValue != e.newValue) {
                  params.push({ id: fieldId + '_' + element.optionId, value: '0' });
                }
              });
            }
            if (elementDetails.hasOtherOption == 1) {
              params.push({ id: fieldId + '_other', value: '' });
            }
          }
        }
      }
      let formId;
      if (this.machFormIds[0] && this.machFormIds[0].type == 'child') {
        formId = this.metaData.associatedForm;
      } else {
        formId = this.formId;
      }
      if (elementDetails.type != 'checkbox') {
        params.push({ id: fieldId, value: newValue });
      }
      if (e.newValue != e.oldValue) {
        let rowMode = 0;
        if (this.enableCellEditHistory == 'true') {
          rowMode = 1;
        }
        let selectedRow = this.gridApi.getSelectedRows();

        let i = 1;
        let selectRowData = selectedRow.map(x => x.submissionID);
        if (config.enableMultiSelectionEdit == false || selectRowData.length == 0) {
          selectRowData = [];
          selectRowData.push(e.data.submissionID);
        }

        let mySiteUserData = JSON.parse(this._structureService.userDetails);
        let mySiteId;
        let siteIds;
        const count = selectRowData.length;
        selectRowData.forEach(element => {
          if (config.prefillMethod == "sharedService" && config.callbackFunction == "getSites") {
            let siteId = mySiteUserData.mySites.filter((siteData) => {
              if (siteData.name == e.data.SiteId) { return siteData }

            });
            mySiteId = siteId[0].id;
            siteIds = [{ patientId: parseInt(e.data.PatientId), siteId: mySiteId }];
            params = [{ id: fieldId, value: mySiteId }];
          }
          if (isDataError == false) {
            this._workListService.updateSingleFormData(params, siteIds, formId, element, rowMode).subscribe(({ data: response }) => {
              if (response['updateFormData'][0].statusCode == 200) {
                if (this.dashboardWidgetField == e.colDef.field) {
                  if (this.rowModelType == 'clientSide') {
                    this.setDefaultWidgetConfig(this.rowData);
                  } else {
                    let obj = { 'field': e.colDef.field, 'newValue': e.newValue, 'oldValue': e.oldValue };
                    this.setDefaultWidgetConfigServerSide(obj, 'edit');
                  }
                }
                if(config.prefillMethod == "sharedService" && config.callbackFunction == "getSites" && mySiteId != ''){
                  let updatePatientData = {'patientId': parseInt(e.data.PatientId),'siteId': mySiteId,'siteName': e.data.SiteId, 'updateSite': true};
                  PatientActivityHubComponent.returned.next(updatePatientData);
                }
                if (this.enableCellEditHistory == 'true') {
                  e.node.setDataValue('submissionID', response['updateFormData'][0].submissionID);
                  if (this.selectedLoadingType == 'allRecords') {
                    this.showAllEntries('all', '');
                  }
                }
                var activityData = {
                  activityName: "Update Single Worklist Field",
                  activityType: "worklist forms",
                  activityDescription: this.userData.displayName + " updated the field " + e.colDef.field + " from " + e.oldValue + " to " + e.newValue + 'of entry id ' + selectRowData + 'of form id' + formId,
                };
                this._structureService.trackActivity(activityData);
                if (count == i) {
                  if (config.enableMultiSelectionEdit == true && selectRowData.length > 1) {
                    this.getFormDataBasedRowModel();
                  }
                  let sendData = {
                    rowData: e.data,
                    updateData: [{ updateField: e.colDef.field, updatedValue: e.newValue }],
                    submissionId: selectRowData,
                    formId: formId,
                    worklistId: this.worklistid,
                    users: [],
                    action: 'update',
                    clientId: this._structureService.socket.io.engine.id
                  };
                  this.sendWorklistUpdatePolling(sendData);
                  if (this.metaData.showAlert == true) {
                    const name = e.colDef.headerName.toLowerCase();
                    let headerName = name.charAt(0).toUpperCase() + name.slice(1);
                    setTimeout(function () {
                      $.notify({ message: headerName + ' updated successfully.' }, { type: 'success' });
                    }, 1000);
                  }
                }
                i++;
                /**Send notification message */
                if (this.metaData.enableNotification == true) {
                  let notificationConfig = {};
                  let staffId = '';
                  if (this.metaData.notificationLevel == 'workListLevel') {
                    if (this.metaData.notifyRecipientOnValueChange == true) {
                      if (e.data.hasOwnProperty('StaffId') == true) {
                        staffId = e.data.StaffId;
                      } else if (this.metaData.fieldForRecipient != '') {
                        const index = this.reportFields.findIndex(x => x.fieldName == this.metaData.fieldForRecipient);
                        const delimitter = this.reportFields[index].fieldValueDelimiter;
                        const value = e.data[this.metaData.fieldForRecipient];
                        if (delimitter != '') {
                          if (delimitter.length == 1) {
                            if (value.includes(delimitter)) {
                              const originalValue = value.split(delimitter);
                              staffId = originalValue[1];
                            }
                          } else if (delimitter.length == 2) {
                            if (value.includes(delimitter.charAt(0)) && value.includes(delimitter.charAt(1))) {
                              let originalValue = value.split(delimitter.charAt(0));
                              originalValue = originalValue[1].split(delimitter.charAt(1))[0];
                              staffId = originalValue;
                            }
                          }
                        }
                      }
                    }
                    notificationConfig = {
                      notifyingRoles: this.metaData.notifyRolesOnValueChange ? this.metaData.notifyRoles : '',
                      notifyRecipient: staffId,
                      notifyMode: {
                        push: this.metaData.fieldChangePush,
                        pushTemplate: this.metaData.fieldChangePushTemplate,
                        sms: this.metaData.fieldChangeSms,
                        smsTemplate: this.metaData.fieldChangeSmsTemplate,
                        email: this.metaData.fieldChangeEmail,
                        emailTemplate: this.metaData.fieldChangeEmailTemplate
                      },
                      data: e.data
                    }
                    this._workListService.sendNotificationMessage(notificationConfig,this.allMessageTemplate,this.formName,this.heading);
                  } else {
                    if (config.enableNotification == true) {
                      if (config.notifyRecipientOnValueChange == true) {
                        if (e.data.hasOwnProperty('StaffId') == true) {
                          staffId = e.data.StaffId;
                        } else if (this.metaData.fieldForRecipient != '') {
                          const index = this.reportFields.findIndex(x => x.fieldName == this.metaData.fieldForRecipient);
                          const delimitter = this.reportFields[index].fieldValueDelimiter;
                          const value = e.data[this.metaData.fieldForRecipient];
                          if (delimitter != '') {
                            if (delimitter.length == 1) {
                              if (value.includes(delimitter)) {
                                const originalValue = value.split(delimitter);
                                staffId = originalValue[1];
                              }
                            } else if (delimitter == 2) {
                              if (value.includes(delimitter.charAt(0)) && value.includes(delimitter.charAt(1))) {
                                let originalValue = value.split(delimitter.charAt(0));
                                originalValue = originalValue[1].split(delimitter.charAt(1))[0];
                                staffId = originalValue;
                              }
                            }
                          }
                        }
                      }
                      notificationConfig = {
                        notifyingRoles: config.notifyRolesOnValueChange == true ? (config.notificationSettings == 'custom' ? config.customNotifyRoles : this.metaData.notifyRoles) : '',
                        notifyRecipient: staffId,
                        notifyMode: {
                          push: config.push,
                          pushTemplate: config.pushTemplate,
                          sms: config.sms,
                          smsTemplate: config.smsTemplate,
                          email: config.email,
                          emailTemplate: config.emailTemplate
                        },
                        data: e.data
                      };
                      if (config.notifySpecificValue == true) {
                        if (e.newValue.toLowerCase() == config.notificationFieldValue.toLowerCase()) {
                          this._workListService.sendNotificationMessage(notificationConfig,this.allMessageTemplate,this.formName,this.heading);
                        }
                      } else {
                        this._workListService.sendNotificationMessage(notificationConfig,this.allMessageTemplate,this.formName,this.heading);
                      }
                    }
                  }
                }
              } else {
                const message  = 'Your changes failed to save. Please redo the changes and save again.';
                swal({
                  title: '',
                  text: response['updateFormData'][0].description ? response['updateFormData'][0].description : message,
                  showCancelButton: false,
                  customClass: 'swal-wide',
                  cancelButtonClass: "btn-default",
                  closeOnConfirm: true
                 });
                }

            });
          } else {
            $.notify({ message: 'Invalid Date. Please enter a valid date and try again.' }, { type: 'danger' });
            e.newValue = e.oldValue;
            newValue = e.oldValue;
          }
        });
      }
    }
  }
  /**Polling for any update in worklist */
  sendWorklistUpdatePolling(sendData) {
    if (this.metaData.visibleToRoles.split(',').length > 1) {
      const count = this.metaData.visibleToRoles.split(',').length;
      let i = 1;
      this.metaData.visibleToRoles.split(',').forEach(element => {
        this._workListService.getRoleBasedStaffs(element, 0, 1).then((data) => {
          const staffList = JSON.parse(JSON.stringify(data['getSessionTenant'].roleBasedStaffs));
          staffList.forEach(element => {
            sendData['users'].push(element.id);
          });
          if (i == count) {
            this._structureService.socket.emit('worklistDataUpdate', sendData);
          }
          i++;
        });
      });
    } else {
      this._workListService.getRoleBasedStaffs(this.metaData.visibleToRoles, 0, 1).then((data) => {
        const staffList = JSON.parse(JSON.stringify(data['getSessionTenant'].roleBasedStaffs));
        staffList.forEach(element => {
          sendData['users'].push(element.id);
        });
        this._structureService.socket.emit('worklistDataUpdate', sendData);
      });
    }
  }
  setColumnDefinitionWithOptions(metaData) {
    var promise = new Promise((resolve, reject) => {
      if (metaData) {
        let colDef = this.columnDefs;
        this.reportFields.forEach(element => {
          if (element.allowPrefillData == true) {
            /**prefill option in inline cell editing */
            if (element.prefillMethod == 'formOptions') {
              let formElementId: number;
              let formId: number;
              let formType = 'internal';
              let extraOptions = [];
              if (element.prefillFormOption == 'internal') {
                if (element.fieldType == 'child') {
                  formId = this.metaData.associatedForm;
                } else {
                  formId = this.formId;
                }
                if (element.fieldId) {
                  formElementId = element.fieldId;
                }
              } else {
                formType = 'external';
                formId = element.prefillOptionForm;
                formElementId = element.prefillOptionFormField;
                /**Add extra option*/
                if (element.prefillExtraOptions && element.prefillExtraOptions != '') {
                  extraOptions = element.prefillExtraOptions.split('||');
                }
              }
              /**Show values in dropdown while double click on cell */
              // columnObj['cellEditor'] = 'agRichSelectCellEditor';
              // columnObj['cellEditorParams'] = {
              //   values: undefined
              // };
              if (metaData && metaData.length > 0) {
                let index = metaData.findIndex(x => x.tid == element.fieldId);
                let elementList = [];
                if(index != -1 && metaData[index].options){
                  metaData[index].options.forEach(element => {
                    if (element.optionValue != '') {
                      elementList.push(element.optionValue);
                    }
                  });
                  this.list[element.fieldName] = JSON.parse(JSON.stringify(metaData[index].options));
                  this.list[element.fieldName].push({ 'type': formType });
                }
                /**set dropdown list while editing cell */
                this.setDropdownList(elementList, element.fieldName, element.fieldType);
              } else {
                /**Get the options of a machform field*/
                this._workListService.getMachformFields(formId, Number(formElementId)).refetch().then(({ data: response }) => {
                  this.list[element.fieldName] = JSON.parse(JSON.stringify(response['getFormElementDetails'].options));
                  this.list[element.fieldName].push({ 'type': formType });
                  let elementList = [];
                  response['getFormElementDetails'].options.forEach(element => {
                    if (element.optionValue != '') {
                      elementList.push(element.optionValue);
                    }
                  });
                  extraOptions.forEach(element => {
                    elementList.push(element.trim());
                  });
                  /**set dropdown list while editing cell */
                  this.setDropdownList(elementList, element.fieldName, element.fieldType);
                });
              }
            }
            if (element.prefillMethod == 'sharedService') {
              let elementList = [];
              let mySiteUserData = JSON.parse(this._structureService.userDetails);
              mySiteUserData.mySites.forEach(element => {
                elementList.push(element.name);
              });
              this.setDropdownList(elementList, element.fieldName, element.fieldType);
            }
          }
        });
        //this.gridApi.setColumnDefs(this.columnDefs);
        console.log('Time: set inline edit options', moment().format('LTS') + ' ' + moment().millisecond());
        console.log('column definition', this.columnDefs);
      }
      resolve();
    });
    return promise;
  }
  getElementIdType(element) {
    console.log(element);
    if (this.machFormIds.length > 0) {
      if (this.machFormIds[1].hasOwnProperty(element)) {
        return { 'id': this.machFormIds[1][element].id, 'type': this.machFormIds[1][element].elementType, 'hasOtherOption': this.machFormIds[1][element].OtherExists };
      } else {
        return {};
      }
    } else if (this.formMetaData && this.formMetaData.length > 0) {
      let index = this.formMetaData.findIndex(x => x.labelText.toLowerCase() === element.toLowerCase());
      if (index != -1) {
        return { 'id': this.formMetaData[index].tid, 'type': this.formMetaData[index].valueType, 'hasOtherOption': this.formMetaData[index].hasOtherOption ? this.formMetaData[index].hasOtherOption : 0 };
      } else {
        return {};
      }
    } else {
      const index = this.reportFields.findIndex(x => x.fieldName == element);
      if (index != -1) {
        return { 'id': this.reportFields[index].fieldId, 'type': this.reportFields[index].valueType, 'hasOtherOption': 0 };
      } else {
        return {};
      }
    }
  }
   /**Change pagination size from ui */
	changePaginationSize(e) {
		this.paginationPageSize = e.target.value.trim();
		this.getformData();
    // this.paginationPageSize = e.target.value.trim();
    // var datasource = ServerSideDatasource(this, false, false, this._workListService);
    // this.gridApi.setServerSideDatasource(datasource);
    // let obj = { worklistId: this.worklistid, pageSize: this.paginationPageSize };
    // localStorage.setItem('worklistPageSize', JSON.stringify(obj));
	}
  /**Refill action */
  refillEntry(e) {
    const actionConfig = e.actionConfig;
    const editType = actionConfig.showEditForm;
    const rolesForEditViewForm = actionConfig.rolesForEditViewForm;
    this.showRefillEdit = true;
    const worklistType = 'NGW';
    const editRowDetails = e.rowData;
    const entryId = editRowDetails['submissionID'];
    this.hideBreadcrumb = true;
    this.refillEditVariable = {
      "worklistName": this.worklistName,
      "worklistid": this.worklistid,
      "submisionId": editRowDetails['submissionID'],
      "type": "refill"
    };
    if (editType == 'popupWindow') {
      if(rolesForEditViewForm != "" && rolesForEditViewForm.split(',').indexOf(this.userData.roleId) !== -1){
        $('#refillEditModal').modal('show');
      }else {
        this.viewFormData(this.formId, entryId, worklistType); 
      }
    } else {
      let actionLink = this.worklistName.replace(/[^a-zA-Z ]/g, ' ').trim();
      actionLink = actionLink.replace(/\s+/g, '-').toLowerCase();
      if(rolesForEditViewForm != "" && rolesForEditViewForm.split(',').indexOf(this.userData.roleId) !== -1){
        window.open(`worklist/${actionLink}/details/${this.worklistid}/${editRowDetails.submissionID}/refill`, '_blank');
      }else {
        window.open(`view/${this.formId}/${entryId}/${worklistType}`, '_blank');
      }
     
    }
    var activityData = {
      activityName: "Refill Worklist Details",
      activityType: "worklist forms",
      activityDescription: this.userData.displayName + " refill the details of the entry having id" + editRowDetails.submissionID + "in " + this.heading + " form",
    };
    this._structureService.trackActivity(activityData);
  }
  viewFormData(formId, entryId, worklistType) {
    this._formsService.getStrucuturedFormResults(formId, entryId, worklistType).then((result) => {
      this.strucuredFormsName = result['formName'];
      this.strucuredFormsData = result['structuredFormResult'];
        this.strucuredFormsData.forEach(element => {
          if(element.label.indexOf('{{') != -1) {
            let labelArray = element.label.split('{{');
            element.label = labelArray[0];
          }
        });
      this.showFormView = true;
      $('#view-form-modal').modal('show');
    });
  }
  
  closeRefillEditModalal() {
    $('#refillEditModal').modal('hide');
    let self = this;
    this.getformData();
    this.gridApi.deselectAll();
    setTimeout(function () {
      self.showRefillEdit = false;
      // localStorage.removeItem('formData');
      // self._structureService.deleteCookie('formData');
    }, 500);
  }
  setWebHookTime(element) :any{
    return ((this.formpPipe.transform(parseInt(element.created_on) * 1000).length >= 9) ? " on " : " at ") +
    this.formpPipe.transform(parseInt(element.created_on) * 1000);
   }
   
   closeHistoryModal(e) {
    if (e) {
      this.showHistoryModal = false;
      this.showHistory = false;
    }
  }
  closeDraftHistoryModal(e) {
    if (e) {
      this.showHistoryModal = false;
      this.showDraftHistory = false;
    }
  }

  clearStatePrefrence() {
    if(this.userPreferenceId != 0) {
      swal({
        title: this._ToolTipService.getTranslateData('MESSAGES.CONFIRM_MESSAGE'),
        text: this._ToolTipService.getTranslateData('MESSAGES.WORKLIST_SESSION_RESET'),
        type: 'warning',
        showCancelButton: true,
        cancelButtonClass: 'btn-default',
        confirmButtonClass: 'btn-warning',
        confirmButtonText: 'Ok',
        closeOnConfirm: true
      }, () => {
        if(this.metaData.stateSavingPreference && this.metaData.stateSavingPreference === 'userProfile') {
         const variable = {
            category: 'worklist_center',
            object_id: Number(this.worklistid),
            profile_key: 'worklist_state'
          };
          this._workListService.deleteWorklistStatePreference(variable).then(
              (data)=> {
                if(data['deleteUserPreference'] && data['deleteUserPreference'].status === 200) {
                  const worklistState = {
                    columnState : this.defaultColumnState,
                    filterState: this.defaultFilterState,
                    sortState: this.defaultSortState,
                    searchState : {
                                  selectedSearchFieldText : '',
                                  selectedSearchFields : this.defaultSelectedSearchFields
                                  }
                  };
                  this.setStatePreferencetoGrid(worklistState, 'clear', this.worklistid);
                  this.gridApi.setColumnDefs(this.columnDefs);
                  this.userPreferenceId = 0;
                  const index = this._sharedService.worklistState.findIndex(worklist => 
                    +worklist.worklistId === +this.worklistid && +worklist.widgetId === +this.selectedWidgetId && worklist.worklistSource === 'PAH');
                  if(index !== -1) {
                    this._sharedService.worklistState.splice(index, 1);
                  }
                  const pIndex = this._sharedService.userWorklistPrefrenceIds.findIndex(item => 
                    item.worklistId === +this.worklistid && item.widgetId === this.selectedWidgetId);
                  if(pIndex !== -1) {
                    this._sharedService.userWorklistPrefrenceIds.splice(pIndex, 1);
                  }
                  setTimeout(function () {
                    $.notify({ message: this._ToolTipService.getTranslateData('MESSAGES.WORKLIST_SESSION_RESET') }, { type: 'success' });
                  }, 1000);
                  const activityData = {
                    activityName: 'Reset worklist state preference',
                    activityType: 'Worklist state preference',
                    activityDescription: this.userData.displayName + ' reset the worklist state preference of '+ this.heading+'('+ this.worklistid+') from user profile.',
                  };
                  this._structureService.trackActivity(activityData);
                } else {
                  setTimeout(function () {
                    $.notify({ message: this._ToolTipService.getTranslateData('MESSAGES.ERROR_MSG_EXCEPTION_CATCH') }, { type: 'danger' });
                  }, 1000);
                }
              },
              () => {
                setTimeout(function () {
                  $.notify({ message: this._ToolTipService.getTranslateData('MESSAGES.ERROR_MSG_EXCEPTION_CATCH') }, { type: 'danger' });
                }, 1000);
          });
        } else {
          const index = this._sharedService.worklistState.findIndex(x=> 
            +x.worklistId === +this.worklistid && +x.widgetId === +this.selectedWidgetId && x.worklistSource === 'PAH');
          if(index != -1) {
            this._sharedService.worklistState.splice(index, 1);
          }
        }
      });
    } else {
      setTimeout(function () {
        $.notify({ message: this._ToolTipService.getTranslateData('MESSAGES.WORKLIST_SESSION_NOT_AVAILABLE') }, { type: 'warning' });
      }, 1000);
    }
  }
  saveStatePrefrence() {
    let columnState;
    let filterState;
    let sortState;
    let searchState = {};
    let widgetState = '';
    if(this.metaData.columnState && this.metaData.columnState == true) {
      columnState = this.gridColumnApi.getColumnState();
    }
    if(this.metaData.filterState && this.metaData.filterState == true) {
      filterState = this.gridApi.getFilterModel();
      widgetState = this.selectedWidget;
    }
    if(this.metaData.sortState && this.metaData.sortState == true) {
      sortState = this.gridApi.getSortModel();
    }
    if(this.metaData.searchState && this.metaData.searchState == true) {
      if(this.rowModelType === 'serverSide'){
      if(this.searchFieldText !== '' && this.selectedSearchFields.length > 0) {
        searchState = { selectedSearchFieldText: this.searchFieldText, 
        selectedSearchFields: this.selectedSearchFields };
      } else {
        searchState = {};
      }
      }
      if(this.rowModelType === 'clientSide'){
        if(this.searchFieldText !== '') {
          searchState = { selectedSearchFieldText: this.searchFieldText};
        } else {
          searchState = {};
        } 
      }
       
    }
    const worklistState = {'columnState': columnState, 'filterState': filterState, 'sortState': sortState, 'searchState': searchState, 'widgetState': widgetState};
    if(this.metaData.stateSavingPreference && this.metaData.stateSavingPreference === 'userProfile') {
      const variable = {
        userId: Number(this.userData.userId),
        category: 'worklist_center',
        meta: `${JSON.stringify(worklistState)}`,
        object_id: Number(this.worklistid),
        profile_key: 'worklist_state',
        tenantId: Number(this._structureService.getCookie('tenantId')),
        widgetId: this.selectedWidgetId,
        modifiedBy: Number(this.userData.userId)
      };
      this._workListService.saveWorklistStatePreference(variable).then(
        (data)=> {
        if(data['manageUserPreference'] && data['manageUserPreference'].status === 200) {
          this.userPreferenceId = data['manageUserPreference'].data.id;
          this._sharedService.userWorklistPrefrenceIds.push({worklistId: this.worklistid,
          userPreferenceId: data['manageUserPreference'].data.id,
          widgetId: this.selectedWidgetId});
          if(this.metaData.stateSavingMode === 'manualSave') {
            setTimeout(function () {
              $.notify({ message: this._ToolTipService.getTranslateData('MESSAGES.WORKLIST_SESSION_STATE_SAVE')}, { type: 'success' });
            }, 1000);
          }
          const activityData = {
            activityName: 'Save worklist state preference',
            activityType: 'Worklist state preference',
            activityDescription: this.userData.displayName + ' saved the worklist state preference of '+ this.heading+'('+ this.worklistid+') to user profile with meta of '+ JSON.stringify(worklistState),
          };
          this._structureService.trackActivity(activityData);
        } else {
          if(this.metaData.stateSavingMode === 'manualSave') {
            setTimeout(function () {
              $.notify({ message: this._ToolTipService.getTranslateData('MESSAGES.ERROR_MSG_EXCEPTION_CATCH') }, { type: 'danger' });
            }, 1000);
          }
        }
      },
      () => {
        if(this.metaData.stateSavingMode === 'manualSave') {
          setTimeout(function () {
            $.notify({ message: this._ToolTipService.getTranslateData('MESSAGES.ERROR_MSG_EXCEPTION_CATCH') }, { type: 'danger' });
          }, 1000);
        }
      });
    }
    const index = this._sharedService.worklistState.findIndex(x=> 
      +x.worklistId === +this.worklistid && +x.widgetId === +this.selectedWidgetId && x.worklistSource === 'PAH');
    if(index != -1) {
      this._sharedService.worklistState[index].worklistState = worklistState;
    } else {
      this._sharedService.worklistState.push({worklistId: this.worklistid,
        worklistState: worklistState, widgetId: this.selectedWidgetId, worklistSource: 'PAH' });
    }
    if(this.metaData.stateSavingPreference === 'cache' && this.metaData.stateSavingMode === 'manualSave') {
      setTimeout(function () {
        $.notify({ message: this._ToolTipService.getTranslateData('MESSAGES.WORKLIST_SESSION_STATE_SAVE') }, { type: 'success' });
      }, 1000);
    }
  }
  setStatePreferencetoGrid(worklistState, type, worklistid) {
    if(worklistState['columnState'] && this.metaData.columnState) {
      //Compare the default column state and user profile state. Modified the user profile state according to default state.
      if(this.gridColumnApi.getColumnState().length > worklistState['columnState'].length) {
        const defState = this.gridColumnApi.getColumnState();
        let i = 0;
        defState.forEach((state)=> {
          if(!worklistState['columnState'].some(x=> x.colId === state.colId)) {
            worklistState['columnState'].splice(i, 0, state);
          }
          i++;
        });
      }
      if(type !== 'clear') {
        this.defaultColumnState = this.gridColumnApi.getColumnState();
      }
      this.setColumnState = false;
      setTimeout(() => {
        this.gridColumnApi.setColumnState(worklistState['columnState']);
        this.gridApi.refreshView(); 
      },100);
    
      this.columnStateChanged();
    }
    /**Apply filter state to grid if filter state is present in saved worklist state*/
    if (worklistState['filterState'] && this.metaData.filterState && Object.keys(worklistState['filterState']).length) {
      if(type !== 'clear') {
        this.defaultFilterState = this.gridApi.getFilterModel();
      }
      this.gridApi.setFilterModel(worklistState['filterState']);
    } else if (this.metaData.widgetState) {
      let filterModel = [];
      filterModel[this.selectedWidgetField] = { type: 'equals', filter: this.selectedWidget, filterType: 'text' };
      this.gridApi.setFilterModel(filterModel);
    }
    this.setFilterState = true;    
    /**Apply sort state to grid if sort state is present in saved worklist state*/
    if(worklistState['sortState'] && this.metaData.sortState && worklistState['sortState'].length) {
      if(type !== 'clear') {
        this.defaultSortState = this.gridApi.getSortModel();
      }
      this.gridApi.setSortModel(worklistState['sortState']);
    } else {
      this.gridApi.setSortModel(null);
    }
    this.setSortState = true;
    /**Apply widget state if filter state is present in saved worklist state*/
    if(worklistState['widgetState'] && this.metaData.filterState) {
      const widgetState = worklistState['widgetState'];
      if (widgetState && widgetState !== '') {
        this.selectedPreviousWidget = widgetState;
        this.selectedWidget = widgetState;
        this.optionShowButton = this.selectedWidget;
      }
    }
     /**Apply search state if search state is present in saved worklist state*/
    if (worklistState['searchState'] && this.metaData.searchState) {
      const searchState = worklistState['searchState'];
      if (searchState && Object.keys(searchState).length > 0) {
        this.searchFieldText = searchState.selectedSearchFieldText
          ? searchState.selectedSearchFieldText
          : '';
        this.savedSearchText = this.searchFieldText;
        this.selectedSearchFields = searchState.selectedSearchFields
          ? searchState.selectedSearchFields
          : [];
        this.customSearch = this.selectedSearchFields.length > 0;
        if (this.rowModelType !== 'serverSide') {
          this.searchFilterData(this.searchFieldText);
        }
      } else {
        this.searchFieldText = '';
        this.savedSearchText = '';
        this.selectedSearchFields = this.defaultSelectedSearchFields;
        if(this.rowModelType !== 'serverSide'){
          this.searchFilterData('');
        }
      }
      this.defaultSelectedSearchFields.forEach((element) => {
        if (
          this.selectedSearchFields.find(
            (item) => item.fieldId === element.fieldId
          )
        ) {
          $('#checkbox' + element.fieldId).prop('checked', true);
        } else $('#checkbox' + element.fieldId).prop('checked', false);
      });
    }
    if (type === 'userProfile') {
      this._sharedService.worklistState.push({worklistId: worklistid,
        worklistState: worklistState, widgetId: this.selectedWidgetId, worklistSource: 'PAH'});
    }
    this.setSessionState = true;
    const filterKey = Object.keys(worklistState['filterState']);
    if ((!isBlank(this.searchFieldText) || filterKey.some(item => 
      (item !== this.selectedWidgetField) && item !== 'PatientId')
   )){
      this._structureService.notifySearchFilterApplied(true);
    } else {
      this._structureService.notifySearchFilterApplied(false);
    }
    if((isBlank(worklistState['filterState']) && isBlank(worklistState['sortState']) && !this.metaData.widgetState) || type === 'cache' || type === 'userProfile') {
      this.loadWorklistData();
    }
  }
  sortChanged(params) {

    autosizeHeaders(params);
    /**Avoid the saveStatePrefrence function call while apply state to the grid in initial loading */
    if (this.metaData.sortState && this.metaData.stateSavingMode === 'autoSave' && !this.setSortState) {
      this.saveStatePrefrence();
    } else {
      this.setSortState = false;
    }
  }
  columnStateChanged() {
    if (this.metaData.columnState && this.metaData.stateSavingMode === 'autoSave' && this.setColumnState) {
      this.saveStatePrefrence();
    } else {
      this.setColumnState = true;
    }
  }
  onColumnDragStopped(params) {
    if (this.metaData.columnState && this.metaData.stateSavingMode === 'autoSave') {
      this.saveStatePrefrence();
    }
  }
  onFilterChanged(params) {
    /**Avoid the saveStatePrefrence function call while apply state to the grid in initial loading */
    if(this.metaData.filterState && this.metaData.stateSavingMode === 'autoSave' && !this.setFilterState) {
      this.saveStatePrefrence();
    } else {
      this.setFilterState = false;
    }
    const filterKey = Object.keys(this.gridApi.getFilterModel());
    if (isBlank(this.searchFieldText) && !filterKey.some(item => 
      (item !== this.selectedWidgetField) && item !== 'PatientId')
    ) {
      this._structureService.notifySearchFilterApplied(false);
    }
  }
}

function ServerSideDatasourceApi(this1, searchText) {
  let cache: any = [];
  let filterModelArray = [];
  let quickFilter;
  return {
  getRows(params) {
  console.log(params);
  console.log(this1.metaData);
  console.log(this1);
  let start = params.request.startRow;
  let end = params.request.endRow;
  $('.ag-paging-panel').css('visibility','visible');
  this1.disableWidget = true;
  let offset = params.request.startRow;
  let filterModel = params.request.filterModel;
  let finalFilterModel = {};
  if(this1.customSearch == false ) {
  this1.searchFieldText = '';
  this1.filterEnabledFields.forEach(element => {
  $('#checkbox'+element.fieldId).prop("checked", false);
  });
  this1.selectedSearchFields = [];
  }
  if(this1.searchFieldText != '' && this1.selectedSearchFields.length > 0) {
  let filterFields = this1.reportFields.filter(x=> x.allowFilter == true && x.valueType != 'checkbox');
  this1.selectedSearchFields.forEach(element => {
  if(element.valueType == 'number') {
  finalFilterModel[element.fieldName] = {type: 'equals', filter: this1.searchFieldText, filterType: 'number', filterCondition: 'OR'};
  } else if(element.valueType == 'date') {
  finalFilterModel[element.fieldName] = {type: 'equals', dateFrom: this1.searchFieldText, filterType: 'date', filterCondition: 'OR'};
  } else if(element.valueType == 'radio' || element.valueType == 'checkbox' || element.valueType == 'select') {
  finalFilterModel[element.fieldName] = {type: 'contains', filter: this1.searchFieldText, filterType: 'text', filterCondition: 'OR'};
  } else {
  finalFilterModel[element.fieldName] = {type: 'contains', filter: this1.searchFieldText, filterType: 'text', filterCondition: 'OR'};
  }
  });
  }
  if(Object.keys(filterModel).length > 0 ) {
  let filterModelKeys = Object.keys(filterModel);
  let widgetFilterKeys = filterModelKeys.filter(x=> x == this1.selectedWidgetField);
  let otherModelKeys = filterModelKeys.filter(x=> x != this1.selectedWidgetField);
  if (otherModelKeys.length > 0 && this1.savedSearchText === '') {
  this1.searchFieldText = '';
  this1.filterEnabledFields.forEach(element => {
  $('#checkbox'+element.fieldId).prop("checked", false);
  });
  this1.selectedSearchFields = [];
  finalFilterModel = filterModel;
  } 
  if(widgetFilterKeys.length > 0) {
  finalFilterModel[this1.selectedWidgetField] = filterModel[this1.selectedWidgetField];
  }
  }
  let sortModel = params.request.sortModel;
  let limit = this1.cacheBlockSize;
  this1.parameters = [];
  if(this1.metaData.parameters){
  this1.metaData.parameters.split('&').forEach(element => {
  let key = element.substring(
  element.lastIndexOf('{{') + 2,
  element.lastIndexOf('}}')
  );
  if (key == 'userId') {
  element = element.replace('{{' + key + '}}', this1.userId);
  }
  if (key == 'logginUserId') {
  element = element.replace('{{' + key + '}}', this1.userData.userId);
  }
  if (key == 'tenantId') {
  element = element.replace('{{' + key + '}}', this1._structureService.getCookie('tenantId'));
  }
  if (key == 'crossTenantId') {
  element = element.replace('{{' + key + '}}', this1._structureService.getCookie("crossTenantId"));
  }
  if (key == 'roleid') {
  element = element.replace('{{' + key + '}}', this1.userData.roleId);
  }
  if (key == 'zone') {
  element = element.replace('{{' + key + '}}', timezone.name());
  }
  if (key == 'isForms') {
  element = element.replace('{{' + key + '}}', true);
  }
  if (key == 'isPrivilege') {
  element = element.replace('{{' + key + '}}', true);
  }
  if (key == 'pah_patient_id') {
  element = element.replace('{{' + key + '}}', this1.userPatientId);
  }
  if (key == 'limit') {
  element = element.replace('{{' + key + '}}', limit);
  }
  if (key == 'offset') {
  element = element.replace('{{' + key + '}}', offset);
  }
  this1.parameters.push(element);
  });
  }
  if (searchText != '') {
  this1.parameters.push('searchText=' + searchText);
  }
  if (Object.keys(finalFilterModel).length > 0) {
  this1.parameters.push('filterModel=' + JSON.stringify(finalFilterModel));
  }
  if (sortModel.length > 0) {
  this1.parameters.push('sortModel=' + JSON.stringify(sortModel));
  }
  if (this1._structureService.getCookie("crossTenantId") && this1._structureService.getCookie('crossTenantId') !== 'undefined' && this1._structureService.getCookie('tenantId') !== this1._structureService.getCookie('crossTenantId')) {
  if(this1.parameters.indexOf('crossTenantId='+this1._structureService.getCookie("crossTenantId")) < 0) {
  this1.parameters.push('crossTenantId=' + this1._structureService.getCookie("crossTenantId"));
  }
  }
  Object.keys(finalFilterModel).forEach((filter) => {
    let filterObject = {
      column:filter,
      filter: finalFilterModel[filter].filter ? finalFilterModel[filter].filter.toString().trim() : '',
      filterTo: finalFilterModel[filter].filterTo,
      filterIn: finalFilterModel[filter].filterIn,
      type: finalFilterModel[filter].type,
      filterCondition: finalFilterModel[filter].filterCondition ? finalFilterModel[filter].filterCondition : 'AND'
    }
    filterModelArray.push(filterObject);
  });
  if (this1.metaData.graphqlApi) {
  let url = this1.metaData.graphqlEndpoint;
  let fieldList = this1.metaData.fieldList.split(',');
  let fieldString = '';
  fieldList.forEach(field => {
  if (field.includes('.')) {
  let colArray = field.split('.');
  let endString = '';
  colArray.forEach((element, index) => {
  fieldString = ` ${fieldString} ${element} `;
  if (index !== colArray.length - 1) {
  fieldString = ` ${fieldString} { `;
  endString = ` ${endString} } `;
  }
  
  });
  fieldString = ` ${fieldString} ${endString} `;
  } else {
  fieldString = `${fieldString} ${field}`;
  }
  });
  let newQuery = this1.metaData.parameters.replace('$fields', fieldString); 
  let variables = {}
  let documentTypes='';
  if (this1.metaData.parameters.includes('patientDocuments')) {
    console.log("hii");
    console.log(this1.metaData);
    // if (this1.metaData.counterApiCallback == 'prescriptionOrders') {
    //   console.log("hii");
      // variables.noteType='liaison'
      variables = { 
        patientId: Number(this1.userPatientId), 
        // id:68972,
        // sessionToken:"AQIC5wM2LY4SfcwZQF1lXaJtcbbF3SgHW24F_s5XfzBcqKE.*AAJTSQACMDEAAlNLABItMTM0NDM3NzMzOTgwMTMxNTkAAlMxAAA.*"};
        sessionToken: this1._structureService.getCookie('authID'),
        startRow:start,
        endRow:end,
        filter:filterModelArray,
        sorting: params.request.sortModel
      };
    }
    // if (this1.metaData.counterApiCallback == 'documents') {
    //   // variables.noteType='liaison'
    //   variables = { 
    //     id: this1.userPatientId, 
    //     // id:68972,
    //     // sessionToken:"AQIC5wM2LY4SfcwZQF1lXaJtcbbF3SgHW24F_s5XfzBcqKE.*AAJTSQACMDEAAlNLABItMTM0NDM3NzMzOTgwMTMxNTkAAlMxAAA.*"};
    //     sessionToken: this1._structureService.getCookie('authID'),
    //     startRow:start,
    //     endRow:end,
    //     docType:"doc",
    //     filter:filterModel,
    //     sorting: params.request.sortModel,
    //     widgetField: ["patientPatStat"]
    //   };
    // }
  // }
  
  getDataSourceGraphqlApi(params,url, newQuery, variables, this1);
  } else {
  this1.totalRowData = [];
  let payloadParam = this1.parameters.filter(name => !name.match('tenantId=')).filter(name => !name.match('crossTenantId='));
  this1._workListService.getWorklistDataUsingAPI(this1.metaData.endpoint,payloadParam.join('&')).then((data) => {
    this1.resetAppliedFilterNotification(filterModelArray);
    this1.disableWidget = false;
  this1.totalRowData = data;
  
  if (offset == 0) {
  this1.setDefaultWidgetConfigApi();
  if(data['totalCount']){
  this.totalCount = data['totalCount'];
  }
  }
  this1.rowData = data['response'];
  this1.reportFields.forEach(element => {
  if(element.hideColumnBasedField && element.hideColumnBasedField != null && this1.rowData.length > 0) {
  if(this1.rowData[0][element.hideColumnBasedField] == element.hideColumnValue) {
  this1.gridColumnApi.setColumnVisible(element.fieldName, false);
  }
  }
  });
  if (this1.rowData && this1.rowData.length > 0) {
  var server = {
  success: true,
  rows: this1.rowData,
  //lastRow: 5
  }
  let res = server;
  let lastRowValue = params.request.startRow + res.rows.length;
  let filterCount = data[this1.filterText.replace(/\s/g, "")];
  if (res.success) {
  const rows = res.rows;
  let lastRow = '';
  if ((res.rows.length < this1.cacheBlockSize) || (lastRowValue == filterCount) || (this.totalCount != 0 && lastRowValue == this.totalCount) ) {
  lastRow = params.request.startRow + res.rows.length;
  }
  setTimeout(() => {
  cache = [];
  this1.gridApi.forEachNode(node => cache.push(node.data));
  }, 0)
  quickFilter = $(".search-afterfilter").val();
  if (quickFilter) {
  let filteredResults = cache.filter(row => {
  return row.toLowerCase().includes(quickFilter.toString().toLowerCase());
  })
  if (filteredResults.length != 0) {
  params.successCallback(filteredResults, filteredResults.length);
  return;
  }
  else {
  params.successCallback([], 0);
  this1.gridApi.hideOverlay();
  // this1.gridApi.showNoRowsOverlay();
  return;
  }
  }
  params.successCallback(rows, lastRow);
  } else {
  params.failCallback();
  }
  } else {
  this1.rowData = [];
  this1.suppressNoRow = false;
  this1.gridApi.hideOverlay();
  let selectedTab = $(".cat__apps__messaging__tab_pah--selected").text().toLowerCase();
  let selectedProfileTab = $(".nav-item .active").text().toLowerCase();
  if(selectedTab.trim() == this1.worklistName.toLowerCase()){
  $('.ag-row-stub').html('<p style="margin-top:7px!important;text-align: center!important;">There are no ' + this1.filterText.toLowerCase() + ' ' + selectedTab + ' with this patient.</p>');
  }
  if(selectedProfileTab.trim() == this1.worklistName.toLowerCase()){
    $('.ag-row-stub').html('<p style="margin-top:7px!important;text-align: center!important;">There are no ' + this1.filterText.toLowerCase() + ' ' + selectedProfileTab + ' with this patient.</p>');
    }
  if (offset == 0) {
  $('.ag-paging-panel').css('visibility','hidden');
  }
  }
  });
  }
  }
  };
  }
      
function ServerSideDatasource(this1, dataCount = false, initialLoad = false, worklistService) {
  return {
    getRows(params) {
      this1.disableWidget = true;
      let start = params.request.startRow;
      let end = params.request.endRow;
      const filterModel = params.request.filterModel;
      const sortModel = params.request.sortModel;
      let formattedSortModel = [];
      let showDateForCount = false;
      if (Object.keys(sortModel).length > 0) {
        sortModel.forEach(element => {
          let elementDetails = this1.getElementIdType(element.colId);
          let sortObj = { 'colId': elementDetails.id, 'sort': element.sort };
          if (elementDetails.type == 'simple_name') {
            sortObj.colId = elementDetails.id + '_1';
          }
          formattedSortModel.push(sortObj);
        });
      } else {
        let sortObj = { 'colId': 'id', 'sort': 'desc' };
        formattedSortModel.push(sortObj);
      }
      console.log('Time: set sortmodel', moment().format('LTS') + ' ' + moment().millisecond());
      let formattedFilterModel = [];
      if (this1.customSearch == false) {
        this1.searchFieldText = '';
        // this1.filterEnabledFields.forEach(element => {
        //   if(element.enableAutoSelect == true) {
        //     $('#checkbox'+element.fieldId).prop("checked", true);
        //   } else {
        //     $('#checkbox'+element.fieldId).prop("checked", false);
        //   }
        // });
        // this1.selectedSearchFields = [];
      }
      if (this1.searchFieldText != '' && this1.selectedSearchFields.length > 0) {
        let filterFields = this1.reportFields.filter(x => x.allowFilter == true && x.valueType != 'checkbox');
        this1.selectedSearchFields.forEach(element => {
          let filterObj = {
            'fieldName': element.fieldName,
            'fieldId': element.fieldId,
            'valueType': element.valueType,
            'filterCondition': 'OR'
          };
          if (element.valueType == 'number') {
            filterObj['filterDetail'] = { type: 'equals', filter: this1.searchFieldText, filterType: 'number' };
          } else if (element.valueType == 'date') {
            filterObj['filterDetail'] = { type: 'equals', dateFrom: this1.searchFieldText, filterType: 'date' };
          } else if (element.valueType == 'radio' || element.valueType == 'checkbox' || element.valueType == 'select') {
            filterObj['filterDetail'] = { type: 'contains', filter: this1.searchFieldText, filterType: 'text' };
          } else {
            filterObj['filterDetail'] = { type: 'contains', filter: this1.searchFieldText, filterType: 'text' };
          }
          formattedFilterModel.push(filterObj);
        });
      }
      
      if (Object.keys(filterModel).length > 0) {
        let filterModelKeys = Object.keys(filterModel);
        let widgetFilterKeys = filterModelKeys.filter(x => x == this1.selectedWidgetField);
        let otherModelKeys = filterModelKeys.filter(x => x != this1.selectedWidgetField);
        if (otherModelKeys.length > 0 && this1.savedSearchText === '') {
          this1.searchFieldText = '';
          formattedFilterModel = [];
          filterModelKeys = filterModelKeys;
        } else if (widgetFilterKeys.length > 0) {
          filterModelKeys = widgetFilterKeys;
        }
        filterModelKeys.forEach(element => {
          if (filterModel[element].filter) {
            filterModel[element].filter = filterModel[element].filter.trim();
          }
          let filterObj = {
            'fieldName': element, 'filterDetail': filterModel[element]
          };
          /**while editing any entry from filtered data it should return to the same resultant page after completing editing. that time we take field id form report fields. */
          let elementDetails = this1.getElementIdType(element);
          filterObj['fieldId'] = elementDetails.id;
          filterObj['valueType'] = elementDetails.type;
          if(element == this1.selectedWidgetField && this1.metaData.formApiEndpoint && this1.metaData.formApiEndpoint != '') {
            filterObj['widgetField'] = 1;
          }
          formattedFilterModel.push(filterObj);
        });
      }
      let changeCount = true;
      if(formattedFilterModel.length > 0) {
        changeCount = false;
      }
      if(this1.clickhistoryFilter == true) {
        changeCount = true;
        formattedFilterModel = [];
      }
      if (
        this1.metaData.advanceSearchCallback === "refillAdvanceSearch" &&
        (!formattedFilterModel.length ||
          (formattedFilterModel.length === 1 &&
            formattedFilterModel[0].fieldName === this1.selectedWidgetField))
      ) {
        showDateForCount = true;
        const processDate = moment()
          .set({ hour: 23, minute: 59, second: 59, millisecond: 0 })
          .utc()
          .format(CONSTANTS.dateRangePicker.dateFormat);
        const searchData = this1.getElementIdType('processingDate');
        formattedFilterModel.push({
          fieldName: 'processingDate',
          fieldId: searchData.id,
          valueType: searchData.type,
          filterDetail: {
            type: FilterOptions.LESSTHANOREQUAL,
            dateFrom: moment(new Date(processDate)).format(
              CONSTANTS.dateRangePicker.dateFormat
            ),
            dateTo: null,
            filterType: FilterTypes.DATE,
          },
          filterCondition: 'AND',
        });
      }
      this1.clickhistoryFilter = false;
      let index = this1.reportFields.findIndex(x => x.fieldName == 'PatientId');
      if(index != -1) {
        let filterObject = {
          'fieldName': this1.reportFields[index].fieldName,
          'fieldId': this1.reportFields[index].fieldId,
          'valueType': this1.reportFields[index].valueType,
          'filterCondition': 'AND',
          'filterDetail' : {type: 'equals', filter: this1.userPatientId, filterType: 'text'}
        }
        if(this1.metaData.formApiEndpoint && this1.metaData.formApiEndpoint != '') {
          filterObject['widgetField'] = 1;
        }
        formattedFilterModel.push(filterObject);
      }
      /**Count of elements used for filtering having element type radio/checkbox/select */
      let count = formattedFilterModel.filter(x => x.valueType == 'radio' || x.valueType == 'checkbox' || x.valueType == 'select').length;
      let j = 1;
      if (count > 0) {
        formattedFilterModel.forEach(element => {
          if (element.valueType == 'radio' || element.valueType == 'checkbox' || element.valueType == 'select') {
            if (this1.formMetaData && this1.formMetaData.length > 0) {
              /**Get meta data from api */
              const data = this1.formMetaData.filter(x => x.tid == element.fieldId)[0];

              const options = data['options'];
              const hasOtherOption = data['hasOtherOption'] ? data['hasOtherOption'] : 0;
              let filterOptions = [];
              options.forEach(elem => {
                const optionValue = elem.optionValue.toLowerCase();
                const filterValue = element.filterDetail.filter.toLowerCase();
                element.otherExist = hasOtherOption;
                if (hasOtherOption == 1) {
                  element.valueOther = filterValue;
                }
                if (element.valueType == 'radio' || element.valueType == 'select' || element.valueType == 'checkbox') {
                  if (element.filterDetail.type == 'contains') {
                    if (Array.isArray(element.filterDetail.filter.split(",")) == true) {
                      let filterValueArray = element.filterDetail.filter.split(",");
                      filterValueArray.forEach(opt => {
                        if (optionValue.includes(opt.toLowerCase())) {
                          filterOptions.push(elem.optionId);
                        }
                      });
                    } else {
                      if (optionValue.includes(filterValue)) {
                        filterOptions.push(elem.optionId);
                      }
                    }
                  }
                  if (element.filterDetail.type == 'equals') {
                    if (Array.isArray(element.filterDetail.filter.split(",")) == true) {
                      let filterValueArray = element.filterDetail.filter.split(",");
                      filterValueArray.forEach(opt => {
                        if (optionValue == opt.toLowerCase()) {
                          filterOptions.push(elem.optionId);
                        }
                      });
                    } else {
                      if (optionValue == filterValue) {
                        filterOptions.push(elem.optionId);
                      }
                    }
                  }
                }
              });
              if (filterOptions.length == 0 && hasOtherOption == 0) {
                filterOptions.push(new Date().getTime());
              }
              let filterdetail = element.filterDetail;
              element.filterDetail = { type: filterdetail.type, filter: filterOptions.join(','), filterType: filterdetail.filterType };
              if (j == count) {
                console.log('Time: set filter model for radio,checkbox,select', moment().format('LTS') + ' ' + moment().millisecond());
                if (this1.metaData.formApiEndpoint && this1.metaData.formApiEndpoint != '') {
                  getDataSourceUsingApi(params, dataCount, changeCount, initialLoad, this1, formattedFilterModel, formattedSortModel, showDateForCount);
                } else {
                  getDataSource(params, start, this1, formattedFilterModel, formattedSortModel);
                }
              }
              j++;
            } else {
              this1._workListService.getMachformFields(this1.formId, element.fieldId).refetch().then(({ data: response }) => {
                const options = response['getFormElementDetails'].options;
                let filterOptions = [];
                options.forEach(elem => {
                  const optionValue = elem.optionValue.toLowerCase();
                  const filterValue = element.filterDetail.filter.toLowerCase();
                  element.otherExist = response['getFormElementDetails'].hasOtherOption;
                  if (response['getFormElementDetails'].hasOtherOption == 1) {
                    element.valueOther = filterValue;
                  }
                  if (element.valueType == 'radio' || element.valueType == 'select' || element.valueType == 'checkbox') {
                    if (element.filterDetail.type == 'contains') {
                      if (Array.isArray(element.filterDetail.filter.split(",")) == true) {
                        let filterValueArray = element.filterDetail.filter.split(",");
                        filterValueArray.forEach(opt => {
                          if (optionValue.includes(opt.toLowerCase())) {
                            filterOptions.push(elem.optionId);
                          }
                        });
                      } else {
                        if (optionValue.includes(filterValue)) {
                          filterOptions.push(elem.optionId);
                        }
                      }
                    }
                    if (element.filterDetail.type == 'equals') {
                      if (Array.isArray(element.filterDetail.filter.split(",")) == true) {
                        let filterValueArray = element.filterDetail.filter.split(",");
                        filterValueArray.forEach(opt => {
                          if (optionValue == opt.toLowerCase()) {
                            filterOptions.push(elem.optionId);
                          }
                        });
                      } else {
                        if (optionValue == filterValue) {
                          filterOptions.push(elem.optionId);
                        }
                      }
                    }
                  }
                });
                if (filterOptions.length == 0 && response['getFormElementDetails'].hasOtherOption == 0) {
                  filterOptions.push(new Date().getTime());
                }
                let filterdetail = element.filterDetail;
                element.filterDetail = { type: filterdetail.type, filter: filterOptions.join(','), filterType: filterdetail.filterType };
                if (j == count) {
                  if (this1.metaData.formApiEndpoint && this1.metaData.formApiEndpoint != '') {
                    getDataSourceUsingApi(params, dataCount, changeCount, initialLoad, this1, formattedFilterModel, formattedSortModel, showDateForCount);
                  } else {
                    getDataSource(params, start, this1, formattedFilterModel, formattedSortModel);
                  }
                }
                j++;
              });
            }
          }
        });
      } else {
        if (this1.metaData.formApiEndpoint && this1.metaData.formApiEndpoint != '') {
          getDataSourceUsingApi(params, dataCount, changeCount, initialLoad, this1, formattedFilterModel, formattedSortModel, showDateForCount);
        } else {
          getDataSource(params, start, this1, formattedFilterModel, formattedSortModel);
        }
      }

    }
  };
}
function getDataSourceUsingApi(params, dataCount, changeCount, initialLoad, this1, formattedFilterModel, sortModel, showDateForCount = false) {
  console.log('Time: call get api call function', moment().format('LTS') + ' ' + moment().millisecond());
  let parameters = [];

  let start = params.request.startRow;
  let end = params.request.endRow;
  parameters.push('formID=' + this1.formId);
  parameters.push('start=' + start);
  parameters.push('end=' + end);
  if (this1.selectedWidgetField != '') {
    let index = this1.reportFields.findIndex(x => x.fieldName == this1.selectedWidgetField);
    parameters.push('widgetField=' + this1.reportFields[index].fieldId);
  }
  let dataObj;
  dataObj = { 'meta': true, 'count': dataCount, 'timeStamp': false, 'totalCount': true };
  var userdata = JSON.parse(this1._structureService.userDetails);
    let index = this1.reportFields.findIndex(x=> x.fieldName == 'SiteId');
    if(index != -1) {
      this1.associatedSiteElementId = this1.reportFields[index].fieldId;
    }
    if(this1.metaData.enableSiteAccess == true  && userdata.config.enable_multisite && userdata.config.enable_multisite == 1) {  
    dataObj['sitevlue'] = this1.selectedSiteIds;
    dataObj['multisite'] = true;
    dataObj['fieldIdSite'] = this1.associatedSiteElementId;
  }
  if (this1.hasUniqueId && this1.uniqueClass != '') {
    dataObj['uniqueid'] = true;
  }
  if (formattedFilterModel.length > 0) {
    dataObj['filterModel'] = formattedFilterModel;
  }
  if (sortModel.length > 0) {
    dataObj['sortModel'] = sortModel;
  }
  if (localStorage.getItem('worklistMetaData') && localStorage.getItem('worklistMetaData') != '') {
    let worklistMetaArray = JSON.parse(localStorage.getItem('worklistMetaData'));
    let index = worklistMetaArray.findIndex(x => x.worklistId == this1.worklistid);
    if (index > -1) {
      dataObj['meta'] = false;
    }
  }
  if (showDateForCount) {
    dataObj['considerDatesForCount'] = true;
  }
  console.log('Time: start data api call', moment().format('LTS') + ' ' + moment().millisecond());
  this1._workListService.getFormWorklistDataUsingAPI(this1.metaData.formApiEndpoint, parameters.join('&'), dataObj).then((data) => {
    this1.resetAppliedFilterNotification(formattedFilterModel);
    let rowData: any = data.data;
    rowData = data['data']
    console.log('Time: get data api result', moment().format('LTS') + ' ' + moment().millisecond());
    //this1.formMetaData = data['metadata'];
    if (rowData.length > 0) {
      if (this1.metaData.worklistType == 'single') {
        this1.rowData = rowData;
        console.log('Time: set data to ag grid', moment().format('LTS') + ' ' + moment().millisecond());
      } else {
        rowData.forEach(element => {
          if (element.formData && element.formData.length > 0) {
            this1.machFormIds = [];
            let formDataList = [];
            let objKeys = Object.keys(element.formData);
            objKeys.forEach(keys => {
              if (element.formData[keys]) {
                let formObj = {};
                let i = 1;
                let obj = {};
                element.formData[keys].forEach(elem => {
                  let labelText = '';
                  if (elem.label.lastIndexOf('{{') == -1) {
                    labelText = elem.label;
                    if (i == 1) {
                      obj[elem.label] = { 'id': elem.element_id, 'type': elem.element_type };
                    }
                  } else {
                    let key = elem.label.substring(elem.label.lastIndexOf('{{') + 2, elem.label.lastIndexOf('}}'));
                    labelText = key;
                    if (i == 1) {
                      obj[key] = { 'id': elem.element_id, 'type': elem.element_type };
                    }
                  }
                  if (elem.element_type == 'date' && elem.value && elem.value != null && elem.value != '') {
                    let newDate = new Date(Number(elem.value) * 1000);
                    formObj[labelText] = moment.utc(newDate).format('MM/DD/YYYY');
                  } else {
                    formObj[labelText] = elem.value;
                  }
                });
                this1.machFormIds.push({ 'type': 'child' });
                this1.machFormIds.push(obj);
                i++;
                formObj['submissionID'] = Number(keys);
                formDataList.push(formObj);
              }
            });
            element.callRecords = formDataList;
          } else {
            element.callRecords = element.day_wise_time_range;
          }
        });
        this1.rowData = rowData;
      }
      this1.disableWidget = false;
      this1.gridApi.hideOverlay();
      console.log(dataCount);
      if (dataCount == true && changeCount == true) {
        this1.widgetCounts = [];
        let totalcount = 0;
        let obj = {};
        data['filterCount'].values.forEach(element => {
          if (element.value != null) {
            obj[element.value.toLowerCase()] = Number(element.count);
          }
          totalcount = totalcount + Number(element.count);
        });
        obj['all'] = totalcount;
        this1.widgetCounts.push(obj);
        console.log(this1.widgetCounts);
        this1.setDefaultWidgetConfigServerSide('', '');
      }
      console.log('initialLoad', initialLoad);
      if (localStorage.getItem('worklistMetaData') && localStorage.getItem('worklistMetaData') != '') {
        let worklistMetaArray = JSON.parse(localStorage.getItem('worklistMetaData'));
        let index = worklistMetaArray.findIndex(x => x.worklistId == this1.worklistid);
        if (index == -1) {
          worklistMetaArray.push({ 'worklistId': this1.worklistid, 'worklistMeta': data['metadata'] });
          localStorage.setItem('worklistMetaData', JSON.stringify(worklistMetaArray));
          this1.formMetaData = data['metadata'];
        } else {
          this1.formMetaData = worklistMetaArray[index].worklistMeta;
        }
      } else {
        let worklistMeta = [{ 'worklistId': this1.worklistid, 'worklistMeta': data['metadata'] }];
        localStorage.setItem('worklistMetaData', JSON.stringify(worklistMeta));
        this1.formMetaData = data['metadata'];
      }
      if (this1.metaData.disableInlineEdit == false) {
        this1.setColumnDefinitionWithOptions(this1.formMetaData).then((res) => console.log(res));
      }
      var server = {
        success: true,
        rows: this1.rowData,
      }
      let res = server;
      if (res.success) {
        const rows = res.rows;
        let lastRow = -1;
        if (res.rows.length < this1.cacheBlockSize) {
          lastRow = params.request.startRow + res.rows.length;
        }
        console.log('Time: complete loading ' + moment().millisecond());
        params.successCallback(rows, lastRow);
      } else {
        params.failCallback();
      }
    } else {
      this1.disableWidget = false;
      this1.rowData = [];
      this1.suppressNoRow = false;
      $('.ag-row-stub').html('<p style="margin-top:7px!important;text-align: center!important;">There are no data available.</p>');
    }    
  }).catch((ex) => {

  });
}
function getDataSource(params, start, this1, formattedFilterModel, sortModel) {
  if (start == 0) {
    let formFieldArray = [];
    this1.dashboardWidgets.forEach(element => {
      if (element.formField != 'all') {
        formFieldArray = this1.reportFields.filter(x => x.fieldName == element.formField);
      }
    });
    if (formFieldArray.length > 0) {

      this1._workListService.getWidgetCount(this1.formId, formFieldArray[0].fieldId,
        this1.uniqueClass).refetch().then(({ data: response }) => {
          this1.widgetCounts = [];
          let totalcount = 0;
          let obj = {};
          let index = this1.dashboardWidgets.findIndex(x => x.widgetValue == 'All');
          let allWidgetConfig = (index != -1) ? this1.dashboardWidgets[index] : {};
          response['getTotalBasedOnStatus'].forEach(element => {
            if (allWidgetConfig.customWidgetCount && allWidgetConfig.customWidgetCount == true) {
              let selectWidgetData = allWidgetConfig.selectWidget.split(",");
              let widgetIndex = selectWidgetData.indexOf(element.option);
              // return element.selectWidget.length; 
              if (widgetIndex != -1) {
                totalcount = totalcount + element.total;
              }
            } else {
              totalcount = totalcount + element.total;
            }
            // });
            if (element.option && element.option != null) {
              let option = element.option.toLowerCase();
              obj[option] = element.total;
            }
          });
          obj['all'] = totalcount;
          this1.widgetCounts.push(obj);
          this1.setDefaultWidgetConfigServerSide('', '');
        });

    }

  }
  this1._workListService.getWorkListFormDataWithFilter(this1.formId, this1.uniqueClass, start, this1.cacheBlockSize, this1.formFieldFrom, formattedFilterModel, sortModel).refetch().then(({ data: response }) => {
    this1.resetAppliedFilterNotification(formattedFilterModel);
    if (this1.uniqueClass !== '') {
      this1.formRowData = response['getFormDataWithUniqueIDWithFilters'];
    } else {
      this1.formRowData = response['getFormDataWithFilters'];
    }
    let totalCount = 0;
    if (this1.formRowData && this1.formRowData.length > 0) {
      this1.machFormIds = [];
      this1.formDataList = [];
      let i = 1;
      let obj = {};
      this1.formRowData.forEach(element => {
        const formObj = {};
        totalCount = element.total;
        element.elements.forEach(elem => {
          formObj[elem.labelText] = elem.value;
          if (elem.valueType == 'radio' && elem.valueOther != '' && elem.value == '') {
            formObj[elem.labelText] = elem.valueOther;
          }
          if (elem.valueType == 'checkbox' && elem.valueOther != '') {
            if (formObj[elem.labelText] == '') {
              formObj[elem.labelText] = elem.valueOther;
            } else {
              formObj[elem.labelText] = formObj[elem.labelText] + ',' + elem.valueOther;
            }
          }
          if (i == 1) {
            obj[elem.labelText] = { 'id': elem.tid, 'elementType': elem.valueType, 'otherExist': elem.OtherExists };
          }
          if (elem.valueType == 'date') {
            if (elem.timestampForDate && elem.timestampForDate != null && elem.timestampForDate != '') {
              let newDate = new Date(Number(elem.timestampForDate));
              formObj[elem.labelText] = moment.utc(newDate).format('MM/DD/YYYY');
            } else {
              formObj[elem.labelText] = '';
            }
          }
        });
        formObj['submissionID'] = element.submissionID;
        formObj['slno'] = i;
        formObj['action'] = '';
        this1.formDataList.push(formObj);
        i++;
      });
      this1.machFormIds.push({ 'type': 'parent' });
      this1.machFormIds.push(obj);

      this1.rowData = this1.formDataList;
      this1.gridApi.hideOverlay();
      this1.disableWidget = false;
    } else {
      this1.rowData = [];
      this1.suppressNoRow = false;
      this1.gridApi.showNoRowsOverlay();
      this1.disableWidget = false;
      $('.ag-row-stub').html('<p style="margin-top:7px!important;text-align: center!important;">There are no data available.</p>');
    }

    var server = {
      success: true,
      rows: this1.rowData,
    }
    let res = server;
    let lastRowValue = params.request.startRow + res.rows.length;
    if (res.success) {
      const rows = res.rows;
      let lastRow = -1;
      if (res.rows.length < this1.cacheBlockSize || (totalCount != 0 && lastRowValue == totalCount)) {
        lastRow = params.request.startRow + res.rows.length;
      }
      params.successCallback(rows, lastRow);
    } else {
      params.failCallback();
    }
  });
}
function autosizeHeaders(event) {
  const MIN_HEADER_HEIGHT = 35;
  if (event.finished !== false) {
    event.api.setGroupHeaderHeight(MIN_HEADER_HEIGHT);
    event.api.setHeaderHeight(MIN_HEADER_HEIGHT);
    let headerCells = Array.from(document.querySelectorAll('#grid-wrapper .ag-header-cell-label'));
    let minHeight = MIN_HEADER_HEIGHT;
    headerCells.forEach((element) => {
      minHeight = Math.max(minHeight, element.scrollHeight);
    });
    event.api.setHeaderHeight(minHeight);
    const groupCells = Array.from(document.querySelectorAll('#grid-wrapper .ag-header-group-cell-label'));
    let minGroupHeight = MIN_HEADER_HEIGHT;
    groupCells.forEach((element) => {
      minGroupHeight = Math.max(minGroupHeight, element.scrollHeight);
    });
    event.api.setGroupHeaderHeight(minGroupHeight);
  }
}

function getDataSourceGraphqlApi(params,url, newQuery, variables, this1) {
  
	this1._pahService.getWorklistDataUsingGraphQLAPI(url, newQuery, variables, this1.prepareHeaderVariables()).then((data) => {
    this1.resetAppliedFilterNotification(variables.filter);
    let rowData: any;
    let keyArray :any;
  if (this1.metaData.parameters.includes('getSessionTenant')) {
    keyArray = Object.keys(data['data']['getSessionTenant']);
    rowData = JSON.parse(JSON.stringify(data['data']['getSessionTenant'][keyArray[0]].data)); 
   console.log(rowData);
   this1.widgetData = data['data']['getSessionTenant'][keyArray[0]].filterCount ? data['data'][keyArray[0]].filterCount : [];
  } else {
      keyArray = Object.keys(data['data']);
    rowData = JSON.parse(JSON.stringify(data['data'][keyArray[0]].data));
    this1.widgetData = data['data'][keyArray[0]].filterCount ? data['data'][keyArray[0]].filterCount : [];
    }
		if (rowData.length == 0) {
			this1.rowData = [];
			this1.gridApi.showNoRowsOverlay();
		} else {
      this1.gridApi.hideOverlay();
			if (this1.metaData.worklistType == 'single') {
				this1.rowData = rowData;
			} else {
				rowData.forEach(element => {
					if (element.formData && element.formData.length > 0) {
						this1.machFormIds = [];
						let formDataList = [];
						let objKeys = Object.keys(element.formData);
						objKeys.forEach(keys => {
							if (element.formData[keys]) {
								let formObj = {};
								let i = 1;
								let obj = {};
								element.formData[keys].forEach(elem => {
									let labelText = '';
									if (elem.label.lastIndexOf('{{') == -1) {
										labelText = elem.label;
										if (i == 1) {
											obj[elem.label] = { 'id': elem.element_id, 'type': elem.element_type };
										}
									} else {
										let key = elem.label.substring(elem.label.lastIndexOf('{{') + 2, elem.label.lastIndexOf('}}'));
										labelText = key;
										if (i == 1) {
											obj[key] = { 'id': elem.element_id, 'type': elem.element_type };
										}
									}
									if (elem.element_type == 'date' && elem.value && elem.value != null && elem.value != '') {
										let newDate = new Date(Number(elem.value) * 1000);
										formObj[labelText] = moment.utc(newDate).format('MM/DD/YYYY');
									} else {
										formObj[labelText] = elem.value;
									}
								});
								this1.machFormIds.push({ 'type': 'child' });
								this1.machFormIds.push(obj);
								i++;
								formObj['submissionID'] = Number(keys);
								formDataList.push(formObj);
							}
						});
						element.callRecords = formDataList;
					} else {
						element.callRecords = element.day_wise_time_range;
					}
				});
				this1.rowData = rowData;
			}
		}
		this1.setDefaultWidgetConfig(this1.rowData);
		var server = {
			success: true,
			rows: this1.rowData,
		}
		let res = server;
		if (res.success) {
			const rows = res.rows;
			let lastRow = -1;
			if (res.rows.length < this1.cacheBlockSize) {
				lastRow = params.request.startRow + res.rows.length;
			}
			params.successCallback(rows, lastRow);
		} else {
			params.failCallback();
		}
	});
}
