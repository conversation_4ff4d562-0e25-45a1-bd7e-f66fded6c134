import { Component, OnInit, EventEmitter, Output } from '@angular/core';
import { Router, ActivatedRoute, Params, NavigationEnd } from '@angular/router';
import { StructureService } from '../structure.service';
import { PahService } from './pah.service';
import { Subject } from 'rxjs';
import { WorkListService } from '../../structure/worklists/worklist.service';
import { SharedService } from '../../structure/shared/sharedServices';
import { ToolTipService } from '../tool-tip.service';
import { isBlank } from 'app/utils/utils';
import { PatientStatus } from 'app/constants/constants';

declare var $: any;

@Component({
  selector: 'app-patient-activity-hub',
  templateUrl: './patient-activity-hub.component.html',
  styleUrls: ['./patient-activity-hub.component.css'],

})
export class PatientActivityHubComponent implements OnInit {
  //Used For know any change in profile Child Component
  public static returned: Subject<any> = new Subject();
  eventsSubject: Subject<void> = new Subject<void>();
  // The Variable Passing to ProfileComponent
  userDetails;
  dynamicData: any = [];
  // this Variables
  patientList = [];
  userPatientId;
    siteId;
  hideSiteSelection:Boolean = false;
  searchText;
  optionShow;
  totalUserCount = 0;
  statusMessage;
  hideLoadMore = true;
  baseuri: any = '';
  userDatam: any;
  public showHide = true;
  emailselected;
  avatarselected;
  optionShowMenu;
  changeArrowButton = false;
  dynamicTabs;
  metaArray: any = [];
  profileTabs: any = [];
  patientId = '';
  dataLoadingMsg = true;
  showpat_msg=false;
  offset;
  limit;
  totalPatientCount;
  searchPatient = '';
  resetPatient = false;
  securityDetails;
  securityChecking;
  manageConfig;
  userDataConfig;
  pahWorklistId = localStorage.getItem('pahWorklistId') ? localStorage.getItem('pahWorklistId') : '';
  enabledMultisite : boolean = false;
  showFormSend;
  //Constructor To initialize Variables or imports 
  constructor(private router: Router, private route: ActivatedRoute, public _structureService: StructureService,
    private _pahService: PahService, public _worklistService: WorkListService,
    private _sharedService: SharedService, private toolTipService: ToolTipService) {
    //Understands a change in ProfileComponent occurs and changes Corresponding Data here!
    this.optionShow = "Profile";
  }
  // First executes on Load
  ngOnInit() {
    var activityData = {
      activityName: "Tab Navigation:",
      activityType: "Tab access",
      activityDescription: "Current Page PAH - .Tab - :" + this.optionShow,
    };
    this._structureService.trackActivity(activityData);



    this.securityDetails = !isBlank(this._structureService.userDetails) ? JSON.parse(this._structureService.userDetails) : {};
    this.securityChecking= this.securityDetails.accessSecurityEnabled;
    this.manageConfig = !isBlank(this._structureService.userDataConfig) ? JSON.parse(this._structureService.userDataConfig) : {};
    this.enabledMultisite = (this.securityDetails.config.enable_multisite === "1")?true: false;
    console.log(this.userDataConfig);
    this._structureService.activePatientActivityDetails = [];
    $(document).ready(function () {
      $('#sidebarCollapse').on('click', function () {
        $('#sidebar').toggleClass('active');
      });
      function sticktothetop() {
        var window_top = $(window).scrollTop();
        if($('#stick-here').length){
          var top = $('#stick-here').offset().top;
          if (window_top > top) {
              document.getElementById('stickThis' ).style.display = 'block';
              $('#stickThis').addClass('stick');
              $('#stick-here').height($('#stickThis').outerHeight());
              $('#stickThis').width($('.tab-content').outerWidth());
          } else {
              $('#stickThis').removeClass('stick');
              $('#stick-here').height(0);
                document.getElementById('stickThis' ).style.display = 'none';
          }
        }
      }
      $(function() {
        $(window).scroll(sticktothetop);
        if($('#stick-here').length){
          sticktothetop();
          }
      });
    });
    this.route.params.subscribe((params: Params) => {
      if (params.patientId) {
        this.patientId = params.patientId;
        //this.dynamicData = { "tabName": 'Profile', "reloadTab": false };
        //this._structureService.activePatientActivityHub = false;
        this.updateActivityHub(params.patientId);
      }
    });
    this.baseuri = this._structureService.serverBaseUrl;
    PatientActivityHubComponent.returned.subscribe(res => {
      if (res)
      if(Object.keys(res).indexOf('updateSite') != -1) {
        this.userDatam = JSON.parse(JSON.stringify(this.userDatam));
        this.userDatam.siteName = res['siteName'];
        this.userDatam.siteId = res['siteId'];
        let siteIds = this.siteId.split(',');
        if(siteIds.indexOf(res['siteId'].toString()) == -1) {
          let index = this.patientList.findIndex(x => x.id == res['patientId']);
          if(index != -1){
            this.patientList.splice(index,1);
            this.totalPatientCount = this.patientList.length;
            if(this.totalPatientCount == 0){
              this.hideLoadMore = true;
              this.statusMessage = "No Patient Found";
              this.resetPatient = true;
            } else {
              this.updateActivityHub(this.patientList[0].id);
            }
          }
        }
      } else {
        this.userDatam = res;
        this.userDetails = res;
        localStorage.setItem('userDetail-pah', this.userDetails.id);
        if(this.patientList.length > 0) {
          let index = this.patientList.findIndex(x => x.id == this.userDetails.id);
          if(index != -1) {
            this.patientList[index].status = this.userDetails.status;
            this.patientList[index].displayName = this.userDetails.displayName;
            this.patientList[index].dateOfBirth = this.userDetails.dateOfBirth;
          }
        }
        this.emailselected = this._pahService.validateEmail(this.userDatam.emails[0].value) ? this.userDatam.emails[0].value : '';
      }
    });

    if(Object.keys(this._sharedService.patientActivityHubTab).length > 0) {
      this.metaArray = this._sharedService.patientActivityHubTab['mainTabs'];
      this.profileTabs = this._sharedService.patientActivityHubTab['profileTabs'];
    } else {
      this._structureService.getShowInPahTabs().then((data) => {
        if (data) {
          this.dynamicTabs = data['getSessionTenant'].formWorklists;
          console.log('this.dynamicTab', this.dynamicTabs);
          const userDetails = JSON.parse(this._structureService.userDetails);
          let privileges = Object.keys(this._structureService.privileges);
          if(this.dynamicTabs && this.dynamicTabs.length > 0)
          this.dynamicTabs.forEach(element => {
            let flag = false;
            let newJson = element.description;
            newJson = newJson.replace(/'/g, '"');
            let metaData = JSON.parse(newJson);
            metaData['id'] = element.id;
            if (element.active) {
              if (((metaData.visibleToRoles && metaData.visibleToRoles.split(',').indexOf(userDetails.roleId) !== -1) || (metaData.allowCrossTenant == true && metaData.visibleToOtherRoles && metaData.visibleToOtherRoles.split(',').indexOf(userDetails.roleId) !== -1))) {
                if (metaData.enablePrivilegedAccess && (metaData.privileges && metaData.privileges != '')) {
                  let privilegeArray = metaData.privileges.split(',');
                  let check = 0;
                  privilegeArray.forEach(element => {
                    if (privileges.indexOf(element) != -1) {
                      if (this._structureService.privileges[element] == true) {
                        check = 1;
                      }
                    }
                  });
                  if (check == 1) {
                    flag = true;
                  }
                } else {
                  flag = true;
                }
              } else {
                if (metaData.visibleToRoles == '') {
                  flag = true;
                } else if (metaData.visibleToOtherRoles == '') {
                  flag = true;
                }
              }
              if(flag == true) {
                if(metaData.showUnderProfile == true) {
                  this.profileTabs.push(metaData);
                } else {
                  this.metaArray.push(metaData);
                }
              }
            }
          });
          this.metaArray.sort(function (a, b) {
            if (a.tabIndex < b.tabIndex) { return -1; }
            if (a.tabIndex > b.tabIndex) { return 1; }
            return 0;
          });
          if(this.profileTabs.length > 1) {
            this.profileTabs.sort(function (a, b) {
              if (a.tabIndex < b.tabIndex) { return -1; }
              if (a.tabIndex > b.tabIndex) { return 1; }
              return 0;
            });
          }
          this._sharedService.patientActivityHubTab = {'mainTabs': this.metaArray, 'profileTabs' : this.profileTabs};
        }
      });
    }
    
    if (this._structureService.activePatientActivityHub == true) {
      this.optionShow = this._structureService.activePatientActivityHubTabId;
      this.userPatientId = this._structureService.activePatientActivityHubPatientId;
    }    
  }

  get isSendFormEnable() {
    const status = this.userDatam.status;
    const isStatusString = typeof status === 'string';
    const isActiveOrVirtualUser = isStatusString && (status.toLowerCase() === PatientStatus.ACTIVE.toLowerCase() || status.toLowerCase() === PatientStatus.VIRTUAL_USER.toLowerCase());

    return this._structureService.userPrevilages.FillStructuredForms && 
           this.manageConfig.enable_forms === '1' && 
           isActiveOrVirtualUser;
  }

  setPatientList() {
    this.limit = 10;
    this.offset = 0;
    this._structureService.getPatientListLazy(this.limit, this.offset, 'displayName', 'asc', '', true,"","",this.siteId).then((data) => {
      
      var activityData = {
        activityName: "Search Patient list default:",
        activityType: "Search patient information",
        activityDescription: "Search Patient default list:" ,
      };
      this._structureService.trackActivity(activityData);
      this.patientList = JSON.parse(JSON.stringify(data['getSessionTenant'].patientUsers));
      this.totalPatientCount = data['getSessionTenant'].patientUsersPagination.totalCount ? data['getSessionTenant'].patientUsersPagination.totalCount : 0;
      this.statusMessage = "";
      if (typeof (this.totalPatientCount) == "number") {
        this.totalUserCount = this.totalPatientCount;
      }
      if (this.totalUserCount == 0) {
        this.hideLoadMore = true;
        this.statusMessage = "No Patient Found";
      } else if (this.totalPatientCount > 10) {
        this.hideLoadMore = false;
      }
      if ((this.offset + this.limit) >= this.totalUserCount) {
        this.hideLoadMore = true;
      }
      this.patientList = this.patientList.filter(item => {
        if (item && item.displayName && (item.displayName).toLowerCase().indexOf('cancelled') == -1) {
          return true;
        }
      });
      this.dynamicData = this._structureService.activePatientActivityHubFilterPahDetails;
      if (this.patientList !== null) {
        if (this._structureService.activePatientActivityHub == true) {
          this.userPatientId = this._structureService.activePatientActivityHubPatientId;
        } else {
          if (this.patientList.length > 0) {
            this.userPatientId = this.patientList[0].id;
          }
        }
        if (this.patientId != '') {
          this.updateActivityHub(this.patientId);
        } else {
          if (this.patientList.length > 0) {
            this.updateActivityHub(this.userPatientId);
          }
          this.patientId = '';
        }
      }
    });
  }
  loadMorePatient() {
    if ((this.patientList.length - 1) < this.totalPatientCount) {
      this.offset = this.offset + this.limit;
      let patientList = [];
      this.hideLoadMore = true;

      var activityData = {
        activityName: "Search patient information",
        activityType: "Search patient information",
        activityDescription: "Load More patient information with search text:" + this.searchPatient
      };
      this._structureService.trackActivity(activityData);


      this._structureService.getPatientListLazy(this.limit, this.offset, 'displayName', 'asc', this.searchPatient, true,"","",this.siteId).then((data) => {
        this.hideLoadMore = false;
        if ((this.offset + this.limit) >= this.totalUserCount) {
          this.hideLoadMore = true;
        }
        patientList = JSON.parse(JSON.stringify(data['getSessionTenant'].patientUsers));
        patientList = patientList.filter(item => {
          if (item && item.displayName && (item.displayName).toLowerCase().indexOf('cancelled') == -1) {
            return true;
          }
        });
        let allList = this.patientList;
        this.patientList = allList.concat(patientList);
      });
    }
  }
  searchUser(searchText) {
    this.resetPatient = false;
    this.showpat_msg=false;
    this.searchPatient = searchText;
    this.hideLoadMore = true;
    this.patientList = [];
    let patientList = [];
    this.offset = 0;

    var activityData = {
      activityName: "Search patient information",
      activityType: "Search patient information",
      activityDescription: "Search patient information with search text:" + searchText
    };
    this._structureService.trackActivity(activityData);

    this._structureService.getPatientListLazy(this.limit, this.offset, 'displayName', 'asc', searchText, true,"","",this.siteId).then((data) => {
      this.hideLoadMore = false;
      this.statusMessage = '';
      this.totalPatientCount = data['getSessionTenant'].patientUsersPagination.totalCount ? data['getSessionTenant'].patientUsersPagination.totalCount : 0;
      if (typeof (this.totalPatientCount) == 'number') {
        this.totalUserCount = this.totalPatientCount;
      }
      if (this.totalUserCount == 0 || this.totalPatientCount == null) {
        this.hideLoadMore = true;
        this.statusMessage = 'No Patient Found';
        this.resetPatient = true;
      } else if (this.totalUserCount > 10) {
        this.hideLoadMore = false;
        this.resetPatient = false;
        this.showpat_msg=true;
      }
      else if (this.totalUserCount > 1) {	
        this.resetPatient = false;	
        this.showpat_msg=true;	
      }
      if ((this.offset + this.limit) >= this.totalUserCount) {
        this.hideLoadMore = true;
  
      }
      patientList = JSON.parse(JSON.stringify(data['getSessionTenant'].patientUsers));
      patientList = patientList.filter(item => {
        if (item && item.displayName && (item.displayName).toLowerCase().indexOf('cancelled') == -1) {
          return true;
        }
      });
      this.patientList = patientList;
      if (this.totalUserCount == 1 )	
      {	
        this.resetPatient = true;	
      console.log('patient list');console.log(this.patientList);
      if(this.patientList && this.patientList[0] && this.patientList[0].id)	
      this.updateActivityHub(this.patientList[0].id);	
      }
    });
  }
  goBack() {
    this.router.navigate(['worklist',localStorage.getItem('pahWorklistName'),localStorage.getItem('pahWorklistId')]);
  }
  reload() {
    console.log(this.optionShow);
    if(this.optionShow.toString().toLowerCase() !=  'profile'){
      let filterPahDetails = this.metaArray.filter(x => x.id == this.optionShow);
      this.dynamicData = { "tabName": filterPahDetails[0].tabName, "pahworklistId": filterPahDetails[0].id, 'filterPahDetails': filterPahDetails, 'reloadTab': true };
      this._structureService.activePatientActivityHub = true;
      //this.userDetails = {};
      //this.userPatientId = '';
      this.userPatientId = this.patientId != '' ? this.patientId : this.userPatientId;
    } else {
      this.dynamicData = { "tabName": 'Profile', 'reloadTab': true };
      this.userPatientId = this.patientId != '' ? this.patientId : this.userPatientId;
      this.updateActivityHub(this.userPatientId);
    }
  }
  //Tabs for MainTasks
  showTabData(data) {
   
    var oldtabs=this.optionShow;
    var newtab =data;
    this.metaArray.forEach(elementtabs => {
         if(oldtabs !='Profile' && oldtabs===elementtabs.id){
          oldtabs=elementtabs.tabName;
         }
         if(newtab !='Profile' && newtab===elementtabs.id){
          newtab=elementtabs.tabName;
         }
    });
    var activityData = {
      activityName: "Tab Navigation:",
      activityType: "Tab access",
      activityDescription: "Current Page PAH - Current Tab-"+ oldtabs +" . New Tab - :" + newtab,
    };
    this._structureService.trackActivity(activityData);

    this.dynamicData = [];
    if (data == "Profile") {
      this.dynamicData = { "tabName": data, 'reloadTab': false };
    } else {
      let filterPahDetails = this.metaArray.filter(x => x.id == data);
      this.dynamicData = { "tabName": filterPahDetails[0].tabName, "pahworklistId": filterPahDetails[0].id, 'filterPahDetails': filterPahDetails , 'reloadTab': false};
      this._structureService.activePatientActivityHub = true;
    }
    this.optionShow = data;
  }
  //Tabs for right side TODO and STATUS
  showDataMenu(data) {
    this.optionShowMenu = data;
  }
  changeArrow() {
    this.changeArrowButton = !this.changeArrowButton;
    if (this.changeArrowButton == true) {
      $('.custom-content').addClass('custom-search-width');
    }
    if (this.changeArrowButton == false) {
      $('.custom-content').removeClass('custom-search-width');
    }
    $('#sidebar').toggleClass('active');
  }
  // Starts The Structure Service for Geting Clicked users in App and list in left with pipe include as searchText
  updateActivityHub(patientId) {
    this.resetPatient=false;	
    this.emailselected = '';
    this.showpat_msg=false;
    localStorage.setItem('currentProfileTab', '');
    $('.editable-field').prop("disabled", true);
    this._structureService.activePatientActivityHubFilterPahDetails = this.dynamicData;
    this._structureService.getPatient(patientId).then((data) => {
        if (data['getSessionTenant']) {
        if (data['getSessionTenant'].patientUsers.length > 0) {
          this.userDatam = data['getSessionTenant'].patientUsers[0];
          console.log(JSON.stringify(this.userDatam)+"user derails patient");

          var activityData = {
            activityName: "Updated Patient Details For Patient Activity Hub",
            activityType: "Updated Patient Details For Patient Activity Hub",
            activityDescription: "Updated Patient For Patient Activity Hub with ID:" + patientId,
          };
          this._structureService.trackActivity(activityData);
          this.emailselected = this._pahService.validateEmail(this.userDatam.emails[0].value) ? this.userDatam.emails[0].value : '';
          if (this.userDatam.avatar)
            this.avatarselected = this._structureService.getApiBaseUrl() + "citus-health/avatars/" + this.userDatam.avatar;
          else
            this.avatarselected = this._structureService.getApiBaseUrl() + "citus-health/avatars/" + "profile-pic-clinician-nurse-default.png";
          if (this.userDatam) {
            // The Variable Passing to ProfileComponent
            this.userDetails = this.userDatam;
            this.userPatientId = this.userDatam.id;
            if (this.dynamicData.pahworklistId) {
              this._structureService.activePatientActivityHub = true;
              this._structureService.activePatientActivityDetails = [];
            }
          }
        }
        if(this.optionShow.toString().toLowerCase() !=  'profile') {
          let filterPahDetails = this.metaArray.filter(x => x.id == this.optionShow);
          this.dynamicData = { "tabName": filterPahDetails[0].tabName, "pahworklistId": filterPahDetails[0].id, 'filterPahDetails': filterPahDetails, 'reloadTab': false };
        } else {
          this.dynamicData = { "tabName": 'Profile', 'reloadTab': true };
        }
      }
      this.dataLoadingMsg = false;
    });
    
  }
  getSiteIds(data){
    this.siteId = data['siteId'].toString();
    if (this.siteId == '0') {
      const userdata = JSON.parse(this._structureService.userDetails);
      const userSites = (userdata.mySites).map(a => a.id);
      this.siteId = userSites.join(",");
    }
    console.log("this.patientId",this.patientId,this.userPatientId);
    if(this._worklistService.worklistForPah == false || this.pahWorklistId == ''){
      this.setPatientList();
    }
  }
  hideDropdown(hideItem : any){
    console.log('hide emit 1 ',this.hideSiteSelection);
    this.hideSiteSelection = hideItem.hideItem;
  }

  openSendFormModal() {
    this.showFormSend = true;
    setTimeout(() => {$('#send-form-modal').modal('show');})
  }

  closeSendFormModal() {
    $('#send-form-modal') && $('#send-form-modal').modal('hide');
    this.showFormSend = false;
  }
}
