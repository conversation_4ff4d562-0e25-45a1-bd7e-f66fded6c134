import { Component, Input, Output, OnChanges, EventEmitter, SimpleChange } from '@angular/core';
import { ActivatedRoute, Params } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { isBlank, removePhoneNumberMasking, setCountryCodeFlag } from 'app/utils/utils';
import { DatePipe } from '@angular/common';
import { CONSTANTS, UserRoles } from 'app/constants/constants';
import * as moment from 'moment';
import { UserTag } from 'app/structure/users/users';
import { HttpService } from 'app/services/http/http.service';
import { APIs } from 'app/constants/apis';
import { StructureService } from '../../structure.service';
import { PahService } from '../pah.service';
import { PatientActivityHubComponent } from '../patient-activity-hub.component';
import { DateTimePipe } from '../../forms/formp.pipe';
import { ToolTipService } from '../../tool-tip.service';

declare var $: any;
declare var swal: any;
declare var NProgress: any;
//TODO Scope for revamp
@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css'],
  providers: [DatePipe],
})
export class ProfileComponent implements OnChanges {
  /**
   * @param user selected user details 
   * @param patientId seleted patient id
   * @param dynamicData includes selected tab, worklist id, worklist details and reload flag
   * @param worklistList list of worklist under profile section
  */
  @Input() user;
  @Input() patientId;
  @Input() dynamicData;
  @Input() worklistList;
  @Output() updateCaregiverDetails: EventEmitter<any> = new EventEmitter<any>();
  userDetails;
  userPatientId;
  // this Variables
  userProfile: FormGroup;
  userId;
  formData;
  countryCode;
  countryIsoCode = '';
  userDetail: any;
  PatientId;
  optionShowProfile;
  esiDetails = [];
  addressDisplayArray = [];
  userData:any = {};
  userDataConfig ={};
  newExternalSystem = [];
  preval;
  externalSystem;
  additionalInfo = [];
  externalSystemArray = [];
  additionalInfoArry = [];
  extInegration;
  editIntegrationData = [];
  additionalsDataArray = [];
  showexternalIntegration = true;
  noExternalIntegration = false;
  showEmail = true;
  loginUserDetails:any = {};
  email = localStorage.getItem('profileUsername');
  enableContactEdit = true;
  enableProfileEdit = true;
  patientRoles = [];
  crossTenantOptions;
  showAddressForm = false;
  addressHeading = '';
  addressDetails;
  allCountries = [];
  allStates = [];
  selectedCountry = '';
  addrType = 0;
  a_submitted = false;
  selectedTab;
  selectedTabName = 'tabProfile';
  enableSecurityRule = false;
  showMoreDetails = true;
  userAddress = [];
  caregiverEditVariable;
  hideBreadcrumb = true;
  showCaregiverModal = false;
  integrationSettings: any = [];
  patientIdentityDetails;
  readonly maskPhoneNumber = CONSTANTS.phoneMask;
  userRoles = UserRoles;
  selectedAdmission: any;
  events;
  userTags: UserTag[] = [];
  referralDetails =  { 
    'gender' : '',
    'language': '', 
    'serviceUnit': '',
    'startOfCareDate': '',
    'status' : ''
  };
  // Constructor To initialize Variables or imports
  constructor(
    private route: ActivatedRoute,
    private _pahService: PahService,
    private _formBuild: FormBuilder,
    public structureService: StructureService,
    private dateTimePipe: DateTimePipe,
    private datePipe: DatePipe,
    private httpService: HttpService,
    private _ToolTipService: ToolTipService
  ) {
    this.userData = JSON.parse(this.structureService.userDetails);
    this.loginUserDetails = JSON.parse(this.structureService.userDetails);
    this.setSecurityRuleCondition();
  }

  // Onclick each Patient from left Menu Change all data in the page 
  ngOnChanges(changes: { [propKey: string]: SimpleChange }) {
    if (changes.user && !changes.user.isFirstChange()) {
      this.events = { admissionEvents: { reload: true } };
    }
    this.userDetail = JSON.parse(JSON.stringify(this.user));
    this.additionalInfo = JSON.parse(JSON.stringify(this.userDetail.patientCustomFields));
    this.addressDetails = this._formBuild.group({
      id: [''],
      category: ['', Validators.required],
      line1: ['', Validators.required],
      line2: [''],
      city: [''],
      state: [''],
      district: [''],
      country: [''],
      zipCode: ['']
    });
    this.route.params.subscribe((params: Params) => {
      this.userId = params['patientId'];
    });
    this.showAddressForm = false;
    this.optionShowProfile = 'tabProfile';
    if (localStorage.getItem('currentProfileTab') == 'contacts') {
      this.optionShowProfile = 'tabContacts';
    }
    if (localStorage.getItem('currentProfileTab') == 'caregiver') {
      this.optionShowProfile = 'tabCaregiver';
    }
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
      let tab = $(e.target).data('target');
      if (tab === '#tabProfile') {
        tab = 'tabProfile';
      } else if (tab === '#tabContacts') {
        tab = 'tabContacts';
      } else if (tab === '#tabCaregiver') {
        tab = 'tabCaregiver'; 
      }else {
        tab = 'tabMoreDetails';
      }
    });
    setTimeout(() => {
      $("#phone").intlTelInput();
      this.getCountryCode();
      $('#taggedUser').select2({
        placeholder: 'Select User Tags'
      });
      $('#taggedUser').val('').trigger('change');
    }, 1000);
    $("#phone").on("countrychange", (e, countryData) => {
      if (countryData.dialCode) {
        this.countryCode = '+' + countryData.dialCode;
        this.countryIsoCode = countryData.iso2;
      }
    });
    this.patientRoles = this.structureService.getPatientRoles();
    this.setUserProfileData();
    this.crossTenantOptions = this.userData.crossTenantsDetails;
    this.getUserTags();
    if(this.dynamicData.reloadTab == true) {     
      this.optionShowProfile = this.selectedTabName;
      if(this.selectedTabName == 'tabProfile' || this.selectedTabName == 'tabContacts' || this.selectedTabName == 'tabCaregiver') {
        $("#email").prop('disabled', true);
      }
      if(this.selectedTabName == 'tabGridDetails') {
        this.userDetails = {};
        this.userPatientId = '';
        const filterPahDetails = this.worklistList.filter(x => x.id == this.selectedTab);
        this.dynamicData = {
          tabName: filterPahDetails[0].tabName,
          pahworklistId: filterPahDetails[0].id,
          filterPahDetails,
          reloadTab: true
        };
        this.userDetails = this.user;
        this.userPatientId = this.patientId;
        this.structureService.activePatientActivityHub = true;
      }
      if (this.selectedTabName === 'tabCaregiver') {
        this.formatCaregiverData();
      } else if(this.selectedTabName === 'tabReferralDetails') {
        this.getReferralDetails();
      }
    } else {
      this.selectedTab = '';
    }
    const returnFromAlternateContact = localStorage.getItem('returnFromAlternateContact');
    if (returnFromAlternateContact) {
      this.optionShowProfile = 'alternate-contacts';
      localStorage.setItem('returnFromAlternateContact', '');
    }
  }
  getUserTags() {
    this.httpService.doGet(APIs.getTagDetails, { params: { group: '3', enroll: '1' } }).subscribe((data) => {
      this.userTags = data;
      if (this.userTags && this.userDetail.userTags) {
        this.setUserTags(this.userDetail.userTags.map((tag: UserTag) => tag.id));
      }
    });
  }
  formatCaregiverData() {
    /** Format the date and time values shown in caregiver listing table */
    let self = this;
    /** For making user variable writable */
    this.user = JSON.parse(JSON.stringify(this.user));
    this.user.associateCaregivers.map(function (associatedUser) {
      if (associatedUser.createdAt) {
        if (
          associatedUser.createdAt === '2019-02-11T06:38:09.000Z' ||
          associatedUser.createdAt === '2019-02-13T04:09:06.000Z' ||
          associatedUser.createdAt === '2019-02-20T12:02:05.000Z' ||
          associatedUser.createdAt === '2018-09-28T04:35:24.000Z'
        ) {
          associatedUser.createdAt = '-';
        } else {
          associatedUser.createdAt = self.dateTimePipe.transform(associatedUser.createdAt);
        }
      }
      if (associatedUser.lastLogin) {
        associatedUser.lastLogin = `${self.datePipe.transform(associatedUser.lastLogin * 1000, 'mediumDate')} ${self.datePipe.transform(
          associatedUser.lastLogin * 1000,
          'shortTime'
        )}`;
      } else {
        associatedUser.lastLogin = '-';
      }
    });
  }
  // Country code for contact Link in Profile
  getCountryCode() {
    const countryDetails = setCountryCodeFlag('phone', this.userDetail.countryCode, this.userDetail.countryIsoCode);
    this.countryCode = countryDetails.countryCode;
    this.countryIsoCode = countryDetails.countryIsoCode;
  }
  setSecurityRuleCondition() {
    this.showMoreDetails = !(
      this.userData.accessSecurityEnabled &&
      (this.userData.accessSecurityType === 'physicianOrPrescriber' || this.userData.accessSecurityType === 'nursingAgency')
    );
    this.enableSecurityRule = +this.userData.config.enable_patient_info_from_third_party_app === 1;
  }
  //Geting profile Datas from Structure Service
  setUserProfileData() {
    let dob = '';
    let startOfDate;
    if (this.userDetail.dateOfBirth) {
      const dateParts = this.userDetail.dateOfBirth.split('-');
      dob = `${dateParts[1]}/${dateParts[2]}/${dateParts[0]}`;
    }
    if (this.userDetail['externalInfo'].length > 0 ) {
      if(this.userDetail['externalInfo'][0].patientRefStartDate) {
        let newDate = new Date(this.userDetail['externalInfo'][0].patientRefStartDate).getTime();
        var time = moment.utc(newDate).format('MM/DD/YYYY');
        startOfDate = time;
      }
    }
    if (this.userDetail.associatePatient != null) {
      this.userDetail.associatePatients.forEach(element => {
        const dateSplit = element.dateOfBirth.split('-');
        element.dateOfBirth = dateSplit[1] + "/" + dateSplit[2] + "/" + dateSplit[0];
      });
    }
    let gender = this.userDetail.gender || '';
    if (gender.toLowerCase() === 'male') {
      gender = 'Male';
    } else if (gender.toLowerCase() === 'female') {
      gender = 'Female';
    }
    let status;
    if (this.userDetail.status === 'Virtual_User') {
      status = 'Active';
    } else {
      status = this.userDetail.status;
    }
    if (this.userDetail) {
      let mrn = "";
      let site = "";
      let zip = "";
      let homePhn = "";
      let workPhn = "";
      let cellPhn = "";
      if(this.userDetail['externalInfo'].length >0) {
        mrn = this.userDetail['externalInfo'][0].patientMRN;
        site = this.userDetail['externalInfo'][0].patientSite;
        zip = this.userDetail['externalInfo'][0].patientZip;
        homePhn = this.userDetail['externalInfo'][0].patientHomePhone;
        workPhn = this.userDetail['externalInfo'][0].patientWorkPhone;
        cellPhn = this.userDetail['externalInfo'][0].patientCellPhone;
      } else {
        if(this.userDetail.patientIdentity && this.userDetail.patientIdentity.IdentityValue) {
          mrn = this.userDetail.patientIdentity.IdentityValue;
        }
        zip = this.userDetail.zip;
        if(this.userDetail.countryCode && this.userDetail.mobile) {
          cellPhn = this.userDetail.countryCode + this.userDetail.mobile;
        }
      }
      this.userDetail.mobile = removePhoneNumberMasking(this.userDetail.mobile);
      this.userProfile = this._formBuild.group({
        firstName: [this.userDetail.firstName, Validators.required],
        lastName: [this.userDetail.lastName, Validators.required],
        mobile: [this.userDetail.mobile],
        dob: [dob],
        gender: [gender],
        address: [this.userDetail.address],
        email: [''],
        city: [this.userDetail.city],
        state: [this.userDetail.state],
        mrn: [mrn],
        site: [site],
        zip: [zip],
        homePhn: [homePhn],
        workPhn: [workPhn],
        cellPhn: [cellPhn],
        patientRefStartDate: [startOfDate],
        name: [this.userDetail['externalShippingAddresses'].length >0 ? this.userDetail['externalShippingAddresses'][0].addressName:null],
        addressType: [this.userDetail['externalShippingAddresses'].length >0 ? this.userDetail['externalShippingAddresses'][0].shippingAddressType:null],
        shippingAddress: [this.userDetail['externalShippingAddresses'].length >0 ? this.userDetail['externalShippingAddresses'][0].address:null],
        zipCode: [this.userDetail['externalShippingAddresses'].length >0 ? this.userDetail['externalShippingAddresses'][0].addressZipCode:null],
        addressCity: [this.userDetail['externalShippingAddresses'].length >0 ? this.userDetail['externalShippingAddresses'][0].addressCity:null],
        addressState: [this.userDetail['externalShippingAddresses'].length >0 ? this.userDetail['externalShippingAddresses'][0].addressState:null],
        addressPhone: [this.userDetail['externalShippingAddresses'].length >0 ? this.userDetail['externalShippingAddresses'][0].addressPhone:null],
        status: [status, Validators.required],
        addressAttention: [this.userDetail['externalShippingAddresses'].length >0 ? this.userDetail['externalShippingAddresses'][0].addressAttention:null],
        codeStatus: [this.userDetail['externalInfo'].length >0 ? this.userDetail['externalInfo'][0].patientStatus:null],
        role: [this.userDetail.role ? this.userDetail.role.id : '', Validators.required],
        comment: [this.userDetail.comments[0].comment],
        guId: [(this.userDetail.patientIdentity && this.userDetail.patientIdentity.guId) || ''],
        enableSmsNotifications: this.userDetail.enableSmsNotifications,
        enableEmailNotifications: this.userDetail.enableEmailNotifications
      });
      if (this.structureService.isMRNFieldMandatory) {
        this.userProfile.controls.mrn.setValidators([Validators.required]);
        this.userProfile.controls.guId.setValidators(this.structureService.isMultiPartMRNEnabled ? [Validators.required] : null);
      }
      if (!this._pahService.validateEmail(this.userDetail.emails[0].value)) {
        this.userProfile.patchValue({
          email : ""
        });
      } else {
        this.userProfile.patchValue({
          email : this.userDetail['emails'][0].value
        });
      }
      this.userProfile.patchValue({});
    }
    this.enableProfileEdit = true;
    $('.editable-field-profile').prop("disabled", true);
    this.enableContactEdit = true;
    $('.editable-field-contact').prop("disabled", true);
    this.userProfile.controls.guId.disable();
  }
  // Settings Cancel Button redirect to Home
  cancelProfile() {
    this.setUserProfileData();
    this.enableProfileEdit = true;
    $('.editable-field-profile').prop('disabled', true);
    this.userProfile.controls.guId.disable();
  }
  selectedAdmissionHandle(event) {
    if (event.admission && event.admission.admissionId) {
      this.selectedAdmission = event.admission.admissionId;
    }
  }
  onEvents(event) {
    if (event && event.admissionEvents && (event.admissionEvents.reload || event.admissionEvents.cancel)) {
      this.selectedAdmission = null;
      this.events = event;
    }
  }
  //Tabs for MainTasks
  showDataProfile(data, patientId, tabId) {
    this.selectedAdmission = null;
    this.optionShowProfile = data;
    this.selectedTabName = data;
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
      let tab = $(e.target).data('target');
      if (tab === '#tabProfile') {
        tab = 'tabProfile';
      } else if (tab === '#tabCaregiver') {
        tab = 'tabCaregiver';
      } else if (tab === '#tabAdmission') {
        tab = 'admission';
      } else {
        tab = 'tabMoreDetails';
      }
    });
    if (this.selectedTabName == 'tabCaregiver') {
      this.formatCaregiverData();
    }
    if(data == 'tabGridDetails' && this.selectedTab != tabId) {
      this.selectedTab = tabId;
      let filterPahDetails = this.worklistList.filter(x => x.id == tabId);
      this.dynamicData = { "tabName": filterPahDetails[0].tabName, "pahworklistId": filterPahDetails[0].id, 'filterPahDetails': filterPahDetails, 'reloadTab':false };
      this.userDetails = this.user;
      this.userPatientId = this.patientId;
      this.structureService.activePatientActivityHub = true;
    } else if(data === 'tabReferralDetails'){
      this.getReferralDetails();
    } else {
      this.selectedTab = '';
    }
  }

  updateProfile() {
    this.enableProfileEdit = false;
    $('.editable-field-profile').prop('disabled', false);
    this.toggleEUIControl();
  }
  updateContact() {
    this.enableContactEdit = false;
    $('.editable-field-contact').prop('disabled', false);
    this.toggleEUIControl();
  }

  toggleEUIControl() {
    const guIdControl = this.userProfile.controls.guId;
    if (isBlank(guIdControl.value)) {
      guIdControl.enable();
    } else {
      guIdControl.disable();
    }
  }

  get getUpdatedUserTags() {
    return $('#taggedUser').val();
  }
  setUserTags(userTags) {
    setTimeout(() => {
      $('#taggedUser').val(userTags).trigger('change');
    }, 1200);
  }
  updateUserProfile() {
    NProgress.start();
    const updateUserData: any = {
      firstName: this.userProfile.value.firstName,
      lastName: this.userProfile.value.lastName,
      id: this.userDetail.id,
      comment: this.userProfile.value.comment,
      status: this.userProfile.value.status,
      mrn: this.userProfile.value.mrn || '',
      guId: this.userProfile.getRawValue().guId,
      name: this.userProfile.value.email,
      userTags: this.getUpdatedUserTags.map((tag) => ({ id: tag })),
      cellNumber: removePhoneNumberMasking(this.userProfile.value.mobile),
      ccode: this.countryCode,
      countryIsoCode: this.countryIsoCode,
      siteIds: this.userDetail.siteId,
      zip: this.userDetail.zip,
      dateOfBirth: this.userProfile.value.dob,
      enableSmsNotifications: this.userProfile.value.enableSmsNotifications,
      enableEmailNotifications: this.userProfile.value.enableEmailNotifications
    };
    let grpId = 3;
    if (this.crossTenantOptions == false){      
      grpId = null;
    }
    let isCareGiver = false;
    if (this.userDetail.role && this.userDetail.role.displayName === 'Caregiver') {
      isCareGiver = true;
    }
    this.structureService
      .updateUser(this.userDetail.id, updateUserData, grpId, isCareGiver, this.userDetail.tenantId, updateUserData.mrn)
      .then((result: any) => {
        if (result && result.updateUser && result.updateUser.updateStatus === 'success') {
          if (
            this.userDetail.status !== this.userProfile.value.status &&
            (this.userProfile.value.status === 'Inactive' || this.userProfile.value.status === 'Deleted')
          ) {
            this.structureService.socket.emit('userDeactivatedPolloing', { userId: this.userId });
          }
          this.userDetail.firstName = updateUserData.firstName;
          this.userDetail.displayName = `${updateUserData.firstName} ${updateUserData.lastName}`;
          this.userDetail.lastName = updateUserData.lastName;
          this.userDetail.status = updateUserData.status;
          this.userDetail.comments = [{ comment: updateUserData.comment }];
          this.userDetail.mobile = updateUserData.cellNumber;
          this.userDetail.emails = [{ value: updateUserData.name }];
          this.userDetail.countryCode = this.countryCode;
          this.userDetail.countryIsoCode = this.countryIsoCode;
          this.userDetail.patientIdentity.IdentityValue = updateUserData.mrn;
          this.userDetail.patientIdentity.guId = updateUserData.guId;
          this.userDetail.userTags = updateUserData.userTags;
          this.userDetail.enableSmsNotifications = updateUserData.enableSmsNotifications;
          this.userDetail.enableEmailNotifications = updateUserData.enableEmailNotifications;
          PatientActivityHubComponent.returned.next(this.userDetail);
          this.structureService.notifyMessage({
            type: 'success',
            message: this._ToolTipService.getTranslateData('MESSAGES.SUCCESS_PROFILE_DETAIL_UPDATE')
          });
          const activityData = {
            activityName: 'Update User Details',
            activityType: 'PAH profile update', // old type "user access"
            activityDescription: `User account updated (${this.userData.displayName})`
          };
          this.structureService.trackActivity(activityData);
          this.enableProfileEdit = true;
          $('.editable-field-profile').prop('disabled', true);
          this.userProfile.controls.guId.disable();
          localStorage.setItem('currentProfileTab', '');
          this.updateCaregiverDetails.emit(this.userDetail.id);
        } else if (result && result.updateUser && result.updateUser.updateStatus === 'duplicateMRN') {
          const activityLogMessage = `${this.userDetail.displayName} not updated by ${this.userData.displayName} because MRN (${updateUserData.mrn}) already exits`;
          const activityData = {
            activityName: 'Update User Details',
            activityType: 'PAH profile update',
            activityDescription: activityLogMessage
          };
          this.structureService.trackActivity(activityData);
          this.structureService.notifyMessage({
            type: 'danger',
            message: this._ToolTipService.getTranslateDataWithParam('MESSAGES.MRN_ALREADY_IN_USE', { mrnNumber: updateUserData.mrn, displayName: '' })
          });
        } else if (result && result.updateUser && result.updateUser.updateStatus === 'duplicateExternalUserId') {
          const activityLogMessage = `${this.user.displayName} not updated by ${this.userData.displayName} because External User Identifier (${updateUserData.guId}) already exist`;
          const activityData = {
            activityName: 'Update User Details',
            activityType: 'PAH profile update',
            activityDescription: activityLogMessage
          };
          this.structureService.trackActivity(activityData);
          this.structureService.notifyMessage({
            type: 'danger',
            message: this._ToolTipService.getTranslateDataWithParam('MESSAGES.EXTERNAL_USER_IDENTIFIER_ALREADY_IN_USE', {
              externalUserId: updateUserData.guId,
              displayName: ''
            })
          });
        } else {
          this.structureService.notifyMessage({ type: 'error', message: this._ToolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG') });
        }
        NProgress.done();
      });
  }
  /**
   * To get the patient referral details
   */
  private getReferralDetails() {
   this.httpService.doGet( 
    APIs.getPatientReferralDetails.replace('{userId}','')
    + parseInt(this.patientId))
     .subscribe((result) => {
      if (!isBlank(result)) {
        this.referralDetails = {
            gender: result.gender,
            language: result.language,
            status: result.status,
            serviceUnit: result.serviceUnit,
            startOfCareDate: !isBlank(result.startOfCare) ? result.startOfCare.split('T')[0] : '' //The value will be in UTC format, need to show the date only
          };
        }
      this.structureService.trackActivity({
        activityName: 'Show referral details in PAH profile',
        activityType: 'View referral details',
        activityDescription: this.userData.displayName + 'viewed the referral details in the PAH profile'
      });
     }, error => {
      this.structureService.trackActivity({
        activityName: 'Show referral details in PAH profile',
        activityType: 'View referral details',
        activityDescription: 'Error while fetching patient referral details,error details' + error
      });
     });
  }

  togglePreference(preference, value) {
    this.userProfile.patchValue({
      [preference]: value
    });
  }
}
