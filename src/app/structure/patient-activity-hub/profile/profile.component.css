
.profile-avatar {
  width: 150px;
  height: 150px;
  border: 1px solid #ddd;
  border-radius: 50%;
  overflow: hidden;
  margin-top: 20px;
}
.profile-avatar img {
  height: 100%;
  object-fit: cover;
} 
img#profileImageSmall {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.profile-avatar a {
  z-index: 999;
  position: absolute;
  left: 122px;
  font-size: 23px;
  top: 100px;
  color: #5394a2;
}
.profile-avatar a i {
  background: #fff;
  border-radius: 50%;
  height: 20px;
  cursor: pointer;
}
.information-edit i {
  float: right;
  cursor: pointer;
  color: #0190fe;
  width: 30px;
  height: 30px;
  text-align: center;
  border-radius: 50%;
  padding-top: 6px;
}
.personal-information-edit-icon{
  float:left;
  position: absolute;
  padding-left: 5px;
  top: 90px;
}
.information-edit i:hover {
  background: #0190fe !important;
  color: #fff;
}
.personal-info-title {
  height: 35px;
}
.personal-info-submit {
  display: none;
}
.profile-avatar-disabled img {
  opacity: 0.3;
  cursor: no-drop;
}
.thead-light {
    background: #f3f3f3 !important;
}
.th-col-style1 {
  border-right: 1px solid #dee2e6 !important;
}
.th-col-style2 {
  border-color: #dee2e6;
  border-right: 1px solid #dee2e6;
  padding-right: 10px;
}
.th-col-style3 {
  border-color: #dee2e6;
  padding-right: 50px;
}
span.allformsort, span.assformsort {
  color: rgb(255, 0, 0);
  cursor: pointer;
  font-size: 18px;
  padding: 4px 2px;
}
.form-search {
  float: right;
  border: 1px solid #00000047;
  border-radius: 4.5px;
  width: 50%;
}
#tabPatientForms h5 {
  font-size: 14px;
  font-weight: 700;
}
#tabPatientForms .form-search {
  width: 175px;
  padding: 5px;
  margin-top: -5px;
  margin-bottom: 10px;
}
#tabPatientForms .addButton, #tabPatientForms .removeButton {
  width: 95%;
}
#tabPatientForms i.fa.fa-refresh {
  position: absolute;
  right: 15px;
  color: #b8b8b8;
  cursor: pointer;
}
#tabPatientForms .form-search{
  margin-right: 25px;
}
#address-menu {
  cursor: pointer !important;
  cursor: hand !important;
}
label.radio-label {
  position: relative !important;
  top: 0 !important;
  left: 2px !important;
  pointer-events: all !important;
  cursor: pointer !important;
  cursor: hand !important;
}
