<div class="mb-6">
  <div class="nav-tabs-horizontal">
    <ul class="nav nav-tabs mb-4 mt-2" role="tablist" yle="font-size:10px;">
      <li class="nav-item">
        <a class="nav-link" data-target="#tabProfile" data-toggle="tab" href="javascript: void(0);" role="tab"
          [ngClass]="{'active': optionShowProfile === 'tabProfile'}" aria-expanded="true"
          (click)="showDataProfile('tabProfile', PatientId, '')"><i class="fa fa-address-card completed"></i>
          Personal Information</a>
      </li>
      <li class="nav-item" *ngIf="structureService.isMultiAdmissionsEnabled">
        <a
          class="nav-link"
          data-target="#tabAdmission"
          data-toggle="tab"
          href="javascript: void(0);"
          role="tab"
          [ngClass]="{ active: optionShowProfile === 'admission' }"
          aria-expanded="true"
          (click)="showDataProfile('admission', PatientId, '')"
        >
          <i class="fa fa-address-card action-required"></i>
          {{ 'ADMISSION.LABELS.ADMISSION_DETAILS' | translate }}
        </a>
      </li>
      <li class="nav-item">
        <a
          class="nav-link"
          data-target="#tabAlternateContacts"
          data-toggle="tab"
          href="javascript: void(0);"
          role="tab"
          [ngClass]="{ active: optionShowProfile === 'alternate-contacts' }"
          aria-expanded="true"
          (click)="showDataProfile('alternate-contacts', PatientId, '')"
        >
          <i class="fa fa-address-book action-required"></i>
          {{ 'LABELS.ALTERNATE_CONTACTS' | translate }}
        </a>
      </li>
      <li class="nav-item" *ngIf="userDetail.role && userDetail.role.displayName !== 'Caregiver' && showMoreDetails && additionalInfo.length">
        <a class="nav-link" data-target="#tabMoreDetails" data-toggle="tab" href="javascript: void(0);" role="tab"
          [ngClass]="{'active': optionShowProfile === 'tabMoreDetails'}" aria-expanded="false"
          (click)="showDataProfile('tabMoreDetails', PatientId, '')"><i class="fa fa-cog waiting-others"></i>
          More Details </a>
      </li>
      <li *ngFor="let tab of worklistList" class="nav-item">
        <a class="nav-link" data-target="#tabGridDetails" data-toggle="tab" href="javascript: void(0);" role="tab"
          [ngClass]="{'active': selectedTab === tab.id && optionShowProfile === 'tabGridDetails'}" aria-expanded="false"
          (click)="showDataProfile('tabGridDetails', PatientId, tab.id)"><i class="{{tab.tabIcon}}"></i>
          {{tab.tabName}} </a>
      </li>
      <li *ngIf="userData.config.enable_refill_dashboard && userData.config.enable_refill_dashboard === '1'">
        <a class="nav-link" data-target="#tabReferralDetails" data-toggle="tab" href="javascript: void(0);" role="tab"
          [ngClass]="{'active': optionShowProfile === 'tabReferralDetails'}" aria-expanded="false"
          (click)="showDataProfile('tabReferralDetails', PatientId)"><i class="fa fa-address-card waiting-others"></i>
          {{'TITLES.REFERRAL_DETAIL' | translate}}</a>
      </li>
    </ul>
    <div class="tab-content">
      <div class="tab-pane" id="tabProfile" role="tabcard" aria-expanded="true"
        [ngClass]="{'active': optionShowProfile === 'tabProfile'}">
        <h5>Demographics <span data-placement="top" title="" data-original-title="Edit Personal Information"
            (click)="updateProfile()" [hidden]="enableSecurityRule == true"  class="information-edit personal-information-edit-icon"
            style="color:rgb(62, 172, 216);"><i style="margin-top: -38px;" class="icmn-pencil"></i></span>
        </h5>
        <hr>
        <form class="new-form" [formGroup]="userProfile" [hidden]="enableSecurityRule">
          <!-- Changing from hidden to ngIf, then, intlTelInput won't render. -->
          <div class="row">
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="mrn">{{ 'LABELS.MRN' | translate }}</label>
                <input type="text" class="form-control editable-field-profile" formControlName="mrn" appTrimSpaces disabled />
              </div>
              <div
                *ngIf="userProfile.controls.mrn.hasError('required') && (userProfile.controls.mrn?.dirty || userProfile.controls.mrn?.touched)"
                class="alert alert-danger"
              >
                {{ 'VALIDATION_MESSAGES.MRN_EMPTY' | translate }}
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l1">First Name</label>
                <input type="text" class="form-control editable-field-profile" id="l1" formControlName="firstName" disabled>
              </div>
              <div
                *ngIf="userProfile.controls['firstName'].hasError('required')&&(userProfile.controls.firstName?.dirty ||userProfile.controls.firstName?.touched)"
                class="alert alert-danger">
                Field cannot be empty
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Last Name</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="lastName" disabled>
              </div>
              <div
                *ngIf="userProfile.controls['lastName'].hasError('required')&&(userProfile.controls.lastName?.dirty ||userProfile.controls.lastName?.touched)"
                class="alert alert-danger">
                Field cannot be empty
              </div>
            </div>
            <div class="col-lg-5" [hidden]="userDetail.role && userDetail?.role.displayName == 'Caregiver'">
              <div class="form-group">
                <label class="form-control-label">DOB</label>
                <input formControlName="dob" id="" type="text" id="" class="form-control "
                  placeholder="Patient Date of Birth" disabled>
              </div>
              <div
                *ngIf="userProfile.controls['dob'].hasError('required')&&(userProfile.controls.dob?.dirty ||userProfile.controls.dob?.touched)"
                class="alert alert-danger">
                Field cannot be empty
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l1">{{ 'LABELS.MOBILE_NUMBER' | translate }}</label>
                <div class="row">
                  <div class="col-lg-2">
                    <input type="tel" placeholder="" class="form-control editable-field-profile" style="width: 0" id="phone" disabled />
                  </div>
                  <div class="col-lg-4">
                    <label class="form-control ccode">{{ countryCode }}</label>
                  </div>
                  <div class="col-lg-6">
                    <input
                      type="text"
                      appPhoneNumberValidator
                      [textMask]="{ mask: maskPhoneNumber, guide: false }"
                      class="form-control editable-field-profile"
                      id="cellNumber"
                      formControlName="mobile"
                      disabled
                    />
                  </div>
                </div>
              </div>
              <div class="alert alert-danger" *ngIf="userProfile.controls.mobile.errors !== null">
                {{ 'VALIDATION_MESSAGES.PLEASE_ENTER_VALID_MOBILE_NUMBER' | translate }}
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="email">{{ 'LABELS.EMAIL' | translate }}</label>
                <input type="text" class="form-control editable-field-profile" id="email" formControlName="email" disabled />
              </div>
            </div>
            <div class="col-lg-5" *ngIf="userDetail.role && userDetail?.role.displayName !== userRoles.caregiver">
              <div class="form-group">
                <label class="form-control-label" for="taggedUser">{{ 'LABELS.USER_TAGS' | translate }}</label>
                <select class="form-control editable-field-profile" id="taggedUser" multiple disabled>
                  <option *ngFor="let userTag of userTags" value="{{ userTag.id }}">{{ userTag.tag_name }}</option>
                </select>
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label">Status</label>
                <select class="form-control editable-field-profile" formControlName="status" disabled>
                  <option value="" hidden>Select Status</option>
                  <option value="Active">Active</option>
                  <option value="Inactive">Inactive</option>
                  <option value="Pending">Pending</option>
                </select>
              </div>
              <div
                *ngIf="userProfile.controls['status'].errors&&(userProfile.controls.status?.dirty ||userProfile.controls.status?.touched )"
                class="alert alert-danger">
                Status cannot be empty
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label">Comment</label>
                <textarea formControlName="comment" id="comment" class="form-control editable-field-profile"
                  placeholder="Comment" disabled></textarea>
              </div>
            </div>
            <div class="col-lg-5" *ngIf="structureService.isMultiPartMRNEnabled">
              <div class="form-group">
                <label class="form-control-label" for="guId">{{ 'LABELS.EXTERNAL_USER_IDENTIFIER' | translate }} <i chToolTip="euiTooltip" class="icmn-info date-range-info" data-animation="false"></i></label>
                <input type="text" class="form-control editable-field-profile" formControlName="guId" appTrimSpaces disabled /> 
              </div>
              <div
                *ngIf="userProfile.controls['guId'].hasError('required') && (userProfile.controls.guId.dirty || userProfile.controls.guId.touched)"
                class="alert alert-danger"
              >
                {{ 'VALIDATION_MESSAGES.EXTERNAL_USER_IDENTIFIER_CANNOT_BE_EMPTY' | translate }}
              </div>
            </div>
            <div id="enable_sms_notifications" class="form-group row col-lg-5">
              <label class="col-md-8 control-label">{{'LABELS.ENABLE_SMS_NOTIFICATION' | translate}}</label>
              <div class="col-md-4">
                  <div class="btn-group float-right" [ngClass]="{'disabled': this.enableProfileEdit}">
                      <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': userProfile.controls['enableSmsNotifications'].value === 1}"
                          (click)="togglePreference('enableSmsNotifications', 1)">
                          {{'BUTTONS.YES' | translate}}
                      </button>
                      <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': userProfile.controls['enableSmsNotifications'].value !== 1}"
                          (click)="togglePreference('enableSmsNotifications', 0)">
                          {{'BUTTONS.NO' | translate}}
                      </button>
                  </div>
              </div>
            </div>
            <div id="enable_email_notifications" class="form-group row col-lg-5 ml-3">
              <label class="col-md-8 control-label">{{'LABELS.ENABLE_EMAIL_NOTIFICATION' | translate}}</label>
              <div class="col-md-4">
                  <div class="btn-group float-right" [ngClass]="{'disabled': this.enableProfileEdit}">
                      <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': userProfile.controls['enableEmailNotifications'].value === 1}"
                          (click)="togglePreference('enableEmailNotifications', 1)">
                          {{'BUTTONS.YES' | translate}}
                      </button>
                      <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': userProfile.controls['enableEmailNotifications'].value !== 1}"
                          (click)="togglePreference('enableEmailNotifications', 0)">
                          {{'BUTTONS.NO' | translate}}
                      </button>
                  </div>
              </div>
            </div>
            <div class="col-lg-10" [hidden]="userDetail?.role.displayName != 'Caregiver'"
              *ngIf="userDetail && userDetail.associatePatients">
              <div class="patients-header" style="padding-left:0px !important;">
                <span>On Behalf Of</span>
              </div>
            </div>
            <div [hidden]="userDetail?.role.displayName != 'Caregiver'"
              *ngFor="let associatedUser of userDetail.associatePatients" class="col-lg-10">
              <div class="row">
                <div class="col-lg-6">
                  <div class="form-group">
                    <label class="form-control-label">Patient Name</label>
                    <input value="{{associatedUser.displayName}}" type="text" id="" class="form-control"
                      placeholder="Patient Name" disabled>
                  </div>
                </div>
                <div class="col-lg-6">
                  <div class="form-group">
                    <label class="form-control-label">Patient Date Of Birth (mm/dd/yyyy)</label>
                    <input value="{{associatedUser.dateOfBirth}}" type="text" id="" class="form-control"
                      placeholder="Patient DOB" disabled>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="form-actions" [hidden]="enableProfileEdit">
            <div class="form-group">
              <button type="submit" class="btn width-200 btn-primary submit-profile" (click)="updateUserProfile()"
                [disabled]="!userProfile.valid">Submit</button>
              <button type="button" (click)="cancelProfile()" class="btn btn-default">Cancel</button>
            </div>
          </div>
        </form>
        <form class="new-form" [formGroup]="userProfile" *ngIf="enableSecurityRule == true">
          <div class="row">
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="mrn">{{ 'LABELS.MRN' | translate }}</label>
                <input type="text" class="form-control editable-field-profile" formControlName="mrn" appTrimSpaces disabled />
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l1">First Name</label>
                <input type="text" class="form-control editable-field-profile" id="l1" formControlName="firstName"
                  disabled>
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Last Name</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="lastName"
                  disabled>
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Gender</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="gender"
                   disabled>
              </div>
              </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Street Address</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="address"
                   disabled>
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label">Code Status</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="codeStatus"
                   disabled>
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">City</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="city"
                   disabled>
              </div>
             
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Site/Team/Company</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="site"
                   disabled>
              </div>
              
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">State</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="state"
                  disabled>
              </div>
              
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Home Phone</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="homePhn"
                  disabled>
              </div>
             
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Zip</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="zip"
                   disabled>
              </div>
             
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Work Phone</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="workPhn"
                  disabled>
              </div>
             
            </div>
            <div class="col-lg-5" [hidden]="userDetail.role && userDetail?.role.displayName == 'Caregiver'">
              <div class="form-group">
                <label class="form-control-label">Date Of Birth</label>
                <input formControlName="dob" id="" type="text" id="" class="form-control "
                  placeholder="Patient Date of Birth" disabled>
              </div>
            
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Cell Phone</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="cellPhn"
                  disabled>
              </div>
            
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Start of care</label>
                <input type="text" class="form-control editable-field-profile" id="l0"
                  formControlName="patientRefStartDate"  disabled>
              </div>
             
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Email</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="email"
                   disabled>
              </div>
            </div>
            <div class="col-lg-5" *ngIf="structureService.isMultiPartMRNEnabled">
              <div class="form-group">
                <label class="form-control-label" for="guId">{{ 'LABELS.EXTERNAL_USER_IDENTIFIER' | translate }} <i chToolTip="euiTooltip" class="icmn-info date-range-info" data-animation="false"></i></label>
                <input type="text" class="form-control editable-field-profile" formControlName="guId" appTrimSpaces disabled />
              </div>
            </div>
            <div class="col-lg-10">
              <div class="form-group">
                <h5>Shipping Address <i class="fa fa-truck" style="color:#5ab7fe;"></i>
                </h5>
                <hr>
              </div>
            </div>

            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label">Name</label>
                <input type="text" class="form-control editable-field-profile" formControlName="name"
                   disabled>
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Address Type</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="addressType"
                   disabled>
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Address</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="shippingAddress"
                   disabled>
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label">Phone</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="addressPhone" disabled>
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Address2</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="addressAttention"
                  disabled>
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">City</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="addressCity"
                   disabled>
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">State</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="addressState"
                  disabled>
              </div>
            </div>
            <div class="col-lg-5">
              <div class="form-group">
                <label class="form-control-label" for="l0">Zip</label>
                <input type="text" class="form-control editable-field-profile" id="l0" formControlName="zipCode"
                   disabled>
              </div>
            </div>
          </div>
          <div class="form-actions" [hidden]="enableProfileEdit">
            <div class="form-group">
            </div>
          </div>
        </form>
        <app-address *ngIf="!enableSecurityRule" [user]="user"></app-address>
      </div>

      <div
        class="tab-pane"
        [ngClass]="{ active: optionShowProfile === 'admission' }"
        id="tabAdmissions"
        (click)="optionShowProfile = 'admission'"
        role="tabcard"
        aria-expanded="false"
      >
        <app-admissions-table
          *ngIf="optionShowProfile === 'admission'"
          [hidden]="selectedAdmission"
          [selectedPatient]="user.id"
          [events]="events"
          (selectedItem)="selectedAdmissionHandle($event)"
        ></app-admissions-table>
      </div>
      <div
        class="tab-pane"
        [ngClass]="{ active: optionShowProfile === 'alternate-contacts' }"
        id="tabAlternateContacts"
        (click)="optionShowProfile = 'alternate-contacts'"
        role="tabcard"
        aria-expanded="false"
      >
        <app-alternate-contacts *ngIf="optionShowProfile === 'alternate-contacts'" [portal]="'pah'"
         [user]="user" [alternateContactData]="user.alternateContacts"></app-alternate-contacts>
      </div>
      
      <div *ngIf="selectedAdmission && optionShowProfile === 'admission'">
        <div class="patients-header no-padding">
          <span>{{ 'ADMISSION.LABELS.EDIT_ADMISSION' | translate }}</span>
          <span class="pull-right col-md-3">
            <a (click)="onEvents({ admissionEvents: { cancel: true } })" class="btn btn-sm btn-primary float-sm-right" id="back">
              <i class="fa fa-long-arrow-left" aria-hidden="true"></i>
              {{ 'BUTTONS.BACK' | translate }}
            </a>
          </span>
        </div>
      </div>
      <app-admission
        [admissionId]="selectedAdmission"
        [user]="user"
        (events)="onEvents($event)"
        *ngIf="selectedAdmission && optionShowProfile === 'admission'"
      >
      </app-admission>
      <hr />
      <app-address *ngIf="selectedAdmission && optionShowProfile === 'admission'" [admissionId]="selectedAdmission" [user]="user"></app-address>
      <div class="tab-pane" *ngIf="userDetail.role && userDetail.role.displayName != 'Caregiver'" id="tabMoreDetails" role="tabcard" aria-expanded="false" [ngClass]="{'active': optionShowProfile === 'tabMoreDetails'}">
            <h5 class="text-black" [hidden]="additionalInfo.length==0"><strong>Additional
                Information</strong></h5>
            <div class="form-actions" [hidden]="additionalInfo.length==0"></div>
            <div *ngFor="let info of additionalInfo; let i= index">
            <div class="form-group row" >
              <label class="control-label col-md-3"><strong>{{info.fieldName}}</strong></label>
              <div class="col-md-5">
                <input type="text" id="" class="form-control" value="{{info.fieldValue}}" placeholder="Custom Field Name"
                  disabled>
            
              </div>
              <div class="col-md-1" [hidden]="additionalInfo.length==0"><strong></strong></div>
            </div>
            </div>
            <div class="form-actions" [hidden]="additionalInfo.length==0"></div>
                    <div class="form-actions"></div>
      </div>
      <div class="tab-pane" id="tabGridDetails" role="tabcard" aria-expanded="false"
        [ngClass]="{'active': optionShowProfile === 'tabGridDetails'}">
        <ng-container *ngFor="let tab of worklistList" class="col-md-12">
          <app-dynamic [userDetails]="userDetails" [userPatientId]="userPatientId" *ngIf="selectedTab == tab.id"
            [dynamicData]="dynamicData"></app-dynamic>
        </ng-container>
      </div>
      <div class="tab-pane"  
      id="tabReferralDetails" role="tabcard" aria-expanded="false" [ngClass]="{'active': optionShowProfile === 'tabReferralDetails'}">
        <h5>{{'TITLES.REFERRAL_DETAIL' | translate}}</h5>
        <hr>
        <div class="row">
          <div class="col-lg-5">
            <div class="form-group">
              <label class="form-control-label">{{'LABELS.GENDER' | translate}}</label>
              <input class="form-control" [value]="referralDetails.gender" disabled />
            </div>
          </div>
          <div class="col-lg-5">
            <div class="form-group">
              <label class="form-control-label">{{'LABELS.LANGUAGE' | translate}}</label>
              <input class="form-control" [value]="referralDetails.language" disabled />
            </div>
          </div>
          <div class="col-lg-5">
            <div class="form-group">
              <label class="form-control-label">{{'LABELS.SERVICE_UNIT' | translate}}</label>
              <input class="form-control" [value]="referralDetails.serviceUnit" disabled />
            </div>
          </div>
          <div class="col-lg-5">
            <div class="form-group">
              <label class="form-control-label">{{'LABELS.START_OF_CARE_DATE' | translate}}</label>
              <input class="form-control" [value]="referralDetails.startOfCareDate" disabled />
            </div>
          </div>
          <div class="col-lg-5">
            <div class="form-group">
              <label class="form-control-label">{{'LABELS.PATIENT_STATUS' | translate}}</label>
              <input class="form-control" [value]="referralDetails.status" disabled />
            </div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
