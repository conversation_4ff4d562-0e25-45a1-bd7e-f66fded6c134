import { EventEmitter, Injectable, Output } from '@angular/core';
import { Http, <PERSON><PERSON>, RequestOptions } from '@angular/http';
import { ApolloClient, createNetworkInterface } from 'apollo-client';
import { Apollo } from 'apollo-angular';
import { StructureService } from '../structure.service';
import { FormsService } from '../forms/forms.service';
import { Router, ActivatedRoute, Params, NavigationEnd } from '@angular/router';
import gql from 'graphql-tag';

@Injectable()
export class PahService {
  browserCache = 'sharedvariable';
    constructor(
        
      ) {
       
      }
      validateEmail(email) {
        var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        if(re.test(email)){
          if (email.includes('unknown_')) { 
            return false;
          }
          else{
            return true;
          }
        }
        else{
          return false;
        }
      }
      getWorklistDataUsingGraphQLAPI(url, query, variables, headers) {  
        let queryParams = gql`${query}`;
        const networkInterface = createNetworkInterface({
          uri: url,
        });
          let self = this;
          networkInterface.use([{
            applyMiddleware(req, next)  {
              if (!req.options.headers) {
                req.options.headers = headers;
              }
              next();
            },
          }]);
        const client = new ApolloClient({
          networkInterface
        });
        console.log(variables);
        // client.query(getWorklistData).map(
        //   res => res
        // );
        // const subscription = client.query({
        //   query: getWorklistData
        // }).then(response => console.log(response));
        // return subscription;
        const subscription = client.query({
          query: queryParams,
          variables: variables, fetchPolicy: 'network-only'
        });
        //subscription.subscribe(({ data }) => { });
        return subscription;
        
        // let headers = new Headers();
        // headers.append('Content-Type', 'application/x-www-form-urlencoded');
        // headers.append('Authentication-Token', this._structureService.getCookie('authID'));
        // let options = new RequestOptions({ headers: headers });
        // var apiConfig = { url: url, requestType: 'http', data: params, method: 'GET', headers: {}, replaceHeaders: false };
        // if (graphqlQuery) {
        //   apiConfig.replaceHeaders = true;
        //   apiConfig.headers = { 'Content-Type': 'application/json' };
        //   apiConfig.method = 'POST';
        //   console.log(typeof graphqlQuery);
        //   console.log(graphqlQuery);
        //   apiConfig.data = JSON.parse(graphqlQuery);
        // }
        // console.log(apiConfig);
        // return this._structureService.requestData(apiConfig);
        }
}