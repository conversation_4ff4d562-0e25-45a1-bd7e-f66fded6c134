import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { PatientActivityHubComponent } from './patient-activity-hub.component';
import { FilterPipe, AssociatePatientSearchFilterPipe} from './filter.pipe';
import { ProfileComponent } from './profile/profile.component';
import { DynamicComponent } from './dynamic/dynamic.component';
import { PahService} from './pah.service';
import { AgGridModule } from 'ag-grid-angular/main';
import { SharedModule } from '../shared/sharedModule';
import { MessageModule } from '../message/message.module';
import { FormManageModule } from '../forms/forms.module';
import { DocumentsModule } from '../documents/documents.module';
import { SafeHtmlPipe } from './formp.pipe';
import { FormpPipe } from './formp.pipe';
import { LinkCellRenderer } from './renderer/link-cell-renderer.component';
import { formSearchPipe } from './formp.pipe';
import { formRecipientPipe, formFilterPipe, patientRolePipe, exPatientCaregiverPipe } from './formp.pipe';
import {WorkListModule} from "../worklists/worklist.module";
import { ViewPageComponent } from './view-page/view-page.component';
import { NgxDocViewerModule } from 'ngx-doc-viewer';
import { AuthGuard } from 'app/guard/auth.guard';
import { UsersModule } from '../users/users.module';
import { EducationModule } from '../education/education.module';
import { TextMaskModule } from 'angular2-text-mask';

export const routes: Routes = [
  { path: 'pah', component: PatientActivityHubComponent ,canActivate:[AuthGuard],data:{checkUserGroupPermission:'3'}},
  { path: 'pah/:patientId', component: PatientActivityHubComponent,canActivate:[AuthGuard],data:{checkUserGroupPermission:'3'} },
 { path: 'view/:formId/:entryId/:worklistType', component: ViewPageComponent}
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    BrowserModule,
    ReactiveFormsModule,
		AgGridModule.withComponents([LinkCellRenderer]),
    RouterModule.forChild(routes),
    AgGridModule,
    SharedModule,
    MessageModule,
    FormManageModule,
    DocumentsModule,
    WorkListModule,
    NgxDocViewerModule,
    TextMaskModule,
    UsersModule,
    EducationModule
  ],
  declarations: [
    PatientActivityHubComponent,
    FilterPipe,
    AssociatePatientSearchFilterPipe,
    ProfileComponent,
    DynamicComponent,
    SafeHtmlPipe,
    FormpPipe,
    formSearchPipe,
    formRecipientPipe,
    formFilterPipe,
    patientRolePipe,
    LinkCellRenderer,
    exPatientCaregiverPipe,
    ViewPageComponent
  ],
  providers: [
    AssociatePatientSearchFilterPipe,
    PahService
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})

export class PatientActivityHubModule { }
