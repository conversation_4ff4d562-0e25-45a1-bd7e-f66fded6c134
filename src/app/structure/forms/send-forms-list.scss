.btn-search {
  height: 38px;
  width: 100%;
  border-radius: 10px;
}

.activePatient {
  border: 1px solid #1790fe;
}

.saveAsDraft-message {
  color: #000;
  font-size: 14px;
}

.Hide-draft {
  display: none;
}

.form-check-label.grey {
  opacity: 0.6 !important;
  color: #828282 !important;
}

title {
  color: black;
}

.patientDriveUl {
  list-style-type: none;
  padding: 0;
  display: none;
  margin: 0;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #0190fe;
  width: 62.5%;
  border-radius: 0 0px 4px 4px;
  border-top: none;
  position: absolute;
  background: #fff;
  z-index: 99;
  top: auto;
  margin-top: 41px;
}

ul li.patientDriveUl-li.selected {
  background: #d2d9e5 !important;
  color: #222034 !important;
}

ul li.patientDriveUl-li {
  margin-top: -1px;
  padding: 0.42rem 1.14rem;
  text-decoration: none;
  position: relative;
  cursor: pointer;
}

.assoc-label-position {
  padding-top: 4px;
}

.label-position {
  padding-top: 9px;
}

.card-header {
  margin-left: 3px;
  margin-right: 3px;
}

.faildraft {
  color: red;
  position: absolute;
  float: left;
  width: 100%;
}

.recipient-ul li.render-manipulate {
  z-index: 100;
  display: inline-block;
}

ul li.text-muted:hover,
li span.text-muted:hover {
  color: #c0bdd0 !important;
  cursor: not-allowed;
}

.not-select {
  text-align: center;
  position: absolute;
  left: 50%;
  top: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  .icon {
    font-size: 50px;
  }
  .text {
    font-size: 20px;
  }
}

.non-contactable-link {
  transition: color 0.2s ease;
  font-weight: 600;

  &:hover {
    color: #3975c7 !important;
    text-decoration: underline;
    font-weight: 600;
  }
}
.expand-collapse {
  max-width: fit-content;
  position: absolute;
  right: 3%;
  z-index: 5;
}
