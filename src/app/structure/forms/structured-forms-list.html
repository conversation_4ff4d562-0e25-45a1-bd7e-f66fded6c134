<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Forms</strong>
        </span>
    </div>
    <div class="card-block">
        <div class="row">
            <div class="col-lg-12">
                <div style="text-align:center;width:100%;position: absolute;top: 10px;" *ngIf="dataLoadingMsg">
                    <img src="./assets/img/loader/loading.gif" />
                </div>
                <div class="mb-5">
                    <form class="new-forms">
                        <table  [hidden]="dataLoadingMsg" class="table table-hover" id="form-send-dt" width="100%"></table>
                    </form>
                    <!-- <table class="table table-hover dataTable no-footer dtr-inline" id="form-send-dtt" width="100%" role="grid">
                        <thead>
                            <tr role="row">
                                <th class="sorting_disabled" rowspan="1" colspan="1" style="width: 20%;" aria-label="#">#</th>
                                <th class="sorting_disabled" tabindex="0" aria-controls="form-send-dt" rowspan="1" colspan="1" style="width: 80%;">Form Name</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr role="row" class="odd" *ngFor="let form of forms;let i=index;" (click)="showFormPriview()">
                                <td tabindex="0">{{i+1}}</td><td class="sorting_1">{{form.name}}</td>
                            </tr>
                        </tbody>
                    </table> -->
                </div>
            </div>
        </div>        
    </div>
</section>

<div class="modal fade bd-example-modal-lg forward-modal" id="priviewForm" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">    
                    <h4 class="modal-title" id="exampleModalLabel">{{activeForms.name}}</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                      <span aria-hidden="true">&times;</span>
                      </button>
                </div>
                <div class="modal-body">
                    <form [formGroup]="structuredFormSend" id="structuredFormSend">
                        <div class="card-block">
                            <div class="cat__core__card-sidebar">
                                <div class="cat__apps__messaging__header">
                                    <!-- <input class="form-control cat__apps__messaging__header__input ng-untouched ng-pristine ng-valid" id="chat-with-modal-search-box" placeholder="Search Here" #userSearch> -->
                                    <!-- <i class="icmn-search"></i> -->
                                    <select class="form-control select2" formControlName="tenantUsers" id="tenantUsers" multiple> 
                                        <option></option>
                                        <option *ngFor="let tenantUser of tenantUsers" value="{{tenantUser.userId}}"> {{tenantUser.displayname}} </option>
                                    </select>
                                    <!-- <button type="submit" class="btn btn-secondary" data-dismiss="modal">Send</button> -->
                                </div>
                            </div>
                        </div>
                        <div class="row ">
                            <div class="structured-form-modal" [innerHTML]="formContent"></div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <span class="chatwith-modal-tip">
                         <!-- <img src="./assets/modules/dummy-assets/common/img/chatwith-tip.png" />
                         <span class="modal-footer-text">Click the user/group name to chat with </span> -->
                    </span>
                    <button type="submit" class="btn btn-secondary" (click)="sendForm()" data-dismiss="modal">Send</button>
                </div>
            </div>
        </div>
    </div>