import { Component, OnInit,ElementRef, Renderer } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { SharedService } from '../shared/sharedServices';
import { Store, StoreService } from '../shared/storeService';
import { isBlank } from 'app/utils/utils';
declare var $: any;
declare var swal: any;

@Component({
  selector: 'app-tag-definitions',
  templateUrl: './tag-definitions.component.html'
})
export class TagDefinitionsComponent implements OnInit {
  tagList = [];
  dTable;
  activeFormTag;
  dataLoadingMsg=true;
  isFirstLoad = true;
  crossTenantChangeSubscriber:any;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private _formBuild: FormBuilder,
    private _structureService: StructureService,
    private _ToolTipService: ToolTipService,
    private storeService: StoreService,
    private _sharedService: SharedService,
    elementRef: ElementRef,
    renderer: Renderer
  ) {
    renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
      if(event.target.id == 'deleteFormTag') {
       // console.log(this.activeFormTag);
        this.deleteFormTag();
      }
      else if(event.target.id=='editFormTag'){       
        this._structureService.setCookie('formTagId',this.activeFormTag.id,1);
        this.router.navigate(['/forms/add-form-tag']);        
      }
    });
    this.crossTenantChangeSubscriber = this._sharedService.crossTenantChange.subscribe((onInboxData) => {
      if(this.router.url.indexOf('forms/form-tags') > -1) {
        this.ngOnInit();
      }
    });
   }

  ngOnInit() {    
   
    var page = 'form-tag-definitions';
    $(".form-tag").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00001') });

    this.getFormTagList();
  }
  ngOnDestroy() {
    if(this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
  }

  addFormTag(){
    this._structureService.deleteCookie('formTagId');
    this.router.navigate(['/forms/add-form-tag']);
  }
  
  deleteFormTag(){
    /*this._structureService.checkFormDraftExists(this.activeFormTag.id).then((result)=>{
    if(result==1){*/
      var IsEnterClicked=false;
      $(document).keypress((event)=> {
        if (event.keyCode == 13) {
            console.log("Enter pressed");
            IsEnterClicked=true;           
        }
      });
      swal({
        title: "Are you sure?",
        text: "You are going to remove this form types",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true
      },(confirm) => {
        console.log('IsEnterClicked',IsEnterClicked);
        if(IsEnterClicked){
          IsEnterClicked=false;
            swal.close();   
            return false;
        }
        if(confirm) {
        var paramsDelete = {tagName:this.activeFormTag.tagName,tagMeta:this.activeFormTag.tagMeta,id:this.activeFormTag.id,deleted:1};
        this._structureService.deleteUpdateFormTag(paramsDelete).then((result)=>{
          if(result['updateFormTag'] && result['updateFormTag'].id){
            if(this.tagList.indexOf(this.activeFormTag)>-1){
              this.tagList.splice(this.tagList.indexOf(this.activeFormTag),1);
              this.populateMsgtags();
            }
            var notify = $.notify('Success! Form Type deleted');
            setTimeout(()=>{
                notify.update({'type': 'success', 'message': '<strong>Success! Form Type deleted</strong>'});            
            }, 1000);
          }
          else{
            setTimeout(()=>{
              var notify = $.notify('Error! Error Occured');
              notify.update({'type': 'danger', 'message': '<strong>Error! Error Occured</strong>'});
              this.router.navigate(['/forms/form-tags']);
          }, 1000);
          }
        });
      }
      })
    /*}else{
      swal({
        title: "Warning",
        text: "Found draft(s) under this form tag. You can't remove the tag until that draft(s) deleted or completed.",
        type:'error',
        
      });
    }
  });*/
    
  }

  getFormTagList(){
    const storedSearchKey = this.storeService.getStoredData(Store.SEARCH_FORM_TYPES);
    if (storedSearchKey && storedSearchKey.searchText) {
      this._structureService.notifySearchFilterApplied(true);
    }
    this.isFirstLoad = true;
    this._structureService.getFormTags().then((result)=>{
        this.tagList = JSON.parse(JSON.stringify( result['getSessionTenant'].formTags));
        this.populateMsgtags();
    });
  }

  populateMsgtags(){
        if (this.dTable) {
           this.dTable.destroy();
         }
         var isTrue = false;    
         if(this.tagList.length > 99){
           isTrue = true;
         }
    this.dTable = $('#formTags').DataTable({
      autoWidth: false,
      "order": [[ 1, "asc" ]],
      responsive: true,
      //bDeferRender:true,
      //processing: true,
      // oLanguage: {
      // sLoadingRecords: "Please wait - loading..."
      // },
      retrieve: true,
      //pagination: true,
      serching: true, 
      stateSave: true,  
      paging: isTrue,
      bInfo: isTrue,
      lengthMenu: [
        [100, 250, 500, 1000, -1],
        [100, 250, 500, 1000, 'All']
      ],
      data: this.tagList,
      fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
        $(nRow).on('click', () => {
          this.activeFormTag = aData;
        });
      },
      'fnCreatedRow': function (nRow, aData, iDataIndex) {
        $(nRow).attr('id', 'my' + iDataIndex); // or whatever you choose to set as the id
    },
      columns: [{ title: '#' }, { title: 'Form Types', data: 'tagName' }, { title: 'Actions' }],
      columnDefs: [
      {
        data: null,
        orderable: false,
        width: "5%",
        targets: 0
        },
        { 
          data: null,
          targets: 1,
          width: "25%",
          render: function (document, type, row) {
            let actions = '';                
            actions +=`
          <label id="lbl${row.id}"> ${document} </label>
            <input type="text" name="abc" id="editinp${row.id}" class="hidden-cl" value="${document}" >
            `
            return actions;
          }
      },
      {
          data: null,
          orderable: false,
          render: function (document, type, row) {
          let actions = '';                
          actions +=`
          <a id="editFormTag" href="javascript: void(0);" class="delete${row.id} cat__core__link--underlined mr-3"><i id="editFormTag" class="icmn-pencil"></i> Edit</a>
          <a id="deleteFormTag" href="javascript: void(0);" class="edit${row.id} cat__core__link--underlined mr-3"><i id="deleteFormTag" class="icmn-cross"></i> Delete</a>             
          `
          return actions;
        },
        width: "15%",
        targets: 2
      }]
    });
    document.querySelector('input[aria-controls="formTags"]').addEventListener('keyup', (event) => {
      const inputElement = event.target as HTMLInputElement;
      const searchText = inputElement.value;
      const storedTextValue = this.storeService.getStoredData(Store.SEARCH_FORM_TYPES);
      if (isBlank(searchText)) {
        this._structureService.notifySearchFilterApplied(false);
        this.storeService.removeData(Store.SEARCH_FORM_TYPES);
      } else if (!storedTextValue || (storedTextValue && storedTextValue.searchText !== searchText)) {
        this.storeService.storeData(Store.SEARCH_FORM_TYPES, { searchText });
      }
    });

    this.dTable.on( 'order.dt search.dt', () => {
      this.dTable.column(0, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
            cell.innerHTML = i + 1;
          });
      })
      .draw();
    this.dataLoadingMsg = false;
    if (this.isFirstLoad) {
      const inputElement = document.querySelector('input[aria-controls="formTags"]') as HTMLInputElement;
      const storedTextValue = this.storeService.getStoredData(Store.SEARCH_FORM_TYPES);
      if (storedTextValue && storedTextValue.searchText) {
        inputElement.value = storedTextValue.searchText;
        this.dTable.search(storedTextValue.searchText).draw();
      }
      this.isFirstLoad = false;
    }
  }
}