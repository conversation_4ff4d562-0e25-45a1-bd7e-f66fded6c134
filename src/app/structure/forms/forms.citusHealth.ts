import { Component, OnInit, <PERSON><PERSON><PERSON><PERSON>, AfterViewInit } from '@angular/core';
import { Router } from '@angular/router';
import { PapaParseService } from 'ngx-papaparse';
import { DomSanitizer } from '@angular/platform-browser';
import { StructureService } from '../structure.service';
import { SharedService } from '../shared/sharedServices';
import { FormpPipe, formSearchPipe } from './formp.pipe';
import { APIs } from 'app/constants/apis';

declare const $: any;
declare const swal: any;

@Component({
  selector: 'manage_form',
  templateUrl: './forms.html',
  providers: [FormpPipe, formSearchPipe]
})

export class FormsComponent implements OnInit, OnDestroy, AfterViewInit {
  formsUrl: any;
  structureFormContent: any;
  crossTenantChangeSubscriber: any;
  previlages: any;
  formData;
  signDocFile;
  serverUrlCustom:any;
  buttonDisabled: any;
  result;
  showLoading: any;
  userData: any = '';
  data: any;
  userDataConfig: any;
  enableMoveForm;
  fileChangevalue;
  domainName;
  showLoader = true;
  constructor(
    private _structureService: StructureService,
    private router: Router,
    private sanitizer: DomSanitizer,
    private papa: PapaParseService,
    private _sharedService: SharedService
  ) {
    this.crossTenantChangeSubscriber = this._sharedService.crossTenantChange.subscribe((onInboxData) => {
      this.ngOnInit();
    });
  }
  ngOnInit() {
    this.buttonDisabled = true;
    this.showLoading = true;
    this.domainName = this._structureService.domainName;
    this.previlages = [];

    this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
    this.enableMoveForm = this.userDataConfig.enable_move_form;
    let manageTenants = this.userData.privileges;
    manageTenants = typeof (manageTenants) === 'undefined' ? this._structureService.getCookie('userPrivileges') : manageTenants;
    manageTenants = manageTenants.split(',');
    for (var i = 0; i < manageTenants.length; i++) {
      this.previlages[manageTenants[i]] = true;
    }
    if (this.previlages.enableMoveForm && (this.enableMoveForm == 1 || this.enableMoveForm == true)) {
      this.enableMoveForm = true;
    } else {
      this.enableMoveForm = false;
    }

    const userDetails = JSON.parse(this._structureService.userDetails);
    const forActivityLog = "&userTenant=" + userDetails.tenantId + "&crossTenantID=" + userDetails.crossTenantId + "&platform=" + this._structureService.platform + "&environment=" + this._structureService.environment + "&version=" + this._structureService.version;
    const formsUrl = this._structureService.machFormUrl + APIs.machFormIndex + '?authenticationToken=' + btoa(userDetails.authenticationToken) + '&displayName=' + userDetails.displayName + '&fromCallBell=1&tenantId=' + this._structureService.getCookie("crossTenantId") + '&userId=' + userDetails.userId + forActivityLog;
    const iFrameContent = '<iframe id="manage-forms" onload="javascript:parent.scrollTo(0,0);" height="2000" allowTransparency="true" frameborder="0" scrolling="yes" style="width:100%;border:none" src="' + formsUrl + '" ></iframe>';
    this.structureFormContent = this.sanitizer.bypassSecurityTrustHtml(iFrameContent);
  }
  moveForm() {

    this.router.navigate(['/forms/copy-form']);
  }
  UploadBtnAction() {
    $('#data-modal-upload').modal('show');
    this.buttonDisabled = true;
    this.showLoading = false;
    $('#bulkDirectFile').val('');
    $('#result-report').replaceWith('<p id="result-report"><p>');
  }
  uploadFile(event) {
    $('#result-report').replaceWith('<p id="result-report"><p>');
    const fileList: FileList = event.target.files;
    if (fileList.length > 0) {
      this.buttonDisabled = false;
      this.signDocFile = fileList[0];
      this.formData = new FormData();
      this.formData.append('bulkDirectFile', this.signDocFile);
      this.fileChangevalue = true;
      const self = this;
      const csvData = this.signDocFile;
      const options = {
        complete: (results, file) => {
          self.result = results.data;
        },
        header: true,
        skipEmptyLines: true
      };
      if (csvData) {
        this.papa.parse(csvData, options);
      }
    } else {
      this.buttonDisabled = true;
    }
    return false;
  }
  saveUploadFile() {
    if (this.result && this.result.length > 0) {

      swal({
        title: "Are you sure?",
        text: "You are going to import Form data from the CSV",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true
      }, () => {
        this.showLoading = true;
        this.buttonDisabled = true;


        this._structureService.setCsvUploadedFormData(this.result).then((data: any) => {

          $("#result-report").append("<br><b> Form data Imported</b><br>");
          });
          this.showLoading = false;
        }
      );
    } else {
      this.buttonDisabled = false;
    }
  }
  ngOnDestroy() {
    if (this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }

    if(this._structureService.activeEditFormId&& this._structureService.activeEditFormId!=''){
      this._structureService.socket.emit("activeEditForms", { userId: this.userData.userId , formId: this._structureService.activeEditFormId,actionType:"delete"});
      this._structureService.activeEditFormId = '';
    }
    this.showLoader = false;
  }

  ngAfterViewInit() {
    const iframe = document.getElementById('manage-forms') as HTMLIFrameElement;
    iframe.addEventListener('load', () => {
      this.showLoader = false;
    });
  }
}
