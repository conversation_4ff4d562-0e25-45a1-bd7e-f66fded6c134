<!-- START: forms/basic-forms-elements -->
    <section class="card">
        <div class="card-header">
            <span class="cat__core__title">
            <strong>Manage Forms</strong>
            <a [hidden]="domainName=='Prod'" style="float:right;margin-left:10px;display:none;" class="pull-right btn btn-sm btn-primary" (click)="UploadBtnAction()">Import Form Data</a> 
            <!--Starts Modal for upload -->
            <div class="modal fade data-modal" id="data-modal-upload" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="exampleModalLabel">Import Form Data</h4>
                        <span *ngIf="showLoading" 
                        style="color:teal;text-align: center;padding:20px 20px;">Please wait while we process
                            your request... <i class="fa fa-refresh fa-spin" ></i></span>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-12">
                            <input type="file" id="bulkDirectFile" name="bulkDirectFile" (change)="uploadFile($event)">  
                            </div>
                        </div>
                        <div style="padding-left: 10px;padding-top: 20px;" class="row">
                            <p id="result-report"></p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-info" [disabled]="buttonDisabled" (click)="saveUploadFile()">Upload</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
            </div>
    <!--End Modal for upload -->
            <a (click)="moveForm()" class="pull-right btn btn-sm btn-primary" *ngIf="enableMoveForm==true;"> Move Form<i class="ml-1"></i></a>
        </span>
        </div>
        <div class="card-block">
                <div [innerHTML]="structureFormContent"></div>
        </div>
    </section>
<!-- END: forms/basic-forms-elements -->
<ch-loader [showLoader]="showLoader" [showInPageCenter]="true"></ch-loader>
