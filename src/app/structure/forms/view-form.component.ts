import { <PERSON><PERSON>nent, On<PERSON>nit, OnDestroy, Input, ElementRef, Renderer, ViewChild } from '@angular/core';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute, Params, NavigationEnd, NavigationStart } from '@angular/router';
import { StructureService } from '../structure.service';
import { DomSanitizer } from '@angular/platform-browser';
import { SharedService } from '../shared/sharedServices';
import { InboxService } from '../inbox/inbox.service';
import { Guid } from "guid-typescript";
import { isBlank } from 'app/utils/utils';
import { Subscription } from 'rxjs';
import { APIs } from 'app/constants/apis';

var jstz = require('jstz');
const timezone = jstz.determine();
let moment = require('moment/moment');

@Component({
  selector: 'fill-form',
  templateUrl: 'view-form.html'
})

export class viewFormComponent implements OnInit, OnDestroy {
  formContent: any;
  activeStrucuredForms: any;
  activeForms: any;
  userData: any;
  formDataSingle: any;
  jsonformData: any;
  userDataConfig: any;
  
  autoSave: any;
  savaAsDraftPatient: any;
  savaAsDraftStaff: any;
  crossTenantChangeSubscriber: any;
  private socketEventSubscriptions: Subscription;
  constructor(
    private _structureService: StructureService,
    private _inboxService: InboxService,
    private sanitizer: DomSanitizer,
    private router: Router,
    private _sharedService: SharedService
  ) {
    this.crossTenantChangeSubscriber = this._sharedService.crossTenantChange.subscribe(
      (onInboxData) => {
        this.ngOnInit();
      }
    );
  }
  ngOnInit() {
    this.autoSave = 0;
    this.savaAsDraftStaff = 0;
    this.savaAsDraftPatient = 0;
    
    this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
    console.log('this.userDataConfig'+this._structureService.userDataConfig);
    if (this.userDataConfig.enable_form_auto_save == 1) {
      this.autoSave = 1;
    }

    // if (this.userDataConfig.enable_form_save_draft_patient == 1) {
    //   this.savaAsDraftPatient = 1;
    // }
    // if (this.userDataConfig.enable_form_save_draft_staff == 1) {
    //   this.savaAsDraftStaff = 1;
    // }
    //nov192019
    // window.addEventListener("message", function (event) {
    //   try {
    //     if (event.data) {
    //       var height = (event.data).split("=");
    //       if(height.length&&height[0]=="scrollTopformsubmit"){
    //         localStorage.setItem('autosavecalledformid',"0");
    //         localStorage.setItem('autosavecalled',"false");
    //         $(".structured-form-modal").scrollTop(0);
    //         window.scrollTo(0, 0);
    //       }
    //       else if(height.length&&height[0]=="gotocreatesendform"){
    //        $(".searchbar input").val("");
    //        $(".send-form-list ").find('.formname').first().trigger('click');
    //       }else if(height.length&&height[0]=="autosavecalled"){
    //            // localStorage.setItem('autosavecalled', height[1]);


    //       }else if(height.length&&height[0]=="autosavecalledformid"){

    //         //localStorage.setItem('autosavecalledformid', height[1]);
    //      }
    //      else if(height.length&&height[0]=="saveasdraftidtodeleted"){

    //       //localStorage.setItem('saveasdraftidtodeleted', height[1]);
    //    }
    //      else {

    //       if (typeof (parseInt(height[1])) == "number") {
    //         console.log("222222222222222222222222");
    //         $("#structured-form-dekstop").height(height[1] + "px")
    //       } else {
    //         $("#structured-form-dekstop").height("5000px")
    //       }
    //     }
    //     }
    //   } catch (error) {
    //     console.log("error");
    //   }
    // }, false);

    var formData = localStorage.getItem('formData');
    console.log("formData", formData);
    if (typeof (formData) != "undefined" && formData != '') {
      this.activeForms = JSON.parse(decodeURIComponent(formData));
    } else {
      if (typeof this._structureService.getCookie('formData') == 'object') {
        this.activeForms = this._structureService.getCookie('formData');
      } else {
        this.activeForms = JSON.parse(decodeURIComponent(this._structureService.getCookie('formData')));
      }
    }

    //this.activeForms = JSON.parse(decodeURIComponent(this._structureService.getCookie('formData')));
    console.log(this.activeForms, "this.activeStrucuredForms");
    
    this.userData = JSON.parse(this._structureService.userDetails);
    this.socketEventSubscriptions = this._structureService.subscribeSocketEvent('saveAsDraft').subscribe((data) => {
      console.log("========================")
      console.log("========================")
      console.log("========================")
      console.log(data)
      console.log("Submission id=======>"+data.data.submissionId);
      if (data) {
        this._structureService.notifyMessage({
          messge: 'The Form ' + this.activeForms.formName + ' Saved As Draft',
          delay: 1000,
          type: 'success'
        });
        if (this.userDataConfig.enable_collaborate_edit == 1) {
          var enable_collaborate_edit = "collaborateDraft-Enabled";
        } else {
          var enable_collaborate_edit = "";
        }
        var activityData = {
          activityName: "Form Saved As Draft" + enable_collaborate_edit,
          activityType: "forms",
          activityDescription: this.userData.displayName + '(' + this.userData.userId + ') has saved the form as Draft- ' + this.activeForms.formName + ' (' + this.activeForms.formId + ') with submission id  - '+data.data.submissionId
        };
        this._structureService.trackActivity(activityData);

      }
      console.log("========================")
      console.log("========================")
      console.log("========================")

    });

    if (this.userDataConfig.enable_form_save_draft_patient == 1 && this.activeForms.enableSaveDraftPatient == 1) {
      this.savaAsDraftPatient = 1;
    }
    if (this.userDataConfig.enable_form_save_draft_staff == 1 && this.activeForms.enableSaveDraftStaff == 1) {
      this.savaAsDraftStaff = 1;
    }
    if (this.activeForms.staff_submission_id != 0) {
      console.log(this.savaAsDraftPatient);
      console.log("data=======4444==========");
      console.log(this.activeForms);
      console.log(this.userDataConfig.enable_form_save_draft_patient )
      console.log(this.userDataConfig.enable_form_save_draft_staff )
      console.log(this.savaAsDraftStaff);
      console.log("data===========");
      console.log(this.activeForms.staff_submission_id);
      console.log("data===============");
      var formsubid = Number(this.activeForms.staff_submission_id);
      this.setIframeUrlN();


      //here the scripts to load form submitted values
    }
    else {

      this.setIframeUrl();
    }
  }
  ngOnDestroy() {
    if (this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
     /**Unsubscribe the socket event subscriptions */
    if(this.socketEventSubscriptions) this.socketEventSubscriptions.unsubscribe();
  }
  setIframeUrlN() {
    console.log('>>>>>>>>>>>>>>>>>>>>>>>>', this.activeForms);
    var clientId = this._structureService.socket.io.engine.id;
    let formId = this.activeForms.formId;
    let fromId = this.activeForms.fromId;
    let sentId = this.activeForms.sentId;
    let recipientEmail = this.activeForms.fromUsername;
    let formName = this.activeForms.formName;
    let categoryvalue = this.activeForms.categoryvalue;
    let fromName = this.activeForms.patientName;
    let toName = this.activeForms.fromName;
    if (this.activeForms.page && this.activeForms.page == "formWorlists") {
      fromName = this.activeForms.fname + ' ' + this.activeForms.lname;
      formId = this.activeForms.form_id;
      fromId = this.activeForms.from_id;
      sentId = this.activeForms.sent_id;
      formName = this.activeForms.form_name;
      categoryvalue = 1;
      toName = this.activeForms.createdUser;
      recipientEmail = this.activeForms.createdUserEmail;
    }

    var tokenId = this._structureService.getCookie('authenticationToken');
    var facing_new = this.activeForms.facing_new;
    if (this.userDataConfig.save_as_draft_message_interval) {

      localStorage.setItem('saveAsDraftMessageInterval', this.userDataConfig.save_as_draft_message_interval);
    }
    if (this.userDataConfig.enable_collaborate_edit) {

      localStorage.setItem('enableCollaborateEdit', this.userDataConfig.enable_collaborate_edit);
    }
    const viewPatientId = !isBlank(this.activeForms.patient_id) ? this.activeForms.patient_id:  !isBlank(this.activeForms.caregiver_userid) ? this.activeForms.caregiver_userid: undefined ;
    var enable_sftp_integration=this.userDataConfig.enable_sftp_integration==1?"&enable_sftp_integration="+true:"&enable_sftp_integration="+false;
    var unique="&uniqueFormIdentity="+Guid.create();
    var confirmActionPrefill=this.activeForms.confirmActionPrefill==1?"&confirmActionPrefill=true":"&confirmActionPrefill=false";
    var fax_queue_show_warning=this.userDataConfig.fax_queue_show_warning == 1?"&fax_queue_show_warning="+true+unique+confirmActionPrefill:"&fax_queue_show_warning="+false+unique+confirmActionPrefill;
    var formsUrl = this._structureService.machFormUrl + APIs.machFormEmbed + '?id=' + formId + "&patientId=" + viewPatientId +"&loginUserId="
      + this.userData.userId + "&tenantId=" + ((this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')) ? this._structureService.getCookie('crossTenantId') : this._structureService.getCookie('tenantId')) + "&toId=" + fromId
      + "&sentId=" + sentId + "&categoryId=" + categoryvalue + "&environment=" + this._structureService.environment + "&formName=" + encodeURIComponent(formName) + "&formId=" + formId + "&apiVersion=" + this._structureService.version + "&toName=" + toName + "&fromName=" + fromName + "&serverBaseUrl=" + encodeURIComponent(this._structureService.serverUrlCustom) +
      "&fromMob=false&isWorkingHour=" + this._inboxService.checkInfusionHours(true).isWorkingHours + "&roleId=" + this.userData.group + "&recipientEmail=" + recipientEmail + "&staffSubmissionId=" + this.activeForms.staff_submission_id + "&autoSave=" + this.autoSave + "&savaAsDraftStaff=" + this.savaAsDraftStaff + "&savaAsDraftPatient=" + this.savaAsDraftPatient + "&clientId=" + clientId + "&authenticationToken=" + tokenId + "&facing_new=" + facing_new + "&saveAsDraftMessageInterval=" + localStorage.getItem('saveAsDraftMessageInterval') + "&enableCollaborateEdit=" + localStorage.getItem('enableCollaborateEdit')+enable_sftp_integration+fax_queue_show_warning + (this._structureService.isMultiAdmissionsEnabled ? `&admissionId=${this.activeForms.admissionId}`: '');
    this.formContent = this.sanitizer.bypassSecurityTrustResourceUrl(formsUrl);
  }
  setIframeUrl() {
    let associatedId = undefined;
    if (this.activeForms.caregiver_userid) {
      associatedId = this.activeForms.caregiver_userid;
    }
    const viewPatientId = !isBlank(this.activeForms.patient_id) ? this.activeForms.patient_id:  !isBlank(this.activeForms.caregiver_userid) ? this.activeForms.caregiver_userid: undefined ;
    var clientId = this._structureService.socket.io.engine.id;
    var tokenId = this._structureService.getCookie('authenticationToken');
    var facing_new = this.activeForms.facing_new;
    var enable_sftp_integration=this.userDataConfig.enable_sftp_integration==1?"&enable_sftp_integration="+true:"&enable_sftp_integration="+false;
    var unique="&uniqueFormIdentity="+Guid.create();
    var fax_queue_show_warning=this.userDataConfig.fax_queue_show_warning == 1?"&fax_queue_show_warning="+true+unique:"&fax_queue_show_warning="+false+unique;
    var formsUrl = this._structureService.machFormUrl + APIs.machFormEmbed + '?id=' + this.activeForms.formId + "&patientId=" + viewPatientId + "&associatedId=" + associatedId + "&loginUserId="
      + this.userData.userId + "&tenantId=" + ((this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')) ? this._structureService.getCookie('crossTenantId') : this._structureService.getCookie('tenantId')) + "&toId=" + this.activeForms.fromId
      + "&sentId=" + this.activeForms.sentId + "&categoryId=" + this.activeForms.categoryvalue + "&environment=" + this._structureService.environment + "&formName=" + encodeURIComponent(this.activeForms.formName) + "&formId=" +
      this.activeForms.formId + "&apiVersion=" + this._structureService.version + "&toName=" + this.activeForms.fromName + "&fromName=" +
      this.activeForms.patientName + "&serverBaseUrl=" + encodeURIComponent(this._structureService.serverUrlCustom) +
      "&fromMob=false&isWorkingHour=" + this._inboxService.checkInfusionHours(true).isWorkingHours + "&roleId=" + this.userData.group + "&recipientEmail=" + this.activeForms.fromUsername + "&autoSave=" + this.autoSave + "&savaAsDraftStaff=" + this.savaAsDraftStaff + "&savaAsDraftPatient=" + this.savaAsDraftPatient + "&clientId=" + clientId + "&authenticationToken=" + tokenId + "&facing_new=" + facing_new + "&saveAsDraftMessageInterval=" + localStorage.getItem('saveAsDraftMessageInterval')+enable_sftp_integration+fax_queue_show_warning + (this._structureService.isMultiAdmissionsEnabled ? `&admissionId=${this.activeForms.admissionId}`: '');
    this.formContent = this.sanitizer.bypassSecurityTrustResourceUrl(formsUrl);
  }
  goBack() {
    if (this.activeForms.fromArchive) {
      this.router.navigate(['/archive']);
    } else {
      if (this.activeForms.page && this.activeForms.page == "formWorlists") {
        this.router.navigate(['/forms/list']);
      } else if (this.activeForms.page && this.activeForms.page == "myFormWorlists") {
        this.router.navigate(['/forms/worklist']);
      } else {
        this.router.navigate(['/inbox']);
      }

    }
  }
}
