<style type="text/css">
    .cat__core__step__desc p {
        font-weight: bold !important;
    }
    
    .cat__core__step__digit {
        cursor: pointer;
    }
    
    .signature-req-block:hover {
        background: #e6e6e6;
    }
    
    .status-active {
        background-color: #e6e6e6;
    }
</style>
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Supply Submissions</strong>
        </span>
    </div>
    <div class="card-block">
        <div>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
                <li class="breadcrumb-item">Supply Submissions</li>
                <li class="info-widget">
                    <span class="chatwith-modal-tip">  
                                <img  src="./assets/modules/dummy-assets/common/img/chatwith-tip.png">                            
                                <span  class="modal-footer-text-sign">Click on a tab to see the forms entries under a category (Completed, Pending, Archived).</span>
                    </span>
                </li>
            </ol>
            <div style="float:left;">
            </div>
        </div>
        <div class="row actions-list" style="margin-bottom: 40px;">
            <div class="col-sm-4 signedblock-border signature-req-block" [ngClass]="{'status-active':isActive == 'completed'}">
                <a href="javascript:void(0)" (click)="changeFormData('COMPLETED')">
                    <div class="row" style="height:100%">
                        <div class="col-sm-3"></div>
                        <div class="col-sm-6">
                            <div class="cat__core__step pending-block">
                                <span class="cat__core__step__digit"><i class="fa fa-check completed"></i></span>
                                <div class="cat__core__step__desc">
                                    <span class="cat__core__step__title">{{completedCount}}</span>
                                    <p>Completed</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3"></div>
                    </div>
                </a>
            </div>
            <div class="col-sm-4 signedblock-border signature-req-block" [ngClass]="{'status-active':isActive == 'pending'}">
                <a href="javascript:void(0)" (click)="changeFormData('PENDING')">
                    <div class="row" style="height:100%">
                        <div class="col-sm-3"></div>
                        <div class="col-sm-6">
                            <div class="cat__core__step cat__core no-border signed-block">
                                <span class="cat__core__step__digit"><i class="fa fa-history waiting-others"></i></span>
                                <div class="cat__core__step__desc">
                                    <span class="cat__core__step__title">{{pendingCount}}</span>
                                    <p>Pending</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3"></div>
                    </div>
                </a>
            </div>
            <div class="col-sm-4 signature-req-block" [ngClass]="{'status-active':isActive == 'archive'}">
                <a href="javascript:void(0)" (click)="changeFormData('ARCHIVE')">
                    <div class="row" style="height:100%">
                        <div class="col-sm-3"></div>
                        <div class="col-sm-6">
                            <div class="cat__core__step pending-block">
                                <span class="cat__core__step__digit requests-archive-icon">
                                <i><img src="./assets/img/archive-icon.png"></i>
                            </span>
                                <div class="cat__core__step__desc">
                                    <span class="cat__core__step__title">{{archiveCount}}</span>
                                    <p style="padding-left: 12%;">Archived</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-3"></div>
                    </div>
                </a>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                <div style="text-align:center;width:100%;" *ngIf="dataLoadingMsg">
                    <img src="./assets/img/loader/loading.gif" />
                </div>
                <div class="mb-5">
                    <form class="new-forms">
                        <table [hidden]="dataLoadingMsg" class="table table-hover" id="form-list-dt" width="100%"></table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>