<style type="text/css">
/*save as draft msg*/
.saveAsDraft-message{
    color: #000;
    font-size: 14px;
  }
 .faildraft{
    color: red;
    width: 73%;
    position: absolute;
    margin-left: 35px;
  }
  /*save as draft msg*/
</style>
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong *ngIf="!checkDraft">{{'LABELS.EDIT_FORM' | translate}}</strong>
            <strong *ngIf="checkDraft">{{'LABELS.FORM_PREVIEW' | translate}}</strong>
             <!--save as draft msg-->
            <span class="saveAsDraft-message"></span>
            <input type="hidden" id="last_save_draft">
            <input type="hidden" id="error_save_draft">
            <!--save as draft msg-->
            <a *ngIf="!checkDraft && !hideBackBtn" (click)="goBackToPreviousUrl()" id="back-form-report" class="pull-right btn btn-sm btn-primary mr-2 mb-2">{{'BUTTONS.BACK' | translate}}</a>
            <a *ngIf="checkDraft && !hideBackBtn" (click)="goBack()" id="back-form-report-draft" class="pull-right btn btn-sm btn-primary mr-2 mb-2">{{'BUTTONS.BACK' | translate}}</a>
        </span>
        <br>
        <span class="cat__core__title" *ngIf="activeForm.message && activeForm.message!=''">
            <span>{{'LABELS.MESSAGE' | translate}} - {{activeForm.message}}</span>
        </span>
    </div>
    <div class="col-md-10 forms-details" *ngIf="practitionerName !== null">
            <label class="control-label"><strong>{{'LABELS.PRACTITIONER_NAME' | translate}} : </strong> &nbsp; {{practitionerName}} </label>
        </div>
    <div class="col-md-10 forms-details" *ngIf="recepientName != null">
            <label class="control-label" *ngIf="(facingvalue==1 || facingvalue==2) && patientAssociation==true"><strong>{{'LABELS.PATIENT_NAME' | translate}} :</strong> &nbsp;{{recepientName}}</label>
            <label class="control-label" *ngIf="facingvalue==0"><strong>{{'LABELS.PATIENT_NAME' | translate}} :  </strong> &nbsp; {{recepientName}}</label>
        </div>
    <div class="col-md-10 forms-details" *ngIf="completedFormRecipients">
        <label class="control-label"><strong>{{'LABELS.COMPLETED_FORM_RECIPIENTS' | translate}} :</strong> &nbsp;{{completedFormRecipients}}</label>
    </div>
    <div class="row forms-details" *ngIf="facingvalue==0||facingvalue==2">        
        <div class="col-md-10">
            <label class="control-label"><strong>{{'LABELS.MESSAGE' | translate}}</strong></label>
             <!-- check form stabilization -->
            <textarea class="form-control select-rec" id="draftArea" placeholder="Enter your optional message here" (blur)="onBlurEvent($event)" ></textarea>
            <!-- check form stabilization -->
        </div>
    </div>
     <!-- check form stabilization -->
    <input type="hidden" id="response">
     <button  type="button" style="display:none;" id="responsebtnedit" (click)="notify()">{{'BUTTONS.SEND' | translate}}</button>
     <button  type="button" style="display:none;" id="failNotifyEdit" (click)="failNotifyEdit()"></button>
      <!-- check form stabilization -->
    <div class="card-block" [ngClass]="{'block-form-cursor':fromArchive}">
        <div class="row" class="structured-form-modal h-100">
            <div  style="display:none" [innerHTML]="structureFormContent"></div>
            <iframe [ngClass]="{'block-form-mouse':fromArchive}" onload="$('html, body').animate({ scrollTop: 0 },'slow');$('.structured-form-modal').scrollTop(0);" allowTransparency="true" class="structured-form-dekstop" id="structured-form-dekstop" frameborder="0" scrolling="no" style="width:100%;border:none" [src]="formContent" ></iframe>
        </div>
    </div>
</section>