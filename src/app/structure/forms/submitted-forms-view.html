<app-sign-pad (newcomp)="sendSignature($event);">
</app-sign-pad>
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>{{ 'LABELS.FORM' | translate }} - {{strucuredFormsData.formName}}</strong>
            <button *ngIf="!formLandingFlow" href="javascript: void(0);" (click)="_location.back();keepActiveTab(activeTab);"
                id="back-form-report" class="pull-right btn btn-sm btn-primary mr-2 mb-2">{{'BUTTONS.BACK' |
                translate}}</button>
            <button class="pull-right btn btn-sm btn-primary mr-2 mb-2" *ngIf="showAllowEdit == true"
                [disabled]="disableAllowEdit" (click)="allowEdit()" id="dwld_btn"
                title="{{'BUTTONS.ALLOWEDIT' | translate}}"><i data-edit="" class="icmn-pencil2"></i></button>
            <button class="pull-right btn btn-sm btn-primary mr-2 mb-2" *ngIf="showArchive == true" [disabled]="disableArchive"
                (click)="archiveForm()" id="dwld_btn" title="{{'BUTTONS.ARCHIVE' | translate}}"><i
                    class="icmn-box-add"></i></button>
            <button class="pull-right btn btn-sm btn-primary mr-2 mb-2" *ngIf="showHistoryButton == true"
                (click)="listHistory()" title="{{'BUTTONS.HISTORY' | translate}}"><i class="icmn-history"></i></button>
            <button class="pull-right btn btn-sm btn-primary mr-2 mb-2"
            *ngIf="(activeStrucuredForms.allowEdit == 1 || activeStrucuredForms.allow_edit == 1) && 
            ((activeStrucuredForms.recipient_id == userData.userId && activeStrucuredForms.staffFacing != '1') || 
            (activeStrucuredForms.staff_facing == '1' && activeStrucuredForms.from_id == userData.userId)) && !formLandingFlow"
                (click)="editSubmittedForm()" title="{{'BUTTONS.EDIT' | translate}}"><i class="icmn-pen"></i></button>
            <button class="pull-right btn btn-sm btn-primary mr-2 mb-2" (click)="getPdfTaggedForm()" id="dwld_btn"
                title="{{'BUTTONS.DOWNLOAD' | translate}}"><i data-download="" class="icmn-download"></i></button>

        </span>
    </div>

    <div class="card-block pb-1 view-form-data-desk">
        <div class="row">
            <div class="col-md-6">
                <div class="row">
                    <label class="col-md-4">{{ 'LABELS.SENT_BY' | translate }} :</label>
                    <div class="col-md-8">
                        <p>{{activeStrucuredForms.createdUser || activeStrucuredForms.fromName}}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6"
                *ngIf="((activeStrucuredForms.facing_new != '2' &&  activeStrucuredForms.staff_facing != '2' && activeStrucuredForms.patientName)||(activeStrucuredForms.facing_new == '0' && activeStrucuredForms.patientName ))">
                <div class="row">
                    <label class="col-md-4" *ngIf="activeStrucuredForms.patient_id != '0'">{{ 'LABELS.PATIENT_NAME' |
                        translate }} :</label>
                    <label class="col-md-4" *ngIf="activeStrucuredForms.patient_id == '0'">{{ 'LABELS.FILLED_BY' |
                        translate }} :</label>

                    <div class="col-md-8">
                        <p *ngIf="activeStrucuredForms.caregiver_userid">{{activeStrucuredForms.caregiver_displayname}}
                            ({{activeStrucuredForms.patientName}})</p>
                        <p *ngIf="!activeStrucuredForms.caregiver_userid">{{activeStrucuredForms.patientName}}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6"
                *ngIf="(activeStrucuredForms.facing_new == '2' || activeStrucuredForms.staff_facing =='2') && patientassociationPract == 1">
                <div class="row">
                    <label class="col-md-4">{{ 'LABELS.PATIENT_NAME' | translate }} :</label>
                    <div class="col-md-8">
                        <p>{{activeStrucuredForms.caregiver_displayname}}</p>
                    </div>
                </div>
            </div>
            <div class="col-md-6" *ngIf="patientassociationPract == 1 && patient_dob">
                <div class="row">
                    <label class="col-md-4">{{ 'LABELS.DOB' | translate }} :</label>
                    <div class="col-md-8">
                        <p>{{patient_dob}} </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6" *ngIf="patientassociationPract == 1 && mrn">
                <div class="row">
                    <label class="col-md-4">{{ 'LABELS.MRN' | translate }} :</label>
                    <div class="col-md-8">
                        <p>{{mrn}} </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6"
                *ngIf="activeStrucuredForms.facing_new == '2' || activeStrucuredForms.staff_facing =='2' ">
                <div class="row">
                    <label class="col-md-4">{{ 'LABELS.FILLED_BY' | translate }} :</label>
                    <div class="col-md-8">
                        <p>{{activeStrucuredForms.patientName}} </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="row">
                    <label class="col-md-4">{{ 'LABELS.FORM_NAME' | translate }} :</label>
                    <div class="col-md-8">
                        <p>{{activeStrucuredForms.form_name || activeStrucuredForms.formName}} </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6" *ngIf="_structureService.isMultiAdmissionsEnabled">
                <div class="row">
                    <label class="col-md-4">{{ 'ADMISSION.LABELS.ADMISSION' | translate }} :</label>
                    <div class="col-md-8">
                        <p>{{ activeStrucuredForms.admissionName }} </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="row">
                    <label class="col-md-4">{{ 'LABELS.SUBMITTED_ON' | translate }} :</label>
                    <div class="col-md-8">
                        <p>{{activeStrucuredForms.formattedSentOn}} </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="row">
                    <label class="col-md-4">{{ 'LABELS.FORM_TYPES' | translate }} :</label>
                    <div class="col-md-8">
                        <p *ngIf="activeStrucuredForms.facing_new == '2'">{{
                            'OPTIONS.WORKFLOW.STAFF_PRACTITIONER_FACING' | translate }}</p>
                        <p
                            *ngIf="activeStrucuredForms.facing_new != '2' && ( activeStrucuredForms.facing_new == '1' || activeStrucuredForms.staff_facing == '1')">
                            {{ 'OPTIONS.WORKFLOW.STAFF_PARTNER_FACING' | translate }}</p>
                        <p *ngIf="activeStrucuredForms.facing_new == '0' || activeStrucuredForms.staff_facing == '0'">{{
                            'OPTIONS.WORKFLOW.PATIENT_FACING' | translate }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="card-block pt-1 view-form-data-desk">
        <div class="row">
            <div class="col-lg-12">
                <div class="mb-5">
                    <form class="new-forms">
                        <table class="table table-hover nowrap" id="example1" width="100%">
                            <thead>
                                <tr class="row">
                                    <th class="col-sm-6"> {{ 'LABELS.QUESTIONS' | translate }} </th>
                                    <th class="col-sm-6"> {{ 'LABELS.ANSWERS' | translate }} </th>
                                </tr>
                            </thead>
                            <tbody>

                                <tr class="odd gradeX row"
                                    *ngFor="let formsData of strucuredFormsData.structuredFormResult;let i=index;"
                                    [hidden]="(formsData.value=='') || (!formsData.label && !formsData.value) || conditionArray.indexOf(formsData.element_id)!=-1">
                                    <td class="py-1" *ngIf="formsData.element_type != 'textarea' || formsData.value.length<newLineLimit" 
                                        [ngClass]="{'col-sm-6':formsData.element_type != 'signature','col-sm-4':formsData.element_type == 'signature'}">
                                        <span [ngClass]="{'bold':formsData.element_type=='section'}"
                                            [innerHTML]="formsData.label?formsData.label:''"></span>

                                        <p *ngIf="formsData.element_type == 'section' && formsData.value !='&nbsp;'"
                                            [innerHTML]="formsData.value?formsData.value:''"></p>
                                    </td>

                                    <td class="py-1" *ngIf="formsData.element_type != 'textarea' || formsData.value.length<newLineLimit" 
                                        [ngClass]="{'col-sm-6':formsData.element_type != 'signature','col-sm-8':formsData.element_type == 'signature'}">
                                        <span [innerHTML]="(formsData.value?formsData.value:'')|safeHtml"
                                            *ngIf="formsData.element_type!='section' && formsData.element_type!='signature'"></span>
                                        <img *ngIf="formsData.element_type == 'signature'"
                                            src="{{formsData.singpad_img_src}}" class="img-responsive"
                                            style="float:right;max-width:100%;height:auto;">
                                    </td>
                                    <td class="py-1 col-sm-12" colspan="2" *ngIf="formsData.element_type == 'textarea' && formsData.value.length>=newLineLimit">
                                        <span [innerHTML]="formsData.label?formsData.label:''"></span> : 
                                        <br><br>
                                        <span [innerHTML]="(formsData.value?formsData.value:'')|safeHtml"></span>
                                    </td>
                                </tr>
                                <tr (click)="openChatSignature()" *ngIf="!signUrl" [hidden]="true">
                                    <span class="sign-message-icon"></span>
                                </tr>
                                <tr *ngIf="signUrl">
                                    <p><img src="{{signUrl}}" /></p>
                                </tr>
                            </tbody>
                        </table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
<app-form-history [showHistory]="showHistory" [isActive]="activeStrucuredForms.formStatus"
    (closeModal)="closeHistoryModal($event)" [formDetails]="activeStrucuredForms"></app-form-history>