<style>
    .error {
        color: red;
    }
</style>
<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
          <strong>{{title}}</strong>
      </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/forms/manage']">Manage Forms</a></li>
            <li class="breadcrumb-item">{{title}}</li>
        </ol>
        <div class="row">
            <div class="col-lg-12">
                <!-- <h5 class="text-black"><strong>Signed Documents</strong></h5>
              <p class="text-muted">Element: read <a href="https://datatables.net/" target="_blank">official documentation<small><i class="icmn-link ml-1"></i></small></a></p> -->
                <div class="mb-5">
                    <form [formGroup]="tagForm" id="tagForm" (ngSubmit)="updateShortcut(f)" #f="ngForm">
                        <div class="form-body">
                            <div class="form-group row">
                                <label class="col-md-3 control-label">Choose Form*</label>
                                <div class="col-md-9">
                                        <select class="form-control" name="FormID" id="FormID" formControlName="FormID">
                                                <option value="" selected *ngIf="tenantForms.length">Select Form</option>
                                                <option value="" selected *ngIf="tenantForms.length==0" [disabled]="tenantForms.length==0">No forms found</option>
                                                <option *ngFor="let Forms of tenantForms" value="{{Forms.form_id}}"
                                                >{{Forms.form_name}}</option>
                                                
                                            </select>


                                   <!--<input type="text" class="form-control" name="FormID" id="FormID" formControlName="FormID" placeholder="Enter FormID" />-->
                                    <span id="error-FormID" class="error"></span>
                                </div>
                            </div>
                            <!--<div class="form-group row">
                                <label class="col-md-3 control-label">TeanantID *</label>
                                <div class="col-md-9">
                                    <input type="text" class="form-control" name="TeanantID" id="TeanantID" formControlName="TeanantID" placeholder="Enter TeanantID" />
                                    <span id="error-TeanantID" class="error"></span>
                                </div>
                            </div>-->
                                                        <div class="form-group row">
                                <label class="col-md-3 control-label">Tenant Name *</label>
                                <div class="col-md-9">
                                    <select class="form-control" name="TeanantID" id="TeanantID" formControlName="TeanantID">
                                            <option value="" selected>Select Tenant</option>
                                            <option value="" selected *ngIf="crossTenantOptions.length==0" [disabled]="crossTenantOptions.length==0">No tenants found</option>
                                            <option *ngFor="let tenant of crossTenantOptions" value="{{tenant.id}}"
                                            >{{tenant.tenantName}}</option>
                                            
                                        </select><span id="error-TeanantID" class="error"></span>
              
  </div>
                            </div>
                 


                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">{{btnLabel}}</button>
                                <button type="button" (click)="cancelShortcut()" class="btn btn-default">Cancel</button>
                            </div>

                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- END: tables/datatables -->