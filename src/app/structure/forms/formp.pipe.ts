import { Pipe, PipeTransform,Inject,forwardRef } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser'
import { DatePipe } from '@angular/common'; 
import { ScheduledFormComponent } from './scheduledForms/scheduledForms.citushealth';

@Pipe({ name: 'safeHtml'})
export class SafeHtmlPipe implements PipeTransform  {
  constructor(private sanitized: DomSanitizer) {}
  transform(value) {
    console.log("pipi value==>",value);
    return this.sanitized.bypassSecurityTrustHtml(value);
  }
}

@Pipe({
  name: 'formp'
})
export class FormpPipe implements PipeTransform {
  /*  transform(items: any[], exponent: string): any {
   // console.log(items.signatureStatus);
    return items.filter(items => items.signatureStatus === exponent);
  } */

  constructor(public datepipe: DatePipe){}
  transform(input: any): any {
    // var shortFormat = (new Date(input).toDateString() === new Date().toDateString()) ? 'hh:mm a' : 'MMM dd hh:mm a';
    var shortFormat = (new Date(input).toDateString() === new Date().toDateString()) ? 'hh:mm a' : (new Date(input).getFullYear() === new Date().getFullYear()) ? 'MMM dd hh:mm a' : 'MMM dd, y hh:mm a';
    var fdate = parseInt(input) * 1;
    return this.datepipe.transform(fdate, shortFormat);
  } 
}
@Pipe({
  name: 'dateTime'
})
export class DateTimePipe implements PipeTransform {
  /*  transform(items: any[], exponent: string): any {
   // console.log(items.signatureStatus);
    return items.filter(items => items.signatureStatus === exponent);
  } */

  constructor(public datepipe: DatePipe){}
  transform(input: any): any {
    var shortFormat = (new Date(input).toDateString() === new Date().toDateString()) ? 'hh:mm a' : (new Date(input).getFullYear() === new Date().getFullYear()) ? 'MMM dd hh:mm a' : 'MMM dd, y hh:mm a';
    return this.datepipe.transform(input, shortFormat);
  } 
}
@Pipe({
  name: 'formSearch'
})
export class formSearchPipe implements PipeTransform {
  transform(forms:any, searchKey:any): any {
    if (!forms) { 
      return [];
    } else {
      if(searchKey) {
        console.log(searchKey);
        return forms.filter(item => item['name'].toLowerCase().indexOf(searchKey.toLowerCase()) != -1);  
      } else {
        return forms;
      }
    }
  }
}
@Pipe({
  name: 'formRecipient',
})
export class formRecipientPipe implements PipeTransform {
  app;
  constructor(@Inject(forwardRef(() => ScheduledFormComponent)) app:ScheduledFormComponent) {
    this.app = app;
  }
  transform(scheduledFormRecipients: any[], filter: any): any {
    
    if (!scheduledFormRecipients || !filter) {
      return scheduledFormRecipients;
    }
    let filteredItems = scheduledFormRecipients.filter(item =>item.status==filter.status);
    //this.app.setRecipientMultipleValue();
    // console.log("scheduledFormRecipientsFiltered=====>",filteredItems);
    return filteredItems;
    
  }
}
@Pipe({
  name: 'formFilter',
})
export class formFilterPipe implements PipeTransform {
  transform(items: any[]): any {

    console.log("scheduledFormRecipients=====>",items);
    if (!items) {
      return items;
    }
    return items.filter(item => item.stafFacing=="false"); 
  }
}

@Pipe({
  name: 'patientRole',
})
export class patientRolePipe implements PipeTransform {
  transform(items: any[],patient=true): any {

    console.log("scheduledFormRecipients Patient=====>",patient);
    if (!items) {
      return items;
    }
    return items.filter(item => {
      if(patient) {
        if((item.citus_role_id=='3')) return true;
      } else {
        if(item.citus_role_id!='3') return true;
      }     
    }); 
  }
}

@Pipe({
  name: 'excludePatientCaregiver',
})
export class exPatientCaregiverPipe implements PipeTransform {
  transform(items: any[]): any {
    console.log("scheduledFormRecipients=====>",items);
    if (!items) {
      return items;
    }
    return items.filter(item => item.citus_role_id != '3'); 
  }
}

@Pipe({
  name: 'excludeMasterRole',
})
export class exMasterRolePipe implements PipeTransform {
  transform(items: any[],type:any): any {
    console.log("Exclude Master=====>", items);
    console.log("Exclude Master =====> Type ===>", type);
    if (!items) {
      return items;
    }
    return items.filter(item => item.master_role == 0); 
  }
}
@Pipe({
  name: 'caregiverOrAlternate',
})
export class caregiverOrAlternatePipe implements PipeTransform {
  transform(items: any[], filter: String): any {
      if (!items || !filter) {
          return items;
      }
      return items.filter(item => item.name !== filter);
  }
} 