<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Form - {{(activeForms.formName)?activeForms.formName:activeForms.form_name}} <span class="dosUpdate"></span></strong>
            <a (click)="goBack()" id="back-form-report" class="pull-right btn btn-sm btn-primary mr-2 mb-2">Back</a>
        </span>
        <br>
        <span class="cat__core__title" *ngIf="activeForms.message && activeForms.message!=''">
            <span>Message - {{activeForms.message}}</span>
        </span>
    </div>
    <div class="card-block" [ngClass]="{'block-form-cursor':activeForms.fromArchive}">
        <div class="row" class="structured-form-modal">
            <iframe [ngClass]="{'block-form-mouse':activeForms.fromArchive}" onload="$('html, body').animate({ scrollTop: 0 },'slow');$('.structured-form-modal').scrollTop(0);" allowTransparency="true" class="structured-form-dekstop" id="structured-form-dekstop" frameborder="0" scrolling="no" style="width:100%;border:none" [src]="formContent" ></iframe>
        </div>
    </div>
</section>