import { Component, OnInit, OnDestroy, Input, Output, ElementRef, EventEmitter, Renderer, ViewChild, Renderer2, ChangeDetectorRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { Router, ActivatedRoute} from '@angular/router';
import { StructureService } from '../structure.service';
import { FormsService } from './forms.service';
import { DomSanitizer } from '@angular/platform-browser';
import { SharedService } from '../shared/sharedServices';
import { ModalComponent } from '../signatures/modal.component';
import { InboxService } from '../inbox/inbox.service';
import { DatePipe } from '@angular/common';
import { ToolTipService } from '../tool-tip.service';
import { EnrollService } from '../user-registration/enroll.service';
import { GlobalDataShareService } from '../shared/global-data-share.service';
import { Guid } from "guid-typescript";
import { Subject, Subscription } from 'rxjs';
import { isBlank, isPresent } from '../../utils/utils';
import { CONSTANTS } from 'app/constants/constants';
import { UserService } from 'app/services/user/user.service';
import { StaticDataService } from '../static-data.service';
import { APIs } from 'app/constants/apis';
import { HttpService } from 'app/services/http/http.service';
import { userFilterPipe } from '../inbox/inbox-modal-filter';

var jstz = require('jstz');
declare var signaturePad: any;
declare var $: any;
declare var swal: any;
const timezone = jstz.determine();
declare var NProgress: any;
let moment = require('moment/moment');

@Component({
  selector: 'form_send_form',
  // templateUrl: './structured-forms-list.html'
  styleUrls: ['./send-forms-list.scss'],
  templateUrl: './send-forms-list.html',
  providers: [DatePipe]
})
//TODO: Need to revamp all swal popups [CHP-6901].
export class SendFormDataComponent implements OnInit, OnDestroy {
  @Input('loadFormFrom') loadFormFrom: any;
  @Input('fwdFormId') fwdFormId: any;
  @Input('fwdTo') fwdTo: any;
  @Input('forwardToId') forwardToId: any;
  @Input('forwardEntryId') forwardEntryId: any;
  @Input('prefillMode') prefillMode: any;
  @Input('prefillFormEntryid') prefillFormEntryid: any;
  @Input('selectedPatient') autoPopulatePatientInfo: any;
  @Output() submitForward = new EventEmitter();
  @Output() submitForPAH = new EventEmitter();
  @ViewChild(ModalComponent) child: ModalComponent;
  hasData = false;
  patientId; selectedData;
  tagName;
  dob;
  // form stabilization
   messageadd : any;
   messagechoose = null;
  // form stabilization 
  showPatientForm = false;
  totalCountInviteSearch;
  noRecordMessage = false;
  datam; searchResetFlag = 0;
  strucuredFormsData: any = '';
  searchString;
  searchInboxkeyword;
  searchAssociatePatient;
  userData: any = '';
  signUrl: any = '';
  dTable;
  forms: any = '';
  orderChange = false;
  formSubmissionId: any = '';
  activeForms: any = '';
  dataLoadingMsg = true;
  patientDataLoadingMsg = false;
  structuredFormSend: FormGroup;
  tenantUsers: any = [];
  formContent: any = '';
  result: any = '';
  allowRecipientRoles: any;
  patientAssociation = true;
  assosiatedPatients: any = [];
  selectedRecipients: any = [];
  isPatientDriven=false;
  formdrivenFlowTooltip='';
  patientDrivenFlowTooltip='';
  selectedFlow='formsdriven';
  selectedAssosiatePatientDrivenName='';
  patientAssociatedForms=[];
  patientAssociatedActiveForm=false;
  selectedAssosiatePatientDrivenId:number;
  searchInboxkeywordForm='';
  selectedAssosiatePatientDisplayName='';
  PatientsDrivenList: any=[];
  PatientDrivenSearch='';
  associatedFormsLoading=false;
  PatientDrivenLoading=false;
  selectedRecipientNames: any = [];
  caregiverPatients: any = [];
  selectedAssosiatePatient: any = '';
  selectedAssosiatePatientName: any = '';
  newPatient: FormGroup;
  staffFill = false;
  iscellNumber = false;
  isEmail = false;
  stafffillfirst = false;
  isFirstName = false;
  isLastName = false;
  isZipCode = false;
  isDob = false;
  isReqEmail = false;
  isReqFirstName = false;
  isReqLastName = false;
  isReqZipCode = false;
  orderChangeStatus = false;
  afterSubmit = false;
  topsend = false;
  isReqDob = false;
  userDataConfig: any = '';
  oneTimeCopy: any = '';
  autoSave: any = '';
  userDataUpdateSubs: any;
  clearForm: any;
  savaAsDraftStaff: any = '';
  savaAsDraftPatient: any = '';
  receipientCount: any = '';
  receipientCountSaveDraft: any = '';
  associatePatientLoading = false;
  recipientLoading = false;
  autosavecalled: any;
  autosavecalledformid: any;
  associatePatientNew: any;
  saveAsDraftLocalTime:any;
  popupmsg = '';
  clearFlag: any = '';
  showClearForm: any = '';
  formSendInProgress = false;
  formSendIberfore = false;
  assocpdata = { "email": '', "firstname": '', "lastname": '', "dob": '', "cell": '', "zipcode": '', "tenantId": '', "operation": 0, "dday": null, "dmonth": null, "dyear": null, mrn: null }
  patientTags = [];
  crossTenantChangeSubscriber: any;
  selectedForm: any;
  searchData: any;
  searchRequired = true;
  userConfig: any;
  enableAlternateContacts :boolean;
  searchInTenantIds;
  showApplessMenu = false;
  indexformsite='0';
  selectedRecipient: any = {
    isVirtual:false
  };
  frmSendMode = new FormGroup({
    mode: new FormControl()
  });
  selectedRecipientsArray = [];
  siteIds: any;
  assocSiteId : any;
  assocSiteIdCopy : any;
  emptySiteCondtion : any;
  isSiteMandatory : any = false;
  assocSelected : any;
  selectedAssosiatePatientDrivenSiteId: number;
  selectedAssociatedPatientSiteId: number;
  hideSiteSelection: boolean;
  eventsSubject: Subject<void> = new Subject<void>();
  patientAssociationOffDiv :boolean;
  siteId:any;
  siteRequired : boolean;
  submitted : boolean;
  siteChoosed : boolean;
  faxQIntegration:boolean;
  multiSiteEnable: boolean;
  siteDiv=true;
  staffFacing:any;
  loadForm = false;
  loadedForm = false;
  showRecipientsForSendCompletedForm = false;
  associatePatientId = '';
  selectedRecipientsForSendCompleteForm = '';
  isPatientFacing = false;
  selectedSiteIds:any;
  showPageLoader = true;
  showLoaderMessage = '';
  private socketEventSubscriptions: Subscription[] = [];
  formSubmitSubscription: Subscription;
  selectedAdmission: any;
  selectedAssociatedPatient: any;
  filterSiteLabel = '';
  isNonContactableUsersExists = false;
  showNonContactableUsers = false;
  observer;
  isCollapsed = false;
  isFullWidthCollapsed = false;
  externalUserId: string;
  selectedPatientDetails;
  brightreeFormLandingFlow = false;
  private previousUserTagIds: Set<string> = new Set();

  constructor(
    public _structureService: StructureService,
    private route: ActivatedRoute,
    private router: Router,
    private _formsService: FormsService,
    private sanitizer: DomSanitizer,
    public _sharedService: SharedService,
    elementRef: ElementRef,
    private _ToolTipService: ToolTipService,
    renderer: Renderer,
    private _formBuild: FormBuilder,
    public _inboxService: InboxService,
    private _enrollService: EnrollService,
    public _GlobalDataShareService: GlobalDataShareService,
    private render: Renderer2,
    private cdRef: ChangeDetectorRef,
    private userService: UserService,
    private staticDataService: StaticDataService,
    private httpService: HttpService,
    public modalFilter: userFilterPipe
  ) {
    this.filterSiteLabel = this.staticDataService.getSiteLabelBasedOnMultiAdmission('FILTER_BY_SITES');
    this.crossTenantChangeSubscriber = this._sharedService.crossTenantChange.subscribe(
      (onInboxData) => {
        this.ngOnInit();
      }
    );
    renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
      let eventClass = $(event.target).attr("class");
      if (eventClass.includes('text-muted')) {
        return;
      }
      if (eventClass == "remove") {
        if (this.brightreeFormLandingFlow) {
          return;
        }
        this.removeSelectedRecipient(event.target.id);
      }
      //console.log("renderer.listen",event);
      var idDetails = ['associate-search-input', 'associate-li', 'associate-ul', 'associate-search', 'tagsInput', 'recipient-li', 'recipient-ul', 'recipient-search',];
      if (!idDetails.includes(event.target.id) && event.target.className.indexOf('recipient-li') == -1) {
        let clear = false;
        let from = "";
        if ($('#recipient-ul').css('display') == 'block') {
          from = "R";
        }
        if (this.selectedAssosiatePatient) {
          //clear =false;
          $("input#associate-search-input").val(this.selectedAssosiatePatientName);
          //$(".associate-close").css("display","block");
        }
        console.log("render :call enableOrDisableUiLI.......");
        this.enableOrDisableUiLI(false, clear, from);
      } else {
        if (event.target.id == 'associate-search-input' && $('#recipient-ul').css('display') == 'block') {
          this.enableOrDisableUiLI(false, false, 'R');
        } else 
        if (event.target.id == 'tagsInput' && $('#associate-ul').css('display') == 'block') {
          this.enableOrDisableUiLI(false, false);
        } else if (this.isLoadFromPAH) {
          this.enableOrDisableUiLI(true, false, 'R');
        }
        if (this.selectedAssosiatePatient) {
          $("input#associate-search-input").val(this.selectedAssosiatePatientName);
        }
      }
    });
  }

  //aparna---
  notify(){


    // var intervalast =  setInterval(()=>{ 
       var resultlast = $("#response").val();
      if(resultlast != ""){
       //  clearInterval(intervalast);
         this.sendForm(1);
         
      }else{
        this.stafffillfirst = false;
        this.formSendInProgress = false;
        this._structureService.notifyMessage({
          messge: 'Form sending failed',
          delay: 1000
        });
      }
     //}, 2000);
    
  }

  failNotify(){
    $("#textmessage").text("");
    $("#newloader").hide();
    this.stafffillfirst = false;
    this.formSendInProgress = false;
    this._structureService.notifyMessage({
      messge: 'Form sending failed',
      delay: 1000
    });
  }

  get isLoadFromPAH() {
    return this.loadFormFrom === 'PAH';
  }

  get isFormSelected() {
    return this.isLoadFromPAH ? this.selectedForm : true;
  }

  ngOnInit() {

  //save as draft msg
    $(".saveAsDraft-message").removeClass('faildraft');
  //save as draft msg
    let page = 'form-tag-definitions';
   
    // Get externalUserId from URL if available
    //This will be reverted after obtaining the session token from the URL and passing it during the initial link loading.
    this.route.queryParams.subscribe(params => {
      if (params['externalUserId']) {
        localStorage.setItem('externalUserId', params['externalUserId']);
        this.externalUserId = params['externalUserId'];
        this.brightreeFormLandingFlow = true;
      }
    });
 
   
    
    this.saveAsDraftLocalTime = moment((moment().unix()) * 1000).format('h:mm a');
    this.searchInTenantIds = this._GlobalDataShareService.getTenantIds(this._structureService.getCookie('userPrivileges'));
    this.receipientCount = 1;
    this.receipientCountSaveDraft = 1;

    this.formSendIberfore = true;
    this.showClearForm = false;
    console.log("=========ff===============")
    console.log("======ff==================")
    this.route.queryParams.subscribe(params => {
      this.patientId = params['patient'];
    });
    this.autosavecalled = false
    this.autosavecalledformid = 0;
    localStorage.setItem('autosavecalledformid', "0");
    localStorage.setItem('autosavecalled', "false");
    this.clearFlag = 0;
    this.setSelectValueFromPah();
  
    this.userData = JSON.parse(this._structureService.userDetails);
    this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
    console.log('this.userDetails'+this._structureService.userDetails);

    this.userConfig = this._structureService.getUserdata().config;
    this.multiSiteEnable = this.userData.config.enable_multisite == 1 ? true : false;
    this.enableAlternateContacts=this.userData.config.default_patients_workflow == 'alternate_contacts'? true : false;
    this.isPatientDriven=(this.userDataConfig.enable_forms==1 && this.userDataConfig.enable_patient_drieven_flow_forms==1)?true:false;
    console.log('this.userDetails', this.userData);
    console.log('this.userDetails.tenantId', this.userData.tenantId);
    this.autoSave = 0;
    this.oneTimeCopy = 0;
    this.savaAsDraftStaff = 0;
    this.savaAsDraftPatient = 0;
    this.clearForm = 0;
    this.assocSiteId = 0;
    this.emptySiteCondtion  = false;
    console.log('this.userDataConfig ', this.userDataConfig);
    
    if (this.userDataConfig.enable_form_auto_save == 1) {
      this.autoSave = 1;
    }
    
    const tagGetData = '?group=3';
    const tagTypes = ["2"]; // Message Tag =1, User Tag =2 , Document Tag =3
    this._enrollService.getTagsByGroup(tagGetData, tagTypes).then((data: any) => {
      this.patientTags = data;
    });
    this.socketEventSubscriptions.push(
    this._structureService.subscribeSocketEvent('submissionId').subscribe((data) => {
    //this.notify();
    })
    );
    this.socketEventSubscriptions.push(
    this._structureService.subscribeSocketEvent('saveFillandSend').subscribe((data) => {
      setTimeout(() => {
        this.render.removeClass(document.body, 'stop-scrolling');
      }, 100);
      console.log("========================")
      console.log("========================")
      console.log("========================")
      console.log(data)


      this.formSubmissionId = data.data
      console.log("========================")
      console.log("========================")
      console.log("========================")

    })
    );
    this.socketEventSubscriptions.push(
    this._structureService.subscribeSocketEvent('saveAsDraft').subscribe((data) => {
      setTimeout(() => {
        this.render.removeClass(document.body, 'stop-scrolling');
      }, 100);
      console.log("========================")
      console.log("========================")
      console.log("========================")
      console.log(data)
      console.log("Submission id=======>"+data.data.submissionId);
      if (data) {
        if (this.activeForms.externalFileExchange == true) {
          this._structureService.notifyMessage({
            messge: 'The Form ' + this.activeForms.name + ' Saved As Draft.',
            delay: 1000,
            type: 'success'
          });
        }
        if (this.userDataConfig.enable_collaborate_edit == 1) {
          var enable_collaborate_edit = "collaborateDraft-Enabled";
        } else {
          var enable_collaborate_edit = "";
        }

        var activityData = {
          activityName: "Form Saved As Draft" + enable_collaborate_edit,
          activityType: "forms",
          activityDescription: this.userData.displayName + ' has saved the form as Draft-for Patient- ' + this.selectedAssosiatePatient + ' -' + this.activeForms.name + ' - ' + this.activeForms.id+' with submission id  - '+data.data.submissionId
        };
        this._structureService.trackActivity(activityData);

      }
      console.log("========================")
      console.log("========================")
      console.log("========================")

    })
    );
    this.socketEventSubscriptions.push(
    this._structureService.subscribeSocketEvent('activityTrack').subscribe((data) => {
      setTimeout(() => {
        this.render.removeClass(document.body, 'stop-scrolling');
      }, 100);
      localStorage.setItem('autosavecalledformid', "0");
      localStorage.setItem('autosavecalled', "false");
      if (this.selectedAssosiatePatient) {
        var patientId = this.selectedAssosiatePatient;
      } else {
        var patientId = this.userData.userId;
      }
      if (data.formData.fromMob == 'false' && data.formData.staffFacing == 'true') {
        var activityData = {
          activityName: "Submit Form " + this._sharedService.myFormsType,
          activityType: "forms",
          activityDescription: this.userData.displayName + ' has completed staff facing form ' + data.formData.formName + ' (' + data.formData.form_id + ')'
        };
        var successMessage=data.formData.form_Success_message;
        this._structureService.trackActivity(activityData);
        //Add configuration in form type Default Outgoing Filing Center (From Citus Health) after Form Submit.
        var formData = {
          "userid": this.userData.userId,
          "tenantId": data.formData.tenant_id,
          "recipient_id": data.formData.recipient_id,
          "formId": data.formData.form_id,
          "staffFacing": data.formData.staffFacing,
          "form_submission_id": data.formData.submissionId,
          "form_name": data.formData.formName,
          "patient_id": data.formData.recipient_id,
          "id": "",
          "patientUser": patientId,
          "orderChange": this.orderChange
        }
        console.log(formData);
        console.log("============orderChange================");
        console.log(this.orderChange);
        console.log(this.orderChange);
        console.log(this.orderChange);
        console.log("=============orderChange===============");
        console.log(this.oneTimeCopy);
        if (data.formData.submissionId) {
          if (this.oneTimeCopy == 0 || this._sharedService.myFormsType == "DRAFTS") {
            this._formsService.filingCenterSubmittedFormdata(formData).then((data) => {
              var notifyResult: any = data;
              if (notifyResult.defaultFromFilingCenterSubmitjson && this.userDataConfig.enable_progress_note_integration == 1) {
                if (notifyResult.defaultFromFilingCenterSubmitjson != false && (notifyResult.identity_value_Patient != ''
                  || notifyResult.identity_value_Patient != null)
                  && notifyResult.patientAssociation == true
                  && notifyResult.CPRUSERNO != '' || notifyResult.CPRUSERNO != null
                  && notifyResult.CPRUSERNAME != '' || notifyResult.CPRUSERNAME != null) {
                  activityData.activityName = "Form Copied to Filing Center as JSON";
                  activityData.activityType = "structured forms";
                  activityData.activityDescription = this.userData.displayName + " archived form " + formData.form_name + " (formId " + formData.formId + ") successfully and copied the JSON file to filing center named " + notifyResult.defaultFromFilingCenterSubmitjson;
                  this._structureService.trackActivity(activityData);

                }
                if (notifyResult.defaultFromFilingCenterSubmitjson != false && notifyResult.identity_value_Patient == ''
                  || notifyResult.identity_value_Patient == null
                  || notifyResult.patientAssociation == false
                  || notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null
                  || notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null) {
                  activityData.activityName = "Failed Form Copy to Filing Center as JSON";
                  activityData.activityType = "structured forms";
                  var messageData = '';
                  var messageData1 = '';
                  if (notifyResult.patientAssociation == false && notifyResult.staffFacing == "true") {
                    messageData1 = "there is no patient associated with this form";
                  }
                  if ((notifyResult.identity_value_Patient == '' || notifyResult.identity_value_Patient == null) && notifyResult.patientAssociation == true) {
                    messageData += ", MRN";
                    if (notifyResult.patient_name && notifyResult.patient_name != "") {
                      messageData += " of " + notifyResult.patient_name;
                    }
                  }
                  let f1 = 0;
                  let f2 = 0;
                  if (notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null) {
                    f1 = 1;
                    messageData += ", USERNO";
                  }
                  if (notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null) {
                    f2 = 1;
                    messageData += ", USERNAME";
                  }
                  if ((f1 == 1 || f2 == 1) && notifyResult.staff_name && notifyResult.staff_name != "") {
                    messageData += " of " + notifyResult.staff_name;
                  }
                  if (notifyResult.patientAssociation == false && notifyResult.staffFacing == "false") {
                    messageData += "";
                  }
                  var removeComa = messageData.charAt(0);
                  if (removeComa == ',') {
                    messageData = messageData.substring(1);
                  }
                  if (messageData1 && messageData) {
                    var finalMessage = 'Generating Progress Note for Form ' + formData.form_name + ' failed due to ' + messageData1 + ' and missing  (' + messageData + ')';
                  }
                  else if (messageData1) {
                    var finalMessage = 'Generating Progress Note for Form ' + formData.form_name + ' failed due to ' + messageData1;
                  }
                  else if (messageData) {
                    var finalMessage = 'Generating Progress Note for Form ' + formData.form_name + ' failed due to missing (' + messageData + ')';
                  }
                  else {
                    var finalMessage = '';
                  }
                  if (finalMessage) {
                    activityData.activityDescription = this.userData.displayName + " send form " + formData.form_name + " (formId " + formData.formId + ") failed to copy the JSON file to filing center named " + notifyResult.defaultFromFilingCenterSubmitjson + "because" + messageData;
                    this._structureService.trackActivity(activityData);
                    // this._structureService.notifyMessage({
                    //   messge: finalMessage,
                    //   delay: 6000,
                    //   type: 'warning'
                    // });
                  }
                }
              }
            });
            this.oneTimeCopy = 1;
          }
          else {
            this.oneTimeCopy = 1;
          }
        }
      }
      this.afterSubmit = false;
      /*$("#textmessage").text("");
      $("#newloader").hide();*/
      if(this.router && this.router.url && this.router.url!='/forms/send/list'){
        swal({
          title:successMessage,
          text: '',
          icon: "success",
          showCancelButton: false,
          showConfirmButton: false,
          timer: 3000
         
        })
      }
     
      
    })
    );
    this.socketEventSubscriptions.push(
    this._structureService.subscribeSocketEvent('submittedData').subscribe((data) => {
      setTimeout(() => {
        this.render.removeClass(document.body, 'stop-scrolling');
      }, 100);
      /*$("#textmessage").text("");
      $("#newloader").hide();*/
      var successMessage=data.formData.form_Success_message;
      localStorage.setItem('autosavecalledformid', "0");
      localStorage.setItem('autosavecalled', "false");
      if (this.selectedAssosiatePatient) {
        var patientId = this.selectedAssosiatePatient;
      } else {
        var patientId = this.userData.userId;
      }
      if (this.userDataConfig.enable_collaborate_edit == 1) {
        var enable_collaborate_edit = "collaborateDraft-Enabled";
      } else {
        var enable_collaborate_edit = "";
      }
      var activityData = {
        activityName: "Submit Form " + this._sharedService.myFormsType + enable_collaborate_edit,
        activityType: "forms",
        activityDescription: this.userData.displayName + ' has completed staff facing form ' + data.formData.formName + ' (' + data.formData.form_id + ')'
      };

      ////qwerty
      this._structureService.trackActivity(activityData);
      var formData = {
        "userid": this.userData.userId,
        "tenantId": data.formData.tenant_id,
        "recipient_id": data.formData.recipient_id,
        "formId": data.formData.form_id,
        "staffFacing": data.formData.staffFacing,
        "form_submission_id": data.formData.submissionId,
        "form_id": data.formData.form_id,
        "form_name": data.formData.formName,
        "patient_id": "",
        "id": "",
        "patientUser": patientId,
        "formDatadraftid": localStorage.getItem('saveasdraftidtodeleted'),
        "orderChange": this.orderChange
      };

      if (localStorage.getItem('saveasdraftidtodeleted') != "false") {
        this._formsService.archiveDraftForm(formData).then((result: any) => {
          localStorage.setItem('saveasdraftidtodeleted', 'false');
        })
      }

      this.afterSubmit = false;
      console.log(formData);
      console.log(this.oneTimeCopy);
      if (data.formData.submissionId) {
        if (this.oneTimeCopy == 0 || this._sharedService.myFormsType == "DRAFTS") {
          this.oneTimeCopy = 1;
          this._formsService.filingCenterSubmittedFormdata(formData).then((data) => {
            var notifyResult: any = data;
            if (notifyResult.defaultFromFilingCenterSubmitjson && this.userDataConfig.enable_progress_note_integration == 1) {
              if (notifyResult.defaultFromFilingCenterSubmitjson != false && (notifyResult.identity_value_Patient != ''
                || notifyResult.identity_value_Patient != null)
                && notifyResult.patientAssociation == true
                && notifyResult.CPRUSERNO != '' || notifyResult.CPRUSERNO != null
                && notifyResult.CPRUSERNAME != '' || notifyResult.CPRUSERNAME != null) {
                activityData.activityName = "Form Copied to Filing Center as JSON";
                activityData.activityType = "structured forms";
                activityData.activityDescription = this.userData.displayName + " archived form " + formData.form_name + " (formId " + formData.formId + ") successfully and copied the JSON file to filing center named " + notifyResult.defaultFromFilingCenterSubmitjson;
                this._structureService.trackActivity(activityData);

              }
              if (notifyResult.defaultFromFilingCenterSubmitjson != false && notifyResult.identity_value_Patient == ''
                || notifyResult.identity_value_Patient == null
                || notifyResult.patientAssociation == false
                || notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null
                || notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null) {
                activityData.activityName = "Failed Form Copy to Filing Center as JSON";
                activityData.activityType = "structured forms";
                var messageData = '';
                var messageData1 = '';
                if (notifyResult.patientAssociation == false && notifyResult.staffFacing == "true") {
                  messageData1 = "there is no patient associated with this form";
                }
                if ((notifyResult.identity_value_Patient == '' || notifyResult.identity_value_Patient == null) && notifyResult.patientAssociation == true) {
                  messageData += ", MRN";
                  if (notifyResult.patient_name && notifyResult.patient_name != "") {
                    messageData += " of " + notifyResult.patient_name;
                  }
                }
                let f1 = 0;
                let f2 = 0;
                if (notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null) {
                  f1 = 1;
                  messageData += ", USERNO";
                }
                if (notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null) {
                  f2 = 1;
                  messageData += ", USERNAME";
                }
                if ((f1 == 1 || f2 == 1) && notifyResult.staff_name && notifyResult.staff_name != "") {
                  messageData += " of " + notifyResult.staff_name;
                }
                if (notifyResult.patientAssociation == false && notifyResult.staffFacing == "false") {
                  messageData += "";
                }
                var removeComa = messageData.charAt(0);
                if (removeComa == ',') {
                  messageData = messageData.substring(1);
                }
                if (messageData1 && messageData) {
                  var finalMessage = 'Generating Progress Note for Form ' + formData.form_name + ' failed due to ' + messageData1 + ' and missing (' + messageData + ')';
                }
                else if (messageData1) {
                  var finalMessage = 'Generating Progress Note for Form ' + formData.form_name + ' failed due to ' + messageData1;
                }
                else if (messageData) {
                  var finalMessage = 'Generating Progress Note for Form ' + formData.form_name + ' failed due to missing (' + messageData + ')';
                }
                else {
                  var finalMessage = '';
                }
                if (finalMessage) {
                  activityData.activityDescription = this.userData.displayName + " send form " + formData.form_name + " (formId " + formData.formId + ") failed to copy the JSON file to filing center named " + notifyResult.defaultFromFilingCenterSubmitjson + "because" + messageData;
                  this._structureService.trackActivity(activityData);
                }
              }
            }
            this.clearFormSelected();
            $("#newloader").hide();
          });
        }
      }
    })
    );

    function showmessage() {
      var data = {
        messge: 'It is a good practice to save your work frequently. Please click Save As Draft to save your work and avoid any data loss if your session times out222.',
        delay: 100000,
        type: 'success'

      };
      let notify = $.notify(data.messge);
      let type = data.type ? data.type : 'danger';
      let deley = data.delay ? data.delay : 1000;
      setTimeout(function () {
        notify.update({ 'type': type, 'message': '<strong>' + data.messge + '</strong>' });
      }, deley);
    }
    
    $('#tenantUsers').select2({
      placeholder: "Select Recipient(s)"
    });
    /*for addng new on the fly patients starts*/
    $(() => {
    
      $(document).on("focus", "#us-phone-mask-input", function () {
        $(this).mask("(000) 000-000000");
      });

      
    });
    $("#button-search").prop("disabled", "disabled");
    $("#button-clear").prop("disabled", "disabled");
    $("#searchTxt").on('keyup', function () {
      self.searchRequired = true;
      self.patientDataLoadingMsg = false;
      if ($(this).val() != "") {
        $("#button-search").prop("disabled", false);
        $("#button-clear").prop("disabled", false);
      } else {
        $("#button-search").prop("disabled", "disabled");
        $("#button-clear").prop("disabled", "disabled");
      }
    });
    
    $('html').click(function(e) {                    
      if(!$(e.target).hasClass('patient-search-input') )
      {
        $(".patientDriveUl").css("display",'none');                
      }
   }); 
    
    this.newPatient = new FormGroup({
      searchInput: new FormControl(),
      mrn: new FormControl()
    });
    var $eventSelect = $("#assosiatedPatients");
    $eventSelect.select2();
    var self = this;

    $eventSelect.on("change", (e) => {
      this.afterSubmit = false;
      this.orderChange = false;
      this.receipientCount = 1;
      var selectedPatient = $("#assosiatedPatients").val();
      console.log(selectedPatient);
      console.log("this.activeForms", this.activeForms);
      var patientAssociatename = $("#assosiatedPatients :selected").text().trim();
      if (selectedPatient) {
        if (selectedPatient.includes('{')) {
          selectedPatient = JSON.parse(selectedPatient);
        }
        if (this.activeForms.externalFileExchange != true) {
          var datacheck: any = {
            formid: this.activeForms.id,
            loggedinuser: this.userData.userId,
            patientid: selectedPatient,
            enableCollaborateEdit: this.userDataConfig.enable_collaborate_edit,
            admissionId: this._structureService.isMultiAdmissionsEnabled && !isBlank(this.selectedAdmission) ? this.selectedAdmission.id : undefined

          };
          this._formsService.checkDraftForm(datacheck).then((result: any) => {

            console.log(result.nodrafts);
            if (result.nodrafts == false && !this.brightreeFormLandingFlow) {
              var msgtxt = "You already have drafts for the selected user"
              swal({
                title: "Are you sure?",
                text: msgtxt,
                type: "warning",
                showCancelButton: true,
                cancelButtonClass: "btn-default",
                confirmButtonClass: "btn-warning",
                cancelButtonText: "Continue",
                confirmButtonText: "Go To Drafts",
                closeOnConfirm: true
              }, (confirm) => {
                if (confirm) {
                  // this.router.navigate(['/forms/worklist']);
                  this._structureService.setCookie('tabname','DRAFTS', 1);
                  this.emitSubmitFormToPAH()
                  if (this.userDataConfig.enable_collaborate_edit == 1) {
                    this.router.navigate(['/forms/list']);
                  } else {
                    this.router.navigate(['/forms/worklist']);
                  }
                  this._sharedService.myFormsType = "DRAFTS";
                }
              })

              var gotodraft = document.getElementsByClassName("btn-warning");
     
              gotodraft[0].setAttribute("id", "goto_cnfrm_btn");



            }


          })
        }


        console.log('selectedPatient', selectedPatient);
        if (selectedPatient)
          if (typeof (selectedPatient) == "object") {
            self.selectedAssosiatePatient = selectedPatient.id;
          } else {
            self.selectedAssosiatePatient = $("#assosiatedPatients").val();
          }
        //self.selectedAssosiatePatient = $("#assosiatedPatients").val();
        if (this.activeForms.stafFacing != 'practitioner') {
          this.oneTimeCopy = 0;
          self.clearForm = 0;
          self.setIframeUrl();
          setTimeout(() => {
            this.setIframeUrl();
          }, 1000);

        }
        if (this.activeForms.stafFacing == 'practitioner') {
          self.clearForm = 0;
          self.setIframeUrlstaffFill();
        }
      }
      var formData = {
        "formId": this.activeForms.id,
        "patientUser": this.selectedAssosiatePatient,
        "facing": this.activeForms.stafFacing
      }
      this._formsService.checkOrderchange(formData).then((data) => {
        console.log(data);
        var orderChangeResult: any = data;
        this.orderChangeStatus = orderChangeResult.orderChange;
        console.log(this.orderChangeStatus);

      })
      var activityData = {
        activityName: "Select Associate Patient",
        activityType: "structured forms",
        activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Associate Patient -222" + patientAssociatename + "(" + selectedPatient + ") Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
      };
      this._structureService.trackActivity(activityData);
    });



    $(".tenantUsers-rec ul").on('click', function (event) {

      this.autosavecalledformid = localStorage.getItem('autosavecalledformid');
      this.autosavecalled = localStorage.getItem('autosavecalled');
      console.log("-----------------==================");
      console.log(this.autosavecalledformid);
      console.log(this.autosavecalled);
      console.log("-----------------==================");
      console.log("-----------------==================");
      // this.associatePatientNew = $('#tenantUsers').val();
      // console.log(this.associatePatientNew);
      localStorage.setItem('associatePatientNew', $('#tenantUsers').val());
      //(... rest of your JS code)
    });

    $("#tenantUsers").on("change", (e) => {
      this.autosavecalledformid = localStorage.getItem('autosavecalledformid');
      this.autosavecalled = localStorage.getItem('autosavecalled');
      if (this.activeForms.stafFacing == "false") {
        this.autosavecalled = "false"
      }


      console.log("-----------------==================");
      console.log(this.autosavecalledformid);
      console.log(this.autosavecalled);
      console.log(this.associatePatientNew);
      console.log("-----------------==================");
      console.log("-----------------==================");
      if (this.autosavecalled == "true") {

        var msgtxt = "You want to close this Form or continue with the form?"
        swal({
          title: "Are you sure?",
          text: msgtxt,
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default",
          confirmButtonClass: "btn-warning",
          cancelButtonText: "Close",
          confirmButtonText: "Continue",
          closeOnConfirm: true
        }, (confirm) => {
          if (confirm) {
            $('#tenantUsers').val([localStorage.getItem('associatePatientNew')]).trigger('change');
            console.log(localStorage.getItem('associatePatientNew'));
            this.associatePatientNew = 1;
          }
          else {
            if (this.userDataConfig.enable_collaborate_edit == 1) {
              var enable_collaborate_edit = " collaborateDraft-Enabled";
            } else {
              var enable_collaborate_edit = "";
            }
            var activityData = {
              activityName: "Switched the form before saved as draft " + enable_collaborate_edit,
              activityType: "structured forms",
              activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + this.selectedAssosiatePatient + "- Switched the form before saved as draft - " + this.activeForms.name + " (" + this.activeForms.id + ") "
            };
            this._structureService.trackActivity(activityData);
            localStorage.setItem('autosavecalledformid', "0");
            localStorage.setItem('autosavecalled', "false");
            $('#btm-send-form-list').attr('disabled', true);
            console.log(self.stafffillfirst)
            //self.stafffillfirst=true;
            console.log(self.stafffillfirst);            
            var recipients = this.selectedRecipients;//$('#tenantUsers').val();
            var recipientName = $('#tenantUsers').select2('data');


            console.log(recipients);
            if (recipients) {
              self.stafffillfirst = true;
            }
            else {
              self.stafffillfirst = false;
            }
            recipients = recipients.map(element => {
              var id = element.substr(element.indexOf(":") + 1);
              id = id.replace(/'/g, "");
              return id.replace(/\s/g, '');
            });
            recipients = recipients.filter(a => a.indexOf('tag-') === -1)
            console.log(recipients)
            var recipi = this.selectedRecipients;//$('#tenantUsers').val();
            console.log(recipi.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join())
            if (recipients.length > 0) {

              self.receipientCount = recipients.length;
              // this.receipientCountSaveDraft=recipients.length;
              if (this.activeForms.stafFacing != 'practitioner') {

                if (recipients.length == 1) {
                  if (this.activeForms.externalFileExchange != true) {
                    var datacheck: any = {
                      formid: this.activeForms.id,
                      loggedinuser: this.userData.userId,
                      patientid: recipients[0],
                      enableCollaborateEdit: this.userDataConfig.enable_collaborate_edit,
                      admissionId: this._structureService.isMultiAdmissionsEnabled && !isBlank(this.selectedAdmission) ? this.selectedAdmission.id : undefined

                    };
                    this._formsService.checkDraftForm(datacheck).then((result: any) => {

                      console.log(result.nodrafts);
                      if (result.nodrafts == false) {
                        if (recipientName[0].text) {
                          var textpatent = recipientName[0].text;
                        }

                        else {
                          var textpatent
                        }
                        console.log(textpatent.trim());
                        var activityDataStart = {
                          activityName: "Patient already have drafts for the selected form",
                          activityType: "structured forms",
                          activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + textpatent.trim() + "- this patient already have drafts for the form- " + this.activeForms.name + " (" + this.activeForms.id + ") "
                        };
                        if (this.userDataConfig.enable_collaborate_edit == 1) {
                          var worklistname = " in All Form Worklist";
                        } else {
                          var worklistname = " in My Form Worklist";
                        }
                        if (!this.brightreeFormLandingFlow) {
                          //TODO: Need to revamp all swal popups [CHP-6901].
                        var msgtxt = "You already have drafts for the Patient " + textpatent.trim() + worklistname
                        msgtxt = msgtxt.replace("<br>", "");
                        swal({
                          title: "Are you sure?",
                          text: msgtxt,
                          type: "warning",
                          showCancelButton: true,
                          cancelButtonClass: "btn-default",
                          confirmButtonClass: "btn-warning",
                          cancelButtonText: "Continue",
                          confirmButtonText: "Go To Drafts",
                          closeOnConfirm: true
                        }, (confirm) => {
                          if (confirm) {
                            if (this.userDataConfig.enable_collaborate_edit == 1) {
                              var enable_collaborate_edit = " collaborateDraft-Enabled";
                            } else {
                              var enable_collaborate_edit = "";
                            }
                            var activityData = {
                              activityName: "Patient already have drafts for the selected form and navigated to draft bucket" + enable_collaborate_edit,
                              activityType: "structured forms",
                              activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + textpatent.trim() + "- this patient already have drafts for the form- and navigated to draft bucket " + this.activeForms.name + " (" + this.activeForms.id + ") "
                            };
                            this._structureService.trackActivity(activityData);
                            this._structureService.setCookie('tabname','DRAFTS', 1);
                            this.emitSubmitFormToPAH()
                            if (this.userDataConfig.enable_collaborate_edit == 1) {
                              this.router.navigate(['/forms/list']);
                            } else {
                              this.router.navigate(['/forms/worklist']);
                            }
                            this._sharedService.myFormsType = "DRAFTS";

                          } else {
                            if (this.userDataConfig.enable_collaborate_edit == 1) {
                              var enable_collaborate_edit = "collaborateDraft-Enabled";
                            } else {
                              var enable_collaborate_edit = "";
                            }
                            var activityData = {
                              activityName: "Patient already have drafts for the selected form and continue with the form" + enable_collaborate_edit,
                              activityType: "structured forms",
                              activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + textpatent.trim() + "- this patient already have drafts for the form - and continue with the form " + this.activeForms.name + " (" + this.activeForms.id + ") "
                            };
                            this._structureService.trackActivity(activityData);
                          }
                        let already = document.getElementsByClassName("btn-warning");
                        already[0].setAttribute("id", "already_draft_cnfrm_btn");

                        })
                        }

                        this._structureService.trackActivity(activityDataStart);
                      }


                    })
                  }
                }

                self.selectedAssosiatePatient = recipients[0];
                self.clearForm = 0;
                self.setIframeUrlstaffFill();
              }
              self.stafffillfirst = true;

              console.log("recipients--111")
            } else {
              self.stafffillfirst = false;
              console.log("recipients--2222")
            }
            if (recipi.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join() != "") {
              if (this.activeForms.stafFacing != 'practitioner') {
                self.clearForm = 0;
                self.setIframeUrlstaffFill();
              }
              self.stafffillfirst = true;
              console.log("recipients--3333333")
            }
            if (recipients.length <= 0 && recipi.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join() == "") {
              self.stafffillfirst = false;
              console.log("recipients-**********");
            }

            var activityData = {
              activityName: "Select Recipients",
              activityType: "structured forms",
              activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected recipient -" + recipients + " Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
            };
            this._structureService.trackActivity(activityData);

          }
        })


        var z = document.getElementsByClassName("btn-warning");
     
      z[0].setAttribute("id", "draft_cnfrm_btn");
      }
      else {
        $('#btm-send-form-list').attr('disabled', true);
        console.log(self.stafffillfirst)
        //self.stafffillfirst=true;
        console.log(self.stafffillfirst)
        var recipients = this.selectedRecipients;//$('#tenantUsers').val();
        var recipientName = $('#tenantUsers').select2('data');


        console.log(recipients);
        if (recipients) {
          self.stafffillfirst = true;
        }
        else {
          self.stafffillfirst = false;
        }
        recipients = recipients.map(element => {
          var id = element.substr(element.indexOf(":") + 1);
          id = id.replace(/'/g, "");
          return id.replace(/\s/g, '');
        });
        recipients = recipients.filter(a => a.indexOf('tag-') === -1)
        console.log(recipients)
        var recipi = this.selectedRecipients;//$('#tenantUsers').val();
        console.log(recipi.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join())
        if (recipients.length > 0) {

          self.receipientCount = recipients.length;
          if (this.activeForms.stafFacing != 'practitioner') {

            if (recipients.length == 1) {
              if (this.activeForms.externalFileExchange != true) {
                var datacheck: any = {
                  formid: this.activeForms.id,
                  loggedinuser: this.userData.userId,
                  patientid: recipients[0],
                  enableCollaborateEdit: this.userDataConfig.enable_collaborate_edit,
                  admissionId: this._structureService.isMultiAdmissionsEnabled && !isBlank(this.selectedAdmission) ? this.selectedAdmission.id : undefined


                };
                this._formsService.checkDraftForm(datacheck).then((result: any) => {

                  console.log(result.nodrafts);
                  if (result.nodrafts == false) {
                    if (recipientName[0].text) {
                      var textpatent = recipientName[0].text;
                    }

                    else {
                      var textpatent
                    }
                    console.log(textpatent.trim());
                    if (this.userDataConfig.enable_collaborate_edit == 1) {
                      var enable_collaborate_edit = " collaborateDraft-Enabled";
                    } else {
                      var enable_collaborate_edit = "";
                    }
                    var activityData = {
                      activityName: "Patient already have drafts for the selected form" + enable_collaborate_edit,
                      activityType: "structured forms",
                      activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + textpatent.trim() + "- this patient already have drafts for the form-" + this.activeForms.name + this.activeForms.name + " (" + this.activeForms.id + ") "
                    };
                    this._structureService.trackActivity(activityData);
                    if (this.userDataConfig.enable_collaborate_edit == 1) {
                      var worklistname = " in All Form Worklist";
                    } else {
                      var worklistname = " in My Form Worklist";
                    }
                    if (!this.brightreeFormLandingFlow) {
                      //TODO: Need to revamp all swal popups [CHP-6901].
                      let msgtxt = "You already have drafts for the Patient " + textpatent.trim() + worklistname
                      msgtxt = msgtxt.replace("<br>", "");
                    swal({
                      title: "Are you sure?",
                      text: msgtxt,
                      type: "warning",
                      showCancelButton: true,
                      cancelButtonClass: "btn-default",
                      confirmButtonClass: "btn-warning",
                      cancelButtonText: "Continue",
                      confirmButtonText: "Go To Drafts",
                      closeOnConfirm: true
                    }, (confirm) => {
                      if (confirm) {
                        if (this.userDataConfig.enable_collaborate_edit == 1) {
                          var enable_collaborate_edit = " collaborateDraft-Enabled";
                        } else {
                          var enable_collaborate_edit = "";
                        }
                        var activityData = {
                          activityName: "Patient already have drafts for the selected form and navigated to draft bucket" + enable_collaborate_edit,
                          activityType: "structured forms",
                          activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + textpatent.trim() + "- this patient already have drafts for the form- and navigated to draft bucket " + this.activeForms.name + " (" + this.activeForms.id + ") "
                        };
                        this._structureService.trackActivity(activityData);
                        this._structureService.setCookie('tabname','DRAFTS', 1);
                        this.emitSubmitFormToPAH()
                        //this.router.navigate(['/forms/worklist']);
                        if (this.userDataConfig.enable_collaborate_edit == 1) {
                          this.router.navigate(['/forms/list']);
                        } else {
                          this.router.navigate(['/forms/worklist']);
                        }
                        this._sharedService.myFormsType = "DRAFTS";
                      }
                      else {
                        if (this.userDataConfig.enable_collaborate_edit == 1) {
                          var enable_collaborate_edit = "collaborateDraft-Enabled";
                        } else {
                          var enable_collaborate_edit = "";
                        }
                        var activityData = {
                          activityName: "Patient already have drafts for the selected form and continue with the form" + enable_collaborate_edit,
                          activityType: "structured forms",
                          activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + textpatent.trim() + "- this patient already have drafts for the form - and continue with the form " + this.activeForms.name + " (" + this.activeForms.id + ") "
                        };
                        this._structureService.trackActivity(activityData);
                      }
                    })
                    }

                    var dr = document.getElementsByClassName("btn-warning");
     
                    dr[0].setAttribute("id", "go_to_draft_cnfrm_btn");
                  }


                })
              }
            }

            self.selectedAssosiatePatient = recipients[0];
            self.clearForm = 0;
            self.setIframeUrlstaffFill();
          }
          self.stafffillfirst = true;

          console.log("recipients--111")
        } else {
          self.stafffillfirst = false;
          console.log("recipients--2222")
        }
        if (recipi.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join() != "") {
          if (this.activeForms.stafFacing != 'practitioner') {
            self.clearForm = 0;
            self.setIframeUrlstaffFill();
          }
          self.stafffillfirst = true;
          console.log("recipients--3333333")
        }
        if (recipients.length <= 0 && recipi.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join() == "") {
          self.stafffillfirst = false;
          console.log("recipients-**********");
        }

        var activityData = {
          activityName: "Select Recipients",
          activityType: "structured forms",
          activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected recipient -" + recipients + " Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
        };
        this._structureService.trackActivity(activityData);

      }
    });
    this.structuredFormSend = this._formBuild.group({
      tenantUsers: [''],
      assosiatedPatients: [''],
      message: ['']
    });
   

    if(this.isPatientDriven!=true || this.selectedFlow=='formsdriven') {
      this._formsService.getAllFormNames({"roleId": this.userData.roleId, isPatientAssociated: this.isLoadFromPAH }).then((result) => {
        this.showPageLoader = false;
        this.forms = result;
        this.forms.sort((a, b) => (a.name > b.name) ? 1 : ((b.name > a.name) ? -1 : 0));
  
        if (this.isLoadFromPAH) {
          // If loaded from PAH, get associated patients. Otherwise, execute else block.
          this.getAssociatePatients(this.activeForms, this.getPatientSearchCriteria(this.autoPopulatePatientInfo));
        } else if (this.externalUserId) {
          //if loaded from brightreeFormLandingFlow then get the associated patients by externalUserId
          if (this.userData.mySites.length) {
            this.siteIds = this.userData.mySites.map((value) => value.id);
          }
          this.getAssociatePatients(this.activeForms, '');
        } else {
          this.selectedForm = this.forms[0];
          if(this.loadFormFrom == 'worklistCenter') {
            let index = this.forms.findIndex(x=> x.id == this.forwardToId);
            this.goToForm(this.forms[index]);
          } else {
            this.goToForm(this.forms[0]);
          }
        }
  
        if (this.dTable) {
          this.dTable.destroy();
        }
  
        var isTrue = false;
  
        if (this.forms.length > 99) {
          isTrue = true;
        }
        
      }).catch(() => {
        this.showPageLoader = false;
      });
    }
    this.userDataUpdateSubs = this._sharedService.userDataUpdate.subscribe(
      (userData) => {
        this.setRecipient();
      });

    $.fn.select2.amd.require(['select2/data/array', 'select2/utils'], (ArrayData, Utils) => {
      console.log('select2 protoype starting');
      interface CustomDataFN {
        (this: any, $element: any, options: any): any;
        __super__: any;
      }

      var CustomData: CustomDataFN = <CustomDataFN>function ($element, options): any {
        CustomData.__super__.constructor.call(this, $element, options);
      }

      function contains(str1, str2) {
        return new RegExp(str2, "i").test(str1);
      }

      Utils.Extend(CustomData, ArrayData);

      CustomData.prototype.query = function (params, callback) {
        if (!("page" in params)) {
          params.page = 1;
        }
        var pageSize = 20;
        console.log("processing select2 pagination");
        var results = this.$element.children().map(function (i, elem) {

          if (contains(elem.innerText, params.term)) {
            return {
              id: elem.value,
              text: elem.innerText
            };
          }
        });
        callback({
          results: results.slice((params.page - 1) * pageSize, params.page * pageSize),
          pagination: {
            more: results.length >= params.page * pageSize
          }
        });
      };
      console.log(self.associatePatientLoading);
      var selectPlaceholder = "Select Associated Patient";
      if (self.associatePatientLoading) {
        selectPlaceholder = "Loading Associated Patient...";
      }
      $("#assosiatedPatients").select2({
        ajax: {},
        allowClear: true,
        width: "element",
        placeholder: selectPlaceholder,
        dataAdapter: CustomData
      });
      if (self.associatePatientLoading) {
        $("#select2-assosiatedPatients-container .select2-selection__placeholder").html("").html("Loading Associated Patient <img src='./assets/img/loader/color.gif' style='width: 31px;'>");
      }
    });
    var self = this;
    $(function () { // DOM ready
      // ::: TAGS BOX    
      $("#tagsInput").on({
        click: function () {
          var txt = this.value;//.replace(/[^a-z0-9\+\-\.\#]/ig,''); // allowed characters
          if (txt) {
            console.log("Enter focusin function....");
            self.recipientInputFocus();
          }
        },
        focusout: function () {
        },
        keyup: function (ev) {
          var txt = this.value;//.replace(/[^a-z0-9\+\-\.\#]/ig,'');
          // if: comma|enter (delimit more keyCodes with | pipe)
          if (txt && /(188|13)/.test(ev.which)) { self.setRecipient(txt);/*$(this).focusout();*/ }
        }
      });
      $("#associate-search-input").on({
        keyup: function (ev) {
          var txt = this.value.replace(/[^a-z0-9\+\-\.\#]/ig, '');
          if (txt && /(13)/.test(ev.which)) { self.checkAssociatePatientWithTems(); }
        }
      });
      $('#tags').on('click', 'span.remove', function () {
        console.log("id========" + $(this)[0].id);

        self.removeSelectedRecipient($(this)[0].id);
      });
    });
    $(document).ready(function(){
      $('[data-toggle="tooltip"]').tooltip();
    });
    if(this.isPatientDriven==true){
      this.formdrivenFlowTooltip=this._ToolTipService.getToolTip(page, 'FORM00012');
      this.patientDrivenFlowTooltip=this._ToolTipService.getToolTip(page, 'FORM00011');  
      
    }
    setTimeout(function () {
      $('#MRN').keypress(function (e) {
        if (e.which === 32 && !this.value.length)
          e.preventDefault();
      });
    }, 2000);
    this.formSubmitSubscription = this._sharedService.formSubmissionEvent.subscribe((success) => {
      if (success && this.isFullWidthCollapsed) {
        setTimeout(() => {
          this.fullscreen();
        }, 1000);
      }
    });
  }
  flowChange(type){
    this.formContent='';
    this.activeForms='';
    this.selectedFlow=type;
    this.selectedAssosiatePatient = '';
    this.selectedAssosiatePatientName = "";
    this.patientAssociatedActiveForm=true;
    this.activeForms='';
    this.assosiatedPatients=[];
    this.selectedAssosiatePatientDrivenId=null;
    this.selectedAssosiatePatientDisplayName='';
    this.selectedAssosiatePatientDrivenName='';
    this.PatientsDrivenList=[];
    this.selectedAssosiatePatientDrivenName='';
    this.patientAssociatedForms=[];
    this.PatientDrivenSearch='';
    this.searchInboxkeywordForm='';
    this.searchInboxkeyword='';
    this.isNonContactableUsersExists = false;
    if(type=='formsdriven' && this.forms && this.forms.length){
      this.activeForms=this.forms[0];
      localStorage.removeItem('autosavecalledformid');
      localStorage.removeItem('autosavecalled');
    }
    this.fullscreen('close');
    
  }
  fullscreen(type=''){
    if(type=='close'){
      $('#banner-wrapper .alert-main').removeClass('banner-alert-full-width');
      $(".send-forms").removeClass("col-lg-12");
      $(".send-forms").addClass("col-lg-8");
      $(".staff-hide-div").removeClass("hide");
      $(".structured-form-modal").css("height", "482px");
      $("#full-screen-icon").addClass("fa-arrows-alt");
      $(".cat__core__title strong").removeClass("hide");
      $("#full-screen-icon").removeClass("fa-compress");
      $(".chatroom-section-first").removeClass("hide");
      $(".card-top-section").removeClass("hide");
      $("#full-screen-icon").attr("title", "Maximize");
      $("body").addClass("cat__menu-left--visible");
    } else {
      $('#banner-wrapper .alert-main').addClass('banner-alert-full-width');
      if ($(".send-forms").hasClass("col-lg-8")) {
        this.isCollapsed = true;
        this.isFullWidthCollapsed = true;
        $(".send-forms").removeClass("col-lg-8");
        $(".send-forms").addClass("col-lg-12");
        $(".chatroom-section-first").addClass("hide");
        $(".cat__core__title strong").addClass("hide");
        $(".staff-hide-div").addClass("hide");
        $(".structured-form-modal").css("height", "auto");
        $("#full-screen-icon").removeClass("fa-arrows-alt");
        $("#full-screen-icon").addClass("fa-compress");
        $(".card-top-section").addClass("hide");
        $("#full-screen-icon").attr("title", "Collapse");
        $("body").removeClass("cat__menu-left--visible");
      } else {
        this.isFullWidthCollapsed = false;
        this.isCollapsed = false;
        $('#banner-wrapper .alert-main').removeClass('banner-alert-full-width');
        $(".send-forms").removeClass("col-lg-12");
        $(".send-forms").addClass("col-lg-8");
        $(".staff-hide-div").removeClass("hide");
        $(".structured-form-modal").css("height", "482px");
        $("#full-screen-icon").addClass("fa-arrows-alt");
        $(".cat__core__title strong").removeClass("hide");
        $("#full-screen-icon").removeClass("fa-compress");
        $(".chatroom-section-first").removeClass("hide");
        $(".card-top-section").removeClass("hide");
        $("#full-screen-icon").attr("title", "Maximize");
        $("body").addClass("cat__menu-left--visible");
      }
      if(this.isPatientFacing) { 
        this.handleFormPreviewHeight();
      }
    }
  }
  openPatientDrivenList(){
    this.PatientsDrivenList=[];
    this.selectedAssosiatePatientDrivenName='';
    this.selectedAssosiatePatientDrivenId=null;
    this.selectedAssosiatePatientDisplayName='';
    let searchTerm = $("#patient-search-input").val();
    this.PatientDrivenSearch=searchTerm;
    $("#patient-search-input").addClass("activePatient");
    if (searchTerm != ""){
      this.PatientDrivenLoading=true;
      this.getPatientsList(searchTerm);
    }
  }
  IsEmail(email) {
    var regex = /^([a-zA-Z0-9_\.\-\+])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
    var validEmail;
    if (!regex.test(email)) {
      validEmail = '';
      return validEmail;
    } else {
      validEmail = email;

      return validEmail;
    }
  }
  gotocreatesendform() { 
   
    this._formsService.getAllFormNames({"roleId": this.userData.roleId}).then((result) => {
      this.forms = result;
      this.forms.sort((a, b) => (a.name > b.name) ? 1 : ((b.name > a.name) ? -1 : 0));

      console.log("this.forms", this.forms);
      this.goToForm(this.forms[0]);

      if (this.dTable) {
        this.dTable.destroy();
      }

      var isTrue = false;

      if (this.forms.length > 99) {
        isTrue = true;
      }

    }).catch((ex) => {
    });
  }
  getPatientsList(search: string = "") { //jithin
    $("#patient-search").text(" ").text("Loading...");
    let x;
    this.userService.getAssociatedPatientsLists(x,search, 0, this.siteIds).subscribe((result: any) => {
      this.PatientDrivenLoading = false;
      this.PatientsDrivenList = result.filter((result) => {
      let date = "";
      if(result.caregiver_dob) {
        date = result.caregiver_dob;
        let dobDay = new Date(date).getDay();
        if (date && !isNaN(dobDay)) {
            date = date.replace(/-/g, '/');
        try {
           date = moment(date).format('MM/DD/YYYY');
        } catch (e) {
            date = '';
        }
        } else {
          date = "";
        }
        } else {
          date = "";
        }
        result.caregiver_dob_formatted = date;
        date = "";
        if(result.dob){
          date = result.dob;
          var dobDay = new Date(date).getDay();
          if (date && !isNaN(dobDay)){
            date = date.replace(/-/g, '/');
            try {
              date = moment(date).format('MM/DD/YYYY');
            }catch (e) {
              date = '';
            }
          }else{
            date = "";
          }
        }else {
          date = "";
        }
        result.dob_formatted = date;
      
        var listDisplayName = this._structureService.formatPatientData(result);
        result.listDisplayName = listDisplayName;
        console.log('listDisplayName');
        console.log(listDisplayName);
        return true;
        
      });
      $(".patientDriveUl").css("display",'block');
      $("#patient-search").text("").text("Search");
      
    });
  }
  searchPatientDrivenPatientonEnter(event){
    if (event.keyCode === 13) { 
    var searchkey=$(event.target).val();
    if(searchkey){
        this.openPatientDrivenList();       
    }
  } 
  }
  searchRecipientonEnter(event){
  if (event.keyCode === 13 && (!this.isLoadFromPAH || this.staffFacing === 'practitioner')) { 
  var searchkey=$(event.target).val();
  if(searchkey){
      this.checkRecipientWithTems();       
  }
} 
}
  selectPatientDrivenPatient(id,listdisplayName,DisplayName,siteId){
    this.selectedAssosiatePatientDrivenSiteId = siteId;
    this.patientAssociatedActiveForm=false;
    this.selectedAssosiatePatientDrivenName=listdisplayName;
    this.selectedRecipients=[];
    this.selectedRecipients.push(id);
    this.selectedAssosiatePatientDrivenId=id;
    this.selectedAssosiatePatientDisplayName=DisplayName;
    $(".patientDriveUl").css("display",'none');
    this.associatedFormsLoading=true;
    var patientID=this.selectedAssosiatePatientDrivenId;
    var loggedInUserId=this.userData.userId;
    var loggedInUserRoleId=this.userData.roleId;
    var tenantId=this.userData.tenantId;
    var crossTenantID=this.userData.crossTenantId;
    var request={
             "patientId":patientID,
             "type":"GET",
             "dataType":"patientForms",
             "form_id":"",
             "associateIds":''
            };
    NProgress.start();        
    this._structureService.FormsOnPatientUserEdit(request).then((response:any) => {
      console.log(response);
      this.patientAssociatedForms=[];
      if(response){
        console.log("--------------all forms--------------------------");
        console.log(response.patientForms);
        if(response.patientForms && response.patientForms.length) this.patientAssociatedForms = response.patientForms;
      }
      NProgress.done();
      this.associatedFormsLoading=false;  
    });
  }
  openPatientDrivenListresume(){
    console.log("clicked");
    if(this.PatientsDrivenList.length){
      $(".patientDriveUl").css("display",'block');
      $("#patientUl").css("display",'block !important');
      
    }
   
  }
  closeList(){
    $(".patientDriveUl").css("display",'none');
  }
  closePatientDrivenList(reset = false){
    this.patientAssociatedActiveForm=false;
    this.activeForms='';
    this.searchInboxkeywordForm='';
    $('#patient-search-input').val('');
    this.PatientDrivenSearch='';
    if(!reset){
        this.PatientsDrivenList = [];
    }
    this.selectedAssosiatePatientDrivenName='';
    this.selectedAssosiatePatientDrivenId=null;
    this.selectedAssosiatePatientDisplayName='';
    this.patientAssociatedForms=[];
    this.selectedRecipients=[];
    this.stafffillfirst = false;
    $(".patientDriveUl").css("display",'none');
   }
  getAssociatePatients(form, search: string = "") {
    $("#associate-search").text(" ").text("Loading...");
    this.associatePatientLoading = true;
    this.userService.getAssociatedPatientsLists(form, search, 0, this.siteIds, this.externalUserId).subscribe((result: any) => {
      // Handle the patient onboarding if the user not exist in citus
      if (result.status_code === 201 && this.externalUserId) {
        this.showLoaderMessage = this._ToolTipService.getTranslateData('MESSAGES.LOADING_PATIENT_ONBOARDING_WAIT');
        setTimeout(() => {
          this.doPatientOnBoarding('associate', this.externalUserId);
        },2000);
        return;
      }
      // this.assosiatedPatients = result;
      this.handleAssociatedPatients(result, search);
    });
  }
  doPatientOnBoarding(type :string, externalPatientId: string, tagId: string = '') {
    this.httpService.doPost(APIs.externalPatientIntegration, { siteIds: this.siteIds.toString(), externalPatientId, type , tagId }).subscribe((result) => {
      this.showPageLoader = false;
      this.showLoaderMessage = '';
      if (result.status_code === 201 && this.externalUserId) {
        this._structureService.notifyMessage({ messge: result.status_message });
      } else {
        this.showLoaderMessage = this._ToolTipService.getTranslateData('MESSAGES.LOADING_DATA');
        setTimeout(() => {
          this.showLoaderMessage = '';
          if (type === 'associate') {
            this.handleAssociatedPatients(result, '');
          } else {
            this.handleRecipientPatients(result, '');
          }
        },1000);
      }
    }, () => {
      this.showPageLoader = false;
      this.showLoaderMessage = '';
    });
  }
  handleAssociatedPatients(result: any, search: string) {
    this.associatePatientLoading = false;
      $("#select2-assosiatedPatients-container .select2-selection__placeholder").html("").html("Select Associated Patient");
      $("#assosiatedPatients").attr("disabled", false);
      this.assosiatedPatients = result.filter((result) => {
        let date = "";
        if (result.caregiver_dob) {
          date = result.caregiver_dob;
          let dobDay = new Date(date).getDay();
          if (date && !isNaN(dobDay)) {
            date = date.replace(/-/g, '/');
            try {
              date = moment(date).format('MM/DD/YYYY');
            }
            catch (e) {
              date = '';
            }
          } else {
            date = "";
          }
        } else {
          date = "";
        }
        result.caregiver_dob_formatted = date;
        date = "";
        if (result.dob) {
          date = result.dob;
          let dobDay = new Date(date).getDay();
          if (date && !isNaN(dobDay)) {
            date = date.replace(/-/g, '/');
            try {
              date = moment(date).format('MM/DD/YYYY');
            }
            catch (e) {
              date = '';
            }
          } else {
            date = "";
          }
        } else {
          date = "";
        }
        result.dob_formatted = date;
        var listDisplayName = this._structureService.formatPatientData(result);
        console.log('listDisplayName2');
        console.log(listDisplayName);
        result.listDisplayName = listDisplayName;
        // If loaded from PAH and user ID matches, set the associated patient.
        if (this.isLoadFromPAH && result.userId === this.autoPopulatePatientInfo.id) {
          this.setAssosiatePatient(result);
        }
        return true;
      });
      $("#associate-search").text("").text("Search");
      if (search != "") {
        this.enableOrDisableUiLI(true, false);
      }
      // Automatically select the patient if only one patient is fetched
      if (this.externalUserId && this.assosiatedPatients && this.assosiatedPatients.length > 0) {
        this.selectedPatientDetails = {
          patientDetailId: this.assosiatedPatients[0].userId,
          selectedAssocPatientName: this.assosiatedPatients[0].listDisplayName,
          siteId: this.assosiatedPatients[0].siteId,
          searchData: this.assosiatedPatients
        };
        this.getSelectedPatientDetails(this.selectedPatientDetails);
      }
  }
  openAssociateList() {

    if (!this.associatePatientLoading) {
      console.log("openAssociateList..............." + this.assosiatedPatients.length);
      if (this.assosiatedPatients.length) {
        this.enableOrDisableUiLI(true, false);
      }
    }

  }
  enableOrDisableUiLI(condition, clear: boolean = true, from: any = "") {
    console.log(`enableOrDisableUiLI condition = ${condition}, clear = ${clear}`);
    if (condition) {
      if (from == "R") {
        if ($('ul#associate-ul').css('display') == 'block') {
          this.enableOrDisableUiLI(false, false);
        }
        $("#tagsInput").addClass('ul-active');
        $("ul#recipient-ul").css('display', 'block');
      } else {
        if ($('#recipient-ul').css('display') == 'block') {
          this.enableOrDisableUiLI(false, false, "R");
        }
        $("ul#associate-ul").css('display', 'block');
        $("input#associate-search-input").addClass("active");
      }

    } else {
      if (from == "R") {
        $("#tagsInput").removeClass("ul-active");
        $("ul#recipient-ul").css('display', 'none');
      } else {
        $("ul#associate-ul").css('display', 'none');
        $("input#associate-search-input").removeClass("active");
      }

    }
    if (clear) {
      console.log("enableOrDisableUiLI : Clear.........")
      if (from == "R") {
        $("#tagsInput").val("");
        $("#tagsInput").attr("placeholder", "Search Recipients");
      } else {
        $("input#associate-search-input").val('');
      }

    }
  }
  checkAssociatePatientWithTems() {
   
    let searchTerm = $("#associate-search-input").val();
    if (searchTerm != "") {
      var activityData = {
        activityName: "Search Associate Patient",
        activityType: "Search Associate Patient",
        activityDescription: "Search Associate Patient Search Text:" + searchTerm,
      };
      this._structureService.trackActivity(activityData);
      this.getAssociatePatients(this.activeForms, searchTerm);
      //$(".associate-close").css("display","none");
    }
  }
  
  searchClear() {
    this.newPatient.patchValue({
      searchInput: ''
    });
    this.noRecordMessage = false;
    this.hasData = false;
    this.searchRequired = false;
    $("#button-search").prop("disabled", "disabled");
    $("#button-clear").prop("disabled", "disabled");
  }
  getsearchAssociatePatients(form, search: string = "") {
    this.userService.getAssociatedPatientsLists(form, search,0,this.assocSiteId).subscribe((result: any) => {
      this.assosiatedPatients = result.filter((result) => {
        var date = "";
        if (result.caregiver_dob) {
          date = result.caregiver_dob;
          var dobDay = new Date(date).getDay();
          if (date && !isNaN(dobDay)) {
            date = date.replace(/-/g, '/');
            try {
              date = moment(date).format('MM/DD/YYYY');
            }
            catch (e) {
              date = '';
            }
          } else {
            date = "";
          }
        } else {
          date = "";
        }
        result.caregiver_dob_formatted = date;
        // } else {
        date = "";
        if (result.dob) {
          date = result.dob;
          var dobDay = new Date(date).getDay();
          if (date && !isNaN(dobDay)) {
            date = date.replace(/-/g, '/');
            try {
             date = moment(date).format('MM/DD/YYYY');
            }
            catch (e) {
              date = '';
            }
          } else {
            date = "";
          }
        } else {
          date = "";
        }
        result.dob_formatted = date;
        var listDisplayName = (result.caregiver_displayname) ? ((result.dob_formatted) ? (result.displayname + ' - ' + result.dob_formatted + ' (' + result.caregiver_displayname + ')') : (result.displayname + ' (' + result.caregiver_displayname + ')')) : ((result.dob_formatted) ? (result.displayname + ' - ' + result.dob_formatted) : result.displayname);
        listDisplayName = listDisplayName + (result.passwordStatus == 'true' ? " (Enrolled)" : " (Virtual)");

        if (result.naTags && result.naTags != null && result.naTags != 'null' && result.naTags != "") {
          listDisplayName += " (" + result.naTagNames + ")";
        }

        result.listDisplayName = listDisplayName;
        // }
        return true;
        /* } else {
          return false;
        } */
      });
      console.log('this.assosiatedPatients', this.assosiatedPatients);

      $("#associate-search").text("").text("Search");
      if (search != "") {
        this.enableOrDisableUiLI(true, false);
      }
    });
  }
  resetAssociatePatientSelected() {

  }

  setSelectValueFromPah() {
    if (this.patientId) {
      setTimeout(() => {
        $('#assosiatedPatients').val(this.patientId).trigger('change');
      }, 200);
    }
  }
  ngOnDestroy() {
    if (this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
    if (this.userDataUpdateSubs) {
      this.userDataUpdateSubs.unsubscribe();
    }
    /**Unsubscribe all the socket event subscriptions */
    this.socketEventSubscriptions.forEach(subscription => {
      if(subscription) subscription.unsubscribe();
    });
    this.showNonContactableUsers = false;
    if (this.formSubmitSubscription) {
      this.formSubmitSubscription.unsubscribe();
    }
  }
  // form stabilization
  onBlurEvent(event: any){
   this.messageadd = event.target.value.trim();
   this.checkiframe(); 
   if(this.messageadd != ""){
     this.messagechoose = 1;
     //this.setIframeUrlstaffFill(this.messagechoose);
   }
  };
  // form stabilization 
  setIframeUrlstaffFill(messagechoose=null) {
  // user tag send failed issue check
  var recipients = this.selectedRecipients; //$('#tenantUsers').val();
      var tagRecipients = recipients.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join();
      var tagRecipientsFlag = "";
      if (tagRecipients != "") {
        tagRecipientsFlag = "true";
        let array = this.selectedRecipients.filter(function(value) {
        return value.indexOf('tag-') < 0;
       });
        if(array.length == 0){
          this.selectedAssosiatePatient = "";
        }
      }
  // user tag send failed issue check
  $(".saveAsDraft-message").html("");
  $("#last_save_draft").val("");
  $("#error_save_draft").val("");
  var clientId = this._structureService.socket.io.engine.id;
    if (this.receipientCountSaveDraft > 1) {
      this.savaAsDraftStaff = 0;
    }
    var recipientset1 = this.selectedRecipients;//$('#tenantUsers').val();
    this.receipientCount = this.selectedRecipients.length;
    recipientset1 = recipientset1.map(element => {
      var id = element.substr(element.indexOf(":") + 1);
      id = id.replace(/'/g, "");
      return id.replace(/\s/g, '');
    });
    // check stabilization form
    var formSendMode = '';
       if(this.userDataConfig.enable_appless_model == 1 ) {
        var virtualRecipients = this.selectedRecipientsArray.filter((x) => (x.passwordStatus == false));
        console.log(virtualRecipients);
        if(virtualRecipients.length > 0) {
          formSendMode = "appless";
        }
        
      }
       var applessMode = '';

    if(this.userDataConfig.enable_appless_model == 1) {
      applessMode = 'both';
    }
    // check stabilization form
    if (recipientset1.length > 1) {
      this.savaAsDraftStaff = 0;
    } else {
      if (this.activeForms.enableSaveDraftStaff == 1)
        this.savaAsDraftStaff = 1;
      else
        this.savaAsDraftStaff = 0;
    }
    if (this.userDataConfig.save_as_draft_message_interval) {

      localStorage.setItem('saveAsDraftMessageInterval', this.userDataConfig.save_as_draft_message_interval);
    }
    if (this.userDataConfig.enable_collaborate_edit) {

      localStorage.setItem('enableCollaborateEdit', this.userDataConfig.enable_collaborate_edit);
    }
    var enable_sftp_integration=this.userDataConfig.enable_sftp_integration==1?"&enable_sftp_integration="+true:"&enable_sftp_integration="+false;
    var unique="&uniqueFormIdentity="+Guid.create();
    var confirmActionPrefill=this.activeForms.confirmActionPrefill==1?"&confirmActionPrefill=true":"&confirmActionPrefill=false";
    var fax_queue_show_warning=this.userDataConfig.fax_queue_show_warning == 1?"&fax_queue_show_warning="+true+unique+confirmActionPrefill:"&fax_queue_show_warning="+false+unique+confirmActionPrefill;
   
    let formsUrl = this._structureService.machFormUrl + APIs.machFormEmbed +'?id=' + this.activeForms.id + "&patientId=" + this.selectedAssosiatePatient + "&loginUserId=" + this.selectedAssosiatePatient + "&tenantId=" + ((this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')) ? this._structureService.getCookie('crossTenantId') : this._structureService.getCookie('tenantId')) + "&toId=" + this.userData.userId + "&sentId=null&environment=" + this._structureService.environment + "&formName=" + encodeURIComponent(this.activeForms.name) + "&formId=" + this.activeForms.id + "&apiVersion=" + this._structureService.version + "&toName=" + this.userData.displayName + "&fromName=" + this.userData.displayName + "&serverBaseUrl=" + encodeURIComponent(this._structureService.serverUrlCustom) + "&noStafffillvalidation=false&fromMob=false&isWorkingHour=" + this._inboxService.checkInfusionHours(true).isWorkingHours + "&autoSave=" + this.autoSave + "&savaAsDraftStaff=" + this.savaAsDraftStaff + "&savaAsDraftPatient=" + this.savaAsDraftPatient + "&receipientCount=" + this.receipientCount + "&clientId=" + clientId + "&clearForm=" + this.clearForm + "&externalFileExchange=" + this.activeForms.externalFileExchange + "&saveAsDraftMessageInterval=" + localStorage.getItem('saveAsDraftMessageInterval') + "&enableCollaborateEdit=" + localStorage.getItem('enableCollaborateEdit') + "&populatePreviousSubmission=" + this.activeForms.populatePreviousSubmission+"&authenticationToken="+this._structureService.getCookie('authenticationToken')+enable_sftp_integration+fax_queue_show_warning+(this._structureService.isMultiAdmissionsEnabled && this.selectedAdmission ? `&admissionId=${this.selectedAdmission.id}`: '');

    if(this.frmSendMode.controls['mode'].value == 'appless' && this.userDataConfig.enable_appless_model == 1) {
      //formsUrl += '&formSendMode=appless';
    }
/*** */
var formSendModeNew = this.frmSendMode.controls['mode'].value;

var applessModeNew = '';
if(this.userDataConfig.enable_appless_model == 1) {
    var virtualRecipients = this.selectedRecipientsArray.filter((x) => (x.passwordStatus == false));

  if(virtualRecipients.length > 0) {
    formSendModeNew = "appless";
  } else {
    formSendModeNew = "";
  }
  if(this.frmSendMode.controls['mode'].value == 'appless'){
    formSendModeNew="appless";
  }
  if(this.frmSendMode.controls['mode'].value == 'mobileapp'){
    formSendModeNew="mobileapp";
  }
  applessModeNew = 'both';

  formsUrl +='&applessMode='+applessModeNew+"&formSendMode="+formSendModeNew;
}
/***************** */
    console.log("formsUrl", formsUrl);
    
    if (this.activeForms.stafFacing == "practitioner") {

      var recipientset = this.selectedRecipients;//$('#tenantUsers').val();
      recipientset = recipientset.map(element => {
        var id = element.substr(element.indexOf(":") + 1);
        id = id.replace(/'/g, "");
        return id.replace(/\s/g, '');
      });
      formsUrl += "&staffFacing=false&staffFilling=" + this.activeForms.staffFill + "&recipientset=" + recipientset + "&noStafffillvalidation=false&flowcheck=" + this.activeForms.stafFacing+"&authenticationToken=" +this._structureService.getCookie('authenticationToken');
    } else if (this.activeForms.stafFacing == "true") {
      formsUrl += "&staffFacing=" + this.activeForms.stafFacing;
    } else {
      var recipients = this.selectedRecipients; //$('#tenantUsers').val();
      var tagRecipients = recipients.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join();
      var tagRecipientsFlag = "";
      if (tagRecipients != "") {
        tagRecipientsFlag = "true";
      }
      formsUrl += "&staffFacing=" + this.activeForms.stafFacing + "&staffFilling=" + this.activeForms.staffFill + "&tagRecipients=" + tagRecipientsFlag;

      formsUrl += "&recipientset=" + recipientset1;
    }
    if(this.messageadd != "" && this.messageadd != undefined){
        formsUrl += "&messageadd=" + this.messageadd;
    }
    // session unique in multiple tabs checking
    formsUrl += "&create_send=true";
    // session unique in multiple tabs checking
    this.formContent = this.sanitizer.bypassSecurityTrustResourceUrl(formsUrl);
    if((this.activeForms.patientAssociation=='false' && this.activeForms.stafFacing == 'true')){
     this.stafffillfirst = true;
    }
  }

  showFormPriview() {
    $('#priviewForm').modal('show');
  }
  validateEmail(email) {
    var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if(re.test(email) && email.indexOf('unknown_') == -1){      
      return true;
    } else {
      return false;
    }
  }
 
  sendForm(sendfrommachform = null) { 
    const notificationData = {
      sourceId: CONSTANTS.notificationSource.form,
      sourceCategoryId: CONSTANTS.notificationSourceCategory.formSentNotification
    };
   
  window.addEventListener('message',  (event)=> {
    var response = event.data;
    try {
      var height = (response).split("=");
      if(height.length&&height[0]=="SendPatientForm") {
      this.formSendInProgress=false;
      }

    } catch (error) {
      console.log('error');
    }
  }, false);

  if(sendfrommachform == 1){ 
    this.topsend = true;
    this.stafffillfirst = false
  }
  console.log("enterd+++++++++++")
  const iframe = document.getElementById('structured-form-dekstop') as HTMLIFrameElement;
  const contentWindow = iframe.contentWindow;
  console.log("enterd+++++++++++")
  
    console.log("this.topsend", this.topsend);

    var formSendMode = this.frmSendMode.controls['mode'].value;

    var applessMode = '';

    if(this.userDataConfig.enable_appless_model == 1) {
      applessMode = 'both';
    }

    var recipients = this.selectedRecipients;

    if(recipients.length == 1) {
      console.log("formSendMode",formSendMode);
      console.log(this.activeForms);
      
      this.selectedRecipient = this.selectedRecipientsArray.find(a => {
        if(recipients[0].indexOf('--') != -1) {
          var recipo = recipients[0].split('--')[0].trim();
          if(a.userId == recipo) {
            return true;
          }
        } else if(a.userId == recipients[0]) {
          return true;
        }          
      });

      console.log("this.selectedRecipient", this.selectedRecipient)
      if(this.selectedRecipient && this.userDataConfig.enable_appless_model == 1
        && this.userData.config.enable_verification_of_cell_and_mobile == 1
        && formSendMode == "appless" 
        && (!this.selectedRecipient.mobVerificationStatus)
        && (!this.selectedRecipient.emailVerificationStatus)
        && !this.selectedRecipient.passwordStatus) {  
        swal({
          title: '',
          text: "User’s either Email or Mobile Number is not verified. AppLess (MagicLink) can not be sent. First verify the Email, SMS going to User Settings->Staff, Partners, Patients and perform the verification process or contact Administrator.",
          type: 'error',
          showCancelButton: false,
          confirmButtonClass: 'btn-warning',
          confirmButtonText: 'Ok',
          closeOnConfirm: true
        }, (isConfirm) => {
          if (isConfirm) {
            $("#textmessage").text("");
            $("#newloader").hide();
            return false;
          }
        });
        return false;
      }
    } else {
      if(this.userDataConfig.enable_appless_model == 1 ) {
        var virtualRecipients = this.selectedRecipientsArray.filter((x) => (x.passwordStatus == false));
        console.log(virtualRecipients);
        if(virtualRecipients.length > 0) {
          formSendMode = "appless";
        } else {
          formSendMode = "";
        }
        
      }
    }

    if (this.topsend) {
      console.log("sendForm : topsend ...");
      this.topsend = false;
      var recipients = this.selectedRecipients;// $('#tenantUsers').val();
      this.stafffillfirst = false;
      console.log(this.stafffillfirst);

      const selectedRecipientsPolling = [];
      const selectedRecipients = [];
      if (recipients.length) {
        console.log("sendForm : topsend : recipients.length ... ");
        this.formSendInProgress = true;
        console.log(this.tenantUsers, "this.tenantUsers");
        recipients = recipients.map(element => {
          var id = element.substr(element.indexOf(":") + 1);
          id = id.replace(/'/g, "");
          return id.replace(/\s/g, '');
        });
        
        if (this.receipientCount > 1) {
          // this.formSubmissionId=0;
        }
        let message = this.structuredFormSend.value['message'].trim();
        // console.log(selectedRecipients,"selectedRecipients");

        if (this.activeForms.staffFill == "false" || this.activeForms.staffFill == false) {
          this.formSubmissionId = 0;
        }
        var data: any = {
          form_id: this.activeForms.id,
          recipients: recipients.filter(a => a.indexOf('tag-') === -1),
          tagRecipients: recipients.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join(),
        //   userId: this.userData.userId,
          message: message,
          formSubmissionId: this.formSubmissionId,
          staffFacing:this.staffFacing
        };
        if(!isBlank(this.siteIds)){
            data.siteIds =  (this.siteIds != '0') ? this.siteIds.join(",") : this.siteIds;

        }else{
            data.siteIds = '0';
        }
       

        if(data.recipients.length == 0 && data.tagRecipients.length > 0 && this.userDataConfig.enable_appless_model == 1) {
          formSendMode = 'appless';
        }

        if (this.activeForms.stafFacing == 'practitioner') {
          let assoVal = [];
          let assocPat = this.selectedAssosiatePatient;
          if(this.selectedAssosiatePatient){
          if (typeof this.selectedAssosiatePatient == 'string' && assocPat.includes('{')) {
            let pJsn = JSON.parse(assocPat);
            assoVal.push(pJsn.id);
          } else {
            assoVal.push(assocPat);
          }
          data.practitionerAssociatePatient = assoVal;
        }
          data.practitioner = 1;
          data.orderChange = this.orderChange;
          console.log(this.siteId+"----"+this.faxQIntegration+"-----"+this.activeForms.patientAssociation);
          if(this.siteId !=0 && (this.faxQIntegration) && (this.activeForms.patientAssociation =="false")){
            console.log("entered3");
            data.siteId=this.siteId.toString();
          }
        }
        if (this.userDataConfig.enable_nursing_agencies_visibility_restrictions == 1) {
          data.nursingAgencies = this.userData.nursing_agencies;
        }
      //  data.tenantId = this._structureService.getCookie('tenantId');
        if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')) {
          data.tenantId = this._structureService.getCookie('crossTenantId');
        }

        if(formSendMode && formSendMode == 'appless') {
          data.formSendMode = 'appless';
          data.applessMode = applessMode;
        }
        var deepLinking = {
          "pushType": "",
          "state": "eventmenu.forms",
          "stateParams": {},
          "tenantId": data.tenantId,
          "tenantName": this.selectedRecipient? this.selectedRecipient.tenantName : '',
          "formSendMode": formSendMode,
          "applessMode" : applessMode,
          "sentId": ""
        };
        // console.log(data);
        let pushMessage = "New Form to fill out";
        localStorage.setItem('autosavecalledformid', "0");
        localStorage.setItem('autosavecalled', "false");
        console.log("sendForm : topsend :data==========> ", data);
        // form stabilization
        data.sendForm = 1;
        if(sendfrommachform == 1){ 
         data.sendfromtop = 0;
        }
        contentWindow.postMessage(data, '*');

        this.stafffillfirst = true;

         var interval =  setInterval(()=>{ 
                    

                       var resultnew = $("#response").val();
                       if(resultnew != ""){
                        clearInterval(interval);
                       }
                      
                      
                       
                        if(resultnew != ""){
                      var result = JSON.parse(resultnew);
          console.log("sendForm : topsend : result=======> ", result);
          this.formSendInProgress = false;
          if (result.send_to) {
            result.send_to.forEach(element => {
              const data = {
                userid: element,
                senderId: this.userData.userId,
                organizationMasterId: this.userData.organizationMasterId,
                formSendMode:formSendMode,
                applessMode: applessMode,
                sentId:result.sentId
              }
              
              console.log(data)
              selectedRecipientsPolling.push(data);
              selectedRecipients.push(element);
            });
            deepLinking.sentId = result.sentId;
          }
          if (result.progressNoteCreated == false && this.userDataConfig.enable_progress_note_integration == 1 && result.progressNoteIntegration == true && result.progressNoteIntegrationSend == true) {

            var activityDatam: any = {};
            console.log("in result");
            if (result.identity_value_Patient != '' || result.identity_value_Patient != "" || result.identity_value_Patient != null
              && result.CPRUSERNO != '' || result.CPRUSERNO != null
              && result.patientAssociation == true
              && result.CPRUSERNAME != null || result.CPRUSERNAME != '') {
              activityDatam.activityName = "Form Copied to Filing Center as JSON";
              activityDatam.activityType = "structured forms";
              activityDatam.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sentId + ") successfully and copied the JSON file to filing center named " + result.defaultFromFilingCenterSubmitjson;
              this._structureService.trackActivity(activityDatam);

            }
            if ((result.identity_value_Patient == '' || result.identity_value_Patient == "" || result.identity_value_Patient == null
              || result.CPRUSERNO == '' || result.CPRUSERNO == null
              || result.patientAssociation == false
              || result.CPRUSERNAME == '' || result.CPRUSERNAME == null)) {
              activityDatam.activityName = "Failed Form Copy to Filing Center as JSON";
              activityDatam.activityType = "structured forms";
              var messageData = '';
              var messageData1 = '';
              console.log("in defaultFromFilingCenter", result);
              if (result.patientAssociation == false && result.staffFacing == "true") {
                messageData1 = "there is no patient associated with this form";
              }
              else if ((result.identity_value_Patient == '' || result.identity_value_Patient == null || result.identity_value_Patient == "")) {

                messageData += ", MRN";
                if (result.firstnamepatient && result.firstnamepatient != "") {
                  messageData += " of " + result.firstnamepatient + " " + result.lastnamepatient;
                }
                console.log("in MRN", messageData);
              }
              let f1 = 0;
              let f2 = 0;
              if (result.CPRUSERNO == '' || result.CPRUSERNO == null) {
                f1 = 1;
                messageData += ", USERNO";
              }
              if (result.CPRUSERNAME == '' || result.CPRUSERNAME == null) {
                f2 = 1;
                messageData += ", USERNAME";
              }
              if ((f1 == 1 || f2 == 1) && result.firstnamestaff && result.firstnamestaff != "") {
                messageData += " of " + result.firstnamestaff + " " + result.lastnamestaff;
              }
              if (result.patientAssociation == false && result.staffFacing == "false") {
                messageData += "";
              }
              var removeComa = messageData.charAt(0);
              if (removeComa == ',') {
                messageData = messageData.substring(1);
              }
              console.log("messageData", messageData);
              if (messageData1 && messageData) {
                var finalMessage = 'Generating Progress Note for Form ' + result.form_name + ' failed due to ' + messageData1 + ' and missing (' + messageData + ')';
              }
              else if (messageData1) {
                var finalMessage = 'Generating Progress Note for Form ' + result.form_name + ' failed due to ' + messageData1;
              }
              else if (messageData) {
                var finalMessage = 'Generating Progress Note for Form ' + result.form_name + ' failed due to missing (' + messageData + ')';
              }
              else {
                var finalMessage = '';
              }
              if (finalMessage) {
                activityDatam.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.formId + ") failed to copy the JSON file to filing center named " + result.defaultFromFilingCenterSubmitjson + "because" + finalMessage;
                this._structureService.trackActivity(activityDatam);
               }
            }
          }
          console.log(selectedRecipientsPolling, "selectedRecipientsPollingselectedRecipientsPolling");
          this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", selectedRecipientsPolling, 'form');//Need to commnd after finish form inbox
          this._structureService.socket.emit("sendFormsToRecipients", selectedRecipientsPolling);
          this.result = result;
          /*patient reminder for form starts*/
          console.log(this.userData.config.patient_reminder_time * 1, this.userData.config.patient_reminder_types);
          var otherDatas = {
            patientReminderTime: this.userData.config.patient_reminder_time * 1,
            patientReminderTypes: this.userData.config.patient_reminder_types,
            messageReplyTimeout: this.userData.config.message_reply_timeout,
            sentId: this.result.sentId,
            senderName: this.userData.displayName,
            tenantId: this.userData.tenantId,
            tenantName: this.userData.tenantName,
            serverBaseUrl: this._structureService.serverBaseUrl,
            apiVersion: this._structureService.version,
            message: "[Reminder] You have new form to fillout",
            formReminderType: this.userData.config.patient_reminder_checking_type,
            environment: this._structureService.environment,
            formSendMode : formSendMode,
            applessMode: applessMode
          }

          console.log(otherDatas, "otherDatas");
          //this._structureService.socket.emit("ReminderForForm", selectedRecipientsPolling, otherDatas);
          this._structureService.reminderForForm(selectedRecipientsPolling, otherDatas);        
          /*patient reminder for form ends*/

          var formData = {
            "userid": this.userData.userId,
            "formId": this.activeForms.id,
            "formDatadraftid": localStorage.getItem('saveasdraftidtodeleted'),
          };
          if (localStorage.getItem('saveasdraftidtodeleted') != "false") {
            this._formsService.archiveDraftForm(formData).then((result: any) => {
              localStorage.setItem('saveasdraftidtodeleted', 'false');
            })
          }
          if(this.userConfig.form_send_mode != 'sqs'){
          this._structureService.sentPushNotification(selectedRecipientsPolling, 0, pushMessage, '', deepLinking, '', '', undefined, undefined, undefined, undefined, notificationData);
          }
          if (this.result.status == 1) {
            if (this.userDataConfig.enable_collaborate_edit == 1) {
              var enable_collaborate_edit = " collaborateDraft-Enabled";
            } else {
              var enable_collaborate_edit = "";
            }
            var activityName = "Send Form Success11";
            if(formSendMode == 'appless') {
              activityName =  "Send Appless Form Success11";
            }
            var activityData = {
              activityName: activityName + enable_collaborate_edit,
              activityType: "structured forms",
              activityDescription: this.userData.displayName + " successfully sent form " + this.activeForms.name + " (" + this.activeForms.id + ") to " + selectedRecipients.toString() + (formSendMode == 'appless'? '(appless)' : '' )
            };
            this._structureService.trackActivity(activityData);

            var sentMessage = this.activeForms.name + ' has been sent successfully.';
            if(this.loadFormFrom == 'worklistCenter') {
              this.submitForward.emit();
            }
            if(this.userDataConfig.enable_appless_model == 1 && formSendMode == 'appless' && this.userDataConfig.enable_verification_of_cell_and_mobile == 1) {
              if(data.recipients.length == 1) {
                if(this.selectedRecipient.mobVerificationStatus && this.selectedRecipient.emailVerificationStatus && this.validateEmail(this.selectedRecipient.name) && (this.selectedRecipient.countryCode != '' && this.selectedRecipient.mobile != '')) {
                  sentMessage += ' AppLess (MagicLink) Sent to Email '+this.selectedRecipient.name+' and Mobile Number '+this.selectedRecipient.countryCode + ' ' + this.selectedRecipient.mobile;
                } else if(this.selectedRecipient.emailVerificationStatus && this.validateEmail(this.selectedRecipient.name)){
                  sentMessage += ' AppLess (MagicLink) Sent to Email '+this.selectedRecipient.name;
                } else if(this.selectedRecipient.mobVerificationStatus && (this.selectedRecipient.countryCode != '' && this.selectedRecipient.mobile != '')) {
                  sentMessage += ' AppLess (MagicLink) Sent to Mobile Number '+this.selectedRecipient.countryCode + ' ' + this.selectedRecipient.mobile;
                }
              } else {
                sentMessage += ' AppLess (MagicLink) Sent to virtual users verified with Email or Mobile Number.';
              }
            } else if(this.userDataConfig.enable_appless_model == 1 && formSendMode == 'appless' && this.userDataConfig.enable_verification_of_cell_and_mobile != 1) {
              if(data.recipients.length == 1) {
                if(this.selectedRecipient.countryCode && this.selectedRecipient.mobile && this.selectedRecipient.name && this.validateEmail(this.selectedRecipient.name)) {
                  sentMessage += ' AppLess (MagicLink) Sent to Email '+this.selectedRecipient.name+' and Mobile Number '+this.selectedRecipient.countryCode + ' ' + this.selectedRecipient.mobile;
                } else if(this.selectedRecipient.name && this.validateEmail(this.selectedRecipient.name)) {
                  sentMessage += ' AppLess (MagicLink) Sent to Email '+this.selectedRecipient.name;
                } else if(this.selectedRecipient.countryCode && this.selectedRecipient.mobile) {
                  sentMessage += ' AppLess (MagicLink) Sent to Mobile Number '+this.selectedRecipient.countryCode + ' ' + this.selectedRecipient.mobile;
                }
              } else {
                sentMessage += ' AppLess (MagicLink) Sent to virtual users verified with Email or Mobile Number.';
              }
            }
            this.notifyMessageSend(sentMessage,this.activeForms.name);
           
            this.structuredFormSend.patchValue({
              message: ''
            });

            $('#assosiatedPatients').select2({
              placeholder: "Select Associated Patient",
              allowClear: true
            });
            
            this.structuredFormSend.patchValue({
              assosiatedPatients: ''
            });

            // this.selected = form;
            this.selectedAssosiatePatient = '';
            this.selectedAssosiatePatientName = "";
            this.closeSelectedAssociatePatient();
            this.resetRecipient();
            this.clearFormSelected();
            this.stafffillfirst = false;
            $("#response").val("");
            $("#textmessage").text("");
            $("#newloader").hide();
            this.messageadd = "";
            $("#message").val("");
            $('#assosiatedPatients').val('');
            $('#assosiatedPatients').select2({
              allowClear: false,
              placeholder: "Select Associated Patient",
              data: ""
            });
            this.afterSubmit = false;
            this.showApplessMenu = false;
            this.frmSendMode.controls['mode'].setValue("");

          } else {
            var activityName = "Send Form Fail";
            if(formSendMode == 'appless') {
              activityName =  "Send Appless Form Fail";
            }
            var activityData = {
              activityName: activityName,
              activityType: "structured forms",
              activityDescription: this.userData.displayName + " form " + this.activeForms.name + " (" + this.activeForms.id + ") to " + selectedRecipients.toString() + " sending failed" + (formSendMode == 'appless'? '(appless)' : '' )
            };
             this._structureService.trackActivity(activityData);
            this._structureService.notifyMessage({
              messge: this.activeForms.name + ' sending failed',
              delay: 1000
            });
          }
          }
        }, 2000);    
      } else {
        this._structureService.notifyMessage({
          messge: "Please choose Recipient(s)",
          delay: 1000
        });
        console.log(recipients);
      }
    } else {
      console.log("sendForm : topsend elseeeeeeeee...");
      var recipients = this.selectedRecipients;//$('#tenantUsers').val();
      if (recipients.length) {
        console.log("selected recipeints=>",recipients);
        /*********Check for integration status before send*************/
           console.log("recipients length==>"+recipients.length);
        var patientId;
        
        var selectedPatient = $("#associate-search-input").val();
        console.log('DD1',selectedPatient);
        if (this.activeForms.stafFacing == 'practitioner') {
          let assocPat = this.selectedAssosiatePatient;
          if (typeof this.selectedAssosiatePatient == 'string' && assocPat.includes('{')) {
            let pJsn = JSON.parse(assocPat);
            patientId = pJsn.id;
          } else {
            patientId = assocPat;
          }
        } else { 
          if (recipients[0].includes('--'))
        {
          console.log("recipients id==>" + recipients[0] ? recipients[0].split('--')[1] : '');
          patientId = recipients[0] ? recipients[0].split('--')[1] : ''
        } else {
          patientId = recipients[0];
        }
        }

           console.log("recipients type==>"+Number.isInteger(parseInt(recipients[0])));
           var enable_sftp_integration=this.userDataConfig.enable_sftp_integration;
           var ignore_mandatory_checking_integration=parseInt(this.userDataConfig.ignore_mandatory_checking_integration);
           if(recipients.length == 1 && Number.isInteger(parseInt(recipients[0])) && enable_sftp_integration!=1 && !ignore_mandatory_checking_integration){ 
            let message = {
              caption:  '',
              check_integration_error : false,
              txt_en : ''
            };
            var caption='';
            const checkIntegrationInFromWorklist={"form_id":this.activeForms.id,
                                                "tenant_id":this.userData.tenantId,
                                                "patient_id": patientId,
                                                 "staff_id":this.userData.userId
                                                };
            // CHP-16445: As discussion with Aparna, we are not using this form integration, so not updated anything here and in future we could remove this.
            this._formsService.checkIntegration(checkIntegrationInFromWorklist).subscribe((result)=>{
              if (result && (result["status"] && result["status"]["integrationStatus"] &&  result["status"]["integrationStatus"] == "Enabled")
                  && (result['data'] && result['data']["progressNoteIntegrationSend"] && result['data']["progressNoteIntegrationSend"] == true) && result['data']["defaultFromFilingCentersendreminder"]==true) { 
                  message = this.showIntegrationItemsMissingWarnings(result);
              }
              
              else if(result && result["status"] && result["status"]["integrationStatus"] && result["status"]["integrationStatus"] == "Enabled" && result['data']["defaultFromFilingCenterSubmit"] && result['data']["defaultFromFilingCenterSubmit"]==true) {
                  message = this.showIntegrationItemsMissingWarnings(result);
              }

              else if (result && result["status"] && result["status"]["integrationStatus"] && result["status"]["integrationStatus"] == "Enabled" && result['data']["directlinkpatientchart"] == true) {
                  message = this.showIntegrationItemsMissingWarnings(result);
              }
              else if (result && result["status"] && result["status"]["integrationStatus"] && result["status"]["integrationStatus"] == "Enabled" && (result['data']["externalFileExchangeWebhook"] == true || result['data']["progressNoteIntegrationWebhook"] == true)) {
                message = this.showIntegrationItemsMissingWarnings(result);
              }
              else if (result && result["data"] && result["data"]["cmisidChecking"] && result["data"]["cmisidChecking"] == true){
                message = this.showIntegrationItemsMissingWarnings(result);
              }
              /*****Swal here******/
              //TODO: Need to revamp all swal popups.
              if(message.check_integration_error==true && message.txt_en!=''){
                var msgtxt = message.txt_en;
                var type="error";
                var confirmButtonText="Go Back";
                var cancelButtonText="Continue Anyway";
                var confirmButtonClass="btn-warning";
                var cancelButtonClass="btn-default"
                /**Check integration swal***/
                swal({
                  title: message.caption,
                  text: msgtxt,
                  type: type,
                  showCancelButton: true,
                          cancelButtonClass: cancelButtonClass,
                          confirmButtonClass: confirmButtonClass,
                          confirmButtonText: confirmButtonText,
                          cancelButtonText: cancelButtonText,
                          closeOnConfirm: true
                  },(confirm) => {
                    if (!confirm) {
                      if($("#textmessage").text()=='') $("#textmessage").text("Sending the Form. Please wait...");
                      $("#newloader").show();
                      localStorage.setItem('autosavecalledformid', "0");
                      localStorage.setItem('autosavecalled', "false");
                      this.stafffillfirst = false;
                      const selectedRecipientsPolling = [];
                      const selectedRecipients = [];
                      this.formSendInProgress = true
                      recipients = recipients.map(element => {
                        var id = element.substr(element.indexOf(":") + 1);
                        id = id.replace(/'/g, "");
                        return id.replace(/\s/g, '');
                      });
                      
                      if (this.receipientCount > 1) {
                        // this.formSubmissionId=0;
                      }
                      let message = this.structuredFormSend.value['message'].trim();
                      // console.log(selectedRecipients,"selectedRecipients");
                      if (this.activeForms.staffFill == "false" || this.activeForms.staffFill == false) {
                        this.formSubmissionId = 0;
                      }
                      var data: any = {
                        form_id: this.activeForms.id,
                        recipients: recipients.filter(a => a.indexOf('tag-') === -1),
                        tagRecipients: recipients.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join(),
                        // userId: this.userData.userId,
                        message: message,
                        formSubmissionId: this.formSubmissionId
                      };
                      if(!isBlank(this.siteIds)){
                        data.siteIds =  (this.siteIds != '0') ? this.siteIds.join(",") : this.siteIds;
                    }else{
                        data.siteIds = '0';
                    }
                       

                      if(data.recipients.length == 0 && data.tagRecipients.length > 0 && this.userDataConfig.enable_appless_model == 1) {
                        formSendMode = 'appless';
                      }

                      
                      if (this.activeForms.stafFacing == 'practitioner') {
                        let assoVal = [];
                        if(this.selectedAssosiatePatient){
                        let assocPat = this.selectedAssosiatePatient;
                        if (typeof this.selectedAssosiatePatient == 'string' && assocPat.includes('{')) {
                          let pJsn = JSON.parse(assocPat);
                          assoVal.push(pJsn.id);
                        } else {
                          assoVal.push(assocPat);
                        }
                        data.practitionerAssociatePatient = assoVal;
                      }
                        data.practitioner = 1;
                        data.orderChange = this.orderChange;
                        console.log(this.siteId+"----"+this.faxQIntegration+"-----"+this.activeForms.patientAssociation);
                        if(this.siteId !=0 && (this.faxQIntegration) && (this.activeForms.patientAssociation =="false")){
                          console.log("entered 2");
                          data.siteId=this.siteId.toString();
                        }
                      }
                      if (this.userDataConfig.enable_nursing_agencies_visibility_restrictions == 1) {
                        data.nursingAgencies = this.userData.nursing_agencies;
                      }

                      data.formSendMode = formSendMode;
                      data.applessMode = applessMode;

                      console.log(data);
                      var deepLinking = {
                        "pushType": "",
                        "state": "eventmenu.forms",
                        "stateParams": {},
                        "tenantId": data.tenantId,
                        "tenantName": this.selectedRecipient.tenantName,
                        "formSendMode": formSendMode,
                        "applessMode": applessMode,
                        "sentId": ""
                      };
                      // console.log(data);
                      let pushMessage = "New Form to fill out";
                      data.sendForm = 1;
                      data.sendfromtop = 1;
                      data.staffFacing = this.activeForms.stafFacing;
                      this.stafffillfirst=true;
                      contentWindow.postMessage(data, '*');
                      var interval =  setInterval(()=>{ 
                      var submit_result = $("#response").val();
                         
                    if(submit_result != ""){
                        clearInterval(interval);
                        var result = JSON.parse(submit_result);
                        this.formSendInProgress = false;
                        if (result.send_to) {
                          result.send_to.forEach(element => {
                            const data = {
                              userid: element,
                              senderId: this.userData.userId,
                              organizationMasterId: this.userData.organizationMasterId,
                              formSendMode:formSendMode,
                              applessMode:applessMode
                            }
                            console.log(data)
                            selectedRecipientsPolling.push(data);
                            selectedRecipients.push(element);
                          });
                        }
                        deepLinking.sentId = result.sentId;
                        var formData = {
                          "userid": this.userData.userId,
                          "formId": this.activeForms.id,
                          "formDatadraftid": localStorage.getItem('saveasdraftidtodeleted'),
                        };
                        if (localStorage.getItem('saveasdraftidtodeleted') != "false") {
                          this._formsService.archiveDraftForm(formData).then((result: any) => {
                            localStorage.setItem('saveasdraftidtodeleted', 'false');
                          })
                        }
                        if (result.progressNoteCreated == false) {
          
                          if (result.progressNoteCreated == false && this.userDataConfig.enable_progress_note_integration == 1 && result.progressNoteIntegration == true && result.progressNoteIntegrationSend == true) {
          
                            var activityDatam: any = {};
                            console.log("in result");
                            if (result.identity_value_Patient != '' || result.identity_value_Patient != "" || result.identity_value_Patient != null
                              && result.CPRUSERNO != '' || result.CPRUSERNO != null
                              && result.patientAssociation == true
                              && result.CPRUSERNAME != null || result.CPRUSERNAME != '') {
                              activityDatam.activityName = "Form Copied to Filing Center as JSON";
                              activityDatam.activityType = "structured forms";
                              activityDatam.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sentId + ") successfully and copied the JSON file to filing center named " + result.defaultFromFilingCenterSubmitjson;
                              this._structureService.trackActivity(activityDatam);
          
                            }
                            if ((result.identity_value_Patient == '' || result.identity_value_Patient == "" || result.identity_value_Patient == null
                              || result.CPRUSERNO == '' || result.CPRUSERNO == null
                              || result.patientAssociation == false
                              || result.CPRUSERNAME == '' || result.CPRUSERNAME == null)) {
                              activityDatam.activityName = "Failed Form Copy to Filing Center as JSON";
                              activityDatam.activityType = "structured forms";
                              var messageData = '';
                              var messageData1 = '';
                              console.log("in defaultFromFilingCenter", result);
                              if (result.patientAssociation == false && result.staffFacing == "true") {
                                messageData1 = "there is no patient associated with this form";
                              }
                              else if ((result.identity_value_Patient == '' || result.identity_value_Patient == null || result.identity_value_Patient == "")) {
          
                                messageData += ", MRN";
                                if (result.firstnamepatient && result.firstnamepatient != "") {
                                  messageData += " of " + result.firstnamepatient + " " + result.lastnamepatient;
                                }
                                console.log("in MRN", messageData);
                              }
                              let f1 = 0;
                              let f2 = 0;
                              if (result.CPRUSERNO == '' || result.CPRUSERNO == null) {
                                f1 = 1;
                                messageData += ", USERNO";
                              }
                              if (result.CPRUSERNAME == '' || result.CPRUSERNAME == null) {
                                f2 = 1;
                                messageData += ", USERNAME";
                              }
                              if ((f1 == 1 || f2 == 1) && result.firstnamestaff && result.firstnamestaff != "") {
                                messageData += " of " + result.firstnamestaff + " " + result.lastnamestaff;
                              }
                              if (result.patientAssociation == false && result.staffFacing == "false") {
                                messageData += "";
                              }
                              var removeComa = messageData.charAt(0);
                              if (removeComa == ',') {
                                messageData = messageData.substring(1);
                              }
                              console.log("messageData", messageData);
                              if (messageData1 && messageData) {
                                var finalMessage = 'Generating Progress Note for Form ' + result.form_name + ' failed due to ' + messageData1 + ' and missing (' + messageData + ')';
                              }
                              else if (messageData1) {
                                var finalMessage = 'Generating Progress Note for Form ' + result.form_name + ' failed due to ' + messageData1;
                              }
                              else if (messageData) {
                                var finalMessage = 'Generating Progress Note for Form ' + result.form_name + ' failed due to missing (' + messageData + ')';
                              }
                              else {
                                var finalMessage = '';
                              }
                              if (finalMessage) {
                                activityDatam.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.formId + ") failed to copy the JSON file to filing center named " + result.defaultFromFilingCenterSubmitjson + "because" + finalMessage;
                                this._structureService.trackActivity(activityDatam);
                              }
                            }
                          }
                          console.log("in MRN", messageData);
                        }
                        console.log(selectedRecipientsPolling, "selectedRecipientsPollingselectedRecipientsPolling");
                        this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", selectedRecipientsPolling, 'form');//Need to commnd after finish form inbox
                        this._structureService.socket.emit("sendFormsToRecipients", selectedRecipientsPolling);
                        this.result = result;
          
                        /*patient reminder for form starts*/
                        console.log(this.userData.config.patient_reminder_time * 1, this.userData.config.patient_reminder_types);
                        var otherDatas = {
                          patientReminderTime: this.userData.config.patient_reminder_time * 1,
                          patientReminderTypes: this.userData.config.patient_reminder_types,
                          messageReplyTimeout: this.userData.config.message_reply_timeout,
                          sentId: this.result.sentId,
                          senderName: this.userData.displayName,
                          tenantId: this.userData.tenantId,
                          tenantName: this.userData.tenantName,
                          serverBaseUrl: this._structureService.serverBaseUrl,
                          apiVersion: this._structureService.version,
                          message: "[Reminder] You have new form to fillout",
                          formReminderType: this.userData.config.patient_reminder_checking_type,
                          environment: this._structureService.environment,
                          formSendMode: formSendMode,
                          applessMode:applessMode
                        }
                        console.log(otherDatas, "otherDatas");
                        //this._structureService.socket.emit("ReminderForForm", selectedRecipientsPolling, otherDatas);
                        this._structureService.reminderForForm(selectedRecipientsPolling, otherDatas);
                        /*patient reminder for form ends*/
                        if(this.userConfig.form_send_mode != 'sqs'){
                        this._structureService.sentPushNotification(selectedRecipientsPolling, 0, pushMessage, '', deepLinking, '', '', undefined, undefined, undefined, undefined, notificationData);
                        }
                        if (this.result.status == 1) {
                          if (this.userDataConfig.enable_collaborate_edit == 1) {
                            var enable_collaborate_edit = "collaborateDraft-Enabled";
                          } else {
                            var enable_collaborate_edit = "";
                          }
                          var activityName = "Send Form Success22";
                          if(formSendMode == 'appless') {
                            activityName =  "Send Appless Form Success22";
                          }
                          var activityData = {
                            activityName: activityName + enable_collaborate_edit,
                            activityType: "structured forms",
                            activityDescription: this.userData.displayName + ' successfully sent form ' + encodeURIComponent(this.activeForms.name) + ' (' + this.activeForms.id + ') ' + (message ? ('with message as "' + encodeURIComponent(message) + '"') : 'without message') + ' to ' + encodeURIComponent(selectedRecipients.toString())+ (formSendMode == 'appless'? '(appless)' : '' )
                          };
                          this._structureService.trackActivity(activityData);

                          var sentMessage = this.activeForms.name + ' has been sent successfully.';

                          if(this.userDataConfig.enable_appless_model == 1 && formSendMode == 'appless' && this.userDataConfig.enable_verification_of_cell_and_mobile == 1) {
                            if(data.recipients.length == 1) {
                              if(this.selectedRecipient.mobVerificationStatus && this.selectedRecipient.emailVerificationStatus && this.validateEmail(this.selectedRecipient.name) && (this.selectedRecipient.countryCode != '' && this.selectedRecipient.mobile != '')) {
                                sentMessage += ' AppLess (MagicLink) Sent to Email '+this.selectedRecipient.name+' and Mobile Number '+this.selectedRecipient.countryCode + ' ' + this.selectedRecipient.mobile;
                              } else if(this.selectedRecipient.emailVerificationStatus && this.validateEmail(this.selectedRecipient.name)){
                                sentMessage += ' AppLess (MagicLink) Sent to Email '+this.selectedRecipient.name;
                              } else if(this.selectedRecipient.mobVerificationStatus && (this.selectedRecipient.countryCode != '' && this.selectedRecipient.mobile != '')) {
                                sentMessage += ' AppLess (MagicLink) Sent to Mobile Number '+this.selectedRecipient.countryCode + ' ' + this.selectedRecipient.mobile;
                              }
                            } else {
                              sentMessage += ' AppLess (MagicLink) Sent to virtual users verified with Email or Mobile Number.';
                            }
                          } else if(this.userDataConfig.enable_appless_model == 1 && formSendMode == 'appless' && this.userDataConfig.enable_verification_of_cell_and_mobile != 1) {
                            if(data.recipients.length == 1) {
                              if(this.selectedRecipient.countryCode && this.selectedRecipient.mobile && this.selectedRecipient.name && this.validateEmail(this.selectedRecipient.name)) {
                                sentMessage += ' AppLess (MagicLink) Sent to Email '+this.selectedRecipient.name+' and Mobile Number '+this.selectedRecipient.countryCode + ' ' + this.selectedRecipient.mobile;
                              } else if(this.selectedRecipient.name && this.validateEmail(this.selectedRecipient.name)) {
                                sentMessage += ' AppLess (MagicLink) Sent to Email '+this.selectedRecipient.name;
                              } else if(this.selectedRecipient.countryCode && this.selectedRecipient.mobile) {
                                sentMessage += ' AppLess (MagicLink) Sent to Mobile Number '+this.selectedRecipient.countryCode + ' ' + this.selectedRecipient.mobile;
                              }
                            } else {
                              sentMessage += ' AppLess (MagicLink) Sent to virtual users verified with Email or Mobile Number.';
                            }
                          }
                          this.notifyMessageSend(sentMessage,this.activeForms.name);
                          this.structuredFormSend.patchValue({
                            message: ''
                          });

                          this.structuredFormSend.patchValue({
                            assosiatedPatients: ''
                          });
                          // this.selected = form;
                          this.selectedAssosiatePatient = '';
                          this.selectedAssosiatePatientName = "";
                          this.closeSelectedAssociatePatient();
                          this.resetRecipient();
                          this.clearFormSelected();
                          this.stafffillfirst = false;
                          $("#response").val("");
                          $("#textmessage").text("");
                          $("#newloader").hide();
                          this.afterSubmit = false;
                        } else {
                          var activityName = "Send Form Fail";
                          if(formSendMode == 'appless') {
                            activityName =  "Send Appless Form Fail";
                          }
                          var activityData = {
                            activityName: activityName,
                            activityType: "structured forms",
                            activityDescription: this.userData.displayName + " form " + this.activeForms.name + " (" + this.activeForms.id + ") to " + selectedRecipients.toString() + " sending failed" + (formSendMode == 'appless'? '(appless)' : '' )
                          };
                          this._structureService.trackActivity(activityData);
                          this._structureService.notifyMessage({
                            messge: this.activeForms.name + ' sending failed',
                            delay: 1000
                          });
                        }
                        }
                      },2000);
                    }else{
                      $("#textmessage").text("");
                      $("#newloader").hide();
                    }
                  })
              }else{
                caption="Are you sure ?";
                var msgtxt = "You are going to send the form to a recipient";
                var tagRecipients = recipients.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join();
                if (tagRecipients != "" || recipients.length > 1) {
                    msgtxt = "You are going to send the form to multiple recipients";
                }
                var type="warning";
                var confirmButtonText="Ok";
                var cancelButtonText="Cancel";
                var confirmButtonClass="btn-warning";
                var cancelButtonClass="btn-default";
                /**Normal Work flow swal**/
                swal({
                  title: caption,
                  text: msgtxt,
                  type: type,
                  showCancelButton: true,
                          cancelButtonClass: cancelButtonClass,
                          confirmButtonClass: confirmButtonClass,
                          confirmButtonText: confirmButtonText,
                          cancelButtonText: cancelButtonText,
                          closeOnConfirm: true
                  },(confirm) => {
                    if (confirm) {
                      if($("#textmessage").text()=='') $("#textmessage").text("Sending the Form. Please wait...");
                      $("#newloader").show();
                      localStorage.setItem('autosavecalledformid', "0");
                      localStorage.setItem('autosavecalled', "false");
                      this.stafffillfirst = false;
                      const selectedRecipientsPolling = [];
                      const selectedRecipients = [];
                      this.formSendInProgress = true
                      recipients = recipients.map(element => {
                        var id = element.substr(element.indexOf(":") + 1);
                        id = id.replace(/'/g, "");
                        return id.replace(/\s/g, '');
                      });
                     
                      let message = this.structuredFormSend.value['message'].trim();
                      // console.log(selectedRecipients,"selectedRecipients");
                      if (this.activeForms.staffFill == "false" || this.activeForms.staffFill == false) {
                        this.formSubmissionId = 0;
                      }
                      var data: any = {
                        form_id: this.activeForms.id,
                        recipients: recipients.filter(a => a.indexOf('tag-') === -1),
                        tagRecipients: recipients.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join(),
                        // userId: this.userData.userId,
                        message: message,
                        formSubmissionId: this.formSubmissionId,
                        staffFacing:this.staffFacing
                      };
                      if(!isBlank(this.siteIds)){
                        data.siteIds =  (this.siteIds != '0') ? this.siteIds.join(",") : this.siteIds;
                    }else{
                        data.siteIds = '0';
                    }
                      

                      if(data.recipients.length == 0 && data.tagRecipients.length > 0 && this.userDataConfig.enable_appless_model == 1) {
                        formSendMode = 'appless';
                      }

                      if (this.activeForms.stafFacing == 'practitioner') {
                        data.siteId="0";
                        let assoVal = [];
                        let assocPat = this.selectedAssosiatePatient;
                        if(this.selectedAssosiatePatient){
                        if (typeof this.selectedAssosiatePatient == 'string' && assocPat.includes('{')) {
                          let pJsn = JSON.parse(assocPat);
                          assoVal.push(pJsn.id);
                        } else {
                          assoVal.push(assocPat);
                        }
                        data.practitionerAssociatePatient = assoVal;
                      }
                        data.practitioner = 1;
                        data.orderChange = this.orderChange;
                        console.log(this.siteId+"----"+this.faxQIntegration+"-----"+this.activeForms.patientAssociation);
                      if(this.siteId !=0 && (this.faxQIntegration) && (this.activeForms.patientAssociation =="false")){
                        console.log("entered practitioner 1 ");
                        data.siteId=this.siteId.toString();
                      }
                      }
                      if (this.userDataConfig.enable_nursing_agencies_visibility_restrictions == 1) {
                        data.nursingAgencies = this.userData.nursing_agencies;
                      }
                    //   data.tenantId = this._structureService.getCookie('tenantId');
                    //   if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')) {
                    //     data.tenantId = this._structureService.getCookie('crossTenantId');
                    //   }

                      data.formSendMode = formSendMode;
                      data.applessMode = applessMode;

                      console.log(this.userDataConfig);
                      var deepLinking = {
                        "pushType": "",
                        "state": "eventmenu.forms",
                        "stateParams": {},
                        "tenantId": data.tenantId,
                        "tenantName": this.selectedRecipient.tenantName,
                        "formSendMode": formSendMode,
                        "applessMode": applessMode,
                        "sentId": ""
                      };
                      // console.log(data);
                      let pushMessage = "New Form to fill out";

                       // form stabilization
                      data.sendForm = 1;
                      if(sendfrommachform == 1){ 
                       data.sendfromtop = 0;
                      }
                      
                     contentWindow.postMessage(data, '*');

                     this.stafffillfirst = true;
                    $(".structured-form").css('display','block !important;');
                    
                      
                      // form stabilization
                    var interval =  setInterval(()=>{ 
                   

                       var resultnew = $("#response").val();
                       
                       if(resultnew != ""){
                        clearInterval(interval);
                       }
                      
                      
                       
                        if(resultnew != ""){
                         var result = JSON.parse(resultnew);
                     
                        this.formSendInProgress = false;
                        if (result.send_to) {
                          result.send_to.forEach(element => {
                            const deeplinkdata = {
                              userid: element,
                              senderId: this.userData.userId,
                              organizationMasterId: this.userData.organizationMasterId,
                              formSendMode:formSendMode,
                              applessMode:applessMode
                            }
                            console.log(deeplinkdata)
                            selectedRecipientsPolling.push(deeplinkdata);
                            selectedRecipients.push(deeplinkdata);
                          });
                        }
                        deepLinking.sentId = result.sentId;
                        var formData = {
                          "userid": this.userData.userId,
                          "formId": this.activeForms.id,
                          "formDatadraftid": localStorage.getItem('saveasdraftidtodeleted'),
                        };
                        if (localStorage.getItem('saveasdraftidtodeleted') != "false") {
                          this._formsService.archiveDraftForm(formData).then((result: any) => {
                            localStorage.setItem('saveasdraftidtodeleted', 'false');
                          })
                        }
                        if (result.progressNoteCreated == false) {
          
                          
                          if (result.progressNoteCreated == false && this.userDataConfig.enable_progress_note_integration == 1 && result.progressNoteIntegration == true && result.progressNoteIntegrationSend == true) {
          
                            var activityDatam: any = {};
                            console.log("in result");
                            if (result.identity_value_Patient != '' || result.identity_value_Patient != "" || result.identity_value_Patient != null
                              && result.CPRUSERNO != '' || result.CPRUSERNO != null
                              && result.patientAssociation == true
                              && result.CPRUSERNAME != null || result.CPRUSERNAME != '') {
                              activityDatam.activityName = "Form Copied to Filing Center as JSON";
                              activityDatam.activityType = "structured forms";
                              activityDatam.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sentId + ") successfully and copied the JSON file to filing center named " + result.defaultFromFilingCenterSubmitjson;
                              this._structureService.trackActivity(activityDatam);
          
                            }
                            if ((result.identity_value_Patient == '' || result.identity_value_Patient == "" || result.identity_value_Patient == null
                              || result.CPRUSERNO == '' || result.CPRUSERNO == null
                              || result.patientAssociation == false
                              || result.CPRUSERNAME == '' || result.CPRUSERNAME == null)) {
                              activityDatam.activityName = "Failed Form Copy to Filing Center as JSON";
                              activityDatam.activityType = "structured forms";
                              var messageData = '';
                              var messageData1 = '';
                              console.log("in defaultFromFilingCenter", result);
                              if (result.patientAssociation == false && result.staffFacing == "true") {
                                messageData1 = "there is no patient associated with this form";
                              }
                              else if ((result.identity_value_Patient == '' || result.identity_value_Patient == null || result.identity_value_Patient == "")) {
          
                                messageData += ", MRN";
                                if (result.firstnamepatient && result.firstnamepatient != "") {
                                  messageData += " of " + result.firstnamepatient + " " + result.lastnamepatient;
                                }
                                console.log("in MRN", messageData);
                              }
                              let f1 = 0;
                              let f2 = 0;
                              if (result.CPRUSERNO == '' || result.CPRUSERNO == null) {
                                f1 = 1;
                                messageData += ", USERNO";
                              }
                              if (result.CPRUSERNAME == '' || result.CPRUSERNAME == null) {
                                f2 = 1;
                                messageData += ", USERNAME";
                              }
                              if ((f1 == 1 || f2 == 1) && result.firstnamestaff && result.firstnamestaff != "") {
                                messageData += " of " + result.firstnamestaff + " " + result.lastnamestaff;
                              }
                              if (result.patientAssociation == false && result.staffFacing == "false") {
                                messageData += "";
                              }
                              var removeComa = messageData.charAt(0);
                              if (removeComa == ',') {
                                messageData = messageData.substring(1);
                              }
                              console.log("messageData", messageData);
                              if (messageData1 && messageData) {
                                var finalMessage = 'Generating Progress Note for Form ' + result.form_name + ' failed due to ' + messageData1 + ' and missing (' + messageData + ')';
                              }
                              else if (messageData1) {
                                var finalMessage = 'Generating Progress Note for Form ' + result.form_name + ' failed due to ' + messageData1;
                              }
                              else if (messageData) {
                                var finalMessage = 'Generating Progress Note for Form ' + result.form_name + ' failed due to missing (' + messageData + ')';
                              }
                              else {
                                var finalMessage = '';
                              }
                              if (finalMessage) {
                                activityDatam.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.formId + ") failed to copy the JSON file to filing center named " + result.defaultFromFilingCenterSubmitjson + "because" + finalMessage;
                                this._structureService.trackActivity(activityDatam);
                               
                              }
                            }
                          }
                          console.log("in MRN", messageData);
                        }
                        console.log(selectedRecipientsPolling, "selectedRecipientsPollingselectedRecipientsPolling");
                        this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", selectedRecipientsPolling, 'form');//Need to commnd after finish form inbox
                        this._structureService.socket.emit("sendFormsToRecipients", selectedRecipientsPolling);
                        this.result = result;
          
                        /*patient reminder for form starts*/
                        console.log(this.userData.config.patient_reminder_time * 1, this.userData.config.patient_reminder_types);
                        var otherDatas = {
                          patientReminderTime: this.userData.config.patient_reminder_time * 1,
                          patientReminderTypes: this.userData.config.patient_reminder_types,
                          messageReplyTimeout: this.userData.config.message_reply_timeout,
                          sentId: this.result.sentId,
                          senderName: this.userData.displayName,
                          tenantId: this.userData.tenantId,
                          tenantName: this.userData.tenantName,
                          serverBaseUrl: this._structureService.serverBaseUrl,
                          apiVersion: this._structureService.version,
                          message: "[Reminder] You have new form to fillout",
                          formReminderType: this.userData.config.patient_reminder_checking_type,
                          environment: this._structureService.environment,
                          formSendMode: formSendMode,
                          applessMode: applessMode
                        }
                        console.log(otherDatas, "otherDatas");                        
                        //this._structureService.socket.emit("ReminderForForm", selectedRecipientsPolling, otherDatas);
                        this._structureService.reminderForForm(selectedRecipientsPolling, otherDatas);
                        /*patient reminder for form ends*/
                        if(this.userConfig.form_send_mode != 'sqs'){
                        this._structureService.sentPushNotification(selectedRecipientsPolling, 0, pushMessage, '', deepLinking, '', '', undefined, undefined, undefined, undefined, notificationData);
                        }
                        if (this.result.status == 1) {
                          if (this.userDataConfig.enable_collaborate_edit == 1) {
                            var enable_collaborate_edit = "collaborateDraft-Enabled";
                          } else {
                            var enable_collaborate_edit = "";
                          }
                          var activityName = "Send Form Success22";
                          if(formSendMode == 'appless') {
                            activityName =  "Send Appless Form Success22";
                          }

                          var activityData = {
                            activityName: activityName + enable_collaborate_edit,
                            activityType: "structured forms",
                            activityDescription: this.userData.displayName + ' successfully sent form ' + encodeURIComponent(this.activeForms.name) + ' (' + this.activeForms.id + ') ' + (message ? ('with message as "' + encodeURIComponent(message) + '"') : 'without message') + ' to ' + encodeURIComponent(selectedRecipients.toString()) + (formSendMode == 'appless'? '(appless)' : '' )
                          };
                          this._structureService.trackActivity(activityData);

                          var sentMessage = this.activeForms.name + ' has been sent successfully.';
                          if(this.loadFormFrom == 'worklistCenter') {
                            this.submitForward.emit();
                          }
                          if(this.userDataConfig.enable_appless_model == 1 && formSendMode == 'appless' && this.userDataConfig.enable_verification_of_cell_and_mobile == 1) {
                            if(data.recipients.length == 1) {
                              if(this.selectedRecipient.mobVerificationStatus && this.selectedRecipient.emailVerificationStatus && this.validateEmail(this.selectedRecipient.name) && (this.selectedRecipient.countryCode != '' && this.selectedRecipient.mobile != '')) {
                                sentMessage += ' AppLess (MagicLink) Sent to Email '+this.selectedRecipient.name+' and Mobile Number '+this.selectedRecipient.countryCode + ' ' + this.selectedRecipient.mobile;
                              } else if(this.selectedRecipient.emailVerificationStatus && this.validateEmail(this.selectedRecipient.name)){
                                sentMessage += ' AppLess (MagicLink) Sent to Email '+this.selectedRecipient.name;
                              } else if(this.selectedRecipient.mobVerificationStatus && (this.selectedRecipient.countryCode != '' && this.selectedRecipient.mobile != '')) {
                                sentMessage += ' AppLess (MagicLink) Sent to Mobile Number '+this.selectedRecipient.countryCode + ' ' + this.selectedRecipient.mobile;
                              }
                            } else {
                              sentMessage += ' AppLess (MagicLink) Sent to virtual users verified with Email or Mobile Number.';
                            }
                          } else if(this.userDataConfig.enable_appless_model == 1 && formSendMode == 'appless' && this.userDataConfig.enable_verification_of_cell_and_mobile != 1) {
                            if(data.recipients.length == 1) {
                              if(this.selectedRecipient.countryCode && this.selectedRecipient.mobile && this.selectedRecipient.name && this.validateEmail(this.selectedRecipient.name)) {
                                sentMessage += ' AppLess (MagicLink) Sent to Email '+this.selectedRecipient.name+' and Mobile Number '+this.selectedRecipient.countryCode + ' ' + this.selectedRecipient.mobile;
                              } else if(this.selectedRecipient.name && this.validateEmail(this.selectedRecipient.name)) {
                                sentMessage += ' AppLess (MagicLink) Sent to Email '+this.selectedRecipient.name;
                              } else if(this.selectedRecipient.countryCode && this.selectedRecipient.mobile) {
                                sentMessage += ' AppLess (MagicLink) Sent to Mobile Number '+this.selectedRecipient.countryCode + ' ' + this.selectedRecipient.mobile;
                              }
                            } else {
                              sentMessage += ' AppLess (MagicLink) Sent to virtual users verified with Email or Mobile Number.';
                            }
                          }
                          this.notifyMessageSend(sentMessage,this.activeForms.name);
                          this.structuredFormSend.patchValue({
                            message: ''
                          });

                          this.structuredFormSend.patchValue({
                            assosiatedPatients: ''
                          });
                          // this.selected = form;
                          this.selectedAssosiatePatient = '';
                          this.selectedAssosiatePatientName = "";
                          this.closeSelectedAssociatePatient();
                          this.resetRecipient();
                          this.clearFormSelected();
                          this.stafffillfirst = false;
                          $("#response").val("");
                          $("#textmessage").text("");
                          $("#newloader").hide();
                          this.messageadd = "";
                          $("#message").val("");
                          this.afterSubmit = false;
                        } else {
                          var activityName = "Send Form Fail";
                          if(formSendMode == 'appless') {
                            activityName =  "Send Appless Form Fail";
                          }
                          var activityData = {
                            activityName: activityName,
                            activityType: "structured forms",
                            activityDescription: this.userData.displayName + " form " + this.activeForms.name + " (" + this.activeForms.id + ") to " + selectedRecipients.toString() + " sending failed" + (formSendMode == 'appless'? '(appless)' : '' )
                          };
                           this._structureService.trackActivity(activityData);
                          this._structureService.notifyMessage({
                            messge: this.activeForms.name + ' sending failed',
                            delay: 1000
                          });

                        }
                        }
                       }, 2000);
                    }else{
                      $("#textmessage").text("");
                      $("#newloader").hide();
                    }
                  })
                  /**Swal ends here....**/
              }
              
              
            });                                
           }else{ //Normal Send flow.......................
            var msgtxt = "You are going to send the form to a recipient";
            var tagRecipients = recipients.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join();
            if (tagRecipients != "" || recipients.length > 1) {
              msgtxt = "You are going to send the form to multiple recipients";
            }
            swal({
              title: "Are you sure?",
              text: msgtxt,
              type: "warning",
              showCancelButton: true,
              cancelButtonClass: "btn-default",
              confirmButtonClass: "btn-warning",
              confirmButtonText: "Ok",
              closeOnConfirm: true
            }, (confirm) => {
              if (confirm) {
                if($("#textmessage").text()=='') $("#textmessage").text("Sending the Form. Please wait...");
                $("#newloader").show();
                localStorage.setItem('autosavecalledformid', "0");
                localStorage.setItem('autosavecalled', "false");
                this.stafffillfirst = false;
                const selectedRecipientsPolling = [];
                const selectedRecipients = [];
                this.formSendInProgress = true
                recipients = recipients.map(element => {
                  var id = element.substr(element.indexOf(":") + 1);
                  id = id.replace(/'/g, "");
                  return id.replace(/\s/g, '');
                });
                
                let message = this.structuredFormSend.value['message'].trim();
                // console.log(selectedRecipients,"selectedRecipients");
                if (this.activeForms.staffFill == "false" || this.activeForms.staffFill == false) {
                  this.formSubmissionId = 0;
                }
                var data: any = {
                  form_id: this.activeForms.id,
                  recipients: recipients.filter(a => a.indexOf('tag-') === -1),
                  tagRecipients: recipients.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join(),
                //   userId: this.userData.userId,
                  message: message,
                  formSubmissionId: this.formSubmissionId,
                  staffFacing:this.staffFacing
                };
               if(!isBlank(this.siteIds)){
                    data.siteIds =  (this.siteIds != '0') ? this.siteIds.join(",") : this.siteIds;
                }else{
                    data.siteIds = '0';
                }
               

                if(data.recipients.length == 0 && data.tagRecipients.length > 0 && this.userDataConfig.enable_appless_model == 1) {
                  formSendMode = 'appless';
                }

                if (this.activeForms.stafFacing == 'practitioner') {
                  let assoVal = [];
                  let assocPat = this.selectedAssosiatePatient;
                  if(this.selectedAssosiatePatient){
                  if (typeof this.selectedAssosiatePatient == 'string' && assocPat.includes('{')) {
                    let pJsn = JSON.parse(assocPat);
                    assoVal.push(pJsn.id);
                  } else {
                    assoVal.push(assocPat);
                  }
                  data.practitionerAssociatePatient = assoVal;
                }
                  data.practitioner = 1;
                  data.orderChange = this.orderChange;
                  console.log(this.siteId+"----"+this.faxQIntegration+"-----"+this.activeForms.patientAssociation);
                  if(this.siteId !=0 && (this.faxQIntegration) && (this.activeForms.patientAssociation =="false")){
                    console.log("entered 4");
                    data.siteId=this.siteId.toString();
                  }
                }

                if (this.userDataConfig.enable_nursing_agencies_visibility_restrictions == 1) {
                  data.nursingAgencies = this.userData.nursing_agencies;
                }

               
                data.formSendMode = formSendMode;
                data.applessMode = applessMode;
                console.log(data);
                var deepLinking = {
                  "pushType": "",
                  "state": "eventmenu.forms",
                  "stateParams": {},
                  "tenantId": data.tenantId,
                  "tenantName": this.selectedRecipient? this.selectedRecipient.tenantName:'',
                  "formSendMode": formSendMode,
                  "applessMode": applessMode,
                  "sentId": ""
                };
                // console.log(data);
                let pushMessage = "New Form to fill out";
                // form stabilization
                 data.sendForm = 1;
                 if(sendfrommachform == 1){ 
                   data.sendfromtop = 0;
                 }
                 contentWindow.postMessage(data, '*');
                 this.stafffillfirst = true;
                 $(".structured-form").css('display','block !important;');
                 var interval =  setInterval(()=>{ 
                 var resultnew = $("#response").val();
                 if(resultnew != ""){
                  clearInterval(interval);
                 }
                 if(resultnew != ""){
                  var result = JSON.parse(resultnew);
                  this.formSendInProgress = false;
                  if (result.send_to) {
                    result.send_to.forEach(element => {
                      const data = {
                        userid: element,
                        senderId: this.userData.userId,
                        organizationMasterId: this.userData.organizationMasterId,
                        formSendMode : formSendMode,
                        applessMode:applessMode
                      }
                      console.log(data)
                      selectedRecipientsPolling.push(data);
                      selectedRecipients.push(element);
                    });
                  }
                  deepLinking.sentId = result.sentId;
                  var formData = {
                    "userid": this.userData.userId,
                    "formId": this.activeForms.id,
                    "formDatadraftid": localStorage.getItem('saveasdraftidtodeleted'),
                  };
                  if (localStorage.getItem('saveasdraftidtodeleted') != "false") {
                    this._formsService.archiveDraftForm(formData).then((result: any) => {
                      localStorage.setItem('saveasdraftidtodeleted', 'false');
                    })
                  }
                  if (result.progressNoteCreated == false) {
    
                  
                    if (result.progressNoteCreated == false && this.userDataConfig.enable_progress_note_integration == 1 && result.progressNoteIntegration == true && result.progressNoteIntegrationSend == true) {
    
                      var activityDatam: any = {};
                      console.log("in result");
                      if (result.identity_value_Patient != '' || result.identity_value_Patient != "" || result.identity_value_Patient != null
                        && result.CPRUSERNO != '' || result.CPRUSERNO != null
                        && result.patientAssociation == true
                        && result.CPRUSERNAME != null || result.CPRUSERNAME != '') {
                        activityDatam.activityName = "Form Copied to Filing Center as JSON";
                        activityDatam.activityType = "structured forms";
                        activityDatam.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sentId + ") successfully and copied the JSON file to filing center named " + result.defaultFromFilingCenterSubmitjson;
                        this._structureService.trackActivity(activityDatam);
    
                      }
                      if ((result.identity_value_Patient == '' || result.identity_value_Patient == "" || result.identity_value_Patient == null
                        || result.CPRUSERNO == '' || result.CPRUSERNO == null
                        || result.patientAssociation == false
                        || result.CPRUSERNAME == '' || result.CPRUSERNAME == null)) {
                        activityDatam.activityName = "Failed Form Copy to Filing Center as JSON";
                        activityDatam.activityType = "structured forms";
                        var messageData = '';
                        var messageData1 = '';
                        console.log("in defaultFromFilingCenter", result);
                        if (result.patientAssociation == false && result.staffFacing == "true") {
                          messageData1 = "there is no patient associated with this form";
                        }
                        else if ((result.identity_value_Patient == '' || result.identity_value_Patient == null || result.identity_value_Patient == "")) {
    
                          messageData += ", MRN";
                          if (result.firstnamepatient && result.firstnamepatient != "") {
                            messageData += " of " + result.firstnamepatient + " " + result.lastnamepatient;
                          }
                          console.log("in MRN", messageData);
                        }
                        let f1 = 0;
                        let f2 = 0;
                        if (result.CPRUSERNO == '' || result.CPRUSERNO == null) {
                          f1 = 1;
                          messageData += ", USERNO";
                        }
                        if (result.CPRUSERNAME == '' || result.CPRUSERNAME == null) {
                          f2 = 1;
                          messageData += ", USERNAME";
                        }
                        if ((f1 == 1 || f2 == 1) && result.firstnamestaff && result.firstnamestaff != "") {
                          messageData += " of " + result.firstnamestaff + " " + result.lastnamestaff;
                        }
                        if (result.patientAssociation == false && result.staffFacing == "false") {
                          messageData += "";
                        }
                        var removeComa = messageData.charAt(0);
                        if (removeComa == ',') {
                          messageData = messageData.substring(1);
                        }
                        console.log("messageData", messageData);
                        if (messageData1 && messageData) {
                          var finalMessage = 'Generating Progress Note for Form ' + result.form_name + ' failed due to ' + messageData1 + ' and missing (' + messageData + ')';
                        }
                        else if (messageData1) {
                          var finalMessage = 'Generating Progress Note for Form ' + result.form_name + ' failed due to ' + messageData1;
                        }
                        else if (messageData) {
                          var finalMessage = 'Generating Progress Note for Form ' + result.form_name + ' failed due to missing (' + messageData + ')';
                        }
                        else {
                          var finalMessage = '';
                        }
                        if (finalMessage) {
                          activityDatam.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.formId + ") failed to copy the JSON file to filing center named " + result.defaultFromFilingCenterSubmitjson + "because" + finalMessage;
                          this._structureService.trackActivity(activityDatam);
                          
                        }
                      }
                    }
                  }
                  console.log(selectedRecipientsPolling, "selectedRecipientsPollingselectedRecipientsPolling");
                  this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", selectedRecipientsPolling, 'form');//Need to commnd after finish form inbox
                  this._structureService.socket.emit("sendFormsToRecipients", selectedRecipientsPolling);
                  this.result = result;
    
                  /*patient reminder for form starts*/
                  console.log(this.userData.config.patient_reminder_time * 1, this.userData.config.patient_reminder_types);
                  var otherDatas = {
                    patientReminderTime: this.userData.config.patient_reminder_time * 1,
                    patientReminderTypes: this.userData.config.patient_reminder_types,
                    messageReplyTimeout: this.userData.config.message_reply_timeout,
                    sentId: this.result.sentId,
                    senderName: this.userData.displayName,
                    tenantId: this.userData.tenantId,
                    tenantName: this.userData.tenantName,
                    serverBaseUrl: this._structureService.serverBaseUrl,
                    apiVersion: this._structureService.version,
                    message: "[Reminder] You have new form to fillout",
                    formReminderType: this.userData.config.patient_reminder_checking_type,
                    environment: this._structureService.environment,
                    formSendMode: formSendMode,
                    applessMode:applessMode
                  }
                  
                  //this._structureService.socket.emit("ReminderForForm", selectedRecipientsPolling, otherDatas);
                  this._structureService.reminderForForm(selectedRecipientsPolling, otherDatas);
                  /*patient reminder for form ends*/
                  if(this.userConfig.form_send_mode != 'sqs'){
                   this._structureService.sentPushNotification(selectedRecipientsPolling, 0, pushMessage, '', deepLinking, '', '', undefined, undefined, undefined, undefined, notificationData);
                  }
                  if (this.result.status == 1) {
                    if (this.userDataConfig.enable_collaborate_edit == 1) {
                      var enable_collaborate_edit = "collaborateDraft-Enabled";
                    } else {
                      var enable_collaborate_edit = "";
                    }
                    var activityName = "Send Form Success22";
                    if(formSendMode == 'appless') {
                      activityName =  "Send Appless Form Success22";
                    }
                    var activityData = {
                      activityName: activityName + enable_collaborate_edit,
                      activityType: "structured forms",
                      activityDescription: this.userData.displayName + ' successfully sent form ' + encodeURIComponent(this.activeForms.name) + ' (' + this.activeForms.id + ') ' + (message ? ('with message as "' + encodeURIComponent(message) + '"') : 'without message') + ' to ' + encodeURIComponent(selectedRecipients.toString()) + (formSendMode == 'appless'? '(appless)' : '' )
                    };
                    this._structureService.trackActivity(activityData);

                    var sentMessage = this.activeForms.name + ' has been sent successfully.';

                    if(this.userDataConfig.enable_appless_model == 1 && formSendMode == 'appless' && this.userDataConfig.enable_verification_of_cell_and_mobile == 1) {
                      if(data.recipients.length == 1) {
                        if(this.selectedRecipient.mobVerificationStatus && this.selectedRecipient.emailVerificationStatus && this.validateEmail(this.selectedRecipient.name) && (this.selectedRecipient.countryCode != '' && this.selectedRecipient.mobile != '')) {
                          sentMessage += ' AppLess (MagicLink) Sent to Email '+this.selectedRecipient.name+' and Mobile Number '+this.selectedRecipient.countryCode + ' ' + this.selectedRecipient.mobile;
                        } else if(this.selectedRecipient.emailVerificationStatus && this.validateEmail(this.selectedRecipient.name)){
                          sentMessage += ' AppLess (MagicLink) Sent to Email '+this.selectedRecipient.name;
                        } else if(this.selectedRecipient.mobVerificationStatus && (this.selectedRecipient.countryCode != '' && this.selectedRecipient.mobile != '')) {
                          sentMessage += ' AppLess (MagicLink) Sent to Mobile Number '+this.selectedRecipient.countryCode + ' ' + this.selectedRecipient.mobile;
                        }
                      } else {
                        sentMessage += ' AppLess (MagicLink) Sent to virtual users verified with Email or Mobile Number.';
                      }
                    } else if(this.userDataConfig.enable_appless_model == 1 && formSendMode == 'appless' && this.userDataConfig.enable_verification_of_cell_and_mobile != 1) {
                      if(data.recipients.length == 1) {
                        if(this.selectedRecipient.countryCode && this.selectedRecipient.mobile && this.selectedRecipient.name && this.validateEmail(this.selectedRecipient.name)) {
                          sentMessage += ' AppLess (MagicLink) Sent to Email '+this.selectedRecipient.name+' and Mobile Number '+this.selectedRecipient.countryCode + ' ' + this.selectedRecipient.mobile;
                        } else if(this.selectedRecipient.name && this.validateEmail(this.selectedRecipient.name)) {
                          sentMessage += ' AppLess (MagicLink) Sent to Email '+this.selectedRecipient.name;
                        } else if(this.selectedRecipient.countryCode && this.selectedRecipient.mobile) {
                          sentMessage += ' AppLess (MagicLink) Sent to Mobile Number '+this.selectedRecipient.countryCode + ' ' + this.selectedRecipient.mobile;
                        }
                      } else {
                        sentMessage += ' AppLess (MagicLink) Sent to virtual users verified with Email or Mobile Number.';
                      }
                    }
                    this.notifyMessageSend(sentMessage,this.activeForms.name);
                    this.structuredFormSend.patchValue({
                      message: '',
                      assosiatedPatients: ''
                    });
                    // this.selected = form;
                    this.selectedAssosiatePatient = '';
                    this.selectedAssosiatePatientName = "";
                    this.closeSelectedAssociatePatient();
                    this.resetRecipient();
                    this.clearFormSelected();
                    this.stafffillfirst = false;
                    $("#response").val("");
                    $("#textmessage").text("");
                    $("#newloader").hide();
                    this.messageadd = "";
                    
                    this.afterSubmit = false;
                  } else {
                    var activityName = "Send Form Fail";
                    if(formSendMode == 'appless') {
                      activityName =  "Send Appless Form Fail";
                    }

                    var activityData = {
                      activityName: activityName,
                      activityType: "structured forms",
                      activityDescription: this.userData.displayName + " form " + this.activeForms.name + " (" + this.activeForms.id + ") to " + selectedRecipients.toString() + " sending failed" + (formSendMode == 'appless'? '(appless)' : '' )
                    };
                    this._structureService.trackActivity(activityData);
                    this._structureService.notifyMessage({
                      messge: this.activeForms.name + ' sending failed',
                      delay: 1000
                     });
                    }
                   }
                  }, 2000);    
                }else{
                  $("#textmessage").text("");
                  $("#newloader").hide();
                }
            })
           }

       /*********Check for integration status before send*************/
        
        

        var x = document.getElementsByClassName("btn-warning");
     
        x[0].setAttribute("id", "send_cnfrm_btn");


      } else {
        this._structureService.notifyMessage({
          messge: "Please choose Recipient(s)",
          delay: 1000
        });
        console.log(recipients);
      }
    }
  }
  
  goToPatientAssociatedForm(form, fromReset: boolean = false) {
    this.formSendInProgress = false;
    console.log("-----active forms details----------");
    console.log(form);
    this.activeForms='';
    this.activeForms=form;
    if (this.activeForms.staffFill == "true") {
      this.staffFill = true;
      
    } else {
      this.staffFill = false;
    }
    this.allowRecipientRoles = (this.activeForms.stafFacing == 'false' || this.activeForms.stafFacing == 'practitioner') ? true : false;
    this.autosavecalledformid = localStorage.getItem('autosavecalledformid');
    this.autosavecalled = localStorage.getItem('autosavecalled');
    this.selectedAssosiatePatient = this.selectedAssosiatePatientDrivenId;
    this.selectedAssosiatePatientName = this.selectedAssosiatePatientDisplayName;
    if (this.autosavecalled == "true" && this.patientAssociatedActiveForm) {
     
      this.patientAssociatedActiveForm=true;
      var msgtxt = "You want to close this Form or continue with the form?"
      swal({
        title: "Are you sure?",
        text: msgtxt,
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        cancelButtonText: "Close",
        confirmButtonText: "Continue",
        closeOnConfirm: true
       },(confirm) => {
        if (confirm) {}
        else{
          var activityData = {
            activityName: "Switched the form before saved as draft ",
            activityType: "structured forms",
            activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + this.selectedAssosiatePatient + "- Switched the form before saved as draft - " + this.activeForms.name + " (" + this.activeForms.id + ") "
          };
          this._structureService.trackActivity(activityData);
          localStorage.setItem('autosavecalledformid', "0");
          localStorage.setItem('autosavecalled', "false");
          if (this.activeForms.externalFileExchange != true) {
            
            var datacheck: any = {
              formid: this.activeForms.id,
              loggedinuser: this.userData.userId,
              patientid: this.selectedAssosiatePatientDrivenId,
              enableCollaborateEdit: this.userDataConfig.enable_collaborate_edit,
              admissionId: this._structureService.isMultiAdmissionsEnabled && !isBlank(this.selectedAdmission) ? this.selectedAdmission.id : undefined

            };
            this._formsService.checkDraftForm(datacheck).then((result: any) => {

              console.log(result.nodrafts);
              if (result.nodrafts == false) {
                var activityData = {
                  activityName: "Patient already have drafts for the selected form",
                  activityType: "structured forms",
                  activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + this.selectedAssosiatePatientDisplayName + "- this patient already have drafts for the form- " + this.activeForms.name + " (" + this.activeForms.id + ") "
                };
                this._structureService.trackActivity(activityData);
                if (this.userDataConfig.enable_collaborate_edit == 1) {
                  var worklistname = " in All Form Worklist";
                } else {
                  var worklistname = " in My Form Worklist";
                }
                if (!this.brightreeFormLandingFlow) {
                  //TODO: Need to revamp all swal popups [CHP-6901].
                var msgtxt = "You already have drafts for the Patient " + this.selectedAssosiatePatientDisplayName + worklistname;
                swal({
                  title: "Are you sure?",
                  text: msgtxt,
                  type: "warning",
                  showCancelButton: true,
                  cancelButtonClass: "btn-default",
                  confirmButtonClass: "btn-warning",
                  cancelButtonText: "Continue",
                  confirmButtonText: "Go To Drafts",
                  closeOnConfirm: true
                }, (confirm) => {
                  if (confirm) {
                    var activityData = {
                      activityName: "Patient already have drafts for the selected form and navigated to draft bucket",
                      activityType: "structured forms",
                      activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + this.selectedAssosiatePatientDisplayName + "- this patient already have drafts for the form- and navigated to draft bucket " + this.activeForms.name + " (" + this.activeForms.id + ") "
                    };
                    this._structureService.trackActivity(activityData);
                    this._structureService.setCookie('tabname','DRAFTS', 1);
                    this.emitSubmitFormToPAH()
                    //this.router.navigate(['/forms/worklist']);
                    if (this.userDataConfig.enable_collaborate_edit == 1) {
                      this.router.navigate(['/forms/list']);
                    } else {
                      this.router.navigate(['/forms/worklist']);
                    }
                    this._sharedService.myFormsType = "DRAFTS";
                  } else {
                    if (this.userDataConfig.enable_collaborate_edit == 1) {
                      var enable_collaborate_edit = "collaborateDraft-Enabled";
                    } else {
                      var enable_collaborate_edit = "";
                    }
                    var activityData = {
                      activityName: "Patient already have drafts for the selected form and continue with the form" + enable_collaborate_edit,
                      activityType: "structured forms",
                      activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + this.selectedAssosiatePatientDisplayName + "- this patient already have drafts for the form - and continue with the form " + this.activeForms.name + " (" + this.activeForms.id + ") "
                    };
                    this._structureService.trackActivity(activityData);
                  }

                })
                }
              }


            })
          }
         
          this.selectedAssosiatePatient = this.selectedAssosiatePatientDrivenId;
          this.selectedAssosiatePatientName = this.selectedAssosiatePatientDisplayName;
          this.clearForm = 0;
          var recipients = this.selectedRecipients;

          if (recipients.length) {
            this.stafffillfirst = true;
          } else {
            this.stafffillfirst = false;
          }
          this.setIframeUrl();
          var activityData = {
            activityName: "Select Associate Patient",
            activityType: "structured forms",
            activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Associate Patient -333" + this.selectedAssosiatePatientName + "(" + this.selectedAssosiatePatient + ") Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
          };
          this._structureService.trackActivity(activityData);
          

          
        }
      })
    } else {
     
      this.patientAssociatedActiveForm=true;
      
        //new Code
        if (this.activeForms.externalFileExchange != true) {
         
          var datacheck: any = {
            formid: this.activeForms.id,
            loggedinuser: this.userData.userId,
            patientid:  this.selectedAssosiatePatient,
            enableCollaborateEdit: this.userDataConfig.enable_collaborate_edit,
            admissionId: this._structureService.isMultiAdmissionsEnabled && !isBlank(this.selectedAdmission) ? this.selectedAdmission.id : undefined
          };
  
          if (this.activeForms.stafFacing == 'true' && this.activeForms.stafFacing != 'practitioner') {
            
            var datacheck: any = {
              formid: this.activeForms.id,
              loggedinuser: this.userData.userId,
              patientid:  this.selectedAssosiatePatient,
              enableCollaborateEdit: this.userDataConfig.enable_collaborate_edit,
              admissionId: this._structureService.isMultiAdmissionsEnabled && !isBlank(this.selectedAdmission) ? this.selectedAdmission.id : undefined
            };
            $('#loadallowconfirm').show();
            this._formsService.checkAllowEditEnabled(datacheck).then((result: any) => {
             $('#loadallowconfirm').hide();
             if (result.ALLOWEDIT == true && result.FROMID == this.userData.userId) {
                var activityData = {
                  activityName: "Patient already has allow edited data with selected form.",
                  activityType: "structured forms",
                  activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected the form and " + this.selectedAssosiatePatientName.trim() + "- this patient already associated with the form " + this.activeForms.name + " (" + this.activeForms.id + ") and moved to pending."
                };
                this._structureService.trackActivity(activityData);
                if (!this.brightreeFormLandingFlow) {
                  //TODO: Need to revamp all swal popups [CHP-6901].
                var msgtxt = "There is already form " + this.activeForms.name + " for patient " + this.selectedAssosiatePatientName + " in pending category, do you want to continue with new form or review the form under pending category first?";
                swal({
                  title: "Are you sure?",
                  text: msgtxt,
                  type: "warning",
                  showCancelButton: true,
                  cancelButtonClass: "btn-default",
                  confirmButtonClass: "btn-warning",
                  cancelButtonText: "Continue",
                  confirmButtonText: "Go To Pending",
                  closeOnConfirm: true
                }, (confirm) => {
                  if (confirm) {
                    var activityData = {
                      activityName: "Patient already has allow edited data with selected form-navigated to pending",
                      activityType: "structured forms",
                      activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected the form and " + this.selectedAssosiatePatientName.trim() + "- this patient already associated with the form " + this.activeForms.name + " (" + this.activeForms.id + ") and navigated to pending."
                    };
                    this._structureService.trackActivity(activityData);
                    this._structureService.setCookie('tabname','PENDING', 1);
                    this.emitSubmitFormToPAH()
                    this.router.navigate(['/forms/list']);
                    this._sharedService.myFormsType = "PENDING";
  
                  } else {
                    var activityData = {
                      activityName: "Patient already has allow edited data with selected form-continue with the form",
                      activityType: "structured forms",
                      activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + this.selectedAssosiatePatientName + "- this patient already have form in pending - and continue with the form " + this.activeForms.name + " (" + this.activeForms.id + ") "
                    };
                    this._structureService.trackActivity(activityData);
                    if (this.activeForms.externalFileExchange != true) {
                      this._formsService.checkDraftForm(datacheck).then((result: any) => {
                        console.log(result.nodrafts);
                        if (result.nodrafts == false) {
                          var activityData = {
                            activityName: "Patient already have drafts for the selected form",
                            activityType: "structured forms",
                            activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + this.selectedAssosiatePatientName.trim() + "- this patient already have drafts for the form-" + this.activeForms.name + this.activeForms.name + " (" + this.activeForms.id + ") "
                          };
                          this._structureService.trackActivity(activityData);
                          if (this.userDataConfig.enable_collaborate_edit == 1) {
                            var worklistname = " in All Form Worklist";
                          } else {
                            var worklistname = " in My Form Worklist";
                          }
                          var msgtxt = "You already have drafts for the Patient " + this.selectedAssosiatePatientName + worklistname;
                          swal({
                            title: "Are you sure?",
                            text: msgtxt,
                            type: "warning",
                            showCancelButton: true,
                            cancelButtonClass: "btn-default",
                            confirmButtonClass: "btn-warning",
                            cancelButtonText: "Continue",
                            confirmButtonText: "Go To Drafts",
                            closeOnConfirm: true
                          }, (confirm) => {
                            if (confirm) {
                              var activityData = {
                                activityName: "Patient already have drafts for the selected form and navigated to draft bucket",
                                activityType: "structured forms",
                                activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + this.selectedAssosiatePatientName + "- this patient already have drafts for the form- and navigated to draft bucket " + this.activeForms.name + " (" + this.activeForms.id + ") "
                              };
                              this._structureService.trackActivity(activityData);
                              this._structureService.setCookie('tabname','DRAFTS', 1);
                              this.emitSubmitFormToPAH()
                              if (this.userDataConfig.enable_collaborate_edit == 1) {
                                this.router.navigate(['/forms/list']);
                              } else {
                                this.router.navigate(['/forms/worklist']);
                              }
                              //this.router.navigate(['/forms/worklist']);
                              this._sharedService.myFormsType = "DRAFTS";
                            }
                            else {
                              if (this.userDataConfig.enable_collaborate_edit == 1) {
                                var enable_collaborate_edit = "collaborateDraft-Enabled";
                              } else {
                                var enable_collaborate_edit = "";
                              }
                              var activityData = {
                                activityName: "Patient already have drafts for the selected form and continue with the form" + enable_collaborate_edit,
                                activityType: "structured forms",
                                activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + this.selectedAssosiatePatientName + "- this patient already have drafts for the form - and continue with the form " + this.activeForms.name + " (" + this.activeForms.id + ") "
                              };
                              this._structureService.trackActivity(activityData);
                            }
  
                          })
                        }
                        
                        this.selectedAssosiatePatient = this.selectedAssosiatePatientDrivenId;
                        this.selectedAssosiatePatientName = this.selectedAssosiatePatientDisplayName;
                        this.clearForm = 0;
                        var recipients = this.selectedRecipients;  
  
                        if (recipients) {
                          this.stafffillfirst = true;
                        } else {
                          this.stafffillfirst = false;
                        }
                        this.setIframeUrl();
                        var activityData = {
                          activityName: "Select Associate Patient",
                          activityType: "structured forms",
                          activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Associate Patient -444" + this.selectedAssosiatePatientName + "(" + this.selectedAssosiatePatient + ") Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
                        };
                        this._structureService.trackActivity(activityData);
                      })
                    }
                  }
                })
                } else {
                  this.selectedAssosiatePatient = this.selectedAssosiatePatientDrivenId;
                  this.selectedAssosiatePatientName = this.selectedAssosiatePatientDisplayName;
                  this.clearForm = 0;
                  if (this.selectedRecipients) {
                    this.stafffillfirst = true;
                  } else {
                    this.stafffillfirst = false;
                  }
                  this.setIframeUrl();
                }
              }
              else {
                this.selectedAssosiatePatient = this.selectedAssosiatePatientDrivenId;
                this.selectedAssosiatePatientName = this.selectedAssosiatePatientDisplayName;
                if (this.activeForms.externalFileExchange != true) {
                  this._formsService.checkDraftForm(datacheck).then((result: any) => {
                    this.loadForm = false;
                    if (result.nodrafts == false) {
                      var activityData = {
                        activityName: "Patient already have drafts for the selected form",
                        activityType: "structured forms",
                        activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + this.selectedAssosiatePatientName.trim() + "- this patient already have drafts for the form-" + this.activeForms.name + this.activeForms.name + " (" + this.activeForms.id + ") "
                      };
                      this._structureService.trackActivity(activityData);
                      if (this.userDataConfig.enable_collaborate_edit == 1) {
                        var worklistname = " in All Form Worklist";
                      } else {
                        var worklistname = " in My Form Worklist";
                      }
                      if (!this.brightreeFormLandingFlow) {
                        //TODO: Need to revamp all swal popups [CHP-6901].
                      var msgtxt = "You already have drafts for the Patient " + this.selectedAssosiatePatientName + worklistname;
                      swal({
                        title: "Are you sure?",
                        text: msgtxt,
                        type: "warning",
                        showCancelButton: true,
                        cancelButtonClass: "btn-default",
                        confirmButtonClass: "btn-warning",
                        cancelButtonText: "Continue",
                        confirmButtonText: "Go To Drafts",
                        closeOnConfirm: true
                      }, (confirm) => {
                        if (confirm) {
                          this.loadForm = false;
                          var activityData = {
                            activityName: "Patient already have drafts for the selected form and navigated to draft bucket",
                            activityType: "structured forms",
                            activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + this.selectedAssosiatePatientName + "- this patient already have drafts for the form- and navigated to draft bucket " + this.activeForms.name + " (" + this.activeForms.id + ") "
                          };
                          this._structureService.trackActivity(activityData);
                          this._structureService.setCookie('tabname','DRAFTS', 1);
                          this.emitSubmitFormToPAH()
                          //this.router.navigate(['/forms/worklist']);
                          if (this.userDataConfig.enable_collaborate_edit == 1) {
                            this.router.navigate(['/forms/list']);
                          } else {
                            this.router.navigate(['/forms/worklist']);
                          }
                          this._sharedService.myFormsType = "DRAFTS";
                        }
                        else {
                          this.loadForm = true;
                          if (this.userDataConfig.enable_collaborate_edit == 1) {
                            var enable_collaborate_edit = "collaborateDraft-Enabled";
                          } else {
                            var enable_collaborate_edit = "";
                          }
                          var activityData = {
                            activityName: "Patient already have drafts for the selected form and continue with the form" + enable_collaborate_edit,
                            activityType: "structured forms",
                            activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + this.selectedAssosiatePatientName + "- this patient already have drafts for the form - and continue with the form " + this.activeForms.name + " (" + this.activeForms.id + ") "
                          };
                          this._structureService.trackActivity(activityData);
                          if(this.loadForm){
                            this.setIframeUrl();
                          }
                        }
                      })
                      } else {
                        this.loadForm = true;
                        if(this.loadForm){
                          this.setIframeUrl();
                        } 
                      }
                    } else {
                      this.loadForm = true;
                    }
                    
                    this.selectedAssosiatePatient = this.selectedAssosiatePatientDrivenId;
                    console.log(this.selectedAssosiatePatient);
                    this.selectedAssosiatePatientName = this.selectedAssosiatePatientDisplayName;
                    this.clearForm = 0;
                    var recipients = this.selectedRecipients;
                    if (recipients) {
                      this.stafffillfirst = true;
                    } else {
                      this.stafffillfirst = false;
                    }
                    if(this.loadForm){
                      this.setIframeUrl();
                    }
                    var activityData = {
                      activityName: "Select Associate Patient",
                      activityType: "structured forms",
                      activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Associate Patient -555" + this.selectedAssosiatePatientName + "(" + this.selectedAssosiatePatient + ") Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
                    };
                    this._structureService.trackActivity(activityData);
  
  
                  })
                }
              }
  
            })
          } 
  
        } else {
         
          this.selectedAssosiatePatient = this.selectedAssosiatePatientDrivenId;
         
          this.selectedAssosiatePatientName = this.selectedAssosiatePatientDisplayName;
          this.clearForm = 0;
          var recipients = this.selectedRecipients;
          
          if (recipients) {
            this.stafffillfirst = true;
          } else {
            this.stafffillfirst = false;
          }
          var formData = {
            "formId": this.activeForms.id,
            "patientUser": this.selectedAssosiatePatient,
            "facing": this.activeForms.stafFacing
          }
          this._formsService.checkOrderchange(formData).then((data) => {
            console.log(data);
            var orderChangeResult: any = data;
            this.orderChangeStatus = orderChangeResult.orderChange;
            console.log(this.orderChangeStatus);
  
          })
          this.setIframeUrl();
          var activityData = {
            activityName: "Select Associate Patient",
            activityType: "structured forms",
            activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Associate Patient -111" + this.selectedAssosiatePatientName + "(" + this.selectedAssosiatePatient + ") Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
          };
          this._structureService.trackActivity(activityData);
        } 

      
    }
    this.checkForChangesInRecipientsSelection();
  }
//jithin

  goToForm(form, fromReset: boolean = false) {
    this.resetAdmissionHandle();
    if (!this.externalUserId) {
      this.clearAssociatePatientSelection();
    }
    this.showRecipientsForSendCompletedForm = !isBlank(form.sendCompletedForm) ? form.sendCompletedForm : false; 
    this.formSendInProgress = false;
    $("#message").val("");
    this.emitEventToSelectSitesId('displaySelectedSitesOnly');
    this.patientAssociationOffDiv=true;
    this.autosavecalledformid = localStorage.getItem('autosavecalledformid');
    this.autosavecalled = localStorage.getItem('autosavecalled');
    this.faxQIntegration=(form.FaxQIntegration)?true:false;
    this.staffFacing=form.stafFacing;
     $("#selected_form").val(form.id);
     $(".saveAsDraft-message").html("");
     $("#last_save_draft").val("");
     $("#error_save_draft").val("");
    if(form.stafFacing == "practitioner" && !(this.selectedAssociatedPatientSiteId) && form.patientAssociation =="true"){
        this.recipientLoading = true;
    }
    if((this.faxQIntegration) && (form.patientAssociation =="false") && (this.staffFacing=='true'|| this.staffFacing=='practitioner')){
      this.patientAssociationOffDiv=false;
      if( this.userData.mySites.length == 1){
      this.getSiteId(this.userData.mySites);
      }
    }
    this.isPatientFacing = (this.staffFacing == 'false'|| this.staffFacing=='practitioner');
    if((this.staffFacing.toString() === 'false' && this.staffFacing !== 'practitioner') || form.patientAssociation.toString() === 'true') {
      this.filterSiteLabel = this.staticDataService.getSiteLabelBasedOnMultiAdmission('FILTER_BY_SITES');
    } else {
      this.filterSiteLabel = 'LABELS.FILTER_BY_SITES';
    }
    if (this.autosavecalled == "true") {
      var msgtxt = "You want to close this Form or continue with the form?"
      swal({
        title: "Are you sure?",
        text: msgtxt,
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        cancelButtonText: "Close",
        confirmButtonText: "Continue",
        closeOnConfirm: true
      }, (confirm) => {
        if (confirm) {

        }
        else {
          var activityData = {
            activityName: "Switched the form before saved as draft ",
            activityType: "structured forms",
            activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + this.selectedAssosiatePatient + "- Switched the form before saved as draft - " + this.activeForms.name + " (" + this.activeForms.id + ") "
          };
          this._structureService.trackActivity(activityData);
          localStorage.setItem('autosavecalledformid', "0");
          localStorage.setItem('autosavecalled', "false");
          this.formSendIberfore = true;
          this.selectedForm = form;
          //this.getAssociatePatients(this.selectedForm);
          this.showClearForm = false;
          this.activeForms = form;
          this.savaAsDraftStaff = 0;
          this.savaAsDraftPatient = 0;
          if (this.userDataConfig.enable_form_save_draft_patient == 1 && this.activeForms.enableSaveDraftPatient == 1) {
            this.savaAsDraftPatient = 1;
          }
          if (this.userDataConfig.enable_form_save_draft_staff == 1 && this.activeForms.enableSaveDraftStaff == 1) {
            this.savaAsDraftStaff = 1;
          }
          this.oneTimeCopy = 0;

          var self = this;
          $.fn.select2.amd.require(
            ['select2/data/array', 'select2/utils'],
            function (ArrayData, Utils) {
              console.log('select2 protoype starting');
              interface CustomDataFN {
                (this: any, $element: any, options: any): any;
                __super__: any;
              }

              var CustomData: CustomDataFN = <CustomDataFN>function ($element, options): any {
                CustomData.__super__.constructor.call(this, $element, options);
              }

              function contains(str1, str2) {
                return new RegExp(str2, "i").test(str1);
              }

              Utils.Extend(CustomData, ArrayData);

              CustomData.prototype.query = function (params, callback) {
                if (!("page" in params)) {
                  params.page = 1;
                }
                var pageSize = 20;
                console.log("processing select2 pagination");
                var results = this.$element.children().map(function (i, elem) {

                  if (contains(elem.innerText, params.term)) {
                    return {
                      id: elem.value,
                      text: elem.innerText
                    };
                  }
                });

                callback({
                  results: results.slice((params.page - 1) * pageSize, params.page * pageSize),
                  pagination: {
                    more: results.length >= params.page * pageSize
                  }
                });
              };
              var selectPlaceholder = "Select Associated Patient";
              if (self.associatePatientLoading) {
                selectPlaceholder = "Loading Associated Patient...";
              }
              $("#assosiatedPatients").select2({
                ajax: {},
                allowClear: true,
                width: "element",
                placeholder: selectPlaceholder,
                dataAdapter: CustomData
              });
              if (self.associatePatientLoading) {
                $("#select2-assosiatedPatients-container .select2-selection__placeholder").html("").html("Loading Associated Patient <img src='./assets/img/loader/color.gif' style='width: 31px;'>");
              }
            });
          if (this.associatePatientLoading) {
            $("#assosiatedPatients").attr("disabled", true);
          }
          this.structuredFormSend.patchValue({
            assosiatedPatients: this.assosiatedPatients
          });

          // this.selected = form;
          this.selectedAssosiatePatient = '';
          this.selectedAssosiatePatientName = "";
          this.selectedAssociatedPatientSiteId = null;
          this.closeSelectedAssociatePatient();

          this.allowRecipientRoles = (this.activeForms.stafFacing == 'false' || this.activeForms.stafFacing == 'practitioner') ? true : false;
          if (this.activeForms.staffFill == "true") {
            this.staffFill = true;
            this.stafffillfirst = false;
          } else {
            this.staffFill = false;
          }
         
          this.patientAssociation = this.activeForms.patientAssociation == 'false' ? false : true;
         
          if (!fromReset) {
            //this.setRecipient();
            this.resetRecipient();
          }

          if (this.staffFill) {

            var recipients = this.selectedRecipients;//$('#tenantUsers').val();
          } else {
            this.clearForm = 0;
            this.setIframeUrl();
          }
          if (!fromReset) {
            var activityData = {
              activityName: "Form Switching",
              activityType: "structured forms",
              activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
            };
            //this._structureService.trackActivity(activityData);
          }
          var activityData = {
            activityName: "Form Switching",
            activityType: "structured forms",
            activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
          };

          this._structureService.trackActivity(activityData);
        }
      })
    } else {
        if (form.stafFacing == "practitioner" && form.patientAssociation =="true") {
            this.recipientLoading = true;
        }else{
            this.recipientLoading = false;
        }
      var datacheck: any = {
        formid: form.id,
        loggedinuser: this.userData.userId,
        patientid: '0',
        enableCollaborateEdit: this.userDataConfig.enable_collaborate_edit,
        admissionId: this._structureService.isMultiAdmissionsEnabled && !isBlank(this.selectedAdmission) ? this.selectedAdmission.id : undefined
      };
      // alert(form.stafFacing)
      // alert(form.patientAssociation)
      if (form.patientAssociation == 'false' && form.stafFacing == 'true' && form.stafFacing != 'practitioner') {
        //   alert("outside")

        this._formsService.checkAllowEditEnabled(datacheck).then((result: any) => {

          if (result.ALLOWEDIT == true && result.FROMID == this.userData.userId) {
            var activityData = {
              activityName: "Form already allow edited and exists in pending.",
              activityType: "structured forms",
              activityDescription: "Form " + form.name + " selected by user " + this.userData.displayName + " already exists in pending."
            };
            this._structureService.trackActivity(activityData);
            if (!this.brightreeFormLandingFlow) {
              //TODO: Need to revamp all swal popups [CHP-6901].
            var msgtxt = "There is already form " + form.name + " in pending category, do you want to continue with new form or review the form under pending category first?";
            swal({
              title: "Are you sure?",
              text: msgtxt,
              type: "warning",
              showCancelButton: true,
              cancelButtonClass: "btn-default",
              confirmButtonClass: "btn-warning",
              cancelButtonText: "Continue",
              confirmButtonText: "Go To Pending",
              closeOnConfirm: true
            }, (confirm) => {
              if (confirm) {
                var activityData = {
                  activityName: "Form already exists in pending and navigated to pending.",
                  activityType: "structured forms",
                  activityDescription: "Form " + form.name + "(" + form.id + ") created by user " + this.userData.displayName + " already exists in pending-navigated to pending."
                };
                this._structureService.trackActivity(activityData);
                this._structureService.setCookie('tabname','PENDING', 1);
                this.emitSubmitFormToPAH()
                this.router.navigate(['/forms/list']);
                this._sharedService.myFormsType = "PENDING";
              } else {
                var activityData = {
                  activityName: "Form already allow edited, exists in pending and continue with the form.",
                  activityType: "structured forms",
                  activityDescription: "Form " + form.name + "(" + form.id + ") selected by user " + this.userData.displayName + " already allow edited and exists in pending-continue with new form."
                };
                this._structureService.trackActivity(activityData);
                if (this.activeForms.externalFileExchange != true) {
                  this._formsService.checkDraftForm(datacheck).then((result: any) => {
                    console.log(result.nodrafts);
                    if (result.nodrafts == false) {
                      var activityData = {
                        activityName: "Patient already have drafts for the selected form",
                        activityType: "structured forms",
                        activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + "- this patient already have drafts for the form-" + this.activeForms.name + this.activeForms.name + " (" + this.activeForms.id + ") "
                      };
                      this._structureService.trackActivity(activityData);

                      var msgtxt = "You already have drafts for the Patient ";
                      swal({
                        title: "Are you sure?",
                        text: msgtxt,
                        type: "warning",
                        showCancelButton: true,
                        cancelButtonClass: "btn-default",
                        confirmButtonClass: "btn-warning",
                        cancelButtonText: "Continue",
                        confirmButtonText: "Go To Drafts",
                        closeOnConfirm: true
                      }, (confirm) => {
                        if (confirm) {
                          var activityData = {
                            activityName: "Patient already have drafts for the selected form and navigated to draft bucket",
                            activityType: "structured forms",
                            activityDescription: this.userData.displayName + "(" + this.userData.userId + ")- this patient already have drafts for the form- and navigated to draft bucket " + this.activeForms.name + " (" + this.activeForms.id + ") "
                          };
                          this._structureService.trackActivity(activityData);
                          this._structureService.setCookie('tabname','DRAFTS', 1);
                          this.emitSubmitFormToPAH()
                          if (this.userDataConfig.enable_collaborate_edit == 1) {
                            this.router.navigate(['/forms/list']);
                          } else {
                            this.router.navigate(['/forms/worklist']);
                          }
                          //this.router.navigate(['/forms/worklist']);
                          this._sharedService.myFormsType = "DRAFTS";
                        }
                        else {
                          if (this.userDataConfig.enable_collaborate_edit == 1) {
                            var enable_collaborate_edit = "collaborateDraft-Enabled";
                          } else {
                            var enable_collaborate_edit = "";
                          }
                          var activityData = {
                            activityName: "Patient already have drafts for the selected form and continue with the form" + enable_collaborate_edit,
                            activityType: "structured forms",
                            activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + "- this patient already have drafts for the form - and continue with the form " + this.activeForms.name + " (" + this.activeForms.id + ") "
                          };
                          this._structureService.trackActivity(activityData);
                        }

                      })
                    }

                  })
                }
                this.initializeAllowEditEnableStaffFacingForm(form, fromReset);
                
              }
            })
            } else {
              this.initializeAllowEditEnableStaffFacingForm(form, fromReset);
            }
          } else {
            if (this.activeForms.externalFileExchange != true) {
              this._formsService.checkDraftForm(datacheck).then((result: any) => {
                this.loadForm = false;
                console.log(result.nodrafts);
                if (result.nodrafts == false) {
                  var activityData = {
                    activityName: "Patient already have drafts for the selected form",
                    activityType: "structured forms",
                    activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + "- this patient already have drafts for the form-" + this.activeForms.name + this.activeForms.name + " (" + this.activeForms.id + ") "
                  };
                  this._structureService.trackActivity(activityData);
                  if (!this.brightreeFormLandingFlow) {
                    //TODO: Need to revamp all swal popups [CHP-6901].
                  var msgtxt = "You already have drafts for the Patient ";
                  swal({
                    title: "Are you sure?",
                    text: msgtxt,
                    type: "warning",
                    showCancelButton: true,
                    cancelButtonClass: "btn-default",
                    confirmButtonClass: "btn-warning",
                    cancelButtonText: "Continue",
                    confirmButtonText: "Go To Drafts",
                    closeOnConfirm: true
                  }, (confirm) => {
                    if (confirm) {
                      var activityData = {
                        activityName: "Patient already have drafts for the selected form and navigated to draft bucket",
                        activityType: "structured forms",
                        activityDescription: this.userData.displayName + "(" + this.userData.userId + ")- this patient already have drafts for the form- and navigated to draft bucket " + this.activeForms.name + " (" + this.activeForms.id + ") "
                      };
                      this._structureService.trackActivity(activityData);
                      this._structureService.setCookie('tabname','DRAFTS', 1);
                      this.emitSubmitFormToPAH()
                      if (this.userDataConfig.enable_collaborate_edit == 1) {
                        this.router.navigate(['/forms/list']);
                      } else {
                        this.router.navigate(['/forms/worklist']);
                      }
                      //this.router.navigate(['/forms/worklist']);
                      this._sharedService.myFormsType = "DRAFTS";
                    } else {
                      this.loadForm = true
                      if (this.userDataConfig.enable_collaborate_edit == 1) {
                        var enable_collaborate_edit = "collaborateDraft-Enabled";
                      } else {
                        var enable_collaborate_edit = "";
                      }
                      var activityData = {
                        activityName: "Patient already have drafts for the selected form and continue with the form" + enable_collaborate_edit,
                        activityType: "structured forms",
                        activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + "- this patient already have drafts for the form - and continue with the form " + this.activeForms.name + " (" + this.activeForms.id + ") "
                      };
                      this._structureService.trackActivity(activityData);
                    }

                  })
                  } else {
                    this.loadForm = true;
                  }
                } else {
                  this.loadForm = true;
                }

              })
            }
            this.formSendIberfore = true;
            this.selectedForm = form;
            //this.getAssociatePatients(this.selectedForm);
            this.showClearForm = false;
            this.activeForms = form;
            this.savaAsDraftStaff = 0;
            this.savaAsDraftPatient = 0;
            if (this.userDataConfig.enable_form_save_draft_patient == 1 && this.activeForms.enableSaveDraftPatient == 1) {
              this.savaAsDraftPatient = 1;
            }
            if (this.userDataConfig.enable_form_save_draft_staff == 1 && this.activeForms.enableSaveDraftStaff == 1 && this.receipientCountSaveDraft == 1) {
              this.savaAsDraftStaff = 1;
            }
            this.oneTimeCopy = 0;
            console.log("-----------------his.activeForms.staffFill");
            console.log(this.activeForms.staffFill);
            console.log(this.activeForms.stafFacing);
            console.log("-------------------------his.activeForms.staffFill");


            // $('#assosiatedPatients').select2({
            //   placeholder: "Select Associated Patient",
            //   allowClear: true
            // });

            var self = this;
            $.fn.select2.amd.require(
              ['select2/data/array', 'select2/utils'],
              function (ArrayData, Utils) {
                console.log('select2 protoype starting');
                interface CustomDataFN {
                  (this: any, $element: any, options: any): any;
                  __super__: any;
                }

                var CustomData: CustomDataFN = <CustomDataFN>function ($element, options): any {
                  CustomData.__super__.constructor.call(this, $element, options);
                }

                function contains(str1, str2) {
                  return new RegExp(str2, "i").test(str1);
                }

                Utils.Extend(CustomData, ArrayData);

                CustomData.prototype.query = function (params, callback) {
                  if (!("page" in params)) {
                    params.page = 1;
                  }
                  var pageSize = 20;
                  console.log("processing select2 pagination");
                  var results = this.$element.children().map(function (i, elem) {

                    if (contains(elem.innerText, params.term)) {
                      return {
                        id: elem.value,
                        text: elem.innerText
                      };
                    }
                  });

                  callback({
                    results: results.slice((params.page - 1) * pageSize, params.page * pageSize),
                    pagination: {
                      more: results.length >= params.page * pageSize
                    }
                  });
                };
                var selectPlaceholder = "Select Associated Patient";
                if (self.associatePatientLoading) {
                  selectPlaceholder = "Loading Associated Patient...";
                }
                $("#assosiatedPatients").select2({
                  ajax: {},
                  allowClear: true,
                  width: "element",
                  placeholder: selectPlaceholder,
                  dataAdapter: CustomData
                });
                if (self.associatePatientLoading) {
                  $("#select2-assosiatedPatients-container .select2-selection__placeholder").html("").html("Loading Associated Patient <img src='./assets/img/loader/color.gif' style='width: 31px;'>");
                }
              });
            if (this.associatePatientLoading) {
              $("#assosiatedPatients").attr("disabled", true);
            }
            this.structuredFormSend.patchValue({
              assosiatedPatients: this.assosiatedPatients
            });

            // this.selected = form;
            this.selectedAssosiatePatient = '';
            this.selectedAssosiatePatientName = "";
            this.closeSelectedAssociatePatient();

            this.allowRecipientRoles = (this.activeForms.stafFacing == 'false' || this.activeForms.stafFacing == 'practitioner') ? true : false;
            if (this.activeForms.staffFill == "true") {
              this.staffFill = true;
              this.stafffillfirst = false;
            } else {
              this.staffFill = false;
            }
            
            this.patientAssociation = this.activeForms.patientAssociation == 'false' ? false : true;
           
            if (!fromReset) {
              //this.setRecipient();
              this.resetRecipient();
            }

            if (this.staffFill) {

              var recipients = this.selectedRecipients;//$('#tenantUsers').val();
            
            } else {
              this.clearForm = 0;
              this.setIframeUrl();
            }
            if (!fromReset) {
              var activityData = {
                activityName: "Form Switching",
                activityType: "structured forms",
                activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
              };
             // this._structureService.trackActivity(activityData);
            }
            var activityData = {
              activityName: "Form Switching",
              activityType: "structured forms",
              activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
            };

            this._structureService.trackActivity(activityData);


          }
        });

      }
      else {
        this.formSendIberfore = true;
        this.selectedForm = form;
        //this.getAssociatePatients(this.selectedForm);
        this.showClearForm = false;
        this.activeForms = form;
        this.savaAsDraftStaff = 0;
        this.savaAsDraftPatient = 0;
        this.selectedRecipients=[];
        this.selectedRecipientNames=[];
        this.selectedRecipientsArray=[];
        if (this.userDataConfig.enable_form_save_draft_patient == 1 && this.activeForms.enableSaveDraftPatient == 1) {
          this.savaAsDraftPatient = 1;
        }
        if (this.userDataConfig.enable_form_save_draft_staff == 1 && this.activeForms.enableSaveDraftStaff == 1 && this.receipientCountSaveDraft == 1) {
          this.savaAsDraftStaff = 1;
        }
        this.oneTimeCopy = 0;
        
        var self = this;
        $.fn.select2.amd.require(
          ['select2/data/array', 'select2/utils'],
          function (ArrayData, Utils) {
            console.log('select2 protoype starting');
            interface CustomDataFN {
              (this: any, $element: any, options: any): any;
              __super__: any;
            }

            var CustomData: CustomDataFN = <CustomDataFN>function ($element, options): any {
              CustomData.__super__.constructor.call(this, $element, options);
            }

            function contains(str1, str2) {
              return new RegExp(str2, "i").test(str1);
            }

            Utils.Extend(CustomData, ArrayData);

            CustomData.prototype.query = function (params, callback) {
              if (!("page" in params)) {
                params.page = 1;
              }
              var pageSize = 20;
              console.log("processing select2 pagination");
              var results = this.$element.children().map(function (i, elem) {

                if (contains(elem.innerText, params.term)) {
                  return {
                    id: elem.value,
                    text: elem.innerText
                  };
                }
              });

              callback({
                results: results.slice((params.page - 1) * pageSize, params.page * pageSize),
                pagination: {
                  more: results.length >= params.page * pageSize
                }
              });
            };
            var selectPlaceholder = "Select Associated Patient";
            if (self.associatePatientLoading) {
              selectPlaceholder = "Loading Associated Patient...";
            }
            $("#assosiatedPatients").select2({
              ajax: {},
              allowClear: true,
              width: "element",
              placeholder: selectPlaceholder,
              dataAdapter: CustomData
            });
            if (self.associatePatientLoading) {
              $("#select2-assosiatedPatients-container .select2-selection__placeholder").html("").html("Loading Associated Patient <img src='./assets/img/loader/color.gif' style='width: 31px;'>");
            }
          });
        if (this.associatePatientLoading) {
          $("#assosiatedPatients").attr("disabled", true);
        }
        this.structuredFormSend.patchValue({
          assosiatedPatients: this.assosiatedPatients
        });

        // this.selected = form;
        if (!this.externalUserId) {
        this.selectedAssosiatePatient = '';
        this.selectedAssosiatePatientName = "";
        this.closeSelectedAssociatePatient();
        }

        this.allowRecipientRoles = (this.activeForms.stafFacing == 'false' || this.activeForms.stafFacing == 'practitioner') ? true : false;
        if (this.activeForms.staffFill == "true") {
          this.staffFill = true;
          this.stafffillfirst = false;
        } else {
          this.staffFill = false;
        }
        this.patientAssociation = this.activeForms.patientAssociation == 'false' ? false : true;
       
        if (!fromReset) {
          //this.setRecipient();
          this.resetRecipient();
        }

        if (this.staffFill) {

          var recipients = this.selectedRecipients;//$('#tenantUsers').val();
        } else {
          this.clearForm = 0;
          this.setIframeUrl();
        }
        if (!fromReset) {
          var activityData = {
            activityName: "Form Switching",
            activityType: "structured forms",
            activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
          };
         // this._structureService.trackActivity(activityData);
        }
        var activityData = {
          activityName: "Form Switching",
          activityType: "structured forms",
          activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
        };

        this._structureService.trackActivity(activityData);


      }
    }
    // If loaded from 'pah' with patientAssociationOffDiv, set recipient and get associated patients.
    if (this.isLoadFromPAH && this.patientAssociationOffDiv) {
      if (this.patientAssociation) {
        this.getAssociatePatients(this.activeForms, this.getPatientSearchCriteria(this.autoPopulatePatientInfo));
      } else {
        this.setRecipient('', this.autoPopulatePatientInfo. id);
      }
    }
    if (isPresent(this.externalUserId)) {
      if (this.patientAssociation) {
        this.getAssociatePatients(this.activeForms, '');
      } else {
        this.setRecipient('', '');
      }
    }
    this.checkForChangesInRecipientsSelection();
  }
  initializeAllowEditEnableStaffFacingForm(form: any, fromReset: boolean = false) {
    this.formSendIberfore = true;
    this.selectedForm = form;
    this.showClearForm = false;
    this.activeForms = form;
    this.savaAsDraftStaff = 0;
    this.savaAsDraftPatient = 0;
    if (this.userDataConfig.enable_form_save_draft_patient == 1 && this.activeForms.enableSaveDraftPatient == 1) {
      this.savaAsDraftPatient = 1;
    }
    if (this.userDataConfig.enable_form_save_draft_staff == 1 && this.activeForms.enableSaveDraftStaff == 1 && this.receipientCountSaveDraft == 1) {
      this.savaAsDraftStaff = 1;
    }
    this.oneTimeCopy = 0;

    const self = this;
    $.fn.select2.amd.require(
      ['select2/data/array', 'select2/utils'],
      function (ArrayData, Utils) {
        interface CustomDataFN {
          (this: any, $element: any, options: any): any;
          __super__: any;
        }

        let CustomData: CustomDataFN = <CustomDataFN>function ($element, options): any {
          CustomData.__super__.constructor.call(this, $element, options);
        }

        function contains(str1, str2) {
          return new RegExp(str2, "i").test(str1);
        }

        Utils.Extend(CustomData, ArrayData);

        CustomData.prototype.query = function (params, callback) {
          if (!('page' in params)) {
            params.page = 1;
          }
          let pageSize = 20;
          let results = this.$element.children().map(function (i, elem) {

            if (contains(elem.innerText, params.term)) {
              return {
                id: elem.value,
                text: elem.innerText
              };
            }
          });

          callback({
            results: results.slice((params.page - 1) * pageSize, params.page * pageSize),
            pagination: {
              more: results.length >= params.page * pageSize
            }
          });
        };
        let selectPlaceholder = this._ToolTipService.getTranslateData('MESSAGES.SELECT_ASSOCIATED_PATIENT');
        if (self.associatePatientLoading) {
          selectPlaceholder = `${this._ToolTipService.getTranslateData('MESSAGES.LOADING_ASSOCIATED_PATIENT')}...`;
        }
        $('#assosiatedPatients').select2({
          ajax: {},
          allowClear: true,
          width: 'element',
          placeholder: selectPlaceholder,
          dataAdapter: CustomData
        });
        if (self.associatePatientLoading) {
          $('#select2-assosiatedPatients-container .select2-selection__placeholder').html("").html(`${this._ToolTipService.getTranslateData('MESSAGES.LOADING_ASSOCIATED_PATIENT')} <img src='./assets/img/loader/color.gif' style='width: 31px;'>`);
        }
      });
    if (this.associatePatientLoading) {
      $('#assosiatedPatients').attr('disabled', true);
    }
    this.structuredFormSend.patchValue({
      assosiatedPatients: this.assosiatedPatients
    });

    this.selectedAssosiatePatient = '';
    this.selectedAssosiatePatientName = '';
    this.closeSelectedAssociatePatient();

    this.allowRecipientRoles = (this.activeForms.stafFacing == 'false' || this.activeForms.stafFacing == 'practitioner') ? true : false;
    if (this.activeForms.staffFill == 'true') {
      this.staffFill = true;
      this.stafffillfirst = false;
    } else {
      this.staffFill = false;
    }
    
    this.patientAssociation = this.activeForms.patientAssociation == 'false' ? false : true;
    
    if (!fromReset) {
      this.resetRecipient();
    }

    if (!this.staffFill) {
      this.clearForm = 0;
      this.setIframeUrl();
    }

    const activityData = {
      activityName: 'Form Switching',
      activityType: 'structured forms',
      activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
    };

    this._structureService.trackActivity(activityData);
  }
  clearFormSelected() {
    if (this.isLoadFromPAH) {
      this.activeForms = '';
      this.selectedForm = null;
    }
  }

  checkForChangesInRecipientsSelection() {
    this.isNonContactableUsersExists = false;
    if (this.observer) {
      this.observer.disconnect();
    }
    if (this.allowRecipientRoles) {
      const targetNode = document.querySelector('#recipient-ul');
      if (targetNode) {
        this.observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
              const displayStyle = window.getComputedStyle(targetNode).display;
              if (displayStyle === 'none') {
                this.isNonContactableUsersExistsForGivenUserTags();
              }
            }
          });
        });

        const config = { attributes: true, attributeFilter: ['style'] };
        this.observer.observe(targetNode, config);
      }
    }
  }

  private areSetsEqual(setA: Set<string>, setB: Set<string>): boolean {
    if (setA.size !== setB.size) return false;
    const arrayA = Array.from(setA);
    const arrayB = Array.from(setB);
    for (let item of arrayA) {
      if (!arrayB.includes(item)) return false;
    }
    return true;
  }

  checkRecipientWithTems() {
    var textValue = $("#tagsInput").val();
    var searchText = textValue;//.replace(/[^a-z0-9\+\-\.\#]/ig,'');
    if (textValue != "") {

      var activityData = {
        activityName: "Search Form Recipients",
        activityType: "Search Form Recipients",
        activityDescription: "Search Form Recipients Search Text: " + searchText,
      };
      this._structureService.trackActivity(activityData);
      this.setRecipient(searchText);
    }
  }
  setRecipient(searchKeyword: any = "", id?: any) {
    // $('#assosiatedPatients').attr("disabled", "true");

    if (this.allowRecipientRoles && !this.recipientLoading) {
      console.log("setRecipient API called.......");
      console.log('this.activeForms', this.activeForms);
      this.recipientLoading = true;
      $("#recipient-search").text(" ").text("Loading...");
      
      var includeVirtual = false;

      var practitioner = false;

      if(this.userDataConfig.enable_appless_model == 1) {
        includeVirtual = true;
        console.log('111111111111111111111')
        if(this.activeForms.stafFacing == "practitioner") {
          console.log('222222222222222222222222')
          practitioner = true;
        }
      }
        const siteId = (this.activeForms.stafFacing == "practitioner") ? ((this.selectedAssociatedPatientSiteId) ?
            this.selectedAssociatedPatientSiteId : this.siteIds) : this.siteIds;
        const admissionId = this.patientAssociation && this.selectedAdmission ? this.selectedAdmission.id : '';
        this._formsService.getFormRecipients(this._structureService.getCookie('tenantId'), this.activeForms.tagId, this.userData.roleId, false, searchKeyword, false, includeVirtual, practitioner,siteId, this.activeForms.patientAssociation, admissionId, id).then((res: any) => {
        this.recipientLoading = false;
        this.tenantUsers = [];
        if (res.status_code === 201 && this.brightreeFormLandingFlow ) {
          this.showPageLoader = true;
          this.showLoaderMessage = this._ToolTipService.getTranslateData('MESSAGES.LOADING_PATIENT_ONBOARDING_WAIT');
          setTimeout(() => {
            this.doPatientOnBoarding('recipient', this.externalUserId, this.activeForms.tagId);
          },2000);
          return;
        }
        this.handleRecipientPatients(res, searchKeyword); 

      }).then(function () {
        console.log('after qpi call 3255');
        this.recipientLoading = false;
       
      }).catch((ex) => {
        this.recipientLoading = false;
      });

      if (this.patientAssociation) {

      }
    } else {

    }
    if(this.activeForms.stafFacing == "practitioner"){
      this.setIframeUrl();
    } 
  }

  handleRecipientPatients(res : any, searchKeyword: string) {
    const result = this.modalFilter.transform(res);
    if (result && result.length > 0) {          
      this.tenantUsers = result.filter((result) => {
        let date = "";
        if (result.caregiver_userid) {
          if (result.caregiver_dob) {
            date = result.caregiver_dob;
            const dobDay = new Date(date).getDay();
            if (date && !isNaN(dobDay)) {
              date = date.replace(/-/g, '/');
              try {
                date = moment(date).format('MM/DD/YYYY');
              } catch (e) {
                date = '';
              }
            } else {
              date = '';
            }
          } else {
            date = '';
          }
          result.caregiver_dob_formatted = date;
        } else {
          if (result.dob) {
            date = result.dob;
            let dobDay = new Date(date).getDay();
            if (date && !isNaN(dobDay)) {
              date = date.replace(/-/g, '/');
              try {
                date = moment(date).format('MM/DD/YYYY');
              }
              catch (e) {
                date = '';
              }
            } else {
              date = '';
            }
          } else {
            date = '';
          }
          result.dob_formatted = date;
        }
        result.option_text = (result.caregiver_displayname) ? ((result.dob_formatted) ? (result.displayname + ' - ' + result.dob_formatted + ' (' + result.caregiver_displayname + ')') : (result.displayname + ' (' + result.caregiver_displayname + ')')) : ((result.dob_formatted) ? (result.displayname + ' - ' + result.dob_formatted) : result.displayname);
        result.optionId = (result.tag_name ? 'tag-' + result.id : (result.caregiver_userid ? result.userId + '--' + result.caregiver_userid : result.userId));
        this.setRecipientName(result);
        this.enableOrDisableUiLI(true, false, 'R');
        $("#recipient-search").text(' ').text("Search");
        return true;
      });
      if(!this.tenantUsers.length) {
        this.enableOrDisableUiLI(true, false, 'R');
        $("#recipient-search").text(' ').text('Search');
      }
      // If loaded from 'pah', find matching recipient in tenantUsers, set them as selected, and finalize selection.
      if (this.isLoadFromPAH) {
        const selectedRecipient = this.tenantUsers.find((result) =>  result.userid === this.autoPopulatePatientInfo.id);
        if (selectedRecipient && selectedRecipient.alternateContacts.length === 0) {
          this.setSelectedRecipients(selectedRecipient, selectedRecipient.userid, null);
          this.doneSelectedRecipient();
        }
      }
      if (this.externalUserId) {
        const selectedRecipient = result[0];
        if (selectedRecipient && selectedRecipient.alternateContacts.length === 0) {
          this.setSelectedRecipients(selectedRecipient, selectedRecipient.userid, null);
          this.doneSelectedRecipient();
        }
      }
      
    } else {
      this.enableOrDisableUiLI(true, false, 'R');
      $("#recipient-search").text(' ').text('Search');
    }

    let tagGetData = `?searchKeyword=${searchKeyword}`;

    if (typeof (this.selectedForm) != 'undefined' && typeof (this.selectedForm.tagId) != 'undefined') {
      tagGetData += `&tagId=${this.selectedForm.tagId}`;
    }

    const tagTypes = ["2"]; // Message Tag =1, User Tag =2 , Document Tag =3
    if(!this.isLoadFromPAH && !this.externalUserId && this.activeForms.stafFacing != 'practitioner' && !this._structureService.isMultiAdmissionsEnabled){
    this._structureService.getTagsByGroup(tagGetData, tagTypes, this.siteIds).then((data: any) => {
      this.patientTags = data;
      this.patientTags = data.filter((tagresult) => {
        if (tagresult.tag_name !== '' && tagresult.id != '') {
          tagresult.optionId = tagresult.tag_name ? `tag-${tagresult.id}` : 0;
          tagresult.recipientOptionText = tagresult.tag_name ? `${tagresult.tag_name} [User Tag]` : '';
          return true;
        } else {
          return false;
        }
      });
      this.tenantUsers = this.patientTags.concat(this.tenantUsers);
    });
  }
  }

   /**
 * @param user Payload contains different values for different entity
 * For patient user is patient id
 * For caregiver user is caregiverid--patientid
 * For tag user is tag-tagid
 * For alternate contact - user is like contact--patientId
 */  
  setSelectedRecipients(users, user, event) { 
    // If patient not clicked and form loaded from 'pah' or target class isn't 'contact-li', execute code.
    if (!users.noContactAvailable && (this.externalUserId || this.isLoadFromPAH || $(event.target).attr('class') != 'contact-li')) {
      let selectedId = this.selectedRecipients.find((id) => id.indexOf(user) > -1);
      if (isBlank(selectedId)) {
        this.selectedRecipients.push(user);
        this.selectedRecipientNames.push({ text: users.recipientOptionText })
        this.selectedRecipientsArray.push(users);
        this.setSelectedRecipientForms(false, users);
      } else {
        this.removeSelectedRecipient(user);
      }
      if(this.selectedRecipients.length === 1) {
        this.setRecipientForSend(this.selectedRecipients[0],'recipient');
      }
      if(!isBlank(this.selectedRecipients) && this.selectedRecipients.length > 1 && this.staffFacing === 'false') {
        this.clearAssociatePatientSelection();
      }
    }
  }

  setSelectedAlternateContact(users, user, contact) {
    if (contact.noContactAvailable) return;
    var contactOption= contact.contactId +'--'+user;
    console.log('printg val'+ this.selectedRecipients.indexOf(String(contactOption)));
    if (this.selectedRecipients.indexOf(String(contactOption)) > -1) {
      return false;
    }
    var recdisplayFormat = contact.patientDisplayName + " ( " + contact.displayName;
    if (contact.relation != "") {
      recdisplayFormat += "-" + contact.relation;
    }
    if (contact.patientDobFormatted) {
      recdisplayFormat += ") " + "- " + contact.patientDobFormatted;
    }
    if (contact.patientMrn) {
      recdisplayFormat += " [MRN : " + contact.patientMrn + "]";
    }
    if (contact.isVirtual) {
      recdisplayFormat += " (Virtual)";
    }
    if (!contact.isVirtual) {
      recdisplayFormat += " (Enrolled)";
    }

    let selectedIndex = this.selectedRecipients.indexOf(String(contactOption));
    if (selectedIndex == -1) {
      var acUser = {
        IdentityValue: null,
        alternateContacts: [],
        c_fname: null,
        c_grp: null,
        c_lname: null,
        caregiverIdentityValue: null,
        caregiver_displayname: null,
        caregiver_dob: null,
        caregiver_mobile: null,
        caregiver_paient_id: null,
        caregiver_patient_displayname: null,
        caregiver_patient_dob: null,
        caregiver_userid: null,
        caregiver_username: null,
        cmisid: contact.cmisid,
        countryCode: contact.countryCode,
        displayname: contact.displayName,
        dob: "",
        dob_formatted: "",
        dualRoles: "[]",
        emailVerificationStatus: true,
        firstname: contact.firstName,
        isCaregiverPatient: false,
        languages: "",
        last_login: "",
        lastname: contact.lastName,
        mobVerificationStatus: true,
        mobile: contact.mobile,
        name: contact.email,
        optionId: contact.contactId,
        option_text: contact.patientDisplayName+" - "+contact.patientDobFormatted,
        password: contact.password,
        passwordStatus: contact.password,
        recipientOptionText: recdisplayFormat,
        role: "Alternate Contact",
        roleId: "3",
        status: contact.status,
        tenantId: contact.tenantId,
        tenantName: contact.tenantName,
        tenantRoleId: "11",
        tenantid: contact.tenantId,
        userId: contact.contactId,
        userid: contact.contactId,
        verificationStatus: null,
        contactOption:contactOption,
        noContactAvailable: contact.noContactAvailable,
        isContactNotOptedIn: contact.isContactNotOptedIn 
      };
      this.setRecipientForSend(user,'alternatecontact');
      this.selectedRecipients.push(contact.contactId+'--'+user);
      this.selectedRecipientNames.push({ text: acUser.recipientOptionText })
      this.selectedRecipientsArray.push(acUser);
      this.setSelectedRecipientForms(false, acUser);
      /*** new activity ***/
      var activityData = {
        activityName: "Select Recipient",
        activityType: "structured forms",
        activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected recipient " + users.recipientOptionText + "(" + user + ") Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
      };
      this._structureService.trackActivity(activityData);
      /*** new activity ***/
    } else {
      this.removeSelectedRecipient(contactOption);
    }
    if(!isBlank(this.selectedRecipients) && this.selectedRecipients.length > 1 && this.staffFacing === 'false') {
      this.clearAssociatePatientSelection();
    }
  }
  setRecipientForSend(user, userType) {
    if(this.showRecipientsForSendCompletedForm) {
      if(this.staffFacing !== 'false') {
        if(userType === 'associatepatient') {
          this.clearAssociatePatientSelection();
          this.associatePatientId = !isBlank(user) ? user : '';
        }
      } else {
        this.clearAssociatePatientSelection();
        if(userType === 'alternatecontact') {
          this.associatePatientId = user;
        } else if(userType === 'recipient') {
          const patientId = user.split('--')[1];
          this.associatePatientId = isBlank(patientId) ? user : patientId;
        }
      }      
    }
  }
  clearAssociatePatientSelection() {
    this.selectedRecipientsForSendCompleteForm = '';
    this.associatePatientId = '';
  }
  setSelectedRecipientForms(fromRemove = false, users: any = "") {
    if (this.selectedRecipients && this.selectedRecipients.length > 0) {
      console.log("setSelectedRecipientForms : Enter==== fromRemove =" + fromRemove);
      this.autosavecalledformid = localStorage.getItem('autosavecalledformid');
      this.autosavecalled = localStorage.getItem('autosavecalled');
      $('#btm-send-form-list').attr('disabled', true);
      console.log("localStorage.getItem('autosavecalled')=======> " + localStorage.getItem('autosavecalled'));
      console.log("this.activeForms====");
      console.log(this.activeForms);
      console.log("this.activeForms.stafFacing===========> " + this.activeForms.stafFacing);

      //this.setOrResetSelectedItemList(user,true);
      var recipients = this.selectedRecipients;
      console.log(recipients);
      recipients = recipients.filter(a => a.indexOf('tag-') === -1)
      console.log(recipients)
      this.receipientCountSaveDraft = recipients.length;

      console.log('this.selectedRecipientsArray', this.selectedRecipientsArray);

      this.enableSendModeSelection();

      if (this.activeForms.stafFacing === 'practitioner' || !this._structureService.isMultiAdmissionsEnabled) this.enableFormData();

      const recipi = this.selectedRecipients;
      if (recipi.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join() !== "") {
        if (this.activeForms.stafFacing !== 'practitioner') {
          this.clearForm = 0;
          this.setIframeUrlstaffFill();
        }
        this.stafffillfirst = true;
      }
      if (recipients.length <= 0 && recipi.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join() === "") {
        this.stafffillfirst = false;
      }
      if (this.patientAssociation && this.selectedAssosiatePatient !== "") {
        $('#btm-send-form-list').attr('disabled', false);
      }
      if (!fromRemove) {
        const textData = users.recipientOptionText;
        if (this.enableAlternateContacts && users.role === "Alternate Contact") {
          $("<span/>", { "class": 'tag-span', 'id': users.contactOption, text: textData, insertBefore: $(".recipient-search-area") }).append("<span class='remove' id=" + users.contactOption + ">x</span>");
        }
        else {
        $("<span/>", { "class": 'tag-span', 'id': users.optionId, text: textData, insertBefore: $(".recipient-search-area") }).append("<span class='remove' id=" + users.optionId + ">x</span>");
        }
      }     

     
    }
    
  }

  enableSendModeSelection() {
    const recipients = this.selectedRecipients.filter(a => !a.includes('tag-'));
  
    if (this.userDataConfig.enable_appless_model && +this.userDataConfig.enable_appless_model === 1 && recipients.length === 1) {
      const recipientId = recipients[0].includes('--') ? recipients[0].split('--')[0].trim() : recipients[0];
      this.selectedRecipient = this.selectedRecipientsArray.find(a => +a.userId === +recipientId);  
      this.showApplessMenu = true;
      if (this.showApplessMenu) {
        const mode = this.selectedRecipient.passwordStatus ? 'mobileapp' : 'appless';
        this.frmSendMode.controls['mode'].setValue(mode);
      } else {
        this.resetApplessMenu();
      }
    } else {
      this.resetApplessMenu();
    }
  }

  resetApplessMenu() {
    this.showApplessMenu = false;
    this.selectedRecipient = {};
    this.frmSendMode.controls['mode'].setValue("");
  }

  enableFormData() {
    const recipients = this.selectedRecipients;
    const recipientName = this.selectedRecipientNames;
    this.stafffillfirst = !!recipients.length;
    if (recipients.length) {
        this.receipientCount = recipients.length;
        if (this.activeForms.stafFacing != 'practitioner') {
          if (recipients.length == 1) {
            if (this.activeForms.externalFileExchange != true) {
              var datacheck: any = {
                formid: this.activeForms.id,
                loggedinuser: this.userData.userId,
                patientid: recipients[0],
                enableCollaborateEdit: this.userDataConfig.enable_collaborate_edit,
                admissionId: this._structureService.isMultiAdmissionsEnabled && !isBlank(this.selectedAdmission) ? this.selectedAdmission.id : undefined 
              };
              if (this.activeForms.externalFileExchange != true) {
                this._formsService.checkDraftForm(datacheck).then((result: any) => {
                  console.log(result.nodrafts);
                  if (result.nodrafts == false) {
                    var textpatent;
                    if (recipientName[0].text) {
                      textpatent = recipientName[0].text;
                    }
                    console.log("textpatent.trim()==> " + textpatent.trim());
                  
                    var activityDataStart = {
                      activityName: "Patient already have drafts for the selected form",
                      activityType: "structured forms",
                      activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + textpatent.trim() + "- this patient already have drafts for the form- " + this.activeForms.name + " (" + this.activeForms.id + ") "
                    };
                    if (this.userDataConfig.enable_collaborate_edit == 1) {
                      var worklistname = " in All Form Worklist";
                    } else {
                      var worklistname = " in My Form Worklist";
                    }
                    if (!this.brightreeFormLandingFlow) {
                      //TODO: Need to revamp all swal popups [CHP-6901].
                    var msgtxt = "You already have drafts for the Patient " + textpatent.trim() + worklistname
                    msgtxt = msgtxt.replace("<br>", "");
                    console.log(msgtxt);
                    swal({
                      title: "Are you sure?",
                      text: msgtxt,
                      type: "warning",
                      showCancelButton: true,
                      cancelButtonClass: "btn-default",
                      confirmButtonClass: "btn-warning",
                      cancelButtonText: "Continue",
                      confirmButtonText: "Go To Drafts",
                      closeOnConfirm: true
                    }, (confirm) => {
                      if (confirm) {
                        if (this.userDataConfig.enable_collaborate_edit == 1) {
                          var enable_collaborate_edit = " collaborateDraft-Enabled";
                        } else {
                          var enable_collaborate_edit = "";
                        }
                        var activityData = {
                          activityName: "Patient already have drafts for the selected form and navigated to draft bucket" + enable_collaborate_edit,
                          activityType: "structured forms",
                          activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + textpatent.trim() + "- this patient already have drafts for the form- and navigated to draft bucket " + this.activeForms.name + " (" + this.activeForms.id + ") "
                        };
                        this._structureService.trackActivity(activityData);
                        this._structureService.setCookie('tabname','DRAFTS', 1);
                        this.emitSubmitFormToPAH()
                        //this.router.navigate(['/forms/worklist']);
                        if (this.userDataConfig.enable_collaborate_edit == 1) {
                          this.router.navigate(['/forms/list']);
                        } else {
                          this.router.navigate(['/forms/worklist']);
                        }
                        this._sharedService.myFormsType = "DRAFTS";

                      } else {
                        if (this.userDataConfig.enable_collaborate_edit == 1) {
                          var enable_collaborate_edit = " collaborateDraft-Enabled";
                        } else {
                          var enable_collaborate_edit = "";
                        }
                        var activityData = {
                          activityName: "Patient already have drafts for the selected form and continue with the form" + enable_collaborate_edit,
                          activityType: "structured forms",
                          activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + textpatent.trim() + "- this patient already have drafts for the form - and continue with the form " + this.activeForms.name + " (" + this.activeForms.id + ") "
                        };
                        this._structureService.trackActivity(activityData);
                        this.selectedAssosiatePatient = recipients[0];
                        this.clearForm = 0;
                        this.setIframeUrlstaffFill();
                      }

                    })
                    } else {
                      this.selectedAssosiatePatient = recipients[0];
                        this.clearForm = 0;
                        this.setIframeUrlstaffFill();
                    }
                    this._structureService.trackActivity(activityDataStart);
                  } else {
                    this.selectedAssosiatePatient = recipients[0];
                    this.clearForm = 0;
                    this.setIframeUrlstaffFill();
                  }
                })
              }
            }
          } else {
            this.selectedAssosiatePatient = recipients[0];
            this.clearForm = 0;
            this.setIframeUrlstaffFill();
          }
        }
        this.stafffillfirst = true;
        console.log("recipients--111");
         if (this.activeForms && this.activeForms.stafFacing && this.activeForms.stafFacing == 'practitioner') {
        this.setIframeUrl();
        }
      } else {
        this.stafffillfirst = false;
      }
  }

  /**
   * Function to perform actions after loading iframe
   */
  onLoadForm(){
    (this.selectedRecipientsForSendCompleteForm) ? this.setRecipients(this.selectedRecipientsForSendCompleteForm) : ''
    this.frmSendMode.controls['mode'].disable();
    if (!isBlank(this.formContent)) {
      this.frmSendMode.controls['mode'].enable();
      this.formSendModeChange(this.frmSendMode.controls['mode'].value);
    }
  }

  formSendModeChange(mode) {
    this.frmSendMode.controls['mode'].setValue(mode);
    if($("#message").val() != undefined){
      this.messageadd = $("#message").val().trim();
    }
    const iframe = document.getElementById('structured-form-dekstop') as HTMLIFrameElement;
    const contentWindow = iframe.contentWindow;
    if(contentWindow){
      var postData: any = {
          "formsendingmode": mode,
          "messagewithform": this.messageadd,
          "sendviaPostmessage":1
        };
        if(this.isPatientFacing) {
          postData.maximize = this.isFullWidthCollapsed;
        }
        contentWindow.postMessage(postData, '*');
    }
  }
  recipientCloseWithoutSaveData(): Promise<any> {
    console.log("recipientCloseWithoutSaveData==========");
    return new Promise(resolve => {
      this.autosavecalled = localStorage.getItem('autosavecalled');
      console.log("localStorage.getItem('autosavecalled')=======> " + localStorage.getItem('autosavecalled'));
      if (this.activeForms.stafFacing == "false" && this.activeForms.staffFill == "false") {
        this.autosavecalled = "false"
      }
      console.log("this.autosavecalled===========> " + this.autosavecalled);
      if (this.autosavecalled == "true") {
        var msgtxt = "You want to close this Form or continue with the form?";
        swal({
          title: "Are you sure?",
          text: msgtxt,
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default",
          confirmButtonClass: "btn-warning",
          cancelButtonText: "Close",
          confirmButtonText: "Continue",
          closeOnConfirm: true
        }, (confirm) => {
          if (confirm) {

            //$('#tenantUsers').val([localStorage.getItem('associatePatientNew')]).trigger('change');
            console.log(localStorage.getItem('associatePatientNew'));
            this.associatePatientNew = 1;
            this.showApplessMenu = false;
            resolve(false);
          }
          else {
            var activityData = {
              activityName: "Switched the form before saved as draft ",
              activityType: "structured forms",
              activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + this.selectedAssosiatePatient + "- Switched the form before saved as draft - " + this.activeForms.name + " (" + this.activeForms.id + ") "
            };
            this._structureService.trackActivity(activityData);
            localStorage.setItem('autosavecalledformid', "0");
            localStorage.setItem('autosavecalled', "false");
            resolve(true);
          }
        })
      } else {
        resolve(true);
      }
    });
  }
  closeSelectedRecipient(condition: boolean = false) {
    console.log("closeSelectedRecipient :called........");
    console.log("this.selectedRecipients.length=======>" + this.selectedRecipients.length);
    console.log("this.selectedRecipients=======>", this.selectedRecipients);
    /*** new activity ***/
      var recipients = this.selectedRecipients;
      var recipientName = this.selectedRecipientNames//$('#tenantUsers').select2('data');
     for(var k=0;k<recipients.length;k++){
      var activityData = {
        activityName: "Remove Recipient",
        activityType: "structured forms",
        activityDescription: this.userData.displayName + "(" + this.userData.userId + ") remove recipient " + this.selectedRecipientsArray[k].recipientOptionText + "(" + recipients[k] + ") Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
      };
      this._structureService.trackActivity(activityData);
    }
    /*** new activity ***/  

    $("#recipient-search").text(" ").text("Search");
    if (this.selectedRecipients.length == 1) {
      this.recipientCloseWithoutSaveData().then(async (result) => {
        var result = await result;
        if (result) {
          this.resetRecipient(condition);
        }
      })
    } else {
      this.resetRecipient(condition);
    }
    if(this.activeForms.patientAssociation === 'false') {
      this.clearAssociatePatientSelection();
    }
  }
  resetRecipient(condition: boolean = false) {
    console.log("resetRecipient :called........")
    this.selectedRecipients = [];
    this.selectedRecipient = {};
    this.selectedRecipientNames = [];
    this.formContent='';
    $(".tag-span").remove();
    this.stafffillfirst = false;
    if (!condition)
      this.enableOrDisableUiLI(false, true, "R");
    
    this.showApplessMenu = false;
    this.frmSendMode.controls['mode'].setValue("");
    this.selectedRecipientsArray = [];
    this.isNonContactableUsersExists = false;
    this.tenantUsers = [];
  }
  doneSelectedRecipient() {
    if (this.selectedRecipients && this.selectedRecipients.length)
      this.enableOrDisableUiLI(false, true, "R");
  }
  selectAllRecipients(event) {
    if (this.tenantUsers && this.tenantUsers.length > 0) {
      this.tenantUsers.forEach(element => {
        var id = element.optionId;
        var index = this.selectedRecipients.indexOf(id);
        if (index == -1) {
          this.setSelectedRecipients(element, id, event);
        }
      });
    }
    this.tenantUsers.forEach((tenantUser) => {
      if (this.enableAlternateContacts && tenantUser && tenantUser.alternateContacts && tenantUser.alternateContacts.length > 0) {
        tenantUser.alternateContacts.forEach((item) => {
          if (this.selectedRecipients.indexOf(item.contactId) === -1) {
            this.setSelectedAlternateContact(item, item.patientId, item);
          }
        });
      }
    });
    var activityData = {
      activityName: "Select Recipients",
      activityType: "structured forms",
      activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected recipient -" + this.selectedRecipients + " Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
    };
    this._structureService.trackActivity(activityData);
  }
  openRecipientList(){
    var txt = $('#tagsInput').val();
    if (txt) {
      this.recipientInputFocus();
    }

  }
  recipientInputFocus() {
    console.log("recipientInputFocus :call enableOrDisableUiLI.......");
    this.enableOrDisableUiLI(true, false, "R");
  }
  checkUserExist(user) {
    if (this.selectedRecipients.indexOf(String(user)) > -1) {
      return true;
    }
    return false;
  }
  removeSelectedRecipient(id) {
    console.group("removeSelectedRecipient = " + id);
    var index = this.selectedRecipients.indexOf(id);
    console.log("index==>" + index);
    if (index > -1) {
      if (this.selectedRecipients.length == 1) {
        console.log("recipientCloseWithoutSaveData calledddddddddddd")
            this.recipientCloseWithoutSaveData().then(async (result) => {
                var result = await result;
                if (result) {
                  this.deleteRemovedUserFromList(index, id);
                }
              })
      } else {
        this.deleteRemovedUserFromList(index, id);
      }
    }
    if (this.selectedRecipients.length === 1 && this.staffFacing === 'false') {
      this.setRecipientForSend(this.selectedRecipients[0], 'recipient'); 
    }
    console.groupEnd();
  }
  deleteRemovedUserFromList(index, id) {
   /*** new activity ***/
    var activityData = {
    activityName: "Remove Recipient",
    activityType: "structured forms",
    activityDescription: this.userData.displayName + "(" + this.userData.userId + ") remove recipient " + this.selectedRecipientsArray[index].recipientOptionText + "(" + id + ") Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
    };
    this._structureService.trackActivity(activityData);
   /*** new activity ***/    
    delete this.selectedRecipients[index];

    this.selectedRecipientsArray = this.selectedRecipientsArray.filter((item) => { console.log(item); return item.userId != id});

    this.setOrResetSelectedItemList(id);
    this.setSelectedRecipientForms(true);
    this.selectedRecipients = this.selectedRecipients.filter((id) => { console.log(id); return id != "" });
    console.log(this.selectedRecipients);
    this.isNonContactableUsersExistsForGivenUserTags();
    this.setIframeUrlstaffFill();    
  }
  setOrResetSelectedItemList(id) {
    $("#" + id).remove();
    return true;
  }
  ClearForm() {

    swal({
      title: "Are you sure?",
      text: "You are going to clear this form",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Yes",
      closeOnConfirm: true
    }, () => {
      this.clearFlag = 1;
      console.log("ddddddd loaded");
      console.log("dddddddd loaded");
      console.log("Select2wwww loaded");
      if (this.receipientCount) {
        console.log("Select2wwww loaded");
        console.log("Select2wwww loaded");
        console.log("Select2wwww loaded");
        this.clearForm = 1;
        this.receipientCount = 2;
        this.setIframeUrlstaffFill();
      }
      else {



        this.clearForm = 1;
        this.receipientCount = 2;
        this.setIframeUrl();
      }
    });
  }
  setIframeUrl() {
     $(".saveAsDraft-message").html("");
     $("#last_save_draft").val("");
     $("#error_save_draft").val("");
    if((this.activeForms.patientAssociation=='true' && !this.selectedAssosiatePatient) || (this.activeForms && this.activeForms.stafFacing == "practitioner"
   && !this.selectedRecipients.length )){
     return false;
    }
    console.log("setIframeUrl triggered");
    console.log(this.activeForms);
    console.log("setIframeUrl :staffFill=>", this.staffFill);
    console.log("setIframeUrl: stafffillfirst=>", this.stafffillfirst);
    console.log("setIframeUrl :selectedAssosiatePatient=>", this.selectedAssosiatePatient);
    //$('.clear-btn-form').css('display', 'block');
    this.receipientCount = this.selectedRecipients.length;
    if(this.activeForms.stafFacing == "true" && this.selectedAssosiatePatient){
      this.receipientCount = 1;
    }else{
      this.receipientCount = 0;
    }
    if (this.activeForms.externalFileDisclosePHI == true) {
      var datacheck: any = {
        formid: this.activeForms.id,
        loggedinuser: this.userData.userId,
        patientid: this.selectedAssosiatePatient,
        tagID: this.activeForms.tagId

      };
      this._formsService.checkPHIForm(datacheck).then((result: any) => {
        console.log(11111111111111);
        if (result.nosubmission == false) {
          $('.clear-btn-form').css('display', 'none');
          var msgtxt = "You already have Submitted (" + this.activeForms.name + ") for the selected user"
          swal({
            title: "",
            text: msgtxt,
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            cancelButtonText: "OK",
            confirmButtonText: "CANCEL",
            closeOnConfirm: true
          }, (confirm) => {
            if (confirm) {
              this.formContent = this.sanitizer.bypassSecurityTrustResourceUrl("");
            }
          })
        }
        else {
          console.log(2222222222222222);
          if (this.receipientCountSaveDraft > 1) {
            this.savaAsDraftStaff = 0;
          }
          var clientId = this._structureService.socket.io.engine.id;
          var tokenId = this._structureService.getCookie('authenticationToken');
          var facing_new = this.activeForms.facing_new;
          if (this.userDataConfig.save_as_draft_message_interval) {

            localStorage.setItem('saveAsDraftMessageInterval', this.userDataConfig.save_as_draft_message_interval);
          }
          if (this.userDataConfig.enable_collaborate_edit) {

            localStorage.setItem('enableCollaborateEdit', this.userDataConfig.enable_collaborate_edit);
          }
          if (this.selectedAssosiatePatient) {
            var enable_sftp_integration=this.userDataConfig.enable_sftp_integration==1?"&enable_sftp_integration="+true:"&enable_sftp_integration="+false;
            var unique="&uniqueFormIdentity="+Guid.create();
            var confirmActionPrefill=this.activeForms.confirmActionPrefill==1?"&confirmActionPrefill=true":"&confirmActionPrefill=false";
            var fax_queue_show_warning=this.userDataConfig.fax_queue_show_warning == 1?"&fax_queue_show_warning="+true+unique+confirmActionPrefill:"&fax_queue_show_warning="+false+unique+confirmActionPrefill;
            var formsUrl = this._structureService.machFormUrl + APIs.machFormEmbed +'?id=' + this.activeForms.id + "&patientId=" + this.selectedAssosiatePatient + "&loginUserId=" + this.selectedAssosiatePatient + "&tenantId=" + ((this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')) ? this._structureService.getCookie('crossTenantId') : this._structureService.getCookie('tenantId')) + "&toId=" + this.userData.userId + "&sentId=null&environment=" + this._structureService.environment + "&formName=" + encodeURIComponent(this.activeForms.name) + "&formId=" + this.activeForms.id + "&apiVersion=" + this._structureService.version + "&toName=" + this.userData.displayName + "&fromName=" + this.userData.displayName + "&serverBaseUrl=" + encodeURIComponent(this._structureService.serverUrlCustom) + (this._structureService.isMultiAdmissionsEnabled && this.selectedAdmission ? `&admissionId=${this.selectedAdmission.id}`: '');

            if(this.activeForms.stafFacing == "true"){
           formsUrl += "&staffFacing=true";
            }else{
           formsUrl += "&staffFacing=false";
            }

           formsUrl += "&fromMob=false&isWorkingHour=" + this._inboxService.checkInfusionHours(true).isWorkingHours + "&autoSave=" + this.autoSave + "&savaAsDraftStaff=" + this.savaAsDraftStaff + "&savaAsDraftPatient=" + this.savaAsDraftPatient + "&clientId=" + clientId + "&clearForm=" + this.clearForm + "&receipientCount=" + this.receipientCount + "&externalFileExchange=" + this.activeForms.externalFileExchange + "&authenticationToken=" + tokenId + "&facing_new=" + facing_new + "&saveAsDraftMessageInterval=" + localStorage.getItem('saveAsDraftMessageInterval') + "&enableCollaborateEdit=" + localStorage.getItem('enableCollaborateEdit') + "&populatePreviousSubmission=" + this.activeForms.populatePreviousSubmission+enable_sftp_integration+fax_queue_show_warning;

          }
          else {
            var enable_sftp_integration=this.userDataConfig.enable_sftp_integration==1?"&enable_sftp_integration="+true:"&enable_sftp_integration="+false;
            var unique="&uniqueFormIdentity="+Guid.create();
            var confirmActionPrefill=this.activeForms.confirmActionPrefill==1?"&confirmActionPrefill=true":"&confirmActionPrefill=false";
            var fax_queue_show_warning=this.userDataConfig.fax_queue_show_warning == 1?"&fax_queue_show_warning="+true+unique+confirmActionPrefill:"&fax_queue_show_warning="+false+unique+confirmActionPrefill;
            var formsUrl = this._structureService.machFormUrl + APIs.machFormEmbed + '?id=' + this.activeForms.id + "&patientId=" + this.userData.userId + "&loginUserId=" + this.userData.userId + "&tenantId=" + ((this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')) ? this._structureService.getCookie('crossTenantId') : this._structureService.getCookie('tenantId')) + "&toId=" + this.userData.userId + "&sentId=null&environment=" + this._structureService.environment + "&formName=" + encodeURIComponent(this.activeForms.name) + "&formId=" + this.activeForms.id + "&apiVersion=" + this._structureService.version + "&toName=" + this.userData.displayName + "&fromName=" + this.userData.displayName + "&serverBaseUrl=" + encodeURIComponent(this._structureService.serverUrlCustom) + "&fromMob=false&isWorkingHour=" + this._inboxService.checkInfusionHours(true).isWorkingHours + "&autoSave=" + this.autoSave + "&savaAsDraftStaff=" + this.savaAsDraftStaff + "&savaAsDraftPatient=" + this.savaAsDraftPatient + "&clientId=" + clientId + "&clearForm=" + this.clearForm + "&receipientCount=" + this.receipientCount + "&externalFileExchange=" + this.activeForms.externalFileExchange + "&authenticationToken=" + tokenId + "&facing_new=" + facing_new + "&saveAsDraftMessageInterval=" + localStorage.getItem('saveAsDraftMessageInterval') + "&enableCollaborateEdit=" + localStorage.getItem('enableCollaborateEdit') + "&populatePreviousSubmission=" + this.activeForms.populatePreviousSubmission+enable_sftp_integration+fax_queue_show_warning + (this._structureService.isMultiAdmissionsEnabled && this.selectedAdmission ? `&admissionId=${this.selectedAdmission.id}`: '');
            if (this.activeForms.stafFacing == "true") {
              formsUrl += "&staffFacing=" + this.activeForms.stafFacing;
            } else {
               formsUrl += "&staffFacing=" + this.activeForms.stafFacing + "&staffFilling=" + this.activeForms.staffFill;
                var recipientset = this.selectedRecipients;
            recipientset = recipientset.map(element => {
              var id = element.substr(element.indexOf(":") + 1);
              id = id.replace(/'/g, "");
              return id.replace(/\s/g, '');
            });
              formsUrl += "&recipientset=" + recipientset;
            }

          }
          if (this.activeForms.stafFacing == "practitioner") {
            console.log("1111111111111111111 result.........")
            var recipientset = this.selectedRecipients;
            recipientset = recipientset.map(element => {
              var id = element.substr(element.indexOf(":") + 1);
              id = id.replace(/'/g, "");
              return id.replace(/\s/g, '');
            });
            console.log("recipients list",recipientset);
            formsUrl += "&staffFacing=false&staffFilling=" + this.activeForms.staffFill + "&recipientset=" + recipientset + "&noStafffillvalidation=false&flowcheck=" + this.activeForms.stafFacing+"&authenticationToken="+this._structureService.getCookie('authenticationToken');
            
          }
     var formSendModeNew = this.frmSendMode.controls['mode'].value;

    var applessModeNew = '';
    if (this.userDataConfig.enable_appless_model == 1) {
      var virtualRecipients = this.selectedRecipientsArray.filter((x) => (x.passwordStatus == false));

      if (virtualRecipients.length > 0) {
        formSendModeNew = "appless";
      } else {
        formSendModeNew = "";
      }
      if (this.frmSendMode.controls['mode'].value == 'appless') {
        formSendModeNew = "appless";
      }
      if (this.frmSendMode.controls['mode'].value == 'mobileapp') {
        formSendModeNew = "mobileapp";
      }
      applessModeNew = 'both';

      formsUrl += '&applessMode=' + applessModeNew + "&formSendMode=" + formSendModeNew;
    }
     // session unique in multiple tabs checking
     formsUrl += "&create_send=true";
     // session unique in multiple tabs checking
          this.formContent = this.sanitizer.bypassSecurityTrustResourceUrl(formsUrl);
        }

      })
      this.formContent = this.sanitizer.bypassSecurityTrustResourceUrl("");
    } else {
      console.log(3333333333333333333);
      if (this.receipientCountSaveDraft > 1) {
        this.savaAsDraftStaff = 0;
      }
      var clientId = this._structureService.socket.io.engine.id;
      var tokenId = this._structureService.getCookie('authenticationToken');
      var facing_new = this.activeForms.facing_new;
      if (this.userDataConfig.save_as_draft_message_interval) {

        localStorage.setItem('saveAsDraftMessageInterval', this.userDataConfig.save_as_draft_message_interval);
      }
      if (this.userDataConfig.enable_collaborate_edit) {

        localStorage.setItem('enableCollaborateEdit', this.userDataConfig.enable_collaborate_edit);
      }
      if (this.selectedAssosiatePatient) {
        console.log("setIframeUrl : selectedAssosiatePatient")
        var enable_sftp_integration=this.userDataConfig.enable_sftp_integration==1?"&enable_sftp_integration="+true:"&enable_sftp_integration="+false;
        var unique="&uniqueFormIdentity="+Guid.create();
        var confirmActionPrefill=this.activeForms.confirmActionPrefill==1?"&confirmActionPrefill=true":"&confirmActionPrefill=false";
        var fax_queue_show_warning=this.userDataConfig.fax_queue_show_warning == 1?"&fax_queue_show_warning="+true+unique+confirmActionPrefill:"&fax_queue_show_warning="+false+unique+confirmActionPrefill;
        var formsUrl = this._structureService.machFormUrl + APIs.machFormEmbed + '?id=' + this.activeForms.id + "&patientId=" + this.selectedAssosiatePatient + "&loginUserId=" + this.selectedAssosiatePatient + "&tenantId=" + ((this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')) ? this._structureService.getCookie('crossTenantId') : this._structureService.getCookie('tenantId')) + "&toId=" + this.userData.userId + "&sentId=null&environment=" + this._structureService.environment + "&formName=" + encodeURIComponent(this.activeForms.name) + "&formId=" + this.activeForms.id + "&apiVersion=" + this._structureService.version + "&toName=" + this.userData.displayName + "&fromName=" + this.userData.displayName + "&serverBaseUrl=" + encodeURIComponent(this._structureService.serverUrlCustom) + (this._structureService.isMultiAdmissionsEnabled && this.selectedAdmission ? `&admissionId=${this.selectedAdmission.id}`: '');

            if(this.activeForms.stafFacing == "true"){
           formsUrl += "&staffFacing=true";
            }else{
           formsUrl += "&staffFacing=false";
            }

           formsUrl += "&fromMob=false&isWorkingHour=" + this._inboxService.checkInfusionHours(true).isWorkingHours + "&autoSave=" + this.autoSave + "&savaAsDraftStaff=" + this.savaAsDraftStaff + "&savaAsDraftPatient=" + this.savaAsDraftPatient + "&clientId=" + clientId + "&clearForm=" + this.clearForm + "&receipientCount=" + this.receipientCount + "&externalFileExchange=" + this.activeForms.externalFileExchange + "&authenticationToken=" + tokenId + "&facing_new=" + facing_new + "&saveAsDraftMessageInterval=" + localStorage.getItem('saveAsDraftMessageInterval') + "&enableCollaborateEdit=" + localStorage.getItem('enableCollaborateEdit') + "&populatePreviousSubmission=" + this.activeForms.populatePreviousSubmission+enable_sftp_integration+fax_queue_show_warning;
      } else {
        console.log(555555555555555555555);
        var enable_sftp_integration=this.userDataConfig.enable_sftp_integration==1?"&enable_sftp_integration="+true:"&enable_sftp_integration="+false;
        var unique="&uniqueFormIdentity="+Guid.create();
        var confirmActionPrefill=this.activeForms.confirmActionPrefill==1?"&confirmActionPrefill=true":"&confirmActionPrefill=false";
        var fax_queue_show_warning=this.userDataConfig.fax_queue_show_warning == 1?"&fax_queue_show_warning="+true+unique+confirmActionPrefill:"&fax_queue_show_warning="+false+unique+confirmActionPrefill;
        var formsUrl = this._structureService.machFormUrl + APIs.machFormEmbed + '?id=' + this.activeForms.id + "&patientId=" + this.userData.userId + "&loginUserId=" + this.userData.userId + "&tenantId=" + ((this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')) ? this._structureService.getCookie('crossTenantId') : this._structureService.getCookie('tenantId')) + "&toId=" + this.userData.userId + "&sentId=null&environment=" + this._structureService.environment + "&formName=" + encodeURIComponent(this.activeForms.name) + "&formId=" + this.activeForms.id + "&apiVersion=" + this._structureService.version + "&toName=" + this.userData.displayName + "&fromName=" + this.userData.displayName + "&serverBaseUrl=" + encodeURIComponent(this._structureService.serverUrlCustom) + "&fromMob=false&isWorkingHour=" + this._inboxService.checkInfusionHours(true).isWorkingHours + "&autoSave=" + this.autoSave + "&savaAsDraftStaff=" + this.savaAsDraftStaff + "&savaAsDraftPatient=" + this.savaAsDraftPatient + "&clientId=" + clientId + "&clearForm=" + this.clearForm + "&receipientCount=" + this.receipientCount + "&externalFileExchange=" + this.activeForms.externalFileExchange + "&authenticationToken=" + tokenId + "&facing_new=" + facing_new + "&saveAsDraftMessageInterval=" + localStorage.getItem('saveAsDraftMessageInterval') + "&enableCollaborateEdit=" + localStorage.getItem('enableCollaborateEdit') + "&populatePreviousSubmission=" + this.activeForms.populatePreviousSubmission+enable_sftp_integration+fax_queue_show_warning + (this._structureService.isMultiAdmissionsEnabled && this.selectedAdmission ? `&admissionId=${this.selectedAdmission.id}`: '');
        var siteIdParam="";
        if (this.activeForms.stafFacing == "true") {
          console.log(66666666666666666666);
          if(this.siteId !=0 && (this.faxQIntegration) && (this.activeForms.patientAssociation =="false")){
            siteIdParam="&siteId="+this.siteId;
          }
          formsUrl += "&staffFacing=" + this.activeForms.stafFacing+siteIdParam;
        } else {
           formsUrl += "&staffFacing=" + this.activeForms.stafFacing + "&staffFilling=" + this.activeForms.staffFill;
           var recipientset = this.selectedRecipients;
            recipientset = recipientset.map(element => {
              var id = element.substr(element.indexOf(":") + 1);
              id = id.replace(/'/g, "");
              return id.replace(/\s/g, '');
            });
         formsUrl += "&recipientset=" + recipientset;
        }

      }
      if (this.activeForms.stafFacing == "practitioner") {
        console.log("1111111111111111111 result.........")
        var recipientset = this.selectedRecipients;//$('#tenantUsers').val();
        recipientset = recipientset.map(element => {
          var id = element.substr(element.indexOf(":") + 1);
          id = id.replace(/'/g, "");
          return id.replace(/\s/g, '');
        });
        // formsUrl += "&staffFacing=false&staffFilling="+this.activeForms.staffFill+"&recipientset="+recipientset;
        formsUrl += "&staffFacing=false&staffFilling=" + this.activeForms.staffFill + "&recipientset=" + recipientset + "&noStafffillvalidation=false" + "&flowcheck=" + this.activeForms.stafFacing+"&authenticationToken="+this._structureService.getCookie('authenticationToken');
      }
      if(this.loadFormFrom == 'worklistCenter') {
        formsUrl += "&fromFormId=" + this.fwdFormId + "&prefillSubmissionId=" + this.forwardEntryId + "&prefillMode=" + this.prefillMode + "&prepopulatedsubmissionidforprefill=" + this.prefillFormEntryid + "&senderFielddisabled=true";
      }
      
      if(this.activeForms.stafFacing == "practitioner"){
      var formSendModeNew = this.frmSendMode.controls['mode'].value;

      var applessModeNew = '';
      if (this.userDataConfig.enable_appless_model == 1) {
        var virtualRecipients = this.selectedRecipientsArray.filter((x) => (x.passwordStatus == false));
        if (virtualRecipients.length > 0) {
          formSendModeNew = "appless";
        } else {
          formSendModeNew = "";
        }
        if (this.frmSendMode.controls['mode'].value == 'appless') {
          formSendModeNew = "appless";
        }
        if (this.frmSendMode.controls['mode'].value == 'mobileapp') {
          formSendModeNew = "mobileapp";
        }
        applessModeNew = 'both';

        formsUrl += '&applessMode=' + applessModeNew + "&formSendMode=" + formSendModeNew;
      }
    }
/***************** */
      console.log(formsUrl)
       if(this.messageadd != "" && this.messageadd != undefined){
        formsUrl += "&messageadd=" + this.messageadd;
      }
       // session unique in multiple tabs checking
        formsUrl += "&create_send=true";
       // session unique in multiple tabs checking
      this.formContent = this.sanitizer.bypassSecurityTrustResourceUrl(formsUrl);
      console.log("setIframeUrl : this.formContent => " + this.formContent);
    }
    // this.showClearForm = true;
    if((this.activeForms.patientAssociation=='false' && this.activeForms.stafFacing == 'true')){
     this.stafffillfirst = true;
    }
  }

  setAssosiatePatient(assosiatedPatient) {
    this.selectedAssosiatePatient = assosiatedPatient.userId;
    this.selectedAssosiatePatientName = assosiatedPatient.listDisplayName;
    $("input#associate-search-input").val(assosiatedPatient.listDisplayName);
    this.enableOrDisableUiLI(false, false);
    this.selectedAssociatedPatient = assosiatedPatient;
    if (!this._structureService.isMultiAdmissionsEnabled) this.selectAssosiatePatient(assosiatedPatient.userId, assosiatedPatient.listDisplayName, assosiatedPatient.displayname, assosiatedPatient.siteId)
  }

  selectedAdmissionAssociatePatientHandle(event) {
    const blankOrChanged = (event && event.id) ? this.selectedAdmission.id !== event.id : true; 
    if (blankOrChanged && this.activeForms.stafFacing === 'practitioner') {
      this.resetRecipient();
    }
    if (event && event.id) {
      this.selectedAdmission = event;
      this.selectAssosiatePatient(this.selectedAssociatedPatient.userId, this.selectedAssociatedPatient.listDisplayName, this.selectedAssociatedPatient.displayname, this.selectedAssociatedPatient.siteId);
    } else {
      this.resetAdmissionHandle();
    }
  }

  selectAssosiatePatient(selectedAssociatePatientId, selectedAssociatePatientName, PatientN = '', associatedPatientSiteId = null) {
    this.setRecipientForSend(selectedAssociatePatientId,'associatepatient');
    this.selectedAssociatedPatientSiteId = associatedPatientSiteId;
    if(this.selectedAssociatedPatientSiteId){
        this.recipientLoading = false;
    }
    var PatientNm = PatientN ? PatientN : selectedAssociatePatientName.trim();
    this.autosavecalledformid = localStorage.getItem('autosavecalledformid');
    this.autosavecalled = localStorage.getItem('autosavecalled');
    console.log("-----------------==================");
    console.log(this.autosavecalledformid);
    console.log(this.autosavecalled);
    console.log("-----------------==================");
    console.log("-----------------==================");
    if (this.autosavecalled == "true") {
      var msgtxt = "You want to close this Form or continue with the form?"
      swal({
        title: "Are you sure?",
        text: msgtxt,
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        cancelButtonText: "Close",
        confirmButtonText: "Continue",
        closeOnConfirm: true
      }, (confirm) => {
        if (confirm) {
          console.log("111111selectAssosiatePatient============> " + selectedAssociatePatientId)

        }
        else {
         this.associatedPatientOnChangePractitioner();
         var activityData = {
            activityName: "Switched the form before saved as draft ",
            activityType: "structured forms",
            activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + selectedAssociatePatientName + "- Switched the form before saved as draft - " + this.activeForms.name + " (" + this.activeForms.id + ") "
          };
          this._structureService.trackActivity(activityData);
          console.log("2222selectAssosiatePatient============> " + selectedAssociatePatientId)
          localStorage.setItem('autosavecalledformid', "0");
          localStorage.setItem('autosavecalled', "false");


          if (this.activeForms.externalFileExchange != true) {
            var datacheck: any = {
              formid: this.activeForms.id,
              loggedinuser: this.userData.userId,
              patientid: selectedAssociatePatientId,
              enableCollaborateEdit: this.userDataConfig.enable_collaborate_edit,
              admissionId: this._structureService.isMultiAdmissionsEnabled && !isBlank(this.selectedAdmission) ? this.selectedAdmission.id : undefined
            };
            this._formsService.checkDraftForm(datacheck).then((result: any) => {

              console.log(result.nodrafts);
              if (result.nodrafts == false) {
                var activityData = {
                  activityName: "Patient already have drafts for the selected form",
                  activityType: "structured forms",
                  activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + selectedAssociatePatientName + "- this patient already have drafts for the form- " + this.activeForms.name + " (" + this.activeForms.id + ") "
                };
                this._structureService.trackActivity(activityData);
                if (this.userDataConfig.enable_collaborate_edit == 1) {
                  var worklistname = " in All Form Worklist";
                } else {
                  var worklistname = " in My Form Worklist";
                }
                if (!this.brightreeFormLandingFlow) {
                  //TODO: Need to revamp all swal popups [CHP-6901].
                var msgtxt = "You already have drafts for the Patient " + selectedAssociatePatientName + worklistname;
                swal({
                  title: "Are you sure?",
                  text: msgtxt,
                  type: "warning",
                  showCancelButton: true,
                  cancelButtonClass: "btn-default",
                  confirmButtonClass: "btn-warning",
                  cancelButtonText: "Continue",
                  confirmButtonText: "Go To Drafts",
                  closeOnConfirm: true
                }, (confirm) => {
                  if (confirm) {
                    var activityData = {
                      activityName: "Patient already have drafts for the selected form and navigated to draft bucket",
                      activityType: "structured forms",
                      activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + selectedAssociatePatientName + "- this patient already have drafts for the form- and navigated to draft bucket " + this.activeForms.name + " (" + this.activeForms.id + ") "
                    };
                    this._structureService.trackActivity(activityData);
                    this._structureService.setCookie('tabname','DRAFTS', 1);
                    this.emitSubmitFormToPAH()
                    //this.router.navigate(['/forms/worklist']);
                    if (this.userDataConfig.enable_collaborate_edit == 1) {
                      this.router.navigate(['/forms/list']);
                    } else {
                      this.router.navigate(['/forms/worklist']);
                    }
                    this._sharedService.myFormsType = "DRAFTS";
                  } else {
                    if (this.userDataConfig.enable_collaborate_edit == 1) {
                      var enable_collaborate_edit = "collaborateDraft-Enabled";
                    } else {
                      var enable_collaborate_edit = "";
                    }
                    var activityData = {
                      activityName: "Patient already have drafts for the selected form and continue with the form" + enable_collaborate_edit,
                      activityType: "structured forms",
                      activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + selectedAssociatePatientName + "- this patient already have drafts for the form - and continue with the form " + this.activeForms.name + " (" + this.activeForms.id + ") "
                    };
                    this._structureService.trackActivity(activityData);
                  }

                })
                }
              }


            })
          }
          this.clearForm = 0;
          var recipients = this.selectedRecipients;//$('#tenantUsers').val();

          if (recipients.length) {
            this.stafffillfirst = true;
          } else {
            this.stafffillfirst = false;
          }
          this.setIframeUrl();
          var activityData = {
            activityName: "Select Associate Patient",
            activityType: "structured forms",
            activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Associate Patient -333" + this.selectedAssosiatePatientName + "(" + this.selectedAssosiatePatient + ") Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
          };
          this._structureService.trackActivity(activityData);
        }
      })
    } else {
      this.associatedPatientOnChangePractitioner();
      if (this.activeForms.externalFileExchange != true) {
        var datacheck: any = {
          formid: this.activeForms.id,
          loggedinuser: this.userData.userId,
          patientid: selectedAssociatePatientId,
          enableCollaborateEdit: this.userDataConfig.enable_collaborate_edit,
          admissionId: this._structureService.isMultiAdmissionsEnabled && !isBlank(this.selectedAdmission) ? this.selectedAdmission.id : undefined
        };

        if (this.activeForms.stafFacing == 'true' && this.activeForms.stafFacing != 'practitioner') {
          // alert(this.activeForms.stafFacing)
          // alert(this.activeForms.name)
          // alert("inside")
          var datacheck: any = {
            formid: this.activeForms.id,
            loggedinuser: this.userData.userId,
            patientid: selectedAssociatePatientId,
            enableCollaborateEdit: this.userDataConfig.enable_collaborate_edit,
            admissionId: this._structureService.isMultiAdmissionsEnabled && !isBlank(this.selectedAdmission) ? this.selectedAdmission.id : undefined
          };
          $('#loadallowconfirm').show();
          this._formsService.checkAllowEditEnabled(datacheck).then((result: any) => {
            //alert(this.userData.userId)
            $('#loadallowconfirm').hide();

            if (result.ALLOWEDIT == true && result.FROMID == this.userData.userId) {
              //  alert(result.ALLOWEDIT)
              // alert("inside inside")
              var activityData = {
                activityName: "Patient already has allow edited data with selected form.",
                activityType: "structured forms",
                activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected the form and " + selectedAssociatePatientName.trim() + "- this patient already associated with the form " + this.activeForms.name + " (" + this.activeForms.id + ") and moved to pending."
              };
              this._structureService.trackActivity(activityData);
              if (!this.brightreeFormLandingFlow) {
                //TODO: Need to revamp all swal popups [CHP-6901].
              var msgtxt = "There is already form " + this.activeForms.name + " for patient " + PatientNm + " in pending category, do you want to continue with new form or review the form under pending category first?";
              swal({
                title: "Are you sure?",
                text: msgtxt,
                type: "warning",
                showCancelButton: true,
                cancelButtonClass: "btn-default",
                confirmButtonClass: "btn-warning",
                cancelButtonText: "Continue",
                confirmButtonText: "Go To Pending",
                closeOnConfirm: true
              }, (confirm) => {
                if (confirm) {
                  var activityData = {
                    activityName: "Patient already has allow edited data with selected form-navigated to pending",
                    activityType: "structured forms",
                    activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected the form and " + selectedAssociatePatientName.trim() + "- this patient already associated with the form " + this.activeForms.name + " (" + this.activeForms.id + ") and navigated to pending."
                  };
                  this._structureService.trackActivity(activityData);
                  this._structureService.setCookie('tabname','PENDING', 1);
                  this.emitSubmitFormToPAH()
                  this.router.navigate(['/forms/list']);
                  this._sharedService.myFormsType = "PENDING";

                } else {
                  var activityData = {
                    activityName: "Patient already has allow edited data with selected form-continue with the form",
                    activityType: "structured forms",
                    activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + selectedAssociatePatientName + "- this patient already have form in pending - and continue with the form " + this.activeForms.name + " (" + this.activeForms.id + ") "
                  };
                  this._structureService.trackActivity(activityData);
                  if (this.activeForms.externalFileExchange != true) {
                    this._formsService.checkDraftForm(datacheck).then((result: any) => {
                      console.log(result.nodrafts);
                      if (result.nodrafts == false) {
                        var activityData = {
                          activityName: "Patient already have drafts for the selected form",
                          activityType: "structured forms",
                          activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + selectedAssociatePatientName.trim() + "- this patient already have drafts for the form-" + this.activeForms.name + this.activeForms.name + " (" + this.activeForms.id + ") "
                        };
                        this._structureService.trackActivity(activityData);
                        if (this.userDataConfig.enable_collaborate_edit == 1) {
                          var worklistname = " in All Form Worklist";
                        } else {
                          var worklistname = " in My Form Worklist";
                        }
                        var msgtxt = "You already have drafts for the Patient " + selectedAssociatePatientName + worklistname;
                        swal({
                          title: "Are you sure?",
                          text: msgtxt,
                          type: "warning",
                          showCancelButton: true,
                          cancelButtonClass: "btn-default",
                          confirmButtonClass: "btn-warning",
                          cancelButtonText: "Continue",
                          confirmButtonText: "Go To Drafts",
                          closeOnConfirm: true
                        }, (confirm) => {
                          if (confirm) {
                            var activityData = {
                              activityName: "Patient already have drafts for the selected form and navigated to draft bucket",
                              activityType: "structured forms",
                              activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + selectedAssociatePatientName + "- this patient already have drafts for the form- and navigated to draft bucket " + this.activeForms.name + " (" + this.activeForms.id + ") "
                            };
                            this._structureService.trackActivity(activityData);
                            this._structureService.setCookie('tabname','DRAFTS', 1);
                            this.emitSubmitFormToPAH()
                            if (this.userDataConfig.enable_collaborate_edit == 1) {
                              this.router.navigate(['/forms/list']);
                            } else {
                              this.router.navigate(['/forms/worklist']);
                            }
                            //this.router.navigate(['/forms/worklist']);
                            this._sharedService.myFormsType = "DRAFTS";
                          }
                          else {
                            if (this.userDataConfig.enable_collaborate_edit == 1) {
                              var enable_collaborate_edit = "collaborateDraft-Enabled";
                            } else {
                              var enable_collaborate_edit = "";
                            }
                            var activityData = {
                              activityName: "Patient already have drafts for the selected form and continue with the form" + enable_collaborate_edit,
                              activityType: "structured forms",
                              activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + selectedAssociatePatientName + "- this patient already have drafts for the form - and continue with the form " + this.activeForms.name + " (" + this.activeForms.id + ") "
                            };
                            this._structureService.trackActivity(activityData);
                          }

                        })
                      }
                      
                      this.clearForm = 0;
                      var recipients = this.selectedRecipients;//$('#tenantUsers').val();

                      if (recipients) {
                        this.stafffillfirst = true;
                      } else {
                        this.stafffillfirst = false;
                      }
                      this.setIframeUrl();
                      var activityData = {
                        activityName: "Select Associate Patient",
                        activityType: "structured forms",
                        activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Associate Patient -444" + this.selectedAssosiatePatientName + "(" + this.selectedAssosiatePatient + ") Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
                      };
                      this._structureService.trackActivity(activityData);
                    })
                  }
                }
              })
              } else {
                this.clearForm = 0;
                if (this.selectedRecipient) {
                  this.stafffillfirst = true;
                } else {
                  this.stafffillfirst = false;
                }
                this.setIframeUrl();
              }
            }
            else {
              if (this.activeForms.externalFileExchange != true) {
                this._formsService.checkDraftForm(datacheck).then((result: any) => {
                  this.loadForm = false;
                  if (result.nodrafts == false) {
                    var activityData = {
                      activityName: "Patient already have drafts for the selected form",
                      activityType: "structured forms",
                      activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + selectedAssociatePatientName.trim() + "- this patient already have drafts for the form-" + this.activeForms.name + this.activeForms.name + " (" + this.activeForms.id + ") "
                    };
                    this._structureService.trackActivity(activityData);
                    if (this.userDataConfig.enable_collaborate_edit == 1) {
                      var worklistname = " in All Form Worklist";
                    } else {
                      var worklistname = " in My Form Worklist";
                    }
                    if (!this.brightreeFormLandingFlow) {
                      //TODO: Need to revamp all swal popups [CHP-6901].
                    var msgtxt = "You already have drafts for the Patient " + selectedAssociatePatientName + worklistname;
                    swal({
                      title: "Are you sure?",
                      text: msgtxt,
                      type: "warning",
                      showCancelButton: true,
                      cancelButtonClass: "btn-default",
                      confirmButtonClass: "btn-warning",
                      cancelButtonText: "Continue",
                      confirmButtonText: "Go To Drafts",
                      closeOnConfirm: true
                    }, (confirm) => {
                      if (confirm) {
                        this.loadForm = false;
                        var activityData = {
                          activityName: "Patient already have drafts for the selected form and navigated to draft bucket",
                          activityType: "structured forms",
                          activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + selectedAssociatePatientName + "- this patient already have drafts for the form- and navigated to draft bucket " + this.activeForms.name + " (" + this.activeForms.id + ") "
                        };
                        this._structureService.trackActivity(activityData);
                        this._structureService.setCookie('tabname','DRAFTS', 1);
                        this.emitSubmitFormToPAH()
                        //this.router.navigate(['/forms/worklist']);
                        if (this.userDataConfig.enable_collaborate_edit == 1) {
                          this.router.navigate(['/forms/list']);
                        } else {
                          this.router.navigate(['/forms/worklist']);
                        }
                        this._sharedService.myFormsType = "DRAFTS";
                      }
                      else {
                        this.loadForm = true;
                        if (this.userDataConfig.enable_collaborate_edit == 1) {
                          var enable_collaborate_edit = "collaborateDraft-Enabled";
                        } else {
                          var enable_collaborate_edit = "";
                        }
                        var activityData = {
                          activityName: "Patient already have drafts for the selected form and continue with the form" + enable_collaborate_edit,
                          activityType: "structured forms",
                          activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + selectedAssociatePatientName + "- this patient already have drafts for the form - and continue with the form " + this.activeForms.name + " (" + this.activeForms.id + ") "
                        };
                        this._structureService.trackActivity(activityData);
                        if(this.loadForm){
                          this.setIframeUrl();
                        }
                    }
                  })
                  } else {
                    this.loadForm = true;
                    if(this.loadForm){
                      this.setIframeUrl();
                    }
                  }
                  } else {
                    this.loadForm = true;
                  }
                  console.log("selectAssosiatePatient============> " + selectedAssociatePatientId)
                  this.selectedAssosiatePatient = selectedAssociatePatientId;//$("#assosiatedPatients").val();
                  console.log(this.selectedAssosiatePatient);
                  this.selectedAssosiatePatientName = selectedAssociatePatientName;
                  console.log("selectedAssociatePatientName===> " + selectedAssociatePatientName);
                  $("input#associate-search-input").val(selectedAssociatePatientName);
                  //$(".associate-close").css("display","block");
                  this.enableOrDisableUiLI(false, false);
                  this.clearForm = 0;
                  var recipients = this.selectedRecipients;
                  if (recipients) {
                    this.stafffillfirst = true;
                  } else {
                    this.stafffillfirst = false;
                  }
                  if(this.loadForm){
                    this.setIframeUrl();
                  }
                  var activityData = {
                    activityName: "Select Associate Patient",
                    activityType: "structured forms",
                    activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Associate Patient -555" + this.selectedAssosiatePatientName + "(" + this.selectedAssosiatePatient + ") Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
                  };
                  this._structureService.trackActivity(activityData);


                })
              }
            }

          })
        } else {
          this._formsService.checkDraftForm(datacheck).then((result: any) => {
            if (result.nodrafts == false) {
              var activityData = {
                activityName: "Patient already have drafts for the selected form",
                activityType: "structured forms",
                activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + selectedAssociatePatientName.trim() + "- this patient already have drafts for the form-" + this.activeForms.name + this.activeForms.name + " (" + this.activeForms.id + ") "
              };
              this._structureService.trackActivity(activityData);
              if (this.userDataConfig.enable_collaborate_edit == 1) {
                var worklistname = " in All Form Worklist";
              } else {
                var worklistname = " in My Form Worklist";
              }
              if (!this.brightreeFormLandingFlow) {
                      //TODO: Need to revamp all swal popups [CHP-6901].
              var msgtxt = "You already have drafts for the Patient " + selectedAssociatePatientName + worklistname;
              swal({
                title: "Are you sure?",
                text: msgtxt,
                type: "warning",
                showCancelButton: true,
                cancelButtonClass: "btn-default",
                confirmButtonClass: "btn-warning",
                cancelButtonText: "Continue",
                confirmButtonText: "Go To Drafts",
                closeOnConfirm: true
              }, (confirm) => {
                if (confirm) {
                  var activityData = {
                    activityName: "Patient already have drafts for the selected form and navigated to draft bucket",
                    activityType: "structured forms",
                    activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + selectedAssociatePatientName + "- this patient already have drafts for the form- and navigated to draft bucket " + this.activeForms.name + " (" + this.activeForms.id + ") "
                  };
                  this._structureService.trackActivity(activityData);
                  this._structureService.setCookie('tabname','DRAFTS', 1);
                  this.emitSubmitFormToPAH()
                  //this.router.navigate(['/forms/worklist']);
                  if (this.userDataConfig.enable_collaborate_edit == 1) {
                    this.router.navigate(['/forms/list']);
                  } else {
                    this.router.navigate(['/forms/worklist']);
                  }
                  this._sharedService.myFormsType = "DRAFTS";
                }
                var activityData = {
                  activityName: "Patient already have drafts for the selected form and continue with the form",
                  activityType: "structured forms",
                  activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + selectedAssociatePatientName + "- this patient already have drafts for the form - and continue with the form " + this.activeForms.name + " (" + this.activeForms.id + ") "
                };
                this._structureService.trackActivity(activityData);
              })
              }
            }
            console.log("selectAssosiatePatient============> " + selectedAssociatePatientId)
            this.selectedAssosiatePatient = selectedAssociatePatientId;//$("#assosiatedPatients").val();
            console.log(this.selectedAssosiatePatient);
            this.selectedAssosiatePatientName = selectedAssociatePatientName;
            console.log("selectedAssociatePatientName===> " + selectedAssociatePatientName);
            $("input#associate-search-input").val(selectedAssociatePatientName);
            //$(".associate-close").css("display","block");
            this.enableOrDisableUiLI(false, false);
            this.clearForm = 0;
            var recipients = this.selectedRecipients;
            if ( !isBlank(recipients)) {
              this.stafffillfirst = true;
            } else {
              this.stafffillfirst = false;
            }
            this.setIframeUrl();
            var activityData = {
              activityName: "Select Associate Patient",
              activityType: "structured forms",
              activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Associate Patient -666" + this.selectedAssosiatePatientName + "(" + this.selectedAssosiatePatient + ") Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
            };
            this._structureService.trackActivity(activityData);

          })

        }

      } else {
        console.log("selectAssosiatePatient: selectAssosiatePatient============> " + selectedAssociatePatientId)
        this.selectedAssosiatePatient = selectedAssociatePatientId;//$("#assosiatedPatients").val();
        console.log(this.selectedAssosiatePatient);
        this.selectedAssosiatePatientName = selectedAssociatePatientName;
        console.log("selectAssosiatePatient : selectedAssociatePatientName===> " + selectedAssociatePatientName);
        $("input#associate-search-input").val(selectedAssociatePatientName);
        //$(".associate-close").css("display","block");
        this.enableOrDisableUiLI(false, false);
        this.clearForm = 0;
        var recipients = this.selectedRecipients;//$('#tenantUsers').val();
        console.log("selectAssosiatePatient : this.selectedRecipients===> ", this.selectedRecipients);
        if (recipients) {
          this.stafffillfirst = true;
        } else {
          this.stafffillfirst = false;
        }
        var formData = {
          "formId": this.activeForms.id,
          "patientUser": this.selectedAssosiatePatient,
          "facing": this.activeForms.stafFacing
        }
        this._formsService.checkOrderchange(formData).then((data) => {
          console.log(data);
          var orderChangeResult: any = data;
          this.orderChangeStatus = orderChangeResult.orderChange;
          console.log(this.orderChangeStatus);

        })
        this.setIframeUrl();
        var activityData = {
          activityName: "Select Associate Patient",
          activityType: "structured forms",
          activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected Associate Patient -111" + this.selectedAssosiatePatientName + "(" + this.selectedAssosiatePatient + ") Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
        };
        this._structureService.trackActivity(activityData);
      }
    }
  }

  closeSelectedAssociatePatient(reset = false) {
    this.autosavecalledformid = localStorage.getItem('autosavecalledformid');
    this.autosavecalled = localStorage.getItem('autosavecalled');
     /*** new activity ***/
    if(this.selectedAssosiatePatient){
      var activityData = {
        activityName: "Remove Associate Patient1",
        activityType: "structured forms",
        activityDescription: this.userData.displayName + "(" + this.userData.userId + ") remove Associate Patient " + this.selectedAssosiatePatientName + "(" + this.selectedAssosiatePatient + ") Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
      };
      this._structureService.trackActivity(activityData);
    }
    /*** new activity ***/
    /*** new activity ***/
    var recipient = this.selectedRecipients;
    if(recipient.length > 0){
      for(var k=0;k<recipient.length;k++){
        var activityData = {
          activityName: "Remove Recipient",
          activityType: "structured forms",
          activityDescription: this.userData.displayName + "(" + this.userData.userId + ") remove recipient " + this.selectedRecipientNames[k].recipientOptionText + "(" + this.selectedRecipients[k] + ") Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
        };
        this._structureService.trackActivity(activityData);
      }
    }
    /*** new activity ***/  
    if (this.autosavecalled == "true") {
      var msgtxt = "You want to close this Form or continue with the form?"
      swal({
        title: "Are you sure?",
        text: msgtxt,
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        cancelButtonText: "Close",
        confirmButtonText: "Continue",
        closeOnConfirm: true
      }, (confirm) => {
        if (confirm) {

        }
        else {
           var activityData = {
            activityName: "Switched the form before saved as draft ",
            activityType: "structured forms",
            activityDescription: this.userData.displayName + "(" + this.userData.userId + ")" + this.selectedAssosiatePatient + "- Switched the form before saved as draft - " + this.activeForms.name + " (" + this.activeForms.id + ") "
          };
          this._structureService.trackActivity(activityData);
          localStorage.setItem('autosavecalledformid', "0");
          localStorage.setItem('autosavecalled', "false");
          if (this.selectedAssosiatePatient) {
            console.log("ffffffffffffffffffffffffffffffffffffffffffffffffffff");
            this.enableOrDisableUiLI(false, true);
            this.selectedAssosiatePatientName = "";
            this.goToForm(this.activeForms, true);
          } else if ($("input#associate-search-input").val() != "") {
            console.log("ffdddddddddddddddddddddddddddddddddddddddddddddddddddddd")
            this.enableOrDisableUiLI(false, true);
            //$("input#associate-search-input").val("");
            $("#associate-search").text(" ").text("Search");
            this.associatePatientLoading = false;
          }
          this.assosiatedPatients = [];

        }
      })
    }
    else {
      if (this.selectedAssosiatePatient) {
        this.enableOrDisableUiLI(false, true);
        this.selectedAssosiatePatientName = "";
        this.goToForm(this.activeForms, true);
      } else if ($("input#associate-search-input").val() != "") {
        $("input#associate-search-input").val("");
        $("#associate-search").text(" ").text("Search");
        this.associatePatientLoading = false;
      }
        if (!reset) {
            this.assosiatedPatients = [];
        }
        if (this.activeForms.stafFacing == 'practitioner') {
            this.selectedAssociatedPatientSiteId = null;
            this.resetRecipient();
        }
    }
    this.clearAssociatePatientSelection();
  }
  openAssocpopup() {
    this.newPatient.reset();
    if (this._structureService.isMRNFieldMandatory) {
      this.newPatient.get('mrn').setValidators([Validators.required]);
      this.newPatient.get('mrn').updateValueAndValidity();
    } else {
      this.newPatient.get('mrn').clearValidators();
      this.newPatient.get('mrn').updateValueAndValidity();
    }
    this.hasData = false;
    this.searchRequired = false;
    this.showPatientForm = false;
    this.patientDataLoadingMsg = false;
    $('#assocModal').modal({ backdrop: 'static', keyboard: false });
  }

  closeAssocpopup() {
    this.emptySiteCondtion = false;
    this.isSiteMandatory = false;
    this.assocSiteIdCopy = this.assocSiteId;
    this.assocSiteId = 0;
    this.assosiatedPatients=[];
    this.emitEventToSelectSites('removeSelectedSite');
    this.hasData = false;
    $('#assocModal').modal('hide');
  }
  _createNewAssoc(form) {
    
        form = form.value;
        this.popupmsg = "Saving your details...";
        NProgress.start();
    if(typeof (this.assocSiteId) === 'undefined'){
      this.assocSiteId="0";
    }
    if (this.assocSiteId !== "0") {
      this.assocSiteId = this.assocSiteId.toString();
    }
    var data = { "email": form.email, "firstname": form.firstName, "lastname": form.lastName, "dob": $('#dob-date-picker').val(), "cell": form.cellno, "zipcode": form.zipcode, "operation": 0, "mrn": form.mrn, "createdBy": this.userData.userId, "siteIds": +this.assocSiteId};
    if (this.userData.config.enable_nursing_agencies_visibility_restrictions != 1) {
    }
    this.closeAssocpopup();
    this._structureService.createAssocpatient(data).subscribe((res) => {
      NProgress.done();
      var result = JSON.parse(res.text());
      if (result.status == 1 && result.already == true && result.message == 1) {
        var notify = $.notify('Sorry! Patient already created.');
        setTimeout(() => {
          notify.update({ 'type': 'warning', 'message': '<strong>Sorry! Patient already created.</strong>' });
          this.openAssocpopup()
        }, 1000);

      }
      else if (result.status == 1 && result.already && result.message == 0) {
        this._structureService.notifyMessage({type:CONSTANTS.notificationTypes.warning,
          message: this._ToolTipService.getTranslateData('MESSAGES.PATIENT_EXISTS_FL_LN_DOB')});
        setTimeout(() => {
          this.openAssocpopup()
        }, 1000);

      } else if (result.status == 1 && result.already == false) {
        var monthNames = ["01", "02", "03", "04", "05", "06", "07", "8", "09", "10", "11", "12"];
        var date = monthNames[this.assocpdata.dmonth] + "/" + this.assocpdata.dday + "/" + this.assocpdata.dyear;
        $('#resetpform').trigger('click');
        $('.month, .day, .year').select2();
        $('.month').on('select2:select', (e) => {
          this.assocpdata.dmonth = $(e.target).val();
          $('.day').select2('open');
        }); $('.day').on('select2:select', (e) => {
          $('.year').select2('open');
          this.assocpdata.dday = $(e.target).val();
        });
        $('.year').on('select2:select', (e) => {
          this.assocpdata.dyear = $(e.target).val();
        });
        var mrnNew = form.firstName + " " + form.lastName + " - " + date
        if(form && form.mrn)
         mrnNew += ' [MRN: '+ form.mrn+'] (Virtual)';
        var virtualPatientObject = {
          id: result.userId,
          cmisId: 0,
          displayName: mrnNew,
          avatar: ''
        }

        this.selectedAssosiatePatient = result.userId;
        this.selectedAssosiatePatientName = virtualPatientObject.displayName;
        this.selectAssosiatePatient(this.selectedAssosiatePatient, this.selectedAssosiatePatientName, '', this.assocSiteIdCopy);
        this.assosiatedPatients.push(virtualPatientObject);
        $("input#associate-search-input").val(virtualPatientObject.displayName);
        var notify = $.notify('Success! Patient created.');
        setTimeout(function () {
          notify.update({ 'type': 'success', 'message': '<strong>Success! Patient created.</strong>' });
        }, 1000);

        var activityData = {
          activityType: "user creation",
          activityName: "On the Fly Patient Registration",
          activityDescription: "New on the fly user registered (" + virtualPatientObject.displayName + " " + data.dob + ") by " + this.userData.displayName + " (" + this.userData.userId + ")"
        };
        this._structureService.trackActivity(activityData);
      }
      else if ((result.status == 3 && result.already == true && result.message == 1) || (result.status == 1 && result.already == null && result.userId)) {
        var virtualPatientAllreadyExistForms = "already exists. Please confirm whether we can associate this patient";
        var self = this;
        swal({
          title: "Are you sure?",
          text: form.pfname + " " + form.plname + " " + virtualPatientAllreadyExistForms,
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default",
          confirmButtonClass: "btn-warning",
          confirmButtonText: "Ok",
          closeOnConfirm: true
        },
          (isConfirm) => {
            if (isConfirm) {
              this.structuredFormSend.patchValue({
                assosiatedPatients: result.userId
              });
              var monthNames = ["01", "02", "03", "04", "05", "06", "07", "8", "09", "10", "11", "12"];
              var date = monthNames[this.assocpdata.dmonth] + "/" + this.assocpdata.dday + "/" + this.assocpdata.dyear;
              this.selectedAssosiatePatient = result.userId;
              this.selectedAssosiatePatientName = form.pfname + " " + form.plname + " - " + date;
              $("input#associate-search-input").val(this.selectedAssosiatePatientName);
              //$("#assosiatedPatients").trigger('change');
              this.selectAssosiatePatient(this.selectedAssosiatePatient, this.selectedAssosiatePatientName);
            }
          });
      } else {
        var activityData = {
          activityType: "user creation",
          activityName: "On the Fly Patient Registration failed",
          activityDescription: "New on the fly user registeration failed (" + virtualPatientObject.displayName + " " + data.dob + ") by " + this.userData.displayName + " (" + this.userData.userId + ")"
        };
        this._structureService.trackActivity(activityData);
        var notify = $.notify('Sorry! We can\'t save your data, Please try again.');
        setTimeout(() => {
          notify.update({ 'type': 'danger', 'message': '<strong>Sorry! We can\'t save your data, Please try again.</strong>' });
          this.openAssocpopup()
        }, 1000);
      }
    });
  }
    getSiteIds(data: any) {
        this.siteIds = data.siteId;
        this.selectedSiteIds = data.siteId;
        this.cdRef.detectChanges();
        if (this.selectedFlow == 'patientdriven') {
            this.closePatientDrivenList();
        } else {
            if (this.activeForms.stafFacing == "true" || this.activeForms.stafFacing == "practitioner") {
                localStorage.setItem('autosavecalledformid', "0");
                localStorage.setItem('autosavecalled', "false");
                this.closeSelectedAssociatePatient();
            } else if (this.activeForms.stafFacing == "false") {
                this.resetRecipient(false);
            }
        }
        if(this.siteIds !== "0"){
          this.selectedSiteIds = this.siteIds.toString();
        }
        this.indexformsite="0";
        this.changesite();

    }
associatedPatientOnChangePractitioner(): void {
    // Remove recipients on change of assocsiated patient based on site id
    if(this.activeForms.stafFacing == "practitioner"){
        if(this.selectedRecipientsArray.length > 0){
            if(this.selectedRecipientsArray[0].siteId != this.selectedAssociatedPatientSiteId){
                this.resetRecipient();
            }
        }
  }
}
 hideDropdown(hideItem : any){
      this.hideSiteSelection = hideItem.hideItem;
  }
  emitEventToSelectSites(status): void {
    this.assocSelected = status;
    this.eventsSubject.next(this.assocSelected);
}
emitEventToSelectSitesId(status): void {
  this.siteId = status;
  this.eventsSubject.next(this.siteId);
}

getSiteId(siteId :any) {
  this.siteId = siteId.siteId;
  if(!this.hideSiteSelection){
    this.siteId= this.userData.mySites[0]['id'];
  }
  this.indexformsite=this.siteId;
  if((this.faxQIntegration) && (this.patientAssociation===false) && (this.staffFacing=='true' || this.staffFacing=='practitioner')){
    this.patientAssociationOffDiv=false;
  }
  if(this.siteId !='0'){
    this.patientAssociationOffDiv=true;
    this.setIframeUrl();
  }

}
changesite(){
  if(this.indexformsite =='0' && (this.faxQIntegration) && (this.patientAssociation===false) && (this.staffFacing=='true' || this.staffFacing=='practitioner')){
    this.patientAssociationOffDiv=false;
        this.emitEventToSelectSitesId('displaySelectedSitesOnly');
  }
}
checkiframe()
   {
    var formSendModeNew = this.frmSendMode.controls['mode'].value;
    if(formSendModeNew == ""){
      formSendModeNew = 'appless';
    }
    if($("#structured-form-dekstop").length > 0){
      this.formSendModeChange(formSendModeNew);
    }
    
  }

/**
 * notifyMessageSend() shows the notifying message after successfull form sending based on the form sending mode api/sqs 
 * @param String message 
 * @param String formname 
 */
notifyMessageSend(message,formName)
{
  var msg = '';
  if(this.userConfig.form_send_mode == 'sqs'){
    msg = formName + " has been successfully queued for the delivery";
  }else{
    msg = message;
  }
  this._structureService.notifyMessage({
    messge: msg,
    delay: 1000,
    type: 'success'
  });
}
showIntegrationItemsMissingWarnings(result) {
  let message = {
    caption:  '',
    check_integration_error : false,
    txt_en : ''
  };
  if (
    result['data']['esi_code_staff_config'] == '' &&
    result['data']['esi_code_patient_config'] == ''
  ) {
      message = {
          caption:  this._ToolTipService.getTranslateData('WARNING.ESI_IDS_MISSING'),
          check_integration_error : true,
          txt_en : this._ToolTipService.getTranslateData('WARNING.ESI_IDS_MISSING_CONTENT')
      }  
  } else if (result['data']['esi_code_patient_config'] == '') {
      message = {
        caption:  this._ToolTipService.getTranslateData('WARNING.ESI_PID_MISSING'),
        check_integration_error : true,
        txt_en : this._ToolTipService.getTranslateData('WARNING.ESI_PID_MISSING_CONTENT'),
      }   
  } else if (result['data']['esi_code_staff_config'] == '') {
      message = {
          caption:  this._ToolTipService.getTranslateData('WARNING.ESI_SPID_MISSING'),
          check_integration_error : true,
          txt_en : this._ToolTipService.getTranslateData('WARNING.ESI_SPID_MISSING_CONTENT'),
      }
  } else if (
    result['data']['patientIdentity'] == '' &&
    result['data']['staffId'] == ''
  ) {
      message = {
          caption:  this._ToolTipService.getTranslateData('WARNING.MRN_SPID_MISSING'),
          check_integration_error : true,
          txt_en : this._ToolTipService.getTranslateData('WARNING.MRN_SPID_MISSING_CONTENT')
      }
  } else if (result['data']['patientIdentity'] == '') {
      message = {
      caption:  this._ToolTipService.getTranslateData('WARNING.PATIENT_MRN_MISSING'),
      check_integration_error : true,
      txt_en : this._ToolTipService.getTranslateData('WARNING.PATIENT_MRN_MISSING_CONTENT')
      }
  } else if (result['data']['staffId'] == '') {
    message = {
      caption:  this._ToolTipService.getTranslateData('WARNING.STAFF_PARTNER_ID_MISSING'),
      check_integration_error : true,
      txt_en : this._ToolTipService.getTranslateData('WARNING.STAFF_PARTNER_ID_MISSING_CONTENT')
    }
  } else if (result['data']['cmisid'] == '' && result['data']['cmisidChecking'] == true) {
    message = {
      caption:  this._ToolTipService.getTranslateData('WARNING.PATIENT_CMISID_MISSING'),
      check_integration_error : true,
      txt_en : this._ToolTipService.getTranslateData('WARNING.PATIENT_CMISID_MISSING_CONTENT')
    }
  } else {
    message = {
      caption:  '',
      check_integration_error : false,
      txt_en : ''
    }
  }
  return message;  
 }

 /**
 * Function to get selected recipients list
 * @param selectedRecipients Recipients to send completed form
 */
 setRecipients(selectedRecipients) {
  this.selectedRecipientsForSendCompleteForm = selectedRecipients;
  const iframe = document.getElementById('structured-form-dekstop') as HTMLIFrameElement;
  const contentWindow = iframe.contentWindow;
  //set completed form recipients in the post data
  const data = {
    completedFormRecipientsPost: selectedRecipients.replace(/,/g, ",")
  };
  //post message to the child window
  contentWindow.postMessage(data, '*');
 }
 handleModalClose(isModalClose): void{
  if(isModalClose){
    this.closeAssocpopup();
  }
}
getPatientData(patientDetails){
  if(patientDetails.siteId != 0){
    const formvalue = patientDetails.formData.value
    this.assocSiteId = patientDetails.siteId;
    this.assocpdata.dmonth = formvalue.dobMonth;
    this.assocpdata.dday = formvalue.dobDay;
    this.assocpdata.dyear = formvalue.dobYear;
    this._createNewAssoc(patientDetails.formData);
  }
}
getSelectedPatientDetails(selectedPatientData){
  this.selectAssosiatePatient(selectedPatientData.patientDetailId, selectedPatientData.selectedAssocPatientName,this.selectedAssosiatePatient,selectedPatientData.siteId);
}
setRecipientName(userDetails) {
  let recipientText = '';
  if (userDetails.tag_name) {
    recipientText = `${userDetails.tag_name} [${this._ToolTipService.getTranslateData('LABELS.USER_TAG')}]`;
  } else if (+userDetails.roleId === 3) {
    if (userDetails.caregiver_userid) {
      recipientText = userDetails.caregiver_dob_formatted ? `${userDetails.caregiver_displayname} (${userDetails.displayname}) - ${userDetails.caregiver_dob_formatted}` : `${userDetails.caregiver_displayname} (${userDetails.displayname})`;
    } else {
      recipientText = userDetails.dob 
        ? `${userDetails.displayname} - ${userDetails.dob_formatted}` 
        : `${userDetails.displayname}`;
    }
  } else {
    recipientText = userDetails.displayname;
  }
  const mrnLabel = this._ToolTipService.getTranslateData('LABELS.MRN');
  const mrnText = userDetails.IdentityValue ? ` [${mrnLabel}: ${userDetails.IdentityValue}]` : '';
  recipientText += mrnText;
  userDetails.displayMrn = mrnText;
  recipientText = !userDetails.IdentityValue && userDetails.caregiverIdentityValue ? `${recipientText} [${mrnLabel}: ${userDetails.caregiverIdentityValue}]` : recipientText;
  userDetails.recipientOptionText = recipientText;
  userDetails.displayPatient = userDetails.displayname;
  if (userDetails.naTags && userDetails.naTags != null && userDetails.naTags != 'null') {
    userDetails.recipientOptionText += `( ${userDetails.naTagNames} )`;
  }
  userDetails.recipientOptionText = `${userDetails.recipientOptionText} ${userDetails.passwordStatus ? this._ToolTipService.getTranslateData('LABELS.PATIENT_ENROLED') : this._ToolTipService.getTranslateData('LABELS.PATIENT_VIRTUAL')}`  
  if(this.activeForms.stafFacing === 'false' && this.multiSiteEnable){
    userDetails.recipientOptionText += userDetails.siteName ? ` - ${userDetails.siteName} ` : '';
  }
}

  selectedAdmissionHandle(event) {
    this.selectedAdmission = event;
    if (isBlank(event)) {
      this.resetAdmissionHandle();
    } else if (!isBlank(event.id)){
      this.enableSendModeSelection();
      this.enableFormData();
    }
  }

  resetAdmissionHandle() {
    this.stafffillfirst = false;
    this.showApplessMenu = false;
    this.selectedAdmission = {};
  }

  get selectedPatient() {
    return !isBlank(this.selectedRecipients) && this.selectedRecipients[0] && this.selectedRecipients[0].includes('--') ? this.selectedRecipients[0].split('--')[1] : this.selectedRecipients[0];
  }

  get selectedUserTags(): string[] {
    return this.selectedRecipients.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')) || [];
  }

  isNonContactableUsersExistsForGivenUserTags(): void {
    const userTagIds = this.selectedUserTags;
    if (userTagIds.length === 0) {
      this.isNonContactableUsersExists = false;
      this.previousUserTagIds = new Set();
      return;
    }
    const currentUserTagIds = new Set(userTagIds);
    if (this.areSetsEqual(this.previousUserTagIds, currentUserTagIds)) {
      return; // No changes, do not call httpService
    }
    this.previousUserTagIds = currentUserTagIds;
    this.showPageLoader = true;
    this.httpService.doGet(APIs.usersNotContactableEndpoint, { params: { userTagIds: userTagIds.join(',') } }).subscribe((response: boolean) => {
      this.isNonContactableUsersExists = response;
      this.showPageLoader = false;
    }, () => {
      this.showPageLoader = false;
    });
  }

  showNonContactableUsersModal() {
    this._structureService.loadMicroFrontend();
    this.showNonContactableUsers = true;
    setTimeout(() => {
      $('#non-contactable-users-modal').modal('show');
    })
  }

  hideNonContactableUsersModal() {
    this._structureService.unLoadMicroFrontend();
    this.showNonContactableUsers = false;
    setTimeout(() => {$('#non-contactable-users-modal').modal('hide');})
  }
  
  getPatientSearchCriteria(patient) {
    if (patient.patientIdentity.IdentityValue) {
      return patient.patientIdentity.IdentityValue;
    } else if (patient.displayName && !patient.displayName.includes('null')) {
      return patient.displayName;
    } else {
      return '';
    }
  }

  emitSubmitFormToPAH() {
    if (this.isLoadFromPAH) this.submitForPAH.emit();
  }

  /** To make the preview more visible by increasing the hight while maximizing and reset to default height when minimizing
   * This only appliied for paitent facing form, the other form types are fine with the preview height
   */
  handleFormPreviewHeight() {
    const iframe = document.getElementById('structured-form-dekstop') as HTMLIFrameElement;
    const contentWindow = iframe.contentWindow;
    if (contentWindow) {
      contentWindow.postMessage({ maximize: this.isFullWidthCollapsed }, '*');
    }
  }
}

// form stabilization
window.addEventListener('message', function(event) {
var response = event.data;
try {
      JSON.parse(response);
    } catch (e) {
        return false;
    }
    if(JSON.parse(response).status == 1){
      $("#response").val(event.data);
      if((JSON.parse(response).bottombtn == 1)&&(JSON.parse(response).bottombtn)){
         $("#responsebtn").trigger('click');
      }     
    }else if(JSON.parse(response).fail == 1){
      $("#failNotify").trigger('click');      
    }
});  
