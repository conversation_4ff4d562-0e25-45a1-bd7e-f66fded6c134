<style>
    #assocModal .form-horizontal {
        width: 724px;
    }
    
    .red-color {
        color: red;
    }
</style>
<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Send Supply Count </strong>
            <!--<a [routerLink]="['/message/messagegroup']" class="pull-right btn btn-sm btn-primary">Add Message Group<i class="ml-1"></i></a>-->
        </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item">Send Supply Count</li>
        </ol>

    </div>
</section>
<section class="row chatroom-section">
    <div class="col-lg-4">
        <section class="card">
            <!--<div class="card-header">
                <span class="cat__core__title">
                    <strong>Message Groups</strong>
                </span>
            </div>-->
            <div class="card-block">
                <div class="cat__core__card-sidebar">
                    <div class="row">
                        <div class="col-md-12 searchbar">
                            <div class="cat__apps__messaging__header">
                                <input class="form-control cat__apps__messaging__header__input" placeholder="Search..." [(ngModel)]="searchInboxkeyword" #messageGroups/>
                                <i class="icmn-search"></i>
                                <button></button>
                            </div>
                        </div>
                    </div>
                    <div class="cat__apps__messaging__list inbox-data-container send-form-list">
                        <div *ngFor="let form of (forms | formSearch:messageGroups.value)">
                            <a href="javascript: void(0);">
                                <div class="cat__apps__messaging__tab messages-tab" (click)="goToForm(form)" [ngClass]="{'cat__apps__messaging__tab--selected': activeForms.id===form.id}">
                                    <div class="cat__apps__messaging__tab__name dropdown-more-profile" style="width:75%;">{{form.name}}</div>
                                   <div class="badge badge-success mr-2 mb-2" *ngIf="form.category == 'staffsupplyCount'">Staff</div>
                                    <div class="badge badge-primary mr-2 mb-2" *ngIf="form.category == 'patientsupplyCount'">Patient</div>
                                   
                                </div>
                            </a>
                        </div>
                        <div *ngIf="!(forms | formSearch:messageGroups.value).length">
                            <a href="javascript: void(0);">
                                <div class="cat__apps__messaging__tab  messages-tab">
                                    <div class="cat__apps__messaging__tab__name">No Forms available</div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
    <div class="col-lg-8 send-forms">
        <section class="card">
            <div class="card-header">
                <span class="cat__core__title">
                     <strong [hidden]="!allowRecipientRoles || !(forms | formSearch:messageGroups.value).length">Select Recipient(s)</strong>
                     <strong [hidden]="(allowRecipientRoles || !(forms | formSearch:messageGroups.value).length)||!patientAssociation">Select Associated Patient</strong>
                     <strong [hidden]="(forms | formSearch:messageGroups.value).length">No Forms Available</strong>
<strong [hidden]="patientAssociation || allowRecipientRoles">Supply Preview</strong>

                </span>
            </div>
            <div class="card-block" [hidden]="!(forms | formSearch:messageGroups.value).length">
                <form [formGroup]="structuredFormSend" id="structuredFormSend" class="structuredFormSend">
                    <!-- <div class="card-block"> -->
                    <!-- <div class="cat__core__card-sidebar"> -->
                    <!-- <div class="cat__apps__messaging__header"> -->
                    <div class="row select-rec" [hidden]="!allowRecipientRoles">
                        <div class="col-lg-10">
                            <select class="form-control select2" formControlName="tenantUsers" id="tenantUsers" multiple> 
                                                   
                                    <option *ngFor="let user of tenantUsers" value="{{(user.tag_name ?'tag-'+user.id :(user.caregiver_userid ? user.userId+'--'+user.caregiver_userid : user.userId))}}">
                                            {{ (user.tag_name ? user.tag_name + ' [User Tag]' : (user.caregiver_userid ? (user.caregiver_dob_formatted ? (user.caregiver_displayname + ' - ' + user.caregiver_dob_formatted) : user.caregiver_displayname ) + ' (' + user.displayname + ')' : (user.dob ? user.displayname + ' - ' + user.dob_formatted : user.displayname))) }} 
                                    </option>
                                            </select>
                        </div>
                        <div class="col-lg-2">
                            <button type="button" class="btn btn-primary" (click)="sendForm()">Send</button>
                        </div>
                    </div>
                    <div class="form-group row select-rec" [hidden]="!allowRecipientRoles">
                        <div class="col-lg-10">
                            <textarea formControlName="message" class="form-control" id="message" placeholder="Enter your message"></textarea>
                            <!-- <div class="alert alert-danger" *ngIf="structuredFormSend.controls['message'].value.length > 500 ">
                                                This message is too long. Please shorten your message.
                                        </div> -->
                        </div>
                    </div>
                    <div class="row select-rec" [hidden]="allowRecipientRoles || !patientAssociation">
                        <div class="col-lg-11">
                            <select class="form-control select2" formControlName="assosiatedPatients" id="assosiatedPatients" (change)="selectAssosiatePatient()"> 
                                                    <option></option>
                                                    <option *ngFor="let assosiatedPatient of assosiatedPatients" value="{{assosiatedPatient.userId}}"> 
                                                            {{(assosiatedPatient.caregiver_displayname)?((assosiatedPatient.dob_formatted)?(assosiatedPatient.displayname+ ' - ' + assosiatedPatient.dob_formatted+' ('+ assosiatedPatient.caregiver_displayname +')'):(assosiatedPatient.displayname+' ('+ assosiatedPatient.caregiver_displayname +')') ):((assosiatedPatient.dob_formatted)?(assosiatedPatient.displayname+ ' - ' + assosiatedPatient.dob_formatted):assosiatedPatient.displayname)}}
                                                            {{(assosiatedPatient.passwordStatus == 'true' ? " (Enrolled)" : " (Virtual)")}}
                                                    </option>
                                            </select>
                        </div>
                        <button type="button" (click)="openAssocpopup()">                                        
                                        <i class="fa fa-user-plus addassocpt"></i>
                                    </button>
                    </div>
                    <!-- </div> -->
                    <!-- </div> -->
                    <!-- </div> -->
                </form>
                <!-- <div class="form-actions">
                    <button  type="button"  class="btn btn-primary" (click)="sendForm()">Send</button>
                    <button type="button" (click)="cancel()"  class="btn btn-default">Cancel</button>
                </div> -->
                <div class="card-block staff-hide-div" *ngIf="!allowRecipientRoles && !selectedAssosiatePatient && patientAssociation">
                    <span class="chatwith-modal-tip">  
                            <img  src="./assets/modules/dummy-assets/common/img/chatwith-tip.png">                            
                            <span  class="modal-footer-text-sign">Please choose associated patient to view form.</span>
                    </span>
                </div>
                <div class="structured-form" [ngClass]="{'block-form-cursor':allowRecipientRoles}" [hidden]="!allowRecipientRoles && !selectedAssosiatePatient && patientAssociation">
                    <h3 class="cat__core__title" *ngIf="allowRecipientRoles"><strong>Supply Preview</strong></h3>
                    <!-- <div class="structured-form-modal" *ngIf="!allowRecipientRoles && !selectedAssosiatePatient">Please choose assosiated patient to view form</div> -->
                    <div class="structured-form-modal" *ngIf="(!allowRecipientRoles && selectedAssosiatePatient) || allowRecipientRoles || !patientAssociation">
                        <iframe *ngIf="formContent" [ngClass]="{'block-form-mouse':allowRecipientRoles}" onload="$('html, body').animate({ scrollTop: 0 },'slow');$('.structured-form-modal').scrollTop(0);" allowTransparency="true" class="structured-form-dekstop" id="structured-form-dekstop" frameborder="0" scrolling="no" style="width:100%;border:none"
                            [src]="formContent"></iframe>
                    </div>
                </div>
            </div>
        </section>
    </div>
</section>
<!-- END: tables/datatables -->
<!-- New assoc patient starts -->

<div class="modal fade forward-modal associated-patient" id="assocModal" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <form class="form-horizontal" (ngSubmit)="createNewAssoc(f)" [formGroup]="newPatient" novalidate #f="ngForm">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="exampleModalLabel">Create Associated Patient</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
                </button>
                </div>
                <div class="modal-body">

                    <section>

                        <div class="invoice-block">
                            <div class="form-group" *ngIf="isEmail">
                                <label class="form-label">Patient Email<span class="red-color" *ngIf="isReqEmail">*</span></label>
                                <input type="text" class="form-control" id="pemail" name="pemail" placeholder="Patient Email" [formControl]="newPatient.controls['pemail']" [(ngModel)]="assocpdata.pemail">
                                <div class="alert alert-danger" *ngIf="newPatient.controls.pemail.errors && newPatient.controls.pemail.errors.required && (newPatient.controls.pemail.dirty || newPatient.controls.pemail.touched || f.submitted)">
                                    Email Address cannot be empty.
                                </div>
                                <div class="alert alert-danger" *ngIf="newPatient.controls.pemail.errors && newPatient.controls.pemail.errors.pattern && (newPatient.controls.pemail.dirty || newPatient.controls.pemail.touched || f.submitted)">
                                    Please enter a valid email address.
                                </div>
                            </div>
                            <div class="form-group" *ngIf="isFirstName">
                                <label class="form-label">First Name<span class="red-color" *ngIf="isReqFirstName">*</span></label>
                                <input type="text" class="form-control" id="firstname" name="pfname" placeholder="Patient First Name" [formControl]="newPatient.controls['pfname']" [(ngModel)]="assocpdata.pfname">
                                <div class="alert alert-danger" *ngIf="!newPatient.controls.pfname.valid && (newPatient.controls.pfname.dirty || newPatient.controls.pfname.touched || f.submitted)">
                                    First name cannot be empty.
                                </div>
                            </div>

                            <div class="form-group" *ngIf="isLastName">
                                <label class="form-label">Last Name<span class="red-color" *ngIf="isReqLastName">*</span></label>
                                <input type="text" class="form-control" id="lastname" name="plname" placeholder="Patient Last Name" [formControl]="newPatient.controls['plname']" [(ngModel)]="assocpdata.plname">
                                <div class="alert alert-danger" *ngIf="!newPatient.controls.plname.valid && (newPatient.controls.plname.dirty || newPatient.controls.plname.touched || f.submitted)">
                                    Last name cannot be empty.
                                </div>
                            </div>
                            <div class="form-group" *ngIf="iscellNumber">
                                <label class="form-label">Cell Number<span class="red-color" *ngIf="isReqcellNumber">*</span></label>
                                <input type="text" class="form-control" id="us-phone-mask-input" name="pcellno" placeholder="Patient Cell Number" [formControl]="newPatient.controls['pcellno']" [(ngModel)]="assocpdata.pcellno">
                                <div class="alert alert-danger" *ngIf="!newPatient.controls.pcellno.valid && (newPatient.controls.pcellno.dirty || newPatient.controls.pcellno.touched || f.submitted)">
                                    Cell number cannot be empty.
                                </div>
                            </div>
                            <div class="form-group row" id="dob-parent" [hidden]="!isDob">
                                <div class="col-md-3"><label class="label-doba" for="">Date Of Birth<span class="red-color" *ngIf="isReqDob">*</span></label></div>
                                <div class="col-md-9">
                                    <input type="text" class="form-control" id="dob-date-picker" required="">
                                    <div *ngIf="isDob">
                                        <input type="hidden" class="form-control" id="dday" name="dday" [formControl]="newPatient.controls['dday']" [(ngModel)]="assocpdata.dday">
                                        <input type="hidden" class="form-control" id="dmonth" name="dmonth" [formControl]="newPatient.controls['dmonth']" [(ngModel)]="assocpdata.dmonth">
                                        <input type="hidden" class="form-control" id="dyear" name="dyear" [formControl]="newPatient.controls['dyear']" [(ngModel)]="assocpdata.dyear">
                                        <!-- <div class="alert alert-danger" *ngIf="(!newPatient.controls.dyear.valid || !newPatient.controls.dmonth.valid || !newPatient.controls.dday.valid) && (newPatient.controls.dyear.dirty || newPatient.controls.dyear.touched || f.submitted)"> -->
                                        <div class="alert alert-danger" *ngIf="(!newPatient.controls.dyear.valid || !newPatient.controls.dmonth.valid || !newPatient.controls.dday.valid) && (f.submitted)">
                                            Date of Birth cannot be empty.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group" *ngIf="isZipCode">
                                <label class="form-label">Zip Code<span class="red-color" *ngIf="isReqZipCode">*</span></label>
                                <input type="text" class="form-control" id="zipcode" name="zipcode" placeholder="Zip Code" [formControl]="newPatient.controls['zipcode']" [(ngModel)]="assocpdata.zipcode">
                                <div class="alert alert-danger" *ngIf="!newPatient.controls.zipcode.valid && (newPatient.controls.zipcode.dirty || newPatient.controls.zipcode.touched || f.submitted)">
                                    Zipcode cannot be empty.
                                </div>
                            </div>

                        </div>


                    </section>


                </div>
                <div class="modal-footer">
                    <span class="chatwith-modal-tip">  
                                <img src="./assets/modules/dummy-assets/common/img/chatwith-tip.png"/>                            
                                <span class="modal-footer-text"><span class="red-color">*</span>Indicates required information.</span>
                    </span>
                    <button style="display:none;" type="reset" id="resetpform">Reset</button>
                    <button class="btn btn-success">Save</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                </div>

            </div>
        </form>
    </div>
</div>

<!-- New assoc patient ends -->