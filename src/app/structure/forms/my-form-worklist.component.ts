import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Router } from '@angular/router';
import { StructureService } from '../structure.service';
import { FormsService } from './forms.service';
import { SharedService } from '../shared/sharedServices';
import { FormpPipe } from './formp.pipe';
import { ToolTipService } from '../tool-tip.service';
import { ISubscription } from "rxjs/Subscription";
import { Subject } from 'rxjs';
import { isBlank } from '../../utils/utils';
import { ControlType } from '../shared/advance-search/control-type';
import { NGXLogger } from 'ngx-logger';
import { APIs } from 'app/constants/apis';
import { CONSTANTS, DateRanges, FormSendMode, IntegrationType, LISTING_PAGE_DATERANGE_OPTIONS, Status, UserGroup, UserRoles } from 'app/constants/constants';
import { FormControl } from '@angular/forms';
import { Guid } from "guid-typescript";
import { Store, StoreService } from '../shared/storeService';
import { Subscription } from 'rxjs';
import { StaticDataService } from '../static-data.service';
import { CombineLatestOperator } from 'rxjs/operators/combineLatest';

const jstz = require('jstz');
declare const $: any;
declare const swal: any;
const timezone = jstz.determine();
declare const NProgress: any;
let moment = require('moment/moment');

@Component({
    selector: 'list_form_data',
    templateUrl: './my-form-worklist.html'
})
// TODO: Need to Revamp the entire component. Contains lots of duplicate codes, jquery functions etc.
export class FormsWorkListingComponent implements OnInit, OnDestroy {
    formsUrl: any;
    dTable;
    filterType;
    userData: any = '';
    reminderDetailsLoaded: boolean;
    documentTagDetails;
    reminderDetails: any = [];
    reminderDetailsLength=0;
    draftHistoryLoaded: boolean;
    draftHistoryData: any = [];
    draftHistoryDataLength=0;
    structureFormContent: any;
    strucuredForms: any = [];
    activeStrucuredForms;
    dataLoadingMsg = true;
    dataTableLoading = false;
    isActive: any;
    isActiveArchive: boolean = false;
    isActivePending: boolean = false;
    completedCount;
    draftCount;
    pendingCount;
    caregiverfname;
    caregiverlname;
    isScheduled = 'all';
    showResendModal =  false;
    contentType = CONSTANTS.contentType.forms.toLowerCase();
    statusChangedFormName:any;
    reviewStatusChangeMessage:any;
    statusChangedTime:any;
    totalCt;
    userRole;
    selectedfilterval: any;
    archiveCount;
    strucuredFormsPending: any;
    strucuredFormsCopy: any;
    strucuredFormsupdated: any;
    strucuredFormsPendingArchived: any;
    strucuredFormsArchive: any;
    pendingall: any;
    pendingallArchived: any;
    actualstrucuredForms: any;
    reverseFlag: any;
    flagcheck: any;
    userDataN:any;
    datam;
    userDataConfig;
    filterValues;
    previlages;
    includeOtherForms;
    crossTenantChangeSubscriber: any;
    onPolling: boolean = false;
    clickedTab = "";
    integrationDetails;
    userConfig;
    siteIds: any = '0';
    hideSiteSelection: boolean;
    isApiError: boolean;
    eventsSubject: Subject<void> = new Subject<void>();
    dynamicControls: ControlType<string>[] | null = [];
    advanceSearchInput: any = {};
    resetAdvanceSearchForm = false;
    showHistory: Boolean = false;
    showDraftHistory: Boolean = false;
    showHistoryModal: Boolean = false;
    public showSuccessMessageSubscription: ISubscription;
    resendPatientId;
    dateRangeFilterOptions = LISTING_PAGE_DATERANGE_OPTIONS;
    dateRange = new FormControl;
    dateRangeStoreKey = Store.DATE_RANGE_FILTER_FORMS;
    constants = CONSTANTS;
    archivedBy = false;
    isTableLoaded = {
        completed: false,
        pending: false,
        archived: false,
        drafts: false
    };
    isPartnerUser = false;
    formStatus = Status;
    private socketEventSubscriptions: Subscription[] = [];
    labelSite = '';
    labelSiteFilter = '';
    showFilterAppliedMessage = false;
    dateRangeType = DateRanges.LAST_THIRTY_DAYS;
    constructor(
        public _structureService: StructureService,
        private router: Router,
        private _ToolTipService: ToolTipService,
        private _formsService: FormsService,
        elementRef: ElementRef,
        renderer: Renderer,
        private formpPipe: FormpPipe,
        private _sharedService: SharedService,
        private ngxLogger: NGXLogger,
        private storeService: StoreService,
        private staticDataService: StaticDataService
    ) {
        this.labelSite = this._ToolTipService.getTranslateData(this.staticDataService.getSiteLabelBasedOnMultiAdmission('SITE'));
        this.labelSiteFilter = this.staticDataService.getSiteLabelBasedOnMultiAdmission('FILTER_BY_SITES');
        renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
            let IsEnterClicked = false;
            $(document).keypress((event)=> {
                if (event.keyCode == 13) {
                    IsEnterClicked = true;           
                }
              }); 
            if (!this.dataTableLoading) {

                if(event.target.parentElement.className && event.target.parentElement.className.includes("reviewcheckclass")){
                    
                    this.userDataN = JSON.parse(this._structureService.userDetails);
                        
                        this.statusChangedFormName= $(event.target).data("form_name");
                        this.statusChangedTime=new Date();
                        const form_id=$(event.target).data("form_id");
                        const submission_id=$(event.target).data("submission_id");
                        let status: number;
                        if($(event.target).prop("checked")){
                          status = 1;
                        }else {
                          status = 2;
                        }
                         
                        const formData = {form_id:form_id,submission_id:submission_id,status:status,user_id: this.userDataN.userId}
                       
                        this._formsService.formReviewStatusChange(formData).then((data) => {
                          if(status==1) this.reviewStatusChangeMessage= "Form "+this.statusChangedFormName+"  has been marked as reviewed";
                          else this.reviewStatusChangeMessage="Review is canceled from the Form "+this.statusChangedFormName;
                          let activity=status === 1 ? "reviewed":"reviewed canceled";
                          let activityData = {
                            activityName: "Form marked as " +activity,
                            activityType: "forms",
                            activityDescription: this.reviewStatusChangeMessage+" by "+this.userDataN.displayName+" on "+this.statusChangedTime+"(form id:"+form_id+",form submission id:"+submission_id+")"
                          };
                          this._structureService.trackActivity(activityData);
                          /**Polling for live update**/
                           var polldata={form_id:form_id,submission_id:submission_id,status:status,tenantid:this.userData.tenantId,user_id:this.userDataN.userId};
                           this._structureService.socket.emit("reviewpolling", polldata);
                           /***************************/
                          return false;
                         });
                }   
              else if (event.target.id === 'view' || event.target.parentElement.id === 'view') {
                    this.viewStrucuturedForm();
                }
                else if (event.target.id == 'download' || event.target.parentElement.id == 'download') {
                    this.ngxLogger.log('form details', this.activeStrucuredForms);
                    if (this.isActive == 'draft' || this.isActive == 'pending') {
                        let formDetails = {
                            "staffId": this.activeStrucuredForms.from_id,
                            "formId": this.activeStrucuredForms.form_id,
                            "submissionId": this.activeStrucuredForms.form_submission_id,
                            "tenantId": this.userData.tenantId,
                            "type": this.isActive
                       }
                       if(this.isActive == 'draft') {
                            formDetails["staffId"] = this.activeStrucuredForms.sender_id;
                            if (this.activeStrucuredForms.form_type == "Staff Facing" || this.activeStrucuredForms.facing_new == 2) {
                                if (this.activeStrucuredForms.patientAssociation == true) {
                                     formDetails["patientId"] = this.activeStrucuredForms.caregiver != null ? this.activeStrucuredForms.caregiver : this.activeStrucuredForms.patient_id;
                                }
                           } else {
                                formDetails["patientId"] = this.activeStrucuredForms.caregiver != null ? this.activeStrucuredForms.caregiver : this.activeStrucuredForms.patient_id;
                           }
                       } else {
                            formDetails['sendId'] = this.activeStrucuredForms.sent_id;
                            formDetails['submissionId'] = this.activeStrucuredForms.form_submission_id != null  ?
                                    this.activeStrucuredForms.form_submission_id : this.activeStrucuredForms.staff_submission_id;
                            if (this.activeStrucuredForms.form_type == "Patient Facing") {
                                 formDetails["patientId"] = this.activeStrucuredForms.recipient_id;
                            }
                            if (this.activeStrucuredForms.facing_new == 2) {
                                 if (this.activeStrucuredForms.caregiver_displayname != null &&
                                      this.activeStrucuredForms.caregiver_displayname != "" &&
                                      this.activeStrucuredForms.caregiver_displayname != "null" &&
                                      this.activeStrucuredForms.caregiver_displayname != "Administrator" &&
                                      this.activeStrucuredForms.createdUser != this.activeStrucuredForms.caregiver_displayname) {
                                      formDetails["patientId"] = this.activeStrucuredForms.caregiver_userid;
                                 }
                            }
                            if (this.activeStrucuredForms.form_type == "Staff Facing" && this.activeStrucuredForms.facing_new != 2) {
                                if (this.activeStrucuredForms.patientName != null && this.activeStrucuredForms.patientName != "" && this.activeStrucuredForms.patientName != " ") {
                                      formDetails["patientId"] = this.activeStrucuredForms.patient_id;
                                 }
                            }
                       }
                        this._formsService.generateDraftPendingForm(formDetails).then((result) => {
                            this.ngxLogger.log('pdf result', result);
                            if (result['success']) {
                                let fileName = result['data'].fileName;
                                let newWindow: any = window.open(this._structureService.serverBaseUrl + APIs.loaderImageUrl);
                                const fileUrl = this._structureService.apiBaseUrl + APIs.downloadFormDetails+'?filetoken=' + fileName;
                                newWindow.location = fileUrl;
                                let activityData = {
                                    activityName: "Download form from " + this.isActive + ' bucket',
                                    activityType: "Download forms",
                                    activityDescription: this.userData.displayName + " download the form from the " + this.isActive  + " bucket. Details are " + JSON.stringify(formDetails) + " and results are "+JSON.stringify(result)+" from my form worklist"
                               };
                               this._structureService.trackActivity(activityData);
                            } else {
                                this._structureService.notifyMessage({
                                    messge: this._ToolTipService.getTranslateData(
                                        'ERROR_MESSAGES.COMMON_ERROR_MSG'
                                    ),
                                    delay: 1000,
                                    type: 'danger'
                                });
                                let activityData = {
                                    activityName: "Download form from " + this.isActive + ' bucket',
                                    activityType: "Download forms",
                                    activityDescription: "Error occured while " + this.userData.displayName + " download the form from the " + this.isActive  + " bucket. Details are " + JSON.stringify(formDetails) + " and results are "+JSON.stringify(result)+" from my form worklist"
                               };
                               this._structureService.trackActivity(activityData);
                            }
                        },
                            () => {
                                this._structureService.notifyMessage({
                                    messge: this._ToolTipService.getTranslateData(
                                        'ERROR_MESSAGES.COMMON_ERROR_MSG'
                                    ),
                                    delay: 1000,
                                    type: 'danger'
                                });
                            });
                    } else {
                        this.getPdfTaggedForm();
                    }
                } else if (event.target.id == 'archive-form' || event.target.parentElement.id == 'archive-form') {
                    if(this.isActive == 'draft'){
                        const txt ="You are going to cancel this draft";
                        swal({
                            title: "Are you sure?",
                            text: txt,
                            type: "warning",
                            showCancelButton: true,
                            cancelButtonClass: "btn-default",
                            confirmButtonClass: "btn-warning",
                            confirmButtonText: "Ok",
                            closeOnConfirm: true
                        }, (confirm) => {
                            if(IsEnterClicked){
                              IsEnterClicked=false;
                                swal.close();   
                                return false;
                            }
                            if(confirm) {   
                                $('[id*="archive-form"]').bind('click', false);
                                this.archiveSubmitedMyForm();
                            }
                        })
                    } else {
                        
                        if(this.userDataConfig.enable_sftp_integration == false) {
                            const rowDetails = this.activeStrucuredForms;
                            const patientID = !isBlank(rowDetails['caregiver_userid']) ? rowDetails['caregiver_userid'] : rowDetails['from_id'];
                            const checkIntegrationInFromWorklist: any = { "form_id": this.activeStrucuredForms.form_id, "tenant_id": this.userData.tenantId, "patient_id": patientID, "staff_id": this.userData.userId,
                                "admissionId": this._structureService.isMultiAdmissionsEnabled ? rowDetails['admissionId'] : undefined,
                                "action" : IntegrationType.FORM_ARCHIVE
                             };
                             this._formsService.checkIntegration(checkIntegrationInFromWorklist).subscribe((data) => {
                                if (data.success) {
                                    setTimeout(() => {
                                        this.confirmAndArchive();
                                    }, 300);
                                }
                            });
                        } else {
                            this.confirmAndArchive();
                        }
                    }
                } else if (event.target.id == 'cancel-form' || event.target.parentElement.id == 'cancel-form') {
                    this.cancelPendingForm();
                } else if (event.target.id == 'restore-forms' || event.target.parentElement.id == 'restore-forms') {
                    var self = this;
                    swal({
                      title: "Are you sure?",
                      text: "You are going to restore this form",
                      type: "warning",
                      showCancelButton: true,
                      cancelButtonClass: "btn-default",
                      confirmButtonClass: "btn-warning",
                      confirmButtonText: "Ok",
                      closeOnConfirm: true
                    },
                    (confirm) => {
                        if(IsEnterClicked){
                          IsEnterClicked=false;
                            swal.close();   
                            return false;
                        }
                        if(confirm) {
                        $('[id*="restore-forms"]').bind('click', false);
                        self.restoreForms();
                      }
                    }); 
                   
                } else if (event.target.id == 'resend-form' || event.target.parentElement.id == 'resend-form') {

                    this.resendForm();
                } else if (event.target.id == 'resend-completed-forms' || event.target.parentElement.id == 'resend-completed-forms') {
                    this.popUpRecipientsList();
                } else if (event.target.id == 'edit-pform' || event.target.parentElement.id == 'edit-pform') {
                    $('[id*="edit-pform"]').bind('click', false);
                    this.editForm();
                } else if (event.target.id == 'allow_edit' || event.target.parentElement.id == 'allow_edit') {
                    this.allowEdit();
                } else if (event.target.id == 'move_fc' || event.target.parentElement.id == 'move_fc') {

                    this.moveToFC();
                } else if (event.target.id == 'submit-forms' || event.target.parentElement.id == 'submit-forms') {
                    this.submitForms();
                } else if (event.target.id === 'mark-complete' || event.target.parentElement.id === 'mark-complete') {
                    this._structureService.showAlertMessagePopup({
                        title: this._ToolTipService.getTranslateData('MESSAGES.CONFIRM_MESSAGE'),
                        text: this._ToolTipService.getTranslateDataWithParam('MESSAGES.YOU_ARE_GOING_TO_COMPLETE_THE_SELECTED_FORM', {'formName': this.activeStrucuredForms.form_name})
                   }).then((confirm) => {
                        if (confirm) {
                            this.dataLoadingMsg = true;
                            this._formsService.markFromAsComplete(this.activeStrucuredForms).then((res) => {
                                this.dataLoadingMsg = false;
                                if (res) {
                                    this.populateData(this.isActive, this.isScheduled, 'refresh');
                                    this.getMyWorklistCount();
                                }
                            });
                        }
                    });
                }
                if(event.target.id == 'advance-searchB'){
                    this._structureService.hideAdvanceSearchArea = !this._structureService.hideAdvanceSearchArea;
                    const state = this._structureService.hideAdvanceSearchArea == true ? 'close' : 'open';
                    var activityData = {
                         activityName: `${state} advance search in form worklist`,
                         activityType: 'Advance Search',
                         activityDescription: `${this.userData.displayName} ${state} advance search in my form worklist`,
                    };
                    this._structureService.trackActivity(activityData);
                }
                if(event.target.id == 'searchB')
                {
                  var value = $('#form-list-dt_wrapper #form-list-dt_filter label input').val();
                   if(value)
                    {
                       this.advanceSearchInput = {};
                       this._structureService.closeAdvanceSearch(true);
                       this.resetAdvanceSearchForm = true;
                       this.dynamicControls = this._structureService.setAdvanceSearchControls('myform',this.isActive);
                       this.dTable.search(value).draw();
                    }
                    else
                    {
                      this.dTable.search("").draw();
                    }
                }
                if(event.target.id == 'resetB')
                {
                  this.dTable.search("").draw();
                  $(".searchB").prop('disabled', true);
                }
                var x = document.getElementsByClassName("btn-warning");
                if(x.length > 0) {
                    x[0].setAttribute("id", "unique_cnfrm_btn");
                }            
            }
        });
        this.crossTenantChangeSubscriber = this._sharedService.crossTenantChange.subscribe(
            (onInboxData) => {
                this.ngOnInit();
            }
        );
        this._structureService.hideAdvanceSearchArea = true;
    }
    validateEmail(email) {
        var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
        if(re.test(email) && email.indexOf('unknown_') == -1){      
          return true;
        } else {
          return false;
        }
      }
    integrationSwal(txt,title, IsEnterClicked) {
        swal({
            title: title,
            text: txt,
            type: 'error',
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            confirmButtonText: "Go Back",
            cancelButtonText: "Continue Anyway",
            closeOnConfirm: true
        }, (confirm) => {
            if (IsEnterClicked) {
                IsEnterClicked = false;
                swal.close();
                return false;
            }
            if (!confirm) {
                this.archiveSubmitedMyForm();
            }
        })
    }
    confirmAndArchive(){
        var IsEnterClicked=false;
        $(document).keypress((event)=> {
            if (event.keyCode == 13) {
                IsEnterClicked=true;           
            }
        }); 
        var txt ="You are going to archive this form";
        swal({
            title: "Are you sure?",
            text: txt,
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            confirmButtonText: "Ok",
            closeOnConfirm: true
        }, (confirm) => {
            if(IsEnterClicked){
              IsEnterClicked=false;
                swal.close();   
                return false;
            }
            if(confirm) {   
                $('[id*="archive-form"]').bind('click', false);
                this.archiveSubmitedMyForm();
            }
        })
    }

    ngOnInit() {
        this.userConfig = this._structureService.getUserdata().config; 
        this.previlages = [];
        this.isApiError = false;
        this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
        this.userDataN =  JSON.parse(this._structureService.userDetails);
        let manageTenants = this.userData.privileges;
        manageTenants = typeof (manageTenants) === 'undefined' ? this._structureService.getCookie('userPrivileges') : manageTenants;
        manageTenants = manageTenants.split(',');
        this._sharedService.FormsPage = "/forms/worklist";
        for (var i = 0; i < manageTenants.length; i++) {
            this.previlages[manageTenants[i]] = true;
        }

        this.filterValues = [

        ]
        if(this.userDataConfig.enable_collaborate_edit==1 && this.userDataN.group != UserGroup.PATIENT && this.userDataN.group != UserGroup.PARTNER && this.previlages.FillStructuredForms ) {
         
            $.notify({'message':'See All Forms Worklist for the Collaborative Draft Category for work in progress Draft Forms shared with all Staff members'},
            {
                allow_dismiss: true,
                delay: 0
            });
        }
        this._sharedService.formFilterValue == 'all';
        this.reverseFlag = true;
        this.flagcheck = 1;
        this.selectedfilterval = 'all';
        $('#form-list-dt').on('draw.dt', () => {
            let page = 'form-tag-definitions';
            $(".default-Scheduled").tooltip({
                title: this._ToolTipService.getToolTip(page, 'FORM00007')
            });
            $(".review-tooltip").tooltip({
                title: this._ToolTipService.getToolTip(page, 'FORM00010')
            });
        })
        this.activeStrucuredForms = null;
        
        this.userData = JSON.parse(this._structureService.userDetails);
        this.isPartnerUser = +this.userData.group === UserGroup.PARTNER;
        this.formStatus = Status;
        let type = (this._sharedService.myFormsType && this._sharedService.myFormsType != "") ? this._sharedService.myFormsType : 'COMPLETED';
        if(this._structureService.allowEditFormSuccess == true)  type='COMPLETED';
        
        $('.ci-form-filter-dropdown select').select2({
            minimumResultsForSearch: -1
        });
        window.addEventListener("message", function (event) {
            if (!isBlank(event.data)) {
                var height = (event.data).split("=");
                if(height.length&&height[0]=="scrollTopformsubmit"){
                    $(".structured-form-modal").scrollTop(0);
                } else if(height.length&&height[0] !== "gotocreatesendform" && height.length&&height[0] !=="autosavecalled" && height.length&&height[0] !== "autosavecalledformid"
                  && height.length&&height[0] !== "saveasdraftidtodeleted") {
                    if (typeof (parseInt(height[1])) == "number") {
                        $("#structured-form-dekstop").height(height[1] + "px")
                    } else {
                        $("#structured-form-dekstop").height("5000px")
                    }
                    setTimeout(
                        function () {
                            $('#btm-send-form-list').removeAttr('disabled');
                        }, 2000);
                }
            }
        }, false);

        $("#scheduledSelect").change((e) => {
            const filter = e.target.value;
            let key = filter.split('-');
            this.filterType = key[1];
            this.onChangescheduled(key[0]);
        });
        if (this._structureService.allowEditFormSuccess != false) {
            /*this._structureService.notifyMessage({
              messge: 'Your form has been sent to Completed',
              type: 'success',
              allow_dismiss: true,
              delay: 0
            });*/
            this._structureService.allowEditFormSuccess = false;
          }
        $('[data-toggle="tooltip"]').tooltip();
        var self = this;
        this.socketEventSubscriptions.push(
            this._structureService.subscribeSocketEvent('formPolling').subscribe((data) => {
                $("#textmessage").text("");
                $("#newloader").hide();
                if(!this.archivedBy) {
                    this.populateDataAfterChange();
                } else {
                    this.archivedBy = false;
            }
            })
        );
        this.setAdvanceSearchForm('refresh');
    }

    ngOnDestroy() {
        if (this.crossTenantChangeSubscriber) {
            this.crossTenantChangeSubscriber.unsubscribe();
        }
        if (this.showSuccessMessageSubscription) {
            this.showSuccessMessageSubscription.unsubscribe();
        }
        this._structureService.socket.off("formPolling");
        this._sharedService.reInitiateMessagePolling.emit({type: 'form'});
        /**Unsubscribe all the socket event subscriptions */
        this.socketEventSubscriptions.forEach(subscription => {
            if(subscription) subscription.unsubscribe();
        });
    }

    submitForms() {
        var self = this;
        this.activeStrucuredForms.page = "myFormWorlists";
        this.activeStrucuredForms.fromName = this.activeStrucuredForms.fname + ' ' + this.activeStrucuredForms.lname;
        this.activeStrucuredForms.formId = this.activeStrucuredForms.form_id;
        this.activeStrucuredForms.fromId = this.activeStrucuredForms.from_id;
        this.activeStrucuredForms.sentId = this.activeStrucuredForms.sent_id;
        this.activeStrucuredForms.formName = this.activeStrucuredForms.form_name;
        this.activeStrucuredForms.categoryvalue = 1;
        this.activeStrucuredForms.loginUserId = this.userData.userId;
        this.activeStrucuredForms.toName = this.activeStrucuredForms.createdUser;
        this.activeStrucuredForms.createdUserName = this.activeStrucuredForms.createdUser;
        this.activeStrucuredForms.fromUsername = this.activeStrucuredForms.createdUserEmail;
        if (this.activeStrucuredForms.form_type == "Staff Facing") {
            this.activeStrucuredForms.staff_facing = 1;
        } else {
            this.activeStrucuredForms.staff_facing = 0;
        }
        this.activeStrucuredForms.entryId = this.activeStrucuredForms.form_submission_id;
        var formId = this.activeStrucuredForms.form_id ? this.activeStrucuredForms.form_id : this.activeStrucuredForms.formId;
        this._formsService.checkIsFormFilled(this.activeStrucuredForms).then(function (result: any) {
            self._sharedService.updateUserInboxCounts.emit({
                getCount: true,
                type: 'forms'
            });
        });

        this._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)), 1);
        localStorage.setItem('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)));
        this.router.navigate(['/forms/view-form']);
    }

    moveToFC() {
        var IsEnterClicked=false;
        $(document).keypress((event)=> {
            if (event.keyCode == 13) {
                IsEnterClicked=true;           
            }
        }); 
        const rowDetails = this.activeStrucuredForms;
        const patientID = !isBlank(rowDetails['caregiver_userid']) ? rowDetails['caregiver_userid'] : rowDetails['from_id'];
                            
        if(this.userDataConfig.enable_sftp_integration == false) {
            const checkIntegrationInFromWorklist: any = { "form_id": this.activeStrucuredForms.form_id, "tenant_id": this.userData.tenantId, "patient_id": patientID, "staff_id": this.userData.userId,
                "admissionId": this._structureService.isMultiAdmissionsEnabled ? rowDetails['admissionId'] : undefined,
                "action": IntegrationType.SEND_TO_EHR
             };
             this._formsService.checkIntegration(checkIntegrationInFromWorklist).subscribe((data) => {
              if (data.success) {
                setTimeout(() => {
                    this.sendToCpr();
                }, 300);
              }
             });
        } else{
            this.sendToCpr();
        }
    }
    
    sendToCpr(){
       
        swal({
            title: "Are you sure?",
            text: "You are going to send this form to EHR system",
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            confirmButtonText: "Ok",
            closeOnConfirm: true
        }, (confirm) => {
            if (confirm) {
                $('[id*="move_fc"]').bind('click', false);
                let formData;
                if (this.activeStrucuredForms.patient_id != '0' && (this.isActive == 'completed' || this.isActive == 'archived') && this.activeStrucuredForms.created_date) {
                    // staff
                    formData = {
                        "userid": this.activeStrucuredForms.userid,
                        "tenantId": this.activeStrucuredForms.tenant_id,
                        "recipient_id": this.activeStrucuredForms.userid,
                        "formId": this.activeStrucuredForms.form_id,
                        "staffFacing": "true",
                        "form_submission_id": this.activeStrucuredForms.form_submission_id,
                        "form_id": this.activeStrucuredForms.form_id,
                        "form_name": this.activeStrucuredForms.form_name,
                        "patient_id": "",
                        "id": "",
                        "regenerate":"true",
                        "regenerateUser":this.userData.userId,
                        "patientUser": this.activeStrucuredForms.from_id
                    }
                } else {
                    if (this.activeStrucuredForms.facing_new == 2) {
                        formData = {
                            "userid": this.activeStrucuredForms.from_id,
                            "tenantId": this.activeStrucuredForms.tenant_id,
                            "recipient_id": this.activeStrucuredForms.userid,
                            "formId": this.activeStrucuredForms.form_id,
                            "staffFacing": null,
                            "form_submission_id": this.activeStrucuredForms.form_submission_id,
                            "form_name": this.activeStrucuredForms.form_name,
                            "id": "",
                            "regenerate":"true",
                            "regenerateUser":this.userData.userId,
                            "associate_patient_id": this.activeStrucuredForms.patient_associated_id,
                            "loggedinUserId": this.userData.userId
                        }
                    } else {
                        formData = {
                            "userid": this.activeStrucuredForms.from_id,
                            "tenantId": this.activeStrucuredForms.tenant_id,
                            "recipient_id": this.activeStrucuredForms.userid,
                            "formId": this.activeStrucuredForms.form_id,
                            "staffFacing": null,
                            "regenerate":"true",
                            "regenerateUser":this.userData.userId,
                            "form_submission_id": this.activeStrucuredForms.form_submission_id
                        }
                    }
                }

                this._formsService.filingCenterSubmittedForm(formData).then((data: any) => {
                    if(this.userData.config.form_integration_mode !='sqs' ){
                    if (data.filename) {
                        this._structureService.notifyMessage({
                            messge: 'Files for ' + this.activeStrucuredForms.form_name + ' generated successfully',
                            delay: 1000,
                            type: 'success'
                        });
                        $('[id*="move_fc"]').unbind('click', false);
                    }
                }else{
                    if (data.message == "Queued Successfully") {
                        this._structureService.notifyMessage({
                             messge: 'Integration for ' + this.activeStrucuredForms.form_name + ' queued successfully',
                             delay: 1000,
                             type: 'success'
                        });
                        $('[id*="move_fc"]').unbind('click', false);
                   }

                }
                });
            }
        });
    }
    noConfirmSendToCpr()
    {
        $('[id*="move_fc"]').bind('click', false);
        let formData;
        if (this.activeStrucuredForms.patient_id != '0' && (this.isActive == 'completed' || this.isActive == 'archived') && this.activeStrucuredForms.created_date) {
            // staff
            formData = {
                "userid": this.activeStrucuredForms.userid,
                "tenantId": this.activeStrucuredForms.tenant_id,
                "recipient_id": this.activeStrucuredForms.userid,
                "formId": this.activeStrucuredForms.form_id,
                "staffFacing": "true",
                "form_submission_id": this.activeStrucuredForms.form_submission_id,
                "form_id": this.activeStrucuredForms.form_id,
                "form_name": this.activeStrucuredForms.form_name,
                "patient_id": "",
                "id": "",
                "regenerate":"true",
                "regenerateUser":this.userData.userId,
                "patientUser": this.activeStrucuredForms.from_id
            }
        } else {
            if (this.activeStrucuredForms.facing_new == 2) {
                formData = {
                    "userid": this.activeStrucuredForms.from_id,
                    "tenantId": this.activeStrucuredForms.tenant_id,
                    "recipient_id": this.activeStrucuredForms.userid,
                    "formId": this.activeStrucuredForms.form_id,
                    "staffFacing": null,
                    "form_submission_id": this.activeStrucuredForms.form_submission_id,
                    "form_name": this.activeStrucuredForms.form_name,
                    "id": "",
                    "regenerate":"true",
                    "regenerateUser":this.userData.userId,
                    "associate_patient_id": this.activeStrucuredForms.patient_associated_id,
                    "loggedinUserId": this.userData.userId
                }
            } else {
                formData = {
                    "userid": this.activeStrucuredForms.from_id,
                    "tenantId": this.activeStrucuredForms.tenant_id,
                    "recipient_id": this.activeStrucuredForms.userid,
                    "formId": this.activeStrucuredForms.form_id,
                    "staffFacing": null,
                    "regenerate":"true",
                    "regenerateUser":this.userData.userId,
                    "form_submission_id": this.activeStrucuredForms.form_submission_id
                }
            }
        }

        this._formsService.filingCenterSubmittedForm(formData).then((data: any) => {
            if (data.filename) {
                this._structureService.notifyMessage({
                    messge: 'Files for ' + this.activeStrucuredForms.form_name + ' generated successfully',
                    delay: 1000,
                    type: 'success'
                });
                $('[id*="move_fc"]').unbind('click', false);
            }
        });
    }
    allowEdit() {
        var swal_options;
        if (this.isActive == 'completed') {
            swal_options = {
                text: `${this._ToolTipService.getTranslateData('MESSAGES.FORM_EDIT_PERMISSION_GRANT')} ${ this.activeStrucuredForms.form_name}`,
                type: CONSTANTS.notificationTypes.input,
                inputPlaceholder: this._ToolTipService.getTranslateData('PLACEHOLDERS.ENTER_OPTIONAL_MSG')
            }
        } else {
            swal_options = {
                text: `${this._ToolTipService.getTranslateData('MESSAGES.FORM_EDIT_PERMISSION_DENY')} ${this.activeStrucuredForms.form_name}`,
                type: CONSTANTS.notificationTypes.warning,
            }
        }
        this._structureService.showAlertMessagePopup(swal_options).then((confirm) => {
            // (!confirm) check is not given as it won't work for type: input, expecting any value, even empty string. 
            if (confirm !== false) {
                $('[id*="allow_edit"]').bind('click', false);
                this.allowEditConfirm(confirm);
            }
        });        
    }

    allowEditConfirm(msg = '') {
        $('[id*="allow_edit"]').bind('click', false);
        this.activeStrucuredForms.loggedinuserid=this.userData.userId;
        this._formsService.allowEditForm(this.activeStrucuredForms, msg).then((result: any) => {
            var nameeditallow = "edit-" + this.activeStrucuredForms.form_id + this.activeStrucuredForms.form_submission_id;
            this.activeStrucuredForms.allow_edit = result.allowedit;
            let activityData: any;
            if (result.allowedit == 0) {
                $("[name=" + nameeditallow + "]").attr('title', 'Allow Edit').html('<i data-edit class="icmn-pencil2"></i>');
            }
            this._formsService.notifyAfterAllowEdit(this.activeStrucuredForms, result, 'listPage', msg).then((result: any) => {
                this.getMyWorklistCount();
                this.populateData(this.isActive, this.isScheduled, false);
            });
        });
    }

    getMyWorklistCount() {
        var self = this;
        this._formsService.getAllTaggedFormsLazyCount(false, false, true, this.siteIds).then((data) => {
            this.completedCount = data['completedCount'];
            this.archiveCount = data['archivedCount'];
            this.pendingCount = data['pendingCount'];
            this.draftCount = data['draftCount'];
        }).catch(function(e) {
            if(self.isApiError == false){
                 self.isApiError = true;
                 $.notify({'message':'<strong>We are facing some technical problem to load the Worklists. Please contact your administrator</strong>'},
                 {
                      allow_dismiss: true, type: "danger", delay: 0,
                      onClose : function(){ self.isApiError = false; }
                 });
            }
         });
    }

    editForm() {
        var self = this;
        this.activeStrucuredForms.fromName = this.activeStrucuredForms.fname + ' ' + this.activeStrucuredForms.lname;
        this.activeStrucuredForms.fromName = this.activeStrucuredForms.fname + ' ' + this.activeStrucuredForms.lname;
        this.activeStrucuredForms.formId = this.activeStrucuredForms.form_id;
        this.activeStrucuredForms.fromId = this.activeStrucuredForms.from_id;
        this.activeStrucuredForms.sentId = this.activeStrucuredForms.sent_id;
        this.activeStrucuredForms.formName = this.activeStrucuredForms.form_name;
        this.activeStrucuredForms.categoryvalue = 1;
        this.activeStrucuredForms.toName = this.activeStrucuredForms.createdUser;
        this.activeStrucuredForms.createdUserName = this.activeStrucuredForms.createdUser;
        this.activeStrucuredForms.fromUsername = this.activeStrucuredForms.createdUserEmail;
        if (this.activeStrucuredForms.form_type == "Staff Facing") {
            this.activeStrucuredForms.staff_facing = 1;
        } else {
            this.activeStrucuredForms.staff_facing = 0;
        }
        this.activeStrucuredForms.page = "myFormWorlists";
        this.activeStrucuredForms.entryId = this.activeStrucuredForms.form_submission_id;

        var formId = this.activeStrucuredForms.form_id ? this.activeStrucuredForms.form_id : this.activeStrucuredForms.formId;
        this.activeStrucuredForms.loginUserId = this.userData.userId;
        this._formsService.checkIsFormFilled(this.activeStrucuredForms).then(function (result: any) {
            self._sharedService.updateUserInboxCounts.emit({
                getCount: true,
                type: 'forms'
            });
        });
        this._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)), 1);
        localStorage.setItem('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)));
        if (this.activeStrucuredForms.allow_edit && this.activeStrucuredForms.allow_edit == 1) {

            this.router.navigate(['forms/edit-form/' + formId + '/' + this.activeStrucuredForms.form_submission_id]);
        } else {
            this.viewForm(this.activeStrucuredForms);

        }
    }

    onChangescheduled(val) {
        this.selectedfilterval = val;
        if (this.selectedfilterval == "all") {
            this.changeFormData('ALL');
        }
        if (this.selectedfilterval == "yes") {
            this.changeFormData('YES');
        }
        if (this.selectedfilterval == "no") {
            this.changeFormData('NO');
        }
        if (this.isActive == "pending") {
            $(this.dTable.column(9).header()).text('Sent On');
        }
    }
    /**
     * Function to close and open Choose Recipient(s) modal
     */
    popUpRecipientsList() {
        this.resendPatientId = this._formsService.getPatientIdForResendForms(this.activeStrucuredForms);
        this.showResendModal = !this.showResendModal;
    }

    /**
     * Function to resend appless link to selected recipients
     * @param recipients Recipients list choose from the model as array of objects
     */
     resendFormToRecipients(recipients) {
        this._formsService.resendFormToRecipients(recipients, this.activeStrucuredForms);
    }

    resendForm() {
        var message = this.activeStrucuredForms.message
        var selectedRecipients = [];
        var selectedRecipientsPolling = [];
        var formSendMode = '';
        if(this.userData.config.enable_appless_model == 1 && this.activeStrucuredForms.applessMode == '1') {
            formSendMode = FormSendMode.APPLESS;
        }              
        var practitioner = 0;
        if (this.activeStrucuredForms.facing_new == 2) {
            practitioner = 1;
        }
        var practitionerAssociatePatient = [];
        if (this.activeStrucuredForms.facing_new == 2) {
            practitionerAssociatePatient.push(this.activeStrucuredForms.caregiver_userid);
        }

        if (this.activeStrucuredForms.caregiver_userid) {
            selectedRecipients.push(this.activeStrucuredForms.recipient_id + '--' + this.activeStrucuredForms.caregiver_userid);
        } else {
            selectedRecipients.push(this.activeStrucuredForms.recipient_id);
        }
        var data = {
            "form_id": this.activeStrucuredForms.form_id,
            "recipients": selectedRecipients,
            "userId": this.activeStrucuredForms.from_id,
            "message": message,
            "previousSendId": this.activeStrucuredForms.sent_id,
            "resend": true,
            "practitioner": practitioner,
            "practitionerAssociatePatient": practitionerAssociatePatient,
            "formSendMode": (formSendMode && formSendMode == FormSendMode.APPLESS)? FormSendMode.APPLESS : FormSendMode.INAPP,
            "applessMode": FormSendMode.BOTH
        };
        var deepLinking = {
            "pushType": "",
            "state": "eventmenu.forms",
            "stateParams": {},
            "tenantId": this.activeStrucuredForms.tenantId,
            "tenantName": this.activeStrucuredForms.tenantName,
            "formSendMode": (formSendMode && formSendMode == FormSendMode.APPLESS)? FormSendMode.APPLESS : FormSendMode.INAPP,
            "applessMode" : FormSendMode.BOTH,
            "sentId": ""
        };
        let pushMessage = "[Reminder] You have new form to fillout"; //"[Reminder] "+
        swal({
            title: "Are you sure?",
            text: "You are going to send reminder for this form ",
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            confirmButtonText: "Ok",
            closeOnConfirm: true
        }, () => {
            $('[id*="resend-form"]').bind('click', false);
            this._formsService.resendForms(data).then((result: any) => {
                var self = this;
                if(this.activeStrucuredForms.recipient_id != this.userData.userId) {
                    selectedRecipientsPolling.push({
                        userid: this.activeStrucuredForms.staffFacing === 1? this.activeStrucuredForms.from_id : this.activeStrucuredForms.recipient_id,
                        senderId: this.userData.userId,
                        reminderforForm:1,
                        organizationMasterId: this.userData.organizationMasterId,
                        formSendMode: (formSendMode && formSendMode == 'appless')? 'appless' : 'inapp',
                        applessMode : 'both',
                        sentId : result.sentId
                    });
                } else {
                    selectedRecipientsPolling.push({
                        userid: this.activeStrucuredForms.staffFacing === 1? this.activeStrucuredForms.from_id : this.activeStrucuredForms.recipient_id,
                        reminderforForm:1,
                        formSendMode: (formSendMode && formSendMode == 'appless')? 'appless' : 'inapp',
                        applessMode : 'both',
                        sentId : result.sentId
                    });
                }
                deepLinking.sentId = result.sentId;
                /*patient reminder for form starts*/
                var otherDatas = {
                    patientReminderTime: this.userData.config.patient_reminder_time * 1,
                    patientReminderTypes: this.userData.config.patient_reminder_types,
                    messageReplyTimeout: this.userData.config.message_reply_timeout,
                    sentId: result.sentId,
                    senderName: this.userData.displayName,
                    tenantId: this.userData.tenantId,
                    tenantName: this.userData.tenantName,
                    serverBaseUrl: this._structureService.serverBaseUrl,
                    apiVersion: this._structureService.version,
                    message: " [Reminder] You have new form to fillout", //[Reminder] 
                    formReminderType: this.userData.config.patient_reminder_checking_type,
                    environment: this._structureService.environment,
                    formSendMode: (formSendMode && formSendMode == 'appless')? 'appless' : '',
                    applessMode : (formSendMode && formSendMode == 'appless')? 'both' : ''
                }

                if(formSendMode != 'appless') {
                }
                /*patient reminder for form ends*/
                const notificationData = {
                    sourceId: CONSTANTS.notificationSource.form,
                    sourceCategoryId: CONSTANTS.notificationSourceCategory.formReSendNotification
                };
                this._structureService.sentPushNotification(selectedRecipientsPolling, 0, pushMessage, '', deepLinking, '', '', undefined, undefined, undefined, undefined, notificationData);
                if (result.status == 1) {
                    var activityDescription = this.userData.displayName + " successfully send reminder form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") to " + selectedRecipients.toString();
                    if(formSendMode && formSendMode == 'appless') {
                        activityDescription = this.userData.displayName + " successfully send reminder appless form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") to " + selectedRecipients.toString();
                    }
                    var activityData = {
                        activityName: (formSendMode && formSendMode == 'appless')? "send reminder Appless Form Success":"send reminder Form Success",
                        activityType: "structured forms",
                        activityDescription: activityDescription
                    };
                    this._structureService.trackActivity(activityData);
                    var sentMessage = "Reminder for " +this.activeStrucuredForms.form_name+ " successfully sent."
                    let notifyItems = [];
                    if (formSendMode && formSendMode == 'appless') {
                         if(this.activeStrucuredForms.username && this.validateEmail(this.activeStrucuredForms.username)){
                             notifyItems.push("Email " + this.activeStrucuredForms.username);
                         }
                         if(this.activeStrucuredForms.mobile && this.activeStrucuredForms.countryCode){
                            notifyItems.push("Mobile Number (" + this.activeStrucuredForms.countryCode + ") " + this.activeStrucuredForms.mobile);
                         }
                         if(notifyItems.length > 0) {
                            sentMessage += ' AppLess (MagicLink) sent to ';
                            if(notifyItems.length == 2) {
                                sentMessage += notifyItems.join(' and ');
                            } else {
                                sentMessage += notifyItems[0];
                            }
                         }
                    }
                    this._structureService.notifyMessage({
                        messge:  sentMessage,
                        delay: 1000,
                        type: 'success'
                    });
                } else {
                    var activityDescription = this.userData.displayName + " form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") to " + selectedRecipients.toString() + " send reminder failed";
                    if(formSendMode && formSendMode == 'appless') {
                        activityDescription = this.userData.displayName + " appless form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") to " + selectedRecipients.toString() + " send reminder failed";
                    }
                    var activityData = {
                        activityName: (formSendMode && formSendMode == 'appless')? "send reminder Appless Form Fail" : "send reminder Form Fail",
                        activityType: "structured forms",
                        activityDescription: activityDescription
                    };
                    this._structureService.trackActivity(activityData);
                    this._structureService.notifyMessage({
                        messge: this.activeStrucuredForms.form_name + ' senting failed',
                        delay: 1000
                    });
                }
                if (this.isActive == "pending") {
                    $(this.dTable.column(9).header()).text('Sent On');
                }
            });
        });
    }

    restoreForms() {
        if (this.activeStrucuredForms.created_date) {
            this.activeStrucuredForms.isActive = 'completed';
        } else {
            this.activeStrucuredForms.isActive = 'pending';
        }
        this.activeStrucuredForms.loginUserId = this.userData.userId;
        this.activeStrucuredForms.deleted_by = this.userData.userId;
        NProgress.start();
        if (this.userData.group != 3 && (this.activeStrucuredForms.facing_new != 2 || (this.activeStrucuredForms.facing_new == 2 && this.userData.userId != this.activeStrucuredForms.recipient_id))) {
            this._formsService.restoreArchivedForm(this.activeStrucuredForms).then((result: any) => {
                var activityData: any = {};
                if (result.status == '1') {
                    NProgress.done();
                    let self = this;
                    if (this.activeStrucuredForms.isActive == 'completed') {
                        this.sendPolling('completed', result.notifyUsers);
                        activityData.activityName = "Restore Completed Form";             
                        this._structureService.notifyMessage({
                            messge: 'Successfully restored the submitted form - ' + this.activeStrucuredForms.form_name,
                            delay: 1000,
                            type: 'success'
                        });
                    } else {
                        this.sendPolling('pending', result.notifyUsers);
                        activityData.activityName = "Restore Pending Form";
                        this._structureService.notifyMessage({
                            messge: 'Successfully restored the pending form - ' + this.activeStrucuredForms.form_name,
                            delay: 1000,
                            type: 'success'
                        });
                        this.pendingCount = this.pendingCount + 1;
                    }
                    activityData.activityType = "structured forms";
                    activityData.activityDescription = this.userData.displayName + " restored form " + this.activeStrucuredForms.form_name + " (formId " + this.activeStrucuredForms.form_id + ",sendId " + this.activeStrucuredForms.sent_id + ") successfully";
                    this._structureService.trackActivity(activityData);
                    this.pendingall = this.strucuredFormsPending;
                    this.archiveCount = this.archiveCount - 1;
                } else {
                    NProgress.done();
                    activityData = {
                        activityName: "Restore  Form Fail",
                        activityType: "structured forms",
                        activityDescription: this.userData.displayName + " form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") failed"
                    };
                    this._structureService.trackActivity(activityData);
                    this._structureService.notifyMessage({
                        messge: this.activeStrucuredForms.form_name + ' Restore failed',
                        delay: 1000
                    });
                }
            });
        } else {
            
            this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
            var data: any = {};
            data = this.activeStrucuredForms;
            data.loginUserId = this.userData.userId;
            data.inboxFormArchiveType = this.userDataConfig.inbox_form_archive_remove_self;
            data.formId = this.activeStrucuredForms.form_id;
            data.sentId = this.activeStrucuredForms.sent_id;
            data.deleted_by = this.userData.userId;
            this._formsService.restoreArchivedMyForm(data).then((result: any) => {
                var activityData: any = {};
                if (result.status == '1') {
                    NProgress.done();
                    let self = this;
                    if (this.activeStrucuredForms.isActive == 'completed') {
                        this.sendPolling('completed', result.notifyUsers);
                        activityData.activityName = "Restore Completed Form";
                        this._structureService.notifyMessage({
                            messge: 'Successfully restored the submitted form - ' + this.activeStrucuredForms.form_name,
                            delay: 1000,
                            type: 'success'
                        });
                    } else {
                        this.sendPolling('pending', result.notifyUsers);
                        activityData.activityName = "Restore Pending Form";
                        this._structureService.notifyMessage({
                            messge: 'Successfully restored the pending form - ' + this.activeStrucuredForms.form_name,
                            delay: 1000,
                            type: 'success'
                        });
                        this.pendingCount = this.pendingCount + 1;
                    }
                    activityData.activityType = "structured forms";
                    activityData.activityDescription = this.userData.displayName + " restored form " + this.activeStrucuredForms.form_name + " (formId " + this.activeStrucuredForms.form_id + ",sendId " + this.activeStrucuredForms.sent_id + ") successfully";
                    this._structureService.trackActivity(activityData);
                    this.pendingall = this.strucuredFormsPending;
                    this.archiveCount = this.archiveCount - 1;
                } else {
                    NProgress.done();
                    activityData = {
                        activityName: "Restore  Form Fail",
                        activityType: "structured forms",
                        activityDescription: this.userData.displayName + " form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") failed"
                    };
                    this._structureService.trackActivity(activityData);
                    this._structureService.notifyMessage({
                        messge: this.activeStrucuredForms.form_name + ' Restore  Fail',
                        delay: 1000
                    });
                }
            });
        }
    }
    cancelPendingForm() {
        this._formsService.cancelForm(this.activeStrucuredForms,'my form worklist').subscribe((res)=>{
            if (!isBlank(res) && res[this.activeStrucuredForms.id].success) {
                this._structureService.socket.emit("sendFormsToRecipients", [{
                    userid: this.activeStrucuredForms.recipient_id,
                    senderId: this.activeStrucuredForms.from_id
                }]);
                const self = this;
                this._formsService.getAllTaggedFormsLazyCount(false, false, true,this.siteIds).then((data) => {
                    this.completedCount = data['completedCount'];
                    this.archiveCount = data['archivedCount'];
                    this.pendingCount = data['pendingCount'];
                    this.draftCount = data['draftCount'];
                }).catch(() => {
                    if(!self.isApiError) {
                        self.isApiError = true;
                        $.notify({'message':`<strong>${this._ToolTipService.getTranslateData('MESSAGES.COMMON_ERROR_MESSAGE')}</strong>`},
                        {
                            allow_dismiss: true, type: "danger", delay: 0,
                            onClose : function(){ self.isApiError = false; }
                        });
                    }
                });
                this.changeFormData(this._sharedService.myFormsType, "polling");
                this._sharedService.updateUserInboxCounts.emit({
                    getCount: true,
                    type: 'forms'
                });
            }
        });
    }
    archiveSubmitedMyForm() {
        this.archivedBy = true;
        NProgress.start();
        this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
        if (this.isActive == 'draft') {
            this.activeStrucuredForms.isActive = this.isActive;
            this.activeStrucuredForms.downloadTime = new Date().toLocaleString();
            let createdontime;
            if (this.activeStrucuredForms.senton.toString().length == 13) {
                createdontime = this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton));
            } else {
                createdontime = this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton) * 1000);
            }
            this.activeStrucuredForms.createdOn = createdontime;
            // TODO: esi_code_for_patient_identity decommisioned, remove usage if it is not causing any issue
            this.activeStrucuredForms.config_identity_value = this.userData.config.esi_code_for_patient_identity;
            this.activeStrucuredForms.config_staff_identity = this.userData.config_replica.esi_code_for_staff_identity;
            this.activeStrucuredForms.config_staff_name = this.userData.config_replica.esi_code_for_staff_name;
            this.activeStrucuredForms.enable = this.userData.config.enable_progress_note_integration;
            this.activeStrucuredForms.deleted_by = this.userData.userId;
            this._formsService.archiveDraftForm(this.activeStrucuredForms).then((result: any) => {
                if (result.status == 1 || result.status == "1") {
                   this.populateDataAfterChange();
                    this._structureService.notifyMessage({
                        messge: this._ToolTipService.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_CANCEL_FROM_DRAFT' ,{'formName': this.activeStrucuredForms.form_name}),
                        delay: 1000,
                        type: 'success'
                    });
                    let activityData = {
                        activityName: "Cancel Draft Form",
                        activityType: "structured forms",
                        activityDescription: ''
                    };
                    activityData.activityDescription = this.userData.displayName + " canceled draft form " + this.activeStrucuredForms.form_name + " (formId " + this.activeStrucuredForms.form_id + ",sendId " + this.activeStrucuredForms.sent_id;
                    this._structureService.trackActivity(activityData);
                    this.populateDataAfterChange();
                } else {
                    let activityData = {
                        activityName: "Cancel Draft Form",
                        activityType: "structured forms",
                        activityDescription: ''
                    };
                    activityData.activityDescription = this.userData.displayName + " failed canceled draft form " + this.activeStrucuredForms.form_name + " (formId " + this.activeStrucuredForms.form_id + ",sendId " + this.activeStrucuredForms.sent_id;
                    this._structureService.trackActivity(activityData);
                    this._structureService.notifyMessage({
                        messge: this._ToolTipService.getTranslateData('ERROR_MESSAGES.FORM_CANCEL_FROM_DRAFT'),
                        delay: 1000,
                        type: 'warning'
                    });
                }
                this._structureService.socket.emit("sendFormsToRecipients", [{
                    senderId: this.userData.userId
                }]);
                NProgress.done();
            });
        } else {
            this.activeStrucuredForms.isActive = this.isActive;
            this.activeStrucuredForms.downloadTime = new Date().toLocaleString();
            let createdontime;
            if (this.activeStrucuredForms.senton.toString().length == 13) {
                createdontime = this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton));
            } else {
                createdontime = this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton) * 1000);
            }
            this.activeStrucuredForms.createdOn = createdontime;
            if (this.userData.group != 3 && (this.activeStrucuredForms.facing_new != 2 || (this.activeStrucuredForms.facing_new == 2 && this.userData.userId != this.activeStrucuredForms.recipient_id))) {
                this._formsService.archiveSubmittedForm(this.activeStrucuredForms).then((result: any) => {
                    let activityData: any = {};
                    let notifyResult: any = result;
                    if (result.status == '1') {
                        this.archiveCount = parseInt(this.archiveCount) + 1;
                        if (notifyResult.defaultfilingCenter) {
                            activityData.activityName = "Form Copied to Filing Center";
                            activityData.activityType = "structured forms";
                            activityData.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") successfully and copied to filing center named " + notifyResult.defaultfilingCenter;
                            this._structureService.trackActivity(activityData);
                        }
                        if (notifyResult.defaultFromFilingCenterarchivejson && this.userDataConfig.enable_progress_note_integration == 1) {
                            if (notifyResult.defaultFromFilingCenterarchivejson != false && notifyResult.identity_value_Patient != '' || notifyResult.identity_value_Patient != "" || notifyResult.identity_value_Patient != null && notifyResult.patientAssociation == true &&
                                notifyResult.CPRUSERNO != '' || notifyResult.CPRUSERNO != null &&
                                notifyResult.CPRUSERNAME != null || notifyResult.CPRUSERNAME != '') {
                                activityData.activityName = "Form Copied to Filing Center as JSON";
                                activityData.activityType = "structured forms";
                                activityData.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") successfully and copied the JSON file to filing center named " + notifyResult.defaultFromFilingCenterarchivejson;
                                this._structureService.trackActivity(activityData);
                            }
                            if (notifyResult.defaultFromFilingCenterarchivejson != false && (notifyResult.identity_value_Patient == '' || notifyResult.identity_value_Patient == "" || notifyResult.identity_value_Patient == null || notifyResult.patientAssociation == false ||
                                    notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null ||
                                    notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null)) {
                                activityData.activityName = "Failed Form Copy to Filing Center as JSON";
                                activityData.activityType = "structured forms";
                                let messageData = '';
                                let messageData1 = '';
                                if (notifyResult.patientAssociation == false && notifyResult.staffFacing == "true") {
                                    messageData1 = "there is no patient associated with this form";
                                }
                                if ((notifyResult.identity_value_Patient == '' || notifyResult.identity_value_Patient == null || notifyResult.identity_value_Patient == "") && notifyResult.patientAssociation == true) {
                                    messageData += ", MRN";
                                    if (notifyResult.patient_name && notifyResult.patient_name != "") {
                                        messageData += " of " + notifyResult.patient_name;
                                    }
                                }
                                let f1 = 0;
                                let f2 = 0;
                                if (notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null) {
                                    f1 = 1;
                                    messageData += ", USERNO";
                                }
                                if (notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null) {
                                    f2 = 1;
                                    messageData += ", USERNAME";
                                }
                                if ((f1 == 1 || f2 == 1) && notifyResult.staff_name && result.staff_name != "") {
                                    messageData += " of " + notifyResult.staff_name;
                                }
                                if (notifyResult.patientAssociation == false && notifyResult.staffFacing == "false") {
                                    messageData += "";
                                }
                                const removeComa = messageData.charAt(0);
                                if (removeComa == ',') {
                                    messageData = messageData.substring(1);
                                }
                                let finalMessage = '';
                                if (messageData1 && messageData) {
                                    finalMessage = 'Generating Progress Note for Form ' + notifyResult.form_name + ' failed due to ' + messageData1 + ' and missing  (' + messageData + ')';
                                } else if (messageData1) {
                                    finalMessage = 'Generating Progress Note for Form ' + notifyResult.form_name + ' failed due to ' + messageData1;
                                } else if (messageData) {
                                    finalMessage = 'Generating Progress Note for Form ' + notifyResult.form_name + ' failed due to missing (' + messageData + ')';
                                } 
                                if (finalMessage) {
                                    activityData.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") failed to copy the JSON file to filing center named " + notifyResult.defaultFromFilingCenterarchivejson + "because" + messageData;
                                    this._structureService.trackActivity(activityData);
                                    this._structureService.notifyMessage({
                                        messge: finalMessage,
                                        delay: 6000,
                                        type: 'warning'
                                    });
                                }
                            }
                        }
                        NProgress.done();
                        if (this.isActive == 'completed') {
                            this.completedCount = parseInt(this.completedCount) - 1;
                            this._structureService.notifyMessage({
                                messge: this._ToolTipService.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_CANCEL_FROM_COMPLETE' ,{'formName': result.form_name}),
                                delay: 1000,
                                type: 'success'
                            });
                            activityData.activityName = "Archive Completed Form";
                            this.sendPolling('completed', notifyResult.notifyUsers);
                            if(notifyResult.statusfailed){
                                let IsEnterClicked = false;
                                const config = {
                                    title: this._ToolTipService.getTranslateData('ERROR_MESSAGES.INTEGRATION_FAILURE'),
                                    text: this._ToolTipService.getTranslateData('ERROR_MESSAGES.INTEGRATION_FAILURE_ERROR'),
                                    type: "warning",
                                    showCancelButton: true,
                                    confirmButtonText: this._ToolTipService.getTranslateData('BUTTONS.OK'),
                                    closeOnConfirm: true,
                                    confirmButtonClass: "btn-warning",
                                    cancelButtonClass: "btn-default",
                                    cancelButtonText: this._ToolTipService.getTranslateData('BUTTONS.CANCEL')
                                  };
                                  this._structureService.showAlertMessagePopup(config).then((response)=>{
                                    if (IsEnterClicked) {
                                         IsEnterClicked = false;
                                         swal.close();
                                         return false;
                                    }
                                    activityData.activityName = "Integration on Archive operation failed";
                                    activityData.activityType = "structured forms";
                                    if (response) {
                                         activityData.activityDescription = this.userData.displayName + " Integration on Archive operation failed - clicked go back - FormName :" + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ")  User clicked OK option from the Archive operation failed popup";
                                         this._structureService.trackActivity(activityData);
                                    }else{
                                         activityData.activityDescription = this.userData.displayName + " Integration on Archive operation failed - clicked  continue anyway Cancel - FormName :" + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ")  User clicked cancel option from the Archive operation failed popup";
                                         this._structureService.trackActivity(activityData);
                                    }
                               });
                            }
                        } else {
                            this.pendingCount = parseInt(this.pendingCount) - 1;
                            activityData.activityName = "Archive Pending Form";
                            this._structureService.notifyMessage({
                                messge: this._ToolTipService.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_CANCEL_FROM_PENDING',{'formName': result.form_name}),
                                delay: 1000,
                                type: 'success'
                            });
                            this.sendPolling('pending', result.notifyUsers);
                        }
                        activityData.activityType = "structured forms";
                        activityData.activityName = "Archived Form successfully";
                        activityData.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") successfully";
                        this._structureService.trackActivity(activityData);
                        this.populateDataAfterChange();
                    } else {
                        NProgress.done();
                        activityData = {
                            activityName: "Archive Completed Form",
                            activityType: "structured forms",
                            activityDescription: this.userData.displayName + " archived form " + result.form_name + " (" + result.form_id + ") failed"
                        };
                        this._structureService.trackActivity(activityData);
                        this._structureService.notifyMessage({
                            messge: this._ToolTipService.getTranslateDataWithParam('ERROR_MESSAGES.FORM_CANCEL_FROM_COMPLETE_PENDING', {'displayName':this.userData.displayName, 'formName': result.form_name, 'formId': result.form_id}),
                            delay: 1000
                        });
                    }
                    
                });
            } else {
                this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
                let data = this.activeStrucuredForms;
                let notifyUsers = this.activeStrucuredForms.notify_users;
                this.activeStrucuredForms.fromName = this.activeStrucuredForms.fname + ' ' + this.activeStrucuredForms.lname;
                this.activeStrucuredForms.formId = this.activeStrucuredForms.form_id;
                this.activeStrucuredForms.fromId = this.activeStrucuredForms.from_id;
                this.activeStrucuredForms.sentId = this.activeStrucuredForms.sent_id;
                this.activeStrucuredForms.formName = this.activeStrucuredForms.form_name;
                this.activeStrucuredForms.categoryvalue = 1;
                this.activeStrucuredForms.toName = this.activeStrucuredForms.createdUser;
                this.activeStrucuredForms.createdUserName = this.activeStrucuredForms.createdUser;
                this.activeStrucuredForms.fromUsername = this.activeStrucuredForms.createdUserEmail;
                if (this.activeStrucuredForms.form_type == "Staff Facing") {
                    this.activeStrucuredForms.staff_facing = 1;
                } else {
                    this.activeStrucuredForms.staff_facing = 0;
                }
                this.activeStrucuredForms.entryId = this.activeStrucuredForms.form_submission_id;
                notifyUsers = notifyUsers.split(",");
                let notifyUsersPolling = [];
                notifyUsers.forEach(element => {
                    notifyUsersPolling.push({
                        "userid": element
                    });
                });
                if (this.activeStrucuredForms.fromId != this.userData.userId) {
                    notifyUsersPolling.push({
                        "userid": this.activeStrucuredForms.fromId
                    });
                }
                if (this.userData.userId != this.activeStrucuredForms.recipient_id) {
                    notifyUsersPolling.push({
                        "userid": this.activeStrucuredForms.recipient_id
                    });
                }
                var self = this;
                this._formsService.deleteSentForm(data).then((result) => {
                    let resultdata = result;
                    let notifyResult = result;
                    if (notifyUsersPolling.length) {
                        let description = "";
                        let activityDataa: any = {};
                        if (resultdata['status'] == 1) {
                            description = "";
                            NProgress.done();
                            if (this.isActive == 'completed') {
                                this._structureService.notifyMessage({
                                    messge: this._ToolTipService.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_CANCEL_FROM_COMPLETE' ,{'formName': this.activeStrucuredForms.form_name}),
                                    delay: 1000,
                                    type: 'success'
                                });
                                activityDataa.activityName = "Archive Completed Form3";
                                this.sendPolling('completed', notifyUsersPolling);
                            } else {
                                activityDataa.activityName = "Archive Pending Form";
                                this._structureService.notifyMessage({
                                    messge: this._ToolTipService.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_CANCEL_FROM_PENDING',{'formName': this.activeStrucuredForms.form_name}),
                                    delay: 1000,
                                    type: 'success'
                                });
                                this.sendPolling('pending', notifyUsersPolling);
                            }
                            activityDataa.activityType = "structured forms";
                            activityDataa.activityName = "Archived Form successfully";
                            activityDataa.activityDescription = this.userData.displayName + " archived form " + this.activeStrucuredForms.form_name + " (formId " + this.activeStrucuredForms.form_id + ",sendId " + this.activeStrucuredForms.sent_id + ") successfully";
                            this._structureService.trackActivity(activityDataa);
                            this.populateDataAfterChange();
                        } else {
                            description = "error in move to default filing center -" + resultdata['description'];
                        }
                        let activityData = {
                            activityName: "Archive Forms",
                            activityType: "manage forms",
                            activityDescription: this.userData.displayName + "(" + this.userData.userId + ") archived from inbox the form " + data.formName + "(" + data.formId + ") " + description
                        };
                        this._structureService.trackActivity(activityData);
                    }
                });
            }
        }
    }
    populateDataAfterChange() {
        this.getMyWorklistCount();
        this.changeFormData(this._sharedService.myFormsType, "polling");
        this._sharedService.updateUserInboxCounts.emit({
            getCount: true,
            type: 'forms'
        });
    }

    sendPolling(type, notifyUsers) {
        var selectedRecipientsPolling = [];
        var deletedFlag =  0;
        var senderFlag =  0;
        var recipientFlag =  0;    
        if(!isBlank(notifyUsers)) {
            notifyUsers.forEach(element => {
                selectedRecipientsPolling.push(element);
                if(element.userid == this.userData.userId) {
                    deletedFlag = 1;
                }
                if(element.userid == this.activeStrucuredForms.userid) {
                    recipientFlag = 1;
                }
                if(element.userid == this.activeStrucuredForms.from_id) {
                    senderFlag = 1;
                }
            });
        }
        if(deletedFlag == 0) {
            selectedRecipientsPolling.push({
                senderId: this.userData.userId
            });                     
        }
        if(recipientFlag == 0 && this.activeStrucuredForms.userid && this.activeStrucuredForms.userid != this.userData.userId) {
            selectedRecipientsPolling.push({
                userid: this.activeStrucuredForms.userid
            });                     
        }
        if(senderFlag == 0 && this.activeStrucuredForms.from_id && this.activeStrucuredForms.from_id != this.userData.userId && this.activeStrucuredForms.from_id != this.activeStrucuredForms.userid) {
            selectedRecipientsPolling.push({
                userid: this.activeStrucuredForms.from_id
            });                     
        }
        this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", selectedRecipientsPolling, 'form'); //Need to commnd after finish form inbox
        this._structureService.socket.emit("sendFormsToRecipients", selectedRecipientsPolling);
    }

    changeFormData(type, refresh = "") {
        this.showFilterAppliedMessage = this.dateRangeType !== DateRanges.LAST_THIRTY_DAYS;
        if (this.dataTableLoading == false) {
            switch (type) {
                case 'PENDING':
                    this.reverseFlag = true;
                    this._sharedService.myFormsType = type;
                    this.dataTableLoading = true;
                    $('.nav li a.pending').addClass('active');
                    this.strucuredForms = [];
                    this.isActive = this.clickedTab = 'pending';
                    this.setAdvanceSearchForm(refresh);
                    this.populateData(this.isActive, this.isScheduled, refresh);
                    break;
                case 'COMPLETED':
                    this.reverseFlag = true;
                    this._sharedService.myFormsType = type;
                    this.dataTableLoading = true;
                    this.isActive = this.clickedTab = 'completed';
                    this.setAdvanceSearchForm(refresh);
                    this.populateData(this.isActive, this.isScheduled, refresh);
                    break;
                case 'ARCHIVE':
                    this.reverseFlag = true;
                    this._sharedService.myFormsType = type;
                    this.dataTableLoading = true;
                    this.isActive = this.clickedTab = 'archived';
                    this.setAdvanceSearchForm(refresh);
                    this.populateData(this.isActive, this.isScheduled, refresh);
                    break;
                case 'DRAFTS':
                    this._sharedService.myFormsType = type;
                    this.reverseFlag = true;
                    this.dataLoadingMsg = true;
                    this.dataTableLoading = true;
                    this.isActive = 'draft';
                    this.setAdvanceSearchForm(refresh);
                    this.populateData(this.isActive, this.isScheduled, refresh);
                    break;
                case 'ALL':
                    this.reverseFlag = true;
                    this.isScheduled = 'all';
                    this.dataLoadingMsg = true;
                    this.dataTableLoading = true;
                    this.strucuredForms = [];
                    this.setAdvanceSearchForm(refresh);
                    this.populateData(this.isActive, this.isScheduled, refresh);
                    break;
                case 'YES':
                    this.reverseFlag = true;
                    this.isScheduled = 'yes';
                    this.dataLoadingMsg = true;
                    this.dataTableLoading = true;
                    this.strucuredForms = [];
                    this.setAdvanceSearchForm(refresh);
                    this.populateData(this.isActive, this.isScheduled, refresh);
                    break;
                case 'NO':
                    this.reverseFlag = true;
                    this.isScheduled = 'no';
                    this.dataLoadingMsg = true;
                    this.dataTableLoading = true;
                    this.strucuredForms = [];
                    this.setAdvanceSearchForm(refresh);
                    this.populateData(this.isActive, this.isScheduled, refresh);
                    break;
            }
        }
    }

    populateData(formStatus, isScheduled, refresh) {

        if (localStorage.getItem('formSendMessage') && localStorage.getItem('formSendMessage') != '') {            
            let message = JSON.parse(localStorage.getItem('formSendMessage'));            
            let notify = $.notify(message.messge);                
            setTimeout(function() {
                notify.update({
                    'type': message.type,
                    'message': '<strong>' + message.messge + '</strong>'
                });                   
                
            }, 1000);
            localStorage.setItem('formSendMessage', '');
        }

        const self = this;
        let datas: any;

        if (this.flagcheck == 1) {
            this.flagcheck = 2
        } else {
            this.flagcheck = 1
        }

        function format(row) {
            const formsUrl = self._structureService.machFormUrl + 
            APIs.machFormEmbed + '?id=' + row.form_id + 
            '&staffSubmissionId=' + row.staff_submission_id + 
            '&patientId=' + row.recipient_id + 
            '&tenantId=' + row.tenant_id + 
            '&toId=' + row.from_id + 
            '&sentId=' + row.sent_id + 
            '&formId=' + row.form_id + 
            '&loadScript=no &clientId=' + self._structureService.socket.io.engine.id + 
            "&authenticationToken=" + self._structureService.getCookie('authenticationToken') + 
            "&facing_new=" + row.facing_new  + 
            (self._structureService.isMultiAdmissionsEnabled ? `&admissionId=${row.admissionId}`: '') +
            "&uniqueFormIdentity=" + Guid.create();
            return `<div style="width:100%;" class="structured-form-modal">
          <iframe onload="$('.structured-form-modal').scrollTop(0);" allowTransparency="true" class="structured-form-dekstop" id="structured-form-dekstop" frameborder="0" scrolling="no" style="width:100%;border:none;pointer-events: none;" src="${ formsUrl }"></iframe>
          </div>`;
        }

        if (refresh && refresh == "polling") {
            this.onPolling = true;
            if (this.dTable) {
                this.dTable.ajax.reload(null, false);
            }
            self.dataTableLoading = false;
        } else {
            if (this.dTable) {
                this.onPolling = false;
                if (refresh && refresh != "") {
                    this.dTable.state.clear();
                }
                this.dTable.destroy();
            } else {
                if (this._structureService.previousUrlNow.indexOf("/forms/list/view") == -1) {
                    this._structureService.resetDataTable();
                }
            }
            let order;
            switch (this.isActive) {
              case 'archived':
                order = 21;
                break;
              case 'draft':
                order = 19;
                break;
              default:
                order = 17;
                break;
            }
            this.userRole = self._structureService.getCookie('userRole');
            this.userDataConfig = JSON.parse(self._structureService.userDataConfig);
            let showPatientFirstLastName = false;
            if (
                 !isBlank(this.userData.config) &&
                 !isBlank(
                   this.userData.config.show_first_name_last_name_separate
                 ) &&
                 (this.userData.config.show_first_name_last_name_separate ==
                   '1' ||
                   this.userData.config.show_first_name_last_name_separate == 1)
               ){
                 showPatientFirstLastName = true;
                }
           $(()=>{  
                     
            var x = this.dTable = $('#form-list-dt').DataTable({
                order: [
                    [order, "desc"]
                ],
                autoWidth: false,
                responsive: true,
                bprocessing: true,
                bServerSide: true,
                bpagination: true,
                bsorting: true,
                retrieve: true,
                bsearching: true,
                stateSave: true,
                bLengthChange: true,
                bInfo: true,
                lengthMenu: [
                    [25, 50, ],
                    [25, 50]
                ],
                fnDrawCallback: (oSettings) => {
                    if (oSettings._iRecordsTotal < oSettings._iDisplayLength || oSettings._iRecordsTotal == 0 || oSettings.aoData.length == 0) {
                        $('.dataTables_paginate').hide();
                    } else {
                        $('.dataTables_paginate').show();
                    }
            
                    if (oSettings.aoData.length == 0) {
                        $('.dataTables_info').hide();
                    } else {
                        $('.dataTables_info').show();
                    }
                },
                fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
                    var patientDetail = '';
                    if(aData && aData.patientName){
         
                    if (self.isActive == "draft") {
                    patientDetail = aData.patientDob ? 'DOB : '+moment(aData.patientDob).format('MM/DD/YYYY') : '';
                    }else{
                    if(aData.caregiver_userid)
                    patientDetail = aData.caregiver_dob ? 'DOB : '+moment(aData.caregiver_dob).format('MM/DD/YYYY') : '';
                    else
                    patientDetail = aData.patientDob ? 'DOB : '+moment(aData.patientDob).format('MM/DD/YYYY') : '';
                    }
                    $(nRow).find('.patient-details').tooltip({html:true, title: patientDetail });
                    }
                    if (self.isActive == 'draft') {
            
                    } else {
                        $(nRow).addClass("hand-link");
                        $(nRow).attr('id', 'row-' + aData.id);
            
                        if ((typeof aData.unreadCount != "undefined") && (aData.unreadCount != 0)) {
                            $(nRow).css('color', '#100a0b');
                            $(nRow).css('font-weight', 'bold');
                        } else {
                            $(nRow).css('background', '#e4e9f0');
                            $(nRow).addClass("read-tr");
                        }
            
                        if (aData.created_date) {
                            
                            $(nRow).closest("tr").find("td").not(":first-child").not(":last-child").not(".reviewcheckclass").addClass('view');
                        } else {
                            if (self.isActive != 'archived') {
                                if (aData.allow_edit == 1 && self.isActive == "pending" && ((this.userData.userId == aData.recipient_id && aData.staffFacing != 1) || (this.userData.userId == aData.from_id && aData.staffFacing == 1))) {
                                    $(nRow).closest("tr").find("td").not(":first-child").not(":last-child").addClass('view');
                                } else if (self.isActive == 'pending' && aData.allow_edit == 1 && aData.form_submission_id) {
                                    $(nRow).closest("tr").find("td").not(":first-child").not(":last-child").addClass('view');
                                } else if (self.isActive == "pending" && (this.userData.userId == aData.recipient_id && this.userData.group == 3) || (this.userData.userId == aData.recipient_id && aData.facing_new == 2)) {
                                    $(nRow).closest("tr").find("td").not(":first-child").not(":last-child").addClass('submit-form');
                                } else {
                                    $(nRow).closest("tr").find("td").not(":first-child").not(":last-child").addClass('view-form');
                                }
                            }
                        }
                       
                    }
                                
                    $(nRow).on('click', (e) => {
                        this._sharedService.goToInnerPage = true;
                        this.activeStrucuredForms = aData;
                         if (e.target.id.substring(0, 9) == "int-close") {
                            let ids = 'inte-status-not-sftp-' + e.target.id.replace('int-close-', "");
                            this.popoverNotSftp(ids, 'close');
                          }
                          else if(e.target.id.substring(0, 9) == "int-close-discrete"){
                            this.popoverNotSftp('inte-status-not-sftp-discrete' + e.target.id.replace('int-close-', ""), 'close');
                          } 
                    });
                },
                dom:
                "<'row form-worklist-search-row'<'col-sm-2 'l>B<'col-sm-3'f><'col-sm-3 my-form-worklist-search-button'>>" +
                "<'row'<'col-sm-12'tr>>" +
                "<'row'<'col-sm-5'i><'col-sm-7'p>>",
                buttons :[{
                  extend: 'collection',
                  text: 'Export Data',
                  autoClose: true,
                  className: 'buttonStyle',
                  buttons: [
                    {
                      extend: 'excel',
                      text: 'Current Page',
                      title: 'My Form Worklist',
                      exportOptions: {
                        columns:self.isActive == 'draft' && self.userRole != 2 && self.userData.group != 3?[0,2,3,4,5,6,7,13,14]:
                        (self.isActive == 'completed' && self.userRole != 'Patient' && self.userRole != 'Caregiver' && self.userDataConfig.enable_integration_status_worklist == 1 && self.userData.group != 3)?[0,1,2,3,4,5,7,9,11,12,15]:
                        (self.isActive == 'pending' && self.userRole != 'Patient' && self.userRole != 'Caregiver' && self.userDataConfig.enable_integration_status_worklist == 1 && self.userData.group != 3)?[0,1,2,3,4,5,6,7,9,11,12,18]:
                        (self.isActive == 'archived' && self.userRole != 'Patient' && self.userRole != 'Caregiver' && self.userDataConfig.enable_integration_status_worklist == 1 && self.userData.group != 3)?[0,1,2,3,4,5,6,7,9,11,12,15,16,17]:
                        (self.isActive == 'completed' && self.userRole != 2 && self.userData.group != 3)?[0,1,2,3,4,5,6,7,9,12,15]:
                        (self.isActive == 'pending' && self.userRole != 2 && self.userData.group != 3)?[0,1,2,3,4,5,6,7,9,12,18]:
                        (self.isActive == 'archived' && self.userRole != 2 && self.userData.group != 3)?[0,1,2,3,4,5,6,7,9,12,15,16,17]:                        
                        []   
                      }                
                
                    },
                    {
                      extend: 'excel',
                      text: 'All Pages',
                      title: 'My Form Worklist',
                      exportOptions: {
                        columns:self.isActive == 'draft' && self.userRole != 2 && self.userData.group != 3?[0,2,3,4,5,6,7,13,14]:
                        (self.isActive == 'completed' && self.userRole != 'Patient' && self.userRole != 'Caregiver' && self.userDataConfig.enable_integration_status_worklist == 1 && self.userData.group != 3)?[0,1,2,3,4,5,6,7,9,11,12,15]:
                        (self.isActive == 'pending' && self.userRole != 'Patient' && self.userRole != 'Caregiver' && self.userDataConfig.enable_integration_status_worklist == 1 && self.userData.group != 3)?[0,1,2,3,4,5,6,7,9,11,12,18]:
                        (self.isActive == 'archived' && self.userRole != 'Patient' && self.userRole != 'Caregiver' && self.userDataConfig.enable_integration_status_worklist == 1 && self.userData.group != 3)?[0,1,2,3,4,5,6,7,9,11,12,15,16,17]:
                        (self.isActive == 'completed' && self.userRole != 2 && self.userData.group != 3)?[0,1,2,3,4,5,6,7,9,12,15]:
                        (self.isActive == 'pending' && self.userRole != 2 && self.userData.group != 3)?[0,1,2,3,4,5,6,7,9,12,18]:
                        (self.isActive == 'archived' && self.userRole != 2 && self.userData.group != 3)?[0,1,2,3,4,5,6,7,9,12,15,16,17]:                        
                        []   
                      } ,         
                      action: function ( e, dt, node, config ) {
                      var selfButton = this;
                      var oldStart = dt.settings()[0]._iDisplayStart;
                      dt.one('preXhr', function (e, s, data) {
                          data.start = 0;
                          data.length = **********;
                          dt.one('preDraw', function (e, settings) {
                              $.fn.dataTable.ext.buttons.excelHtml5.action.call(selfButton, e, dt, node, config);
                              dt.one('preXhr', function (e, s, data) {
                                  settings._iDisplayStart = oldStart;
                                  data.start = oldStart;
                              });
                              setTimeout(dt.ajax.reload, 0);
                              return false;
                          });
                      });
                      dt.ajax.reload();
                      }
                  }]
              }],
                initComplete: () => {
                    $('.dataTables_filter label input').attr('placeholder', 'Search');
                    $('.dataTables_filter label input').attr('id', 'Search_bx');
                    $('.dataTables_filter label input').unbind();            
                    $('div.dataTables_filter').addClass('my-form-search');
                    $("div.dataTables_filter input").on('input', function(e) {
                        var value = $("div.dataTables_filter input").val();
                        if (value) {
                            $(".searchB").prop('disabled', false);  
                        } 
                        else {
                            $(".searchB").prop('disabled', true);   
                        }
                    });
                    $("div.dataTables_filter input").on('keydown', function (e) {
                        if (e.which == 13) {
                            var value = $("div.dataTables_filter input").val();
            
                            if (value) {
                                self.dTable.search(value);
                            } else {
                                self.dTable.search("");
                            }
                        }
                    });
                    this.socketEventSubscriptions.push(
                        this._structureService.subscribeSocketEvent('reviewStatusChange').subscribe((data) => {
                            if(data && data.UserId!=self.userData.userId){
                            if(data.NewStatus==1 && $("#"+data.form_id+"_"+data.submissionId).prop("checked") == false){
                                $("#"+data.form_id+"_"+data.submissionId).prop('checked', true);
                            }else if(data.NewStatus!=1 && $("#"+data.form_id+"_"+data.submissionId).prop("checked") == true){
                                $("#"+data.form_id+"_"+data.submissionId).prop('checked', false);
                            }
                            }
                        })
                    );
                  
                    $("div.dataTables_filter input").on('keypress', function (e) {
                        $(".searchB").prop('disabled', false);
                    });
                   
                    $("div.dataTables_filter input").on('keyup', function (e) {
                        var value = $("div.dataTables_filter input").val();
                        if (value) {} else
                            $(".searchB").prop('disabled', true);
                    });
                    let label = self._ToolTipService.getTranslateData('TOOLTIPS.ADVANCE_SEARCH');
                    $("div.my-form-worklist-search-button")
                        .html('<button disabled="true" class="btn btn-sm btn-info reset-btn searchB" id="searchB" title="Search" type="submit">Search</button>' +
                            '<button style="margin-left:10px;" class="btn btn-sm btn-default reset-btn resetB" id="resetB" title="Reset" type="submit">Reset</button>'+
                            '<button class="btn btn-sm btn-default reset-btn" id="advance-searchB" style="margin-left:10px;">' + 
                            '<img src="./assets/img/filter.png" data-toggle="tooltip" id="advance-searchB" data-placement="top" title="'+label+'" class="adv-search" style="background-color: #acb7bf;height: 18px;">' +
                            '</button>');
            
                    var value = $("div.dataTables_filter input").val();
            
                    if (value) {
                        $(".searchB").prop('disabled', false);
                    }
            
                    $("div.dataTables_filter input").on('paste', function (event) {
                        var element = this;
                        var text;
                        setTimeout(function () {
                            text = $(element).val();
                            if (text) {
                                $(".searchB").prop('disabled', false);
                            }
                        }, 100);
                    });
                    $(".buttons-collection").click(function(event) {
                        setTimeout(function () {
                          if($(".buttons-collection").attr("aria-expanded") === "true" && $(".dt-button-collection").find("button").length == 0){
                            $(".dt-button-collection").remove();
                            $(".dt-button-background").remove();
                            $(".buttons-collection").attr("aria-expanded","false");
                          }
                        },500);
                    });
                },
                "ajax": (dat, callback, settings) => {
                    let searchText;
                    if (!self.isTableLoaded[self.isActive]) {                      
                        Object.keys(self.isTableLoaded).forEach(tab => {
                             self.isTableLoaded[tab] = false;
                         });
                        self.isTableLoaded[self.isActive] = true;
                        const searchKey = self.getSearchKey(self.isActive);
                        const searchStored = self.storeService.getStoredData(searchKey);
                        if(searchStored && searchStored.advanceSearchInput && !isBlank(searchStored.advanceSearchInput)) {
                            self.advanceSearchInput = searchStored.advanceSearchInput;
                            const  { dynamicControls, isSearchAvailable } = self._structureService.setAdvanceControlDefaultValues('myform', self.isActive, self.advanceSearchInput);
                            self.dynamicControls = dynamicControls;
                            this.showFilterAppliedMessage = isSearchAvailable;
                        } else {
                            self._structureService.hideAdvanceSearchArea = true;
                            const searchText = searchStored && searchStored.searchText ? searchStored.searchText : '';
                            dat.search.value = searchText;
                            if (searchText){
                                this.showFilterAppliedMessage = true;
                                setTimeout(() => {
                                    const searchBox = $('.dataTables_filter label input');
                                    searchBox.val(searchText);
                                }, 1);
                            }
                        }   
                    }
                    self._structureService.notifySearchFilterApplied(this.showFilterAppliedMessage);                 
                    if (Object.keys(self.advanceSearchInput).length > 0) {
                        searchText = JSON.stringify(self.advanceSearchInput);
                        self.storeService.storeData(self.getSearchKey(self.isActive), { advanceSearchInput: self.advanceSearchInput });
                    } else {
                        self._structureService.hideAdvanceSearchArea = true;
                        searchText = dat.search.value ? dat.search.value : '';
                        self.storeService.storeData(self.getSearchKey(self.isActive), {searchText});
                    }
                    if (settings.oAjaxData.start != 0 && self.datam && self.datam.aaData && self.datam.aaData.length == 1 && settings._iDisplayStart != 0 && searchText == '') {
                        settings.oAjaxData.start = settings.oAjaxData.start - settings.oAjaxData.length;
                        settings._iDisplayStart = settings.oAjaxData.start;
                    }
                   
                    let orderData;
                    let orderby;
                    var i = dat.order[0].column ? dat.order[0].column : '';
                    orderby = dat.order[0].dir ? dat.order[0].dir : '';
                
                    if (i != 0) {
                         if (i == 13)
                             orderData = 'senton';
                         else
                             orderData = dat.columns[i].data ? dat.columns[i].data : '';
                     } else {
                         orderData = '';
                     }
                    this.filterType = "";
                    orderData = orderData == 'fname' ? 'fnameSort' : orderData == 'lname' ? 'lnameSort' : orderData;
                    self._formsService.getAllTaggedFormsLazyPagination(timezone.name(), true, dat.length, dat.start, searchText, orderData, orderby, formStatus, isScheduled, this.filterType,this.userData.userId, this.includeOtherForms, 'myWorklist',false, false,self.siteIds).then((dataa) => {
                       this.showFilterAppliedMessage = false;
                        datas = {};
                        self.datam = {};            
                        self.totalCt = dataa['totalCt'];            
                        datas = dataa['response'] ? dataa['response'] : [];
                        self.strucuredForms = datas;
                        const total = (datas && datas.length === 0 && searchText === '' && dat.start === 0) ? 0 : self.totalCt;

                        self.datam = {
                            "draw": dat.draw,
                            "recordsTotal": total,
                            "recordsFiltered": total,
                            "aaData": datas
                        };
                        callback(self.datam)

                        self.clickedTab = "";
                        self.dataTableLoading = false;
                    }).catch(function (e) {
                        if(self.isApiError == false){
                             self.isApiError = true;
                             $.notify({'message':'<strong>We are facing some technical problem to load the Worklists. Please contact your administrator</strong>'},
                             {
                                  allow_dismiss: true, type: "danger", delay: 0,
                                  onClose : function(){ self.isApiError = false; }
                             });
                        }
                    });
                },
                columnDefs: [{
                        title: "#",
                        data: null,
                        orderable: false,
                        render: function (data, type, row, meta) {
                            return meta.row + 1 + meta.settings._iDisplayStart;
                        },
                        width: "5%",
                        targets: 0
                    },
                    {
                        title: "Created By",
                        data: 'createdUser',
                        targets: 1,
                        width: "15%",
                        visible: (self.isActive == 'draft' && self.previlages['accessAllDraftinMyFormWorklist']) ? true : false,
                        render: (data, type, row) => {
                            return data.replace(/,/g, ' ');
                        }
                    },
                    {
                        title: "Sent By",
                        data: 'createdUser',
                        visible: (self.isActive != 'draft') ? true : false,
                        targets: 2,
                        width: "15%",
                    },
                    {
                        title: "Patient Name",
                        data: 'patientName',
                        targets: 3,
                        visible: (self.userRole != 'Patient') ? !showPatientFirstLastName : false,
                        width: "20%",
                        render: (data, type, row) => {
                            let patientDetails = '';
                            if(row && row.patientName == null || row.patientName == ""||row.patientName == "null" ){
                                return '';
                            }
                            if(row && row.patientName && row.patientName != " "){
                            patientDetails = ` <i class='patient-details icmn-info'></i>`;
                            }
                            if (formStatus == "draft") {
                                if (row.caregiver) {
                                    return row.patientName + " (" + row.original_patient_displayname + ")" +patientDetails;
                                } else {
                                    if (row.facing_new == 2&&row.createdUser==row.patientName) {
                                        return ' ';
                                    }
                                    
                                    else{
                                        return row.patientName +patientDetails;
                                    }
                                    
                                }
                            } else {
                                if (row.facing_new == 2) {
                                    // Practitioner flow
                                    if(row && isBlank(row.caregiver_displayname) || row.createdUser==row.caregiver_displayname ){
                                        return '';
                                    }
                                    else{
                                        return row.caregiver_displayname +patientDetails;
                                    }
                                    
                                } else {
                                    if (row.caregiver_userid) {
                                        return row.caregiver_displayname + " (" + row.patientName + ")" +patientDetails;
                                    } else {
                                        return row.patientName +patientDetails;
                                    }
                                }
                            }
                        },
                    },
                    {
                        title: "Patient First Name",
                        data: 'fname',
                        targets: 4,
                        width: "15%",
                        visible: showPatientFirstLastName,
                        render: function(data, type, row) {
                            let patientDetails = '';
                            if (row && isBlank(row.fname)){
                                 return '';
                            } else {
                                 patientDetails = ` <i class='patient-details icmn-info'></i>`;
                            }
                            if (formStatus == "draft") {
                               if (row.caregiver) {
                                   return row.fname + " (" + row.original_patient_displayname + ")" +patientDetails;
                               } else {
                                   if (row.facing_new == 2&&row.createdUser==row.patientName) {
                                       return ' ';
                                   } else{
                                       return (row.fname && row.patientName) ? row.fname + patientDetails : "";
                                   }
                                   
                               }
                           } else {
                               if (row.facing_new == 2) {
                                   // Practitioner flow
                                   if(row && isBlank(row.caregiver_displayname) || row.createdUser==row.caregiver_displayname ){
                                       return '';
                                   }
                                   else{
                                       return row.cgiver_fname ? row.cgiver_fname + patientDetails : "";
                                   }
                                   
                               } else {
                                   if (row.caregiver_userid) {
                                       return row.cgiver_fname + " (" + row.patientName + ")" +patientDetails;
                                   } else {
                                       return (row.fname && row.patientName) ? row.fname + patientDetails : "";
                                   }
                               }
                           }
                        }
                   },
                   {
                        title: "Patient Last Name",
                        data: 'lname',
                        targets: 5,
                        width: "15%",
                        visible: showPatientFirstLastName,
                        render: function(data, type, row) {
                            if (row && isBlank(row.lname)){
                                 return '';
                            }
                            if (formStatus == "draft") {
                               if (row.caregiver) {
                                   return row.lname;
                               } else {
                                   if (row.facing_new == 2&&row.createdUser==row.patientName) {
                                       return ' ';
                                   }
                                   
                                   else{
                                       return (row.lname && row.patientName) ? row.lname : "";
                                   }
                                   
                               }
                           } else {
                               if (row.facing_new == 2) {
                                   // Practitioner flow
                                   if(row && isBlank(row.caregiver_displayname) || row.createdUser==row.caregiver_displayname ){
                                       return '';
                                   }
                                   else{
                                       return row.cgiver_lname;
                                   }
                                   
                               } else {
                                   if (row.caregiver_userid) {
                                       return row.cgiver_lname;
                                   } else {
                                       return (row.lname && row.patientName) ? row.lname : "";
                                   }
                               }
                           }
                       }
                   },
                    {
                        title: "MRN",
                        data: 'patientIdentityValue',
                        targets: 6,
                        visible: +self.userData.group !== UserGroup.PATIENT,
                        render: function (data, type, row) {
                            if (row.patientIdentityValue != null && row.patientIdentityValue != '') {
                                return row.patientIdentityValue;
                            } else if (row.patientIdentityValue == null && row.cgiverIdentityValue != null && row.cgiverIdentityValue != ''){    
                                return row.cgiverIdentityValue;
                            } else{
                                return "";
                            }
                        },

                      },
                      {
                        title: this._ToolTipService.getTranslateData('ADMISSION.LABELS.ADMISSION'),
                        data: 'admissionName',
                        targets: 7,
                        visible: self._structureService.isMultiAdmissionsEnabled,
                        render: function(data, type, row) {
                             if (!isBlank(row.admissionName)) {
                                  return row.admissionName;
                             } 
                             return "";
                             
                        }
                       },
                      {
                        title: this.labelSite,
                        data: 'siteName',
                        width: "15%",
                        visible: (+self.userData.group !== UserGroup.PATIENT && this.userData && this.userData.config && (( this.userData.mySites && this.userData.config.enable_multisite && this.userData.mySites.length > 1 && this.userData.config.enable_multisite == "1") || (( !this.userData.config.enable_multisite || this.userData.config.enable_multisite !="1")))),
                        targets: 8,
                        orderable: true,
                        render: function (data, type, row) {
                            if(row.siteName){
                                return row.siteName;
                            }else{
                                return null;
                            }
                          }
                    },
                    {
                        title: "Form Name",
                        data: 'form_name',
                        targets: 9,
                        width: "20%",
                        render: function (data, type, row) {
                            if(self.userData.group != 3){
                            let sentPopover = ``;

                            let statusHtml = '';

                            if(self.isActive == "pending"){
                                row.tag_id = row.tagId;
                            }

                            let popoverSent = `<div id="inte-status-not-sftp-form-${row.tag_id}-${row.id}" class="fade show bs-popover-right inte-popover" style="display:none;"
                            >
                            <div class="popover-arrow"></div>
                            <div class="pop-title">
                                 <button type="button" id="int-close-form-${row.tag_id}-${row.id}" class="close fa fa-close"></button>
                            </div>
                            <div class="pop-content-form">
                                 <div class="int-loader">
                                      <img src="./assets/modules/dummy-assets/common/img/spinner.svg" alt="loading">
                                 </div>
                                 <div class="int-main-content-form" id="int-content-form-${row.tag_id}-${row.id}">
                                      ${sentPopover}
                                 </div>
                            </div>    
                            </div>`;

                            let style = 'badge-success';
                            let text = 'Completed';
                            let color_code = '#46be8a';

                            statusHtml += `
                            <div style="position:relative;">
                            <span mr-2 mb-2">${row.form_name}</span>
                            <span style="font-size: 14px; color: #0000FF; cursor: pointer;">
                            <i class='icmn-info'id="inte-status-not-sftp-form"></i>
                            </span>
                            ${popoverSent}
                            </div>`;

                            return statusHtml;
                            }else{
                                return row.form_name;
                            }
                       }
                    },
                   
                    {
                        title: "Workflow",
                        data: null,
                        targets: 10,
                        width: "5%",
                        orderable: false,
                        render: function (data, type, row) {
                            // if ((row.patient_id != '0' && (self.isActive == 'completed' || self.isActive == 'archived' || self.isActive == 'draft') && row.created_date)||(row.allow_edit==1 && row.form_type=='Staff Facing')) {
                            if (row.facing_new === 2) {
                                return '<span class="badge badge-primary mr-2 mb-2">Staff-Practitioner Facing </span>';
                            } else if ((row.patient_id != '0' && (self.isActive == 'completed' || self.isActive == 'archived' || self.isActive == 'draft') && row.created_date && row.form_type == 'Staff Facing') || (row.allow_edit == 1 && row.form_type == 'Staff Facing')) {
                                return '<span class="badge badge-success mr-2 mb-2">Staff/Partner Facing </span>';
                            } else if (row.facing_new === 0) {
                                return '<span class="badge badge-primary mr-2 mb-2">Patient Facing </span>'
							} else if (row.form_type == 'Staff Facing' && row.facing_new != 2) {
							  return '<span class="badge badge-success mr-2 mb-2"> Staff/Partner Facing </span>';
							}
                        }
                    },
                    {
                        title:"Review Complete <i class='review-tooltip icmn-info' data-toggle='tooltip' data-placement='right'></i>",
                        data: null,
                        orderable: false,
                        searchable: false,
                        visible: ((self.isActive == 'completed' || self.isActive == 'archived') && (self.userData.group!=3))? true : false,
                        className: 'dt-body-center reviewcheckclass',
                        render: function (data, type, row, meta) {
                         var checked=(row.reviewed && row.reviewed==1)?'checked="checked"':'';
                         var ReviewStatusNew=(row.reviewed && row.reviewed==1)?2:1;
                         if(self.userData.privileges && self.userData.privileges.includes("reviewSubmittedForm")) return '<input type="checkbox" id="'+row.form_id+'_'+row.form_submission_id+'" name="form_review_status" '+checked+' data-form_name="'+row.form_name+'" class="form-select-all-indicator form-select-cls reviewstatuscheck" data-submission_id="'+row.form_submission_id+'" value="' + row.id + '" data-form_id="'+row.form_id+'" data-status="'+ReviewStatusNew+'">';
                         else return '<input type="checkbox" id="'+row.form_id+'_'+row.form_submission_id+'" name="form_review_status" class="form-select-all-indicator disabled form-select-cls" '+checked+' value="' + row.id + '">';
                        },
                        width: "5%",
                        targets:11
                      },
                    {
                        title: "Scheduled? <i class='default-Scheduled icmn-info' data-toggle='tooltip' data-placement='right'></i>",
                        data: 'isScheduled',
                        targets: 12,
                        width: "12%",
                        visible: (self.isActive != 'draft') ? true : false,
                        render: function (data, type, row) {
            
                            if (data == 1) {
                                return "Yes";
                            } else {
                                return "No";
                            }
            
                        },
                    },
                    {
                        title: "Sent Form via",
                        data: 'applessMode',
                        orderable: true,
                        searchable: false,
                        targets: 13,
                        render: (data, type, row) => {
                            if(row.applessMode == "1") {
                              return 'AppLess (MagicLink)';
                            } else {
                              return 'In-App';
                            }
                        }
                      },
                    {
                        title: "Integration Status",
                        data: 'isScheduled',
                        targets: 14,
                        width: "12%",
                        visible: (self.isActive != 'draft' && self.userRole != 'Patient' && self.userRole != 'Caregiver' && self.userDataConfig.enable_integration_status_worklist == 1) ? true : false,
                        render: function (data, type, row) {
            if(self.userDataConfig.enable_sftp_integration == 0){
            if(!row.request_id || typeof(row.request_id) == 'undefined'){
              return '';
            }

          let sentPopover = ``;

          let statusHtml = '';

          let popoverSent = `<div id="inte-status-not-sftp-${row.senton}" class="fade show bs-popover-right inte-popover" style="display:none;"
          >
            <div class="popover-arrow"></div>
            <div class="pop-title">
                <button type="button" id="int-close-${row.senton}" class="close fa fa-close"></button>
                <div>
                    <i class="copy-ico fa fa-files-o" id="int-copy-sent" aria-hidden="true"></i>
                </div>
            </div>
            <div class="pop-content">
                <div class="int-loader">
                    <img src="./assets/modules/dummy-assets/common/img/spinner.svg" alt="loading">
                </div>
                <div class="int-main-content" id="inte-status-not-sftp-${row.senton}-int-content">
                    ${sentPopover}
                </div>
            </div>    
           </div>`;

          let style = 'badge-warning';
          let text = 'Processing';
          let color_code = '#f39834';

          if(row.integration_status == "completed"){
            style = 'badge-success';
            text = 'Completed';
            color_code = '#46be8a';
          }

          if(row.integration_status == "failed"||row.integration_status ==  "Error"){
            style = 'badge-danger';
            text = 'Failed';
            color_code = '#fb434a';
          }
          
          statusHtml += `
            <div style="position:relative;">
            <span class="badge ${style} mr-2 mb-2">${text}</span>
            <span style="font-size: 14px; color: ${color_code}; cursor: pointer;">
            <i class='icmn-info'id="inte-status-not-sftp"></i>
            </span>
          ${popoverSent}
          </div>`;



          return statusHtml;
            
          }else{
              return '';
            }
        
            
                        },
                    },
                    {
                        title: "Discrete Data Integration Status",
                        data: 'discrete_integration_status',
                        targets: 15,
                        width: "12%",
                        visible: (self.isActive != 'draft' && self.userRole != 'Patient' && self.userRole != 'Caregiver' && self.userDataConfig.enable_discrete_integration_status_worklist == 1) ? true : false,
                        render: function (data, type, row) {
            if(self.userDataConfig.enable_sftp_integration == 0){
            if(!row.discrete_request_id || typeof(row.discrete_request_id) == 'undefined'){
              return '';
            }

          let sentPopover = ``;

          let statusHtml = '';

          let popoverSent = `<div id="inte-status-not-sftp-discrete-${row.senton}" class="fade show bs-popover-right inte-popover" style="display:none;"
          >
            <div class="popover-arrow"></div>
            <div class="pop-title">
                <button type="button" id="int-close-discrete-${row.senton}" class="close fa fa-close"></button>
                <div>
                    <i class="copy-ico fa fa-files-o int-copy-clipboard" id="int-copy-sent-discrete" aria-hidden="true"></i>
                </div>
            </div>
            <div class="pop-content">
                <div class="int-loader">
                    <img src="./assets/modules/dummy-assets/common/img/spinner.svg" alt="loading">
                </div>
                <div class="int-main-content" id="inte-status-not-sftp-discrete-${row.senton}-int-content">
                    ${sentPopover}
                </div>
            </div>    
           </div>`;

          let style = 'badge-warning';
          let text = 'Processing';
          let color_code = '#f39834';

          if(row.discrete_integration_status == "completed"){
            style = 'badge-success';
            text = 'Completed';
            color_code = '#46be8a';
          }

          if(row.discrete_integration_status == "failed"||row.discrete_integration_status ==  "Error"){
            style = 'badge-danger';
            text = 'Failed';
            color_code = '#fb434a';
          }
          
          statusHtml += `
            <div style="position:relative;">
            <span class="badge ${style} mr-2 mb-2">${text}</span>
            <span style="font-size: 14px; color: ${color_code}; cursor: pointer;">
            <i class='icmn-info'id="inte-status-not-sftp-discrete"></i>
            </span>
          ${popoverSent}
          </div>`;



          return statusHtml;
            
          }else{
              return '';
            }
        
            
                        },
                    },
                    {
                        title: "Practitioner Name",
                        data: 'practitionerName',
                        targets: 16,
                        visible: (self.userData.group == 3) ? false : true,
                        width: "20%",
                        // visible: (self.isActive != 'draft')? true:false,
                        render: function (data, type, row) {
                            if (self.isActive == 'draft') {
                                return row.practitionerName;
                            } else {
                                if (row.facing_new == 2) {
                                    // Practitioner flow
                                    return row.patientName;
                                } else {
                                    return "";
                                }
            
                            }
                        },
                    },
                    {
                        title: "Sent On",
                        data: 'senton',
                        targets: 17,
                        width: "10%",
                        visible: (self.isActive != 'draft') ? true : false,
                        // orderData: [11],
                        render: (data, type, row) => {
                            if (row.patient_id != '0' && (self.isActive == 'completed' || self.isActive == 'archived') && row.created_date) {
                                return this.formpPipe.transform(parseInt(row.sentDate) * 1000);
                            } else {
                                return this.formpPipe.transform(parseInt(row.sentDate) * 1000);
                            }
                        }
                    },
                    {
                        title: "Saved On",
                        data: 'saved_on',
                        targets: 18,
                        width: "10%",
                        visible: (self.isActive == 'draft') ? true : false,
                        // orderData: [11],
                        render: (data, type, row) => {
                            if (row.createdtimestamp) {
                                return this.formpPipe.transform(parseInt(row.createdtimestamp) * 1000);
                            } else {
                                return this.formpPipe.transform(parseInt(row.updatedtimestamp) * 1000);
                            }
                        }
                    },
                    {
                        title: "Modified On",
                        data: 'modified_on',
                        targets: 19,
                        width: "10%",
                        visible: (self.isActive == 'draft') ? true : false,
                        // orderData: [11],
                        render: (data, type, row) => {
                            if (row.updatedtimestamp) {
                                return this.formpPipe.transform(parseInt(row.updatedtimestamp) * 1000);
                            } else {
                                return this.formpPipe.transform(parseInt(row.updatedtimestamp) * 1000);
                            }
                        }
                    },
                    {
                        title: "Submitted On",
                        data: 'senton',
                        targets: 20,
                        width: "10%",
                        visible: (self.isActive == 'pending' || self.isActive == 'draft') ? false : true,
                        // orderData: [8],
                        render: (data, type, row) => {
                            if (self.isActive == 'archived' && !row.created_date) {
                                return '';
                            } else {
                                if (row.updatedtimestamp == null) {
                                    if (data && data.toString().length == 13) {
                                        return this.formpPipe.transform(parseInt(data));
                                    } else {
                                        return this.formpPipe.transform(parseInt(data) * 1000);
                                    }
                                } else {
                                    return this.formpPipe.transform(parseInt(row.updatedtimestamp) * 1000);
                                }
            
                            }
                        }
                    },
                    {
                        title: "Archived On",
                        data: 'deletedOn',
                        targets: 21,
                        width: "10%",
                        visible: (self.isActive == 'archived' && self.userData.group != 3) ? true : false,
                        render: (data, type, row) => {
                            if (data) {
                                return this.formpPipe.transform(parseInt(data) * 1000);
                            } else {
                                return '';
                            }
                        }
                    },
                    {
                        title: "Archived By",
                        data: 'deletedName',
                        targets: 22,
                        width: "10%",
                        visible: (self.isActive == 'archived' && self.userData.group != 3) ? true : false,
                        render: (data, type, row) => {
                            if (data) {
                                return row.deletedName;
                            } else {
                                return '';
                            }
                        }
                    },
                    {
                        title: "Last Reminder Sent",
                        data: 'lastRemindedOn',
                        targets: 23,
                        width: "10%",
                        // orderData: [10],
                        visible: self.isActive == 'pending' ? true : false,
                        render: (data, type, row) => {
                            let activeTimeZone = (new Date().getTimezoneOffset()) * -1;
                            if (self.isActive != 'pending' || !row.lastRemindedOn) {
                                return '';
                            } else {
                                return this.formpPipe.transform(parseInt(row.lastRemindedOn) * 1000); // + '-' +this._structureService.convertTo24Hour(this.formpPipe.transform(parseInt(row.lastRemindedOn) * 1000)) + '-'+this._structureService.clientToGmtTime(this._structureService.convertTo24Hour(this.formpPipe.transform(parseInt(row.lastRemindedOn) * 1000)), true, 0, ((new Date().getTimezoneOffset())*-1));
                            }
                        }
                    },
                    
                    { 	className: 'details-control',
                    	title: "Actions",
                        data: null,
                        orderable: false,
                        width: "10%",
                        targets: 24,
                        render: (strucuredForm, type, row) => {
                            let actions = '<div class="btn-group mr-2 mb-2 no-margin" aria-label="" role="group">';
                            if (self.isActive == 'draft') {
                                actions += `<a href="javascript: void(0);" id="edit-dform" data-form-edit="${row.form_id}" data-view class="pull-right btn btn-sm edit-dform" title="Edit"><i data-form-edit="${row.form_id}" data-view class="icmn-pen"></i></a>`;
                                actions += `<a href="javascript: void(0);" id="archive-form" data-archive class="pull-right btn btn-sm" title="Cancel"><i class="icmn-cancel-circle"></i></a>`;
                                
                                actions += `<a href="javascript: void(0);" id="draftHistory" style="color: #11a2e3;text-decoration:none;" data-view class="pull-right btn btn-sm reminder-details" data-form-edit="${row.form_id}" data-form-submission-edit="${row.form_submission_id}" title="History"><i data-view class="icmn-history"></i></a>`;
                                /**Add download action in draft bucket */
                                actions += `<a href="javascript: void(0);" id="download" data-download class="pull-right btn btn-sm" title="Download"><i data-download class="icmn-download"></i></a>`;
                            } else {
                                if (row.created_date) {
                                    actions += `<a href="javascript: void(0);" id="views" style="color: #11a2e3;" data-view class="pull-right btn btn-sm views" title="View"><i data-view class="icmn-eye"></i></a>`;
                                    if (this.userData.group != 3) {
                                    actions += `<a href="javascript: void(0);" id="history" style="color: #11a2e3;" data-view class="pull-right btn btn-sm" data-form-edit="${row.form_id}" data-form-submission-edit="${row.form_submission_id}" data-sent-id-edit="${row.sent_id}" data-recipient-id-edit="${row.recipient_id}" data-sent-date-edit="${row.senton}"  data-sent-date-pending-edit="${row.sentDate}" data-submit-date-edit="${row.updatedtimestamp}" title="History"><i data-view class="icmn-history"></i></a>`;
                                    }
                                    actions += `<a href="javascript: void(0);"  style="color: #11a2e3;" id="download" data-download class="pull-right btn btn-sm" title="Download"><i data-download class="icmn-download"></i></a>`;
                                } else {
                                    if (self.isActive != 'archived') {
                                        if (row.allow_edit == 1 && self.isActive === Status.Pending && ((this.userData.userId == row.recipient_id && row.staffFacing != 1) || (this.userData.userId == row.from_id && row.staffFacing == 1) || (row.form_submission_id))) {
                                            actions += `<a href="javascript: void(0);" id="views" style="color: #11a2e3;" data-view class="pull-right btn btn-sm views" title="View"><i data-view class="icmn-eye"></i></a>`;
                                        } else if (self.isActive == Status.Pending && (this.userData.userId == row.recipient_id && this.userData.group == CONSTANTS.userGroupIds.patient)) {
                                            actions += `<a href="javascript: void(0);" style="color: #11a2e3;" id="submit-forms" data-view class="pull-right btn btn-sm submit-forms" title="View"><i data-view class="icmn-eye"></i></a>`;
                                            if(this.userData.group != 3) {
                                           actions += `<a href="javascript: void(0);" id="history" class="pull-right btn btn-sm"  data-form-edit="${row.form_id}" data-form-submission-edit="${row.form_submission_id}" data-sent-id-edit="${row.sent_id}" data-recipient-id-edit="${row.recipient_id}" data-sent-date-edit="${row.senton}"  data-sent-date-pending-edit="${row.sentDate}" data-submit-date-edit="${row.updatedtimestamp}" title="History"><i data-view class="icmn-history"></i></a>`;
                                            }
                                        } else {
                                            if (self.isActive == Status.Pending || (row.facing_new == 2 && this.userData.userId != row.recipient_id)) {
                                                actions += `<a href="javascript: void(0);" style="color: #11a2e3;" id="view-forms" data-view class="pull-right btn btn-sm view-forms" title="View"><i data-view class="icmn-eye"></i></a>`;
                                            }
                                        }
                                        if (this.userRole !== UserRoles.patient && this.userData.userId != row.recipient_id) {
                                            actions += `<a href="javascript: void(0);" id="resend-form" data-view class="pull-right btn btn-sm resend-form" title="${this._ToolTipService.getTranslateData('TITLES.SEND_REMINDER')}" style="color: #11a2e3;"><i class="fa icmn-clock" aria-hidden="true"></i></a>`;
                                        } 
                                    }
                                }
            
                                if (row.facing_new == 2 && row.allow_edit != 1 && self.isActive == Status.Pending && this.userData.userId == row.recipient_id) {
                                    actions += `<a href="javascript: void(0);" id="edit-pform" style="color: #11a2e3;" data-form-edit="${row.form_id}" data-view class="pull-right btn btn-sm edit-pform" title="Sign"><i data-form-edit="${row.form_id}" data-view class="icmn-pen"></i></a>`;
                                } else if (row.allow_edit == 1 && self.isActive == Status.Pending && ((this.userData.userId == row.recipient_id && row.staffFacing != 1) || (this.userData.userId == row.from_id && row.staffFacing == 1))) {
                                    actions += `<a href="javascript: void(0);" style="color: #11a2e3;"  id="edit-pform" data-form-edit="${row.form_id}" data-view class="pull-right btn btn-sm edit-pform" title="Edit"><i data-form-edit="${row.form_id}" data-view class="icmn-pen"></i></a>`;
                                }   
            
                                if (this.userData.group != 3) {
                                    
                                    if (self.isActive == 'archived' && row.created_date && self.previlages.allowArchiveForms == true && row.facing_new == 2 && this.userData.userId == row.recipient_id) {
                                        actions += `<a href="javascript: void(0);" id="restore-forms" class="pull-right btn btn-sm" title="Restore" style="color: #11a2e3;display:none"><i class="fa fa-undo"></i></a>`;
                                    } else if (self.isActive == 'archived' && row.created_date && self.previlages.allowArchiveForms == true) {
                                        actions += `<a href="javascript: void(0);" id="restore-forms" class="pull-right btn btn-sm" title="Restore" style="color: #11a2e3;"><i class="fa fa-undo"></i></a>`;
                                    }else if (self.isActive == 'completed' && self.previlages.allowArchiveForms == true && row.facing_new ==2 && this.userData.userId == row.recipient_id) {
                                        actions += `<a href="javascript: void(0);" id="archive-form" data-archive class="pull-right btn btn-sm" title="Archive" style="color: #11a2e3;display:none"><i class="icmn-box-add"></i></a>`;
                                    } else if (self.isActive == 'completed' && self.previlages.allowArchiveForms == true) {
                                        actions += `<a href="javascript: void(0);" id="archive-form" data-archive class="pull-right btn btn-sm" title="Archive" style="color: #11a2e3;"><i class="icmn-box-add"></i></a>`;
                                    }
                                    if (self.isActive == 'pending') {

                                        if ((self.previlages.viewFormEntries || row.from_id == self.userData.userId) && row.allow_edit!=1 )
                                        {
                                         actions += `<a href="javascript: void(0);" style="color: #11a2e3;" id="cancel-form"   data-cancel class="pull-right btn btn-sm" title="Cancel"><i class="icmn-cancel-circle"></i></a>`;
                
                                        }


                                         if (row.allow_edit == 1) {
                                            actions += `<a href="javascript: void(0);" style="color: #11a2e3;" id="allow_edit" name="edit-` + row.form_id + row.form_submission_id + `" class="pull-right btn btn-sm" title="Deny Edit"><i data-edit class="icmn-undo2"></i></a>`;
            
                                        } 

       
                                        actions += `<a href="javascript: void(0);" id="history" style="color: #11a2e3;" class="pull-right btn btn-sm"  data-form-edit="${row.form_id}" data-form-submission-edit="${row.form_submission_id}" data-sent-id-edit="${row.sent_id}" data-recipient-id-edit="${row.recipient_id}" data-sent-date-edit="${row.senton}"  data-sent-date-pending-edit="${row.sentDate}" data-submit-date-edit="${row.updatedtimestamp}" title="History"><i data-view class="icmn-history"></i></a>`;
                                        /**Add download action for the users other than patient in pending bucket */
                                        actions += `<a href="javascript: void(0);" id="download" data-download class="btn-blue pull-right btn btn-sm" title="Download"><i data-download class="icmn-download"></i></a>`;


                                    } else if((self.isActive === Status.Completed || self.isActive === Status.Archived) && !isBlank(self.previlages.cancelCompletedArchivedForms) && self.previlages.cancelCompletedArchivedForms) {
                                        actions += `<a href="javascript: void(0);" style="color: #11a2e3;" id="cancel-form" data-cancel class="pull-right btn btn-sm" title="`+this._ToolTipService.getTranslateData('BUTTONS.CANCEL')+`"><i class="icmn-cancel-circle"></i></a>`;
                                    }
            
                                    if (row.created_date && self.isActive != 'archived') {

                                        
                                        //temporary comment for release
                                        if (row.allow_edit == 1) {
                                            actions += `<a href="javascript: void(0);" style="color: #11a2e3;" id="allow_edit" name="edit-` + row.form_id + row.form_submission_id + `" class="pull-right btn btn-sm" title="Deny Edit"><i data-edit class="icmn-undo2"></i></a>`;
            
                                        } else {
                                            actions += `<a href="javascript: void(0);" style="color: #11a2e3;" id="allow_edit" name="edit-` + row.form_id + row.form_submission_id + `" class="pull-right btn btn-sm" title="Allow Edit"><i data-edit class="icmn-pencil2"></i></a>`;
            
                                        }
                                    }
                                    if (self.isActive == 'completed' && row.integrationenable && ((row.facing_new == 2 && this.userData.userId != row.recipient_id) || (row.facing_new != 2))) {
                                        let tooltip = "Send to EHR";
                                        if (this.userDataConfig.label_for_file_generation) {
                                            tooltip = this.userDataConfig.label_for_file_generation;
                                        }
                                        actions += `<a href="javascript: void(0);" style="color: #11a2e3;" id="move_fc" class="pull-right btn btn-sm" title="${tooltip}"><i data-edit class="icmn-folder-upload"></i></a>`;
                                    }

                                    //show resend receipients action button based on the form type settings
                                    if ((self.isActive == 'archived' || self.isActive == 'completed') && row.sendCompletedForm) {
                                        actions += `<a href="javascript: void(0);" id="resend-completed-forms" class="pull-right btn btn-sm" title="`+this._ToolTipService.getTranslateData('TOOLTIPS.RESEND_COMPLETED_FORM')+`" style="color: #11a2e3;"><i class="fa fa-refresh"></i></a>`;
                                    }            
                                } else {

                   
            
                                    //////////////////////////////////////////
                                    if (self.isActive == 'archived' && row.admin_archived != 1 && self.previlages.allowArchiveForms == true ) {
                                        actions += `<a href="javascript: void(0);" id="restore-forms" class="pull-right btn btn-sm" title="Restore" style="color: #11a2e3;"><i class="fa fa-undo"></i></a>`;
                                    } else if (self.isActive == 'completed' && self.previlages.allowArchiveForms == true ) {
                                        actions += `<a href="javascript: void(0);" id="archive-form" data-archive class="pull-right btn btn-sm" title="Archive" style="color: #11a2e3;"><i class="icmn-box-add"></i></a>`;
                                    }
            
                                    if (self.isActive == 'pending') {
                                        if (row.allow_edit == 1) {
                                            // actions += `<a href="javascript: void(0);" style="color: #11a2e3;"  id="allow_edit" name="edit-` + row.form_id + row.form_submission_id + `" class="pull-right btn btn-sm" title="Deny Edit"><i data-edit class="icmn-undo2"></i></a>`;
            
                                        } else if ((self.previlages.viewFormEntries || row.from_id == self.userData.userId)  && (row.facing_new!=2 && row.form_type != 'Staff Facing')&& (row.allow_edit!=1) ) {
                                            actions += `<a href="javascript: void(0);" id="cancel-myForm" data-cancel class="pull-right btn btn-sm" title="Cancel"><i class="icmn-cancel-circle"></i></a>`;
                                        }
                                    } 
                                }
                                if (row.allow_edit !== 1 && self._structureService.privileges.completePendingForms && self.isActive === 'pending' && row.facing_new === 0) {
                                   actions += `<a href="javascript: void(0);" style="color: #11a2e3;" id="mark-complete" class="pull-right btn btn-sm" data-form-edit="${row.form_id}" data-form-submission-edit="${row.form_submission_id}" data-sent-id-edit="${row.sent_id}" data-recipient-id-edit="${row.recipient_id}" data-sent-date-edit="${row.senton}"  data-sent-date-pending-edit="${row.sentDate}" data-submit-date-edit="${row.updatedtimestamp}"  title="${this._ToolTipService.getTranslateData('TOOLTIPS.MARK_AS_COMPLETED')}"><i data-view id="mark-complete" class="icmn-clipboard"></i></a>`;
                              }
                            }
                            actions += `</div>`
                            return actions;
                        },
                        
                    }
                ]
            });
            $('#form-list-dt tbody').on('click', 'tr .child', function (e) {
                self._sharedService.goToInnerPage = true;
                var parentRow = $(this).closest("tr").prev()[0];
                self.activeStrucuredForms = self.dTable.row( parentRow ).data();
           }); 
            $('#form-list-dt_filter label input').attr('id','search_box');
            $('#form-list-dt tbody').off('click', 'div.btn-group > .archive-form');
            $('#form-list-dt tbody').on('click', 'div.btn-group > .archive-form', function () {
                if($(this).closest("tr.child").length > 0){
                    var parentRow = $(this).closest("tr").prev()[0];
                    self.activeStrucuredForms = self.dTable.row( parentRow ).data();
                }
            });

            $('#form-list-dt tbody').on('click', '#history', ( e) => {
                e.preventDefault();
                this.showHistoryModal = true;
                this.showHistory = true;
          
              });
             
            $('#form-list-dt tbody').on('click', '.reminder-details', (e) => {
               
                e.preventDefault();
                this.showHistoryModal = true;
                this.showDraftHistory = true;
               
            });
            $('#form-list-dt tbody').off('click', 'div.btn-group > .edit-dform');
            $('#form-list-dt tbody').on('click', 'div.btn-group > .edit-dform', () => {
                if($(this).closest("tr.child").length > 0){
                    var parentRow = $(this).closest("tr").prev()[0];
                    self.activeStrucuredForms = self.dTable.row( parentRow ).data();
                }
                this.activeStrucuredForms.fromName = this.activeStrucuredForms.fname + ' ' + this.activeStrucuredForms.lname;
                this.activeStrucuredForms.fromName = this.activeStrucuredForms.fname + ' ' + this.activeStrucuredForms.lname;
                this.activeStrucuredForms.formId = this.activeStrucuredForms.form_id;
                this.activeStrucuredForms.fromId = this.activeStrucuredForms.from_id;
                this.activeStrucuredForms.sentId = this.activeStrucuredForms.sent_id;
                this.activeStrucuredForms.formName = this.activeStrucuredForms.form_name;
                this.activeStrucuredForms.categoryvalue = 1;
                this.activeStrucuredForms.toName = this.activeStrucuredForms.createdUser;
                this.activeStrucuredForms.createdUserName = this.activeStrucuredForms.createdUser;
                this.activeStrucuredForms.fromUsername = this.activeStrucuredForms.createdUserEmail;
                if (this.activeStrucuredForms.form_type == "Staff Facing") {
                    this.activeStrucuredForms.staff_facing = 1;
                } else {
                    this.activeStrucuredForms.staff_facing = 0;
                }
                this.activeStrucuredForms.entryId = this.activeStrucuredForms.form_submission_id;

                var formId = this.activeStrucuredForms.form_id ? this.activeStrucuredForms.form_id : this.activeStrucuredForms.formId;
                this._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)), 1);
                localStorage.setItem('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)));
                if (this.activeStrucuredForms.allow_edit && this.activeStrucuredForms.allow_edit == 1) {
                    this.router.navigate(['forms/edit-form/' + formId + '/' + this.activeStrucuredForms.form_submission_id]);
                } else if (this.activeStrucuredForms.formStatus == "Draft") {
                    this.router.navigate(['forms/edit-form/' + formId + '/' + this.activeStrucuredForms.form_submission_id]);
                } else {
                    this.viewForm(this.activeStrucuredForms);
                }
            });

            $('#form-list-dt tbody').off('click', 'div.btn-group > .view-forms');
            $('#form-list-dt tbody').on('click', 'div.btn-group > .view-forms', function () {
                 if($(this).closest("tr.child").length > 0){
                    var parentRow = $(this).closest("tr").prev()[0];
                    self.activeStrucuredForms = self.dTable.row( parentRow ).data();
                }
                if (self.dataTableLoading == false || this.dataTableLoading == false) {
                    if (this.activeStrucuredForms && ((this.activeStrucuredForms.allow_edit && this.activeStrucuredForms.allow_edit == 1) && this.isActive == "pending" && ((this.userData.userId == this.activeStrucuredForms.recipient_id && this.activeStrucuredForms.staffFacing != 1) || (this.userData.userId == this.activeStrucuredForms.from_id && this.activeStrucuredForms.staffFacing == 1)))) {
                        this.activeStrucuredForms.page = "myFormWorlists";
                        this.activeStrucuredForms.fromName = this.activeStrucuredForms.fname + ' ' + this.activeStrucuredForms.lname;
                        this.activeStrucuredForms.formId = this.activeStrucuredForms.form_id;
                        this.activeStrucuredForms.fromId = this.activeStrucuredForms.from_id;
                        this.activeStrucuredForms.sentId = this.activeStrucuredForms.sent_id;
                        this.activeStrucuredForms.formName = this.activeStrucuredForms.form_name;
                        this.activeStrucuredForms.categoryvalue = 1;
                        this.activeStrucuredForms.loginUserId = this.userData.userId;
                        this.activeStrucuredForms.toName = this.activeStrucuredForms.createdUser;
                        this.activeStrucuredForms.createdUserName = this.activeStrucuredForms.createdUser;
                        this.activeStrucuredForms.fromUsername = this.activeStrucuredForms.createdUserEmail;
                        if (this.activeStrucuredForms.form_type == "Staff Facing") {
                            this.activeStrucuredForms.staff_facing = 1;
                        } else {
                            this.activeStrucuredForms.staff_facing = 0;
                        }
                        this.activeStrucuredForms.entryId = this.activeStrucuredForms.form_submission_id;
                        var formId = this.activeStrucuredForms.form_id ? this.activeStrucuredForms.form_id : this.activeStrucuredForms.formId;
                        self._formsService.checkIsFormFilled(self.activeStrucuredForms).then(function (result: any) {
                            self._sharedService.updateUserInboxCounts.emit({
                                getCount: true,
                                type: 'forms'
                            });
                        });

                        this._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)), 1);
                        localStorage.setItem('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)));

                        this.viewForm(this.activeStrucuredForms);
                    } else {
                        self.activeStrucuredForms.fromName = self.activeStrucuredForms.fname + ' ' + self.activeStrucuredForms.lname;
                        self.activeStrucuredForms.formId = self.activeStrucuredForms.form_id;
                        self.activeStrucuredForms.fromId = self.activeStrucuredForms.from_id;
                        self.activeStrucuredForms.sentId = self.activeStrucuredForms.sent_id;
                        self.activeStrucuredForms.formName = self.activeStrucuredForms.form_name;
                        self.activeStrucuredForms.categoryvalue = 1;
                        self.activeStrucuredForms.loginUserId = self.userData.userId;
                        self.activeStrucuredForms.toName = self.activeStrucuredForms.createdUser;
                        self.activeStrucuredForms.createdUserName = self.activeStrucuredForms.createdUser;
                        self.activeStrucuredForms.fromUsername = self.activeStrucuredForms.createdUserEmail;
                        self.activeStrucuredForms.entryId = self.activeStrucuredForms.form_submission_id;
                        self._formsService.checkIsFormFilled(self.activeStrucuredForms).then(function (result: any) {
                            self._sharedService.updateUserInboxCounts.emit({
                                getCount: true,
                                type: 'forms'
                            });
                        });
                        var tr = $(this).closest('tr');
                        var row = x.row(tr);
                        if (row.data()) {
                            if (row.child.isShown()) {
                                //This row is already open - close it
                                row.child.hide();
                                tr.removeClass('shown');
                            } else {
                                //Open this row
                                row.child(format(row.data())).show();
                                tr.addClass('shown');
                            }
                        } else {
                            var tr = $('[id="row-' + self.activeStrucuredForms.id + '"]');
                            var row = x.row(tr);
                            if (row.child.isShown()) {
                                //This row is already open - close it
                                row.child.hide();
                                tr.removeClass('shown');
                                row.child(format(row.data())).show();
                                tr.addClass('shown');
                            } else {
                                //Open this row
                                row.child(format(row.data())).show();
                                tr.addClass('shown');
                            }
                        }
                    }
                }
            });

            $('#form-list-dt_wrapper #form-list-dt tbody').off('click', 'td.view-form');
            $('#form-list-dt_wrapper #form-list-dt tbody').on('click', 'td.view-form', function () {
                if($(this).closest("tr.child").length > 0){
                    var parentRow = $(this).closest("tr").prev()[0];
                    self.activeStrucuredForms = self.dTable.row( parentRow ).data();
                }
                if (self.dataTableLoading == false || this.dataTableLoading == false) {

                    if (this.activeStrucuredForms && ((this.activeStrucuredForms.allow_edit && this.activeStrucuredForms.allow_edit == 1) && this.isActive == "pending" && ((this.userData.userId == this.activeStrucuredForms.recipient_id && this.activeStrucuredForms.staffFacing != 1) || (this.userData.userId == this.activeStrucuredForms.from_id && this.activeStrucuredForms.staffFacing == 1)))) {
                        this.activeStrucuredForms.page = "myFormWorlists";
                        this.activeStrucuredForms.fromName = this.activeStrucuredForms.fname + ' ' + this.activeStrucuredForms.lname;
                        this.activeStrucuredForms.formId = this.activeStrucuredForms.form_id;
                        this.activeStrucuredForms.fromId = this.activeStrucuredForms.from_id;
                        this.activeStrucuredForms.sentId = this.activeStrucuredForms.sent_id;
                        this.activeStrucuredForms.formName = this.activeStrucuredForms.form_name;
                        this.activeStrucuredForms.categoryvalue = 1;
                        this.activeStrucuredForms.loginUserId = this.userData.userId;
                        this.activeStrucuredForms.toName = this.activeStrucuredForms.createdUser;
                        this.activeStrucuredForms.createdUserName = this.activeStrucuredForms.createdUser;
                        this.activeStrucuredForms.fromUsername = this.activeStrucuredForms.createdUserEmail;
                        if (this.activeStrucuredForms.form_type == "Staff Facing") {
                            this.activeStrucuredForms.staff_facing = 1;
                        } else {
                            this.activeStrucuredForms.staff_facing = 0;
                        }
                        this.activeStrucuredForms.entryId = this.activeStrucuredForms.form_submission_id;
                        var formId = this.activeStrucuredForms.form_id ? this.activeStrucuredForms.form_id : this.activeStrucuredForms.formId;
                        self._formsService.checkIsFormFilled(self.activeStrucuredForms).then(function (result: any) {
                            self._sharedService.updateUserInboxCounts.emit({
                                getCount: true,
                                type: 'forms'
                            });
                        });

                        this._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)), 1);
                        localStorage.setItem('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)));

                        this.viewForm(this.activeStrucuredForms);
                    } else {
                        var tr = $(this).closest('tr');
                        var row = x.row(tr);
                        if (row.child.isShown()) {
                            //This row is already open - close it
                            row.child.hide();
                            tr.removeClass('shown');
                        } else {
                            //Open this row
                            row.child(format(row.data())).show();
                            tr.addClass('shown');
                        }
                    }
                }
            });

            $('#form-list-dt_wrapper #form-list-dt tbody').off('click', 'td.view-form');
            $('#form-list-dt_wrapper #form-list-dt tbody').on('click', 'td.view-form', function () {
                if($(this).closest("tr.child").length > 0){
                    var parentRow = $(this).closest("tr").prev()[0];
                    self.activeStrucuredForms = self.dTable.row( parentRow ).data();
                }
                if (this.activeStrucuredForms && ((this.activeStrucuredForms.allow_edit && this.activeStrucuredForms.allow_edit == 1) && this.isActive == "pending" && ((this.userData.userId == this.activeStrucuredForms.recipient_id && this.activeStrucuredForms.staffFacing != 1) || (this.userData.userId == this.activeStrucuredForms.from_id && this.activeStrucuredForms.staffFacing == 1)))) {
                    this.activeStrucuredForms.page = "myFormWorlists";
                    this.activeStrucuredForms.fromName = this.activeStrucuredForms.fname + ' ' + this.activeStrucuredForms.lname;
                    this.activeStrucuredForms.formId = this.activeStrucuredForms.form_id;
                    this.activeStrucuredForms.fromId = this.activeStrucuredForms.from_id;
                    this.activeStrucuredForms.sentId = this.activeStrucuredForms.sent_id;
                    this.activeStrucuredForms.formName = this.activeStrucuredForms.form_name;
                    this.activeStrucuredForms.categoryvalue = 1;
                    self.activeStrucuredForms.loginUserId = self.userData.userId;
                    this.activeStrucuredForms.toName = this.activeStrucuredForms.createdUser;
                    this.activeStrucuredForms.createdUserName = this.activeStrucuredForms.createdUser;
                    this.activeStrucuredForms.fromUsername = this.activeStrucuredForms.createdUserEmail;
                    if (this.activeStrucuredForms.form_type == "Staff Facing") {
                        this.activeStrucuredForms.staff_facing = 1;
                    } else {
                        this.activeStrucuredForms.staff_facing = 0;
                    }
                    this.activeStrucuredForms.entryId = this.activeStrucuredForms.form_submission_id;
                    var formId = this.activeStrucuredForms.form_id ? this.activeStrucuredForms.form_id : this.activeStrucuredForms.formId;
                    this._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)), 1);
                    localStorage.setItem('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)));

                    this.viewForm(this.activeStrucuredForms);
                } else {
                    var tr = $(this).closest('tr');
                    var row = x.row(tr);
                    if (row.child.isShown()) {
                        //This row is already open - close it
                        row.child.hide();
                        tr.removeClass('shown');
                    } else {
                        //Open this row
                        row.child(format(row.data())).show();
                        tr.addClass('shown');
                    }
                }
            });

            $('#form-list-dt_wrapper #form-list-dt tbody').off('click', 'td.submit-form');
            $('#form-list-dt_wrapper #form-list-dt tbody ').on('click', 'td.submit-form', function () {
                if($(this).closest("tr.child").length > 0){
                    var parentRow = $(this).closest("tr").prev()[0];
                    self.activeStrucuredForms = self.dTable.row( parentRow ).data();
                }
                if (self.dataTableLoading == false || this.dataTableLoading == false) {
                    self.activeStrucuredForms.page = "myFormWorlists";
                    self.activeStrucuredForms.fromName = self.activeStrucuredForms.fname + ' ' + self.activeStrucuredForms.lname;
                    self.activeStrucuredForms.formId = self.activeStrucuredForms.form_id;
                    self.activeStrucuredForms.fromId = self.activeStrucuredForms.from_id;
                    self.activeStrucuredForms.sentId = self.activeStrucuredForms.sent_id;
                    self.activeStrucuredForms.formName = self.activeStrucuredForms.form_name;
                    self.activeStrucuredForms.categoryvalue = 1;
                    self.activeStrucuredForms.loginUserId = self.userData.userId;
                    self.activeStrucuredForms.toName = self.activeStrucuredForms.createdUser;
                    self.activeStrucuredForms.createdUserName = self.activeStrucuredForms.createdUser;
                    self.activeStrucuredForms.fromUsername = self.activeStrucuredForms.createdUserEmail;
                    if (self.activeStrucuredForms.form_type == "Staff Facing") {
                        self.activeStrucuredForms.staff_facing = 1;
                    } else {
                        self.activeStrucuredForms.staff_facing = 0;
                    }
                    self.activeStrucuredForms.entryId = self.activeStrucuredForms.form_submission_id;
                    var formId = self.activeStrucuredForms.form_id ? self.activeStrucuredForms.form_id : self.activeStrucuredForms.formId;
                    self._formsService.checkIsFormFilled(self.activeStrucuredForms).then(function (result: any) {
                        self._sharedService.updateUserInboxCounts.emit({
                            getCount: true,
                            type: 'forms'
                        });
                    });
                    self._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(self.activeStrucuredForms)), 1);
                    localStorage.setItem('formData', encodeURIComponent(JSON.stringify(self.activeStrucuredForms)));
                    self.router.navigate(['/forms/view-form']);
                }
            });

            $("#form-list-dt_wrapper #form-list-dt tbody").off('click', '#inte-status-not-sftp');
            $('#form-list-dt_wrapper #form-list-dt tbody').on('click', '#inte-status-not-sftp', function () {
                        let ids = 'inte-status-not-sftp-' + self.activeStrucuredForms.senton;
                        self.popoverNotSftp(ids, 'loader');
                        self._formsService.getIntegrationDetails(
                            'not-sftp', 
                            self.activeStrucuredForms.request_id,
                            self.activeStrucuredForms.tenant_id,
                            self.activeStrucuredForms.recipient_id,
                            ).then((row) => {
                        self.integrationDetails = row;
                        self.integrationDetails.type = 'inte-status-not-sftp';
                        self.popoverNotSftp(ids, 'show');
                       });
                return false;
            });

            $("#form-list-dt_wrapper #form-list-dt tbody").off('click', '#inte-status-not-sftp-discrete');
            $('#form-list-dt_wrapper #form-list-dt tbody').on('click', '#inte-status-not-sftp-discrete', function () {
                        let ids = 'inte-status-not-sftp-discrete-' + self.activeStrucuredForms.senton;
                        self.popoverNotSftp(ids, 'loader');
                        self._formsService.getIntegrationDetails(
                            'not-sftp', 
                            self.activeStrucuredForms.discrete_request_id,
                            self.activeStrucuredForms.tenant_id,
                            self.activeStrucuredForms.recipient_id,
                            ).then((row) => {
                        self.integrationDetails = row;
                        self.integrationDetails.type = 'inte-status-not-sftp-discrete';
                        self.popoverNotSftp(ids, 'show');
                        });
                return false;
            });

            $("#form-list-dt_wrapper #form-list-dt tbody").off('click', '#inte-status-not-sftp-form');
            $('#form-list-dt_wrapper #form-list-dt tbody').on('click', '#inte-status-not-sftp-form', function () {
                        if(self.isActive == "pending"){
                          self.activeStrucuredForms.tag_id = self.activeStrucuredForms.tagId;
                        }
                        let ids = 'inte-status-not-sftp-form-' + self.activeStrucuredForms.tag_id+'-'+self.activeStrucuredForms.id;
                        self.popoverNotSftp(ids, 'loader');
                        if(self.activeStrucuredForms.tag_id){
                        self._formsService.getDocumentTypeDetails(self.activeStrucuredForms.tag_id
                            ).then((row) => {
                                 self.documentTagDetails = row['getSessionTenant']['formTypeDetails'];
                                 if(self.documentTagDetails && self.documentTagDetails.id)
                                 self.popoverNotSftpForm(ids, 'show');
                                 else
                                 self.popoverNotSftpForm(ids, 'showNill');
                            });
                        }else{
                            self.popoverNotSftpForm(ids, 'showNill');
                       }
                return false;
            });

            $("#form-list-dt_wrapper #form-list-dt tbody").off('click', '.close-tooltip');
            $('#form-list-dt_wrapper #form-list-dt tbody').on('click', '.close-tooltip', function () {
                $(this).closest('.inte-popover').hide();
                return false;
            });

            $("#form-list-dt_wrapper #form-list-dt tbody").off('click', '#int-copy-sent');
            $('#form-list-dt_wrapper #form-list-dt tbody').on('click', '#int-copy-sent', function () {
                self.CopyToClipboard('inte-status-not-sftp-'+ self.activeStrucuredForms.senton+'-int-content');
                return false;
            });

            $("#form-list-dt_wrapper #form-list-dt tbody").off('click', '#int-copy-sent-discrete');
            $('#form-list-dt_wrapper #form-list-dt tbody').on('click', '#int-copy-sent-discrete', function () {
                self.CopyToClipboard('inte-status-not-sftp-discrete-'+ self.activeStrucuredForms.senton+'-int-content');
                return false;
            });
            $("#form-list-dt_wrapper #form-list-dt tbody").off('click', '#int-copy-receive');
            $('#form-list-dt_wrapper #form-list-dt tbody').on('click', '#int-copy-receive', function () {
                self.CopyToClipboard('int-content-receive-' + self.activeStrucuredForms.senton);
                return false;
            });

            $("#form-list-dt_wrapper #form-list-dt tbody").off('click', '.inte-popover');
            $('#form-list-dt_wrapper #form-list-dt tbody').on('click', '.inte-popover', function () {
                return false;
            });

            $('#form-list-dt_wrapper #form-list-dt tbody').off('click', 'td.view');
            $('#form-list-dt_wrapper #form-list-dt tbody').on('click', 'td.view', function () {
                if($(this).closest("tr.child").length > 0){
                    var parentRow = $(this).closest("tr").prev()[0];
                    self.activeStrucuredForms = self.dTable.row( parentRow ).data();
                }
                if (self.dataTableLoading == false || this.dataTableLoading == false) {
                    self.activeStrucuredForms.page = "myFormWorlists";
                    self.activeStrucuredForms.fromName = self.activeStrucuredForms.fname + ' ' + self.activeStrucuredForms.lname;
                    self.activeStrucuredForms.formId = self.activeStrucuredForms.form_id;
                    self.activeStrucuredForms.fromId = self.activeStrucuredForms.from_id;
                    self.activeStrucuredForms.sentId = self.activeStrucuredForms.sent_id;
                    self.activeStrucuredForms.formName = self.activeStrucuredForms.form_name;
                    self.activeStrucuredForms.categoryvalue = 1;
                    self.activeStrucuredForms.toName = self.activeStrucuredForms.createdUser;
                    self.activeStrucuredForms.createdUserName = self.activeStrucuredForms.createdUser;
                    self.activeStrucuredForms.fromUsername = self.activeStrucuredForms.createdUserEmail;
                    self.activeStrucuredForms.loginUserId = self.userData.userId;
                    if (self.activeStrucuredForms.form_type == "Staff Facing") {
                        self.activeStrucuredForms.staff_facing = 1;
                    } else {
                        self.activeStrucuredForms.staff_facing = 0;
                    }
                    self.activeStrucuredForms.entryId = self.activeStrucuredForms.form_submission_id;
                    self.activeStrucuredForms.unreadCount = 0;
                    self.activeStrucuredForms.unread = 0;
                    var savaAsDraftStaff = 0;
                    var savaAsDraftPatient = 0;
                    if (self.userDataConfig.enable_form_save_draft_patient == 1 && self.activeStrucuredForms.enableSaveDraftPatient == 1) {
                        savaAsDraftPatient = 1;
                    }
                    if (self.userDataConfig.enable_form_save_draft_staff == 1 && self.activeStrucuredForms.enableSaveDraftStaff == 1) {
                        savaAsDraftStaff = 1;
                    }
                    self.activeStrucuredForms.savaAsDraftStaff = savaAsDraftStaff;
                    self.activeStrucuredForms.savaAsDraftPatient = savaAsDraftPatient;
                    self._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(self.activeStrucuredForms)), 1);
                    localStorage.setItem('formData', encodeURIComponent(JSON.stringify(self.activeStrucuredForms)));
                    self._formsService.checkIsFormFilled(self.activeStrucuredForms).then(function (result: any) {
                        self._sharedService.updateUserInboxCounts.emit({
                            getCount: true,
                            type: 'forms'
                        });
                    });
                    self.router.navigate(['/forms/list/view']);
                }
            });

            $('#form-list-dt tbody').off('click', 'div.btn-group > .views');
            $('#form-list-dt tbody').on('click', 'div.btn-group > .views', function () {
                if($(this).closest("tr.child").length > 0){
                    var parentRow = $(this).closest("tr").prev()[0];
                    self.activeStrucuredForms = self.dTable.row( parentRow ).data();
                }
                if (self.dataTableLoading == false || this.dataTableLoading == false) {
                    self.activeStrucuredForms.page = "myFormWorlists";
                    self.activeStrucuredForms.fromName = self.activeStrucuredForms.fname + ' ' + self.activeStrucuredForms.lname;
                    self.activeStrucuredForms.formId = self.activeStrucuredForms.form_id;
                    self.activeStrucuredForms.fromId = self.activeStrucuredForms.from_id;
                    self.activeStrucuredForms.sentId = self.activeStrucuredForms.sent_id;
                    self.activeStrucuredForms.formName = self.activeStrucuredForms.form_name;
                    self.activeStrucuredForms.categoryvalue = 1;
                    self.activeStrucuredForms.toName = self.activeStrucuredForms.createdUser;
                    self.activeStrucuredForms.createdUserName = self.activeStrucuredForms.createdUser;
                    self.activeStrucuredForms.fromUsername = self.activeStrucuredForms.createdUserEmail;
                    self.activeStrucuredForms.loginUserId = self.userData.userId;
                    if (self.activeStrucuredForms.form_type == "Staff Facing") {
                        self.activeStrucuredForms.staff_facing = 1;
                    } else {
                        self.activeStrucuredForms.staff_facing = 0;
                    }
                    self.activeStrucuredForms.entryId = self.activeStrucuredForms.form_submission_id;
                    self.activeStrucuredForms.unreadCount = 0;
                    self.activeStrucuredForms.unread = 0;
                    var savaAsDraftStaff = 0;
                    var savaAsDraftPatient = 0;
                    if (self.userDataConfig.enable_form_save_draft_patient == 1 && self.activeStrucuredForms.enableSaveDraftPatient == 1) {
                        savaAsDraftPatient = 1;
                    }
                    if (self.userDataConfig.enable_form_save_draft_staff == 1 && self.activeStrucuredForms.enableSaveDraftStaff == 1) {
                        savaAsDraftStaff = 1;
                    }
                    self.activeStrucuredForms.savaAsDraftStaff = savaAsDraftStaff;
                    self.activeStrucuredForms.savaAsDraftPatient = savaAsDraftPatient;
                    self._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(self.activeStrucuredForms)), 1);
                    localStorage.setItem('formData', encodeURIComponent(JSON.stringify(self.activeStrucuredForms)));
                    self._formsService.checkIsFormFilled(self.activeStrucuredForms).then(function (result: any) {
                        self._sharedService.updateUserInboxCounts.emit({
                            getCount: true,
                            type: 'forms'
                        });
                    });
                    self.router.navigate(['/forms/list/view']);
                }
            });
        });
        }
    }
    getSearchKey(isActive: string): string {
        switch (isActive) {
            case 'completed':
                return Store.SEARCH_MY_FORM_WORK_LIST_COMPLETED;
            case 'pending':
                return Store.SEARCH_MY_FORM_WORK_LIST_PENDING;
            case 'archived':
                return Store.SEARCH_MY_FORM_WORK_LIST_ARCHIVED;
            case 'draft':
                return Store.SEARCH_MY_FORM_WORK_LIST_DRAFTS;
            default:
                return Store.SEARCH_MY_FORM_WORK_LIST_COMPLETED;
        }
    }
    popoverNotSftp(ids, action) {
        if (action == 'loader') {
    
          $('.inte-popover').hide();
          $('#' + ids).show();
          $('#' + ids + ' > .pop-content > .int-loader').show();
          $('#' + ids + ' > .pop-content > .int-loader').css("display", "block");
          $('#' + ids + ' > .pop-content > .int-main-content').hide();
        } else if (action == 'show') {
          this.fillPopOverNotSftp(ids);
          $('#' + ids + ' > .pop-content > .int-loader').hide();
          $('#' + ids + ' > .pop-content > .int-main-content').show();
        } else if (action == "close") {
          $('#' + ids + ' > .pop-content > .int-loader').hide();
          $('#' + ids + ' > .pop-content > .int-main-content').hide();
          $('#' + ids).hide();
        }
      }
    
    fillPopOverNotSftp(ids) {
      let outputHtml = '';
      if(this.integrationDetails.type == 'inte-status-not-sftp' || this.integrationDetails.type == 'inte-status-not-sftp-discrete'){
        let response = this.integrationDetails.response;
        let diagnostics = (response.hasOwnProperty("diagnostics")) ? response.diagnostics : {};

        if(response.hasOwnProperty("integration_status") && typeof(response.integration_status) != 'undefined'){
            var status_message = (response.integration_status == 'Pending') ? 'Waiting for response from CHIE' : response.integration_status;
            outputHtml += `Status: ${status_message}<br />`;
          }
      
          if(response.hasOwnProperty("mrn") && typeof(response.mrn) != 'undefined'){
            outputHtml += `MRN: ${response.mrn}<br />`;
          }
      
          if(response.hasOwnProperty("reference_id") && typeof(response.reference_id) != 'undefined'){
            outputHtml += `Reference ID: ${response.reference_id}<br />`;
          }
      
          if(response.hasOwnProperty("processedAt") && typeof(response.processedAt) != 'undefined'){
            outputHtml += `Processed At: ${response.processedAt}`;
          }
      }

      $('#'+ids+'-int-content').html(outputHtml);
    }

    CopyToClipboard(containerid) {
        let para = $('#' + containerid)[0] as HTMLInputElement;
        this.selectElementText(para)
        if (window.getSelection) { // all modern browsers and IE9+
            let selectedText = window.getSelection().toString()
            const el = document.createElement('textarea');
            el.value = selectedText;
            document.body.appendChild(el);
            el.select();
            document.execCommand('copy');
            document.body.removeChild(el);
            this.selectElementText(para);
        }
    }

    selectElementText(el) {
        var range = document.createRange() // create new range object
        range.selectNodeContents(el) // set range to encompass desired element text
        var selection = window.getSelection() // get Selection object from currently user selected text
        selection.removeAllRanges() // unselect any user selected text (if any)
        selection.addRange(range) // add range to Selection object to select it
    }

    viewForm(message) {
        message.page = "myFormWorlists";
        this._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(message)), 1);
        localStorage.setItem('formData', encodeURIComponent(JSON.stringify(message)));
        /* this._formsService.checkIsFormFilled(message).then(function(result: any){
        }); */
        if (message.form_submission_id && message.form_submission_id != 0) {
            this.router.navigate(['/forms/list/view']);
        } else {
            this.router.navigate(['/forms/view-form']);
        }
    }

    viewStrucuturedForm() {
        this._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)), 1);
        localStorage.setItem('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)));
        this.router.navigate(['/forms/list/view']);
    }

    getPdfTaggedForm() {
        console.log("From My form list");
        var patientIdForPdf = this.activeStrucuredForms.patient_id;
        if(typeof this.activeStrucuredForms.associated_user_id !== 'undefined' && this.activeStrucuredForms.associated_user_id !='')
        {
             patientIdForPdf = this.activeStrucuredForms.associated_user_id;
        }
        if(this.activeStrucuredForms.facing_new ==0 && this.activeStrucuredForms.patient_id ==0)
        {
             patientIdForPdf = this.activeStrucuredForms.from_id;
        }

        var newWindow: any = window.open(this._structureService.serverBaseUrl + "/webapp/www/img/gif-loader.gif");
        var downloadTime = moment((moment().unix()) * 1000).format('MMMM DD, YYYY h:mm a');
        if (this.activeStrucuredForms.updatedtimestamp == null) {
            var data = {
                "patient_id": this.activeStrucuredForms.patient_id,
                "patient_user": patientIdForPdf,
                "generate_send_id": this.activeStrucuredForms.sent_id,
                "userid": this.activeStrucuredForms.userid,
                "formId": this.activeStrucuredForms.form_id,
                "submissionId": this.activeStrucuredForms.form_submission_id,
                "tenantId": this.userData.tenantId,
                "tenantName": this.userData.tenantName,
                "createdOn": moment(this.activeStrucuredForms.senton * 1000).format('MMMM DD, YYYY h:mm a'),
                "sendOn": moment(this.activeStrucuredForms.sentDate * 1000).format('MMMM DD, YYYY h:mm a'),
                "downloadTime": downloadTime
            }; //this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton) * 1000)
        } else {
            var data = {
                "patient_id": this.activeStrucuredForms.patient_id,
                "patient_user": patientIdForPdf,
                "generate_send_id": this.activeStrucuredForms.sent_id,
                "userid": this.activeStrucuredForms.userid,
                "formId": this.activeStrucuredForms.form_id,
                "submissionId": this.activeStrucuredForms.form_submission_id,
                "tenantId": this.userData.tenantId,
                "tenantName": this.userData.tenantName,
                "createdOn": moment(this.activeStrucuredForms.senton * 1000).format('MMMM DD, YYYY h:mm a'),
                "sendOn": moment(this.activeStrucuredForms.updatedtimestamp * 1000).format('MMMM DD, YYYY h:mm a'),
                "downloadTime": downloadTime
            }; //this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton) * 1000)
        }
        this._formsService.generateTaggedFormReportPdf(data, 2, timezone.name()).then((result) => {
            var fileName: any = '';
            fileName = result;
            var fileUrl  = this._structureService.apiBaseUrl +'/citus-health/v4/form-download.php?filetoken='+fileName._body;  
            newWindow.location = fileUrl;
        }).catch((ex) => {
        });
    }

    download(fileUrl, fileName) {
        window.open(fileUrl, "_blank");
    }
    getSiteIds(data: any): void {
       this.siteIds = data.siteId;
       var type = (this._sharedService.myFormsType && this._sharedService.myFormsType != "") ? this._sharedService.myFormsType : 'COMPLETED';
        if (this._structureService.allowEditFormSuccess == true) type = 'COMPLETED';

        if(this._structureService.getCookie('tabname') != ""){
           this._sharedService.myFormsType = this._structureService.getCookie('tabname');
           var type = this._sharedService.myFormsType;
           this._structureService.deleteCookie('tabname');

        }
        this.changeFormData(type);
        this.getMyWorklistCount();
    }
    hideDropdown(hideItem : any){
        this.hideSiteSelection = hideItem.hideItem;
    }
    popoverNotSftpForm(ids, action) {
        if (action == 'loader') {

             $('.inte-popover').hide();
             $('#' + ids).show();
             $('#' + ids + ' > .pop-content-form > .int-loader').show();
             $('#' + ids + ' > .pop-content-form > .int-loader').css("display", "block");
             $('#' + ids + ' > .pop-content-form > .int-main-content-form').hide();
        } else if (action == 'show') {
             this.fillPopOverNotSftpForm(ids);
             $('#' + ids + ' > .pop-content-form > .int-loader').hide();
             $('#' + ids + ' > .pop-content-form > .int-main-content-form').show();
        } else if (action == "close") {
             $('#' + ids + ' > .pop-content-form > .int-loader').hide();
             $('#' + ids + ' > .pop-content-form > .int-main-content-form').hide();
             $('#' + ids).hide();
            } else if (action == 'showNill') {
                this.fillPopOverNotSftpForm("");
                $('#' + ids + ' > .pop-content-form > .int-loader').hide();
                $('#' + ids + ' > .pop-content-form > .int-main-content-form').show();
           }
   }

   fillPopOverNotSftpForm(ids="") {
        let outputHtml = '';
        if(ids == ""){
            outputHtml +=  `No Form Type Associated <br />`;
            $('#int-content-form-' + this.activeStrucuredForms.tag_id+'-'+this.activeStrucuredForms.id).html(outputHtml);
       }else{
             let response = this.documentTagDetails;
             if (response.hasOwnProperty("tagName") && typeof(response.tagName) != 'undefined') {
                  outputHtml += `Form Type: ${response.tagName}<br /> <br><b>Integration Details</b></br>`;
             }
             var integrationStatus = 0;
             if (response.hasOwnProperty("tagMeta")) {
                  let tagMeta = response.tagMeta;
                  if (tagMeta.hasOwnProperty("directlinkpatientchart") && tagMeta.directlinkpatientchart) {
                    integrationStatus=1;
                  outputHtml += `Enable Direct Linking to Patient Chart: ${tagMeta.directlinkpatientchartTriggerOn}<br />`;
                  }
                  if (tagMeta.hasOwnProperty("externalFileDisclosePHI") && tagMeta.externalFileDisclosePHI) {
                    integrationStatus=1;
                       outputHtml += `Enable Disclose PHI with External Systems: ${tagMeta.externalFileDisclosePHITriggerOn}<br />`;
                  }
                  if (tagMeta.hasOwnProperty("progressNoteIntegration") && tagMeta.progressNoteIntegration) {
                    integrationStatus=1;
                       outputHtml += `Progress Note Integration: ${tagMeta.progressNoteIntegrationTriggerOn}<br />`;
                  }
                  if (tagMeta.hasOwnProperty("sendformdatajson") && tagMeta.sendformdatajson) {
                    integrationStatus=1;
                       outputHtml += `Enable Sending Form Data to Third Party Application: ${tagMeta.sendformdatajsonTriggerOn}<br />`;
                  }
                  if (tagMeta.hasOwnProperty("FaxQIntegration") && tagMeta.FaxQIntegration) {
                    integrationStatus=1;
                       outputHtml += `Enable FaxQueue: ${tagMeta.faxQIntegrationTriggerOn}<br />`;
                  }
                  if (tagMeta.hasOwnProperty("externalFileExchangeWebhook") && tagMeta.externalFileExchangeWebhook) {
                    integrationStatus=1;
                       outputHtml += `Enable Document Exchange: ${tagMeta.externalFileExchangeTriggerOn}<br />`;
                  }
                  if(integrationStatus== 0){
                    outputHtml += `Nil<br />`;
                  }
             }
        $('#int-content-form-' + this.activeStrucuredForms.tag_id+'-'+this.activeStrucuredForms.id).html(outputHtml);
     }
   }

    applyAdvanceSearch(searchValues) {
        this.advanceSearchInput = searchValues;
        if(Object.keys(this.advanceSearchInput).length > 0){
            this.populateData(this.isActive, this.isScheduled, "refresh");
        } else {
            this.dTable.search("").draw();
        }
    }
    setAdvanceSearchForm(refresh) {
        if(refresh != 'polling' && refresh != '') {
            this.advanceSearchInput = {};
            this.dynamicControls = this._structureService.setAdvanceSearchControls('myform',this.isActive);
            this.resetAdvanceSearchForm = false;
       }
    }
   setWebHookTime(element) :any{
    return ((this.formpPipe.transform(parseInt(element.created_on) * 1000).length >= 9) ? " on " : " at ") +
    this.formpPipe.transform(parseInt(element.created_on) * 1000);
   }
   
    closeHistoryModal(e) {
        if (e == true) {
            this.showHistoryModal = false;
            this.showHistory = false;
        }
    }
    closeDraftHistoryModal(e) {
        if (e == true) {
            this.showHistoryModal = false;
            this.showDraftHistory = false;
        }
    }
    /**
     * Function to reload the data table when date range is selected
     */
    onSelectDateRange(event) {
        if (event.type) {
            this.dateRangeType = event.type;
        }
       if (!isBlank(event.changeDetectedOnInit)) {
            this.showFilterAppliedMessage = event.changeDetectedOnInit;
        } else {
            this.populateData(this.isActive, this.isScheduled, "refresh");
            this.getMyWorklistCount();
        }
    }
}
