import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ement<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ViewChild, Renderer2 } from '@angular/core';
import { FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import {
  advancedFilterCount,
  CONSTANTS,
  DateRanges,
  FormSendMode,
  IntegrationType,
  LISTING_PAGE_DATERANGE_OPTIONS,
  ModuleName,
  Status,
  UserRoles
} from 'app/constants/constants';
import { HttpService } from 'app/services/http/http.service';
import { APIs } from 'app/constants/apis';
import { SearchService } from 'app/services/search-criteria/search.service';
import { FormpPipe } from './formp.pipe';
import { StructureService } from '../structure.service';
import { FormsService } from './forms.service';
import { SharedService } from '../shared/sharedServices';
import { ToolTipService } from '../tool-tip.service';
import { Guid } from "guid-typescript";
import { Subject, Subscription } from 'rxjs';
import { ControlType } from '../shared/advance-search/control-type';
import { NGXLogger } from 'ngx-logger';
import { Store, StoreService } from '../shared/storeService';
import { StaticDataService } from '../static-data.service';
import { SaveSearch, UpdateSearch } from '../../models/configuration/searchCriteria';
import { AdvanceSearchComponent } from '../shared/advance-search/advance-search.component';

const jstz = require('jstz');
declare const $: any;
declare const swal: any;
const timezone = jstz.determine();
declare const NProgress: any;
let moment = require('moment/moment');
import { isBlank } from 'app/utils/utils';
@Component({
     selector: 'list_form_data',
     templateUrl: './list-submitted-forms.html',
     styleUrls: ['./forms-listing.component.scss']
})
// TODO: Need to Revamp the entire component. Contains lots of duplicate codes, jquery functions etc.  
export class FormsListingComponent implements OnInit, OnDestroy {
     formsUrl: any;
     reminderDetailsLoaded: boolean;
     reminderDetails: any = [];
     reminderDetailsLength = 0;
     dTable;
     filterType;
     userData: any = '';
     structureFormContent: any;
     strucuredForms: any = [];
     activeStrucuredForms;
     dataLoadingMsg1 = true;
     isActive: any;
     statusChangedFormName: any;
     reviewStatusChangeMessage: any;
     statusChangedTime: any;
     isActiveArchive: boolean = false;
     isActivePending: boolean = false;
     isApiError: boolean;
     completedCount;
     pendingCount;
     draftCount;
     isScheduled = 'all';
     totalCt;
     userRole;
     clickedTab = "";
  integrationDetails;
  advancedFilterOptions = [];
  advancedFilterSearchKeys;
  advancedFilterOptionsSettings = {};
  selectedAdvancedFilterItem: any;
  searchFilterItem: any;
  advancedFilterNew: any;
  searchFilterName = '';
  resetAdvancedFilter = false;
  @ViewChild('advancedSearchFilter') advancedFilterComponent: AdvanceSearchComponent;
     documentTagDetails:any=[];
     selectedfilterval: any;
     archiveCount;
     strucuredFormsPending: any;
     strucuredFormsCopy: any;
     strucuredFormsupdated: any;
     strucuredFormsPendingArchived: any;
     strucuredFormsArchive: any;
     pendingall: any;
     pendingallArchived: any;
     actualstrucuredForms: any;
     reverseFlag: any;
     flagcheck: any;
     datam;
     userDataConfig;
     userDataN: any;
     filterValues;
     notclickablecol: any;
     collaborate_edit_visible: any;
     previlages;
     caregiverfname;
     caregiverlname;
     includeOtherForms;
     onPolling: boolean = false;
     crossTenantChangeSubscriber: any;
     userConfig;
     siteIds: any = '0';
     hideSiteSelection: boolean;
     dataTableLoading: boolean;
     eventsSubject: Subject<void> = new Subject<void>();
     dynamicControls: ControlType<string>[] | null = [];
     dynamicControlsAdvancedView: ControlType<string>[] | null = [];
     resetAdvanceSearchForm = false;
     advanceSearchInput: any = {};
     showHistory = false;
     showDraftHistory = false;
     showHistoryModal = false;
     showResendModal = false;
     contentType = CONSTANTS.contentType.forms.toLowerCase();
     resendPatientId;
     dateRangeFilterOptions = LISTING_PAGE_DATERANGE_OPTIONS;
     dateRange = new FormControl;     
     constants = CONSTANTS;
     advancedFilterCount = advancedFilterCount;
     archivedBy = false;
     isTableLoaded = {
          completed: false,
          pending: false,
          archived: false,
          drafts: false
      };
     isFirtsLoad = true;
     dateRangeStoreKey = Store.DATE_RANGE_FILTER_FORMS;
     private socketEventSubscriptions: Subscription[] = [];
     labelSite = '';
     labelSiteFilter = '';
     showFilterAppliedMessage = false;
     dateRangeType = DateRanges.LAST_THIRTY_DAYS;
     isAdvancedSearchView = false;
  toggleAdvancedFilter = false;
  selectedColumns;
  columnConfig;
  defaultColumns = [];
  customColumnControl: ControlType<string>;
     constructor(
    public readonly structureService: StructureService,
    private readonly router: Router,
    private readonly toolTipService: ToolTipService,
    private readonly _formsService: FormsService,
    readonly elementRef: ElementRef,
    readonly renderer: Renderer,
    readonly renderer2: Renderer2,
    private readonly formpPipe: FormpPipe,
    private readonly _sharedService: SharedService,
    private readonly ngxLogger: NGXLogger,
    private readonly storeService: StoreService,
    private readonly staticDataService: StaticDataService,
    private readonly httpService: HttpService,
    private readonly searchService: SearchService
     ) {
    this.labelSite = this.toolTipService.getTranslateData((this.labelSite = this.staticDataService.getSiteLabelBasedOnMultiAdmission('SITE')));
    this.columnConfig = [
      {
        title:
          '<input type="checkbox" name="select_all" value="1" style="float:left" class="form-select-all-indicator form-select-cls" id="form-select-all" title="Select">',
        columnId: 0,
        isSelected: true
      },
      { title: this.toolTipService.getTranslateData('LABELS.SENT_BY'), data: 'createdUser', isSelected: true, columnId: 1 },
      { title: this.toolTipService.getTranslateData('LABELS.CREATED_BY'), data: 'createdUser', isSelected: false, columnId: 2 },
      { title: this.toolTipService.getTranslateData('LABELS.LAST_MODIFIED_BY'), data: 'modifiedbyname', isSelected: false, columnId: 3 },
      { title: this.toolTipService.getTranslateData('LABELS.PATIENT_NAME'), data: 'patientName', isSelected: false, columnId: 4 },
      { title: this.toolTipService.getTranslateData('LABELS.PATIENT_FIRST_NAME'), data: 'fname', isSelected: true, columnId: 5 },
      { title: this.toolTipService.getTranslateData('LABELS.PATIENT_LAST_NAME'), data: 'lname', isSelected: true, columnId: 6 },
      { title: this.toolTipService.getTranslateData('LABELS.MRN'), data: 'patientIdentityValue', isSelected: true, columnId: 7 },
      { title: this.toolTipService.getTranslateData('ADMISSION.LABELS.ADMISSION'), data: 'admissionName', isSelected: false, columnId: 8 },
      { title: this.toolTipService.getTranslateData('LABELS.FORM_NAME'), data: 'form_name', isSelected: true, columnId: 9 },
      { title: this.labelSite, data: 'siteName', isSelected: true, columnId: 10 },
      { title: this.toolTipService.getTranslateData('LABELS.WORKFLOW'), data: null, isSelected: true, columnId: 11 },
      { title: this.toolTipService.getTranslateData('BUTTONS.REVIEW_COMPLETE'), data: null, isSelected: false, columnId: 12 },
      { title: this.toolTipService.getTranslateData('LABELS.SCHEDULED'), data: 'isScheduled', isSelected: true, columnId: 13 },
      { title: this.toolTipService.getTranslateData('LABELS.SENT_FORM_VIA'), data: 'applessMode', isSelected: true, columnId: 14 },
      { title: this.toolTipService.getTranslateData('LABELS.INTEGRATION_STATUS'), data: 'integration_type', isSelected: true, columnId: 15 },
      {
        title: this.toolTipService.getTranslateData('LABELS.DISCRETE_DATA_INTEGRATION_STATUS'),
        data: 'discrete_integration_status',
        isSelected: true,
        columnId: 16
      },
      { title: this.toolTipService.getTranslateData('LABELS.PRACTITIONER_NAME'), data: 'patientName', isSelected: true, columnId: 17 },
      { title: this.toolTipService.getTranslateData('LABELS.SENT_ON'), data: 'senton', isSelected: true, columnId: 18 },
      { title: this.toolTipService.getTranslateData('LABELS.SAVED_ON'), data: 'createdtimestamp', isSelected: false, columnId: 19 },
      { title: this.toolTipService.getTranslateData('LABELS.UPDATED_ON'), data: 'updatedtimestamp', isSelected: false, columnId: 20 },
      { title: this.toolTipService.getTranslateData('LABELS.SUBMITTED_ON'), data: 'senton', isSelected: false, columnId: 21 },
      { title: this.toolTipService.getTranslateData('LABELS.ARCHIVED_ON'), data: 'deletedOn', isSelected: false, columnId: 22 },
      { title: this.toolTipService.getTranslateData('LABELS.ARCHIVED_BY'), data: 'deletedName', isSelected: false, columnId: 23 },
      { title: this.toolTipService.getTranslateData('LABELS.LAST_REMINDER_SENT'), data: 'lastRemindedOn', isSelected: true, columnId: 24 },
      { title: this.toolTipService.getTranslateData('GENERAL.ACTIONS'), data: null, isSelected: true, columnId: 25 }
    ];
    this.selectedColumns = this.columnConfig.reduce((acc, col, index, array) => {
      if (col.isSelected && index !== 0 && index !== array.length - 1) {
        acc.push({ key: col.columnId, value: col.title });
      }
      return acc;
    }, []);
    this.defaultColumns = this.selectedColumns;
    const columnKeyValueArray = this.columnConfig.slice(1, -1).map((col) => ({
      key: col.columnId,
      value: col.title
    }));
    this.customColumnControl = new ControlType({
      key: 'formWorklistColumns',
      label: this.toolTipService.getTranslateData('LABELS.COLUMN_SELECTION'),
      options: columnKeyValueArray,
      controlType: 'dropdown',
      order: 6,
      value: this.selectedColumns
    });
          this.labelSiteFilter = this.staticDataService.getSiteLabelBasedOnMultiAdmission('FILTER_BY_SITES');
          renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
               var self = this;
               var IsEnterClicked = false;
               $(document).keypress((event) => {
                    if (event.keyCode == 13) {
                         IsEnterClicked = true;
                    }
               });
               if (event.target.parentElement.className && event.target.parentElement.className.includes("reviewcheckclass")) {
                    this.statusChangedFormName = $(event.target).data("form_name");
                    this.statusChangedTime = new Date();
                    var form_id = $(event.target).data("form_id");
                    var submission_id = $(event.target).data("submission_id");
                    if ($(event.target).prop("checked") == true) {
                         var new_status = 1;
                    } else if ($(event.target).prop("checked") == false) {
                         var new_status = 2;
                    }

                    var formData = {
                         form_id: form_id,
                         submission_id: submission_id,
                         status: new_status,
                         user_id: this.userData.userId
                    }
                    this._formsService.formReviewStatusChange(formData).then((data) => {
                         if (new_status == 1) this.reviewStatusChangeMessage = "Form " + this.statusChangedFormName + "  has been marked as reviewed";
                         else this.reviewStatusChangeMessage = "Review is canceled from the Form " + this.statusChangedFormName;
                         let activity = new_status == 1 ? "reviewed" : "reviewed canceled";
                         var activityData = {
                              activityName: "Form marked as " + activity,
                              activityType: "forms",
                              activityDescription: this.reviewStatusChangeMessage + " by " + this.userData.displayName + " on " + this.statusChangedTime + "(form id:" + form_id + ",form submission id:" + submission_id + ")"
                         };
                         this._structureService.trackActivity(activityData);
                         /**Polling for live update**/
                         var polldata = {
                              form_id: form_id,
                              submission_id: submission_id,
                              status: new_status,
                              tenantid: this.userData.tenantId,
                              user_id: this.userData.userId
                         };
                         console.log("poll data=>", polldata);
                         this._structureService.socket.emit("reviewpolling", polldata);
                         /***************************/
                   });
               } else if (event.target.id == 'view' || event.target.parentElement.id == 'view') {
                    this.viewStrucuturedForm();
               }
               else if (event.target.id == 'download' || event.target.parentElement.id == 'download') {
                    this.ngxLogger.log('form details', this.activeStrucuredForms);
                    if (this.isActive == 'draft' || this.isActive == 'pending') {
                         let formDetails = {
                              "staffId": this.activeStrucuredForms.from_id,
                              "formId": this.activeStrucuredForms.form_id,
                              "submissionId": this.activeStrucuredForms.form_submission_id,
                              "tenantId": this.userData.tenantId,
                              "type": this.isActive
                         }
                         if(this.isActive == 'draft') {
                              formDetails["staffId"] = this.activeStrucuredForms.sender_id;
                              if (this.activeStrucuredForms.form_type == "Staff Facing" || this.activeStrucuredForms.facing_new == 2) {
                                   if (this.activeStrucuredForms.patientName != null && this.activeStrucuredForms.patientName != "" && this.activeStrucuredForms.patientName != " ") {
                                        formDetails["patientId"] = this.activeStrucuredForms.patient_id;
                                   }
                              } else {
                                   formDetails["patientId"] = this.activeStrucuredForms.caregiver != null ? this.activeStrucuredForms.caregiver : this.activeStrucuredForms.patient_id;
                              }
                         } else {
                              formDetails['sendId'] = this.activeStrucuredForms.sent_id;
                              formDetails['submissionId'] = this.activeStrucuredForms.form_submission_id != null ?
                                    this.activeStrucuredForms.form_submission_id : this.activeStrucuredForms.staff_submission_id;
                              if (this.activeStrucuredForms.form_type == "Patient Facing") {
                                   formDetails["patientId"] = this.activeStrucuredForms.recipient_id;
                              }
                              if (this.activeStrucuredForms.facing_new == 2) {
                                   if (this.activeStrucuredForms.caregiver_displayname != null &&
                                        this.activeStrucuredForms.caregiver_displayname != "" &&
                                        this.activeStrucuredForms.caregiver_displayname != "null" &&
                                        this.activeStrucuredForms.caregiver_displayname != "Administrator" &&
                                        this.activeStrucuredForms.createdUser != this.activeStrucuredForms.caregiver_displayname) {
                                        formDetails["patientId"] = this.activeStrucuredForms.associated_user_id;
                                   }
                              }
                              if (this.activeStrucuredForms.form_type == "Staff Facing" && this.activeStrucuredForms.facing_new != 2) {
                                   if (this.activeStrucuredForms.patientName != null && this.activeStrucuredForms.patientName != "" && this.activeStrucuredForms.patientName != " ") {
                                        formDetails["patientId"] = this.activeStrucuredForms.patient_id;
                                   }
                              }
                         }
                         this._formsService.generateDraftPendingForm(formDetails).then((result) => {
                              this.ngxLogger.log('pdf result', result);
                              if (result['success']) {
                                   let fileName = result['data'].fileName;
                                   let newWindow: any = window.open(this._structureService.serverBaseUrl + APIs.loaderImageUrl);
                                   const fileUrl = this._structureService.apiBaseUrl + APIs.downloadFormDetails+'?filetoken=' + fileName;
                                   newWindow.location = fileUrl;
                                   let activityData = {
                                        activityName: "Download form from " + this.isActive + ' bucket',
                                        activityType: "Download forms",
                                        activityDescription: this.userData.displayName + " download the form from the " + this.isActive  + " bucket. Details are " + JSON.stringify(formDetails) + " and results are "+JSON.stringify(result)+" from all form worklist"
                                   };
                                   this._structureService.trackActivity(activityData);
                              } else {
                                   let activityData = {
                                        activityName: "Download form from " + this.isActive + ' bucket',
                                        activityType: "Download forms",
                                        activityDescription: "Error occured while " + this.userData.displayName + " download the form from the " + this.isActive  + " bucket. Details are " + JSON.stringify(formDetails) + " and results are "+JSON.stringify(result)+" from all form worklist"
                                   };
                                   this._structureService.trackActivity(activityData);
                                   this._structureService.notifyMessage({
                                        messge: this._ToolTipService.getTranslateData(
                                             'ERROR_MESSAGES.COMMON_ERROR_MSG'
                                        ),
                                        delay: 1000,
                                        type: 'danger'
                                   });
                              }
                         },
                              () => {
                                   this._structureService.notifyMessage({
                                        messge: this._ToolTipService.getTranslateData(
                                             'ERROR_MESSAGES.COMMON_ERROR_MSG'
                                        ),
                                        delay: 1000,
                                        type: 'danger'
                                   });
                              });
                    } else {
                         this.getPdfTaggedForm();
                    }
               } else if (event.target.id == 'archive-form' || event.target.parentElement.id == 'archive-form') {
                    if (this.isActive == 'draft') {
                         this._structureService.showAlertMessagePopup({
                                   text: this._ToolTipService.getTranslateData('MESSAGES.DRAFT_CANCEL_CONFIRM')
                              }
                         ).then((confirm) => {
                              if (IsEnterClicked) {
                                   IsEnterClicked = false;
                                   return false;
                              }
                              if (confirm) {
                                   $('[id*="archive-form"]').bind('click', false);
                                   this.archiveSubmitedForm('');
                              }
                         });
                    } else {
                         var rowDetails = this.activeStrucuredForms;
                         var patientName;
                         var patientID;
                         if (rowDetails['caregiver_userid'] != null) {
                              patientName = rowDetails['caregiver_displayname'];
                              patientID = rowDetails['caregiver_userid'];
                         } else {
                              patientName = rowDetails['patientName'];
                              patientID = rowDetails['from_id'];
                         }
                         const checkIntegrationInFromWorklist: any = {
                              "form_id": rowDetails['form_id'],
                              "tenant_id": this.userData.tenantId,
                              "patient_id": patientID,
                              "staff_id": this.userData.userId,
                              "admissionId": this._structureService.isMultiAdmissionsEnabled?  rowDetails['admissionId']: undefined,
                              "action": IntegrationType.FORM_ARCHIVE
                         };
                         if (this.userDataConfig.enable_sftp_integration == false) {
                              this._formsService.checkIntegration(checkIntegrationInFromWorklist).subscribe((data) => {
                                  if (data.success) {
                                         setTimeout(() => {
                                             this.archiveForm();
                                         }, 300)                             
                                   }
                              }
                         );
                         } else {
                              this.archiveForm();
                         }
                    }

               } else if (event.target.id === 'cancel-form' || event.target.parentElement.id === 'cancel-form') {
                    this.cancelPendingForm('');
               } else if (event.target.id == 'restore-forms' || event.target.parentElement.id == 'restore-forms') {
                    var self = this;
                    swal({
                              title: "Are you sure?",
                              text: "You are going to restore this form",
                              type: "warning",
                              showCancelButton: true,
                              cancelButtonClass: "btn-default",
                              confirmButtonClass: "btn-warning",
                              confirmButtonText: "Ok",
                              closeOnConfirm: true
                         },
                         (confirm) => {
                              if (IsEnterClicked) {
                                   IsEnterClicked = false;
                                   swal.close();
                                   return false;
                              }
                              if (confirm) {
                                   $('[id*="restore-forms"]').bind('click', false);
                                   self.restoreForms();
                              }
                         });
               } else if (event.target.id == 'resend-form' || event.target.parentElement.id == 'resend-form') {
                    this.resendForm();
               } else if (event.target.id == 'allow_edit' || event.target.parentElement.id == 'allow_edit') {
                    this.allowEdit();
               } else if (event.target.id == 'resend-completed-forms' || event.target.parentElement.id == 'resend-completed-forms') {
                    this.popUpRecipientsList();
               }
               else if (event.target.id == 'move_fc' || event.target.parentElement.id == 'move_fc') {
                    var rowDetails = this.activeStrucuredForms;
                    var rowDetails = this.activeStrucuredForms;
                    var patientName;
                    var patientID;
                    if (rowDetails['caregiver_userid'] != null) {
                         patientName = rowDetails['caregiver_displayname'];
                         patientID = rowDetails['caregiver_userid'];
                    } else {
                         patientName = rowDetails['patientName'];
                         patientID = rowDetails['from_id'];
                    }
                    if (this.userDataConfig.enable_sftp_integration == false) {
                         const checkIntegrationInFromWorklist: any = {
                              "form_id": rowDetails['form_id'],
                              "tenant_id": this.userData.tenantId,
                              "patient_id": patientID,
                              "staff_id": this.userData.userId,
                              "admissionId": this._structureService.isMultiAdmissionsEnabled? rowDetails['admissionId']: undefined,
                              "action" : IntegrationType.SEND_TO_EHR
                         };
                         this._formsService.checkIntegration(checkIntegrationInFromWorklist).subscribe((data) => {
                              if (data.success) {
                                   setTimeout(() => {
                                        this.moveToFC();
                                   }, 300);
                              }
                         });
                    } else {
                         this.moveToFC();
                    }
               } else if (event.target.id === 'mark-complete') {
                    this._structureService.showAlertMessagePopup({
                         title: this._ToolTipService.getTranslateData('MESSAGES.CONFIRM_MESSAGE'),
                         text: this._ToolTipService.getTranslateDataWithParam('MESSAGES.YOU_ARE_GOING_TO_COMPLETE_THE_SELECTED_FORM', {'formName': this.activeStrucuredForms.form_name})
                    }).then((confirm) => {
                         if (confirm) {
                              this.dataLoadingMsg1 = true;
                              this._formsService.markFromAsComplete(this.activeStrucuredForms).then((res) => {
                                   this.dataLoadingMsg1 = false;
                                   if (res) {
                                        this.populateData(this.isActive, this.isScheduled, 'refresh');
                                        this.displayList();
                                   }
                              });
                         }
                     });
               }
               if (event.target.id == 'form-select-all') {
                    var rows = this.dTable.rows({
                         'search': 'applied'
                    }).nodes();
                    $('input[type="checkbox"]', rows).not(":disabled").prop('checked', event.target.checked);
               }
               if (event.target.className.split(' ').indexOf('form-select-cls') != -1) {
                    let count = 0;
                    $('.form-select-cls').each(function() {
                         if ($(this).prop('checked') == true) {
                              count = 1;
                         }
                    });
                    if (count > 0) {
                         $("#archiveForm").prop('disabled', false);
                         $("#cancelForm").prop('disabled', false);
                    } else {
                         $("#archiveForm").prop('disabled', true);
                         $("#cancelForm").prop('disabled', true);
                    }
               }
               var ids = [];
               var allIds = [];
               var unreadCountList = false;
               var self = this;
               $('input[name="formid[]"]').each(function() {
                    if (this.checked) {
                         ids.push($(this).val());
                         unreadCountList = true;
                    }
               });
               allIds = self.datam.aaData;
               allIds = allIds.filter(x => ids.some(y => y == x.id));
               let classArr = ['popover', 'int-main-content', 'copy-ico', 'pop-title'];
               let targetClassName = (event.target.className.split(" ")[0]) ? event.target.className.split(" ")[0] : "null";
               let resArr = classArr.map((className) => {
                    if (className.includes(targetClassName)) {
                         return false
                    } else {
                         return true
                    }
               });
               let status = resArr.every(function(t) {
                    if (t) return true
               })
               if (event.target.id != "inte-status-sent" && event.target.id != "inte-status-receive" && event.target.id != "inte-status-not-sftp" && event.target.id != "inte-status-not-sftp-discrete" && event.target.id != "inte-status-not-sftp-form" && targetClassName && status) {
                    $('.inte-popover').hide();
               }
               allIds.forEach(element => {
                    element.isActive = self.isActive;
                    element.downloadTime = new Date().toLocaleString();
                    var createdontime;
                    if (element.senton.toString().length == 13) {
                         createdontime = self.formpPipe.transform(parseInt(element.senton));
                    } else {
                         createdontime = self.formpPipe.transform(parseInt(element.senton) * 1000);
                    }
                    element.createdOn = createdontime;
                    // TODO: esi_code_for_patient_identity decommisioned, remove usage if it is not causing any issue
                    element.config_identity_value = self.userData.config.esi_code_for_patient_identity;
                    element.config_staff_identity = self.userData.config_replica.esi_code_for_staff_identity;
                    element.config_staff_name = self.userData.config_replica.esi_code_for_staff_name;
                    element.enable = self.userData.config.enable_progress_note_integration;
                    element.deleted_by = self.userData.userId;
               });
               var IsEnterClicked = false;
               $(document).keypress((event) => {
                    if (event.keyCode == 13) {
                         IsEnterClicked = true;
                    }
               });
               //Handle Archive all
               if (event.target.id == 'cancelForm') {
                    if (!unreadCountList) {
                         this._structureService.notifyMessage({
                              messge: 'Select at least one form',
                              delay: 1000,
                              type: 'warning'
                         });
                    } else {
                         this.cancelPendingForm(allIds);
                    }
               };
               if (event.target.id == 'archiveForm') {
                    if (!unreadCountList) {
                         this._structureService.notifyMessage({
                              messge: 'Select at least one form',
                              delay: 1000,
                              type: 'warning'
                         });
                    } else {
                         swal({
                              title: "Are you sure?",
                              text: "You are going to archive the selected form(s)",
                              type: "warning",
                              showCancelButton: true,
                              cancelButtonClass: "btn-default",
                              confirmButtonClass: "btn-warning",
                              confirmButtonText: "Ok",
                              closeOnConfirm: true
                         }, (confirm) => {
                              if (IsEnterClicked) {
                                   IsEnterClicked = false;
                                   swal.close();
                                   return false;
                              }
                              if (confirm) {
                                   this.archiveSubmitedForm(allIds);
                              }
                         })
                    }
               };
               if(event.target.id == 'advance-searchB'){
        /** Hide or show advance search area */
        let state = '';
        let activityName = '';
        if (this.isAdvancedSearchView) {
          this.toggleAdvancedFilter = !this.toggleAdvancedFilter;
          state = this.toggleAdvancedFilter ? 'close' : 'open';
          activityName = `${state} advanced search filter in form worklist`;
        } else {
          this.structureService.hideAdvanceSearchArea = !this.structureService.hideAdvanceSearchArea;
          state = this.structureService.hideAdvanceSearchArea ? 'close' : 'open';
          activityName = `${state} advance search in form worklist`;
        }

                    var activityData = {
          activityName,
                         activityType: 'Advance Search',
                         activityDescription: `${this.userData.displayName} ${state} advance search in form worklist`,
                    };
                    this._structureService.trackActivity(activityData);
               }
               if (event.target.id == 'searchB') {
                    var value = $('#form-list-dt_wrapper #form-list-dt_filter label input').val();
                    if (value) {
                         this.advanceSearchInput = {};
                         this._structureService.closeAdvanceSearch(true);
                         this.resetAdvanceSearchForm = true;
                         this.dynamicControls = this._structureService.setAdvanceSearchControls('allform',this.isActive);
                         this.dTable.search(value).draw();
                        
                    } else {
                         this.dTable.search("").draw();
                    }
               }
               if (event.target.id == 'resetB') {
                    this.dTable.search("").draw();
                    $(".searchB").prop('disabled', true);
               }
          });
          this.crossTenantChangeSubscriber = this._sharedService.crossTenantChange.subscribe(
               (onInboxData) => {
                    this.ngOnInit();
               }
          );
          this._structureService.hideAdvanceSearchArea = true;
     }
     validateEmail(email) {
          var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
          if(re.test(email) && email.indexOf('unknown_') == -1){      
            return true;
          } else {
            return false;
          }
        }

     ngOnInit() {
          this.userConfig = this._structureService.getUserdata().config;
          this.previlages = [];
          
          this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
          this.userDataN =  JSON.parse(this._structureService.userDetails);
          if(this.userDataN.group == 20){

               this.router.navigate(['/forms/worklist']);
          }
          /******* session interrupt alert *****/
          if(this._structureService.getCookie('tabname') != ""){
           this._sharedService.formsType = this._structureService.getCookie('tabname');
           this._structureService.deleteCookie('tabname');
          }
          /******* session interrupt alert *****/
          if (this.userDataConfig.enable_collaborate_edit != 1 && this.userDataN.group != 3 && this._structureService.allowEditFormSuccess == false) {
             this._sharedService.formsType = "COMPLETED";
             $.notify({
                    'message': 'See My Forms Worklist for the Draft Category for your saved Draft Forms'
               }, {
                    allow_dismiss: true,
                    newest_on_top: true,
                    delay: 0
               });

          }

          this.notclickablecol = 0;
          this.dataTableLoading = false;
          this.isApiError = false;
          let manageTenants = this.userData.privileges;
          manageTenants = typeof(manageTenants) === 'undefined' ? this._structureService.getCookie('userPrivileges') : manageTenants;
          manageTenants = manageTenants.split(',');
          this._sharedService.FormsPage = '/forms/list';
          for (var i = 0; i < manageTenants.length; i++) {
               this.previlages[manageTenants[i]] = true;
          }
          this.collaborate_edit_visible = 1;
          if (this.userDataConfig.enable_collaborate_edit == 1 && this.previlages.FillStructuredForms) {
               this.collaborate_edit_visible = 1;
          } else {
               this.collaborate_edit_visible = 0;
          }
          this.filterValues = [];
          if (this.userDataConfig.enable_collaborate_edit == 1 && this.userDataN.group != 3) {
              
               var type = (this._sharedService.formsType && this._sharedService.formsType != "") ? this._sharedService.formsType : 'DRAFTS';
          } else {
               var type = (this._sharedService.formsType && this._sharedService.formsType != "") ? this._sharedService.formsType : 'COMPLETED';
               this._sharedService.formsType = "COMPLETED";
          }
          if (this.previlages.viewFormEntries) {
               var type = (this._sharedService.formsType && this._sharedService.formsType != "") ? this._sharedService.formsType : 'COMPLETED';
               this._sharedService.formsType = type;
          } else {
               if (this.userDataConfig.enable_collaborate_edit == 1 && this.userDataN.group != 3) {

                    this.notclickablecol = 1;
                    $.notify({
                         'message': 'No privilege to view completed, pending and archived forms.'
                    }, {
                         allow_dismiss: true,
                         delay: 0
                    });
               }

          }
          if (this.previlages.viewFormEntries || this.userDataConfig.enable_collaborate_edit == 1) {
               this.filterValues.push({
                    label: "All Forms",
                    value: "all",
                    selected: (this._sharedService.formFilterValue && this._sharedService.formFilterValue == 'all') ? true : true
               });
               this.filterValues.push({
                    label: "My Scheduled Forms",
                    value: "yes-me",
                    selected: (this._sharedService.formFilterValue && this._sharedService.formFilterValue == 'yes-me') ? true : false
               });
               this.filterValues.push({
                    label: "My Ad-hoc Forms",
                    value: "no-me",
                    selected: (this._sharedService.formFilterValue && this._sharedService.formFilterValue == 'no-me') ? true : false
               });
               this.filterValues.push({
                    label: "Scheduled Forms by Others",
                    value: "yes-others",
                    selected: (this._sharedService.formFilterValue && this._sharedService.formFilterValue == 'yes-others') ? true : false
               });
               this.filterValues.push({
                    label: "Ad-hoc Forms by Others",
                    value: "no-others",
                    selected: (this._sharedService.formFilterValue && this._sharedService.formFilterValue == 'no-others') ? true : false
               });
          } else {
               this.filterValues.push({
                    label: "All Forms",
                    value: "all",
                    selected: (this._sharedService.formFilterValue && this._sharedService.formFilterValue == 'all') ? true : true
               });
               this.filterValues.push({
                    label: "Scheduled Forms",
                    value: "yes-me",
                    selected: (this._sharedService.formFilterValue && this._sharedService.formFilterValue == 'yes-me') ? true : false
               });
               this.filterValues.push({
                    label: "Ad-hoc Forms",
                    value: "no-me",
                    selected: (this._sharedService.formFilterValue && this._sharedService.formFilterValue == 'no-me') ? true : false
               });
          }
          this.reverseFlag = true;
          this.flagcheck = 1;
          this.selectedfilterval = 'all';
          $('#form-list-dt').on('draw.dt', () => {
               let page = 'form-tag-definitions';
               $(".default-Scheduled").tooltip({
                    title: this._ToolTipService.getToolTip(page, 'FORM00007')
               });
               $(".review-tooltip").tooltip({
                    title: this._ToolTipService.getToolTip(page, 'FORM00010')
               });
          })
          this.activeStrucuredForms = null;
          
          this.userData = JSON.parse(this._structureService.userDetails);

          $('#scheduledSelect').select2({
               minimumResultsForSearch: -1
          });

          if (this._structureService.allowEditFormSuccess == true) {
               type = "COMPLETED";
          }
          if (this._sharedService.viewFormBackActiveTab != '') {
               type = this._sharedService.viewFormBackActiveTab;
               this._sharedService.viewFormBackActiveTab == '';
          }
          if (type == "COMPLETED") {
               this.clickedTab = this.isActive = "completed";
          } else if (type == "PENDING") {
               this.clickedTab = this.isActive = "pending"
          } else if (type == "ARCHIVE") {
               this.clickedTab = this.isActive = "archived"
          } else if (type == "DRAFTS") {
               this.clickedTab = this.isActive = "draft"
          }
          this.setAdvanceSearchForm('refresh');
           
          $("#scheduledSelect").change((e) => {
               const filter = e.target.value;
               this.selectedfilterval = this._sharedService.formFilterValue = filter;
               let key = filter.split('-');
               this.filterType = key[1];
               this.onChangescheduled(key[0]);
          });


          if (this._sharedService.formFilterValue && this._sharedService.formFilterValue != "") {
               let key = this._sharedService.formFilterValue.split('-');
               $("#scheduledSelect").val(key[0]).trigger('change');
          } 

          $('a.not-clickable-col').click(function(event) {
               event.preventDefault();

          });

          if (this._structureService.allowEditFormSuccess != false) {
               this._structureService.notifyMessage({
                    messge: 'Your form has been sent to Completed',
                    type: 'success',
                    allow_dismiss: true,
                    delay: 0
               });
               this._structureService.allowEditFormSuccess = false;
          }

          $('[data-toggle="tooltip"]').tooltip();
          var self = this;
          this.socketEventSubscriptions.push(
               this._structureService.subscribeSocketEvent('formPolling').subscribe((data) => {
                    $("#textmessage").text("");
                    $("#newloader").hide();
                    if(!this.archivedBy) {
                         this.populateDataAfterChange();
                    } else {
                         this.archivedBy = false;
                    }
               })
          );
     }

     ngOnDestroy() {
          if (this.crossTenantChangeSubscriber) {
               this.crossTenantChangeSubscriber.unsubscribe();
          }
          this._structureService.socket.off("formPolling");
          this._sharedService.reInitiateMessagePolling.emit({
               type: 'form'
          });
          /**Unsubscribe all the socket event subscriptions */
          this.socketEventSubscriptions.forEach(subscription => {
               if(subscription) subscription.unsubscribe();
          });
    if (this.advancedFilterComponent) {
      this.advancedFilterComponent.resetSearch();
    }
    this.advanceSearchInput = {};
     }

     archiveForm() {
          this._structureService.showAlertMessagePopup({
               text: this._ToolTipService.getTranslateData('MESSAGES.ARCHIVE_FORM')
          }).then((confirm) => {
               if (confirm) {
                    $('[id*="archive-form"]').bind('click', false);
                    this.archiveSubmitedForm('');
               }
          });
     }

     moveToFC() {
          swal({
               title: "Are you sure?",
               text: "You are going to send this form to EHR system",
               type: "warning",
               showCancelButton: true,
               cancelButtonClass: "btn-default",
               confirmButtonClass: "btn-warning",
               confirmButtonText: "Ok",
               closeOnConfirm: true
          }, (confirm) => {
               if (confirm) {
                    $('[id*="move_fc"]').bind('click', false);
                    let formData;
                    if (this.activeStrucuredForms.patient_id != '0' && (this.isActive == 'completed' || this.isActive == 'archived') && this.activeStrucuredForms.created_date) {
                         // staff
                         formData = {
                              "userid": this.activeStrucuredForms.userid,
                              "tenantId": this.activeStrucuredForms.tenant_id,
                              "recipient_id": this.activeStrucuredForms.userid,
                              "formId": this.activeStrucuredForms.form_id,
                              "staffFacing": "true",
                              "form_submission_id": this.activeStrucuredForms.form_submission_id,
                              "form_id": this.activeStrucuredForms.form_id,
                              "form_name": this.activeStrucuredForms.form_name,
                              "patient_id": "",
                              "id": "",
                              "regenerate":"true",
                              "regenerateUser":this.userData.userId,
                              "patientUser": this.activeStrucuredForms.from_id,
                              "patient_user": this.activeStrucuredForms.patient_id,
                              "generate_send_id": this.activeStrucuredForms.sent_id
                         }
                    } else {
                         if (this.activeStrucuredForms.facing_new == 2) {
                              formData = {
                                   "userid": this.activeStrucuredForms.from_id,
                                   "tenantId": this.activeStrucuredForms.tenant_id,
                                   "recipient_id": this.activeStrucuredForms.userid,
                                   "formId": this.activeStrucuredForms.form_id,
                                   "staffFacing": null,
                                   "form_submission_id": this.activeStrucuredForms.form_submission_id,
                                   "form_name": this.activeStrucuredForms.form_name,
                                   "id": "",
                                   "regenerate":"true",
                                   "regenerateUser":this.userData.userId,
                                   "associate_patient_id": this.activeStrucuredForms.patient_associated_id,
                                   "loggedinUserId": this.userData.userId,
                                   "patient_user": this.activeStrucuredForms.associated_user_id,
                                   "generate_send_id": this.activeStrucuredForms.sent_id
                              }
                         } else {
                              formData = {
                                   "userid": this.activeStrucuredForms.from_id,
                                   "tenantId": this.activeStrucuredForms.tenant_id,
                                   "recipient_id": this.activeStrucuredForms.userid,
                                   "formId": this.activeStrucuredForms.form_id,
                                   "staffFacing": null,
                                   "regenerate":"true",
                                   "regenerateUser":this.userData.userId,
                                   "form_submission_id": this.activeStrucuredForms.form_submission_id,
                                   "patient_user": this.activeStrucuredForms.from_id,
                                   "generate_send_id": this.activeStrucuredForms.sent_id
                              }
                         }
                    }

                    this._formsService.filingCenterSubmittedForm(formData).then((data: any) => {
                         if(this.userData.config.form_integration_mode !='sqs' ){
                         if (data.filename) {
                              this._structureService.notifyMessage({
                                   messge: 'Files for ' + this.activeStrucuredForms.form_name + ' generated successfully',
                                   delay: 1000,
                                   type: 'success'
                              });
                              $('[id*="move_fc"]').unbind('click', false);
                         }
                    }else{
                         if (data.message == "Queued Successfully") {
                              this._structureService.notifyMessage({
                                   messge: 'Integration for ' + this.activeStrucuredForms.form_name + ' queued successfully',
                                   delay: 1000,
                                   type: 'success'
                              });
                              $('[id*="move_fc"]').unbind('click', false);
                         }
                    }
                    });
               }
          });

          var x = document.getElementsByClassName("btn-warning");

          x[0].setAttribute("id", "ehr_cnfrm_btn");


     }

     allowEdit() {
          var swal_options;
          if (this.isActive == 'completed') {
               swal_options = {
                    title: this._ToolTipService.getTranslateData('LABELS.ARE_YOU_SURE'),
                    text: `${this._ToolTipService.getTranslateData('MESSAGES.FORM_EDIT_PERMISSION_GRANT')} ${this.activeStrucuredForms.form_name}`,
                    type: "input",
                    showCancelButton: true,
                    cancelButtonClass: "btn-default",
                    confirmButtonClass: "btn-warning",
                    confirmButtonText: `${this._ToolTipService.getTranslateData('BUTTONS.OK')}`,
                    closeOnConfirm: true,
                    inputPlaceholder: this._ToolTipService.getTranslateData('PLACEHOLDERS.ENTER_OPTIONAL_MSG'),

               }
          } else {
               swal_options = {
                    title: this._ToolTipService.getTranslateData('LABELS.ARE_YOU_SURE'),
                    text: `${this._ToolTipService.getTranslateData('MESSAGES.FORM_EDIT_PERMISSION_DENY')} ${this.activeStrucuredForms.form_name}`,
                    type: "warning",
                    showCancelButton: true,
                    cancelButtonClass: "btn-default",
                    confirmButtonClass: "btn-warning",
                    confirmButtonText: this._ToolTipService.getTranslateData('BUTTONS.OK'),
                    closeOnConfirm: true
               }
          }
          swal(swal_options, (data) => {
               if (data !== false) {
                    $('[id*="allow_edit"]').bind('click', false);
                    this.allowEditConfirm(data);
               }
          })
          var x = document.getElementsByClassName("btn-warning");
          x[0].setAttribute("id", "permssn_cnfrm_btn");
          $('div .form-group').find("input").attr("id", "msg_input");
     }
     allowEditConfirm(msg = '') {   
          this.activeStrucuredForms.loggedinuserid = this.userData.userId;
          this._formsService.allowEditForm(this.activeStrucuredForms, msg).then((result: any) => {
               var nameeditallow = "edit-" + this.activeStrucuredForms.form_id + this.activeStrucuredForms.form_submission_id;
               this.activeStrucuredForms.allow_edit = result.allowedit;
               let activityData: any;
               if (result.allowedit == 0) {
                    $("[name=" + nameeditallow + "]").attr('title', 'Allow Edit').html('<i data-edit class="icmn-pencil2"></i>');
               }
               this._formsService.notifyAfterAllowEdit(this.activeStrucuredForms, result, 'listPage', msg).then((result: any) => {
                    this.populateData(this.isActive, this.isScheduled, false);
                    var self = this;
                    this._formsService.getAllTaggedFormsLazyCount(true, this.isScheduled, this.includeOtherForms, this.siteIds).then((data) => {
                         this.completedCount = data['completedCount'];
                         this.archiveCount = data['archivedCount'];
                         this.pendingCount = data['pendingCount'];
                         this.draftCount = data['draftCount'];
                    }).catch(function(e) {
                         if(self.isApiError == false){
                              self.isApiError = true;
                              $.notify({'message':'<strong>We are facing some technical problem to load the Worklists. Please contact your administrator</strong>'},
                              {
                                   allow_dismiss: true, type: "danger", delay: 0,
                                   onClose : function(){ self.isApiError = false; }
                              });
                         }
                    });
               });
          });
     }

     onChangescheduled(val, condition = "") {
          this.selectedfilterval = val;
          if (this.selectedfilterval == "all") {
               this.changeFormData('ALL', condition, true);
          }
          if (this.selectedfilterval == "yes") {
               this.changeFormData('YES', condition, true);
          }
          if (this.selectedfilterval == "no") {
               this.changeFormData('NO', condition, true);
          }
          if (this.isActive == "pending") {
               $(this.dTable.column(11).header()).text('Sent On');
          }
          var self = this;
          
          this._formsService.getAllTaggedFormsLazyCount(true, this.selectedfilterval, this.includeOtherForms, this.siteIds).then((data) => {
               this.completedCount = data['completedCount'];
               this.archiveCount = data['archivedCount'];
               this.pendingCount = data['pendingCount'];
               this.draftCount = data['draftCount'];
          }).catch(function(e) {
               if(self.isApiError == false){
                    self.isApiError = true;
                    $.notify({'message':'<strong>We are facing some technical problem to load the Worklists. Please contact your administrator</strong>'},
                    {
                         allow_dismiss: true, type: "danger", delay: 0,
                         onClose : function(){ self.isApiError = false; }
                    });
               }
           });
     }

     resendForm() {
          var message = this.activeStrucuredForms.message
          var selectedRecipients = [];
          var selectedRecipientsPolling = [];
          var practitioner = 0;
          if (this.activeStrucuredForms.facing_new == 2) {
               practitioner = 1;
          }
          var practitionerAssociatePatient = [];
          if (this.activeStrucuredForms.facing_new == 2) {
               practitionerAssociatePatient.push(this.activeStrucuredForms.caregiver_userid);
          }
          if (this.activeStrucuredForms.caregiver_userid) {
               selectedRecipients.push(this.activeStrucuredForms.recipient_id + '--' + this.activeStrucuredForms.caregiver_userid);
          } else {
               selectedRecipients.push(this.activeStrucuredForms.recipient_id);
          }
          var formSendMode = '';
          if (this.userData.config.enable_appless_model == 1 && this.activeStrucuredForms.applessMode == '1') {
               formSendMode = "appless";
          }
          var data = {
               "form_id": this.activeStrucuredForms.form_id,
               "recipients": selectedRecipients,
               "userId": this.activeStrucuredForms.from_id,
               "message": message,
               "previousSendId": this.activeStrucuredForms.sent_id,
               "resend": true,               
               "practitioner": practitioner,
               "practitionerAssociatePatient": practitionerAssociatePatient,
               "formSendMode": (formSendMode && formSendMode == 'appless') ? 'appless' : '',
               "applessMode": (formSendMode && formSendMode == 'appless') ? 'both' : ''
          };
          var deepLinking = {
               "pushType": "",
               "state": "eventmenu.forms",
               "stateParams": {},
               "tenantId": this.activeStrucuredForms.tenant_id,
               "tenantName": this.userData.tenantName,
               "formSendMode": (formSendMode && formSendMode == 'appless') ? 'appless' : '',
               "applessMode": (formSendMode && formSendMode == 'appless') ? 'both' : '',
               "sentId": "",
               "form_id": this.activeStrucuredForms.form_id
          };
          let pushMessage = "[Reminder] You have new form to fillout"; //"[Reminder] "+
          swal({
               title: "Are you sure?",
               text: "You are going to send reminder for this form ",
               type: "warning",
               showCancelButton: true,
               cancelButtonClass: "btn-default",
               confirmButtonClass: "btn-warning",
               confirmButtonText: "Ok",
               closeOnConfirm: true
          }, () => {
               $('[id*="resend-form"]').bind('click', false);
               this._formsService.resendForms(data).then((result: any) => {
                    var self = this;
                    this.populateData(this.isActive, this.isScheduled, 0);
                    selectedRecipientsPolling.push({
                         userid: this.activeStrucuredForms.staffFacing === 1 ? this.activeStrucuredForms.from_id : this.activeStrucuredForms.recipient_id,
                         senderId: this.userData.userId,
                         reminderforForm:1,
                         organizationMasterId: this.userData.organizationMasterId,
                         formSendMode: (formSendMode && formSendMode == FormSendMode.APPLESS) ? FormSendMode.APPLESS : FormSendMode.INAPP,
                         applessMode: FormSendMode.BOTH,
                         sentId: result.sentId
                    });
                    deepLinking.sentId = result.sentId;
                  
                    //this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", selectedRecipientsPolling, 'form'); //Need to commnd after finish form inbox
                    //this._structureService.socket.emit("sendFormsToRecipients", selectedRecipientsPolling);
                    /*patient reminder for form starts*/
                    var otherDatas = {
                         patientReminderTime: this.userData.config.patient_reminder_time * 1,
                         patientReminderTypes: this.userData.config.patient_reminder_types,
                         messageReplyTimeout: this.userData.config.message_reply_timeout,
                         sentId: result.sentId,
                         senderName: this.userData.displayName,
                         tenantId: this.userData.tenantId,
                         tenantName: this.userData.tenantName,
                         serverBaseUrl: this._structureService.serverBaseUrl,
                         apiVersion: this._structureService.version,
                         message: "[Reminder] You have new form to fillout", //[Reminder] 
                         formReminderType: this.userData.config.patient_reminder_checking_type,
                         environment: this._structureService.environment,
                         formSendMode: (formSendMode && formSendMode == 'appless') ? 'appless' : '',
                         applessMode: (formSendMode && formSendMode == 'appless') ? 'both' : ''
                    }
                    const notificationData = {
                         sourceId: CONSTANTS.notificationSource.form,
                         sourceCategoryId: CONSTANTS.notificationSourceCategory.formReSendNotification
                    };
                    /*patient reminder for form ends*/
                    this._structureService.sentPushNotification(selectedRecipientsPolling, 0, pushMessage, '', deepLinking, '', '', undefined, undefined, undefined, undefined, notificationData);
                    if (result.status == 1) {
                         var activityDescription = this.userData.displayName + " successfully send reminder form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") to " + selectedRecipients.toString();
                         if (formSendMode && formSendMode == 'appless') {
                              activityDescription = this.userData.displayName + " successfully send reminder appless form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") to " + selectedRecipients.toString();
                         }
                         var activityData = {
                              activityName: (formSendMode && formSendMode == 'appless') ? "send reminder Appless Form Success" : "send reminder Form Success",
                              activityType: "structured forms",
                              activityDescription: activityDescription
                         };
                         this._structureService.trackActivity(activityData);
                         var sentMessage = "Reminder for " +this.activeStrucuredForms.form_name+ " successfully sent."
                         let notifyItems = [];

                         if (formSendMode && formSendMode == 'appless') {
                              if(this.activeStrucuredForms.username && this.validateEmail(this.activeStrucuredForms.username)){
                                   notifyItems.push("Email " + this.activeStrucuredForms.username);
                              }

                              if(this.activeStrucuredForms.mobile && this.activeStrucuredForms.countryCode){
                                   notifyItems.push("Mobile Number (" + this.activeStrucuredForms.countryCode + ") " + this.activeStrucuredForms.mobile);
                              }
                              if(notifyItems.length > 0) {
                                   sentMessage += ' AppLess (MagicLink) sent to ';
                                   if(notifyItems.length == 2) {
                                        sentMessage += notifyItems.join(' and ');
                                   } else {
                                        sentMessage += notifyItems[0];
                                   }
                              }
                              
                         }
                         this._structureService.notifyMessage({
                              messge:  sentMessage,
                              delay: 1000,
                              type: 'success'
                         });
                    } else {
                         var activityDescription = this.userData.displayName + " form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") to " + selectedRecipients.toString() + " resending failed";
                         if (formSendMode && formSendMode == 'appless') {
                              activityDescription = this.userData.displayName + " appless form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") to " + selectedRecipients.toString() + " resending failed";
                         }
                         var activityData = {
                              activityName: (formSendMode && formSendMode == 'appless') ? "send reminder Appless Form Fail" : "send reminder Form Fail",
                              activityType: "structured forms",
                              activityDescription: activityDescription
                         };
                         this._structureService.trackActivity(activityData);
                         this._structureService.notifyMessage({
                              messge: this.activeStrucuredForms.form_name + ' sending failed',
                              delay: 1000
                         });
                    }
                    if (this.isActive == "pending") {
                         $(this.dTable.column(11).header()).text('Sent On');
                    }
               });
          });
          var nw_frm = document.getElementsByClassName("btn-warning");
          nw_frm[0].setAttribute("id", "new_form_cnfrm_btn");
     }

     restoreForms() {
          if (this.activeStrucuredForms.created_date) {
               this.activeStrucuredForms.isActive = 'completed';
          } else {
               this.activeStrucuredForms.isActive = 'pending';
          }
          this.activeStrucuredForms.deleted_by = this.userData.userId;
          NProgress.start();
          this._formsService.restoreArchivedForm(this.activeStrucuredForms).then((result: any) => {
               var activityData: any = {};
               if (result.status == '1') {
                    this.archiveCount = parseInt(this.archiveCount) - 1;
                    NProgress.done();
                    let self = this;
                    if (this.activeStrucuredForms.isActive == 'completed') {
                         this.completedCount = parseInt(this.completedCount) + 1;
                         this.sendPolling('completed', result.notifyUsers);
                         activityData.activityName = "Restore Completed Form";
                         this.populateData(this.isActive, this.isScheduled, 0);
                         this._structureService.notifyMessage({
                              messge: 'Successfully restored the submitted form - ' + this.activeStrucuredForms.form_name,
                              delay: 1000,
                              type: 'success'
                         });
                    } else {
                         this.pendingCount = parseInt(this.pendingCount) + 1;
                         this.sendPolling('pending', result.notifyUsers);
                         activityData.activityName = "Restore Pending Form";
                         this.populateData(this.isActive, this.isScheduled, 0);
                         this._structureService.notifyMessage({
                              messge: 'Successfully restored the pending form - ' + this.activeStrucuredForms.form_name,
                              delay: 1000,
                              type: 'success'
                         });
                    }
                    activityData.activityType = "structured forms";
                    activityData.activityDescription = this.userData.displayName + " restored form " + this.activeStrucuredForms.form_name + " (formId " + this.activeStrucuredForms.form_id + ",sendId " + this.activeStrucuredForms.sent_id + ") successfully";
                    this._structureService.trackActivity(activityData);
                    this.pendingall = this.strucuredFormsPending;
               } else {
                    NProgress.done();
                    activityData = {
                         activityName: "Restore  Form Fail",
                         activityType: "structured forms",
                         activityDescription: this.userData.displayName + " restored form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") failed"
                    };
                    this._structureService.trackActivity(activityData);
                    this._structureService.notifyMessage({
                         messge: this.userData.displayName + " restored form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") failed",
                         delay: 1000,
                         type: 'danger'
                    });
               }
          });
     }

     cancelPendingForm(allIds) {
          if (allIds.length <= 1) {
               const formData = allIds.length === 1 ? allIds[0] : this.activeStrucuredForms;
               this._formsService.cancelForm(formData,'all form worklist').subscribe((res)=>{
                    if (!isBlank(res) && res[this.activeStrucuredForms.id].success) {
                         this.populateDataAfterChange();
                         this._sharedService.updateUserInboxCounts.emit({
                              getCount: true,
                              type: 'forms'
                         });
                    }
               });
          } else {
               allIds.map(function(x) {
                    x.cancelForm = true;
                    return x
               });
               this._formsService.cancelForm(allIds,'all form worklist').subscribe((res) => {
                    const result = Object.keys(res).map(key => res[key]);
                    let successCount = 0;
                    let failureCount = 0;
                    let totalCount = 0;
                    const successResult = result.filter((result) => {
                         return result.status == '1';
                    })
                    const failureResult = result.filter((result) => {
                         return result.status != '1';
                    });
                    var successFormPendingArray = [];
                    var failureFormPendingArray = [];
                    result.forEach(result => {
                         var activityData: any = {};
                         totalCount = totalCount + 1;
                         if (result.success) {
                              successCount = successCount + 1;
                              successFormPendingArray.push(this.userData.displayName + " canceled form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ",receipientId " + result.recipient_id + ") successfully");
                              if (successCount == successResult.length && successFormPendingArray.length) {
                                   activityData.activityType = "structured forms";
                                   activityData.activityName = "Cancel Pending Form";
                                   activityData.activityDescription = this.userData.displayName + " cancelled pending forms " + successFormPendingArray.join(', ') + " successfully";
                                   this._structureService.trackActivity(activityData);
                              }
                         } else {
                              failureCount = failureCount + 1;
                              failureFormPendingArray.push(this.userData.displayName + " canceled form " + result.form_name + " (" + result.form_id + ",sendId " + result.sent_id + ",receipientId " + result.recipient_id + ") failed");
                              if (failureCount == failureResult.length && failureFormPendingArray.length) {
                                   activityData = {
                                        activityName: "Cancel Pending Form",
                                        activityType: "structured forms",
                                        activityDescription: this.userData.displayName + " cancel pending forms " + failureFormPendingArray.join(', ') + " were failed"
                                   };
                                   this._structureService.trackActivity(activityData);
                              }
                         }
                    })
                    if (successCount >= 1) {
                         this._sharedService.updateUserInboxCounts.emit({
                              getCount: true,
                              type: 'forms'
                         });
                         this.populateDataAfterChange();
                         this._structureService.notifyMessage({
                              messge:  this._ToolTipService.getTranslateData(
                                  'SUCCESS_MESSAGES.MULTIPLE_FORM_CANCELED_SUCCESS'
                              ),
                              delay: 1000,
                              type: 'success'
                          });
                    }
                    $('#form-select-all').prop('checked', false);
                });
          }
     }

     archiveSubmitedForm(allIds) {
          this.archivedBy = true;
          NProgress.start();
          this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
          if (this.isActive == 'draft') {
               this.activeStrucuredForms.isActive = this.isActive;
               this.activeStrucuredForms.downloadTime = new Date().toLocaleString();
               let createdontime;
               if (this.activeStrucuredForms.senton.toString().length == 13) {
                    createdontime = this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton));
               } else {
                    createdontime = this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton) * 1000);
               }
               this.activeStrucuredForms.createdOn = createdontime;
               // TODO: esi_code_for_patient_identity decommisioned, remove usage if it is not causing any issue
               this.activeStrucuredForms.config_identity_value = this.userData.config.esi_code_for_patient_identity;
               this.activeStrucuredForms.config_staff_identity = this.userData.config_replica.esi_code_for_staff_identity;
               this.activeStrucuredForms.config_staff_name = this.userData.config_replica.esi_code_for_staff_name;
               this.activeStrucuredForms.enable = this.userData.config.enable_progress_note_integration;
               this.activeStrucuredForms.deleted_by = this.userData.userId;
               this._formsService.archiveDraftForm(this.activeStrucuredForms).then((result: any) => {
                    if (result.status == 1 || result.status == "1") {
                         this._structureService.notifyMessage({
                              messge: this._ToolTipService.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_CANCEL_FROM_DRAFT' ,{'formName': this.activeStrucuredForms.form_name}),
                              delay: 1000,
                              type: 'success'
                         });
                         let activityData =  {
                              activityName: "Cancel Draft Form - collaborate enabled",
                              activityType: "structured forms",
                              activityDescription: ''
                         };
                         activityData.activityDescription = this.userData.displayName + " canceled draft form " + this.activeStrucuredForms.form_name + " (formId " + this.activeStrucuredForms.form_id + ",sendId " + this.activeStrucuredForms.sent_id;
                         this._structureService.trackActivity(activityData);
                         this.populateDataAfterChange();
                    } else {
                         let activityData = {
                              activityName: "Cancel Draft Form -collaborate enabled",
                              activityType: "structured forms",
                              activityDescription: ''
                         };
                         activityData.activityDescription = this.userData.displayName + " failed canceled draft form " + this.activeStrucuredForms.form_name + " (formId " + this.activeStrucuredForms.form_id + ",sendId " + this.activeStrucuredForms.sent_id;
                         this._structureService.trackActivity(activityData);
                         this._structureService.notifyMessage({
                              messge: this._ToolTipService.getTranslateData('ERROR_MESSAGES.FORM_CANCEL_FROM_DRAFT'),
                              delay: 1000,
                              type: 'warning'
                         });
                    }
                    this._structureService.socket.emit("sendFormsToRecipients", [{
                         senderId: this.userData.userId
                    }]);
                    NProgress.done();
               });
          } else {
               if (allIds.length == 0) {
                    this.activeStrucuredForms.isActive = this.isActive;
                    this.activeStrucuredForms.downloadTime = new Date().toLocaleString();
                    let createdontime;
                    if (this.activeStrucuredForms.senton.toString().length == 13) {
                         createdontime = this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton));
                    } else {
                         createdontime = this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton) * 1000);
                    }
                    this.activeStrucuredForms.createdOn = createdontime;
                    this.activeStrucuredForms.createdOnNew = this.activeStrucuredForms.senton;
                    this._formsService.archiveSubmittedForm(this.activeStrucuredForms).then((result: any) => {
                         let activityData: any = {};
                         let notifyResult: any = result;
                         if (result.status == '1') {
                              this.archiveCount = parseInt(this.archiveCount) + 1;
                              if (notifyResult.defaultfilingCenter) {
                                   activityData.activityName = "Form Copied to Filing Center";
                                   activityData.activityType = "structured forms";
                                   activityData.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") successfully and copied to filing center named " + notifyResult.defaultfilingCenter;
                                   this._structureService.trackActivity(activityData);
                              }
                              if (notifyResult.defaultFromFilingCenterarchivejson && this.userDataConfig.enable_progress_note_integration == 1) {
                                   if (notifyResult.defaultFromFilingCenterarchivejson != false && notifyResult.identity_value_Patient != '' || notifyResult.identity_value_Patient != "" || notifyResult.identity_value_Patient != null && notifyResult.patientAssociation == true &&
                                        notifyResult.CPRUSERNO != '' || notifyResult.CPRUSERNO != null &&
                                        notifyResult.CPRUSERNAME != null || notifyResult.CPRUSERNAME != '') {
                                        activityData.activityName = "Form Copied to Filing Center as JSON";
                                        activityData.activityType = "structured forms";
                                        activityData.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") successfully and copied the JSON file to filing center named " + notifyResult.defaultFromFilingCenterarchivejson;
                                        this._structureService.trackActivity(activityData);
                                   }
                                   if (notifyResult.defaultFromFilingCenterarchivejson != false && (notifyResult.identity_value_Patient == '' || notifyResult.identity_value_Patient == "" || notifyResult.identity_value_Patient == null || notifyResult.patientAssociation == false ||
                                             notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null ||
                                             notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null)) {
                                        activityData.activityName = "Failed Form Copy to Filing Center as JSON";
                                        activityData.activityType = "structured forms";
                                        const activityMsg = this.setActivityMsg(notifyResult);
                                        if (!isBlank(activityMsg['finalMessage'])) {
                                             activityData.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") failed to copy the JSON file to filing center named " + notifyResult.defaultFromFilingCenterarchivejson + "because" + activityMsg['messageData'];
                                             this._structureService.trackActivity(activityData);
                                        }
                                   }
                              }
                              NProgress.done();
                              if (this.isActive == 'completed') {
                                   this.completedCount = parseInt(this.completedCount) - 1;
                                   this._structureService.notifyMessage({
                                        messge: this._ToolTipService.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_CANCEL_FROM_COMPLETE' ,{'formName': result.form_name}),
                                        delay: 1000,
                                        type: 'success'
                                   });
                                   activityData.activityName = "Archive Completed Form";
                                   this.sendPolling('completed', notifyResult.notifyUsers);
                                   if(notifyResult.statusfailed){
                                        let IsEnterClicked = false;
                                        const config = {
                                             title: this._ToolTipService.getTranslateData('ERROR_MESSAGES.INTEGRATION_FAILURE'),
                                             text: this._ToolTipService.getTranslateData('ERROR_MESSAGES.INTEGRATION_FAILURE_ERROR'),
                                             type: "warning",
                                             showCancelButton: true,
                                             confirmButtonText: this._ToolTipService.getTranslateData('BUTTONS.OK'),
                                             closeOnConfirm: true,
                                             confirmButtonClass: "btn-warning",
                                             cancelButtonClass: "btn-default",
                                             cancelButtonText: this._ToolTipService.getTranslateData('BUTTONS.CANCEL')
                                           };
                                           this._structureService.showAlertMessagePopup(config).then((response)=>{
                                             if (IsEnterClicked) {
                                                  IsEnterClicked = false;
                                                  swal.close();
                                                  return false;
                                             }
                                             activityData.activityName = "Integration on Archive operation failed";
                                             activityData.activityType = "structured forms";
                                             if (response) {
                                                  activityData.activityDescription = this.userData.displayName + " Integration on Archive operation failed - clicked go back - FormName :" + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ")  User clicked OK option from the Archive operation failed popup";
                                                  this._structureService.trackActivity(activityData);
                                             }else {
                                                  activityData.activityDescription = this.userData.displayName + " Integration on Archive operation failed - clicked  continue anyway Cancel - FormName :" + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ")  User clicked cancel option from the Archive operation failed popup";
                                                  this._structureService.trackActivity(activityData);
                                             }
                                        });
                                        
                                   }
                              } else {
                                   this.pendingCount = parseInt(this.pendingCount) - 1;
                                   activityData.activityName = "Archive Pending Form";
                                   this._structureService.notifyMessage({
                                        messge: this._ToolTipService.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_CANCEL_FROM_PENDING',{'formName': result.form_name}),
                                        delay: 1000,
                                        type: 'success'
                                   });
                                   this.sendPolling('pending', result.notifyUsers);
                              }
                              activityData.activityType = "structured forms";
                              activityData.activityName = "Archived Form successfully";
                              activityData.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") successfully";
                              this._structureService.trackActivity(activityData);
                              this.populateDataAfterChange();
                         } else {
                              NProgress.done();
                              activityData = {
                                   activityName: "Archive Completed Form",
                                   activityType: "structured forms",
                                   activityDescription: this.userData.displayName + " archived form " + result.form_name + " (" + result.form_id + ") failed"
                              };
                              this._structureService.trackActivity(activityData);
                              this._structureService.notifyMessage({
                                   messge: this._ToolTipService.getTranslateDataWithParam('ERROR_MESSAGES.FORM_CANCEL_FROM_COMPLETE_PENDING', {'displayName':this.userData.displayName, 'formName': result.form_name, 'formId': result.form_id}),
                                   delay: 1000,
                                   type: 'danger'
                              });
                         }
                    });
               } else {
                    this._formsService.archiveSubmittedMultipleForm(allIds).then((resultN: any) => {
                         let successCount = 0;
                         let failureCount = 0;
                         let totalCount = 0;
                         let successResult = resultN.filter((result) => {
                              return result.status == '1';
                         })
                         let failureResult = resultN.filter((result) => {
                              return result.status != '1';
                         });
                         let notifyCount = resultN.length;
                         let successFormPendingArray = [];
                         let successFormCompletedArray = [];
                         let failureFormPendingArray = [];
                         let failureFormCompletedArray = [];
                         let filingCenterFormsArray = [];
                         let successfilingCenterFormsArray = [];
                         let failurefilingCenterFormsArray = [];
                         let notifyUsersFull = [];
                         resultN.forEach(result => {
                              let activityData: any = {};
                              let notifyResult: any = result;
                             
                              totalCount = totalCount + 1;
                              if (result.status == '1') {
                                   this.archiveCount = parseInt(this.archiveCount) + 1;
                                   successCount = successCount + 1;
                                   if (notifyResult.defaultfilingCenter) {
                                        filingCenterFormsArray.push(result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") copied to filing center named " + notifyResult.defaultfilingCenter);
                                   }
                                   if (successCount == successResult.length && filingCenterFormsArray.length) {
                                        activityData.activityName = "Multiple Forms Copied to Filing Center";
                                        activityData.activityType = "structured forms";
                                        activityData.activityDescription = this.userData.displayName + " archived forms (" + filingCenterFormsArray.join(', ') + ")";
                                        this._structureService.trackActivity(activityData);
                                   }
                                   
                                   let activityMsg = {};
                                   if (notifyResult.defaultFromFilingCenterarchivejson && this.userDataConfig.enable_progress_note_integration == 1) {
                                        if (notifyResult.defaultFromFilingCenterarchivejson != false && notifyResult.identity_value_Patient != '' || notifyResult.identity_value_Patient != "" || notifyResult.identity_value_Patient != null && notifyResult.patientAssociation == true &&
                                             notifyResult.CPRUSERNO != '' || notifyResult.CPRUSERNO != null &&
                                             notifyResult.CPRUSERNAME != null || notifyResult.CPRUSERNAME != '') {
                                             successfilingCenterFormsArray.push(this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") successfully and copied the JSON file to filing center named " + notifyResult.defaultFromFilingCenterarchivejson);
                                        }
                                        if (notifyResult.defaultFromFilingCenterarchivejson != false && (notifyResult.identity_value_Patient == '' || notifyResult.identity_value_Patient == "" || notifyResult.identity_value_Patient == null || notifyResult.patientAssociation == false ||
                                                  notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null ||
                                                  notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null)) {
                                             activityData.activityName = "Failed Form Copy to Filing Center as JSON";
                                             activityData.activityType = "structured forms";
                                             activityMsg = this.setActivityMsg(notifyResult);
                                             if (!isBlank(activityMsg['finalMessage'])) {
                                                  failurefilingCenterFormsArray.push(this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") failed to copy the JSON file to filing center named " + notifyResult.defaultFromFilingCenterarchivejson + "because" + activityMsg['messageData']);
                                             }
                                        }
                                   }

                                   activityData.activityType = "structured forms";
                                   if (successCount == successResult.length && successfilingCenterFormsArray.length) {
                                        activityData.activityName = "Multiple Forms Copied to Filing Center as JSON";
                                        activityData.activityDescription = successfilingCenterFormsArray.join(', ');
                                        this._structureService.trackActivity(activityData);
                                   }
                                   if (successCount == successResult.length && failurefilingCenterFormsArray.length && !isBlank(activityMsg['finalMessage'])) {
                                        activityData.activityName = "Multiple Forms failed to Copy to Filing Center as JSON";
                                        activityData.activityDescription = failurefilingCenterFormsArray.join(', ');
                                        this._structureService.trackActivity(activityData);
                                   }
                                   NProgress.done();
                                   let deletedFlag = 0;
                                   let senderFlag = 0;
                                   let recipientFlag = 0;
                                   if(!isBlank(notifyResult.notifyUsers)) {
                                        notifyResult.notifyUsers.forEach(element => {
                                             notifyUsersFull.push(element);
                                             if (element.userid == this.userData.userId) {
                                                  deletedFlag = 1;
                                             }
                                             if (element.userid == result.userid) {
                                                  recipientFlag = 1;
                                             }
                                             if (element.userid == result.from_id) {
                                                  senderFlag = 1;
                                             }
                                        });
                                   }
                                   if (deletedFlag == 0) {
                                        notifyUsersFull.push({
                                             senderId: this.userData.userId
                                        });
                                   }
                                   if (recipientFlag == 0 && result.userid && result.userid != this.userData.userId) {
                                        notifyUsersFull.push({
                                             userid: result.userid
                                        });
                                   }
                                   if (senderFlag == 0 && result.from_id && result.from_id != this.userData.userId && result.from_id != result.userid) {
                                        notifyUsersFull.push({
                                             userid: result.from_id
                                        });
                                   }
                                   if (this.isActive == 'completed') {
                                        this.completedCount = parseInt(this.completedCount) - 1;
                                        successFormCompletedArray.push(result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ")");
                                   } else {
                                        this.pendingCount = parseInt(this.pendingCount) - 1;
                                        successFormPendingArray.push(result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ")");
                                   }

                                   if (successCount == successResult.length && successFormCompletedArray.length) {
                                        activityData.activityName = "Multiple Archive Completed Forms";
                                        activityData.activityDescription = this.userData.displayName + " archived completed forms " + successFormCompletedArray.join(', ') + " successfully";
                                        this._structureService.trackActivity(activityData);
                                   }

                                   if (successCount == successResult.length && successFormPendingArray.length) {
                                        activityData.activityName = "Multiple Archive Pending Forms";
                                        activityData.activityDescription = this.userData.displayName + " archived pending forms " + successFormPendingArray.join(', ') + " successfully";
                                        this._structureService.trackActivity(activityData);
                                   }
                              } else {
                                   NProgress.done();
                                   failureCount = failureCount + 1;
                                   if (this.isActive == 'completed') {
                                        failureFormCompletedArray.push(result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ")");
                                   } else {
                                        failureFormPendingArray.push(result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ")");
                                   }
                                   activityData = {
                                        activityName: "Archive Completed Form",
                                        activityType: "structured forms",
                                        activityDescription: ''
                                   };
                                   if (failureCount == failureResult.length && failureFormCompletedArray.length) {
                                        activityData.activityDescription = this.userData.displayName + " archived completed forms " + failureFormCompletedArray.join(', ') + " were failed";
                                        this._structureService.trackActivity(activityData);
                                   }

                                   if (failureCount == failureResult.length && failureFormPendingArray.length) {
                                        activityData.activityDescription = this.userData.displayName + " archived pending forms " + failureFormPendingArray.join(', ') + " were failed";
                                        this._structureService.trackActivity(activityData);
                                        this._structureService.notifyMessage({
                                             messge: this._ToolTipService.getTranslateDataWithParam('ERROR_MESSAGES.FORMS_CANCEL_FROM_PENDING_COMPLETE',{'displayName': this.userData.displayName, 'forms': failureFormPendingArray.join(', ')}),
                                             delay: 1000
                                         });
                                   }
                              }
                         });

                         if (this.isActive == 'completed' && totalCount == notifyCount) {
                              this._structureService.notifyMessage({
                                   messge: this._ToolTipService.getTranslateData('SUCCESS_MESSAGES.FORMS_CANCEL_FROM_COMPLETE'),
                                   delay: 1000,
                                   type: 'success'
                              });
                              if(successCount && notifyUsersFull.length) {
                                   this.sendCommonPollingMultiple(notifyUsersFull);
                              }
                         } else if (totalCount == notifyCount) {
                              this._structureService.notifyMessage({
                                   messge: this._ToolTipService.getTranslateData('SUCCESS_MESSAGES.FORMS_CANCEL_FROM_PENDING'),
                                   delay: 1000,
                                   type: 'success'
                              });
                              if(successCount && notifyUsersFull.length) {
                                   this.sendCommonPollingMultiple(notifyUsersFull);
                              }
                         }
                         this.populateDataAfterChange();
                         $('#form-select-all').prop('checked', false);
                    });
               }
          }
     }
     setActivityMsg(notifyResult) {
          let finalMessage = '';
          let messageData = '';
          let messageData1 = '';
          if (notifyResult.patientAssociation == false && notifyResult.staffFacing == "true") {
               messageData1 = "there is no patient associated with this form";
          }
          if ((notifyResult.identity_value_Patient == '' || notifyResult.identity_value_Patient == null || notifyResult.identity_value_Patient == "") && (notifyResult.patientAssociation == true || notifyResult.staffFacing == "false")) {
               messageData += ", MRN";
               if (notifyResult.patient_name && notifyResult.patient_name != "") {
                    messageData += " of " + notifyResult.patient_name;
               }
          }
          let f1 = 0;
          let f2 = 0;
          if (notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null) {
               f1 = 1;
               messageData += ", USERNO";
          }
          if (notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null) {
               f2 = 1;
               messageData += ", USERNAME";
          }
          if ((f1 == 1 || f2 == 1) && notifyResult.staff_name && notifyResult.staff_name != "") {
               messageData += " of " + notifyResult.staff_name;
          }
          if (notifyResult.patientAssociation == false && notifyResult.staffFacing == "false") {
               messageData += "";
          }

          const removeComa = messageData.charAt(0);

          if (removeComa == ',') {
               messageData = messageData.substring(1);
          }
          if (messageData1 && messageData) {
               finalMessage = 'Generating Progress Note for Form ' + notifyResult.form_name + ' failed due to ' + messageData1 + ' and missing  (' + messageData + ')';
          } else if (messageData1) {
               finalMessage = 'Generating Progress Note for Form ' + notifyResult.form_name + ' failed due to ' + messageData1;
          } else if (messageData) {
               finalMessage = 'Generating Progress Note for Form ' + notifyResult.form_name + ' failed due to missing (' + messageData + ')';
          } 
          return {'finalMessage': finalMessage, 'messageData': messageData};
     }
     populateDataAfterChange() {
          var self = this;
          this._formsService.getAllTaggedFormsLazyCount(true, this.isScheduled, this.includeOtherForms, this.siteIds).then((data) => {
               this.completedCount = data['completedCount'];
               this.archiveCount = data['archivedCount'];
               this.pendingCount = data['pendingCount'];
               this.draftCount = data['draftCount'];
          }).catch(function(e) {
               if(!self.isApiError){
                    self.isApiError = true;
                    $.notify({'message':'<strong>We are facing some technical problem to load the Worklists. Please contact your administrator</strong>'},
                    {
                         allow_dismiss: true, type: "danger", delay: 0,
                         onClose : function(){ self.isApiError = false; }
                    });
               }
          });
          this.changeFormData(this._sharedService.formsType, "polling");
          this._sharedService.updateUserInboxCounts.emit({
               getCount: true,
               type: 'forms'
          });
     }

     sendPollingMultiple(type, notifyUsers, result, last = 1) {
          var selectedRecipientsPolling = [];
          var deletedFlag = 0;
          var senderFlag = 0;
          var recipientFlag = 0;

          notifyUsers.forEach(element => {
               selectedRecipientsPolling.push(element);

               if (element.userid == this.userData.userId) {
                    deletedFlag = 1;
               }

               if (element.userid == result.userid) {
                    recipientFlag = 1;
               }

               if (element.userid == result.from_id) {
                    senderFlag = 1;
               }
          });

          if (deletedFlag == 0 && last == 1) {
               selectedRecipientsPolling.push({
                    senderId: this.userData.userId
               });
          }

          if (recipientFlag == 0 && result.userid && result.userid != this.userData.userId) {
               selectedRecipientsPolling.push({
                    userid: result.userid

               });
          }

          if (senderFlag == 0 && result.from_id && result.from_id != this.userData.userId && result.from_id != result.userid) {
               selectedRecipientsPolling.push({
                    userid: result.from_id
               });
          }
          this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", selectedRecipientsPolling, '', 'form'); //Need to commnd after finish form inbox
          this._structureService.socket.emit("sendFormsToRecipients", selectedRecipientsPolling);

     }

     sendCommonPollingMultiple(notifyUsers) {          
          notifyUsers = this.removeDuplicates(notifyUsers, 'userid');
          notifyUsers = this.removeDuplicates(notifyUsers, 'senderId');
          notifyUsers = notifyUsers.filter(a => a.userid != a.senderId);
          this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", notifyUsers, '', 'form'); //Need to commnd after finish form inbox
          this._structureService.socket.emit("sendFormsToRecipients", notifyUsers);
     }

     removeDuplicates( arr, prop ) {
          let obj = {};
          return Object.keys(arr.reduce((prev, next) => {
               if(!obj[next[prop]]) obj[next[prop]] = next; 
                    return obj;
          }, obj)).map((i) => obj[i]);
     }

     sendPolling(type, notifyUsers) {
          var selectedRecipientsPolling = [];
          var deletedFlag = 0;
          var senderFlag = 0;
          var recipientFlag = 0;
          if(!isBlank(notifyUsers)) {
               notifyUsers.forEach(element => {
                    selectedRecipientsPolling.push(element);

                    if (element.userid == this.userData.userId) {
                         deletedFlag = 1;
                    }

                    if (element.userid == this.activeStrucuredForms.userid) {
                         recipientFlag = 1;
                    }

                    if (element.userid == this.activeStrucuredForms.from_id) {
                         senderFlag = 1;
                    }
               });
          }
          if (deletedFlag == 0) {
               selectedRecipientsPolling.push({
                    senderId: this.userData.userId,
                    userid: this.activeStrucuredForms.recipient_id

               });

          }

          if (recipientFlag == 0 && this.activeStrucuredForms.userid && this.activeStrucuredForms.userid != this.userData.userId) {
               selectedRecipientsPolling.push({
                    userid: this.activeStrucuredForms.userid,
                    senderId: this.activeStrucuredForms.from_id
               });
          }

          if (senderFlag == 0 && this.activeStrucuredForms.from_id && this.activeStrucuredForms.from_id != this.userData.userId && this.activeStrucuredForms.from_id != this.activeStrucuredForms.userid) {
               selectedRecipientsPolling.push({
                    userid: this.activeStrucuredForms.from_id,
                    senderId: this.activeStrucuredForms.recipient_id
               });
          }

          this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", selectedRecipientsPolling, ' ', 'form'); //Need to commnd after finish form inbox
          this._structureService.socket.emit("sendFormsToRecipients", selectedRecipientsPolling);
     }

     changeFormData(type, refresh = "", fromScheduled = false) {
          $('#form-select-all').prop('checked', false);
          this.isFirtsLoad =true;
          Object.keys(this.isTableLoaded).forEach(tab => {
               if(this.isTableLoaded[tab] || !fromScheduled){
                    if(!fromScheduled){
                         this.isFirtsLoad = type !== tab;
                    }
                    this.isTableLoaded[tab] = false;
               }
          });
          if (this.dataTableLoading == false) {
          switch (type) {
               case 'COMPLETED':
                    if (this.notclickablecol != 1) {
                         this._sharedService.formsType = type;
                         this.reverseFlag = true;
                         this.dataTableLoading = true;
                         this.dataLoadingMsg1 = true;
                         $('.nav li a.completed').addClass('active');
                         this.strucuredForms = [];
                         this.isActive = this.clickedTab = 'completed';
                         this.setAdvanceSearchForm(refresh);
                         this.populateData(this.isActive, this.isScheduled, refresh);
                    }

                    break;
               case 'PENDING':
                    if (this.notclickablecol != 1) {
                         this._sharedService.formsType = type;
                         this.reverseFlag = true;
                         this.dataTableLoading = true;
                         this.dataLoadingMsg1 = true;
                         this.isActive = this.clickedTab = 'pending';
                         this.setAdvanceSearchForm(refresh);
                         this.populateData(this.isActive, this.isScheduled, refresh);
                         this.dTable && $(this.dTable.column(11).header()).text('Sent On');
                    }
                    break;
               case 'ARCHIVE':
                    if (this.notclickablecol != 1) {
                         this._sharedService.formsType = type;
                         this.reverseFlag = true;
                         this.dataTableLoading = true;
                         this.dataLoadingMsg1 = true;
                         this.isActive = this.clickedTab = 'archived';
                         this.setAdvanceSearchForm(refresh);
                         this.populateData(this.isActive, this.isScheduled, refresh);
                    }
                    break;
               case 'DRAFTS':
                        this._sharedService.formsType = type;
                        this.reverseFlag = true;
                        this.dataTableLoading = true;
                        this.dataLoadingMsg1 = true;
                        this.isActive = this.clickedTab = 'draft';
                        this.setAdvanceSearchForm(refresh);
                        this.populateData(this.isActive, this.isScheduled, refresh);
                    break;
               case 'ALL':
                    this.reverseFlag = true;
                    this.isScheduled = 'all';
                    this.dataTableLoading = true;
                    this.dataLoadingMsg1 = true;
                    this.strucuredForms = [];
                    this.setAdvanceSearchForm(refresh);
                    this.populateData(this.isActive, this.isScheduled, refresh);
                    break;
               case 'YES':
                    this.reverseFlag = true;
                    this.isScheduled = 'yes';
                    this.dataTableLoading = true;
                    this.dataLoadingMsg1 = true;
                    this.strucuredForms = [];
                    this.setAdvanceSearchForm(refresh);
                    this.populateData(this.isActive, this.isScheduled, refresh);
                    break;
               case 'NO':

                    this.reverseFlag = true;
                    this.isScheduled = 'no';
                    this.dataTableLoading = true;
                    this.dataLoadingMsg1 = true;
                    this.strucuredForms = [];
                    this.setAdvanceSearchForm(refresh);
                    this.populateData(this.isActive, this.isScheduled, refresh);
                    break;
          }
        }
     }

     selectElementText(el) {
          var range = document.createRange() // create new range object
          range.selectNodeContents(el) // set range to encompass desired element text
          var selection = window.getSelection() // get Selection object from currently user selected text
          selection.removeAllRanges() // unselect any user selected text (if any)
          selection.addRange(range) // add range to Selection object to select it
     }

     CopyToClipboard(containerid) {

          let para = $('#' + containerid)[0] as HTMLInputElement;
          this.selectElementText(para) // select the element's text we wish to read
          if (window.getSelection) { // all modern browsers and IE9+
               let selectedText = window.getSelection().toString()
               const el = document.createElement('textarea');
               el.value = selectedText;
               document.body.appendChild(el);
               el.select();
               document.execCommand('copy');
               document.body.removeChild(el);
               this.selectElementText(para);
          }

     }

     fillPopOver(ids) {
          let outputHtml = "";
          if (this.integrationDetails.type == "receive") {
               let data = this.integrationDetails.data;
               if (this.integrationDetails.count > 1) {
                    // table structure
                    let row = this.activeStrucuredForms;
                    let patientId;
                    if ((row.patient_id != '0' && (this.isActive == 'completed' || this.isActive == 'archived' || this.isActive == 'draft') && (row.form_type == 'Staff Facing' && row.facing_new != 2)) || (row.allow_edit == 1 && (row.form_type == 'Staff Facing' && row.facing_new != 2))) {
                         patientId = row.recipient_id;
                    } else {
                         if (row.facing_new == 2) {
                              patientId = row.associated_user_id;
                         } else {
                              patientId = row.recipient_id;
                         }
                    }
                    outputHtml = `
          Order Id: ${(this.activeStrucuredForms.form_submission_order_id && this.activeStrucuredForms.form_submission_order_id != 'null') ? this.activeStrucuredForms.form_submission_order_id : ''}
          <br>
          Citus MRN: ${patientId} 
          <br>
          Generic Drug: ${data[0].drug_id}
          <br>
          <br>
          <table style="margin-top: 10px;">
          <tr>
            <th>RMS Order Id</th>
            <th>Authorization</th>          
            <th>Citus ESI</th>
          </tr>`;

                    for (const result of data) {
                         outputHtml += `<tr>
           <td>${result.rms_order_id}</td>
           <td>${result.authorization}</td>         
           <td>${result.esi_id}</td>
          </tr>`;

                    }
                    outputHtml += `</table>`;
                    const idContentDiv = 'int-content-receive-' + this.activeStrucuredForms.created_date;
                    const idContainerDiv = 'inte-status-receive-' + this.activeStrucuredForms.created_date;
                    $('#' + idContentDiv).html(outputHtml);
                    setTimeout(() => {
                         const wid = $('#' + idContentDiv + ' > table').width() + 'px';
                         $('#' + idContainerDiv).width(wid);
                    }, 1);
               } else {
                    let row = this.activeStrucuredForms;
                    let patientId;
                    if ((row.patient_id != '0' && (this.isActive == 'completed' || this.isActive == 'archived' || this.isActive == 'draft') && (row.form_type == 'Staff Facing' && row.facing_new != 2)) || (row.allow_edit == 1 && (row.form_type == 'Staff Facing' && row.facing_new != 2))) {
                         patientId = row.recipient_id;
                    } else {
                         if (row.facing_new == 2) {
                              patientId = row.associated_user_id;
                         } else {
                              patientId = row.recipient_id;
                         }
                    }       
                    outputHtml = `
          OrderId: ${(this.activeStrucuredForms.form_submission_order_id && this.activeStrucuredForms.form_submission_order_id != 'null') ? this.activeStrucuredForms.form_submission_order_id : ''}
          <br />
          Genericdrug: ${data[0].drug_id}
          <br />
          RMS OrderId: ${data[0].rms_order_id}
          <br />        
          Citus ESI: ${data[0].esi_id}       
          <br />
          Citus MRN: ${patientId}       
          <br />
          Authorization: ${data[0].authorization}`;
                    $('#int-content-receive-' + this.activeStrucuredForms.created_date).html(outputHtml);
               }

          } else {
               let row = this.activeStrucuredForms;
               let patientId;
               if ((row.patient_id != '0' && (this.isActive == 'completed' || this.isActive == 'archived' || this.isActive == 'draft') && (row.form_type == 'Staff Facing' && row.facing_new != 2)) || (row.allow_edit == 1 && (row.form_type == 'Staff Facing' && row.facing_new != 2))) {
                    patientId = row.recipient_id;
               } else {
                    if (row.facing_new == 2) {
                         patientId = row.associated_user_id;
                    } else {
                         patientId = row.recipient_id;
                    }
               }
               let sentOn = "";
               if (row.patient_id != '0' && row.created_date) {
                    sentOn = this.formpPipe.transform(parseInt(row.senton) * 1000);
               } else {
                    sentOn = this.formpPipe.transform(parseInt(row.sentDate) * 1000);
               }
               let outputHtml = `OrderId: ${row.form_submission_order_id}
          <br />
          Citus ESI: ${(row.mrn) ? row.mrn : ''}
          <br />
          Citus MRN: ${patientId}
          <br />
          Sent On: ${sentOn}                   
          `;

               $('#int-content-receive-' + this.activeStrucuredForms.created_date).html(outputHtml);
          }

     }

     popover(ids, action) {
          if (action == 'loader') {
               $('.inte-popover').hide();
               $('#' + ids).show();
               $('#' + ids + ' > .pop-content > .int-loader').show();
               $('#' + ids + ' > .pop-content > .int-main-content').hide();
          } else if (action == 'show') {
               this.fillPopOver(ids);
               $('#' + ids + ' > .pop-content > .int-loader').hide();
               $('#' + ids + ' > .pop-content > .int-main-content').show();
          } else if (action == "close") {
               $('#' + ids + ' > .pop-content > .int-loader').hide();
               $('#' + ids + ' > .pop-content > .int-main-content').hide();
               $('#' + ids).hide();
          }
     }

     popoverNotSftp(ids, action) {
          if (action == 'loader') {
               $('.inte-popover').hide();
               $('#' + ids).show();
               $('#' + ids + ' > .pop-content > .int-loader').show();
               $('#' + ids + ' > .pop-content > .int-loader').css("display", "block");
               $('#' + ids + ' > .pop-content > .int-main-content').hide();
          } else if (action == 'show') {
               this.fillPopOverNotSftp(ids);
               $('#' + ids + ' > .pop-content > .int-loader').hide();
               $('#' + ids + ' > .pop-content > .int-main-content').show();
          } else if (action == "close") {
               $('#' + ids + ' > .pop-content > .int-loader').hide();
               $('#' + ids + ' > .pop-content > .int-main-content').hide();
               $('#' + ids).hide();
          }
     }

     fillPopOverNotSftp(ids) {
          let outputHtml = '';
          if (this.integrationDetails.type == 'inte-status-not-sftp' || this.integrationDetails.type == 'inte-status-not-sftp-discrete') {
               let response = this.integrationDetails.response;
               let diagnostics = (response.hasOwnProperty("diagnostics")) ? response.diagnostics : {};
               if (response.hasOwnProperty("integration_status") && typeof(response.integration_status) != 'undefined') {
                    var status_message = (response.integration_status == 'Pending') ? 'Waiting for response from CHIE' : response.integration_status;
                    outputHtml += `Status: ${status_message}<br />`;
               }
               if (response.hasOwnProperty("mrn") && typeof(response.mrn) != 'undefined') {
                    outputHtml += `MRN: ${response.mrn}<br />`;
               }

               if (response.hasOwnProperty("reference_id") && typeof(response.reference_id) != 'undefined') {
                    outputHtml += `Reference ID: ${response.reference_id}<br />`;
               }
               if(response.hasOwnProperty("processedAt") && typeof(response.processedAt) != 'undefined'){
                    outputHtml += `Processed At: ${response.processedAt}`;
               }
          }
          if(this.integrationDetails.type == 'inte-status-not-sftp-discrete') {
               $('#'+ids+' #int-content-discrete-' + this.activeStrucuredForms.sentDate).html(outputHtml);
          } else {
               $('#'+ids+' #int-content-' + this.activeStrucuredForms.sentDate).html(outputHtml);
          }
     }
     popoverNotSftpForm(ids, action) {
          if (action == 'loader') {
               $('.inte-popover').hide();
               $('#' + ids).show();
               $('#' + ids + ' > .pop-content-form > .int-loader').show();
               $('#' + ids + ' > .pop-content-form > .int-loader').css("display", "block");
               $('#' + ids + ' > .pop-content-form > .int-main-content-form').hide();
          } else if (action == 'show') {
               this.fillPopOverNotSftpForm(ids);
               $('#' + ids + ' > .pop-content-form > .int-loader').hide();
               $('#' + ids + ' > .pop-content-form > .int-main-content-form').show();
          } else if (action == "close") {
               $('#' + ids + ' > .pop-content-form > .int-loader').hide();
               $('#' + ids + ' > .pop-content-form > .int-main-content-form').hide();
               $('#' + ids).hide();
          } else if (action == 'showNill') {
               this.fillPopOverNotSftpForm("");
               $('#' + ids + ' > .pop-content-form > .int-loader').hide();
               $('#' + ids + ' > .pop-content-form > .int-main-content-form').show();
          }
     }

     fillPopOverNotSftpForm(ids="") {
          let outputHtml = '';
          if(ids == ""){
               outputHtml +=  `No Form Type Associated <br />`;
               $('#int-content-form-' + this.activeStrucuredForms.tag_id+'-'+this.activeStrucuredForms.id).html(outputHtml);
          }else{
               let response = this.documentTagDetails;
               if (response.hasOwnProperty("tagName") && typeof(response.tagName) != 'undefined') {
                    outputHtml += `Form Type: ${response.tagName}<br /> <br><b>Integration Details</b></br>`;
               }
               var integrationStatus = 0;
               if (response.hasOwnProperty("tagMeta")) {
                    let tagMeta = response.tagMeta;
                    if (tagMeta.hasOwnProperty("directlinkpatientchart") && tagMeta.directlinkpatientchart) {
                    integrationStatus=1;
                    outputHtml += `Enable Direct Linking to Patient Chart: ${tagMeta.directlinkpatientchartTriggerOn}<br />`;
                    }
                    if (tagMeta.hasOwnProperty("externalFileDisclosePHI") && tagMeta.externalFileDisclosePHI) {
                         integrationStatus=1;
                         outputHtml += `Enable Disclose PHI with External Systems: ${tagMeta.externalFileDisclosePHITriggerOn}<br />`;
                    }
                    if (tagMeta.hasOwnProperty("progressNoteIntegration") && tagMeta.progressNoteIntegration) {
                         integrationStatus=1;
                         outputHtml += `Progress Note Integration: ${tagMeta.progressNoteIntegrationTriggerOn}<br />`;
                    }
                    if (tagMeta.hasOwnProperty("sendformdatajson") && tagMeta.sendformdatajson) {
                         integrationStatus=1;
                         outputHtml += `Enable Sending Form Data to Third Party Application: ${tagMeta.sendformdatajsonTriggerOn}<br />`;
                    }
                    if (tagMeta.hasOwnProperty("FaxQIntegration") && tagMeta.FaxQIntegration) {
                         integrationStatus=1;
                         outputHtml += `Enable FaxQueue: ${tagMeta.faxQIntegrationTriggerOn}<br />`;
                    }
                    if (tagMeta.hasOwnProperty("externalFileExchangeWebhook") && tagMeta.externalFileExchangeWebhook) {
                         integrationStatus=1;
                         outputHtml += `Enable Document Exchange: ${tagMeta.externalFileExchangeTriggerOn}<br />`;
                    }
                    if(integrationStatus== 0){
                         outputHtml += `Nil<br />`;
                    }
               }
          $('#int-content-form-' + this.activeStrucuredForms.tag_id+'-'+this.activeStrucuredForms.id).html(outputHtml);
          }
     }

     populateData(formStatus, isScheduled, refresh, allIds = '') { 
          this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
          let self = this;
          let datas: any;          
          if (this.flagcheck == 1) {
               this.flagcheck = 2
          } else {
               this.flagcheck = 1
          }
          let counttoshow = $("#forms-to-show").val();
          if (localStorage.getItem('formSendMessage') && localStorage.getItem('formSendMessage') != '') {            
               let message = JSON.parse(localStorage.getItem('formSendMessage'));            
               let notify = $.notify(message.messge);                
               setTimeout(function() {
                   notify.update({
                       'type': message.type,
                       'message': '<strong>' + message.messge + '</strong>'
                   });                   
               }, 1000);
               localStorage.setItem('formSendMessage', '');
           }

          function format(row) {
               var clientId = self._structureService.socket.io.engine.id;
               var tokenId = self._structureService.getCookie('authenticationToken');
               var facing_new = row.facing_new;
               var enable_sftp_integration = self.userDataConfig.enable_sftp_integration == 1 ? "&enable_sftp_integration=" + true : "&enable_sftp_integration=" + false;
               var unique = "&uniqueFormIdentity=" +Guid.create();
               var confirmActionPrefill = row.confirmActionPrefill == 1 ? "&confirmActionPrefill=true" : "&confirmActionPrefill=false";
               var fax_queue_show_warning = self.userDataConfig.fax_queue_show_warning == 1 ? "&fax_queue_show_warning=" + true + unique + confirmActionPrefill : "&fax_queue_show_warning=" + false + unique + confirmActionPrefill;
               var formsUrl: any = self._structureService.machFormUrl + APIs.machFormEmbed +'?id=' + row.form_id + '&staffSubmissionId=' + row.staff_submission_id + '&patientId=' + row.recipient_id + '&tenantId=' + row.tenant_id + '&toId=' + row.from_id + '&sentId=' + row.sent_id + '&formId=' + row.form_id + '&loadScript=no &clientId=' + clientId + "&authenticationToken=" + tokenId + "&facing_new=" + facing_new + enable_sftp_integration + fax_queue_show_warning + (self._structureService.isMultiAdmissionsEnabled ? `&admissionId=${row.admissionId}`: '');
               formsUrl += "&pendingView=true";
               return `<div style="width:100%;" class="structured-form-modal">
        <iframe onload="$('html, body').animate({ scrollTop: 0 },'slow');$('.structured-form-modal').scrollTop(0);" allowTransparency="true" class="structured-form-dekstop" id="structured-form-dekstop" frameborder="0" scrolling="no" style="width:100%;border:none;pointer-events: none;" src="${ formsUrl}" ></iframe>
        </div>`;
          }

          if (refresh && refresh == "polling") {
               this.dataTableLoading = false;
               this.onPolling = true;
               if (this.dTable) {
                    this.dTable.ajax.reload(null, false);
                    var x = this.dTable;
               }
          } else {
               if (this.dTable) {
                    this.onPolling = false;
                    if (refresh && refresh != "") {
                         this.dTable.state.clear();
                    }
                    this.dTable.clear();
                    this.dTable.destroy();
               } else {
                    if (this._structureService.previousUrlNow.indexOf("/forms/list/view") == -1) {
                         this._structureService.resetDataTable();
                    }
               }
               let order = 21;
               if (this.isActive == 'pending') {
                    order = 18;
               }
               this.userRole = self._structureService.getCookie('userRole');
               let showPatientFirstLastName = false;
               if (
                 !isBlank(this.userData.config) &&
                 !isBlank(
                   this.userData.config.show_first_name_last_name_separate
                 ) &&
                 (this.userData.config.show_first_name_last_name_separate ==
                   '1' ||
                   this.userData.config.show_first_name_last_name_separate == 1)
               ) {
                 showPatientFirstLastName = true;
               }
               $(() => {
                    const x = this.dTable = $('#form-list-dt').DataTable({
                      	 order: [[order, 'desc']],
                         autoWidth: false,
                         responsive: true,
                         bprocessing: true,
                         bServerSide: true,
                         bpagination: true,
                         bsorting: true,
                         retrieve: true,
                         bsearching: true,
                         stateSave: true,
                         bLengthChange: true,
                         bInfo: true,
                         lengthMenu: [
                              [25, 50, ],
                              [25, 50]
                         ],
                         fnDrawCallback: function(oSettings) {
                              if (oSettings._iRecordsTotal < oSettings._iDisplayLength || oSettings._iRecordsTotal == 0 || oSettings.aoData.length == 0) {
                                   $('.dataTables_paginate').hide();
                              } else {
                                   $('.dataTables_paginate').show();
                              }
                              if (oSettings.aoData.length == 0) {
                                   $('.dataTables_info').hide();
                              } else {
                                   $('.dataTables_info').show();
                              }
                              if (self.isActive == 'archived') {
                                   $('#form-list-dt tr:eq(0) th:eq(0)').text(" #");
                              }

                              if (self.isActive == 'draft') {
                                   $('#form-list-dt tr:eq(0) th:eq(0)').text(" #");
                              }
                              if (self.isActive == 'completed' && self.previlages.allowArchiveForms != true) {
                                   $('#form-list-dt tr:eq(0) th:eq(0)').text(" #");
                              }
                         },
                         dom: "<'row form-worklist-search-row'<'col-sm-1 addButton'><'col-sm-2 text-center'l>B<'col-sm-2'f><'col-sm-3  form-worklist-search-button'>>" +
                              "<'row'<'col-sm-12'tr>>" +
                              "<'row'<'col-sm-5'i><'col-sm-7'p>>",
                         buttons: [{
                              extend: 'collection',
                              text: 'Export Data',
                              autoClose: true,
                              className: 'buttonStyle',
                              buttons: [{
                                        extend: 'excel',
                                        text: 'Current Page',
                                        title: 'All Form Worklist',
                                        exportOptions: {
                                             columns: self.isActive == 'draft' ? [0, 2, 3, 4, 5, 7, 8, 14, 15] : (self.isActive == 'completed' && self.userDataConfig.enable_sftp_integration == 1 && self.userDataConfig.enable_integration_status_worklist == 1) ? [0, 1, 4, 5, 6, 7, 8, 10, 12, 13, 16] : (self.isActive == 'completed' && self.userDataConfig.enable_sftp_integration != 1 && self.userDataConfig.enable_integration_status_worklist == 1) ? [0, 1, 4, 5, 6, 7, 8, 10, 12, 13, 16] : (self.isActive == 'pending' && self.userDataConfig.enable_sftp_integration != 1 && self.userDataConfig.enable_integration_status_worklist == 1) ? [0, 1, 4, 5, 6, 7, 8, 10, 12, 13, 19] : (self.isActive == 'archived' && self.userDataConfig.enable_sftp_integration != 1 && self.userDataConfig.enable_integration_status_worklist == 1) ? [0, 1, 4, 5, 6, 7, 8, 10, 12, 13, 16, 17, 18] : self.isActive == 'pending' ? [0, 1, 4, 5, 6, 7, 8, 10, 13, 19] : self.isActive == 'completed' ? [0, 1, 4, 5, 6, 7, 8, 10, 13, 16] : self.isActive == 'archived' ? [0, 1, 4, 5, 6, 7, 8, 10, 13, 16, 17, 18] : []
                                        }
                                   },
                                   {
                                        extend: 'excel',
                                        text: 'All Pages',
                                        title: 'All Form Worklist',
                                        exportOptions: {
                                             columns: self.isActive == 'draft' ? [0, 2, 3, 4, 5, 7, 8, 14, 15] : (self.isActive == 'completed' && self.userDataConfig.enable_sftp_integration == 1 && self.userDataConfig.enable_integration_status_worklist == 1) ? [0, 1, 4, 5, 6, 7, 8, 10, 12, 13, 16] : (self.isActive == 'completed' && self.userDataConfig.enable_sftp_integration != 1 && self.userDataConfig.enable_integration_status_worklist == 1) ? [0, 1, 4, 5, 6, 7, 8, 10, 12, 13, 16] : (self.isActive == 'pending' && self.userDataConfig.enable_sftp_integration != 1 && self.userDataConfig.enable_integration_status_worklist == 1) ? [0, 1, 4, 5, 6, 7, 8, 10, 12, 13, 19] : (self.isActive == 'archived' && self.userDataConfig.enable_sftp_integration != 1 && self.userDataConfig.enable_integration_status_worklist == 1) ? [0, 1, 4, 5, 6, 7, 8, 10, 12, 13, 16, 17, 18] : self.isActive == 'pending' ? [0, 1, 4, 5, 6, 7, 8, 10, 13, 19] : self.isActive == 'completed' ? [0, 1, 4, 5, 6, 7, 8, 10, 13, 16] : self.isActive == 'archived' ? [0, 1, 4, 5, 6, 7, 8, 10, 13, 16, 17, 18] : []
                                        },
                                        action: function(e, dt, node, config) {
                                             var selfButton = this;
                                             var oldStart = dt.settings()[0]._iDisplayStart;
                                             dt.one('preXhr', function(e, s, data) {
                                                  data.start = 0;
                                                  data.length = self.totalCt;
                                                  dt.one('preDraw', function(e, settings) {
                                                       $.fn.dataTable.ext.buttons.excelHtml5.action.call(selfButton, e, dt, node, config);
                                                       dt.one('preXhr', function(e, s, data) {
                                                            settings._iDisplayStart = oldStart;
                                                            data.start = oldStart;
                                                       });
                                                       setTimeout(dt.ajax.reload, 0);
                                                  });
                                             });
                                             dt.ajax.reload();
                                        }
                                   }
                              ]
                         }],
                         initComplete: function() {
                              if (((formStatus === Status.Completed || formStatus === Status.Archived) && self.previlages.cancelCompletedArchivedForms)  || formStatus === Status.Pending || (formStatus === Status.Completed && self.previlages.allowArchiveForms)) {
                                   $("div.addButton .form-more-action").remove();
                                   $("div.addButton")
                                        .append('<small style="margin-left: -60px;" class="cat__apps__messaging__tab__time action-btn-container-inbox form-more-action"><button aria-expanded="false" class="btn btn-sm btn-default dropdown-toggle action-btn" data-toggle="dropdown" type="button"> More</button><ul class="dropdown-menu">');
                                   if (formStatus === Status.Completed) {
                                        $("div.addButton ul")
                                             .append('<a class="dropdown-item"  id="archiveForm">'+self._ToolTipService.getTranslateData('BUTTONS.ARCHIVE')+'</a><a class="dropdown-item" id="cancelForm">'+self._ToolTipService.getTranslateData('BUTTONS.CANCEL')+'</a>');
                                        if(!self.previlages.cancelCompletedArchivedForms) {
                                             $("div.addButton #cancelForm").remove();
                                        }
                                   } else if (formStatus === Status.Pending || (formStatus === Status.Archived && self.previlages.cancelCompletedArchivedForms)) {
                                        $("div.addButton #archiveForm").remove();
                                        $("div.addButton ul")
                                             .append('<a class="dropdown-item" id="cancelForm">'+self._ToolTipService.getTranslateData('BUTTONS.CANCEL')+'</a>');
                                   } else {
                                        $("div.addButton #archiveForm").remove();
                                        $("div.addButton #cancelForm").remove();
                                   }
                                   $("div.addButton")
                                        .append('</ul></small>');
                              }
                              $('.dataTables_filter label input').attr('placeholder', 'Search');
                              $('.dataTables_filter label input').attr('id', 'search_fld');
                              $('.dataTables_filter label input').unbind();
                              $('div.dataTables_filter').addClass('all_form_search');
                               $("div.dataTables_filter input").on('input', function(e) {
                                   var value = $("div.dataTables_filter input").val();
                                   if (value) {
                                       $(".searchB").prop('disabled', false);  
                                   } 
                                   else {
                                       $(".searchB").prop('disabled', true);   
                                   }
                               });

                              $("div.dataTables_filter input").on('keydown', function(e) {
                                   if (e.which == 13) {
                                        var value = $("div.dataTables_filter input").val();
                                        if (value) {
                                             self.dTable.search(value);
                                        } else {
                                             self.dTable.search("");
                                        }
                                   }
                              });
                              $("div.dataTables_filter input").on('keypress', function(e) {
                                   $(".searchB").prop('disabled', false);
                              });
                              $("div.dataTables_filter input").on('keyup', function(e) {
                                   var value = $("div.dataTables_filter input").val();
                                   if (value) {} else
                                        $(".searchB").prop('disabled', true);
                              });
                              let label = self._ToolTipService.getTranslateData('TOOLTIPS.ADVANCE_SEARCH');
                              $("div.form-worklist-search-button")
                                   .html('<button disabled="true" class="btn btn-sm btn-info searchB reset-btn" id="searchB" title="Search" type="submit">Search</button>' +
                                        '<button style="margin-left:10px;" class="btn btn-sm btn-default resetB reset-btn" id="resetB" title="Reset" type="submit">Reset</button>' +
                                        '<button class="btn btn-sm btn-default reset-btn" id="advance-searchB" style="margin-left:10px;">' + 
                                        '<img src="./assets/img/filter.png" data-toggle="tooltip" id="advance-searchB" data-placement="top" title="'+label+'" class="adv-search" style="background-color: #acb7bf;height: 18px;">' +
                                        '</button>');
                              var value = $("div.dataTables_filter input").val();
                              if (value) {
                                   $(".searchB").prop('disabled', false);
                              }
                              $("div.dataTables_filter input").on('paste', function(event) {
                                   var element = this;
                                   var text;
                                   setTimeout(function() {
                                        text = $(element).val();
                                        if (text) {
                                             $(".searchB").prop('disabled', false);
                                        }
                                   }, 100);
                              });
                         },
                         fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
                              var patientDetail = '';
                              if (aData && aData.patientName) {
                                   if (self.isActive == "draft") {
                                        patientDetail = aData.patientDob ? 'DOB : ' + moment(aData.patientDob).format('MM/DD/YYYY') : '';
                                   } else {
                                        if (aData.caregiver_userid)
                                             patientDetail = aData.caregiver_dob ? 'DOB : ' + moment(aData.caregiver_dob).format('MM/DD/YYYY') : '';
                                        else
                                             patientDetail = aData.patientDob ? 'DOB : ' + moment(aData.patientDob).format('MM/DD/YYYY') : '';
                                   }
                                   $(nRow).find('.patient-details').tooltip({
                                        html: true,
                                        title: patientDetail
                                   });
                              }
                              $(nRow).attr('id', 'row-' + aData.id);
                              $(nRow).on('click', (e) => {
                                   this.activeStrucuredForms = aData;
                                   if (e.target.id == "inte-status-sent") {
                                        let ids = 'inte-status-sent-' + this.activeStrucuredForms.created_date;
                                        this.popover(ids, 'loader');
                                        this._formsService.getIntegrationDetails('sent', this.activeStrucuredForms.form_submission_order_id).then((row) => {
                                             this.integrationDetails = row;
                                             this.popover(ids, 'show');
                                        });
                                   } else if (e.target.id.substring(0, 9) == "int-close") {
                                        let ids = 'inte-status-sent-' + e.target.id.replace('int-close-', "");
                                        this.popover(ids, 'close');
                                   } else if (e.target.id == "int-copy-sent") {
                                        this.CopyToClipboard('int-content-' + this.activeStrucuredForms.sentDate);
                                   } else if (e.target.id == "int-copy-discrete") {
                                        this.CopyToClipboard('int-content-discrete-' + this.activeStrucuredForms.sentDate);
                                   } else if (e.target.id == "int-copy-receive") {
                                        this.CopyToClipboard('int-content-receive-' + this.activeStrucuredForms.created_date);
                                   } else if (e.target.id == "inte-status-receive") {

                                        let ids = 'inte-status-receive-' + this.activeStrucuredForms.created_date;
                                        this.popover(ids, 'loader');
                                        this._formsService.getIntegrationDetails('receive', this.activeStrucuredForms.form_submission_order_id).then((row) => {
                                             this.integrationDetails = {
                                                  count: row['result'].length,
                                                  type: "receive",
                                                  data: row['result']
                                             };
                                             this.popover(ids, 'show');
                                        });

                                   } else if ((e.target.id == "inte-status-not-sftp") || (e.target.id == "inte-status-not-sftp-discrete")) {
                                        let ids = e.target.id + '-' + this.activeStrucuredForms.sentDate;
                                        let request_id = e.target.id == "inte-status-not-sftp"? this.activeStrucuredForms.request_id : this.activeStrucuredForms.discrete_request_id
                                        this.popoverNotSftp(ids, 'loader');
                                        let patientID = this.activeStrucuredForms.caregiver_userid ? this.activeStrucuredForms.caregiver_userid : this.activeStrucuredForms.recipient_id;
                                        this._formsService.getIntegrationDetails(
                                             'not-sftp',
                                             request_id,
                                             this.activeStrucuredForms.tenant_id,
                                             patientID,
                                        ).then((row) => {
                                             this.integrationDetails = row;
                                             this.integrationDetails.type = e.target.id;
                                             this.popoverNotSftp(ids, 'show');
                                        });
                                   } else if (e.target.id == "inte-status-not-sftp-form") {
                                        let ids = 'inte-status-not-sftp-form-' + this.activeStrucuredForms.tag_id+'-'+this.activeStrucuredForms.id;
                                        this.popoverNotSftpForm(ids, 'loader');
                                        if(this.activeStrucuredForms.tag_id){
                                        this._formsService.getDocumentTypeDetails(this.activeStrucuredForms.tag_id
                                        ).then((row) => {
                                             this.documentTagDetails = row['getSessionTenant']['formTypeDetails'];
                                             if(this.documentTagDetails && this.documentTagDetails.id)
                                             this.popoverNotSftpForm(ids, 'show');
                                             else
                                             this.popoverNotSftpForm(ids, 'showNill');
                                        });
                                        }else{
                                             this.popoverNotSftpForm(ids, 'showNill');
                                        }
                                   }
                              });
                              $(".buttons-collection").click(function(event) {
                                   setTimeout(function() {
                                        if ($(".buttons-collection").attr("aria-expanded") === "true" && $(".dt-button-collection").find("button").length == 0) {
                                             $(".dt-button-collection").remove();
                                             $(".dt-button-background").remove();
                                             $(".buttons-collection").attr("aria-expanded", "false");
                                        }
                                   }, 500);
                              });
                         },
                         "ajax": (dat, callback, settings) => {
                              let searchText;  
            if ('formWorklistColumns' in self.advanceSearchInput) {
              delete self.advanceSearchInput.formWorklistColumns;
            }
                              if (!self.isTableLoaded[self.isActive]) {
                                   Object.keys(self.isTableLoaded).forEach(tab => {
                                        self.isTableLoaded[tab] = false;
                                    });
                                   self.isTableLoaded[self.isActive] = true;
                                   const searchKey = self.getSearchKey(self.isActive);
                                   const searchStored = self.storeService.getStoredData(searchKey);

                                   if(searchStored && searchStored.advanceSearchInput && !isBlank(searchStored.advanceSearchInput)) {
                                        self.advanceSearchInput = searchStored.advanceSearchInput;
                                        const  { dynamicControls, isSearchAvailable } = self._structureService.setAdvanceControlDefaultValues('allform', self.isActive, self.advanceSearchInput);
                                        self.dynamicControls = dynamicControls;
                                        this.showFilterAppliedMessage = isSearchAvailable;
                                   } else {
                                        self.isTableLoaded[self.isActive] = true;
                                        const searchText = searchStored && searchStored.searchText ? searchStored.searchText : '';
                                        dat.search.value = searchText;
                                        if (searchText) {
                                            this.showFilterAppliedMessage = true;
                                            setTimeout(() => {
                                                const searchBox = $('.dataTables_filter label input');
                                                searchBox.val(searchText);
                                            }, 1);
                                        }
                                   }
                              }
                              
                              if(Object.keys(self.advanceSearchInput).length > 0) {
                              /**Set advance search input to the api input parameter */
                                   searchText = JSON.stringify(self.advanceSearchInput);
                                   self.storeService.storeData(self.getSearchKey(self.isActive), { advanceSearchInput: self.advanceSearchInput });
                              } else {
                                   self._structureService.hideAdvanceSearchArea = true;
                                   searchText = dat.search.value ? dat.search.value : '';
                                   self.storeService.storeData(self.getSearchKey(self.isActive), {searchText});
                              }

                              if (settings.oAjaxData.start != 0 && self.datam && self.datam.aaData && self.datam.aaData.length == 1 && settings._iDisplayStart != 0 && searchText == '') {
                                   settings.oAjaxData.start = settings.oAjaxData.start - settings.oAjaxData.length;
                                   settings._iDisplayStart = settings.oAjaxData.start;
                              }
                              if (allIds && allIds.length > 0 && settings.oAjaxData.start != 0 && self.datam && self.datam.aaData && allIds.length >= self.datam.aaData.length && settings._iDisplayStart != 0 && searchText == '') {
                                   settings.oAjaxData.start = settings.oAjaxData.start - settings.oAjaxData.length;
                                   settings._iDisplayStart = settings.oAjaxData.start;
                              }
                              let orderData;
                              let orderby;
                              let limit;
                              var i = dat.order[0].column ? dat.order[0].column : '';
                              orderby = dat.order[0].dir ? dat.order[0].dir : '';
                              if (i != 0) {
                                   if (i == 13)
                                        orderData = 'senton';
                                   else
                                        orderData = dat.columns[i].data ? dat.columns[i].data : '';
                              } else {
                                   orderData = '';
                              }
                              this.filterType = "";
                              let enableIntegrationStatus = false;
                              if (this.userDataConfig.enable_integration_status_worklist == 1) {
                                   enableIntegrationStatus = true;
                              }
                              let enableSftpIntegration = false;
                              if (this.userDataConfig.enable_sftp_integration == 1) {
                                   enableSftpIntegration = true;
                              }
                              orderData = orderData == 'fname' ? 'fnameSort' : orderData == 'lname' ? 'lnameSort' : orderData;
                              this._structureService.manageNotifySearchFilterApplied([this.getSearchKey(self.isActive)], this.dateRangeStoreKey, this.isFirtsLoad);
                              this.isFirtsLoad = false;
            let advancedSearchPayload = {};
            if (!isBlank(self.selectedAdvancedFilterItem) && self.selectedAdvancedFilterItem.length > 0) {
              self.advanceSearchInput = JSON.parse(self.selectedAdvancedFilterItem[0].searchFilterCriteria).filters;
              if ('formWorklistColumns' in self.advanceSearchInput) {
                delete self.advanceSearchInput.formWorklistColumns;
              }
            }
            if (self.isAdvancedSearchView) {
              advancedSearchPayload = {
                limit: CONSTANTS.contentLimit,
                offset: dat.start,
                siteIds: self.siteIds.toString(),
                searchText: self.advanceSearchInput
              };
            }
            self._formsService
              .getAllTaggedFormsLazyPagination(
                timezone.name(),
                true,
                dat.length,
                dat.start,
                searchText,
                orderData,
                orderby,
                formStatus,
                isScheduled,
                this.filterType,
                this.userData.userId,
                this.includeOtherForms,
                '',
                enableIntegrationStatus,
                enableSftpIntegration,
                self.siteIds,
                self.isAdvancedSearchView,
                advancedSearchPayload).then((response: any) => {
                const dataa = this.processFormListData(response);
                                   datas = {};
                                   self.datam = {};
                                   self.totalCt = dataa['totalCt'];
                                   datas = dataa['response'] ? dataa['response'] : [];
                                   self.strucuredForms = datas;
                                   $('#form-select-all').prop("checked", false);                                   
                                   const total = (datas && datas.length === 0 && searchText === '' && dat.start === 0) ? 0 : self.totalCt;

                                   self.datam = {
                                       "draw": dat.draw,
                                       "recordsTotal": total,
                                       "recordsFiltered": total,
                                       "aaData": datas
                                   };
                                   callback(self.datam)
                                   self.clickedTab = "";
                                   this.dataLoadingMsg1 = false;
                                   self.dataTableLoading = false;
                              }).catch(function(e) {
                                   if(self.isApiError == false){
                                        self.isApiError = true;
                                        $.notify({'message':'<strong>We are facing some technical problem to load the Worklists. Please contact your administrator</strong>'},
                                        {
                                             allow_dismiss: true, type: "danger", delay: 0,
                                             onClose : function(){ self.isApiError = false; }
                                        });
                                   }
                               });
                         },
                        
                         columnDefs: [
                        {
                          title:
                            '<input type="checkbox" name="select_all" value="1" style="float:left" class="form-select-all-indicator form-select-cls" id="form-select-all" title="Select">',
                          data: null,
                          orderable: false,
                          searchable: false,
                          className: 'dt-body-center',
                          render: function (data, type, row, meta) {
                            let roNo =
                              meta.row + 1 + meta.settings._iDisplayStart;
                            if (formStatus == 'pending' && row.allow_edit != 1)
                              return (
                                '<input type="checkbox" name="formid[]" class="form-select-all-indicator form-select-cls" value="' +
                                row.id +
                                '">'
                              );
                            else if (
                              formStatus == 'pending' &&
                              row.allow_edit == 1
                            )
                              return (
                                '<input disabled type="checkbox" name="formid[]" class="form-select-all-indicator form-select-cls" value="' +
                                row.id +
                                '">'
                              );
                            else if (
                              formStatus == 'completed' &&
                              self.previlages.allowArchiveForms == true
                            )
                              return (
                                '<input type="checkbox" name="formid[]" class="form-select-all-indicator form-select-cls" value="' +
                                row.id +
                                '">'
                              );
                            else if (
                              formStatus == 'completed' &&
                              self.previlages.allowArchiveForms != true
                            )
                              return roNo;
                              else if (formStatus == 'archived') return roNo;
                            else if (formStatus == 'draft') return roNo;
                          },
                          width: '5%',
                          targets: 0,
                        },
                        {
              title: self.columnConfig[1].title,
              data: self.columnConfig[1].data,
              visible: self.isAdvancedSearchView ? self.isActive !== 'draft' && self.columnConfig[1].isSelected : self.isActive !== 'draft',
                          targets: 1,
                          width: '15%',
                        },
                        {
              title: self.columnConfig[2].title,
              data: self.columnConfig[2].data,
              visible: self.isAdvancedSearchView ? self.isActive === 'draft' && self.columnConfig[2].isSelected : self.isActive === 'draft',
                          targets: 2,
                          width: '10%',
                        },
                        {
              title: self.columnConfig[3].title,
              data: self.columnConfig[3].data,
              visible: self.isAdvancedSearchView ? self.isActive === 'draft' && self.columnConfig[3].isSelected : self.isActive === 'draft',
                          targets: 3,
                          width: '15%',
                          render: function (data, type, row) {
                            return (
                              `<span class="reminder-details" style=" cursor: pointer;" data-form-edit="${row.form_id}" data-form-submission-edit="${row.form_submission_id}"  >` +
                              row.modifiedbyname +
                              `</span>`
                            );
                          },
                        },
                        {
              title: self.columnConfig[4].title,
              data: self.columnConfig[4].data,
                          targets: 4,
                          width: '15%',
              visible: self.isAdvancedSearchView ? !showPatientFirstLastName && self.columnConfig[4].isSelected : !showPatientFirstLastName,
                          render: function (data, type, row) {
                            let patientDetails = '';
                            if (
                              row && isBlank(row.patientName)
                            ) {
                              return '';
                            }
                            if (row && !isBlank(row.patientName)) {
                              patientDetails = ` <i class='patient-details icmn-info'></i>`;
                            }
                            if (self.isActive === 'draft') {
                              if (row.facing_new === 1) {
                                if (
                                  row.recepient_id === row.sender_id &&
                                  row.sender_id === row.patient_id
                                ) {
                                  return '';
                                } else {
                                  if (row.caregiver) {
                                    return (
                                      row.patientName +
                                      ' (' +
                                      row.original_patient_displayname +
                                      ')' +
                                      patientDetails
                                    );
                                  } else {
                                    return row.patientName
                                      ? row.patientName + patientDetails
                                      : '';
                                  }
                                }
                              } else {
                                if (row.caregiver) {
                                  return (
                                    row.patientName +
                                    ' (' +
                                    row.original_patient_displayname +
                                    ')' +
                                    patientDetails +
                                    ''
                                  );
                                } else {
                                  if (
                                    row.createdUser === row.patientName &&
                                    row.facing_new === 2
                                  ) {
                                    return '';
                                  } else {
                                    return row.patientName
                                      ? row.patientName + patientDetails + ''
                                      : '';
                                  }
                                }
                              }
                            } else {
                              if (row.facing_new === 2) {
                                // Practitioner flow
                                if (
                                  row && isBlank(row.caregiver_displayname) || 
                                  row.caregiver_displayname === 'Administrator' ||
                                  row.createdUser === row.caregiver_displayname
                                ) {
                                  return '';
                                } else {
                                  return (
                                    row.caregiver_displayname + patientDetails
                                  );
                                }
                              } else {
                                if (row.caregiver_userid) {
                                  return (
                                    row.caregiver_displayname +
                                    ' (' +
                                    row.patientName +
                                    ')' +
                                    patientDetails
                                  );
                                } else {
                                  return row.patientName
                                    ? row.patientName + patientDetails
                                    : '';
                                }
                              }
                            }
                          },
                        },
                        
                        {
              title: self.columnConfig[5].title,
              data: self.columnConfig[5].data,
                         targets: 5,
                         width: "15%",
              visible: self.isAdvancedSearchView ? showPatientFirstLastName && self.columnConfig[5].isSelected : showPatientFirstLastName,
                         render: function(data, type, row) {
                              let patientDetails = '';
                              if (row && isBlank(row.fname)){
                                   return '';
                              } else {
                                   patientDetails = ` <i class='patient-details icmn-info'></i>`;
                              }
                              if (self.isActive === "draft") {
                                   if (row.facing_new == 1) {
                                        if (row.recepient_id === row.sender_id && row.sender_id === row.patient_id) {
                                             return '';
                                        } else {
                                             if (row.caregiver) {
                                                  return row.fname + " (" + row.original_patient_displayname + ")" + patientDetails;
                                             } else {
                                                  return row.fname && row.patientName ? row.fname + patientDetails : "";
                                             }
                                        }
                                   } else {
                                        if (row.caregiver) {
                                             return row.fname + " (" + row.original_patient_displayname + ")" + patientDetails +'';
                                        } else {
                                             if(row.createdUser===row.patientName&& row.facing_new === 2){
                                                  return '';
                                             }else{
                                                  return (row.fname && row.patientName) ? row.fname + patientDetails+'' : "";
                                             }
                                        }
                                   }
                              } else {
                                   if (row.facing_new === 2) {
                                        if(row && isBlank(row.caregiver_displayname) || row.caregiver_displayname === "Administrator" || row.createdUser===row.caregiver_displayname ){
                                             return '';
                                         }else{
                                             return row.cgiver_fname ? row.cgiver_fname + patientDetails : "";
                                         }
                                       
                                   } else {
                                        if (row.caregiver_userid) {
                                             return row.cgiver_fname + " (" + row.patientName + ")" + patientDetails;
                                        } else {
                                             return row.fname && row.patientName ? row.fname + patientDetails : "";
                                        }
                                   }
                              }
                         },
                    },
                    {
              title: self.columnConfig[6].title,
              data: self.columnConfig[6].data,
                         targets: 6,
                         width: "15%",
              visible: self.isAdvancedSearchView ? showPatientFirstLastName && self.columnConfig[6].isSelected : showPatientFirstLastName,
                         render: function(data, type, row) {
                              if (row && isBlank(row.lname)){
                                   return '';
                              }
                              if (self.isActive === "draft") {
                                   if (row.facing_new === 1) {
                                        if (row.recepient_id === row.sender_id && row.sender_id === row.patient_id) {
                                             return '';
                                        } else {
                                             return row.lname && row.patientName ? row.lname : "";
                                        }
                                   } else {
                                        if (row.caregiver) {
                                             return row.lname;
                                        } else {
                                             if(row.createdUser === row.patientName && row.facing_new === 2){
                                                  return '';
                                             }else{
                                                  return row.lname && row.patientName ? row.lname : "";
                                             }
                                        }
                                   }
                              } else {
                                   if (row.facing_new === 2) {
                                        if(row && isBlank(row.caregiver_displayname) || row.caregiver_displayname === "Administrator" || row.createdUser === row.caregiver_displayname ){
                                             return '';
                                         }else{
                                             return row.cgiver_lname;
                                         }
                                   } else {
                                        if (row.caregiver_userid) {
                                             return row.cgiver_lname;
                                        } else {
                                             return row.lname && row.patientName ? row.lname : "";
                                        }
                                   }
                              }
                         },
                    },
                    
                        {
              title: self.columnConfig[7].title,
              data: self.columnConfig[7].data,
                          targets: 7,
              visible: self.isAdvancedSearchView ? self.columnConfig[7].isSelected : true,
                          render: function (data, type, row) {
                            if (
                              row.patientIdentityValue && !isBlank(row.patientIdentityValue)
                            ) {
                              return row.patientIdentityValue;
                            } else if (
                              !row.patientIdentityValue &&
                              row.patientIdentityValue == null && !isBlank(row.cgiverIdentityValue)
                            ) {
                              return row.cgiverIdentityValue;
                            } else {
                              return '';
                            }
                          },
                        },
                        {
              title: self.columnConfig[8].title,
              data: self.columnConfig[8].data,
                         targets: 8,
              visible: self.isAdvancedSearchView
                ? self.structureService.isMultiAdmissionsEnabled && self.columnConfig[8].isSelected
                : self.structureService.isMultiAdmissionsEnabled,
                         render: function(data, type, row) {
                              if (!isBlank(row.admissionName)) {
                                   return row.admissionName;
                              } 
                              return "";
                              
                         }
                        },
                        {
              title: self.columnConfig[9].title,
              data: self.columnConfig[9].data,
                          targets: 9,
                          width: '15%',
              visible: self.isAdvancedSearchView ? self.columnConfig[9].isSelected : true,
                          render: function (data, type, row) {
                            if (self.userData.group != 3) {
                              let sentPopover = ``;

                              let statusHtml = '';

                              let popoverSent = `<div id="inte-status-not-sftp-form-${row.tag_id}-${row.id}" class="fade show bs-popover-right inte-popover" style="display:none;"
                                        >
                                        <div class="popover-arrow"></div>
                                        <div class="pop-title">
                                             <button type="button" id="int-close-form-${row.tag_id}-${row.id}" class="close fa fa-close"></button>
                                        </div>
                                        <div class="pop-content-form">
                                             <div class="int-loader">
                                                  <img src="./assets/modules/dummy-assets/common/img/spinner.svg" alt="loading">
                                             </div>
                                             <div class="int-main-content-form" id="int-content-form-${row.tag_id}-${row.id}">
                                                  ${sentPopover}
                                             </div>
                                        </div>    
                                        </div>`;

                              statusHtml += `
                                        <div style="position:relative;">
                                        <span mr-2 mb-2">${row.form_name}</span>
                                        <span style="font-size: 14px; color: #0000FF; cursor: pointer;">
                                        <i class='icmn-info'id="inte-status-not-sftp-form"></i>
                                        </span>
                                        ${popoverSent}
                                        </div>`;

                              return statusHtml;
                            } else {
                              return row.form_name;
                            }
                          },
                        },
                        {
              title: self.columnConfig[10].title,
              data: self.columnConfig[10].data,
              visible:
                (self.isAdvancedSearchView ? self.columnConfig[10].isSelected : true) &&
                self.userRole !== 'Patient' &&
                ((+self.userData.config.enable_multisite === 1 && self.userData.mySites.length > 1) ||
                  (!(+self.userData.config.enable_multisite === 1) && +self.userData.enable_cross_site === 1 && self.userData.mySites.length > 1) ||
                  self.userData.mySites.length > 1 ||
                  (+self.userData.config.enable_multisite === 1 && +self.userData.enable_cross_site === 1) ||
                  self.userData.mySites.length > 1),
                          targets: 10,
                          orderable: true,
                          render: function (data, type, row) {
                            if (row.siteName) {
                              return row.siteName;
                            } else {
                              return null;
                            }
                          },
                        },
                        {
              title: self.columnConfig[11].title,
              data: self.columnConfig[11].data,
                          targets: 11,
                          width: '5%',
                          orderable: false,
              visible: self.isAdvancedSearchView ? self.columnConfig[11].isSelected : true,
                          render: function (data, type, row) {
                            if (
                              (row.patient_id != '0' &&
                                (self.isActive == 'completed' ||
                                  self.isActive == 'archived' ||
                                  self.isActive == 'draft') &&
                                row.form_type == 'Staff Facing' &&
                                row.facing_new != 2) ||
                              (row.allow_edit == 1 &&
                                row.form_type == 'Staff Facing' &&
                                row.facing_new != 2)
                            ) {
                              return '<span class="badge badge-success mr-2 mb-2">Staff/Partner Facing </span>';
                             } else if (row.facing_new === 2) {
                                return '<span class="badge badge-primary mr-2 mb-2">Staff-Practitioner Facing </span>';
                             } else if (row.facing_new === 0) {
                                return '<span class="badge badge-primary mr-2 mb-2">Patient Facing </span>';
			     } else if (row.form_type == 'Staff Facing' && row.facing_new != 2) {
				return '<span class="badge badge-success mr-2 mb-2"> Staff/Partner Facing </span>';
			    }
                          },
                        },
                        {
              title: `${self.columnConfig[12].title} <i class='review-tooltip icmn-info' data-toggle='tooltip' data-placement='right'></i>`,
              data: self.columnConfig[12].data,
                          orderable: false,
                          searchable: false,
              visible:
                (self.isAdvancedSearchView ? self.columnConfig[12].isSelected : true) &&
                (self.isActive === 'completed' || self.isActive === 'archived') &&
                +self.userData.group !== 3,
                          className: 'dt-body-center reviewcheckclass',
                          render: function (data, type, row, meta) {
                            var checked =
                              row.reviewed && row.reviewed == 1
                                ? 'checked="checked"'
                                : '';
                            var ReviewStatusNew =
                              row.reviewed && row.reviewed == 1 ? 2 : 1;
                            if (
                              self.userData.privileges &&
                              self.userData.privileges.includes(
                                'reviewSubmittedForm'
                              )
                            )
                              return (
                                '<input type="checkbox" name="form_review_status" ' +
                                checked +
                                ' data-form_name="' +
                                row.form_name +
                                '" class="form-select-all-indicator form-select-cls reviewstatuscheck" data-submission_id="' +
                                row.form_submission_id +
                                '" value="' +
                                row.id +
                                '" data-form_id="' +
                                row.form_id +
                                '" data-status="' +
                                ReviewStatusNew +
                                '" id="' +
                                row.form_id +
                                '_' +
                                row.form_submission_id +
                                '">'
                              );
                            else
                              return (
                                '<input type="checkbox" name="form_review_status" class="form-select-all-indicator disabled form-select-cls" ' +
                                checked +
                                ' value="' +
                                row.id +
                                '">'
                              );
                          },
                          width: '5%',
                          targets: 12,
                        },
                        {
              title: `${self.columnConfig[13].title} <i class='default-Scheduled icmn-info'></i>`,
              data: self.columnConfig[13].data,
                          targets: 13,
                          width: '8%',
              visible: self.isAdvancedSearchView ? self.isActive !== 'draft' && self.columnConfig[13].isSelected : self.isActive !== 'draft',
                          render: function (data, type, row) {
                            if (data == 1) {
                              return 'Yes';
                            } else {
                              return 'No';
                            }
                          },
                        },
                        {
              title: self.columnConfig[14].title,
              data: self.columnConfig[14].data,
                          orderable: true,
                          searchable: false,
                          targets: 14,
              visible: self.isAdvancedSearchView ? self.columnConfig[14].isSelected : true,
                          render: (data, type, row) => {
                            if (row.applessMode == '1') {
                              return 'AppLess (MagicLink)';
                            } else {
                              return 'In-App';
                            }
                          },
                        },
                        {
              title: self.columnConfig[15].title,
              data: self.columnConfig[15].data,
                          targets: 15,
                          orderable: false,
                          searchable: false,
                          width: '16%',
              visible:
                (self.isAdvancedSearchView ? self.columnConfig[15].isSelected : true) &&
                (self.isActive === 'completed' &&
                +this.userDataConfig.enable_sftp_integration === 1 &&
                +this.userDataConfig.enable_integration_status_worklist === 1
                  ? true
                  : (self.isActive === 'completed' || self.isActive === 'pending' || self.isActive === 'archived') &&
                    +this.userDataConfig.enable_sftp_integration !== 1 &&
                    +this.userDataConfig.enable_integration_status_worklist === 1),
                          render: (data, type, row) => {
                            if (
                              this.userDataConfig.enable_sftp_integration != 1
                            ) {
                              if (
                                !row.request_id ||
                                typeof row.request_id == 'undefined'
                              ) {
                                return '';
                              }

                              let sentPopover = ``;

                              let statusHtml = '';

                              let popoverSent = `<div id="inte-status-not-sftp-${row.sentDate}" class="fade show bs-popover-right inte-popover" style="display:none;"
            >
              <div class="popover-arrow"></div>
              <div class="pop-title">
                  <button type="button" id="int-close-${row.sentDate}" class="close fa fa-close"></button>
                  <div>
                      <i class="copy-ico fa fa-files-o" id="int-copy-sent" aria-hidden="true"></i>
                  </div>
              </div>
              <div class="pop-content">
                  <div class="int-loader">
                      <img src="./assets/modules/dummy-assets/common/img/spinner.svg" alt="loading">
                  </div>
                  <div class="int-main-content" id="int-content-${row.sentDate}">
                      ${sentPopover}
                  </div>
              </div>    
             </div>`;

                              let style = 'badge-warning';
                              let text = 'Processing';
                              let color_code = '#f39834';

                              if (row.integration_status == 'completed') {
                                style = 'badge-success';
                                text = 'Completed';
                                color_code = '#46be8a';
                              }

                              if (
                                row.integration_status == 'failed' ||
                                row.integration_status == 'Error'
                              ) {
                                style = 'badge-danger';
                                text = 'Failed';
                                color_code = '#fb434a';
                              }

                              statusHtml += `
              <div style="position:relative;">
              <span class="badge ${style} mr-2 mb-2">${text}</span>
              <span style="font-size: 14px; color: ${color_code}; cursor: pointer;">
              <i class='icmn-info'id="inte-status-not-sftp"></i>
              </span>
            ${popoverSent}
            </div>`;

                              return statusHtml;
                            } else {
                              if (
                                self.isActive != 'completed' &&
                                this.userDataConfig
                                  .enable_integration_status_worklist != 1
                              ) {
                                return '';
                              }
                              let sentOn = '';
                              if (
                                row.patient_id != '0' &&
                                self.isActive == 'completed' &&
                                row.created_date
                              ) {
                                sentOn = this.formpPipe.transform(
                                  parseInt(row.senton) * 1000
                                );
                              } else {
                                sentOn = this.formpPipe.transform(
                                  parseInt(row.sentDate) * 1000
                                );
                              }

                              let patientId;
                              if (
                                (row.patient_id != '0' &&
                                  (this.isActive == 'completed' ||
                                    this.isActive == 'archived' ||
                                    this.isActive == 'draft') &&
                                  row.form_type == 'Staff Facing' &&
                                  row.facing_new != 2) ||
                                (row.allow_edit == 1 &&
                                  row.form_type == 'Staff Facing' &&
                                  row.facing_new != 2)
                              ) {
                                patientId = row.recipient_id;
                              } else {
                                if (row.facing_new == 2) {
                                  patientId = row.associated_user_id;
                                } else {
                                  patientId = row.recipient_id;
                                }
                              }

                              let sentPopover = `OrderId: ${
                                row.form_submission_order_id &&
                                row.form_submission_order_id != 'null'
                                  ? row.form_submission_order_id
                                  : ''
                              }
                <br />
                Citus ESI: ${row.mrn ? row.mrn : ''}
                <br />
                Citus MRN: ${patientId}
                <br />
                Sent On: ${sentOn}                   
                `;
                              let receivePopover = '';
                              if (
                                row.authorization &&
                                row.authorization != null &&
                                row.authorization != ''
                              ) {
                                receivePopover = `Genericdrug: ${
                                  row.drug_id ? row.drug_id : ''
                                }
                    <br />                
                    RMS OrderId: ${row.rms_id ? row.rms_id : ''}
                    <br />
                    Citus OrderId: ${
                      row.form_submission_order_id
                        ? row.form_submission_order_id
                        : ''
                    }
                    <br />
                    Citus ESI: ${row.esi_id ? row.esi_id : ''}
                    <br />
                    Authorization: ${
                      row.authorization ? row.authorization : ''
                    }`;
                              }

                              let statusHtml = '';

                              let popoverSent = `<div id="inte-status-sent-${row.created_date}" class="fade show bs-popover-right inte-popover"
                style="display:none;">
                <div class="popover-arrow"></div>
                <div class="pop-title">
                    <button type="button" id="int-close-${row.created_date}" class="close fa fa-close"></button>
                    <div>
                        <i class="copy-ico fa fa-files-o" id="int-copy-sent" aria-hidden="true"></i>
                    </div>
                </div>
                <div class="pop-content">
                    <div class="int-loader">
                        <img src="./assets/modules/dummy-assets/common/img/spinner.svg" alt="loading">
                    </div>
                    <div class="int-main-content" id="int-content-${row.created_date}">
                        ${sentPopover}
                    </div>
                </div>    
               </div>`;

                              let popoverReceive = `<div id="inte-status-receive-${row.created_date}" class="fade show bs-popover-right inte-popover"
                style="display:none;">
                <div class="popover-arrow"></div>
                <div class="pop-title">
                    <button type="button" id="int-close-${row.created_date}" class="close fa fa-close"></button>
                    <div>
                        <i class="copy-ico fa fa-files-o" id="int-copy-receive" aria-hidden="true"></i>
                    </div>
                </div>
                <div class="pop-content">
                    <div class="int-loader">
                        <img src="./assets/modules/dummy-assets/common/img/spinner.svg" alt="loading">
                    </div>
                    <div class="int-main-content" id="int-content-receive-${row.created_date}">
                        OrderId: 
                        <br />
                        MRN:
                        <br />
                        Processed At: 
                        <br />
                        SFTP Transfer At: 
                        <br>
                        File Count: 
                        <br>
                        Attachment: 
                        <br>
                    </div>
                </div>    
               </div>`;

                              // Sent

                              statusHtml += `
                  <div id="inte-status-sent" class="tag-list tag-list-log" style="background: #BF55EC; color: rgb(27, 26, 26);margin-left: 5px;">
                    ${popoverSent}
                    <span class="" id="inte-status-sent">Sent</span>
                    <i aria-hidden="true" id="inte-status-sent" class="fa fa-caret-right arrow-right"><span></span></i>
                  </div>`;
                              if (row.integration_status == 'completed') {
                                statusHtml += `        
                    <div id="inte-status-receive" class="tag-list tag-list-log" style="background: #BF55EC; color: rgb(27, 26, 26);cursor: pointer;">
                    ${popoverReceive}
                     <i  aria-hidden="true" id="inte-status-receive" class="fa fa-caret-left" style="color: #BF55EC;"><span ></span></i>        
                    <span class="" id="inte-status-receive" style="color: white;white-space: nowrap;">Received</span>
                    
                    </div>
                    `;
                              } else {
                                // Not Received
                                statusHtml += `        
                    <div class="tag-list tag-list-log" style="background: #bdc6cc; color: rgb(27, 26, 26);">
                        <i  aria-hidden="true" class="fa fa-caret-left" style="color: #bdc6cc;"><span ></span></i>
            
                    <span class="" style="color: white;white-space: nowrap;">Not Received</span>
                    
                    </div>
                    `;
                              }

                              return statusHtml;
                            }
                          },
                        },
                        {
                          
              title: self.columnConfig[16].title,
              data: self.columnConfig[16].data,
                          targets: 16,
                          orderable: false,
                          searchable: false,
                          width: '16%',
              visible:
                (self.isAdvancedSearchView ? self.columnConfig[16].isSelected : true) &&
                (self.isActive === 'completed' &&
                +this.userDataConfig.enable_sftp_integration === 1 &&
                +this.userDataConfig.enable_discrete_integration_status_worklist === 1
                  ? true
                  : (self.isActive === 'completed' || self.isActive === 'pending' || self.isActive === 'archived') &&
                    +this.userDataConfig.enable_sftp_integration !== 1 &&
                    +this.userDataConfig.enable_discrete_integration_status_worklist === 1),
                          render: (data, type, row) => {
                            if (
                              this.userDataConfig.enable_sftp_integration != 1
                            ) {
                              if (
                                !row.discrete_request_id ||
                                typeof row.discrete_request_id == 'undefined'
                              ) {
                                return '';
                              }

                              let sentPopover = ``;

                              let statusHtml = '';

                              let popoverSent = `<div id="inte-status-not-sftp-discrete-${row.sentDate}" class="fade show bs-popover-right inte-popover" style="display:none;"
            >
              <div class="popover-arrow"></div>
              <div class="pop-title">
                  <button type="button" id="int-close-${row.sentDate}" class="close fa fa-close"></button>
                  <div>
                      <i class="copy-ico fa fa-files-o int-copy-clipboard" id="int-copy-discrete" aria-hidden="true"></i>
                  </div>
              </div>
              <div class="pop-content">
                  <div class="int-loader">
                      <img src="./assets/modules/dummy-assets/common/img/spinner.svg" alt="loading">
                  </div>
                  <div class="int-main-content" id="int-content-discrete-${row.sentDate}">
                      ${sentPopover}
                  </div>
              </div>    
             </div>`;

                              let style = 'badge-warning';
                              let text = 'Processing';
                              let color_code = '#f39834';

                              if (
                                row.discrete_integration_status == 'completed'
                              ) {
                                style = 'badge-success';
                                text = 'Completed';
                                color_code = '#46be8a';
                              }

                              if (
                                row.discrete_integration_status == 'failed' ||
                                row.discrete_integration_status == 'Error'
                              ) {
                                style = 'badge-danger';
                                text = 'Failed';
                                color_code = '#fb434a';
                              }

                              statusHtml += `
              <div style="position:relative;">
              <span class="badge ${style} mr-2 mb-2">${text}</span>
              <span style="font-size: 14px; color: ${color_code}; cursor: pointer;">
              <i class='icmn-info'id="inte-status-not-sftp-discrete"></i>
              </span>
            ${popoverSent}
            </div>`;

                              return statusHtml;
                            } else {
                              if (
                                self.isActive != 'completed' &&
                                this.userDataConfig
                                  .enable_discrete_integration_status_worklist !=
                                  1
                              ) {
                                return '';
                              }
                              let sentOn = '';
                              if (
                                row.patient_id != '0' &&
                                self.isActive == 'completed' &&
                                row.created_date
                              ) {
                                sentOn = this.formpPipe.transform(
                                  parseInt(row.senton) * 1000
                                );
                              } else {
                                sentOn = this.formpPipe.transform(
                                  parseInt(row.sentDate) * 1000
                                );
                              }

                              let patientId;
                              if (
                                (row.patient_id != '0' &&
                                  (this.isActive == 'completed' ||
                                    this.isActive == 'archived' ||
                                    this.isActive == 'draft') &&
                                  row.form_type == 'Staff Facing' &&
                                  row.facing_new != 2) ||
                                (row.allow_edit == 1 &&
                                  row.form_type == 'Staff Facing' &&
                                  row.facing_new != 2)
                              ) {
                                patientId = row.recipient_id;
                              } else {
                                if (row.facing_new == 2) {
                                  patientId = row.associated_user_id;
                                } else {
                                  patientId = row.recipient_id;
                                }
                              }

                              let sentPopover = `OrderId: ${
                                row.form_submission_order_id &&
                                row.form_submission_order_id != 'null'
                                  ? row.form_submission_order_id
                                  : ''
                              }
                <br />
                Citus ESI: ${row.mrn ? row.mrn : ''}
                <br />
                Citus MRN: ${patientId}
                <br />
                Sent On: ${sentOn}                   
                `;
                              let receivePopover = '';
                              if (
                                row.authorization &&
                                row.authorization != null &&
                                row.authorization != ''
                              ) {
                                receivePopover = `Genericdrug: ${
                                  row.drug_id ? row.drug_id : ''
                                }
                    <br />                
                    RMS OrderId: ${row.rms_id ? row.rms_id : ''}
                    <br />
                    Citus OrderId: ${
                      row.form_submission_order_id
                        ? row.form_submission_order_id
                        : ''
                    }
                    <br />
                    Citus ESI: ${row.esi_id ? row.esi_id : ''}
                    <br />
                    Authorization: ${
                      row.authorization ? row.authorization : ''
                    }`;
                              }

                              let statusHtml = '';

                              let popoverSent = `<div id="inte-status-sent-${row.created_date}" class="fade show bs-popover-right inte-popover"
                style="display:none;">
                <div class="popover-arrow"></div>
                <div class="pop-title">
                    <button type="button" id="int-close-${row.created_date}" class="close fa fa-close"></button>
                    <div>
                        <i class="copy-ico fa fa-files-o" id="int-copy-sent" aria-hidden="true"></i>
                    </div>
                </div>
                <div class="pop-content">
                    <div class="int-loader">
                        <img src="./assets/modules/dummy-assets/common/img/spinner.svg" alt="loading">
                    </div>
                    <div class="int-main-content" id="int-content-${row.created_date}">
                        ${sentPopover}
                    </div>
                </div>    
               </div>`;

                              let popoverReceive = `<div id="inte-status-receive-${row.created_date}" class="fade show bs-popover-right inte-popover"
                style="display:none;">
                <div class="popover-arrow"></div>
                <div class="pop-title">
                    <button type="button" id="int-close-${row.created_date}" class="close fa fa-close"></button>
                    <div>
                        <i class="copy-ico fa fa-files-o" id="int-copy-receive" aria-hidden="true"></i>
                    </div>
                </div>
                <div class="pop-content">
                    <div class="int-loader">
                        <img src="./assets/modules/dummy-assets/common/img/spinner.svg" alt="loading">
                    </div>
                    <div class="int-main-content" id="int-content-receive-${row.created_date}">
                        OrderId: 
                        <br />
                        MRN:
                        <br />
                        Processed At: 
                        <br />
                        SFTP Transfer At: 
                        <br>
                        File Count: 
                        <br>
                        Attachment: 
                        <br>
                    </div>
                </div>    
               </div>`;

                              // Sent

                              statusHtml += `
                  <div id="inte-status-sent" class="tag-list tag-list-log" style="background: #BF55EC; color: rgb(27, 26, 26);margin-left: 5px;">
                    ${popoverSent}
                    <span class="" id="inte-status-sent">Sent</span>
                    <i aria-hidden="true" id="inte-status-sent" class="fa fa-caret-right arrow-right"><span></span></i>
                  </div>`;
                              if (row.integration_status == 'completed') {
                                statusHtml += `        
                    <div id="inte-status-receive" class="tag-list tag-list-log" style="background: #BF55EC; color: rgb(27, 26, 26);cursor: pointer;">
                    ${popoverReceive}
                     <i  aria-hidden="true" id="inte-status-receive" class="fa fa-caret-left" style="color: #BF55EC;"><span ></span></i>        
                    <span class="" id="inte-status-receive" style="color: white;white-space: nowrap;">Received</span>
                    
                    </div>
                    `;
                              } else {
                                // Not Received
                                statusHtml += `        
                    <div class="tag-list tag-list-log" style="background: #bdc6cc; color: rgb(27, 26, 26);">
                        <i  aria-hidden="true" class="fa fa-caret-left" style="color: #bdc6cc;"><span ></span></i>
            
                    <span class="" style="color: white;white-space: nowrap;">Not Received</span>
                    
                    </div>
                    `;
                              }

                              return statusHtml;
                            }
                          },
                        },
                        {
              title: self.columnConfig[17].title,
              data: self.columnConfig[17].data,
                          targets: 17,
              visible: self.isAdvancedSearchView ? self.isActive !== 'draft' && self.columnConfig[17].isSelected : self.isActive !== 'draft',
                          width: '20%',
                          render: function (data, type, row) {
                            if (row.facing_new == 2) {
                              // Practitioner flow
                              return row.patientName;
                            } else {
                              return '';
                            }

                          },
                        },
                        {
              title: self.columnConfig[18].title,
              data: self.columnConfig[18].data,
                          targets: 18,
                          width: '10%',
              visible: self.isAdvancedSearchView ? self.isActive !== 'draft' && self.columnConfig[18].isSelected : self.isActive !== 'draft',
                          render: (data, type, row) => {
                            if (
                              row.patient_id != '0' &&
                              (self.isActive == 'completed' ||
                                self.isActive == 'archived') &&
                              row.created_date
                            ) {
                              return this.formpPipe.transform(
                                parseInt(row.sentDate) * 1000
                              );
                            } else {
                              return this.formpPipe.transform(
                                parseInt(row.sentDate) * 1000
                              );
                            }
                          },
                        },
                        {
              title: self.columnConfig[19].title,
              data: self.columnConfig[19].data,
                          targets: 19,
                          width: '10%',
              visible: self.isAdvancedSearchView ? self.isActive === 'draft' && self.columnConfig[19].isSelected : self.isActive === 'draft',
                          render: (data, type, row) => {
                            if (row.createdtimestamp) {
                              return this.formpPipe.transform(
                                parseInt(row.createdtimestamp) * 1000
                              );
                            } else {
                              return this.formpPipe.transform(
                                parseInt(row.updatedtimestamp) * 1000
                              );
                            }
                          },
                        },
                        {
              title: self.columnConfig[20].title,
              data: self.columnConfig[20].data,
                          targets: 20,
                          width: '10%',
              visible: self.isAdvancedSearchView ? self.isActive === 'draft' && self.columnConfig[20].isSelected : self.isActive === 'draft',
                          render: (data, type, row) => {
                            if (row.updatedtimestamp) {
                              return this.formpPipe.transform(
                                parseInt(row.updatedtimestamp) * 1000
                              );
                            } else {
                              return this.formpPipe.transform(
                                parseInt(row.createdtimestamp) * 1000
                              );
                            }
                          },
                        },
                        {
              title: self.columnConfig[21].title,
              data: self.columnConfig[21].data,
                          targets: 21,
                          width: '10%',
              visible:
                !(self.isAdvancedSearchView ? self.columnConfig[21].isSelected : true) && (self.isActive === 'pending' || self.isActive === 'draft'),
                          render: (data, type, row) => {
                            if (
                              self.isActive == 'archived' &&
                              !row.created_date
                            ) {
                              return '';
                            } else {
                              if (row.updatedtimestamp == null) {
                                if (data && data.toString().length == 13) {
                                  return this.formpPipe.transform(
                                    parseInt(data)
                                  );
                                } else {
                                  return this.formpPipe.transform(
                                    parseInt(data) * 1000
                                  );
                                }
                              } else {
                                return this.formpPipe.transform(
                                  parseInt(row.updatedtimestamp) * 1000
                                );
                              }
                            }
                          },
                        },
                        {
              title: self.columnConfig[22].title,
              data: self.columnConfig[22].data,
                          targets: 22,
                          width: '10%',
              visible: self.isAdvancedSearchView ? self.isActive === 'archived' && self.columnConfig[22].isSelected : self.isActive === 'archived',
                          render: (data, type, row) => {
                            if (data) {
                              return this.formpPipe.transform(
                                parseInt(data) * 1000
                              );
                            } else {
                              return '';
                            }
                          },
                        },
                        {
              title: self.columnConfig[23].title,
              data: self.columnConfig[23].data,
                          targets: 23,
                          width: '10%',
              visible: self.isAdvancedSearchView ? self.isActive === 'archived' && self.columnConfig[23].isSelected : self.isActive === 'archived',
                          render: (data, type, row) => {
                            if (data) {
                              return row.deletedName;
                            } else {
                              return '';
                            }
                          },
                        },
                        {
              title: self.columnConfig[24].title,
              data: self.columnConfig[24].data,
                          targets: 24,
                          width: '10%',
              visible: self.isAdvancedSearchView ? self.isActive === 'pending' && self.columnConfig[24].isSelected : self.isActive === 'pending',
                          render: (data, type, row) => {
                            let activeTimeZone =
                              new Date().getTimezoneOffset() * -1;
                            if (
                              self.isActive != 'pending' ||
                              !row.lastRemindedOn
                            ) {
                              return '';
                            } else {
                              return this.formpPipe.transform(
                                parseInt(row.lastRemindedOn) * 1000
                              ); 
                            }
                          },
                        },
                        {
                          className: 'details-control',
              title: self.columnConfig[25].title,
              data: self.columnConfig[25].data,
                          orderable: false,
                          render: (strucuredForm, type, row) => {
                           let actions =
                              '<div class="btn-group mr-2 mb-2 no-margin" aria-label="" role="group">';
                            if (self.isActive == 'draft') {
                              actions += `<a href="javascript: void(0);" class="reminder-details pull-right btn btn-sm" style=" cursor: pointer; color: #74708d !important; text-decoration: none !important;" data-form-edit="${row.form_id}" data-form-submission-edit="${row.form_submission_id}" ><i data-view class="icmn-history"></i></a>`;
                              actions += `<a href="javascript: void(0);" id="edit-pform" data-form-edit="${row.form_id}" data-view class="pull-right btn btn-sm edit-pform" title="Edit"><i data-form-edit="${row.form_id}" data-view class="icmn-pen"></i></a>`;
                              actions += `<a href="javascript: void(0);" id="archive-form" data-archive class="pull-right btn btn-sm" title="Cancel"><i class="icmn-cancel-circle"></i></a>`;
                              /**Add download action in draft bucket */
                              actions += `<a href="javascript: void(0);" id="download" data-download class="pull-right btn btn-sm" title="Download"><i data-download class="icmn-download"></i></a>`;
                            } else {
                              if (row.created_date) {
                                actions += `<a href="javascript: void(0);" id="view" data-view class="pull-right btn btn-sm" title="View"><i data-view class="icmn-eye"></i></a>`;
                                actions += `<a href="javascript: void(0);" id="history" data-view class="pull-right btn btn-sm" data-form-edit="${row.form_id}" data-form-submission-edit="${row.form_submission_id}" data-sent-id-edit="${row.sent_id}" data-recipient-id-edit="${row.recipient_id}" data-sent-date-edit="${row.senton}"  data-sent-date-pending-edit="${row.sentDate}" data-submit-date-edit="${row.updatedtimestamp}" title="History"><i data-view class="icmn-history"></i></a>`;
                                actions += `<a href="javascript: void(0);" id="download" data-download class="pull-right btn btn-sm" title="Download"><i data-download class="icmn-download"></i></a>`;
                              } else {
                                if (self.isActive != 'archived') {
                                  if (
                                    row.allow_edit == 1 &&
                                    self.isActive === Status.Pending &&
                                    ((this.userData.userId ==
                                      row.recipient_id &&
                                      row.staffFacing != 1) ||
                                      (this.userData.userId == row.from_id &&
                                        row.staffFacing == 1) ||
                                      row.form_submission_id)
                                  ) {
                                    actions += `<a href="javascript: void(0);" id="view" data-view class="pull-right btn btn-sm" title="View"><i data-view class="icmn-eye"></i></a>`;
                                  } else {
                                    actions += `<a href="javascript: void(0);" id="view-form" data-view class=" pull-right btn btn-sm view-form" title="View"><i data-view class="icmn-eye"></i></a>`;
                                    if (self.isActive !== Status.Pending) {
                                      actions += `<a href="javascript: void(0);" id="history" class="pull-right btn btn-sm" data-form-edit="${row.form_id}" data-form-submission-edit="${row.form_submission_id}" data-sent-id-edit="${row.sent_id}" data-recipient-id-edit="${row.recipient_id}" data-sent-date-edit="${row.senton}"  data-sent-date-pending-edit="${row.sentDate}" data-submit-date-edit="${row.updatedtimestamp}"  title="History"><i data-view class="icmn-history"></i></a>`;
                                    }
                                  }
                                }
                                if (self.isActive !== Status.Archived && this.userRole !== UserRoles.patient) {
                                   actions += `<a href="javascript: void(0);" id="resend-form" data-view class="pull-right btn btn-sm resend-form" title="${this._ToolTipService.getTranslateData('TITLES.SEND_REMINDER')}"><i class="icmn-clock" aria-hidden="true"></i></a>`;
                                }
                              }

                              if (
                                row.facing_new == 2 &&
                                row.allow_edit != 1 &&
                                self.isActive === Status.Pending &&
                                this.userData.userId == row.recipient_id
                              ) {
                                actions += `<a href="javascript: void(0);" id="edit-pform" data-form-edit="${row.form_id}" data-view class="pull-right btn btn-sm edit-pform" title="Sign"><i data-form-edit="${row.form_id}" data-view class="icmn-pen"></i></a>`;
                              } else if (
                                row.allow_edit == 1 &&
                                self.isActive === Status.Pending &&
                                ((this.userData.userId == row.recipient_id &&
                                  row.staffFacing != 1) ||
                                  (this.userData.userId == row.from_id &&
                                    row.staffFacing == 1))
                              ) {
                                actions += `<a href="javascript: void(0);" id="edit-pform" data-form-edit="${row.form_id}" data-view class="pull-right btn btn-sm edit-pform" title="Edit"><i data-form-edit="${row.form_id}" data-view class="icmn-pen"></i></a>`;
                              }
                              if (this.userRole !== UserRoles.patient) {
                                if (
                                  self.isActive === Status.Archived &&
                                  self.previlages.allowArchiveForms == true
                                ) {
                                  actions += `<a href="javascript: void(0);" id="restore-forms" class="pull-right btn btn-sm" title="Restore"><i class="fa fa-undo"></i></a>`;
                                } else if (
                                  self.isActive == 'completed' &&
                                  self.previlages.allowArchiveForms == true
                                ) {
                                  actions += `<a href="javascript: void(0);" id="archive-form" data-archive class="pull-right btn btn-sm" title="Archive"><i class="icmn-box-add"></i></a>`;
                                }
                                if (self.isActive == 'pending') {
                                  if (row.allow_edit == 1) {
                                    actions +=
                                      `<a href="javascript: void(0);" id="allow_edit" name="edit-` +
                                      row.form_id +
                                      row.form_submission_id +
                                      `" class="pull-right btn btn-sm" title="Deny Edit"><i data-edit class="icmn-undo2"></i></a>`;
                                  } else if (
                                    self.previlages.viewFormEntries ||
                                    row.from_id == self.userData.userId
                                  ) {
                                    actions += `<a href="javascript: void(0);" id="cancel-form" data-cancel class="pull-right btn btn-sm" title="Cancel"><i class="icmn-cancel-circle"></i></a>`;
                                  }
                                  /**Add download action for the users other than patient in pending bucket */
                                  actions += `<a href="javascript: void(0);" id="download" data-download class="pull-right btn btn-sm" title="Download"><i data-download class="icmn-download"></i></a>`;
                                }

                                if (
                                  self.isActive != 'archived' &&
                                  self.isActive == 'pending'
                                ) {
                                  if (
                                    row.allow_edit == 1 &&
                                    self.isActive == 'pending' &&
                                    ((this.userData.userId ==
                                      row.recipient_id &&
                                      row.staffFacing != 1) ||
                                      row.form_submission_id)
                                  ) {
                                    actions += `<a href="javascript: void(0);" id="history" class="pull-right btn btn-sm"  data-form-edit="${row.form_id}" data-form-submission-edit="${row.form_submission_id}" data-sent-id-edit="${row.sent_id}" data-recipient-id-edit="${row.recipient_id}" data-sent-date-edit="${row.senton}"  data-sent-date-pending-edit="${row.sentDate}" data-submit-date-edit="${row.updatedtimestamp}" title="History"><i data-view class="icmn-history"></i></a>`;
                                  } else {
                                    actions += `<a href="javascript: void(0);" id="history" class="pull-right btn btn-sm" data-form-edit="${row.form_id}" data-form-submission-edit="${row.form_submission_id}" data-sent-id-edit="${row.sent_id}" data-recipient-id-edit="${row.recipient_id}" data-sent-date-edit="${row.senton}"  data-sent-date-pending-edit="${row.sentDate}" data-submit-date-edit="${row.updatedtimestamp}"  title="History"><i data-view class="icmn-history"></i></a>`;
                                  }
                                }

                                if (
                                  row.created_date &&
                                  self.isActive != 'archived'
                                ) {
                                  //temporary comment for release
                                  if (row.allow_edit == 1) {
                                    actions +=
                                      `<a href="javascript: void(0);" id="allow_edit" name="edit-` +
                                      row.form_id +
                                      row.form_submission_id +
                                      `" class="pull-right btn btn-sm" title="Deny Edit"><i data-edit class="icmn-undo2"></i></a>`;
                                  } else {
                                    actions +=
                                      `<a href="javascript: void(0);" id="allow_edit" name="edit-` +
                                      row.form_id +
                                      row.form_submission_id +
                                      `" class="pull-right btn btn-sm" title="Allow Edit"><i data-edit class="icmn-pencil2"></i></a>`;
                                  }
                                }

                                if (
                                  self.isActive == 'completed' &&
                                  row.integrationenable
                                ) {
                                  let tooltip = 'Send to EHR';
                                  if (
                                    this.userDataConfig
                                      .label_for_file_generation
                                  ) {
                                    tooltip =
                                      this.userDataConfig
                                        .label_for_file_generation;
                                  }
                                  actions += `<a href="javascript: void(0);" id="move_fc" class="pull-right btn btn-sm" title="${tooltip}"><i data-edit class="icmn-folder-upload"></i></a>`;
                                }
                                if ((self.isActive === Status.Archived || self.isActive === Status.Completed) && row.sendCompletedForm) {
                                   actions += `<a href="javascript: void(0);" id="resend-completed-forms" class="pull-right btn btn-sm" title="`+this._ToolTipService.getTranslateData('TOOLTIPS.RESEND_COMPLETED_FORM')+`"><i data-edit class="fa fa-refresh"></i></a>`;
                                }
                              }
                              //show resend receipients action button based on the form type settings
                              if ((self.isActive === Status.Archived || self.isActive === Status.Completed) && !isBlank(self.previlages.cancelCompletedArchivedForms) && self.previlages.cancelCompletedArchivedForms) {
                                   actions += `<a href="javascript: void(0);" id="cancel-form" data-cancel class="pull-right btn btn-sm" title="`+this._ToolTipService.getTranslateData('BUTTONS.CANCEL')+`"><i class="icmn-cancel-circle"></i></a>`;
                              }
                              if (row.allow_edit !== 1 && self._structureService.privileges.completePendingForms && self.isActive === 'pending' && row.facing_new === 0) {
                                   actions += `<a href="javascript: void(0);" id="mark-complete" class="pull-right btn btn-sm" data-form-edit="${row.form_id}" data-form-submission-edit="${row.form_submission_id}" data-sent-id-edit="${row.sent_id}" data-recipient-id-edit="${row.recipient_id}" data-sent-date-edit="${row.senton}"  data-sent-date-pending-edit="${row.sentDate}" data-submit-date-edit="${row.updatedtimestamp}"  title="${this._ToolTipService.getTranslateData('TOOLTIPS.MARK_AS_COMPLETED')}"><i data-view id="mark-complete" class="icmn-clipboard"></i></a>`;
                              }
                            }
                            actions += `</div>`;
                            return actions;
                          },
                          width: '10%',
                          targets: 25,
                        },
                      ]
                    });
                         $('#form-list-dt tbody').on('click', 'tr .child', function (e) {
                            self._sharedService.goToInnerPage = true;
                              var parentRow = $(this).closest("tr").prev()[0];
                              self.activeStrucuredForms = self.dTable.row( parentRow ).data();
                         }); 
                    $('#form-list-dt tbody').on('click', '#history', (e) => {
                         e.preventDefault();
                         this.showHistoryModal = true;
                         this.showHistory = true;
                    });
                    this.socketEventSubscriptions.push(
                         this._structureService.subscribeSocketEvent('reviewStatusChange').subscribe((data) => {
                              if (data && data.UserId != this.userData.userId) {
                                   if (data.NewStatus == 1 && $("#" + data.form_id + "_" + data.submissionId).prop("checked") == false) {
                                        $("#" + data.form_id + "_" + data.submissionId).prop('checked', true);
                                   } else if (data.NewStatus != 1 && $("#" + data.form_id + "_" + data.submissionId).prop("checked") == true) {
                                        $("#" + data.form_id + "_" + data.submissionId).prop('checked', false);
                                   }
                              }
                         })
                    );

                    $('#form-list-dt tbody').on('click', '.reminder-details', (e) => {
                         e.preventDefault();
                         this.showHistoryModal = true;
                         this.showDraftHistory = true;

                    });
                    $('#form-list-dt tbody').off('click', 'div.btn-group > .edit-pform');
                    $('#form-list-dt tbody').on('click', 'div.btn-group > .edit-pform', (event) => {
                         this.activeStrucuredForms.fromName = this.activeStrucuredForms.fname + ' ' + this.activeStrucuredForms.lname;
                         this.activeStrucuredForms.fromName = this.activeStrucuredForms.fname + ' ' + this.activeStrucuredForms.lname;
                         this.activeStrucuredForms.formId = this.activeStrucuredForms.form_id;
                         this.activeStrucuredForms.fromId = this.activeStrucuredForms.from_id;
                         this.activeStrucuredForms.sentId = this.activeStrucuredForms.sent_id;
                         this.activeStrucuredForms.formName = this.activeStrucuredForms.form_name;
                         this.activeStrucuredForms.categoryvalue = 1;
                         this.activeStrucuredForms.toName = this.activeStrucuredForms.createdUser;
                         this.activeStrucuredForms.createdUserName = this.activeStrucuredForms.createdUser;
                         this.activeStrucuredForms.fromUsername = this.activeStrucuredForms.createdUserEmail;
                         if (this.activeStrucuredForms.form_type == "Staff Facing") {
                              this.activeStrucuredForms.staff_facing = 1;
                         } else {
                              this.activeStrucuredForms.staff_facing = 0;
                         }
                         this.activeStrucuredForms.entryId = this.activeStrucuredForms.form_submission_id;

                         var formId = this.activeStrucuredForms.form_id ? this.activeStrucuredForms.form_id : this.activeStrucuredForms.formId;
                         this._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)), 1);
                         localStorage.setItem('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)));
                         if (this.activeStrucuredForms.allow_edit && this.activeStrucuredForms.allow_edit == 1) {
                              this.router.navigate(['forms/edit-form/' + formId + '/' + this.activeStrucuredForms.form_submission_id]);
                         } else if (this.activeStrucuredForms.formStatus == "Draft") {
                              this.router.navigate(['forms/edit-form/' + formId + '/' + this.activeStrucuredForms.form_submission_id]);
                         } else {
                              this.viewForm(this.activeStrucuredForms);

                         }
                    });
                    $('#form-list-dt tbody').off('click', 'div.btn-group > .view-form');
                    $('#form-list-dt tbody').on('click', 'div.btn-group > .view-form', function() {
                         if (this.activeStrucuredForms && ((this.activeStrucuredForms.allow_edit && this.activeStrucuredForms.allow_edit == 1) && this.isActive == "pending" && ((this.userData.userId == this.activeStrucuredForms.recipient_id && this.activeStrucuredForms.staffFacing != 1) || (this.userData.userId == this.activeStrucuredForms.from_id && this.activeStrucuredForms.staffFacing == 1)))) {
                              this.activeStrucuredForms.fromName = this.activeStrucuredForms.fname + ' ' + this.activeStrucuredForms.lname;
                              this.activeStrucuredForms.formId = this.activeStrucuredForms.form_id;
                              this.activeStrucuredForms.fromId = this.activeStrucuredForms.from_id;
                              this.activeStrucuredForms.sentId = this.activeStrucuredForms.sent_id;
                              this.activeStrucuredForms.formName = this.activeStrucuredForms.form_name;
                              this.activeStrucuredForms.categoryvalue = 1;
                              this.activeStrucuredForms.toName = this.activeStrucuredForms.createdUser;
                              this.activeStrucuredForms.createdUserName = this.activeStrucuredForms.createdUser;
                              this.activeStrucuredForms.fromUsername = this.activeStrucuredForms.createdUserEmail;
                              if (this.activeStrucuredForms.form_type == "Staff Facing") {
                                   this.activeStrucuredForms.staff_facing = 1;
                              } else {
                                   this.activeStrucuredForms.staff_facing = 0;
                              }
                              this.activeStrucuredForms.entryId = this.activeStrucuredForms.form_submission_id;
                              var formId = this.activeStrucuredForms.form_id ? this.activeStrucuredForms.form_id : this.activeStrucuredForms.formId;
                              this._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)), 1);
                              localStorage.setItem('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)));
                              this.viewForm(this.activeStrucuredForms);
                         } else {
                              var tr = $(this).closest('tr');
                              var row = x.row(tr);
                              if (row.data()) {
                                   if (row.child.isShown()) {
                                        //This row is already open - close it
                                        row.child.hide();
                                        tr.removeClass('shown');
                                   } else {
                                        //Open this row
                                        row.child(format(row.data())).show();
                                        tr.addClass('shown');
                                   }
                              } else {
                                   var tr = $('[id="row-' + self.activeStrucuredForms.id + '"]');
                                   var row = x.row(tr);
                                   if (row.child.isShown()) {
                                        //This row is already open - close it
                                        row.child.hide();
                                        tr.removeClass('shown');
                                        row.child(format(row.data())).show();
                                        tr.addClass('shown');
                                   } else {
                                        //Open this row
                                        row.child(format(row.data())).show();
                                        tr.addClass('shown');
                                   }
                              }
                         }
                    });
               });
          }
     }
     getSearchKey(isActive: string): string {
          switch (isActive) {
              case 'completed':
                  return Store.SEARCH_ALL_FORM_WORK_LIST_COMPLETED;
              case 'pending':
                  return Store.SEARCH_ALL_FORM_WORK_LIST_PENDING;
              case 'archived':
                  return Store.SEARCH_ALL_FORM_WORK_LIST_ARCHIVED;
              case 'draft':
                  return Store.SEARCH_ALL_FORM_WORK_LIST_DRAFTS;
              default:
                  return Store.SEARCH_ALL_FORM_WORK_LIST_COMPLETED;
          }
     }

     viewForm(message) {
          message.page = "formWorlists";
          this._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(message)), 1);
          localStorage.setItem('formData', encodeURIComponent(JSON.stringify(message)));
          if (message.form_submission_id && message.form_submission_id != 0) {
               this.router.navigate(['/forms/list/view']);
          } else {
               this.router.navigate(['/forms/view-form']);
          }
     }

     removeFormData(id, index) {
          swal({
               title: "Are you sure want to delete?",
               text: "",
               type: "warning",
               showCancelButton: true,
               cancelButtonClass: "btn-default",
               confirmButtonClass: "btn-warning",
               confirmButtonText: "Ok",
               closeOnConfirm: true
          }, () => {
               this._formsService.deleteTaggedForms(id, 1).then((result) => {
                    if (result) {
                         this.strucuredForms = this.strucuredForms.filter(function(data) {
                              return data.id != id;
                         });
                    }
               });
          });

          var yx = document.getElementsByClassName("btn-warning");

          yx[0].setAttribute("id", "delete_cnfrm_btn");


     }

     viewStrucuturedForm() {
          this._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)), 1);
          localStorage.setItem('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)));
          this.router.navigate(['/forms/list/view']);
     }

     getPdfTaggedForm() {

          var patientIdForPdf = this.activeStrucuredForms.patient_id;
          if(typeof this.activeStrucuredForms.associated_user_id !== 'undefined' && this.activeStrucuredForms.associated_user_id !='')
          {
               patientIdForPdf = this.activeStrucuredForms.associated_user_id;
          }
          if(this.activeStrucuredForms.facing_new ==0 && this.activeStrucuredForms.patient_id ==0)
          {
               patientIdForPdf = this.activeStrucuredForms.from_id;
          }
          var newWindow: any = window.open(this._structureService.serverBaseUrl + "/webapp/www/img/gif-loader.gif");
          var downloadTime = moment((moment().unix()) * 1000).format('MMMM DD, YYYY h:mm a');
          if (this.activeStrucuredForms.updatedtimestamp == null) {
               var data = {
                    "patient_id": this.activeStrucuredForms.patient_id,
                    "patient_user": patientIdForPdf,
                    "generate_send_id": this.activeStrucuredForms.sent_id,
                    "userid": this.activeStrucuredForms.userid,
                    "formId": this.activeStrucuredForms.form_id,
                    "submissionId": this.activeStrucuredForms.form_submission_id,
                    "tenantId": this.userData.tenantId,
                    "tenantName": this.userData.tenantName,
                    "createdOnNew": this.activeStrucuredForms.senton,
                    "createdOn": moment(this.activeStrucuredForms.senton * 1000).format('MMMM DD, YYYY h:mm a'),
                    "sendOn": moment(this.activeStrucuredForms.sentDate * 1000).format('MMMM DD, YYYY h:mm a'),
                    "downloadTime": downloadTime
               };
          } else {
               var data = {
                    "patient_id": this.activeStrucuredForms.patient_id,
                    "patient_user": patientIdForPdf,
                    "generate_send_id": this.activeStrucuredForms.sent_id,
                    "userid": this.activeStrucuredForms.userid,
                    "formId": this.activeStrucuredForms.form_id,
                    "submissionId": this.activeStrucuredForms.form_submission_id,
                    "tenantId": this.userData.tenantId,
                    "tenantName": this.userData.tenantName,
                    "createdOnNew": this.activeStrucuredForms.senton,
                    "createdOn": moment(this.activeStrucuredForms.senton * 1000).format('MMMM DD, YYYY h:mm a'),
                    "sendOn": moment(this.activeStrucuredForms.updatedtimestamp * 1000).format('MMMM DD, YYYY h:mm a'),
                    "downloadTime": downloadTime
               };
          }
          this._formsService.generateTaggedFormReportPdf(data, 2, timezone.name()).then((result) => {
               var fileName: any = '';
               fileName = result;              
               var fileUrl  = this._structureService.apiBaseUrl +'/citus-health/v4/form-download.php?filetoken='+fileName._body;
               newWindow.location = fileUrl;
          });
     }

     download(fileUrl, fileName) {
          window.open(fileUrl, "_blank");
     }
     getSiteIds(data: any): void {
          this.siteIds = data.siteId;
          let type = '';
          if (this.userDataConfig.enable_collaborate_edit == 1 && this.userDataN.group != CONSTANTS.userGroupIds.patient) {
               type = (this._sharedService.formsType && this._sharedService.formsType != "") ? this._sharedService.formsType : 'DRAFTS';
          } else {
               type = (this._sharedService.formsType && this._sharedService.formsType != "") ? this._sharedService.formsType : 'COMPLETED';
               this._sharedService.formsType = type;
          }
          if (this.previlages.viewFormEntries || this.previlages.manageTenants) {
               type = (this._sharedService.formsType && this._sharedService.formsType != "") ? this._sharedService.formsType : 'COMPLETED';
               this._sharedService.formsType = type;
          }
          if (this._structureService.allowEditFormSuccess) {
               type = "COMPLETED";
          }
          if (this._sharedService.viewFormBackActiveTab != '') {
               type = this._sharedService.viewFormBackActiveTab;
               this._sharedService.viewFormBackActiveTab == '';
          }
          this.changeFormData(type);
          this.displayList();
     }
    hideDropdown(hideItem: any) {
        this.hideSiteSelection = hideItem.hideItem;
    }
    displayList(): void {
        const self = this;
        this._formsService.getAllTaggedFormsLazyCount(true, this.isScheduled, this.includeOtherForms, this.siteIds).then((data) => {
            this.completedCount = data['completedCount'];
            this.archiveCount = data['archivedCount'];
            this.pendingCount = data['pendingCount'];
            this.draftCount = data['draftCount'];
        }).catch(() => {
          if(!this.isApiError){
               this.isApiError = true;
               $.notify({'message':`<strong>${this._ToolTipService.getTranslateData('ERROR_MESSAGES.API_ERROR_WARNING')}</strong>`},
               {
                    allow_dismiss: true, type: "danger", delay: 0,
                    onClose : function(){ self.isApiError = false; }
               });
          }
      });
    } 
    
    setWebHookTime(element) :any{
     return ((this.formpPipe.transform(parseInt(element.created_on) * 1000).length >= 9) ? " on " : " at ") +
     this.formpPipe.transform(parseInt(element.created_on) * 1000);
    }
  applyAdvanceSearch(searchValues) {
    /** Get advance search input variable form advance search component */
    this.advanceSearchInput = searchValues;
    this.selectedAdvancedFilterItem = [];
    if (Object.keys(this.advanceSearchInput).length > 0) {
      if (!this.isAdvancedSearchView) {
        this.populateData(this.isActive, this.isScheduled, 'refresh');
      } else if (searchValues.formWorklistColumns) {
        const updatedColumns = searchValues.formWorklistColumns.split(',').reduce((acc: string[], id: string) => {
          const trimmedId = id.trim();
          if (trimmedId !== '') {
            acc.push(trimmedId);
          }
          return acc;
        }, []);
        this.updateDataTableColumnVisibility(updatedColumns);
      } else {
        this.populateData(this.isActive, this.isScheduled, 'refresh');
      }
    } else if (this.isAdvancedSearchView) {
      const keysArray: number[] = this.defaultColumns.map((item) => item.key);
      this.updateDataTableColumnVisibility(keysArray);
    }
  }
     setAdvanceSearchForm(refresh) {
          if(refresh != 'polling' && refresh != '') {
               this.advanceSearchInput = {};
               this.resetAdvanceSearchForm = false;
               this.dynamicControls = this._structureService.setAdvanceSearchControls('allform',this.isActive);
          }
     }
     closeHistoryModal(e) {
          if (e == true) {
               this.showHistoryModal = false;
               this.showHistory = false;
          }
     }
     closeDraftHistoryModal(e) {
          if (e == true) {
               this.showHistoryModal = false;
               this.showDraftHistory = false;
          }
     }

    /**
     * Function to close and open Choose Recipient(s) modal
     */
     popUpRecipientsList() {
          this.resendPatientId = this._formsService.getPatientIdForResendForms(this.activeStrucuredForms);
          this.showResendModal = !this.showResendModal;
     }

    /**
     * Function to resend appless link to selected recipients
     * @param recipients Recipients list choose from the model as array of objects
     */
     resendFormToRecipients(recipients) {
          this._formsService.resendFormToRecipients(recipients, this.activeStrucuredForms);
     }

    /**
     * Function to reload the data table when date range is selected
     */
     onSelectDateRange(event) {
          if (event.type) {
               this.dateRangeType = event.type;
          }
         if (!isBlank(event.changeDetectedOnInit)) {
          } else {
               this.populateData(this.isActive, this.isScheduled, "refresh");
               this.displayList();
          }
     }
  /**
   * Displays the advanced filter view by setting the `showAdvanceFilterView` property to true.
   */
  populateAdvancedSearchView() {
    this.advanceSearchInput = {};
    this.advancedFilterOptions = [];
    this.advancedFilterSearchKeys = '';
    this.advancedFilterOptionsSettings = {};
    this.selectedAdvancedFilterItem = [];
    this.searchFilterItem = [];
    this.advancedFilterNew = [];
    this.searchFilterName = '';
    this.resetAdvancedFilter = false;
    this.isAdvancedSearchView = true;
    if (this.toggleAdvancedFilter) {
      this.toggleAdvancedFilter = false;
    }
    this._structureService.hideAdvanceSearchArea = true;
    this.dynamicControlsAdvancedView = this._structureService.setAdvanceSearchControls('allform', this.isActive, {});
    const statusOptions = [
      { key: 'completed', value: this.toolTipService.getTranslateData('OPTIONS.FORMS_STATUS.COMPLETED') },
      { key: 'pending', value: this.toolTipService.getTranslateData('OPTIONS.FORMS_STATUS.PENDING') },
      { key: 'archived', value: this.toolTipService.getTranslateData('OPTIONS.FORMS_STATUS.ARCHIVED') },
      { key: 'canceled', value: this.toolTipService.getTranslateData('OPTIONS.FORMS_STATUS.CANCELLED') }
    ];

    if (+this.userDataConfig.enable_collaborate_edit === 1) {
      statusOptions.push({ key: 'draft', value: this.toolTipService.getTranslateData('OPTIONS.FORMS_STATUS.DRAFT') });
    }

    const miscellaneousOptions = [
      { key: 'review_complete_yes', value: this.toolTipService.getTranslateData('OPTIONS.FORMS_MISCELLANEOUS.REVIEW_COMPLETE.YES') },
      { key: 'review_complete_no', value: this.toolTipService.getTranslateData('OPTIONS.FORMS_MISCELLANEOUS.REVIEW_COMPLETE.NO') },
      { key: 'workflow_patient_facing', value: this.toolTipService.getTranslateData('OPTIONS.FORMS_MISCELLANEOUS.WORKFLOW.PATIENT') },
      { key: 'workflow_staff_facing', value: this.toolTipService.getTranslateData('OPTIONS.FORMS_MISCELLANEOUS.WORKFLOW.STAFF') },
      {
        key: 'workflow_StaffPractitioner_facing',
        value: this.toolTipService.getTranslateData('OPTIONS.FORMS_MISCELLANEOUS.WORKFLOW.STAFF_PRACTITIONER')
      },
      { key: 'sent_via_inapp', value: this.toolTipService.getTranslateData('OPTIONS.FORMS_MISCELLANEOUS.SENT_VIA.IN_APP') },
      { key: 'sent_via_apples', value: this.toolTipService.getTranslateData('OPTIONS.FORMS_MISCELLANEOUS.SENT_VIA.APP_LESS') },
      { key: 'scheduled_yes', value: this.toolTipService.getTranslateData('OPTIONS.FORMS_MISCELLANEOUS.SCHEDULED.YES') },
      { key: 'scheduled_no', value: this.toolTipService.getTranslateData('OPTIONS.FORMS_MISCELLANEOUS.SCHEDULED.NO') }
    ];

    this.dynamicControlsAdvancedView.push(
      new ControlType({
        key: 'formStatus',
        label: this.toolTipService.getTranslateData('LABELS.FORM_STATUS'),
        options: statusOptions,
        controlType: 'dropdown',
        order: 5
      })
    );
    this.dynamicControlsAdvancedView.push(
      new ControlType({
          key: 'miscellaneous',
          label: this.toolTipService.getTranslateData('LABELS.MISCELLANEOUS'),
          options: miscellaneousOptions,
          controlType: 'dropdown',
          order: 6
      })
    );
    this.dynamicControlsAdvancedView.push(this.customColumnControl);
    this.dynamicControlsAdvancedView.push(
      new ControlType({
        key: 'isDefault',
        label: '',
        options: [],
        controlType: 'optional',
        order: null
      })
    );
    this.getAdvancedSearchDefaultFilters();
    // TODO Remove this line after verifying the functionality works as expected.
    //  this.populateData(this.isActive, this.isScheduled, 'refresh');
  }
  /**
   * To hide the advanced search view and display the normal view
   */
  hideAdvanceSearchView() {
    this.advanceSearchInput = {};
    this.structureService.hideAdvanceSearchArea = true;
    this.isAdvancedSearchView = false;
    this.advancedFilterComponent.resetSearch();
    this.populateData(this.isActive, this.isScheduled, 'refresh');
  }
  // TODO: Remove this function once replaced all the occurence of _ToolTipService with toolTipService */
  // eslint-disable-next-line no-underscore-dangle
  get _ToolTipService() {
    return this.toolTipService;
  }
  // TODO: Remove this function once replaced all the occurence of _structureService with structureService */
  // eslint-disable-next-line no-underscore-dangle
  get _structureService() {
    return this.structureService;
  }
  /* Get the saved filters for the advanced search */
  getAdvancedSearchDefaultFilters() {
    this.advancedFilterOptions = [];
    const requestBody = {
      rowsPerPage: advancedFilterCount,
      currentPage: 0
    };
    this.httpService.doPost(`${APIs.getSearchCriteriaEndpoint}?module=${ModuleName.FORM_CENTER}`, requestBody).subscribe((response) => {
      if (response && !isBlank(response.content) && response.content.length) {
        const filters = response.content;
        this.advancedFilterOptions = filters.map((item) => ({
          ...item,
          itemName: item.searchFilterName
        }));
        const defaultFilter = this.advancedFilterOptions.find((item) => item.isDefault);
        if (defaultFilter) {
          this.handleDefaultFilterData();
          this.selectedAdvancedFilterItem = [defaultFilter];
          [this.searchFilterItem] = this.selectedAdvancedFilterItem;
          this.searchFilterName = this.searchFilterItem.itemName;
          const selectedColumnIds = JSON.parse(defaultFilter.columns).selectedColumns;
          this.setFilteredColumns(selectedColumnIds);
          this.prepopulateFilterOptionValuesInAdvFilter();
        }
        this.addDeletebuttonforFilter();
      }
    });
    this.initializeDropdownSettings();
  }
  /** Initialize dropdown settings */
  initializeDropdownSettings() {
    this.advancedFilterOptionsSettings = {
      singleSelection: true,
      text: this.toolTipService.getTranslateData('PLACEHOLDERS.CHOOSE_FILTER'),
      enableSearchFilter: true,
      classes: 'myclass custom-class',
      enableCheckAll: false,
      showCheckbox: true
    };
  }
  /* To get the selected filter from dropdown */
  setSelectedAdvancedFilterItem(item: any) {
    const selectedColumnIds = JSON.parse(item.columns).selectedColumns;
    this.setFilteredColumns(selectedColumnIds);
    this.selectedAdvancedFilterItem = [item];
    this.searchFilterName = item.searchFilterName;
    [this.searchFilterItem] = this.selectedAdvancedFilterItem;
    this.prepopulateFilterOptionValuesInAdvFilter();
  }
  /* To Remove the selected filter from dropdown */
  removeAdvancedFilterItem() {
    this.selectedAdvancedFilterItem = [];
  }

  /** Prepopulate saved values in the fitler options */
  prepopulateFilterOptionValuesInAdvFilter() {
    if (this.selectedAdvancedFilterItem.length === 0) return;
    const temp: any = this.dynamicControlsAdvancedView.map((control) => {
      let controlValue;
      if (control.key === 'formWorklistColumns') {
        controlValue = this.selectedColumns;
      } else {
        controlValue = control.controlType === 'dropdown' ? [] : '';
      }
      return new ControlType({
        ...control,
        value: controlValue
      });
    });
    this.selectedAdvancedFilterItem.forEach((filter) => {
      const parsedFilter = JSON.parse(filter.searchFilterCriteria);
      if (parsedFilter && parsedFilter.filters) {
        const updatedTemp = temp.map((control) => {
          if (Object.prototype.hasOwnProperty.call(parsedFilter.filters, control.key)) {
            let updatedValue;
            if (control.key !== 'formWorklistColumns') {
              if (control.controlType === 'dropdown') {
                updatedValue = (parsedFilter.filters[control.key] as string).split(',');
              } else {
                updatedValue = parsedFilter.filters[control.key];
              }
            } else {
              updatedValue = control.value;
            }
            return {
              ...control,
              value: updatedValue
            };
          }
          return control;
        });
        setTimeout(() => {
          this.dynamicControlsAdvancedView = updatedTemp;
        }, 50);
      }
    });
  }

  /* To save the filter */
  saveFilter() {
    this.advancedFilterComponent.getSelectedKeyValues();
    if (
      !this.searchService.validateAdvancedFilterOptions(
        this.advanceSearchInput,
        this.advancedFilterOptions,
        this.searchFilterItem,
        this.searchFilterName,
        true
      )
    ) {
      return;
    }
    const columnsArray = this.advanceSearchInput.formWorklistColumns.split(',').map(Number);
    if ('formWorklistColumns' in this.advanceSearchInput) {
      delete this.advanceSearchInput.formWorklistColumns;
    }
    const activityData: any = { activityType: 'forms worklist' };
    const saveSearch: SaveSearch = {
      moduleName: ModuleName.FORM_CENTER,
      searchFilterName: this.searchFilterName,
      searchFilterCriteria: JSON.stringify({ filters: this.advanceSearchInput }),
      isDefault: this.advanceSearchInput.isDefault || false,
      columns: JSON.stringify({ selectedColumns: columnsArray })
    };
    this.httpService.doPost(APIs.searchCriteriaEndpoint, saveSearch).subscribe(
      (response) => {
        if (response && response.id) {
          this.structureService.notifyMessage({
            message: this.toolTipService.getTranslateData('SUCCESS_MESSAGES.ADV_FILTER_SAVE'),
            type: CONSTANTS.notificationTypes.success
          });
          this.searchFilterItem = response;
          this.advancedFilterOptions.push({ ...response, itemName: response.searchFilterName || '' });
          this.handleDefaultFilterData(response.id);
          activityData.activityName = 'Filter saved successfully';
          activityData.activityDescription = `Filter saved by ${this.userData.displayName}`;
          this.structureService.trackActivity(activityData);
        } else {
          this.structureService.notifyMessage({
            message: this.toolTipService.getTranslateData('ERROR_MESSAGES.ADV_FILTER_SAVE'),
            type: CONSTANTS.notificationTypes.warning
          });
          activityData.activityName = 'Filter save failed';
          activityData.activityDescription = `Filter save failed for the user ${this.userData.displayName}`;
          this.structureService.trackActivity(activityData);
        }
      },
      () => {
        activityData.activityName = 'Filter save failed';
        activityData.activityDescription = `Filter save failed for the user ${this.userData.displayName}`;
        this.structureService.trackActivity(activityData);
      }
    );
  }
  /** To set the filters as new, show the save new button and hide update button */
  setToNewFilter(): void {
    this.searchFilterItem = {};
    this.searchFilterName = '';
    this.resetAdvancedFilter = true;
    this.advancedFilterComponent.resetSearch();
  }

  /** To update the filter */
  updateFilter(): void {
    this.advancedFilterComponent.getSelectedKeyValues();

    if (
      !this.searchService.validateAdvancedFilterOptions(
        this.advanceSearchInput,
        this.advancedFilterOptions,
        this.searchFilterItem,
        this.searchFilterName,
        false
      )
    ) {
      return;
    }
    this.structureService
      .showAlertMessagePopup({ text: this.toolTipService.getTranslateData('MESSAGES.ADV_FILTER_UPDATE_CONFIRM') })
      .then((confirm) => {
        if (confirm) {
          this.confirmedToUpdateAdvanceFilter();
        }
      });
  }
  /** Filter update is happening here once user confirmed */
  confirmedToUpdateAdvanceFilter(): void {
    const columnsArray = this.advanceSearchInput.formWorklistColumns.split(',').map(Number);
    const activityData: any = { activityType: 'forms worklist' };
    const updateSearch: UpdateSearch = {
      id: this.searchFilterItem.id,
      moduleName: ModuleName.FORM_CENTER,
      searchFilterName: this.searchFilterName,
      searchFilterCriteria: JSON.stringify({ filters: this.advanceSearchInput }),
      isDefault: this.advanceSearchInput.isDefault || false,
      columns: JSON.stringify({ selectedColumns: columnsArray })
    };
    this.httpService.doPut(APIs.searchCriteriaEndpoint, updateSearch).subscribe((response) => {
        if (response && response.id) {
          this.setFilteredColumns(columnsArray);
          this.advancedFilterOptions = this.advancedFilterOptions.map((item) => (item.id === response.id ? response : item));
          this.handleDefaultFilterData(response.id);
          this.structureService.notifyMessage({
            message: this.toolTipService.getTranslateData('SUCCESS_MESSAGES.ADV_FILTER_UPDATE'),
            type: CONSTANTS.notificationTypes.success
          });
          activityData.activityName = 'Filter updated successfully';
          activityData.activityDescription = `Filter updated by ${this.userData.displayName}`;
        } else {
          this.structureService.notifyMessage({
            message: this.toolTipService.getTranslateData('ERROR_MESSAGES.ADV_FILTER_UPDATE'),
            type: CONSTANTS.notificationTypes.warning
          });
          activityData.activityName = 'Filter update failed';
          activityData.activityDescription = `Filter update failed for the user ${this.userData.displayName}`;
        }
      },
      () => {
        activityData.activityName = 'Filter update failed';
        activityData.activityDescription = `Filter update failed for the user ${this.userData.displayName}`;
      }
    );
    this.structureService.trackActivity(activityData);
  }

  showValidationMessage(messageKey: string): boolean {
    this.structureService.notifyMessage({
      message: this.toolTipService.getTranslateData(messageKey),
      type: CONSTANTS.notificationTypes.warning
    });
    return false;
  }

  /** To get and set filter keys to save and update */
  setAdvancedFilterKeys(keyValues: any) {
    this.advanceSearchInput = keyValues || {};
  }
  /** To handle the default filter option */
  handleDefaultFilterData(id?: number) {
    const defaultLabel = `(${this.toolTipService.getTranslateData('LABELS.DEFAULT')})`;
    this.advancedFilterOptions = this.advancedFilterOptions.map((item) => {
      const isDefault = !isBlank(id) ? item.id === id : item.isDefault;
      return { ...item, itemName: `${item.searchFilterName} ${isDefault ? defaultLabel : ''}`, isDefault };
    });
  }

  get showSaveNewFilter() {
    return !(this.searchFilterItem && this.searchFilterItem.id) && this.advancedFilterOptions.length <= advancedFilterCount;
  }
  /* To clear and remove the filter from the input */
  clearFilter() {
    this.selectedAdvancedFilterItem = [];
    this.searchFilterItem = null;
    this.searchFilterName = '';
    this.advancedFilterComponent.resetSearch();
    setTimeout(() => {
      this.selectedAdvancedFilterItem = [...this.selectedAdvancedFilterItem];
    });
  }
  updateDataTableColumnVisibility(updatedColumns?) {
    const updatedColumnIds = updatedColumns.map(Number);
    if ($.fn.DataTable.isDataTable('#form-list-dt') && this.isAdvancedSearchView) {
      const firstColumnId = this.columnConfig[0].columnId;
      const lastColumnId = this.columnConfig[this.columnConfig.length - 1].columnId;
      this.columnConfig = this.columnConfig.map((col, index) => {
        if (col.columnId === firstColumnId || col.columnId === lastColumnId) {
          return {
            ...col,
            isSelected: true
          };
        }
        return {
          ...col,
          isSelected: updatedColumnIds.includes(Number(col.columnId))
        };
      });
      const dataTableInstance = $('#form-list-dt').DataTable();
      this.columnConfig.forEach((col) => {
        const columnIndex = col.columnId;
        dataTableInstance.column(columnIndex).visible(col.isSelected);
      });
      dataTableInstance.draw();
    }
  }
  addDeletebuttonforFilter() {
    setTimeout(() => {
      const items = document.querySelectorAll('.search-filter .list-area ul.lazyContainer li');
      Array.from(items).forEach((el, index) => {
        if (!el.classList.contains('multi-select-item')) {
          this.renderer2.addClass(el, 'multi-select-item');
          const label = el.querySelector('label');
          if (label) {
            this.renderer2.setStyle(label, 'width', '100%');
            const icon = this.renderer2.createElement('i');
            this.renderer2.addClass(icon, 'fa');
            this.renderer2.addClass(icon, 'fa-trash');
            this.renderer2.setStyle(icon, 'marginLeft', '10px');
            this.renderer2.setStyle(icon, 'cursor', 'pointer');
            this.renderer2.setStyle(icon, 'paddingTop', '4px');
            this.renderer2.addClass(icon, 'pull-right');
            this.renderer2.listen(icon, 'click', (event) => {
              event.stopPropagation();
              this.deleteFilter(index);
            });
            this.renderer2.appendChild(label, icon);
          }
        }
      });
    }, 500);
  }
  setFilteredColumns(selectedColumnIds) {
    this.columnConfig = this.columnConfig.map((column) => ({
      ...column,
      isSelected: selectedColumnIds.includes(column.columnId)
    }));
    this.selectedColumns = this.columnConfig.filter((col) => col.isSelected).map((col) => ({ key: col.columnId, value: col.title }));
    this.updateDataTableColumnVisibility(selectedColumnIds);
  }
  deleteFilter(index: number) {
    const filterToDelete = this.advancedFilterOptions[index];
    this.searchService.deleteAdvanceSearchFilterCriteria(filterToDelete, ModuleName.FORM_CENTER).subscribe((res) => {
      if (res.status) {
        this.advancedFilterOptions = this.advancedFilterOptions.filter((item) => item.id !== filterToDelete.id);
        if (this.selectedAdvancedFilterItem && this.selectedAdvancedFilterItem.length > 0) {
          if (this.selectedAdvancedFilterItem[0].id === filterToDelete.id) {
            this.selectedAdvancedFilterItem = [];
            this.searchFilterItem = null;
            this.searchFilterName = '';
            this.advancedFilterComponent.resetSearch();
          }
        }
      }
    });
  }
  /**
   * Function to process the form list data
   * @param data - The data to be processed
   * @returns - The processed data
   */
  processFormListData(param: any): any {
    const dataList = param;
    if (!this.isAdvancedSearchView) {
      return dataList;
    }
    const processedData = {
      response: dataList.data,
      totalCt: +dataList.recordCount,
    };
    const response = processedData.response.map((item) => ({
      applessMode: item.appless_mode,
      authorization: item.authorization,
      patientIdentityValue: item.patient_mrn,
      form_submission_order_id: item.form_submission_order_id,
      discrete_integration_status: item.discrete_integration_status,
      discrete_request_id: item.discrete_request_id,
      integration_status: item.integration_status,
      recipient_id: item.recipient_id,
      associated_user_id: item.associated_user_id,
      newrequest_id: item.request_id,
      request_id: item.request_id,
      mrn: item.patient_mrn,
      patientDob: item.patient_dob,
      isScheduled: item.scheduled_status,
      formStatus: item.form_status,
      scheduledstatus: item.scheduled_status,
      category: item.category,
      AFE_id: item.afe_id,
      patient_associated_id: item.ac_userid,
      caregiver_userid: item.caregiver_userid,
      cgiver_fname: item.ac_firstname,
      cgiver_lname: item.ac_lastname,
      updated_date_new: item.modified_date_formatted,
      caregiver_displayname: item.ac_displayname,
      sent_id: item.record_id,
      is_deleted: item.is_deleted,
      tag_id: item.tag_id,
      sentDate: item.sent_date,
      created_date_new: item.created_date,
      allow_edit: item.allow_edit,
      facing_new: item.staff_facing === '0' ? 0 : 1, // yet to provide the key
      form_submission_id: item.submission_id,
      form_tags: item.tag_namem,
      form_id: item.form_id,
      formName: item.form_name,
      form_name: item.form_name,
      form_type: item.staff_facing === '0' ? 'Patient facing' : 'Staff Facing', // yet to provide the key
      newtenantid: item.tenant_id,
      tenant_id: item.tenant_id,
      newpatientid: item.patient_id,
      patient_id: item.patient_id,
      from_id: item.recipient_id,
      patientName: item.patient_name,
      fname: item.patient_firstname,
      lname: item.patient_lastname,
      mobile: item.patient_mobile,
      countryCode: item.patient_country_code,
      username: item.patient_email,
      userid: item.filled_by,
      createdUser: item.filled_by_displayname,
      createdUserEmail: item.filled_by_email,
      sentDate_date_formatted: item.created_date_formatted,
      created_date_date_formatted: item.created_date_formatted,
      created_date_new_date_formatted: item.created_date_formatted,
      updated_date_new_date_formatted: item.modified_date_formatted,
      id: item.record_id,
      integration_type: item.integration_type,
      updated_date: item.modified_date,
      updated_date_converted: item.modified_date_formatted,
      senton: item.sent_date,
      updatedtimestamp: item.modified_date,
      createdtimestamp: item.created_date,
      siteName: item.site_name,
      drug_id: item.drug_id,
      esi_id: item.esi_id,
      rms_id: item.rms_id
    }));
    processedData.response = response;
    return processedData;
  }
}
