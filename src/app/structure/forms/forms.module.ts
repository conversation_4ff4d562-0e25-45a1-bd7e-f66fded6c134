import { NgModule, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { FormsComponent } from './forms.citusHealth';
import { FormsListingComponent } from './forms-listing.component';
import { FormsListingSupplyComponent } from './forms-listing-supply.component';
import { ViewFormDataComponent } from './view-submitted-form.component';
import { SendFormDataComponent } from './send-form-data.component';
import { SendSupplyFormDataComponent } from './send-supply-form-data.component';
import { viewFormComponent } from './view-form.component';
import { editFormComponent } from './edit-form.component';
import { FormsWorkListingComponent } from './my-form-worklist.component';
import { SharedModule } from '../shared/sharedModule';
import { FormpPipe, SafeHtmlPipe,DateTimePipe } from './formp.pipe';
import { formSearchPipe, formRecipientPipe, formFilterPipe, patientRolePipe, exPatientCaregiverPipe, exMasterRolePipe,caregiverOrAlternatePipe} from './formp.pipe';
import { TagDefinitionsComponent } from './tag-definitions.component';
import { AddTagDefinitionComponent } from './add-tag-definition.component';
import { CopyFormDefinitionComponent } from './copy-form-definition.component';
import { ScheduledFormComponent } from './scheduledForms/scheduledForms.citushealth';
import { AuthGuard } from '../../guard/auth.guard';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';

export const routes: Routes = [
  { path: 'forms/manage', component: FormsComponent, canActivate:[AuthGuard], data: {checkRoutingPrivileges:'formsLibrarymanager', checkRoutingConfig: 'enable_forms'} },
  { path: 'forms/list', component: FormsListingComponent, canActivate: [AuthGuard], data: { checkRoutingPrivileges: 'viewFormEntries', checkConfigException: 'enable_collaborate_edit',checkUserGroupPermission : '3', checkRoutingConfig: 'enable_forms' } },
  { path: 'forms/supply/list', component: FormsListingSupplyComponent },
  { path: 'forms/list/view', component: ViewFormDataComponent },
  {
    path: 'forms/send/list',
    component: SendFormDataComponent,
    canActivate: [AuthGuard],
    data: { checkRoutingPrivileges: 'FillStructuredForms', checkRoutingConfig: 'enable_forms' }
  },
  { path: 'forms/send/supply/list', component: SendSupplyFormDataComponent },
  { path: 'forms/view-form', component: viewFormComponent },
  { path: 'forms/edit-form/:formId/:entryId', component: editFormComponent },
  { path: 'forms/scheduledforms', component: ScheduledFormComponent,canActivate:[AuthGuard],data:{checkRoutingPrivileges:'allowScheduleForms', checkRoutingConfig: 'enable_forms'} },
  { path: 'forms/worklist', component: FormsWorkListingComponent },
  { path: 'forms/form-tags', component: TagDefinitionsComponent, canActivate:[AuthGuard], data: {checkRoutingPrivileges:'formsLibrarymanager,manageTenants', checkRoutingConfig: 'enable_forms'}},
  { path: 'forms/add-form-tag', component: AddTagDefinitionComponent, canActivate:[AuthGuard] , data: {checkRoutingPrivileges:'formsLibrarymanager,manageTenants',checkUserGroupPermission : '3'} },
  { path: 'forms/copy-form', component: CopyFormDefinitionComponent },  
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    SharedModule,
    NgMultiSelectDropDownModule,
    AngularMultiSelectModule
  ],
  declarations: [
    FormsComponent,
    FormsListingComponent,
    FormsListingSupplyComponent,
    ViewFormDataComponent,
    SendFormDataComponent,
    SendSupplyFormDataComponent,
    ScheduledFormComponent,
    CopyFormDefinitionComponent,
    FormpPipe,
    DateTimePipe,
    formSearchPipe,
    SafeHtmlPipe,
    formRecipientPipe,
    formFilterPipe,
    patientRolePipe,
    exPatientCaregiverPipe,
    TagDefinitionsComponent,
    AddTagDefinitionComponent,
    viewFormComponent,
    editFormComponent,
    exMasterRolePipe,
    FormsWorkListingComponent,
    caregiverOrAlternatePipe
  ],
  providers: [ 
    FormpPipe,
    DateTimePipe,
    formSearchPipe,
    SafeHtmlPipe
    
  ],
  exports: [ editFormComponent, SendFormDataComponent ],
  schemas: [NO_ERRORS_SCHEMA]

})

export class FormManageModule { }
