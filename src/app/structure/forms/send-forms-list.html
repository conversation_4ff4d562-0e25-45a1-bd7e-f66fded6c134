<!-- START: tables/datatables -->
<section class="card">

  <div class="card-header row" [hidden]= "loadFormFrom == 'worklistCenter' || isLoadFromPAH">
    <span class="cat__core__title col-md-6">
      <strong>Send Forms </strong>
      <!--<a [routerLink]="['/message/messagegroup']" class="pull-right btn btn-sm btn-primary">Add Message Group<i class="ml-1"></i></a>-->
    </span>

  </div>
  <div class="card-block" [hidden]= "loadFormFrom == 'worklistCenter' || isLoadFromPAH">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
      <li class="breadcrumb-item">Send Forms</li>
    </ol>
  </div>
  <div *ngIf="isPatientDriven==true && loadFormFrom != 'worklistCenter' && !isLoadFromPAH  && !_structureService.isMultiAdmissionsEnabled" [hidden]="isFullWidthCollapsed">
      <input  name="flow" type="radio" value="formsdriven" style="width: 59px;height: 25px;" [ngModel]="selectedFlow" (change)="flowChange('formsdriven')">
      <label  id="formsdriven" style="position: absolute;margin: 1px 9px 3px -7px;" >Form Driven Workflow</label>
      <i class='formsdriven-tooltip icmn-info' id="formsdriven-tooltip" data-toggle='tooltip' data-placement='right' style="position: absolute;
      margin: 3px 0px 0px 135px;" title="{{formdrivenFlowTooltip}}"></i>
      <input  name="flow" type="radio" value="patientdriven" style="width: 59px;height: 25px;margin: 0px 0px 0px 162px;" [ngModel]="selectedFlow" (change)="flowChange('patientdriven')">
      <label  id="patientdriven" style="position: absolute;position: absolute;" >Patient Driven Workflow</label>
      <i class='patientdriven-tooltip icmn-info' id="patientdriven-tooltip" data-toggle='tooltip' data-placement='right' style="position: absolute;
      margin: 3px 0px 0px 155px;" title="{{patientDrivenFlowTooltip}}"></i>
  </div>


<div class="row chatroom-section">
  <!---------------------------------------------------------------Patient Driven Flow Starts------------------------------------------------------->
  <div class="col-lg-4 chatroom-section-first" *ngIf="selectedFlow=='patientdriven'">
      <section class="card">
        
        <div class="card-block">
          <div class="cat__core__card-sidebar">
           
          <div class="row col-lg-12" style="margin-bottom: 15px;">
            <div class="col-lg-12">
            <h5 class="label-position" *ngIf="hideSiteSelection">{{ filterSiteLabel | translate }}</h5>
                   <span  class="col-lg-8" *ngIf="hideSiteSelection">
                    <app-select-sites [events]="eventsSubject.asObservable()" [filterType]=true (siteIds)="getSiteIds($event)"
                    (hideDropdown)="hideDropdown($event)">
                  </app-select-sites>
                    </span>
            </div>
          
            <div class="col-lg-12"><h5 style="margin-left: -9px;">Select Associate Patient</h5></div>
              <div class="patient-search col-lg-12">
                  <input
                    type="text" style="margin-left: -10px;float: left;"
                    class="form-control patient-search-input col-lg-8"
                    id="patient-search-input"
                    autocomplete="off"
                    (click)="openPatientDrivenListresume()"
                    (keydown)="searchPatientDrivenPatientonEnter($event)"
                    [(ngModel)]="selectedAssosiatePatientDrivenName?selectedAssosiatePatientDrivenName:PatientDrivenSearch"
                    placeholder="Search Patient..."/>
                 
                  <div class="asscoiate-actions col-lg-4" style="margin-top:0px !important;width: 100% !important;">
                    <button
                      type="button"
                      [disabled]="PatientDrivenLoading"
                      id="patient-search"
                      (click)="openPatientDrivenList()"
                      class="associate-search-button btn btn-sm btn-primary" style="width: 48%;height: 38px;padding: 0px !important;"
                    >
                      Search
                    </button>
                    <button
                      type="button"
                      [disabled]="PatientDrivenLoading"
                      class="associate-close"
                      id="associate-close"
                      (click)="closePatientDrivenList()"
                      class="associate-search-button btn btn-sm btn-default" style="width: 48%;float: right;padding: 0px !important;
                      height: 38px;"
                    >
                      Reset
                    </button>
                  </div>
                  <ul class="patientDriveUl" id="patientUl" style="margin-left: -10px;float: left;">
                    <li
                      id="associate-li"
                      class="patientDriveUl-li selected"
                      *ngIf="selectedAssosiatePatientDrivenName != ''" (click)="
                      closeList()"
                    >
                    <span>{{ selectedAssosiatePatientDrivenName }}</span>
                    </li>
                    <li
                      id="associate-li"
                      class="patientDriveUl-li"
                      *ngIf="PatientsDrivenList.length == 0"
                    >
                    <span>No item found</span>
                    </li>
                    <li
                      id="associate-li"
                      class="patientDriveUl-li"
                      [ngClass]="{
                        selected:
                        selectedAssosiatePatientDrivenId == patients.userId
                      }"
                      *ngFor="let patients of PatientsDrivenList"
                      [hidden]="
                      selectedAssosiatePatientDrivenId == patients.userId
                      "
                      (click)="
                        selectPatientDrivenPatient(
                          patients.userId,
                          patients.listDisplayName,
                          patients.displayname,
                          patients.siteId
                        )
                      "
                    >
                    <span>{{ patients.listDisplayName }}</span>
                    </li>
                  </ul>
                </div>
          </div>
         
          <div *ngIf="selectedAssosiatePatientDrivenId && !patientAssociatedForms.length">
            <a href="javascript: void(0);">
              <div class="cat__apps__messaging__tab  messages-tab">
                <div class="cat__apps__messaging__tab__name">
                  No Forms available
                </div>
              </div>
            </a>
          </div>
           <div *ngIf="selectedAssosiatePatientDrivenId && patientAssociatedForms.length">
            <div class="row" [hidden]="!patientAssociatedForms.length">
              <div class="col-md-12 searchbar">
                  <h5>Select Form</h5>
                <div class="cat__apps__messaging__header">
                  <input id="search_frm_p"
                     class="form-control cat__apps__messaging__header__input"
                    placeholder="Search..."
                    [(ngModel)]="searchInboxkeywordForm" 
                    [disabled]="!patientAssociatedForms.length" #patientDrivenForm
                  />
                  <i class="icmn-search"></i>
                  <button></button>
                </div>
              </div>
            </div>
  
  
  
            <div
              class="cat__apps__messaging__list inbox-data-container send-form-list sendform-names-badges" 
            >
              <div *ngFor="let form of patientAssociatedForms | formSearch: (patientDrivenForm && patientDrivenForm.value)">
                <a href="javascript: void(0);">
                  <div id='frm_{{form.id}}'
                    class="cat__apps__messaging__tab messages-tab row msg-tab-section"
                    (click)="goToPatientAssociatedForm(form)"
                    [ngClass]="{
                      'cat__apps__messaging__tab--selected':
                        activeForms.id === form.id
                    }"
                  >
                    <div class="col-sm-12 formname">
                      <div id='frmnme_{{form.id}}'
                        class="cat__apps__messaging__tab__name dropdown-more-profile"
                      >
                        {{ form.name }}
                      </div>
                    </div>
                    
                    <div class="col-sm-12 badges-sendforms">
                      <div  id='frmsndbtn_{{form.id}}'
                        class="badge badge-success mr-2 mb-2"
                        *ngIf="form.stafFacing == 'true'"
                      >
                        {{ form.tag_name }}
                      </div>
                      <div id='frmsndbtn_{{form.id}}'
                        class="badge badge-primary mr-2 mb-2"
                        *ngIf="
                          form.stafFacing != 'true' && form.staffFill != 'true'
                        "
                      >
                        {{ form.tag_name }}
                      </div>
                      <div id='frmsndbtn_{{form.id}}'
                        class="badge badge-primary mr-2 mb-2"
                        *ngIf="
                          form.stafFacing != 'true' && form.staffFill == 'true'
                        "
                      >
                        {{ form.tag_name }}
                      </div>
                    </div>
                  </div>
                </a>
              </div>
              <div *ngIf="!(patientAssociatedForms | formSearch: (patientDrivenForm && patientDrivenForm.value)).length">
                <a href="javascript: void(0);">
                  <div class="cat__apps__messaging__tab  messages-tab">
                    <div class="cat__apps__messaging__tab__name">
                      No Forms available
                    </div>
                  </div>
                </a>
              </div>
            </div>
           </div>
  
  
  
            
          </div>
        </div>
      </section>
    </div>
    <!--Item to delete-->
    <div class="col-lg-8 send-forms" *ngIf="selectedFlow=='patientdriven'">
      <section class="card">
        <div class="card-header">
            
          <span class="cat__core__title">
            <i
              class="fa fa-arrows-alt"
              aria-hidden="true"
              id="full-screen-icon-patient" (click)="fullscreen()"
              style="float:right;cursor: pointer;"
              title="Maximize"
            ></i>
            <strong *ngIf="!selectedAssosiatePatientDrivenName">Select Associate Patient</strong>
            <strong *ngIf="selectedAssosiatePatientDrivenName">Associate Patient: {{selectedAssosiatePatientDrivenName}}</strong>
            
          </span>
         
          <div *ngIf="!patientAssociatedActiveForm && selectedAssosiatePatientDrivenName!=''">
            <div class="card-block">
              <span class="chatwith-modal-tip">
                <img
                  src="./assets/modules/dummy-assets/common/img/chatwith-tip.png"
                />
                <span class="modal-footer-text-sign"
                  >Please choose form to view form.</span
                >
              </span>
            </div>
           
          </div>
          
        </div>
        <div
          class="card-block"
          [hidden]="!(patientAssociatedForms).length || !patientAssociatedActiveForm"
        >
        
        <form
        [formGroup]="structuredFormSend"
        id="structuredFormSend"
        class="structuredFormSend"
      >
     <div class="form-group row" [hidden]="!showApplessMenu">
          <div class="col-md-3">
            <label class="control-label">Send Form via <i chToolTip="SendFormAppless"></i></label>
          </div>
          <div class="col-md-6">
            <label class="form-check-label" style="margin-right:15px;" *ngIf="selectedRecipient && !selectedRecipient.isContactNotOptedIn">
              <input [formControl]="frmSendMode.controls['mode']" id="formSendModeAppless"
                (change)="formSendModeChange('appless')" checked="" class="form-check-input"
                name="formSendMode" value="appless" type="radio">&nbsp;AppLess (MagicLink)
            </label>
            <label for="formSendMode" class="form-check-label grey" style="margin-right:15px;" *ngIf="selectedRecipient && selectedRecipient.isContactNotOptedIn" title="{{ 'TOOLTIPS.FORM_SEND_TO_CONTACTABLE_OPTED_IN_USERS_IN_APPLES_MODE' | translate }}">
              <input [formControl]="frmSendMode.controls['mode']" id="formSendModeAppless"
                (change)="formSendModeChange('appless')" class="form-check-input disabled"
                name="formSendMode" value="appless" type="radio" disabled>&nbsp;AppLess (MagicLink)
            </label>
            <label for="formSendMode"
              class="form-check-label grey" id="disabled-label" style="margin-right:15px;" *ngIf="selectedRecipient && !selectedRecipient.passwordStatus" title="Forms can only be sent via In-App to Enrolled/Registered Users">
              <input                   
                [formControl]="frmSendMode.controls['mode']" id="formSendModeApp"
                (change)="formSendModeChange('mobileapp')" class="form-check-input disabled"
                name="formSendMode" value="mobileapp" type="radio" disabled>&nbsp;In-App
            </label>
            <label
              class="form-check-label" style="margin-right:15px;" *ngIf="selectedRecipient && selectedRecipient.passwordStatus">
              <input                   
                [formControl]="frmSendMode.controls['mode']" id="formSendModeApp"
                (change)="formSendModeChange('mobileapp')" class="form-check-input"
                name="formSendMode" value="mobileapp" type="radio">&nbsp;In-App
            </label>
          </div>
        </div>

       
      </form>
         
          
  
          <span id="loadallowconfirm" style="display:none"
            ><i class="fa fa-spinner fa-spin"></i>"Please wait, Loading...."</span
          >
  
          <div *ngIf="!staffFill">
            <div
              class="structured-form"
              
              
            >
              <div *ngIf="afterSubmit == false" class="clear-btn-form row">
                <div class="col-sm-2">
                <button
                  type="button"
                  class="btn btn-primary"
                  (click)="ClearForm()"
                  id="btm-send-form-list"
                >
                  {{'BUTTONS.CLEAR_FORM' | translate}}
                </button></div>
                <div class="col-sm-9"> 
                  <!-- save as draft msg -->
               <span  class="saveAsDraft-message"></span>
               <input type="hidden" id="last_save_draft">
               <input type="hidden" id="error_save_draft">
               <!-- save as draft msg -->
                </div>
                
              </div>
              
             
              <div
                class="structured-form-modal"
                
              >
                <div
                  class="row"
                  *ngIf="
                    activeForms.externalFileExchange == true &&
                    afterSubmit == false
                  "
                >
                  <label style="margin:13px 20px 0px 10px;"
                    ><input
                      type="radio"
                      [value]="false"
                      [(ngModel)]="orderChange"
                      name="ordertype"
                    />
                    New Referal</label
                  >
                  <label style="margin-top: 13px;"
                    ><input
                      [disabled]="!orderChangeStatus"
                      type="radio"
                      [value]="true"
                      [(ngModel)]="orderChange"
                      name="ordertype"
                    />
                    Order Change
                  </label>
                </div>
                <iframe (load)="onLoadForm()"
                  [ngClass]="{ 'block-form-mouse': allowRecipientRoles }"
                  onload="$('.structured-form-modal').scrollTop(0);"
                  allowTransparency="true"
                  class="structured-form-dekstop"
                  id="structured-form-dekstop"
                  frameborder="0"
                  scrolling="no"
                  style="width:100%;border:none"
                  [src]="formContent"
                ></iframe>
              </div>
            </div>
          </div>
          
        </div>
      </section>
    </div>
  <!---------------------------------------------------------------------------Patient Driven Flow Ends--------------------------------------------------------------->
  <div class="col-lg-12 row" *ngIf="selectedFlow!='patientdriven'">
  <div class="col-lg-4 chatroom-section-first" [hidden]= "loadFormFrom == 'worklistCenter'">
    <section class="card">
      <!--<div class="card-header" >
                <span class="cat__core__title">
                    <strong>Message Groups</strong>
                </span>
            </div>-->
      <div class="card-block">
        <div class="cat__core__card-sidebar">
          <div class="row">
            <div class="col-md-12 searchbar">
              <div class="cat__apps__messaging__header">
                <input id="search_frm"
                  class="form-control cat__apps__messaging__header__input"
                  placeholder="Search..."
                  [(ngModel)]="searchInboxkeyword"
                  #messageGroups
                />
                <i class="icmn-search"></i>
                <button></button>
              </div>
            </div>
          </div>
          <div
            class="cat__apps__messaging__list inbox-data-container send-form-list sendform-names-badges formdriven"
          >

          <!-- added for dynamic notification -->
          <input type="hidden" id="selected_form">

          
            <div *ngFor="let form of forms | formSearch: messageGroups.value" class="formslisting">
              <a href="javascript: void(0);">
                <div id='{{form.id}}'
                  class="cat__apps__messaging__tab messages-tab row msg-tab-section"
                  (click)="goToForm(form)"
                  [ngClass]="{
                    'cat__apps__messaging__tab--selected':
                      activeForms.id === form.id
                  }"
                >
                  <div class="col-sm-12 formname">
                    <div id='frmnme_{{form.id}}'
                      class="cat__apps__messaging__tab__name dropdown-more-profile"
                    >
                      {{ form.name }}
                    </div>
                  </div>
                 
                  <div class="col-sm-12 badges-sendforms">
                    <div  id='frmsndbtn_{{form.id}}'
                      class="badge badge-success mr-2 mb-2"
                      *ngIf="form.stafFacing == 'true'"
                    >
                      {{ form.tag_name }}
                    </div>
                    <div id='frmsndbtn_{{form.id}}'
                      class="badge badge-primary mr-2 mb-2"
                      *ngIf="
                        form.stafFacing != 'true' && form.staffFill != 'true'
                      "
                    >
                      {{ form.tag_name }}
                    </div>
                    <div id='frmsndbtn_{{form.id}}'
                      class="badge badge-primary mr-2 mb-2"
                      *ngIf="
                        form.stafFacing != 'true' && form.staffFill == 'true'
                      "
                    >
                      {{ form.tag_name }}
                    </div>
                  </div>
                </div>
              </a>
            </div>
            <div *ngIf="!(forms | formSearch: messageGroups.value).length">
              <a href="javascript: void(0);">
                <div class="cat__apps__messaging__tab  messages-tab">
                  <div class="cat__apps__messaging__tab__name">
                    No Forms available
                  </div>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>


  <div class="send-forms" [ngClass]="loadFormFrom == 'worklistCenter' ? 'col-lg-12' : 'col-lg-8'">
    <section class="card">
      <div class="card-header" *ngIf="isFormSelected">
        <span  class="row select-rec" *ngIf="patientAssociationOffDiv">
            <span class="cat__core__title col-sm-4">
                <strong
                [hidden]="
                  !allowRecipientRoles ||
                  !(forms | formSearch: messageGroups.value).length
                "
                >Select Recipient(s)</strong> 
              <strong
                [hidden]="
                  allowRecipientRoles ||
                  !(forms | formSearch: messageGroups.value).length ||
                  !patientAssociation
                "
                >Select Associated Patient</strong
              > 
              <strong [hidden]="(forms | formSearch: messageGroups.value).length || loadFormFrom == 'worklistCenter'"
                >No Forms Available</strong
              > 
            </span>
          <span id="loadallowconfirm" *ngIf="loadFormFrom == 'worklistCenter'" [hidden]="(forms | formSearch: messageGroups.value).length"
          ><i class="fa fa-spinner fa-spin"></i>Please wait, Loading....</span>
         </span>
      </div>
      <div
        class="card-block"
        [hidden]="!(forms | formSearch: messageGroups.value).length"
      >
        <form
          [formGroup]="structuredFormSend"
          id="structuredFormSend"
          class="structuredFormSend" [hidden]="isCollapsed"
        >
          <div class="row" [hidden]="!hideSiteSelection || isLoadFromPAH">
            <div class="col-md-12" style="float: right">
              <div class="row">
                <div class="site-label">
                  <span>{{ filterSiteLabel | translate}}</span>
                </div>
                <div class="col-md-8" style="width: 73%">
                  <app-select-sites [events]="eventsSubject.asObservable()" [filterType]=true (siteIds)="getSiteIds($event)"
                    (hideDropdown)="hideDropdown($event)">
                  </app-select-sites>
                </div>
              </div>
            </div>
          </div>
          <div *ngIf="(staffFacing =='true' || staffFacing =='practitioner') && faxQIntegration && !patientAssociation && hideSiteSelection && !isLoadFromPAH" class="form-group row" id="select-site"> 
            <label class="col-md-3 control-label">{{ 'LABELS.SELECT_SITES' | translate }}*
              <i chToolTip="fcFoldersite" data-animation="false" style="padding-left: 3px;"></i>
            </label>
            <div class="col-md-6"><span (click)="changesite()">
              <app-select-sites [events]="eventsSubject.asObservable()" [hideApplyFilter]=true [selectedSiteIds]=siteIds [singleSelection]=true (siteIds)="getSiteId($event)" [siteSelection]="true" (hideDropdown)="hideDropdown($event)">
              </app-select-sites>
            </span>
  
                <div class="alert alert-danger site-position" *ngIf="siteRequired">
                    Please select site .
                </div>
            </div>
        </div>
          <!-- <div class="card-block"> -->
          <!-- <div class="cat__core__card-sidebar"> -->
          <!-- <div class="cat__apps__messaging__header"> firoz -->
            <div class="row select-rec" [hidden]="!patientAssociation || !isFormSelected">
                <div class="col-lg-10">
                  <div class="associate-search display-flex" [ngClass]="{ 'disabled': isLoadFromPAH }">
                    <input
                      type="text"
                      class="form-control associate-search-input col-lg-10 width-auto margin-auto"
                      id="associate-search-input"
                      autocomplete="off"
                      placeholder="Search Associated Patient..."
                      [disabled]="brightreeFormLandingFlow"
                      (click)="openAssociateList()"
                    />
                    <!-- <span class="associate-close" id="associate-close" (click)="closeSelectedAssociatePatient()">x</span> -->
                    <div class="associate-search-buttons col-lg-2" *ngIf="!brightreeFormLandingFlow">
                      <button
                        type="button"
                        [disabled]="associatePatientLoading"
                        id="associate-search"
                        (click)="checkAssociatePatientWithTems()"
                        class="associate-search-button width-full btn btn-sm btn-primary"
                      >
                        Search
                      </button>
                      <button
                        type="button"
                        [disabled]="associatePatientLoading"
                        class="associate-close"
                        id="associate-close"
                        (click)="closeSelectedAssociatePatient()"
                        class="associate-search-button width-full btn btn-sm btn-default"
                      >
                        Reset
                      </button>
                    </div>
                    <div class="associate-search-buttons col-lg-1" *ngIf="!isLoadFromPAH && !brightreeFormLandingFlow">
                      <button
                        type="button"
                        class="associate-create btn btn-sm"
                        [disabled]="externalUserId"
                        (click)="openAssocpopup()"
                      >
                        <i class="fa fa-user-plus addassocpt"></i>
                      </button>
                    </div>
                    <ul class="associate-ul col-lg-10 w-100" id="associate-ul">
                      <li
                        id="associate-li"
                        class="associate-li selected"
                        *ngIf="selectedAssosiatePatientName != ''"
                      >
                        <span>{{ selectedAssosiatePatientName }}</span>
                      </li>
                      <li
                        id="associate-li"
                        class="associate-li"
                        *ngIf="assosiatedPatients.length == 0"
                      >
                        <span>No item found</span>
                      </li>
                      <li
                        id="associate-li"
                        class="associate-li"
                        [ngClass]="{
                          selected:
                            selectedAssosiatePatient == assosiatedPatient.userId
                        }"
                        *ngFor="let assosiatedPatient of assosiatedPatients"
                        [hidden]="
                          selectedAssosiatePatient == assosiatedPatient.userId
                        "
                        (click)="
                          setAssosiatePatient(assosiatedPatient)
                        "
                      >
                        <span>{{ assosiatedPatient.listDisplayName }}</span>
                      </li>
                    </ul>
                  </div>
                  <!-- <br>
                                <select class="form-control select2" formControlName="assosiatedPatients" id="assosiatedPatients" (change)="selectAssosiatePatient()">
                                    <option></option>
                                    <option *ngFor="let assosiatedPatient of assosiatedPatients" value="{{assosiatedPatient.userId}}">
                                        {{(assosiatedPatient.caregiver_displayname)?((assosiatedPatient.dob_formatted)?(assosiatedPatient.displayname+' - ' + assosiatedPatient.dob_formatted+' ('+assosiatedPatient.caregiver_displayname +')'):(assosiatedPatient.displayname+' ('+assosiatedPatient.caregiver_displayname +')')):((assosiatedPatient.dob_formatted)?(assosiatedPatient.displayname+ ' - ' +assosiatedPatient.dob_formatted):assosiatedPatient.displayname)}}
                                        {{(assosiatedPatient.passwordStatus == 'true' ? " (Registered)" : "")}}
                                    </option>
                                </select> -->
                </div>
                <div class="col-md-10 margin-top-5px" *ngIf="patientAssociation && _structureService.isMultiAdmissionsEnabled && selectedAssosiatePatient">
                  <app-admissions-dropdown
                    [siteIds]="siteIds"
                    [selectedPatient]="selectedAssosiatePatient"
                    (selectedItem)="selectedAdmissionAssociatePatientHandle($event)"
                   ></app-admissions-dropdown>
                </div>
            <div class="col-lg-11" *ngIf="showRecipientsForSendCompletedForm && staffFacing == 'true' && associatePatientId !== '' && (!_structureService.isMultiAdmissionsEnabled || (selectedAdmission && selectedAdmission.id))">
              <div class="recipient-list mt-2 mb-3">
                <app-recipients placeholder="{{ 'PLACEHOLDERS.RECIPIENTS_FORM_COMPLETED' | translate }}" [patientId]="associatePatientId" [admissionId]="selectedAdmission && selectedAdmission.id" (recipients)="setRecipients($event)">
                </app-recipients>
              </div>
              <div class="recipient-actions complete-recipient-icon">
                <i class="icmn-info pl-2" data-toggle='tooltip' data-placement='right' chToolTip="sendCompletedFormRecipients"></i>
              </div>
            </div>
              </div>
        <span *ngIf="patientAssociationOffDiv && isFormSelected">
          <div class="row select-rec" [hidden]="!allowRecipientRoles">
            <div class="col-lg-10 display-flex">
              <div id="tags" class="col-lg-10 padding-2px">
                <div class="input-dropdown width-full">
                  <div class="recipient-search-area"></div>
                  <input
                    type="text"
                    class="form-control margin-auto "
                    id="tagsInput"
                    autocomplete="off"
                    value=""
                    [disabled]="brightreeFormLandingFlow"
                    (keydown)="searchRecipientonEnter($event)"
                    (click)="openRecipientList()"
                    placeholder="Search Recipients"
                    *ngIf="!_structureService.isMultiAdmissionsEnabled || staffFacing === 'practitioner' || (_structureService.isMultiAdmissionsEnabled && selectedRecipients.length === 0)"
                  />
                  <div class="tw-relative">
                    <ul *ngIf="!_structureService.isMultiAdmissionsEnabled || staffFacing === 'practitioner' || (_structureService.isMultiAdmissionsEnabled && selectedRecipients.length === 0)" [ngClass]="(enableAlternateContacts==true)?'associate-ul recipient-ul alternatereclist':'associate-ul recipient-ul'"  id="recipient-ul" class="mt-0">
                      <li
                        id="recipient-li"
                        class="associate-li recipient-li"
                        *ngIf="tenantUsers.length == 0"
                      >
                      <span>No item found</span>
                      </li>
                      <li id="li-{{ user.optionId }}" class="associate-li recipient-li" [ngClass]="{ 'li-selected': checkUserExist(user.optionId), 'inactive': user.noContactAvailable }" 
                        *ngFor="let user of tenantUsers" (click)="setSelectedRecipients(user, user.optionId, $event)">
                        <span class="recipient-li-color" [ngClass]="{ 'text-muted block-form-cursor': user.noContactAvailable }" [chToolTip]="user.noContactAvailable? 'noContactUser': ''" data-animation="false"> {{user.recipientOptionText}} </span>
                        <ul class="contact-ul" *ngIf="user.alternateContacts && user.alternateContacts.length > 0 && enableAlternateContacts">
                          <li *ngFor="let contact of user.alternateContacts" id="li-{{ contact.contactId }}--{{user.optionId}}" class="contact-li" [ngClass]="{ 'text-muted block-form-cursor': contact.noContactAvailable }" [chToolTip]="contact.noContactAvailable? 'noContactUser': ''" (click)="setSelectedAlternateContact(user, user.optionId, contact)" data-animation="false">
                            {{user.displayPatient}} ( {{contact.displayName}} - {{contact.relation}}) - {{contact.patientDobFormatted}} {{user.displayMrn}}<span *ngIf="contact.isVirtual">(Virtual)</span><span *ngIf="!contact.isVirtual">(Enrolled)</span> 
                          </li>
                        </ul>
                      </li>
                      <li class="render-manipulate" *ngIf="staffFacing === 'practitioner' || (tenantUsers.length > 0 && !_structureService.isMultiAdmissionsEnabled && !isLoadFromPAH && !brightreeFormLandingFlow)" >
                        <input
                          type="button"
                          class="recipient-select-all btn"
                          (click)="selectAllRecipients($event)"
                          value="Select All"
                        />
                        <input
                          type="button"
                          class="recipient-class-clear btn"
                          (click)="closeSelectedRecipient(true)"
                          value="Clear All"
                        />
                        <input
                          type="button"
                          class="recipient-class-done btn"
                          *ngIf="selectedRecipients.length > 0"
                          (click)="doneSelectedRecipient()"
                          value="Done"
                        />
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
             
              <div class="associate-search-buttons col-lg-2" *ngIf="!brightreeFormLandingFlow && !isLoadFromPAH || staffFacing === 'practitioner'">
                <button
                  type="button"
                  [disabled]="recipientLoading"
                  id="recipient-search"
                  (click)="checkRecipientWithTems()"
                  class="recipient-search-button btn btn-sm btn-primary"
                >
                  Search
                </button>
                <button
                  type="button"
                  [disabled]="recipientLoading"
                  id="recipient-close"
                  (click)="closeSelectedRecipient()"
                  class="recipient-search-button btn btn-sm btn-default recipient-close"
                >
                  Reset
                </button>
              </div>
              <!-- <select class="form-control select2" formControlName="tenantUsers" id="tenantUsers" multiple>
                                <option *ngFor="let user of tenantUsers" value="{{(user.tag_name ?'tag-'+user.id :(user.caregiver_userid ? user.userId+'--'+user.caregiver_userid :user.userId))}}">                                    
                                    {{(user.tag_name ? user.tag_name + ' [User Tag]' :
                                    ((user.roleId == "3")?(user.caregiver_userid ? (user.caregiver_dob_formatted ?
                                    (user.caregiver_displayname + ' - ' + user.caregiver_dob_formatted) :
                                    user.caregiver_displayname ) + ' (' + user.displayname + ')' : (user.dob ?
                                    user.displayname + ' - ' + user.dob_formatted :
                                    user.displayname)):(user.displayname))) }}
                                </option>
                            </select> -->
            </div>
            <!-- *ngIf="!staffFill" -->
            <div class="col-lg-2">
              <button *ngIf="formSendInProgress" type="button" class="btn btn-primary" disabled >
                <span >
                  <i class="fa fa-spinner fa-spin "></i> Sending
                </span>
              </button>
              <button *ngIf="!formSendInProgress" type="button" class="btn btn-primary d-none" (click)="sendForm()" id="btm-send-form-list">
                <span >
                  <i class="fa fa-comments" aria-hidden="true"></i> Send
                </span>
              </button>
            </div>
            <div class="col-lg-12" *ngIf="isNonContactableUsersExists">
              <i class="fa fa-exclamation-circle text-warning" aria-hidden="true"></i>&nbsp;
              <a class="text-warning non-contactable-link" href="javascript: void(0);" (click)="showNonContactableUsersModal()">
                <strong>{{ 'MESSAGES.SELECTED_USER_TAGS_NON_CONTACTABLE_INFO' | translate }}</strong>
              </a>
            </div>
          </div>
          <div class="row" *ngIf="allowRecipientRoles">
            <div class="col-md-10 margin-top-5px" *ngIf="_structureService.isMultiAdmissionsEnabled && staffFacing !== 'practitioner' && selectedRecipients && selectedRecipients[0]">
              <app-admissions-dropdown [siteIds]="siteIds" [selectedPatient]="selectedPatient" (selectedItem)="selectedAdmissionHandle($event)"></app-admissions-dropdown>
            </div>
          </div>
          <div
            class="row select-rec"
            [hidden]="!allowRecipientRoles"
            *ngIf="(((selectedRecipients.length ==1 && staffFacing == 'false') || (staffFacing == 'practitioner' && associatePatientId !== '')) && (!_structureService.isMultiAdmissionsEnabled || (selectedAdmission && selectedAdmission.id))) && showRecipientsForSendCompletedForm"
          >
          <div class="col-lg-11">
            <div class="recipient-list mb-3">
              <app-recipients placeholder="{{ 'PLACEHOLDERS.RECIPIENTS_FORM_COMPLETED' | translate }}" [patientId]="associatePatientId" [admissionId]="selectedAdmission && selectedAdmission.id" (recipients)="setRecipients($event)">
              </app-recipients>
            </div>
            <div class="complete-recipient-icon"><i class="icmn-info pl-2" data-toggle='tooltip' data-placement='right' chToolTip="sendCompletedFormRecipients"></i></div>
          </div>
          </div>
          <div class="row" [hidden]="!allowRecipientRoles">
            <div class="col-lg-10">
              <textarea
                formControlName="message"
                class="form-control"
                id="message"
                placeholder="Enter your message"
                [readonly]="stafffillfirst == false && (!stafffillfirst || (stafffillfirst &&  activeForms.stafFacing == 'practitioner' && selectedRecipients=='') || (stafffillfirst &&  activeForms.stafFacing == 'practitioner' && selectedRecipients!='' && activeForms.patientAssociation=='true'  && !this.selectedAssosiatePatient))||(activeForms.stafFacing == 'practitioner'&&selectedRecipients=='')"
                (blur)="onBlurEvent($event)" xssInputValidate="{{'LABELS.MESSAGE' | translate}}"
              ></textarea>
              <!-- <div class="alert alert-danger" *ngIf="structuredFormSend.controls['message'].value.length > 500 ">
                                                This message is too long. Please shorten your message.
                                        </div> -->
            </div>  
          </div>
        </span>
          <div class="form-group row" [hidden]="!showApplessMenu || !isFormSelected || (showApplessMenu && !selectedAssosiatePatient &&
          patientAssociation && (_structureService.isMultiAdmissionsEnabled && !selectedAdmission?.id)) || fwdTo == 'staff'">
            <div class="col-md-3">
              <label class="control-label">Send Form via <i chToolTip="SendFormAppless"></i></label>
            </div>
            <div class="col-md-6">
              <label class="form-check-label" style="margin-right:15px;" *ngIf="selectedRecipient && !selectedRecipient.isContactNotOptedIn">
                <input [formControl]="frmSendMode.controls['mode']" id="formSendModeAppless"
                  (change)="formSendModeChange('appless')" checked="" class="form-check-input"
                  name="formSendMode" value="appless" type="radio">&nbsp;AppLess (MagicLink)
              </label>
              <label for="formSendMode" class="form-check-label grey" style="margin-right:15px;" *ngIf="selectedRecipient && selectedRecipient.isContactNotOptedIn" title="{{ 'TOOLTIPS.FORM_SEND_TO_CONTACTABLE_OPTED_IN_USERS_IN_APPLES_MODE' | translate }}">
                <input [formControl]="frmSendMode.controls['mode']" id="formSendModeAppless"
                  (change)="formSendModeChange('appless')" class="form-check-input disabled"
                  name="formSendMode" value="appless" type="radio" disabled>&nbsp;AppLess (MagicLink)
              </label>
              <label for="formSendMode"
                class="form-check-label grey" id="disabled-label" style="margin-right:15px;" *ngIf="selectedRecipient && !selectedRecipient.passwordStatus" title="Forms can only be sent via In-App to Enrolled/Registered Users">
                <input                   
                  [formControl]="frmSendMode.controls['mode']" id="formSendModeApp"
                  (change)="formSendModeChange('mobileapp')" class="form-check-input disabled"
                  name="formSendMode" value="mobileapp" type="radio" disabled>&nbsp;In-App
              </label>
              <label
                class="form-check-label" style="margin-right:15px;" *ngIf="selectedRecipient && selectedRecipient.passwordStatus">
                <input                   
                  [formControl]="frmSendMode.controls['mode']" id="formSendModeApp"
                  (change)="formSendModeChange('mobileapp')" class="form-check-input"
                  name="formSendMode" value="mobileapp" type="radio">&nbsp;In-App
              </label>
            </div>
          </div>

          <!-- </div> -->
          <!-- </div> -->
          <!-- </div> -->
        </form>
        <div *ngIf="isFormSelected">
           <!-- <div class="form-actions">
                    <button  type="button"  class="btn btn-primary" (click)="sendForm()">Send</button>
                    <button type="button" (click)="cancel()"  class="btn btn-default">Cancel</button>
                </div> -->
          <div
          class="card-block staff-hide-div"
          *ngIf="
            !allowRecipientRoles &&
            !selectedAssosiatePatient &&
            patientAssociation
          "
        >
          <span class="chatwith-modal-tip">
            <img
              src="./assets/modules/dummy-assets/common/img/chatwith-tip.png"
            />
            <span class="modal-footer-text-sign"
              >Please choose associated patient to view form.</span
            >
          </span>
        </div>

        <span id="loadallowconfirm" style="display:none"
          ><i class="fa fa-spinner fa-spin"></i>"Please wait, Loading...."</span
        >

        <!-- form stabilization -->
          <!--  <iframe id="structured-form-dekstop-div" frameborder="0" scrolling="no" [src]="formContent"></iframe> -->
          <input type="hidden" id="response">
          <button  type="button" style="display:none;" id="responsebtn" (click)="notify()">Send</button>
          <button  type="button" style="display:none;" id="failNotify" (click)="failNotify()"></button>
        <!-- form stabilization -->
  
        <div *ngIf="!staffFill">
        <div  *ngIf="patientAssociationOffDiv">
            <div
            class="structured-form"
            [hidden]="stafffillfirst == false && (!stafffillfirst || (stafffillfirst &&  activeForms.stafFacing == 'practitioner' && selectedRecipients=='') || (stafffillfirst &&  activeForms.stafFacing == 'practitioner' && selectedRecipients!='' && activeForms.patientAssociation=='true'  && !this.selectedAssosiatePatient))"
          >
          <ng-container *ngTemplateOutlet="expandCollapse"></ng-container>
            <div *ngIf="afterSubmit == false" class="clear-btn-form row">
              <div class="col-sm-2"> 
              <button
                type="button"
                class="btn btn-primary" 
                (click)="ClearForm()" [ngClass]="{ 'btn-clear-form' : isPatientFacing}"
                id="btm-send-form-list"
              >
                {{'BUTTONS.CLEAR_FORM' | translate}}
              </button></div>
              <div class="col-sm-9"> 
              <!-- save as draft msg -->
              <span  class="saveAsDraft-message"></span>
              <input type="hidden" id="last_save_draft">
              <input type="hidden" id="error_save_draft">
              <!-- save as draft msg -->
              </div>
              
            </div>
            <h3
              class="cat__core__title previewedit"
              *ngIf="allowRecipientRoles"
            >
              <strong>Form Preview</strong>
            </h3>
            <!-- <div class="structured-form-modal" *ngIf="!allowRecipientRoles && !selectedAssosiatePatient">Please choose assosiated patient to view form</div> -->
            <div [ngStyle]="{'overflow-y': isPatientFacing ? 'hidden': 'auto'}"
              class="structured-form-modal"
              *ngIf="
                (!allowRecipientRoles && selectedAssosiatePatient) ||
                allowRecipientRoles ||
                !patientAssociation
              "
            >
              <div
                class="row"
                *ngIf="
                  activeForms.externalFileExchange == true &&
                  afterSubmit == false
                "
              >
                <label style="margin:13px 20px 0px 10px;"
                  ><input
                    type="radio"
                    [value]="false"
                    [(ngModel)]="orderChange"
                    name="ordertype"
                  />
                  New Referal</label
                >
                <label style="margin-top: 13px;"
                  ><input
                    [disabled]="!orderChangeStatus"
                    type="radio"
                    [value]="true"
                    [(ngModel)]="orderChange"
                    name="ordertype"
                  />
                  Order Change
                </label>
              </div>
              <iframe (load)="onLoadForm()"
                onload="$('.structured-form-modal').scrollTop(0);"
                allowTransparency="true"
                class="structured-form-dekstop"
                id="structured-form-dekstop"
                frameborder="0"
                scrolling="no"
                style="width:100%;border:none"
                [src]="formContent"
              ></iframe>
            </div>
          </div>
            </div>
          </div>

        <div *ngIf="staffFill">
          <div class="card-block" [hidden]="stafffillfirst">
            <span class="chatwith-modal-tip">
              <img
                src="./assets/modules/dummy-assets/common/img/chatwith-tip.png"
              />
              <span class="modal-footer-text-sign"
                >Please choose recipient(s) to fill form and send.</span
              >
            </span>
          </div>
          <div class="card-block" *ngIf= "stafffillfirst &&  activeForms.stafFacing == 'practitioner'&&selectedRecipients==''">
            <span class="chatwith-modal-tip">
              <img
                src="./assets/modules/dummy-assets/common/img/chatwith-tip.png"
              />
              <span class="modal-footer-text-sign"
                >Please choose recipient(s) to fill form and send.</span
              >
            </span>
          </div>
        <!-- {{stafffillfirst}}-{{staffFill}}--{{selectedAssosiatePatient}}--{{selectedRecipients}} -->
          <ng-container
          
          >
            <div class="structured-form" [hidden]="stafffillfirst == false && (!stafffillfirst || (stafffillfirst &&  activeForms.stafFacing == 'practitioner' && selectedRecipients=='') || (stafffillfirst &&  activeForms.stafFacing == 'practitioner' && selectedRecipients!='' && activeForms.patientAssociation=='true'  && !this.selectedAssosiatePatient))">
              <div class="clear-btn-form row">
                <ng-container *ngTemplateOutlet="expandCollapse"></ng-container>
              <div class="col-sm-2" *ngIf="(stafffillfirst &&  activeForms.stafFacing != 'practitioner')||(stafffillfirst &&  activeForms.stafFacing == 'practitioner'&&selectedRecipients!='')">  <button
                  type="button"
                  class="btn btn-primary" [ngClass]="{ 'd-none' : isPatientFacing}"
                  (click)="ClearForm()"
                  id="btm-send-form-list"
                >
                  {{'BUTTONS.CLEAR_FORM' | translate}}
                </button></div><div class="col-sm-9"> 
                  <!-- save as draft msg -->
              <span  class="saveAsDraft-message"></span>
              <input type="hidden" id="last_save_draft">
              <input type="hidden" id="error_save_draft">
              <!-- save as draft msg -->
              </div>
              </div>
              <h3 class="cat__core__title previewedit" *ngIf="(stafffillfirst &&  activeForms.stafFacing != 'practitioner')||(stafffillfirst &&  activeForms.stafFacing == 'practitioner'&&selectedRecipients!='')">
                <strong>Form Preview with Edit</strong>
              </h3>
              <div
                class="row"
                *ngIf="
                  activeForms.externalFileExchange == true &&
                  afterSubmit == false
                "
              >
                <label style="margin:13px 20px 0px 10px;"
                  ><input
                    type="radio"
                    [value]="false"
                    [(ngModel)]="orderChange"
                    name="ordertype"
                  />
                  New Referal</label
                >
                <label style="margin-top: 13px;"
                  ><input
                    [disabled]="!orderChangeStatus"
                    type="radio"
                    [value]="true"
                    [(ngModel)]="orderChange"
                    name="ordertype"
                  />
                  Order Change
                </label>
              </div>
              <!-- <div class="structured-form-modal" *ngIf="!allowRecipientRoles && !selectedAssosiatePatient">Please choose assosiated patient to view form</div> -->
            <div class="structured-form-modal" *ngIf="(showMainFrame == true || (stafffillfirst &&  activeForms.stafFacing != 'practitioner')||(stafffillfirst &&  activeForms.stafFacing == 'practitioner'&&selectedRecipients!=''))"
            [ngStyle]="{'overflow-y': isPatientFacing ? 'hidden': 'auto'}">
            <button *ngIf="isPatientFacing" style="margin-top: 10px;"
                  type="button"
                  class="btn btn-primary btn-clear-form"
                  (click)="ClearForm()"
                  id="btm-send-form-list"
                >{{'BUTTONS.CLEAR_FORM' | translate}}</button>
              <iframe (load)="onLoadForm()"
                  onload="$('.structured-form-modal').scrollTop(0);"
                  allowTransparency="true"
                  class="structured-form-dekstop"
                  id="structured-form-dekstop"
                  frameborder="0"
                  scrolling="no"
                  style="width:100%;border:none"
                  [src]="formContent"
                ></iframe>
              </div>
            </div>
          </ng-container>
        </div>
        </div>
      </div>
    </section>
    <section class="not-select" *ngIf="!isFormSelected">
      <div class="icon fa fa-list-alt"></div>
      <div class="text"> {{ 'PLACEHOLDERS.PLEASE_SELECT_FORM_FROM_LIST' | translate }}</div>
    </section>
  </div>
  </div>
</div>
</section>

<!-- END: tables/datatables -->
<!-- New assoc patient starts -->

<div
  class="modal fade forward-modal associated-patient"
  id="assocModal"
  role="dialog"
  aria-labelledby="exampleModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-llg" role="document">
    
      <div class="modal-content">
        <app-associate-patient-modal [selectedSites]="selectedSiteIds" (closeModal)="handleModalClose($event)" (patientDetails)="getPatientData($event)" (selectedAssocPatient)="getSelectedPatientDetails($event)"></app-associate-patient-modal>
        
      </div>
    
  </div>
</div>
<div class="modal fade forward-modal" id="non-contactable-users-modal" tabindex="-1" role="dialog" *ngIf="showNonContactableUsers"
  aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg" style="max-width: 90% !important;">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="exampleModalLabel">{{ 'TITLES.NON_CONTACTABLE_USERS' | translate }}</h4>
        <button type="button" class="close" data-dismiss="modal" attr.aria-label="{{ 'BUTTONS.CLOSE' | translate }}"
          (click)="hideNonContactableUsersModal()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <app-users-component [data]="{userTagIds: selectedUserTags }"></app-users-component>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="hideNonContactableUsersModal()">{{
          'BUTTONS.CLOSE' | translate }}</button>
      </div>
    </div>
  </div>
</div>
<ch-loader [showInPageCenter]="true" [showLoader]="showPageLoader" [showLoaderMessage]="showLoaderMessage"><ch-loader></ch-loader>
<!-- New assoc patient ends -->
<ng-template #expandCollapse>
  <div class="expand-collapse">
    <span class="col-sm">
      <i
        class="fa fa-arrows-alt"
        aria-hidden="true"
        id="full-screen-icon"
        style="float:right;cursor: pointer;"
        title="Maximize" (click)="fullscreen()" [hidden]= "loadFormFrom === 'worklistCenter'">
      </i>
    </span>
  </div>
</ng-template>