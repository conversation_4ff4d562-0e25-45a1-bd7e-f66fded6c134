import { Component, OnInit, OnDestroy, Input, ElementRef, Renderer, ViewChild } from '@angular/core';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute, Params, NavigationEnd, NavigationStart } from '@angular/router';
import { PapaParseService } from 'ngx-papaparse';
import { StructureService } from '../structure.service';
import { FormsService } from './forms.service';
import { DomSanitizer } from '@angular/platform-browser';
import { Modal } from "../shared/commonModal"
import { SharedService } from '../shared/sharedServices';
import { SignPadComponent } from '../shared/signPad/sign-pad.component';
import { FormpPipe } from './formp.pipe';
import { formSearchPipe } from './formp.pipe';
import { SignService } from '../signatures/sign.service';
import { ModalComponent } from '../signatures/modal.component';
import { InboxService } from '../inbox/inbox.service';
import { Location, DatePipe } from '@angular/common';
import { ToolTipService } from '../tool-tip.service';
import { ISubscription } from "rxjs/Subscription";
import { EnrollService } from '../user-registration/enroll.service';
import { DateTimePipe } from './formp.pipe';
import { GlobalDataShareService } from '../shared/global-data-share.service';
import { Guid } from "guid-typescript";
import { UserService } from 'app/services/user/user.service';
import { Subscription } from 'rxjs';
import { APIs } from 'app/constants/apis';

var jstz = require('jstz');
declare var signaturePad: any;
declare var $: any;
declare var swal: any;
const timezone = jstz.determine();
declare var NProgress: any;
let moment = require('moment/moment');

@Component({
  selector: 'form_send_form',
  templateUrl: './send-supply-forms-list.html'
})
export class SendSupplyFormDataComponent implements OnInit, OnDestroy {
  @ViewChild(ModalComponent) child: ModalComponent;
  strucuredFormsData: any = '';
  userData: any = '';
  signUrl: any = '';
  dTable;
  forms: any = '';
  activeForms: any = '';
  dataLoadingMsg = true;
  structuredFormSend: FormGroup;
  tenantUsers: any = [];
  formContent: any;
  result: any = '';
  allowRecipientRoles: any;
  assosiatedPatients: any = [];
  selectedAssosiatePatient: any = '';
  newPatient: FormGroup;
  iscellNumber = false;
  isEmail = false;
  isFirstName = false;
  userDataConfig: any;
  isLastName = false;
  isZipCode = false;
  patientAssociation = false;
  isDob = false;
  isReqEmail = false;
  isReqFirstName = false;
  isReqLastName = false;
  isReqZipCode = false;
  isReqDob = false;
  oneTimeCopy: any = '';
  popupmsg = '';
  assocpdata = { "email": '', "firstname": '', "lastname": '', "dob": '', "cell": '', "zipcode": '', "tenantId": '', "operation": 0, "dday": null, "dmonth": null, "dyear": null }
  patientTags = [];
  searchInboxkeyword;
  crossTenantChangeSubscriber: any;
  private socketEventSubscriptions: Subscription[] = [];
  constructor(
    private _structureService: StructureService,
    private route: ActivatedRoute,
    private router: Router,
    private _formsService: FormsService,
    private modals: Modal,
    private sanitizer: DomSanitizer,
    public _sharedService: SharedService,
    private formpPipe: FormpPipe,
    elementRef: ElementRef,
    renderer: Renderer,
    private _formBuild: FormBuilder,
    private SignService: SignService,
    public _inboxService: InboxService,
    private _enrollService: EnrollService,
    private _datePipe: DatePipe,
    private userService: UserService
  ) {
    /*renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
      if(event.target.attributes[0].name == 'data-view') {
        console.log(this.activeForms);
        this.showFormPriview();
        var formsUrl = this._structureService.serverBaseUrl+'/machform/Upload/embed.php?id='+this.activeForms.id;
        var iFrameContent='<iframe onload="javascript:parent.scrollTo(0,0);" allowTransparency="true" class="structured-form-dekstop" frameborder="0" scrolling="no" style="width:100%;border:none" src="'+formsUrl+'" ></iframe>';
        this.formContent = this.sanitizer.bypassSecurityTrustHtml(iFrameContent);
      }else {

      }
    });*/
    this.crossTenantChangeSubscriber = this._sharedService.crossTenantChange.subscribe(
      (onInboxData) => {
        this.ngOnInit();
      }
    );
  }
  ngOnInit() {
    this.oneTimeCopy = 0;

    this.userData = JSON.parse(this._structureService.userDetails);
    this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
    console.log('this.userDetails'+this._structureService.userDetails);
    const tagGetData = "?userId=" + this.userData.userId + "&tenantId=" + this.userData.tenantId + '&group=3';
    const tagTypes = ["2"]; // Message Tag =1, User Tag =2 , Document Tag =3
    this._structureService.getTagsByGroup(tagGetData, tagTypes).then((data: any) => {
      this.patientTags = data;
    });
    this.socketEventSubscriptions.push(
    this._structureService.subscribeSocketEvent('activityTrack').subscribe((data) => {
      if (data.formData.fromMob == 'false' && data.formData.staffFacing == 'true') {
        if (this.selectedAssosiatePatient) {
          var patientId = this.selectedAssosiatePatient;
        } else {
          var patientId = this.userData.userId;
        }
        var activityData = {
          activityName: "Submit Form " + this._sharedService.myFormsType,
          activityType: "forms",
          activityDescription: this.userData.displayName + ' has completed staff facing form ' + data.formData.formName + ' (' + data.formData.form_id + ')'
        };
        this._structureService.trackActivity(activityData);
        //Add configuration in form type Default Outgoing Filing Center (From Citus Health) after Form Submit.
        var formData = {
          "userid": this.userData.userId,
          "tenantId": data.formData.tenant_id,
          "recipient_id": data.formData.recipient_id,
          "formId": data.formData.form_id,
          "staffFacing": data.formData.staffFacing,
          "form_submission_id": data.formData.submissionId,
          "form_id": data.formData.form_id,
          "form_name": data.formData.formName,
          "patient_id": "",
          "id": "",
          "patientUser": patientId

        }
        console.log(formData);
        if (data.formData.submissionId) {
          if (this.oneTimeCopy == 0 || this._sharedService.myFormsType == "DRAFTS") {
            this._formsService.filingCenterSubmittedFormdata(formData);
            this.oneTimeCopy = 1;
          }
          else {
            this.oneTimeCopy = 1;
          }
        }


        //
      }
    })
    );
    this.socketEventSubscriptions.push(
    this._structureService.subscribeSocketEvent('submittedData').subscribe((data) => {
      $("#textmessage").text("");
$("#newloader").hide();
      var successMessage=data.formData.form_Success_message;
      if (this.selectedAssosiatePatient) {
        var patientId = this.selectedAssosiatePatient;
      } else {
        var patientId = this.userData.userId;
      }
      if (data.formData.fromMob == 'false' && data.formData.staffFacing == 'staffsupplyCount') {
        var formData = {
          "userid": this.userData.userId,
          "tenantId": data.formData.tenant_id,
          "recipient_id": data.formData.recipient_id,
          "formId": data.formData.form_id,
          "staffFacing": data.formData.staffFacing,
          "form_submission_id": data.formData.submissionId,
          "form_id": data.formData.form_id,
          "form_name": data.formData.formName,
          "patient_id": "",
          "id": "",
          "patientUser": patientId
        }

        console.log(formData);
        if (data.formData.submissionId) {
          if (this.oneTimeCopy == 0) {
            this._formsService.filingCenterSubmittedFormdata(formData);
            this.oneTimeCopy = 1;
          } else {
            this.oneTimeCopy = 1;
          }
        }
        //
      }
    })
  );
    //nov192019
    // window.addEventListener("message", function (event) {
    //   try {
    //     if (event.data) {
    //       var height = (event.data).split("=");
    //       console.log(height);
    //       if(height.length&&height[0]=="scrollTopformsubmit"){
    //         localStorage.setItem('autosavecalledformid',"0");
    //         localStorage.setItem('autosavecalled',"false");
    //         $(".structured-form-modal").scrollTop(0);
    //         window.scrollTo(0, 0);
    //       }
    //       else if(height.length&&height[0]=="gotocreatesendform"){
    //        $(".searchbar input").val("");
    //        $(".send-form-list ").find('.formname').first().trigger('click');
    //       }else if(height.length&&height[0]=="autosavecalled"){
    //          //   localStorage.setItem('autosavecalled', height[1]);
    //        //this.localStorage

    //       }else if(height.length&&height[0]=="autosavecalledformid"){

    //         //localStorage.setItem('autosavecalledformid', height[1]);
    //      }
    //      else if(height.length&&height[0]=="saveasdraftidtodeleted"){

    //       //localStorage.setItem('saveasdraftidtodeleted', height[1]);
    //    }
    //      else {

    //       if (typeof (parseInt(height[1])) == "number") {
    //         console.log("7777777777777777777777777777777");
    //         $("#structured-form-dekstop").height(height[1] + "px")
    //       } else {
    //         $("#structured-form-dekstop").height("5000px")
    //       }
    //     }
    //     }
    //   } catch (error) {
    //     console.log(error);
    //   }
    // }, false);
    /*this.dTable = $('#form-send-dt').DataTable();
    $('#form-send-dt').on('click', 'tbody tr', function() {
      console.log('API row values : ', this.dTable.row(this).data());
    })*/
    $('#tenantUsers').select2({
      placeholder: "Select Recipient(s)"
    });
    /*for addng new on the fly sendFormpatients starts*/
    $(() => {
      /*$(".view-tooltip").toolsendFormtip({
        title: "Select the users role to whome the message to be send"
      });
      $('.swal-btn-info').click(function (e) {
        e.preventDefault();
        swal({
          title: "Are you sure you want to send this message?",
          type: "info",
          showCancelButton: true,
          cancelButtonClass: "btn-default",
          confirmButtonText: "Ok",
          confirmButtonClass: "btn-primary"
        });
      });*/
      $(document).on("focus", "#us-phone-mask-input", function () {
        $(this).mask("(000) 000-000000");
      });

      $('#dob-date-picker').combodate({
        format: 'DD-MM-YYYY',
        template: 'MMM D YYYY',
        minYear: 1900,
        firstItem: 'empty',
        maxYear: (new Date()).getFullYear(),
        customClass: 'form-control select2 dob-sec',
        smartDays: true
      });

      $('.month, .day, .year').select2();
      $('.month').on('select2:select', (e) => {
        this.assocpdata.dmonth = $(e.target).val();
        $('.day').select2('open');
      }); $('.day').on('select2:select', (e) => {
        $('.year').select2('open');
        this.assocpdata.dday = $(e.target).val();
      });
      $('.year').on('select2:select', (e) => {
        this.assocpdata.dyear = $(e.target).val();
      });
    });
    this._structureService.getVirtualPatientOptions().then((data) => {
      let parsedResponceData = data;
      var vitualpsettings = JSON.parse(parsedResponceData['getVirtualPatientOptions']);

      /*vitualpsettings ={
               email :{view :true,required : true},
               firstName : {view :true,required : true},
               lastName : {view :true,required : true},
               dob : {view :true,required : false},
               cellNumber : {view :true,required : true},
               zipCode : {view :true,required : true},
       }*/
      var options;
      if (vitualpsettings.cellNumber.view && vitualpsettings.cellNumber.required) {
        this.iscellNumber = true;
        this.newPatient.addControl("pcellno", new FormControl(null, Validators.required));
      }
      else if (vitualpsettings.cellNumber.view && !vitualpsettings.cellNumber.required) {
        this.iscellNumber = true;
        this.newPatient.addControl("pcellno", new FormControl(null));
      }


      if (vitualpsettings.email.view && vitualpsettings.email.required) {
        this.isEmail = true;
        this.isReqEmail = true;
        this.newPatient.addControl("pemail", new FormControl(null, [Validators.required, Validators.pattern(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/)]));
      }
      else if (vitualpsettings.email.view && !vitualpsettings.email.required) {
        this.isEmail = true;
        this.newPatient.addControl("pemail", new FormControl(null, Validators.pattern(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/)));

      }

      if (vitualpsettings.firstName.view && vitualpsettings.firstName.required) {
        this.isFirstName = true;
        this.isReqFirstName = true;
        this.newPatient.addControl("pfname", new FormControl(null, Validators.required));
      }
      else if (vitualpsettings.firstName.view && !vitualpsettings.firstName.required) {
        this.isFirstName = true;
        this.newPatient.addControl("pfname", new FormControl(null));
      }
      if (vitualpsettings.lastName.view && vitualpsettings.lastName.required) {
        this.isLastName = true;
        this.isReqLastName = true;
        this.newPatient.addControl("plname", new FormControl(null, Validators.required));
      }
      else if (vitualpsettings.lastName.view && !vitualpsettings.lastName.required) {
        this.isLastName = true;
        this.newPatient.addControl("plname", new FormControl(null));

      }
      if (vitualpsettings.zipCode.view && vitualpsettings.zipCode.required) {
        this.isZipCode = true;
        this.isReqZipCode = true;
        this.newPatient.addControl("zipcode", new FormControl(null, Validators.required));
      }
      else if (vitualpsettings.zipCode.view && !vitualpsettings.zipCode.required) {
        this.isZipCode = true;
        this.newPatient.addControl("zipcode", new FormControl(null));

      }
      if (vitualpsettings.dob.view && vitualpsettings.dob.required) {
        this.isDob = true;
        this.isReqDob = true;
        this.newPatient.addControl("dday", new FormControl(null, Validators.required));
        this.newPatient.addControl("dmonth", new FormControl(null, Validators.required));
        this.newPatient.addControl("dyear", new FormControl(null, Validators.required));
      }
      else if (vitualpsettings.dob.view && !vitualpsettings.dob.required) {
        this.isDob = true;
        this.newPatient.addControl("dday", new FormControl(null));
        this.newPatient.addControl("dmonth", new FormControl(null));
        this.newPatient.addControl("dyear", new FormControl(null));

      }



      /* if (data['signatureRequestTypesDisplay'].length) {
         this.signatureTypes = data['signatureRequestTypesDisplay'];
 
         this.mdlDocType = null;
         $(".selectd").attr("disabled", false);
         $('.selectd').select2({
           placeholder: "Select Document Type"
        });
       }
       else {
         //this.isData=true; 
       }*/
    });
    /*for addng new on the fly patients ends*/

    this.newPatient = new FormGroup({
    });
    var $eventSelect = $("#assosiatedPatients");
    $eventSelect.select2();
    var self = this;
    $eventSelect.on("change", function (e) {
      var selectedPatient = $("#assosiatedPatients").val();
      selectedPatient = JSON.parse(selectedPatient);
      if (typeof (selectedPatient) == "object") {
        self.selectedAssosiatePatient = selectedPatient.id;
      } else {
        self.selectedAssosiatePatient = $("#assosiatedPatients").val();
      }
      //self.selectedAssosiatePatient = $("#assosiatedPatients").val();
      self.setIframeUrl();
    });
    /*this.SignService.getAllTenantUser().then((result) => {
      this.tenantUsers = result
      this.tenantUsers = this.tenantUsers.filter(function(user){
        return user.roleId == 3 && user.status!=7;
      })
    });*/
    this.structuredFormSend = this._formBuild.group({
      tenantUsers: [''],
      assosiatedPatients: [''],
      message: ['']
    });
    //aparna
    //  this._formsService.getAllFormNames({"tenantId":this._structureService.getCookie('tenantId'),"roleId":this.userData.roleId}).then((result) => {
    this._structureService.getSupplyCountForms(timezone.name(), this.userData.roleId).then((res) => {
      this.forms = res['getSessionTenant'] ? JSON.parse(JSON.stringify(res['getSessionTenant']['supplyForms'])) : [];
      // this.forms = result;
      this.goToForm(this.forms[0]);
      if (this.dTable) {
        this.dTable.destroy();
      }
      var isTrue = false;
      if (this.forms.length > 99) {
        isTrue = true;
      }
      /*this.dTable = $('#form-send-dt').DataTable({
        autoWidth: false,
        order: [[ 1, "desc" ]],
        responsive: true,
        retrieve: true,
        pagination: true,
        serching: true,  
        paging: isTrue,
        bInfo: isTrue,
        lengthMenu: [[100, 250, 500, 1000, -1], [100, 250, 500, 1000, 'All']],            
        data: this.forms,
        fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
          $(nRow).on('click', () => {
            this.activeForms = aData;
            // console.log(nRow, aData, iDisplayIndex, iDisplayIndexFull);
          });
        },
        columns: [
          {title: "#"},
          {title: "Form Name", data: 'name'},
          {title: "Action"}
        ],
        columnDefs: [
        {
          data: null,
          orderable: false,
          width: "10%",
          targets: 0
         },
         { 
           data: null,
           targets: 1,
           width: "80%",
         },
        {
            data: null,
            orderable: false,
            render: function (forms, type, row) {                 
            let actions = '<div class="btn-group mr-2 mb-2 no-margin" aria-label="" role="group">';
            actions += `<a data-view class="pull-right btn btn-sm" title="View"><i data-view class="icmn-eye"></i></a>`;
            // actions += `<a data-download class="pull-right btn btn-sm" title="Send"><i data-download class="icmn-forward"></i></a>`;
            actions += `</div>`
            return actions;
          },
          width: "10%",
          targets: -1
        }
      ]
      });*/

      /*this.dTable.on( 'order.dt search.dt', () => {
        this.dTable.column(0, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
            cell.innerHTML = i+1;
        });
      }).draw();
      this.dataLoadingMsg = false;*/
    }).catch((ex) => {
    });
  }
  ngOnDestroy() {
    if (this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
    /**Unsubscribe all the socket event subscriptions */
    this.socketEventSubscriptions.forEach(subscription => {
      if(subscription) subscription.unsubscribe();
    });
  }
  showFormPriview() {
    $('#priviewForm').modal('show');
  }
  sendForm() {
    var recipients = $('#tenantUsers').val();
    const selectedRecipientsPolling = [];
    const selectedRecipients = [];
    if (recipients.length) {
      console.log(this.tenantUsers, "this.tenantUsers");
      recipients = recipients.map(element => {
        var id = element.substr(element.indexOf(":") + 1);
        id = id.replace(/'/g, "");
        return id.replace(/\s/g, '');
      });
      let message = this.structuredFormSend.value['message'].trim();
      // console.log(selectedRecipients,"selectedRecipients");
      var data = {
        form_id: this.activeForms.id,
        recipients: recipients.filter(a => a.indexOf('tag-') === -1),
        tagRecipients: recipients.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join(),
        userId: this.userData.userId,
        message: message
      };
      console.log('@@@@@@@@@@@@@@@@@@@@@@');
      console.log(data);

      var deepLinking = {
        "pushType": "",
        "state": "eventmenu.supply",
        "stateParams": {}
      };
      // console.log(data);
      let pushMessage = "New Supply to fill out";
      this._formsService.sendSupply(data).then((result: any) => {
        if (result.send_to) {
          result.send_to.forEach(element => {
            const data = {
              userid: element,
              senderId: this.userData.userId,
              organizationMasterId: this.userData.organizationMasterId
            }
            console.log(data)
            selectedRecipientsPolling.push(data);
            selectedRecipients.push(element);
          });
        }
        console.log(selectedRecipientsPolling, "selectedRecipientsPollingselectedRecipientsPolling");
        this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", selectedRecipientsPolling, 'form');//Need to commnd after finish form inbox
        this._structureService.socket.emit("sendFormsToRecipients", selectedRecipientsPolling);
        this.result = result;

        /*patient reminder for form starts*/
        console.log(this.userData.config.patient_reminder_time * 1, this.userData.config.patient_reminder_types);
        var otherDatas = {
          patientReminderTime: this.userData.config.patient_reminder_time * 1,
          patientReminderTypes: this.userData.config.patient_reminder_types,
          messageReplyTimeout: this.userData.config.message_reply_timeout,
          sentId: this.result.sentId,
          senderName: this.userData.displayName,
          tenantId: this.userData.tenantId,
          tenantName: this.userData.tenantName,
          serverBaseUrl: this._structureService.serverBaseUrl,
          apiVersion: this._structureService.version,
          message: "[Reminder] You have new supply to fillout",
          formReminderType: this.userData.config.patient_reminder_checking_type,
          environment: this._structureService.environment
        }
        console.log(otherDatas, "otherDatas");
        //this._structureService.socket.emit("ReminderForForm", selectedRecipientsPolling, otherDatas);
        this._structureService.reminderForForm(selectedRecipientsPolling, otherDatas);
        /*patient reminder for form ends*/
        this._structureService.sentPushNotification(selectedRecipientsPolling, 0, pushMessage, '', deepLinking, '', '');
        if (this.result.status == 1) {
          var activityData = {
            activityName: "Send Supply Form Success",
            activityType: "structured forms",
            activityDescription: this.userData.displayName + " successfully sent form " + this.activeForms.name + " (" + this.activeForms.id + ") to " + selectedRecipients.toString()
          };
          this._structureService.trackActivity(activityData);
          this._structureService.notifyMessage({
            messge: this.activeForms.name + ' has been sent successfully',
            delay: 1000,
            type: 'success'
          });
          //$('#tenantUsers').val(null).trigger('change');
          $("#tenantUsers").select2("val", " ");
          // $("#message").val("");
          this.structuredFormSend.patchValue({
            message: ''
          });
        } else {
          var activityData = {
            activityName: "Send Supply Form Fail",
            activityType: "structured forms",
            activityDescription: this.userData.displayName + " form " + this.activeForms.name + " (" + this.activeForms.id + ") to " + selectedRecipients.toString() + " sending failed"
          };
          this._structureService.trackActivity(activityData);
          this._structureService.notifyMessage({
            messge: this.activeForms.name + ' sending failed',
            delay: 1000
          });
        }
      });
    } else {
      this._structureService.notifyMessage({
        messge: "Please choose Recipient(s)",
        delay: 1000
      });
      console.log(recipients);
    }
  }

  goToForm(form) {


    $('#assosiatedPatients').select2({
      placeholder: "Select Associated Patient",
      allowClear: true
    });
    this.structuredFormSend.patchValue({
      assosiatedPatients: ''
    });
    // this.selected = form;
    this.selectedAssosiatePatient = '';
    this.activeForms = form;

    this.allowRecipientRoles = this.activeForms.category == 'patientsupplyCount' ? true : false;
    this.patientAssociation = this.activeForms.patientAssociation

    console.log("==============================")
    console.log("==============================")
    console.log(this.patientAssociation)
    console.log("==============================")
    console.log("==============================")
    console.log("==============================")
    console.log("==============================")
    console.log(this.allowRecipientRoles)
    console.log("==============================")
    console.log("==============================")
    //this.allowRecipientRoles = true;
    /*let tagMeta = JSON.parse(this.activeForms.tagMeta);
    tagMeta.forEach(element => {
      console.log(element);
      if(element.allowRecipientRoles == false){
        this.allowRecipientRoles = false;
      }
    });*/
    var self = this;
    if (this.allowRecipientRoles) {
      this._formsService.getTenantUsersByRoleId(this._structureService.getCookie('tenantId'), this.activeForms.tagId, this.userData.roleId, false).then((result: any) => {
        this.tenantUsers = result.filter((result) => {
          if (result.status == 1 && result.userid != self.userData.userId) {
            var date = "";
            if (result.caregiver_userid) {
              if (result.caregiver_dob) {
                date = result.caregiver_dob;
                var dobDay = new Date(date).getDay();
                if (date && !isNaN(dobDay)) {
                  date = date.replace(/-/g, '/');
                  try {
                    date = this._datePipe.transform(date, 'MM/dd/yyyy');
                  }
                  catch (e) {
                    date = '';
                  }
                } else {
                  date = "";
                }
              } else {
                date = "";
              }
              result.caregiver_dob_formatted = date;
            } else {
              if (result.dob) {
                date = result.dob;
                var dobDay = new Date(date).getDay();
                if (date && !isNaN(dobDay)) {
                  date = date.replace(/-/g, '/');
                  try {
                    date = this._datePipe.transform(date, 'MM/dd/yyyy');
                  }
                  catch (e) {
                    date = '';
                  }
                } else {
                  date = "";
                }
              } else {
                date = "";
              }
              result.dob_formatted = date;
            }
            return true;
          } else {
            return false;
          }
        });
        this.tenantUsers = this.patientTags.concat(this.tenantUsers);
      }).catch((ex) => { });
    } else {
      this.userService.getAssociatedPatientsLists(form).subscribe((result: any) => {
        this.assosiatedPatients = result.filter((result) => {
          let date = "";
          if (result.caregiver_dob) {
            date = result.caregiver_dob;
            let dobDay = new Date(date).getDay();
            if (date && !isNaN(dobDay)) {
              date = date.replace(/-/g, '/');
              try {
                date = this._datePipe.transform(date, 'MM/dd/yyyy');
              }
              catch (e) {
                date = '';
              }
            } else {
              date = "";
            }
          } else {
            date = "";
          }
          result.caregiver_dob_formatted = date;
          date = "";
          if (result.dob) {
            date = result.dob;
            let dobDay = new Date(date).getDay();
            if (date && !isNaN(dobDay)) {
              date = date.replace(/-/g, '/');
              try {
                date = this._datePipe.transform(date, 'MM/dd/yyyy');
              }
              catch (e) {
                date = '';
              }
            } else {
              date = "";
            }
          } else {
            date = "";
          }
          result.dob_formatted = date;
          return true;
        });
        this.tenantUsers = result;
      });
    }

    this.setIframeUrl();
    /*var formsUrl = this._structureService.serverBaseUrl+'/machform/Upload/embed.php?id='+this.activeForms.id;
    var iFrameContent='<iframe onload="javascript:parent.scrollTo(0,0);" allowTransparency="true" class="structured-form-dekstop" frameborder="0" scrolling="no" style="width:100%;border:none" src="'+formsUrl+'" ></iframe>';
    this.formContent = this.sanitizer.bypassSecurityTrustHtml(iFrameContent);*/
  }
  setIframeUrl() {
    var clientId = this._structureService.socket.io.engine.id;
    var tokenId = this._structureService.getCookie('authenticationToken');
    var facing_new = this.activeForms.facing_new;

    //var formsUrl = this._structureService.serverBaseUrl+'/machform/Upload/embed.php?id='+this.activeForms.id;
    var enable_sftp_integration=this.userDataConfig.enable_sftp_integration==1?"&enable_sftp_integration="+true:"&enable_sftp_integration="+false;
    var unique="&uniqueFormIdentity="+Guid.create();
    var confirmActionPrefill=this.activeForms.confirmActionPrefill==1?"&confirmActionPrefill=true":"&confirmActionPrefill=false";
    var fax_queue_show_warning=this.userDataConfig.fax_queue_show_warning == 1?"&fax_queue_show_warning="+true+unique+confirmActionPrefill:"&fax_queue_show_warning="+false+unique+confirmActionPrefill;
    if (this.selectedAssosiatePatient) {
      var formsUrl = this._structureService.machFormUrl + APIs.machFormEmbed + '?id=' + this.activeForms.id + "&patientId=" + this.selectedAssosiatePatient + "&loginUserId=" + this.selectedAssosiatePatient + "&tenantId=" + ((this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')) ? this._structureService.getCookie('crossTenantId') : this._structureService.getCookie('tenantId')) + "&toId=" + this.userData.userId + "&sentId=null&environment=" + this._structureService.environment + "&formName=" + encodeURIComponent(this.activeForms.name) + "&formId=" + this.activeForms.id + "&apiVersion=" + this._structureService.version + "&toName=" + this.userData.displayName + "&fromName=" + this.userData.displayName + "&serverBaseUrl=" + encodeURIComponent(this._structureService.serverUrlCustom) + "&staffFacing=staffsupplyCount&fromMob=false&isWorkingHour=" + this._inboxService.checkInfusionHours(true).isWorkingHours + "&clientId=" + clientId + "&authenticationToken=" + tokenId + "&facing_new=" + facing_new+enable_sftp_integration+fax_queue_show_warning;

    }
    else {
      var formsUrl = this._structureService.machFormUrl + APIs.machFormEmbed + '?id=' + this.activeForms.id + "&patientId=" + this.userData.userId + "&loginUserId=" + this.userData.userId + "&tenantId=" + ((this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')) ? this._structureService.getCookie('crossTenantId') : this._structureService.getCookie('tenantId')) + "&toId=" + this.userData.userId + "&sentId=null&environment=" + this._structureService.environment + "&formName=" + encodeURIComponent(this.activeForms.name) + "&formId=" + this.activeForms.id + "&apiVersion=" + this._structureService.version + "&toName=" + this.userData.displayName + "&fromName=" + this.userData.displayName + "&serverBaseUrl=" + encodeURIComponent(this._structureService.serverUrlCustom) + "&staffFacing=staffsupplyCount&fromMob=false&isWorkingHour=" + this._inboxService.checkInfusionHours(true).isWorkingHours + "&clientId=" + clientId + "&authenticationToken=" + tokenId + "&facing_new=" + facing_new+enable_sftp_integration+fax_queue_show_warning;

    }

    // var formsUrl = this._structureService.serverBaseUrl+'/machform/Upload/embed.php?id='+this.activeForms.id+"&patientId="+this.selectedAssosiatePatient+"&loginUserId="+this.selectedAssosiatePatient+"&tenantId="+this._structureService.getCookie('tenantId')+"&toId="+this.userData.userId+"&sentId=null&environment="+this._structureService.environment+"&formName="+encodeURIComponent(this.activeForms.name)+"&formId="+this.activeForms.id+ "&apiVersion="+this._structureService.version+"&toName="+this.userData.displayName+"&fromName="+this.userData.displayName+"&serverBaseUrl="+encodeURIComponent(this._structureService.serverBaseUrl)+"&staffFacing=staffsupplyCount&fromMob=false&isWorkingHour="+this._inboxService.checkInfusionHours(true).isWorkingHours;
    /*var iFrameContent='<iframe onload="javascript:parent.scrollTo(0,0);" allowTransparency="true" class="structured-form-dekstop" frameborder="0" scrolling="no" style="width:100%;border:none" src="'+formsUrl+'" ></iframe>';
    this.formContent = this.sanitizer.bypassSecurityTrustHtml(iFrameContent);*/
    this.formContent = this.sanitizer.bypassSecurityTrustResourceUrl(formsUrl);
  }
  selectAssosiatePatient() {
    console.log("selectAssosiatePatient : rrrrrrrrrr")
    this.selectedAssosiatePatient = $("#assosiatedPatients").val();
    console.log(this.selectAssosiatePatient);
    this.setIframeUrl();

  }
  openAssocpopup() {

    $('#assocModal').modal({ backdrop: 'static', keyboard: false });
    // $('#assocModal').modal('show');
  }

  closeAssocpopup() {
    $('#assocModal').modal('hide');
  }
  createNewAssoc(form) {
    if (!form.valid) {
      return false;
    }
    form = form.value;
    this.closeAssocpopup();
    this.popupmsg = "Saving your details...";
    NProgress.start();
    var data = { "email": form.pemail, "firstname": form.pfname, "lastname": form.plname, "dob": $('#dob-date-picker').val(), "cell": form.pcellno, "zipcode": form.zipcode, "tenantId": this.userData.tenantId, "operation": 0 }
    this._structureService.createAssocpatient(data).subscribe((res) => {
      NProgress.done();
      var result = JSON.parse(res.text());
      if (result.status == 1 && result.already == true && result.message == 1) {
        var notify = $.notify('Sorry! Patient already created.');
        setTimeout(() => {
          notify.update({ 'type': 'warning', 'message': '<strong>Sorry! Patient already created.</strong>' });
          this.openAssocpopup()
        }, 1000);

      }
      else if (result.status == 1 && result.already && result.message == 0) {
        var notify = $.notify('Sorry! Email Id is already exists for another user.');
        setTimeout(() => {
          notify.update({ 'type': 'warning', 'message': '<strong>Sorry! Email Id is already exists for another user.</strong>' });
          this.openAssocpopup()
        }, 1000);

      } else if (result.status == 1 && result.already == false) {
        $('#resetpform').trigger('click');
        $('.month, .day, .year').select2();
        $('.month').on('select2:select', (e) => {
          this.assocpdata.dmonth = $(e.target).val();
          $('.day').select2('open');
        }); $('.day').on('select2:select', (e) => {
          $('.year').select2('open');
          this.assocpdata.dday = $(e.target).val();
        });
        $('.year').on('select2:select', (e) => {
          this.assocpdata.dyear = $(e.target).val();
        });

        var virtualPatientObject = {
          id: result.userId,
          cmisId: 0,
          displayName: form.pfname + " " + form.plname,
          avatar: ''
        }

        var newOption = new Option(virtualPatientObject.displayName, '{"displayName":"' + virtualPatientObject.displayName + '","id":"' + virtualPatientObject.id + '","cmisId":"' + virtualPatientObject.cmisId + '","avatar":"' + virtualPatientObject.avatar + '"}', true, true);
        $("#assosiatedPatients").append(newOption).trigger('change');
        var notify = $.notify('Success! Patient created.');
        setTimeout(function () {
          notify.update({ 'type': 'success', 'message': '<strong>Success! Patient created.</strong>' });
        }, 1000);

        var activityData = {
          activityType: "user creation",
          activityName: "On the Fly Patient Registration",
          activityDescription: "New on the fly user registered (" + virtualPatientObject.displayName + " " + data.dob + ") by " + this.userData.displayName + " (" + this.userData.userId + ")"
        };
        this._structureService.trackActivity(activityData);
      }
      else if (result.status == 3 && result.already == true && result.message == 1) {
        var virtualPatientAllreadyExistForms = "already exists. Please confirm whether we can associate this patient?";
        var self = this;
        swal({
          title: "Are you sure?",
          text: form.pfname + " " + form.plname + " " + virtualPatientAllreadyExistForms,
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default",
          confirmButtonClass: "btn-warning",
          confirmButtonText: "Ok",
          closeOnConfirm: true
        },
          function (isConfirm) {
            if (isConfirm) {
              self.structuredFormSend.patchValue({
                assosiatedPatients: result.userId
              });
              $("#assosiatedPatients").trigger('change');
            }
          });
      } else {
        var activityData = {
          activityType: "user creation",
          activityName: "On the Fly Patient Registration failed",
          activityDescription: "New on the fly user registeration failed (" + virtualPatientObject.displayName + " " + data.dob + ") by " + this.userData.displayName + " (" + this.userData.userId + ")"
        };
        this._structureService.trackActivity(activityData);
        var notify = $.notify('Sorry! We can\'t save your data, Please try again.');
        setTimeout(() => {
          notify.update({ 'type': 'danger', 'message': '<strong>Sorry! We can\'t save your data, Please try again.</strong>' });
          this.openAssocpopup()
        }, 1000);
      }
    });
  }


  

 
}
