import { Component, OnInit, Input, On<PERSON><PERSON>roy } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { DomSanitizer } from '@angular/platform-browser';
import { isBlank } from 'app/utils/utils';
import { APIs } from 'app/constants/apis';
import { CONSTANTS } from 'app/constants/constants';
import { Location } from '@angular/common';
import { Guid } from "guid-typescript";
import { StructureService } from '../structure.service';
import { FormsService } from './forms.service';
import { SharedService } from '../shared/sharedServices';
import { InboxService } from '../inbox/inbox.service';
import { FormpPipe } from './formp.pipe';
import { Subscription } from 'rxjs';

const jstz = require('jstz');

declare const $: any;
declare const swal: any;
const moment = require('moment/moment');

@Component({
  selector: 'edit-form',
  templateUrl: 'edit_forms.html'
})
export class editFormComponent implements OnInit, OnDestroy {
  // check form stabilization
  recipientsetArray:any;
  messageadd:any;
  // check form stabilization
  structureFormContent: any = '';
  formContent: any = '';
  userData;
  fromArchive: any = false;
  formId;
  entryId;
  autoSave;
  loadingFlag = false;
  checkDraft = false;
  checkFacing = false;
  facingvalue: any;
  patientAssociation: any
  savaAsDraftStaff;
  savaAsDraftPatient;
  userDataConfig: any;
  activeForm;
  tenantUsers: any;
  practitionerName: any = null;
  recepientName: any = null;
  formSubmissionId: any;
  result: any;
  disableTimer: any;
  editFormId;
  toName;
  @Input() editDetails: any;
  hideBackBtn = false;
  completedFormRecipients: string;
  private socketEventSubscriptions: Subscription[] = []

  constructor(
    private _structureService: StructureService,
    private _inboxService: InboxService,
    private sanitizer: DomSanitizer,
    private router: Router,
    private route: ActivatedRoute,
    private _formsService: FormsService,
    public _location: Location,
    private _sharedService: SharedService,
    private formpPipe: FormpPipe
  ) {
    route.params.subscribe(val => {
      this.formId = this.route.snapshot.params['formId'];
      this.entryId = this.route.snapshot.params['entryId'];
    });
    this._sharedService.crossTenantChange.subscribe(
      (onInboxData) => {
        this.ngOnInit();
      }
    );
    this._sharedService.crossTenantChange.subscribe((onInboxData) => {
      if ((this.router.url.indexOf('forms/form-tags') > -1)) {
        this.ngOnInit();
      }
    });
  }
  ngOnDestroy() {
    window.removeEventListener("message", this.disableTimer, false);
    /**Unsubscribe all the socket event subscriptions */
    this.socketEventSubscriptions.forEach(subscription => {
      if(subscription) subscription.unsubscribe();
    });
  }
   notify(){

     // var intervalast =  setInterval(()=>{ 
      var resultlast = $("#response").val();
      if(resultlast != ""){
       //  clearInterval(intervalast);
         this.sendForm();
         
      }else{
        this._structureService.notifyMessage({
          messge: 'Form sending failed',
          delay: 1000
        });
      }
     //}, 2000);
    
  }
  failNotifyEdit(){
    $("#textmessage").text("");
    $("#newloader").hide();
    this._structureService.notifyMessage({
      messge: 'Form sending failed',
      delay: 1000
    });
  }
  // check form stabilization
  onBlurEvent(event: any){
   this.messageadd = event.target.value.trim();
    const iframe = document.getElementById('structured-form-dekstop') as HTMLIFrameElement;
    const contentWindow = iframe.contentWindow;
    var postData: any = {
      "messagewithform": this.messageadd,
      "sendviaPostmessage":1
    };
    contentWindow.postMessage(postData, '*');
   //this.setIframeUrl();
  };
  // check form stabilization 
  ngOnInit() {

  //save as draft msg
   $(".saveAsDraft-message").removeClass('faildraft');

   var datacheck: any = {
          formid:  (this.editDetails && this.editDetails.formId) ? this.editDetails.formId : this.formId,
          submissionid: (this.editDetails && this.editDetails.entryId) ? this.editDetails.entryId : this.entryId,
        };
      this._formsService.detailsDraftForm(datacheck).then((data: any) => {
        if(data.length > 0) {
          let monthdate = "";
          if(new Date(parseInt(data[0]? data[0].modifiedAT: '') * 1000).toDateString() === new Date().toDateString()){
            monthdate =  moment((moment().unix()) * 1000).format('MMM DD')+" ";
          }
            $("#last_save_draft").val(monthdate+this.formpPipe.transform(parseInt(data[0].modifiedAT) * 1000))
            $("#error_save_draft").val(monthdate+this.formpPipe.transform(parseInt(data[0].modifiedAT) * 1000))
        }
       });

  //save as draft msg

    this.autoSave = 0;
    this.savaAsDraftStaff = 0;
    this.savaAsDraftPatient = 0;
    
    this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
    if (this.userDataConfig.enable_form_auto_save == 1) {
      this.autoSave = 1;
    }
    this.socketEventSubscriptions.push(
      this._structureService.subscribeSocketEvent('submissionId').subscribe((data) => {
      })
    );
    this.socketEventSubscriptions.push(
    this._structureService.subscribeSocketEvent('submittedData').subscribe((data) => {
      var activityData = {
        activityName: "Submit Form " + this._sharedService.myFormsType,
        activityType: "forms",
        activityDescription: this.userData.displayName + ' has completed staff facing form ' + data.formData.formName + ' (' + data.formData.form_id + ')'
      };

      this._structureService.trackActivity(activityData);
      var formData = {
        "userid": this.userData.userId,
        "tenantId": data.formData.tenant_id,
        "recipient_id": data.formData.recipient_id,
        "formId": data.formData.form_id,
        "staffFacing": data.formData.staffFacing,
        "form_submission_id": data.formData.submissionId,
        "form_id": data.formData.form_id,
        "form_name": data.formData.formName,
        "patient_id": "",
        "id": "",
        "patientUser": this.activeForm.patient_id,

      }
      if (data.formData.submissionId) {
        $("#textmessage").text("");
$("#newloader").hide();
        var successMessage=data.formData.form_Success_message;
        if (this._sharedService.myFormsType == "DRAFTS") {

          this._formsService.filingCenterSubmittedFormdata(formData).then((data) => {
            var notifyResult: any = data;
            if (notifyResult.defaultFromFilingCenterSubmitjson && this.userDataConfig.enable_progress_note_integration == 1) {
              if (notifyResult.defaultFromFilingCenterSubmitjson != false && (notifyResult.identity_value_Patient != ''
                || notifyResult.identity_value_Patient != null)
                && notifyResult.patientAssociation == true
                && notifyResult.CPRUSERNO != '' || notifyResult.CPRUSERNO != null
                && notifyResult.CPRUSERNAME != '' || notifyResult.CPRUSERNAME != null) {
                activityData.activityName = "Form Copied to Filing Center as JSON";
                activityData.activityType = "structured forms";
                activityData.activityDescription = this.userData.displayName + " archived form " + formData.form_name + " (formId " + formData.formId + ") successfully and copied the JSON file to filing center named " + notifyResult.defaultFromFilingCenterSubmitjson;
                this._structureService.trackActivity(activityData);

              }
              if (notifyResult.defaultFromFilingCenterSubmitjson != false && notifyResult.identity_value_Patient == ''
                || notifyResult.identity_value_Patient == null
                || notifyResult.patientAssociation == false
                || notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null
                || notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null) {
                activityData.activityName = "Failed Form Copy to Filing Center as JSON";
                activityData.activityType = "structured forms";
                var messageData = '';
                var messageData1 = '';
                if (notifyResult.patientAssociation == false && notifyResult.staffFacing == "true") {
                  messageData1 = "there is no patient associated with this form";
                }
                if ((notifyResult.identity_value_Patient == '' || notifyResult.identity_value_Patient == null) && notifyResult.patientAssociation == true) {
                  messageData += ", MRN";
                  if (notifyResult.patient_name && notifyResult.patient_name != "") {
                    messageData += " of " + notifyResult.patient_name;
                  }
                }
                let f1 = 0;
                let f2 = 0;
                if (notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null) {
                  f1 = 1;
                  messageData += ", USERNO";
                }
                if (notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null) {
                  f2 = 1;
                  messageData += ", USERNAME";
                }
                if ((f1 == 1 || f2 == 1) && notifyResult.staff_name && notifyResult.staff_name != "") {
                  messageData += " of " + notifyResult.staff_name;
                }
                if (notifyResult.patientAssociation == false && notifyResult.staffFacing == "false") {
                  messageData += "";
                }
                var removeComa = messageData.charAt(0);
                if (removeComa == ',') {
                  messageData = messageData.substring(1);
                }
                if (messageData1 && messageData) {
                  var finalMessage = 'Generating Progress Note for Form ' + formData.form_name + ' failed due to ' + messageData1 + ' and missing (' + messageData + ')';
                }
                else if (messageData1) {
                  var finalMessage = 'Generating Progress Note for Form ' + formData.form_name + ' failed due to ' + messageData1;
                }
                else if (messageData) {
                  var finalMessage = 'Generating Progress Note for Form ' + formData.form_name + ' failed due to missing (' + messageData + ')';
                }
                else {
                  var finalMessage = '';
                }
                if (finalMessage) {
                  activityData.activityDescription = this.userData.displayName + " send form " + formData.form_name + " (formId " + formData.formId + ") failed to copy the JSON file to filing center named " + notifyResult.defaultFromFilingCenterSubmitjson + "because" + messageData;
                  this._structureService.trackActivity(activityData);
                  // this._structureService.notifyMessage({
                  //   messge: finalMessage,
                  //   delay: 6000,
                  //   type: 'warning'
                  // });
                }
              }
            }
          });
        }
        $("#textmessage").text("");
        $("#newloader").hide();
        swal({
          title: successMessage,
          text: '',
          icon: "success",
          showCancelButton: false,
          showConfirmButton: false,
          timer: 3000
         
        });
       
               //  this._structureService.notifyMessage({
        //             messge: successMessage,
        //             delay: 6000,
        //             type: 'success'
        //           });
      }
      if (data.formData.staffFacing == "true" && !this._structureService.pahEdit) {
        if (this.userDataConfig.enable_collaborate_edit == 1) {
          this.router.navigate(['/forms/list']);
        } else {
          this.router.navigate(['/forms/worklist']);
        }
      }
     
    })
  );
  this.socketEventSubscriptions.push(
    this._structureService.subscribeSocketEvent('saveAsDraft').subscribe((data) => {
      if (data) {
      //save as draft msg
        if (this.userDataConfig.enable_collaborate_edit == 1) {
          var enable_collaborate_edit = "collaborateDraft-Enabled";
        } else {
          var enable_collaborate_edit = "";
        }
        var activityData = {
          activityName: "Form Saved As Draft from " + this.activeForm.formStatus + enable_collaborate_edit,
          activityType: "forms",
          activityDescription: this.userData.displayName + '(' + this.userData.userId + ') has saved the form as Draft- ' + this.activeForm.formName + ' (' + this.activeForm.formId + ') with submission id  - '+data.data.submissionId
        };
        this._structureService.trackActivity(activityData);

      }
    })
  )

    $(function () {
      if (this.editDetails) {
        this.formId = this.editDetails.formId;
        this.entryId = this.editDetails.entryId;
        this.userData.userId = this.editDetails.userId;
        this.activeForm.staff_facing = this.editDetails.staffFacing;
        this.activeForm.formName = this.editDetails.formName;
        this.activeForm.fromId = this.editDetails.fromId;
        this.toName = this.editDetails.toName;
        if(this.editDetails.roleId){
          this.userData.roleId = this.editDetails.roleId;
        }
        this.userData.displayName = this.editDetails.displayName;
        this.userData.tenantId = this.editDetails.tenantId;
        this.activeForm.fromUsername = this.editDetails.fromUsername;
        this.activeForm.sentId = this.editDetails.sentId;
        this.activeForm.formStatus = this.editDetails.formStatus;
        this.hideBackBtn = true;
     }
    });
    function showmessage() {
      var data = {
        messge: 'It is a good practice to save your work frequently. Please click Save As Draft to save your work and avoid any data loss if your session times out1111.',
        delay: 100000,
        type: 'success'

      };
      let notify = $.notify(data.messge);
      let type = data.type ? data.type : 'danger';
      let deley = data.delay ? data.delay : 1000;
      setTimeout(function () {
        notify.update({ 'type': type, 'message': '<strong>' + data.messge + '</strong>' });
      }, deley);
    }
    this.disableTimer = function (event) {
      try {
        if (event.data) {
          var height = (event.data).split("=");
          if (height.length && height[0] == "scrollTopformsubmit") {
            $(".structured-form-modal").scrollTop(0);
          }
          else if (height.length && height[0] == "gotocreatesendform") {
            $(".searchbar input").val("");
            $(".send-form-list ").find('.formname').first().trigger('click');
          } else if (height.length && height[0] == "saveasdraftidtodeleted") {
            localStorage.setItem('saveasdraftidtodeleted', height[1]);
          }
          else {
            if (typeof (parseInt(height[1])) == "number") {
              $("#structured-form-dekstop").height(height[1] + "px")
            } else {
              $("#structured-form-dekstop").height("5000px")
            }
            setTimeout(
              function () {
                $('#btm-send-form-list').removeAttr('disabled');
              }, 2000);
          }
        }
      } catch (error) {
      }
    }
    
    this.userData =  JSON.parse(this._structureService.userDetails);
    var forActivityLog = "&userTenant=" + this.userData.tenantId + "&crossTenantID=" + this.userData.crossTenantId + "&platform=" + this._structureService.platform + "&environment=" + this._structureService.environment + "&version=" + this._structureService.version;
    var formsUrl = this._structureService.machFormUrl + APIs.machFormIndex + '?fromCallBell=1&tenantId=' + this._structureService.getCookie("crossTenantId") + '&userId=' + this.userData.userId + forActivityLog;
    
    var formData = localStorage.getItem('formData');
    if (typeof (formData) != "undefined" && formData != '') {
      this.activeForm = JSON.parse(decodeURIComponent(formData));
    } else {
      if (typeof this._structureService.getCookie('formData') == 'object') {
        this.activeForm = this._structureService.getCookie('formData');
      } else {
        this.activeForm = JSON.parse(decodeURIComponent(this._structureService.getCookie('formData')));
      }
    }

    if (this.userDataConfig.enable_form_save_draft_patient == 1 && this.activeForm.enableSaveDraftPatient == 1) {
      this.savaAsDraftPatient = 1;
    }
    if (this.userDataConfig.enable_form_save_draft_staff == 1 && this.activeForm.enableSaveDraftStaff == 1) {
      this.savaAsDraftStaff = 1;
    }
    if (this.activeForm.facing_new == 0) {
      this.checkFacing = true;
    }
    this.facingvalue = this.activeForm.facing_new;
    this.patientAssociation = this.activeForm.patientAssociation;
    if (this.userDataConfig.enable_collaborate_edit == 1) {
      if (this.activeForm.patient_associated_id != this.activeForm.sender_id)
        this.patientAssociation = true;
    }
    if (this.activeForm.formStatus == "Draft") {
      this.checkDraft = true;
      if (this.activeForm.practitioner_firstname && this.activeForm.practitioner_firstname !== null && this.activeForm.practitioner_firstname !== '') {
        this.practitionerName = this.activeForm.practitioner_firstname + " " + this.activeForm.practitioner_lastname;
      }
      if (this.activeForm.recepient_firstname && this.activeForm.recepient_firstname !== null && this.activeForm.recepient_firstname !== '') {
        this.recepientName = this.activeForm.recepient_firstname + " " + this.activeForm.recepient_lastname;
      }
      if(!isBlank(this.activeForm.completedFormRecipients)){
        this.completedFormRecipients = this.activeForm.completedFormRecipients;
      }
      this.setIframeUrl();
      if (this.userDataConfig.enable_collaborate_edit == 1) {
        var enable_collaborate_edit = "collaborateDraft-Enabled";
      } else {
        var enable_collaborate_edit = "";
      }
      var activityData = {
        activityName: "Form view from Draft bucket" + enable_collaborate_edit,
        activityType: "forms",
        activityDescription: this.userData.displayName + '(' + this.userData.userId + ') has viewed the form from Draft bucket(Draft ID :'+ this.activeForm.form_submission_id+') - ' + this.activeForm.formName + ' (' + this.activeForm.formId + ')'
      };
      this._structureService.trackActivity(activityData);
    }
    else {
      this.setFormUrl();
    }
  }
  validateEmail(email) {
    var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if(re.test(email) && email.indexOf('unknown_') == -1){      
      return true;
    } else {
      return false;
    }
  }
  sendForm() {
    var formSendMode = '';
    var applessMode = '';
    if(this.activeForm.applessMode == 1) {
      formSendMode = 'appless';
      applessMode = 'both';
    }
    
    var deepLinking = {
      "pushType": "",
      "state": "eventmenu.forms",
      "stateParams": {},
      "tenantId": this.userData.tenantId,
      "tenantName": this.userData.tenantName,
      "formSendMode": formSendMode,
      "applessMode" : applessMode,
      "sentId": ""
    };
    var recipients = [];
    const selectedRecipientsPolling = [];
    const selectedRecipients = [];
    if(this.activeForm.tag_meta){
      var  tagMeta=this.activeForm.tag_meta.replace(/\\/g, '');
      tagMeta=JSON.parse(tagMeta);
    }

    if (this.activeForm.caregiver) {
      recipients.push(this.activeForm.caregiver + '--' + this.activeForm.patient_id);
    } else {
      recipients.push(this.activeForm.patient_id);
    }
    
    let message = $("#draftArea").val().trim();
    if (this.activeForm.caregiver) {
    var data: any = {
      form_id: this.activeForm.form_id,
      recipients: recipients.filter(a => a.indexOf('tag-') === -1),
      tagRecipients: "",
      userId: this.userData.userId,
      message: message,
      formSubmissionId: this.formSubmissionId
    };
  }
  else{
    var data: any = {
      form_id: this.activeForm.form_id,
      recipients: recipients,
      tagRecipients: "",
      userId: this.userData.userId,
      message: message,
      formSubmissionId: this.formSubmissionId
    };
  }

    if(formSendMode && formSendMode == 'appless') {
      data.formSendMode = 'appless';
      data.applessMode = applessMode;
    }

    data.savedDraftID = this.activeForm.form_submission_id;
    if (this.activeForm.facing_new == 2) {

      var pract = [];
      pract.push(this.activeForm.practitionerid);
      data.recipients = pract;
      if(tagMeta.patientAssociation!=false){
        data.practitionerAssociatePatient = recipients;
      }
      
      data.practitioner = 1;
      data.orderChange = false;
      data.savedDraftID = this.activeForm.form_submission_id;
    }
    let pushMessage = "New Form to fill out";
    this._formsService.getTenantUsersByRoleId(this._structureService.getCookie('tenantId'), this.activeForm.tagid, this.userData.roleId, false).then((result: any) => {
      // this.tenantUsers = result;
      this.tenantUsers = result.filter((result) => {
        if (result.status == 1 && result.userid != this.userData.userId) {
          var date = "";
          if (result.caregiver_userid) {
            if (result.caregiver_dob) {
              date = result.caregiver_dob;
              var dobDay = new Date(date).getDay();
              if (date && !isNaN(dobDay)) {
                date = date.replace(/-/g, '/');

                try {
                  // date = this._datePipe.transform(date, 'MM/dd/yyyy');
                  date = moment(date).format('MM/DD/YYYY');
                }
                catch (e) {
                  date = '';
                }
              } else {
                date = "";
              }
            } else {
              date = "";
            }
            result.caregiver_dob_formatted = date;
          } else {
            if (result.dob) {
              date = result.dob;
              var dobDay = new Date(date).getDay();
              if (date && !isNaN(dobDay)) {
                date = date.replace(/-/g, '/');

                try {
                  // date = this._datePipe.transform(date, 'MM/dd/yyyy');
                  date = moment(date).format('MM/DD/YYYY');
                }
                catch (e) {
                  date = '';
                }
              } else {
                date = "";
              }
            } else {
              date = "";
            }
            result.dob_formatted = date;
          }
          result.option_text = (result.caregiver_displayname) ? ((result.dob_formatted) ? (result.displayname + ' - ' + result.dob_formatted + ' (' + result.caregiver_displayname + ')') : (result.displayname + ' (' + result.caregiver_displayname + ')')) : ((result.dob_formatted) ? (result.displayname + ' - ' + result.dob_formatted) : result.displayname);
          return true;
        } else {
          return false;
        }
      });
      this.tenantUsers = this.tenantUsers;
    }).then(function () {
    })
      var interval =  setInterval(()=>{ 
       var resultnew = $("#response").val();
      if(resultnew != ""){
         clearInterval(interval);
      }
      if(resultnew != ""){
                      var result = JSON.parse(resultnew);
      if (result.send_to) {
        result.send_to.forEach(element => {
          const data = {
            userid: element,
            senderId: this.userData.userId,
            organizationMasterId: this.userData.organizationMasterId,
            formSendMode:formSendMode,
            applessMode: applessMode,
            sentId:result.sentId
          }
          selectedRecipientsPolling.push(data);
          selectedRecipients.push(element);
        });
      }
      deepLinking.sentId = result.sentId;
      this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", selectedRecipientsPolling, 'form');//Need to commnd after finish form inbox
      this._structureService.socket.emit("sendFormsToRecipients", selectedRecipientsPolling);
      this.result = result;

      /*patient reminder for form starts*/
      var otherDatas = {
        patientReminderTime: this.userData.config.patient_reminder_time * 1,
        patientReminderTypes: this.userData.config.patient_reminder_types,
        messageReplyTimeout: this.userData.config.message_reply_timeout,
        sentId: this.result.sentId,
        senderName: this.userData.displayName,
        tenantId: this.userData.tenantId,
        tenantName: this.userData.tenantName,
        serverBaseUrl: this._structureService.serverBaseUrl,
        apiVersion: this._structureService.version,
        message: "[Reminder] You have new form to fillout",
        formReminderType: this.userData.config.patient_reminder_checking_type,
        environment: this._structureService.environment,
        formSendMode:formSendMode,
        applessMode : applessMode
      }
           
      this._structureService.reminderForForm(selectedRecipientsPolling, otherDatas);
      /*patient reminder for form ends*/
      const notificationData = {
        sourceId: CONSTANTS.notificationSource.form,
        sourceCategoryId: CONSTANTS.notificationSourceCategory.formSentNotification
      };
      this._structureService.sentPushNotification(selectedRecipientsPolling, 0, pushMessage, '', deepLinking, '', '', undefined, undefined, undefined, undefined, notificationData);
      
      if (this.userDataConfig.enable_collaborate_edit == 1) {
        var enable_collaborate_edit = "collaborateDraft-Enabled";
      } else {
        var enable_collaborate_edit = "";
      }
     

      if (this.result.status == 1) {

        var sentMessage = this.activeForm.form_name + ' has been sent successfully';
        var activityName = "Send Form Success from -" + this.activeForm.formStatus + enable_collaborate_edit;

        var activityDescription = this.userData.displayName + " successfully sent form " + this.activeForm.form_name + " (" + this.activeForm.form_id + ") to " + selectedRecipients.toString();

        if(this.userDataConfig.enable_appless_model == 1 && formSendMode == 'appless') {
          activityName = "Send Appless Form Success from -" + this.activeForm.formStatus + enable_collaborate_edit;
          var activityDescription = this.userData.displayName + " successfully sent Appless form " + this.activeForm.form_name + " (" + this.activeForm.form_id + ") to " + selectedRecipients.toString();
          
          if (this.activeForm.facing_new == 0) {
            if(this.activeForm.patient_country_code && this.activeForm.patient_mobile && this.activeForm.patient_email && this.validateEmail(this.activeForm.patient_email)) {
              sentMessage += ' AppLess (MagicLink) Sent to Email '+this.activeForm.patient_email+' and Mobile Number '+this.activeForm.patient_country_code + ' ' + this.activeForm.patient_mobile;
            } else if(this.activeForm.patient_email && this.validateEmail(this.activeForm.patient_email)) {
              sentMessage += ' AppLess (MagicLink) Sent to Email '+this.activeForm.patient_email;
            } else if(this.activeForm.patient_country_code && this.activeForm.patient_mobile) {
              sentMessage += ' AppLess (MagicLink) Sent to Mobile Number '+this.activeForm.patient_country_code + ' ' + this.activeForm.patient_mobile;
            }
          } else if(this.activeForm.facing_new == 2) {
            if(this.activeForm.practitioner_country_code && this.activeForm.practitioner_mobile && this.activeForm.patient_email && this.validateEmail(this.activeForm.practitioner_email)) {
              sentMessage += ' AppLess (MagicLink) Sent to Email '+this.activeForm.patient_email+' and Mobile Number '+this.activeForm.practitioner_country_code + ' ' + this.activeForm.practitioner_mobile;
            } else if(this.activeForm.patient_email && this.validateEmail(this.activeForm.practitioner_email)) {
              sentMessage += ' AppLess (MagicLink) Sent to Email '+this.activeForm.patient_email;
            } else if(this.activeForm.practitioner_country_code && this.activeForm.practitioner_mobile) {
              sentMessage += ' AppLess (MagicLink) Sent to Mobile Number '+this.activeForm.practitioner_country_code + ' ' + this.activeForm.practitioner_mobile;
            }            
          }
        }        

        var activityData = {
          activityName: activityName,
          activityType: "structured forms",
          activityDescription: activityDescription
        };

        this._structureService.trackActivity(activityData);

        localStorage.setItem('formSendMessage', JSON.stringify({
          messge: sentMessage,
          delay: 1000,
          type: 'success'
        }));



        /* this._structureService.notifyMessage({
          messge: sentMessage,
          delay: 3000,
          type: 'success'
        }); */
        //$('#tenantUsers').val(null).trigger('change');
        $("#tenantUsers").select2("val", " ");
         $("#response").val("");
      } else {
        var activityData = {
          activityName: "Send Form Fail from " + this.activeForm.formStatus + enable_collaborate_edit,
          activityType: "structured forms",
          activityDescription: this.userData.displayName + " form " + this.activeForm.form_name + " (" + this.activeForm.form_id + ") to " + selectedRecipients.toString() + " sending failed"
        };
        this._structureService.trackActivity(activityData);

        localStorage.setItem('formSendMessage', JSON.stringify({
          messge: this.activeForm.form_name + ' sending failed',
          delay: 1000,
          type: 'danger'
        }));
      }
      if(!this._structureService.pahEdit) {
      if (enable_collaborate_edit == "collaborateDraft-Enabled") {
        this.router.navigate(['/forms/list']);
      } else {
        this.router.navigate(['/forms/worklist']);
      }
    } 
    }
      }, 2000); 


    if (this.activeForm.facing_new == 2 || this.activeForm.facing_new == 0) {
      this._formsService.archiveDraftForm(this.activeForm).then((result: any) => {
      })
    }

  }

  setIframeUrl() {
    var confirmActionPrefillCheck=false;
    var prepopluatePreviousSub=false;
    
    if(this.activeForm.formStatus && (this.activeForm.formStatus=='Drafts' || this.activeForm.formStatus=='Draft')){
               if(this.activeForm.tag_meta){
                 var  tagMeta=this.activeForm.tag_meta.replace(/\\/g, '');
                 tagMeta=JSON.parse(tagMeta);
                 if((tagMeta.confirmActionPrefill && tagMeta.confirmActionPrefill==1) || (!tagMeta.hasOwnProperty('confirmActionPrefill'))){
                  confirmActionPrefillCheck=true;
                 }
                
                 if((tagMeta.populatePreviousSubmission && tagMeta.populatePreviousSubmission==1)){
                  prepopluatePreviousSub=true;
                 }
    }
  }
     
      if(this.activeForm.confirmActionPrefill && this.activeForm.confirmActionPrefill==1) confirmActionPrefillCheck=true;
      if(this.activeForm.populatePreviousSubmission && this.activeForm.populatePreviousSubmission==1) prepopluatePreviousSub=true;

      
    
    const pahEdit = this._structureService.pahEdit;
    this.savaAsDraftStaff = 1;
    const clientId = this._structureService.socket.io.engine.id;

    const tokenId = this._structureService.getCookie('authenticationToken');
    const facing_new = this.activeForm.facing_new;
    const enable_sftp_integration = +this.userDataConfig.enable_sftp_integration === 1 ? '&enable_sftp_integration=true':'&enable_sftp_integration=false';
    const unique = `&uniqueFormIdentity=${this.activeForm.form_guid || Guid.create()}`;
    const confirmActionPrefill = confirmActionPrefillCheck ? '&confirmActionPrefill=true' : '&confirmActionPrefill=false';
    
    const populatePreviousSubmission = prepopluatePreviousSub ? '&draftPopulate=true' : '&draftPopulate=false';
    const fax_queue_show_warning = +this.userData.config.fax_queue_show_warning === 1
    ? `&fax_queue_show_warning=true${unique}${confirmActionPrefill}${populatePreviousSubmission}`
    : `&fax_queue_show_warning=false${unique}${confirmActionPrefill}${populatePreviousSubmission}`;
    if (this.userDataConfig.save_as_draft_message_interval) {

      localStorage.setItem('saveAsDraftMessageInterval', this.userDataConfig.save_as_draft_message_interval);
    }
    if (this.userDataConfig.enable_collaborate_edit) {

      localStorage.setItem('enableCollaborateEdit', this.userDataConfig.enable_collaborate_edit);
    }
    let formsUrl = '';
    const baseUrl = this._structureService.machFormUrl + APIs.machFormEmbed + '?id=' + this.activeForm.form_id;
    if (this.activeForm.patient_id) {
      formsUrl = baseUrl  + "&patientId=" + this.activeForm.patient_id + "&loginUserId=" + this.userData.userId + "&autoSave=" + this.autoSave + "&savaAsDraftStaff=" + this.savaAsDraftStaff + "&savaAsDraftPatient=" + this.savaAsDraftPatient ;
    }
    else {
      formsUrl = baseUrl  + "&patientId=" + this.userData.userId + "&loginUserId=" + this.userData.userId + "&autoSave=" + this.autoSave + "&savaAsDraftStaff=" + this.savaAsDraftStaff + "&savaAsDraftPatient=" + this.savaAsDraftPatient + enable_sftp_integration + fax_queue_show_warning;
    }
    if (this.activeForm.facing_new == 1) {
      formsUrl = baseUrl  + "&patientId=" + this.activeForm.patient_id + "&loginUserId=" + this.activeForm.patient_id + "&autoSave=" + this.autoSave + "&savaAsDraftStaff=" + this.savaAsDraftStaff + "&savaAsDraftPatient=" + this.savaAsDraftPatient + enable_sftp_integration + fax_queue_show_warning + "&staffFacing=true&clearForm=0";
    } else {
       formsUrl = baseUrl  + "&patientId=" + this.activeForm.patient_id + "&loginUserId=" + this.activeForm.patient_id + "&noStafffillvalidation=false&autoSave=1&savaAsDraftStaff=1&savaAsDraftPatient=1&clearForm=0&staffFacing=false&staffFilling=true" + "&authenticationToken=" + this._structureService.getCookie('authenticationToken') + enable_sftp_integration + fax_queue_show_warning;
    }
    //Common params
    formsUrl += "&tenantId=" + ((this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')) ? this._structureService.getCookie('crossTenantId') : this._structureService.getCookie('tenantId')) + 
    "&toId=" +  this.userData.userId + 
    (this._structureService.isMultiAdmissionsEnabled ? `&admissionId=${this.activeForm.admissionId}` : '') + 
    "&sentId=null&environment=" + this._structureService.environment + 
    "&formName=" + encodeURIComponent(this.activeForm.formName) + 
    "&formId=" + this.activeForm.form_id + 
    "&apiVersion=" + this._structureService.version + 
    "&toName=" + this.userData.displayName + 
    "&fromName=" + this.userData.displayName + 
    "&serverBaseUrl=" + encodeURIComponent(this._structureService.serverUrlCustom) +
    "&fromMob=false" +
    "&isWorkingHour=" + this._inboxService.checkInfusionHours(true).isWorkingHours +
    "&clientId=" + clientId +
    "&authenticationToken=" + tokenId +
    "&facing_new=" + facing_new +
    "&savedDraftid=" + this.activeForm.form_submission_id + 
    "&saveAsDraftMessageInterval=" + localStorage.getItem('saveAsDraftMessageInterval') + 
    "&enableCollaborateEdit=" + localStorage.getItem('enableCollaborateEdit') +
    "&pahEdit=" + pahEdit +
    "&editDraft=1" +
    "&draftSave=true";

    if(this.activeForm.applessMode == 1) {
      formsUrl += '&formSendMode=appless';
    }
     // check form stabilization
    if (this.activeForm.facing_new == 0) {
     if (this.activeForm.caregiver) {
            this.recipientsetArray = this.activeForm.caregiver + '--' + this.activeForm.patient_id;
        } else {
           this.recipientsetArray = this.activeForm.patient_id;
        }
     }else if(this.activeForm.facing_new == 2) {
      this.recipientsetArray =this.activeForm.practitionerid; 
      formsUrl += "&flowcheck=practitioner";
    }
  var recipientset = this.recipientsetArray;
  if(recipientset == ""){
      recipientset = "";
  }
   if(this.messageadd != "" && this.messageadd != undefined){
        formsUrl += "&messageadd=" + this.messageadd;
    }
   formsUrl += "&userId="+this.userData.userId+"&recipientset="+recipientset;
   // check form stabilization

    
    this._formsService.draftSubmit=true;
    this._formsService.draftSubmissionID=this.activeForm.form_submission_id;
    this.formContent = this.sanitizer.bypassSecurityTrustResourceUrl(formsUrl);
  }
  setFormUrl() {
    const clientId = this._structureService.socket.io.engine.id;
    let toName = this.activeForm.createdUserName ? this.activeForm.createdUserName : this.activeForm.formName;
    const page = this.activeForm.page ? this.activeForm.page : '';
    if (this.editDetails) {
      this.formId = this.editDetails.formId;
      this.entryId = this.editDetails.entryId;
      this.userData.userId = this.editDetails.userId ? this.editDetails.userId : this.userData.userId ;
      this.activeForm.staff_facing = this.editDetails.staffFacing;
      this.activeForm.formName = this.editDetails.formName;
      this.activeForm.fromId = this.editDetails.fromId;
      toName = this.editDetails.toName;
      if(this.editDetails.roleId){
         this.userData.roleId = this.editDetails.roleId;
      }
      this.userData.displayName = this.editDetails.displayName;
      this.userData.tenantId = this.editDetails.tenantId;
      this.activeForm.fromUsername = this.editDetails.fromUsername;
      this.activeForm.sentId = this.editDetails.sentId;
      this.activeForm.formStatus = this.editDetails.formStatus;
      this.hideBackBtn = true;
    }
    
    const pahEdit = this._structureService.pahEdit;
    const tokenId = this._structureService.getCookie('authenticationToken');
    const progressnoteIntegrate=(this.userDataConfig.enable_progress_note_integration == 1)?true:false;
    const enable_sftp_integration=this.userDataConfig.enable_sftp_integration==1?"&enable_sftp_integration="+true:"&enable_sftp_integration="+false;
    const unique="&uniqueFormIdentity="+Guid.create(); 
    let confirmActionPrefillCheck=false;
    let prepopluatePreviousSub=false;
    
    if(this.activeForm.formStatus && this.activeForm.formStatus=='Draft'){
               if(this.activeForm.tag_meta){
                 var  tagMeta=this.activeForm.tag_meta.replace(/\\/g, '');
                 tagMeta=JSON.parse(tagMeta);
                 if((tagMeta.confirmActionPrefill && tagMeta.confirmActionPrefill==1) || (!tagMeta.hasOwnProperty('confirmActionPrefill'))){
                  confirmActionPrefillCheck=true;
                 }
                
                 if((tagMeta.populatePreviousSubmission && tagMeta.populatePreviousSubmission==1)){
                  prepopluatePreviousSub=true;
                 }
    }
  }
  if(this.activeForm.confirmActionPrefill && this.activeForm.confirmActionPrefill==1) confirmActionPrefillCheck=true;  
  if(this.activeForm.populatePreviousSubmission && this.activeForm.populatePreviousSubmission==1) prepopluatePreviousSub=true;
  const confirmActionPrefill = confirmActionPrefillCheck ?"&confirmActionPrefill=true":"&confirmActionPrefill=false";
  const populatePreviousSubmission = prepopluatePreviousSub ?"&draftPopulate=true":"&draftPopulate=false";
  const fax_queue_show_warning=this.userDataConfig.fax_queue_show_warning == 1?"&fax_queue_show_warning="+true+unique+confirmActionPrefill+populatePreviousSubmission:"&fax_queue_show_warning="+false+unique+confirmActionPrefill+populatePreviousSubmission;
  const patient_id=(this.activeForm.patient_id!=0)?this.activeForm.patient_id:undefined;
  const loginuserid=(this.activeForm.patient_id!=0 && this.activeForm.patient_id!==undefined)?this.activeForm.patient_id:this.userData.userId;
  let formsUrl = this._structureService.machFormUrl + APIs.machFormEmbed + '?id=' + this.formId + 
    "&patientId=" + patient_id + 
    (this._structureService.isMultiAdmissionsEnabled ? `&admissionId=${this.activeForm.admissionId}` : '') + 
    "&loginUserId=" + loginuserid + 
    "&tenantId=" + (
      (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')) 
      ? this._structureService.getCookie('crossTenantId') 
      : this._structureService.getCookie('tenantId')) + 
    "&toId=" + this.activeForm.fromId + 
    "&environment=" + this._structureService.environment + 
    "&formName=" + encodeURIComponent(this.activeForm.formName) + 
    "&formId=" + this.activeForm.form_id + 
    "&apiVersion=" + this._structureService.version + 
    "&toName=" + this.userData.displayName + 
    "&fromName=" + this.userData.displayName + 
    "&serverBaseUrl=" + encodeURIComponent(this._structureService.serverUrlCustom) + 
    "&fromMob=false&clearForm=0&staffFacing=true&savedDraftid=" + this.entryId + 
    "&AllowEdit=true&FormEntryID=" + this.entryId +
    "&authenticationToken=" + tokenId +
    "&progressnoteIntegrate=" + progressnoteIntegrate+enable_sftp_integration +
    "&pahEdit="+pahEdit + fax_queue_show_warning +
    "&editDraft=1";
if(typeof this.editDetails === 'undefined'){
      this._structureService.pahEdit = false;
      this._structureService.allowEditFrom=this._structureService.previousUrlNow;
      if(this.activeForm.applessMode == 1) {
        formsUrl += '&formSendMode=appless'
      }
      this.formContent = this.sanitizer.bypassSecurityTrustResourceUrl(formsUrl);
    } else {
      this._structureService.pahEdit = true;
      if(this.editDetails && this.editDetails.formStatus == "Pending"){
        this._structureService.allowEditFrom = 'pah';
        if(this.activeForm.applessMode == 1) {
          formsUrl += '&formSendMode=appless'
        }
        this.formContent = this.sanitizer.bypassSecurityTrustResourceUrl(formsUrl);
      } else {
        this.setIframeUrl();
     }
  }
  }

  goBack() {
    if (this.activeForm.formStatus == "Draft") {
      this._sharedService.myFormsType = "DRAFTS";
      if (this.userDataConfig.enable_collaborate_edit == 1) {
        this.router.navigate(['/forms/list']);
      } else {
        this.router.navigate(['/forms/worklist']);
      }

    }
    else
      this.router.navigate(['/forms/list/view']);

  }
  goBackToPreviousUrl() {
    if(this._structureService.previousUrlNow) {
      this.router.navigate([this._structureService.previousUrlNow]);
    } else {
      this._location.back();
    }
  }
}

// save as draft msg
window.addEventListener('message', function(event) {

  var response = event.data;
  var downloadTime = moment((moment().unix()) * 1000).format('hh:mm A');
  var downloadDateTime = moment((moment().unix()) * 1000).format('MMM DD hh:mm A');
 
  if(response == 'failed'){

    var previousTimenew = "";
    

    var previousTime = $("#error_save_draft").val();
    if(previousTime != "" && previousTime != "undefined" ){
      previousTimenew = '(last saved at '+ previousTime +')';
    }
     setTimeout(function(){
    $(".saveAsDraft-message").addClass('faildraft')
    $(".saveAsDraft-message").html('Automatic Save As Draft operation failed at '+downloadTime+' '+previousTimenew+'. Please use Save As Draft operation at the end of the Form to save your work periodically')
    }, 800);
  }else if(response == 'truesuccess'){
    $(".saveAsDraft-message").html("This form was automatically Saved as Draft at "+downloadTime+" and can also be found in the Drafts category of your Form Worklist.");
    $(".saveAsDraft-message").removeClass('faildraft');
    $("#last_save_draft").val(downloadDateTime);

  setTimeout(function(){
    $("#error_save_draft").val($("#last_save_draft").val());
    }, 2000);
   
  }

   try {
      JSON.parse(response);
    } catch (e) {
        return false;
    }
    if(JSON.parse(response).status == 1){
      $("#response").val(event.data);
      if((JSON.parse(response).bottombtn == 1)&&(JSON.parse(response).bottombtn)){
        $("#responsebtnedit").trigger('click');
      }
     
    }else if(JSON.parse(response).fail == 1){
      $("#failNotifyEdit").trigger('click');
      
    }
});  
// save as draft msg
