import { Injectable } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import 'rxjs/add/operator/toPromise';
import {Observable} from "rxjs/Observable";
import 'rxjs/add/observable/forkJoin';
import { StructureService } from '../structure.service';
import { convertTimeZoneDateTimeToUTCIso, isBlank, isPresent, validateEmail } from '../../utils/utils';
import gql from 'graphql-tag';
import { APIs } from 'app/constants/apis';
import { ToolTipService } from '../tool-tip.service';
import {CONSTANTS, FormSendMode, IntegrationType, UserGroup} from 'app/constants/constants';
import { HttpService } from 'app/services/http/http.service';
import * as moment from 'moment';
import { Store, StoreService } from '../shared/storeService';
import { map } from 'rxjs/operator/map';
import { catchError, switchMap } from 'rxjs/operators';
import { from } from 'rxjs/observable/from';
import { of } from 'rxjs/observable/of';

declare const swal: any;
declare const NProgress: any;
@Injectable()
export class FormsService {
  getAllTaggedFormsApi = '';
  getAllTaggedFormsLazyAll = '';
  sendSupplyApi='';
  getAllTaggedSupplyApi='';
  deleteStrucutredFormDataApi = '';
  getStrucuturedFormResultsApi = '';
  unlinkSignaturePadImagesApi='';
  uploadChatSignatureApi = '';
  getAllFormNamesApi = '';
  generateTaggedFormReportPdfApi = ''; 
  config:any;
  userDetails:any;

  configData:any = {};
  userData:any = {};
  data={};
  sendFormApi;
  resendFormApi;
  getTenantUsers;
  checkFormUrl;
  integrationStatusUrl;
  deleteFormEntryApi;
  archiveSubmittedFormyApi;
  archiveDraftFormApi;
  draftSubmit=false;
  draftSubmissionID;
  archiveSubmittedFormMultipleApi;
  filingCenterSubmittedFormApi;
  filingCenterSubmittedFormApidata;
  formReviewStatusChangeApi;
  draftHistorySave;
  restoreArchivedFormyApi;
  allowEditFormyApi;
  getAllTaggedFormsLazyApi;
  restoreArchivedMyFormyApi;
  checkOrderchangeAPI;
  copyformAPI;
  checkDraftFormApi;
  detailsDraftFormApi;
  detailsHistoryFormApi
  checkAllowEditEnabledApi;
  getalltenantsAPI;
  geMyFormWorklistApi;
  checkPHIFormApi;
  generateCSVApi;
  getMyFormWorklistCountApi;
  getAllFormWorklistCountApi;
  constructor( 
    private readonly _http: Http,
    private readonly _toolTipService: ToolTipService,
    private readonly httpService: HttpService,
    private readonly structureService: StructureService,
    private readonly storeService: StoreService
    ) {

      this.config= this._structureService.userDataConfig
      this.userDetails = this._structureService.userDetails;
      
      this.configData = JSON.parse(this.config);
      this.userData = JSON.parse(this.userDetails);

    this.getAllTaggedFormsApi = `${this.structureService.apiBaseUrl}citus-health/${this.structureService.version}/get-all-tagged-forms.php`;
    this.getAllTaggedFormsLazyAll = `${this.structureService.apiBaseUrl}citus-health/${this.structureService.version}/get-all-tagged-forms-limit-lazy.php`;
    this.geMyFormWorklistApi = `${this.structureService.apiBaseUrl}citus-health/${this.structureService.version}/get-my-form-worklist.php`;
    this.getMyFormWorklistCountApi = `${this.structureService.apiBaseUrl}citus-health/${this.structureService.version}/get-my-form-worklist-count.php`;
    this.getAllFormWorklistCountApi = `${this.structureService.apiBaseUrl}citus-health/${this.structureService.version}/get-all-form-worklist-count.php`;
    this.getAllTaggedFormsLazyApi = `${this.structureService.apiBaseUrl}citus-health/${this.structureService.version}/get-all-tagged-forms-limit-all.php`;
    this.getAllTaggedSupplyApi = `${this.structureService.apiBaseUrl}citus-health/${this.structureService.version}/get-all-tagged-supply.php`;
    this.deleteStrucutredFormDataApi = `${this.structureService.apiBaseUrl}citus-health/v4/delete-tagged-forms.php`;
    this.getStrucuturedFormResultsApi = `${this.structureService.apiBaseUrl}citus-health/v4/get-strucured-form-data.php`;
    this.unlinkSignaturePadImagesApi = `${this.structureService.apiBaseUrl}citus-health/v4/unlink-signpad-images.php`;
    this.generateTaggedFormReportPdfApi = `${this.structureService.apiBaseUrl}citus-health/v4/generate-structured-form-data-pdf.php`;
    this.uploadChatSignatureApi = `${this.structureService.apiBaseUrl}citus-health/v4/chat-signature-upload.php`;
    this.getAllFormNamesApi = `${this.structureService.apiBaseUrl}citus-health/v4/get-all-survey-names.php`;
    this.generateCSVApi = `${this.structureService.apiBaseUrl}citus-health/v4/generate-csv.php`;
    this.sendFormApi = `${this.structureService.apiBaseUrl}citus-health/v4/send-form-to-recipients.php`;
    this.sendSupplyApi = `${this.structureService.apiBaseUrl}citus-health/v4/send-supply-to-recipients.php`;
    this.getTenantUsers = `${this.structureService.apiBaseUrl}citus-health/v4/get-tenant-users-by-roleid.php`;
    this.checkFormUrl = `${this.structureService.apiBaseUrl}citus-health/v4/check-structured-form-submitted.php`;
    this.integrationStatusUrl = `${this.structureService.apiBaseUrl}citus-health/v4/get-integration-status.php`;
    this.deleteFormEntryApi = `${this.structureService.apiBaseUrl}citus-health/v4/delete-sent-form.php`;
    this.archiveSubmittedFormyApi = `${this.structureService.apiBaseUrl}citus-health/v4/archive-submitted-form.php`;
    this.archiveDraftFormApi = `${this.structureService.apiBaseUrl}citus-health/v4/delete-draft-form.php`;
    this.checkDraftFormApi = `${this.structureService.apiBaseUrl}citus-health/v4/check-draft-form.php`;
    this.detailsDraftFormApi = `${this.structureService.apiBaseUrl}citus-health/v4/get-all-drafts-form-details.php`;
    this.detailsHistoryFormApi = `${this.structureService.apiBaseUrl}citus-health/v4/get-all-form-history-details.php`;
    this.checkAllowEditEnabledApi = `${this.structureService.apiBaseUrl}citus-health/v4/check-allow-edit-enabled-form.php`;
    this.resendFormApi = `${this.structureService.apiBaseUrl}citus-health/v4/resend-form-to-recipient.php`;
     
    this.archiveSubmittedFormMultipleApi = `${this.structureService.apiBaseUrl}citus-health/v4/archive-submitted-form-multiple.php`;
    this.filingCenterSubmittedFormApi = `${this.structureService.apiBaseUrl}citus-health/v4/save-submitted-form.php`;
    this.filingCenterSubmittedFormApidata = `${this.structureService.apiBaseUrl}citus-health/v4/save-submitted-form-data.php`;
    this.formReviewStatusChangeApi = `${this.structureService.apiBaseUrl}citus-health/v4/change-form-review-status.php`;
    this.draftHistorySave = `${this.structureService.apiBaseUrl}citus-health/v4/save-draft-history.php`;
    this.restoreArchivedFormyApi = `${this.structureService.apiBaseUrl}citus-health/v4/restore-archived-forms.php`;
    this.restoreArchivedMyFormyApi = `${this.structureService.apiBaseUrl}citus-health/v4/restore-sent-form.php`;
    this.allowEditFormyApi = `${this.structureService.apiBaseUrl}citus-health/v4/allow-edit-form.php`;
    this.checkOrderchangeAPI = `${this.structureService.apiBaseUrl}citus-health/v4/check-form-order-change.php`;
    this.copyformAPI = `${this.structureService.apiBaseUrl}citus-health/v4/copy-form-to-another-tenant.php`;
    this.checkPHIFormApi = `${this.structureService.apiBaseUrl}citus-health/v4/check-PHI-form.php`;
    this.getalltenantsAPI = `${this.structureService.apiBaseUrl}citus-health/v4/get-all-tenant-names.php`;
  }
  allowEditForm(data,msg=''){
    if(msg) {
      data.message = msg;
    }
    return this._structureService.requestData({url: this.allowEditFormyApi, requestType: 'http', data});
  }

    getalltenants(){
    return this._structureService.requestData({url: this.getalltenantsAPI, requestType: 'http'});
  }

  copyform(data){
    return this._structureService.requestData({url: this.copyformAPI, requestType: 'http', data});
  }

  checkPHIForm(data)
  {
    return this._structureService.requestData({url: this.checkPHIFormApi, requestType: 'http', data});
  }
  restoreArchivedForm(data) {
    const restoreForm = {
      deleted_by: this.userData.userId,
      form_id: data.form_id,
      form_submission_id: data.form_submission_id,
      from_id: data.from_id,
      sent_id: data.sent_id,
      recipient_id: data.recipient_id,
      isActive: data.isActive,
      from_id_n: data.from_id_n
    };
    return this._structureService.requestData({url: this.restoreArchivedFormyApi, requestType: 'http', data: restoreForm});
  }

  restoreArchivedMyForm(data){
    delete data.inboxFormArchiveType;
    delete data.loginUserId;
    delete data.formId;
    return this._structureService.requestData({url: this.restoreArchivedMyFormyApi, requestType: 'http', data});
  }

  archiveDraftForm(data)
  {
    
    var userData = JSON.parse(this._structureService.userDetails);
    if(userData)
    {
      // TODO: esi_code_for_patient_identity decommisioned, remove usage if it is not causing any issue
      data['config_identity_value'] = userData.config.esi_code_for_patient_identity;
      data['config_staff_identity'] = userData.config_replica.esi_code_for_staff_identity;
      data['config_staff_name'] = userData.config_replica.esi_code_for_staff_name;
      data['enable'] = userData.config.enable_progress_note_integration;
      data['deleted_by'] = userData.userId;
   }
   var archiveReqBody;
   if(data['formDatadraftid']){
    archiveReqBody = {
        formDatadraftid : data['formDatadraftid'],
        formId :  data['formId']
    }
   }else{
    archiveReqBody = {
        draftid : data['draftid']
    }
   }
   
    var apiConfig = {url: this.archiveDraftFormApi, requestType: 'http', data: archiveReqBody};
    return this._structureService.requestData(apiConfig);
  }
  checkDraftForm(data)
  {
    var userData = JSON.parse(this._structureService.userDetails);
    var requestBody = {
        formid: data.formid,
        patientid: data.patientid,
        admissionId: data.admissionId
    }
    var apiConfig = {url: this.checkDraftFormApi, requestType: 'http', data: requestBody};
    return this._structureService.requestData(apiConfig);
  }

  detailsDraftForm(data)
  {    
    return this._structureService.requestData({url: this.detailsDraftFormApi, requestType: 'http', data});
  }
  detailsHistoryForm(data)
  {    
    return this._structureService.requestData({url: this.detailsHistoryFormApi, requestType: 'http', data});
  }
  checkAllowEditEnabled(data)
  {    
    var requestBody = {
      formid: data.formid,
      patientid: data.patientid,
      admissionId: data.admissionId
    };
    return this._structureService.requestData({url: this.checkAllowEditEnabledApi, requestType: 'http', data: requestBody});
  }


  archiveSubmittedForm(data){
    return this._structureService.requestData({url: this.archiveSubmittedFormyApi, requestType: 'http', data});
  }
  archiveSubmittedMultipleForm(data){
    var apiConfig = {url: this.archiveSubmittedFormMultipleApi, requestType: 'http', data};
    return this._structureService.requestData(apiConfig);
  }
  filingCenterSubmittedForm(data){

    var userData = JSON.parse(this._structureService.userDetails);
    if(userData)
    {
      // TODO: esi_code_for_patient_identity decommisioned, remove usage if it is not causing any issue
      data['config_identity_value'] = userData.config.esi_code_for_patient_identity;
      data['config_staff_identity'] = userData.config_replica.esi_code_for_staff_identity;
      data['config_staff_name'] = userData.config_replica.esi_code_for_staff_name;
      data['enable'] = userData.config.enable_progress_note_integration;
    }
    return this._structureService.requestData({url: this.filingCenterSubmittedFormApi, requestType: 'http', data});
  }
  formReviewStatusChange(data){   
    return this._structureService.requestData({url:this.formReviewStatusChangeApi , requestType: 'http', data});
  }
  filingCenterSubmittedFormdata(data){

    var userData = JSON.parse(this._structureService.userDetails);
    if(userData)
    {
      // TODO: esi_code_for_patient_identity decommisioned, remove usage if it is not causing any issue
      data['config_identity_value'] = userData.config.esi_code_for_patient_identity;
      data['config_staff_identity'] = userData.config_replica.esi_code_for_staff_identity;
      data['config_staff_name'] = userData.config_replica.esi_code_for_staff_name;
      data['enable'] = userData.config.enable_progress_note_integration;
    }
    return this._structureService.requestData({url: this.filingCenterSubmittedFormApidata, requestType: 'http', data});
  }
  draftHistoryUpdate(data){
    return this._structureService.requestData({url: this.draftHistorySave, requestType: 'http', data});
  }
  checkOrderchange(data){
    return this._structureService.requestData({url: this.checkOrderchangeAPI, requestType: 'http', data});
  }
  deleteSentForm(data){
    return this._structureService.requestData({url: this.deleteFormEntryApi, requestType: 'http', data});
  }
  checkIsFormFilled(data){
    let reqParam: any = {};
    reqParam.caregiver_userid = data.caregiver_userid ? data.caregiver_userid :'';
    reqParam.recipient_id = data.recipient_id ? data.recipient_id : '';
    reqParam.form_submission_id = data.form_submission_id ? data.form_submission_id : '';
    reqParam.fromId = data.fromId ? data.fromId : '';
    reqParam.sentId = data.sent_id ? data.sent_id : '';
    reqParam.created_on = data.created_date ? data.created_date : '';

    var apiConfig = {url: this.checkFormUrl, requestType: 'http', data: reqParam};
    return this._structureService.requestData(apiConfig);
  }
  getAllTaggedForms(tenantId,roleid,zone,forms, pending){
    var isForms = false;
    if(forms){
        isForms = true;
    } 
    const isPrivilege = this.isViewAllFormEntriesPrivilegeEnabled();
    var data='';
    if(pending){
      data = "tenantId=" +tenantId+ "&roleid=" +roleid+ "&zone=" +zone+ "&isForms="+isForms+"&isPrivilege="+isPrivilege+"&pending="+true;
    }else{
      data = "tenantId=" +tenantId+ "&roleid=" +roleid+ "&zone=" +zone+ "&isForms="+isForms+"&isPrivilege="+isPrivilege;
    }
    if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
      data=data+"&crossTenantId="+this._structureService.getCookie('crossTenantId');
    }
    return this._structureService.requestData({url: this.getAllTaggedFormsApi, requestType: 'http', data});
  }
  getIntegrationDetails(type,orderId, tenantId='', patientId = '') {
    let c = {
      reference_id:orderId,
      type,
      tenantId,
      patientId
    };
    if(type == 'not-sftp'){
      this.integrationStatusUrl = this._structureService.apiBaseUrl+'citus-health/v4/'+ APIs.getIntegrationStatusAPI;
    }
    var apiConfig = {url: this.integrationStatusUrl, requestType: 'http', data: JSON.stringify(c)};
    return this._structureService.requestData(apiConfig);
  }
  // TODO: Will change the payload once new API is ready to integrate
  getAllTaggedFormsLazyPagination(
    zone,
    forms,
    limit,
    offset,
    searchText,
    orderData,
    orderby,
    formStatus,
    isScheduled,
    filterType,
    userId,
    includeOtherForms = true,
    type = '',
    enableIntegrationStatus = false,
    enableSftpIntegration = false,
    siteId,
    isAdvanceSearch = false,
    advancedSearchPayload?
  ) {
    if (isAdvanceSearch) {
      return this.structureService.requestData({
        url: `${this.structureService.apiBaseUrl}${APIs.formWorkListAdvancedSearch}`,
        requestType: 'http',
        data: advancedSearchPayload
      });
    }

    const configData = JSON.parse(this.structureService.userDataConfig);
    const userData = JSON.parse(this.structureService.userDetails);
    let isForms = false;
    let getWorklist = '';
    if (forms){
      isForms = true;
    }
    if(type ==='myWorklist') {
      isScheduled = "no";
      filterType = "me";
      getWorklist = this.geMyFormWorklistApi;
    } else {
      getWorklist = this.getAllTaggedFormsLazyAll;
    }
  const isPrivilege = this.isViewAllFormEntriesPrivilegeEnabled();

    let data = `zone=${zone}&isForms=${isForms}&isPrivilege=${isPrivilege}&limit=${limit}&offset=${offset}&searchText=${searchText}&${formStatus}=${true}&orderData=${orderData}&orderby=${orderby}&isScheduled=${isScheduled}&accessSecurityEnabled=${userData.accessSecurityEnabled}${this.getDateRangeFilter()}`;
    if(filterType) {
      data += `&filterType=${filterType}`;
    }
    if(isScheduled == "all" && !includeOtherForms) {
      data += `&includeOthers=no`
    }
   
    if("enable_nursing_agencies_visibility_restrictions" in configData && configData.enable_nursing_agencies_visibility_restrictions == 1) {
      data += `&nursingAgencies=${userData.nursing_agencies}`;
    }
    data += `&citusRoleId=${userData.group}&enableIntegrationStatus=${enableIntegrationStatus}&enableSftpIntegration=${enableSftpIntegration}`;
    if(+userData.group !== UserGroup.PATIENT){
      data += `&siteIds=${siteId}`;
    }
    if (!isBlank(siteId)) {
      return this._structureService.requestData({url: getWorklist, requestType: 'http', data});
    }
  }

  /**
   * Get date range filter from session
   * @returns dateRange string
   */
  getDateRangeFilter(){
    let dateRange = '';
    if(this.storeService.getStoredData(Store.DATE_RANGE_FILTER_FORMS)){
      let dateRangeSelected = JSON.parse(this.storeService.getStoredData(Store.DATE_RANGE_FILTER_FORMS));
      const startDate = !isBlank(dateRangeSelected.startDate) ? convertTimeZoneDateTimeToUTCIso(dateRangeSelected.startDate, CONSTANTS.startTime, moment.tz.guess()) : dateRangeSelected.startDate;
      const endDate = !isBlank(dateRangeSelected.endDate) ? convertTimeZoneDateTimeToUTCIso(dateRangeSelected.endDate, CONSTANTS.endTime, moment.tz.guess()) : dateRangeSelected.endDate;
      dateRange += "&startDate="+startDate+"&endDate="+endDate;
    }
    return dateRange;
  }

  getAllTaggedFormsLazyCount(allForms = false, isScheduled, includeOtherForms = true, siteId){ 
    let configData= JSON.parse(this._structureService.userDataConfig);
    let userData = JSON.parse(this._structureService.userDetails);
    let getWorklist = this.getMyFormWorklistCountApi;
    let data = `accessSecurityEnabled=${userData.accessSecurityEnabled}${this.getDateRangeFilter()}`;

    if(allForms) {
      getWorklist = this.getAllFormWorklistCountApi;
      const isPrivilege = this.isViewAllFormEntriesPrivilegeEnabled();
      data += `&isPrivilege=${isPrivilege}&isScheduled=${isScheduled}`;
      
      if(isScheduled == "all" && !includeOtherForms) {
        data += `&includeOthers=no`;
      } else {
        data += `&includeOthers=yes`;
      }
      data += `&citusRoleId=${userData.group}`;      
    }

    if("enable_nursing_agencies_visibility_restrictions" in configData && configData.enable_nursing_agencies_visibility_restrictions == 1) {
      data += `&nursingAgencies=${userData.nursing_agencies}`;
    }
   if(userData.group != 3){
        data += `&siteIds=${siteId}`;
    }
    if(!isBlank(siteId)){
      return this._structureService.requestData({ url: getWorklist, requestType: 'http', data });
    }
    
  }

  getAllTaggedFormsLazy(tenantId,roleid,zone,forms, pending,archieved,offset,limit,searchText,userId,type=''){ 
    console.log('searchText' , searchText);
    var isForms = false;
    if(forms){
        isForms = true;
    }
    const isPrivilege = this.isViewAllFormEntriesPrivilegeEnabled();
    var data='';
    if(pending == true){
      data = "tenantId=" +tenantId+ "&roleid=" +roleid+ "&zone=" +zone+ "&isForms="+isForms+"&isPrivilege="+isPrivilege+"&pending="+true+"&offset="+offset+"&limit="+limit+"&searchText="+searchText+"&pah_patient_id="+userId;
    }else if (archieved == true) {
      data = "tenantId=" +tenantId+ "&roleid=" +roleid+ "&zone=" +zone+ "&isForms="+isForms+"&isPrivilege="+isPrivilege+"&archived="+true+"&offset="+offset+"&limit="+limit+"&searchText="+searchText+"&pah_patient_id="+userId;
    }
    else{
      data = "tenantId=" +tenantId+ "&roleid=" +roleid+ "&zone=" +zone+ "&isForms="+isForms+"&isPrivilege="+isPrivilege+"&offset="+offset+"&limit="+limit+"&searchText="+searchText+"&pah_patient_id="+userId;
    }
    console.log('dataaaaaaaaaaaaaaaaa',data);
    if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
      data=data+"&crossTenantId="+this._structureService.getCookie('crossTenantId');
    }
    var apiConfig = {url: this.getAllTaggedFormsLazyApi, requestType: 'http', data: data};
    return this._structureService.requestData(apiConfig);
  }
  getAllTaggedAutomatedFormsCombined(tenantId, roleid, zone) {
    const isPrivilege = this.isViewAllFormEntriesPrivilegeEnabled();
    var data = "tenantId=" +tenantId+ "&roleid=" +roleid+ "&zone=" +zone+ "&isForms="+true+"&isPrivilege="+isPrivilege+"&pending="+false;  
    if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
      data=data+"&crossTenantId="+this._structureService.getCookie('crossTenantId');
    }
    var apiConfig = {url: this.getAllTaggedSupplyApi, requestType: 'http', data: data};
    let taggedFormNotPending =  this._structureService.requestData(apiConfig);
    var data1 = "tenantId=" +tenantId+ "&roleid=" +roleid+ "&zone=" +zone+ "&isForms="+true+"&isPrivilege="+isPrivilege+"&pending="+true;  
    if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
      data1=data1+"&crossTenantId="+this._structureService.getCookie('crossTenantId');
    }
    var apiConfig = {url: this.getAllTaggedSupplyApi, requestType: 'http', data: data1};
    let taggedFormPending =  this._structureService.requestData(apiConfig);
    return Observable.forkJoin([taggedFormNotPending, taggedFormPending]);
  }
   getAllTaggedAutomatedForms(tenantId,roleid,zone,forms, pending){
    var isForms = false;
    if(forms){
        isForms = true;
    }
    const isPrivilege = this.isViewAllFormEntriesPrivilegeEnabled();
    var data='';
    if(pending){
      data = "tenantId=" +tenantId+ "&roleid=" +roleid+ "&zone=" +zone+ "&isForms="+isForms+"&isPrivilege="+isPrivilege+"&pending="+true;
    }else{
      data = "tenantId=" +tenantId+ "&roleid=" +roleid+ "&zone=" +zone+ "&isForms="+isForms+"&isPrivilege="+isPrivilege;
    }
    if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
      data=data+"&crossTenantId="+this._structureService.getCookie('crossTenantId');
    }    
    return this._structureService.requestData({url: this.getAllTaggedSupplyApi, requestType: 'http', data});
  }

  getTenantUsersByRoleId(tenantId,tagsId,roleId,isStafFacing,searchText="", siteIds=0, virtualNeeded = false){
    var userData = JSON.parse(this._structureService.userDetails);

    var data = '';
    if(isStafFacing){
      data = "isTenantRoles=" + undefined+"&roleId=3&status=notRejected&needVirtualPatients=true";
    }else{
      data = "tagsId=" + tagsId + "&isTenantRoles=" + true+"&roleId="+roleId;
    }
    if(userData.config.enable_nursing_agencies_visibility_restrictions == 1) {
      data += "&nursingAgencies="+userData.nursing_agencies;
    }
    if(searchText){
    data +="&searchKeyword="+searchText;
    }
    if (userData.accessSecurityEnabled) {
      data += "&accessSecurityEnabled=" + userData.accessSecurityEnabled;
      data += "&accessSecurityEsiValue=" + userData.accessSecurityEsiValue;
      data += "&accessSecurityIdentifierType=" + userData.accessSecurityIdentifierType;
      data += "&accessSecurityType=" + userData.accessSecurityType;
    }
    if(virtualNeeded){
      data += "&needVirtualPatients=true&status=notRejected";
    }
    data += "&siteIds="+ siteIds;
   
    return this._structureService.requestData({url: this.getTenantUsers, requestType: 'http', data});
  }

  getFormRecipients(tenantId,tagsId,roleId,isStafFacing,searchKeyword,reoleSearchNeeded=true, virtualNeeded = false, practitioner = false, siteIds,patientAssociation, admissionId = '', recipientId?){
    const externalUserId = localStorage.getItem('externalUserId');
    var userData = JSON.parse(this._structureService.userDetails);

    var data = '';
    if(isStafFacing){
      data = "isTenantRoles=" + undefined+"&roleId=3&status=notRejected&needVirtualPatients=true";
    } else if(virtualNeeded && practitioner) {
      data = "tagsId=" + tagsId + "&isTenantRoles=" + true+"&status=notRejected&needVirtualPatients=true&roleId="+roleId;
    } else if(virtualNeeded && !practitioner) {
      //data = "tenantId=" + tenantId +"&isTenantRoles=" + undefined+"&roleId=3&status=notRejected&needVirtualPatients=true";
      data = "tagsId=" + tagsId + "&isTenantRoles=" + true+"&status=notRejected&needVirtualPatients=true&roleId="+roleId;
    } else {
      data = "tagsId=" + tagsId + "&isTenantRoles=" + true+"&status=notRejected&roleId="+roleId;
    }
   
    if(userData.config.enable_nursing_agencies_visibility_restrictions == 1) {
      data += "&nursingAgencies="+userData.nursing_agencies;
    }
    data += "&formRecipients=1";
    if(searchKeyword && searchKeyword !=""){
      data += "&searchKeyword="+searchKeyword;
    }
    if (isPresent(externalUserId)) {
      data += `&externalPatientId=${externalUserId}&type=recipient`;
    }
    if(!reoleSearchNeeded){
      data += "&reoleSearchNeeded=false";
    }
    if (userData.accessSecurityEnabled) {
      data += "&accessSecurityEnabled=" + userData.accessSecurityEnabled;
      data += "&accessSecurityEsiValue=" + userData.accessSecurityEsiValue;
      data += "&accessSecurityIdentifierType=" + userData.accessSecurityIdentifierType;
      data += "&accessSecurityType=" + userData.accessSecurityType;
    }
    if(!practitioner|| (practitioner && patientAssociation!='false')){
        data += "&siteIds="+ siteIds;
    }
    if (this._structureService.isMultiAdmissionsEnabled) {
      data += `&admissionId=${admissionId}`;
    }
    if (recipientId) {
      data += `&selectedPatient=${recipientId}`;
    }
    return this._structureService.requestData({url: this.getTenantUsers, requestType: 'http', data});
  }

  deleteTaggedForms(formId,formType){
    var data = "formId=" + formId + "&formType=" + formType;
    var apiConfig = {url: this.deleteStrucutredFormDataApi, requestType: 'http', data: data};
    return this._structureService.requestData(apiConfig);
  }
  getStrucuturedFormResults(formId,formSubmissionId, worklistType = ''){ 
    var data = "formId=" + formId + "&submissionId=" + formSubmissionId;
    if(worklistType != ''){
      data = data + "&worklistType=NGW";
    }
    var apiConfig = {url: this.getStrucuturedFormResultsApi, requestType: 'http', data: data};
    return this._structureService.requestData(apiConfig);
    }
  unlinkSignaturePadImages(data){ 
    var rew = JSON.stringify(data);
    var apiConfig = {url: this.unlinkSignaturePadImagesApi, requestType: 'http', data:{'data':rew} };
    return this._structureService.requestData(apiConfig);
  }
  
  generateTaggedFormReportPdf(data,type,timezone){
    /*ar apiConfig = {url: this.generateTaggedFormReportPdfApi, requestType: 'http', data: data};
    return this._structureService.requestData(apiConfig);*/


    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
      var params = "?type=" + type + "&zone=" +undefined
      if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
        params=params+"&crossTenantId="+this._structureService.getCookie('crossTenantId')+"&crossTenantName="+this._structureService.getCookie('crossTenantName');
      }
    const apiURL = this.generateTaggedFormReportPdfApi+params;
       this._http.post(apiURL,data,options)
        .toPromise()
        .then(
        res => {
          const result = res;
          resolve(result);
        }
        );
    });
    return promise;
  }

  uploadChatSignature(data,signImageData,action){
    return this._structureService.requestData({url: this.uploadChatSignatureApi+"?action="+action, requestType: 'http', data});
  }
  getAllFormNames(tenantId,selectedTenant=false,patientFacing =false){
    if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')) {
      tenantId.crossTenantId=this._structureService.getCookie('crossTenantId');
    }   
    if(selectedTenant && this._structureService.getCookie('selectedTenantId') && this._structureService.getCookie('selectedTenantId') !== 'undefined') {
      tenantId.selectedTenantId=this._structureService.getCookie('selectedTenantId');
      
    } 
    if(patientFacing) {     
      tenantId.patientFacing=true;
    }  else{
      tenantId.patientFacing=false;
    }
    var apiConfig = {url: this.getAllFormNamesApi, requestType: 'http', data: tenantId};
    return this._structureService.requestData(apiConfig);
  }
  generateCSV(tenantId,selectedTenant=false,formType='',reportType='',siteId){
    if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')) {
      tenantId.crossTenantId=this._structureService.getCookie('crossTenantId');
    } 
    if(reportType && reportType =='enrollmentsReports'){
      // tenantId.selectedTenantId=this._structureService.getCookie('selectedTenantKey');
    } else if(selectedTenant && this._structureService.getCookie('selectedTenantId') && this._structureService.getCookie('selectedTenantId') !== 'undefined') {
      // tenantId.selectedTenantId=this._structureService.getCookie('selectedTenantId');
      
    } 
    let reqParam:any = {
      "formType": formType,
      "reportType":reportType,
      "siteId": siteId
    };
      tenantId.formType=formType;
      tenantId.reportType=reportType;
      tenantId.siteId = siteId;
      console.log(tenantId);
      console.log(siteId);
      console.log(tenantId.siteId);
    var apiConfig = {url: this.generateCSVApi, requestType: 'http', data: reqParam};
    return this._structureService.requestData(apiConfig);
  }
  ;

  resendForms(data){
    return this._structureService.requestData({url: this.resendFormApi, requestType: 'http', data});
  }
  sendForms(data){
    return this._structureService.requestData({url: this.sendFormApi, requestType: 'http', data});
  }
 sendSupply(data){
    return this._structureService.requestData({url: this.sendSupplyApi, requestType: 'http', data});
  }

  checkIntegration(data): Observable<any> {
    return this.httpService.doPost(APIs.checkFormIntegrationStatusEndpoint, data, undefined, { skipErrorHandling: true }).pipe(
      catchError((error) => {
        return new Observable(observer => {
          this._structureService.showAlertMessagePopup({
            text: `${error.error.status.message} \n ${this._toolTipService.getTranslateData('WARNING.YOU_CAN_CONTINUE_ANYWAY')}`,
            type: CONSTANTS.notificationTypes.error,
            title: '',
            confirmButtonText: this._toolTipService.getTranslateData('BUTTONS.CONTINUE_ANYWAY'),
            cancelButtonText: this._toolTipService.getTranslateData('BUTTONS.GO_BACK')
          })
          .then((isConfirm) => {
            if (isConfirm) {
              observer.next({
                success: true,
                status: { code: "200", message: this._toolTipService.getTranslateData('BUTTONS.CONTINUE_ANYWAY') },
                data: {}
              });
            } else {
              observer.next(error);
            }
            observer.complete();
          });
        })
      })
    )
  }

  getDocumentTypeDetailsQuery(){
      let TaggedForms =`query getSessionTenant($sessionId: String!,$id:Int`;
      TaggedForms = TaggedForms+`){getSessionTenant(sessionToken:$sessionId`;
      TaggedForms = TaggedForms+`){
        formTypeDetails(id:$id) {
          id
          tagName
          tagMeta {
            externalFileDisclosePHI
            externalFileDisclosePHITriggerOn
            directlinkpatientchart
            directlinkpatientchartTriggerOn
            progressNoteIntegrationTriggerOn
            progressNoteIntegration
            sendformdatajson
            sendformdatajsonTriggerOn
            FaxQIntegration
            faxQIntegrationTriggerOn
            externalFileExchangeWebhook
            externalFileExchangeTriggerOn
          }
          tenantId
      } 
  
      }}`;
      console.log("TaggedForms====>",TaggedForms);
      return  gql`${TaggedForms}`;
    }
    getDocumentTypeDetails(id) {
      let variables:any = { 
        id: Number(id),
        sessionId : this._structureService.getCookie('authID')
      };
      let apiConfig = {
        method: 'GET',
        data: this.getDocumentTypeDetailsQuery(),
        variables,
        requestType: 'gql',
        use: "",
      };
      return this._structureService.requestData(apiConfig);
    }

    /**
 * setLifeCycleHistory() is used to return the
   form lifecycle history note for each worklist
 * @param element <obj> - History element data.
   @param statusCode <boolean>- Which type of webhook need to integrate  
 * @returns  <String> Note that need to show in life cycle history
 */
 
 setLifeCycleHistory(element,statusCode,type='') :any{
  let timeLength = 9;
  let formStatus ="";
  if(type == 'Submit' && element.submitgenerate)
     type='Send to EHR';
     
  let note="Progress Note";
  switch(statusCode){
      case 1 : formStatus = "initiated";
               break;
      case 2 : formStatus = "initiation failed";
               break;
      case 3 : formStatus = "completed";
               break;
      case 4 : formStatus = "failed";
               break;
      default: formStatus = "";
  }
  if(element.ehrname && !element.webhoookDocExchangeArchive && !element.webhoookDocExchangeSubmit){
    if(element.ehrname.includes("MatrixCare")){
      note="Communication Note";
    }
    else if(element.ehrname.includes("HCHB")){
      note="Coordination Note";
    }
    else{
      note="Progress Note";
    }
  }
  else{
    note="Document";
  }
  
  let message ="Form integrated as "+note+" for "+element.archivepatientname+
  " by "+element.recepient_firstname+' '+element.recepient_lastname +" on "+type+ " action "+
  formStatus;

  const promise = new Promise((resolve, reject) => {
      resolve(message);
    });
   return promise;  
  }  
  setESignatureAuditLogLifeCycleHistory(element, dateTime): Promise<any> {
    let date = '';
    if(dateTime) {
      date = (dateTime.length >= 9)? 'on '+dateTime : 'at '+dateTime;
    }
    let message ='';
    if (element.esignature_log == 1) {
      message = 'Signature added to the '+ element.signature_field_name+' field '+date;
    } else if(element.esignature_log == 2) {
      message = 'Signature removed from the '+ element.signature_field_name+' field '+date;
    }
  const promise = new Promise((resolve) => {
    resolve(message);
  });

 return promise;  
 }
  generateDraftPendingForm(formDetails) {
    var apiConfig = {url: this._structureService.apiBaseUrl + APIs.generateDraftPendingFormPdf, requestType: 'http', data: formDetails};
    return this._structureService.requestData(apiConfig);
  }
  notifyAfterAllowEdit(activeStrucuredForms, result, from, msg) :Promise<any> {
    return new Promise(async (resolve) => {
      let formSendMode = +activeStrucuredForms.applessMode === 1 ? FormSendMode.APPLESS : +activeStrucuredForms.applessMode === 0 ? FormSendMode.INAPP : '';
      let applessMode = FormSendMode.BOTH;
      let activityData: any;      
      if (result.allowedit == 0) {
        this._structureService.notifyMessage({
          messge: 'Editable permission denied for ' + activeStrucuredForms.form_name,
          delay: 1000,
          type: 'success'
        });
        var activityName = "Allow Edit";
        var activityDescription = "Edit has been denied for " + activeStrucuredForms.form_name + " (" + activeStrucuredForms.form_id + ") by " + this.userData.displayName;

        if (formSendMode == 'appless') {
          activityName = "Allow Edit (Appless)";
          activityDescription = "Edit has been denied for appless form " + activeStrucuredForms.form_name + " (" + activeStrucuredForms.form_id + ") by " + this.userData.displayName;
        }

        activityData = {
          activityName: activityName,
          activityType: "structured forms",
          activityDescription: activityDescription
        };
        this._structureService.trackActivity(activityData);
      }
      if (result.allowedit == 1) {
        if ((activeStrucuredForms.facing_new == 1 && activeStrucuredForms.userid == this.userData.userId) || (activeStrucuredForms.facing_new == 0 && activeStrucuredForms.from_id == this.userData.userId) || (activeStrucuredForms.facing_new == 2 && activeStrucuredForms.from_id == this.userData.userId)) {
          let sentMessage = 'Your form has been sent to pending for editing';
          if (formSendMode && formSendMode === FormSendMode.APPLESS) {
            let notifyItems = [];
            if (activeStrucuredForms.username && validateEmail(activeStrucuredForms.username)) {
              notifyItems.push("Email " + activeStrucuredForms.username);
            }
            if (activeStrucuredForms.mobile && activeStrucuredForms.countryCode) {
              notifyItems.push("Mobile Number (" + activeStrucuredForms.countryCode + ") " + activeStrucuredForms.mobile);
            }
            if (notifyItems.length > 0) {
              sentMessage += '. AppLess (MagicLink) sent to ';
              if (notifyItems.length == 2) {
                sentMessage += notifyItems.join(' and ');
              } else {
                sentMessage += notifyItems[0];
              }
            }
          }
          if(from == 'viewPage') {
            this._structureService.notifyMessage({
              messge: sentMessage,
              type: 'success'
            });
          } else {
            localStorage.setItem('formSendMessage', JSON.stringify({
              messge: sentMessage,
              delay: 1000,
              type: 'success'
            }));    
          }
        } else {
          let sentMessage = this._toolTipService.getTranslateDataWithParam('SUCCESS_MESSAGES.FORM_EDIT_PERMISSION_GRANTED',
          {'formName': activeStrucuredForms.form_name});
          let notifyItems = [];
          if (formSendMode && formSendMode === FormSendMode.APPLESS) {
            if (activeStrucuredForms.username && validateEmail(activeStrucuredForms.username)) {
              notifyItems.push("Email " + activeStrucuredForms.username);
            }
            if (activeStrucuredForms.mobile && activeStrucuredForms.countryCode) {
              notifyItems.push("Mobile Number (" + activeStrucuredForms.countryCode + ") " + activeStrucuredForms.mobile);
            }
            if (notifyItems.length > 0) {
              sentMessage += '. AppLess (MagicLink) sent to ';
              if (notifyItems.length == 2) {
                sentMessage += notifyItems.join(' and ');
              } else {
                sentMessage += notifyItems[0];
              }
            }
          }
          if(from == 'viewPage') {
            this._structureService.notifyMessage({
              messge: sentMessage,
              type: 'success'
            });
          } else {
            localStorage.setItem('formSendMessage', JSON.stringify({
              messge: sentMessage,
              delay: 1000,
              type: 'success'
            }));
          }
        }
        let activityName = "Allow Edit";
        let activityDescription = 'Edit has been granted for ' + encodeURIComponent(activeStrucuredForms.form_name) + ' (' + activeStrucuredForms.form_id + ') by ' + encodeURIComponent(this.userData.displayName) + (msg ? (' with message as "' + encodeURIComponent(msg) + '"') : ' without message');
        if (formSendMode === FormSendMode.APPLESS) {
          activityName = "Allow Edit (Appless)";
          activityDescription = 'Edit has been granted for appless form ' + encodeURIComponent(activeStrucuredForms.form_name) + ' (' + activeStrucuredForms.form_id + ') by ' + encodeURIComponent(this.userData.displayName) + (msg ? (' with message as "' + encodeURIComponent(msg) + '"') : ' without message');
        }
        activityData = {
          activityName,
          activityType: "structured forms",
          activityDescription
        };
        this._structureService.trackActivity(activityData);
        const deepLinking = {
          "pushType": "",
          "state": "eventmenu.forms",
          "stateParams": {},
          "tenantId": activeStrucuredForms.tenant_id,
          "tenantName": activeStrucuredForms.tenantName,
          "formSendMode": formSendMode,
          "applessMode": applessMode,
          "sentId": activeStrucuredForms.sent_id
        };
        const notificationData = {
          userid: activeStrucuredForms.recipient_id,
          senderId: this.userData.userId,
          organizationMasterId: this.userData.organizationMasterId,
          formSendMode,
          applessMode
        }
        const notificationSourceData = {
          sourceId: CONSTANTS.notificationSource.form,
          sourceCategoryId: CONSTANTS.notificationSourceCategory.formAllowEditNotification
        };
        if (formSendMode == FormSendMode.APPLESS) {
          let selectedRecipientsPolling = [];         
          selectedRecipientsPolling.push(notificationData);
          const pushMessage = `${this._toolTipService.getTranslateData('MESSAGES.FORM_EDIT_PERMISSION_GRANTED')} ${activeStrucuredForms.form_name}`;
          this._structureService.sentPushNotification(selectedRecipientsPolling, 0, pushMessage, '', deepLinking, '', '', undefined, undefined, undefined, undefined, notificationSourceData);
        } else if(result.pushMode){
          if(activeStrucuredForms.facing_new === 1) {
            notificationData.userid = activeStrucuredForms.userid;
          }          
          this._structureService.sentPushNotification([notificationData], 0,
            `${this._toolTipService.getTranslateData('MESSAGES.FORM_EDIT_PERMISSION_GRANTED')} ${activeStrucuredForms.form_name}`, '', deepLinking, '', '', undefined, undefined, undefined, undefined, notificationSourceData);
        }
      }
      let toId = +result.allowedit === 1 ? activeStrucuredForms.from_id : activeStrucuredForms.recipient_id;
      const data = {
        id: activeStrucuredForms.form_id,
        allowEdit: activeStrucuredForms.allow_edit,
        toId: activeStrucuredForms.patient_id != "0" ? (activeStrucuredForms.userid || activeStrucuredForms.recipient_id) : toId,
        form_submission_id: activeStrucuredForms.form_submission_id,
        message: msg
      };
      this._structureService.socket.emit("updateFormAllowEdit", data);
      resolve(true);
    });
  }
  confirmArchiveForm(isActive, activeStrucuredForms,data) :Promise<any> {
    return new Promise(async (resolve) => {
      if (isActive == 'draft') {
        let swal_options = {
          title: "Are you sure?",
          text: "You are going to cancel this draft",
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default",
          confirmButtonClass: "btn-warning",
          confirmButtonText: "Ok",
          closeOnConfirm: true
        }
        this._structureService.showAlertMessagePopup(
          swal_options).then(
            (confirm) => {
              resolve(true);
            });
      } else {
        let patientName;
        let patientID;
        if (activeStrucuredForms['caregiver_userid'] != null) {
          patientName = activeStrucuredForms['caregiver_displayname'];
          patientID = activeStrucuredForms['caregiver_userid'];
        } else {
          patientName = activeStrucuredForms['patientName'];
          patientID = activeStrucuredForms['from_id'];
        }
        var checkIntegrationInFromWorklist = {
          "form_id": activeStrucuredForms['form_id'],
          "tenant_id": this.userData.tenantId,
          "admissionId": data.admissionId,
          "patient_id": patientID,
          "staff_id": this.userData.userId,
          action: IntegrationType.FORM_ARCHIVE
        };
        if (this.configData.enable_sftp_integration == false) {
          this.checkIntegration(checkIntegrationInFromWorklist).subscribe((data) => {
            if (data.success) {
              setTimeout(() => {
                swal({
                  title: "Are you sure?",
                  text: "You are going to archive this form",
                  type: "warning",
                  showCancelButton: true,
                  cancelButtonClass: "btn-default",
                  confirmButtonClass: "btn-warning",
                  confirmButtonText: "Ok",
                  closeOnConfirm: true
                }, (confirm) => {
                  if (confirm) {
                    resolve(true);
                  } else {
                    resolve(false);
                  }
                });
              }, 300);
            }
          });
        } else {
          var txt = "You are going to archive this form";
          swal({
            title: "Are you sure?",
            text: txt,
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            confirmButtonText: "Ok",
            closeOnConfirm: true
          }, (confirm) => {
            if (confirm) {
              resolve(true);
            }
          })
        }
      }
    });
  }
  
  archiveSubmitedForm(allIds, activeStrucuredForms) :Promise<any> {
    return new Promise(async (resolve) => {
      const userDataConfig = JSON.parse(this._structureService.userDataConfig);
      if (activeStrucuredForms.formStatus == 'draft') {
        activeStrucuredForms.isActive = activeStrucuredForms.formStatus;
        activeStrucuredForms.downloadTime = new Date().toLocaleString();
        // TODO: esi_code_for_patient_identity decommisioned, remove usage if it is not causing any issue
        activeStrucuredForms.config_identity_value = this.userData.config.esi_code_for_patient_identity;
        activeStrucuredForms.config_staff_identity = this.userData.config_replica.esi_code_for_staff_identity;
        activeStrucuredForms.config_staff_name = this.userData.config_replica.esi_code_for_staff_name;
        activeStrucuredForms.enable = this.userData.config.enable_progress_note_integration;
        activeStrucuredForms.deleted_by = this.userData.userId;
        this.archiveDraftForm(activeStrucuredForms).then((result: any) => {
          if (result.status == 1 || result.status == "1") {
            this._structureService.notifyMessage({
              messge: 'Successfully canceled the draft of ' + activeStrucuredForms.form_name,
              delay: 1000,
              type: 'success'
            });
            var activityData: any = {};
            activityData = {
              activityName: "Cancel Draft Form - collaborate enabled",
              activityType: "structured forms"
            };
            activityData.activityDescription = this.userData.displayName + " canceled draft form " + activeStrucuredForms.form_name + " (formId " + activeStrucuredForms.form_id + ",sendId " + activeStrucuredForms.sent_id;
            this._structureService.trackActivity(activityData);
          } else {
            var activityData: any = {};
            activityData = {
              activityName: "Cancel Draft Form -collaborate enabled",
              activityType: "structured forms"
            };
            activityData.activityDescription = this.userData.displayName + " failed canceled draft form " + activeStrucuredForms.form_name + " (formId " + activeStrucuredForms.form_id + ",sendId " + activeStrucuredForms.sent_id;
            this._structureService.trackActivity(activityData);
            this._structureService.notifyMessage({
              messge: 'Failed Cancel from draft forms',
              delay: 1000,
              type: 'warning'
            });
          }
          this._structureService.socket.emit("sendFormsToRecipients", [{
            senderId: this.userData.userId
          }]);
          resolve(result);
        });
      } else {
        if (allIds.length == 0) {
          activeStrucuredForms.isActive = activeStrucuredForms.formStatus;
          activeStrucuredForms.downloadTime = new Date().toLocaleString();
          activeStrucuredForms.createdOnNew = activeStrucuredForms.senton;
          this.archiveSubmittedForm(activeStrucuredForms).then((result: any) => {
            var activityData: any = {};
            var notifyResult: any = result;
            if (result.status == '1') {
              if (notifyResult.defaultfilingCenter) {
                activityData.activityName = "Form Copied to Filing Center";
                activityData.activityType = "structured forms";
                activityData.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") successfully and copied to filing center named " + notifyResult.defaultfilingCenter;
                this._structureService.trackActivity(activityData);
              }
              if (notifyResult.defaultFromFilingCenterarchivejson && userDataConfig.enable_progress_note_integration == 1) {
                if (notifyResult.defaultFromFilingCenterarchivejson != false && notifyResult.identity_value_Patient != '' || notifyResult.identity_value_Patient != "" || notifyResult.identity_value_Patient != null && notifyResult.patientAssociation == true &&
                  notifyResult.CPRUSERNO != '' || notifyResult.CPRUSERNO != null &&
                  notifyResult.CPRUSERNAME != null || notifyResult.CPRUSERNAME != '') {
                  activityData.activityName = "Form Copied to Filing Center as JSON";
                  activityData.activityType = "structured forms";
                  activityData.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") successfully and copied the JSON file to filing center named " + notifyResult.defaultFromFilingCenterarchivejson;
                  this._structureService.trackActivity(activityData);
                }
                if (notifyResult.defaultFromFilingCenterarchivejson != false && (notifyResult.identity_value_Patient == '' || notifyResult.identity_value_Patient == "" || notifyResult.identity_value_Patient == null || notifyResult.patientAssociation == false ||
                  notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null ||
                  notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null)) {
                  activityData.activityName = "Failed Form Copy to Filing Center as JSON";
                  activityData.activityType = "structured forms";
                  var messageData = '';
                  var messageData1 = '';
                  if (notifyResult.patientAssociation == false && notifyResult.staffFacing == "true") {
                    messageData1 = "there is no patient associated with this form";
                  }
                  if ((notifyResult.identity_value_Patient == '' || notifyResult.identity_value_Patient == null || notifyResult.identity_value_Patient == "") && (notifyResult.patientAssociation == true || notifyResult.staffFacing == "false")) {
                    messageData += ", MRN";
                    if (notifyResult.patient_name && notifyResult.patient_name != "") {
                      messageData += " of " + notifyResult.patient_name;
                    }
                    console.log("in MRN", messageData);
                  }
                  let f1 = 0;
                  let f2 = 0;
                  if (notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null) {
                    f1 = 1;
                    messageData += ", USERNO";
                  }
                  if (notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null) {
                    f2 = 1;
                    messageData += ", USERNAME";
                  }
                  if ((f1 == 1 || f2 == 1) && notifyResult.staff_name && result.staff_name != "") {
                    messageData += " of " + notifyResult.staff_name;
                  }
                  if (notifyResult.patientAssociation == false && notifyResult.staffFacing == "false") {
                    messageData += "";
                  }
                  var removeComa = messageData.charAt(0);
                  if (removeComa == ',') {
                    messageData = messageData.substring(1);
                  }
                  if (messageData1 && messageData) {
                    var finalMessage = 'Generating Progress Note for Form ' + notifyResult.form_name + ' failed due to ' + messageData1 + ' and missing  (' + messageData + ')';
                  } else if (messageData1) {
                    var finalMessage = 'Generating Progress Note for Form ' + notifyResult.form_name + ' failed due to ' + messageData1;
                  } else if (messageData) {
                    var finalMessage = 'Generating Progress Note for Form ' + notifyResult.form_name + ' failed due to missing (' + messageData + ')';
                  } else {
                    var finalMessage = '';
                  }
                  if (finalMessage) {
                    activityData.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") failed to copy the JSON file to filing center named " + notifyResult.defaultFromFilingCenterarchivejson + "because" + messageData;
                    this._structureService.trackActivity(activityData);
                  }
                }
              }
              if (activeStrucuredForms.formStatus == 'completed') {
                this._structureService.notifyMessage({
                  messge: 'Successfully archived the completed form - ' + result.form_name,
                  delay: 1000,
                  type: 'success'
                });
                activityData.activityName = "Archive Completed Form";
                if(notifyResult.notifyUsers) {
                  this.sendPolling('completed', notifyResult.notifyUsers, activeStrucuredForms);
                }
                if (notifyResult.statusfailed) {
                  var txt = "Integration on Archive action failed. You can restore form from archive and try archive again.";
                  swal({
                    title: "Integration Failure",
                    text: txt,
                    type: "error",
                    showCancelButton: false,
                    cancelButtonClass: "btn-default",
                    confirmButtonClass: "btn-warning",
                    confirmButtonText: "OK",
                    closeOnConfirm: true
                  }, (confirm) => {
                    if (confirm) {
                      activityData.activityName = "Integration on Archive operation failed";
                      activityData.activityType = "structured forms";
                      activityData.activityDescription = this.userData.displayName + " Integration on Archive operation failed - clicked go back - FormName :" + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ")  User clicked OK option from the Archive operation failed popup";
                      this._structureService.trackActivity(activityData);
                    } else {
                      activityData.activityName = "Integration on Archive operation failed";
                      activityData.activityType = "structured forms";
                      activityData.activityDescription = this.userData.displayName + " Integration on Archive operation failed - clicked  continue anyway Cancel - FormName :" + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ")  User clicked cancel option from the Archive operation failed popup";
                      this._structureService.trackActivity(activityData);
                    }
                  })
                }
              } else {
                activityData.activityName = "Archive Pending Form";
                this._structureService.notifyMessage({
                  messge: 'Successfully archived the pending form - ' + result.form_name,
                  delay: 1000,
                  type: 'success'
                });
                this.sendPolling('pending', result.notifyUsers, activeStrucuredForms);
              }
              activityData.activityType = "structured forms";
              activityData.activityName = "Archived Form successfully";
              activityData.activityDescription = this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") successfully";
              this._structureService.trackActivity(activityData);
              //this.removeScopeFormOnArchive();
            } else {
              activityData = {
                activityName: "Archive Completed Form",
                activityType: "structured forms",
                activityDescription: this.userData.displayName + " archived form " + result.form_name + " (" + result.form_id + ") failed"
              };
              this._structureService.trackActivity(activityData);
              this._structureService.notifyMessage({
                messge: this.userData.displayName + " archived form " + result.form_name + " (" + result.form_id + ") failed",
                delay: 1000,
                type: 'danger'
              });
            }
            resolve(result);
          });
        } else {
          this.archiveSubmittedMultipleForm(allIds).then((resultN: any) => {
            var successCount = 0;
            var failureCount = 0;
            var totalCount = 0;
            var successResult = resultN.filter((result) => {
              return result.status == '1';
            })
            var failureResult = resultN.filter((result) => {
              return result.status != '1';
            });
            var notifyCount = resultN.length;
            var successFormPendingArray = [];
            var successFormCompletedArray = [];
            var failureFormPendingArray = [];
            var failureFormCompletedArray = [];
            var filingCenterFormsArray = [];
            var successfilingCenterFormsArray = [];
            var failurefilingCenterFormsArray = [];
            var notifyUsersFull = [];
            resultN.forEach(result => {
              var activityData: any = {};
              var notifyResult: any = result;
              totalCount = totalCount + 1;
              if (result.status == '1') {
                successCount = successCount + 1;
                if (notifyResult.defaultfilingCenter) {
                  filingCenterFormsArray.push(result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") copied to filing center named " + notifyResult.defaultfilingCenter);
                }
                if (successCount == successResult.length && filingCenterFormsArray.length) {
                  activityData.activityName = "Multiple Forms Copied to Filing Center";
                  activityData.activityType = "structured forms";
                  activityData.activityDescription = this.userData.displayName + " archived forms (" + filingCenterFormsArray.join(', ') + ")";
                  this._structureService.trackActivity(activityData);
                }
                if (notifyResult.defaultFromFilingCenterarchivejson && userDataConfig.enable_progress_note_integration == 1) {
                  if (notifyResult.defaultFromFilingCenterarchivejson != false && notifyResult.identity_value_Patient != '' || notifyResult.identity_value_Patient != "" || notifyResult.identity_value_Patient != null && notifyResult.patientAssociation == true &&
                    notifyResult.CPRUSERNO != '' || notifyResult.CPRUSERNO != null &&
                    notifyResult.CPRUSERNAME != null || notifyResult.CPRUSERNAME != '') {
                    successfilingCenterFormsArray.push(this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") successfully and copied the JSON file to filing center named " + notifyResult.defaultFromFilingCenterarchivejson);
                  }
                  if (notifyResult.defaultFromFilingCenterarchivejson != false && (notifyResult.identity_value_Patient == '' || notifyResult.identity_value_Patient == "" || notifyResult.identity_value_Patient == null || notifyResult.patientAssociation == false ||
                    notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null ||
                    notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null)) {
                    activityData.activityName = "Failed Form Copy to Filing Center as JSON";
                    activityData.activityType = "structured forms";
                    var messageData = '';
                    var messageData1 = '';
                    if (notifyResult.patientAssociation == false && notifyResult.staffFacing == "true") {
                      messageData1 = "there is no patient associated with this form";
                    }
                    if ((notifyResult.identity_value_Patient == '' || notifyResult.identity_value_Patient == null || notifyResult.identity_value_Patient == "") && notifyResult.patientAssociation == true) {
                      messageData += ", MRN";
                      if (notifyResult.patient_name && notifyResult.patient_name != "") {
                        messageData += " of " + notifyResult.patient_name;
                      }
                    }
                    let f1 = 0;
                    let f2 = 0;
                    if (notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null) {
                      f1 = 1;
                      messageData += ", USERNO";
                    }
                    if (notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null) {
                      f2 = 1;
                      messageData += ", USERNAME";
                    }
                    if ((f1 == 1 || f2 == 1) && notifyResult.staff_name && result.staff_name != "") {
                      messageData += " of " + notifyResult.staff_name;
                    }
                    if (notifyResult.patientAssociation == false && notifyResult.staffFacing == "false") {
                      messageData += "";
                    }
                    var removeComa = messageData.charAt(0);
                    if (removeComa == ',') {
                      messageData = messageData.substring(1);
                    }
                    if (messageData1 && messageData) {
                      var finalMessage = 'Generating Progress Note for Form ' + notifyResult.form_name + ' failed due to ' + messageData1 + ' and missing  (' + messageData + ')';
                    } else if (messageData1) {
                      var finalMessage = 'Generating Progress Note for Form ' + notifyResult.form_name + ' failed due to ' + messageData1;
                    } else if (messageData) {
                      var finalMessage = 'Generating Progress Note for Form ' + notifyResult.form_name + ' failed due to missing (' + messageData + ')';
                    } else {
                      var finalMessage = '';
                    }
                    if (finalMessage) {
                      failurefilingCenterFormsArray.push(this.userData.displayName + " archived form " + result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ") failed to copy the JSON file to filing center named " + notifyResult.defaultFromFilingCenterarchivejson + "because" + messageData);
                    }
                  }
                }
                if (successCount == successResult.length && successfilingCenterFormsArray.length) {
                  activityData.activityName = "Multiple Forms Copied to Filing Center as JSON";
                  activityData.activityType = "structured forms";
                  activityData.activityDescription = successfilingCenterFormsArray.join(', ');
                  this._structureService.trackActivity(activityData);
                }
                if (successCount == successResult.length && failurefilingCenterFormsArray.length && finalMessage) {
                  activityData.activityName = "Multiple Forms failed to Copy to Filing Center as JSON";
                  activityData.activityType = "structured forms";
                  activityData.activityDescription = failurefilingCenterFormsArray.join(', ');
                  this._structureService.trackActivity(activityData);
                }
                var deletedFlag = 0;
                var senderFlag = 0;
                var recipientFlag = 0;
                notifyResult.notifyUsers.forEach(element => {
                  notifyUsersFull.push(element);
                  if (element.userid == this.userData.userId) {
                    deletedFlag = 1;
                    console.log(deletedFlag, "deletedFlag");
                  }
                  if (element.userid == result.userid) {
                    recipientFlag = 1;
                    console.log(recipientFlag, "recipientFlag");
                  }
                  if (element.userid == result.from_id) {
                    senderFlag = 1;
                    console.log(senderFlag, "senderFlag");
                  }
                });
                if (deletedFlag == 0) {
                  notifyUsersFull.push({
                    senderId: this.userData.userId
                  });
                }
                if (recipientFlag == 0 && result.userid && result.userid != this.userData.userId) {
                  notifyUsersFull.push({
                    userid: result.userid
                  });
                }
                if (senderFlag == 0 && result.from_id && result.from_id != this.userData.userId && result.from_id != result.userid) {
                  notifyUsersFull.push({
                    userid: result.from_id
                  });
                }
                if (activeStrucuredForms.formStatus == 'completed') {
                  successFormCompletedArray.push(result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ")");
                } else {
                  successFormPendingArray.push(result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ")");
                }
                if (successCount == successResult.length && successFormCompletedArray.length) {
                  activityData.activityName = "Multiple Archive Completed Forms";
                  activityData.activityType = "structured forms";
                  activityData.activityDescription = this.userData.displayName + " archived completed forms " + successFormCompletedArray.join(', ') + " successfully";
                  this._structureService.trackActivity(activityData);
                }
                if (successCount == successResult.length && successFormPendingArray.length) {
                  activityData.activityName = "Multiple Archive Pending Forms";
                  activityData.activityType = "structured forms";
                  activityData.activityDescription = this.userData.displayName + " archived pending forms " + successFormPendingArray.join(', ') + " successfully";
                  this._structureService.trackActivity(activityData);
                }
              } else {
                failureCount = failureCount + 1;
                if (activeStrucuredForms.formStatus == 'completed') {
                  failureFormCompletedArray.push(result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ")");
                } else {
                  failureFormPendingArray.push(result.form_name + " (formId " + result.form_id + ",sendId " + result.sent_id + ")");
                }
                if (failureCount == failureResult.length && failureFormCompletedArray.length) {
                  activityData = {
                    activityName: "Archive Completed Form",
                    activityType: "structured forms",
                    activityDescription: this.userData.displayName + " archived completed forms " + failureFormCompletedArray.join(', ') + " were failed"
                  };
                  this._structureService.trackActivity(activityData);
                }
                if (failureCount == failureResult.length && failureFormPendingArray.length) {
                  activityData = {
                    activityName: "Archive Completed Form",
                    activityType: "structured forms",
                    activityDescription: this.userData.displayName + " archived pending forms " + failureFormPendingArray.join(', ') + " were failed"
                  };
                  this._structureService.trackActivity(activityData);
                  this._structureService.notifyMessage({
                    messge: this.userData.displayName + " archived pending forms " + failureFormPendingArray.join(', ') + " were failed",
                    delay: 1000
                  });
                }
              }
            });
            if (activeStrucuredForms.formstatus == 'completed' && totalCount == notifyCount) {
              this._structureService.notifyMessage({
                messge: 'Successfully archived the completed forms',
                delay: 1000,
                type: 'success'
              });
              if (successCount && notifyUsersFull.length) {
                this.sendCommonPollingMultiple(notifyUsersFull);
              }
            } else if (totalCount == notifyCount) {
              this._structureService.notifyMessage({
                messge: 'Successfully archived the pending forms',
                delay: 1000,
                type: 'success'
              });
              if (successCount && notifyUsersFull.length) {
                this.sendCommonPollingMultiple(notifyUsersFull);
              }
            }
            resolve(resultN);
          });
        }
      }
    });
  }
  sendPolling(type, notifyUsers, activeStrucuredForms) {
    var selectedRecipientsPolling = [];
    var deletedFlag = 0;
    var senderFlag = 0;
    var recipientFlag = 0;
    notifyUsers.forEach(element => {
      selectedRecipientsPolling.push(element);
      if (element.userid == this.userData.userId) {
        deletedFlag = 1;
      }
      if (element.userid == activeStrucuredForms.userid) {
        recipientFlag = 1;
      }
      if (element.userid == activeStrucuredForms.from_id) {
        senderFlag = 1;
      }
    });
    if (deletedFlag == 0) {
      selectedRecipientsPolling.push({
        senderId: this.userData.userId,
        userid: activeStrucuredForms.recipient_id
      });
    }
    if (recipientFlag == 0 && activeStrucuredForms.userid && activeStrucuredForms.userid != this.userData.userId) {
      selectedRecipientsPolling.push({
        userid: activeStrucuredForms.userid,
        senderId: activeStrucuredForms.from_id
      });
    }
    if (senderFlag == 0 && activeStrucuredForms.from_id && activeStrucuredForms.from_id != this.userData.userId && activeStrucuredForms.from_id != activeStrucuredForms.userid) {
      selectedRecipientsPolling.push({
        userid: activeStrucuredForms.from_id,
        senderId: activeStrucuredForms.recipient_id
      });
    }
    this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", selectedRecipientsPolling, ' ', 'form'); //Need to commnd after finish form inbox
    this._structureService.socket.emit("sendFormsToRecipients", selectedRecipientsPolling);
  }
  sendCommonPollingMultiple(notifyUsers) {
    notifyUsers = this.removeDuplicates(notifyUsers, 'userid');
    notifyUsers = this.removeDuplicates(notifyUsers, 'senderId');
    notifyUsers = notifyUsers.filter(a => a.userid != a.senderId);
    this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", notifyUsers, '', 'form'); //Need to commnd after finish form inbox
    this._structureService.socket.emit("sendFormsToRecipients", notifyUsers);
  }
  removeDuplicates(arr, prop) {
    let obj = {};
    return Object.keys(arr.reduce((prev, next) => {
      if (!obj[next[prop]]) obj[next[prop]] = next;
      return obj;
    }, obj)).map((i) => obj[i]);
  }

  /**
   * Function to get patient id to resend the forms
   * @param formDetails form details object
   * @returns patientId
   */
    getPatientIdForResendForms(formDetails)
    {
      let patientId = formDetails.patient_id;
      //To handle the Patient Id instead of Caregiver Id in patient facing forms 
      if(formDetails.form_type == "Patient Facing")
      {
        if(!isBlank(formDetails.patient_associated_id) && formDetails.patient_associated_id != 0)
        { 
          patientId = formDetails.patient_associated_id;
        } 
        else if((isBlank(patientId) || patientId == 0))
        { 
          patientId = formDetails.recipient_id;
        } 
      }
      return patientId;
    }

    /**
     * Function to resend appless link to selected recipients
     * @param recipients Recipients list choose from the model as array of objects
     */
    resendFormToRecipients(recipients, formDetails, patientId = '') {             
      //Create request payload for resend appless 
      const postData = {
          "patientId": !isBlank(patientId) ? patientId : this.getPatientIdForResendForms(formDetails),
          "send_id": formDetails.sent_id,
          "form_id": formDetails.form_id,
          "submission_id": formDetails.form_submission_id,
          "updateRecipients": true,
          "completedFormRecipients":  recipients.map((item) =>  item.userId ).join(',')
      };

      //call the API to resend the appless link and update recipients
      this.httpService
          .doPost(APIs.sendCompletedFormsToRecipients, postData)
          .subscribe((res) => {
              if(res.success) {
                  this._structureService.notifyMessage({
                      messge:  this._toolTipService.getTranslateDataWithParam(
                          'SUCCESS_MESSAGES.FORM_DOCUMENT_RESENT_SUCCESS', {type: CONSTANTS.contentType.form}
                      ),
                      delay: 1000,
                      type: 'success'
              });
          }else{
              this._structureService.notifyMessage({
                  messge:  this._toolTipService.getTranslateData(
                      'ERROR_MESSAGES.COMMON_ERROR_MSG'
                  ),
                  delay: 1000,
                  type: 'danger'
            });
            const activityData = {
              activityName: "Form worklist",
              activityType: "Resend completed forms",
              activityDescription: "Sending completed forms failed, " + JSON.stringify(postData)
            };
            this._structureService.trackActivity(activityData);
          }
      });
  }
  isViewAllFormEntriesPrivilegeEnabled(): boolean {
    return (this._structureService.getCookie('userPrivileges').indexOf('viewFormEntries') !== -1);
  }
  cancelForm(formData, list) : Observable<any> {
    return new Observable<any>((observer) => {
      let text = this._toolTipService.getTranslateData('MESSAGES.CANCEL_FORM');
      if(Array.isArray(formData)) {
        text = this._toolTipService.getTranslateData('MESSAGES.CANCEL_MULTIPLE_FORMS');
      } else if(formData.formStatus.toLowerCase() === 'pending') {
        text = this._toolTipService.getTranslateDataWithParam('MESSAGES.CANCEL_PENDING_DRAFT_FORM',{'formName': formData.form_name});
      } 
      const config = {
        title: this._toolTipService.getTranslateData('MESSAGES.ARE_YOU_SURE' ),
        text: text
      };
      this._structureService.showAlertMessagePopup(config).then((confirm) => {
          if (confirm) {
            NProgress.start();
            let payload: any = [];
            if(Array.isArray(formData)) {
              formData.filter((elem)=>{
                payload.push({
                  "form_id": elem.form_id,
                  "form_name": elem.form_name,
                  "sent_id": elem.sent_id,
                  "recipient_id": elem.recipient_id,
                  "id": elem.id,
                  "form_submission_id": elem.form_submission_id
                });
              });
            } else {
                payload.push({
                "form_id": formData.form_id,
                "form_name": formData.form_name,
                "sent_id": formData.sent_id,
                "recipient_id": formData.recipient_id,
                "id": formData.id,
                "form_submission_id": formData.form_submission_id
              });
            }
            this.httpService.doPut(APIs.cancelForm, payload)
            .subscribe((res) => {
              NProgress.done();
              let activityData: any = {};
              activityData.activityType = "structured forms";
              activityData.activityName = `Cancel Pending Form ${list}`;
              if(Object.keys(res).length === 1) {
                if(res[formData.id].success) {
                  this._structureService.notifyMessage({
                      messge:  this._toolTipService.getTranslateDataWithParam(
                          'SUCCESS_MESSAGES.FORM_CANCELED_SUCCESS', {formName: formData.form_name}
                      ),
                      type: 'success'
                  });
                  activityData.activityDescription = this.userData.displayName + " canceled form " + formData.form_name + " (formId " + formData.form_id + ",sendId " + formData.sent_id + ",receipientId " + formData.recipient_id + ") successfully";
                  observer.next(res);
                } else {
                  this._structureService.notifyMessage({
                    messge:  this._toolTipService.getTranslateDataWithParam(
                        'ERROR_MESSAGES.FORM_CANCELED_ERROR', {formName: formData.form_name}
                    ),
                    type: 'success'
                  });
                  activityData.activityDescription = this.userData.displayName + " canceled form " + formData.form_name + " (formId " + formData.form_id + ",sendId " + formData.sent_id + ",receipientId " + formData.recipient_id + ") failed and the api response is " + res.status.message;
                  observer.next({});
                }
              } else {
                observer.next(res);
              } 
              this._structureService.trackActivity(activityData);
              observer.complete();
            });
          } else {
            observer.next({});
            observer.complete();
          }
      });
    });
  }
    /**
   * Function to complete Form
   * @param formDetails FormDetails required to complete the form
   * @param onSuccessCallback Callback on success
   */
  markFromAsComplete(formDetails) {             
    // Create request payload for complete 
    const postData = {
      "form_name": formDetails.form_name,
      "form_id": formDetails.form_id,
      "id": formDetails.id,
      "recipient_id": formDetails.recipient_id,
      "sent_id": formDetails.sent_id,
      "siteId": formDetails.siteId,
      "staff_submission_id": formDetails.staff_submission_id
    };
    return new Promise<boolean>((resolve, reject) => {
      this.httpService.doPut(APIs.moveFormToComplete, postData).subscribe((res) => {
        if(res.success) {
          this._structureService.notifyMessage({
            messge: this._toolTipService.getTranslateData('SUCCESS_MESSAGES.FROM_COMPLETED_SUCCESSFULLY'),
            delay: 1000,
            type: 'success'
          });
          resolve(true);
        } else {
          reject(false);
        }
      });
    });
  }
  get _structureService() {
    return this.structureService;
  }
}

