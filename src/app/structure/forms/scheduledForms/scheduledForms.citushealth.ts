import {
  Compo<PERSON>,
  On<PERSON>ni<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>
} from "@angular/core";
import { DatePipe } from "@angular/common";
import { StructureService } from '../../structure.service';
import { FormsService } from '../forms.service';
import { SignaturepPipe } from '../../signatures/signaturep.pipe';
import { SharedService } from '../../shared/sharedServices';
import { Store, StoreService } from 'app/structure/shared/storeService';
import { Subject } from 'rxjs';
import { CONSTANTS, DateFormat } from "app/constants/constants";
import { isNull } from "util";
import { ToolTipService } from "app/structure/tool-tip.service";
import { isBlank } from "app/utils/utils";
import { StaticDataService } from "app/structure/static-data.service";
import { convertTimeZoneDateTimeToUTCIso,convertToMillisecondTime } from '../../../utils/utils';
import { userFilterPipe } from "app/structure/inbox/inbox-modal-filter";

const jstz = require('jstz');
declare const $: any;
declare const swal: any;
declare const moment :any;
const timezone = jstz.determine();
@Component({
  selector: "scheduled-form",
  templateUrl: "scheduledForms.citushealth.html",
  styleUrls: ['./scheduledForms.scss'],
  providers: [DatePipe, SignaturepPipe]
})
export class ScheduledFormComponent implements OnInit {
  userConfig;
  environment:string = this._structureService.environment;
  userData:any='';
  dTable;
  scheduledFormsList:any= [];
  activeRecord: any;
  dataLoadingMsg = true;
  isActive = false;
  userRole;
  scheduledForms:any={};
  formsTagList:any=[];
  selectedOptions:any={};
  scheduledFormRecipients:any=[];
  userDataUpdateSubs:any;
  selectedFormTypeId:any;
  recipientLoading = true;
  scheduledFormTimmer:any='';
  scheduleSending=false;
  totalCountScheduled :any;
  scheduledFormRecipientsArray:any=[];
  datam;
  searchResetFlag = 0;
  editScheduledForm:boolean=false;
  advanceOptionCommented = true;
  editScheduleData:any;
  change=false;
  scheduleButtonName:String="Schedule";
  scheduleFormHeaderTittle:String="Schedule Forms";
  formRecipientFilterargs:object={status:"1"};
  refreshDatatableButton = false;
  refreshDatatableErrorCount=0;
  crossTenantChangeSubscriber:any;
  selectedRecipients = [];
  mdlRecName = null;
  mdlRecDisplayName:any = '';
  associatePatientLoading=true;
  selectedFormTypeIndex;
  tenantUsers: any = [];
  patientTags = [];
  isStaffFacing;
  stafffillfirst;
  receipientCount;
  selectedRecipientNames: any = [];
  selectedRecipientsData: any =[];
  siteIds : any;
  hideSiteSelection : any;
  selectedSiteId : any;
  selectedRecipientsSiteId : any;
  editSiteId : any;
  eventsSubject: Subject<void> = new Subject<void>();
  searchValue = '';
  timezones: any;
  labelSite = '';
  labelSites = '';
  filterSiteLabel = '';
  isTableLoaded = false;
  constructor(private datePipe: DatePipe,public _structureService: StructureService,private _formsService: FormsService,
    renderer: Renderer,elementRef: ElementRef,private _sharedService:SharedService, private toolTipService: ToolTipService,
    private storeService: StoreService, private staticDataService: StaticDataService, private userFilter: userFilterPipe) {
    this.labelSite = this.toolTipService.getTranslateData(this.staticDataService.getSiteLabelBasedOnMultiAdmission('SITE'));
    this.labelSites = this.staticDataService.getSiteLabelBasedOnMultiAdmission('SITES');
    this.filterSiteLabel = this.staticDataService.getSiteLabelBasedOnMultiAdmission('FILTER_BY_SITES');
    renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
      console.log("event.target.====>",event.target);
      console.log("event.target.==class==>",$(event.target).attr('class'));
      let eventClass = $(event.target).attr('class');
      if(event.target.id == 'more_recipients') {
        $("body .openmore_recipients").css("display","block");
        $("body .more-recipients").css("display","none");
        $("body .closemore_recipients").css("display","none");

        var attributes =$(event.target).attr('data-moreId');
        console.log("attributes===>"+attributes);
        $("body #"+attributes).css("display","block");
        $('table tr td').find('.more-recipients#'+attributes).attr('style','display:block')
        $("body .openmore_recipients"+attributes).css("display","none");
        $("body .closemore_recipients"+attributes).css("display","block");
      }else if(event.target.id == "closemore_recipients"){
        var attributes =$(event.target).attr('data-moreId');
        $("body #"+attributes).css("display","none");
        $('table tr td').find('.more-recipients#'+attributes).attr('style','display:none');
        $("body .openmore_recipients"+attributes).css("display","block");
        $("body .closemore_recipients"+attributes).css("display","none");

      }else if(event.target.id == 'delete-scheduled-form'){

        var scheduledId =$(event.target).attr('data-rowId');
        console.log("attributes===>"+scheduledId);
        this.deleteScheduledForms(scheduledId);
      }else if(event.target.id == 'edit-scheduled-form'){
        this.selectedRecipients = [];
        this.selectedRecipientsData = [];
        $(".tag-span").remove();
        var scheduledId =$(event.target).attr('data-rowId');
        this.editScheduledForms(scheduledId);
      }else if(event.target.id == 'stop-scheduled-form'){
        console.log("stop-scheduled-form===========");
        var scheduledId =$(event.target).attr('data-rowId');
        this.stopAndStartScheduledForms(scheduledId,2);
      }else if(event.target.id == 'start-scheduled-form'){
        console.log("start-scheduled-form===========");
        var scheduledId =$(event.target).attr('data-rowId');
        this.stopAndStartScheduledForms(scheduledId,1);
      }else if(eventClass && (eventClass.indexOf('fa-calendar')>-1 || eventClass.indexOf('input-group-addon')>-1)){
       let eventWidth= $("#schedule-date-picker1 input")[0].clientWidth;
       console.log("eventWidth===>"+eventWidth);
        $("#schedule-date-picker1 .bootstrap-datetimepicker-widget").css("max-width",eventWidth+"px");
        $("#schedule-enddate-picker1 .bootstrap-datetimepicker-widget").css("max-width",eventWidth+"px");
      }
      if(event.target.id !=='submit-schedule'){
        this.errorReset();
      }
      if(event.target.id !== "closemore_recipients" && event.target.id !== 'more_recipients'){
        $("body .openmore_recipients").css("display","block");
        $("body .more-recipients").css("display","none");
        $("body .closemore_recipients").css("display","none");
      }        
      if (event.target.id == 'searchBScheduled') {
        var value = $("#staff-list_filter input").val();
        if (value) {
          this.searchResetFlag = 0;
          this.dTable.search(value).draw();
        }
        else {
          this.loadScheduleFormsListTable('refresh');
        }
      }
      if(eventClass == "remove"){
          this.removeSelectedRecipient(event.target.id);
      }
      if (event.target.id == 'resetBScheduled') {
        this.loadScheduleFormsListTable('refresh');
        $(".searchBScheduled").prop('disabled', true);
      }  
      var idDetails = ['tagsInput', 'recipient-li', 'recipient-ul', 'recipient-search'];
      if (!idDetails.includes(event.target.id) && event.target.className.indexOf('recipient-li') == -1) {
        let clear = false;
        let from = "";
        if ($('#recipient-ul').css('display') == 'block') {
          from = "R";
        }
        console.log("render :call enableOrDisableUiLI.......");
        this.enableOrDisableUiLI(false, clear, from);
      } else {
        if (event.target.id == 'associate-search-input' && $('#recipient-ul').css('display') == 'block') {
          this.enableOrDisableUiLI(false, false, 'R');
        }
      }
    });
    this.crossTenantChangeSubscriber = this._sharedService.crossTenantChange.subscribe(
      (onInboxData) => {
          this.ngOnInit();
      }
    );
  }
  ngOnInit() {
    this.userConfig = this._structureService.getUserdata().config; 
    
    this.userData = JSON.parse(this._structureService.userDetails);
    console.log("this.userData===>",this.userData)
    //this.selSelectOptions();

    $('#form_type').select2({
      placeholder: 'Select Form(s)',
    });

    $('#form_type').on('select2:open', function (event) {
     
      $('.select2-search__field').attr('id','srch_form');
    });
 

    


    $('#taggedUser').select2({
      placeholder: 'Select Recipient(s)',
    });


    $('#taggedUser').on('select2:open', function (event) {
     
      $('.select2-search__field').attr('id','srch_usr');
    });

    


    this.scheduleTimmerSelect();
    this.resetSelectOption();
    // this.refreshScheduleFormTable();
    this._formsService.getAllFormNames({"roleId": this.userData.roleId}).then((result:any) => {
      //,"roleId":this.userData.roleId
      console.log("getAllFormNames result===>",result);
      var error = (result['errors'])?true:false;
      if(!error){
        this.formsTagList = [];
        var assignedRoles = this.userData.assignedRoles.split(',').map( Number );
        var secondaryRoleId = assignedRoles.filter(x => !this.userData.roleId.includes(x));
        if(secondaryRoleId && secondaryRoleId.length == 1){
          secondaryRoleId = Number(secondaryRoleId);
        }
        console.log("assignedRoles",assignedRoles,this.userData.roleId,secondaryRoleId);
        this.formsTagList = result.filter(item => item.stafFacing=="false" && (item.visibleToRoles.includes(parseInt(this.userData.roleId)) || item.visibleToRoles.includes(parseInt(secondaryRoleId))));
        console.log("this.formsTagList====>",this.formsTagList);
      }else{
        console.log("getAllFormNames : graphql error ")
      }
    }).catch((ex) => {
      console.log("result errorrrrrrrrrrrr",ex);
      //this.dataLoadingMsg = false;
    });
    this.userDataUpdateSubs = this._sharedService.userDataUpdate.subscribe(
      (userData)=>{
        this.fetchRecipientWithFormId(this.selectedFormTypeId,false);
    });

    $('#form_type').on('change', (e) => {
      this.recipientLoading = false;
      this.selectedFormTypeIndex = $(e.target)[0].selectedOptions[0].id; 
      if(this.selectedFormTypeIndex && this.selectedFormTypeIndex > -1){
        var selectedFormType = this.formsTagList[this.selectedFormTypeIndex];
        this.selectedFormTypeId = selectedFormType.tagId;
        this.selectedOptions.form = selectedFormType.name;
        this.selectedOptions.formId = selectedFormType.id;
        console.log("selectedFormType",selectedFormType);
        if(this.selectedFormTypeId){
          this.associatePatientLoading= false;
          this.isStaffFacing = selectedFormType.stafFacing;        
        }   
      }  
    });

    $("#taggedUser").on('change', (e) => {
      var m = $(e.target).val();        
      var selectedRecipients= [];
      this.selectedRecipients = m;
      if(this.selectedRecipients) {
        this.selectedRecipients.forEach(element => {
          var member  = { id:""};
          var id = element.substr(element.indexOf(":") + 1);
          var index = element.substring(0, element.indexOf(":"));
          id = id.replace(/'/g, "");
          member.id = id.replace(/\s/g, '');                
          selectedRecipients.push(Number(member.id));

          index = index.replace(/'/g, "");
          index = index.replace(/\s/g, '');
          
        });
      }
      setTimeout(() => {
        this.selectedRecipients = selectedRecipients.map(String);
        this.selectedOptions.recipients = this.scheduledFormRecipients.filter((val)=> {
          return this.selectedRecipients.indexOf(val.userId) > -1
        });
      }, 500);
    });
    var self=this;
    $(function () { // DOM ready
      // ::: TAGS BOX    
      $("#tagsInput").on({
        click: function () {
          var txt = this.value;//.replace(/[^a-z0-9\+\-\.\#]/ig,''); // allowed characters
          if (txt) {
            console.log("Enter focusin function....");
            self.recipientInputFocus();
          }
        },
        focusout: function () {
        },
        keyup: function (ev) {
          var txt = this.value;//.replace(/[^a-z0-9\+\-\.\#]/ig,'');
          if (txt && /(188|13)/.test(ev.which)) { self.setRecipient(txt,self.isStaffFacing);/*$(this).focusout();*/ }
        }
      });
    });
    this._structureService.getAllTimeZones().then((timezones: any) => {
      if (timezones && timezones.length) {
        this.timezones = timezones;   
      }
    });
  }
  ngOnDestroy() {
    if(this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
    if (this.userDataUpdateSubs) {
      this.userDataUpdateSubs.unsubscribe();
    }
  }
  refreshScheduleFormTable(){
    this.dataLoadingMsg = true;
    this.refreshDatatableButton = false;
    let scheduleId =0;
    this.scheduledFormsList = [];
    this._structureService.getScheduledForms(scheduleId).then(( data ) => {//.subscribe((data) => {
      console.log("gggggggggggggggggggg",data);
      //console.log(data['graphQLErrors'].length)
      var error = (data['graphQLErrors'] && data['graphQLErrors'].length)?true:false;
      if(!error){
        this.scheduledFormsList = JSON.parse(JSON.stringify(data['getScheduledForms']));
        console.log("this.scheduledFormsList===========>",this.scheduledFormsList);
        this.loadScheduleFormsListTable();
        this.refreshDatatableButton = false;
      }else{
        console.log(`getScheduledForms : graphql error ${this.refreshDatatableErrorCount}`);
        if(this.refreshDatatableErrorCount <3){
          var notify = $.notify(`We cant fetch your data ,Please try again.`);
          setTimeout(function () {
            notify.update({ 'type': 'warning', 'message': `<strong>We cant fetch your data ,Please try again.</strong>`});
          });
          this.refreshDatatableButton = true;
        }else{
          var notify = $.notify(`We cant fetch your data ,Please contact Adminstrator.`);
          setTimeout(function () {
            notify.update({ 'type': 'warning', 'message': `<strong>We cant fetch your data ,Please contact Adminstrator.</strong>`});
          });
          this.refreshDatatableButton = false;
        }
        this.loadScheduleFormsListTable();
        this.refreshDatatableErrorCount++;
        //this.scheduledFormsList = [];
      }
    });
  }
  selSelectOptions(condition=false){
    if(condition){
      $('body .select2-forms').val(null).trigger('change');
      return true;
    }

    $('body .select2-forms').select2({
      placeholder: 'Select Form(s)',
    });

    this.addRecipientSelect2('Select Recipient(s)');
    this.weekSelectTimmerSet(false);       
  }

  addRecipientSelect2(holder,change=false){
    if(change) {
      setTimeout(() => {
        $('#taggedUser').val(this.selectedRecipients).select2({
          placeholder: holder
        });
      }, 1000);
    } else {
      setTimeout(() => {
        $('#taggedUser').val(this.selectedRecipients);
        $('#taggedUser').select2({
          placeholder: holder,
        }).trigger('select2:select');
      }, 2000);
    }    
  }

  callChangeSetUp(change){
    console.log("callChangeSetUp ===> change========> "+change);
    if(!change && this.selectedOptions && this.selectedOptions.recipients && this.selectedOptions.recipients.length==0){
      console.log(".........................")
      setTimeout(() => {
        this.addRecipientSelect2('Select Recipient(s)',false);
      }, 100);
    }
  }

  weekSelectTimmerSet(condition){
    if(condition){
      if($(".weekselect").attr("reset") =='false'){
        $(".weekselect").attr("reset",true);
      }
     $(".weekselect").val('').trigger('change');
    }else{
      $(".weekselect").attr("reset",false);
    }
    $('body .weekselect').select2({
      placeholder: 'Select Week Day(s)',
    });
  }
  scheduleTimmerSelect(){ 
    $(".month-days").off().on('change',(e) =>{
      this.weekSelectTimmerSet(true);
      this.selectedOptions.day =  $(e.target).val();
      this.selectedOptions.weekselect = '*';
      this.setTimmer();
    });
    $(".weekselect").on('change',(e) =>{
      let condition = ($(e.target).attr('reset')=="true")?true:false;
      if(!condition){
        $(".month-days").val('');        
      }
      $(".weekselect").attr("reset",false);
      this.selectedOptions.weekselect =  $(e.target).val();
      this.selectedOptions.day = '*';
      this.setTimmer();
    });
    $(".months-every").off().on('change',(e) =>{
      this.selectedOptions.everyMonth =  $(e.target).val();
      this.setTimmer();
    });
    $(".hours").off().on('change',(e) =>{
      this.selectedOptions.hours =  $(e.target).val();
      console.log("this.selectedOptions.hou===>"+this.selectedOptions.hours)
      if(this.selectedOptions.hours && this.selectedOptions.hours !=''){
        $("#minutesSelection").css("display","block");
      }else{
        this.selectedOptions.minutes="*";
        $("#minutesSelection").css("display","none");
      }
      this.setTimmer();
    });
    $(".minutes").off().on('change',(e) =>{
      this.selectedOptions.minutes =  $(e.target).val();
      this.setTimmer();
    });
    $(".seconds").off().on('change',(e) =>{
      this.selectedOptions.seconds =  $(e.target).val();
      this.setTimmer();
    });
    $(".hour-types").off().on('change',(e) =>{
      this.selectedOptions.hourTypes =  $(e.target).val();
      this.setTimmer();
    });
    
    $("#schedule-date-picker1").datetimepicker({
      collapse:false,
      sideBySide:true,
      useCurrent:false,
      showClose:false,
      minDate : new Date(),
      format:'MM/DD/YYYY hh:mm A',
      icons: {
        date: 'fa fa-calendar',
        time: 'fa fa-clock-o',
        up: 'fa fa-chevron-up',
        down: 'fa fa-chevron-down',
        previous: 'fa fa-chevron-left',
        next: 'fa fa-chevron-right',
        today: 'fa fa-crosshairs',
        clear: 'fa fa-trash-o',
        close: 'fa fa-times'
      },
     ignoreReadonly: true
    });

    $("#schedule-date-picker1").on("dp.change", (e) => {
      if(e.date){
        console.log("ggggggggggggggggggg==>",e.date);
        console.log("e.date.format('MMMM D, YYYY')===>"+e.date.format('MM/DD/YYYY hh:mm:ss a'))
        var momentDate=moment(e.date.format('MM/DD/YYYY hh:mm a'));
        console.log("moment===>",momentDate);
        console.log("date.valueOf()===>",e.date.valueOf());
        this.selectedOptions.date = momentDate;
        console.log("this.selectedOptions.date================> ",this.selectedOptions.date);
       // $("#schedule-enddate-picker1").data("DateTimePicker").minDate(new Date(e.date.valueOf()));
        //$("#schedule-enddate-picker1").datetimepicker({ minDate : new Date(e.date.valueOf())});
        //$("#schedule-enddate-picker1").data("DateTimePicker").setStartDate(e.date);//moment(moment(startDate).format("MM/DD/YYYY hh:mm A")).format("MM/DD/YYYY hh:mm A"));
        console.log("schedule-enddate-picker1======min date set-----------");
        if(this.selectedOptions.repeat)
          $(".selectrepeatOption").trigger('change');
      }
    });
    $("#schedule-enddate-picker1").datetimepicker({
      collapse:false,
      sideBySide:true,
      useCurrent:false,
      showClose:false,
      minDate : new Date(),
      format:'MM/DD/YYYY hh:mm A',
      icons: {
        date: 'fa fa-calendar',
        time: 'fa fa-clock-o',
        up: 'fa fa-chevron-up',
        down: 'fa fa-chevron-down',
        previous: 'fa fa-chevron-left',
        next: 'fa fa-chevron-right',
        today: 'fa fa-crosshairs',
        clear: 'fa fa-trash-o',
        close: 'fa fa-times'
      },
     ignoreReadonly: true
    });

    $("#schedule-enddate-picker1").on("dp.change", (e) => {
      if(e.date){
        console.log("ggggggggggggggggggg==>",e.date);
        console.log("e.date.format('MMMM D, YYYY')===>"+e.date.format('MM/DD/YYYY hh:mm:ss a'))
        var momentDate=moment(e.date.format('MM/DD/YYYY hh:mm a'));
        console.log("moment===>",momentDate);
        console.log("moment===>",momentDate.format('MM/DD/YYYY hh:mm:ss a'));



        this.selectedOptions.endDate = momentDate;
        console.log("this.selectedOptions.endDate================>",this.selectedOptions.endDate);
        // if(this.selectedOptions.repeat)
        //   $(".selectrepeatOption").trigger('change');
      }
    });

    const monthNumbers = (duration: number, startMonth: number) => {
      const monthsArray = [];
      for (let i = 0; i < (12 / duration); i++) {
        monthsArray.push((startMonth % 12) || startMonth);
        startMonth = startMonth + duration;
      }
      return monthsArray.toString();
    }

    $(".selectrepeatOption").off().on('change',(e) =>{
      this.selectedOptions.repeatOption =  $(e.target).val();
      this.scheduledFormTimmer =="";
      if(this.selectedOptions.date){
        var hour=this.selectedOptions.date.format("HH");
        var minutes=this.selectedOptions.date.format("mm");
        if(this.selectedOptions.repeatOption=="D"){
          //0 15 10 ? * *	   === Fire at 10:15 AM every day

          console.log("timeSele====>",hour,minutes);

          this.scheduledFormTimmer = "02 "+minutes+" "+hour+" * * *";

        }else if(this.selectedOptions.repeatOption=="M"){
          //0 15 10 15 * ?   === Fire at 10:15 AM on the 15th day of every month
          var dayNmuber = this.selectedOptions.date.format("DD");
          console.log("dayNumber=======>",dayNmuber);
          this.scheduledFormTimmer = "02 "+minutes+" "+hour+" "+dayNmuber+" * *";
        }else if(this.selectedOptions.repeatOption=="W"){
          //0 15 10 ? * 1 === Fire at 10:15 AM every Monday
          var weekNumber = this.selectedOptions.date.format("d");
          console.log("weekNumber---->"+weekNumber);
          this.scheduledFormTimmer = "02 "+minutes+" "+hour+" * * "+weekNumber;
        } else if(this.selectedOptions.repeatOption=="e1M") {
          this.scheduledFormTimmer = "*/1 * * * *";
        } else if(this.selectedOptions.repeatOption=="e5M") {
          this.scheduledFormTimmer = "*/5 * * * *";
        }
        const day = this.selectedOptions.date.format("D");
        const monthNumber = Number(this.selectedOptions.date.format('M'));

        if(this.selectedOptions.repeatOption == '3M'){
          this.scheduledFormTimmer = `02 ${minutes} ${hour} ${day} ${monthNumbers(3,monthNumber)} ?`;
        }
        if(this.selectedOptions.repeatOption == '6M'){
          this.scheduledFormTimmer = `02 ${minutes} ${hour} ${day} ${monthNumbers(6,monthNumber)} ?`;
        }
        if(this.selectedOptions.repeatOption == '12M'){
          this.scheduledFormTimmer = `02 ${minutes} ${hour} ${day} ${monthNumber} ?`;
        }
      }
    });
  }
  setTimmer(){
    this.scheduledFormTimmer  = '02 ';
    if(this.selectedOptions.hours =="*" && this.selectedOptions.weekselect=="*" && this.selectedOptions.day=="*" && this.selectedOptions.everyMonth=="*"){
      this.selectedOptions.minutes = "*/"+(this.selectedOptions.minutes=="00")?"0":this.selectedOptions.minutes;
    }
    this.scheduledFormTimmer=this.scheduledFormTimmer+((this.selectedOptions.minutes=="00")?"0":this.selectedOptions.minutes)+' '+((this.selectedOptions.hours !="*" && this.selectedOptions.hourTypes=="PM")?(parseInt(this.selectedOptions.hours)+12):this.selectedOptions.hours);
    //if(this.selectedOptions.day && this.selectedOptions.weekselect =='*'){
      this.scheduledFormTimmer = this.scheduledFormTimmer+' '+this.selectedOptions.day;
   // }
    // if(this.selectedOptions.weekselect !="*" && this.selectedOptions.day=='*'){
    //   console.log(this.selectedOptions.weekselect);
    //   this.scheduledFormTimmer = this.scheduledFormTimmer+' ?';
    // }
    //console.log("this.scheduledFormTimmer=====> " +this.scheduledFormTimmer);
    //console.log("this.selectedOptions.everyMonth==> "+this.selectedOptions.everyMonth)
    if(this.selectedOptions.everyMonth !="*"){
      this.scheduledFormTimmer = this.scheduledFormTimmer+' '+"1/"+this.selectedOptions.everyMonth ;//+((this.selectedOptions.weekselect !="*")?" ":" *");
    }else{
      this.scheduledFormTimmer = this.scheduledFormTimmer+" *";
    }

    //if(this.selectedOptions.weekselect !="*"){
      this.scheduledFormTimmer = this.scheduledFormTimmer+" "+this.selectedOptions.weekselect;
    //}
  }
  resetSelectOption(){
    this.selectedOptions={
      name:'',
      form :'',
      formId:'',
      recipients:[], 
      date:'',     
     /* day : '*',
      weekselect:'*',
      everyMonth:'*',
      hours: '*',
      minutes:'*',
      seconds:'0',      
      hourTypes:"AM",*/
      message :'',
      repeat :false,
      advanced :false,
      endDate:'',
      repeatOption:''

    };
    this.resetTimmerSelectedOptions();
    this.selectedOptions.timeZone = 
    timezone.name() === CONSTANTS.timeZoneAsiaCalcutta
      ? CONSTANTS.timeZoneAsiaKolkata
      : timezone.name();
    this.addRecipientSelect2('Select Recipient(s)', true);
    this.weekSelectTimmerSet(true);
    this.selSelectOptions(true);
    this.editScheduledForm = false;
    //this.resetTimmerSelectedOptions();
    this.scheduledFormRecipients = [];
    this.selectedRecipients = [];
    $("#schedule-date-picker1").data('DateTimePicker').date(null);
    $("#schedule-enddate-picker1").data('DateTimePicker').date(null);
    $("#minutesSelection").css("display","none");
    $(".selectrepeatOption").val("");
   /* this.scheduledFormTimmer='';
    $(".month-days").val('');
    $(".months-every").val('');
    $(".hours").val('');
    $(".minutes").val('0');
    $(".seconds").val('0');
    $(".hour-types").val('AM');
    */
  }
  addScheduleFormHideShow(active) {
    console.log("addScheduleFormHideShow=========> active==> "+active);
    if (!active) {
      this.isActive = true;
      this.scrollToTopFunction();
    } else {
      this.isActive = false;
      this.resetSelectOption();
      this.recipientLoading=true;
      this.closeSelectedRecipient();
      
    }
    this.scheduleButtonName="Schedule";
    this.scheduleFormHeaderTittle="Schedule Forms";
  }

  loadScheduleFormsListTable(refresh="") {
    this.dataLoadingMsg = true;
    var self = this;
    let datas: any;
    this.userRole = self._structureService.getCookie('userRole');
    if (this.dTable) {
      this.dTable.clear().destroy();
    }
    console.log("refresh ",refresh);
    if (refresh && refresh != "") {
      this._structureService.resetDataTable();
    }
    $(()=>{
    this.dTable = $("#staff-list").DataTable({
       
      autoWidth: false,
      responsive: true,
      bServerSide: true,
      bpagination: true,
      bsorting: true,
      retrieve: true,
      stateSave: true,
      bsearching: true,
      bInfo: true,
      lengthMenu: [[25, 50,], [25, 50]],
      order: [[ 1, "desc" ]],
      fnDrawCallback: function (oSettings) {
        console.log('oSettings', oSettings);
        if (oSettings._iRecordsTotal < oSettings._iDisplayLength || oSettings._iRecordsTotal == 0 || oSettings.aoData.length == 0) {
          $('.dataTables_paginate').hide();
        }
        else {
          $('.dataTables_paginate').show();
        }
        if (oSettings.aoData.length == 0) {
          $('.dataTables_info').hide();
        }
        else {
          $('.dataTables_info').show();
        }
      },
      fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
        this.activeRecord = aData;
        var patientDetail = '';
        var recipientsDetails = this.activeRecord.recipients;
        var count =recipientsDetails.length;
        for(var k=0;k<count;k++){
          var singleRecipients = recipientsDetails[k];
          if(singleRecipients.careGiverId == null || singleRecipients.careGiverId == ""){
              if(singleRecipients && ((singleRecipients.identityValue != null && singleRecipients.identityValue != "") ||(singleRecipients.dateOfBirth != null && singleRecipients.dateOfBirth != ""))){         
              patientDetail = singleRecipients.dateOfBirth ? 'DOB : '+moment(singleRecipients.dateOfBirth).format('MM/DD/YYYY') : '';
              patientDetail += singleRecipients.identityValue ? '<br> MRN : '+singleRecipients.identityValue:'';  
              $(nRow).find('.patient-details'+singleRecipients.id).tooltip({html:true, title: patientDetail });
             }
            } else if(singleRecipients.careGiverId != null || singleRecipients.careGiverId != ""){
              if(singleRecipients && ((singleRecipients.careGiveIdentityValue != null && singleRecipients.careGiveIdentityValue != "") ||(singleRecipients.careGiverDob != null && singleRecipients.careGiverDob != ""))){         
                patientDetail = singleRecipients.careGiverDob ? 'DOB : '+moment(singleRecipients.careGiverDob).format('MM/DD/YYYY') : '';
                patientDetail += singleRecipients.careGiveIdentityValue ? '<br> MRN : '+singleRecipients.careGiveIdentityValue:'';  
                $(nRow).find('.patient-details'+singleRecipients.id).tooltip({html:true, title: patientDetail });
               }
            }            
            else{
              patientDetail = '';
            }
        }
        $(nRow).on("click", () => {
          this.activeRecord = aData;              
          console.log("aData................")
          console.log(aData);
        });
      },
      dom:
			"<'row'<'col-sm-4 'l>B<'col-sm-4'f><'col-sm-2 searchButton'>>" +
			"<'row'<'col-sm-12'tr>>" +
			"<'row'<'col-sm-5'i><'col-sm-7'p>>",
			buttons :[{
			  extend: 'collection',
			  text: 'Export Data',
			  autoClose: true,
			  className: 'buttonStyle',
			  buttons: [
				{
				  extend: 'excel',
				  text: 'Current Page',
				  title: 'Scheduled Forms',
				  exportOptions: {
					columns:[0,1,2,3,4,5,6]
				  }
				},
				{
				  extend: 'excel',
				  text: 'All Pages',
				  title: 'Scheduled Forms',
				  exportOptions: {
					columns:[0,1,2,3,4,5,6]
				  },
				  action: function ( e, dt, node, config ) {
				  var selfButton = this;
				  var oldStart = dt.settings()[0]._iDisplayStart;
				  dt.one('preXhr', function (e, s, data) {
					  data.start = 0;
					  data.length = self.totalCountScheduled;
					  dt.one('preDraw', function (e, settings) {
						  $.fn.dataTable.ext.buttons.excelHtml5.action.call(selfButton, e, dt, node, config);
						  dt.one('preXhr', function (e, s, data) {
							  settings._iDisplayStart = oldStart;
							  data.start = oldStart;
						  });
						  setTimeout(dt.ajax.reload, 0);
						  return false;
					  });
				  });
				  dt.ajax.reload();
				  }
			  }]
		  }],
    initComplete: function () {
      $('#staff-list_filter label input').attr('placeholder', 'Search');
      $('#staff-list_filter label input').unbind();
      $("#staff-list_wrapper #staff-list_filter input").on('keydown', function (e) {
        if (e.which == 13) {
          var value = $("#staff-list_filter input").val();
          console.log(value);
          if (value) {
            console.log("self.dpTable", self.dTable);
            self.searchResetFlag = 0;
            self.dTable.search(value).draw();
          } else {
            self.loadScheduleFormsListTable('refresh');
          }
        }
      });
      $("#staff-list_filter input").on('keypress', function (e) {
        $(".searchBScheduled").prop('disabled', false);
      });
      $("#staff-list_filter input").on('keyup', function (e) {
        var value = $("#staff-list_filter input").val();
        if (value) { } else
          $(".searchBScheduled").prop('disabled', true);
      });
      $("div.searchButton")
        .html('<button disabled="true" class="btn btn-sm btn-info searchBScheduled" id="searchBScheduled" title="Search" type="submit">Search</button>' +
          '<button style="margin-left:10px;" class="btn btn-sm btn-default resetBScheduled" id="resetBScheduled" title="Reset" type="submit">Reset</button>');
      var value = $("#staff-list_filter input").val();
      if (value) {
        $(".searchBScheduled").prop('disabled', false);
      }
      $("#staff-list_filter input").on('paste', function (event) {
        console.log("eeeeeeeeeeeeeeeeee", event);
        var element = this;
        var text;
        setTimeout(function () {
          text = $(element).val();
          if (text) {
            $(".searchBScheduled").prop('disabled', false);
          }
        }, 100);
      });
      self.dataLoadingMsg = false;
      $(".buttons-collection").click(function(event) {
        setTimeout(function () {
          if($(".buttons-collection").attr("aria-expanded") === "true" && $(".dt-button-collection").find("button").length == 0){
          $(".dt-button-collection").remove();
          $(".dt-button-background").remove();
          $(".buttons-collection").attr("aria-expanded","false");
          }
        },500);
      });
    },
    ajax: function (dat, callback, settings) {
      let orderData;
      let searchText;
      let orderby;
      let limit;
      console.log('searchData', dat);
      var i = dat.order[0].column ? dat.order[0].column : '';
      orderby = dat.order[0].dir ? dat.order[0].dir : '';
      if (i != 0) {
          orderData = dat.columns[i].data ? dat.columns[i].data : '';
      } else {
        orderData = '';
      }
      let showFilterAppliedMessage = false;
      if (!self.isTableLoaded) {
        self.isTableLoaded = true;
        const searchStored = self.storeService.getStoredData(Store.SEARCH_SCHEDULE_FORMS);
        const searchText = searchStored && searchStored.searchText ? searchStored.searchText : '';
        dat.search.value = searchText;
        if (searchText) {
          showFilterAppliedMessage = true;
        }
        setTimeout(() => {
          const searchBox = $('.dataTables_filter input');
          searchBox.val(searchText);
        }, 0);
      }
      self._structureService.notifySearchFilterApplied(showFilterAppliedMessage);
      self.searchValue = searchText = dat.search.value ? dat.search.value : '';
      self.storeService.storeData(Store.SEARCH_SCHEDULE_FORMS, { searchText });
      if(searchText &&  self.searchResetFlag == 0) {
        dat.start = 0;
        settings.oAjaxData.search.value = searchText;
        settings.oAjaxData.start = 0;
        settings._iDisplayStart = 0;
        self.searchResetFlag = 1;
      }
      if (settings.oAjaxData.start != 0 && self.datam && self.datam.aaData && self.datam.aaData.length == 1 && settings._iDisplayStart != 0 && searchText == '') {
        settings.oAjaxData.start = settings.oAjaxData.start - settings.oAjaxData.length;
        settings._iDisplayStart = settings.oAjaxData.start;
      }
      console.log('searchData-fields', 'i - ', i, 'offset - ', dat.start,dat.length, 'orderby - ', orderby, 'orderData - ', orderData, 'searchText - ', searchText);
      // dat.length,dat.start ,orderData,orderby,searchText
      let schedule = 0;
      self._structureService.getScheduledFormsPagination(schedule,dat.length,dat.start,orderby,orderData,searchText,self.siteIds).then((dataa) => {
        datas = {};
        self.datam = {};
        if (dat.start == 0) {
          this.totalCt = dataa['getScheduledFormsPagination']['totalCount'];
          self.totalCountScheduled = this.totalCt;
        }
        else {
          if (self.totalCountScheduled) {
            self._structureService.setCookie('totalCountScheduled', this.totalCt, 1);
            this.totalCt = self.totalCountScheduled;
          }
          else {
            this.totalCt = self._structureService.getCookie('totalCountScheduled');
            self.totalCountScheduled = this.totalCt;
          }
        }
        datas = dataa['getScheduledFormsPagination']['data'] ? dataa['getScheduledFormsPagination']['data'] : [];
        self.scheduledFormsList = datas;
        let draw;
        let total;
        if (datas && datas.length == 0 && searchText == '') {
          draw = 0;
          total = 0;
        }
        else {
          draw = dat.draw;
          total = this.totalCt;
        }
        self.datam = {
          "draw": draw,
          "recordsTotal": total,
          "recordsFiltered": total,
          "aaData": datas
        };
        self.dataLoadingMsg = false;
        callback(self.datam)
      });
    },
      // data: this.scheduledFormsList,
      columns: [
        { title: "#" },//0
        { title: "Subject", data: "scheduleName" },//1
        { title: "Form Name", data: "formName" },//2
        { title: this.labelSite},//3
        { title: "Created User", data: "createdUserName" },//4
        { title: "Recipients", data: "recipients.name" },//5
        { title: this.toolTipService.getTranslateData('ADMISSION.LABELS.ADMISSION') },//6
        { title: "Scheduled On", data: "scheduledOn" },//7
        { title: "Next Scheduled Date/Time", data: "nextScheduleDate" },//8
        { title: "Actions" }//9
      ],
      columnDefs: [
        {
          //slNo
          data: null,
          orderable: false,
          width: "5%",
          targets: 0,
          render: function (data, type, row, meta) {
            let roNo = meta.row + 1 + meta.settings._iDisplayStart;
            return roNo;
          }
        },
        {
          //scheduleName
          data: null,
          width: "15%",
          render: (data, type, row) => {
            return data.charAt(0).toUpperCase() + data.slice(1);
          },
          targets: 1
        },
        {
          //formName
          data: null,
          width: "10%",
          targets: 2
        },
        {
            //site_name
            data: null,
            width: "10%",
            visible: (self.userRole != 'Patient' && (self.userData.config.enable_multisite === "1") && self.hideSiteSelection),
            render: (data, type, row) => {
              if (row.recipients.length > 0) {
                let siteName = [];
              row.recipients.forEach(value => {
                    siteName.push(value.site_name);
                });
                var siteNam = ""
                var labelHtml="";
                var count =siteName.length;
                console.log("siteName",siteName,count);
                for(let k=0;k<count;k++){
                  console.log("siteName",siteName[k]);
                      siteNam=siteName[k];
                      if(k==0){
                        labelHtml +=siteNam;
                      }
                      if(count>1 && k==1){
                        labelHtml+=`<a class="pull-right btn btn-sm openmore_recipients openmore_recipients`+row.id+count+`" href="javascript: void(0);" title="More recipients" data-moreId=`+row.id+count+` id="more_recipients"  (click)="moreRecipients()"><i id="more_recipients" data-moreId=`+row.id+count+` class="fa fa-plus" aria-hidden="true"></i></a>`;
                        labelHtml+=`<a class="pull-right btn btn-sm closemore_recipients closemore_recipients`+row.id+count+`" href="javascript: void(0);" title="Close recipients" style="display:none" data-moreId=`+row.id+count+` id="closemore_recipients"  (click)="moreRecipients()"><i id="closemore_recipients" data-moreId=`+row.id+count+` class="fa fa-close" aria-hidden="true"></i></a>`;
                        labelHtml+=`<div class="more-recipients" style="display:none" id="`+row.id+count+`">`;
                      }
                      if(k>=1){
                       labelHtml+=`<label>`+siteNam+`</label>`;
                      }
                      if(k==(count-1)){
                       labelHtml+=`</div>`;
                      }
                    }
                return labelHtml;
                
            } else {
                return null;
            } },
            
            targets: 3
          },
        {
          //createdUserName
          data: null,
          width: "10%",
          targets: 4
        },
        {
          //recipients
          data: null,
          width: "10%",  
          render: (data, type, row) => {
            var recipientsDetails = row.recipients;
            var labelHtml="";
            var count =recipientsDetails.length;
            let patientDetails = '';
       
          
            for(var k=0;k<count;k++){
              var singleRecipients = recipientsDetails[k];
              if(singleRecipients.name && (singleRecipients.identityValue || singleRecipients.dateOfBirth || singleRecipients.careGiveIdentityValue || singleRecipients.careGiverDob )){
                patientDetails = ` <i class='patient-details`+singleRecipients.id+` icmn-info'></i>`;
              } else{
                patientDetails = '';  
              }
              var recipientName = (singleRecipients.name) ? singleRecipients.name : '';
              if(singleRecipients.name){
              recipientName=recipientName.charAt(0).toUpperCase() + recipientName.slice(1)
              if(k==0){
                labelHtml +=recipientName +patientDetails;
              }
               if(count>1 && k==1){
                labelHtml+=`<a class="pull-right btn btn-sm openmore_recipients openmore_recipients`+row.id+`" href="javascript: void(0);" title="More recipients" data-moreId=`+row.id+` id="more_recipients"  (click)="moreRecipients()"><i id="more_recipients" data-moreId=`+row.id+` class="fa fa-plus" aria-hidden="true"></i></a>`;
                labelHtml+=`<a class="pull-right btn btn-sm closemore_recipients closemore_recipients`+row.id+`" href="javascript: void(0);" title="Close recipients" style="display:none" data-moreId=`+row.id+` id="closemore_recipients"  (click)="moreRecipients()"><i id="closemore_recipients" data-moreId=`+row.id+` class="fa fa-close" aria-hidden="true"></i></a>`;
                labelHtml+=`<div class="more-recipients" style="display:none" id="`+row.id+`">`;
                labelHtml+=`<label>`+recipientName+patientDetails+`</label>`;
              }
               if(k>1){
                labelHtml+=`<label>`+recipientName+patientDetails+`</label>`;
               }
               if(k==(count-1)){
                labelHtml+=`</div>`;
               }
              }

            }
            return labelHtml;
            
          },        
          targets: 5
        },
        {
          data: null,
          width: "7%",
          targets: 6,
          visible: self._structureService.isMultiAdmissionsEnabled,
          render: function(data, type, row) {
            if (row.recipients.length > 0){
              return `<label>${row.recipients.map((recipient) => recipient.admissionName).join('</label>, <label>')}</label>`;
            }
            return "";
          }
        },
        {
          //scheduledOn
          data: null,
          width: "13%",
          targets: 7,
          render: (data, type, row) => {
            if(row.scheduleRepeat){
              return "Recurring";
            }else{
              return this.datePipe.transform(row.scheduleDate, 'MMM dd yyyy hh:mm a');
            }
          }
        },
        {
          //next scheduled date/time
          data: null,
          width: "13%",
          orderable:false,
          searchable:false,
          render: (data, type, row) => {
            console.log("row.nextScheduleDate=====>"+row.nextScheduleDate);
            if(row.status=="RUNNING" && ((row.scheduleRepeat && row.nextScheduleDate)||!row.scheduleRepeat)){
              var nextschedule= row.nextScheduleDate;
              if(!row.scheduleRepeat){
                nextschedule = row.scheduleDate;
              }
              //nextschedule =new Date(nextschedule);
              console.log("nextschedule==================>"+nextschedule);
              return this.datePipe.transform(nextschedule, 'MMM dd yyyy hh:mm a');
            }else if(row.status=="STOPPED"){
              return "Stopped";
            }else if(row.status=="RUNNING" && row.scheduleRepeat &&!row.nextScheduleDate){
              return "Can't Start";
            }else if(row.status=="FormInactive"){
              return "Form Inactive";
            }else{
              return "Completed";
            }
          },
          targets: 8
        },
        {
          //actions
          data: null,
          orderable: false,
          render: (data, type, row) => {
            let actions = `<a class="pull-right btn btn-sm" title="Delete" href="javascript: void(0);" data-rowId ="`+row.id+`" id="delete-scheduled-form" (click)="deleteSchedule()"><i data-rowId ="`+row.id+`" id="delete-scheduled-form" class="fa fa-trash" aria-hidden="true"></i></a>`;
           if(row.status=="RUNNING"){
              actions += `<a class="pull-right btn btn-sm" title="Stop" href="javascript: void(0);" data-rowId ="`+row.id+`" id="stop-scheduled-form" (click)="changeScheduleRunningStatus()"  ><i data-rowId ="`+row.id+`" id="stop-scheduled-form" class="fa fa-ban" aria-hidden="true"></i></a>`;
           }else if(row.status=="STOPPED"){
              actions += `<a class="pull-right btn btn-sm" title="Start" href="javascript: void(0);" data-rowId ="`+row.id+`" id="start-scheduled-form" (click)="changeScheduleRunningStatus()"  ><i data-rowId ="`+row.id+`" id="start-scheduled-form" class="fa fa-repeat" aria-hidden="true"></i></a>`;
           }
              actions += `<a class="pull-right btn btn-sm" title="Edit" href="javascript: void(0);" data-rowId ="`+row.id+`" id="edit-scheduled-form" (click)="editSchedule()" ><i data-rowId ="`+row.id+`" id="edit-scheduled-form" class="fa fa-pencil" aria-hidden="true"></i></a>`;
            return actions;
          },
          width: "10%",
          targets: 9
        }
      ],
      language: {
        emptyTable: "No item found."
      }
    });

    $('.dataTables_filter input').attr('id','Searchbox');
  });

   /* this.dTable
      .on("order.dt search.dt", () => {
        this.dTable
          .column(0, { search: "applied", order: "applied" })
          .nodes()
          .each(function(cell, i) {
            cell.innerHTML = i + 1;
          });
      })
      .draw(); */
  }
  sendScheduledForms(){
    if(this.editScheduledForm){
      this.updateScheduledForm();
    }else{
      if(this.selectedRecipients){
        this.selectedOptions.recipients = this.selectedRecipients;
      }
      var scheduledForms = {
        recipients:[],
        formName:'',
        formId:'',
        message:'',
        name:'',
        time : '',
        timeZone:this.selectedOptions.timeZone,
        date:'',
        repeat:false,
        advanced:false,
        repeatOption:0,
        endDate:''
      };
      let validate =this.validateScheduledForm()
      if(validate){
        this.scheduleSending=true;
        let recipients=[];
        for(let i=0;i<this.selectedOptions.recipients.length; i++){
          let indexData:any = this.selectedOptions.recipients[i];
          let selectedFilterData :any = [];
          if(indexData) {
            selectedFilterData = this.scheduledFormRecipientsArray.filter(x => x.optionId == indexData);
            if(selectedFilterData){
              selectedFilterData =selectedFilterData[0];
              let data:any = {email:selectedFilterData.name, userId: selectedFilterData.userId, admissionId: this.selectedOptions.admissionId};
              if(selectedFilterData.caregiver_userid) {
                data.caregiverUserId = selectedFilterData.caregiver_userid;
              }
              recipients.push(data);
            }
          }
          
        }
        scheduledForms.date = this.convertDateToUtc(this.selectedOptions.date);
        scheduledForms.repeat = this.selectedOptions.repeat;
        scheduledForms.advanced = this.selectedOptions.advanced;
        scheduledForms.name = this.selectedOptions.name;
        scheduledForms.recipients=recipients;
        scheduledForms.formName=this.selectedOptions.form;
        scheduledForms.formId  = this.selectedOptions.formId;
        scheduledForms.message=this.selectedOptions.message;
        scheduledForms.time = this.scheduledFormTimmer;
        scheduledForms.timeZone=this.selectedOptions.timeZone;
        scheduledForms.repeatOption=(this.selectedOptions.repeatOption=="D")?1:(this.selectedOptions.repeatOption=="W")?2:(this.selectedOptions.repeatOption=="M")?3:(this.selectedOptions.repeatOption=="e1M" || this.selectedOptions.repeatOption=="e5M") ? 4 : (this.selectedOptions.repeatOption=="3M") ? 5: (this.selectedOptions.repeatOption=="6M") ? 6 : (this.selectedOptions.repeatOption=="12M") ? 7 : 0;
        scheduledForms.endDate = (this.selectedOptions.repeat && this.selectedOptions.repeatOption !="" && this.convertDateToUtc(this.selectedOptions.endDate))?this.convertDateToUtc(this.selectedOptions.endDate):'';
        //scheduledForms.tenantKey="5a5c6cc3b082d";
        //scheduledForms.formName="General Supply Template";
        //scheduledForms.time ='*/2 * * * *';
        //console.log("this.selectedOptions===> ",this.selectedOptions)
        console.log("scheduledForms=====> ",scheduledForms);

        this._structureService.sendScheduledForms(scheduledForms).subscribe((result) => {
          console.log("sendScheduledForms : result-------->",result);
          if(!isNull(result.data.sendScheduleForms)){
            var activityData = {
              activityName: "Add Schedule",
              activityType: "formschedule",
              activityDescription: this.userData.displayName +" schedule a Form with name "+scheduledForms.name+ " and scheduled on "+((scheduledForms.advanced)?scheduledForms.time:scheduledForms.date)+ " (Repeat option-"+scheduledForms.repeat+" )"
            };        
            if(this.totalCountScheduled > 0)
            this.totalCountScheduled = this.totalCountScheduled + 1;
            console.log(result['data']['sendScheduleForms']);
            console.log(result['data']['sendScheduleForms'].scheduleName)
            var scheduleResult=result['data']['sendScheduleForms'];
            var scheduleName = result['data']['sendScheduleForms'].scheduleName;
            this.resetSelectOption();
            this.selSelectOptions(true);
            this.addRecipientSelect2('Select Recipient(s)',true);
            this.weekSelectTimmerSet(true);
            $("#schedule-date-picker1 input").val('');
            $("#schedule-enddate-picker1 input").val('');
            this.closeSelectedRecipient();
            var notify = $.notify('success! '+scheduleName+' successfully scheduled.');
            // this.scheduledFormsList.unshift(scheduleResult);
            this.loadScheduleFormsListTable();
            this.scheduleSending=false;
            this._structureService.trackActivity(activityData); 
            setTimeout(function () {
              notify.update({ 'type': 'success', 'message': '<strong>'+scheduleName+' successfully scheduled.</strong>' });
            });    
          } else {
            let activityData = {
                activityName: "Error Add Schedule",
                activityType: "formschedule",
                activityDescription: `Error occured while addd Schedule : ${this.userData.displayName} schedule a Form with name ${scheduledForms.name} and scheduled on ${((scheduledForms.advanced)?scheduledForms.time:scheduledForms.date)} (Repeat option-${scheduledForms.repeat})`
              }; 
            this.scheduleSending = false;
            let notify = $.notify('Error Occur please try again.');
            setTimeout(function () {
              notify.update({ 'type': 'warning', 'message': '<strong>Error Occur please try again.</strong>' });
            }); 
            this._structureService.trackActivity(activityData); 
          } 
        },(error) => {
           console.log("sendScheduledForms : errorrrrrrrrrrrrrrrrrrr");
          console.log(error);
                    console.log(error['graphQLErrors'])
          var activityData = {
              activityName: "Error Add Schedule",
              activityType: "formschedule",
              activityDescription: `Error occured while addd Schedule : ${this.userData.displayName} schedule a Form with name ${scheduledForms.name} and scheduled on ${((scheduledForms.advanced)?scheduledForms.time:scheduledForms.date)} (Repeat option-${scheduledForms.repeat})`
            }; 
            this.scheduleSending = false;
            var notify = $.notify('Error Occur please try again.');
            setTimeout(function () {
              notify.update({ 'type': 'warning', 'message': '<strong>Error Occur please try again.</strong>' });
            });
            this._structureService.trackActivity(activityData); 
      });
      }else{
        console.log("validation error........");
      }
    }
  }
  errorReset(){
    $("#error-timmer").html("");
    $("#error-start").html("");
    $("#error-name").html("");
    $("#error-form").html("");
    $("#error-recipient").html("");
    $("#error-message").html("");
    $("#error-selectrepeatOption").html("");
    $("#error-admission").html("");
  }
  errorSet(id,message){
    $("#error-"+id).html("").html(message);
  }
  validateScheduledForm(){
    console.log("validate function called");
    if(this.selectedOptions.name && this.selectedOptions.form && this.selectedOptions.formId  && Object.keys(this.selectedOptions.recipients).length>0 && (!this._structureService.isMultiAdmissionsEnabled || this.selectedOptions.admissionId)){
      if(this.selectedOptions.date && this.selectedOptions.repeat && this.selectedOptions.advanced){
        if(this.scheduledFormTimmer){
          if(this.selectedOptions.day =="*" && this.selectedOptions.weekselect=="*" && this.selectedOptions.everyMonth=="*" && this.selectedOptions.hours=="*" && this.selectedOptions.minutes=="*" && this.selectedOptions.seconds==0){
            //$("#error-timmer").html("").html("Please select a time");
            this.errorSet("timmer","Please select a time");
            return false;
          }else{
            return true;
          }
        }else{
         // $("#error-timmer").html("").html("Please select a time");
          this.errorSet("timmer","Please select a time");
          return false;
        }
      }else if(this.selectedOptions.date && !this.selectedOptions.repeat){
        return true;
      }else if(this.selectedOptions.date && this.selectedOptions.repeat && !this.selectedOptions.advanced){
        if(this.scheduledFormTimmer && this.selectedOptions.repeatOption){
          return true;
        }else{
          this.errorSet("selectrepeatOption","Please select a repeat option");
          return false;
        }
      }else{
        this.errorSet("start","Please select a start date");
        //$("#error-start").html("").html("Please select a start date");
        return false;
      }
    }else{
      if(!this.selectedOptions.name){
        this.errorSet("name","Enter schedule name");
        //$("#error-name").html("").html("Enter schedule name");
      }
      if(!this.selectedOptions.form){
        this.errorSet("form","Please select a form");
        //$("#error-form").html("").html("Please select a form");
      }
      if(this.selectedOptions.repeat && !this.selectedOptions.repeatOption && !this.selectedOptions.advanced ){
        this.errorSet("selectrepeatOption","Please select a repeat option");
      }
      if(Object.keys(this.selectedOptions.recipients).length<=0){
        this.errorSet("recipient","Please select at least one recipient");
        //$("#error-recipient").html("").html("Please select at least one recipient");
      }
      if(this._structureService.isMultiAdmissionsEnabled && !this.selectedOptions.admissionId){
        this.errorSet("admission", this.toolTipService.getTranslateData('VALIDATION_MESSAGES.ADMISSION_REQUIRED'));
      }
      // if(!this.selectedOptions.message){
      //   this.errorSet("message","Enter your message");
      // }

      return false;
    }
  }
  deleteScheduledForms(scheduledId){
    var self =this;
    swal({
      title: "Are you sure?",
      text: "You are going to remove this scheduled form",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Ok",
      closeOnConfirm: true
    },
      function (isConfirm) {
        if (isConfirm) {
          let index = self.scheduledFormsList.indexOf(self.activeRecord);
          self._structureService.deleteScheduledForm(scheduledId).subscribe((data) => {
            var resultData=data['data']['deleteScheduledForm'];
            var errorData=data['error'];
            if(resultData.id && !errorData){
              if(self.totalCountScheduled > 0)
              self.totalCountScheduled = self.totalCountScheduled - 1;
              if(index>-1){
                let scheduleData = self.scheduledFormsList[index];
                var activityData = {
                  activityName: "Delete Schedule",
                  activityType: "formschedule",
                  activityDescription: self.userData.displayName +" delete a schedule with Schedule name "+scheduleData.scheduleName+ " and scheduled on "+((scheduleData.scheduleRepeat)?scheduleData.scheduledOn:scheduleData.scheduleDate)+ " (Repeat option-"+scheduleData.scheduleRepeat+" )"
                };
                // self.scheduledFormsList.splice(index, 1);
                self.notifyPopup('Deleted successfully.','warning');
                if(self.editScheduledForm && self.editScheduleData && self.editScheduleData.id==scheduledId){
                  self.resetEditScheduleForm();
                  //self.notifyPopup('Trying to edit Schedule was deleted.','warning');
                }
                self.loadScheduleFormsListTable();
                self._structureService.trackActivity(activityData); 
              }else{
                console.log("index not get...........");
              }
            }else{
              console.log("delete error from graphql..........");
            }
          });
        }
      });

      var x = document.getElementsByClassName("btn-warning");
     
      x[0].setAttribute("id", "remove_cnfrm_btn");

  }
  fetchRecipientWithFormId(selectedFormTypeId, condition, stafFacing = "false",searchText=""){
    $("#recipientt-search").text(" ").text("Loading...");
    var isStaffFacing = false;

    if(stafFacing != "false") {
      isStaffFacing = true;
    }

    this._formsService.getTenantUsersByRoleId(this._structureService.getCookie('tenantId'), selectedFormTypeId, this.userData.roleId, isStaffFacing,searchText).then(async (result)=>{
      this.scheduledFormRecipients = this.userFilter.transform(result);

      setTimeout(() => {
        this.selectedRecipients = this.selectedRecipients.map(String);
        this.selectedOptions.recipients = this.scheduledFormRecipients.filter((val)=> {
          return this.selectedRecipients.indexOf(val.userId) > -1
        });
      }, 1000);

      this.recipientLoading = false;    
      this.enableOrDisableUiLI(true,false);
      $("#recipientt-search").text(" ").text("Search");
    }).catch((ex)=>{
      console.log("get recipients errorrrrrrr");
    });
  }
  editScheduledForms(scheduledId){
    if(this.activeRecord.id && scheduledId){
      this._structureService.getScheduledForms(this.activeRecord.id).then(( data ) => {//.subscribe((data) => {
        this.editScheduledForm=true;
        console.log("editScheduleData===>gggggggggggggggggggg",data);
        this.editScheduleData= JSON.parse(JSON.stringify(data['getScheduledForms'][0]));
        console.log("this.editScheduleData===========>",this.editScheduleData);
        this.selectedRecipients = [];
        this.selectedRecipientsSiteId = [];
        this.selectedRecipientsData = this.editScheduleData.recipients;
          this.editScheduleData.recipients.forEach((d) => {
              this.selectedRecipients.push(d.id);
              if (!this.selectedRecipientsSiteId.includes(d.siteId)) {
                  this.selectedRecipientsSiteId.push(d.siteId);
              }
              if (this._structureService.isMultiAdmissionsEnabled) this.selectedOptions.admissionId = d.admissionId;
          });
        this.selectedRecipients = this.selectedRecipients.map(String);
        this.scheduleButtonName = "Update Schedule";
        this.scheduleFormHeaderTittle = "Edit Schedule Forms";
        this.selectedOptions.name = this.editScheduleData.scheduleName;
        this.selectedOptions.repeat = this.editScheduleData.scheduleRepeat;
        this.selectedOptions.advanced = this.editScheduleData.scheduleAdvanced;
        this.selectedOptions.message = this.editScheduleData.message;
        this.selectedOptions.date = this.editScheduleData.scheduleDate;
        this.selectedOptions.endDate = this.editScheduleData.endDate;
        this.setScheduleTimeForEdit();
        this.addScheduleFormHideShow(false);
        console.log("setup formssssssssssssssssssss"+this.editScheduleData.scheduleDate);
        var startDate =this.editScheduleData.scheduleDate.replace("T"," ");
        var endDate =this.editScheduleData.endDate.replace("T"," ");
        console.log("set start dte================",startDate)
        if(moment(startDate)<Date.now()){
          console.log(`Enter date diff condition  start date= ${moment(startDate)} and date now ${Date.now()}`);
          $("#schedule-date-picker1").data("DateTimePicker").minDate(moment(moment(startDate).format("MM/DD/YYYY hh:mm A")).format("MM/DD/YYYY hh:mm A"));
        }
        $('#schedule-date-picker1').data("DateTimePicker").date(moment(moment(startDate).format("MM/DD/YYYY hh:mm A")).format("MM/DD/YYYY hh:mm A"));
        if(this.editScheduleData.endDate && this.editScheduleData.endDate !=""){
          $("#schedule-enddate-picker1").data("DateTimePicker").minDate(moment(moment(startDate).format("MM/DD/YYYY hh:mm A")).format("MM/DD/YYYY hh:mm A"));
          $('#schedule-enddate-picker1').data("DateTimePicker").date(moment(moment(endDate).format("MM/DD/YYYY hh:mm A")).format("MM/DD/YYYY hh:mm A"));
        }
        // var formId = this.checkFormIndex(this.editScheduleData.formsId);
        // if(formId !==null){
        // console.log(`formIndex======>${formId}`)
        // $('#form_type').val(this.editScheduleData.formsId); 
        if(this.editScheduleData.formsId){
        setTimeout(() => {
          $("#form_type").val(this.editScheduleData.formsId).trigger('change');
        }, 1000);                  
      }              
        // }
      });
      // this.loadScheduleFormsListTable();
    }else{
      console.log("gggggggggggggggggggggggggggggggggggggg---->editScheduledForms");
      var notify = $.notify({ 'type': 'warning', 'message': '<strong>Error Occur please try again.</strong>' });
      setTimeout(function () {
        notify.update({ 'type': 'warning', 'message': '<strong>Error Occur please try again.</strong>' });
      });
    }
  }
  setScheduleTimeForEdit(){
    console.log("enterrrrrrrrrrrrr===>setScheduleTimeForEdit")
    var repeat = this.editScheduleData.scheduleRepeat;
    var repeatOption = this.editScheduleData.repeatOption;
    var advanced = this.editScheduleData.scheduleAdvanced;
    var scheduledOn = this.editScheduleData.scheduledOn;
    //var scheduleDate = moment
    // var hour = 
    var hourType= "AM";
    var startDate = moment(moment(this.editScheduleData.scheduleDate));
    console.log("startDate=============>"+startDate);
    console.log(startDate.format());
    

    console.log(`repeat ${repeat}, repeatOption -->${repeatOption} , advanced -->${advanced} , scheduledOn-->${scheduledOn}`);
    if(repeat && repeatOption !=0){
      console.log("enter repeat option if.............");
      console.log("enter repeat option if.............");
      console.log("enter repeat option if.............");
      console.log("enter repeat option if.............");
      var hour=startDate.format("HH");
      var minutes=startDate.format("mm");
      $(".minutes").val(minutes);
      if(hour>12){
        hourType = "PM";
        hour=hour-12;
      }
      $(".hours").val(hour);
      $(".hour-types").val(hourType);
      console.log("hour======>"+hour);
      console.log("minutes====>"+minutes);
      if(repeatOption==1){
          //daily
        $(".selectrepeatOption").val("D");  
      }else if(repeatOption ==2){
          //weekly
          $(".selectrepeatOption").val("W");  
          
      }else if(repeatOption==3){
          //monthly
          $(".selectrepeatOption").val("M");  
      }else if(repeatOption==4) {
        $(".selectrepeatOption").val("e5M");
      }else if(repeatOption==5) {
        $(".selectrepeatOption").val("3M");
      }else if(repeatOption==6) {
        $(".selectrepeatOption").val("6M");
      }else if(repeatOption==7) {
        $(".selectrepeatOption").val("12M");
      }
    }else if(repeat && !advanced && !repeatOption && scheduledOn){
      console.log("enter with out advance and repeat option");
      console.log("enter with out advance and repeat option");
      console.log("enter with out advance and repeat option");
      console.log("enter with out advance and repeat option");
      //for old schedule (before repeatOption implementation)
      let scheduleRule = scheduledOn.split("*");
      if(scheduleRule.length==4){
          //daily
          $(".selectrepeatOption").val("D");
      }else if(scheduleRule.length==3){
          if(scheduleRule[2]==""){
              //weekly
              $(".selectrepeatOption").val("W");

          }else{
              //monthly
              $(".selectrepeatOption").val("M");
          }
        }
    }else if(repeat && advanced && repeatOption==0 && scheduledOn){
        console.log("enter repeat with advance and withour repeatoption");
        console.log("enter repeat with advance and withour repeatoption");
        console.log("enter repeat with advance and withour repeatoption");
        console.log("enter repeat with advance and withour repeatoption");
        console.log("enter repeat with advance and withour repeatoption");
        let scheduleRule = scheduledOn.split("*");
        let hour,minutes,details,everyMonth,monthday,weekselect;
        var data = scheduleRule[0];
        details=data.split(" ");
        minutes = details[1];
        hour = details[2];
        weekselect = '';
        monthday='';
        everyMonth=""
        console.log("scheduleRule====>",scheduleRule);
        if(scheduleRule.length==2){
          console.log("every month option.................")
          //with every - month @ option. "02 40 08 1/5 * *"
          everyMonth = details[3].split("/").prop();                    
        }else if(scheduleRule.length==3){
          if(scheduleRule[2]!==""){
            console.log("enter week option...........................")
              //weekly "02 40 08 * * 2"
              weekselect = parseInt(scheduleRule[2]);

          }else{
            //monthly "02 40 08 15 * *"
            console.log("enter month option..............................")
            monthday = details[3];
          }
        }else if(scheduleRule.length==4){
            //daily
            console.log("enter daily option.....................");

        }
        if(hour>12){
          hourType = "PM";
          hour=hour-12;
        }
        this.selectedOptions.hours = hour;
        this.selectedOptions.minutes = minutes;
        this.selectedOptions.hourTypes = hourType;
        this.selectedOptions.everyMonth = (everyMonth)?everyMonth:"*";
        this.selectedOptions.weekselect = (weekselect)?weekselect:'*';
        this.selectedOptions.day =(monthday)?monthday:"*";

          $(".minutes").val(minutes);
          $(".hours").val(hour);
          $(".hour-types").val(hourType);
          $(".months-every").val(everyMonth);
          $(".month-days").val(monthday);
          $("#minutesSelection").css("display","block");
          $(".weekselect").select2("trigger", "select", {
            data: { id: weekselect }
        });
    }
  }
  convertDateToUtc(scheduleDateTime) {
    const selectedTimezone = $('#timezone-dropdown').val();
    const formattedDate = scheduleDateTime.format(DateFormat.YYMMDD_FORMAT_HYPHEN);
    const formattedTime = scheduleDateTime.format(DateFormat.TIME_24_HR);
    const utcScheduleDateTimeIso = convertTimeZoneDateTimeToUTCIso(formattedDate, formattedTime, selectedTimezone);
    const utcScheduleDateWithMilliseconds = convertToMillisecondTime(utcScheduleDateTimeIso);
    return utcScheduleDateWithMilliseconds;
  }
  updateScheduledForm(){
    if(this.selectedRecipients){
    this.selectedOptions.recipients = this.selectedRecipients;
    }
    var scheduledForms={
      scheduleId : this.editScheduleData.id,
      recipients:[],
      formName:'',
      formId:'',
      message:'',
      name:'',
      time : '',
      timeZone:this.selectedOptions.timeZone,
      date:'',
      repeat:false,
      advanced:false,
      repeatOption:0,
      endDate:""
    };
    let validate = this.validateScheduledForm()
      if(validate){
        this.scheduleSending=true;
        let recipients=[];
        for(let i=0; i < this.selectedOptions.recipients.length; i++){
          let indexData:any = this.selectedOptions.recipients[i];
          let selectedFilterData :any = [];
          if(indexData && this.scheduledFormRecipientsArray) {
            selectedFilterData = this.scheduledFormRecipientsArray.filter(x => x.optionId == indexData);
            if(selectedFilterData && selectedFilterData[0]){
              selectedFilterData =selectedFilterData[0];
              let data:any = {email:selectedFilterData.name, userId: selectedFilterData.userId, admissionId: this.selectedOptions.admissionId};
              if(selectedFilterData.caregiver_userid) {
                data.caregiverUserId = selectedFilterData.caregiver_userid
              }
              recipients.push(data);
            } else{
              selectedFilterData = this.selectedRecipientsData.filter(x => x.id == indexData);
              if(selectedFilterData[0]){
                selectedFilterData =selectedFilterData[0];
                let data:any = {email:selectedFilterData.email, userId: selectedFilterData.id ? selectedFilterData.id.split('--')[0] : selectedFilterData.id, admissionId: this.selectedOptions.admissionId};
                if(selectedFilterData.caregiverUserId) {
                  data.caregiverUserId = selectedFilterData.caregiverUserId
                }
                recipients.push(data);
              }
            }
          }  
        }
        scheduledForms.date =  this.convertDateToUtc(this.selectedOptions.date);
        scheduledForms.repeat = this.selectedOptions.repeat;
        scheduledForms.advanced = this.selectedOptions.advanced;
        scheduledForms.name = this.selectedOptions.name;
        scheduledForms.recipients=recipients;
        scheduledForms.formName=this.selectedOptions.form;
        scheduledForms.formId  = this.selectedOptions.formId;
        scheduledForms.message=this.selectedOptions.message;
        scheduledForms.time = this.scheduledFormTimmer;
        scheduledForms.timeZone=this.selectedOptions.timeZone;
        scheduledForms.repeatOption=(this.selectedOptions.repeatOption=="D")?1:(this.selectedOptions.repeatOption=="W")?2:(this.selectedOptions.repeatOption=="M")?3:(this.selectedOptions.repeatOption=="e1M" || this.selectedOptions.repeatOption=="e5M") ? 4 : (this.selectedOptions.repeatOption=="3M") ? 5: (this.selectedOptions.repeatOption=="6M") ? 6 : (this.selectedOptions.repeatOption=="12M") ? 7 : 0;
        this.selectedOptions.endDate = this.convertDateToUtc(this.selectedOptions.endDate);
        scheduledForms.endDate=(this.selectedOptions.repeat && this.selectedOptions.repeatOption !="" && this.selectedOptions.endDate)?this.selectedOptions.endDate:'';

        this._structureService.updateScheduledForms(scheduledForms).subscribe((result) => {
            console.log("result-------->",result);
            if(!isNull(result.data.updateScheduledForms)){
            console.log(result['data']['updateScheduledForms']);
            var scheduleResult=result['data']['updateScheduledForms'];
            this.notifyPopup(`${scheduledForms.name} successfully Updated.`,'success');
            this.resetEditScheduleForm();
            this.scheduleSending=false;
            var activityData = {
              activityName: "Update Schedule",
              activityType: "formschedule",
              activityDescription: this.userData.displayName +" schedule a Form with name "+scheduledForms.name+ "("+scheduleResult.id+") and scheduled on "+((scheduledForms.advanced)?scheduledForms.time:scheduledForms.date)+ " (Repeat option-"+scheduledForms.repeat+" )"
            };
            // this.updateScheduleFormObject(scheduleResult.id,scheduleResult);
            this.loadScheduleFormsListTable();
            this._structureService.trackActivity(activityData);             
            } else {
              let activityData = {
                activityName: "Error Update Schedule",
                activityType: "formschedule",
                activityDescription: `Error occured while update Schedule : ${this.userData.displayName} schedule a Form with name ${scheduledForms.name} (${this.editScheduleData.id}) and scheduled on ${((scheduledForms.advanced)?scheduledForms.time:scheduledForms.date)} (Repeat option-${scheduledForms.repeat})`
              }; 
              this.scheduleSending = false;
              this.notifyPopup('Error Occur please try again.','warning');
              this._structureService.trackActivity(activityData);
            }
          },(error) => {
            console.log("updateScheduledForm : errorrrrrrrrrrrrrrrrrrr");
            console.log(error);
            console.log(error['graphQLErrors'])
            var activityData = {
              activityName: "Error Update Schedule",
              activityType: "formschedule",
              activityDescription: `Error occured while update Schedule : ${this.userData.displayName} schedule a Form with name ${scheduledForms.name} (${this.editScheduleData.id}) and scheduled on ${((scheduledForms.advanced)?scheduledForms.time:scheduledForms.date)} (Repeat option-${scheduledForms.repeat})`
            }; 
            this.scheduleSending = false;
            this.notifyPopup('Error Occur please try again.','warning');
            this._structureService.trackActivity(activityData);  
      });
        }
  }
  updateScheduleFormObject(id,scheduleResult){
    if(this.scheduledFormsList.length){
      for(var i=0;i<this.scheduledFormsList.length;i++){
        var data = this.scheduledFormsList[i];
        if(data.id==id){
          this.scheduledFormsList[i].scheduleName = scheduleResult.scheduleName;
          this.scheduledFormsList[i].formName = scheduleResult.formName;
          this.scheduledFormsList[i].recipients = scheduleResult.recipients;
          this.scheduledFormsList[i].scheduledOn = scheduleResult.scheduledOn;
          this.scheduledFormsList[i].scheduleDate=scheduleResult.scheduleDate;
          this.scheduledFormsList[i].scheduleRepeat = scheduleResult.scheduleRepeat;
          this.scheduledFormsList[i].nextScheduleDate = scheduleResult.nextScheduleDate;
          this.scheduledFormsList[i].status = scheduleResult.status;
          this.scheduledFormsList[i].endDate = (scheduleResult.endDate && scheduleResult.endDate!="")?scheduleResult.endDate:'';
        }
      }
    }
    return null;    
  }
  resetEditScheduleForm(){
    this.editScheduledForm=false;
    this.editScheduleData=[];
    this.selSelectOptions(true);
    this.addRecipientSelect2('Select Recipient(s)',true);
    this.weekSelectTimmerSet(true);
    this.addScheduleFormHideShow(true);
    this.resetSelectOption();
    this.errorReset();
    $("#schedule-date-picker1").data("DateTimePicker").minDate(new Date());
    $("#schedule-date-picker1 input").val('');
    $("#schedule-enddate-picker1").data("DateTimePicker").minDate(new Date());
    $("#schedule-enddate-picker1 input").val('');
  }
  stopAndStartScheduledForms(scheduledId,status){
    var self =this;
    let shStatus = "Start";
    if(status==2){
      shStatus = "Stop";
    }
    let data={
      timeZone : timezone.name()
    }
    swal({
      title: "Are you sure?",
      text: "You are going to "+shStatus.toLowerCase()+" this scheduled form",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Ok",
      closeOnConfirm: true
    },
      function (isConfirm) {
        if (isConfirm) {
          let index = self.scheduledFormsList.indexOf(self.activeRecord);
          self._structureService.stopScheduledForm(scheduledId,status,data).subscribe((data) => {
            var resultData=data['data']['stopOrStartScheduledForm'];
            var errorData=data['error'];
            if(resultData.id && !errorData){
              if(index>-1){
                let scheduleData = self.scheduledFormsList[index];
                console.log('self.scheduledFormsList',self.scheduledFormsList[index],self.scheduledFormsList[index].status);
                /*let shStatus = "Start";
                if(status==2){
                  //shStatus = "Stop";
                  self.scheduledFormsList[index].status="STOPPED";
                }else if(status==1){
                  self.scheduledFormsList[index].status="RUNNING";
                }*/
                var activityData = {
                  activityName: shStatus+" a Schedule",
                  activityType: "formschedule",
                  activityDescription: self.userData.displayName +" "+shStatus+" a schedule with Schedule name "+scheduleData.scheduleName+ " and scheduled on "+ ((scheduleData.scheduleRepeat)?scheduleData.scheduledOn:scheduleData.scheduleDate )+ " (Repeat option-"+scheduleData.scheduleRepeat+" )"
                };
                self._structureService.trackActivity(activityData); 
                self.loadScheduleFormsListTable();
              }else{
                console.log("index not get...........");
              }
            }else{
              console.log("Stop error from graphql..........");
            }
          });
        }
      });


      var cn = document.getElementsByClassName("btn-warning");
     
      cn[0].setAttribute("id", "stp_start_cnfrm_btn");


  }
  changeScheduleRunningStatus(){
    console.log("ffffffffffffffffffffffffffffffff");
  }
  resetTimmerSelectedOptions(){
    this.selectedOptions.day = '*';
    this.selectedOptions.weekselect='*';
    this.selectedOptions.everyMonth='*';
    this.selectedOptions.hours= '*';
    this.selectedOptions.minutes='*';
    this.selectedOptions.seconds='0';      
    this.selectedOptions.hourTypes="AM";
    this.scheduledFormTimmer='';
    this.selectedOptions.repeatOption ='';
    $(".month-days").val('');
    $(".months-every").val('');
    $(".hours").val('');
    $(".minutes").val('0');
    $(".seconds").val('0');
    $(".hour-types").val('AM');
    $("#schedule-enddate-picker1 input").val('');
  }
  changeRepeatStatus(){
    this.resetTimmerSelectedOptions();
    console.log("repeat--------->",this.selectedOptions.repeat);
    this.selectedOptions.advanced = false;
    $(".selectrepeatOption").val(''); 
  }
  changeAdvanceStatus(){
    this.resetTimmerSelectedOptions();
    console.log("advance--------->",this.selectedOptions.repeat);
    //$("#selectrepeatOption").attr("disabled",true);
    $(".selectrepeatOption").val('');    
  }
  notifyPopup(message,type){
   var notify = $.notify(message);
    setTimeout(function () {
      notify.update({ 'type': type, 'message': `<strong>${message}</strong>` });
    }); 
  }
  scrollToTopFunction() {
    console.log("scrollToTopFunction.............");
    document.body.scrollTop = 0; // For Safari
    document.documentElement.scrollTop = 0; // For Chrome, Firefox, IE and Opera
  }
  changeFormsSelection(){
    
  }
  removeSelectedRecipient(id) {
    console.group("removeSelectedRecipient = " + id, typeof id);
    var index = this.selectedRecipients.indexOf(id);
        this.deleteRemovedUserFromList(index, id);
  }
  deleteRemovedUserFromList(index, id) {
    delete this.selectedRecipients[index];
    this.setOrResetSelectedItemList(id);
    this.setSelectedRecipientForms(true);
    this.selectedRecipients = this.selectedRecipients.filter((id) => { console.log(id); return id != "" });
  }
  setOrResetSelectedItemList(id) {
    $("#"+id).remove();
    return true;
  }
  checkUserExist(user) {
    if (this.selectedRecipients.indexOf(String(user)) > -1) {
      return true;
    }
    return false;
  }
  recipientInputFocus() {
    console.log("recipientInputFocus :call enableOrDisableUiLI.......");
    this.enableOrDisableUiLI(true, false, "R");
  }
  doneSelectedRecipient() {
    if (this.selectedRecipients && this.selectedRecipients.length)
      this.enableOrDisableUiLI(false, true, "R");
  }
  enableOrDisableUiLI(condition, clear: boolean = true, from: any = "") {
    console.log(`enableOrDisableUiLI condition = ${condition}, clear = ${clear}`);
    if (condition) {
      if (from == "R") {
        if ($('ul#associate-ul').css('display') == 'block') {
          this.enableOrDisableUiLI(false, false);
        }
        $("#tagsInput").addClass('ul-active');
        $("ul#recipient-ul").css('display', 'block');
      } else {
        if ($('#recipient-ul').css('display') == 'block') {
          this.enableOrDisableUiLI(false, false, "R");
        }
        $("ul#associate-ul").css('display', 'block');
        $("input#associate-search-input").addClass("active");
      }

    } else {
      if (from == "R") {
        $("#tagsInput").removeClass("ul-active");
        $("ul#recipient-ul").css('display', 'none');
      } else {
        $("ul#associate-ul").css('display', 'none');
        $("input#associate-search-input").removeClass("active");
      }

    }
    if (clear) {
      console.log("enableOrDisableUiLI : Clear.........")
      if (from == "R") {
        $("#tagsInput").val("");
        $("#tagsInput").attr("placeholder", "Search Recipients");
      } else {
        $("input#associate-search-input").val('');
      }

    }
  }
  checkRecipientWithTems() {
    var textValue = $("#tagsInput").val();
    var searchText = textValue;//.replace(/[^a-z0-9\+\-\.\#]/ig,'');
    if (textValue != "") {
      this.setRecipient(searchText,this.isStaffFacing);
    }
  }
  setRecipient(searchKeyword: any = "",stafFacing='') {
    var isStaffFacing = false;

    if(stafFacing != "false") {
      isStaffFacing = true;
    }
    console.log("isStaffFacing",isStaffFacing);
    if (!this.recipientLoading) {
      console.log("setRecipient grapgql called.......");
      $("#recipient-search").text(" ").text("Loading");
      console.log("this.selectedFormTypeId",this.selectedFormTypeId);
      this._formsService.getTenantUsersByRoleId(this._structureService.getCookie('tenantId'), this.selectedFormTypeId, this.userData.roleId, isStaffFacing,searchKeyword, this.selectedSiteId, true).then(async (res : any)=>{
        const result = this.userFilter.transform(res);
        console.log("getFormRecipients result 1 ", result);
        this.scheduledFormRecipients = result;
        if (result && result.length > 0) {
          this.scheduledFormRecipients =  this.scheduledFormRecipients.filter((result) => {
            var date = "";
            if (result.caregiver_userid) {
              if (result.caregiver_dob) {
                date = result.caregiver_dob;
                var dobDay = new Date(date).getDay();
                if (date && !isNaN(dobDay)) {
                  date = date.replace(/-/g, '/');

                  try {
                    // date = this._datePipe.transform(date, 'MM/dd/yyyy');
                    date = moment(date).format('MM/DD/YYYY');
                  } catch (e) {
                    date = '';
                  }
                } else {
                  date = "";
                }
              } else {
                date = "";
              }
              result.caregiver_dob_formatted = date;
            } else {
              if (result.dob) {
                date = result.dob;
                var dobDay = new Date(date).getDay();
                if (date && !isNaN(dobDay)) {
                  date = date.replace(/-/g, '/');
                  try {
                    // date = this._datePipe.transform(date, 'MM/dd/yyyy');
                    date = moment(date).format('MM/DD/YYYY');
                  }
                  catch (e) {
                    date = '';
                  }
                } else {
                  date = "";
                }
              } else {
                date = "";
              }
              result.dob_formatted = date;
            }
            result.optionId = (result.caregiver_userid ? result.userId + '--' + result.caregiver_userid : result.userId);
            result.recipientOptionText = (result.tag_name ? result.tag_name + ' [User Tag]' : ((result.roleId == "3") ? (result.caregiver_userid ? (result.caregiver_dob_formatted ? (result.caregiver_displayname + ' (' + result.displayname + ')'+ ' - ' + result.caregiver_dob_formatted) : result.caregiver_displayname + ' (' + result.displayname + ')') : (result.dob ? result.displayname + ' - ' + result.dob_formatted : result.displayname)) : (result.displayname)));
            result.recipientOptionText += result.IdentityValue ? ' [MRN: '+result.IdentityValue+']':result.caregiverIdentityValue?' [MRN: '+result.caregiverIdentityValue+']':"";
            this.enableOrDisableUiLI(true, false, "R");
            $("#recipient-search").text(" ").text("Search");
            return true;
          });
          console.log("getFormRecipients result 2 ", result);
        } else {
          console.log("No result.............");
          this.enableOrDisableUiLI(true, false, "R");
          $("#recipient-search").text(" ").text("Search");
        }
      }).then(function () {
        console.log('after qpi call 3255');
      }).catch((ex) => {
      });
    } else {

    }
    console.log("set recipient loaded");

  }
  closeSelectedRecipient(condition: boolean = false) {
    $("#recipient-search").text(" ").text("Search");
      this.resetRecipient(condition);
  }
  resetRecipient(condition: boolean = false) {
    this.selectedRecipients = [];
    this.scheduledFormRecipients = [];
    this.scheduledFormRecipientsArray = [];
    $(".tag-span").remove();
    if (!condition)
      this.enableOrDisableUiLI(false, true, "R");
  }
  setSelectedRecipients(users, user) {
    if (users.noContactAvailable) return;
    console.log("users============>");
    console.log(users);
    console.log("user=============");
    console.log(user);
    let selectedIndex = this.selectedRecipients.indexOf(user);
    if (selectedIndex == -1) {
      this.selectedRecipients.push(user);
      this.scheduledFormRecipientsArray.push(users);
      this.selectedRecipientNames.push({ text: users.recipientOptionText });
      console.log("this.selectedOptions.recipients",this.selectedOptions.recipients);
      this.setSelectedRecipientForms(false,users);
    } else {
      this.removeSelectedRecipient(user);
    }
  }
  setSelectedRecipientForms(fromRemove = false,users: any = "") {
    console.log("setSelectedRecipientForms this.selectedRecipients",this.selectedRecipients);
    if (this.selectedRecipients && this.selectedRecipients.length > 0) {
      var recipients = this.selectedRecipients;
      var recipientName = this.selectedOptions.recipients//$('#tenantUsers').select2('data');
      console.log(recipients);
      recipients = recipients.filter(a => a.indexOf('tag-') === -1)
      console.log(recipients)
      if (recipients.length > 0) {
        this.receipientCount = recipients.length;
        var recipi = this.selectedRecipients;
        console.log(recipi.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join())
          var textData = users.recipientOptionText;
          if (!fromRemove) {
          $("<span/>", { "class": 'tag-span', 'id': users.optionId, text: textData, insertBefore: $(".recipient-search-area") }).append("<span class='remove' id=" + users.optionId + ">x</span>");
          }
          var activityData = {
        activityName: "Select Recipients",
        activityType: "scheduled forms",
        activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected recipient -" + this.selectedRecipients + "for scheduled forms"
      };
      this._structureService.trackActivity(activityData);
    }
  }
}
selectAllRecipients() {
  if (this.scheduledFormRecipients && this.scheduledFormRecipients.length > 0) {
    console.log("this.scheduledFormRecipients",this.scheduledFormRecipients);
    this.scheduledFormRecipients.forEach(element => {
      var id = element.optionId;
      var index = this.selectedRecipients.indexOf(id);
      if (index == -1) {
        this.setSelectedRecipients(element, id);
      }
    });
  }
}
getSelectedSiteIds(data: any): void {
    this.selectedSiteId = data.siteId;
    if(this.editScheduledForm){
        const array = [];
         this.selectedRecipientsData.forEach((d) => {
             if (this.selectedSiteId.includes((d.siteId).toString())) {
                 array.push(d);
             }
         });
         this.selectedRecipientsData = array;
    }else{
        this.closeSelectedRecipient();
    }
}
hideDropdown(hideItem : any){
    this.hideSiteSelection = hideItem.hideItem;
}
getSiteIds(data: any): void {
    this.siteIds = data.siteId;
    this.loadScheduleFormsListTable('refresh');
}
timeZoneChange(event) {
  this.selectedOptions.timeZone = event.target.value;
  }
selectedAdmissionHandle(event) {
  if (!isBlank(event) && !isBlank(event.id)){
    this.selectedOptions.admissionId = event.id
  }
}

}
