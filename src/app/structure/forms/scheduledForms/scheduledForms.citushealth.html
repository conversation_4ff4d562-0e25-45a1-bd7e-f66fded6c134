<section class="card">
    <div [hidden]='!isActive'>
    <div class="card-header">
        <span class="cat__core__title">
            <strong>{{scheduleFormHeaderTittle}}</strong>
            <a class="pull-right btn btn-sm btn-primary" (click)="addScheduleFormHideShow(isActive)" id="back_btn">Back<i class="ml-1"></i></a>
        </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item">{{scheduleFormHeaderTittle}}</li>
        </ol>
        <div class="wrapper-content ">
            <!-- <div class="row">
                <button type="button" (click)="addScheduleFormHideShow(isActive)" class="btn btn-primary swal-btn-info">{{createButtonText}}</button>
            </div> -->
            <div class="row">
                <div class="col-lg-12">
                    <div class="mb-5">
                        <!-- <form action="#"> -->
                        <div class="form-body">
                            <div class="form-group row" *ngIf="!editScheduledForm && hideSiteSelection">
                                <label class="col-md-3 control-label">{{ labelSites | translate}} *</label>
                                <div class="col-md-6">
                                    <app-select-sites [events]="eventsSubject.asObservable()"  [hideApplyFilter]=true [singleSelection]=false  [siteSelection]="true" (siteIds)="getSelectedSiteIds($event)" (hideDropdown)="hideDropdown($event)">
                                    </app-select-sites>
                                </div>
                            </div>
                            <div class="form-group row" *ngIf="editScheduledForm && hideSiteSelection">
                                <label class="col-md-3 control-label">{{ labelSites | translate}} *</label>
                                <div class="col-md-6">
                                    <app-select-sites (hideDropdown)="hideDropdown($event)" [events]="eventsSubject.asObservable()" [hideApplyFilter]=true [filterType]=true [selectedSiteIds]="selectedRecipientsSiteId" [singleSelection]="singleSelection" (siteIds)="getSelectedSiteIds($event)">
                                    </app-select-sites>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-md-3 control-label">Subject *</label>
                                <div class="col-md-6">
                                    <input type="text" [(ngModel)]="selectedOptions.name" class="form-control" placeholder="Subject" id="sub_filed">
                                    <span id="error-name" class="error"></span>
                                </div>
                            </div>
                            
                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Forms * </label>
                                </div>
                                <div class="col-md-6">
                                    <select class="form-control select2-forms" id="form_type" required (change)="changeFormsSelection()">
                                            <option value="">Select One</option>
                                            <option *ngFor="let formtag of formsTagList; let i = index" [attr.data-index]="i" id="{{i}}" value="{{formtag.id}}"> {{formtag.name}} </option>
                                        </select>
                                    <span id="error-form" class="error"></span>
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Recipients * </label>
                                </div>
                                <div class="col-md-5 select-rec">
                                    <!-- <div class="col-md-9"> -->
                                      <div id="tags" style="padding: 0px !important;">
                                        <span *ngFor="let data of selectedRecipientsData" [hidden]="!editScheduledForm" class="tag-span" id="{{data.id}}">
                                            {{ data.nameDob }}<span class="remove" id="{{data.id}}">x</span>
                                        </span>
                                        <div class="recipient-search-area">
                                          <div class="input-dropdown">
                                            <input
                                              type="text"
                                              class="form-control"
                                              id="tagsInput"
                                              autocomplete="off"
                                              value=""
                                              [disabled]="recipientLoading"
                                              style="width:143%;margin-left: 0;"
                                              *ngIf="!_structureService.isMultiAdmissionsEnabled || (_structureService.isMultiAdmissionsEnabled && selectedRecipients.length === 0)"
                                              placeholder="Search Recipients"/>
                                            <ul *ngIf="!_structureService.isMultiAdmissionsEnabled || (_structureService.isMultiAdmissionsEnabled && selectedRecipients.length === 0)" class="associate-ul recipient-ul" id="recipient-ul" style="width: 143% !important;">
                                              <li
                                                id="recipient-li"
                                                class="associate-li recipient-li"
                                                *ngIf="scheduledFormRecipients && scheduledFormRecipients.length == 0">
                                                No item found
                                              </li>
                                              <li
                                                id="li-{{ user.optionId }}"
                                                class="associate-li recipient-li"
                                                [ngClass]="{
                                                  'li-selected': checkUserExist(user.optionId),
                                                  'custom-disabled': user.noContactAvailable
                                                }"
                                                [chToolTip]="user.noContactAvailable ? 'noContactUser': ''"
                                                *ngFor="let user of scheduledFormRecipients" value="{{user.optionId}}"
                                                (click)="setSelectedRecipients(user, user.optionId)">
                                                {{ user.recipientOptionText }}
                                              </li>
                                              <li
                                                class="render-manipulate"
                                                *ngIf="scheduledFormRecipients && scheduledFormRecipients.length > 0 && !_structureService.isMultiAdmissionsEnabled">
                                                <input
                                                  type="button"
                                                  class="recipient-select-all btn"
                                                  (click)="selectAllRecipients()"
                                                  value="Select All"/>
                                                <input
                                                  type="button"
                                                  class="recipient-class-clear btn"
                                                  (click)="closeSelectedRecipient(true)"
                                                  value="Clear All"/>
                                                <input
                                                  type="button"
                                                  class="recipient-class-done btn"
                                                  *ngIf="selectedRecipients.length > 0"
                                                  (click)="doneSelectedRecipient()"
                                                  value="Done"
                                                />
                                              </li>
                                            </ul>
                                          </div>
                                        </div>
                                      </div>
                                    <!-- </div> -->
                                    <span id="error-recipient" class="error"></span>
                                </div>
                                <div class="col-md-4 recipient-actions" style="position: relative;">
                                    <button
                                      type="button"
                                      [disabled]="recipientLoading"
                                      id="recipient-search" style="width: 62px;"
                                      (click)="checkRecipientWithTems()"
                                      class="recipient-search-button btn btn-sm btn-primary" id="srch_btn">
                                      Search
                                    </button>
                                    <button
                                      type="button"
                                      [disabled]="recipientLoading"
                                      id="recipient-close" style="width: 62px;"
                                      (click)="closeSelectedRecipient()"
                                      class="recipient-search-button btn btn-sm btn-default recipient-close" id="reset_btn">
                                      Reset
                                    </button>
                                  </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-md-3">{{'LABELS.TIMEZONE' | translate }}</div>
                                <div class="col-md-6">
                                  <select  id="timezone-dropdown" class="form-control" [(ngModel)]="selectedOptions.timeZone" (change)="timeZoneChange($event)">
                                    <option *ngFor="let timeZone of timezones" value="{{ timeZone.city }}" [selected]="timeZone.city === selectedOptions.timeZone">{{ timeZone.name }}</option>
                                  </select>
                                </div>
                              </div>

                            <div class="form-group row" style="padding-top:5px;" *ngIf="_structureService.isMultiAdmissionsEnabled && selectedRecipients[0]">
                                <div class="col-md-3">
                                    <label class="control-label">{{ 'ADMISSION.LABELS.ADMISSION' | translate }} * </label>
                                </div>
                                <div class="col-md-6">
                                    <app-admissions-dropdown
                                        [siteIds]="selectedSiteId"
                                        [selectedPatient]="selectedRecipients[0]"
                                        [selectedAdmissionId]="selectedOptions.admissionId"
                                        (selectedItem)="selectedAdmissionHandle($event)"
                                        ></app-admissions-dropdown>
                                    <span id="error-admission" class="error"></span>
                                </div>
                            </div>
                            
                            <div class="form-group row" style="padding-top:5px;">
                                <div class="col-md-3">
                                    <label class="control-label">Start Time * </label>
                                </div>
                                <div class="col-md-4">
                                    <div class='input-group date' id='schedule-date-picker1'>
                                        <input readonly="readonly" type='text' id="start_date" placeholder="Pick a start date" class="form-control" />
                                        <span class="input-group-addon">
                                            <span class="fa fa-calendar"></span>
                                        </span>
                                    </div>
                                    <span id="error-start" class="error"></span>
                                </div>
                                <div class="col-md-2" [hidden]='!selectedOptions.date'>
                                    <input type="checkbox" id="rep_check" [(ngModel)]="selectedOptions.repeat" (ngModelChange)="changeRepeatStatus()" /> Repeat
                                </div>
                            </div>
                            <div class="form-group row" [hidden]='!selectedOptions.repeat'>
                                <div class="col-md-3">
                                    <label class="control-label">Select Repeat option</label>
                                </div>
                                <div class="col-md-4">
                                    <select class="form-control selectrepeatOption" [attr.disabled]="selectedOptions.advanced ? selectedOptions.advanced : null" name="selectrepeatOption">                                    
                                        <option value="" disabled selected>Select Option</option>
                                        <!-- <option *ngIf="environment == 'test' || environment == 'devl'" label="Every 1 Minute" value="e1M">Every 1 Minute</option> -->
                                        <option [hidden]="environment == 'portal'" label="Every 5 Minute" value="e5M">Every 5 Minute</option>
                                        <option label="Daily" value="D">Daily</option>
                                        <option label="Weekly" value="W">Weekly</option>
                                        <option label="Monthly" value="M">Monthly</option>
                                        <option label="3 Months" value="3M">3 Months</option>
                                        <option label="6 Months" value="6M">6 Months</option>
                                        <option label="12 Months" value="12M">12 Months</option>
                                    </select>
                                    <span id="error-selectrepeatOption" class="error"></span>
                                </div>
                                <div class="col-md-2" [hidden]="advanceOptionCommented">
                                    <input type="checkbox" [(ngModel)]="selectedOptions.advanced" (ngModelChange)="changeAdvanceStatus()" /> Advanced
                                </div>
                            </div>
                            <div class="form-group row" [hidden]="advanceOptionCommented" ><!--[hidden]='!selectedOptions.advanced'-->
                                <div class="col-md-3">
                                    <label class="control-label">Time * </label>
                                </div>
                                <div class="col-md-9">
                                    <div class="row">
                                        <!-- <div class="col-md-1">
                                                <label class="schedule-time-label">On the</label>
                                            </div> -->
                                        <div class="col-md-2">
                                            <select class="form-control month-days" name="monthlySpecificDayDay" required="required">
                                                    <option value="" disabled selected>Select Day</option>
                                                    <option label="1st Day" value="1">1st Day</option>
                                                    <option label="2nd Day" value="2">2nd Day</option>
                                                    <option label="3rd Day" value="3">3rd Day</option>
                                                    <option label="4th Day" value="4">4th Day</option>
                                                    <option label="5th Day" value="5">5th Day</option>
                                                    <option label="6th Day" value="6">6th Day</option>
                                                    <option label="7th Day" value="7">7th Day</option>
                                                    <option label="8th Day" value="8">8th Day</option>
                                                    <option label="9th Day" value="9">9th Day</option>
                                                    <option label="10th Day" value="10">10th Day</option>
                                                    <option label="11th Day" value="11">11th Day</option>
                                                    <option label="12th Day" value="12">12th Day</option>
                                                    <option label="13th Day" value="13">13th Day</option>
                                                    <option label="14th Day" value="14">14th Day</option>
                                                    <option label="15th Day" value="15">15th Day</option>
                                                    <option label="16th Day" value="16">16th Day</option>
                                                    <option label="17th Day" value="17">17th Day</option>
                                                    <option label="18th Day" value="18">18th Day</option>
                                                    <option label="19th Day" value="19">19th Day</option>
                                                    <option label="20th Day" value="20">20th Day</option>
                                                    <option label="21st Day" value="21">21st Day</option>
                                                    <option label="22nd Day" value="22">22nd Day</option>
                                                    <option label="23rd Day" value="23">23rd Day</option>
                                                    <option label="24th Day" value="24">24th Day</option>
                                                    <option label="25th Day" value="25">25th Day</option>
                                                    <option label="26th Day" value="26">26th Day</option>
                                                    <option label="27th Day" value="27">27th Day</option>
                                                    <option label="28th Day" value="28">28th Day</option>
                                                    <option label="29th Day" value="29">29th Day</option>
                                                    <option label="30th Day" value="30">30th Day</option>
                                                    <option label="31st Day" value="31">31st Day</option>
                                                </select>
                                        </div>
                                        <div class="col-md-1"> <label class="schedule-time-label">OR </label></div>
                                        <div class="col-md-5">
                                            <select class="form-control weekselect" name="monthlySpecificDayDay" multiple required="required">                                    
                                                    <!-- <option label="First Weekday" value="1W">First Weekday</option> -->
                                                    <option label="1st Day" value="1">Monday</option>
                                                    <option label="2nd Day" value="2">Tuesday</option>
                                                    <option label="3rd Day" value="3">Wednesday</option>
                                                    <option label="4th Day" value="4">Thursday</option>
                                                    <option label="5th Day" value="5">Friday</option>
                                                    <option label="6th Day" value="6">Saturday</option>
                                                    <option label="7th Day" value="0">Sunday</option>
                                                </select>
                                        </div>
                                    </div>
                                    <div class="row clear"></div>
                                    <div class="row">
                                        <div class="col-md-12  width-adjust">
                                            <div class="form-group  every-row">
                                                <label class="schedule-time-label first-every">Every</label>
                                                <select class="form-control months-every" name="monthlySpecificDayMonths" required="required">
                                                            <option label="None" value="">None</option>
                                                            <option label="1" value="1">1</option>
                                                            <option label="2" value="2">2</option>
                                                            <option label="3" value="3">3</option>
                                                            <option label="4" value="4">4</option>
                                                            <option label="5" value="5">5</option>
                                                            <option label="6" value="6">6</option>
                                                            <option label="7" value="7">7</option>
                                                            <option label="8" value="8">8</option>
                                                            <option label="9" value="9">9</option>
                                                            <option label="10" value="10">10</option>
                                                            <option label="11" value="11">11</option>
                                                            <option label="12" value="12">12</option>
                                                        </select>
                                                <label class="schedule-time-label">Month(s) at</label>
                                                <select class="form-control hours" name="Hours" required="required">
                                                <option label="Hour" value="">Hour</option>
                                                <option label="01" value="1">01</option>
                                                <option label="02" value="2">02</option>
                                                <option label="03" value="3">03</option>
                                                <option label="04" value="4">04</option>
                                                <option label="05" value="5">05</option>
                                                <option label="06" value="6">06</option>
                                                <option label="07" value="7">07</option>
                                                <option label="08" value="8">08</option>
                                                <option label="09" value="9" >09</option>
                                                <option label="10" value="10">10</option>
                                                <option label="11" value="11">11</option>
                                                <option label="12" value="12">12</option>
                                            </select>
                                                <select class="form-control minutes" id="minutesSelection" style="display:none" name="Minutes" required="required">
                                                    <option label="Minutes" value="0">Minutes</option>
                                                    <option label="00" value="00">00</option>
                                                    <option label="01" value="01">01</option>
                                                    <option label="02" value="02">02</option>
                                                    <option label="03" value="03">03</option>
                                                    <option label="04" value="04">04</option>
                                                    <option label="05" value="05">05</option>
                                                    <option label="06" value="06">06</option>
                                                    <option label="07" value="07">07</option>
                                                    <option label="08" value="08">08</option>
                                                    <option label="09" value="09">09</option>
                                                    <option label="10" value="10">10</option>
                                                    <option label="11" value="11">11</option>
                                                    <option label="12" value="12">12</option>
                                                    <option label="13" value="13">13</option>
                                                    <option label="14" value="14">14</option>
                                                    <option label="15" value="15">15</option>
                                                    <option label="16" value="16">16</option>
                                                    <option label="17" value="17">17</option>
                                                    <option label="18" value="18">18</option>
                                                    <option label="19" value="19">19</option>
                                                    <option label="20" value="20">20</option>
                                                    <option label="21" value="21">21</option>
                                                    <option label="22" value="22">22</option>
                                                    <option label="23" value="23">23</option>
                                                    <option label="24" value="24">24</option>
                                                    <option label="25" value="25">25</option>
                                                    <option label="26" value="26">26</option>
                                                    <option label="27" value="27">27</option>
                                                    <option label="28" value="28">28</option>
                                                    <option label="29" value="29">29</option>
                                                    <option label="30" value="30">30</option>
                                                    <option label="31" value="31">31</option>
                                                    <option label="32" value="32">32</option>
                                                    <option label="33" value="33">33</option>
                                                    <option label="34" value="34">34</option>
                                                    <option label="35" value="35">35</option>
                                                    <option label="36" value="36">36</option>
                                                    <option label="37" value="37">37</option>
                                                    <option label="38" value="38">38</option>
                                                    <option label="39" value="39">39</option>
                                                    <option label="40" value="40">40</option>
                                                    <option label="41" value="41">41</option>
                                                    <option label="42" value="42">42</option>
                                                    <option label="43" value="43">43</option>
                                                    <option label="44" value="44">44</option>
                                                    <option label="45" value="45">45</option>
                                                    <option label="46" value="46">46</option>
                                                    <option label="47" value="47">47</option>
                                                    <option label="48" value="48">48</option>
                                                    <option label="49" value="49">49</option>
                                                    <option label="50" value="50">50</option>
                                                    <option label="51" value="51">51</option>
                                                    <option label="52" value="52">52</option>
                                                    <option label="53" value="53">53</option>
                                                    <option label="54" value="54">54</option>
                                                    <option label="55" value="55">55</option>
                                                    <option label="56" value="56">56</option>
                                                    <option label="57" value="57">57</option>
                                                    <option label="58" value="58">58</option>
                                                    <option label="59" value="59">59</option>
                                                </select>
                                                <select class="form-control  hour-types" name="HourType" required="required">
                                                        <option label="AM" value="AM" selected >AM</option>
                                                        <option label="PM" value="PM">PM</option>
                                                    </select>
                                            </div>

                                        </div>
                                        <!-- <div class="col-md-1  width-adjust-ms">
                                            <select class="form-control seconds" name="Seconds" required="required">
                                                    <option label="Seconds" value="0">Seconds</option>
                                                     <option label="00" value="0">00</option>
                                                    <option label="01" value="1">01</option>
                                                    <option label="02" value="2">02</option>
                                                    <option label="03" value="3">03</option>
                                                    <option label="04" value="4">04</option>
                                                    <option label="05" value="5">05</option>
                                                    <option label="06" value="6">06</option>
                                                    <option label="07" value="7">07</option>
                                                    <option label="08" value="8">08</option>
                                                    <option label="09" value="9">09</option>
                                                    <option label="10" value="10">10</option>
                                                    <option label="11" value="11">11</option>
                                                    <option label="12" value="12">12</option>
                                                    <option label="13" value="13">13</option>
                                                    <option label="14" value="14">14</option>
                                                    <option label="15" value="15">15</option>
                                                    <option label="16" value="16">16</option>
                                                    <option label="17" value="17">17</option>
                                                    <option label="18" value="18">18</option>
                                                    <option label="19" value="19">19</option>
                                                    <option label="20" value="20">20</option>
                                                    <option label="21" value="21">21</option>
                                                    <option label="22" value="22">22</option>
                                                    <option label="23" value="23">23</option>
                                                    <option label="24" value="24">24</option>
                                                    <option label="25" value="25">25</option>
                                                    <option label="26" value="26">26</option>
                                                    <option label="27" value="27">27</option>
                                                    <option label="28" value="28">28</option>
                                                    <option label="29" value="29">29</option>
                                                    <option label="30" value="30">30</option>
                                                    <option label="31" value="31">31</option>
                                                    <option label="32" value="32">32</option>
                                                    <option label="33" value="33">33</option>
                                                    <option label="34" value="34">34</option>
                                                    <option label="35" value="35">35</option>
                                                    <option label="36" value="36">36</option>
                                                    <option label="37" value="37">37</option>
                                                    <option label="38" value="38">38</option>
                                                    <option label="39" value="39">39</option>
                                                    <option label="40" value="40">40</option>
                                                    <option label="41" value="41">41</option>
                                                    <option label="42" value="42">42</option>
                                                    <option label="43" value="43">43</option>
                                                    <option label="44" value="44">44</option>
                                                    <option label="45" value="45">45</option>
                                                    <option label="46" value="46">46</option>
                                                    <option label="47" value="47">47</option>
                                                    <option label="48" value="48">48</option>
                                                    <option label="49" value="49">49</option>
                                                    <option label="50" value="50">50</option>
                                                    <option label="51" value="51">51</option>
                                                    <option label="52" value="52">52</option>
                                                    <option label="53" value="53">53</option>
                                                    <option label="54" value="54">54</option>
                                                    <option label="55" value="55">55</option>
                                                    <option label="56" value="56">56</option>
                                                    <option label="57" value="57">57</option>
                                                    <option label="58" value="58">58</option>
                                                    <option label="59" value="59">59</option>
                                                </select>
                                        </div> -->

                                    </div>
                                    <!-- <div class="well well-small">
                                            On the
                                            <select class="form-control width-adjust month-days" name="monthlySpecificDayDay" required="required"><option label="First Weekday" value="1W">First Weekday</option><option label="1st Day" value="1">1st Day</option><option label="2nd Day" value="2">2nd Day</option><option label="3rd Day" value="3">3rd Day</option><option label="4th Day" value="4">4th Day</option><option label="5th Day" value="5">5th Day</option><option label="6th Day" value="6">6th Day</option><option label="7th Day" value="7">7th Day</option><option label="8th Day" value="8">8th Day</option><option label="9th Day" value="9" selected="selected">9th Day</option><option label="10th Day" value="10">10th Day</option><option label="11th Day" value="11">11th Day</option><option label="12th Day" value="12">12th Day</option><option label="13th Day" value="13">13th Day</option><option label="14th Day" value="14">14th Day</option><option label="15th Day" value="15">15th Day</option><option label="16th Day" value="16">16th Day</option><option label="17th Day" value="17">17th Day</option><option label="18th Day" value="18">18th Day</option><option label="19th Day" value="19">19th Day</option><option label="20th Day" value="20">20th Day</option><option label="21st Day" value="21">21st Day</option><option label="22nd Day" value="22">22nd Day</option><option label="23rd Day" value="23">23rd Day</option><option label="24th Day" value="24">24th Day</option><option label="25th Day" value="string:25">25th Day</option><option label="26th Day" value="string:26">26th Day</option><option label="27th Day" value="string:27">27th Day</option><option label="28th Day" value="string:28">28th Day</option><option label="29th Day" value="string:29">29th Day</option><option label="30th Day" value="string:30">30th Day</option><option label="31st Day" value="string:31">31st Day</option><option label="Last Weekday" value="string:LW">Last Weekday</option><option label="Last Day" value="string:L">Last Day</option></select>                                            of every
                                            <select class="form-control width-adjust months-small" name="monthlySpecificDayMonths" required="required"><option label="1" value="1">1</option><option label="2" value="number:2">2</option><option label="3" value="number:3">3</option><option label="4" value="number:4">4</option><option label="5" value="number:5">5</option><option label="6" value="number:6">6</option><option label="7" value="number:7">7</option><option label="8" value="number:8" selected="selected">8</option><option label="9" value="number:9">9</option><option label="10" value="number:10">10</option><option label="11" value="number:11">11</option><option label="12" value="number:12">12</option></select>                                            month(s) at
                                            <select class="form-control width-adjust hours" name="Hours" required="required"><option label="01" value="number:1">01</option><option label="02" value="number:2">02</option><option label="03" value="number:3">03</option><option label="04" value="number:4">04</option><option label="05" value="number:5">05</option><option label="06" value="number:6">06</option><option label="07" value="number:7">07</option><option label="08" value="number:8">08</option><option label="09" value="number:9" selected="selected">09</option><option label="10" value="number:10">10</option><option label="11" value="number:11">11</option><option label="12" value="number:12">12</option></select>
                                            <select class="form-control width-adjust minutes" name="Minutes" required="required"><option label="00" value="number:0">00</option><option label="01" value="number:1">01</option><option label="02" value="number:2">02</option><option label="03" value="number:3">03</option><option label="04" value="number:4">04</option><option label="05" value="number:5">05</option><option label="06" value="number:6">06</option><option label="07" value="number:7">07</option><option label="08" value="number:8" selected="selected">08</option><option label="09" value="number:9">09</option><option label="10" value="number:10">10</option><option label="11" value="number:11">11</option><option label="12" value="number:12">12</option><option label="13" value="number:13">13</option><option label="14" value="number:14">14</option><option label="15" value="number:15">15</option><option label="16" value="number:16">16</option><option label="17" value="number:17">17</option><option label="18" value="number:18">18</option><option label="19" value="number:19">19</option><option label="20" value="number:20">20</option><option label="21" value="number:21">21</option><option label="22" value="number:22">22</option><option label="23" value="number:23">23</option><option label="24" value="number:24">24</option><option label="25" value="number:25">25</option><option label="26" value="number:26">26</option><option label="27" value="number:27">27</option><option label="28" value="number:28">28</option><option label="29" value="number:29">29</option><option label="30" value="number:30">30</option><option label="31" value="number:31">31</option><option label="32" value="number:32">32</option><option label="33" value="number:33">33</option><option label="34" value="number:34">34</option><option label="35" value="number:35">35</option><option label="36" value="number:36">36</option><option label="37" value="number:37">37</option><option label="38" value="number:38">38</option><option label="39" value="number:39">39</option><option label="40" value="number:40">40</option><option label="41" value="number:41">41</option><option label="42" value="number:42">42</option><option label="43" value="number:43">43</option><option label="44" value="number:44">44</option><option label="45" value="number:45">45</option><option label="46" value="number:46">46</option><option label="47" value="number:47">47</option><option label="48" value="number:48">48</option><option label="49" value="number:49">49</option><option label="50" value="number:50">50</option><option label="51" value="number:51">51</option><option label="52" value="number:52">52</option><option label="53" value="number:53">53</option><option label="54" value="number:54">54</option><option label="55" value="number:55">55</option><option label="56" value="number:56">56</option><option label="57" value="number:57">57</option><option label="58" value="number:58">58</option><option label="59" value="number:59">59</option></select>
                                            <select class="form-control width-adjust seconds" name="Seconds" required="required"><option label="00" value="number:0">00</option><option label="01" value="number:1">01</option><option label="02" value="number:2">02</option><option label="03" value="number:3">03</option><option label="04" value="number:4">04</option><option label="05" value="number:5">05</option><option label="06" value="number:6">06</option><option label="07" value="number:7">07</option><option label="08" value="number:8">08</option><option label="09" value="number:9">09</option><option label="10" value="number:10">10</option><option label="11" value="number:11">11</option><option label="12" value="number:12">12</option><option label="13" value="number:13">13</option><option label="14" value="number:14">14</option><option label="15" value="number:15">15</option><option label="16" value="number:16">16</option><option label="17" value="number:17">17</option><option label="18" value="number:18">18</option><option label="19" value="number:19">19</option><option label="20" value="number:20">20</option><option label="21" value="number:21">21</option><option label="22" value="number:22">22</option><option label="23" value="number:23">23</option><option label="24" value="number:24">24</option><option label="25" value="number:25">25</option><option label="26" value="number:26">26</option><option label="27" value="number:27">27</option><option label="28" value="number:28">28</option><option label="29" value="number:29">29</option><option label="30" value="number:30">30</option><option label="31" value="number:31">31</option><option label="32" value="number:32">32</option><option label="33" value="number:33">33</option><option label="34" value="number:34">34</option><option label="35" value="number:35">35</option><option label="36" value="number:36">36</option><option label="37" value="number:37">37</option><option label="38" value="number:38">38</option><option label="39" value="number:39">39</option><option label="40" value="number:40">40</option><option label="41" value="number:41">41</option><option label="42" value="number:42">42</option><option label="43" value="number:43">43</option><option label="44" value="number:44">44</option><option label="45" value="number:45">45</option><option label="46" value="number:46">46</option><option label="47" value="number:47">47</option><option label="48" value="number:48">48</option><option label="49" value="number:49">49</option><option label="50" value="number:50">50</option><option label="51" value="number:51">51</option><option label="52" value="number:52">52</option><option label="53" value="number:53">53</option><option label="54" value="number:54">54</option><option label="55" value="number:55">55</option><option label="56" value="number:56">56</option><option label="57" value="number:57">57</option><option label="58" value="number:58">58</option><option label="59" value="number:59">59</option></select>
                                            <select class="form-control width-adjust hour-types" name="HourType" required="required"><option label="AM" value="string:AM">AM</option><option label="PM" value="string:PM">PM</option></select>
                                        </div> -->
                                    <span id="error-timmer" class="error"></span>
                                </div>



                            </div>
                            <!-- <div class="form-group row">
                                <label class="col-md-3 control-label"></label>
                                <div class="col-md-6">
                                    <label>{{scheduledFormTimmer}}</label>
                                </div>
                            </div> -->
                            <div class="form-group row" [hidden]='!selectedOptions.repeat && selectedOptions.repeatOption ==""'>
                                <div class="col-md-3">
                                    <label class="control-label">End Time </label>
                                </div>
                                <div class="col-md-4">
                                    <div class='input-group date' id='schedule-enddate-picker1'>
                                        <input readonly="readonly" type='text' id="formpick" placeholder="Pick an end date" class="form-control" />
                                        <span class="input-group-addon">
                                            <span class="fa fa-calendar"></span>
                                        </span>
                                    </div>
                                    <span id="error-start" class="error"></span>
                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-md-3 control-label">Message</label>
                                <div class="col-md-6">
                                    <textarea class="form-control" id="msg" [(ngModel)]="selectedOptions.message" rows="3" placeholder="Type your message here"></textarea>
                                    <span id="error-message" class="error"></span>
                                </div>
                            </div>
                        </div>
                        <div class="form-actions">
                            <button type="button" [disabled]="scheduleSending" id="submit-schedule" (click)="sendScheduledForms()" class="btn btn-primary swal-btn-info">{{scheduleButtonName}}</button>
                            <button type="button" (click)="addScheduleFormHideShow(isActive)" class="btn btn-default" id="cancel_btn" >Cancel</button> </div>
                        <!-- </form> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    </div>
<!-- </section> -->
<!-- <section class="card"> -->
    <div class="card-header">
            <span class="cat__core__title">
                <strong>Routine Form Schedule</strong>
            </span>
            <div [ngClass]="{'tagmsgs':!isActive , 'tagmsgs-edit': isActive}" [hidden]="!hideSiteSelection">
                <div class="col-sm-11">
                    <span style="width: 76%" class="tagfilter">
                        <span class="site-label user-sites">{{ filterSiteLabel | translate }} </span>
                        <app-select-sites [events]="eventsSubject.asObservable()" [filterType]=true (siteIds)="getSiteIds($event)" (hideDropdown)="hideDropdown($event)">
                        </app-select-sites>
                    </span>
            </div>
            </div>
                <a *ngIf='!isActive' class="pull-right btn btn-sm btn-primary" (click)="addScheduleFormHideShow(isActive)"
                id="schdle_frm">Schedule Form<i class="ml-1"></i></a>
      </div>

    <div class="card-block">
        <ol class="breadcrumb" *ngIf='!isActive'>
            <li class="breadcrumb-item" id="home_lnk"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item" id="sched_frms">Scheduled Forms</li>
        </ol>
        <div class="wrapper-content ">
            <div class="row">
                <div class="col-lg-12">
                    <div class="mb-5">
                        <div class="wait-loading" *ngIf="dataLoadingMsg">
                            <img src="./assets/img/loader/loading.gif" />
                        </div>
                        <div class="refresh-button pull-right" [hidden]="!refreshDatatableButton"><button class="btn btn-sm btn-primary" id="refreshDatatableButton" (click)="refreshScheduleFormTable()">Refresh Table</button></div>
                        <table class="table table-hover" id="staff-list" width="100%"></table>
                        <div class="col-md-12 text-center" *ngIf="!dataLoadingMsg && searchValue == '' && scheduledFormsList.length==0">{{ 'MESSAGES.NO_DATA_AVAILABLE' | translate }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
