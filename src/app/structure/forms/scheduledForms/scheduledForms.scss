.width-adjust-ms {
    flex: 0 0 10.333333% !important;
    max-width: 10.333333% !important;
}

.row.clear {
    padding-top: 18px;
}

label.schedule-time-label {
    position: relative;
    top: 10px;
}

.error {
    color: red;
}

.time-section label {
    min-height: 15px;
    margin-bottom: 10px;
}

.every-row select,
.every-row label {
    max-width: 100px;
    float: left;
    margin-right: 10px;
    margin-bottom: 5px;
}

.every-row label.first-every {
    max-width: 60px;
}    
.refresh-button {
    position: relative;
    z-index: 9999999999999;
}
.btn-search {
height: 38px;
width: 100%;
border-radius: 10px;
}
.tagmsgs
{
   position: absolute;
   width: 50%;
   margin-left: 376px;
}
.tagmsgs-edit {
    position: absolute;
    width: 50%;
    margin-left: 510px;
}
.tagfilter{
	display: flex;
	width: 100% !important;
	margin-top: -36px !important;
}
.tagfilter .site-label, .tagfilter app-select-sites{
	flex-basis: 100%;
}
 .tagfilter app-select-sites{
     margin-left: -207px;
 }