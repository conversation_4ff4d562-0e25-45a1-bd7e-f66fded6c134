<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>{{title}}</strong>
        </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">{{'TITLES.HOME' | translate}}</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/forms/form-tags']">{{'TITLES.FORM_TYPES' |
                    translate}}</a></li>
            <li class="breadcrumb-item">{{title}}</li>
        </ol>
        <div class="nav-tabs-horizontal">
            <ul class="nav nav-tabs mb-4 mt-2" role="tablist">
                <li class="nav-item">
                    <a class="nav-link" data-target="#tabGeneral" data-toggle="tab" href="javascript: void(0);"
                        role="tab" [ngClass]="{'active': currentTab === 'tabGeneral'}" aria-expanded="true"
                        (click)="showDataTabs('tabGeneral')"><i class="fa fa-cog"></i>
                        {{'MENU.FORM_TYPES_TABS_GENERAL' | translate}}</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" data-target="#tabIntegration" data-toggle="tab" href="javascript: void(0);"
                        role="tab" [ngClass]="{'active': currentTab === 'tabIntegration'}" aria-expanded="false"
                        (click)="showDataTabs('tabIntegration')"><i class="fa fa-wrench"></i>
                        {{'MENU.FORM_TYPES_TABS_INTEGRATION' | translate}} </a>
                </li>
            </ul>
        </div>
        <form [formGroup]="tagForm" id="tagForm" (ngSubmit)="updateTag(f)" #f="ngForm">
            <div class="tab-content">
                <div class="mb-5 tab-pane" id="tabGeneral" [ngClass]="{'active': currentTab === 'tabGeneral'}"
                    role="tabcard" aria-expanded="true">
                    <div class="form-body">
                        <div class="form-group row">
                            <label class="col-md-3 control-label">{{'LABELS.FORM_TYPE_NAME' | translate}} *</label>
                            <div class="col-md-9">
                                <input type="text" class="form-control" formControlName="formTagName"
                                    placeholder="{{'PLACEHOLDERS.FORM_TYPE_NAME' | translate}}" />
                                <div *ngIf="tagForm.controls['formTagName'].hasError('required')&&(tagForm.controls.formTagName?.dirty ||tagForm.controls.formTagName?.touched || f.submitted)"
                                    class="alert alert-danger">
                                    {{'VALIDATION_MESSAGES.FORM_TYPE_NAME_EMPTY' | translate}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 control-label">{{'LABELS.VISIBLE_TO_ROLES' | translate}} <i class="visible-role icmn-info" 
                                    data-original-title="" title=""></i></label>
                            <div class="col-md-9">
                                <select class="form-control select2" formControlName="visibleToRoles"
                                    id="visibleToRoles" multiple>
                                    <option
                                        *ngFor="let teanntRole of teanntRoles | excludePatientCaregiver | excludeMasterRole : '1'"
                                        value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group row">
                            <label class="col-md-3 control-label">{{'LABELS.NOTIFY_ON_SUBMIT' | translate}} <i class="notify-on-submit icmn-info"
                                data-original-title="" title=""></i></label>
                            <div class="col-md-9">
                                <select class="form-control select2" formControlName="notifyOnSubmit"
                                    id="notifyOnSubmit" multiple>
                                    <option *ngFor="let teanntRole of teanntRoles | excludePatientCaregiver"
                                        value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="col-md-3">
                                <label class="control-label">{{'LABELS.WORKFLOW' | translate}} *</label>
                                <i class="user-type-tt icmn-info"></i>
                            </div>
                            <div class="col-md-9">
                                <select class="form-control" formcontrolname="allowRecipientRoles"
                                    id="allowRecipientRoles" (change)="chooseFacing()">
                                    <option value="">{{'OPTIONS.WORKFLOW.SELECT_WORKFLOW' | translate}}</option>
                                    <option [selected]="tagForm.controls['allowRecipientRoles'].value == 'true'"
                                        [value]="true">{{'OPTIONS.WORKFLOW.STAFF_PARTNER_FACING' | translate}}</option>
                                    <option [selected]="tagForm.controls['allowRecipientRoles'].value == 'false'"
                                        [value]="false">{{'OPTIONS.WORKFLOW.PATIENT_FACING'| translate}}</option>
                                    <option [selected]="tagForm.controls['allowRecipientRoles'].value == 'practitioner'"
                                        value="practitioner">{{'OPTIONS.WORKFLOW.STAFF_PRACTITIONER_FACING'| translate}}
                                    </option>
                                </select>
                                <div *ngIf="tagForm.controls['allowRecipientRoles'].hasError('required')&&(tagForm.controls.allowRecipientRoles?.dirty ||tagForm.controls.allowRecipientRoles?.touched || f.submitted)"
                                    class="alert alert-danger">
                                    {{'VALIDATION_MESSAGES.WORKFLOW_EMPTY' | translate}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group row"
                            [hidden]="allowRecipientRoles == 'true' || allowRecipientRoles == 'partner' ||  allowRecipientRoles == '' || allowRecipientRoles == 'referral' ">
                            <label class="col-md-3 control-label">{{'LABELS.RECIPIENT_ROLES' | translate}}*</label>
                            <div class="col-md-9">
                                <select class="form-control select2" formControlName="recipientRoles"
                                    id="recipientRoles" multiple>
                                    <option></option>
                                    <option
                                        *ngFor="let teanntRole of teanntRoles | excludeMasterRole : '2' | caregiverOrAlternate : caregiverOrAlternateArgs | patientRole:((allowRecipientRoles == 'practitioner')?false:true)"
                                        value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                </select>
                                <div *ngIf="tagForm.controls['recipientRoles'].hasError('required')&&(tagForm.controls.recipientRoles?.dirty ||tagForm.controls.recipientRoles?.touched || f.submitted)"
                                    class="alert alert-danger">
                                    {{'VALIDATION_MESSAGES.RECIPIENT_ROLES_EMPTY'| translate}}
                                </div>
                            </div>
                        </div>
                        <div class="form-group row"
                            [hidden]="allowRecipientRoles == 'true' || allowRecipientRoles == 'partner'||  allowRecipientRoles == ''  ">
                            <label class="col-md-3 control-label">{{'LABELS.ALLOW_STAFF_TO_FILL_BEFORE_SENDING' |
                                translate}}</label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="staff_fill" name="staff_fill"
                                            formControlName="stafffill" />
                                    </div>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': stafffillEnable}" (click)="stafffillChange(true)">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !stafffillEnable}" (click)="stafffillChange(false)">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>

                                </div>
                            </div>
                        </div>

                        <div class="form-group row" [hidden]="allowRecipientRoles == 'false' ">
                            <label class="col-md-3 control-label">{{'LABELS.PATIENT_ASSOCIATION' | translate}}
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="patient_Association" name="patientAssociation"
                                            formControlName="patientAssociation" />

                                    </div>

                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': patientEnable  }"
                                        (click)="patientAssociationChange(true)">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !patientEnable }"
                                        (click)="patientAssociationChange(false)">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>

                                </div>
                            </div>

                        </div>
                        <!-- maneesh -->
                        <div class="form-group row" [hidden]="true">
                            <label class="col-md-3 control-label">{{'LABELS.VALIDATE_FORM_BEFORE_SENDING' | translate}}

                                <i class="validate-form-data icmn-info"></i>
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="validate_form_data" name="validateformdata"
                                            formControlName="validateformdata" />

                                    </div>

                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': validateformdataEnable }"
                                        (click)="validateformdataChange(true)">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !validateformdataEnable }"
                                        (click)="validateformdataChange(false)">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>

                                </div>
                            </div>

                        </div>

                        <div class="form-group row">
                            <label class="col-md-3 control-label">{{'LABELS.PRE_POPULATE_PREVIOUS_SUBMISSION' | translate}}
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="populate_previous_submission"
                                            name="populatePreviousSubmission"
                                            formControlName="populatePreviousSubmission" />

                                    </div>

                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': populatePreviousSubmissionEnable }"
                                        (click)="populatePreviousSubmissionChange(true)">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !populatePreviousSubmissionEnable }"
                                        (click)="populatePreviousSubmissionChange(false)">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>

                                </div>
                            </div>

                        </div>
                        <!-- ================================================================================= -->
                        <div class="form-group row"
                            [hidden]="!(tagForm.controls['allowRecipientRoles'].value == 'true')  || !populatePreviousSubmissionEnable ">
                            <label class="col-md-3 control-label">{{'LABELS.SHOW_CONFIRM_ACTION_ON_PRE_POPULATE'| translate}}
                                <i class="user-type-tttt icmn-info"></i>
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="confirm_action_prefill" name="confirmActionPrefill"
                                            formControlName="confirmActionPrefill" />

                                    </div>

                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': confirmActionPrefillEnable }"
                                        (click)="confirmActionPrefillChange(true)">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !confirmActionPrefillEnable }"
                                        (click)="confirmActionPrefillChange(false)">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>

                                </div>
                            </div>

                        </div>
                        <div class="form-group row"
                            [hidden]="!populatePreviousSubmissionEnable  || !confirmActionPrefillEnable || !(tagForm.controls['allowRecipientRoles'].value == 'true')">
                            <label class="col-md-3 control-label">
                                {{'LABELS.CONFIRM_CHECK_IN_MULTI_PAGE_FORM_MOVING_ACROSS_PAGES' | translate}}
                                <i class="user-type-confirm icmn-info"></i>
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="enableConfirmLinkChecking"
                                            name="enableConfirmLinkChecking"
                                            formControlName="enableConfirmLinkChecking" />
                                    </div>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': enableConfirmLinkCheckingEnable }"
                                        (click)="enableConfirmLinkCheckingEnableChange(true)">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !enableConfirmLinkCheckingEnable }"
                                        (click)="enableConfirmLinkCheckingEnableChange(false)">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 control-label">
                                {{'LABELS.MANDATORY_FIELDS_CHECK_IN_MULTI_PAGE_FORM_MOVING_ACROSS_PAGES' | translate}}
                                <i class="user-type-medatory icmn-info"></i>
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="enableMendatoryFieldChecking"
                                            name="enableMendatoryFieldChecking"
                                            formControlName="enableMendatoryFieldChecking" />
                                    </div>

                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': enableMendatoryFieldCheckingEnable }"
                                        (click)="enableMendatoryFieldCheckingChange(true)">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !enableMendatoryFieldCheckingEnable }"
                                        (click)="enableMendatoryFieldCheckingChange(false)">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 control-label">
                                {{'LABELS.INCLUDE_TENANT_NAME_IN_THE_HEADER_FOOTER_OF_PDF' | translate}}
                                <i class="user-type-includeTenantNameInPDFHeader icmn-info"></i>
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="includeTenantNameInPDFHeader"
                                            name="includeTenantNameInPDFHeader"
                                            formControlName="includeTenantNameInPDFHeader" />
                                    </div>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': includeTenantNameInPDFHeaderEnable }"
                                        (click)="includeTenantNameInPDFHeaderChange(true)">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !includeTenantNameInPDFHeaderEnable }"
                                        (click)="includeTenantNameInPDFHeaderChange(false)">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <!-- Configuration -- Include in the Form Types FHIR list API -->
                        <div class="form-group row" [hidden]="userData.config.documet_exchange_mode == 'fc'">
                            <label class="col-md-3 control-label">{{'LABELS.INCLUDE_IN_THE_FORM_TYPES_FHIR_LIST_API' |
                                translate }}
                                <i class="user-type-include_in_the_form_types_fhir_list_api icmn-info"></i>
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="includeFormTypesFHIRList"
                                            name="includeFormTypesFHIRList"
                                            formControlName="includeFormTypesFHIRList" />
                                    </div>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': includeFormTypesFHIRListEnable }"
                                        (click)="includeFormTypesFHIRListChange(true)">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !includeFormTypesFHIRListEnable }"
                                        (click)="includeFormTypesFHIRListChange(false)">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <!-- For page break-->
                        <div class="form-group row">
                            <label class="col-md-3 control-label">{{'LABELS.INCLUDE_PAGE_BREAK_IN_PDF/TIFF' | translate }}
                                <i class="user-type-includePageBreakInPDF icmn-info"></i>
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="includePageBreakInPDF" name="includePageBreakInPDF"
                                            formControlName="includePageBreakInPDF" />
                                    </div>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': includePageBreakInPDFEnable }"
                                        (click)="includePageBreakInPDFChange(true)">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !includePageBreakInPDFEnable }"
                                        (click)="includePageBreakInPDFChange(false)">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row"
                            [hidden]="!userData.config.enable_form_save_draft_patient || userData.config.enable_form_save_draft_patient == '0' || tagForm.controls['allowRecipientRoles'].value == 'true' || tagForm.controls['allowRecipientRoles'].value == ''">
                            <label class="col-md-3 control-label">{{'LABELS.ENABLE_SAVE_DRAFT_FORM_FOR_RECIPIENT'|
                                translate }}
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="enable_save_draft_patient"
                                            name="enableSaveDraftPatient" formControlName="enableSaveDraftPatient" />
                                    </div>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': enableSaveDraftPatientEnable}"
                                        (click)="enableSaveDraftPatientChange(true)">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !enableSaveDraftPatientEnable}"
                                        (click)="enableSaveDraftPatientChange(false)">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row"
                            [hidden]="!userData.config.enable_form_save_draft_staff || userData.config.enable_form_save_draft_staff == '0' || tagForm.controls['allowRecipientRoles'].value == ''">
                            <label class="col-md-3 control-label">{{'LABELS.SAVE_DRAFT_FORM_FOR_SENDER' | translate}}
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="enable_save_draft_staff" name="enableSaveDraftStaff"
                                            formControlName="enableSaveDraftStaff" />
                                    </div>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': enableSaveDraftStaffEnable}"
                                        (click)="enableSaveDraftStaffChange(true)">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !enableSaveDraftStaffEnable}"
                                        (click)="enableSaveDraftStaffChange(false)">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row" [hidden]="userData.config.esignature_audit_log!=1">
                            <label class="col-md-3 control-label">{{ 'LABELS.ENABLE_ESIGN_AUDIT' | translate }}
                                <i class="icmn-info form-type-enable-esignature-audit"></i>
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="esignature_audit_log" name="eSignature"
                                            formControlName="eSignature" />
                                    </div>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': tagForm.value.eSignature}"
                                        (click)="tagForm.value.eSignature=true">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !tagForm.value.eSignature}"
                                        (click)="tagForm.value.eSignature=false">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row" [hidden]="!showSendCompletedForm">
                            <label class="col-md-3 control-label">{{'LABELS.ENABLE_SENDING_COPY_OF_COMPLETED_FORM' |
                                translate}}
                                <i class="icmn-info enable-copy-completed-form"></i>
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="sendCompletedForm" name="sendCompletedForm"
                                            formControlName="sendCompletedForm" />
                                    </div>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': tagForm.value.sendCompletedForm}"
                                        (click)="tagForm.value.sendCompletedForm=true">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !tagForm.value.sendCompletedForm}"
                                        (click)="tagForm.value.sendCompletedForm=false">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row" [hidden]="!tagForm.controls['allowRecipientRoles'].value || tagForm.controls['allowRecipientRoles'].value === 'true'">
                            <label class="col-md-3 control-label">{{'LABELS.ENABLE_CONFIRMATION_POPUP_ON_FORM_SEND' |
                                translate}}
                                <i class="icmn-info" chToolTip="ENABLE_CONFIRMATION_POPUP_ON_FORM_SEND"></i>
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="enableConfirmationPopupOnSend" name="enableConfirmationPopupOnSend"
                                            formControlName="enableConfirmationPopupOnSend" />
                                    </div>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': tagForm.value.enableConfirmationPopupOnSend}"
                                        (click)="tagForm.value.enableConfirmationPopupOnSend=true">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !tagForm.value.enableConfirmationPopupOnSend}"
                                        (click)="tagForm.value.enableConfirmationPopupOnSend=false">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row" [hidden]="!tagForm.controls['allowRecipientRoles'].value">
                            <label class="col-md-3 control-label">{{'LABELS.ENABLE_CONFIRMATION_POPUP_ON_FORM_SUBMIT' |
                                translate}}
                                <i class="icmn-info" chToolTip="ENABLE_CONFIRMATION_POPUP_ON_FORM_SUBMIT"></i>
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="enableConfirmationPopupOnSubmit" name="enableConfirmationPopupOnSubmit"
                                            formControlName="enableConfirmationPopupOnSubmit" />
                                    </div>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': tagForm.value.enableConfirmationPopupOnSubmit}"
                                        (click)="tagForm.value.enableConfirmationPopupOnSubmit=true">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !tagForm.value.enableConfirmationPopupOnSubmit}"
                                        (click)="tagForm.value.enableConfirmationPopupOnSubmit=false">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mb-5 tab-pane" id="tabIntegration" [ngClass]="{'active': currentTab === 'tabIntegration'}"
                    role="tabcard" aria-expanded="true">
                    <div class="form-group row" [hidden]="userData.config.enable_disclose_PHI == 0 ||
            userData.config.progress_note_integration_mode === 'webhook' ||
            (!patientEnable && tagForm.controls['allowRecipientRoles'].value == 'true')">
                        <label class="col-md-3 control-label">{{'LABELS.ENABLE_DISCLOSE_PHI_WITH_EXTERNAL_SYSTEMS' |
                            translate}}
                            <i class="enable-disclose-phi icmn-info" data-original-title="" title=""></i> </label>

                        <div class="col-md-9">
                            <div class="btn-group col-md-2">
                                <div class="ng-hide checkbox checkbox-primary">
                                    <input type="hidden" id="disclose_phi_external_systems"
                                        name="externalFileDisclosePHI" formControlName="externalFileDisclosePHI" />
                                </div>

                                <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                    [ngClass]="{ active: externalFileDisclosePHIEnable }"
                                    (click)="externalFileDisclosePHIChange(true)">
                                    {{ 'BUTTONS.YES' | translate }}
                                </button>
                                <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                    [ngClass]="{ active: !externalFileDisclosePHIEnable }"
                                    (click)="externalFileDisclosePHIChange(false)">
                                    {{ 'BUTTONS.NO' | translate }}
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="form-group row"
                        [hidden]="userData.config.enable_disclose_PHI == 0 ||
      userData.config.progress_note_integration_mode === 'webhook' ||
      (!patientEnable && tagForm.controls['allowRecipientRoles'].value == 'true') || !externalFileDisclosePHIEnable || !multiSiteEnable ">
                        <label class="col-md-3 control-label">{{'LABELS.TRIGGER_ON' | translate}}</label>
                        <div class="col-md-3">
                            <select class="form-control" id="externalFileDisclosePHIEnableTrigger"
                                formcontrolname="externalFileDisclosePHITriggerOn">
                                <option *ngFor="let opt of TriggerOnArchiveOrSubmit"
                                    [selected]="externalFileDisclosePHITriggerOn == opt.value " [value]="opt.value">
                                    {{opt.key}}</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group row"
                        [hidden]=" userData.config.progress_note_integration_mode === 'webhook' || isDirectLink!=  true">
                        <label class="col-md-3 control-label">{{'LABELS.ENABLE_DIRECT_LINKING_TO_PATIENT_CHART' | translate}}
                            <i class="direct-linking icmn-info" data-original-title="" title=""></i> </label>
                        <div class="col-md-9">
                            <div class="btn-group col-md-2">
                                <div class="ng-hide checkbox checkbox-primary">
                                    <input type="hidden" id="direct_link_patient_chart" name="directlinkpatientchart"
                                        formControlName="directlinkpatientchart" />


                                </div>

                                <button [disabled]="(SelDirctlink && multiSiteEnable)" type="button" aria-pressed="true"
                                    class="btn btn-outline-success btn-sm"
                                    [ngClass]="{'active':directlinkpatientchartEnable}"
                                    (click)="directlinkpatientchartChange(true)">
                                    {{ 'BUTTONS.YES' | translate }}
                                </button>
                                <button [disabled]="(SelDirctlink && multiSiteEnable)" type="button" aria-pressed="true"
                                    class="btn btn-outline-default btn-sm"
                                    [ngClass]="{'active': !directlinkpatientchartEnable}"
                                    (click)="directlinkpatientchartChange(false)">
                                    {{ 'BUTTONS.NO' | translate }}
                                </button>

                            </div>
                        </div>


                    </div>

                    <div class="form-group row"
                        [hidden]=" userData.config.progress_note_integration_mode === 'webhook' || isDirectLink!=  true || !directlinkpatientchartEnable || !multiSiteEnable">
                        <label class="col-md-3 control-label">{{'LABELS.TRIGGER_ON' | translate}}</label>
                        <div class="col-md-3">
                            <select class="form-control" id="patientChartTrigger"
                                formcontrolname="directlinkpatientchartTriggerOn">
                                <option *ngFor="let opt of TriggerOnArchiveOrSubmit"
                                    [selected]=" directlinkpatientchartTriggerOn == opt.value" [value]="opt.value">
                                    {{opt.key}} </option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group row"
                        [hidden]="userData.config.progress_note_integration_mode === 'webhook' || userData.config.enable_sftp_integration == 0">
                        <label class="col-md-3 control-label">{{'LABELS.ENABLE_EXTERNAL_FILE_EXCHANGE_WITH_FC' |
                            translate}}
                            <!-- <i chToolTip="MSGES00001"></i> -->
                            <i class="external-file-exchange icmn-info" data-original-title="" title=""></i>
                        </label>
                        <div class="col-md-9">
                            <div class="btn-group col-md-2">
                                <div class="ng-hide checkbox checkbox-primary">
                                    <input type="hidden" id="external_file_exchange" name="externalFileExchange"
                                        formControlName="externalFileExchange" />


                                </div>

                                <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                    [ngClass]="{'active': externalFileExchangeEnable}"
                                    (click)="externalFileExchangeChange(true)">
                                    {{ 'BUTTONS.YES' | translate }}
                                </button>
                                <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                    [ngClass]="{'active': !externalFileExchangeEnable}"
                                    (click)="externalFileExchangeChange(false)">
                                    {{ 'BUTTONS.NO' | translate }}
                                </button>

                            </div>
                        </div>


                    </div>
                    <div class="form-group row padding-top-ten" [hidden]="!userData.config.enable_progress_note_integration ||
                  userData.config.enable_progress_note_integration == '0' ||
                  (!patientEnable && tagForm.controls['allowRecipientRoles'].value == 'true') 
                  ">
                        <label class="col-md-3 control-label">{{'LABELS.PROGRESS_NOTE_INTEGRATION' | translate}}
                            <i class="progress-note-integration icmn-info" data-original-title="" title=""></i>

                        </label>
                        <div class="col-md-9">
                            <div class="btn-group col-md-2">
                                <div class="ng-hide checkbox checkbox-primary">
                                    <input type="hidden" id="progress_note_integration" name="progressNoteIntegration"
                                        formControlName="progressNoteIntegration" />
                                </div>

                                <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                    [ngClass]="{ active: progressNoteIntegrationEnable }"
                                    (click)="progressNoteIntegrationChange(true)">
                                    {{ 'BUTTONS.YES' | translate }}
                                </button>
                                <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                    [ngClass]="{ active: !progressNoteIntegrationEnable }"
                                    (click)="progressNoteIntegrationChange(false)">
                                    {{ 'BUTTONS.NO' | translate }}
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="form-group row"
                        [hidden]="!progressNoteIntegrationEnable || !multiSiteEnable ||(!patientEnable && tagForm.controls['allowRecipientRoles'].value == 'true')  ">
                        <label class="col-md-3 control-label">{{'LABELS.TRIGGER_ON' | translate }}</label>
                        <div class="col-md-3">
                            <select class="form-control" id="progressNoteIntegrationId"
                                formcontrolname="progressNoteIntegrationTriggerOn">
                                <option *ngFor="let opt of TriggerOnProgressNoteIntegration"
                                    [selected]="progressNoteIntegrationTriggerOn == opt.value" [value]="opt.value">
                                    {{opt.key}}</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group row" [hidden]="(!userData.config.enable_external_cateogry_code_in_form_types ||
            userData.config.enable_external_cateogry_code_in_form_types == '0') || (!userData.config.enable_progress_note_integration ||
            userData.config.enable_progress_note_integration == 0 ||
            !progressNoteIntegrationEnable)">
                        <label class="col-md-3 control-label">{{catLabel}}
                            <i class="external-note-category icmn-info" data-original-title="" title=""></i>
                        </label>
                        <div class="col-md-6">
                            <input type="text" class="form-control" name="externalNoteCategory"
                                formControlName="externalNoteCategory"
                                [readonly]="(isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType)"
                                placeholder="{{catLabel}}" />
                        </div>
                    </div>
                    <div class="form-group row" [hidden]="(!userData.config.enable_progress_note_integration ||
            userData.config.enable_progress_note_integration == 0 ||
            !progressNoteIntegrationEnable) || !isEnableExternalCategory || 
            (isEnableApiBasedIntegrationCategory && !isEnableExternalCategory && isEnableApiBasedIntegrationType) || (!isEnableApiBasedIntegrationCategory && !isEnableExternalCategory && isEnableApiBasedIntegrationType) || 
            (!isEnableApiBasedIntegrationCategory && !isEnableExternalCategory && !isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && !isEnableExternalCategory && !isEnableApiBasedIntegrationType) ||
            (isEnableApiBasedIntegrationCategory && isEnableExternalCategory && isEnableApiBasedIntegrationType)">
                        <label class="col-md-3 control-label">{{'LABELS.EXTERNAL_NOTE_CATEGORY_ID' | translate}}
                            <i class="external-note-category-id icmn-info" data-original-title="" title=""></i>
                        </label>
                        <div class="col-md-6">
                            <input type="text" class="form-control" name="externalNoteCategoryId"
                                formControlName="externalNoteCategoryId" placeholder="{{'LABELS.EXTERNAL_NOTE_CATEGORY_ID' | translate}}" />
                        </div>
                    </div>
                    <div class="form-group row" [hidden]="(!userData.config.enable_external_message_typeId_in_form_types ||
            userData.config.enable_external_message_typeId_in_form_types == '0') || 
            (!userData.config.enable_progress_note_integration ||
            userData.config.enable_progress_note_integration == 0 ||
            !progressNoteIntegrationEnable)">
                        <label class="col-md-3 control-label">{{typeLabel}}
                            <i class="external-note-type-id icmn-info" data-original-title="" title=""></i>
                        </label>
                        <div class="col-md-6">
                            <input type="text" class="form-control" name="externalNoteTypeId"
                                formControlName="externalNoteTypeId"
                                [readonly]="(isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType)"
                                placeholder="{{typeLabel}}" />
                        </div>
                        <div class="col-md-2 get-ext-data-class"
                            *ngIf="(isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType)">
                            <button type="button" class="handButton" (click)="getExtMessageData(1)"><i
                                    title="{{'TITLES.LOOKUP_NOTE_TYPES_FROM_EXTERNAL_SYSTEM' | translate}}"
                                    class="fa fa-search"></i></button>
                            <button type="button" class="handButton" (click)="clearExtMessageData()"><i
                                    class="fa fa-close" title="{{'TITLES.RESET_THE_VALUE_OF_EXTERNAL_NOTE_TYPE' | translate}}"></i></button>
                        </div>
                    </div>
                    <div class="form-group row" [hidden]="(!userData.config.enable_progress_note_integration ||
            userData.config.enable_progress_note_integration == 0 ||
            !progressNoteIntegrationEnable) || !isEnableMessageType || (isEnableApiBasedIntegrationCategory && !isEnableMessageType && isEnableApiBasedIntegrationType ) || 
            (!isEnableApiBasedIntegrationCategory && !isEnableMessageType && isEnableApiBasedIntegrationType ) || 
            (!isEnableApiBasedIntegrationCategory && !isEnableMessageType && !isEnableApiBasedIntegrationType) || 
            (isEnableApiBasedIntegrationCategory && !isEnableMessageType && !isEnableApiBasedIntegrationType) || 
            (isEnableApiBasedIntegrationCategory && isEnableMessageType && isEnableApiBasedIntegrationType )">
                        <label class="col-md-3 control-label">{{'LABELS.EXTERNAL_NOTE_TYPE_ID' | translate}}
                            <i class="external-note-type-id-name icmn-info" data-original-title="" title=""></i>
                        </label>
                        <div class="col-md-6">
                            <input type="text" class="form-control"
                                placeholder="{{'LABELS.EXTERNAL_NOTE_TYPE_ID' | translate}}" name="externalNoteType"
                                formControlName="externalNoteType">
                        </div>
                    </div>
                    <div class="form-group row" [hidden]="!userData.config.enable_progress_note_integration ||
                  userData.config.enable_progress_note_integration == 0 ||
                  !progressNoteIntegrationEnable ||
                  tagForm.controls['allowRecipientRoles'].value == 'true'">
                        <label class="col-md-3 control-label">{{'LABELS.CREATE_PROGRESS_NOTE_WHEN_SENDING_FORM' |
                            translate}}

                        </label>
                        <div class="col-md-9">
                            <div class="btn-group col-md-2">
                                <div class="ng-hide checkbox checkbox-primary">
                                    <input type="hidden" id="progress_note_integration_send"
                                        name="progressNoteIntegrationSend"
                                        formControlName="progressNoteIntegrationSend" />
                                </div>

                                <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                    [ngClass]="{ active: progressNoteIntegrationSendEnable }"
                                    (click)="progressNoteIntegrationSendChange(true)">
                                    {{ 'BUTTONS.YES' | translate }}
                                </button>
                                <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                    [ngClass]="{ active: !progressNoteIntegrationSendEnable }"
                                    (click)="progressNoteIntegrationSendChange(false)">
                                    {{ 'BUTTONS.NO' | translate }}

                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="form-group row" [hidden]="!userData.config.enable_progress_note_integration ||
                  userData.config.enable_progress_note_integration == 0 ||
                  !progressNoteIntegrationEnable ||
                  tagForm.controls['allowRecipientRoles'].value == 'true'">
                        <label class="col-md-3 control-label">{{'LABELS.CREATE_PROGRESS_NOTE_WHEN_SENDING_FORM_REMINDER'
                            | translate}}

                        </label>
                        <div class="col-md-9">
                            <div class="btn-group col-md-2">
                                <div class="ng-hide checkbox checkbox-primary">
                                    <input type="hidden" id="progress_note_integration_reminder"
                                        name="progressNoteIntegrationReminder"
                                        formControlName="progressNoteIntegrationReminder" />
                                </div>

                                <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                    [ngClass]="{ active: progressNoteIntegrationReminderEnable }"
                                    (click)="progressNoteIntegrationReminderChange(true)">
                                    {{ 'BUTTONS.YES' | translate }}
                                </button>
                                <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                    [ngClass]="{ active: !progressNoteIntegrationReminderEnable }"
                                    (click)="progressNoteIntegrationReminderChange(false)">
                                    {{ 'BUTTONS.NO' | translate }}
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="form-group row" [hidden]="userData.config.progress_note_integration_mode != 'webhook' ">
                        <label
                            class="col-md-3 control-label">{{'LABELS.ENABLE_DOCUMENT_EXCHANGE_WITH_EXTERNAL_APPLICATIONS'
                            | translate}}
                            <i class="external-application icmn-info" data-original-title="" title=""></i>
                        </label>
                        <div class="col-md-9">
                            <div class="btn-group col-md-2">
                                <div class="ng-hide checkbox checkbox-primary">
                                    <input type="hidden" id="external_file_exchange" name="externalFileExchangeWebhook"
                                        formControlName="externalFileExchangeWebhook" />
                                </div>
                                <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                    [ngClass]="{'active': externalFileExchangeWebhookEnable}"
                                    (click)="externalFileExchangeWebhookChange(true)">
                                    {{ 'BUTTONS.YES' | translate }}
                                </button>
                                <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                    [ngClass]="{'active': !externalFileExchangeWebhookEnable}"
                                    (click)="externalFileExchangeWebhookChange(false)">
                                    {{ 'BUTTONS.NO' | translate }}
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="form-group row" [hidden]="(!userData.config.enable_esi_value_in_document_category ||
            userData.config.enable_esi_value_in_document_category == '0') || !externalFileExchangeWebhookEnable">
                        <label class="col-md-3 control-label">{{docLabel}}
                            <i class="external-document-type icmn-info" data-original-title="" title=""></i>
                        </label>
                        <div class="col-md-6">
                            <input type="text" class="form-control" name="externalDocumentType"
                                formControlName="externalDocumentType" [readonly]="isEnableApiBasedIntegration==true"
                                placeholder="{{docLabel}}" />
                        </div>
                        <div class="col-md-2 get-ext-data-class" *ngIf="isEnableApiBasedIntegration==true">
                            <button type="button" class="handButton" (click)="getExtDocData(1)"><i
                                    title="{{'TITLES.LOOKUP_DOCUMENT_TYPES_FROM_EXTERNAL_SYSTEM' | translate}}"
                                    class="fa fa-search"></i></button>
                            <button type="button" class="handButton" (click)="clearExtDocData()"><i class="fa fa-close"
                                    title="{{'TITLES.RESET_THE_VALUE_OF_EXTERNAL_DOCUMENT_TYPE' | translate}}"></i></button>
                        </div>

                    </div>
                    <div class="form-group row"
                        [hidden]="(!userData.config.enable_esi_value_in_document_category ||
    userData.config.enable_esi_value_in_document_category == '0') || !externalFileExchangeWebhookEnable  || isEnableApiBasedIntegration">
                        <label class="col-md-3 control-label">{{'LABELS.EXTERNAL_DOCUMENT_TYPE_ID' | translate}}
                            <i class="external-document-type-id icmn-info" data-original-title="" title=""></i>
                        </label>
                        <div class="col-md-6">
                            <input type="text" class="form-control" name="externalDocumentTypeId"
                                formControlName="externalDocumentTypeId"
                                placeholder="{{'LABELS.EXTERNAL_DOCUMENT_TYPE_ID' | translate}}" />
                        </div>
                    </div>


                    <div class="form-group row"
                        [hidden]=" FCselected !== true && directlinkpatientchartEnable !==  true && progressNoteIntegrationEnable !==  true && userData.config.enable_exchange_of_discrete_data == 0 &&   enableThirdPartyDiv || !(userData.config.enable_exchange_of_discrete_data)">
                        <label class="col-md-3 control-label">{{'LABELS.ENABLE_SENDING_FORM_DATA_TO_THIRD_PARTY' |
                            translate}}
                            <!-- <i chToolTip="MSGES00001"></i> -->
                            <i class="send-form-data-json icmn-info" data-original-title="" title=""></i> </label>
                        <div class="col-md-9">
                            <div class="btn-group col-md-2">
                                <div class="ng-hide checkbox checkbox-primary">
                                    <input type="hidden" id="send_form_data_json" name="sendformdatajson"
                                        formControlName="sendformdatajson" />


                                </div>

                                <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                    [ngClass]="{'active':sendformdatajsonEnable}"
                                    (click)="sendformdatajsonChange(true)">
                                    {{ 'BUTTONS.YES' | translate }}
                                </button>
                                <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                    [ngClass]="{'active': !sendformdatajsonEnable}"
                                    (click)="sendformdatajsonChange(false)">
                                    {{ 'BUTTONS.NO' | translate }}
                                </button>

                            </div>
                        </div>


                    </div>

                    <div class="form-group row"
                        [hidden]="userData.config.progress_note_integration_mode === 'webhook' ||!sendformdatajsonEnable || !multiSiteEnable">
                        <label class="col-md-3 control-label">{{'LABELS.TRIGGER_ON' | translate }}</label>
                        <div class="col-md-3">
                            <select class="form-control" id="formDataThirdpartyTriggerId"
                                formcontrolname="sendformdatajsonTriggerOn">
                                <option *ngFor="let opt of TriggerOnArchiveOrSubmit"
                                    [selected]="sendformdatajsonTriggerOn == opt.value" [value]="opt.value">{{opt.key}}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group row"
                        [hidden]=" userData.config.progress_note_integration_mode === 'webhook' || !isFilingCenter  || !multiSiteEnable">
                        <label class="col-md-3 control-label ">{{ 'LABELS.ENABLE_FAXQUEUE' | translate }}
                            <i class="enable-faxqueue icmn-info" data-original-title="" title=""></i> </label>
                        <div class="col-md-9">
                            <div class="btn-group col-md-2">
                                <div class="ng-hide checkbox checkbox-primary">
                                    <input type="hidden" id="faxQ" name="FaxQueue" formControlName="FaxQueue" />
                                </div>
                                <button [disabled]="(SelFaxq && multiSiteEnable)" type="button" aria-pressed="true"
                                    class="btn btn-outline-success btn-sm" [ngClass]="{'active':faxQEnable}"
                                    (click)="faxQChange(true)">
                                    {{ 'BUTTONS.YES' | translate }}
                                </button>
                                <button [disabled]="(SelFaxq && multiSiteEnable)" type="button" aria-pressed="true"
                                    class="btn btn-outline-default btn-sm" [ngClass]="{'active': !faxQEnable}"
                                    (click)="faxQChange(false)">
                                    {{ 'BUTTONS.NO' | translate }}
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="form-group row"
                        [hidden]="userData.config.progress_note_integration_mode === 'webhook' || !faxQEnable || !multiSiteEnable ">
                        <label class="col-md-3 control-label">{{'LABELS.TRIGGER_ON' | translate }}</label>
                        <div class="col-md-3">

                            <select class="form-control" id="faxQTriggerId" formcontrolname="faxQIntegrationTriggerOn">
                                <option *ngFor="let opt of TriggerOnArchiveOrSubmit"
                                    [selected]="faxQIntegrationTriggerOn == opt.value" [value]="opt.value">{{opt.key}}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div [hidden]="hideExternalAppltnDiv">

                        <div class="form-group row"
                            [hidden]="( !externalFileExchangeWebhookEnable && !externalFileDisclosePHIEnable ) ">
                            <label class="col-md-3 control-label">{{'LABELS.TRIGGER_ON_SUBMIT' | translate}}
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="external_file_exchange"
                                            name="externalFileExchangeOnSubmit"
                                            formControlName="externalFileExchangeOnSubmit" />
                                    </div>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': externalFileExchangeOnSubmitEnable}"
                                        (click)="externalFileExchangeOnSubmitChange(true)">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !externalFileExchangeOnSubmitEnable}"
                                        (click)="externalFileExchangeOnSubmitChange(false)">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row"
                            [hidden]="( !externalFileExchangeWebhookEnable && !externalFileDisclosePHIEnable) ">
                            <label class="col-md-3 control-label">{{'LABELS.TRIGGER_ON_ARCHIVE' | translate}}
                            </label>
                            <div class="col-md-9">
                                <div class="btn-group col-md-2">
                                    <div class="ng-hide checkbox checkbox-primary">
                                        <input type="hidden" id="external_file_exchange"
                                            name="externalFileExchangeOnArchive"
                                            formControlName="externalFileExchangeOnArchive" />
                                    </div>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': externalFileExchangeOnArchiveEnable}"
                                        (click)="externalFileExchangeOnArchiveChange(true)">
                                        {{ 'BUTTONS.YES' | translate }}
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !externalFileExchangeOnArchiveEnable}"
                                        (click)="externalFileExchangeOnArchiveChange(false)">
                                        {{ 'BUTTONS.NO' | translate }}
                                    </button>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div [hidden]="userData.config.progress_note_integration_mode === 'webhook' ">
                        <div class="form-group row"
                            [hidden]="!userData.config.enable_progress_note_integration ||
            userData.config.enable_progress_note_integration == 0 ||
            !progressNoteIntegrationEnable ||
            ((!progressNoteIntegrationSendEnable || !progressNoteIntegrationEnable) && (!progressNoteIntegrationReminderEnable ||!progressNoteIntegrationEnable)) || 
            allowRecipientRoles == 'true' ||
            allowRecipientRoles == 'partner' || 
            (allowRecipientRoles == '' && (!progressNoteIntegrationSendEnable && !progressNoteIntegrationReminderEnable))  ||  multiSiteEnable">
                            <label
                                class="col-md-3 control-label">{{'LABELS.OUTGOING_FILING_CENTER_FOR_FORM_SEND_REMINDER'
                                | translate}}
                            </label>
                            <div class="col-md-3">
                                <input id="sfilingCenterssendreminder" value="" />
                                <i title="{{'TITLES.CLEAR' | translate}}" *ngIf="showClossendreminder"
                                    (click)="clearFilingCenter('sendreminder')"
                                    class="icmn-cross close-file-center"></i>
                            </div>
                        </div>
                        <div class="form-group row" [hidden]="!isFilingCenter ||  multiSiteEnable">
                            <label class="col-md-3 control-label">{{'LABELS.OUTGOING_FILING_CENTER_AFTER_FORM_SUBMIT' |
                                translate}}</label>
                            <div class="col-md-3">
                                <input id="sfilingCenterssubmit" value="" />
                                <i title="{{'TITLES.CLEAR' | translate}}" *ngIf="showCloseSubmits"
                                    (click)="clearFilingCenter('submits')" class="icmn-cross close-file-center"></i>

                            </div>
                        </div>
                        <div class="form-group row" [hidden]="!isFilingCenter ||  multiSiteEnable">
                            <label class="col-md-3 control-label">{{'LABELS.OUTGOING_FILING_CENTER_AFTER_FORM_ARCHIVE' |
                                translate}}</label>
                            <div class="col-md-3">
                                <input id="sfilingCenterss" value="" />
                                <i title="{{'TITLES.CLEAR' | translate}}" *ngIf="showClose"
                                    (click)="clearFilingCenter()" class="icmn-cross close-file-center"></i>
                            </div>
                        </div>
                        <div class="form-group row"
                            [hidden]="( progressNoteIntegrationEnable==  true || sendformdatajsonEnable!= true)  ||  multiSiteEnable">
                            <label
                                class="col-md-3 control-label">{{'LABELS.OUTGOING_FILING_CENTER_FOR_FORM_DATA_EXCHANGE'
                                | translate}}</label>
                            <div class="col-md-3">
                                <input id="sfilingCenterssjsondataexchange" value="" />
                                <i title="{{'TITLES.CLEAR' | translate}}" *ngIf="showCloseexchangejson"
                                    (click)="clearFilingCenter('exchangejson')"
                                    class="icmn-cross close-file-center"></i>
                            </div>
                        </div>
                        <div class="form-group row"
                            [hidden]="(!userData.config.enable_progress_note_integration || userData.config.enable_progress_note_integration == 0) ||  ( !externalFileExchangeEnable && !progressNoteIntegrationEnable && !externalFileDisclosePHIEnable) ||  multiSiteEnable ">
                            <label
                                class="col-md-3 control-label">{{'LABELS.OUTGOING_FILING_CENTER_AFTER_FORM_SUBMIT(JSON)'|
                                translate}}
                            </label>
                            <div class="col-md-3">
                                <input id="sfilingCenterssubmitjson" value="" />
                                <i title="{{'TITLES.CLEAR' | translate}}" *ngIf="sfilingCenterssubmitjson"
                                    (click)="clearFilingCenter('submitsjson')" class="icmn-cross close-file-center"></i>
                            </div>
                        </div>
                        <div class="form-group row"
                            [hidden]="(!userData.config.enable_progress_note_integration || userData.config.enable_progress_note_integration == 0)||  ( !externalFileExchangeEnable && !progressNoteIntegrationEnable && !externalFileDisclosePHIEnable)||  multiSiteEnable ">
                            <label
                                class="col-md-3 control-label">{{'LABELS.OUTGOING_FILING_CENTER_AFTER_FORM_ARCHIVE(JSON)'
                                | translate}}</label>
                            <div class="col-md-3">
                                <input id="sfilingCenterssjson" value="" />
                                <i title="{{'TITLES.CLEAR' | translate}}" *ngIf="showClosearchivejson"
                                    (click)="clearFilingCenter('archivejson')" class="icmn-cross close-file-center"></i>
                            </div>
                        </div>
                    </div>
                    <div class="form-group row element-hide">
                        <div class="col-md-3">
                            <label class="control-label">{{'LABELS.ENROLLMENT' | translate}} <i
                                    class="enrolment icmn-info" data-original-title="" title=""></i></label>
                        </div>
                        <div class="col-md-3">
                            <div class="btn-group">

                                <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm"
                                    [ngClass]="{'active': tagForm.controls['isEnrolment'].value}"
                                    (click)="toggleMetaTag('isEnrolment',true)">
                                    {{ 'BUTTONS.YES' | translate }}
                                </button>
                                <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm"
                                    [ngClass]="{'active': !tagForm.controls['isEnrolment'].value}"
                                    (click)="toggleMetaTag('isEnrolment',false)">
                                    {{ 'BUTTONS.NO' | translate }}
                                </button>
                            </div>
                        </div>
                    </div>
                    <div
                        [hidden]="!userData.config.enable_progress_note_integration || userData.config.enable_progress_note_integration == '0' || tagForm.controls['allowRecipientRoles'].value == 'true'">
                        <div [hidden]="!progressNoteIntegrationEnable || !progressNoteIntegrationSendEnable">
                            <div class="card-header">
                                <span class="font-eighteen">
                                    <strong>{{'TITLES.FORM_SEND_PROGRESS_NOTE_TEMPLATE' | translate}}</strong>
                                </span>
                            </div>
                            <div class="form-body">
                                <div class="form-group row padding-top-twenty">
                                    <label class="col-md-1 control-label">{{'LABELS.SUBJECT' | translate}}</label>
                                    <div class="col-md-5">
                                        <input type="text" class="form-control" formControlName="subject"
                                            placeholder="{{'LABELS.SUBJECT' | translate}}" />
                                    </div>
                                </div>
                                <div class="form-group row padding-top-ten">
                                    <label class="col-md-1 control-label">{{'LABELS.BODY' | translate}}</label>
                                    <div class="col-md-5">
                                        <input type="text" class="form-control" formControlName="body"
                                            placeholder="{{'LABELS.BODY' | translate}}" />
                                    </div>
                                    <div class="col-md-6">
                                        <label>
                                            <input type="checkbox" id="attachMessage" name="attachMessage"
                                                (change)="attachMessageF($event.target.checked)"
                                                value="attachMessage.checked = attachMessage.checked;"
                                                [checked]="attachMessage">
                                            {{'LABELS.MESSAGE_SENT_PROGRESS_NOTE_BODY' | translate}}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div [hidden]="!progressNoteIntegrationEnable || !progressNoteIntegrationReminderEnable">
                            <div class="card-header">
                                <span class="font-eighteen">
                                    <strong>{{'TITLES.FORM_REMINDER_PROGRESS_NOTE_TEMPLATE' | translate}}</strong>
                                </span>
                            </div>
                            <div class="form-body">
                                <div class="form-group row padding-top-twenty">
                                    <label class="col-md-1 control-label">{{'LABELS.SUBJECT' | translate}}</label>
                                    <div class="col-md-5">
                                        <input type="text" class="form-control" formControlName="subjectOne"
                                            placeholder="{{'LABELS.SUBJECT' | translate}}" />
                                    </div>
                                </div>
                                <div class="form-group row padding-top-ten">
                                    <label class="col-md-1 control-label">{{'LABELS.BODY' | translate}}</label>
                                    <div class="col-md-5">
                                        <input type="text" class="form-control" formControlName="bodyOne"
                                            placeholder="{{'LABELS.BODY' | translate}}" />
                                    </div>
                                    <div class="col-md-6">
                                        <label>
                                            <input type="checkbox" id="attachMessageOne" name="attachMessageOne"
                                                (change)="attachMessageOneF($event.target.checked)"
                                                [checked]="attachMessageOne">
                                            {{'LABELS.MESSAGE_SENT_PROGRESS_NOTE_BODY' | translate }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-md-3 control-label ">{{ 'LABELS.DATE_FORMAT_FOR_DATE_OF_SERVICE' | translate
                            }}
                            <i chToolTip="dateFormatForDateOfService" data-animation="false" class="icmn-info"></i>
                        </label>
                        <div class="col-md-3">
                            <select class="form-control" formControlName="dosDateFormat">
                                <option *ngFor="let format of dateFormatOptions" [value]="format.value">
                                    {{format.option}}</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">{{btnLabel}}</button>
                <button type="button" (click)="cancel()" class="btn btn-default"> {{ 'BUTTONS.CANCEL' | translate
                    }}</button>
            </div>
        </form>
    </div>
</section>
<div class="modal bulk-edit" id="bulk-edit" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="exampleModalLabel">{{'LABELS.DOCUMENT_TYPE' | translate }}</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                    (click)="closeDynamicModal()" style="margin-right: 7px;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div *ngIf="showLoader" class="loading-container">
                    <div class="lds-roller">
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                    </div>
                    <div class="loading-text">{{'MESSAGES.LOADING_DATA' | translate }}</div>
                </div>
                <form>
                    <div *ngIf="!showLoader && (noteCount != 0)" class="card-block">
                        <div class="cat__core__card-sidebar">
                            <label><strong>{{'LABELS.DOCUMENT_CATEGORY' | translate}}</strong></label>
                            <div class="doc-cat-tab">
                                <select class="form-control doc-cat-class" (change)="onSelectCategory($event)"
                                    id="doc-category">
                                    <option value="">{{'OPTIONS.SELECT' | translate }}</option>
                                    <option *ngFor="let noteType of noteTypes;let i = index" value="{{noteType.id}}">
                                        {{noteType.value}} </option>
                                </select>
                                <button type="button" (click)="getExtDocData(0)"><i
                                        title="{{'TITLES.REFRESH_DATA_FROM_EXTERNAL_SYSTEM' | translate}}"
                                        class="fa fa-refresh get-ext-data-class"></i></button>

                            </div>
                            <div class="" *ngIf="noteTypesCount != 0">
                                <label><strong>{{'LABELS.DOCUMENT_TYPE' | translate }}</strong></label>
                                <select [disabled]="!enableDocType" class="form-control doc-type-class"
                                    (change)="onSelectType($event)" id="doc-type">
                                    <option value="">{{'OPTIONS.SELECT' | translate }}</option>
                                    <option *ngFor="let documentTypeSet of documentTypeSet;let i = index"
                                        value="{{documentTypeSet.id}}">
                                        {{documentTypeSet.name}}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
                <div style="text-align:center;width:100%;" *ngIf="!showLoader && noteTypesCount == 0">
                    <p>{{'MESSAGES.ISSUES_FROM_THIRD_PARTY_SERVICE' | translate }}</p>
                </div>
                <div style="text-align:center;width:100%;" *ngIf="!showLoader && noteTypesCount == 2">
                    <p>{{'MESSAGES.AUTHENTICATION_FAILED' | translate }} </p>
                </div>
                <div style="text-align:center;width:100%;" *ngIf="!showLoader && noteTypesCount == 1">
                    <p>{{'MESSAGES.INVALID_PARAMETERS_IN_PARTNER_TOKEN' | translate }}</p>
                </div>
                <div style="text-align:center;width:100%;" *ngIf="!showLoader && noteTypesCount == 3">
                    <p>{{'MESSAGES.INVALID_PARAMETERS_TO_THIRD_PARTY_APPLICATION' | translate }}</p>
                </div>
                <div style="text-align:center;width:100%;" *ngIf="!showLoader && noteTypesCount == 4">
                    <p>{{'MESSAGES.INVALID_CLIENT_ID_TO_THIRD_PARTY_APPLICATION' | translate }}</p>
                </div>
            </div>
            <div class="modal-footer">
                <div class="footer-padding footer-padding">
                    <button type="button" [disabled]="!enableSubData" class="btn btn-primary"
                        (click)="setSelectedData()">{{ 'BUTTONS.OK' | translate }}</button>
                    <button type="button" class="btn btn-primary" (click)="closeDynamicModal()">{{ 'BUTTONS.CLOSE' |
                        translate }}</button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal bulk-edit-new" id="bulk-edit-new" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="exampleModalLabel">{{'LABELS.EXTERNAL_NOTES_DETAILS' | translate }}</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"
                    (click)="closeDynamicMsgModal()" style="margin-right: 7px;">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div *ngIf="showLoader" class="loading-container">
                    <div class="lds-roller">
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                    </div>
                    <div class="loading-text">{{'MESSAGES.LOADING_DATA' | translate }}</div>
                </div>
                <form>
                    <div *ngIf="!showLoader && (noteCount != 0)" class="card-block">
                        <div class="cat__core__card-sidebar">
                            <label><strong>{{'LABELS.NOTE_CATEGORY' | translate }}</strong></label>
                            <div class="doc-cat-tab">
                                <select class="form-control doc-cat-class" (change)="onSelectMessage($event)"
                                    id="doc-category">
                                    <option value="">{{'OPTIONS.SELECT' | translate }}</option>
                                    <option *ngFor="let noteType of messageTypes;let i = index" value="{{noteType.id}}">
                                        {{noteType.value}} </option>
                                </select>
                                <button type="button" (click)="getExtMessageData(0)"><i
                                        title="{{'TITLES.REFRESH_DATA_FROM_EXTERNAL_SYSTEM' | translate}}"
                                        class="fa fa-refresh get-ext-data-class"></i></button>

                            </div>
                            <div class="" *ngIf="msgTagTypeCount != 0">
                                <label><strong>{{'LABELS.NOTE_TYPE' | translate }}</strong></label>
                                <select [disabled]="!enableDocType" class="form-control doc-type-class"
                                    (change)="onSelectNoteType($event)" id="doc-type">
                                    <option value="">{{'OPTIONS.SELECT' | translate }}</option>
                                    <option *ngFor="let msgTypeSet of msgTypeSet;let i = index"
                                        value="{{msgTypeSet.id}}">
                                        {{msgTypeSet.name}}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
                <div style="text-align:center;width:100%;"
                    *ngIf="!showLoader && (noteTypesCount == 0 || msgTagTypeCount == 0)">
                    <p>{{'MESSAGES.ISSUES_FROM_THIRD_PARTY_SERVICE' | translate }}</p>
                </div>
                <div style="text-align:center;width:100%;"
                    *ngIf="!showLoader && (noteTypesCount == 2 || msgTagTypeCount == 2)">
                    <p>{{'MESSAGES.AUTHENTICATION_FAILED' | translate }}</p>
                </div>
                <div style="text-align:center;width:100%;"
                    *ngIf="!showLoader && (noteTypesCount == 1 || msgTagTypeCount == 1)">
                    <p>{{'MESSAGES.INVALID_PARAMETERS_IN_PARTNER_TOKEN' | translate }}</p>
                </div>
                <div style="text-align:center;width:100%;"
                    *ngIf="!showLoader && (noteTypesCount == 3 || msgTagTypeCount == 3)">
                    <p>{{'MESSAGES.INVALID_PARAMETERS_TO_THIRD_PARTY_APPLICATION' | translate }}</p>
                </div>
                <div style="text-align:center;width:100%;"
                    *ngIf="!showLoader && (noteTypesCount == 4 || msgTagTypeCount == 4)">
                    <p>{{'MESSAGES.INVALID_CLIENT_ID_TO_THIRD_PARTY_APPLICATION' | translate }}</p>
                </div>
            </div>
            <div class="modal-footer">
                <div class="footer-padding">
                    <button type="button" [disabled]="!enableSubData" class="btn btn-primary"
                        (click)="setSelectedMsgData()">{{ 'BUTTONS.OK' | translate }}</button>
                    <button type="button" class="btn btn-primary" (click)="closeDynamicMsgModal()">{{ 'BUTTONS.CLOSE' |
                        translate }}</button>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- END: tables/datatables -->
