
<section class="card">
    <div class="card-block">          
        <div class="row request-tab">
<div class="col-md-12 from-to-btn-sec" style="margin-bottom: 14px;">
        <div class="nav-tabs-horizontal">
            <ul class="filter-sites-container nav nav-tabs filing-icons" role="tablist">
                <li class="nav-item list-top">
                    <a class="nav-link "  [routerLink]="['/forms/worklist']" href="javascript: void(0);" data-toggle="tab"  role="tab" aria-expanded="true">
                        <i class="fa icmn-pen" aria-hidden="true"></i>
                        <span class="cat__core__title">My Form Worklists</span></a>
                </li>
                <li class="nav-item list-top" [hidden]="(userDataN.group == constants.userGroupIds.partner || !previlages.viewFormEntries &&  (userDataConfig.enable_collaborate_edit != 1 || userDataN.group == constants.userGroupIds.patient || userDataN.nursing_agencies != '' || (!previlages.viewFormEntries  && !previlages.FillStructuredForms) || (userDataN.accessSecurityEnabled && userDataN.accessSecurityEsiValue && userDataN.accessSecurityEsiValue != '')))" >
                    <a class="nav-link active"  [routerLink]="['/forms/list']" href="javascript: void(0);" data-toggle="tab"  role="tab" aria-expanded="true">
                        <i class="fa fa-list-alt" aria-hidden="true"></i>
                        <span class="cat__core__title">All Form Worklists</span></a>
                </li>                       
                <div class="filter-sites-wrapper"  [hidden]="!hideSiteSelection">
                    <div class="col-sm-11" style="float: right">
                            <span style="width: 76%">
                                <span class="site-label user-sites">{{ labelSiteFilter | translate }} </span>
                                <app-select-sites [events]="eventsSubject.asObservable()" [filterType]=true (siteIds)="getSiteIds($event)" (hideDropdown)="hideDropdown($event)">
                                </app-select-sites>
                            </span>
                    </div>
                </div>
            </ul>
        </div>
    </div>
</div>    
</div>
    <div class="card-block">
        <div>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
                <li class="breadcrumb-item">Form Worklists</li>
                <li class="info-widget">
                    <span class="chatwith-modal-tip" *ngIf="!isAdvancedSearchView">
                                <img  src="./assets/modules/dummy-assets/common/img/chatwith-tip.png">                            
                                <span *ngIf="userDataConfig.enable_collaborate_edit == 1" class="modal-footer-text-sign">Click on a tab to see the forms entries under a category (Completed, Pending, Draft, Archived).</span>
                                <span *ngIf="userDataConfig.enable_collaborate_edit != 1" class="modal-footer-text-sign">Click on a tab to see the forms entries under a category (Completed, Pending, Archived).</span>

                    </span>
                </li>
            </ol>

        </div>

    <div class="row">
      <div class="col-md-3 search-filter" [hidden]="!isAdvancedSearchView">
        <angular2-multiselect
          [data]="advancedFilterOptions"
          [(ngModel)]="selectedAdvancedFilterItem"
          [settings]="advancedFilterOptionsSettings"
          (onSelect)="setSelectedAdvancedFilterItem($event)"
          (onDeSelect)="removeAdvancedFilterItem()"
          (onDeSelectAll)="clearFilter()"
          name="advancedFilterSavedOptions"
        ></angular2-multiselect>
      </div>
            <div class="advanced-search-btn" (click)="hideAdvanceSearchView()" *ngIf="isAdvancedSearchView">
                <span>{{'BUTTONS.BACK' | translate}}</span>
            </div>
            <div class="col-lg-12" [hidden]="isAdvancedSearchView">
                <div style="text-align:center;width:100%;position: absolute;top: 10px;" *ngIf="dataLoadingMsg1">
                    <img src="./assets/img/loader/loading.gif" />
                </div>
                <div class=" form-lists">
                        <div style="float:right;height:200px;">
                                <li class="ci-form-filter-dropdown" style="list-style: none; display: inline-flex;">
                                    <label for="">Filter : </label>                
                                    <select [value] = "selectedfilterval" id="scheduledSelect">
                                        <option value="all">All</option>
                                        <option value="yes">Scheduled Forms</option>
                                        <option value="no">Ad-hoc Forms</option>
                                    </select>
                                    <div class="advanced-search-btn" (click)="populateAdvancedSearchView()" *ngIf="!isAdvancedSearchView">
                                        <span>{{'BUTTONS.ADVANCED_SEARCH' | translate}}</span>
                                    </div>
                                </li>
                            </div>
                            
                        <div *ngIf="userDataConfig.enable_collaborate_edit == 1 && collaborate_edit_visible == 1" class="row actions-list " style="margin-bottom: 40px;margin-top: 53px;" [ngClass]="{'not-clickable':clickedTab != ''}">
                                <div class="col-sm-3 signedblock-border signature-req-block completed-collaborate" [ngClass]="{'status-active':isActive == 'completed'}">
                                    <a href="javascript:void(0)" [ngClass]="{'not-clickable-col':notclickablecol == 1}" (click)="changeFormData('COMPLETED','refresh')">
                                        <div class="row" style="height:100%">
                                            <div class="col-sm-3"></div>
                                            <div class="col-sm-6">
                                                <div class="cat__core__step pending-block">
                                                    <span class="cat__core__step__digit"><i class="fa fa-check completed"></i></span>
                                                    <div class="cat__core__step__desc">
                                                        <span class="cat__core__step__title" *ngIf="notclickablecol != 1" >{{completedCount}}</span>
                                                        <p>Completed</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-3"></div> 
                                        </div>
                                    </a>
                                </div>
                                <div class="col-sm-3 signedblock-border signature-req-block pending-collaborate" [ngClass]="{'status-active':isActive == 'pending'}">
                                    <a href="javascript:void(0)" [ngClass]="{'not-clickable-col':notclickablecol == 1}" (click)="changeFormData('PENDING','refresh')">
                                        <div class="row" style="height:100%">
                                            <div class="col-sm-3"></div>
                                            <div class="col-sm-6">
                                                <div class="cat__core__step cat__core no-border signed-block">
                                                    <span class="cat__core__step__digit"><i class="fa fa-clock-o waiting-others"></i></span>
                                                    <div class="cat__core__step__desc">
                                                        <span class="cat__core__step__title" *ngIf="notclickablecol != 1">{{pendingCount}}</span>
                                                        <p>Pending</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-3"></div>
                                        </div>
                                    </a>
                                </div>
                                <div class="col-sm-3 signedblock-border signature-req-block" [ngClass]="{'status-active':isActive == 'draft'}">
                                    <a href="javascript:void(0)" (click)="changeFormData('DRAFTS','refresh')">
                                        <div class="row" style="height:100%">
                                            <div class="col-sm-3"></div>
                                            <div class="col-sm-6">
                                                <div class="cat__core__step pending-block">
                                                    <span class="cat__core__step__digit requests-archive-icon">
                                                    <i><img src="./assets/img/draft.png"></i>
                                                </span>
                                                    <div class="cat__core__step__desc">
                                                        <span class="cat__core__step__title">{{draftCount}}</span>
                                                        <p >Drafts</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-3"></div>
                                        </div>
                                    </a>
                                 </div>
                                <div class="col-sm-3 signature-req-block archive-collaborate" [ngClass]="{'status-active':isActive == 'archived'}">
                                    <a href="javascript:void(0)" [ngClass]="{'not-clickable-col':notclickablecol == 1}" (click)="changeFormData('ARCHIVE','refresh')">
                                        <div class="row" style="height:100%">
                                            <div class="col-sm-3"></div>
                                            <div class="col-sm-6">
                                                <div class="cat__core__step pending-block">
                                                    <span class="cat__core__step__digit requests-archive-icon">
                                                    <i><img src="./assets/img/archive-icon.png"></i>
                                                </span>
                                                    <div class="cat__core__step__desc">
                                                        <span class="cat__core__step__title" *ngIf="notclickablecol != 1">{{archiveCount}}</span>
                                                        <p style="padding-left: 12%;">Archived</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-3"></div>
                                        </div>
                                    </a>
                                </div>

                         </div>
                         <div *ngIf="userDataConfig.enable_collaborate_edit != 1 || collaborate_edit_visible != 1" class="row actions-list" style="margin-bottom: 40px;margin-top: 53px;" [ngClass]="{'not-clickable':clickedTab != ''}">
                            <div class="col-sm-4 signedblock-border signature-req-block" [ngClass]="{'status-active':isActive == 'completed'}">
                                <a href="javascript:void(0)" (click)="changeFormData('COMPLETED','refresh')">
                                    <div class="row" style="height:100%">
                                        <div class="col-sm-3"></div>
                                        <div class="col-sm-6">
                                            <div class="cat__core__step pending-block">
                                                <span class="cat__core__step__digit"><i class="fa fa-check completed"></i></span>
                                                <div class="cat__core__step__desc">
                                                    <span class="cat__core__step__title">{{completedCount}}</span>
                                                    <p>Completed</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-3"></div> 
                                    </div>
                                </a>
                            </div>
                            <div class="col-sm-4 signedblock-border signature-req-block" [ngClass]="{'status-active':isActive == 'pending'}">
                                <a href="javascript:void(0)" (click)="changeFormData('PENDING','refresh')">
                                    <div class="row" style="height:100%">
                                        <div class="col-sm-3"></div>
                                        <div class="col-sm-6">
                                            <div class="cat__core__step cat__core no-border signed-block">
                                                <span class="cat__core__step__digit"><i class="fa fa-clock-o waiting-others"></i></span>
                                                <div class="cat__core__step__desc">
                                                    <span class="cat__core__step__title">{{pendingCount}}</span>
                                                    <p>Pending</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-3"></div>
                                    </div>
                                </a>
                            </div>
                     
                            <div class="col-sm-4 signature-req-block" [ngClass]="{'status-active':isActive == 'archived'}">
                                <a href="javascript:void(0)" (click)="changeFormData('ARCHIVE','refresh')">
                                    <div class="row" style="height:100%">
                                        <div class="col-sm-3"></div>
                                        <div class="col-sm-6">
                                            <div class="cat__core__step pending-block">
                                                <span class="cat__core__step__digit requests-archive-icon">
                                                <i><img src="./assets/img/archive-icon.png"></i>
                                            </span>
                                                <div class="cat__core__step__desc">
                                                    <span class="cat__core__step__title">{{archiveCount}}</span>
                                                    <p style="padding-left: 12%;">Archived</p>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-3"></div>
                                    </div>
                                </a>
                            </div>

                     </div>
                        </div>
                    </div>
                </div>
                <div class="row">
      <div class="col-lg-12" *ngIf="!isAdvancedSearchView">
                        <app-advance-search [hidden]="_structureService.hideAdvanceSearchArea" [resetForm]="resetAdvanceSearchForm" worklist="all form" [dynamicControls]="dynamicControls" (closeAdvanceSection)="_structureService.closeAdvanceSearch($event)" (advanceSearch)="applyAdvanceSearch($event)">
                        </app-advance-search>
                    </div>
                </div>
    <!-- Advanced search option for all the WorkList  -->
    <div class="row" *ngIf="isAdvancedSearchView" [hidden]="toggleAdvancedFilter">
      <div class="col-lg-12">
        <app-advance-search
          #advancedSearchFilter
          [dynamicControls]="dynamicControlsAdvancedView"
          (selectedFilters)="setAdvancedFilterKeys($event)"
          (closeAdvanceSection)="toggleAdvancedFilter = !toggleAdvancedFilter"
          (advanceSearch)="applyAdvanceSearch($event)"
          [advancedSearchView]="true"
          (resetForm)="(resetAdvancedFilter)"
          worklist="all form"
          [defaultFilterColumns]="defaultColumns"
        >
        </app-advance-search>
      </div>
    </div>

    <!-- column customization -->
    <div class="row d-flex justify-content-end" *ngIf="isAdvancedSearchView">
      <div class="col-lg-3 filter d-flex align-items-center gap-2" [hidden]="!isAdvancedSearchView">
        <input
          id="advancedFilterName"
          type="text"
          class="form-control adv-form-control"
          [(ngModel)]="searchFilterName"
          placeholder="{{ 'PLACEHOLDERS.ENTER_FILTER' | translate }}"
        />
        <button
          type="button"
          *ngIf="showSaveNewFilter; else btnUpdateFilter"
          class="btn btn-primary btn-sm ml-2"
          (click)="saveFilter()"
          [disabled]="!searchFilterName?.trim()"
        >
          {{ 'BUTTONS.SAVE_FILTER' | translate }}
        </button>
        <ng-template #btnUpdateFilter>
          <button type="button" class="btn btn-primary btn-sm ml-2" (click)="updateFilter()" [disabled]="!searchFilterName?.trim()">
            {{ 'BUTTONS.UPDATE_FILTER' | translate }}
          </button>
        </ng-template>
        <button
          type="button"
          *ngIf="searchFilterItem && searchFilterItem.id"
          (click)="setToNewFilter()"
          class="btn btn-sm btn-primary round-button ml-2"
        >
          {{ 'BUTTONS.ADD_NEW_FILTER' | translate }}
        </button>
      </div>
    </div>


                <div class="row">
                    <div class="col-lg-9"></div>
                    <div class="col-lg-3 date-range-align">
                        <div class="row">
                            <div class="col-lg-4 text-right padding-top-ten">
                                <label>{{'LABELS.DATE_RANGE' | translate}}
                                    <i chToolTip="defaultDateRangeInfo" class="icmn-info" data-animation="false">&nbsp;</i>:&nbsp;
                                </label>
                            </div>
                            <div class="col-lg-8">
                                <ch-daterange-picker [keepSession]="true" [dateRangeFilterOptions]="dateRangeFilterOptions" [control]="dateRange" [saveStateInto]="dateRangeStoreKey" (selectDateRange)="onSelectDateRange($event)" [emitOnLoad]="true"></ch-daterange-picker>
                            </div>
        </div>
      </div>
    </div>
                <form class="new-forms" >
                    <table  class="table table-hover table-responsive" id="form-list-dt" width="100%"  ></table>
                </form>  
    </div>
</section>


<div class="modal fade bd-example-modal-lg forward-modal" id="reminderDetailsModel" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="exampleModalLabel">Draft Activity History</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="page-wrap">
                    <div style="text-align:center;width:100%;" *ngIf="!reminderDetailsLoaded">
                        <img src="./assets/img/loader/loading.gif" />
                    </div>
                    <div class="table-responsive" *ngIf="reminderDetailsLoaded">
                        <table class="table table-striped" *ngIf="reminderDetails.length">
                            <tr *ngFor="let item of reminderDetails; let i = index">
                                <td>{{reminderDetailsLength-i}}</td>
                                <td>{{ item.sendername }} {{ item.modifiedAT }}</td>
                            </tr>
                        </table>
                        <div style="text-align:center;width:100%;" *ngIf="!reminderDetails.length">
                            <p>The draft details of this request are not available in the system.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<app-resend-document-form *ngIf="showResendModal" [showModal]="showResendModal" (onClose)="popUpRecipientsList()" 
[getRecipientsFor]="contentType" [patientId]="resendPatientId" [extraData]="{ admissionId: resendPatientId && activeStrucuredForms.admissionId }" [entityId]="activeStrucuredForms.sent_id" 
(eventEmitterSelectedRecipients)="resendFormToRecipients($event)">
</app-resend-document-form>

<app-form-history *ngIf="showHistoryModal == true" [showHistory]="showHistory" [showDraftHistory]="showDraftHistory" [isActive]="isActive" (closeModal)="closeHistoryModal($event)" (closeDraftModal)="closeDraftHistoryModal($event)" [formDetails]="activeStrucuredForms"></app-form-history>
