import { Component, OnInit, Input, ElementRef, <PERSON><PERSON>er, ViewChild } from '@angular/core';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute, Params, NavigationEnd, NavigationStart } from '@angular/router';
import { PapaParseService } from 'ngx-papaparse';
import { StructureService } from '../structure.service';
import { FormsService } from './forms.service';
import { DomSanitizer } from '@angular/platform-browser';
import { Modal } from "../shared/commonModal"
import { SharedService } from '../shared/sharedServices';
import { SignPadComponent } from '../shared/signPad/sign-pad.component';
import { FormpPipe } from './formp.pipe';
import { formSearchPipe } from './formp.pipe';
import { SignService } from '../signatures/sign.service';
import { ModalComponent } from '../signatures/modal.component';
import { InboxService } from '../inbox/inbox.service';
import { Location, DatePipe } from '@angular/common';
import { ToolTipService } from '../tool-tip.service';
import { ISubscription } from "rxjs/Subscription";
import { EnrollService } from '../user-registration/enroll.service';
import { DateTimePipe } from './formp.pipe';
import { GlobalDataShareService } from '../shared/global-data-share.service';
import { Guid } from "guid-typescript";
import { APIs } from 'app/constants/apis';
var jstz = require('jstz');
declare var signaturePad: any;
declare var $: any;
declare var swal: any;
const timezone = jstz.determine();
declare var NProgress: any;
let moment = require('moment/moment');

@Component({
  selector: 'list_form_data',
  templateUrl: './list-submitted-forms-automated.html'
})
export class FormsListingSupplyComponent implements OnInit {

  formsUrl: any;
  dTable;
  userData: any = '';
  structureFormContent: any;
  strucuredForms: any = [];
  activeStrucuredForms;
  dataLoadingMsg = true;
  isActive: any;
  isActiveArchive: boolean = false;
  isActivePending: boolean = false;
  
  completedCount;
  pendingCount;
  archiveCount;
  strucuredFormsPending: any;
  strucuredFormsCopy: any;
  strucuredFormsPendingArchived: any;
  crossTenantChangeSubscriber: any;
  constructor(
    private _structureService: StructureService,
    private route: ActivatedRoute,
    private router: Router,
    private _formsService: FormsService,
    elementRef: ElementRef,
    renderer: Renderer,
    private formpPipe: FormpPipe,
    private formSearchPipe: formSearchPipe,
    private _sharedService: SharedService
  ) {
    renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
      /*if(event.target.attributes[0] && event.target.attributes[0].name == 'data-view') {*/
      if (event.target.id == 'view' || event.target.parentElement.id == 'view') {
        this.viewStrucuturedForm();
      }/*else if(event.target.attributes[0] && event.target.attributes[0].name == 'data-download') {*/
      else if (event.target.id == 'download' || event.target.parentElement.id == 'download') {
        this.getPdfTaggedForm();
      } else if (event.target.id == 'archive-form' || event.target.parentElement.id == 'archive-form') {
        this.archiveSubmitedForm();
      } else if (event.target.id == 'restore-forms' || event.target.parentElement.id == 'restore-forms') {
        this.restoreForms();
      } else if (event.target.id == 'resend-form' || event.target.parentElement.id == 'resend-form') {
        this.resendForm();
      } else if (event.target.id == 'allow_edit' || event.target.parentElement.id == 'allow_edit') {

        this.allowEdit();
      }
    });
    this.crossTenantChangeSubscriber = this._sharedService.crossTenantChange.subscribe(
      (onInboxData) => {
        this.ngOnInit();
      }
    );
  }
  ngOnInit() {

    this.activeStrucuredForms = null;
    
    this.userData = JSON.parse(this._structureService.userDetails);
    this._formsService.getAllTaggedAutomatedForms(this._structureService.getCookie('tenantId'), this.userData.roleId, timezone.name(), true, true).then((result: any) => {
      this.changeFormData('COMPLETED');
      this.strucuredFormsPending = result.filter(function (formsData) {
        return formsData.admin_archived != 1;
      });
      this.strucuredFormsPendingArchived = result.filter(function (formsData) {
        return formsData.admin_archived == 1;
      });
      this.pendingCount = this.strucuredFormsPending.length;
    }).catch((ex) => {
      this.dataLoadingMsg = false;
    });

    //nov192019
    // window.addEventListener("message", function (event) {
    //   if (event.data) {
    //     var height = (event.data).split("=");
    //     if(height.length&&height[0]=="scrollTopformsubmit"){
    //       localStorage.setItem('autosavecalledformid',"0");
    //       localStorage.setItem('autosavecalled',"false");
    //       $(".structured-form-modal").scrollTop(0);
    //       window.scrollTo(0, 0);
    //     }
    //     else if(height.length&&height[0]=="gotocreatesendform"){
    //      $(".searchbar input").val("");
    //      $(".send-form-list ").find('.formname').first().trigger('click');
    //     }else if(height.length&&height[0]=="autosavecalled"){
    //        //   localStorage.setItem('autosavecalled', height[1]);
    //     // this.localStorage

    //     }else if(height.length&&height[0]=="autosavecalledformid"){

    //      // localStorage.setItem('autosavecalledformid', height[1]);
    //    }
    //    else if(height.length&&height[0]=="saveasdraftidtodeleted"){

    //    // localStorage.setItem('saveasdraftidtodeleted', height[1]);
    //  }
    //    else {

    //     if (typeof (parseInt(height[1])) == "number") {
    //       console.log("5555555555555555555555555555");
    //       $("#structured-form-dekstop").height(height[1] + "px")
    //     } else {
    //       $("#structured-form-dekstop").height("5000px")
    //     }
    //   }
    //   }
    // }, false);

  }
  ngOnDestroy() {
    if (this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
  }
  allowEdit() {
    console.log("111111111111111111111111111111111");

    var formSendMode = '';
    var applessMode = '';
    if(this.activeStrucuredForms.applessMode == 1) {
       formSendMode = 'appless';
       applessMode = 'both';
    }

    this.activeStrucuredForms.loggedinuserid = this.userData.userId;
    this._formsService.allowEditForm(this.activeStrucuredForms).then((result: any) => {
      var nameeditallow = "edit-" + this.activeStrucuredForms.form_id + this.activeStrucuredForms.form_submission_id;
      this.activeStrucuredForms.allow_edit = result.allowedit;
      let activityData: any;
      if (result.allowedit == 0) {
        $("[name=" + nameeditallow + "]").attr('title', 'Allow Edit').html('<i data-edit class="icmn-pencil2"></i>');
        this._structureService.notifyMessage({
          messge: 'Editable permission denied for ' + this.activeStrucuredForms.form_name,
          delay: 1000,
          type: 'success'
        });
        activityData = {
          activityName: "Allow Edit",
          activityType: "structured forms",
          activityDescription: "Edit has been denied for " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") by " + this.userData.displayName
        };
      }
      if (result.allowedit == 1) {
        $("[name=" + nameeditallow + "]").attr('title', 'Deny Edit').html('<i data-edit class="icmn-undo2"></i>');
        this._structureService.notifyMessage({
          messge: 'Editable permission granted for ' + this.activeStrucuredForms.form_name,
          delay: 1000,
          type: 'success'
        });
        activityData = {
          activityName: "Allow Edit",
          activityType: "structured forms",
          activityDescription: "Edit has been granted for " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") by " + this.userData.displayName
        };
        var deepLinking = {
          "pushType": "",
          "state": "eventmenu.forms",
          "stateParams": {},
          "formSendMode" : formSendMode,
          "applessMode" : applessMode,
          "sentId": this.activeStrucuredForms.sent_id
        };
        /* var deepLinking = {
              "pushType": "",
              "state": "eventmenu.forms",
              "stateParams": {},
              "tenantId": this.activeStrucuredForms.,
              "tenantName": this.userData.tenantName,
              "formSendMode": formSendMode,
              "applessMode" : applessMode,
              "sentId": ""
        }; */

        if(formSendMode == 'appless') {
          const selectedRecipientsPolling = [];
          const data = {
               userid: this.activeStrucuredForms.recipient_id,
               senderId: this.userData.userId,
               organizationMasterId: this.userData.organizationMasterId,
               formSendMode : formSendMode,
               applessMode:applessMode
          }
          selectedRecipientsPolling.push(data);
          var pushMessage = 'You have been granted permission to edit the form "' + this.activeStrucuredForms.form_name;
          var deepLinking1 = {
               "pushType": "",
               "state": "eventmenu.forms",
               "stateParams": {},
               "tenantId": this.activeStrucuredForms.tenant_id,
               "tenantName": this.activeStrucuredForms.tenantName,
               "formSendMode": formSendMode,
               "applessMode" : applessMode,
               "sentId": this.activeStrucuredForms.sent_id
          };
          this._structureService.sentPushNotification(selectedRecipientsPolling, 0, pushMessage, '', deepLinking1, '', '');
        } else {
          this._structureService.sentPushNotification(this.activeStrucuredForms.patient_id != "0" ? this.activeStrucuredForms.userid : this.activeStrucuredForms.from_id, 0, 'You have been granted permission to edit the form "' + this.activeStrucuredForms.form_name + '"', '', deepLinking, '', '');
        }
      }
      this._structureService.trackActivity(activityData);
      var data = { id: this.activeStrucuredForms.form_id, allowEdit: this.activeStrucuredForms.allow_edit, toId: this.activeStrucuredForms.patient_id != "0" ? (this.activeStrucuredForms.userid || this.activeStrucuredForms.recipient_id) : ((result.allowedit == 1) ? this.activeStrucuredForms.from_id : this.activeStrucuredForms.recipient_id), form_submission_id: this.activeStrucuredForms.form_submission_id };
      this._structureService.socket.emit("updateFormAllowEdit", data);
    });
  }
  resendForm() {
    var recipients = this.activeStrucuredForms.recipient_id.split();
    var message = this.activeStrucuredForms.message
    var selectedRecipients = [];
    var selectedRecipientsPolling = [];
    selectedRecipientsPolling.push({
      userid: this.activeStrucuredForms.recipient_id,
      organizationMasterId: this.userData.organizationMasterId
    });
    selectedRecipients.push(this.activeStrucuredForms.recipient_id);
    var data = { "form_id": this.activeStrucuredForms.form_id, "recipients": selectedRecipients, "userId": this.activeStrucuredForms.from_id, "message": message, "previousSendId": this.activeStrucuredForms.sent_id, "resend": true };
    const deepLinking = {
      pushType: "",
      state: "eventmenu.supply",
      stateParams: {},
      sentId: ''
    };

    let pushMessage = "[Reminder] New Supply to fill out";
    swal({
      title: "Are you sure?",
      text: "Do you want to resend this supply?",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Ok",
      closeOnConfirm: true
    }, () => {
      this._formsService.sendSupply(data).then((result: any) => {
        var self = this;
        this.strucuredFormsPending.map(function (pendingForms) {
          if (pendingForms.recipient_id == self.activeStrucuredForms.recipient_id && pendingForms.sent_id == self.activeStrucuredForms.sent_id) {
            pendingForms.senton = Date.now();
            pendingForms.sent_id = result.sentId;
          }
        });
        this.populateData(this.strucuredFormsPending);
        this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", selectedRecipientsPolling, 'form');

        /*patient reminder for form starts*/
        var otherDatas = {
          patientReminderTime: this.userData.config.patient_reminder_time * 1,
          patientReminderTypes: this.userData.config.patient_reminder_types,
          messageReplyTimeout: this.userData.config.message_reply_timeout,
          sentId: result.sentId,
          senderName: this.userData.displayName,
          tenantId: this.userData.tenantId,
          tenantName: this.userData.tenantName,
          serverBaseUrl: this._structureService.serverBaseUrl,
          apiVersion: this._structureService.version,
          message: "[Reminder] You have new Supply to fillout",
          formReminderType: this.userData.config.patient_reminder_checking_type,
          environment: this._structureService.environment
        }
        //this._structureService.socket.emit("ReminderForForm", selectedRecipientsPolling, otherDatas);
        this._structureService.reminderForForm(selectedRecipientsPolling, otherDatas);
        /*patient reminder for form ends*/
        deepLinking.sentId = result.sentId;
        this._structureService.sentPushNotification(selectedRecipientsPolling, 0, pushMessage, '', deepLinking, '', '');
        if (result.status == 1) {
          var activityData = {
            activityName: "Resend Form Success",
            activityType: "structured forms",
            activityDescription: this.userData.displayName + " successfully resend form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") to " + selectedRecipients.toString()
          };
          this._structureService.trackActivity(activityData);
          this._structureService.notifyMessage({
            messge: this.activeStrucuredForms.form_name + ' has been resend successfully',
            delay: 1000,
            type: 'success'
          });
        } else {
          var activityData = {
            activityName: "Resend Form Fail",
            activityType: "structured forms",
            activityDescription: this.userData.displayName + " form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") to " + selectedRecipients.toString() + " resending failed"
          };
          this._structureService.trackActivity(activityData);
          this._structureService.notifyMessage({
            messge: this.activeStrucuredForms.form_name + ' sending failed',
            delay: 1000
          });
        }
        if (this.isActive == "pending") {
          $(this.dTable.column(11).header()).text('Sent On');
          this.dTable.column(7).visible(false);
        }
      });
    });
  }
  restoreForms() {
    if (this.activeStrucuredForms.created_date) {
      this.activeStrucuredForms.isActive = 'completed';
    } else {
      this.activeStrucuredForms.isActive = 'pending';
    }
    NProgress.start();
    this._formsService.restoreArchivedForm(this.activeStrucuredForms).then((result: any) => {
      var activityData: any = {};
      if (result.status == '1') {        
        NProgress.done();
        let self = this;
        if (this.activeStrucuredForms.isActive == 'completed') {
          this.sendPolling('completed', result.notifyUsers);
          activityData.activityName = "Restore Completed Form";
          this.strucuredForms.map(function (element) {
            if (element.form_id == self.activeStrucuredForms.form_id && element.form_submission_id == self.activeStrucuredForms.form_submission_id) {
              element.is_deleted = '0';
            }
          });
          this.strucuredForms = this.strucuredForms.filter(function (formData) {
            return formData.is_deleted != 0;
          });
          this.populateData(this.strucuredForms);
          this.completedCount = this.completedCount + 1;

          this._structureService.notifyMessage({
            messge: 'Successfully restored the completed form - ' + this.activeStrucuredForms.form_name,
            delay: 1000,
            type: 'success'
          });
        } else {
          this.sendPolling('pending', result.notifyUsers);
          activityData.activityName = "Restore Pending Form";
          this.strucuredForms.map(function (element) {
            if (element.sent_id == self.activeStrucuredForms.sent_id && element.recipient_id == self.activeStrucuredForms.recipient_id) {
              element.admin_archived = '0';
            }
          });
          this.strucuredForms = this.strucuredForms.filter(function (formData) {
            return formData.admin_archived != 0;
          });
          this.strucuredFormsPendingArchived.map(function (element) {
            if (element.sent_id == self.activeStrucuredForms.sent_id && element.recipient_id == self.activeStrucuredForms.recipient_id) {
              element.admin_archived = '0';
            }
          });
          this.strucuredFormsPendingArchived = this.strucuredFormsPendingArchived.filter(function (element) {
            return element.admin_archived != 0;
          });
          this.populateData(this.strucuredForms);

          this.strucuredFormsPending = this.strucuredFormsPending.concat(this.activeStrucuredForms);
          this.strucuredFormsPending.sort(function (a, b) { return a.senton - b.senton });

          this._structureService.notifyMessage({
            messge: 'Successfully restored the pending form - ' + this.activeStrucuredForms.form_name,
            delay: 1000,
            type: 'success'
          });
          this.pendingCount = this.pendingCount + 1;

          activityData.activityType = "structured forms";
          activityData.activityDescription = this.userData.displayName + " restored form " + this.activeStrucuredForms.form_name + " (formId " + this.activeStrucuredForms.form_id + ",sendId " + this.activeStrucuredForms.sent_id + ") successfully";
          this._structureService.trackActivity(activityData);
        }
        this.archiveCount = this.archiveCount - 1;
      } else {
        NProgress.done();
        activityData = {
          activityName: "Restore Completed Form",
          activityType: "structured forms",
          activityDescription: this.userData.displayName + " restored form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") failed"
        };
        this._structureService.trackActivity(activityData);
      }
    });
  }
  archiveSubmitedForm() {
    NProgress.start();
    this.activeStrucuredForms.isActive = this.isActive;
    this.activeStrucuredForms.downloadTime = new Date().toLocaleString();
    var createdontime;
    if (this.activeStrucuredForms.senton.toString().length == 13) {
      createdontime = this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton));
    } else {
      createdontime = this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton) * 1000);
    }
    this.activeStrucuredForms.createdOn = createdontime;
    this.activeStrucuredForms.createdOnNew = this.activeStrucuredForms.senton;
    this._formsService.archiveSubmittedForm(this.activeStrucuredForms).then((result: any) => {
      var activityData: any = {};
      var notifyResult: any = result;
      if (result.status == '1') {
        if (notifyResult.defaultfilingCenter) {
          activityData.activityName = "Form Copied to Filing Center";
          activityData.activityType = "structured forms";
          activityData.activityDescription = this.userData.displayName + " archived form " + this.activeStrucuredForms.form_name + " (formId " + this.activeStrucuredForms.form_id + ",sendId " + this.activeStrucuredForms.sent_id + ") successfully and copied to filing center named " + notifyResult.defaultfilingCenter;
          this._structureService.trackActivity(activityData);

        }


        NProgress.done();
        if (this.isActive == 'completed') {
          this._formsService.getAllTaggedAutomatedForms(this._structureService.getCookie('tenantId'), this.userData.roleId, timezone.name(), true, false).then((result: any) => {
            this.strucuredFormsCopy = result;
            this._structureService.notifyMessage({
              messge: 'Successfully archived the completed form - ' + this.activeStrucuredForms.form_name,
              delay: 1000,
              type: 'success'
            });
            activityData.activityName = "Archive Completed Form";
            this.sendPolling('completed', notifyResult.notifyUsers);
          });
        } else {
          activityData.activityName = "Archive Pending Form";
          this._structureService.notifyMessage({
            messge: 'Successfully pending the completed form - ' + this.activeStrucuredForms.form_name,
            delay: 1000,
            type: 'success'
          });
          this.sendPolling('pending', result.notifyUsers);
        }
        activityData.activityType = "structured forms";
        activityData.activityDescription = this.userData.displayName + " archived form " + this.activeStrucuredForms.form_name + " (formId " + this.activeStrucuredForms.form_id + ",sendId " + this.activeStrucuredForms.sent_id + ") successfully";
        this._structureService.trackActivity(activityData);
        this.removeScopeFormOnArchive(this.activeStrucuredForms);
      } else {
        NProgress.done();
        activityData = {
          activityName: "Archive Completed Form",
          activityType: "structured forms",
          activityDescription: this.userData.displayName + " archived form " + this.activeStrucuredForms.form_name + " (" + this.activeStrucuredForms.form_id + ") failed"
        };
        this._structureService.trackActivity(activityData);
      }
    });
  }
  removeScopeFormOnArchive(archivedForm) {
    if (this.isActive == 'completed') {
      this.strucuredForms.map(function (element) {
        if (element.form_id == archivedForm.form_id && element.form_submission_id == archivedForm.form_submission_id) {
          element.is_deleted = '1';
        }
      });
      this.strucuredForms = this.strucuredForms.filter(function (formData) {
        return formData.is_deleted == 0;
      })
      this.completedCount = this.completedCount - 1;
      this.archiveCount = this.archiveCount + 1;
      this.populateData(this.strucuredForms);

      /*this._formsService.getAllTaggedForms(this._structureService.getCookie('tenantId'),this.userData.roleId, timezone.name(), true, false).then((result:any) => {
        this.strucuredFormsCopy = result;
      });*/
    } else {
      this.strucuredFormsPending.map(function (element) {
        if (element.sent_id == archivedForm.sent_id && element.recipient_id == archivedForm.recipient_id) {
          element.admin_archived = '1';
        }
      });
      this.strucuredFormsPending = this.strucuredFormsPending.filter(function (formData) {
        return formData.admin_archived == 0;
      });
      /*this.strucuredFormsPendingArchived = this.strucuredFormsPending.filter(function(formData){
        return formData.admin_archived != 0;
      });*/
      this.strucuredFormsPendingArchived = this.strucuredFormsPendingArchived.concat(this.activeStrucuredForms);
      this.strucuredFormsPendingArchived.sort(function (a, b) { return a.senton - b.senton });
      this.pendingCount = this.pendingCount - 1;
      this.archiveCount = this.archiveCount + 1;
      this.populateData(this.strucuredFormsPending);

      /*this._formsService.getAllTaggedForms(this._structureService.getCookie('tenantId'),this.userData.roleId, timezone.name(), true, true).then((result:any) => {
        this.strucuredFormsPendingArchived = result.filter(function(formsData){
          return formsData.admin_archived == 1;
        });
      });*/
    }
  }
  sendPolling(type, notifyUsers) {
    var selectedRecipientsPolling = [];
    if (type == 'completed') {
      if (this.userData.userId != this.activeStrucuredForms.from_id) {
        notifyUsers.push({ userid: this.activeStrucuredForms.from_id });
      }
      if (this.userData.userId != this.activeStrucuredForms.userid) {
        notifyUsers.push({ userid: this.activeStrucuredForms.userid });
      }
      selectedRecipientsPolling = notifyUsers;
    } else {
      if (this.userData.userId != this.activeStrucuredForms.from_id) {
        selectedRecipientsPolling.push({ userid: this.activeStrucuredForms.from_id });
      }
      if (this.userData.userId != this.activeStrucuredForms.recipient_id) {
        selectedRecipientsPolling.push({ userid: this.activeStrucuredForms.recipient_id });
      }
    }
    console.log(selectedRecipientsPolling, "selectedRecipientsPolling");
    this._structureService.socket.emit("inventoryCountOrSignatureDocMessages", selectedRecipientsPolling, 'form');
  }
  changeFormData(type) {
    switch (type) {
      case 'COMPLETED':
        this.dataLoadingMsg = true;
        this.isActive = 'completed';
        this._formsService.getAllTaggedAutomatedForms(this._structureService.getCookie('tenantId'), this.userData.roleId, timezone.name(), true, false).then((result: any) => {
          this.strucuredFormsCopy = result;
          this.strucuredForms = result.filter(function (formData) {
            return formData.is_deleted == 0;
          })
          this.completedCount = this.strucuredForms.length;
          this.archiveCount = (this.strucuredFormsCopy.length - this.strucuredForms.length) + this.strucuredFormsPendingArchived.length;
          if (this.isActive == 'completed') {
            this.populateData(this.strucuredForms);
          }
        }).catch((ex) => {
          this.dataLoadingMsg = false;
        });
        break;
      case 'PENDING':
        this.dataLoadingMsg = true;
        this.isActive = 'pending';
        this.populateData(this.strucuredFormsPending);
        $(this.dTable.column(11).header()).text('Sent On');
        this.dTable.column(7).visible(false);
        break;
      case 'ARCHIVE':
        this.dataLoadingMsg = true;
        this.isActive = 'archive';
        this.strucuredForms = this.strucuredFormsCopy.filter(function (formData) {
          return formData.is_deleted != 0;
        });
        let arr: any = [];
        arr = this.strucuredForms.concat(this.strucuredFormsPendingArchived);
        arr.sort(function (a, b) { return a.senton - b.senton });
        this.strucuredForms = arr;
        this.archiveCount = this.strucuredForms.length;
        this.populateData(this.strucuredForms);
        break;
    }
  }
  populateData(strucuredForms) {
    var self = this;
    function format(row) {
      var clientId = self._structureService.socket.io.engine.id;
      var tokenId = self._structureService.getCookie('authenticationToken');
      var facing_new = row.facing_new;
      var enable_sftp_integration=this.userDataConfig.enable_sftp_integration==1?"&enable_sftp_integration="+true:"&enable_sftp_integration="+false;
      var unique="&uniqueFormIdentity="+Guid.create();
      var confirmActionPrefill=this.activeForm.confirmActionPrefill==1?"&confirmActionPrefill=true":"&confirmActionPrefill=false";
      var fax_queue_show_warning=this.userDataConfig.fax_queue_show_warning == 1?"&fax_queue_show_warning="+true+unique+confirmActionPrefill:"&fax_queue_show_warning="+false+unique+confirmActionPrefill;
      var formsUrl: any = this._structureService.machFormUrl + APIs.machFormEmbed + '?id=' + row.form_id + "&clientId=" + clientId + "&facing_new=" + facing_new+"&authenticationToken=" +this._structureService.getCookie('authenticationToken')+enable_sftp_integration+fax_queue_show_warning;
      return `<div style="width:100%;" class="structured-form-modal">
      <iframe onload="$('.structured-form-modal').scrollTop(0);" allowTransparency="true" class="structured-form-dekstop" id="structured-form-dekstop" frameborder="0" scrolling="no" style="width:100%;border:none;pointer-events: none;" src="${ formsUrl}" ></iframe>
      </div>`;
    }
    if (this.dTable) {
      this.dTable.destroy();
    }
    var isTrue = false;
    if (this.strucuredForms.length > 99) {
      isTrue = true;
    }
    var self = this;
    const x = this.dTable = $('#form-list-dt').DataTable({
      autoWidth: false,
      order: [[8, "desc"]],
      responsive: true,
      retrieve: true,
      pagination: true,
      serching: true,
      paging: isTrue,
      bInfo: isTrue,
      lengthMenu: [[100, 250, 500, 1000, -1], [100, 250, 500, 1000, 'All']],
      data: strucuredForms,
      fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
        $(nRow).on('click', () => {
          this.activeStrucuredForms = aData;
        });
      },
      columns: [
        { title: "#" },
        { title: "Sent By", data: 'createdUser' },
        { title: "Received By" },
        { title: "Supply Name", data: 'form_name' },
        { title: "Supply Type" },
        { title: "Sent On", data: 'sentDate' },
        { title: "Sent On" },
        { title: "Submitted On", data: 'senton' },
        { title: "Submitted On", data: 'senton' },
        { className: 'details-control', title: "Actions" }
      ],
      columnDefs: [
        {
          data: null,
          orderable: false,
          width: "5%",
          targets: 0
        },
        {
          data: null,
          targets: 1,
          width: "20%",
        },
        {
          data: null,
          targets: 2,
          width: "20%",
          render: function (data, type, row) {
            if (row.patient_id != '0' && (self.isActive == 'completed' || self.isActive == 'archive') && row.created_date) {
              return '';
            } else {
              if (row.caregiver_userid) {
                return row.caregiver_displayname + " (" + row.patientName + ")";
              } else {
                return row.patientName;
              }
            }
          },
        },
        {
          data: null,
          targets: 3,
          width: "20%",
        },
        {
          data: null,
          targets: 4,
          width: "5%",
          orderable: false,
          render: function (data, type, row) {
            if (row.patient_id != '0' && (self.isActive == 'completed' || self.isActive == 'archive') && row.created_date) {
              return '<span class="badge badge-success mr-2 mb-2">Staff/Partner Facing</span>';
            } else {
              return '<span class="badge badge-primary mr-2 mb-2">Patient Facing</span>';
            }
          }
        },
        {
          data: null,
          targets: 5,
          width: "0%",
          visible: false,
          searchable: false
        },
        {
          data: null,
          targets: 6,
          width: "10%",
          orderData: [5],
          render: (data, type, row) => {
            if (row.patient_id != '0' && (self.isActive == 'completed' || self.isActive == 'archive') && row.created_date) {
              return this.formpPipe.transform(parseInt(row.senton) * 1000);
            } else {
              return this.formpPipe.transform(parseInt(row.sentDate) * 1000);
            }
          }
        },
        {
          data: null,
          targets: 7,
          width: "10%",
          orderData: [8],
          render: (data, type, row) => {
            if (self.isActive == 'archive' && !row.created_date) {
              return '';
            } else {
              if (data.toString().length == 13) {
                return this.formpPipe.transform(parseInt(data));
              } else {
                return this.formpPipe.transform(parseInt(data) * 1000);
              }
            }
          }
        }, {
          data: null,
          targets: 8,
          width: "0%",
          visible: false,
          searchable: false
        },
        {
          data: null,
          orderable: false,
          render: function (strucuredForm, type, row) {
            let actions = '<div class="btn-group mr-2 mb-2 no-margin" aria-label="" role="group">';
            if (row.created_date) {
              actions += `<a href="javascript: void(0);" id="view" data-view class="pull-right btn btn-sm" title="View"><i data-view class="icmn-eye"></i></a>`;
              actions += `<a href="javascript: void(0);" id="download" data-download class="pull-right btn btn-sm" title="Download"><i data-download class="icmn-download"></i></a>`;
            } else {
              actions += `<a href="javascript: void(0);" id="view-form" data-view class="pull-right btn btn-sm view-form" title="View"><i data-view class="icmn-eye"></i></a>`;
              if (self.isActive != 'archive') {
                actions += `<a href="javascript: void(0);" id="resend-form" data-view class="pull-right btn btn-sm resend-form" title="Resend"><i class="fa fa-refresh" aria-hidden="true"></i></a>`;
              }
            }
            if (self.isActive == 'archive') {
              actions += `<a href="javascript: void(0);" id="restore-forms" class="pull-right btn btn-sm" title="Restore"><i class="fa fa-history"></i></a>`;
            } else {
              actions += `<a href="javascript: void(0);" id="archive-form" data-archive class="pull-right btn btn-sm" title="Archive"><i class="icmn-box-add"></i></a>`;
            }
            if (row.created_date && self.isActive != 'archive') {
              if (row.allow_edit == 1) {
                actions += `<a href="javascript: void(0);" id="allow_edit" name="edit-` + row.form_id + row.form_submission_id + `" class="pull-right btn btn-sm" title="Deny Edit"><i data-edit class="icmn-undo2"></i></a>`;

              }
              else {
                actions += `<a href="javascript: void(0);" id="allow_edit" name="edit-` + row.form_id + row.form_submission_id + `" class="pull-right btn btn-sm" title="Allow Edit"><i data-edit class="icmn-pencil2"></i></a>`;

              }
            }
            actions += `</div>`
            return actions;
          },
          width: "10%",
          targets: -1
        }
      ]
    });
    this.dTable.on('order.dt search.dt', () => {
      this.dTable.column(0, { search: 'applied', order: 'applied' }).nodes().each(function (cell, i) {
        cell.innerHTML = i + 1;
      });
    }).draw();
    $('#form-list-dt tbody').off('click', 'td.details-control > div > .view-form');
    $('#form-list-dt tbody').on('click', 'td.details-control > div > .view-form', function () {
      var tr = $(this).closest('tr');
      var row = x.row(tr);
      if (row.child.isShown()) {
        //This row is already open - close it
        row.child.hide();
        tr.removeClass('shown');
      } else {
        //Open this row
        row.child(format(row.data())).show();
        tr.addClass('shown');
      }
    });
    this.dataLoadingMsg = false;
  }
  removeFormData(id, index) {
    swal({
      title: "Are you sure want to delete?",
      text: "",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Ok",
      closeOnConfirm: true
    }, () => {
      this._formsService.deleteTaggedForms(id, 1).then((result) => {
        if (result) {
          this.strucuredForms = this.strucuredForms.filter(function (data) {
            return data.id != id;
          });
        }
      }).catch((ex) => {
        //this.loginFailed = true;
      });
    });
  }
  viewStrucuturedForm() {
    this._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)), 1);
    localStorage.setItem('formData', encodeURIComponent(JSON.stringify(this.activeStrucuredForms)));
    this.router.navigate(['/forms/list/view']);
  }
  getPdfTaggedForm() {
    var newWindow: any = window.open(this._structureService.serverBaseUrl + "/webapp/www/img/gif-loader.gif");
    var downloadTime = moment((moment().unix()) * 1000).format('MMMM DD, YYYY h:mm a');
    var data = { "formId": this.activeStrucuredForms.form_id, "submissionId": this.activeStrucuredForms.form_submission_id, "tenantId": this.userData.tenantId, "tenantName": this.userData.tenantName, "createdOnNew": this.activeStrucuredForms.senton, "createdOn": moment(this.activeStrucuredForms.senton * 1000).format('MMMM DD, YYYY h:mm a'), "sendOn": moment(this.activeStrucuredForms.sentDate * 1000).format('MMMM DD, YYYY h:mm a'), "downloadTime": downloadTime };//this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton) * 1000)
    this._formsService.generateTaggedFormReportPdf(data, 2, timezone.name()).then((result) => {
      var fileName: any = '';
      fileName = result;
      var fileUrl = this._structureService.apiBaseUrl + '/writable/filetransfer/uploads/tagged-form-reports/' + fileName._body + '.pdf';
      newWindow.location = fileUrl;
      //this.download(fileUrl,fileName);
    }).catch((ex) => {
      //this.loginFailed = true;
    });
  }
  download(fileUrl, fileName) {
    /*var link = document.createElement("a");
    document.body.appendChild(link);
    link.setAttribute("type", "hidden"); 
    link.setAttribute("target", "_blank"); 
    link.download = 'tagged-from';
    link.href = fileUrl;
    link.click();*/
    window.open(fileUrl, "_blank");
  }
}
