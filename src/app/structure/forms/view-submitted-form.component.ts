import { Component, OnInit, ViewChild, ChangeDetectorRef } from '@angular/core';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute, Params, NavigationEnd, NavigationStart } from '@angular/router';
import { CONSTANTS } from 'app/constants/constants';
import { StructureService } from '../structure.service';
import { FormsService } from './forms.service';
import { DomSanitizer } from '@angular/platform-browser';
import { Modal } from "../shared/commonModal"
import { SharedService } from '../shared/sharedServices';
import { SignPadComponent } from '../shared/signPad/sign-pad.component';
import { FormpPipe } from './formp.pipe';
import { Location, DatePipe } from '@angular/common';
import { ISubscription } from "rxjs/Subscription";
import { NGXLogger } from 'ngx-logger';

var jstz = require('jstz');
declare var signaturePad: any;
declare var $: any;
declare var swal: any;
const timezone = jstz.determine();
declare var NProgress: any;
let moment = require('moment/moment');

@Component({
  selector: 'form_data_view',
  templateUrl: './submitted-forms-view.html'
})
export class ViewFormDataComponent implements OnInit {
  showLoader = false;
  formLandingFlow = false;
  strucuredFormsData: any = '';
  userData: any = '';
  signUrl: any = '';
  activeStrucuredForms;
  activeTab:any='';
  hidewithoutAssociation;
  patientassociationPract:any ='';
  sendBy: any = '';
  patientInitial: any = '';
  conditionArray: any = [];
  prevUrl: any = '';
  formContent: any = '';
  signpad_url_array:any=[];
  canvas_height: any = '';
  line_margin_top: any = '';
  structureFormContent: any = '';
  private subscription: ISubscription;
  crossTenantChangeSubscriber: any;
  patient_dob : any;
  mrn :any;
  showAllowEdit = false;
  disableAllowEdit = false;
  showArchive = false;
  disableArchive = false;
  previlages;
  showHistory: Boolean = false;
  showHistoryButton: Boolean = false;
  newLineLimit = CONSTANTS.formViewNewLineLimit;
  formId: any = '';
  submissionId: any = '';
  
  constructor(
    public _structureService: StructureService,
    private route: ActivatedRoute,
    private router: Router,
    private readonly formsService: FormsService,
    private modals: Modal,
    private sanitizer: DomSanitizer,
    public readonly sharedService: SharedService,
    private formpPipe: FormpPipe,
    public _location: Location,
    private ngxLogger: NGXLogger,
    private cdr: ChangeDetectorRef
  ) {
    this.crossTenantChangeSubscriber = this._sharedService.crossTenantChange.subscribe(
      (onInboxData) => {
        this.ngOnInit();
      }
    );
  }
  @ViewChild(SignPadComponent) childpad: SignPadComponent;

  // eslint-disable-next-line no-underscore-dangle
  get _sharedService() {
    return this.sharedService;
  }
  // eslint-disable-next-line no-underscore-dangle
  get _formsService() {
    return this.formsService;
  }
  ngOnDestroy() {
    if(this.signpad_url_array.length){
      this._formsService.unlinkSignaturePadImages(this.signpad_url_array).then((result) => { 
      if(result==true)  this.signpad_url_array=[];
     
    });
    }
    this.subscription.unsubscribe();
    if (this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
  }
  ngOnInit() {
    this.formLandingFlow = !!localStorage.getItem('isFormLandingFlow');
    this.previlages = [];
    this.activeTab= this._sharedService.formsType;
    this.hidewithoutAssociation = true;
    this.patientassociationPract=1;
    this.subscribe();
    this.userData = JSON.parse(this._structureService.userDetails);
    let manageTenants = this.userData.privileges;
    manageTenants = typeof(manageTenants) === 'undefined' ? this._structureService.getCookie('userPrivileges') : manageTenants;
    manageTenants = manageTenants.split(',');
    for (var i = 0; i < manageTenants.length; i++) {
          this.previlages[manageTenants[i]] = true;
    }
    this.formId = localStorage.getItem('formId');
    this.submissionId = localStorage.getItem('submissionId');
    // console.log ("Form id ng On It ",formId);
    // console.log ("submissionId  id ng On It ",submissionId);
    if (this.formLandingFlow && this.formId && this.submissionId) {
      this.activeStrucuredForms = {
        ...this.activeStrucuredForms,
        form_id: this.formId,
        form_submission_id: this.submissionId
      };
      this.processData();
    } else {
    let formData = localStorage.getItem('formData');
    this.ngxLogger.log('formData',formData);
    if (typeof (formData) != "undefined" && formData != '') {
      this.activeStrucuredForms = JSON.parse(decodeURIComponent(formData));
    } else {
      if (typeof this._structureService.getCookie('formData') == 'object') {
        this.activeStrucuredForms = this._structureService.getCookie('formData');
      } else {
        this.activeStrucuredForms = JSON.parse(decodeURIComponent(this._structureService.getCookie('formData')));
      }
      }
      this.processData();
    }
    this.ngxLogger.log('formData', this.activeStrucuredForms);
  }
  subscribe() {
    this.subscription = this.sharedService.onAllowEditChange.subscribe((value) => {
      this.activeStrucuredForms = value;
      this.subscription.unsubscribe();
      this.ngOnInit();
    });
  }
  processData() {
    this.formsService
      .getStrucuturedFormResults(this.activeStrucuredForms.form_id || this.activeStrucuredForms.formId, this.activeStrucuredForms.form_submission_id)
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      .then((result: any) => {
        this.strucuredFormsData = result;
        if (this.formLandingFlow) {
          this.activeStrucuredForms = result.entries;
          this.activeStrucuredForms.created_on = Number(this.activeStrucuredForms.created_on);
        }
        this.cdr.detectChanges();
      if(this.activeStrucuredForms.facing_new == '2'&& this.activeStrucuredForms.caregiver_displayname==this.activeStrucuredForms.createdUser)
      {
        this.patientassociationPract = 0;
      } 
      this.sendBy = this.strucuredFormsData.entries.createdUserLastname;
      this.patient_dob= this.strucuredFormsData.entries.pat_dob;

      var patient_dobDay = new Date(this.patient_dob).getDay();
      if (this.patient_dob && !isNaN(patient_dobDay)) {
        this.patient_dob = this.patient_dob.replace(/-/g, '/');

        try {
          this.patient_dob = moment(this.patient_dob).format('MM/DD/YYYY');
        }
        catch (e) {
          this.patient_dob = '';
        }
      } else {
        this.patient_dob = "";
      }
      this.mrn= this.strucuredFormsData.entries.mrn;
      if(typeof this.strucuredFormsData.formName!='undefined' && this.strucuredFormsData.formName!='') this.activeStrucuredForms.form_name = this.activeStrucuredForms.formName =this.strucuredFormsData.formName;
      this.patientInitial = this.strucuredFormsData.entries.pateintInitial;
      if (this.strucuredFormsData.entries.sign_status == 1) {
        this.signUrl = this._structureService.apiBaseUrl + "writable/filetransfer/uploads/" + this.strucuredFormsData.entries.sign_url;
      }
      /*logic start*/
      var self = this;
      this.strucuredFormsData.structuredFormResult.forEach(formsData => {
        self.strucuredFormsData.structuredFormResult.filter(function (element) {


          if (formsData.condition_element != undefined) {
            if (element.element_id == formsData.condition_element) {
              if (formsData.condition__keyword != '') {
                if (formsData.condition__keyword != element.value) {
                  //self.conditionArray.push(formsData.element_id);  commented for some conditions not working - some fields are not showing in the report
                }
              } else {
                if (element.value == '') {
                  //self.conditionArray.push(formsData.element_id);
                }
              }
            }
          }
        });
        if (formsData.element_type == "signature") {
           if(formsData.singpad_img_src) this.signpad_url_array.push(formsData.singpad_img_src);
          if (formsData.size == 'small') {
            this.canvas_height = 70;
            this.line_margin_top = 50;
          } else if (formsData.size == 'medium') {
            this.canvas_height = 130;
            this.line_margin_top = 95;
          } else {
            this.canvas_height = 260;
            this.line_margin_top = 200;
          }


          console.log(formsData.value)
          // formsData.value=[{"lx":29,"ly":195,"mx":29,"my":194},{"lx":33,"ly":189,"mx":29,"my":195},{"lx":37,"ly":183,"mx":33,"my":189},{"lx":43,"ly":178,"mx":37,"my":183},{"lx":47,"ly":175,"mx":43,"my":178},{"lx":50,"ly":172,"mx":47,"my":175},{"lx":53,"ly":171,"mx":50,"my":172},{"lx":54,"ly":170,"mx":53,"my":171},{"lx":55,"ly":168,"mx":54,"my":170},{"lx":57,"ly":167,"mx":55,"my":168},{"lx":56,"ly":166,"mx":57,"my":167},{"lx":55,"ly":170,"mx":56,"my":166},{"lx":53,"ly":175,"mx":55,"my":170},{"lx":50,"ly":183,"mx":53,"my":175},{"lx":47,"ly":189,"mx":50,"my":183},{"lx":44,"ly":193,"mx":47,"my":189},{"lx":43,"ly":196,"mx":44,"my":193},{"lx":41,"ly":196,"mx":43,"my":196},{"lx":45,"ly":188,"mx":41,"my":196},{"lx":51,"ly":178,"mx":45,"my":188},{"lx":57,"ly":168,"mx":51,"my":178},{"lx":61,"ly":162,"mx":57,"my":168},{"lx":65,"ly":155,"mx":61,"my":162},{"lx":68,"ly":151,"mx":65,"my":155},{"lx":70,"ly":149,"mx":68,"my":151},{"lx":72,"ly":151,"mx":70,"my":149},{"lx":72,"ly":153,"mx":72,"my":151},{"lx":71,"ly":160,"mx":72,"my":153},{"lx":68,"ly":168,"mx":71,"my":160},{"lx":65,"ly":175,"mx":68,"my":168},{"lx":62,"ly":182,"mx":65,"my":175},{"lx":61,"ly":188,"mx":62,"my":182},{"lx":60,"ly":192,"mx":61,"my":188},{"lx":60,"ly":193,"mx":60,"my":192},{"lx":73,"ly":173,"mx":60,"my":193},{"lx":80,"ly":163,"mx":73,"my":173},{"lx":83,"ly":158,"mx":80,"my":163},{"lx":84,"ly":158,"mx":83,"my":158},{"lx":85,"ly":158,"mx":84,"my":158},{"lx":83,"ly":165,"mx":85,"my":158},{"lx":81,"ly":169,"mx":83,"my":165},{"lx":77,"ly":174,"mx":81,"my":169},{"lx":73,"ly":179,"mx":77,"my":174},{"lx":70,"ly":183,"mx":73,"my":179},{"lx":68,"ly":186,"mx":70,"my":183},{"lx":66,"ly":189,"mx":68,"my":186},{"lx":66,"ly":190,"mx":66,"my":189},{"lx":66,"ly":192,"mx":66,"my":190},{"lx":66,"ly":193,"mx":66,"my":192},{"lx":78,"ly":181,"mx":66,"my":193},{"lx":83,"ly":172,"mx":78,"my":181},{"lx":87,"ly":167,"mx":83,"my":172},{"lx":90,"ly":162,"mx":87,"my":167},{"lx":93,"ly":158,"mx":90,"my":162},{"lx":96,"ly":156,"mx":93,"my":158},{"lx":98,"ly":155,"mx":96,"my":156},{"lx":99,"ly":155,"mx":98,"my":155},{"lx":98,"ly":157,"mx":99,"my":155},{"lx":96,"ly":163,"mx":98,"my":157},{"lx":92,"ly":172,"mx":96,"my":163},{"lx":87,"ly":181,"mx":92,"my":172},{"lx":84,"ly":187,"mx":87,"my":181},{"lx":82,"ly":192,"mx":84,"my":187},{"lx":82,"ly":195,"mx":82,"my":192},{"lx":82,"ly":196,"mx":82,"my":195},{"lx":82,"ly":195,"mx":82,"my":196},{"lx":88,"ly":192,"mx":82,"my":195},{"lx":96,"ly":183,"mx":88,"my":192},{"lx":102,"ly":177,"mx":96,"my":183},{"lx":107,"ly":171,"mx":102,"my":177},{"lx":111,"ly":166,"mx":107,"my":171},{"lx":113,"ly":163,"mx":111,"my":166},{"lx":115,"ly":161,"mx":113,"my":163},{"lx":116,"ly":161,"mx":115,"my":161},{"lx":115,"ly":168,"mx":116,"my":161},{"lx":113,"ly":173,"mx":115,"my":168},{"lx":110,"ly":180,"mx":113,"my":173},{"lx":107,"ly":186,"mx":110,"my":180},{"lx":105,"ly":191,"mx":107,"my":186},{"lx":103,"ly":194,"mx":105,"my":191},{"lx":102,"ly":195,"mx":103,"my":194},{"lx":109,"ly":186,"mx":102,"my":195},{"lx":113,"ly":182,"mx":109,"my":186},{"lx":116,"ly":179,"mx":113,"my":182},{"lx":118,"ly":178,"mx":116,"my":179},{"lx":119,"ly":176,"mx":118,"my":178},{"lx":121,"ly":175,"mx":119,"my":176},{"lx":123,"ly":174,"mx":121,"my":175},{"lx":124,"ly":173,"mx":123,"my":174},{"lx":125,"ly":173,"mx":124,"my":173},{"lx":126,"ly":172,"mx":125,"my":173},{"lx":123,"ly":171,"mx":126,"my":172},{"lx":119,"ly":168,"mx":123,"my":171},{"lx":114,"ly":166,"mx":119,"my":168},{"lx":107,"ly":163,"mx":114,"my":166},{"lx":99,"ly":160,"mx":107,"my":163},{"lx":93,"ly":157,"mx":99,"my":160},{"lx":88,"ly":155,"mx":93,"my":157},{"lx":85,"ly":154,"mx":88,"my":155},{"lx":84,"ly":152,"mx":85,"my":154},{"lx":105,"ly":152,"mx":84,"my":152},{"lx":116,"ly":151,"mx":105,"my":152},{"lx":125,"ly":149,"mx":116,"my":151},{"lx":131,"ly":149,"mx":125,"my":149},{"lx":135,"ly":149,"mx":131,"my":149},{"lx":139,"ly":149,"mx":135,"my":149},{"lx":144,"ly":149,"mx":139,"my":149},{"lx":146,"ly":149,"mx":144,"my":149},{"lx":173,"ly":160,"mx":173,"my":159},{"lx":166,"ly":160,"mx":173,"my":160},{"lx":160,"ly":163,"mx":166,"my":160},{"lx":154,"ly":166,"mx":160,"my":163},{"lx":147,"ly":169,"mx":154,"my":166},{"lx":142,"ly":173,"mx":147,"my":169},{"lx":138,"ly":178,"mx":142,"my":173},{"lx":136,"ly":182,"mx":138,"my":178},{"lx":136,"ly":186,"mx":136,"my":182},{"lx":137,"ly":190,"mx":136,"my":186},{"lx":140,"ly":193,"mx":137,"my":190},{"lx":147,"ly":194,"mx":140,"my":193},{"lx":156,"ly":194,"mx":147,"my":194},{"lx":164,"ly":192,"mx":156,"my":194},{"lx":170,"ly":189,"mx":164,"my":192},{"lx":176,"ly":184,"mx":170,"my":189},{"lx":180,"ly":180,"mx":176,"my":184},{"lx":182,"ly":176,"mx":180,"my":180},{"lx":183,"ly":172,"mx":182,"my":176},{"lx":183,"ly":168,"mx":183,"my":172},{"lx":181,"ly":164,"mx":183,"my":168},{"lx":178,"ly":160,"mx":181,"my":164},{"lx":174,"ly":157,"mx":178,"my":160},{"lx":169,"ly":156,"mx":174,"my":157},{"lx":164,"ly":155,"mx":169,"my":156},{"lx":160,"ly":154,"mx":164,"my":155},{"lx":180,"ly":196,"mx":180,"my":195},{"lx":183,"ly":199,"mx":180,"my":196},{"lx":185,"ly":201,"mx":183,"my":199},{"lx":186,"ly":201,"mx":185,"my":201},{"lx":187,"ly":201,"mx":186,"my":201},{"lx":190,"ly":199,"mx":187,"my":201},{"lx":192,"ly":196,"mx":190,"my":199},{"lx":193,"ly":192,"mx":192,"my":196},{"lx":195,"ly":188,"mx":193,"my":192},{"lx":196,"ly":185,"mx":195,"my":188},{"lx":196,"ly":184,"mx":196,"my":185},{"lx":196,"ly":183,"mx":196,"my":184},{"lx":196,"ly":181,"mx":196,"my":183},{"lx":196,"ly":180,"mx":196,"my":181},{"lx":197,"ly":178,"mx":196,"my":180},{"lx":199,"ly":176,"mx":197,"my":178},{"lx":201,"ly":175,"mx":199,"my":176},{"lx":202,"ly":174,"mx":201,"my":175},{"lx":203,"ly":173,"mx":202,"my":174},{"lx":200,"ly":180,"mx":203,"my":173},{"lx":197,"ly":184,"mx":200,"my":180},{"lx":196,"ly":188,"mx":197,"my":184},{"lx":196,"ly":189,"mx":196,"my":188},{"lx":197,"ly":190,"mx":196,"my":189},{"lx":213,"ly":182,"mx":197,"my":190},{"lx":220,"ly":176,"mx":213,"my":182},{"lx":227,"ly":169,"mx":220,"my":176},{"lx":232,"ly":161,"mx":227,"my":169},{"lx":237,"ly":151,"mx":232,"my":161},{"lx":242,"ly":135,"mx":237,"my":151},{"lx":248,"ly":117,"mx":242,"my":135},{"lx":254,"ly":103,"mx":248,"my":117},{"lx":258,"ly":98,"mx":254,"my":103},{"lx":260,"ly":99,"mx":258,"my":98},{"lx":261,"ly":101,"mx":260,"my":99},{"lx":260,"ly":108,"mx":261,"my":101},{"lx":255,"ly":119,"mx":260,"my":108},{"lx":245,"ly":133,"mx":255,"my":119},{"lx":233,"ly":148,"mx":245,"my":133},{"lx":223,"ly":162,"mx":233,"my":148},{"lx":217,"ly":172,"mx":223,"my":162},{"lx":216,"ly":178,"mx":217,"my":172},{"lx":217,"ly":181,"mx":216,"my":178},{"lx":220,"ly":180,"mx":217,"my":181},{"lx":223,"ly":179,"mx":220,"my":180},{"lx":226,"ly":177,"mx":223,"my":179},{"lx":227,"ly":176,"mx":226,"my":177},{"lx":223,"ly":184,"mx":227,"my":176},{"lx":221,"ly":189,"mx":223,"my":184},{"lx":219,"ly":193,"mx":221,"my":189},{"lx":218,"ly":196,"mx":219,"my":193},{"lx":218,"ly":198,"mx":218,"my":196},{"lx":218,"ly":199,"mx":218,"my":198},{"lx":228,"ly":195,"mx":218,"my":199},{"lx":232,"ly":192,"mx":228,"my":195},{"lx":234,"ly":191,"mx":232,"my":192},{"lx":235,"ly":190,"mx":234,"my":191},{"lx":237,"ly":191,"mx":235,"my":190},{"lx":238,"ly":189,"mx":237,"my":191},{"lx":240,"ly":188,"mx":238,"my":189},{"lx":242,"ly":187,"mx":240,"my":188},{"lx":244,"ly":187,"mx":242,"my":187},{"lx":245,"ly":187,"mx":244,"my":187},{"lx":246,"ly":187,"mx":245,"my":187},{"lx":248,"ly":187,"mx":246,"my":187},{"lx":251,"ly":187,"mx":248,"my":187},{"lx":253,"ly":187,"mx":251,"my":187},{"lx":255,"ly":188,"mx":253,"my":187},{"lx":257,"ly":190,"mx":255,"my":188},{"lx":256,"ly":194,"mx":257,"my":190},{"lx":249,"ly":206,"mx":256,"my":194},{"lx":236,"ly":223,"mx":249,"my":206},{"lx":222,"ly":237,"mx":236,"my":223},{"lx":211,"ly":246,"mx":222,"my":237},{"lx":205,"ly":249,"mx":211,"my":246},{"lx":201,"ly":249,"mx":205,"my":249},{"lx":214,"ly":238,"mx":201,"my":249},{"lx":231,"ly":224,"mx":214,"my":238},{"lx":250,"ly":210,"mx":231,"my":224},{"lx":265,"ly":198,"mx":250,"my":210},{"lx":273,"ly":192,"mx":265,"my":198},{"lx":275,"ly":190,"mx":273,"my":192},{"lx":276,"ly":190,"mx":275,"my":190},{"lx":277,"ly":191,"mx":276,"my":190}];
          setTimeout(function () {

            var sigpad_options_5 = {
              drawOnly: true,
              displayOnly: true,
              bgColour: "#fff",
              penColour: "#000",
              output: "#element_5",
              lineTop: this.line_margin_top,
              lineMargin: 10,
              validateFields: false
            };
            var sigpad_data_5 = formsData.value;
            if (formsData.value)
              $("#mf_sigpad_" + formsData.element_id).signaturePad(sigpad_options_5).regenerate(sigpad_data_5);
          }, 1000);

          //this.signload(formsData.value)
        }
      });
      console.log("signature pad array",this.signpad_url_array);
        if (this.activeStrucuredForms.updatedtimestamp == null) {
          this.activeStrucuredForms.formattedSentOn = moment(
            (this.activeStrucuredForms.senton || this.activeStrucuredForms.submitted_on || this.activeStrucuredForms.created_on) * 1000
          ).format('MMMM DD, YYYY h:mm a');
        } else {
          this.activeStrucuredForms.formattedSentOn = moment(this.activeStrucuredForms.updatedtimestamp * 1000).format('MMMM DD, YYYY h:mm a');
        }
      /*logic start*/
    }).catch((ex) => {
      //this.loginFailed = true;
    });
    if (
      this.activeStrucuredForms.created_date &&
      this.activeStrucuredForms.formStatus === 'completed' &&
      this.userData.group !== 3 &&
      !this.formLandingFlow
    ) {
      this.showAllowEdit = true;
      this.showHistoryButton = true;
    }

    if (
      this.activeStrucuredForms.formStatus === 'completed' &&
      this.previlages.allowArchiveForms === true &&
      this.userData.group !== 3 &&
      !this.formLandingFlow
    ) {
      this.showArchive = true;
    }
    if (this.activeStrucuredForms.patient_id === 'undefined' || this.activeStrucuredForms.patient_id === undefined) {
      this.activeStrucuredForms.patient_id = this.activeStrucuredForms.staff_facing;
    }
    if (
      this.activeStrucuredForms.patientName === this.activeStrucuredForms.fromName ||
      this.activeStrucuredForms.patientName === this.activeStrucuredForms.createdUser
    ) {
      this.hidewithoutAssociation = false;
    }
    this.activeStrucuredForms.formattedSentByDate = moment(this.activeStrucuredForms.sentDate * 1000).format('MMMM DD, YYYY h:mm a');
  }
  keepActiveTab(activeTab){
    this._sharedService.viewFormBackActiveTab=activeTab;
  }
  editSubmittedForm() {
    var formId = this.activeStrucuredForms.form_id ? this.activeStrucuredForms.form_id : this.activeStrucuredForms.formId;
    this.router.navigate(['forms/edit-form/' + formId + '/' + this.activeStrucuredForms.form_submission_id]);
  }
  // getPdfTaggedForm() {
  //   console.log("View submitted list");
  //   var newWindow: any = window.open(this._structureService.serverBaseUrl + "/webapp/www/img/gif-loader.gif");
  //   var downloadTime = moment((moment().unix()) * 1000).format('MMMM DD, YYYY h:mm a');
  //   var patientIdForPdf = this.activeStrucuredForms.patient_id;
  //   if(typeof this.activeStrucuredForms.associated_user_id !== 'undefined' && this.activeStrucuredForms.associated_user_id !='')
  //   {
  //         patientIdForPdf = this.activeStrucuredForms.associated_user_id;
  //   }
  //   if(this.activeStrucuredForms.facing_new ==0 && this.activeStrucuredForms.patient_id ==0)
  //   {
  //         patientIdForPdf = this.activeStrucuredForms.from_id;
  //   }
  //   console.log("FORM ID ------- : ",this.activeStrucuredForms.from_id);
  //   console.log("SUBMISSION ID ------- : ",this.activeStrucuredForms.form_submission_id);
  //   var data = { "patient_id": this.activeStrucuredForms.patient_id,"patient_user": patientIdForPdf,"generate_send_id": this.activeStrucuredForms.sent_id, "userid": this.activeStrucuredForms.userid, "formId": (this.activeStrucuredForms.form_id || this.activeStrucuredForms.formId), "submissionId": this.activeStrucuredForms.form_submission_id, "tenantId": this.userData.tenantId, "tenantName": this.userData.tenantName, "createdOn": moment((this.activeStrucuredForms.senton || this.activeStrucuredForms.submitted_on) * 1000).format('MMMM DD, YYYY h:mm a'), "sendOn": moment((this.activeStrucuredForms.sentDate || this.activeStrucuredForms.sent) * 1000).format('MMMM DD, YYYY h:mm a'), "downloadTime": downloadTime, "createdOnNew": (this.activeStrucuredForms.senton || this.activeStrucuredForms.submitted_on) };//this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton) * 1000)
  //   this._formsService.generateTaggedFormReportPdf(data, 2, timezone.name()).then((result) => {
  //     var fileName: any = '';
  //     fileName = result;
  //     //var fileUrl = this._structureService.apiBaseUrl + '/writable/filetransfer/uploads/tagged-form-reports/' + fileName._body + '.pdf';
  //     var fileUrl  = this._structureService.apiBaseUrl +'/citus-health/v4/form-download.php?filetoken='+fileName._body;
               
  //     newWindow.location = fileUrl;
  //     //this.download(fileUrl,fileName);
  //   }).catch((ex) => {
  //     //this.loginFailed = true;
  //   });
  // }
  getPdfTaggedForm() {
    console.log("View submitted list");
  
    // Try to get the new formId and submissionId (you can set these in ngOnInit or wherever needed)
    const newFormId = this.formId;
    const newSubmissionId = this.submissionId;
  
    var newWindow: any = window.open(this._structureService.serverBaseUrl + "/webapp/www/img/gif-loader.gif");
    var downloadTime = moment((moment().unix()) * 1000).format('MMMM DD, YYYY h:mm a');
  
    var patientIdForPdf = this.activeStrucuredForms.patient_id;
  
    if (typeof this.activeStrucuredForms.associated_user_id !== 'undefined' && this.activeStrucuredForms.associated_user_id !== '') {
      patientIdForPdf = this.activeStrucuredForms.associated_user_id;
    }
  
    if (this.activeStrucuredForms.facing_new === 0 && this.activeStrucuredForms.patient_id === 0) {
      patientIdForPdf = this.activeStrucuredForms.from_id;
    }
  
    // Prefer new IDs if available, otherwise fallback to old ones
    const finalFormId = newFormId || this.activeStrucuredForms.form_id || this.activeStrucuredForms.formId;
    const finalSubmissionId = newSubmissionId || this.activeStrucuredForms.form_submission_id;
  
    console.log("FORM ID ------- : ", finalFormId);
    console.log("SUBMISSION ID ------- : ", finalSubmissionId);
  
    const data = {
      patient_id: this.activeStrucuredForms.patient_id,
      patient_user: patientIdForPdf,
      generate_send_id: this.activeStrucuredForms.sent_id,
      userid: this.activeStrucuredForms.userid,
      formId: finalFormId,
      submissionId: finalSubmissionId,
      tenantId: this.userData.tenantId,
      tenantName: this.userData.tenantName,
      createdOn: moment((this.activeStrucuredForms.senton || this.activeStrucuredForms.submitted_on) * 1000).format('MMMM DD, YYYY h:mm a'),
      sendOn: moment((this.activeStrucuredForms.sentDate || this.activeStrucuredForms.sent) * 1000).format('MMMM DD, YYYY h:mm a'),
      downloadTime: downloadTime,
      createdOnNew: (this.activeStrucuredForms.senton || this.activeStrucuredForms.submitted_on)
    };
  
    this._formsService.generateTaggedFormReportPdf(data, 2, timezone.name()).then((result) => {
      const fileName: any = result;
      const fileUrl = this._structureService.apiBaseUrl + '/citus-health/v4/form-download.php?filetoken=' + fileName._body;
      newWindow.location = fileUrl;
    }).catch((ex) => {
      console.error("PDF download failed", ex);
    });
  }
  
  
  
  openChatSignature() {
    this.childpad.show_popup();
  }
  sendSignature(sign) {
    if (sign.close == "true") {
      return false;
    }
    var baseSignUrl: any = '';
    var formData = localStorage.getItem('formData');
    console.log("formData", formData);
    var form: any;
    if (typeof (formData) != "undefined" && formData != '') {
      form = JSON.parse(decodeURIComponent(formData));
    } else {
      if (typeof this._structureService.getCookie('formData') == 'object') {
        form = this._structureService.getCookie('formData');
      } else {
        form = JSON.parse(decodeURIComponent(this._structureService.getCookie('formData')));
      }
    }

    var data = { "formId": form.formId, "id": form.submissionId, "flag": 1, "sign": sign.signature }
    this._formsService.uploadChatSignature(data, sign.signature, 'uploadSignatureStructuredForm').then((result) => {
      var baseSignUrl: any = result;
      this.signUrl = this._structureService.apiBaseUrl + "writable/filetransfer/uploads/" + baseSignUrl.content;
    }).catch((ex) => { });
  }
  listHistory() {
    this.showHistory = true;
  }
  closeHistoryModal(e){
    if(e == true) {
      this.showHistory = false;
    }
  }
  allowEdit() {
    let swal_options;
    if (this.activeStrucuredForms.formStatus == 'completed') {
      swal_options = {
        title: "Are you sure?",
        text: "You are about to grant editable permission for " + this.activeStrucuredForms.form_name,
        type: "input",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true,
        inputPlaceholder: "Enter your optional message",

      }
    }
    this._structureService.showAlertMessagePopup(
      swal_options).then(
        (confirm) => {
          if(confirm !== false) {
            this.allowEditConfirm(confirm);
          }
        });
  }
  allowEditConfirm(msg = '') {
    this.disableAllowEdit = true;
    this.activeStrucuredForms.loggedinuserid = this.userData.userId;
    this._formsService.allowEditForm(this.activeStrucuredForms, msg).then((result: any) => {
      this.activeStrucuredForms.allow_edit = result.allowedit;
      this._formsService.notifyAfterAllowEdit(this.activeStrucuredForms, result, 'viewPage', msg).then((data: any) => {
        let self = this;
        setTimeout(function () {
          self._location.back();
          self.keepActiveTab(self.activeTab);
        }, 1200);   
        this.disableAllowEdit = false;     
      });
    });
  }
  // archiveForm() {
  //   this.disableArchive = true;
  //   this._formsService.confirmArchiveForm(this.activeStrucuredForms.formStatus, this.activeStrucuredForms,data).then((data: any) => {
  //     if(data == true) {
  //       if (this.activeStrucuredForms.senton.toString().length == 13) {
  //         this.activeStrucuredForms.createdOn = this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton));
  //       } else {
  //         this.activeStrucuredForms.createdOn = this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton) * 1000);
  //       }
  //       this._formsService.archiveSubmitedForm('',this.activeStrucuredForms).then((result: any) => {
  //         if (result.status == '1') {
  //           let self = this;
  //           setTimeout(function () {
  //             self._location.back();
  //             self.keepActiveTab(self.activeTab);
  //           }, 1200);   
  //         } else {
  //           // On failure
  //           this.disableArchive = false;
  //         }
  //       }, () => {
  //         // On error / Promise rejected
  //         this.disableArchive = false;
  //       });
  //     } else {
  //       // Cancelled the archive
  //       this.disableArchive = false;
  //     }
  //   }, () => {
  //     // Promise rejected
  //     this.disableArchive = false;
  //   });
  // }

  archiveForm() {
    this.disableArchive = true;
  
    const data = {
      admissionId: this.activeStrucuredForms.admissionId
    };

    console.log("this.activeStrucuredForms",this.activeStrucuredForms);
  
    this._formsService.confirmArchiveForm(
      this.activeStrucuredForms.formStatus,
      this.activeStrucuredForms,
      data
    ).then((data: any) => {
      if (data == true) {
        if (this.activeStrucuredForms.senton.toString().length == 13) {
          this.activeStrucuredForms.createdOn = this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton));
        } else {
          this.activeStrucuredForms.createdOn = this.formpPipe.transform(parseInt(this.activeStrucuredForms.senton) * 1000);
        }
  
        this._formsService.archiveSubmitedForm('', this.activeStrucuredForms).then((result: any) => {
          if (result.status == '1') {
            let self = this;
            setTimeout(function () {
              self._location.back();
              self.keepActiveTab(self.activeTab);
            }, 1200);
          } else {
            this.disableArchive = false;
          }
        }, () => {
          this.disableArchive = false;
        });
      } else {
        this.disableArchive = false;
      }
    }, () => {
      this.disableArchive = false;
    });
  }
  
}

