import { Component, OnInit } from '@angular/core';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { StructureService } from '../structure.service';
import { SignService } from '../signatures/sign.service';
import { FormsService } from './forms.service';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { ToolTipService } from '../../structure/tool-tip.service';
import { GlobalDataShareService } from '../../structure/shared/global-data-share.service';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;
@Component({
  selector: 'app-add-tag-definition',
  templateUrl: './copy-form.component.html'
})

export class CopyFormDefinitionComponent implements OnInit {
  visibleToRoles:any;
  visibleToUsers:any;
  tagForm;
  teanntRoles;
  mobile:any;
  desktop:any;
  tagList:any;
  patientAssociateRole;
  selectedvisibleToRoles;
  selectednotifyOnSubmit; 
  organizationSwitchPrivilege = false;
  selectedformType;
  selectedRecipientRoles;
  title="Move Form";
  btnLabel = "Submit";
  allowRecipientRoles: any = '';
   userDataConfig: any;
    enableMoveForm;
  mobileEnable = false;
  patientEnable = false;
  externalFileExchangeEnable = false;
  progressNoteIntegrationEnable = false;
  stafffillEnable = false;
  nameOfBurgerMenu;
  tenantForms: any;
  patternOfNameForPatients;
  patternOfNameForStaff;
  nameOfSendMenu;
  selectedTenant:any;
  nameOfManagementMenu;
  userData;
  crossTenantOptions;
  formTypesArr = [];
 
  formTypeString;
  deletedFormTypesArr=[];
  updatedFormTypesArr=[];
  desktopEnable = false;
  activeStrucuredForms;

  constructor(
    private _formBuild: FormBuilder,
    private _signService: SignService,
    private _structureService : StructureService,
    private route: ActivatedRoute,
     private _formsService: FormsService,
    private router: Router,
    public _GlobalDataShareService:GlobalDataShareService,
    private _ToolTipService : ToolTipService) { }
    

  ngOnInit() {
      
     
    /*this._formsService.getalltenants().then((result)=>{
        console.log("===============================");
        console.log("===============================");
        console.log("===============================");
        console.log("===============================");
        console.log(result);
        console.log("===============================");
        console.log("===============================");
        console.log("===============================");
        console.log("===============================");
        this.crossTenantOptions =result; 
        
    });*/
    
    this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
    console.log('this.userDataConfig'+this._structureService.userDataConfig);
    var selectedTenant= this._GlobalDataShareService.getselectedTenantDetails().id;
    this.enableMoveForm = this.userDataConfig.enable_move_form;
      this.userData = this._structureService.getUserdata();
      this.crossTenantOptions =this.userData.crossTenantsDetails; 
      this.tenantForms=[];
      this._structureService.getTenantAllForms(selectedTenant).then((data) => {
        this.tenantForms=data;
        console.log("tenant forms list===>"+this.tenantForms);
      });
      
    $('.select2').select2({
      placeholder: "Select"
    });

    this.tagForm = this._formBuild.group({
      FormID: ['', Validators.required],
      TeanantID: ['', Validators.required],
    
    });
    this.mobile = false;
    this.desktop = false;


       
  }

 errorReset(){
    $("#error-FormID").html("");
    $("#error-formType").html("");
    
  }

  updateShortcut(f) {
    this.errorReset();


    let validate = this.validateFrom();

    if (validate) {
        var data={formId: this.tagForm.value['FormID'],tenantID: this.tagForm.value['TeanantID']};
        var formid = this.tagForm.value['FormID'];
        var tenantID =this.tagForm.value['TeanantID'];
       console.log("===============================");
        console.log(data);
        console.log("===============================");
        this._formsService.copyform(data).then((result: any) => {
          if(result.status){
            var notify = $.notify('Success! Form Moved');
            setTimeout(()=>{
              notify.update({'type': 'success', 'message': '<strong>Form Moved</strong>'});
                this.router.navigate(['/forms/manage']);
            }, 1000);
            var activityData = {
              activityName: "Form Moved",
              activityType: "forms move ",
              activityDescription: this.userData.displayName + ' has moved the form ' + this.activeStrucuredForms.form_name + '-' + '(Form ID - ' + formid + ')' + 'to' + this.userData.crossTenantOptions.tenantName + ' - ' + ' (TenantID - ' +tenantID + ' ) '
            };
            this._structureService.trackActivity(activityData);
          }
else{
              var notify = $.notify('Error! Invalid Form ID');
            setTimeout(()=>{
              //  notify.update({'type': 'success', 'message': '<strong>Form Moved</strong>'});
               
            }, 1000);
            var activityData = {
              activityName: "Form Moving Falied",
              activityType: "forms",
              activityDescription: this.userData.displayName + ' has failed to move the form -' + formid + ' tenantID - ' +tenantID
            };
            this._structureService.trackActivity(activityData);
}

        })
      
    }
  }
cancelShortcut(){
   this.router.navigate(['/forms/manage']);
}

 validateFrom() {  
 
      let valid=true;
        
       if(this.tagForm.controls.FormID.errors && this.tagForm.controls.FormID.errors.required==true){
     
            this.errorSet("FormID","Form is required");     
            valid=false;    
         }         
          if($("#TeanantID").val()==""){ 
            console.log("===============xxxx===============");
            console.log("===============================");
            console.log($("#TeanantID").val());
            console.log("===============================");
            console.log("===============================");
            this.errorSet("TeanantID","Teanant is required");  
             valid=false;            
         }else{
            this.errorSet("TeanantID","");  
         } 
      

    return valid;
  }

 errorSet(id,message){
    $("#error-"+id).html("").html(message);
  }

}
