import { Component, OnInit } from '@angular/core';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { StructureService } from '../structure.service';
import { SignService } from '../signatures/sign.service';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { ToolTipService } from '../../structure/tool-tip.service';
import { isNull } from "util";
import { CONSTANTS } from 'app/constants/constants';

declare const $: any;
@Component({
  selector: 'app-add-tag-definition',
  templateUrl: './add-tag-definition.component.html'
})
// TODO: Need to Revamp the entire component. Contains lots of duplicate codes, jquery functions etc.

export class AddTagDefinitionComponent implements OnInit {
 
  visibleToRoles:any;
  visibleToUsers:any;
  tagForm;
  teanntRoles;
  patientAssociateRole;
  selectedvisibleToRoles;
  selectednotifyOnSubmit;
  metadataForTag:any;
  selectedRecipientRoles;
  patientEnable = false;
  populatePreviousSubmissionEnable=true;
  validateformdataEnable=true;


  confirmActionPrefillEnable=true;
  enableMendatoryFieldCheckingEnable=true;
  includeTenantNameInPDFHeaderEnable=true;
  includeFormTypesFHIRListEnable=false;

  includePageBreakInPDFEnable=false
  enableConfirmLinkCheckingEnable=true;
  progressNoteIntegrationEnable = false;
  progressNoteIntegrationSendEnable = false;
  progressNoteIntegrationReminderEnable = false;
  externalFileExchangeEnable = false;
  externalFileDisclosePHIEnable = false;
  externalFileExchangeWebhookEnable = false;
  directlinkpatientchartEnable=false
  sendformdatajsonEnable=false
  externalFileExchangeOnSubmitEnable = false;
  externalFileExchangeOnArchiveEnable = false;
  enableSaveDraftPatientEnable = true;
  enableSaveDraftStaffEnable = true;
  /**Multisite Enabled **/
  multiSiteEnable: boolean;
  directlinkpatientchartTriggerOn;
  externalFileDisclosePHITriggerOn;
  progressNoteIntegrationTriggerOn;
  faxQIntegrationTriggerOn;
  sendformdatajsonTriggerOn;

  faxQEnable =false;
  TriggerOnArchiveOrSubmit = [
    {
      key: "Archive", value: true,
    },
    {
      key: "Submit", value: false,
    }
  ];
  /**Multisite Enabled end **/
  currentTab = "tabGeneral";
  stafffillEnable=false;
  folderName;
  FCselected=false;
  folderNamesubmit;
  folderNamesubmitjson;
  folderNamesubmitexchangejson;
  folderNamesubmitarchivejson;
  folderNamesendreminder;
  showClose;
  showCloseSubmits;
  showClosearchivejson;
  showCloseexchangejson;
  showClossendreminder;
  sfilingCenterssubmitjson;
  userData;
  isFilingCenter=false;
  isDirectLink=false;
  SelDirctlink=false;
  SelFaxq=false;
  isProgressNoteIntegration=false;
  isExternalFileExchange=false;
  isExternalFileDisclosePHI=false;
  isdirectlinkpatientchart=false;
  issendformdatajson=false;
  Enable = false;
  documentsfromFilingCenterFolderOut;
  filingCentersOut = [];
  title = "Add Form Type";
  btnLabel = "Submit";
  fromFilingCenter;
  fromFilingCenterSubmit;
  fromFilingCenterexchangejson;
  fromFilingCenterjson;
  fromFilingCentersSubmitjson;
  subject;
  body;
  subjectOne;
  bodyOne;
  attachMessage = false;
  attachMessageOne = false;
  allowRecipientRoles:any='';
  triggerOnPHI:any='';
  practitionerFill = false;
  visibilityRoles = [];
  notifyRoles = [];
  nursingAgencyTags: any = [];
  nursingAgencyUserTagSelected: any = [];
  isNursingAgencyEnabled = 0;
  caregiverOrAlternateArgs = 'Alternate Contact';
  hideExternalAppltnDiv =false;
  enableThirdPartyDiv=false;
  isEnableApiBasedIntegration = false;
  authorizationKey;
  apiEndPoint;
  noteTypes: any = [];
  showLoader: boolean;
  selectedCatValue;
  selectedCatName;
  selectedMessageValue;
  isEnableApiBasedIntegrationCategory= false;
  isEnableApiBasedIntegrationType: boolean;
  documentTypeSet;
  enableDocType: boolean;
  selectedTypeValue: any;
  selectedTypeName: any;
  selectedNoteTypeValue: any;
  externalIntegrationSettings: any;
  noteTypesCount: any;
  isEnableMessageType = false;
  isEnableMessageTypeMsg = false;
  isEnableExternalCategory = false;
  msgTagTypes: any;
	msgTagTypeCount: any;
	msgTypeSet: any;
  messageTypes: any = [];
  catLabel;
  typeLabel;
  isEnableMessageCategory= false;
  docLabel;
  enableSubData = false;
  noteCount: any = 0;
	msgCount: any ;
  eSignature: false;
  dateFormatOptions = CONSTANTS.dateFormatOptions;
  
  TriggerOnProgressNoteIntegration = [
    {
      key:'None', value: ''
    },
    {
      key: 'Archive', value: 'archive',
    },
    {
      key: 'Submit', value: 'submit',
    }
  ];
  showSendCompletedForm = false; 
  constructor(
    private _formBuild: FormBuilder,
    private _signService: SignService,
    private _structureService : StructureService,
    private route: ActivatedRoute,
    private router: Router,
    private _ToolTipService : ToolTipService) { }

  ngOnInit() {
    this.userData = this._structureService.getUserdata();
    if(this.userData && this.userData.config && this.userData.config.default_patients_workflow && this.userData.config.default_patients_workflow == "alternate_contacts")
    this.caregiverOrAlternateArgs = "Caregiver";
    /** 
    * Get User tags with tag type (Nursing Agency) BioMatrix -  Nursing tags.
    */
    this.multiSiteEnable = this.userData.config.enable_multisite == 1 ? true : false;
    this.isNursingAgencyEnabled = this.userData.config.enable_nursing_agencies_visibility_restrictions;
    this.isEnableApiBasedIntegrationCategory = (this.userData.config.enable_API_based_integration_for_message_category == 1) ? true:false;
    this.isEnableApiBasedIntegrationType = (this.userData.config.enable_API_based_integration_for_message_type == 1) ? true:false;
    if(this.isEnableApiBasedIntegrationType){
      this.catLabel = "External Note Category"
      this.typeLabel = "External Note Type Id"
    }else{
      this.catLabel = "External Note Category Name"
      this.typeLabel = "External Note Type Name"
    }
    this.isEnableApiBasedIntegration = (this.userData.config.enable_API_based_integration_for_category_code == 1) ? true:false;
    if(this.isEnableApiBasedIntegration) {
      this.docLabel = "External Document Type";
    } else {
      this.docLabel = "External Document Type Name";
    }
    this.isEnableExternalCategory = (this.userData.config.enable_external_cateogry_code_in_form_types == 1) ? true:false;
    this.isEnableMessageType = (this.userData.config.enable_external_message_typeId_in_form_types == 1) ? true:false;

    this.isEnableMessageCategory = (this.userData.config.enable_external_message_category_code_in_message_tag == 1) ? true:false;
	  this.isEnableMessageTypeMsg = (this.userData.config.enable_external_message_type_id_in_message_tag == 1) ? true:false;	

    this.apiEndPoint = this.userData.config.api_integration_end_point;
    this.authorizationKey = this.userData.config.api_integration_authorization_key;

    var page = 'form-tag-definitions';
    $(".visible-role").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00002') });
    $(".notify-on-submit").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00003') });
    $(".staff-facing").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00004') });
    $(".enrolment").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00005') });
    $(".user-type-tt").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00006') });
    $(".user-type-tttt").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00009') });
    $(".user-type-medatory").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00020') });
    
    $(".user-type-includeTenantNameInPDFHeader").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00033') });
    $(".user-type-includePageBreakInPDF").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00040') });
    $('.form-type-enable-esignature-audit').tooltip({ title:  this._ToolTipService.getToolTip(page,'ENABLE_AUDIT_LOG') });
    $('.enable-copy-completed-form').tooltip({ title:  this._ToolTipService.getToolTip(page,'ENABLE_SEND_COMPLETED_FORM')});
    $(".user-type-confirm").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00022') });
    $(".direct-linking").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00008') });
    $(".send-form-data-json").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00021') });
    $(".external-file-exchange").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00023') });
    $(".progress-note-integration").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00024') });
    $(".enable-faxqueue").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00025') });
    $(".enable-disclose-phi").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00026') });
    $(".enable-send-party").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00027') });
    $(".external-application").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00028') });
    $(".external-note-category-id").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00029') });
    $(".external-note-type-id-name").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00030') });
    $(".external-document-type-id").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00038') });
    if(this.isEnableApiBasedIntegrationCategory && this.isEnableApiBasedIntegrationType){
      $(".external-note-category").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00037') });
      $(".external-note-type-id").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00030') });
    } else {
      $(".external-note-category").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00034') });
      $(".external-note-type-id").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00035') });
    }

    if(this.isEnableApiBasedIntegration) {
      $(".external-document-type").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00031') });
    } else {
      $(".external-document-type").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00036') });
    }
    
    $(".validate-form-data").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00032') });
    $(".user-type-include_in_the_form_types_fhir_list_api").tooltip({ title:  this._ToolTipService.getToolTip(page,'FORM00039') });
   
    $('#sfilingCenterss').on('change', (e) => {
      this.showClose=true;
      this.FCselected=true;
      if($('#sfilingCenterss').val()==""&&$('#sfilingCenterssubmit').val()==""){
        this.FCselected=false;
      }
      this.folderName = {folderName:$('#sfilingCenterss').val(),type:'OUTGOING'}        
    });

    $('#sfilingCenterssubmit').on('change', (e) => {
        this.showCloseSubmits=true;
        this.FCselected=true;
        if($('#sfilingCenterss').val()==""&&$('#sfilingCenterssubmit').val()==""){
          this.FCselected=false;
        }
        this.folderNamesubmit = {folderName:$('#sfilingCenterssubmit').val(),type:'OUTGOING'}        
    });

    $('#sfilingCenterssjson').on('change', (e) => {
        this.showClosearchivejson=true;
        this.folderNamesubmitarchivejson = {folderName:$('#sfilingCenterssjson').val(),type:'OUTGOING'}        
    });
    $('#sfilingCenterssjsondataexchange').on('change', (e) => {
      this.showCloseexchangejson=true;
      this.folderNamesubmitexchangejson = {folderName:$('#sfilingCenterssjsondataexchange').val(),type:'OUTGOING'}        
  });
    
    

    $('#sfilingCenterssendreminder').on(
      'change',
      (e) => {
        this.showClossendreminder=true;
        this.folderNamesendreminder = {folderName:$('#sfilingCenterssendreminder').val(),type:'OUTGOING'}        
      }
    );

    $('#sfilingCenterssubmitjson').on(
      'change',
      (e) => {
        this.sfilingCenterssubmitjson=true;
        this.folderNamesubmitjson = {folderName:$('#sfilingCenterssubmitjson').val(),type:'OUTGOING'}        
    });    
 

    $('.select2').select2({
      placeholder: "Select"
    });
    if(this.userData.config.progress_note_integration_mode !== 'webhook'){
      if(this.multiSiteEnable){
        this.hideExternalAppltnDiv=true;
      }
    
    }
    if(this.userData.config.progress_note_integration_mode !== 'webhook'){
      if(!this.multiSiteEnable){
        this.enableThirdPartyDiv=true;
      }
    }else{
      this.enableThirdPartyDiv=true;
    }

    this.tagForm = this._formBuild.group({
      formTagName: ['', Validators.required],
      visibleToRoles: [''],
      patientAssociation: [false],
      populatePreviousSubmission :[false],
      validateformdata :[false],
      confirmActionPrefill:[false],
      enableMendatoryFieldChecking:[false],
      includeTenantNameInPDFHeader:[false],
      includeFormTypesFHIRList:[false],
      includePageBreakInPDF:[false],
      enableConfirmLinkChecking:[false],
      progressNoteIntegration: [false],
      progressNoteIntegrationSend: [false],
      progressNoteIntegrationReminder: [false],
      externalFileExchange: [false],
      externalFileDisclosePHI : [false],
      directlinkpatientchart :[false],
      sendformdatajson:[false],
      externalFileExchangeWebhook: [false],
       externalFileExchangeOnSubmit: [false],
      externalFileExchangeOnArchive: [false],
      enableSaveDraftPatient: [false],
      enableSaveDraftStaff: [false],
      eSignature: [false],
      stafffill:[false],
      notifyOnSubmit: [''],
      nursingAgencyUserTag: "",
      recipientRoles: ['', Validators.required],
      allowRecipientRoles: ['', Validators.required],
      triggerOnPHI:[],
      fromFilingCenter:[],
      fromFilingCenterSubmit:[],
      fromFilingCenterjson:[],
      fromFilingCenterexchangejson:[],
      fromFilingCentersSubmitjson:[],
      isEnrolment: [false],
      subject: [''],
      body: [''],
      subjectOne: [''],
      bodyOne: [''],
      attachMessage: [false],
      attachMessageOne: [false],
      enableApplessWorkflow:[false],
      applessDevices: [''],
      FaxQueue:[false],
      sendCompletedForm:[false],
      externalNoteCategoryId: [''],
      externalNoteCategory: [''],
      externalNoteTypeId: [''],
      externalNoteType: [''],
      externalDocumentType: [''],
      dosDateFormat: ['m-d-Y'],
      externalDocumentTypeId: [''],
      enableConfirmationPopupOnSend:[true],
      enableConfirmationPopupOnSubmit:[true],
    });

    $('#recipientRoles').on('change', (e) => {
        var m = $(e.target).val();
        
        var selectedRecipientRoles= [];
        this.selectedRecipientRoles = m;
        if(this.selectedRecipientRoles) {
          this.selectedRecipientRoles.forEach(element => {
              var member  = { id:""};
              var id=element.substr(element.indexOf(":") + 1);
              id = id.replace(/'/g, "");
              member.id = id.replace(/\s/g, '');                
                selectedRecipientRoles.push(Number(member.id));
              });
         }
        this.tagForm.patchValue({
          recipientRoles: selectedRecipientRoles.map(String)
        });         
    });   
    
    if(this.userData.config.enable_filing_center=="1") {
      this.isFilingCenter= true;
    } else {
      this.isFilingCenter= false;
    } 
    
    if(this.userData.config.enable_direct_link=="1") {
      this.isDirectLink= true;
    } else {
      this.isDirectLink= false;
    }
    if(this.userData.config.progress_note_integration_data_format=="json") {      
      this.isProgressNoteIntegration= true;
    }else{      
      this.isProgressNoteIntegration= false;
    }

    if( this.isFilingCenter){
      var typess="OUTGOING";
      this._structureService.getTenantFilingCenterFolders(typess).then((data) => {
          this.documentsfromFilingCenterFolderOut = data['getTenantFilingCenterFolders'];
            
          for (let i = 0; i < this.documentsfromFilingCenterFolderOut.length; i++) {
            var fname = this.documentsfromFilingCenterFolderOut[i].folderName;
            var ftype = this.documentsfromFilingCenterFolderOut[i].type;
            var item = {
              id: fname,
              text: fname
            }
            this.filingCentersOut.push(item);
          }

          $('#sfilingCenterss').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
            data: this.filingCentersOut
          });

          $('#sfilingCenterssubmit').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
            data:this.filingCentersOut
          });

          $('#sfilingCenterssjson').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
            data:this.filingCentersOut
          });
          $('#sfilingCenterssjsondataexchange').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
            data:this.filingCentersOut
        });
        $('#sfilingCenterssendreminder').select2({
          allowClear: true,
          placeholder: 'Select Outgoing Filing Center',
          data:this.filingCentersOut
      	});
        $('#sfilingCenterssubmitjson').select2({
          allowClear: true,
          placeholder: 'Select Outgoing Filing Center',
          data:this.filingCentersOut
        });
      });      
    }

    if(this._structureService.getCookie('formTagId')){
      this.title = "Edit Form Type";
      this.btnLabel = "Update";
      var form = this._formBuild;
      this._structureService.getFormTagDetails(this._structureService.getCookie('formTagId')).then((result) => {    
        
        let res= result['getSessionTenant'].formTags[0];
        let metaTag = JSON.parse(res.tagMeta);
        this.metadataForTag=metaTag;
        let visibleToRoles = [];
        let notifyOnSubmit = [];
        let recipientRoles = [];
        let nursingAgencyUserTagSelected = [];
        let patientAssociation = false;
        let populatePreviousSubmission=false;
        let validateformdata=true;
        let confirmActionPrefill=true;
        let enableMendatoryFieldChecking=true;
        let includeTenantNameInPDFHeader =true;
        let includeFormTypesFHIRList=false;
        let includePageBreakInPDF=false;
        let enableConfirmLinkChecking=true;
        let externalFileExchange = false;
        let externalFileExchangeWebhook = false;
        let externalFileExchangeOnSubmit = false;
        let externalFileExchangeOnArchive = false;
        if(metaTag.defaultFromFilingCenter !=""|| metaTag.defaultFromFilingCenterSubmit!=""){
          this.FCselected=true;
        }

        let externalFileDisclosePHI= false;
        let directlinkpatientchart=false;
        let sendformdatajson=false;
        let enableSaveDraftPatient = true;
        let enableSaveDraftStaff = true;
        let progressNoteIntegration= false;
        let progressNoteIntegrationSend= false;
        let progressNoteIntegrationReminder= false;
        let FaxQueue=false;
        let stafffill =false;
        let attachMessage =false;
        let subject ;
        let body ;
        let subjectOne ;
        let bodyOne ;
        let attachMessageOne =false;
        let externalNoteCategory;
        let externalNoteCategoryId;
        let externalNoteTypeId;
        let externalNoteType;
        let externalIntegrationSettings = [];
        let externalDocumentType;
        let externalDocumentTypeId;

        if(metaTag.attachMessage == false) {
          metaTag.attachMessage = false;
        } else {
          metaTag.attachMessage = true;
        }

        if(metaTag.attachMessageOne == false) {
          metaTag.attachMessageOne = false;
        } else {
          metaTag.attachMessageOne = true;
        }

        if(metaTag.enableSaveDraftPatient == false) {
          metaTag.enableSaveDraftPatient = false;
        } else {
          metaTag.enableSaveDraftPatient = true;
        }

        if(metaTag.enableSaveDraftStaff == false) {
          metaTag.enableSaveDraftStaff  = false;
        } else {
          metaTag.enableSaveDraftStaff = true;
        }
        if(metaTag.confirmActionPrefill == false){
          metaTag.confirmActionPrefill=false;
        }else{
          metaTag.confirmActionPrefill=true;
        }
        if((metaTag.enableMendatoryFieldChecking  && metaTag.enableMendatoryFieldChecking == true) || !("enableMendatoryFieldChecking" in metaTag)){
          metaTag.enableMendatoryFieldChecking=true;
        }else{
          metaTag.enableMendatoryFieldChecking=false;
        }
        if((metaTag.includeTenantNameInPDFHeader  && metaTag.includeTenantNameInPDFHeader == true) || !("includeTenantNameInPDFHeader" in metaTag)){
          metaTag.includeTenantNameInPDFHeader=true;
        }else{
          metaTag.includeTenantNameInPDFHeader=false;
        }
        
        if((metaTag.includeFormTypesFHIRList  && metaTag.includeFormTypesFHIRList == true) ){
          metaTag.includeFormTypesFHIRList=true;
        }else{
          metaTag.includeFormTypesFHIRList=false;
        }

        if((metaTag.includePageBreakInPDF  && metaTag.includePageBreakInPDF == true) ){
          metaTag.includePageBreakInPDF=true;
        }else{
          metaTag.includePageBreakInPDF=false;
        }
        
        if((metaTag.enableConfirmLinkChecking  && metaTag.enableConfirmLinkChecking == true) || !("enableConfirmLinkChecking" in metaTag)){
          metaTag.enableConfirmLinkChecking=true;
        }else{
          metaTag.enableConfirmLinkChecking=false;
        }
        if((metaTag.validateformdata  && metaTag.validateformdata == true) || !("validateformdata" in metaTag)){
          metaTag.validateformdata=true;
        }else{
          metaTag.validateformdata=false;
        }


        if (res.tagMeta) {
          visibleToRoles = metaTag.visibleToRoles;
          notifyOnSubmit = metaTag.notifyOnSubmit;
          recipientRoles = metaTag.recipientRoles;
          patientAssociation = metaTag.patientAssociation;
          populatePreviousSubmission = metaTag.populatePreviousSubmission;
          validateformdata=metaTag.validateformdata;
          confirmActionPrefill=metaTag.confirmActionPrefill;
          enableMendatoryFieldChecking=metaTag.enableMendatoryFieldChecking;
          includeTenantNameInPDFHeader= metaTag.includeTenantNameInPDFHeader;
          includeFormTypesFHIRList=metaTag.includeFormTypesFHIRList;
          includePageBreakInPDF= metaTag.includePageBreakInPDF;
          enableConfirmLinkChecking=metaTag.enableConfirmLinkChecking;
          externalFileExchange= metaTag.externalFileExchange;
          externalFileDisclosePHI = metaTag.externalFileDisclosePHI;
          directlinkpatientchart=  metaTag.directlinkpatientchart;
          sendformdatajson=metaTag.sendformdatajson;
            externalFileExchangeWebhook = metaTag.externalFileExchangeWebhook;
            externalFileExchangeOnSubmit = metaTag.externalFileExchangeOnSubmit;
            externalFileExchangeOnArchive = metaTag.externalFileExchangeOnArchive;

          enableSaveDraftPatient= metaTag.enableSaveDraftPatient;
          enableSaveDraftStaff= metaTag.enableSaveDraftStaff;
          progressNoteIntegration = metaTag.progressNoteIntegration;
          progressNoteIntegrationSend = metaTag.progressNoteIntegrationSend;
          progressNoteIntegrationReminder = metaTag.progressNoteIntegrationReminder;
          stafffill =metaTag.stafffill;        
          subject = metaTag.subject;
          body = metaTag.body;
          subjectOne = metaTag.subjectOne;
          bodyOne = metaTag.bodyOne;
          attachMessage = metaTag.attachMessage;
          attachMessageOne = metaTag.attachMessageOne;    
          nursingAgencyUserTagSelected = [];

          FaxQueue=metaTag.FaxQIntegration;
          externalNoteCategory = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagCategoryName: "";
          externalNoteCategoryId = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagCategoryId: "";
          externalNoteTypeId = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagTypeName: "";
          externalNoteType = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagTypeId: "";
          if((this.isEnableApiBasedIntegrationCategory && this.isEnableApiBasedIntegrationType) || this.isEnableApiBasedIntegrationType ){
            externalNoteCategory = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagCategoryName: "";
            this.selectedMessageValue = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagCategoryId: "";
            externalNoteTypeId = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagTypeId: "";
            this.selectedNoteTypeValue = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagTypeId: "";
            externalNoteTypeId = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagTypeName: "";
            this.tagForm.patchValue({
              externalNoteTypeId: this.selectedNoteTypeValue,
              externalNoteCategoryId: this.selectedCatValue
            });
          }
          if(this.isEnableMessageType && !this.isEnableApiBasedIntegrationType){      
            externalNoteCategory = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagCategoryName: "";
            externalNoteCategoryId = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagCategoryId: "";
            externalNoteTypeId = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagTypeName: "";
            externalNoteType = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagTypeId: "";
          }
          if(this.isEnableApiBasedIntegration == true){
            externalDocumentType = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.documentType: "";
            this.selectedTypeValue = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.documentTypeId: "";
          } else {
            externalDocumentType = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.documentType: "";
            externalDocumentTypeId = metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.documentTypeId: "";
          }  
          this.eSignature = this.metadataForTag.eSignature;    
        }

        this.practitionerFill = metaTag.practitionerFill;
        this.allowRecipientRoles = metaTag.staffFacing;
        this.triggerOnPHI = metaTag.triggerOnPHI;
        this.patientEnable = patientAssociation;
        this.populatePreviousSubmissionEnable=populatePreviousSubmission
        this.validateformdataEnable=validateformdata;
        this.confirmActionPrefillEnable=confirmActionPrefill;
        this.enableMendatoryFieldCheckingEnable=enableMendatoryFieldChecking;
        this.includeTenantNameInPDFHeaderEnable=includeTenantNameInPDFHeader;
        this.includeFormTypesFHIRListEnable=includeFormTypesFHIRList;
        this.includePageBreakInPDFEnable=includePageBreakInPDF;
        this.enableConfirmLinkCheckingEnable=enableConfirmLinkChecking;;
        this.externalFileExchangeEnable = externalFileExchange;
        this.externalFileDisclosePHIEnable = externalFileDisclosePHI;
        this.directlinkpatientchartEnable =directlinkpatientchart;
        this.sendformdatajsonEnable=sendformdatajson;
        this.externalFileExchangeWebhookEnable = externalFileExchangeWebhook;
        this.externalFileExchangeOnSubmitEnable = externalFileExchangeOnSubmit;
        this.externalFileExchangeOnArchiveEnable = externalFileExchangeOnArchive;

        this.enableSaveDraftPatientEnable = enableSaveDraftPatient;
        this.enableSaveDraftStaffEnable = enableSaveDraftStaff;
        this.progressNoteIntegrationEnable =progressNoteIntegration;
        this.progressNoteIntegrationSendEnable =progressNoteIntegrationSend;
        this.progressNoteIntegrationReminderEnable =progressNoteIntegrationReminder;
        this.attachMessage = attachMessage;
        this.attachMessageOne = attachMessageOne;
        this.stafffillEnable= stafffill;
        /**Multisite Enabled */
        this.directlinkpatientchartTriggerOn=(metaTag.directlinkpatientchartTriggerOn !=="")?(metaTag.directlinkpatientchartTriggerOn =="archive" ?true:false):true;
        this.externalFileDisclosePHITriggerOn =(metaTag.externalFileDisclosePHITriggerOn!=="")? (metaTag.externalFileDisclosePHITriggerOn=="archive" ?true:false):true;
        this.progressNoteIntegrationTriggerOn = metaTag.progressNoteIntegrationTriggerOn;
        this.sendformdatajsonTriggerOn = (metaTag.sendformdatajsonTriggerOn !=="") ? (metaTag.sendformdatajsonTriggerOn=="archive" ? true:false):true;
        this.faxQIntegrationTriggerOn =(metaTag.faxQIntegrationTriggerOn !=="")? (metaTag.faxQIntegrationTriggerOn=="archive" ?true:false):true;
        this.faxQEnable=FaxQueue;
        if(this.progressNoteIntegrationEnable === true && this.multiSiteEnable ){
          metaTag.defaultFromFilingCentersendreminder="";
          if( (this.progressNoteIntegrationSendEnable || this.progressNoteIntegrationReminderEnable)){
            metaTag.defaultFromFilingCentersendreminder=true;
          }
        }
        if (this.userData.config.progress_note_integration_mode === 'webhook' && progressNoteIntegration) {
          this.progressNoteIntegrationTriggerOn = externalFileExchangeOnSubmit ? 'submit' : externalFileExchangeOnArchive ? 'archive' : '';
        }
         /**Multisite Enabled */
        this.tagForm = this._formBuild.group({
          formTagName: [res.tagName, Validators.required],
          category:"",
          categoryType:"",
          visibleToRoles: [visibleToRoles.map(String)],
          notifyOnSubmit: notifyOnSubmit ? [notifyOnSubmit.map(String)] : [],
          recipientRoles: recipientRoles ? [recipientRoles.map(String)] : [],
          allowRecipientRoles: [metaTag.staffFacing, Validators.required],
          nursingAgencyUserTag: [],
          patientAssociation : patientAssociation,
          populatePreviousSubmission:populatePreviousSubmission,
          validateformdata:validateformdata,
          confirmActionPrefill:confirmActionPrefill,
          enableMendatoryFieldChecking:enableMendatoryFieldChecking,
          includeTenantNameInPDFHeader:includeTenantNameInPDFHeader,
          includeFormTypesFHIRList:includeFormTypesFHIRList,
          includePageBreakInPDF:includePageBreakInPDF,
          enableConfirmLinkChecking:enableConfirmLinkChecking,
          externalFileExchange :externalFileExchange,
          externalFileDisclosePHI:externalFileDisclosePHI,
          directlinkpatientchart:directlinkpatientchart,
          sendformdatajson:sendformdatajson,
          externalFileExchangeWebhook:externalFileExchangeWebhook,
          externalFileExchangeOnSubmit:externalFileExchangeOnSubmit,
          externalFileExchangeOnArchive:externalFileExchangeOnArchive, 
          enableSaveDraftPatient :enableSaveDraftPatient,
          enableSaveDraftStaff :enableSaveDraftStaff,
          eSignature: this.eSignature,
          progressNoteIntegration:progressNoteIntegration,
          progressNoteIntegrationSend:progressNoteIntegrationSend,
          progressNoteIntegrationReminder:progressNoteIntegrationReminder,
          stafffill:stafffill, 
          fromFilingCenter: metaTag.defaultFromFilingCenter,
          fromFilingCenterSubmit: metaTag.defaultFromFilingCenterSubmit,
          fromFilingCenterjson:metaTag.defaultFromFilingCenterarchivejson,
          fromFilingCenterexchangejson:metaTag.fromFilingCenterexchangejson,
          fromFilingCentersSubmitjson:metaTag.defaultFromFilingCenterSubmitjson,
          fromFilingCentersendreminder:metaTag.defaultFromFilingCentersendreminder,
          isEnrolment: (!metaTag.isEnrolmentForm || metaTag.isEnrolmentForm == 'false') ? [false] : [true],
          subject: metaTag.subject,
          body: metaTag.body,
          subjectOne: metaTag.subjectOne,
          bodyOne: metaTag.bodyOne,
          attachMessage: metaTag.attachMessage,
          attachMessageOne: metaTag.attachMessageOne,
          enableApplessWorkflow:[false],
          applessDevices: [""],
          dosDateFormat:metaTag.dosDateFormat ? metaTag.dosDateFormat : 'm-d-Y',
          sendCompletedForm:metaTag.sendCompletedForm ? metaTag.sendCompletedForm : false,
          FaxQueue:metaTag.FaxQIntegration,
          externalNoteCategory: metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagCategoryName: "",
          externalNoteCategoryId: metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagCategoryId: "",
          externalNoteTypeId: metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagTypeName: "",
          externalNoteType: metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.messageTagTypeId: "",
          externalDocumentType: metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.documentType: "",
          externalDocumentTypeId: metaTag.externalIntegrationSettings ? metaTag.externalIntegrationSettings.documentTypeId: "",
          enableConfirmationPopupOnSend: metaTag && metaTag.hasOwnProperty('enableConfirmationPopupOnSend') ? metaTag.enableConfirmationPopupOnSend : true,
          enableConfirmationPopupOnSubmit: metaTag && metaTag.hasOwnProperty('enableConfirmationPopupOnSubmit') ? metaTag.enableConfirmationPopupOnSubmit : true
        });
        
        if(this.multiSiteEnable)
        {
          this.SelDirctlink =metaTag.FaxQIntegration;
          this.SelFaxq=directlinkpatientchart;
          if(this.SelDirctlink==true)
          { this.directlinkpatientchartChange(false); }
          if(this.SelFaxq==true)
          {  this.faxQChange(false); }
        }

        let fOut:any =metaTag.defaultFromFilingCenter?metaTag.defaultFromFilingCenter : "";
        let fOutSubmit: any = metaTag.defaultFromFilingCenterSubmit ? metaTag.defaultFromFilingCenterSubmit : "";
        let foutsendreminder:any =metaTag.defaultFromFilingCentersendreminder?metaTag.defaultFromFilingCentersendreminder : "";
        let fOutjsonarchive:any =metaTag.defaultFromFilingCenterarchivejson?metaTag.defaultFromFilingCenterarchivejson : "";
        let foutfromFilingCenterexchangejson:any = metaTag.fromFilingCenterexchangejson?metaTag.fromFilingCenterexchangejson:"";
        let fOutSubmitjson:any =metaTag.defaultFromFilingCenterSubmitjson?metaTag.defaultFromFilingCenterSubmitjson : "";   
        $('#sfilingCenterss').val(fOut).trigger('change');
        if(fOut==""){
        this.showClose=false;          
        }      

        $('#sfilingCenterssubmit').val(fOutSubmit).trigger('change');
        if (fOutSubmit == "") {
          this.showCloseSubmits = false;
        }
       
        $('#sfilingCenterssjson').val(fOutjsonarchive).trigger('change');
        if(fOutjsonarchive==""){          
          this.showClosearchivejson=false;
        }
            
        $('#sfilingCenterssjsondataexchange').val(foutfromFilingCenterexchangejson).trigger('change');
        if(foutfromFilingCenterexchangejson==""){          
          this.showCloseexchangejson=false;
        }
        
        $('#sfilingCenterssendreminder').val(foutsendreminder).trigger('change');
        if(foutsendreminder==""){          
          this.showClossendreminder=false;
        }

        $('#sfilingCenterssubmitjson').val(fOutSubmitjson).trigger('change');
        if(fOutSubmitjson==""){          
          this.sfilingCenterssubmitjson=false;
        }

        if ((metaTag.staffFacing == "true") ||(metaTag.staffFacing == "partner")) {
          this.tagForm.controls['recipientRoles'].setValidators(null);
          this.tagForm.controls['recipientRoles'].updateValueAndValidity();
        }else{      
          this.tagForm.controls['recipientRoles'].setValidators(Validators.required);
          this.tagForm.controls['recipientRoles'].updateValueAndValidity();
        }

        if(this.tagForm.value['enableApplessWorkflow']){
          this.tagForm.controls['applessDevices'].setValidators(Validators.required);
          this.tagForm.controls['applessDevices'].updateValueAndValidity();
        } else {
          this.tagForm.controls['applessDevices'].setValidators(null);
          this.tagForm.controls['applessDevices'].updateValueAndValidity();
        }
            
        setTimeout(() => {      
          $("#visibleToRoles").select2({});
          $("#notifyOnSubmit").select2({});     
          $("#recipientRoles").select2({});
        });
        this.showSendCompletedFormField();
      });
    } else {
      this.btnLabel = "Submit";
      this.title = "Add Form Type";
    }
    
      this._structureService.setCookie('cache-page', 'add-tag', 1, '');
      this._signService.getAllTenantRoles().then((data) => {
        this.teanntRoles = data;
        var patientAssociateRole;
        this.teanntRoles.filter(function(role){
          if(role.citus_role_id == '3'){
            patientAssociateRole = role.id;
          }
        });

        this.patientAssociateRole = patientAssociateRole;
        this._structureService.setCookie('cache-page', 'add-tag', -1);
      });
  }
  
  showDataTabs(current)
  {
    this.currentTab = current;
  }

  togglePreference(preference,value) {
    if (preference === 'enableApplessWorkflow'){
      this.tagForm.patchValue({
        enableApplessWorkflow:value
      });
      if(value) {
        this.tagForm.controls['applessDevices'].setValidators(Validators.required);
        this.tagForm.controls['applessDevices'].updateValueAndValidity();
      } else {
        this.tagForm.controls['applessDevices'].setValidators(null);
        this.tagForm.controls['applessDevices'].updateValueAndValidity();
      }
    }
  }
  
  chooseFacing(){
      this.allowRecipientRoles = $("#allowRecipientRoles").val();
      this.tagForm.patchValue({
        allowRecipientRoles:$("#allowRecipientRoles").val(),
        recipientRoles:[]
      });
      $("#recipientRoles").val($("#allowRecipientRoles").val()).trigger("change");
      
      if($("#allowRecipientRoles").val()=="true"||$("#allowRecipientRoles").val()=="partner" || $("#allowRecipientRoles").val()=="referral"||$("#allowRecipientRoles").val()=="staffsupplyCount"||$("#allowRecipientRoles").val()=="patientsupplyCount"){      
        this.tagForm.controls['recipientRoles'].setValidators(null);
        this.tagForm.controls['recipientRoles'].updateValueAndValidity();
      }else{      
        if($("#allowRecipientRoles").val()=="practitioner") {
          this.patientEnable = true;
          this.tagForm.patchValue({
            patientAssociation: true
          });
        }
        this.tagForm.controls['recipientRoles'].setValidators(Validators.required);
        this.tagForm.controls['recipientRoles'].updateValueAndValidity();
      }
      this.showSendCompletedFormField();
  }

  chooseEvent(){
      this.triggerOnPHI = $("#triggerOnPHI").val();
  }
  toggleMetaTag(formTag,value){
    if (formTag === 'isEnrolment') {
      this.tagForm.patchValue({
        isEnrolment:value
      });
    }
  }

  cancel(){
    this.router.navigate(['/forms/form-tags']);
  }

  clearFilingCenter(type=''){
    if(type =='submits'){
     
      this.folderNamesubmit ='';
      this.showCloseSubmits=false; 
      $('#sfilingCenterssubmit').val('');
      $('#sfilingCenterssubmit').select2({
        allowClear: false,
        placeholder: 'Select Outgoing Filing Center',
        data:this.filingCentersOut
    });
    if($('#sfilingCenterss').val()==""&&$('#sfilingCenterssubmit').val()==""){
      this.FCselected=false;
    }
    }else if(type =='archivejson'){
      this.folderNamesubmitarchivejson ='';
      this.showClosearchivejson=false; 
      $('#sfilingCenterssjson').val('');
      $('#sfilingCenterssjson').select2({
        allowClear: false,
        placeholder: 'Select Outgoing Filing Center',
        data:this.filingCentersOut
    });
    } else if(type =='exchangejson'){
      this.folderNamesubmitexchangejson ='';
      this.showClosearchivejson=false; 
      $('#sfilingCenterssjsondataexchange').val('');
      $('#sfilingCenterssjsondataexchange').select2({
        allowClear: false,
        placeholder: 'Select Outgoing Filing Center',
        data:this.filingCentersOut
    });
    }
    else if(type =='exchangejson'){
      this.folderNamesubmitexchangejson ='';
      this.showClosearchivejson=false; 
      $('#sfilingCenterssjsondataexchange').val('');
      $('#sfilingCenterssjsondataexchange').select2({
        allowClear: false,
        placeholder: 'Select Outgoing Filing Center',
        data:this.filingCentersOut
    });
    }
    else if(type =='sendreminder'){
      this.folderNamesendreminder ='';
      this.showClossendreminder=false; 
      $('#sfilingCenterssendreminder').val('');
      $('#sfilingCenterssendreminder').select2({
        allowClear: false,
        placeholder: 'Select Outgoing Filing Center',
        data:this.filingCentersOut
    });
    }
    
    
    else if(type =='submitsjson'){
      this.folderNamesubmitjson ='';
      this.sfilingCenterssubmitjson=false; 
      $('#sfilingCenterssubmitjson').val('');
      $('#sfilingCenterssubmitjson').select2({
        allowClear: false,
        placeholder: 'Select Outgoing Filing Center',
        data:this.filingCentersOut
    });
    } else {
      
      this.folderName ='';
      this.showClose=false;
    $('#sfilingCenterss').val('');
    $('#sfilingCenterss').select2({
      allowClear: false,
      placeholder: 'Select Outgoing Filing Center',
      data:this.filingCentersOut
  });
  if($('#sfilingCenterss').val()==""&&$('#sfilingCenterssubmit').val()==""){
    this.FCselected=false;
  }
}
  }
  attachMessageF(event)
  {
    this.attachMessage = event;
  }
  attachMessageOneF(event)
  {
    this.attachMessageOne = event;
  }
  patientAssociationChange(status) {
    if(!status)
    {
      if(this.userData.config.progress_note_integration_mode === 'webhook'){
          this.enableThirdPartyDiv=true;
      }
      this.progressNoteIntegrationEnable = false;
    }
    this.tagForm.value.patientAssociation = status;
    this.patientEnable = status;
    this.showSendCompletedFormField();
  }
  showSendCompletedFormField() {
    if(this.tagForm.value.allowRecipientRoles === 'true' && this.tagForm.value.patientAssociation || 
    this.tagForm.value.allowRecipientRoles === 'false' ||
      this.tagForm.value.allowRecipientRoles === 'practitioner' && this.tagForm.value.patientAssociation){
        this.showSendCompletedForm = true;
      } else {
        this.tagForm.patchValue({
          sendCompletedForm : false
        });
        this.showSendCompletedForm = false;
      }
  }
  populatePreviousSubmissionChange(status) {
    this.tagForm.value.populatePreviousSubmission = status;
    this.populatePreviousSubmissionEnable = status;
  }
  
  validateformdataChange(status) {
    this.tagForm.value.validateformdata = status;
    this.validateformdataEnable = status;
  }

  confirmActionPrefillChange(status) {
    this.tagForm.value.confirmActionPrefill = status;
    this.confirmActionPrefillEnable = status;
  }
  enableMendatoryFieldCheckingChange(status) {
    this.tagForm.value.enableMendatoryFieldChecking = status;
    this.enableMendatoryFieldCheckingEnable = status;
  }
  includeTenantNameInPDFHeaderChange(status) {
    this.tagForm.value.includeTenantNameInPDFHeader = status;
    this.includeTenantNameInPDFHeaderEnable = status;
  }
  /* Status change for the configuration - Include in the Form Types FHIR list API 
      Yes/No values will be update as per the selection
  */
  includeFormTypesFHIRListChange(status) {
    this.tagForm.value.includeFormTypesFHIRList = status;
    this.includeFormTypesFHIRListEnable = status;
  }
  /* Status change for the configuration - Include in the Form Types Page break include 
      Yes/No values will be update as per the selection
  */
  includePageBreakInPDFChange(status) {
    this.tagForm.value.includePageBreakInPDF = status;
    this.includePageBreakInPDFEnable = status;
  }

includePageBreakInPDF

  enableConfirmLinkCheckingEnableChange(status) {
    this.tagForm.value.enableConfirmLinkChecking = status;
    this.enableConfirmLinkCheckingEnable = status;
  }
  externalFileExchangeChange(status) {
    this.tagForm.value.externalFileExchange = status;
    this.externalFileExchangeEnable = status;
  }
  
  externalFileDisclosePHIChange(status) {
    this.tagForm.value.externalFileDisclosePHI = status;
    this.externalFileDisclosePHIEnable = status;
  }
  directlinkpatientchartChange(status) {
    this.tagForm.value.directlinkpatientchart = status;
    this.directlinkpatientchartEnable = status;
    this.SelFaxq=status;
    if(status==false && this.multiSiteEnable !== true ){
      this.sendformdatajsonEnable = status;
    }
  }

  faxQChange(status){
    this.tagForm.value.FaxQueue = status;
    this.faxQEnable = status;
    this.SelDirctlink=status;
  }
  sendformdatajsonChange(status) {
    this.tagForm.value.sendformdatajson = status;
    this.sendformdatajsonEnable = status;
  }
  externalFileExchangeWebhookChange(status) {
    this.tagForm.value.externalFileExchangeWebhook = status;
    this.externalFileExchangeWebhookEnable = status;
  }

  externalFileExchangeOnSubmitChange(status) {
    this.tagForm.value.externalFileExchangeOnSubmit = status;
    this.externalFileExchangeOnSubmitEnable = status;
  }

  externalFileExchangeOnArchiveChange(status) {
    this.tagForm.value.externalFileExchangeOnArchive = status;
    this.externalFileExchangeOnArchiveEnable = status;
  }

  enableSaveDraftStaffChange(status) {
    this.tagForm.value.enableSaveDraftStaff = status;
    this.enableSaveDraftStaffEnable = status;
  }
  enableSaveDraftPatientChange(status) {
    this.tagForm.value.enableSaveDraftPatient = status;
    this.enableSaveDraftPatientEnable = status;
  }
  progressNoteIntegrationChange(status) {
    if(this.userData.config.progress_note_integration_mode === 'webhook'){
      this.enableThirdPartyDiv=false;
      if(status === false){
        this.enableThirdPartyDiv=true;
      }
    }
    this.tagForm.value.progressNoteIntegration = status;
    this.progressNoteIntegrationEnable = status;
  }

  progressNoteIntegrationSendChange(status) {
    this.tagForm.value.progressNoteIntegrationSend = status;
    this.progressNoteIntegrationSendEnable = status;
  }
  progressNoteIntegrationReminderChange(status) {
    this.tagForm.value.progressNoteIntegrationReminder = status;
    this.progressNoteIntegrationReminderEnable = status;
  }

  practitionerFillChange(status) {
    this.practitionerFill = status;
  }

  stafffillChange(status) {   
    this.tagForm.value.stafffill = status;
    this.stafffillEnable = status;
  }
  
  updateTag(f) {   
    if (!f.valid || this.allowRecipientRoles == '') {
      this.currentTab = "tabGeneral";
      return false;
    }
    var selectednotifyOnSubmit= [];
    this.selectednotifyOnSubmit = $('#notifyOnSubmit').val();
    if(this.selectednotifyOnSubmit) {
      this.selectednotifyOnSubmit.forEach(element => {
          var member  = { id:""};
          var id=element.substr(element.indexOf(":") + 1);
          id = id.replace(/'/g, "");
          member.id = id.replace(/\s/g, '');
            selectednotifyOnSubmit.push(Number(member.id));
          });
     }

     var selectedvisibleToRoles= [];
     this.selectedvisibleToRoles = $('#visibleToRoles').val();
     if(this.selectedvisibleToRoles) {
       this.selectedvisibleToRoles.forEach(element => {
           var member  = { id:""};
           var id=element.substr(element.indexOf(":") + 1);
           id = id.replace(/'/g, "");
           member.id = id.replace(/\s/g, '');
             selectedvisibleToRoles.push(Number(member.id));
           });
      }
      var selectedRecipientRoles= [];
      this.selectedRecipientRoles = $('#recipientRoles').val();
      if(this.selectedRecipientRoles) {
        this.selectedRecipientRoles.forEach(element => {
            var member  = { id:""};
            var id=element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
              selectedRecipientRoles.push(Number(member.id));
            });
       }


    let selectedNursingTags = [];
    this.nursingAgencyUserTagSelected = $('#nursing-agency-user-tags').val();
    if (this.nursingAgencyUserTagSelected && this.nursingAgencyUserTagSelected !="") {
      selectedNursingTags.push(Number(this.nursingAgencyUserTagSelected));
    }

     const isAllowrecipientRoles= $("#allowRecipientRoles").val();
     const isPatientChartTriggerVal=$("#patientChartTrigger").val();
     const isPhiTriggerVal=$("#externalFileDisclosePHIEnableTrigger").val();
     const isProgressNoteIntegrationVal=$("#progressNoteIntegrationId").val();
     const isEnableThirdPartyVal=$("#formDataThirdpartyTriggerId").val();
     const isFaxQueVal=$("#faxQTriggerId").val();
     let activityValue;
     if(isAllowrecipientRoles == "true")
     {
      activityValue = "staff facing";
     }else if(isAllowrecipientRoles == "false")
     {
      activityValue = "patient facing";
     }
     else if(isAllowrecipientRoles == "practitioner" )
     {
      activityValue = "practitioner facing";
     }
     else if (isAllowrecipientRoles == "referral")
     {
      activityValue = "referral facing";
     }
     else
     {
      activityValue = "no data selected";
     }

     this.fromFilingCenter = this.folderName&&this.folderName.folderName;
     this.fromFilingCenterSubmit = this.folderNamesubmit&&this.folderNamesubmit.folderName;
     this.fromFilingCenterjson= this.folderNamesubmitarchivejson&&this.folderNamesubmitarchivejson.folderName;
     this.fromFilingCenterexchangejson= this.folderNamesubmitexchangejson&&this.folderNamesubmitexchangejson.folderName;
  
     this.fromFilingCentersSubmitjson= this.folderNamesubmitjson&&this.folderNamesubmitjson.folderName;
     this.folderNamesendreminder=this.folderNamesendreminder&&this.folderNamesendreminder.folderName;
     
      let categorySupply="";
       if(this.metadataForTag){
        if(this.metadataForTag.categoryType=="Supply"){
          if(isAllowrecipientRoles == 'true'){
              categorySupply="staffsupplyCount"
        }
        if(isAllowrecipientRoles == 'false'){
          categorySupply="patientsupplyCount"
          }
        }


       }
      const externalIntegrationSettings: {[key: string]: number} = {
        documentTypeId: this.isEnableApiBasedIntegration == true?this.selectedTypeValue: this.tagForm.value['externalDocumentTypeId'],
        documentType: this.isEnableApiBasedIntegration == true? this.tagForm.value['externalDocumentType']:this.tagForm.value['externalDocumentType'],
        messageTagCategoryId:(this.isEnableApiBasedIntegrationCategory == true && this.isEnableApiBasedIntegrationType == true) || this.isEnableApiBasedIntegrationType == true?this.selectedMessageValue:this.tagForm.value['externalNoteCategoryId'],
				messageTagCategoryName: (this.isEnableApiBasedIntegrationCategory == true && this.isEnableApiBasedIntegrationType == true) || this.isEnableApiBasedIntegrationType == true? this.tagForm.value['externalNoteCategory']:this.tagForm.value['externalNoteCategory'],
				messageTagTypeId: (this.isEnableApiBasedIntegrationCategory == true && this.isEnableApiBasedIntegrationType == true) || this.isEnableApiBasedIntegrationType == true ? this.selectedNoteTypeValue:this.tagForm.value['externalNoteType'],
				messageTagTypeName: (this.isEnableApiBasedIntegrationCategory == true && this.isEnableApiBasedIntegrationType == true) || this.isEnableApiBasedIntegrationType == true? this.tagForm.value['externalNoteTypeId']:this.tagForm.value['externalNoteTypeId'],
    }
      var tagMeta
      if(this.metadataForTag){
        if(this.progressNoteIntegrationEnable === true && this.multiSiteEnable ){
          this.folderNamesendreminder="";
          if( (this.progressNoteIntegrationSendEnable || this.progressNoteIntegrationReminderEnable)){
            this.folderNamesendreminder=true;
          }
        }
          tagMeta = {
          category:categorySupply,
          categoryType:this.metadataForTag.categoryType,
          visibleToRoles: selectedvisibleToRoles ? selectedvisibleToRoles:[],
          patientAssociation:(isAllowrecipientRoles == 'true') ? this.patientEnable : (isAllowrecipientRoles == 'practitioner') ? this.patientEnable :'' ,
          populatePreviousSubmission:this.populatePreviousSubmissionEnable,
          validateformdata:this.validateformdataEnable,
          confirmActionPrefill:this.confirmActionPrefillEnable,
          enableMendatoryFieldChecking:this.enableMendatoryFieldCheckingEnable,
          includeTenantNameInPDFHeader:this.includeTenantNameInPDFHeaderEnable,
          includeFormTypesFHIRList:this.includeFormTypesFHIRListEnable,
          includePageBreakInPDF:this.includePageBreakInPDFEnable,          
          enableConfirmLinkChecking:this.enableConfirmLinkCheckingEnable,
          progressNoteIntegration: this.progressNoteIntegrationEnable  ,
          triggerOnPHI:this.triggerOnPHI,
          progressNoteIntegrationSend: this.progressNoteIntegrationSendEnable  ,
          progressNoteIntegrationReminder: this.progressNoteIntegrationReminderEnable  ,
          externalFileExchange: this.externalFileExchangeEnable  ,
          externalFileDisclosePHI:this.externalFileDisclosePHIEnable,
          directlinkpatientchart:this.directlinkpatientchartEnable,
          sendformdatajson:this.sendformdatajsonEnable,
          externalFileExchangeWebhook: this.externalFileExchangeWebhookEnable,
          externalFileExchangeOnSubmit: this.externalFileExchangeOnSubmitEnable,
          externalFileExchangeOnArchive: this.externalFileExchangeOnArchiveEnable,
          enableSaveDraftPatient: this.enableSaveDraftPatientEnable,
          enableSaveDraftStaff: this.enableSaveDraftStaffEnable,
          eSignature: this.tagForm.value.eSignature,
          stafffill:(isAllowrecipientRoles != 'true' || isAllowrecipientRoles == 'practitioner') ? this.stafffillEnable :'' ,
          practitionerFill: this.practitionerFill,
          notifyOnSubmit: selectednotifyOnSubmit ? selectednotifyOnSubmit:[],
          recipientRoles: selectedRecipientRoles ? selectedRecipientRoles:[],
          nursingAgencyUserTagSelected: selectedNursingTags.length > 0 ? selectedNursingTags:[],
          staffFacing : isAllowrecipientRoles,
          isEnrolmentForm : this.tagForm.value['isEnrolment'].toString(),
          defaultFromFilingCenter :this.fromFilingCenter,
          defaultFromFilingCenterSubmit :this.fromFilingCenterSubmit,
          defaultFromFilingCenterarchivejson :this.fromFilingCenterjson,
          fromFilingCenterexchangejson:this.fromFilingCenterexchangejson,
          defaultFromFilingCenterSubmitjson :this.fromFilingCentersSubmitjson,
          defaultFromFilingCentersendreminder :this.folderNamesendreminder,
          subject: this.tagForm.value['subject'],
          body: this.tagForm.value['body'],
          subjectOne: this.tagForm.value['subjectOne'],
          bodyOne: this.tagForm.value['bodyOne'],
          attachMessage: this.attachMessage,
          attachMessageOne: this.attachMessageOne,
          enableApplessWorkflow: false,
          applessDevices:'',
          FaxQIntegration :this.faxQEnable,
          directlinkpatientchartTriggerOn:((this.userData.config.enable_multisite == 1) && (this.directlinkpatientchartEnable ==true)) ? ((isPatientChartTriggerVal =="true") ?"archive":"submit") :"",
          externalFileDisclosePHITriggerOn:((this.userData.config.enable_multisite == 1) &&  (this.externalFileDisclosePHIEnable ==true )) ? ((isPhiTriggerVal =="true")?"archive":"submit"):"",
          progressNoteIntegrationTriggerOn:((this.userData.config.enable_multisite == 1 ) && (this.progressNoteIntegrationEnable ==true )) ? isProgressNoteIntegrationVal:'',
          faxQIntegrationTriggerOn:((this.userData.config.enable_multisite == 1) && (this.faxQEnable == true )) ? ((isFaxQueVal == "true") ?"archive":"submit") :"",
          sendformdatajsonTriggerOn:((this.userData.config.enable_multisite == 1) && (this.sendformdatajsonEnable ==true ))? ((isEnableThirdPartyVal == "true")?"archive":"submit") :"",
          sendCompletedForm: this.tagForm.value['sendCompletedForm'],
          dosDateFormat:this.tagForm.value.dosDateFormat,
          externalNoteCategory: this.tagForm.value['externalNoteCategory'],
          externalNoteTypeId: this.tagForm.value['externalNoteTypeId'],
          externalNoteCategoryId: this.tagForm.value['externalNoteCategoryId'],
          externalNoteType: this.tagForm.value['externalNoteType'],
          externalIntegrationSettings: externalIntegrationSettings,
          enableConfirmationPopupOnSend: this.tagForm.value['enableConfirmationPopupOnSend'],
          enableConfirmationPopupOnSubmit: this.tagForm.value['enableConfirmationPopupOnSubmit'],
          }
      } else {
          tagMeta = {
          
          visibleToRoles: selectedvisibleToRoles ? selectedvisibleToRoles:[],
          patientAssociation:(isAllowrecipientRoles == 'true' || isAllowrecipientRoles == 'practitioner') ? this.patientEnable :'' ,
          externalFileExchange: this.externalFileExchangeEnable  ,
          populatePreviousSubmission:this.populatePreviousSubmissionEnable,
          dosDateFormat:this.tagForm.value.dosDateFormat,
          confirmActionPrefill:this.confirmActionPrefillEnable,
          enableMendatoryFieldChecking:this.enableMendatoryFieldCheckingEnable,
          includeTenantNameInPDFHeader:this.includeTenantNameInPDFHeaderEnable,
          includeFormTypesFHIRList:this.includeFormTypesFHIRListEnable,
          includePageBreakInPDF:this.includePageBreakInPDFEnable,          
          enableConfirmLinkChecking:this.enableConfirmLinkCheckingEnable,
          externalFileDisclosePHI:this.externalFileDisclosePHIEnable,
          directlinkpatientchart:this.directlinkpatientchartEnable,
          sendformdatajson:this.sendformdatajsonEnable,
          externalFileExchangeWebhook: this.externalFileExchangeWebhookEnable,
          externalFileExchangeOnSubmit: this.externalFileExchangeOnSubmitEnable,
          externalFileExchangeOnArchive: this.externalFileExchangeOnArchiveEnable,
          enableSaveDraftPatient: this.enableSaveDraftPatientEnable,
          enableSaveDraftStaff: this.enableSaveDraftStaffEnable,
          eSignature: this.tagForm.value.eSignature,
          progressNoteIntegration:this.progressNoteIntegrationEnable ,
          progressNoteIntegrationSend:this.progressNoteIntegrationSendEnable ,
          progressNoteIntegrationReminder:this.progressNoteIntegrationReminderEnable ,
          stafffill:(isAllowrecipientRoles != 'true' || isAllowrecipientRoles == 'practitioner') ? this.stafffillEnable :'' ,
          practitionerFill: this.practitionerFill,
          triggerOnPHI:this.triggerOnPHI,
          notifyOnSubmit: selectednotifyOnSubmit ? selectednotifyOnSubmit:[],
          recipientRoles: selectedRecipientRoles ? selectedRecipientRoles:[],
          nursingAgencyUserTagSelected: selectedNursingTags.length > 0 ? selectedNursingTags : [],
          staffFacing : isAllowrecipientRoles,
          isEnrolmentForm : this.tagForm.value['isEnrolment'].toString(),
          defaultFromFilingCenter :this.fromFilingCenter,
          defaultFromFilingCenterSubmit :this.fromFilingCenterSubmit,
          defaultFromFilingCenterarchivejson :this.fromFilingCenterjson,
          defaultFromFilingCenterSubmitjson :this.fromFilingCentersSubmitjson,
          defaultFromFilingCentersendreminder :this.folderNamesendreminder,
          subject: this.tagForm.value['subject'],
          body: this.tagForm.value['body'],
          subjectOne: this.tagForm.value['subjectOne'],
          bodyOne: this.tagForm.value['bodyOne'],
          attachMessage: this.attachMessage,
          attachMessageOne: this.attachMessageOne,
          enableApplessWorkflow:false,
          applessDevices: '',
          FaxQIntegration :this.faxQEnable,
          directlinkpatientchartTriggerOn:((this.userData.config.enable_multisite == 1) && (this.directlinkpatientchartEnable ==true)) ? ((isPatientChartTriggerVal =="true") ?"archive":"submit") :"",
          externalFileDisclosePHITriggerOn:((this.userData.config.enable_multisite == 1) &&  (this.externalFileDisclosePHIEnable ==true )) ? ((isPhiTriggerVal =="true")?"archive":"submit"):"",
          progressNoteIntegrationTriggerOn:((this.userData.config.enable_multisite == 1 ) && (this.progressNoteIntegrationEnable ==true )) ? isProgressNoteIntegrationVal:'',
          faxQIntegrationTriggerOn:((this.userData.config.enable_multisite == 1) && (this.faxQEnable == true )) ? ((isFaxQueVal == "true") ?"archive":"submit") :"",
          sendformdatajsonTriggerOn:((this.userData.config.enable_multisite == 1) && (this.sendformdatajsonEnable ==true ))? ((isEnableThirdPartyVal == "true")?"archive":"submit") :"",
          externalNoteCategory: this.tagForm.value['externalNoteCategory'],
          externalNoteTypeId: this.tagForm.value['externalNoteTypeId'],
          externalNoteCategoryId: this.tagForm.value['externalNoteCategoryId'],
          externalNoteType: this.tagForm.value['externalNoteType'],
          sendCompletedForm: this.tagForm.value['sendCompletedForm'],
          externalIntegrationSettings: externalIntegrationSettings,
          enableConfirmationPopupOnSend: this.tagForm.value['enableConfirmationPopupOnSend'],
          enableConfirmationPopupOnSubmit: this.tagForm.value['enableConfirmationPopupOnSubmit'],
          }
      }
      if (this.userData.config.progress_note_integration_mode === 'webhook' && this.progressNoteIntegrationEnable) {
        tagMeta['externalFileExchangeOnSubmit'] = isProgressNoteIntegrationVal === 'submit';
        tagMeta['externalFileExchangeOnArchive'] = isProgressNoteIntegrationVal === 'archive';
      }
     if(this._structureService.getCookie('formTagId')){
      var paramsUpdate = {tagName:this.tagForm.value['formTagName'],tagMeta:JSON.stringify(tagMeta),id:this._structureService.getCookie('formTagId'),deleted:0};
      this._structureService.deleteUpdateFormTag(paramsUpdate).then((result)=>{
        let res:any=result;
        if(!isNull(res.updateFormTag)){
        if(result['updateFormTag'] && result['updateFormTag'].id){
          if(result['updateFormTag'].id=="0"){
              var notify = $.notify('Duplicate Entries Not Allowed');
              setTimeout(()=>{
                  notify.update({'type': 'warning', 'message': '<strong>Duplicate Entries Not Allowed</strong>'});                  
              }, 1000);
          }
          else{
              var notify = $.notify('Success! Form Type updated');
              setTimeout(()=>{
                  notify.update({'type': 'success', 'message': '<strong>Success! Form Type updated</strong>'});
                  this.router.navigate(['/forms/form-tags']);
              }, 1000);
              var activityData = {
                activityName: "Form Type updated",
                activityType: "forms",
                activityDescription: this.userData.displayName + ' has updated the Form Type as ' + activityValue + ' for tag Name ' +this.tagForm.value['formTagName'] + '(id-'+this._structureService.getCookie('formTagId')+')'+' metadata('+JSON.stringify(tagMeta)+')'
              };
              this._structureService.trackActivity(activityData);
          }
          
        }
        else{
          setTimeout(()=>{
            var notify = $.notify('Error! Error Occured');
            notify.update({'type': 'danger', 'message': '<strong>Error! Error Occured</strong>'});
            this.router.navigate(['/forms/form-tags']);
        }, 1000);
        }
    } else {
        let activityData = {
            activityName: "Error Update Form Tag",
            activityType: "formtag",
            activityDescription: `Error occured while Updating Form Tag due to invalid input. `
          }; 
          this._structureService.notifyMessage({
            messge: this._ToolTipService.getTranslateData('MESSAGES.ERROR_MSG_INVALID_INPUT'),
            delay: 1000,
            type: 'warning'
          });
          this._structureService.trackActivity(activityData);
    }
      });
     }
     else{
       var paramsCreate = { tagName: this.tagForm.value['formTagName'], tagMeta:JSON.stringify(tagMeta)};
      this._structureService.saveFormTag(paramsCreate).then((result)=>{
        let res:any=result;
        if(!isNull(res.createFormTag)){
        if(result['createFormTag'] && result['createFormTag'].id){
          if(result['createFormTag'].id=="0"){
            var notify = $.notify('Duplicate Entries Not Allowed');
            setTimeout(()=>{
                notify.update({'type': 'warning', 'message': '<strong>Duplicate Entries Not Allowed</strong>'});                
            }, 1000);
        }
        else{
            var notify = $.notify('Success! Form Type created');
            setTimeout(()=>{
                notify.update({'type': 'success', 'message': '<strong>Success! Form Type created</strong>'});
                this.router.navigate(['/forms/form-tags']);
            }, 1000);
            var activityData = {
              activityName: "Form Type Added",
              activityType: "forms",
              activityDescription: this.userData.displayName + ' has added the Form Type as ' + activityValue + ' for tag Name ' +this.tagForm.value['formTagName']+' metadata('+JSON.stringify(tagMeta)+')'
            };
            this._structureService.trackActivity(activityData);
        }
        }
        else{
          setTimeout(()=>{
            var notify = $.notify('Error! Error Occured');
            notify.update({'type': 'danger', 'message': '<strong>Error! Error Occured</strong>'});
            this.router.navigate(['/forms/form-tags']);
        }, 1000);
        }
    } else {
        let activityData = {
            activityName: "Error Create Form Tag",
            activityType: "formtag",
            activityDescription: `Error occured while Creating Form Tag due to invalid input.`
          }; 
          this._structureService.notifyMessage({
            messge: this._ToolTipService.getTranslateData('MESSAGES.ERROR_MSG_INVALID_INPUT'),
            delay: 1000,
            type: 'warning'
          });
          this._structureService.trackActivity(activityData);
    }
     });

     }
  }

  getExtDocData() {
    $('#bulk-edit').modal('show');
      this.showLoader = true;
      this.enableSubData = false;
      this._structureService.getNoteTypes('documentType').then((data) => {
        if(data){
          if(data['issue']){
               if(data['issue'][0]['diagnostics']['statusCode'] == 412){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 1;
               } else if(data['issue'][0]['diagnostics']['statusCode'] == 406){
                    this.noteCount = 0;
                    this.showLoader = false;
                    this.noteTypesCount = 4;
                    } 
          } else if (data['documentCategory']) {
               this.showLoader = false;
               this.noteTypes = data['documentCategory'];
               this.noteCount  = this.noteTypes.length;
             this.noteTypesCount = -1;
          } else if (data['message']) {
             if(data['message'] == 412){
               this.noteCount = 0;
               this.showLoader = false;
               this.noteTypesCount = 1;
             } else if(data['message'] == 404){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 0;
             } else if(data['message'] == 500){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 0;
             } else if(data['message'] == 0){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 0;
             } else if(data['message'] == 401){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 2;
             } else if(data['message'] == "Invalid Parameter"){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 3;
             } else if(data['message'] == 406){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 4;
             }
             }
        } else {
          this.showLoader = false;
          this.noteTypesCount = 0;
        }
    })
    this.enableDocType = false;
  }

  getExtMessageData(reload){
		this.showLoader = true;
		if(this.isEnableApiBasedIntegrationCategory==true && this.isEnableApiBasedIntegrationType == true){
    this.enableSubData = false;
			this._structureService.getNoteTypes('noteCategory').then((data) => {
        if(data){
				  if(data['issue']){
					  if(data['issue'][0]['diagnostics']['statusCode'] == 412){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 1;
					  } else if(data['issue'][0]['diagnostics']['statusCode'] == 406){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 4;
					  }
				  }else if(data['messageTagCategory']){
					this.showLoader = false;
					this.messageTypes = data['messageTagCategory'];
					this.messageTypes = this.messageTypes['items'];
					this.noteCount  = this.messageTypes.length;
					this.noteTypesCount = -1;
					this.msgTagTypeCount = -1;
				  }else if(data['message']){
					if(data['message'] == 412){
					  this.noteCount = 0;
					  this.showLoader = false;
					  this.noteTypesCount = 1;
					} else if(data['message'] == 404){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 0;
					} else if(data['message'] == 500){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 0;
					} else if(data['message'] == 0){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 0;
					} else if(data['message'] == 401){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 2;
					} else if(data['message'] == "Invalid Parameter"){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 3;
					} else if(data['message'] == 406){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 4;
					}
					}
        } else {
          this.showLoader = false;
				  this.noteTypesCount = 0;
        }
			})
		}else if(this.isEnableApiBasedIntegrationCategory==false && this.isEnableApiBasedIntegrationType == true){
			this.getMsgTagTypes();
		} 
		
	  if(reload != 0){
		$('#bulk-edit-new').modal('show');
	  }
    this.enableDocType = false;
  }

  getMsgTagTypes(cat=""){
    if(cat == ""){
      this._structureService.getNoteTypes('documentType').then((data) => {
        if(data){
          if(data['issue']){
               if(data['issue'][0]['diagnostics']['statusCode'] == 412){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 1;
               } else if(data['issue'][0]['diagnostics']['statusCode'] == 406){
                    this.noteCount = 0;
                    this.showLoader = false;
                    this.noteTypesCount = 4;
                    } 
          } else if (data['documentCategory']) {
               this.showLoader = false;
               this.noteTypes = data['documentCategory'];
               this.noteCount  = this.noteTypes.length;
             this.noteTypesCount = -1;
          } else if (data['message']) {
             if(data['message'] == 412){
               this.noteCount = 0;
               this.showLoader = false;
               this.noteTypesCount = 1;
             } else if(data['message'] == 404){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 0;
             } else if(data['message'] == 500){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 0;
             } else if(data['message'] == 0){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 0;
             } else if(data['message'] == 401){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 2;
             } else if(data['message'] == "Invalid Parameter"){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 3;
             } else if(data['message'] == 406){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 4;
             }
             }
        } else {
          this.showLoader = false;
				  this.noteTypesCount = 0;
        }
    })
    } else{
      this._structureService.getNoteTypes('noteType',cat).then((data) => {
        if(data){
					if(data['issue']){
						if(data['issue'][0]['diagnostics']['statusCode'] == 412){
						  this.noteCount = 0;
						  this.showLoader = false;
						  this.msgTagTypeCount = 1;
						} else if(data['issue'][0]['diagnostics']['statusCode'] == 406){
							this.noteCount = 0;
							this.showLoader = false;
							this.msgTagTypeCount = 4;
						  }
					}else if(data['messageTagType']){
					  this.showLoader = false;
					  this.msgTagTypes = data['messageTagType'];
					  this.msgCount  = this.msgTagTypes.length;		
				 	  this.msgTypeSet = this.msgTagTypes['items']
					  this.noteTypesCount = -1;
					  this.msgTagTypeCount = -1;
					}else if(data['message']){
					  if(data['message'] == 412){
						this.msgCount = 0;
						this.showLoader = false;
						this.msgTagTypeCount = 1;
					  } else if(data['message'] == 404){
						  this.msgCount = 0;
						  this.showLoader = false;
						  this.msgTagTypeCount = 0;
					  } else if(data['message'] == 500){
						  this.msgCount = 0;
						  this.showLoader = false;
						  this.msgTagTypeCount = 0;
					  } else if(data['message'] == 0){
						  this.msgCount = 0;
						  this.showLoader = false;
						  this.msgTagTypeCount = 0;
					  } else if(data['message'] == 401){
						  this.msgCount = 0;
						  this.showLoader = false;
						  this.msgTagTypeCount = 2;
					  } else if(data['message'] == "Invalid Parameter"){
						  this.msgCount = 0;
						  this.showLoader = false;
						  this.msgTagTypeCount = 3;
					  } else if(data['message'] == 406){
						  this.msgCount = 0;
						  this.showLoader = false;
						  this.msgTagTypeCount = 4;
					  }
					}	
        } else {
          this.showLoader = false;
				  this.msgTagTypeCount = 0;
        }
      })
      }   
  }

  clearExtMessageData() {
    this.enableSubData = false;
    this.tagForm.patchValue({
      externalNoteTypeId: "",
      externalNoteCategory: ""
    });
    this.selectedCatName = "";
    this.selectedTypeName = "";
  }

  onSelectCategory(event){
    if(event.target.value == ""){
      this.enableSubData = false
    }
    this.selectedCatValue = event.target.value;     
    this.documentTypeSet = this.noteTypes.find(function (item) {          
         return item.id == event.target.value;     
    });
    this.documentTypeSet = this.documentTypeSet['documentType']
    this.enableDocType = true; 
}

  closeDynamicModal() {
    this.enableDocType = false;  
    this.enableSubData = false;
    $('#bulk-edit').modal('hide');
  }

  closeDynamicMsgModal() {
    this.enableDocType = false;  
    this.enableSubData = false;
    $('#bulk-edit-new').modal('hide');
  }

  clearExtDocData() {
    this.enableSubData = false;
    this.tagForm.patchValue({
      externalDocumentType: "",
    });
  }

  onSelectType(event){
    this.selectedTypeValue = event.target.value;
    if(this.isEnableApiBasedIntegration == true){ 
    this.selectedTypeName = event.target.selectedOptions[0].text;
    } else {
    this.selectedTypeName = '';
    }
    if(event.target.value != "" && this.selectedCatValue != ""){
			this.enableSubData = true
		}else{
			this.enableSubData = false
		}
  }

  onSelectNoteType(event){
    this.selectedNoteTypeValue = event.target.value;
    if(this.isEnableApiBasedIntegration == true){ 
    this.selectedTypeName = event.target.selectedOptions[0].text;
    } else {
    this.selectedTypeName = '';
    }
    if(event.target.value != "" && this.selectedCatValue != ""){
			this.enableSubData = true
		}else{
			this.enableSubData = false
		}
  }
 
 setSelectedData(){
      this.tagForm.patchValue({
           externalDocumentType: this.selectedTypeName,
      });
      this.enableDocType = false;
      this.enableSubData = false;
      $('#bulk-edit').modal('hide');
 }

 setSelectedMsgData(){
  this.tagForm.patchValue({
    externalNoteCategory:this.selectedCatName,
    externalNoteTypeId: this.selectedTypeName
  });
  this.enableDocType = false;
  this.enableSubData = false;
  $('#bulk-edit-new').modal('hide');
 }

 onSelectMessage(event) {
  if(event.target.value == ""){
		this.enableSubData = false
	}
  this.selectedMessageValue = event.target.value;     
  this.selectedCatName =  event.target.selectedOptions[0].text;
  if(this.isEnableApiBasedIntegrationCategory==true && this.isEnableApiBasedIntegrationType == true){
  this.getMsgTagTypes(this.selectedMessageValue);
  } else if(this.isEnableApiBasedIntegrationCategory==false && this.isEnableApiBasedIntegrationType == true){
    this.msgTagTypes = this.noteTypes.find(function (item) {          
      return item.id == event.target.value;     
     });
     this.msgTypeSet = this.msgTagTypes['documentType']
  }		
  this.enableDocType = true;   
 }

  getNursingAgencyTags() {
    const userData: any = this._structureService.getUserdata();
    const tagGetData = '?userId=' + userData.userId + '&tenantId=' + userData.tenantId;
    const tagTypes = ['2']; // Message Tag =1, User Tag =2 , Document Tag =3
    this._structureService.getNursingAgencyTagsByGroup(tagGetData, tagTypes).then((data: any) => {
      this.nursingAgencyTags = data;      
    });
  }

  getNursingAgencyRoles(nursingAgency) {
    if(nursingAgency != "") {
      this._structureService.setCookie('cache-page', 'add-tag', 1, '');
      this._signService.getAllNursingAgencyRoles(nursingAgency).then((data) => {
        this.teanntRoles = data;
        var patientAssociateRole;
        this.teanntRoles.filter(function(role){
          if(role.citus_role_id == '3'){
            patientAssociateRole = role.id;
          }
        });

        this.patientAssociateRole = patientAssociateRole;
        this._structureService.setCookie('cache-page', 'add-tag', -1);      
      }).catch((ex) => {
        //this.loginFailed = true;
      });
    } else {
      this._structureService.setCookie('cache-page', 'add-tag', 1, '');
      this._signService.getAllTenantRoles().then((data) => {
        this.teanntRoles = data;
        var patientAssociateRole;
        this.teanntRoles.filter(function(role){
          if(role.citus_role_id == '3'){
            patientAssociateRole = role.id;
          }
        });

        this.patientAssociateRole = patientAssociateRole;
        this._structureService.setCookie('cache-page', 'add-tag', -1);
      });
    }    
  }

  
}
