.cat__core__step__desc p {
  font-weight: bold !important;
}
.cat__core__step__digit {
  cursor: pointer;
}
.signature-req-block:hover {
  background: #e6e6e6;
}
.status-active {
  background-color: #e6e6e6;
}
span.reminder-details i {
  margin-right: 7px;
  color: #52b3aa;
}
span.reminder-details {
  cursor: pointer;
}
.not-clickable-col {
  pointer-events: none !important;
  cursor: not-allowed !important;
}
a.reminder-details {
  text-transform: capitalize;
  color: #74708d !important;
  font-weight: normal;
  text-decoration: none !important;
  font-size: 12px;
}
.filter-site {
  margin-left: 5.28rem;
  margin-right: -0.72rem;
}
.user-sites {
  float: left;
}
.list-top {
  padding-top: 10px;
}
.row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.advanced-search-btn {
  margin-left: 2em;
  margin-bottom: 10px;
  text-decoration: underline;
  color: blue;
  cursor: pointer;
}
.adv-form-control {
  padding: 0.55rem .14rem !important;
  border: 1px solid #adadad !important;
}
.filter {
  margin-top: 10px;
  float: right;
  margin-right: 10px;
}
