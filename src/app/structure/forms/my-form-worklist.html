<style type="text/css">
    .cat__core__step__desc p {
        font-weight: bold !important;
    } 
    
    .cat__core__step__digit {
        cursor: pointer;
    }
    
    .signature-req-block:hover {
        background: #e6e6e6;
    }
    
    .status-active {
        background-color: #e6e6e6;
    }
    #draftHistory{
    color: #74708d;
    text-decoration: none;

    }
    #draftHistory:hover{
        color:#0088ff;
    } 
    .user-sites{
        float:left;
    }
    .list-top {
        padding-top: 10px;
    }
</style>
<section class="card">
    <div class="card-block">          
        <div class="row request-tab">
<div class="col-md-12 from-to-btn-sec" style="margin-bottom: 14px;">
        <div class="nav-tabs-horizontal">
            <ul class="filter-sites-container nav nav-tabs filing-icons" role="tablist">
                <li class="nav-item list-top">
                    <a class="nav-link active"  [routerLink]="['/forms/worklist']" href="javascript: void(0);" data-toggle="tab"  role="tab" aria-expanded="true">
                        <i class="fa icmn-pen" aria-hidden="true"></i>
                        <span class="cat__core__title">My Form Worklists</span></a>
                </li>
                <li class="nav-item list-top" [hidden]="(userDataN.group == constants.userGroupIds.partner || !previlages.viewFormEntries &&  (userDataConfig.enable_collaborate_edit != 1 || userDataN.group == constants.userGroupIds.patient || userDataN.nursing_agencies != '' || (!previlages.viewFormEntries  && !previlages.FillStructuredForms) || (userDataN.accessSecurityEnabled && userDataN.accessSecurityEsiValue && userDataN.accessSecurityEsiValue != '')))" >
                    <a class="nav-link "  [routerLink]="['/forms/list']" href="javascript: void(0);" data-toggle="tab"  role="tab" aria-expanded="true">
                        <i class="fa fa-list-alt" aria-hidden="true"></i>
                        <span class="cat__core__title">All Form Worklists</span></a>
                </li>                       
                <div class="filter-sites-wrapper"  [hidden]="!hideSiteSelection">
                    <div class="col-sm-11" style="float: right">
                            <span style="width: 76%">
                                <span class="site-label user-sites">{{ labelSiteFilter | translate }} </span>
                                <app-select-sites [events]="eventsSubject.asObservable()" [filterType]=true (siteIds)="getSiteIds($event)" (hideDropdown)="hideDropdown($event)">
                                </app-select-sites>
                            </span>
                    </div>
                </div>
            </ul>


        </div>
    </div>
</div>    
</div>
    <div class="card-block">
        <div>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
                <li class="breadcrumb-item">Form Worklists</li>
                <li class="info-widget">
                    <span class="chatwith-modal-tip">  
                                <img  src="./assets/modules/dummy-assets/common/img/chatwith-tip.png">
                                <span *ngIf="+userData.group === constants.userGroupIds.patient || (!isPartnerUser && +userData.group !== constants.userGroupIds.patient 
                                && +userDataConfig.enable_collaborate_edit === 1)"  class="modal-footer-text-sign">{{'MESSAGES.FORMS_TAB_CATEGORY' | translate}}</span>
                                <span *ngIf="isPartnerUser || (+userData.group !== constants.userGroupIds.patient && +userDataConfig.enable_collaborate_edit !== 1)" 
                                class="modal-footer-text-sign">{{'MESSAGES.FORMS_TAB_CATEGORY_WITH_DRAFT' | translate}}</span>
                    </span>
                </li>
            </ol>

 </div>
        <div class="row">
            <div class="col-lg-12">
               
                <div style="text-align:center;width:100%;position: absolute;top: 10px;" *ngIf="dataTableLoading">
                    <img src="./assets/img/loader/loading.gif" />
                </div>
                
                <div class="mb-5">
                    <div *ngIf="userDataConfig.enable_collaborate_edit == 1" class="row actions-list" style="margin-bottom: 40px;margin-top: 53px;" [ngClass]="{'not-clickable':clickedTab != ''}">
                        <div class=" signedblock-border signature-req-block" [ngClass]="{'status-active':isActive == formStatus.Completed, 'col-sm-3': isPartnerUser, 'col-sm-4': !isPartnerUser}">
                            <a href="javascript:void(0)" (click)="changeFormData('COMPLETED','refresh')">
                                <div class="row" style="height:100%">
                                    <div class="col-sm-3"></div>
                                    <div class="col-sm-6">
                                        <div class="cat__core__step pending-block">
                                            <span class="cat__core__step__digit"><i
                                                    class="fa fa-check completed"></i></span>
                                            <div class="cat__core__step__desc">
                                                <span class="cat__core__step__title">{{completedCount}}</span>
                                                <p>{{'TITLES.WORKLIST_TAB.COMPLETED' | translate}}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3"></div>
                                </div>
                            </a>
                        </div>
                        <div class="signedblock-border signature-req-block" [ngClass]="{'status-active':isActive == formStatus.Pending, 'col-sm-3': isPartnerUser, 'col-sm-4': !isPartnerUser}">
                            <a href="javascript:void(0)" (click)="changeFormData('PENDING','refresh')">
                                <div class="row" style="height:100%">
                                    <div class="col-sm-3"></div>
                                    <div class="col-sm-6">
                                        <div class="cat__core__step cat__core no-border signed-block">
                                            <span class="cat__core__step__digit"><i
                                                    class="fa fa-clock-o waiting-others"></i></span>
                                            <div class="cat__core__step__desc">
                                                <span class="cat__core__step__title">{{pendingCount}}</span>
                                                <p>{{'TITLES.WORKLIST_TAB.PENDING' | translate}}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3"></div>
                                </div>
                            </a>
                        </div>
                        <div class="col-sm-3 signedblock-border signature-req-block" *ngIf="isPartnerUser" [ngClass]="{'status-active':isActive === formStatus.DRAFT}">
                            <a href="javascript:void(0)" (click)="changeFormData('DRAFTS','refresh')">
                                <div class="row" style="height:100%">
                                    <div class="col-sm-3"></div>
                                    <div class="col-sm-6">
                                        <div class="cat__core__step pending-block">
                                            <span class="cat__core__step__digit requests-archive-icon">
                                            <i><img src="./assets/img/draft.png"></i>
                                        </span>
                                            <div class="cat__core__step__desc">
                                                <span class="cat__core__step__title">{{draftCount}}</span>
                                                <p style="padding-left: 12%;">{{'TITLES.WORKLIST_TAB.DRAFTS' | translate}}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3"></div>
                                </div>
                            </a>
                        </div>
                        <div class="signature-req-block" [ngClass]="{'status-active':isActive == formStatus.Archived, 'col-sm-3': isPartnerUser, 'col-sm-4': !isPartnerUser}">
                            <a href="javascript:void(0)" (click)="changeFormData('ARCHIVE','refresh')">
                                <div class="row" style="height:100%">
                                    <div class="col-sm-3"></div>
                                    <div class="col-sm-6">
                                        <div class="cat__core__step pending-block">
                                            <span class="cat__core__step__digit requests-archive-icon">
                                                <i><img src="./assets/img/archive-icon.png"></i>
                                            </span>
                                            <div class="cat__core__step__desc">
                                                <span class="cat__core__step__title">{{archiveCount}}</span>
                                                <p style="padding-left: 12%;">{{'TITLES.WORKLIST_TAB.ARCHIVED' | translate}}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3"></div>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div *ngIf="userDataConfig.enable_collaborate_edit != 1" class="row actions-list" style="margin-bottom: 40px;margin-top: 53px;" [ngClass]="{'not-clickable':clickedTab != ''}">
                        <div class="col-sm-4 signedblock-border signature-req-block" [ngClass]="{'status-active':isActive == formStatus.Completed,'col-sm-3':userData.group !='3','col-sm-4':userData.group =='3'}">
                            <a href="javascript:void(0)" (click)="changeFormData('COMPLETED','refresh')">
                                <div class="row" style="height:100%">
                                    <div class="col-sm-3"></div>
                                    <div class="col-sm-6">
                                        <div class="cat__core__step pending-block">
                                            <span class="cat__core__step__digit"><i
                                                    class="fa fa-check completed"></i></span>
                                            <div class="cat__core__step__desc">
                                                <span class="cat__core__step__title">{{completedCount}}</span>
                                                <p>{{'TITLES.WORKLIST_TAB.COMPLETED' | translate}}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3"></div>
                                </div>
                            </a>
                        </div>
                        <div class="col-sm-4 signedblock-border signature-req-block" [ngClass]="{'status-active':isActive == formStatus.Pending,'col-sm-3':userData.group !='3','col-sm-4':userData.group =='3'}">
                            <a href="javascript:void(0)" (click)="changeFormData('PENDING','refresh')">
                                <div class="row" style="height:100%">
                                    <div class="col-sm-3"></div>
                                    <div class="col-sm-6">
                                        <div class="cat__core__step cat__core no-border signed-block">
                                            <span class="cat__core__step__digit"><i
                                                    class="fa fa-clock-o waiting-others"></i></span>
                                            <div class="cat__core__step__desc">
                                                <span class="cat__core__step__title">{{pendingCount}}</span>
                                                <p>{{'TITLES.WORKLIST_TAB.PENDING' | translate}}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3"></div>
                                </div>
                            </a>
                        </div>
                        <div class="col-sm-3 signedblock-border signature-req-block" *ngIf="userData.group !='3'" [ngClass]="{'status-active':isActive == formStatus.DRAFT}">
                            <a href="javascript:void(0)" (click)="changeFormData('DRAFTS','refresh')">
                                <div class="row" style="height:100%">
                                    <div class="col-sm-3"></div>
                                    <div class="col-sm-6">
                                        <div class="cat__core__step pending-block">
                                            <span class="cat__core__step__digit requests-archive-icon">
                                            <i><img src="./assets/img/draft.png"></i>
                                        </span>
                                            <div class="cat__core__step__desc">
                                                <span class="cat__core__step__title">{{draftCount}}</span>
                                                <p style="padding-left: 12%;">{{'TITLES.WORKLIST_TAB.DRAFTS' | translate}}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3"></div>
                                </div>
                            </a>
                        </div>
                        <div class="col-sm-4 signature-req-block" [ngClass]="{'status-active':isActive == formStatus.Archived,'col-sm-3':userData.group !='3','col-sm-4':userData.group =='3'}">
                            <a href="javascript:void(0)" (click)="changeFormData('ARCHIVE','refresh')">
                                <div class="row" style="height:100%">
                                    <div class="col-sm-3"></div>
                                    <div class="col-sm-6">
                                        <div class="cat__core__step pending-block">
                                            <span class="cat__core__step__digit requests-archive-icon">
                                                <i><img src="./assets/img/archive-icon.png"></i>
                                            </span>
                                            <div class="cat__core__step__desc">
                                                <span class="cat__core__step__title">{{archiveCount}}</span>
                                                <p style="padding-left: 12%;">{{'TITLES.WORKLIST_TAB.ARCHIVED' | translate}}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3"></div>
                                </div>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div class="row">
            <div class="col-lg-12">
                <app-advance-search [hidden]="_structureService.hideAdvanceSearchArea" [resetForm]="resetAdvanceSearchForm" worklist="my form" [dynamicControls]="dynamicControls" (closeAdvanceSection)="_structureService.closeAdvanceSearch($event)" (advanceSearch)="applyAdvanceSearch($event)">
                </app-advance-search>
            </div>
        </div>          
    <div class="row">
      <div class="col-lg-9"></div>
      <div class="col-lg-3 date-range-align">
        <div class="row">
          <div class="col-lg-4 text-right padding-top-ten">
            <label>{{ 'LABELS.DATE_RANGE' | translate }}
                <i chToolTip="defaultDateRangeInfo" class="icmn-info" data-animation="false">&nbsp;</i>:&nbsp;
            </label>
          </div>
          <div class="col-lg-8">
            <ch-daterange-picker
              [keepSession]="true"
              [dateRangeFilterOptions]="dateRangeFilterOptions"
              [control]="dateRange"
              [saveStateInto]="dateRangeStoreKey"
              (selectDateRange)="onSelectDateRange($event)"
              [emitOnLoad]="true"
            ></ch-daterange-picker>
          </div>
        </div>
      </div>
    </div>               
        <form class="new-forms" [ngClass]="{'not-clickable':clickedTab != ''}" > 
            <div class="col-sm-12">
                <table class="table table-hover table-responsive" id="form-list-dt" width="100%"></table>
            </div>            
        </form>
    </div>
</section>

<app-resend-document-form *ngIf="showResendModal" [showModal]="showResendModal" (onClose)="popUpRecipientsList()" 
[getRecipientsFor]="contentType" [patientId]="resendPatientId" [extraData]="{ admissionId: resendPatientId && activeStrucuredForms.admissionId }" [entityId]="activeStrucuredForms.sent_id" 
(eventEmitterSelectedRecipients)="resendFormToRecipients($event)">
</app-resend-document-form>

<app-form-history [showHistory]="showHistory" [showDraftHistory]="showDraftHistory" [isActive]="isActive" (closeModal)="closeHistoryModal($event)" (closeDraftModal)="closeDraftHistoryModal($event)" [formDetails]="activeStrucuredForms"></app-form-history>
