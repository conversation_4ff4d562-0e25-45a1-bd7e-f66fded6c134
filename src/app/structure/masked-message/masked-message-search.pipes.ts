import { Injectable, Pipe, PipeTransform } from '@angular/core';
declare var $: any;
declare var jQuery: any;
@Pipe({
 name: 'searchfilter'
})

@Injectable()
export class SearchFilterPipe implements PipeTransform {
 transform(items: any[], field: string, value: string): any[] {
    //console.log(items,field,value)
   if (!items){
       //console.log('not item')
       //return items;
    return [];
   } else{
       //console.log('elseeeee not item')
       if(value) {
        //console.log('elseeeee not item', value, items);
        //return items.filter(item=>item.name.toLowerCase().indexOf(value.toLowerCase()) != -1);

        var result = $.map(items, function(item) { 
            if(item.name.toLowerCase().indexOf(value.toLowerCase()) != -1){
                item.searchHideStatus = false;
                return item;
            } else {
                if(item.caregiver_displayname) {
                    if(item.caregiver_displayname.toLowerCase().indexOf(value.toLowerCase()) != -1) {
                        item.searchHideStatus = false;
                        return item;
                    } else {
                        item.searchHideStatus = true;
                        return item;
                    }
                } else {
                    item.searchHideStatus = true;
                    return item;
                }
            }
        });

        //console.log("result ::: ",result);
        return result;

       } else {
            var result = $.map(items, function(item) { 
                item.searchHideStatus = false;
                return item;
            });
            return result;
       }
       //return items;
   //return items.filter(it => it[field] == value);
   }
 }
}

@Pipe({
    name: 'searchRolefilter'
   })
   
   @Injectable()
   export class SearchRoleFilterPipe implements PipeTransform {
    transform(items: any[], field: string, value: string): any[] {
       //console.log(items,field,value)
      if (!items){
          //console.log('not item')
          //return items;
       return [];
      } else {
        //console.log('SearchRoleFilterPipe', items, value);

        var result = $.map(items, function(uitem) { 
            //console.log('uitem', uitem);
                var uSatus = true;
            var userList = $.map(uitem.userList, function(user) { 
                //console.log('user', user);
                if(user.name.toLowerCase().indexOf(value.toLowerCase()) != -1){
                    //uitem.searchHideStatus = false;
                    //console.log('Falseeeeeeeee');
                    uSatus = false;
                    return user;
                } else {
                    if(user.caregiver_displayname) {
                        if(user.caregiver_displayname.toLowerCase().indexOf(value.toLowerCase()) != -1) {
                            uSatus = false;
                            return user;
                        }
                    }
                    if(user.IdentityValue) {
                      if(user.IdentityValue.toLowerCase().indexOf(value.toLowerCase()) != -1) {
                          uSatus = false;
                          return user;
                      }
                  }
                  if(user.caregiver_identityvalue) {
                    if(user.caregiver_identityvalue.toLowerCase().indexOf(value.toLowerCase()) != -1) {
                        uSatus = false;
                        return user;
                    }
                }
                } 
            });
            uitem.searchHideStatus = uSatus; 
            uitem.filteredUserList = userList;
            return uitem;
        });
        //console.log("result ::: ", result)
        return result;
      }
    }
   }

   @Pipe({
    name: 'searchfilterroletreeview'
  })
  
  @Injectable()
  export class SearchFilterRoleTreeViewPipe implements PipeTransform {
    transform(items: any[], field: string, value: string): any[] {
      if (!items) {
        return [];
      } else {
        if(value) {
          var result = [];
          items.filter( (user, key)=> {
            if(user[field].toLowerCase().indexOf(value.toLowerCase()) != -1) {
              result.push(user);
            }
          })
          return result;
        } else {
          return items;
        }
      }
    }
  }

  @Pipe({
    name: 'searchfiltermrn'
  })
  
  @Injectable()
  export class searchfiltermrn implements PipeTransform {
    transform(items: any[], field: string, value: string): any[] {
      if (!items) {
        return [];
      } else {
        if(value) {
          var result = [];
          items.filter( (user, key)=> {
            if(user[field].toLowerCase().indexOf(value.toLowerCase()) != -1) {
              result.push(user);
            }
          })
          return result;
        } else {
          return items;
        }
      }
    }
  }

   @Pipe({
    name: 'searchRolefilterSchedule'
   })
   
   @Injectable()
   export class SearchRoleFilterSchedulePipe implements PipeTransform {
    transform(items: any[], value: string, selectedScheduleType: number): any[] {
      if (!items){
       return [];
      } else {
        var count = 0;
        var result = $.map(items, function(uitem) { 
        var uSatus = true;
        var userList = $.map(uitem.userList, function(user) { 
        if(user.name.toLowerCase().indexOf(value.toLowerCase()) != -1){
            uSatus = false;
        } 
            });
            if(selectedScheduleType == 2 && !uSatus) {
                count++;
            }
            if(selectedScheduleType == 1 && !uitem.HideNoEscalationPrivilege && !uSatus) {
                count++;
            }
            uitem.searchHideStatus = uSatus; 
            return uitem;
        });
        result['count'] = count;
        return result;
      }
    }
   }
   
@Pipe({
    name: 'pluralizefilter'
   })
   
   @Injectable()
   export class pluralizeFilterPipe implements PipeTransform {
    transform(items: string, field: string, value: string): string {
        var pluralLetter = items.search(/(s|sh|ch|x|z)$/)!=-1?'es':'s';
        //new RegExp(/(s|sh|ch|x|z)$/).test(name)
        //console.log("pluralizeFilterPipe ::: ", items, items+pluralLetter);
        return items;//items.endsWith('ing')?items:items+pluralLetter;
    }
   }
   @Pipe({
    name: 'pluralizeSchedulefilter'
   })
   
   @Injectable()
   export class pluralizeScheduleFilterPipe implements PipeTransform {
    transform(items: string, field: string, value: string): string {
        var pluralLetter = items.search(/(s|sh|ch|x|z)$/)!=-1?'es':'s';
        //new RegExp(/(s|sh|ch|x|z)$/).test(name)
        //console.log("pluralizeFilterPipe ::: ", items, items+pluralLetter);
        return items+pluralLetter;
    }
   }