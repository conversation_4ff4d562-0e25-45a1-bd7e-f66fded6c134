import { Component, OnInit, EventEmitter } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import {
  FormsModule,
  FormBuilder,
  FormGroup,
  Validators,
  FormControl,
  FormArray,
  ReactiveFormsModule,
} from '@angular/forms';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { MaskedMessageService } from './masked-message.service';
import {
  UploadOutput,
  UploadInput,
  UploadFile,
  humanizeBytes,
} from 'ngx-uploader';
import * as io from 'socket.io-client';
import { InboxService } from '../inbox/inbox.service';
import { MessageBroadcastService } from '../message/message-broadcast.service';
import { SearchFilterPipe } from './masked-message-search.pipes';
import {
  SearchRoleFilterPipe,
  SearchFilterRoleTreeViewPipe,
  searchfiltermrn,
} from './masked-message-search.pipes';
import { pluralizeFilterPipe } from './masked-message-search.pipes';
import { SharedService } from './../shared/sharedServices';
import { IfObservable } from 'rxjs/observable/IfObservable';
import { Subject } from 'rxjs';
import { CONSTANTS, MessagePriority } from 'app/constants/constants';
import { getDistinctArray } from 'app/utils/utils';

declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;
declare var swal: any;
declare var NProgress: any;
@Component({
  selector: 'app-message-broadcast',
  templateUrl: './masked-message.html',
  styleUrls: ['./masked-message.css'],
})
export class MaskedMessageComponent implements OnInit {
  maskedMessageForm: FormGroup;
  privileges = this._structureService.getCookie('userPrivileges');

  config: any;
  userDetails: any;
  configData: any = {};
  userData: any = {};
  maskedDiscussionRecipientRoles = [];
  cmisFileUploadData = [];
  formData: FormData;
  files: UploadFile[];
  uploadInput: EventEmitter<UploadInput>;
  humanizeBytes: Function;
  dragOver: boolean;
  uploadFlag: boolean;
  broadcastData: any;
  pushNotifyMsg = '';
  selectedUserRoles = [];
  selectedFileNames = [];
  selectedOptions;
  order = [];
  orderMain = [];
  orderMainRoles = [];
  orderGroup = {};
  orderGroupMain = [];
  orderMainGroups = [];
  selectedRecipients;
  selectedInitiators;
  selectedRoleWiseRecipients = [];
  selectedRoleWiseRecipientsName = '';
  recipientSearchName = '';
  member;
  taggedUsers = [];
  usersListForMaskedMessage: any;
  allUsersListForMaskedMessage: any;
  clinicianData: any;
  clinicianDataRoleWise: any;

  checkComponentStatus = false;
  setupComponentStatus: any;
  crossTenantChangeSubscriber: any;
  filesAttached = {
    pdf: 0,
    document: 0,
    image: 0,
    audio: 0,
    video: 0,
  };
  siteId: any;
  hideSiteSelection: boolean;
  prioritySelected = MessagePriority.NORMAL;
  eventsSubject: Subject<void> = new Subject<void>();
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private _formBuild: FormBuilder,
    private _structureService: StructureService,
    private _ToolTipService: ToolTipService,
    private _MaskedMessageService: MaskedMessageService,
    private _inboxService: InboxService,
    //private searchfilter: SearchFilterPipe,
    private _messageBroadcastService: MessageBroadcastService,
    public _SharedService: SharedService
  ) {
    this.config = this._structureService.userDataConfig;
    this.userDetails = this._structureService.userDetails;
    this.configData = JSON.parse(this.config);
    this.userData = JSON.parse(this.userDetails);
    this.files = []; // local uploading files array
    this.uploadInput = new EventEmitter<UploadInput>(); // input events, we use this to emit data to ngx-uploader
    this.humanizeBytes = humanizeBytes;

    this.crossTenantChangeSubscriber =
      this._SharedService.crossTenantChange.subscribe((onInboxData) => {
        if (this.router.url.indexOf('/masked/message') > -1) {
          this.ngOnInit();
        }
      });
  }
  ngOnInit() {
    this.maskedDiscussionRecipientRoles = [];
    this.clinicianDataRoleWise = [];
    this.checkComponentStatus = false;

    this.config = this._structureService.userDataConfig;
    this.userDetails = this._structureService.userDetails;
    this.configData = JSON.parse(this.config);
    this.userData = JSON.parse(this.userDetails);
    this.maskedDiscussionRecipientRoles = this.userData.config[
      'masked_discussion_recipient_roles'
    ]
      ? this.userData.config['masked_discussion_recipient_roles'].split(',')
      : [];
    console.log(
      'maskedDiscussionRecipientRoles ::: ',
      this.maskedDiscussionRecipientRoles
    );

    /****Start Role Wise Select box script */
    // $('#roleWiseUsersWithModel').on('hide.bs.modal', (e) => {
    //   this.clearAllRoleWiseUsers();
    // })
    $('.js-example-basic-multiple').select2();
    this.setupComponentStatus = setInterval(() => {
      if (this.checkComponentStatus) {
        $('input[type="checkbox"]').change(checkboxChanged);
        clearInterval(this.setupComponentStatus);
      }
    }, 100);
    function checkboxChanged() {
      var $this = $(this),
        checked = $this.prop('checked'),
        container = $this.parent(),
        siblings = container.siblings();

      container
        .find('input[type="checkbox"]')
        .prop({
          indeterminate: false,
          checked: checked,
        })
        .siblings('label')
        .removeClass('custom-checked custom-unchecked custom-indeterminate')
        .addClass(checked ? 'custom-checked' : 'custom-unchecked');

      checkSiblings(container, checked);
    }

    function checkSiblings($el, checked) {
      var parent = $el.parent().parent(),
        all = true,
        indeterminate = false;

      $el.siblings().each(function () {
        return (all =
          $(this).children('input[type="checkbox"]').prop('checked') ===
          checked);
      });

      if (all && checked) {
        parent
          .children('input[type="checkbox"]')
          .prop({
            indeterminate: false,
            checked: checked,
          })
          .siblings('label')
          .removeClass('custom-checked custom-unchecked custom-indeterminate')
          .addClass(checked ? 'custom-checked' : 'custom-unchecked');

        checkSiblings(parent, checked);
      } else if (all && !checked) {
        indeterminate =
          parent.find('input[type="checkbox"]:checked').length > 0;

        parent
          .children('input[type="checkbox"]')
          .prop('checked', checked)
          .prop('indeterminate', indeterminate)
          .siblings('label')
          .removeClass('custom-checked custom-unchecked custom-indeterminate')
          .addClass(
            indeterminate
              ? 'custom-indeterminate'
              : checked
              ? 'custom-checked'
              : 'custom-unchecked'
          );

        checkSiblings(parent, checked);
      } else {
        $el
          .parents('li')
          .children('input[type="checkbox"]')
          .prop({
            indeterminate: true,
            checked: false,
          })
          .siblings('label')
          .removeClass('custom-checked custom-unchecked custom-indeterminate')
          .addClass('custom-indeterminate');
      }
    }
    /****End Role Wise Select box script */

    // this._inboxService.getUsersListByRoleId(3, 0, undefined).then((users) => {
    //   this.usersListForMaskedMessage = users;
    // }).catch((ex) => {
    // });

    // this._inboxService.getTenantClinicians(this.userData.tenantId).then((tenantUsers) => {
    //   this.clinicianData = tenantUsers;
    //   console.log("tenantUsers ::: ", tenantUsers)
    //   this.clinicianDataRoleWise;
    // });

    this.maskedMessageForm = this._formBuild.group({
      initiators: ['', Validators.required],
      recipients: ['', Validators.required],
      messageSubject: ['', Validators.required],
      messageText: ['', Validators.required],
      attachedFile: [''],
    });

    $('.select2-ini').select2({
      placeholder: 'Choose initiators(s)',
    });
    $('.select2-reci').select2({
      placeholder: 'Choose Recipient(s)',
    });

    $('.dropify').dropify({
      messages: {
        default:
          'Drag and drop a file here or click,<br>multiple file upload enabled.',
        replace: 'Drag and drop or click to add',
        remove: 'Remove',
        error: 'Ooops, something wrong happend.',
      },
      error: {
        fileSize: 'The file size is too big ({{ value }} max).',
        minWidth: 'The image width is too small ({{ value }}}px min).',
        maxWidth: 'The image width is too big ({{ value }}}px max).',
        minHeight: 'The image height is too small ({{ value }}}px min).',
        maxHeight: 'The image height is too big ({{ value }}px max).',
        imageFormat: 'The image format is not allowed ({{ value }} only).',
      },
    });

    $('.select2-container .select2-results__group').click(function () {
      alert('The paragraph was clicked.');
    });
  }

  listMaskedMessage() {
    this._inboxService
      .getMaskedMessageRecipientList(
        this.maskedDiscussionRecipientRoles,
        0,
        undefined,
        this.siteId
      )
      .then((users) => {
        this.clinicianDataRoleWise = {};
        this.allUsersListForMaskedMessage = users;
        console.log(
          'this.userData.nursing_agencies ',
          this.userData.nursing_agencies
        );
        console.log(
          'this.maskedDiscussionRecipientRoles',
          this.maskedDiscussionRecipientRoles
        );
        if (
          this.userData.nursing_agencies &&
          this.userData.nursing_agencies != ''
        ) {
          this.allUsersListForMaskedMessage =
            this.allUsersListForMaskedMessage.filter((a) => {
              if (
                (a.naTags &&
                  a.naTags != null &&
                  a.naTags != 'null' &&
                  a.naTags == this.userData.nursing_agencies &&
                  a.roleId == 3) ||
                a.roleId != 3
              ) {
                return a;
              }
            });
        }

        console.log(
          'this.allUsersListForMaskedMessage',
          this.allUsersListForMaskedMessage
        );
        this.allUsersListForMaskedMessage.forEach((value) => {
          console.log('value ::: ', value);
          var item = {
            id: value.userid,
            name: value.displayname,
            caregiver_displayname: null,
            caregiver_userid: null,
            dualRole: value.dualRoles,
            naTags: '',
            naTagNames: '',
            dob: '',
            IdentityValue: '',
            role: '',
            caregiver_dob: '',
            caregiver_identityvalue: '',
          };
          var roleData = {
            id: value.tenantRoleId,
            name: value.role,
            tenantRoleId: value.tenantRoleId,
          };

          if (
            value.naTags &&
            value.naTags != '' &&
            value.naTags != null &&
            value.naTags != 'null'
          ) {
            item.naTags = value.naTags;
            item.naTagNames = value.naTagNames;
          }

          console.log(
            'values ::: ',
            value.userid,
            this.userData.userId,
            value.password,
            value.roleId
          );
          if (
            value.userid != this.userData.userId &&
            value.password &&
            this.maskedDiscussionRecipientRoles.indexOf(
              value.tenantRoleId.toString()
            ) != -1
          ) {
            console.log(
              'User ---- ',
              value.userid,
              '--',
              value.displayname,
              '--',
              value.role,
              '--',
              value.roleId,
              '--',
              value.tenantRoleId
            );

            if (value.role == 'Caregiver') {
              item.caregiver_displayname = value.caregiver_displayname;
              item.caregiver_userid = value.caregiver_userid;
              item.caregiver_identityvalue = value.caregiver_identityvalue;
              if (value.caregiver_dob != '') {
                var date = new Date(value.caregiver_dob);
                if (date && date.getMonth() != NaN)
                  value.caregiver_dob =
                    (date.getMonth() > 8
                      ? date.getMonth() + 1
                      : '0' + (date.getMonth() + 1)) +
                    '/' +
                    (date.getDate() > 9
                      ? date.getDate()
                      : '0' + date.getDate()) +
                    '/' +
                    date.getFullYear();
                else {
                  value.caregiver_dob = '';
                }
                item.caregiver_dob = value.caregiver_dob;
              } else {
                console.log('date error');
              }
            }
            if (value.dob != '') {
              var date = new Date(value.dob);
              if (date && date.getMonth() != NaN)
                value.dob =
                  (date.getMonth() > 8
                    ? date.getMonth() + 1
                    : '0' + (date.getMonth() + 1)) +
                  '/' +
                  (date.getDate() > 9 ? date.getDate() : '0' + date.getDate()) +
                  '/' +
                  date.getFullYear();
              else {
                value.dob = '';
              }
              item.dob = value.dob;
            } else {
              console.log('date error');
            }
            if (
              value.IdentityValue != '' &&
              value.IdentityValue != 'null' &&
              value.IdentityValue != null
            ) {
              item.IdentityValue = value.IdentityValue;
            }
            if (value.role) {
              item.role = value.role;
            }
            if (value.tenantRoleId) {
              if (!this.clinicianDataRoleWise[value.tenantRoleId]) {
                this.clinicianDataRoleWise[value.tenantRoleId] = {};
              }
              //console.log($scope.userRoleWiseList[value.roleId],value.roleId)
              if (
                !('userList' in this.clinicianDataRoleWise[value.tenantRoleId])
              ) {
                //console.log("userList");
                this.clinicianDataRoleWise[value.tenantRoleId]['userList'] = [];
              }
              if (
                !(
                  'filteredUserList' in
                  this.clinicianDataRoleWise[value.tenantRoleId]
                )
              ) {
                this.clinicianDataRoleWise[value.tenantRoleId][
                  'filteredUserList'
                ] = [];
              }
              if (
                !('roleData' in this.clinicianDataRoleWise[value.tenantRoleId])
              ) {
                //console.log("roleData");
                this.clinicianDataRoleWise[value.tenantRoleId]['roleData'] = {};
              }
              this.clinicianDataRoleWise[value.tenantRoleId]['roleData'] =
                roleData;

              console.log('typeof Item', typeof item);
              this.clinicianDataRoleWise[value.tenantRoleId]['userList'].push(
                item
              );
              // this.clinicianDataRoleWise[value.tenantRoleId]['filteredUserList'].push(item);
              var a = JSON.parse(
                JSON.stringify(
                  this.clinicianDataRoleWise[value.tenantRoleId][
                    'filteredUserList'
                  ]
                )
              );
              console.log(a);
              console.log(this.clinicianDataRoleWise[value.tenantRoleId]);

              if (value.tenantRoleId == 11) {
                console.log('Patient', item);
                console.log(
                  'this.clinicianDataRoleWise[value.tenantRoleId]',
                  JSON.parse(
                    JSON.stringify(
                      this.clinicianDataRoleWise[value.tenantRoleId]
                    )
                  )
                );
              }
              // if(value.dualRoles && value.dualRoles.length > 1){
              //   console.log("Enter dual Role condition=============================================");
              //   console.log("value.dualRoles ===",value.dualRoles);
              //   console.log(typeof value.dualRoles);
              //   var dualRoles=JSON.parse(value.dualRoles);
              //   dualRoles.forEach(dualEach => {
              //     console.log("dualEach======================");
              //     console.log(dualEach);
              //     console.log("value.tenantRoleId====> "+value.tenantRoleId)
              //     if(value.tenantRoleId != dualEach.tenantUsersRoleId){
              //       var dualRoleData = { id: dualEach.tenantUsersRoleId, name: dualEach.roleName, tenantRoleId: dualEach.tenantUsersRoleId };
              //       if (!this.clinicianDataRoleWise[dualEach.tenantUsersRoleId]) {
              //         this.clinicianDataRoleWise[dualEach.tenantUsersRoleId] = {};
              //       }
              //       if (!('userList' in this.clinicianDataRoleWise[dualEach.tenantUsersRoleId])) {
              //         this.clinicianDataRoleWise[dualEach.tenantUsersRoleId]['userList'] = [];
              //       }
              //       if (!('roleData' in this.clinicianDataRoleWise[dualEach.tenantUsersRoleId])){
              //         this.clinicianDataRoleWise[dualEach.tenantUsersRoleId]['roleData'] = {};
              //       }
              //       this.clinicianDataRoleWise[dualEach.tenantUsersRoleId]['roleData'] = dualRoleData;
              //       this.clinicianDataRoleWise[dualEach.tenantUsersRoleId]['userList'].push(item);
              //     }
              //   })
              // }
            }
          }
        });
        this.clinicianDataRoleWise = this.clinicianDataRoleWise.filter(
          function (item) {
            //console.log("item", item, item.length)
            return true;
          }
        );

        this.checkComponentStatus = true;
        // console.log("this.clinicianDataRoleWise ::: ", this.clinicianDataRoleWise);
        // this.clinicianDataRoleWise.sort(function(a, b) {
        //   return a.roleData.name - b.roleData.name;
        // });
        this.clinicianDataRoleWise.sort(function (a, b) {
          if (a.roleData.name < b.roleData.name) return -1;
          if (a.roleData.name > b.roleData.name) return 1;
          return 0;
        });

        // console.log("this.clinicianDataRoleWise2 ::: ", this.clinicianDataRoleWise);
      })
      .catch((ex) => {});
  }
  ngOnDestroy() {
    if (this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
  }
  roleWiseUsersWithModel() {
    this.listMaskedMessage();
    $('#roleWiseUsersWithModel').modal('show');
    $('#chat-with-modal-search-box').val('');
    $('#chat-with-modal-search-box').focus();
  }
  emitEventToSelectSites(status): void {
    this.eventsSubject.next(status);
  }
  closeModal() {
    this.emitEventToSelectSites('closePopup');
  }
  closeRecipientModal(clear) {
    this.emitEventToSelectSites('closePopup');
    var elements = $('.fa-minus[class*=expand-icon-]');
    for (var i = 0; i < elements.length; i++) {
      var roleID =
        elements[i].parentNode.attributes['class'].value.split('-')[1];
      if ($('.expand-icon-' + roleID).hasClass('fa-plus')) {
        $('.expand-icon-' + roleID).removeClass('fa-plus');
        $('.expand-icon-' + roleID).addClass('fa-minus');
        $('.sub-item-panel-' + roleID).addClass('showall');
      } else {
        $('.expand-icon-' + roleID).removeClass('fa-minus');
        $('.expand-icon-' + roleID).addClass('fa-plus');
        $('.sub-item-panel-' + roleID).removeClass('showall');
      }
    }
    if ($('#roleWiseUsersWithModel').hasClass('show')) {
      $('#roleWiseUsersWithModel').modal('hide');
    }
    //this.clearAllRoleWiseUsers();
  }

  selectAllGroupUsers(data) {
    this.orderMainGroups = [];
    var self = this;
    this.orderGroupMain.forEach(function (value, key) {
      if (data == key) {
        if (value) {
          self.orderMainGroups.push(key);
          $('.userRole-' + data).each(function () {
            var alt = $(this).attr('alt');
            self.orderGroup[String(alt)] = true;
          });
        } else {
          $('.userRole-' + data).each(function () {
            var alt = $(this).attr('alt');
            self.orderGroup[String(alt)] = false;
          });
        }
      }
    });
    setTimeout(() => {
      this.formated('groups');
    }, 100);
  }
  formated(type) {
    var order = null;
    if (type == 'groups') {
      order = this.orderGroup;
    } else {
      order = this.order;
    }
    this.selectedUserRoles = [];
    for (var key in order) {
      if (order.hasOwnProperty(key) && order[key]) {
        this.selectedUserRoles.push(key);
      }
    }
  }

  addRoleWiseUsers() {
    var val = [];
    var userSelected = '';
    // if(!this.selectedRoleWiseRecipients.length) {
    this.selectedRoleWiseRecipients = [];
    this.selectedRoleWiseRecipientsName = '';
    //}
    this.recipientSearchName = '';
    $('#recipientSearchName').val('');
    let self = this;
    setTimeout(() => {
      var j = 0;
      $(':checkbox:checked').each(function (i) {
        console.log('checked each............ this.name=' + this.name);
        var name = this.name;
        if (name == 'cliniciansRoleUser[]') {
          var pushData = false;
          if (self.selectedRoleWiseRecipients.length) {
            if (self.selectedRoleWiseRecipients.indexOf($(this).val()) == -1) {
              pushData = true;
            }
          } else {
            pushData = true;
          }
          if (pushData) {
            if (!val.includes($(this).val())) {
              val[j] = $(this).val();
              userSelected = userSelected
                ? userSelected +
                  ', ' +
                  $(this).attr('title') +
                  '' +
                  ($(this).attr('data-patient-dob')
                    ? ' -' + $(this).attr('data-patient-dob')
                    : '') +
                  '' +
                  ($(this).attr('data-patient-mrn')
                    ? ' [MRN:' + $(this).attr('data-patient-mrn') + ']'
                    : '') +
                  '' +
                  ($(this).attr('care-dob')
                    ? ' -' + $(this).attr('care-dob')
                    : '') +
                  '' +
                  ($(this).attr('care-mrn')
                    ? ' [MRN:' + $(this).attr('care-mrn') + ']'
                    : '')
                : $(this).attr('title') +
                  '' +
                  ($(this).attr('data-patient-dob')
                    ? ' -' + $(this).attr('data-patient-dob')
                    : '') +
                  '' +
                  ($(this).attr('data-patient-mrn')
                    ? ' [MRN:' + $(this).attr('data-patient-mrn') + ']'
                    : '') +
                  '' +
                  ($(this).attr('care-dob')
                    ? ' -' + $(this).attr('care-dob')
                    : '') +
                  '' +
                  ($(this).attr('care-mrn')
                    ? ' [MRN:' + $(this).attr('care-mrn') + ']'
                    : '');
              j++;
            }
          }
        }
      });
      console.log('addRoleWiseUsers : vallllllllll=', val);
      console.log('addRoleWiseUsers : userSelected', userSelected);
      if (this.selectedRoleWiseRecipients.length) {
        this.selectedRoleWiseRecipients.push.apply(
          this.selectedRoleWiseRecipients,
          val
        );
        this.selectedRoleWiseRecipientsName =
          this.selectedRoleWiseRecipientsName + ', ' + userSelected;
      } else {
        this.selectedRoleWiseRecipients = val;
        this.selectedRoleWiseRecipientsName = userSelected;
      }
      this.closeRecipientModal(false);
      console.log(
        'clicked addRoleWiseUsers ::: ',
        this.selectedRoleWiseRecipients,
        userSelected
      );
    }, 100);
  }

  clearAllRoleWiseUsers() {
    var val = [];
    var j = 0;
    for (var c = 0; c <= 1; c++) {
      $(':checkbox:checked').each(function (i) {
        // var name = this.name;
        //console.log("Clear Value ------- ", $(this).val(), name ,"--", j);
        // $(this).attr('checked', false);
        if (c == 0) {
          if ($(this).val() == 'on') {
            $(this).trigger('click');
          }
        } else {
          if ($(this).val() != 'on') {
            $(this).trigger('click');
          }
        }
      });
    }

    this.selectedRoleWiseRecipients = [];
    this.selectedRoleWiseRecipientsName = '';
    console.log('clicked clearAllRoleWiseUsers');
    //$(".role-wise-modal").trigger("select");
  }

  callAccordion(roleID, eve) {
    if ($('.expand-icon-' + roleID).hasClass('fa-plus')) {
      $('.expand-icon-' + roleID).removeClass('fa-plus');
      $('.expand-icon-' + roleID).addClass('fa-minus');
      $('.sub-item-panel-' + roleID).addClass('showall');
    } else {
      $('.expand-icon-' + roleID).removeClass('fa-minus');
      $('.expand-icon-' + roleID).addClass('fa-plus');
      $('.sub-item-panel-' + roleID).removeClass('showall');
    }
    console.log('callAccordion===========', roleID, eve);
  }

  selectAllRoleUsers(data) {
    this.orderMainRoles = [];
    var self = this;
    this.orderMain.forEach(function (value, key) {
      if (data == key) {
        if (value) {
          self.orderMainRoles.push(key);
          $('.userRole-' + data).each(function () {
            var alt = $(this).attr('alt');
            self.order[alt] = true;
          });
        } else {
          $('.userRole-' + data).each(function () {
            var alt = $(this).attr('alt');
            self.order[alt] = false;
          });
        }
      }
    });

    setTimeout(() => {
      this.formated('staff');
    }, 100);
  }
  metaDataSet(uploadedFiles, callBack) {
    let metaData = {};
    for (var i = 0; i < uploadedFiles.length; i++) {
      metaData[i] = {
        attributes: {
          data: [
            {
              userId: this.userData.userCmisId,
              actualUserId: this.userData.userId,
              owner: this.userData.userCmisId,
              userType: 'owner',
              objectType: 'file',
              fileType: uploadedFiles[i].format,
              displayName: uploadedFiles[i].details.name,
              parentFolderId: 'nil',
              isDeleted: false,
              createdOn: new Date().getTime(),
              modifiedOn: new Date().getTime(),
              chatRoomId: 0,
              chatType: 'broadcast_message',
              sourceType: 'attachment',
              fileOriginalName: uploadedFiles[i].name,
              broadcastMessage: '',
            },
          ],
        },
      };
      if (i == uploadedFiles.length - 1) {
        callBack(metaData);
      }
    }
  }
  /* file upload starts*/
  onUploadOutput(output: UploadOutput): void {
    console.log('Output: ', output);
    let self = this;
    if (output.type === 'allAddedToQueue') {
      // when all files added in queue
      // uncomment this if you want to auto upload files when added
      // const event: UploadInput = {
      //   type: 'uploadAll',
      //   url: 'http://192.168.1.90/call-bell-hybrid-communication/cometchat/citus-health/v4/chatfile-upload.php',
      //   method: 'POST',
      //   headers: {
      //     'Authentication-Token': this._structureService.getCookie('authenticationToken'),
      //   },
      //   data: { user: '835' },
      //   concurrency: 0
      // };
      // console.log("Event:::: ",event);
      // this.uploadInput.emit(event);
    } else if (
      output.type === 'addedToQueue' &&
      typeof output.file !== 'undefined'
    ) {
      // add file to array when added
      if (output.file.size > 20971520) {
        //if( output.file.size > 1048576 ){
        $.notify(
          {
            message:
              'Sorry for the inconvenience! ' +
              output.file.name +
              ' file size more than 20 mb not supported.',
          },
          {
            type: 'danger',
          },
          {
            delay: 5000,
          }
        );
      } else if (!this.isValidFile(output.file.name)) {
        $.notify(
          {
            message:
              'Sorry for the inconvenience! ' +
              output.file.name +
              ' files types are not supported.',
          },
          {
            type: 'danger',
          },
          {
            delay: 5000,
          }
        );
      } else if (this.files.length >= 10) {
        $.notify(
          {
            message:
              "Sorry for the inconvenience! More than 10 files can't be uploaded at a time.",
          },
          {
            type: 'danger',
          },
          {
            delay: 5000,
          }
        );
      } else {
        this.files.push(output.file);
      }
    } else if (
      output.type === 'uploading' &&
      typeof output.file !== 'undefined'
    ) {
      // update current data in files array for uploading file
      const index = this.files.findIndex(
        (file) =>
          typeof output.file !== 'undefined' && file.id === output.file.id
      );
      this.files[index] = output.file;
    } else if (output.type === 'removed') {
      // remove file from array when removed
      this.files = this.files.filter(
        (file: UploadFile) => file !== output.file
      );
      console.log('this.files.length :', this.files, this.files.length);
      if (this.files.length == 0) {
        $('.dropify-clear').trigger('click');
        this.removeAllFiles();
      }
    } else if (output.type === 'dragOver') {
      this.dragOver = true;
    } else if (output.type === 'dragOut') {
      this.dragOver = false;
    } else if (output.type === 'drop') {
      this.dragOver = false;
    }

    if (output.type === 'done') {
      console.log('Done : ', output.file);
      if (
        output.file.response.msg &&
        output.file.response.view &&
        output.file.type
      ) {
        var imgdata = {
          id: output.file.fileIndex,
          details: output.file,
          name: output.file.response.msg,
          format: output.file.response.view,
          size: output.file.size,
          type: output.file.type,
        };
        this.selectedFileNames.push(imgdata);
        console.log('selectedFileNames: ', this.selectedFileNames);
        //console.log(this.files.length,'-----',this.selectedFileNames.length);
        if (this.files.length == this.selectedFileNames.length) {
          this.uploadFlag = true;
          //console.log(" **xxxxx*** ",this.pushNotifyMsg);
          this.metaDataSet(this.selectedFileNames, function (data) {
            let metaData = JSON.stringify(data);
            let fileUniqueName = 'multipleupload';
            self._structureService
              .uploadFileToCmis(fileUniqueName, metaData)
              .subscribe((filedata) => {
                let cmisFileData: any = filedata;
                cmisFileData = JSON.parse(
                  cmisFileData.data.uploadFileToCmis.cmisFileData
                );
                self.sendMasked(self.pushNotifyMsg, cmisFileData);
              });
          });
        } else {
          //NProgress.done();
        }
      } else {
        NProgress.done();
        this.resetMaskedMessageForm('');
        swal('Sent Failed!', 'Some error occured, please try again.', 'error');
      }
    }
  }
  isValidFile(filename) {
    var ext = filename.split('.').pop().toLowerCase();
    var allowedExt = [
      'gif',
      'png',
      'jpg',
      'jpeg',
      'jpe',
      'mpeg',
      'bmp',
      'mpg',
      'mpe',
      'avi',
      'mp4',
      'movie',
      'qt',
      'mov',
      'pdf',
      'mp3',
      'mp2',
      'doc',
      'docx',
      'word',
      'xl',
      'xls',
      'xlsx',
      'aac',
      'amr',
    ];
    if (allowedExt.indexOf(ext) >= 0) {
      console.log('S-ext-', ext);
      this.pushNotificationMessage(ext);
      return true;
    } else {
      console.log('E-ext-', ext);
      return false;
    }
  }
  pushNotificationMessage(ext) {
    var oneFile;
    var manyFile;
    console.log('push-> ', this.pushNotifyMsg, 'EXE', ext);
    var allowedExtImg = [
      'gif',
      'png',
      'jpg',
      'jpeg',
      'jpe',
      'mpeg',
      'bmp',
      'mpg',
      'mpe',
    ];
    var allowedExtVid = ['avi', 'mp4', 'movie', 'qt', 'mov'];
    var allowedExtPdf = ['pdf'];
    var allowedExtAud = ['mp3', 'mp2', 'aac', 'amr'];
    var allowedExtDoc = ['doc', 'docx', 'word', 'xl', 'xls', 'xlsx'];

    if (allowedExtImg.indexOf(ext) >= 0) {
      oneFile = 'Shared an image';
      manyFile = 'Shared images';
      if (this.pushNotifyMsg.indexOf(oneFile) >= 0) {
        this.pushNotifyMsg = this.pushNotifyMsg.replace(oneFile, manyFile);
      } else {
        if (this.pushNotifyMsg) {
          this.pushNotifyMsg = this.pushNotifyMsg + ', ' + oneFile;
        } else {
          this.pushNotifyMsg = oneFile;
        }
      }
    }
    if (allowedExtVid.indexOf(ext) >= 0) {
      oneFile = 'Shared a video';
      manyFile = 'Shared videos';
      if (this.pushNotifyMsg.indexOf(oneFile) >= 0) {
        this.pushNotifyMsg = this.pushNotifyMsg.replace(oneFile, manyFile);
      } else {
        if (this.pushNotifyMsg) {
          this.pushNotifyMsg = this.pushNotifyMsg + ', ' + oneFile;
        } else {
          this.pushNotifyMsg = oneFile;
        }
      }
    }
    if (allowedExtPdf.indexOf(ext) >= 0) {
      oneFile = 'Shared a pdf';
      manyFile = 'Shared pdfs';
      if (this.pushNotifyMsg.indexOf(oneFile) >= 0) {
        this.pushNotifyMsg = this.pushNotifyMsg.replace(oneFile, manyFile);
      } else {
        if (this.pushNotifyMsg) {
          this.pushNotifyMsg = this.pushNotifyMsg + ', ' + oneFile;
        } else {
          this.pushNotifyMsg = oneFile;
        }
      }
    }
    if (allowedExtAud.indexOf(ext) >= 0) {
      oneFile = 'Shared an audio';
      manyFile = 'Shared audios';
      if (this.pushNotifyMsg.indexOf(oneFile) >= 0) {
        this.pushNotifyMsg = this.pushNotifyMsg.replace(oneFile, manyFile);
      } else {
        if (this.pushNotifyMsg) {
          this.pushNotifyMsg = this.pushNotifyMsg + ', ' + oneFile;
        } else {
          this.pushNotifyMsg = oneFile;
        }
      }
    }
    if (allowedExtDoc.indexOf(ext) >= 0) {
      oneFile = 'Shared a document';
      manyFile = 'Shared documents';
      if (this.pushNotifyMsg.indexOf(manyFile) >= 0) {
        // no changes
      } else if (this.pushNotifyMsg.indexOf(oneFile) >= 0) {
        this.pushNotifyMsg = this.pushNotifyMsg.replace(oneFile, manyFile);
      } else {
        if (this.pushNotifyMsg) {
          this.pushNotifyMsg = this.pushNotifyMsg + ', ' + oneFile;
        } else {
          this.pushNotifyMsg = oneFile;
        }
      }
    }
    console.log('pushNotificationMessage:::::::::::: ', this.pushNotifyMsg);
  }
  startUpload(): void {
    const event: UploadInput = {
      type: 'uploadAll',
      url:
        this._structureService.apiBaseUrl +
        'citus-health/v4/chatfile-upload.php',
      //url: 'http://192.168.1.90/call-bell-hybrid-communication/cometchat/citus-health/v4/chatfile-upload.php',
      method: 'POST',
      headers: {
        'Authentication-Token': this._structureService.getCookie(
          'authenticationToken'
        ),
      },
      data: { user: this._structureService.getCookie('userId') },
    };
    this.uploadInput.emit(event);
    console.log('Check this : ', this.files, this.uploadInput, event);
  }
  resetMaskedMessageForm(data) {
    if (data) {
      var selectedOptionsLength = 0;
      var self = this;
      if (
        this.maskedMessageForm.value['messageSubject'] ||
        this.maskedMessageForm.value['messageText'] ||
        this.files.length > 0
      ) {
        swal(
          {
            title: 'Do you want to leave this page?',
            text: 'Changes you made may not be saved.',
            type: 'warning',
            showCancelButton: true,
            cancelButtonClass: 'btn-default',
            confirmButtonClass: 'btn-warning',
            confirmButtonText: 'Ok',
            closeOnConfirm: true,
          },
          function (isConfirm) {
            if (isConfirm) {
              self.resetMaskedMessageForm('');
            }
          }
        );
      } else {
        self.resetMaskedMessageForm('');
      }
    } else {
      this.files = [];
      this.selectedFileNames = [];
      this.removeAllFiles();
      console.log('A L:', this.files.length);
      $('#initiators').val(null).trigger('change');
      $('#recipients').val(null).trigger('change');
      var drEvent = $('.dropify').dropify();
      drEvent.on('dropify.beforeClear', function (event, element) {
        return true;
      });
      $('.dropify-clear').trigger('click');
      this.maskedMessageForm = this._formBuild.group({
        initiators: ['', Validators.required],
        recipients: ['', Validators.required],
        messageSubject: ['', Validators.required],
        messageText: ['', Validators.required],
        attachedFile: [''],
      });
      this.pushNotifyMsg = '';
      NProgress.done();
    }
    this.selectedRoleWiseRecipients = [];
    this.selectedRoleWiseRecipientsName = '';
    // $(':checkbox').each(function(i){

    // });
    this.filesAttached = {
      pdf: 0,
      document: 0,
      image: 0,
      audio: 0,
      video: 0,
    };
    this.cmisFileUploadData = [];
  }

  removeFile(id: string): void {
    this.uploadInput.emit({ type: 'remove', id: id });
  }

  removeAllFiles(): void {
    this.uploadInput.emit({ type: 'removeAll' });
  }

  /* file upload ends*/
  sendMasked(pushNotifyMsg, cmisFileData) {
    var self = this;
    var messageText = this.maskedMessageForm.value['messageText'];
    var imgPath = '';
    let filesAttachedFailed = '';
    var count = 0;
    if (cmisFileData) {
      for (var key in cmisFileData) {
        let FileData = JSON.parse(cmisFileData[key]);
        if (FileData.status == 200) {
          this.cmisFileUploadData.push(FileData.results);
          if (FileData.results.organizationId) {
            let type = FileData.results.attributes.data[0].fileType;
            if (type == 'image') {
              this.filesAttached.image = this.filesAttached.image + 1;
              var fileUrl =
                this.userData.cmisFileBaseUrl +
                FileData.results.attachment[0].attributes.fileshareId +
                '.json?type=thumbnail';
            } else if (type == 'document' || type == 'pdf') {
              if (type == 'document') {
                this.filesAttached.document = this.filesAttached.document + 1;
              } else {
                this.filesAttached.pdf = this.filesAttached.pdf + 1;
              }
              var fileUrl =
                this.userData.cmisFileBaseUrl +
                FileData.results.attachment[0].attributes.fileshareId +
                '.json?type=pdf';
            } else {
              if (type == 'video') {
                this.filesAttached.video = this.filesAttached.video + 1;
              } else {
                this.filesAttached.audio = this.filesAttached.audio + 1;
              }
              var fileUrl =
                this.userData.cmisFileBaseUrl +
                FileData.results.attachment[0].attributes.fileshareId +
                '.json';
            }
            const fileName = FileData.results.attributes.data[0].displayName;  
            this._messageBroadcastService.fileFormatTypeTofileTagSendBroadcast(
              this.selectedFileNames[key].details,
              fileUrl,
              fileName,
              this.selectedFileNames[key].format,
              FileData.results.attachment[0].attributes.mimeType,
              function (result, filedata, type, file) {
                count = count + 1;
                self.broadcastData.attachmentStr += result.message;
                if (self.selectedFileNames.length == count) {
                  self.sendMaskedMessageFinish(
                    filesAttachedFailed,
                    messageText
                  );
                }
              }
            );
          }
        } else {
          count = count + 1;
          if (filesAttachedFailed == '') {
            filesAttachedFailed = FileData.name;
          } else {
            filesAttachedFailed = filesAttachedFailed + ', ' + FileData.name;
          }
          if (self.selectedFileNames.length == count) {
            self.sendMaskedMessageFinish(filesAttachedFailed, messageText);
          }
        }
      }
    } else {
      self.sendMaskedMessageFinish(filesAttachedFailed, messageText);
    }
  }

  sendMaskedMessageFinish(filesAttachedFailed, messageText) {
    var self = this;
    console.log(this.broadcastData);

    this._MaskedMessageService
      .createChatroomFromMaskedMessage(this.broadcastData)
      .then((response: any) => {
        const users = getDistinctArray(response.data, 'userid');
        if (response.success) {
          var size = 0,
            key,
            configurationNotEnabled = false;
          if (
            users &&
            users.length &&
            'configurationNotEnabled' in users[0] &&
            users[0]['configurationNotEnabled'] == 1
          ) {
            configurationNotEnabled = true;
          } else {
            for (key in users) {
              if (users.hasOwnProperty(key)) size++;
            }
          }
          if (size > 0 && !configurationNotEnabled) {
            self._structureService.socket.emit('userPollingtoServer', users);
            var args = {
              type: 'messages',
              setCount: 2,
              chatroomId: users[0].chatroom_id,
              message: self.broadcastData.message,
              sentTime: (new Date().getTime() / 1000).toString(),
              selfUpdate: true,
            };
            self._SharedService.messagePollingSelfUpdate.emit({
              data: 'Polling:true',
              args: args,
            });
            var pushMessage = '';
            for (let key in this.filesAttached) {
              if (this.filesAttached[key] > 0) {
                if (this.filesAttached[key] == 1) {
                  switch (key) {
                    case 'pdf':
                      pushMessage = pushMessage + 'Shared a pdf';
                      break;
                    case 'document':
                      pushMessage = pushMessage + ', Shared a document';
                      break;
                    case 'image':
                      pushMessage = pushMessage + ', Shared an image';
                      break;
                    case 'audio':
                      pushMessage = pushMessage + ', Shared an audio';
                      break;
                    case 'video':
                      pushMessage = pushMessage + ', Shared a video';
                      break;
                  }
                } else {
                  switch (key) {
                    case 'pdf':
                      pushMessage = pushMessage + 'Shared pdfs';
                      break;
                    case 'document':
                      pushMessage = pushMessage + ', Shared documents';
                      break;
                    case 'image':
                      pushMessage = pushMessage + ', Shared images';
                      break;
                    case 'audio':
                      pushMessage = pushMessage + ', Shared audios';
                      break;
                    case 'video':
                      pushMessage = pushMessage + ', Shared videos';
                      break;
                  }
                }
              }
            }

            if (messageText) {
              pushMessage = messageText + '. ' + pushMessage;
            } else {
              pushMessage = pushMessage;
            }
            var toIds = this.selectedRoleWiseRecipients.join(', ');
            console.log(
              ' selectedRoleWiseRecipients ------------ ',
              this.selectedRoleWiseRecipients
            );
            var maskedchatRoomId = users[0].chatroom_id
              ? users[0].chatroom_id
              : 0;
            console.log('PUSH MESSAGE----------:', pushMessage);
            //self._structureService.sentPushNotification(users, 0, self.userData.displayName + ":" + pushMessage, '', '', '','');
            var deeplinking = {
              //"pushType": config.pushDeepLinkCategories.chat + '' + targetID,
              state: 'eventmenu.group-chat',
              stateParams: {
                targetID: maskedchatRoomId,
                targetName: 'group-chat',
              },
              activeMessage: {
                sent: new Date().getTime() / 1000,
                messageType: 2,
                baseId: 0,
                userid: self.userData.userId,
                fromName: '"' + self.userData.displayName + '"',
                message_group_id: 0,
                chatroomid: maskedchatRoomId,
                title: this.maskedMessageForm.value['messageSubject'],
                createdby: self.userData.userId,
              },
              priorityId: this.prioritySelected
            };
            const messageSendNotificationData = {
              sourceId: CONSTANTS.notificationSource.message,
              sourceCategoryId: CONSTANTS.notificationSourceCategory.messageSendNotification
            };
            self._structureService.sentPushNotification(
              users,
              0,
              'You have a new message to review',
              '',
              deeplinking,
              '',
              '',
              undefined,
              undefined,
              this.prioritySelected,
              undefined,
              messageSendNotificationData
            );
            this.prioritySelected = MessagePriority.NORMAL; // reset priority back to normal
            //var activityLogMessageBroadcast = self.userData.displayName + " send Masked message to" + rolesName;
            //var activityLogMessageBroadcast = self.userData.displayName + " send Masked message";
            var activityLogMessageBroadcast =
              'Masked message with chatroom id ' +
              maskedchatRoomId +
              ' sent by- ' +
              self.userData.displayName +
              ' to ' +
              toIds;
            var activityData = {
              activityName: 'Masked Message',
              activityType: 'messaging',
              activityDescription: activityLogMessageBroadcast,
            };
            this._structureService.trackActivity(activityData);
            var msg = 'Masked Message Sent Successfully.';
            if (filesAttachedFailed != '') {
              msg =
                msg + 'File upload failed for file(s) ' + filesAttachedFailed;
            }
            NProgress.done();
            swal('Sent Successfully!', msg, 'success');
            this.resetMaskedMessageForm('');
            this.clearAllRoleWiseUsers();
          } else {
            NProgress.done();
            if (configurationNotEnabled) {
              this._structureService.notifyMessage({
                messge:
                  'Warning!  This feature is not enabled for your tenant. Please contact support for more information',
                delay: 1000,
                type: 'warning',
              });
              this.router.navigate([self.userData.defaultPage || '/profile']);
            } else {
              swal(
                'Message Sending Failed!',
                'There are no recipient exits in the selected role.',
                'warning'
              );
            }
          }
        } else {
          NProgress.done();
          this.resetMaskedMessageForm('');
          var activityData = {
            activityName: 'Masked Message',
            activityType: 'communication',
            activityDescription: `Masked message sending failed due to ${response.status.message}`,
          };
          this._structureService.notifyMessage({
            messge: this._ToolTipService.getTranslateData(
              'MESSAGES.MSG_SENDING_FAILED'
            ),
            delay: 1000,
            type: 'danger',
          });
          this._structureService.trackActivity(activityData);
        }
      })
      .catch((ex) => {
        NProgress.done();
        this.resetMaskedMessageForm('');
      });
  }
  sendMaskedMessage() {
    let selectedInitiators = [];
    let selectedRecipients = [];
    this.selectedInitiators = $('#initiators').val();
    this.selectedRecipients = $('#recipients').val();
    if (navigator.onLine && this._structureService.socket.connected) {
      if (this.selectedInitiators) {
        this.selectedInitiators.forEach((element) => {
          var member = { id: '' };
          var id = element.substr(element.indexOf(':') + 1);
          id = id.replace(/'/g, '');
          member.id = id.replace(/\s/g, '');
          selectedInitiators.push(Number(member.id));
        });
      }
      if (this.selectedRecipients) {
        this.selectedRecipients.forEach((element) => {
          var member = { id: '' };
          var id = element.substr(element.indexOf(':') + 1);
          id = id.replace(/'/g, '');
          member.id = id.replace(/\s/g, '');
          selectedRecipients.push(Number(member.id));
        });
      }

      var messageSubject = this.maskedMessageForm.value['messageSubject'];

      var messageText = this.maskedMessageForm.value['messageText'];

      if (
        this.selectedRoleWiseRecipients.length > 0 &&
        (this.files.length > 0 ||
          (this.maskedMessageForm.value['messageText'] &&
            this.maskedMessageForm.value['messageSubject']))
      ) {
        // selectedInitiators.length > 0 &&
        this.broadcastData = {
          messageSubject: this.maskedMessageForm.value['messageSubject'],
          message: this.maskedMessageForm.value['messageText'],
          selectedRoles: selectedInitiators,
          selectedRolesPatient: this.selectedRoleWiseRecipients,
          attachmentStr: '',
          priorityId: this.prioritySelected
        };
        var self = this;
        var confirmMesssage = 'You are going to send this masked message';
        swal(
          {
            title: 'Are you sure?',
            text: confirmMesssage,
            type: 'warning',
            showCancelButton: true,
            cancelButtonClass: 'btn-default',
            confirmButtonClass: 'btn-warning',
            confirmButtonText: 'Ok',
            closeOnConfirm: true,
          },
          function (isConfirm) {
            if (isConfirm) {
              if (self.files.length > 0) {
                NProgress.start();
                self.startUpload();
              } else {
                NProgress.start();
                self.sendMasked('', null);
              }
            } else {
              //swal("Cancelled", "Your imaginary file is safe :)", "error");
            }
          }
        );
      } else if (this.selectedRoleWiseRecipients.length < 1) {
        $.notify(
          {
            message: 'Please choose recipient(s)',
          },
          {
            type: 'danger',
          },
          {
            delay: 5000,
          }
        );
      } else if (!this.maskedMessageForm.value['messageSubject']) {
        $.notify(
          {
            message: 'Please enter message subject',
          },
          {
            type: 'danger',
          },
          {
            delay: 5000,
          }
        );
      } else if (!this.maskedMessageForm.value['messageText']) {
        $.notify(
          {
            message: 'Please enter message',
          },
          {
            type: 'danger',
          },
          {
            delay: 5000,
          }
        );
      }

      var x = document.getElementsByClassName('btn-warning');
      x[0].setAttribute('id', 'unique_cnfrm_btn');
    } else {
      this._structureService.notifyMessage({
        messge: 'Your Connection Lost. Trying to Reconnect..',
        delay: 3000,
        type: 'warning',
      });
    }
  }

  eventHandlerSearch(e) {
    console.log('keyCode :', e, e.keyCode);

    if (e.keyCode === 8) {
      console.log('Back Btn here!');
    }
  }
  getSiteIds(siteId: any) {
    this.siteId = siteId.siteId;
    this.listMaskedMessage();
  }
  hideDropdown(hideItem: any) {
    this.hideSiteSelection = hideItem.hideItem;
  }
  selectedPriority(value) {
    this.prioritySelected = value;
  }
}
