import { Injectable } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import { Subject } from 'rxjs/Subject';
import 'rxjs/add/operator/toPromise';
import { StructureService } from '../structure.service';
@Injectable()
export class MaskedMessageService {
data;
sendMaskedMessageUrl;
maskedMessageReply;
iconPath;
  constructor(
    private _http: Http,
    private _structureService:StructureService
  ) {    
    this.sendMaskedMessageUrl = _structureService.apiBaseUrl+"citus-health/v4/create-chatroom-masked-message.php";
    this.maskedMessageReply = _structureService.apiBaseUrl+"citus-health/v4/masked-message-reply-from-patient.php";
  }  
  createChatroomFromMaskedMessage(data){
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.sendMaskedMessageUrl;
    let postData = { "maskedMessage":data,"platform": '', "language": (navigator.language ? navigator.language : '') };

       this._http.post(apiURL,postData,options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          resolve(result);
        }
        );
    });
    return promise;
  }
  maskMessagePatientReply(data){
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.maskedMessageReply + "?language=" + (navigator.language ? navigator.language : '');
       this._http.post(apiURL,data,options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          resolve(result);
        }
        );
    });
    return promise;

  }
}