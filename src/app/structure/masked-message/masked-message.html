<!-- START: dashboard alpha -->
<!-- START: dashboard alpha -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Send Masked Messages</strong>
        </span>

    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item">Send Masked Messages</li>
        </ol>
        <div class="row">
            <div class="col-lg-12">
                <div class="mb-5">            
                            
                    <form #messageBroacastForm="ngForm" action="#" [formGroup]="maskedMessageForm" class="form-horizontal" id="message-broadcast-form">
                        <div class="form-body">                            
                            <div class="form-group row" [hidden]="true">
                                <div class="col-md-3">
                                    <label class="control-label">Choose Initiator(s)* </label>
                                </div>
                                <div class="col-md-6">
                                    <select class="form-control select2-ini" formControlName="initiators" id="initiators" multiple required>
                                            <option *ngFor="let clinicians of clinicianData" value="{{clinicians.userid}}"> {{clinicians.displayname}} </option>
                                    </select>
                                </div>
                            </div>
                            <!-- <div class="form-group row" [hidden]="true">
                                <div class="col-md-3">
                                    <label class="control-label">Choose Recipient(s)* </label>
                                </div>
                                <div class="col-md-6">
                                    <select class="form-control select2-reci" formControlName="recipients" id="recipients" multiple required>
                                            <option *ngFor="let userList of usersListForMaskedMessage" value="{{userList.userid}}"> {{userList.displayname}} </option>
                                    </select>
                                </div>
                            </div> -->

                            <!-- <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Choose Recipient(s)* 1 </label>
                                </div>
                                <div class="col-md-6">
                                    <select class="form-control select2-reci js-example-basic-multiple" formControlName="recipients" id="recipients" multiple required>
                                        <optgroup *ngFor="let cliniciansRole of clinicianDataRoleWise" label="{{cliniciansRole.roleData.name}}" >
                                            <option *ngFor="let cliniciansRoleUser of cliniciansRole.userList" value="{{cliniciansRoleUser.id}}">{{cliniciansRoleUser.name}}</option>
                                        </optgroup>
                                    </select>
                                </div>
                            </div> -->

                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Choose Recipient(s)*</label>
                                </div>
                                <div class="col-md-6">
                                    <!-- <div class="uploads">
                                        <div class="upload-item" *ngFor="let selectedUserName of selectedRoleWiseRecipientsName">
                                            <div class="upload-item-top">
                                                <span class="filename">{{selectedUserName}}</span>
                                            </div>
                                        </div>
                                    </div> -->
                                    <input class="form-control" (click)="roleWiseUsersWithModel()"  id="choose_reciever" placeholder="Click here for choose Recipient(s)" value="{{selectedRoleWiseRecipientsName}}" >
                                </div>
                            </div>


                            <div class="form-group row">
                                <label class="col-md-3 control-label">Subject*</label>

                                <div class="col-md-6">
                                    <input type="text" class="form-control" formControlName="messageSubject" id="messageSubject" rows="3" placeholder="Subject">
                                </div>
                            </div> 

                            <div class="form-group row">
                                <label class="col-md-3 control-label">Message*</label>

                                <div class="col-md-6">
                                    <textarea class="form-control" formControlName="messageText" id="messageText" rows="3" placeholder="Message"></textarea>
                                </div>
                                <app-message-priority [messagePriority]= "prioritySelected" (selectedPriority)="selectedPriority($event)"></app-message-priority>
                            </div>
                            <div class="form-group row">
                                <label class="col-md-3 control-label">Attach File</label>
                                <div class="col-md-6">
                                    <div class="uploads" *ngIf="files?.length">
                                        <div class="upload-item" *ngFor="let f of files; let i = index;">
                                            <div class="upload-item-top">
                                                <span class="filename">{{ f.name }}</span>
                                                <div class="doc-file-remove">
                                                    <span class="x" (click)="removeFile(f.id)">x</span>         
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <label class="upload-button">
                                    <input type="file" class="dropify" ngFileSelect (uploadOutput)="onUploadOutput($event)" [uploadInput]="uploadInput" multiple data-allowed-file-extensions="gif png jpg jpeg jpe mpeg bmp mpg mpe avi mp4 movie qt mov pdf mp3 mp2 doc docx word xl xls xlsx aac amr" data-max-file-size="20M" >
                                    
                                    </label>
                                </div>
                            </div>                         
                        </div>
                        <!-- app.component.html -->
                        <div class="form-actions">
                            <button type="submit" (click)="sendMaskedMessage()" class="btn btn-primary swal-btn-info" id="send_masked_message">Send</button>
                            <button type="button" (click)="resetMaskedMessageForm('data')" class="btn btn-default" id="cancel_masked_message">Cancel</button> </div>
                    </form>
                    <!-- End Horizontal Form -->
                </div>
            </div>
        </div>
    </div>
</section>
<!-- ******************** Chatwith Model ************************* -->
<div class="modal fade bd-example-modal-lg forward-modal" id="roleWiseUsersWithModel" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="exampleModalLabel">Choose Recipient(s)</h4>
                <button type="button" class="close" data-dismiss="modal" (click)="closeRecipientModal(true)"aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                  </button>
            </div>
            <div class="form-group row filter-position" [hidden]="!hideSiteSelection">
                <label class="control-label assoc-label-position modal-title site-label">{{ 'LABELS.FILTER_BY_SITES' | translate }} </label>
                <span class="col-md-5">
                    <app-select-sites [events]="eventsSubject.asObservable()"  [filterType]=true [popupSelectSite]=true (siteIds)="getSiteIds($event)" (hideDropdown)="hideDropdown($event)">
                    </app-select-sites>
                </span>
            </div>
            <div class="modal-body">
                <div id="page-wrap">
                        <!-- <input class="form-control"  placeholder="Search Recipient(s)"  [(ngModel)]="searchName"> -->
                        <input class="form-control" id="recipientSearchName" #recipientSearch placeholder="Search Recipient(s)" [(ngModel)]="recipientSearchName" (keypress)="eventHandlerSearch($event)" />
                    <!-- <h2>Filter with {{searchName}}</h2> -->

                    <ul class="treeview treeview-section-ui">
                        <li *ngFor="let cliniciansRole of clinicianDataRoleWise | searchRolefilter : 'roleData.name' : recipientSearch.value " [hidden]="cliniciansRole.searchHideStatus" class="role-{{cliniciansRole.roleData.id}}" >
                            <i class="fa fa-plus expand-icon-{{cliniciansRole.roleData.id}}" (click)="callAccordion(cliniciansRole.roleData.id)" [ngClass]="{'fa-minus':recipientSearch.value , 'fa-plus':!recipientSearch.value}"></i>
                            <input type="checkbox" name="middle" id="role-{{cliniciansRole.roleData.id}}" [(ngModel)]="orderMain[cliniciansRole.roleData.id]" (change)="selectAllRoleUsers(cliniciansRole.roleData.id)">
                            <label for="middle" [ngClass]="orderMain[cliniciansRole.roleData.id] ? 'custom-checked' : 'custom-unchecked'">{{cliniciansRole.roleData.name | pluralizefilter }}</label>
                    
                            <ul class="sub-item-panel sub-item-panel-{{cliniciansRole.roleData.id}}" [ngClass]="{'showall':recipientSearch.value}">
                                    <li *ngFor="let cliniciansRoleUser of cliniciansRole.filteredUserList |searchfilterroletreeview: 'name' : (cliniciansRole.filterUserStatus ? recipientSearch.value : '')" [hidden]="cliniciansRoleUser.searchHideStatus"><!--[hidden]="cliniciansRoleUser.searchHideStatus"-->
                                        <input type="checkbox" name="cliniciansRoleUser[]" id="role-{{cliniciansRole.roleData.id}}-{{cliniciansRoleUser.id}}" value="{{cliniciansRoleUser.id}}" class="cliniciansRoleUser userRole-{{cliniciansRole.roleData.id}}" alt="{{cliniciansRoleUser.id}}" title='{{(cliniciansRoleUser.naTagNames && cliniciansRoleUser.naTagNames != "" ? cliniciansRoleUser.name + " (" + cliniciansRoleUser.naTagNames + ")" : cliniciansRoleUser.name)}}' attr.data-patient-dob='{{((cliniciansRoleUser.dob && cliniciansRoleUser.dob != "" && cliniciansRoleUser.role == "Patient") ? cliniciansRoleUser.dob : "" )}}' attr.data-patient-mrn='{{((cliniciansRoleUser.IdentityValue && cliniciansRoleUser.IdentityValue != "" && cliniciansRoleUser.role == "Patient") ?  cliniciansRoleUser.IdentityValue  : "")}}' attr.data-care-dob="{{((cliniciansRoleUser.caregiver_dob && cliniciansRoleUser.caregiver_dob != '' && cliniciansRoleUser.role == 'Caregiver') ? cliniciansRoleUser.caregiver_dob : '')}}" attr.data-care-mrn="{{((cliniciansRoleUser.caregiver_identityvalue && cliniciansRoleUser.caregiver_identityvalue != '' && cliniciansRoleUser.role == 'Caregiver') ? cliniciansRoleUser.caregiver_identityvalue : '')}}" [(ngModel)]="order[cliniciansRoleUser.id]">
                                        <label for="{{cliniciansRoleUser.name}}" [ngClass]="order[cliniciansRoleUser.id] ? 'custom-checked' : 'custom-unchecked'">{{(cliniciansRoleUser.caregiver_displayname ? cliniciansRoleUser.caregiver_displayname + " (" + cliniciansRoleUser.name + ")" : cliniciansRoleUser.name)}} <span *ngIf="cliniciansRoleUser.naTagNames && cliniciansRoleUser.naTagNames != ''">({{cliniciansRoleUser.naTagNames}}) </span> 
                                            <span *ngIf="cliniciansRoleUser.dob && cliniciansRoleUser.dob != '' && cliniciansRoleUser.role == 'Patient'" >-{{cliniciansRoleUser.dob}} </span><span *ngIf="cliniciansRoleUser.IdentityValue && cliniciansRoleUser.IdentityValue != '' && cliniciansRoleUser.role == 'Patient'">[MRN: {{cliniciansRoleUser.IdentityValue}}] </span>
                                            <span *ngIf="cliniciansRoleUser.caregiver_dob && cliniciansRoleUser.caregiver_dob != '' && cliniciansRoleUser.role == 'Caregiver'" >-{{cliniciansRoleUser.caregiver_dob}} </span><span *ngIf="cliniciansRoleUser.caregiver_identityvalue && cliniciansRoleUser.caregiver_identityvalue != '' && cliniciansRoleUser.role == 'Caregiver'">[MRN: {{cliniciansRoleUser.caregiver_identityvalue}}] </span></label>

                                        </li>
                            </ul>
                        </li>
                        <li *ngIf="maskedDiscussionRecipientRoles.length == 0 " >No Recipients Available</li>
                    </ul>
                     
                 <!--  <ul class="treeview treeview-section-ui">
                     
                     <li *ngFor="let cliniciansRole of clinicianDataRoleWise | searchRolefilter : 'roleData.name' : recipientSearch.value " [hidden]="cliniciansRole.searchHideStatus" class="role-{{cliniciansRole.roleData.id}}" >
                        <i class="fa fa-plus expand-icon-{{cliniciansRole.roleData.id}}" (click)="callAccordion(cliniciansRole.roleData.id)" [ngClass]="{'fa-minus':recipientSearch.value , 'fa-plus':!recipientSearch.value}"></i>
                        <input type="checkbox" name="middle" id="role-{{cliniciansRole.roleData.id}}">
                        <label for="middle" class="custom-unchecked">{{cliniciansRole.roleData.name | pluralizefilter }}</label>
                        
                        <ul class="sub-item-panel sub-item-panel-{{cliniciansRole.roleData.id}}" [ngClass]="{'showall':recipientSearch.value}">
                             <li *ngFor="let cliniciansRoleUser of cliniciansRole.filteredUserList | searchfilter: 'name' : recipientSearch.value">
                                 <input type="checkbox" name="cliniciansRoleUser[]" id="role-{{cliniciansRole.roleData.id}}-{{cliniciansRoleUser.id}}" value="{{cliniciansRoleUser.id}}" class="cliniciansRoleUser" title="{{cliniciansRoleUser.name}}" >
                                 <label for="{{cliniciansRoleUser.name}}" (click)="makeCheckedOrUnchecked(cliniciansRoleUser)" [ngClass]="{'custom-unchecked':!cliniciansRoleUser.isChecked,'custom-checked':cliniciansRoleUser.isChecked}" >{{(cliniciansRoleUser.caregiver_displayname ? cliniciansRoleUser.caregiver_displayname + " (" + cliniciansRoleUser.name + ")" : cliniciansRoleUser.name)}}</label>
                             </li>
                        </ul>
                    </li>
                    <li *ngIf="maskedDiscussionRecipientRoles.length == '0'" >No Recipients Available</li>

                
                 </ul> -->
                 
                 </div>
            </div>
            <div class="modal-footer">
                <span class="chatwith-modal-tip">
                    <button type="button" class="btn btn-secondary" (click)="clearAllRoleWiseUsers()" id="clear_recp" >Clear All</button>
                    <button type="button" class="btn btn-secondary" (click)="addRoleWiseUsers()" id="add_recp">Add</button>
                     <!-- <img src="./assets/modules/dummy-assets/common/img/chatwith-tip.png" />
                     <span class="modal-footer-text">Click the user/group name to chat with </span> -->
                </span>
                
                <button type="button" class="btn btn-secondary role-wise-modal" data-dismiss="modal" id="close_recp" (click)="closeModal()">Close</button>
            </div>
        </div>
    </div>
</div>
<!-- *********************End Section*************************** -->
<!-- END: forms/basic-forms-elements -->
<!-- END: dashboard alpha -->
