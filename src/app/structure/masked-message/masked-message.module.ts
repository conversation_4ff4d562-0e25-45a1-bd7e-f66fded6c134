import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { BrowserModule } from '@angular/platform-browser';
import { ReactiveFormsModule } from '@angular/forms';
import { FormsModule } from '@angular/forms';
import { MaskedMessageComponent } from './masked-message.citushealth';
import { NgUploaderModule } from 'ngx-uploader';
import { SearchFilterPipe, SearchRoleFilterSchedulePipe, pluralizeScheduleFilterPipe } from './masked-message-search.pipes';
import { SearchRoleFilterPipe,SearchFilterRoleTreeViewPipe,searchfiltermrn } from './masked-message-search.pipes';
import { pluralizeFilterPipe } from './masked-message-search.pipes';
import { AuthGuard } from '../../guard/auth.guard';
import { SharedModule } from '../shared/sharedModule';
export const routes: Routes = [
  { path: 'masked/message', component: MaskedMessageComponent, canActivate:[AuthGuard],data: {
		checkRoutingConfig: 'enable_message_center',checkRoutingPrivileges:'superAdmin,sendMaskedMessage'
	} }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    BrowserModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    NgUploaderModule,
    SharedModule,

  ],
  declarations: [
    MaskedMessageComponent,
    SearchFilterPipe,
    SearchRoleFilterPipe,
    pluralizeFilterPipe,
    SearchFilterRoleTreeViewPipe,
    SearchRoleFilterSchedulePipe,
    pluralizeScheduleFilterPipe,
    searchfiltermrn
    
  ],
  providers: []
  

})

export class MaskedMessageModule { }
