import { Component, OnInit,ElementRef, Renderer } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { StructureService } from '../structure.service';
import { Subject } from 'rxjs';
import { ToolTipService } from '../tool-tip.service';
import { SharedService } from '../../structure/shared/sharedServices';
declare var $: any;
import { isBlank } from 'app/utils/utils';
import { isNull } from "util";
declare var swal: any;
@Component({
  selector: 'app-add-external-integration',
  templateUrl: './add-external-integration.component.html'
})
export class AddExternalIntegrationComponent implements OnInit {
  externalSystem: FormGroup;
  name;
  code;
  fileNameExpression;
  uniqueIdentifier;
  externalSystemOption;
  emptyName;
  emptyuniqueIdentifier;
  emptyCode;
  submitted : boolean;
  emptyextSysValue = false;
  extSysValue;
  emptyLabel;
  preferences;
  labelInInvite;
  siteChoosed : boolean;
  hideSiteSelection : boolean;
  siteRequired : boolean = false;
  showInEditor;
  eventsSubject: Subject<void> = new Subject<void>();
  sites = [];
  siteId;
  dynamic;
  filterType = true;
  showInInvite;
  fileNameLabel;
  requiredInInvite;
  extInegration;
  tenantAssoc = '0';
  showuserInvite;

  userDetails:any;
  userData:any = {};

  externalSystemValues = [];
  txtESIValue = '';
  intESIIndex = null;
  newEsiValue = '';
  esiAlreadyAdded = 0;

  constructor(private route: ActivatedRoute,
    private router: Router,
    private _structureService: StructureService,
    private _ToolTipService: ToolTipService,
    private _formBuild: FormBuilder,
    public _sharedService: SharedService) { 
      this.userDetails = this._structureService.userDetails;
      this.userData = JSON.parse(this.userDetails);
    }

  ngOnInit() {
    this.showInEditor;
    this.emptyName=false;
    this.emptyLabel=false;
    this.fileNameLabel=false;
    this.emptyuniqueIdentifier=false;
    this.emptyCode=false;
    this.emptyextSysValue=false;
    this.showuserInvite =1;
    this.externalSystem = this._formBuild.group({
       name : ['',[Validators.required, this.noWhitespaceValidator]],
       code : ['',[Validators.required, this.noWhitespaceValidator]],
       fileNameExpression : ['',[Validators.required, this.noWhitespaceValidator]],
       uniqueIdentifier : ['',Validators.required],
       labelShowInEditPage:['',Validators.required],
       showEditor: [''],
       showInInvite: [false],
       requiredInInvite: [false],
       extSysValue:[''],
       esiValues:[null],
       tenantAssoc:['0'],
       userType:['patient']
       
    });
     this._structureService.getExternalSystems().then(( data ) => {
     console.log(data['getSessionTenant']['externalIntegrationSystems']);
     this.extInegration=JSON.parse(JSON.stringify(data['getSessionTenant']['externalIntegrationSystems']));
     });
  }

  noWhitespaceValidator(control: FormControl) {
    let isWhitespace = (control.value || '').trim().length === 0;
    let isValid = !isWhitespace;
    return isValid ? null : { 'whitespace': true }
}
getSiteIds(siteId: any) {
  this.siteId = siteId.siteId.toString();
  this.siteRequired = ((((isBlank(this.siteId) || this.siteId=='0') && this.submitted) ||  ((isBlank(this.siteId) || this.siteId=='0') && this.siteChoosed)) && this.tenantAssoc == '2');
    this.siteChoosed = true;
  console.log("this.siteId",this.siteId);
}
getSiteName(data:any){
  console.log(data);
}
hideDropdown(hideItem : any){
    this.hideSiteSelection = hideItem.hideItem;
}

 togglePreference(preference, value) {
    this.externalSystem.patchValue({labelShowInEditPage:''});
    this.externalSystem.patchValue({extSysValue:''});
    if (preference === 'showEditor') {
        this.showInEditor=value;
      this.externalSystem.patchValue({
        showEditor: value
      });
    } else if(preference === 'requiredInInvite') {
      this.requiredInInvite=value;
      this.externalSystem.patchValue({
        requiredInInvite: value
      });
    } else if(preference === 'showInInvite'){
      this.showInInvite=value;
      this.externalSystem.patchValue({
        showInInvite: value
      });
    } else if(preference === 'tenantAssoc') {
      if(value != '2')
      this.siteRequired = false;
      if(value != '0')
      this.emptyLabel = false;
      this.tenantAssoc=value;
      this.externalSystem.patchValue({
        tenantAssoc: value
      });
      if(value) {
        console.log('yes');
        this.externalSystem.get('extSysValue').setValidators([Validators.required]);
        this.externalSystem.get('extSysValue').updateValueAndValidity();
      } else {
        console.log('no');
        this.externalSystem.get('extSysValue').clearValidators();
        this.externalSystem.get('extSysValue').updateValueAndValidity();
      }
    }
    else if(preference === 'userType') {
      if(value !='patient'){
       this.showuserInvite =0;
       this.showInInvite = false;
       this.externalSystem.patchValue({
        showInInvite: false
      });
       this.requiredInInvite =false;
       this.externalSystem.patchValue({
        requiredInInvite: false
      });

      } else {
        this.showuserInvite =1;
      }
      console.log(value);
     // console.log(userType)
    }
 }
  createExternalSystem(){
    this.submitted = true;
    this.siteRequired = false;
    if(this.externalSystem.value.showEditor){
      this.showInEditor=1;
    }
    else{
      this.showInEditor=0;
      this.externalSystem.value.showEditor = false;
    }
    this.siteRequired = ((isBlank(this.siteId) || this.siteId=='0') && this.tenantAssoc == '2');
    if(this.siteRequired && this.tenantAssoc == '2'){
        $('.addextintegration').removeAttr('disabled');
        return false;
    }
    this.emptyName=false;
    this.emptyLabel=false;
    this.fileNameLabel=false;
    this.emptyuniqueIdentifier=false;
    this.emptyCode=false;
    this.emptyextSysValue=false;
    this.name = this.externalSystem.value['name'];
    this.labelInInvite = this.externalSystem.value['labelShowInEditPage'];
    this.code =  this.externalSystem.value['code'];
    this.fileNameExpression =  this.externalSystem.value['fileNameExpression'];
    this.uniqueIdentifier =  this.externalSystem.value['uniqueIdentifier'];
    this.tenantAssoc =  this.externalSystem.value['tenantAssoc'];
    
    if(this.name && this.code && this.uniqueIdentifier  && this.fileNameExpression && ((this.tenantAssoc == '1' && this.externalSystemValues.length > 0) || (this.tenantAssoc == '0' && this.labelInInvite) || (this.tenantAssoc == '2' && this.siteId && this.externalSystemValues.length > 0))){
      console.log('#');
      const formObjData = this.externalSystem.value;
      console.log(formObjData);
          let inArray=[];
          if(this.extInegration) {
            inArray = this.extInegration.filter((row)=>{
              if(row.name.toLowerCase()==this.name.toLowerCase()&&row.code.toLowerCase()==this.code.toLowerCase()){
              return true;
              }
              });
          }
          
          console.log(inArray);
          let inuArray=[];
          if(this.extInegration) {
            inuArray = this.extInegration.filter((row)=>{
            if(row.uniqueIdentifier.toLowerCase()==this.uniqueIdentifier.toLowerCase()){
            return true;
            }
            });
          }

          console.log('###');
          if(this.tenantAssoc != "2"){}
          if(this.tenantAssoc != "2" && inArray && inArray.length>0){
              var notify = $.notify('External System already exists');
              setTimeout(()=> {
              notify.update({'type': 'warning', 'message': '<strong>External System already exists</strong>'});   
              }, 1000); 
          }
          else if(this.tenantAssoc != "2" && inuArray && inuArray.length>0){
               var notify = $.notify('Identity In External System already exists');
              setTimeout(()=> {
              notify.update({'type': 'warning', 'message': '<strong>Identity In External System already exists</strong>'});   
              }, 1000); 
          }else{
            let ex :any;
            if(this.tenantAssoc == "2" && inuArray && inuArray.length>0){
             ex = inuArray.map(x=>x.siteId);
            console.log("sites",ex,Number(this.siteId),ex.includes(Number(this.siteId)));
            }
            if(ex && ex.length > 0 && ex.includes(Number(this.siteId))){
            var notify = $.notify('Identity In External System already exists');
            setTimeout(()=> {
            notify.update({'type': 'warning', 'message': '<strong>Identity In External System already exists</strong>'});   
            }, 1000); 
            }else{
            $('.addextintegration').attr( 'disabled', 'disabled' );
              console.log('Show in Patient Editor FALSE');
              formObjData.siteId = this.siteId;

              if(this.tenantAssoc != '0')
                formObjData.esiValues = this.externalSystemValues;
              
              this._structureService.createExternalIntegrationSystem(formObjData).subscribe(
              (data) => {
                if(!isNull(data.data.createExternalIntegrationSystem)){
                  
                //check the esi already added for other branch or not
                if(data.data.createExternalIntegrationSystem.name == "ESI_EXIST"){
                  //log the error
                  let activityData = {
                      activityName: "Error Create External Integration System",
                      activityType: "esi exist",
                      activityDescription: `ESI value already exists in the same tenant`
                    }; 
                    //show message that the esi already exist
                    this._structureService.notifyMessage({
                      messge: this._ToolTipService.getTranslateData('ERROR_MESSAGES.EXTERNAL_SYSTEM_ALREADY_EXIST'),
                      delay: 4000,
                      type: 'warning'
                    });                    
                    this._structureService.trackActivity(activityData);
                  }
                  else {

              console.log(data)
              var notify = $.notify(' External Integration System created successfully');
              setTimeout(function () {
              notify.update({ 'type': 'success', 'message': '<strong>  External Integration System created successfully </strong>' });
              }, 1000);

              var activityLogMessage = this.userData.displayName + " created External Integration System" ;    
              var activityData = {
                activityName: "Create External Integration System",
                activityType: "External Integration System",
                activityDescription: activityLogMessage
              };
              console.log("activityData :::", activityData);
              this._structureService.trackActivity(activityData);

              this.router.navigate(['/external-integration/' + this._sharedService.externalIntegrationToken]);
            }
              $('.addextintegration').removeAttr('disabled');
            }else {
                let activityData = {
                    activityName: "Error Create External Integration System",
                    activityType: "usertagtype",
                    activityDescription: `Error occured while creating External Integration System by ${this.userData.displayName} due to invalid input.`
                  }; 
                  this._structureService.notifyMessage({
                    messge: this._ToolTipService.getTranslateData('MESSAGES.ERROR_MSG_INVALID_INPUT'),
                    delay: 1000,
                    type: 'warning'
                  });
                  $('.addextintegration').removeAttr('disabled');
                  this._structureService.trackActivity(activityData);
            }
              }
              );
            }
          }
  
    }
    else{
      if(!this.name)
        this.emptyName=true;
      if(!this.code)
        this.emptyCode=true;
      if(!this.uniqueIdentifier)
        this.emptyuniqueIdentifier=true;
      if(!this.labelInInvite && this.tenantAssoc == '0')
        this.emptyLabel=true;
      if(!this.fileNameExpression)
        this.fileNameLabel=true;
        if(!this.extSysValue && this.tenantAssoc != '0')
        this.emptyextSysValue=true;
    }
  }

  /**
   * Add external system value 
   * On click add button add external system value to the array
   */
  addExternalSystemValue(){
    if(this.txtESIValue != '' && !this.checkValueExistOrNot(this.txtESIValue))
    {
      const arrEsiValue = { id : null, esiValue : this.txtESIValue, status : 1 };
      this.externalSystemValues.push(arrEsiValue);
      this.closeExternalSystemValueEdit();
    }
  }

  /**
   * Edit already added values
   * @param index Index of the array externalSystemValues which is edited
   */
  editExternalSystemValue(index){
    if(this.newEsiValue && !this.checkValueExistOrNot(this.newEsiValue, index)){
      this.externalSystemValues[index].esiValue = this.newEsiValue;
      this.closeExternalSystemValueEdit();
    }
  }

  /**
   * Check the esi value already added or not 
   * @param esiValue (string) the esi value entered
   * @param id (int) index of the array externalSystemValues
   * @returns boolean : 0 if value exist , 1 if value not exist
   */
  checkValueExistOrNot(esiValue, id = null){
    try {
      this.externalSystemValues.forEach((data, key) => {
        if (data.esiValue == esiValue && key != id){
          throw 'VALUE_EXIST'; //throw exception if value already exist
        }
      });
      return 0;
    } catch (e) {
      //set flag to show the message if value already exist
      if (e == 'VALUE_EXIST') {
        this.esiAlreadyAdded = 1;
        return 1;
      }
    }
  }

  /**
   * Load already added data to the edit mode
   * @param index  Index of the array externalSystemValues which is going to edit
   */
  loadExternalSystemValue(index){
    this.intESIIndex = index;
    this.newEsiValue = this.externalSystemValues[index].esiValue ;
  }
  /**
   * Reset the UI from edit mode
   */
  closeExternalSystemValueEdit(){
    this.intESIIndex = null;
    this.newEsiValue = '';
    this.txtESIValue = '';
    this.esiAlreadyAdded = 0;
  }

  /**
   * Delete external system value 
   * On click delete icon in the list, delete corresponding details from the array
   * @param index integer - array index
   */
  deleteExternalSystemValue(index){  
    //ask confirmation before deleting the value
    swal({
      title: this._ToolTipService.getTranslateData('MESSAGES.ARE_YOU_SURE'),
      text: this._ToolTipService.getTranslateData('MESSAGES.DELETE_CONFIRM')+", " + this.externalSystemValues[index].esiValue,
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      cancelButtonText: "Cancel",
      confirmButtonText: "OK",
      closeOnConfirm: true
    }, (confirm) => {
      if(confirm) {
        //change the status to 0 if already saved to db 
        //or else just remove the details from array
        this.externalSystemValues.splice(index, 1);
        this.closeExternalSystemValueEdit();
      }
    });
  }

}
