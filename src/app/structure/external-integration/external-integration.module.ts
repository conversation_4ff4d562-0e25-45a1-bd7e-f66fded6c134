import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { ExternalIntegrationComponent } from './external-integration.component';
import { FormsModule } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { AddExternalIntegrationComponent } from './add-external-integration.component';
import { EditExternalIntegrationComponent } from './edit-external-integration.component';
import { SharedModule } from '../shared/sharedModule';
import { AuthGuard } from '../../guard/auth.guard';
export const routes: Routes = [
  {
    path: 'external-integration/:token', component: ExternalIntegrationComponent, canActivate: [AuthGuard], data: {
      checkRoutingPrivileges: 'manageTenants,superAdmin'
    }  
  },
  { path: 'add-external-integration', component: AddExternalIntegrationComponent },
  { path: 'edit-external-integration/:id', component: EditExternalIntegrationComponent },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    RouterModule.forChild(routes)
  ],
  declarations: [
    ExternalIntegrationComponent,
    AddExternalIntegrationComponent,
    EditExternalIntegrationComponent,
  ]

})

export class  ExternalIntegrationModule { }
