import { Component, OnInit, ElementRef, Renderer,ViewChild,  } from '@angular/core';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { isBlank } from 'app/utils/utils';
import { SharedService } from '../../structure/shared/sharedServices';

declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;
declare var jsPDF: any;
declare var swal:any;

@Component({
  selector: 'app-external-integration',
  templateUrl: './external-integration.component.html'
})
export class ExternalIntegrationComponent implements OnInit {

  dTable;
  extInegration=[];
  activeRecord;
  dataLoadingMsg=true;
  editIntegrationData=[];
  deleted;
  systemId;

  userDetails:any;
  userData:any = {};
  isDetail:any =false;
  token;
  constructor( private route: ActivatedRoute,
    private router: Router,
    private renderer: Renderer,
    private elementRef: ElementRef,
    private _structureService: StructureService,
    private _ToolTipService: ToolTipService,
    private _formBuild: FormBuilder,
    public _sharedService: SharedService) 
    { 
    this.userDetails = this._structureService.userDetails;
    this.userData = JSON.parse(this.userDetails);
    renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
      var targetElement;
      if(event.target.id == 'chkbox') {
       // this.detailsView();
      // alert('ok');
       //console.log(elementRef.nativeElement);
      }
       
      else if(event.target.id == 'deleteDetails')
      {
        var IsEnterClicked=false;
        $(document).keypress((event)=> {
          if (event.keyCode == 13) {
              console.log("Enter pressed");
              IsEnterClicked=true;           
          }
        });
        swal({
          title: "Are you sure?",
          text: "You are going to delete the External System",
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default",
          confirmButtonClass: "btn-warning",
          confirmButtonText: "Ok",
          closeOnConfirm: true
      }, (confirm) => {
        console.log('IsEnterClicked',IsEnterClicked);
        if(IsEnterClicked){
          IsEnterClicked=false;
            swal.close();   
            return false;
        }
        if(confirm) {

       this.deleteExternalSystem();
      }

      });
      } else if($(event.target).hasClass('edit-external-integration') ) {
        targetElement = $(event.target);
        let extSysId = targetElement.data("ext-sys-id");
        this.router.navigate(['/edit-external-integration/'+extSysId])
      }
    
    });
    this.route.params.subscribe((params: Params) => {
      if (params.token) {
          this.token = params.token;
          this.getAllInegrations();
      }
    });

    }

  ngOnInit() {
    
    
  }
  getAllInegrations(){
    if (this.token == this._sharedService.externalIntegrationToken) {
      this._structureService.getExternalSystems().then(( data ) => {
      console.log(data['getSessionTenant']);
      this.extInegration=data['getSessionTenant'] ? JSON.parse(JSON.stringify(data['getSessionTenant']['externalIntegrationSystems'])) : [];
      for(var i=0; i<this.extInegration.length;i++){
        console.log("=======================");
        console.log(this.extInegration[i]);
        this.editIntegrationData[this.extInegration[i].externalSystemId]=this.extInegration[i];
        console.log("=======================");
      }
        console.log("=======================");
        console.log(this.editIntegrationData);
        localStorage.setItem('IntegrationData', JSON.stringify(this.editIntegrationData));
        console.log("=======================");
        console.log(this.extInegration);
        this.populateDatatble();
      })
    } else {
      this.router.navigate(['external-integration']);
    }

    //this.extInegration.push({tennantId:10,extIntId:1002,name:'Mediware',code:'MRN',fileformat:'{Mediware}-{MRN}'});
    //this.extInegration.push({tennantId:11,extIntId:1003,name:'CPR+',code:'MRN',fileformat:'{CPR+}-{MRN}'});   
    

  }
  addExternalInt(){
    alert('ok');
  }

deleteExternalSystem(){
  this.systemId=this.activeRecord.externalSystemId;
        this.deleted=1;
        var formObjData={}
      this._structureService.updateExternalIntegrationSystem(formObjData,this.systemId,this.deleted).subscribe(
          (data) => {
           console.log(data)
              this.extInegration.filter((elem)=>{
                if(elem==this.activeRecord)
                  {
                    this.extInegration.splice(this.extInegration.indexOf(this.activeRecord), 1);
                   
                    this.populateDatatble();
                 var activityLogMessage = this.userData.displayName + " deleted External Integration System" ;    
                  var activityData = {
                    activityName: "Delete External Integration System",
                    activityType: "External Integration System",
                    activityDescription: activityLogMessage
                  };
                  console.log("activityData :::", activityData);
                  this._structureService.trackActivity(activityData);



                  var notify = $.notify(' External Integration System Deleted successfully');
                setTimeout(function () {
                    notify.update({ 'type': 'success', 'message': '<strong>  External Integration System Deleted successfully </strong>' });
                }, 1000);
                this.router.navigate(['/external-integration/' + this._sharedService.externalIntegrationToken]);
                  }
                });
          }
        );
}



  populateDatatble(){
    if (this.dTable) {
      this.dTable.destroy();
    }
    var isTrue = false;    
    if(this.extInegration.length > 99){
      isTrue = true;
    }
    this.dTable = $('#dtExtInt').DataTable({           
      autoWidth: false,
      "order": [[ 4, "desc" ]],
      responsive: true,
      //bDeferRender:true,
      //processing: true,
      // oLanguage: {
      // sLoadingRecords: "Please wait - loading..."
      // },
      retrieve: true,
      //pagination: true,
      serching: true,  
      paging: isTrue,
      bInfo: isTrue,
      lengthMenu: [[100, 250, 500, 1000, -1], [100, 250, 500, 1000, 'All']],            
      data: this.extInegration,
      fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
        $(nRow).on('click', () => {
          console.log(aData);
          this.activeRecord = aData;
        });
      },
      columns: [
        {title: "#"},
        {title: "Account ID", data: 'accountId'},
        {title: "System Name", data: 'name'},
        {title: "System Code", data: 'code'},
        {title: "Identity In External System", data: 'uniqueIdentifier'},
        {title: "File Name Expression", data: 'filenameExpression'},
        {title: "Show In Editor", data: 'showEditor'},
        {title: "Association Type", data: 'tenantAssoc'},
        //  {title: "Show In Invite", data: 'showInInvite'},
        {title: "Actions"}
      ],
      columnDefs: [
      {
        data: null,
        orderable: false,
        width: "5%",
        targets: 0,
       },
       { 
         data: null,
         targets: 1,
         width: "5%",
          "visible": false,
      },
      { 
        data: null,
        targets: 2,
        width: "15%",
        render:function(document,type,row){
          if(row.name)
          return row.name;
          else
          return "";
 
         }
        
      },
      { 
        data: null,
        targets: 3,
        width: "15%",
      },
      { 
        data: null,
        targets: 4,
        width: "15%",
      },            
      { 
        data: null,
        targets: 5,
        width: "20%"
      },
      { 
        data: null,
        targets: 6,
        width: "5%",
        render:function(document,type,row){
         if(document ==1){
           //return parsejson1.summarizeOutcomeMeasure;
         return '<span class="badge badge-success mr-2 mb-2">Yes</span>';
        }else{
         return '<span class="badge badge-danger mr-2 mb-2">No</span>';
        }
         
       }
     },
     { 
      data: null,
      targets: 7,
      width: "5%",
      render:function(document,type,row){
       if(row.tenantAssoc==0){
       return "User";
      }else if (row.tenantAssoc==1){
       return "Tenant";
      } else if (row.tenantAssoc==2){
        if(!isBlank(row.site)){
            return "Site (" +row.site.name+")";
          }
          else
          {
            return "Site";
          }
      }
       
     }
    },
    //  { 
    //   data: null,
    //   targets: 7,
    //   width: "5%",
    //   render:function(document,type,row){
    //    if(document ==1){
    //      //return parsejson1.summarizeOutcomeMeasure;
    //    return '<span class="badge badge-success mr-2 mb-2">Yes</span>';
    //   }else{
    //    return '<span class="badge badge-danger mr-2 mb-2">No</span>';
    //   }
       
    //  }
    // },
     {
          data: null,
          orderable: false,
          render: function (document, type, row) {
          let actions = '';                
          //href="#/edit-external-integration/${document.externalSystemId}" routerLink="/edit-external-integration/${document.externalSystemId}"
          actions += `<a href="javascript: void(0);" class="cat__core__link--underlined mr-3 edit-external-integration" data-ext-sys-id="${document.externalSystemId}" id="viewdetails" ><i class="icmn-pencil"></i> Edit</a><a class="cat__core__link--underlined mr-3" id="deleteDetails" ><small><i class="icmn-cross"></i></small> Delete</a> `
          return actions;
        },
        width: "15%",
        targets: 8
      }]
    });

    this.dTable.on( 'order.dt search.dt', () => {
      this.dTable.column(0, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
          cell.innerHTML = i+1;
      });
    }).draw();
    this.dataLoadingMsg=false;
  }

}
