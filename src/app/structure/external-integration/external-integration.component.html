<style>
  .action-button-panel {
      width: 14%;
  }
  
  .action-button-panel a {
      background: #399fb6;
      border-radius: 5px;
      color: #fff;
      width: 20px;
      height: 20px;
      margin-right: 10px;
      padding: 5px;
  }
   #details{
       background-color: white;
       display: none;
   }
  .signed-docs-table td {
      vertical-align: middle;
  }
  
  .sd-modal-overlay {
      background-color: rgba(0, 0, 0, 0.5) !important;
  }
  

  .data-cnt{
    /* width: 600px;
    margin: auto; */
  }
  .data-cnt label{
    color: #1d6472;
  }
  .data-cnt  p{
    color: #818181;
  }
  .data-cnt .form-group{
    clear: both;
  }
  .tags-sec span{
        border: 1px solid #c1c1c1;
    padding: 2px 12px;
    margin-right: 10px;
    border-radius: 10px;
    margin-bottom: 6px;
    float: left;

  }
</style>

<!-- START: tables/datatables -->
<section class="card">
  <div class="card-header">
      <span class="cat__core__title">
          <strong>External Integrations</strong>
          <a [routerLink]="['/add-external-integration']" [hidden]="token != _sharedService.externalIntegrationToken" class="pull-right btn btn-sm btn-primary">Add External System <i class="ml-1"></i></a>
      </span>
  </div>
  <div class="card-block">
      <ol class="breadcrumb">
          <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>          
          <li class="breadcrumb-item">External Integrations</li>
      </ol>

      <div class="row" [hidden]="isDetail">
          <div class="col-lg-12">
              <div class="mb-5">
                  <div id="example1_wrapper" class="dataTables_wrapper container-fluid dt-bootstrap4 no-footer">
                      <div class="row">
                          <div class="col-sm-12 col-md-6">                            
                          </div>
                      </div>
                  </div>
                  <div class="wait-loading" *ngIf="dataLoadingMsg">
                        <img src="./assets/img/loader/loading.gif" />
                </div>                
                  <table class="table table-hover" id="dtExtInt" width="100%"></table>
              </div>
          </div>

      </div>
  </div>
  <div>
    <canvas id="signCanvas" width="100" height="150"></canvas>
  </div>
</section>
<!-- END: tables/datatables -->

