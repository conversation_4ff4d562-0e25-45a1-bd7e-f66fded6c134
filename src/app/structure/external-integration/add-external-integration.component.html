<!-- START: tables/datatables -->
<section class="card">
  <div class="card-header">
    <span class="cat__core__title">
      <strong>{{ 'LABELS.EXTERNAL_SYSTEM' | translate }} </strong>
      <!-- <a [routerLink]="['/supplies/add-inventory-type']" class="pull-right btn btn-sm btn-primary">Add New Staff <i class="ml-1"></i></a> -->
    </span>
  </div>
  <div class="card-block">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
      <li class="breadcrumb-item">
        <a [routerLink]="['/external-integration', _sharedService.externalIntegrationToken]">External Integrations</a>
      </li>
      <li class="breadcrumb-item">{{ 'LABELS.EXTERNAL_SYSTEM' | translate }}</li>
    </ol>
    <div class="row">
      <div class="col-lg-12">
        <!-- <h5 class="text-black"><strong>Tag Definitions</strong></h5>
              <p class="text-muted">Element: read <a href="https://datatables.net/" target="_blank">official documentation<small><i class="icmn-link ml-1"></i></small></a></p> -->
        <div class="mb-5">
          <form
            class="form-horizontal"
            action="#"
            [formGroup]="externalSystem"
            #f1="ngForm"
          >
            <div class="form-body">
              <!--<div class="form-group row">
                              <div class="col-md-3">
                                  <label class="control-label">Account ID <i class="tenant-id icmn-info" data-toggle="tooltip" data-placement="right"></i></label>
                              </div>
                              <div class="col-md-6">
                                  <input class="form-control" type="text" placeholder="Account ID"  name="tenantID"  id="tenantID" >                                

                              </div>
                          </div>
                          <div class="form-group row">
                            <div class="col-md-3">
                                <label class="control-label">External System ID <i class="tenant-id icmn-info" data-toggle="tooltip" data-placement="right"></i></label>
                            </div>
                            <div class="col-md-6">
                                <input class="form-control" type="text" placeholder="External Integration ID"  name="tenantID"  id="tenantID" >                                

                            </div>
                          </div>-->
              <div class="form-group row">
                <div class="col-md-3">
                  <label class="control-label"
                    >System Name*<i chToolTip="EIName"></i
                  ></label>
                </div>
                <div class="col-md-6">
                  <input
                    formControlName="name"
                    class="form-control"
                    type="text"
                    placeholder="System Name"
                    name="tenantID"
                    id="tenantID"
                  />
                  <div
                    *ngIf="
                      (externalSystem.controls['name'].errors &&
                        (externalSystem.controls.name?.dirty ||
                          externalSystem.controls.name?.touched)) ||
                      emptyName
                    "
                    class="alert alert-danger"
                  >
                    System Name cannot be empty
                  </div>
                </div>
              </div>
              <div class="form-group row">
                <div class="col-md-3">
                  <label class="control-label"
                    >System Code* <i chToolTip="EICode"></i
                  ></label>
                </div>
                <div class="col-md-6">
                  <input
                    formControlName="code"
                    class="form-control"
                    type="text"
                    placeholder="System Code"
                    name="tenantID"
                    id="tenantID"
                  />
                  <div
                    *ngIf="
                      (externalSystem.controls['code'].errors &&
                        (externalSystem.controls.code?.dirty ||
                          externalSystem.controls.code?.touched)) ||
                      emptyCode
                    "
                    class="alert alert-danger"
                  >
                    System Code cannot be empty
                  </div>
                </div>
              </div>
              <div class="form-group row">
                <div class="col-md-3">
                  <label class="control-label">File Name Expression</label>
                </div>
                <div class="col-md-6">
                  <input
                    formControlName="fileNameExpression"
                    class="form-control"
                    type="text"
                    placeholder="File Name Expression"
                    name="tenantID"
                    id="tenantID"
                  />
                  <div
                    *ngIf="
                      externalSystem.controls['fileNameExpression'].errors &&
                      (externalSystem.controls.fileNameExpression?.dirty ||
                        externalSystem.controls.fileNameExpression?.touched)
                    "
                    class="alert alert-danger"
                  >
                    File Name Expression cannot be empty
                  </div>
                </div>
              </div>
              <div class="form-group row">
                <div class="col-md-3">
                  <label class="control-label"
                    >Identity In External System* <i chToolTip="EIPCode"></i
                  ></label>
                </div>
                <div class="col-md-6">
                  <input
                    formControlName="uniqueIdentifier"
                    class="form-control"
                    type="text"
                    placeholder="Identity In External System"
                    name="unique-identifier"
                    id="unique-identifier"
                  />
                  <div
                    *ngIf="
                      (externalSystem.controls['uniqueIdentifier'].errors &&
                        (externalSystem.controls.uniqueIdentifier?.dirty ||
                          externalSystem.controls.uniqueIdentifier?.touched)) ||
                      emptyuniqueIdentifier
                    "
                    class="alert alert-danger"
                  >
                    Identity In External System cannot be empty
                  </div>
                </div>
              </div>
<!--               <div class="form-group row">
                <div class="col-md-3">
                  <label class="control-label">Tenant Association </label>
                </div>
                <div class="btn-group col-md-6">
                  <button
                    aria-pressed="true"
                    class="btn btn-outline-success btn-sm"
                    [ngClass]="{ active: tenantAssoc }"
                    (click)="togglePreference('tenantAssoc', true)"
                  >
                    Yes
                  </button>
                  <button
                    aria-pressed="true"
                    class="btn btn-outline-default btn-sm"
                    [ngClass]="{ active: !tenantAssoc }"
                    (click)="togglePreference('tenantAssoc', false)"
                  >
                    No
                  </button>
                </div>
              </div> -->
              <div class="form-group row">
                <div class="col-md-3">
                    <label class="control-label">{{ 'LABELS.ASSOCIATION_TYPE' | translate }} <i chToolTip="AssociationType"></i></label>
                </div>
            
                <div class="col-md-6">
                    <select class="form-control" formControlName="tenantAssoc" id="tenantAssoc" (change)="
                    togglePreference('tenantAssoc', $event.target.value)
                  ">
                        <option value="1" selected>Tenant</option>
                        <option value="0">User</option>
                        <option value="2" *ngIf="(userData && userData.config && userData.config.enable_multisite == '1')">Site</option>
                    </select>
                </div>
            </div>
              <div [hidden]="(externalSystem.controls['tenantAssoc'].value == 1 || externalSystem.controls['tenantAssoc'].value == 2)">
                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="control-label">User Type</label>
                  </div>
                  <div class="col-md-6">
                    <select
                      class="form-control"
                      formControlName="userType"
                      (change)="
                        togglePreference('userType', $event.target.value)
                      "
                    >
                      <option value="patient" selected>Patient</option>
                      <option value="staff" selected>Staff</option>
                      <option value="partner" selected>Partner</option>
                    </select>
                  </div>
                </div>
                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="control-label"
                      >Label show in edit page <i chToolTip="EIPLabel"></i
                    ></label>
                  </div>
                  <div class="col-md-6">
                    <input
                      formControlName="labelShowInEditPage"
                      class="form-control"
                      type="text"
                      placeholder="Label show in edit page"
                      name="labelShowInEditPage"
                      id="labelShowInEditPage"
                    />
                    <div
                      *ngIf="
                        ((externalSystem.controls['labelShowInEditPage']
                          .errors &&
                          (externalSystem.controls.labelShowInEditPage?.dirty ||
                            externalSystem.controls.labelShowInEditPage
                              ?.touched)) ||
                        emptyLabel) && externalSystem.controls['tenantAssoc'].value == 0
                      "
                      class="alert alert-danger"
                    >
                      Label show in edit page cannot be empty
                    </div>
                  </div>
                </div>
                <!-- <div class="form-group row" *ngIf="showuserInvite == 1" >
                                    <div class="col-md-3">
                                        <label class="control-label">Show in User Invite <i chToolTip="showPatientInvite"></i></label>
                                    </div>
                                    <div class="btn-group col-md-6">
                                        <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': showInInvite}" (click)="togglePreference('showInInvite',true)">
                                                            Yes
                                            </button>
                                        <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !showInInvite}" (click)="togglePreference('showInInvite',false)">
                                                                No
                                            </button>
                                    </div>
                                </div> -->
                <!-- <div class="form-group row" *ngIf="showuserInvite == 1">
                                    <div class="col-md-3">
                                        <label class="control-label">Required in User Invite <i chToolTip="reqPatientInvite"></i></label>
                                    </div>
                                    <div class="btn-group col-md-6">
                                        <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': requiredInInvite}" (click)="togglePreference('requiredInInvite',true)">
                                                            Yes
                                            </button>
                                        <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !requiredInInvite}" (click)="togglePreference('requiredInInvite',false)">
                                                                No
                                            </button>
                                    </div>
                                </div> -->
                <div class="form-group row" [hidden]="(externalSystem.controls['tenantAssoc'].value == 1 || externalSystem.controls['tenantAssoc'].value ==2)">
                  <div class="col-md-3">
                    <label class="control-label"
                      >Show in User Editor <i chToolTip="EIPatienteditor"></i
                    ></label>
                  </div>
                  <div class="btn-group col-md-6">
                    <button
                      aria-pressed="true"
                      class="btn btn-outline-success btn-sm"
                      [ngClass]="{ active: showInEditor }"
                      (click)="togglePreference('showEditor', true)"
                    >
                      Yes
                    </button>
                    <button
                      aria-pressed="true"
                      class="btn btn-outline-default btn-sm"
                      [ngClass]="{ active: !showInEditor }"
                      (click)="togglePreference('showEditor', false)"
                    >
                      No
                    </button>
                  </div>
                </div>
              </div>
              <div class="form-group row" id="select-site"  [hidden]="!hideSiteSelection || externalSystem.controls['tenantAssoc'].value == 1 || externalSystem.controls['tenantAssoc'].value ==0">
                <label class="col-md-3 control-label"> Site *
                    <i chToolTip="site"></i>
                </label>
                <div class="col-md-6">
                    <app-select-sites [events]="eventsSubject.asObservable()" [hideApplyFilter]=true [singleSelection]="true" [siteSelection]="true" (siteIds)="getSiteIds($event)" (siteName)="getSiteName($event)" (hideDropdown)="hideDropdown($event)">
                    </app-select-sites>
                    <div class="alert alert-danger site-position" *ngIf="siteRequired && externalSystem.controls['tenantAssoc'].value == 2">
                        Please select site. 
                    </div>
                </div>                
            </div>

            <div class="form-group row" [hidden]="(externalSystem.controls['tenantAssoc'].value ==0)">
              <div class="col-md-3">
                <label class="control-label">{{ 'LABELS.EXTERNAL_SYSTEM_VALUE' | translate }} *</label>
              </div>
              <div class="col-md-6">
                <div class="input-group mb-0">
                  <input type="text" class="form-control" [value]="txtESIValue" (input)="txtESIValue=$event.target.value"
                    aria-describedby="button-addon2" placeholder="{{ 'LABELS.EXTERNAL_SYSTEM_VALUE' | translate }}">
                  <ul class="list-inline m-2">
                    <li class="list-inline-item">
                      <button type="button" data-toggle="tooltip" data-placement="top" [disabled]="!txtESIValue" class="btn btn-primary btn-sm" (click)="addExternalSystemValue()">{{ 'BUTTONS.ADD' | translate }}</button>
                    </li>
                    <li class="list-inline-item">
                      <button type="button" data-toggle="tooltip" data-placement="top" class="btn btn-default btn-sm" (click)="txtESIValue = ''">{{ 'BUTTONS.CLEAR' | translate }}</button>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
            
            <div class="form-group row" [hidden]="(externalSystem.controls['tenantAssoc'].value ==0) && externalSystemValues.length > 0">
              <div class="col-md-3"></div>
              <div class="col-md-6">
            
                <div class="input-group mb-1" *ngFor="let esi of externalSystemValues; let i = index;">

                  <input type="text" class="form-control" *ngIf="i != intESIIndex" disabled value="{{esi.esiValue}}" aria-describedby="button-addon2" >

                  <input type="text" class="form-control" *ngIf="i == intESIIndex" value="{{newEsiValue}}"
                    (input)="newEsiValue=$event.target.value" aria-describedby="button-addon2" placeholder="{{ 'LABELS.EXTERNAL_SYSTEM_VALUE' | translate }}">

                  <ul class="list-inline m-2">
                    <li class="list-inline-item">            
                      <button type="button" data-toggle="tooltip" *ngIf="i == intESIIndex" data-placement="top" title="{{ 'BUTTONS.UPDATE' | translate }}"
                        class="btn btn-primary btn-sm" (click)="editExternalSystemValue(i)"><i class="fa fa-check"></i></button>

                        <button type="button" data-toggle="tooltip" *ngIf="i == intESIIndex" data-placement="top" title="{{ 'BUTTONS.CLOSE' | translate }}"
                          class="btn btn-warning btn-sm" (click)="closeExternalSystemValueEdit()"><i class="fa fa-close"></i></button>
            
                      <button type="button" data-toggle="tooltip" *ngIf="i != intESIIndex" data-placement="top" title="{{ 'BUTTONS.EDIT' | translate }}"
                        class="btn btn-outline btn-sm" (click)="loadExternalSystemValue(i)"><i class="fa fa-edit"></i></button>
            
                      <button type="button" data-toggle="tooltip" *ngIf="i != intESIIndex" data-placement="top" title="{{ 'BUTTONS.DELETE' | translate }}"
                        class="btn btn-outline btn-sm" (click)="deleteExternalSystemValue(i)"><i class="fa fa-trash"></i></button>            
                    </li>
                  </ul>
                </div>

                <div class="alert alert-danger" *ngIf="(f1.submitted && externalSystemValues.length == 0  && externalSystem.controls['tenantAssoc'].value != 0)">
                  {{ 'VALIDATION_MESSAGES.EXTERNAL_SYSTEM_NOT_EMPTY' | translate }}
                </div>
                <div class="alert alert-danger" *ngIf="esiAlreadyAdded">
                  {{ 'VALIDATION_MESSAGES.EXTERNAL_SYSTEM_ALREADY_ADDED' | translate }}
                </div>
              </div>
            </div>

              <div class="form-group row">
                <div class="col-md-6">
                  <button
                    class="btn btn-primary addextintegration"
                    (click)="createExternalSystem()"
                  >
                    Add
                  </button>
                  <button
                    class="btn btn-default"
                    type="button"
                    [routerLink]="['/external-integration', _sharedService.externalIntegrationToken]"
                  >
                    Cancel
                  </button>
                  <button
                    type="reset"
                    style="display:none;"
                    id="hideReset"
                    class="btn btn-success"
                  >
                    Reset
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- END: tables/datatables -->
