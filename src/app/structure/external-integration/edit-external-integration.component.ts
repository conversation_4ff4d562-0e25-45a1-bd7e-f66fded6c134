import { Component, OnInit,ElementRef, Renderer } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { SharedService } from '../../structure/shared/sharedServices';
declare var $: any;
import { Subject } from 'rxjs';
import { isBlank } from 'app/utils/utils';
declare var swal: any;
@Component({
  selector: 'app-add-external-integration',
  templateUrl: './edit-external-integration.component.html'
})
export class EditExternalIntegrationComponent implements OnInit {
  externalSystem: FormGroup;
  name;
  code;
  fileNameExpression;
  uniqueIdentifier;
  systemId;
  systemData;
  hideSiteSelection : boolean;
  siteRequired : boolean = false;
  eventsSubject: Subject<void> = new Subject<void>();
  sites = [];
  siteId;
  editSiteData = "";
  siteChoosed : boolean;
  dynamic=false;
  filterType = true;
  submitted : boolean;
  emptyLabel=false;
  currentExternalSysrtem;
  deleted;
  showInEditor;
  showInInvite;
  requiredInInvite;
  labelInInvite;
  editor;
  emptyextSysValue = false;
  extSysValue;
  emptyName;
  emptyCode;
  emptyuniqueIdentifier;
  fileNameLabel;
  extInegration;
  tenantAssoc ='0';
  showuserInvite;

  userDetails:any;
  userData:any = {};
  isDetail=false;

  externalSystemValues = [];
  txtESIValue = '';
  intESIIndex = null;
  newEsiValue = '';
  intActiveEsi = 0;
  esiAlreadyAdded = 0;

  constructor(private route: ActivatedRoute,
    private router: Router,
    private _structureService: StructureService,
    private _formBuild: FormBuilder,
    private _ToolTipService: ToolTipService,
    public _sharedService: SharedService) {
      this.userDetails = this._structureService.userDetails;
      this.userData = JSON.parse(this.userDetails);
     }

  ngOnInit() {
    this.emptyName=false;
    this.emptyLabel=false;
    this.emptyextSysValue=false;
    this.fileNameLabel=false;
    this.emptyuniqueIdentifier=false;
    this.emptyCode=false;
    var retrievedObject = localStorage.getItem('IntegrationData');

//console.log('IntegrationData: ', JSON.parse(retrievedObject));

     this.systemData=JSON.parse(retrievedObject);
     this.route.params.subscribe((params: Params) => {

        this.systemId = params['id'];
    });

    this.currentExternalSysrtem= this.systemData[this.systemId];

    //get external system values to a global array
    if( this.currentExternalSysrtem.esiValues ){
      this.externalSystemValues = this.currentExternalSysrtem.esiValues;
      this.intActiveEsi = this.externalSystemValues.length;
      this.externalSystemValues.forEach(function(v) // TODO: Need to handle this from the apollo client connection itself
      { 
        delete v.__typename 
      }); 
    }
    
   if((this.currentExternalSysrtem.userType !='patient') &&(this.currentExternalSysrtem.userType !='')){
    this.showuserInvite =0;
    this.showInInvite=false;
    this.requiredInInvite=false;
  } else {
    this.showuserInvite =1;
  }
/*     if(this.currentExternalSysrtem.tenantAssoc==1){
      this.tenantAssoc=true;
    }
    else{
      this.tenantAssoc=false;
    } */
    this.tenantAssoc=this.currentExternalSysrtem.tenantAssoc;
    if(this.currentExternalSysrtem.showEditor==1){
    this.showInEditor=true;
    }
    else{
      this.showInEditor=false;
    }
  if(this.currentExternalSysrtem.showInInvite==1){
    this.showInInvite=true;
    }
    else{
      this.showInInvite=false;
    }
    if(this.currentExternalSysrtem.requiredInInvite==1){
    this.requiredInInvite=true;
    }
    else{
      this.requiredInInvite=false;
    }

    this.externalSystem = this._formBuild.group({
       name : [this.currentExternalSysrtem.name,[Validators.required, this.noWhitespaceValidator]],
       code : [this.currentExternalSysrtem.code,[Validators.required, this.noWhitespaceValidator]],
       fileNameExpression : [this.currentExternalSysrtem.filenameExpression,[Validators.required, this.noWhitespaceValidator]],
       uniqueIdentifier : [this.currentExternalSysrtem.uniqueIdentifier,Validators.required],
       labelShowInEditPage:[this.currentExternalSysrtem.labelShowInEditPage,Validators.required],
       showInInvite: this.showInInvite,
       requiredInInvite: this.requiredInInvite,
       extSysValue:[this.currentExternalSysrtem.extSysValue],
       esiValues:[this.currentExternalSysrtem.esiValues],
       tenantAssoc:[(this.tenantAssoc) ? this.tenantAssoc.toString() : '0'],
       userType:[(this.currentExternalSysrtem.userType)?this.currentExternalSysrtem.userType:'patient']
      
    });
    if(this.currentExternalSysrtem.siteId){
      this.editSiteData = this.currentExternalSysrtem.siteId.toString();
      this.dynamic = (!isBlank(this.editSiteData)) ? true : false;
    }
    if(this.tenantAssoc) {
      this.externalSystem.get('extSysValue').setValidators([Validators.required]);
      this.externalSystem.get('extSysValue').updateValueAndValidity();
    } else {
      this.externalSystem.get('extSysValue').clearValidators();
      this.externalSystem.get('extSysValue').updateValueAndValidity();
    }

    if(this.showInInvite) {
      console.log('yes');
      this.externalSystem.get('labelShowInEditPage').setValidators([Validators.required]);
      this.externalSystem.get('labelShowInEditPage').updateValueAndValidity();
    } else {
      console.log('no');
      this.externalSystem.get('labelShowInEditPage').clearValidators();
      this.externalSystem.get('labelShowInEditPage').updateValueAndValidity();
    }

         this._structureService.getExternalSystems().then(( data ) => {
     console.log(data['getSessionTenant']['externalIntegrationSystems']);
     this.extInegration=JSON.parse(JSON.stringify(data['getSessionTenant']['externalIntegrationSystems']));
     });
     
  }

  noWhitespaceValidator(control: FormControl) {
    let isWhitespace = (control.value || '').trim().length === 0;
    let isValid = !isWhitespace;
    return isValid ? null : { 'whitespace': true }
}
getSiteIds(siteId: any) {
  this.siteId = siteId.siteId.toString();
  this.siteRequired = ((((isBlank(this.siteId) || this.siteId=='0') && this.submitted) ||  ((isBlank(this.siteId) || this.siteId=='0') && this.siteChoosed)) && this.tenantAssoc == '2');
    this.siteChoosed = true;
  console.log("this.siteId",this.siteId);
}
getSiteName(data:any){
  console.log(data);
}
hideDropdown(hideItem : any){
    this.hideSiteSelection = hideItem.hideItem;
}
  togglePreference(preference, value) {
    if (preference === 'showEditor') {
      this.showInEditor=value;
      if(value){
        this.externalSystem.patchValue({
        showEditor: 1
        });
      }
      else{
        this.externalSystem.patchValue({
        showEditor: 0
        });
      }
    }else if(preference === 'requiredInInvite') {
      this.requiredInInvite=value;
      this.externalSystem.patchValue({
        requiredInInvite: value
      });
    } else if(preference === 'showInInvite'){
      this.showInInvite=value;
      this.externalSystem.patchValue({
        showInInvite: value
      });

      if(value) {
        console.log('yes');
        this.externalSystem.get('labelShowInEditPage').setValidators([Validators.required]);
        this.externalSystem.get('labelShowInEditPage').updateValueAndValidity();
      } else {
        console.log('no');
        this.externalSystem.get('labelShowInEditPage').clearValidators();
        this.externalSystem.get('labelShowInEditPage').updateValueAndValidity();
      }

    } else if(preference === 'tenantAssoc') {
      if(value != '2')
      this.siteRequired = false;
      if(value != '0')
      this.emptyLabel = false;
      console.log(value);
      this.tenantAssoc=value;
      this.externalSystem.patchValue({
        tenantAssoc: value
      });
      if(value) {
        console.log('yes');
        this.externalSystem.get('extSysValue').setValidators([Validators.required]);
        this.externalSystem.get('extSysValue').updateValueAndValidity();
      } else {
        console.log('no');
        this.externalSystem.get('extSysValue').clearValidators();
        this.externalSystem.get('extSysValue').updateValueAndValidity();
      }
    } else if(preference === 'userType') {
      if(value !='patient'){
       this.showuserInvite =0;

       this.showInInvite=false;
       this.externalSystem.patchValue({
         showInInvite: false
       });

       this.requiredInInvite=false;
      this.externalSystem.patchValue({
        requiredInInvite: false
      });

      } else {
        this.showuserInvite =1;
      }
      console.log(value);
     // console.log(userType)
    }
  }

  updateExternalSystem(f){
    this.submitted = true;
    
    /*if(!f.valid) {
      if(this.showInInvite) {
        var notify = $.notify('Label show in edit page is required.');
        setTimeout(()=> {
        notify.update({'type': 'warning', 'message': '<strong>Label show in edit page is required.</strong>'});   
        }, 1000);
      }
      return false;
    }*/
    this.siteRequired = ((isBlank(this.siteId) || this.siteId=='0') && this.tenantAssoc == '2');
    if(this.siteRequired && this.tenantAssoc == '2'){
        return false;
    }
    this.emptyName=false;
    this.emptyLabel=false;
    this.emptyextSysValue=false;
    this.fileNameLabel=false;
    this.emptyuniqueIdentifier=false;
    this.emptyCode=false;
    this.name = this.externalSystem.value['name'];
    this.code =  this.externalSystem.value['code'];
    this.fileNameExpression =  this.externalSystem.value['fileNameExpression'];
    this.uniqueIdentifier =  this.externalSystem.value['uniqueIdentifier'];
    this.labelInInvite =  this.externalSystem.value['labelShowInEditPage'];
    this.extSysValue =  this.externalSystem.value['extSysValue'];      

    if(this.name && this.code && this.fileNameExpression && this.uniqueIdentifier && ((this.tenantAssoc == '1' && this.intActiveEsi > 0) || (this.tenantAssoc == '0' && this.labelInInvite) || (this.tenantAssoc == '2' && this.siteId && this.intActiveEsi > 0))){
      const formObjData = this.externalSystem.value;
          let inArray=[];
          inArray = this.extInegration.filter((row)=>{
            
          if(row.name.toLowerCase()==this.name.toLowerCase()&&row.code.toLowerCase()==this.code.toLowerCase()&&row.externalSystemId!=this.systemId){
            console.log(row.externalSystemId)
            console.log(this.systemId)
         
          return true;
          }
        });
          console.log("=========================");
          console.log(inArray);
          let inuArray=[];
          inuArray = this.extInegration.filter((row)=>{
          if(row.uniqueIdentifier.toLowerCase()==this.uniqueIdentifier.toLowerCase()&&row.externalSystemId!=this.systemId){
            console.log(row.externalSystemId)
            console.log(this.systemId)
          return true;
          }
        });
          console.log("=========================");
          console.log(inuArray);



      if(this.showInEditor){
        formObjData.showInEditor=1;
      }
      else{
         formObjData.showInEditor=0;
      }
      formObjData.siteId = this.siteId;

      if(this.tenantAssoc != '0')
        formObjData.esiValues = this.externalSystemValues;
        
      this.deleted=0;

       if(this.tenantAssoc != "2" && inArray && inArray.length>0){
              var notify = $.notify('External System already exists');
              setTimeout(()=> {
              notify.update({'type': 'warning', 'message': '<strong>External System already exists</strong>'});   
              }, 1000); 
          }
          else if(this.tenantAssoc != "2" && inuArray && inuArray.length>0){
               var notify = $.notify('Identity In External System already exists');
              setTimeout(()=> {
              notify.update({'type': 'warning', 'message': '<strong>Identity In External System already exists</strong>'});   
              }, 1000); 
          }
          else{
            let ex :any;
            if(this.tenantAssoc == "2" && inuArray && inuArray.length>0){
             ex = inuArray.map(x=>x.siteId);
            console.log("sites",ex,Number(this.siteId),ex.includes(Number(this.siteId)));
            }
            if(ex && ex.length > 0 && ex.includes(Number(this.siteId))){
            var notify = $.notify('Identity In External System already exists');
            setTimeout(()=> {
            notify.update({'type': 'warning', 'message': '<strong>Identity In External System already exists</strong>'});   
            }, 1000); 
            }else{
            this._structureService.updateExternalIntegrationSystem(formObjData,this.systemId,this.deleted).subscribe(
                (data) => {
                //check the esi already added for other branch or not
                if(data.data.updateExternalIntegrationSystem.name == "ESI_EXIST"){
                  //log the error
                  let activityData = {
                      activityName: "Error Create External Integration System",
                      activityType: "esi exist",
                      activityDescription: `ESI value already exists in the same tenant`
                    }; 
                    //show message that the esi already exist
                    this._structureService.notifyMessage({
                      messge: this._ToolTipService.getTranslateData('ERROR_MESSAGES.EXTERNAL_SYSTEM_ALREADY_EXIST'),
                      delay: 4000,
                      type: 'warning'
                    });                    
                    this._structureService.trackActivity(activityData);
                }
                else {
                  var notify = $.notify(' External Integration System Updated successfully');
                  setTimeout(function () {
                      notify.update({ 'type': 'success', 'message': '<strong>  External Integration System Updated successfully </strong>' });
                  }, 1000);

                  var activityLogMessage = this.userData.displayName + " updated External Integration System" ;    
                  var activityData = {
                    activityName: "Update External Integration System",
                    activityType: "External Integration System",
                    activityDescription: activityLogMessage
                  };
                  console.log("activityData :::", activityData);
                  this._structureService.trackActivity(activityData);
                  this.router.navigate(['/external-integration/' + this._sharedService.externalIntegrationToken]);
                }
              });
            }
          }
    } else {
      if(!this.name)
        this.emptyName=true;
      if(!this.code)
        this.emptyCode=true;
      if(!this.uniqueIdentifier)
        this.emptyuniqueIdentifier=true;
      if(!this.labelInInvite && this.tenantAssoc == '0')
        this.emptyLabel=true;
      if(!this.fileNameExpression)
        this.fileNameLabel=true;
      if(!this.extSysValue && this.tenantAssoc != '0')
        this.emptyextSysValue=true;
    }
  }

  /**
   * Add external system value 
   * On click add button add external system value to the array
   */
   addExternalSystemValue(){    
    if(this.txtESIValue != '' && !this.checkValueExistOrNot(this.txtESIValue))
    {       
      const arrEsiValue = { id : null, esiValue : this.txtESIValue, status : 1 };
      this.externalSystemValues.push(arrEsiValue);
      this.intActiveEsi++;
      this.closeExternalSystemValueEdit();
    }
  }

  /**
   * Edit already added values
   * @param index Index of the array externalSystemValues which is edited
   */
   editExternalSystemValue(index){
    if(this.newEsiValue && !this.checkValueExistOrNot( this.newEsiValue, index )){
      this.externalSystemValues[index].esiValue = this.newEsiValue;
      this.closeExternalSystemValueEdit();
    }
  }

  /**
   * Check the esi value already added or not 
   * @param esiValue (string) the esi value entered
   * @param id (int) index of the array externalSystemValues
   * @returns boolean : 0 if value exist , 1 if value not exist
   */
  checkValueExistOrNot(esiValue, id = null){
    try {
      this.externalSystemValues.forEach((data, key) => {
        if (data.esiValue == esiValue && key != id){
          //activate if already deleted esi exist
          if(data.status == 0){
            data.status = 1;
            if(id != null){
              this.externalSystemValues[id].status = 0;
            }
            throw 'VALUE_ACTIVATED'; 
          }
          else{
            throw 'VALUE_EXIST'; //throw exception if value already exist
          }
        }
      });
      return 0;
    } catch (e) {
      //set flag to show the message if value already exist
      if (e == 'VALUE_EXIST') {
        this.esiAlreadyAdded = 1;
        return 1;
      }else{
        this.closeExternalSystemValueEdit();
        return 1;
      }
    }
  }

  /**
   * Load already added data to the edit mode
   * @param index  Index of the array externalSystemValues which is going to edit
   */
  loadExternalSystemValue(index){
    this.intESIIndex = index;
    this.newEsiValue = this.externalSystemValues[index].esiValue ;
  }
  /**
   * Reset the UI from edit mode
   */
  closeExternalSystemValueEdit(){
    this.intESIIndex = null;
    this.newEsiValue = '';
    this.txtESIValue = '';
    this.esiAlreadyAdded = 0;
  }

  /**
   * Delete external system value 
   * On click delete icon in the list, delete corresponding details from the array
   * @param index integer - array index
   */
  deleteExternalSystemValue(index){  
    //ask confirmation before deleting the value
    swal({
      title: this._ToolTipService.getTranslateData('MESSAGES.ARE_YOU_SURE'),
      text: this._ToolTipService.getTranslateData('MESSAGES.DELETE_CONFIRM')+", " + this.externalSystemValues[index].esiValue,
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      cancelButtonText: "Cancel",
      confirmButtonText: "OK",
      closeOnConfirm: true
    }, (confirm) => {
      if(confirm) {   
        //change the status to 0 if already saved to db 
        //or else just remove the details from array
        if(this.externalSystemValues[index].id == null)
          this.externalSystemValues.splice(index, 1);
        else
          this.externalSystemValues[index].status = 0;

        this.intActiveEsi--;
        this.closeExternalSystemValueEdit();
      }
    });
  }

}
