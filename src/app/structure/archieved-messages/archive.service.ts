import { Injectable } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import 'rxjs/add/operator/toPromise';
import { StructureService } from '../structure.service';
// TODO: Revamp CHP-7018
@Injectable()
export class ArchiveService {
  inboxData = '';
  searchApi = '';
  deleteChatMessagesApi = '';
  restoreChatMessagesApi = '';
  deleteSignatureDocApi = '';
  deleteInventoryCountApi = '';
  restoreMaskedMessageApi = '';
  data = {};

  config: any;
  userDetails: any;
  configData: any = {};
  public userData: any = {};
  constructor(private http: Http, private structureService: StructureService) {
    this.config = this.structureService.userDataConfig;
    this.userDetails = this.structureService.userDetails;
    this.configData = JSON.parse(this.config);
    this.userData = JSON.parse(this.userDetails);
    this.restoreChatMessagesApi = `${this.structureService.apiBaseUrl}citus-health/${this.structureService.version}/restore-chatroom-messages.php`;
    this.restoreMaskedMessageApi = `${this.structureService.apiBaseUrl}citus-health/${this.structureService.version}/restore-masked-message.php`;
  }

  restoreMaskedMessages(data) {
    const headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this.structureService.getCookie('authenticationToken'));
    const options = new RequestOptions({ headers });
    const promise = new Promise((resolve) => {
      const apiURL = this.restoreMaskedMessageApi;
      this.http
        .post(apiURL, data, options)
        .toPromise()
        .then((res) => {
          const inboxDetails = res.json();
          resolve(inboxDetails);
        });
    });
    return promise;
  }
  restoreChatroomMessages(chatroomId, isBroadcast) {
    const headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this.structureService.getCookie('authenticationToken'));
    const options = new RequestOptions({ headers });
    const promise = new Promise((resolve) => {
      const apiURL = this.restoreChatMessagesApi;
      this.data = `chatroomId=${chatroomId}`;
      this.http
        .post(apiURL, this.data, options)
        .toPromise()
        .then((res) => {
          const inboxDetails = res.json();
          resolve(inboxDetails);
        });
    });
    return promise;
  }
}
