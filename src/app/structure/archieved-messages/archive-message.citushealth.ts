import { Component, OnInit, ElementRef, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { DatePipe } from '@angular/common';
import {
  messageFilterOptions,
  defaultCategoryOfMessage,
  CONSTANTS,
  MessageType,
  DefaultPics,
  ChatAvatarOptions,
  MessageCategory,
  ContentTypes,
  msgHistoryDateTimeFormat
} from 'app/constants/constants';
import { MessageService } from 'app/services/message-center/message.service';
import { GetChatMessages, GetChatMessagesResponse, GetMaskedRepliesPayload, defaultGetMaskedRepliesPayload } from 'app/models/message-center/messageCenter';
import { HttpService } from 'app/services/http/http.service';
import { StructureService } from '../structure.service';
import { ChatService } from '../inbox/chat.service';
import { InboxService } from '../inbox/inbox.service';
import { SharedService } from '../../structure/shared/sharedServices';
import { FormsService } from '../forms/forms.service';
import { ToolTipService } from '../tool-tip.service';
import { DateRangeSelected } from '../shared/ch-daterange-picker/date-range-selected.interface';
import { StoreService, Store } from '../shared/storeService';
import { APIs } from 'app/constants/apis';
import * as moment from 'moment';

declare const $: any;
declare const NProgress: any;

@Component({
  selector: 'cat-page',
  templateUrl: './archive-message.html'
})
// TODO: Revamp CHP-7018
export class ArchiveMessages implements OnInit {
  @ViewChild('userSearch') srch: ElementRef;
  @ViewChild('userSearchFromChat') search: ElementRef;
  totalCount;
  contentLimit = 25;
  currentPage = 1;
  contentOffset = 0;
  pageCountMessage = 1;
  previousPage;
  chatroomUsersList: any;
  archived = 'true';
  inboxData: any;
  searchData = {};
  userId = '';
  iconPath = '';
  archiveqlimit: any;
  searchFlag = false;
  searchInboxkeyword: string;
  newMessagedetails = [];
  messageFetching: any = false;
  loadMoreButtonText = 'Loading..';
  page = 0;
  last = 0;
  translatedMessagesDetails = [];
  toggleTranslation = false;
  autoTranslateFailureCount = 0;
  selectedRow: number;
  beforeLoad = false;
  moreToLoad;
  activePatient;
  chatWithHeading;
  currentChatroomId = localStorage.getItem('targetId');
  activeMessage = JSON.parse(localStorage.getItem('activeMessage'));
  pageHeading = localStorage.getItem('chatWithHeading');
  config: any;
  userDetails: any;
  configData: any = {};
  userData: any = {};
  privileges = this.structureService.getCookie('userPrivileges');
  optionShow = 'staff';
  messageFilterOptions = [];
  userGroupIds = CONSTANTS.userGroupIds;
  messageFilter = defaultCategoryOfMessage;
  dateRange: DateRangeSelected;
  messageType = MessageType;
  defaultPics = DefaultPics;
  chatAvatarOptions = ChatAvatarOptions;
  messageCategory = MessageCategory;
  isSubListVisible = false;
  isSubListFetching = false;
  chatroomSelected = 0;
  maskedMessageSubList = [];
  defaultMsgPics: { [key: string]: string } = {
    'pdg': DefaultPics.PDG,
    'message-group': DefaultPics.MSGGROUP,  
    'broadcast': DefaultPics.BROADCAST     
  };
  constructor(
    private router: Router,
    public inboxService: InboxService,
    private structureService: StructureService,
    private chatService: ChatService,
    private datePipe: DatePipe,
    private sharedService: SharedService,
    public formsService: FormsService,
    private toolTipService: ToolTipService,
    private storeService: StoreService,
    private messageService: MessageService,
    private httpService: HttpService
  ) {
    this.config = this.structureService.userDataConfig;
    this.userDetails = this.structureService.userDetails;
    this.configData = JSON.parse(this.config);
    this.userData = JSON.parse(this.userDetails);
    localStorage.setItem('pageCountMessageArchive', this.pageCountMessage.toString());
    this.messageFilterOptions = messageFilterOptions;
    this.userGroupIds = CONSTANTS.userGroupIds;
  }
  ngOnInit() {
    this.messageType = MessageType;
    this.defaultPics = DefaultPics;
    this.chatAvatarOptions = ChatAvatarOptions;
    this.messageCategory = MessageCategory;
    this.totalCount = +localStorage.getItem('inboxTotalMessageCountArchive').replace(/"/g, '');
    this.currentPage = +localStorage.getItem('messageInboxPaginationArchive') || 0;
    const searchInboxkeyword = localStorage.getItem('searchInboxkeywordInboxArchive');
    if (searchInboxkeyword !== '') {
      $('.chatroom-card-cnt .chatroom-section .searchbar input#SearchTxt').val(this.searchInboxkeyword);
      this.searchInboxkeyword = searchInboxkeyword;
    }
    localStorage.setItem('searchInboxkeywordForPollingArchive', searchInboxkeyword);

    this.config = this.structureService.userDataConfig;
    this.userDetails = this.structureService.userDetails;
    this.configData = JSON.parse(this.config);
    this.userData = JSON.parse(this.userDetails);
    this.userId = this.structureService.getCookie('userId');
    this.iconPath = this.structureService.iconPath;
    this.archiveqlimit = +this.structureService.getCookie('archiveqlimit') || 0;
    if (this.storeService.getStoredData(Store.DATE_RANGE_FILTER_ARCHIVED_MESSAGE)) {
      this.dateRange = JSON.parse(this.storeService.getStoredData(Store.DATE_RANGE_FILTER_ARCHIVED_MESSAGE));
    }
    NProgress.done();
    const params: GetChatMessages = {
      loaderStatus: true,
      archived: 1,
      pageCount: this.currentPage,
      fromInboxPagination: true,
      searchKeyword: this.searchInboxkeyword,
      siteIds: 0,
      dateRange: this.dateRange
    };
    this.messageService.getChatMessages(params).subscribe((data: GetChatMessagesResponse) => {
      const dataRes: any = data.data.messages;
      this.structureService.archiveData = dataRes;
      if (+this.pageCountMessage <= 1) {
        this.structureService.archiveDataTotalCount = data.data.totalChatRoomsCount;
        this.structureService.inboxTotalMessageCountArchive = this.totalCount;
      }
      this.inboxData = dataRes;
      NProgress.done();
      setTimeout(() => {
        if ($('.inbox-data-container .cat__apps__messaging__tab--selected').length) {
          const topPos = $('.inbox-data-container .cat__apps__messaging__tab--selected').offset().top;
          const sTop = $('.inbox-data-container').scrollTop();
          const topPosition = topPos + sTop;
          $('.inbox-data-container').animate({ scrollTop: topPosition - 300 }, 'slow');
        }
      }, 1000);
    });
    this.messageFilter = this.configData.default_category_of_message_display || defaultCategoryOfMessage;
    this.loadMore(true);
    this.sharedService.sessionRefresh.subscribe((userData) => {
      this.privileges = userData.privileges;
    });
  }
  backSpaceKey(e) {
    const code = e.keyCode ? e.keyCode : e.which;
    if (code === CONSTANTS.enterKeyCode) this.searchMessagesAndDocs();
    localStorage.setItem('searchInboxkeywordForPollingArchive', this.searchInboxkeyword);
  }
  searchMessagesAndDocs() {
    this.searchInboxkeyword = $('#SearchTxt').val();
    if (this.searchInboxkeyword) {
      this.currentPage = 1;
      localStorage.setItem('messageInboxPaginationArchive', JSON.stringify(this.currentPage));
      localStorage.setItem('pageCountMessageArchive', '1');
      this.searchFlag = !!this.searchInboxkeyword;
      localStorage.setItem('searchInboxkeywordForPollingArchive', this.searchInboxkeyword);
      this.activate();
    }
  }
  resetSearch() {
    this.searchFlag = false;
    this.searchInboxkeyword = '';
    localStorage.setItem('searchInboxkeywordForPollingArchive', this.searchInboxkeyword);
    localStorage.setItem('searchInboxkeywordInboxArchive', this.searchInboxkeyword);
    this.currentPage = 1;
    localStorage.setItem('messageInboxPaginationArchive', JSON.stringify(this.currentPage));
    localStorage.setItem('pageCountMessage', '1');
    $('#SearchTxt').val('');
    this.activate();
  }
  scrollBottom() {
    setTimeout(() => {
      if ($('.chatroom-message-listing .cat__apps__chat-block__item').length) {
        $('.chatroom-message-listing').animate({ scrollTop: 999999 }, 'slow');
      }
    }, 1);
  }
  htmlFromText(str) {
    const formattedText = str
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/`/g, '&#x60;')
      .replace(/(?:\r\n|\r|\n)/g, '\n')
      .replace(/(\n+)/g, '<div>$1</div>')
      .replace(/\n/g, '<br/>')
      .replace(/<br\/><\/div>/g, '</div>');
    return formattedText;
  }
  loadMore(first, fromWhere = false) {
    const showChatHistory =
      (this.configData.show_chat_history_to_new_participant && this.configData.show_chat_history_to_new_participant.toString() === '1') || false;
    this.messageFetching = true;
    this.loadMoreButtonText = this.toolTipService.getTranslateData('MESSAGES.LOADING_DATA');
    const searchContent = fromWhere ? this.htmlFromText(this.search.nativeElement.value.trim()) : '';
    this.chatService
      .getChatroomMessages(this.currentChatroomId, showChatHistory, this.page, this.last, searchContent, this.messageFilter)
      .then((data) => {
        if (data) {
          if (JSON.parse(JSON.stringify(data)).chatroomid.toString() === this.currentChatroomId.toString()) {
            const result = JSON.parse(JSON.stringify(data));
            if (first) {
              this.pageHeading = 'title' in result && result.title ? result.title : '';
              this.chatWithHeading = this.pageHeading;
              localStorage.setItem('chatWithHeading', this.pageHeading);
              this.newMessagedetails = [];
              this.translatedMessagesDetails = [];
              $('.cat__apps__chat-block').css({ height: '100px' });
            }
            this.messageFetching = false;
            this.populateMessageList(data, 0, 1);
            if(this.newMessagedetails.length) {
              this.newMessagedetails = this.messageService.handleMessageDeleteUndoHistoryData(this.newMessagedetails);
            }
    
            const currentChatroomId = result.chatroomid;
            /**
             * Translate message when populating...
             */
            if (result.content.length && this.userData.config.chat_auto_translate == '1') {
              this.toggleTranslation = true;
              let messageToBeTranslated = '';
              result.content.forEach((item) => {
                messageToBeTranslated += `${encodeURIComponent(item.message)}&q=`;
              });
              const self = this;
              this.chatService.translateMessage(messageToBeTranslated.substring(0, messageToBeTranslated.length - 3), currentChatroomId).then(
                (res) => {
                  let resultResponse = JSON.parse(JSON.stringify(res));
                  const { chatroomId } = resultResponse;
                  resultResponse = resultResponse.content;
                  if (this.currentChatroomId.toString() === chatroomId.toString()) {
                    const condition = first && this.search.nativeElement.value.trim() ? 1 : 0;
                    self.populateMessageList(result, resultResponse.data.translations.reverse(), condition);
                  }
                },
                (errMsg) => {
                  this.autoTranslateFailureCount++;
                  if (this.autoTranslateFailureCount < 2) {
                    this.structureService.notifyMessage({
                      messge:
                        'Translation services are temporarily unavailable. This issue has been reported to the CitusHealth support team. We apologize for the inconvenience.'
                    });
                    const activityData = {
                      activityName: 'Failure Auto Translate',
                      activityType: 'manage messaging',
                      activityDescription: `Chat auto translation failed - ${errMsg}`
                    };
                    this.structureService.trackActivity(activityData);
                  }
                  if (first) this.scrollBottom();
                }
              );
            }
            /**
             * End Of Translate message when populating...
             */
            this.page++;
          }
        } else {
          this.moreToLoad = false;
        }
        const msgKeys = { '1': 'User Generated Messages', '0': 'System Generated messages', all: 'All Messages' };
        const activityData = {
          activityName: 'Filter Messages',
          activityType: 'manage messaging',
          activityDescription:
            `${this.userData.displayName}(${this.userData.userId}) has filter with ${msgKeys[this.messageFilter]} ` +
            `in chatroom-${this.currentChatroomId}, in archived messages`
        };
        this.structureService.trackActivity(activityData);
      });
  }
  loadPage(page: number) {
    localStorage.setItem('messageInboxPaginationArchive', JSON.stringify(page));
    this.previousPage = page;
    this.currentPage = page;
    this.contentOffset = (page - 1) * this.contentLimit + 1;
    this.pageCountMessage = page;
    localStorage.setItem('pageCountMessageArchive', JSON.stringify(page));
    this.activate(false, page);
  }
  activate(loaderStatus = true, pageCountMessage = 1) {
    NProgress.start();
    const searchKeyword = this.searchInboxkeyword || '';
    localStorage.setItem('searchInboxkeywordForPollingArchive', this.searchInboxkeyword);
    this.searchFlag = searchKeyword !== '' || this.searchFlag;
    const params: GetChatMessages = {
      loaderStatus,
      searchKeyword: this.searchInboxkeyword,
      archived: 1,
      pageCount: pageCountMessage,
      siteIds: 0,
      dateRange: this.dateRange
    };
    if (this.userId && this.searchFlag) {
      this.messageService.getChatMessages(params).subscribe((data: GetChatMessagesResponse) => {
        this.beforeLoad = true;
        const dataRes = data.data.messages;
        if (pageCountMessage <= 1) {
          this.totalCount = data.data.totalChatRoomsCount;
          this.structureService.inboxTotalMessageCountArchive = this.totalCount;
        }
        this.inboxData = dataRes;
        NProgress.done();
        this.searchFlag = false;
      });
    } else {
      params.loaderStatus = false;
      this.messageService.getChatMessages(params).subscribe((data: GetChatMessagesResponse) => {
        const dataRes = data.data.messages;
        if (pageCountMessage <= 1) {
          this.totalCount = data.data.totalChatRoomsCount;
          this.structureService.inboxTotalMessageCountArchive = this.totalCount;
        }
        this.inboxData = dataRes;
        NProgress.done();
      });
    }
  }
  populateMessageList(message, translatedMessages, first) {
    let newMessagedetails = [];
    const messages = message.content;
    const messageLength = messages.length;
    if (+message.activity === 1 && message.content && messageLength >= 20) {
      this.loadMoreButtonText = "Load earlier messages";
    } else {
      this.loadMoreButtonText = '';
    }
    if (first) {
      newMessagedetails = [];
    }
    for (let key = 0; key < messageLength; key += 1) {
      const value = messages[key];
      if (!translatedMessages) {
        let userName;
        let userClass;
        let owner = false;
        if (value.userid == this.userData.userId) {
          userName = this.toolTipService.getTranslateData('LABELS.MESSAGE_USER.ME');
          userClass = 'self';
          owner = true;
        } else {
          userName = value.displayName;
          userClass = "other";
        }
        let getitems = '';
        if (value.tag) {
          getitems = value.tagedItems;
        }
        let sign = value.sign != 'false' ? this.structureService.apiBaseUrl + 'writable/filetransfer/uploads/' + value.sign : false;
                let details = {
                    "id": value.id,
                    "name": userName,
                    "class": userClass,
                    "avatar": value.avatar,
                    "msg": value.message,
                    "time": value.sent,
                    "userid": value.userid,
                    "sign": sign,
                    "owner": owner,
                    "tag": value.tag,
                    "tagSign": value.tagSign,
                    getitems: getitems,
                    messageStatus: value.messageStatus,
                    deleteUndoHistory: value.deleteUndoHistory
                }
                this.newMessagedetails.unshift(details);
                this.last = value.id;
            }
        }
        this.newMessagedetails = this.newMessagedetails;
        this.scrollBottom();
    }
  reloadChatRoom(evt, message, index) {
        this.messageFilter = this.configData.default_category_of_message_display || defaultCategoryOfMessage;
        this.search.nativeElement.value = '';
        this.selectedRow = index;
        if (evt && (((($(evt.target.parentElement).hasClass('from-name') && this.configData.show_prototypes === '1' && this.userData.group !== '3') || ($(evt.target).hasClass('icon-pending') && this.userData.privileges.indexOf('clinicianApprover') !== -1)) && (message.grp === '3' || (message.chatWithRoleId === '3' && message.grp === this.userData.group))) || $(evt.target.parentElement).hasClass('forward-link-col'))) {
            if ($(evt.target.parentElement).hasClass('from-name')) {
                this.activePatient = message;
            } else if ($(evt.target).hasClass('icon-pending')) {
                this.activePatient = message;
            } else {
      }
    } else if (evt.target.tagName !== 'A') {
      this.activeMessage = message; // Forward option from chat window
      let chatWithDob = '';
      let fromDob = '';
      const chatWithDay = new Date(message.chatWithDob).getDay();
      const messageDay = new Date(message.dob).getDay();
            if (message.chatWithDob && !isNaN(chatWithDay)) {
                chatWithDob = " DOB " + this.datePipe.transform(message.chatWithDob, 'MM/dd/yy');
            }
            if (message.dob && !isNaN(messageDay)) {
                fromDob = " DOB " + this.datePipe.transform(message.dob, 'MM/dd/yy');
            }
            const chatWith = this.userData.userId === message.userid ? (message.chatWithRoleId === "3" ? message.chatWith + chatWithDob : message.chatWith) : (message.grp === "3" ? message.fromName + fromDob : message.fromName);
            this.chatWithHeading = parseInt(message.message_group_id) && message.groupName ? message.groupName : (parseInt(message.messageType) ? "Message from " + ((message.userid !== this.userData.userId) ? message.fromName : "Me") : "Chat with " + chatWith);
            const targetID = message.chatroomId || message.chatroomid;
            if (this.userData.userId !== message.userid) {
                this.activeMessage.chatWith = message.fromName;
                this.activeMessage.chatWithUserId = message.userid;
            } else {
                this.activeMessage.chatWith = message.chatWith;
                this.activeMessage.chatWithUserId = message.chatWithUserId;
            }
            localStorage.setItem('targetId', targetID);
            localStorage.setItem('targetName', 'group-chat');
            localStorage.setItem('chatWithHeading', this.chatWithHeading);
            localStorage.setItem('activeMessage', JSON.stringify(this.activeMessage));
            localStorage.setItem('archived', 'true');
            this.currentChatroomId = targetID;
            this.newMessagedetails = [];
            this.page = 0;
            this.last = 0;
            this.loadMore(true);
        }
    }
    viewStructuredForm(event, message) {
        var data: any = {};
        data = message;
        message.fromArchive = true;
        data.loginUserId = this.userData.userId;
        this.structureService.setCookie('formData', encodeURIComponent(JSON.stringify(message)), 1);
        localStorage.setItem('formData', encodeURIComponent(JSON.stringify(message)));
        this.formsService.checkIsFormFilled(data).then(function (result: any) {
            this._sharedService.updateUserInboxCounts.emit({getCount:true, type: 'forms'});
        });
        if (message.form_submission_id && message.form_submission_id != 0) {
            this.router.navigate(['/forms/list/view']);
        } else {
            this.router.navigate(['/forms/view-form']);
        }
    }
    searchOnEnter(event) {
        if(event.keyCode == 13) {
            if(this.search.nativeElement.value.trim()){
                this.searchReroute();
            } else {
                this.resetChatMessageSearch()
            }
        }
    }    
    searchReroute(optionShow = false) {
        this.resetPageAndLimit();
        this.loadMore(true, this.search.nativeElement.value.trim());
    }
    resetChatMessageSearch() {
        this.search.nativeElement.value = "";        
        this.resetPageAndLimit();
        this.loadMore(true);
    }
    resetPageAndLimit(){
        this.last = 0;
        this.page = 0;
    }
    filterMessages() {
        this.resetPageAndLimit();
        this.loadMore(true,this.search.nativeElement.value.trim());
    }

  /**
   * For ngFor performance improvement
   * @param index
   * @param item
   * @returns chatroom Id
   */
  trackByForArchiveMessages(index, item): number {
    return item.chatroomId;
  }
  /**
   * To fetch and show masked message replies
   */
  expandSubMessageList(chatroomId) {
    this.chatroomSelected = chatroomId;
    this.isSubListVisible = !this.isSubListVisible;
    if (this.isSubListVisible) {
      this.isSubListFetching = true;
      const params: GetMaskedRepliesPayload = {
        chatRoomId: chatroomId,
        archived: 1
      };
      this.httpService
        .doPost(APIs.getMaskedReplyMessages, { ...defaultGetMaskedRepliesPayload, ...params }, ContentTypes.FORM)
        .subscribe((response) => {
          this.maskedMessageSubList = response.message;
          this.isSubListFetching = false;
        });
    }
  }

  formatMessageDeletedTime(dateTimeUTC: string): string {
    return dateTimeUTC ? moment(dateTimeUTC).format(msgHistoryDateTimeFormat) : '';
  }
}
