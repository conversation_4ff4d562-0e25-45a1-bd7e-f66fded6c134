import { Directive, ElementRef, Input, AfterViewInit } from '@angular/core';

@Directive({
  selector: '[messagecompile]',
})
export class messagecompileDirective implements AfterViewInit  {    

  @Input() messagecompile: string;
    constructor(private elRef: ElementRef) {       

    }
    ngAfterViewInit(): void {
       // console.log(this.compile);  
                        var resetValue;
                        var dataValue;
                        if(this.messagecompile.search("data-mediaType='pdf'")!=-1){
                             dataValue = this.messagecompile;
                            var hrefStrings = dataValue.substring(parseInt(dataValue.lastIndexOf("data-src="))+9,dataValue.lastIndexOf(".pdf'"));
                            var appendedString = " data-view="+hrefStrings+".pdf'";
                            // resetValue = this.messagecompile.splice(parseInt(dataValue.lastIndexOf(".pdf'"))+5,0,appendedString);
                            resetValue = dataValue;
                        } else {
                            resetValue = this.messagecompile;
                        }
            this.elRef.nativeElement.innerHTML = resetValue;
    }
}
 