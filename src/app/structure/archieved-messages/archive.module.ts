import { NgModule }      from '@angular/core';
import { CommonModule }  from '@angular/common';
import { Routes, RouterModule }  from '@angular/router';

import { Archive } from './archive.citushealth';
import { ArchiveMessages } from './archive-message.citushealth';
import { FormsModule } from '@angular/forms';
import { inboxCompileDirective } from './archive-directive';
import { messagecompileDirective } from './archive-message-directive';
import { dateTimehortFilterPipe } from './archive-filter.pipes';
import { unreadmessageFilterPipe, userPersistantMessageFilterFnPipe } from './archive-unreadmessage.pipes';
import { SearchFilterPipe } from './archive-search.pipes';
import { SortPipe } from './archive-date-filter.pipes';
import {NgbModule} from '@ng-bootstrap/ng-bootstrap'; // For inbox Pagination Implemented by A
import { AuthGuard } from '../../guard/auth.guard';
import { SharedModule } from '../shared/sharedModule';
export const routes: Routes = [
  { path: 'archive', component: Archive, canActivate:[AuthGuard], data: {
		checkRoutingConfig: 'enable_message_center'
	}},
  { path: 'archive/messages', component: ArchiveMessages, canActivate:[AuthGuard], data: {
		checkRoutingConfig: 'enable_message_center'
	}}
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    FormsModule,
    SharedModule ,
    NgbModule.forRoot()
  ],
  declarations: [
    Archive,
    ArchiveMessages,
    inboxCompileDirective,
    dateTimehortFilterPipe,
    unreadmessageFilterPipe,
    messagecompileDirective,
    SearchFilterPipe,
    SortPipe,
    userPersistantMessageFilterFnPipe
  ],
  providers: [SortPipe]


})

export class ArchiveModule { }
