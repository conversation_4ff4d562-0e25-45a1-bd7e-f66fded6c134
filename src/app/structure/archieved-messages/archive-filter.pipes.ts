import { Component, OnInit, Pipe, PipeTransform } from '@angular/core';
import { DatePipe } from '@angular/common';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Pipe({
  name: 'dateTimehortFilter'
})
export class dateTimehortFilterPipe implements PipeTransform {
  transform(value: any, exponent: string): any {
    var shortFormat:any = (new Date(value).toDateString() === new Date().toDateString()) ? 'hh:mm a' : (new Date(value).getFullYear() != new Date().getFullYear()) ? 'MMM dd y hh:mm a' : 'MMM dd hh:mm a';
    var datePipe = new DatePipe("en-US");   
    value = datePipe.transform(value, shortFormat);   
      return value;   
    }
}