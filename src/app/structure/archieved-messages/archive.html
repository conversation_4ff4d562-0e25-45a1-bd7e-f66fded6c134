<section class="card">
  <div class="card-header row">
    <span class="cat__core__title col-md-6">
      <strong>Archived Messages </strong>
    </span>
    <div class="filter-sites-wrapper" [hidden]="!hideSiteSelection">
      <div class="col-md-12" style="float: right">
        <div class="filter-site row">
          <div class="site-label">
            <span>{{ labelSiteFilter | translate }}</span>
          </div>
          <div class="col-md-8" style="width: 73%">
            <app-select-sites
              [events]="eventsSubject.asObservable()"
              [filterType]="true"
              (siteIds)="getSiteIds($event)"
              (hideDropdown)="hideDropdown($event)"
            >
            </app-select-sites>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="card-block">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
      <li class="breadcrumb-item">Archived Messages</li>
    </ol>
    <div class="row inbox-data-listing-container">
      <div class="col-lg-12">
        <div class="mb-5">
          <section class="card" order-id="card-3">
            <div class="card-block">
              <div class="cat__core__card-sidebar">
                <div class="cat__apps__messaging__header row cat__apps__messaging__header searchbar">
                  <div class="col-sm-4">
                    <input
                      class="form-control cat__apps__messaging__header__input"
                      (keydown)="enterpressalert($event)"
                      placeholder="Search..."
                      id="userSearchTxt"
                      [(ngModel)]="searchInboxKeyword"
                      #search
                    />
                    <i style="right: -30px !important" class="icmn-loader" *ngIf="searchFlag"
                      ><img src="./assets/img/loader/color.gif" class="menu-spinner"
                    /></i>
                    <i style="right: -30px !important" class="icmn-loader" *ngIf="resetSearchFlag"
                      ><img src="./assets/img/loader/color.gif" class="menu-spinner"
                    /></i>
                  </div>
                  <div class="col-sm-2">
                    <button
                      type="button"
                      [disabled]="!search.value"
                      class="btn btn-sm btn-primary srchBtn"
                      title="Search"
                      (click)="searchMessagesAndDocs()"
                    >
                      Search
                    </button>
                    <button type="button" class="btn btn-sm btn-default resetBtn" title="Reset" (click)="resetSearch()">Reset</button>
                  </div>
                  <div class="col-sm-6 date-range">
                    <div class="row">
                      <div class="col-sm-2 date-range-col">
                        <label
                          >{{ 'LABELS.DATE_RANGE' | translate }}
                          <i chToolTip="defaultDateRangeInfo" class="icmn-info date-range-info" data-animation="false"><span>:</span></i>
                        </label>
                      </div>
                      <div class="col-sm-5">
                        <ch-daterange-picker
                          [dateRangeFilterOptions]="dateRangeFilterOptions"
                          [saveStateInto]="dateRangeStoreKey"
                          (selectDateRange)="dateRangeSelected($event)"
                          [keepSession]="true"
                          [emitOnLoad]="true"
                        ></ch-daterange-picker>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="cat__core__card-sidebar">
                <div class="cat__apps__messaging__list">
                  <div class="no-data" *ngIf="beforeLoad && +archieveDataLength === 0">
                    <span class="chat-with-link-span" *ngIf="!searchFlag && !resetSearchFlag && !searchInboxKeyword && +msgCount >= 0">{{ 'MESSAGES.EMPTY_ARCHIVED_MESSAGE' | translate }}</span>
                    <span class="chat-with-link-span" *ngIf="+qLimit !== 0 && msgCount !== 0"
                      >No archived message threads have been created or updated in the last {{ qLimit }} day(s). Please select a different date range
                      see earlier messages.</span
                    ><span class="chat-with-link-span" *ngIf="searchInboxKeyword && +msgCount === 0">{{'VALIDATION_MESSAGES.NO_MATCHING_ITEMS_FOUND' | translate}}</span>
                  </div>
                  <div
                    class="cat__apps__messaging__tab row-selection"
                    *ngFor="let message of inboxData | sortBy : 'messageOrder'; let i = index; trackBy: trackByForArchivedMsgData"
                  >
                    <div *ngIf="message">
                      <div
                        class="cat__apps__messaging__tab__avatar avatar-position"
                        *ngIf="message.messageCategory === messageCategory.GENERAL && +message.messageType !== 1; else otherMessages"
                        (click)="gotoChatRoom($event, message)"
                      >
                        <a class="cat__core__avatar cat__core__avatar--50" href="javascript:void(0);">
                          <img
                            [src]="
                                message.chatAvatar === chatAvatarOptions.CLINICIAN || message.chatAvatar === chatAvatarOptions.PATIENT
                                  ? './assets/modules/dummy-assets/common/img/profile-pic-default.png'
                                  : message.chatAvatar
                              "
                            (error)="this.src = 'assets/img/file-404.png'"
                            alt="Avatar" outOfOfficeStatus [oooInfo]="message.oooInfo"
                          />
                        </a>
                      </div>
                      <ng-template #otherMessages>
                        <div
                          class="float-left cat__core__avatar cat__core__avatar--50"
                          [ngSwitch]="message.messageCategory"
                          (click)="gotoChatRoom($event, message)"
                        >
                          <img
                            *ngSwitchCase="messageCategory.MESSAGE_GROUP"
                            [src]="defaultPics.MSGGROUP"
                            draggable="false"
                            alt="Avatar"
                          />
                          <img
                          *ngSwitchCase="messageCategory.PDG"
                          [src]="defaultPics.PDG"
                          draggable="false"
                          alt="Avatar"
                          />
                          <img
                            *ngSwitchCase="messageCategory.BROADCAST"
                            [src]="defaultPics.BROADCAST"
                            draggable="false"
                            alt="Avatar"
                          />
                          <img
                            *ngSwitchCase="messageCategory.MASKED"
                            [src]="defaultPics.MASKED"
                            draggable="false"
                            alt="Avatar"
                          />
                          <img
                            *ngSwitchDefault
                            [src]="defaultPics.PROFILE"
                            draggable="false"
                            alt="Avatar"
                          />
                        </div>
                      </ng-template>
                      <div class="cat__apps__messaging__tab__content inbox-cont-row">
                        <div  class="icons-div">
                          <small *ngIf="message.pinnedStatus" class="pinned-icon">
                            <span><i class="fa fa-thumb-tack"></i>
                            </span>
                          </small>
                        </div>
                        <small class="cat__apps__messaging__tab__time"
                          >
                          {{(+message.messageStatus === 1?  message.messageOrder * 1000 : formatMessageDeletedTime(message.messageDeletedTime)) | dateTimehortFilter : 'MMM dd hh:mm a' : 'MMM dd hh:mm a' }}
                          <ng-container>
                            <button
                              [ngClass]="{ 'has-no-action': !message.isSelfArchived }"
                              aria-expanded="false"
                              class="btn btn-sm btn-default dropdown-toggle action-btn"
                              data-toggle="dropdown"
                              type="button"
                            >
                              {{ 'GENERAL.ACTIONS' | translate }}
                            </button>
                            <ul class="dropdown-menu">
                              <a
                                class="dropdown-item"
                                (click)="
                                  (+message.messageType !== 2 &&
                                    restoreChatroomMessages(message.chatroomId, message.id, message.messageType, currentPage)) ||
                                    (+message.messageType === 2 &&
                                      restoreMaskedMessages(message, currentPage))
                                  "
                                >{{ 'GENERAL.RESTORE' | translate }}</a
                              >
                            </ul>
                          </ng-container>
                        </small>
                        <div (click)="gotoChatRoom($event, message)">
                          <div
                            class="cat__apps__messaging__tab__name"
                            *ngIf="message.messageCategory === messageCategory.MESSAGE_GROUP"
                            (click)="gotoChatRoom($event, message)"
                          >
                            <div>{{ message.chatHeading }}</div>
                          </div>
                          <div class="cat__apps__messaging__tab__name" *ngIf="+message.messageType === 1" (click)="gotoChatRoom($event, message)">
                            <div>{{ 'TITLES.BROADCAST_MSG' | translate }}</div>
                          </div>
                          <div
                            class="from-name"
                            *ngIf="message.messageCategory !== messageCategory.MESSAGE_GROUP && +message.messageType !== 1"
                            (click)="gotoChatRoom($event, message)"
                          >
                            <div>
                              {{ message.chatHeading }}{{ message.chatSubHeading ? ', ' + message.chatSubHeading : '' }}
                              <img *ngIf="message.chatWithStatus == 2" class="icon-pending" src="{{ iconPath }}/pending-icon.png" alt="pending" />
                            </div>
                          </div>
                          <div class="rerouted-by" *ngIf="message.messageForwarded" (click)="gotoChatRoom($event, message)">
                            <span class="rerouted-by-span">({{ message.messageForwarded }})</span>
                          </div>
                          <div
                            class="cat__apps__messaging__tab__text"
                            [ngClass]="{'has-unread': +message.maskedUnreadCount || +message.unreadCount }"
                            *ngIf="(message.messageCategory === messageCategory.MESSAGE_GROUP || message.messageCategory === messageCategory.PDG) && message.chatSubject"
                          >
                            <span class="msg_grp_title">{{'LABELS.SUBJECT' | translate}}: {{message.chatSubject}}</span>
                          </div>
                          <div class="cat__apps__messaging__tab__text" *ngIf="message.messageCategory === messageCategory.PDG && message.siteName">
                            <span class="msg_grp_title">{{ labelSite | translate }}: {{message.siteName}}</span>
                          </div>
                          <span class="arrow-sub-mesage" *ngIf="message.maskedSubCount" (click)="expandSubMessageList(message.chatroomId); $event.stopPropagation()">
                            <i
                              class="fa fa-2x fa-caret-right"
                              [ngClass]="{ 'message-box-list-sublist-icon': isSubListVisible && message.chatroomId === chatroomSelected }"
                            ></i>
                          </span>
                          <div
                            style="min-height: 20px" 
                            (click)="gotoChatRoom($event, message)"
                          >
                            <div class="cat__apps__messaging__tab__text chatroom-message-min-list" *ngIf="message.messageCategory !== messageCategory.PDG">
                              {{ message.isSelfMessage ? 'Me: ' : '' }}
                              <span archiveCompile="{{ message.message }}"></span>
                            </div>
                            <ng-container *ngIf="message.maskedSubCount">
                              <i class="loader-inner-small" *ngIf="isSubListFetching && message.chatroomId === chatroomSelected"><img src="./assets/img/loader/color.gif"></i>
                              <ng-container *ngIf="isSubListVisible && !isSubListFetching && +message.chatroomId === +chatroomSelected">
                                  <div class="message-sublist-open" *ngFor="let subListData of maskedMessageSubList; let i = index" id="sublist_{{ subListData.chatroomid }}">
                                      <hr>
                                      <div class="message-sublist-details" (click)="gotoChatRoom($event, subListData); $event.stopPropagation()">
                                        <span class="message-sublist-name">{{ userData.userId === subListData.userid ? subListData.chatWith : subListData.fromName }}:&nbsp;</span>
                                        <span class="message-sublist-description" [ngClass]="{'font-italic': +subListData.messageStatus === 0}" archiveCompile="{{ subListData.message }}"></span>
                                        <small class="cat__apps__sublist_messaging__tab__time">{{(+subListData.messageStatus === 1? subListData.sent * 1000: formatMessageDeletedTime(subListData.messageDeletedTime)) | dateshortyearFilter }}</small>
                                      </div>
                                    </div>
                              </ng-container>
                            </ng-container>
                          </div>
                        </div>
                      </div>
                    </div>
                  
                </div>
                  <div *ngIf="messageLoader.messages" class="messageLoads loading-container">
                    <div class="lds-roller">
                      <div></div>
                      <div></div>
                      <div></div>
                      <div></div>
                      <div></div>
                    </div>
                    <div class="loading-text">{{ 'MESSAGES.LOADING_MESSAGES' | translate }}</div>
                  </div>

                  <!-- *********************Start section Pagination Implemented by A for inbox*************************** -->
                  <div class="row" [hidden]="!totalCount && totalCount == 0 && currentPage == 1">
                    <div class="col-sm-5" style="width: 100%; margin-top: 26px; padding-left: 25px">
                      Showing {{ currentPage * 25 - 25 + 1 }} to {{ currentPage * 25 - 25 + inboxData?.length }} of {{ totalCount }} entries
                    </div>
                    <div class="col-sm-7 inbox-pagination" style="width: 100%; margin-top: 20px">
                      <ngb-pagination
                        [collectionSize]="totalCount"
                        [pageSize]="contentLimit"
                        [(page)]="currentPage"
                        [maxSize]="8"
                        [rotate]="true"
                        responsive="true"
                        [directionLinks]="true"
                        aria-label="Pagination"
                        (pageChange)="loadPage($event)"
                      >
                      </ngb-pagination>
                    </div>
                  </div>
                  <!-- *********************Start section Pagination Implemented by A for inbox*************************** -->
                </div>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  </div>
</section>
