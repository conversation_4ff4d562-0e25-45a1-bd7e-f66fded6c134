<!-- START: apps/messaging -->
<section class="card chatroom-card-cnt" >
<div class="chatwith-container">
        <div class="chatwith-cnt">
            <!--<button class="btn btn-primary btn-rounded" (click)="inviteModel()" >Invite <i class="fa fa-caret-up"></i></button>-->
            <!--<button class="btn btn-default btn-sm btn-rounded" (click)="chatWithModel(userData.group)" *ngIf="userData.tenantId && ((userData.group!='3' && (privileges.indexOf('chatWithClinician')!==-1 || privileges.indexOf('chatWithPatients')!==-1 || privileges.indexOf('chatWithMessageGroups')!==-1)) || (userData.group=='3' && privileges.indexOf('chatWithClinician')!==-1 )) " style="cursor:pointer;"><i class="fa fa-comments" aria-hidden="true"></i> Chat </button>-->
            
             <!---->
        </div>
    </div>   
 <!-- ******************** Chatwith Model ************************* -->
                        <div class="modal fade bd-example-modal-lg forward-modal" id="chatWithModel" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel" aria-hidden="true">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                <div class="modal-header">
                                    <h4 class="modal-title" id="exampleModalLabel">Chat with</h4>
                                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                    </button>
                                </div>
                                <div class="modal-body">
                                    
                                    <form>
                                    <div class="card-block">
                                        <div class="cat__core__card-sidebar">
                                            <div class="cat__apps__messaging__header">
                                                <input class="form-control cat__apps__messaging__header__input ng-untouched ng-pristine ng-valid" placeholder="Search Here" #userSearchChatroom>
                                                <i class="icmn-search"></i>
                                                <button></button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="chatwith-modal-tab">
                                        <div class="col-md-4 chatwith-model-head" *ngIf="privileges.indexOf('chatWithMessageGroups')!==-1"  (click)="showData('groups')" [class.cat__apps__messaging__tab--selected]="optionShow=='groups'">Groups</div>
                                         <div class="col-md-4 chatwith-model-head" *ngIf="privileges.indexOf('chatWithClinician')!==-1" (click)="showData('staff')" [class.cat__apps__messaging__tab--selected]="optionShow=='staff'">Staff</div>
                                         <div class="col-md-4 chatwith-model-head" *ngIf="privileges.indexOf('chatWithPatients')!==-1" (click)="showData('patient')" [class.cat__apps__messaging__tab--selected]="optionShow=='patient'">Patient</div>
                                    </div>

                                    <div class="row">
                                        
                                        <div class="col-md-12" *ngIf="optionShow=='groups'">
                                            <!---->
                                            <div class="forwarding-behaviour-container">
                                                <div class="forward-model-option-user-list" *ngFor="let userdatas of (messageGroup ); let i=index">
                                                    <div class="forward-user-role" (click)="createMessgeGroupChatroom(userdatas)">                                            
                                                        {{userdatas.name}} 
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12" *ngIf="optionShow=='staff'">
                                             <!--    -->
                                            <div class="forwarding-behaviour-container">
                                                <div class="forward-model-option-user-list" *ngFor="let user of (userListChatwith);" >
                                                    <div class="forward-user-role" (click)="selectedModal(user)">
                                                        <i class="fa fa-clock-o" [hidden]="!user.isScheduled"></i>                                            
                                                        <p>{{user.displayname}} {{ user.role!='Patient' ?  ' [' + user.role + ']' : '' }} </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-12" *ngIf="optionShow=='patient'">
                                             <!--     -->
                                            <div class="forwarding-behaviour-container">
                                                <div class="forward-model-option-user-list" *ngFor="let user of (userListChatwith);">
                                                    <div class="forward-user-role" (click)="selectedModal(user)">
                                                        <i class="fa fa-clock-o" [hidden]="!user.isScheduled"></i>                                            
                                                        <p>{{user.displayname}} {{ user.role!='Patient' ?  ' [' + user.role + ']' : '' }} </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    </form>
                                </div>
                                <div class="modal-footer" >
                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                                </div>
                                </div>
                            </div>
                        </div>
       <!-- *********************End Section*************************** -->
    
    <div class="card-header">
            <h4 class="mt-1 mb-1 text-black">
            <strong>{{pageHeading}}</strong>
            <div class="invite-btn">
                    <button class="btn btn-default btn-sm btn-rounded" *ngIf="privileges.indexOf('messageForwarding')!==-1 && archived=='false'" ng-hide="curMessage.messageType=='1'" (click)="selectModal('Reroute to','')" ><i class="fa fa-share" aria-hidden="true"></i> Forward </button>
                    <button class="btn btn-default btn-sm btn-rounded" *ngIf="privileges.indexOf('inviteToChat')!==-1 && archived=='false'"  (click)="inviteModel('Invite','')" ><i class="icmn-user-plus"></i> Invite</button>
                </div>
        </h4>
        </div>
        <div class="breadcrumb-wrap chatroom-route" [ngClass]="{'subject-exists': (activeMessage.messageCategory && (activeMessage.messageCategory === messageCategory.PDG || activeMessage.messageCategory === messageCategory.MESSAGE_GROUP) && activeMessage.chatSubject)}">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
                <li class="breadcrumb-item"><a [routerLink]="['/archive']">Archived Messages</a></li>
                <li class="breadcrumb-item">Chat</li>
            </ol>
        </div>
        <div class="breadcrumb-wrap chatroom-subject" *ngIf="activeMessage.messageCategory && (activeMessage.messageCategory === messageCategory.PDG || activeMessage.messageCategory === messageCategory.MESSAGE_GROUP) && activeMessage.chatSubject" >
            <ol class="breadcrumb">
                <li class="msg_grp_title dropdown subjectDropdown chatroon-subject archive" align="right">
                    <div class="row">
                        <div class="col-md-12 chatroom-subject-text-container" id="chatroom-subject-text-wrapper">
                            <span>{{'LABELS.SUBJECT' | translate}}: {{activeMessage.chatSubject}}</span>
                        </div>
                    </div>
                </li>
            </ol>
        </div>
        <button class="align-center oldMessage" (click)="loadMore()" *ngIf="loadMoreButtonText" >{{loadMoreButtonText}}</button> 
            <div class="cat__top-bar__item hidden-sm-down chatrrom-group-icon-for-members" *ngIf="chatroomUsersList && activeMessage.messageType!='1'">
                <div class="dropdown">
                    <a aria-expanded="false" class="dropdown-toggle" data-toggle="dropdown" href="javascript: void(0);">
                    <i class="fa fa-users" aria-hidden="true"></i>
                    </a>
                    <ul aria-labelledby="" class="dropdown-menu dropdown-menu-right" role="menu">
                    <div class="cat__top-bar__activity">
                        <div class="cat__top-bar__activity__item" *ngFor="let user of chatroomUsersList;let i=index" >
                            <div class="cat__top-bar__activity__inner" *ngIf="user.status!='7'">
                                <div class="cat__top-bar__activity__title">
                                <a href="javascript: void(0);">
                                    {{user.displayName}}
                                    <span *ngIf="user.userId==userData.userId">&nbsp;(Me)</span>
                                    <span *ngIf="user.userId!=userData.userId && user.familycare">&nbsp;({{user.familycare}})</span>
                                    </a>
                                </div>
                            </div>
                        </div>  
                    </div>
                    </ul>
                </div>
            </div>
            <section class="card cat__core__card-with-sidebar cat__core__card-with-sidebar--large chatroom-section">
        <!-- Inbox Data Listing -->
       
        <div class="cat__core__card-sidebar ">
             <div class="cat__apps__messaging__header searchbar chatroom-searcbar">
                <input class="form-control cat__apps__messaging__header__input" (keydown)="backSpaceKey($event)" id="SearchTxt" placeholder="Search..." [(ngModel)]="searchInboxkeyword" #search>
                <i class="icmn-loader" *ngIf="searchFlag"><img src="./assets/img/loader/color.gif" class="menu-spinner"></i>
                <!-- <i class="icmn-search" (click)="searchMessagesAndDocs()"></i>
                <button (click)="searchMessagesAndDocs()"></button> -->
                <span _ngcontent-c2="" class=" searchtip" data-placement="top" data-toggle="tooltip" title="" data-original-title="Search"><i class="btnicn icmn-search" (click)="searchMessagesAndDocs()"></i></span>
                <span _ngcontent-c2="" class=" resettip" data-placement="top" data-toggle="tooltip" title="" data-original-title="Reset">
                <i class="reset icmn-close" (click)="resetSearch()"></i>   </span>
            </div>
            <div class="cat__apps__messaging__list inbox-data-container"> 
                    <div class="no-data" *ngIf="beforeLoad && !(inboxData.length)">No matching items found</div>           
                    <div class="overlay-hidden-scroll"></div>
                <div class="cat__apps__messaging__tab row-selection tab-right-border" *ngFor="let message of (inboxData  | sortBy : 'messageOrder');let i=index; trackBy: trackByForArchiveMessages" 
                       [class.cat__apps__messaging__tab--selected]="i == selectedRow || message.chatroomId === +currentChatroomId">                      
                       
                    <div *ngIf="message" (click)="reloadChatRoom($event, message, i)">
                        <div class="cat__apps__messaging__tab__avatar avatar-position" *ngIf="message.messageType === messageType.GENERAL; else otherMessages">
                            <a class="cat__core__avatar cat__core__avatar--50" href="javascript:void(0);">
                                <img [src]="message.chatAvatar === chatAvatarOptions.CLINICIAN || message.chatAvatar === chatAvatarOptions.PATIENT 
                                ? defaultPics.PROFILE : message.chatAvatar"
                                outOfOfficeStatus [oooInfo]="message.oooInfo"
                                 [customClass]="'chatroom-inbox-list'"
                                (error)="this.src='assets/img/file-404.png'"
                                alt="Avatar"> {{message.messageType}}                      
                            </a>
                        </div>
                        <ng-template #otherMessages>
                            <div class="float-left cat__core__avatar cat__core__avatar--50" *ngIf="message.messageCategory === messageCategory.MASKED" 
                            (click)="message.subList.length?expandSubMessageList($event, message.chatroomId):reloadChatRoom($event, message, i)">
                                <img [src]="defaultPics.MASKED" draggable="false">
                            </div>
                            <div class="float-left cat__core__avatar cat__core__avatar--50" *ngIf="message.messageType === messageType.MESSAGE_GROUP || message.messageType === messageType.PDG || message.messageType === messageType.BROADCAST">
                                <img [src]="defaultMsgPics[message.messageCategory]" draggable="false">
                            </div>
                        </ng-template>                   
                        <div class="cat__apps__messaging__tab__content inbox-cont-row" >                                            
                            <small class="cat__apps__messaging__tab__time">{{message.messageOrder * 1000 | dateTimehortFilter}}</small>
                            <small class="unread-count-box">
                                <i class="unread-icon">
                                    <span class="unread-count" *ngIf="message.messageUnreadCount">{{message.messageUnreadCount}}</span>
                                </i>
                                </small>
                            <div class="cat__apps__messaging__tab__name">
                                <div>{{message.chatHeading + (message.chatSubHeading? ',&nbsp; '+ message.chatSubHeading : '') }}</div>
                            </div>
                            <div  class="rerouted-by" *ngIf="message.messageForwarded"><span class="rerouted-by-span">({{message.messageForwarded}})</span></div>
                            <span class="arrow-sub-mesage" *ngIf="message.maskedSubCount" (click)="expandSubMessageList(message.chatroomId); $event.stopPropagation()">
                                <i
                                  class="fa fa-2x fa-caret-right"
                                  [ngClass]="{ 'message-box-list-sublist-icon': isSubListVisible && message.chatroomId === chatroomSelected }"
                                ></i>
                            </span>
                            <div   class="cat__apps__messaging__tab__text chatroom-message-min-list" [ngClass]="{'font-italic': +message.messageStatus === 0 }" >{{message.isSelfMessage?'Me:&nbsp;': ''}}<span archiveCompile="{{message.message}}" ></span></div>
                            <ng-container *ngIf="message.maskedSubCount">
                                <i class="loader-inner-small" *ngIf="isSubListFetching && message.chatroomId === chatroomSelected"><img src="./assets/img/loader/color.gif"></i>
                                <ng-container *ngIf="isSubListVisible && !isSubListFetching && +message.chatroomId === +chatroomSelected">
                                    <div class="message-sublist-open" *ngFor="let subListData of maskedMessageSubList; let k = index" id="sublist_{{ subListData.chatroomid }}">
                                        <hr>
                                        <div class="message-sublist-details" (click)="reloadChatRoom($event, subListData, i); $event.stopPropagation()">
                                          <span class="message-sublist-name">{{ userData.userId === subListData.userid ? subListData.chatWith : subListData.fromName }}:&nbsp;</span>
                                          <span class="message-sublist-description" [ngClass]="{'font-italic': +subListData.messageStatus === 0}" archiveCompile="{{ subListData.message }}"></span>
                                          <small class="cat__apps__sublist_messaging__tab__time">{{(+subListData.messageStatus === 1? subListData.sent * 1000 : formatMessageDeletedTime(subListData.messageDeletedTime)) | dateshortyearFilter }}</small>
                                        </div>
                                      </div>
                                </ng-container>
                              </ng-container>
                        </div>
                    </div>
                </div>
            </div>
            <div  class="chatroom-pagination">
                <ngb-pagination 
                [collectionSize]="totalCount" 
                [pageSize]="contentLimit" 
                [page]="currentPage" 
                [maxSize]="2" 
                [rotate]="true" 
                [boundaryLinks]="false" 
                (pageChange)="loadPage($event)">
                </ngb-pagination>
        </div>
        </div>  
    <!-- Inbox Data Listing -->
    <div class="card-block">
      <div class="row">
        <div class="col-md-5">
          <div class="filter-message chatroom-filter-message message-load-dd" *ngIf="userData.group !== userGroupIds.patient">
            <span class="show-msg"
              ><b>{{ 'LABELS.SHOW_MESSAGES' | translate }}</b></span
            >
            <div class="large-tooltip show-msg-tooltip"><i chToolTip="messageFilter" data-animation="false"></i></div>
            <span class="user-select">
              <select
                id="messages-to-show"
                aria-controls="message-worklist"
                class="form-control input-sm input-show"
                [(ngModel)]="messageFilter"
                (change)="filterMessages()"
              >
                <option *ngFor="let item of messageFilterOptions" value="{{ item.value }}">{{ item.text | translate }}</option>
              </select>
            </span>
          </div>
        </div>

        <div class="col-md-6">
          <div class="archiveSearch message-search-li">
            <div class="cat__apps__messaging__header chat-with-wrapper-search">
              <input
                (keydown)="searchOnEnter($event)"
                autocomplete="off"
                class="message-search-input form-control cat__apps__messaging__header__input ng-untouched ng-pristine ng-valid"
                id="chat-with-modal-search-box"
                placeholder="Search..."
                #userSearchFromChat
              />
              <span class="message-search-actions">
                <button
                  [disabled]="userSearchFromChat.value.trim() === ''"
                  type="button"
                  class="message-search-button btn btn-sm btn-primary srchBtn-message"
                  title="Search"
                  (click)="searchReroute()"
                >
                  {{ 'BUTTONS.SEARCH' | translate }}
                </button>
                <button
                  type="button"
                  class="message-search-reset btn btn-sm btn-default resetBtn-message"
                  title="Reset"
                  (click)="resetChatMessageSearch()"
                >
                  {{ 'BUTTONS.RESET' | translate }}
                </button>
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="cat__apps__messaging chatroom-message-listing archives-list">
            
            <div class="height-700 custom-scroll cat__core__scrollable" id="chat-window-scroll">
                <div class="cat__apps__chat-block" >                  
                    
                    <div class="cat__apps__chat-block__item message-item clearfix" *ngFor="let mDetails of newMessagedetails;let i=index" [ngClass]="{'cat__apps__chat-block__item--right': mDetails.name!='Me','citus-notification':mDetails.name=='Administrator'}">
                        <div class="cat__apps__chat-block__avatar">
                            <a class="cat__core__avatar cat__core__avatar--50" href="javascript:void(0);">
                                <img src="{{mDetails.avatar}}" alt="Alternative text to the image" outOfOfficeStatus [oooInfo]="mDetails.oooInfo">
                            </a>
                        </div>
                        <div class="cat__apps__chat-block__content">
                    <small class="cat__apps__messaging__tab__time">{{mDetails.time*1000 | dateTimehortFilter}}</small>
                    <div class="cat__apps__messaging__tab__name">{{mDetails.name}}:</div>
                    <span class="msg-read-status" *ngIf="mDetails.time <= lastUserActivityTime && mDetails.name!='Administrator' && mDetails.class=='self'">Read</span>
                    <div class="cat__apps__messaging__tab__text chat-message-container"><span *ngIf="mDetails.messageStatus === 1; else messageHistory" messagecompile="{{mDetails.msg | autolinker}}" ></span>
                        <ng-template #messageHistory>
                            <ng-container *ngFor="let msg of mDetails.deleteUndoHistory; let i = index;">
                                <span class="font-italic">{{mDetails.deleteUndoHistory.length - i}}. {{ msg.actionHistory }}</span><br>
                            </ng-container>
                        </ng-template>
                    <div class="message-tag-wrap">
                        <div class="tag-list tag-list-log" [style.background-color]="tagsList.bgColor" [style.color]="tagsList.fontColor" [style.background]="tagsList.bgColor" *ngFor="let tagsList of mDetails.getitems index as t" >
                            <i class="fa fa-caret-left" [style.color]="tagsList.bgColor ? tagsList.bgColor : '#8899a0'" aria-hidden="true"><span></span></i>
                            <span [style.color]="tagsList.fontColor ? tagsList.fontColor : '#ffffff'"  class="message-tag-list-text">{{tagsList.name}}</span>
                        </div>
                    </div>
                    
                    </div>

                    


                    <p class="msg-cont-text sign" *ngIf="mDetails.sign">
                            <img [src]="mDetails.sign" class="chat-sign-image">
                            <span class="remove-chat-sign" *ngIf="mDetails.owner" (click)="removeChatSignature(mDetails);"><i class="fa fa-trash" aria-hidden="true"></i>
                            </span>
                        </p>
                    </div>
                    </div>                     
                </div>
            </div>            
        </div>
    </div>
    </section>
</section>
<a class="cat__core__scroll-top" href="javascript: void(0);" onclick="$('body, html').animate({'scrollTop': 0}, 500);"><i class="icmn-arrow-up"></i></a>

<!-- END: apps/messaging -->
