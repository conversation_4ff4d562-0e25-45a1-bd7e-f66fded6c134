import { Component, OnInit, Pipe, PipeTransform } from '@angular/core';
import { DatePipe } from '@angular/common';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Pipe({
  name: 'unreadmessageFilter'
})
export class unreadmessageFilterPipe implements PipeTransform {
    transform(arr: any, exponent: string): any {
        var totalUnReadMessages = 0;
            for (var i = 0; i < arr.length; i++) {
                totalUnReadMessages= totalUnReadMessages + parseInt(arr[i].unreadCount || arr[i].unread);
            }
            //console.log("toalmessage"+totalUnReadMessages);
        return totalUnReadMessages;
    }
}

@Pipe({
  name: 'userPersistantMessageFilterFn'
})
export class userPersistantMessageFilterFnPipe implements PipeTransform {
    transform(item: any, exponent: string): any {
            return (item.category == 'signature-documents') || (item.category == 'inventory-counts') || item.escalated === "0" || (item.escalated === '1');

    }
}
 