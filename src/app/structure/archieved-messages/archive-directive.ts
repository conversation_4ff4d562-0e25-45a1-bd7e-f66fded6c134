import { Directive, ElementRef, Input, AfterViewInit } from '@angular/core';

@Directive({
  selector: '[archiveCompile]',
})
export class inboxCompileDirective implements AfterViewInit  {
  @Input() archiveCompile: string;
    constructor(private elRef: ElementRef) {
    }
    ngAfterViewInit(): void {
      //console.log("Inner Directive",this.inboxCompile);
      //console.log(this.inboxCompile);
       if (this.archiveCompile.match(/<img data-mediaType='image'/)||this.archiveCompile.match(/<img ng-src=/) || this.archiveCompile.match(/<img data-mediaType="image"/))
            {
                this.archiveCompile = '<i class="fa fa-image"></i> Image';
            } else if (this.archiveCompile.match(/<img data-mediaType="video"/)||this.archiveCompile.match(/<video ><source src =/)) {
                this.archiveCompile = '<i class="fa fa-video-camera"></i> Video';
            } else if (this.archiveCompile.match(/<img data-mediaType='pdf'/)) {
                this.archiveCompile = '<i class="fa fa-file"></i> Pdf';
            } else if (this.archiveCompile.match(/<img data-mediaType='document'/)||this.archiveCompile.match(/<img data-src=/)) {
                this.archiveCompile = '<i class="fa fa-file-text "></i> Document';
            } else if (this.archiveCompile.match(/<audio controls><source src=/)) {
                this.archiveCompile = '<i class="fa fa-headphones"></i> Audio';
            }
            this.elRef.nativeElement.innerHTML = this.archiveCompile;
    }
}
 