import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ArchiveMessages } from './archive-message.citushealth';
import { Router } from '@angular/router';
import { DatePipe } from '@angular/common';
import { MessageService } from 'app/services/message-center/message.service';
import { HttpService } from 'app/services/http/http.service';
import { StructureService } from '../structure.service';
import { ChatService } from '../inbox/chat.service';
import { InboxService } from '../inbox/inbox.service';
import { SharedService } from '../../structure/shared/sharedServices';
import { FormsService } from '../forms/forms.service';
import { ToolTipService } from '../tool-tip.service';
import { StoreService } from '../shared/storeService';
import { of } from 'rxjs/observable/of';
import { RouterTestingModule } from '@angular/router/testing';
import { CommonTestingModule } from 'app/utils/common-testing.module';
import { SortPipe } from './archive-date-filter.pipes';
import { SharedModule } from '../shared/sharedModule';
import { inboxCompileDirective } from './archive-directive';
import { messagecompileDirective } from './archive-message-directive';
import { dateTimehortFilterPipe } from './archive-filter.pipes';
import { unreadmessageFilterPipe, userPersistantMessageFilterFnPipe } from './archive-unreadmessage.pipes';
import { NO_ERRORS_SCHEMA } from '@angular/core';


describe('ArchiveMessages', () => {
  let component: ArchiveMessages;
  let fixture: ComponentFixture<ArchiveMessages>;
  let messageServiceMock;
  let structureServiceMock;
  let chatServiceMock;
  let sharedServiceMock;
  let formsServiceMock;
  let toolTipServiceMock;
  let storeServiceMock;
  let httpServiceMock;

  beforeEach(() => {
    localStorage.setItem('inboxTotalMessageCountArchive', JSON.stringify(0));
    localStorage.setItem('activeMessage', JSON.stringify({}));
    messageServiceMock = {
      getChatMessages: jasmine.createSpy('getChatMessages').and.returnValue(of({ data: { messages: [], totalChatRoomsCount: 0 } })),
    };
    structureServiceMock = {
      getCookie: jasmine.createSpy('getCookie'),
      userDataConfig: JSON.stringify({ tenantId: 1 }),
      userDetails: JSON.stringify({ userId: 1, enable_cross_site: '1', config: { enable_multisite: '1' } }),
    };
    chatServiceMock = {
      getChatroomMessages: jasmine.createSpy('getChatroomMessages').and.returnValue(Promise.resolve({ content: [], chatroomid: '1' })), // { content: [], chatroomid: '1' }
    };
    sharedServiceMock = {
      sessionRefresh: of({ privileges: [] })
    };
    formsServiceMock = {
      checkIsFormFilled: jasmine.createSpy('checkIsFormFilled')
    };
    toolTipServiceMock = {
      getTranslateData: jasmine.createSpy('getTranslateData'),
      getToolTipTitle: jasmine.createSpy('getToolTipTitle')
    };
    storeServiceMock = {
      getStoredData: jasmine.createSpy('getStoredData')
    };
    httpServiceMock = {
      doPost: jasmine.createSpy('doPost')
    };

    TestBed.configureTestingModule({
      declarations: [unreadmessageFilterPipe, userPersistantMessageFilterFnPipe, ArchiveMessages, SortPipe, inboxCompileDirective, messagecompileDirective, dateTimehortFilterPipe],
      imports: [RouterTestingModule, CommonTestingModule, SharedModule],
      providers: [
       
        { provide: DatePipe, useValue: {} },
        { provide: MessageService, useValue: messageServiceMock },
        { provide: StructureService, useValue: structureServiceMock },
        { provide: ChatService, useValue: chatServiceMock },
        { provide: InboxService, useValue: {} },
        { provide: SharedService, useValue: sharedServiceMock },
        { provide: FormsService, useValue: formsServiceMock },
        { provide: ToolTipService, useValue: toolTipServiceMock },
        { provide: StoreService, useValue: storeServiceMock },
        { provide: HttpService, useValue: httpServiceMock }
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
    fixture = TestBed.createComponent(ArchiveMessages);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize component', () => {
    component.ngOnInit();
    expect(component.totalCount).toBe(0);
    expect(component.inboxData).toEqual([]);
  });

  it('should search messages and docs', () => {
    component.searchInboxkeyword = 'test';
    spyOn(window, '$').and.returnValue({ val: () => 'test' });
    spyOn(component, 'activate');
    component.searchMessagesAndDocs();
    expect(component.currentPage).toBe(1);
    expect(component.searchFlag).toBe(true);
    expect(component.activate).toHaveBeenCalled();
  });

  it('should reset search', () => {
    component.searchFlag = true;
    component.searchInboxkeyword = 'test';
    spyOn(component, 'activate');
    component.resetSearch();
    expect(component.searchFlag).toBeFalsy();
    expect(component.searchInboxkeyword).toBe('');
    expect(component.currentPage).toBe(1);
    expect(component.activate).toHaveBeenCalled();
  });

  it('should load more messages', () => {
    const mockMessages = { content: [], chatroomid: '1' };
    chatServiceMock.getChatroomMessages.and.returnValue(Promise.resolve(mockMessages));
    component.loadMore(true);
    expect(component.messageFetching).toBe(true);
  });

  it('should reload chat room', () => {
    const mockMessage = { chatroomId: '1', userid: '1', chatWith: 'test', chatWithUserId: '2' };
    const mockEvent = { target: { tagName: 'DIV' } };
    spyOn(component, 'loadMore');
    component.reloadChatRoom(mockEvent, mockMessage, 0);
    expect(component.currentChatroomId).toBe('1');
    expect(component.loadMore).toHaveBeenCalled();
  });

  it('should expand sub message list', () => {
    const mockResponse = { message: [] };
    httpServiceMock.doPost.and.returnValue(of(mockResponse));
    component.expandSubMessageList(1);
    expect(component.isSubListVisible).toBe(true);
    expect(component.isSubListFetching).toBeFalsy();
  });
});
