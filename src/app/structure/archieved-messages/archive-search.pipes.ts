import { Component, OnInit, Pipe, PipeTransform } from '@angular/core';
import { DatePipe } from '@angular/common';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Pipe({
  name: 'SearchFilter'
})
export class SearchFilterPipe implements PipeTransform {
    transform(searchItems:any, criteria:any): any {
      if (!searchItems)
        return searchItems;
        if(searchItems!=undefined &&  Object.keys(searchItems).length != 0){
        return searchItems.filter(item =>{
           for (let key in item ) {
             if((item[key]+ "").toLowerCase().includes(criteria.toLowerCase())){
               
                return true;
             }
           }
           return false;
        });
      }
    }
}
 