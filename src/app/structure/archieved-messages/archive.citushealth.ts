import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { Router } from '@angular/router';
import { DatePipe } from '@angular/common';
import { Subject } from 'rxjs';
import {
  CONSTANTS,
  LISTING_PAGE_DATERANGE_OPTIONS_MESSAGE,
  ChatAvatarOptions,
  MessageCategory,
  UserGroup,
  DefaultPics,
  ContentTypes,
  msgHistoryDateTimeFormat
} from 'app/constants/constants';
import { isBlank } from 'app/utils/utils';
import { MessageService } from 'app/services/message-center/message.service';
import {
  GetChatMessages,
  GetChatMessagesResponse,
  GetMaskedRepliesPayload,
  defaultGetMaskedRepliesPayload
} from 'app/models/message-center/messageCenter';
import { NotifyService } from 'app/services/notify/notify-service';
import { HttpService } from 'app/services/http/http.service';
import { APIs } from 'app/constants/apis';
import { ArchiveService } from './archive.service';
import { StructureService } from '../structure.service';
import { InboxService } from '../inbox/inbox.service'; 
import { SharedService } from '../../structure/shared/sharedServices';
import { FormsService } from '../forms/forms.service';
import { DateRangeSelected } from '../shared/ch-daterange-picker/date-range-selected.interface';
import { Store, StoreService } from '../shared/storeService';
import { StaticDataService } from '../static-data.service';
import * as moment from 'moment';

declare const $: any;
declare const swal: any;
declare const NProgress: any;

@Component({
  selector: 'cat-page',
  templateUrl: './archive.html',
  styleUrls: ['./archive-list.scss']
})
// TODO: Revamp CHP-7018
export class Archive implements OnInit, OnDestroy {
  perPage = CONSTANTS.perPage;
  totalCount = 0;
  currentPage = 1;
  contentOffset =0;
  contentLimit = CONSTANTS.contentLimit;
  inboxUnreadMessageCount = 0;
  previousPage;
  inboxData:any = [];
  searchData = {};
  userId = '';
  iconPath = '';
  CSRMessages = [];
  searchFlag=false;
  searchInboxKeyword = '';
  activeMessage:any;
  activePatient;
  chatWithHeading;
  archieveDataLength: any = 0;
  beforeLoad = false;

  config:any;
  userDetails:any;
  configData:any = {};
  userData:any = {};
  privileges = this.structureService.getCookie('userPrivileges');
  pageCountMessage = 1;
  hideLoadMore = true;
  doLoadmore = false;
  resetSearchFlag = false;
  msgCount = 1;
  selectSiteId ;
  messageLoader:any = {
    messages: true
  };
  restoredAdminMessage: any = '{{displayName}} has restored the message and will be available in this conversation.';
  inboxDataLength: any = 0;
  qLimit: any = this.structureService.getCookie('archiveqlimit')?parseInt(this.structureService.getCookie('archiveqlimit')): 0;  
  crossTenantChangeSubscriber:any;
  hideSiteSelection: boolean;
  eventsSubject: Subject<void> = new Subject<void>();
  preventMultipleCall = true;
  dateRangeFilterOptions = LISTING_PAGE_DATERANGE_OPTIONS_MESSAGE;
  dateRange: DateRangeSelected;
  dateRangeStoreKey = Store.DATE_RANGE_FILTER_ARCHIVED_MESSAGE;
  messageCategory = MessageCategory;
  chatAvatarOptions = ChatAvatarOptions;
  userGroup = UserGroup;
  defaultPics = DefaultPics;
  isSubListVisible = false;
  isSubListFetching = false;
  chatroomSelected = 0;
  maskedMessageSubList = [];
  labelSite = '';
  labelSiteFilter = '';
  showFilterAppliedMessage = false;
  constructor(
    private httpService: HttpService,
    private router: Router,
    private _archiveService: ArchiveService,
    public _inboxService: InboxService,
    public structureService: StructureService,
    private datePipe: DatePipe,
    public sharedService: SharedService,
    public _formsService: FormsService,
    renderer: Renderer,
    elementRef: ElementRef,
    private storeService: StoreService,
    private messageService: MessageService,
    private notifyService: NotifyService,
    private staticDataService: StaticDataService
  ) {
    this.labelSite = this.staticDataService.getSiteLabelBasedOnMultiAdmission('SITE');
    this.labelSiteFilter = this.staticDataService.getSiteLabelBasedOnMultiAdmission('FILTER_BY_SITES');
    this.defaultPics = DefaultPics;
    this.config = this.structureService.userDataConfig;
    this.userDetails = this.structureService.userDetails;
    this.configData = JSON.parse(this.config);
    this.userData = JSON.parse(this.userDetails);
    localStorage.setItem('messageInboxPaginationArchive', '1');
    this.crossTenantChangeSubscriber = this.sharedService.crossTenantChange.subscribe(() => {
      if (this.router.url.indexOf('/archive') > -1) {
        this.ngOnInit();
      }
    });
    this.messageCategory = MessageCategory;
    this.chatAvatarOptions = ChatAvatarOptions;
    this.userGroup = UserGroup;
  }
  ngOnInit() {
    if (localStorage.getItem('searchInboxkeywordInboxArchive')) {
      const searchOnRefreshText = localStorage.getItem('searchInboxkeywordInboxArchive');
      this.searchInboxKeyword = searchOnRefreshText;
      this.showFilterAppliedMessage = !!searchOnRefreshText;
    }

    this.config = this.structureService.userDataConfig;
    this.userDetails = this.structureService.userDetails;
    this.configData = JSON.parse(this.config);
    this.userData = JSON.parse(this.userDetails);
    this.userId = this.structureService.getCookie('userId');
    this.iconPath = this.structureService.iconPath;
    this.structureService.setCookie('archiveqlimit', this.qLimit, 0);
    this.messageLoader.messages = true;
    this.hideLoadMore = false;
    this.preventMultipleCall = false;
    if (!isBlank(this.storeService.getStoredData(this.dateRangeStoreKey))) {
      this.dateRange = JSON.parse(this.storeService.getStoredData(this.dateRangeStoreKey));
    }
    const params: GetChatMessages = {
      archived: 1,
      pageCount: this.pageCountMessage,
      searchKeyword: this.searchInboxKeyword,
      siteIds: this.selectSiteId,
      dateRange: this.dateRange,
      loaderStatus: true,
      mentionedUsers: ''
    };
    this.structureService.notifySearchFilterApplied(this.showFilterAppliedMessage);
    this.messageService.getChatMessages(params).subscribe(
      (response: GetChatMessagesResponse) => {
        this.showFilterAppliedMessage = false;
        if (response.success) {
          const dataRes: [any] = response.data.messages;
          if (this.pageCountMessage <= 1) {
            this.totalCount = +response.data.totalChatRoomsCount;
            this.structureService.inboxTotalMessageCountArchive = this.totalCount;
            localStorage.setItem('inboxTotalMessageCountArchive', JSON.stringify(this.totalCount));
          }
          if (!dataRes || (!dataRes.length && !this.pageCountMessage)) {
            this.hideLoadMore = true;
          }
          if (this.doLoadmore) {
            this.inboxData = [...this.inboxData, ...dataRes];
          } else {
            this.inboxData = dataRes;
          }
          this.structureService.archiveData = this.inboxData;
          this.archieveDataLength = this.inboxData.length;
          this.beforeLoad = true;
          this.messageLoader.messages = false;
        } else {
          this.hideLoadMore = false;
          this.messageLoader.messages = false;
        }
      },
      () => {
        this.messageLoader.messages = false;
      }
    );
    this.sharedService.sessionRefresh.subscribe((userData) => {
      this.privileges = userData.privileges;
    });
  }
  ngOnDestroy() {
    if (this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
  }
  getSiteIds(data:any){
    this.selectSiteId = data.siteId.toString();
    this.selectSiteId = this.selectSiteId === '' || !this.hideSiteSelection ? 0 : this.selectSiteId;
    localStorage.setItem('siteId', this.selectSiteId);
    if (this.preventMultipleCall) {
      this.activate(false, 0, 0);
    }
    this.preventMultipleCall = true;
  }
  hideDropdown(hideItem: any) {
    this.hideSiteSelection = hideItem.hideItem;
  }
  resetSearch() {
    this.pageCountMessage = 1;
    this.currentPage = 1;
    if (this.searchInboxKeyword) {
      this.resetSearchFlag = true;
    }
    this.searchInboxKeyword = '';
    localStorage.setItem('searchInboxkeywordForPollingArchive', this.searchInboxKeyword);
    this.searchInboxKeyword = '';
    this.pageCountMessage = 1;
    this.doLoadmore = false;
    this.hideLoadMore = false;
    this.activate(true, this.qLimit);
  }
  loadPage(page: number) {
    localStorage.setItem("messageInboxPaginationArchive",JSON.stringify(page));
    this.previousPage = page;
    this.currentPage = page;
    this.contentOffset = (page - 1) * this.contentLimit + 1;
    this.messageLoader.messages = false;
    const searchKeyword = this.searchInboxKeyword || '';
    localStorage.setItem("searchInboxkeywordForPollingArchive",this.searchInboxKeyword);
    let days = ` ${CONSTANTS.allDays} `;
    let actdescription = ` , Filter: ${days}`;
    if (this.qLimit && +this.qLimit !== 0) {
      days = `${this.qLimit} Days `;
      actdescription = ` , Filter: ${days}`;
    }
    if (searchKeyword) {
      this.searchFlag = true;
      actdescription = `${actdescription}, Search Keyword ${searchKeyword}`;
    }
    this.doLoadmore = true;
    const unreadCount = this.inboxUnreadMessageCount;
    const activityData = {
      activityName: 'Pagination Messages',
      activityType: 'manage messaging',
      activityDescription: `Paginated ${this.pageCountMessage} Page, Unread Count: ${unreadCount} ${actdescription}`
    }
    this.structureService.trackActivity(activityData);
    this.activate(true, this.qLimit, page);
    this.resetSearchFlag = false;
  }
  loadMoreMessages() {
    this.messageLoader.messages = false;
    this.pageCountMessage = this.pageCountMessage+1;
    var searchKeyword = this.searchInboxKeyword ? this.searchInboxKeyword : '';
    localStorage.setItem("searchInboxkeywordForPollingArchive",this.searchInboxKeyword);
    if(searchKeyword){
      this.searchFlag = true; 
    }
    var days =" All Days "
    var actdescription =" , Filte: "+days;
    if(this.qLimit && this.qLimit != 0){
        
            days =this.qLimit+" Days "
            actdescription =" , Filte: "+days;           
    }
    if(searchKeyword){
      this.searchFlag = true; 
      actdescription = actdescription +", Search Keyword "+searchKeyword;  
    }
    this.doLoadmore =true;
    var activityData = {
        activityName: "Load More Archive Messages",
        activityType: "manage messaging",
        activityDescription: "Loaded "+this.pageCountMessage+ " Page"+ actdescription
    }
   
    this.structureService.trackActivity(activityData);

    this.doLoadmore =true;
    this.activate(true,this.qLimit,this.pageCountMessage);        
   }
  gotoChatRoom(evt, message){
    this.sharedService.goToInnerPage = true;
            if (evt && (((($(evt.target.parentElement).hasClass('from-name') && this.configData.show_prototypes === '1' && this.userData.group !== '3') || ($(evt.target).hasClass('icon-pending') && this.userData.privileges.indexOf('clinicianApprover') !== -1)) && (message.grp === '3' || (message.chatWithRoleId === '3' && message.grp === this.userData.group))) || $(evt.target.parentElement).hasClass('forward-link-col'))) {
                if ($(evt.target.parentElement).hasClass('from-name')) {
                    this.activePatient = message;
                } else if ($(evt.target).hasClass('icon-pending')) {
                    this.activePatient = message;
                } else {
                }
            } else if (evt.target.tagName !== 'A') {
                this.activeMessage = message; //Forwad option from chat window.
                var chatWithDob = "";
                var fromDob = "";
                var chatwithDay = new Date(message.chatWithDob).getDay();
                var messageDay = new Date(message.dob).getDay();
                if (message.chatWithDob && !isNaN(chatwithDay)) {
                    chatWithDob = " DOB " + this.datePipe.transform(message.chatWithDob, 'MM/dd/yy');
                }
                if (message.dob && !isNaN(messageDay)) {                  
                    fromDob = " DOB " + this.datePipe.transform(message.dob, 'MM/dd/yy');
                }
                var chatwith = this.userData.userId === message.userid ? (message.chatWithRoleId === "3" ? message.chatWith + chatWithDob : message.chatWith) : (message.grp === "3" ? message.fromName + fromDob : message.fromName);
                this.chatWithHeading = message.chatHeading;
                const targetID = message.chatroomId || message.chatroomid;
                if (this.userData.userId !== message.userid && message.grp === "3") {
                    var pushMessage = this.userData.displayName + ' is reviewing your message "' + message.message + '.."';
                    //SharedService.sentPushNotification(this.message.userid, 0, pushMessage);
                }
                if (this.userData.userId !== message.userid) {
                    this.activeMessage.chatWith = message.fromName;
                    this.activeMessage.chatWithUserId = message.userid;
                } else {
                    this.activeMessage.chatWith = message.chatWith;
                    this.activeMessage.chatWithUserId = message.chatWithUserId;
                }
                localStorage.setItem('targetId', targetID);
                localStorage.setItem('targetName', 'group-chat');
                localStorage.setItem('chatWithHeading',this.chatWithHeading);
                localStorage.setItem('activeMessage',JSON.stringify(this.activeMessage));
                localStorage.setItem('archived', 'true');
                this.router.navigate(['/archive/messages']);
            }
  }
  restoreMaskedMessages(msg,pageCountMessage) {
    var IsEnterClicked=false;
    $(document).keypress((event)=> {
      if (event.keyCode == 13) {
          IsEnterClicked=true;           
      }
    });
    swal({
        title: "Are you sure?",
        text: "You are going to restore the message(s)",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true
    },  (confirm) => {
        if(IsEnterClicked){
          IsEnterClicked=false;
            swal.close();   
            return false;
        }
        if(confirm) { 
    const data = {
        baseId: (msg.subList && msg.subList.length) ? msg.subList[0].baseId : 0,
        initiatedBaseId: msg.initiatedBaseId,
        userId: this.userId,
        createdby: msg.createdby,
        chatroomId: msg.chatroomId,
        subList: '',
        invitedStatus:msg.invited_status,
        displayname: this.userData.displayName
    }
    if(msg.subList && msg.subList.length) {
        var result = msg.subList.map(function(sub) {
            return sub.chatroomId;
        }).join(',');
        data.subList = result;
    }
    this._archiveService.restoreMaskedMessages(data).then((res) => {
        var response = JSON.parse(JSON.stringify(res));
        if(response.status) {
            var pollingMessage = (this.restoredAdminMessage).replace(/{{displayName}}/g, this.userData.displayName);
            let chatroomIds = [];
            if( data.invitedStatus == '0') {
                chatroomIds.push(data.chatroomId);
                if(data.subList != '') {
                    $.merge(chatroomIds,data.subList.split(','));
                }
            } else {
                chatroomIds.push(data.chatroomId);
                if(data.userId == data.createdby) {
                    chatroomIds.push(data.initiatedBaseId);
                }
            }
            chatroomIds.forEach(id => {
                this.structureService.socket.emit("userMessagetoServer", { data: pollingMessage, notification: true, chatRoomId: id, insert: false, tenantId: this.userData.tenantId, tenantName: this.userData.tenantName });                    
            });
            var notify = $.notify('Message Restored successfully');
            setTimeout(function () {
                notify.update({ 'type': 'success', 'message': '<strong> Message Restored successfully</strong>' });
            }, 1000);
            var activityData = {
                activityName: "Restore Messages",
                activityType: "manage messaging",
                activityDescription: this.userData.displayName + " restored messages of chatroom - (" + chatroomIds.join() + ")"
            };
            this.structureService.trackActivity(activityData);
            this.removeScopeMessageOnRestore(msg.chatroomId);
            this.sharedService.reloadInbox = true;
            var pageCountMessage = parseInt(localStorage.getItem("messageInboxPaginationArchive")) ?  parseInt(localStorage.getItem("messageInboxPaginationArchive")) :  pageCountMessage;
            this.loadPage(pageCountMessage);
        }
    });
}
})
}
enterpressalert(e) {
    const code = e.keyCode ? e.keyCode : e.which;
    if (code === 13) { //Enter keycode
        e.preventDefault();
        localStorage.setItem("searchInboxkeywordForPollingArchive",this.searchInboxKeyword);
        if (this.searchInboxKeyword) {
            this.searchMessagesAndDocs();
        }
    } 
    
}
restoreChatroomMessages(chatroomId, messageId, isBroadcast,pageCountMessage) {
    var IsEnterClicked=false;
    $(document).keypress((event)=> {
      if (event.keyCode == 13) {
          IsEnterClicked=true;           
      }
    }); 
    swal({
        title: "Are you sure?",
        text: "You are going to restore the message(s)",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true
    }, (confirm) => {
        if(IsEnterClicked){
          IsEnterClicked=false;
            swal.close();   
            return false;
        }
        if(confirm) {  
    let pollingMessage = (this.restoredAdminMessage).replace(/{{displayName}}/g, this.userData.displayName);
    this._archiveService.restoreChatroomMessages(chatroomId, isBroadcast).then((data) => {
        this.structureService.socket.emit("userMessagetoServer", { data: pollingMessage, notification: true, chatRoomId: chatroomId, insert: false, tenantId: this.userData.tenantId, tenantName: this.userData.tenantName });
        let notify = $.notify('Message Restored successfully');
        setTimeout(function () {
            notify.update({ 'type': 'success', 'message': '<strong> Message Restored successfully</strong>' });
        }, 1000);
        let activityData = {
            activityName: "Restore Messages",
            activityType: "manage messaging",
            activityDescription: this.userData.displayName + " restored messages of chatroom - " + chatroomId
        };
        this.structureService.trackActivity(activityData);
        this.removeScopeMessageOnRestore(chatroomId);
        this.sharedService.reloadInbox = true;
        pageCountMessage = parseInt(localStorage.getItem("messageInboxPaginationArchive")) || pageCountMessage;
        this.loadPage(pageCountMessage);
    }).catch(() => {
      this.notifyService.notifyCommonError();
    });
}
})
}   
 removeScopeMessageOnRestore(chatroomId) {
    if (Array.isArray(chatroomId)) {
        this.inboxData = this.inboxData.filter(msg=>{
            if(chatroomId.indexOf(msg.chatroomId) !== -1)
            this.structureService.inboxUnreadMessageCount = this.structureService.inboxUnreadMessageCount + (msg.messageType=='2' ? (parseInt(msg.maskedUnreadCount) ? parseInt(msg.maskedUnreadCount) : 0) : parseInt(msg.unreadCount || msg.unread));
            return chatroomId.indexOf(msg.id) == -1
        });
    } else {
        this.inboxData = this.inboxData.filter(msg=>{
            if(+msg.chatroomId === +chatroomId)
            this.structureService.inboxUnreadMessageCount = this.structureService.inboxUnreadMessageCount + (msg.messageType=='2' ? (parseInt(msg.maskedUnreadCount) ? parseInt(msg.maskedUnreadCount) : 0) : parseInt(msg.unreadCount || msg.unread));
            return msg.id != chatroomId
        });
    }
    if(!this.structureService.inboxUnreadMessageCount || this.structureService.inboxUnreadMessageCount < 0) {
        this.structureService.inboxUnreadMessageCount = 0;    
    }
    let types = 'messages';      
            this.structureService.getInboxCounts(types).then((inboxCounts)=> { 
              var resCounts = JSON.parse(JSON.stringify(inboxCounts));
              this.structureService.inboxUnreadMessageCount = parseInt(resCounts.messages);
              if(!this.structureService.inboxUnreadMessageCount || this.structureService.inboxUnreadMessageCount < 0) {
                this.structureService.inboxUnreadMessageCount = 0;
              }
              this.sharedService.unreadCounts.emit({counts: resCounts, types: types, trackRoot:{function: 'removeScopeMessageOnRestore', page:'archive.citushealth.ts', lineNo: '341'}});

            }, (err)=> {
              this.structureService.inboxUnreadMessageCount = 0;                   
            });
    
    if(this.inboxData.length) {
        this.inboxDataLength = this.inboxData.length;
        this.archieveDataLength = this.inboxData.length;
    } else {
        this.inboxDataLength = 0;
        this.archieveDataLength = 0;
    }
    this.structureService.archiveData = this.inboxData;
}
  viewInventory(e, message) {
        message.unread = 0;
        message.unreadCount = 0;
        var inventoryId = message.inventoryId;
        var userId = message.userid;
        var activityData = {
            activityName: "Select Inventory",
            activityType: "inventory",
            activityDescription: this.userData.displayName + " has viewed inventory report(" + message.message + ") of " + message.patientName
        };
        this.structureService.trackActivity(activityData);
        let routingUrl = '/supplies/inventory-submissions/submission/' + message.userid + '/' + message.inventoryId + '/' + message.id;
        this.router.navigate([routingUrl]);
    }
    
    searchMessagesAndDocs() {
        const copy = this.inboxData;
        this.pageCountMessage = 1;
        this.currentPage = 1;
        localStorage.setItem("messageInboxPaginationArchive",JSON.stringify(this.currentPage));
        this.doLoadmore =false;
        this.hideLoadMore = false;
        this.searchInboxKeyword = $("#userSearchTxt").val();
        localStorage.setItem("searchInboxkeywordForPollingArchive",this.searchInboxKeyword);
        if (this.searchInboxKeyword) {
            this.searchFlag = true;
        } else {
            this.searchFlag = false;
        }
        this.activate(false);
    }
  changeMessagesCount() {
    this.activate(false, this.qLimit,0);
    this.searchInboxKeyword = '';
    this.doLoadmore =false;
    this.pageCountMessage = 1;
    this.structureService.setCookie('archiveqlimit',this.qLimit, 0);
  }
  activate(loaderStatus = true, qlimit = 0, pageCountMessage = 1){
    localStorage.setItem("searchInboxkeywordInboxArchive",this.searchInboxKeyword);
    localStorage.setItem("searchInboxkeywordForPollingArchive",this.searchInboxKeyword);
    NProgress.start();
    this.hideLoadMore = true;
    const param: GetChatMessages = {
        archived: 1,
        pageCount: pageCountMessage,
        searchKeyword: this.searchInboxKeyword,
        siteIds: this.selectSiteId,
        dateRange: this.dateRange,
        loaderStatus: loaderStatus,
        mentionedUsers: ''
     }; 
    if(this.userId && (this.searchFlag || !isBlank(this.searchInboxKeyword))){
      this.structureService.notifySearchFilterApplied(this.showFilterAppliedMessage);
      this.messageService.getChatMessages(param).subscribe((response: GetChatMessagesResponse) => {    
        this.showFilterAppliedMessage = false;  
        let dataRes: any = response.data.messages;
    if(pageCountMessage <= 1){
      this.totalCount = +response.data.totalChatRoomsCount;
    this.structureService.inboxTotalMessageCountArchive = this.totalCount;
    localStorage.setItem("inboxTotalMessageCountArchive",JSON.stringify(this.totalCount));
    }
      this.inboxData = dataRes;
      NProgress.done();
      this.structureService.archiveData=this.inboxData;
      this.msgCount = dataRes.length;
      this.archieveDataLength = this.inboxData.length;
      this.beforeLoad = true;
      this.hideLoadMore = false;
      this.messageLoader.messages = false;     
      if(!dataRes || (dataRes && dataRes.length==0) || (dataRes.length < 1) ) {
        this.hideLoadMore = true;
    }
    this.searchFlag = false;  
    },() => {
        this.hideLoadMore = false;
        this.messageLoader.messages = false;
        NProgress.done();
    });
    } else {
      this.hideLoadMore = false;
      param.searchKeyword = '';
      this.structureService.notifySearchFilterApplied(this.showFilterAppliedMessage);
      this.messageService.getChatMessages(param).subscribe((response: GetChatMessagesResponse) => {
        this.showFilterAppliedMessage = false;
        if(response.success) {
          let dataRes: [any] = response.data.messages;
          if(pageCountMessage <= 1) {
            this.totalCount = +response.data.totalChatRoomsCount;
            this.structureService.inboxTotalMessageCountArchive = this.totalCount;
            localStorage.setItem("inboxTotalMessageCountArchive",JSON.stringify(this.totalCount));
          }
          if (!dataRes || !dataRes.length) {
            this.hideLoadMore = true;
          }
          this.inboxData = dataRes;
          this.msgCount = dataRes.length; 
          NProgress.done();
          this.structureService.archiveData=this.inboxData;
          this.archieveDataLength = this.inboxData.length;
          this.beforeLoad = true;
          this.resetSearchFlag = false;
          this.messageLoader.messages = false;
          NProgress.done();
        } else {
          this.messageLoader.messages = false;
          NProgress.done();
        }        
    },() => {       
        this.messageLoader.messages = false;
        NProgress.done();
    });
    } 
  }
  viewStructuredForm(event, message) {
        var data:any = {};
        data = message;
        message.fromArchive = true;
        data.loginUserId = this.userData.userId;
        this.structureService.setCookie('formData',encodeURIComponent(JSON.stringify(message)), 1);
        localStorage.setItem('formData', encodeURIComponent(JSON.stringify(message)));
        this._formsService.checkIsFormFilled(data).then(function(result: any){
        });
        if(message.form_submission_id && message.form_submission_id!=0){
            this.router.navigate(['/forms/list/view']);
        }else{
            this.router.navigate(['/forms/view-form']);
        }
    }

/**On date range filter emits the value. */
dateRangeSelected(event: DateRangeSelected) {
    if (!isBlank(event.changeDetectedOnInit)) {
      this.showFilterAppliedMessage = event.changeDetectedOnInit;
    } else {
      this.dateRange = event;
      this.resetSearch();
    }
}

/** For ngFor directive performance */
trackByForArchivedMsgData(index, item): number {
  return item.chatroomId;
}

expandSubMessageList(chatroomId) {
  this.chatroomSelected = chatroomId;
  this.isSubListVisible = !this.isSubListVisible;        
  if(this.isSubListVisible) {
      this.isSubListFetching = true;
      const params: GetMaskedRepliesPayload = {
          chatRoomId: chatroomId,
          archived: 1
      };
      this.httpService.doPost(APIs.getMaskedReplyMessages, { ...defaultGetMaskedRepliesPayload, ...params }, ContentTypes.FORM).subscribe((response) => {
          this.maskedMessageSubList = response.message;          
          this.isSubListFetching = false;
      });   
  }
}
formatMessageDeletedTime(dateTimeUTC: string): string {
  return dateTimeUTC ? moment(dateTimeUTC).format(msgHistoryDateTimeFormat) : '';
}

}

