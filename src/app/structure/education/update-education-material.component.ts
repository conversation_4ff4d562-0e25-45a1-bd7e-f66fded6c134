import { Component, OnInit, ViewChild, ElementRef } from "@angular/core";
import { Router, ActivatedRoute } from "@angular/router";
import { RegistrationService } from "../registration/registration.service";
import { StructureService } from "../structure.service";
import { ToolTipService } from "../tool-tip.service";
import { InboxService } from "../inbox/inbox.service";
import { filterMessageGroupPipe } from "./messagegroup-search.pipes";
import { SharedService } from "../shared/sharedServices";
import { isBlank, getUserPreferenceSiteIds } from 'app/utils/utils';
import {
  FormBuilder,
  FormGroup,
  Validators,
  FormControl
} from "@angular/forms";
import { Http, Headers, RequestOptions } from "@angular/http";
import { GlobalDataShareService } from "./../shared/global-data-share.service";
import { Subject } from 'rxjs';
declare var $: any;
declare var NProgress: any;

@Component({
  selector: "app-update-education-material",
  templateUrl: "./update-education-material.component.html",
  styleUrls: ['./education-material.component.scss']
})
export class UpdateEducationMaterialComponent implements OnInit {
  @ViewChild("userSearch") srch: ElementRef;
  editGroupMessage = false;
  addGroupMessage = false;
  searchInboxkeyword;
  staffList = [];
  otherTenantStaffList = [];
  memberList = [];
  otherTenantMemberList = [];
  offset = 0;
  limit = 20;
  allMessageGroups = [];
  hideLoadMore = false;
  selected = [];
  prevText;
  newMemberList = [];
  newMemberOtherTenantList = [];
  newallUserDetailsInMsgGroup = [];
  selectedTenant: any;
  newMessageGroups;
  newMemberListByRoleWise = [];
  newMemberListByRoleWiseTagUser = [];
  newMemberOtherTenantListByRoleWise = [];
  groupListNames = [];
  newMember;
  existingMemberIds = [];
  selectedGroupMembers = [];
  selectedGroupMemberIds = [];
  checkedIds = [];
  selectedisPublic;
  documentMaterialGroup: FormGroup;
  updateParams;
  userDetails: any;
  userData;
  privilegeCreateEducationMaterial = false;
  allowSave = true;
  privilegeManageAllMessageGroup = false;
  addMemberError = false;
  noMemberError = false;
  disableButton = true;
  loadingGroups: Boolean;
  loadingStaffs: Boolean;
  loadingPatients: Boolean;
  createdPatientIdIndex: any;
  messageGroups: any = "groups";
  optionShow: any = "taggedUsers";
  userListChatwith: any = [];
  memberDataGroupWise: any;

  config:any = {};
  configData:any = {};
  clinicalUserDetails;
  allUserDetailsInMsgGroup = [];
  searchText = "";
  searchTexts = "";
  crossTenantOptions = [];
  crossTenantName: String;
  MessageGroupWithLoader: any = {
    groups: true,
    staffs: true,
    otherTenantstaff: true
  };
  enableCommunicationWithInternalstaffs = false;
  enableCommunicationWithOtherTenantPatients = false;
  enableCommunicationWithOtherTenantStaffs = false;
  internalSiteId: any;
  crossTenatsForBranch = [];
  column3 = false;
  column4 = false;

  signDocFile = [];
  fileToUpload;
  fileName: string;
  invalidFileType = false;
  selectedMaterialId: any;
  materialData: any;
  getAssignedUsersArray = [];
  getAssignedUsersArrayTemp = [];
  getAssignedUsersArrayTempOnLoad = [];
  selectedGroupDetails;
  othertenantSelected = false;
  otherTenantPatientsList;
  otherTenantPatientsListTemp;
  isPatientUser = false;
  userTaggedUsers;
  dataLoadingMsg = true;
  showUserTag = "0";
  formSubmitted = false;
  uploadedFileName;
  isNursingAgencyEnabled = 0;
  nursing_agencies;
  nursingAgencyTags: any = [];
  canEdit = true;

  chatWithTenantFilter: any = {
    selectedTenant: null,
    tenants: [],
    filterEnabled: false,
    enabledReset: false
  };
  crossTenantOptionsChatWith = [];
  searchKey = "";

  chatwithPageCount = {
    staffs: 0,
    patients: 0,
    usertag: 0
  };
  noMoreItemsAvailable = {
    users: false
  };
  loadMoreSearchValue: any = undefined;
  callFromInitialCountChatWithUserlist: boolean = false;
  usersList: any;
  chatWithUsersLoading = true;
  chatWithLoader: any = {
    groups: true,
    usertag: true,
    staffs: true,
    otherTenantstaff: true,
    patients: true,
    otherTenantPatients: true
  };
  messageGroupMemberIds;
  addedUserGroupId = '';
  loadMoremessage = {
    users: "Load more"
  };
  selectedroleBasedStaffs = [];
  userTaggedUsersById;
  userTaggedUsersCount;
  noMoreItemsAvailableUserags = true;
  privileges = this._structureService.getCookie('userPrivileges');
  singleSelection: boolean = true; 
  siteId:any=0;
  hideSiteSelection:Boolean;
  eventsSubject: Subject<void> = new Subject<void>();
  multiSiteEnable: boolean;
  private preventMultipleUserTagCallOnInit = true;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private _structureService: StructureService,
    private _ToolTipService: ToolTipService,
    private _inboxService: InboxService,
    private _formBuild: FormBuilder,
    private MessageGroupSearchFilter: filterMessageGroupPipe,
    public _SharedService: SharedService,
    private registrationservice: RegistrationService,
    private _http: Http,
    public _GlobalDataShareService: GlobalDataShareService
  ) { 
    this.config = this._structureService.userDataConfig;
    this.configData = JSON.parse(this.config);
  }

  ngOnInit() {
    this.selectedMaterialId = this._structureService.getCookie(
      "educationMaterialId"
    );
    this.getEducationData();
    
    this.userDetails = this._structureService.userDetails;
    this.userData = JSON.parse(this.userDetails);
    this.multiSiteEnable = this.userData.config.enable_multisite == 1 ? true : false;
    if(this.multiSiteEnable && !isBlank(this.userData.defaultSitesFilter) && this.userData.defaultSitesFilter != 0) {
      this.siteId = getUserPreferenceSiteIds(this.userData.defaultSitesFilter);
    }
    this.showUserTag = this.userData.config.show_user_tagging;
    this.isNursingAgencyEnabled = this.userData.config.enable_nursing_agencies_visibility_restrictions;
    this.nursing_agencies = this.userData.nursing_agencies
      ? this.userData.nursing_agencies
      : "";
    this.crossTenantOptionsChatWith = this.userData.crossTenantsDetails;

    this.chatWithTenantFilter.tenants = this.crossTenantOptionsChatWith;

    console.log(
      "NGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGG"
    );
    console.log(this._structureService.getCookie("userPrivileges").indexOf("manageTenants"));
    console.log(this.privileges);
    console.log(this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication'));
    console.log(this.userData);
    console.log(this.userData.crossTenantsDetails);
    console.log(this.crossTenantOptionsChatWith);
    console.log(this.chatWithTenantFilter.tenants);
    console.log(
      "NGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGG"
    );

    if (this.nursing_agencies != "") {
      this.showUserTag = "0";
      console.log("this.nursing_agencies !=");
    }
    if (this.showUserTag != "1") {
      this.optionShow = "staffs";
    } else {
      this.optionShow = "taggedUsers";
    }

    this.selectedTenant = {};

    if (this.nursing_agencies != "") {
      this.documentMaterialGroup = this._formBuild.group({
        materialName: ["", [Validators.required, this.noWhitespaceValidator]],
        description: [""],
        nursingAgencyUserTag: [""],
        fileup: ["", [Validators.required, this.noWhitespaceValidator]]
      });
    } else {
      this.documentMaterialGroup = this._formBuild.group({
        materialName: ["", [Validators.required, this.noWhitespaceValidator]],
        description: [""],
        nursingAgencyUserTag: [""],
        fileup: ["", [Validators.required, this.noWhitespaceValidator]]
      });
    }

    if (
      this._structureService
        .getCookie("userPrivileges")
        .indexOf("enableEducationTraining") != -1 ||
      this._structureService
        .getCookie("userPrivileges")
        .indexOf("manageTenants") != -1
    ) {
      this.privilegeCreateEducationMaterial = true;
    }







    console.log("this.showUserTag", this.showUserTag);
    if (this.showUserTag == "0") {
      console.log("11111111111111111112222222222222222");
      if (
        this.userData.organizationMasterId != "0" &&
        this.userData.crossTenantsDetails &&
        this.userData.crossTenantsDetails.length &&
        this.userData.masterEnabled == "0"
      ) {
        if (
          this.userData.crossTenantsDetails.length > 1 &&
          this.configData.allow_multiple_organization == 1 &&
          (this.configData.enable_nursing_agencies_visibility_restrictions !=
            1 ||
            (this.configData.enable_nursing_agencies_visibility_restrictions ==
              1 &&
              this.userData.nursing_agencies == ""))
        ) {
          this.column3 = true;
          this.crossTenatsForBranch = this.crossTenantOptions = this.userData.crossTenantsDetails;
          //   this.crossTenantOptions.forEach(tenant => {
          //     if (
          //       tenant.id == this._structureService.getCookie("crossTenantId")
          //     ) {
          //       this.crossTenantName = tenant.tenantName;
          //       this.selectedTenant = tenant;
          // 	}
          //   });

          this.crossTenantOptions.forEach(tenant => {
            if (
              tenant.id == this._structureService.getCookie("crossTenantId")
            ) {
              this.crossTenantName = tenant.tenantName;
              this.selectedTenant = tenant;
              this.chatWithTenantFilter.selectedTenant = tenant.id;
              this._GlobalDataShareService.setSelectedTenantDetails(tenant);
            }
          });

          this.enableCommunicationWithOtherTenantPatients = true;
          this.enableCommunicationWithOtherTenantStaffs = true;
          console.log("Condition21");
        } else {
          this.crossTenatsForBranch = this.crossTenantOptions = [];
          this.enableCommunicationWithOtherTenantPatients = false;

          this.enableCommunicationWithOtherTenantStaffs = false;
          this.column3 = false;
          this.selectedTenant = {
            id: this._structureService.getCookie("tenantId")
          };
          console.log("Condition22");
        }
      } else if (
        this.userData.masterEnabled == "1" &&
        this.userData.isMaster == "1"
      ) {
        this.column3 = true;
        this.crossTenantOptions = this.userData.crossTenantsDetails;
        this.crossTenantOptions.forEach(tenant => {
          if (tenant.id == this._structureService.getCookie("crossTenantId")) {
            this.crossTenantName = tenant.tenantName;
            this.selectedTenant = tenant;
          }
        });
        this.enableCommunicationWithOtherTenantStaffs = true;
        this.enableCommunicationWithOtherTenantPatients = true;
        console.log("Condition23");
      } else if (
        this.userData.masterEnabled == "1" &&
        this.userData.isMaster == "0"
      ) {
        this.column3 = false;
        this.crossTenantOptions = this.userData.crossTenantsDetails;
        this.crossTenantOptions.forEach(tenant => {
          if (tenant.id == this._structureService.getCookie("crossTenantId")) {
            this.crossTenantName = tenant.tenantName;
            this.selectedTenant = tenant;
            this.crossTenatsForBranch.push(tenant);
          }
        });
        this.enableCommunicationWithInternalstaffs = true;
        this.enableCommunicationWithOtherTenantStaffs = false;
        this.enableCommunicationWithOtherTenantPatients = false;
        this.internalSiteId = this.userData.master_details.id;
        console.log("Condition24");
      } else {
        this.column3 = false;
        this.crossTenatsForBranch = this.crossTenantOptions = [];
        this.enableCommunicationWithOtherTenantStaffs = false;
        this.enableCommunicationWithOtherTenantPatients = false;
        this.selectedTenant = {
          id: this._structureService.getCookie("tenantId")
        };
        console.log("Condition25");
      }
    } else {
      console.log("222222222222222222222222222222222");
      if (
        this.userData.organizationMasterId != "0" &&
        this.userData.crossTenantsDetails &&
        this.userData.crossTenantsDetails.length &&
        this.userData.masterEnabled == "0"
      ) {
        if (
          this.userData.crossTenantsDetails.length > 1 &&
          this.configData.allow_multiple_organization == 1 &&
          (this.configData.enable_nursing_agencies_visibility_restrictions !=
            1 ||
            (this.configData.enable_nursing_agencies_visibility_restrictions ==
              1 &&
              this.userData.nursing_agencies == ""))
        ) {
          this.column4 = true;
          this.column3 = false;
          this.crossTenatsForBranch = this.crossTenantOptions = this.userData.crossTenantsDetails;
          this.crossTenantOptions.forEach(tenant => {
            if (
              tenant.id == this._structureService.getCookie("crossTenantId")
            ) {
              this.crossTenantName = tenant.tenantName;
              this.selectedTenant = tenant;
              this.chatWithTenantFilter.selectedTenant = tenant.id;
              this._GlobalDataShareService.setSelectedTenantDetails(tenant);
            }
          });
          this.enableCommunicationWithOtherTenantPatients = true;
          this.enableCommunicationWithOtherTenantStaffs = true;
          console.log("Condition21");
        } else {
          this.crossTenatsForBranch = this.crossTenantOptions = [];
          this.enableCommunicationWithOtherTenantPatients = false;
          this.enableCommunicationWithOtherTenantStaffs = false;
          this.column4 = false;
          this.column3 = true;
          this.selectedTenant = {
            id: this._structureService.getCookie("tenantId")
          };
          console.log("Condition22");
        }
      } else if (
        this.userData.masterEnabled == "1" &&
        this.userData.isMaster == "1"
      ) {
        this.column4 = true;
        this.column3 = false;
        this.crossTenantOptions = this.userData.crossTenantsDetails;
        this.crossTenantOptions.forEach(tenant => {
          if (tenant.id == this._structureService.getCookie("crossTenantId")) {
            this.crossTenantName = tenant.tenantName;
            this.selectedTenant = tenant;
            this.chatWithTenantFilter.selectedTenant = tenant.id;
            this._GlobalDataShareService.setSelectedTenantDetails(tenant);
          }
        });
        this.enableCommunicationWithOtherTenantStaffs = true;
        this.enableCommunicationWithOtherTenantPatients = true;
        console.log("Condition23");
      } else if (
        this.userData.masterEnabled == "1" &&
        this.userData.isMaster == "0"
      ) {
        this.column4 = false;
        this.column3 = true;
        this.crossTenantOptions = this.userData.crossTenantsDetails;

        console.log("22222222222222222222222222222222");
        console.log(this.crossTenantOptions);
        console.log("2222222222222222222222222222222222");


        this.crossTenantOptions.forEach(tenant => {
          if (tenant.id == this._structureService.getCookie("crossTenantId")) {
            this.crossTenantName = tenant.tenantName;
            this.selectedTenant = tenant;
            this.chatWithTenantFilter.selectedTenant = tenant.id;
            this._GlobalDataShareService.setSelectedTenantDetails(tenant);
            this.crossTenatsForBranch.push(tenant);
          }
        });

        this.enableCommunicationWithInternalstaffs = true;
        this.enableCommunicationWithOtherTenantStaffs = false;
        this.enableCommunicationWithOtherTenantPatients = false;
        this.internalSiteId = this.userData.master_details.id;
        console.log("Condition24");
      } else {
        this.column4 = false;
        this.column3 = true;
        this.crossTenatsForBranch = this.crossTenantOptions = [];
        this.enableCommunicationWithOtherTenantStaffs = false;
        this.enableCommunicationWithOtherTenantPatients = false;
        this.selectedTenant = {
          id: this._structureService.getCookie("tenantId")
        };
      }
    }




//**************************************** Starts ************************************************************** */

if((this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
&& this.configData.allow_multiple_organization==1 
&& this.userData.crossTenantsDetails.length > 1 
&& this.userData.masterEnabled == '0'
&& this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
|| (this.userData.masterEnabled == '1' 
&& this.userData.isMaster == '1')
|| (this.userData.masterEnabled == '1' 
&& this.userData.isMaster == '0'
&& this.userData.group !='3')
|| (this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
&& this.configData.allow_multiple_organization==1 
&& this.userData.crossTenantsDetails.length > 1 
&& this.userData.masterEnabled == '0'
&& this.configData.enable_nursing_agencies_visibility_restrictions == '1'
&& this.userData.nursing_agencies == '')) {
  this.chatWithTenantFilter.filterEnabled = true;
  this.chatWithTenantFilter.setTenantDropDown = true;
}else {
  this.chatWithTenantFilter.filterEnabled = false;
  this.chatWithTenantFilter.tenants = [];
}
if (
  this.userData.organizationMasterId != 0 &&
  this.userData.crossTenantsDetails &&
  this.userData.crossTenantsDetails.length &&
  this.userData.masterEnabled == '0'
) {
  if (this.userData.crossTenantsDetails.length > 1 
    && this._structureService.getCookie('userPrivileges').indexOf('allowMultipleOrganizationsStaffCommunication') !== -1 
    && this.configData.allow_multiple_organization == 1
    && (this.configData.enable_nursing_agencies_visibility_restrictions != 1 || (this.configData.enable_nursing_agencies_visibility_restrictions == 1 && this.userData.nursing_agencies == ""))
  ) {
    this.column3 = true;
    this.crossTenatsForBranch = this.crossTenantOptions = this.userData.crossTenantsDetails;
    this.crossTenantOptionsChatWith = this.userData.crossTenantsDetails;
    this.chatWithTenantFilter.tenants = this.crossTenantOptionsChatWith;
    this.crossTenantOptions.forEach((tenant) => {
      if (tenant.id == this._structureService.getCookie('crossTenantId')) {
        this.crossTenantName = tenant.tenantName;
        this.selectedTenant = tenant;
        this.chatWithTenantFilter.selectedTenant = tenant.id;
        this._GlobalDataShareService.setSelectedTenantDetails(tenant);
      }
    });
  } else {
    this.crossTenatsForBranch = this.crossTenantOptions = [];
    this.column3 = false;
  }
} else if (this.userData.masterEnabled == '1' && this.userData.isMaster == '1') {
  this.column3 = true;
  this.crossTenatsForBranch = this.crossTenantOptions = this.userData.crossTenantsDetails;
  this.crossTenantOptionsChatWith = this.userData.crossTenantsDetails;
  this.chatWithTenantFilter.tenants = this.crossTenantOptionsChatWith;
  this.crossTenantOptions.forEach((tenant) => {
    if (tenant.id == this._structureService.getCookie('crossTenantId')) {
      this.crossTenantName = tenant.tenantName;
      this.selectedTenant = tenant;
      this.chatWithTenantFilter.selectedTenant = tenant.id;
      this._GlobalDataShareService.setSelectedTenantDetails(tenant);
    }
  });
} else if (this.userData.masterEnabled == '1' && this.userData.isMaster == '0') {
  this.column3 = true;
  this.crossTenantOptions = this.userData.crossTenantsDetails;
  this.crossTenantOptions.forEach((tenant) => {
    if (tenant.id == this._structureService.getCookie('crossTenantId')) {
      this.crossTenantName = tenant.tenantName;
        this.chatWithTenantFilter.selectedTenant = tenant.id;
        this._GlobalDataShareService.setSelectedTenantDetails(tenant);
      this.selectedTenant = tenant;
        this.chatWithTenantFilter.tenants = this.userData.crossTenantsDetails;
        
    }
  });
  this.crossTenantOptions.forEach((tenant) => {
    if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
        this.selectedTenant = tenant;
        this.chatWithTenantFilter.selectedTenant = tenant.id;
        this._GlobalDataShareService.setSelectedTenantDetails(tenant);
        this.crossTenantOptions = this.userData.crossTenantsDetails;
        this.chatWithTenantFilter.tenants = this.crossTenantOptions;
        this.crossTenantOptions.forEach((tenant) => {
          if (tenant.id == this._structureService.getCookie("crossTenantId")) {
              this.crossTenantName = tenant.tenantName;
          }
      });
    }
  });
  this.enableCommunicationWithInternalstaffs = true;
  this.internalSiteId = this.userData.master_details.id;
  
 
  if (this.userData.organizationMasterId != 0 
    && this.userData.crossTenantsDetails 
    && this.userData.crossTenantsDetails.length > 1) {
    this.crossTenantOptionsChatWith = this.userData.crossTenantsDetails;
    this.chatWithTenantFilter.tenants = this.crossTenantOptions;
    this.crossTenantOptions.forEach((tenant) => {
        if (tenant.id == this._structureService.getCookie("crossTenantId")) {
            this.crossTenantName = tenant.tenantName;
        }
    });
    if ((this.crossTenantOptions.length > 1
        && this.privileges.indexOf('allowOrganizationSwitching') != -1
        && this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication') !== -1 
        && this.configData.allow_multiple_organization == 1
        && this.userData.masterEnabled == '0'
        && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
    || (this.crossTenantOptions.length > 1 
        && this.userData.isMaster =='1' 
        && this.userData.masterEnabled == '1')
    || (this.crossTenantOptions.length > 1 
        && this.userData.isMaster =='0' 
        && this.userData.masterEnabled == '1'
        && this.userData.group !='3')
    || (this.crossTenantOptions.length > 1
        && this.privileges.indexOf('allowOrganizationSwitching') != -1
        && this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication') !== -1 
        && this.configData.allow_multiple_organization == 1
        && this.userData.masterEnabled == '0'
        && this.configData.enable_nursing_agencies_visibility_restrictions == 1
        && this.userData.nursing_agencies == "")
    ) {
       if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1') {
            this.internalSiteId = this.userData.master_details.id;
        }
        this.chatWithLoader.otherTenantstaff = true;
        this.chatWithTenantFilter.tenants = this.chatWithTenantFilter.tenants.filter((tenant)=> {
            if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1'){
                if(this.internalSiteId == tenant.id) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return true;
            }
        });
        if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1') {
            if(this.userData.master_details.id != this.userData.tenantId) {
                this.crossTenantOptions.map((tenant)=> {
                    if(tenant.id == this.userData.tenantId) {
                        this.chatWithTenantFilter.tenants.unshift(tenant);
                    }
                });
            }
        }
        
    } else { 
        this.crossTenantOptionsChatWith = [];
        this.chatWithTenantFilter.tenants = [];
        
    }
  }
} else {
  this.column3 = false;
  this.crossTenatsForBranch = this.crossTenantOptions = [];
}


//**************************************  Ends **************************************************************** */


    var page = "patient-discussion-groups";
    $(".clear-btn-img").tooltip({
      title: this._ToolTipService.getToolTip(page, "PTDGP00004")
    });
    $(".toolSelect").tooltip({
      title: this._ToolTipService.getToolTip(page, "PTDGP00005")
    });


    this.getAssignedUsersList(
      this.selectedMaterialId,
      "",
      this.othertenantSelected,
      this.isPatientUser
    );
    //this.getAllPatients();
    //this.getAllUserData();
    /**
     * Get User tags with tag type (Nursing Agency) BioMatrix -  Nursing tags.
     */
    if (
      this.userData.config.enable_nursing_agencies_visibility_restrictions == 1
    ) {
      this.getNursingAgencyTags();
      this.isNursingAgencyEnabled = this.userData.config.enable_nursing_agencies_visibility_restrictions;
    }
    this.dataLoadingMsg = false;
    setTimeout( ()=> {
      if($("#messageTenantFilter").length) {
            $('#messageTenantFilter').select2({
             dropdownParent: $("#staffListblock")
           });
            $("#messageTenantFilter").css("text-overflow", "ellipsis");
      } 
  });

    $("body").on("change", "#messageTenantFilter", event => {

      // if (this.optionShow == "roles") {
      //   $("#userSearchTxtRoles").val("");
      //   $("#notFoundRoles").hide();
      // }

      if (event.target.value) {
        var previousSelectedTenant = this.chatWithTenantFilter.tenants.find(
          tenant => tenant.id == this.chatWithTenantFilter.selectedTenant
        );

        var currentSelectedTenant = this.chatWithTenantFilter.tenants.find(
          tenant => tenant.id == this.chatWithTenantFilter.selectedTenant
        );
       

        var activityData = {
          activityName:
            "Education Material " +
            (this.optionShow == "staffs"
              ? "Staff "
              : this.optionShow == "patient"
                ? "Patient "
                : " ") +
            "Tenant Switching",
          activityType: "messaging",
          activityDescription:
            this.userData.displayName +
            " (" +
            this.userData.userId +
            ") has selected tenant " +
            currentSelectedTenant.tenantName +
            "(" +
            currentSelectedTenant.id +
            ")" +
            (previousSelectedTenant
              ? " from tenant " +
              previousSelectedTenant.tenantName +
              "(" +
              previousSelectedTenant.id +
              ")"
              : ""),
          tenantId:
            this._structureService.getCookie("crossTenantId") &&
              this._structureService.getCookie("crossTenantId") != "undefined" &&
              this._structureService.getCookie("tenantId") !==
              this._structureService.getCookie("crossTenantId")
              ? this._structureService.getCookie("crossTenantId")
              : this.userData.tenantId
        };
        this._structureService.trackActivity(activityData);
        this.filterBranch(event);
      }
    });


    
  }

  getAssignedUsersList(
    materialId,
    searchText,
    othertenantSelected,
    isPatientUser
  ) {
    this.selected["memberDetails"] = [];
    this._structureService
      .getAssignedUsersList(
        materialId,
        searchText,
        othertenantSelected,
        this.isPatientUser
      )
      .then(resultData => {

        this.showData(this.optionShow);

        this.getAssignedUsersArrayTemp = [];
        let assignedUserId = [];
        let assignedUserGroupId = [];
        this.getAssignedUsersArray =
          resultData["getSessionTenant"].assignedEducationMaterialUserList;
        console.log("ASSIGNEDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD");
        console.log(this.getAssignedUsersArray);
        console.log("ASSIGNEDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD");

        this.getAssignedUsersArray.forEach((data, key) => {
          if (data.status != 0) {
            var userRoleData = {
              displayName: data.userRoleName,
              id: data.userRoleId,
              tenantId: data.tenantId,
              tenantName: data.displayName,
              // siteId:data.siteId
            };

            this.newMember = {
              id: parseInt(data.id),
              displayName: data.firstName + " "+ data.lastName,
              dob: data.dateOfBirth,
              IdentityValue: data.patientIdentityValue,              
              caregiver_dob: data.careDateOfBirth,
              caregiver_displayname: data.careDisplayName,
              caregiverIdentityValue: data.caregiverIdentityValue,
              name: data.displayName,
              role: userRoleData,
              roleName: data.userRoleName,
              tenantId: parseInt(data.tenantId),
              tenantName: data.tenantName,
              isUserGroup: data.isUserGroup != "1" ? false : true,
              isPatient: data.userRoleName == "Patient" ? true : false,
              naTags: data.naTags ? data.naTags : "",
              naTagNames: data.naTagNames ? data.naTagNames : "",
              status: data.status,
              // siteId: data.siteId ? data.siteId : ""
            };
            if (data.isUserGroup != 1) {
              assignedUserId.push(data.id);
            } else {
              assignedUserGroupId.push(data.tenantId);
            }

            this.getAssignedUsersArrayTemp.push(this.newMember);
            this.getAssignedUsersArrayTempOnLoad.push(this.newMember);
          }
        });
        if (assignedUserId.length) {
          this.messageGroupMemberIds = assignedUserId.join(", ");
        }

        this.addedUserGroupId = assignedUserGroupId.join(", ");
        console.log("JOINNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNN");
        console.log(assignedUserGroupId);
        console.log(this.addedUserGroupId);
        console.log("JOINNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNN");

        if (this.getAssignedUsersArray.length > 0) {
          this.getAssignedUsersArray.forEach(element => {
            this.selected["memberDetails"].push(element);
          });
        }
        // Call the user tag detail function.
        //this.getTaggedUserData(this.searchKey);
      });
  }

  getEducationData(): void {
    this._structureService
      .getSingleEducationMaterialData(this.selectedMaterialId)
      .then(resultData => {
        this.materialData =
          resultData["getSessionTenant"].educationMaterialSingleData;

        if (
          this.userData.config
            .enable_nursing_agencies_visibility_restrictions == 1 &&
          this.userData.nursing_agencies &&
          this.userData.nursing_agencies != ""
        ) {
          if (this.materialData.createdBy != this.userData.userId) {
            this.canEdit = false;
          }
        }

        this.documentMaterialGroup.patchValue({
          materialName: this.materialData.materialName,
          description: this.materialData.description,
          nursingAgencyUserTag:
            this.materialData.educationNursingAgencyTags &&
              this.materialData.educationNursingAgencyTags.length > 0
              ? String(
                this.materialData.educationNursingAgencyTags[0].selectedTags
              )
              : ""
        });
        this.uploadedFileName = this.materialData.uploadedFileName;
        if (this.materialData.fileName !== null) {
          this.signDocFile.push(this.materialData.fileName);
        }
      });
  }

  ngOnDestroy(): void { }

  removeFile(index, fileName): void {
    this.signDocFile = []; // single file case.
    this.documentMaterialGroup.patchValue({
      fileup: ""
    });
    this.invalidFileType = false;
  }

  handleFileInput(files: FileList) {
    this.fileToUpload = files.item(0);
    this.signDocFile.push(this.fileToUpload.name);

    if (this.fileToUpload.type != "application/pdf") {
      this.invalidFileType = true;
      this.documentMaterialGroup.patchValue({
        fileup: ""
      });
    }
  }

  callAccordion(roleID, eve) {

console.log("DDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD" );
console.log(roleID);
console.log(eve);
console.log(this.newMemberListByRoleWiseTagUser[eve]["userList"].length);
console.log("DDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDDD" );


    if ($(".expand-icon-" + roleID).hasClass("fa-plus")) {
      if (
        this.optionShow == "taggedUsers" &&
        this.newMemberListByRoleWiseTagUser[eve]["userList"].length <= 1
      ) {
        this.getUsertagBasedStaff(roleID, eve, true);
      }

      $(".expand-icon-" + roleID).removeClass("fa-plus");
      $(".expand-icon-" + roleID).addClass("fa-minus");
      $(".sub-item-panel-" + roleID).addClass("showall");
    } else {
      $(".expand-icon-" + roleID).removeClass("fa-minus");
      $(".expand-icon-" + roleID).addClass("fa-plus");
      $(".sub-item-panel-" + roleID).removeClass("showall");
    }
  }

  getUsertagBasedStaff(roleId, eve, populate = false) {
    console.log(eve);
    let searchKey = "";
    var tenantIdData =
      "?searchKey=" +
      searchKey +
      "&usertag=" +
      roleId;
    this._structureService
      .getTaggedUserData(tenantIdData)
      .then(data => {
        this.loadingGroups = false;
        this.userTaggedUsersById = data;

this.newMemberListByRoleWiseTagUser[eve]["userList"] = [];

        this.userTaggedUsersById.forEach(element => {
          this.newMember = {
            id: "",
            displayName: "",
            caregiverName:"",
            role: {},
            tenantId: null,
            tenantName: "",
            isUserGroup: true
          };

          this.newMember.id = element.user_id;
          this.newMember.displayName = element.displayName;
          this.newMember.caregiverName = element.caregiver_displayname;
          this.newMember.role = "";
          this.newMember.tenantId = element.tagTenantId;
          this.newMember.tenantName = element.tenantName;
          this.newMemberList.push(this.newMember);
          this.memberList.push(this.newMember);
          var user = {
            id: this.newMember.id,
            name: this.newMember.displayName,
            caregiverName: this.newMember.caregiverName,
            tenantId: this.newMember.tenantId,
            tenantName: this.newMember.tenantName
          };

          var roleData = {
            tenantRoleId: element.tagId,
            tenantId: element.tagTenantId,
            tenantName: element.tagName
          };

          this.newMemberListByRoleWiseTagUser[eve]["userList"].push(user);
        });
      })
      .catch(error => {
        console.log(error);
        this.loadingGroups = false;
      });
  }

  noWhitespaceValidator(control: FormControl) {
    let isWhitespace = (control.value || "").trim().length === 0;
    let isValid = !isWhitespace;
    return isValid ? null : { whitespace: true };
  }

  updateTrainingMaterial(): any {
    this.formSubmitted = true;
    this.dataLoadingMsg = true;
    this.fileName = this.fileToUpload
      ? this.fileToUpload.name
      : this.materialData.fileName;
console.log("ssssssssssssssss",this.getAssignedUsersArrayTemp);
    let shareUserData = this.getAssignedUsersArrayTemp.map(data => {
      if (data.role) delete data.role;
      if (data.hasOwnProperty("naTags")) delete data.naTags;
      if (data.hasOwnProperty("naTagNames")) delete data.naTagNames;
      if (data.hasOwnProperty("status")) delete data.status;
      if (data.hasOwnProperty("IdentityValue")) delete data.IdentityValue;
      if (data.hasOwnProperty("caregiverIdentityValue")) delete data.caregiverIdentityValue;
      if (data.hasOwnProperty("caregiver_displayname")) delete data.caregiver_displayname;
      if (data.hasOwnProperty("caregiver_dob")) delete data.caregiver_dob;
      if (data.hasOwnProperty("caregiver_userid")) delete data.caregiver_userid;
      if (data.hasOwnProperty("dob")) delete data.dob;

      return data;
    });

    console.log("MMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMM");
    console.log(shareUserData);
    console.log("MMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMM");

    let params = {
      materialId: this.materialData.id,
      materialName: this.documentMaterialGroup.value.materialName,
      description: this.documentMaterialGroup.value.description,
      tenantId: this.selectedTenant.id,
      createdBy: this.userData.userId,
      updatedBy: this.userData.userId,
      fileName: this.fileName,
      uploadedFileName: "",
      nursingAgencyUserTags: this.documentMaterialGroup.value
        .nursingAgencyUserTag,
      shareUserData: shareUserData
    };

    this._structureService.updateEducationMaterial(params).then(response => {
      if (response["updateEducationMaterial"]) {
        if (this.documentMaterialGroup.value.fileup != "") {
          const uploadData = new FormData();
          uploadData.append(
            "myFile",
            this.fileToUpload,
            this.fileToUpload.name
          );
          var fileUploadApiUrl =
            this._structureService.apiBaseUrl +
            "citus-health/" +
            this._structureService.version +
            "/upload-file-education.php?fileId=" +
            this.materialData.id +
            "&tId=" +
            this.selectedTenant.id;
          NProgress.start();
          let headers = new Headers();
          headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
          let options = new RequestOptions({ headers: headers });
          this._http.post(fileUploadApiUrl, uploadData, options).subscribe(
            response => {
              let sharedUserString = "";
              if (shareUserData.length > 0) {
                sharedUserString += "and assigned user(s) with id ";
                shareUserData.forEach(userData => {
                  sharedUserString += " " + userData.id;
                });
              }

              var activityLogMessage =
                this.userData.displayName +
                " modified Education Material with id " +
                this.materialData.id +
                " " +
                sharedUserString;
              var activityData = {
                activityName: "Modify Education Material",
                activityType: "manage education material",
                activityDescription: activityLogMessage
              };
              console.log('Activity Data =>>',activityData)
              this._structureService.trackActivity(activityData);
              this.dataLoadingMsg = false;
              this.formSubmitted = false;
              NProgress.done();
              var notify = $.notify("Success! Education Material updated");
              setTimeout(function () {
                notify.update({
                  type: "success",
                  message:
                    "<strong>Success! Education Material updated</strong>"
                });
              }, 1000);
              this.router.navigate(["/education/education-material-list"]);
            },
            error => {
              NProgress.done();
              this.formSubmitted = true;
              this.dataLoadingMsg = false;
              var notify = $.notify(
                "Error! Something went wrong, please try again later"
              );
              setTimeout(function () {
                notify.update({
                  type: "danger",
                  message:
                    "<strong>Error! Something went wrong, please try again later</strong>"
                });
              }, 1000);
            }
          );
        } else {
          let sharedUserString = "";
          let patientArray =[], staffArray=[],tagArray=[];
          if (shareUserData.length > 0) {
            sharedUserString += "and assigned user(s) with id ";

            shareUserData.forEach(userData => {
              if(userData.isPatient){
                patientArray.push(userData.id);
              }
              else
              if(userData.isUserGroup) {
                tagArray.push(userData.id);
              }
              else {
                staffArray.push(userData.id);
              }
              sharedUserString += " " + userData.id;
            });
          }

          var activityLogMessage =
            this.userData.displayName +
            " modified Education Material with id " +
            this.materialData.id +
            " " +
            sharedUserString;
          var activityLogMsg = 
          this.userData.displayName +
          " modified Education Material with id " +
          this.materialData.id ;
          let maxLength=Math.max(staffArray.length,patientArray.length,tagArray.length);
          if (maxLength>0) {
            activityLogMsg +=" and assigned "; 
            let stafString="",patientString="",tagString="";
            for (let i = 0; i < maxLength; i++) {
              if(i==0){
                if(patientArray[i])
                 patientString += "patient(s) with id "+patientArray[i]+",";
                if(staffArray[i])
                 stafString +="staff(s) with id "+ staffArray[i]+",";
                if(tagArray[i])
                 tagString +="user tag(s) with id "+ tagArray[i]+",";
              }
              else {
                if(patientArray[i])
                 patientString += patientArray[i]+ "," ;
                if(staffArray[i])
                 stafString += staffArray[i]+ "," ;
                if(tagArray[i])
                 tagString += tagArray[i]+ "," ;
              }
            }
            activityLogMsg +=patientString+stafString+tagString;


          }
          var activityData = {
            activityName: "Modify Education Material",
            activityType: "manage education material",
            activityDescription: activityLogMsg
          };
          console.log('Activity Data =>>',activityData)
          console.log('Shared user data ==>',shareUserData)
          console.log('Old Log messgae ==>',activityLogMessage)
          this._structureService.trackActivity(activityData);
          this.dataLoadingMsg = false;
          this.formSubmitted = false;
          NProgress.done();
          var notify = $.notify("Success! Education Material updated");
          setTimeout(function () {
            notify.update({
              type: "success",
              message: "<strong>Success! Education Material updated</strong>"
            });
          }, 1000);
          this.router.navigate(["/education/education-material-list"]);
        }
      }
    });
  }

  revokeShare(userData): void {

    this.getAssignedUsersArrayTemp = this.getAssignedUsersArrayTemp.filter(
      obj => {
        return obj.id != userData.id.toString();
      }
    );
    this.getAssignedUsersArray = this.getAssignedUsersArray.filter(
      obj => obj.id != userData.id.toString()
    );

    if (userData.isUserGroup) {
      this.newMember = {
        id: "",
        displayName: "",
        role: {},
        tenantId: null,
        tenantName: "",
        isUserGroup: true
      };

      this.newMember.id = String(userData.id);
      this.newMember.displayName = userData.displayName;
      this.newMember.role = "";
      this.newMember.tenantId = String(userData.tenantId);
      this.newMember.tenantName = userData.tenantName;
      this.newMemberList.push(this.newMember);
      this.memberList.push(this.newMember);
      var user = {
        id: String(this.newMember.id),
        name: String(this.newMember.tenantId),
        tenantId: String(this.newMember.tenantId),
        tenantName: this.newMember.tenantName
      };

      var roleData = {
        tenantRoleId: String(userData.tenantId),
        tenantId: 1,
        tenantName: userData.displayName
      };

      if (!this.newMemberListByRoleWiseTagUser[userData.tenantId]) {
        this.newMemberListByRoleWiseTagUser[userData.tenantId] = {};
      }

      if (
        !("userList" in this.newMemberListByRoleWiseTagUser[userData.tenantId])
      ) {
        this.newMemberListByRoleWiseTagUser[userData.tenantId]["userList"] = [];
      }

      if (
        !("roleData" in this.newMemberListByRoleWiseTagUser[userData.tenantId])
      ) {
        this.newMemberListByRoleWiseTagUser[userData.tenantId]["roleData"] = {};
      }

      if (
        !("tenantId" in this.newMemberListByRoleWiseTagUser[userData.tenantId])
      ) {
        this.newMemberListByRoleWiseTagUser[userData.tenantId][
          "tenantId"
        ] = null;
      }

      this.newMemberListByRoleWiseTagUser[userData.tenantId][
        "roleData"
      ] = roleData;
      this.newMemberListByRoleWiseTagUser[userData.tenantId]["userList"].push(
        user
      );
      this.newMemberListByRoleWiseTagUser[userData.tenantId]["tenantId"] =
        userData.tenantId;
      this.newMemberListByRoleWiseTagUser[userData.tenantId]["tenantName"] =
        userData.displayName;

        this.newMemberListByRoleWiseTagUser = this.newMemberListByRoleWiseTagUser.filter(
          item => {
            return item;
          }
        );

        this.newMemberListByRoleWiseTagUser.sort(function (a, b) {
          if (a.roleData.tenantName.toUpperCase() < b.roleData.tenantName.toUpperCase()) return -1;
          if (a.roleData.tenantName.toUpperCase() > b.roleData.tenantName.toUpperCase()) return 1;
          return 0;
        });



      if (userData.isUserGroup) {
        let assignedUserGroupId = [];
        assignedUserGroupId = this.addedUserGroupId.split(",");
        assignedUserGroupId = assignedUserGroupId.filter(
          obj => obj.trim() != userData.tenantId
        );
        console.log("assignedUserGroupId AFTERRRR" + assignedUserGroupId);
        this.addedUserGroupId = assignedUserGroupId.join(", ");
      }
    } else if (!userData.isPatient) {
      console.log(
        "22222222222222222222222222222222222222222222222222222222222222222"
      );

      this.newMember = {
        id: "",
        displayName: "",
        role: {},
        tenantId: null,
        tenantName: "",
        otherTenantStaff:
          this.userData.tenantId != this.selectedTenant.id ? true : false,
        naTags: "",
        naTagNames: "",
        IdentityValue: "",
        dob:"",
        caregiverIdentityValue:"",
        caregiver_dob:"",
        caregiver_displayname:""
      };

      this.newMember.id = userData.id.toString();
      this.newMember.displayName = userData.displayName;
      this.newMember.role = userData.role;
      this.newMember.tenantId = userData.role.tenantId;
      this.newMember.tenantName = userData.role.tenantName;
      // this.newMember.siteId = (userData && userData.siteId) ? userData.siteId : "";
      if (userData.IdentityValue ) {
        this.newMember.IdentityValue = userData.IdentityValue;
      }
      if (userData.dob ) {

        this.newMember.dob = userData.dob;
      }
      if (userData.caregiverIdentityValue ) {
        this.newMember.caregiverIdentityValue = userData.caregiverIdentityValue;
      }
      if (userData.caregiver_dob ) {

        this.newMember.caregiver_dob = userData.caregiver_dob;
      }
     
      if (userData.caregiver_displayname ) {

        this.newMember.caregiver_displayname = userData.caregiver_displayname;
      }

      if (userData.naTags) {
        this.newMember.naTags = userData.naTags;
        this.newMember.naTagNames = userData.naTagNames;
      }

      this.newMemberList.push(this.newMember);

      let user = {
        id: this.newMember.id,
        userId: this.newMember.id,
        userid: this.newMember.id,
        name: this.newMember.displayName,
        displayname: this.newMember.displayName,
        role: this.newMember.role,
        tenantId: userData.role.tenantId,
        tenantid: userData.role.tenantId,
        tenantName: this.newMember.tenantName,
        tenantRoleId: this.newMember.role.id,
        naTags: "",
        naTagNames: "",
        IdentityValue: this.newMember.IdentityValue,
        dob: this.newMember.dob,
        caregiver_dob: this.newMember.caregiver_dob,
        caregiverIdentityValue: this.newMember.caregiverIdentityValue,
        caregiver_displayname: this.newMember.caregiver_displayname,
        // siteId: this.newMember.siteId
      };

      if (userData.naTags) {
        user.naTags = userData.naTags;
        user.naTagNames = userData.naTagNames;
      }

      let roleData = {
        id: this.newMember.role.id,
        name: this.newMember.role.displayName,
        tenantRoleId: this.newMember.role.id
      };

      if (((this.chatWithTenantFilter.selectedTenant == user.tenantId && !this.multiSiteEnable) || this.multiSiteEnable) && this.optionShow == "staffs") {
        this.userListChatwith.push(user);
        this.userListChatwith.sort(function (a, b) {
          if (a.displayname < b.displayname) return -1;
          if (a.displayname > b.displayname) return 1;
          return 0;
        });
      }

      if (!userData.isUserGroup) {
        let assignedUserId = [];
        assignedUserId = this.messageGroupMemberIds.split(",");
        assignedUserId = assignedUserId.filter(
          obj => obj.trim() != userData.id.toString().trim()
        );
        this.messageGroupMemberIds = assignedUserId.join(", ");
      }
    } else {
      console.log("333333333333333333333333333333333333333333333333333");
      this.getAssignedUsersArray = this.getAssignedUsersArray.filter(
        obj => obj.id !== userData.id.toString()
      );

      this.newMember = {
        id: "",
        displayName: "",
        role: {},
        tenantId: null,
        tenantName: "",
        otherTenantStaff:
          this.userData.tenantId != this.selectedTenant.id ? true : false,
        naTags: "",
        naTagNames: "",
        IdentityValue: "",
        dob:"",
        caregiverIdentityValue:"",
        caregiver_dob:"",
        caregiver_userid:"",
        caregiver_displayname:""

      };
      this.newMember.id = userData.id.toString();
      this.newMember.displayName = userData.displayName;
      this.newMember.role = userData.role;
      this.newMember.tenantId = userData.role.tenantId;
      this.newMember.tenantName = userData.role.tenantName;

      if (userData.naTags) {
        this.newMember.naTags = userData.naTags;
        this.newMember.naTagNames = userData.naTagNames;
      }
      if (userData.IdentityValue ) {
        this.newMember.IdentityValue = userData.IdentityValue;
      }
      if (userData.dob ) {

        this.newMember.dob = userData.dob;
      }
      if (userData.caregiverIdentityValue ) {
        this.newMember.caregiverIdentityValue = userData.caregiverIdentityValue;
      }
      if (userData.caregiver_dob ) {

        this.newMember.caregiver_dob = userData.caregiver_dob;
      }
      if (userData.caregiver_userid ) {
        this.newMember.caregiver_userid = userData.caregiver_userid;
      }
      if (userData.caregiver_displayname ) {

        this.newMember.caregiver_displayname = userData.caregiver_displayname;
      }

      this.newMemberList.push(this.newMember);

      let user = {
        c_grp: null,
        caregiver_displayname: this.newMember.caregiver_displayname,
        caregiver_userid: this.newMember.caregiver_userid,
        id: this.newMember.id,
        name: this.newMember.displayName,
        displayname: this.newMember.displayName,
        roleId: this.newMember.role.id,
        //role: 'Patient',
        tenantId: userData.role.tenantId,
        //tenantName: this.newMember.tenantName,
        userId: this.newMember.id,
        userid: this.newMember.id,
        naTags: "",
        naTagNames: "",
        IdentityValue: this.newMember.IdentityValue,
        dob: this.newMember.dob,
        caregiver_dob: this.newMember.caregiver_dob,
        caregiverIdentityValue: this.newMember.caregiverIdentityValue
      };
      let roleData = {
        tenantId: userData.role.tenantId,
        name: this.newMember.tenantName,
        tenantRoleId: this.newMember.role.id
      };
      if (((this.chatWithTenantFilter.selectedTenant == user.tenantId && !this.multiSiteEnable) || this.multiSiteEnable) && this.optionShow == "patient")  {
        this.userListChatwith.push(user);
        this.userListChatwith.sort(function (a, b) {
          if (a.displayname < b.displayname) return -1;
          if (a.displayname > b.displayname) return 1;
          return 0;
        });
      }

      if (!userData.isUserGroup) {
        let assignedUserId = [];
        assignedUserId = this.messageGroupMemberIds.split(",");
        assignedUserId = assignedUserId.filter(
          obj => obj.trim() != userData.id.toString().trim()
        );
        this.messageGroupMemberIds = assignedUserId.join(", ");
      }
    }
  }

  showData(data) {
    this.optionShow = data;
    this.searchText = this.searchTexts = "";
    this.srch.nativeElement.value = "";
    //this.getAssignedUsersArrayTemp = [];
    // let mySites = (this.siteId && this.siteId.siteId && this.siteId.siteId.length && this.siteId.siteId.length > 0) ? this.siteId.siteId.join() : this.siteId.siteId;
    console.log("this.siteId",this.siteId);
    if (this.optionShow == "staffs") {
      this.chatWithTenantFilter.enabledReset = true;
       //this.setChatWithTenantFilterSelect();
      this.initialiseStaffOrPatientList(this.optionShow,this.siteId);
    }
    if (this.optionShow == "patient") {
      this.chatWithTenantFilter.enabledReset = true;
       //this.setChatWithTenantFilterSelect();
      this.chatWithLoader.patients = true;
      this.initialiseStaffOrPatientList(this.optionShow,this.siteId);
    }

    if (this.optionShow == "taggedUsers") {
      this.chatWithTenantFilter.enabledReset = true;
     // this.setChatWithTenantFilterSelect();
      this.chatWithLoader.usertag = true;
      this.getTaggedUserData("",this.siteId);
    }
    // this.isPatientUser = false;
  }
 
 
  // setChatWithTenantFilterSelect() {
	// 	if(this.selectedTenant) {
	// 		this.chatWithTenantFilter.selectedTenant =  this.selectedTenant.id;
	// 	} else {
	// 		this.chatWithTenantFilter.selectedTenant = this._structureService.getCookie('tenantId');
	// 	}
	// 	switch(this.optionShow) {
	// 		case 'staffs': {
	// 			if((this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
	// 			&& this.configData.allow_multiple_organization==1 
	// 			&& this.userData.crossTenantsDetails.length > 1 
	// 			&& this.userData.masterEnabled == '0'
	// 			&& this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
	// 		|| (this.userData.masterEnabled == '1' 
	// 			&& this.userData.isMaster == '1')
	// 		|| (this.userData.masterEnabled == '1' 
	// 			&& this.userData.isMaster == '0'
	// 			&& this.userData.group !='3')
	// 		|| (this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
	// 			&& this.configData.allow_multiple_organization==1 
	// 			&& this.userData.crossTenantsDetails.length > 1 
	// 			&& this.userData.masterEnabled == '0'
	// 			&& this.configData.enable_nursing_agencies_visibility_restrictions == '1'
	// 			&& this.userData.nursing_agencies == '')) {
	// 			   this.assignChatWithTenantFilterData(); 
	// 			} else {
	// 				this.chatWithTenantFilter.filterEnabled = false;
	// 				this.chatWithTenantFilter.tenants = [];
	// 			}
	// 			break;
	// 		} 
	// 		case 'patient': {
	// 			if((this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
	// 			&& this.configData.allow_multiple_organization==1 
	// 			&& this.userData.crossTenantsDetails.length > 1 
	// 			&& this.userData.masterEnabled == '0'
	// 			&& this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
	// 		|| (this.userData.masterEnabled == '1' 
	// 			&& this.userData.isMaster == '1')
	// 		|| (this.userData.masterEnabled == '1' 
	// 			&& this.userData.isMaster == '0'
	// 			&& this.userData.group !='3')
	// 		|| (this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
	// 			&& this.configData.allow_multiple_organization==1 
	// 			&& this.userData.crossTenantsDetails.length > 1 
	// 			&& this.userData.masterEnabled == '0'
	// 			&& this.configData.enable_nursing_agencies_visibility_restrictions == '1'
	// 			&& this.userData.nursing_agencies == '')) {
	// 			   this.assignChatWithTenantFilterData(); 
	// 			} else {
	// 				this.chatWithTenantFilter.filterEnabled = false;
	// 				this.chatWithTenantFilter.tenants = [];
	// 			}
	// 			break;
	// 		}      
	// 		default: {
	// 			this.chatWithTenantFilter.filterEnabled = false;
	// 			this.chatWithTenantFilter.tenants = [];
	// 			break;
	// 		} 
	// 	}
	// }
  // assignChatWithTenantFilterData() {
	// 	this.chatWithTenantFilter.filterEnabled = true;
	// 	this.chatWithTenantFilter.enabledReset = true;
	// 	if($("#messageTenantFilter").length) {
	// 		setTimeout( ()=> {
	// 			$('#messageTenantFilter').select2({
	// 				dropdownParent: $("#staffListblock")
	// 			});
	// 			$("#messageTenantFilter").css("text-overflow", "ellipsis");
	// 			if(this.optionShow == 'roles') {
	// 			$("#messageTenantFilter").val(this.chatWithTenantFilter.selectedTenant).trigger("change");
	// 			}
	// 		});
	// 	} else {
	// 		this.chatWithTenantFilter.setTenantDropDown = true;
	// 	}
	// }
  initialiseStaffOrPatientList(optionShow,mySites="") {
    if (
      this.chatWithTenantFilter.setTenantDropDown &&
      this.chatWithTenantFilter.selectedTenant
    ) {
      this.chatWithTenantFilter.setTenantDropDown = false;
      setTimeout(() => {
        $("#messageTenantFilter").select2({
          dropdownParent: $("#staffListblock")
        });
        $("#messageTenantFilter")
          .val(this.chatWithTenantFilter.selectedTenant)
          .trigger("change");
        $("#messageTenantFilter").css("text-overflow", "ellipsis");
      });
      var tenantFilter =true;      
    }
    console.log("initialiseStaffOrPatientList.optionShow", optionShow);

    this.loadMoreSearchValue = undefined;
    this.callFromInitialCountChatWithUserlist = true;
    this.chatwithPageCount = {
      staffs: 0,
      patients: 0,
      usertag: 0
    };
    this.noMoreItemsAvailable = {
      users: false
    };
    this.userListChatwith = [];
    this.clinicalUserDetails = [];
    this.usersList = [];
    var isRoleAvailable = true;
    var clinicianRolesAvaiable = null;
    var setCliniciansRoleAvailableResponse;

    if (isRoleAvailable  && !tenantFilter ) {
      this.getUsersListByRoleIdAvailableOnClick(
        clinicianRolesAvaiable,
        true,
        false,
        optionShow,
        undefined,
        mySites
      );
    } else {
      /* if (isRoleAvailable) {
        this.getUsersListByRoleIdAvailableOnClick(
          clinicianRolesAvaiable,
          true,
          false,
          optionShow,
          undefined
        );
      } else { */
        this.chatWithUsersLoading = false;
      //}
    }

    /** ***********************End Section ****************** */
  }

  getUsersListByRoleIdAvailableOnClick(
    clinicianRolesAvaiable,
    init,
    loadMore,
    optionShow,
    searchValue,
    mySites=""
  ) {
    mySites=this.siteId;
    let chatWithFilterTenantId = null;

    if (init) {
      if (optionShow == "staffs") {
        this.chatWithLoader.staffs = true;
      }
    }
    if (optionShow == "staffs" || optionShow == "patient") {
      //this.chatWithLoader.patients = true;
      chatWithFilterTenantId = this.chatWithTenantFilter.selectedTenant;
      var memberIds = "";
      if (this.messageGroupMemberIds != "") {
        memberIds = this.messageGroupMemberIds;
      }
    }

    this.loadMoremessage.users = "Loading ....";
    this._inboxService
      .getUsersListByRoleId(
        clinicianRolesAvaiable ? clinicianRolesAvaiable : 3,
        0,
        null,
        null,
        init
          ? 0
          : optionShow == "staffs"
            ? this.chatwithPageCount.staffs
            : this.chatwithPageCount.patients,
        optionShow == "staffs" && !clinicianRolesAvaiable
          ? true
          : optionShow == "staffs" && clinicianRolesAvaiable
            ? undefined
            : false,
        searchValue,
        chatWithFilterTenantId,
        undefined,
        memberIds,
        "",
        mySites
      )
      .then((data: any) => {
        console.log("hiiiiiiiiiiiii");
        console.log("data", data);
        this.chatWithLoader.patients = false;
        this.chatWithUsersLoading = false;
        this.loadMoreSearchValue = searchValue;

        if (loadMore) {
          console.log(
            "clinicalUserDetails beforeeeeeeeeeeeeeee",
            this.clinicalUserDetails
          );
          console.log(
            "selectedGroupMembers beforeeeeeeeeeeeeeee",
            this.selectedGroupMembers
          );

          this.clinicalUserDetails = [...this.clinicalUserDetails, ...data];
        } else {
          this.clinicalUserDetails = data;
        }
        if (data.length != 20) this.noMoreItemsAvailable.users = true;

        if (optionShow == "staffs") {
          this.chatwithPageCount.staffs += 1;
        }
        if (optionShow == "patient") {
          this.chatwithPageCount.patients += 1;
        }
        console.log(this.clinicalUserDetails);
        for (var _i = 0; _i < this.clinicalUserDetails.length; _i++) {
          this.clinicalUserDetails[_i].isScheduled = 1;
          if (
            this._inboxService.scheduleSelectionFilter(
              this.clinicalUserDetails[_i]
            )
          ) {
            this.clinicalUserDetails[_i].isScheduled = 1;
          } else {
            this.clinicalUserDetails[_i].isScheduled = 0;
          }
        }

        this.loadMoremessage.users = "Load more";
        // this.newMemberListByRoleWise = this.clinicalUserDetails;
        this.userListChatwith = this.clinicalUserDetails;

        this.userListChatwith = this.userListChatwith.filter(
          x => !this.selectedGroupMembers.some(y => y.id == x.userid)
        );

        if (init) {
          if (optionShow == "staffs") {
            this.chatWithLoader.staffs = false;
          }
        }
      })
      .catch(ex => {
        if (init) {
          if (optionShow == "staffs") {
            this.chatWithLoader.staffs = false;
          }
        }
      });
  }

  checkboxChanged(event): void {
    var $this = $(event.target),
      checked = $this.prop("checked"),
      container = $this.parent(),
      siblings = container.siblings();

    container
      .find('input[type="checkbox"]')
      .prop({
        indeterminate: false,
        checked: checked
      })
      .siblings("label")
      .removeClass("custom-checked custom-unchecked custom-indeterminate")
      .addClass(checked ? "custom-checked" : "custom-unchecked");
    if (this.optionShow != "taggedUsers") {
      this.checkSiblings(container, checked);
    }
  }

  checkSiblings($el, checked) {
    var parent = $el.parent().parent(),
      all = true,
      indeterminate = false;

    $el.siblings().each(function () {
      return (all =
        $(this)
          .children('input[type="checkbox"]')
          .prop("checked") === checked);
    });

    if (all && checked) {
      parent
        .children('input[type="checkbox"]')
        .prop({
          indeterminate: false,
          checked: checked
        })
        .siblings("label")
        .removeClass("custom-checked custom-unchecked custom-indeterminate")
        .addClass(checked ? "custom-checked" : "custom-unchecked");

      this.checkSiblings(parent, checked);
    } else if (all && !checked) {
      indeterminate = parent.find('input[type="checkbox"]:checked').length > 0;

      parent
        .children('input[type="checkbox"]')
        .prop("checked", checked)
        .prop("indeterminate", indeterminate)
        .siblings("label")
        .removeClass("custom-checked custom-unchecked custom-indeterminate")
        .addClass(
          indeterminate
            ? "custom-indeterminate"
            : checked
              ? "custom-checked"
              : "custom-unchecked"
        );

      this.checkSiblings(parent, checked);
    } else {
      $el
        .parents("li")
        .children('input[type="checkbox"]')
        .prop({
          indeterminate: true,
          checked: false
        })
        .siblings("label")
        .removeClass("custom-checked custom-unchecked custom-indeterminate")
        .addClass("custom-indeterminate");
    }
  }

  addMember() {
    var self = this;
    this.addMemberError = false;
    $(":checkbox:checked").each(function (i) {
      var name = this.name;
      console.log("checked name", name);
      console.log(
        "checked name getAssignedUsersArray",
        self.getAssignedUsersArray
      );
      console.log("checked value", $(this).val());
      console.log("checked TABBBB", self.optionShow);
      if (name == "cliniciansRoleUser[]") {
        var index = self.getAssignedUsersArray.findIndex(
          x => x.id == $(this).val()
        );

        if (index == -1) {
          self.checkedIds.push($(this).val());
        }
      }

      if (self.optionShow == "taggedUsers") {
        if (name == "middle[]") {
          var index = self.getAssignedUsersArray.findIndex(
            x => x.id == $(this).val()
          );
          if (index === -1) {
            self.checkedIds.push($(this).val());
          }
        }
      }
    });

    console.log("checked IDDDDDDDDDDDDDDDDDDDDDDDD", this.checkedIds);

    if (this.checkedIds.length > 0) {
      //this.disableButton = false;
      this.addMemberToList(this.checkedIds);
      this.noMemberError = false;
      this.checkedIds = [];
      this.addMemberError = false;
    } else {
      // this.disableButton = true;
      this.addMemberError = true;
    }
  }

  addMemberToList(checkedIds) {
    var self = this;
    let assignedUserId = [];
    let assignedUserGroupId = [];
    //if(this.messageGroupMemberIds)
    assignedUserId = this.messageGroupMemberIds
      ? this.messageGroupMemberIds.split(",")
      : [];
    assignedUserGroupId = this.addedUserGroupId
      ? this.addedUserGroupId.split(",")
      : [];

    if (this.optionShow == "staffs") {
      this.userListChatwith.forEach(element => {
        if (this.checkedIds.includes(element.userId)) {
          this.newMember = {
            id: "",
            displayName: "",
            role: {},
            tenantId: null,
            tenantName: "",
            roleId: null,
            isPatient: false,
            otherTenantStaff: false,
            naTags: "",
            naTagNames: ""
          };
          this.newMember.id = parseInt(element.userId);
          this.newMember.displayName = element.displayname;
          this.newMember.role = element.role;
          this.newMember.tenantId = parseInt(element.tenantId);
          this.newMember.tenantName = element.tenantName;
          this.newMember.roleId = parseInt(element.roleId);
          if (
            element.naTags &&
            element.naTags != null &&
            element.naTags != "null"
          ) {
            this.newMember.naTags = element.naTags;
            this.newMember.naTagNames = element.naTagNames;
          }

          this.getAssignedUsersArrayTemp.push(this.newMember);

          assignedUserId.push(element.userId);
          this.messageGroupMemberIds = assignedUserId.join(", ");
          this.selectedGroupMembers.push(this.newMember);
          this.userListChatwith = this.userListChatwith.filter(function (
            members
          ) {
            return members.userid != element.userId;
          });
        }
      });
      if(this.userListChatwith.length ==0 ){
        this.userListChatwith = [];
          this.clinicalUserDetails = [];
          this.usersList = [];
          this.reset(this.optionShow)  
      }
    } else if (this.optionShow == "patient") {
      this.userListChatwith.forEach(element => {
        if (this.checkedIds.includes(element.userId)) {
          this.newMember = {
            id: "",
            displayName: "",
            role: {},
            tenantId: null,
            tenantName: "",
            roleId: null,
            isPatient: true,
            otherTenantStaff: false,
            naTags: "",
            naTagNames: "",
            IdentityValue: "",
            dob:"",
            caregiverIdentityValue:"",
            caregiver_dob:"",
            caregiver_userid:"",
            caregiver_displayname:"",
            // siteId:""

          };

          let roleData = {
            id: element.roleId,
            displayName: element.displayname,
            tenantName: element.tenantName,
            tenantId: element.tenantId
          };

          this.newMember.id = parseInt(element.userId);
          this.newMember.displayName = element.displayname;
          this.newMember.role = roleData;
          this.newMember.tenantId = parseInt(element.tenantId);
          this.newMember.tenantName = element.tenantName;
          this.newMember.roleId = parseInt(element.roleId);
          // this.newMember.siteId = element.siteId ? element.siteId : "";

          if (
            element.naTags &&
            element.naTags != null &&
            element.naTags != "null"
          ) {
            this.newMember.naTags = element.naTags;
            this.newMember.naTagNames = element.naTagNames;
          }
          if (
            element.IdentityValue &&
            element.IdentityValue != null &&
            element.IdentityValue != "null"
          ) {
            this.newMember.IdentityValue = element.IdentityValue;
          }
          if (
            element.dob &&
            element.dob != null &&
            element.dob != "null"
          ) {
            this.newMember.dob = element.dob;
          }
          if(element.caregiver_displayname && element.caregiver_displayname != null){
            this.newMember.caregiver_displayname = element.caregiver_displayname;
            this.newMember.caregiver_userid = element.caregiver_userid;

          }
          if (
            element.caregiverIdentityValue &&
            element.caregiverIdentityValue != null &&
            element.caregiverIdentityValue != "null"
          ) {
            this.newMember.caregiverIdentityValue = element.caregiverIdentityValue;
          }
          if (
            element.caregiver_dob &&
            element.caregiver_dob != null &&
            element.caregiver_dob != "null"
          ) {
            this.newMember.caregiver_dob = element.caregiver_dob;
          }

          this.getAssignedUsersArrayTemp.push(this.newMember);
          assignedUserId.push(element.userId);
          this.messageGroupMemberIds = assignedUserId.join(", ");

          this.userListChatwith = this.userListChatwith.filter(function (
            members
          ) {
            return members.userid != element.userId;
          });
        }
      });
    } else if (this.optionShow == "taggedUsers") {
      this.newMemberListByRoleWiseTagUser.forEach(tenantData => {
        this.checkedIds.forEach(checkedId => {
          if (checkedId.includes(tenantData.roleData.tenantRoleId)) {
            this.newMember = {
              id: "",
              displayName: "",
              //role: {},
              tenantId: null,
              tenantName: "",
              roleId: null,
              isPatient: false,
              isUserGroup: true
              //otherTenantStaff: true
            };
            this.newMember.id = parseInt(tenantData.roleData.tenantRoleId);
            this.newMember.displayName = tenantData.roleData.tenantName;
            //this.newMember.role = element.role;
            this.newMember.tenantId = parseInt(tenantData.roleData.tenantRoleId);
            this.newMember.tenantName = tenantData.tenantName;
            this.newMember.roleId = null;
            this.getAssignedUsersArrayTemp.push(this.newMember);

            assignedUserGroupId.push(tenantData.roleData.tenantRoleId);
            this.addedUserGroupId = assignedUserGroupId.join(", ");
            console.log("JOINNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNN");
            console.log(assignedUserGroupId);
            console.log(this.addedUserGroupId);
            console.log("JOINNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNNN");
          }
          this.newMemberListByRoleWiseTagUser = this.newMemberListByRoleWiseTagUser.filter(
            item => {
              return item.roleData.tenantRoleId != checkedId;
            }
          );
        });
      });
    }

    this.getAssignedUsersArrayTemp = this.getAssignedUsersArrayTemp.filter(
      (value, index, array) =>
        !array.filter(
          (v, i) => JSON.stringify(value) == JSON.stringify(v) && i < index
        ).length
    );
  }

  loadMoreUerTags() {
    this.chatwithPageCount.usertag += 1;
    console.log(this.chatwithPageCount.usertag);
    this.loadMoremessage.users = "Loading ....";
    this.getTaggedUserData(this.srch.nativeElement.value.trim(),this.siteId);
  }

  getTaggedUserData(searchKey,siteIds="") {
    if (this.chatwithPageCount.usertag <= 0) {
      this.loadingGroups = true;
      this.newMemberListByRoleWiseTagUser = [];
    }

    let offset = 0;
    offset = this.limit * this.chatwithPageCount.usertag;

    var tenantIdData =
      "?searchKey=" +
      searchKey +
      "&offset=" +
      offset +
      "&limit=" +
      this.limit +
      "&excludetags=" +
      this.addedUserGroupId;
    this._structureService
      .getTaggedUserData(tenantIdData,siteIds)
      .then(data => {

        this.loadingGroups = false;
        this.userTaggedUsers = data["taggedUsers"];
        this.userTaggedUsersCount = data["totalCount"];
        this.loadMoremessage.users = "Load more";

        if (
          this.userTaggedUsersCount >
          (this.chatwithPageCount.usertag + 1) * this.limit
        ) {
          this.noMoreItemsAvailableUserags = true;
        } else {
          this.noMoreItemsAvailableUserags = false;
        }


        if (this.userTaggedUsersCount > 0) {
          this.userTaggedUsers.forEach(element => {
            const index = this.getAssignedUsersArrayTemp.findIndex(
              val => val.tenantId == element.tagId
            );
            if (index == -1) {
              this.newMember = {
                id: "",
                displayName: "",
                role: {},
                tenantId: null,
                tenantName: "",
                isUserGroup: true
              };

              this.newMember.id = element.user_id;
              this.newMember.displayName = element.displayName;
              this.newMember.role = "";
              this.newMember.tenantId = element.tagTenantId;
              this.newMember.tenantName = element.tenantName;
              this.newMemberList.push(this.newMember);
              this.memberList.push(this.newMember);
              var user = {
                id: this.newMember.id,
                name: this.newMember.displayName,
                tenantId: this.newMember.tenantId,
                tenantName: this.newMember.tenantName
              };

              var roleData = {
                tenantRoleId: element.tagId,
                tenantId: element.tagTenantId,
                tenantName: element.tagName
              };

              if (!this.newMemberListByRoleWiseTagUser[element.tagId]) {
                this.newMemberListByRoleWiseTagUser[element.tagId] = {};
              }

              if (
                !(
                  "userList" in this.newMemberListByRoleWiseTagUser[element.tagId]
                )
              ) {
                this.newMemberListByRoleWiseTagUser[element.tagId][
                  "userList"
                ] = [];
              }

              if (
                !(
                  "roleData" in this.newMemberListByRoleWiseTagUser[element.tagId]
                )
              ) {
                this.newMemberListByRoleWiseTagUser[element.tagId][
                  "roleData"
                ] = {};
              }

              if (
                !(
                  "tenantId" in this.newMemberListByRoleWiseTagUser[element.tagId]
                )
              ) {
                this.newMemberListByRoleWiseTagUser[element.tagId][
                  "tenantId"
                ] = null;
              }

              this.newMemberListByRoleWiseTagUser[element.tagId][
                "roleData"
              ] = roleData;
              // this.newMemberListByRoleWiseTagUser[element.tagId]["userList"].push(
              //   user
              // );
              this.newMemberListByRoleWiseTagUser[element.tagId]["tenantId"] =
                element.tagTenantId;
              this.newMemberListByRoleWiseTagUser[element.tagId]["tenantName"] =
                element.tenantName;
            }
          });


          this.newMemberListByRoleWiseTagUser = this.newMemberListByRoleWiseTagUser.filter(
            item => {
              return item;
            }
          );

          this.newMemberListByRoleWiseTagUser.sort(function (a, b) {
            if (a.roleData.tenantName.toUpperCase() < b.roleData.tenantName.toUpperCase()) return -1;
            if (a.roleData.tenantName.toUpperCase() > b.roleData.tenantName.toUpperCase()) return 1;
            return 0;
          });

        } else {
          this.chatWithLoader.usertag = false;
        }
        //Remove the duplicate values from the array 
        const uniqueArray = this.newMemberListByRoleWiseTagUser.filter((item, index, self) =>
          index === self.findIndex((t) => t.roleData.tenantRoleId === item.roleData.tenantRoleId && t.roleData.tenantName === item.roleData.tenantName)
        );
        this.newMemberListByRoleWiseTagUser = uniqueArray;
      })
      .catch(error => {
        console.log(error);
      });
  }

  getNursingAgencyTags() {
    const userData: any = this._structureService.getUserdata();
    const tagGetData =
      "?userId=" + userData.userId + "&tenantId=" + userData.tenantId;
    const tagTypes = ["2"]; // Message Tag =1, User Tag =2 , Document Tag =3
    this._structureService
      .getNursingAgencyTagsByGroup(tagGetData, tagTypes)
      .then((data: any) => {
        this.nursingAgencyTags = data;
      });
  }

  initGroups() {
    this.offset = 0;
    this.allMessageGroups = [];
    this.memberDataGroupWise = [];
    this.hideLoadMore = false;
    this.prevText = "";
  }

  search(optionShow = "groups") {
    if (optionShow != "staffs" && optionShow != "patient") { 
      this.chatWithLoader.usertag = true;
      this.initGroups();
      if (this.srch.nativeElement.value.trim()) {
        this.chatwithPageCount = {
          staffs: 0,
          patients: 0,
          usertag: 0
        };
        this.getTaggedUserData(this.srch.nativeElement.value.trim(),this.siteId);
      }
    } else if (optionShow == "staffs" || optionShow == "patient") {
      this.chatWithLoader.patients = true;
      this.callFromInitialCountChatWithUserlist = true;
      this.chatwithPageCount = {
        staffs: 0,
        patients: 0,
        usertag: 0
      };
      this.noMoreItemsAvailable = {
        users: false
      };
      this.userListChatwith = [];
      this.clinicalUserDetails = [];
      this.usersList = [];
      this.loadMoreUsers(optionShow, this.srch.nativeElement.value.trim());
    }
  }

  reset(optionShow = "taggedUsers") {
    this.srch.nativeElement.value = "";
    if (optionShow == "taggedUsers") {
      this.chatWithLoader.usertag = true;
      this.initGroups();
      this.searchTexts = "";
      this.getTaggedUserData(this.srch.nativeElement.value.trim());
    } else {
      this.chatWithLoader.patients = true;
      this.loadMoreSearchValue = undefined;
      this.callFromInitialCountChatWithUserlist = true;
      this.chatwithPageCount = {
        staffs: 0,
        patients: 0,
        usertag: 0
      };
      this.noMoreItemsAvailable = {
        users: false
      };
      this.userListChatwith = [];
      this.clinicalUserDetails = [];
      this.usersList = [];
      this.loadMoreUsers(optionShow);
    }
  }

  loadMoreUsers(optionShow, searchKeyword = undefined, notInit = false) {
    if (!this.loadMoreSearchValue) {
      if (this.srch.nativeElement.value.trim()) {
        this.loadMoreSearchValue = undefined;
        this.callFromInitialCountChatWithUserlist = true;
        this.chatwithPageCount = {
          staffs: 0,
          patients: 0,
          usertag: 0
        };
        this.noMoreItemsAvailable = {
          users: false
        };
        notInit = false;
        this.userListChatwith = [];
        this.clinicalUserDetails = [];
        this.usersList = [];
        searchKeyword = this.srch.nativeElement.value.trim();
      }
    } else if (
      this.loadMoreSearchValue &&
      !this.srch.nativeElement.value.trim()
    ) {
      this.reset(optionShow);
      return false;
    } else if (
      this.loadMoreSearchValue == this.srch.nativeElement.value.trim()
    ) {
    } else {
      this.loadMoreSearchValue = undefined;
      this.callFromInitialCountChatWithUserlist = true;
      this.chatwithPageCount = {
        staffs: 0,
        patients: 0,
        usertag: 0
      };
      this.noMoreItemsAvailable = {
        users: false
      };
      notInit = false;
      this.userListChatwith = [];
      this.clinicalUserDetails = [];
      this.usersList = [];
      searchKeyword = this.srch.nativeElement.value.trim();
    }
    var isRoleAvailable = true;
    var clinicianRolesAvaiable = null;
    var setCliniciansRoleAvailableResponse;
    if (isRoleAvailable) {
      this.getUsersListByRoleIdAvailableOnClick(
        clinicianRolesAvaiable,
        !notInit ? true : false,
        !notInit ? false : true,
        optionShow,
        searchKeyword
      );
    } else {
      this.chatWithUsersLoading = false;
    }
  }

  searchOnEnter(event, optionShow) {
    console.log("SEARCH-------------------------------------");
    console.log(optionShow);
    console.log("SEARCH-------------------------------------");

    if (
      optionShow == "staffs" ||
      optionShow == "taggedUsers" ||
      optionShow == "patient"
    ) {
      if (event.keyCode == 13) {
        event.preventDefault();

        if (this.srch.nativeElement.value.trim()) {
          this.search(optionShow);
        } else {
          this.reset(optionShow);
        }
      }
    }
  }
  filterBranch(event) {
    if (event.target.value) {
      var previousSelectedTenant = this.chatWithTenantFilter.tenants.find(
        tenant => tenant.id == this.chatWithTenantFilter.selectedTenant
      );
      this.chatWithTenantFilter.selectedTenant = event.target.value;
      console.log(this.chatWithTenantFilter);
      console.log(this.chatWithTenantFilter.enabledReset);
      if (this.chatWithTenantFilter.enabledReset) {
        console.log(this.chatWithTenantFilter.selectedTenant);
        console.log(this.chatWithTenantFilter.tenants);
        if (this.optionShow == "staffs") {
          this.reset(this.optionShow);
        }
        if (this.optionShow == "patient") {
          this.reset(this.optionShow);
          this.chatWithLoader.patients = true;
        }
        var currentSelectedTenant = this.chatWithTenantFilter.tenants.find(
          tenant => tenant.id == this.chatWithTenantFilter.selectedTenant
        );
        var activityData = {
          activityName:
            "Chat With " +
            (this.optionShow == "staffs"
              ? "Staff "
              : this.optionShow == "patient"
                ? "Patient "
                : " ") +
            "Tenant Switching",
          activityType: "messaging",
          activityDescription:
            this.userData.displayName +
            " (" +
            this.userData.userId +
            ") has selected tenant " +
            currentSelectedTenant.tenantName +
            "(" +
            currentSelectedTenant.id +
            ")" +
            (previousSelectedTenant
              ? " from tenant " +
              previousSelectedTenant.tenantName +
              "(" +
              previousSelectedTenant.id +
              ")"
              : ""),
          tenantId:
            this._structureService.getCookie("crossTenantId") &&
              this._structureService.getCookie("crossTenantId") != "undefined" &&
              this._structureService.getCookie("tenantId") !==
              this._structureService.getCookie("crossTenantId")
              ? this._structureService.getCookie("crossTenantId")
              : this.userData.tenantId
        };
        this._structureService.trackActivity(activityData);
      } else {
        this.chatWithTenantFilter.enabledReset = true;
      }
    }
  }

  getSiteIds(siteId: any) {
		this.siteId = siteId['siteId'].toString();
    if(!this.preventMultipleUserTagCallOnInit) this.showData(this.optionShow);
    this.preventMultipleUserTagCallOnInit = false;
  }

  hideDropdown(hideItem: any){
    this.hideSiteSelection = hideItem.hideItem;
  }
  
  trackByFn(index,item) {
    return item.roleData.tenantRoleId;
  }
}
