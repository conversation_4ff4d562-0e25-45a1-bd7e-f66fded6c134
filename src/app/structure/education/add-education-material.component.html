<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Create Education Material </strong>
        </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']" id="hme">Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/education/education-material-list']" id="edu_centr">Education Center</a>
            </li>
            <li class="breadcrumb-item" id="create_ed_matrial">Create Education Material</li>
        </ol>
    </div>
    <div class="wait-loading" *ngIf="dataLoadingMsg">
        <img src="./assets/img/loader/loading.gif" />
    </div>
<!-- </section>
<section class="row chatroom-section"> -->
    <div class="col-lg-12">
        <section class="card">
            <div class="card-block">
                <form class="form-horizontal" [formGroup]="documentMaterialGroup">
                    <div class="form-group row">
                        <div class="col-md-3">
                            <label>Material Name * </label>
                        </div>
                        <div class="col-md-6">
                            <input type="text" class="form-control" id="material_nme" formControlName="materialName"
                                placeholder="Material Name">
                            <div *ngIf="documentMaterialGroup.controls['materialName'].errors&&(documentMaterialGroup.controls.materialName?.dirty || documentMaterialGroup.controls.materialName?.touched)"
                                class="alert alert-danger">
                                Please enter material name
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-md-3">
                            <label>Description</label>
                        </div>
                        <div class="col-md-6">
                            <textarea class="form-control"  id="description" formControlName="description" placeholder="" maxlength="1000"
                                rows="4"></textarea>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-md-3">
                            <label class="control-label">Attach Document *</label>
                        </div>
                        <div class="col-md-6">
                            <input [hidden]="signDocFile.length>0" formControlName="fileup" class="dropify" type="file"
                                multiple name="myFile" (change)="handleFileInput($event.target.files)"
                                data-allowed-file-extensions="pdf doc docx word xl xls xlsx odt jpg JPG jpeg JPEG png PNG"
                                data-max-file-size="10M" id="doc-file-upload">

                            <div class="uploads" *ngIf="signDocFile?.length" >
                                <div class="upload-item" *ngFor="let f of signDocFile; let i = index;">
                                    <div class="upload-item-top">
                                        <div style="float: left;">
                                            <span class="filename">{{ f.name }}</span>
                                            <div class="doc-file-remove">
                                                <span class="x" id="remv" (click)="removeFile(i, f.name)">x</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div *ngIf="invalidFileType" class="alert alert-danger file-upload-error">
                                Only pdf files are allowed.
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-4">
                            <br>
                            <section class="card existing">
                                <div class="card-header">
                                    <span class="cat__core__title">
                                        <strong>Shared Members</strong>
                                    </span>
                                </div>
                                <div class="card-block">
                                    <div class="cat__apps__messaging__header">
                                        <input type="text"  id="search_mmbrs" [(ngModel)]="searchTextShared" [ngModelOptions]="{standalone: true}"
                                           
                                            class="form-control cat__apps__messaging__header__input"
                                            placeholder="Search..." #existingMembers />
                                        <i class="icmn-search"></i>
                                        <button type="button"></button>
                                    </div>
                                    <div class="member-list">
                                        <table class="table table-hover nowrap existing-members" id="example1">
                                            <tbody>
                                                <tr
                                                    *ngFor="let member of getAssignedUsersArrayTemp | MessageGroupSearchFilter:existingMembers.value;">
                                                    <td><span *ngIf="!member.caregiver_userid">{{member.displayName}}</span><span *ngIf="member.caregiver_userid">{{member.caregiver_displayname}} ({{member.displayName}})</span>&nbsp;<span
                                                        *ngIf="!member.caregiver_dob && member.dob">-&nbsp;{{member.dob | date:'MM/dd/yyyy'}}
                                                    </span> &nbsp; <span
                                                        *ngIf="!member.caregiverIdentityValue && member.IdentityValue && member.IdentityValue != ''">[MRN: {{member.IdentityValue}}]
                                                    </span>&nbsp;<span
                                                    *ngIf="member.caregiver_dob">-&nbsp;{{member.caregiver_dob | date:'MM/dd/yyyy'}}
                                                </span> &nbsp; <span
                                                    *ngIf="member.caregiverIdentityValue && member.caregiverIdentityValue != ''">[MRN: {{member.caregiverIdentityValue}}]
                                                </span>&nbsp; <span
                                                            *ngIf="member.naTagNames && member.naTagNames != ''">({{member.naTagNames}})
                                                        </span>&nbsp;<br><span
                                                            *ngIf="(selectedTenant.id != member.tenantId && member.isUserGroup != '1')">[{{member.tenantName}}]</span>
                                                    </td>
                                                    <td> <a id="revoke" href="javascript: void(0);"
                                                            (click)="revokeShare(member);"><small><i
                                                                    class="icmn-cross"></i></small></a></td>
                                                </tr>
                                                <tr>
                                                    <td *ngIf="getAssignedUsersArrayTemp.length<=0">
                                                        No Users Available</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </section>
                        </div>

                        <div class="col-lg-8 pull-right education" id="staffListblock"  >
                            <br>
                            <section class="card">
                                <div class="card-header">
                                    <span class="cat__core__title">
                                        <strong>Share to members</strong>
                                        <input style="margin-top:18px;" type="button"
                                            class="btn btn-sm btn-primary pull-right" id="add_mmbr" (click)="addMember();" name=""
                                            value="Add" />
                                    </span>
                                </div>
                                <div class="card-block">

                                    <div
                                        class="cat__apps__messaging__header  search-with-clear chat-with-wrapper-search">

                                        <div class="chatwith-modal-tab chatwith-modal-tab-mar"
                                            style="margin-left: 55px;">
                                            <div [ngClass]="{'col-md-4': showUserTag==1, 'col-md-6': showUserTag!=1}"
                                                class="chatwith-model-head" (click)="showData('taggedUsers')"
                                                [class.cat__apps__messaging__tab--selected]="optionShow=='taggedUsers'"
                                                *ngIf="showUserTag == '1'" id="usr_tag" >User Tag</div>
                                            <div [ngClass]="{'col-md-4': showUserTag === '1' , 'col-md-6': showUserTag != '1' }"
                                                class="chatwith-model-head" (click)="showData('staffs')"
                                                [class.cat__apps__messaging__tab--selected]="optionShow=='staffs'" id="staff_tag">
                                                Staff</div>
                                            <div [ngClass]="{'col-md-4': showUserTag== '1', 'col-md-6': showUserTag != '1'}"
                                                class="chatwith-model-head" (click)="showData('patient')"
                                                [class.cat__apps__messaging__tab--selected]="optionShow=='patient'" id="pantn_tag">
                                                Patient</div>
                                        </div>


                                        <div class="row chat-with-wrapper-filter" style="width: 115%;margin-top: 20px;">
                                            <div class="col-md-6 chat-with-wrapper-filter-tenant"  
                                            *ngIf="((optionShow !='taggedUsers')  && !multiSiteEnable && chatWithTenantFilter.filterEnabled && chatWithTenantFilter.tenants.length) || (multiSiteEnable)" [hidden] = "!hideSiteSelection">
                                                <div class="row">
                                                    <div class="col-md-2 site-label">
                                                        <span>Site(s):&nbsp;</span>
                                                    </div>
                                                    <div class="col-md-10 chat-with-wrapper-filter-tenant-select">
                                                    <app-select-sites [events]="eventsSubject.asObservable()" [crossSite]=true (hideDropdown)="hideDropdown($event)" [filterType]=true (siteIds)="getSiteIds($event)">
                                                    </app-select-sites>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- [ngClass]="{'col-md-8 filter-enabled': ((optionShow !='taggedUsers')), 'col-md-12':!((optionShow !='taggedUsers'))}" -->
                                            <div class="chat-with-wrapper-search" [ngClass]="{'col-md-6' : (multiSiteEnable || (!multiSiteEnable && optionShow !='taggedUsers')),'col-md-12' : (!multiSiteEnable && optionShow =='taggedUsers')}"
                                                >
<div class="row">
                                                <div [style.width]="optionShow =='taggedUsers' ? '72%' : '72%'" [ngClass]="{'col-md-8' : (multiSiteEnable),'col-md-10' : (!multiSiteEnable && optionShow =='taggedUsers'),'col-md-6' : (!multiSiteEnable && optionShow !='taggedUsers')}">
                                                    <input type="text" [(ngModel)]="searchText" [ngModelOptions]="{standalone: true}"
                                                        [hidden]="optionShow !='roles'"
                                                   
                                                        class="search-width-grp form-control cat__apps__messaging__header__input"
                                                        placeholder="Search Role(s)..." #newMembers
                                                         id="userSearchTxtRoles" />
                                                    <input (keydown)="searchOnEnter($event,optionShow)"
                                                        [hidden]="optionShow ==='roles'"
                                                        class="form-control cat__apps__messaging__header__input groups-srch-width"
                                                        id="chat-with-modal-search-box" placeholder="Search Here"
                                                        #userSearch>
                                                </div>
                                                <div [ngClass]="{'col-md-4' : (multiSiteEnable || (!multiSiteEnable && optionShow !='taggedUsers')),'col-md-2' : (!multiSiteEnable && optionShow =='taggedUsers')}">
                                                <button [hidden]="optionShow ==='roles'"
                                                    [disabled]="this.srch.nativeElement.value.trim() == ''"
                                                    type="button" class="btn btn-sm btn-primary srchBtn" title="Search"
                                                    (click)="search(optionShow)" id="srch">Search</button>
                                                <button [hidden]="optionShow ==='roles'" type="button"
                                                    class="btn btn-sm btn-default resetBtn" title="Reset" id="reset"
                                                    (click)="reset(optionShow)">Reset</button>
                                            </div>
                                            </div>
                                        </div>
                                        </div>

                                    </div>
                                    <div class="form-group row">

                                        <div class="col-md-12">

                                            <ul class="treeview treeview-section-ui" style="width:100%;"
                                                *ngIf="optionShow ==='taggedUsers'">
                                                <li *ngFor="let cliniciansRole of newMemberListByRoleWiseTagUser; let i = index"
                                                    [hidden]="cliniciansRole.searchHideStatus"
                                                    class="role-{{cliniciansRole.roleData.tenantRoleId}}">
                                                    <i class="fa fa-plus expand-icon-{{cliniciansRole.roleData.tenantRoleId}}"
                                                        (click)="callAccordion(cliniciansRole.roleData.tenantRoleId, i)"></i>
                                                    <input type="checkbox" name="middle[]"
                                                        value="{{cliniciansRole.roleData.tenantRoleId}}"
                                                        id="role-{{cliniciansRole.roleData.tenantRoleId}}"
                                                        (change)="checkboxChanged($event)">
                                                    <label for="middle"
                                                        class="custom-unchecked">{{cliniciansRole.roleData.tenantName }}</label>

                                                    <ul
                                                        class="sub-item-panel sub-item-panel-{{cliniciansRole.roleData.tenantRoleId}}">
                                                        <li class='fa fa-caret-right' 
                                                            style="display: list-item!important;"
                                                            *ngFor="let usertagUser of cliniciansRole.userList">
                                                            <span class="ag-grid-cell"
                                                                style="line-height: 30px;padding-left:5px;">{{usertagUser.name}}{{((usertagUser.caregiverName)?"("+usertagUser.caregiverName+")" : "")}}
                                                            </span>
                                                        </li>
                                                    </ul>
                                                </li>

                                                <div class="chat-with-empty-data"
                                                    *ngIf="!(newMemberListByRoleWiseTagUser).length && !chatWithLoader.usertag">
                                                    No
                                                    Usertag Available. <span *ngIf="userSearch.value"><span
                                                            style="color: #0088ff;cursor: pointer;"
                                                            (click)="reset(optionShow)" id="reset_usertag"> Click Here </span> to Reset the
                                                        Search.</span></div>

                                                <div class="loading-container loader" *ngIf="loadingGroups">
                                                    <ng-container [ngTemplateOutlet]="loader"></ng-container>
                                                    <div class="loading-text">{{'MESSAGES.LOADING_USER_TAGS' | translate}}</div>
                                                </div>
                                            </ul>


                                            <div *ngIf="optionShow=='taggedUsers'" class="col-md-12">
                                                <div
                                                    style="text-align: center;width:100%;margin-top: 14px;float: left;">
                                                    <button type="button"
                                                        [hidden]="!( optionShow =='taggedUsers' && noMoreItemsAvailableUserags && newMemberListByRoleWiseTagUser.length)"
                                                        class="btn btn-sm btn-default"
                                                        (click)="loadMoreUerTags('usertag',loadMoreSearchValue, true)" id='ld_more_usrs'>{{loadMoremessage.users}}</button>
                                                </div>
                                            </div>

                                            <div *ngIf="optionShow=='staffs'" class="col-md-12">

                                                <div class="chat-with-empty-data"
                                                    *ngIf="!(userListChatwith).length && !chatWithLoader.staffs">No
                                                    Clinicians Available. <span *ngIf="userSearch.value"><span
                                                            style="color: #0088ff;cursor: pointer;"
                                                            (click)="reset(optionShow)" id="reset_clinicians"> Click Here </span> to Reset the
                                                        Search.</span></div>
                                                <div *ngIf="chatWithLoader.staffs" class="loading-container loader">
                                                    <ng-container [ngTemplateOutlet]="loader"></ng-container>
                                                    <div class="loading-text">{{'MESSAGES.LOADING_CLINICIANS' | translate}}</div>
                                                </div>
                                            </div>

                                            <ul class="treeview treeview-section-ui" style="width:100%;"
                                                *ngIf="optionShow ==='staffs'">
                                                <li *ngFor="let cliniciansRoleUser of userListChatwith ">

                                                    <input type="checkbox" (change)="checkboxChanged($event)"
                                                        name="cliniciansRoleUser[]"
                                                        [attr.data-roleSet]="cliniciansRoleUser.roleId"
                                                        id="role-{{cliniciansRoleUser.roleId}}-{{cliniciansRoleUser.id}}"
                                                        value="{{cliniciansRoleUser.userid}}" class="cliniciansRoleUser"
                                                        title="{{cliniciansRoleUser.displayname}}">
                                                    <label for="{{cliniciansRoleUser.displayname}}"
                                                        class="custom-unchecked">{{cliniciansRoleUser.displayname}}&nbsp;<span
                                                            *ngIf="cliniciansRoleUser.naTagNames">({{cliniciansRoleUser.naTagNames}})</span></label>
                                                </li>
                                            </ul>

                                            <div *ngIf="optionShow=='staffs'" class="col-md-12">
                                                <div
                                                    style="text-align: center;width:100%;margin-top: 14px;float: left;">
                                                    <button type="button"
                                                        [hidden]="!( optionShow =='staffs' && !noMoreItemsAvailable.users && userListChatwith.length)"
                                                        class="btn btn-sm btn-default"
                                                        (click)="loadMoreUsers('staffs',loadMoreSearchValue, true)" id="load_more_usrs">{{loadMoremessage.users}}
                                                    </button>
                                                </div>
                                            </div>

                                            <div *ngIf="optionShow=='patient'" class="col-md-12">

                                                <div class="chat-with-empty-data"
                                                    *ngIf="!(userListChatwith).length && !chatWithLoader.patients">No
                                                    Patients Available <span *ngIf="userSearch.value"><span
                                                            style="color: #0088ff;cursor: pointer;"
                                                            (click)="reset(optionShow)" id="reset_patnts"> Click Here </span> to Reset the
                                                        Search.</span></div>
                                                <div *ngIf="chatWithLoader.patients" class="loading-container loader">
                                                    <ng-container [ngTemplateOutlet]="loader"></ng-container>
                                                    <div class="loading-text">{{'MESSAGES.LOADING_PATIENTS' | translate}}</div>
                                                </div>
                                            </div>

                                            <ul class="treeview treeview-section-ui" style="width:100%;"
                                                *ngIf="optionShow ==='patient'">
                                                <li *ngFor="let user of (userListChatwith)">

                                                    <input type="checkbox" (change)="checkboxChanged($event)"
                                                        name="cliniciansRoleUser[]" [attr.data-roleSet]="user.roleId"
                                                        id="role-{{user.roleId}}-{{user.id}}" value="{{user.userid}}"
                                                        class="cliniciansRoleUser" title="{{user.displayname}}">

                                                    <label *ngIf="!user.caregiver_userid" for="{{user.displayname}}"
                                                        class="custom-unchecked">{{user.displayname}}&nbsp;<span
                                                        *ngIf="user.dob">- {{user.dob | date:'MM/dd/yyyy'}}
                                                    </span>&nbsp;<span
                                                    *ngIf="user.IdentityValue">[MRN: {{user.IdentityValue}}]
                                                      </span> &nbsp;<span
                                                    *ngIf="user.naTagNames">({{user.naTagNames}})
                                                      </span></label>

                                                    <label *ngIf="user.caregiver_userid" for="{{user.displayname}}"
                                                        class="custom-unchecked">{{user.caregiver_displayname}}
                                                        ({{user.displayname}})&nbsp;<span
                                                        *ngIf="user.caregiver_dob">- {{user.caregiver_dob | date:'MM/dd/yyyy'}}
                                                    </span>&nbsp;<span
                                                    *ngIf="user.caregiverIdentityValue">[MRN: {{user.caregiverIdentityValue}}]
                                                     </span>&nbsp;<span
                                                   *ngIf="user.naTagNames">({{user.naTagNames}})
                                                    </span></label>
                                                   </li>
                                                </ul>

                                            <div *ngIf="optionShow=='patient'" class="col-md-12">
                                                <div
                                                    style="text-align: center;width:100%;margin-top: 14px;float: left;">
                                                    <button type="button"
                                                        [hidden]="!( optionShow =='patient' && !noMoreItemsAvailable.users && userListChatwith.length)"
                                                        class="btn btn-sm btn-default"
                                                       id="load_more"  (click)="loadMoreUsers('patient',loadMoreSearchValue, true)">{{loadMoremessage.users}}</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                    <div class="form-actions">
                        <button [disabled]="!documentMaterialGroup.valid || formSubmitted" type="button"
                            class="btn btn-primary" (click)="createTrainingMaterial()" id="submit_btn">Submit</button>
                        <a [routerLink]="['/education/education-material-list']" class="btn btn-default" id="cancle_link" >Cancel</a>
                    </div>
                </form>
            </div>
        </section>
    </div>
</section>
<ng-template #loader>
  <ch-loader [showLoader]="true"></ch-loader>
</ng-template>
<!-- END: tables/datatables -->
