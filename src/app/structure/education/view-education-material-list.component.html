<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>View Education Materials</strong>
        </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item" *ngIf="trainingManagePermission">
                <a [routerLink]="['/education/education-material-list']">Education
                    Center</a>
            </li>
            <li class="breadcrumb-item">View Education Materials</li>
        </ol>

        <div class="row">
            <div class="col-lg-12">
                <div class="mb-5 pdg-list">
                    <div class="wait-loading" *ngIf="dataLoadingMsg">
                        <img src="./assets/img/loader/loading.gif" />
                    </div>
                    <div class="exportData" *ngIf="!dataLoadingMsg" [hidden]="userData.enable_export_data != '1'">
                        <button daterangepicker [options]="_SharedService.exportOptions" (selected)="selectedDate($event, daterange)" class="btn btn-sm btn-default load-more">Export</button>
                        </div>
                    <table class="table table-hover" id="dtPDiscGroups" width="100%"></table>
                </div>
            </div>
        </div>
    </div>
</section>
<modal class="loading-container" [show-modal]="isModalOpen" [title]="title" [sub-title]="subTitle" [modal-retry]=false
    [modal-body]="modalBody" [show-header]="showHeader" [show-footer]="showFooter" [cancel-label]="cancelLabel"
    (closed)="closeModal()" [show-iframe]="showIframe">
</modal>