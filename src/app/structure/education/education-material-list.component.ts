import { Component, OnInit, ElementRef, Renderer } from '@angular/core';
import { DatePipe } from '@angular/common';
import { Router, ActivatedRoute } from '@angular/router';
import { StructureService } from '../structure.service';
import { SharedService } from './../shared/sharedServices';
import { FormpPipe } from '../forms/formp.pipe';
import { Modal } from './educationModal';
import { DomSanitizer } from '@angular/platform-browser';
import { FormBuilder } from '@angular/forms';
import { Store, StoreService } from '../shared/storeService';

declare var $: any;
declare var swal: any;

@Component({
	selector: 'app-education-material',
	templateUrl: './education-material-list.component.html',
	providers: [ DatePipe, FormpPipe ],
	styleUrls: ['./education-material.component.scss']
})
export class EducationMaterialComponent implements OnInit {
	groupList = [];
	dTable;
	datam;
	searchText = '';
	activeGroup = null;
	dataLoadingMsg = true;

	userDetails:any;
	userData:any = {};
	crossTenantOptions = [];
	crossTenantChangeSubscriber: any;
	contentLimit = 25;
	totalCount = 50;
	perPage = 25;
	currentPage = 1;
	contentOffset = 0;
	pdfOrDocSrc = '';
	pdfOrDocUrl = '';
	isModalOpen = false;
	cancelLabel = false;
	showIframe = false;
	modalBody;
	title;
	subTitle;
	showHeader;
	showFooter;
	pdfUrl: any;
	fileUploadedTenantId;
	isNursingAgencyEnabled = 0;
	nursing_agencies;
	selectedExport=false;
	isTableLoaded = false;
    public daterange: any = {};
    public options: any = {};
	constructor(
		private router: Router,
		private _structureService: StructureService,
		elementRef: ElementRef,
		renderer: Renderer,
		public _SharedService: SharedService,
		private storeService: StoreService,
		private modals: Modal,
		private sanitizer: DomSanitizer
	) {
		this.userDetails = this._structureService.userDetails;
		this.userData = JSON.parse(this.userDetails);
		renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
			if (event.target.id == 'editgrp' || event.target.id == 'editgrp-icon') {
				this._structureService.setCookie('educationMaterialId', this.activeGroup.id, 1);
				this.router.navigate([ '/education/update-education-material' ]);
			} else if (event.target.id == 'deletegrp' || event.target.id == 'deletegrp-icon') {
				this.deleteEducationMaterial(this.activeGroup.id);
			} else if (event.target.id == 'view-file' || event.target.id == 'view-file-eye') {
				let sample = 'https://www.antennahouse.com/XSLsample/pdf/sample-link_1.pdf';

				this.pdfUrl =
					this._structureService.educationMaterialFileUrl +
					'/' +
					this.fileUploadedTenantId +
					'/' +
					this.activeGroup.id +
					'-education-material.pdf';

				// if (this.activeGroup.uploadedFileName != null) {
				// 	this.pdfUrl =
				// 		this._structureService.educationMaterialFileUrl +
				// 		'/' +
				// 		this.fileUploadedTenantId +
				// 		'/' +
				// 		this.activeGroup.uploadedFileName.trim();
				// }
				this.showPdf(this.pdfUrl);
			}

			// Handle click on "Select all" control.
			if (event.target.id == 'pdg-select-all') {
				var rows = this.dTable.rows({ search: 'applied' }).nodes();
				$('input[type="checkbox"]', rows).not(':disabled').prop('checked', event.target.checked);
			}
			if (event.target.className.split(' ').indexOf('pdg-select-cls') != -1) {
				let count = 0;
				$('.pdg-select-cls').each(function() {
					if ($(this).prop('checked') == true) {
						count = 1;
					}
				});
				if (count > 0) {
					document.getElementById('archive-education-material').setAttribute('disabled', 'false');
				} else {
					document.getElementById('archive-education-material').setAttribute('disabled', 'disabled');
				}
			}

			//Handle Archive all
			if (event.target.id == 'archive-education-material') {
				swal(
					{
						title: 'Are you sure?',
						text: 'You will not be able to recover this education material(s).',
						type: 'warning',
						showCancelButton: true,
						cancelButtonClass: 'btn-default',
						confirmButtonClass: 'btn-warning',
						confirmButtonText: 'Ok',
						closeOnConfirm: true
					},
					() => {
						var allIds = [];
						var unreadCountList = false;

						$('input[name="pdgid[]"]').each(function() {
							if (this.checked) {
								allIds.push(parseInt($(this).val()));
								unreadCountList = true;
							}
						});

						if (!unreadCountList) {
							this._structureService.notifyMessage({
								messge: 'Select at least one patient discussion group',
								delay: 1000,
								type: 'warning'
							});
						} else {
							this.archiveAllMaterialtems(allIds);
						}
					}
				);
			}

			var x = document.getElementsByClassName("btn-warning");
     if(x && x[0])
			  x[0].setAttribute("id", "unique_cnfrm_btn");
			  
		});
		this.crossTenantChangeSubscriber = this._SharedService.crossTenantChange.subscribe((onInboxData) => {
			if (this.router.url.indexOf('/education/education-material-list') > -1) {
				this.ngOnInit();
			}
		});
	}

	/**
 * Soft delete education materials- multiple rows.
 */
	archiveAllMaterialtems(allIds) {
		this.dataLoadingMsg = true;
		this._structureService.deleteEducationMaterials(allIds).subscribe((data) => {
			const deletedObj = Object.keys(data).map((key) => data[key]);
			let deletedMaterialId = deletedObj[0].deleteEducationMaterials.id;
			var notify = $.notify('Success! Education material(s) deleted');
			setTimeout(function() {
				notify.update({ type: 'success', message: '<strong>Success! Education material(s) deleted</strong>' });
			}, 1000);
			document.getElementById('archive-education-material').setAttribute('disabled', 'disabled');
			this.populateEducationData();
			var activityLogMessage =
				this.userData.displayName + " deleted Education Materials with id's " + deletedMaterialId;
			var activityData = {
				activityName: 'Delete Education Material',
				activityType: 'manage education material',
				activityDescription: activityLogMessage
			};
			this._structureService.trackActivity(activityData);
			document.getElementById('archive-education-material').setAttribute('disabled', 'disabled');
		});
	}

	ngOnInit() {
		this.isNursingAgencyEnabled = this.userData.config.enable_nursing_agencies_visibility_restrictions
			? this.userData.config.enable_nursing_agencies_visibility_restrictions
			: 0;
		this.nursing_agencies = this.userData.nursing_agencies ? this.userData.nursing_agencies : '';

		console.log('NURSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSS');
		console.log(this.isNursingAgencyEnabled);
		console.log(this.nursing_agencies);
		console.log('NURSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSSS');

		this.populateEducationData();
		this.refreshScheduleFormTable();
	}

	refreshScheduleFormTable() {}

	ngOnDestroy() {
		if (this.crossTenantChangeSubscriber) {
			this.crossTenantChangeSubscriber.unsubscribe();
		}
	}

	/**
	 *  Soft delete Education material for row wise.
	 */
	deleteEducationMaterial(id) {
		var Ids = [];
		Ids.push(parseInt(id));
		swal(
			{
				title: 'Are you sure?',
				text: 'You will not be able to recover this education material',
				type: 'warning',
				showCancelButton: true,
				cancelButtonClass: 'btn-default',
				confirmButtonClass: 'btn-warning',
				confirmButtonText: 'Ok',
				closeOnConfirm: true
			},
			() => {
				this.dataLoadingMsg = true;
				this._structureService.deleteEducationMaterials(Ids).subscribe((data) => {
					const deletedObj = Object.keys(data).map((key) => data[key]);
					let deletedMaterialId = deletedObj[0].deleteEducationMaterials.id;
					var notify = $.notify('Success! Education material deleted');
					setTimeout(function() {
						notify.update({
							type: 'success',
							message: '<strong>Success! Education material deleted</strong>'
						});
					}, 1000);
					this.populateEducationData();
					document.getElementById('archive-education-material').setAttribute('disabled', 'disabled');

					var activityLogMessage =
						this.userData.displayName + ' deleted Education Material with id ' + deletedMaterialId;
					var activityData = {
						activityName: 'Delete Education Material',
						activityType: 'manage education material',
						activityDescription: activityLogMessage
					};
					this._structureService.trackActivity(activityData);
				});
			}
		);


		var y = document.getElementsByClassName("btn-warning");
     
		y[0].setAttribute("id", "recover_cnfrm_btn");


	}
	selectedDate(value: any, datepicker?: any) {
		// this is the date  selected
		console.log("selectedDate",value);
		// return false;
this.selectedExport = true;
		// any object can be passed to the selected event and it will be passed back here
		datepicker.start = value.start;
		datepicker.end = value.end;

		// use passed valuable to update state
		this.daterange.start = value.start;
		this.daterange.end = value.end;
		this.daterange.label = value.label;
		var table = $('#dtPDiscGroups').DataTable();
		if(this.selectedExport == true)
		table.button('.buttons-excel').trigger();
}
	toTimestamp(strDate){
		var datum = Date.parse(strDate);
	 return (datum).toString();
	}
	/**
 * Load Ecucation material list func.
 */
	populateEducationData() {
		var self = this;
		let datas: any;
		if (this.dTable) {
			this.dTable.destroy();
		}
		var isTrue = false;
		if (this.groupList.length > 99) {
			isTrue = true;
		}
		// Array holding selected row IDs
		$(()=>{
		this.dTable = $('#dtPDiscGroups').DataTable({
			autoWidth: false,
			responsive: true,
			bprocessing: true,
			bServerSide: true,
			bpagination: true,
			bsorting: true,
			retrieve: true,
			bsearching: true,
			stateSave: true,
			bInfo: true,
			lengthMenu: [ [ 25, 50 ], [ 25, 50 ] ],
			fnDrawCallback: function(oSettings) {
				console.log('oSettings', oSettings);
				if (
					oSettings._iRecordsTotal == 0 ||
					oSettings._iRecordsTotal < oSettings._iDisplayLength ||
					oSettings.aoData.length == 0
				) {
					$('.dataTables_paginate').hide();
				} else {
					$('.dataTables_paginate').show();
				}
				if (oSettings.aoData.length == 0) {
					$('.dataTables_info').hide();
				}
			},
			fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
				$(nRow).on('click', () => {
					console.log('KKKKKKKKKKKKKKKKKKKKKKKKKKKK');
					console.log(aData);
					this.fileUploadedTenantId = aData.tenantId;
					console.log('KKKKKKKKKKKKKKKKKKKKKKKKKKKK');
					this.activeGroup = aData;
				});
			},
			dom:
			"<'row'<'col-sm-5 'l><'col-sm-3'f><'col-sm-2 searchButton'>>" +
			"<'row'<'col-sm-12'tr>>" +
			"<'row'<'col-sm-5'i><'col-sm-7'p>>",
			  buttons: [
				{
				  extend: 'excel',
				  text: 'All Pages',
				  title: 'Education Material List',
				  exportOptions: {
					columns:[1,2,3,4,5,6]
				  },
				  action: function ( e, dt, node, config ) {
				  var selfButton = this;
				  var oldStart = dt.settings()[0]._iDisplayStart;
				  dt.one('preXhr', function (e, s, data) {
					  data.start = 0;
					  data.length = this.totalCt;
					  dt.one('preDraw', function (e, settings) {
						  $.fn.dataTable.ext.buttons.excelHtml5.action.call(selfButton, e, dt, node, config);
						  dt.one('preXhr', function (e, s, data) {
							  settings._iDisplayStart = oldStart;
							  data.start = oldStart;
						  });
						  setTimeout(dt.ajax.reload, 0);
						  return false;
					  });
				  });
				  dt.ajax.reload();
				  }
		  }], 
			initComplete: function(){
			  $('.dataTables_filter label input').attr('placeholder','Search');
			  $('.dataTables_filter label input').unbind();
			  $("div.dataTables_filter input").on('keydown', function(e) {
				if (e.which == 13) {
				  var value = $("div.dataTables_filter input").val();
				  if(value)
				  {
				  value = value.replace('”','"');
				  value = value.replace("‘","'");
				  value = value.replace("’","'");
				  self.dTable.search(value).draw();
				}
				else
				{
					self.dTable.search('').draw();
				  }
				}
			  });
			  $("div.dataTables_filter input").on('keypress', function(e) {
				$(".searchBEdu").prop('disabled', false);
			  });
			  $("div.dataTables_filter input").on('keyup', function(e) {
				var value = $("div.dataTables_filter input").val();
				if(value)
				{
				}
				else
				$(".searchBEdu").prop('disabled', true);
			  });
			  $("div.searchButton")
			  .html('<button disabled="true" class="btn btn-sm btn-info searchBEdu" title="Search" type="submit" id="search_btn_edu">Search</button>'+
			  '<button style="margin-left:10px;" class="btn btn-sm btn-default resetBEdu" title="Reset" type="submit" id="reset_btn_edu">Reset</button>');
			  var value =  $("div.dataTables_filter input").val();
			  if(value)
			  {
				$(".searchBEdu").prop('disabled', false);
			  }
			  $("div.dataTables_filter input").on('paste', function(event) {
				console.log("eeeeeeeeeeeeeeeeee",event);
				var element = this;
				var text ;
				setTimeout(function () {
				  text = $(element).val();
				  if(text)
				  {
					$(".searchBEdu").prop('disabled', false);
				  }
			  }, 100);
				});
				$(".buttons-collection").click(function(event) {
					setTimeout(function () {
					  if($(".buttons-collection").attr("aria-expanded") === "true" && $(".dt-button-collection").find("button").length == 0){
						$(".dt-button-collection").remove();
						$(".dt-button-background").remove();
						$(".buttons-collection").attr("aria-expanded","false");
					  }
					},500);
				});
			  },
			//data: this.groupList,
			ajax: function(dat, callback, settings) {
				self.dataLoadingMsg = true;
				let orderData;
				let searchText;
				let orderby;
				let limit;
				let startDate = '';
        		let endDate = '';
        		let label = '';

				var i = dat.order[0].column ? dat.order[0].column : '';
				orderby = dat.order[0].dir ? dat.order[0].dir : '';
				if (i != 0) {
					orderData = dat.columns[i].data ? dat.columns[i].data : '';
				} else {
					orderData = '';
				}
				let showFilterAppliedMessage = false;
				if (!self.isTableLoaded) {
					self.isTableLoaded = true;
					const searchStored = self.storeService.getStoredData(Store.SEARCH_EDU_MATERIALS);
					const searchText = searchStored && searchStored.searchText ? searchStored.searchText : '';
					dat.search.value = searchText;
					if (searchText) {
						showFilterAppliedMessage = true;
					}
					setTimeout(() => {
					  const searchBox = $('.dataTables_filter input');
					  searchBox.val(searchText);
					}, 0);
				}
				self._structureService.notifySearchFilterApplied(showFilterAppliedMessage);
				searchText = dat.search.value ? dat.search.value : '';
				self.storeService.storeData(Store.SEARCH_EDU_MATERIALS, { searchText });
				if(self.selectedExport == true){
					startDate = self.toTimestamp(self.daterange.start);
					endDate = self.toTimestamp(self.daterange.end);
					label = self.daterange.label;
					self.selectedExport = false;
			}
				console.log(
					'searchData-fields',
					'i - ',
					i,
					'offset - ',
					dat.start,
					'orderby - ',
					orderby,
					'orderData - ',
					orderData,
					'searchText - ',
					searchText
				);

				self._structureService
					.getEducationMaterialList(dat.length, dat.start, orderData, orderby, searchText,startDate,endDate)
					.then((resultData) => {
						self.dataLoadingMsg = false;
						datas = {};
						self.datam = {};
						if (dat.start == 0) {
							this.totalCt = resultData['getSessionTenant'].educationMaterialPagination.totalCount;
						}
						datas = [];
						datas = resultData['getSessionTenant']
							? resultData['getSessionTenant']['educationMaterialList']['data']
							: [];

						self.groupList = datas;
						let draw;
						let total;
						if (datas && datas.length == 0 && searchText == '') {
							draw = 0;
							total = 0;
						} else {
							draw = dat.draw;
							total = this.totalCt;
						}

						self.datam = {
							draw: draw,
							recordsTotal: total,
							recordsFiltered: total,
							aaData: datas
						};
						callback(self.datam);
					});
			},
			columns: [
				{
					title:
						'<input type="checkbox" name="select_all" value="1" style="float:left" class="pdg-select-all-indicator pdg-select-cls" id="pdg-select-all" title="Select all for Delete">'
				},
				{ title: 'Material Name', data: 'materialName',rowId: 'matrial_nme' },
				{ title: 'Description', data: 'description',rowId: 'desc' },
				{ title: 'File Name', data: 'fileName',rowId: 'fle_nme' },
				{ title: 'Created By', data: 'createdBy',rowId: 'created_by' },
				{ title: 'Created On', data: 'createdOn',rowId: 'created_on'  },
				{ title: 'Updated On', data: 'updatedOn' ,rowId: 'updated_on' },
				{ title: 'Actions' }
			],
			columnDefs: [
				{
					targets: 0,
					searchable: false,
					orderable: false,
					width: '3%',
					className: 'dt-body-center',

					render: (document, type, row) => {
						if (
							this.userData.config.enable_nursing_agencies_visibility_restrictions == 1 &&
							this.userData.nursing_agencies &&
							this.userData.nursing_agencies != ''
						) {
							if (row.createdBy == this.userData.userId) {
								return (
									'<input type="checkbox" name="pdgid[]" class="pdg-select-all-indicator pdg-select-cls"   id="bx'+row.id +'"            value="' +
									row.id +
									'">'
								);
							} else {
								return (
									'<input type="checkbox" disabled="disabled" name="pdgid[]" class="pdg-select-all-indicator pdg-select-cls"   id="bx'+row.id +'"   value="' +
									row.id +
									'">'
								);
							}
						} else {
							return (
								'<input type="checkbox" name="pdgid[]" class="pdg-select-all-indicator pdg-select-cls"    id="bx'+row.id +'"  value="' +
								row.id +
								'">'
							);
						}
					}
				},
				{
					data: null,
					targets: 1,
					width: '15%',
					render: (data, type, row) => {
						if (row.materialName) {
							return row.materialName;
						} else {
							return '';
						}
					}
				},
				{
					data: null,
					targets: 2,
					width: '15%',
					orderable: true,
					render: (data, type, row) => {
						if (row.description) {
							return row.description;
						} else {
							return '';
						}
					}
				},
				{
					data: null,
					targets: 3,
					width: '15%',
					orderable: true,
					render: (data, type, row) => {
						if (row.fileName) {
							return row.fileName;
						} else {
							return '';
						}
					}
				},
				{
					data: null,
					targets: 4,
					width: '13%',
					orderable: true,
					render: (data, type, row) => {
						if (row.displayName) {
							return row.displayName;
						} else {
							return '';
						}
					}
				},
				{
					data: null,
					targets: 5,
					width: '13%',
					orderable: true,
					//orderData: 5,
					render: (data, type, row) => {
						if (row.createdOn && row.createdOn.toString().length == 13) {
						// if (row.createdOn) {
							var datePipe = new DatePipe('en-US');
							return datePipe.transform(row.createdOn, 'MMM dd hh:mm a');
						} else {
							return '';
						}
					}
				},
				{
					data: null,
					targets: 6,
					width: '13%',
					orderable: true,
					//orderData: 5,
					render: (data, type, row) => {
						if (row.updatedOn) {
							var datePipe = new DatePipe('en-US');
							return datePipe.transform(row.updatedOn, 'MMM dd hh:mm a');
						} else {
							return '';
						}
					}
				},
				{
					data: null,
					orderable: false,
					render: (data, type, row) => {
						let actions = '';
						if (
							this.userData.config.enable_nursing_agencies_visibility_restrictions == 1 &&
							this.userData.nursing_agencies &&
							this.userData.nursing_agencies != ''
						) {
							if (row.createdBy == this.userData.userId) {
								actions +=
									`<a class="pull-right btn btn-sm" title="Delete" href="javascript: void(0);" data-rowId ="` +
									row.id +
									`" id="deletegrp" ><i data-rowId ="` +
									row.id +
									`" id="deletegrp-icon" class="fa fa-trash" aria-hidden="true"></i></a>`;

								actions +=
									`<a class="pull-right btn btn-sm" title="Edit" href="javascript: void(0);" data-rowId ="` +
									row.id +
									`" id="editgrp"  ><i data-rowId ="` +
									row.id +
									`" id="editgrp-icon" class="fa fa-pencil" aria-hidden="true"></i></a>`;

								actions +=
									`<a class="pull-right btn btn-sm" title="View Document" href="javascript: void(0);" data-rowId ="` +
									row.id +
									`" id="view-file"  ><i data-rowId ="` +
									row.id +
									`" id="view-file-eye" class="icmn-eye" aria-hidden="true"></i></a>`;
							} else {
								actions +=
									`<a class="pull-right btn btn-sm" title="View Details" href="javascript: void(0);" data-rowId ="` +
									row.id +
									`" id="editgrp"  ><i data-rowId ="` +
									row.id +
									`" id="editgrp-icon" class="fa fa-file" aria-hidden="true"></i></a>`;

								actions +=
									`<a class="pull-right btn btn-sm" title="View Document" href="javascript: void(0);" data-rowId ="` +
									row.id +
									`" id="view-file"  ><i data-rowId ="` +
									row.id +
									`" id="view-file-eye" class="icmn-eye" aria-hidden="true"></i></a>`;
							}
						} else {
							actions +=
								`<a class="pull-right btn btn-sm" title="Delete" href="javascript: void(0);" data-rowId ="` +
								row.id +
								`" id="deletegrp" ><i data-rowId ="` +
								row.id +
								`" id="deletegrp-icon" class="fa fa-trash" aria-hidden="true"></i></a>`;

							actions +=
								`<a class="pull-right btn btn-sm" title="Edit" href="javascript: void(0);" data-rowId ="` +
								row.id +
								`" id="editgrp"  ><i data-rowId ="` +
								row.id +
								`" id="editgrp-icon" class="fa fa-pencil" aria-hidden="true"></i></a>`;

							actions +=
								`<a class="pull-right btn btn-sm" title="View Document" href="javascript: void(0);" data-rowId ="` +
								row.id +
								`" id="view-file"  ><i data-rowId ="` +
								row.id +
								`" id="view-file-eye" class="icmn-eye" aria-hidden="true"></i></a>`;
						}

						return actions;
					},

					width: '13%',
					targets: 7
				}
			],
			order: [ [ 6, 'desc' ] ]
		});
		$('div.dataTables_filter').addClass('view_edu_search'); 

		$('#dtPDiscGroups').on('click', 'tr .child', function (e) {
			var parentRow = $(this).closest("tr").prev()[0];
			self.activeGroup = self.dTable.row( parentRow ).data();
			self.fileUploadedTenantId = self.activeGroup.tenantId;
}); 
		$('.dataTables_filter input').attr('id','Searchbox');

		$(document).on('click', '.resetBEdu',(event)=> {
			self.dTable.search('').draw();
			$(".searchBEdu").prop('disabled', true);
		  });
		  $(document).on('click', '.searchBEdu',(event)=> {
		  var value = $('#dtPDiscGroups_wrapper #dtPDiscGroups_filter label input').val();
		  if(value)
		  {
		  value = value.replace('”','"');
		  value = value.replace("‘","'");
		  value = value.replace("’","'");
		  self.dTable.search(value).draw();
		  }
		  else
		  {
			self.dTable.search('').draw();
		  }
		   });

		this.dataLoadingMsg = false;
			});

	}
	showPdf(targetEvent): void {
		this._structureService.pdfLoaded = false;
		this.pdfOrDocSrc = targetEvent;
		this.isModalOpen = true;
		this.cancelLabel = true;
		this.showIframe = true;
		this.modalBody = this.sanitizer.bypassSecurityTrustResourceUrl(`${this.pdfOrDocSrc}#navbar=0&navpanes=0`);
		this._structureService.pdfLoaded = true;
		this.modals.show();
	}

	closeModal(): void {
		this.modals.hide();
		this.isModalOpen = false;
		this.modalBody = '';
		this.pdfOrDocSrc = '';
		this.pdfOrDocUrl = '';
		this.showIframe = false;
		this.cancelLabel = false;
	}
}
