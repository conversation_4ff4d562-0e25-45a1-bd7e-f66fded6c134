<style>
    .loading-container {
        padding: 20% 40% 20% 40%;
        top: 30%;
        position: absolute;
        width: 100%;
    }
    .doc-modal-content {
        height: 100% !important;
    }
    #wrap { width:100%; height: 95%; padding: 0; overflow: hidden; }
    #retryPdfIframe { width:100%; height: 90%; }
    #retryPdfIframe {
        -ms-zoom: 0.75;
        -moz-transform: scale(0.75);
        -moz-transform-origin: 0 0;
    }
</style>
<div class="modal-backdrop fade in" [style.display]="showModal ? 'block' : 'none'"></div>
<div class="modal forward-modal" *ngIf="!modalRetry" tabindex="-1" role="dialog" style="display: block"
    [style.display]="showModal ? 'block' : 'none'">
    <div class="modal-dialog" [ngClass]="{'modal-iframe-height':modalIframeBody}">
        <div class="modal-content"
            [ngClass]="{'modal-iframe-height':modalIframeBody,'video-transparent':modalVideoBody}">
            <div class="modal-header" [ngClass]="{'modal-header-hide':cancelLabel}">
                <h4 class="modal-title">{{title}}</h4>
                <button type="button" class="close" [ngClass]="{'video-close-button-position':modalVideoBody}"
                    data-dismiss="modal" (click)="hide()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" *ngIf="!modalIframeBody && !modalVideoBody" [innerHTML]="modalBody">
            </div>
            <div class="modal-body" *ngIf="modalIframeBody" [ngClass]="{'modal-iframe-height':modalIframeBody}">
                <iframe class="e2e-iframe-trusted-src" id="pdfIframe" scrolling="no" [src]="modalBody" width="100%"
                    height="100%" onload="javascript:this.contentWindow.location.hash=':0.page.20';">
                </iframe>
                <div  class="loading-container" *ngIf="showLoader">
                    <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                    <div class="loading-text">Loading ...</div>
                </div>
            </div>
            <div class="modal-body" *ngIf="modalVideoBody" class="centerme">
                <video poster="assets/modules/dummy-assets/common/img/Loading_icon.gif" [src]="modalBody"
                    class="centerme" controls="controls" autoplay></video>
            </div>

            <div *ngIf="showFooter" class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<!-- This moadl is added to resolve pdf loading issue in iframe. A loader and retry functionality is added. -->
<div class="modal forward-modal" *ngIf="modalRetry" tabindex="-1" role="dialog" style="display: block" [style.display]="showModal ? 'block' : 'none'">
    <div class="modal-dialog" [ngClass]="{'modal-iframe-height':modalIframeBody}">
        <div class="modal-content doc-modal-content" [ngClass]="{'modal-iframe-height':modalIframeBody,'video-transparent':modalVideoBody}">
            <div class="modal-header" [ngClass]="{'modal-header-hide':cancelLabel}">
                <h4 class="modal-title">{{title}}</h4>
                <button type="button" class="close" [ngClass]="{'video-close-button-position':modalVideoBody}" data-dismiss="modal" (click)="hide()"  aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" *ngIf="!modalIframeBody && !modalVideoBody" [innerHTML]="modalBody">
            </div>
            <div class="modal-body" id="wrap" *ngIf="modalIframeBody" [ngClass]="{'modal-iframe-height':modalIframeBody}">
                <iframe class="e2e-iframe-trusted-src" id="retryPdfIframe" scrolling="no" [src]="modalBody" onload="javascript:window.oniframeloadlatest();this.contentWindow.location.hash=':0.page.20';"> 
                </iframe>
                <div  class="loading-container" *ngIf="!this._structureService.pdfLoaded">
                    <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                    <div class="loading-text">Loading ...</div>
                </div>
                <div  class="loading-container" *ngIf="iframeLoaded && faildToLoadDoc">
                    <h4>Failed to load</h4>
                    <button (click)="retry()"> <i class="fa fa-refresh" aria-hidden="true"></i> Retry</button>
                </div>
            </div>
            <div class="modal-body" *ngIf="modalVideoBody" class="centerme">
                <video poster="assets/modules/dummy-assets/common/img/Loading_icon.gif" [src]="modalBody" class="centerme" controls="controls" autoplay></video>
            </div>
            
            <div *ngIf="showFooter" class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>