import { Component, OnInit, Pipe, PipeTransform } from '@angular/core';
import { DatePipe } from '@angular/common';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Pipe({
	name: 'MessageGroupSearchFilter'
})
export class MessageGroupSearchFilterPipe implements PipeTransform {
	transform(searchItems: any, criteria: any): any {
		if (!searchItems) {
			return [];
		} else {
			if (criteria) {
				return searchItems.filter(
					(item) =>
						item['displayName']
							? item['displayName'].toLowerCase().indexOf(criteria.toLowerCase()) != -1
							: item['name'].toLowerCase().indexOf(criteria.toLowerCase()) != -1
				);
			} else {
				return searchItems;
			}
		}
	}
}
@Pipe({
	name: 'filterMessageGroup'
})
export class filterMessageGroupPipe implements PipeTransform {
	transform(items: any[], args: any[]): any {
		var privilege = args[0];
		var userid = args[1];
		if (!items) return items;
		return items.filter((item) => {
			for (let key in item) {
				if (privilege) {
					return true;
				} else {
					console.log(item.isPublic, item.createdUser.id, userid);
					return item.isPublic == true || item.createdUser.id == userid;
				}
			}
			return false;
		});
	}
}

@Pipe({
	name: 'searchGroupfilterSchedule'
})
export class SearchGroupFilterSchedulePipe implements PipeTransform {
	transform(items: any[], field: string, value: string): any[] {
		if (!items) {
			return [];
		} else {
			var result = $.map(items, function(uitem) {
				var uSatus = true;
				if (uitem) {
					if (uitem.groupData.name.toLowerCase().indexOf(value.toLowerCase()) != -1) {
						uSatus = false;
					}
					uitem.searchHideStatus = uSatus;
					return uitem;
				}
			});
			return result;
		}
	}
}

@Pipe({
	name: 'searchRolefilterSchedule'
})
export class SearchRoleFilterSchedulePipe implements PipeTransform {
	transform(items: any[], field: string, value: string): any[] {
		if (!items) {
			return [];
		} else {
			var empty = true;
			var result = $.map(items, function(uitem) {
				var uSatus = true;
				if (uitem) {
					var userList = $.map(uitem.userList, function(user) {
						if (user.name.toLowerCase().indexOf(value.toLowerCase()) != -1) {
							empty = false;
							uSatus = false;
							user.searchHideStatus = false;
						} else {
							// console.log(
							// 	'>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'
							// );
							user.searchHideStatus = true;
						}

						// console.log('11111111111111111111');
						// console.log(items);
						// console.log(user);
						// console.log(value);
						// console.log('1111111111111111111');
					});
					uitem.searchHideStatus = uSatus;
					return uitem;
				}
			});
			result.empty = empty;

			// console.log('OKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK');
			// console.log(result);
			// console.log('OKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK');
			return result;
		}
	}
}

@Pipe({
	name: 'searchRolefilterScheduleGroups'
})
export class SearchRoleFilterScheduleGroupsPipe implements PipeTransform {
	transform(items: any[], field: string, value: string): any[] {
		if (!items) {
			return [];
		} else {
			var empty = true;
			var result = $.map(items, function(uitem) {
				var uSatus = true;
				if (uitem) {
					//var userList = $.map(uitem.roleData, function(user) {
					if (uitem.roleData.tenantName.toLowerCase().indexOf(value.toLowerCase()) != -1) {
						empty = false;
						uSatus = false;
						uitem.roleData.searchHideStatus = false;
					} else {
						// console.log(
						// 	'>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>><<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<<'
						// );
						uitem.roleData.searchHideStatus = true;
					}

					// console.log('222222222222222222222222222');
					// console.log(items);
					// console.log(uitem);
					// //console.log(user);
					// console.log(value);
					// console.log('222222222222222222222222222222');
					//});
					uitem.searchHideStatus = uSatus;
					return uitem;
				}
			});
			result.empty = empty;

			// console.log('OKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK');
			// console.log(result);
			// console.log('OKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK');
			return result;
		}
	}
}

@Pipe({
	name: 'searchRolefilterScheduleMsgGrps'
})
export class SearchRoleFilterMsgGrpsPipe implements PipeTransform {
	transform(items: any[], field: string, value: string): any[] {
		if (!items) {
			return [];
		} else {
			var result = items.filter((item) => {
				if (item[field.split('.')[0]][field.split('.')[1]].toLowerCase().indexOf(value.toLowerCase()) != -1) {
					item.filterUserStatus = false;
					return item;
				} else {
					item.filterUserStatus = true;
					var status = false;
					item['userList'].forEach((user) => {
						if ('displayname' in user) {
							if (user.displayname.toLowerCase().indexOf(value.toLowerCase()) != -1) {
								status = true;
							}
						}
						if ('name' in user) {
							if (user.name.toLowerCase().indexOf(value.toLowerCase()) != -1) {
								status = true;
							}
						}
					});
					if (status) {
						return item;
					}
				}
			});
			return result;
		}
	}
}

@Pipe({
	name: 'searchfilterroletreeview'
})
export class SearchFilterRoleTreeViewPipe implements PipeTransform {
	transform(items: any[], field: string, value: string): any[] {
		if (!items) {
			return [];
		} else {
			if (value) {
				var result = [];
				items.filter((user, key) => {
					if (user[field].toLowerCase().indexOf(value.toLowerCase()) != -1) {
						result.push(user);
					}
				});
				return result;
			} else {
				return items;
			}
		}
	}
}

@Pipe({
	name: 'pluralizeSchedulefilter'
})
export class pluralizeScheduleFilterPipe implements PipeTransform {
	transform(items: string, field: string, value: string): string {
		var pluralLetter = items.search(/(s|sh|ch|x|z)$/) != -1 ? 'es' : 's';
		return items + pluralLetter;
	}
}

@Pipe({
	name: 'searchfilter'
})
export class SearchFilterPipe implements PipeTransform {
	transform(items: any[], field: string, value: string): any[] {
		if (!items) {
			return [];
		} else {
			if (value) {
				var result = $.map(items, function(item) {
					if (item.name.toLowerCase().indexOf(value.toLowerCase()) != -1) {
						item.searchHideStatus = false;
					}
					return item;
				});
				return result;
			} else {
				var result = $.map(items, function(item) {
					item.searchHideStatus = false;
					return item;
				});
				return result;
			}
		}
	}
}
