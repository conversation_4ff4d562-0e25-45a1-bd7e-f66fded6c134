<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong> Create/Modify Education Material</strong>
            <a [routerLink]="['/education/add-education-material']" class="pull-right btn btn-sm btn-primary" id="create_edu_mat">Create
                Education Material<i class="ml-1"></i></a>
        </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']" id="homelnk" >Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/education/education-material-list']" id="edu_cntre">Education Center</a>
            </li>
            <li class="breadcrumb-item" id="create_mdfy_ed_materila"> Create/Modify Education Material</li>
        </ol>
        <div class="row">
            <div class="col-lg-12">
                <div class="mb-5 pdg-list" id="pdg-list-edu">
                    <a class="pull-right btn btn-sm btn-primary" id="archive-education-material" 
                        disabled="disabled">Delete Education
                        Material(s)</a>
                    <div class="wait-loading" *ngIf="dataLoadingMsg">
                        <img src="./assets/img/loader/loading.gif" />
                    </div>
                    <div class="exportData" *ngIf="!dataLoadingMsg" [hidden]="userData.enable_export_data != '1'">
                        <button daterangepicker [options]="_SharedService.exportOptions" (selected)="selectedDate($event, daterange)" class="btn btn-sm btn-default load-more">Export</button>
                        </div>
                    <table class="table table-hover" id="dtPDiscGroups" width="100%"></table>
                </div>
            </div>
        </div>
    </div>
</section>
<modal [show-modal]="isModalOpen" [title]="title" [sub-title]="subTitle" [modal-body]="modalBody" [modal-retry]=false
    [show-header]="showHeader" [show-footer]="showFooter" id="close_mdl" [cancel-label]="cancelLabel" (closed)="closeModal()"
    [show-iframe]="showIframe">
</modal>
<!-- END: tables/datatables -->