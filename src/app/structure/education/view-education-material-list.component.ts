import { Component, OnInit, ElementRef, Renderer } from '@angular/core';
import { DatePipe } from '@angular/common';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';
import { SharedService } from './../shared/sharedServices';
import { Modal } from './educationModal';
import { DomSanitizer } from '@angular/platform-browser';
import { FormBuilder } from '@angular/forms';
import { isInteger } from '@ng-bootstrap/ng-bootstrap/util/util';
import { Store, StoreService } from '../shared/storeService';
let moment = require('moment/moment');
declare var $: any;

@Component({
	selector: 'app-view-education-material',
	templateUrl: './view-education-material-list.component.html',
	providers: [ DatePipe ],
	styleUrls: ['./education-material.component.scss']
})
export class ViewEducationMaterialComponent implements OnInit {
	groupList = [];
	dTable;
	datam;
	searchText = '';
	selectedData;
	dataLoadingMsg = true;

	userDetails:any;
	userData:any = {};
	crossTenantOptions = [];
	crossTenantChangeSubscriber: any;
	contentLimit = 25;
	totalCount = 50;
	perPage = 25;
	currentPage = 1;
	pdfOrDocSrc = '';
	pdfOrDocUrl = '';
	isModalOpen = false;
	cancelLabel = false;
	showIframe = false;
	modalBody;
	title;
	subTitle;
	showHeader;
	showFooter;
	pdfUrl: any;
	manageConfig;
	fileUploadedTenantId;
	tenanatPrivileges;
	previlages;
	trainingManagePermission = false;
	loadingGroups = true;
	isTableLoaded = false;

	selectedExport=false;
public daterange: any = {};
public options: any = {};

	constructor(
		private route: ActivatedRoute,
		private router: Router,
		private _structureService: StructureService,
		private _formBuild: FormBuilder,
		private datePipe: DatePipe,
		elementRef: ElementRef,
		renderer: Renderer,
		public _SharedService: SharedService,
		private modals: Modal,
		private storeService: StoreService,
		private sanitizer: DomSanitizer
	) {
		this.userDetails = this._structureService.userDetails;
		this.userData = JSON.parse(this.userDetails);
		
		renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
			if (event.target.id == 'view-file') {
				this._structureService.setCookie('educationMaterialId', this.selectedData.id, 1);
				console.log('99999999999999999999999999999999999999');
				console.log(this.selectedData);
				console.log(this.selectedData.uploadedFileName);
				console.log('99999999999999999999999999999999999999');

				this.pdfUrl =
					this._structureService.educationMaterialFileUrl +
					'/' +
					this.fileUploadedTenantId +
					'/' +
					this.selectedData.id +
					'-education-material.pdf';

				// if (this.selectedData.uploadedFileName != null) {
				// 	this.pdfUrl =
				// 		this._structureService.educationMaterialFileUrl +
				// 		'/' +
				// 		this.fileUploadedTenantId +
				// 		'/' +
				// 		this.selectedData.uploadedFileName;
				// }
				let sample = 'http://www.pdf995.com/samples/pdf.pdf';
				sample = 'http://www.africau.edu/images/default/sample.pdf';
				//this.showPdf(sample);
				this.showPdf(this.pdfUrl);
			}
		});
		this.crossTenantChangeSubscriber = this._SharedService.crossTenantChange.subscribe((onInboxData) => {
			if (this.router.url.indexOf('/education/education-material-list') > -1) {
				this.ngOnInit();
			}
		});
	}

	ngOnInit(): void {
		this.tenanatPrivileges = this._structureService.getCookie('userPrivileges');
		this.tenanatPrivileges = this.tenanatPrivileges.split(',');
		var index = this.tenanatPrivileges.findIndex((x) => x == 'enableEducationTraining');
		if (index != -1) {
			this.trainingManagePermission = true;
		}
		this.populateEducationData();
	}

	ngOnDestroy() {
		if (this.crossTenantChangeSubscriber) {
			this.crossTenantChangeSubscriber.unsubscribe();
		}
	}
	selectedDate(value: any, datepicker?: any) {
		// this is the date  selected
		console.log("selectedDate",value);
		// return false;
this.selectedExport = true;
		// any object can be passed to the selected event and it will be passed back here
		datepicker.start = value.start;
		datepicker.end = value.end;

		// use passed valuable to update state
		this.daterange.start = value.start;
		this.daterange.end = value.end;
		this.daterange.label = value.label;
		var table = $('#dtPDiscGroups').DataTable();
		if(this.selectedExport == true)
		table.button('.buttons-excel').trigger();
}
	toTimestamp(strDate){
		var datum = Date.parse(strDate);
		return (datum).toString();
	}
	populateEducationData(): void {
		this.dataLoadingMsg = true;

		var self = this;
		let datas: any;
		if (this.dTable) {
			this.dTable.destroy();
		}
		var isTrue = false;
		if (this.groupList.length > 99) {
			isTrue = true;
		}
		// Array holding selected row IDs
		$(() => {            
		this.dTable = $('#dtPDiscGroups').DataTable({
			autoWidth: false,
			responsive: true,
			bprocessing: true,
			bServerSide: true,
			bpagination: true,
			bsorting: true,
			retrieve: true,
			bsearching: true,
			bInfo: true,
			lengthMenu: [ [ 25, 50 ], [ 25, 50 ] ],
			fnDrawCallback: function(oSettings) {
				console.log('oSettings', oSettings);
				if (
					oSettings._iRecordsTotal == 0 ||
					oSettings._iRecordsTotal < oSettings._iDisplayLength ||
					oSettings.aoData.length == 0
				) {
					$('.dataTables_paginate').hide();
				} else {
					$('.dataTables_paginate').show();
				}
				if (oSettings.aoData.length == 0) {
					$('.dataTables_info').hide();
				}
			},
			fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
				$(nRow).on('click', () => {
					this.selectedData = aData;
					this.fileUploadedTenantId = aData.tenantId;
				});
			},
			dom:
			"<'row'<'col-sm-4'l><'col-sm-4'f><'col-sm-2 searchButton'>>" +
			"<'row'<'col-sm-12'tr>>" +
			"<'row'<'col-sm-5'i><'col-sm-7'p>>",
			  buttons: [
				{
				  extend: 'excel',
				  text: 'All Pages',
				  title: 'View Education Materials',
				  exportOptions: {
					columns:[1,2,3,4,5,6]
				  },
				  action: function ( e, dt, node, config ) {
				  var selfButton = this;
				  var oldStart = dt.settings()[0]._iDisplayStart;
				  dt.one('preXhr', function (e, s, data) {
					  data.start = 0;
					  data.length = 2147483647;
					  dt.one('preDraw', function (e, settings) {
						  $.fn.dataTable.ext.buttons.excelHtml5.action.call(selfButton, e, dt, node, config);
						  dt.one('preXhr', function (e, s, data) {
							  settings._iDisplayStart = oldStart;
							  data.start = oldStart;
						  });
						  setTimeout(dt.ajax.reload, 0);
						  return false;
					  });
				  });
				  dt.ajax.reload();
				  }
		  }],
			initComplete: function(){
			  $('.dataTables_filter label input').attr('placeholder','Search');
			  $('.dataTables_filter label input').attr('id','search_area');
			  $('.dataTables_filter label input').unbind();
			  $("div.dataTables_filter input").on('keydown', function(e) {
				if (e.which == 13) {
				  var value = $("div.dataTables_filter input").val();
				  if(value)
				  {
				  value = value.replace('”','"');
				  value = value.replace("‘","'");
				  value = value.replace("’","'");
				  self.dTable.search(value).draw();
				}
				else
				{
					self.dTable.search('').draw();
				  }
				}
			  });
			  $("div.dataTables_filter input").on('keypress', function(e) {
				$(".searchBView").prop('disabled', false);
			  });
			  $("div.dataTables_filter input").on('keyup', function(e) {
				var value = $("div.dataTables_filter input").val();
				if(value)
				{
				}
				else
				$(".searchBView").prop('disabled', true);
			  });
			  $("div.searchButton")
			  .html('<button disabled="true" class="btn btn-sm btn-info searchBView" title="Search" type="submit">Search</button>'+
			  '<button style="margin-left:10px;" class="btn btn-sm btn-default resetBView" title="Reset" type="submit">Reset</button>');
			  var value =  $("div.dataTables_filter input").val();
			  if(value)
			  {
				$(".searchBView").prop('disabled', false);
			  }
			  $("div.dataTables_filter input").on('paste', function(event) {
				console.log("eeeeeeeeeeeeeeeeee",event);
				var element = this;
				var text ;
				setTimeout(function () {
				  text = $(element).val();
				  if(text)
				  {
					$(".searchBView").prop('disabled', false);
				  }
			  }, 100);
				});
				$(".buttons-collection").click(function(event) {
					setTimeout(function () {
					  if($(".buttons-collection").attr("aria-expanded") === "true" && $(".dt-button-collection").find("button").length == 0){
						$(".dt-button-collection").remove();
						$(".dt-button-background").remove();
						$(".buttons-collection").attr("aria-expanded","false");
					  }
					},500);
				});
			  },
			//data: this.groupList,
			ajax: function(dat, callback, settings) {
				let orderData;
				let searchText;
				let orderby;
				let limit;
				let startDate = '';
				let endDate = '';
				let label = '';

				console.log('searchData', dat);

				var i = dat.order[0].column ? dat.order[0].column : '';
				orderby = dat.order[0].dir ? dat.order[0].dir : '';
				if (isInteger(i)) {
					orderData = dat.columns[i].data ? dat.columns[i].data : '';
				} else {
					orderData = 'id';
				}
				let showFilterAppliedMessage = false;
				if (!self.isTableLoaded) {
					self.isTableLoaded = true;
					const searchStored = self.storeService.getStoredData(Store.SEARCH_VIEW_EDU_MATERIALS);
					const searchText = searchStored && searchStored.searchText ? searchStored.searchText : '';
					dat.search.value = searchText;
					if (searchText) {
						showFilterAppliedMessage = true;
					}
					setTimeout(() => {
					  const searchBox = $('.dataTables_filter input');
					  searchBox.val(searchText);
					}, 0);
				  }
				self._structureService.notifySearchFilterApplied(showFilterAppliedMessage);
				searchText = dat.search.value ? dat.search.value : '';
				self.storeService.storeData(Store.SEARCH_VIEW_EDU_MATERIALS, { searchText });

				console.log('ORDERRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRR');
				console.log(i);
				console.log(dat);
				console.log(orderData);
				console.log(orderby);
				console.log('ORDERRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRR');

				console.log(
					'searchData-fields',
					'i - ',
					i,
					'offset - ',
					dat.start,
					'orderby - ',
					orderby,
					'orderData - ',
					orderData,
					'searchText - ',
					searchText
				);
				if(self.selectedExport == true){
					startDate = self.toTimestamp(self.daterange.start);
					endDate = self.toTimestamp(self.daterange.end);
					label = self.daterange.label;
					self.selectedExport = false;
			}
				self._structureService
					.getAssignedEducationMaterialList(dat.length, dat.start, orderData, orderby, searchText,startDate,endDate)
					.then((resultData) => {
						self.dataLoadingMsg = false;
						datas = {};
						self.datam = {};
						if (dat.start == 0) {
							this.totalCt = resultData['getSessionTenant'].assignedMaterialPagination.totalCount;
						}
						datas = [];
						datas = resultData['getSessionTenant']
							? resultData['getSessionTenant']['assignedMaterialList']['data']
							: [];

						self.groupList = datas;
						let draw;
						let total;
						if (datas && datas.length == 0 && searchText == '') {
							draw = 0;
							total = 0;
						} else {
							draw = dat.draw;
							total = this.totalCt;
						}

						self.datam = {
							draw: draw,
							recordsTotal: total,
							recordsFiltered: total,
							aaData: datas
						};
						callback(self.datam);
					});
			},
			columns: [
				{ title: '', data: '' },
				{ title: 'Material Name', data: 'materialName' },
				{ title: 'Description', data: 'description' },
				{ title: 'File Name', data: 'fileName' },
				{ title: 'Created By', data: 'createdBy' },
				{ title: 'Created On', data: 'createdOn' },
				{ title: 'Updated On', data: 'updatedOn' },
				{ title: 'Actions' }
			],
			columnDefs: [
				{
					targets: 0,
					searchable: false,
					orderable: false,
					visible: false,
					className: 'dt-body-center',
					render: function(data, type, row) {
						return '';
					}
				},
				{
					data: null,
					targets: 1,
					width: '15%',
					render: function(data, type, row) {
						if (row.materialName) {
							return row.materialName;
						} else {
							return '';
						}
					}
				},
				{
					data: null,
					targets: 2,
					width: '15%',
					orderable: true,
					render: function(data, type, row) {
						if (row.description) {
							return row.description;
						} else {
							return '';
						}
					}
				},
				{
					data: null,
					targets: 3,
					width: '15%',
					orderable: true,
					render: function(data, type, row) {
						if (row.fileName) {
							return row.fileName;
						} else {
							return '';
						}
					}
				},
				{
					data: null,
					targets: 4,
					width: '15%',
					orderable: true,
					render: function(data, type, row) {
						if (row.displayName) {
							return row.displayName;
						} else {
							return '';
						}
					}
				},
				{
					data: null,
					targets: 5,
					width: '15%',
					orderable: true,
					//orderData: 5,
					render: function(data, type, row) {
						if (row.createdOn) {
							var datePipe = new DatePipe('en-US');
							return datePipe.transform(row.createdOn, 'MMM dd y hh:mm a');
						} else {
							return '';
						}
					}
				},
				{
					data: null,
					targets: 6,
					width: '15%',
					orderable: true,
					//orderData: 5,
					render: function(data, type, row) {
						if (row.updatedOn) {
							var datePipe = new DatePipe('en-US');
							return datePipe.transform(row.updatedOn, 'MMM dd y hh:mm a');
						} else {
							return '';
						}
					}
				},
				{
					data: null,
					orderable: false,
					render: function(data, type, row) {
						let actions = '';
						actions += `
						<a id="editgrp" href="javascript: void(0);" title="View Document" class="cat__core__link mr-3"><i id="view-file" data-view="" class="icmn-eye"></i> </a>
						`;
						return actions;
					},
					width: '10%',
					targets: 7
				}
			],
			language: {
				emptyTable: 'You do not have any education materials shared at this time.'
			},
			order: [ [ 6, 'desc' ] ]
		});

		$('#staff-list_filter label input').attr('id', 'search_area');
		$('#dtPDiscGroups').on('click', 'tr .child', function (e) {
			var parentRow = $(this).closest("tr").prev()[0];
			self.selectedData = self.dTable.row( parentRow ).data();
			self.fileUploadedTenantId = self.selectedData.tenantId;
});
		$(document).on('click', '.resetBView',(event)=> {
			self.dTable.search('').draw();
			$(".searchBView").prop('disabled', true);
		  });
		  $(document).on('click', '.searchBView',(event)=> {
		  var value = $('#dtPDiscGroups_wrapper #dtPDiscGroups_filter label input').val();
		  if(value)
		  {
		  value = value.replace('”','"');
		  value = value.replace("‘","'");
		  value = value.replace("’","'");
		  self.dTable.search(value).draw();
		  }
		  else
		  {
			self.dTable.search('').draw();
		  }
		   });
		   	});

	}

	showPdf(targetEvent): void {
		console.log(targetEvent);
    	this.loadingGroups = true;
		if (this.selectedData.fileName === '') {
			var notify = $.notify('Error! No such file found');
			setTimeout(function() {
				notify.update({
					type: 'warning',
					message: '<strong>Error! No such file found</strong>'
				});
			}, 1000);
		} else {
				this._structureService.pdfLoaded = false;
				this.pdfOrDocSrc = targetEvent;
				this.isModalOpen = true;
				this.cancelLabel = true;
				this.showIframe = true;
				this.modalBody = this.sanitizer.bypassSecurityTrustResourceUrl(`${this.pdfOrDocSrc}#navbar=0&navpanes=0`);
				this._structureService.pdfLoaded = true;
				this.modals.show();
		}
	}

	closeModal(): void {
		this.modals.hide();
		this.isModalOpen = false;
		this.modalBody = '';
		this.pdfOrDocSrc = '';
		this.pdfOrDocUrl = '';
		this.showIframe = false;
		this.cancelLabel = false;
	}
}
