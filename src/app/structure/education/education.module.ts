import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { BrowserModule } from '@angular/platform-browser';
import { ReactiveFormsModule } from '@angular/forms';
import { FormsModule } from '@angular/forms';
import { ColorPickerModule } from 'ngx-color-picker';
import { NgUploaderModule } from 'ngx-uploader';
import { EducationMaterialComponent } from './education-material-list.component';
import { AddEducationMaterialComponent } from './add-education-material.component';
import { UpdateEducationMaterialComponent } from './update-education-material.component';
import { ViewEducationMaterialComponent } from './view-education-material-list.component';
import { Modal } from './educationModal';
import { AuthGuard } from '../../guard/auth.guard';
import {
	MessageGroupSearchFilterPipe,
	SearchRoleFilterSchedulePipe,
	SearchFilterPipe,
	SearchGroupFilterSchedulePipe,
	SearchRoleFilterMsgGrpsPipe,
	SearchFilterRoleTreeViewPipe,
	pluralizeScheduleFilterPipe,
	SearchRoleFilterScheduleGroupsPipe
} from './messagegroup-search.pipes';
import { filterMessageGroupPipe } from './messagegroup-search.pipes';
import { Daterangepicker } from 'ng2-daterangepicker';

import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { SharedModule } from '../shared/sharedModule';

export const routes: Routes = [
	{ path: 'education/education-material-list', component: EducationMaterialComponent,canActivate:[AuthGuard],data:{checkRoutingPrivileges:'enableEducationTraining'} },
	{ path: 'education/add-education-material', component: AddEducationMaterialComponent,canActivate:[AuthGuard], data:{checkUserGroupPermission:'3'}},
	{ path: 'education/update-education-material', component: UpdateEducationMaterialComponent,canActivate:[AuthGuard],data:{checkRoutingPrivileges:'enableEducationTraining'} },
	{ path: 'education/view-education-material-list', component: ViewEducationMaterialComponent }
];

@NgModule({
	imports: [
		CommonModule,
		FormsModule,
		BrowserModule,
		ReactiveFormsModule,
		RouterModule.forChild(routes),
		NgUploaderModule,
		ColorPickerModule,
		Daterangepicker,
		NgbModule.forRoot(),
		SharedModule
	],
	declarations: [
		EducationMaterialComponent,
		AddEducationMaterialComponent,
		UpdateEducationMaterialComponent,
		ViewEducationMaterialComponent,
		Modal,
		MessageGroupSearchFilterPipe,
		SearchRoleFilterSchedulePipe,
		SearchRoleFilterScheduleGroupsPipe,
		SearchFilterPipe,
		filterMessageGroupPipe,
		SearchGroupFilterSchedulePipe,
		pluralizeScheduleFilterPipe,
		SearchFilterRoleTreeViewPipe,
		SearchRoleFilterMsgGrpsPipe
	],
	providers: [ Modal, MessageGroupSearchFilterPipe, filterMessageGroupPipe ],
	exports:[Modal]
})
export class EducationModule {}
