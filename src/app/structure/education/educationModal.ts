import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { StructureService } from './../../structure/structure.service';
// const template: string = require('./templates/commonModal.html');
declare var window;
@Component({
    selector: 'modal',
    templateUrl:'./templates/commonModal.html'
})

export class Modal implements OnInit {
    @Input('show-modal') showModal: boolean;
    @Input('show-header') showHeader: boolean;
    @Input('show-footer') showFooter: boolean;
    @Input('show-iframe') modalIframeBody: boolean;
    @Input('show-video') modalVideoBody: boolean;
    @Input('title') title: string;
    @Input('sub-title') subTitle: string;
    @Input('modal-body') modalBody: String = "";
    @Input('cancel-label') cancelLabel: Boolean;
    @Input('positive-label') positiveLabel: string;
    @Input('modal-retry') modalRetry: boolean = false;
    @Output('closed') closeEmitter: EventEmitter<ModalResult> = new EventEmitter<ModalResult>();
    @Output('loaded') loadedEmitter: EventEmitter<Modal> = new EventEmitter<Modal>();
    @Output() positiveLabelAction = new EventEmitter();
    
    iframeReloadTimeOut:any;
    iframeReloadInterval:any;
    iframeLoaded = false;
    faildToLoadDoc=false;    
    constructor(private _structureService: StructureService) { 
        window.oniframeloadlatest = ()=>{
            console.log('-------on iframe load---------')
            if(this.modalRetry){
                this.retryPdfIframeOnLoad()  
            }
            else{
                this.pdfIframeOnLoad();
            }    

        };
    }

    ngOnInit() {
        this.loadedEmitter.next(this);
    }

    show() {
        console.log("show............");
        this.showModal = true;      
        this._structureService.pdfIframeLoaded = false;
        this.iframeLoaded = false;
        if(this.iframeReloadInterval) {
            clearInterval(this.iframeReloadInterval)
        }
        if(this.iframeReloadTimeOut && this.modalRetry) {
            clearTimeout(this.iframeReloadTimeOut)
        }
    }

    hide() {
        this.showModal = false;
        this._structureService.pdfIframeLoaded = false;
        this.iframeLoaded = false;
        if(this.modalRetry){
            this.faildToLoadDoc = false;
            this._structureService.pdfLoaded = false;
            if(this.iframeReloadTimeOut) {
                clearTimeout(this.iframeReloadTimeOut)
            }
        }
        if(this.iframeReloadInterval) {
            clearInterval(this.iframeReloadInterval)
        }
        this.closeEmitter.next({
            action: ModalAction.POSITIVE
        });
    }

    positiveAction() {
        this.positiveLabelAction.next(this);
        return false;
    }

    cancelAction() {
        this.showModal = false;
        this._structureService.pdfIframeLoaded = false;
        this.iframeLoaded = false;
        if(this.modalRetry){
            this.faildToLoadDoc = false;
            this._structureService.pdfLoaded = false;
            if(this.iframeReloadTimeOut) {
                clearTimeout(this.iframeReloadTimeOut)
            }
        }
        if(this.iframeReloadInterval) {
            clearInterval(this.iframeReloadInterval)
        }
        this.closeEmitter.next({
            action: ModalAction.CANCEL
        });
        return false;
    }
    //Added iframe reload retry functionality
    retryPdfIframeOnLoad(retryCount=1, calledfrom='default') {
        if(this.iframeReloadInterval) {
            clearInterval(this.iframeReloadInterval)
        }
        if(this.iframeReloadTimeOut) {
            clearTimeout(this.iframeReloadTimeOut)
        }
        if(retryCount && !this.iframeLoaded) {
            if(this._structureService.pdfIframeLoaded) {
                this.iframeLoaded = true;
                this.faildToLoadDoc = false;               
                }
            var counter = ((retryCount == 1) ? 2 : 1);
            this.iframeReloadInterval = setInterval( ()=> {
                if(counter > 0) { 
                    if(this._structureService.pdfIframeLoaded) {
                        //counter greater than zero then iframe loaded
                        this._structureService.pdfIframeLoaded = false;
                        this.iframeLoaded = true;
                        this.faildToLoadDoc = false;                       
                        if(this.iframeReloadInterval) {
                            clearInterval(this.iframeReloadInterval)
                        }
                    }
                    counter--;
                } else {
                    this.reloadIframe();
                    this.retryPdfIframeOnLoad(retryCount-1, 'inner');    
                }
            },1000);
        } else {
            this.iframeReloadTimeOut = setTimeout( ()=> {
            if(retryCount == 0 && !this.iframeLoaded && !this._structureService.pdfLoaded){ 
                this.faildToLoadDoc = true;
                this.iframeLoaded = true;
                this._structureService.pdfLoaded = true;                
            }
        },4000)

           if(this._structureService.pdfIframeLoaded){
            // pdfIframeOnLoad -- stopped
            this._structureService.pdfIframeLoaded = false;
            this.iframeLoaded = true;
            this.faildToLoadDoc = false;            
            if(this.iframeReloadInterval) {
                clearInterval(this.iframeReloadInterval)
            }
            if(this.iframeReloadTimeOut) {
                clearTimeout(this.iframeReloadTimeOut)
            }
           }
           
        }
    }
    reloadIframe() {
        if(this.modalRetry){
            let iframe:any = document.getElementById('retryPdfIframe');
            if(iframe && "src" in iframe && iframe.src)
            iframe.src = iframe.src;
        }
        else{
            let iframe:any = document.getElementById('pdfIframe');
            if(iframe && "src" in iframe && iframe.src)
            iframe.src = iframe.src;  
        }    
    }
    pdfIframeOnLoad(retryCount=5, calledfrom='default') {
        if(this.iframeReloadInterval) {
            clearInterval(this.iframeReloadInterval)
        }
        if(retryCount && !this.iframeLoaded) {
            var counter = ((retryCount == 5) ? 2 : 5);
            this.iframeReloadInterval = setInterval( ()=> {
                if(counter > 0) {
                    if(this._structureService.pdfIframeLoaded) {
                       //counter greater than zero , then iframe loaded
                        this._structureService.pdfIframeLoaded = false;
                        this.iframeLoaded = true;                       
                        if(this.iframeReloadInterval) {
                            clearInterval(this.iframeReloadInterval)
                        }
                    }
                    counter--;
                } else {
                    this.reloadIframe();
                    this.pdfIframeOnLoad(retryCount-1, 'inner');    
                }
            },1000);
        } else {
            //pdfIframeOnLoad -- stopped
            this._structureService.pdfIframeLoaded = false;
            this.iframeLoaded = true;            
            if(this.iframeReloadInterval) {
                clearInterval(this.iframeReloadInterval)
            }
        }
    }
    retry(){        
        this.faildToLoadDoc = false;
        this._structureService.pdfLoaded = false;
        this.iframeLoaded = false;
        this.retryPdfIframeOnLoad(5,'default');
    }
}

export enum ModalAction { POSITIVE, CANCEL }

export interface ModalResult {
    action: ModalAction;
}