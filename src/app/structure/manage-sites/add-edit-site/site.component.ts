import { Compo<PERSON>, OnIni<PERSON>, Renderer, ElementRef } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { Router } from '@angular/router';
import { isBlank, setCountryCodeFlag } from 'app/utils/utils';
import { configTimeZone } from '../../../../environments/environment';
import { StructureService } from '../../structure.service';
import { SharedService } from '../../shared/sharedServices';
import { ManageSitesService } from '../manage-sites.service';
import { ToolTipService } from '../../tool-tip.service';
import { CONSTANTS } from '../../../constants/constants';

/* eslint-disable */
declare var $: any;
declare var NProgress: any;
declare var swal: any;
/* eslint-enable */

@Component({
  selector: 'app-site',
  templateUrl: '../add-edit-site/site.component.html',
  styleUrls: ['./site.component.css',],
})
export class SiteComponent implements OnInit {
  timezones: Array<{
    offset: string;
    city: string;
    name: string;
    isDst: string;
    current_offset: string;
  }>;
  siteActionHeader = 'Add Site';
  siteActionButton = 'Save';
  editSite = false;
  staffList: any = [];
  staffList1: any = [];
  staffList2: any = [];
  selectedStaff = [];
  selectedStaff1 = [];
  selectedStaff2 = [];
  site: FormGroup;
  userDetails = this._structureService.userDetails;
  userConfig = this._structureService.getUserdata().config;
  userData = JSON.parse(this.userDetails);
  StaffCount;
  selectedOptions;
  allBranchDays;
  timeZone;
  timeZoneData;
  identifier;
  filingCentersOut;
  filingCentersOutnew = [];
  documentCategories;
  documentCategoriesout;
  filingCentersIn;
  documentTypes;
  documentTypessout;

  documentCentersOut = [];
  documentCentersIn = [];

  documentTypesOut = [];
  documentTypesIn = [];

  multisite = false;
  FCselected = false;
  inbound = false;
  outbound = false;

  showClose;
  folderName;

  folderNamenotfy;
  showClosenotfy;
  folderNamedlink;
  showClosedlink;
  folderNamepnote;
  showClosepnote;
  folderNamephi;
  showClosephi;
  folderNamefddata;
  folderNamecvldata;
  showClosefddata;
  showClosecvldata;
  folderNameinbdoc;
  showCloseinbdoc;

  finfaxq;
  finnotification;
  finDirectLinking;
  finProgressNote;
  finPHI;
  finFormDiscreteData;
  finCvlData;
  fininbdoc;

  documentsfromFilingCenterFolderOut;
  documentsfromFilingCenterFolderIn;
  pnotinmode;
  docexmode;
  direct_link = 0;
  progress_note_integration = 0;
  disclose_PHI = 0;
  exchange_of_discrete_data = 0;
  cvl_integration = 0;
  email_notify = 0;

  DocumentsFCIn;
  DocFCIn = [];
  showPageLoader = false;
  crossTenantChangeSubscriber: any;
  buttonUser = 'Search';
  buttonUser1 = 'Search';
  buttonUser2 = 'Search';
  siteId = '0';
  selectedStaffEdit = [];
  selectedStaffEdit1 = [];
  notificationLanguages = [];
  timeZoneOf;
  tenantRoles;
  siteLogo: File;
  formData: FormData;
  fileSelected = false;
  imageUrl: string | ArrayBuffer;
  imageError: string;
  siteExternalData: any;
  hoursPatientPermittedToStartChat = false;
  activeSiteTab;
  activeSiteSubTab;
  siteName = '';
  constructor(
    private router: Router,
    public readonly structureService: StructureService,
    private _sharedService: SharedService,
    private _ToolTipService: ToolTipService,
    private _manageSitesService: ManageSitesService,
    private _formBuild: FormBuilder,
    renderer: Renderer,
    elementRef: ElementRef
  ) {
    this.notificationLanguages = CONSTANTS.notificationLanguages;
    this.timeZone = configTimeZone();
    this.activeSiteTab = 'siteDetails';
    this.activeSiteSubTab = 'incoming';
    this.inbound = true;
    $('#officeNumber').intlTelInput();
    $('#helplineNumber').intlTelInput();
    this.getTimeZones();
    this.siteId = this._structureService.getCookie('siteId');
    renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
      $('body').on('keypress', function (e) {
        var code = e.keyCode ? e.keyCode : e.which;
        if (code == 13) {
          return false;
        }
      });
      const eventClass = $(event.target).attr('data-flag');
      if (eventClass === 'remove') {
        this.removeSelectedStaff(event.target.id);
      }
      if (eventClass === 'remove1') {
        this.removeSelectedStaff1(event.target.id);
      }
      if (eventClass === 'remove2') {
        this.removeSelectedStaff2(event.target.id);
      }
      var idDetails = [
        'tagsInput',
        'recipient-li',
        'recipient-ul',
        'defaultInput',
        'recipient-li1',
        'recipient-ul1',
        'escalationInput',
        'recipient-li2',
        'recipient-ul2',
        'recipient-search1',
        'recipient-search2',
      ];
      if (
        !idDetails.includes(event.target.id) &&
        event.target.className.indexOf('recipient-li') === -1
      ) {
        const clear = false;
        let from = '';
        if ($('#recipient-ul').css('display') === 'block') {
          from = 'R';
        }
        if ($('#recipient-ul1').css('display') === 'block') {
          from = 'S';
        }
        if ($('#recipient-ul2').css('display') === 'block') {
          from = 'P';
        }
        this.enableOrDisableUiLI(false, clear, from);
      } else {
        if (
          event.target.id === 'tagInput' &&
          $('#recipient-ul').css('display') === 'block'
        ) {
          this.enableOrDisableUiLI(false, false, 'R');
        }
        if (
          event.target.id === 'defaultInput' &&
          $('#recipient-ul1').css('display') === 'block'
        ) {
          this.enableOrDisableUiLI(false, false, 'S');
        }
        if (
          event.target.id === 'escalationInput' &&
          $('#recipient-ul2').css('display') === 'block'
        ) {
          this.enableOrDisableUiLI(false, false, 'P');
        }
      }
    });
    this.crossTenantChangeSubscriber =
      this._sharedService.crossTenantChange.subscribe((onInboxData) => {
        this.ngOnInit();
      });
  }

  ngOnInit() {
    this.site = this._formBuild.group({
      regId: ['',],
      esi: ['',],
      name: [
        '',
        [
          Validators.required,
          Validators.pattern(/^[^<>]*$/),
          this.noWhitespaceValidator,
        ],
      ],
      address: ['',],
      officeEmail: new FormControl(
        {
          value: '',
          disabled: false,
        },
        [
          Validators.pattern(
            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          ),
        ]
      ),
      contactEmail: new FormControl(
        {
          value: '',
          disabled: false,
        },
        [
          Validators.pattern(
            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          ),
        ]
      ),
      officeCountryCode: [''],
      officePhoneCountryIsoCode: '',
      helplineCountryCode: [''],
      helplineCountryIsoCode: '',
      officePhoneNo: [
        '',
        Validators.compose([
          Validators.pattern(/^\d{10}$/),
          Validators.maxLength(10),
        ]),
      ],
      helplinePhoneNo: [
        '',
        Validators.compose([
          Validators.pattern(/^\d{10}$/),
          Validators.maxLength(10),
        ]),
      ],
      timezone: ['', Validators.required,],
      staffIds: ['', Validators.required,],
      staffIds1: ['',],
      staffIds2: ['',],
      branchDays: ['', Validators.required,],
      branchStart: ['', [Validators.required,],],
      branchStartTime: ['', [Validators.required,],],
      branchEnd: ['', [Validators.required,],],
      branchEndTime: ['', [Validators.required,],],
      status: ['',],
      working_hour: [0,],
      rolesDefault: ['',],
      rolesPdg: ['',],
      recipientEmailsForSupportWidget: [
        '',
        Validators.pattern(CONSTANTS.multiEmailValidationRegex),
      ],
      foutfaxq: ['',],
      foutnotification: ['',],
      foutDirectLinking: ['',],
      foutProgressNote: ['',],
      foutPHI: ['',],
      foutFormDiscreteData: ['',],
      foutCvlIntegration: [''],
      new_patient_chat_welcome_message: ['',],
      no_clinician_message_on_working_hours: ['', [Validators.required,],],
      chat_start_time: ['', [Validators.required,],],
      chat_start_period: ['', [Validators.required,],],
      chat_end_time: ['', [Validators.required,],],
      chat_end_period: ['', [Validators.required,],],
      enableSiteLevelAppBranding: ['0',],
      appName: ['',],
      appShortLink: ['',],
      emailFromMailingAddress: [
        '',
        [
          Validators.pattern(
            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          ),
        ],
      ],
      smtpDomain: ['',],
      supportWidgetEmailColorCode: ['',],
      forgotPasswordTemplate: ['',],
      notificationLanguage: CONSTANTS.defaultNotificationLanguage,
      magiclinkTokenExpirationTime: [''],
      magiclinkVerificationExpiryTime: [''],
      magiclinkVerificationTokenExpirationTime: ['']
    });
    this.allBranchDays = [
      { id: '1', name: 'Monday', },
      { id: '2', name: 'Tuesday', },
      { id: '3', name: 'Wednesday', },
      { id: '4', name: 'Thursday', },
      { id: '5', name: 'Friday', },
      { id: '6', name: 'Saturday', },
      { id: '0', name: 'Sunday', },
    ];
    this.timeZoneSelectScript();
    this.branchDaysSelectScript();
    this.staffListSelectScript();
    this.roleSelect();
    if (this.siteId && this.siteId != '0') {
      this.siteActionButton = 'Update';
      this.siteActionHeader = 'Edit Site';
      this.editSite = true;
      setTimeout(() => {
        this.editSiteFormSetting();
      }, 500);
    } else {
      this.siteActionButton = 'Save';
      this.siteActionHeader = 'Add Site';
      this.editSite = false;
    }
    var page = 'manage-site';
    $('.logoUpload').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'logoUpload'),
    });
    $('.manageSite1').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'manageSite1'),
    });
    $('.manageSite2').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'manageSite2'),
    });
    $('.manageSite3').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'manageSite3'),
    });
    $('.manageSite4').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'manageSite4'),
    });
    $('.manageSite5').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'manageSite5'),
    });
    $('.manageSite6').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'manageSite6'),
    });
    $('.manageSite7').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'manageSite7'),
    });
    $('.manageSite8').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'manageSite8'),
    });
    $('.manageSite9').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'manageSite9'),
    });
    $('.manageSite10').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'manageSite10'),
    });
    $('.manageSite11').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'manageSite11'),
    });
    $('.manageSite12').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'manageSite12'),
    });
    $('.manageSite13').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'manageSite13'),
    });
    $('.manageSite14').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'manageSite14'),
    });
    $('.manageSite15').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'manageSite15'),
    });
    $('.support-widget-emails__tooltip').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(
        page,
        'SUPPORT_WIDGET_RECIPIENT_EMAILS'
      ),
    });
    $('.startTime').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'startTime'),
    });
    $('.endTime').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'endTime'),
    });
    $('.esicode').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'esicode'),
    });

    $('.infilecenter1').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'infilecenter1'),
    });

    $('.outfilecenter1').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'outfilecenter1'),
    });
    $('.outfilecenter2').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'outfilecenter2'),
    });
    $('.outfilecenter3').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'outfilecenter3'),
    });
    $('.outfilecenter4').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'outfilecenter4'),
    });
    $('.outfilecenter5').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'outfilecenter5'),
    });
    $('.outfilecenter6').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'outfilecenter6'),
    });
    $('.outfilecentercvl').tooltip({
      html: true,
      title: this._ToolTipService.getToolTip(page, 'outfilecentercvl'),
    });
    $('#rolesDefault').select2({
      placeholder() {
        $(this).data('placeholder');
      }
    });
    $('#rolesPdg').select2({
      placeholder() {
        $(this).data('placeholder');
      }
    });
    $('#officeNumber').intlTelInput();
    $('#officeNumber').on('countrychange', (e, countryData) => {
      if (countryData.dialCode) {
        this.site.patchValue({
          officeCountryCode: `+${countryData.dialCode}`,
          officePhoneCountryIsoCode: countryData.iso2
        });
        $('#officeNumber').val(`+${this.site.controls.officeCountryCode.value}`);
      }
    });

    $('#helplineNumber').intlTelInput();
    $('#helplineNumber').on('countrychange', (e, countryData) => {
      if (countryData.dialCode) {
        this.site.patchValue({
          helplineCountryCode: `+${countryData.dialCode}`,
          helplineCountryIsoCode: countryData.iso2
        });
        $('#helplineNumber').val(`+${this.site.controls.helplineCountryCode.value}`);
      }
    });
  }
  ngOnDestroy() {
    if (this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
  }
  togglePreference(preference, value) {
    if (preference === 'working_hour') {
      this.site.patchValue({
        working_hour: value,
      });
      if (value == 1) {
        this.hoursPatientPermittedToStartChat = false;
      } else {
        this.hoursPatientPermittedToStartChat = true;
      }
    } else if (preference === 'appSettings') {
      this.site.patchValue({
        enableSiteLevelAppBranding: value,
      });
      this.setValidationForAppSettingsFields(value === '1' ? true : false);
    }
  }
  roleSelect() {
    this._manageSitesService.getAllTenantRoles().then((data) => {
      this.tenantRoles = data;
      this.tenantRoles = this.tenantRoles.filter((data1) => {
        if (data1.citus_role_id != '3') {
          return true;
        }
      });
    });
  }

  fileChange(event) {
    this.imageError = '';
    const allowedImageFileTypes = ['png', 'jpg', 'jpeg', 'JPG', 'JPEG', 'PNG',];
    const fileList = event.target.files;
    const max_size = 102400;
    const getFileExt = event.target.files[0].name
      .substring(event.target.files[0].name.lastIndexOf('.') + 1)
      .toLowerCase();
    const pos = allowedImageFileTypes.indexOf(getFileExt);
    if (pos < 0) {
      this.imageError = 'Only Images are allowed (png, jpg, jpeg, JPG, JPEG)';
    } else if (event.target.files[0].size > max_size) {
      this.imageError = 'Maximum size allowed is 100 Kb';
    } else {
      this.fileSelected = true;
      this.siteLogo = fileList[0];
      this.formData = new FormData();
      this.formData.append('id', this.siteId); //site id
      this.formData.append('logo', this.siteLogo); // file
      const reader = new FileReader();
      reader.readAsDataURL(this.siteLogo);
      reader.onload = (event) => {

        // TODO : Dimension validations.
        const image = new Image();
        this.imageUrl = reader.result;
      };
    }
  }
  editSiteFormSetting() {
    console.log('user details', this.userData.config);

    this.pnotinmode = this.userData.config.progress_note_integration_mode;
    this.docexmode = this.userData.config.documet_exchange_mode;

    this.direct_link = this.userData.config.enable_direct_link;
    this.progress_note_integration =
      this.userData.config.enable_progress_note_integration;
    this.disclose_PHI = this.userData.config.enable_disclose_PHI;

    this.exchange_of_discrete_data =
      this.userData.config.enable_exchange_of_discrete_data;
    this.cvl_integration = this.userData.config.enable_clinical_visit_log_integration;
    this.email_notify = this.userData.config.enable_email_notify;

    console.log('direct_link', this.direct_link);
    console.log('direct_link', this.progress_note_integration);
    console.log('direct_link', this.disclose_PHI);

    //this.docexmode='both';
    console.log('pnotemode', this.pnotinmode);
    console.log('dexchangemode', this.docexmode);
    var self = this;
    var guid;
    var branchDays = [];
    this._manageSitesService
      .getExternalIntegration(this.siteId)
      .then((data: any) => {
        this.siteExternalData = data.siteExternalIntegrations;
      });
    this._manageSitesService.getSiteDetails(this.siteId, '', true).then((data: any) => {
      if (data && data.getSiteDetails) {
        const result = data.getSiteDetails;
        this.siteName = !isBlank(result.name) ? result.name : '';

        const configsvlaues = data.getSiteDetails.configs;

        console.log('site-configus', configsvlaues);

        configsvlaues.forEach(function (fcvalue) {
          console.log('FC-configus ************', fcvalue);

          if (fcvalue.key === 'default_outgoing_filing_center_faxq') {
            self.finfaxq = fcvalue.value;
          }

          if (fcvalue.key === 'default_outgoing_filing_center_notification') {
            self.finnotification = fcvalue.value;
          }
          if (fcvalue.key === 'default_outgoing_filing_center_directlinking') {
            self.finDirectLinking = fcvalue.value;
          }

          if (fcvalue.key === 'default_outgoing_filing_center_progressnote') {
            self.finProgressNote = fcvalue.value;
          }

          if (fcvalue.key === 'default_outgoing_filing_center_phi') {
            self.finPHI = fcvalue.value;
          }

          if (
            fcvalue.key === 'default_outgoing_filing_center_formdiscretedata'
          ) {
            self.finFormDiscreteData = fcvalue.value;
          }
          if (
            fcvalue.key === 'default_outgoing_filing_center_cvl'
          ) {
            self.finCvlData = fcvalue.value;
          }
          if (fcvalue.key == 'default_inbound_fc') {
            self.fininbdoc = fcvalue.value;
          }
        });

        guid = result.registrationId;
        const workingDays = result.workingDays
          ? result.workingDays.split(',')
          : result.workingDays;
        if (workingDays) {
          workingDays.forEach((element) => {
            var types = {
              id: '',
            };
            types.id = element;
            branchDays.push(types);
          });
        }
        this.selectedStaffEdit = result.data;
        let siteStartPeriod = '';
        let siteEndPeriod = '';
        let siteEndTime = '';
        let siteStartTime = '';
        if (result.endTime) {
          if (result.endTime.indexOf('AM') === -1) {
            siteEndPeriod = 'PM';
            siteEndTime = result.endTime.replace('PM', '').replace(/ /g, '');
          } else {
            siteEndPeriod = 'AM';
            siteEndTime = result.endTime.replace('AM', '').replace(/ /g, '');
          }
        }
        if (result.startTime) {
          if (result.startTime.indexOf('AM') === -1) {
            siteStartPeriod = 'PM';
            siteStartTime = result.startTime
              .replace('PM', '')
              .replace(/ /g, '');
          } else {
            siteStartTime = result.startTime
              .replace('AM', '')
              .replace(/ /g, '');
            siteStartPeriod = 'AM';
          }
        }
        let new_patient_chat_welcome_message;
        let no_clinician_message_on_working_hours;
        let working_hour;
        let chat_end_period = '';
        let chat_end_time = '';
        let chat_start_period = '';
        let chat_start_time = '';
        let default_clinician_roles_available;
        let default_escalation_users;
        let staff_escalation_members;
        let member_roles_for_pdg_which_create_automatically;
        let recipientEmailsForSupportWidget: string;
        let site_external_system_integration;

        let foutfaxq;
        let foutnotification;
        let foutDirectLinking;
        let foutProgressNote;
        let foutPHI;
        let foutFormDiscreteData;
        let foutCvlIntegration;
        let appShortLink = '';
        let enableSupportWidgetBranding = '0';
        let supportWidgetFromEmail = '';
        let appName = '';
        let smtpDomain = '';
        let supportWidgetEmailColorCode = '';
        let forgotPasswordTemplate = '';

        if (result && result.configs) {
          result.configs.forEach(function (config) {
            if (config.key === 'new_patient_chat_welcome_message')
              new_patient_chat_welcome_message =
                config && config.value ? config.value : '';
            if (config.key === 'no_clinician_message_on_working_hours')
              no_clinician_message_on_working_hours =
                config && config.value ? config.value : '';
            if (config.key === 'chat_start_time')
              chat_start_time = config && config.value ? config.value : '';
            if (config.key === 'chat_start_period')
              chat_start_period = config && config.value ? config.value : '';
            if (config.key === 'chat_end_time')
              chat_end_time = config && config.value ? config.value : '';
            if (config.key === 'chat_end_period')
              chat_end_period = config && config.value ? config.value : '';
            if (config.key === 'working_hour')
              working_hour = config && config.value ? config.value : 0;
            if (config.key === 'default_clinician_roles_available')
              default_clinician_roles_available =
                config && config.value ? config.value : '';
            if (
              config.key === 'member_roles_for_pdg_which_create_automatically'
            )
              member_roles_for_pdg_which_create_automatically =
                config && config.value ? config.value : '';
            if (config.key === 'default_escalation_users') {
              default_escalation_users =
                config && config.data ? config.data : '';
            }
            if (config.key === 'staff_escalation_members') {
              staff_escalation_members =
                config && config.data ? config.data : '';
            }
            if (config.key === 'site_external_system_integration') {
              site_external_system_integration =
                config && config.value ? config.value : '';
            }
            if (config.key === 'default_outgoing_filing_center_faxq') {
              foutfaxq = config && config.data ? config.data : '';
            }
            if (config.key === 'default_outgoing_filing_center_notification') {
              foutnotification = config && config.data ? config.data : '';
            }
            if (config.key === 'default_outgoing_filing_center_directlinking') {
              foutDirectLinking = config && config.data ? config.data : '';
            }
            if (config.key === 'default_outgoing_filing_center_progressnote') {
              foutProgressNote = config && config.data ? config.data : '';
            }
            if (config.key === 'default_outgoing_filing_center_phi') {
              foutPHI = config && config.data ? config.data : '';
            }
            if (
              config.key === 'default_outgoing_filing_center_cvl'
            ) {
              foutCvlIntegration = config && config.data ? config.data : '';
            }
            if (
              config.key === 'default_outgoing_filing_center_formdiscretedata'
            ) {
              foutFormDiscreteData = config && config.data ? config.data : '';
            }
            if (config.key === 'app_short_link') {
              appShortLink = config && config.value ? config.value : '';
            } else if (config.key === 'enable_support_widget_branding') {
              enableSupportWidgetBranding =
                config && config.value && config.value === '1' ? '1' : '0';
            } else if (config.key === 'support_widget_from_email') {
              supportWidgetFromEmail =
                config && config.value ? config.value : '';
            } else if (config.key === 'app_name') {
              appName = config && config.value ? config.value : '';
            } else if (config.key === 'SMTP_domain') {
              smtpDomain = config && config.value ? config.value : '';
            } else if (config.key === 'support_widget_email_color_code') {
              supportWidgetEmailColorCode =
                config && config.value ? config.value : '';
            } else if (config.key === 'recipient_email_for_support_widget') {
              recipientEmailsForSupportWidget =
                config && config.value ? config.value : '';
            } else if (config.key === 'realm_key') {
              forgotPasswordTemplate =
                config && config.value ? config.value : '';
            } else if (config.key === 'notification_language'){
              self.site.patchValue({notificationLanguage : 
              config && config.value ? config.value : CONSTANTS.defaultNotificationLanguage});
            } else if (config.key === 'magiclink_token_expiration_time'){
              self.site.patchValue({magiclinkTokenExpirationTime : 
              config && config.value ? config.value : ''});
            } else if (config.key === 'magiclink_verification_expiry_time'){
              self.site.patchValue({magiclinkVerificationExpiryTime : 
              config && config.value ? config.value : ''});
            } else if (config.key === 'magiclink_verification_token_expiration_time'){
              self.site.patchValue({magiclinkVerificationTokenExpirationTime : 
              config && config.value ? config.value : ''});
            }
          });
        }
        this.selectedStaffEdit1 = default_escalation_users;

        // this.selectedStaffEdit2 = staff_escalation_members;
        this.site.patchValue({
          regId: result.registrationId,
          name: result.name,
          address: result.Address,
          officeEmail: result.officeEmail,
          contactEmail: result.contactEmail,
          officePhoneNo: result.officePhone,
          helplinePhoneNo: result.helplinePhone,
          officeCountryCode: !isBlank(result.officePhoneCountryCode)
            ? result.officePhoneCountryCode
            : '+1',
          helplineCountryCode: !isBlank(result.helplineCountryCode)
            ? result.helplineCountryCode
            : '+1',
          timezone: result.siteTimeZone,
          staffIds: this.selectedStaffEdit ? this.selectedStaffEdit.join() : '',
          staffIds1: default_escalation_users
            ? default_escalation_users.join()
            : '',

          // staffIds2: (staff_escalation_members)?staff_escalation_members.join():"",
          branchDays: branchDays ? branchDays.map((a) => a.id) : [],
          status: result.status,
          branchStartTime: siteStartPeriod,
          branchEndTime: siteEndPeriod,
          branchStart: siteStartTime,
          branchEnd: siteEndTime,
          new_patient_chat_welcome_message: new_patient_chat_welcome_message,
          no_clinician_message_on_working_hours:
            no_clinician_message_on_working_hours,
          working_hour: working_hour ? working_hour : 0,
          chat_end_period: chat_end_period,
          chat_end_time: chat_end_time,
          chat_start_period: chat_start_period,
          chat_start_time: chat_start_time,
          rolesDefault: default_clinician_roles_available
            ? default_clinician_roles_available.split(',')
            : [],
          rolesPdg: member_roles_for_pdg_which_create_automatically
            ? member_roles_for_pdg_which_create_automatically.split(',')
            : [],
          //recipientEmailsForSupportWidget: recipientEmailsForSupportWidget,
          esi: site_external_system_integration,
          foutfaxq: foutfaxq,
          foutnotification: foutnotification,
          foutDirectLinking: foutDirectLinking,
          foutProgressNote: foutProgressNote,
          foutPHI: foutPHI,
          foutCvlIntegration: foutCvlIntegration,
          foutFormDiscreteData: foutFormDiscreteData,
          appShortLink: appShortLink,
          enableSiteLevelAppBranding: enableSupportWidgetBranding,
          emailFromMailingAddress: supportWidgetFromEmail,
          appName: appName,
          smtpDomain: smtpDomain,
          supportWidgetEmailColorCode: supportWidgetEmailColorCode,
          forgotPasswordTemplate: forgotPasswordTemplate,
        });
        
        this.setValidationForAppSettingsFields(
          enableSupportWidgetBranding === '1' ? true : false
        );
        if (
          this.site.controls.working_hour.value == 1 ||
          this.site.controls.working_hour.value == true ||
          this.site.controls.working_hour.value == 'true'
        ) {
          this.hoursPatientPermittedToStartChat = false;
        } else {
          this.hoursPatientPermittedToStartChat = true;
        }
        // Handle the phone number values
        const officeCountry = setCountryCodeFlag('officeNumber', result.officePhoneCountryCode, result.officePhoneCountryIsoCode);
        this.site.patchValue({
          officeCountryCode: officeCountry.countryCode,
          officePhoneCountryIsoCode: officeCountry.countryIsoCode
        });

        const helplineCountry = setCountryCodeFlag('helplineNumber', result.helplineCountryCode, result.helplineCountryIsoCode);
        this.site.patchValue({
          helplineCountryCode: helplineCountry.countryCode,
          helplineCountryIsoCode: helplineCountry.countryIsoCode
        });

        if (this.selectedStaffEdit) {
          this.selectedStaffEdit.forEach((element) => {
            this.selectedStaff.push(element.userId.toString());
          });
        }
        if (this.selectedStaffEdit1) {
          this.selectedStaffEdit1.forEach((element) => {
            this.selectedStaff1.push(element.userId.toString());
          });
        }

        /* if(this.selectedStaffEdit2){
          this.selectedStaffEdit2.forEach(element => {
            this.selectedStaff2.push(element.userId.toString());
          });
          } */
        if (result.logo) {
          this.fileSelected = true;
          this.imageUrl =
            this._structureService.apiBaseUrl +
            'citus-health/site-logos/' +
            result.logo;
        } else {
          this.fileSelected = false;
        }
        if ($('#site_timezone').val()) {
          setTimeout(() => {
            self.site.patchValue({
              timezone: $('#site_timezone').val(),
            });
          }, 500);
        }
        $(document).ready(() => {
          $('#site_timezone').val($('#site_timezone').val()).trigger('change');
          $('#branchDays').val($('#branchDays').val()).trigger('change');
          $('#sfilingCenterss').on('change', (e) => {
            console.log($('#sfilingCenterss').val());
            self.showClose = true;
            this.FCselected = true;

            this.folderName = $('#sfilingCenterss').val();
            console.log(this.folderName);
          });

          $('#sfilingCenterssnotfy').on('change', (e) => {
            console.log($('#sfilingCenterssnotfy').val());
            this.showClosenotfy = true;
            this.FCselected = true;

            this.folderNamenotfy = $('#sfilingCenterssnotfy').val();
            console.log(this.folderNamenotfy);
          });

          $('#sfilingCenterssdlink').on('change', (e) => {
            console.log($('#sfilingCenterssdlink').val());
            this.showClosedlink = true;
            this.FCselected = true;

            this.folderNamedlink = $('#sfilingCenterssdlink').val();
            console.log(this.folderNamedlink);
          });

          $('#sfilingCentersspnote').on('change', (e) => {
            console.log($('#sfilingCentersspnote').val());
            this.showClosepnote = true;
            this.FCselected = true;

            this.folderNamepnote = $('#sfilingCentersspnote').val();
            console.log(this.folderNamepnote);
          });

          $('#sfilingCenterssphi').on('change', (e) => {
            console.log($('#sfilingCenterssphi').val());
            this.showClosephi = true;
            this.FCselected = true;

            this.folderNamephi = $('#sfilingCenterssphi').val();
            console.log(this.folderNamephi);
          });

          $('#sfilingCenterssfddata').on('change', (e) => {
            console.log($('#sfilingCenterssfddata').val());
            this.showClosefddata = true;
            this.FCselected = true;

            this.folderNamefddata = $('#sfilingCenterssfddata').val();
            console.log(this.folderNamefddata);
          });

          $('#sfilingCentercvl').on('change', (e) => {
            this.showClosecvldata = true;
            this.FCselected = true;

            this.folderNamecvldata = $('#sfilingCentercvl').val();
          });

          $('#sfilingCenterssinbdoc').on('change', (e) => {
            console.log($('#sfilingCenterssinbdoc').val());
            this.showCloseinbdoc = true;
            this.FCselected = true;
            this.folderNameinbdoc = $('#sfilingCenterssinbdoc').val();
            console.log(this.folderNameinbdoc);
          });

          $(document).on('change', '.dcupdate', function (e) {
            console.log($(this).val());
            console.log('name1', $(this).attr('name'));
            var keyname = $(this).attr('name');
            var valuename = $(this).val();
            self.keyvaluedocumentCenter(keyname, valuename);
          });

          $(document).on('change', '.dtupdate', function (e) {
            console.log($(this).val());
            console.log('name1', $(this).attr('name'));
            var keyname = $(this).attr('name');
            var valuename = $(this).val();
            self.keyvaluedocumentType(keyname, valuename);
          });

          $('.select2').select2({
            placeholder: 'Select',
          });
        });
        if (result.siteTimeZone && result.siteTimeZone != '') {
          this.timeZoneOf = result.siteTimeZone;
        } else {
          this.timeZoneOf = new Date().getTimezoneOffset() * -1;
        }

        //console.log("user",this.userData.config.enable_multisite);
        if (this.userData.config.enable_multisite == 1) {
          self.multisite = true;

          self._manageSitesService.getsiteFcMappings(guid).then((data: any) => {
            var arrsFC = [];
            self.showPageLoader = true;
            self.DocumentsFCIn = data.siteFcMappings;
            if (self.DocumentsFCIn.length > 0) {
              for (let i = 0; i < self.DocumentsFCIn.length; i++) {
                var site = self.DocumentsFCIn[i].siteRegistrationId;

                var id = self.DocumentsFCIn[i].refId;
                var type = self.DocumentsFCIn[i].refType;
                var folder = self.DocumentsFCIn[i].folder;

                /* console.log("data from all  site::", site);
                   console.log("data from all type::", id);
                   console.log("data from all folder::", type);
                   console.log("data from all id::", folder); */

                if (site === guid) {
                  var items = {
                    id: id,
                    type: type,
                    folder: folder,
                  };
                  self.DocFCIn.push(items);
                }
              }
            }
            if (!this.isFilingCenterEnabledForSiteEdit) {
              // document exchange mode is webhook and enable_document_management is ON. Then no filing center involved.
              this.showPageLoader = false;
            } else {
              this.getAllsiteTenantFolders(guid);
              if (this.structureService.isDocumentCategoryEnabled) {
                this.getAllDocumentCategories(guid);
              }
              if (this.isIncomingFCForDocTypeEnabled) {
                this.getAllDocumentTypes(guid);
              }
            }
          });
        }
      }
    });
  }
  // eslint-disable-next-line no-underscore-dangle
  get _structureService() {
    return this.structureService;
  }
  get isFilingCenterEnabledForSiteEdit() {
    return (
      this.structureService.isFilingCenterEnabled &&
      !(
        this.structureService.isDocumentManagementEnabled &&
        this.userConfig.documet_exchange_mode === 'wh' &&
        !this.structureService.isDocumentCategoryEnabled
      )
    );
  }
  get isIncomingFCForDocTypeEnabled() {
    return !this.structureService.isDocumentManagementEnabled && this.structureService.isFilingCenterEnabled;
  }
  showApiGenericMessage() {
    this.showPageLoader = false;
    this.structureService.showApiGenericMessage();
  }

  getAllDocumentCategories(guid) {
    this.showPageLoader = true;
    this._structureService.getDocumentTagtype().then((data: any) => {
      var arry = [];
      var arrynew = [];
      var idname;
      var folderval;
      this.documentCategoriesout = data.getSessionTenant.documentTagTypes;
      if (this.documentCategoriesout.length > 0) {
        for (let i = 0; i < this.documentCategoriesout.length; i++) {
          var oldval = '';
          var closeval = false;
          var name = this.documentCategoriesout[i].typeName;
          var code = this.documentCategoriesout[i].typeCode;
          var itemid = this.documentCategoriesout[i].id;
          var ids = 'incomeing' + i;

          console.log('Document categories selected', this.DocFCIn);
          console.log('Document categories guid', guid);

          this.DocFCIn.forEach((element) => {
            if (itemid == element.id && element.type == 2) {
              oldval = element.folder;
            }

            if (oldval == '' || oldval == null) {
              closeval = false;
            } else {
              closeval = true;
            }
          });

          var items = {
            id: ids,
            text: code,
            values: oldval,
            showClose: closeval,
            name: name,
          };
          arry.push(items);

          var saves = {
            key: ids,
            cvalue: oldval,
            nvalue: oldval,
            name: name,
            code: code,
            position: itemid,
          };
          arrynew.push(saves);
        }
        this.documentCategories = arry;
        this.documentCentersOut = arrynew;

        console.log('Document categoriesout  inside', this.documentCentersOut);
        console.log('Document categories inside', this.documentCategories);

        var typess = 'INCOMING';
        this._manageSitesService
          .getSiteTenantFilingCenterFolders(typess, guid, false)
          .then((data: any) => {
            if (data.getSiteTenantFilingCenterFolders == null) {
              console.log('Document categories CATCH  inside');
              $('#sfilingCenterssinbdoc').select2({
                allowClear: true,
                placeholder: 'Select Incoming Filing Center',
              });
              for (let j = 0; j < this.documentCategories.length; j++) {
                idname = '#' + this.documentCategories[j].id;
                folderval = this.documentCategories[j].values;

                //console.log("Document categories incoming idname "+idname);
                $(idname).select2({
                  allowClear: true,
                  placeholder: 'Select Incoming Filing Center',
                });
              }
            this.showApiGenericMessage();
            } else {
              console.log('data from  CATEGORIES::check correct');
              var arrs = [];
              console.log('data from  CATEGORIES::', data);
              this.documentsfromFilingCenterFolderIn =
                data.getSiteTenantFilingCenterFolders;
              if (this.documentsfromFilingCenterFolderIn.length > 0) {
                for (
                  let i = 0;
                  i < this.documentsfromFilingCenterFolderIn.length;
                  i++
                ) {
                  var fname =
                    this.documentsfromFilingCenterFolderIn[i].folderName;

                  arrs.push(fname);
                }
            } else {
              this.showPageLoader = false;
            }
              console.log('FC center array++' + arrs);
              this.filingCentersIn = arrs;

              $('#sfilingCenterssinbdoc').select2({
                allowClear: true,
                placeholder: 'Select incoming Filing Center',
                data: this.filingCentersIn,
              });
              if (this.fininbdoc != '') {
                $('#sfilingCenterssinbdoc')
                  .val(this.fininbdoc)
                  .trigger('change');
              }
              this.folderNameinbdoc = this.fininbdoc;
              for (let j = 0; j < this.documentCategories.length; j++) {
                idname = '#' + this.documentCategories[j].id;
                folderval = this.documentCategories[j].values;
                console.log('Document categories incoming idname ' + idname);
                console.log('Document categories incoming idname ' + folderval);
                $(idname).select2({
                  allowClear: true,
                  placeholder: 'Select Incoming Filing Center',
                  data: this.filingCentersIn,
                });
                $(idname).val(folderval)
.trigger('change');
              }
            }
          });
      } else {
        this.showPageLoader = false;
      }
    });
  }
  getAllDocumentTypes(guid) {
    this.showPageLoader = true;
    this.structureService.getDocumentTagList().subscribe({
      next: ({ data }) => {
        const arrys = [];
        const arrysnew = [];
        let idnames;
        this.documentTypessout =
          (data && data.getSessionTenant && data.getSessionTenant.documentTags && data.getSessionTenant.documentTags.data) || [];
      if (this.documentTypessout.length > 0) {
        for (let i = 0; i < this.documentTypessout.length; i++) {
          var oldval = '';
          var name = this.documentTypessout[i].tagName;
          var itemid = this.documentTypessout[i].id;
          var ids = 'incomeingtype' + itemid;
          var closeval = false;
          this.DocFCIn.forEach((element) => {
            if (itemid == element.id && element.type == 1) {
              oldval = element.folder;
            }
          });

          if (oldval == '' || oldval == null) {
            closeval = false;
          } else {
            closeval = true;
          }

          var items = {
            id: ids,
            text: name,
            values: oldval,
            showCloses: closeval,
            name: name,
          };
          arrys.push(items);

          var saves = {
            key: ids,
            cvalue: oldval,
            nvalue: oldval,
            name: name,
            position: itemid,
          };
          arrysnew.push(saves);
        }
          this.documentTypes = arrys;
          this.documentTypesOut = arrysnew;
          const typess = 'INCOMING';
        this._manageSitesService
          .getSiteTenantFilingCenterFolders(typess, guid, false)
          .then((data: any) => {
            if (data.getSiteTenantFilingCenterFolders == null) {
              console.log('Document type CATCH  inside');
              $('#sfilingCenterssinbdoc').select2({
                delay:1000,
                allowClear: true,
                placeholder: 'Select Incoming Filing Center',
              });
              for (let j = 0; j < this.documentTypes.length; j++) {
                idnames = '#' + this.documentTypes[j].id;

                console.log('Document categories incoming idname ' + idnames);
                $(idnames).select2({
                  allowClear: true,
                  placeholder: 'Select Incoming Filing Center',
                });
              }
              this.showApiGenericMessage();
            } else {
              var arrs = [];
              console.log('data from ::', data);
              this.documentsfromFilingCenterFolderIn =
                data.getSiteTenantFilingCenterFolders;
              if (this.documentsfromFilingCenterFolderIn.length > 0) {
                for (
                  let i = 0;
                  i < this.documentsfromFilingCenterFolderIn.length;
                  i++
                ) {
                  var fname =
                    this.documentsfromFilingCenterFolderIn[i].folderName;

                  arrs.push(fname);
                }
              } else {
                this.showPageLoader = false;
              }
              console.log('FC center array++' + arrs);
              this.filingCentersIn = arrs;

              console.log('document center array++' + arrys);
              console.log(
                'document center Filing centers inside++' + this.filingCentersIn
              );

              console.log(
                'document center Filing centers type  inside++' +
                  this.documentTypes
              );
              for (let j = 0; j < this.documentTypes.length; j++) {
                idnames = '#' + this.documentTypes[j].id;
                console.log('Document categories incoming idname ' + idnames);
                $(idnames).select2({
                  allowClear: true,
                  placeholder: 'Select Incoming Filing Center',
                  data: this.filingCentersIn,
                });
              }
              this.documentTypes.forEach((element) => {
                if ($('#' + element.id)) {
                  $('#' + element.id).val(element.values);
                  $('#' + element.id).trigger('change');
                }
              });
              $('#sfilingCenterssinbdoc').select2({
                delay:1000,
                allowClear: true,
                placeholder: 'Select Incoming Filing Center',
                data: this.filingCentersIn,
              });
              if (this.fininbdoc != '') {
                $('#sfilingCenterssinbdoc')
                  .val(this.fininbdoc)
                  .trigger('change');
              }
              this.folderNameinbdoc = this.fininbdoc;
            }
          });
        } else {
          this.showPageLoader = false;
        }
      }
    });
  }

  getAllsiteTenantFolders(guid) {
    this.showPageLoader = true;
    console.log('here filing centere calls here ');
    var typess = 'OUTGOING';
    this._manageSitesService
      .getSiteTenantFilingCenterFolders(typess, guid)
      .then((data: any) => {
        if (data.getSiteTenantFilingCenterFolders == null) {
          $('#sfilingCenterss').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
          });
          $('#sfilingCenterssnotfy').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
          });
          $('#sfilingCenterssdlink').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
          });
          $('#sfilingCentersspnote').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
          });
          $('#sfilingCenterssphi').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
          });
          $('#sfilingCenterssfddata').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
          });
          $('#sfilingCentercvl').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
          });
        this.showApiGenericMessage();
        } else {
          console.log('data from site folders all::', data);
          var arr = [];
          console.log('data from ::', data);
          this.documentsfromFilingCenterFolderOut =
            data.getSiteTenantFilingCenterFolders;
          if (this.documentsfromFilingCenterFolderOut.length > 0) {
            for (
              let i = 0;
              i < this.documentsfromFilingCenterFolderOut.length;
              i++
            ) {
              var fname = this.documentsfromFilingCenterFolderOut[i].folderName;
              var ftype = this.documentsfromFilingCenterFolderOut[i].type;

              /* var item = {
                   id: fname,
                   text: fname
                 } */
              arr.push(fname);
            }
        } else {
          this.showPageLoader = false;
        }

          console.log('new array ::' + arr);
          this.filingCentersOut = arr;

          console.log('lengthsssssssssssssssss', this.filingCentersOut);

          $('#sfilingCenterss').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
            data: this.filingCentersOut,
          });
          if (this.finfaxq != '') {
            $('#sfilingCenterss').val(this.finfaxq)
.trigger('change');
          }
          this.folderName = this.finfaxq;

          $('#sfilingCenterssnotfy').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
            data: this.filingCentersOut,
          });
          if (this.finnotification != '') {
            $('#sfilingCenterssnotfy')
              .val(this.finnotification)
              .trigger('change');
          }
          this.folderNamenotfy = this.finnotification;

          $('#sfilingCenterssdlink').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
            data: this.filingCentersOut,
          });
          if (this.finDirectLinking != '') {
            $('#sfilingCenterssdlink')
              .val(this.finDirectLinking)
              .trigger('change');
          }
          this.folderNamedlink = this.finDirectLinking;

          $('#sfilingCentersspnote').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
            data: this.filingCentersOut,
          });
          if (this.finProgressNote != '') {
            $('#sfilingCentersspnote')
              .val(this.finProgressNote)
              .trigger('change');
          }
          this.folderNamepnote = this.finProgressNote;

          $('#sfilingCenterssphi').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
            data: this.filingCentersOut,
          });
          if (this.finPHI != '') {
            $('#sfilingCenterssphi').val(this.finPHI)
.trigger('change');
          }
          this.folderNamephi = this.finPHI;

          $('#sfilingCenterssfddata').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
            data: this.filingCentersOut,
          });
          if (this.finFormDiscreteData != '') {
            $('#sfilingCenterssfddata')
              .val(this.finFormDiscreteData)
              .trigger('change');
          }
          this.folderNamefddata = this.finFormDiscreteData;
          $('#sfilingCentercvl').select2({
            allowClear: true,
            placeholder: 'Select Outgoing Filing Center',
            data: this.filingCentersOut,
          });
          if (this.finCvlData != '') {
            $('#sfilingCentercvl')
              .val(this.finCvlData)
              .trigger('change');
          }
          this.folderNamecvldata = this.finCvlData;
        }
      });
  }

  clearsnewFilingCenter(type = '') {
    var idname = '#' + type;
    var objIndexCate = this.documentTypes.findIndex((obj) => obj.id == type);

    // console.log("Before update: ", this.documentCategories[objIndexCate]);
    this.documentTypes[objIndexCate].showCloses = false;

    //console.log("Before update: ", this.documentCategories[objIndexCate]);
    var objIndex = this.documentTypesOut.findIndex((obj) => obj.key == type);

    /* //Log object to Console.
       console.log("Before update: ", this.documentCentersOut[objIndex]);
       //Update object's name property. */
    this.documentTypesOut[objIndex].nvalue = '';

    //        console.log("after update: ", this.documentCentersOut[objIndex]);
    $(idname).val('');
    $(idname).select2({
      allowClear: false,
      placeholder: 'Select incoming Filing Center',
      data: this.filingCentersIn,
    });
  }

  clearnewFilingCenter(type = '') {
    var idname = '#' + type;
    var objIndexCate = this.documentCategories.findIndex(
      (obj) => obj.id == type
    );
    console.log('Before update: ', this.documentCategories[objIndexCate]);
    this.documentCategories[objIndexCate].showClose = false;

    //console.log("Before update: ", this.documentCategories[objIndexCate]);
    var objIndex = this.documentCentersOut.findIndex((obj) => obj.key == type);

    // //Log object to Console.
    console.log('Before update: ', this.documentCentersOut[objIndex]);

    // //Update object's name property.
    this.documentCentersOut[objIndex].nvalue = '';

    //        console.log("after update: ", this.documentCentersOut[objIndex]);
    $(idname).val('');
    $(idname).select2({
      allowClear: false,
      placeholder: 'Select incoming Filing Center',
      data: this.filingCentersIn,
    });
  }

  keyvaluedocumentCenter(keyon, valon) {

    // console.log('name hAIIIII'+keyon);
    var objIndexCate = this.documentCategories.findIndex(
      (obj) => obj.id == keyon
    );
    console.log('Before update: ', this.documentCategories[objIndexCate]);

    if (valon == '' || valon == null) {
      this.documentCategories[objIndexCate].showClose = false;
    } else {
      this.documentCategories[objIndexCate].showClose = true;
    }

    console.log('Before update: ', this.documentCategories[objIndexCate]);
    console.log('The key does not existsss.' + this.documentCentersOut);
    var objIndex = this.documentCentersOut.findIndex((obj) => obj.key == keyon);

    //Log object to Console.
    console.log('Before update: ', this.documentCentersOut[objIndex]);

    //Update object's name property.
    this.documentCentersOut[objIndex].nvalue = valon;

    // //Log object to console again.
    console.log('After update: ', JSON.stringify(this.documentCentersOut));
  }

  keyvaluedocumentType(keyon, valon) {
    console.log('name hAIIIII' + keyon);

    var objIndexCate = this.documentTypes.findIndex((obj) => obj.id == keyon);

    // console.log("Before update: ", this.documentCategories[objIndexCate]);

    if (valon == '' || valon == null) {
      this.documentTypes[objIndexCate].showCloses = false;
    } else {
      this.documentTypes[objIndexCate].showCloses = true;
    }

    console.log('The key does not existsss.' + this.documentTypesOut);
    var objIndex = this.documentTypesOut.findIndex((obj) => obj.key == keyon);
   if(objIndex){
      this.showPageLoader = false;
    }
    this.documentTypesOut[objIndex].nvalue = valon;

    // //Log object to console again.
    console.log('After update: ', JSON.stringify(this.documentTypesOut));
  }

  clearFilingCenter(type = '') {
    if (type === 'notfy') {
      this.folderNamenotfy = '';
      this.showClosenotfy = false;
      $('#sfilingCenterssnotfy').val('');
      $('#sfilingCenterssnotfy').select2({
        allowClear: false,
        placeholder: 'Select Outgoing Filing Center',
        data: this.filingCentersOut,
      });
    } else if (type === 'dlink') {
      this.folderNamedlink = '';
      this.showClosedlink = false;
      $('#sfilingCenterssdlink').val('');
      $('#sfilingCenterssdlink').select2({
        allowClear: false,
        placeholder: 'Select Outgoing Filing Center',
        data: this.filingCentersOut,
      });
    } else if (type === 'pnote') {
      this.folderNamepnote = '';
      this.showClosepnote = false;
      $('#sfilingCentersspnote').val('');
      $('#sfilingCentersspnote').select2({
        allowClear: false,
        placeholder: 'Select Outgoing Filing Center',
        data: this.filingCentersOut,
      });
    } else if (type === 'phi') {
      this.folderNamephi = '';
      this.showClosephi = false;
      $('#sfilingCenterssphi').val('');
      $('#sfilingCenterssphi').select2({
        allowClear: false,
        placeholder: 'Select Outgoing Filing Center',
        data: this.filingCentersOut,
      });
    } else if (type === 'fddata') {
      this.folderNamefddata = '';
      this.showClosefddata = false;
      $('#sfilingCenterssfddata').val('');
      $('#sfilingCenterssfddata').select2({
        allowClear: false,
        placeholder: 'Select Outgoing Filing Center',
        data: this.filingCentersOut,
      });
    } else if (type === 'cvldata') {
      this.folderNamecvldata = '';
      this.showClosecvldata = false;
      $('#sfilingCentercvl').val('');
      $('#sfilingCentercvl').select2({
        allowClear: false,
        placeholder: 'Select Outgoing Filing Center',
        data: this.filingCentersOut,
      });
    } else if (type === 'inbdoc') {
      this.folderNameinbdoc = '';
      this.showCloseinbdoc = false;
      $('#sfilingCenterssinbdoc').val('');
      $('#sfilingCenterssinbdoc').select2({
        delay:1000,
        allowClear: false,
        placeholder: 'Select Incoming Filing Center',
        data: this.filingCentersIn,
      });
    } else {
      this.folderName = '';
      this.showClose = false;
      $('#sfilingCenterss').val('');
      $('#sfilingCenterss').select2({
        allowClear: false,
        placeholder: 'Select Outgoing Filing Center',
        data: this.filingCentersOut,
      });
    }
  }

  staffListSelectScript() {
    var self = this;
    $(function () {

      /* DOM ready
         ::: TAGS BOX */
      $('#tagsInput').on({
        click: function () {
          var txt = this.value; //.replace(/[^a-z0-9\+\-\.\#]/ig,''); // allowed characters
          if (txt) {
            self.userInputFocus('R');
          }
        },
        focusout: function () {},
        keyup: function (ev) {
          var txt = this.value; //.replace(/[^a-z0-9\+\-\.\#]/ig,'');
          if (txt && /(188|13)/.test(ev.which)) {
            self.setUser(txt, 'R'); /*$(this).focusout();*/
          }
        },
      });
      $('#defaultInput').on({
        click: function () {
          var txt = this.value; //.replace(/[^a-z0-9\+\-\.\#]/ig,''); // allowed characters
          if (txt) {
            self.userInputFocus('S');
          }
        },
        focusout: function () {},
        keyup: function (ev) {
          var txt = this.value; //.replace(/[^a-z0-9\+\-\.\#]/ig,'');
          if (txt && /(188|13)/.test(ev.which)) {
            self.setUser(txt, 'S'); /*$(this).focusout();*/
          }
        },
      });
      $('#escalationInput').on({
        click: function () {
          var txt = this.value; //.replace(/[^a-z0-9\+\-\.\#]/ig,''); // allowed characters
          if (txt) {
            self.userInputFocus('P');
          }
        },
        focusout: function () {},
        keyup: function (ev) {
          var txt = this.value; //.replace(/[^a-z0-9\+\-\.\#]/ig,'');
          if (txt && /(188|13)/.test(ev.which)) {
            self.setUser(txt, 'P'); /*$(this).focusout();*/
          }
        },
      });
    });
  }
  branchDaysSelectScript() {
    $('#branchDays').select2({
      placeholder: function () {
        $(this).data('placeholder');
      },
    });
    $('#branchDays').on('change', (e) => {
      var branchDays = [];
      var xy = $(e.target).val();
      if (xy) {
        xy.forEach((element) => {
          var types = {
            id: '',
          };
          var id = element.substr(element.indexOf(':') + 1);
          id = id.replace(/'/g, '');
          types.id = id.replace(/\s/g, '');
          branchDays.push(types.id);
        });

        this.site.patchValue({
          branchDays: branchDays,
        });
      }
    });
    $('#rolesDefault').on('change', (e) => {
      var rolesDefault = [];
      var xy = $(e.target).val();
      if (xy) {
        xy.forEach((element) => {
          var types = {
            id: '',
          };
          var id = element.substr(element.indexOf(':') + 1);
          id = id.replace(/'/g, '');
          types.id = id.replace(/\s/g, '');
          rolesDefault.push(types.id);
        });

        this.site.patchValue({
          rolesDefault: rolesDefault,
        });
      }
    });
    $('#rolesPdg').on('change', (e) => {
      var rolesPdg = [];
      var xy = $(e.target).val();
      if (xy) {
        xy.forEach((element) => {
          var types = {
            id: '',
          };
          var id = element.substr(element.indexOf(':') + 1);
          id = id.replace(/'/g, '');
          types.id = id.replace(/\s/g, '');
          rolesPdg.push(types.id);
        });

        this.site.patchValue({
          rolesPdg: rolesPdg,
        });
      }
    });
  }
  timeZoneSelectScript() {
    var self = this;
    $('#site_timezone').select2({
      placeholder: function () {
        $(this).data('placeholder');
      },
    });
    $('#site_timezone').on('change', (e) => {
      self.timeZoneOf = $(e.target).val();

      self.site.patchValue({
        timezone: self.timeZoneOf,
      });
      if (self.timeZone)
        self.timeZoneData = self.timezones.find((zone, i) => {
          return zone.offset == self.timeZone;
        });
    });
  }
  getTimeZones() {
    this._manageSitesService.getTimeZones().then((result: any) => {
      if (result && result.length > 0) this.timezones = result;
    });
  }
  noWhitespaceValidator(control: FormControl) {
    const isWhitespace = (control.value || '').trim().length === 0;
    const isValid = !isWhitespace;
    return isValid ? null : { whitespace: true, };
  }
  doneSelectedUser() {
    if (this.staffList && this.staffList.length)
      this.enableOrDisableUiLI(false, true, 'R');
    if (this.staffList1 && this.staffList1.length)
      this.enableOrDisableUiLI(false, true, 'S');
    if (this.staffList2 && this.staffList2.length)
      this.enableOrDisableUiLI(false, true, 'P');
  }
  enableOrDisableUiLI(condition, clear = true, from: any = '') {
    console.log('enableOrDisableUiLI', condition, clear, from);
    if (condition) {
      if (from === 'R') {
        if ($('ul#recipient-ul').css('display') === 'block') {
          this.enableOrDisableUiLI(false, false);
        }
        $('#tagsInput').addClass('ul-active');
        $('ul#recipient-ul').css('display', 'block');
      } else if (from === 'S') {
        if ($('#recipient-ul1').css('display') === 'block') {
          this.enableOrDisableUiLI(false, false, 'S');
        }
        $('ul#recipient-ul1').css('display', 'block');
        $('input#defaultInput').addClass('active');
      } else if (from === 'P') {
        if ($('#recipient-ul2').css('display') === 'block') {
          this.enableOrDisableUiLI(false, false, 'P');
        }
        $('ul#recipient-ul2').css('display', 'block');
        $('input#escalationInput').addClass('active');
      }
    } else {
      if (from === 'R') {
        $('#tagsInput').removeClass('ul-active');
        $('ul#recipient-ul').css('display', 'none');
      } else if (from === 'S') {
        $('ul#recipient-ul1').css('display', 'none');
        $('input#defaultInput').removeClass('active');
      } else if (from === 'P') {
        $('ul#recipient-ul2').css('display', 'none');
        $('input#escalationInput').removeClass('active');
      }
    }
    if (clear) {
      if (from === 'R') {
        $('#tagsInput').val('');
        $('#tagsInput').attr('placeholder', 'Search Staff');
      } else if (from === 'S') {
        $('input#defaultInput').val('');
      } else if (from === 'P') {
        $('input#escalationInput').val('');
      }
    }
  }
  closeSelectedUser(condition = false, which) {
    if (which == '0') this.buttonUser = 'Search';
    else if (which == '1') this.buttonUser1 = 'Search';
    else if (which == '2') this.buttonUser2 = 'Search';
    else this.buttonUser1 = 'Search';
    this.resetUser(condition, which);
  }
  resetUser(condition = false, which = '') {
    if (which == '0') {
      this.staffList = [];
      this.selectedStaff = [];
      $('#tagsInput').val('');
    } else if (which == '1') {
      this.staffList1 = [];
      this.selectedStaff1 = [];
      $('#defaultInput').val('');
    } else if (which == '2') {
      this.staffList2 = [];
    } else {
      this.staffList = [];
      this.selectedStaff = [];
      $('#tagsInput').val('');
    }
    $(String('.tag-span' + which)).remove();
    if (!condition) this.enableOrDisableUiLI(false, true, which);
  }
  selectAllUser(which = '') {
    if (which == '0') {
      if (this.staffList && this.staffList.length > 0) {
        this.staffList.forEach((element) => {
          var id = element.userId;
          var index = this.selectedStaff.indexOf(id);
          if (index === -1) {
            this.setSelectedUser(element, id, which);
          }
        });
      }
    }
    if (which == '1') {
      if (this.staffList1 && this.staffList1.length > 0) {
        this.staffList1.forEach((element) => {
          var id = element.userId;
          var index = this.selectedStaff1.indexOf(id);
          if (index === -1) {
            this.setSelectedUser(element, id, which);
          }
        });
      }
    }
    if (which == '2') {
      if (this.staffList2 && this.staffList2.length > 0) {
        this.staffList2.forEach((element) => {
          var id = element.userId;
          var index = this.selectedStaff2.indexOf(id);
          if (index === -1) {
            this.setSelectedUser(element, id, which);
          }
        });
      }
    }
  }
  setSelectedUser(users, user, which) {
    if (which == '0') {
      const selectedIndex = this.selectedStaff.indexOf(user);
      if (selectedIndex === -1) {
        this.selectedStaff.push(user);
        this.setSelectedStaffForms(false, users);
      } else {
        this.removeSelectedStaff(user);
      }
    }
    if (which == '1') {
      const selectedIndex = this.selectedStaff1.indexOf(user);
      if (selectedIndex === -1) {
        this.selectedStaff1.push(user);
        this.setSelectedStaffForms1(false, users);
      } else {
        this.removeSelectedStaff1(user);
      }
    }
    if (which == '2') {
      const selectedIndex = this.selectedStaff2.indexOf(user);
      if (selectedIndex === -1) {
        this.selectedStaff2.push(user);
        this.setSelectedStaffForms2(false, users);
      } else {
        this.removeSelectedStaff2(user);
      }
    }
  }
  setSelectedStaffForms(fromRemove = false, users: any = '') {
    if (this.selectedStaff && this.selectedStaff.length > 0) {
      var Staff = this.selectedStaff;
      if (Staff.length > 0) {
        this.StaffCount = Staff.length;
        var stf = this.selectedStaff;
        var textData = users.displayname;
        if (!fromRemove) {
          $('#viewLimit').append(
            '<span class="tag-span0" id="' +
              users.userId +
              '">' +
              users.displayname +
              '<span class="remove" data-flag="remove" style="position:relative;"  id="' +
              users.userId +
              '">x</span></span>'
          );
        }
        var activityData = {
          activityName: 'Select Staff',
          activityType: 'scheduled forms',
          activityDescription:
            this.userData.displayName +
            '(' +
            this.userData.userId +
            ') selected recipient -' +
            this.selectedStaff +
            'for scheduled forms',
        };
        this._structureService.trackActivity(activityData);
      }
    }
  }
  setSelectedStaffForms1(fromRemove = false, users: any = '') {
    if (this.selectedStaff1 && this.selectedStaff1.length > 0) {
      var Staff = this.selectedStaff1;
      if (Staff.length > 0) {
        this.StaffCount = Staff.length;
        var stf = this.selectedStaff1;
        var textData = users.displayname;
        if (!fromRemove) {
          $('#viewLimit1').append(
            '<span class="tag-span1" id="' +
              users.userId +
              '">' +
              users.displayname +
              '<span class="remove1" data-flag="remove1" style="position:relative;"  id="' +
              users.userId +
              '">x</span></span>'
          );
        }
        var activityData = {
          activityName: 'Select Staff',
          activityType: 'scheduled forms',
          activityDescription:
            this.userData.displayName +
            '(' +
            this.userData.userId +
            ') selected recipient -' +
            this.selectedStaff1 +
            'for scheduled forms',
        };
        this._structureService.trackActivity(activityData);
      }
    }
  }
  setSelectedStaffForms2(fromRemove = false, users: any = '') {
    if (this.selectedStaff2 && this.selectedStaff2.length > 0) {
      var Staff = this.selectedStaff2;
      if (Staff.length > 0) {
        this.StaffCount = Staff.length;
        var stf = this.selectedStaff2;
        var textData = users.displayname;
        if (!fromRemove) {
          $('<span/>', {
            class: 'tag-span2',
            id: users.userId,
            text: textData,
            insertBefore: $('.recipient-search-area2'),
          }).append("<span class='remove2' id=" + users.userId + '>x</span>');
        }
        var activityData = {
          activityName: 'Select Staff',
          activityType: 'scheduled forms',
          activityDescription:
            this.userData.displayName +
            '(' +
            this.userData.userId +
            ') selected recipient -' +
            this.selectedStaff2 +
            'for scheduled forms',
        };
        this._structureService.trackActivity(activityData);
      }
    }
  }
  removeSelectedStaff(id) {
    var index = this.selectedStaff.indexOf(id);
    if (this.selectedStaff && this.selectedStaff.length === 0) {
      this.site.patchValue({
        staffIds: '',
      });
    }
    this.deleteRemovedUserFromList(index, id);
  }
  removeSelectedStaff1(id) {
    var index = this.selectedStaff1.indexOf(id);
    if (this.selectedStaff1 && this.selectedStaff1.length === 0) {
      this.site.patchValue({
        staffIds1: '',
      });
    }
    this.deleteRemovedUserFromList1(index, id);
  }
  removeSelectedStaff2(id) {
    var index = this.selectedStaff2.indexOf(id);
    if (this.selectedStaff2 && this.selectedStaff2.length === 0) {

      /* this.site.patchValue({
          staffIds2: ""
        }); */
    }
    this.deleteRemovedUserFromList2(index, id);
  }
  deleteRemovedUserFromList(index, id) {
    delete this.selectedStaff[index];
    this.setOrResetSelectedItemList(id, '0');
    this.setSelectedStaffForms(true);
    this.selectedStaff = this.selectedStaff.filter((id) => {
      console.log(id);
      return id != '';
    });
  }
  deleteRemovedUserFromList1(index, id) {
    delete this.selectedStaff1[index];
    this.setOrResetSelectedItemList(id, '1');
    this.setSelectedStaffForms1(true);
    this.selectedStaff1 = this.selectedStaff1.filter((id) => {
      console.log(id);
      return id != '';
    });
  }
  deleteRemovedUserFromList2(index, id) {
    delete this.selectedStaff2[index];
    this.setOrResetSelectedItemList(id, '2');
    this.setSelectedStaffForms2(true);
    this.selectedStaff2 = this.selectedStaff2.filter((id) => {
      console.log(id);
      return id != '';
    });
  }
  setOrResetSelectedItemList(id, which = '') {
    if (which == '0')
      $('.select-rec')
        .find('#' + id)
        .remove();
    if (which == '1')
      $('.select-rec1')
        .find('#' + id)
        .remove();
    if (which == '2') $('#' + id).remove();
    return true;
  }
  checkUserWithTerms() {
    var textValue = $('#tagsInput').val();
    var searchText = textValue; //.replace(/[^a-z0-9\+\-\.\#]/ig,'');
    if (textValue != '') {
      this.setUser(searchText, 'R');
    }
  }
  checkUserWithTerms1() {
    console.log('checkUserWithTerms1');
    var textValue = $('#defaultInput').val();
    var searchText = textValue; //.replace(/[^a-z0-9\+\-\.\#]/ig,'');
    if (textValue != '') {
      this.setUser(searchText, 'S');
    }
  }
  checkUserWithTerms2() {
    console.log('checkUserWithTerms2');
    var textValue = $('#escalationInput').val();
    var searchText = textValue; //.replace(/[^a-z0-9\+\-\.\#]/ig,'');
    if (textValue != '') {
      this.setUser(searchText, 'P');
    }
  }
  userInputFocus(which = '') {
    this.enableOrDisableUiLI(true, false, which);
  }
  checkUserExist(user) {
    if (this.selectedStaff && this.selectedStaff.length > 0) {
      this.site.patchValue({
        staffIds: this.selectedStaff.join(),
      });
    }
    if (this.selectedStaff.indexOf(String(user)) > -1) {
      return true;
    }
    return false;
  }
  checkUserExist1(user) {
    if (this.selectedStaff1 && this.selectedStaff1.length > 0) {
      this.site.patchValue({
        staffIds1: this.selectedStaff1.join(),
      });
    }
    if (this.selectedStaff1.indexOf(String(user)) > -1) {
      return true;
    }
    return false;
  }
  checkUserExist2(user) {
    if (this.selectedStaff2 && this.selectedStaff2.length > 0) {

      /* this.site.patchValue({
            staffIds2: this.selectedStaff2.join()
          }); */
    }
    if (this.selectedStaff2.indexOf(String(user)) > -1) {
      return true;
    }
    return false;
  }
  isValidTime(time) {
    var pattern = /^(1[0-2]|0?[1-9]):[0-5][0-9]$/;
    return pattern.test(time);
  }
  setUser(searchText = '', which = '') {
    if (which === 'R') this.buttonUser = 'Loading';
    else if (which === 'S') this.buttonUser1 = 'Loading';
    else if (which === 'P') this.buttonUser2 = 'Loading';
    else this.buttonUser1 = 'Loading';
    this._manageSitesService
      .getStaffList(
        this.userData.tenantId,
        searchText,
        '0',
        false,
        'staffpartner'
      )
      .then((result) => {
        if (result) {
          if (which === 'R') {
            this.staffList = result;
            this.buttonUser = 'Search';
          } else if (which === 'S') {
            this.staffList1 = result;
            this.buttonUser1 = 'Search';
          } else if (which === 'P') {
            this.staffList2 = result;
            this.buttonUser2 = 'Search';
          } else {
            this.staffList = result;
            this.buttonUser1 = 'Search';
          }
        }
        this.enableOrDisableUiLI(true, false, which);
      });
  }
  saveSite(f) {
    console.log('formObjData', f);
    console.log(this.site.controls.esi, 'esi');
    if (this.selectedStaff && this.selectedStaff.length === 0) {
      this.site.controls.staffIds.setValue('');
      this.site.controls.staffIds.setValidators(Validators.required);
      this.site.controls.staffIds.updateValueAndValidity();
    }
    if (this.selectedStaff1 && this.selectedStaff1.length === 0) {
      this.site.controls.staffIds1.setValue('');

      /* this.site.controls['staffIds1'].setValidators(Validators.required);
         this.site.controls['staffIds1'].updateValueAndValidity(); */
    }
    if (!this.hoursPatientPermittedToStartChat) {
      this.site.controls.chat_start_time.setValidators(null);
      this.site.controls.chat_start_time.updateValueAndValidity();
      this.site.controls.chat_start_period.setValidators(null);
      this.site.controls.chat_start_period.updateValueAndValidity();
      this.site.controls.chat_end_time.setValidators(null);
      this.site.controls.chat_end_time.updateValueAndValidity();
      this.site.controls.chat_end_period.setValidators(null);
      this.site.controls.chat_end_period.updateValueAndValidity();
    }
    let invalidArrayAlreadyHave = [];
    if (!this.hoursPatientPermittedToStartChat) {
      invalidArrayAlreadyHave = ['no_clinician_message_on_working_hours',];
    } else {
      invalidArrayAlreadyHave = [
        'no_clinician_message_on_working_hours',
        'chat_start_time',
        'chat_end_time',
        'chat_start_period',
        'chat_end_period',
      ];
    }

    // This field is in Site Settings tab, added to array so if any error the user stays in the tab
    invalidArrayAlreadyHave.push('recipientEmailsForSupportWidget');

    var notifyS = 'Please fill all the required fields.';
    let checkValidity1 = 0;
    let checkValidity2 = 0;
    let appSettingsValidation = 0;
    const controls = this.site.controls;
    if (!f.valid) {
      for (const name in controls) {
        if (controls[name].invalid) {
          if (invalidArrayAlreadyHave.some((x) => x === name)) {
            checkValidity1 = checkValidity1 + 1;
          } else if (name !== 'appName' && name !== 'appShortLink') {
            checkValidity2 = checkValidity2 + 1;
          } else if (name === 'appName' || name === 'appShortLink') {
            ++appSettingsValidation;
          }
        }
      }
      if (checkValidity1 > 0) {
        notifyS = this._ToolTipService.getTranslateData(
          'VALIDATION_MESSAGES.SITE_SETTINGS'
        );
        this.activeSiteTab = 'settings';
      }
      if (checkValidity2 > 0) {
        notifyS = this._ToolTipService.getTranslateData(
          'VALIDATION_MESSAGES.SITE_DETAILS'
        );
        this.activeSiteTab = 'siteDetails';
      }
      if (checkValidity1 && checkValidity2) {
        notifyS = this._ToolTipService.getTranslateData(
          'VALIDATION_MESSAGES.SITE'
        );
        this.activeSiteTab = 'siteDetails';
      }
      if (appSettingsValidation && !checkValidity1 && !checkValidity2) {
        notifyS = this._ToolTipService.getTranslateData(
          'VALIDATION_MESSAGES.APP_SETTINGS'
        );
        this.activeSiteTab = 'appSettings';
      }
      setTimeout(function () {
        var notifyMessage = $.notify(notifyS);
        notifyMessage.update({
          type: 'danger',
          message: '<strong>' + notifyS + '</strong>',
        });
      });
      return false;
    }

    if (!this.isValidTime(this.site.value.branchStart)) {
      var notify = $.notify('Start Time is not valid');
      setTimeout(function () {
        notify.update({
          type: 'danger',
          message: '<strong>Start Time is not valid</strong>',
        });
      });
      return false;
    } else if (!this.isValidTime(this.site.value.branchEnd)) {
      var notify = $.notify('End Time is not valid');
      setTimeout(function () {
        notify.update({
          type: 'danger',
          message: '<strong>End Time is not valid</strong>',
        });
      });
      return false;
    }
    const formObjData = this.site.value;
    let branchEndSave = '';
    let branchStartSave = '';
    branchStartSave =
      formObjData.branchStart + ' ' + formObjData.branchStartTime;
    branchEndSave = formObjData.branchEnd + ' ' + formObjData.branchEndTime;
    formObjData.branchStartTimeAll = branchStartSave;
    formObjData.branchEndTimeAll = branchEndSave;
    formObjData.staffIds = this.selectedStaff.join();
    formObjData.staffIds1 = this.selectedStaff1.join();

    // formObjData.staffIds2 = this.selectedStaff2.join();
    if (this.site.value.branchDays) {
      formObjData.branchWorkingDays = this.site.value.branchDays.join();
    }
    if (this.site.value.timezone) {
      formObjData.tenantTimeZone = this.timeZoneOf;
    }
    if (this.siteId && this.siteId != '0') {
      this.editSiteForm(formObjData);
    } else {
      this.createSiteForm(formObjData);
    }
  }
  createSiteForm(formObjData) {
    this._manageSitesService.addSite(formObjData).subscribe((data: any) => {
      if (
        data &&
        data.data &&
        data.data.createSite.id &&
        data.data.createSite.id
      ) {
        var notify = $.notify('Success! Site creation success');
        setTimeout(function () {
          notify.update({
            type: 'success',
            message: '<strong>Success! Site creation success</strong>',
          });
        });
        var activityLogMessage =
          'Add site with the data as follows -> ' + JSON.stringify(formObjData);
        var activityData = {
          activityName: 'Add Site',
          activityType: 'Update',
          activityDescription: activityLogMessage,
        };
        this._structureService.trackActivity(activityData);
        this._structureService.setCookie('siteId', '', 1);
        var self = this;
        setTimeout(function () {
          self.router.navigate(['/manage-sites',]);
        }, 1000);
      } else if (
        data &&
        data.data &&
        data.data.createSite.message &&
        data.data.createSite.message === 'Already exist'
      ) {
        var notify = $.notify('Error! Site Already exists');
        setTimeout(function () {
          notify.update({
            type: 'warning',
            message: '<strong>Error! Site Already exists</strong>',
          });
        });
      } else {
        var notify = $.notify('Failed! Site creation failed');
        setTimeout(function () {
          notify.update({
            type: 'danger',
            message: '<strong>Failed! Site creation failed</strong>',
          });
        });
      }
    });
  }
  editSiteForm(formObjData) {
    const rolesDefault = [];
    const rolesPdg = [];
    if ($('#rolesPdg').val()) {
      $('#rolesPdg')
        .val()
        .forEach((element) => {
          var member = {
            id: '',
          };
          var id = element.substr(element.indexOf(':') + 1);
          id = id.replace(/'/g, '');
          member.id = id.replace(/\s/g, '');
          console.log(member.id);
          rolesPdg.push(member.id);
        });
    }
    if ($('#rolesDefault').val()) {
      $('#rolesDefault')
        .val()
        .forEach((element) => {
          var member = {
            id: '',
          };
          var id = element.substr(element.indexOf(':') + 1);
          id = id.replace(/'/g, '');
          member.id = id.replace(/\s/g, '');
          rolesDefault.push(member.id);
        });
    }
    formObjData.rolesDefault = rolesDefault ? rolesDefault.join() : '';
    formObjData.rolesPdg = rolesPdg ? rolesPdg.join() : '';
    formObjData.siteId = this.siteId;
    console.log('formObjData', formObjData);

    formObjData.foutfaxq = this.folderName;
    formObjData.foutnotification = this.folderNamenotfy;
    formObjData.foutDirectLinking = this.folderNamedlink;
    formObjData.foutProgressNote = this.folderNamepnote;
    formObjData.foutPHI = this.folderNamephi;
    formObjData.foutFormDiscreteData = this.folderNamefddata;
    formObjData.foutCvlIntegration = this.folderNamecvldata;
    formObjData.foutInbdoc = this.folderNameinbdoc;
    if (this.isIncomingFCForDocTypeEnabled) {
      const docdetails = [];
      for (let j = 0; j < this.documentCentersOut.length; j++) {
        const singledoc: any = {
          refId: Number(this.documentCentersOut[j].position),
          refType: 2,
          folder: this.documentCentersOut[j].nvalue,
        };
        docdetails.push(singledoc);
      }
      for (let j = 0; j < this.documentTypesOut.length; j++) {
        const singledoc: any = {
          refId: Number(this.documentTypesOut[j].position),
          refType: 1,
          folder: this.documentTypesOut[j].nvalue,
        };
        if (this.documentsfromFilingCenterFolderIn) {
          this.documentsfromFilingCenterFolderIn.filter((path) => {
            if (this.documentTypesOut[j].nvalue == path.folderName) {
              if (path.folderPath === 'FROM CRUD OPERATIONS') {
                singledoc.folderPath = 'FROM CRUD OPERATIONS';
              } else {
                singledoc.folderPath = '';
              }
            }
          });
        }
        docdetails.push(singledoc);
      }
      const tenantId = this.userData.crossTenantId;
      const siteRegistrationId = formObjData.regId;
      this._manageSitesService.createSiteFcMapping(tenantId, siteRegistrationId, docdetails).subscribe((data) => {
        const activityDataFC = {
          activityName: 'Filing Center update from Manage Sites',
          activityType: 'Update Filing Center',
          activityDescription:
            'Filing Center update requested with details: ' + JSON.stringify(docdetails) + ' and got response: ' + JSON.stringify(data) + ' for the site with registration id: ' + siteRegistrationId,
        };
        this._structureService.trackActivity(activityDataFC);
      });
    }

    this._manageSitesService.updateSite(formObjData).subscribe((data: any) => {
      var logoUploadUrl =
        this._structureService.apiBaseUrl +
        'citus-health/' +
        this._structureService.version +
        '/site-logo-upload.php';
      if (this.fileSelected && this.formData) {
        NProgress.start();
        $.ajax({
          url: logoUploadUrl,
          type: 'POST',
          data: this.formData,
          processData: false, // tell jQuery not to process the data
          contentType: false, // tell jQuery not to set contentType,
          headers: {
            'Authentication-Token': this.userData.authenticationToken,
          },
          progress: function (e) {},
        }).done((logoRes) => {
          var res = JSON.parse(logoRes);
          if (res.status === '1') {
            var notify = $.notify(' Success! Logo file updated');
            setTimeout(function () {
              notify.update({
                type: 'success',
                message: '<strong>  Success! Logo image updated. </strong>',
              });
            }, 1000);
            if (
              data &&
              data.data &&
              data.data.updateSite.id &&
              data.data.updateSite.message &&
              data.data.updateSite.message === 'Site Updated successfully'
            ) {
              this.reRouteToList(data, res.status, formObjData);
            }
          } else {
            var message = res.message;
            var notify = $.notify('Error!' + message);
            setTimeout(function () {
              notify.update({
                type: 'danger',
                message: '<strong>' + 'Error!' + message + '</strong>',
              });
            }, 2000);
            if (
              data &&
              data.data &&
              data.data.updateSite.id &&
              data.data.updateSite.message &&
              data.data.updateSite.message === 'Site Updated successfully'
            ) {
              this.reRouteToList(data, res.status, formObjData);
            }
          }
        });
        NProgress.done();
      } else if (
        data &&
        data.data &&
        data.data.updateSite.id &&
        data.data.updateSite.message &&
        data.data.updateSite.message === 'Site Updated successfully'
      ) {
        this.reRouteToList(data, '0', formObjData);
      } else if (
        data &&
        data.data &&
        data.data.updateSite.message &&
        data.data.updateSite.message === 'Already exist'
      ) {
        this.showErrorSiteAlreadyExists();
      } else {
        this.showErrorSiteFailed();
      }
    }, (error) => {
      const gqlError = error['graphQLErrors'];
      if (
        gqlError &&
        gqlError[0].message &&
        String(gqlError[0].message) === 'Already exist'
      ) {
        this.showErrorSiteAlreadyExists();
      } else {
        this.showCommonError(gqlError[0].message);
      }
    });
  }
  showErrorSiteAlreadyExists(){
    const notify = $.notify('Error! Site Already exists');
    setTimeout(function () {
      notify.update({
        type: 'warning',
        message: '<strong>Error! Site Already exists</strong>',
      });
    });
  }
  showErrorSiteFailed(){
    const notify = $.notify('Failed! Site update(s) failed');
        setTimeout(function () {
          notify.update({
            type: 'danger',
            message: '<strong>Failed! Site update(s) failed</strong>',
          });
        });
  }
  showCommonError(message){
    const notify = $.notify('Error!' + message);
    setTimeout(function () {
      notify.update({
        type: 'danger',
        message: '<strong>' + 'Error!' + message + '</strong>',
      });
    });
  }

  showActiveSite(data) {
    this.activeSiteTab = data;
  }
  showActiveSiteTab(data, data1) {
    this.activeSiteSubTab = data;
    console.log('click' + data);
    if (data === 'incoming') {
      this.inbound = true;
      this.outbound = false;
    } else {
      this.inbound = false;
      this.outbound = true;
    }

    this.activeSiteTab = data1;
    console.log('click' + data1);
  }
  reRouteToList(updateSiteRes, logoRes, formObjData) {
    var notify = $.notify('Success! Site Updated successfully');
    setTimeout(function () {
      notify.update({
        type: 'success',
        message: '<strong>Success! Site Updated successfully</strong>',
      });
    });
    var activityLogMessage =
      'Update site with the data as follows -> ' + JSON.stringify(formObjData);
    var activityData = {
      activityName: 'Update Site',
      activityType: 'manage Site',
      activityDescription: activityLogMessage,
    };
    this._structureService.trackActivity(activityData);
    this._structureService.setCookie('siteId', '', 1);
    if (this.formData && logoRes == 1) {
      setTimeout(() => {
        this.router.navigate(['/manage-sites',]);
      }, 1000);
    } else if (isBlank(this.formData) && updateSiteRes.data.updateSite.id) {
      setTimeout(() => {
        this.router.navigate(['/manage-sites',]);
      }, 1000);
    }
  }
  removeLogo(): any {
    swal(
      {
        title: 'Are you sure?',
        text: 'Do you want to delete logo',
        type: 'warning',
        showCancelButton: true,
        cancelButtonClass: 'btn-default',
        confirmButtonClass: 'btn-warning',
        confirmButtonText: 'Ok',
        closeOnConfirm: true,
      },
      (confirm) => {
        if (confirm) {
          this._manageSitesService.removeLogo(this.siteId).then((data: any) => {
            if (data.statusMessage === 'Site logo deleted successfully') {
              this.fileSelected = false;

              this._structureService.notifyMessage({
                messge: this._ToolTipService.getTranslateData(
                  'MESSAGES.MSG_REMOVE_LOGO'
                ),
                delay: 1000,
                type: 'success',
              });
              var activityData = {
                activityName: 'Site logo removed',
                activityType: 'remove site-logo ',
                activityDescription:
                  'Site logo removed via manage site by ' +
                  this.userData.displayName,
                siteId: this.siteId,
              };
            } else {
              var activityData = {
                activityName: 'Error check remove site logo',
                activityType: 'remove site-logo ',
                activityDescription:
                  'Error occured while checking remove site logo by ' +
                  this.userData.displayName,
                siteId: this.siteId,
              };
              this._structureService.notifyMessage({
                messge: this._ToolTipService.getTranslateData(
                  'MESSAGES.ERROR_MSG_REMOVE_LOGO'
                ),
                delay: 1000,
                type: 'danger',
              });
            }
            this._structureService.trackActivity(activityData);
          });
        } else {
          var activityData = {
            activityName: 'Cancel check remove site logo',
            activityType: 'remove site-logo ',
            activityDescription:
              'Cancel the process of remove site logo by ' +
              this.userData.displayName,
            siteId: this.siteId,
          };
          this._structureService.trackActivity(activityData);
        }
      }
    );
  }

  setValidationForAppSettingsFields(setValidation: boolean): void {

    /* Dynamic validation. Need to set the validation for app settings fields
       based on site level app branding is enabled or not */
    if (setValidation) {

      //If site level branding is enabled, set the validators
      this.site.controls.appName.setValidators([Validators.required,]);
      this.site.controls.appShortLink.setValidators([Validators.required,]);
      this.site.controls.appName.updateValueAndValidity();
      this.site.controls.appShortLink.updateValueAndValidity();
    } else {

      //If site level branding is disabled, clear the validators
      this.site.controls.appName.clearValidators();
      this.site.controls.appShortLink.clearValidators();
      this.site.controls.appName.updateValueAndValidity();
      this.site.controls.appShortLink.updateValueAndValidity();
    }
  }

  trackByFn(i, item): number {
    return item.id;
  }
}
