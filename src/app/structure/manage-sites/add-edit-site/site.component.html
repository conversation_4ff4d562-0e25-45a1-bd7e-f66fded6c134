<section class="card">
  <div class="card-header">
    <span class="cat__core__title">
      <strong>{{ siteActionHeader }}</strong>
    </span>
    <span class="cat__core__title">
      <a class="pull-right btn btn-sm btn-primary" [routerLink]="['/manage-sites']" id="bck_btn">Back<i class="ml-1"></i></a>
    </span>
  </div>
  <div class="card-block">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"
        ><a [routerLink]="['/inbox']">{{ "MENU.INBOX_AS_HOME" | translate }}</a></li
      >
      <li class="breadcrumb-item"
        ><a [routerLink]="['/manage-sites']">{{ "MENU.MANAGE_SITES" | translate }}</a></li
      >
      <li class="breadcrumb-item">{{ siteActionHeader }}</li>
      <li class="breadcrumb-item" *ngIf="siteName !== ''">{{ siteName }}</li>
    </ol>
    <div class="row">
      <div class="col-lg-12">
        <div class="mb-5">
          <div class="nav-tabs-horizontal">
            <ul class="nav nav-tabs mb-4" role="tablist">
              <li id="user-reg-patient-tab">
                <ul class="nav nav-tabs">
                  <li class="nav-item">
                    <a
                      class="nav-link"
                      [ngClass]="{ active: activeSiteTab === 'siteDetails' }"
                      (click)="showActiveSite('siteDetails')"
                      data-target="#tabSiteDetails"
                      data-toggle="tab"
                      href="javascript: void(0);"
                      role="tab"
                      aria-expanded="true"
                      ><i class="fa fa-id-badge completed"></i> {{ "MENU.MANAGE_SITES_SITE_DETAILS" | translate }}</a
                    >
                  </li>
                  <li class="nav-item">
                    <a
                      class="nav-link"
                      [ngClass]="{ active: activeSiteTab === 'settings' }"
                      (click)="showActiveSite('settings')"
                      data-target="#tabSettings"
                      data-toggle="tab"
                      href="javascript: void(0);"
                      role="tab"
                      aria-expanded="false"
                      ><i class="fa fa-cog waiting-others"></i> {{ "MENU.MANAGE_SITES_SITE_SETTINGS" | translate }}
                    </a>
                  </li>
                  <li class="nav-item" *ngIf="isFilingCenterEnabledForSiteEdit">
                    <a
                      class="nav-link"
                      [ngClass]="{ active: activeSiteTab === 'filingCenter' }"
                      (click)="showActiveSite('filingCenter')"
                      data-target="#tabfilingCenter"
                      data-toggle="tab"
                      href="javascript: void(0);"
                      role="tab"
                      aria-expanded="false"
                      ><i class="fa fa-file-o waiting-others"></i> {{ "MENU.MANAGE_SITES_FILING_CENTER" | translate }}
                    </a>
                  </li>
                  <li class="nav-item">
                    <a
                      class="nav-link"
                      [ngClass]="{ active: activeSiteTab === 'appSettings' }"
                      (click)="showActiveSite('appSettings')"
                      data-target="#tabAppSettings"
                      data-toggle="tab"
                      href="javascript: void(0);"
                      role="tab"
                      aria-expanded="false"
                      ><i class="fa fa-cogs waiting-others"></i> {{ "LABELS.APP_SETTINGS" | translate }}
                    </a>
                  </li>
                </ul>
              </li>
            </ul>
            <form action="#" (ngSubmit)="saveSite(f)" #f="ngForm" [formGroup]="site">
              <div class="tab-content">
                <div class="tab-pane" [ngClass]="{ active: activeSiteTab === 'siteDetails' }" id="tabSiteDetails" role="tabcard" aria-expanded="true">
                  <div class="form-body">
                    <div class="form-body">
                      <div class="form-group row">
                        <label class="col-md-3 control-label"
                          >{{ "LABELS.REGISTRATION_ID" | translate }} <i class="manageSite1 icmn-info" data-placement="right"></i
                        ></label>
                        <div class="col-md-6">
                          <input type="text" formControlName="regId" class="form-control" id="registration_id" readonly />
                        </div>
                      </div>
                    </div>

                    <div class="form-group row">
                      <label class="col-md-3 control-label">{{ "LABELS.SITE_DETAILS_SITE_NAME" | translate }} * </label>
                      <div class="col-md-6">
                        <input type="text" formControlName="name" class="form-control" placeholder="Site Name" id="site_name" required />
                        <div
                          *ngIf="site.controls['name'].errors && (site.controls.name?.dirty || site.controls.name?.touched || f.submitted)"
                          class="alert alert-danger">
                          {{ "VALIDATION_MESSAGES.SITE_DETAILS_SITE_NAME" | translate }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="form-body">
                    <div class="form-group row">
                      <label class="col-md-3 control-label"
                        >Logo
                        <!-- <i class='logo-tooltip icmn-info' id="sitelogo-tooltip" data-toggle='tooltip' data-placement='right'
                                title="{{siteLogoTooltip}}"></i> -->
                        <i class="logoUpload icmn-info" data-placement="right"></i>
                      </label>

                      <div class="logo-field">
                        <div class="logo-image" *ngIf="fileSelected">
                          <div class="logo-remove">
                            <a (click)="removeLogo()" *ngIf="!siteLogo" class="remove-icon-class"
                              ><i class="fa fa-times-circle delete-image" aria-hidden="true"></i
                            ></a>

                            <img [src]="imageUrl" />
                          </div>
                        </div>

                        <div class="col-md-6 drop-file">
                          <input class="" type="file" (change)="fileChange($event)" id="doc-file-upload" />
                          <div *ngIf="imageError" class="alert alert-danger logo-error">
                            {{ imageError }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="form-body">
                    <div class="form-group row">
                      <label class="col-md-3 control-label">{{ "LABELS.SITE_DETAILS_ADDRESS" | translate }} </label>
                      <div class="col-md-6">
                        <input
                          type="text"
                          formControlName="address"
                          class="form-control"
                          placeholder="{{ 'LABELS.SITE_DETAILS_ADDRESS' | translate }}"
                          id="site_address" />
                      </div>
                    </div>
                  </div>
                  <div class="form-body">
                    <div class="form-group row">
                      <label class="col-md-3 control-label">{{ "LABELS.SITE_DETAILS_OFFICE_EMAIL" | translate }} </label>
                      <div class="col-md-6">
                        <input
                          type="text"
                          formControlName="officeEmail"
                          class="form-control"
                          placeholder="{{ 'LABELS.SITE_DETAILS_OFFICE_EMAIL' | translate }}"
                          id="site_officeEmail" />
                        <div
                          class="alert alert-danger"
                          *ngIf="
                            site.controls.officeEmail.errors &&
                            site.controls.officeEmail.errors.pattern &&
                            (site.controls.officeEmail.dirty || site.controls.officeEmail.touched || f.submitted)
                          ">
                          {{ "VALIDATION_MESSAGES.SITE_DETAILS_OFFICE_EMAIL" | translate }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="form-body">
                    <div class="form-group row">
                      <label class="col-md-3 control-label">{{ "LABELS.SITE_DETAILS_CONTACT_EMAIL" | translate }} </label>
                      <div class="col-md-6">
                        <input
                          type="text"
                          formControlName="contactEmail"
                          class="form-control"
                          placeholder="{{ 'LABELS.SITE_DETAILS_CONTACT_EMAIL' | translate }}"
                          id="site_contactEmail" />
                        <div
                          class="alert alert-danger"
                          *ngIf="
                            site.controls.contactEmail.errors &&
                            site.controls.contactEmail.errors.pattern &&
                            (site.controls.contactEmail.dirty || site.controls.contactEmail.touched || f.submitted)
                          ">
                          {{ "VALIDATION_MESSAGES.SITE_DETAILS_CONTACT_EMAIL" | translate }}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="form-body">
                    <div class="form-group row">
                      <label class="col-md-3 control-label">{{ "LABELS.SITE_DETAILS_OFFICE_PHONE" | translate }} </label>
                      <div class="col-md-6">
                        <div class="row">
                          <div class="col-md-2">
                            <input type="tel" class="form-control" name="officeNumber" id="officeNumber" style="width: 0" />
                          </div>
                          <div class="col-md-3">
                            <input type="text" class="form-control" id="office_country_code" formControlName="officeCountryCode" readonly />
                          </div>
                          <div class="col-md-6">
                            <input
                              type="text"
                              class="form-control"
                              formControlName="officePhoneNo"
                              id="officePhone"
                              onkeypress="return (event.charCode >= 48 && event.charCode <= 57) || event.charCode == 43"
                              onpaste="return (event.charCode >= 48 && event.charCode <= 57) || event.charCode == 43"
                              placeholder="Office Number" />
                            <div
                              *ngIf="
                                site.controls['officePhoneNo'].hasError('pattern') &&
                                (site.controls.officePhoneNo?.dirty || site.controls.officePhoneNo?.touched || f.submitted)
                              "
                              class="alert alert-danger">
                              {{ "VALIDATION_MESSAGES.SITE_DETAILS_OFFICE_PHONE" | translate }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="form-body">
                    <div class="form-group row">
                      <label class="col-md-3 control-label">{{ "LABELS.SITE_DETAILS_HELPLINE_PHONE" | translate }} </label>
                      <div class="col-md-6">
                        <div class="row">
                          <div class="col-md-2">
                            <input type="tel" class="form-control" name="helplineNumber" id="helplineNumber" style="width: 0" />
                          </div>
                          <div class="col-md-3">
                            <input type="text" class="form-control" id="helpline_country_code" formControlName="helplineCountryCode" readonly />
                          </div>
                          <div class="col-md-6">
                            <input
                              type="text"
                              class="form-control"
                              formControlName="helplinePhoneNo"
                              id="helplinePhone"
                              onkeypress="return (event.charCode >= 48 && event.charCode <= 57) || event.charCode == 43"
                              onpaste="return (event.charCode >= 48 && event.charCode <= 57) || event.charCode == 43"
                              placeholder="Helpline Number" />
                            <div
                              *ngIf="
                                site.controls['helplinePhoneNo'].hasError('pattern') &&
                                (site.controls.helplinePhoneNo?.dirty || site.controls.helplinePhoneNo?.touched || f.submitted)
                              "
                              class="alert alert-danger">
                              {{ "VALIDATION_MESSAGES.SITE_DETAILS_HELPLINE_PHONE" | translate }}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="form-group row">
                    <div class="col-md-3">
                      <label class="control-label">{{ "LABELS.SITE_DETAILS_TIMEZONE" | translate }} * </label>
                    </div>
                    <div class="col-md-6">
                      <select
                        class="form-control select2 select-time-zone"
                        formControlName="timezone"
                        id="site_timezone"
                        data-placeholder="Select Time Zone"
                        required>
                        <option *ngFor="let timezone of timezones" value="{{ timezone.offset }}"> {{ timezone.name }} </option>
                      </select>
                      <div
                        *ngIf="site.controls['timezone'].errors && (site.controls.timezone?.dirty || site.controls.timezone?.touched || f.submitted)"
                        class="alert alert-danger">
                        {{ "VALIDATION_MESSAGES.SITE_DETAILS_TIMEZONE" | translate }}
                      </div>
                    </div>
                  </div>
                  <div class="form-group row">
                    <div class="col-md-3">
                      <label class="control-label"
                        >{{ "LABELS.STAFF_LIST_THAT_HAS_ACCESS_TO_SITE" | translate }} * <i class="manageSite7 icmn-info" data-placement="right"></i
                      ></label>
                    </div>
                    <div class="col-md-5 select-rec">
                      <div id="tags" style="padding: 0px !important">
                        <div id="viewLimit" class="view-limit">
                          <span *ngFor="let data of selectedStaffEdit" [hidden]="!editSite" class="tag-span0" id="{{ data.userId }}">
                            {{ data.displayname }}<span *ngIf="+data.status === 0"> ({{ 'LABELS.INACTIVE' | translate }})</span>
                            <span class="remove relative-position" data-flag="remove" id="{{ data.userId }}">x</span>
                          </span>
                        </div>
                        <div class="recipient-search-area">
                          <div class="input-dropdown">
                            <input
                              type="text"
                              class="form-control dropdown-class"
                              id="tagsInput"
                              autocomplete="off"
                              value=""
                              placeholder="Search Staff" />
                            <input type="text" hidden class="form-control" value="" formControlName="staffIds" />
                            <ul class="associate-ul recipient-ul dropdown-class" id="recipient-ul">
                              <li id="recipient-li" class="associate-li recipient-li" *ngIf="staffList && staffList.length == 0"> No item found </li>
                              <li
                                id="li-{{ user.userId }}"
                                class="associate-li recipient-li"
                                [ngClass]="{
                                  'li-selected': checkUserExist(user.userId)
                                }"
                                *ngFor="let user of staffList"
                                value="{{ user.userId }}"
                                (click)="setSelectedUser(user, user.userId, '0')">
                                {{ user.displayname }}
                              </li>
                              <li class="render-manipulate" *ngIf="staffList && staffList.length > 0">
                                <input type="button" class="recipient-select-all btn" (click)="selectAllUser('0')" value="Select All" />
                                <input type="button" class="recipient-class-clear btn" (click)="closeSelectedUser(true, '0')" value="Clear All" />
                                <input
                                  type="button"
                                  class="recipient-class-done btn"
                                  *ngIf="selectedStaff.length > 0"
                                  (click)="doneSelectedUser()"
                                  value="Done" />
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                      <!-- </div> -->
                    </div>
                    <div class="col-md-4 recipient-actions" style="position: relative">
                      <button
                        type="button"
                        id="recipient-search2"
                        style="width: 62px"
                        (click)="checkUserWithTerms()"
                        class="recipient-search-button btn btn-sm btn-primary"
                        id="srch_btn">
                        {{ buttonUser }}
                      </button>
                      <button
                        type="button"
                        id="recipient-close"
                        style="width: 62px"
                        (click)="closeSelectedUser(false, '0')"
                        class="recipient-search-button btn btn-sm btn-default recipient-close"
                        id="reset_btn">
                        {{ "BUTTONS.RESET" | translate }}
                      </button>
                    </div>
                  </div>
                  <div
                    class="row staff-list-error"
                    *ngIf="site.controls['staffIds'].errors && (site.controls.staffIds?.dirty || site.controls.staffIds?.touched || f.submitted)">
                    <span class="col-md-3"> </span>
                    <span class="col-md-5">
                      <div class="alert alert-danger"> {{ "VALIDATION_MESSAGES.STAFF_LIST_THAT_HAS_ACCESS_TO_SITE" | translate }} </div>
                    </span>
                  </div>
                  <div class="form-group row">
                    <div class="col-md-3">
                      <label class="control-label"
                        >{{ "LABELS.WORKING_DAY" | translate }} * <i class="manageSite8 icmn-info" data-placement="right"></i
                      ></label>
                    </div>
                    <div class="col-md-6">
                      <select
                        class="form-control select2"
                        formControlName="branchDays"
                        data-placeholder="Select Working Day(s)"
                        id="branchDays"
                        multiple>
                        <option *ngFor="let branchDay of allBranchDays" value="{{ branchDay.id }}"> {{ branchDay.name }} </option>
                      </select>
                      <div
                        *ngIf="
                          site.controls['branchDays'].errors && (site.controls.branchDays?.dirty || site.controls.branchDays?.touched || f.submitted)
                        "
                        class="alert alert-danger">
                        {{ "VALIDATION_MESSAGES.WORKING_DAY" | translate }}
                      </div>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="col-md-3 control-label"
                      ><b>{{ "LABELS.SITE_DETAILS_SITE_HOURS" | translate }}</b> <i class="manageSite9 icmn-info" data-placement="right"></i
                    ></label>
                  </div>
                  <div class="form-group row">
                    <label class="col-md-3 control-label"
                      >{{ "LABELS.TIME_START" | translate }} *<i class="startTime icmn-info" data-placement="right"></i
                    ></label>
                    <div class="col-md-2">
                      <input class="form-control" type="text" formControlName="branchStart" id="branchStart" placeholder="hh:mm" />
                      <div
                        *ngIf="
                          site.controls['branchStart'].hasError('required') &&
                          (site.controls.branchStart?.dirty || site.controls.branchStart?.touched || f.submitted)
                        "
                        class="alert alert-danger">
                        {{ "VALIDATION_MESSAGES.TIME_START" | translate }}
                      </div>
                    </div>
                    <div class="col-md-2">
                      <select class="form-control" formControlName="branchStartTime" id="branchStartTime">
                        <option value="AM">AM</option>
                        <option value="PM">PM</option>
                      </select>
                      <div
                        *ngIf="
                          site.controls['branchStartTime'].hasError('required') &&
                          (site.controls.branchStartTime?.dirty || site.controls.branchStartTime?.touched || f.submitted)
                        "
                        class="alert alert-danger">
                        {{ "VALIDATION_MESSAGES.TIME_PERIOD" | translate }}
                      </div>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="col-md-3 control-label"
                      >{{ "LABELS.TIME_END" | translate }} *<i class="endTime icmn-info" data-placement="right"></i
                    ></label>
                    <div class="col-md-2">
                      <input class="form-control" type="text" formControlName="branchEnd" id="branchEnd" placeholder="hh:mm" />
                      <div
                        *ngIf="
                          site.controls['branchEnd'].hasError('required') &&
                          (site.controls.branchEnd?.dirty || site.controls.branchEnd?.touched || f.submitted)
                        "
                        class="alert alert-danger">
                        {{ "VALIDATION_MESSAGES.TIME_END" | translate }}
                      </div>
                    </div>
                    <div class="col-md-2">
                      <select class="form-control" formControlName="branchEndTime" id="branchEndTime">
                        <option value="AM">AM</option>
                        <option value="PM">PM</option>
                      </select>
                      <div
                        *ngIf="
                          site.controls['branchEndTime'].hasError('required') &&
                          (site.controls.branchEndTime?.dirty || site.controls.branchEndTime?.touched || f.submitted)
                        "
                        class="alert alert-danger">
                        {{ "VALIDATION_MESSAGES.TIME_PERIOD" | translate }}
                      </div>
                    </div>
                  </div>
                  <div id="site-status" class="form-group row">
                    <label class="col-md-3 control-label">Status</label>
                    <div class="col-md-6">
                      <div class="form-group">
                        <select class="form-control" formControlName="status" disabled>
                          <option value="1" [selected]="site.controls['status'].value == '1'">Active</option>
                          <option value="0" [selected]="site.controls['status'].value == '0'">Inactive</option>
                        </select>
                        <!-- <div *ngIf="site.controls['status'].errors&&(site.controls.status?.dirty ||site.controls.status?.touched || f.submitted)" class="alert alert-danger">
                                        Status cannot be empty
                                    </div> -->
                      </div>
                    </div>
                  </div>
                  <!-- <div class="form-group row">
                            <label class="col-md-3 control-label"><b>Site Settings</b> <i class="manageSite10 icmn-info" data-placement="right"></i></label>
                        </div> -->
                </div>
                <div class="tab-pane" [ngClass]="{ active: activeSiteTab === 'settings' }" id="tabSettings" role="tabcard" aria-expanded="true">
                  <div class="form-group row">
                    <div class="col-md-3">
                      <label class="control-label"
                        >{{ "LABELS.ESI_CODE_FOR_SITE_IDENTITY" | translate }} <i class="esicode icmn-info" data-placement="right"></i
                      ></label>
                    </div>
                    <div class="col-md-6">
                      <select class="form-control" formControlName="esi" id="site_external_system_integration" data-placeholder="Select ESI code">
                        <option value="" disabled>Select ESI code</option>
                        <option *ngFor="let esi of siteExternalData" [value]="esi.externalSystemId"> {{ esi.uniqueIdentifier }}</option>
                      </select>
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="col-md-3 control-label"
                      >{{ "LABELS.WELCOME_MESSAGE_FOR_NEW_PATIENT_ON_FIRST_LOG_IN" | translate }}
                      <i class="manageSite4 icmn-info" data-placement="right"></i
                    ></label>
                    <div class="col-md-6">
                      <input
                        type="text"
                        formControlName="new_patient_chat_welcome_message"
                        class="form-control"
                        placeholder="{{ 'LABELS.WELCOME_MESSAGE_FOR_NEW_PATIENT_ON_FIRST_LOG_IN' | translate }}"
                        id="site_name" />
                      <!-- <div *ngIf="site.controls['new_patient_chat_welcome_message'].errors && (site.controls.new_patient_chat_welcome_message?.dirty || site.controls.new_patient_chat_welcome_message?.touched || f.submitted)" class="alert alert-danger">
                                    Welcome Message for New Patient on Initiate Chat is required
                                </div> -->
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="col-md-3 control-label"
                      >{{ "LABELS.NO_STAFF_MEMBER_AVAILABLE_MESSAGE" | translate }} *<i class="manageSite6 icmn-info" data-placement="right"></i
                    ></label>
                    <div class="col-md-6">
                      {{ "" // @TODO the below does not look like it should be id=site_name ... }}
                      <input
                        type="text"
                        formControlName="no_clinician_message_on_working_hours"
                        class="form-control"
                        placeholder="{{ 'LABELS.NO_STAFF_MEMBER_AVAILABLE_MESSAGE' | translate }}"
                        id="site_name"
                        required />
                      <div
                        *ngIf="
                          site.controls['no_clinician_message_on_working_hours'].errors &&
                          (site.controls.no_clinician_message_on_working_hours?.dirty ||
                            site.controls.no_clinician_message_on_working_hours?.touched ||
                            f.submitted)
                        "
                        class="alert alert-danger">
                        {{ "VALIDATION_MESSAGES.NO_STAFF_MEMBER_AVAILABLE_MESSAGE" | translate }}
                      </div>
                    </div>
                  </div>

                  <div class="form-group row">
                    <label class="col-md-3 control-label"
                      ><b>{{ "LABELS.SITE_SETTINGS_PATIENT_MESSAGING" | translate }}</b> <i class="manageSite11 icmn-info" data-placement="right"></i
                    ></label>
                  </div>
                  <div class="form-group row">
                    <label class="col-md-3 control-label"
                      >{{ "LABELS.ALLOW_PATIENTS_TO_CHAT_24_HOURS" | translate }} <i class="manageSite13 icmn-info" data-placement="right"></i
                    ></label>
                    <div class="col-md-6">
                      <button
                        type="button"
                        aria-pressed="true"
                        class="btn btn-outline-success btn-sm"
                        [ngClass]="{
                          active:
                            site.controls['working_hour'].value == 1 ||
                            site.controls['working_hour'].value == true ||
                            site.controls['working_hour'].value == 'true'
                        }"
                        (click)="togglePreference('working_hour', 1)">
                        Yes
                      </button>
                      <button
                        type="button"
                        aria-pressed="true"
                        class="btn btn-outline-default btn-sm"
                        [ngClass]="{
                          active:
                            site.controls['working_hour'].value == 0 ||
                            site.controls['working_hour'].value == false ||
                            site.controls['working_hour'].value == 'false'
                        }"
                        (click)="togglePreference('working_hour', 0)">
                        No
                      </button>
                    </div>
                  </div>
                  <div [hidden]="!hoursPatientPermittedToStartChat">
                    <div class="form-group row">
                      <label class="col-md-12 control-label"
                        ><b>{{ "LABELS.ENTER_THE_HOURS_PATIENTS_ARE_PERMITTED_TO_START_CHATS" | translate }}</b>
                        <i class="manageSite5 icmn-info" data-placement="right"></i
                      ></label>
                    </div>
                    <div class="form-group row">
                      <label class="col-md-3 control-label"
                        >{{ "LABELS.TIME_START" | translate }} *<i class="startTime icmn-info" data-placement="right"></i
                      ></label>
                      <div class="col-md-2">
                        <input class="form-control" type="text" formControlName="chat_start_time" id="chat_start_time" placeholder="hh:mm" />
                        <div
                          *ngIf="
                            site.controls['chat_start_time'].hasError('required') &&
                            (site.controls.chat_start_time?.dirty || site.controls.chat_start_time?.touched || f.submitted)
                          "
                          class="alert alert-danger">
                          {{ "VALIDATION_MESSAGES.TIME_START" | translate }}
                        </div>
                      </div>
                      <div class="col-md-2">
                        <select class="form-control" formControlName="chat_start_period" id="chat_start_period">
                          <option value="AM">AM</option>
                          <option value="PM">PM</option>
                        </select>
                        <div
                          *ngIf="
                            site.controls['chat_start_period'].hasError('required') &&
                            (site.controls.chat_start_period?.dirty || site.controls.chat_start_period?.touched || f.submitted)
                          "
                          class="alert alert-danger">
                          {{ "VALIDATION_MESSAGES.TIME_PERIOD" | translate }}
                        </div>
                      </div>
                    </div>
                    <div class="form-group row">
                      <label class="col-md-3 control-label"
                        >{{ "LABELS.TIME_END" | translate }} *<i class="endTime icmn-info" data-placement="right"></i
                      ></label>
                      <div class="col-md-2">
                        <input class="form-control" type="text" formControlName="chat_end_time" id="chat_end_time" placeholder="hh:mm" />
                        <div
                          *ngIf="
                            site.controls['chat_end_time'].hasError('required') &&
                            (site.controls.chat_end_time?.dirty || site.controls.chat_end_time?.touched || f.submitted)
                          "
                          class="alert alert-danger">
                          {{ "VALIDATION_MESSAGES.TIME_END" | translate }}
                        </div>
                      </div>
                      <div class="col-md-2">
                        <select class="form-control" formControlName="chat_end_period" id="chat_end_period">
                          <option value="AM">AM</option>
                          <option value="PM">PM</option>
                        </select>
                        <div
                          *ngIf="
                            site.controls['chat_end_period'].hasError('required') &&
                            (site.controls.chat_end_period?.dirty || site.controls.chat_end_period?.touched || f.submitted)
                          "
                          class="alert alert-danger">
                          {{ "VALIDATION_MESSAGES.TIME_END" | translate }}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="form-group row">
                    <div class="col-md-3">
                      <label class="control-label"
                        >{{ "LABELS.DEFAULT_USER_WHO_RECEIVES_ESCALATED_MESSAGES" | translate
                        }}<i class="manageSite12 icmn-info" data-placement="right"></i>
                      </label>
                    </div>
                    <div class="col-md-5 select-rec1">
                      <div id="tags" style="padding: 0px !important">
                        <div id="viewLimit1" class="view-limit">
                          <span *ngFor="let data of selectedStaffEdit1" [hidden]="!editSite" class="tag-span1" id="{{ data.userId }}"
                            >{{ data.displayname }}<span class="remove1 relative-position" data-flag="remove1" id="{{ data.userId }}">x</span></span
                          >
                        </div>
                        <div class="recipient-search-area1">
                          <div class="input-dropdown">
                            <input
                              type="text"
                              class="form-control dropdown-class"
                              id="defaultInput"
                              autocomplete="off"
                              value=""
                              placeholder="Search User" />
                            <input type="text" hidden class="form-control" value="" formControlName="staffIds1" />
                            <ul class="associate-ul recipient-ul1 dropdown-class" id="recipient-ul1">
                              <li id="recipient-li1" class="associate-li recipient-li1" *ngIf="staffList1 && staffList1.length == 0">
                                No item found
                              </li>
                              <li
                                id="li-{{ user.userId }}"
                                class="associate-li recipient-li1"
                                [ngClass]="{
                                  'li-selected': checkUserExist1(user.userId)
                                }"
                                *ngFor="let user of staffList1"
                                value="{{ user.userId }}"
                                (click)="setSelectedUser(user, user.userId, '1')">
                                {{ user.displayname }}
                              </li>
                              <li class="render-manipulate" *ngIf="staffList1 && staffList1.length > 0">
                                <input type="button" class="recipient-select-all btn" (click)="selectAllUser('1')" value="Select All" />
                                <input type="button" class="recipient-class-clear btn" (click)="closeSelectedUser(true, '1')" value="Clear All" />
                                <input
                                  type="button"
                                  class="recipient-class-done btn"
                                  *ngIf="selectedStaff.length > 0"
                                  (click)="doneSelectedUser()"
                                  value="Done" />
                              </li>
                            </ul>
                          </div>
                        </div>
                        <!-- <div style ='font-family: "PT Sans", sans-serif; !important' *ngIf="site.controls['staffIds1'].errors && (site.controls.staffIds1?.dirty || site.controls.staffIds1?.touched || f.submitted)" class="alert alert-danger">
                                        User List is required
                                    </div> -->
                      </div>
                      <!-- </div> -->
                    </div>
                    <div class="col-md-4 recipient-actions" style="position: relative">
                      <button
                        type="button"
                        id="recipient-search"
                        style="width: 62px"
                        (click)="checkUserWithTerms1()"
                        class="recipient-search-button btn btn-sm btn-primary"
                        id="srch_btn">
                        {{ buttonUser1 }}
                      </button>
                      <button
                        type="button"
                        id="recipient-close"
                        style="width: 62px"
                        (click)="closeSelectedUser(false, '1')"
                        class="recipient-search-button btn btn-sm btn-default recipient-close"
                        id="reset_btn">
                        {{ "BUTTONS.RESET" | translate }}
                      </button>
                    </div>
                  </div>
                  <div class="form-group row">
                    <div class="col-md-3">
                      <label class="control-label"
                        >{{ "LABELS.ROLES_TO_RECEIVE_PATIENT_CHATS_BY_DEFAULT" | translate }}
                        <i class="manageSite14 icmn-info" data-placement="right"></i
                      ></label>
                    </div>
                    <div class="col-md-6 view-limit">
                      <select class="form-control select2" data-placeholder="None Selected" formControlName="rolesDefault" id="rolesDefault" multiple>
                        <option *ngFor="let tenantRole of tenantRoles; trackBy:trackByFn" value="{{ tenantRole.id }}"> {{ tenantRole.name }} </option>
                      </select>
                    </div>
                  </div>
                  <div class="form-group row">
                    <div class="col-md-3">
                      <label class="control-label"
                        >{{ "LABELS.ROLES_FOR_GENERATED_PDG" | translate }} <i class="manageSite15 icmn-info" data-placement="right"></i
                      ></label>
                    </div>
                    <div class="col-md-6 view-limit">
                      <select class="form-control select2" data-placeholder="None Selected" formControlName="rolesPdg" id="rolesPdg" multiple>
                        <option *ngFor="let tenantRole of tenantRoles; trackBy:trackByFn" value="{{ tenantRole.id }}"> {{ tenantRole.name }} </option>
                      </select>
                      <!-- <div style ='font-family: "PT Sans", sans-serif; !important' *ngIf="site.controls['rolesPdg'].errors && (site.controls.rolesPdg?.dirty || site.controls.rolesPdg?.touched || f.submitted)" class="alert alert-danger">
                                  Member Roles for PDG is required
                              </div> -->
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="col-md-3 control-label"
                      >{{ "LABELS.NOTIFICATION_LANGUAGE" | translate }}
                      <i chToolTip="notificationLanguage"></i
                    ></label>
                    <div class="col-md-6">
                      <select class="form-control" formControlName="notificationLanguage">
                        <option *ngFor="let items of notificationLanguages" value="{{items.value}}">{{items.text}}</option>
                      </select>                     
                    </div>
                  </div>
                  <ng-container *ngIf="userConfig.enable_appless_model === '1'">
                    <div class="form-group row">
                      <label class="col-md-3 control-label">
                        {{ "LABELS.MAGICLINK_TOKEN_EXPIRATION_TIME" | translate }} 
                        <i chToolTip="PREFET0026"></i>
                      </label> 
                      <div class="col-md-6">
                        <input
                          type="text"
                          formControlName="magiclinkTokenExpirationTime"
                          class="form-control"
                          placeholder="{{ 'LABELS.MAGICLINK_TOKEN_EXPIRATION_TIME' | translate }}"
                          id="magiclinkTokenExpirationTime" 
                          pattern="^[0-9]*(\.[0-9]{0,2})?$" />
                          <div *ngIf="site.controls['magiclinkTokenExpirationTime'] && site.controls['magiclinkTokenExpirationTime'].errors && site.controls['magiclinkTokenExpirationTime'].errors.pattern" class="alert alert-danger">
                            {{ "VALIDATION_MESSAGES.MAGICLINK_TOKEN_EXPIRATION" | translate }}
                          </div>
                      </div>
                    </div>
                    <div class="form-group row">
                      <label class="col-md-3 control-label">
                        {{ "LABELS.MAGICLINK_REMEMBER_VERIFICATION_CODE" | translate }} 
                        <i chToolTip="PREFET0027"></i>
                      </label>
                      <div class="col-md-6">
                        <input
                          type="text"
                          formControlName="magiclinkVerificationExpiryTime"
                          class="form-control"
                          placeholder="{{ 'LABELS.MAGICLINK_REMEMBER_VERIFICATION_CODE' | translate }}"
                          id="magiclinkVerificationExpiryTime"
                          pattern="^([-][1])|^([0-9]*)$" />
                          <div *ngIf="site.controls['magiclinkVerificationExpiryTime'] && site.controls['magiclinkVerificationExpiryTime'].errors && site.controls['magiclinkVerificationExpiryTime'].errors.pattern" class="alert alert-danger">
                            {{ "VALIDATION_MESSAGES.MAGICLINK_TIME_REMEMBER_VERIFICATION_CODE" | translate }}
                        </div>
                      </div>
                    </div>
                    <div class="form-group row">
                      <label class="col-md-3 control-label">
                        {{ "LABELS.MAGICLINK_VERIFICATION_CODE_EXPIRATION_TIME" | translate }} 
                        <i chToolTip="PREFET0028"></i>
                      </label>
                      <div class="col-md-6">
                        <input
                          type="text"
                          formControlName="magiclinkVerificationTokenExpirationTime"
                          class="form-control"
                          placeholder="{{ 'LABELS.MAGICLINK_VERIFICATION_CODE_EXPIRATION_TIME' | translate }}"
                          id="magiclinkVerificationTokenExpirationTime"
                          pattern="^(0*[1-9][0-9]*(\.[0-9]+)?|0+\.[0-9]*[1-9][0-9]*)$"/>
                          <div *ngIf="site.controls['magiclinkVerificationTokenExpirationTime'] && site.controls['magiclinkVerificationTokenExpirationTime'].errors && site.controls['magiclinkVerificationTokenExpirationTime'].errors.pattern" class="alert alert-danger">
                            {{ "VALIDATION_MESSAGES.MAGICLINK_VERIFICATION_CODE_EXPIRATION_TIME" | translate }}
                        </div>
                      </div>
                    </div>
                  </ng-container>
                </div>
                <div
                  class="tab-pane"
                  [ngClass]="{ active: activeSiteTab === 'filingCenter' }"
                  id="tabfilingCenter"
                  role="tabcard"
                  aria-expanded="true">
                  <!-- <div class="form-group row">
                                <label class="col-md-3 control-label"><b>Filing Center</b> <i chToolTip=""></i></label>
                            </div> -->

                  <div class="nav-tabs-horizontal">
                    <ul class="nav nav-tabs mb-4" role="tablist">
                      <li id="user-reg-patient-tab">
                        <ul class="nav nav-tabs">
                          <li class="nav-item">
                            <a
                              class="nav-link"
                              [ngClass]="{ active: activeSiteSubTab === 'incoming' }"
                              (click)="showActiveSiteTab('incoming', 'filingCenter')"
                              data-target="#tabincoming"
                              data-toggle="tab"
                              href="javascript: void(0);"
                              role="tab"
                              aria-expanded="true"
                              ><i class="fa fa-file-o waiting-others"></i> {{ "MENU.MANAGE_SITES_FILING_CENTER_INBOUND" | translate }}</a
                            >
                          </li>
                          <li class="nav-item" [hidden]="docexmode === 'wh' || docexmode === 'both'">
                            <a
                              class="nav-link"
                              [ngClass]="{ active: activeSiteSubTab === 'outgoing' }"
                              (click)="showActiveSiteTab('outgoing', 'filingCenter')"
                              data-target="#taboutgoing"
                              data-toggle="tab"
                              href="javascript: void(0);"
                              role="tab"
                              aria-expanded="false"
                              ><i class="fa fa-file-o waiting-others"></i> {{ "MENU.MANAGE_SITES_FILING_CENTER_OUTBOUND" | translate }}
                            </a>
                          </li>
                        </ul>
                      </li>
                    </ul>

                    <div
                      class="tab-pane"
                      [ngClass]="{ active: activeSiteSubTab === 'incoming' }"
                      id="tabincoming"
                      role="tabcard"
                      aria-expanded="true"
                      [hidden]="!inbound">
                      <div class="form-group row" [hidden]="!multisite">
                        <label class="col-md-6 control-label"
                          ><b>{{ "LABELS.DEFAULT_INCOMING_FILING_CENTER_SETTINGS" | translate }}</b>
                        </label>
                      </div>

                      <div class="form-group row" [hidden]="!multisite">
                        <label class="col-md-3 control-label"
                          >{{ "LABELS.DEFAULT_INCOMING_FILING_CENTER_INBOUND_DOCUMENT_EXCHANGE" | translate }}
                          <i class="infilecenter1 icmn-info" data-placement="right"></i
                        ></label>
                        <div class="col-md-6">
                          <input id="sfilingCenterssinbdoc" value="" />
                          <i title="Clear" *ngIf="showCloseinbdoc" (click)="clearFilingCenter('inbdoc')" class="icmn-cross close-file-center"></i>
                        </div>
                      </div>
                      <div id="incategorys" [hidden]="!structureService.isDocumentCategoryEnabled">
                        <div class="form-group row" [hidden]="!multisite">
                          <label class="col-md-6 control-label"
                            ><b>{{ "LABELS.DEFAULT_INCOMING_FILING_CENTER_SETTINGS_FOR_DOC_CATEGORIES" | translate }}</b> <i chToolTip=""></i
                          ></label>
                        </div>

                        <div class="form-group row" [hidden]="!multisite">
                          <label class="col-md-3 control-label"
                            ><b> {{ "LABELS.DOCUMENT_CATEGORIES" | translate }} </b></label
                          >

                          <label class="col-md-6 control-label"
                            ><b> {{ "LABELS.DEFAULT_INCOMING_FILING_CENTER" | translate }}</b>
                          </label>
                        </div>

                        <div id="incategory">
                          <div class="form-group row" *ngFor="let documentCategory of documentCategories; let i = index">
                            <label class="col-md-3 control-label">{{ documentCategory.name }}</label>
                            <div class="col-md-6">
                              <input class="dcupdate" data-name="{{ documentCategory.id }}" id="{{ documentCategory.id }}" value="" />
                              <i
                                title="Clear"
                                *ngIf="documentCategory.showClose"
                                (click)="clearnewFilingCenter([documentCategory.id])"
                                class="icmn-cross close-file-center"></i>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div id="intypes" [hidden]="!isIncomingFCForDocTypeEnabled">
                        <div class="form-group row" [hidden]="!multisite">
                          <label class="col-md-6 control-label"
                            ><b>{{ "LABELS.DEFAULT_INCOMING_FILING_CENTER_SETTINGS_FOR_DOC_TYPES" | translate }}</b> <i chToolTip=""></i
                          ></label>
                        </div>

                        <div class="form-group row" [hidden]="!multisite">
                          <label class="col-md-3 control-label"
                            ><b> {{ "LABELS.DOCUMENT_TYPES" | translate }} </b></label
                          >

                          <label class="col-md-6 control-label"
                            ><b> {{ "LABELS.DEFAULT_INCOMING_FILING_CENTER" | translate }}</b>
                          </label>
                        </div>

                        <div id="intype">
                          <div class="form-group row" *ngFor="let documenttype of documentTypes; let i = index">
                            <label class="col-md-3 control-label">{{ documenttype.name }}</label>
                            <div class="col-md-6">
                              <input class="dtupdate" data-name="{{ documenttype.id }}" id="{{ documenttype.id }}" value="" />
                              <i
                                title="Clear"
                                *ngIf="documenttype.showCloses"
                                (click)="clearsnewFilingCenter(documenttype.id)"
                                class="icmn-cross close-file-center"></i>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      class="tab-pane"
                      [ngClass]="{ active: activeSiteSubTab === 'outgoing' }"
                      id="taboutgoing"
                      role="tabcard"
                      aria-expanded="true"
                      [hidden]="!outbound || docexmode === 'wh' || docexmode === 'both'">
                      <div id="outall" [hidden]="docexmode === 'wh' || docexmode === 'both'">
                        <!-- [hidden]="(pnotinmode !=fc || docexmode !=fc) " -->
                        <div class="form-group row" [hidden]="!multisite">
                          <label class="col-md-6 control-label"
                            ><b>{{ "LABELS.DEFAULT_OUTGOING_FILING_CENTER_SETTINGS" | translate }} </b>
                          </label>
                        </div>
                        <div class="form-group row" [hidden]="!multisite">
                          <label class="col-md-3 control-label"
                            >{{ "LABELS.DEFAULT_OUTGOING_FILING_CENTER_FOR_FAX_QUEUE" | translate }}
                            <i class="outfilecenter1 icmn-info" data-placement="right"></i
                          ></label>
                          <div class="col-md-6">
                            <input id="sfilingCenterss" value="" />
                            <i title="Clear" *ngIf="showClose" (click)="clearFilingCenter()" class="icmn-cross close-file-center"></i>
                          </div>
                        </div>
                        <div class="form-group row" [hidden]="!multisite || email_notify == 0">
                          <label class="col-md-3 control-label"
                            >{{ "LABELS.DEFAULT_OUTGOING_FILING_CENTER_FOR_NOTIFICATION" | translate }}
                            <i class="outfilecenter2 icmn-info" data-placement="right"></i
                          ></label>
                          <div class="col-md-6">
                            <input id="sfilingCenterssnotfy" value="" />
                            <i title="Clear" *ngIf="showClosenotfy" (click)="clearFilingCenter('notfy')" class="icmn-cross close-file-center"></i>
                            <!-- </div> -->
                          </div>
                        </div>
                        <div class="form-group row" [hidden]="!multisite || direct_link == 0">
                          <label class="col-md-3 control-label"
                            >{{ "LABELS.DEFAULT_OUTGOING_FILING_CENTER_FOR_DIRECT_LINKING" | translate }}
                            <i class="outfilecenter3 icmn-info" data-placement="right"></i
                          ></label>
                          <div class="col-md-6">
                            <input id="sfilingCenterssdlink" value="" />
                            <i title="Clear" *ngIf="showClosedlink" (click)="clearFilingCenter('dlink')" class="icmn-cross close-file-center"></i>
                            <!-- </div> -->
                          </div>
                        </div>
                        <div class="form-group row" [hidden]="!multisite || progress_note_integration == 0">
                          <label class="col-md-3 control-label"
                            >{{ "LABELS.DEFAULT_OUTGOING_FILING_CENTER_FOR_PROGRESS_NOTE" | translate }}
                            <i class="outfilecenter4 icmn-info" data-placement="right"></i
                          ></label>
                          <div class="col-md-6">
                            <input id="sfilingCentersspnote" value="" />
                            <i title="Clear" *ngIf="showClosepnote" (click)="clearFilingCenter('pnote')" class="icmn-cross close-file-center"></i>
                            <!-- </div> -->
                          </div>
                        </div>
                        <div class="form-group row" [hidden]="!multisite || disclose_PHI == 0">
                          <label class="col-md-3 control-label"
                            >{{ "LABELS.DEFAULT_OUTGOING_FILING_CENTER_FOR_PHI" | translate }}
                            <i class="outfilecenter5 icmn-info" data-placement="right"></i
                          ></label>
                          <div class="col-md-6">
                            <input id="sfilingCenterssphi" value="" />
                            <i title="Clear" *ngIf="showClosephi" (click)="clearFilingCenter('phi')" class="icmn-cross close-file-center"></i>
                            <!-- </div> -->
                          </div>
                        </div>
                        <div class="form-group row" [hidden]="!multisite || exchange_of_discrete_data == 0">
                          <label class="col-md-3 control-label"
                            >{{ "LABELS.DEFAULT_OUTGOING_FILING_CENTER_FOR_FORM_DISCRETE_DATA" | translate }}
                            <i class="outfilecenter6 icmn-info" data-placement="right"></i
                          ></label>
                          <div class="col-md-6">
                            <input id="sfilingCenterssfddata" value="" />
                            <i title="Clear" *ngIf="showClosefddata" (click)="clearFilingCenter('fddata')" class="icmn-cross close-file-center"></i>
                            <!-- </div> -->
                          </div>
                        </div>
                        <div class="form-group row" [hidden]="!multisite || cvl_integration == 0" >
                          <label class="col-md-3 control-label"
                            >{{ "LABELS.DEFAULT_OUTGOING_FILING_CENTER_FOR_CLINICAL_VISIT_LOG" | translate }}
                            <i class="outfilecentercvl icmn-info" data-placement="right"></i
                          ></label>
                          <div class="col-md-6">
                            <input id="sfilingCentercvl" value="" />
                            <i title="Clear" *ngIf="showClosecvldata" (click)="clearFilingCenter('cvldata')" class="icmn-cross close-file-center"></i>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  class="tab-pane"
                  [ngClass]="{ active: activeSiteTab === 'appSettings' }"
                  id="tabAppSettings"
                  role="tab-card"
                  aria-expanded="true">
                  <div class="form-body">
                    <div class="form-group row">
                      <label class="col-md-3 control-label"
                        ><b>{{ "LABELS.SITE_LEVEL_BRANDING" | translate }}</b> <i chToolTip="sitLevelBranding"></i
                      ></label>
                    </div>
                    <div class="form-group row">
                      <div class="col-md-3">
                        <label class="control-label"
                          >{{ "LABELS.ENABLE_SITE_LEVEL_APP_BRANDING" | translate }} <i chToolTip="enableSiteLevelBranding"></i
                        ></label>
                      </div>
                      <div class="col-md-6">
                        <button
                          type="button"
                          aria-pressed="true"
                          class="btn btn-outline-success btn-sm"
                          [ngClass]="{ active: site.controls['enableSiteLevelAppBranding'].value === '1' }"
                          (click)="togglePreference('appSettings', '1')"
                          >{{ "BUTTONS.YES" | translate }}</button
                        >
                        <button
                          type="button"
                          aria-pressed="true"
                          class="btn btn-outline-default btn-sm"
                          [ngClass]="{ active: site.controls['enableSiteLevelAppBranding'].value === '0' }"
                          (click)="togglePreference('appSettings', '0')"
                          >{{ "BUTTONS.NO" | translate }}</button
                        >
                      </div>
                    </div>
                    <div *ngIf="site.value.enableSiteLevelAppBranding === '1'">
                      <div class="form-group row">
                        <div class="col-md-3">
                          <label class="control-label">{{ "LABELS.APP_NAME" | translate }}* <i chToolTip="appName"></i></label>
                        </div>
                        <div class="col-md-6">
                          <input
                            class="form-control"
                            type="text"
                            xssInputValidate="{{ 'LABELS.APP_NAME' | translate }}"
                            placeholder="{{ 'PLACEHOLDERS.APP_NAME' | translate }}"
                            formControlName="appName" />
                          <div
                            class="alert alert-danger"
                            *ngIf="site.controls.appName.errors && (site.controls.appName.dirty || site.controls.appName.touched || f.submitted)">
                            {{ "VALIDATION_MESSAGES.APP_NAME" | translate }}
                          </div>
                        </div>
                      </div>
                      <div class="form-group row">
                        <div class="col-md-3">
                          <label class="control-label">{{ "LABELS.APP_SHORT_LINK" | translate }}* <i chToolTip="appShortLink"></i></label>
                        </div>
                        <div class="col-md-6">
                          <input
                            class="form-control"
                            type="text"
                            xssInputValidate="{{ 'LABELS.APP_SHORT_LINK' | translate }}"
                            placeholder="{{ 'PLACEHOLDERS.APP_SHORT_LINK' | translate }}"
                            formControlName="appShortLink" />
                          <div
                            class="alert alert-danger"
                            *ngIf="
                              site.controls.appShortLink.errors &&
                              (site.controls.appShortLink.dirty || site.controls.appShortLink.touched || f.submitted)
                            ">
                            {{ "VALIDATION_MESSAGES.APP_SHORT_LINK" | translate }}
                          </div>
                        </div>
                      </div>
                      <div class="form-group row">
                        <div class="col-md-3">
                          <label class="control-label"
                            >{{ "LABELS.EMAIL_FROM_MAILING_ADDRESS" | translate }} <i chToolTip="emailFromMailing"></i
                          ></label>
                        </div>
                        <div class="col-md-6">
                          <input
                            class="form-control"
                            type="text"
                            xssInputValidate="{{ 'LABELS.EMAIL_FROM_MAILING_ADDRESS' | translate }}"
                            placeholder="{{ 'PLACEHOLDERS.EMAIL_FROM_MAILING_ADDRESS' | translate }}"
                            formControlName="emailFromMailingAddress" />
                          <div
                            class="alert alert-danger"
                            *ngIf="
                              site.controls.emailFromMailingAddress.errors &&
                              site.controls.emailFromMailingAddress.errors.pattern &&
                              (site.controls.emailFromMailingAddress.dirty || site.controls.emailFromMailingAddress.touched || f.submitted)
                            ">
                            {{ "VALIDATION_MESSAGES.EMAIL_FROM_MAILING_ADDRESS" | translate }}
                          </div>
                        </div>
                      </div>
                      <div class="form-group row">
                        <div class="col-md-3">
                          <label class="control-label">{{ "LABELS.SMTP_DOMAIN" | translate }} <i chToolTip="smtpDomain"></i></label>
                        </div>
                        <div class="col-md-6">
                          <input
                            class="form-control"
                            type="text"
                            xssInputValidate="{{ 'LABELS.SMTP_DOMAIN' | translate }}"
                            placeholder="{{ 'PLACEHOLDERS.SMTP_DOMAIN' | translate }}"
                            formControlName="smtpDomain" />
                          <div
                            class="alert alert-danger"
                            *ngIf="
                              site.controls.smtpDomain.errors && (site.controls.smtpDomain.dirty || site.controls.smtpDomain.touched || f.submitted)
                            ">
                            {{ "VALIDATION_MESSAGES.SMTP_DOMAIN" | translate }}
                          </div>
                        </div>
                      </div>
                      <div class="form-group row">
                        <div class="col-md-3">
                          <label class="control-label"
                            >{{ "LABELS.SUPPORT_WIDGET_EMAIL_COLOR_CODE" | translate }} <i chToolTip="supportWidgetEmailColor"></i
                          ></label>
                        </div>
                        <div class="col-md-6">
                          <input
                            class="form-control"
                            type="text"
                            xssInputValidate="{{ 'LABELS.SUPPORT_WIDGET_EMAIL_COLOR_CODE' | translate }}"
                            placeholder="{{ 'PLACEHOLDERS.SUPPORT_WIDGET_EMAIL_COLOR_CODE' | translate }}"
                            formControlName="supportWidgetEmailColorCode" />
                          <div
                            class="alert alert-danger"
                            *ngIf="
                              site.controls.supportWidgetEmailColorCode.errors &&
                              (site.controls.supportWidgetEmailColorCode.dirty || site.controls.supportWidgetEmailColorCode.touched || f.submitted)
                            ">
                            {{ "VALIDATION_MESSAGES.SUPPORT_WIDGET_EMAIL_COLOR_CODE" | translate }}
                          </div>
                        </div>
                      </div>
                      <div class="form-group row">
                        <div class="col-md-3">
                          <label class="control-label"
                            >{{ "LABELS.FORGOT_PASSWORD_TEMPLATE" | translate }} <i chToolTip="forgotPasswordTemplate"></i
                          ></label>
                        </div>
                        <div class="col-md-6">
                          <input
                            class="form-control"
                            type="text"
                            xssInputValidate="{{ 'LABELS.FORGOT_PASSWORD_TEMPLATE' | translate }}"
                            placeholder="{{ 'PLACEHOLDERS.FORGOT_PASSWORD_TEMPLATE' | translate }}"
                            formControlName="forgotPasswordTemplate" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="form-actions">
                <button type="submit" class="btn btn-primary swal-btn-info">{{ siteActionButton }}</button>
                <button type="button" [routerLink]="['/manage-sites']" class="btn btn-default" id="cancel_btn">{{
                  "BUTTONS.CANCEL" | translate
                }}</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<ch-loader [showInPageCenter]="true" [showLoader]="showPageLoader"></ch-loader>