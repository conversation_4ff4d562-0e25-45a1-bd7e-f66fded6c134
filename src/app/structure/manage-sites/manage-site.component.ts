import { Component, OnInit, Input, ViewChild, Element<PERSON>ef , Renderer } from '@angular/core';
import { FormsModule,FormBuilder,FormGroup,Validators,FormControl,FormArray,ReactiveFormsModule } from '@angular/forms';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';
import { SharedService } from '../shared/sharedServices';
import { ManageSitesService } from '../manage-sites/manage-sites.service';
import { ToolTipService } from '../tool-tip.service';
import { configTimeZone } from "../../../environments/environment";
import { isInteger } from "@ng-bootstrap/ng-bootstrap/util/util";
import { Store, StoreService } from '../shared/storeService';
declare var $: any;
declare var swal: any;
declare var NProgress: any;
let moment = require('moment/moment');


@Component({
  selector: 'app-manage-sites',
  templateUrl: './manage-site.component.html'
})
export class ManageSiteComponent implements OnInit {
  dataLoadingMsg = true;
    searchText = "";
    groupList = [];
    searchResetFlag= 0;
    dTable;
     crossTenantChangeSubscriber: any;
    datam;
    timezones;
    timeZoneList;
    selectedSiteId: any;
    totalCountDataTable;
    timeZone;
    timeZoneData;
    initialLoad = true;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private _structureService: StructureService,
    private _sharedService: SharedService,
    private _ToolTipService: ToolTipService,
    renderer: Renderer,
    elementRef: ElementRef,
    private _manageSitesService: ManageSitesService,
    private _formBuild: FormBuilder,
    private storeService: StoreService
  ) {
    this.timeZone = configTimeZone();
    renderer.listen(elementRef.nativeElement, "click", (event, target) => {
  if (event.target.id == "edit-site") {
    this._structureService.setCookie("siteId", this.selectedSiteId.id, 1);
    this.router.navigate(["/site"]);
  }
});

  }

  ngOnInit() {
    this._structureService.setCookie( "siteId",'',1);
  this.getTimeZones();
   
      //this.fetchSiteList();
    }

      ngOnDestroy() {
       
    /*     $(".dataTables_filter label input").attr("placeholder", "Search");
        $(".dataTables_filter label input").unbind();
        $(".dataTables_filter label input").val('');
        if(this.dTable)
        this.dTable.search("").draw(); */
      }
      getTimeZones(){
        this._structureService.getAllTimeZones().then((timezones:any)=> {
          if(timezones && timezones.length) {
            this.timezones = timezones; 
          this._sharedService.timeZoneList = this.timezones;
          if(this.timezones){
            this.fetchSiteList();
        }
          }
        });
      }

      fetchSiteList(): void {
    this.dataLoadingMsg = true;

    var self = this;
    let datas: any;
    if (this.dTable) {
      this.dTable.destroy();
    }
    var isTrue = false;
    if (this.groupList.length > 99) {
      isTrue = true;
    }
    // Array holding selected row IDs
    $(()=>{
    this.dTable = $("#manageSiteTableId").DataTable({
      autoWidth: false,
      responsive: true,
      bprocessing: true,
      bServerSide: true,
      bpagination: true,
      bsorting: true,
      retrieve: true,
      bsearching: true,
      stateSave: true,
      bInfo: true,
      lengthMenu: [
        [25, 50],
        [25, 50]
      ],
      fnDrawCallback: function(oSettings) {
        if (
          oSettings._iRecordsTotal == 0 ||
          oSettings._iRecordsTotal < oSettings._iDisplayLength ||
          oSettings.aoData.length == 0
        ) {
          $(".dataTables_paginate").hide();
        } else {
          $(".dataTables_paginate").show();
        }
        if (oSettings.aoData.length == 0) {
          $(".dataTables_info").hide();
        }else{
          $('.dataTables_info').show();
        }
      },
      fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
        $(nRow).on("click", () => {
          this.selectedSiteId = aData;
        });
      },
      dom:
			"<'row'<'col-sm-4 'l>B<'col-sm-4'f><'col-sm-2 searchButton'>>" +
			"<'row'<'col-sm-12'tr>>" +
			"<'row'<'col-sm-5'i><'col-sm-7'p>>",
			buttons :[{
			  extend: 'collection',
			  text: 'Export Data',
			  autoClose: true,
			  className: 'buttonStyle',
			  buttons: [
				{
				  extend: 'excel',
				  text: 'Current Page',
				  title: 'Banner Alerts',
				  exportOptions: {
					columns:[0,1,2,3,4,5,6,7]
				  }
				},
				{
				  extend: 'excel',
				  text: 'All Pages',
				  title: 'Banner Alerts',
				  exportOptions: {
         			 columns:[0,1,2,3,4,5,6,7]
				  },
				  action: function ( e, dt, node, config ) {
				  var selfButton = this;
				  var oldStart = dt.settings()[0]._iDisplayStart;
				  dt.one('preXhr', function (e, s, data) {
					  data.start = 0;
					  data.length = this.totalCt;
					  dt.one('preDraw', function (e, settings) {
						  $.fn.dataTable.ext.buttons.excelHtml5.action.call(selfButton, e, dt, node, config);
						  dt.one('preXhr', function (e, s, data) {
							  settings._iDisplayStart = oldStart;
							  data.start = oldStart;
						  });
						  setTimeout(dt.ajax.reload, 0);
						  return false;
					  });
				  });
				  dt.ajax.reload();
				  }
			  }]
		  }],
      initComplete: function() {
        $(".dataTables_filter label input").attr("placeholder", "Search");
        $(".dataTables_filter label input").unbind();
        $("div.dataTables_filter input").on("keydown", function(e) {
          if (e.which == 13) {
            self.searchResetFlag = 0;
            var value = $("div.dataTables_filter input").val();
            if (value) {
              value = value.replace("”", '"');
              value = value.replace("‘", "'");
              value = value.replace("’", "'");
              self.dTable.search(value).draw();
            } else {
              self.dTable.search("").draw();
            }
          }
        });
        $("div.dataTables_filter input").on("keypress", function(e) {
          $(".searchBView").prop("disabled", false);
        });
        $("div.dataTables_filter input").on("keyup", function(e) {
          var value = $("div.dataTables_filter input").val();
          if (value) {
          } else $(".searchBView").prop("disabled", true);
        });
        $("div.searchButton").html(
          `<button disabled="true" class="btn btn-sm btn-info searchBView" title="Search" type="submit">Search</button>
            <button style="margin-left:10px;" class="btn btn-sm btn-default resetBView" title="Reset" type="submit">Reset</button>`
        );
        var value = $("div.dataTables_filter input").val();
        if (value) {
          $(".searchBView").prop("disabled", false);
        }
        $("div.dataTables_filter input").on("paste", function(event) {
          var element = this;
          var text;
          setTimeout(function() {
            text = $(element).val();
            if (text) {
              $(".searchBView").prop("disabled", false);
            }
          }, 100);
        });
        $(".buttons-collection").click(function(event) {
					setTimeout(function () {
					  if($(".buttons-collection").attr("aria-expanded") === "true" && $(".dt-button-collection").find("button").length == 0){
						$(".dt-button-collection").remove();
						$(".dt-button-background").remove();
						$(".buttons-collection").attr("aria-expanded","false");
					  }
					},500);
				});
      },
      //data: this.groupList,
      ajax: function (dat, callback, settings) {
        self.dataLoadingMsg = true;
        NProgress.start();
        let orderData;
        let searchText = '';
        let orderby;
        let limit;

        var i = dat.order[0].column ? dat.order[0].column : "";
        orderby = dat.order[0].dir ? dat.order[0].dir : "";
        if (isInteger(i)) {
          orderData = dat.columns[i].data ? dat.columns[i].data : "";
        } else {
          orderData = "id";
        }
        const searchStored = self.storeService.getStoredData(Store.SEARCH_MANAGE_SITE);
        if (dat.search.value) {
          searchText = dat.search.value;
          if (self.initialLoad) {
            self._structureService.notifySearchFilterApplied(true);
          }
        } else if (self.initialLoad && searchStored && searchStored.searchText) {
          searchText = searchStored.searchText;
          self._structureService.notifySearchFilterApplied(true);
        } else {
          self.storeService.removeData(Store.SEARCH_MANAGE_SITE);
          self._structureService.notifySearchFilterApplied(false);
        }
        if(searchText &&  self.searchResetFlag == 0){
          dat.start = 0;
          settings.oAjaxData.search.value = searchText;
          settings.oAjaxData.start = 0;
          settings._iDisplayStart = 0;
          self.searchResetFlag = 1;
        }
        if (!searchStored || (searchStored && searchStored.searchText !== searchText)) {
          self.storeService.storeData(Store.SEARCH_MANAGE_SITE, { searchText });
        }
          $("div.dataTables_filter input").val(searchText);
        if(settings.oAjaxData.start !=0 && self.datam && self.datam.aaData && self.datam.aaData.length == 1 && settings._iDisplayStart !=0  && searchText == '') 
        { 
          settings.oAjaxData.start= settings.oAjaxData.start-settings.oAjaxData.length;
          settings._iDisplayStart= settings.oAjaxData.start;
        }
        self._manageSitesService
          .getSiteList(
            dat.length ? dat.length:this.totalCt,
            dat.start,
            orderData,
            orderby,
            searchText
          )
          .then(resultData => {
            self.dataLoadingMsg = false;
            self.initialLoad = false;
            NProgress.done();
            datas = {};
            self.datam = {};
            if (dat.start == 0) {
              this.totalCt =
              resultData["sitePagination"].totalCount;
              self.totalCountDataTable = this.totalCt;
            } 
            datas = [];
            datas = (resultData['siteList'] && resultData['siteList']["data"])
            ? resultData['siteList']["data"]
            : [];

            self.groupList = datas;
            let draw;
            let total;
            if (datas && datas.length == 0 && searchText == '') {
              draw = 0;
              total = 0;
            } else {
              draw = dat.draw;
              total = this.totalCt;
            }

            self.datam = {
              draw: draw,
              recordsTotal: total,
              recordsFiltered: total,
              aaData: datas
            };
            callback(self.datam);
          });
      },
      columns: [
        { title: '#' }, 
            { title: "Name",data:"name"},
            { title: "Address", data: "Address" },
            { title: "Start Time", data: "startTime" },
            { title: "End Time", data: "endTime" },
            { title: "Timezone", data: "siteTimeZone" },
            { title: "Actions", data: "status" }
      ],
      columnDefs: [
        {
          data: null,
              orderable: false,
              width: '5%',
              targets: 0,
              render: function (data, type, row, meta) {
                let roNo = meta.row + 1 + meta.settings._iDisplayStart;
                return roNo;
              }
        },
        {
          data: null,
              targets: 1,
              width: "10%",
              orderable: true,
              render: function(data, type, row) {
                return row.name;
              }
        },
        {
          data: null,
              targets: 2,
              width: "10%",
              orderable: true,
              render: function(data, type, row) {             
                  return row.Address;
              }
        },
        {
          data: null,
                targets: 3,
                width: "10%",
                orderable: false,
                render: function(data, type, row) {
                  return (row.startTime ? row.startTime : "");
                }
        },
        {
          data: null,
                targets: 4,
                width: "10%",
                orderable: false,
                render: function(data, type, row) {
                  return (row.endTime ? row.endTime : "");
                }
        },
        {
          data: null,
              targets: 5,
              width: "10%",
              orderable: false,
              render: function(data, type, row) {
                if(self._sharedService.timeZoneList){
                let config = self._sharedService.timeZoneList.filter(x => x.offset == row.siteTimeZone);
               if(row.siteTimeZone!=null && config && config[0])
               {            
                return config[0].name; }
                else { return "";}      
              }else{
                return "";
              }
            }
        },
        {
          data: null,
            orderable: false,
            render: (data, type, row) => {
              let actions = '';
              actions += `<a id="edit-site" href="javascript: void(0);"  data-rowId ="${row.id}"  class="cat__core__link--underlined mr-3"><i id="edit-site" data-rowId ="${row.id}" class="icmn-pencil"></i> Edit</a>`
                return actions;
              },
              width: "18%",
              targets: 6
        },
        {
          data: null,
              targets: 7,
              width: "1%",
              orderable: true,
              "visible": false,
              render: function(data, type, row) {             
                  return "";
              }
        }
      ],
      language: {
        emptyTable: "No Sites found."
      },
      order: [[7,"desc"]]
    });

    $(document).on("click", ".resetBView", event => {
      this.storeService.removeData(Store.SEARCH_MANAGE_SITE);
      self._structureService.notifySearchFilterApplied(false);
      self.dTable.search("").draw();
      $(".searchBView").prop("disabled", true);
    });
    $(document).on("click", ".searchBView", event => {
      var value = $(
        "#manageSiteTableId_wrapper #manageSiteTableId_filter label input"
      ).val();
      if (value) {
        value = value.replace("”", '"');
        value = value.replace("‘", "'");
        value = value.replace("’", "'");
        const searchText = value;
        
        if (!searchText || this.initialLoad) {
          self._structureService.notifySearchFilterApplied(false);
        }
        self.searchResetFlag = 0;
        self.dTable.search(value).draw();
      } else {
        self.dTable.search("").draw();
      }
    });
        });
  }
}
