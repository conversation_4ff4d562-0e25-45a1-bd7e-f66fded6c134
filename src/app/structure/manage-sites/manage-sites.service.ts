import { Injectable } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import { ApolloClient, createNetworkInterface } from 'apollo-client';
import { Subject } from 'rxjs/Subject';
import { Observable } from 'rxjs/Rx';
import { Apollo } from 'apollo-angular';
import gql from 'graphql-tag';
import 'rxjs/add/operator/toPromise';
import * as io from "socket.io-client";
import { ISubscription } from "rxjs/Subscription";
import { SharedService } from '../../structure/shared/sharedServices';
import { GlobalDataShareService } from '../../structure/shared/global-data-share.service';
import { isBlank } from 'app/utils/utils';


let moment = require('moment/moment');
declare var swal: any;
import { StructureService } from '../structure.service';

@Injectable()
export class ManageSitesService {

    getTenantUsers;
    loginObservable;
    progressObservable;
    loginApiUrl;
    locationUrl;
    apiBaseUrl;
    serverBaseUrl;
    getTenantRoles;
    constructor(
        private _http: Http,
        private apollo: Apollo,
        private _structureService: StructureService,
        private _sharedService: SharedService,
        private _GlobalDataShareService: GlobalDataShareService,
    ) {
        this.loginObservable = new Subject<string>();
        this.progressObservable = new Subject<string>();
        this.locationUrl = "https://ipinfo.io";
        this.getTenantUsers = _structureService.apiBaseUrl + "citus-health/v4/get-tenant-users-by-roleid.php";
        this.getTenantRoles = _structureService.apiBaseUrl + 'citus-health/v4/get-tenant-roles.php';
    }

    getStaffList(tenantId = null, searchText = "", siteId="0",type= false,optionShow="staffs") {
        var data = '';
        data = "optionShow=" +optionShow+ "&roleId=3" + "&excludeRoleId=true"+ "&isFromChat=1";
        if(optionShow == 'staffpartner'){
            data = "optionShow=" +optionShow+ "&roleId=3" + "&excludeRoleId=true"; 
        }
        if(type){
            data = "roleId=3" + "&excludeRoleId=true"+ "&isFromChat=1";
        }
        
        if (searchText) {
            data += "&searchKeyword=" + searchText;
        }
        if(siteId){
          data += "&siteIds="+ siteId;
        }
       
        var apiConfig = { url: this.getTenantUsers, requestType: 'http', data: data };
        return this._structureService.requestData(apiConfig);
    }
    /**
   * Get Site data list.
   */
    getTimeZones(){
        var self = this;
        console.log("this.timezones 1",this._sharedService.timeZoneList);
        return new Promise((resolve, reject) => {
            if(this._sharedService.timeZoneList.length == 0 || this._sharedService.timeZoneList == [] || this._sharedService.timeZoneList == null){
            console.log("this.timezones 2");
            this._structureService.getAllTimeZones().then((timezones:any)=> {
                console.log("this.timezones 3",timezones);
                if(timezones && timezones.length) {
                    self._sharedService.timeZoneList = timezones;
                    resolve(timezones);
                }
            });
            }else{
                resolve(self._sharedService.timeZoneList);
            }
        });
    }
    getSiteList(limit, offset, orderData, orderby, searchText) {


        let variables: any = {
            searchText: searchText,
            limit: limit,
            offset: offset,
            orderData: orderData,
            orderby:orderby
        };

        console.log("INPUTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTT");
        console.log(variables);
        console.log("INPUTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTT");

        var getSiteListQueryTest = gql`
query siteList($limit: Int, $offset: Int, $searchText: String!,$orderData : String, $orderby : String) {
             
  siteList(TableFilter:{offset:$offset,limit:$limit,searchText:$searchText,orderData:$orderData,orderby:$orderby})  {

  data{
    id
    tenantId
    siteTimeZone
    startTime
    endTime
    Address
    status
    name
    default
  }
}

  sitePagination(TableFilter:{offset:$offset,searchText:$searchText}) {
    totalCount
  }
}
`;
        let apiConfig = {
            method: 'GET',
            data: getSiteListQueryTest,
            variables: variables,
            requestType: 'gql',
            use: "multiSite",
            nested: false,
            noLoader: true,
            count: 0
        };
        console.log("fffffffffffffffffffff", this._structureService.requestData(apiConfig));
        console.log(this._structureService.requestData(apiConfig));
        return this._structureService.requestData(apiConfig);
    }
    createSiteMutation() {
        let createSite = `
    mutation createSite(
      $name:String!,
      $Address:String,
      $siteTimeZone:String!,
      $staffIds:String!
      $workingDays:String!
      $startTime:String!
      $endTime:String!,
      $status:Int!
     ){
      createSite(
      params:{
        name:$name,
        Address:$Address,
        siteTimeZone:$siteTimeZone,
        staffIds:$staffIds,
        workingDays : $workingDays,
        startTime : $startTime,
        endTime : $endTime,
        status:$status })
          {message id}
      }
    `;
        console.log("createSite====>", createSite);
        return gql`${createSite}`;
    }
    addSite(data) {
        return this.apollo.use('multiSite').mutate({
            mutation: this.createSiteMutation(),
            variables: {
                name: data.name,
                Address: data.address,
                siteTimeZone: data.timezone,
                staffIds: data.staffIds,
                workingDays: data.branchWorkingDays,
                startTime: data.branchStartTime,
                endTime: data.branchEndTime,
                status: parseInt(data.status)
            }
        }).map(
            res => res
        );
    }
    updateSiteMutation() {
        let updateSite = `
    mutation updateSite(
        $id:Int!,
        $name:String,
        $Address:String,
        $officeEmail:String,
        $contactEmail:String,
        $officePhone:String,
        $helplinePhone:String,
        $officePhoneCountryCode:String,
        $helplineCountryCode:String,
        $siteTimeZone:String,
        $staffIds:String
        $workingDays:String
        $startTime:String
        $endTime:String
        $new_patient_chat_welcome_message:String
        $chat_start_time:String
        $chat_start_period:String
        $chat_end_time:String
        $chat_end_period:String
        $working_hour:String
        $staffIds1:String
        $rolesPdg:String
        $rolesDefault:String
        $no_clinician_message_on_working_hours:String
        $staffIds2:String
        $site_external_system_integration: String
        $foutfaxq:String 
        $foutnotification:String
        $foutDirectLinking:String
        $foutProgressNote:String
        $foutPHI:String
        $foutFormDiscreteData:String 
        $foutCvlIntegration:String 
        $foutInbdoc:String ,
        $appShortLink: String,
        $appName: String,
        $enableSupportWidgetBranding: String,
        $supportWidgetFromEmail: String,
        $recipientEmailForSupportWidget: String,
        $supportWidgetEmailColorCode: String,
        $SMTPDomain: String,
        $forgotPasswordTemplate : String,
        $notificationLanguage : String,
        $officePhoneCountryIsoCode: String,
        $helplineCountryIsoCode: String,
        $magiclinkTokenExpirationTime: String,
        $magiclinkVerificationExpiryTime: String,
        $magiclinkVerificationTokenExpirationTime: String
     ){
      updateSite(
      params:{
        id:$id,
        name:$name,
        Address:$Address,
        officeEmail:$officeEmail,
        contactEmail:$contactEmail,
        officePhone:$officePhone,
        helplinePhone:$helplinePhone,
        officePhoneCountryCode:$officePhoneCountryCode,
        helplineCountryCode:$helplineCountryCode,
        siteTimeZone:$siteTimeZone,
        staffIds:$staffIds,
        workingDays : $workingDays,
        startTime : $startTime,
        endTime : $endTime,
        officePhoneCountryIsoCode: $officePhoneCountryIsoCode,
        helplineCountryIsoCode: $helplineCountryIsoCode,
        configInputs: [
         { configKey:"new_patient_chat_welcome_message", configValue:$new_patient_chat_welcome_message },
         { configKey:"chat_start_time", configValue:$chat_start_time },
         { configKey:"chat_start_period", configValue:$chat_start_period },
         { configKey:"chat_end_time", configValue:$chat_end_time },
         { configKey:"chat_end_period", configValue:$chat_end_period },
         { configKey:"working_hour", configValue:$working_hour },
         { configKey:"default_escalation_users", configValue:$staffIds1 },
         { configKey:"default_clinician_roles_available", configValue:$rolesDefault },
         { configKey:"staff_escalation_members", configValue:$staffIds2 },
         { configKey:"no_clinician_message_on_working_hours", configValue:$no_clinician_message_on_working_hours },
         { configKey:"member_roles_for_pdg_which_create_automatically", configValue:$rolesPdg },
         { configKey: "site_external_system_integration", configValue:$site_external_system_integration} ,
         { configKey:"default_outgoing_filing_center_faxq", configValue:$foutfaxq },
         { configKey:"default_outgoing_filing_center_notification", configValue:$foutnotification },
         { configKey:"default_outgoing_filing_center_directlinking", configValue:$foutDirectLinking },
         { configKey:"default_outgoing_filing_center_progressnote", configValue:$foutProgressNote },
         { configKey:"default_outgoing_filing_center_phi", configValue:$foutPHI }, 
         { configKey:"default_outgoing_filing_center_formdiscretedata", configValue:$foutFormDiscreteData },  
         { configKey:"default_outgoing_filing_center_cvl", configValue:$foutCvlIntegration }, 
         { configKey:"default_inbound_fc", configValue:$foutInbdoc },
         { configKey:"app_short_link", configValue:$appShortLink},
         { configKey:"app_name",configValue:$appName},
         { configKey:"SMTP_domain",configValue:$SMTPDomain},
         { configKey:"enable_support_widget_branding",configValue:$enableSupportWidgetBranding},
         { configKey:"support_widget_from_email",configValue:$supportWidgetFromEmail},
         { configKey:"recipient_email_for_support_widget",configValue:$recipientEmailForSupportWidget},
         { configKey:"support_widget_email_color_code",configValue:$supportWidgetEmailColorCode}
         { configKey:"realm_key",configValue:$forgotPasswordTemplate },
         { configKey: "notification_language",configValue:$notificationLanguage},
         { configKey: "magiclink_token_expiration_time",configValue:$magiclinkTokenExpirationTime}
         { configKey: "magiclink_verification_expiry_time",configValue:$magiclinkVerificationExpiryTime}
         { configKey: "magiclink_verification_token_expiration_time",configValue:$magiclinkVerificationTokenExpirationTime}]
        })
          {message id}
      }
    `;
        console.log("updateSite====>", updateSite);
        return gql`${updateSite}`;
    }
    updateSite(data) {
        return this.apollo.use('multiSite').mutate({
            mutation: this.updateSiteMutation(),
            variables: {
                id: Number(data.siteId),
                name: data.name,
                Address: data.address,
                officeEmail:data.officeEmail,
                contactEmail:data.contactEmail,
                officePhone:data.officePhoneNo,
                helplinePhone:data.helplinePhoneNo,
                officePhoneCountryCode:data.officeCountryCode,
                helplineCountryCode:data.helplineCountryCode,
                siteTimeZone: data.timezone,
                staffIds: data.staffIds,
                workingDays: data.branchWorkingDays,
                startTime: data.branchStartTimeAll,
                endTime: data.branchEndTimeAll,
                new_patient_chat_welcome_message:data.new_patient_chat_welcome_message,
                chat_start_time:data.chat_start_time,
                chat_start_period:data.chat_start_period,
                chat_end_time:data.chat_end_time,
                chat_end_period:data.chat_end_period,
                working_hour:data.working_hour.toString(),
                staffIds1:data.staffIds1,
                rolesDefault:data.rolesDefault,
                staffIds2:data.staffIds2,
                no_clinician_message_on_working_hours:data.no_clinician_message_on_working_hours,
                rolesPdg: data.rolesPdg,
                site_external_system_integration: data.esi,
                foutfaxq: data.foutfaxq, 
                foutnotification: data.foutnotification,
                foutDirectLinking: data.foutDirectLinking,
                foutProgressNote: data.foutProgressNote,
                foutPHI: data.foutPHI,
                foutFormDiscreteData: data.foutFormDiscreteData,
                foutCvlIntegration: data.foutCvlIntegration,
                foutInbdoc:data.foutInbdoc,
                appShortLink : data.appShortLink,
                appName : data.appName,
                enableSupportWidgetBranding: data.enableSiteLevelAppBranding,
                supportWidgetFromEmail : data.emailFromMailingAddress,
                recipientEmailForSupportWidget : data.recipientEmailsForSupportWidget,
                supportWidgetEmailColorCode : data.supportWidgetEmailColorCode,
                SMTPDomain : data.smtpDomain,
                forgotPasswordTemplate : data.forgotPasswordTemplate,
                notificationLanguage : data.notificationLanguage,
                officePhoneCountryIsoCode: data.officePhoneCountryIsoCode,
                helplineCountryIsoCode: data.helplineCountryIsoCode,
                magiclinkTokenExpirationTime: data.magiclinkTokenExpirationTime,
                magiclinkVerificationExpiryTime: data.magiclinkVerificationExpiryTime,
                magiclinkVerificationTokenExpirationTime: data.magiclinkVerificationTokenExpirationTime
            }
        }).map(
            res => res
        );
    }



    createSiteFcMappingMutation() {
        let updateSiteFc = `
    mutation createSiteFcMapping(
      $tenantId:Int,
      $siteRegistrationId:String,
      $documentDetails:[DocumentDetailsInput],
     
     ){
     createSiteFcMapping(
      params:{
        tenantId:$tenantId,
        siteRegistrationId:$siteRegistrationId,
        documentDetails:$documentDetails
        })
          { message }
      }
    `;
        console.log("updateSite====>", updateSiteFc);
        return gql`${updateSiteFc}`;
    }
   
   
   
   
    createSiteFcMapping(tenantId,siteRegistrationId,documentDetails) {

        return this.apollo.use('multiSite').mutate({
            mutation: this.createSiteFcMappingMutation(),
            variables: {
                tenantId:Number(tenantId),
                siteRegistrationId:siteRegistrationId,
                documentDetails:documentDetails,
            }
        }).map(
            res => res
        );
    }
    listExternalIdentifierQuery() {
        let query = `query getSessionTenant($sessionToken: String!){
      getSessionTenant(sessionToken: $sessionToken) {
      externalIntegrationSystems {
        accountId
        uniqueIdentifier
        name
        code
        createdOn
        createdBy
        filenameExpression
        showEditor
        showInInvite
        requiredInInvite
        labelShowInEditPage
        extSysValue
        tenantAssoc
        userType      
      }    
    }}`;
        console.log("listExternalIdentifierQuery====>", query);
        return gql`${query}`;
    }
    listExternalIdentifier() {

        let variables: any = {
            sessionToken: this._structureService.getCookie('authenticationToken'),
        };
        let apiConfig = {
            method: 'GET',
            data: this.listExternalIdentifierQuery(),
            variables: variables,
            requestType: 'gql',
            use: "",
        };
        return this._structureService.requestData(apiConfig);
    }
    getSiteDetailsQuery(includeInactive?: boolean) {
    let query =
      `query getSiteDetails($siteId: Int${this._structureService.isMultiAdmissionsEnabled ? ', $admissionId: String' : ''}${
        includeInactive ? ', $includeInactive: Boolean' : ''
      }) {
        getSiteDetails(siteId: $siteId${this._structureService.isMultiAdmissionsEnabled ? ',admissionId: $admissionId' : ''}${includeInactive?',includeInactive: $includeInactive': ''}` +
            `,configs:[ ` +
            `{ configKey:"chat_end_time"}, ` +
            `{ configKey:"chat_end_period" }, ` +
            `{ configKey:"working_hour" }, ` +
            `{ configKey:"default_escalation_users"}, ` +
            `{ configKey:"default_clinician_roles_available"}, ` +
            `{ configKey:"staff_escalation_members" }, ` +
            `{ configKey:"member_roles_for_pdg_which_create_automatically" }, ` +
            `{ configKey:"no_clinician_message_on_working_hours" }, ` +
            `{ configKey:"new_patient_chat_welcome_message" }, ` +
            `{ configKey:"chat_start_time" }, ` +
            `{ configKey:"chat_start_period" }, ` +
            `{ configKey: "site_external_system_integration"},`+
            `{ configKey:"default_outgoing_filing_center_faxq" }, ` +
            `{ configKey:"default_outgoing_filing_center_notification" }, ` +
            `{ configKey:"default_outgoing_filing_center_directlinking" }, ` +
            `{ configKey:"default_outgoing_filing_center_progressnote" }, ` +
            `{ configKey:"default_outgoing_filing_center_phi" }, ` +
            `{ configKey:"default_outgoing_filing_center_formdiscretedata" }, ` +
            `{ configKey:"default_outgoing_filing_center_cvl" }, ` +
            `{ configKey:"default_inbound_fc" }, ` +
            `{ configKey:"app_short_link" }, `+
            `{ configKey:"enable_support_widget_branding" }, `+
            `{ configKey:"support_widget_from_email" }, `+
            `{ configKey:"recipient_email_for_support_widget" }, `+
            `{ configKey:"app_name" }, `+
            `{ configKey:"SMTP_domain" },`+
            `{ configKey:"support_widget_email_color_code" },`+
            `{ configKey:"realm_key"},` +  
            `{ configKey:"notification_language"},`  +
            `{ configKey:"magiclink_token_expiration_time"},`  +
            `{ configKey:"magiclink_verification_expiry_time"},`  +
            `{ configKey:"magiclink_verification_token_expiration_time"},`  +          
            `] ` +
            `) {
          tenantId
          name
          status
          siteTimeZone
          startTime
          endTime
          Address
          workingDays
          modifiedBy
          createdAt
          createdBy
          modifiedAt
          logo
          configs{
            key
            value
            data {
                userId
                displayname
              }
            }
          data {
            userId
            displayname
            status
          }
          registrationId
          officeEmail
          contactEmail
          officePhone
          helplinePhone
          officePhoneCountryCode
          helplineCountryCode
          officePhoneCountryIsoCode
          helplineCountryIsoCode
      }    
    }`;
        console.log("getSiteDetailsQuery====>", query);
        return gql`${query}`;
    }
    getSiteDetails(siteId, admissionId = "", includeInactive?: boolean) {
        let variables: any = {
            siteId: Number(siteId),
            admissionId: this._structureService.isMultiAdmissionsEnabled ? admissionId : undefined,
            includeInactive
        };
        let apiConfig = {
            method: 'GET',
            data: this.getSiteDetailsQuery(includeInactive),
            variables: variables,
            requestType: 'gql',
            use: "multiSite",
        };
        return this._structureService.requestData(apiConfig);
    }
    getExternalIntegrationQuery() {
        let query = `query siteExternalIntegrations($siteId: Int){
        siteExternalIntegrations(siteId: $siteId)   
        {
            externalSystemId
            accountId
            name
            code
            uniqueIdentifier
            filenameExpression
            createdOn
            createdBy
            showEditor
            showInInvite
            requiredInInvite
            labelShowInEditPage
            extSysValue
            tenantAssoc
            userType
            siteId
        }    
    }`;
        return gql`${query}`;
    }
    getExternalIntegration(siteId) {
        console.log(siteId, 'siteeeeeeeeee');
        let variables: any = {
            siteId: Number(siteId)
        };
        let apiConfig = {
            method: 'GET',
            data: this.getExternalIntegrationQuery(),
            variables: variables,
            requestType: 'gql',
            use: "multiSite",
        };
        return this._structureService.requestData(apiConfig);
    }
    getSitesByUserId(userId,enableMultisite=false,crossSite=false,crossSiteCommunication=false): Promise<any> {
        return new Promise(async (resolve) => {
            const userSites = JSON.parse(this._structureService.userDetails);
            console.log('cookie', this._structureService.getCookie('siteCrossBranchTenantId'));
            if(enableMultisite !== true && crossSite == true){
              
                    let chatWithTenantFilterDetails = this._GlobalDataShareService.getchatWithTenantFilterDetails();
                      if(chatWithTenantFilterDetails){


                    console.log("this.chatWithTenantFilterDetails",chatWithTenantFilterDetails);
    
    
                    console.log("chatWithTenantFilter crosstenantSites",userSites.crosstenantSites);
    
    
                    console.log("select-site-if")
                    resolve(chatWithTenantFilterDetails);
                }else{                 
    
                    console.log("select-site-if")
                    let mySiteData = userSites.mySites[0];
                    if(isBlank(userSites.crosstenantSites)){
                    let crossTenantOptions = [{"id":mySiteData.id, "name": mySiteData.name}]; 
                     resolve(crossTenantOptions); 
                      }else{
                        resolve(userSites.crosstenantSites); 
                      }
                  
                }
            }else{
                if(crossSiteCommunication == true){
                    console.log("select-site-if")
                    resolve(userSites.enabledCrosssites);
                }
            console.log("select-site-else")
            if (!(userSites.mySites).length) {
                let variables: any = {
                    crossTenantId: (this._structureService.getCookie('siteCrossBranchTenantId') ? Number(this._structureService.getCookie('siteCrossBranchTenantId')) : 0)
                };
                if (userId) {
                    variables.userId = Number(userId);
                }
                let apiConfig = {
                    method: 'GET',
                    data: this.getSitesByUserIdQuery(variables.crossTenantId),
                    variables: variables,
                    requestType: 'gql',
                    use: "multiSite"
                };
                this._structureService.requestData(apiConfig).then((res) => {
                    if(res && res['siteListByUserId'] && res['siteListByUserId']['data'])
                    resolve(res['siteListByUserId']['data']);
                    else 
                    resolve([]);
                });

            } else {
                resolve(userSites.mySites);
            }
            }
        });
    }
    getSitesByUserIdQuery(crossTenantId): any {
        let query = `query siteListByUserId($userId: Int`
            if(crossTenantId) {
                query += `, $crossTenantId: Int`;
              }
           query+= ` ) {
    siteListByUserId(userId: $userId`;
    if(crossTenantId) {
        query += `, crossTenantId: $crossTenantId`;
    }
        query += `
        )
      {
       data{
        id
        name
      }
    }
}`;
        return gql`${query}`;
    }
    getAllTenantRoles() {
		var tenantId = this._structureService.getCookie('tenantId');

		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			tenantId = this._structureService.getCookie('crossTenantId');
		}

		let data = '?tenantId=' + tenantId;

		if (
			this._structureService.getCookie('cache-page') &&
			this._structureService.getCookie('cache-page') !== 'undefined' &&
			this._structureService.getCookie('cache-page') === 'add-tag'
		) {
			data += '&page=add-tag';
		}

		var apiConfig = { url: this.getTenantRoles + data, requestType: 'http' };
		return this._structureService.requestData(apiConfig);		
	}


    qryTenantFilingCenterFoldersQuery() {

        console.log("here manage  site services query  ");
        let TenantFilingCenterFolders =`query getTenantFilingCenterFolders($sessionId: String!,$type:SyncFolderType!`;
        if( this._structureService.getCookie('crossTenantId') &&  this._structureService.getCookie('crossTenantId') !== 'undefined' &&  this._structureService.getCookie('tenantId')!== this._structureService.getCookie('crossTenantId')){
            TenantFilingCenterFolders = TenantFilingCenterFolders +`,
            $crossTenantId : Int!`;
        }
    
        TenantFilingCenterFolders = TenantFilingCenterFolders+`){getTenantFilingCenterFolders(sessionId:$sessionId,type:$type`;
        if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
          TenantFilingCenterFolders = TenantFilingCenterFolders + `,crossTenantId:$crossTenantId`;
        }
        TenantFilingCenterFolders = TenantFilingCenterFolders+`){
          folderName
          type
          folderPath
          }}`;
        console.log("MessageGroups====>",TenantFilingCenterFolders);
        return  gql`${TenantFilingCenterFolders}`;
      }  
    
    
    
    
    getTenantFilingCenterFolders(type) {
        console.log("here manage  site services ");
        let variables:any = { sessionId: this._structureService.getCookie('authenticationToken'), type: type };
        if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
            variables.crossTenantId=Number(this._structureService.getCookie('crossTenantId'));
        }
        let apiConfig = {
            method: 'GET',
            data: this.qryTenantFilingCenterFoldersQuery(),
            variables: variables,
            requestType: 'gql',
            use: "signatureRequestFilingCenterApi",
        };
        return this._structureService.requestData(apiConfig);
    
        }


        qrySiteTenantFilingCenterFoldersQuery() {
            console.log("hi here")
            let SiteTenantFilingCenterFolders =`query getSiteTenantFilingCenterFolders($sessionId: String!,$type:SyncFolderType!,$guid:String,$fromCrud:Boolean`;
            if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' &&this._structureService.getCookie('tenantId')!== this._structureService.getCookie('crossTenantId')){
            SiteTenantFilingCenterFolders = SiteTenantFilingCenterFolders +`,
            $crossTenantId : Int!`;
            }
            SiteTenantFilingCenterFolders = SiteTenantFilingCenterFolders+`){getSiteTenantFilingCenterFolders(sessionId:$sessionId,type:$type,guid:$guid,fromCrud:$fromCrud`;
            if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
            SiteTenantFilingCenterFolders = SiteTenantFilingCenterFolders + `,crossTenantId:$crossTenantId`;
            }
            SiteTenantFilingCenterFolders = SiteTenantFilingCenterFolders+`){
            folderName
            type
            folderPath
            }}`;
            console.log("MessageGroups====>",SiteTenantFilingCenterFolders);
            return  gql`${SiteTenantFilingCenterFolders}`;
        } 

        getSiteTenantFilingCenterFolders(type,guid,fromCrud= false) {
        let variables:any = { sessionId: this._structureService.getCookie('authID'), type: type, guid : guid,fromCrud:fromCrud };
        if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
            variables.crossTenantId=Number(this._structureService.getCookie('crossTenantId'));
        }
        console.log("i am  here guid   "+guid+" "+type);

        let apiConfig = {
            method: 'GET',
            data: this.qrySiteTenantFilingCenterFoldersQuery(),
            variables: variables,
            requestType: 'gql',
            use: "signatureRequestFilingCenterApi",
        };
    
          
        return this._structureService.requestData(apiConfig);
    
    
    
    }
    siteFcMapping(siteRegistrationId,docRefType,docRefTypeId,patient=0) {
        let variables: any = {
            siteRegistrationId,
            docRefType,
            docRefTypeId,
            patient
        };
        let apiConfig = {
            method: 'GET',
            data: this.siteFcMappingQuery(),
            variables: variables,
            requestType: 'gql',
            use: "multiSite",
        };
        return this._structureService.requestData(apiConfig);
    }
    siteFcMappingQuery() {
        let query = `query siteFcMappings($siteRegistrationId: String!,$docRefType: Int,$docRefTypeId: Int,$patient:Int){
            siteFcMappings(siteRegistrationId: $siteRegistrationId,docRefType: $docRefType,docRefTypeId: $docRefTypeId,patient:$patient)   
        {
            id
            tenantId
            siteRegistrationId
            refId
            refType
            folderPath
            folder
            filingCenterSiteConfig{
                fromFilingCenter
              }
        }    
    }`;
        return gql`${query}`;
    }



    qrysiteFcMappingsQuery() {
        console.log("hi here")
        let siteFcMappings =`query siteFcMappings($siteRegistrationId:String`;
        if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' &&this._structureService.getCookie('tenantId')!== this._structureService.getCookie('crossTenantId')){
            siteFcMappings = siteFcMappings +`,
        $tenantId : String`;
        }
        siteFcMappings = siteFcMappings+`){siteFcMappings(siteRegistrationId:$siteRegistrationId`;
        if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
            siteFcMappings = siteFcMappings + `,tenantId:$tenantId`;
        }
        siteFcMappings = siteFcMappings+`){ id tenantId siteRegistrationId folderPath folder refId refType}}`;
        console.log("MessageGroups====>",siteFcMappings);
        return  gql`${siteFcMappings}`;
    } 

    getsiteFcMappings(guid) {
        let variables:any = {siteRegistrationId : guid };
        if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
            variables.tenantId=this._structureService.getCookie('crossTenantId');
        }
        let apiConfig = {
            method: 'GET',
            data: this.qrysiteFcMappingsQuery(),
            variables: variables,
            requestType: 'gql',
            use: "multiSite",
        };
        return this._structureService.requestData(apiConfig);
    }
    removeLogo(siteId) {
        const apiUrl = this._structureService.apiBaseUrl + "citus-health/v4/delete-site-logo.php";
        var data = "siteId="+siteId;
        var apiConfig = {url: apiUrl, requestType: 'http', data: data, contentType: 'application/x-www-form-urlencoded'};
        return this._structureService.requestData(apiConfig);
    }
   





}
