import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { BrowserModule } from '@angular/platform-browser';
import { ReactiveFormsModule } from '@angular/forms';
import { FormsModule } from '@angular/forms';
import { ManageSiteComponent } from './manage-site.component';
import { SiteComponent } from './add-edit-site/site.component';
import { AuthGuard } from '../../guard/auth.guard';
import { TranslateModule } from '@ngx-translate/core';
import{SharedModule} from  '../shared/sharedModule';

export const routes: Routes = [
  { path: 'manage-sites', component: ManageSiteComponent,canActivate:[AuthGuard], data: {
    checkRoutingConfig: 'enable_multisite', checkRoutingPrivileges:'superAdmin,manageTenants'}},
  { path: 'site', component: SiteComponent,canActivate:[AuthGuard], data: {
    checkRoutingConfig: 'enable_multisite'}},
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    BrowserModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    TranslateModule,
    SharedModule
  ],
  exports:[TranslateModule],
  declarations: [
    ManageSiteComponent,
    SiteComponent
  ],
  providers: []
  

})

export class ManageSitesModule { }
