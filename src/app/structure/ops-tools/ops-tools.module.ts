import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { OsQueryComponent } from './os-query.citushealth';

export const routes: Routes = [
  { path: 'ops-tools/os-query', component: OsQueryComponent }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes)
  ],
  declarations: [
    OsQueryComponent,
  ]

})

export class OpsToolsModule { }
