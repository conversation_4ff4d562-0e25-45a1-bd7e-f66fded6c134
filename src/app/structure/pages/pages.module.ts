import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';

import { PagesPage404Component } from './page-404.page';
import { PagesPage500Component } from './page-500.page';

export const routes: Routes = [
  { path: 'pages/page-404', component: PagesPage404Component },
  { path: 'pages/page-500', component: PagesPage500Component },
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes)
  ],
  declarations: [
    PagesPage404Component,
    PagesPage500Component,
  ]

})
export class PagesModule { }
