import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';

import { RouterModule, Routes } from '@angular/router';
import { AFSComponent } from './afs.component';
import { AuthGuard } from '../../guard/auth.guard';
import { SharedModule } from '../shared/sharedModule';

export const routes: Routes = [
  {
    path: 'afs',
    canActivate: [AuthGuard],
    data: {
      checkRoutingPrivileges: 'manageAFS'
    },
    children: [{ path: '**', component: AFSComponent }]
  }
];

@NgModule({
  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)],
  declarations: [AFSComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class AFSModule {}
