import { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ViewEncapsulation } from '@angular/core';
import { StructureService } from '../structure.service';

@Component({
  selector: 'app-afs',
  templateUrl: './afs.component.html',
  styleUrls: ['./afs.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AFSComponent implements OnInit, OnDestroy {
  userData: any;
  constructor(public structureService: StructureService) {}

  ngOnInit(): void {
    this.userData = JSON.parse(this.structureService.userDetails);
    this.structureService.loadMicroFrontend();
  }

  ngOnDestroy(): void {
    this.structureService.unLoadMicroFrontend();
  }
}
