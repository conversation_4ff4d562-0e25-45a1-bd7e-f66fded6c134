import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { ProfileComponent } from './profile.citushealth';
import { ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '../shared/sharedModule';
import { TextMaskModule } from 'angular2-text-mask';
import { UsersModule } from '../users/users.module';

export const routes: Routes = [
  { path: 'profile', component: ProfileComponent }
];

@NgModule({
  imports: [
    UsersModule,
    CommonModule,
    FormsModule,
    TextMaskModule,
    ReactiveFormsModule,
    SharedModule,
    RouterModule.forChild(routes)
  ],
  declarations: [
    ProfileComponent
  ]

})

export class ProfileModule { }
