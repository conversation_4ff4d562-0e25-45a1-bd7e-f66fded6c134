import { Pipe, PipeTransform } from '@angular/core';
@Pipe({
  name: 'filter'
})
export class FilterPipe implements PipeTransform {
  transform(items: any[], searchText: any, key?: string): any[] {
    if (!items) return [];
    if (!searchText) return items;
    return items.filter((item) => {
      if (typeof item[key || 'displayName'] === 'string') {
        return item[key || 'displayName'].toLowerCase() === searchText.toLowerCase();
      }
      return item[key || 'displayName'] === searchText;
    });
  }
}

