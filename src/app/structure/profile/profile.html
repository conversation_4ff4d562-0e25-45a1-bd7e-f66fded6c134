<!-- START: apps/profile -->
<section class="card">
<nav class="cat__core__top-sidebar cat__core__top-sidebar--bg">
    <div class="row">
        <div class="col-xl-8 offset-xl-4">
            <!-- <div class="width-100 text-center pull-right hidden-md-down">
                <h2>154</h2>
                <p class="mb-0">Followers</p>
            </div>
            <div class="width-100 text-center pull-right hidden-md-down">
                <h2>17</h2>
                <p class="mb-0">Posts</p>
            </div>-->
            <h2>
                <span class="text-black">
                    <strong>{{userDetails.displayName}}</strong>
                </span>

            </h2>
            <p class="mb-1">{{userDetails.roleName}}</p>
        </div>
    </div>
</nav>
<div class="row">
    <div class="col-xl-4">
        <section class="card cat__apps__profile__card" style="background-image: url(assets/modules/dummy-assets/common/img/photos/4.jpeg)">
            <div class="card-block text-center">
                <a class="cat__core__avatar cat__core__avatar--border-white cat__core__avatar--110" href="javascript:void(0);">
                    <img id="profile-img" src="{{avatar}}" alt="Avatar" onerror="this.src='assets/img/file-404.png'">
                </a>
                <br />
                <br />
                <p class="text-white"  outOfOfficeStatus [oooInfo]="userDetails.oooInfo" [returnOnly]="true" (badgeStatusInfo)="badgeStatusInfo = $event">
                    <span class="cat__core__donut cat__core__donut--success" [ngStyle]="badgeStatusInfo && badgeStatusInfo.status ? { 'border-color' : badgeStatusInfo.color } : {}"></span> {{ (badgeStatusInfo && badgeStatusInfo.status ? badgeStatusInfo.message :'LABELS.ONLINE') | translate }}
                </p>
            </div>
        </section>


        <section class="card">
            <div class="card-block">
                <h5 class="mb-3 text-black">
                    <strong>{{'LABELS.PERSONAL_INFORMATION' | translate}}</strong>

                </h5>
                <dl class="row profile-left-list">
                    <dt class="col-xl-5">{{'LABELS.USER_PROFILE_DISPLAY_NAME' | translate}}</dt>
                    <dd class="col-xl-7">{{userDetails.displayName}} </dd>
                    <dt class="col-xl-5">{{'LABELS.USER_PROFILE_FIRST_NAME' | translate}}</dt>
                    <dd class="col-xl-7">{{userDetails.firstName}}</dd>
                    <dt class="col-xl-5">{{'LABELS.USER_PROFILE_LAST_NAME' | translate}}</dt>
                    <dd class="col-xl-7">{{userDetails.secondName}}</dd>
                    <dt class="col-xl-5">{{'LABELS.USER_PROFILE_EMAIL' | translate}}</dt>
                    <dd class="col-xl-7 emailwarapper">{{(!userDetails.username.includes('unknown_')) ? userDetails.username : ''}}</dd>
                    <dt class="col-xl-5">{{'LABELS.LOGIN_USERNAME' | translate}}</dt>
                    <dd class="col-xl-7 emailwarapper">{{userDetails.alternate_username}}</dd>
                    <dt class="col-xl-5">{{'LABELS.CONTACT_NUMBER' | translate}}</dt>
                    <dd class="col-xl-7">{{userDetails.mobile}}</dd>
                    <dt class="col-xl-5">{{'LABELS.ROLE' | translate}}</dt>
                    <dd class="col-xl-7">{{userDetails.roleName}}</dd>
                    <dt class="col-xl-5" *ngIf="userDetails.group == '3' && !userDetails.caregiver_userid" >{{'LABELS.DATE_OF_BIRTH' | translate}}</dt>
                    <dd class="col-xl-7" *ngIf="userDetails.group == '3' && !userDetails.caregiver_userid" >{{userDetails.dob | date:'MM-dd-yyyy' }}</dd>
                    <dt class="col-xl-5" *ngIf="userDetails.caregiver_userid" >{{'LABELS.PATIENT_NAME' | translate}}</dt>
                    <dd class="col-xl-7" *ngIf="userDetails.caregiver_userid" >{{userDetails.caregiver_displayname}}</dd>
                    <dt class="col-xl-5" *ngIf="userDetails.caregiver_userid" >{{'LABELS.PATIENT_DOB' | translate}}</dt>
                    <dd class="col-xl-7" *ngIf="userDetails.caregiver_userid" >{{userDetails.caregiver_dob | date:'MM-dd-yyyy' }}</dd>
                    <dt *ngIf="userDetails.group == 3 && !userDetails.caregiver_userid" class="col-xl-5">{{'LABELS.USER_PROFILE_ZIP' | translate}}</dt>
                    <dd *ngIf="userDetails.group == 3 && !userDetails.caregiver_userid" class="col-xl-7">{{userDetails.zip}}</dd>
                    <dt class="col-xl-5">{{'LABELS.LANGUAGE_KNOWN' | translate}}</dt>
                    <dd class="col-xl-7">{{selectedLanguages}}</dd>
                    <!--<dt class="col-xl-3">Active Since</dt>
                    <dd class="col-xl-9">January 30 2016</dd>-->
                </dl>
            </div>
            <div class="card-block" *ngIf="userDetails.group == '3' && userDetails.alternate_contacts && userDetails.alternate_contacts.length">
                <h5 class="mb-3 text-black">
                    <strong *ngIf="userDetails.alternate_contacts.length == 1"> {{'LABELS.ALTERNATE_CONTACT' | translate}}</strong>
                    <strong *ngIf="userDetails.alternate_contacts.length > 1"> {{'LABELS.ALTERNATE_CONTACTS' | translate}}</strong>

                </h5>
                <dl class="row" *ngFor="let contact of userDetails.alternate_contacts;let i = index" [ngStyle]="{'border-bottom': (userDetails.alternate_contacts.length-1!=i) ? '1px solid #e4e9f0' :  '' }">
                   <dt class="col-xl-5">{{'LABELS.USER_PROFILE_FIRST_NAME' | translate}}</dt>
                    <dd class="col-xl-7">{{contact.firstName}}</dd>
                    <dt class="col-xl-5">{{'LABELS.USER_PROFILE_LAST_NAME' | translate}}</dt>
                    <dd class="col-xl-7">{{contact.lastName}}</dd>
                     <dt class="col-xl-5" *ngIf="contact.email && contact.email!=''">{{'LABELS.USER_PROFILE_EMAIL' | translate}}</dt>
                    <dd class="col-xl-7" *ngIf="contact.email && contact.email!=''">{{contact.email}}</dd>
                    <dt class="col-xl-5" *ngIf="contact.mobile && contact.mobile!=''">{{'LABELS.CONTACT_NUMBER' | translate}}</dt>
                    <dd class="col-xl-7" *ngIf="contact.mobile && contact.mobile!=''">{{contact.mobile}}</dd>
                    <dt class="col-xl-5">{{'LABELS.RELATION' | translate}}</dt>
                    <dd class="col-xl-7">{{contact.relation}}</dd>
                </dl>
            </div>
            <div class="card-block" *ngIf="userDetails.group == '3' && userDetails.isAlternateContact && (userDetails.isAlternateContact=='true' || userDetails.isAlternateContact==true) && userDetails.alternate_contact_patient">
                <h5 class="mb-3 text-black">
                    <strong>{{'LABELS.ON_BAHALF_OF' | translate}}</strong>

                </h5>
                <dl class="row" *ngFor="let patient of userDetails.alternate_contact_patient;let i = index" [ngStyle]="{'border-bottom': (userDetails.alternate_contact_patient.length-1!=i) ? '1px solid #e4e9f0' :  '' }">
                   <dt class="col-xl-5">{{'LABELS.USER_PROFILE_FIRST_NAME' | translate}}</dt>
                    <dd class="col-xl-7">{{patient.firstName}}</dd>
                    <dt class="col-xl-5">{{'LABELS.USER_PROFILE_LAST_NAME' | translate}}</dt>
                    <dd class="col-xl-7">{{patient.lastName}}</dd>
                     <dt class="col-xl-5" *ngIf="patient.email && patient.email!=''">{{'LABELS.USER_PROFILE_EMAIL' | translate}}</dt>
                    <dd class="col-xl-7" *ngIf="patient.email && patient.email!=''">{{patient.email}}</dd>
                    <dt class="col-xl-5" *ngIf="patient.mobile && patient.mobile!=''">{{'LABELS.CONTACT_NUMBER' | translate}}</dt>
                    <dd class="col-xl-7" *ngIf="patient.mobile && patient.mobile!=''">{{patient.mobile}}</dd>
                    <dt class="col-xl-5">{{'LABELS.RELATION_WITH_PATIENT' | translate}}</dt>
                    <dd class="col-xl-7">{{patient.relation}}</dd>
                </dl>
            </div>
        </section>

    </div>
    <div class="col-xl-8">
        <section class="card">
            <div class="card-block">
                <div class="nav-tabs-horizontal">
                    <ul class="nav nav-tabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" href="javascript: void(0);" data-toggle="tab" data-target="#posts" role="tab" (click)="resetAdmissionHandle()">
                                <i class="icmn-menu"></i> {{'LABELS.PERSONAL_INFORMATION' | translate}}
                            </a>
                        </li>
                        <li class="nav-item" *ngIf="showAdmissionTab">
                            <a class="nav-link" href="javascript: void(0);" data-toggle="tab" data-target="#admissions" role="tab">
                                <i class="icmn-profile"></i> {{ 'ADMISSION.LABELS.ADMISSION_DETAILS' | translate }}
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link" href="javascript: void(0);" data-toggle="tab" data-target="#settings" role="tab" (click)="resetAdmissionHandle()">
                                <i class="icmn-cog"></i> {{'LABELS.PROFILE_SETTINGS' | translate}}
                            </a>
                        </li>
                        <li class="nav-item" *ngIf="isOktaUserLogined && userDetails.config.enable_idm_authentication === '1' && isOktaWorkflowEnabled">
                            <a class="nav-link" href="javascript: void(0);" data-toggle="tab" data-target="#reset-password" role="tab" (click)="resetAdmissionHandle('reset-password')">
                                {{ 'LABELS.RESET_PASSWORD' | translate }}
                            </a>
                        </li>
                    </ul>
                    <div class="tab-content py-4">
                        <div class="tab-pane active" id="posts" role="tabcard">
                            <form class="new-form" [formGroup]="userProfile">
                                <h5 class="text-black mt-4 personal-info-title">
                                    <strong> {{'LABELS.PERSONAL_INFORMATION' | translate}}</strong>
                                    <span data-toggle="tooltip" data-placement="top" title="" data-original-title="Edit Personal Information"  (click)="updateProfile()" class="information-edit personal-information-edit-icon"><i class="icmn-pencil"></i></span>
                                </h5>
                                <div class="row">
                                    <div class="col-lg-6" *ngIf="displayNameFieldStatus">
                                        <div class="form-group">
                                            <label class="form-control-label" for="l0">{{'LABELS.USER_PROFILE_DISPLAY_NAME' | translate}}</label>
                                            <input type="text" xssInputValidate="{{'LABELS.USER_PROFILE_DISPLAY_NAME' | translate}}" class="form-control editable-field" formControlName="displayName" disabled (keyup)="checkTextPad()">
                                        </div>
                                        <div *ngIf="userProfile.controls['displayName'].hasError('required')&&(userProfile.controls.displayName?.dirty ||userProfile.controls.displayName?.touched)" class="alert alert-danger">
                                            {{'VALIDATION_MESSAGES.FIELD_CANNOT_BE_EMPTY' | translate}}
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="form-control-label" for="l1">{{'LABELS.USER_PROFILE_FIRST_NAME' | translate}}</label>
                                            <input [title]="+userDetails.group === 3 && editOptionEnabled ? ('MESSAGES.CHECK_PERMISSION' | translate:{ field: ('LABELS.USER_PROFILE_FIRST_NAME' | translate) }) : ''" type="text" xssInputValidate="{{'LABELS.USER_PROFILE_FIRST_NAME' | translate}}" [ngClass]="+userDetails.group !== 3 ? 'editable-field' : ''" class="form-control" id="l1" formControlName="firstName" disabled (keyup)="checkTextPad()">
                                        </div>
                                        <div *ngIf="userProfile.controls['firstName'].hasError('required')&&(userProfile.controls.firstName?.dirty ||userProfile.controls.firstName?.touched)" class="alert alert-danger">
                                            {{'VALIDATION_MESSAGES.FIELD_CANNOT_BE_EMPTY' | translate}}
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="form-control-label" for="l0">{{'LABELS.USER_PROFILE_LAST_NAME' | translate}}</label>
                                            <input type="text" [title]="+userDetails.group === 3 && editOptionEnabled ? ('MESSAGES.CHECK_PERMISSION' | translate:{ field: ('LABELS.USER_PROFILE_LAST_NAME' | translate) }) : ''" xssInputValidate="{{'LABELS.USER_PROFILE_LAST_NAME' | translate}}" [ngClass]="+userDetails.group !== 3 ? 'editable-field' : ''" class="form-control" id="l0" formControlName="lastName" disabled (keyup)="checkTextPad()">
                                        </div>
                                        <div *ngIf="userProfile.controls['lastName'].hasError('required')&&(userProfile.controls.lastName?.dirty ||userProfile.controls.lastName?.touched)" class="alert alert-danger">
                                            {{'VALIDATION_MESSAGES.FIELD_CANNOT_BE_EMPTY' | translate}}
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="form-control-label" for="l1">{{'LABELS.MOBILE_NUMBER' | translate}}</label>
                                            <div class="row">
                                                <div class="col-lg-2">
                                                     <input type="tel" placeholder="" class="form-control editable-field" style="width:0;" id="phone" disabled>
                                                </div>
                                                <div class="col-lg-3">
                                                        <label class="form-control ccode">+{{countryCode}}</label>
                                                </div>
                                                <div class="col-lg-5">
                                                     <input type="text" appPhoneNumberValidator [textMask]="{mask: maskPhoneNumber, guide: false}" xssInputValidate="{{'LABELS.MOBILE_NUMBER' | translate}}" class="form-control editable-field xl-short-reduce-pad" formControlName="mobileUser" id="mobileUser" disabled (blur)="checkMobileNumberVAlidation()" (keyup)="phoneNumberValidation()">
                                                     <input type="hidden" id="oldPhoneNumber" name="oldPhoneNumber">
                                                </div>
                                                <div  class="col-lg-2" *ngIf="!verifyPopup.verifyMobCodeSend && userProfile.controls.mobileUser.value !='' &&(userProfile.controls.mobileUser.valid || userProfile.controls.mobileUser.touched) && userDetails.config.enable_verification_of_cell_and_mobile == 1" >
                                                    <button type="button" class="btn btn-primary btn-sm" *ngIf="editOptionEnabled && userProfile.controls.mobileUser.valid && userProfile.controls.mobileVerified.value==0" (click)="verifyPopup.show_popup({mobile:true,formData:{countryCode:countryCode,pcellno:userProfile.controls.mobileUser.value}})">{{'LABELS.VERIFY' | translate}}</button>
                                                    <span class="btn btn-primary btn-sm" *ngIf="validNumber && userProfile.controls.mobileVerified.value ==1">{{'LABELS.VERIFIED' | translate}}</span>
                                                </div>
                                                <app-verify-em></app-verify-em>
                                            </div>
                                        </div>
                                        <div *ngIf="userProfile.controls.mobileUser.errors != null" class="alert alert-danger">
                                             {{'VALIDATION_MESSAGES.PLEASE_ENTER_VALID_MOBILE_NUMBER' | translate}}
                                        </div>

                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="form-group" >
                                            <label class="form-control-label" for="l0">{{'LABELS.USER_PROFILE_EMAIL' | translate}}</label> <i chToolTip="communicationEmail"></i>
                                            <div class="row">
                                                <div class="col-lg-10">
                                                    <input type="text" class="form-control editable-field" id="l0" formControlName="emailUser" disabled>
                                                </div>
                                                <div  class="col-lg-2" *ngIf="!verifyPopup.verifyEmailCodeSend && userProfile.controls.emailUser.value !='' &&(userProfile.controls.emailUser.valid || userProfile.controls.emailUser.touched) && userDetails.config.enable_verification_of_cell_and_mobile == 1" >
                                                    <button type="button" class="btn btn-primary btn-sm" *ngIf="editOptionEnabled && userProfile.controls.emailUser.valid && userProfile.controls.emailVerified.value==0" (click)="verifyPopup.show_popup({email:true,formData:{pemail:userProfile.controls.emailUser.value}})">Verify</button>
                                                    <span class="btn btn-primary btn-sm" *ngIf="userProfile.controls.emailVerified.value ==1">Verified</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div *ngIf="userProfile.controls['emailUser'].errors && userProfile.controls['emailUser'].errors.pattern && (userProfile.controls['emailUser'].dirty || userProfile.controls['emailUser'].touched)" class="alert alert-danger">
                                            {{'LABELS.SITE_DETAILS_CONTACT_EMAIL' | translate}}
                                        </div>
                                    </div>

                                    <div class="col-lg-6" >
                                        <div class="form-group">
                                            <label class="form-control-label" for="l0">{{'LABELS.LOGIN_USERNAME' | translate}}</label>
                                            <div class="row">
                                                <div class="col-lg-10">
                                                    <input type="text" [title]="+userDetails.group === 3 && editOptionEnabled ? ('MESSAGES.CHECK_PERMISSION' | translate:{ field: ('LABELS.LOGIN_USERNAME' | translate) }) : ''" class="form-control" id="l0" formControlName="alternateUsername" placeholder="{{'PLACEHOLDERS.USERNAME' | translate}}" disabled>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="form-control-label" for="l0">{{'LABELS.LANGUAGE_KNOWN' | translate}}</label>
                                            <select class="form-control select2 editable-field" data-placeholder="None Selected" formControlName="selLanguages" id="prof-languages" disabled multiple>
                                                <option *ngFor="let lang of languages" value="{{lang.name}}"> {{lang.name}} </option>
                                            </select>
                                        </div>
                                    </div>
                                    <!-- Site selection for multi-site enabled users -->
                                    <div class="col-lg-6" *ngIf="showSiteSelection">
                                        <div class="form-group"  >
                                            <label class="form-control-label" for="l0">{{'LABELS.PREFERRED_SITES' | translate}} <i chToolTip="profileSiteSelection"></i></label>
                                            <app-select-sites  [events]="eventsSubject.asObservable()"  (siteIds)="getSiteIds($event)" [selectedSiteIds]="selectedSitedIdsForEdit" [filterType]=true [disableFilter]="!editOptionEnabled" [hideApplyFilter]=true>
                                            </app-select-sites>
                                            <div class="alert alert-danger site-position" *ngIf="siteRequired">
                                                {{'VALIDATION_MESSAGES.SITE_PROFILE_VALID' | translate}}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div formArrayName="associated_user" *ngIf="userDetails.group == 3 && userProfile.get('associated_user') && userProfile.get('associated_user')['controls'] && userProfile.get('associated_user')['controls'].length >0" >

                                    <div class="row patients-header"  >
                                         <span >{{'LABELS.ON_BAHALF_OF' | translate}}</span>
                                    </div>
                                    <div [formGroupName]="j" *ngFor="let associatedUser of userProfile.controls.associated_user.controls; let j = index" class="row chatrooms-patient-name">

                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label class="form-control-label" >{{'LABELS.PATIENT_DISPLAY_NAME' | translate}}</label>
                                                    <input  formControlName="displayname" type="text" class="form-control " id=""  [value]="associatedUser.controls.displayname.value"  required disabled>
                                                </div>
                                                <div *ngIf="associatedUser.controls['displayname'].hasError('required')&&(associatedUser.controls.displayname?.dirty ||associatedUser.controls.displayname?.touched)" class="alert alert-danger">
                                                    {{'VALIDATION_MESSAGES.FIELD_CANNOT_BE_EMPTY' | translate}}
                                                </div>
                                            </div>
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label class="form-control-label" >{{'LABELS.PATIENT_FIRST_NAME' | translate}}</label>
                                                    <input formControlName="firstname" type="text" class="form-control " id="" [value]="associatedUser.controls.firstname.value" required disabled>
                                                </div>
                                                <div *ngIf="associatedUser.controls['firstname'].hasError('required')&&(associatedUser.controls.firstname?.dirty ||associatedUser.controls.firstname?.touched)" class="alert alert-danger">
                                                    {{'VALIDATION_MESSAGES.FIELD_CANNOT_BE_EMPTY' | translate}}
                                                </div>
                                            </div>
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label class="form-control-label" >{{'LABELS.PATIENT_LAST_NAME' | translate}}</label>
                                                    <input type="text" class="form-control " id="" formControlName="lastname"  [value]="associatedUser.controls.lastname.value" required disabled>
                                                </div>
                                                <div *ngIf="associatedUser.controls['lastname'].hasError('required')&&(associatedUser.controls.lastname?.dirty ||associatedUser.controls.lastname?.touched)" class="alert alert-danger">
                                                    {{'VALIDATION_MESSAGES.FIELD_CANNOT_BE_EMPTY' | translate}}
                                                </div>
                                            </div>
                                            <div class="col-lg-6">
                                                <div class="form-group">
                                                    <label class="form-control-label" >{{'LABELS.PATIENT_DATE_OF_BIRTH' | translate}}</label>
                                                    <input  formControlName="dob" id="" [value]="associatedUser.controls.dob.value" type="text" id="" class="form-control " placeholder="Patient Date of Birth" pattern="(0[1-9]|1[012])[- /.](0[1-9]|[12][0-9]|3[01])[- /.](19|20)\d\d" disabled>
                                                </div>
                                                <div *ngIf="associatedUser.controls['dob'].hasError('required')&&(associatedUser.controls.dob?.dirty ||associatedUser.controls.dob?.touched)" class="alert alert-danger">
                                                    {{'VALIDATION_MESSAGES.FIELD_CANNOT_BE_EMPTY' | translate}}
                                                </div>
                                            </div>

                                    </div>
                                </div>
                            <div class="form-actions personal-info-submit">
                                <div class="form-group">
                                    <button type="submit" class="btn width-200 btn-primary submit-profile" (click)="updateUserProfile()" [disabled]="!userProfile.valid">{{'BUTTONS.SUBMIT' | translate}}</button>
                                    <button type="button" (click)="cancelProfile()" class="btn btn-default">{{'BUTTONS.CANCEL' | translate}}</button>
                                </div>
                            </div>
                        </form>
                        </div>
                        <div class="tab-pane" id="admissions" role="tabcard" *ngIf="showAdmissionTab">
                            <div class="form-group" *ngIf="associatedUsers">
                                <label class="form-control-label" for="associate-patient">{{'TITLES.ASSOCIATE_PATIENT' | translate}}</label>
                                <select [(ngModel)]="selectedPatientForAdmission" class="form-control" id="associate-patient">
                                    <option *ngFor="let item of associatedUsers" [value]="item.patientId" id="associate-patient-{{ item.patientId }}">{{ item.firstName }} {{ item.lastName }}</option>
                                </select>
                            </div>
                            <app-admissions-table
                                [hidden]="selectedAdmission"
                                [selectedPatient]="selectedPatientForAdmission"
                                [events]="events"
                                [isEditable]="false"
                                (selectedItem)="selectedAdmissionHandle($event)"
                            >
                            </app-admissions-table>
                            <ng-container *ngIf="selectedAdmission">
                                <div class="patients-header no-padding">
                                <span>{{ 'ADMISSION.LABELS.VIEW_ADMISSION' | translate }}</span>
                                <span class="pull-right col-md-3">
                                    <a (click)="resetAdmissionHandle()" class="btn btn-sm btn-primary float-sm-right" id="back">
                                        <i class="fa fa-long-arrow-left" aria-hidden="true"></i>
                                        {{ 'BUTTONS.BACK' | translate }}
                                    </a>
                                </span>
                                </div>
                                <app-admission
                                    [admissionId]="selectedAdmission"
                                    [user]="userDetails"
                                    [isEditable]="false"
                                    (events)="onEvents($event)"
                                >
                                </app-admission>
                                <app-address [admissionId]="selectedAdmission" [isEditable]="false" [user]="userDetails"></app-address>
                            </ng-container>
                        </div>
                        <div class="tab-pane" id="settings" role="tabcard">
                            <div class="row">
                                <div class="col col-6">
                                    <h5 class="text-black mt-4">
                                        <strong>{{'LABELS.PERSONAL_SETTINGS' | translate}}</strong>
                                    </h5>
                                    <div class="row">
                                        <div class="col-lg-6">
                                            <div class="form-group">
                                                <label class="form-control-label" for="l0">{{'LABELS.NOTIFICATION_SOUND' | translate}}</label>
                                                <select [(ngModel)]="notificationSound" class="form-control notification-sound" (change)="setNotificationSound()">
                                                    <option value="aurora">{{'OPTIONS.NOTIFICATION_SOUND.AURORA' | translate}}</option>
                                                    <option value="bell">{{'OPTIONS.NOTIFICATION_SOUND.BELL' | translate}}</option>
                                                    <option value="chime">{{'OPTIONS.NOTIFICATION_SOUND.CHIME' | translate}}</option>
                                                    <option value="glass">{{'OPTIONS.NOTIFICATION_SOUND.GLASS' | translate}}</option>
                                                    <option value="ring">{{'OPTIONS.NOTIFICATION_SOUND.RING' | translate}}</option>
                                                    <option value="tritone">{{'OPTIONS.NOTIFICATION_SOUND.TRITONE' | translate}}</option>
                                                </select>
                                            </div>
                                            <a href="javascript:void(0)" class="audio-button" (click)="playAudio()"><i class="fa fa-volume-up" aria-hidden="true"></i></a>
                                        </div>
                                    </div>
        
                                    <div class="no-padding form-group row" [hidden]="userDetails.config.web_notification_hide_behaviour != '1'" >
                                        <label class="control-label col-md-12">{{'LABELS.ENABLE_AUTO_HIDE_WEB_NOTIFICATION' | translate}}
                                        </label>
                                        <div class="btn-group col-md-2">
                                            <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': userProfile.controls['enable_auto_hide_web_notifications'].value == '1'}"
                                                (click)="togglePreference('enable_auto_hide_web_notifications',1)">
                                                {{'BUTTONS.YES' | translate}}
                                            </button>
                                            <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': userProfile.controls['enable_auto_hide_web_notifications'].value != '1'}"
                                                (click)="togglePreference('enable_auto_hide_web_notifications',0)">
                                                {{'BUTTONS.NO' | translate}}
                                            </button>
                                        </div>
                                    </div>
                                    <div class="no-padding form-group row" [hidden]="(userDetails.config.patient_message_sms_notifcation_beyond_branch_24hr != 1 && userDetails.group == '3') || (userDetails.config.staff_message_sms_notifcation != 1 && userDetails.group != '3')" >
                                        <label class="control-label col-md-12">{{'LABELS.ENABLE_SMS_NOTIFICATION' | translate}}
                                        </label>
                                        <div class="btn-group col-md-2">
                                            <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': userProfile.controls['enable_sms_notifications'].value == '1'}"
                                                (click)="togglePreference('enable_sms_notifications',1)">
                                                {{'BUTTONS.YES' | translate}}
                                            </button>
                                            <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': userProfile.controls['enable_sms_notifications'].value != '1'}"
                                                (click)="togglePreference('enable_sms_notifications',0)">
                                                {{'BUTTONS.NO' | translate}}
                                            </button>
                                        </div>
                                    </div>
                                    <div class="no-padding form-group row" [hidden]="(userDetails.config.enable_email_for_patient != 1 && userDetails.group == '3') || (userDetails.config.enable_email_for_staff != 1 && userDetails.group != '3')" >
                                        <label class="control-label col-md-12">{{'LABELS.ENABLE_EMAIL_NOTIFICATION' | translate}}
                                            <i chToolTip="PREFET0025"></i>
                                        </label>
                                        <div class="btn-group col-md-2">
                                            <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': userProfile.controls['enable_email_notifications'].value == '1'}"
                                                (click)="togglePreference('enable_email_notifications',1)">
                                                {{'BUTTONS.YES' | translate}}
                                            </button>
                                            <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': userProfile.controls['enable_email_notifications'].value != '1'}"
                                                (click)="togglePreference('enable_email_notifications',0)">
                                                {{'BUTTONS.NO' | translate}}
                                            </button>
                                        </div>
                                    </div>
                                    <form class="form-horizontal" id="form-profile-pic-upload" enctype="multipart/form-data">
                                        <div class="row">
                                            <div class="col-lg-6">
                                                <h5 class="text-black mt-4">
                                                    <strong>{{'LABELS.PROFILE_AVATAR' | translate}}</strong>
                                                </h5>
                                                <div class="form-group profile-avatar-panel">
                                                    <label class="form-control-label" for="l6">{{'LABELS.FILE_UPLOAD' | translate}}</label>
                                                    <input type="file" name="myfile" id="myfile" (change)="fileChange($event)">
        
                                                </div>
                                                <div class="profile-avatar">
                                                    <a *ngIf="userDetails.avatar && userDetails.avatar != ''" (click)="deleteImage()">
                                                        <i class="fa fa-times-circle delete-image" aria-hidden="true"></i></a>
                                                    <img id="profileImageSmall" src="{{avatar}}" alt="Avatar" onerror="this.src='assets/img/file-404.png'">
                                                </div>
                                            </div>
                                        </div>
        
                                    </form>
                                    <div class="row" *ngIf="userDetails.config.enable_save_signature_to_profile === '1'">
                                        <div class="col-lg-6">
                                            <h5 class="text-black mt-4">
                                                <strong>{{ 'LABELS.PROFILE_SIGNATURE' | translate }}
                                                    <i chToolTip="addOrEditSignature"></i>
                                                </strong>
                                            </h5>
                                            <div *ngIf="!userSignImage" class="sign-pad mt-3" (click)="openChatSignature()">
                                                <img class="signature"
                                                    src="./assets/modules/dummy-assets/common/img/shc-logo.png">
                                            </div>
                                            <div class="sign-pad mt-3" *ngIf="userSignImage" (click)="openChatSignature()">
                                                <img class="signature" [src]="encodedUserSignature" alt="Signature" /></div>
                                            <div>
                                                <app-sign-pad (send)="sendSignature($event)"
                                                    [buttonSave]='true'></app-sign-pad>
                                            </div>
                                        </div>
                                    </div>
        
                                    <div class="row" *ngIf="userDetails.config.enable_save_signature_to_profile === '1'">
                                        <div class="col-lg-6 d-inline-flex align-items-center">
                                            <input type="checkbox" [checked]="useSavedSign" (change)="savedSignCheckbox()" [disabled]="!userSignImage">
                                            <label class="form-control-label ">{{'LABELS.USE_SAVED_SIGN_CHECKBOX' | translate}}</label>
                                        </div>
                                    </div>
        
                                </div>
                                <div class="col col-6"  *ngIf="+userDetails.group !== 3">
                                    <out-of-office (outOfOfficeDetails)="getOutOfOffice($event)"></out-of-office>
                                </div>
                            </div>
                             <div class="form-actions">
                                    <div class="form-group">
                                        <button type="submit" class="btn width-200 btn-primary submit-image"
                                        [disabled]="!showSubmitButton"  (click)="updateUserProfilePicture()" disabled>{{'BUTTONS.SUBMIT' |
                                            translate}}</button>
                                        <button type="button" (click)="cancelProfile()"
                                            class="btn btn-default">{{'BUTTONS.CANCEL' | translate}}</button>
                                    </div>
                                </div>
                        </div>
                        <div class="tab-pane w-50" id="reset-password" role="tabcard">
                            <h5 class="text-black mt-4 personal-info-title">
                                <strong> {{ 'LABELS.RESET_PASSWORD' | translate }}</strong>
                            </h5>
                            <reset-password (passwordDetails)="resetPassword($event)" [clearForm]="resetPasswordTabActive"></reset-password>
                         </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>
</section>
<!-- END: apps/profile -->
