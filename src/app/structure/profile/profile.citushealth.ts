import { Component, OnInit,ViewChild } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { RegistrationService } from '../registration/registration.service';
import { StructureService } from '../structure.service';
import createAutoCorrectedDatePipe from 'text-mask-addons/dist/createAutoCorrectedDatePipe';
import { FilterPipe} from './filter.pipe';
import { VerifyEmComponent } from "../shared/verifyEM/verify-em.component";
import {SharedService} from '../shared/sharedServices';
import { ToolTipService } from './../../structure/tool-tip.service';
import { Languages } from 'app/constants/languages';
import { CONSTANTS, UserGroup } from 'app/constants/constants';
import { convertTimeZoneDateTimeToUTCIso, getOooStatusBasedOnTime, isBlank, isPresent, removeBase64Prefix, removePhoneNumberMasking, setCountryCodeFlag} from 'app/utils/utils';
import { Subject } from 'rxjs';
import { SignPadComponent } from './../shared/signPad/sign-pad.component'

let moment = require('moment/moment');
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var swal: any;
declare var NProgress: any;

@Component({
  selector: 'cat-page',
  templateUrl: './profile.html',
  styleUrls: ['profile.css']
})

export class ProfileComponent implements OnInit {
  userProfile: FormGroup;
  userDetails:any ={}; 
  useSavedSign: boolean;
  encodedSignature;
  encodedUserSignature = null;
  userSign;
  enableSaveSignatureToProfile = 0;
  showSubmitButton: boolean;
  userSignImage = false;
  isSignpad = false;
  isSignChecked = false;
  isuserSign = false;
  email = localStorage.getItem('profileUsername');
  firstName;
  lastName;
  cellnumber;
  emailuser;
  displayName;
  zip;
  gender="";
  enable_auto_hide_web_notifications = 0;
  enable_sms_notifications = 1;
  enable_email_notifications = 0;
  mobileUser;
  dateOfBirth;
  signDocFile;
  countryCode = '';
  countryIsoCode = '';
  avatar;  
  avatarData;
  imagepath;
  formData;
  userId;
  displayNameFieldStatus:any=true;
  fileChangevalue=false;
  selectedLanguages;
  languages;
  autoCorrectedDatePipe: any = createAutoCorrectedDatePipe('mm/dd/yyyy');
  mask: any = [/\d/, /\d/, '/', /\d/, /\d/, '/', /\d/, /\d/, /\d/, /\d/];
  validNumber=false;
  verifiedEmailOrMobile=null;
  oldMobileNumber;
  editOptionEnabled=false;
  enableAlternameUsername = false;
  readonly maskPhoneNumber = CONSTANTS.phoneMask;

  notificationSound: string;
  readonly notificationDefaultSound = CONSTANTS.defaultAudio;
  @ViewChild(VerifyEmComponent) verifyPopup: VerifyEmComponent;
  @ViewChild(SignPadComponent) childpad: SignPadComponent;
   eventsSubject: Subject<void> = new Subject<void>();
   selectedSiteIds : string;
   selectedSitedIdsForEdit = "";
   showSiteSelection = false;
   siteRequired = false;
  selectedAdmission;
  associatedUsers;
  selectedPatientForAdmission;
  events;
  isOktaWorkflowEnabled = false;
  badgeStatusInfo;
  resetPasswordTabActive = false;
  outOfOffice: any = {
    startDate: '',
    endDate: '',
    isEnableOutOfOffice: false,
    startTime: false,
    endTime: '',
    statusMessage: '',
    startDateTime: '',
    endDateTime: '',
    invalidTime: false
  };
  isOktaUserLogined = true;
   constructor(private route: ActivatedRoute,
    private router: Router,
    private _formBuild: FormBuilder,
    public _structureService: StructureService,
     private registrationservice: RegistrationService,
     private _sharedService: SharedService,
     private _ToolTipService: ToolTipService,
  ) {
    this.userDetails = JSON.parse(this._structureService.userDetails);
    this.isOktaWorkflowEnabled = this._structureService.isOktaWorkflowEnabled();
    this.showSiteSelection =  (this.userDetails.enable_multisite == 1 && this.userDetails.group != 3 && this.userDetails.mySites.length > 1);
    if (this.showAdmissionTab) {
      this.associatedUsers = this.userDetails.alternate_contact_patient;
      this.selectedPatientForAdmission = !isBlank(this.associatedUsers) ? this.associatedUsers[0].patientId : this.userDetails.userId;
    }
    if(this.verifiedEmailOrMobile==undefined || this.verifiedEmailOrMobile==null){
      this.verifiedEmailOrMobile = this._sharedService.mobileOrEmailVerified.subscribe(
        (data) => {
            if(data.mobile){
              this.userProfile.patchValue({
                mobileVerified: 1
              });
            }else if(data.email){              
              this.userProfile.patchValue({
                emailVerified: 1
              });
            }
        }
      );
    }
  }
  ngOnInit() {
      this.isOktaUserLogined  = this._structureService.isOktaUserLogined();

    //$('#date-mask-input').mask("00/00/0000", {placeholder: "( mm/dd/yyyy) "}); 
    if (!isBlank(this.userDetails) && this.userDetails.enable_multisite == 1 && this.userDetails.group != 3) {
      if (this.userDetails.defaultSitesFilter && this.userDetails.defaultSitesFilter != 0) {
        this.selectedSitedIdsForEdit = this.userDetails.defaultSitesFilter.map(item=>{
          return item.id
        });
      } 
    } 
    this.languages = Languages.values;

      $('.select2').select2({
        placeholder: function(){
            $(this).data('placeholder');
      }});

    if (!(this.validateEmail(this.email))) {
      this.email = "";
    }
    if (this.userDetails.alternate_contact_patient && this.userDetails.alternate_contact_patient[0] && this.userDetails.alternate_contact_patient[0].email && !(this.validateEmail(this.userDetails.alternate_contact_patient[0].email))) {
      this.userDetails.alternate_contact_patient[0].email = "";
    }
      $('#prof-languages').on(
        'change',
        (e) => {
          var patientReminderTypes = [];
          var xy = $(e.target).val();
          if (xy) {
            xy.forEach(element => {
              var types = { name: "" };
              var id = element.substr(element.indexOf(":") + 1);
              id = id.replace(/'/g, "");
              types.name = id.replace(/\s/g, '');
              patientReminderTypes.push(types);
            });
            
            let val = patientReminderTypes.reduce((acc,obj)=>{
             return acc = acc + obj.name+', ';
            },'');
            this.selectedLanguages = val.trim().replace(/(^,)|(,$)/g, "");
            /* this.userProfile.patchValue({
              selLanguages: patientReminderTypes
            }); */
          }  
        }
      );
    setTimeout(()=>{
              $("#phone").intlTelInput();
              $("#phone").on("countrychange", (e, countryData)=> {
                   if(countryData.dialCode){
                     this.countryCode = countryData.dialCode;
                     this.countryIsoCode = countryData.iso2;
                     this.checkMobileNumberVAlidation();                    
                   }
                 });
                 this.getCountryCode();
            },1000);
    if(this.userDetails.group == 3 && this.userDetails.config.patient_name_display == '1'){
        this.displayNameFieldStatus=false;
    }
    setTimeout(function () {
            $('body').tooltip({selector: '[data-toggle="tooltip"]'});
    },100);
  
  $(".delete-image").tooltip({
      title: "Delete Profile Avatar"
    });

  
    this.userId=this.userDetails.userId;
    this.displayName = this.userDetails.displayName;
    this.firstName = this.userDetails.firstName;
    this.lastName = this.userDetails.secondName;
    this.cellnumber = this.userDetails.mobile;
    this.emailuser = this.email;
    this.zip = this.userDetails.zip;
    this.avatarData=this.userDetails.avatar;
    this.notificationSound=localStorage.getItem('notificationSound');
    if(this.userDetails.gender=="1"){
      this.gender="Male";
    }else if(this.userDetails.gender=="2"){
      this.gender="Female";
    }else{
      this.gender = "";
    }
    this.enable_auto_hide_web_notifications = this.userDetails.enable_auto_hide_web_notifications;
    this.enable_sms_notifications = this.userDetails.enable_sms_notifications;
    this.enable_email_notifications = this.userDetails.enable_email_notifications
    var dob = '';
    if (this.userDetails.dob) {
      var dateParts = this.userDetails.dob.split('-');
      dob = dateParts[1]+"/"+dateParts[2]+"/"+dateParts[0]
    }

    var cgdob = '';
    if (this.userDetails.caregiver_dob) {
      var dateParts_CG = this.userDetails.caregiver_dob.split('-');
      cgdob = dateParts_CG[1]+"/"+dateParts_CG[2]+"/"+dateParts_CG[0]
    }
    if (this.userDetails.associated_user) {
      this.userDetails.associated_user.map(function (associatedUser) { 
        if(associatedUser.dob){
          var dateParts_CG = associatedUser.dob.split('-');  
          if(dateParts_CG.length > 0) {              
            var dateOfBirth = dateParts_CG[1] + "/" + dateParts_CG[2] + "/" + dateParts_CG[0]; 
          
            associatedUser.dob = dateOfBirth;     
          }       
        }         
      });
    }
 this.userProfile = this._formBuild.group({
      displayName: [this.userDetails.caregiver_userid ? this.userDetails.patientDisplayName:this.userDetails.displayName, Validators.required],
      firstName: [this.userDetails.firstName, Validators.required],
      lastName: [this.userDetails.secondName, Validators.required],
      mobileUser: [removePhoneNumberMasking(this.userDetails.mobile)],
      emailUser: [(!this.userDetails.username.includes('unknown_')) ? this.userDetails.username : '' , Validators.compose([Validators.pattern(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/)])],
      alternateUsername: [this.userDetails.alternate_username],
      encodedUserSignature: [this.userDetails.savedUserSignature],
      useSavedSign: [this.userDetails.useSavedSign],
      selLanguages: [''],
      zip: [this.zip, Validators.pattern('[0-9]*')],
      languages: [Validators.required],
      gender: [this.userDetails.gender],
      careGiverdisplayName: [this.userDetails.caregiver_displayname ],
      careGiverfirstName: [this.userDetails.caregiver_firstname ],
      careGiverlastName: [this.userDetails.caregiver_lastname ],
      careGiverDOB: [ cgdob ],
      dateOfBirth : [ dob ],
      associated_user: this._formBuild.array([]),
      enable_auto_hide_web_notifications : [this.userDetails.enable_auto_hide_web_notifications],
      enable_sms_notifications: [this.userDetails.enable_sms_notifications],
      enable_email_notifications: [this.userDetails.enable_email_notifications],
      enableSaveSignatureToProfile : [this.userDetails.config.enable_save_signature_to_profile],
      mobileVerified :this.userDetails.userContactVerification.mobileVerified,
      emailVerified :this.userDetails.userContactVerification.emailVerified
    });

    if(!this.displayNameFieldStatus) {
      this.userProfile.controls['displayName'].setValidators(null);
      this.userProfile.controls['displayName'].updateValueAndValidity();
    }
    this.oldMobileNumber = this.countryCode+this.userDetails.mobile;
    if(this.userDetails.userContactVerification.mobileVerified==1){
      $("#oldPhoneNumber").val(this.countryCode+this.userDetails.mobile);
      this.validNumber=true;
    }
    this.selectedLanguages = this.userDetails.languages;
    if(localStorage.getItem("languages")){
      this.selectedLanguages = localStorage.getItem("languages");
    }
    let arr = this.selectedLanguages ? this.selectedLanguages.split(',').map(v=>v.trim()) : '';
    this.userProfile.patchValue({
      selLanguages: arr
    });

    if (this.userDetails.associated_user.length) {
      let control = <FormArray>this.userProfile.controls.associated_user['controls'];      
        for (let i = 0; i < this.userDetails.associated_user.length ; i++) {
         let element =this.userDetails.associated_user[i];
      const userDetails = this._formBuild.group({
      displayname: [element.displayname],
      firstname:[element.firstname],
      lastname:[element.lastname],
      dob:[element.dob]             
      });        
      control.push(userDetails);
      }
      
    }    
   
if(this.userDetails.avatar){
    this.avatarData=this.userDetails.avatar;
}else{
    this.avatarData="profile-pic-patient.png";
    this.userDetails.avatar="";
}
    this.notificationSound = localStorage.getItem('notificationSound'); 
    this.imagepath=this._structureService.getApiBaseUrl() +"citus-health/avatars/";
    this.avatar = this._structureService.getApiBaseUrl() +"citus-health/avatars/"+ this.avatarData;
    localStorage.setItem('imagepath',this.imagepath);
    $(function() {

      ///////////////////////////////////////////////////////////
      // ADJUSTABLE TEXTAREA
      autosize($('.adjustable-textarea'));

      ///////////////////////////////////////////////////////////
      // CALENDAR
      $('.example-calendar-block').fullCalendar({
        //aspectRatio: 2,
        height: 450,
        header: {
          left: 'prev, next',
          center: 'title',
          right: 'month, agendaWeek, agendaDay'
        },
        buttonIcons: {
          prev: 'none fa fa-arrow-left',
          next: 'none fa fa-arrow-right',
          prevYear: 'none fa fa-arrow-left',
          nextYear: 'none fa fa-arrow-right'
        },
        Actionable: true,
        eventLimit: true, // allow "more" link when too many events
        viewRender: function(view, element) {
          if (!(/Mobi/.test(navigator.userAgent)) && jQuery().jScrollPane) {
            $('.fc-scroller').jScrollPane({
              autoReinitialise: true,
              autoReinitialiseDelay: 100
            });
          }
        },
        eventClick: function(calEvent, jsEvent, view) {
          if (!$(this).hasClass('event-clicked')) {
            $('.fc-event').removeClass('event-clicked');
            $(this).addClass('event-clicked');
          }
        },
        defaultDate: '2016-05-12',
        events: [
          {
            title: 'All Day Event',
            start: '2016-05-01',
            className: 'fc-event-success'
          },
          {
            id: 999,
            title: 'Repeating Event',
            start: '2016-05-09T16:00:00',
            className: 'fc-event-default'
          },
          {
            id: 999,
            title: 'Repeating Event',
            start: '2016-05-16T16:00:00',
            className: 'fc-event-success'
          },
          {
            title: 'Conference',
            start: '2016-05-11',
            end: '2016-05-14',
            className: 'fc-event-danger'
          }
        ]
      });

      ///////////////////////////////////////////////////////////
      // SWAL ALERTS
      $('.swal-btn-success').click(function(e){
        e.preventDefault();
        swal({
          title: "Following",
          text: "Now you are following Artour Scott",
          type: "success",
          confirmButtonClass: "btn-success",
          confirmButtonText: "Ok"
        });
      });

      $('.swal-btn-success-2').click(function(e){
        e.preventDefault();
        swal({
          title: "Friends request",
          text: "Friends request was successfully sent to Artour Scott",
          type: "success",
          confirmButtonClass: "btn-success",
          confirmButtonText: "Ok"
        });
      });

    });
  
    if(this.email.includes('unknown_')&&!this.validateEmail(this.email) ) {
      this.userProfile.controls['emailUser'].setValue('');
    }

    const sessionSign = sessionStorage.getItem('savedUserSignature');
    const savedSign = this.userDetails.savedUserSignature;
    const signToConvert = sessionSign || savedSign;
    if (signToConvert) {
      this.userSignImage = true;
      this.encodedUserSignature = this._structureService.convertToBase64Img(signToConvert);
    }
    const useSignFromSession = sessionStorage.getItem('useSavedSign');
    this.useSavedSign = useSignFromSession ? JSON.parse(useSignFromSession) : this.userDetails.useSavedSign;      
}
initPatients(): FormGroup {
  return this._formBuild.group({
    displayname: [this.userDetails.associated_user[0].displayname],
    firstname:[this.userDetails.associated_user[0].firstname],
    lastname:[this.userDetails.associated_user[0].lastname],
    dob:[this.userDetails.associated_user[0].dob]
  });
}
  validateEmail(email) {
    var re = /^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
    if (re.test(email) && email.indexOf('unknown_') == -1) {
      return true;
    } else {
      return false;
    }
  }
  checkMobileNumberVAlidation(){
    var oldPhone = $("#oldPhoneNumber").val();
    var exactvalue = this.countryCode +this.userProfile.value['mobileUser'];
    if(oldPhone !=""){
      if(oldPhone != exactvalue){
        if(this.oldMobileNumber ==exactvalue && this.userDetails.userContactVerification.mobileVerified==1){
          this.userProfile.patchValue({
            mobileVerified: 1
          });
        }else{
          this.userProfile.patchValue({
            mobileVerified: 0
          });
        }
      }
    }else{
     $("#oldPhoneNumber").val(exactvalue);
    }
  }
  phoneNumberValidation(){
    if(this.userProfile.controls.mobileUser.errors == null){
      this.validNumber =true;
      this.checkMobileNumberVAlidation();
    }else{
      this.validNumber =false;
    }
  }
  
   getCountryCode() {
    const countryDetails = setCountryCodeFlag('phone', this.userDetails.countryCode, this.userDetails.countryIsoCode);
    this.countryCode = countryDetails.countryCode.replace(/\+/g, '');
    this.countryIsoCode = countryDetails.countryIsoCode;
   }

  playAudio() {
    const audioPath = CONSTANTS.audioPath + this.notificationSound + CONSTANTS.audioType;
    const audio = new Audio(audioPath);
    audio.play();
  }

  fileChange(event) {
    let fileList: FileList = event.target.files;
    this.signDocFile = fileList[0];
    this.formData = new FormData();
    this.formData.append('myfile', this.signDocFile);
    $(".profile-avatar").addClass("profile-avatar-disabled");
    $(".submit-image").prop("disabled", false);
    var type = fileList[0].type;
    this.fileChangevalue=true;
  }
  updateUserProfilePicture(){
    this.updateUserProfile(true);
    var sound = $(".notification-sound  :selected").val();
    localStorage.setItem('notificationSound', sound);
    if(this.fileChangevalue){
               
               this.userDetails = JSON.parse(this._structureService.userDetails);
               var self = this;            
         var userProfilePicUploadUrl =this._structureService.apiBaseUrl+'citus-health/'+this._structureService.version+"/profile-upload.php";
                NProgress.start();      
                       $.ajax({
                          url: userProfilePicUploadUrl,
                          type: "POST",
                          data: this.formData,
                          processData: false, // tell jQuery not to process the data
                          contentType: false, // tell jQuery not to set contentType,
                          headers: {
                              "Authentication-Token": this.userDetails.authenticationToken
                          },
                          progress: function(e) {
                            
                          }
                      }).done(function(data) {
                        var notify = $.notify(' Success! Profile details updated');
                        setTimeout(function() {
                        notify.update({'type': 'success', 'message': '<strong>  Success! Profile details updated. </strong>'});
                        }, 1000);
                        var fields = data.split('_');
                        var activityData = {
                          activityName: "Update User Details",
                          activityType: "user access",
                          activityDescription: "User profile picture updated (" +  self.userDetails.displayName + ")"
                        };
                        self._structureService.trackActivity(activityData);
                         // this.avatarData="profile-pic-patient.png";
                       
                         var oldPath=$("#profileImageSmall").attr("src");
                          var path=localStorage.getItem('imagepath');
                          var thumbPath = path + 'thumbs/';
                        $("#profileImageSmall").attr("src", path+fields[0]);
                         
                        this.userDetails = JSON.parse(self._structureService.userDetails);
                        self.userDetails = JSON.parse(self._structureService.userDetails);
                        this.avatar=path+fields[0];
                        this.thumbAvatar=thumbPath+fields[0];
                        self.avatar=path+fields[0];
                        //self.thumbAvatar=thumbPath+fields[0];

                        //if(this.userDetails.avatar){
                          this.userDetails.avatar=fields[0];
                          self.userDetails.avatar=fields[0];
                        //}
                        
                        self._structureService.setCookie('profileImageThumbUrl',  this.thumbAvatar, 1);
                        self._structureService.setCookie('profileImageUrl',  this.avatar, 1);
                        this.userDetails.profileImageThumbUrl=this.avatar;
                        this.userDetails.profileImageUrl=this.avatar;
                        self.userDetails.profileImageThumbUrl=self.avatar;
                        self.userDetails.profileImageUrl=self.avatar;

                        
                        self._structureService.userDetails = JSON.stringify(self.userDetails);
                         $("#profile-img").attr("src", path+fields[0]);
                          $(".cat__top-bar__avatar img").attr("src", path+fields[0]);
                        $(".profile-avatar").removeClass("profile-avatar-disabled");
                      });
             NProgress.done();
    }
 }

  deleteImage(){
          swal({
        title: "Are you sure?",
        text: "You will not be able to recover this Avatar",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true
      },() => {
     
        var path=localStorage.getItem('imagepath');
        $("#profileImageSmall").attr("src", path+'profile-pic-patient.png');
        this.avatar=path+'profile-pic-patient.png';
        $(".cat__top-bar__avatar img").attr("src", path+'profile-pic-patient.png');
        var userData={};//userID will be fetched from session value in the API //var userData={"userId":this.userId};
          this.registrationservice.deleteAvatar(userData).then((data) => {
            this.avatarData="profile-pic-patient.png";
           this.avatar = this._structureService.getApiBaseUrl() +"citus-health/avatars/"+ this.avatarData;

           this.userDetails.avatar='';
           this.userDetails.profileImageThumbUrl=this.avatar;
           this.userDetails.profileImageUrl=this.avatar;
         
            this._structureService.userDetails = JSON.stringify(this.userDetails);

           this._structureService.setCookie('profileImageThumbUrl', this.avatar , 1);
           this._structureService.setCookie('profileImageUrl',  this.avatar , 1);
          }).catch((ex) => {});


      });
    
  }
  ConfirmdeleteImage(){
     var path=localStorage.getItem('imagepath');
        $("#profileImageSmall").attr("src", path+'profile-pic-patient.png');
         $('#deleteImageConfirm').modal('hide');
  }
  

  setNotificationSound(){
       $(".submit-image").prop("disabled", false);
  }

  /**
   * Redirect to inbox when click on cancel button
   */
  cancelProfile(){
    this.router.navigate(['/inbox']);
  }
  
  togglePreference(preference, value) {
    $(".submit-image").prop("disabled", false);
    if (preference === 'enable_auto_hide_web_notifications') {
      this.userProfile.patchValue({
        enable_auto_hide_web_notifications: value
      });
    }
    if(preference === 'enable_sms_notifications') {
      this.userProfile.patchValue({
        enable_sms_notifications: value
      })
    }
    if(preference === 'enable_email_notifications') {
      this.userProfile.patchValue({
        enable_email_notifications: value
      })
    }
  }
  get showAdmissionTab(): boolean {
    const userData = this._structureService.getUserdata();
    return this._structureService.isMultiAdmissionsEnabled && userData && +userData.group === UserGroup.PATIENT;
  }
  updateUserProfile(fromProfile = false){
    if(!this.siteRequired){
      this.displayName = this.userDetails.displayName;
      this.firstName = this.userDetails.firstName;
      this.lastName = this.userDetails.secondName;
      this.cellnumber = this.userDetails.mobile;
      this.zip = this.userDetails.zip;
      this.emailuser = this.email;
      this.enable_auto_hide_web_notifications = this.userDetails.enable_auto_hide_web_notifications;
      this.enable_sms_notifications = this.userDetails.enable_sms_notifications;
      this.enable_email_notifications = this.userDetails.enable_email_notifications;
      let mobileNumberChanged=false;
      let emailIdVerified =false;
      if(this.userDetails.mobile != this.userProfile.value['mobileUser'] || this.userDetails.countryCode !='+'+this.countryCode){
          mobileNumberChanged =true;
      }else if(this.userDetails.userContactVerification.mobileVerified !=this.userProfile.value['mobileVerified']){
        mobileNumberChanged =true;
      }
      if(this.userDetails.userContactVerification.emailVerified !=this.userProfile.value['emailVerified']){
        emailIdVerified =true;
      }
      if(this.userProfile.value['gender'] && this.userProfile.value['gender']!=""){
        this.userDetails.gender=this.userProfile.value['gender'];
        if(this.userProfile.value['gender']=="1"){
          this.gender="Male";
        }else if(this.userProfile.value['gender']=="2"){
          this.gender="Female";
        }else{
          this.gender = "";
        }
      }else{
        this.gender = "";
        this.userDetails.gender = "";
      }
      if(this.userProfile.value['displayName']){
           this.displayName = this.userProfile.value['displayName'];
           if(this.userDetails.caregiver_userid){
              this.userDetails.displayName= this.userDetails.caregiver_displayname+" ("+this.userProfile.value['displayName']+")";
           }else{
             this.userDetails.displayName=this.userProfile.value['displayName'];
           }
      }
      if(this.userProfile.value['firstName']){
        this.firstName = this.userProfile.value['firstName'];
        this.userDetails.firstName =this.userProfile.value['firstName']
      }
      if(this.userProfile.value['lastName']){
        this.lastName = this.userProfile.value['lastName'];
        this.userDetails.secondName =this.userProfile.value['lastName']
      }
      if(!this.displayNameFieldStatus) {
        this.displayName = this.firstName+" "+this.lastName
        if(this.userDetails.caregiver_userid){
           this.userDetails.displayName= this.userDetails.caregiver_displayname+" ("+this.displayName+")";
        }else{
          this.userDetails.displayName=this.displayName;
        }
      }
      this.cellnumber = this.userProfile.value['mobileUser'];
      this.userDetails.mobile = this.userProfile.value['mobileUser'];
        
      if (this.userProfile.value['zip']) {
        this.zip = this.userProfile.value['zip'];
        this.userDetails.zip = this.userProfile.value['zip']
      }     
      if(this.userProfile.value['emailUser']){
        this.emailuser = this.userProfile.value['emailUser'];
        this.email =this.userProfile.value['emailUser'];
        this.userDetails.username = this.email;
      }
      if (this.userDetails.alternate_username) {
        this.emailuser = this.userProfile.value['emailUser'];
        this.email = this.userProfile.value['emailUser'];
        this.userDetails.username = this.userProfile.value['emailUser'];
        localStorage.setItem("profileUsername", this.email);
      }
      if(mobileNumberChanged){
        this.userDetails.userContactVerification.mobileVerified = this.userProfile.value['mobileVerified'];
      }
      if(emailIdVerified){
        this.userDetails.userContactVerification.emailVerified = this.userProfile.value['emailVerified'];
      }
      
        this.enable_auto_hide_web_notifications = this.userProfile.value['enable_auto_hide_web_notifications'];
        this.userDetails.enable_auto_hide_web_notifications =this.userProfile.value['enable_auto_hide_web_notifications'];
        this.enable_sms_notifications = this.userProfile.value['enable_sms_notifications'];
        this.userDetails.enable_sms_notifications = this.userProfile.value['enable_sms_notifications'];
        this.enable_email_notifications = this.userProfile.value['enable_email_notifications'];
        this.userDetails.enable_email_notifications = this.userProfile.value['enable_email_notifications'];
        
      if(this.userProfile.value['dateOfBirth']){

        this.dateOfBirth = this.userProfile.value['dateOfBirth'];
        var patient_dob = moment(this.dateOfBirth).format('YYYY-MM-DD');
        this.userDetails.dob = patient_dob;
        
      }
      this.userDetails.selLanguages = this.selectedLanguages;
      this.countryCode.replace("+","");
      this.userDetails.countryCode = '+'+ this.countryCode;
      this.userDetails.countryIsoCode = this.countryIsoCode;
      var cg_updateUserData = {};
      if(this.userDetails.caregiver_userid){
        var cg_dob = moment(this.userProfile.value.careGiverDOB).format('YYYY-MM-DD');
        cg_updateUserData = { 
                      "displayName": this.userProfile.value.careGiverdisplayName, 
                      "firstName": this.userProfile.value.careGiverfirstName, 
                      "lastName": this.userProfile.value.careGiverlastName, 
                      "username": this.userDetails.caregiver_username, 
                      "userId": this.userDetails.caregiver_userid, 
                      "dob": cg_dob, 
                    };
      }
      
     let updateUserData:any = {"languages": this.selectedLanguages,"displayName": this.displayName, "firstName": this.firstName, "lastName": this.lastName, "username": this.emailuser, "userId": this.userDetails.userId, "tenantKey": this.userDetails.tenantKey, "mobile": this.cellnumber, "dob": this.userDetails.dob, "address": "", "city": "", "state": "", "country": "", "zip": this.zip,"caregiverName":"","hasFamilyCare":"0","countryCode":'+'+this.countryCode, 'countryIsoCode': this.countryIsoCode, gender:this.userDetails.gender, "careGiverData" : cg_updateUserData, "enable_auto_hide_web_notifications" : this.enable_auto_hide_web_notifications, "enable_sms_notifications" : this.enable_sms_notifications, "enable_email_notifications" : this.enable_email_notifications ,"defaultSitesFilter":this.selectedSiteIds };

      if (this.userDetails.group !== 3 && (this.outOfOffice.isEnableOutOfOffice || !isBlank(this.outOfOffice.statusMessage))) {
        if (!this.validateOutOfOfficeDates() || this.outOfOffice.invalidTime) {
          return false;
        }
        if ((isPresent(this.outOfOffice.startDate) && isBlank(this.outOfOffice.startTime)) ||
            (isPresent(this.outOfOffice.endDate) && isBlank(this.outOfOffice.endTime))) {
          this._structureService.notifyMessage({
            messge: this._ToolTipService.getTranslateData('MESSAGES.OUT_OF_OFFICE_REQUIRED'),
            delay: 1000,
            type: 'warning'
          });
          return false;
        }
      }

      let userSignature;

      if (this.isSignChecked) {
        userSignature = this.encodedSignature;
        if (this.isuserSign) {
          updateUserData.useSavedSign = this.useSavedSign;
        }
      } else {
        updateUserData.useSavedSign = this.isuserSign ? this.useSavedSign : undefined;
        userSignature = this.isSignChecked ? this.encodedSignature : undefined;
      }

      updateUserData.encodedUserSignature = userSignature || undefined;

      const outOfOfficeInfo = {
        isOutOfOffice: this.outOfOffice.isEnableOutOfOffice,
        message: this.outOfOffice.statusMessage,
        startDateTime: isPresent(this.outOfOffice.startDateTime) ? this.outOfOffice.startDateTime : '',
        endDateTime: isPresent(this.outOfOffice.endDateTime) ? this.outOfOffice.endDateTime : ''
      };
      updateUserData.outOfOfficeInfo = outOfOfficeInfo;

      if(mobileNumberChanged){
        updateUserData.mobileVerified = (this.userProfile.value['mobileVerified']==1)?1:2;
      }
      if(emailIdVerified){
        updateUserData.emailVerified = (this.userProfile.value['emailVerified']==1)?1:2;
      }
      if(this.userProfile.value['alternateUsername']){
        updateUserData.alternate_username = this.userProfile.value['alternateUsername'];
      }
      updateUserData.notificationSoundName = !isBlank(this.notificationSound) ? this.notificationSound : CONSTANTS.defaultAudio;
      let profileUpdate = true;     // passing as true for identifying this call for user update
      //calling data updating api from user profile
      NProgress.start();
      this._structureService.updateUserProfile(updateUserData, profileUpdate,'').then((data) => {      
        NProgress.done();
        let res : any = data;
        if(res && res["updateUser"] && res["updateUser"].updateStatus === "success" && res["updateUser"].id) {
          if (this.userDetails.mySites.length > 1 && this.selectedSiteIds) {
            const selectedSites = this.userDetails.mySites.filter(item => this.selectedSiteIds.includes(item.id));
            if(!isBlank(selectedSites)){
              this.userDetails.defaultSitesFilter = this.userDetails.mySites.length === selectedSites.length ? 0 : selectedSites;
            }else{
              this.userDetails.defaultSitesFilter = 0;
            }
          }
          //update user details with updated data if user details update was successfull
          this.userDetails.oooInfo = outOfOfficeInfo;
          this._structureService.userDetails = JSON.stringify(this.userDetails);
        this.userDetails.languages = this.selectedLanguages;
        localStorage.setItem("languages",this.selectedLanguages);
        //set new sound to local storage
        localStorage.setItem('notificationSound', this.notificationSound); 
      this.isSignChecked = false;
      this.isuserSign = false;
      if (res["updateUser"].savedUserSignature) {
        sessionStorage.setItem('savedUserSignature', res["updateUser"].savedUserSignature);
      }
      sessionStorage.setItem('useSavedSign', this.useSavedSign.toString());

      var activityData = {
        activityName: "Update User Details",
        activityType: "user access",
        activityDescription: "User account updated (" +  this.userDetails.displayName + ")"
      };
      this._structureService.trackActivity(activityData);
      $(".profile-name span.displayNameProfile").html(this.displayName);
      this._structureService.setCookie('displayname', this.displayName, 1);
      this._structureService.notifyMessage({
        type: 'success',
        message: this._ToolTipService.getTranslateData('MESSAGES.SUCCESS_PROFILE_DETAIL_UPDATE')
      });

      $(".personal-info-submit").css("display","none");
      NProgress.done();
      $('.editable-field').prop("disabled", true);
      this.editOptionEnabled =false;
    } else if(res["updateUser"].updateStatus === "Invalid Params" && !res["updateUser"].id){
      //Reset data with user details if the update is failed.
      this.userDetails = JSON.parse(this._structureService.userDetails);  
      $(".personal-info-submit").css("display","block");
      $('.editable-field').prop("disabled", false);
      this.editOptionEnabled =true;
      this._structureService.notifyMessage({
        messge: this._ToolTipService.getTranslateData('MESSAGES.ERROR_MSG_INVALID_INPUT'),
        delay: 1000,
        type: 'warning'
      });
      let activityData = {
        activityName: "Update User Details",
        activityType: "user access",
        activityDescription: `Error occured while updating user details : Due to invalid input data.`            
      };
      this._structureService.trackActivity(activityData);         
    } else {
      this._structureService.notifyMessage({
        messge: res["updateUser"].updateStatus,
        delay: 1000,
        type: 'warning'
      });
    }
    }).catch((ex) => {
      this._structureService.notifyMessage({
        messge: this._ToolTipService.getTranslateData('MESSAGES.ERROR_MSG_EXCEPTION_CATCH'),
        delay: 1000,
        type: 'warning'
      });
    });
  }
}
  updateProfile(){
    $(".personal-info-submit").css("display","block");
     $('.editable-field').prop("disabled", false);
     this.editOptionEnabled =true;
 }

 checkTextPad(){
   //$(".submit-profile").prop("disabled", false);
 }
 getSiteIds(siteData) {
  if (+siteData.siteId !== 0) {
    this.siteRequired = false;
  } else {
    this.siteRequired = true;
  }
  if (siteData.siteId.length === this.userDetails.mySites.length) {
    this.selectedSiteIds = "0";
  } else {
    this.selectedSiteIds = siteData.siteId.toString();
  }
 }
 openChatSignature() {
  this.isSignChecked = true;
  this.childpad.show_popup();
  }
  selectedAdmissionHandle(event) {
    if (event.admission && event.admission.admissionId) {
      this.selectedAdmission = event.admission.admissionId;
    }
  }
  resetAdmissionHandle(tab?: string) {
    this.onEvents({ admissionEvents: { cancel: true } });
    this.resetPasswordTabActive = tab === 'reset-password';
  }
  onEvents(event) {
    if (event && event.admissionEvents && event.admissionEvents.cancel) {
      this.selectedAdmission = null;
      this.events = event;
    }
  }
  sendSignature(data) {
    this.showSubmitButton = true;
    let previousSign = this.encodedUserSignature;
    this.encodedSignature = removeBase64Prefix(data.signature);

    if (data.close) {
      if (isBlank(data.signature)) {
        if (previousSign === null) {
          this.userSignImage = false;
        } else {
          this.encodedUserSignature = previousSign;
          this.userSignImage = true;
        }
      } else {
        this.encodedUserSignature = previousSign;
        this.userSignImage = true;
      }
    } else if (!isBlank(data.signature)) {
      this.isSignpad = true;
      this.userSignImage = true;
      this.encodedUserSignature = data.signature;
    } else {
      if (previousSign === null) {
        this.userSignImage = false;
        if (!data.close) {
          this._structureService.notifyMessage({
            messge: this._ToolTipService.getTranslateData('VALIDATION_MESSAGES.DRAW_SIGN_VALIDATION')
          });
        }
      } else {
        this.encodedUserSignature = previousSign;
        this.userSignImage = true;
      }
    }
  }
  savedSignCheckbox() {
    this.isuserSign = true;
    this.showSubmitButton=true;
    this.useSavedSign = !this.useSavedSign;
  }
  getOutOfOffice(data){
    this.outOfOffice = data;
    if(this.outOfOffice.statusMessage || isPresent(this.outOfOffice.isEnableOutOfOffice)){
      this.showSubmitButton = true;
    } else {
      this.showSubmitButton = false;
    }
  }

   validateOutOfOfficeDates(): boolean {
    const startDateTime = convertTimeZoneDateTimeToUTCIso(moment(this.outOfOffice.startDate).format('YYYY-MM-DD'), moment(this.outOfOffice.startTime, 'hh:mm A').format('HH:mm:ss'), moment.tz.guess());
    this.outOfOffice.startDateTime = isPresent(moment(startDateTime).toISOString()) ? moment(startDateTime).toISOString() : '';
  
    const endDateTime = convertTimeZoneDateTimeToUTCIso(moment(this.outOfOffice.endDate).format('YYYY-MM-DD'), moment(this.outOfOffice.endTime, 'hh:mm A').format('HH:mm:ss'), moment.tz.guess());
    this.outOfOffice.endDateTime = isPresent(moment(endDateTime).toISOString()) ? moment(endDateTime).toISOString() : '';
  
    const start = moment(this.outOfOffice.startDateTime);
    const end = moment(this.outOfOffice.endDateTime);
  
    if (start.isAfter(end)) {
      this._structureService.notifyMessage({
        messge: this._ToolTipService.getTranslateData('MESSAGES.VALIDATE_DATE_TIME'),
        delay: 1000,
        type: 'warning'
      });
      return false;
    }
    return true;
  }
   
 resetPassword(passwordData) {
  this.registrationservice.resetUserPassword(passwordData,true);
 }
}
