.profile-avatar {
    width: 150px;
    height: 150px;
    border: 1px solid #ddd;
    border-radius: 50%;
    overflow: hidden;
    margin-top: 20px;
}

.profile-avatar img {
    height: 100%;
    object-fit: cover;
}

img#profileImageSmall {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-avatar a {
    z-index: 999;
    position: absolute;
    left: 122px;
    font-size: 23px;
    top: 100px;
    color: #5394a2;
}

.profile-avatar a i {
    background: #fff;
    border-radius: 50%;
    height: 20px;
    cursor: pointer;
}

.information-edit i {
    float: right;
    cursor: pointer;
    color: #0190fe;
    width: 30px;
    height: 30px;
    text-align: center;
    border-radius: 50%;
    padding-top: 6px;
}

.personal-information-edit-icon {
    float: left;
    position: absolute;
    padding-left: 5px;
    top: 90px;
}

.information-edit i:hover {
    background: #0190fe !important;
    color: #fff;
}

.personal-info-title {
    height: 35px;
}

.personal-info-submit {
    display: none;
}

.profile-avatar-disabled img {
    opacity: 0.3;
    cursor: no-drop;
}

.icmn-pen {
    cursor: pointer;
}

.sign-pad {
    border: 2px dashed #228072;
    margin: 0 0 2% 0;
    float: left;
    cursor: pointer;
    position: relative;
}

.form-control-label {
    margin-left: 0.4rem;
    margin-top: 0.8rem
}
.signature{
    top: 97.5845px;
    left: 107.797px;
    height: 45.7923px;
    width: 146.25px;
    padding: 5px;
}
@media (min-width: 1200px) and (max-width: 1265px) {
    .xl-short-reduce-pad {
        padding: .78rem .75rem;
    }
}
.w-50 {
    width: 50%;
}