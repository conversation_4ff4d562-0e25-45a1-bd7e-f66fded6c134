import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';

import { RouterModule, Routes } from '@angular/router';
import { AuthGuard } from '../../guard/auth.guard';
import { SharedModule } from '../shared/sharedModule';
import { CampaignManagerComponent } from './campaign-manager.component';

export const routes: Routes = [
  {
    path: 'campaign-manager',
    canActivate: [AuthGuard],
    data: {
      checkRoutingPrivileges: 'mangeCampaign'
    },
    children: [{ path: '**', component: CampaignManagerComponent }]
  }
];

@NgModule({
  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)],
  declarations: [CampaignManagerComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class CampaignManagerModule {}
