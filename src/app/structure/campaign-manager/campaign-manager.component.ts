import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, ViewEncapsulation } from '@angular/core';
import { StructureService } from '../structure.service';

@Component({
  selector: 'app-campaign-manager',
  templateUrl: './campaign-manager.component.html',
  styleUrls: ['./campaign-manager.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CampaignManagerComponent implements OnInit, OnDestroy {
  userData: any;
  constructor(public structureService: StructureService) {}

  ngOnInit(): void {
    this.userData = JSON.parse(this.structureService.userDetails);
    this.structureService.loadMicroFrontend();
  }

  ngOnDestroy(): void {
    this.structureService.unLoadMicroFrontend();
  }
}
