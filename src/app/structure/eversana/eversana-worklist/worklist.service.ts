import { Injectable, OnInit } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import { ApolloClient, createNetworkInterface } from 'apollo-client';
import { Apollo } from 'apollo-angular';
import { StructureService } from '../../structure.service';
import { FormsService } from '../../forms/forms.service';
import { Router, ActivatedRoute, Params, NavigationEnd } from '@angular/router';
import gql from 'graphql-tag';
import { configTimeZone } from '../../../../environments/environment';

declare var $: any;
declare var jQuery: any;

const getWorklistFormEntriesWithFilter = gql`
query getFormDataWithFilters(
  $sessionId: String!,
  $formID:Int,
  $end:Int,
  $start:Int,
  $fromlabel: Int,
  $fromCSS: Int,
  $filterModel: [filter],
  $sortModel: [sortArray]
){
  getFormDataWithFilters(sessionId:$sessionId, formID:$formID, start: $start, end: $end, fromlabel: $fromlabel, fromCSS: $fromCSS, filterModel: $filterModel, sortModel:$sortModel){ 
    submissionID
    total
    elements{
      tid
      label
      labelText
      value
      valueType
      valueOther
      timestampForDate
      OtherExists
    }
  }
}
`;
const getWorklistFormEntries = gql`
query getFormData(
  $sessionId: String!,
  $formID:Int,
  $end:Int,
  $start:Int,
  $fromlabel: Int,
  $fromCSS: Int
){
  getFormData(sessionId:$sessionId, formID:$formID, start: $start, end: $end, fromlabel: $fromlabel, fromCSS: $fromCSS){ 
    submissionID
    elements{
      tid
      label
      labelText
      value
      valueType
      valueOther
      timestampForDate
    }
  }
}
`;

const getWorklistFormEntriesByGroupWithFilter = gql`
query getFormDataWithUniqueIDWithFilters(
  $sessionId: String!,
  $formID:Int,
  $uniqueId: String!,
  $end:Int,
  $start:Int,
  $fromlabel: Int,
  $fromCSS: Int,
  $filterModel: [filter],
  $sortModel: [sortArray]
){
  getFormDataWithUniqueIDWithFilters(sessionId:$sessionId, formID:$formID, uniqueId: $uniqueId, start: $start, end: $end, fromlabel: $fromlabel, fromCSS: $fromCSS, filterModel: $filterModel, sortModel:$sortModel){ 
    submissionID
    total
    elements{
      tid
      label
      labelText
      value
      valueType
      valueOther
      timestampForDate
      OtherExists
    }
  }
}
`;
const getWorklistFormEntriesByGroup = gql`
query getFormDataWithUniqueID(
  $sessionId: String!,
  $formID:Int,
  $uniqueId: String!,
  $end:Int,
  $start:Int,
  $fromlabel: Int,
  $fromCSS: Int
){
  getFormDataWithUniqueID(sessionId:$sessionId, formID:$formID, uniqueId: $uniqueId, start: $start, end: $end, fromlabel: $fromlabel, fromCSS: $fromCSS){ 
    submissionID
    elements{
      tid
      label
      labelText
      value
      valueType
      valueOther
      timestampForDate
    }
  }
}
`;

const getWorklistFormElementIds = gql`
query getFormDataWithUniqueID(
  $sessionId: String!,
  $formID:Int,
  $uniqueId: String!
){
  getFormDataWithUniqueID(sessionId:$sessionId, formID:$formID, uniqueId: $uniqueId){ 
    submissionID
    elements{
      tid
      label
      labelText
    }
  }
}
`;
const getMachformFields = gql`
query getFormElementDetails(
  $sessionId: String!,
  $formID:Int,
  $elementId: Int
){
  getFormElementDetails(sessionId:$sessionId, formID:$formID, elementId: $elementId){ 
    type
    options{
      optionId
      optionValue
    }
    hasOtherOption
    otherLabelText
  }
}
`;
const getWorklistFiles = gql`
query getFormData(
  $sessionToken: String!,
  $formID: Int,
  $submissionId: Int
  $elementType: String
){
  getFormData(sessionId:$sessionToken, formID:$formID, submissionId:$submissionId, elementType:$elementType){
    submissionID
    elements{
      tid
      label
      labelText
      value
      valueType
      valueOther
      timestampForDate
      fileValues{
        name
        dbname
        pathTofile
      }
      filesCount
    }
  }
}`;
const getAllFormDataMultipleForm = gql`
query getAllFormDataMultipleForm(
  $sessionToken: String!,
  $formID: Int,
  $mappingField: String,
  $mappingFieldValue: String
){
  getAllFormDataMultipleForm(sessionId:$sessionToken, formID:$formID, mappingField:$mappingField, mappingFieldValue:$mappingFieldValue){
    submissionID
    elements{
      tid
      label
      labelText
      value
      tableValue
      timestampForDate
      valueOther
      valueType
      keyName
      filesCount
    }
  }
}`;

const mutDeleteWorklistEntry = gql`
mutation deleteFormData(
  $sessionId: String,
  $formID: Int,
  $submissionId: Int
)
{
  deleteFormData(sessionId: $sessionId, formID: $formID, submissionId: $submissionId)
  {
    submissionID
  }
}
`;
const deleteApiWorklistEntry = gql`
mutation deletePatientEnrollMent(
  $sessionId: String!
  $id: [ID!]
)
{
  deletePatientEnrollMent(sessionId: $sessionId, id: $id)
  
}
`;
const deleteWorklistColumnGroup = gql`
mutation updateWorklistColumnGroup(
  $sessionToken: String!
  $id: Int!,
  $status: Int,
  $params: WorklistColumnGroupInput!
){
  updateWorklistColumnGroup(sessionToken:$sessionToken, id:$id, status:$status, params: $params){
        id
    }
}
`;

const deleteWorklistMsgTemplate = gql`
mutation updateWorklistMessageTemplate(
  $sessionToken: String!
  $id: Int!,
  $deleted: Int,
  $params: WorklistMessageTemplateInput!
){
  updateWorklistMessageTemplate(sessionToken:$sessionToken, id:$id, deleted:$deleted, params: $params){
      
        id
      
    }
}
`;
const updateSingleFormData = gql`
mutation updateFormData(
  $sessionId: String,
  $formID: Int,
  $submissionId: Int,
  $params:  [elementInput]
)
{
  updateFormData(sessionId: $sessionId, formID: $formID, submissionId: $submissionId, params:$params)
  {
    submissionID
  }
}
`;
const getAllAppNames = gql `
query apps(
    $sessionId: String!,
    $crossTenantId: Int
){
 apps(sessionId:$sessionId,crossTenantId:$crossTenantId){
    name    
    id    
    guid    
    description    
    redirectUrl    
    imageName   
    iconImagePath    
    imageThumbName
 }
}
`;
const getTotalBasedOnStatus = gql`
query getTotalBasedOnStatus(
  $sessionId: String!,
  $formID:Int,
  $elementId:Int,
  $unique:Int,
  $uniqueId:String
){
  getTotalBasedOnStatus(sessionId:$sessionId, formID:$formID, elementId:$elementId, unique:$unique, uniqueId:$uniqueId){
    total
    optionValue
    option
  }
}
`;

const insertFormData = gql`
mutation insertFormData(
  $sessionId: String,
  $formID: Int,
  $params:  [elementInput]
)
{
  insertFormData(sessionId: $sessionId, formID: $formID, params:$params)
  {
    submissionID
  }
}
`;
const getWorklistData = gql`
query patientEnrollments($id: ID, $Pagination: Pagination) {
	patientEnrollments(id: $id, pageParams: $Pagination) {
		data {
			patientId firstName lastName dateOfBirth gender enrollmentStatus provider {
				provider providerId providerNpi providerTaxId providerPTan
			}
			treatingSite {
				treatingSite treatingSiteId address1 address2 city state zip phoneNumber fax ext
			}
			haveInsurance
		}
		meta {
			totalCount pageCount
		}
	}
}
`;
const createPatientEnrollment = gql`
mutation patientEnrollMent (
    $sessionId: String!
    $firstName: String
    $lastName: String    
    $dateOfBirth: String
    $gender: String
    $phoneNumber: String
    $mobileNumber: String
    $address: AddressInput
    $email: String
    $enrollmentStatus: EnrollmentStatus
    $preferredContactMethod: String
    $legalGuardianName: String
    $preferredLanguage: String
    $patientAuthorizedProgramToContactCaregiver: Boolean
    $patientAuthorizedProgramToLeaveNameOfMedication: Boolean
    $caregiver: CaregiverInput
    $providerInfo: ProviderInfoInput
    $insuranceInfo: [InsuranceInput]
    $treatmentInfo: TreatmentInput
    $treatmentSiteInfo: TreatmentSiteInput
  ) {
    patientEnrollMent (sessionId:$sessionId,params:
     { 
      firstName: $firstName  
      lastName: $lastName
      dateOfBirth: $dateOfBirth  
      gender: $gender
      phoneNumber: $phoneNumber
      mobileNumber: $mobileNumber
      address: $address
      email: $email
      enrollmentStatus: $enrollmentStatus
      preferredContactMethod: $preferredContactMethod
      legalGuardianName: $legalGuardianName
      preferredLanguage: $preferredLanguage
      patientAuthorizedProgramToContactCaregiver: $patientAuthorizedProgramToContactCaregiver
      patientAuthorizedProgramToLeaveNameOfMedication: $patientAuthorizedProgramToLeaveNameOfMedication
      caregiver: $caregiver
      providerInfo: $providerInfo
      insuranceInfo: $insuranceInfo
      treatmentInfo: $treatmentInfo
      treatmentSiteInfo: $treatmentSiteInfo
      
    }
      
    )
    {
      patientId
  }
}
`;
const updatePatientEnrollment = gql`
mutation updatePatientEnrollMent (
    $sessionId: String!
    $id: ID!
    $firstName: String
    $lastName: String    
    $dateOfBirth: String
    $gender: String
    $phoneNumber: String
    $mobileNumber: String
    $haveInsurance: Boolean
    $address: AddressInput
    $email: String
    $enrollmentStatus: EnrollmentStatus
    $preferredContactMethod: String
    $legalGuardianName: String
    $preferredLanguage: String
    $patientAuthorizedProgramToContactCaregiver: Boolean
    $patientAuthorizedProgramToLeaveNameOfMedication: Boolean
    $caregiver: CaregiverInput
    $providerInfo: ProviderInfoInput
    $insuranceInfo: [InsuranceInput]
    $treatmentInfo: TreatmentInput
    $treatmentSiteInfo: TreatmentSiteInput
  ) {
    updatePatientEnrollMent (sessionId: $sessionId,id:$id,params:
     { 
      firstName: $firstName  
      lastName: $lastName
      dateOfBirth: $dateOfBirth  
      gender: $gender
      phoneNumber: $phoneNumber
      mobileNumber: $mobileNumber
      haveInsurance: $haveInsurance
      address: $address
      email: $email
      enrollmentStatus: $enrollmentStatus
      preferredContactMethod: $preferredContactMethod
      legalGuardianName: $legalGuardianName
      preferredLanguage: $preferredLanguage
      patientAuthorizedProgramToContactCaregiver: $patientAuthorizedProgramToContactCaregiver
      patientAuthorizedProgramToLeaveNameOfMedication: $patientAuthorizedProgramToLeaveNameOfMedication
      caregiver: $caregiver
      providerInfo: $providerInfo
      insuranceInfo: $insuranceInfo
      treatmentInfo: $treatmentInfo
      treatmentSiteInfo: $treatmentSiteInfo
      
    }
      
    )
    {
      patientId
  }
}
`;
const getDropDownValues = gql`
query listOfValueItems(
    $sessionId: String!
    $type: ListOfValueType
    
  ) {
    listOfValueItems(sessionId:$sessionId,type:$type) {
    id
    type
    itemName
    itemCode
    sortOrder
    controlType
  }
}  
`;

const getStateList = gql`
query states(
  $sessionId: String!
){
  states(sessionId:$sessionId){
    id
    stateName    
    postalCode
    countryId
  }
}  
`;
const getLanguageList = gql`
query languages(
  $sessionId: String!
){
  languages(sessionId:$sessionId){
    id
    languageKey
    languageName
  }
}  
`;
const getEnrollment = gql`
query 
patientEnrollments(
    $sessionId: String
    $id: ID
  ) {
    patientEnrollments(sessionId:$sessionId,id:$id) {
      data {
        firstName
     lastName
     dateOfBirth
     gender
     enrollmentStatus
     phoneNumber
      mobileNumber
      email
      legalGuardianName
      preferredContactMethod
      preferredLanguage
      patientAuthorizedProgramToContactCaregiver
      patientAuthorizedProgramToLeaveNameOfMedication
     provider{
       provider
       providerId
        providerNpi
      providerTaxId
      providerPTan
     }
     treatingSite {
       treatingSite
       treatingSiteId
         address1
     address2
     city
     state
     zip
     phoneNumber
     fax
     ext
     }
     caregiver {
         firstName
         lastName
         patientRelationship
         phoneNumber
         email
         prefferedLanguage
         preferredContactMethod
     }
     address{
       address1
       address2
       city
       state
       zip
     }
     insuranceInfo {
       patientHaveInsurance
       typeOfInsurance
       primacy
       insuranceType
       insuranceCompany
       planName
       insurancePhone
       policySubscriberId
       group
       subscriberFirstName
       subscriberLastName
       subscriberDateOfBirth
       relationshipToSubscriber
       RxBIN
       RxPCN
       subscriberEmployer
     }
      treatmentInfo {		 
       placeOfService
       product
       treatmentArea
       treatmentCode
       primaryDiagnosisCode
       isPatientScheduled
       dateOfService    
   }
     haveInsurance
   }
   meta {
     totalCount
     pageCount
   }
      
    }
  }  
`;
// mutation{updateFormData(sessionId:"AQIC5wM2LY4Sfcy8uaWabRT7-oG_unpLSCYYpUoMv_e74kQ.*AAJTSQACMDEAAlNLABQtMzg4NzAyMzkyNTI0NjI5NTYwNAACUzEAAA..*",formID:37523,submissionId:2,params:[{id:"5",value:"WWW updated"},{id:"6",value:"WWW updated"}]) {
//   submissionID
// }}
interface QueryResponse {
  getWorklistFormEntries: any;
}
@Injectable()

export class EversanaWorkListService {
  worklistDetails = {};
  public appCenterWorklistDetails = [];
  public activeTabId;
  public activeFilterDetails = [];
  public activeActivityDetails = [];  
  timezone;
  systemUrlToken = '';
  constructor(
    private _http: Http,
    private router: Router,
    public _structureService: StructureService,
    public _formService: FormsService,
    private apollo: Apollo
  ) {
    this.timezone = configTimeZone();
  }
  getWidgetCount(formId, fieldId, uniqueClass) {
    const subscription = this.apollo.use('worklistGraphApi').watchQuery<QueryResponse>({
      query: getTotalBasedOnStatus,
      variables: {
        sessionId: this._structureService.getCookie('authenticationToken'),
        formID: Number(formId),
        elementId: fieldId,
        uniqueId: uniqueClass,
        unique: uniqueClass != '' ?  1 : 0
      }
    });
    subscription.subscribe(({ data }) => { });
    return subscription;
  }
  getWorkListFormDataWithFilter(formId, uniqueClass, start, end, formFieldFrom, filterModel, sortModel) {
    let query;
    let variables = {
      sessionId: this._structureService.getCookie('authenticationToken'),
      formID: Number(formId),
      start: start,
      end: end,
      filterModel: filterModel,
      sortModel: sortModel
    };
    if (formFieldFrom == 'fromlabel') {
      variables['fromlabel'] = 1;
      variables['fromCSS'] = 0;
    } else {
      variables['fromlabel'] = 0;
      variables['fromCSS'] = 1;
    }
    if (uniqueClass != '') {
      variables['uniqueId'] = uniqueClass;
      query = getWorklistFormEntriesByGroupWithFilter;
    } else {
      query = getWorklistFormEntriesWithFilter;
    }
    const subscription = this.apollo.use('worklistGraphApi').watchQuery<QueryResponse>({
      query: query,
      variables: variables,
      fetchPolicy: 'network-only'
    });
    subscription.subscribe(({ data }) => { });
    return subscription;
  }
  getWorkListFormData(formId, uniqueClass, start, end, formFieldFrom) {
    let query;
    let variables = {
      sessionId: this._structureService.getCookie('authenticationToken'),
      formID: Number(formId),
      start: start,
      end: end,
    };
    if (formFieldFrom == 'fromlabel') {
      variables['fromlabel'] = 1;
      variables['fromCSS'] = 0;
    } else {
      variables['fromlabel'] = 0;
      variables['fromCSS'] = 1;
    }
    if (uniqueClass != '') {
      variables['uniqueId'] = uniqueClass;
      query = getWorklistFormEntriesByGroup;
    } else {
      query = getWorklistFormEntries;
    }
    const subscription = this.apollo.use('worklistGraphApi').watchQuery<QueryResponse>({
      query: query,
      variables: variables,
      fetchPolicy: 'network-only'
    });
    subscription.subscribe(({ data }) => { });
    return subscription;
  }
 
  getWorklistsQuery() {
    let worklists = `query getSessionTenant(
      $sessionToken: String!`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      worklists = worklists + `,
      $crossTenantId : Int`;
    }
    worklists = worklists + `){
      getSessionTenant(sessionToken:$sessionToken`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      worklists = worklists + `,
      crossTenantId:$crossTenantId`;
    }
    worklists = worklists + `){
      formWorklists{
        id
        name
        description
        active
        desktopMenu{
          menuName
          menuGroup{
            id
            name
            tenantId
          }
          menuIndex
        }
        reportForms{
          id
          name
        }
        statusForms{
          id
          name
        }
        
      }
    }
  }
  `;
    return gql`${worklists}`;
  }
  getWorkLists() {
    let variables: any = {
      sessionToken: this._structureService.getCookie('authenticationToken')
    };
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      variables.crossTenantId = this._structureService.getCookie('crossTenantId');
    }
    const apiConfig = {
      method: 'GET',
      data: this.getWorklistsQuery(),
      variables: variables,
      requestType: 'gql',
      use: '',
      nested: false,
    };
    return this._structureService.requestData(apiConfig);
  }

  getWorkListDetailsQuery() {
    let worklistDetails = `query getSessionTenant(
      $sessionToken: String!
      $id: Int`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      worklistDetails = worklistDetails + `,
      $crossTenantId : Int`;
    }
    worklistDetails = worklistDetails + `){
      getSessionTenant(sessionToken:$sessionToken`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      worklistDetails = worklistDetails + `,
      crossTenantId:$crossTenantId`;
    }
    worklistDetails = worklistDetails + `){
      formWorklists(id:$id){
        id
        name
        description
        active
        strategy
        associatedEntity
        desktopMenu{
          menuName
          menuGroup{
            id
            name
            tenantId
          }
          menuIndex
        }
        reportForms{
          id
          name
        }
        statusForms{
          id
          name
        }
        reportFields{
          formId
          fieldName
          headerName
          allowSort
          displayIndex
          visibility
          valueType
          allowFilter
          filterType
          allowEdit
          allowBulkEdit
          allowRowGrouping
          groupName
          allowPrefillData
          prefillMethod
          apiEndpoint
          parameters
          fieldId
          checkboxSelection
          prefillFormOption
          prefillOptionForm
          prefillListOption
          prefillOptionFormField
          prefillExtraOptions
          fieldType
          mapField
          showInEditor
          linkField
          clearFilter
          applyFilter
          includeInExport
          columnWidth
          enableEventAssociation
          cellEvent
          allowCellStyle
          cellStyles{
            fieldValue
            fieldText
            expression
            cellStyleMethod
            bgColour
            fontColour
            cssStyleClass
            styleOption
            expressionType
            day
            month
            year
            value
            contentType
            iconClass
            iconColor
          }
          allowCellFormat
          cellFormat{
            cellValue
            expression
            formatOption
            formattedValue
          }
          allowCellValueChange
          newCellValue
          allowQuickSearch
          enableAutoSelect
          enableNotification
          notifySpecificValue
          notificationFieldValue
          push
          pushTemplate
          sms
          smsTemplate
          email
          emailTemplate
          notifyRolesOnValueChange
          notifyRecipientOnValueChange
          fieldValueDelimiter
          hideColumnBasedField
          hideColumnValue
        }
        dashboardWidgets{
          displayText
          displayIndex
          displayIcon
          iconColor
          formField
          textAlign
          rightBorder
          count
          class
          widgetValue
          hideColumns
        }
        singleWorklistAction{
            actionButton{
              actionLabel
              actionIcon
              actionLink
              buttonIndex
              cssClass
              tooltiptext
              actionType
              customJS
              customJsURL
              actionCallbackFunction
              buttonStyle
              actionMode
              actionButtonType
              formField
              selectionMode
              shortLink
              fieldValues
              mapField
              enableIntegration
              enableNotification
              push
              pushTemplate
              sms
              smsTemplate
              email
              emailTemplate
              notifyRolesOnAction
              notifyRecipientOnAction
              actionRoles
              actionPrivileges
              showOnlyLoginUser
              loginUserMatchField
              actionFields{
                associatedField
                fieldValues
              }
              actionMenu{
                itemName
                itemIndex
                itemActionLink
                cssClass
                itemActionType
                itemActionCallbackFunction
                itemFormField
              }
            }
          }
      }
    }
  }
  `;
    return gql`${worklistDetails}`;
  }
  getWorklistDetails(listId) {
    let variables: any = {
      sessionToken: this._structureService.getCookie('authenticationToken'),
      id: Number(listId),
    };
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      variables.crossTenantId = this._structureService.getCookie('crossTenantId');
    }
    const apiConfig = {
      method: 'GET',
      data: this.getWorkListDetailsQuery(),
      variables: variables,
      requestType: 'gql',
      use: '',
      nested: false,
    };
    return this._structureService.requestData(apiConfig);
  }
  
  deleteWorklistEntry(formId, submissionID) {
    return this.apollo.use('worklistGraphApi').mutate({
      mutation: mutDeleteWorklistEntry,
      variables: {
        sessionId: this._structureService.getCookie('authenticationToken'),
        formID: formId,
        submissionId: submissionID
      }
    }).map(
      res => res
    );
  }
  deleteApiWorklistEntry(deleteArray) {
    return this.apollo.use('eversanaClient').mutate({
      mutation: deleteApiWorklistEntry,
      variables: {
        sessionId: this._structureService.getCookie('authenticationToken'),
        
        id: deleteArray
      }
    }).map(
      res => res
    );
  }

  getWorklistMenuGroupQuery() {
    let menuGroup = `query getSessionTenant(
      $sessionToken: String!`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      menuGroup = menuGroup + `,
        $crossTenantId : Int`;
    }
    menuGroup = menuGroup + `){
        getSessionTenant(sessionToken:$sessionToken`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      menuGroup = menuGroup + `,
        crossTenantId:$crossTenantId`;
    }
    menuGroup = menuGroup + `){
        formWorklistMenuGroup{
          id
          tenantId
          name
          createdBy
          createdOn
        }
        }
    }`;
    return gql`${menuGroup}`;
  }
  getWorklistMenuGroup() {
    let variables: any = {
      sessionToken: this._structureService.getCookie('authID'),
    };
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      variables.crossTenantId = this._structureService.getCookie('crossTenantId');
    }
    const apiConfig = {
      method: 'GET',
      data: this.getWorklistMenuGroupQuery(),
      variables: variables,
      requestType: 'gql',
      use: '',
    };
    const responce = this._structureService.requestData(apiConfig);
    return responce;
  }
  

  createWorklistColumnGroupQuery() {
    let createColumnGp = `mutation createWorklistColumnGroup( $sessionToken:String!,$params : WorklistColumnGroupInput`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      createColumnGp = createColumnGp + `,
        $crossTenantId : Int!`;
    }
    createColumnGp = createColumnGp + `){createWorklistColumnGroup(sessionToken:$sessionToken params:$params`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      createColumnGp = createColumnGp + `,crossTenantId:$crossTenantId`;
    }
    createColumnGp = createColumnGp + `){
      id}
      }`;
    return gql`${createColumnGp}`;
  }
  createWorklistColumngroup(params) {
    let variables: any = {
      sessionToken: this._structureService.getCookie('authID'),
      params: params
    };
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      variables.crossTenantId = this._structureService.getCookie('crossTenantId');
    }
    const apiConfig = {
      method: 'POST',
      data: this.createWorklistColumnGroupQuery(),
      variables: variables,
      requestType: 'gql',
      use: '',
    };
    const responce = this._structureService.requestData(apiConfig);
    return responce;
  }
  getWorklistColumnGroupQuery() {
    let columnGroup = `query getSessionTenant(
      $sessionToken: String!,$worklistId :Int`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      columnGroup = columnGroup + `,
        $crossTenantId : Int`;
    }
    columnGroup = columnGroup + `){
        getSessionTenant(sessionToken:$sessionToken`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      columnGroup = columnGroup + `,
        crossTenantId:$crossTenantId`;
    }
    columnGroup = columnGroup + `){
        formWorklistColumnGroup(worklistId:$worklistId){
          id
          tenantId
          name
          description
          createdBy
          createdOn
        }
      }
    }`;
    return gql`${columnGroup}`;
  }
  getWorklistColumnGroup(worklistId) {
    let variables: any = {
      sessionToken: this._structureService.getCookie('authID'),
      worklistId: worklistId
    };
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      variables.crossTenantId = this._structureService.getCookie('crossTenantId');
    }
    const apiConfig = {
      method: 'GET',
      data: this.getWorklistColumnGroupQuery(),
      variables: variables,
      requestType: 'gql',
      use: '',
    };
    const responce = this._structureService.requestData(apiConfig);
    return responce;
  }
  deleteColumnGroup(deleteId, deleteName) {
    const variables = {
      sessionToken: this._structureService.getCookie('authID'),
      id: deleteId,
      status: 1,
      params: {
        name: deleteName,
      }
    };
    const apiConfig = {
      method: 'POST',
      data: deleteWorklistColumnGroup,
      variables: variables,
      requestType: 'gql',
      use: '',
    };
    const responce = this._structureService.requestData(apiConfig);
    return responce;
  }
  updateWorklistColumnGroupQuery() {
    let updatecolumnGp = `mutation updateWorklistColumnGroup( $sessionToken:String!,$params : WorklistColumnGroupInput!,$status: Int,$id: Int!`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      updatecolumnGp = updatecolumnGp + `,
      $crossTenantId : Int!`;
    }
    updatecolumnGp = updatecolumnGp + `){updateWorklistColumnGroup(sessionToken:$sessionToken params:$params,id: $id,status: $status`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      updatecolumnGp = updatecolumnGp + `,crossTenantId:$crossTenantId`;
    }
    updatecolumnGp = updatecolumnGp + `){
     id}
    }`;
    return gql`${updatecolumnGp}`;
  }
  updateColumnGroup(updateId, params) {
    let variables: any = {
      sessionToken: this._structureService.getCookie('authID'),
      id: updateId,
      status: 0,
      params: params
    };
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      variables.crossTenantId = this._structureService.getCookie('crossTenantId');
    }
    const apiConfig = {
      method: 'POST',
      data: this.updateWorklistColumnGroupQuery(),
      variables: variables,
      requestType: 'gql',
      use: '',
    };
    const responce = this._structureService.requestData(apiConfig);
    return responce;
  }
  getAllFormNames(tenantId) {
    const getAllFormNamesApi = this._structureService.apiBaseUrl + "citus-health/v4/get-all-survey-names.php";
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')) {
      tenantId.crossTenantId = this._structureService.getCookie('crossTenantId');
    }
    var apiConfig = {url: getAllFormNamesApi, requestType: 'http', data: tenantId};
    return this._structureService.requestData(apiConfig);
  }
  getFormsInType(params) {
    const getAllFormNamesApi = this._structureService.apiBaseUrl + "citus-health/v4/get-all-survey-names.php";
    var apiConfig = { url: getAllFormNamesApi, requestType: 'http', data: params };
    return this._structureService.requestData(apiConfig);
  }
  updateSingleFormData(params, formId, submissionId) {
    return this.apollo.use('worklistGraphApi').mutate({
      mutation: updateSingleFormData,
      variables: {
        sessionId: this._structureService.getCookie('authenticationToken'),
        formID: formId,
        submissionId: submissionId,
        params: params
      }
    }).map(
      res => res
    );
  }
  getMachformFields(formId, elementId) {
    const subscription = this.apollo.use('worklistGraphApi').watchQuery<QueryResponse>({
      query: getMachformFields,
      variables: {
        sessionId: this._structureService.getCookie('authenticationToken'),
        formID: formId,
        elementId: elementId,
      }
    });
    subscription.subscribe(({ data }) => { });
    return subscription;
  }
  getassociatedPatients(tenantid) {
    var data = "tenantId=" + tenantid + "&isTenantRoles=" + undefined + "&roleId=3";
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      data = data + "&crossTenantId=" + this._structureService.getCookie('crossTenantId');
    }
    var apiConfig = { url: this._structureService.apiBaseUrl + "citus-health/v4/get-tenant-associated-patient.php", requestType: 'http', data: data };
    return this._structureService.requestData(apiConfig);
  }
  getWorklistFiles(formId, submissionId) {
    const subscription = this.apollo.use('worklistGraphApi').watchQuery<QueryResponse>({
      query: getWorklistFiles,
      variables: {
        sessionToken: this._structureService.getCookie('authID'),
        formID: formId,
        submissionId: submissionId,
        elementType: "file"
      }, fetchPolicy: 'network-only'
    });
    subscription.subscribe(({ data }) => { });
    return subscription;
  }
  getWorklistDataUsingAPI(url, params, graphqlQuery ='') {
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authID'));
    let options = new RequestOptions({ headers: headers }); 
    var apiConfig = { url: url, requestType: 'http', data: params, method: 'GET', headers: {}, replaceHeaders:false };
    if (graphqlQuery) {
      apiConfig.replaceHeaders = true;
      apiConfig.headers = { 'Content-Type': 'application/json'};
      apiConfig.method = 'POST';
      console.log(typeof graphqlQuery);
      console.log(graphqlQuery);
      apiConfig.data = JSON.parse(graphqlQuery);
    } 
    return this._structureService.requestData(apiConfig);
  }
  getWorklistDataUsingGraphQLAPI(url, query, variables) { 

    let queryParams = gql`${query}`;
    const networkInterface = createNetworkInterface({
      uri: url,
    });
    const client = new ApolloClient({
      networkInterface
    });

    const userData = this._structureService.getUserdata();
    variables.sessionId = this._structureService.getCookie('authenticationToken');
    variables.accessSecurityType = userData.accessSecurityType;
    variables.accessSecurityIdentifierType = userData.accessSecurityIdentifierType;
    variables.accessSecurityEsiValue = userData.accessSecurityEsiValue;
    if(userData.mySites.length > 0) {
      var siteIds = userData.mySites.map(function (e) {
        return e.id;
      });

      var siteFilter = {
        column : 'patientSiteId',
        filter : siteIds,
        type : 'in'
      }

      if(variables.filter && variables.filter.length > 0) {
        variables.filter.push(siteFilter);
      } else {
        variables.filter = [siteFilter];
      }
    }
    const subscription = client.query({
      query: queryParams,
      variables: variables, 
      fetchPolicy: 'network-only'
    });
    return subscription;
  }
  addFormEntry(formId, params) {
    return this.apollo.use('worklistGraphApi').mutate({
      mutation: insertFormData,
      variables: {
        sessionId: this._structureService.getCookie('authenticationToken'),
        formID: formId,
        params: params
      }
    }).map(
      res => res
    );
  }
  getChildFormData(formId, mappingField, mappingFieldValue) {
    const subscription = this.apollo.use('worklistGraphApi').watchQuery<QueryResponse>({
      query: getAllFormDataMultipleForm,
      variables: {
        sessionToken: this._structureService.getCookie('authID'),
        formID: formId,
        mappingField: mappingField,
        mappingFieldValue: mappingFieldValue
      }, fetchPolicy: 'network-only'
    });
    subscription.subscribe(({ data }) => { });
    return subscription;
  }
  userToTenantTime() {
    let activeTimeZone = ((((new Date().getTimezoneOffset()) * -1) * -1) / 60) * -1;
    let timeToPassDay = [];
    let timeDifference;
    if (activeTimeZone > this.timezone) {
      timeDifference = -1 * Math.abs(this.timezone - activeTimeZone);
    } else {
      timeDifference = Math.abs(this.timezone - activeTimeZone);
    }
    if (((timeDifference) + '').split('.').length == 2) {
      timeToPassDay = ((timeDifference) + '').split('.');
    } else {
      timeToPassDay = ((timeDifference) + '').split('.');
      timeToPassDay.push("0");
    }
    if (timeToPassDay[1] == '5') {
      if (timeDifference < 0) {
        timeToPassDay[1] = '-30';
      } else {
        timeToPassDay[1] = '30';
      }
    } else {
      timeToPassDay[1] = '0';
    }
    if (timeToPassDay[0] == '-0') {
      timeToPassDay[0] = '0';
    }

    return timeToPassDay = timeToPassDay.map(function (time) {
      return parseInt(time, 10);
    });
  }
  getOtherTenantRoles(roleId){
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authID'));
    const apiURL = this._structureService.apiBaseUrl + "citus-health/v4/get-roles-of-cross-tenant.php?roleId=" + roleId;
    var apiConfig = { url: apiURL, requestType: 'http'};
    return this._structureService.requestData(apiConfig);
  }
  getAllAppNames() {
    let variables: any = {
      sessionId: "AQIC5wM2LY4SfczEWBeEFQToeMPLFCDmpTM58Y7yeiRlCKU.*AAJTSQACMDEAAlNLABQtMTUzNjA1MTczODI3NTYxMDUzMQACUzEAAA..*",
      crossTenantId: Number(this._structureService.getCookie('crossTenantId'))
    };
    const apiConfig = {
      method: 'GET',
      data: getAllAppNames,
      variables: variables,
      requestType: 'gql',
      use: 'signatureRequestApi',
      nested: false,
    };
    return this._structureService.requestData(apiConfig);
  }
  getWorklistMenu() {
    this.getWorkLists().then((data) => {
      let workLists = [];
      if(data['getSessionTenant'] && data['getSessionTenant'].formWorklists) {
        workLists = JSON.parse(JSON.stringify(data['getSessionTenant'].formWorklists));
      }      
      this._structureService.worklistMenu = [];
      let workListMenu = [];
      workLists.sort(function (a, b) {
        if (a.desktopMenu.menuIndex < b.desktopMenu.menuIndex) { return -1; }
        if (a.desktopMenu.menuIndex > b.desktopMenu.menuIndex) { return 1; }
        return 0;
      });
      
      workLists.forEach(element => {
        if (element.description) {
          let newJson = element.description;
          newJson = newJson.replace(/'/g, '"');
          const metaData = JSON.parse(newJson);
          let actionLink = metaData.nameOfDesktopMenu.replace(/[^a-zA-Z ]/g, ' ').trim();
          actionLink = actionLink.replace(/\s+/g, '-').toLowerCase();
          const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
          if (((metaData.visibleToRoles && metaData.visibleToRoles.split(',').indexOf(userDetails.roleId) !== -1) || (metaData.allowCrossTenant == true && metaData.visibleToOtherRoles && metaData.visibleToOtherRoles.split(',').indexOf(userDetails.roleId) !== -1)) && element.active && metaData.showInMenu == true) {
            workListMenu.push(
              {
                worklistId: element.id,
                workListName: element.name,
                menuGroupId: element.desktopMenu.menuGroup.id,
                menuTitle: element.desktopMenu.menuName,
                actionLink: `worklist/${actionLink}/${element.id}`,
                menuIcon: metaData.menuIcon
              }
            );
          }
        }
      });
      this.getWorklistMenuGroup().then((group)=> {
        let groups = JSON.parse(JSON.stringify(group['getSessionTenant'].formWorklistMenuGroup));
        groups.forEach(element => {
          this._structureService.worklistMenu.push({'name': element.name, 'worklists': workListMenu.filter(x=> x.menuGroupId == element.id)});
        });
        
      });
    });
  }
  getWorklistMessageTemplaeQuery() {
    let columnGroup = `query getSessionTenant(
      $sessionToken: String!`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      columnGroup = columnGroup + `,
        $crossTenantId : Int`;
    }
    columnGroup = columnGroup + `){
        getSessionTenant(sessionToken:$sessionToken`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      columnGroup = columnGroup + `,
        crossTenantId:$crossTenantId`;
    }
    columnGroup = columnGroup + `){
      formWorklistMessageTemplate{
        id
        name
        tenantId
        type
        content
        title
        subject
        status
        worklistId
        }
      }
    }`;
    return gql`${columnGroup}`;
  }
  getWorklistMessageTemplate() {
    let variables: any = {
      sessionToken: this._structureService.getCookie('authID'),
    };
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      variables.crossTenantId = this._structureService.getCookie('crossTenantId');
    }
    const apiConfig = {
      method: 'GET',
      data: this.getWorklistMessageTemplaeQuery(),
      variables: variables,
      requestType: 'gql',
      use: '',
    };
    const responce = this._structureService.requestData(apiConfig);
    return responce;
  }
  createWorklistMessageTemplateQuery() {
    let createTemplate = `mutation createWorklistMessageTemplate( $sessionToken:String!,$params : WorklistMessageTemplateInput`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      createTemplate = createTemplate + `,
        $crossTenantId : Int!`;
    }
    createTemplate = createTemplate + `){createWorklistMessageTemplate(sessionToken:$sessionToken params:$params`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      createTemplate = createTemplate + `,crossTenantId:$crossTenantId`;
    }
    createTemplate = createTemplate + `){
      id}
      }`;
    return gql`${createTemplate}`;
  }
  createWorklistMessageTemplate(params) {
    let variables: any = {
      sessionToken: this._structureService.getCookie('authID'),
      params: params
    };
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      variables.crossTenantId = this._structureService.getCookie('crossTenantId');
    }
    const apiConfig = {
      method: 'POST',
      data: this.createWorklistMessageTemplateQuery(),
      variables: variables,
      requestType: 'gql',
      use: '',
    };
    const responce = this._structureService.requestData(apiConfig);
    return responce;
  }
  updateWorklistMessageTemplateQuery() {
    let updateTemplate = `mutation updateWorklistMessageTemplate( $sessionToken:String!,$params : WorklistMessageTemplateInput!,$id : Int!`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      updateTemplate = updateTemplate + `,
        $crossTenantId : Int!`;
    }
    updateTemplate = updateTemplate + `){updateWorklistMessageTemplate(sessionToken:$sessionToken params:$params id:$id`;
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      updateTemplate = updateTemplate + `,crossTenantId:$crossTenantId`;
    }
    updateTemplate = updateTemplate + `){
      id}
      }`;
    return gql`${updateTemplate}`;
  }
  updateWorklistMessageTemplate(params, id) {
    let variables: any = {
      sessionToken: this._structureService.getCookie('authID'),
      params: params,
      id: id
    };
    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      variables.crossTenantId = this._structureService.getCookie('crossTenantId');
    }
    const apiConfig = {
      method: 'POST',
      data: this.updateWorklistMessageTemplateQuery(),
      variables: variables,
      requestType: 'gql',
      use: '',
    };
    const responce = this._structureService.requestData(apiConfig);
    return responce;
  }
  deleteWorklistMessageTemplate(params, id) {
    const variables = {
      sessionToken: this._structureService.getCookie('authID'),
      id: id,
      deleted: 0,
      params: params
    };
    const apiConfig = {
      method: 'POST',
      data: deleteWorklistMsgTemplate,
      variables: variables,
      requestType: 'gql',
      use: '',
    };
    const responce = this._structureService.requestData(apiConfig);
    return responce;
  }
  getRoleBasedStaffs(roleId, status, type) {
    let variables:any = {
      sessionToken: this._structureService.getCookie('authenticationToken'),
      roleId: roleId,
      status: status,
      type: type
    };
    if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
      variables.crossTenantId=this._structureService.getCookie('crossTenantId');
    }  
    let apiConfig = {
      method: 'GET',
      data: this.getRoleBasedStaffsQuery(),
      variables: variables,
      requestType: 'gql',
      use: "",
    };
    //var responce = this.requestData_New(apiConfig); 
    let responce = this._structureService.requestData(apiConfig);

    return responce;
  }
  getRoleBasedStaffsQuery() {
    let RoleBasedStaffs =`query getSessionTenant($sessionToken: String!, $roleId: Int!, $status: Int!,$type:Int`;
    if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
      RoleBasedStaffs = RoleBasedStaffs +`,
      $crossTenantId : Int!`;
    }

    RoleBasedStaffs = RoleBasedStaffs+`){getSessionTenant(sessionToken:$sessionToken`;
    if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
      RoleBasedStaffs = RoleBasedStaffs+`,crossTenantId:$crossTenantId`;
    }
    RoleBasedStaffs = RoleBasedStaffs+`){ 
      roleBasedStaffs(roleId:$roleId,status:$status,type:$type){
        id
        displayName
        mobile
        countryCode
        role{
          roleId
          roleName
        }
        firstName
        lastName
        emails {
          type
          value
          primary
        }
      }
    }
  }`;

    return  gql`${RoleBasedStaffs}`;
  }
  createPatientEnrollment(params) {
    
    let variables: any = {
      sessionId: this._structureService.getCookie('authenticationToken'),

      firstName: params.firstName,
      lastName: params.lastName,
      dateOfBirth: params.dateOfBirth,
      gender: params.gender,     
      phoneNumber: params.phoneNumber,
      mobileNumber: params.mobileNumber,
      address:{
        address1:params.address1,
        address2:params.address2,
        city: params.city,
        state: params.state,
        zip: params.zip
      },
      email: params.email,
        enrollmentStatus: "DRAFT",
        preferredContactMethod: params.preferredContactMethod,
        legalGuardianName: params.legalGuardianName,
        preferredLanguage: params.preferredLanguage,
        patientAuthorizedProgramToContactCaregiver: params.patientAuthorizedProgramToContactCaregiver,
        patientAuthorizedProgramToLeaveNameOfMedication: params.patientAuthorizedProgramToLeaveNameOfMedication,
        caregiver: {
          firstName: params.careGiverFirstName,
          lastName: params.careGiverLastName,
          patientRelationship: params.patientRelationship,
          phoneNumber: params.careGiverphoneNumber,
          email:params.careGiverEmail,
          prefferedLanguage: params.careGiverPreferredLanguage,
          preferredContactMethod: params.careGiverPreferredContactMethod,
        },
        providerInfo: {
          provider: params.provider,
          providerNpi: params.providerNpi,
          providerTaxId: params.providerTaxId,
          providerPTan: params.providerPTan
        
        },
        
       insuranceInfo: params.insuranceArray,
       treatmentInfo: {
        placeOfService: params.placeOfService,
        product: params.product,
        treatmentArea: params.treatmentArea,
        treatmentCode: params.treatmentCode,
        primaryDiagnosisCode: params.primaryDiagnosisCode,
        isPatientScheduled: this.getBoolean(params.isPatientScheduled),
        dateOfService: params.dateOfService,
      },
      treatmentSiteInfo:{

        treatingSite: params.treatingSite,
        address1: params.siteAddress1,
        address2: params.siteAddress2,
        city: params.siteCity,
        state: params.siteState,
        zip: params.siteZip,
        phoneNumber: params.sitePhoneNumber,
        fax: params.siteFax,
        ext: params.siteExt
      }
      
    }
    console.log(variables);
    return this.apollo.use('eversanaClient').mutate<any>({
      mutation: createPatientEnrollment,
      variables: variables
    }).map(
      res => res
    );
  }
  getBoolean(value){
    console.log(value);
    switch(value){
         case true:
         case "true":
         case 1:
         case "1":
         case "on":
         case "yes":
         case '':
             return true;
         default: 
             return false;
     }
 }
  updatePatientEnrollment(enrollId,params) {
    let insuranceArray = [];
    if(params.insuranceArray){
      
      for (const insurance of params.insuranceArray) {
        if(insurance.subscriberDateOfBirth !== "") {
          // console.log(getDate(insurance.subscriberDateOfBirth));
          var date = new Date(insurance.subscriberDateOfBirth),
          mnth = ("0" + (date.getMonth() + 1)).slice(-2),
          day = ("0" + date.getDate()).slice(-2);
          insurance.subscriberDateOfBirth = [date.getFullYear(), mnth, day].join("-");
        }
        insuranceArray.push(insurance);
      }
      console.log(insuranceArray)
    }
    let variables: any = {
      sessionId: this._structureService.getCookie('authenticationToken'),

      id:Number(enrollId),

      firstName: params.firstName,
      lastName: params.lastName,
      dateOfBirth: params.dateOfBirth,
      gender: params.gender,     
      phoneNumber: params.phoneNumber,
      mobileNumber: params.mobileNumber,
      haveInsurance: this.getBoolean(params.haveInsurance),
      address:{
        address1:params.address1,
        address2:params.address2,
        city: params.city,
        state: params.state,
        zip: params.zip,
      },
      email: params.email,
        enrollmentStatus: "DRAFT",
        preferredContactMethod: params.preferredContactMethod,
        legalGuardianName: params.legalGuardianName,
        preferredLanguage: params.preferredLanguage,
        patientAuthorizedProgramToContactCaregiver: params.patientAuthorizedProgramToContactCaregiver,
        patientAuthorizedProgramToLeaveNameOfMedication: params.patientAuthorizedProgramToLeaveNameOfMedication,
        caregiver: {
          firstName: params.careGiverFirstName,
          lastName: params.careGiverLastName,
          patientRelationship: params.patientRelationship,
          phoneNumber: params.careGiverPhoneNumber,
          email:params.careGiverEmail,
          prefferedLanguage: params.careGiverPreferredLanguage,
          preferredContactMethod: params.careGiverpreferredContactMethod,
        },
        providerInfo: {
          provider: params.provider,
          providerNpi: params.providerNpi,
          providerTaxId: params.providerTaxId,
          providerPTan: params.providerPTan
        
        },
        
       insuranceInfo: insuranceArray,
       treatmentInfo: {
        placeOfService: params.placeOfService,
        product: params.product,
        treatmentArea: params.treatmentArea,
        treatmentCode: params.treatmentCode,
        primaryDiagnosisCode: params.primaryDiagnosisCode,
        isPatientScheduled: this.getBoolean(params.isPatientScheduled) ,
        dateOfService: params.dateOfService,
      },
      treatmentSiteInfo:{

        treatingSite: params.treatingSite,
        address1: params.siteAddress1,
        address2: params.siteAddress2,
        city: params.siteCity,
        state: params.siteState,
        zip: params.siteZip,
        phoneNumber: params.sitePhoneNumber,
        fax: params.siteFax,
        ext: params.siteExt
      }
      
    }
    // console.log(variables);
    return this.apollo.use('eversanaClient').mutate<any>({
      mutation: updatePatientEnrollment,
      variables: variables
    }).map(
      res => res
    );
  }
  getListofValues(dropDownType) {
    const subscription = this.apollo.use('eversanaClient').query<QueryResponse>({
      query: getDropDownValues,
      variables: {
        sessionId: this._structureService.getCookie('authenticationToken'),
        type: dropDownType
      },
      fetchPolicy: 'network-only'
    });
    return subscription;
  }
  getStateList() {
    const subscription = this.apollo.use('eversanaClient').query<QueryResponse>({
      query: getStateList,
      variables: {
        sessionId: this._structureService.getCookie('authenticationToken')
      },
      fetchPolicy: 'network-only'
    });
    return subscription;
  }
  getLanguageList() {
    const subscription = this.apollo.use('eversanaClient').query<QueryResponse>({
      query: getLanguageList,
      variables: {
        sessionId: this._structureService.getCookie('authenticationToken')
      },
      fetchPolicy: 'network-only'
    });
    return subscription;
  }
  getEnrollmentDetails(enrollmentId) {
    const subscription = this.apollo.use('eversanaClient').query<QueryResponse>({
      query: getEnrollment,
      variables: {
        sessionId: this._structureService.getCookie('authenticationToken'),
        id: Number(enrollmentId)
      },
      fetchPolicy: 'network-only'
    });
    subscription.subscribe(({ data }) => { });
    return subscription;

  }
}
