<style>
    .cursor-hand {
        cursor: pointer;
    }
</style>
<!-- <span>jhyut<PERSON>tu</span> -->
<!-- <a class="pull-right btn btn-sm" (click)="onClick($event,'rowEdit')" >
<i class="fa fa-pencil" ></i>
</a> -->
<div class="btn-group mr-2 mb-2 no-margin" aria-label="" role="group">
    
    <a class="pull-right btn btn-sm {{element.cssClass}}" [ngClass]="{'disabled':element.disable}" (click)="onClick($event,element.label)" *ngFor="let element of singleIconElements"
        title="{{element.label}}">
        <i class="{{element.iconClass}}" ></i>
    </a>
</div>
<div class="btn-group" *ngFor="let element of multipleIconElements">
    <select class="action form-control" data-placeholder="Actions" (change)="onClick($event,element.label)">
        <option value="" hidden>{{element.label}}</option>
        <option *ngFor="let item of element.itemElements" value="{{item.action}}">{{item.label}}</option>
    </select>
</div>
