import {Component} from '@angular/core';
import {ICellRendererAngularComp} from "ag-grid-angular";

@Component({
    selector: 'floating-cell',
    template: `<span style="text-transform: capitalize;"> {{cellName}}</span>`,
})
export class GroupRowInnerRenderer implements ICellRendererAngularComp {
    cellName: any;

    agInit(params: any): void {
        const node = params.node;
        this.cellName = node.key;
    }

    refresh(): boolean {
        return false;
    }
}
