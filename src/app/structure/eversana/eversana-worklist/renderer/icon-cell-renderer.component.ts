import { Component } from '@angular/core';
import { INoRowsOverlayAngularComp } from 'ag-grid-angular';
import { Expression } from '@angular/compiler/src/output/output_ast';
declare var moment: any;

@Component({
    selector: 'app-icon-cell-renderer',
    template: `
  
    <span style="cursor:pointer" *ngIf="iconShow"><i class="{{icon}}" [style.color]="colour"></i> {{value}}</span>
    <span *ngIf="!iconShow">{{value}}</span>
    `
})
export class IconCellRenderer implements INoRowsOverlayAngularComp {
    private icon: string;
    private value: string;
    private colour: any;
    public iconShow: boolean;

    agInit(params): void {
        //     const image = params.value === 'Male' ? 'male.png' : 'female.png';;
        //     this.imageSource = `../images/${image}`
        this.value = params.value;
        let columnValue;
        if (typeof params.value == 'string') {
            columnValue = params.value.toLowerCase();
        } else {
            columnValue = params.value;
        }
        params.iconElements.forEach(element => {
            let fieldValue;
            if (typeof params.value == 'string') {
                fieldValue = element.fieldValue.toLowerCase();
            } else {
                fieldValue = element.fieldValue;
            }
            if (fieldValue == columnValue && element.expression == '==') {
                this.icon = element.iconClass;
                this.colour = element.iconColour;
                this.iconShow = true;
            } else if (columnValue != fieldValue && element.expression == '!=') {
                this.icon = element.iconClass;
                this.colour = element.iconColour;
                this.iconShow = true;
            } else if (columnValue > fieldValue && element.expression == '>') {
                this.icon = element.iconClass;
                this.colour = element.iconColour;
                this.iconShow = true;
            } else if (columnValue >= fieldValue && element.expression == '>=') {
                this.icon = element.iconClass;
                this.colour = element.iconColour;
                this.iconShow = true;
            } else if (columnValue < fieldValue && element.expression == '<') {
                this.icon = element.iconClass;
                this.colour = element.iconColour;
                this.iconShow = true;
            } else if (columnValue <= fieldValue && element.expression == '<=') {
                this.icon = element.iconClass;
                this.colour = element.iconColour;
                this.iconShow = true;
            } else if (columnValue && columnValue.includes(fieldValue) && element.expression == 'contains') {
                this.icon = element.iconClass;
                this.colour = element.iconColour;
                this.iconShow = true;
            } else {
                this.iconShow = false;
            }


            if (element.expressionType && element.expressionType == 'day') {
                var date = moment(params.value);
                let dayValues = element.day.split(',');
                if (dayValues.indexOf(date.day().toString()) != -1) {
                    this.icon = element.iconClass;
                    this.colour = element.iconColour;
                    this.iconShow = true;
                } else if (dayValues.indexOf('today') != -1) {
                    var today = moment(new Date()).format('MM/DD/YYYY');
                    if (date.isSame(today) == true) {
                        this.icon = element.iconClass;
                        this.colour = element.iconColour;
                        this.iconShow = true;
                    }
                } else if (dayValues.indexOf('yesterday') != -1) {
                    var yesterday = moment().subtract(1, 'days').format('MM/DD/YYYY');
                    if (date.isSame(yesterday) == true) {
                        this.icon = element.iconClass;
                        this.colour = element.iconColour;
                        this.iconShow = true;
                    }
                } else if (dayValues.indexOf('tomorrow') != -1) {
                    var tomorrow = moment().add(1, 'days').format('MM/DD/YYYY');
                    if (date.isSame(tomorrow) == true) {
                        this.icon = element.iconClass;
                        this.colour = element.iconColour;
                        this.iconShow = true;
                    }
                } else {
                    this.iconShow = false;
                }
            }
            if (element.expressionType && element.expressionType == 'month') {
                var date = moment(params.value);
                let monthValue = element.month.split(',');
                if (monthValue.indexOf(date.month().toString()) != -1) {
                    this.icon = element.iconClass;
                    this.colour = element.iconColour;
                    this.iconShow = true;
                } else {
                    this.iconShow = false;
                }
            }
            if (element.expressionType && element.expressionType == 'year') {
                var date = moment(params.value);
                if (element.year == date.year()) {
                    this.icon = element.iconClass;
                    this.colour = element.iconColour;
                    this.iconShow = true;
                } else {
                    this.iconShow = false;
                }
            }


        });

    }
}