import { Component, ViewChild, OnInit, Output, EventEmitter, Input, SimpleChange } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ButtonRendererComponent } from './renderer/button-renderer.component';
import { GroupRowInnerRenderer } from './renderer/group-row-inner-renderer.component';
import { IconCellRenderer } from './renderer/icon-cell-renderer.component';
import { <PERSON><PERSON><PERSON>orkListService } from './worklist.service';
import { FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../../structure.service';
import { InboxService } from '../../../structure/inbox/inbox.service';
import { userFilterPipe } from '../../../structure/inbox/inbox-modal-filter';
import { SharedService } from '../../../structure/shared/sharedServices';
import { DynamicService } from './dynamic-form/dynamic.service';
import { DropdownQuestion } from './dynamic-form/dynamic-dropdown';
import { DynamicBase } from './dynamic-form/dynamic-base';
import { TextboxQuestion } from './dynamic-form/dynamic-textbox';
import { CheckboxQuestion } from './dynamic-form/dynamic-checkbox';
import { RadioQuestion } from './dynamic-form/dynamic-radio';
import { IMyDpOptions, IMyDateModel } from 'mydatepicker';
import { ToastComponent } from '@syncfusion/ej2-angular-notifications';

declare var moment: any;
var momentTz = require('moment-timezone');
declare var swal: any;
declare var $: any;
var jstz = require('jstz');
import { DomSanitizer } from '@angular/platform-browser';
declare var NProgress: any;
import 'ag-grid-enterprise';
const timezone = jstz.determine();
@Component({
    selector: 'app-eversana-worklist',
    templateUrl: './worklist.component.html',

})
export class WorklistComponent implements OnInit {
    @ViewChild('element') element: ToastComponent;
    //@ViewChild('notificationMsg') notificationMsg: ToastComponent;
    @Input() worklistId;
    @Input('dynamicData') dynamicData: any;
    public position = { X: 'Right' };
    public gridApi;
    private gridColumnApi;
    private overlayLoadingTemplate;
    private overlayNoRowsTemplate;
    private operationType = 'Database';
    isRowMaster;
    rowGroupPanelShow;
    rowData: any = [];
    columnDefs = [];
    frameworkComponents: any;
    dashboardWidgets: any;
    reportFields: any;
    filterEnabledFields: any;
    actionFields: any;
    defaultColDef: any;
    formRowData = [];
    formId: number;
    formName: string;
    worklistDetails: any;
    formDataList = [];
    heading: string;
    helpMsg: string;
    addNewBtn = true;
    addNewBtnText: string;
    addNewBtnLink: string;
    worklistName: string;
    worklistid: number;
    dataLoadingMsg: boolean;
    structureFormContent: any = '';
    uniqueClass = '';
    showAll = false;
    showLess = true;
    hasUniqueId;
    staffList = [];
    patientList = [];
    clinicalUserDetails: any;
    staffLanguage = '';
    selectedEntry = '';
    searchTexts = '';
    searchFieldText = '';
    singleRowActions = [];
    shortLinkActions = [];
    singleRowAllActions = [];
    disableWidget = false;
    //multipleActionButtons = [];
    autoGroupColumnDef;
    filterField;
    filterCheck;
    cellClickFn;
    selectName;
    private groupRowInnerRenderer;
    private components;
    machFormIds = [];
    sideBar: any;
    assignType = 'single';
    modalType = 'staff';
    nurseList = [];
    list = [];
    refillDateType = 'due';
    selectedDays = '';
    updateData = [];
    rowModelType;
    cacheBlockSize;
    maxBlocksInCache;
    pagination;
    btnBehaviour = true;
    hideActionButtons = true;
    rowCount = 0;
    userData = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
    actionColumnPosition = '';
    formFieldFrom = '';
    metaData;
    formField;
    hideBulkEdit = true;
    bulkEditFields = [];
    dynamicFields: any[];
    form: FormGroup;
    enableBulkEdit = false;
    fieldValueList = [];
    updateProperty = '';
    formFiles = [];
    fileContent;
    detailCellRendererParams;
    selectedDate;
    mapFields = [];
    linkFields = [];
    childColumnDef = [];
    loadMsg = true;
    public myDatePickerOptions: IMyDpOptions = {
        dateFormat: 'mm/dd/yyyy',
    };
    editRowDetails;
    public dateModel: any = {};
    selectedRowDetails = [];
    apiType = '';
    rowSelection = '';
    showDateError = false;
    widgetData = [];
    visitDates = [];
    therapyForm: FormGroup;
    therapyDetails = [];
    editorFields = [];
    childFields = [];
    editorDynamicFields = [];
    showField = false;
    editTherapyId = '';
    therapyCount = 0;
    buttonLabel = 'Add';
    paginationPageSize = 0;
    cacheOverflowSize;
    maxConcurrentDatasourceRequests;
    infiniteInitialRowCount;
    columnWidth;
    suppressExcelExport = false;
    animateRows = false;
    cellChangeFlash = false;
    suppressCsvExport = false;
    showCustomExport = false;
    excelStyles;
    selectedWidget = '';
    selectedWidgetField = '';
    previousHideColumns = [];
    hideColumns = [];
    groupSelectsChildren;
    staffLoadingMsg = true;
    widgetCounts = [];
    selectedLoadingType = '';
    dashboardWidgetField = '';
    dashboardWidgetFieldId = '';
    refillType = true;
    dynamicFilter = false;
    dynamicFilterField = '';
    dynamicFilterLabel = '';
    showAllMsg = false;
    customSearchFields = {};
    allMessageTemplate = [];
    assignStaffActionConfig;
    modalHeading = '';
    onClickEvent = true;
    associatePatientLoading = false;
    assosiatedPatients: any = [];
    selectedAssosiatePatient: any = '';
    selectedAssosiatePatientName: any = '';
    selectedPatientName: any = '';
    //firstLoading = false;
    //deactivateRouting : boolean = true;
    selectedSearchFields = [];
    customSearch;
    assignPatientActionConfig;
    formContent: any = '';
    disableBtn = true;
    submitId = 0;
    responseStatus;
    enableIntegrationforAction = false;
    showTooltip = false;
    showResponse = false;
    showLoading = false;
    showNoData = false;
    deleteArray;
    constructor(private http: HttpClient,
        private _workListService: EversanaWorkListService,
        private router: Router,
        private _structureService: StructureService,
        private route: ActivatedRoute,
        private sanitizer: DomSanitizer,
        public _inboxService: InboxService,
        public modalFilter: userFilterPipe,
        private _sharedService: SharedService,
        private formBuilder: FormBuilder) {

            /**ag-grid loading message */
        this.overlayLoadingTemplate =
        '<span class="ag-overlay-loading-center">Please wait while we process your request</span>';
        this.overlayNoRowsTemplate = "<span>No data available to show.</span>";
    }
    ngOnInit() {
        this.worklistName = 'hcp-dashboard';
        this.worklistid = this.worklistId;
        this.dataLoadingMsg = true;
        //this.deactivateRouting = true;
        this.getWorklistdetails();
        /**Avoid duplicate entry while edit machform- authorization */
        var userdata = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
        const formsUrl = this._structureService.serverBaseUrl + '/machform/Upload/machform_authentication.php?fromCallBell=1&tenantId=' + this._structureService.getCookie("crossTenantId") + '&userId=' + userdata.userId;
        const iFrameContent = '<iframe onload="javascript:parent.scrollTo(0,0);" height="10" allowTransparency="true" frameborder="0" scrolling="no" style="width:100%;border:none" src="' + formsUrl + '" ></iframe>';
        this.structureFormContent = this.sanitizer.bypassSecurityTrustHtml(iFrameContent);
        this._structureService.socket.off('worklistDataUpdate').on('worklistDataUpdate', (data) => {
            const refillData = data['formData'];
            if (refillData.action == 'delete') {
                /**Data polling while delete entry */
                if (this.rowModelType == 'serverSide') {
                    this.element.show();
                } else {
                    this.rowData = this.rowData.filter(x => refillData.submissionId.indexOf(x.submissionID) == -1);
                    this.gridApi.setRowData(this.rowData);
                }
            } else if (refillData.action == 'submit') {
                /**Data polling while submit an entry */
                if (this.rowModelType == 'serverSide') {
                    this.element.show();
                } else {
                    this.rowData.push(refillData.rowData);
                    this.gridApi.setRowData(this.rowData);
                }
            } else if (refillData.action == 'refill') {
                /**Data polling while refill an entry */
                if (this.rowModelType == 'serverSide') {
                    this.element.show();
                } else {
                    if (this.showAll == true) {
                        this.rowData.push(refillData.rowData);
                    } else {
                        this.rowData.push(refillData.rowData);
                        this.rowData = this.rowData.filter(x => refillData.refillId.indexOf(x.submissionID) == -1);
                    }
                    this.gridApi.setRowData(this.rowData);
                }
            } else if (refillData.action == 'editForm') {
                /**Data polling while edit an entry */
                if (this.rowModelType == 'serverSide') {
                    this.element.show();
                } else {
                    let index = this.rowData.findIndex(x => refillData.submissionId.indexOf(x.submissionID) != -1);
                    this.rowData[index] = refillData.rowData;
                    this.gridApi.setRowData(this.rowData);
                }
            } else {
                /**Data polling while inline edit */
                refillData.submissionId.forEach(element => {
                    const index = this.rowData.findIndex(x => x.submissionID == element);
                    if (refillData.updateData.length > 1) {
                        refillData.updateData.forEach(element => {
                            this.rowData[index][element.updateField] = element.updatedValue;
                        });
                    } else {
                        this.rowData[index][refillData.updateData[0].updateField] = refillData.updateData[0].updatedValue;
                    }
                });
                this.gridApi.refreshCells({ force: true });
            }
        });
        
        this.frameworkComponents = {
            buttonRenderer: ButtonRendererComponent,
            groupRowInnerRenderer: GroupRowInnerRenderer,
            iconCellRenderer: IconCellRenderer
        };
        this.groupRowInnerRenderer = "groupRowInnerRenderer";
        /**Default configuration of column */
        this.defaultColDef = {
            resizable: true,
            enableFilter:true,
            //next three properties for the functionalities in column tool panel
            enableValue: true,
            enablePivot: true,
            headerComponentParams: {
                template:
                    '<div class="ag-cell-label-container" role="presentation">' +
                    '  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>' +
                    '  <div ref="eLabel" class="ag-header-cell-label" role="presentation">' +
                    '    <span ref="eSortOrder" class="ag-header-icon ag-sort-order" ></span>' +
                    '    <span ref="eSortAsc" class="ag-header-icon ag-sort-ascending-icon" ></span>' +
                    '    <span ref="eSortDesc" class="ag-header-icon ag-sort-descending-icon" ></span>' +
                    '    <span ref="eSortNone" class="ag-header-icon ag-sort-none-icon" ></span>' +
                    '    <span ref="eText" class="ag-header-cell-text" role="columnheader" ' +
                    '    style="font-family: PT Sans, sans-serif;font-size: 1.02rem;' +
                    '    font-weight: bold;color:#514d6a"></span>' +
                    '    <span ref="eFilter" class="ag-header-icon ag-filter-icon"></span>' +
                    '  </div>' +
                    '</div>'
            },
            autoHeight: true,
        };
        /**Excel header style while export ag grid data */
        // this.excelStyles = [
        //     {
        //         id: "header",
        //         interior: {
        //             color: "#CCCCCC",
        //             pattern: "Solid"
        //         }
        //     }
        // ];
        // this.helpMsg = 'Click on a tab to see the Patient Details under a category';
        // this.addNewBtnText = 'Add New';
        /** for initialize dynamic component for bulk edit functionality */
        let field: DynamicBase<any>[] = [];
        field.push(
            new DropdownQuestion({
                key: 'Dropdown',
                value: '',
                label: 'Test',
                controlType: 'dropdown',
                required: false,
                order: 1
            })
        );
        this.dynamicFields = field;
        let self = this;
        /**Define detail grid configurations - master/detail ag grid */
        this.detailCellRendererParams = {
            detailGridOptions: {
                components: {
                    datePicker: this.getDatePicker()
                },
                rowSelection: 'multiple',
                onSelectionChanged(event: any) {
                    self.onSelectionChanged(event);
                },
                onCellValueChanged(event: any) {
                    self.onCellValueChanged(event);
                },
                columnDefs: [],
                onFirstDataRendered(params) {
                    params.api.sizeColumnsToFit();
                },
            },
            getDetailRowData: function (params) {
                if (self.metaData.lazyChild == 'true') {
                    setTimeout(function () {
                        self._workListService.getChildFormData(self.metaData.associatedForm, self.metaData.childMapField, params.data[self.metaData.parentMapField]).refetch().then(({ data: response }) => {
                            let formDataList = [];
                            let formData = response['getAllFormDataMultipleForm'];
                            let i = 1;
                            formData.forEach(element => {
                                const formObj = {};
                                element.elements.forEach(elem => {
                                    formObj[elem.labelText] = elem.value;
                                });
                                formObj['submissionID'] = element.submissionID;
                                formDataList.push(formObj);
                                i++;
                            });
                            params.successCallback(formDataList);
                        });
                    }, 1000);
                } else {
                    params.successCallback(params.data.callRecords);
                }
            }
        };
        /**Only show the detail grid if it has value */
        this.isRowMaster = function (dataItem) {
            return dataItem ? (dataItem.callRecords) && dataItem.callRecords.length > 0 : false;
        };
        this.therapyForm = new FormGroup({});
    }
    ngOnChanges(changes: { [propKey: string]: SimpleChange }) {
        if (changes['dynamicData']) {
            this.worklistName = this.dynamicData.tabName;
            this.worklistid = this.worklistId;
            //this.optionShowButton = 'All';
            this.hideActionButtons = true;
            this.metaData = this.dynamicData.filterDetails;
            let metaData = this.metaData;
            this._workListService.activeTabId = this.dynamicData.worklistId;
            this._workListService.activeFilterDetails = this.dynamicData;
            const index = this._workListService.activeActivityDetails.findIndex(x => x.tabId == this.dynamicData.worklistId);
            this.cacheBlockSize = metaData.InitCountVal;
            this.maxBlocksInCache = metaData.blocksize;
            this.paginationPageSize = Number(metaData.pageSize);
            //this.getWorklistdetails(this.worklistid);            
            if (metaData.rowModel != 'server') {
                this.getDefaultGrid();
                this.showLoading = false;
                if (this.rowData && this.rowData.length == 0) {
                    this.showNoData = true;
                }
            } else {
                if (this.dynamicData && this.dynamicData.tabName) {
                    this.dataLoadingMsg = true;
                    this.worklistName = this.dynamicData.tabName;
                    this.worklistid = this.dynamicData.worklistId;
                    this.dataLoadingMsg = true;
                    if (metaData.rowModel == 'server') {
                        this.rowModelType = "serverSide";
                        if (metaData.dataLoadingType && metaData.dataLoadingType == 'pagination') {
                            this.pagination = true;
                            this.paginationPageSize = Number(metaData.pageSize);
                        } else {
                            this.pagination = false;
                        }
                    } else {
                        this._workListService.activeActivityDetails.push({
                            'tabId': this.dynamicData.worklistId
                        });
                        this.rowModelType = "clientSide";
                    }
                    this.getWorklistdetails();
                }
            }
        }
    }
    getDefaultGrid() {
        const index = this._workListService.activeActivityDetails.findIndex(x => x.tabId == this.dynamicData.pahworklistId);
        if (index != -1) {
            this.gridApi = this._workListService.activeActivityDetails[index].gridApi;
            this.gridApi.showLoadingOverlay();
            this.dashboardWidgets = this._workListService.activeActivityDetails[index].dashboardWidgets;
            this.cellClickFn = this._workListService.activeActivityDetails[index].cellClickFn;
            this.rowGroupPanelShow = this._workListService.activeActivityDetails[index].rowGroupPanelShow;
            this.sideBar = this._workListService.activeActivityDetails[index].sideBar;
            this.filterCheck = this._workListService.activeActivityDetails[index].filterCheck;
            this.selectName = this._workListService.activeActivityDetails[index].selectName;
            this.filterField = this._workListService.activeActivityDetails[index].filterField;
            this.singleRowActions = this._workListService.activeActivityDetails[index].singleRowActions;
            this.singleRowAllActions = this._workListService.activeActivityDetails[index].singleRowActions;
            this.columnDefs = this._workListService.activeActivityDetails[index].columnDefs;
            this.rowData = this._workListService.activeActivityDetails[index].rowData;
            this.setDefaultWidgetConfig(this.rowData);
            
            this.gridApi.hideOverlay();
            this.disableWidget = false;
        }
    }
    refreshGrid() {
        this.element.hide();
        this.gridApi.purgeServerSideCache();
    }
    /**Get the configuration details of worklist and build ag grid */
    getWorklistdetails() {
      
        this.columnDefs = [];
        this.childColumnDef = [];
        this.heading = '';
        this.formId = 0;
        this.formName = '';
        this.dashboardWidgets = [];
        this.rowData = [];
        this.addNewBtnLink = '';
        this.uniqueClass = '';
        this.actionFields = [];
        this.singleRowActions = [];
        this.shortLinkActions = [];
        this.btnBehaviour = true;
        this.hideActionButtons = true;
        this.suppressExcelExport = false;
        this.suppressCsvExport = false;
        this.showCustomExport = false;
        let metaData;
        this.metaData = {};
        /**Get details of particular worklist */
        this._workListService.getWorklistDetails(this.worklistid).then((data) => {
            if (data['getSessionTenant']) {
                this.worklistDetails = data['getSessionTenant'].formWorklists;
                this.heading = this.worklistDetails[0].name;
                if (this.worklistDetails[0].reportForms.length > 0) {
                    /**Machform id associated with worklist */
                    this.formId = this.worklistDetails[0].reportForms[0].id;
                }
                /**Link for add new button */
                this.addNewBtnLink = `worklist/${this.worklistName}/details/${this.worklistid}`;
                this.dataLoadingMsg = false;
                this.actionFields = [];
                /**Get the meta data of worklist*/
                let newJson = this.worklistDetails[0].description;
                newJson = newJson.replace(/'/g, '"');
                metaData = JSON.parse(newJson);
                this.metaData = metaData;
                this.metaData.graphqlApi = true;
                this.metaData.graphqlEndpoint = this.metaData.endpoint;
                // this.metaData.graphqlEndpoint = 'http://************:8090/eversana/graphql';
                // this.metaData.graphqlEndpoint = 'https://eversana-middleware-gqdss.citushealth.com/eversana/graphql';
                if (this.metaData.reportFormName) {
                    this.formName = this.metaData.reportFormName;
                }
                if (this.metaData.enableNotification == true) {
                    /**Get all message template if notification is enabled */
                    this._workListService.getWorklistMessageTemplate().then((data) => {
                        this.allMessageTemplate = JSON.parse(JSON.stringify(data['getSessionTenant'].formWorklistMessageTemplate));
                    });
                }
                /**for adding refill due filter */
                this.dynamicFilter = this.metaData.enableDynamicFilter;
                this.dynamicFilterField = this.metaData.filterField;
                this.dynamicFilterLabel = this.metaData.prefixText;
                /**Unique id for grouping entries based on a unique value */
                this.hasUniqueId = metaData.hasUniqueId;
                if (metaData.hasUniqueId && (localStorage.getItem('selectedLoadingType') == '' || localStorage.getItem('selectedLoadingType') == null)) {
                    this.uniqueClass = metaData.uniqueIdClass;
                    this.selectedLoadingType = '';
                    this.showAll = false;
                    this.showLess = true;
                } else {
                    this.showAll = true;
                    this.showLess = false;
                    this.selectedLoadingType = 'allRecords';
                    localStorage.setItem('selectedLoadingType', '');
                }
                if (localStorage.getItem('selectedSearchFieldText') !== '' && localStorage.getItem('selectedSearchFieldText') !== null) {
                    this.searchFieldText = localStorage.getItem('selectedSearchFieldText');
                } else {
                    this.searchFieldText = '';
                    localStorage.setItem('selectedSearchFieldText', '')
                }
                /**Default column width */
                this.columnWidth = this.metaData.defaultColumnWidth;
                /** Single/Multiple row selection */
                this.rowSelection = this.metaData.checkboxSelectionType;

                if (this.metaData.checkboxSelectionType == 'multiple') {
                    /**select all sub entries of group while select the checkbox for the group */
                    this.groupSelectsChildren = true;
                } else {
                    this.groupSelectsChildren = false;
                }
                /** form label from either label text or from css class */
                this.formFieldFrom = metaData.formFieldFrom;
                /**determine the position of action button */
                this.btnBehaviour = metaData.btnBehaviour;
                this.actionColumnPosition = metaData.btnPosition;
                /**set Lazy loading parameters */
                this.cacheBlockSize = Number(metaData.InitCountVal);
                this.maxBlocksInCache = Number(metaData.blocksize);
                /**Page size */
                this.paginationPageSize = Number(metaData.pageSize);
                if (metaData.rowModel == 'server') {
                    /**Set data loading type as server side */
                    this.rowModelType = 'serverSide';
                    if (metaData.dataLoadingType && metaData.dataLoadingType == 'pagination') {
                        this.pagination = true;
                    } else {
                        this.pagination = false;
                    }
                } else {
                    /**Set data loading type as client side */
                    this.rowModelType = 'clientSide';
                    if (metaData.pageSize != null && metaData.pageSize != '') {
                        this.pagination = true;
                    } else {
                        this.pagination = false;
                    }
                }
                /**Get the reportFields and sort  */
                this.reportFields = JSON.parse(JSON.stringify(this.worklistDetails[0].reportFields));
                /**Set dynamic fields of report field those having bulk edit feature */
                this.setDynamicFields();
                this.reportFields.sort(function (a, b) {
                    if (a.displayIndex < b.displayIndex) { return -1; }
                    if (a.displayIndex > b.displayIndex) { return 1; }
                    return 0;
                });
                /**Filter field */
                this.selectedSearchFields = this.reportFields.filter(x => x.allowQuickSearch == true && x.enableAutoSelect == true);
                this.filterEnabledFields = this.reportFields.filter(x => x.allowQuickSearch == true && x.visibility == true);
                
                /**Child table fields in master/detail type ag grid */
                this.childFields = this.reportFields.filter(x => x.fieldType == 'child');
                /**Check column definition is present in shared service */
                /**Save configuration data to shared service in initial loading and get that data ifrom shared service in further loading */
                const defIndex = this._sharedService.worklistColumnDef.findIndex(x => x.worklistId == this.worklistid);
                if (defIndex == -1) {
                    this.setColumnDefinition(this.actionColumnPosition);
                    this._sharedService.worklistColumnDef.push({
                        worklistId: this.worklistid,
                        columnDef: this.columnDefs,
                        childColumnDef: this.childColumnDef,
                        edited: false,
                        actionColDef: this.singleRowActions,
                        shortLinks: this.shortLinkActions,
                        linkFields: this.linkFields,
                        mapFields: this.mapFields,
                        fieldPrefillData: this.list
                    });
                } else {
                    if (this._sharedService.worklistColumnDef[defIndex].edited) {
                        this.setColumnDefinition(this.actionColumnPosition);
                        this._sharedService.worklistColumnDef[defIndex].edited = false;
                        this._sharedService.worklistColumnDef[defIndex].columnDef = this.columnDefs;
                        this._sharedService.worklistColumnDef[defIndex].actionColDef = this.singleRowActions;
                        this._sharedService.worklistColumnDef[defIndex].shortLinks = this.shortLinkActions;
                        this._sharedService.worklistColumnDef[defIndex].childColumnDef = this.childColumnDef;
                        this._sharedService.worklistColumnDef[defIndex].mapFields = this.mapFields;
                        this._sharedService.worklistColumnDef[defIndex].linkFields = this.linkFields;
                        this._sharedService.worklistColumnDef[defIndex].fieldPrefillData = this.list;
                    } else {
                        this.columnDefs = this._sharedService.worklistColumnDef[defIndex].columnDef;
                        this.childColumnDef = this._sharedService.worklistColumnDef[defIndex].childColumnDef;
                        this.singleRowActions = this._sharedService.worklistColumnDef[defIndex].actionColDef;
                        this.shortLinkActions = this._sharedService.worklistColumnDef[defIndex].shortLinks;
                        this.detailCellRendererParams.detailGridOptions['columnDefs'] = [];
                        this.detailCellRendererParams.detailGridOptions['columnDefs'] = this._sharedService.worklistColumnDef[defIndex].childColumnDef;
                        this.mapFields = this._sharedService.worklistColumnDef[defIndex].mapFields;
                        this.linkFields = this._sharedService.worklistColumnDef[defIndex].linkFields;
                        this.list = this._sharedService.worklistColumnDef[defIndex].fieldPrefillData;
                    }
                }
                /**Adding datepicker while editing cell having date value */
                this.components = { datePicker: this.getDatePicker() };
                /**Row grouping settings of ag grid */
                if (this.metaData.enableRowGroup == true) {
                    this.rowGroupPanelShow = 'always';
                }
                /**Default group definition */
                this.autoGroupColumnDef = {
                    headerName: 'Group',
                    width: 200,
                    cellRenderer: "agGroupCellRenderer",
                    cellRendererParams: { checkbox: true }
                };
                /**Showing add new button on right top corner */
                this.addNewBtn = this.metaData.addNewButton;
                /**Set the dashboard widgets */
                this.dashboardWidgets = JSON.parse(JSON.stringify(this.worklistDetails[0].dashboardWidgets));
                this.dashboardWidgets.sort(function (a, b) {
                    if (a.displayIndex < b.displayIndex) { return -1; }
                    if (a.displayIndex > b.displayIndex) { return 1; }
                    return 0;
                });
                if (this.dashboardWidgets.length > 0) {
                    /**Set selected widget for showing that widget highlighted */
                    this.selectedWidget = this.dashboardWidgets[0].widgetValue;
                }
                if (this.gridApi) {
                    /**set column definition in ag grid */
                    this.gridApi.setColumnDefs(this.columnDefs);
                }
                /**Set export option */
                if (metaData.enableExcelExport == false) {
                    this.suppressExcelExport = true;
                }
                if (metaData.enableCsvExport == false) {
                    this.suppressCsvExport = true;
                }
                if (metaData.enableAnimateRows == true) {
                    this.animateRows = true;
                }
                if (metaData.enableCellChangeFlash == true) {
                    this.cellChangeFlash = true;
                }
                /**Add external custom export button */
                if (metaData.enableCustomExport == true) {
                    this.showCustomExport = true;
                }
                /**Set the right side panel of ag grid */
                this.sideBar = {
                    toolPanels: []
                };
                let panelObj = {};
                if (metaData.filterPanel == 'true') {
                    panelObj = {
                        id: 'filters',
                        labelDefault: 'Filters',
                        labelKey: 'filters',
                        iconKey: 'filter',
                        toolPanel: 'agFiltersToolPanel'
                    };
                    this.sideBar.toolPanels.push(panelObj);
                }
                if (metaData.columnPanel == 'true') {
                    panelObj = {
                        id: 'columns',
                        labelDefault: 'Columns',
                        labelKey: 'columns',
                        iconKey: 'columns',
                        toolPanel: 'agColumnsToolPanel',
                        toolPanelParams: {
                            suppressRowGroups: true,
                            suppressValues: true,
                            suppressPivots: true,
                            suppressPivotMode: true,
                        }
                    };
                    this.sideBar.toolPanels.push(panelObj);
                }
            } else {
                this._structureService.deleteCookie('authenticationToken');
                this.router.navigate(['/login']);
            }
        });
    }
    setColumnDefinition(position) {
        /**Action button cofiguration - single and multiple */
        let columnObj = {};
        this.list = [];
        this.detailCellRendererParams.detailGridOptions['columnDefs'] = [];
        this.actionFields = JSON.parse(JSON.stringify(this.worklistDetails[0].singleWorklistAction));
        this.actionFields['actionButton'].forEach(element => {
            const obj = {
                label: element.actionLabel,
                iconClass: element.actionIcon,
                disable: false,
                toolTip: element.tooltiptext,
                cssClass: element.cssClass,
                onClick: '',
                type: '',
                showAlert: false,
                buttonStyle: '',
                itemElements: [],
                callbackfunction: element.actionCallbackFunction,
                actionLink: element.actionLink,
                enableIntegration: element.enableIntegration,
                actionFields: element.actionFields,
                actionField: element.formField ? element.formField : '',
                mapField: element.mapField ? element.mapField : '',
                actionMode: element.actionMode ? element.actionMode : '',
                selectionMode: element.selectionMode ? element.selectionMode : '',
                fieldValues: element.fieldValues,
                notification: {
                    enableNotification: element.enableNotification,
                    push: element.push,
                    pushTemplate: element.pushTemplate,
                    sms: element.sms,
                    smsTemplate: element.smsTemplate,
                    email: element.email,
                    emailTemplate: element.emailTemplate,
                    notifyRolesOnAction: element.notifyRolesOnAction,
                    notifyRecipientOnAction: element.notifyRecipientOnAction
                }
            };
            if (element.actionLink === 'linkPAH' || element.actionCallbackFunction === 'linkPAH') {
                obj.onClick = this.linkPAH.bind(this);
            }
            
            if (element.actionButtonType == 'single') {
                obj.type = 'single';
                /**Short links are shown above ag grid for rerouting to any other page */
                if (element.shortLink == true) {
                    this.shortLinkActions.push(obj);
                } else {
                    this.singleRowActions.push(obj);
                }
            } else {
                obj.type = 'multiple';
                obj.buttonStyle = element.buttonStyle;
                obj.onClick = this.multipleButtonAction.bind(this);
                element.actionMenu.forEach(item => {
                    const itemObj = {
                        label: item.itemName,
                        disable: false,
                        action: '',
                        itemField: item.itemFormField,
                    };
                    if (item.itemActionType == 'link') {
                        itemObj.action = item.itemActionLink;
                    } else {
                        itemObj.action = item.itemActionCallbackFunction;
                    }
                    obj.itemElements.push(itemObj);
                });
                this.singleRowActions.push(obj);
            }
        });
        if (position == 'first') {
            if (this.btnBehaviour) {
                this.columnDefs.push(
                    {
                        headerName: 'Actions',
                        field: 'action',
                        minWidth: 200,
                        suppressSorting: true,
                        suppressFilter: true,
                        cellRenderer: 'buttonRenderer',
                        cellRendererParams: {
                            label: 'Label',
                            iconElements: this.singleRowActions
                        }
                    }
                );
            }
        }
        /**Configure column definition */
        let i = 1;
        this.reportFields.forEach(element => {
            columnObj = {};

            if (element.fieldName == 'submissionID' && this.rowModelType == 'clientSide') {
                /**Add default sorting based submission id in client side loading */
                columnObj['sort'] = { direction: 'desc', priority: 0 };
            }
            /**Table column header name */
            columnObj['headerName'] = element.headerName;
            /**form field corresponding to each column */
            columnObj['field'] = element.fieldName;
            if (element.cellEvent) {
                columnObj['cellEvent'] = element.cellEvent;
            }
            /**Set minimum width for column */
            if (element.columnWidth && element.columnWidth != '') {
                columnObj['minWidth'] = Number(element.columnWidth);
            } else {
                if (this.columnWidth && this.columnWidth != '') {
                    columnObj['minWidth'] = Number(this.columnWidth);
                } else {
                    columnObj['minWidth'] = 200;
                }
            }
            /**check whether the column is visible or not*/
            if (element.visibility === false || element.visibility === null) {
                columnObj['hide'] = true;
            }
            /**check whether the cell can be edit*/
            if (element.allowEdit == true) {
                columnObj['editable'] = function (params) {
                    // if(params.data['Status'] == 'Pending Assignment') {
                    //   return false;
                    // } else {
                    //   return true;
                    // }
                    return true;
                }
            }

            /**Value formatter for null value */
            if (element.allowCellValueChange) {
                columnObj['valueFormatter'] = function cellFormatterFn(params) {
                    if (params.value == '') {
                        if (element.newCellValue && element.newCellValue != '') {
                            return element.newCellValue;
                        } else {
                            return params.value;
                        }
                    }
                };
            }
            /**Format cell value if field value delimitter is present */
            if (element.fieldName.includes('.')) {
                columnObj['valueFormatter'] = function cellFormatterFn(params) {
                    return params.value;
                }
            }
            if (element.fieldValueDelimiter && element.fieldValueDelimiter != '') {
                columnObj['valueFormatter'] = function cellFormatterFn(params) {
                    if (element.fieldValueDelimiter.length == 1) {
                        if (params.value.includes(element.fieldValueDelimiter)) {
                            const originalValue = params.value.split(element.fieldValueDelimiter);
                            return originalValue[0];
                        }
                    } else if (element.fieldValueDelimiter.length == 2) {
                        if (params.value.includes(element.fieldValueDelimiter.charAt(0)) && params.value.includes(element.fieldValueDelimiter.charAt(1))) {
                            let originalValue = params.value.split(element.fieldValueDelimiter.charAt(0));
                            return originalValue[0];
                        }
                    } else {
                        return params.value;
                    }
                };
            }
            /**map field  used in master/detail page*/
            if (element.mapField) {
                this.mapFields.push({ fieldName: element.fieldName, mapField: element.mapField, type: element.valueType });
            }
            /**fields and its values to prepopulate to the form while adding new entries */
            if (element.linkField) {
                this.linkFields.push({ fieldName: element.fieldName, mapField: element.fieldId, linkField: element.linkField, type: element.valueType });
            }
            if (element.allowCellFormat == true && element.cellFormat) {
                let cssFormat = element.cellFormat;
                if (cssFormat.length > 0) {
                    columnObj['valueFormatter'] = function cellRenderer(params) {
                        if (params.value != '') {
                            let columnValue = params.value;
                            for (let i = 0; i < cssFormat.length; i++) {
                                let fieldValue;
                                fieldValue = cssFormat[i].cellValue;
                                if (cssFormat[i].expression == '==' && columnValue == fieldValue) {
                                    return cssFormat[i].formattedValue;
                                }
                                if (cssFormat[i].expression == '!=' && columnValue != fieldValue) {
                                    return cssFormat[i].formattedValue;
                                }
                                if (cssFormat[i].expression == '>' && columnValue > fieldValue) {
                                    return cssFormat[i].formattedValue;
                                }
                                if (cssFormat[i].expression == '>=' && columnValue >= fieldValue) {
                                    return cssFormat[i].formattedValue;
                                }
                                if (cssFormat[i].expression == '<' && columnValue < fieldValue) {
                                    return cssFormat[i].formattedValue;
                                }
                                if (cssFormat[i].expression == '<=' && columnValue <= fieldValue) {
                                    return cssFormat[i].formattedValue;
                                }
                                if (cssFormat[i].expression == 'contains' && columnValue && columnValue.includes(fieldValue)) {
                                    return cssFormat[i].formattedValue;
                                }
                            }
                        }

                    };
                }
            }
            /**Cell style property */
            if (element.allowCellStyle == true && element.cellStyles) {
                let cssStyles = element.cellStyles.filter(x => x.contentType == "text");
                if (cssStyles.length > 0) {

                    columnObj['cellStyle'] = function (params) {
                        let columnValue;
                        if (typeof params.value == 'string') {
                            columnValue = params.value.toLowerCase();
                        } else {
                            columnValue = params.value;
                        }
                        for (let i = 0; i < cssStyles.length; i++) {
                            let fieldValue;
                            if (typeof params.value == 'string') {
                                fieldValue = cssStyles[i].fieldValue.toLowerCase();
                            } else {
                                fieldValue = cssStyles[i].fieldValue;
                            }

                            if (cssStyles[i].expression == '==' && columnValue == fieldValue) {
                                // columnValue = cssStyles[i].fieldText;
                                return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                            }
                            if (cssStyles[i].expression == '!=' && columnValue != fieldValue) {
                                return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                            }
                            if (cssStyles[i].expression == '>' && columnValue > fieldValue) {
                                return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                            }
                            if (cssStyles[i].expression == '>=' && columnValue >= fieldValue) {
                                return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                            }
                            if (cssStyles[i].expression == '<' && columnValue < fieldValue) {
                                return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                            }
                            if (cssStyles[i].expression == '<=' && columnValue <= fieldValue) {
                                return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                            }
                            if (cssStyles[i].expression == 'contains' && columnValue && columnValue.includes(fieldValue)) {
                                return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                            }
                            if (cssStyles[i].expressionType && cssStyles[i].expressionType == 'day') {
                                var date = moment(params.value);
                                let dayValues = cssStyles[i].day.split(',');
                                if (dayValues.indexOf(date.day().toString()) != -1) {
                                    return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                                } else if (dayValues.indexOf('today') != -1) {
                                    var today = moment(new Date()).format('MM/DD/YYYY');
                                    if (date.isSame(today) == true) {
                                        return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                                    }
                                } else if (dayValues.indexOf('yesterday') != -1) {
                                    var yesterday = moment().subtract(1, 'days').format('MM/DD/YYYY');
                                    if (date.isSame(yesterday) == true) {
                                        return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                                    }
                                } else if (dayValues.indexOf('tomorrow') != -1) {
                                    var tomorrow = moment().add(1, 'days').format('MM/DD/YYYY');
                                    if (date.isSame(tomorrow) == true) {
                                        return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                                    }
                                }
                            }
                            if (cssStyles[i].expressionType && cssStyles[i].expressionType == 'month') {
                                var date = moment(params.value);
                                let monthValue = cssStyles[i].month.split(',');
                                if (monthValue.indexOf(date.month().toString()) != -1) {
                                    return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                                }
                            }
                            if (cssStyles[i].expressionType && cssStyles[i].expressionType == 'year') {
                                var date = moment(params.value);
                                if (cssStyles[i].year == date.year()) {
                                    return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                                }
                            }
                        }
                    }
                }
                let iconStyles = element.cellStyles.filter(x => x.contentType == "icon");
                if (iconStyles.length > 0) {
                    columnObj['cellRenderer'] = 'iconCellRenderer';
                    let iconArray = [];
                    for (let i = 0; i < iconStyles.length; i++) {
                        let iconObj;
                        iconObj = {
                            iconColour: iconStyles[i].iconColor, iconClass: iconStyles[i].iconClass,
                            fieldValue: iconStyles[i].fieldValue, expression: iconStyles[i].expression,
                            expressionType: iconStyles[i].expressionType, day: iconStyles[i].day,
                            month: iconStyles[i].month, year: iconStyles[i].year,
                        }
                        iconArray.push(iconObj);
                    }
                    columnObj['cellRendererParams'] = {
                        iconElements: iconArray
                    }
                }
            }
            /**Set the column group feature*/
            let columnGroups = [];
            if (element.groupName != '' || element.groupName != '') {
                columnGroups.push(element.groupName);
                let obj = {
                    headerName: element.headerName,
                    field: element.fieldName,
                }
            }
            /**formating time */
            if (element.valueType === 'time') {
                if (element.allowCellValueChange && element.newCellValue) {
                    columnObj['valueFormatter'] = function cellFormatterFn(params) {
                        return element.newCellValue;
                    };
                } else {
                    columnObj['valueFormatter'] = this.convertTimeFormat;
                }
            }
            /**Add sorting feature */
            if (element.allowSort === false) {
                columnObj['suppressSorting'] = true;
            }
            /**Add filter feature */
            if (element.allowFilter === false) {
                columnObj['suppressFilter'] = true;
            } else {
                let obj = {
                    newRowsAction: 'keep',
                    suppressRemoveEntries: true,
                    applyButton: element.applyFilter ? element.applyFilter : false,
                    clearButton: element.clearFilter ? element.clearFilter : false,
                    suppressAndOrCondition: true
                };
                if (element.valueType == 'checkbox' || (element.fieldValueDelimiter && element.fieldValueDelimiter != '')) {
                    obj['filterOptions'] = ['contains'];
                    columnObj['filterParams'] = obj;
                } else if (element.valueType == 'radio' || element.valueType == 'select') {
                    obj['filterOptions'] = ['contains', 'equals'];
                    columnObj['filterParams'] = obj;
                } else {
                    columnObj['filterParams'] = obj;
                }
                /**Set different kind of filterings */
                if (element.filterType === 'Number_Filter') {
                    columnObj['filter'] = 'agNumberColumnFilter';
                }
                if (element.filterType === 'Text_Filter') {
                    columnObj['filter'] = 'agTextColumnFilter';
                }
                if (element.filterType === 'Date_filter') {
                    columnObj['filter'] = 'agDateColumnFilter';
                }
            }
            /**Process date value*/
            if (element.valueType === 'date') {
                /**Show datepicker while editing */
                columnObj['cellEditor'] = 'datePicker';
                /**Format date to a particular format*/
                if (element.allowCellValueChange && element.newCellValue) {
                    columnObj['valueFormatter'] = function cellFormatterFn(params) {
                        return element.newCellValue;
                    };
                } else {
                    columnObj['valueFormatter'] = this.convertDateFormat;
                }
                /**Implement sorting of date fields */
                columnObj['comparator'] = this.dateComparator;
                /**Implement date filter functionality */
                columnObj['filterParams'] = {
                    inRangeInclusive: true,
                    newRowsAction: 'keep',
                    suppressRemoveEntries: true,
                    suppressAndOrCondition: true,
                    applyButton: element.applyFilter ? element.applyFilter : false,
                    clearButton: element.clearFilter ? element.clearFilter : false,
                    comparator: function (filterLocalDateAtMidnight, cellValue) {
                        const dateParts = cellValue.split("/");
                        const cellDate = new Date(dateParts);
                        if (cellDate < filterLocalDateAtMidnight) {
                            return -1;
                        } else if (cellDate > filterLocalDateAtMidnight) {
                            return 1;
                        } else {
                            return 0;
                        }
                    }
                };
            }
            /**Enable row grouping for each column */
            columnObj['enableRowGroup'] = false;
            if (element.allowRowGrouping) {
                columnObj['enableRowGroup'] = true;
            }
            /**Enable checkbox selection for each column */
            if (element.checkboxSelection) {
                columnObj['checkboxSelection'] = true;
                if (this.rowSelection == 'multiple' && this.rowModelType == 'clientSide') {
                    /** header checkbox selection only supported for client side loading */
                    /**for adding checkbox in header also */
                    columnObj['headerCheckboxSelection'] = true;
                    /**Set the property of header check box */
                    if (this.metaData.headerCheckboxMode == 'filtered') {
                        columnObj['headerCheckboxSelectionFilteredOnly'] = true;
                    } else {
                        columnObj['headerCheckboxSelectionFilteredOnly'] = false;
                    }
                }
                /**For Master detail grid view */
                if (this.metaData.worklistType == 'master/detail') {
                    columnObj['cellRenderer'] = 'agGroupCellRenderer';
                }
            }
            /**For Master detail grid view */
            if (this.metaData.worklistType == 'master/detail' && i == 1) {
                columnObj['cellRenderer'] = 'agGroupCellRenderer';
            }
            if (element.allowPrefillData == true) {
                /**prefill option in inline cell editing */
                if (element.prefillMethod == 'formOptions') {
                    let formElementId: number;
                    let formId: number;
                    let formType = 'internal';
                    let extraOptions = [];
                    if (element.prefillFormOption == 'internal') {
                        if (element.fieldType == 'child') {
                            formId = this.metaData.associatedForm;
                        } else {
                            formId = this.formId;
                        }
                        if (element.fieldId) {
                            formElementId = element.fieldId;
                        }
                    } else {
                        formType = 'external';
                        formId = element.prefillOptionForm;
                        formElementId = element.prefillOptionFormField;
                        /**Add extra option*/
                        if (element.prefillExtraOptions && element.prefillExtraOptions != '') {
                            extraOptions = element.prefillExtraOptions.split('||');
                        }
                    }
                    /**Show values in dropdown while double click on cell */
                    columnObj['cellEditor'] = 'agRichSelectCellEditor';
                    columnObj['cellEditorParams'] = {
                        values: undefined
                    };
                    /**Get the options of a machform field*/
                    this._workListService.getMachformFields(formId, Number(formElementId)).refetch().then(({ data: response }) => {
                        this.list[element.fieldName] = JSON.parse(JSON.stringify(response['getFormElementDetails'].options));
                        this.list[element.fieldName].push({ 'type': formType });
                        const elementList = [];
                        response['getFormElementDetails'].options.forEach(element => {
                            elementList.push(element.optionValue);
                        });
                        extraOptions.forEach(element => {
                            elementList.push(element.trim());
                        });
                        /**set dropdown list while editing cell */
                        this.setDropdownList(elementList, element.fieldName, element.fieldType);
                    });
                }
            }
            /**set grouping for ag grid */
            if (element.groupName != '' && element.groupName != null && element.visibility == true) {
                const groupIndex = this.columnDefs.findIndex(x => x.headerName == element.groupName && x.children.length != 0);
                if (groupIndex == -1) {
                    const childObj = {
                        children: []
                    };
                    childObj['headerName'] = element.groupName;
                    childObj['marryChildren'] = true;
                    childObj['children'].push(columnObj);
                    this.columnDefs.push(childObj);
                } else {
                    columnObj['columnGroupShow'] = 'open';
                    this.columnDefs[groupIndex]['children'].push(columnObj);
                }
            } else {
                if (element.fieldType && element.fieldType == 'child') {
                    this.childColumnDef.push(columnObj);
                    this.detailCellRendererParams.detailGridOptions['columnDefs'].push(columnObj);
                } else {
                    this.columnDefs.push(columnObj);
                }
            }
            i++;
        });
        if (position == 'last') {
            if (this.btnBehaviour) {
                this.columnDefs.push(
                    {
                        headerName: 'Actions',
                        field: 'action',
                        minWidth: 200,
                        suppressSorting: true,
                        suppressFilter: true,
                        cellRenderer: 'buttonRenderer',
                        cellRendererParams: {
                            label: 'Label',
                            iconElements: this.singleRowActions
                        }
                    }
                );
            }
        }
    }
    /**List options while edit cell */
    setDropdownList(list, field, gridType) {
        let colDef;
        if (gridType == 'child') {
            colDef = this.childColumnDef;
        } else {
            colDef = this.columnDefs;
        }
        colDef.forEach(element => {
            if (element.field == field) {
                element.cellEditorParams.values = list;
            } else {
                if (element.children && element.children.length > 0) {
                    let index = element.children.findIndex(x => x.field == field);
                    if (index != -1) {
                        element.children[index].cellEditorParams.values = list;
                    }
                }
            }
        });
    }
    /**Bulk edit functionality */
    bulkEditAction() {
        $('#bulk-edit').modal('show');
        this.updateProperty = '';
        this.enableBulkEdit = false;
    }
    /**set dynamic form components for bulk edit */
    setDynamicFields() {
        this.dynamicFields = [];
        this.bulkEditFields = this.reportFields.filter(x => x.allowBulkEdit == true);
        let field: DynamicBase<any>[] = [];
        this.bulkEditFields.forEach(element => {
            let formElementId;
            formElementId = element.fieldId;
            this._workListService.getMachformFields(this.formId, Number(formElementId)).refetch().then(({ data: response }) => {
                let elementDetails = response['getFormElementDetails'];
                this.fieldValueList[element.fieldName] = elementDetails['options'];
                let fieldOptions = elementDetails['options'];
                let hasOtherOption: boolean;
                let option = [];
                if (element['valueType'] == 'text') {
                    field.push(
                        new TextboxQuestion({
                            key: element.fieldName,
                            value: '',
                            fieldName: element.fieldName,
                            label: element.fieldName,
                            controlType: 'textbox',
                            required: false,
                            options: option,
                            order: 1,
                            show: true
                        })
                    );
                }
                if (fieldOptions.length > 0) {
                    if (elementDetails['type'] == 'checkbox' || elementDetails['type'] == 'radio' || elementDetails['type'] == 'select') {
                        option = JSON.parse(JSON.stringify(fieldOptions));
                        if (elementDetails['hasOtherOption'] == 1) {
                            option.push({ 'optionId': 'Others', 'optionValue': elementDetails['otherLabelText'] });
                            element.hasOtherOption = true;
                        } else {
                            element.hasOtherOption = false;
                        }
                    }

                    if (elementDetails['type'] == 'checkbox') {
                        option.forEach(element1 => {
                            field.push(
                                new CheckboxQuestion({
                                    key: element1.optionId == 'Others' ? 'Others' : element1.optionValue.replace(/\s/g, ""),
                                    value: '',
                                    fieldName: element.fieldName,
                                    label: element1.optionValue,
                                    controlType: elementDetails['type'],
                                    required: false,
                                    order: 1,
                                    show: true
                                })
                            );
                        });
                    } else if (elementDetails['type'] == 'radio') {
                        field.push(
                            new RadioQuestion({
                                key: element.fieldName,
                                value: '',
                                fieldName: element.fieldName,
                                label: element.fieldName,
                                controlType: elementDetails['type'],
                                required: false,
                                options: option,
                                order: 1,
                                show: true
                            })
                        );
                    } else if (elementDetails['type'] == 'select') {
                        field.push(
                            new DropdownQuestion({
                                key: element.fieldName,
                                value: '',
                                fieldName: element.fieldName,
                                label: element.fieldName,
                                controlType: 'dropdown',
                                required: false,
                                options: option,
                                order: 1,
                                show: true
                            })
                        );
                    }
                }
                this.dynamicFields = field;
            });
        });
    }
    showFormElement(e) {
        this.enableBulkEdit = false;
        let selectedRow = this.gridApi.getSelectedRows();
        let formElementName;
        let index = this.bulkEditFields.findIndex(x => x.fieldId == e.target.value);
        if (index != -1) {
            formElementName = this.bulkEditFields[index].fieldName;
        }
        this.updateData = [];
        this.updateData.push({
            'rowData': selectedRow,
            'worklistId': this.worklistid,
            'formId': this.formId,
            'entryId': selectedRow.map(x => x.submissionID),
            'elementId': e.target.value,
            'elementType': formElementName,
            'hasOtherOption': this.bulkEditFields[index].hasOtherOption,
            'visibleToRoles': this.metaData.visibleToRoles
        });
        setTimeout(() => {
            this.enableBulkEdit = true;
        }, 100);
    }
    /**For the correct working of date sorting in ag grid */
    dateComparator(date1, date2) {
        let cdate1 = new Date(date1);
        date1 = moment.utc(cdate1).format('DD/MM/YYYY');
        let cdate2 = new Date(date2);
        date2 = moment.utc(cdate2).format('DD/MM/YYYY');
        let date1Number: number;
        if (date1 === undefined || date1 === null || date1.length !== 10) {
            date1Number = null;
        }
        var result = date1.substring(6, 10) * 10000 + date1.substring(3, 5) * 100 + date1.substring(0, 2);
        date1Number = result;
        let date2Number: number;
        if (date2 === undefined || date2 === null || date2.length !== 10) {
            date2Number = null;
        }
        var result = date2.substring(6, 10) * 10000 + date2.substring(3, 5) * 100 + date2.substring(0, 2);
        date2Number = result;
        if (date1Number === null && date2Number === null) {
            return 0;
        }
        if (date1Number === null) {
            return -1;
        }
        if (date2Number === null) {
            return 1;
        }
        return date1Number - date2Number;
    }
    getDatePicker() {
        function Datepicker() { }
        Datepicker.prototype.init = function (params) {
            // create the cell
            this.eInput = document.createElement('input');
            this.eInput.value = params.value;
            $(this.eInput).datepicker({
                dateFormat: 'mm/dd/yy',
                changeMonth: true,
                changeYear: true,
                yearRange: 'c-100:c+100'
            });
        };
        Datepicker.prototype.getGui = function () {
            return this.eInput;
        };
        Datepicker.prototype.afterGuiAttached = function () {
            this.eInput.focus();
            this.eInput.select();
        };
        Datepicker.prototype.getValue = function () {
            return this.eInput.value;
        };
        Datepicker.prototype.destroy = function () { };
        Datepicker.prototype.isPopup = function () {
            return false;
        };
        return Datepicker;
    }
    convertDateFormat(params) {
        if (!params.value) {
            return '';
        }
        if (params.value || params.value != '') {
            const dateParts = params.value.toString().split("/");
            if (dateParts.length <= 1) {
                let newDate;
                if (Number(params.value) !== NaN) {
                    let newDate = new Date(Number(params.value) * 1000);
                } else {
                    let newDate = new Date(params.value);
                }
                return moment.utc(newDate).format('MM/DD/YYYY');
            } else {
                return params.value;
            }
        } else {
            return '';
        }

    }
    convertTimeFormat(params) {
        if (!params.value) {
            return '';
        }
        if (params.value || params.value != '') {
            return moment(params.value, ['HH:mm']).format('h:mm A');
        }
    }
    /**custom filtering of past and due date */
    setRefillValue(type, value, field) {
        this.refillDateType = type;
        if (value != '') {
            this.filteringDate(value, field);
        }
    }
    /**custom filering function for past and due date filter */
    filteringDate(value, filterField) {
        if (filterField !== '' && value != '') {
            let dateFrom;
            let dateTo;
            if (this.refillDateType == 'due') {
                this.refillType = true;
                dateFrom = moment().format('YYYY-MM-DD');
                dateTo = moment().add(Number(value - 1), 'd').format('YYYY-MM-DD');
            } else {
                this.refillType = false;
                dateFrom = moment().subtract(value, 'd').format('YYYY-MM-DD');
                dateTo = moment().subtract(1, 'd').format('YYYY-MM-DD');
            }
            let filterModel = this.gridApi.getFilterModel();
            if (this.rowModelType == 'serverSide') {
                filterModel[filterField] = { type: 'inRange', dateFrom: dateFrom, dateTo: dateTo, filterType: 'date' };
                this.gridApi.setFilterModel(filterModel);
            } else {
                const statusFilterComponent = this.gridApi.getFilterInstance(filterField);
                statusFilterComponent.setModel({
                    type: 'inRange',
                    dateFrom: dateFrom,
                    dateTo: dateTo
                });
                this.gridApi.onFilterChanged();
            }
        } else {
            this.selectedDays = '';
            this.refillType = true;
            this.refillDateType = 'due';
            const filterModel = this.gridApi.getFilterModel();
            let objKeys = Object.keys(filterModel);
            if (objKeys.findIndex(x => x == filterField) != -1) {
                delete filterModel[filterField];
                this.gridApi.setFilterModel(filterModel);
            }
        }
    }
    /**Reload after edit data using bulk edit */
    getFormDataBasedRowModel() {
        if (this.rowModelType == 'clientSide') {
            this.getformData();
        } else {
            var datasource = ServerSideDatasource(this);
            this.gridApi.setServerSideDatasource(datasource);
            this.getWidgetCountServerSide();
        }
    }
    /**Get the form entries */
    getformData() {
		if (this.metaData.dataSource && this.metaData.dataSource == 'API') {
			/**Get the date using an API */
			let parameters = [];
			this.metaData.parameters.split('&').forEach((element) => {
				let key = element.substring(element.lastIndexOf('{{') + 2, element.lastIndexOf('}}'));
				if (key.toLowerCase() == 'tenantid') {
					element = element.replace('{{' + key + '}}', this._structureService.getCookie('tenantId'));
				}
				if (key.toLowerCase() == 'currentdate') {
					const userToTenantTimeRes = this._structureService.userToTenantTime();
					const date = moment()
						.add(userToTenantTimeRes[0], 'hours')
						.add(userToTenantTimeRes[1], 'm')
						.format('YYYYMMDD');
					element = element.replace('{{' + key + '}}', date);
				}
				if (key == 'patient_id') {
					let pIndex = this.mapFields.findIndex((x) => x.fieldName == key);
					element = element.replace('{{' + key + '}}', this.mapFields[pIndex].mapField);
				}
				if (key == 'staff_id') {
					let pIndex = this.mapFields.findIndex((x) => x.fieldName == key);
					element = element.replace('{{' + key + '}}', this.mapFields[pIndex].mapField);
				}
				if (key == 'parent_schedule_id') {
					let pIndex = this.mapFields.findIndex((x) => x.fieldName == key);
					element = element.replace('{{' + key + '}}', this.mapFields[pIndex].mapField);
				}
				if (key == 'formID') {
					const formId = this.metaData.associatedForm;
					element = element.replace('{{' + key + '}}', formId);
				}
				if (element == 'type=multiple') {
					this.apiType = 'multiple';
				} else {
					this.apiType = 'single';
					parameters.push(element);
				}
			});
			let graphqlQuery = '';
			let url = this.metaData.endpoint;
			if (this.metaData.graphqlApi) {
				let url = this.metaData.graphqlEndpoint;
				let fieldList = this.metaData.fieldList.split(',');
				let fieldString = '';
				fieldList.forEach(field => {
					if (field.includes('.')) {
						let colArray = field.split('.');
						let endString = '';
						colArray.forEach((element, index) => {
							fieldString = ` ${fieldString} ${element} `;
							if (index !== colArray.length - 1) {
								fieldString = ` ${fieldString} { `;
								endString = ` ${endString} } `;
							}

						});
						fieldString = ` ${fieldString} ${endString}  `;
					} else {
						fieldString = `${fieldString} ${field}`;
					}
				});
				let newQuery = this.metaData.parameters.replace('$fields', fieldString);
				//newQuery = newQuery.replace('$fields', fieldString);
				console.log(newQuery);
				// let variables = {};
                let variables = {
                  
                    widgetField: ["patientPatStat"]
                }
				this._workListService.getWorklistDataUsingGraphQLAPI(url, newQuery, variables).then((data) => {
					this.prepareApiData(data);
				});
			} else {
				this._workListService.getWorklistDataUsingAPI(url, parameters.join('&'), graphqlQuery).then((data) => {
					this.prepareApiData(data);
				});
			}
		} else {
			/**Geting data from machform */
			this.formRowData = [];
			this._workListService
				.getWorkListFormData(this.formId, this.uniqueClass, '', '', this.formFieldFrom)
				.refetch()
				.then(({ data: response }) => {
					if (this.uniqueClass !== '') {
						this.formRowData = response['getFormDataWithUniqueID'];
					} else {
						this.formRowData = response['getFormData'];
					}
					if (this.formRowData) {
						if (this.formRowData.length > 0) {
							this.machFormIds = [];
							this.formDataList = [];
							let i = 1;
							let obj = {};
							this.formRowData.forEach((element) => {
								const formObj = {};
								element.elements.forEach((elem) => {
									formObj[elem.labelText] = elem.value;
									if (elem.valueType == 'radio' && elem.valueOther != '' && elem.value == '') {
										formObj[elem.labelText] = elem.valueOther;
									}
									if (elem.valueType == 'checkbox' && elem.valueOther != '') {
										if (formObj[elem.labelText] == '') {
											formObj[elem.labelText] = elem.valueOther;
										} else {
											formObj[elem.labelText] = formObj[elem.labelText] + ',' + elem.valueOther;
										}
									}
									if (i == 1) {
										obj[elem.labelText] = {
											id: elem.tid,
											elementType: elem.valueType,
											otherExist: elem.OtherExists
										};
									}
									if (elem.valueType == 'date') {
										/**Formating date for correcting filetring */
										if (
											elem.timestampForDate &&
											elem.timestampForDate != null &&
											elem.timestampForDate != ''
										) {
											let newDate = new Date(Number(elem.timestampForDate));
											formObj[elem.labelText] = moment.utc(newDate).format('MM/DD/YYYY');
										} else {
											formObj[elem.labelText] = '';
										}
									}
								});
								formObj['submissionID'] = element.submissionID;
								formObj['slno'] = i;
								formObj['action'] = '';
								formObj['callRecords'] = [{ Therapy: '', MOA: '', Drug: '' }];
								this.formDataList.push(formObj);
								i++;
							});
							this.machFormIds.push({ type: 'parent' });
							this.machFormIds.push(obj);
							this.rowData = this.formDataList;
							this.gridApi.hideOverlay();
							this.setDefaultWidgetConfig(this.rowData);
						} else {
							this.rowData = [];
							this.gridApi.hideOverlay();
							this.setDefaultWidgetConfig(this.rowData);
						}
					} else {
						this.rowData = [];
						this.columnDefs = [];
						this.setDefaultWidgetConfig(this.rowData);
					}
				});
		}
	}
    prepareApiData(data) {
        let rowData: any = data;
        if (this.metaData.graphqlApi) {
            let keyArray = Object.keys(data['data']);
            rowData = data['data'][keyArray[0]].data;
            this.widgetData = data['data'][keyArray[0]].filterCount;
        }
        if (this.metaData.worklistType == 'single') {
            this.rowData = rowData;
        } else {
            rowData.forEach(element => {
                if (element.formData) {
                    let formDataList = [];
                    let objKeys = Object.keys(element.formData);
                    objKeys.forEach(keys => {
                        if (element.formData[keys]) {
                            let formObj = {};
                            let i = 1;
                            let obj = {};
                            element.formData[keys].forEach(elem => {
                                let labelText = '';
                                if (elem.label.lastIndexOf('{{') == -1) {
                                    labelText = elem.label;
                                    if (i == 1) {
                                        obj[elem.label] = { 'id': elem.element_id, 'elementType': elem.element_type };
                                    }
                                } else {
                                    let key = elem.label.substring(elem.label.lastIndexOf('{{') + 2, elem.label.lastIndexOf('}}'));
                                    labelText = key;
                                    if (i == 1) {
                                        obj[key] = { 'id': elem.element_id, 'elementType': elem.element_type };
                                    }
                                }
                                if (elem.element_type == 'date' && elem.value && elem.value != null && elem.value != '') {
                                    let newDate = new Date(Number(elem.value) * 1000);
                                    formObj[labelText] = moment.utc(newDate).format('MM/DD/YYYY');
                                } else {
                                    formObj[labelText] = '';
                                }
                            });
                            this.machFormIds.push({ 'type': 'child' });
                            this.machFormIds.push(obj);
                            i++;
                            formObj['submissionID'] = Number(keys);
                            formDataList.push(formObj);
                        }
                    });
                    element.callRecords = formDataList;
                } else {
                    element.callRecords = element.day_wise_time_range;
                }
            });
            this.rowData = rowData;
        }
        // this.getWidgetCountServerSide();
        this.setDefaultWidgetConfig(this.rowData);
    }
    /**Set the count of filering result in dashboard widget in server side pagination */
    setDefaultWidgetConfigServerSide(fieldDetails, action) {

        let i = 0;
        this.dashboardWidgetField = '';
        this.dashboardWidgetFieldId = '';
        this.dashboardWidgets.forEach(element => {
            if (i == 0) {
                this.dashboardWidgetField = element.formField;
                if (this.machFormIds.length > 0) {
                    if (this.machFormIds[1][this.dashboardWidgetField]) {
                        this.dashboardWidgetFieldId = this.machFormIds[1][this.dashboardWidgetField].id;
                    }
                } else {
                    const index = this.reportFields.findIndex(x => x.fieldName == this.dashboardWidgetField);
                    if (index != -1) {
                        this.dashboardWidgetFieldId = this.reportFields[index].fieldId;
                    }
                }
            }
            i++;
            if (this.widgetCounts[0][element.widgetValue.toLowerCase()]) {
                element.count = this.widgetCounts[0][element.widgetValue.toLowerCase()];
            } else {
                element.count = 0;
            }
        });
        if (action == 'edit' && fieldDetails != '' && this.dashboardWidgetField == fieldDetails.field && fieldDetails.oldValue != fieldDetails.newValue) {
            const oldValueCount = this.widgetCounts[0][fieldDetails.oldValue.toLowerCase()];
            const newValueCount = this.widgetCounts[0][fieldDetails.newValue.toLowerCase()];
            let newIndex = this.dashboardWidgets.findIndex(x => x.widgetValue == fieldDetails.newValue);
            if (newIndex != -1) {
                this.dashboardWidgets[newIndex].count = this.dashboardWidgets[newIndex].count + 1;
            }
            let oldIndex = this.dashboardWidgets.findIndex(x => x.widgetValue == fieldDetails.oldValue);
            if (oldIndex != -1) {
                this.dashboardWidgets[oldIndex].count = this.dashboardWidgets[oldIndex].count - 1;
            }
            this.widgetCounts[0][fieldDetails.oldValue.toLowerCase()] = oldValueCount - 1;
            this.widgetCounts[0][fieldDetails.newValue.toLowerCase()] = newValueCount + 1;
            let index = this.dashboardWidgets.findIndex(x => x.widgetValue == this.selectedWidget);
            if (index != -1) {
                this.widgetFiltering(this.dashboardWidgets[index].widgetValue, this.dashboardWidgets[index].formField, this.dashboardWidgets[index].hideColumns, this.dashboardWidgets[index].count);
            }
        }
        if (action == 'delete') {
            const oldCount = this.widgetCounts[0][fieldDetails.field.toLowerCase()];
            const allCount = this.widgetCounts[0]['all'];
            let newIndex = this.dashboardWidgets.findIndex(x => x.widgetValue == fieldDetails.field);
            if (newIndex != -1) {
                this.dashboardWidgets[newIndex].count = this.dashboardWidgets[newIndex].count - 1;
            }
            let oldIndex = this.dashboardWidgets.findIndex(x => x.widgetValue == 'All');
            if (oldIndex != -1) {
                this.dashboardWidgets[oldIndex].count = this.dashboardWidgets[oldIndex].count - 1;
            }
            this.widgetCounts[0][fieldDetails.field.toLowerCase()] = oldCount - 1;
            this.widgetCounts[0]['all'] = allCount - 1;
        }
        if (localStorage.getItem('selectedWidget')) {
            let index = this.dashboardWidgets.findIndex(x => x.widgetValue == localStorage.getItem('selectedWidget'));
            if (index != -1) {
                this.widgetFiltering(this.dashboardWidgets[index].widgetValue, this.dashboardWidgets[index].formField, this.dashboardWidgets[index].hideColumns, this.dashboardWidgets[index].count);
            }
            localStorage.setItem('selectedWidget', '');
        }
    }
    /**Set the count of filering result in dashboard widget in client side pagination*/
    setDefaultWidgetConfig(rowData) {
        // this.dashboardWidgets.forEach(element => {
        //     this.dashboardWidgetField = element.formField;
        //     element.count = this.getFilterCount(element.widgetValue, element.formField, rowData);
        // });
        this.dashboardWidgets.forEach(element => {
            this.dashboardWidgetField = element.formField;
            if (this.metaData.graphqlApi) {
                element.count = 0;
                let widgetCountData = this.widgetData.filter((item) => item.columnName === element.formField);
                if (widgetCountData.length > 0) { 
                    let countDetails = widgetCountData[0].values.filter((item) => item.value === element.widgetValue);
                    if (countDetails.length > 0) {
                        element.count = countDetails[0].count;
                    }
                }
                //element.count = this.getFilterCount(element.widgetValue, element.formField, rowData);
            } else {
                element.count = this.getFilterCount(element.widgetValue, element.formField, rowData);   
            }            
        });
        if (localStorage.getItem('selectedWidget')) {
            let index = this.dashboardWidgets.findIndex(x => x.widgetValue == localStorage.getItem('selectedWidget'));
            if (index != -1) {
                this.widgetFiltering(this.dashboardWidgets[index].widgetValue, this.dashboardWidgets[index].formField, this.dashboardWidgets[index].hideColumns, this.dashboardWidgets[index].count);
            }
            localStorage.setItem('selectedWidget', '');
        }
        if (this.selectedWidget != 'All' && this.selectedWidget != '' && this.rowModelType == 'clientSide') {
            let index = this.dashboardWidgets.findIndex(x => x.widgetValue == this.selectedWidget);
            if (index != -1) {
                this.widgetFiltering(this.dashboardWidgets[index].widgetValue, this.dashboardWidgets[index].formField, this.dashboardWidgets[index].hideColumns, this.dashboardWidgets[index].count);
            }
        }
    }
    /**Get count of widget  */
    getFilterCount(filterValue, filterField, rowData) {
        if (filterValue === 'All') {
            return rowData.length;
        } else {
            const filteredData = rowData.filter(x => x[filterField] === filterValue);
            return filteredData.length;
        }
    }
    /**Show recent data and history data */
    showAllEntries(type, event) {
        NProgress.start();
        this.uniqueClass = '';
        if (type == 'less') {
            this.showLess = true;
            this.showAll = false;
            this.uniqueClass = this.metaData.uniqueIdClass;
            this.selectedLoadingType = '';
        } else {
            this.selectedLoadingType = 'allRecords';
            this.showAll = true;
            this.showLess = false;
        }
        this.searchFieldText = '';
        this.gridApi.setFilterModel(null);
        this.selectedDays = '';
        if (this.dashboardWidgets.length > 0) {
            this.selectedWidget = this.dashboardWidgets[0].widgetValue;
        }
        if (this.rowModelType == 'serverSide') {
            var datasource = ServerSideDatasource(this);
            this.gridApi.setServerSideDatasource(datasource);
            this.gridApi.paginationGoToFirstPage();
        } else {
            this.getformData();
        }
        NProgress.done();
    }
    /**search function in client side pagination */
    searchFilterData(params) {
        const filterModel = this.gridApi.getFilterModel();
        this.gridApi.setFilterModel(null);
        if (this.selectedWidget != 'All') {
            if (filterModel.hasOwnProperty(this.selectedWidgetField) == true) {
                const statusFilterComponent = this.gridApi.getFilterInstance(this.selectedWidgetField);
                statusFilterComponent.setModel({
                    type: 'equals',
                    filter: filterModel[this.selectedWidgetField].filter
                });
            }
        }
        if (params == '') {
            this.searchFieldText = '';
        }
        this.gridApi.setQuickFilter(params);
    }
    /**search function in server side pagination */
    searchBasedField(searchText) {
        if (this.searchFieldText !== '') {
            localStorage.setItem('selectedSearchFieldText', this.searchFieldText);
        }
        if (this.selectedSearchFields.length > 0) {
            if (this.selectedWidget == 'All') {
                this.gridApi.setFilterModel(null);
            }
            this.customSearch = true;
            var datasource = ServerSideDatasource(this);
            this.gridApi.setServerSideDatasource(datasource);
        }
    }
    clearSearch() {
        localStorage.setItem('selectedSearchFieldText', '');
        var datasource = ServerSideDatasource(this);
        this.gridApi.setServerSideDatasource(datasource);
        this.searchFieldText = '';
    }
    checkboxSelection(field) {
        if (this.selectedSearchFields.findIndex(x => x.fieldId == field.fieldId) == -1) {
            this.selectedSearchFields.push(field);
        } else {
            this.selectedSearchFields = this.selectedSearchFields.filter(x => x.fieldId != field.fieldId);
        }
    }
    editEnrollment(){

    }
    onChangeSearchField(event, field) {

    }
    /**custom filtering function of dashboard widget */
    widgetFiltering(filterValue, filterField, columns, count) {
        this.customSearch = false;
        this.selectedWidget = filterValue;
        localStorage.setItem('selectedWidget', this.selectedWidget);
        this.selectedWidgetField = filterField;
        if (this.previousHideColumns.length > 0) {
            this.previousHideColumns.forEach(element => {
                this.gridColumnApi.setColumnVisible(element, true);
            });
        }
        this.hideColumns = (columns && columns != null) ? columns.split(',') : [];
        if (this.hideColumns.length > 0) {
            this.hideColumns.forEach(element => {
                this.gridColumnApi.setColumnVisible(element, false);
            });
            this.previousHideColumns = this.hideColumns;
        } else {
            this.previousHideColumns = [];
        }
        if (filterValue == 'All') {
            filterValue = '';
        }
        if (filterField !== '') {
            const statusFilterComponent = this.gridApi.getFilterInstance(filterField);
            let filterModel = this.gridApi.getFilterModel();
            if (this.rowModelType == 'serverSide') {
                filterModel[filterField] = { type: 'equals', filter: filterValue, filterType: 'text' };
                this.gridApi.setFilterModel(filterModel);
            } else {
                statusFilterComponent.setModel({
                    type: 'equals',
                    filter: filterValue
                });
                this.gridApi.onFilterChanged();
                if (count == 0) {
                    this.gridApi.showNoRowsOverlay();
                } else {
                    this.gridApi.hideOverlay();
                }
            }
        } else {
            this.gridApi.setFilterModel(null);
        }
        this.gridApi.deselectAll();
    }

    /** inline cell editing and update the corresponding value */
    onCellValueChanged(e) {
        const field = e.colDef.field;
        const configIndex = this.reportFields.findIndex(x => x.fieldName == field);
        const config = this.reportFields[configIndex];
        let newValue = e.newValue;
        if (config.valueType == 'date') {
            const cellDate = new Date(newValue);
            /*date format should be in yyyy-mm-dd for saving data to machform db */
            newValue = moment(cellDate).format('YYYY-MM-DD');
        }
        let params = [];
        let fieldValueList = this.list[e.colDef.field];
        if (fieldValueList) {
            let count: number;
            count = fieldValueList.length - 1;
            if (fieldValueList[Number(count)].type == 'internal') {
                const index = fieldValueList.findIndex(x => x.optionValue == newValue);
                if (index != -1) {
                    newValue = fieldValueList[index].optionId;
                    if (config.valueType == 'checkbox') {
                        params.push({ id: this.machFormIds[1][e.colDef.field].id + '_' + newValue, value: '1' });
                        fieldValueList.forEach(element => {
                            if (element.optionValue && element.optionValue != e.newValue) {
                                params.push({ id: this.machFormIds[1][e.colDef.field].id + '_' + element.optionId, value: '0' });
                            }
                        });
                    }
                    if (this.machFormIds[1][e.colDef.field].otherExist == 1) {
                        params.push({ id: this.machFormIds[1][e.colDef.field].id + '_other', value: '' });
                    }
                }
            }
        }
        let formId;
        if (this.machFormIds[0].type == 'child') {
            formId = this.metaData.associatedForm;
        } else {
            formId = this.formId;
        }
        if (config.valueType != 'checkbox') {
            params.push({ id: this.machFormIds[1][e.colDef.field].id, value: newValue });
        }
        if (e.newValue != e.oldValue) {
            this._workListService.updateSingleFormData(params, formId, e.data.submissionID).subscribe(({ data: response }) => {
                if (this.dashboardWidgetField == e.colDef.field) {
                    if (this.rowModelType == 'clientSide') {
                        this.setDefaultWidgetConfig(this.rowData);
                    } else {
                        let obj = { 'field': e.colDef.field, 'newValue': e.newValue, 'oldValue': e.oldValue };
                        this.setDefaultWidgetConfigServerSide(obj, 'edit');
                    }
                }
                var activityData = {
                    activityName: "Update Single Worklist Field",
                    activityType: "worklist forms",
                    activityDescription: this.userData.displayName + " updated the field " + e.colDef.field + " from " + e.oldValue + " to " + e.newValue + 'of entry id ' + e.data.submissionID + 'of form id' + formId,
                };
                this._structureService.trackActivity(activityData);
                let sendData = {
                    rowData: e.data,
                    updateData: [{ updateField: e.colDef.field, updatedValue: e.newValue }],
                    submissionId: [e.data.submissionID],
                    formId: formId,
                    worklistId: this.worklistid,
                    users: [],
                    action: 'update',
                    clientId: this._structureService.socket.io.engine.id
                };
                this.sendWorklistUpdatePolling(sendData);
                if (this.metaData.showAlert == true) {
                    const name = e.colDef.headerName.toLowerCase();
                    let headerName = name.charAt(0).toUpperCase() + name.slice(1);
                    setTimeout(function () {
                        $.notify({ message: headerName + ' updated successfully.' }, { type: 'success' });
                    }, 1000);
                }
                /**Send notification message */
                if (this.metaData.enableNotification == true) {
                    let notificationConfig = {};
                    let staffId = '';
                    if (this.metaData.notificationLevel == 'workListLevel') {
                        if (this.metaData.notifyRecipientOnValueChange == true) {
                            if (e.data.hasOwnProperty('StaffId') == true) {
                                staffId = e.data.StaffId;
                            } else if (this.metaData.fieldForRecipient != '') {
                                const index = this.reportFields.findIndex(x => x.fieldName == this.metaData.fieldForRecipient);
                                const delimitter = this.reportFields[index].fieldValueDelimiter;
                                const value = e.data[this.metaData.fieldForRecipient];
                                if (delimitter != '') {
                                    if (delimitter.length == 1) {
                                        if (value.includes(delimitter)) {
                                            const originalValue = value.split(delimitter);
                                            staffId = originalValue[1];
                                        }
                                    } else if (delimitter.length == 2) {
                                        if (value.includes(delimitter.charAt(0)) && value.includes(delimitter.charAt(1))) {
                                            let originalValue = value.split(delimitter.charAt(0));
                                            originalValue = originalValue[1].split(delimitter.charAt(1))[0];
                                            staffId = originalValue;
                                        }
                                    }
                                }
                            }
                        }
                        notificationConfig = {
                            notifyingRoles: this.metaData.notifyRolesOnValueChange ? this.metaData.notifyRoles : '',
                            notifyRecipient: staffId,
                            notifyMode: {
                                push: this.metaData.fieldChangePush,
                                pushTemplate: this.metaData.fieldChangePushTemplate,
                                sms: this.metaData.fieldChangeSms,
                                smsTemplate: this.metaData.fieldChangeSmsTemplate,
                                email: this.metaData.fieldChangeEmail,
                                emailTemplate: this.metaData.fieldChangeEmailTemplate
                            },
                            data: e.data
                        }
                        this.sendNotificationMessage(notificationConfig);
                    } else {
                        if (config.enableNotification == true) {
                            if (config.notifyRecipientOnValueChange == true) {
                                if (e.data.hasOwnProperty('StaffId') == true) {
                                    staffId = e.data.StaffId;
                                } else if (this.metaData.fieldForRecipient != '') {
                                    const index = this.reportFields.findIndex(x => x.fieldName == this.metaData.fieldForRecipient);
                                    const delimitter = this.reportFields[index].fieldValueDelimiter;
                                    const value = e.data[this.metaData.fieldForRecipient];
                                    if (delimitter != '') {
                                        if (delimitter.length == 1) {
                                            if (value.includes(delimitter)) {
                                                const originalValue = value.split(delimitter);
                                                staffId = originalValue[1];
                                            }
                                        } else if (delimitter == 2) {
                                            if (value.includes(delimitter.charAt(0)) && value.includes(delimitter.charAt(1))) {
                                                let originalValue = value.split(delimitter.charAt(0));
                                                originalValue = originalValue[1].split(delimitter.charAt(1))[0];
                                                staffId = originalValue;
                                            }
                                        }
                                    }
                                }
                            }
                            notificationConfig = {
                                notifyingRoles: config.notifyRolesOnValueChange == true ? this.metaData.notifyRoles : '',
                                notifyRecipient: staffId,
                                notifyMode: {
                                    push: config.push,
                                    pushTemplate: config.pushTemplate,
                                    sms: config.sms,
                                    smsTemplate: config.smsTemplate,
                                    email: config.email,
                                    emailTemplate: config.emailTemplate
                                },
                                data: e.data
                            };
                            if (config.notifySpecificValue == true) {
                                if (e.newValue.toLowerCase() == config.notificationFieldValue.toLowerCase()) {
                                    this.sendNotificationMessage(notificationConfig);
                                }
                            } else {
                                this.sendNotificationMessage(notificationConfig);
                            }
                        }
                    }
                }
            });
        }
    }
    /**for retaining sorting of entries using submissionID */
    sortChanged(event) {
        if (this.gridApi.getSortModel().length == 0 && this.rowModelType == 'clientSide') {
            this.gridApi.setSortModel([{ colId: 'submissionID', sort: 'desc' }]);
        }
    }
    onColumnResized(event) {
        if (event.finished) {
            this.gridApi.resetRowHeights();
        }
    }
    /**For make the ag grid responsive */
    onGridSizeChanged(params) {
        var gridWidth = document.getElementById("grid-wrapper").offsetWidth;
        var columnsToShow = [];
        var columnsToHide = [];
        var totalColsWidth = 0;
        var allColumns = params.columnApi.getAllColumns();
        for (var i = 0; i < allColumns.length; i++) {
            let column = allColumns[i];
            totalColsWidth += column.getMinWidth();
            if (totalColsWidth > gridWidth) {
                columnsToHide.push(column.colId);
            } else {
                columnsToShow.push(column.colId);
            }
        }
        params.api.sizeColumnsToFit();
    }
    /**Grid ready function */
    onGridReady(params) {
        this.gridApi = params.api;
        this.gridColumnApi = params.columnApi;
        /**Show our own loading message */
        this.gridApi.showLoadingOverlay();
        setTimeout(function () {
            params.api.resetRowHeights();
        }, 500);
        window.addEventListener('resize', function () {
            setTimeout(function () {
                params.api.resetRowHeights();
            });
        });
        this.reportFields.forEach(element => {
            if (element.clearFilter == true) {
                let filterInstance = this.gridApi.getFilterInstance(element.fieldName);
                if (filterInstance) {
                    filterInstance.eClearButton.addEventListener("click", () => {
                        this.gridApi.onFilterChanged();
                    });
                }
            }
        });
        this.gridApi.setHeaderHeight(35);
        this.gridApi.setColumnDefs(this.columnDefs);
        /**Check the type of loading data and call function accordingly. */
        if (this.rowModelType == 'serverSide') {
            var datasource = ServerSideDatasource(this);
            this.gridApi.setServerSideDatasource(datasource);
        } else {
            this.getformData();
        }
        this.onGridSizeChanged(params);
    }
    /**Check whether show action buttons and bulk edit based on the selected row count */
    onSelectionChanged(event) {
        let type;
        if (event.columnApi.columnController.columnDefs == this.columnDefs) {
            type = 'parent';
        } else {
            type = 'child';
        }
        this.rowCount = event.api.getSelectedNodes().length;
        this.selectedRowDetails = event.api.getSelectedRows();
        if (this.rowCount == 0) {
            this.hideActionButtons = true;
        } else {
            let privileges = Object.keys(this._structureService.privileges);
            let userRole = this._structureService.getCookie('userRole');
            let rowData = event.api.getSelectedNodes()[0].data;
            let actionFields;
            let indices = [];
            this.singleRowActions.forEach((element, i) => {
                /**Check privilege for showing action button */
                if (element.enableIntegration == true) {
                    this.enableIntegrationforAction = true;
                }
                if (
                    (
                        element.actionPrivileges != '' &&
                        privileges.indexOf(element.actionPrivileges) != -1
                    ) ||
                    element.actionPrivileges == '' ||
                    typeof (element.actionPrivileges) == 'undefined'
                ) {
                    if (element.actionField && element.actionField != '') {
                        actionFields.push({ 'associatedField': element.actionField, 'fieldValues': element.fieldValues });
                    } else if (element.actionFields) {
                        actionFields = element.actionFields;
                    }
                    if (actionFields.length > 0) {
                        let count = 0;
                        let loginUserId = this.userData.userId;
                        let loginDisplayName = this.userData.displayName;
                        actionFields.forEach(field => {
                            if (rowData.hasOwnProperty(field.associatedField)) {
                                if (field.fieldValues && field.fieldValues.split(',').findIndex(x => x == rowData[field.associatedField]) != -1) {
                                    if (element.showOnlyLoginUser == true) {
                                        if (rowData[element.loginUserMatchField] == loginUserId || rowData[element.loginUserMatchField] == loginDisplayName) {
                                            count++;
                                        }
                                    } else {
                                        count++;
                                    }
                                    // indices.push(i);
                                }
                            }
                            if (field.fieldValues && field.fieldValues == 'Any') {
                                if (rowData[field.associatedField] == '') {
                                    indices.push(i);
                                }
                                if (rowData[field.associatedField]) {
                                    // indices.push(i);
                                    count++;
                                }
                            }
                            if (field.fieldValues && field.fieldValues == 'not_null') {
                                if (rowData[field.associatedField] != '') {
                                    indices.push(i);
                                }
                            }
                        });
                        
                    }
                }
                if (element.selectionMode != type) {
                    element.disable = true;
                } else if (this.rowCount > 1 && element.actionMode == 'single row') {
                    element.disable = true;
                } else {
                    if (rowData.hasOwnProperty(element.actionField)) {
                        if (element.fieldValues && element.fieldValues != '') {
                            if (element.fieldValues.split(',').findIndex(x => x == rowData[element.actionField]) == -1) {
                                element.disable = true;
                            } else {
                                element.disable = false;
                            }
                        } else {
                            element.disable = false;
                        }
                    } else {
                        element.disable = false;
                    }
                }
            });
            if (indices.length) {
                indices.forEach(i => {
                    this.singleRowActions[i].disable = true;
                });
            }

            if (this.btnBehaviour == false) {
                this.hideActionButtons = false;
                if (Number(this.rowCount) > 1) {
                    this.hideBulkEdit = false;
                } else {
                    this.hideBulkEdit = true;
                }
            } else {
                this.hideActionButtons = true;
            }
        }
    }
    /**Single button action */
    singleBtnAction(action) {
        let selectedData = this.selectedRowDetails;
        let callbck = action.callbackfunction;
        let link = action.actionLink;
        if (selectedData.length > 0) {
            this.hideActionButtons = false;
            let e = { rowData: selectedData[0], actionField: action.actionField, actionMode: action.actionMode, selectionMode: action.selectionMode, mapField: action.mapField, actionConfig: action };
            if (link == '' || link == null) {
                if (callbck === 'linkPAH') {
                    this.linkPAH(e);
                }
                
            } else {
                this.goToActionLink(link);
            }
        } else {
            this.hideActionButtons = true;
        }
    }
    /**Short link action */
    goToActionLink(actionLink) {
        this.router.navigate([`${actionLink}`]);
    }
    /**Go to Pah page */
    goToPahPage(e) {
        this.router.navigate(['pah', e.rowData.patient_id]);
    }
    /**Edit action  */
    linkPAH(e) {
        const editRowDetails = e.rowData;
        localStorage.setItem('selectedWidget', this.selectedWidget);
        localStorage.setItem('selectedSearchFieldText', this.searchFieldText);
        this.router.navigate([`pah/${editRowDetails['id']}`]);
    }
    /**New order - currently not used  */
    newOrder(e) {
        const editRowDetails = e.rowData;
        this.router.navigate([`worklist/${this.worklistName}/details/${this.worklistid}/${editRowDetails['submissionID']}/new`]);
    }
    /**Visit plan */
    visitPlan(e) {
        this.showDateError = false;
        this.visitDates = [];
        this.editRowDetails = e.rowData;
        $('#date-modal').modal('show');
        let highlights = [];
        let markDates = [];
        const today = new Date();
        let schedules = [];
        if (this.apiType == 'multiple') {
            schedules = this.editRowDetails.day_wise_time_range;
        } else {
            schedules = this.editRowDetails.callRecords;
        }
        schedules.forEach(element => {
            const d = new Date(Number(element.date) * 1000);
            const date1 = moment.utc(d).format('MM/DD/YYYY');
            if (this.editRowDetails.callRecords) {
                const index = this.editRowDetails.callRecords.findIndex(x => x['FU Visit Date'] == date1);
                if (index > -1) {
                    markDates.push({ dates: [{ year: d.getFullYear(), month: (d.getMonth() + 1), day: d.getDate() }], color: 'red' });
                    this.visitDates.push({ 'submissionId': this.editRowDetails.callRecords[index].submissionID, 'visitDate': moment.utc(d).format('MM/DD/YYYY') });
                }
            }
            highlights.push({ year: d.getFullYear(), month: (d.getMonth() + 1), day: d.getDate() });
        });
        this.myDatePickerOptions = {
            todayBtnTxt: 'Today',
            firstDayOfWeek: 'mo',
            sunHighlight: false,
            highlightDates: highlights,
            markDates: markDates,
            disableUntil: this.convertDate(this.editRowDetails.start_date, '-', 1),
            disableSince: this.convertDate(this.editRowDetails.end_date, '+', 1)
        };
    }
    goToVisitPlanPage() {
        this.mapFields.forEach(element => {
            element.value = this.editRowDetails[element.fieldName];
        });
        localStorage.setItem('scheduleDetails', JSON.stringify(this.mapFields));
        if (!this.dateModel.epoc) {
            this.showDateError = true;
        } else if (this.dateModel.epoc != null || this.dateModel.epoc || this.dateModel.epoc != '') {
            $('#date-modal').modal('hide');
            const visitIndex = this.visitDates.findIndex(x => x.visitDate == this.dateModel.formatted);
            if (visitIndex != -1) {
                this.router.navigate([`worklist/${this.worklistName}/details/${this.worklistid}/${this.visitDates[visitIndex].submissionId}`]);
            } else {
                this.router.navigate([`worklist/${this.worklistName}/details/${this.worklistid}/${this.editRowDetails.parent_schedule_id}/visit/${this.dateModel.epoc}`]);
            }
        }
    }
    clearDate(): void {
        // Clear the date using the patchValue function
        this.selectedDate = '';
    }
    convertDate(d, mode, count) {
        let date1 = new Date(Number(d) * 1000);
        if (mode == '+') {
            return { year: date1.getFullYear(), month: (date1.getMonth() + 1), day: date1.getDate() + count };
        } else {
            return { year: date1.getFullYear(), month: (date1.getMonth() + 1), day: date1.getDate() - count };
        }

    }
    /**Therapy functions */
    therapyList(e) {
        let formId = this.metaData.associatedForm;
        this.editorFields = this.reportFields.filter(x => x.showInEditor == true);
        this.therapyDetails = [];
        let parentDetails = e.rowData;
        $('#therapy-modal').modal('show');
        this.loadMsg = true;
        this._workListService.getChildFormData(this.metaData.associatedForm, this.metaData.childMapField, parentDetails[this.metaData.parentMapField]).refetch().then(({ data: response }) => {
            let formDataList = [];
            let formData = response['getAllFormDataMultipleForm'];
            let i = 1;
            formData.forEach(element => {
                const formObj = {};
                element.elements.forEach(elem => {
                    formObj[elem.labelText] = elem.value;
                });
                formObj['submissionID'] = element.submissionID;
                formDataList.push(formObj);
                i++;
            });
            this.therapyDetails = formDataList;
            this.loadMsg = false;
            this.showField = false;
            this.createControls(parentDetails[this.metaData.parentMapField]);
        });
    }
    createControls(patientId) {
        this.editorFields = this.reportFields.filter(x => x.showInEditor == true);
        this.editorDynamicFields = [];
        this.editorFields.forEach(element => {
            let formElementId;
            formElementId = element.fieldId;
            if (element.fieldName == 'Therapy') {
                this.editorDynamicFields.push({
                    'name': element.fieldName,
                    'elementType': 'select',
                    'options': ['ABT', 'ANE', 'ASIV', 'ASO', 'ASP', 'ATNF', 'BLD', 'CAR', 'CHL'],
                    'visibility': element.visibility,
                    'fieldId': element.fieldId,
                    'value': ''
                });
            }
            if (element.fieldName == 'MOA') {
                this.editorDynamicFields.push({
                    'name': element.fieldName,
                    'elementType': 'select',
                    'options': ['MOA1', 'MOA2', 'MOA3'],
                    'visibility': element.visibility,
                    'fieldId': element.fieldId,
                    'value': ''
                });
            }
            if (element.fieldName == 'Drug') {
                this.editorDynamicFields.push({
                    'name': element.fieldName,
                    'elementType': 'textbox', 'options': [],
                    'visibility': element.visibility,
                    'fieldId': element.fieldId,
                    'value': ''
                });
            }
            if (element.fieldName == 'PatientId') {
                this.editorDynamicFields.push({
                    'name': element.fieldName,
                    'elementType': 'textbox', 'options': [],
                    'visibility': element.visibility,
                    'fieldId': element.fieldId,
                    'value': patientId
                });
            }
            this.therapyForm = this.createNewControl(this.editorDynamicFields);
        });
    }
    createNewControl(fields) {
        let group: any = {};
        if (fields) {
            fields.forEach(fields => {
                group[fields.name] = new FormControl('', Validators.required);
            });
        }
        return new FormGroup(group);
    }
    addTherapy(f) {
        if (f.valid) {
            let obj = {};
            let params = [];
            this.editorDynamicFields.forEach(element => {
                obj[element.name] = f.value[element.name];
                params.push({ id: element.fieldId, value: f.value[element.name] });
            });
            this.therapyCount++;
            const index = this.therapyDetails.findIndex(x => x.id == this.editTherapyId);
            if (index != -1) {
                this._workListService.updateSingleFormData(params, this.metaData.associatedForm, this.therapyDetails[index].id).subscribe(({ data: response }) => {
                    obj['id'] = this.editTherapyId;
                    this.therapyDetails[index] = obj;
                    this.showField = false;
                    this.editTherapyId = '';
                    var activityData = {
                        activityName: "Update data in a worklist",
                        activityType: "worklist forms",
                        activityDescription: this.userData.displayName + " updated value of the field of id " + this.editTherapyId + " in the form of id " + this.metaData.associatedForm + "in the entry of " + this.therapyDetails[index].id,
                    };
                    this._structureService.trackActivity(activityData);
                });
            } else {
                this._workListService.addFormEntry(this.metaData.associatedForm, params).subscribe(({ data: response }) => {
                    obj['id'] = response['insertFormData'][0].submissionID;
                    this.therapyDetails.push(obj);
                    this.showField = false;
                    var activityData = {
                        activityName: "Insert data to a worklist",
                        activityType: "worklist forms",
                        activityDescription: this.userData.displayName + " inserted data to the form of id " + this.metaData.associatedForm,
                    };
                    this._structureService.trackActivity(activityData);
                });
            }
            this.therapyForm.reset();
            this.therapyForm = this.createNewControl(this.editorDynamicFields);
            this.buttonLabel = 'Add';
        } else {
            alert('Please fill all the field');
        }
    }
    cancelTherapy() {
        this.therapyForm.reset();
        this.therapyForm = this.createNewControl(this.editorDynamicFields);
    }
    editTherapy(i) {
        this.buttonLabel = 'Update';
        this.showField = true;
        const index = this.therapyDetails.findIndex(x => x.id == i);
        this.editTherapyId = i;
        this.editorDynamicFields.forEach(element => {
            const name = element.name;
            this.therapyForm.controls[name].setValue(this.therapyDetails[index][element.name]);
        });
    }
    deleteTherapy(i) {
        swal({
            title: 'Are you sure?',
            text: 'You are deleting this entry',
            type: 'warning',
            showCancelButton: true,
            cancelButtonClass: 'btn-default',
            confirmButtonClass: 'btn-warning',
            confirmButtonText: 'Ok',
            closeOnConfirm: true
        }, () => {
            this._workListService.deleteWorklistEntry(this.metaData.associatedForm, i).subscribe(({ data: response }) => {
                this.therapyDetails = this.therapyDetails.filter(x => x.id != i);
                var activityData = {
                    activityName: "Delete data in a worklist",
                    activityType: "worklist forms",
                    activityDescription: this.userData.displayName + " deleted the entry of id " + i + "in a form of id " + this.metaData.associatedForm,
                };
                this._structureService.trackActivity(activityData);
            });
        });
    }
    
    onCellClicked(params) {
        if (params.colDef['cellEvent'] === 'showDetail') {
            // let e = { rowData: params.data, actionField: "", actionMode: "", selectionMode: "", link: "" };
            if (params.value != "") {
                this.showDetailedView(params);
            }
        }
    }
    showDetailedView(params) {
        $('.my-tooltip').css('top', params.event.pageY - 200);
        this.showTooltip = true;
        $('.my-tooltip').show();
        this.showResponse = false;
        var getData = {
            "reference_id": params.value
        }
        this._structureService.getIntegration(getData).then((res) => {
            if (res && res['response'].integration_status) {
                this.responseStatus = res['response'];
                this.showResponse = true;
            } else {
                $('.my-tooltip').hide();
            }
        });
    }
    closeDetailpop() {
        $('.my-tooltip').hide();
    }
    
    enableOrDisableUiLI(condition, clear: boolean = true) {
        if (condition) {
            $("ul.associate-ul").css('display', 'block');
            $("input#associate-search-input").addClass("active");
        } else {
            $("ul.associate-ul").css('display', 'none');
            $("input#associate-search-input").removeClass("active");
        }
        if (clear) {
            $("input#associate-search-input").val('');
        }
    }
    openAssociateList() {
        this.onClickEvent = false;
        if (!this.associatePatientLoading) {
            if (this.assosiatedPatients.length > 0) {
                this.enableOrDisableUiLI(true, false);
            }
        }
    }
    checkAssociatePatientWithTems() {
        let searchTerm = $("#associate-search-input").val();
        if (searchTerm != "") {
            this.getAssociatePatients(searchTerm);
        } else {
            this.enableOrDisableUiLI(false, false);
        }
    }
    getAssociatePatients(search: string = "") {
        $("#associate-search").text(" ").text("Loading...");
        this.associatePatientLoading = true;
        this._structureService.getAssociatedPatientsLists('', search).then((result: any) => {

            // this.assosiatedPatients = result;
            this.associatePatientLoading = false;
            this.assosiatedPatients = result.filter((result) => {
                //if(result.caregiver_userid == null && result.role != 'Caregiver') {
                var date = "";
                //if(result.caregiver_userid ) {
                if (result.caregiver_dob) {
                    date = result.caregiver_dob;
                    var dobDay = new Date(date).getDay();
                    if (date && !isNaN(dobDay)) {
                        date = date.replace(/-/g, '/');
                        try {
                            // date = this._datePipe.transform(date, 'MM/dd/yyyy');
                            date = moment(date).format('MM/DD/YYYY');
                        }
                        catch (e) {
                            date = '';
                        }
                    } else {
                        date = "";
                    }
                } else {
                    date = "";
                }
                result.caregiver_dob_formatted = date;
                // } else {
                date = "";
                if (result.dob) {
                    date = result.dob;
                    var dobDay = new Date(date).getDay();
                    if (date && !isNaN(dobDay)) {
                        date = date.replace(/-/g, '/');
                        try {
                            // date = this._datePipe.transform(date, 'MM/dd/yyyy');
                            date = moment(date).format('MM/DD/YYYY');
                        }
                        catch (e) {
                            date = '';
                        }
                    } else {
                        date = "";
                    }
                } else {
                    date = "";
                }
                result.dob_formatted = date;
                var listDisplayName = (result.caregiver_displayname) ? ((result.dob_formatted) ? (result.displayname + ' - ' + result.dob_formatted + ' (' + result.caregiver_displayname + ')') : (result.displayname + ' (' + result.caregiver_displayname + ')')) : ((result.dob_formatted) ? (result.displayname + ' - ' + result.dob_formatted) : result.displayname);
                listDisplayName = listDisplayName + (result.passwordStatus == 'true' ? " (Registered)" : "");
                result.listDisplayName = listDisplayName;
                // }
                return true;
                /* } else {
                  return false;
                } */
            });
            $("#associate-search").text("").text("Search");
            //$("input#associate-search-input").attr("placeholder", "Select Associated Patient or Search");
            if (search != "") {
                this.enableOrDisableUiLI(true, false);
                if (this.selectedAssosiatePatientName.includes(search) == false) {
                    this.selectedAssosiatePatientName = '';
                }
            }
            $("#select2-assosiatedPatients-container .select2-selection__placeholder").html("").html("Select Associated Patient");
            $("#assosiatedPatients").attr("disabled", false);

            // this.associatePatientSelect2();
            // this.tenantUsers = result;
        }).catch((ex) => { });
    }
   

    clearSearchField() {
        let searchTerm = $("#associate-search-input").val();
        if (searchTerm == '') {
            this.closeSelectedAssociatePatient(true);
        }
    }
    closeSelectedAssociatePatient(contentReset) {
        if (this.selectedAssosiatePatient) {
            this.enableOrDisableUiLI(false, true);
            this.selectedAssosiatePatient = '';
            this.selectedAssosiatePatientName = '';
            this.selectedPatientName = '';
        } else if ($('input#associate-search-input').val() != '') {
            $('input#associate-search-input').val('');
            $('#associate-search').text(' ').text('Search');
            this.associatePatientLoading = false;
            this.enableOrDisableUiLI(false, true);
        } else if ($('input#associate-search-input').val() == '') {
            this.enableOrDisableUiLI(false, false);
        }
        if (contentReset == true) {
            this.formContent = '';
        }
        this.disableBtn = true;
        this.assosiatedPatients = [];
    }
    
    /**Multiple batch action functionality */
    multipleButtonAction(e) {
        if (e.action == 'linkPAH') {
            this.linkPAH(e);
        }
    }
    
    /**get widget count using api in server side pagination */
    getWidgetCountServerSide() {
        this._workListService.getWidgetCount(this.formId, this.dashboardWidgetFieldId, this.uniqueClass).refetch().then(({ data: response }) => {
            this.widgetCounts = [];
            let totalcount = 0;
            let obj = {};
            response['getTotalBasedOnStatus'].forEach(element => {
                if (element.option && element.option != null) {
                    let option = element.option.toLowerCase();
                    obj[option] = element.total;
                }
                totalcount = totalcount + element.total;
            });
            obj['all'] = totalcount;
            this.widgetCounts.push(obj);
            this.setDefaultWidgetConfigServerSide('', '');
        });
    }
    /**Send notification from actions in worklist */
    sendNotification(actionConfig, rowData) {
        let staffId = '';
        if (actionConfig.notifyRecipientOnAction == true) {
            if (rowData.hasOwnProperty('StaffId') == true) {
                staffId = rowData.StaffId;
            } else if (this.metaData.fieldForRecipient != '') {
                const index = this.reportFields.findIndex(x => x.fieldName == this.metaData.fieldForRecipient);
                const delimitter = this.reportFields[index].fieldValueDelimiter;
                const value = rowData[this.metaData.fieldForRecipient];
                if (delimitter != '') {
                    if (delimitter.length == 1) {
                        if (value.includes(delimitter)) {
                            const originalValue = value.split(delimitter);
                            staffId = originalValue[1];
                        }
                    } else if (delimitter == 2) {
                        if (value.includes(delimitter.charAt(0)) && value.includes(delimitter.charAt(1))) {
                            let originalValue = value.split(delimitter.charAt(0));
                            originalValue = originalValue[1].split(delimitter.charAt(1))[0];
                            staffId = originalValue;
                        }
                    }
                }
            }
        }
        let notificationConfig = {
            notifyingRoles: actionConfig.notifyRolesOnAction == true ? this.metaData.notifyRoles : '',
            notifyRecipient: staffId,
            notifyMode: {
                push: actionConfig.push,
                pushTemplate: actionConfig.pushTemplate,
                sms: actionConfig.sms,
                smsTemplate: actionConfig.smsTemplate,
                email: actionConfig.email,
                emailTemplate: actionConfig.emailTemplate
            },
            data: rowData
        };
        this.sendNotificationMessage(notificationConfig);
    }
    /**Format the content and select which method used for notification */
    sendNotificationMessage(notificationConfig) {
        if (notificationConfig.notifyMode.push == true) {
            const pIndex = this.allMessageTemplate.findIndex(x => x.id == notificationConfig.notifyMode.pushTemplate);
            if (pIndex != -1) {
                const content = this.formatContent(this.allMessageTemplate[pIndex].content, notificationConfig.data);

                this.sendPushNotification(content, notificationConfig);
            }
        }
        const notificationContent = [];
        if (notificationConfig.notifyMode.email == true || notificationConfig.notifyMode.sms == true) {
            if (notificationConfig.notifyMode.email == true) {
                const eIndex = this.allMessageTemplate.findIndex(x => x.id == notificationConfig.notifyMode.emailTemplate);
                if (eIndex != -1) {
                    let content = this.formatContent(this.allMessageTemplate[eIndex].content, notificationConfig.data);
                    notificationContent.push({ 'mode': 'email', 'content': content, 'subject': this.allMessageTemplate[eIndex].subject, 'title': this.allMessageTemplate[eIndex].title });
                }
            }
            if (notificationConfig.notifyMode.sms == true) {
                const sIndex = this.allMessageTemplate.findIndex(x => x.id == notificationConfig.notifyMode.smsTemplate);
                if (sIndex != -1) {
                    let content = this.formatContent(this.allMessageTemplate[sIndex].content, notificationConfig.data);
                    notificationContent.push({ 'mode': 'sms', 'content': content });
                }
            }
            this.sendEmailOrSmsNotification(notificationContent, notificationConfig);
        }
    }
    /**Format content by replacing Field/date/form name/worklist */
    formatContent(content, data) {
        let formName = this.formName;
        let worklistName = this.heading;
        const zone = timezone.name();
        let newContent = content.replace(/\[(.*?)\]/g, function (m) {
            if (data.hasOwnProperty(m.slice(1, -1))) {
                return data[m.slice(1, -1)];
            } else if (m == '[Date]') {
                const newDate = momentTz.tz(new Date(), zone).format('MM/DD/YYYY');
                return newDate;
            } else if (m == '[FormName]') {
                return formName;
            } else if (m == '[Worklist]') {
                return worklistName;
            } else if (m == '[DateTime]') {
                const newDate = momentTz.tz(new Date(), zone).format('MM/DD/YYYY h:mm:ss A');
                return newDate;
            } else {
                return m;
            }
        });
        return newContent;
    }
    /**Configure and send push notification */
    sendPushNotification(content, notificationConfig) {
        var deepLinking = {
            'pushType': '',
            'state': 'eventmenu.worklist',
            'stateParams': {},
            "tenantId": this.userData.tenantId,
            "tenantName": this.userData.tenantName
        };
        let pushMessage = content;
        let status = 0;
        let selectedRecipientsPolling = [];
        if (notificationConfig.notifyingRoles != '') {
            let notifyArray = notificationConfig.notifyingRoles.split(',');
            let i = 1;
            notifyArray.forEach(elem => {
                this._workListService.getRoleBasedStaffs(elem, status, 1).then((data) => {
                    let staffList = JSON.parse(JSON.stringify(data['getSessionTenant'].roleBasedStaffs));
                    staffList.forEach(element => {
                        if (this.userData.userId != element.id && element.id != notificationConfig.notifyRecipient) {
                            const staff = {
                                username: element.emails[0].value,
                                displayname: element.displayName,
                                userid: element.id,
                                roleId: elem,
                                mobile: element.mobile,
                                countryCode: element.countryCode,
                                senderId: this.userData.userId
                            };
                            selectedRecipientsPolling.push(staff);
                        }
                    });
                    if (i == notifyArray.length) {
                        if (notificationConfig.notifyRecipient != '') {
                            this._structureService.getUser(notificationConfig.notifyRecipient).then((data) => {
                                const staff = data['staffUsers'][0];
                                const staffDetails = {
                                    username: staff.emails[0].value,
                                    displayname: staff.displayName,
                                    userid: staff.id,
                                    roleId: staff.role.id,
                                    mobile: staff.mobile,
                                    countryCode: staff.countryCode,
                                    senderId: this.userData.userId
                                };
                                selectedRecipientsPolling.push(staffDetails);
                                this._structureService.sentPushNotification(selectedRecipientsPolling, 0, pushMessage, '', deepLinking, '', '');
                            });
                        } else {
                            this._structureService.sentPushNotification(selectedRecipientsPolling, 0, pushMessage, '', deepLinking, '', '');
                        }
                    }
                    i++;
                });
            });
        } else {
            if (notificationConfig.notifyRecipient != '') {
                this._structureService.getUser(notificationConfig.notifyRecipient).then((data) => {
                    const staff = data['staffUsers'][0];
                    const staffDetails = {
                        username: staff.emails[0].value,
                        displayname: staff.displayName,
                        userid: staff.id,
                        roleId: staff.role.id,
                        mobile: staff.mobile,
                        countryCode: staff.countryCode,
                        senderId: this.userData.userId
                    };
                    selectedRecipientsPolling.push(staffDetails);
                    this._structureService.sentPushNotification(selectedRecipientsPolling, 0, pushMessage, '', deepLinking, '', '');
                });
            }
        }
    }
    /**Configure and send email/sms notification */
    sendEmailOrSmsNotification(content, notificationConfig) {
        let notificationData = {
            notifyUsers: [],
            notifyMode: content,
            tenantId: this._structureService.getCookie('tenantId'),
            senderName: this.userData.displayName,
        };
        if (notificationConfig.notifyingRoles != '') {
            let notifyArray = notificationConfig.notifyingRoles.split(',');
            const status = 0;
            let i = 1;
            notifyArray.forEach(elem => {
                this._workListService.getRoleBasedStaffs(elem, status, 1).then((data) => {
                    let staffList = JSON.parse(JSON.stringify(data['getSessionTenant'].roleBasedStaffs));
                    staffList.forEach(element => {
                        if (this.userData.userId != element.id && element.id != notificationConfig.notifyRecipient) {
                            const staff = {
                                firstname: element.firstName,
                                lastname: element.lastName,
                                displayname: element.displayName,
                                username: element.emails[0].value,
                                countryCode: element.countryCode,
                                mobile: element.mobile,
                            };
                            notificationData.notifyUsers.push(staff);
                        }
                    });
                    if (i == notifyArray.length) {
                        if (notificationConfig.notifyRecipient != '') {
                            this._structureService.getUser(notificationConfig.notifyRecipient).then((data) => {
                                const staff = data['staffUsers'][0];
                                const staffDetails = {
                                    firstname: staff.firstName,
                                    lastname: staff.lastName,
                                    displayname: staff.displayName,
                                    username: staff.emails[0].value,
                                    countryCode: staff.countryCode,
                                    mobile: staff.mobile
                                };
                                notificationData.notifyUsers.push(staffDetails);
                                this._structureService.socket.emit("sendWorklistNotification", notificationData);
                            });
                        } else {
                            this._structureService.socket.emit("sendWorklistNotification", notificationData);
                        }
                    }
                    i++;
                });
            });
        } else {
            if (notificationConfig.notifyRecipient != '') {
                this._structureService.getUser(notificationConfig.notifyRecipient).then((data) => {
                    const staff = data['staffUsers'][0];
                    const staffDetails = {
                        firstname: staff.firstName,
                        lastname: staff.lastName,
                        displayname: staff.displayName,
                        username: staff.emails[0].value,
                        countryCode: staff.countryCode,
                        mobile: staff.mobile
                    };
                    notificationData.notifyUsers.push(staffDetails);
                    this._structureService.socket.emit('sendWorklistNotification', notificationData);
                });
            }
        }
    }
    /**Polling for any update in worklist */
    sendWorklistUpdatePolling(sendData) {
        if (this.metaData.visibleToRoles.split(',').length > 1) {
            const count = this.metaData.visibleToRoles.split(',').length;
            let i = 1;
            this.metaData.visibleToRoles.split(',').forEach(element => {
                this._workListService.getRoleBasedStaffs(element, 0, 1).then((data) => {
                    const staffList = JSON.parse(JSON.stringify(data['getSessionTenant'].roleBasedStaffs));
                    staffList.forEach(element => {
                        sendData['users'].push(element.id);
                    });
                    if (i == count) {
                        this._structureService.socket.emit('worklistDataUpdate', sendData);
                    }
                    i++;
                });
            });
        } else {
            this._workListService.getRoleBasedStaffs(this.metaData.visibleToRoles, 0, 1).then((data) => {
                const staffList = JSON.parse(JSON.stringify(data['getSessionTenant'].roleBasedStaffs));
                staffList.forEach(element => {
                    sendData['users'].push(element.id);
                });
                this._structureService.socket.emit('worklistDataUpdate', sendData);
            });
        }
    }
}

/**Implement lazy loading functionality */
function ServerSideDatasource(this1) {
    return {
        getRows(params) {
	
            let start = params.request.startRow;
            let end = params.request.endRow;
            let filterModel = params.request.filterModel;
            let finalFilterModel = {};
            if (this1.searchFieldText != '' && this1.selectedSearchFields.length > 0) {
				let filterFields = this1.reportFields.filter(x => x.allowFilter == true && x.valueType != 'checkbox');
				this1.selectedSearchFields.forEach(element => {
					if (element.valueType == 'number') {
						finalFilterModel[element.fieldName] = { type: 'equals', filter: this1.searchFieldText, filterType: 'number', filterCondition: 'OR' };
					} else if (element.valueType == 'date') {
						finalFilterModel[element.fieldName] = { type: 'equals', dateFrom: this1.searchFieldText, filterType: 'date', filterCondition: 'OR' };
					} else if (element.valueType == 'radio' || element.valueType == 'checkbox' || element.valueType == 'select') {
						finalFilterModel[element.fieldName] = { type: 'contains', filter: this1.searchFieldText, filterType: 'text', filterCondition: 'OR' };
					} else {
						finalFilterModel[element.fieldName] = { type: 'contains', filter: this1.searchFieldText, filterType: 'text', filterCondition: 'OR' };
					}
				});
            }
            if (Object.keys(filterModel).length > 0) {
				let filterModelKeys = Object.keys(filterModel);
				let widgetFilterKeys = filterModelKeys.filter(x => x == this1.selectedWidgetField);
				let otherModelKeys = filterModelKeys.filter(x => x != this1.selectedWidgetField);
				if (otherModelKeys.length > 0) {
					this1.searchFieldText = '';
					/* this1.filterEnabledFields.forEach(element => {
						$('#checkbox' + element.fieldId).prop("checked", false);
					}); */
					this1.selectedSearchFields = [];
					finalFilterModel = filterModel;
				}
				if (widgetFilterKeys.length > 0) {
					finalFilterModel[this1.selectedWidgetField] = filterModel[this1.selectedWidgetField];
				}
            }
            if (this1.metaData.dataSource == 'API') {
                let parameters = [];
                this1.metaData.parameters.split('&').forEach(element => {
                    let key = element.substring(
                        element.lastIndexOf('{{') + 2,
                        element.lastIndexOf('}}')
                    );

                    if (key.toLowerCase() == 'tenantid') {
                        element = element.replace('{{' + key + '}}', this1._structureService.getCookie('tenantId'));
                    }
                    if (key.toLowerCase() == 'currentdate') {
                        const userToTenantTimeRes = this1._workListService.userToTenantTime();
                        const date = moment().add(userToTenantTimeRes[0], 'hours').add(userToTenantTimeRes[1], 'm').format('YYYYMMDD');
                        element = element.replace('{{' + key + '}}', date);
                    }
                    if (key == 'patient_id') {
                        let pIndex = this1.mapFields.findIndex(x => x.fieldName == key);
                        element = element.replace('{{' + key + '}}', this1.mapFields[pIndex].mapField);
                    }
                    if (key == 'staff_id') {
                        let pIndex = this1.mapFields.findIndex(x => x.fieldName == key);
                        element = element.replace('{{' + key + '}}', this1.mapFields[pIndex].mapField);
                    }
                    if (key == 'parent_schedule_id') {
                        let pIndex = this1.mapFields.findIndex(x => x.fieldName == key);
                        element = element.replace('{{' + key + '}}', this1.mapFields[pIndex].mapField);
                    }
                    if (key == 'formID') {
                        const formId = this1.metaData.associatedForm;
                        element = element.replace('{{' + key + '}}', formId);
                    }
                    if (element == 'type=multiple') {
                        this1.apiType = 'multiple';
                    } else {
                        this1.apiType = 'single';
                        parameters.push(element);
                    }
                });
                if (this1.metaData.graphqlApi) {
                    let url = this1.metaData.graphqlEndpoint;                     
                    let fieldString = '';
                    this1.columnDefs.forEach(col => {
                        if (col.field.includes('.')) {
                            let colArray = col.field.split('.');   
                            let endString = '';
                            colArray.forEach((element,index) => {
                                fieldString = ` ${fieldString} ${element} `;
                                if (index !== colArray.length - 1) {
                                    fieldString = ` ${fieldString} { `;
                                    endString = ` ${endString} } `; 
                                } 
                               
                            });  
                            fieldString = ` ${fieldString} ${endString}  `;                           
                        } else {
                            fieldString = `${fieldString} ${col.field}`;
                        }
                    });
                    
                    let newQuery = this1.metaData.parameters.replace('$fields', fieldString);
                    let filterModelArray = [];
                    Object.keys(finalFilterModel).forEach((filter) => {
						//	console.log(filter);
						//console.log(finalFilterModel[filter].type)
						let filterObject = {
							column: filter,
							//	type: finalFilterModel[filter].filterType,
							filter: finalFilterModel[filter].filter,
							filterTo: finalFilterModel[filter].filterTo,
							type: finalFilterModel[filter].type
						}
						filterModelArray.push(filterObject);
					});
                    let variables = {
                        startRow:start,
                        endRow:end,
                        filter:filterModelArray,
                        sorting: params.request.sortModel,
                        rowGroups: params.request.rowGroupCols, 
                        groupKeys: params.request.groupKeys,
                        widgetField: ["patientPatStat"]
                    }
                    this1._workListService.getWorklistDataUsingGraphQLAPI(url, newQuery, variables).then((data) => {
                        let rowData: any = data;
                        let keyArray = Object.keys(data['data']);
                        rowData = data['data'][keyArray[0]].data;
                        this1.widgetData = data['data'][keyArray[0]].filterCount;
                        if(rowData.length == 0) {
							this1.rowData = [];
                            this1.gridApi.showNoRowsOverlay();
				      
						} else {
                            if (this1.metaData.worklistType == 'single') {
                                this1.rowData = rowData;
                            } else {
                                rowData.forEach(element => {
                                    if (element.formData && element.formData.length > 0) {
                                        this1.machFormIds = [];
                                        let formDataList = [];
                                        let objKeys = Object.keys(element.formData);
                                        objKeys.forEach(keys => {
                                            if (element.formData[keys]) {
                                                let formObj = {};
                                                let i = 1;
                                                let obj = {};
                                                element.formData[keys].forEach(elem => {
                                                    let labelText = '';
                                                    if (elem.label.lastIndexOf('{{') == -1) {
                                                        labelText = elem.label;
                                                        if (i == 1) {
                                                            obj[elem.label] = { 'id': elem.element_id, 'type': elem.element_type };
                                                        }
                                                    } else {
                                                        let key = elem.label.substring(elem.label.lastIndexOf('{{') + 2, elem.label.lastIndexOf('}}'));
                                                        labelText = key;
                                                        if (i == 1) {
                                                            obj[key] = { 'id': elem.element_id, 'type': elem.element_type };
                                                        }
                                                    }
                                                    if (elem.element_type == 'date' && elem.value && elem.value != null && elem.value != '') {
                                                        let newDate = new Date(Number(elem.value) * 1000);
                                                        formObj[labelText] = moment.utc(newDate).format('MM/DD/YYYY');
                                                    } else {
                                                        formObj[labelText] = elem.value;
                                                    }
                                                });
                                                this1.machFormIds.push({ 'type': 'child' });
                                                this1.machFormIds.push(obj);
                                                i++;
                                                formObj['submissionID'] = Number(keys);
                                                formDataList.push(formObj);
                                            }
                                        });
                                        element.callRecords = formDataList;
                                    } else {
                                        element.callRecords = element.day_wise_time_range;
                                    }
                                });
                                this1.rowData = rowData;
                            }
                        }
                        this1.setDefaultWidgetConfig(this1.rowData);
                        var server = {
                            success: true,
                            rows: this1.rowData,
                        }
                        let res = server;
                        if (res.success) {
                            const rows = res.rows;
                            let lastRow = -1;
                            if (res.rows.length < this1.cacheBlockSize) {
                                lastRow = params.request.startRow + res.rows.length;
                            }
                            params.successCallback(rows, lastRow);
                        } else {
                            params.failCallback();
                        }
                    });
                } else {
                    this1._workListService.getWorklistDataUsingAPI(this1.metaData.endpoint, parameters.join('&')).then((data) => {
                        let rowData: any = data;
                        if (this1.metaData.worklistType == 'single') {
                            this1.rowData = data;
                        } else {
                            rowData.forEach(element => {
                                if (element.formData && element.formData.length > 0) {
                                    this1.machFormIds = [];
                                    let formDataList = [];
                                    let objKeys = Object.keys(element.formData);
                                    objKeys.forEach(keys => {
                                        if (element.formData[keys]) {
                                            let formObj = {};
                                            let i = 1;
                                            let obj = {};
                                            element.formData[keys].forEach(elem => {
                                                let labelText = '';
                                                if (elem.label.lastIndexOf('{{') == -1) {
                                                    labelText = elem.label;
                                                    if (i == 1) {
                                                        obj[elem.label] = { 'id': elem.element_id, 'type': elem.element_type };
                                                    }
                                                } else {
                                                    let key = elem.label.substring(elem.label.lastIndexOf('{{') + 2, elem.label.lastIndexOf('}}'));
                                                    labelText = key;
                                                    if (i == 1) {
                                                        obj[key] = { 'id': elem.element_id, 'type': elem.element_type };
                                                    }
                                                }
                                                if (elem.element_type == 'date' && elem.value && elem.value != null && elem.value != '') {
                                                    let newDate = new Date(Number(elem.value) * 1000);
                                                    formObj[labelText] = moment.utc(newDate).format('MM/DD/YYYY');
                                                } else {
                                                    formObj[labelText] = elem.value;
                                                }
                                            });
                                            this1.machFormIds.push({ 'type': 'child' });
                                            this1.machFormIds.push(obj);
                                            i++;
                                            formObj['submissionID'] = Number(keys);
                                            formDataList.push(formObj);
                                        }
                                    });
                                    element.callRecords = formDataList;
                                } else {
                                    element.callRecords = element.day_wise_time_range;
                                }
                            });
                            this1.rowData = rowData;
                            //this1.deactivateRouting = false;
                            //this1.firstLoading = true;
                            //this1.notificationMsg.hide('All');
                        }
                        this1.setDefaultWidgetConfig(this1.rowData);
                        var server = {
                            success: true,
                            rows: this1.rowData,
                        }
                        let res = server;
                        if (res.success) {
                            const rows = res.rows;
                            let lastRow = -1;
                            if (res.rows.length < this1.cacheBlockSize) {
                                lastRow = params.request.startRow + res.rows.length;
                            }
                            params.successCallback(rows, lastRow);
                        } else {
                            params.failCallback();
                        }
                    }).catch((ex) => {
                        //this1.deactivateRouting = false;
                        //this1.firstLoading = true;
                        //this1.notificationMsg.hide('All');
                    });
                }
            } else {
                const filterModel = params.request.filterModel;
                const sortModel = params.request.sortModel;
                let formattedSortModel = [];
                if (Object.keys(sortModel).length > 0) {
                    sortModel.forEach(element => {
                        let sortObj = { 'colId': this1.machFormIds[1][element.colId].id, 'sort': element.sort };
                        if (this1.machFormIds[1][element.colId].elementType == 'simple_name') {
                            sortObj.colId = this1.machFormIds[1][element.colId].id + '_1';
                        }
                        formattedSortModel.push(sortObj);
                    });
                } else {
                    let sortObj = { 'colId': 'id', 'sort': 'desc' };
                    formattedSortModel.push(sortObj);
                }
                let formattedFilterModel = [];
                if (this1.customSearch == false) {
                    this1.searchFieldText = '';
                    this1.filterEnabledFields.forEach(element => {
                        $('#checkbox' + element.fieldId).prop("checked", false);
                    });
                    this1.selectedSearchFields = [];
                }
                if (this1.searchFieldText != '' && this1.selectedSearchFields.length > 0) {
                    let filterFields = this1.reportFields.filter(x => x.allowFilter == true && x.valueType != 'checkbox');
                    this1.selectedSearchFields.forEach(element => {
                        let filterObj = {
                            'fieldName': element.fieldName,
                            'fieldId': element.fieldId,
                            'valueType': element.valueType,
                            'filterCondition': 'OR'
                        };
                        if (element.valueType == 'number') {
                            filterObj['filterDetail'] = { type: 'equals', filter: this1.searchFieldText, filterType: 'number' };
                        } else if (element.valueType == 'date') {
                            filterObj['filterDetail'] = { type: 'equals', dateFrom: this1.searchFieldText, filterType: 'date' };
                        } else if (element.valueType == 'radio' || element.valueType == 'checkbox' || element.valueType == 'select') {
                            filterObj['filterDetail'] = { type: 'contains', filter: this1.searchFieldText, filterType: 'text' };
                        } else {
                            filterObj['filterDetail'] = { type: 'contains', filter: this1.searchFieldText, filterType: 'text' };
                        }
                        formattedFilterModel.push(filterObj);
                    });
                }
                if (Object.keys(filterModel).length > 0) {
                    let filterModelKeys = Object.keys(filterModel);
                    let widgetFilterKeys = filterModelKeys.filter(x => x == this1.selectedWidgetField);
                    let otherModelKeys = filterModelKeys.filter(x => x != this1.selectedWidgetField);
                    if (otherModelKeys.length > 0) {
                        this1.searchFieldText = '';
                        this1.filterEnabledFields.forEach(element => {
                            $('#checkbox' + element.fieldId).prop("checked", false);
                        });
                        this1.selectedSearchFields = [];
                        formattedFilterModel = [];
                        filterModelKeys = filterModelKeys;
                    } else if (widgetFilterKeys.length > 0) {
                        filterModelKeys = widgetFilterKeys;
                    }
                    filterModelKeys.forEach(element => {
                        if (filterModel[element].filter) {
                            filterModel[element].filter = filterModel[element].filter.trim();
                        }
                        let filterObj = {
                            'fieldName': element, 'filterDetail': filterModel[element]
                        };
                        /**while editing any entry from filtered data it should return to the same resultant page after completing editing. that time we take field id form report fields. */
                        if (this1.machFormIds.length > 0) {
                            filterObj['fieldId'] = this1.machFormIds[1][element].id;
                            filterObj['valueType'] = this1.machFormIds[1][element].elementType;
                        } else {
                            const index = this1.reportFields.findIndex(x => x.fieldName == element);
                            filterObj['fieldId'] = this1.reportFields[index].fieldId;
                            filterObj['valueType'] = this1.reportFields[index].valueType;
                        }
                        formattedFilterModel.push(filterObj);
                    });
                }
                /**Count of elements used for filtering having element type radio/checkbox/select */
                let count = formattedFilterModel.filter(x => x.valueType == 'radio' || x.valueType == 'checkbox' || x.valueType == 'select').length;
                let j = 1;
                if (count > 0) {
                    formattedFilterModel.forEach(element => {
                        if (element.valueType == 'radio' || element.valueType == 'checkbox' || element.valueType == 'select') {
                            this1._workListService.getMachformFields(this1.formId, element.fieldId).refetch().then(({ data: response }) => {
                                const options = response['getFormElementDetails'].options;
                                let filterOptions = [];
                                options.forEach(elem => {
                                    const optionValue = elem.optionValue.toLowerCase();
                                    const filterValue = element.filterDetail.filter.toLowerCase();
                                    element.otherExist = response['getFormElementDetails'].hasOtherOption;
                                    if (response['getFormElementDetails'].hasOtherOption == 1) {
                                        element.valueOther = filterValue;
                                    }
                                    if (element.valueType == 'radio' || element.valueType == 'select' || element.valueType == 'checkbox') {
                                        if (element.filterDetail.type == 'contains') {
                                            if (optionValue.includes(filterValue)) {
                                                filterOptions.push(elem.optionId);
                                            }
                                        }
                                        if (element.filterDetail.type == 'equals') {
                                            if (optionValue == filterValue) {
                                                filterOptions.push(elem.optionId);
                                            }
                                        }
                                    }
                                });
                                if (filterOptions.length == 0 && response['getFormElementDetails'].hasOtherOption == 0) {
                                    filterOptions.push(new Date().getTime());
                                }
                                let filterdetail = element.filterDetail;
                                element.filterDetail = { type: filterdetail.type, filter: filterOptions.join(','), filterType: filterdetail.filterType };
                                if (j == count) {
                                    getDataSource(params, start, this1, formattedFilterModel, formattedSortModel);
                                }
                                j++;
                            });
                        }
                    });
                } else {
                    getDataSource(params, start, this1, formattedFilterModel, formattedSortModel);
                }
                // } else {
                //   getDataSource(params, start, this1, formattedFilterModel, formattedSortModel);
                // }
            }
        }
    };
}
function getDataSource(params, start, this1, formattedFilterModel, sortModel) {
    if (start == 0 && formattedFilterModel.length == 0 && sortModel[0].colId == 'id') {
        let formFieldArray = [];
        this1.dashboardWidgets.forEach(element => {
            if (element.formField != 'all') {
                formFieldArray = this1.reportFields.filter(x => x.fieldName == element.formField);
            }
        });
        if (formFieldArray.length > 0) {
            this1._workListService.getWidgetCount(this1.formId, formFieldArray[0].fieldId, this1.uniqueClass).refetch().then(({ data: response }) => {
                this1.widgetCounts = [];
                let totalcount = 0;
                let obj = {};
                response['getTotalBasedOnStatus'].forEach(element => {
                    if (element.option && element.option != null) {
                        let option = element.option.toLowerCase();
                        obj[option] = element.total;
                    }
                    totalcount = totalcount + element.total;
                });
                obj['all'] = totalcount;
                this1.widgetCounts.push(obj);
                this1.setDefaultWidgetConfigServerSide('', '');
            });
        }
    }
    this1._workListService.getWorkListFormDataWithFilter(this1.formId, this1.uniqueClass, start, this1.cacheBlockSize, this1.formFieldFrom, formattedFilterModel, sortModel).refetch().then(({ data: response }) => {
        if (this1.uniqueClass !== '') {
            this1.formRowData = response['getFormDataWithUniqueIDWithFilters'];
        } else {
            this1.formRowData = response['getFormDataWithFilters'];
        }
        let totalCount = 0;
        if (this1.formRowData && this1.formRowData.length > 0) {
            this1.machFormIds = [];
            this1.formDataList = [];
            let i = 1;
            let obj = {};
            this1.formRowData.forEach(element => {
                const formObj = {};
                totalCount = element.total;
                element.elements.forEach(elem => {
                    formObj[elem.labelText] = elem.value;
                    if (elem.valueType == 'radio' && elem.valueOther != '' && elem.value == '') {
                        formObj[elem.labelText] = elem.valueOther;
                    }
                    if (elem.valueType == 'checkbox' && elem.valueOther != '') {
                        if (formObj[elem.labelText] == '') {
                            formObj[elem.labelText] = elem.valueOther;
                        } else {
                            formObj[elem.labelText] = formObj[elem.labelText] + ',' + elem.valueOther;
                        }
                    }
                    if (i == 1) {
                        obj[elem.labelText] = { 'id': elem.tid, 'elementType': elem.valueType, 'otherExist': elem.OtherExists };
                    }
                    if (elem.valueType == 'date') {
                        if (elem.timestampForDate && elem.timestampForDate != null && elem.timestampForDate != '') {
                            let newDate = new Date(Number(elem.timestampForDate));
                            formObj[elem.labelText] = moment.utc(newDate).format('MM/DD/YYYY');
                        } else {
                            formObj[elem.labelText] = '';
                        }
                    }
                });
                formObj['submissionID'] = element.submissionID;
                formObj['slno'] = i;
                formObj['action'] = '';
                this1.formDataList.push(formObj);
                i++;
            });
            this1.machFormIds.push({ 'type': 'parent' });
            this1.machFormIds.push(obj);
            this1.rowData = this1.formDataList;
            this1.gridApi.hideOverlay();
        } else {
            this1.rowData = [];
            this1.gridApi.showNoRowsOverlay();
            $('.ag-row-stub').html('<p style="margin-top:7px!important;text-align: center!important;">There are no data available.</p>');
        }

        var server = {
            success: true,
            rows: this1.rowData,
        }
        let res = server;
        let lastRowValue = params.request.startRow + res.rows.length;
        if (res.success) {
            const rows = res.rows;
            let lastRow = -1;
            if (res.rows.length < this1.cacheBlockSize || (totalCount != 0 && lastRowValue == totalCount)) {
                lastRow = params.request.startRow + res.rows.length;
            }
            params.successCallback(rows, lastRow);
        } else {
            params.failCallback();
        }
    });
}


function flattenObject(ob) {
    var toReturn = {};

    for (var i in ob) {
        if (!ob.hasOwnProperty(i)) continue;

        if ((typeof ob[i]) == 'object' && ob[i] !== null) {
            var flatObject = flattenObject(ob[i]);
            for (var x in flatObject) {
                if (!flatObject.hasOwnProperty(x)) continue;

                toReturn[i + '.' + x] = flatObject[x];
            }
        } else {
            toReturn[i] = ob[i];
        }
    }
    return toReturn;
}




