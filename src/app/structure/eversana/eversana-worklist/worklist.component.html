<style type="text/css">
    .cat__core__step__desc p {
        font-weight: bold !important;
    }

    .cat__core__step__digit {
        cursor: pointer;
    }

    .signature-req-block:hover {
        background: #e6e6e6;
    }

    .status-active {
        background-color: #e6e6e6;
    }

    .refill-dues {
        margin-bottom: 0 !important;
        margin-top: 8px;
    }

    .history-records {
        margin-top: 4px;
    }

    .batch-action-search {
        margin-top: 1px;
    }
    .new-modal {
        width: 390px;
        float: right;
        text-align: center;
        top: 175px;
        position: absolute;
        left: 110%;
        border: 1px solid #ccc;
    }
    .detail-modal-close {
        right: 0;
        position: absolute;
        padding: 5px;
        font-size: 1rem;
        z-index: 99;
    }
    .new-modal table {
        width: 100%;
    }
    .int-loader img {
        width: 50px;
        padding: 10px;
        margin: 0;
    }
    .my-tooltip {
        position: absolute;
        background: #f7f7f7;
        border: 1px solid #ccc;
        right: 22px;
        min-width: 200px;
        border-radius: 6px;
    }
    .my-tooltip:after, .my-tooltip:before {
        bottom: 100%;
        left: 70%;
        border: solid transparent;
        content: " ";
        height: 0;
        width: 0;
        position: absolute;
        pointer-events: none;
    }

    .my-tooltip:after {
        border-color: rgba(136, 183, 213, 0);
        border-bottom-color: #f7f7f7;
        border-width: 13px;
        margin-left: -13px;
    }
    .my-tooltip:before {
        border-color: rgba(194, 225, 245, 0);
        border-bottom-color: #ccc;
        border-width: 14px;
        margin-left: -14px;
    }
    .my-tooltip .content-block {
        padding: 15px 20px 15px 20px;
    }
    .list-data{
        padding:0 !important;
    }
    .worklist-section {
        margin-bottom:0px !important;
    }
</style>
<!-- START: tables/datatables -->
<ejs-toast #element showCloseButton=true width=400 timeOut=0 [position]='position'>
    <ng-template #content>
        <div><i class="fa fa-exclamation-triangle"></i> This worklist has been updated. <a (click)="refreshGrid()"
                style="color:blue;text-decoration: underline;">Refresh</a></div>
    </ng-template>
</ejs-toast>
<!-- <ejs-toast #notificationMsg showCloseButton=true width=400 timeOut=0 [position]='position'>
    <ng-template #title>
            <div><i class="fa fa-exclamation-triangle"></i> Information</div>
    </ng-template>
    <ng-template #content>
        <div>Please wait..</div>
    </ng-template>
</ejs-toast> -->

<section class="card" *ngIf="dataLoadingMsg">
    <div class="card-block mb-2 mt-2">
        <div class="wait-loading">
            <img src="assets/img/loader/loading.gif" />
        </div>
    </div>
</section>
<section class="worklist-section" *ngIf="!dataLoadingMsg">  
    <div class="card-block">      
        <div class="row widgets-lists">
            <div class="{{widget.class}}  worklist-req-block"
                [class.widget-selected]="selectedWidget == widget.widgetValue" *ngFor="let widget of dashboardWidgets">
                <a href="javascript:void(0)"
                    (click)="widgetFiltering(widget.widgetValue, widget.formField, widget.hideColumns, widget.count)">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="cat__core__step cat__core no-border signed-block"
                                [class.widget-selected]="selectedWidget == widget.widgetValue" background-color:
                                #ececec;>
                                <span class="cat__core__step__digit">
                                    <i class="{{widget.displayIcon}}"
                                        [ngStyle]="{'color': widget.iconColor}"></i></span>
                                <div class="cat__core__step__desc">
                                    <span class="cat__core__step__title">{{widget.count}} <i
                                            class="fa fa-refresh fa-spin" [hidden]="widget.count !== null"
                                            style="font-size: 16px;"></i> </span>
                                    <p style="width: 100%;float: left;text-align: center;">{{widget.displayText}}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12 list-data">
                <div class="">
                    <div class="row mb-2 mt-3">
                        <div class="col-sm-12 col-md-5">
                            <div class="due-action" *ngIf="dynamicFilter == true">
                                <span><input type="radio" class="mr-2 refill-dues" name="refill" value="due"
                                        (click)="setRefillValue('due',selectedDays,dynamicFilterField)"
                                        [(ngModel)]="refillDateType" [checked]="refillType">{{dynamicFilterLabel}}
                                    Due</span>
                                <span><input type="radio" class="mr-2 ml-2 refill-dues" name="refill" value="past"
                                        (click)="setRefillValue('past',selectedDays,dynamicFilterField)"
                                        [(ngModel)]="refillDateType" [checked]="!refillType"> Past
                                    {{dynamicFilterLabel}} Due</span>
                                <span><select class="form-control refill-due ml-2 mr-2" [(ngModel)]="selectedDays"
                                        (change)="filteringDate(selectedDays,dynamicFilterField)">
                                        <option value="" hidden>Select</option>
                                        <option value="7">7 days</option>
                                        <option value="14">14 days</option>
                                    </select></span>
                                <span>
                                    <button class="btn btn-default reset-btn"
                                        (click)="filteringDate('',dynamicFilterField)">
                                        <!-- <i class="fa fa-close" ></i> -->
                                        Reset
                                    </button>
                                </span>
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-7">
                            <!-- <label *ngIf="formId == 79842" class="mr-2 refill-dues" style="float:left"><strong>Refills Due in next</strong></label> -->
                            <!-- <div id="btn-action-patient" class="btn-group pull-right mb-2" [hidden]="!showCustomExport">
                                <button aria-expanded="false" [disabled]="disableWidget"
                                    class="btn btn-sm btn-default dropdown-toggle action-btn-user-settings-patient"
                                    data-toggle="dropdown" type="button"><i class="fa fa-file-excel-o"></i>
                                    Export</button>
                                <ul class="dropdown-menu">
                                    <a class="dropdown-item " href="javascript: void(0);" (click)="exportCSV()">Export
                                        CSV</a>
                                    <a class="dropdown-item" href="javascript: void(0);"
                                        (click)="exportExcel('xlsx')">Export Excel(.xlsx)</a>
                                    <a class="dropdown-item" href="javascript: void(0);"
                                        (click)="exportExcel('xml')">Export Excel(.xml)</a>
                                </ul>
                            </div> -->


                            <!-- <input type="button" class="btn btn-sm btn-primary" value="Refills Due Next 7 Days" (click)="filteringDate(7,'Refill Date')"/> -->
                            <div *ngIf="hasUniqueId" class="pull-right mr-3 history-records">
                                <label class="mr-3"><strong>Show</strong></label>
                                <input type="radio" class="mr-2" name="list" value="listall"
                                    (click)="showAllEntries('all',$event)" [checked]="showAll">History Records
                                <input type="radio" class="mr-2 ml-2" name="list" value="listall"
                                    (click)="showAllEntries('less',$event)" [checked]="showLess">Most Recent
                            </div>
                        </div>

                    </div>
                    <div class="row " style="height: 35px;">
                        <div class="col-md-6">
                            <div style="line-height: 3rem;"
                                [hidden]="hideActionButtons == true || singleRowActions.length === 0">
                                <label class="mr-2">Actions:</label>
                                <span *ngFor="let action of singleRowActions">
                                    <a class="mr-3 {{action.cssClass}}" title="{{action.toolTip}}"
                                        [ngClass]="{'disabled':action.disable}" *ngIf="action.type == 'single'"
                                        style="color:#0190fe; cursor: pointer;" (click)="singleBtnAction(action)">
                                        <i class="{{action.iconClass}} mr-1"></i>{{action.label}}</a>
                                    <!-- <select class="action form-control" data-placeholder="Actions" (change)="onClick($event,element.label)">
                                        <option value="" hidden>{{action.label}}</option>
                                        <option *ngFor="let item of action.itemElements" value="{{item.action}}">{{item.label}}</option>
                                    </select> -->
                                </span>
                                <a class="mr-3" style="color:#0190fe; cursor: pointer;" (click)="bulkEditAction()"
                                    [hidden]="bulkEditFields.length == 0"> <i class="fa fa-pencil mr-1"></i>Bulk Edit
                                </a>
                                <label class="mr-4 ml-2" *ngIf="shortLinkActions.length > 0">|</label>
                                <span *ngFor="let action of shortLinkActions">
                                    <a class="mr-3 {{action.cssClass}}" [ngClass]="{'disabled':action.disable}"
                                        *ngIf="action.type == 'single'" style="color:#0190fe; cursor: pointer;"
                                        (click)="singleBtnAction(action)">
                                        <i class="{{action.iconClass}} mr-1"></i>{{action.label}}</a>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6" *ngIf="metaData.enableQuickSearch == true">
                            <div class="pull-right batch-action-search" *ngIf="rowModelType == 'clientSide'">
                                <label>Search:</label>
                                <label>
                                    <input type="text" #search class="input-sm form-control" placeholder="Search.."
                                        (keyup.enter)="searchFilterData(search.value)" [(ngModel)]="searchFieldText"/>
                                </label>
                                <label>
                                    <button class="btn btn-primary btn-sm reset-btn" [disabled]="searchFieldText == ''"
                                        (click)="searchFilterData(search.value)">
                                        Search
                                    </button>
                                    <button class="btn btn-default btn-sm reset-btn" (click)="searchFilterData('')">
                                        Reset
                                    </button>
                                </label>
                            </div>
                            <div class="pull-right"
                                *ngIf="rowModelType == 'serverSide' && filterEnabledFields.length > 0">
                                <label>Search:</label>
                                <label class="button-group custom-search">
                                    <button type="button" class="btn btn-default-outline btn-sm dropdown-toggle"
                                        style="border-top-right-radius:0px;border-bottom-right-radius:0px;"
                                        data-toggle="dropdown"><span class="fa fa-search"></span> <span
                                            class="caret"></span>
                                    </button>
                                    <ul class="dropdown-menu filter-worklist">
                                        <li class="" *ngFor="let field of filterEnabledFields;">
                                            <span (change)="checkboxSelection(field)">
                                                <label>
                                                    <input class="form-check-input" type="checkbox" id="checkbox{{field.fieldId}}"
                                                        (change)="onChangeSearchField($event, field)"  value="{{field.fieldId}}" [checked]="field.enableAutoSelect">&nbsp;{{field.headerName}}
                                                </label>
                                            </span>
                                        </li>
                                    </ul>
                                </label>
                                <label>
                                    <input type="text"
                                        style="padding-top: .33rem;padding-bottom: .47rem;margin-left: -5px;border-bottom-left-radius: 0px;border-top-left-radius: 0px;border-color:#d8d8d8;"
                                        [(ngModel)]="searchFieldText" class="input-sm form-control" placeholder="Search"
                                        (keyup.enter)="searchBasedField(searchFieldText)" />
                                </label>
                                <label>
                                    <button class="btn btn-primary btn-sm reset-btn" [disabled]="searchFieldText == '' || selectedSearchFields.length == 0"
                                        (click)="searchBasedField(searchFieldText)">
                                        Search
                                    </button>
                                    <button class="btn btn-default btn-sm reset-btn" (click)="clearSearch()">
                                        Reset
                                    </button>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row" id="grid-wrapper">
                        <ag-grid-angular style="width: 100%; min-height: 500px;" class="ag-theme-balham"
                            [rowData]="rowData" [animateRows]="true" [columnDefs]="columnDefs"
                            [getRowHeight]="getRowHeight" [defaultColDef]="defaultColDef" [enableFilter]="true"
                            [enableSorting]="true" [rowStyle]="rowBorder" [frameworkComponents]="frameworkComponents"
                            (gridReady)="onGridReady($event)" [enableColResize]="true"
                            [overlayLoadingTemplate]="overlayLoadingTemplate"
                            [overlayNoRowsTemplate]="overlayNoRowsTemplate" (columnResized)="onColumnResized($event)"
                            (sortChanged)="sortChanged($event)" [icons]="icons" [enableRangeSelection]="true"
                            [rowSelection]='rowSelection' [autoGroupColumnDef]="autoGroupColumnDef"
                            (cellValueChanged)="onCellValueChanged($event)"
                            [groupRowInnerRenderer]="groupRowInnerRenderer" [components]="components"
                            [sideBar]="sideBar" [groupSelectsChildren]="groupSelectsChildren"
                            [suppressRowClickSelection]="true" [rowGroupPanelShow]="rowGroupPanelShow"
                            [detailCellRendererParams]="detailCellRendererParams" [masterDetail]="true"
                            (gridSizeChanged)="onGridSizeChanged($event)" (cellClicked)="onCellClicked($event)"
                            (selectionChanged)="onSelectionChanged($event)" [rowModelType]="rowModelType"
                            [cacheBlockSize]="cacheBlockSize" [maxBlocksInCache]="maxBlocksInCache"
                            [paginationPageSize]="paginationPageSize" [pagination]="pagination"
                            [isRowMaster]="isRowMaster" [suppressCsvExport]="suppressCsvExport"
                            [suppressExcelExport]="suppressExcelExport" [excelStyles]="excelStyles" [animateRows]="animateRows"
                            [enableCellChangeFlash]="cellChangeFlash">
                        </ag-grid-angular>
                        <div class="my-tooltip" style="display:none;">
                            <div class="button-block">
                                <button type="button" class="close detail-modal-close" (click)="closeDetailpop()" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="content-block">
                                <table style="text-align: center;" class="loader-table" *ngIf=!showResponse width="100%">
                                    <tbody>
                                        <tr>
                                            <td>
                                                <div class="int-loader">
                                                    <img src="./assets/modules/dummy-assets/common/img/spinner.svg" alt="loading">
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <table style="text-align: left;" *ngIf='showResponse' class="response-table">
                                    <tbody>
                                        <tr *ngIf='responseStatus?.integration_status'>
                                            <td>Status: </td>
                                            <td>{{responseStatus?.integration_status}}</td>       
                                        </tr>
                                        <tr *ngIf='responseStatus?.reference_id'>
                                            <td>Reference Id: </td>
                                            <td *ngIf='responseStatus'>{{responseStatus?.reference_id}}</td>           
                                        </tr>
                                        <tr *ngIf='responseStatus?.processedAt'>
                                            <td>Processed At: </td>
                                            <td *ngIf='responseStatus'>{{responseStatus?.processedAt}}</td>              
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div [innerHTML]="structureFormContent"></div>
                </div>
            </div>
        </div>
     
    </div>
</section>

<!-- END: tables/datatables -->
