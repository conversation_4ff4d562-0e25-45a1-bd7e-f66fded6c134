<style type="text/css">
    .cat__core__step__desc p {
        font-weight: bold !important;
    }

    .cat__core__step__digit {
        cursor: pointer;
    }

    .signature-req-block:hover {
        background: #e6e6e6;
    }

    .status-active {
        background-color: #e6e6e6;
    }

    .refill-dues {
        margin-bottom: 0 !important;
        margin-top: 8px;
    }

    .history-records {
        margin-top: 4px;
    }

    .batch-action-search {
        margin-top: 1px;
    }
    .worklist-content {
        width:100%
    }
    .worklist-tab {
        padding: 0px ! important;
    }
</style>
<!-- START: tables/datatables -->
<nav class="cat__core__top-sidebar cat__core__top-sidebar--bg">
    <div class="row">
        <!-- <h2 style="padding-left: 14px;">
            <span class="text-black">
                <strong>HCP Dashboard</strong>
                <a id="enroll-patient-btn" class="pull-right btn btn-sm btn-primary">Invite User<i class="ml-1"></i></a>
            </span>

        </h2> -->
        <p class="mb-1"></p>
    </div>
</nav>
<section class="card" *ngIf="dataLoadingMsg">
    <div class="card-block mb-2 mt-2">
        <div class="wait-loading">
            <img src="assets/img/loader/loading.gif" />
        </div>
    </div>
</section>
<section class="card" *ngIf="!dataLoadingMsg">
    <div class="card-header">
        <span class="cat__core__title">
            <strong id="invite-user-label">HCP Dashboard</strong>
           
        </span>
    </div>
    <div class="card-block">

        <div>
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
                <li class="breadcrumb-item">HCP Dashboard</li>
                <li class="info-widget">
                    <span class="chatwith-modal-tip" style="position:absolute !important;">  
                            <img  src="assets/modules/dummy-assets/common/img/chatwith-tip.png">                            
                            <span  class="modal-footer-text-sign">{{helpMsg}}</span>
                    </span>
                </li>
            </ol>
            <div style="float:left;">

            </div>
        </div>
        
        <section class="">
             <!-- <div class="main-tabs">
                <div class="chatwith-modal-tab pah-tab">                    -->
                    <!-- <div *ngFor="let metaData of metaArray" class="chatwith-model-head-pah" id="metaData.id" (click)="showTabData(metaData.id)"
                        [class.cat__apps__messaging__tab_pah--selected]="optionShow==metaData.id">
                        <i class="{{metaData.tabIcon}}" aria-hidden="true"></i>
                        {{metaData.tabName}}</div> -->
                <!-- </div>
            </div> -->

             <ng-container *ngFor="let metaDat of metaArray" class="col-md-12">
                    <div class="row widgets-lists" *ngIf="optionShow==metaDat.id">
                         <app-eversana-worklist  [worklistId]="metaDat.id" [dynamicData]="this.dynamicData" class="worklist-content" ></app-eversana-worklist>
                    </div>
            </ng-container>
           

        </section>
      
          
    </div>
</section>
<!-- END: tables/datatables -->