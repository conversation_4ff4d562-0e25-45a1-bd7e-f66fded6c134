import { Injectable } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';

import { DynamicBase } from './dynamic-base';

@Injectable()
export class DynamicControlService {
    constructor() { }

    toFormGroup(fields: DynamicBase<any>[]) {
        let group: any = {};
        if (fields) {
            fields.forEach(fields => {
                group[fields.key] = fields.required ?
                    new FormControl(fields.value || '', Validators.required)
                    : new FormControl(fields.value || '');
            });
        }
        return new FormGroup(group);
    }
}
