import { Component, Input } from '@angular/core';
import { FormGroup, FormBuilder, FormArray, FormControl } from '@angular/forms';

import { DynamicBase } from './dynamic-base';
declare var $: any;
@Component({
  selector: 'app-field',
  templateUrl: './dynamic-form-question.component.html'
})
export class DynamicFormQuestionComponent {
  @Input() field: DynamicBase<any>;
  @Input() form: FormGroup;
  isChecked;
  tags;
  ngOnInit(): void {
  }
  onChange(val: string, isChecked: boolean) {
    if (isChecked) {
      if (val === 'Others') {
        this.isChecked = true;
      } else {
        this.isChecked = false;
      }
    } else {
      this.isChecked = false;
    }
  }

  changeCheckbox(tags, i) {
    if (tags) {
      tags[i].checked = !tags[i].checked;
    }
  }
}
