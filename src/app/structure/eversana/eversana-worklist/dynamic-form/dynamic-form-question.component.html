<div [formGroup]="form" *ngIf="field.show">
  <div class="">
    <div *ngIf="field.controlType != 'checkbox'">
      <label [attr.for]="field.key"><strong>{{field.label}}</strong></label>
    </div>
    <div class="">
      <div [ngSwitch]="field.controlType">
        <input class="form-control" *ngSwitchCase="'textbox'" [formControlName]="field.key" [id]="field.key" [type]="field.type">
        <div [id]="field.key" class="form-group" *ngSwitchCase="'radio'">
          <div class="radio" *ngFor="let opt of field.options">
            <label>
              <input type="radio" [value]="opt.optionId" ng-value="opt.optionId" [formControlName]="field.key"
                (change)="onChange(opt.optionId, $event.target.checked)" ng-model="opt.optionId">
              {{opt.optionValue}}
            </label>
            <!-- <br> -->

          </div>
        </div>
        <select [id]="field.key" class="form-control demo" *ngSwitchCase="'dropdown'" [formControlName]="field.key">
          <option value=''>Select</option>
          <option *ngFor="let opt of field.options" value="{{opt.optionId}}" >{{opt.optionValue}}</option>
      </select>
        <div class="form-group" *ngSwitchCase="'checkbox'">
            <span *ngSwitchCase="'checkbox'">
                <label [attr.for]="field.key">
                  <input [formControlName]="field.key" (change)="onChange(field.key, $event.target.checked)" [id]="field.key"
                    type="checkbox" [value]="field.label" ng-value="field.label" ng-model="field.label">
                  {{field.label}}</label>
               
              </span>
        </div>
        <input type="text" class="form-control" name="othersc" formControlName = "othersc" id="othersc" [hidden]="!isChecked">
       
      </div>
    </div>
  </div>
</div>