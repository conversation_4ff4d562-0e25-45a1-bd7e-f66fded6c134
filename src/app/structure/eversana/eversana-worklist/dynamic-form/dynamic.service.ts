import { Injectable }       from '@angular/core';
 
import { DropdownQuestion } from './dynamic-dropdown';
import { DynamicBase }     from './dynamic-base';
import { TextboxQuestion }  from './dynamic-textbox';
 
@Injectable()
export class DynamicService {
 
  // TODO: get from a remote source of question metadata
  // TODO: make asynchronous
  getQuestions() {
 
    let fields: DynamicBase<any>[] = [
 
      new DropdownQuestion({
        key: 'brave',
        label: 'Bravery Rating',
        options: [
          {key: 'solid',  value: 'Solid'},
          {key: 'great',  value: 'Great'},
          {key: 'good',   value: 'Good'},
          {key: 'unproven', value: 'Unproven'}
        ],
        order: 3
      }),
 
      new TextboxQuestion({
        key: 'firstName',
        label: 'First name',
        value: 'Bombasto',
        required: true,
        order: 1
      }),
 
      new TextboxQuestion({
        key: 'emailAddress',
        label: 'Email',
        type: 'email',
        order: 2
      })
    ];
 
    return fields.sort((a, b) => a.order - b.order);
  }
}