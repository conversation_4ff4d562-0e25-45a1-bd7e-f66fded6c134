<div>
  <form (ngSubmit)="onSubmit()" [formGroup]="form" class="mt-3">
    <div *ngFor="let field of fields" class="form-row">
      <app-field [field]="field" [form]="form"></app-field>
    </div>
    <div class="form-row mt-4">
        <div class="col-sm-offset-4">
      <button type="submit" class="btn btn-primary" [disabled]="!form.valid">Save</button>
      <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
    </div>
  </div>
  </form>
  <br>
</div>