export class DynamicBase<T> {
    value: T;
    key: string;
    label: string;
    required: boolean;
    order: number;
    controlType: string;
    fieldName: string;
    show: boolean;

    constructor(options: {
        value?: T,
        key?: string,
        label?: string,
        fieldName?: string,
        required?: boolean,
        order?: number,
        controlType?: string,
        show?:boolean
      } = {}) {
      this.value = options.value;
      this.key = options.key || '';
      this.label = options.label || '';
      this.required = options.required;
      this.order = options.order === undefined ? 1 : options.order;
      this.controlType = options.controlType || '';
      this.show = options.show;
      this.fieldName = options.fieldName;
    }
}