import { Component, OnInit, EventEmitter, Output } from '@angular/core';
import { Router, ActivatedRoute, Params, NavigationEnd } from '@angular/router';
import { Subject } from 'rxjs';
import { e } from '@angular/core/src/render3';
import { EversanaWorkListService } from './worklist.service';
import { StructureService } from '../../structure.service';
import 'ag-grid-enterprise';
let moment = require('moment/moment');
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var swal: any;
declare var NProgress: any;

@Component({
    selector: 'app-eversana-worklist-tab',
    templateUrl: './worklist.tab.component.html',
    styleUrls: ['./worklist.tab.component.css'],

})
export class WorklistTabComponent implements OnInit {
    public gridApi;
    public menuShow = true;
    filterDetails: any = [];
    dynamicData: any = [];
    optionShow;
    dynamicTabs;
    metaArray: any = [];
    worklistId;
    private gridColumnApi;
    rowData: any = [];
    metaFields: any = [];
    columnDefs = [];
    helpMsg: string;
    dashboardWidgets;
    dataLoadingMsg: boolean;
    defaultColDef: any;
    list = [];
    // showAddEnrollment = false;
    sideBar: any;
    worklistArray = [];
    constructor(private router: Router, private route: ActivatedRoute, public _structureService: StructureService, public _worklistService: EversanaWorkListService,) {
        
    }
    ngOnInit() {   
        this.helpMsg = 'Click on a tab to see the Patient Details under a category';      
        this._worklistService.activeActivityDetails = []; 
        this._worklistService.getWorkLists().then((data) => {
            if (data) {
                this.dynamicTabs = data['getSessionTenant'].formWorklists.filter((item) => (item.name == 'HCP Dashboard'));
                const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
                //let privileges = Object.keys(this._worklistService.privileges);
                this.dynamicTabs.forEach(element => {
                    let newJson = element.description;
                    newJson = newJson.replace(/'/g, '"');
                    let metaData = JSON.parse(newJson);
                    metaData['id'] = element.id;
                    if (element.active) {
                        if (((metaData.visibleToRoles && metaData.visibleToRoles.split(',').indexOf(userDetails.roleId) !== -1) || (metaData.allowCrossTenant == true && metaData.visibleToOtherRoles && metaData.visibleToOtherRoles.split(',').indexOf(userDetails.roleId) !== -1))) {
                            if (metaData.enablePrivilegedAccess && (metaData.privileges && metaData.privileges != '')) {
                                let privilegeArray = metaData.privileges.split(',');
                                // let check = 0;
                                // privilegeArray.forEach(element => {
                                //     if (privileges.indexOf(element) != -1) {
                                //         if (this._structureService.privileges[element] == true) {
                                //             check = 1;
                                //         }
                                //     }
                                // });
                                // if (check == 1) {
                                    this.metaArray.push(metaData);
                               //
                            
                               //}
                            } else {
                                this.metaArray.push(metaData);
                            }
                        } else {
                            if (metaData.visibleToRoles == '') {
                                this.metaArray.push(metaData);
                            } else if (metaData.visibleToOtherRoles == '') {
                                this.metaArray.push(metaData);
                            }
                        }
                    }
                });
                this.metaArray.sort(function (a, b) {
                    if (a.tabIndex < b.tabIndex) { return -1; }
                    if (a.tabIndex > b.tabIndex) { return 1; }
                    return 0;
                });
                this.showTabData(this.metaArray[0].id);

            }
        });
        // if (this._structureService.activePatientActivityHub == true) {
        //     this.optionShow = this._structureService.activePatientActivityHubTabId;
        //     this.userPatientId = this._structureService.activePatientActivityHubPatientId;
        // }
        //this.setPatientList();
        
    }
    
    showTabData(id) {
        this.optionShow = id;
        this.worklistId = id;
        let filterDetails = this.metaArray.filter(x => x.id == id);
        console.log(filterDetails);
        // if(filterDetails[0].tabName == "Dashboard") {
        //     this.showAddEnrollment = false;
        // } else {
        //     this.showAddEnrollment = true;
        // }
        this.dynamicData = {
            "tabName": filterDetails[0].tabName,
            "worklistId": filterDetails[0].id,
            'filterDetails': filterDetails
        };
    }
}
