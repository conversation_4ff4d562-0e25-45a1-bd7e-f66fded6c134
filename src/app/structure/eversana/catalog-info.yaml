apiVersion: backstage.io/v1alpha1

# Valid kind are Component, Template, API, Resource, System, Domain, Location. # Read more about kind at https://backstage.io/docs/features/software-catalog/descriptor-format/#kind-component
kind: Component
metadata:
  name: "desktop-client-eversana"

  # Describe what your project is and what it does
  description: "CitusHealth Desktop Client eversana"

  # This is the non-slug version of your project's name, such as "ResMed Awesome App"
  title: "citushealth-desktop-client-eversana"

  # Provide relevant links for your project, such as where it's repo is located, the staging url, any extra docs, etc.
  links:
    - title: HomePage
      url: "https://github.com/resmed/citushealth-desktop-client-eversana"

  # Fill out info for location of mkdocs or api spec
  annotations:
    backstage.io/techdocs-ref: dir:.
    sonarqube.org/project-key: "resmed_citushealth-desktop-client-eversana"
    resmed.com/capability-code: ""
    resmed.com/business-owner: ""
    resmed.com/business-vertical: ""
    resmed.com/product-identifier: ""

spec:
  # Valid type are service, website, library etc # Read More at https://backstage.io/docs/features/software-catalog/descriptor-format/#spec-varies
  type: website

  # This should be the GitHub team slug
  owner: "citushealth-admin"

  # Valid lifecycles are production, development, experimental, deprecated, pre-alpha, aplha, beta, release-candidate and stable
  lifecycle: development
