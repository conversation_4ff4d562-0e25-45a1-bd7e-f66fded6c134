import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { FormManageModule } from '../forms/forms.module';
import { DocumentsModule } from '../documents/documents.module';
import { AgGridModule } from 'ag-grid-angular/main';
import { SharedModule } from '../shared/sharedModule';
import { MessageModule } from '../message/message.module';
import { WorklistTabComponent } from './eversana-worklist/worklist.tab.component';
import { WorklistComponent } from './eversana-worklist/worklist.component';
import { MyDatePickerModule } from 'mydatepicker';
import { ColorPickerModule } from 'ngx-color-picker';
import { MentionModule } from 'angular-mentions';
import { ToastModule } from '@syncfusion/ej2-angular-notifications';
import { IconPickerModule } from 'ngx-icon-picker';
import { DynamicFormComponent } from './eversana-worklist/dynamic-form/dynamic-form.component';
import { DynamicFormQuestionComponent } from './eversana-worklist/dynamic-form/dynamic-form-question.component';
import { ButtonRendererComponent } from './eversana-worklist/renderer/button-renderer.component';
import { GroupRowInnerRenderer } from './eversana-worklist/renderer/group-row-inner-renderer.component';
import { IconCellRenderer } from './eversana-worklist/renderer/icon-cell-renderer.component';
export const routes: Routes = [
  { path: 'hcp-dashboard', component: WorklistTabComponent },
];

@NgModule({
  imports: [
    MyDatePickerModule,
    ColorPickerModule,
    MentionModule,
    ToastModule,
    IconPickerModule,
    CommonModule,
    FormsModule,
    BrowserModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    AgGridModule,
    SharedModule,
    MessageModule,
    FormManageModule,
    DocumentsModule
  ],
  declarations: [
    WorklistTabComponent,
    WorklistComponent,
    DynamicFormComponent,
    DynamicFormQuestionComponent,
    IconCellRenderer,
    ButtonRendererComponent,
    GroupRowInnerRenderer
  ],
  // providers: [
  // ]
})

export class EversanaModule { }
