
<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong id="invite-initiate-label">{{pageTitle}}</strong>
        </span>
        <a *ngIf="userData.config.enable_staff_import && userData.privileges.includes('enableImport')" style="float:right;" class="btn btn-sm btn-info" (click)="UploadBtnAction()">Import Users</a>
        <div class="modal fade data-modal" id="data-modal-upload" tabindex="-1" role="dialog"
            aria-labelledby="exampleModalLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="exampleModalLabel">Import Users</h4>
                        <span *ngIf="showLoading"
                            style="color:teal;text-align: center;padding:20px 20px;">Please wait while we process
                            your request... <i class="fa fa-refresh fa-spin"></i></span>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-12">
                                <input type="file" accept=".xls,.xlsx,.csv,.ods" id="bulkDirectFile" name="bulkDirectFile" (change)="uploadFile($event)">
                            </div>
                        </div>
                        <div style="padding-left: 10px;padding-top: 20px;" class="row">
                            <p id="result-report"></p>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-info" [disabled]="buttonDisabled"
                            (click)="saveUploadFile()">Upload</button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item">User Center</li>
            <li class="breadcrumb-item">{{pageTitleBreadcrumbs}}</li>
        </ol>
        <div class="row">
            <div class="col-lg-12">
                <div class="mb-5">
                    <div class="tab-content">

                    <form class="form-horizontal enroll-form"  [formGroup]="newPatient"  (ngSubmit)="registerUser()"
                        autocomplete="off"
                        novalidate #f="ngForm">
                        <div class="form-body">
                            <div [hidden]="pageNumber > 0" class="form-group row" id="user-initiate-category">
                                <label class="col-md-3 control-label">User Category</label>
                                <div class="col-md-6">
                                    <label *ngIf="userData.privileges.includes('allowAddUser') && userData.privileges.includes('addVirtualPatientUsers')"
                                        class="form-check-label" style="margin-right:15px;">
                                        <input
                                            [formControl]="newPatient.controls['utype']" id="uTypePatient"
                                            (click)="userCategoryChange('patient')" checked="" class="form-check-input"
                                            name="utype" value="patient" type="radio"> Virtual Patient
                                    </label>
                                    <label *ngIf="userData.privileges.includes('allowAddUser') && userData.privileges.includes('addVirtualEnrolledStaffUsers')"
                                        class="form-check-label" style="margin-right:15px;">
                                        <input [attr.disabled]="editId && editId != '' ? '' : null"
                                            [formControl]="newPatient.controls['utype']" id="uTypeStaff"
                                            (click)="userCategoryChange('staff')" class="form-check-input" name="utype"
                                            value="staff" type="radio"> Virtual/Enrolled Staff
                                    </label>
                                    <label *ngIf="userData.privileges.includes('allowAddUser') && userData.privileges.includes('addVirtualPartnerUsers')"
                                        class="form-check-label">
                                        <input [attr.disabled]="editId && editId != '' ? '' : null"
                                            [formControl]="newPatient.controls['utype']" id="uTypePartner"
                                            (click)="userCategoryChange('partner')" class="form-check-input"
                                            name="utype" value="partner" type="radio"> Virtual Partner
                                    </label>
                                </div>
                            </div>

                          <!-- for alternate contact--->
                        <div *ngIf="newPatient.controls['utype'].value == 'patient'">
                            <div class="col-md-6">
                                <ul class="nav nav-tabs mb-4" role="tablist">
                                    <li id="user-reg-patient-tab">
                                        <ul class="nav nav-tabs">
                                            <li class="nav-item">
                                                <a id="enable-alternative" class="nav-link" data-target="#tabPatientDetails" [ngClass]="{ active: activePatientTab == 'details' }"
                                                    (click)="setActivetab('details')" data-toggle="tab" href="javascript: void(0);"
                                                    role="tab" aria-expanded="true" ><i
                                                        class="fa fa-id-badge completed"></i> Patient Details</a>
                                                </li>
                                            <li class="nav-item">
                                                <span *ngIf="userData.config.default_patients_workflow == 'alternate_contacts'">
                                                <a id="disable-alternative" class="nav-link" data-target="#tabAlternateContacts"
                                                [ngClass]="{ active: activePatientTab === 'alternate-contacts' }"
                                                (click)="activePatientTab='alternate-contacts';"

                                                data-toggle="tab" href="javascript: void(0);" role="tab"
                                                    aria-expanded="false"><i class="fa fa-address-book action-required"></i> Alternate Contacts
                                                </a>
                                            </span>
                                            </li>

                                        </ul>
                                    </li>

                                </ul>

                            </div>

                        </div>
                             <!-- for alternate contact--->

                                <div class="tab-pane" id="tabPatientDetails" [ngClass]="{ active: activePatientTab == 'details' }"
                                    (click)="activePatientTab='details'" role="tabcard" aria-expanded="true">
                            <div *ngIf="newPatient.controls['utype'].value != 'partner' && activePatientTab !='alternate-contacts'">
                                <div class="form-group row" [hidden]="!((manageConfig.show_user_tagging == 1 && newPatient.controls['utype'].value =='patient')||(manageConfig.show_user_tagging == 1 && manageConfig.enable_nursing_agencies_visibility_restrictions == 1 && (newPatient.controls['utype'].value =='patient'))) || pageNumber > 0" *ngIf="newPatient.controls['utype'].value =='patient'">
                                    <label class="col-md-3 control-label">User Tag
                                        <i chToolTip="PatientType"></i>
                                    </label>
                                    <div class="col-md-6">
                                        <div id="tags">
                                            <div class="recipient-search-area">
                                                <div class="input-dropdown">
                                                    <input type="text" (keydown)="searchOnEnter($event)"
                                                        class="form-control" id="tagsInput" autocomplete="off" value=""
                                                        placeholder="{{ 'PLACEHOLDERS.ADD_USER__USER_TAG' | translate }}" />

                                                    <ul class="associate-ul recipient-ul" id="recipient-ul">

                                                        <li id="recipient-li" class="associate-li recipient-li"
                                                            *ngIf="responsePatient && responsePatient.length == 0">
                                                            No item found
                                                        </li>

                                                        <li id="li-{{ tag.id }}" class="associate-li recipient-li"
                                                            [ngClass]="{'li-selected': checkTagExist(tag.id)}" *ngFor="let tag of responsePatient"
                                                            (click)="setSelectedTags(tag, tag.id)">
                                                            {{ tag.tag_name }}
                                                        </li>

                                                        <li class="render-manipulate"
                                                            *ngIf="patientTags && patientTags.length > 0">
                                                            <input type="button" class="recipient-select-all btn"
                                                                (click)="selectAllTags()" value="Select All" />

                                                            <input type="button" class="recipient-class-clear btn"
                                                                (click)="closeSelectedTag(true)"
                                                                value="Clear All" />

                                                            <input type="button" class="recipient-class-done btn"
                                                                *ngIf="selectedTags && selectedTags.length > 0"
                                                                (click)="doneSelectedTag()" value="Done" />

                                                        </li>
                                                    </ul>
                                                </div>
                                                <div>
                                                    <button type="button" [disabled]="recipientLoading"
                                                        id="recipient-search" (click)="checkTagWithTems()"
                                                        class="recipient-search-button btn btn-sm btn-primary">
                                                        Search
                                                    </button>

                                                    <button type="button" [disabled]="recipientLoading"
                                                        id="recipient-close" (click)="closeSelectedTag()"
                                                        class="recipient-search-button btn btn-sm btn-default recipient-close">
                                                        Reset
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Role Selection for Staff-->
                                <div id="user-role" class="form-group row" *ngIf="newPatient.controls['utype'].value =='staff'">
                                    <label class="col-md-3 control-label">User Role *</label>
                                    <div class="col-md-6">
                                        <select [formControl]="newPatient.controls['userRole']" name="userRole"
                                            class="form-control show-staff" style="display: block;">
                                            <option value="">Select User Role</option>
                                            <option *ngFor="let role of staffRoles" [value]="role.id">
                                                {{role.name }}
                                            </option>
                                        </select>
                                        <div class="alert alert-danger"
                                            *ngIf="!newPatient.controls.userRole.valid && (newPatient.controls.userRole.dirty || newPatient.controls.userRole.touched || f.submitted)">
                                            Please select user role.
                                        </div>
                                    </div>
                                </div>
                                <!-- EndRole Selection for Staff-->

                                <!-- Selection of Staff/Patient Tag-->
                                <div class="form-group row"
                                    [hidden]="manageConfig.show_user_tagging != 1 || !(newPatient.controls['utype'].value =='staff') || pageNumber > 0"
                                    *ngIf="newPatient.controls['utype'].value =='staff'">
                                    <label class="col-md-3 control-label">User Tag
                                        <i chToolTip="StaffType"></i>
                                    </label>
                                    <div class="col-md-6">
                                        <div id="tags" name="staffTag">
                                            <div class="recipient-search-area">
                                                <div class="input-dropdown">
                                                    <input type="text" class="form-control"
                                                        (keydown)="searchOnEnter($event)" id="tagsInput"
                                                        autocomplete="off" value="" placeholder="{{ 'PLACEHOLDERS.ADD_USER__USER_TAG' | translate }}" />

                                                    <ul class="associate-ul recipient-ul" id="recipient-ul">
                                                        <li id="recipient-li" class="associate-li recipient-li"
                                                            *ngIf="responseStaff && responseStaff.length == 0">
                                                            No item found
                                                        </li>

                                                        <li id="li-{{ tag.id }}" class="associate-li recipient-li"
                                                            [ngClass]="{'li-selected': checkTagExist(tag.id)}"
                                                            *ngFor="let tag of responseStaff"
                                                            (click)="setSelectedTags(tag, tag.id)">
                                                            {{ tag.tag_name }}
                                                        </li>

                                                        <li class="render-manipulate"
                                                            *ngIf="staffTags && staffTags.length > 0">
                                                            <input type="button" class="recipient-select-all btn"
                                                                (click)="selectAllTags()" value="Select All" />

                                                            <input type="button" class="recipient-class-clear btn"
                                                                (click)="closeSelectedTag(true)"
                                                                value="Clear All" />

                                                            <input type="button" class="recipient-class-done btn"
                                                                *ngIf="selectedTags && selectedTags.length > 0"
                                                                (click)="doneSelectedTag()" value="Done" />

                                                        </li>
                                                    </ul>
                                                </div>

                                                <div>
                                                    <button type="button" [disabled]="recipientLoading"
                                                        id="recipient-search" (click)="checkTagWithTems()"
                                                        class="recipient-search-button btn btn-sm btn-primary">
                                                        Search
                                                    </button>

                                                    <button type="button" [disabled]="recipientLoading"
                                                        id="recipient-close" (click)="closeSelectedTag()"
                                                        class="recipient-search-button btn btn-sm btn-default recipient-close">
                                                        Reset
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row" id="select-site" [hidden]="!hideSiteSelection" *ngIf="singleSelection">
                                    <label class="col-md-3 control-label"> {{'LABELS.SITE' | translate}} *
                                        <i chToolTip="site"></i>
                                    </label>
                                    <div class="col-md-6">
                                        <app-select-sites [events]="eventsSubject.asObservable()" [hideApplyFilter]=true [filterType]="filterType" [dynamic]="filterType" [siteSelection]="true" [singleSelection]="singleSelection" [selectedSiteIds]="editSiteData" (siteIds)="getSiteIds($event)" (hideDropdown)="hideDropdown($event)">
                                        </app-select-sites>
                                        <div class="alert alert-danger site-position" *ngIf="siteRequired">
                                            Please select site.
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group row site-row-margin" id="select-site" [hidden]="!hideSiteSelection" *ngIf="!singleSelection">
                                    <label class="col-md-3 control-label"> {{'LABELS.SITES' | translate}} *
                                        <i chToolTip="site"></i>
                                    </label>
                                    <div class="col-md-6">
                                        <app-select-sites [events]="eventsSubject.asObservable()" [hideApplyFilter]=true [siteSelection]="true" [singleSelection]="singleSelection" (siteIds)="getSiteIds($event)" (hideDropdown)="hideDropdown($event)">
                                        </app-select-sites>
                                        <div class="alert alert-danger site-position" *ngIf="siteRequired">
                                            Please select site.
                                        </div>
                                    </div>
                                </div>
                                <!-- End Selection of Staff/Patient Tag-->

                                <div class="form-group row" id="home-site" [hidden]="!hideSiteSelection" *ngIf="!singleSelection">
                                    <label class="col-md-3 control-label"> {{'LABELS.HOME_SITE' | translate}}
                                        <i chToolTip="homeSiteStaff"></i>
                                    </label>
                                    <div class="col-md-6">
                                        <app-home-site [events]="eventsSubject.asObservable()" [siteSelection]="true" [singleSelection]="true" [dynamic]="true" (siteIds)="getHomeSiteId($event)"  [selectedSiteIds]="siteId" >
                                        </app-home-site>
                                    </div>
                                </div>

                                <div id="patient-name">
                                    <div [hidden]="pageNumber > 0 " class="form-group row" id="first-name">
                                        <label class="col-md-3 control-label">{{ newPatient.controls['utype'].value }}
                                            First Name *</label>
                                        <div class="col-md-6">
                                            <input [attr.disabled]="onEditPFNameLoad ? '' : null" type="text"
                                                (keypress)="omit_special_char($event)" class="form-control" id="fname"
                                                name="pfname" placeholder="First Name"
                                                [formControl]="newPatient.controls['pfname']">
                                            <div class="alert alert-danger"
                                                *ngIf="!newPatient.controls.pfname.valid && (newPatient.controls.pfname.dirty || newPatient.controls.pfname.touched || f.submitted)">
                                                First name cannot be empty.
                                            </div>
                                        </div>
                                    </div>
                                    <div [hidden]="pageNumber > 0" class="form-group row" id="last-name">
                                        <label class="col-md-3 control-label">{{newPatient.controls['utype'].value}}
                                            Last Name *</label>
                                        <div class="col-md-6">
                                            <input [attr.disabled]="onEditPLNameLoad ? '' : null" type="text"
                                                (keypress)="omit_special_char($event)" class="form-control" id="lname"
                                                name="plname" placeholder="Last Name"
                                                [formControl]="newPatient.controls['plname']">
                                            <div class="alert alert-danger"
                                                *ngIf="!newPatient.controls.plname.valid && (newPatient.controls.plname.dirty || newPatient.controls.plname.touched || f.submitted)">
                                                Last name cannot be empty.
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Selection of Staff ID-->
                                <div [hidden]="pageNumber > 0 " class="form-group row"
                                    *ngIf="newPatient.controls['utype'].value =='staff' && manageConfig.show_staffid_in_user_invite == 1">
                                    <label for="mrn" class="col-md-3 control-label"> {{'LABELS.STAFF_ID' | translate}}
                                        <i chToolTip="staffIdInStaffInvitation"></i></label>
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" id="mrn" name="mrn" appTrimSpaces
                                            placeholder="{{'LABELS.STAFF_ID' | translate}}" [formControl]="newPatient.controls['mrn']" appRequiredIndicator>
                                        <div class="alert alert-danger"
                                            *ngIf="!newPatient.controls.mrn.valid && (newPatient.controls.mrn.dirty || newPatient.controls.mrn.touched || f.submitted)">
                                            {{'VALIDATION_MESSAGES.STAFF_ID_EMPTY' | translate}}
                                        </div>
                                    </div>
                                </div>
                                <!-- End Selection of Staff ID-->

                                <div class="form-group row" id="dob-parent"
                                    *ngIf="newPatient.controls['utype'].value == 'patient'" [hidden]="pageNumber > 0 ">
                                    <div class="col-md-3"><label class="label-doba" for="">Date Of Birth <span
                                                *ngIf="newPatient.controls['utype'].value =='patient'">*</span></label>
                                    </div>
                                    <div class="col-md-6">
                                        <input [attr.disabled]="onEditDobLoad ? '' : null" type="text"
                                            class="form-control" id="dob-date-picker" required="">

                                        <input [attr.disabled]="onEditDobLoad ? '' : null" type="hidden"
                                            class="form-control" id="dob" name="dob"
                                            [formControl]="newPatient.controls['dob']">
                                        <div class="alert alert-danger"
                                            *ngIf="!newPatient.controls.dob.valid && (f.submitted || newPatient.controls.dob.touched)">
                                            Date of Birth cannot be empty.
                                        </div>
                                    </div>
                                </div>

                                <div id="mobile-email">
                                    <div class="row" *ngIf="newPatient.controls['createPassword'].value || newPatient.controls['utype'].value !== 'patient' || (isEnrolledPatient && editId )">
                                        <div class="col-md-6 offset-md-3">
                                            <app-display-info infoContent="{{ 'VALIDATION_MESSAGES.EMAIL_OR_MOBILE_NUMBER_MANDATORY' | translate }}"></app-display-info>
                                        </div>
                                    </div>                                
                                    <div id="mobile-no" class="form-group row">
                                        <label class="col-md-3 control-label">{{'LABELS.MOBILE_NUMBER' | translate}}</label>
                                        <div class="col-md-6">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <input type="text"
                                                        [ngStyle]="newPatient.controls.countryCode.errors ? {'border-color': 'red'} : {}"
                                                        placeholder="Country Code"
                                                        onkeypress="return (event.charCode >= 48 && event.charCode <= 57) || event.charCode == 43"
                                                        class="form-control" id="country_code">
                                                </div>
                                                <div class="col-md-8">
                                                    <input appPhoneNumberValidator [textMask]="{mask: maskPhoneNumber, guide: false}" formControlName="pcellno" name="pcellno" id="pcellno"
                                                        placeholder="{{'LABELS.MOBILE_NUMBER' | translate}}" class="form-control tel" (blur)="oldPhoneNumberCheck()">
                                                    <input type="hidden" id="oldPhoneNumber" name="oldNumber">
                                                </div>
                                            </div>
                                            <div class="alert alert-danger"
                                                *ngIf="newPatient.controls.pcellno.errors != null">
                                                {{'VALIDATION_MESSAGES.PLEASE_ENTER_VALID_MOBILE_NUMBER' | translate}}
                                            </div>
                                        </div>
                                    </div>
                                    <div id="email" class="form-group row">
                                        <label class="col-md-3 control-label">Email <i chToolTip="communicationEmail"></i></label>
                                        <div class="col-md-6">
                                            <div class="row">
                                                <div class="col-md-12" >
                                                    <div class="ac">
                                                        <input class="form-control" id="email-address" type="text" autocomplete="off"
                                                            placeholder="Email" formControlName="patientEmail" (blur)="oldEmailIdCheck()" />
                                                    </div>
                                                    <div class="alert alert-danger"
                                                        *ngIf="newPatient.controls.patientEmail.errors && newPatient.controls.patientEmail.errors.pattern && (newPatient.controls.patientEmail.dirty || newPatient.controls.patientEmail.touched || f.submitted)">
                                                        Please enter a valid email address.
                                                    </div>

                                                    <p class="ac" id="status-message"></p>
                                                    <input type="hidden" class="form-control" id="pemail" name="pemail"
                                                        placeholder="Email" [formControl]="newPatient.controls['pemail']">
                                                    <input type="hidden" id="oldemailId" name="oldemail">
                                                    <div class="alert alert-danger"
                                                        *ngIf="newPatient.controls.pemail.errors && newPatient.controls.pemail.errors.required && (newPatient.controls.pemail.dirty || newPatient.controls.pemail.touched || f.submitted)">
                                                        Email Address cannot be empty.
                                                    </div>
                                                    <div class="alert alert-danger"
                                                        *ngIf="newPatient.controls.pemail.errors && newPatient.controls.pemail.errors.pattern && (newPatient.controls.pemail.dirty || newPatient.controls.pemail.touched || f.submitted)">
                                                        Please enter a valid email address.
                                                    </div>
                                                </div>
                                            <div class="col-md-2" *ngIf="verifyPopup && !verifyPopup.verifyEmailCodeSend && newPatient.controls.pemail.value !='' &&(newPatient.controls.pemail.valid || newPatient.controls.pemail.touched) && userData.config.enable_verification_of_cell_and_mobile == 1" >
                                                    <button type="button" class="btn btn-primary btn-sm" *ngIf="!newPatient.controls.emailVerified.value"(click)="verifyPopup.show_popup({email:true,formData:newPatient.value})">Verify</button>
                                                    <span class="btn btn-primary btn-sm" *ngIf="newPatient.controls.emailVerified.value">Verified</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="zip-code" class="form-group row">
                                    <label class="col-md-3 control-label">Zip Code</label>
                                    <div class="col-md-6">
                                        <input type="text" (keypress)="omit_special_char($event)" class="form-control" id="zipcode" name="zipcode" placeholder="Zip Code" [formControl]="newPatient.controls['zipcode']">
                                        <div class="alert alert-danger" *ngIf="!newPatient.controls.zipcode.valid && (newPatient.controls.zipcode.dirty || newPatient.controls.zipcode.touched || f.submitted)">
                                            Zipcode cannot be empty.
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group row" *ngIf="newPatient.controls['utype'].value =='staff'">
                                    <label class="col-md-3 control-label"> Create Password and Enroll Staff <i chToolTip="TempPassword"></i></label>
                                    <!-- <i class="ltoPC icmn-info"></i> -->
                                    <div class="col-md-9">
                                        <div class="btn-group col-md-2">
                                            <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': newPatient.controls['createPassword'].value == true}" (click)="togglePreference('createPassword',true)">Yes</button>
                                            <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': newPatient.controls['createPassword'].value == false}" (click)="togglePreference('createPassword',false)">No</button>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group row check-align" *ngIf="newPatient.controls['createPassword'].value">
                                    <div class="col-md-3"></div>
                                    <div class="col-md-6 ">
                                        <div class="checkbox checkbox-primary">
                                            <input (change)="onCheckboxEmailAsUsernameChange($event)" type="checkbox"  id="checkboxEmailUsernamePartner" [formControl]="newPatient.controls['checkboxEmailUsernamePartner']" />
                                            <label data-toggle="tooltip" data-placement="top" style="margin-bottom: 0 !important;" for="checkboxEmailUsernamePartner"> {{'LABELS.CHECKBOX_USERNAME' | translate}} <i chToolTip="checkboxUsername"></i></label>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group row" *ngIf="newPatient.controls['createPassword'].value">
                                    <label class="col-md-3 control-label">{{'LABELS.USERNAME' | translate}} * <i chToolTip="username"></i></label>
                                    <div class="col-md-6">
                                        <input type="text" class="form-control" placeholder="{{'PLACEHOLDERS.USERNAME' | translate}}"
                                        [formControl]="newPatient.controls['patientUsername']" name="patientUsername" (blur)="checkUsernameSameAsEmail()" />
                                        <div class="alert alert-danger"
                                           *ngIf="newPatient.controls.patientUsername.errors && (newPatient.controls.patientUsername.touched || newPatient.controls.patientUsername.dirty|| f.submitted)">
                                          {{'VALIDATION_MESSAGES.USERNAME'| translate}}
                                        </div>
                                    </div>
                                </div>

                                <div *ngIf="!(!newPatient.controls['createPassword'].value || newPatient.controls['utype'].value !='staff')" class="form-group row" >
                                    <label class="col-md-3 control-label">Password</label>
                                    <div class="col-md-6">
                                            <input #passwordField autocomplete="new-password" class="form-control" id="temppassword" name="temppassword" [type]="show_button ? 'text' : 'password'" placeholder="Password" [formControl]="newPatient.controls['temppassword']" />
                                            <i data-placement="top" data-toggle="tooltip" title="" data-original-title="Show temporary password" id="toggle_icon" [class]="show_eye ? 'fa fa-eye show-password' : 'fa fa-eye-slash show-password'" (click)="showPassword()"></i>
                                                <div *ngIf="newPatient.controls['temppassword'].hasError('required')&&(newPatient.controls.temppassword?.dirty ||newPatient.controls.temppassword?.touched || f.submitted)" class="alert alert-danger">
                                                    Password Required
                                                </div>
                                                <div 
                                                *ngIf="f.errors?.invalidPassword && (newPatient.controls.temppassword.dirty || newPatient.controls.temppassword.touched || f.submitted)" 
                                                class="alert alert-danger">
                                                {{'VALIDATION_MESSAGES.OKTA_PASSWORD_VALIDATION' | translate}}
                                              </div>
                                       </div>
                                </div>
                                <div *ngIf="!(!newPatient.controls['createPassword'].value || newPatient.controls['utype'].value !='staff')" class="form-group row" >
                                    <label class="col-md-3 control-label">Confirm Password</label>
                                    <div class="col-md-6">
                                            <input class="form-control" autocomplete="new-password" id="repeat-password" name="repeat-password" type="password"
                                                placeholder="Confirm Password" [formControl]="newPatient.controls['repeatPassword']" />
                                                <div *ngIf="newPatient.controls['repeatPassword'].hasError('required')&&(newPatient.controls.repeatPassword?.dirty ||newPatient.controls.repeatPassword?.touched || f.submitted)" class="alert alert-danger">
                                                    Please Confirm Password
                                                </div>
                                                <div *ngIf="f.errors?.passwordNotMatch &&(newPatient.controls.repeatPassword?.dirty ||newPatient.controls.repeatPassword?.touched || f.submitted)" class="alert alert-danger">
                                                        Password mismatch
                                                    </div>
                                    </div>
                                </div>

                                <div id="associate-mrn-with-patient" style="margin-top:20px;display: none"
                                    class="form-group row"
                                    *ngIf="newPatient.controls['utype'].value == 'patient' && manageConfig.enable_external_integration==1 && showInInviteRecord"
                                    [hidden]="pageNumber > 0">
                                    <label class="col-md-3 control-label">Associate MRN with this patient?</label>
                                    <div class="col-md-9">
                                        <div class="checkbox checkbox-primary">
                                            <input (change)="setMrnField()"
                                                [checked]="showInInviteRecord.requiredInInvite"
                                                [attr.disabled]="showInInviteRecord.requiredInInvite ? 'disabled' : null || onEditUseMRNLoad ? '' : null"
                                                type="checkbox" id="use_mrn" name="use_mrn"
                                                [formControl]="newPatient.controls['use_mrn']" />
                                            <label data-toggle="tooltip" data-placement="top" for="use_mrn"></label>
                                        </div>
                                    </div>
                                </div>

                                <span>
                                    <div id="associate-mrn-with-patient" style="margin-top:20px;" class="form-group row"
                                        *ngIf="newPatient.controls['utype'].value == 'patient'">
                                        <label class="col-md-3 control-label">{{'LABELS.MRN'| translate}}
                                            <span *ngIf="_structureService.isMRNFieldMandatory">*</span>
                                        </label>

                                        <div class="col-md-6">
                                            <input type="text" (keypress)="omit_special_char($event)" [attr.disabled]="onEditMRNLoad ? '' : null" class="form-control" id="mrn" name="mrn" placeholder="{{'PLACEHOLDERS.MRN' | translate}}"
                                                appTrimSpaces [formControl]="newPatient.controls['mrn']" [readonly]="showDisabled">
                                            <div class="alert alert-danger"
                                                *ngIf="!newPatient.controls.mrn.valid && (newPatient.controls.mrn.dirty || newPatient.controls.mrn.touched || f.submitted)">
                                                {{'VALIDATION_MESSAGES.MRN_EMPTY'| translate}}
                                            </div>
                                        </div>
                                    </div>
                                </span>
                                <div id="enable_sms_notifications" class="form-group row" *ngIf="newPatient.controls['utype'].value == 'patient'">
                                    <label class="col-md-3 control-label">{{'LABELS.ENABLE_SMS_NOTIFICATION' | translate}}</label>
                                    <div class="col-md-6">
                                        <div class="btn-group">
                                            <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': newPatient.controls['enableSmsNotifications'].value === 1}"
                                                (click)="togglePreference('enableSmsNotifications', 1)">
                                                {{'BUTTONS.YES' | translate}}
                                            </button>
                                            <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': newPatient.controls['enableSmsNotifications'].value !== 1}"
                                                (click)="togglePreference('enableSmsNotifications', 0)">
                                                {{'BUTTONS.NO' | translate}}
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div id="enable_email_notifications" class="form-group row" *ngIf="newPatient.controls['utype'].value == 'patient'">
                                    <label class="col-md-3 control-label">{{'LABELS.ENABLE_EMAIL_NOTIFICATION' | translate}}</label>
                                    <div class="col-md-6">
                                        <div class="btn-group">
                                            <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': newPatient.controls['enableEmailNotifications'].value === 1}"
                                                (click)="togglePreference('enableEmailNotifications', 1)">
                                                {{'BUTTONS.YES' | translate}}
                                            </button>
                                            <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': newPatient.controls['enableEmailNotifications'].value !== 1}"
                                                (click)="togglePreference('enableEmailNotifications', 0)">
                                                {{'BUTTONS.NO' | translate}}
                                            </button>
                                        </div>
    
                                    </div>
                                </div>
                                <button *ngIf="userData.config.default_patients_workflow != 'alternate_contacts' || newPatient.controls['utype'].value != 'patient'"  id="btn-submit" [disabled]="disableSubmit" class="btn btn-info">
                                    Submit
                                </button>
                                <button  *ngIf="userData.config.default_patients_workflow == 'alternate_contacts' && newPatient.controls['utype'].value == 'patient'"    id="btn-submit" [disabled]="disableSubmit" class="btn btn-info">
                                   Save Patient Details
                                </button>
                                <a id="btn-cancel" class="btn btn-secondary" (click)="cancelAdd()">Cancel</a>
                            </div>
                        </div>




                            <div *ngIf="newPatient.controls['utype'].value == 'partner'">
                                <!-- Partner Form Start-->
                                <div [hidden]="pageNumber != 0">
                                    <div class="form-group row">
                                        <label class="col-md-3 control-label">Partner Role *</label>
                                        <div class="col-md-6">
                                            <select [formControl]="newPatient.controls['partnerRole']"
                                                name="partnerRole" class="form-control show-staff"
                                                style="display: block;">
                                                <option value="">Select Partner Role</option>
                                                <option *ngFor="let role of partnerRoles" [value]="role.id">
                                                    {{role.name }}
                                                </option>
                                            </select>
                                            <div class="alert alert-danger"
                                                *ngIf="!newPatient.controls.partnerRole.valid && (newPatient.controls.partnerRole.dirty || newPatient.controls.partnerRole.touched || f.submitted)">
                                                Please select user role.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row" *ngIf="manageConfig.enable_patient_info_from_third_party_app=='1' && partnerCategory.length > 0">
                                        <label class="col-md-3 control-label">Partner Category
                                            <i chToolTip="userCategory"></i>
                                        </label>
                                        <div class="col-md-6">
                                            <select [formControl]="newPatient.controls['partnerCategory']" name="partnerCategory"
                                            (change)="changePartnerCategory()" class="form-control show-staff" style="display: block;">
                                                <option value="">Select Partner Category</option>
                                                <option *ngFor="let category of partnerCategory" [value]="category.id">
                                                    {{category.displayName }}
                                                </option>
                                            </select>
                                            <div class="alert alert-danger"
                                                *ngIf="!newPatient.controls.partnerCategory.valid && (newPatient.controls.partnerCategory.dirty || newPatient.controls.partnerCategory.touched || f.submitted)">
                                                Please select partner category.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row"
                                        [hidden]="manageConfig.show_user_tagging != 1 || !(newPatient.controls['utype'].value =='partner') || pageNumber > 0"
                                        *ngIf="newPatient.controls['utype'].value =='partner'">
                                        <label class="col-md-3 control-label">User Tag
                                            <i chToolTip="PartnerType"></i>
                                        </label>
                                        <div class="col-md-6">
                                            <div id="tags">
                                                <div class="recipient-search-area">
                                                    <div class="input-dropdown">
                                                        <input type="text" (keydown)="searchOnEnter($event)"
                                                            class="form-control" id="tagsInput" autocomplete="off"
                                                            value="" placeholder="{{ 'PLACEHOLDERS.ADD_USER__USER_TAG' | translate }}" />

                                                        <ul class="associate-ul recipient-ul" id="recipient-ul">
                                                            <li id="recipient-li" class="associate-li recipient-li"
                                                                *ngIf="responsePartner && responsePartner.length == 0">
                                                                No item found
                                                            </li>
                                                            <li id="li-{{ tag.id }}" class="associate-li recipient-li"
                                                                [ngClass]="{'li-selected': checkTagExist(tag.id)}"
                                                                *ngFor="let tag of responsePartner"
                                                                (click)="setSelectedTags(tag, tag.id)">
                                                                {{ tag.tag_name }}
                                                            </li>

                                                            <li class="render-manipulate"
                                                                *ngIf="partnerTags && partnerTags.length > 0">
                                                                <input type="button" class="recipient-select-all btn"
                                                                    (click)="selectAllTags()"
                                                                    value="Select All" />

                                                                <input type="button" class="recipient-class-clear btn"
                                                                    (click)="closeSelectedTag(true)"
                                                                    value="Clear All" />

                                                                <input type="button" class="recipient-class-done btn"
                                                                    *ngIf="selectedTags && selectedTags.length > 0"
                                                                    (click)="doneSelectedTag()" value="Done" />

                                                            </li>
                                                        </ul>
                                                    </div>
                                                    <div>
                                                        <button type="button" [disabled]="recipientLoading"
                                                            id="recipient-search" (click)="checkTagWithTems()"
                                                            class="recipient-search-button btn btn-sm btn-primary">
                                                            Search
                                                        </button>

                                                        <button type="button" [disabled]="recipientLoading"
                                                            id="recipient-close" (click)="closeSelectedTag()"
                                                            class="recipient-search-button btn btn-sm btn-default recipient-close">
                                                            Reset
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row site-row-margin" id="select-site" [hidden]="!hideSiteSelection">
                                        <label class="col-md-3 control-label"> Sites *
                                            <i chToolTip="site"></i>
                                        </label>
                                        <div class="col-md-6">
                                            <app-select-sites [events]="eventsSubject.asObservable()" [hideApplyFilter]=true [siteSelection]="true" [singleSelection]="singleSelection" (siteIds)="getSiteIds($event)" (hideDropdown)="hideDropdown($event)">
                                            </app-select-sites>
                                            <div class="alert alert-danger site-position" *ngIf="siteRequired">
                                                Please select site.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row" id="home-site" [hidden]="!hideSiteSelection" *ngIf="!singleSelection">
                                        <label class="col-md-3 control-label"> {{'LABELS.HOME_SITE' | translate}}
                                            <i chToolTip="homeSitePartner"></i>
                                        </label>
                                        <div class="col-md-6">
                                            <app-home-site [events]="eventsSubject.asObservable()" [siteSelection]="true" [singleSelection]="true" [dynamic]="true" (siteIds)="getHomeSiteId($event)"  [selectedSiteIds]="siteId" >
                                            </app-home-site>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-md-3 control-label">{{partnerRoleTitle}} First Name *</label>
                                        <div class="col-md-6">
                                            <input type="text" (keypress)="omit_special_char($event)"
                                                class="form-control" id="fname" name="pfname" placeholder="First Name"
                                                [formControl]="newPatient.controls['pfname']">
                                            <div class="alert alert-danger"
                                                *ngIf="!newPatient.controls.pfname.valid && (newPatient.controls.pfname.dirty || newPatient.controls.pfname.touched || f.submitted)">
                                                First name cannot be empty.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-md-3 control-label">{{partnerRoleTitle}} Last Name *</label>
                                        <div class="col-md-6">
                                            <input type="text" (keypress)="omit_special_char($event)"
                                                class="form-control" id="lname" name="plname" placeholder="Last Name"
                                                [formControl]="newPatient.controls['plname']">
                                            <div class="alert alert-danger"
                                                *ngIf="!newPatient.controls.plname.valid && (newPatient.controls.plname.dirty || newPatient.controls.plname.touched || f.submitted)">
                                                Last name cannot be empty.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6 offset-md-3">
                                            <app-display-info infoContent="{{ 'VALIDATION_MESSAGES.EMAIL_OR_MOBILE_NUMBER_MANDATORY' | translate }}"></app-display-info>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-md-3 control-label">{{'LABELS.MOBILE_NUMBER' | translate}}</label>
                                        <div class="col-md-6">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <input type="text"
                                                        [ngStyle]="newPatient.controls.countryCode.errors ? {'border-color': 'red'} : {}"
                                                        placeholder="Country Code" class="form-control"
                                                        id="country_code">
                                                </div>
                                                <div class="col-md-8">
                                                    <input id="tel" appPhoneNumberValidator [textMask]="{mask: maskPhoneNumber, guide: false}" id="pcellno" name="pcellno"
                                                        formControlName="pcellno" id="pcellno"
                                                        placeholder="{{'LABELS.MOBILE_NUMBER' | translate}}" class="form-control tel"
                                                        (blur)="oldPhoneNumberCheck()">
                                                        <input type="hidden" id="oldPhoneNumber" name="oldNumber">
                                                </div>
                                               <!-- <div class="col-md-2" *ngIf="verifyPopup && !verifyPopup.verifyMobCodeSend && newPatient.controls.countryCode.valid && newPatient.controls.pcellno.value !='' &&(newPatient.controls.pcellno.valid || newPatient.controls.pcellno.touched) && userData.config.enable_verification_of_cell_and_mobile == 1" >
                                                        <button type="button" class="btn btn-primary btn-sm" *ngIf="!newPatient.controls.mobileVerified.value" (click)="verifyPopup.show_popup({mobile:true,formData:newPatient.value})">Verify</button>
                                                        <span class="btn btn-primary btn-sm" *ngIf="newPatient.controls.mobileVerified.value">Verified</span>
                                                </div> -->
                                                <app-verify-em></app-verify-em>
                                            </div>
                                            <div class="alert alert-danger"
                                                *ngIf="newPatient.controls.pcellno.errors != null">
                                                {{'VALIDATION_MESSAGES.PLEASE_ENTER_VALID_MOBILE_NUMBER' | translate}}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row">
                                        <label class="col-md-3 control-label">Email
                                        </label>
                                        <div class="col-md-6">
                                            <div class="row">
                                                <div class="col-md-12" >
                                                    <p class="ac">
                                                        <input class="form-control" id="email-address" type="text"
                                                            placeholder="Email" (blur)="oldEmailIdCheck()"/>
                                                    </p>
                                                    <p class="ac" id="status-message"></p>
                                                    <input type="hidden" class="form-control" id="pemail" name="pemail"
                                                        placeholder="Email" [formControl]="newPatient.controls['pemail']">
                                                    <div class="alert alert-danger"
                                                        *ngIf="newPatient.controls.pemail.errors && newPatient.controls.pemail.errors.required && (newPatient.controls.pemail.dirty || newPatient.controls.pemail.touched || f.submitted)">
                                                        Email Address cannot be empty.
                                                    </div>
                                                    <div class="alert alert-danger"
                                                        *ngIf="newPatient.controls.pemail.errors && newPatient.controls.pemail.errors.pattern && (newPatient.controls.pemail.dirty || newPatient.controls.pemail.touched || f.submitted)">
                                                        Please enter a valid email address.
                                                    </div>
                                                </div>
                                                <!--<div class="col-md-2" *ngIf="verifyPopup && !verifyPopup.verifyEmailCodeSend && newPatient.controls.pemail.value !='' &&(newPatient.controls.pemail.valid || newPatient.controls.pemail.touched) && userData.config.enable_verification_of_cell_and_mobile == 1" >
                                                    <button type="button" class="btn btn-primary btn-sm" *ngIf="!newPatient.controls.emailVerified.value"(click)="verifyPopup.show_popup({email:true,formData:newPatient.value})">Verify</button>
                                                    <span class="btn btn-primary btn-sm" *ngIf="newPatient.controls.emailVerified.value">Verified</span>
                                                </div>-->
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row"
                                        *ngIf="newPatient.controls['utype'].value == 'partner'&& showAuxilaryPhysicianOrNursingLabel == true">
                                        <label class="col-md-3 control-label">{{auxilaryPhysicianOrNursingLabel}} <i chToolTip="prescriberOrNursingId"></i></label>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control" id="partnerId" name="partnerId"
                                                placeholder={{auxilaryPhysicianOrNursingLabel}} [formControl]="newPatient.controls['partnerId']">
                                            <div class="alert alert-danger"
                                                *ngIf="!newPatient.controls.partnerId.valid && (newPatient.controls.partnerId.dirty || newPatient.controls.partnerId.touched || f.submitted)">
                                                {{auxilaryPhysicianOrNursingLabel}} cannot be empty.
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group row" *ngIf="userData.config.enable_appless_model == 1">
                                        <label class="col-md-3 control-label">Organization</label>
                                        <div class="col-md-6">
                                            <input type="text"
                                                class="form-control" id="companyNursingAgency"
                                                name="companyNursingAgency"
                                                placeholder="Organization"
                                                [formControl]="newPatient.controls['companyNursingAgency']">
                                        </div>
                                    </div>
                                </div>
                                <button [disabled]="disableSubmit" class="btn btn-info">Submit</button>
                                <a class="btn btn-secondary" (click)="cancelAdd()">Cancel</a>
                                <!-- Partner Form End -->
                            </div>
                        </div>
                    </form>
                   <!---alter dats-->
                <div class="tab-pane" [ngClass]="{ active: activePatientTab=='alternate-contacts' }" id="tabAlternateContacts"
                    (click)="activePatientTab='alternate-contacts';patientEditTabs('alternate-contacts');" role="tabcard"
                    aria-expanded="false">


                    <!-- START: tables/datatables -->
                    <section class="card">
                        <div class="card-header">
                            <span class="cat__core__title">

                                <a class="pull-right btn btn-sm btn-primary" id="add_alternate" (click)="gotoAlternate()">
                                    Add Alternate Contact<i class="ml-1"></i></a>
                            </span>
                        </div>
                        <table class="table table-hover" id="alternate-contact-list" width="100%">
                            <thead class="thead-light">
                                <tr *ngIf="alernateContactsData.length!=0">
                                    <th class="th-col-style1">First Name</th>
                                    <th class="th-col-style1">Last Name</th>
                                    <th class="th-col-style1">Email</th>
                                    <th class="th-col-style1">Relation</th>
                                    <th class="th-col-style1">Enrollment Status</th>
                                    <th class="th-col-style1">Status</th>
                                    <th class="th-col-style1" *ngIf="alternate_flag!= 1">Actions</th>
                                </tr>
                            </thead>
                            <tr *ngFor="let item of alernateContactsData; let i = index" id="{{i}}">

                                <td class="th-col-style2">{{item.firstName}} </td>
                                <td class="th-col-style2">{{item.lastName}}</td>
                                <td class="th-col-style2">{{item.email}}</td>
                                <td class="th-col-style2">{{item.relation}}</td>
                                <td class="th-col-style2">{{((item.isVirtual == true) ? 'Virtual' : 'Enrolled')}}</td>
                                <td class="th-col-style2 display-flex" id="stat{{item.contactId}}"> {{ getStatus(item.status) | translate }}
                                    <span
                                        class="user-not-contactable-outline bg-danger"
                                        *ngIf="item.isContactNotOptedIn"
                                        data-toggle="tooltip"
                                        data-placement="top"
                                        chToolTip="noContactInfoOrOptedOut"
                                    ></span>
                                </td>
                                <td >
                                    <div class="btn-group mr-2 mb-2 no-margin">
                                         <a class="pull-right btn btn-sm " title="Edit Alternate Contact" id="EditcontactClick" (click)="navigateToNext('add-alternate-contact/'+item.contactId)"><i id="invitePatientClick"  class="fa fa-pencil"></i></a>
                                        <span *ngIf="item.status == 1" > <a class="pull-right btn btn-sm " title="Inactivate" (click)="AlternateInactivate(item.contactId,'2',i)"><i id="invitePatientClick"  class="fa fa-ban"></i></a> </span>
                                        <span *ngIf="item.status == 2" > <a class="pull-right btn btn-sm " title="Activate" (click)="AlternateInactivate(item.contactId,'1',i)"><i id="activatePatientClick"  class="fa fa-check"></i></a> </span>
                                        <span *ngIf="item.status != 3" > <a class="pull-right btn btn-sm " title="Delete" (click)="AlternateDelete(item.contactId,i)"><i id="activatePatientClick"  class="fa fa-trash"></i></a> </span>
                                         <span *ngIf="item.isVirtual == true" > <a class="pull-right btn btn-sm iac-icon" title="Invite" (click)="AlternateInvite(item,editId)"> <img id="inviteRelativesClick" data-id="{{item.contactId}}" class="iac-icon" src="./assets/img/invite-auto-enrollment.png"/></a></span>
                                    </div>
                                </td>

                            </tr>
                            <tr *ngIf="alernateContactsData.length==0">
                                <td colspan="6" style="text-align: center;">No Alternate Contacts available now.</td>
                            </tr>
                        </table>
                    </section>
                </div>

                     <!---alter dats end-->
                </div>


                </div>

            </div>
        </div>
    </div>
</section>
<!-- END: tables/datatables -->
<ch-loader [showLoader]="!hideSiteSelection" [showInPageCenter]="true" *ngIf="userData.config.enable_multisite === '1' && userData.mySites.length > 1"></ch-loader>
