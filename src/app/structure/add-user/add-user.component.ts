import { <PERSON>mponent, <PERSON><PERSON>nit, <PERSON>ement<PERSON>ef, <PERSON><PERSON>hild, <PERSON><PERSON><PERSON>, HostListener } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ValidatorFn, AbstractControl, FormControl, FormGroupDirective } from '@angular/forms';
import { isBlank, setCountryCodeFlag } from 'app/utils/utils';
import { NGXLogger } from 'ngx-logger';
import { Subject } from 'rxjs';
import { CONSTANTS, ConfigStatus, DateFormat, DefaultPatientWorkFlow, PATTERNS, UserGroup } from 'app/constants/constants';
import { defaultCountryFlag } from 'environments/environment';
import { AddUserService } from './adduser.service';
import { SharedService } from '../../structure/shared/sharedServices';
import { GlobalDataShareService } from '../../structure/shared/global-data-share.service';
import { RegistrationService } from '../../structure/registration/registration.service';
import { VerifyEmComponent } from '../shared/verifyEM/verify-em.component';
import { ToolTipService } from '../tool-tip.service';
import { StructureService } from '../structure.service';
import { userFilterPipe } from '../inbox/inbox-modal-filter';

declare const $: any;
declare const moment: any;
declare const swal: any;
declare const NProgress: any;

@Component({
  selector: 'app-add-user',
  templateUrl: './add-user.component.html',
  styleUrls: ['./add-user.component.css']
})
export class AddUserComponent implements OnInit {
  @ViewChild('passwordField') passwordField: ElementRef;
  onEditUTypeLoad = false;
  onEditLoad = false;
  onEditUserTagLoad = false;
  onEditPFNameLoad = false;
  onEditPLNameLoad = false;
  onEditUseMRNLoad = false;
  onEditDobLoad = false;
  showDisabled = false;
  onEditMRNLoad = false;
  patientTags: any = [];
  partnerTags: any = [];
  selectedTagIds: any = [];
  patientInfo = [];
  staffTags: any = [];
  enableNext: boolean;
  isNavigate = false;
  editId = '';
  partnerRoleTitle: any;
  referralCodes: any;
  pageNumber = 0;
  selectedObj;
  userCountryCode;
  activePatientTab;
  puser;
  userMobile;
  alternatemrn;
  duplicateUsers = [];
  disableMRNPage = false;
  isEnrolledPatient = true;
  configUser: any;
  config: any;
  staffenroll = 0;
  patientenroll = 0;
  selectFlag = false;
  enrollData;
  showInInviteRecord: any;
  showAuxilaryPhysicianOrNursingLabel = false;
  auxilaryPhysicianOrNursingLabel: any;
  partnerCategory: any = [];
  partnerenroll = 0;
  staffRoles: any = [];
  partnerRoles: any = [];
  dob: any;
  tabSelect;
  disableSubmit = false;
  extInegration: any = [];
  userCountry = defaultCountryFlag;
  initialUserCountry = defaultCountryFlag;
  warning_msg = '';
  referelTknSet = false;
  newPatient: FormGroup;
  defaultTenantDetails;
  selectedTenantDetails;
  userData;
  patientList: any;

  manageConfig: any = {};
  responsePatient: any = [];
  responseStaff: any = [];
  responsePartner: any = [];
  pageTitle = 'Add User';
  pageTitleBreadcrumbs = 'Add Users';
  word: any;
  alternateId;
  stat = 1;
  submitted: boolean;
  siteChoosed: boolean;
  userInfo = {
    token: '',
    firstName: '',
    middleName: '',
    lastName: '',
    email: '',
    mobile: '',
    birthDate: '',
    sex: 'male',
    zipCode: '',
    address: '',
    city: '',
    district: '',
    state: '',
    countryCode: '+1',
    staffId: '',
    companyNursingAgency: ''
  };

  userListDisaplay: any[];
  tagLoading = false;
  removeArray = [];
  userTypes;
  alternate_flag = '0';
  selectedTags: any = [];
  selectedTagNames: any = [];
  stafffillfirst = false;
  tagtypeList: any = [];
  usersList = [];
  removeArr: any = [];
  dataResponseUser: any;
  registrationToken;
  registrationTenantId;
  patientRoleId;
  alernateContactsData: any = [];

  show_button = false;
  show_eye = false;
  verifiedEmailOrMobile = null;
  hideCaregiverSingup = false;
  onEditCareGiverSignupLoad = false;
  buttonDisabled: any;
  showLoading: any;
  errorCount: any;
  domainName;
  signDocFile;
  formData;
  fileChangevalue;
  result;
  importResult: any;
  siteId: any;
  homeSiteId: any;
  hideSiteSelection: boolean;
  singleSelection: boolean;
  siteRequired: boolean;
  eventsSubject: Subject<void> = new Subject<void>();
  editSiteData = '';
  filterType = false;
  enableCreatePasswordOption = false;
  readonly maskPhoneNumber = CONSTANTS.phoneMask;

  @ViewChild(VerifyEmComponent) verifyPopup: VerifyEmComponent;
  @ViewChild(FormGroupDirective) formGroupDirective: FormGroupDirective;
  @HostListener('document:keypress', ['$event'])
  onKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      event.preventDefault();
    }
  }
  constructor(
    public _structureService: StructureService,
    private _enrollService: AddUserService,
    private router: Router,
    private _sharedService: SharedService,
    private route: ActivatedRoute,
    public _GlobalDataShareService: GlobalDataShareService,
    private elementRef: ElementRef,
    private renderer: Renderer,
    private registrationservice: RegistrationService,
    private fb: FormBuilder,
    private logger: NGXLogger,
    private toolTipService: ToolTipService,
    public modalFilter: userFilterPipe
  ) {
    this.manageConfig = JSON.parse(this._structureService.userDataConfig);
    this.formData = this.fb.group({
      file: null
    });
    renderer.listen(elementRef.nativeElement, 'click', (event) => {
      $('#tagsInput').text('');
      this.logger.info('event.target', event.target);
      this.logger.info('event', event);
      if (
        event.target.id === 'enable-alternative' &&
        isBlank(this.editId) &&
        this.userData.config.default_patients_workflow === DefaultPatientWorkFlow.ALTERNATE_CONTACTS
      ) {
        $(document).ready(() => {
          $('.nav li').find('#disable-alternative').addClass('disabled');
          $('.nav li').find('#disable-alternative').find('a').removeAttr('data-toggle');
        });
      }
      const idDetails = [
        'associate-search-input',
        'associate-li',
        'associate-ul',
        'associate-search',
        'tagsInput',
        'recipient-li',
        'recipient-ul',
        'recipient-search'
      ];
      if (!idDetails.includes(event.target.id) && event.target.className.indexOf('recipient-li') === -1) {
        const clear = false;
        let from = '';

        if ($('#recipient-ul').css('display') === 'block') {
          $('#tagsInput').val('');
          from = 'R';
        }
        this.enableOrDisableUiLI(false, clear, from);
        this.logger.info('Condition1');
      } else {
        if (event.target.id === 'associate-search-input' && $('#recipient-ul').css('display') === 'block') {
          this.enableOrDisableUiLI(false, false, 'R');
        } else if (event.target.id === 'tagsInput' && $('#associate-ul').css('display') === 'block') {
          this.enableOrDisableUiLI(false, false);
        }
        this.logger.info('Condition2');
      }
      // Show/hide password  tool-tip change
      if (event.target.id === 'toggle_icon') {
        if (event.target.className === 'fa fa-eye show-password') {
          $('#toggle_icon').attr('data-original-title', 'Hide temporary password').tooltip('show');
        } else {
          $('#toggle_icon').attr('data-original-title', 'Show temporary password').tooltip('show');
        }
      }
      // End of show/hide password tool-tip change
    });

    if (isBlank(this.verifiedEmailOrMobile)) {
      this.verifiedEmailOrMobile = this._sharedService.mobileOrEmailVerified.subscribe((data) => {
        if (data.mobile) {
          this.newPatient.patchValue({
            mobileVerified: true
          });
        } else if (data.email) {
          this.newPatient.patchValue({
            emailVerified: true
          });
        }
      });
    }

    this.userData = JSON.parse(this._structureService.userDetails);

    this.registrationToken = this.userData.config.token;

    route.params.subscribe(() => {
      if (this.route.snapshot.params['id']) {
        this.editId = this.route.snapshot.params['id'];
        localStorage.setItem('patientId', this.editId);
        localStorage.setItem('returnFromAlternateContact', '');

        if (
          this.userData.config.default_patients_workflow === DefaultPatientWorkFlow.ALTERNATE_CONTACTS &&
          localStorage.getItem('showEditdisabled') === '1'
        ) {
          this.showDisabled = true;
          this.logger.info(this.showDisabled);
        }
      } else {
        this._sharedService.showAlternateTab = false;
        this.patientEditTabs('details');
      }
    });
    this.pageTitle = this.toolTipService.getTranslateData('TITLES.ADD_USER');
    this.pageTitleBreadcrumbs = this.toolTipService.getTranslateData('BREADCRUMBS.ADD_USERS');
  }

  password(formGroup: FormGroup) {
    const { value: password } = formGroup.get('temppassword');
    const { value: confirmPassword } = formGroup.get('repeatPassword');
    return password === confirmPassword
      ? null
      : {
          passwordNotMatch: true
        };
  }

  ngOnDestroy() {
    this._structureService.unSubsc();
  }

  ngOnInit() {
    this.emitEventToSelectSites();
    let tab = localStorage.getItem('addUserTab');
    this.logger.info('config details');
    this.alternate_flag = localStorage.getItem('contactAdded');
    this.logger.info('check flag val');
    this.logger.info(this.userData.config.default_patients_workflow);
    localStorage.setItem('contactAdded', '');
    if (this._sharedService.showAlternateTab && tab === CONSTANTS.userTypes.patient) {
      this.activePatientTab = DefaultPatientWorkFlow.ALTERNATE_CONTACTS;
    } else {
      this.activePatientTab = 'details';
      if (this.userData.config.default_patients_workflow === DefaultPatientWorkFlow.ALTERNATE_CONTACTS && isBlank(this.editId)) {
        $(document).ready(() => {
          $('.nav li').find('#disable-alternative').addClass('disabled');
          $('.nav li').find('#disable-alternative').find('a').removeAttr('data-toggle');
        });
      }
    }

    if (!tab) {
      if (this.userData.privileges.includes('allowAddUser') && this.userData.privileges.includes('addVirtualEnrolledStaffUsers')) {
        tab = CONSTANTS.userTypes.staff;
      } else if (this.userData.privileges.includes('allowAddUser') && this.userData.privileges.includes('addVirtualPartnerUsers')) {
        tab = CONSTANTS.userTypes.partner;
      }
    }

    if (tab === CONSTANTS.userTypes.patient) {
      this.onEditUTypeLoad = true;
      if (this.userData.config.default_patients_workflow === DefaultPatientWorkFlow.ALTERNATE_CONTACTS && !isBlank(this.editId)) {
        localStorage.setItem('Addclciked', '');
        const request = {
          tenantId: this.userData.tenantId,
          patientId: this.editId || ''
        };
        this._structureService.getAllalternateContactByPatientId(request).then((response: any) => {
          if (response.data && response.data.length) {
            this.modalFilter.transform(response.data).forEach((e) => {
              if (!e.email.includes('unknown_')) {
                e.email = this.IsEmail(e.email);
              } else {
                e.email = '';
              }
              this.alernateContactsData.push({
                firstName: e.firstName,
                lastName: e.lastName,
                email: e.email,
                mobile: e.mobile,
                countryCode: e.countryCode,
                password: e.password,
                ESIValue: e.ESIValue,
                isVirtual: e.isVirtual,
                status: e.status,
                contactId: e.contactId,
                relation: e.relation,
                modified_at: e.modified_at,
                created_at: e.created_at,
                patientFirstName: e.patientFirstName,
                patientLastName: e.patientLastName,
                patientStatus: e.patientStatus,
                alternateUsername: e.alternateUsername,
                isContactNotOptedIn: e.isContactNotOptedIn
              });
            });
          }
        });
      }
    } else {
      this.onEditUTypeLoad = false;
    }

    this.tabSelect = tab;
    this.singleSelection = this.tabSelect === 'patient';
    this.newPatient = new FormGroup(
      {
        utype: new FormControl({
          value: this.tabSelect,
          disabled: false
        }),
        userTag: new FormControl({
          value: '',
          disabled: false
        }),
        tags: new FormControl({
          value: '',
          disabled: false
        }),
        staffTag: new FormControl({
          value: '',
          disabled: false
        }),
        partnerTag: new FormControl({
          value: '',
          disabled: false
        }),
        use_mrn: new FormControl({
          value: false,
          disabled: false
        }),
        puid: new FormControl({
          value: '',
          disabled: false
        }),
        pfname: new FormControl(
          {
            value: '',
            disabled: false
          },
          Validators.required
        ),
        plname: new FormControl(
          {
            value: '',
            disabled: false
          },
          Validators.required
        ),
        userRole: new FormControl({
          value: '',
          disabled: false
        }),
        mrnSys: new FormControl({
          value: '',
          disabled: false
        }),
        mrnSysCode: new FormControl({
          value: '',
          disabled: true
        }),
        mrn: new FormControl({
          value: '',
          disabled: false
        }),

        pemail: new FormControl(
          {
            value: '',
            disabled: false
          },
          [
            Validators.pattern(
              /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
            )
          ]
        ),
        zipcode: new FormControl({
          value: '',
          disabled: false
        }),
        pcellno: new FormControl({
          value: '',
          disabled: false
        }),
        countryCode: new FormControl(
          {
            value: '',
            disabled: false
          },
          [Validators.required, Validators.pattern(/^\+\d+$/)]
        ),
        dob: new FormControl({
          value: '',
          disabled: false
        }),
        // Partner Fields
        partnerRole: new FormControl({
          value: '',
          disabled: false
        }),
        partnerId: new FormControl({ value: '', disabled: false }),
        partnerCategory: new FormControl({ value: '', disabled: false }),
        patientEmail: new FormControl(
          {
            value: '',
            disabled: false
          },
          [
            Validators.pattern(
              /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
            )
          ]
        ),
        patientMobile: new FormControl(),
        companyNursingAgency: new FormControl({
          value: '',
          disabled: false
        }),
        createPassword: new FormControl({
          value: '',
          disabled: false
        }),
        temppassword: new FormControl({
          value: '',
          disabled: false
        }),
        repeatPassword: new FormControl({
          value: '',
          disabled: false
        }),
        mobileVerified: new FormControl({
          value: false,
          disabled: false
        }),
        emailVerified: new FormControl({
          value: false,
          disabled: false
        }),
        patientUsername: new FormControl(
          {
            value: '',
            disabled: false
          },
          [
            Validators.pattern(
              /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
            )
          ]
        ),
        checkboxEmailUsernamePartner: new FormControl({
          checked: true
        }),
        enableSmsNotifications: new FormControl({
          value: 1,
          disabled: false
        }),
        enableEmailNotifications: new FormControl({
          value: 1,
          disabled: false
        })
      },
      {
        validators: Validators.compose([
          this.password.bind(this),
          this.oktaPasswordValidator.bind(this)
        ])
      }
    );

    if (tab === 'staff') {
      this.newPatient.get('userRole').setValidators([Validators.required]);
      this.newPatient.get('userRole').updateValueAndValidity();

      // Add mandatory validation for Staff ID when the configurations are enabled
      if (
        !isBlank(this.manageConfig.make_staffid_field_mandatory_in_staff_invite_page) &&
        this.manageConfig.make_staffid_field_mandatory_in_staff_invite_page === ConfigStatus.ENABLED &&
        !isBlank(this.manageConfig.show_staffid_in_user_invite) &&
        this.manageConfig.show_staffid_in_user_invite === ConfigStatus.ENABLED
      ) {
        this.newPatient.get('mrn').setValidators([Validators.required]);
      }
    } else if (tab === 'partner') {
      this.newPatient.get('partnerRole').setValidators([Validators.required]);
      this.newPatient.get('partnerRole').updateValueAndValidity();
    } else if (tab === 'patient' && !isBlank(this.editId)) {
      if (
        this._sharedService.formdataWithAlternate.length !== 0 &&
        this.userData.config.default_patients_workflow === DefaultPatientWorkFlow.ALTERNATE_CONTACTS &&
        tab === 'patient'
      ) {
        this.oneditInvite();
      }
    }

    setTimeout(function () {
      $('body').tooltip({ selector: '[data-toggle="tooltip"]' });
      $('[data-toggle="tooltip"]').tooltip();
    }, 100);

    let self: any;

    this.defaultTenantDetails = JSON.parse(this._structureService.userDetails);
    this.selectedTenantDetails = this._GlobalDataShareService.getselectedTenantDetails();
    const arrayOfElements = [];
    const disableTrainMe = this.newPatient.get('utype').value === CONSTANTS.userTypes.patient;
    this._structureService.introJsBtnHidden = false;
    this._structureService.setIntroOptions('add-user,register', arrayOfElements, disableTrainMe, () => {
      delete this._structureService.introSteps;
      this._structureService.introSteps = JSON.parse(JSON.stringify(this._structureService.introStepsCopy));
      this._sharedService.startIntroCallBack.emit({
        value: true
      });

      this._structureService.introJsObject.oncomplete(() => {
        this._structureService.introJsObject.exit();
        delete this._structureService.introJsObject;
        delete this._structureService.introSteps;
      });

      this._structureService.introJsObject.onexit(() => {
        delete this._structureService.introJsObject;
        delete this._structureService.introSteps;
      });
    });
    const userDetails = this._structureService.getUserdata();
    this._structureService.getUser(userDetails.userId).then((data) => {
      if (data['staffUsers']) {
        if (data['staffUsers'].length > 0) {
          const user = data['staffUsers'][0];
          this.userCountryCode = user.countryCode;
          this.userMobile = user.mobile;
        }
      }
    });

    this._enrollService.getTenantRolesByPrivilege('allowEnrollmentInitiation').then((data: any) => {
      this.staffRoles = data.filter((a) => +a.citus_role_id !== UserGroup.PARTNER);
      this.partnerRoles = data.filter((a) => +a.citus_role_id === UserGroup.PARTNER);
    });
    this._structureService.getTenantUserCategories(this._structureService.getCookie('crossTenantId'), 'invite').then((data: any) => {
      this.logger.info(' partnerCategory', data);
      this.partnerCategory = data;
    });
    // TODO: esi_code_for_patient_identity decommisioned, remove usage if it is not causing any issue
    this._structureService.getExternalSystems().then((data) => {
      this.extInegration = data['getSessionTenant']['externalIntegrationSystems'];
      const showInInvite = this.extInegration.filter((rec) => {
        if (rec.externalSystemId.toString() === this.userData.config.esi_code_for_alternate_contact.toString()) {
          this.alternatemrn = rec.labelShowInEditPage;
        }
        return rec.externalSystemId.toString() === this.userData.config.esi_code_for_patient_identity.toString();
      });

      this.newPatient.patchValue({
        use_mrn: false
      });

      if (showInInvite.length) {
        this.showInInviteRecord = showInInvite[0];
        this.newPatient.patchValue({
          mrnSys: this.showInInviteRecord.externalSystemId,
          mrnSysCode: this.showInInviteRecord.code
        });
      }

      if (this.newPatient.get('utype').value === CONSTANTS.userTypes.patient) {
        if (this._structureService.isMRNFieldMandatory) {
          this.newPatient.get('mrn').setValidators([Validators.required]);
          this.newPatient.get('mrn').updateValueAndValidity();
          this.newPatient.patchValue({
            use_mrn: true,
            mrnSys: this.showInInviteRecord ? this.showInInviteRecord.externalSystemId : ''
          });
        } else {
          this.newPatient.patchValue({
            mrnSys: this.showInInviteRecord ? this.showInInviteRecord.externalSystemId : ''
          });
          this.newPatient.get('mrn').clearValidators();
          this.newPatient.get('mrn').updateValueAndValidity();
        }
      }
    });

    this.newPatient.get('utype').valueChanges.subscribe((val) => {
      this.togglePreference('createPassword', false);
      $('#email-address').val('');

      this.newPatient.get('pfname').clearValidators();
      this.newPatient.get('pfname').setValidators([Validators.required]);
      this.newPatient.get('pfname').updateValueAndValidity();
      this.newPatient.get('plname').clearValidators();
      this.newPatient.get('plname').setValidators([Validators.required]);
      this.newPatient.get('plname').updateValueAndValidity();

      if (val === 'staff') {
        this.newPatient.get('userRole').setValidators([Validators.required]);
        this.newPatient.get('userRole').updateValueAndValidity();
        this.newPatient.get('mrnSys').clearValidators();
        this.newPatient.get('mrnSys').updateValueAndValidity();
        if (
          this.userData.config.make_staffid_field_mandatory_in_staff_invite_page &&
          +this.userData.config.make_staffid_field_mandatory_in_staff_invite_page === 1
        ) {
          this.newPatient.get('mrn').clearValidators();
          this.newPatient.get('mrn').setValidators([Validators.required]);
          this.newPatient.get('mrn').updateValueAndValidity();
        } else {
          this.newPatient.get('mrn').clearValidators();
          this.newPatient.get('mrn').updateValueAndValidity();
        }
        this.newPatient.get('dob').clearValidators();
        this.newPatient.get('dob').updateValueAndValidity();

        this.newPatient.get('partnerRole').clearValidators();
        this.newPatient.get('partnerRole').updateValueAndValidity();

        this.newPatient.get('createPassword').clearValidators();
        this.newPatient.get('createPassword').updateValueAndValidity();

        this.newPatient.get('temppassword').clearValidators();
        this.newPatient.get('temppassword').updateValueAndValidity();

        this.newPatient.get('repeatPassword').clearValidators();
        this.newPatient.get('repeatPassword').updateValueAndValidity();

        setTimeout(() => {
          $('select[name="userRole"]').select2({
            placeholder: $('select[name="userRole"] option:first').text()
          });
        }, 100);
        this.newPatient.patchValue({
          use_mrn: false,
          userTag: '',
          partnerTag: '',
          staffTag: '',
          tags: []
        });
      } else if (val === 'patient') {
        this.newPatient.get('userRole').clearValidators();
        this.newPatient.get('userRole').updateValueAndValidity();
        this.newPatient.get('partnerRole').clearValidators();
        this.newPatient.get('partnerRole').updateValueAndValidity();

        this.newPatient.get('createPassword').clearValidators();
        this.newPatient.get('createPassword').updateValueAndValidity();

        this.newPatient.get('temppassword').clearValidators();
        this.newPatient.get('temppassword').updateValueAndValidity();

        this.newPatient.get('repeatPassword').clearValidators();
        this.newPatient.get('repeatPassword').updateValueAndValidity();

        this.newPatient.patchValue({
          use_mrn: false,
          userTag: '',
          partnerTag: '',
          staffTag: '',
          tags: []
        });

        setTimeout(() => {
          $('select[name="userTag"]').select2({
            placeholder: $('select[name="userTag"] option:first').text()
          });

          $('select[name="partnerTag"]').select2({
            placeholder: $('select[name="partnerTag"] option:first').text()
          });

          $('select[name="staffTag"]').select2({
            placeholder: $('select[name="staffTag"] option:first').text()
          });

          $('#dob-date-picker').combodate({
            format: 'YYYY-MM-DD',
            template: 'MMM D YYYY',
            minYear: 1900,
            firstItem: 'empty',
            maxYear: new Date().getFullYear(),
            customClass: 'form-control select2 dob-sec',
            smartDays: true
          });

          $('.month, .day, .year').select2();
        }, 100);
      } else {
        this.newPatient.get('userRole').clearValidators();
        this.newPatient.get('userRole').updateValueAndValidity();
        this.newPatient.get('mrnSys').clearValidators();
        this.newPatient.get('mrnSys').updateValueAndValidity();
        this.newPatient.get('mrn').clearValidators();
        this.newPatient.get('mrn').updateValueAndValidity();
        this.newPatient.get('dob').clearValidators();
        this.newPatient.get('dob').updateValueAndValidity();
        this.newPatient.get('partnerRole').setValidators([Validators.required]);
        this.newPatient.get('partnerRole').updateValueAndValidity();

        this.newPatient.get('createPassword').clearValidators();
        this.newPatient.get('createPassword').updateValueAndValidity();

        this.newPatient.get('temppassword').clearValidators();
        this.newPatient.get('temppassword').updateValueAndValidity();

        this.newPatient.get('repeatPassword').clearValidators();
        this.newPatient.get('repeatPassword').updateValueAndValidity();

        setTimeout(() => {
          $('select[name="partnerRole"]').select2({
            placeholder: $('select[name="partnerRole"] option:first').text()
          });
        }, 100);
      }
      this.newPatient.get('userRole').updateValueAndValidity();
      setTimeout(() => {
        $('#country_code').intlTelInput();

        $('body').off('keyup', '#email-address');
        $('body').on('keyup', '#email-address', () => {
          this.newPatient.patchValue({
            pemail: $('#email-address').val()
          });
        });

        $('body').off('countrychange', '#country_code');
        $('body').on('countrychange', '#country_code', (e, countryData) => {
          if (countryData.dialCode) {
            this.userCountry = countryData.iso2;
            this.userInfo.countryCode = `+${countryData.dialCode}`;
            this.newPatient.patchValue({
              countryCode: this.userInfo.countryCode
            });
            $('#country_code').intlTelInput('setNumber', `+${countryData.dialCode}`);
          }
        });

        $('#country_code').intlTelInput('setCountry', this.initialUserCountry);
        const countryDetails = $('#country_code').intlTelInput('getSelectedCountryData');
        this.userInfo.countryCode = `+${countryDetails.dialCode}`;
        $('#country_code').intlTelInput('setNumber', this.userInfo.countryCode);
        this.newPatient.patchValue({
          countryCode: this.userInfo.countryCode
        });

        $('#dob-date-picker').combodate({
          format: 'YYYY-MM-DD',
          template: 'MMM D YYYY',
          minYear: 1900,
          firstItem: 'empty',
          maxYear: new Date().getFullYear(),
          customClass: 'form-control select2 dob-sec',
          smartDays: true
        });

        $('.month, .day, .year').select2();

        $('select[name="userRole"]').select2({
          placeholder: $('select[name="userRole"] option:first').text()
        });
      }, 100);
    });

    setTimeout(() => {
      if (this.newPatient.get('utype').value === 'partner') {
        $('select[name="partnerRole"]').select2({
          placeholder: $('select[name="partnerRole"] option:first').text()
        });
      }
      $('body').on('keyup', '#email-address', () => {
        this.newPatient.patchValue({
          pemail: $('#email-address').val()
        });
      });

      $('#country_code').intlTelInput();
      $('body').on('countrychange', '#country_code', (e, countryData) => {
        if (countryData.dialCode) {
          this.userCountry = countryData.iso2;
          this.userInfo.countryCode = `+${countryData.dialCode}`;
          this.newPatient.patchValue({
            countryCode: this.userInfo.countryCode
          });
          $('#country_code').intlTelInput('setNumber', `+${countryData.dialCode}`);
        }
      });

      $('#country_code').intlTelInput('setCountry', this.initialUserCountry);
      const cntryDetails = $('#country_code').intlTelInput('getSelectedCountryData');
      this.userInfo.countryCode = `+${cntryDetails.dialCode}`;
      $('#country_code').intlTelInput('setNumber', this.userInfo.countryCode);
      this.newPatient.patchValue({
        countryCode: this.userInfo.countryCode
      });

      $('#tags').on('click', 'span.remove', (e) => {
        this.logger.info($(e.target).attr('id'));
        this.removeSelectedTag($(e.target).attr('id'));
      });

      $(function () {
        $('#tagsInput').on({
          click() {
            const txt = this.value; // .replace(/[^a-z0-9\+\-\.\#]/ig,''); // allowed characters
            if (txt) {
              /** @TODO: Fix below error, `this` references tagsInput element not Add User Component
               * this.logger.info('Enter focusin function....');
               */

              // @TODO: Review below, self may not refer to component either
              self.tagInputFocus();
            }
          }
        });
      });

      $('#dob-date-picker').combodate({
        format: DateFormat.YYMMDD_FORMAT_HYPHEN,
        template: 'MMM D YYYY',
        minYear: 1900,
        firstItem: 'empty',
        maxYear: new Date().getFullYear(),
        customClass: 'form-control select2 dob-sec',
        smartDays: true
      });

      $('.month, .day, .year').select2();
      $('select[name="userRole"]').select2({
        placeholder: $('select[name="userRole"] option:first').text()
      });
      $('select[name="userTag"]').select2({
        placeholder: $('select[name="userTag"] option:first').text()
      });
      $('select[name="partnerTag"]').select2({
        placeholder: $('select[name="partnerTag"] option:first').text()
      });
      $('select[name="staffTag"]').select2({
        placeholder: $('select[name="staffTag"] option:first').text()
      });
    }, 100);

    $('body').on('change', '#dob-date-picker', () => {
      if (!isBlank($('#dob-date-picker').val())) {
        this.dob = moment($('#dob-date-picker').val()).isValid() ? moment($('#dob-date-picker').val()).format('ll') : '';
        this.newPatient.patchValue({
          dob: this.dob
        });
      }
    });
    $('body').on('select2:select', 'select[name="mrnSys"]', (e) => {
      const { data } = e.params;
      this.newPatient.patchValue({
        mrnSys: data.id,
        mrnSysCode: data.id
      });
    });
    $('body').on('select2:select', 'select[name="userRole"]', (e) => {
      const { data } = e.params;
      this.newPatient.patchValue({
        userRole: data.id
      });
    });

    $('body').on('change', 'select[name="userTag"]', () => {
      this.newPatient.patchValue({
        userTag: $('select[name="userTag"]').val()
      });
    });
    $('body').on('change', 'select[name="partnerTag"]', () => {
      this.newPatient.patchValue({
        partnerTag: $('select[name="partnerTag"]').val()
      });
    });
    $('body').on('change', 'select[name="staffTag"]', () => {
      this.newPatient.patchValue({
        staffTag: $('select[name="staffTag"]').val()
      });
    });

    $('body').on('select2:select', 'select[name="partnerRole"]', (e) => {
      const { data } = e.params;
      this.partnerRoleTitle = data.text.trim();
      this.newPatient.patchValue({
        partnerRole: data.id,
        partner_role_behalf: false
      });
      this.enableNext = false;

      setTimeout(() => {
        $('#country_code').intlTelInput();
        $('#country_code').intlTelInput('setCountry', this.initialUserCountry);
        const countryDetails = $('#country_code').intlTelInput('getSelectedCountryData');
        this.userInfo.countryCode = `+${countryDetails.dialCode}`;
        $('#country_code').intlTelInput('setNumber', this.userInfo.countryCode);
        this.newPatient.patchValue({
          countryCode: this.userInfo.countryCode
        });
      }, 100);
    });

    $('body').on('keyup', '#country_code', () => {
      this.userInfo.countryCode = $('#country_code').val();
      this.newPatient.patchValue({
        countryCode: this.userInfo.countryCode
      });
    });

    $('html').bind('input', function (event) {
      if (event.target.id === 'email-address') {
        $('#email-address').trigger('keyup');
      } else if (event.target.id === 'us-phone-mask-input') {
        $('#us-phone-mask-input').trigger('keyup');
      }
    });

    setTimeout(() => {
      $('#country_code').on('change paste keyup', () => {
        const regex = /^\+?[0-9]*$/;
        if ($('#country_code').val()) {
          if (!regex.test($('#country_code').val())) {
            const countryDetails = $('#country_code').intlTelInput('getSelectedCountryData');
            $('#country_code').intlTelInput('setNumber', `+${countryDetails.dialCode}`);
            this.userInfo.countryCode = `+${countryDetails.dialCode}`;
          }
        }
      });

      setTimeout(() => {
        if (this.tabSelect === 'patient') {
          this.isNavigate = true;
        }
      }, 1500);
    }, 1000);

    if (this.editId !== '') {
      this.oneditInvite();
    } else {
      this.editSiteData = '';
      this.filterType = false;
      localStorage.setItem('isCareGiver', '');
    }
    setTimeout(() => {
      $('#mrn').keypress(function (e) {
        if (e.which === 32 && !this.value.length) e.preventDefault();
      });
    }, 2000);
  }
  patientEditTabs(requestParams) {
    this.activePatientTab = requestParams;
  }
  AlternateInactivate(alternateId, status, index) {
    let changeStat = '';
    if (+status === 1) {
      changeStat = 'Active';
    } else {
      changeStat = 'Inactive';
    }
    this._structureService.showAlertMessagePopup({
        text: this.toolTipService.getTranslateData('MESSAGES.CONFIRM_ALTERNATE_CONTACT_STATUS_CHANGE'),
        type: CONSTANTS.notificationTypes.warning
      })
      .then((confirm) => {
        if (confirm) {
          const request = {
            contactId: alternateId,
            tenantId: this.userData.tenantId,
            status
          };
          this._structureService.AlternatecontactChangestatus(request).then((response: any) => {
            this.logger.info('check alternate contacts status');
            this.logger.info(response);
            if (response.status != null) {
              this._structureService.notifyMessage({
                type: CONSTANTS.notificationTypes.success,
                messge: this.toolTipService.getTranslateData('SUCCESS_MESSAGES.SUCCESSFULLY_CHANGED_ALTERNATE_CONTACT_STATUS')
              });
              const activityData = {
                activityName: `${changeStat}Alternate contact`,
                activityType: 'manage alternate contacts',
                activityDescription: `${this.userData.displayName} (User-${this.userData.userId}) deleted the Alternate contact with user id ${alternateId}`
              };
              this._structureService.trackActivity(activityData);
              this.alernateContactsData[index].status = status;
            } else {
              this._structureService.notifyMessage({
                type: CONSTANTS.notificationTypes.warning,
                messge: this.toolTipService.getTranslateData('WARNING.CANNOT_CHANGE_AC_STATUS')
              });
              const activityData = {
                activityName: `Error in ${changeStat}ate Alternate contact`,
                activityType: 'manage alternate contacts',
                activityDescription: `${this.userData.displayName} (User-${this.userData.userId})error in  deleting  the Alternate contact with user id ${alternateId}`
              };
              this._structureService.trackActivity(activityData);
            }
          });
        } else {
          return false;
        }
      });
  }

  AlternateInvite(alternateId, patientId) {
    this._structureService
      .showAlertMessagePopup({
        text: this.toolTipService.getTranslateData('MESSAGES.CONFIRM_ALTERNATE_CONTACT_INVITE')
      })
      .then((confirm) => {
        if (confirm) {
          this.logger.info(alternateId);
          alternateId.username = alternateId.email;
          this.patientInfo['alternateContacts'] = alternateId;
          this.patientInfo['patientId'] = patientId;
          localStorage.setItem('isRelative', 'true');
          localStorage.setItem('patientInfo', JSON.stringify(this.patientInfo['alternateContacts']));
          this.router.navigate([`/user-registrations/register/false/${this.patientInfo['patientId']}`]);
        } else {
          return false;
        }
      });
  }

  AlternateDelete(alternateId, index) {
    this._structureService.showAlertMessagePopup({
        text: this.toolTipService.getTranslateData('MESSAGES.CONFIRM_ALTERNATE_CONTACT_DELETE')
      })
      .then((confirm) => {
        if (confirm) {
          const request = {
            contactId: alternateId,
            tenantId: this.userData.tenantId,
            status: 3
          };
          this._structureService.AlternatecontactChangestatus(request).then((response: any) => {
            if (response.status != null) {
              document.getElementById(index).style.display = 'none';
              this.alernateContactsData.splice(index, 1);
              this._structureService.notifyMessage({
                type: CONSTANTS.notificationTypes.success,
                messge: this.toolTipService.getTranslateData('SUCCESS_MESSAGES.SUCCESSFULLY_DELETED_ALTERNATE_CONTACT')
              });
              const activityData = {
                activityName: 'Delete Alternate contact',
                activityType: 'manage alternate contacts',
                activityDescription: `${this.userData.displayName} (User-${this.userData.userId}) deleted the Alternate contact with user id ${alternateId}`
              };
              this._structureService.trackActivity(activityData);
            } else {
              this._structureService.notifyMessage({
                type: CONSTANTS.notificationTypes.warning,
                messge: this.toolTipService.getTranslateData('WARNING.CANNOT_DELETE_AC')
              });
              const activityData = {
                activityName: 'Error in Inactivate Alternate contact',
                activityType: 'manage alternate contacts',
                activityDescription: `${this.userData.displayName} (User-${this.userData.userId})error in  deleting  the Alternate contact with user id ${alternateId}`
              };
              this._structureService.trackActivity(activityData);
            }
          });
        } else {
          return false;
        }
      });
  }

  oldPhoneNumberCheck() {
    const oldPhone = $('#oldPhoneNumber').val();
    const exactValue = this.newPatient.get('countryCode').value.trim() + this.newPatient.get('pcellno').value;
    if (!isBlank(oldPhone)) {
      if (oldPhone.toString() !== exactValue.toString()) {
        this.newPatient.patchValue({
          mobileVerified: false
        });
      }
    } else $('#oldPhoneNumber').val(exactValue);
  }

  oldEmailIdCheck() {
    const oldEmail = $('#oldemailId').val();
    const exactvalue = this.newPatient.get('pemail').value.trim();
    if (!isBlank(oldEmail)) {
      if (oldEmail.toString() !== exactvalue.toString()) {
        this.newPatient.patchValue({
          emailVerified: false
        });
      }
    } else {
      $('#oldemailId').val(exactvalue);
    }

    if (this.newPatient.controls['createPassword'].value && this.newPatient.controls['checkboxEmailUsernamePartner'].value) {
      this.newPatient.controls['patientUsername'].setValue(this.newPatient.controls.patientEmail.value);
    }
  }

  oneditInvite() {
    this.onEditUTypeLoad = true;
    this.onEditLoad = true;
    if (!isBlank(this.editId) && (localStorage.getItem('isCareGiver').toString() === 'false' || isBlank(localStorage.getItem('isCareGiver')))) {
      this.hideCaregiverSingup = true;
      let selectedItem: any = '';
      this._structureService.getPatient(this.editId).then((data) => {
        this.pageTitle = this.toolTipService.getTranslateData('TITLES.EDIT_PATIENT');
        this.patientList = data['getSessionTenant'].patientUsers;
        if (this.patientList[0]) {
          this.isEnrolledPatient = this.patientList.length > 0 ? this.patientList[0].password : false;
          if (this.patientList[0].countryCode) {
            $('#country_code').intlTelInput('setNumber', this.patientList[0].countryCode);
          }
          this.getCountryCode(this.patientList[0].countryCode, this.patientList[0].countryIsoCode);
          let mrn = '';
          if (!isBlank(localStorage.getItem('selectedRow'))) {
            selectedItem = JSON.parse(localStorage.getItem('selectedRow'));
            mrn = selectedItem && selectedItem.mrn ? selectedItem.mrn : '';
          } else {
            mrn =
              this.patientList[0] && this.patientList[0].patientIdentity && this.patientList[0].patientIdentity.IdentityValue
                ? this.patientList[0].patientIdentity.IdentityValue
                : '';
          }
          this.editSiteData = this.patientList[0].siteId;
          this.filterType = !!this.editSiteData;
          let email = '';
          if (!this.patientList[0].emails[0].value.includes('unknown_')) {
            email = this.IsEmail(this.patientList[0].emails[0].value);
          }
          let selectedMobile = '';
          if (!isBlank(this.patientList[0].mobile)) {
            selectedMobile = this.patientList[0].mobile.replace(/[|&;$%@"<>()+, -]/g, '');
          }
          this.newPatient.patchValue({
            pfname: this.patientList[0].firstName,
            plname: this.patientList[0].lastName,
            pcellno: selectedMobile,
            pemail: email,
            patientEmail: email,
            zipcode: this.patientList[0].zip,
            patientMobile: this.patientList[0].mobile,
            isVirtualUser: 'yes',
            virtualPatientId: this.editId,
            mrn,
            mobileVerified: this.patientList[0].userEMverification.mobileVerified,
            emailVerified: this.patientList[0].userEMverification.emailVerified,
            enableSmsNotifications: this.patientList[0].enableSmsNotifications,
            enableEmailNotifications: this.patientList[0].enableEmailNotifications
          });

          if (!isBlank(mrn)) {
            this.onEditMRNLoad = true;
          }

          if (this.patientList[0].userTags) {
            this.setTag('', true);
            this.logger.info(this.patientList[0].userTags);
            const arrayUsertags = [];
            for (let i = 0; i < this.patientList[0].userTags.length; i += 1) {
              arrayUsertags.push(this.patientList[0].userTags[i].id);
              const selectedIndex = this.selectedTags.indexOf(this.patientList[0].userTags[i].id);
              if (selectedIndex === -1) {
                this.selectedTags.push(this.patientList[0].userTags[i].id);
                this.selectedTagNames.push({
                  text: this.patientList[0].userTags[i].tagName
                });
                this.selectedTagIds.push(this.patientList[0].userTags[i].id);
              }
              $('<span/>', {
                class: 'tag-span',
                id: this.patientList[0].userTags[i].id,
                text: this.patientList[0].userTags[i].tagName,
                insertBefore: $('.recipient-search-area')
              }).append(`<span class='remove' id=${this.patientList[0].userTags[i].id}>x</span>`);
            }
            this.newPatient.patchValue({
              userTag: arrayUsertags
            });
            this.newPatient.patchValue({
              tags: arrayUsertags
            });

            this.logger.info('on edit converting');
            this.logger.info(this.newPatient.value);
            this._sharedService.formdataWithAlternate = this.newPatient.value;
            $('select[name="userTag"]').select2({
              placeholder: $('select[name="userTag"] option:first').text()
            });

            this.enableOrDisableUiLI(false, true, 'R');
          }

          const dob = this.patientList[0].dateOfBirth.split('-');
          if (dob.length) {
            $('.year').val(parseInt(dob[0])).trigger('change');
            $('.month')
              .val(parseInt(dob[1]) - 1)
              .trigger('change');
            setTimeout(() => {
              $('.day').val(parseInt(dob[2])).trigger('change');
            }, 1);
          }
        }
      });
      this.logger.info('this.selectedObj', this.selectedObj);
    } else {
      this.editSiteData = '';
      this.filterType = false;
    }
  }
  getCountryCode(countryCode, countryIsoCode) {
    setTimeout(() => {
      $('#country_code').intlTelInput();
      const countryDetails = setCountryCodeFlag('country_code', countryCode, countryIsoCode);
      this.userCountry = countryDetails.countryIsoCode;
      this.userInfo.countryCode = countryDetails.countryCode;
      this.newPatient.patchValue({
        countryCode: this.userInfo.countryCode
      });
    }, 1000);
  }
  loadPatdetails() {
    this.onEditUTypeLoad = true;
    this.onEditLoad = true;
    this.patientList = this._sharedService.formdataWithAlternate;
    this.logger.info('in load');
    this.logger.info(this.patientList);
    let email = '';
    if (!this.patientList.pemail.includes('unknown_')) {
      email = this.IsEmail(this.patientList.pemail);
    }
    let selectedMobile = '';
    if (!isBlank(this.patientList.pcellno)) {
      selectedMobile = this.patientList.pcellno.replace(/[|&;$%@"<>()+, -]/g, '');
    }
    this.newPatient.patchValue({
      pfname: this.patientList.pfname,
      plname: this.patientList.plname,
      pcellno: selectedMobile,
      pemail: email,
      patientEmail: email,
      zipcode: this.patientList.zipcode,
      patientMobile: this.patientList.patientMobile,
      isVirtualUser: 'yes',
      mrn: this.patientList.mrn,
      enableSmsNotifications: this.patientList.enableSmsNotifications,
      enableEmailNotifications: this.patientList.enableEmailNotifications
    });

    if (!isBlank(this.patientList.mrn)) {
      this.onEditMRNLoad = true;
    }

    if (this.patientList.userTag) {
      this.setTag('', true);
      this.logger.info(this.patientList.userTag);
      const arrayUsertags = [];
      for (let i = 0; i < this.patientList.userTag.length; i += 1) {
        this.logger.info(i);
        arrayUsertags.push(this.patientList.userTag[i].id);
        const selectedIndex = this.selectedTags.indexOf(this.patientList.userTag[i].id);
        if (selectedIndex === -1) {
          this.selectedTags.push(this.patientList.userTag[i].id);
          this.selectedTagNames.push({
            text: this.patientList.userTag[i].tagName
          });
          this.selectedTagIds.push(this.patientList.userTag[i].id);
        }

        $('<span/>', {
          class: 'tag-span',
          id: this.patientList.userTag[i].id,
          text: this.patientList.userTag[i].tagName,
          insertBefore: $('.recipient-search-area')
        }).append(`<span class='remove' id=${this.patientList.userTag[i].id}>x</span>`);
      }
      this.newPatient.patchValue({
        userTag: arrayUsertags
      });
      this.newPatient.patchValue({
        tags: arrayUsertags
      });

      $('select[name="userTag"]').select2({
        placeholder: $('select[name="userTag"] option:first').text()
      });

      this.enableOrDisableUiLI(false, true, 'R');
    }
  }

  IsEmail(email) {
    const regex = /^([a-zA-Z0-9_\.\-\+])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
    let validEmail;
    if (!regex.test(email)) {
      validEmail = '';
      return validEmail;
    }
    validEmail = email;
    return validEmail;
  }

  showPassword() {
    this.show_button = !this.show_button;
    this.show_eye = !this.show_eye;
    this.passwordField.nativeElement.focus();
  }

  omit_special_char(event) {
    return event.charCode !== 34;
  }

  tagInputFocus() {
    this.logger.info('tagInputFocus :call enableOrDisableUiLI.......');
    this.enableOrDisableUiLI(true, false, 'R');
  }

  selectAllTags() {
    let taglist = [];
    if (this.staffTags && this.staffTags.length) {
      taglist = this.staffTags;
    }
    if (this.patientTags && this.patientTags.length) {
      taglist = this.patientTags;
    }
    if (this.partnerTags && this.partnerTags.length) {
      taglist = this.partnerTags;
    }
    if (taglist && taglist.length > 0) {
      taglist.forEach((element) => {
        const { id } = element;
        if (this.selectedTags.indexOf(id) === -1) {
          this.setSelectedTags(element, id);
        }
      });
    }
  }

  closeSelectedTag(condition = false) {
    $('#recipient-search').text(' ').text('Search');
    this.resetTag(condition);
  }

  resetTag(condition = false) {
    this.selectedTags = [];
    this.patientTags = [];
    $('.tag-span').remove();
    if (!condition) this.enableOrDisableUiLI(false, true, 'R');
  }

  togglePreference(preference, value) {
    if (preference === 'createPassword') {
      this.enableCreatePasswordOption = value;
      this.show_button = false;
      this.show_eye = false;
      this.newPatient.patchValue({
        createPassword: value
      });
      this.newPatient.controls['temppassword'].setValue('');
      this.newPatient.controls['repeatPassword'].setValue('');
      this.newPatient.controls['patientUsername'].setValue('');

      if (value) {
        this.newPatient.controls['temppassword'].setValidators(Validators.required);
        this.newPatient.controls['temppassword'].updateValueAndValidity();
        this.newPatient.controls['repeatPassword'].setValidators([Validators.required]);
        this.newPatient.controls['repeatPassword'].updateValueAndValidity();
        this.newPatient.controls['patientUsername'].setValue(this.newPatient.controls.patientEmail.value);
        this.newPatient.controls['patientUsername'].setValidators([
          Validators.required,
          Validators.pattern(
            /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
          )
        ]);
        this.newPatient.controls['patientUsername'].updateValueAndValidity();
        this.newPatient.controls.checkboxEmailUsernamePartner.setValue(true);
      } else {
        this.newPatient.controls['temppassword'].setValidators(null);
        this.newPatient.controls['temppassword'].updateValueAndValidity();
        this.newPatient.controls['repeatPassword'].setValidators(null);
        this.newPatient.controls['repeatPassword'].updateValueAndValidity();
        this.newPatient.controls['patientUsername'].setValue('');
        this.newPatient.controls['patientUsername'].setValidators(null);
        this.newPatient.controls['patientUsername'].updateValueAndValidity();
      }
    } else {
      this.newPatient.patchValue({
        [preference]: value
      });
    }
  }
  oktaPasswordValidator = (formGroup: FormGroup): { [key: string]: string | boolean } | null | undefined => {
    const password = formGroup.get('temppassword').value;
    if (!password) {
      return null; 
    }
    if(this._structureService.isOktaWorkflowEnabled()){
    const regex = PATTERNS.idmPasswordPattern; 
    const username = this.newPatient && this.newPatient.get('patientUsername') ? this.newPatient.get('patientUsername').value || '' : '';
    const firstName = this.newPatient && this.newPatient.get('pfname') ? this.newPatient.get('pfname').value || '' : '';
    const lastName = this.newPatient && this.newPatient.get('plname') ? this.newPatient.get('plname').value || '' : '';
    const lowerValue = password.toLowerCase();
    if (!isBlank(username) && !isBlank(firstName) && !isBlank(lastName) && !isBlank(password)) {
      const usernameParts = username.split(PATTERNS.usernameDelimiters).map((part: string) => part.toLowerCase());
      const containsUsername = usernameParts.some((part: string) => lowerValue.includes(part));
      const containsFirstName = lowerValue.includes(firstName.toLowerCase());
      const containsLastName = lowerValue.includes(lastName.toLowerCase());
      const isValidPassword = regex.test(password);
      const isValid = isValidPassword && !containsUsername && !containsFirstName && !containsLastName;
      if (isValid) {
        return null; 
      }
      return {
        invalidPassword: true
      };
    }
  }
  };
  checkTagWithTems() {
    const textValue = $('#tagsInput').val();
    const searchText = textValue;

    if (!isBlank(textValue)) {
      this.setTag(searchText);
    }
  }

  setTag(searchKeyword = '', fromEdit = false) {
    if (!this.tagLoading) {
      this.tagLoading = true;
      $('#recipient-search').text(' ').text('Loading...');

      const formdata = this.newPatient.value;

      // Id of patient group is 3
      if (formdata.utype === 'patient') {
        const tagGetData = `?group=3&enroll=1&messageUserTag=1&searchKeyword=${searchKeyword}`;
        const tagTypes = ['2']; // Message Tag =1, User Tag =2 , Document Tag =3
        this._enrollService
          .getTagsByGroup(tagGetData, tagTypes)
          .then((data: any) => {
            this.tagLoading = false;
            if (data) {
              this.responsePatient = data;
              this.patientTags = this.patientTags.filter((a) => this.selectedTagIds.includes(a.id));
              this.patientTags = [...this.patientTags, ...data];
              this.patientTags = this.patientTags.reduce((unique, o) => {
                if (!unique.some((obj) => obj.id === o.id)) {
                  unique.push(o);
                }
                return unique;
              }, []);
              if (isBlank(searchKeyword) && fromEdit) {
                this.enableOrDisableUiLI(false, true, 'R');
                $('#recipient-search').text(' ').text('Search');
              } else {
                this.enableOrDisableUiLI(true, false, 'R');
                $('#recipient-search').text(' ').text('Search');
              }
            } else if (isBlank(searchKeyword) && fromEdit) {
              this.enableOrDisableUiLI(false, true, 'R');
              $('#recipient-search').text(' ').text('Search');
            } else {
              this.enableOrDisableUiLI(true, false, 'R');
              $('#recipient-search').text(' ').text('Search');
            }
          })
          .then(function () {
            this.tagLoading = false;
          })
          .catch(() => {
            this.tagLoading = false;
          });
      } else if (formdata.utype === CONSTANTS.userTypes.partner) {
        const tagTypes = ['2'];
        const partnerTagGetData = `?group=20&enroll=1&searchKeyword=${searchKeyword}`;
        this._enrollService
          .getTagsByGroup(partnerTagGetData, tagTypes)
          .then((data: any) => {
            this.tagLoading = false;
            if (data) {
              this.responsePartner = data;
              this.partnerTags = this.partnerTags.filter((a) => this.selectedTagIds.includes(a.id));
              this.partnerTags = [...this.partnerTags, ...data];
              this.partnerTags = this.partnerTags.reduce((unique, o) => {
                if (!unique.some((obj) => obj.id === o.id)) {
                  unique.push(o);
                }
                return unique;
              }, []);
              this.enableOrDisableUiLI(true, false, 'R');
              $('#recipient-search').text(' ').text('Search');
            } else {
              this.logger.info('No result.............');
              this.enableOrDisableUiLI(true, false, 'R');
              $('#recipient-search').text(' ').text('Search');
            }
          })
          .then(function () {
            this.tagLoading = false;
          })
          .catch(() => {
            this.tagLoading = false;
          });
      } else if (formdata.utype === 'staff') {
        const tagTypes = ['2'];
        const staffTagGetData = `?group=2&enroll=1&searchKeyword=${searchKeyword}`;
        this._enrollService
          .getTagsByGroup(staffTagGetData, tagTypes)
          .then((data: any) => {
            this.tagLoading = false;
            if (data) {
              this.responseStaff = data;
              this.staffTags = this.staffTags.filter((a) => this.selectedTagIds.includes(a.id));
              this.staffTags = [...this.staffTags, ...data];
              this.staffTags = this.staffTags.reduce((unique, o) => {
                if (!unique.some((obj) => obj.id === o.id)) {
                  unique.push(o);
                }
                return unique;
              }, []);
              this.enableOrDisableUiLI(true, false, 'R');
              $('#recipient-search').text(' ').text('Search');
            } else {
              this.enableOrDisableUiLI(true, false, 'R');
              $('#recipient-search').text(' ').text('Search');
            }
          })
          .then(() => {
            this.tagLoading = false;
          })
          .catch(() => {
            this.tagLoading = false;
          });
      }
    }
  }

  enableOrDisableUiLI(condition, clear = true, from: any = '') {
    if (condition) {
      if (from === 'R') {
        if ($('ul#associate-ul').css('display') === 'block') {
          this.enableOrDisableUiLI(false, false);
        }
        $('#tagsInput').addClass('ul-active');
        $('ul#recipient-ul').css('display', 'block');
      } else {
        if ($('#recipient-ul').css('display') === 'block') {
          this.enableOrDisableUiLI(false, false, 'R');
        }
        $('ul#associate-ul').css('display', 'block');
        $('input#associate-search-input').addClass('active');
      }
    } else if (from === 'R') {
      $('#tagsInput').removeClass('ul-active');
      $('ul#recipient-ul').css('display', 'none');
    } else {
      $('ul#associate-ul').css('display', 'none');
      $('input#associate-search-input').removeClass('active');
    }

    if (clear) {
      if (from === 'R') {
        $('#tagsInput').val('');
        $('#tagsInput').attr('placeholder', 'Search User Tags');
      } else {
        $('input#associate-search-input').val('');
      }
    }
  }

  setSelectedTags(tags, tagid) {
    const selectedIndex = this.selectedTags.indexOf(tagid);
    if (selectedIndex === -1) {
      this.selectedTags.push(tagid);

      this.selectedTagNames.push({
        text: tags.tag_name
      });

      this.selectedTagIds.push(tags.id);
      this.setSelectedTagForms(false, tags);
    } else {
      this.removeSelectedTag(tagid);
    }
  }

  removeSelectedTag(id) {
    const index = this.selectedTags.indexOf(id);
    if (index > -1) {
      if (this.selectedTags.length === 1) {
        this.deleteRemovedTagFromList(index, id);
      } else {
        this.deleteRemovedTagFromList(index, id);
      }
    }
  }

  deleteRemovedTagFromList(index, id) {
    delete this.selectedTags[index];
    this.setOrResetSelectedItemList(id);
    this.setSelectedTagForms(true);
    this.selectedTags = this.selectedTags.filter((id) => {
      this.logger.info(id);
      return id !== '';
    });
  }

  checkTagExist(user) {
    if (this.selectedTags.indexOf(String(user)) > -1) {
      return true;
    }
    return false;
  }

  setOrResetSelectedItemList(id) {
    $(`#${id}`).remove();
    return true;
  }

  doneSelectedTag() {
    if (this.selectedTags && this.selectedTags.length) this.enableOrDisableUiLI(false, true, 'R');
    $('#tagsInput').text('');
  }

  setSelectedTagForms(fromRemove = false, users: any = '') {
    if (this.selectedTags && this.selectedTags.length > 0) {
      let tags = this.selectedTags;
      if (tags.length) {
        this.stafffillfirst = true;
      } else {
        this.stafffillfirst = false;
      }

      tags = tags.filter((a) => a.indexOf('tag-') === -1);
      const recipi = this.selectedTags;
      if (
        tags.length <= 0 &&
        recipi
          .filter((a) => a.indexOf('tag-') !== -1)
          .map((a) => a.replace('tag-', ''))
          .join() === ''
      ) {
        this.stafffillfirst = false;
      }

      if (!fromRemove) {
        const textData = users.tag_name;
        $('<span/>', {
          class: 'tag-span',
          id: users.id,
          text: textData,
          insertBefore: $('.recipient-search-area')
        }).append(`<span class='remove' id=${users.id}>x</span>`);
      }
    }
  }

  careGiverChange() {
    this.selectedObj = '';
    const val = this.newPatient.get('is_caregiver').value;
    if (val) {
      this.newPatient.get('cfname').setValidators([Validators.required]);
      this.newPatient.get('cfname').updateValueAndValidity();
      this.newPatient.get('clname').setValidators([Validators.required]);
      this.newPatient.get('clname').updateValueAndValidity();
      this.newPatient.get('dob').setValidators([Validators.required]);
      this.newPatient.get('dob').updateValueAndValidity();
    } else {
      this.newPatient.get('cfname').clearValidators();
      this.newPatient.get('cfname').updateValueAndValidity();
      this.newPatient.get('clname').clearValidators();
      this.newPatient.get('clname').updateValueAndValidity();
      this.newPatient.get('dob').clearValidators();
      this.newPatient.get('dob').updateValueAndValidity();
      this.newPatient.get('mrn').clearValidators();
      this.newPatient.get('mrn').updateValueAndValidity();
    }
  }

  setMrnField() {
    const val = this.newPatient.get('use_mrn').value;
    if (val && this.newPatient.get('utype').value === 'patient') {
      this.newPatient.get('mrnSys').setValidators([Validators.required]);
      this.newPatient.get('mrnSys').updateValueAndValidity();
      this.newPatient.get('mrn').setValidators([Validators.required]);
      this.newPatient.get('mrn').updateValueAndValidity();
      this.newPatient.get('mrn').markAsTouched();
      this.newPatient.patchValue({
        mrnSys: this.showInInviteRecord.externalSystemId,
        mrnSysCode: this.showInInviteRecord.code,
        mrn: ''
      });
    } else {
      this.newPatient.get('mrnSys').clearValidators();
      this.newPatient.get('mrnSys').updateValueAndValidity();
      this.newPatient.get('mrn').clearValidators();
      this.newPatient.get('mrn').updateValueAndValidity();
    }
  }

  validateDuplication(formdata) {
    if (!isBlank(formdata.pemail) || (!isBlank(formdata.mrn) && formdata.utype === CONSTANTS.userTypes.patient)) {
      /** To check the user exists with same email address or MRN */
      const data = {
        email: this._structureService.replaceSpecialChar(formdata.patientUsername.trim()),
        operation: 'checking',
        esiOperation: !!formdata.mrn,
        MRNumber: formdata.mrn,
        externalSystemId: formdata.utype === CONSTANTS.userTypes.staff ? formdata.sysId : formdata.mrnSys,
        userId: this.editId || ''
      };
      if (!this.newPatient.controls['createPassword'].value) {
        delete data.email;
      }

      this._enrollService.checkUserExists(data).then((d: any) => {
        if (data.esiOperation && d.esiExist) {
          this._structureService.notifyMessage({
            type: CONSTANTS.notificationTypes.warning,
            messge: this.toolTipService.getTranslateDataWithParam('MESSAGES.MRN_ALREADY_IN_USE', {
              mrnNumber: data.MRNumber,
              displayName: d.displayname
            })
          });
          return false;
        }
        if (d.userExist) {
          this._structureService.notifyMessage({
            type: CONSTANTS.notificationTypes.warning,
            messge: this.toolTipService.getTranslateDataWithParam('MESSAGES.EMAIL_ALREADY_USED', { email: data.email })
          });
          return false;
        }

        if (!d.userExists && !d.mrnExists) {
          /* User not exist check dob for patient starts */
          if (formdata.utype === CONSTANTS.userTypes.patient && isBlank(formdata.mrn)) {
            this.checkDuplicateExistWithDob(formdata);
          } else {
            this.showConfirmation(formdata);
          }
        }
      });
    } else if (formdata.utype === CONSTANTS.userTypes.patient) {
      this.checkDuplicateExistWithDob(formdata);
    }
  }

  removeDeletedUsers(allusers) {
    const users = allusers.filter((row) => {
      if (+row.status !== 8) {
        return true;
      }
    });

    return users;
  }

  checkDuplicateExistWithDob(formdata) {
    this.logger.info('checkDuplicateExistWithDob');

    const data = {
      firstname: this.newPatient.get('pfname').value,
      lastname: this.newPatient.get('plname').value,
      dob: moment($('#dob-date-picker').val()).format('DD-MM-YYYY'),
      operation: 'patientChecking',
      userId: this.editId || ''
    };

    if (formdata.utype === CONSTANTS.userTypes.patient) {
      this._enrollService.checkUserExists(data).then((d: any) => {
        if (d.userExist) {
          this._structureService.notifyMessage({
            type: CONSTANTS.notificationTypes.warning,
            messge: this.toolTipService.getTranslateData('MESSAGES.PATIENT_EXISTS_FL_LN_DOB')
          });
        } else {
          this.showConfirmation(formdata);
        }
      });
    }
  }
  gotoAlternate() {
    localStorage.setItem('Addclciked', '1');
    if (+this.userData.config.enable_progress_note_integration === 1) {
      this._structureService.showAlertMessagePopup({
          text: this.toolTipService.getTranslateData('MESSAGES.INTEGRATION_ENABLED_NO_AC_ADDED_DIRECTLY'),
          type: CONSTANTS.notificationTypes.warning,
          title: '',
          confirmButtonText: this.toolTipService.getTranslateData('BUTTONS.CONTINUE_ANYWAY'),
          cancelButtonText: this.toolTipService.getTranslateData('BUTTONS.GO_BACK')
        })
        .then((isConfirm) => {
          if (isConfirm) this.router.navigate(['/add-alternate-contact']);
        });
    } else {
      this.router.navigate(['/add-alternate-contact']);
    }
  }
  registerAll() {
    let formdata = this.newPatient.value;
    if (this.userData.config.default_patients_workflow === DefaultPatientWorkFlow.ALTERNATE_CONTACTS) {
      formdata = this._sharedService.formdataWithAlternate;
    }
    if (formdata.length) {
      if (formdata.pemail.trim() && formdata.pcellno.trim()) {
        this.showConfirmation(formdata);
      }
    } else {
      this._structureService.showAlertMessagePopup({
          text: this.toolTipService.getTranslateData('MESSAGES.FILL_PATIENT_DETAILS')
        })
        .then((confirm) => {
          if (confirm) return false;
          return false;
        });
    }
  }

  registerUser() {
    this.logger.info(this.newPatient);
    this.siteRequired = isBlank(this.siteId) || this.siteId.toString() === '0';
    if (this.siteRequired) {
      return false;
    }
    let formdata = this.newPatient.value;
    this.submitted = true;
    if (!formdata.createPassword || formdata.createPassword === '') {
      this.newPatient.controls['temppassword'].setValue('');
    }

    if (
      isBlank(formdata.pemail.trim()) &&
      isBlank(formdata.pcellno.trim()) &&
      (this.newPatient.controls['createPassword'].value || formdata.utype !== CONSTANTS.userTypes.patient || (this.isEnrolledPatient && this.editId))
    ) {
      this._structureService.notifyMessage({
        messge: this.toolTipService.getTranslateData('VALIDATION_MESSAGES.EMAIL_OR_MOBILE_NUMBER_MANDATORY'),
        type: CONSTANTS.notificationTypes.warning
      });
      return false;
    }
    if (!this.newPatient.valid ) {
      return false;
    }

    formdata = this.newPatient.value;

    if ($('#dob-date-picker') && $('#dob-date-picker').val() === '' && formdata.utype === CONSTANTS.userTypes.patient) {
      this._structureService.notifyMessage({
        messge: this.toolTipService.getTranslateData('VALIDATION_MESSAGES.DOB_MANDATORY'),
        type: CONSTANTS.notificationTypes.warning
      });
      return false;
    }

    if ($('#temppassword').val() !== $('#repeat-password').val() && formdata.createPassword) {
      this._structureService.notifyMessage({
        messge: this.toolTipService.getTranslateData('VALIDATION_MESSAGES.PASSWORD_MISMATCH'),
        type: CONSTANTS.notificationTypes.warning
      });
      return false;
    }

    this.logger.info(formdata);
    let role = this.staffRoles.filter((a) => a.id === formdata.userRole);
    this.logger.info('this.selectedTags');
    this.logger.info(this.selectedTags);

    if (this.selectedTags.length) {
      if (formdata.utype === CONSTANTS.userTypes.patient) {
        formdata['tags'] = this.selectedTags.map((a) => {
          const tag = this.patientTags.find((b) => b.id.toString() === a.toString());
          if (tag) {
            const data = {
              tagType: CONSTANTS.userTypes.patient,
              tagId: a,
              tagName: tag.tag_name
            };
            return data;
          }
        });
      } else if (formdata.utype === CONSTANTS.userTypes.staff) {
        formdata['tags'] = this.selectedTags.map((a) => {
          const tag = this.staffTags.find((b) => b.id.toString() === a.toString());
          if (tag) {
            const data = {
              tagType: CONSTANTS.userTypes.staff,
              tagId: a,
              tagName: tag.tag_name
            };
            return data;
          }
        });
        this.logger.info(formdata);
      } else if (formdata.utype === CONSTANTS.userTypes.partner) {
        formdata['tags'] = this.selectedTags.map((a) => {
          const tag = this.partnerTags.find((b) => b.id.toString() === a.toString());
          if (tag) {
            const data = {
              tagType: CONSTANTS.userTypes.partner,
              tagId: a,
              tagName: tag.tag_name
            };
            return data;
          }
        });
      }
    } else {
      formdata['tags'] = [];
    }

    if (role.length > 0) {
      formdata['citusRoleId'] = role[0].citus_role_id.toString();
      formdata['staffRole'] = role[0].name;
    }

    if (formdata.utype === CONSTANTS.userTypes.partner) {
      role = this.partnerRoles.filter((a) => a.id.toString() === formdata.partnerRole.toString());
      if (role.length > 0) {
        formdata['citusRoleId'] = role[0].citus_role_id.toString();
        formdata['staffRole'] = role[0].name;
      }
    }

    if (formdata.utype === CONSTANTS.userTypes.staff) {
      if (+this.manageConfig.show_staffid_in_user_invite === 1 && formdata.mrn !== '') {
        formdata['sysId'] = this.manageConfig.esi_code_for_staff_identity;
      } else {
        formdata['sysId'] = '';
      }
    }

    if (
      formdata.utype !== CONSTANTS.userTypes.patient &&
      formdata.createPassword &&
      formdata.temppassword !== '' &&
      formdata.temppassword === formdata.repeatPassword
    ) {
      if (formdata.pemail.trim() === '') {
        formdata.pemail = formdata.patientUsername;
      }
    }

    /* The mrn form control is a common form control used for patient's MRN and staff ID
    The duplicate MRN is not allowed for patient */
    const mrnCheck = !!(formdata.utype === CONSTANTS.userTypes.patient && !isBlank(this.newPatient.value.mrn));
    if (
      isBlank(formdata.pemail.trim()) &&
      isBlank(formdata.pcellno.trim()) &&
      !this.newPatient.controls['createPassword'].value &&
      (formdata.utype === CONSTANTS.userTypes.patient || !this.isEnrolledPatient)
    ) {
      let confirmMsg = this.toolTipService.getTranslateData('MESSAGES.CONFIRM_WITHOUT_CONTACT');
      if (this.editId && !this.isEnrolledPatient) {
        confirmMsg = this.toolTipService.getTranslateData('MESSAGES.CONFIRM_WITHOUT_CONTACT_FOR_UPDATE');
      }

      this._structureService
        .showAlertMessagePopup({
          text: confirmMsg,
          type: CONSTANTS.notificationTypes.warning,
          confirmButtonText: this.toolTipService.getTranslateData('BUTTONS.CONTINUE_WITHOUT_CONTACT'),
          cancelButtonText: this.toolTipService.getTranslateData('BUTTONS.RETURN_TO_EDIT'),
          cancelBtnClass: 'btn-info',
          customClass: 'swal-wide'
        })
        .then((confirm) => {
          if ($('div .sa-button-container button').hasClass('btn-info')) {
            $('div .sa-button-container button').removeClass('btn-info');
          }
          if (confirm) {
            setTimeout(() => {
              swal.close();
              this.handleFormSubmission(formdata, mrnCheck);
              return true;
            }, 100);
          } else {
            swal.close();
            return false;
          }
        });
    } else {
      this.handleFormSubmission(formdata, mrnCheck);
    }
  }

  handleFormSubmission(formdata: any, mrnCheck: boolean): boolean {
    if (!isBlank(formdata.pemail) || mrnCheck) {
      if (this.newPatient.get('pemail').valid || mrnCheck) {
        this.validateDuplication(formdata);
      } else {
        this._structureService.notifyMessage({
          messge: this.toolTipService.getTranslateData('VALIDATION_MESSAGES.EMAIL_NOT_FORMATTED'),
          type: CONSTANTS.notificationTypes.warning
        });
        return false;
      }
    } else if (formdata.utype === CONSTANTS.userTypes.patient) {
      this.checkDuplicateExistWithDob(formdata);
    } else {
      this.showConfirmation(formdata);
    }
    return true;
  }
  setActivetab(tabname) {
    setTimeout(() => {
      $('select[name="userTag"]').select2({
        placeholder: $('select[name="userTag"] option:first').text()
      });
      $('#country_code').intlTelInput();

      $('#country_code').intlTelInput('setCountry', this.initialUserCountry);
      const cntryDetails = $('#country_code').intlTelInput('getSelectedCountryData');

      $('#country_code').intlTelInput('setNumber', `+${cntryDetails.dialCode}`);

      $('#dob-date-picker').combodate({
        format: 'YYYY-MM-DD',
        template: 'MMM D YYYY',
        minYear: 1900,
        firstItem: 'empty',
        maxYear: new Date().getFullYear(),
        customClass: 'form-control select2 dob-sec',
        smartDays: true
      });

      $('.month, .day, .year').select2();
    }, 100);
    this.activePatientTab = tabname;
    this.oneditInvite();
  }
  deleteUser(formdata) {
    const params = {
      status: 'Deleted'
    };

    this._structureService.updateUser(this.selectedObj.userid, params).then((datasuccess) => {
      this.selectedObj = '';
      if (datasuccess) {
        this.sendUserRegisterData(formdata);
        if (this.selectedObj.referralCode) {
          // Remove the entry from invite users (wordpress instance)
          this._enrollService.deleteFormSubmission(this.selectedObj.referralCode).then((res: any) => {
            this.logger.info(res);
          });
        }
        const activityData = {
          activityName: 'Delete User',
          activityType: 'manage user access',
          activityDescription: `${this.userData.displayName} (User-${this.userData.userId}) deleted the Patient with user id ${datasuccess['updateUser'].id}`
        };
        this._structureService.trackActivity(activityData);
      }
    });
  }

  showConfirmation(formdata) {
    this.logger.info(formdata);
    let confirmMsg = 'You are going to add this User.';
    if (this.editId) {
      confirmMsg = 'You are going to update this User.';
    }
    swal(
      {
        title: 'Are you sure?',
        text: confirmMsg,
        type: 'warning',
        showCancelButton: true,
        cancelButtonClass: 'btn-default',
        confirmButtonClass: 'btn-warning',
        confirmButtonText: 'Ok',
        closeOnConfirmsysId: true,
      },
      (confirm) => {
        if (confirm) {
          this.logger.info(formdata);
          NProgress.start();
          this.disableSubmit = true;
          this.sendUserRegisterData(formdata);
        } else {
          return false;
        }
      }
    );
  }

  private sendUserRegisterData(formdata) {
    this.logger.info('formdata', formdata);
    const bDate = moment($('#dob-date-picker').val());
    const tags = [];
    if (formdata.tags.length) {
      for (let i = 0; i < formdata.tags.length; i += 1) {
        tags.push(formdata.tags[i].tagId);
      }
    }
    const virtualUserData = {
      email: this._structureService.replaceSpecialChar(formdata.pemail.trim()),
      firstname: this._structureService.replaceSpecialChar(formdata.pfname.trim()),
      lastname: this._structureService.replaceSpecialChar(formdata.plname.trim()),
      dob: bDate.isValid() ? bDate.format('MMM D,YYYY') : '',
      countryCode: this.userInfo.countryCode ? this.userInfo.countryCode : '+1',
      countryIsoCode: this.userCountry,
      cell: formdata.pcellno.trim(),
      zipcode: formdata.zipcode ? formdata.zipcode.trim() : '',
      mrn: formdata.utype === CONSTANTS.userTypes.staff || formdata.utype === CONSTANTS.userTypes.patient ? formdata.mrn : formdata.partnerId,
      companyNursingAgency: formdata.companyNursingAgency || '',
      citusRoleId: formdata.citusRoleId,
      source: 'AddUser',
      userType: formdata.utype,
      tags,
      mobileVerified: formdata.mobileVerified ? 1 : 2, // 1 for verified,2 for not verified
      emailVerified: formdata.emailVerified ? 1 : 2, // 1 for verified,2 for not verified
      virtualPatientId: this.editId ? this.editId : '',
      userCategoryId: formdata.partnerCategory,
      siteIds: this.siteId.join(','),
      homeSiteId: '',
      tenantRoleId: formdata.utype === 'staff' ? formdata.userRole : formdata.partnerRole,
      enableSmsNotifications: formdata.enableSmsNotifications,
      enableEmailNotifications: formdata.enableEmailNotifications
    };
    if (formdata.utype !== CONSTANTS.userTypes.patient) {
      virtualUserData.dob = '';
      virtualUserData.homeSiteId = this.homeSiteId.siteId ? this.homeSiteId.siteId[0] : '';
    }
    if (formdata.createPassword && formdata.temppassword && formdata.temppassword === formdata.repeatPassword) {
      const env = this._structureService.environment || 'devl';
      const details = `userName=${encodeURIComponent(
        this._structureService.replaceSpecialChar(formdata.pemail.trim())
      )}&displayName=${encodeURIComponent(
        `${this._structureService.replaceSpecialChar(formdata.pfname.trim())} ${this._structureService.replaceSpecialChar(formdata.plname.trim())}`
      )}&firstname=${encodeURIComponent(this._structureService.replaceSpecialChar(formdata.pfname.trim()))}&lastname=${encodeURIComponent(
        this._structureService.replaceSpecialChar(formdata.plname.trim())
      )}&password=${encodeURIComponent(formdata.temppassword)}&role=${
        formdata.utype !== CONSTANTS.userTypes.patient ? formdata.citusRoleId : '3'
      }&tenantRole=${formdata.utype === CONSTANTS.userTypes.staff ? formdata.userRole : formdata.partnerRole}&mail=${encodeURIComponent(
        this._structureService.replaceSpecialChar(formdata.pemail.trim())
      )}&country_code=${encodeURIComponent(formdata.countryCode ? formdata.countryCode : '+1')}&countryIsoCode=${encodeURIComponent(
        this.userCountry
      )}&mobile=${formdata.pcellno.trim()}&mobileVerified=${formdata.mobileVerified ? 1 : 2}&emailVerified=${
        formdata.emailVerified ? 1 : 2
      }&registrationToken=${this.registrationToken}&environment=${env}&isAccept=true&dob=${encodeURIComponent(
        formdata.utype === CONSTANTS.userTypes.patient ? (bDate.isValid() ? bDate.format('MMM D,YYYY') : '') : ''
      )}&address=&state=&country=&city=&zip=${encodeURIComponent(
        formdata.zipcode ? formdata.zipcode.trim() : ''
      )}&patient_associated_id=0&isFamilyMember=false&familyMemberName=undefined&MRNumber=${
        formdata.utype === CONSTANTS.userTypes.staff ? formdata.mrn : formdata.partnerId
      }&companyNursingAgency=${virtualUserData.companyNursingAgency}
      &registrationType=6
        &tags=${encodeURIComponent(tags.join(','))}&siteIds=${this.siteId.join(',')}&userId=${encodeURIComponent(
        this._structureService.replaceSpecialChar(formdata.patientUsername.trim())
      )}&userType=staff&homeSiteId=${encodeURIComponent(formdata.utype !== CONSTANTS.userTypes.patient && this.homeSiteId.siteId ? this.homeSiteId.siteId[0] : '')}`;
      this.registerUserDetails(details, formdata);
    } else {
      this._structureService.createVirtualUser(virtualUserData).then((res: any) => {
          let activityData = {};
          let msg = this.toolTipService.getTranslateDataWithParam('MESSAGES.SUCCESSFULLY_ADDED_CONTENT', { content: formdata.utype });
          if (!isBlank(this.editId)) {
            msg = this.toolTipService.getTranslateData('MESSAGES.SUCCESSFULLY_UPDATED_PATIENT_DETAILS');
          }
          if (+res.status === 1) {
            NProgress.done();
            this.disableSubmit = false;
            if (!isBlank(this.editId)) { 
              const notify = $.notify(msg);
              setTimeout(() => {
                notify.update({
                  type: CONSTANTS.notificationTypes.success,
                  message: `<strong>${msg}</strong>`
                });
                if (this.userData.config.default_patients_workflow !== DefaultPatientWorkFlow.ALTERNATE_CONTACTS) {
                  this.registerBack();
                } else {
                  this.activePatientTab = 'alternate-contacts';
                  this.patientEditTabs('alternate-contacts');
                }
              }, 2000);
              activityData = {
                activityName: 'Edited patient via User Center',
                activityType: 'add user',
                activityDescription: `Edited patient (${virtualUserData.email}) via User Center`,
                tenantId: this._structureService.getCookie('crossTenantId')
              };
            } else if (res.already) {
              msg = this.toolTipService.getTranslateData('MESSAGES.USER_ALREADY_EXISTS');
              const notify = $.notify(msg);
              setTimeout(() => {
                notify.update({
                  type: 'warning',
                  message: `<strong>${msg}</strong>`,
                });
              }, 2000);

              activityData = {
                activityName: 'Added User via User Center already exists.',
                activityType: 'add user',
                activityDescription: 'Added User via User Center already exists.',
                tenantId: this._structureService.getCookie('crossTenantId')
              };
            } else {
              const notify = $.notify(msg);
              setTimeout(() => {
                notify.update({
                  type: 'success',
                  message: `<strong>${msg}</strong>`
                });
                if (
                  this.userData.config.default_patients_workflow !== DefaultPatientWorkFlow.ALTERNATE_CONTACTS ||
                  formdata.utype !== CONSTANTS.userTypes.patient
                ) {
                  this.registerBack();
                } else {
                  $(document).ready(function () {
                    $('.nav li').find('#disable-alternative').removeClass('disabled');
                    $('.nav li').find('#disable-alternative').find('a').attr('data-toggle');
                  });
                  this.activePatientTab = 'alternate-contacts';
                  this.patientEditTabs('alternate-contacts');
                  this.editId = res.userId;
                  localStorage.setItem('patientId', res.userId);
                }
              }, 2000);
              activityData = {
                activityName: 'Added User via User Center',
                activityType: 'add user',
                activityDescription: `Added User ${virtualUserData.email} (${res.userId}) via User Center`,
                tenantId: this._structureService.getCookie('crossTenantId')
              };
            }
          } else {
            NProgress.done();
            this.disableSubmit = false;
            msg = this.toolTipService.getTranslateData('MESSAGES.SOMETHING_WRONG');
            const notify = $.notify(msg);
            setTimeout(() => {
              notify.update({
                type: 'error',
                message: `<strong>${msg}</strong>`
              });
            }, 2000);
            activityData = {
              activityName: 'Added User failed via User Center',
              activityType: 'add user',
              activityDescription: `Added User failed (${virtualUserData.email}) via User Center`,
              tenantId: this._structureService.getCookie('crossTenantId')
            };
          }
          this._structureService.trackActivity(activityData);
        })
        .catch(() => {
          NProgress.done();
        });
    }
    localStorage.setItem('addUserTab', formdata.utype);
  }

  registerUserDetails(details, formdata) {
    this.registrationservice.signup(details).then((data) => {
      this.dataResponseUser = data;
      if (!this.dataResponseUser.uid) {
        let msg = this.dataResponseUser.errorMessage;
        let notify = $.notify(msg);
        setTimeout(function () {
          notify.update({
            type: 'error',
            message: '<strong>' + msg + '</strong>'
          });
        }, 2000);

        let activityData = {
            activityName: 'Failure User Registration via User Center',
            activityType: 'user creation',
            activityDescription:
              'User registration failed via User Center. ' +
              this.dataResponseUser.errorMessage +
              '. Details: ' +
              details,
            tenantId: formdata.tenantId,
          };

          this._structureService.trackActivity(activityData);
        } else {
          if (this.dataResponseUser.wpEnrollUrl) {
            // create shortcode
            const reqData = {
              tenantId: this.registrationToken,
              enrollSource: 'chwebsite',
              enrollType: 'Enrollment',

              userInfo: {
                firstName: formdata.firstname,
                lastName: formdata.lastname,
                honorificPrefix: '',
                honorificSuffix: '',
                designation: '',
                organization: this.dataResponseUser.tenantName,
                email: formdata.email,
                mobile: formdata.cell,
                countryCode: formdata.countryCode,
              },
            };

            this.registrationservice
              .createReferralCodes(reqData, this.dataResponseUser.wpEnrollUrl)
              .then((data: any) => {
                if (data.status && data.status.statusCode != '0') {
                  const activityData = {
                    activityName:
                      'Failure Referral Token Create - Signup via User Center',
                    activityType: 'manage user enrollment',
                    activityDescription:
                      'Referral token creation for the user ' +
                      formdata.displayName +
                      '(' +
                      this.dataResponseUser.uid +
                      ') via User Center. Failed due to ' +
                      data.status.statusMessage,
                    tenantId: this.registrationTenantId,
                  };
                  this._structureService.trackActivity(activityData);
                }
              });
          }

          let activityData = {
            activityName: 'User Registration via via User Center',
            activityType: 'user creation',
            activityDescription:
              'New user registered ' + formdata.patientUsername + ' (' + this.dataResponseUser.uid + ') via User Center',
            tenantId: formdata.tenantId,
          };

          if (this.dataResponseUser.isExistingUser) {
            activityData.activityName = 'User Reactivation via User Center';
            activityData.activityDescription =
              'Reactivated the existing user (' +
              formdata.email +
              ') via User Center';
          }
          this._structureService.trackActivity(activityData);
          let successMessage;
          if (this.dataResponseUser.isExistingUser) {
            successMessage = this.dataResponseUser.isExistingUser + ' ';
          } else {
            successMessage = this.toolTipService.getTranslateDataWithParam('MESSAGES.REGISTRATION_SUCCESS_SENT_MAIL',
            {content: this._structureService.replaceSpecialChar(formdata.pemail.trim())});
          }
          this._structureService.notifyMessage({
            type: CONSTANTS.notificationTypes.success,
            messge: successMessage,
          });          
          setTimeout(() => {
            this.registerBack();
          }, 2000);
        }
      });
  }

  conditionalValidator(
    condition: () => boolean,
    validator: ValidatorFn
  ): ValidatorFn {
    return (
      control: AbstractControl
    ): {
      [key: string]: any;
    } => {
      if (!condition()) {
        return null;
      }
      return validator(control);
    };
  }

  private makeid() {
    let text = '';
    const possible =
      'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 15; i++)
      text += possible.charAt(Math.floor(Math.random() * possible.length));
    return text;
  }

  userCategoryChange(type) {
    this.siteChoosed = false;
    this.submitted = false;
    this.singleSelection = type === 'patient';
    this.logger.info(
      'userCategoryChange called from add-user addUserTab',
      localStorage.getItem('addUserTab')
    );
    this.logger.info('userCategoryChange called from add-user type', type);
    this.selectedObj = '';

    if (localStorage.getItem('addUserTab') == type) {
    }

    if (type == 'patient') {
      this.logger.info('patient clicked');
      localStorage.setItem('addUserTab', 'patient');
      this.router.navigate(['/add-user/search-user']);
    }

    if (type == 'partner') {
      localStorage.setItem('addUserTab', 'partner');
      this.resetTag();
      this.formGroupDirective.resetForm();
      this.ngOnInit();
    }

    if (type == 'staff') {
      localStorage.setItem('addUserTab', 'staff');
      this.resetTag();
      this.formGroupDirective.resetForm();
      this.ngOnInit();
    }

    let paramthis = this;
    $('.remove').click(
      {
        datathis: paramthis,
      },
      getInfo
    );

    function getInfo(event) {
      let self = event.data.datathis;
      let delid = $(event.target).parent().attr('id');
      self.removeSelectedTag(delid);
    }
  }

  changePartnerCategory() {
    if (
      this.manageConfig.enable_patient_info_from_third_party_app == '1' &&
      this.partnerCategory.length > 0
    ) {
      let physicianRoles =
        this.manageConfig.auxilary_physician_roles_for_manage_security_rules.split(
          ','
        );
      let nursingRoles =
        this.manageConfig.auxilary_nursing_agency_roles_for_manage_security_rules.split(
          ','
        );
      this.logger.info('physicianRoles', physicianRoles);
      this.logger.info('nursingRoles', nursingRoles);

      if (physicianRoles.includes($('select[name="partnerCategory"]').val())) {
        this.showAuxilaryPhysicianOrNursingLabel = true;
        this.auxilaryPhysicianOrNursingLabel =
          this.manageConfig.auxilary_physician_label;
        this.newPatient.patchValue({
          mrnSys: this.manageConfig.esi_code_for_auxilary_physician,
        });
      } else if (
        nursingRoles.includes($('select[name="partnerCategory"]').val())
      ) {
        this.showAuxilaryPhysicianOrNursingLabel = true;
        this.auxilaryPhysicianOrNursingLabel =
          this.manageConfig.auxilary_nursing_agency_label;
        this.newPatient.patchValue({
          mrnSys: this.manageConfig.esi_code_for_auxilary_nursing_agency,
        });
      } else {
        this.showAuxilaryPhysicianOrNursingLabel = false;
      }
    } else {
      this.showAuxilaryPhysicianOrNursingLabel = false;
    }
    this.logger.info(
      'this.auxilaryPhysicianOrNursingLabel',
      this.auxilaryPhysicianOrNursingLabel
    );
  }

  searchOnEnter(event) {
    let txt = $('#tagsInput').val().trim();
    if (event.keyCode == 13) {
      if (txt) {
        this.setTag(txt);
      }
    }
  }

  closeInvite() {
    $('.overlay-invite').hide();
  }

  showInvite() {
    $('.popup-invite').css('display', 'flex');
    $('.overlay-invite').css('display', 'flex');
  }

  proceedInvite() {
    this.closeInvite();
    if (
      this.selectedObj.associateCaregiverCount &&
      this.selectedObj.associateCaregiverCount != '0'
    ) {
      this.selectedObj = '';
      let msg = `You are not allowed to invite a patient associated with a caregiver`;
      const notify = $.notify(msg);
      setTimeout(function () {
        notify.update({
          type: 'warning',
          message: '<strong>' + msg + '</strong>',
        });
      }, 1000);
      return false;
    } else if (
      this.selectedObj.associatePatientsCount &&
      this.selectedObj.associatePatientsCount != '0'
    ) {
      this.selectedObj = '';
      let msg = `You are not allowed to invite a caregiver associated with a patient`;
      const notify = $.notify(msg);
      setTimeout(function () {
        notify.update({
          type: 'warning',
          message: '<strong>' + msg + '</strong>',
        });
      }, 1000);
      return false;
    }

    const userDetails = this._structureService.getUserdata();
    if (
      this.selectedObj.tenantid == userDetails.crossTenantId &&
      this.selectedObj.user == 'real'
    ) {
      let status =
        this.selectedObj.status && this.selectedObj.status == '2'
          ? 'pending'
          : this.selectedObj.status && this.selectedObj.status == '0'
          ? 'inactive'
          : 'active';
      let msg = `This ${this.newPatient.get('utype').value}, ${
        this.selectedObj.firstname
      } ${
        this.selectedObj.lastname
      } is  ${status} in the current tenant. You are not allowed to re-invite an  ${status} user within the current tenant.`;
      const notify = $.notify(msg);
      setTimeout(() => {
        notify.update({
          type: 'warning',
          message: '<strong>' + msg + '</strong>',
        });
      }, 1000);
      this.selectedObj = '';
      return false;
    } else if (
      this.selectedObj.tenantid != userDetails.crossTenantId &&
      this.selectedObj.user == 'virtual'
    ) {
      this.selectedObj = '';
      let msg = `Virtual user is in different tenant. Not able to proceed`;
      const notify = $.notify(msg);
      setTimeout(function () {
        notify.update({
          type: 'warning',
          message: '<strong>' + msg + '</strong>',
        });
      }, 1000);
      return false;
    }

    if (
      (this.newPatient.get('utype').value == 'staff' ||
        this.newPatient.get('utype').value == 'partner') &&
      this.selectedObj.user == 'virtual'
    ) {
      this.selectedObj = '';
      let msg = `You are not allowed to convert a virtual patient to ${
        this.newPatient.get('utype').value
      }`;
      const notify = $.notify(msg);
      setTimeout(function () {
        notify.update({
          type: 'warning',
          message: '<strong>' + msg + '</strong>',
        });
      }, 1000);
      return false;
    }
    this.logger.info(this.selectedObj);
    this.prepopulateData();
  }

  prepopulateData() {
    this.logger.info(this.selectedObj);
    if (this.selectedObj.user == 'real') {
      $('#email-address').val(this.selectedObj.username);
      if (
        this.selectedObj.country_code &&
        this.selectedObj.country_code != null
      ) {
        $('#country_code').intlTelInput(
          'setNumber',
          this.selectedObj.country_code
        );
      }
      if (this.selectedObj.dob) {
        let ar = this.selectedObj.dob.split('-');
        this.logger.info(ar);
        if (ar.length) {
          $('.year').val(parseInt(ar[0])).trigger('change');
          $('.month')
            .val(parseInt(ar[1]) - 1)
            .trigger('change');
          setTimeout(() => {
            this.logger.info(ar[2]);
            $('.day').val(parseInt(ar[2])).trigger('change');
          }, 1);
        }
      }
      this.newPatient.patchValue({
        pcellno: this.selectedObj.mobile
          ? this.selectedObj.mobile.replace(/-/g, '')
          : '',
        pemail: this.selectedObj.username ? this.selectedObj.username : '',
        zipcode: this.selectedObj.zip ? this.selectedObj.zip : '',
        pfname: this.selectedObj.firstname ? this.selectedObj.firstname : '',
        plname: this.selectedObj.lastname ? this.selectedObj.lastname : '',
      });

      if (
        this.selectedObj.country_code &&
        this.selectedObj.country_code != null
      ) {
        this.newPatient.patchValue({
          countryCode: this.selectedObj.country_code,
        });
      } else {
        const cntryDetails = $('#country_code').intlTelInput(
          'getSelectedCountryData'
        );
        this.userInfo.countryCode = '+' + cntryDetails.dialCode;
        this.newPatient.patchValue({
          countryCode: this.userInfo.countryCode,
        });
      }
    }
  }

  registerBack() {
    this.logger.info('Here');
    if (this.tabSelect == 'patient') {
      this.router.navigate(['/add-user/search-user']);
    } else {
      setTimeout(() => {
        this.formGroupDirective.resetForm();
        this.emitEventToSelectSites();
        this.resetTag();
        this.ngOnInit();
        $('#email-address').val('');
        this.siteId = 0;
      }, 0);
    }
  }

  cancelAdd() {
    this.logger.info('Here');
    localStorage.setItem('contactAdded', '');
    if (this.tabSelect == 'patient' && this.editId) {
      this.router.navigate(['/add-user/search-user']);
    } else {
      this.router.navigate(['/inbox']);
    }
  }

  showAlternateTab() {
    this.logger.info('next clicked');
    this.activePatientTab = 'alternate-contacts';
  }

  resetForm() {
    this.logger.info('reset called');
    let val = this.tabSelect;

    this.togglePreference('createPassword', false);

    this.newPatient.get('pfname').setValue('');
    this.newPatient.get('plname').setValue('');
    this.newPatient.get('pemail').setValue('');
    this.newPatient.get('userRole').setValue('');
    this.newPatient.get('mrnSys').setValue('');
    this.newPatient.get('mrn').setValue('');
    this.newPatient.get('dob').setValue('');
    this.newPatient.get('partnerRole').setValue('');

    this.newPatient.get('pfname').clearValidators();
    this.newPatient.get('pfname').setValidators([Validators.required]);
    this.newPatient.get('plname').setValidators([Validators.required]);
    this.newPatient.get('plname').updateValueAndValidity();

    if (val === 'staff') {
      this.newPatient.get('userRole').setValidators([Validators.required]);
      this.newPatient.get('userRole').updateValueAndValidity();
      this.newPatient.get('mrnSys').clearValidators();
      this.newPatient.get('mrnSys').updateValueAndValidity();

      if (
        this.userData.config
          .make_staffid_field_mandatory_in_staff_invite_page &&
        this.userData.config
          .make_staffid_field_mandatory_in_staff_invite_page == '1'
      ) {
        this.newPatient.get('mrn').clearValidators();
        this.newPatient.get('mrn').setValidators([Validators.required]);
        this.newPatient.get('mrn').updateValueAndValidity();
      } else {
        this.newPatient.get('mrn').clearValidators();
        this.newPatient.get('mrn').updateValueAndValidity();
      }

      this.newPatient.get('dob').clearValidators();
      this.newPatient.get('dob').updateValueAndValidity();

      this.newPatient.get('partnerRole').clearValidators();
      this.newPatient.get('partnerRole').updateValueAndValidity();

      this.newPatient.get('createPassword').setValue(false);
      this.newPatient.get('createPassword').clearValidators();
      this.newPatient.get('createPassword').updateValueAndValidity();

      this.newPatient.get('temppassword').clearValidators();
      this.newPatient.get('temppassword').updateValueAndValidity();

      this.newPatient.get('repeatPassword').clearValidators();
      this.newPatient.get('repeatPassword').updateValueAndValidity();

      this.newPatient.get('patientUsername').clearValidators();
      this.newPatient.get('patientUsername').updateValueAndValidity();

      setTimeout(() => {
        $('select[name="userRole"]').select2({
          placeholder: $('select[name="userRole"] option:first').text(),
        });
      }, 100);
    } else if (val === 'patient') {
      this.newPatient.get('patientEmail').setValue('');

      this.newPatient.get('userRole').clearValidators();
      this.newPatient.get('userRole').updateValueAndValidity();
      this.newPatient.get('partnerRole').clearValidators();
      this.newPatient.get('partnerRole').updateValueAndValidity();

      this.newPatient.get('createPassword').setValue(false);
      this.newPatient.get('createPassword').clearValidators();
      this.newPatient.get('createPassword').updateValueAndValidity();

      this.newPatient.get('temppassword').clearValidators();
      this.newPatient.get('temppassword').updateValueAndValidity();

      this.newPatient.get('repeatPassword').clearValidators();
      this.newPatient.get('repeatPassword').updateValueAndValidity();

      this.newPatient.patchValue({
        use_mrn: false,
        userTag: '',
        partnerTag: '',
      });

      setTimeout(() => {
        $('select[name="userTag"]').select2({
          placeholder: $('select[name="userTag"] option:first').text(),
        });

        $('select[name="partnerTag"]').select2({
          placeholder: $('select[name="partnerTag"] option:first').text(),
        });

        $('select[name="staffTag"]').select2({
          placeholder: $('select[name="staffTag"] option:first').text(),
        });

        $('#dob-date-picker').combodate({
          format: 'YYYY-MM-DD',
          template: 'MMM D YYYY',
          minYear: 1900,
          firstItem: 'empty',
          maxYear: new Date().getFullYear(),
          customClass: 'form-control select2 dob-sec',
          smartDays: true,
        });

        $('.month, .day, .year').select2();
      }, 100);
    } else {
      this.newPatient.get('companyNursingAgency').setValue('');

      this.newPatient.get('userRole').clearValidators();
      this.newPatient.get('userRole').updateValueAndValidity();
      this.newPatient.get('mrnSys').clearValidators();
      this.newPatient.get('mrnSys').updateValueAndValidity();
      this.newPatient.get('mrn').clearValidators();
      this.newPatient.get('mrn').updateValueAndValidity();
      this.newPatient.get('dob').clearValidators();
      this.newPatient.get('dob').updateValueAndValidity();
      this.newPatient.get('partnerRole').setValidators([Validators.required]);
      this.newPatient.get('partnerRole').updateValueAndValidity();

      this.newPatient.get('createPassword').clearValidators();
      this.newPatient.get('createPassword').updateValueAndValidity();

      this.newPatient.get('temppassword').clearValidators();
      this.newPatient.get('temppassword').updateValueAndValidity();

      this.newPatient.get('repeatPassword').clearValidators();
      this.newPatient.get('repeatPassword').updateValueAndValidity();

      setTimeout(() => {
        $('select[name="partnerRole"]').select2({
          placeholder: $('select[name="partnerRole"] option:first').text(),
        });

        $('#country_code').intlTelInput();

        $('#country_code').intlTelInput('setCountry', this.initialUserCountry);

        const cntryDetails = $('#country_code').intlTelInput(
          'getSelectedCountryData'
        );

        $('#country_code').intlTelInput(
          'setNumber',
          '+' + cntryDetails.dialCode
        );

        this.newPatient.patchValue({
          countryCode: this.userInfo.countryCode,
        });
      }, 100);
    }

    this.newPatient.get('userRole').updateValueAndValidity();

    setTimeout(() => {
      $('body').off('keyup', '#email-address');
      $('body').on('keyup', '#email-address', () => {
        this.newPatient.patchValue({
          pemail: $('#email-address').val(),
        });
      });

      $('#country_code').intlTelInput();
      $('body').off('countrychange', '#country_code');
      $('body').on('countrychange', '#country_code', (e, countryData) => {
        if (countryData.dialCode) {
          this.userCountry = countryData.iso2;
          this.userInfo.countryCode = '+' + countryData.dialCode;
          this.newPatient.patchValue({
            countryCode: this.userInfo.countryCode,
          });
          $('#country_code').intlTelInput(
            'setNumber',
            '+' + countryData.dialCode
          );
        }
      });

      $('#country_code').intlTelInput( 'setCountry', this.initialUserCountry);
      const countryDetails = $('#country_code').intlTelInput(
        'getSelectedCountryData'
      );
      this.userInfo.countryCode = '+' + countryDetails.dialCode;
      $('#country_code').intlTelInput('setNumber', this.userInfo.countryCode);
      this.newPatient.patchValue({
        countryCode: this.userInfo.countryCode,
      });

      $('#dob-date-picker').combodate({
        format: 'YYYY-MM-DD',
        template: 'MMM D YYYY',
        minYear: 1900,
        firstItem: 'empty',
        maxYear: new Date().getFullYear(),
        customClass: 'form-control select2 dob-sec',
        smartDays: true,
      });

      $('.month, .day, .year').select2();
      $('select[name="userRole"]').select2({
        placeholder: $('select[name="userRole"] option:first').text(),
      });
    }, 100);
  }
  getSiteIds(siteId: any) {
    this.siteId = siteId.siteId;
    this.siteRequired =
      ((isBlank(this.siteId) || this.siteId == '0') && this.submitted) ||
      ((isBlank(this.siteId) || this.siteId == '0') && this.siteChoosed);
    this.siteChoosed = true;
  }
  hideDropdown(hideItem: any) {
    this.hideSiteSelection = hideItem.hideItem;
  }
  emitEventToSelectSites(): void {
    this.eventsSubject.next();
  }

     getHomeSiteId(homeSiteId: any) {
          this.homeSiteId = homeSiteId;
      }
  reloadDropdowns() {
    this.logger.info(this.userInfo);
    this.logger.info(this.newPatient.value);

    setTimeout(() => {
      $('#dob-date-picker').combodate({
        format: 'YYYY-MM-DD',
        template: 'MMM D YYYY',
        minYear: 1900,
        firstItem: 'empty',
        maxYear: new Date().getFullYear(),
        customClass: 'form-control select2 dob-sec',
        smartDays: true,
      });

      $('.month, .day, .year').select2();

      $('#country_code').intlTelInput();

      $('#country_code').intlTelInput('setCountry', this.initialUserCountry);

      const countryDetails = $('#country_code').intlTelInput(
        'getSelectedCountryData'
      );

      $('#country_code').intlTelInput('setNumber', countryDetails.dialCode);
      this.newPatient.patchValue({
        countryCode: countryDetails.dialCode
      });
      if (this.newPatient.get('dob').value != '') {
        const format = 'YYYY-MM-DD';
        let date = new Date(this.newPatient.get('dob').value);
        let dob = moment(date).format(format);
        let ar = dob.split('-');
        this.logger.info(ar);
        if (ar.length) {
          $('.year').val(parseInt(ar[0])).trigger('change');
          $('.month')
            .val(parseInt(ar[1]) - 1)
            .trigger('change');

          this.logger.info(ar[2]);
          $('.day').val(parseInt(ar[2])).trigger('change');
        }
      }
    }, 1);
  }

  UploadBtnAction() {
    $('#data-modal-upload').modal('show');
    this.buttonDisabled = true;
    this.showLoading = false;
    $('#bulkDirectFile').val('');
    $('#result-report').replaceWith('<p id="result-report"><p>');
  }

  uploadFile(event) {
    $('#result-report').replaceWith('<p id="result-report"><p>');
    let fileList: FileList = event.target.files;
    let reader = new FileReader();
    let validExtns = ['csv', 'xls', 'xlsx', 'ods'];
    this.logger.info(fileList);
    if (fileList.length > 0) {
      let fileExt = fileList[0].name.split('.').pop().toLowerCase();
      if (validExtns.indexOf(fileExt) != -1) {
        this.buttonDisabled = false;
        this.signDocFile = fileList[0];
        reader.readAsDataURL(fileList[0]);
        reader.onload = () => {
          this.formData.get('file').patchValue({
            filename: fileList[0].name,
            filetype: fileList[0].type,
            value: (<string>reader.result).split(',')[1],
          });
        };
      } else {
        this.buttonDisabled = true;
        $('#result-report').replaceWith(
          '<p id="result-report" class="error">Invalid File Type. Please upload csv / xls / xlsx / ods file.<p>'
        );
      }
    } else {
      this.buttonDisabled = true;
    }
  }

  saveUploadFile() {
    this.errorCount = 0;
    let fileName = this.formData.value['file'].filename;
    let fileExt = fileName.split('.').pop().toUpperCase();
    swal(
      {
        title: 'Are you sure?',
        text: 'You are going to import multiple users from the ' + fileExt,
        type: 'warning',
        showCancelButton: true,
        cancelButtonClass: 'btn-default',
        confirmButtonClass: 'btn-warning',
        confirmButtonText: 'Ok',
        closeOnConfirm: true,
      },
      () => {
        this.showLoading = true;
        this.buttonDisabled = true;
        this._structureService
          .uploadImportFile(this.formData.value['file'])
          .then((importResult) => {
            this.importResult = importResult;
            this.logger.info(this.importResult);
            if (this.importResult.status == 1) {
              $('#result-report').append(
                '<b>Upload Status :</b><br>Successfully uploaded the file for Import'
              );
            } else {
              $('#result-report').replaceWith(
                '<p id="result-report" class="error"><b>Upload Status: </b><br>' +
                  this.importResult.message +
                  '<br><p>'
              );
            }
          });
        this.showLoading = false;
      }
    );
  }

  /** 
         * onCheckboxEmailAsUsernameChange() returns void 
         * Used to set email field value into username field using
          @param <object> event  
       */

  onCheckboxEmailAsUsernameChange = (event) =>
    event.target.checked
      ? this.newPatient.controls['patientUsername'].setValue(
          this.newPatient.controls.patientEmail.value
        )
      : this.newPatient.controls['patientUsername'].setValue('');
      
      checkUsernameSameAsEmail() {
        //This method will be called on username's blur event
        //It checks whether user name and email id same, if not, then
        //unchek the checkbox, else check the checkbox
        this.newPatient.controls.checkboxEmailUsernamePartner.setValue(
          this.newPatient.value.patientUsername !==
            this.newPatient.value.patientEmail
            ? false
            : true
        );
      }
  navigateToNext(url) {
    this._structureService.navigateToPage(url);
  }
  getStatus(status) {
    if (+status === 1) {
      return 'LABELS.ACTIVE';
    }
    if (+status === 4) {
      return 'LABELS.PENDING';
    }
    return 'LABELS.INACTIVE';
  }
}
