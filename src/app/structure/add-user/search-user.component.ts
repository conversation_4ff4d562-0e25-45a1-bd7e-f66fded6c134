import { DatePipe } from '@angular/common';
import { Component, ElementRef, HostListener, OnInit, Renderer } from '@angular/core';
import { FormControl, FormGroup,NgForm,
  FormBuilder,
  Validators,
  ValidatorFn,
  AbstractControl,
  FormGroupDirective } from '@angular/forms';

import { ActivatedRoute, Router } from '@angular/router';

import { DateTimePipe } from '../forms/formp.pipe';
import { StructureService } from '../structure.service';
import {
  SharedService
} from '../../structure/shared/sharedServices';
import { GlobalDataShareService } from '../../structure/shared/global-data-share.service';
import { Subject } from 'rxjs';
import { removePhoneNumberMasking } from 'app/utils/utils';
import { CONSTANTS } from 'app/constants/constants';
import { ToolTipService } from '../tool-tip.service';
import { StaticDataService } from '../static-data.service';

declare var $: any;
declare var jQuery: any;
declare var swal: any;
let moment = require('moment/moment');
@Component({
  selector: 'app-search-user',
  templateUrl: './search-user.component.html'
})
export class SearchUserComponent implements OnInit {
  dTable; datam; searchData: any = [];
  userDetails: any;
  selectedData;
  associatePatients = [];
  userRole;
  noRecordMessage: boolean = false;
  searchInvitesForm: FormGroup;
  patientDetails: FormGroup; onBehalfPatient;
  iscareGiverFlag: boolean; externalSystemName = ''; onBehalfexternalSystemName = '';
  searchResetFlag=0;
  dataLoadingMsg=false;
  searchInTenantIds;
  userConfig:any;
  totalCountInviteSearch; searchString;
  buttonDisabled:any;
  showLoading: any;
  errorCount:any;
  domainName;
  signDocFile;
  formData;
  fileChangevalue;
  result;
  importResult: any;

  editSiteData="";
  hideSiteSelection : boolean;
  siteId : any;
  eventsSubject: Subject<void> = new Subject<void>();
  filterType=true;
  selectSiteId:any;
  readonly maskPhoneNumber = CONSTANTS.phoneMask;
  labelSiteFilter = '';
  labelSite = '';
  constructor(
    private router: Router, 
    private route: ActivatedRoute,
    renderer: Renderer,
    elementRef: ElementRef,
    public _structureService: StructureService,
    private _sharedService: SharedService,
    private datePipe: DatePipe,
    public _GlobalDataShareService:GlobalDataShareService,
    private dateTimePipe: DateTimePipe,
    private toolTipService: ToolTipService,
    private staticDataService: StaticDataService) {

      this.formData = new FormGroup({
            file: new FormControl()
      });
    
    this.searchInvitesForm = new FormGroup({
      searchInput: new FormControl()
    });
    this.patientDetails = new FormGroup({
      pEmailDetail: new FormControl(),
      pFNameDetail: new FormControl(),
      pLNameDetail: new FormControl(),
      pCellNoDetail: new FormControl(),
      pDOBDetail: new FormControl(),
      pBranchDetail: new FormControl(),
      pCountryCodeDetails: new FormControl(), 
      pUserTagsDetail: new FormControl(), 
      pMRNDetail: new FormControl(), 
      pCreatedOnDetail: new FormControl(), 
      pLastLoginDetail: new FormControl(), 
      pSourceDetail: new FormControl(), 

      cFNameDetail: new FormControl(),
      cLNameDetail: new FormControl(),
      cMobileDetail: new FormControl(),
      cEmailDetail: new FormControl(),
      cCountryCodeDetails: new FormControl(), 
      cCellNoDetail: new FormControl(),
      cBranchDetail: new FormControl(),
      cMRNDetail: new FormControl(),
      cpPatient: new FormControl(),
      cpDob: new FormControl(),
      cCreatedOnDetail: new FormControl(),
      cLastLoginDetail: new FormControl(),
        cSourceDetail: new FormControl(), 
    });
    renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
      const targetElement = $(event.target);
      var patientId = targetElement.data('id');
      const selectedItem = this.searchData.filter(
        x => Number(x.userid) === Number(targetElement.data('id'))
      );
    
      localStorage.setItem('selectedRow', JSON.stringify( selectedItem[0]));
        if (event.target.id === 'inviteCareGiverClick') {
          localStorage.setItem('isCareGiver', 'true');
          const activityData = {
            activityName: 'Invite caregiver',
            activityType: "user enrollment",
            activityDescription: "Invite caregiver for the patient " + selectedItem[0].firstname + " " + selectedItem[0].lastname
          };
          this._structureService.trackActivity(activityData);


      } else if (event.target.id === 'invitePatientClick') {
        this._sharedService.goToInnerPage = true;
          localStorage.setItem('isCareGiver', 'false');
          const activityData = {
            activityName: 'Edit Patient',
            activityType: "user enrollment",
            activityDescription: "Invite for the patient " + selectedItem[0].firstname + " " + selectedItem[0].lastname
          };
          this._structureService.trackActivity(activityData);
          this.navigateToNext(`add-user/add/${selectedItem[0].userid}`);
      }
        else if (event.target.id === 'patientDetailsClick') { 
          this.editSiteData = "";
          this.filterType =true;
          const activityData = {
            activityName: 'Patient details',
            activityType: "user enrollment",
            activityDescription: "View details of " + selectedItem[0].firstname + " " + selectedItem[0].lastname
          };
          this._structureService.trackActivity(activityData);

          this.patientDetails.reset();
          this.externalSystemName = selectedItem[0] ? selectedItem[0].externalSystemName : ''
          if (selectedItem[0].isCaregiver === true)
          {
            this.iscareGiverFlag = true;
          } else {
            this.iscareGiverFlag = false;
          }
          let createdOn='';
          let lastLogin = '';let source='';
          if (selectedItem[0].registration_type == 0) {
            source= "Manual";
          } else if (selectedItem[0].registration_type == 1 || selectedItem[0].registration_type == 2) {
            source= "Enroll";
          } else if (selectedItem[0].registration_type == 3) {
            source= "CHIE";
          } else if (selectedItem[0].registration_type == 4) {
            source= "Import";
          } else if (selectedItem[0].registration_type == 6) {
            source= "User Center";
          }
           if ( selectedItem[0].created_at) {
              if ( selectedItem[0].created_at == "2019-02-11T06:38:09.000Z" ||  
              selectedItem[0].created_at == "2019-02-13T04:09:06.000Z" ||  
              selectedItem[0].created_at == "2019-02-20T12:02:05.000Z" || 
               selectedItem[0].created_at == "2018-09-28T04:35:24.000Z") {
                createdOn= "-";
              } else {
                createdOn= this.dateTimePipe.transform( selectedItem[0].created_at);
              }
            } else {
              createdOn= '';
            }
            if( selectedItem[0].password &&  selectedItem[0].password==true){
            if( selectedItem[0].last_login && selectedItem[0].last_login!=null){
              lastLogin= this.datePipe.transform(selectedItem[0].last_login*1000,"mediumDate")+' '+this.datePipe.transform( selectedItem[0].last_login*1000,"shortTime");           
            }else{
              lastLogin= "Not Logged in";
            }            
          }else{
            lastLogin= "Virtual";
          }
        this._structureService.getPatient(patientId).then((data) => {
          if (data['getSessionTenant']) {
            if (data['getSessionTenant'].patientUsers.length > 0) {
              this.selectedData = JSON.parse(JSON.stringify(data['getSessionTenant'].patientUsers[0]));
              var dob = '';
              if (this.selectedData.dateOfBirth) {
                var dateParts = this.selectedData.dateOfBirth.split('-');
                let dateParts1 = dateParts[1].length < 2 ? '0' + dateParts[1] : '' + dateParts[1];
                var dateParts2 = dateParts[2].length < 2 ? '0' + dateParts[2] : '' + dateParts[2];
                var dateParts3 = dateParts[0].length < 2 ? '0' + dateParts[0] : '' + dateParts[0];
                dob = dateParts1 + "/" + dateParts2 + "/" + dateParts3;
              }
              this.editSiteData = this.selectedData ? this.selectedData.siteId : this.editSiteData;
              if(this.editSiteData == "" || this.editSiteData == undefined){
                this.filterType =false;
                this.editSiteData == "";
               }
              var email = '';
              if (!this.selectedData.emails[0].value.includes('unknown_')) {
                email = this.IsEmail(this.selectedData.emails[0].value);
              }
              if (this.iscareGiverFlag == false) {
               
                this.patientDetails.patchValue({
                  pFNameDetail: this.selectedData.firstName,
                  pLNameDetail: this.selectedData.lastName,
                  pCellNoDetail: this.selectedData.mobile ? removePhoneNumberMasking(this.selectedData.mobile) : '',
                  pDOBDetail: dob,
                  pBranchDetail: selectedItem[0].tenantName,
                  pCountryCodeDetails: this.selectedData.countryCode,
                  pEmailDetail: email,
                  pMRNDetail: selectedItem[0].mrn,
                  pCreatedOnDetail:createdOn,
                  pLastLoginDetail: lastLogin,
                  pSourceDetail: source
                });
              }
              else {
               
                var enrollSearch = {
                  "operation": "getUserDetails",
                  // "tenantId": this.userDetails.tenantId,
                  "userId": patientId
                };
                this._structureService.searchInvites(enrollSearch).then((patientData) => {
                  this.onBehalfPatient = patientData;
                });
    
                // if (this.selectedData.associatePatients) {
                //   this.selectedData.associatePatients.map(function (associatedUser) {
                //     if (associatedUser.dateOfBirth) {
                //       var dateParts = associatedUser.dateOfBirth.split('-');
                //       let dateParts1 = dateParts[1].length < 2 ? '0' + dateParts[1] : '' + dateParts[1];
                //       var dateParts2 = dateParts[2].length < 2 ? '0' + dateParts[2] : '' + dateParts[2];
                //       var dateParts3 = dateParts[0].length < 2 ? '0' + dateParts[0] : '' + dateParts[0];
                //       dob = dateParts1 + "-" + dateParts2 + "-" + dateParts3;
                //       associatedUser.dateOfBirth = dob;
                //     }

                //   });

                // }
                this.patientDetails.patchValue({
                  cFNameDetail: selectedItem[0].firstname,
                  cLNameDetail: selectedItem[0].lastname,
                  cCellNoDetail: selectedItem[0].mobile ? selectedItem[0].mobile.replace(/[|&;$%@"<>()+, -]/g, "") : '',
                  cBranchDetail: selectedItem[0].tenantName,
                  cCountryCodeDetails: selectedItem[0].country_code,
                  cEmailDetail: email,
                  cMRNDetail: selectedItem[0].mrn,
                   cCreatedOnDetail:createdOn,
                  cLastLoginDetail: lastLogin, cSourceDetail: source
                });
              }
            } else {
              this.editSiteData = selectedItem[0] ? selectedItem[0].siteId : '';
              if(this.editSiteData == "" || this.editSiteData == undefined){
                this.filterType =false;
                this.editSiteData == "";
              }
                var dob = '';
                if (selectedItem[0].dob) {
                  var dateParts = selectedItem[0].dob.split('-');

                  let dateParts1 = dateParts[1].length < 2 ? '0' + dateParts[1] : '' + dateParts[1];
                  var dateParts2 = dateParts[2].length < 2 ? '0' + dateParts[2] : '' + dateParts[2];
                  var dateParts3 = dateParts[0].length < 2 ? '0' + dateParts[0] : '' + dateParts[0];
                  dob = dateParts1 + "/" + dateParts2 + "/" + dateParts3;
              }
              var email = '';
              if (!selectedItem[0].username.includes('unknown_')) {
                email = this.IsEmail(selectedItem[0].username);
              }
              if (this.iscareGiverFlag == false) {
                this.patientDetails.patchValue({
                  pFNameDetail: selectedItem[0].firstname,
                  pLNameDetail: selectedItem[0].lastname,
                  pCellNoDetail: selectedItem[0].mobile ? removePhoneNumberMasking(selectedItem[0].mobile) : '',
                  pDOBDetail: dob,
                  pBranchDetail: selectedItem[0].tenantName,
                  pCountryCodeDetails: selectedItem[0].country_code,
                  pEmailDetail: email,
                  pMRNDetail: selectedItem[0].mrn,
                  pCreatedOnDetail: createdOn,
                  pLastLoginDetail: lastLogin,
                  pSourceDetail: source
                });
              } else {
              
                var enrollSearch = {
                  "operation": "getUserDetails",
                  // "tenantId": this.userDetails.tenantId,
                  "userId": patientId
                };
                this._structureService.searchInvites(enrollSearch).then((patientData) => {
                  this.onBehalfPatient = patientData;
                });
              this.patientDetails.patchValue({
                cFNameDetail: selectedItem[0].firstname,
                cLNameDetail: selectedItem[0].lastname,
                cCellNoDetail: selectedItem[0].mobile ? selectedItem[0].mobile.replace(/[|&;$%@"<>()+, -]/g, "") : '',
                cBranchDetail: selectedItem[0].tenantName,
                cCountryCodeDetails: selectedItem[0].country_code,
                cEmailDetail: email,
                cMRNDetail: selectedItem[0].mrn,
                cCreatedOnDetail: createdOn,
                cLastLoginDetail: lastLogin, cSourceDetail: source
              });
              }
            }
          }
        
        });
        console.log("this.editSiteData",this.editSiteData)
          $('#patientDetailModel').modal('show');
      }
    });
  }

  ngOnInit() {
    this.labelSite = this.toolTipService.getTranslateData( this.staticDataService.getSiteLabelBasedOnMultiAdmission('SITE'));
    this.labelSiteFilter = this.staticDataService.getSiteLabelBasedOnMultiAdmission('FILTER_BY_SITES');
    this.buttonDisabled = true;
    this.showLoading = false;
    this.domainName = this._structureService.domainName;

   this.searchInTenantIds = this._GlobalDataShareService.getTenantIds(this._structureService.getCookie('userPrivileges'));
    var self = this;
    this.userConfig = this._structureService.getUserdata().config; 
    console.log('this.userConfig.tenantId', this.userConfig.tenantId);   
    this.userDetails = JSON.parse(this._structureService.userDetails); 
    console.log('this.userDetails', this.userDetails);
    console.log('this.userDetails.tenantId', this.userDetails.tenantId);
    $("#button-search").prop("disabled", "disabled");
     $("#button-clear").prop("disabled", "disabled");
   
    $("#searchTxt").on('keyup', function () {
      if ($(this).val() != "") {
        $("#button-search").prop("disabled", false);
        $("#button-clear").prop("disabled", false);

      } else {
        $("#button-search").prop("disabled", "disabled");
          $("#button-clear").prop("disabled", "disabled");
      }
    });
    $("#searchTxt").on('keydown', function (e) {
      if (e.which == 13) {
        self.searchResetFlag == 0;
        self.searchString = $("#searchTxt").val();
        setTimeout(() => {
          $('.data-table').show();
        }, 2000);
        self.loadTable('refresh');
        const activityData = {
          activityName: 'Search Patient invite',
          activityType: "user enrollment",
          activityDescription: "Search patient invites with keyword " + self.searchString + " as first name ,last name or username.",
        };
        self._structureService.trackActivity(activityData);
      }
    }
    );
    this.searchClear();
  if (localStorage.getItem('storeSearchText'))
   {
     this.searchInvitesForm.patchValue({
       searchInput: localStorage.getItem('storeSearchText')
     })
     this.loadTable('refresh');

   }
   localStorage.setItem('addUserTab', 'patient');
}
  loadTable(refresh="") {
    this.dataLoadingMsg=true;
    let self = this;
    let datas: any;
    if (refresh && refresh != "") {
      this._structureService.resetDataTable();
    }
    if (this.dTable) {
      this.dTable.destroy();
    }
    this.userRole = self._structureService.getCookie('userRole');
    $(".data-table").html('<table class= "table table-hover table-responsive" id = "patient-list" width = "100%"> </table>');
    this.dTable = $('#patient-list').DataTable({
      autoWidth: false,
      responsive: true,
      bServerSide: true,
      bpagination: true,
      stateSave: true,
      bInfo: true,
      bFilter :false,
      lengthMenu: [[25, 50,], [25, 50]],
      ajax: function (dat, callback, settings) {
        let orderData;
        let searchText = '';
        let orderby;
        let siteIds;
        let result;
        var i = dat.order[0].column ? dat.order[0].column : '';
        orderby =dat.order[0].dir ? dat.order[0].dir : '';
        if(i != 0)
        {
         orderData =dat.columns[i].data ? dat.columns[i].data : '';
        }
        const userDetails = self._structureService.getUserdata();
        if(userDetails.mySites) {
        console.log('site', userDetails.mySites);
        if(Array.isArray(userDetails.mySites)) {
        result = (userDetails.mySites).map((item) => { return item.id }).join(',');
        } 
        /* else {          
          result = userDetails.mySites.id;
        } */
        console.log(result);
        }
        if (settings.oAjaxData.start != 0 && self.datam && self.datam.aaData && self.datam.aaData.length == 1 && settings._iDisplayStart != 0 && searchText == '') {
          settings.oAjaxData.start = settings.oAjaxData.start - settings.oAjaxData.length;
          settings._iDisplayStart = settings.oAjaxData.start;
        }
        const formdata = self.searchInvitesForm.value;
        if(formdata.searchInput &&  self.searchResetFlag == 0){
          dat.start = 0;
          settings.oAjaxData.search.value = formdata.searchInput;
          settings.oAjaxData.start = 0;
          settings._iDisplayStart = 0;
          self.searchResetFlag = 1;
        }
        if(self.selectSiteId == 0 || self.selectSiteId == undefined) {
          siteIds = result;
        } else {
          console.log(self.selectSiteId);
          siteIds = (self.selectSiteId).toString();
        }
        this.userData = JSON.parse(self._structureService.userDetails);
        var enrollSearch = { 
          "operation": "enrollSearch", 
          // "tenantId": self.userDetails.tenantId, 
          "firstname": formdata.searchInput,
          "limit" : dat.length,
          "offset" : dat.start,
          "orderData" : orderData,
          "orderby" : orderby,
          "searchText": formdata.searchInput,
          "searchInTenantIds": self.searchInTenantIds.join(),
          "siteIds": siteIds
          // "organization_master_id":parseInt(this.userData.organizationMasterId)
        };
        self._structureService.searchInvites(enrollSearch).then((dataa) => {
          $("#button-search").prop("disabled", "disabled");
     
          datas = {};
          self.datam = {};
          if (dat.start == 0) {
            this.totalCt = dataa['totalCount'];
            self._structureService.setCookie('totalCountInvitePatientSearch', this.totalCt, 1);
            self.totalCountInviteSearch= this.totalCt;
          } else {
            if (self.totalCountInviteSearch) {
              this.totalCt = self.totalCountInviteSearch;
            }
            else {
              this.totalCt = self._structureService.getCookie('totalCountInvitePatientSearch');
              self.totalCountInviteSearch = this.totalCt;
            }
          }
          datas = dataa['data'] ? dataa['data'] : [];
          self.searchData = datas;
          let draw;
          let total;
          if (datas && datas.length == 0 && searchText == '') {
            draw = 0;
            total = 0;   
          }
          else {
            draw = dat.draw;
            total = this.totalCt;
          }
          self.datam = {
            "draw": draw,
            "recordsTotal": total,
            "recordsFiltered": total,
            "aaData": datas
          };
          callback(self.datam);
          self.dataLoadingMsg = false;

        });
      },
     initComplete: function () {
    
      },
      fnDrawCallback: function () {
        if (self.searchData.length == 0) {
          var table = $('#patient-list').DataTable();
          table.destroy(true);
          self.noRecordMessage = true;
          self.dataLoadingMsg = false;
        } 
        else {
          self.noRecordMessage = false;
          self.dataLoadingMsg = true;
        }
      },
      columns: [
        { title: "#" },
        { title: "First Name"},
        { title: "Last Name", data: 'lastname' },        
        { title: "DOB", data: 'dob' },
        { title: this.labelSite, data: 'siteName' },
        { title: "MRN", data: 'mrn' },
        { title: "Source", data: 'registration_type' },
        { title: "Branch", data: 'tenantName' },
        // { title: "Email", data: 'username' },
        // { title: "Created On", data: 'created_at' },
        { title: "Last Login", data: 'last_login' },       
        { title: "Actions"  }
      ],
      columnDefs: [
        {
          data: null,
          orderable: false,
          width: '10%',
          targets: 0,
          render: function (data, type, row, meta) {
            let roNo = meta.row + 1 + meta.settings._iDisplayStart;
            return roNo;
          }
         
        },
        {
          data: null,
          orderable: false,
          width: '15%',
          render: (data) => {
            if (data.isCaregiver == false) {
              return data.firstname;
            } else {
              return data.firstname + '(Caregiver)';
            }
          },
          targets: 1
        },
        {
          data: null,
          orderable: false,
          width: '15%',
          targets: 2
        },        
        {
          data: null,
          orderable: false,
          width: '10%',
          render: (data,type, row) => {
            var dob = '';
            if (row.dob) {
              var dateParts = row.dob.split('-');
              if(dateParts.length>1)
              {
              let dateParts1 = dateParts[1].length < 2 ? '0' + dateParts[1] : '' + dateParts[1];
              var dateParts2 = dateParts[2].length < 2 ? '0' + dateParts[2] : '' + dateParts[2];
              var dateParts3 = dateParts[0].length < 2 ? '0' + dateParts[0] : '' + dateParts[0];
              dob = dateParts1 + "/" + dateParts2 + "/" + dateParts3;
              return dob;
              }else {
              return '';
            }
            } else {
              return '';
            }
           },
          targets: 3
        },
        {
          data: null,
          orderable: true,
          width: '20%',
          visible: (self.userRole != 'Patient' && this.userDetails && this.userDetails.config && (( this.userDetails.mySites && this.userDetails.config.enable_multisite && this.userDetails.mySites.length > 1 && this.userDetails.config.enable_multisite == "1") || (( !this.userDetails.config.enable_multisite || this.userDetails.config.enable_multisite !="1")))),
          targets: 4,
          render: (data) => {
            if (data)
            return data;
            else return "";
          },
        },
        {
          data: null,
          orderable: false,
          width: '20%',
          targets: 5
        },
         {
          data: null,
          orderable: false,
           width: '10%',
           render: (data) => {
             if (data == 0) {
               return "Manual";
             } else if (data == 1 || data == 2) {
               return "Enroll";
             } else if (data == 3) {
               return "CHIE";
             } else if (data == 4) {
               return "Import";
             } else if (data == 6) {
              return "User Center";
            }
           }, 
          targets: 6
        },

        {
          data: null,
          orderable: true,
          width: '20%',
          visible: false,
          targets: 7,
          render: (data) => {
            if (data)
            return data;
            else return "";
          },
        },
        {
          data: null,
          orderable: false,
          width: '10%',
          render: (data, type, row) => {  
            if (row.password && row.password == true) {
              if (data && data != null) {
                return this.datePipe.transform(data * 1000, "mediumDate") + ' ' + this.datePipe.transform(data * 1000, "shortTime");
              } else {
                return "Not Logged in";
              }
            } else {
              return '<span style="font-size: 12px;" class="badge badge-default mr-2 mb-2">Virtual Patient</span>';
            }
          },
          targets: 8
        },
        {
          data: null,
          orderable: false,
          width: '10%',
          targets: 9,
          render: (data) => {
            console.log('dataaaa67', data.caregiver ? data.caregiver:null)
            let caregiverCount = data.caregiver ? data.caregiver.filter(r => r.caregiver_status == 'active'):[];
            console.log('count67', caregiverCount.length);
            console.log(this.userDetails);
          //  if (count.length > 0) { 
            if (data.user =="virtual") {
              let actions = `<div class="btn-group mr-2 mb-2 no-margin">`;
              if ((data.tenantid == this.userDetails.crossTenantId  && caregiverCount.length == 0)|| (data.relation_type_id == 2)) {
                localStorage.setItem('showEditdisabled','');
                actions += `<a class="pull-right btn btn-sm " title="Edit Patient" id="invitePatientClick" data-id="${data.userid}"><i id="invitePatientClick" data-id="${data.userid}" class="fa fa-pencil"></i></a>`;
              } else if (data.tenantid == this.userDetails.crossTenantId && data.caregiver && caregiverCount.length > 0 && data.relation_type_id == 1) {
                actions += `<a class="pull-right btn btn-sm" style="background-color: #9fa4a7;" title = "You are not allowed to edit patients associated with a caregiver."> <i class="fa fa-pencil"> </i> </a> `;
               } else if (data.tenantid != this.userDetails.crossTenantId) { 
                 //added virtual but other tenant.
                actions += `<a class="pull-right btn btn-sm" style="background-color: #9fa4a7;" title = "You are not allowed to edit virtual patients from other branches."> <i class="fa fa-pencil"> </i> </a> `;
              }
              actions += `<a class="pull-right btn btn-sm " title="Details" id="patientDetailsClick" data-id="${data.userid}" > <i id="patientDetailsClick" data-id="${data.userid}" class="fa fa-eye"></i> </a> 
               </div>`;
              // <a class="pull-right btn btn-sm " title = "Remainder On" id = "patientRemainderClick" data - id="${data.userid}" > <i id="patientRemainderClick" data - id="${data.userid}" class="fa fa-toggle-on" > </i> </a > 
              return actions;
            } else if (data.user == "real") {
              let actions = ` <div class="btn-group mr-2 mb-2 no-margin">`;
              if (data.tenantid != this.userDetails.crossTenantId) {
                actions += `<a class="pull-right btn btn-sm" style="background-color: #9fa4a7;" title = "You are not allowed to edit patients from other branches."> <i class="fa fa-pencil"> </i> </a> `;
              }
              //addded real patients of the same branch are not allowed to add patient.
              else if ((data.tenantid == this.userDetails.crossTenantId) && (this.userDetails.config.default_patients_workflow == "caregiver")) {
                actions += `<a class="pull-right btn btn-sm" style="background-color: #9fa4a7;" title = "You are not allowed to edit an Active Patient."> <i class="fa fa-pencil"> </i> </a> `;
              }
              else if (this.userDetails.config.default_patients_workflow == "alternate_contacts" && ((data.tenantid == this.userDetails.crossTenantId))) {
                localStorage.setItem('showEditdisabled','1');
                actions += `<a class="pull-right btn btn-sm " title="Edit Patient" id="invitePatientClick" data-id="${data.userid}"><i id="invitePatientClick" data-id="${data.userid}" class="fa fa-pencil"></i></a>`;
              }                
              actions += ` <a class="pull-right btn btn-sm" title="Details" id="patientDetailsClick" data-id="${data.userid}" ><i id="patientDetailsClick" data-id="${data.userid}" class="fa fa-eye"></i></a>
             
              </div>`;
              return actions;
             }           
          }
        },
      ],
    });
   /* this.dTable.on('order.dt search.dt', () => {
      this.dTable.column(0, { search: 'applied', order: 'applied' }).nodes().each(function (cell, i) {
        cell.innerHTML = i + 1;
      });
    }).draw(); */
  }
  navigateToNext(url) {
    this._structureService.navigateToPage(url);
  }
  addPatientDetails() {
    const userDetails = this._structureService.getUserdata();
    localStorage.setItem('returnFromAlternateContact',"" ) ;
    localStorage.setItem('patientId',"");
    localStorage.setItem('Addclciked',"");
    localStorage.setItem('showEditdisabled', "");
    this._sharedService.alternateData =[];
    var self = this;
    const msg = `Patient record integration is enabled from your EHR and if you add a new patient using this workflow, it will create duplicate records and impact integration back to your EHR. Strongly recommend you request adding the patient to your EHR first instead of adding here`;
    if(userDetails.config.enable_progress_note_integration == 1) {
      swal({
        title: '',
        text: msg,        
        type: 'error',
        showCancelButton: true,
        cancelButtonClass: 'btn-default',
        confirmButtonClass: 'btn-warning',
        confirmButtonText: 'Ok',
        closeOnConfirm: true
      }, 
      // (confirm) => {
      //   this.router.navigate(['/user-registrations/register/false']);   
      // }
           function(isConfirm) {
             if (isConfirm) {
               localStorage.setItem('selectedRow', '');
                  self.router.navigate(['/add-user/add']);
                } else{

                }
            }
      );
    } else {
      self.router.navigate(['/add-user/add']);
    }
    //  else {
    //   this.router.navigate(['/user-registrations/register/false']);   
    // }
  }
 
  searchClick() {
    this.searchData = [];
    localStorage.setItem('returnFromAlternateContact',"" ) ;
    localStorage.setItem('patientId','');
    localStorage.setItem('Addclciked','');
    localStorage.setItem('showEditdisabled','');
    this._sharedService.alternateData =[];
    setTimeout(() => {
      $('.data-table').show();
    }, 2000);
  

    const formdata = this.searchInvitesForm.value;
    this.searchString = formdata.searchInput;
    this.loadTable('refresh');
    localStorage.setItem('storeSearchText', formdata.searchInput);
    const activityData = {
      activityName: 'Search Patient invite',
      activityType: "user enrollment",
      activityDescription: "Search patient invites with " + this.searchString + " as first name ,last name or username.",
    };
    this._structureService.trackActivity(activityData);
  }
  searchClear() {
    localStorage.setItem('storeSearchText', '');
    this.searchInvitesForm.patchValue({
      searchInput:''
    });
    this.searchData = [];
    this.noRecordMessage = false;
    $('.data-table').hide();
    $("#button-clear").prop("disabled", "disabled");
    $("#button-search").prop("disabled", "disabled");
   }
  IsEmail(email) {
    var regex = /^([a-zA-Z0-9_\.\-\+])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
    var validEmail;
    if (!regex.test(email)) {
      validEmail = '';
      return validEmail;
    } else {
      validEmail = email;

      return validEmail;
    }
  }
  
  userCategoryChange1(type) {
    if (type == 'patient') {
      localStorage.setItem('addUserTab', 'patient');
    } else if (type == 'staff') {
      this.searchClear();
      localStorage.setItem('addUserTab', 'staff');
      this.router.navigate(['/add-user/add']);
        
    } else if (type == 'partner') {
      this.searchClear();
      localStorage.setItem('addUserTab', 'partner');
      this.router.navigate(['/add-user/add']);      
    }
  }

  UploadBtnAction() {
    $('#data-modal-upload').modal('show');
    this.buttonDisabled = true;
    this.showLoading = false;
    $('#bulkDirectFile').val('');
    $('#result-report').replaceWith('<p id="result-report"><p>');
  }

  uploadFile(event) {
    $('#result-report').replaceWith('<p id="result-report"><p>');
    let fileList: FileList = event.target.files;
    let reader = new FileReader();
    let validExtns = ['csv', 'xls', 'xlsx', 'ods'];
    console.log(fileList)
    if (fileList.length > 0) {
      var fileExt = fileList[0].name.split('.').pop().toLowerCase();
      if(validExtns.indexOf(fileExt) != -1) {
      this.buttonDisabled = false;
        this.signDocFile = fileList[0];
        reader.readAsDataURL(fileList[0]);
        reader.onload = () => {
          this.formData.get('file').patchValue({
            filename: fileList[0].name,
            filetype: fileList[0].type,
            value: (<string>reader.result).split(',')[1]
          })
        };
      } else {
        this.buttonDisabled = true;
        $('#result-report').replaceWith('<p id="result-report" class="error">Invalid File Type. Please upload csv / xls / xlsx / ods file.<p>');
      }      
    } else {
      this.buttonDisabled = true;
    }
  }

  saveUploadFile() {
    this.errorCount = 0;
    var fileName = this.formData.value['file'].filename;
    var fileExt = fileName.split('.').pop().toUpperCase();    
    swal({
      title: "Are you sure?",
      text: "You are going to import multiple users from the "+fileExt,
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Ok",
      closeOnConfirm: true
    }, () => {
      this.showLoading = true;
      this.buttonDisabled = true;
      this._structureService.uploadImportFile(this.formData.value['file']).then((importResult) => {
        this.importResult = importResult;
        console.log(this.importResult);
        if (this.importResult.status == 1) {
          $("#result-report").append("<b>Upload Status :</b><br>Successfully uploaded the file for Import");
        } else {
          $('#result-report').replaceWith('<p id="result-report" class="error"><b>Upload Status: </b><br>' + this.importResult.message + '<br><p>');          
        }
      });
      this.showLoading = false;
    });
  }
  getSiteIds(siteId: any) {
    this.siteId = siteId.siteId;
  }
  getSiteIdsSearch(siteId: any) {
    this.selectSiteId = siteId.siteId;
    if(this.selectSiteId && this.searchString)
    this.loadTable();
  }
  hideDropdown(hideItem : any){
    this.hideSiteSelection = hideItem.hideItem;
}
}
