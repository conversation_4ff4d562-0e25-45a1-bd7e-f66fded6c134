import { Injectable } from '@angular/core';
import gql from 'graphql-tag';
import { Http, Headers, RequestOptions } from '@angular/http';
import { StructureService } from '../structure.service';
import { configWpMiddlewareWebHook } from '../../../environments/environment';
import { HTTPFetchNetworkInterface } from 'apollo-client';
import {APIs} from '../../constants/apis';
declare var NProgress: any;
declare var moment: any;

const getReferralUsers = gql`
query formSubmission($where: queryArgs) {
  formSubmission(last:10000, where: $where){
    edges {
      node{
        tokenId
        sentData
        referredDate
        tokenStatus
        signInDatetime
        submissionDate
        submissionMode
        apiStatus
        assigneeEmail
        assigneeName
        shortenSmsUrl
        shortenWebUrl
        username
        tempToken
        lastReminderSent
        referralStatus
        expiredOn
        inviteOpen
        userMobile
        userEmail
        assigneeSource
        submissionType
      }
    }
  }
}`;
const getReminderDetails = gql`
query notifyResponse($where: queryArgs) {
  notifyResponse(last:10000, where: $where){
    edges {
      node{
        serviceType
        sentTo
        reminderSentAt
      }
    }
  }
}`;

const getDelegatedRolesByRoleId = gql`
query getSessionTenant($sessionToken: String!, $roleId: Int!) {
    getSessionTenant(sessionToken:$sessionToken){
      delegatedRoles(roleId:$roleId){
          roleId
          delegatedRole{
            displayName
            citusRoleId
            id
          }
      }
     
    }
  }
`;


@Injectable()
export class AddUserService {

  constructor(private _structureService: StructureService, private _http: Http) { }

  getDefaultReferralTokens() {
    const userDetails = this._structureService.getUserdata();
    const args = {
      // 'tenant': userDetails.config.token,
      // 'email': userDetails.username
    };
    //const apiURL = userDetails.config.wp_enroll_url + '/wp-json/api/wp/v2/referral-codes';
     const apiURL = this._structureService.apiBaseUrl + "citus-health/v4/referral-codes-wrapper.php"
    let headers = new Headers();
    headers.append('Content-Type', 'application/X-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    const options = new RequestOptions({ headers: headers });
    NProgress.start();
    const promise = new Promise((resolve, reject) => {
      this._http.post(apiURL, args, options)
        .toPromise()
        .then(
          res => {
            const result = res.json();
            NProgress.done();
            resolve(result);
          },
          err => {
            NProgress.done();
            resolve([]);
          }
        );
    });
    return promise;
  }
  setIniviteServerSidePagination(userType,limit,offset,searchText,searchBy,orderBy,orderColumn,filterKey) {
    const userDetails = this._structureService.getUserdata();
    const args = {
      'tenantId': userDetails.config.token,
      'userType': userType,
      'limit': limit,
      'offset': offset,
      'searchText': searchText,
      "searchBy": searchBy,
      'orderBy' : orderBy,
      'orderColumn':orderColumn,
      'email': userDetails.username,
      'filterKey': filterKey
    };
    const apiURL = userDetails.config.wp_enroll_url + '/wp-json/api/wp/v2/invite-list';
    let headers = new Headers();
    headers.append('Content-Type', 'application/X-www-form-urlencoded');
    const options = new RequestOptions({ headers: headers });
    NProgress.start();
    const promise = new Promise((resolve, reject) => {
      this._http.post(apiURL, args, options)
        .toPromise()
        .then(
          res => {
            const result = res.json();
            NProgress.done();
            resolve(result);
          },
          err => {
            NProgress.done();
            const callBackResult = this.rejectApi(err,args,apiURL);
            resolve(callBackResult);
          }
        );
    });
    return promise;
  }
  rejectApi(err,args,apiURL)
  {
    let headers = new Headers();
    headers.append('Content-Type', 'application/X-www-form-urlencoded');
    const options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
      this._http.post(apiURL, args, options)
        .toPromise()
        .then(
          res => {
            const result = res.json();
            NProgress.done();
            resolve(result);
          },
          err => {
            NProgress.done();
            resolve([]);
          }
        );
    });
    return promise;
  }

  checkInviteList(email) {
    const userDetails = this._structureService.getUserdata();
    const args = {
      // 'tenant': userDetails.config.token,
      'email': email
    };
    //const apiURL = userDetails.config.wp_enroll_url + '/wp-json/api/wp/v2/invite-exists';
     const apiURL = this._structureService.apiBaseUrl + "citus-health/v4/invite-exists-wrapper.php";
    let headers = new Headers();
    headers.append('Content-Type', 'application/X-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    const options = new RequestOptions({ headers: headers });
    NProgress.start();
    const promise = new Promise((resolve, reject) => {
      this._http.post(apiURL, args, options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          NProgress.done();
          resolve(result);
        },
        err => {
          NProgress.done();
          reject(err);
        }
        );
    });
    return promise;
  }
  manageRemainder(data) {
    const userDetails = this._structureService.getUserdata();
    const args = {
      'token': data.token,
      'status': data.status,
      'tenantId': userDetails.config.token
    };
    //const apiURL = userDetails.config.wp_enroll_url + '/wp-json/api/wp/v2/manage-reminder';
    const apiURL = this._structureService.apiBaseUrl + "citus-health/v4/manage-reminder-wrapper.php";
    let headers = new Headers();
    headers.append('Content-Type', 'application/json');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    const options = new RequestOptions({ headers: headers });
    NProgress.start();
    const promise = new Promise((resolve, reject) => {
      this._http.post(apiURL, args, options)
        .toPromise()
        .then(
          res => {
           const result = res.json();
            NProgress.done();
           resolve(result);
          },
          err => {
            NProgress.done();
            reject(err);
          }
        );
    });
    return promise;
  }
  autoremainderList(data) {
    const userDetails = this._structureService.getUserdata();
    const args = {
      'token': data.token,
      'limit': data.limit,
      'type':data.type,
      'offset': data.offset
    };
    const apiURL = userDetails.config.wp_enroll_url + '/wp-json/api/wp/v2/auto-reminder-list';
    let headers = new Headers();
    headers.append('Content-Type', 'application/json');
    const options = new RequestOptions({ headers: headers });
    NProgress.start();
    const promise = new Promise((resolve, reject) => {
      this._http.post(apiURL, args, options)
        .toPromise()
        .then(
          res => {
            const result = res.json();
            NProgress.done();
            resolve(result);
          },
          err => {
            NProgress.done();
            reject(err);
          }
        );
    });
    return promise;
  }

  getEnrollData(args) {
    const userDetails = this._structureService.getUserdata();
    // args['tenantId'] = userDetails.config.token;
   // const apiURL = userDetails.config.wp_enroll_url + '/wp-json/api/wp/v2/enroll';
    const apiURL = this._structureService.apiBaseUrl + "citus-health/v4/enroll-wrapper.php";
    let headers = new Headers();
    headers.append('Content-Type', 'application/X-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    const options = new RequestOptions({ headers: headers });
    NProgress.start();
    const promise = new Promise((resolve, reject) => {
      this._http.post(apiURL, args, options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          //NProgress.done();
          resolve(result);
        },
        err => {
          NProgress.done();
          const callBackResult = this.rejectApi(err, args, apiURL);
          resolve(callBackResult);
        }
        );
    });
    return promise;
  }

 getStrucuturedFormResultsdetails(formId,formSubmissionId){
    var data = "formId=" + formId + "&submissionId=" + formSubmissionId+ "&partnerReferral=true" ;
    var apiConfig = {url:this._structureService.apiBaseUrl+"citus-health/v4/get-strucured-form-data.php", requestType: 'http', data: data};
    return this._structureService.requestData(apiConfig);
  }
  getPatientForCaregiver(data) {
    const apiConfig = {
      url: this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/'+APIs.checkPatientForCaregiver,
      requestType: 'http',
      data: data
    };
    return this._structureService.requestData(apiConfig);
  }

  checkUserExists(data) {
    const apiConfig = {
      url: this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/check-user-exists.php',
      requestType: 'http',
      data: data
    };
    return this._structureService.requestData(apiConfig);
  }

  sendDataToMiddleware(data) {
    const userDetails = this._structureService.getUserdata();
    let apiURL = configWpMiddlewareWebHook() + 'hook/web/prescribe';
    let headers = new Headers();
    headers.append('Content-Type', 'application/json');
    headers.append('Authorization', `Session ${this._structureService.getCookie('authID')}`);
    const options = new RequestOptions({ headers: headers });
    //NProgress.start();
    const promise = new Promise((resolve, reject) => {
      this._http.post(apiURL, data, options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          //NProgress.done();
          resolve(result);
        },
        err => {
          NProgress.done();
          reject(err);
        }
        );
    });
    return promise;
  }

  sendReminder(data) {
    const userDetails = this._structureService.getUserdata();
    const apiURL = configWpMiddlewareWebHook() + APIs.sendReminderApi;
    const headers = new Headers();
    headers.append('Content-Type', 'application/json');
    headers.append('Authorization', `Session ${this._structureService.getCookie('authID')}`);
    const options = new RequestOptions({ headers: headers });
    NProgress.start();
    const promise = new Promise((resolve, reject) => {
      this._http.post(apiURL, data, options)
        .toPromise()
        .then(
          res => {
            const result = res.json();
            NProgress.done();
            resolve(result);
          },
          err => {
            NProgress.done();
            reject(err);
          }
        );
    });
    return promise;
  }

  updateLastReminder(token, dayToExpire) {
    const userDetails = this._structureService.getUserdata();
    const tenantId  = userDetails.config.token;
   // const apiURL = userDetails.config.wp_enroll_url + '/wp-json/api/wp/v2/update-last-reminder';
   const apiURL = this._structureService.apiBaseUrl + "citus-health/v4/update-last-reminder-wrapper.php";
   let headers = new Headers();
    headers.append('Content-Type', 'application/X-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    const options = new RequestOptions({ headers: headers });
    NProgress.start();
    const promise = new Promise((resolve, reject) => {
      this._http.post(apiURL, { token: token, time: moment().unix(), dayToExpire: dayToExpire, tenantId: tenantId }, options)
        .toPromise()
        .then(
          res => {
            const result = res.json();
            NProgress.done();
            resolve(result);
          },
          err => {
            NProgress.done();
            reject(err);
          }
        );
    });
    return promise;
  }

  createPersonalCodes(reqData) {
    const userDetails = this._structureService.getUserdata();
    let apiURL = userDetails.config.wp_enroll_url + '/wp-json/api/wp/v2/signup';
    let headers = new Headers();
    headers.append('Content-Type', 'application/X-www-form-urlencoded');
    const options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
      this._http.post(apiURL, reqData, options)
        .toPromise()
        .then(
          res => {
            const result = res.json();
            resolve(result);
          },
          err => {
            reject(err);
          }
        );
    });
    return promise;
  }

  storeSubmissionData(data) {
    const userDetails = this._structureService.getUserdata();
    // data['tenantId'] = userDetails.config.token;
    //const apiURL = userDetails.config.wp_enroll_url + '/wp-json/api/wp/v2/form';
    const apiURL = this._structureService.apiBaseUrl + "citus-health/v4/forms-wrapper.php";
    let headers = new Headers();
    headers.append('Content-Type', 'application/json');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    const options = new RequestOptions({ headers: headers });
    //NProgress.start();
    const promise = new Promise((resolve, reject) => {
      this._http.post(apiURL, data, options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          NProgress.done();
          resolve(result);
        },
        err => {
          NProgress.done();
          const callBackResult = this.rejectApi(err,data,apiURL);
          resolve(callBackResult);
        }
        );
    });
    return promise;
  }

  getReferralUsers() {
    const userData = this._structureService.getUserdata();
    const networkInterface = new HTTPFetchNetworkInterface(userData.config.wp_enroll_url + '/graphql');
    const queryArgs = {
      metaQuery: {
        relation: 'AND',
        metaArray: [
          { key: 'wpcf-post-tenant-id', value: userData.config.token, compare: 'EQUAL_TO' },
          // { key: 'wpcf-email-of-assignee', value: userData.username, compare: 'EQUAL_TO' },
          // { key: 'wpcf-api-status', value: '0,10', compare: 'IN' }
          { key: 'wpcf-referral-status', value: 'Cancelled', compare: 'NOT_EQUAL_TO' }
        ]
      }
    };
    const variables = { where: queryArgs };
    return networkInterface.query({
      query: getReferralUsers,
      variables: variables
    });
  }

  getReminderDetails(token) {
    const userData = this._structureService.getUserdata();
    const networkInterface = new HTTPFetchNetworkInterface(userData.config.wp_enroll_url + '/graphql');
    const queryArgs = {
      metaQuery: {
        relation: 'AND',
        metaArray: [
          { key: 'wpcf-request-token', value: token, compare: 'EQUAL_TO' },
          { key: 'wpcf-notification-type', value: 'reminder', compare: 'EQUAL_TO' }
        ]
      }
    };
    const variables = { where: queryArgs };
    return networkInterface.query({
      query: getReminderDetails,
      variables: variables
    });
  }
  getTenantRoles() {
    const tenantId = this._structureService.getCookie("crossTenantId");
    const apiURL = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version +
      '/get-tenant-roles.php?tenantId=' + tenantId;
    const apiConfig = { url: apiURL, method: 'GET', requestType: 'http' };
    const response = this._structureService.requestData(apiConfig);
    return response;
  }

  getTenantRolesByPrivilege(privilege: any) {
    const tenantId = this._structureService.getCookie("crossTenantId");
    const apiURL = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version +'/get-tenant-roles-by-privilege.php?' + 'privilegeKey=' + privilege;
    const apiConfig = { url: apiURL, method: 'GET', requestType: 'http' };
    const response = this._structureService.requestData(apiConfig);
    return response;
  }

  getDelegatedRolesByRoleId(roleId) {
    const variables = {
      sessionToken: this._structureService.getCookie('authenticationToken'),
      roleId: roleId,
    };
    const apiConfig = {
      method: 'GET',
      data: getDelegatedRolesByRoleId,
      variables: variables,
      requestType: 'gql',
      use: '',
    };
    return this._structureService.requestData(apiConfig);
  }

  checkStaffExists(params) {
    const tenantId = this._structureService.getCookie("crossTenantId");
    //const userDetails = this._structureService.getUserdata();
    const apiURL = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version +
    `/check-staff-exists.php?tenantId=${tenantId}&email=${params.email.trim()}&firstname=${params.firstname.trim()}&lastname=${params.lastname.trim()}&mobile=${params.mobile.trim()}`;
    const apiConfig = { url: apiURL,  method: 'GET', requestType: 'http' };
    const response = this._structureService.requestData(apiConfig);
    return response;
  }

  deleteFormSubmission(token) {
    const userDetails = this._structureService.getUserdata();
    const tenantId = userDetails.config.token;
   // const apiURL = userDetails.config.wp_enroll_url + '/wp-json/api/wp/v2/delete-form-submission';
    const apiURL = this._structureService.apiBaseUrl + "citus-health/v4/delete-form-submission-wrapper.php";
    let headers = new Headers();
    headers.append('Content-Type', 'application/X-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    const options = new RequestOptions({ headers: headers });
    NProgress.start();
    const promise = new Promise((resolve, reject) => {
      this._http.post(apiURL, { token: token, changedBy: userDetails.username, tenantId: tenantId }, options)
        .toPromise()
        .then(
          res => {
            const result = res.json();
            NProgress.done();
            resolve(result);
          },
          err => {
            NProgress.done();
            reject(err);
          }
        );
    });
    return promise;
  }

  getTagsByGroup(userData, tagType) {

    const userDetails = this._structureService.getUserdata();
    userData=userData+"&crossTenantId="+this._structureService.getCookie("crossTenantId");
    const apiURL = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/get-tag-details.php' + userData;
    const apiConfig = { url: apiURL, method: 'POST', requestType: 'http' };
    return this._structureService.requestData(apiConfig);
  }

}
