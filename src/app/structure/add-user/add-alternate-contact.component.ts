import {
     Component,
     OnInit,
     <PERSON>ement<PERSON>ef,
     <PERSON><PERSON><PERSON>,
     ViewChild
} from '@angular/core';
import {
     StructureService
} from '../structure.service';
import {
     Router,
     ActivatedRoute,
     Params
} from '@angular/router';
import {
     NgForm,
     FormBuilder,
     FormGroup,
     Validators,
     ValidatorFn,
     AbstractControl,
     FormControl,
     FormGroupDirective
} from '@angular/forms';
import {
     DatePipe
} from '@angular/common';
import {
     AddUserService
} from './adduser.service';
import {
     SharedService
} from '../../structure/shared/sharedServices';
import {
     GlobalDataShareService
} from '../../structure/shared/global-data-share.service';

import {
     RegistrationService
} from '../../structure/registration/registration.service';
import {
     VerifyEmComponent
} from "../shared/verifyEM/verify-em.component";
import { Console } from 'console';
import { ThrowStmt } from '@angular/compiler';
import {CONSTANTS} from 'app/constants/constants';
import { isBlank, setCountryCodeFlag } from 'app/utils/utils';

declare var $: any;
declare var moment: any;
declare var swal: any;

const STATUS_CODE = {
     '0': 'SUCCESS',
     '10': 'EMAIL_EXIST',
     '20': 'VALIDATION_ERROR',
     '25': 'TECHNICAL_ERROR',
     '30': 'ERROR',
     '40': 'SERVER_ERROR'
};

const USER_REG_STATUS = {
     0: 'Reject',
     1: 'Approve',
     2: 'Pending',
     3: 'Disable',
     4: 'Discharge',
     8: 'Deleted'
};

@Component({
     selector: 'app-add-user',
     templateUrl: './add-alternate-contact.component.html'
})
export class AddAlternatecontactComponent implements OnInit {
     onEditUTypeLoad = false;
     onEditLoad = false;
     onEditUserTagLoad = false;
     onEditPFNameLoad = false;
     onEditPLNameLoad = false;
     onEditUseMRNLoad = false;
     onEditDobLoad = false;
     onEditMRNLoad = false;
     patientTags: any = [];
     partnerTags: any = [];
     selectedTagIds: any = [];
     staffTags: any = [];
     enableNext: boolean;
     isNavigate = false;
     editId = "";
     partnerRoleTitle: any;
     referralCodes: any;
     pageNumber = 0;
     selectedObj;
     userCountryCode;
     activePatientTab;
     puser;
     userMobile;
     duplicateUsers = [];
     disableMRNPage = false;
     configUser: any;
     config: any;
     staffenroll = 0;
     patientenroll = 0;
     selectFlag = false;
     enrollData;
     showInInviteRecord: any;
     showAuxilaryPhysicianOrNursingLabel = false;
     auxilaryPhysicianOrNursingLabel: any;
     partnerCategory: any = [];
     partnerenroll = 0;
     staffRoles: any = [];
     partnerRoles: any = [];
     dob: any;
     tabSelect;
     disableSubmit: Boolean = false;
     extInegration: any = [];
     userCountry: any = 'us';
     initialUserCountry: any = 'us';
     warning_msg: String = '';
     referelTknSet = false;
     newPatient: FormGroup;
     defaultTenantDetails;
     selectedTenantDetails;
     userData;
     patientList: any;
     readonly maskPhoneNumber  = CONSTANTS.phoneMask;
     
     manageConfig: any = {};
     responsePatient: any = [];
     responseStaff: any = [];
     responsePartner: any = [];
     pageTitle = 'Add Alternate Contact';
     pageTitleBreadcrumbs = 'Add Users';
     editpatientUrl = '/add-user/add';
     returnFromAlternateContact ='';

     userInfo = {
          token: '',
          firstName: '',
          middleName: '',
          lastName: '',
          email: '',
          mobile: '',
          birthDate: '',
          sex: 'male',
          zipCode: '',
          address: '',
          city: '',
          district: '',
          state: '',
          countryCode: '+1',
          countryIsoCode: '',
          staffId: '',
          companyNursingAgency: ''
     };

     userListDisaplay: any[];
     tagLoading = false;
     removeArray = [];
     userTypes;
     selectedTags: any = [];
     selectedTagNames: any = [];
     stafffillfirst = false;
     tagtypeList: any = [];
     usersList = [];
     removeArr: any = [];
     dataResponseUser: any;
     registrationToken;
     registrationTenantId;
     patientRoleId;
     AlternateeditId:any;
     patientId;

     show_button: Boolean = false;
     show_eye: Boolean = false;
     verifiedEmailOrMobile = null;
     hideCaregiverSingup: Boolean = false;
     onEditCareGiverSignupLoad: Boolean = false;
     returnUrl = "";
     patientForContact:any;

     @ViewChild(VerifyEmComponent) verifyPopup: VerifyEmComponent;
     @ViewChild(FormGroupDirective) formGroupDirective: FormGroupDirective;
     constructor(
          private _structureService: StructureService,
          private _enrollService: AddUserService,
          private router: Router,
          private _sharedService: SharedService,
          private route: ActivatedRoute,
          public _GlobalDataShareService: GlobalDataShareService,
          private elementRef: ElementRef,
          private renderer: Renderer,
          private registrationservice: RegistrationService

     ) {  this.manageConfig =  JSON.parse(this._structureService.userDataConfig);
          renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
               $('#tagsInput').text('');
               console.log("event.target", event.target);
               console.log("event", event);
               var idDetails = ['associate-search-input', 'associate-li', 'associate-ul', 'associate-search', 'tagsInput', 'recipient-li', 'recipient-ul', 'recipient-search',];
               if (!idDetails.includes(event.target.id) && event.target.className.indexOf('recipient-li') == -1) {
                    let clear = false;
                    let from = "";

                    if ($('#recipient-ul').css('display') == 'block') {
                         $('#tagsInput').val('');
                         from = "R";
                    }
                    this.enableOrDisableUiLI(false, clear, from);
                    console.log('Condition1');
               } else {
                    if (event.target.id == 'associate-search-input' && $('#recipient-ul').css('display') == 'block') {
                         this.enableOrDisableUiLI(false, false, 'R');
                    } else if (event.target.id == 'tagsInput' && $('#associate-ul').css('display') == 'block') {
                         this.enableOrDisableUiLI(false, false);
                    }
                    console.log('Condition2');
               }
          });

          if (this.verifiedEmailOrMobile == undefined || this.verifiedEmailOrMobile == null) {
               this.verifiedEmailOrMobile = this._sharedService.mobileOrEmailVerified.subscribe(
                    (data) => {
                         console.log("mobileOrEmailVerified subscribe Enter", data);
                         if (data.mobile) {
                              this.newPatient.patchValue({
                                   mobileVerified: true
                              });
                         } else if (data.email) {
                              this.newPatient.patchValue({
                                   emailVerified: true
                              });
                         }
                    }
               );
          }

          
          this.userData = JSON.parse(this._structureService.userDetails);

          this.registrationToken = this.userData.config.token;

          route.params.subscribe(val => {
              
                if (this.route.snapshot.params['id']) {
                     this.AlternateeditId = this.route.snapshot.params['id'];
                    
                }
           });
           this.patientId = localStorage.getItem('patientId'); 
           console.log('edit id');
           console.log(this.AlternateeditId);
     }

     /* password(formGroup: FormGroup) {
           const {
                value: password
           } = formGroup.get('temppassword');
           const {
                value: confirmPassword
           } = formGroup.get('repeatPassword');
           return password === confirmPassword ? null : {
                passwordNotMatch: true
           };
      }*/

     ngOnDestroy() {
          //$('#intro-btn').attr('hidden',true);
          this._structureService.unSubsc();
     }

     ngOnInit() {
          let tab = localStorage.getItem('addUserTab');
          localStorage.setItem('contactAdded', '');
          this.activePatientTab = 'details';          
          if (!tab || tab == "") {
               if (this.userData.privileges.includes('allowAddUser') && this.userData.privileges.includes('addVirtualEnrolledStaffUsers')) {
                    tab = 'staff';
               } else if (this.userData.privileges.includes('allowAddUser') && this.userData.privileges.includes('addVirtualPartnerUsers')) {
                    tab = 'partner';
               }
          }

          if (tab == 'patient') {
               this.onEditUTypeLoad = true;
          } else {
               this.onEditUTypeLoad = false;
          }
          if (localStorage.getItem('returnFromAlternateContact') !="") {
               this.returnFromAlternateContact = localStorage.getItem('returnFromAlternateContact');
               this.pageTitleBreadcrumbs = 'Edit Alternate Contact';
               this.pageTitle = 'Edit Alternate Contact';
               this.editpatientUrl = this.returnFromAlternateContact;
          }
          else {
               this.pageTitleBreadcrumbs = 'User Center / Add Users';               
               this.editpatientUrl = '/add-user/add/'+this.patientId;
          }

          this.tabSelect = tab;

          this.newPatient = new FormGroup({


               firstName: new FormControl({
                    value: '',
                    disabled: false
               }, Validators.required),
               lastName: new FormControl({
                    value: '',
                    disabled: false
               }, Validators.required),


               ESIValue: new FormControl({
                    value: '',
                    disabled: false
               }, Validators.required),

               // tslint:disable-next-line:max-line-length
               email: new FormControl({
                    value: '',
                    disabled: false
               }, [Validators.pattern(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/)]),

               // gender: new FormControl({ value: 'male', disabled: false }),
               alternateUsername: new FormControl({
                    value: '',
                    disabled: true,
               }),

               mobile: new FormControl({
                    value: '',
                    disabled: false
               }),
               countryCode: new FormControl({
                    value: '',
                    disabled: false
               }, [Validators.required, Validators.pattern(/^\+\d+$/)]),

               countryIsoCode: new FormControl({
                    value: '',
                    disabled: false
               }),

               // dmonth: new FormControl({ value: '', disabled: false }),
               // dyear: new FormControl({ value: '', disabled: false }),
               // Partner Fields

               relation: new FormControl({
                    value: '',
                    disabled: false
               }),

               alternateMobile: new FormControl(),
               enableSmsNotifications: new FormControl({
                    value: 1,
                    disabled: false
               }),
               enableEmailNotifications: new FormControl({
                    value: 1,
                    disabled: false
               })
          });


          $('body').on('keypress', function (e) {
               var code = (e.keyCode ? e.keyCode : e.which);
               if (code == 13) {
                    return false;
               }
          })

          $('body').tooltip({
               selector: '[data-toggle="tooltip"]'
          });
          console.log('checking array');
          console.log(this._sharedService.formdataWithAlternate);
          let self: any;
          
          this.defaultTenantDetails = JSON.parse(this._structureService.userDetails);
          this.selectedTenantDetails = this._GlobalDataShareService.getselectedTenantDetails();
          var arrayOfElements = [];
          ///  let disableTrainMe = (this.newPatient.get('utype').value === 'patient') ? true : false;
          this._structureService.introJsBtnHidden = false;
          /* this._structureService.setIntroOptions('add-user,register', arrayOfElements, disableTrainMe, (conditionalParams) => {
                 delete this._structureService.introSteps;
                 this._structureService.introSteps = JSON.parse(JSON.stringify(this._structureService.introStepsCopy));
     
                 this._sharedService.startIntroCallBack.emit({
                     value: true
                 });
     
                 this._structureService.introJsObject.oncomplete(() => {
                     this._structureService.introJsObject.exit();
                     delete this._structureService.introJsObject;
                     delete this._structureService.introSteps;
                 });
     
                 this._structureService.introJsObject.onexit(() => {
                     delete this._structureService.introJsObject;
                     delete this._structureService.introSteps;
                 });
           });*/

          const userDetails = this._structureService.getUserdata();

          this._structureService.getUser(userDetails.userId).then((data) => {
               if (data['staffUsers']) {
                    if (data['staffUsers'].length > 0) {
                         let user = data['staffUsers'][0];
                         this.userCountryCode = user.countryCode;
                         this.userMobile = user.mobile;
                    }
               }
          });

          this._enrollService.getTenantRolesByPrivilege('allowEnrollmentInitiation').then((data: any) => {
               this.staffRoles = data.filter(a => a.citus_role_id !== '20');
               this.partnerRoles = data.filter(a => a.citus_role_id === '20');
          });

          this._structureService.getExternalSystems().then((data) => {
               this.extInegration = data['getSessionTenant']['externalIntegrationSystems'];
               console.log('checking');
               console.log(this.extInegration);
               let showInInvite = this.extInegration.filter((rec) => {
                    if (rec.externalSystemId == this.userData.config.esi_code_for_alternate_contact) return true;
               });
               if (showInInvite.length) {
                    this.showInInviteRecord = showInInvite[0];
                    this.newPatient.patchValue({
                         mrnSys: this.showInInviteRecord.externalSystemId,
                         mrnSysCode: this.showInInviteRecord.code
                    });
               }
          });

          if (this.manageConfig.make_esi_code_mandatory_in_alternate_contact_invite == "1") {
               this.newPatient.get('ESIValue').setValidators([Validators.required]);
               this.newPatient.get('ESIValue').updateValueAndValidity();
          } else {
               this.newPatient.get('ESIValue').clearValidators();
               this.newPatient.get('ESIValue').updateValueAndValidity();
          }
          setTimeout(() => {

               $('#country_code').intlTelInput();
               this.getCountryCode('', '');
               $('#country_code').on('countrychange', (e, countryData) => {
                    if (countryData.dialCode) {
                         this.userInfo.countryCode = '+' + countryData.dialCode;
                         this.userInfo.countryIsoCode = this.userCountry = countryData.iso2;
                         this.newPatient.patchValue({
                              countryCode: '+' + countryData.dialCode,
                              countryIsoCode: this.userCountry
                         });
                         $('#country_code').intlTelInput('setNumber', '+' + countryData.dialCode);
                    }
               });

               $('body').on('keyup', '#email-address', (e) => {
                    this.newPatient.patchValue({
                         email: $('#email-address').val()
                    });
               });
               

          }, 100);

     

          $('body').on('select2:select', 'select[name="partnerRole"]', (e) => {
               const data = e.params.data;
               this.partnerRoleTitle = data.text.trim();
               this.newPatient.patchValue({
                    partnerRole: data.id,
                    partner_role_behalf: false
               });
               this.enableNext = false;

               setTimeout(() => {
                    $('#country_code').intlTelInput();
                    $('#country_code').intlTelInput('setCountry', this.initialUserCountry);
                    const cntryDetails = $('#country_code').intlTelInput('getSelectedCountryData');
                    $('#country_code').intlTelInput('setNumber', '+' + cntryDetails.dialCode);
                    this.userInfo.countryCode = '+' + cntryDetails.dialCode;
                    this.newPatient.patchValue({
                         countryCode: this.userInfo.countryCode
                    });
               }, 100);
          });

          $('body').on('keyup', '#country_code', (e) => {
               this.userInfo.countryCode = $('#country_code').val();
               this.newPatient.patchValue({
                    countryCode: this.userInfo.countryCode
               });
          });

          $('html').bind('input', function (event) {
               if (event.target.id == 'email-address') {
                    $('#email-address').trigger('keyup');
               } 
          });

          setTimeout(() => {
               $("#country_code").on("change paste keyup", () => {
                    var regex = /^\+?[0-9]*$/;
                    if ($('#country_code').val()) {
                         if (!regex.test($('#country_code').val())) {
                              const cntryDetails = $('#country_code').intlTelInput('getSelectedCountryData');
                              $('#country_code').intlTelInput('setNumber', '+' + cntryDetails.dialCode);
                              this.userInfo.countryCode = '+' + cntryDetails.dialCode;
                         }
                    }
               });

               setTimeout(() => {
                    if (this.tabSelect == 'patient') {
                         this.isNavigate = true;
                    }
               }, 1500);
          }, 1000);

          if (!isBlank(this.AlternateeditId)) {
               if (localStorage.getItem('Addclciked') == '' || localStorage.getItem('returnFromAlternateContact') != "") {
                    this.onEditMRNLoad = true;
               }
               this.oneditAlternate();
          } else {
               this.patientList = {};
               localStorage.setItem('isCareGiver', '');
          }
          setTimeout(function () {
               $('#mrn').keypress(function (e) {
                    if (e.which === 32 && !this.value.length)
                         e.preventDefault();
               });
          }, 2000);
     }
     patientEditTabs(requestParams) {
          this.activePatientTab = requestParams;
     }
     oldPhoneNumberCheck() {
          console.log("oldPhoneNumberCheck=====Enter");
          var oldPhone = $("#oldPhoneNumber").val();
          console.log("oldPhoneNumberCheck oldphone==> " + oldPhone);
          var exactvalue = this.newPatient.get('countryCode').value.trim() + this.newPatient.get('mobile').value.trim();
          console.log("oldPhoneNumberCheck exactvalue==> " + exactvalue);
          if (oldPhone != "") {
               console.log("oldPhoneNumberCheck first if case")
               if (oldPhone != exactvalue) {
                    console.log("oldPhoneNumberCheck Enter second if");
                    this.newPatient.patchValue({
                         mobileVerified: false
                    });
               }
          } else {
               console.log("oldphone else case");
               $("#oldPhoneNumber").val(exactvalue);
          }
     }
     oldEmailIdCheck() {
          console.log("oldEmailIdCheck=====Enter");
          var oldEmail = $("#oldemailId").val();
          console.log("oldEmailIdCheck oldEmail==> " + oldEmail);
          var exactvalue = this.newPatient.get('email').value.trim();
          console.log("oldEmailIdCheck exactvalue==> " + exactvalue);
          if (oldEmail != "") {
               console.log("oldEmailIdCheck first if case")
               if (oldEmail != exactvalue) {
                    console.log("oldEmailIdCheck Enter second if");
                    this.newPatient.patchValue({
                         emailVerified: false
                    });
               }
          } else {
               console.log("oldEmail else case");
               $("#oldemailId").val(exactvalue);
          }
     }

     oneditAlternate() {
          this.onEditUTypeLoad = true;
          this.onEditLoad = true;
          if (!isBlank(this.AlternateeditId)) {
               this.hideCaregiverSingup = true;
               var request = {
                     "tenantId": this.userData.tenantId,
                     "contactId": this.AlternateeditId ? this.AlternateeditId : ''
                };
                console.log(request);               
               this._structureService.getsingleAlternateContact(request).then((res:any) => {
                    if (localStorage.getItem('Addclciked') == '') {
                         this.pageTitle = 'Edit Alternate Contact';
                    }
                    this.patientList =res.data;
                    console.log('response is');
                    console.log(this.patientList);
                    console.log(localStorage.getItem('Addclciked'));
                    if (this.patientList && (localStorage.getItem('Addclciked') == '' || localStorage.getItem('Addclciked') == null ) ){

                         var email = '';
                         if (!this.patientList.email.includes('unknown_')) {
                              email = this.IsEmail(this.patientList.email);
                         }
                         var alternateUsername = '';
                         if (!this.patientList.alternateUsername.includes('unknown_')) {
                              alternateUsername = this.patientList.alternateUsername;
                         }
                         console.log(alternateUsername);
                         var selectedMobile = '';
                         if (this.patientList.mobile != null) {
                              selectedMobile = this.patientList.mobile.replace(/[|&;$%@"<>()+, -]/g, "");
                         }
                         this.getCountryCode(this.patientList.countryCode, this.patientList.countryIsoCode);
                         this.newPatient.patchValue({
                              firstName: this.patientList.firstName,
                              lastName: this.patientList.lastName,
                              mobile: selectedMobile,
                              alternateMobile:selectedMobile,
                              email: email,
                              alternateEmail: email,
                              alternateUsername:alternateUsername,
                              relation:this.patientList.relation,
                              patientMobile: this.patientList.mobile,
                              isVirtualUser: "yes",
                              virtualPatientId: this.AlternateeditId,
                              ESIValue: this.patientList.ESIValue,
                              enableSmsNotifications: parseInt(this.patientList.enable_sms_notifications),
                              enableEmailNotifications: parseInt(this.patientList.enable_email_notifications)
                             // mobileVerified: this.patientList[0].userEMverification.mobileVerified,
                             // emailVerified: this.patientList[0].userEMverification.emailVerified
                         });

                    }
               });
               console.log('this.selectedObj', this.selectedObj);
          }
     }
     getCountryCode(countryCode, countryIsoCode) {
          setTimeout(() => {
               $('#country_code').intlTelInput();
               const countryDetails = setCountryCodeFlag('country_code', countryCode, countryIsoCode);
               this.newPatient.patchValue({
                    countryCode: countryDetails.countryCode,
                    countryIsoCode: countryDetails.countryIsoCode
               });
               this.userInfo.countryIsoCode = this.userCountry = countryDetails.countryIsoCode;
               this.userInfo.countryCode = countryDetails.countryCode;
          }, 1000);
     }
     
     IsEmail(email) {
          var regex = /^([a-zA-Z0-9_\.\-\+])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
          var validEmail;
          if (!regex.test(email)) {
               validEmail = '';
               return validEmail;
          } else {
               validEmail = email;

               return validEmail;
          }
     }

     replaceSpecialChar(data) {
          if (data) {
               return data.replace(/"/g, "'").trim();
          } else {
               return data;
          }

     }

     showPassword() {
          this.show_button = !this.show_button;
          this.show_eye = !this.show_eye;
     }

     omit_special_char(event) {
          var k;
          k = event.charCode; //         k = event.keyCode;  (Both can be used)
          return k != 34;
     }

     tagInputFocus() {
          console.log("tagInputFocus :call enableOrDisableUiLI.......");
          this.enableOrDisableUiLI(true, false, "R");
     }

     selectAllTags() {
          var taglist = [];
          if (this.staffTags && this.staffTags.length != 0) {
               taglist = this.staffTags;
          }
          if (this.patientTags && this.patientTags.length != 0) {
               taglist = this.patientTags;
          }
          if (this.partnerTags && this.partnerTags.length != 0) {
               taglist = this.partnerTags;
          }
          if (taglist && taglist.length > 0) {
               taglist.forEach(element => {
                    var id = element.id;
                    var index = this.selectedTags.indexOf(id);
                    if (index == -1) {
                         this.setSelectedTags(element, id);
                    }
               });
          }
     }

     closeSelectedTag(condition: boolean = false) {
          console.log("closeSelectedTag :called........");
          console.log("this.selectedTags.length=======>" + this.selectedTags.length);
          console.log("this.selectedTags=======>", this.selectedTags);

          $("#recipient-search").text(" ").text("Search");

          if (this.selectedTags.length == 1) {
               this.resetTag(condition);
          } else {
               this.resetTag(condition);
          }
     }

     resetTag(condition: boolean = false) {
          console.log("resetTag :called........")
          this.selectedTags = [];
          this.patientTags = [];
          $(".tag-span").remove();
          if (!condition)
               this.enableOrDisableUiLI(false, true, "R");
     }

     togglePreference(preference, value) {
          this.newPatient.patchValue({
               [preference]: value
          })
     }

     checkTagWithTems() {
          var textValue = $("#tagsInput").val();
          var searchText = textValue; //.replace(/[^a-z0-9\+\-\.\#]/ig,'');

          if (textValue != "") {
               this.setTag(searchText);
          }
     }


     setTag(searchKeyword: any = "", fromEdit: boolean = false) {
          if (!this.tagLoading) {
               this.tagLoading = true;
               $("#recipient-search").text(" ").text("Loading...");

               const formdata = this.newPatient.value;

               const userData: any = this._structureService.getUserdata();
               // Id of patient group is 3
               if (formdata.utype === 'patient') {
                    const tagGetData =  '?group=3&enroll=1' + "&searchKeyword=" + searchKeyword;
                    const tagTypes = ["2"]; // Message Tag =1, User Tag =2 , Document Tag =3
                    this._enrollService.getTagsByGroup(tagGetData, tagTypes).then((data: any) => {
                         this.tagLoading = false;
                         if (data) {
                              this.responsePatient = data;
                              this.patientTags = this.patientTags.filter((a) => this.selectedTagIds.includes(a.id));
                              this.patientTags = [...this.patientTags, ...data];
                              this.patientTags = this.patientTags.reduce((unique, o) => {
                                   if (!unique.some(obj => obj.id === o.id)) {
                                        unique.push(o);
                                   }
                                   return unique;
                              }, []);
                              if (searchKeyword == "" && fromEdit) {
                                   this.enableOrDisableUiLI(false, true, "R");
                                   $("#recipient-search").text(" ").text("Search");
                              } else {
                                   this.enableOrDisableUiLI(true, false, "R");
                                   $("#recipient-search").text(" ").text("Search");
                              }

                         } else {
                              if (searchKeyword == "" && fromEdit) {
                                   this.enableOrDisableUiLI(false, true, "R");
                                   $("#recipient-search").text(" ").text("Search");
                              } else {
                                   this.enableOrDisableUiLI(true, false, "R");
                                   $("#recipient-search").text(" ").text("Search");
                              }
                         }
                    }).then(function () {
                         this.tagLoading = false;

                    }).catch((ex) => {
                         this.tagLoading = false;
                    });

               } else if (formdata.utype === 'partner') {
                    const tagTypes = ["2"];
                    const partnerTagGetData = '?group=20&enroll=1' + "&searchKeyword=" + searchKeyword;
                    this._enrollService.getTagsByGroup(partnerTagGetData, tagTypes).then((data: any) => {
                         this.tagLoading = false;
                         if (data) {
                              this.responsePartner = data;
                              this.partnerTags = this.partnerTags.filter((a) => this.selectedTagIds.includes(a.id));
                              this.partnerTags = [...this.partnerTags, ...data];
                              this.partnerTags = this.partnerTags.reduce((unique, o) => {
                                   if (!unique.some(obj => obj.id === o.id)) {
                                        unique.push(o);
                                   }
                                   return unique;
                              }, []);
                              this.enableOrDisableUiLI(true, false, "R");
                              $("#recipient-search").text(" ").text("Search");

                         } else {
                              console.log("No result.............");
                              this.enableOrDisableUiLI(true, false, "R");
                              $("#recipient-search").text(" ").text("Search");
                         }
                    }).then(function () {
                         this.tagLoading = false;

                    }).catch((ex) => {
                         this.tagLoading = false;
                    });


               } else if (formdata.utype === 'staff') {

                    const tagTypes = ["2"];
                    const staffTagGetData = '?group=2&enroll=1' + "&searchKeyword=" + searchKeyword;
                    this._enrollService.getTagsByGroup(staffTagGetData, tagTypes).then((data: any) => {
                         this.tagLoading = false;
                         if (data) {
                              this.responseStaff = data;
                              this.staffTags = this.staffTags.filter((a) => this.selectedTagIds.includes(a.id));
                              this.staffTags = [...this.staffTags, ...data];
                              this.staffTags = this.staffTags.reduce((unique, o) => {
                                   if (!unique.some(obj => obj.id === o.id)) {
                                        unique.push(o);
                                   }
                                   return unique;
                              }, []);
                              this.enableOrDisableUiLI(true, false, "R");
                              $("#recipient-search").text(" ").text("Search");

                         } else {
                              console.log("No result.............");
                              this.enableOrDisableUiLI(true, false, "R");
                              $("#recipient-search").text(" ").text("Search");
                         }
                    }).then(function () {
                         this.tagLoading = false;

                    }).catch((ex) => {
                         this.tagLoading = false;
                    });
               }

          } else {

          }
          console.log("set recipient loaded");

     }

     enableOrDisableUiLI(condition, clear: boolean = true, from: any = "") {
          console.log(`enableOrDisableUiLI condition = ${condition}, clear = ${clear}, clear = ${from}`);
          if (condition) {
               if (from == "R") {
                    if ($('ul#associate-ul').css('display') == 'block') {
                         this.enableOrDisableUiLI(false, false);
                    }
                    $("#tagsInput").addClass('ul-active');
                    $("ul#recipient-ul").css('display', 'block');
               } else {
                    if ($('#recipient-ul').css('display') == 'block') {
                         this.enableOrDisableUiLI(false, false, "R");
                    }
                    $("ul#associate-ul").css('display', 'block');
                    $("input#associate-search-input").addClass("active");
               }

          } else {
               if (from == "R") {
                    $("#tagsInput").removeClass("ul-active");
                    $("ul#recipient-ul").css('display', 'none');
               } else {
                    $("ul#associate-ul").css('display', 'none');
                    $("input#associate-search-input").removeClass("active");
               }

          }

          if (clear) {
               if (from == "R") {
                    $("#tagsInput").val("");
                    $("#tagsInput").attr("placeholder", "Search User Tags");
               } else {
                    $("input#associate-search-input").val('');
               }

          }
     }

     setSelectedTags(tags, tagid) {
          let selectedIndex = this.selectedTags.indexOf(tagid);
          if (selectedIndex == -1) {
               this.selectedTags.push(tagid);

               this.selectedTagNames.push({
                    text: tags.tag_name
               })

               this.selectedTagIds.push(tags.id);
               this.setSelectedTagForms(false, tags);
          } else {
               this.removeSelectedTag(tagid);
          }
     }

     removeSelectedTag(id) {
          console.group("removeSelectedTag = " + id);
          var index = this.selectedTags.indexOf(id);
          console.log("index==>" + index);
          if (index > -1) {
               if (this.selectedTags.length == 1) {
                    console.log("tagCloseWithoutSaveData calledddddddddddd")

                    this.deleteRemovedTagFromList(index, id);

               } else {
                    this.deleteRemovedTagFromList(index, id);
               }
          }
     }

     deleteRemovedTagFromList(index, id) {
          delete this.selectedTags[index];
          this.setOrResetSelectedItemList(id);
          this.setSelectedTagForms(true);
          this.selectedTags = this.selectedTags.filter((id) => {
               console.log(id);
               return id != ""
          });
          console.log(this.selectedTags);
          // this.setIframeUrlstaffFill();
     }

     checkTagExist(user) {

          if (this.selectedTags.indexOf(String(user)) > -1) {
               return true;
          }
          return false;
     }

     setOrResetSelectedItemList(id) {
          $("#" + id).remove();
          return true;
     }

     doneSelectedTag() {
          if (this.selectedTags && this.selectedTags.length)
               this.enableOrDisableUiLI(false, true, "R");
          $('#tagsInput').text('');
     }

     setSelectedTagForms(fromRemove = false, users: any = "") {
          if (this.selectedTags && this.selectedTags.length > 0) {
               var tags = this.selectedTags;
               var tagName = this.selectedTagNames;

               if (tags.length) {
                    this.stafffillfirst = true;
               } else {
                    this.stafffillfirst = false;
               }

               tags = tags.filter(a => a.indexOf('tag-') === -1)
               var recipi = this.selectedTags;

               if (tags.length <= 0 && recipi.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join() == "") {
                    this.stafffillfirst = false;
               }

               if (!fromRemove) {
                    var textData = users.tag_name;
                    $("<span/>", {
                         "class": 'tag-span',
                         'id': users.id,
                         text: textData,
                         insertBefore: $(".recipient-search-area")
                    }).append("<span class='remove' id=" + users.id + ">x</span>");
               }



               // var activityData = {
               //   activityName: "Select Recipients",
               //   activityType: "structured forms",
               //   activityDescription: this.userData.displayName + "(" + this.userData.userId + ") selected recipient -" + this.selectedRecipients + " Form - " + this.activeForms.name + " (" + this.activeForms.id + ") "
               // };
               // this._structureService.trackActivity(activityData);
          }
     }



     careGiverChange() {
          this.selectedObj = "";
          const val = this.newPatient.get('is_caregiver').value;
          if (val) {

               this.newPatient.get('cfname').setValidators([Validators.required]);
               this.newPatient.get('cfname').updateValueAndValidity();
               this.newPatient.get('clname').setValidators([Validators.required]);
               this.newPatient.get('clname').updateValueAndValidity();
               this.newPatient.get('dob').setValidators([Validators.required]);
               this.newPatient.get('dob').updateValueAndValidity();
          } else {
               this.newPatient.get('cfname').clearValidators();
               this.newPatient.get('cfname').updateValueAndValidity();
               this.newPatient.get('clname').clearValidators();
               this.newPatient.get('clname').updateValueAndValidity();
               this.newPatient.get('dob').clearValidators();
               this.newPatient.get('dob').updateValueAndValidity();
               this.newPatient.get('mrn').clearValidators();
               this.newPatient.get('mrn').updateValueAndValidity();
          }
     }

     validateDuplication(formdata) {
          console.log('validateDuplication');
          let mrnExists = false;
          let userExists = false;
          if (formdata.email && formdata.email != '') {

           
               /**
                * To check the user exists with same email address or MRN
                */
               const data = {
                    operation: 'checking',
                    esiOperation: (formdata.mrn) ? true : false,
                    MRNumber: formdata.mrn,
                    externalSystemId: formdata.utype === 'staff' ? formdata.sysId : formdata.mrnSys,
                    // organization_master_id: parseInt(this.userData.organizationMasterId),
                    // tenantId: this._structureService.getCookie("crossTenantId"),
                    userId: this.AlternateeditId ? this.AlternateeditId : ''
               }

               this._enrollService.checkUserExists(data).then((d: any) => {
                    if (data.esiOperation && d.esiExist) {
                         let msg = `The given value of MRN (${data.MRNumber}) is already in use by another another user ${d.displayname}`;
                         const notify = $.notify(msg);
                         setTimeout(function () {
                              notify.update({
                                   'type': 'warning',
                                   'message': '<strong>' + msg + '</strong>'
                              });
                         }, 1000);
                         mrnExists = true;
                         return false;
                    } else if (d.userExist) {
                         let msg = `The given value of user (${data.userId}) is already in use by an another user ${d.data.displayname}`;
                         const notify = $.notify(msg);
                         setTimeout(function () {
                              notify.update({
                                   'type': 'warning',
                                   'message': '<strong>' + msg + '</strong>'
                              });
                         }, 1000);
                         userExists = true;
                         return false;
                    }

                    if (!d.userExists && !d.mrnExists) {                       
                         formdata.email = this.replaceSpecialChar(formdata.email.trim());
                         formdata.email =formdata.email;                         
                         formdata.firstName = this.replaceSpecialChar(formdata.firstName.trim());
                         formdata.firstName = formdata.firstName;                         
                         formdata.lastName = this.replaceSpecialChar(formdata.lastName.trim());
                         formdata.lastName = formdata.lastName;                         
                         formdata.countryCode = (this.userInfo.countryCode) ? this.userInfo.countryCode : '+1';
                         formdata.mobile = formdata.mobile.trim();                         
                         delete formdata.alternateMobile;
                         formdata.tenantId = this._structureService.getCookie("crossTenantId");
                         formdata.status = 1;
                         formdata.source = 6;
                         formdata.patientId = this.patientId;
                         var ActivityDescription = "Name (" + formdata.firstName + " " + formdata.lastName + ")";
                         if (formdata.email) {
                              ActivityDescription = ActivityDescription + "Email (" + formdata.email + ")";
                         }
                         if (formdata.mobile) {
                              ActivityDescription = ActivityDescription + "Mobile (" + formdata.mobile + ")";
                         }
                         ActivityDescription = ActivityDescription + "For Patient Id (" + formdata.patientId + ")";
                         if (this.AlternateeditId) {                                
                              this.showConfirmation(formdata);
                         } else {
                              var confirmMsg = 'Do you want to add Alternate Contact for this patient.';
                              swal({
                                   title: 'Are you sure?',
                                   text: confirmMsg,
                                   type: 'warning',
                                   showCancelButton: true,
                                   cancelButtonClass: 'btn-default',
                                   confirmButtonClass: 'btn-warning',
                                   confirmButtonText: 'Ok',
                                   closeOnConfirmsysId: true
                              }, (confirm) => {
                                   if (confirm) {
                                        this._structureService.createAlternateContact(formdata).then((res: any) => {
                                             if(res.status == 1) {
                                                  let msg = "Successfully added Alternate Contact"
                                                  var notify=$.notify(msg);
                    
                                                  setTimeout(()=> {
                                                       notify.update( {
                                                                 'type': 'success',
                                                                 'message': '<strong>'+ msg + '</strong>'
                                                            }
                    
                                                       );                                                       
                                                       localStorage.setItem('contactAdded', '1'); 
                                                       this.tabSelect = 'alternate-contacts';
                                                       this.activePatientTab = 'alternate-contacts';   
                                                       this._sharedService.showAlternateTab = true;                                           
                                                       this.router.navigate(['/add-user/add/'+this.patientId]);
                                                  }, 2000);
                                                  var activityData = {
                                                       activityName: "Add Alternate Contact",
                                                       activityType: "manage Alternate contacts",
                                                       activityDescription: this.userData.displayName + " (User-" + this.userData.userId + ") Added the alternate contact "+ActivityDescription
                                                  };
                                                  this._structureService.trackActivity(activityData);
                                             } else {
                                                  let msg = "Something Went Wrong"
                                                  var notify=$.notify(msg);
                    
                                                  setTimeout(()=> {
                                                       notify.update( {
                                                                 'type': 'warning',
                                                                 'message': '<strong>'+ msg + '</strong>'
                                                            }
                    
                                                       );                                      
                                                  }, 2000);
                                             }
                                        });
                                   } else {

                                   }
                              });
                         }                         

                    }
               });
          } else if (formdata.utype == 'patient') {
               // this.checkDuplicateExistWithDob(formdata);
          }
     }

     removeDeletedUsers(allusers) {
          let users = allusers.filter((row) => {
               if (row.status != 8) {
                    return true;
               }
          });

          return users;
     } 

     registerAltenatecontact() {
          var formdata = this.newPatient.value;
          
          if (formdata.email.trim() == '' && formdata.mobile.trim() == '') {
               const notify = $.notify('Email or Mobile Number is mandatory.');
               setTimeout(function () {
                    notify.update({ 'type': 'warning', 'message': '<strong>Email or Mobile Number is mandatory.</strong>' });
               }, 1000);
               return false;
          }

          if (!this.newPatient.valid) {
               return false;
          }

          formdata = this.newPatient.value;     
          formdata.enableSmsNotifications = parseInt(formdata.enableSmsNotifications);
          formdata.enableEmailNotifications = parseInt(formdata.enableEmailNotifications);  

          if (formdata.email.trim() !== '') {
               if (this.newPatient.get('email').valid) {
                    this.validateDuplication(formdata);
               } else {
                    const notify = $.notify('Email is not correctly formatted.');
                    setTimeout(function () {
                         notify.update({
                              'type': 'warning',
                              'message': '<strong>Email is not correctly formatted.</strong>'
                         });
                    }, 1000);
                    return false;
               }
          } else {               
               formdata.email = this.replaceSpecialChar(formdata.email.trim());
               if (formdata.email == "" || formdata.email == undefined) {
                    formdata.email = 'unknown_' + formdata.firstName + formdata.lastName + formdata.ESIValue;
               }
               formdata.firstName = this.replaceSpecialChar(formdata.firstName.trim());
               formdata.lastName = this.replaceSpecialChar(formdata.lastName.trim());
               formdata.countryCode = (this.userInfo.countryCode) ? this.userInfo.countryCode : '+1';
               formdata.mobile = formdata.mobile.trim();
               formdata.tenantId = this._structureService.getCookie("crossTenantId");
               formdata.status = 1;
               formdata.source = 6;
               formdata.patientId = this.patientId;  
               var ActivityDescription =      "Name ("+formdata.firstName+" "+formdata.lastName+")";
               if(formdata.email){
                    ActivityDescription = ActivityDescription+"Email ("+formdata.email+")";
               }
               if(formdata.mobile){
                    ActivityDescription = ActivityDescription+"Mobile ("+formdata.mobile+")";
               }
               ActivityDescription = ActivityDescription+"Patient Id ("+formdata.patientId+")";
               if (this.AlternateeditId) {  formdata.firstName
                    this.showConfirmation(formdata);
               } else {                    
                    var confirmMsg = 'Do you want to add Alternate Contact for this patient.';
          
                    swal({
                         title: 'Are you sure?',
                         text: confirmMsg,
                         type: 'warning',
                         showCancelButton: true,
                         cancelButtonClass: 'btn-default',
                         confirmButtonClass: 'btn-warning',
                         confirmButtonText: 'Ok',
                         closeOnConfirmsysId: true
                    }, (confirm) => {
                         if (confirm) {
                              this._structureService.createAlternateContact(formdata).then((res: any) => {
                                   if(res.status == 1) {                              
                                        let msg = "Successfully added Alternate Contact"
                                        var notify=$.notify(msg);

                                        setTimeout(()=> {
                                             notify.update( {
                                                       'type': 'success',
                                                       'message': '<strong>'+ msg + '</strong>'
                                                  }

                                             );                                             
                                             localStorage.setItem('contactAdded', '1');  
                                                                              
                                            this.router.navigate(['/add-user/add/'+this.patientId]);
                                        }, 2000);
                                        var activityData = {
                                             activityName: "Add Alternate Contact",
                                             activityType: "manage Alternate contacts",
                                             activityDescription: this.userData.displayName + " (User-" + this.userData.userId + ") Added the alternate contact "+ActivityDescription
                                        };
                                        this._structureService.trackActivity(activityData);
                                   } else {
                                        let msg = "Something Went Wrong"
                                        var notify=$.notify(msg);

                                        setTimeout(()=> {
                                             notify.update( {
                                                       'type': 'warning',
                                                       'message': '<strong>'+ msg + '</strong>'
                                                  }

                                             );                                      
                                        }, 2000);
                                   }
                              });
                         } else {
                              
                         }
                    });
               }
          }

     }

     deleteUser(formdata) {
          var params = {
               status: 'Deleted'
          }

          this._structureService.updateUser(this.selectedObj.userid, params).then((datasuccess) => {
               this.selectedObj = "";
               if (datasuccess) {
                    this.sendAlternateRegisterData(formdata);
                    if (this.selectedObj.referralCode) {
                         // Remove the entry from invite users (wordpress instance)
                         this._enrollService.deleteFormSubmission(this.selectedObj.referralCode).then((res: any) => {
                              console.log(res);
                         });
                    }
                    var activityData = {
                         activityName: "Delete User",
                         activityType: "manage user access",
                         activityDescription: this.userData.displayName + " (User-" + this.userData.userId + ") deleted the Patient with user id " + datasuccess['updateUser'].id
                    };
                    this._structureService.trackActivity(activityData);
               }

          })
     }

     showConfirmation(formdata) {
          console.log(formdata);
          const userDetails = this._structureService.getUserdata();
          let confirmMsg = 'Do you want to add another Alternate Contact for this patient.';
          if (this.AlternateeditId) {
               confirmMsg = 'Do you want to update Alternate Contact for this patient.';
          
               swal({
                    title: 'Are you sure?',
                    text: confirmMsg,
                    type: 'warning',
                    showCancelButton: true,
                    cancelButtonClass: 'btn-default',
                    confirmButtonClass: 'btn-warning',
                    confirmButtonText: 'Ok',
                    closeOnConfirmsysId: true
               }, (confirm) => {
                    if (confirm) {
                         if (this.AlternateeditId) {
                              formdata.contactId = this.AlternateeditId;
                              formdata.patientId = this.patientId;
                              formdata.tenantId = this._structureService.getCookie("crossTenantId");                          
                              localStorage.setItem('contactAdded', '1');                                                            
                              this._structureService.editsingleAlternateContact(formdata).then((res:any) => {
                                   let msg = `Successfully updated alternate contact details.`;
                                   if (res.status != null) {
                                             var notify = $.notify(msg);
                                        setTimeout(() => {
                                             notify.update({
                                                  'type': 'success',
                                                  'message': '<strong>' + msg + '</strong>'
                                             });                                             
                                             localStorage.setItem('contactAdded', '1');                                                  
                                             // this.router.navigate([localStorage.getItem('returnFromAlternateContact')]);
                                        }, 2000);
                                        var activityData = {
                                             activityName: "Edit Alternate Contact",
                                             activityType: "manage Alternate contacts",
                                             activityDescription: this.userData.displayName + " (User-" + this.userData.userId + ") edited the alternate contact with user id " + formdata.contactId
                                        };
                                        this._structureService.trackActivity(activityData);
                                        if(localStorage.getItem('returnFromAlternateContact') && localStorage.getItem('returnFromAlternateContact') != "") {
                                             this.router.navigate([localStorage.getItem('returnFromAlternateContact')]);   
                                        } else {
                                             this.router.navigate(['/add-user/add/'+this.patientId]);
                                        }
                                   }               
                              });                                                       

                         } else {
                              console.log(formdata);
                              this.resetForm();
                         }
                    } else {
                         // return false;
                    
                              console.log('before submit');
                              localStorage.setItem('contactAdded', '1');                              
                              console.log('going to list');                              
                              if(localStorage.getItem('returnFromAlternateContact') && localStorage.getItem('returnFromAlternateContact') != "") {
                                   this.router.navigate([localStorage.getItem('returnFromAlternateContact')]);
                              } else {
                                   if (localStorage.getItem('patientId')!="") {
                                        this.router.navigate(['/add-user/add/' + this.patientId]);
                                   } else {
                                        this.router.navigate(['/add-user/add']);
                                   }
                              }
                    }
               });
          }
     }

     private sendAlternateRegisterData(formdata) {
          const userDetails = this._structureService.getUserdata();
          console.log("formdata", formdata);
          const bDate = moment($('#dob-date-picker').val());

          var tags = [];

          var vuData;
           vuData = {
               email: this.replaceSpecialChar(formdata.email.trim()),
               firstname: this.replaceSpecialChar(formdata.firstName.trim()),
               lastname: this.replaceSpecialChar(formdata.lastName.trim()),
               //  dob: (bDate.isValid()) ? bDate.format('MMM D,YYYY') : '',
               countryCode: (this.userInfo.countryCode) ? this.userInfo.countryCode : '+1',
               cell: formdata.mobile.trim(),
               zipcode: (formdata.zipcode) ? formdata.zipcode.trim() : '',
               // tenantId: this._structureService.getCookie("crossTenantId"),
               mrn: (formdata.utype == 'staff' || formdata.utype == 'patient') ? formdata.mrn : formdata.partnerId,
               externalSystemId: (formdata.utype == 'staff' ? formdata.sysId : formdata.mrnSys),
               companyNursingAgency: (formdata.companyNursingAgency && formdata.companyNursingAgency != "") ? formdata.companyNursingAgency : '',
               // createdBy: userDetails.userId,
               tenantRoleId: (formdata.utype == 'staff' ? formdata.userRole : formdata.partnerRole),
               citusRoleId: formdata.citusRoleId,
               // staffRole: formdata.staffRole,
               source: "AddAlternatecontact",
               // userType: formdata.utype,
               // tags: tags,
               alernateContacts: []
          };

          console.log('vuData');
          console.log(vuData);

          if (formdata.createPassword && formdata.temppassword != '' && formdata.temppassword == formdata.repeatPassword) {

               var env = this._structureService.environment;
               if (env)
                    env = env;
               else
                    env = 'devl';

               var details = 'userName=' + encodeURIComponent(this.replaceSpecialChar(formdata.email.trim())) +
                    '&displayName=' + encodeURIComponent(this.replaceSpecialChar(formdata.pfname.trim()) + ' ' + this.replaceSpecialChar(formdata.lastName.trim())) +
                    '&firstname=' + encodeURIComponent(this.replaceSpecialChar(formdata.pfname.trim())) +
                    '&lastname=' + encodeURIComponent(this.replaceSpecialChar(formdata.lastName.trim())) +
                    '&password=' + encodeURIComponent(formdata.temppassword) +
                    '&role=' + (formdata.utype != 'patient' ? formdata.citusRoleId : '3') +
                    '&tenantRole=' + (formdata.utype == 'staff' ? formdata.userRole : formdata.partnerRole) +
                    '&mail=' + encodeURIComponent(this.replaceSpecialChar(formdata.email.trim())) +
                    '&country_code=' + encodeURIComponent((formdata.countryCode) ? formdata.countryCode : '+1') +
                    '&mobile=' + formdata.mobile.trim() +
                    '&mobileVerified=' + (formdata.mobileVerified ? 1 : 2) +
                    '&emailVerified=' + (formdata.emailVerified ? 1 : 2) +
                    '&registrationToken=' + this.registrationToken +
                    '&environment=' + env + '&isAccept=true&dob=' + encodeURIComponent((bDate.isValid()) ? bDate.format('MMM D,YYYY') : '') +
                    '&address=&state=&country=&city=&zip=' + encodeURIComponent((formdata.zipcode) ? formdata.zipcode.trim() : '') +
                    '&patient_associated_id=0&isFamilyMember=false&familyMemberName=undefined' +
                    '&companyNursingAgency=' + vuData.companyNursingAgency +
                    '&externalSystemId=' + (formdata.utype == 'staff' ? formdata.sysId : formdata.mrnSys) +
                    '&MRNumber=' + (formdata.utype == 'staff' ? formdata.mrn : formdata.partnerId) +
                    '&registrationType=6' +
                    '&tags=' + encodeURIComponent(tags.join(','));

               this.registerUserDetails(details, formdata);

          } else {
               this._structureService.createVirtualUser(vuData).then((res: any) => {
                    let msg = `Successfully added Alternate Contact`;

                    if (this.editId && this.editId != '') {
                         msg = `Successfully updated patient details.`;
                    }

                    if (res.status == 1) {
                         vuData.tenantId = this._structureService.getCookie("crossTenantId");

                         if (this.editId && this.editId != '') {
                              var notify = $.notify(msg);
                              setTimeout(() => {
                                   notify.update({
                                        'type': 'success',
                                        'message': '<strong>' + msg + '</strong>'
                                   });
                                   this.registerBack();
                              }, 2000);
                              var activityData = {
                                   activityName: "Edited patient via User Center",
                                   activityType: "add user",
                                   activityDescription: "Edited patient (" + vuData.email + ") via User Center",
                                   tenantId: vuData.tenantId,
                              };


                         } else {
                              if (res.already) {
                                   msg = "User already exists!";
                                   var notify = $.notify(msg);
                                   setTimeout(function () {
                                        notify.update({
                                             'type': 'warning',
                                             'message': '<strong>' + msg + '</strong>'
                                        });
                                   }, 2000);

                                   var activityData = {
                                        activityName: "Added User via User Center already exists.",
                                        activityType: "add user",
                                        activityDescription: "Added User via User Center already exists.",
                                        tenantId: vuData.tenantId,
                                   };


                              } else {
                                   var notify = $.notify(msg);
                                   setTimeout(() => {
                                        notify.update({
                                             'type': 'success',
                                             'message': '<strong>' + msg + '</strong>'
                                        });
                                        this.registerBack();
                                   }, 2000);

                                   var activityData = {
                                        activityName: "Added User via User Center",
                                        activityType: "add user",
                                        activityDescription: "Added User (" + vuData.email + ") via User Center",
                                        tenantId: vuData.tenantId,
                                   };

                              }
                         }


                    } else {
                         msg = "Something went wrong!"
                         var notify = $.notify(msg);
                         setTimeout(function () {
                              notify.update({
                                   'type': 'error',
                                   'message': '<strong>' + msg + '</strong>'
                              });
                         }, 2000);

                         var activityData = {
                              activityName: "Added User failed via User Center",
                              activityType: "add user",
                              activityDescription: "Added User failed (" + vuData.email + ") via User Center",
                              tenantId: vuData.tenantId,
                         };
                    }
                    this._structureService.trackActivity(activityData);
               });
          }

          localStorage.setItem('addUserTab', formdata.utype);
     }

     registerUserDetails(details, formdata) {
          this.registrationservice.signup(details).then((data) => {
               this.dataResponseUser = data;
               if (!this.dataResponseUser.uid) {
                    var msg = this.dataResponseUser.errorMessage
                    var notify = $.notify(msg);
                    setTimeout(function () {
                         notify.update({
                              'type': 'error',
                              'message': '<strong>' + msg + '</strong>'
                         });
                    }, 2000);

                    var activityData = {
                         activityName: "Failure User Registration via User Center",
                         activityType: "user creation",
                         activityDescription: "User registration failed via User Center. " + this.dataResponseUser.errorMessage + ". Details: " + details,
                         tenantId: formdata.tenantId,
                    };

                    this._structureService.trackActivity(activityData);

               } else {

                    if (this.dataResponseUser.wpEnrollUrl) {
                         // create shortcode
                         const reqData = {
                              "tenantId": this.registrationToken,
                              "enrollSource": "chwebsite",
                              "enrollType": "Enrollment",

                              "userInfo": {
                                   "firstName": formdata.firstname,
                                   "lastName": formdata.lastname,
                                   "honorificPrefix": "",
                                   "honorificSuffix": "",
                                   "designation": "",
                                   "organization": this.dataResponseUser.tenantName,
                                   "email": formdata.email,
                                   "mobile": formdata.cell,
                                   "countryCode": formdata.countryCode
                              }
                         };

                         this.registrationservice.createReferralCodes(reqData, this.dataResponseUser.wpEnrollUrl).then((data: any) => {
                              if (data.status && data.status.statusCode != "0") {
                                   const activityData = {
                                        activityName: "Failure Referral Token Create - Signup via User Center",
                                        activityType: "manage user enrollment",
                                        activityDescription: "Referral token creation for the user " + formdata.displayName + "(" + this.dataResponseUser.uid + ") via User Center. Failed due to " + data.status.statusMessage,
                                        tenantId: this.registrationTenantId,
                                   };
                                   this._structureService.trackActivity(activityData);
                              }
                         });
                    }

                    var activityData = {
                         activityName: "User Registration via via User Center",
                         activityType: "user creation",
                         activityDescription: "New user registered (" + formdata.email + ") via User Center",
                         tenantId: formdata.tenantId,
                    };

                    if (this.dataResponseUser.isExistingUser) {
                         activityData.activityName = "User Reactivation via User Center";
                         activityData.activityDescription = "Reactivated the existing user (" + formdata.email + ") via User Center";
                    }

                    this._structureService.trackActivity(activityData);

                    var successMessage;

                    if (this.dataResponseUser.isExistingUser) {
                         successMessage = this.dataResponseUser.isExistingUser + " ";
                    } else {
                         successMessage = 'Registration Successful. A welcome message send to the email ' + this.replaceSpecialChar(formdata.email.trim()) + '.';
                    }

                    var notify = $.notify(successMessage);
                    setTimeout(function () {
                         notify.update({
                              'type': 'success',
                              'message': '<strong>' + successMessage + '</strong>'
                         });
                    }, 1000);

                    setTimeout(() => {
                         this.registerBack();
                    }, 2000);
               }
          }).catch((ex) => { });
     }

     conditionalValidator(condition: (() => boolean), validator: ValidatorFn): ValidatorFn {
          return (control: AbstractControl): {
               [key: string]: any
          } => {
               if (!condition()) {
                    return null;
               }
               return validator(control);
          }
     }

     private makeid() {
          let text = '';
          const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
          for (let i = 0; i < 15; i++)
               text += possible.charAt(Math.floor(Math.random() * possible.length));
          return text;
     }

     userCategoryChange(type) {
          console.log('userCategoryChange called from add-user addUserTab', localStorage.getItem('addUserTab'));
          console.log('userCategoryChange called from add-user type', type);
          this.selectedObj = "";

          if (localStorage.getItem('addUserTab') == type) {

          }

          if (type == 'patient') {
               console.log('patient clicked');
               localStorage.setItem('addUserTab', 'patient');
               this.router.navigate(['/add-user/search-user']);
          }

          if (type == 'partner') {
               localStorage.setItem('addUserTab', 'partner');
               this.resetTag();
               this.formGroupDirective.resetForm();
               this.ngOnInit();
          }

          if (type == 'staff') {
               localStorage.setItem('addUserTab', 'staff');
               this.resetTag();
               this.formGroupDirective.resetForm();
               this.ngOnInit();
          }

          var paramthis = this;
          $(".remove").click({
               datathis: paramthis
          }, getInfo);

          function getInfo(event) {
               let self = event.data.datathis;
               var delid = $(event.target).parent().attr("id");
               self.removeSelectedTag(delid);
          }
     }

     changePartnerCategory() {
          if (this.manageConfig.enable_patient_info_from_third_party_app == '1') {
               var physicianRoles = this.manageConfig.auxilary_physician_roles_for_manage_security_rules.split(',');
               var nursingRoles = this.manageConfig.auxilary_nursing_agency_roles_for_manage_security_rules.split(',');
               console.log('physicianRoles', physicianRoles);
               console.log('nursingRoles', nursingRoles);

               if (this.manageConfig.enable_patient_info_from_third_party_app == '1') {
                    var physicianRoles = this.manageConfig.auxilary_physician_roles_for_manage_security_rules.split(',');
                    var nursingRoles = this.manageConfig.auxilary_nursing_agency_roles_for_manage_security_rules.split(',');
                    console.log('physicianRoles', physicianRoles);
                    console.log('nursingRoles', nursingRoles);

                    if (physicianRoles.includes($('select[name="partnerCategory"]').val())) {
                         this.showAuxilaryPhysicianOrNursingLabel = true;
                         this.auxilaryPhysicianOrNursingLabel = this.manageConfig.auxilary_physician_label;
                         this.newPatient.patchValue({
                              mrnSys: this.manageConfig.esi_code_for_auxilary_physician
                         });
                      
                    } else
                         if (nursingRoles.includes($('select[name="partnerCategory"]').val())) {
                              this.showAuxilaryPhysicianOrNursingLabel = true;
                              this.auxilaryPhysicianOrNursingLabel = this.manageConfig.auxilary_nursing_agency_label;
                              this.newPatient.patchValue({
                                   mrnSys: this.manageConfig.esi_code_for_auxilary_nursing_agency
                              });
                          
                         } else {
                              this.showAuxilaryPhysicianOrNursingLabel = false;
                              //   this.newPatient.get('partnerId').clearValidators();
                              //   this.newPatient.get('partnerId').updateValueAndValidity();
                         }
               } else {
                    this.showAuxilaryPhysicianOrNursingLabel = false;
                    //   this.newPatient.get('partnerId').clearValidators();
                    //   this.newPatient.get('partnerId').updateValueAndValidity();
               }



               console.log("this.auxilaryPhysicianOrNursingLabel", this.auxilaryPhysicianOrNursingLabel);
          }
     }


     searchOnEnter(event) {
          var txt = $('#tagsInput').val().trim();
          if (event.keyCode == 13) {
               if (txt) {
                    this.setTag(txt);
               }
          }
     }

     closeInvite() {
          $('.overlay-invite').hide();
     }


     showInvite() {

          $('.popup-invite').css('display', 'flex');
          $('.overlay-invite').css('display', 'flex');
          /* $('.overlay-invite').show();*/
     }

     proceedInvite() {
          //if(this.selectedObj) {
          //this._structureService.getPatient(this.selectedObj.userid).then((data) => {
          // if (data['getSessionTenant']) {
          // if (data['getSessionTenant'].patientUsers.length > 0) {
          // this.puser = JSON.parse(JSON.stringify(data['getSessionTenant'].patientUsers[0]));
          this.closeInvite();
          if (this.selectedObj.associateCaregiverCount && this.selectedObj.associateCaregiverCount != "0") {
               this.selectedObj = "";
               let msg = `You are not allowed to invite a patient associated with a caregiver`;
               const notify = $.notify(msg);
               setTimeout(function () {
                    notify.update({
                         'type': 'warning',
                         'message': '<strong>' + msg + '</strong>'
                    });
               }, 1000);
               return false;
          } else if (this.selectedObj.associatePatientsCount && this.selectedObj.associatePatientsCount != "0") {
               this.selectedObj = "";
               let msg = `You are not allowed to invite a caregiver associated with a patient`;
               const notify = $.notify(msg);
               setTimeout(function () {
                    notify.update({
                         'type': 'warning',
                         'message': '<strong>' + msg + '</strong>'
                    });
               }, 1000);
               return false;
          }

          const userDetails = this._structureService.getUserdata();
          let user = "patient";
          if (this.selectedObj.grp == 3) {
               user = "patient";
          } else if (this.selectedObj.grp == 20) {
               user = "partner";
          } else {
               user = "staff";
          }

          if (this.selectedObj.tenantid == userDetails.crossTenantId && this.selectedObj.user == "real") {
               let msg = `This ${this.newPatient.get('utype').value}, ${this.selectedObj.firstname} ${this.selectedObj.lastname} is active in the current tenant. You are not allowed to re-invite an active user within the current tenant.`;
               const notify = $.notify(msg);
               setTimeout(() => {
                    notify.update({
                         'type': 'warning',
                         'message': '<strong>' + msg + '</strong>'
                    });
               }, 1000);
               this.selectedObj = "";
               return false;
          } else if (this.selectedObj.tenantid != userDetails.crossTenantId && this.selectedObj.user == "virtual") {
               this.selectedObj = "";
               let msg = `Virtual user is in different tenant. Not able to proceed`;
               const notify = $.notify(msg);
               setTimeout(function () {
                    notify.update({
                         'type': 'warning',
                         'message': '<strong>' + msg + '</strong>'
                    });
               }, 1000);
               return false;
          }

          if ((this.newPatient.get('utype').value == 'staff' || this.newPatient.get('utype').value == 'partner') && this.selectedObj.user == "virtual") {
               this.selectedObj = "";
               let msg = `You are not allowed to convert a virtual patient to ${this.newPatient.get('utype').value}`;
               const notify = $.notify(msg);
               setTimeout(function () {
                    notify.update({
                         'type': 'warning',
                         'message': '<strong>' + msg + '</strong>'
                    });
               }, 1000);
               return false;
          }
          console.log(this.selectedObj);
          this.prepopulateData();

          


     }

     prepopulateData() {
          console.log(this.selectedObj);
          if (this.selectedObj.user == "real") {
               $('#email-address').val(this.selectedObj.username);
               if (this.selectedObj.country_code && this.selectedObj.country_code != null) {
                    $('#country_code').intlTelInput('setNumber', this.selectedObj.country_code);
               }
               if (this.selectedObj.dob) {
                    let ar = this.selectedObj.dob.split('-');
                    console.log(ar);
                    if (ar.length) {
                         $(".year").val(parseInt(ar[0])).trigger('change');
                         $(".month").val(parseInt(ar[1]) - 1).trigger('change');
                         setTimeout(() => {
                              console.log(ar[2]);
                              $(".day").val(parseInt(ar[2])).trigger('change');
                         }, 1);

                    }

               }
               this.newPatient.patchValue({
                    mobile: (this.selectedObj.mobile) ? this.selectedObj.mobile.replace(/-/g, "") : '',
                    email: (this.selectedObj.username) ? this.selectedObj.username : '',
                    zipcode: (this.selectedObj.zip) ? this.selectedObj.zip : '',
                    pfname: (this.selectedObj.firstname) ? this.selectedObj.firstname : '',
                    lastName: (this.selectedObj.lastname) ? this.selectedObj.lastname : ''
               });

               if (this.selectedObj.country_code && this.selectedObj.country_code != null) {
                    this.newPatient.patchValue({
                         countryCode: this.selectedObj.country_code
                    });
               } else {
                    const cntryDetails = $('#country_code').intlTelInput('getSelectedCountryData');
                    this.userInfo.countryCode = '+' + cntryDetails.dialCode;
                    this.newPatient.patchValue({
                         countryCode: this.userInfo.countryCode
                    });
               }


          }

     }

     registerBack() {
          console.log('Here');
          //this.resetForm();
          if (this.tabSelect == 'patient') {
               this.router.navigate(['/add-user/search-user']);
          } else {
               setTimeout(() => {
                    this.formGroupDirective.resetForm();
                    this.resetTag();
                    this.ngOnInit();
                    $('#email-address').val("");

               }, 0);
          }
     }

     cancelAdd() {
          console.log('Here');
          console.log(this.patientId);
          if(localStorage.getItem('returnFromAlternateContact') && localStorage.getItem('returnFromAlternateContact') != "") {
               this.router.navigate([localStorage.getItem('returnFromAlternateContact')]);
          } else {
               //this.resetForm();
               if (this.patientId) {
                    this.router.navigate(['/add-user/add/'+this.patientId]);
               } else {
                    this.tabSelect = 'alternate-contacts';
                    this.activePatientTab = 'alternate-contacts';
                    console.log('going');
                    console.log(this.activePatientTab);                    
                    this.router.navigate(['/add-user/add']);
               }
          }          
     }

     resetForm() {

          var val = this.tabSelect;

          //  this.togglePreference('createPassword', false);

          this.newPatient.get('firstName').setValue('');
          this.newPatient.get('lastName').setValue('');
          this.newPatient.get('email').setValue('');
          this.newPatient.get('ESIValue').setValue('');
          this.newPatient.get('mobile').setValue('');
          this.newPatient.get('relation').setValue('');
          this.newPatient.get('alternateMobile').setValue('');
          

          this.newPatient.get('firstName').clearValidators();
       //   this.newPatient.get('firstName').setValidators([Validators.required]);
         // this.newPatient.get('lastName').setValidators([Validators.required]);
          this.newPatient.get('lastName').updateValueAndValidity();

          this.newPatient.get('email').setValue('');

          //const uType = (val === 'patient') ? 'patient' : 'staff';
          const uType = val;

          // this.newPatient.get('userRole').updateValueAndValidity();

          setTimeout(() => {
             

               $('#country_code').intlTelInput();
               const countryDetails = setCountryCodeFlag('country_code', '', '');
               this.newPatient.patchValue({
                    countryCode: countryDetails.countryCode,
                    countryIsoCode: countryDetails.countryIsoCode
               });
               this.userInfo.countryCode = '+' + countryDetails.countryCode;
               this.userInfo.countryIsoCode = this.userCountry = countryDetails.countryIsoCode;
               $('body').off('keyup', '#email-address');

               $('body').on('keyup', '#email-address', (e) => {
                    this.newPatient.patchValue({
                         email: $('#email-address').val()
                    });
               });
               $('body').off('countrychange', '#country_code');
          }, 100);
     }

}
