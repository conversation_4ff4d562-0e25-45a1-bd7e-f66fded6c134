import { TestBed, inject } from '@angular/core/testing';
import { HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Apollo, ApolloModule } from 'apollo-angular';
import { PermissionService } from 'app/services/permission/permission.service';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { provideClients } from 'test-utils';
import { AuthService } from '../shared/auth.service';
import { SharedService } from '../shared/sharedServices';
import { StoreService } from '../shared/storeService';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { WorklistIndexdbService } from '../worklists/worklist-indexdb.service';
import { AddUserService } from './adduser.service';

describe('AddUserService', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        AddUserService,
        StructureService,
        Apollo,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        TranslateService,
        PermissionService,
        StoreService
      ],
      imports: [
        RouterTestingModule,
        HttpModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF,
        }),
        TranslateModule.forRoot(),
      ],
    });
  });

  it('should create the service', inject(
    [AddUserService],
    (service: AddUserService) => {
      expect(service).toBeTruthy();
    }
  ));
});
