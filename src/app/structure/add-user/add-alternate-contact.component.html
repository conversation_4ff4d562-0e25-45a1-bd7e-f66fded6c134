<style>
    .show-password {
        position: absolute;
        top: 12px;
        right: 22px;
        cursor: pointer;
    }
</style>
<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong id="invite-initiate-label">{{pageTitle}}</strong>
        </span>
    </div>

    <div class="card-block" >
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item" *ngIf="returnFromAlternateContact.includes('pah')">
                <a [routerLink]="[editpatientUrl]">PAH</a>
            </li>
            <ng-container *ngIf="returnFromAlternateContact != '' && !returnFromAlternateContact.includes('pah')">
                <li class="breadcrumb-item"><a [routerLink]="['/users/patients']">Users Settings</a></li>
                <li class="breadcrumb-item"><a [routerLink]="['/users/patients']">Patients</a></li>
                <li class="breadcrumb-item"><a [routerLink]="[editpatientUrl]">Edit Patients</a></li>
            </ng-container>
            <li class="breadcrumb-item">{{pageTitleBreadcrumbs}}</li>
        </ol>
        <div class="row">
            <div class="col-lg-12">
                <div class="mb-5">
                    <form class="form-horizontal enroll-form" (ngSubmit)="registerAltenatecontact()"
                        [formGroup]="newPatient" novalidate #f="ngForm">
                        <div class="form-body">

                            <div id="firstname" class="form-group row">
                                <label class="col-md-3 control-label">{{'LABELS.ALTERNATE_CONTACT_FIRST_NAME' | translate}} *</label>
                                <div class="col-md-6">
                                    <input type="text" (keypress)="omit_special_char($event)" class="form-control" xssInputValidate="{{'LABELS.ALTERNATE_CONTACT_FIRST_NAME' | translate}}"
                                        id="firstnme" name="firstName" placeholder="First Name"
                                        [formControl]="newPatient.controls['firstName']">
                                    <div class="alert alert-danger"
                                        *ngIf="!newPatient.controls.firstName.valid && (newPatient.controls.firstName.dirty || newPatient.controls.firstName.touched || f.submitted)">
                                        {{'VALIDATION_MESSAGES.FIRST_NAME_EMPTY' | translate}}
                                    </div>
                                </div>
                            </div>
                            <div id="lastname" class="form-group row">
                                <label class="col-md-3 control-label">{{'LABELS.ALTERNATE_CONTACT_LAST_NAME' | translate}} *</label>
                                <div class="col-md-6">
                                    <input type="text" (keypress)="omit_special_char($event)" class="form-control"
                                        id="lastName" name="lastName" placeholder="Last Name" xssInputValidate="{{'LABELS.ALTERNATE_CONTACT_LAST_NAME' | translate}}"
                                        [formControl]="newPatient.controls['lastName']">
                                    <div class="alert alert-danger"
                                        *ngIf="!newPatient.controls.lastName.valid && (newPatient.controls.lastName.dirty || newPatient.controls.lastName.touched || f.submitted)">
                                        {{'VALIDATION_MESSAGES.LAST_NAME_EMPTY' | translate}}
                                    </div>
                                </div>
                            </div>



                            <div id="mobile-email">                          
                                <div class="row">
                                    <div class="col-md-6 offset-md-3">
                                        <app-display-info infoContent="{{ 'VALIDATION_MESSAGES.EMAIL_OR_MOBILE_NUMBER_MANDATORY' | translate }}"></app-display-info>
                                    </div>
                                </div>
                                <div id="mobile-no" class="form-group row">
                                    <label class="col-md-3 control-label">Mobile Number</label>
                                    <div class="col-md-6">
                                        <div class="row">
                                            <div class="col-md-2 col-xl-2">
                                                <input type="text"
                                                    [ngStyle]="newPatient.controls.countryCode.errors ? {'border-color': 'red'} : {}"
                                                    placeholder="Country Code"
                                                    onkeypress="return (event.charCode >= 48 && event.charCode <= 57) || event.charCode == 43"
                                                    class="form-control  zero-width" id="country_code">
                                            </div>
                                            <div class="col-md-2 col-xl-2">
                                                <input type="text" class="form-control editable-field" id="countryCode" formControlName="countryCode" readonly>
                                            </div>
                                            <div class="col-md-10 col-xl-8">
                                                <input appPhoneNumberValidator [textMask]="{mask: maskPhoneNumber , guide: false}"
                                                    id="mobile" formControlName="mobile" name="mobile"
                                                    placeholder="{{'LABELS.MOBILE_NUMBER' | translate}}"
                                                    class="form-control tel" (blur)="oldPhoneNumberCheck()">
                                                <input type="hidden" id="oldPhoneNumber" name="oldNumber">
                                            </div>
                                            <div class="col-md-2"
                                                *ngIf="verifyPopup && !verifyPopup.verifyMobCodeSend && newPatient.controls.countryCode.valid && newPatient.controls.mobile.value !='' &&(newPatient.controls.mobile.valid || newPatient.controls.mobile.touched) && userData.config.enable_verification_of_cell_and_mobile == 1">
                                                <button type="button" class="btn btn-primary btn-sm"
                                                    *ngIf="!newPatient.controls.mobileVerified.value"
                                                    (click)="verifyPopup.show_popup({mobile:true,formData:newPatient.value})">Verify</button>
                                                <span class="btn btn-primary btn-sm"
                                                    *ngIf="newPatient.controls.mobileVerified.value">Verified</span>
                                            </div>
                                            <app-verify-em></app-verify-em>
                                        </div>
                                        <div class="alert alert-danger"
                                            *ngIf="newPatient.controls.mobile.errors != null">
                                            {{'VALIDATION_MESSAGES.PLEASE_ENTER_VALID_MOBILE_NUMBER' | translate}}
                                        </div>
                                    </div>
                                </div>
                                <div id="email" class="form-group row">
                                    <label class="col-md-3 control-label">Email</label>
                                    <div class="col-md-6">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <p class="ac">
                                                    <input class="form-control" id="email-address" type="text"
                                                        placeholder="Email" formControlName="email"
                                                        (blur)="oldEmailIdCheck()" />
                                                </p>
                                                <p class="ac" id="status-message"></p>
                                                <input type="hidden" class="form-control" id="email" name="email"
                                                    placeholder="Email" [formControl]="newPatient.controls['email']">
                                                <input type="hidden" id="oldemailId" name="oldemail">
                                                <div class="alert alert-danger"
                                                    *ngIf="newPatient.controls.email.errors && newPatient.controls.email.errors.required && (newPatient.controls.email.dirty || newPatient.controls.email.touched || f.submitted)">
                                                    Email Address cannot be empty.
                                                </div>
                                                <div class="alert alert-danger"
                                                    *ngIf="newPatient.controls.email.errors && newPatient.controls.email.errors.pattern && (newPatient.controls.email.dirty || newPatient.controls.email.touched || f.submitted)">
                                                    Please enter a valid email address.
                                                </div>
                                            </div>
                                            <div class="col-md-2"
                                                *ngIf="verifyPopup && !verifyPopup.verifyEmailCodeSend && newPatient.controls.email.value !='' &&(newPatient.controls.email.valid || newPatient.controls.email.touched) && userData.config.enable_verification_of_cell_and_mobile == 1">
                                                <button type="button" class="btn btn-primary btn-sm"
                                                    *ngIf="!newPatient.controls.emailVerified.value"
                                                    (click)="verifyPopup.show_popup({email:true,formData:newPatient.value})">Verify</button>
                                                <span class="btn btn-primary btn-sm"
                                                    *ngIf="newPatient.controls.emailVerified.value">Verified</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div id="alternate-username" class="form-group row" *ngIf="AlternateeditId && newPatient.controls.alternateUsername.value !=='' " >
                                    <label class="col-md-3 control-label">Username</label>
                                    <div class="col-md-6">
                                        <div class="row">
                                            <div class="col-md-12">
                                                <p class="ac">
                                                    <input class="form-control" id="alternate-username" type="text"
                                                        placeholder="Username" [formControl]="newPatient.controls.alternateUsername"
                                                       readonly />
                                                </p>
                                                </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="relation" class="form-group row">
                                <label class="col-md-3 control-label">{{'LABELS.RELATION' | translate}}</label>
                                <div class="col-md-6">
                                    <input type="text" (keypress)="omit_special_char($event)" class="form-control"
                                        id="relation" name="relation" placeholder="Relation" xssInputValidate="{{'LABELS.RELATION' | translate}}"
                                        [formControl]="newPatient.controls['relation']">

                                </div>
                            </div>
                            <span>
                                <div id="associate-mrn-with-patient" style="margin-top:20px;" class="form-group row"
                                    [hidden]="pageNumber !== 0">
                                    <label class="col-md-3 control-label">{{'LABELS.ALTERNATE_CONTACT_ID'| translate}}
                                        <span
                                            *ngIf="manageConfig.make_esi_code_mandatory_in_alternate_contact_invite == '1'">*</span>
                                    </label>

                                    <div class="col-md-6">
                                        <input type="text" [attr.disabled]="(patientList.ESIValue != null && patientList.ESIValue != '')? '' : null" (keypress)="omit_special_char($event)" class="form-control" id="mrn"
                                            name="mrn" placeholder="{{'LABELS.ALTERNATE_CONTACT_ID'| translate}}" [formControl]="newPatient.controls['ESIValue']" xssInputValidate="{{'LABELS.ALTERNATE_CONTACT_ID'| translate}}">

                                        <div class="alert alert-danger"
                                            *ngIf="!newPatient.controls.ESIValue.valid && (newPatient.controls.ESIValue.dirty || newPatient.controls.ESIValue.touched || f.submitted)">
                                            {{ 'VALIDATION_MESSAGES.ALTERNATE_CONTACT_ID_EMPTY' | translate }}
                                        </div>
                                    </div>
                                </div>
                            </span>
                            <div id="enable_sms_notifications" class="form-group row">
                                <label class="col-md-3 control-label">{{'LABELS.ENABLE_SMS_NOTIFICATION' | translate}}</label>
                                <div class="col-md-6">
                                    <div class="btn-group">
                                        <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': newPatient.controls['enableSmsNotifications'].value === 1}"
                                            (click)="togglePreference('enableSmsNotifications', 1)">
                                            {{'BUTTONS.YES' | translate}}
                                        </button>
                                        <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': newPatient.controls['enableSmsNotifications'].value !== 1}"
                                            (click)="togglePreference('enableSmsNotifications', 0)">
                                            {{'BUTTONS.NO' | translate}}
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div id="enable_email_notifications" class="form-group row">
                                <label class="col-md-3 control-label">{{'LABELS.ENABLE_EMAIL_NOTIFICATION' | translate}}</label>
                                <div class="col-md-6">
                                    <div class="btn-group">
                                        <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': newPatient.controls['enableEmailNotifications'].value === 1}"
                                            (click)="togglePreference('enableEmailNotifications', 1)">
                                            {{'BUTTONS.YES' | translate}}
                                        </button>
                                        <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': newPatient.controls['enableEmailNotifications'].value !== 1}"
                                            (click)="togglePreference('enableEmailNotifications', 0)">
                                            {{'BUTTONS.NO' | translate}}
                                        </button>
                                    </div>

                                </div>
                            </div>
                            <button id="btn-submit" [disabled]="disableSubmit" class="btn btn-info">
                                Submit
                            </button>
                            <a id="btn-cancel" class="btn btn-secondary" (click)="cancelAdd()">Cancel</a>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
</section>
<!-- END: tables/datatables -->
