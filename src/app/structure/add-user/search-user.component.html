<!-- START: tables/datatables -->
<style>
  td:not(:first-child):hover,
  td:not(:first-child):hover ~ td {
    cursor: pointer;
  }
  .dataTables_filter {
    text-align: left !important;
  }
  td {
    cursor: pointer;
  }
  .btn-search {
    height: 38px;
    width: 100%;
    border-radius: 10px;
  }
</style>
<section class="card">
  <div class="card-header">
    <span class="cat__core__title">
      <strong id="invite-initiate-label">Add User</strong>
    </span>
    <a [hidden]="!userDetails.config.enable_staff_import || userDetails.config.enable_staff_import != true || !userDetails.privileges.includes('enableImport')" style="float:right;" class="btn btn-sm btn-info" (click)="UploadBtnAction()">Import Users</a> 
    <div class="modal fade data-modal" id="data-modal-upload" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header">
            <h4 class="modal-title" id="exampleModalLabel">Import Users</h4>
            <span *ngIf="showLoading"
              style="color:teal;text-align: center;padding:20px 20px;">Please wait while we process
              your request... <i class="fa fa-refresh fa-spin"></i></span>
          </div>
          <div class="modal-body">
            <div class="row">
              <div class="col-md-12">
                <input type="file" id="bulkDirectFile" accept=".xls,.xlsx,.csv,.ods" name="bulkDirectFile" (change)="uploadFile($event)">
              </div>
            </div>
            <div style="padding-left: 10px;padding-top: 20px;" class="row">
              <p id="result-report"></p>
            </div>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-info" [disabled]="buttonDisabled"
              (click)="saveUploadFile()">Upload</button>
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
          </div>
        </div>
      </div>
    </div>
    <div class="filter-site" style="float: right;width: 28%; padding-right:10px;" [hidden]="!hideSiteSelection">
      <app-select-sites [events]="eventsSubject.asObservable()" (hideDropdown)="hideDropdown($event)" [filterType]=true (siteIds)="getSiteIdsSearch($event)">
      </app-select-sites>
    </div>
<span [hidden]="!hideSiteSelection" class="site-label" style="float: right">{{ labelSiteFilter | translate }}</span>
  </div>
  <div class="card-block">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
      <li class="breadcrumb-item">User Center</li>
      <li class="breadcrumb-item">Add Users</li>
    </ol>
    <form
      class="form-horizontal enroll-form"
      [formGroup]="searchInvitesForm"
      novalidate
      #f="ngForm"
    >
      <div class="form-body">
        <!-- //// -->
        <div class="form-group row" id="user-initiate-category">
          <label class="col-md-3 control-label">User Category</label>
          <div class="col-md-6">
            <label
              *ngIf="userDetails.privileges.includes('allowAddUser') && userDetails.privileges.includes('addVirtualPatientUsers')"
              class="form-check-label"
              style="margin-right:15px;"
            >
              <input
                id="uTypePatient"
                checked=""
                class="form-check-input"
                name="utype"
                value="patient"
                type="radio"
                (click)="userCategoryChange1('patient')"
              />
              Virtual Patient
            </label>
            <label
              *ngIf="userDetails.privileges.includes('allowAddUser') && userDetails.privileges.includes('addVirtualEnrolledStaffUsers')"
              class="form-check-label"
              style="margin-right:15px;"
            >
              <input
                id="uTypeStaff"
                class="form-check-input"
                name="utype"
                value="staff"
                type="radio"
                (click)="userCategoryChange1('staff')"
              />
              Virtual/Enrolled Staff
            </label>
            <label
              *ngIf="userDetails.privileges.includes('allowAddUser') && userDetails.privileges.includes('addVirtualPartnerUsers')"
              class="form-check-label"
            >
              <input
                id="uTypePartner"
                class="form-check-input"
                name="utype"
                value="partner"
                type="radio"
                (click)="userCategoryChange1('partner')"
              />
              Virtual Partner
            </label>
          </div>
        </div>
        <h6>
          <div class="alert alert-info dupilcate-search-info">
            You can search for existing Patient (strongly recommended to avoid
            duplicate Patient records) and edit existing patient details or skip the search
            and directly add Patient information if required.
          </div>
        </h6>
        <br />
        <br />
        <div class="form-group row">
          <label class="col-md-2 control-label"
            >Search Patient: <i chToolTip="searchPatientDuplicate"></i
          ></label>

          <div class="col-md-4">
            <input
              type="text"
              class="form-control"
              formControlName="searchInput"
              id="searchTxt"
            />
            <!-- [formControl]="searchInvitesForm.controls['searchInput']" -->
          </div>
          <div class="col-md-1">
            <button
              type="button"
              class="btn btn-sm btn-search btn-info"
              id="button-search"
              (click)="searchClick()"
            >
              Search
            </button>
          </div>
          <div class="col-md-1">
              <button type="button" class="btn btn-sm btn-search btn-info" id="button-clear" (click)="searchClear()">
                Clear
              </button>
          </div>
          <label style="padding-top: 8px;" class="control-label">OR</label>
          <div class="col-md-3">
            <button
              type="button"
              class="btn btn-sm btn-search btn-info"
              id="button-add-patient-details"
              (click)="addPatientDetails()"
            >
              Add Patient Details
            </button>
          </div>
        </div>
        <div class="row">
          <div class="col-lg-12">
            <div class="mb-5">
              <div class="wait-loading" *ngIf="dataLoadingMsg">
                <img src="./assets/img/loader/loading.gif" />
              </div>
              <div class="data-table">
                <table
                  class="table table-hover table-responsive"
                  id="patient-list"
                  width="100%"
                ></table>
              </div>
            </div>
          </div>
          <!-- <span
            class="col-lg-12 font-weight-bold"
            [hidden]="noRecordMessage === false || dataLoadingMsg == true"
          >
            The Patient
            {{ IsEmail(searchString) == '' ? searchString : '' }} you are
            searching for does not exist in the system. Add new patient by
            clicking on the Invite by adding Patient Details button.
          </span> -->
          <div class="row">
            <div class="col-lg-2"></div>
            <div class="col-lg-10">
              <div
                [hidden]="noRecordMessage === false || dataLoadingMsg == true"
                class="alert alert-info"
              >
                The Patient
                {{ IsEmail(searchString) == '' ? searchString : '' }} you are
                searching for does not exist in the system. Add new patient by
                clicking on the Add Patient Details button.
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>

  <!-- // -->
</section>
<!-- END: tables/datatables -->

<!-- Detail popup starts -->
<div
  class="modal fade forward-modal associated-patient"
  data-backdrop="static"
  id="patientDetailModel"
  role="dialog"
  aria-labelledby="exampleModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-lg" role="document">
    <form
      class=""
      [formGroup]="patientDetails"
      novalidate
      #f1="ngForm"
    >
      <div class="modal-content">
        <div *ngIf="iscareGiverFlag === false">
          <div class="modal-header">
            <h4 class="modal-title" id="exampleModalLabel">Patient Details</h4>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <section>
              <div class="invoice-block">
                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">First Name </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control"
                      disabled
                      formControlName="pFNameDetail"
                    />
                  </div>
                </div>

                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">Last Name </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control"
                      disabled
                      formControlName="pLNameDetail"
                    />
                  </div>
                </div>

                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label"
                      >Date Of Birth (mm/dd/yyyy)
                    </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control"
                      disabled
                      formControlName="pDOBDetail"
                    />
                  </div>
                </div>
                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">Email </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control"
                      disabled
                      formControlName="pEmailDetail"
                    />
                  </div>
                </div>

                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">{{'LABELS.MOBILE_NUMBER' | translate}}</label>
                  </div>

                  <div class="col-md-2">
                    <input
                      type="text"
                      style="width: 86px;"
                      formControlName="pCountryCodeDetails"
                      disabled
                      class="form-control"
                    />
                  </div>
                  <div class="col-md-7">
                    <input
                      type="text"
                      class="form-control"
                      id="us-phone-mask-input"
                      appPhoneNumberValidator [textMask]="{mask: maskPhoneNumber, guide: false}"
                      disabled
                      formControlName="pCellNoDetail"
                    />
                  </div>
                </div>
                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">Source </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control"
                      disabled
                      formControlName="pSourceDetail"
                    />
                  </div>
                </div>

                <!-- <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">Branch </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control"
                      disabled
                      formControlName="pBranchDetail"
                    />
                  </div>
                </div> -->
                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">Site </label>
                  </div>
                  <div class="col-md-9" >
                    <app-select-sites [disableFilter]="true" [crossSite]="true" [events]="eventsSubject.asObservable()" [filterType]="filterType" (siteIds)="getSiteIds($event)" [dynamic]="filterType" [siteSelection]="true" [singleSelection]="true" [selectedSiteIds]="editSiteData" (hideDropdown)="hideDropdown($event)">
                    </app-select-sites>
                  </div>
                </div>
                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">{{ 'LABELS.MRN' | translate  }} </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control"
                      disabled
                      formControlName="pMRNDetail"
                    />
                  </div>
                </div>
                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">Created On </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control "
                      disabled
                      formControlName="pCreatedOnDetail"
                    />
                  </div>
                </div>
                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">Last Login </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control"
                      disabled
                      formControlName="pLastLoginDetail"
                    />
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
        <div *ngIf="iscareGiverFlag === true">
          <div class="modal-header">
            <h4 class="modal-title" id="exampleModalLabel">
              Caregiver Details
            </h4>
            <button
              type="button"
              class="close"
              data-dismiss="modal"
              aria-label="Close"
            >
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
          <div class="modal-body">
            <section>
              <div class="invoice-block">
                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">First Name </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control"
                      disabled
                      formControlName="cFNameDetail"
                    />
                  </div>
                </div>
                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">Last Name </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control"
                      disabled
                      formControlName="cLNameDetail"
                    />
                  </div>
                </div>
                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">Email </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control"
                      disabled
                      formControlName="cEmailDetail"
                    />
                  </div>
                </div>

                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">Cell Number </label>
                  </div>

                  <div class="col-md-2">
                    <input
                      type="text"
                      style="width: 86px;"
                      formControlName="cCountryCodeDetails"
                      disabled
                      class="form-control"
                    />
                  </div>
                  <div class="col-md-7">
                    <input
                      type="text"
                      class="form-control"
                      id="us-phone-mask-input"
                      disabled
                      formControlName="cCellNoDetail"
                    />
                  </div>
                </div>
                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">Source </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control"
                      disabled
                      formControlName="cSourceDetail"
                    />
                  </div>
                </div>
                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">Branch </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control"
                      disabled
                      formControlName="cBranchDetail"
                    />
                  </div>
                </div>
                <!-- <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">MRN </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control"
                      disabled
                      formControlName="cMRNDetail"
                    />
                  </div>
                </div> -->
                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">Created On </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control "
                      disabled
                      formControlName="cCreatedOnDetail"
                    />
                  </div>
                </div>
                <div class="form-group row">
                  <div class="col-md-3">
                    <label class="form-label">Last Login </label>
                  </div>
                  <div class="col-md-9">
                    <input
                      type="text"
                      class="form-control"
                      disabled
                      formControlName="cLastLoginDetail"
                    />
                  </div>
                </div>

                <div>
                  <div class="row patients-header">
                    <span>On Behalf Of</span>
                  </div>
                  <div
                    *ngFor="let associatedUser of onBehalfPatient"
                    class="chatrooms-patient-name"
                  >
                    <div class="form-group row ">
                      <label class="control-label col-md-3">Patient Name</label>
                      <div class="col-md-9">
                        <div class="form-group">
                          <input
                            class="form-control"
                            disabled
                            type="text"
                            value="{{ associatedUser.displayname }}"
                            formControlName="cpPatient"
                          />
                        </div>
                      </div>
                    </div>
                    
                    <div class="form-group chatrooms-patient-dob row">
                      <label class="control-label col-md-3"
                        >Patient Date Of Birth (mm/dd/yyyy)</label
                      >
                      <div class="col-md-9">
                        <div class="form-group">
                          <input
                            class="form-control"
                            value="{{ associatedUser.dob | date:'MM/dd/yyyy' }}"
                            disabled
                            formControlName="cpDob"
                            type="text"
                          />
                        </div>
                      </div>
                    </div>
                    <div class="form-group row">
                      <label class="control-label col-md-3" for="cpPatient">{{ 'LABELS.MRN' | translate }}</label>
                      <div class="col-md-9">
                        <div class="form-group">
                          <input class="form-control" disabled type="text" value="{{ associatedUser.mrn }}" formControlName="cpPatient" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-dismiss="modal">
            Close
          </button>
        </div>
      </div>
    </form>
  </div>
</div>

<!-- Detail popup ends -->
