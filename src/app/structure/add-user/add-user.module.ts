import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { FormsService } from './../forms/forms.service';
import { AddUserComponent } from './add-user.component';
import {FormsModule, ReactiveFormsModule} from '@angular/forms';
import { AddUserService } from './adduser.service';
import { SharedModule } from '../shared/sharedModule';
import { AuthGuard } from '../../guard/auth.guard';
import { SearchUserComponent } from './search-user.component';
import { AddAlternatecontactComponent } from './add-alternate-contact.component';
import { TextMaskModule } from 'angular2-text-mask';
import { NextgenUtilsModule } from 'app/nextgen-utils/nextgen-utils.module';

export const routes: Routes = [
  {
    path: 'add-user/add',
    component: AddUserComponent,
    canActivate: [AuthGuard],
    data: {
      expression: 'privileges.allowAddUser',
      checkUserGroupPermission: '3'
    }
  },
  {
    path: 'add-user/add/:id',
    component: AddUserComponent,
    data: {
      expression: 'privileges.allowAddUser',
      checkUserGroupPermission: '3'
    }
  },
  {
    path: 'add-user/search-user',
    component: SearchUserComponent,
    data: {
      expression: 'privileges.allowAddUser',
      checkRoutingPrivileges: 'allowAddUser,addVirtualPatientUsers'
    },
    canActivate: [AuthGuard]
  },
  {
    path: 'add-alternate-contact',
    component: AddAlternatecontactComponent,
    data: {
      expression: 'privileges.allowAddUser'
    }
  },
  {
    path: 'add-alternate-contact/:id',
    component: AddAlternatecontactComponent,
    data: {
      expression: 'privileges.allowAddUser'
    }
  }


];
@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    NextgenUtilsModule,
    TextMaskModule,
    RouterModule.forChild(routes)
  ],
  exports: [
  ],
  declarations: [
    AddUserComponent,
    SearchUserComponent,
    AddAlternatecontactComponent
  ],
  providers: [AddUserService]
})
export class AddUserModule { }
