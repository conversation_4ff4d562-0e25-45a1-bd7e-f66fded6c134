import { Component, OnInit, EventEmitter, Output } from '@angular/core';
import { StructureService } from '../../../structure/structure.service';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { SharedService } from '../../../structure/shared/sharedServices';
import { FormsService } from '.././../../structure/forms/forms.service';
import { GlobalDataShareService } from '.././../../structure/shared/global-data-share.service';
import { WorkListService } from '../../../structure/worklists/worklist.service';
import { Location } from '@angular/common';
let moment = require('moment/moment');
import { CommonVideoService } from "../../../../assets/lib/universal-video/common-video.service";
import { ISubscription } from "rxjs/Subscription";
declare var $: any;
declare var jQuery: any;
declare var notifyMe: any;
declare var serviceWorkerReg: any;
import { HostListener } from '@angular/core';
import { IntakeService } from '../intake.service';
@Component({
  selector: 'app-apps-menu',
  templateUrl: './apps-menu.component.html',
  styleUrls: ['./apps-menu.component.css']
})
export class AppsMenuComponent implements OnInit {
  childmessage: string = "I am passed from Parent to child component"
  privileges = [];
  userInfo;
  showData=[];
  appName;
  manageConfig;
  accountId;
  userRole;
  arr = [];
  resultDataContent;
  audio: any;
  previlages: any = {};
  inboxData;
  CSRMessages = [];
  supplyMenu: any;
  supplyMenuItems = [];
  supplyMenuItemsManagement = [];
  inboxCountLoading = true;
  inboxLoadingError = false;
  inboxUnreadMessageCount: number = 0;
  inboxUnreadFormCount: number = 0;;
  signatureCountLoading = true;
  signatureLoadingError = false;
  enableFilingCenter = true;
  showDashboard: Boolean = false;
  signatureRequest = [];
  userDataConfig;
  appNames: any;
  showNewSignature = true;
  nursingAgencyUser = false;
  menuSettings = {
    allowPatientDiscussionGroup: false
  };
  tenantId;
  ringTimmer: any;
  config;
  userDetails;
  configData;
  userData;
  crossTenantCommunicationEnabled: boolean = false;
  patientTopic = {
    count: localStorage.getItem('patientTopicCount')
  };
  videoChatData: any;
  crossTenantId;
  acceptedPush: boolean = false;
  crossTenantChangeSubscriber: any;
  userInboxCounts: any = {
    forms: {
      loading: true,
      error: false,
      totalCount: 0
    }
  }
  canNaUserManageEdm = true;
  canNaUserManageGroup = true;
  appVersion = this._sharedService.appVersion.split("-")[0];
  showWorklistAppMenu = '';
  toggleMenu;
  guid = '';
  worklistMenuPulse;
  dataLoadingMsg: boolean;
  @Output() valueChange = new EventEmitter();
  constructor(
    public _structureService: StructureService,
    private route: ActivatedRoute,
    private router: Router,
    private _sharedService: SharedService,
    // private _formsService: FormsService,
    private _formsService: FormsService,
    private location: Location,
    public _GlobalDataShareService: GlobalDataShareService,
    private _worklistService: WorkListService,
    public _intakeService: IntakeService
  ) {
    // this._formsService.getallMasterdata().then((result)=>{
    //   console.log(result);
    //      });
    console.log(this._intakeService.worklistMenuPulse);
    this.toggleMenu = false;
    this.dataLoadingMsg = true;
    console.log(this.showWorklistAppMenu);
    this.route.params.subscribe((params: Params) => {
      // console.log(params);
      if (params.guid) {
        //	this._structureService.displayProgress.emit(false);
        this.guid = params.guid;

        //this.getAppNames();
        // this.getCategores();
      }
    });
    console.log("const");


  }
  getAppNames() {
    if (this.guid){
    this._intakeService.getAllAppNames().then((data) => {
        this.appNames = data['apps'];
        //  console.log(this.appNames);
        // this.populateRoles('self');
    });
  }
}
  goToLastPage(defaultPage) {

    this.valueChange.emit(true);
    // window.history.go(-1);
    this._structureService.displayProgress.emit(true);
    localStorage.removeItem('appGuid');
    localStorage.removeItem('activeMenuIndex');
    localStorage.removeItem('showWorklistAppMenu');
    this._structureService.displayMenuId.emit('');
    this._structureService.appCenterGuid = '';
    this.router.navigateByUrl(defaultPage);

    this.router.navigateByUrl(defaultPage).then(() => {
      this.router.navigateByUrl(defaultPage);
      // window.location.href = defaultPage;
      console.log('naviate to any route which you want');
    });
    this._intakeService.blogStaffList = [];
  }
  editLiaison() {
    this._structureService.displayProgress.emit(false);
    this.router.navigateByUrl('laisan-edit-details');
  }
  worklistmenu(actionLink) {
    console.log(actionLink);

    this._structureService.appCenterGuid = localStorage.getItem('appGuid');
    //var res = actionLink.split('/');
    var url = 'apps/' + this._structureService.appCenterGuid + '/';
    var str = actionLink.replace('worklist/', url);
    this._structureService.displayProgress.emit(false);
    this.router.navigateByUrl(str);
  }
  menuClicked(index){
    localStorage.setItem('activeMenuIndex', index);
    localStorage.setItem('showWorklistAppMenu', index);
  }
  activeMenu(index){
    return localStorage.getItem('activeMenuIndex') == index;
  }
  @HostListener('click', ['$event'])
  onClick(event) {
    console.log(event.target.id);
    if (event.target.id != 'worklist-group' && event.target.id != 'worklist-menu') {
      this.toggleMenu = false;
    } else {
      $('.cat__menu-left__submenu--toggled .cat__menu-left__list').css('display', 'none');
      $('li').removeClass('cat__menu-left__submenu--toggled');
      if (event.target.id == 'worklist-group') {

        this.showWorklistAppMenu = event.target.innerText;
        localStorage.setItem('showWorklistAppMenu', this.showWorklistAppMenu);
        this.toggleMenu = !this.toggleMenu;
        let toggleMenu;
        toggleMenu = this.toggleMenu
        localStorage.setItem('toggleMenu', toggleMenu);

      }
    }
  }
  ngOnDestroy() {
  }
  ngOnInit() {
    //console.log(localStorage.getItem('appGuid'));
    this.appName = localStorage.getItem('appName');
    this._intakeService.menuAppName = this.appName;
    this._intakeService.breadcrumbAppName = this.appName;
    let guidVal = this.route.snapshot.paramMap.get('guid');
    this.guid = guidVal
      ? guidVal
      : localStorage.getItem('appGuid') ? localStorage.getItem('appGuid') : '';
    //console.log(guidVal);
    //console.log(this.guid)
    if (this.guid) {
      this._intakeService.getWorklistMenuPulse(this.guid, null).then((data) => {
        // console.log(data);
        this.worklistMenuPulse = data;
        this.dataLoadingMsg = false;


      });

    }

    this._structureService.displayMenuId.subscribe(
      (displayMenuId) => {

        this.showWorklistAppMenu = localStorage.getItem('showWorklistAppMenu');
        this.toggleMenu = true;





      });



    this.userData = this._structureService.getUserdata();
    /* this._structureService.socket.off("redirectToInbox").on("redirectToInbox", (data) => {
       var activeStrucuredFormData: any;;
       var formData1 = localStorage.getItem('formData');
       console.log("formData", formData);
       if (typeof (formData1) != "undefined" && formData1 != '') {
         activeStrucuredFormData = JSON.parse(decodeURIComponent(formData1));
       } else {
         if (typeof this._structureService.getCookie('formData') == 'object') {
           activeStrucuredFormData = this._structureService.getCookie('formData');
         } else {
           activeStrucuredFormData = JSON.parse(decodeURIComponent(this._structureService.getCookie('formData')));
         }
       }
 
       var patientId = "";
       var patient_name = "";
       if (activeStrucuredFormData.patientName) {
         var patientName = activeStrucuredFormData.patientName.trim();
       }
       if (activeStrucuredFormData.recipient_role_name == "Patient" && (patientName == "")) {
         patientId = activeStrucuredFormData.recipient_id;
       }
       else if (activeStrucuredFormData.patientName) {
         patient_name = activeStrucuredFormData.patientName;
 
       } else {
         patientId = this.userData.userId;
       }
       if (activeStrucuredFormData.page) {
         var page = activeStrucuredFormData.page;
 
       }
       var formData = {
         "userid": this.userData.userId,
         "tenantId": data.formData.tenant_id,
         "recipient_id": data.formData.recipient_id,
         "formId": data.formData.form_id,
         "staffFacing": data.formData.staffFacing,
         "form_submission_id": data.formData.form_submission_id,
         "form_id": data.formData.form_id,
         "form_name": data.formData.formName,
         "patient_id": "",
         "id": "",
         "patient_name": patient_name,
         "patientUser": patientId
       }
       console.log(formData);
       if (data.formData.form_submission_id) {
         this._formsService.filingCenterSubmittedFormdata(formData);
       }
       //console.log(data, this.router.url,this.router.url.indexOf("/forms/edit-form/"));
       if (this.router.url.indexOf('/forms/edit-form/') != -1 && data.formData.fromMob == 'false') {
         var notify = $.notify('Thank you for completing the ' + data.formData.formName);
         setTimeout(function () {
           notify.update({ 'type': 'success', 'message': '<strong>Thank you for completing the ' + data.formData.formName + '</strong>' });
         }, 1000);
         if ((data.formData.page == "formWorlists") || (page == "formWorlists")) {
           this.router.navigate(['/forms/list']);
         } else if ((data.formData.page == "myFormWorlists") || (page == "myFormWorlists")) {
           this.router.navigate(['/forms/worklist']);
         } else if (this._sharedService.FormsPage) {
           this.router.navigate([this._sharedService.FormsPage]);
         } else {
           this.router.navigate(['/inbox']);
         }
       }
     });
     this._structureService.socket.off("activityTrack").on("activityTrack", (data) => {
       console.log(this._sharedService.formsType);
       console.log(this._sharedService.myFormsType);
       var displayname = this.userData.displayName;
       setTimeout(() => {
         if (((this.router.url.indexOf('/forms/view-form') != -1) || (this.router.url.indexOf('/forms/view-form') != -1)) && data.formData.fromMob == 'false') {
           var notify = $.notify('Thank you for completing the ' + data.formData.formName);
           setTimeout(function () {
             notify.update({ 'type': 'success', 'message': '<strong>Thank you for completing the ' + data.formData.formName + '</strong>' });
           }, 1000);
           var activityData = {
             activityName: data.formData.action == 'edit' ? "Edit Form" : "Submit Form",
             activityType: "forms",
             activityDescription: ''
           };
           if (data.formData.staffFacing == 'true') {
             activityData.activityDescription = displayname + ' has ' + (data.formData.action == "edit" ? 'updated' : 'completed') + ' staff facing form ' + data.formData.formName + ' (' + data.formData.form_id + ')';
           } else {
             activityData.activityDescription = displayname + ' has ' + (data.formData.action == "edit" ? 'updated ' : 'completed ') + data.formData.formName + ' (' + data.formData.form_id + ')' + ' sent by ' + data.formData.toName;
           }
           //Add configuration in form type Default Outgoing Filing Center (From Citus Health) after Form Submit.
           var formData = {
             "userid": this.userData.userId,
             "tenantId": data.formData.tenant_id,
             "recipient_id": data.formData.recipient_id,
             "formId": data.formData.form_id,
             "staffFacing": data.formData.staffFacing,
             "form_submission_id": data.formData.submissionId
           }
           console.log(formData);
           this._formsService.filingCenterSubmittedFormdata(formData);
           this._structureService.trackActivity(activityData);
           if (this._sharedService.FormsPage) {
             this.router.navigate([this._sharedService.FormsPage]);
           } else if (data.formData.staffFacing != 'true') {
             this.router.navigate(['/inbox']);
           }
         }
       }, 100);
     });
     */



    $(function () {

      // scripts for "menu-left" module

      /////////////////////////////////////////////////////////////////////////////////////////
      // add backdrop

      $('.cat__menu-left').after('<div class="cat__menu-left__backdrop cat__menu-left__action--backdrop-toggle"><!-- --></div>');

      /////////////////////////////////////////////////////////////////////////////////////////
      // submenu

      $('.cat__menu-left__submenu > a').off('click').on('click', function () {

        if ($('body').hasClass('cat__config--vertical') || $('body').width() < 768) {

          var parent = $(this).parent(),
            opened = $('.cat__menu-left__submenu--toggled');

          if (!parent.hasClass('cat__menu-left__submenu--toggled') && !parent.parent().closest('.cat__menu-left__submenu').length)
            opened.removeClass('cat__menu-left__submenu--toggled').find('> .cat__menu-left__list').slideUp(200);

          parent.toggleClass('cat__menu-left__submenu--toggled');
          parent.find('> .cat__menu-left__list').slideToggle(200);

        }

      });

      // remove submenu toggle class when viewport back to full view
      $(window).on('resize', function () {
        if ($('body').hasClass('cat__config--horizontal') || $('body').width() > 768) {
          $('.cat__menu-left__submenu--toggled').removeClass('cat__menu-left__submenu--toggled').find('> .cat__menu-left__list').attr('style', '');
        }
      });

      /////////////////////////////////////////////////////////////////////////////////////////
      // custom scroll init

      if ($('body').hasClass('cat__config--vertical')) {
        if (!(/Mobi/.test(navigator.userAgent)) && jQuery().jScrollPane) {
          $('.cat__menu-left__inner').each(function () {
            $(this).jScrollPane({
              contentWidth: '0px',
              autoReinitialise: true,
              autoReinitialiseDelay: 100
            });
            var api = $(this).data('jsp'),
              throttleTimeout;
            $(window).bind('resize', function () {
              if (!throttleTimeout) {
                throttleTimeout = setTimeout(function () {
                  api.reinitialise();
                  throttleTimeout = null;
                }, 50);
              }
            });
          });
        }
      }
      /////////////////////////////////////////////////////////////////////////////////////////
      // toggle menu

      $('.cat__menu-left__action--menu-toggle').on('click', function () {
        if ($('body').width() < 768) {
          $('body').toggleClass('cat__menu-left--visible--mobile');
        } else {
          $('body').toggleClass('cat__menu-left--visible');
          if (!$('body').hasClass('cat__menu-left--visible')) {
            $('body .cat__menu-left').addClass("toggleMenuClick");
          }
        }
      })
      $('body .cat__menu-left').on('mouseenter', function () {
        $('body .cat__menu-left').removeClass("toggleMenuClick");
      })

      $('.cat__menu-left__action--backdrop-toggle').on('click', function () {
        $('body').removeClass('cat__menu-left--visible--mobile');
      })


      /////////////////////////////////////////////////////////////////////////////////////////
      // colorful menu

      var colorfulClasses = 'cat__menu-left--colorful--primary cat__menu-left--colorful--pulse cat__menu-left--colorful--secondary cat__menu-left--colorful--primary cat__menu-left--colorful--pulse cat__menu-left--colorful--default cat__menu-left--colorful--info cat__menu-left--colorful--success cat__menu-left--colorful--warning cat__menu-left--colorful--danger cat__menu-left--colorful--yellow',
        colorfulClassesArray = colorfulClasses.split(' ');

      function setColorfulClasses() {
        $('.cat__menu-left__list--root > .cat__menu-left__item').each(function () {
          var randomClass = colorfulClassesArray[Math.floor(Math.random() * colorfulClassesArray.length)];
          $(this).addClass(randomClass);
        })
      }

      function removeColorfulClasses() {
        $('.cat__menu-left__list--root > .cat__menu-left__item').removeClass(colorfulClasses);
      }

      if ($('body').hasClass('cat__menu-left--colorful')) {
        setColorfulClasses();
      }

      $('body').on('setColorfulClasses', function () {
        setColorfulClasses();
      });

      $('body').on('removeColorfulClasses', function () {
        removeColorfulClasses();
      });


    });
  }
  loadPulseData(){
    let data=this;
    $('#exampleModalCenter').on('show.bs.modal', function(event) {
     
  
      data._intakeService.getallMasterdata().then((result)=>{
        const dataJson= JSON.parse(result['_body']);
        console.log(result['_body']);
        console.log(dataJson);
        console.log(dataJson.items);
  data.resultDataContent =dataJson.items;
  console.log(data.resultDataContent);
  // Object.keys(data.resultDataContent).forEach((element)=>{
  //   let masterData = [];
  //   Object.keys(data.resultDataContent[element]).forEach((elem)=>{
  //     let synchData = {key: elem, value: data.resultDataContent[element][elem]};
  //     masterData.push(synchData);
  //   });
  //   let dataMaster = {key: element, value: masterData };
  //   data.showData.push(dataMaster);
  //   console.log('showData',this.showData);
  //   });
  Object.keys(data.resultDataContent).forEach((element)=>{


    let v = {key: element, value: data.resultDataContent[element]};
    
    data.showData.push(v);
    console.log('showData',data.showData);
   
    });
    // data.showData=[];
      });
    });
    $('#exampleModalCenter').modal(
      {
        backdrop: 'static', 
        keyboard: false
      }
    );
  }
  loadloggedInblock(){
    $( "#whologgedinid" ).trigger( "click" );
  
    this.loadPulseData();
  }
}
