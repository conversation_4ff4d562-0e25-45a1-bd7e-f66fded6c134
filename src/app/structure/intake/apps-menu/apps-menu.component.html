
<style>
    .cat__menu-left__item--hidden {
        display: none !important;
    }

    .cat__menu-left__item--disabled {
        cursor: not-allowed !important;
    }

    .cat__menu-left__item--disabled>a {
        pointer-events: none !important;
    }

    .toggleMenuClick {
        -webkit-transform: translate3d(-14.29rem, 0, 0);
        transform: translate3d(-14.29rem, 0, 0);
    }
</style>
<nav class="cat__menu-left">
    <div class="cat__menu-left__lock cat__menu-left__action--menu-toggle">
        <div class="cat__menu-left__pin-button">
            <div>

        </div>
    </div>
</div>
<div class="cat__menu-left__logo">
    <!-- <img src="./assets/modules/dummy-assets/common/img/pulse_logo.png" /> -->
    <!-- <img src="https://assets.citushealth.com/a/0/img/account-logo-on-white-bg.png" /> -->
    <img src="https://assets.citushealth.com/a/{{userData.crossTenantId}}/img/account-logo-on-white-bg.png"
                onerror="this.src='assets/modules/dummy-assets/common/img/account-logo-on-white-bg.png'" />

</div>
<div class="cat__menu-left__inner">
    <ul class="cat__menu-left__list cat__menu-left__list--root">

        <li class="cat__menu-left__item cat__menu-left__submenu">
            <a class="inbox-collapse-count" href="javascript: void(0);" id="pulse_center">
                <span class="cat__menu-left__icon"><i style="font-size:24px" class="fa">&#xf21e;</i></span>
                {{_intakeService.menuAppName}}

            </a>

                <ul class="cat__menu-left__list">
                    <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']"
                        (click)="goToLastPage(userData.defaultPage)">
                        <a id="swich-citus">
                            <span class="cat__menu-left__icon"><i style="font-size:24px" class="fa">&#xf079;</i>
                            </span> Switch To Citus App

                        </a>
                    </li>

                </ul>
            </li>
            <li class="cat__menu-left__item cat__menu-left__submenu"
                [ngClass]="{'cat__menu-left__submenu--toggled' : showWorklistAppMenu === menuGroup.name && toggleMenu == true}"
                [hidden]="menuGroup.worklists.length == 0"
                *ngFor="let menuGroup of _intakeService.worklistMenuPulse; let i = index" [attr.data-index]="i">
                <a href="javascript: void(0);" id="worklist-group" (click)="menuClicked(menuGroup.name)">
                    <span class="cat__menu-left__icon"><i class="fa fa-user-plus"></i></span> {{menuGroup.name}}
                </a>
                <ul class="cat__menu-left__list"
                    [ngClass]="{'show-worklist': showWorklistAppMenu === menuGroup.name && toggleMenu == true}">
                    <li *ngFor="let menu of menuGroup.worklists; let i = index" class="cat__menu-left__item"
                        [routerLinkActive]="['cat__menu-left__item--active']">
                        <a [routerLink]="[menu.actionLink]" id="worklist-menu">
                            <span class="cat__menu-left__icon"><i class="{{menu.menuIcon}}"></i></span>
                            {{menu.menuTitle}}
                        </a>
                    </li>
                    <li class="cat__menu-left__item" *ngIf="userData.config.enable_refill_dashboard && userData.config.enable_refill_dashboard === '1'
                    && _intakeService.menuAppName.toLowerCase().includes('refill dashboard')" 
                    [routerLinkActive]="['cat__menu-left__item--active']">
                        <a id="worklist-menu" [routerLink]="['configuration']"> 
                            <span class="cat__menu-left__icon"><i class="fa fa-cogs"></i></span> {{'MENU.CONFIGURATION' | translate}}
                        </a>
                      </li>
                </ul>
            </li>
            <!-- <li class="cat__menu-left__divider"></li> -->

         <!-- <li class="cat__menu-left__item cat__menu-left__submenu">
            <a class="inbox-collapse-count" href="javascript: void(0);" id="pulse_center">
                <span class="cat__menu-left__icon fa fa-list-alt">
                   
                </span>
                Settings

            </a>

                <ul class="cat__menu-left__list">
                    <li class="cat__menu-left__item" [routerLinkActive]="['cat__menu-left__item--active']"
                        (click)="loadloggedInblock()">
                        <a id="swich-citus">
                                <span class="cat__menu-left__icon">
                                    <i class="fa fa-cogs" aria-hidden="true"></i>

                            </span>Sync Master Data

                        </a>
                    </li>

                </ul>
            </li>  -->
        </ul>
    </div>
</nav>
<!-- 
 <div class="modal fade" id="exampleModalCenter" 
tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
          <div class="modal-content">
          
            <div class="modal-header">
                <div class="master-details-header">
              <h1 class="modal-title" id="exampleModalCenterTitle">Sync Master Data</h1>
              </div>
              <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">&times;</span>
              </button>
            </div>
            <div class="modal-body">
              <div class="row">
               
                    <div class="objData" *ngFor="let obj of showData">
                            <span>{{obj.key}}: </span>
                            <span>{{obj.value['insert']}} New entry,</span>
                            <span>{{obj.value['update']}} Update</span>
                            </div>
             </div>
             
            </div>
            <div class="modal-footer-custom">
              <div class="row">
                 <div class="modal-footer">
                    <button type="button" class="pull-right btn btn-default" 
                    data-dismiss="modal">Close</button>
                  </div>
                
              </div>
            </div>
          </div>
        </div>
      </div> -->