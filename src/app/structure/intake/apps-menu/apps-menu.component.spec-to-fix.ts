import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Apollo, ApolloModule } from 'apollo-angular';
import ApolloClient, { createNetworkInterface } from 'apollo-client';
import { FormsService } from 'app/structure/forms/forms.service';
import { AuthService } from 'app/structure/shared/auth.service';
import { GlobalDataShareService } from 'app/structure/shared/global-data-share.service';
import { SharedService } from 'app/structure/shared/sharedServices';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { WorkListService } from 'app/structure/worklists/worklist.service';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { provideClients } from 'test-utils';
import { IntakeService } from '../intake.service';

import { AppsMenuComponent } from './apps-menu.component';

describe('AppsmenuComponent', () => {
  let component: AppsMenuComponent;
  let fixture: ComponentFixture<AppsMenuComponent>;
  let formservice: FormsService;
  let workservice: WorkListService;
  beforeEach(async(() => {
    TestBed.configureTestingModule({
      providers: [
        StructureService,
        Apollo,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        TranslateService,
        { provide: FormsService, useValue: formservice },
        GlobalDataShareService,
        { provide: WorkListService, useValue: workservice },
        IntakeService,
      ],
      declarations: [AppsMenuComponent],
      imports: [
        HttpModule,
        RouterTestingModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF,
        }),
        TranslateModule.forRoot(),
      ],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AppsMenuComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
});
