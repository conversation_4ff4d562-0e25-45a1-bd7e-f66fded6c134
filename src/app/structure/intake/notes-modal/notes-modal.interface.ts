export interface IntegrationParams {
    integrationMode?: string,
    fromEmail? : string,
    fromName? : string,
    type? : string,
    filePath?: string,
    integrationType?: string,
    subject?: string,
    body?: string,
    patientId: string,
    callBackURL?: string,
    workListType: string,
    progressNoteType?: string,
    appName: string,
    entityType?: string,
    to? : string,
    resourceURL? : string,
    partnerToken? : string,
    staffId: string,
}