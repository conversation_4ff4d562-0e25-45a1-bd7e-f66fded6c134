textarea, textarea:focus, textarea:active{
  padding: 10px;
  border: 1px solid #ccc;
  outline: none;
  margin: 10px 0!important;
} 
.patient-data{
  font-size: 13px;
  font-weight: 700;
}
.btn.note-close-btn{
  padding: 3px 30px !important;
  background-color: #343434;
}
.btn.note-save-btn{
  padding: 3px 15px !important;
  background-color: #52b3aa;
}
.modal-footer{
  padding: 15px 33px 15px 15px;
}

.red-border-class {
  border: 1px solid red;
}
.no-border-class {
  border: 1px solid #d2d9e5;
}
.swal-wide {
  width: 850px !important;
}
.therapy-check-input {
    padding-left: 17px;
}
.pointer {
    cursor: pointer;
}
.col-md-3 {
    width: 20%;
}
.pull-right {
    margin: 2px;
} 
.headBox {
  border-bottom: solid 1px #dad8d8;
  margin-top: -16px;
  background: #dad8d8;
}
.headBoxBg {
  margin-top: 5px;
  border-bottom: 1px solid #dad8d8;
}
.swal-wide{
  border: 1px solid #d2d9e5;
}
.errorMsg{
  color: white;
  background-color: red;
  display: table-cell;
  padding: 2px 5px;
}
.patient-details-header{
  margin: 0;
  padding: 10px;
  width: 100%;
  background-color: #eceeef;
}
.patient-header{
margin: 0;
background-color: #eceeef;
width: 100%;
padding: 10px;
/* padding: 10px 0; */
}
.notes-modal-content span.dropdown-up, .notes-modal-content span.dropdown-down {
position: absolute;
right: 10px;
/*border-bottom: 5px solid #d2d9e5 !important;
border-left: 5px solid transparent !important;
border-right: 5px solid transparent !important;
top: 15px;
font-size: 10px !important;*/
}
.multiselect-dropdown .dropdown-btn .dropdown-down {
  display: inline-block;
  top: 18px !important;
  width: 0;
  height: 0;
  border-top: 5px solid #d2d9e5 !important;
  border-left: 4px solid transparent !important;
  border-right: 4px solid transparent !important;
}
.notes-modal-content span.dropdown-btn {
border: 1px solid #e4e9f0 !important;
min-height: 40px !important;
}
.divDisabled {
pointer-events: none;
opacity: 0.6;
}
.divDisabled:hover {
cursor: not-allowed;
}
.selecttxtTherapy {
  width: 135px;
}
.selecttxtMoa {
  width: 135px;
  margin-left: 10px;
}
.textDrug {
  /* margin-left: 20px; */
}
.notes-modal-content .form-group {
display: inline-block;
margin-bottom: 10px;
vertical-align: middle;
width: 50%;
}
.sendemail{
width: 30% !important;
}
/*.sendemail-dropdown{
width: 70% !important;
}*/
.sendemail-checkbox{
margin-left: 15px;
display: inline-block;
}
.blognote-section{
width: 100% !important;
}
.blognotesubject-section{
padding-right: 15px;
}

.sendemail-dropdown .dropdown-down{
border: 0 !important;
}
.sendemail-dropdown .dropdown-btn{
background: #fff center right no-repeat url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABYAAAAFCAYAAABB9hwOAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA25pVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuNi1jMDY3IDc5LjE1Nzc0NywgMjAxNS8wMy8zMC0yMzo0MDo0MiAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDpiNWZkMzNlMC0zNTcxLTI4NDgtYjA3NC01ZTRhN2RjMWVmNjEiIHhtcE1NOkRvY3VtZW50SUQ9InhtcC5kaWQ6ODZDNDdFRTkxRTJBMTFFNjg0MUM5MTMwMjYwRDYwRDkiIHhtcE1NOkluc3RhbmNlSUQ9InhtcC5paWQ6ODZDNDdFRTgxRTJBMTFFNjg0MUM5MTMwMjYwRDYwRDkiIHhtcDpDcmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENDIDIwMTUgKFdpbmRvd3MpIj4gPHhtcE1NOkRlcml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6RTUxRUI3MDZEQjk4MTFFNUI1NDA5QTcyNTlFQzRERTYiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6RTUxRUI3MDdEQjk4MTFFNUI1NDA5QTcyNTlFQzRERTYiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz69wtu7AAAAe0lEQVR42mLce+zSOVFhYUMGNHDv4cOd/q6WHgxkAqbvP77H/P339zey4Nfv3z7ceXA/hoECwCQnLXPtw8eP05EFHz15WuRm7/CGIoNBhLCgUPnPX79egdgv37w+qKmqOp+BQsAEpX8wMTFm/fnz5/P/f//DGagAAAIMAKIuMR+q/rU9AAAAAElFTkSuQmCC) !important;
width: 100% !important;
border-color: #e4e9f0 !important;
height: 40px !important;
line-height: 24px !important;
font-size: 13px !important;
}
.blognots-modal .btn{
border: 0 !important;
}
.cpr-dropdown.cpr-subjectdropdown {
width: 50%;
}
.cpr-subject-label {
width: 12%;
float: left;
}
.cpr-area {
width: 38%;
float: left;
}
.cpr-subject-label, .cpr-area {
padding-top: 10px;
}
.filedropContainer {
  width: 670px;
  height: 202px;
  padding: 0rem;
  text-align: center;
  border: dashed 1px #979797;
  position: relative;
  margin: 1.5rem auto;
}
.fileInput {
    opacity: 0;
    position: absolute;
    z-index: 2;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
  }

  .fileLabel {
    color: white;
    width: 183px;
    height: 44px;
    border-radius: 21.5px;
    background-color: #0190fe;
    padding: 8px 16px;
  }

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #38424c;
  }

.files-list {
  width: 670px;
  position: relative;
  margin: 1.5rem auto;
}
  .single-file {
    display: flex;
    padding: 0.5rem;
    justify-content: space-between;
    align-items: center;
    border: dashed 1px #979797;
    margin-bottom: 1rem;
  }
    .deleteFile {
      display: flex;
      margin-left: 0.5rem;
      margin-bottom: 1.5rem;
      cursor: pointer;
      align-self: flex-end;
    }

    .name {
      font-size: 14px;
      font-weight: 500;
      color: #353f4a;
      margin: 0;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .size {
      font-size: 12px;
      font-weight: 500;
      color: #a4a4a4;
      margin: 0;
      margin-bottom: 0.25rem;
    }

    .info {
      width: 100%
    }
    .leftbox { 
      float:left;  
      width:30%; 
  } 
  .middlebox { 
      float:left;  
      width:35%; 
      margin: 5px;
  } 
  .middlebox select {
    white-space: pre-wrap;
  }
  .rightbox { 
      float:right; 
      width:35%; 
  } 
  .fa-3x {
    color: #514d6a;
  }
  .therapy-text{
    word-break: break-word;
  }

/* Shake animation */
@keyframes shake {
  0% {
    transform: translate(1px, 1px) rotate(0deg);
  }

  10% {
    transform: translate(-1px, -2px) rotate(-1deg);
  }

  20% {
    transform: translate(-3px, 0px) rotate(1deg);
  }

  30% {
    transform: translate(3px, 2px) rotate(0deg);
  }

  40% {
    transform: translate(1px, -1px) rotate(1deg);
  }

  50% {
    transform: translate(-1px, 2px) rotate(-1deg);
  }

  60% {
    transform: translate(-3px, 1px) rotate(0deg);
  }

  70% {
    transform: translate(3px, 1px) rotate(-1deg);
  }

  80% {
    transform: translate(-1px, -1px) rotate(1deg);
  }

  90% {
    transform: translate(1px, 2px) rotate(0deg);
  }

  100% {
    transform: translate(1px, -2px) rotate(-1deg);
  }

  
}
.therapyText{
  font-size: 1.25rem;
  font-weight: 700;
  color: #0a81e8;
}

.hide {
  visibility: none!important;
}
.popover {
  width:350px;
  max-width:1500px;
  background-color: #fff !important;
}
.popover-content h4 .newText{
  font-weight: normal!important;
  font-size: 15px!important;
  color: #00A1FF;
}
.popover-content h4 small {
  color: black;
}
.popover-content button.btn-primary {
  color: #00A1FF;
  border-color:#00A1FF;
  background:white;
}

.popover-content button.btn-default {
  color: gray;
  border-color:gray;
}

.note-textarea {
  width:100%; 
  height:200px; 
  border-radius:8px;
}

.blog-textarea {
  width:100%; 
  height:200px; 
  border-radius:8px; 
}

.therapy-label {
  color: #0a81e8;
}

.edoc-delete{
  font-size:25px;
  color:grey
}
