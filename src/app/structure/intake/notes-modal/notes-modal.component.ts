import { Component, OnInit, Input, Output, EventEmitter, SimpleChange } from '@angular/core';
import { FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms';
import { IntakeService } from '../intake.service';
import { WorkListService } from '../../worklists/worklist.service';
import { SignService } from '../../signatures/sign.service';
import { StructureService } from 'app/structure/structure.service';
import { ConstantService } from '../constant.service';
import { UploadOutput, UploadInput, UploadFile, humanizeBytes } from 'ngx-uploader';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { isBlank } from 'app/utils/utils';
import { IntegrationParams } from './notes-modal.interface';
declare var $: any;
import * as $ from 'jquery';
declare var swal: any;
var momentTz = require('moment-timezone');
var jstz = require('jstz');
const timezone = jstz.determine();
@Component({
  selector: 'app-notes-modal',
  templateUrl: './notes-modal.component.html',
  styleUrls: ['./notes-modal.component.css']
})
export class NotesModalComponent implements OnInit {
  @Input('dynamicData') dynamicData: any;
  @Input('fromDashboard') fromDashboard: any;
  @Input('integrationSettings') integrationSettings: any;
  @Input() patientId: any;
  @Input() childWorklistId: any;
  @Input() noteType: string;
  @Input() worklistEditData: any;
  @Input() rowData: any;
  @Input() parentWorklistName: any;
  @Input() worklistMenuName: any;
  @Input() noteCatId: number;
  @Output() reloadGrid = new EventEmitter();
  @Output() therapyStatus = new EventEmitter();
  @Output() closeModal = new EventEmitter();
  @Output() updateBlogList = new EventEmitter();
  @Input('workflowId') workflowId: any;
  note = '';
  userData = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
  therapyDetails = [];
  loadMsg = true;
  showField = false;
  showFormField = true;
  therapyForm: FormGroup;
  editorDynamicFields = [];
  tenantRoles: any;
  notifyStatus;
  editId;
  editorFields = [];
  reportFields: any;
  worklistDetails = [];
  buttonLabel = 'Add';
  therapyCount = 10;
  editTherapyId = '';
  therapyList = [];
  moaList = [];
  therapyName;
  moaName;
  editSubmit = true;
  modifyTherapyList = false;
  noteSubmit = false;
  deleteIds = [];
  submitted = false;
  edocSubmitted = false;
  therapyComplete = false;
  therapyLen = false;
  blogForm;
  noteForm;
  edocForm;
  blogSubjects = [];
  progressNoteSubject = [];
  sendEmailChk = false;
  progressNoteChk = false;
  progressNoteText: any;
  showSubjectError = false;
  showPnSubjectError = false;
  noteError = false;
  showRecheckOption = false;
  therapyReason;
  therapyReasonId;
  therapyCompleteStatus;
  therapyRecheckStatus;
  therapyRecheckReasonValue;
  recheckReason;
  dropdownList = [];
  staffUserList = [];
  selectedItems = [];
  dropdownSettings = {};
  therapyRecheckRequired;
  isDisabled;
  therapyRecheckCompleted;
  cmisFileUploadData = [];
  filesAttached = {
    pdf: 0,
    document: 0,
    image: 0
  };
  formData: FormData;
  files: UploadFile[];
  uploadInput: EventEmitter<UploadInput>;
  humanizeBytes: Function;
  dragOver: boolean;
  uploadFlag: boolean;
  selectedFileNames = [];
  descriptionId;
  descriptionData;
  textDescription;
  descriptionList = [];
  workflowType;
  integrationStatus: any;
  /**For send notification */
  notificationData: any = {};
  allMessageTemplate = [];
  appName: string;
  blogNoteContent = '';
  dropdownSettingsBlogSubject = {};
  dropdownSettingsProgressSubject = {};
  dbFour = false;
  loadSpinner = false;
  selectedBlogSubjects = [];
  blogSubjectData = false;
  pnSubjectData = false;
  staffData = false;
  notePlaceholder = '';
  integrationParams : IntegrationParams;
  constructor(
    private _intakeService: IntakeService,
    private _workListService: WorkListService,
    private formBuilder: FormBuilder,
    private _signService: SignService,
    private _structureService: StructureService,
    private _constantService: ConstantService,
    private toolTipService: ToolTipService
  ) {
    this.files = []; // local uploading files array
    this.uploadInput = new EventEmitter<UploadInput>(); // input events, we use this to emit data to ngx-uploader
    this.humanizeBytes = humanizeBytes;
    this.blogForm = this.formBuilder.group({
      blogNoteSubject: [''],
      sendEmail: [''],
      progressNote: [false],
      progressNoteSubject: [''],
      blogNote: [''],
      emailRecipients: [''],
    });
    this.noteForm = this.formBuilder.group({
      noteContent:['']
    });
    this.edocForm = this.formBuilder.group({
      descriptionListId: ['', Validators.required],
      edocText: ['']
    });
    this.therapyForm = this.formBuilder.group({
      therapy: ['', [Validators.required]],
      moa: ['', [Validators.required]],
      drug: ['', [Validators.required]]
    });
  }
  ngOnInit() {
    $(function () {
      $('.dropify').dropify({
        messages: {
          'default': 'Drag and drop a file here or click,<br>multiple file upload enabled.',
          'replace': 'Drag and drop or click to add',
          'remove': 'Remove',
          'error': 'Ooops, something wrong happend.'
        },
        error: {
          'fileSize': 'The file size is too big ({{ value }} max).',
          'minWidth': 'The image width is too small ({{ value }}}px min).',
          'maxWidth': 'The image width is too big ({{ value }}}px max).',
          'minHeight': 'The image height is too small ({{ value }}}px min).',
          'maxHeight': 'The image height is too big ({{ value }}px max).',
          'imageFormat': 'The image format is not allowed ({{ value }} only).'
        }
      });
    });
    if (this.parentWorklistName == 'intake-followup') {
      this.workflowType = this._constantService.entryTypes['INTAKE'];
    } else if (this.parentWorklistName == 'liaisons') {
      this.workflowType = this._constantService.entryTypes['LIAISON'];
    } else if (this.parentWorklistName == 'initial-insurance-verification') {
      this.workflowType = this._constantService.entryTypes['INITIAL_INS'];
      this.editSubmit = true;
    } else if (this.parentWorklistName == 'insurance-verification-follow-up') {
      this.workflowType = this._constantService.entryTypes['INS_VERIFICATION'];
      this.editSubmit = true;
      this.dbFour = true;
    } else if (this.parentWorklistName == 'pharmacy-follow-up') {
      this.workflowType = this._constantService.entryTypes['PHARMACY_FOLLOWUP'];
      this.editSubmit = true;
      this.dbFour = true;
    }
    this.userData = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
    if (this.noteType == 'eDoc') {
      if (this._intakeService.edocList.length > 0) {
        this.descriptionList = this._intakeService.edocList;
      } else {
        this._intakeService.getDocList().then((data) => {
          this.descriptionList = data['getDocList'];
          this._intakeService.edocList = data['getDocList'];
        });
      }
    }
    if (this.noteType == 'Therapy') {
      if (this._intakeService.therapyList.length > 0) {
        this.therapyList = this._intakeService.therapyList;
      } else {
        this._intakeService.getTherapies().then((data) => {
          this.therapyList = data['getTherapies'];
          this._intakeService.therapyList = data['getTherapies'];
        });
      }
      if (this._intakeService.moaList.length > 0) {
        this.moaList = this._intakeService.moaList;
      } else {
        this._intakeService.getMoas().then((data) => {
          this.moaList = data['getMoas'];
          this._intakeService.moaList = data['getMoas'];
        });
      }
      if (this._intakeService.recheckReasons.length > 0) {
        this.recheckReason = this._intakeService.recheckReasons;
      } else {
        this._intakeService.getRecheckReasons().then((data) => {
          this.recheckReason = data['getRecheckReason'];
          this._intakeService.recheckReasons = data['getRecheckReason'];
        });
      }
      if (this.worklistEditData.therapyRecheckRequired == true) {
        this.therapyRecheckRequired = true;
      } else {
        this.therapyRecheckRequired = false;
      }
      if (this.worklistEditData.therapyStatus == 1) {
        this.therapyCompleteStatus = true;
        this.showFormField = false;
        this.isDisabled = true;
        this.therapyComplete = true;
      } else {
        this.isDisabled = false;
        this.therapyCompleteStatus = false;
        this.therapyComplete = false;
      }
      if (this.worklistEditData.therapyRecheck == true && this.worklistEditData.therapyRecheckReason != null && this.worklistEditData.therapyRecheckReasonLovId != 0) {
        this.therapyRecheckStatus = true;
        this.therapyRecheckCompleted = true;
        this.therapyRecheckReasonValue = this.worklistEditData.therapyRecheckReasonLovId;
        this.therapyReasonId = this.worklistEditData.therapyRecheckReasonLovId;
        this.therapyReason = this.worklistEditData.therapyRecheckReason;
      } else {
        this.therapyRecheckStatus = false;
        this.therapyRecheckCompleted = false;
      }
      if (!this.therapyCompleteStatus && this.therapyRecheckRequired && !this.therapyRecheckStatus) {
        this.showFormField = false;
      }
      this.createControls();
    }
    if (this.noteType == 'Blog') {
      if (this._intakeService.blogNoteSubject && this._intakeService.blogNoteSubject.length > 0) {
        this.blogSubjects = this._intakeService.blogNoteSubject;
        this.blogSubjectData = true;
        this.dropdownSettingsBlogSubject = {
          lazyLoading: true,
          singleSelection: true,
          idField: 'id',
          textField: 'name',
          enableSearchFilter: true,
          allowSearchFilter: true,
          noDataAvailablePlaceholderText: '',
          closeDropDownOnSelection: true
        };
      } else {
        this._intakeService.blogSubjects().then((data) => {
          this.blogSubjects = data['blogSubjects'];
          this._intakeService.blogNoteSubject = data['blogSubjects'];
          this.blogSubjectData = true;
          this.dropdownSettingsBlogSubject = {
            lazyLoading: true,
            singleSelection: true,
            idField: 'id',
            textField: 'name',
            enableSearchFilter: true,
            allowSearchFilter: true,
            noDataAvailablePlaceholderText: '',
            closeDropDownOnSelection: true
          };
        });
      }
      if (this._intakeService.pnSubjects && this._intakeService.pnSubjects.length > 0) {
        this.progressNoteSubject = this._intakeService.pnSubjects;
        this.pnSubjectData = true;
        this.dropdownSettingsProgressSubject = {
          lazyLoading: true,
          singleSelection: true,
          idField: 'id',
          textField: 'subject',
          enableSearchFilter: true,
          allowSearchFilter: true,
        }
      } else {
        this._intakeService.progressNoteSubject().then((data) => {
          this.progressNoteSubject = data['progressNoteSubject'];
          this._intakeService.pnSubjects = data['progressNoteSubject'];
          this.pnSubjectData = true;
          this.dropdownSettingsProgressSubject = {
            lazyLoading: true,
            singleSelection: true,
            idField: 'id',
            textField: 'subject',
            enableSearchFilter: true,
            allowSearchFilter: true,
          };
        });
      }
      this.notifyStatus = this.dynamicData.filterLiaisonsDetails[0].enableNotification;
      if (this.notifyStatus == true) {
        if (this._intakeService.blogStaffList && this._intakeService.blogStaffList.length > 0) {
          this.dropdownList = this._intakeService.blogStaffList ;
          this.staffUserList = this._intakeService.blogStaffList;
          this.staffData = true;
        } else {
          this._intakeService.getActiveStaffList().then((data) => {
            let staffList = JSON.parse(JSON.stringify(data['staffUsers']));
            staffList.sort(function (a, b) {
              if (a.displayName < b.displayName) {
                return -1;
              }
              if (a.displayName > b.displayName) {
                return 1;
              }
              return 0;
            });
            const dataSet = this.dynamicData.filterLiaisonsDetails[0].notifySenderOnSubmit;
            if (dataSet == false) {
              staffList = staffList.filter(x => x.id != this.userData.userId);
            }
            this._intakeService.blogStaffList = staffList;
            this.dropdownList = staffList;
            this.staffUserList = staffList;
            this.staffData = true;
          });
        }
        this.dropdownSettings = {
          lazyLoading: true,
          singleSelection: false,
          enableCheckAll: false,
          idField: 'id',
          textField: 'displayName',
          enableSearchFilter: true,
          allowSearchFilter: true,
          noDataAvailablePlaceholderText: '',
          closeDropDownOnSelection: true,
          itemsShowLimit: 3,
        };
      } else {
        this.staffData = true;
      }
    }
    this.notePlaceholder = this.noteType.toLowerCase() === 'pharmacy' ?
    this.toolTipService.getTranslateDataWithParam('PLACEHOLDERS.ADD_NOTE_WITH_LIMIT',
      {
        notetype: this.noteType,
        character: this._constantService.noteCharacterLimit
      }
    ) : this.toolTipService.getTranslateDataWithParam(
      'PLACEHOLDERS.ADD_NOTE_WITHOUT_LIMIT',
      { notetype: this.noteType }
    );
    //For limit the character while adding pharmacy note
    if(this.noteType.toLowerCase() === 'pharmacy') {
      this.noteForm.controls.noteContent.valueChanges.subscribe(value => {
        if (value && value.length > 50) {
          this.noteForm.controls.noteContent.setValue(value.slice(0, this._constantService.noteCharacterLimit), { emitEvent: false });
        }
      });
    }
    this.initializeIntegrationParam();
  }
  initializeIntegrationParam() {
    this.integrationParams = {
      'patientId': this.patientId,
      'appName': localStorage.getItem('appName'),
      'workListType': 'NextGen',
      'staffId': this.userData.userId
    };
  }
  closeDynamicModal() {
    $('#dynamic-worklist-modal').modal('hide');
    if (this.noteType == 'eDoc') {
      this.files = [];
      $('.dropify-preview').css('display', 'none');
      $('.dropify-error').css('display', 'none');
      $('.dropify-wrapper').removeClass('has-error');
      this.edocForm.reset();
    }
    this.closeModal.emit();
  }
  onNativeChange(e) {
    if (e.target.checked) {
      this.therapyComplete = true;
      this.editSubmit = false;
    } else {
      this.therapyComplete = false;
    }
  }
  onRecheckChange(e) {
    if (e.target.checked) {
      this.showRecheckOption = true;
      if(this.therapyRecheckReasonValue && this.therapyRecheckReasonValue != '') {
        this.editSubmit = false;
      }
    } else {
      this.showRecheckOption = false;
      this.editSubmit = true;
    }
  }
  addNote(noteFormData) {
    this.note = noteFormData.noteContent;
    const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
    this.loadSpinner = true;
    const subject = `${this.noteType} Note`;
    const noteType = this.noteType.toLowerCase();
    const noteData: any = {
      patientId: this.patientId,
      categoryId: this.noteCatId,
      createdBy: userDetails.userId,
      subject: subject.trim(),
      body: this.note.trim(),
      noteType: noteType,
      requestId: '',
      mrn: this.worklistEditData.patientId,
      workflowId: this.workflowId/*To be updated based on actual workflowId*/
    };
    this.integrationStatus = this.dynamicData.filterLiaisonsDetails[0].enableIntegration;
    if (typeof this.note == 'undefined' || this.note.trim() == '') {
      setTimeout(() => {
        $.notify({ message: '<strong>Error!</strong> Please add a note.' },
          { type: 'danger' });
      }, 1000);
      this.loadSpinner = false;
    } else {
      this.saveLiaisonNote(noteData);
    }
  }
  saveLiaisonNote(noteData){
    const noteType = this.noteType.toLowerCase();
    if ((noteType == 'liaison' && this.integrationStatus == false) || noteType != 'liaison') {
      this._intakeService.addNote(noteData).subscribe((res) => {
        if (res.data['userNoteCreate'].status == 201) {
          this.closeModalAfterSaveLiaison(noteData);
        } else {
          this.loadSpinner = false;
          setTimeout(() => {
            $.notify({ message: 'Error! Some error occured, please try again.' },
              { type: 'danger' });
          }, 1000);
        }
      });
    } else if (noteType == 'liaison' && this.integrationStatus == true) {
      const params = {
        'patientId': this.patientId
      };
      this._intakeService.checkIntegraiondata(params).then((res) => {
        const resData = res;
        if (resData['status'] == 'Success') {
          this._intakeService.addNote(noteData).subscribe((res) => {
            this.editId = res.data['userNoteCreate'].id;
            if (res.data['userNoteCreate'].status === 201) {
              const integrationSettings = this.integrationSettings.filter((x) => x.integrationType === 'progressNote');
              this.initializeIntegrationParam();
              this.integrationParams['integrationMode'] = 'FC';
              this.integrationParams['filePath'] = integrationSettings[0].filingCenterPath ? integrationSettings[0].filingCenterPath : '';
              this.integrationParams['integrationType'] = integrationSettings[0].enableFileCenter ? 'PNFC' : '';
              this.integrationParams['subject'] = integrationSettings[0].progressNoteSubject;
              this.integrationParams['body'] = noteData.body;
              this.integrationParams['callBackURL'] = integrationSettings[0].callbackUrl ? integrationSettings[0].callbackUrl : '';
              this.integrationParams['progressNoteType'] = integrationSettings[0].integrationDataType ? integrationSettings[0].integrationDataType : '';
              this.integrationParams['entityType'] = 'LIAISONNOTE';
              this._intakeService.integraiondata(this.integrationParams).then((integrationRes) => {
                const activityData = {
                  activityName: 'Send Progress Note',
                  activityType: 'Progress Note Integration',
                  activityDescription: this.userData.displayName + ' send progress note for note id ' + this.editId + ' and details ' + JSON.stringify(this.integrationParams) + ' and get the response of ' + JSON.stringify(integrationRes) + ' for the patient ' + this.worklistEditData.name + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.dynamicData.tabName + ' worklist in ' + this.parentWorklistName + ' dashboard'
                };
                this._structureService.trackActivity(activityData);
                const integration = integrationRes;
                const params = {
                  mrn: noteData.mrn,
                  requestId: integration['request_id'] ? integration['request_id'] : '',
                  apiResponse: JSON.stringify(integrationRes),
                  noteType: noteData.noteType,
                  workflowId: noteData.workflowId,
                  createdBy: noteData.createdBy,
                  integrationType: this.integrationParams['integrationType']
                };
                this._intakeService.editNote(this.editId, params).subscribe((res) => {
                  const activityData = {
                    activityName: 'Update liaison note',
                    activityType: 'Liaison note',
                    activityDescription: this.userData.displayName + ' update pn note with id '+this.editId+' after pn integration and details ' + JSON.stringify(params) + ' and get the response of ' + JSON.stringify(res) + ' for the patient ' + this.worklistEditData.name + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.dynamicData.tabName + ' worklist in ' + this.parentWorklistName + ' dashboard'
                  };
                  this._structureService.trackActivity(activityData);
                  this.closeModalAfterSaveLiaison(noteData);
                });
              });
            } else {
              this.loadSpinner = false;
              setTimeout(() => {
                $.notify({ message: 'Error! Some error occurred, please try again.' },
                  { type: 'danger' });
              }, 1000);
            }
          });
        } else {
          this._intakeService.enableSwal = true;
          swal({
            title: 'Are you sure?',
            text: 'You are missing Staff ID and Patient MRN which are required to complete action with EHR integration.You can continue with action anyway, but data will not be sent to EHR.',
            type: 'warning',
            showCancelButton: true,
            cancelButtonClass: 'btn-default',
            confirmButtonClass: 'btn-warning',
            cancelButtonText: 'Cancel',
            confirmButtonText: 'Continue',
            closeOnConfirm: true
          },(confirm) => {
            this._intakeService.enableSwal = false;
            if (confirm) {
              this._intakeService.addNote(noteData).subscribe((res) => {
                if (res.data['userNoteCreate'].status == 201) {
                  this.closeModalAfterSaveLiaison(noteData);
                } else {
                  this.loadSpinner = false;
                  setTimeout(() => {
                    $.notify({ message: 'Error! Some error occurred, please try again.' },
                      { type: 'danger' });
                  }, 1000);
                }
              });
            } else {
              this.closeDynamicModal();
            }
          });
        }
      });
    }
  }
  createControls() {
    this.therapyDetails = this.rowData;
    if (this.therapyDetails && this.therapyDetails.length) {
      this.therapyLen = true;
      this.therapyDetails = JSON.parse(JSON.stringify(this.therapyDetails));
      for (let i = 1; i < this.therapyDetails.length; i++) {
        this.therapyDetails[i].tempId = i;
      }
    }
    this.loadMsg = false;
    this.showField = true;    
  }
  createNewControl(fields) {
    let group: any = {};
    if (fields) {
      fields.forEach(fields => {
        group[fields.name] = new FormControl('', Validators.required);
      });
    }
    return new FormGroup(group);
  }
  onRecheckValueChange(value) {
    if (value != "") {
      this.therapyReasonId = value;
      this.therapyRecheckReasonValue = value;

      this.recheckReason.forEach(element => {
        if (element.id == value) {
          this.therapyReason = element.name;
        }
      });
      this.therapyRecheckStatus = true;
      this.editSubmit = false;
    } else {
      this.therapyReasonId = '';
      this.therapyRecheckReasonValue = '';
      this.therapyRecheckStatus = false;
      this.editSubmit = true;
    }
  }
  onEnterKeyDown(event, f) {
    if (event.keyCode == 13) {
      this.addTherapy(f);
    }
  }
  addTherapy(f) {
    const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
    if (f.valid) {
      this.therapyCount++;
      let index = -1;
      let action;
      if (this.therapyDetails && this.therapyDetails.length) {
        index = this.therapyDetails.findIndex(x => x.tempId == this.editTherapyId);
        action = this.therapyDetails.findIndex(x => x.action == 'create');
      }
      if (index != -1) {
        this.therapyList.forEach(element => {
          if (f.value['therapy'] == element.id) {
            this.therapyName = element.name;
          }
        });
          this.moaList.forEach(element => {
            if (f.value['moa'] == element.id) {
              this.moaName = element.name;
            }
          });
            this.therapyDetails[index].patientId = this.patientId;
            this.therapyDetails[index].therapyId = f.value['therapy'];
            this.therapyDetails[index].moaId = f.value['moa'];
            this.therapyDetails[index].therapy = { id: f.value['therapy'], name: this.therapyName };
            this.therapyDetails[index].moa = { id: f.value['moa'], name: this.moaName };
            this.therapyDetails[index].drug = f.value['drug'];
            this.therapyDetails[index].tempId = this.editTherapyId;
            if (action != -1) {
              this.therapyDetails[index].action = 'create';
              this.therapyDetails[index].createdBy = userDetails.userId;
            } else {
              this.therapyDetails[index].action = 'update';
              this.therapyDetails[index].modifiedBy = userDetails.userId;
            }
          
        this.showFormField = false;
        this.editSubmit = false;
        this.modifyTherapyList = true;
        this.editTherapyId = '';
      } else {
        let params = [];
        this.therapyList.forEach(element => {
          if (f.value['therapy'] == element.id) {
            this.therapyName = element.name;
          }
        });
        this.moaList.forEach(element => {
          if (f.value['moa'] == element.id) {
            this.moaName = element.name;
          }
        });
        if (this.therapyDetails && this.therapyDetails.length) {
          params = JSON.parse(JSON.stringify(this.therapyDetails));
        }
        params.push({
          patientId: this.patientId,
          tempId: this.therapyCount,
          therapyId: f.value['therapy'],
          moaId: f.value['moa'],
          therapy: { id: f.value['therapy'], name: this.therapyName },
          moa: { id: f.value['moa'], name: this.moaName },
          drug: f.value['drug'],
          action: 'create',
          createdBy: userDetails.userId
        });
        this.therapyDetails = params;
        this.editSubmit = false;
        this.modifyTherapyList = true;
        this.showFormField = false;
      }
      this.therapyForm.reset();
      this.therapyForm.patchValue({
        therapy: '',
        moa: '',
        drug: ''
      });
      this.buttonLabel = 'Add';
      this.submitted = false;
    }
  }
  cancelTherapy() {
    this.therapyForm.reset();
    this.therapyForm.patchValue({
      therapy: '',
      moa: '',
      drug: ''
    });
    this.buttonLabel = 'Add';
    this.submitted = false;
    if (this.therapyDetails && this.therapyDetails.length) {
      this.showFormField = false;
      this.therapyLen = true;
    } else {
      this.showFormField = true;
      this.editSubmit = true;
    }
  }
  deleteTherapy(i) {
    const index = this.therapyDetails.findIndex(x => x == i);
    if ((i.id && this.therapyRecheckStatus && this.therapyDetails.length == 1) ||
      (this.therapyRecheckStatus && this.therapyDetails.length == 1)) {
      swal({
        title: '',
        text: "You can't delete all therapies when therapy is in re-check status",
        type: 'warning',
        customClass: 'swal-wide',
        confirmButtonText: 'Ok',
        closeOnConfirm: true
      }, (confirm) => {
        this._intakeService.enableSwal = false;
      });
    } else if (i.id) {
      this._intakeService.enableSwal = true;
      swal({
        title: 'Are you sure?',
        text: 'You are going to delete this therapy entry ' + i.therapy.name,
        type: 'warning',
        showCancelButton: true,
        cancelButtonClass: 'btn-default',
        customClass: 'swal-wide',
        confirmButtonClass: 'btn-warning',
        confirmButtonText: 'Ok',
        closeOnConfirm: true
      }, (confirm) => {
        this._intakeService.enableSwal = false;
        if (confirm) {
          this.deleteIds.push(i.id);
          this.therapyDetails.splice(index, 1);
          if(this.therapyDetails.length > 0) {
            this.editSubmit = false;
          } else {
            this.editSubmit = true;
          }
          this.modifyTherapyList = true;
          this.showFormField = true;
        }
      });
    } else {
      this.therapyDetails.splice(index, 1);
      this.editSubmit = false;
      this.modifyTherapyList = true;
      this.showFormField = true;
    }
    if (this.therapyDetails && this.therapyDetails.length) {
      this.showFormField = false;
      this.therapyLen = true;
    } else {
      this.showFormField = true;
      this.editSubmit = true;
    }
  }
  editTherapy(i) {
    this.buttonLabel = 'Update';
    this.showFormField = true;
    const index = this.therapyDetails.findIndex(x => x.tempId == i);
    this.editTherapyId = i;
    this.therapyForm.patchValue({
      therapy: this.therapyDetails[index]['therapy'].id,
      moa: this.therapyDetails[index]['moa'].id,
      drug: this.therapyDetails[index]['drug']
    });
    this.editorDynamicFields.forEach(element => {
      const name = element.name;
      if (this.therapyDetails[index][element.name].id) {
        this.therapyForm.controls[name].setValue(this.therapyDetails[index][element.name].id);
      } else {
        this.therapyForm.controls[name].setValue(this.therapyDetails[index][element.name]);
      }
    });
  }
  addNewTherapy() {
    this.showFormField = true;
    this.submitted = false;
    this.buttonLabel = 'Add';
    this.editTherapyId = '';
    this.therapyForm.reset();
    this.therapyForm.patchValue({
      therapy: '',
      moa: '',
      drug: ''
    });
  }
  saveTherapyData() {
    let textData;
    let title;
    if (!this.therapyComplete) {
      textData = 'You are submitting without checking the Therapy Complete checkbox.';
      title = 'Do you want to continue?';
    } else if (this.therapyComplete && (this.therapyDetails && this.therapyDetails.length)) {
      textData = 'You are going to complete therapy of this patient';
      title = 'Are you sure?';
    }
    this._intakeService.enableSwal = true;
    swal({
      title: title,
      text: textData,
      type: 'warning',
      showCancelButton: true,
      cancelButtonClass: 'btn-default',
      customClass: 'swal-wide',
      confirmButtonClass: 'btn-warning',
      confirmButtonText: 'Ok',
      closeOnConfirm: true
    }, (confirm) => {
      this._intakeService.enableSwal = false;
      if (confirm) {
        this.modifyTherapyData();
      }
    });
  }
  modifyTherapyData() {
    if (this.therapyDetails && this.therapyDetails.length == 0) {
      this.therapyComplete = false;
    }
    const newTherapyDetail = [];
    this.therapyDetails.forEach(element => {
      if (element.hasOwnProperty('action') == true) {
        newTherapyDetail.push(element);
      }
    });
    const dataset = [];
    if (newTherapyDetail.length > 0) {
      newTherapyDetail.forEach(element => {
        const dataObject = {};
        if (element.hasOwnProperty('id')) {
          dataObject['id'] = element.id;
        }
        dataObject['patientId'] = element.patientId;
        dataObject['therapyId'] = element.therapyId;
        dataObject['moaId'] = element.moaId;
        dataObject['drug'] = element.drug;
        dataObject['createdBy'] = element.createdBy;
        dataObject['modifiedBy'] = element.modifiedBy;
        dataObject['createdDate'] = element.createdDate;
        dataObject['modifiedDate'] = element.modifiedDate;
        dataset.push(dataObject);
      });
    }
    const datasetMod = { dataset, 'workflowType': this.workflowType };
    if (this.deleteIds.length > 0 && newTherapyDetail.length == 0) {
      this._intakeService.deleteTherapyData(this.deleteIds).subscribe((data) => {
        if (data.data['UserTherapiesDelete'].status == 201) {
          const activityData = {
            activityName: 'Delete Therapy',
            activityType: 'Therapy',
            activityDescription: this.userData.displayName + ' deleted Therapy with id(s) ' + this.deleteIds + ' of patient with id ' + this.worklistEditData.id
              + ' and name ' + this.worklistEditData.name + ' from the dashboard ' + this.workflowType
          };
          this._structureService.trackActivity(activityData);
          this.updatePatientTherapyDetails();
        } else {
          this.showErrorMsg();
        }
      });
    } else if (this.deleteIds.length == 0 && newTherapyDetail.length > 0) {
      this._intakeService.createTherapyData(datasetMod).subscribe((data) => {
        if (data.data['UserTherapiesCreate'].status == 201) {
          const activityData = {
            activityName: 'Create Therapy',
            activityType: 'Therapy',
            activityDescription: this.userData.displayName + ' created Therapy with details ' + JSON.stringify(dataset) + ' for patient with id ' + this.worklistEditData.id
              + ' and name ' + this.worklistEditData.name + ' from the dashboard ' + this.workflowType
          };
          this._structureService.trackActivity(activityData);
          this.updatePatientTherapyDetails();
        } else {
          this.showErrorMsg();
        }
      });
    } else if (this.deleteIds.length > 0 && newTherapyDetail.length > 0) {
      this._intakeService.createTherapyData(datasetMod).subscribe((data) => {
        if (data.data['UserTherapiesCreate'].status == 201) {
          const activityData = {
            activityName: 'Create Therapy',
            activityType: 'Therapy',
            activityDescription: this.userData.displayName + ' created Therapy with details ' + JSON.stringify(dataset) + ' for patient with id ' + this.worklistEditData.id
              + ' and name ' + this.worklistEditData.name + ' from the dashboard ' + this.workflowType,
          };
          this._structureService.trackActivity(activityData);
          this._intakeService.deleteTherapyData(this.deleteIds).subscribe((data) => {
            if (data.data['UserTherapiesDelete'].status == 201) {
              const activityData = {
                activityName: 'Delete Therapy',
                activityType: 'Therapy',
                activityDescription: this.userData.displayName + ' deleted Therapy with id(s) ' + this.deleteIds + ' of patient with id ' + this.worklistEditData.id
                  + ' and name ' + this.worklistEditData.name + ' from the dashboard ' + this.workflowType,
              };
              this._structureService.trackActivity(activityData);
              this.updatePatientTherapyDetails();
            } else {
              this.showErrorMsg();
            }
          });
        } else {
          this.showErrorMsg();
        }
      });
    } else if ((this.therapyComplete || this.therapyRecheckStatus) && (this.deleteIds.length == 0 && newTherapyDetail.length == 0)) {
      this.updatePatientTherapyDetails();
    } else if (this.deleteIds.length == 0 && newTherapyDetail.length == 0) {
      this.reloadGrid.emit(true);
      this.editSubmit = true;
      $('#dynamic-worklist-modal').modal('hide');
    }
  }
  updatePatientTherapyDetails() {
    const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
    let patientData;
    let params;
    let pharmacyReviewed = 0;
    const array = new Set(this._constantService.therapyForPharmacy);
    const therapy = this.therapyDetails.find(x => !array.has(x.therapy.name));
    if(therapy) {
      pharmacyReviewed = 1;
    }
    if (this.therapyRecheckStatus && !this.therapyComplete) {
      /** Therapy recheck enabled but not complete the therapy */
        params = {
          'therapyStatus': 0,
          'pharmacyReviewed': pharmacyReviewed,
          'therapyRecheck': true,
          'therapyRecheckRequired': false,
          'therapyRecheckReason': this.therapyReason,
          'therapyRecheckReasonLovId': this.therapyReasonId,
          'mrn': this.worklistEditData.patientId
        };
        patientData = {
          'id': this.patientId,
          'params': params,
          'workflowType': this.workflowType,
          'modifiedBy': userDetails.userId
        };
        /**For save the therapy details in patient table. Eg. therapy complete status, therapy recheck status etc*/
        this._intakeService.updatePatientData(patientData).subscribe((data) => {
          if (data.data['patientUpdate'].status == 201) {
            /**For save the recheck data. Eg. recheck reason,time etc */
            this.updateTherapyRecheckData(userDetails);
            const activityData = {
              activityName: 'Therapy Recheck',
              activityType: 'Therapy',
              activityDescription: this.userData.displayName + ' recheck Therapy with recheck reason as ' + this.therapyReason +
                ' of patient with id ' + this.worklistEditData.id + ' and name ' + this.worklistEditData.name + ' from the dashboard ' + this.workflowType,
            };
            this._structureService.trackActivity(activityData);
            this.therapyRecheckRequired = false;
            this.therapyCompleteStatus = false;
            this.therapyRecheckReasonValue = this.therapyReasonId;
            this.therapyRecheckCompleted = true;
            this.therapyStatus.emit(
              { therapyStatus: 0, 
                therapyRecheck: true, 
                recheckReasonId: this.therapyReasonId, 
                recheckReason: this.therapyReason,
                btnText: 'Add Therapy',
                therapyRecheckRequired: false
              }
            );
            this.showSuccessMsg();
          } else {
            this.showErrorMsg();
          }
        });
    } else if (this.therapyComplete && (this.therapyDetails && this.therapyDetails.length)) {
      let activityData = {
        activityName: 'Therapy Complete',
        activityType: 'Therapy',
        activityDescription: this.userData.displayName + ' completed Therapy of patient with id ' + this.worklistEditData.id
          + ' and name ' + this.worklistEditData.name + ' from the dashboard ' + this.workflowType,
      };
      if (this.therapyRecheckStatus) {
        /** Thereapy recheck and complete */
        params = {
          'therapyStatus': 1,
          'pharmacyReviewed': pharmacyReviewed,
          'therapyRecheck': true,
          'therapyRecheckRequired': false,
          'therapyRecheckReason': this.therapyReason,
          'therapyRecheckReasonLovId': this.therapyReasonId,
          'mrn': this.worklistEditData.patientId
        };
        patientData = {
          'id': this.patientId,
          'params': params,
          'workflowType': this.workflowType,
          'isTherapy': true,                             //adding this parameter to check wheather therapy is completed
          'modifiedBy': userDetails.userId
        };
        activityData.activityName =  'Therapy Recheck Complete';
        activityData.activityDescription = this.userData.displayName + ' completed Therapy Recheck with recheck reason as ' + this.therapyReason + ' of patient with id ' + this.worklistEditData.id
          + ' and name ' + this.worklistEditData.name + ' from the dashboard ' + this.workflowType;
        this.updateChecklist(patientData,activityData);
      } else {
        /** Therapy complete process */
        params = { 'therapyStatus': 1, 'mrn': this.worklistEditData.patientId, 'pharmacyReviewed': pharmacyReviewed }
        patientData = {
          'id': this.patientId,
          'params': params,
          'workflowType': this.workflowType,
          'isTherapy': true,
          'modifiedBy': userDetails.userId
        };
        this.updatePatientTherapyData(patientData, activityData, userDetails);
      }
    } else {
      this.showSuccessMsg();
    }
    
  }
  updateChecklist(patientData, patientActivityData) {
    const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
    const variable = {
      patientId: this.patientId,
      checklistId: 1,
      workflowId: 3,
      statusCode: 2,
      createdBy: userDetails.userId,
      modifiedBy: userDetails.userId,
      mrn: this.worklistEditData.patientId
    };
    this._intakeService.createChecklist(variable).subscribe((data) => {
      if (data.data['UserCheckListCreate'].status == 201) {
        const activityData = {
          activityName: 'Checklist Update from recheck',
          activityType: 'Checklist',
          activityDescription: this.userData.displayName + ' updated the insurance verification checklist of patient with id ' + this.worklistEditData.id + ' and name ' + this.worklistEditData.name + ' from the dashboard ' + this.workflowType + ' as part of therapy recheck',
        };
        this._structureService.trackActivity(activityData);
        variable.checklistId = 4;
        this._intakeService.createChecklist(variable).subscribe((data) => {
          if (data.data['UserCheckListCreate'].status == 201) {
            const activityData = {
              activityName: 'Checklist Update from recheck',
              activityType: 'Checklist',
              activityDescription: this.userData.displayName + ' updated the Insurance Verification Items checklist of patient with id ' + this.worklistEditData.id + ' and name ' + this.worklistEditData.name + ' from the dashboard ' + this.workflowType + ' as part of therapy recheck',
            };
            this._structureService.trackActivity(activityData);
            this.updatePatientTherapyData(patientData, patientActivityData, userDetails);
          } else {
            this.showErrorMsg('Error! Some error occurred in recheck process. Please try again.');
          }
        });
      } else {
        this.showErrorMsg('Error! Some error occurred in recheck process. Please try again.');
      }
    });

  }
  updatePatientTherapyData(patientData, activityData, userDetails) {
    this._intakeService.updatePatientData(patientData).subscribe((data) => {    
      if (data.data['patientUpdate'].status == 201) {
        this.therapyCompleteStatus = true;
        if (this.therapyReasonId) {
          this.updateTherapyRecheckData(userDetails);
        }
        this._structureService.trackActivity(activityData);
        this.therapyRecheckCompleted = true;
        if (this.therapyRecheckStatus) {
          this.therapyStatus.emit(
            { therapyStatus: 1, 
              therapyRecheck: true, 
              recheckReasonId: this.therapyReasonId, 
              recheckReason: this.therapyReason,
              btnText: 'View Therapy',
              therapyRecheckRequired: false
            }
          );
        } else {
          this.therapyStatus.emit(
            { therapyStatus: 1, 
              therapyRecheck: false, 
              btnText: 'View Therapy',
              therapyRecheckRequired: false
            }
          );
        }
        this.showSuccessMsg();
      } else {
        this.showErrorMsg('Error! Some error occurred in recheck process. Please try again.');
      }
    });
  }
  updateTherapyRecheckData(userDetails){
    const recheckParams = {
      'patientId': this.patientId,
      'lastModifiedDate': new Date().getTime(),
      'therapyRecheckReasonLovId': this.therapyReasonId,
      'recheckWfId': 2,
      'modifiedBy': userDetails.userId,
      'status': 1
    };
    this._intakeService.updateTherapyRecheckData(recheckParams).subscribe((recheckData) => {
      if (recheckData.data['CreateTherapyRechecked'].status == 201) {
        const activityData = {
          activityName: 'Save therapy recheck data',
          activityType: 'Therapy recheck',
          activityDescription: this.userData.displayName + ' updated the therapy recheck data with reason  '+this.therapyReason + ' of patient with id ' + this.worklistEditData.id + ' and name ' + this.worklistEditData.name + ' from the dashboard ' + this.workflowType,
        };
        this._structureService.trackActivity(activityData);
      }
    });
  }
  showSuccessMsg() {
    this.reloadGrid.emit(true);
    this.editSubmit = true;
    $('#dynamic-worklist-modal').modal('hide');
    if(this.therapyComplete && this.therapyRecheckStatus){
      setTimeout(() => {
        $.notify({ message: 'Success! Therapy Recheck Completed.' },
          { type: 'success' });
      }, 1000);
    } else if(this.therapyComplete && !this.therapyRecheckStatus) {
      setTimeout(() => {
        $.notify({ message: 'Success! Therapy Completed.' },
          { type: 'success' });
      }, 1000);
    } else {
      setTimeout(() => {
        $.notify({ message: 'Success! Therapy Details Saved.' },
          { type: 'success' });
      }, 1000);
    }
    
  }
  showErrorMsg(msg = '') {
    const msgText = msg != '' ? msg : 'Error! Some error occurred, please try again.';
    setTimeout(() => {
      $.notify({ message: msgText },
        { type: 'danger' });
    }, 1000);
    this.reloadGrid.emit(true);
    this.editSubmit = true;
    $('#dynamic-worklist-modal').modal('hide');
  }
  addBlogNote(blogNoteData) {
    const blogSubject = blogNoteData.blogNoteSubject;
    const staffEmailsList = !isBlank(blogNoteData.emailRecipients) ? this.staffUserList.filter(x => 
      blogNoteData.emailRecipients.findIndex(a => a.id === x.id) > -1) : [];
    const staffEmails = staffEmailsList.map(x=> x.emails[0].value);
    blogNoteData.blogNoteSubject = Object.keys(blogSubject).length > 0 ? blogSubject[0].id.toString() : '';
    const progressSubject = blogNoteData.progressNoteSubject;
    blogNoteData.progressNoteSubject = Object.keys(progressSubject).length > 0 ? progressSubject[0].id.toString() : '';
    this.progressNoteText = Object.keys(progressSubject).length > 0 ? progressSubject[0].subject : '';
    if (this.checkData(blogNoteData)) {
      this.noteSubmit = true;
      const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
      const variables = {
        patientId: this.worklistEditData.id,
        createdBy: userDetails.userId,
        noteType: 'blog',
        workflowId: this.workflowId,
        body: blogNoteData.blogNote.trim(),
        isProgressNote: blogNoteData.progressNote,
        requestId: '',
        mrn: this.worklistEditData.patientId,
        userEmails: staffEmails.length > 0 ? staffEmails.toString() : ''
      };

      if (blogNoteData.progressNote) {
        variables['blogSubjectId'] = blogNoteData.progressNoteSubject;
        variables['subject'] = this.progressNoteText;
      } else {
        variables['blogSubjectId'] = blogNoteData.blogNoteSubject;
        variables['subject'] = Object.keys(blogSubject).length > 0 ? blogSubject[0].name : '';
      }

      this.blogNOteData(variables, blogNoteData);
    }

  }
  sendNotification(dataId, variables, blogNoteData){
    /**For send notification */
    const worklistDetails = this.dynamicData['filterLiaisonsDetails'][0];
    if (worklistDetails.enableNotification && blogNoteData.emailRecipients.length > 0) {
      const templateData = {
        subject: variables['subject'],
        body: blogNoteData.blogNote,
        userName: this.worklistEditData.name
      };
      this.notificationData.push = worklistDetails.submitPush;
      this.notificationData.pushTemplate = worklistDetails.submitPushTemplate;
      this.notificationData.email = worklistDetails.submitEmail;
      this.notificationData.emailTemplate = worklistDetails.submitEmailTemplate;
      this.notificationData.sms = worklistDetails.submitSms;
      this.notificationData.smsTemplate = worklistDetails.submitSmsTemplate;
      this.notificationData.notifyRoles = worklistDetails.notifyRolesOnSubmit == true ? worklistDetails.notifyRoles : '';
      this.notificationData.notifyRecipient = '';
      this.notificationData.notifySender = worklistDetails.notifySenderOnSubmit;
      this.notificationData.data = {};
      this._workListService.getWorklistMessageTemplate().then((data) => {
        this.allMessageTemplate = JSON.parse(JSON.stringify(data['getSessionTenant'].formWorklistMessageTemplate));
        this.sendNotificationMessage(dataId,this.notificationData, templateData, blogNoteData.emailRecipients);
      });
    }
  }
  blogNOteData(variables, blogNoteData) {
    const worklistDetails = this.dynamicData['filterLiaisonsDetails'][0];
    this.integrationStatus = this.dynamicData.filterLiaisonsDetails[0].enableIntegration;
    if (variables['noteType'] == 'blog' && blogNoteData.progressNote == false) {
      this._intakeService.addNote(variables).subscribe(
        (res) => {
          const dataId = res.data['userNoteCreate'].id;
          if (res.data['userNoteCreate'].status == 201) {
            if (worklistDetails.enableNotification && blogNoteData.emailRecipients.length > 0) {
              this.sendNotification(dataId,variables, blogNoteData);
            } else {
              /**Activity for adding blog note */
              const activityData = {
                activityName: 'Add ' + this.worklistMenuName,
                activityType: this.worklistMenuName,
                activityDescription: this.userData.displayName +
                  ' add ' + this.worklistMenuName + ' with id = ' + this.worklistEditData.id + ' subject ' +
                  variables['subject'] + ' for the patient ' +
                  this.worklistEditData.name + ' with MRN ' +
                  this.worklistEditData.patientId + ' from ' + this.dynamicData.tabName + ' in ' +
                  this.parentWorklistName
              };
              this._structureService.trackActivity(activityData);
            }
            this.closeModalAfterSaveBlog();
          } else {
            this.noteSubmit = false;
            setTimeout(() => {
              $.notify({ message: 'Error! Some error occurred, please try again.' },
                { type: 'danger' });
            }, 1000);
          }
      },(error)=>{
        this.showAddNoteErrorMessage();
      });
    } else if (variables['noteType'] == 'blog' && this.integrationStatus == true && blogNoteData.progressNote == true) {
      const params = {
        'patientId': this.patientId
      };
      this._intakeService.checkIntegraiondata(params).then((res) => {
        const resData = res;
        if (resData['status'] == 'Success') {
          this._intakeService.addNote(variables).subscribe(
            (res) => {
              if (res.data['userNoteCreate'].status == 201) {
                const dataId = res.data['userNoteCreate'].id;
                if (worklistDetails.enableNotification && blogNoteData.emailRecipients.length > 0) {
                  this.sendNotification(dataId, variables, blogNoteData);
                }
                const integrationSettings = this.integrationSettings.filter((x) => x.integrationType === 'progressNote');
                this.initializeIntegrationParam();
                this.integrationParams['integrationMode'] = 'FC';
                this.integrationParams['filePath'] = integrationSettings[0].filingCenterPath ? integrationSettings[0].filingCenterPath : '';
                this.integrationParams['integrationType'] = integrationSettings[0].enableFileCenter ? 'PNFC' : '';
                this.integrationParams['subject'] = this.progressNoteText;
                this.integrationParams['body'] = blogNoteData.blogNote.trim();
                this.integrationParams['callBackURL'] = integrationSettings[0].callbackUrl ? integrationSettings[0].callbackUrl : '';
                this.integrationParams['progressNoteType'] = integrationSettings[0].integrationDataType ? integrationSettings[0].integrationDataType : '';
                this.integrationParams['entityType'] = 'BLOGNOTE';
                this._intakeService.integraiondata(this.integrationParams).then((integrationRes) => {
                  const activityData = {
                    activityName: 'Send Progress Note',
                    activityType: 'Progress Note Integration',
                    activityDescription: this.userData.displayName + ' send progress note for note id ' + dataId + ' and details ' + JSON.stringify(this.integrationParams) + ' and get the response of ' + JSON.stringify(integrationRes) + ' for the patient ' + this.worklistEditData.name + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.dynamicData.tabName + ' worklist in ' + this.parentWorklistName + ' dashboard'
                  };
                  this._structureService.trackActivity(activityData);
                  const integration = integrationRes;
                  const params = {
                    mrn: variables.mrn,
                    requestId: integration['request_id'] ? integration['request_id'] : '',
                    apiResponse: JSON.stringify(integrationRes),
                    noteType: variables.noteType,
                    workflowId: variables.workflowId,
                    createdBy: variables.createdBy,
                    integrationType: this.integrationParams['integrationType']
                  };
                  this._intakeService.editNote(dataId, params).subscribe((res) => {
                    const activityData = {
                      activityName: 'Update blog note',
                      activityType: 'Blog or Progress note',
                      activityDescription: this.userData.displayName + ' update pn note with id '+dataId+' after pn integration and details ' + JSON.stringify(params) + ' and get the response of ' + JSON.stringify(res) + ' for the patient ' + this.worklistEditData.name + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.dynamicData.tabName + ' worklist in ' + this.parentWorklistName + ' dashboard'
                    };
                    this._structureService.trackActivity(activityData);
                    this.closeModalAfterSaveBlog();
                  },(error)=>{
                    this.showAddNoteErrorMessage();
                  });
                },(error)=>{
                  this.showAddNoteErrorMessage();
                });
                /**Activity for adding blog note */
                const activityData = {
                  activityName: 'Add ' + this.worklistMenuName,
                  activityType: this.worklistMenuName,
                  activityDescription: this.userData.displayName +
                    ' add ' + this.worklistMenuName + ' with id = ' + this.worklistEditData.id + ' subject ' +
                    variables['subject'] + ' for the patient ' +
                    this.worklistEditData.name + ' with MRN ' +
                    this.worklistEditData.patientId + ' from ' + this.dynamicData.tabName + ' in ' +
                    this.parentWorklistName
                };
                this._structureService.trackActivity(activityData);
              } else {
                this.noteSubmit = false;
                setTimeout(() => {
                  $.notify({ message: 'Error! Some error occurred, please try again.' },
                    { type: 'danger' });
                }, 1000);
              }
          },(error) => {
            this.showAddNoteErrorMessage();
          });
        } else {
          const activityData = {
            activityName: 'Checking integration',
            activityType: 'Check integration failed',
            activityDescription: 'Missing Staff ID and Patient MRN which are required to complete action with EHR integration.response ' +JSON.stringify(resData)
          };
          this._structureService.trackActivity(activityData);
          this._intakeService.enableSwal = true;
          swal({
            title: 'Are you sure?',
            text: 'You are missing Staff ID and Patient MRN which are required to complete action with EHR integration.You can continue with action anyway, but data will not be sent to EHR.',
            type: 'warning',
            showCancelButton: true,
            cancelButtonClass: 'btn-default',
            confirmButtonClass: 'btn-warning',
            cancelButtonText: 'Cancel',
            confirmButtonText: 'Continue',

            closeOnConfirm: true
          },(confirm) => {
            this._intakeService.enableSwal = false;
            if (confirm) {
                this._intakeService.addNote(variables).subscribe(
                  (res) => {
                    if (res.data['userNoteCreate'].status == 201) {
                      const dataId = res.data['userNoteCreate'].id;
                      if (worklistDetails.enableNotification && blogNoteData.emailRecipients.length > 0) {
                        this.sendNotification(dataId, variables, blogNoteData);
                      }
                      this.closeModalAfterSaveBlog();
                      /**Activity for adding blog note */
                      const activityData = {
                        activityName: 'Add ' + this.worklistMenuName,
                        activityType: this.worklistMenuName,
                        activityDescription: this.userData.displayName +
                          ' add ' + this.worklistMenuName + ' with id = ' + this.worklistEditData.id + ' subject ' +
                          variables['subject'] + ' for the patient ' +
                          this.worklistEditData.name + ' with MRN ' +
                          this.worklistEditData.patientId + ' from ' + this.dynamicData.tabName + ' in ' +
                          this.parentWorklistName
                      };
                      this._structureService.trackActivity(activityData);
                    } else {
                      this.noteSubmit = false;
                      setTimeout(() => {
                        $.notify({ message: 'Error! Some error occured, please try again.' },
                          { type: 'danger' });
                      }, 1000);
                    }
                },(error) => {
                  this.showAddNoteErrorMessage();
                });
            } else {
              if(this.fromDashboard == 'false') {
                this.closeDynamicModal();
                this.reloadGrid.emit(true);
              } else {
                this.noteSubmit = false;
                this.blogForm.patchValue({
                  'blogNoteSubject': '',
                  'progressNoteSubject': '',
                  'blogNote': '',
                  'emailRecipients': '',
                  'progressNote': false
                });
                this.selectedBlogSubjects = [];
                this.progressNoteChk = false;
                this.sendEmailChk = false;
                this.updateBlogList.emit();
              }
            }
          });
        }
      });
    }
  }
  showAddNoteErrorMessage(){
    this.noteSubmit = false;
    this._structureService.notifyMessage({
      messge: this.toolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG'),
      type: 'danger'
    });
    setTimeout(()=> {
      this.closeModalAfterSaveBlog(false);
    }, 1000);
    
  }
  sendNotificationMessage(noteId,notificationConfig, data, emailRecipients) {
    if (notificationConfig.push == true) {
      const pIndex = this.allMessageTemplate.findIndex(x => x.id == notificationConfig.pushTemplate);
      if (pIndex != -1) {
        const content = this.formatContent(this.allMessageTemplate[pIndex].content, data);
        this.sendPushNotification(content, notificationConfig, emailRecipients);
      }
    }
    let notificationContent = [];
    let mailIntegrationContent = [];
    let content = '';
    let subject = '';
    if (notificationConfig.email == true || notificationConfig.sms == true) {
      if (notificationConfig.email == true) {
        const notificationSettings = this.integrationSettings.filter((x) => x.integrationType == 'notification');
        const eIndex = this.allMessageTemplate.findIndex(x => x.id == notificationConfig.emailTemplate);
        if (notificationSettings.length > 0) {
          if (eIndex != -1) {
            content = this.formatContent(this.allMessageTemplate[eIndex].content, data);
            subject = this.formatContent(this.allMessageTemplate[eIndex].subject, data);
            mailIntegrationContent.push({ 'mode': 'email', 'content': content, 'subject': subject, 'title': this.allMessageTemplate[eIndex].title });
            this.sendEmailByIntegration(noteId,notificationSettings, mailIntegrationContent, emailRecipients);
          }
        } else {
          if (eIndex != -1) {
            content = this.formatContent(this.allMessageTemplate[eIndex].content, data);
            subject = this.formatContent(this.allMessageTemplate[eIndex].subject, data);
            notificationContent.push({ 'mode': 'email', 'content': content, 'subject': subject, 'title': this.allMessageTemplate[eIndex].title });
            this.sendEmailOrSmsNotification(notificationContent, notificationConfig, emailRecipients);
          }
        }
      }
      if (notificationConfig.sms == true) {
        const sIndex = this.allMessageTemplate.findIndex(x => x.id == notificationConfig.smsTemplate);
        if (sIndex != -1) {
          const content = this.formatContent(this.allMessageTemplate[sIndex].content, data);
          notificationContent.push({ 'mode': 'sms', 'content': content });
          this.sendEmailOrSmsNotification(notificationContent, notificationConfig, emailRecipients);
        }
      }
    }
  }
  formatContent(content, data) {
    const zone = timezone.name();
    let this1 = this;
    let newContent = content.replace(/\[(.*?)\]/g, function (m) {
      if (data.hasOwnProperty(m.slice(1, -1))) {
        return data[m.slice(1, -1)];
      } else if (m == '[Date]') {
        const newDate = momentTz.tz(new Date(), zone).format('MM/DD/YYYY');
        return newDate;
      } else if (m == '[Worklist]') {
        //return worklistName;
        return '';
      } else if (m == '[DateTime]') {
        const newDate = momentTz.tz(new Date(), zone).format('MM/DD/YYYY h:mm:ss A');
        return newDate;
      } else if (m == '[enteredBy]') {
        return this1.userData.displayName;
      } else {
        return m;
      }
    });
    return newContent;
  }
  sendPushNotification(content, notificationConfig, emailRecipients) {
    const deepLinking = {
      'pushType': '',
      'state': 'eventmenu.worklist',
      'stateParams': {},
      'tenantId': this.userData.tenantId,
      'tenantName': this.userData.tenantName
    };
    const pushMessage = content;
    let selectedRecipientsPolling = [];
    const notifyArray = emailRecipients;
    let i = 1;
    notifyArray.forEach(elem => {
      this._structureService.getUser(elem.id).then((data) => {
        const staff = data['staffUsers'][0];
        const staffDetails = {
          username: staff.emails[0].value,
          displayname: staff.displayName,
          userid: staff.id,
          roleId: staff.role.id,
          mobile: staff.mobile,
          countryCode: staff.countryCode,
          senderId: this.userData.userId
        };
        selectedRecipientsPolling.push(staffDetails);
        if (i == notifyArray.length) {
          this._structureService.sentPushNotification(selectedRecipientsPolling, 0, pushMessage, '', deepLinking, '', '');
        }
        i++;
      });
    });
  }
  sendEmailOrSmsNotification(content, notificationConfig, emailRecipients) {
    const notificationData = {
      notifyUsers: [],
      notifyMode: content,
      tenantId: this._structureService.getCookie('tenantId'),
      senderName: this.userData.displayName
    };
	//blogEmail: true
    const notifyArray = emailRecipients;
    let i = 1;
    notifyArray.forEach(elem => {
      this._structureService.getUser(elem.id).then((data) => {
        const staffList = data['staffUsers'][0];
        const staff = {
          firstname: staffList.firstName,
          lastname: staffList.lastName,
          displayname: staffList.displayName,
          username: staffList.emails[0].value,
          countryCode: staffList.countryCode,
          mobile: staffList.mobile,
          userid: staffList.id
        };
        const userIndex = notificationData.notifyUsers.findIndex(x => x.userid == staffList.id);
        if (userIndex == -1) {
          notificationData.notifyUsers.push(staff);
        }
        if (i == notifyArray.length) {
          this._structureService.socket.emit('sendWorklistNotification', notificationData);
        }
        i++;
      });
    });
  }
  sendEmailByIntegration(noteId,notificationSettings, content, emailRecipients) {
    let notifyArray = [];
    emailRecipients.forEach(element => {
      if (notifyArray.findIndex(x => x.id == this.userData.userId) == -1) {
        notifyArray.push(element);
      }
    });
    let i = 1;
    let userDetails = [];
    notifyArray.forEach(elem => {
      this._structureService.getUser(elem.id).then((data) => {
        const staffList = data['staffUsers'][0];
        const staff = {
          name: staffList.displayName,
          email: staffList.emails[0].value,
        };
        const userIndex = userDetails.findIndex(x => x.email == staffList.emails[0].value);
        if (userIndex == -1) {
          userDetails.push(staff);
        }
        if (i == notifyArray.length) {
          this.notificationIntegration(noteId,notificationSettings, content, userDetails);
        }
        i++;
      });
    });
  }
  notificationIntegration(noteId,notificationSettings, content, userDetails) {
    const emailBody = `<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
    <html xmlns="http://www.w3.org/1999/xhtml">

    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title>${this.userData.tenantName}</title>
    </head>

    <body paddingwidth="0" paddingheight="0" class="bgBody" style="padding: 0; margin: 0px; background-repeat: repeat; width: 100% !important; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; -webkit-font-smoothing: antialiased;" offset="0" toppadding="0" leftpadding="0">
        <table width="100%" border="0" cellspacing="0" cellpadding="0" class="bgBody">
            <tbody>
                <tr>
                    <td>
                        <table width="600" border="0" cellspacing="0" cellpadding="0" align="center" class="MainContainer" style="font-family: Arial, Helvetica, sans-serif;">
                            <tbody>
                                <tr>
                                    <td class="movableContentContainer">
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td bgcolor="#ffffff" style="padding: 10px 20px;">
                                                        <table width="100%">
                                                            <td width="390" align="center"><img src="https://assets.citushealth.com/a/${this.userData.tenantId}/img/account-logo-on-white-bg.png"></td>
                                                        </table>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                            <tbody>
                                                <tr>
                                                    <td valign="middle" width="600" height="60" style="text-align: center; background: #1fbdbe;">
                                                        <div class="contentEditableContainer contentImageEditable">
                                                            <div class="contentEditable" align="center">
                                                            <p style="color: #ffffff; font-size: 22px; margin: 0; font-family: Arial, Helvetica, sans-serif; line-height: 22px; font-style: italic;">${content[0].title}</p>
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tbody>
                                        </table>
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#f3f3f3 ">
                                            <tbody>
                                                <tr>
                                                    <td valign="top" width="20">&nbsp;</td>
                                                    <td>
                                                        <table width="100%" border="0" cellspacing="0" cellpadding="0" align="center">
                                                            <tr>
                                                                <td height="30"></td>
                                                            </tr>
                                                            <tr>
                                                                <td align="left" valign="top">
                                                                    <div style="color: #222222; font-size: 14px; line-height: 24px; font-family: Arial, Helvetica, sans-serif; padding: 0px; margin: 0px; font-style: italic;">
                                                                        Hi,
                                                                        <br />

                                                                        <br />
                                                                        <p style="color: #222222; font-size: 14px; line-height: 24px; padding: 0px; margin: 0px; font-style: italic;">
                                                                        ${content[0].content}</p>
                                                                        <br>
                                                                        <br> Best Regards,
                                                                        <br>${this.userData.tenantName} Team
                                                                    </div>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td height="30"></td>
                                                            </tr>
                                                        </table>
                                                    </td>
                                                    <td valign="top" width="20">&nbsp;</td>
                                                </tr>
                                            </tbody>
                                        </table> 
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0" bgcolor="#fff">
<tbody>
    <tr>
        <td>
            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                <tbody>
                    <tr>
                        <td height="8"></td>
                    </tr>
                    <tr>
                        <td>
                            <table width="100%" border="0" cellspacing="0" cellpadding="0">
                                <tbody>
                                    <tr>
                                        <td valign="middle" class="specbundle2">
                                            <table width="100%">
                                                <tr>
                                                    <td width="100px" style="font-size:12px; text-align:center;"><img src="https://assets.citushealth.com/a/${this.userData.tenantId}/img/account-logo-on-white-bg.png" data-default="placeholder" boder="0" width="80"></td>
                                                    <td>
                                                        <p style="font-size: 12px; color: #333; margin: 0px; padding: 0px;">
                                                            Powered by ${this.userData.tenantName}, <br> Collaborating to transform the patient care continuum.
                                                        </p>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td height="8"></td>
                    </tr>
                </tbody>
            </table>                                            
                                        <table width="100%" border="0" cellspacing="0" cellpadding="0" align="center">
                                            <tr>
                                                <td width="560" bgcolor="#222222" style="padding: 25px;" valign="top">
                                                    <p style="color: #a3a3a3; font-family: Arial, Helvetica, sans-serif; line-height: 20px; font-size: 13px; font-style: italic;">
                                                        *** This is an automatically generated email, please do not reply. The information in this email is confidential and intended solely for the addressee. If you are not the intended recipient, please notify us immediately and remove the message entirely from your system. ***
                                                    </p>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
            </tbody>
        </table>
        
        </td>
    </tr>
</tbody>
</table>
    </body>
    </html>`;
    this.initializeIntegrationParam();
    this.integrationParams['filePath'] = notificationSettings[0].filingCenterPath ? notificationSettings[0].filingCenterPath : '';
    this.integrationParams['subject'] = content[0].subject;
    this.integrationParams['body'] = emailBody;
    this.integrationParams['callBackURL'] = notificationSettings[0].callbackUrl ? notificationSettings[0].callbackUrl : '';
    this.integrationParams['progressNoteType'] = notificationSettings[0].integrationDataType ? notificationSettings[0].integrationDataType : '';
    this.integrationParams['entityType'] = 'BLOGNOTE';
    this.integrationParams['fromEmail'] = this.dynamicData.filterLiaisonsDetails[0].fromEmail;
    this.integrationParams['fromName'] = this.dynamicData.filterLiaisonsDetails[0].fromName;
    this.integrationParams['type'] = this.dynamicData.filterLiaisonsDetails[0].contentType;
    this.integrationParams['to'] = userDetails;
    if(notificationSettings[0].enableFileCenter && notificationSettings[0].integrationDataType === 'webhook') {
      const params = {
        'patientId': this.patientId
      };
      this._intakeService.checkIntegraiondata(params).then((res) => {
        const resData = res;
        if (resData['status'] === 'Success') {
          this.integrationParams['integrationMode'] = 'FC';
          this.integrationParams['integrationType'] = notificationSettings[0].enableFileCenter ? 'NOTIFFC' : '';
          this._intakeService.integraiondata(this.integrationParams).then((integrationRes) => {
            const activityData = {
              activityName: 'Send Notification',
              activityType: 'Notification Integration- FC',
              activityDescription: this.userData.displayName + ' send notification for note id ' + noteId + ' and details ' + JSON.stringify(this.integrationParams) + ' and get the response of ' + JSON.stringify(integrationRes) + ' for the patient ' + this.worklistEditData.name + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.dynamicData.tabName + ' worklist in ' + this.parentWorklistName + ' dashboard'
            };
            this._structureService.trackActivity(activityData);
            const integration = integrationRes;
            const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
            const params = {
              mrn: this.worklistEditData.patientId,
              requestId: integration['request_id'] ? integration['request_id'] : '',
              apiResponse: JSON.stringify(integrationRes),
              noteType: 'blog',
              workflowId: this.workflowId,
              createdBy: userDetails.userId,
              integrationType: this.integrationParams['integrationType']
            };
            this._intakeService.editNote(noteId, params).subscribe((res) => {
              const activityData = {
                activityName: 'Update blog note',
                activityType: 'Blog or Progress note',
                activityDescription: this.userData.displayName + ' update pn note with id '+noteId+' after notification-fc integration and details ' + JSON.stringify(params) + ' and get the response of ' + JSON.stringify(res) + ' for the patient ' + this.worklistEditData.name + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.dynamicData.tabName + ' worklist in ' + this.parentWorklistName + ' dashboard'
              };
              this._structureService.trackActivity(activityData);
            });
          });
        }
      });
    } else {      
      this.integrationParams['integrationMode'] = 'Webhook';
      this.integrationParams['integrationType'] = 'NOTIFWH';
      this.integrationParams['resourceURL'] = notificationSettings[0].apiEndPoint;
      this.integrationParams['partnerToken'] = notificationSettings[0].authorizationKey;
      this._intakeService.integraiondata(this.integrationParams).then((integrationRes) => {
        const activityData = {
          activityName: 'Send Notification',
          activityType: 'Notification Integration- webhook',
          activityDescription: this.userData.displayName + ' send notification for note id ' + noteId + ' and details ' + JSON.stringify(this.integrationParams) + ' and get the response of ' + JSON.stringify(integrationRes) + ' for the patient ' + this.worklistEditData.name + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.dynamicData.tabName + ' worklist in ' + this.parentWorklistName + ' dashboard'
        };
        this._structureService.trackActivity(activityData);
        const integration = integrationRes;
        const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
        const params = {
          mrn: this.worklistEditData.patientId,
          requestId: integration['request_id'] ? integration['request_id'] : '',
          apiResponse: JSON.stringify(integrationRes),
          noteType: 'blog',
          workflowId: this.workflowId,
          createdBy: userDetails.userId,
          integrationType: this.integrationParams['integrationType']
        };
        this._intakeService.editNote(noteId, params).subscribe((res) => {
          const activityData = {
            activityName: 'Update blog note',
            activityType: 'Blog or Progress note',
            activityDescription: this.userData.displayName + ' update pn note with id '+noteId+' after notification-webhook integration and details ' + JSON.stringify(params) + ' and get the response of ' + JSON.stringify(res) + ' for the patient ' + this.worklistEditData.name + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.dynamicData.tabName + ' worklist in ' + this.parentWorklistName + ' dashboard'
          };
          this._structureService.trackActivity(activityData);
        });
      });
    }
    
  }
  closeModalAfterSaveLiaison(noteData) {
    let str: string = noteData.noteType;
    str = str[0].toUpperCase() + str.slice(1);
    let notify = $.notify('Success!' + ' ' + str + ' ' + 'Note Saved.');
    setTimeout(function () {
      notify.update({ 'type': 'success', 'message': 'Success!' + ' ' + str + ' ' + 'Note Saved.' });
    }, 1000);
    this.closeDynamicModal();
    this.reloadGrid.emit(true);
    const activityData = {
      activityName: 'Add Note',
      activityType: noteData.noteType,
      activityDescription: this.userData.displayName +' add ' + noteData.noteType + ' with id= ' + noteData.patientId +
        ' subject ' +noteData.subject + ' for the patient ' +this.worklistEditData.name + ' with MRN ' +
        this.worklistEditData.patientId + ' from ' + this.dynamicData.tabName + ' in ' +this.parentWorklistName
    };
    this._structureService.trackActivity(activityData);
  }
  closeModalAfterSaveBlog(status: Boolean = true){
    if(status) {
      setTimeout(() => {
        this._structureService.notifyMessage({
          messge: this.toolTipService.getTranslateDataWithParam('SUCCESS_MESSAGES.SAVE_WORKLIST', { worklistMenuName: this.worklistMenuName }),
          type: 'success'
        });
      }, 1000);
    }
    if(this.fromDashboard == 'false') {
      this.closeDynamicModal();
      this.reloadGrid.emit(true);
    } else {
      this.noteSubmit = false;
      this.blogForm.patchValue({
        'blogNoteSubject': '',
        'progressNoteSubject': '',
        'blogNote': '',
        'emailRecipients': '',
        'progressNote': false
      });
      this.selectedBlogSubjects = [];
      this.progressNoteChk = false;
      this.sendEmailChk = false;
      this.updateBlogList.emit();
    }
  }
  blogNoteChange(item: any) {
    if (item.name != '') {
      this.showSubjectError = false;
    }
  }
  progressNoteChange(item: any) {
    if (item.name != '') {
      this.showPnSubjectError = false;
    }
  }
  sendEmailChange() {
    if (this.notifyStatus == true) {
      this.sendEmailChk = !this.sendEmailChk;
    } else{
        this.loadNotifyModal();
      }
  }
  progressNoteChkChg() {
    this.progressNoteChk = !this.progressNoteChk;
    this.blogForm.patchValue({
      'blogNoteSubject': ''
    });
    this.showSubjectError = false;
  }
  checkData(blogNoteData) {
    let hasError = false;
    this.noteError = false;
    this.showPnSubjectError = false;
    this.showSubjectError = false;
    if (blogNoteData.blogNote.trim() == '') {
      this.noteError = true;
      hasError = true;
    }
    if (blogNoteData.progressNote && blogNoteData.progressNoteSubject == '') {
      this.showPnSubjectError = true;
      hasError = true;
    }
    if (!blogNoteData.progressNote && blogNoteData.blogNoteSubject == '') {
      this.showSubjectError = true;
      hasError = true;
    }
    if (hasError) {
      return false;
    } else {
      return true;
    }
  }
  getTenantroles() {
    this._signService.getAllTenantRoles().then((data) => {
      this.tenantRoles = data;
    });
  }
  loadNotifyModal() {
    setTimeout(function () {
      this._intakeService.enableSwal = true;
      swal({
        title: '',
        text: 'Please enable Notification field in worklist',
        customClass: 'swal-size-sm',
      },
        (confirm) => {
          this._intakeService.enableSwal = false;
        })
        , 1000;
    });
  }

  metaDataSet(uploadedFiles, callBack) {
    let metaData = {};
    for (var i = 0; i < uploadedFiles.length; i++) {
      metaData[i] = {
        "attributes": {
          "data": [{
            "userId": this.userData.userCmisId,
            "actualUserId": this.userData.userId,
            "owner": this.userData.userCmisId,
            "userType": "owner",
            "objectType": "file",
            "fileType": uploadedFiles[i].format,
            "displayName": uploadedFiles[i].details.name,
            "parentFolderId": 'nil',
            "isDeleted": false,
            "createdOn": new Date().getTime(),
            "modifiedOn": new Date().getTime(),
            "sourceType": 'attachment',
            "fileOriginalName": uploadedFiles[i].name,
          }]
        }
      };
      if (i == (uploadedFiles.length - 1)) {
        callBack(metaData);
      }
    };
  }
  /* file upload starts*/
  onUploadOutput(output: UploadOutput): void {
    let self = this;
    if (output.type === 'allAddedToQueue') { // when all files added in queue

    } else if (output.type === 'addedToQueue' && typeof output.file !== 'undefined') { // add file to array when added
      if (output.file.size > 20971520) {
        //if( output.file.size > 1048576 ){  
        $.notify({
          message: 'Sorry for the inconvenience! ' + output.file.name + ' file size more than 20 mb not supported.'
        }, {
          type: 'danger'
        }, {
          delay: 5000
        });
      } else if (!this.isValidFile(output.file.name)) {
        $.notify({
          message: 'Sorry for the inconvenience! ' + output.file.name + ' files types are not supported.'
        }, {
          type: 'danger'
        }, {
          delay: 5000
        });
      } else if (this.files.length >= 10) {
        $.notify({
          message: "Sorry for the inconvenience! More than 10 files can't be uploaded at a time."
        }, {
          type: 'danger'
        }, {
          delay: 5000
        });
      } else {
        this.files.push(output.file);
      }
    } else if (output.type === 'uploading' && typeof output.file !== 'undefined') {
      // update current data in files array for uploading file
      const index = this.files.findIndex(file => typeof output.file !== 'undefined' && file.id === output.file.id);
      this.files[index] = output.file;
    } else if (output.type === 'removed') {
      // remove file from array when removed
      this.files = this.files.filter((file: UploadFile) => file !== output.file);
      if (this.files.length == 0) {
        $(".dropify-clear").trigger("click");
        this.removeAllFiles();
      }

    } else if (output.type === 'dragOver') {
      this.dragOver = true;
    } else if (output.type === 'dragOut') {
      this.dragOver = false;
    } else if (output.type === 'drop') {
      this.dragOver = false;
    }

    if (output.type === 'done') {
      if (output.file.response.msg && output.file.response.view) {
        var imgdata = { 'id': output.file.fileIndex, 'details': output.file, 'name': output.file.response.msg, 'format': output.file.response.view, 'size': output.file.size, 'type': output.file.type };
        this.selectedFileNames.push(imgdata);
        if (this.files.length == this.selectedFileNames.length) {
          this.uploadFlag = true;
          this.metaDataSet(this.selectedFileNames, function (data) {
            let metaData = JSON.stringify(data);
            let fileUniqueName = 'multipleupload';
            self._structureService.uploadFileToCmis(fileUniqueName, metaData).subscribe((filedata) => {
              let cmisFileData: any = filedata;
              cmisFileData = JSON.parse(cmisFileData.data.uploadFileToCmis.cmisFileData);
              self.sendEdocData(cmisFileData);
            })
          });
        }
      } else {
        swal("Sent Failed!", "Some error occured, please try again.", "error");
      }
    }
  }
  /* file upload ends*/
  sendEdocData(cmisFileData) {
    const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
    var self = this;
    var imgPath = '';
    let filesAttachedFailed = '';
    var count = 0;
    if (cmisFileData) {
      for (var key in cmisFileData) {
        let FileData = JSON.parse(cmisFileData[key]);
        if (FileData.status == 200) {
          this.cmisFileUploadData.push(FileData.results);
          if (FileData.results.organizationId) {
            var type = FileData.results.attributes.data[0].fileType;
            if (type == 'image') {
              this.filesAttached.image = this.filesAttached.image + 1;
              var fileUrl = this.userData.cmisFileBaseUrl + FileData.results.attachment[0].attributes.fileshareId + '.json?type=thumbnail';
            } else if (type == 'document' || type == 'pdf') {
              if (type == 'document') {
                this.filesAttached.document = this.filesAttached.document + 1;
              } else {
                this.filesAttached.pdf = this.filesAttached.pdf + 1;
              }
              var fileUrl = this.userData.cmisFileBaseUrl + FileData.results.attachment[0].attributes.fileshareId + '.json?type=pdf';
            }
            var fileName = FileData.results.attributes.data[0].displayName.substring(0, FileData.results.attributes.data[0].displayName.lastIndexOf('.'));
            let dataSet = [{
              "patientId": this.patientId,
              "mrn": this.worklistEditData.id,
              "fileName": fileName,
              "filePath": fileUrl,
              "cmisId": this.userData.userCmisId,
              "description": this.descriptionData,
              "notes": this.textDescription,
              "descriptionId": this.descriptionId,
              "documentType": "",
              "documentCategory": type,
              "createdBy": userDetails.userId
            }]
            this._intakeService.CreatePatientEdocs(dataSet).subscribe((data) => {
              if (data.data['CreatePatientEdocs'].status == 201) {
                self.files = [];
                $('.dropify-preview').css('display', 'none');
                var activityData = {
                  activityName: "Add eDoc",
                  activityType: "eDoc",
                  activityDescription: this.userData.displayName +
                    ' add eDoc with file Name ' + dataSet['fileName'] +
                    ' and cpr+ description ' +
                    dataSet['description'] + ' for the patient ' +
                    this.worklistEditData.name + ' with MRN ' +
                    this.worklistEditData.patientId + ' from ' + this.dynamicData.tabName + ' in ' +
                    this.parentWorklistName
                };
                this._structureService.trackActivity(activityData);
                var notify = $.notify(' Success! eDocs Saved.');
                setTimeout(function () {
                  notify.update({ 'type': 'success', 'message': '<strong>  Success! eDocs Saved. </strong>' });
                }, 1000);
              }
              $('#dynamic-worklist-modal').modal('hide');
              this.reloadGrid.emit(true);
            });
          }
        } else {
          count = count + 1;
          if (filesAttachedFailed == '') {
            filesAttachedFailed = FileData.name;
          } else {
            filesAttachedFailed = filesAttachedFailed + ', ' + FileData.name;
          }
        }
      }
    }
  }

  isValidFile(filename) {
    var ext = filename.split('.').pop().toLowerCase();
    var allowedExt = ["gif", "png", "jpg", "jpeg", "jpe", "mpeg", "bmp", "mpg", "mpe", "avi", "movie", "qt", "mov", "pdf", "doc", "docx", "word", "xl", "xls", "xlsx", "aac", "amr"];
    if (allowedExt.indexOf(ext) >= 0) {
      return true;
    } else {
      return false;
    }
  }
  removeAllFiles(): void {
    this.uploadInput.emit({ type: 'removeAll' });
  }
  formatBytes(bytes, decimals) {
    if (bytes === 0) {
      return '0 Bytes';
    }
    const k = 1024;
    const dm = decimals <= 0 ? 0 : decimals || 2;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
  }
  removeFile(id: string): void {
    this.uploadInput.emit({ type: 'remove', id: id });
  }
  saveEdocData(f, element, text) {
    if (f.valid) {
      element.textContent = text;
      element.disabled = true;
      this.descriptionId = this.edocForm.value['descriptionListId'];
      this.descriptionData = $('#descriptionListData_' + this.descriptionId).val();
      this.textDescription = this.edocForm.value['edocText'];
      if (this.files.length > 0) {
        this.startUpload();
      } else {
        this.sendEdocData(null);
      }
    } else {
      // alert('pls fill des')
    }
  }
  startUpload(): void {
    const event: UploadInput = {
      type: 'uploadAll',
      // url:  'https://admin.nelc.sandbox.citushealth.com/cometchat/citus-health/v4/chatfile-upload.php',
      url: this._structureService.apiBaseUrl + 'citus-health/v4/chatfile-upload.php',
      method: 'POST',
      headers: {
        'Authentication-Token': this._structureService.getCookie('authenticationToken'),
      },
      data: { user: this._structureService.getCookie('userId') },
    };
    this.uploadInput.emit(event);
  }
}
