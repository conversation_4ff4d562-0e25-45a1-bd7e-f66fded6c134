
<div *ngIf="noteType == 'Liaison' || noteType=='Insurance' || noteType=='Intake' || noteType == 'Pharmacy'">
  <form [formGroup]="noteForm" (ngSubmit)="addNote(noteForm.value)">
    <div class="modal-body">
      <div class="container">
        <div class="row">
          <div class="patient-header row">
            <div class="col-sm-4 patient-data">
              <div>{{ 'LABELS.MRN' | translate }}: {{worklistEditData?.patientId}}</div>
            </div>
            <div class="col-sm-8 patient-data">
              <div>{{ 'LABELS.PATIENT_NAME' | translate }}: {{worklistEditData?.name}}</div>
            </div>
          </div>
          <div class="col-sm-12">
            <div>
              <textarea placeholder="{{notePlaceholder}}" formControlName="noteContent"
                class="note-textarea"></textarea>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-secondary note-close-btn" (click)="closeDynamicModal()"> {{ 'BUTTONS.CLOSE' | translate }}</button>
      <button type="submit" *ngIf="!loadSpinner" class="btn btn-primary note-save-btn"> {{ 'BUTTONS.SAVE_NOTE' | translate }}</button>
      <button type="button" class="btn btn-primary note-save-btn" style="width: 95px;" *ngIf="loadSpinner"><i
          class="fa fa-circle-o-notch fa-spin"></i></button>
    </div>
  </form>
</div>
<div class="blognots-modal">
  <div *ngIf="noteType == 'Blog'">
    <div *ngIf="blogSubjectData == false || pnSubjectData == false || staffData == false" class="loading-container">
      <div class="lds-roller">
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
        <div></div>
      </div>
      <div class="loading-text" style="margin-bottom: 15%;">{{ 'MESSAGES.LOADING_DATA' | translate }}</div>
    </div>
    <div *ngIf="blogSubjectData == true && pnSubjectData == true && staffData == true">
      <div class="row patient-details-header">
        <div class="col-sm-4 patient-data">
          <div>{{ 'LABELS.MRN' | translate }}: {{worklistEditData?.patientId}}</div>
        </div>
        <div class="col-sm-8 patient-data">
          <div>{{ 'LABELS.PATIENT_NAME' | translate }}: {{worklistEditData?.name}}</div>
        </div>
      </div>
      <form [formGroup]="blogForm" (ngSubmit)="addBlogNote(blogForm.value)">
        <div class="modal-body">
          <div class="container notes-modal-content">
            <div class="row">
              <div class="form-group blognotesubject-section">
                <label for="">{{ 'LABELS.SUBJECT' | translate }}</label>
                <ng-multiselect-dropdown [placeholder]="'Select Subject'" [data]="blogSubjects"
                  [settings]="dropdownSettingsBlogSubject" [(ngModel)]="selectedBlogSubjects"
                  formControlName="blogNoteSubject" (onSelect)="blogNoteChange($event)">
                </ng-multiselect-dropdown>
                <br>
                <div *ngIf="showSubjectError" class="alert alert-danger">Please select {{worklistMenuName}} subject
                </div>
              </div>
              <div class="form-group">
                <label for="">{{ 'LABELS.NOTIFICATION_RECIPIENT' | translate }}</label>
                <input class="sendemail-checkbox" type="checkbox" formControlName="sendEmail" [checked]="sendEmailChk"
                  (click)="sendEmailChange()">
                <div class="sendemail-dropdown">
                  <ng-multiselect-dropdown [hidden]="!sendEmailChk || notifyStatus=='false'"
                    [placeholder]="'Select Options'" [data]="dropdownList" [(ngModel)]="selectedItems"
                    [settings]="dropdownSettings"
                    formControlName="emailRecipients">
                  </ng-multiselect-dropdown>
                </div>
              </div>
            </div>
            <div class="row">
              <div class="cpr-area">
                <label for="">{{ 'LABELS.CPR_PROGRESS_NOTE' | translate }}</label>
                <input class="sendemail-checkbox" type="checkbox" formControlName="progressNote"
                  [checked]="progressNoteChk" (click)="progressNoteChkChg()">
              </div>
              <div class="cpr-subject-label" [hidden]="!progressNoteChk">
                <label for="">{{ 'LABELS.SUBJECT' | translate }}</label>
              </div>
              <div class="cpr-dropdown cpr-subjectdropdown" [hidden]="!progressNoteChk">
                <ng-multiselect-dropdown [placeholder]="'Select Subject'" [data]="progressNoteSubject"
                  [settings]="dropdownSettingsProgressSubject" formControlName="progressNoteSubject"
                  (onSelect)="progressNoteChange($event)">
                </ng-multiselect-dropdown>
                <br>
                <div *ngIf="showPnSubjectError" class="alert alert-danger">{{ 'VALIDATION_MESSAGES.PLEASE_SELECT_PROGRESS_NOTE' | translate }}</div>
              </div>
            </div>
            <div class="row">
              <div class="form-group blognote-section">
                <label for=""></label>
                <textarea class="form-control" placeholder="Add {{worklistMenuName}}" formControlName="blogNote"
                  class="blog-textarea"></textarea>
                <br>
                <div *ngIf="noteError" class="alert alert-danger">{{ 'VALIDATION_MESSAGES.PLEASE_ENTER_NOTE' | translate }}</div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-footer" *ngIf="fromDashboard == 'true'" style="border:none !important;">
          <button type="button" class="btn btn-secondary note-close-btn" (click)="closeDynamicModal()">{{ 'BUTTONS.CLOSE' | translate }}</button>
          <button type="submit" [disabled]="noteSubmit" class="btn btn-primary note-save-btn" [hidden]="noteSubmit">{{ 'BUTTONS.SAVE_NOTE' | translate }} </button>
          <button type="button" class="btn btn-primary note-save-btn" style="width: 95px;" [hidden]="!noteSubmit"><i
              class="fa fa-circle-o-notch fa-spin"></i></button>
        </div>
        <hr *ngIf="fromDashboard == 'true'">
        <div class="modal-footer" *ngIf="fromDashboard == 'false'">
          <button type="button" class="btn btn-secondary note-close-btn" (click)="closeDynamicModal()">{{ 'BUTTONS.CLOSE' | translate }}</button>
          <button type="submit" [disabled]="noteSubmit" class="btn btn-primary note-save-btn" [hidden]="noteSubmit">{{ 'BUTTONS.SAVE_NOTE' | translate }} </button>
          <button type="button" class="btn btn-primary note-save-btn" style="width: 95px;" [hidden]="!noteSubmit"><i
              class="fa fa-circle-o-notch fa-spin"></i></button>
        </div>
      </form>
    </div>
  </div>
</div>
<div *ngIf="noteType == 'eDoc'">
  <div class="modal-body">
    <div class="row p-2 headBox">
      <div class="container">
        <div class="row">
          <div class="col-sm-3 patient-data">
            <div>{{ 'LABELS.MRN' | translate }}: {{worklistEditData?.patientId}}</div>
          </div>
          <div class="col-sm-9 patient-data">
            <div>{{ 'LABELS.PATIENT_NAME' | translate }}: {{worklistEditData?.name}}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="container filedropContainer">
      <input type="file" class="dropify" ngFileSelect (uploadOutput)="onUploadOutput($event)"
        [uploadInput]="uploadInput" multiple
        data-allowed-file-extensions="gif png jpg jpeg jpe mpeg bmp mpg mpe avi movie qt mov pdf doc docx word xl xls xlsx aac amr"
        data-max-file-size="20M">
    </div>
    <form class="row" [formGroup]="edocForm" #edoc="ngForm">
      <div class="files-list" *ngIf="files?.length">
        <div class="single-file" *ngFor="let f of files; let i = index;">
          <div class="file-icon" style="width: 50px">
            <i class="fa fa-file-image-o fa-3x" aria-hidden="true"></i>
          </div>
          <div class="leftbox">
            <h4 class="name">
              {{ f.name }}
            </h4>
            <p class="size">
              {{ formatBytes(f.size) }}
            </p>
          </div>
          <div class="middlebox">
            <select formControlName="descriptionListId" id="descriptionListId" class="form-control"
              [ngClass]="(edocForm.controls['descriptionListId'].errors && (edocForm.controls['descriptionListId']?.dirty || edocForm.controls['descriptionListId']?.touched || edocSubmitted))?'red-border-class':'no-border-class'">
              <option value="">{{ 'LABELS.CPR_DOCUMENT_DESCRIPTION' | translate }}</option>
              <option *ngFor="let opt of descriptionList" value="{{opt.id}}">
                <input type="hidden" id="descriptionListData_{{opt.id}}" name="descriptionListData"
                  value="{{opt.name}}">
                {{opt.name}} </option>
            </select>
          </div>
          <div class="rightbox">
            <textarea id="edocText" formControlName="edocText" name="edocText" rows="1" cols="20">
                </textarea>
          </div>
          <div (click)="removeFile(f.id)">
            <i title="{{ 'BUTTONS.DELETE' | translate }}" class="fa fa-trash-o" class="edoc-delete"></i>
          </div>
        </div>
      </div>
    </form>
  </div>
  <div class="modal-footer" *ngIf="noteType == 'eDoc'">
    <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="closeDynamicModal()">Close</button>
    <button type="button" class="btn btn-primary" [disabled]="!files?.length" style="cursor:pointer;"
      (click)="saveEdocData(edoc, $event.target, 'Uploading...');edocSubmitted = true">Save</button>
  </div>
</div>
<div class="modal-body" *ngIf="noteType == 'Therapy'">
  <span *ngIf="loadMsg == true"><img src="./assets/img/loader/loading.gif" /></span>
  <div *ngIf="showField">
    <div class="row p-2 headBox">
      <div class="container">
        <div class="row">
          <div class="col-sm-3 patient-data">
            <div>MRN: {{worklistEditData?.patientId}}</div>
          </div>
          <div class="col-sm-9 patient-data">
            <div>Patient Name: {{worklistEditData?.name}}</div>
          </div>
        </div>
      </div>
    </div>
    <div class="row p-2 headBoxBg"
      *ngIf="!therapyRecheckRequired && !therapyRecheckReasonValue && therapyDetails.length > 0">
      <div class="container">
        <div class="row">
          <div class="col-md-3 therapyText">
            {{ 'LABELS.THERAPY_COMPLETE' | translate }}
            <input type="checkbox" [disabled]="therapyCompleteStatus" [checked]="therapyCompleteStatus"
              id="myComCheckbox" (click)="onNativeChange($event)" style="margin-left: 18px;" />
          </div>
        </div>
      </div>
    </div>
    <div class="row p-2 headBoxBg" [hidden]="therapyDetails.length == 0"
      *ngIf="therapyRecheckRequired || therapyRecheckStatus">
      <div class="container" [ngClass]="dbFour ? 'divDisabled' : null">
        <div class="row">
          <div class="col-md-3 textAlign">
            <input type="checkbox" [disabled]="therapyRecheckCompleted" [checked]="therapyRecheckCompleted"
              id="myCheckbox" (change)="onRecheckChange($event)" />
            {{ 'LABELS.THERAPY_RECHECK' | translate }}</div>
          <div class="col-md-3" *ngIf="showRecheckOption || therapyRecheckCompleted">
            <select class="form-control pointer" style="padding: 4px;" [disabled]="therapyRecheckCompleted"
              (change)="onRecheckValueChange($event.target.value)">
              <option value="">{{ 'LABELS.RECHECK_REASON' | translate }}</option>
              <option *ngFor="let opt of recheckReason" value="{{opt.id}}"
                [selected]="opt.id==therapyRecheckReasonValue">{{opt.name}}
              </option>
            </select>
          </div>
          <div class="col-md-3 therapyText"
            *ngIf="(therapyRecheckStatus && showRecheckOption) || therapyRecheckCompleted">
            {{ 'LABELS.THERAPY_COMPLETE' | translate }}
            <input type="checkbox" id="myCheckbox" [disabled]="therapyCompleteStatus && !therapyRecheckRequired"
              [checked]="therapyCompleteStatus" (change)="onNativeChange($event)" />
          </div>
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="showField"
    [ngClass]="dbFour || (therapyCompleteStatus && !therapyRecheckRequired)  ||(therapyCompleteStatus && therapyRecheckRequired && (!therapyRecheckStatus || !showRecheckOption)) ? 'divDisabled' : null">
    <div class="row p-2" [hidden]="therapyDetails.length == 0">
      <div class="col-md-2">
        <h5 class="therapy-label">{{ 'LABELS.THERAPY' | translate }}</h5>
      </div>
      <div class="col-md-2" style="width:200px">
        <h5 class="therapy-label">{{ 'LABELS.MOA' | translate }}</h5>
      </div>
      <div class="col-md-5" style="width:200px">
        <h5 class="therapy-label">{{ 'LABELS.DRUG' | translate }}</h5>
      </div>
    </div>
    <div class="row p-2" [hidden]="therapyDetails.length == 0" *ngFor="let detail of therapyDetails;let i = index;">
      <div class="col-md-2">
        <span class="therapy-text">{{detail['therapy'].name}}</span>
      </div>
      <div class="col-md-2">
        <span class="therapy-text">{{detail['moa'].name}}</span>
      </div>
      <div class="col-md-5">
        <span class="therapy-text">{{detail['drug']}}</span>
      </div>
      <div class="col-md-3">
        <button title="{{ 'BUTTONS.ADD_NEW_THERAPY' | translate }}" class="btn btn-outline btn-sm pull-right" *ngIf="(i+1) == therapyDetails.length"
          (click)="addNewTherapy()"><i class="fa fa-plus"></i></button>
        <button title="{{ 'BUTTONS.DELETE' | translate }}" class="btn btn-outline btn-sm pull-right" (click)="deleteTherapy(detail)"><i
            class="icmn-bin"></i></button>
        <button title="{{ 'BUTTONS.EDIT' | translate }}" class="btn btn-outline btn-sm pull-right" (click)="editTherapy(detail.tempId);"><i
            class="fa fa-pencil"></i></button>
      </div>
    </div>
    <div class="row p-2" [hidden]="!showFormField">
      <form class="row" [formGroup]="therapyForm" #th="ngForm" style="width:100%;"
        (keydown.enter)="onEnterKeyDown($event, th)">
        <div class="col-md-2">
          <select formControlName="therapy" class="form-control pointer selecttxtTherapy"
            [ngClass]="(therapyForm.controls['therapy'].errors && submitted )?'red-border-class':'no-border-class'">
            <option value="">{{ 'LABELS.SELECT' | translate }} {{ 'LABELS.THERAPY' | translate }}</option>
            <option *ngFor="let opt of therapyList" value="{{opt.id}}">{{opt.name}}
            </option>
          </select>
        </div>
        <div class="col-md-2">
          <select formControlName="moa" class="form-control pointer selecttxtMoa"
            [ngClass]="(therapyForm.controls['moa'].errors && submitted )?'red-border-class':'no-border-class'">
            <option value="">{{ 'LABELS.SELECT' | translate }} {{ 'LABELS.MOA' | translate }}</option>
            <option *ngFor="let opt of moaList" value="{{opt.id}}">{{opt.name}}
            </option>
          </select>
        </div>
        <div class="col-md-4 ml-4">
          <input type="text" placeholder="Drug, Dose & Freq." formControlName="drug" class="form-control textDrug"
            [ngClass]="(therapyForm.controls['drug'].errors && submitted)?'red-border-class':'no-border-class'" />
        </div>
        <div class="col-md-3">
          <button class="btn btn-primary btn-sm mt-1 pull-right" style="margin:-10px"
            (click)="showFormField = false;cancelTherapy();">{{ 'BUTTONS.CANCEL' | translate }}</button>
          <button type="submit" class="btn btn-primary btn-sm mt-1 pull-right" style="margin:0px 13px"
            (click)="addTherapy(th);submitted = true">{{buttonLabel}}</button>
        </div>
      </form>
    </div>
  </div>
  <div class="modal-footer" *ngIf="noteType == 'Therapy' && showField">
    <span class="chatwith-modal-tip">
      <img src="./assets/modules/dummy-assets/common/img/chatwith-tip.png">
      <span class="modal-footer-text">{{ 'LABELS.THERAPY_BUTTON_MESSAGE' | translate }}</span>
    </span>
    <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="closeDynamicModal()">{{ 'BUTTONS.CLOSE' | translate }}</button>
    <button type="button" id="saveThBtn" class="btn btn-primary" style="cursor:pointer;" [disabled]="editSubmit"
      (click)="saveTherapyData()">{{ 'BUTTONS.SUBMIT' | translate }}</button>
  </div>
</div>