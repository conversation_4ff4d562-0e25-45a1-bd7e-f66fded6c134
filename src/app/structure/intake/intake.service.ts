import { Injectable } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import { Apollo } from 'apollo-angular';
import gql from 'graphql-tag';
import { isBlank } from 'app/utils/utils';
import { StructureService } from '../structure.service';
import { FormsService } from '../forms/forms.service';
import { configTimeZone } from '../../../environments/environment';

const getAllAppNames = gql`
	query apps($sessionId: String!, $crossTenantId: Int) {
		apps(sessionId: $sessionId, crossTenantId: $crossTenantId) {
			name
			id
			guid
			description
			redirectUrl
			launchUrl
			imageName
			iconImagePath
			imageThumbName
		}
	}

`;
const getAllLocation = gql`
	query getLocations {
		getLocations {
			id
			locationName
            abbreviation
            type
           status
		}
	}
	
	`;
	const getTeams = gql`
	query getTeams($tenantIds:[Int])  {
		getTeams(tenantIds:$tenantIds) {
			id
			name
		}
	}
	
	`;

const getSites = gql`
	query getSites($tenantIds:[Int]) {
		getSites(tenantIds:$tenantIds) {
			id
	       name
		}
	}
	
	`;
const getTherapiesList = gql`
	query getTherapies($tenantIds:[Int] ) {
		getTherapies (tenantIds:$tenantIds){
			id
	       name
		   description
		}
	}
	
	`;
const getInsuranceList = gql`
query getInsurance ($tenantIds:[Int]) {
	getInsurance (tenantIds:$tenantIds){
		id
		name
	}
}`;

const getRecheckReasonsList = gql`
query getRecheckReason {
	getRecheckReason {
		id
		status
		name	
	}
}`;
const getMoasList = gql`
query getMoas($tenantIds:[Int]){
	getMoas(tenantIds:$tenantIds){
	  id
	  name
	  description
	}
  }
`;
	const getAllPatientStatus = gql`
	query getPatientStatus($id: ID, $tenantIds:[Int] ) {
		getPatientStatus (id: $id,tenantIds:$tenantIds ){
			id
           name
		}
	}
	
	`;
const blogSubjects = gql`
query blogSubjects($id: ID, $tenantIds:[Int] ) {
	blogSubjects(id: $id,tenantIds:$tenantIds )  {
    id
    name
  }
}
`;
const progressNoteSubject = gql`
query progressNoteSubject($id: ID, $tenantIds:[Int] ) {
  progressNoteSubject(id: $id, tenantIds:$tenantIds) {
    id
    subject
    status
  }
}
`;
const getDocList = gql`
query getDocList($tenantIds:[Int]){
	getDocList(tenantIds:$tenantIds){
	  id
	  name
	}
  }
`;
const getIntegrationRequestIds = gql`
query getIntegrationRequest($id:Int!,$noteType:noteTypeEnum!, $mrn: ID!){
	getIntegrationRequest(id:$id,noteType:$noteType, mrn: $mrn){
	  requestId
	}
  }
`;
const getStaffIdUsers = gql`
query getStaffIdUsers($appGuid: String) {
	getStaffIdUsers(appGuid: $appGuid) {
		staffIds
  }
}
`;
const updateLiaisonEntryData = gql`

mutation patientUpdate($id:ID!, $params:PatientInput!, $workflowType: WorkflowType!){
	patientUpdate(id:$id, params:$params, workflowType: $workflowType) {
		status
		statusMessage
	} 
  }
`;

const addNote = gql`
mutation userNoteCreate($input: NoteInput) {
  userNoteCreate(params: $input) {
	  id
	status
    statusMessage
	}
}
`;
const editNote = gql`
mutation editUserNote($id: ID!,$params: NoteInput) {
	editUserNote(id: $id, params: $params){
	status
    statusMessage
	}
}
`;
const UserTherapiesDelete = gql`
mutation UserTherapiesDelete($ids :[ID!]) {
	UserTherapiesDelete(ids:$ids){
		status
		statusMessage
	  }
}
`;
const createTherapies = gql`
mutation UserTherapiesCreate($arrayParams :[TherapiesUserIntput!], $workflowType:WorkflowType!) {
	UserTherapiesCreate(arrayParams:$arrayParams, workflowType:$workflowType)  {
		status
		statusMessage
	  }
  }  
`;
const patientUpdate = gql`
mutation patientUpdate($id:ID!, $params:PatientInput!, $workflowType: WorkflowType!, $isTherapy:Boolean){
	patientUpdate(id:$id, params:$params, workflowType: $workflowType, isTherapy:$isTherapy) {
		status
		statusMessage
	} 
  }
  
`;
const voidUserNote = gql`
mutation voidUserNote($ids:[ID!], $voided:Boolean!, $noteType: noteTypeEnum!){
	voidUserNote(ids:$ids, voided:$voided, noteType: $noteType) {
		status
		statusMessage
	} 
  }
  
`;
const createTherapyRechecked = gql`
mutation CreateTherapyRechecked($params:TherapyRecheckInput! ) {
    CreateTherapyRechecked(params: $params) {
	  status
	  statusMessage
	}
  }
  
`;
const getWorklistCount = gql`
  query recordCounts($filter: [CountFilterModel]) {
    recordCounts(filter: $filter) {
      identifierName
      count
    }
  }
`;
const createChecklist = gql`
  mutation UserCheckListCreate($args: patientCheckListInput!) {
    UserCheckListCreate(args: $args) {
      status
    }
  }
`;
const CreatePatientEdocs = gql`
  mutation CreatePatientEdocs($arrayParams: [EdocsRecords!]) {
    CreatePatientEdocs(arrayParams: $arrayParams) {
      status
      statusMessage
    }
  }
`;
const edocDelete = gql`
  mutation deletePatientEdocs($ids: [ID!]) {
    deletePatientEdocs(ids: $ids) {
      status
      statusMessage
    }
  }
`;
const unlockPatient = gql`
  mutation unallocatePatient($id: [ID!], $workflowType: WorkflowType!, $unlockedBy: Int) {
    unallocatePatient(id: $id, workflowType: $workflowType, unlockedBy: $unlockedBy) {
      status
      statusMessage
    }
  }
`;
@Injectable()
export class IntakeService {
  visible: boolean;
  activePatientDetails = [];
  currentPatient = '';
  timezone;
  menuAppName = 'Citus-Pulse';
  breadcrumbAppName: any;
  systemUrlToken = '';
  categoryWorklistTabs = [];
  worklistMenuPulse = [];
  locationList: any;
  teamList: any;
  siteList: any;
  childWorklistMenu: any;
  parentWorklistMenu: any;
  insuranceList: any;
  therapyList: any;
  moaList: any;
  blogNoteSubject = [];
  pnSubjects = [];
  public blogStaffList = [];
  recheckReasons: any;
  edocList: any;
  enableSwal = false;
  unlockPatients = [];
  rowDataCount = 0;
  cellCount = 0;
  constructor(
    private _http: Http,
    public _structureService: StructureService,
    public _formService: FormsService,
    private apollo: Apollo
  ) {
    this.timezone = configTimeZone();
  }
  getallMasterdata() {
		let dataJson = {
			"resourceType": "Bundle", "type": "searchset", "total": 1,
			"link": [{ "relation": "self", "url": "http:\/\/************:3005\/3_0_1\/list?item=RXTYPE-PAT_STAT-CM-PUMP-PNSUBJECTS-POPUPDATA-SITES" }],
			"entry": [{
				"fullUrl": "http:\/\/localhost:3000\/3_0_1\/List\/undefined",
				"resource": {
					"resourceType": "List", "meta": {
						"versionId": "1",
						"lastUpdated": "2020-09-14T06:18:44.836Z"
					},
					"contained": [{
						"items": {
							"MOA": [{ "id": "1", "name": "test", "deleted": "0" },
							{ "id": "2", "name": "ANE", "deleted": "0" }, { "id": "4", "name": "ASO", "deleted": "0" }],
							"THERAPY": [{ "id": "1", "name": "test", "deleted": "0" },
							{ "id": "2", "name": "ANE", "deleted": "0" },
							{ "id": "4", "name": "ASO", "deleted": "0" }],
							"PAT_STAT": [{ "id": "2", "status": "Active", "deleted": "0" },
							{ "id": "3", "status": "InActive", "deleted": "0" }],
							"CM": [{
								"id": "1", "firstName": "Terry",
								"lastName": "Johnson", "professionalDesignation": "MR", "deleted": "0"
							},
							{
								"id": "2", "firstName": "Peter", "lastName": "Taylor", "professionalDesignation": "HR",
								"deleted": "0"
							}], "PUMP": [{ "id": "1", "name": "Elastomeric", "deleted": "0" },
							{ "id": "2", "name": "ENT%20Gravity", "deleted": "0" },
							{ "id": "3", "name": "ENT%20Infinity", "deleted": "0" }],
							"PNSUBJECTS": [{ "id": "1", "name": "ProgressNote1", "deleted": "0" },
							{ "id": "2", "name": "ProgressNote2", "deleted": "0" },
							{ "id": "3", "name": "ProgressNote3", "deleted": "0" }],
							"POPUPDATA": [{ "id": "1", "name": "Team", "value": "Carex", "code": "CX", "deleted": "0" },
							{ "id": "4", "name": "ENTTEAM", "value": "Genex", "code": "", "deleted": "0" }],
							"SITES": [{ "id": "1", "name": "596e092ab28cc", "deleted": "0" },
							{ "id": "2", "name": "596e03ab28cc", "deleted": "0" }]
						},
						"resourceType": "List", "id": "a9fcea7c-fcdf-4d17-a5e"
					}]
				},
				"search": { "mode": "include" }
			}]
		}

		let myJSON = JSON.stringify(dataJson);
		let getallMasterdataAPI = this._structureService.apiBaseUrl + 'citus-health/v4/auto-sync-pulse-masterdata.php?data=' + myJSON;
		let headers = new Headers();
		headers.append('Content-Type', 'application/x-www-form-urlencoded');
		let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve) => {
			this._http.get(getallMasterdataAPI, options)
				.toPromise()
				.then(
					res => {
						resolve(res);
					}
				);
		});
		return promise;
	}
	checkIntegraiondata(data) {
		const headers = {
			"Authentication-Token": this._structureService.getCookie('authID'),
			"Access-Control-Allow-Origin": "*"
		}
		let integrationApi = this._structureService.apiBaseUrl + 'citus-health/v4/nelc/check-integration-setting.php';
		const apiConfig = { url: integrationApi, method: 'POST', data: data, headers: headers, requestType: 'http' };
		return this._structureService.requestData(apiConfig);
	}
	integraiondata(data) {
		let headers = {
			"Authentication-Token": this._structureService.getCookie('authID'),
			"Access-Control-Allow-Origin": "*"
		}
		let integrationDataApi = this._structureService.apiBaseUrl + 'citus-health/v4/nelc/integration-manager.php';
		const apiConfig = { url: integrationDataApi, method: 'POST', data: data, headers: headers, requestType: 'http' };
		return this._structureService.requestData(apiConfig);
	}
	getLocationList() {
		if (!this.locationList) {
			const apiConfig = {
				method: 'GET',
				data: getAllLocation,
				requestType: 'gql',
				use: 'pulseWorklistGraphApi',
				nested: false,
				noLoader: true
			};
			this.locationList = this._structureService.requestData(apiConfig);
		}
		return this.locationList;
	}
	getInsuranceList() {
		let tenantId=[];
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
			){
            tenantId.push(this._structureService.getCookie('crossTenantId'));
			}
			else{
			tenantId.push(this._structureService.getCookie('tenantId'))	;
			}
		if (!this.insuranceList) {
			const apiConfig = {
				method: 'GET',
				data: getInsuranceList,
				variables: { tenantIds: tenantId },
				requestType: 'gql',
				use: 'pulseWorklistGraphApi',
				nested: false,
				noLoader: true
			};
			this.insuranceList = this._structureService.requestData(apiConfig);
		}
		return this.insuranceList;
	}
  getTeamList() {
    let tenantId = [];
    if (
      this._structureService.getCookie('crossTenantId') &&
      this._structureService.getCookie('crossTenantId') !== 'undefined' &&
      this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
    ) {
      tenantId.push(this._structureService.getCookie('crossTenantId'));
    } else {
      tenantId.push(this._structureService.getCookie('tenantId'));
    }
    if (!this.teamList) {
      const apiConfig = {
        method: 'GET',
        data: getTeams,
        variables: { tenantIds: tenantId },
        requestType: 'gql',
        use: 'pulseWorklistGraphApi',
        nested: false,
        noLoader: true
      };
      this.teamList = this._structureService.requestData(apiConfig);
    }
    return this.teamList;
	}
	getSiteList() {
		let tenantId=[];
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
			){
            tenantId.push(this._structureService.getCookie('crossTenantId'));
			}
			else{
			tenantId.push(this._structureService.getCookie('tenantId'))	;
			}
		if (!this.siteList) {
			const apiConfig = {
				method: 'GET',
				data: getSites,
				variables: { tenantIds: tenantId },
				requestType: 'gql',
				use: 'pulseWorklistGraphApi',
				nested: false,
				noLoader: true
			};
			this.siteList = this._structureService.requestData(apiConfig);
		}
		return this.siteList;
	}
	getAllAppNames() {
		let variables: any = {
			sessionId: this._structureService.getCookie('authenticationToken'),
			crossTenantId: this._structureService.getCookie('crossTenantId')
		};
		const apiConfig = {
			method: 'GET',
			data: getAllAppNames,
			variables: variables,
			requestType: 'gql',
			use: 'signatureRequestApi',
			nested: false,
			noLoader: true
		};
		return this._structureService.requestData(apiConfig);
	}
	getTherapies() {
		let tenantId=[];
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
			){
            tenantId.push(this._structureService.getCookie('crossTenantId'));
			}
			else{
			tenantId.push(this._structureService.getCookie('tenantId'))	;
			}
		const apiConfig = {
			method: 'GET',
			data: getTherapiesList,
			variables: { tenantIds: tenantId },
			requestType: 'gql',
			use: 'pulseWorklistGraphApi',
			nested: false,
			noLoader: true
		};
		return this._structureService.requestData(apiConfig);
	}
	getRecheckReasons() {
		const apiConfig = {
			method: 'GET',
			data: getRecheckReasonsList,
			requestType: 'gql',
			use: 'pulseWorklistGraphApi',
			nested: false,
			noLoader: true
		};
		return this._structureService.requestData(apiConfig);
	}
	getMoas() {
		let tenantId=[];
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
			){
            tenantId.push(this._structureService.getCookie('crossTenantId'));
			}
			else{
			tenantId.push(this._structureService.getCookie('tenantId'))	;
			}
		const apiConfig = {
			method: 'GET',
			data: getMoasList,
			variables: { tenantIds: tenantId },
			requestType: 'gql',
			use: 'pulseWorklistGraphApi',
			nested: false,
			noLoader: true
		};
		return this._structureService.requestData(apiConfig);
	}
	getPatientStatusData(){
	let tenantId=[];
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
			){
           tenantId.push(this._structureService.getCookie('crossTenantId'));
			}
			else{
			tenantId.push(this._structureService.getCookie('tenantId'))	;
			}
		const apiConfig = {
			method: 'GET',
			data: getAllPatientStatus ,
			variables: { tenantIds: tenantId },
			requestType: 'gql',
			use: 'pulseWorklistGraphApi',
			nested: false,
			noLoader: true
		};
		return this._structureService.requestData(apiConfig);
	}
	blogSubjects() {
		let tenantId=[];
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
			){
            tenantId.push(this._structureService.getCookie('crossTenantId'));
			}
			else{
			tenantId.push(this._structureService.getCookie('tenantId'))	;
			}
		const apiConfig = {
			method: 'GET',
			data: blogSubjects,
			variables: { tenantIds: tenantId },
			requestType: 'gql',
			use: 'pulseWorklistGraphApi',
			nested: false,
			noLoader: true
		};
		return this._structureService.requestData(apiConfig);
	}
	progressNoteSubject() {
		let tenantId=[];
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
			){
            tenantId.push(this._structureService.getCookie('crossTenantId'));
			}
			else{
			tenantId.push(this._structureService.getCookie('tenantId'))	;
			}
		const apiConfig = {
			method: 'GET',
			data: progressNoteSubject,
			variables: { tenantIds: tenantId },
			requestType: 'gql',
			use: 'pulseWorklistGraphApi',
			nested: false,
			noLoader: true
		};
		return this._structureService.requestData(apiConfig);
	}
	getWorklistCount(filter) {
		const apiConfig = {
			method: 'GET',
			data: getWorklistCount,
			variables: { filter: filter },
			requestType: 'gql',
			use: 'pulseWorklistGraphApi',
			nested: false,
			noLoader: true
		};
		return this._structureService.requestData(apiConfig);
	}
	getWorklistMenuPulse(guid, parentWorklistId): any {
		const userDataConfig = JSON.parse(this._structureService.userDataConfig);
		if (!this.parentWorklistMenu) {
			this.worklistMenuPulse = [];
			let workListAppMenu = [];
			this.parentWorklistMenu = this.getWorkListsPulse(guid, parentWorklistId).then((data) => {
				let worklistGroupIds = [];
				let workListItems = this.getWorkListItems(data);
				workListItems = this.sortWorkListItems(workListItems);				
				workListItems.forEach((element) => {
					if (element.description) {
						let newJson = element.description;
						newJson = newJson.replace(/'/g, '"');
						const metaData = JSON.parse(newJson);
						let actionLink = metaData.nameOfDesktopMenu.replace(/[^a-zA-Z ]/g, ' ').trim();
						actionLink = actionLink.replace(/\s+/g, '-').toLowerCase();
						if (
							
							element.active &&
							metaData.showInMenu
						) {
							const workListLink = (!isBlank(userDataConfig.enable_refill_dashboard) && userDataConfig.enable_refill_dashboard === '1')
							|| (!isBlank(userDataConfig.enable_app_center_left_menu) && userDataConfig.enable_app_center_left_menu === '1')							  
							? `worklist/${actionLink}/${element.id}`
							: `apps/${guid}/${actionLink}/${element.id}`;
							workListAppMenu.push({
								worklistId: element.id,
								workListName: element.name,
								menuGroupId: element.desktopMenu.menuGroup.id,
								menuTitle: element.desktopMenu.menuName,
								actionLink: workListLink,
								menuIcon: metaData.menuIcon
							});
							worklistGroupIds.push({ id: element.desktopMenu.menuGroup.id });

						}
					}
				});
				if (workListAppMenu.length > 0) {

					return this.getWorklistMenuGroupPulse(worklistGroupIds).then((group) => {
						this.worklistMenuPulse = [];
						let groups = JSON.parse(JSON.stringify(group['getSessionTenant'].formWorklistMenuGroup));
						groups.forEach((element) => {
							this.worklistMenuPulse.push({
								name: element.name,
								worklists: workListAppMenu.filter((x) => x.menuGroupId == element.id)
							});
						});
						return this.worklistMenuPulse;
					});
				} else {
					return this.worklistMenuPulse;
				}
			});
		}
		return this.parentWorklistMenu;
	}
	getWorklistMenuGroupPulse(params = null) {
		let worklistIdParams = Array.from(new Set(params.map((obj) => +obj.id)));		
		let variables: any = {
			sessionToken: this._structureService.getCookie('authID'),
			ids: worklistIdParams
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
			method: 'GET',
			data: this.getWorklistMenuGroupQueryPulse(),
			variables: variables,
			requestType: 'gql',
			use: '',
			noLoader: true
		};		
		return this._structureService.requestData(apiConfig);
	}
	getWorklistMenuGroupQueryPulse() {
		let menuGroup = `query getSessionTenant(
      $sessionToken: String!
			$ids:[Int]`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			menuGroup =
				menuGroup +
				`,
        $crossTenantId : Int`;
		}
		menuGroup =
			menuGroup +
			`){
        getSessionTenant(sessionToken:$sessionToken`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			menuGroup =
				menuGroup +
				`,
        crossTenantId:$crossTenantId`;
		}
		menuGroup =
			menuGroup +
			`){
        formWorklistMenuGroup(ids:$ids){
          id
          tenantId
          name
          createdBy
          createdOn
        }
        }
    }`;
		return gql`${menuGroup}`;
	}
	getChildWorklistQueryPulse() {
		let worklists = `query getWorklistMenu($appGuid:String,$parentWorklistId:Int!){
			getWorklistMenu(appGuid:$appGuid,parentWorklistId:$parentWorklistId){
			  worklistid
			  worklistName
			  childWorklist {
				  worklistId
				  workListName
				  menuGroupId
				  metaData
				  menuGroupName
				  active
			  }
		  }
		}
		`;
			  return gql`${worklists}`;
	}
	getWorklistsQueryPulse() {
		let worklists = `query getSessionTenant(
      $sessionToken: String!
      $appGuid:String
      $parentWorklistId:String`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			worklists =
				worklists +
				`,
      $crossTenantId : Int`;
		}
		worklists =
			worklists +
			`){
      getSessionTenant(sessionToken:$sessionToken`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			!isBlank(this._structureService.getCookie('crossTenantId')) &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			worklists =
				worklists +
				`,
      crossTenantId:$crossTenantId`;
		}
		worklists =
			worklists +
			`){
      formWorklists(appGuid:$appGuid,parentWorklistId:$parentWorklistId){
        id
        name
        description
        active
        desktopMenu{
          menuName
          menuGroup{
            id
            name
            tenantId
          }
          menuIndex
        }
        reportForms{
          id
          name
        }
        statusForms{
          id
          name
        }
        
      }
    }
  }
  `;
		return gql`${worklists}`;
	}
	getWorkListsPulse(guid, parentWorklistId) {
		let variables: any = {
			sessionToken: this._structureService.getCookie('authenticationToken'),
			appGuid: guid,
			parentWorklistId: parentWorklistId
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
			method: 'GET',
			data: this.getWorklistsQueryPulse(),
			variables: variables,
			requestType: 'gql',
			use: '',
			nested: false,
			noLoader: true
		};
		return this._structureService.requestData(apiConfig);
	}

	getWorklistCategoryMenuList(guid = null, parentWorklistId): any {
		let variables: any = {
			appGuid: guid,
			parentWorklistId: parentWorklistId
		};
		const apiConfig = {
			method: 'GET',
			data: this.getChildWorklistQueryPulse(),
			variables: variables,
			requestType: 'gql',
			use: 'pulseWorklistGraphApi',
			nested: false,
			noLoader: true
		};
		return this._structureService.requestData(apiConfig);
	}
	getWorklistCategoryTabList(guid = null, parentWorklistId): any {

		return this.getWorkListsPulse(guid, parentWorklistId).then((data) => {
			let workLists = [];
			let categoryWorklist = [];
			let worklistGroupIds = [];

			if (data['getSessionTenant'] && data['getSessionTenant'].formWorklists) {
				workLists = JSON.parse(JSON.stringify(data['getSessionTenant'].formWorklists));
			}

			workLists.sort(function (a, b) {
				if (a.desktopMenu.menuIndex < b.desktopMenu.menuIndex) {
					return -1;
				}
				if (a.desktopMenu.menuIndex > b.desktopMenu.menuIndex) {
					return 1;
				}
				return 0;
			});

			workLists.forEach((element) => {
				if (element.description) {
					let newJson = element.description;
					newJson = newJson.replace(/'/g, '"');
					const metaData = JSON.parse(newJson);
					let actionLink = metaData.nameOfDesktopMenu.replace(/[^a-zA-Z ]/g, ' ').trim();
					actionLink = actionLink.replace(/\s+/g, '-').toLowerCase();
					const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
					if (
						((metaData.visibleToRoles &&
							metaData.visibleToRoles.split(',').indexOf(userDetails.roleId) !== -1) ||
							(metaData.allowCrossTenant == true &&
								metaData.visibleToOtherRoles &&
								metaData.visibleToOtherRoles.split(',').indexOf(userDetails.roleId) !== -1)) &&
						element.active &&
						metaData.showInMenu !== true
					) {
						categoryWorklist.push({
							worklistId: element.id,
							workListName: element.name,
							menuGroupId: element.desktopMenu.menuGroup.id,
							menuTitle: element.desktopMenu.menuName,
							actionLink: `worklist/${actionLink}/${element.id}`,
							menuIcon: metaData.menuIcon,
							nameOfDesktopMenu: metaData.nameOfDesktopMenu,
							showTab: metaData.showTab,
							statusCallback: metaData.enableStatusIndication == true ? metaData.statusIndicationCallback : ''
						});
						worklistGroupIds.push({ id: element.desktopMenu.menuGroup.id });
					}
				}
			});
			if (categoryWorklist.length > 0) {
				let categoryTabList = [];
				let childWorklist: any = [];
				return this.getWorklistMenuGroupPulse(worklistGroupIds).then((group) => {
					let groups = JSON.parse(JSON.stringify(group['getSessionTenant'].formWorklistMenuGroup));
					groups.forEach((element) => {

						let groupRelatedWorkList = categoryWorklist.filter((x) => x.menuGroupId == element.id && (x.showTab == false || x.showTab == undefined));
						if (groupRelatedWorkList.length > 0) {
							categoryTabList.push({
								"name": null,
								"worklists": categoryWorklist.filter((x) => x.menuGroupId == element.id && (x.showTab == false || x.showTab == undefined))
							});
						}
						let groupRelatedWorkList2 = categoryWorklist.filter((x) => x.menuGroupId == element.id && x.showTab == true);
						if (groupRelatedWorkList2.length > 0) {
							categoryTabList.push({
								"name": element.name,
								"worklists": categoryWorklist.filter((x) => x.menuGroupId == element.id && x.showTab == true)
							});
						}



					});
					console.log(categoryTabList)
					return categoryTabList;
				});
			}

			return [];
		});

	}

	updateLiaisonEntryDetails(data) {
		let userData = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
		let dataTypes = data.values;
		dataTypes['modifiedBy'] = userData.userId;
		return this.apollo.use('pulseWorklistGraphApi').mutate<any>({
			mutation: updateLiaisonEntryData,
			variables: {
				params: dataTypes, id: data.id,
				workflowType: data.workflowType
			}
		}).map(
			res => res
		);

	}

	addNote(noteData) {
		return this.apollo.use('pulseWorklistGraphApi').mutate<any>({
			mutation: addNote,
			variables: { input: noteData }
		}).map(
			res => res
		);
	}

	editNote(id,noteData) {
		return this.apollo.use('pulseWorklistGraphApi').mutate<any>({
			mutation: editNote,
			variables: {id:id, params: noteData }
		}).map(
			res => res
		);
	}
	deleteTherapyData(data) {
		return this.apollo.use('pulseWorklistGraphApi').mutate<any>({
			mutation: UserTherapiesDelete,
			variables: { ids: data }
		}).map(
			res => res
		);
	}
	createTherapyData(therapyData) {
		return this.apollo.use('pulseWorklistGraphApi').mutate<any>({
			mutation: createTherapies,
			variables: { arrayParams: therapyData.dataset, workflowType: therapyData.workflowType }
		}).map(
			res => res
		);
	}
	voidUserNote(params) {
		return this.apollo.use('pulseWorklistGraphApi').mutate<any>({
			mutation: voidUserNote,
			variables: { ids: params.ids,voided: params.voided, noteType: params.noteType }
		}).map(
			res => res
		);
	}

	updatePatientData(patientData) {
		let userData = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
		let dataTypes = patientData.params;
		dataTypes['modifiedBy'] = userData.userId;
		return this.apollo.use('pulseWorklistGraphApi').mutate<any>({
			mutation: patientUpdate,
			variables: {
				params: dataTypes, id: patientData.id,
				isTherapy: patientData.isTherapy, workflowType: patientData.workflowType
			}
		}).map(
			res => res
		);
	}
	updateTherapyRecheckData(recheckData) {
		return this.apollo.use('pulseWorklistGraphApi').mutate<any>({
			mutation: createTherapyRechecked,
			variables: { params: recheckData }
		}).map(
			res => res
		);
	}
	CreatePatientEdocs(edocData) {
		return this.apollo.use('pulseWorklistGraphApi').mutate<any>({
			mutation: CreatePatientEdocs,
			variables: { arrayParams: edocData }
		}).map(
			res => res
		);
	}
	deleteEdocData(data) {
		return this.apollo.use('pulseWorklistGraphApi').mutate<any>({
			mutation: edocDelete,
			variables: { ids: data }
		}).map(
			res => res
		);
	}
	createChecklist(variable) {
		return this.apollo.use('pulseWorklistGraphApi').mutate<any>({
			mutation: createChecklist,
			variables: { args: variable }
		}).map(
			res => res
		);
	}
	getIntegrationRequestIds(variable) {
		const apiConfig = {
			method: 'GET',
			data: getIntegrationRequestIds,
			variables: { id: variable.id, mrn: variable.mrn, noteType: variable.noteType },
			requestType: 'gql',
			use: 'pulseWorklistGraphApi',
			nested: false,
		};
		return this._structureService.requestData(apiConfig);
	}
	getDocList() {
		let tenantId=[];
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
			){
            tenantId.push(this._structureService.getCookie('crossTenantId'));
			}
			else{
			tenantId.push(this._structureService.getCookie('tenantId'))	;
			}
		const apiConfig = {
			method: 'GET',
			data: getDocList,
			variables: { tenantIds: tenantId },
			requestType: 'gql',
			use: 'pulseWorklistGraphApi',
			nested: false,
			noLoader: true
		};
		return this._structureService.requestData(apiConfig);
	}
	unlockPatient(variable) {
		return this.apollo.use('pulseWorklistGraphApi').mutate<any>({
			mutation: unlockPatient,
			variables: variable
		  }).map(
			res => res
			);
	}
	getStaffIdUsers(variable) {
		const apiConfig = {
			method: 'GET',
			data: getStaffIdUsers,
			variables: variable,
			requestType: 'gql',
			use: 'pulseWorklistGraphApi',
			nested: false,
			noLoader: true
		};
		return this._structureService.requestData(apiConfig);
	}
	getActiveStaffListQuery(siteIds = "") {
		let Staffs = `query staffUsers($limit:Int, $offset: Int!,$orderData:String!,$orderby: String!,$searchText: String!,$startDate:String,$endDate:String,$needReal:Boolean,$status: String`;
		if(siteIds){
			Staffs = Staffs +`,
			$siteIds : String`;
		}	
		Staffs = Staffs + `){`;
		Staffs = Staffs + ` staffUsers(limit:$limit,offset:$offset,orderData:$orderData,orderby:$orderby,searchText:$searchText,startDate:$startDate,endDate:$endDate,needReal:$needReal,status:$status`;
		if (siteIds) {
			Staffs = Staffs + `,
		 siteIds:$siteIds`;
		}

		Staffs = Staffs + `){
		   emails {
			   value
			   type
			   primary
		   }
		   displayName,
		   lastName,
		   firstName,
		   status,
		   id
		 }  
		}`;
		return gql`${Staffs}`;
	}
	getActiveStaffList(siteIds="") {
		let variables: any = { sessionToken: this._structureService.getCookie('authenticationToken'), 
		limit: 0, offset: 0, searchText: '', orderData: "displayName",orderby: "ASC", 
		startDate: "", endDate: "", needReal: false, status: 'Active' };

		if (siteIds != "") {
			variables.siteIds = siteIds;
		}
		let apiConfig = {
			method: 'GET',
			data: this.getActiveStaffListQuery(siteIds),
			variables: variables,
			requestType: 'gql',
			use: "commonGraphqlClient",
			count: 0
		};
		return this._structureService.requestData(apiConfig);
	}	
	private getWorkListItems(data) {
		let workListItems = [];
		if (!isBlank(data.getSessionTenant) && data.getSessionTenant.formWorklists) {
			workListItems = JSON.parse(JSON.stringify(data['getSessionTenant'].formWorklists));
		}
		return workListItems;
	}
	
	private sortWorkListItems(workListItems) {
		return workListItems.sort((a, b) => a.desktopMenu.menuIndex - b.desktopMenu.menuIndex);
	}
}
