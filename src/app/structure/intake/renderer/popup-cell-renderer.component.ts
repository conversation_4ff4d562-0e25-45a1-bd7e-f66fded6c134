import { Component } from '@angular/core';
import { INoRowsOverlayAngularComp } from 'ag-grid-angular';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../../structure.service';
import { IntakeService } from '../intake.service';
declare var $: any;
declare var moment: any;
var momentTz = require('moment-timezone');
var jstz = require('jstz');
const timezone = jstz.determine();
import { HostListener } from '@angular/core';
// <i class="copy-ico fa fa-files-o" id="int-copy-sent" aria-hidden="true"></i>
@Component({
    selector: 'app-popup-cell-renderer',
    template: `
    <div *ngIf="badge == true">
            <span class="badge badge-success" *ngIf="params.value == 'Completed'">
                {{params.value}}
            </span> <i class= "icmn-info popup" style="color:#5cb85c;cursor:pointer;" *ngIf="params.value == 'Completed' && params.value != '' && params.value != null" (click)="showIntegrationStatusDetails(params)" data-toggle="popover-status" id="popover-status{{params.rowIndex}}" data-container="body" data-trigger="focus" data-animation="false" data-placement="left" type="" data-html="true"></i>
            <span class="badge badge-warning" *ngIf="params.value == 'Pending'">
                {{params.value}}
            </span> <i class= "icmn-info popup" style="color: #f39834;cursor:pointer;" *ngIf="params.value == 'Pending' && params.value != '' && params.value != null" (click)="showIntegrationStatusDetails(params)" data-toggle="popover-status" id="popover-status{{params.rowIndex}}" data-container="body" data-trigger="focus" data-animation="false" data-placement="left" type="" data-html="true"></i>
            <span class="badge badge-danger" *ngIf="params.value == 'Failed'">
                {{params.value}}
            </span> <i class= "icmn-info popup" style="color: #d9534f;cursor:pointer;" *ngIf="params.value == 'Failed' && params.value != '' && params.value != null" (click)="showIntegrationStatusDetails(params)" data-toggle="popover-status" id="popover-status{{params.rowIndex}}" data-container="body" data-trigger="focus" data-animation="false" data-placement="left" type="" data-html="true"></i>
        
        <div id="popover-content-status{{params.rowIndex}}" class="hide">
            <div class="button-block">
                <button type="button" id="close-popover" class="close detail-modal-close" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                
            </div>
            <div class="content-block" id="copy-content-{{params.rowIndex}}">
                <span *ngIf = "responseStatus && responseStatus.length == 1 && responseStatus[0].integration_status"> <strong>Status:</strong> {{responseStatus[0]?.integration_status}} </span><br> 
                <span *ngIf = "responseStatus && responseStatus.length == 1 && responseStatus[0].reference_id"> <strong>Reference Id:</strong> {{responseStatus[0]?.reference_id}}</span> <br>
                <span *ngIf = "responseStatus && responseStatus.length == 1 && responseStatus[0].processedAt"> <strong>Processed At:</strong> {{responseStatus[0]?.processedAt}} </span>
                <table *ngIf = "responseStatus && responseStatus.length > 1">
                    <thead>
                        <th>Reference Id</th>
                        <th>Status</th>
                        <th>Processed At</th>
                    </thead>
                    <tbody>
                        <tr *ngFor="let res of responseStatus">
                            <td>{{res?.reference_id}}</td>
                            <td>{{res?.integration_status}}</td>
                            <td>{{res?.processedAt}}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <span style="cursor:pointer;" *ngIf="badge == false">
        <span>{{params.value}}</span>
    </span>
    `
})

export class PopupCellRenderer implements INoRowsOverlayAngularComp {

    public params;
    public responseStatus;
    public contentDetails;
    public badge;
    public showResponse;
    public currentRowId;
    constructor(
        private route: ActivatedRoute,
        private router: Router,
        public _structureService: StructureService,
        public _intakeService: IntakeService
	) {
    }
    agInit(params): void {
        this.params = params;
        this.badge = params.contentDetails['textBanner'];
        let this1= this;
        $(function(){
            $(document).on('click',"#close-popover",function () {
                $('[data-toggle="popover-status"]').popover('hide');
            });
            $(document).on('click',"#int-copy-sent",function () {
                this1.CopyToClipboard();
            });
        });
    }
    @HostListener('click', ['$event'])
    onClick(event) {
        console.log(event);
        if ($(event.target).data('toggle') !== 'popover-status') { 
            $('[data-toggle="popover-status"]').popover('hide');
        }
       
    }
    showIntegrationStatusDetails(params) {
        console.log('params', params);
        this.responseStatus = [];
        this.showResponse = false;
        this.currentRowId = params.rowIndex;
        console.log(this.currentRowId);
        localStorage.setItem('currentRowId', this.currentRowId);
        const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
        var getData = {
            "reference_id": params.data['requestId']
        }
        this._structureService.getIntegration(getData).then((res) => {
            console.log(res);
            if (res && res['response'].integration_status) {
                this.responseStatus[0] = res['response'];
                console.log(this.responseStatus);
                this.showResponse = true;
                $(".popup").popover('hide');
                setTimeout(() => {
                    $("#popover-status" + params.rowIndex).popover({
                        html: true,
                        content: function () {
                            return $('#popover-content-status' + params.rowIndex).html();
                        }
                    });
                    $("#popover-status" + params.rowIndex).popover('show');
                    $(".popover").addClass('popover-class');
                }, 100);
            }
        });

    }
   
    CopyToClipboard() {
        let para = $('#copy-content-' + localStorage.getItem('currentRowId') )[0] as HTMLInputElement;
        console.log(para);
        this.selectElementText(para);
        if (window.getSelection) { // all modern browsers and IE9+
            let selectedText = window.getSelection().toString()
            console.log(selectedText);
            const el = document.createElement('textarea');
            el.value = selectedText;
            document.body.appendChild(el);
            el.select();
            document.execCommand('copy');
            document.body.removeChild(el);
            this.selectElementText(para);
        }
    }
    selectElementText(el) {
        var range = document.createRange() // create new range object
        range.selectNodeContents(el) // set range to encompass desired element text
        console.log(range);
        var selection = window.getSelection() // get Selection object from currently user selected text
        selection.removeAllRanges() // unselect any user selected text (if any)
        selection.addRange(range) // add range to Selection object to select it
    }
}
