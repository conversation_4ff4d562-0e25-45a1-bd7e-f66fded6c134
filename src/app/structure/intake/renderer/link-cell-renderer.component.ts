import { Component } from '@angular/core';
import { INoRowsOverlayAngularComp } from 'ag-grid-angular';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { IntakeService } from '../intake.service';
declare var $: any;
declare var moment: any;
var momentTz = require('moment-timezone');
var jstz = require('jstz');
const timezone = jstz.determine();

@Component({
    selector: 'app-link-cell-renderer',
    template: `
        <span (click)="onClick($event)" style="cursor:pointer;">
        {{params.value}}
        <i class= "fa fa-lock" (click)="showDetail(1,params.rowIndex)" (mouseover)="showDetail(1,params.rowIndex)" (mouseout)="showDetail(0,params.rowIndex)"  *ngIf="lockConfig == true" data-toggle="popover" id="popover{{params.rowIndex}}" data-container="body" data-trigger="focus" data-animation="false" data-placement="right" type="" data-html="true"></i>
        </span> 
        <div id="popover-content{{params.rowIndex}}" class="hide">
        <div class="row">
         <div class="col-xs-8">
         {{patientName}} is locked for editing by {{lockedBy}} since {{lockedAt}}.
         </div>
    </div>
</div>
    `
})
export class LinkCellRenderer implements INoRowsOverlayAngularComp {

    public params;
    linkDetails;
    userData;
    lockConfig;
    lockedAt;
    lockedBy;
    patientName;
    data;
    workflowType;
    enableLockingMode;
    lockTime;
    constructor(
        private route: ActivatedRoute,
        private router: Router,
        public _intakeService: IntakeService
	) {
    }
    agInit(params): void {
        this.params = params; 
        this.linkDetails = params.linkDetails;
        this.workflowType = params.workflowType;
        this.enableLockingMode = params.enableLockingMode;
        this.lockTime = params.lockTime;
        this.userData = params.userData;
        this._intakeService.cellCount++;
        if(this._intakeService.cellCount == 1) {
            this._intakeService.unlockPatients = [];
        }
        if(this.enableLockingMode == true) {
            if(params.data) {
                if(params.data['lockedAt'] == null && params.data['patientLock'] == true) {
                    let index = this._intakeService.unlockPatients.indexOf(params.data['id']);
                    if(index == -1) {
                        this._intakeService.unlockPatients.push(params.data['id']);
                    }
                } else {
                    this.lockConfig = params.data['patientLock'] ? params.data['patientLock'] : false;
                }
                const zone = timezone.name();
                this.lockedAt = momentTz.tz(params.data['lockedAt'], zone).format('MM/DD/YYYY h:mm:ss A');
                this.lockedBy = params.data['lockedByUser'];
                this.patientName = params.data['name'];
                let dt1 = params.data['lockedAt'];
                if(dt1 != null && params.data['patientLock'] == true){
                    let dt2 = new Date().getTime();
                    var diff =(dt2 - dt1) / 1000;
                    diff /= 60;
                    let minDiff = Math.abs(Math.round(diff));
                    if(minDiff > this.lockTime) {
                        this.lockConfig = false;
                        params.data['patientLock'] = false;
                        let index = this._intakeService.unlockPatients.indexOf(params.data['id']);
                        if(index == -1) {
                            this._intakeService.unlockPatients.push(params.data['id']);
                        }
                    }
                }
            }
            if(this._intakeService.rowDataCount == this._intakeService.cellCount){
                if(this._intakeService.unlockPatients.length > 0) {
                    this.unlockPatient();
                }
            }
        }
    }
    showDetail(action,rowId){
        if(action == 1) {
            $("#popover"+rowId).popover({
                html: true, 
                content: function() {
                  return $('#popover-content'+rowId).html();
                }
            });
            $("#popover"+rowId).popover('show');
        } else {
            $("#popover"+rowId).popover('hide');
        }
    }
    onClick($event) {
        if (this.linkDetails.onClick instanceof Function ) {
            const params = {
                event: $event,
                rowData: this.params.node.data,
                actionField: this.linkDetails.actionFields
            };
            let actionFields =[];
            let privileges = this.params.privileges;
            if (
                (this.linkDetails.actionPrivileges != '' && privileges.indexOf(this.linkDetails.actionPrivileges) != -1) ||
                this.linkDetails.actionPrivileges == '' ||
                typeof this.linkDetails.actionPrivileges == 'undefined'
            ) {
                if (this.linkDetails.actionField && this.linkDetails.actionField != '') {
                    actionFields.push({ associatedField: this.linkDetails.actionField, fieldValues: this.linkDetails.fieldValues });
                } else if (this.linkDetails.actionFields) {
                    actionFields = this.linkDetails.actionFields;
                }

                if (actionFields.length > 0) {
                    let loginUserId = this.userData.userId;
                    let loginDisplayName = this.userData.displayName;
                    let trueCount = 0;
                    actionFields.forEach((field) => {
                        if (this.params.node.data.hasOwnProperty(field.associatedField)) {
                            if (
                                field.fieldValues &&
                                field.fieldValues
                                    .split(',')
                                    .findIndex((x) => x.trim().toUpperCase() == this.params.node.data[field.associatedField].toString().trim().toUpperCase()) != -1
                            ) {
                                if (this.linkDetails.showOnlyLoginUser == true) {
                                    if (
                                        this.params.node.data[this.linkDetails.loginUserMatchField] == loginUserId ||
                                        this.params.node.data[this.linkDetails.loginUserMatchField] == loginDisplayName
                                    ) {
                                        trueCount++;
                                    }
                                } else {
                                    trueCount++;
                                }
                            } 
                        }
                        if (field.fieldValues && field.fieldValues == 'Any') {
                            if (this.params.node.data[field.associatedField]) {
                                trueCount++;
                            }
                        }
                        if (field.fieldValues && field.fieldValues == 'not_null') {
                            if (this.params.node.data[field.associatedField] != '') {
                                trueCount++;
                            }
                        }
                    });
                    if(actionFields.length == trueCount) {
                        this.linkDetails.disable = false;
                    } else {
                        this.linkDetails.disable = true;
                    }
                } else {
                    this.linkDetails.disable = false;
                }
            }
            if(this.linkDetails.disable == false) {
                //this.linkDetails.onClick(params);
                if(this.linkDetails.callbackfunction == 'editEntry') {
                    this.editDetails(this.params.node.data);
                }
            }
        }
    }
    editDetails(e){
        const editRowDetails = e;
        let currentWidget = localStorage.getItem('currentWidget');
        this._intakeService.activePatientDetails = e;
        if(currentWidget != 'All'){
            localStorage.setItem('selectedWidget', currentWidget);
            localStorage.setItem('currentDashboard', this.route.snapshot.paramMap.get('worklistid'));
        }
        this._intakeService.currentPatient = editRowDetails['id'];
		this.router.navigate([
			`apps/${this.route.snapshot.paramMap.get('guid')}/${this.route.snapshot.paramMap.get('worklist')}/details/${this.route.snapshot.paramMap.get('worklistid')}/${editRowDetails['id']}`
		]);
    }
    unlockPatient() {
        let variable = {
            id: this._intakeService.unlockPatients,
            workflowType: this.workflowType
        }
        this._intakeService.unlockPatient(variable).subscribe(({ data: response }) => {
            console.log(response);
        });
    }
}
