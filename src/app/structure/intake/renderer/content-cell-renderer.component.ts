import { Component } from '@angular/core';
import { INoRowsOverlayAngularComp } from 'ag-grid-angular';

declare var swal: any;
@Component({
    selector: 'app-content-cell-renderer',
    template: `
        <span *ngIf="contentType == 'CHECKBOX'" >
            <input type="checkbox" (click)="checkedHandler($event)"
            [checked]="params.value == 1" name="" [disabled]="!allowEdit">
        </span>
        <span *ngIf="contentType == 'SELECTBOX'">
            <select class="form-control" (change)="selectedItem($event)" [disabled]="!allowEdit">
                <option value="">Select</option>
                <option *ngFor="let item of list" [selected]="params.value == item.id" value="{{item.id}}">{{item.itemValue}}</option>
            </select>
        </span>
        <span *ngIf="contentType == 'text'">{{params.value}}</span>
        <span *ngIf="contentType == 'DISABLE-CHECKBOX'" >
            <input type="checkbox" [checked]="params.value == 1" name="" [disabled]="true">
        </span>
        
    `
})
export class ContentCellRenderer implements INoRowsOverlayAngularComp {

    public contentType: string;
    public params: any;
    public list: any;
    public dataSourceField: any;
    public displayTypeField: any;
    public removeContent: any;
    public allowEdit;
    public checklistCallback;
    public workflowId;
    public entryTypeIds;
    public therapyRecheck;
    agInit(params): void {
        this.params = params;
        let rowData = params.data;
        this.dataSourceField = params.contentDetails.dataSourceField;
        this.displayTypeField = params.contentDetails.displayTypeField;
        this.therapyRecheck = params.contentDetails.therapyRecheck;
        this.allowEdit = params.contentDetails.allowEdit;
        this.list = params.data[this.dataSourceField];
        this.removeContent = false;
        this.checklistCallback = params.contentDetails.checklistCallback;
        this.workflowId = params.contentDetails.workflowId;
        this.entryTypeIds = params.contentDetails.entryTypeIds;
        if(this.workflowId == this.entryTypeIds['INITIAL_INS'] && this.checklistCallback == 'insuranceVerification'){
            this.contentType = 'CHECKBOX';
            if(rowData['status'] == 'Completed') {
                this.contentType = 'DISABLE-CHECKBOX';
            }
        } else if(this.workflowId == this.entryTypeIds['INTAKE'] && this.checklistCallback == 'finalInsuranceVerification') {
            if(this.params.column.colId == 'intakePatientEntry') {
                this.contentType = rowData[this.displayTypeField].trim() == 'CHECKBOX' ? 'DISABLE-CHECKBOX' : 'text'; 
            } else {
                this.contentType = 'DISABLE-CHECKBOX';
            }          
        } else if(this.workflowId == this.entryTypeIds['INS_VERIFICATION'] && this.checklistCallback == 'finalInsuranceVerification') {
            if(this.params.column.colId == 'intakePatientEntry') {
                this.contentType = rowData[this.displayTypeField].trim() == 'CHECKBOX' ? 'DISABLE-CHECKBOX' : 'text';  
            } else {
                this.contentType = 'CHECKBOX';
                if(rowData['status'] == 'Completed') {
                    this.contentType = 'DISABLE-CHECKBOX';
                }
            }
        } else if(this.workflowId == this.entryTypeIds['INS_VERIFICATION'] && this.checklistCallback == 'insuranceVerification') {
            this.contentType = rowData[this.displayTypeField].trim() == 'CHECKBOX' ? 'DISABLE-CHECKBOX' : 'text'; 
            params.value = rowData['intakePatientEntry'];

        } else {
            this.contentType = rowData[this.displayTypeField].trim();
            if(rowData['status'] == 'Completed') {
                /**if status is completed then disable checklist */
                this.contentType = rowData[this.displayTypeField].trim() == 'CHECKBOX' ? 'DISABLE-CHECKBOX' : 'text';
                if(rowData[this.displayTypeField].trim() == 'SELECTBOX') {
                    console.log(this.list);
                    let index = this.list.findIndex(x=> x.id == params.value);
                    if(index > -1) {
                        params.value = this.list[index].itemValue;
                    }
                    console.log(params.value);
                }  
            }
        }
        if(params.contentDetails.emptyCellField != '') {
            if(params.data[params.contentDetails.emptyCellField] == params.contentDetails.emptyFieldValue){
                params.value = '';
                this.contentType = 'text';
            }
        }
    }
    checkedHandler(event) {
        let checked = event.target.checked;
        let colId = this.params.column.colId;
        let noneRequiredActive = 0;
        let otherItemActive = 0;
        if( this.checklistCallback == 'insuranceVerificationItems' && this.params.data['status'].toUpperCase() == 'NEW'){
            this.params.api.forEachNode(function(rowNode, index) {
                if( rowNode.data.itemValueComplete == true || rowNode.data.initialInsuranceEntry == true ) {
                    if(rowNode.data.noneRequired == true) {
                        noneRequiredActive = 1;
                    } else {
                        otherItemActive = 1;
                    }
                }
            });
        } else {
            this.params.api.forEachNode(function(rowNode, index) {
                if( rowNode.data.itemValueComplete == true || rowNode.data.itemValueRequired == true ) {
                    if(rowNode.data.noneRequired == true) {
                        noneRequiredActive = 1;
                    } else {
                        otherItemActive = 1;
                    }
                }
            });
        }
        if(this.params.data['noneRequired'] == true) {
            if(otherItemActive == 1) {
                swal(" ", "The 'None Required' must have no other documents selected", "warning");
                this.params.node.setDataValue(colId, false);
                event.target.checked = false;
            } else {
                this.params.node.setDataValue(colId, checked);
            }
        } else {
            if(noneRequiredActive == 1) {
                swal(" ", "The 'None Required' must have no other documents selected", "warning");
                this.params.node.setDataValue(colId, false);
                event.target.checked = false;
            } else {
                if(this.checklistCallback == 'insuranceVerification' && this.workflowId == this.entryTypeIds['INITIAL_INS'] && this.params.data['recheckRequired'] == true){
                    if(Number(this.params.data['itemValueComplete']) === 1 && this.params.data['status'].toUpperCase() != 'NEW' && this.therapyRecheck == true) { 
                        if(checked == false) {
                            swal(" ","A recheck type must be selected", "warning");
                        }
                        this.params.node.setDataValue(colId, 1);
                        event.target.checked = true;
                    } else {
                         this.params.node.setDataValue(colId, checked);
                    }
                } else {
                    this.params.node.setDataValue(colId, checked);
                }
          
            }
        }
    }
    selectedItem(event) {
        let colId = this.params.column.colId;
        this.params.node.setDataValue(colId, event.target.value);
    }
}
