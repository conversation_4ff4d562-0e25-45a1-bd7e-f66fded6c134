// Author: T4professor

import { Component } from '@angular/core';
import { ICellRendererAngularComp } from 'ag-grid-angular';
import { ICellRendererParams, IAfterGuiAttachedParams } from 'ag-grid-community';

@Component({
  selector: 'app-button-renderer',
  templateUrl: './button-render.html',
})

export class ButtonRendererComponent implements ICellRendererAngularComp {

  params;
  label: string;
  singleIconElements = [];
  multipleIconElements = [];
  formField;

  agInit(params): void {
    this.params = params;
    this.label = this.params.label || null;
    this.singleIconElements = this.params.iconElements.filter(x => x.type == 'single');
    this.multipleIconElements = this.params.iconElements.filter(x => x.type == 'multiple');
  }

  refresh(params?: any): boolean {
    return true;
  }

  onClick($event, label) {
    this.singleIconElements.forEach(element => {
        if (element.onClick instanceof Function && element.label === label ) {
            const params = {
              event: $event,
              rowData: this.params.node.data,
              actionField: element.actionField
            };
            element.onClick(params);
          }
    });
    this.multipleIconElements.forEach(element => {
        if (element.onClick instanceof Function) {
            const params = {
              event: $event,
              rowData: this.params.node.data,
              action: $event.target.value,
              actionField: element.actionField
            };
            element.onClick(params);
          }
    });
  }
}
