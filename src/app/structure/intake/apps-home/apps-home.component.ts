import { Component, OnInit, Input} from '@angular/core';
// import { IntakeService} from '../intake.service';
import { StructureService } from 'app/structure/structure.service';
import { Http, Headers, RequestOptions } from '@angular/http';
import { ApolloClient, createNetworkInterface } from 'apollo-client';
import { Apollo } from 'apollo-angular';
import { FormsService } from '../../forms/forms.service';
import { Router, ActivatedRoute, Params, NavigationEnd } from '@angular/router';
import gql from 'graphql-tag';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { configTimeZone } from '../../../../environments/environment';
// import { ConstantService } from '../structure/constants/constant.service';
// import { CommonService } from '../structure/services/common.service';
import { setTimeout } from 'timers';
import * as moment from 'moment-timezone';
import { Observable } from 'rxjs/Observable';
declare var $: any;
declare var jQuery: any;

@Component({
  selector: 'app-apps-home',
  templateUrl: './apps-home.component.html',
  styleUrls: ['./apps-home.component.css']
})
export class AppsHomeComponent implements OnInit {
  

  visible: boolean;
  timezone;
  systemUrlToken = '';
  constructor(
    private _http: Http,
    private router: Router,
    public _structureService: StructureService,
    public _formService: FormsService,
    private apollo: Apollo
  ) {
    this.timezone = configTimeZone();
    this._structureService.displayProgress.emit(false);
  }
  

  ngOnInit(){
  

  }
}