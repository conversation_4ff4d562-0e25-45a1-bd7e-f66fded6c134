import { EventEmitter } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Apollo, ApolloModule } from 'apollo-angular';
import { FormsService } from 'app/structure/forms/forms.service';
import { AuthService } from 'app/structure/shared/auth.service';
import { SharedService } from 'app/structure/shared/sharedServices';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { provideClients } from 'test-utils';

import { AppsHomeComponent } from './apps-home.component';

describe('AppsHomeComponent', () => {
  let component: AppsHomeComponent;
  let fixture: ComponentFixture<AppsHomeComponent>;
  let formservice: FormsService;
  let structservice: StructureService;
  beforeEach(async(() => {
    structservice = jasmine.createSpyObj('StructureService', [
      'displayProgress',
    ]);
    structservice.displayProgress = new EventEmitter<any>();
    TestBed.configureTestingModule({
      providers: [
        { provide: StructureService, useValue: structservice },
        Apollo,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        TranslateService,
        { provide: FormsService, useValue: formservice },
      ],
      declarations: [AppsHomeComponent],
      imports: [
        HttpModule,
        RouterTestingModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF,
        }),
        TranslateModule.forRoot(),
      ],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AppsHomeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
});
