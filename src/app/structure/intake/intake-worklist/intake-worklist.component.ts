import { Component, ViewChild, OnInit, OnDestroy, Output, EventEmitter, Input } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ButtonRendererComponent } from '../renderer/button-renderer.component';
import { GroupRowInnerRenderer } from '../renderer/group-row-inner-renderer.component';
import { IconCellRenderer } from '../renderer/icon-cell-renderer.component';
import { LinkCellRenderer } from '../renderer/link-cell-renderer.component';
import { IntakeWorkListService } from '../intake-worklist/intake-worklist.service';
import { FormGroup, FormControl, FormBuilder, Validators } from '@angular/forms';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../../structure.service';
import { InboxService } from '../../../structure/inbox/inbox.service';
import { userFilterPipe } from '../../../structure/inbox/inbox-modal-filter';
import { SharedService } from '../../../structure/shared/sharedServices';
import { DynamicService } from './dynamic-form/dynamic.service';
import { DropdownQuestion } from './dynamic-form/dynamic-dropdown';
import { DynamicBase } from './dynamic-form/dynamic-base';
import { TextboxQuestion } from './dynamic-form/dynamic-textbox';
import { CheckboxQuestion } from './dynamic-form/dynamic-checkbox';
import { RadioQuestion } from './dynamic-form/dynamic-radio';
import { IMyDpOptions, IMyDateModel } from 'mydatepicker';
import { ToastComponent } from '@syncfusion/ej2-angular-notifications';
import {LicenseManager} from "ag-grid-enterprise";
import { IndexedDbService } from '../indexed-db.service';
// import { ComponentCanDeactivate } from './can-deactivate/can-deactivate.guard';
import { HostListener } from '@angular/core';
import { IntakeService } from '../intake.service';
declare var moment: any;
var momentTz = require('moment-timezone');
var jstz = require('jstz');
const timezone = jstz.determine();
declare var swal: any;
declare var $: any;
import { isBlank } from 'app/utils/utils';
import { DomSanitizer } from '@angular/platform-browser';
declare var NProgress: any;
import 'ag-grid-enterprise';
import { PARAMETERS } from '@angular/core/src/util/decorators';
import { AdvanceSearchComponent } from '../advance-search/advance-search.component';
import { ConstantService } from '../constant.service';
import { NGXLogger } from 'ngx-logger';
import { Subscription } from 'rxjs';
@Component({
	selector: 'app-intake-worklist',
	templateUrl: './intake-worklist.html',
	styleUrls: ['./intake-worklist.css']
})
export class IntakeWorklistComponent implements OnInit, OnDestroy {
	@ViewChild('element') element: ToastComponent;
	@ViewChild('advanceSearchComp') advanceSearchComp: AdvanceSearchComponent;
	//@ViewChild('notificationMsg') notificationMsg: ToastComponent;
	workflowId;
	statusMessage;
	tenantIds; 
	public position = { X: 'Right' };
	public gridApi;
	private gridColumnApi;
	private overlayLoadingTemplate;
	private overlayNoRowsTemplate;
	private operationType = 'Database';
	isRowMaster;
	rowGroupPanelShow;
	rowData: any = [];
	columnDefs = [];
	frameworkComponents: any;
	dashboardWidgets: any;
	reportFields: any;
	filterEnabledFields: any;
	actionFields: any;
	defaultColDef: any;
	formRowData = [];
	formId: number;
	formName: string;
	worklistDetails: any = [];
	formDataList = [];
	heading: string;
	helpMsg: string;
	addNewBtn = true;
	addNewBtnText: string;
	addNewBtnLink: string;
	worklistName: string;
	public papperWorkList = [];
	public bothDataType = [];
	public checklistTitle = '';
	public checklistShowMultipleEntry = false;
	public checklistItemTitle = '';
	public checklistRequiredTitle = '';
	public checklistCompletedTitle = '';
	worklistid: number;
	dataLoadingMsg: boolean;
	structureFormContent: any = '';
	uniqueClass = '';
	showAll = false;
	showLess = true;
	hasUniqueId;
	staffList = [];
	patientList = [];
	clinicalUserDetails: any;
	staffLanguage = '';
	selectedEntry = '';
	searchTexts = '';
	searchFieldText = '';
	singleRowActions = [];
	shortLinkActions = [];
	//multipleActionButtons = [];
	autoGroupColumnDef;
	private groupRowInnerRenderer;
	private components;
	machFormIds = [];
	sideBar: any;
	assignType = 'single';
	modalType = 'staff';
	nurseList = [];
	list = [];
	refillDateType = 'due';
	selectedDays = '';
	updateData = [];
	rowModelType;
	cacheBlockSize;
	maxBlocksInCache;
	pagination;
	btnBehaviour = true;
	hideActionButtons = true;
	rowCount = 0;
	userData = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
	userDataConfig = this._structureService.userDataConfig?JSON.parse(this._structureService.userDataConfig):{};
	actionColumnPosition = '';
	formFieldFrom = '';
	metaData;
	formField;
	hideBulkEdit = true;
	bulkEditFields = [];
	dynamicFields: any[];
	form: FormGroup;
	enableBulkEdit = false;
	fieldValueList = [];
	updateProperty = '';
	formFiles = [];
	fileContent;
	detailCellRendererParams;
	selectedDate;
	mapFields = [];
	linkFields = [];
	childColumnDef = [];
	loadMsg = true;
	public myDatePickerOptions: IMyDpOptions = {
		dateFormat: 'mm/dd/yyyy'
	};
	editRowDetails;
	public dateModel: any = {};
	selectedRowDetails = [];
	apiType = '';
	rowSelection = '';
	showDateError = false;
	widgetData = [];
	visitDates = [];
	therapyForm: FormGroup;
	therapyDetails = [];
	editorFields = [];
	childFields = [];
	editorDynamicFields = [];
	showField = false;
	editTherapyId = '';
	therapyCount = 0;
	buttonLabel = 'Add';
	paginationPageSize = 0;
	cacheOverflowSize;
	maxConcurrentDatasourceRequests;
	infiniteInitialRowCount;
	columnWidth;
	suppressExcelExport = false;
	animateRows = false;
	cellChangeFlash = false;
	suppressCsvExport = false;
	showCustomExport = false;
	excelStyles;
	selectedWidget = '';
	selectedWidgetField = '';
	previousHideColumns = [];
	hideColumns = [];
	groupSelectsChildren;
	staffLoadingMsg = true;
	widgetCounts = [];
	selectedLoadingType = '';
	dashboardWidgetField = '';
	dashboardWidgetFieldId = '';
	refillType = true;
	dynamicFilter = false;
	dynamicFilterField = '';
	dynamicFilterLabel = '';
	showAllMsg = false;
	customSearchFields = {};
	allMessageTemplate = [];
	assignStaffActionConfig;
	modalHeading = '';
	onClickEvent = true;
	associatePatientLoading = false;
	assosiatedPatients: any = [];
	selectedAssosiatePatient: any = '';
	selectedAssosiatePatientName: any = '';
	selectedPatientName: any = '';
	//firstLoading = false;
	//deactivateRouting : boolean = true;
	selectedSearchFields = [];
	customSearch = true;
	advanceSearchFields = [];
	customAdvanceSearch;

	assignPatientActionConfig;
	formContent: any = '';
	disableBtn = true;
	submitId = 0;
	responseStatus;
	enableIntegrationforAction = false;
	showTooltip = false;
	showResponse = false;
	guid = '';
	public cellClickDetails: any;
	isShow = true;
	cellClickFn;
	showChat = false;
	senderId;
	initiatorFromLogs;
	chatDetails = {};
	pageList = [];
	public papperWorkData = [];
	papperWorkForm: FormGroup;
	showLoader: boolean;
	pageSize;
	workflowType;
	defaultWidget = '';
	defaultWidgetField = '';
	showModalComponent = false;
	dynamicData: any;
	integrationSettings: any;
	patientId;
	parentWorklistName;
	noteCatId;
	worklistEditData;
	worklistMenuName;
	timeLeft: number = 65;
	minLeft: number = 0;
	interval;
	//masterTenantIds = []; 
style = {
        width: '100%',
            height: window.innerHeight+"px",
        flex: '1 1 auto'
    };
	rowDetails;
	worklistState: any;
	userPreferenceId = 0;
	showStateBtn = false;
	defaultColumnState;
	defaultSortState;
	defaultFilterState;
	defaultSelectedSearchFields = [];
	setColumnState = false;
	setFilterState = false;
	setSortState = false;
	apiCallTriggered:Boolean = false;
	getDataCount:Boolean = false;
	private socketEventSubscriptions: Subscription;
	constructor(
		private http: HttpClient,
		private _workListService: IntakeWorkListService,
		private router: Router,
		private _structureService: StructureService,
		private route: ActivatedRoute,
		private sanitizer: DomSanitizer,
		public _inboxService: InboxService,
		public modalFilter: userFilterPipe,
		private _sharedService: SharedService,
		private formBuilder: FormBuilder,
		public _intakeService: IntakeService,
		private _formBuild: FormBuilder,
		private _constantService: ConstantService,
		private _indexedDbService: IndexedDbService,
		private logger: NGXLogger
	) {
		// Set License key for ag grid enterprose version  
		let agGridLicenseKey = !isBlank(this._structureService.agGridLicenceKey) ? atob(this._structureService.agGridLicenceKey) : this._structureService.agGridLicenceKey;
		this.logger.log(this._structureService.agGridLicenceKey);
		if (agGridLicenseKey) {
			LicenseManager.setLicenseKey(agGridLicenseKey);
		}
		this.papperWorkForm = this._formBuild.group({
			itemName: [''],
			chklist: ['false']

		});
		
	
		this.route.params.subscribe((params: Params) => {
			this.worklistName = params.worklist;
			this.worklistid = params.worklistid;
			this.dataLoadingMsg = true;
			//this.deactivateRouting = true;
			if (params.guid && params.worklist) {
				this._structureService.displayProgress.emit(false);
				this.guid = params.guid;
				this._structureService.appCenterGuid = this.guid;
			}
			this.getIntakeWorklistdetails(this.worklistid);
		});
		/**ag-grid loading message */
		this.overlayLoadingTemplate =
			'<span class="ag-overlay-loading-center">Please wait while we process your request</span>';
		this.overlayNoRowsTemplate = '<span>There are no data available.</span>';
		this.frameworkComponents = {
			buttonRenderer: ButtonRendererComponent,
			groupRowInnerRenderer: GroupRowInnerRenderer,
			iconCellRenderer: IconCellRenderer,
			linkCellRenderer: LinkCellRenderer
		};
		this.groupRowInnerRenderer = 'groupRowInnerRenderer';
		/**Default configuration of column */
		this.defaultColDef = {
			resizable: true,
			//next three properties for the functionalities in column tool panel
			enableValue: true,
			enablePivot: true,
			headerClass: 'multiline',
			headerComponentParams: {
				template:
					'<div class="ag-cell-label-container" role="presentation">' +
					'  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>' +
					'  <div ref="eLabel" class="ag-header-cell-label" role="presentation">' +
					'    <span ref="eSortOrder" class="ag-header-icon ag-sort-order" ></span>' +
					'    <span ref="eSortAsc" class="ag-header-icon ag-sort-ascending-icon" ></span>' +
					'    <span ref="eSortDesc" class="ag-header-icon ag-sort-descending-icon" ></span>' +
					'    <span ref="eSortNone" class="ag-header-icon ag-sort-none-icon" ></span>' +
					'    <span ref="eText" class="ag-header-cell-text" role="columnheader" ' +
					'    style="font-family: PT Sans, sans-serif;font-size: 1.02rem;' +
					'    font-weight: bold;color:#514d6a"></span>' +
					'    <span ref="eFilter" class="ag-header-icon ag-filter-icon"></span>' +
					'  </div>' +
					'</div>'
			},
			autoHeight: true
		};
		/**Excel header style while export ag grid data */
		this.excelStyles = [
			{
				id: 'header',
				interior: {
					color: '#CCCCCC',
					pattern: 'Solid'
				}
			}
		];
		this.helpMsg = 'Click on a tab to see the Patient Details under a category';
		this.addNewBtnText = 'Add New';
		/** for initialize dynamic component for bulk edit functionality */
		let field: DynamicBase<any>[] = [];
		field.push(
			new DropdownQuestion({
				key: 'Dropdown',
				value: '',
				label: 'Test',
				controlType: 'dropdown',
				required: false,
				order: 1
			})
		);
		this.dynamicFields = field;
		let self = this;
		/**Define detail grid configurations - master/detail ag grid */
		this.detailCellRendererParams = {
			detailGridOptions: {
				components: {
					datePicker: this.getDatePicker()
				},
				rowSelection: 'multiple',
				onSelectionChanged(event: any) {
					self.onSelectionChanged(event);
				},
				onCellValueChanged(event: any) {
					self.onCellValueChanged(event);
				},
				columnDefs: [],
				onFirstDataRendered(params) {
					params.api.sizeColumnsToFit();
				}
			},
			getDetailRowData: function (params) {
				if (self.metaData.lazyChild == 'true') {
					setTimeout(function () {
						self._workListService
							.getChildFormData(
								self.metaData.associatedForm,
								self.metaData.childMapField,
								params.data[self.metaData.parentMapField]
							)
							.refetch()
							.then(({ data: response }) => {
								let formDataList = [];
								let formData = response['getAllFormDataMultipleForm'];
								let i = 1;
								formData.forEach((element) => {
									const formObj = {};
									element.elements.forEach((elem) => {
										formObj[elem.labelText] = elem.value;
									});
									formObj['submissionID'] = element.submissionID;
									formDataList.push(formObj);
									i++;
								});
								params.successCallback(formDataList);
							});
					}, 1000);
				} else {
					params.successCallback(params.data.callRecords);
				}
			}
		};
		/**Only show the detail grid if it has value */
		this.isRowMaster = function (dataItem) {
			return dataItem ? dataItem.callRecords && dataItem.callRecords.length > 0 : false;
		};
		this.therapyForm = new FormGroup({});
		// if(localStorage.getItem('advanceSearchFields') && localStorage.getItem('advanceSearchFields') != '') {
		// 	this.isShow = false;
		// }
	}
	ngOnInit() {
		/**Avoid duplicate entry while edit machform- authorization */
		var userdata = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
		this.socketEventSubscriptions = this._structureService.subscribeSocketEvent('worklistDataUpdate').subscribe((data) => {
		const refillData = data['formData'];
			if (refillData.action == 'update' && (refillData.lockLevel == 'app' || (refillData.lockLevel == 'worklist' && refillData.worklistId == this.worklistid))){
				const index = this.rowData.findIndex((x) => x.id == refillData.patientId);
				if(index != -1){
					if (refillData.updateData.length > 1) {
						refillData.updateData.forEach(element => {
						  this.rowData[index][element.updateField] = element.updatedValue;
						});
					} else {
					this.rowData[index][refillData.updateData[0].updateField] = refillData.updateData[0].updatedValue;
					}
					this.gridApi.refreshCells({ force: true });
				}
			}
		});
		setTimeout(() => {
			if(localStorage.getItem('loadFirstDashboard') == 'true') {
				let element: HTMLElement = document.getElementById('worklist-group') as HTMLElement;
				if(element != null) {
					element.click();
					localStorage.setItem('loadFirstDashboard', '');
				}
			}
		});
this.style = {
            width:'100%',
            height: window.innerHeight+"px",
            flex: '1 1 auto'
        };
	}
	@HostListener('click', ['$event'])
    onClick(event) {
        if ($(event.target).data('toggle') !== 'popover') { 
            $('[data-toggle="popover"]').popover('hide');
        }
    }
	ngOnDestroy() {
		if(this.socketEventSubscriptions) this.socketEventSubscriptions.unsubscribe();
	}
	// @HostListener('window:beforeunload', ["$event"])
	//   canDeactivate(): boolean {
	//     if (this.deactivateRouting == true && this.firstLoading == true ) {
	//       this.notificationMsg.hide('All');
	//       this.notificationMsg.show({cssClass: 'e-toast-info'});
	//       return false;
	//     } else {
	//       return true;
	//     }
	//   }
	/**Refreshing ag grid data after polling detect */
	advanceSearch() {
		this.isShow = !this.isShow;
		if (this.isShow == false) {
			this.customSearch = false;
			//localStorage.setItem('singleSearchFields','');
			this._workListService.singleSearchConfig = {};
			//this.searchFieldText = '';
		} else {
			this.customSearch = true;
		}
	}
	refreshGrid() {
		this.element.hide();
		this.gridApi.purgeServerSideCache();
	}
	/**Get worklist details from cache */
	async getCacheData(worklistid) {
	if (this._workListService.browserCache == 'localstorage') {
		if (localStorage.getItem('worklistDetails') && localStorage.getItem('worklistDetails') != '') {
		let details = JSON.parse(localStorage.getItem('worklistDetails')).filter(x => Number(x.worklistId) == Number(worklistid) && x.edited == false);
		return details.length > 0 ? details[0].worklistDetails : [];
		} else {
		return [];
		}
	} else if (this._workListService.browserCache == 'indexdb') {
		let dataFromIdb: any = [];
		await this._indexedDbService.getByKeyFromIDb(this._workListService.indexDbConfig).then(
		(response) => {
			dataFromIdb = response;
		});
		if (dataFromIdb && !dataFromIdb.length) {
		return [];
		} else {
		let details = dataFromIdb.filter(x => Number(x.worklistId) == Number(worklistid) && x.edited == false);
		return details.length > 0 ? details[0].worklistDetails : [];
		}
	} else if(this._workListService.browserCache == 'sharedvariable') {
		const defIndex = this._sharedService.worklistDetails.findIndex(x => x.worklistId == this.worklistid && x.edited == false);
		if (defIndex == -1) {
		return [];
		} else {
		return this._sharedService.worklistDetails[defIndex].worklistDetails;
		}
	}
	}
	/**Set worklist details to the cache */
	setCacheData(worklistid, worklistDetails) {
	var promise = new Promise((resolve, reject) => {
		let worklistData = [{ 'worklistId': worklistid, 'worklistDetails': worklistDetails, 'edited': false }];
		if (this._workListService.browserCache == 'localstorage') {
		if (localStorage.getItem('worklistDetails') && localStorage.getItem('worklistDetails') != '') {
			let details = JSON.parse(localStorage.getItem('worklistDetails'));
			let index = details.findIndex(x=> x.worklistId == worklistid);
			if(index != -1) {
			details[index].worklistDetails = worklistDetails;
			details[index].edited = false;
			} else {
			details.push({ 'worklistId': worklistid, 'worklistDetails': worklistDetails, 'edited': false });
			}
			localStorage.setItem('worklistDetails', JSON.stringify(details));
		} else {
			localStorage.setItem('worklistDetails', JSON.stringify(worklistData));
		}
		} else if (this._workListService.browserCache == 'indexdb') {
		let dataFromIdb: any = [];
		this._indexedDbService.getByKeyFromIDb(this._workListService.indexDbConfig).then(
			(response) => {
			dataFromIdb = response;
			if (dataFromIdb && !dataFromIdb.length) {
				this._indexedDbService.addToIDb(this._workListService.indexDbConfig, worklistData);
			} else {
				let details = dataFromIdb;
				let index = details.findIndex(x=> x.worklistId == worklistid);
				if(index != -1) {
				details[index].worklistDetails = worklistDetails;
				details[index].edited = false;
				} else {
				details.push({ 'worklistId': worklistid, 'worklistDetails': worklistDetails, 'edited': false });
				}
				this._indexedDbService.addToIDb(this._workListService.indexDbConfig, details);
			}
			});
		} else if(this._workListService.browserCache == 'sharedvariable') {
		const defIndex = this._sharedService.worklistDetails.findIndex(x => x.worklistId == worklistid);
		if (defIndex == -1) {
			this._sharedService.worklistDetails.push({ 'worklistId': worklistid, 'worklistDetails': worklistDetails, 'edited': false });
		} else {
			this._sharedService.worklistDetails[defIndex].worklistId = worklistid;
			this._sharedService.worklistDetails[defIndex].worklistDetails = worklistDetails;
		}
		}
		resolve();
	});
	return promise;
	}
	/**Get the configuration details of worklist and build ag grid */
	getIntakeWorklistdetails(listid) {
		//this.advanceSearchFields = [];
		this.columnDefs = [];
		this.childColumnDef = [];
		this.heading = '';
		this.formId = 0;
		this.formName = '';
		this.dashboardWidgets = [];
		this.rowData = [];
		this.addNewBtnLink = '';
		this.uniqueClass = '';
		this.actionFields = [];
		this.singleRowActions = [];
		this.shortLinkActions = [];
		this.btnBehaviour = true;
		this.hideActionButtons = true;
		this.suppressExcelExport = false;
		this.suppressCsvExport = false;
		this.showCustomExport = false;
		this.metaData = {};
		this.dynamicData = {};
		this.integrationSettings = [];
		this.defaultWidget = '';
		this.defaultWidgetField = '';
		this.isShow = true;
		this.customSearch = true;
		this.setColumnState = false;
		this.setFilterState = false;
		this.setSortState = false;
		this.apiCallTriggered = false;
		/**Check whether worklist details in cache */
		this.getCacheData(listid).then((data) => {
			this.worklistDetails = data;
			if (this.worklistDetails.length == 0) {
			  this._workListService.getWorklistDetails(this.worklistid).then((data) => {
				if (data['getSessionTenant']) {
				  this.worklistDetails = data['getSessionTenant'].formWorklists;
				  this.setCacheData(this.worklistDetails[0].id, this.worklistDetails).then(()=>console.log('cache'));
				  this.getWorklistMetaDetails();
				}
			  });
			} else {
			  this.getWorklistMetaDetails();
			}
		  });
	}
	getWorklistMetaDetails() {
			//this.worklistDetails = data['getSessionTenant'].formWorklists;
			/**For accessing worklist details in inner page */
			localStorage.setItem('worklistDetails', JSON.stringify(this.worklistDetails));
			this.worklistid = this.worklistDetails[0].id;
			this.heading = this.worklistDetails[0].name;
			if (this.worklistDetails[0].reportForms.length > 0) {
				/**Machform id associated with worklist */
				this.formId = this.worklistDetails[0].reportForms[0].id;
			}
			/**Link for add new button */
			this.addNewBtnLink = `apps/${this.guid}/${this.worklistName}/details/${this.worklistid}`;
			this._structureService.displayMenuId.emit(this.worklistid);
			this.actionFields = [];
			/**Get the meta data of worklist*/
			let newJson = this.worklistDetails[0].description;
			newJson = newJson.replace(/'/g, '"');
			let metaData = JSON.parse(newJson);
			this.metaData = metaData;
			if(!this.metaData.stateSavingMode) {
				this.metaData.stateSavingMode = 'autoSave';
			}
			if(!this.metaData.stateSavingPreference) {
				this.metaData.stateSavingPreference = 'cache';
			}
			if(this.metaData.stateSavingMode == 'manualSave' && this.metaData.enableSavingState == true && 
			(this.metaData.sortState == true || this.metaData.filterState == true || this.metaData.columnState == true || this.metaData.searchState == true)) {
				this.showStateBtn = true;
			} else {
				this.showStateBtn = false;
			}
			//this.getMasterTenantIds(this.metaData.tenantType);
			if (this.metaData.reportFormName) {
				this.formName = this.metaData.reportFormName;
			}
			if (this.metaData.enableNotification == true) {
				/**Get all message template if notification is enabled */
				this._workListService.getWorklistMessageTemplate().then((data) => {
					this.allMessageTemplate = JSON.parse(
						JSON.stringify(data['getSessionTenant'].formWorklistMessageTemplate)
					);
				});
			}
			this.metaData.enableWidgetApi = isBlank(this.metaData.enableWidgetApi) ? false : this.metaData.enableWidgetApi;
			/**Show page size list */
			if (this.metaData.pageSizeCount) {
				this.pageList = this.metaData.pageSizeCount.split(',');
				this.pageList.splice(0, 0, Number(metaData.pageSize));
			}
			/**for adding refill due filter */
			this.dynamicFilter = this.metaData.enableDynamicFilter;
			this.dynamicFilterField = this.metaData.filterField;
			this.dynamicFilterLabel = this.metaData.prefixText;
			/**Set the cell click Function */
			this.cellClickFn = metaData.cellClickFn;
			/**Unique id for grouping entries based on a unique value */
			this.hasUniqueId = metaData.hasUniqueId;
			if (
				metaData.hasUniqueId &&
				(localStorage.getItem('selectedLoadingType') == '' ||
					localStorage.getItem('selectedLoadingType') == null)
			) {
				this.uniqueClass = metaData.uniqueIdClass;
				this.selectedLoadingType = '';
				this.showAll = false;
				this.showLess = true;
			} else {
				this.showAll = true;
				this.showLess = false;
				this.selectedLoadingType = 'allRecords';
				localStorage.setItem('selectedLoadingType', '');
			}
			/**Default column width */
			this.columnWidth = this.metaData.defaultColumnWidth;
			/** Single/Multiple row selection */
			this.rowSelection = this.metaData.checkboxSelectionType;

			if (this.metaData.checkboxSelectionType == 'multiple') {
				/**select all sub entries of group while select the checkbox for the group */
				this.groupSelectsChildren = true;
			} else {
				this.groupSelectsChildren = false;
			}
			/** form label from either label text or from css class */
			this.formFieldFrom = metaData.formFieldFrom;
			/**determine the position of action button */
			this.btnBehaviour = metaData.btnBehaviour;
			this.actionColumnPosition = metaData.btnPosition;
			/**set Lazy loading parameters */
			this.cacheBlockSize = Number(metaData.InitCountVal);
			this.maxBlocksInCache = Number(metaData.blocksize);
			/**Page size */
			this.paginationPageSize = Number(metaData.pageSize);
			this.pageSize = this.paginationPageSize;
			if (localStorage.getItem('worklistPageSize') && localStorage.getItem('worklistPageSize') != '') {
				let pageSizeList = JSON.parse(localStorage.getItem('worklistPageSize'));
				if (pageSizeList.worklistId == this.worklistid) {
					this.paginationPageSize = pageSizeList.pageSize;
					this.pageSize = this.paginationPageSize;
				} else {
					localStorage.setItem('worklistPageSize', '');
				}
			}
			if (metaData.rowModel == 'server') {
				/**Set data loading type as server side */
				this.rowModelType = 'serverSide';
				if (metaData.dataLoadingType && metaData.dataLoadingType == 'pagination') {
					this.pagination = true;
				} else {
					this.pagination = false;
				}
			} else {
				/**Set data loading type as client side */
				this.rowModelType = 'clientSide';
				if (metaData.pageSize != null && metaData.pageSize != '') {
					this.pagination = true;
				} else {
					this.pagination = false;
				}
			}
			/**Set workflow type */
			let callback = metaData.callbackFunctionName != null ? metaData.callbackFunctionName.trim().toUpperCase() : '';
			if (callback == 'LIAISONS') {
				this.workflowType = this._constantService.entryTypes['LIAISON'];
			} else if (callback == 'INTAKEFOLLOWUP') {
				this.workflowType = this._constantService.entryTypes['INTAKE'];
			} else if (callback == 'INITIALINSURANCEVERIFICATION') {
				this.workflowType = this._constantService.entryTypes['INITIAL_INS'];
			} else if (callback == 'INSURANCEVERIFICATIONFOLLOWUP') {
				this.workflowType = this._constantService.entryTypes['INS_VERIFICATION'];
			} else if (callback == 'PHARMACYFOLLOWUP') {
				this.workflowType = this._constantService.entryTypes['PHARMACY_FOLLOWUP'];
			}
			localStorage.setItem('currentWorkflowType', this.workflowType);
			localStorage.setItem('visibleToRole', metaData.visibleToRoles);
			/**Get the reportFields and sort  */
			this.reportFields = JSON.parse(JSON.stringify(this.worklistDetails[0].reportFields));
			/**Set dynamic fields of report field those having bulk edit feature */
			this.setDynamicFields();
			this.reportFields.sort(function (a, b) {
				if (a.displayIndex < b.displayIndex) {
					return -1;
				}
				if (a.displayIndex > b.displayIndex) {
					return 1;
				}
				return 0;
			});
			/**Filter field */
			this.searchFieldText = '';
			this.selectedSearchFields = this.reportFields.filter(
				(x) => x.allowQuickSearch == true && x.enableAutoSelect == true
			);
			this.filterEnabledFields = this.reportFields.filter(
				(x) => x.allowQuickSearch == true && x.visibility == true
			);
			this.defaultSelectedSearchFields = this.reportFields.filter(x => x.allowQuickSearch == true && x.enableAutoSelect == true);
			/**Child table fields in master/detail type ag grid */
			this.childFields = this.reportFields.filter((x) => x.fieldType == 'child');
			/**Check column definition is present in shared service */
			/**Save configuration data to shared service in initial loading and get that data ifrom shared service in further loading */
			const defIndex = this._sharedService.worklistColumnDef.findIndex(
				(x) => x.worklistId == this.worklistid
			);
			if (defIndex == -1) {
				this.setColumnDefinition(this.actionColumnPosition);
				this._sharedService.worklistColumnDef.push({
					worklistId: this.worklistid,
					columnDef: this.columnDefs,
					childColumnDef: this.childColumnDef,
					edited: false,
					actionColDef: this.singleRowActions,
					shortLinks: this.shortLinkActions,
					linkFields: this.linkFields,
					mapFields: this.mapFields,
					fieldPrefillData: this.list
				});
			} else {
				if (this._sharedService.worklistColumnDef[defIndex].edited) {
					this.setColumnDefinition(this.actionColumnPosition);
					this._sharedService.worklistColumnDef[defIndex].edited = false;
					this._sharedService.worklistColumnDef[defIndex].columnDef = this.columnDefs;
					this._sharedService.worklistColumnDef[defIndex].actionColDef = this.singleRowActions;
					this._sharedService.worklistColumnDef[defIndex].shortLinks = this.shortLinkActions;
					this._sharedService.worklistColumnDef[defIndex].childColumnDef = this.childColumnDef;
					this._sharedService.worklistColumnDef[defIndex].mapFields = this.mapFields;
					this._sharedService.worklistColumnDef[defIndex].linkFields = this.linkFields;
					this._sharedService.worklistColumnDef[defIndex].fieldPrefillData = this.list;
				} else {
					this.columnDefs = this._sharedService.worklistColumnDef[defIndex].columnDef;
					this.childColumnDef = this._sharedService.worklistColumnDef[defIndex].childColumnDef;
					this.singleRowActions = this._sharedService.worklistColumnDef[defIndex].actionColDef;
					this.shortLinkActions = this._sharedService.worklistColumnDef[defIndex].shortLinks;
					this.detailCellRendererParams.detailGridOptions['columnDefs'] = [];
					this.detailCellRendererParams.detailGridOptions[
						'columnDefs'
					] = this._sharedService.worklistColumnDef[defIndex].childColumnDef;
					this.mapFields = this._sharedService.worklistColumnDef[defIndex].mapFields;
					this.linkFields = this._sharedService.worklistColumnDef[defIndex].linkFields;
					this.list = this._sharedService.worklistColumnDef[defIndex].fieldPrefillData;
				}
			}
			/**Adding datepicker while editing cell having date value */
			this.components = { datePicker: this.getDatePicker() };
			/**Row grouping settings of ag grid */
			if (this.metaData.enableRowGroup == true) {
				this.rowGroupPanelShow = 'always';
			}
			/**Default group definition */
			this.autoGroupColumnDef = {
				headerName: 'Group',
				width: 200,
				cellRenderer: 'agGroupCellRenderer',
				cellRendererParams: { checkbox: true }
			};
			/**Showing add new button on right top corner */
			this.addNewBtn = this.metaData.addNewButton;
			/**Set the dashboard widgets */
			this.dashboardWidgets = JSON.parse(JSON.stringify(this.worklistDetails[0].dashboardWidgets));
			this.dashboardWidgets.sort(function (a, b) {
				if (a.displayIndex < b.displayIndex) {
					return -1;
				}
				if (a.displayIndex > b.displayIndex) {
					return 1;
				}
				return 0;
			});
			if (this.dashboardWidgets.length > 0) {
				/**Set selected widget for showing that widget highlighted */
				if(localStorage.getItem('selectedWidget') && localStorage.getItem('selectedWidget') != '' && localStorage.getItem('currentDashboard') == this.route.snapshot.paramMap.get('worklistid')) {
					this.selectedWidget = localStorage.getItem('selectedWidget');
					localStorage.setItem('currentDashboard','');
					this.defaultWidget = localStorage.getItem('selectedWidget');
					this.defaultWidgetField = this.dashboardWidgets[0].formField;
					this.selectedWidgetField = this.dashboardWidgets[0].formField;
				} else {
					let index = this.dashboardWidgets.findIndex(x=> x.defaultWidget == true);
					if(index > -1) {
						this.selectedWidget = this.dashboardWidgets[index].widgetValue;
						this.selectedWidgetField = this.dashboardWidgets[index].formField;
						this.defaultWidget = this.dashboardWidgets[index].widgetValue;
						this.defaultWidgetField = this.dashboardWidgets[index].formField;

					} else {
						this.selectedWidget = this.dashboardWidgets[0].widgetValue;
						this.selectedWidgetField = this.dashboardWidgets[0].formField;
						if(this.dashboardWidgets[0].widgetValue != 'All') {
							this.defaultWidget = this.dashboardWidgets[0].widgetValue;
							this.defaultWidgetField = this.dashboardWidgets[0].formField;
						}
					}
				}
				localStorage.setItem('currentWidget', this.selectedWidget);
				if(this.metaData.enableSavingState == true && this.metaData.filterState == true) {
					let widgetModel = (localStorage.getItem('widgetState') && localStorage.getItem('widgetState') != '') ? JSON.parse(localStorage.getItem('widgetState')) : [];
					let index = widgetModel.findIndex(x=> x.worklist == this.worklistid);
					  if(index == -1) {
						widgetModel.push({worklist: this.worklistid, widgetState: this.defaultWidget});
					  }
					localStorage.setItem('widgetState', JSON.stringify(widgetModel));
				  }
			}
			if (this.gridApi) {
				/**set column definition in ag grid */
				this.gridApi.setColumnDefs(this.columnDefs);
			}
			/**Set export option */
			if (metaData.enableExcelExport == false) {
				this.suppressExcelExport = true;
			}
			if (metaData.enableCsvExport == false) {
				this.suppressCsvExport = true;
			}
			if (metaData.enableAnimateRows == true) {
				this.animateRows = true;
			}
			if (metaData.enableCellChangeFlash == true) {
				this.cellChangeFlash = true;
			}
			/**Add external custom export button */
			if (metaData.enableCustomExport == true) {
				this.showCustomExport = true;
			}
			/**Set the right side panel of ag grid */
			this.sideBar = {
				toolPanels: []
			};
			let panelObj = {};
			if (metaData.filterPanel == 'true') {
				panelObj = {
					id: 'filters',
					labelDefault: 'Filters',
					labelKey: 'filters',
					iconKey: 'filter',
					toolPanel: 'agFiltersToolPanel'
				};
				this.sideBar.toolPanels.push(panelObj);
			}
			if (metaData.columnPanel == 'true') {
				panelObj = {
					id: 'columns',
					labelDefault: 'Columns',
					labelKey: 'columns',
					iconKey: 'columns',
					toolPanel: 'agColumnsToolPanel',
					toolPanelParams: {
						suppressRowGroups: true,
						suppressValues: true,
						suppressPivots: true,
						suppressPivotMode: true
					}
				};
				this.sideBar.toolPanels.push(panelObj);
			}
			setTimeout(() => {
				this.dataLoadingMsg = false;
			  }, 20);
	}
	// getMasterTenantIds(config) {
	// 	let tenantId = '';
	// 	let userDetails = JSON.parse(localStorage.getItem('userDetails'));
	// 	if(config == 'tenant') {
	// 		this._intakeService.getMaterTenantIds(userDetails.organizationMasterId).then((data) => {
	// 			this.masterTenantIds = data['getMasterTenants'];
	// 		});
	// 	}
	// }
	setColumnDefinition(position) {
		/**Action button cofiguration - single and multiple */
		let columnObj = {};
		this.list = [];
		this.detailCellRendererParams.detailGridOptions['columnDefs'] = [];
		this.actionFields = JSON.parse(JSON.stringify(this.worklistDetails[0].singleWorklistAction));
		this.actionFields['actionButton'].forEach((element) => {
			const obj = {
				label: element.actionLabel,
				iconClass: element.actionIcon,
				disable: false,
				toolTip: element.tooltiptext,
				cssClass: element.cssClass,
				onClick: '',
				type: '',
				showAlert: false,
				buttonStyle: '',
				itemElements: [],
				callbackfunction: element.actionCallbackFunction,
				actionLink: element.actionLink,
				enableIntegration: element.enableIntegration,
				actionFields: element.actionFields,
				actionField: element.formField ? element.formField : '',
				mapField: element.mapField ? element.mapField : '',
				actionMode: element.actionMode ? element.actionMode : '',
				selectionMode: element.selectionMode ? element.selectionMode : '',
				fieldValues: element.fieldValues,
				notification: {
					enableNotification: element.enableNotification,
					push: element.push,
					pushTemplate: element.pushTemplate,
					sms: element.sms,
					smsTemplate: element.smsTemplate,
					email: element.email,
					emailTemplate: element.emailTemplate,
					notifyRolesOnAction: element.notifyRolesOnAction,
					notifyRecipientOnAction: element.notifyRecipientOnAction
				}
			};
			if (element.actionLink === 'edit' || element.actionCallbackFunction === 'editEntry') {
				obj.onClick = this.editEntry.bind(this);
			}
			if (element.actionLink === 'recall' || element.actionCallbackFunction === 'recallEntry') {
				obj.onClick = this.recallEntry.bind(this);
			}
			if (element.actionLink === 'delete' || element.actionCallbackFunction === 'deleteEntry') {
				obj.onClick = this.deleteEntry.bind(this);
			}
			if (element.actionLink === 'sendtoCprplus' || element.actionCallbackFunction === 'sendtoCprplus') {
				//obj.onClick = this.sendtoCprplus.bind(this);
			}
			if (element.actionLink === 'addNote' || element.actionCallbackFunction === 'addNote') {
				obj.onClick = this.addBlogNote.bind(this);
			}
			if (element.actionLink === 'unlockPatient' || element.actionCallbackFunction === 'unlockPatient') {
				obj.onClick = this.unlockPatient.bind(this);
			}
			if (element.actionButtonType == 'single') {
				obj.type = 'single';
				/**Short links are shown above ag grid for rerouting to any other page */
				if (element.shortLink == true) {
					this.shortLinkActions.push(obj);
				} else {
					this.singleRowActions.push(obj);
				}
			} else {
				obj.type = 'multiple';
				obj.buttonStyle = element.buttonStyle;
				obj.onClick = this.multipleButtonAction.bind(this);
				element.actionMenu.forEach((item) => {
					const itemObj = {
						label: item.itemName,
						disable: false,
						action: '',
						itemField: item.itemFormField
					};
					if (item.itemActionType == 'link') {
						itemObj.action = item.itemActionLink;
					} else {
						itemObj.action = item.itemActionCallbackFunction;
					}
					obj.itemElements.push(itemObj);
				});
				this.singleRowActions.push(obj);
			}
		});
		if (position == 'first') {
			if (this.btnBehaviour) {
				this.columnDefs.push({
					headerName: 'Actions',
					field: 'action',
					minWidth: 200,
					sortable: false,
					filter: false,
					cellRenderer: 'buttonRenderer',
					cellRendererParams: {
						label: 'Label',
						iconElements: this.singleRowActions
					}
				});
			}
		}
		/**Configure column definition */
		let i = 1;
		this.reportFields.forEach((element) => {
			columnObj = {};
			if (element.fieldName == 'submissionID' && this.rowModelType == 'clientSide') {
				/**Add default sorting based submission id in client side loading */
				columnObj['sort'] = { direction: 'desc', priority: 0 };
			}
			/**Table column header name */
			columnObj['headerName'] = element.headerName;
			/**form field corresponding to each column */
			columnObj['field'] = element.fieldName;
			if (element.cellEvent) {
				columnObj['cellEvent'] = element.cellEvent;
			}
			/**Set minimum width for column */
			if (element.columnWidth && element.columnWidth != '') {
				columnObj['minWidth'] = Number(element.columnWidth);
			} else {
				if (this.columnWidth && this.columnWidth != '') {
					columnObj['minWidth'] = Number(this.columnWidth);
				} else {
					columnObj['minWidth'] = 200;
				}
			}
			/**check whether the column is visible or not*/
			if (element.visibility === false || element.visibility === null) {
				columnObj['hide'] = true;
			}
			/**check whether the cell can be edit*/
			if (element.allowEdit == true) {
				columnObj['editable'] = function (params) {
					return true;
				};
			}
			columnObj['headerTooltip'] = element.headerTooltip;
			/**Value formatter for null value */
			if (element.allowCellValueChange) {
				columnObj['valueFormatter'] = function cellFormatterFn(params) {
					if (params.value == '') {
						if (element.newCellValue && element.newCellValue != '') {
							return element.newCellValue;
						} else {
							return params.value;
						}
					}
				};
			}
			/**Format cell value if field value delimitter is present */
			if (element.fieldValueDelimiter && element.fieldValueDelimiter != '') {
				columnObj['valueFormatter'] = function cellFormatterFn(params) {
					if (element.fieldValueDelimiter.length == 1) {
						if (params.value.includes(element.fieldValueDelimiter)) {
							const originalValue = params.value.split(element.fieldValueDelimiter);
							return originalValue[0];
						}
					} else if (element.fieldValueDelimiter.length == 2) {
						if (
							params.value.includes(element.fieldValueDelimiter.charAt(0)) &&
							params.value.includes(element.fieldValueDelimiter.charAt(1))
						) {
							let originalValue = params.value.split(element.fieldValueDelimiter.charAt(0));
							return originalValue[0];
						}
					} else {
						return params.value;
					}
				};
			}
			/**map field  used in master/detail page*/
			if (element.mapField) {
				this.mapFields.push({
					fieldName: element.fieldName,
					mapField: element.mapField,
					type: element.valueType
				});
			}
			/**fields and its values to prepopulate to the form while adding new entries */
			if (element.linkField) {
				this.linkFields.push({
					fieldName: element.fieldName,
					mapField: element.fieldId,
					linkField: element.linkField,
					type: element.valueType
				});
			}
			if (element.allowCellFormat == true && element.cellFormat) {
				let cssFormat = element.cellFormat;
				if (cssFormat.length > 0) {
					columnObj['valueFormatter'] = function cellRenderer(params) {
						if (params.value != '') {
							let columnValue = params.value != null ? params.value.toString().trim() : params.value;
							for (let i = 0; i < cssFormat.length; i++) {
								let fieldValue;
								fieldValue = cssFormat[i].cellValue != null ? cssFormat[i].cellValue.toString().trim() : '';
								if (cssFormat[i].expression == '==' && columnValue == fieldValue) {
									return cssFormat[i].formattedValue;
								}
								if (cssFormat[i].expression == '!=' && columnValue != fieldValue) {
									return cssFormat[i].formattedValue;
								}
								if (cssFormat[i].expression == '>' && columnValue > fieldValue) {
									return cssFormat[i].formattedValue;
								}
								if (cssFormat[i].expression == '>=' && columnValue >= fieldValue) {
									return cssFormat[i].formattedValue;
								}
								if (cssFormat[i].expression == '<' && columnValue < fieldValue) {
									return cssFormat[i].formattedValue;
								}
								if (cssFormat[i].expression == '<=' && columnValue <= fieldValue) {
									return cssFormat[i].formattedValue;
								}
								if (
									cssFormat[i].expression == 'contains' &&
									columnValue &&
									columnValue.includes(fieldValue)
								) {
									return cssFormat[i].formattedValue;
								}
							}
						}
					};
				}
			}
			/**Add hyperlink to cell content */
			if(element.enableHyperlink == true) {
				let index = this.singleRowActions.findIndex(x=> x.actionLabel == element.associatedAction);
				columnObj['cellRenderer'] = 'linkCellRenderer';
				columnObj['cellRendererParams'] = {
					linkDetails: this.singleRowActions[0],
					privileges: Object.keys(this._structureService.privileges),
					userData : this.userData,
					workflowType: this.workflowType,
					lockTime: this.metaData.lockTime,
					enableLockingMode: this.metaData.enableLockingMode
				}
			}
			/**Cell style property */
			if (element.allowCellStyle == true && element.cellStyles) {
				let cssStyles = element.cellStyles.filter(x => x.contentType == "text");
				if (cssStyles.length > 0) {

					columnObj['cellStyle'] = function (params) {
						let columnValue;
						let formField;
						if (typeof params.value == 'string') {
							columnValue = params.value.toLowerCase();
						} else {
							columnValue = params.value;
						}
						for (let i = 0; i < cssStyles.length; i++) {
							let fieldValue;
							let allowRelatedFeild;
							allowRelatedFeild = cssStyles[i].allowRelatedFeild;
							if (allowRelatedFeild) {
								let relatedField = cssStyles[i].formField;
								if (params.data) {
									columnValue = params.data[`${relatedField}`] != null ? params.data[`${relatedField}`].toString() : '';
								}
							}

							if (typeof cssStyles[i].fieldValue == 'string') {

								fieldValue = cssStyles[i].fieldValue.toLowerCase();
							} else {
								fieldValue = cssStyles[i].fieldValue;
							}
							if (cssStyles[i].expression == '==' && columnValue == fieldValue) {
								return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
							}

							if (cssStyles[i].expression == '!=' && columnValue != fieldValue) {
								return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
							}
							if (cssStyles[i].expression == '>' && columnValue > fieldValue) {
								return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
							}
							if (cssStyles[i].expression == '>=' && columnValue >= fieldValue) {
								return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
							}
							if (cssStyles[i].expression == '<' && columnValue < fieldValue) {
								return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
							}
							if (cssStyles[i].expression == '<=' && columnValue <= fieldValue) {
								return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
							}
							if (cssStyles[i].expression == 'contains' && columnValue && columnValue.includes(fieldValue)) {
								return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
							}
							if (cssStyles[i].expressionType && cssStyles[i].expressionType == 'day') {
								var date = moment(params.value);
								let dayValues = cssStyles[i].day.split(',');
								if (dayValues.indexOf(date.day().toString()) != -1) {
									return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
								} else if (dayValues.indexOf('today') != -1) {
									var today = moment(new Date()).format('MM/DD/YYYY');
									if (date.isSame(today) == true) {
										return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
									}
								} else if (dayValues.indexOf('yesterday') != -1) {
									var yesterday = moment().subtract(1, 'days').format('MM/DD/YYYY');
									if (date.isSame(yesterday) == true) {
										return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
									}
								} else if (dayValues.indexOf('tomorrow') != -1) {
									var tomorrow = moment().add(1, 'days').format('MM/DD/YYYY');
									if (date.isSame(tomorrow) == true) {
										return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
									}
								}
							}
							if (cssStyles[i].expressionType && cssStyles[i].expressionType == 'month') {
								var date = moment(params.value);
								let monthValue = cssStyles[i].month.split(',');
								if (monthValue.indexOf(date.month().toString()) != -1) {
									return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
								}
							}
							if (cssStyles[i].expressionType && cssStyles[i].expressionType == 'year') {
								var date = moment(params.value);
								if (cssStyles[i].year == date.year()) {
									return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
								}
							}
						}

					}
				}				
				let iconStyles = element.cellStyles.filter(x => x.contentType == "icon" || x.contentType == "both");
				if (iconStyles.length > 0) {
					columnObj['cellRenderer'] = 'iconCellRenderer';
					columnObj['cellStyle'] = { textAlign: 'center' };
					let iconArray = [];
					for (let i = 0; i < iconStyles.length; i++) {
						let iconObj;
						iconObj = {
							iconColour: iconStyles[i].iconColor, 
							iconClass: iconStyles[i].iconClass,
							fieldValue: iconStyles[i].fieldValue, 
							expression: iconStyles[i].expression,
							expressionType: iconStyles[i].expressionType,
							day: iconStyles[i].day,
							month: iconStyles[i].month, 
							year: iconStyles[i].year,
							contentType: iconStyles[i].contentType,
							allowRelatedField: iconStyles[i].allowRelatedField,
							relatedField: iconStyles[i].allowRelatedField == true ? iconStyles[i].formField : ''
						}
						iconArray.push(iconObj);
					}
					columnObj['cellRendererParams'] = {
						iconElements: iconArray
					}
				}
			}
			/**Set the column group feature*/
			let columnGroups = [];
			if (element.groupName != '' || element.groupName != '') {
				columnGroups.push(element.groupName);
				let obj = {
					headerName: element.headerName,
					field: element.fieldName
				};
			}
			/**formating date time */
			if (element.valueType === 'timestamp') {
				if(element.enableTenantTimezone == true) {
					let timeZone = 'America/New_York';
					if(this.userDataConfig.tenant_timezoneName && this.userDataConfig.tenant_timezoneName != '') {
						if(this.userDataConfig.tenant_timezoneName.toUpperCase() != 'UTC') { 
							timeZone = this.userDataConfig.tenant_timezoneName;
						}
					} else {
						timeZone = momentTz.tz.guess();
					}
					columnObj['valueFormatter'] = function cellFormatterFn(params) {
						if(!isBlank(params.value)) {
							let dat = params.value;
							let data = moment.utc(new Date(dat));
							return momentTz.tz((data), timeZone).format('MM/DD/YYYY h:mm:ss A z');
						}
					};
				} else {
					columnObj['valueFormatter'] = this.convertDateTimeFormat;
				}
			}
			/**formating time */
			if (element.valueType === 'time') {
				if (element.allowCellValueChange && element.newCellValue) {
					columnObj['valueFormatter'] = function cellFormatterFn(params) {
						return element.newCellValue;
					};
				} else {
					columnObj['valueFormatter'] = this.convertTimeFormat;
				}
			}
			/**Add sorting feature */
			if (element.allowSort === false) {
				columnObj['sortable'] = false;
			}
			/**Add filter feature */
			if (element.allowFilter === false) {
				columnObj['filter'] = false;
			} else {
				let obj = {
					newRowsAction: 'keep',
					suppressRemoveEntries: true,
					applyButton: element.applyFilter ? element.applyFilter : false,
					clearButton: element.clearFilter ? element.clearFilter : false,
					suppressAndOrCondition: true
				};
				if (
					element.valueType == 'checkbox' ||
					(element.fieldValueDelimiter && element.fieldValueDelimiter != '')
				) {
					obj['filterOptions'] = ['contains'];
					columnObj['filterParams'] = obj;
				} else if (element.valueType == 'radio' || element.valueType == 'select') {
					obj['filterOptions'] = ['contains', 'equals'];
					columnObj['filterParams'] = obj;
				} else {
					columnObj['filterParams'] = obj;
				}
				/**Set different kind of filterings */
				if (element.filterType === 'Number_Filter') {
					columnObj['filter'] = 'agNumberColumnFilter';
				}
				if (element.filterType === 'Text_Filter') {
					columnObj['filter'] = 'agTextColumnFilter';
				}
				if (element.filterType === 'Date_filter') {
					columnObj['filter'] = 'agDateColumnFilter';
				}
			}
			/**Process date value*/
			if (element.valueType === 'date') {
				/**Show datepicker while editing */
				columnObj['cellEditor'] = 'datePicker';
				/**Format date to a particular format*/
				if (element.allowCellValueChange && element.newCellValue) {
					columnObj['valueFormatter'] = function cellFormatterFn(params) {
						return element.newCellValue;
					};
				} else {
					if(element.enableTenantTimezone == true) {
						let timeZone = 'America/New_York';
						if(this.userDataConfig.tenant_timezoneName && this.userDataConfig.tenant_timezoneName != '') {
							if(this.userDataConfig.tenant_timezoneName.toUpperCase() != 'UTC') { 
								timeZone = this.userDataConfig.tenant_timezoneName;
							}
						} else {
							timeZone = momentTz.tz.guess();
						}
						columnObj['valueFormatter'] = function cellFormatterFn(params) {
							if (!params.value) {
								return '';
							}
							if (params.value || params.value != '') {
								let dat = params.value;
								let data = moment.utc(new Date(dat));
								return momentTz.tz((data), timeZone).format('MM/DD/YYYY');
							} else {
								return '';
							}
						};
					} else {
						columnObj['valueFormatter'] = this.convertDateFormat;
					}
				}
				/**Implement sorting of date fields */
				columnObj['comparator'] = this.dateComparator;
				/**Implement date filter functionality */
				columnObj['filterParams'] = {
					inRangeInclusive: true,
					newRowsAction: 'keep',
					suppressRemoveEntries: true,
					suppressAndOrCondition: true,
					applyButton: element.applyFilter ? element.applyFilter : false,
					clearButton: element.clearFilter ? element.clearFilter : false,
					comparator: function (filterLocalDateAtMidnight, cellValue) {
						const dateParts = cellValue.split('/');
						const cellDate = new Date(dateParts);
						if (cellDate < filterLocalDateAtMidnight) {
							return -1;
						} else if (cellDate > filterLocalDateAtMidnight) {
							return 1;
						} else {
							return 0;
						}
					}
				};
			}
			/**Enable row grouping for each column */
			columnObj['enableRowGroup'] = false;
			if (element.allowRowGrouping) {
				columnObj['enableRowGroup'] = true;
			}
			/**Enable checkbox selection for each column */
			if (element.checkboxSelection) {
				columnObj['checkboxSelection'] = true;
				if (this.rowSelection == 'multiple' && this.rowModelType == 'clientSide') {
					/** header checkbox selection only supported for client side loading */
					/**for adding checkbox in header also */
					columnObj['headerCheckboxSelection'] = true;
					/**Set the property of header check box */
					if (this.metaData.headerCheckboxMode == 'filtered') {
						columnObj['headerCheckboxSelectionFilteredOnly'] = true;
					} else {
						columnObj['headerCheckboxSelectionFilteredOnly'] = false;
					}
				}
				/**For Master detail grid view */
				if (this.metaData.worklistType == 'master/detail') {
					columnObj['cellRenderer'] = 'agGroupCellRenderer';
				}
			}
			/**For Master detail grid view */
			if (this.metaData.worklistType == 'master/detail' && i == 1) {
				columnObj['cellRenderer'] = 'agGroupCellRenderer';
			}

			if (element.largeCellEditor == true) {
				columnObj['cellEditor'] = 'agLargeTextCellEditor';
			}
			if (element.allowPrefillData == true) {
				/**prefill option in inline cell editing */
				if (element.prefillMethod == 'formOptions') {
					let formElementId: number;
					let formId: number;
					let formType = 'internal';
					let extraOptions = [];
					if (element.prefillFormOption == 'internal') {
						if (element.fieldType == 'child') {
							formId = this.metaData.associatedForm;
						} else {
							formId = this.formId;
						}
						if (element.fieldId) {
							formElementId = element.fieldId;
						}
					} else {
						formType = 'external';
						formId = element.prefillOptionForm;
						formElementId = element.prefillOptionFormField;
						/**Add extra option*/
						if (element.prefillExtraOptions && element.prefillExtraOptions != '') {
							extraOptions = element.prefillExtraOptions.split('||');
						}
					}
					/**Show values in dropdown while double click on cell */
					columnObj['cellEditor'] = 'agRichSelectCellEditor';
					columnObj['cellEditorParams'] = {
						values: undefined
					};
					/**Get the options of a machform field*/
					this._workListService
						.getMachformFields(formId, Number(formElementId))
						.refetch()
						.then(({ data: response }) => {
							this.list[element.fieldName] = JSON.parse(
								JSON.stringify(response['getFormElementDetails'].options)
							);
							this.list[element.fieldName].push({ type: formType });
							const elementList = [];
							response['getFormElementDetails'].options.forEach((element) => {
								elementList.push(element.optionValue);
							});
							extraOptions.forEach((element) => {
								elementList.push(element.trim());
							});
							/**set dropdown list while editing cell */
							this.setDropdownList(elementList, element.fieldName, element.fieldType);
						});
				}
			}
			/**set grouping for ag grid */
			if (element.groupName != '' && element.groupName != null && element.visibility == true) {
				const groupIndex = this.columnDefs.findIndex(
					(x) => x.headerName == element.groupName && x.children.length != 0
				);
				if (groupIndex == -1) {
					const childObj = {
						children: []
					};
					childObj['headerName'] = element.groupName;
					childObj['marryChildren'] = true;
					childObj['children'].push(columnObj);
					this.columnDefs.push(childObj);
				} else {
					columnObj['columnGroupShow'] = 'open';
					this.columnDefs[groupIndex]['children'].push(columnObj);
				}
			} else {
				if (element.fieldType && element.fieldType == 'child') {
					this.childColumnDef.push(columnObj);
					this.detailCellRendererParams.detailGridOptions['columnDefs'].push(columnObj);
				} else {
					this.columnDefs.push(columnObj);
				}
			}
			i++;
		});
		if (position == 'last') {
			if (this.btnBehaviour) {
				this.columnDefs.push({
					headerName: 'Actions',
					field: 'action',
					minWidth: 200,
					sortable: false,
					filter: false,
					cellRenderer: 'buttonRenderer',
					cellRendererParams: {
						label: 'Label',
						iconElements: this.singleRowActions
					}
				});
			}
		}
	}
	/**List options while edit cell */
	setDropdownList(list, field, gridType) {
		let colDef;
		if (gridType == 'child') {
			colDef = this.childColumnDef;
		} else {
			colDef = this.columnDefs;
		}
		colDef.forEach((element) => {
			if (element.field == field) {
				element.cellEditorParams.values = list;
			} else {
				if (element.children && element.children.length > 0) {
					let index = element.children.findIndex((x) => x.field == field);
					if (index != -1) {
						element.children[index].cellEditorParams.values = list;
					}
				}
			}
		});
	}
	/**Bulk edit functionality */
	bulkEditAction() {
		$('#bulk-edit').modal('show');
		this.updateProperty = '';
		this.enableBulkEdit = false;
	}
	/**set dynamic form components for bulk edit */
	setDynamicFields() {
		this.dynamicFields = [];
		this.bulkEditFields = this.reportFields.filter((x) => x.allowBulkEdit == true);
		let field: DynamicBase<any>[] = [];
		this.bulkEditFields.forEach((element) => {
			let formElementId;
			formElementId = element.fieldId;
			this._workListService
				.getMachformFields(this.formId, Number(formElementId))
				.refetch()
				.then(({ data: response }) => {
					let elementDetails = response['getFormElementDetails'];
					this.fieldValueList[element.fieldName] = elementDetails['options'];
					let fieldOptions = elementDetails['options'];
					let hasOtherOption: boolean;
					let option = [];
					if (element['valueType'] == 'text') {
						field.push(
							new TextboxQuestion({
								key: element.fieldName,
								value: '',
								fieldName: element.fieldName,
								label: element.fieldName,
								controlType: 'textbox',
								required: false,
								options: option,
								order: 1,
								show: true
							})
						);
					}
					if (fieldOptions.length > 0) {
						if (
							elementDetails['type'] == 'checkbox' ||
							elementDetails['type'] == 'radio' ||
							elementDetails['type'] == 'select'
						) {
							option = JSON.parse(JSON.stringify(fieldOptions));
							if (elementDetails['hasOtherOption'] == 1) {
								option.push({ optionId: 'Others', optionValue: elementDetails['otherLabelText'] });
								element.hasOtherOption = true;
							} else {
								element.hasOtherOption = false;
							}
						}

						if (elementDetails['type'] == 'checkbox') {
							option.forEach((element1) => {
								field.push(
									new CheckboxQuestion({
										key:
											element1.optionId == 'Others'
												? 'Others'
												: element1.optionValue.replace(/\s/g, ''),
										value: '',
										fieldName: element.fieldName,
										label: element1.optionValue,
										controlType: elementDetails['type'],
										required: false,
										order: 1,
										show: true
									})
								);
							});
						} else if (elementDetails['type'] == 'radio') {
							field.push(
								new RadioQuestion({
									key: element.fieldName,
									value: '',
									fieldName: element.fieldName,
									label: element.fieldName,
									controlType: elementDetails['type'],
									required: false,
									options: option,
									order: 1,
									show: true
								})
							);
						} else if (elementDetails['type'] == 'select') {
							field.push(
								new DropdownQuestion({
									key: element.fieldName,
									value: '',
									fieldName: element.fieldName,
									label: element.fieldName,
									controlType: 'dropdown',
									required: false,
									options: option,
									order: 1,
									show: true
								})
							);
						}
					}
					this.dynamicFields = field;
				});
		});
	}
	showFormElement(e) {
		this.enableBulkEdit = false;
		let selectedRow = this.gridApi.getSelectedRows();
		let formElementName;
		let index = this.bulkEditFields.findIndex((x) => x.fieldId == e.target.value);
		if (index != -1) {
			formElementName = this.bulkEditFields[index].fieldName;
		}
		this.updateData = [];
		this.updateData.push({
			rowData: selectedRow,
			worklistId: this.worklistid,
			formId: this.formId,
			entryId: selectedRow.map((x) => x.submissionID),
			elementId: e.target.value,
			elementType: formElementName,
			hasOtherOption: this.bulkEditFields[index].hasOtherOption,
			visibleToRoles: this.metaData.visibleToRoles
		});
		setTimeout(() => {
			this.enableBulkEdit = true;
		}, 100);
	}
	/**For the correct working of date sorting in ag grid */
	dateComparator(date1, date2) {
		let cdate1 = new Date(date1);
		date1 = moment.utc(cdate1).format('DD/MM/YYYY');
		let cdate2 = new Date(date2);
		date2 = moment.utc(cdate2).format('DD/MM/YYYY');
		let date1Number: number;
		if (date1 === undefined || date1 === null || date1.length !== 10) {
			date1Number = null;
		}
		var result = date1.substring(6, 10) * 10000 + date1.substring(3, 5) * 100 + date1.substring(0, 2);
		date1Number = result;
		let date2Number: number;
		if (date2 === undefined || date2 === null || date2.length !== 10) {
			date2Number = null;
		}
		var result = date2.substring(6, 10) * 10000 + date2.substring(3, 5) * 100 + date2.substring(0, 2);
		date2Number = result;
		if (date1Number === null && date2Number === null) {
			return 0;
		}
		if (date1Number === null) {
			return -1;
		}
		if (date2Number === null) {
			return 1;
		}
		return date1Number - date2Number;
	}
	getDatePicker() {
		function Datepicker() { }
		Datepicker.prototype.init = function (params) {
			// create the cell
			this.eInput = document.createElement('input');
			this.eInput.value = params.value;
			$(this.eInput).datepicker({
				dateFormat: 'mm/dd/yy',
				changeMonth: true,
				changeYear: true,
				yearRange: 'c-100:c+100'
			});
		};
		Datepicker.prototype.getGui = function () {
			return this.eInput;
		};
		Datepicker.prototype.afterGuiAttached = function () {
			this.eInput.focus();
			this.eInput.select();
		};
		Datepicker.prototype.getValue = function () {
			return this.eInput.value;
		};
		Datepicker.prototype.destroy = function () { };
		Datepicker.prototype.isPopup = function () {
			return false;
		};
		return Datepicker;
	}

	onCellClicked(params) {
		if (params.colDef['cellEvent'] === 'showDetail') {
			// let e = { rowData: params.data, actionField: "", actionMode: "", selectionMode: "", link: "" };
			if (params.value != '') {
				this.showDetailedView(params);
			}
		}
		if (params.colDef['cellEvent'] === 'showCellClickModal') {
			let e = { rowData: params.data, actionField: "", actionMode: "", selectionMode: "", link: "" };
			let type = params.column['colId'];
			this.showCellClickModal(e, type);
		}
		
	}
	papperWorkDetails(patientId, type,workflowId, patientEntryRequiredWorkflowId,
		bgColorRequired){
		let tenantId=[];
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		){
			tenantId.push(this._structureService.getCookie('crossTenantId'));
		} else{
			tenantId.push(this._structureService.getCookie('tenantId'))	;
		} 
		this.papperWorkList = [];
		if(this.metaData.callbackFunctionName.trim().toUpperCase() == 'LIAISONS') {
				  this.workflowId = this._constantService.entryTypeIds['LIAISON'];
				} else if(this.metaData.callbackFunctionName.trim().toUpperCase() == 'INTAKEFOLLOWUP'){
				  this.workflowId = this._constantService.entryTypeIds['INTAKE'];
				}
				
		let variables = {
			patientId: patientId,tenantIds: tenantId , type: type, workflowId: workflowId,
			patientEntryRequiredWorkflowId: patientEntryRequiredWorkflowId,
			bgColorRequired: bgColorRequired}
		this.showLoader = true;
		if(type == 'LIAISONMISC') {
			this._workListService.getMiscellaneousCompletedItems(variables).then((data) => {
				let papperWorkList = JSON.parse(JSON.stringify(data['checkLists'][0]));
				this._workListService.getMiscellaneousRequiredItems(variables).then((data) => {
					this.showLoader = false;
					papperWorkList.requiredItems = data['checkLists'][0].requiredItems;
					this.papperWorkList = papperWorkList;
				});
			});
		} else {
			this._workListService.getPaperWork(variables).then((data) => {
				this.showLoader = false;
				this.papperWorkList = data['checkLists'][0];
			});
		}
	}
	showCellClickModal(e, type) {
		let tenantId;
		this.cellClickDetails = e.rowData;
		// this.cellClickDetails = e.rowData;
		let patient = this.cellClickDetails.id
		let lovItemType;
		let patientEntryRequiredWorkflowId = null;
		let bgColorRequired = false;
		let workflowId = 0;
		this.checklistShowMultipleEntry = false;
		if(type =='intakePaperWorkStatus'){
			lovItemType = 'INTKPPRWRK';
			this.checklistTitle = 'Intake Follow Up Paperwork ';
			this.checklistItemTitle = 'Intake Paperwork';
			this.checklistRequiredTitle = '';
			this.checklistCompletedTitle = 'Items';
			workflowId = this._constantService.entryTypeIds['INTAKE'];
		} else if (type == 'insuranceVerificationStatus') {
			patientEntryRequiredWorkflowId = 3;
			lovItemType = 'ININSVER';
			this.checklistTitle = 'Intake Follow Up Insurance Verification Paperwork ';
			this.checklistItemTitle = 'Items';
			this.checklistRequiredTitle = '';
			this.checklistCompletedTitle = 'Intake';
			workflowId = this._constantService.entryTypeIds['INTAKE'];
			
		} else if (type == 'finalVerificationStatus') {
			this.checklistShowMultipleEntry = true;
			patientEntryRequiredWorkflowId = 3;
			bgColorRequired = true;
			lovItemType = 'ININSVER';	
			this.checklistShowMultipleEntry = true;
			this.checklistTitle = 'Intake Follow Up Final Insurance Verification ';
			this.checklistItemTitle = 'Intake Items';
			this.checklistRequiredTitle = '';
			this.checklistCompletedTitle = '';
			if (this.metaData.callbackFunctionName.trim().toUpperCase() == 'INTAKEFOLLOWUP') {
				workflowId = this._constantService.entryTypeIds['INTAKE'];
			} else {
				workflowId = this._constantService.entryTypeIds['INS_VERIFICATION'];
			}
		} else if (type =='intakeFinalReviewStatus'){
			lovItemType = 'INTKFINREV';
			this.checklistTitle = 'Intake Follow Up Final Review';
			this.checklistItemTitle = '';
			this.checklistRequiredTitle = '';
			this.checklistCompletedTitle = 'Items';
			workflowId = this._constantService.entryTypeIds['INTAKE'];
		} else if (type == 'insuranceVerificationItemStatus') {
			lovItemType = 'INSVERITEMS';
			this.checklistTitle = 'Insurance Verification Items';
			this.checklistItemTitle = '';
			this.checklistRequiredTitle = 'Required';
			this.checklistCompletedTitle = 'Completed';
			workflowId = this._constantService.entryTypeIds['INS_VERIFICATION'];
		} else if(type == 'miscPaperWorkStatus') {
			lovItemType = 'LIAISONMISC';
			this.checklistTitle = 'Miscellaneous';
			this.checklistItemTitle = 'Item';
			this.checklistRequiredTitle = 'Required';
			this.checklistCompletedTitle = 'Completed';
			workflowId = this._constantService.entryTypeIds['LIAISON'];;
		}
		// this.papperWorkList;

	
			this.papperWorkDetails(patient, lovItemType, workflowId, patientEntryRequiredWorkflowId,
				bgColorRequired);
		$('#cellClickModel').modal('show');
	}

	closeDynamicModal() {
		$('#cellClickModel').modal('hide');
	}


	convertDateFormat(params) {
		if (!params.value) {
			return '';
		}
		if (params.value || params.value != '') {
			const dateParts = params.value.toString().split('/');
			if (dateParts.length <= 1) {
				let newDate = new Date(Number(params.value));
				return moment.utc(newDate).format('MM/DD/YYYY');
				//return moment(newDate).format('MM/DD/YYYY');
			} else {
				return params.value;
			}
		} else {
			return '';
		}
	}
	convertTimeFormat(params) {
		if (!params.value) {
			return '';
		}
		if (params.value || params.value != '') {
			return moment(params.value).format('hh:mm:ss A');
			// return moment(params.value, ['HH:mm']);
			return (params.value);

		}
	}
	convertDateTimeFormat(params) {
		if (!params.value) {
			return '';
		}
		if (params.value || params.value != '') {
			let newDate = new Date(params.value);
			return moment.utc(newDate).format('MM/DD/YYYY hh:mm:ss A');
		}
	}
	/**custom filtering of past and due date */
	setRefillValue(type, value, field) {
		this.refillDateType = type;
		if (value != '') {
			this.filteringDate(value, field);
		}
	}
	/**custom filering function for past and due date filter */
	filteringDate(value, filterField) {
		if (filterField !== '' && value != '') {
			let dateFrom;
			let dateTo;
			if (this.refillDateType == 'due') {
				this.refillType = true;
				dateFrom = moment().format('YYYY-MM-DD');
				dateTo = moment().add(Number(value - 1), 'd').format('YYYY-MM-DD');
			} else {
				this.refillType = false;
				dateFrom = moment().subtract(value, 'd').format('YYYY-MM-DD');
				dateTo = moment().subtract(1, 'd').format('YYYY-MM-DD');
			}
			let filterModel = this.gridApi.getFilterModel();
			if (this.rowModelType == 'serverSide') {
				filterModel[filterField] = { type: 'inRange', dateFrom: dateFrom, dateTo: dateTo, filterType: 'date' };
				this.gridApi.setFilterModel(filterModel);
			} else {
				const statusFilterComponent = this.gridApi.getFilterInstance(filterField);
				statusFilterComponent.setModel({
					type: 'inRange',
					dateFrom: dateFrom,
					dateTo: dateTo
				});
				this.gridApi.onFilterChanged();
			}
		} else {
			this.selectedDays = '';
			this.refillType = true;
			this.refillDateType = 'due';
			const filterModel = this.gridApi.getFilterModel();
			let objKeys = Object.keys(filterModel);
			if (objKeys.findIndex((x) => x == filterField) != -1) {
				delete filterModel[filterField];
				this.gridApi.setFilterModel(filterModel);
			}
		}
	}
	/**Reload after edit data using bulk edit */
	getFormDataBasedRowModel() {
		if (this.rowModelType == 'clientSide') {
			this.getformData();
		} else {
			var datasource = ServerSideDatasource(this);
			this.gridApi.setServerSideDatasource(datasource);
			this.getWidgetCountServerSide();
		}
	}
	/**Get the form entries */
	getformData() {
		if (this.metaData.dataSource && this.metaData.dataSource == 'API') {
			/**Get the date using an API */
			let parameters = [];
			this.metaData.parameters.split('&').forEach((element) => {
				let key = element.substring(element.lastIndexOf('{{') + 2, element.lastIndexOf('}}'));
				if (key.toLowerCase() == 'tenantid') {
					element = element.replace('{{' + key + '}}', this._structureService.getCookie('tenantId'));
				}
				if (key.toLowerCase() == 'currentdate') {
					const userToTenantTimeRes = this._structureService.userToTenantTime();
					const date = moment()
						.add(userToTenantTimeRes[0], 'hours')
						.add(userToTenantTimeRes[1], 'm')
						.format('YYYYMMDD');
					element = element.replace('{{' + key + '}}', date);
				}
				if (key == 'patient_id') {
					let pIndex = this.mapFields.findIndex((x) => x.fieldName == key);
					element = element.replace('{{' + key + '}}', this.mapFields[pIndex].mapField);
				}
				if (key == 'staff_id') {
					let pIndex = this.mapFields.findIndex((x) => x.fieldName == key);
					element = element.replace('{{' + key + '}}', this.mapFields[pIndex].mapField);
				}
				if (key == 'parent_schedule_id') {
					let pIndex = this.mapFields.findIndex((x) => x.fieldName == key);
					element = element.replace('{{' + key + '}}', this.mapFields[pIndex].mapField);
				}
				if (key == 'formID') {
					const formId = this.metaData.associatedForm;
					element = element.replace('{{' + key + '}}', formId);
				}
				if (element == 'type=multiple') {
					this.apiType = 'multiple';
				} else {
					this.apiType = 'single';
					parameters.push(element);
				}
			});
			let graphqlQuery = '';
			let url = this.metaData.endpoint;
			if (this.metaData.graphqlApi) {
				let url = this.metaData.graphqlEndpoint;
				let fieldList = this.metaData.fieldList.split(',');
				let fieldString = '';
				fieldList.forEach(field => {
					if (field.includes('.')) {
						let colArray = field.split('.');
						let endString = '';
						colArray.forEach((element, index) => {
							fieldString = ` ${fieldString} ${element} `;
							if (index !== colArray.length - 1) {
								fieldString = ` ${fieldString} { `;
								endString = ` ${endString} } `;
							}

						});
						fieldString = ` ${fieldString} ${endString}  `;
					} else {
						fieldString = `${fieldString} ${field}`;
					}
				});
				let newQuery = this.metaData.parameters.replace('$fields', fieldString);
				//newQuery = newQuery.replace('$fields', fieldString);
				let variables = {};
				this._workListService.getWorklistDataUsingGraphQLAPI(url, newQuery, variables, this.prepareHeaderVariables()).then((data) => {
					this.prepareApiData(data);
				});
			} else {
				this._workListService.getWorklistDataUsingAPI(url, parameters.join('&'), graphqlQuery).then((data) => {
					this.prepareApiData(data);
				});
			}
		} else {
			/**Geting data from machform */
			this.formRowData = [];
			this._workListService
				.getWorkListFormData(this.formId, this.uniqueClass, '', '', this.formFieldFrom)
				.refetch()
				.then(({ data: response }) => {
					if (this.uniqueClass !== '') {
						this.formRowData = response['getFormDataWithUniqueID'];
					} else {
						this.formRowData = response['getFormData'];
					}
					if (this.formRowData) {
						if (this.formRowData.length > 0) {
							this.machFormIds = [];
							this.formDataList = [];
							let i = 1;
							let obj = {};
							this.formRowData.forEach((element) => {
								const formObj = {};
								element.elements.forEach((elem) => {
									formObj[elem.labelText] = elem.value;
									if (elem.valueType == 'radio' && elem.valueOther != '' && elem.value == '') {
										formObj[elem.labelText] = elem.valueOther;
									}
									if (elem.valueType == 'checkbox' && elem.valueOther != '') {
										if (formObj[elem.labelText] == '') {
											formObj[elem.labelText] = elem.valueOther;
										} else {
											formObj[elem.labelText] = formObj[elem.labelText] + ',' + elem.valueOther;
										}
									}
									if (i == 1) {
										obj[elem.labelText] = {
											id: elem.tid,
											elementType: elem.valueType,
											otherExist: elem.OtherExists
										};
									}
									if (elem.valueType == 'date') {
										/**Formating date for correcting filetring */
										if (
											elem.timestampForDate &&
											elem.timestampForDate != null &&
											elem.timestampForDate != ''
										) {
											let newDate = new Date(Number(elem.timestampForDate));
											formObj[elem.labelText] = moment.utc(newDate).format('MM/DD/YYYY');
										} else {
											formObj[elem.labelText] = '';
										}
									}
								});
								formObj['submissionID'] = element.submissionID;
								formObj['slno'] = i;
								formObj['action'] = '';
								formObj['callRecords'] = [{ Therapy: '', MOA: '', Drug: '' }];
								this.formDataList.push(formObj);
								i++;
							});
							this.machFormIds.push({ type: 'parent' });
							this.machFormIds.push(obj);
							this.rowData = this.formDataList;
							this.gridApi.hideOverlay();
							this.setDefaultWidgetConfig(this.rowData);
						} else {
							this.rowData = [];
							this.gridApi.hideOverlay();
							this.setDefaultWidgetConfig(this.rowData);
						}
					} else {
						this.rowData = [];
						this.columnDefs = [];
						this.setDefaultWidgetConfig(this.rowData);
					}
				});
		}
	}
	/** Prepare header variables for api */
	prepareHeaderVariables() {
		let headers = {};
		if (this.metaData.enableAuthorization === true) {
			if (this.metaData.authtype === "token") {
				headers = {
					authorizationtoken: this._structureService.getCookie('authID')
				}
				// if (this.metaData.authsource === "callback") { 
				// 	headers = this[`${this.metaData.authCallbackFunction}`]();

				// }
			}
		}
		return headers;
	}
	/*addHeaderToApi() {
		return {
			authorizationtoken: this._structureService.getCookie('authID')
		};
	}*/
	/**Set the count of filering result in dashboard widget in server side pagination */
	setDefaultWidgetConfigServerSide(fieldDetails, action) {
		let i = 0;
		this.dashboardWidgetField = '';
		this.dashboardWidgetFieldId = '';
		this.dashboardWidgets.forEach((element) => {
			if (i == 0) {
				this.dashboardWidgetField = element.formField;
				if (this.machFormIds.length > 0) {
					if (this.machFormIds[1][this.dashboardWidgetField]) {
						this.dashboardWidgetFieldId = this.machFormIds[1][this.dashboardWidgetField].id;
					}
				} else {
					const index = this.reportFields.findIndex((x) => x.fieldName == this.dashboardWidgetField);
					if (index != -1) {
						this.dashboardWidgetFieldId = this.reportFields[index].fieldId;
					}
				}
			}
			i++;
			if (this.widgetCounts[0][element.widgetValue.toLowerCase()]) {
				element.count = this.widgetCounts[0][element.widgetValue.toLowerCase()];
			} else {
				element.count = 0;
			}
		});
		if (
			action == 'edit' &&
			fieldDetails != '' &&
			this.dashboardWidgetField == fieldDetails.field &&
			fieldDetails.oldValue != fieldDetails.newValue
		) {
			const oldValueCount = this.widgetCounts[0][fieldDetails.oldValue.toLowerCase()];
			const newValueCount = this.widgetCounts[0][fieldDetails.newValue.toLowerCase()];
			let newIndex = this.dashboardWidgets.findIndex((x) => x.widgetValue == fieldDetails.newValue);
			if (newIndex != -1) {
				this.dashboardWidgets[newIndex].count = this.dashboardWidgets[newIndex].count + 1;
			}
			let oldIndex = this.dashboardWidgets.findIndex((x) => x.widgetValue == fieldDetails.oldValue);
			if (oldIndex != -1) {
				this.dashboardWidgets[oldIndex].count = this.dashboardWidgets[oldIndex].count - 1;
			}
			this.widgetCounts[0][fieldDetails.oldValue.toLowerCase()] = oldValueCount - 1;
			this.widgetCounts[0][fieldDetails.newValue.toLowerCase()] = newValueCount + 1;
			let index = this.dashboardWidgets.findIndex((x) => x.widgetValue == this.selectedWidget);
			if (index != -1) {
				this.widgetFiltering(
					this.dashboardWidgets[index].widgetValue,
					this.dashboardWidgets[index].formField,
					this.dashboardWidgets[index].hideColumns,
					this.dashboardWidgets[index].count
				);
			}
		}
		if (action == 'delete') {
			const oldCount = this.widgetCounts[0][fieldDetails.field.toLowerCase()];
			const allCount = this.widgetCounts[0]['all'];
			let newIndex = this.dashboardWidgets.findIndex((x) => x.widgetValue == fieldDetails.field);
			if (newIndex != -1) {
				this.dashboardWidgets[newIndex].count = this.dashboardWidgets[newIndex].count - 1;
			}
			let oldIndex = this.dashboardWidgets.findIndex((x) => x.widgetValue == 'All');
			if (oldIndex != -1) {
				this.dashboardWidgets[oldIndex].count = this.dashboardWidgets[oldIndex].count - 1;
			}
			this.widgetCounts[0][fieldDetails.field.toLowerCase()] = oldCount - 1;
			this.widgetCounts[0]['all'] = allCount - 1;
		}
		if (localStorage.getItem('selectedWidget')) {
			let index = this.dashboardWidgets.findIndex((x) => x.widgetValue == localStorage.getItem('selectedWidget'));
			if (index != -1) {
				this.widgetFiltering(
					this.dashboardWidgets[index].widgetValue,
					this.dashboardWidgets[index].formField,
					this.dashboardWidgets[index].hideColumns,
					this.dashboardWidgets[index].count
				);
			}
			localStorage.setItem('selectedWidget', '');
		}
	}
	/**Set the count of filering result in dashboard widget in client side pagination*/
	setDefaultWidgetConfig(rowData = []) {
		this.dashboardWidgets.forEach((element) => {
			this.dashboardWidgetField = element.formField;
			if (this.metaData.graphqlApi) {
				element.count = 0;
				let widgetCountData = this.widgetData.filter((item) => item.columnName === element.formField);
				if (widgetCountData.length > 0) {
					let countDetails = widgetCountData[0].values.filter((item) => item.value === element.widgetValue);
					if (countDetails.length > 0) {
						element.count = countDetails[0].count;
					}
					else if (countDetails.length == 0) {
						this.statusMessage = 'No Records Found';
					}
				}
				//element.count = this.getFilterCount(element.widgetValue, element.formField, rowData);
			} else {
				element.count = this.getFilterCount(element.widgetValue, element.formField, rowData);
				this.statusMessage = 'No Records Found';
			}
		});
		if (localStorage.getItem('selectedWidget') && this.metaData.graphqlApi == false) {
			
			let index = this.dashboardWidgets.findIndex((x) => x.widgetValue == localStorage.getItem('selectedWidget'));
			if (index != -1) {
				this.widgetFiltering(
					this.dashboardWidgets[index].widgetValue,
					this.dashboardWidgets[index].formField,
					this.dashboardWidgets[index].hideColumns,
					this.dashboardWidgets[index].count
				);
			}
			localStorage.setItem('selectedWidget', '');
		}
		if (this.selectedWidget != 'All' && this.selectedWidget != '' && this.rowModelType == 'clientSide') {
			let index = this.dashboardWidgets.findIndex((x) => x.widgetValue == this.selectedWidget);
			if (index != -1) {
				this.widgetFiltering(
					this.dashboardWidgets[index].widgetValue,
					this.dashboardWidgets[index].formField,
					this.dashboardWidgets[index].hideColumns,
					this.dashboardWidgets[index].count
				);
			}
		}
	}
	/**Get count of widget  */
	getFilterCount(filterValue, filterField, rowData) {
		if (filterValue === 'All') {
			return rowData.length;
		} else {
			const filteredData = rowData.filter((x) => x[filterField] === filterValue);
			return filteredData.length;
		}
	}
	/**Show recent data and history data */
	showAllEntries(type, event) {
		NProgress.start();
		this.uniqueClass = '';
		if (type == 'less') {
			this.showLess = true;
			this.showAll = false;
			this.uniqueClass = this.metaData.uniqueIdClass;
			this.selectedLoadingType = '';
		} else {
			this.selectedLoadingType = 'allRecords';
			this.showAll = true;
			this.showLess = false;
		}
		this.searchFieldText = '';
		this.gridApi.setFilterModel(null);
		this.selectedDays = '';
		if (this.dashboardWidgets.length > 0) {
			this.selectedWidget = this.dashboardWidgets[0].widgetValue;
		}
		if (this.rowModelType == 'serverSide') {
			var datasource = ServerSideDatasource(this);
			this.gridApi.setServerSideDatasource(datasource);
			this.gridApi.paginationGoToFirstPage();
		} else {
			this.getformData();
		}
		NProgress.done();
	}
	/**search function in client side pagination */
	searchFilterData(params) {
		if (params != '') {
			const filterModel = this.gridApi.getFilterModel();
			this.gridApi.setFilterModel(null);
			if (this.selectedWidget != 'All') {
				if (filterModel.hasOwnProperty(this.selectedWidgetField) == true) {
					const statusFilterComponent = this.gridApi.getFilterInstance(this.selectedWidgetField);
					statusFilterComponent.setModel({
						type: 'equals',
						filter: filterModel[this.selectedWidgetField].filter
					});
				}
			}
			this.gridApi.setQuickFilter(params);
		} else {
			this.searchFieldText = '';
		}
	}
	/**search function in server side pagination */
	searchBasedField(searchText) {
		this.searchFieldText = searchText.trim();
		this.advanceSearchFields = [];
		this.isShow = true;
		if (this.selectedSearchFields.length > 0) {
			if (this.selectedWidget == 'All') {
				this.gridApi.setFilterModel(null);
			}
			this.customSearch = true;
			this.customAdvanceSearch = false;
			this._workListService.advancedSearchConfig = {};
			//localStorage.setItem('advanceSearchFields','');
			setTimeout(() => {
				this.advanceSearchComp.advanceSearchForm.patchValue({
					firstName: '',
					lastName: '',
					patientId: '',
					hrPatStatus: '',
					locationId: '',
					siteId: '',
					team: '',
					hospitalDischargeDate: '',
					cprSocDate: '',
					insuranceRep: ''
				});
			}, 1000);
			//localStorage.setItem('searchForm', '');
			var datasource = ServerSideDatasource(this);
			this.gridApi.setServerSideDatasource(datasource);
			if(this.metaData.searchState == true && this.metaData.stateSavingMode == 'autoSave') {
				console.log('save state search');
				this.saveStatePrefrence();
			} else if(this.metaData.searchState == false) {
				this._workListService.singleSearchConfig = {searchText: this.searchFieldText, searchFields: this.selectedSearchFields};
			}
		}
	}
	advanceSearchBasedField(data) {
		this.customSearch = true;
		if (data['selectedFields'] && data['type'] != 'close') {
			if (this.selectedWidget == 'All') {
				this.gridApi.setFilterModel(null);
			}
			this.advanceSearchFields = data['selectedFields'];
			this.customAdvanceSearch = true;
			//localStorage.setItem('singleSearchFields','');
			this._workListService.singleSearchConfig = {};
			this.searchFieldText = '';
			if (data['type'] == 'reset') {
				this.customAdvanceSearch = false;
				//localStorage.setItem('advanceSearchFields', '');
				this._workListService.advancedSearchConfig = {};
				this.customSearch = false;
			}
			var datasource = ServerSideDatasource(this);
			this.gridApi.setServerSideDatasource(datasource);
		} else {
			this.isShow = true;
		}
	}

	clearSearch() {
		if (this.customSearch == true) {
			var datasource = ServerSideDatasource(this);
			this.gridApi.setServerSideDatasource(datasource);
			this.searchFieldText = '';
			//localStorage.setItem('singleSearchFields', '');
			this._workListService.singleSearchConfig = {};

		}
	}
	checkboxSelection(field) {
		if (this.selectedSearchFields.findIndex((x) => x.fieldId == field.fieldId) == -1) {
			this.selectedSearchFields.push(field);
		} else {
			this.selectedSearchFields = this.selectedSearchFields.filter((x) => x.fieldId != field.fieldId);
		}
	}
	onChangeSearchField(event, field) { }
	/**custom filtering function of dashboard widget */
	widgetFiltering(filterValue, filterField, columns, count) {
		//this.customSearch = false;
		localStorage.setItem('currentWidget', filterValue);
		this.selectedWidget = filterValue;
		this.selectedWidgetField = filterField;
		this.defaultWidgetField = filterField;
		this.defaultWidget = filterValue;
		if (this.previousHideColumns.length > 0) {
			this.previousHideColumns.forEach((element) => {
				this.gridColumnApi.setColumnVisible(element, true);
			});
		}
		this.hideColumns = columns && columns != null ? columns.split(',') : [];
		if (this.hideColumns.length > 0) {
			this.hideColumns.forEach((element) => {
				this.gridColumnApi.setColumnVisible(element, false);
			});
			this.previousHideColumns = this.hideColumns;
		} else {
			this.previousHideColumns = [];
		}
		if (filterValue == 'All') {
			filterValue = '';
		}
		this.getDataCount = true;
		if (filterField !== '') {
			const statusFilterComponent = this.gridApi.getFilterInstance(filterField);
			let filterModel = this.gridApi.getFilterModel();
			if (this.rowModelType == 'serverSide') {
				filterModel[filterField] = { type: 'equals', filter: filterValue, filterType: 'text' };
				this.gridApi.setFilterModel(filterModel);
			} else {
				statusFilterComponent.setModel({
					type: 'equals',
					filter: filterValue
				});
				this.gridApi.onFilterChanged();
				if (count == 0) {
					this.gridApi.showNoRowsOverlay();
				} else {
					this.gridApi.hideOverlay();
				}
			}
			if(this.metaData.enableSavingState == true && this.metaData.filterState == true && filterValue != '') {
				let widgetModel = (localStorage.getItem('widgetState') && localStorage.getItem('widgetState') != '') ? JSON.parse(localStorage.getItem('widgetState')) : [];
				let index = widgetModel.findIndex(x=> x.worklist == this.worklistid);
				  if(index != -1) {
					widgetModel[index].widgetState = filterValue;
				  } else {
					widgetModel.push({worklist: this.worklistid, widgetState: filterValue});
				  }
				localStorage.setItem('widgetState', JSON.stringify(widgetModel));
			  }
		} else {
			this.gridApi.setFilterModel(null);
		}
		this.gridApi.deselectAll();
	}

	/** inline cell editing and update the corresponding value */
	onCellValueChanged(e) {
		const field = e.colDef.field;
		const configIndex = this.reportFields.findIndex((x) => x.fieldName == field);
		const config = this.reportFields[configIndex];
		let newValue = e.newValue;
		if (config.valueType == 'date') {
			const cellDate = new Date(newValue);
			/*date format should be in yyyy-mm-dd for saving data to machform db */
			newValue = moment(cellDate).format('YYYY-MM-DD');
		}
		let params = [];
		let fieldValueList = this.list[e.colDef.field];
		if (fieldValueList) {
			let count: number;
			count = fieldValueList.length - 1;
			if (fieldValueList[Number(count)].type == 'internal') {
				const index = fieldValueList.findIndex((x) => x.optionValue == newValue);
				if (index != -1) {
					newValue = fieldValueList[index].optionId;
					if (config.valueType == 'checkbox') {
						params.push({ id: this.machFormIds[1][e.colDef.field].id + '_' + newValue, value: '1' });
						fieldValueList.forEach((element) => {
							if (element.optionValue && element.optionValue != e.newValue) {
								params.push({
									id: this.machFormIds[1][e.colDef.field].id + '_' + element.optionId,
									value: '0'
								});
							}
						});
					}
					if (this.machFormIds[1][e.colDef.field].otherExist == 1) {
						params.push({ id: this.machFormIds[1][e.colDef.field].id + '_other', value: '' });
					}
				}
			}
		}
		let formId;
		if (this.machFormIds[0].type == 'child') {
			formId = this.metaData.associatedForm;
		} else {
			formId = this.formId;
		}
		if (config.valueType != 'checkbox') {
			params.push({ id: this.machFormIds[1][e.colDef.field].id, value: newValue });
		}
		if (e.newValue != e.oldValue) {
			this._workListService
				.updateSingleFormData(params, formId, e.data.submissionID)
				.subscribe(({ data: response }) => {
					if (this.dashboardWidgetField == e.colDef.field) {
						if (this.rowModelType == 'clientSide') {
							this.setDefaultWidgetConfig(this.rowData);
						} else {
							let obj = { field: e.colDef.field, newValue: e.newValue, oldValue: e.oldValue };
							this.setDefaultWidgetConfigServerSide(obj, 'edit');
						}
					}
					var activityData = {
						activityName: 'Update Single Worklist Field',
						activityType: 'worklist forms',
						activityDescription:
							this.userData.displayName +
							' updated the field ' +
							e.colDef.field +
							' from ' +
							e.oldValue +
							' to ' +
							e.newValue +
							'of entry id ' +
							e.data.submissionID +
							'of form id' +
							formId
					};
					this._structureService.trackActivity(activityData);
					let sendData = {
						rowData: e.data,
						updateData: [{ updateField: e.colDef.field, updatedValue: e.newValue }],
						submissionId: [e.data.submissionID],
						formId: formId,
						worklistId: this.worklistid,
						users: [],
						action: 'update',
						clientId: this._structureService.socket.io.engine.id
					};
					this.sendWorklistUpdatePolling(sendData);
					if (this.metaData.showAlert == true) {
						const name = e.colDef.headerName.toLowerCase();
						let headerName = name.charAt(0).toUpperCase() + name.slice(1);
						setTimeout(function () {
							$.notify({ message: headerName + ' updated successfully.' }, { type: 'success' });
						}, 1000);
					}
					/**Send notification message */
					if (this.metaData.enableNotification == true) {
						let notificationConfig = {};
						let staffId = '';
						if (this.metaData.notificationLevel == 'workListLevel') {
							if (this.metaData.notifyRecipientOnValueChange == true) {
								if (e.data.hasOwnProperty('StaffId') == true) {
									staffId = e.data.StaffId;
								} else if (this.metaData.fieldForRecipient != '') {
									const index = this.reportFields.findIndex(
										(x) => x.fieldName == this.metaData.fieldForRecipient
									);
									const delimitter = this.reportFields[index].fieldValueDelimiter;
									const value = e.data[this.metaData.fieldForRecipient];
									if (delimitter != '') {
										if (delimitter.length == 1) {
											if (value.includes(delimitter)) {
												const originalValue = value.split(delimitter);
												staffId = originalValue[1];
											}
										} else if (delimitter.length == 2) {
											if (
												value.includes(delimitter.charAt(0)) &&
												value.includes(delimitter.charAt(1))
											) {
												let originalValue = value.split(delimitter.charAt(0));
												originalValue = originalValue[1].split(delimitter.charAt(1))[0];
												staffId = originalValue;
											}
										}
									}
								}
							}
							notificationConfig = {
								notifyingRoles: this.metaData.notifyRolesOnValueChange ? this.metaData.notifyRoles : '',
								notifyRecipient: staffId,
								notifyMode: {
									push: this.metaData.fieldChangePush,
									pushTemplate: this.metaData.fieldChangePushTemplate,
									sms: this.metaData.fieldChangeSms,
									smsTemplate: this.metaData.fieldChangeSmsTemplate,
									email: this.metaData.fieldChangeEmail,
									emailTemplate: this.metaData.fieldChangeEmailTemplate
								},
								data: e.data
							};
							this.sendNotificationMessage(notificationConfig);
						} else {
							if (config.enableNotification == true) {
								if (config.notifyRecipientOnValueChange == true) {
									if (e.data.hasOwnProperty('StaffId') == true) {
										staffId = e.data.StaffId;
									} else if (this.metaData.fieldForRecipient != '') {
										const index = this.reportFields.findIndex(
											(x) => x.fieldName == this.metaData.fieldForRecipient
										);
										const delimitter = this.reportFields[index].fieldValueDelimiter;
										const value = e.data[this.metaData.fieldForRecipient];
										if (delimitter != '') {
											if (delimitter.length == 1) {
												if (value.includes(delimitter)) {
													const originalValue = value.split(delimitter);
													staffId = originalValue[1];
												}
											} else if (delimitter == 2) {
												if (
													value.includes(delimitter.charAt(0)) &&
													value.includes(delimitter.charAt(1))
												) {
													let originalValue = value.split(delimitter.charAt(0));
													originalValue = originalValue[1].split(delimitter.charAt(1))[0];
													staffId = originalValue;
												}
											}
										}
									}
								}
								notificationConfig = {
									notifyingRoles:
										config.notifyRolesOnValueChange == true ? this.metaData.notifyRoles : '',
									notifyRecipient: staffId,
									notifyMode: {
										push: config.push,
										pushTemplate: config.pushTemplate,
										sms: config.sms,
										smsTemplate: config.smsTemplate,
										email: config.email,
										emailTemplate: config.emailTemplate
									},
									data: e.data
								};
								if (config.notifySpecificValue == true) {
									if (e.newValue.toLowerCase() == config.notificationFieldValue.toLowerCase()) {
										this.sendNotificationMessage(notificationConfig);
									}
								} else {
									this.sendNotificationMessage(notificationConfig);
								}
							}
						}
					}
				});
		}
	}
	/**for retaining sorting of entries using submissionID */
	sortChanged(params) {
		if (params.api.getSortModel().length == 0 && this.rowModelType == 'clientSide') {
		  params.api.setSortModel([{ colId: 'submissionID', sort: 'desc' }]);
		}
		autosizeHeaders(params);
		// if(this.metaData.sortState == true) {
		//   let sortModel = (localStorage.getItem('sortState') && localStorage.getItem('sortState') != '') ? JSON.parse(localStorage.getItem('sortState')) : [];
		//   let index = sortModel.findIndex(x=> x.worklist == this.worklistid);
		//   if(index != -1) {
		// 	sortModel[index].sortState = params.api.getSortModel();
		//   } else {
		// 	sortModel.push({worklist: this.worklistid, sortState: params.api.getSortModel()});
		//   }
		//   localStorage.setItem('sortState', JSON.stringify(sortModel));
		// }
		if(this.metaData.sortState == true  && this.metaData.stateSavingMode == 'autoSave' && this.setSortState == false) {
			console.log('sort save');
			this.saveStatePrefrence();
		}
	}
	onFilterChanged(params) {
		// if(this.metaData.enableSavingState == true && this.metaData.filterState == true) {
		//   let filterModel = (localStorage.getItem('filterState') && localStorage.getItem('filterState') != '') ? JSON.parse(localStorage.getItem('filterState')) : [];
		//   let index = filterModel.findIndex(x=> x.worklist == this.worklistid);
		// 	if(index != -1) {
		// 	  filterModel[index].filterState = params.api.getFilterModel();
		// 	} else {
		// 	  filterModel.push({worklist: this.worklistid, filterState: params.api.getFilterModel()});
		// 	}
		//   localStorage.setItem('filterState', JSON.stringify(filterModel));
		// }
		if(this.metaData.filterState == true && this.metaData.stateSavingMode == 'autoSave' && this.setFilterState == false) {
			console.log('filter save');
			this.saveStatePrefrence();
		}
	  }
	onBodyScrollEvent(event) {
		if (event.direction == 'horizontal') {
			autosizeHeaders(event);
		}
	}
	onColumnResized(event) {
		autosizeHeaders(event);
		if (event.finished) {
			this.gridApi.resetRowHeights();
		}
	}
	/**For make the ag grid responsive */
	onGridSizeChanged(params) {
		var gridWidth = document.getElementById('grid-wrapper').offsetWidth;
		var columnsToShow = [];
		var columnsToHide = [];
		var totalColsWidth = 0;
		var allColumns = params.columnApi.getAllColumns();
		for (var i = 0; i < allColumns.length; i++) {
			let column = allColumns[i];
			totalColsWidth += column.getMinWidth();
			if (totalColsWidth > gridWidth) {
				columnsToHide.push(column.colId);
			} else {
				columnsToShow.push(column.colId);
			}
		}
		params.api.sizeColumnsToFit();
	}
	
	/**Grid ready function */
	onGridReady(params) {
		autosizeHeaders(params);
		this.gridApi = params.api;
		this.gridColumnApi = params.columnApi;
		/**Show our own loading message */
		this.gridApi.showLoadingOverlay();
		setTimeout(function () {
			params.api.resetRowHeights();
		}, 500);
		window.addEventListener('resize', function () {
			setTimeout(function () {
				params.api.resetRowHeights();
			});
		});
		this.reportFields.forEach((element) => {
			if (element.clearFilter == true) {
				let filterInstance = this.gridApi.getFilterInstance(element.fieldName);
				if (filterInstance) {
					filterInstance.eClearButton.addEventListener('click', () => {
						this.gridApi.onFilterChanged();
					});
				}
			}
		});
		this.gridApi.setHeaderHeight(35);
		this.gridApi.setColumnDefs(this.columnDefs);
		/**Check the type of loading data and call function accordingly. */
		// let userDetails = JSON.parse(localStorage.getItem('userDetails'));
		// if(this.metaData.tenantType != 'masterTenant' && (userDetails.tenantType == "Master" || userDetails.organizationMasterId != '')) {
		/**check master tenant id if set master tenant config in worklist*/
		// 	swal(
		// 		{
		// 		  title: '',
		// 		  text: "No master teannt associated with this tennant",
		// 		  type: 'warning',
		// 		  confirmButtonClass: 'btn-warning',
		// 		  confirmButtonText: 'Ok',
		// 		  closeOnConfirm: true
		// 		},
		// 		() => {
	  
		// 		});
		// 		this.rowData = [];
		// 		this.setDefaultWidgetConfig(this.rowData);
		// 		this.gridApi.showNoRowsOverlay();
		// } else {
			if (this.metaData.enableSavingState && this.metaData.enableSavingState == true) {
				console.log('state: enable Saving State');
				this.defaultColumnState = this.gridColumnApi.getColumnState();
				this.defaultSortState = this.gridApi.getSortModel();
				this.defaultFilterState = this.gridApi.getFilterModel();
				let index = this._sharedService.worklistState.findIndex(x => x.worklistId == this.worklistid);
				if (index == -1) {
					if (this.metaData.stateSavingPreference && this.metaData.stateSavingPreference == 'userProfile') {
						console.log('state: user profile');
						let variable = { 'userId': Number(this.userData.userId), 'object_id': Number(this.worklistid) };
						this._workListService.getWorklistStatePreference(variable).then((data) => {
							let response = data['getSessionTenant'].getUserPeference;
							console.log('response', response.status);
							if (response.status == 200) {
								console.log('state: state details', response);
								if (response.data.length > 0) {
									console.log('state: data', response.data);
									this.worklistState = JSON.parse(response.data[0].meta);
									this.userPreferenceId = response.data[0].id;
									this._sharedService.userWorklistPrefrenceIds.push({ 'worklistId': this.worklistid, userPreferenceId: response.data[0].id });
									console.log('state: userPreferenceId', this.userPreferenceId);
									this.setStatePreferencetoGrid(this.worklistState, 'userProfile');		
								} else {
									this.loadDashboardData();
								}
							} else {
								this.loadDashboardData();
							}
						}).catch((err) => {
							this.loadDashboardData();
						});
					}
					else {
						this.loadDashboardData();
					}
				} else {
					console.log('worklist state cache', this._sharedService.worklistState[index].worklistState);
				  console.log('state: from cache');
				  let pIndex = this._sharedService.userWorklistPrefrenceIds.findIndex(x=> x.worklistId == this.worklistid);
				  if(pIndex != -1) {
					this.userPreferenceId = this._sharedService.userWorklistPrefrenceIds[pIndex].userPreferenceId;
				  }
				  this.setStatePreferencetoGrid(this._sharedService.worklistState[index].worklistState,'cache');
				}
			} else {
				this.loadDashboardData();
			}
			
		//}
		this.onGridSizeChanged(params);
	}
	loadDashboardData() {
		if (this.rowModelType == 'serverSide') {
			if (!this.apiCallTriggered) {
				this.apiCallTriggered = true;
				this.getDataCount = true;
				var datasource = ServerSideDatasource(this);
				this.gridApi.setServerSideDatasource(datasource);
			}
		} else {
			let singleSearchFields = this._workListService.singleSearchConfig;
			if(singleSearchFields && Object.keys(singleSearchFields).length > 0) {
				this.searchFilterData(this.searchFieldText);
			}
			this.getformData();
		}
	}
	setStatePreferencetoGrid(worklistState, type) {
		this.setColumnState = true;
		this.setSortState = true;
		this.setFilterState = true;
		if(worklistState['columnState'] && Object.keys(worklistState['columnState']).length > 0 && this.metaData.columnState == true) {
			if (this.gridColumnApi.getColumnState().length > worklistState['columnState'].length) {
        		let defState = this.gridColumnApi.getColumnState();
        		let i = 0;
        		defState.forEach((state) => {
          			if (worklistState['columnState'].findIndex((x) => x.colId == state.colId) == -1) {
            			worklistState['columnState'].splice(i, 0, state);
          			}
          			i++;
        		});
      		}
		  	if(type != 'clear') {
				this.defaultColumnState = this.gridColumnApi.getColumnState();
		  	}
		  	this.gridColumnApi.setColumnState(worklistState['columnState']);
		}
		if(worklistState['sortState'] && Object.keys(worklistState['sortState']).length > 0 && this.metaData.sortState == true) {
		  if(type != 'clear') {
			this.defaultSortState = this.gridApi.getSortModel();
		  }
		  this.gridApi.setSortModel(worklistState['sortState']);
		}
		if(worklistState['filterState'] && Object.keys(worklistState['filterState']).length > 0 && this.metaData.filterState == true) {
		  if(type != 'clear') {
			this.defaultFilterState = this.gridApi.getFilterModel();
		  }
		  this.gridApi.setFilterModel(worklistState['filterState']);
		}
		if(worklistState['widgetState'] && worklistState['widgetState'] != '' && this.metaData.filterState == true) {
		  let widgetState = worklistState['widgetState'];
		  if (widgetState && widgetState != '') {
			this.selectedWidget = widgetState;
			this.defaultWidget = widgetState;
			this.defaultWidgetField = this.dashboardWidgets[0].formField;
			localStorage.setItem('selectedWidget', this.selectedWidget);
		  }
		}
		if(worklistState['searchState'] && Object.keys(worklistState['searchState']).length > 0 && this.metaData.searchState == true) {
		  let searchState = worklistState['searchState'];
		  if (searchState && Object.keys(searchState).length > 0) {
			this.searchFieldText = searchState.selectedSearchFieldText ? searchState.selectedSearchFieldText : '';
			this.selectedSearchFields = searchState.selectedSearchFields ? searchState.selectedSearchFields : [];
			this.selectedSearchFields.forEach(element => {
			  $('#checkbox' + element.fieldId).prop("checked", true);
			});
			if (this.rowModelType != 'serverSide') {
			  this.searchFilterData(this.searchFieldText);
			}
			this.customSearch = true;
			this.isShow = true;
		  }
		}
		if(type == 'userProfile') {
		  this._sharedService.worklistState.push({'worklistId': this.worklistid, 'worklistState': worklistState});
		}
		
		this.loadDashboardData();
	}
	clearStatePrefrence() {
		if(this.userPreferenceId != 0) {
		  swal({
			title: 'Are you sure?',
			text: 'Do you want to reset the worklist session state?',
			type: 'warning',
			showCancelButton: true,
			cancelButtonClass: 'btn-default',
			confirmButtonClass: 'btn-warning',
			confirmButtonText: 'Ok',
			closeOnConfirm: true
		  }, () => {
			if(this.metaData.stateSavingPreference && this.metaData.stateSavingPreference == 'userProfile') {
			 let variable = {
				category: 'worklist_center',
				object_id: Number(this.worklistid),
				profile_key: 'worklist_state'
			  }
			  console.log(variable);
			  this._workListService.deleteWorklistStatePreference(variable).then(
				  (data)=> {
					console.log(data);
					if(data['deleteUserPreference'] && data['deleteUserPreference'].status == 200) {
					  let worklistState = {
						columnState : this.defaultColumnState,
						filterState: this.defaultFilterState,
						sortState: this.defaultSortState,
						searchState : {
									  selectedSearchFieldText : "",
									  selectedSearchFields : this.defaultSelectedSearchFields
									  }
					  }
					  console.log('state: clear state');
					  console.log('state: default worklist state', worklistState);
					  this.setStatePreferencetoGrid(worklistState, 'clear');
					  this.gridApi.setColumnDefs(this.columnDefs);
					  this.userPreferenceId = 0;
					  let index = this._sharedService.worklistState.findIndex(x=> x.worklistId == this.worklistid);
					  if(index != -1) {
						this._sharedService.worklistState.splice(index, 1);
					  }
					  let pIndex = this._sharedService.userWorklistPrefrenceIds.findIndex(x=> x.worklistId == this.worklistid);
					  if(pIndex != -1) {
						this._sharedService.userWorklistPrefrenceIds.splice(pIndex, 1);
					  }
					  setTimeout(function () {
						$.notify({ message: '<strong>Success</strong>: Worklist session state reset.' }, { type: 'success' });
					  }, 1000);
					  var activityData = {
						activityName: 'Reset worklist state preference',
						activityType: 'Worklist state preference',
						activityDescription: this.userData.displayName + ' reset the worklist state preference of '+ this.heading+'('+ this.worklistid+') from user profile.',
					  };
					  this._structureService.trackActivity(activityData);
					} else {
					  setTimeout(function () {
						$.notify({ message: 'Error occured.Please try again later.' }, { type: 'danger' });
					  }, 1000);
					}
				  },
				  () => {
					setTimeout(function () {
					  $.notify({ message: 'Error occured.Please try again later.' }, { type: 'danger' });
					}, 1000);
			  });
			} else {
			  let index = this._sharedService.worklistState.findIndex(x=> x.worklistId == this.worklistid);
			  if(index != -1) {
				this._sharedService.worklistState.splice(index, 1);
			  }
			}
		  });
		} else {
		  setTimeout(function () {
			$.notify({ message: '<strong>Warning</strong>: No worklist session state available in your profile.' }, { type: 'warning' });
		  }, 1000);
		}
	  }
	saveStatePrefrence() {
		let columnState;
		let filterState;
		let sortState;
		let searchState = {};
		let widgetState = '';
		if(this.metaData.columnState && this.metaData.columnState == true) {
		  columnState = this.gridColumnApi.getColumnState();
		}
		if(this.metaData.filterState && this.metaData.filterState == true) {
		  filterState = this.gridApi.getFilterModel();
		  widgetState = this.selectedWidget;
		}
		if(this.metaData.sortState && this.metaData.sortState == true) {
		  sortState = this.gridApi.getSortModel();
		}
		if(this.metaData.searchState && this.metaData.searchState == true) {
		  if(this.searchFieldText != "" && this.selectedSearchFields.length > 0) {
			searchState = { selectedSearchFieldText: this.searchFieldText, selectedSearchFields: this.selectedSearchFields };
		  } else {
			searchState = {};
		  }
		}
		let worklistState = {'columnState': columnState, 'filterState': filterState, 'sortState': sortState, 'searchState': searchState, 'widgetState': widgetState};
		if(this.metaData.stateSavingPreference && this.metaData.stateSavingPreference == 'userProfile') {
		  let variable = {
			userId: Number(this.userData.userId),
			category: 'worklist_center',
			meta: `${JSON.stringify(worklistState)}`,
			object_id: Number(this.worklistid),
			profile_key: 'worklist_state',
			tenantId: Number(this._structureService.getCookie('tenantId')),
			modifiedBy: Number(this.userData.userId)
		  }
		//   if(this.userPreferenceId != 0){
		// 	variable['id'] = this.userPreferenceId;
		//   }
		  this._workListService.saveWorklistStatePreference(variable).then(
			(data)=> {
			if(data['manageUserPreference'] && data['manageUserPreference'].status == 200) {
			  this.userPreferenceId = data['manageUserPreference'].data.id;
			  this._sharedService.userWorklistPrefrenceIds.push({'worklistId':this.worklistid, userPreferenceId: data['manageUserPreference'].data.id});
			  if(this.metaData.stateSavingMode == 'manualSave') {
				setTimeout(function () {
				  $.notify({ message: '<strong>Success</strong>: Worklist session state saved' }, { type: 'success' });
				}, 1000);
			  }
			  var activityData = {
				activityName: 'Save worklist state preference',
				activityType: 'Worklist state preference',
				activityDescription: this.userData.displayName + ' saved the worklist state preference of '+ this.heading+'('+ this.worklistid+') to user profile with meta of '+ JSON.stringify(worklistState),
			  };
			  this._structureService.trackActivity(activityData);
			} else {
			  setTimeout(function () {
				$.notify({ message: 'Error occured.Please try again later.' }, { type: 'danger' });
			  }, 1000);
			}
		  },
		  () => {
			setTimeout(function () {
			  $.notify({ message: 'Error occured.Please try again later.' }, { type: 'danger' });
			}, 1000);
		  });
		}
		let index = this._sharedService.worklistState.findIndex(x=> x.worklistId == this.worklistid);
		if(index != -1) {
		  this._sharedService.worklistState[index].worklistState = worklistState;
		} else {
		  this._sharedService.worklistState.push({'worklistId': this.worklistid, 'worklistState': worklistState});
		}
		if(this.metaData.stateSavingPreference == 'cache' && this.metaData.stateSavingMode == 'manualSave') {
		  setTimeout(function () {
			$.notify({ message: '<strong>Success</strong>: Worklist session state saved' }, { type: 'success' });
		  }, 1000);
		}
	}
	/**Check whether show action buttons and bulk edit based on the selected row count */
	onSelectionChanged(event) {
		let type;
		if (event.columnApi.columnController.columnDefs == this.columnDefs) {
			type = 'parent';
		} else {
			type = 'child';
		}
		this.rowCount = event.api.getSelectedNodes().length;
		this.selectedRowDetails = event.api.getSelectedRows();
		if (this.rowCount == 0) {
			this.hideActionButtons = true;
		} else {
			let privileges = Object.keys(this._structureService.privileges);
			let userRole = this._structureService.getCookie('userRole');
			let rowData = event.api.getSelectedNodes()[0].data;
			let actionFields = [];
			let indices = [];
			this.singleRowActions.forEach((element, i) => {
				/**Check privilege for showing action button */
				if (element.enableIntegration == true) {
					this.enableIntegrationforAction = true;
				}

				//  }
				if (element.selectionMode != type) {
					element.disable = true;
				} else if (this.rowCount > 1 && element.actionMode == 'single row') {
					element.disable = true;
				} else {
					if (
						(element.actionPrivileges != '' && privileges.indexOf(element.actionPrivileges) != -1) ||
						element.actionPrivileges == '' ||
						typeof element.actionPrivileges == 'undefined'
					) {
						if (element.actionField && element.actionField != '') {
							actionFields.push({ associatedField: element.actionField, fieldValues: element.fieldValues });
						} else if (element.actionFields) {
							actionFields = element.actionFields;
						}

						if (actionFields.length > 0) {
							let loginUserId = this.userData.userId;
							let loginDisplayName = this.userData.displayName;
							let trueCount = 0;
							actionFields.forEach((field) => {
								if (rowData.hasOwnProperty(field.associatedField)) {
									if (
										field.fieldValues &&
										field.fieldValues
											.split(',')
											.findIndex((x) => x.trim().toUpperCase() == rowData[field.associatedField].toString().trim().toUpperCase()) != -1
									) {
										if (element.showOnlyLoginUser == true) {
											if (
												rowData[element.loginUserMatchField] == loginUserId ||
												rowData[element.loginUserMatchField] == loginDisplayName
											) {
												trueCount++;
											}
										} else {											
											trueCount++;
										}
									}
								}
								if (field.fieldValues && field.fieldValues == 'Any') {
									if (rowData[field.associatedField]) {
										trueCount++;
									}
								}
								if (field.fieldValues && field.fieldValues == 'not_null') {
									if (rowData[field.associatedField] != '') {
										trueCount++;
									}
								}
							});
							if(actionFields.length == trueCount) {
								element.disable = false;
							} else {
								element.disable = true;
							}
						} else {
							element.disable = false;
						}
					}
				}
			});
			// if (indices.length) {
			// 	indices.forEach((i) => {
			// 		this.singleRowActions[i].disable = true;
			// 	});
			// }
			if (this.btnBehaviour == false) {
				this.hideActionButtons = false;
				if (Number(this.rowCount) > 1) {
					this.hideBulkEdit = false;
				} else {
					this.hideBulkEdit = true;
				}
			} else {
				this.hideActionButtons = true;
			}
		}
	}
	/**Single button action */
	singleBtnAction(action) {
		let selectedData = this.selectedRowDetails;
		let callbck = action.callbackfunction;
		let link = action.actionLink;
		if (selectedData.length > 0) {
			this.hideActionButtons = false;
			let e = {
				rowData: selectedData[0],
				actionField: action.actionField,
				actionMode: action.actionMode,
				selectionMode: action.selectionMode,
				mapField: action.mapField,
				actionConfig: action
			};
			if (link == '' || link == null) {
				if (callbck === 'editEntry') {
					this.editEntry(e);
				}
				if (callbck === 'deleteEntry') {
					this.deleteEntry(e);
				}
				if( callbck === 'recallEntry') {
					this.recallEntry(e);
				}
				if( callbck === 'addNote') {
					this.addBlogNote(e);
				}
				if( callbck === 'unlockPatient') {
					this.unlockPatient(e);
				}
			} else {
				this.goToActionLink(link);
			}
		} else {
			this.hideActionButtons = true;
		}
	}
	/**Short link action */
	goToActionLink(actionLink) {

		this.router.navigate([`${actionLink}`]);
	}
	goToHome() {
		this.router.navigate(['apps', this.guid]);
	}
	/**Go to Pah page */
	goToPahPage(e) {
		this.router.navigate(['pah', e.rowData.patient_id]);
	}
	prepareApiData(data) {
		let rowData: any = data;
		if (this.metaData.graphqlApi) {
			let keyArray = Object.keys(data['data']);
			rowData = data['data'][keyArray[0]].data;
			this.widgetData = data['data'][keyArray[0]].filterCount;
		}
		if (this.metaData.worklistType == 'single') {
			this.rowData = rowData;
		} else {
			rowData.forEach(element => {
				if (element.formData) {
					let formDataList = [];
					let objKeys = Object.keys(element.formData);
					objKeys.forEach(keys => {
						if (element.formData[keys]) {
							let formObj = {};
							let i = 1;
							let obj = {};
							element.formData[keys].forEach(elem => {
								let labelText = '';
								if (elem.label.lastIndexOf('{{') == -1) {
									labelText = elem.label;
									if (i == 1) {
										obj[elem.label] = { 'id': elem.element_id, 'elementType': elem.element_type };
									}
								} else {
									let key = elem.label.substring(elem.label.lastIndexOf('{{') + 2, elem.label.lastIndexOf('}}'));
									labelText = key;
									if (i == 1) {
										obj[key] = { 'id': elem.element_id, 'elementType': elem.element_type };
									}
								}
								if (elem.element_type == 'date' && elem.value && elem.value != null && elem.value != '') {
									let newDate = new Date(Number(elem.value) * 1000);
									formObj[labelText] = moment.utc(newDate).format('MM/DD/YYYY');
								} else {
									formObj[labelText] = '';
								}
							});
							this.machFormIds.push({ 'type': 'child' });
							this.machFormIds.push(obj);
							i++;
							formObj['submissionID'] = Number(keys);
							formDataList.push(formObj);
						}
					});
					element.callRecords = formDataList;
				} else {
					element.callRecords = element.day_wise_time_range;
				}
			});
			this.rowData = rowData;
		}
		this.setDefaultWidgetConfig(this.rowData);
	}

	/**Edit action  */
	editEntry(e) {
		const editRowDetails = e.rowData;
		this._intakeService.activePatientDetails = e.rowData;
		this._intakeService.currentPatient = editRowDetails['id'];
		localStorage.setItem('selectedWidget', this.selectedWidget);
		localStorage.setItem('selectedLoadingType', this.selectedLoadingType);
		localStorage.setItem('currentDashboard', this.route.snapshot.paramMap.get('worklistid'));
		this.router.navigate([
			`apps/${this.guid}/${this.route.snapshot.paramMap.get('worklist')}/details/${this.route.snapshot.paramMap.get('worklistid')}/${editRowDetails['id']}`
		]);
	}
openInNewTab()
{
	localStorage.setItem('advanceSearchFields',JSON.stringify(this._workListService.advancedSearchConfig["selectedSearchFields"]));
	localStorage.setItem('searchForm',JSON.stringify(this._workListService.advancedSearchConfig["searchForm"]));
window.open(
			`#/apps/${this.guid}/${this.route.snapshot.paramMap.get('worklist')}/${this.route.snapshot.paramMap.get('worklistid')}`
		);
}


	closeBlogNoteModal() {
		$('#note-modal').modal('hide');
		this.showModalComponent = false;
	}
	updateBlogList() {
		this.addBlogNote(this.rowDetails, 'refresh');
	}
	addBlogNote(e, loading = '') {
		// if(this.dynamicData && Object.keys(this.dynamicData).length > 0) {
		// 	this.setBlogNoteModal(e);
		// } else {
			this.rowDetails = e;
			this._workListService.getWorkLists().then((data) => {
				data['getSessionTenant'].formWorklists.forEach(element => {
					let newJson = element.description;
					newJson = newJson.replace(/'/g, '"');
					let metaData = JSON.parse(newJson);
					if(metaData.callbackFunctionName == 'blogNote') {
						let blogNoteworklistDetails = [];
						blogNoteworklistDetails.push(metaData);
						this.dynamicData = { "patientId": '', 'filterLiaisonsDetails': blogNoteworklistDetails,'tabName': this.route.snapshot.paramMap.get('worklist') + ' dashboard', "pahworklistId": element.id,"loading":loading };
						this.integrationSettings = (element.integrationSettings && element.integrationSettings != null) ? element.integrationSettings : [];
						this.worklistMenuName = blogNoteworklistDetails[0].nameOfDesktopMenu;
					}
				});
				this.setBlogNoteModal(e);
			});
		// }
	}
	setBlogNoteModal(e){
		const editRowDetails = e.rowData;
		this.showModalComponent = true;
		this.patientId = editRowDetails['id'];
		this.dynamicData['patientId'] = this.patientId;
		this.worklistEditData = e.rowData;
		this.parentWorklistName = this.route.snapshot.paramMap.get('worklist');
		this.noteCatId = 2; //for blog note
		let callback = this.metaData.callbackFunctionName != null ? this.metaData.callbackFunctionName.trim().toUpperCase() : '';
		if (callback == 'LIAISONS') {
			this.workflowId = this._constantService.entryTypeIds['LIAISON'];
		} else if (callback == 'INTAKEFOLLOWUP') {
			this.workflowId = this._constantService.entryTypeIds['INTAKE'];
		} else if (callback == 'INITIALINSURANCEVERIFICATION') {
			this.workflowId = this._constantService.entryTypeIds['INITIAL_INS'];
		} else if (callback == 'INSURANCEVERIFICATIONFOLLOWUP') {
			this.workflowId = this._constantService.entryTypeIds['INS_VERIFICATION'];
		} else if (callback == 'PHARMACYFOLLOWUP') {
			this.workflowId = this._constantService.entryTypeIds['PHARMACY_FOLLOWUP'];
		}
		$('#note-modal').modal('show');
	}
	recallEntry(e) {
		swal(
			{
				title: 'Are you sure?',
				text: 'You are going to recall this patient',
				type: 'warning',
				showCancelButton: true,
				cancelButtonClass: 'btn-default',
				confirmButtonClass: 'btn-warning',
				confirmButtonText: 'Ok',
				closeOnConfirm: true
			},
			() => {
				const editRowDetails = e.rowData;
				let count = this.selectedRowDetails.length;
				let patientId = this.selectedRowDetails.map(x=> x.id);
				let variable = {
					id: patientId, 
					workflowType: this.workflowType,
					createdBy: this.userData.userId
				};
				this._workListService.recallPatient(variable).subscribe(({ data: response }) => {
					/**Activity tracking */
					var activityData = {
						activityName: 'Recall Process',
						activityType: 'Recall a patient',
						activityDescription: this.userData.displayName + ' initiated recall process for the patient ' + editRowDetails.firstName + ' ' + editRowDetails.lastName + ' with MRN ' + editRowDetails.patientId + ' from ' + this.metaData.nameOfDesktopMenu + ' dashboard',
					};
					if(count > 1){
						activityData.activityDescription = this.userData.displayName + ' initiated recall process for the patients ' + this.selectedRowDetails.map(x=> x.name).toString() + ' with MRN ' + this.selectedRowDetails.map(x=> x.patientId).toString() + ' from ' + this.metaData.nameOfDesktopMenu + ' dashboard'
					}	
					this._structureService.trackActivity(activityData);
					/** */
					if (this.rowModelType == 'clientSide') {
						this.rowData = this.rowData.filter((x) => x.id != editRowDetails.id);
						this.gridApi.setRowData(this.rowData);
						this.setDefaultWidgetConfig(this.rowData);
					} else {
						this.getDataCount = true;
						var datasource = ServerSideDatasource(this);
						this.gridApi.setServerSideDatasource(datasource);
					}
					this.gridApi.deselectAll();
				});
			});
	}
	unlockPatient(e) {
		let popupText = '';
		const zone = timezone.name();
		const rowDetails = e.rowData;
		if (Number(this.userData.userId) === Number(rowDetails['lockedUserId'])) {
			popupText = "Patient Record " + rowDetails['name'] + " is locked by you at " + momentTz.tz(rowDetails['lockedAt'], zone).format('MM/DD/YYYY h:mm:ss A') + ". Do you want to unlock? ";
		} else {
			popupText = "Patient Record " + rowDetails['name'] + " is locked by " + rowDetails['lockedByUser'] + " at " + momentTz.tz(rowDetails['lockedAt'], zone).format('MM/DD/YYYY h:mm:ss A') + ". " + rowDetails['lockedByUser'] + " may lose the changes. Do you want to unlock? ";
		}
		swal({
			title: '',
			text: popupText,
			type: 'warning',
			showCancelButton: true,
			cancelButtonClass: 'btn-default',
			confirmButtonClass: 'btn-warning',
			confirmButtonText: 'Ok',
			closeOnConfirm: true
		},
			(confirm) => {
				if(confirm === true) {
					/**Call unlock patient api */
					this.unlockPatientData(rowDetails);
				} else {
					const zone = timezone.name();
					let now = momentTz.tz(new Date(), zone).format('MM/DD/YYYY h:mm:ss A');
					var activityData = {
						activityName: 'Cancel unlock Patient',
						activityType: 'Unlock patient',
						activityDescription: `${this.userData.displayName} canceled the unlock action of the patient ${rowDetails['name']} with MRN ${rowDetails['patientId']} from ${rowDetails['lockedByUser']} for editing in ${this.metaData.nameOfDesktopMenu} dashboard at ${now}`,
					};
					this._structureService.trackActivity(activityData);
				}
			});
	}
	unlockPatientData(editRowDetails) {
		const index = this.rowData.findIndex(x => x.id == editRowDetails['id']);
		if (index != -1) {
			if (this.rowData[index].patientLock == true) {
				let variable = {
					id: [editRowDetails['id']],
					workflowType: this.workflowType
				}
				this._intakeService.unlockPatient(variable).subscribe(({ data: response }) => {
					this.rowData[index].patientLock = false;
					this.gridApi.refreshCells({ force: true });
					const zone = timezone.name();
					let now = momentTz.tz(new Date(), zone).format('MM/DD/YYYY h:mm:ss A');
					var activityData = {
						activityName: 'Unlock a Patient',
						activityType: 'Unlock patient',
						activityDescription: `${this.userData.displayName} unlocked the patient ${editRowDetails['name']} with MRN ${editRowDetails['patientId']} from ${editRowDetails['lockedByUser']} for editing from ${this.metaData.nameOfDesktopMenu} dashboard since ${now}`,
					};
					this._structureService.trackActivity(activityData);
					/**Polling for unlock the patient from dashboard */
					let sendData = {
						rowData: {},
						updateData: [
							{ updateField: 'patientLock', updatedValue: false },
							{ updateField: 'lockedAt', updatedValue: '' },
							{ updateField: 'lockedUserId', updatedValue: '' },
							{ updateField: 'lockedByUser', updatedValue: '' }
						],
						patientId: editRowDetails['id'],
						worklistId: this.worklistid,
						users: [],
						action: 'update',
						clientId: this._structureService.socket.io.engine.id,
						lockLevel: this.metaData.lockLevel
					};
					this.sendWorklistUpdatePolling(sendData);
					let lockedUserMsg = '';
					if (Number(this.userData.userId) !== Number(editRowDetails['lockedUserId'])) {
						/**If the logged in user and locked by user are different, send msg to user for notify about the unlock of this patient */
						lockedUserMsg = `locked by ${editRowDetails['lockedByUser']}`;
						let user = [];
						user.push(editRowDetails['lockedUserId']);
						let sendData = {
							updateData: [{ updateField: 'lockConfig', updatedValue: false }],
							patientId: editRowDetails['id'],
							patientName: editRowDetails['name'],
							userName: this.userData.displayName,
							lockedTime: now,
							worklistId: this.worklistid,
							users: user,
							action: 'unlock',
							clientId: this._structureService.socket.io.engine.id,
							lockLevel: this.metaData.lockLevel
						};
						let socketResponse = this._structureService.socket.emit('worklistDataUpdate', sendData);
						if (socketResponse['connected'] == false) {
							var activityData = {
								activityName: 'Socket emit - Unlock Patient',
								activityType: 'Socket Connection Error',
								activityDescription: `Error occured in socket connection while ${this.userData.displayName} unlock the patient ${editRowDetails['name']} with MRN ${editRowDetails['patientId']} from ${this.metaData.nameOfDesktopMenu} dashboard at ${now}`,
							};
						}
					}
					setTimeout(function () {
						$.notify({ message: `Patient ${lockedUserMsg} has been unlocked successfully.` }, { type: 'success' });
					}, 1000);
				});
			}
		}
	}
	clearDate(): void {
		// Clear the date using the patchValue function
		this.selectedDate = '';
	}
	convertDate(d, mode, count) {
		let date1 = new Date(Number(d) * 1000);
		if (mode == '+') {
			return { year: date1.getFullYear(), month: date1.getMonth() + 1, day: date1.getDate() + count };
		} else {
			return { year: date1.getFullYear(), month: date1.getMonth() + 1, day: date1.getDate() - count };
		}
	}

	createControls(patientId) {
		this.editorFields = this.reportFields.filter((x) => x.showInEditor == true);
		this.editorDynamicFields = [];
		this.editorFields.forEach((element) => {
			let formElementId;
			formElementId = element.fieldId;
			if (element.fieldName == 'Therapy') {
				this.editorDynamicFields.push({
					name: element.fieldName,
					elementType: 'select',
					options: ['ABT', 'ANE', 'ASIV', 'ASO', 'ASP', 'ATNF', 'BLD', 'CAR', 'CHL'],
					visibility: element.visibility,
					fieldId: element.fieldId,
					value: ''
				});
			}
			if (element.fieldName == 'MOA') {
				this.editorDynamicFields.push({
					name: element.fieldName,
					elementType: 'select',
					options: ['MOA1', 'MOA2', 'MOA3'],
					visibility: element.visibility,
					fieldId: element.fieldId,
					value: ''
				});
			}
			if (element.fieldName == 'Drug') {
				this.editorDynamicFields.push({
					name: element.fieldName,
					elementType: 'textbox',
					options: [],
					visibility: element.visibility,
					fieldId: element.fieldId,
					value: ''
				});
			}
			if (element.fieldName == 'PatientId') {
				this.editorDynamicFields.push({
					name: element.fieldName,
					elementType: 'textbox',
					options: [],
					visibility: element.visibility,
					fieldId: element.fieldId,
					value: patientId
				});
			}
			this.therapyForm = this.createNewControl(this.editorDynamicFields);
		});
	}
	createNewControl(fields) {
		let group: any = {};
		if (fields) {
			fields.forEach((fields) => {
				group[fields.name] = new FormControl('', Validators.required);
			});
		}
		return new FormGroup(group);
	}

	/**Delete single entry */
	deleteEntry(e) {
		let text = '';
		if (this.selectedRowDetails.length > 1) {
			text = 'these entries';
		} else {
			text = 'this entry';
		}
		swal(
			{
				title: 'Are you sure?',
				text: 'You are deleting ' + text,
				type: 'warning',
				showCancelButton: true,
				cancelButtonClass: 'btn-default',
				confirmButtonClass: 'btn-warning',
				confirmButtonText: 'Ok',
				closeOnConfirm: true
			},
			() => {
				let formId;
				if (e.selectionMode == 'child') {
					formId = Number(this.metaData.associatedForm);
				} else {
					formId = this.formId;
				}
				let count = this.selectedRowDetails.length;
				if (count == 1) {
					this._workListService
						.deleteWorklistEntry(formId, e.rowData.submissionID)
						.subscribe(({ data: response }) => {
							if (response['deleteFormData'][0].submissionID === 0) {
								setTimeout(function () {
									$.notify({ message: 'Worklist entry deleted successfully.' }, { type: 'success' });
								}, 1000);
								if (this.rowModelType == 'clientSide') {
									this.rowData = this.rowData.filter((x) => x.submissionID != e.rowData.submissionID);
									this.gridApi.setRowData(this.rowData);
									this.setDefaultWidgetConfig(this.rowData);
								} else {
									var datasource = ServerSideDatasource(this);
									this.gridApi.setServerSideDatasource(datasource);
									const filterModel = this.gridApi.getFilterModel();
									if (Object.keys(filterModel).length > 0) {
										let obj = { field: e.rowData[this.dashboardWidgetField] };
										this.setDefaultWidgetConfigServerSide(obj, 'delete');
									}
								}
								var activityData = {
									activityName: 'Delete Worklist Entry',
									activityType: 'worklist forms',
									activityDescription:
										this.userData.displayName +
										' deleted the entry of id ' +
										e.rowData.submissionID +
										' from the form of id ' +
										formId
								};
								this._structureService.trackActivity(activityData);
								if (
									this.metaData.enableNotification == true &&
									e.actionConfig.notification.enableNotification == true
								) {
									this.sendNotification(e.actionConfig.notification, e.rowData);
								}
								let sendData = {
									rowData: e.rowData,
									updateData: [],
									submissionId: [e.rowData.submissionID],
									formId: formId,
									worklistId: this.worklistid,
									users: [],
									action: 'delete',
									clientId: this._structureService.socket.io.engine.id
								};
								this.sendWorklistUpdatePolling(sendData);
							}
						});
				} else {
					let selectedData = this.selectedRowDetails;
					let i = 1;
					selectedData.forEach((element) => {
						this._workListService
							.deleteWorklistEntry(formId, element.submissionID)
							.subscribe(({ data: response }) => {
								if (response['deleteFormData'][0].submissionID === 0) {
									if (i == count) {
										if (this.rowModelType == 'clientSide') {
											this.getformData();
											this.rowData = this.rowData.filter(
												(x) => selectedData.indexOf(x.submissionID) == -1
											);
											this.gridApi.setRowData(this.rowData);
											this.setDefaultWidgetConfig(this.rowData);
										} else {
											var datasource = ServerSideDatasource(this);
											this.gridApi.setServerSideDatasource(datasource);
											this.getWidgetCountServerSide();
										}
										setTimeout(function () {
											if (count > 1) {
												$.notify(
													{ message: 'Worklist entries deleted successfully.' },
													{ type: 'success' }
												);
											} else {
												$.notify(
													{ message: 'Worklist entry deleted successfully.' },
													{ type: 'success' }
												);
											}
										}, 1000);
										//this.sendNotification(e.actionConfig.notification, e.rowData);
										let sendData = {
											rowData: selectedData,
											updateData: [],
											submissionId: selectedData.map((x) => x.submissionID),
											formId: formId,
											worklistId: this.worklistid,
											users: [],
											action: 'delete',
											clientId: this._structureService.socket.io.engine.id
										};
										this.sendWorklistUpdatePolling(sendData);
									}
									i++;
									var activityData = {
										activityName: 'Delete Worklist Entry',
										activityType: 'worklist forms',
										activityDescription:
											this.userData.displayName +
											' deleted the entry of id ' +
											element.submissionID +
											' from the form ' +
											this.heading
									};
									this._structureService.trackActivity(activityData);
								}
							});
					});
				}
				this.gridApi.deselectAll();
			}
		);
	}
	// onCellClicked(params) {
	// 	if (params.colDef['cellEvent'] === 'showDetail') {
	// 		// let e = { rowData: params.data, actionField: "", actionMode: "", selectionMode: "", link: "" };
	// 		if (params.value != '') {
	// 			this.showDetailedView(params);
	// 		}
	// 	}
	// }
	showDetailedView(params) {
		$('.my-tooltip').css('top', params.event.pageY - 200);
		this.showTooltip = true;
		$('.my-tooltip').show();
		this.showResponse = false;
		var getData = {
			reference_id: params.value
		};
		this._structureService.getIntegration(getData).then((res) => {
			if (res && res['response'].integration_status) {
				this.responseStatus = res['response'];
				this.showResponse = true;
			} else {
				$('.my-tooltip').hide();
			}
		});
	}
	closeDetailpop() {
		$('.my-tooltip').hide();
	}
	showAllStaffs() {
		this.staffLoadingMsg = true;
		this._structureService.getUserListLazy(0, 0, '', '', '').then((data) => {
			let staffLists = JSON.parse(JSON.stringify(data['staffUsers']));
			staffLists.forEach((element) => {
				element.staffLanguage = element.languages;
				this.staffList.push(element);
			});
			//this.staffList = this.staffList.filter(x => x.languages.split(',').indexOf(this.staffLanguage.split(',')[0]) != -1);
			this.staffList.sort(function (a, b) {
				if (a.displayName < b.displayName) {
					return -1;
				}
				if (a.displayName > b.displayName) {
					return 1;
				}
				return 0;
			});
			this.staffLoadingMsg = false;
			this.showAllMsg = false;
			// this.modalType = 'staff';
			this.searchTexts = '';
			// if (e.actionMode == 'single row') {
			//   this.assignType = 'single';
			// } else {
			//   this.assignType = 'multiple';
			// }
		});
	}
	enableOrDisableUiLI(condition, clear: boolean = true) {
		if (condition) {
			$('ul.associate-ul').css('display', 'block');
			$('input#associate-search-input').addClass('active');
		} else {
			$('ul.associate-ul').css('display', 'none');
			$('input#associate-search-input').removeClass('active');
		}
		if (clear) {
			$('input#associate-search-input').val('');
		}
	}
	openAssociateList() {
		this.onClickEvent = false;
		if (!this.associatePatientLoading) {
			if (this.assosiatedPatients.length > 0) {
				this.enableOrDisableUiLI(true, false);
			}
		}
	}
	checkAssociatePatientWithTems() {
		let searchTerm = $('#associate-search-input').val();
		if (searchTerm != '') {
			this.getAssociatePatients(searchTerm);
		} else {
			this.enableOrDisableUiLI(false, false);
		}
	}
	getAssociatePatients(search: string = '') {
		$('#associate-search').text(' ').text('Loading...');
		this.associatePatientLoading = true;
		this._structureService
			.getAssociatedPatientsLists('', search)
			.then((result: any) => {
				// this.assosiatedPatients = result;
				this.associatePatientLoading = false;
				this.assosiatedPatients = result.filter((result) => {
					//if(result.caregiver_userid == null && result.role != 'Caregiver') {
					var date = '';
					//if(result.caregiver_userid ) {
					if (result.caregiver_dob) {
						date = result.caregiver_dob;
						var dobDay = new Date(date).getDay();
						if (date && !isNaN(dobDay)) {
							date = date.replace(/-/g, '/');
							try {
								// date = this._datePipe.transform(date, 'MM/dd/yyyy');
								date = moment(date).format('MM/DD/YYYY');
							} catch (e) {
								date = '';
							}
						} else {
							date = '';
						}
					} else {
						date = '';
					}
					result.caregiver_dob_formatted = date;
					// } else {
					date = '';
					if (result.dob) {
						date = result.dob;
						var dobDay = new Date(date).getDay();
						if (date && !isNaN(dobDay)) {
							date = date.replace(/-/g, '/');
							try {
								// date = this._datePipe.transform(date, 'MM/dd/yyyy');
								date = moment(date).format('MM/DD/YYYY');
							} catch (e) {
								date = '';
							}
						} else {
							date = '';
						}
					} else {
						date = '';
					}
					result.dob_formatted = date;
					var listDisplayName = result.caregiver_displayname
						? result.dob_formatted
							? result.displayname +
							' - ' +
							result.dob_formatted +
							' (' +
							result.caregiver_displayname +
							')'
							: result.displayname + ' (' + result.caregiver_displayname + ')'
						: result.dob_formatted ? result.displayname + ' - ' + result.dob_formatted : result.displayname;
					listDisplayName = listDisplayName + (result.passwordStatus == 'true' ? ' (Registered)' : '');
					result.listDisplayName = listDisplayName;
					// }
					return true;
					/* } else {
          return false;
        } */
				});
				$('#associate-search').text('').text('Search');
				//$("input#associate-search-input").attr("placeholder", "Select Associated Patient or Search");
				if (search != '') {
					this.enableOrDisableUiLI(true, false);
					if (this.selectedAssosiatePatientName.includes(search) == false) {
						this.selectedAssosiatePatientName = '';
					}
				}
				$('#select2-assosiatedPatients-container .select2-selection__placeholder')
					.html('')
					.html('Select Associated Patient');
				$('#assosiatedPatients').attr('disabled', false);

				// this.associatePatientSelect2();
				// this.tenantUsers = result;
			})
			.catch((ex) => { });
	}
	populateBasicPatient(userid, userDisplayName) {
		this.submitId = 0;
		this.disableBtn = false;
		let extraField = '';
		this.selectedAssosiatePatient = userid;
		this.selectedAssosiatePatientName = userDisplayName;
		this.selectedPatientName = userDisplayName;
		let index = this.assosiatedPatients.findIndex((x) => x.userId == userid);
		//const extraField = '&patientId=' + this.appPatientList[index].userId + '&patient_first=' + this.appPatientList[index].firstname + '&patient_last=' + this.appPatientList[index].lastname + '&patient_dob=' + this.appPatientList[index].dob;
		if (localStorage.getItem('populatedData').length > 0) {
			let populatedData = JSON.parse(localStorage.getItem('populatedData'));
			populatedData.forEach((element) => {
				element.value = this.assosiatedPatients[index][element.linkField];
				let fields = element.linkField.split(',');
				if (fields.length > 1) {
					if (element.type == 'simple_name') {
						populatedData.push({
							fieldName: element.fieldName,
							mapField: element.mapField + '_2',
							linkField: fields[1],
							type: 'simple_name',
							value: this.assosiatedPatients[index][fields[1]]
						});
						element.value = this.assosiatedPatients[index][fields[0]];
						element.mapField = element.mapField + '_1';
						element.linkField = fields[0];
					}
				} else {
					element.value = this.assosiatedPatients[index][element.linkField];
				}
			});
			extraField = extraField + '&populateData=' + JSON.stringify(populatedData);
		}
		this.enableOrDisableUiLI(false, true);
		var activityData = {
			activityName: 'Patient Association',
			activityType: 'worklist forms',
			activityDescription:
				this.userData.displayName +
				' associated  ' +
				this.assosiatedPatients[index].displayname +
				' of id ' +
				this.assosiatedPatients[index].userId +
				' to the form with id' +
				this.formId
		};
		this._structureService.trackActivity(activityData);
		let selectedData = this.gridApi.getSelectedRows();
		let count = selectedData.length;
		if (count == 1) {
			var field = this.formField;
			const gridData = this.rowData;
			const index = this.rowData.findIndex((x) => x.submissionID == this.selectedEntry);
			this.rowData[index][field] = userid;
			let params = [{ id: this.machFormIds[1][field].id, value: userid }];
			if (this.machFormIds[1].hasOwnProperty('PatientId')) {
				params.push({ id: this.machFormIds[1]['PatientId'].userid, value: userid });
			}
			this.gridApi.refreshCells({ force: true });
			this._workListService
				.updateSingleFormData(params, this.formId, this.selectedEntry)
				.subscribe(({ data: response }) => {
					var activityData = {
						activityName: 'Assign Patient',
						activityType: 'worklist forms',
						activityDescription:
							this.userData.displayName +
							' assign ' +
							userid +
							' to the entry of id ' +
							this.selectedEntry +
							' in form with id ' +
							this.formId
					};
					this._structureService.trackActivity(activityData);
					setTimeout(function () {
						$.notify({ message: '<strong>Success! </strong> Patient assigned.' }, { type: 'success' });
					}, 1000);
					this.singleRowActions.forEach((elt) => {
						elt.disable = false;
					});
					this.rowData[index]['PatientId'] = userid;
					if (
						this.metaData.enableNotification == true &&
						this.assignPatientActionConfig.actionConfig.notification.enableNotification == true
					) {
						this.sendNotification(
							this.assignPatientActionConfig.actionConfig.notification,
							this.rowData[index]
						);
					}
					let sendData = {
						rowData: selectedData,
						updateData: [
							{ updateField: field, updatedValue: userid },
							{ updateField: 'patientId', updatedValue: userid }
						],
						submissionId: [this.selectedEntry],
						formId: this.formId,
						worklistId: this.worklistid,
						users: [],
						action: 'update',
						clientId: this._structureService.socket.io.engine.id
					};
					this.sendWorklistUpdatePolling(sendData);
				});
		}
		const params = { force: true };
		this.gridApi.refreshCells(params);
		$('#assign-patient-modal').modal('hide');
		this.selectedPatientName = '';
	}

	clearSearchField() {
		let searchTerm = $('#associate-search-input').val();
		if (searchTerm == '') {
			this.closeSelectedAssociatePatient(true);
		}
	}
	closeSelectedAssociatePatient(contentReset) {
		if (this.selectedAssosiatePatient) {
			this.enableOrDisableUiLI(false, true);
			this.selectedAssosiatePatient = '';
			this.selectedAssosiatePatientName = '';
			this.selectedPatientName = '';
		} else if ($('input#associate-search-input').val() != '') {
			$('input#associate-search-input').val('');
			$('#associate-search').text(' ').text('Search');
			this.associatePatientLoading = false;
			this.enableOrDisableUiLI(false, true);
		} else if ($('input#associate-search-input').val() == '') {
			this.enableOrDisableUiLI(false, false);
		}
		if (contentReset == true) {
			this.formContent = '';
		}
		this.disableBtn = true;
		this.assosiatedPatients = [];
	}
	assignesPatientStaff(staff, id) {
		let selectedData = this.gridApi.getSelectedRows();
		let count = selectedData.length;
		if (count == 1) {
			var field = this.formField;
			const gridData = this.rowData;
			const index = this.rowData.findIndex((x) => x.submissionID == this.selectedEntry);
			this.rowData[index][field] = staff;
			let params = [{ id: this.machFormIds[1][field].id, value: staff }];
			if (this.machFormIds[1].hasOwnProperty('StaffId')) {
				params.push({ id: this.machFormIds[1]['StaffId'].id, value: id });
			}
			this.gridApi.refreshCells({ force: true });
			this._workListService
				.updateSingleFormData(params, this.formId, this.selectedEntry)
				.subscribe(({ data: response }) => {
					var activityData = {
						activityName: 'Assign Staff For Patient',
						activityType: 'worklist forms',
						activityDescription:
							this.userData.displayName +
							' assign ' +
							staff +
							' to the entry of id ' +
							this.selectedEntry +
							' in form with id ' +
							this.formId
					};
					this._structureService.trackActivity(activityData);
					setTimeout(function () {
						$.notify({ message: '<strong>Success! </strong> Staff assigned.' }, { type: 'success' });
					}, 1000);
					this.rowData[index]['StaffId'] = id;
					if (
						this.metaData.enableNotification == true &&
						this.assignStaffActionConfig.actionConfig.notification.enableNotification == true
					) {
						this.sendNotification(
							this.assignStaffActionConfig.actionConfig.notification,
							this.rowData[index]
						);
					}
					let sendData = {
						rowData: selectedData,
						updateData: [
							{ updateField: field, updatedValue: staff },
							{ updateField: 'StaffId', updatedValue: id }
						],
						submissionId: [this.selectedEntry],
						formId: this.formId,
						worklistId: this.worklistid,
						users: [],
						action: 'update',
						clientId: this._structureService.socket.io.engine.id
					};
					this.sendWorklistUpdatePolling(sendData);
				});
		} else {
			var field = this.formField;
			const gridData = this.rowData;
			let i = 1;
			selectedData.forEach((element) => {
				let params = [{ id: this.machFormIds[1][field].id, value: staff }];
				if (this.machFormIds[1].hasOwnProperty('StaffId')) {
					params.push({ id: this.machFormIds[1]['StaffId'].id, value: id });
				}
				this._workListService
					.updateSingleFormData(params, this.formId, element.submissionID)
					.subscribe(({ data: response }) => {
						var activityData = {
							activityName: 'Assign Staff For Patient',
							activityType: 'worklist forms',
							activityDescription:
								this.userData.displayName +
								' assign ' +
								staff +
								' to the entry of id ' +
								element.submissionID +
								' in form of id ' +
								this.formId
						};
						this._structureService.trackActivity(activityData);
						if (i == count) {
							setTimeout(function () {
								$.notify(
									{ message: '<strong>Success! </strong> Staff assigned.' },
									{ type: 'success' }
								);
							}, 1000);
							let sendData = {
								rowData: selectedData,
								updateData: [
									{ updateField: field, updatedValue: staff },
									{ updateField: 'StaffId', updatedValue: id }
								],
								submissionId: selectedData.map((x) => x.submissionID),
								formId: this.formId,
								worklistId: this.worklistid,
								users: [],
								action: 'update',
								clientId: this._structureService.socket.io.engine.id
							};
							this.sendWorklistUpdatePolling(sendData);
						}
						i++;
					});
				const index = this.rowData.findIndex((x) => x.submissionID == element.submissionID);
				this.rowData[index][field] = staff;
				this.rowData[index]['StaffId'] = id;
				//this.sendNotification(this.assignStaffActionConfig.actionConfig.notification, this.rowData[index]);
			});
		}
		const params = { force: true };
		this.gridApi.refreshCells(params);
		$('#assign-modal').modal('hide');
		this.searchTexts = '';
	}
	/**Multiple batch action functionality */
	multipleButtonAction(e) {
		if (e.action == 'editEntry') {
			this.editEntry(e);
		} else if (e.action == 'deleteEntry') {
			this.deleteEntry(e);
		} else if (e.action == 'recallEntry') {
			this.recallEntry(e);
		} else if(e.action == 'addNote') {
			this.addBlogNote(e);
		} else if(e.action == 'unlockPatient') {
			this.unlockPatient(e);
		}
	}
	batchAction(e) {

	}
	/**add new button functionality */
	goToPage() {
		if (this.linkFields.length > 0) {
			localStorage.setItem('populatedData', JSON.stringify(this.linkFields));
		} else {
			localStorage.setItem('populatedData', '');
		}
		this.router.navigate([this.addNewBtnLink]);
	}
	/**Dynamic csv export */
	exportCSV() {
		let self = this;
		let columnkey = [];
		this.reportFields.forEach((element) => {
			if (
				(element.includeInExport == null || element.includeInExport == true) &&
				this.hideColumns.indexOf(element.fieldName) == -1 &&
				element.visibility == true
			) {
				columnkey.push(element.fieldName);
			}
		});
		let params: any = {
			columnKeys: columnkey,
			allColumns: false,
			fileName: self.heading,
			sheetName: self.heading
		};
		params.processCellCallback = function (params) {
			let index = self.reportFields.findIndex((x) => x.fieldName == params.column.colDef.field);
			if (index != -1 && self.reportFields[index].valueType == 'date') {
				return self.convertDateFormat(params);
			} else {
				return params.value;
			}
		};
		this.gridApi.exportDataAsCsv(params);
	}
	/**Dynamic excel export */
	exportExcel(mode) {
		let self = this;
		let columnkey = [];
		this.reportFields.forEach((element) => {
			if (
				(element.includeInExport == null || element.includeInExport == true) &&
				this.hideColumns.indexOf(element.fieldName) == -1 &&
				element.visibility == true
			) {
				columnkey.push(element.fieldName);
			}
		});
		let params: any = {
			columnKeys: columnkey,
			allColumns: false,
			fileName: self.heading,
			exportMode: mode,
			sheetName: self.heading
		};
		params.processHeaderCallback = function (params) {
			return params.column.getColDef().headerName.toUpperCase();
		};
		params.processCellCallback = function (params) {
			let index = self.reportFields.findIndex((x) => x.fieldName == params.column.colDef.field);
			if (index != -1 && self.reportFields[index].valueType == 'date') {
				return self.convertDateFormat(params);
			} else {
				return params.value;
			}
		};
		this.gridApi.exportDataAsExcel(params);
	}
	/**get widget count using api in server side pagination */
	getWidgetCountServerSide() {
		this._workListService
			.getWidgetCount(this.formId, this.dashboardWidgetFieldId, this.uniqueClass)
			.refetch()
			.then(({ data: response }) => {
				this.widgetCounts = [];
				let totalcount = 0;
				let obj = {};
				response['getTotalBasedOnStatus'].forEach((element) => {
					if (element.option && element.option != null) {
						let option = element.option.toLowerCase();
						obj[option] = element.total;
					}
					totalcount = totalcount + element.total;
				});
				obj['all'] = totalcount;
				this.widgetCounts.push(obj);
				this.setDefaultWidgetConfigServerSide('', '');
			});
	}
	/**Send notification from actions in worklist */
	sendNotification(actionConfig, rowData) {
		let staffId = '';
		if (actionConfig.notifyRecipientOnAction == true) {
			if (rowData.hasOwnProperty('StaffId') == true) {
				staffId = rowData.StaffId;
			} else if (this.metaData.fieldForRecipient != '') {
				const index = this.reportFields.findIndex((x) => x.fieldName == this.metaData.fieldForRecipient);
				const delimitter = this.reportFields[index].fieldValueDelimiter;
				const value = rowData[this.metaData.fieldForRecipient];
				if (delimitter != '') {
					if (delimitter.length == 1) {
						if (value.includes(delimitter)) {
							const originalValue = value.split(delimitter);
							staffId = originalValue[1];
						}
					} else if (delimitter == 2) {
						if (value.includes(delimitter.charAt(0)) && value.includes(delimitter.charAt(1))) {
							let originalValue = value.split(delimitter.charAt(0));
							originalValue = originalValue[1].split(delimitter.charAt(1))[0];
							staffId = originalValue;
						}
					}
				}
			}
		}
		let notificationConfig = {
			notifyingRoles: actionConfig.notifyRolesOnAction == true ? this.metaData.notifyRoles : '',
			notifyRecipient: staffId,
			notifyMode: {
				push: actionConfig.push,
				pushTemplate: actionConfig.pushTemplate,
				sms: actionConfig.sms,
				smsTemplate: actionConfig.smsTemplate,
				email: actionConfig.email,
				emailTemplate: actionConfig.emailTemplate
			},
			data: rowData
		};
		this.sendNotificationMessage(notificationConfig);
	}
	/**Format the content and select which method used for notification */
	sendNotificationMessage(notificationConfig) {
		if (notificationConfig.notifyMode.push == true) {
			const pIndex = this.allMessageTemplate.findIndex((x) => x.id == notificationConfig.notifyMode.pushTemplate);
			if (pIndex != -1) {
				const content = this.formatContent(this.allMessageTemplate[pIndex].content, notificationConfig.data);

				this.sendPushNotification(content, notificationConfig);
			}
		}
		const notificationContent = [];
		if (notificationConfig.notifyMode.email == true || notificationConfig.notifyMode.sms == true) {
			if (notificationConfig.notifyMode.email == true) {
				const eIndex = this.allMessageTemplate.findIndex(
					(x) => x.id == notificationConfig.notifyMode.emailTemplate
				);
				if (eIndex != -1) {
					let content = this.formatContent(this.allMessageTemplate[eIndex].content, notificationConfig.data);
					notificationContent.push({
						mode: 'email',
						content: content,
						subject: this.allMessageTemplate[eIndex].subject,
						title: this.allMessageTemplate[eIndex].title
					});
				}
			}
			if (notificationConfig.notifyMode.sms == true) {
				const sIndex = this.allMessageTemplate.findIndex(
					(x) => x.id == notificationConfig.notifyMode.smsTemplate
				);
				if (sIndex != -1) {
					let content = this.formatContent(this.allMessageTemplate[sIndex].content, notificationConfig.data);
					notificationContent.push({ mode: 'sms', content: content });
				}
			}
			this.sendEmailOrSmsNotification(notificationContent, notificationConfig);
		}
	}
	/**Format content by replacing Field/date/form name/worklist */
	formatContent(content, data) {
		let formName = this.formName;
		let worklistName = this.heading;
		const zone = timezone.name();
		let newContent = content.replace(/\[(.*?)\]/g, function (m) {
			if (data.hasOwnProperty(m.slice(1, -1))) {
				return data[m.slice(1, -1)];
			} else if (m == '[Date]') {
				const newDate = momentTz.tz(new Date(), zone).format('MM/DD/YYYY');
				return newDate;
			} else if (m == '[FormName]') {
				return formName;
			} else if (m == '[Worklist]') {
				return worklistName;
			} else if (m == '[DateTime]') {
				const newDate = momentTz.tz(new Date(), zone).format('MM/DD/YYYY h:mm:ss A');
				return newDate;
			} else {
				return m;
			}
		});
		return newContent;
	}
	/**Configure and send push notification */
	sendPushNotification(content, notificationConfig) {
		var deepLinking = {
			pushType: '',
			state: 'eventmenu.worklist',
			stateParams: {},
			tenantId: this.userData.tenantId,
			tenantName: this.userData.tenantName
		};
		let pushMessage = content;
		let status = 0;
		let selectedRecipientsPolling = [];
		if (notificationConfig.notifyingRoles != '') {
			//let notifyArray = notificationConfig.notifyingRoles.split(',');
			//let i = 1;
			//notifyArray.forEach((elem) => {
				this._workListService.getRoleBasedStaffs(notificationConfig.notifyingRoles, status, 1).then((data) => {
					let staffList = JSON.parse(JSON.stringify(data['getSessionTenant'].getMultipleRolesBasedStaffs));
					staffList.forEach((element) => {
						if (this.userData.userId != element.id && element.id != notificationConfig.notifyRecipient) {
							const staff = {
								username: element.emails[0].value,
								displayname: element.displayName,
								userid: element.id,
								//roleId: elem,
								mobile: element.mobile,
								countryCode: element.countryCode,
								senderId: this.userData.userId
							};
							selectedRecipientsPolling.push(staff);
						}
					});
					//if (i == notifyArray.length) {
						if (notificationConfig.notifyRecipient != '') {
							this._structureService.getUser(notificationConfig.notifyRecipient).then((data) => {
								const staff = data['staffUsers'][0];
								const staffDetails = {
									username: staff.emails[0].value,
									displayname: staff.displayName,
									userid: staff.id,
									roleId: staff.role.id,
									mobile: staff.mobile,
									countryCode: staff.countryCode,
									senderId: this.userData.userId
								};
								selectedRecipientsPolling.push(staffDetails);
								this._structureService.sentPushNotification(
									selectedRecipientsPolling,
									0,
									pushMessage,
									'',
									deepLinking,
									'',
									''
								);
							});
						} else {
							this._structureService.sentPushNotification(
								selectedRecipientsPolling,
								0,
								pushMessage,
								'',
								deepLinking,
								'',
								''
							);
						}
					// }
					// i++;
				});
			//});
		} else {
			if (notificationConfig.notifyRecipient != '') {
				this._structureService.getUser(notificationConfig.notifyRecipient).then((data) => {
					const staff = data['staffUsers'][0];
					const staffDetails = {
						username: staff.emails[0].value,
						displayname: staff.displayName,
						userid: staff.id,
						roleId: staff.role.id,
						mobile: staff.mobile,
						countryCode: staff.countryCode,
						senderId: this.userData.userId
					};
					selectedRecipientsPolling.push(staffDetails);
					this._structureService.sentPushNotification(
						selectedRecipientsPolling,
						0,
						pushMessage,
						'',
						deepLinking,
						'',
						''
					);
				});
			}
		}
	}
	/**Configure and send email/sms notification */
	sendEmailOrSmsNotification(content, notificationConfig) {
		let notificationData = {
			notifyUsers: [],
			notifyMode: content,
			tenantId: this._structureService.getCookie('tenantId'),
			senderName: this.userData.displayName
		};
		if (notificationConfig.notifyingRoles != '') {
			let notifyArray = notificationConfig.notifyingRoles.split(',');
			const status = 0;
			let i = 1;
			notifyArray.forEach((elem) => {
				this._workListService.getRoleBasedStaffs(elem, status, 1).then((data) => {
					let staffList = JSON.parse(JSON.stringify(data['getSessionTenant'].roleBasedStaffs));
					staffList.forEach((element) => {
						if (this.userData.userId != element.id && element.id != notificationConfig.notifyRecipient) {
							const staff = {
								firstname: element.firstName,
								lastname: element.lastName,
								displayname: element.displayName,
								username: element.emails[0].value,
								countryCode: element.countryCode,
								mobile: element.mobile
							};
							notificationData.notifyUsers.push(staff);
						}
					});
					if (i == notifyArray.length) {
						if (notificationConfig.notifyRecipient != '') {
							this._structureService.getUser(notificationConfig.notifyRecipient).then((data) => {
								const staff = data['staffUsers'][0];
								const staffDetails = {
									firstname: staff.firstName,
									lastname: staff.lastName,
									displayname: staff.displayName,
									username: staff.emails[0].value,
									countryCode: staff.countryCode,
									mobile: staff.mobile
								};
								notificationData.notifyUsers.push(staffDetails);
								this._structureService.socket.emit('sendWorklistNotification', notificationData);
							});
						} else {
							this._structureService.socket.emit('sendWorklistNotification', notificationData);
						}
					}
					i++;
				});
			});
		} else {
			if (notificationConfig.notifyRecipient != '') {
				this._structureService.getUser(notificationConfig.notifyRecipient).then((data) => {
					const staff = data['staffUsers'][0];
					const staffDetails = {
						firstname: staff.firstName,
						lastname: staff.lastName,
						displayname: staff.displayName,
						username: staff.emails[0].value,
						countryCode: staff.countryCode,
						mobile: staff.mobile
					};
					notificationData.notifyUsers.push(staffDetails);
					this._structureService.socket.emit('sendWorklistNotification', notificationData);
				});
			}
		}
	}
	/**Polling for any update in worklist */
	sendWorklistUpdatePolling(sendData) {
		// if (this.metaData.visibleToRoles.split(',').length > 1) {
		// 	const count = this.metaData.visibleToRoles.split(',').length;
		// 	let i = 1;
		// 	this.metaData.visibleToRoles.split(',').forEach((element) => {
		// 		this._workListService.getRoleBasedStaffs(element, 0, 1).then((data) => {
		// 			const staffList = JSON.parse(JSON.stringify(data['getSessionTenant'].roleBasedStaffs));
		// 			staffList.forEach((element) => {
		// 				sendData['users'].push(element.id);
		// 			});
		// 			if (i == count) {
		// 				this._structureService.socket.emit('worklistDataUpdate', sendData);
		// 			}
		// 			i++;
		// 		});
		// 	});
		// } else {
		// if(this.metaData.visibleToRoles != '') {
			// this._workListService.getRoleBasedStaffs(this.metaData.visibleToRoles, 0, 1).then((data) => {
			// 	const staffList = JSON.parse(JSON.stringify(data['getSessionTenant'].getMultipleRolesBasedStaffs));
			// 	staffList.forEach((element) => {
			// 		sendData['users'].push(element.id);
			// 	});
			// 	this._structureService.socket.emit('worklistDataUpdate', sendData);
			// });
			this._intakeService.getStaffIdUsers({appGuid: this.guid}).then((data) => {
				const staffList = JSON.parse(JSON.stringify(data['getStaffIdUsers']));
				sendData['users'] = data['getStaffIdUsers'].staffIds;
				this._structureService.socket.emit('worklistDataUpdate', sendData);
			});
		// }

	}
	/**Change pagination size from ui */
	changePaginationSize(e) {
		this.paginationPageSize = e.target.value.trim();
		var datasource = ServerSideDatasource(this);
		this.gridApi.setServerSideDatasource(datasource);
		let obj = { worklistId: this.worklistid, pageSize: this.paginationPageSize };
		localStorage.setItem('worklistPageSize', JSON.stringify(obj));
	}
	onColumnStateChanged(params) {
		// if(this.metaData.columnState == true) {
		//   let columnState = (localStorage.getItem('columnState') && localStorage.getItem('columnState') != '') ? JSON.parse(localStorage.getItem('columnState')) : [];
		//   let index = columnState.findIndex(x=> x.worklist == this.worklistid);
		// 	if(index != -1) {
		// 	  columnState[index].columnState = params.columnApi.getColumnState();
		// 	} else {
		// 	  columnState.push({worklist: this.worklistid, columnState: params.columnApi.getColumnState()});
		// 	}
		//   localStorage.setItem('columnState', JSON.stringify(columnState));
		// }
		console.log('setColumnState- column changed', this.setColumnState);
		if (this.metaData.columnState == true && this.metaData.stateSavingMode == 'autoSave' && this.setColumnState == false) {
			console.log('save column');
			this.saveStatePrefrence();
		}
	}
	onColumnDragStopped(params){
		console.log(params,"drag stopped");
		if (this.metaData.columnState == true && this.metaData.stateSavingMode == 'autoSave' && this.setColumnState == false) {
		console.log('state: auto save column state prefernece');
		this.saveStatePrefrence();
		}
		}
	setAdvanceSearchFormValues() {
		//console.log("searchForm", this._workListService.advancedSearchConfig);
		let formData ;
		
		 if (this._workListService.advancedSearchConfig['searchForm'] &&  this._workListService.advancedSearchConfig['selectedSearchFields'] && this._workListService.advancedSearchConfig['selectedSearchFields'] != '') {
			formData = this._workListService.advancedSearchConfig['searchForm'];
		}
		else if (JSON.parse(localStorage.getItem('searchForm'))) {
			 formData = JSON.parse(localStorage.getItem('searchForm'));
		}
		//let formData = JSON.parse(localStorage.getItem('searchForm'));
		if(formData) {
			setTimeout(() => {
				this.advanceSearchComp.advanceSearchForm.patchValue({
					firstName: formData.firstName,
					lastName: formData.lastName,
					patientId: formData.patientId,
					hrPatStatus: formData.hrPatStatus,
					locationId: formData.locationId,
					siteId: formData.siteId,
					team: formData.team,
					hospitalDischargeDate: formData.hospitalDischargeDate,
					cprSocDate: formData.cprSocDate,
					insuranceRep: formData.insuranceRep
				});
			}, 1000);	
			this.isShow = true;
			//this.customSearch = false;
			this._workListService.singleSearchConfig = {};
			//localStorage.setItem('singleSearchFields','');
		}
		// if(localStorage.getItem('singleSearchFields') && localStorage.getItem('singleSearchFields') != '') {
		// 	let singleSearch = JSON.parse(localStorage.getItem('singleSearchFields'));
		// 	this.searchFieldText = singleSearch.searchText;
		// 	this.selectedSearchFields = singleSearch.searchFields;
		// 	this.selectedSearchFields.forEach(element => {
		// 		$('#checkbox' + element.fieldId).prop("checked", true);
		// 	});
		// 	this.customSearch = true;
		// 	this.isShow = true;
		// }
	}
	setUnlockPatient(){
		if(localStorage.getItem('changePatientData') && localStorage.getItem('changePatientData') != '') {
			const data = JSON.parse(localStorage.getItem('changePatientData'));
			if (data.worklistId == this.worklistid){
				const index = this.rowData.findIndex((x) => x.id == data.patientId);
				if(index != -1) {
					data.updateData.forEach(element => {
						this.rowData[index][element.updateField] = element.updatedValue;
					});
					this.gridApi.refreshCells({ force: true });
				}
				localStorage.removeItem('changePatientData');
			}
			
		}
	}
	refreshDashboard() {
		if (this.metaData.enableSavingState && this.metaData.enableSavingState == true) {
			let index = this._sharedService.worklistState.findIndex(x => x.worklistId == this.worklistid);
			if(index == -1) {
			  if(this.metaData.stateSavingPreference && this.metaData.stateSavingPreference == 'userProfile') {
				let variable = {'userId':Number(this.userData.userId), 'object_id': Number(this.worklistid)};
				this._workListService.getWorklistStatePreference(variable).then((data)=> {
				  let response = data['getSessionTenant'].getUserPeference;
				  console.log('response', response.status);
				  if(response.status == 200) {
					console.log('state: state details', response);
					if(response.data.length > 0) {
					  console.log('state: data',response.data);
					  this.worklistState = JSON.parse(response.data[0].meta);
					  this.userPreferenceId = response.data[0].id;
					  this._sharedService.userWorklistPrefrenceIds.push({'worklistId':this.worklistid, userPreferenceId: response.data[0].id});
					  console.log('state: userPreferenceId',this.userPreferenceId);
					  this.setStatePreferencetoGrid(this.worklistState,'userProfile');
					}
				  }
				});
			  }
			} else {
				console.log('worklist state cache', this._sharedService.worklistState[index].worklistState);
			  console.log('state: from cache');
			  let pIndex = this._sharedService.userWorklistPrefrenceIds.findIndex(x=> x.worklistId == this.worklistid);
			  if(pIndex != -1) {
				this.userPreferenceId = this._sharedService.userWorklistPrefrenceIds[pIndex].userPreferenceId;
			  }
			  this.setStatePreferencetoGrid(this._sharedService.worklistState[index].worklistState,'cache');
			}
		}
	
		if (this.rowModelType == 'serverSide') {
			this.getDataCount = true;
			var datasource = ServerSideDatasource(this);
			this.gridApi.setServerSideDatasource(datasource);
		} else {
			this.getformData();
		}
		this._workListService.advancedSearchConfig = {};
	}
}

/**Implement lazy loading functionality */
function ServerSideDatasource(this1) {
	return {
		getRows(params) {
			let start = params.request.startRow;
			let end = params.request.endRow;
			let offset = params.request.startRow;
			let filterModel = params.request.filterModel;
			let finalFilterModel = {};
			if(this1.metaData.searchState == false || (this1.metaData.enableSavingState && this1.metaData.enableSavingState == false) || this1.metaData.enableSavingState) {
				if(this1._workListService.singleSearchConfig && Object.keys(this1._workListService.singleSearchConfig).length > 0) {
					this1.searchFieldText = this1._workListService.singleSearchConfig['searchText'];
					this1.selectedSearchFields = this1._workListService.singleSearchConfig['searchFields'];
					this1.selectedSearchFields.forEach(element => {
						$('#checkbox'+element.fieldId).prop("checked", true);
					});
					this1.customSearch = true;
					this1.isShow = true;
				}
			}
			if (this1.searchFieldText != '' && this1.selectedSearchFields.length > 0) {
				let filterFields = this1.reportFields.filter(x => x.allowFilter == true && x.valueType != 'checkbox');
				this1.selectedSearchFields.forEach(element => {
					if (element.valueType == 'number') {
						finalFilterModel[element.fieldName] = { type: 'equals', 
																filter: this1.searchFieldText, 
																filterType: 'number', 
																filterCondition: this1.selectedSearchFields.length == 1 ? 'AND' : 'OR' };
					} else if (element.valueType == 'date') {
						finalFilterModel[element.fieldName] = { type: 'equals', 
																dateFrom: this1.searchFieldText, 
																filterType: 'date', 
																filterCondition: this1.selectedSearchFields.length == 1 ? 'AND' : 'OR' };
					} else if (element.valueType == 'radio' || element.valueType == 'checkbox' || element.valueType == 'select') {
						finalFilterModel[element.fieldName] = { type: 'contains', 
																filter: this1.searchFieldText, 
																filterType: 'text', 
																filterCondition: this1.selectedSearchFields.length == 1 ? 'AND' : 'OR' };
					} else {
						finalFilterModel[element.fieldName] = { type: 'contains', 
																filter: this1.searchFieldText, 
																filterType: 'text', 
																filterCondition: this1.selectedSearchFields.length == 1 ? 'AND' : 'OR' };
					}
				});
			}
			if (Object.keys(filterModel).length > 0) {
				let filterModelKeys = Object.keys(filterModel);
				let widgetFilterKeys = filterModelKeys.filter(x => x == this1.selectedWidgetField);
				let otherModelKeys = filterModelKeys.filter(x => x != this1.selectedWidgetField);
				if (otherModelKeys.length > 0) {
					this1.searchFieldText = '';
					finalFilterModel = filterModel;
				}
			}	
			if (this1._workListService.advancedSearchConfig.selectedSearchFields && this1._workListService.advancedSearchConfig.selectedSearchFields != '') {
				finalFilterModel = this1._workListService.advancedSearchConfig.selectedSearchFields;
				let callback = this1.metaData.callbackFunctionName != null ? this1.metaData.callbackFunctionName.trim().toUpperCase() : '';
				if (callback != 'INITIALINSURANCEVERIFICATION' && callback != 'INSURANCEVERIFICATIONFOLLOWUP') {
					delete finalFilterModel['insuranceRep'];					
					let searchForm = this1._workListService.advancedSearchConfig.searchForm;
					delete searchForm['insuranceRep'];				
					 this1._workListService.advancedSearchConfig={'selectedSearchFields':finalFilterModel,'searchForm':searchForm};
				}
				this1.setAdvanceSearchFormValues();
				this1._workListService.singleSearchConfig = {};
				
			}
			else if (localStorage.getItem('advanceSearchFields') && localStorage.getItem('advanceSearchFields') != '' && this1.metaData.enableSavingState == true && this1.metaData.searchState == true) {
				finalFilterModel = JSON.parse(localStorage.getItem('advanceSearchFields'));
				 this1._workListService.advancedSearchConfig={'selectedSearchFields':finalFilterModel,'searchForm':JSON.parse(localStorage.getItem('searchForm'))};
				localStorage.setItem('singleSearchFields', '');
				localStorage.removeItem('advanceSearchFields');
				this1.setAdvanceSearchFormValues();
			}
			if(this1.defaultWidget && this1.defaultWidget != '' && this1.defaultWidget != 'All' && this1.defaultWidgetField && this1.defaultWidgetField != '')  {
				finalFilterModel[this1.defaultWidgetField] = { type: 'equals', filter: this1.defaultWidget, filterType: 'text' };
			}
			if(this1.defaultWidget == '') {
				delete finalFilterModel[this1.selectedWidgetField];
			}
			let sortModel = params.request.sortModel;
			if (this1.metaData.dataSource == 'API') {
				let parameters = [];
				this1.metaData.parameters.split('&').forEach(element => {
					let key = element.substring(
						element.lastIndexOf('{{') + 2,
						element.lastIndexOf('}}')
					);
					if (key.toLowerCase() == 'tenantid') {
						element = element.replace('{{' + key + '}}', this1._structureService.getCookie('tenantId'));
					}
					if (key.toLowerCase() == 'currentdate') {
						const userToTenantTimeRes = this1._workListService.userToTenantTime();
						const date = moment().add(userToTenantTimeRes[0], 'hours').add(userToTenantTimeRes[1], 'm').format('YYYYMMDD');
						element = element.replace('{{' + key + '}}', date);
					}
					if (key == 'patient_id') {
						let pIndex = this1.mapFields.findIndex(x => x.fieldName == key);
						element = element.replace('{{' + key + '}}', this1.mapFields[pIndex].mapField);
					}
					if (key == 'staff_id') {
						let pIndex = this1.mapFields.findIndex(x => x.fieldName == key);
						element = element.replace('{{' + key + '}}', this1.mapFields[pIndex].mapField);
					}
					if (key == 'parent_schedule_id') {
						let pIndex = this1.mapFields.findIndex(x => x.fieldName == key);
						element = element.replace('{{' + key + '}}', this1.mapFields[pIndex].mapField);
					}
					if (key == 'formID') {
						const formId = this1.metaData.associatedForm;
						element = element.replace('{{' + key + '}}', formId);
					}
					if (element == 'type=multiple') {
						this1.apiType = 'multiple';
					} else {
						this1.apiType = 'single';
						parameters.push(element);
					}
				});
				if (this1.metaData.graphqlApi) {
					let url = this1.metaData.graphqlEndpoint;
					let fieldList = this1.metaData.fieldList.split(',');
					let fieldString = '';
					fieldList.forEach(field => {
						if (field.includes('.')) {
							let colArray = field.split('.');
							let endString = '';
							colArray.forEach((element, index) => {
								fieldString = ` ${fieldString} ${element} `;
								if (index !== colArray.length - 1) {
									fieldString = ` ${fieldString} { `;
									endString = ` ${endString} } `;
								}

							});
							fieldString = ` ${fieldString} ${endString}  `;
						} else {
							fieldString = `${fieldString} ${field}`;
						}
					});
					let newQuery = this1.metaData.parameters.replace('$fields', fieldString);
					let filterModelArray = [];
					Object.keys(finalFilterModel).forEach((filter) => {
						let filterObject = {
							column: filter,
							filter: finalFilterModel[filter].filter ? finalFilterModel[filter].filter.toString().trim() : '',
							filterTo: finalFilterModel[filter].filterTo,
							filterIn: finalFilterModel[filter].filterIn,
							type: finalFilterModel[filter].type,
							filterCondition: finalFilterModel[filter].filterCondition ? finalFilterModel[filter].filterCondition : 'AND'
						}
						filterModelArray.push(filterObject);
					});
					let variables = {};
					let tenantIds= [];
					if (
						this1._structureService.getCookie('crossTenantId') &&
						this1._structureService.getCookie('crossTenantId') !== 'undefined' &&
						this1._structureService.getCookie('tenantId') !== this1._structureService.getCookie('crossTenantId')
					) {
						tenantIds.push(this1._structureService.getCookie('crossTenantId'));
					} else {
						tenantIds.push(this1._structureService.getCookie('tenantId'));
					}
					if(this1.metaData.enableLockingMode == false) {
						variables = {
							startRow: start,
							endRow: end,
							filter: filterModelArray,
							sorting: params.request.sortModel,
							rowGroups: params.request.rowGroupCols,
							groupKeys: params.request.groupKeys,
							widgetField: ["patientStatus"],
							tenantIds: tenantIds,
							lockConfig: false,
							createdBy: this1.userData.userId
						}
					} else {
						variables = {
							startRow: start,
							endRow: end,
							filter: filterModelArray,
							sorting: params.request.sortModel,
							rowGroups: params.request.rowGroupCols,
							groupKeys: params.request.groupKeys,
							widgetField: ["patientStatus"],
							tenantIds: tenantIds,
							lockConfig: true,
							createdBy: this1.userData.userId,
							appLock: this1.metaData.lockLevel == 'app' ? true : false
						}
					}
					getDataSourceGraphqlApi(params,url, newQuery, variables, this1);
					/**Call separate api call for fetching count to improve the loading based on worklist settings */
					if(this1.metaData.enableWidgetApi && this1.getDataCount && this1.apiCallTriggered) {
						variables['enableWidgetCount'] = true;
						getGraphqlWidgetCount(url, newQuery, variables, this1);
						this1.getDataCount = false;
					}
				} else {
					this1._workListService.getWorklistDataUsingAPI(this1.metaData.endpoint, parameters.join('&')).then((data) => {
						let rowData: any = data;
						if (this1.metaData.worklistType == 'single') {
							this1.rowData = data;
						} else {
							rowData.forEach(element => {
								if (element.formData && element.formData.length > 0) {
									this1.machFormIds = [];
									let formDataList = [];
									let objKeys = Object.keys(element.formData);
									objKeys.forEach(keys => {
										if (element.formData[keys]) {
											let formObj = {};
											let i = 1;
											let obj = {};
											element.formData[keys].forEach(elem => {
												let labelText = '';
												if (elem.label.lastIndexOf('{{') == -1) {
													labelText = elem.label;
													if (i == 1) {
														obj[elem.label] = { 'id': elem.element_id, 'type': elem.element_type };
													}
												} else {
													let key = elem.label.substring(elem.label.lastIndexOf('{{') + 2, elem.label.lastIndexOf('}}'));
													labelText = key;
													if (i == 1) {
														obj[key] = { 'id': elem.element_id, 'type': elem.element_type };
													}
												}
												if (elem.element_type == 'date' && elem.value && elem.value != null && elem.value != '') {
													let newDate = new Date(Number(elem.value) * 1000);
													formObj[labelText] = moment.utc(newDate).format('MM/DD/YYYY');
												} else {
													formObj[labelText] = elem.value;
												}
											});
											this1.machFormIds.push({ 'type': 'child' });
											this1.machFormIds.push(obj);
											i++;
											formObj['submissionID'] = Number(keys);
											formDataList.push(formObj);
										}
									});
									element.callRecords = formDataList;
								} else {
									element.callRecords = element.day_wise_time_range;
								}
							});
							this1.rowData = rowData;
							//this1.deactivateRouting = false;
							//this1.firstLoading = true;
							//this1.notificationMsg.hide('All');
						}
						this1.setDefaultWidgetConfig(this1.rowData);
						var server = {
							success: true,
							rows: this1.rowData,
						}
						let res = server;
						if (res.success) {
							const rows = res.rows;
							let lastRow = -1;
							if (res.rows.length < this1.cacheBlockSize) {
								lastRow = params.request.startRow + res.rows.length;
							}
							params.successCallback(rows, lastRow);
						} else {
							params.failCallback();
						}
					}).catch((ex) => {
						//this1.deactivateRouting = false;
						//this1.firstLoading = true;
						//this1.notificationMsg.hide('All');
					});
				}
			} else {
				const filterModel = params.request.filterModel;
				const sortModel = params.request.sortModel;
				let formattedSortModel = [];
				if (Object.keys(sortModel).length > 0) {
					sortModel.forEach(element => {
						let sortObj = { 'colId': this1.machFormIds[1][element.colId].id, 'sort': element.sort };
						if (this1.machFormIds[1][element.colId].elementType == 'simple_name') {
							sortObj.colId = this1.machFormIds[1][element.colId].id + '_1';
						}
						formattedSortModel.push(sortObj);
					});
				} else {
					let sortObj = { 'colId': 'id', 'sort': 'desc' };
					formattedSortModel.push(sortObj);
				}
				let formattedFilterModel = [];
				if (this1.customSearch == false) {
					this1.searchFieldText = '';
					this1.filterEnabledFields.forEach(element => {
						$('#checkbox' + element.fieldId).prop("checked", false);
					});
					this1.selectedSearchFields = [];
				}
				if (this1.searchFieldText != '' && this1.selectedSearchFields.length > 0) {
					let filterFields = this1.reportFields.filter(x => x.allowFilter == true && x.valueType != 'checkbox');
					this1.selectedSearchFields.forEach(element => {
						let filterObj = {
							'fieldName': element.fieldName,
							'fieldId': element.fieldId,
							'valueType': element.valueType,
							'filterCondition': 'OR'
						};
						if (element.valueType == 'number') {
							filterObj['filterDetail'] = { type: 'equals', filter: this1.searchFieldText, filterType: 'number' };
						} else if (element.valueType == 'date') {
							filterObj['filterDetail'] = { type: 'equals', dateFrom: this1.searchFieldText, filterType: 'date' };
						} else if (element.valueType == 'radio' || element.valueType == 'checkbox' || element.valueType == 'select') {
							filterObj['filterDetail'] = { type: 'contains', filter: this1.searchFieldText, filterType: 'text' };
						} else {
							filterObj['filterDetail'] = { type: 'contains', filter: this1.searchFieldText, filterType: 'text' };
						}
						formattedFilterModel.push(filterObj);
					});
				}
				if (Object.keys(filterModel).length > 0) {
					let filterModelKeys = Object.keys(filterModel);
					let widgetFilterKeys = filterModelKeys.filter(x => x == this1.selectedWidgetField);
					let otherModelKeys = filterModelKeys.filter(x => x != this1.selectedWidgetField);
					if (otherModelKeys.length > 0) {
						this1.searchFieldText = '';
						this1.filterEnabledFields.forEach(element => {
							$('#checkbox' + element.fieldId).prop("checked", false);
						});
						this1.selectedSearchFields = [];
						formattedFilterModel = [];
						filterModelKeys = filterModelKeys;
					} else if (widgetFilterKeys.length > 0) {
						filterModelKeys = widgetFilterKeys;
					}
					filterModelKeys.forEach(element => {
						if (filterModel[element].filter) {
							filterModel[element].filter = filterModel[element].filter.trim();
						}
						let filterObj = {
							'fieldName': element, 'filterDetail': filterModel[element]
						};
						/**while editing any entry from filtered data it should return to the same resultant page after completing editing. that time we take field id form report fields. */
						if (this1.machFormIds.length > 0) {
							filterObj['fieldId'] = this1.machFormIds[1][element].id;
							filterObj['valueType'] = this1.machFormIds[1][element].elementType;
						} else {
							const index = this1.reportFields.findIndex(x => x.fieldName == element);
							filterObj['fieldId'] = this1.reportFields[index].fieldId;
							filterObj['valueType'] = this1.reportFields[index].valueType;
						}
						formattedFilterModel.push(filterObj);
					});
				}
				/**Count of elements used for filtering having element type radio/checkbox/select */
				let count = formattedFilterModel.filter(x => x.valueType == 'radio' || x.valueType == 'checkbox' || x.valueType == 'select').length;
				let j = 1;
				if (count > 0) {
					formattedFilterModel.forEach(element => {
						if (element.valueType == 'radio' || element.valueType == 'checkbox' || element.valueType == 'select') {
							this1._workListService.getMachformFields(this1.formId, element.fieldId).refetch().then(({ data: response }) => {
								const options = response['getFormElementDetails'].options;
								let filterOptions = [];
								options.forEach(elem => {
									const optionValue = elem.optionValue.toLowerCase();
									const filterValue = element.filterDetail.filter.toLowerCase();
									element.otherExist = response['getFormElementDetails'].hasOtherOption;
									if (response['getFormElementDetails'].hasOtherOption == 1) {
										element.valueOther = filterValue;
									}
									if (element.valueType == 'radio' || element.valueType == 'select' || element.valueType == 'checkbox') {
										if (element.filterDetail.type == 'contains') {
											if (optionValue.includes(filterValue)) {
												filterOptions.push(elem.optionId);
											}
										}
										if (element.filterDetail.type == 'equals') {
											if (optionValue == filterValue) {
												filterOptions.push(elem.optionId);
											}
										}
									}
								});
								if (filterOptions.length == 0 && response['getFormElementDetails'].hasOtherOption == 0) {
									filterOptions.push(new Date().getTime());
								}
								let filterdetail = element.filterDetail;
								element.filterDetail = { type: filterdetail.type, filter: filterOptions.join(','), filterType: filterdetail.filterType };
								if (j == count) {
									getDataSource(params, start, this1, formattedFilterModel, formattedSortModel);
								}
								j++;
							});
						}
					});
				} else {
					getDataSource(params, start, this1, formattedFilterModel, formattedSortModel);
				}
				// } else {
				//   getDataSource(params, start, this1, formattedFilterModel, formattedSortModel);
				// }
			}
		}
	};
}
function getGraphqlWidgetCount(url, newQuery, variables, this1) {
	this1._workListService.getWorklistDataUsingGraphQLAPI(url, newQuery, variables, this1.prepareHeaderVariables()).then((data) => {
		if(data['data']) {
			let keyArray = Object.keys(data['data']);
			this1.widgetData = data['data'][keyArray[0]].filterCount;
			this1.setDefaultWidgetConfig();
		}
	});
}
function getDataSourceGraphqlApi(params,url, newQuery, variables, self) {
	if (self.apiCallTriggered) { //For avoiding the multiple api calls while loading dashboard if state is saved
		if (self.metaData.enableWidgetApi) {
			/**Fetching data only */
			variables['enableWidgetCount'] = false;
		}
	self._workListService.getWorklistDataUsingGraphQLAPI(url, newQuery, variables, self.prepareHeaderVariables()).then((data) => {
		self._intakeService.unlockPatients = [];
		let rowData: any = data;
		let keyArray = Object.keys(data['data']);
		rowData = JSON.parse(JSON.stringify(data['data'][keyArray[0]].data));
		self._intakeService.rowDataCount = rowData.length;
		self._intakeService.cellCount = 0;
		if(!self.metaData.enableWidgetApi){
			self.widgetData = data['data'][keyArray[0]].filterCount;
			self.setDefaultWidgetConfig(self.rowData);
		}
		if (rowData.length == 0) {
			self.rowData = [];
			self.gridApi.showNoRowsOverlay();
		} else {
			if (self.metaData.worklistType == 'single') {
				self.rowData = rowData;
			} else {
				rowData.forEach(element => {
					if (element.formData && element.formData.length > 0) {
						self.machFormIds = [];
						let formDataList = [];
						let objKeys = Object.keys(element.formData);
						objKeys.forEach(keys => {
							if (element.formData[keys]) {
								let formObj = {};
								let i = 1;
								let obj = {};
								element.formData[keys].forEach(elem => {
									let labelText = '';
									if (elem.label.lastIndexOf('{{') == -1) {
										labelText = elem.label;
										if (i == 1) {
											obj[elem.label] = { 'id': elem.element_id, 'type': elem.element_type };
										}
									} else {
										let key = elem.label.substring(elem.label.lastIndexOf('{{') + 2, elem.label.lastIndexOf('}}'));
										labelText = key;
										if (i == 1) {
											obj[key] = { 'id': elem.element_id, 'type': elem.element_type };
										}
									}
									if (elem.element_type == 'date' && elem.value && elem.value != null && elem.value != '') {
										let newDate = new Date(Number(elem.value) * 1000);
										formObj[labelText] = moment.utc(newDate).format('MM/DD/YYYY');
									} else {
										formObj[labelText] = elem.value;
									}
								});
								self.machFormIds.push({ 'type': 'child' });
								self.machFormIds.push(obj);
								i++;
								formObj['submissionID'] = Number(keys);
								formDataList.push(formObj);
							}
						});
						element.callRecords = formDataList;
					} else {
						element.callRecords = element.day_wise_time_range;
					}
				});
				self.rowData = rowData;
			}
			self.gridApi.hideOverlay();
		}
		/**unlock patient when sudden back to list page */
		self.setUnlockPatient();
		self.setColumnState = false;
		self.setSortState = false;
		self.setFilterState = false;
		setDataInGrid(params, self);
	}, (error) => {
		self.rowData = [];
		self.setDefaultWidgetConfig(self.rowData);
		self.gridApi.showNoRowsOverlay();
		setDataInGrid(params, self);
	});
	}
}

function setDataInGrid(params, refThis){
	const res = {
		success: true,
		rows: refThis.rowData,
	}
	if (res.success) {
		const rows = res.rows;
		let lastRow = -1;
		if (res.rows.length < refThis.cacheBlockSize) {
			lastRow = params.request.startRow + res.rows.length;
		}
		params.successCallback(rows, lastRow);
	} else {
		params.failCallback();
	}
}

function getDataSource(params, start, this1, formattedFilterModel, sortModel) {
	if (start == 0 && formattedFilterModel.length == 0 && sortModel[0].colId == 'id') {
		let formFieldArray = [];
		this1.dashboardWidgets.forEach((element) => {
			if (element.formField != 'all') {
				formFieldArray = this1.reportFields.filter((x) => x.fieldName == element.formField);
			}
		});
		if (formFieldArray.length > 0) {
			this1._workListService
				.getWidgetCount(this1.formId, formFieldArray[0].fieldId, this1.uniqueClass)
				.refetch()
				.then(({ data: response }) => {
					this1.widgetCounts = [];
					let totalcount = 0;
					let obj = {};
					response['getTotalBasedOnStatus'].forEach((element) => {
						if (element.option && element.option != null) {
							let option = element.option.toLowerCase();
							obj[option] = element.total;
						}
						totalcount = totalcount + element.total;
					});
					obj['all'] = totalcount;
					this1.widgetCounts.push(obj);
					this1.setDefaultWidgetConfigServerSide('', '');
				});
		}
	}
	this1._workListService
		.getWorkListFormDataWithFilter(
			this1.formId,
			this1.uniqueClass,
			start,
			this1.cacheBlockSize,
			this1.formFieldFrom,
			formattedFilterModel,
			sortModel
		)
		.refetch()
		.then(({ data: response }) => {
			if (this1.uniqueClass !== '') {
				this1.formRowData = response['getFormDataWithUniqueIDWithFilters'];
			} else {
				this1.formRowData = response['getFormDataWithFilters'];
			}
			let totalCount = 0;
			if (this1.formRowData && this1.formRowData.length > 0) {
				this1.machFormIds = [];
				this1.formDataList = [];
				let i = 1;
				let obj = {};
				this1.formRowData.forEach((element) => {
					const formObj = {};
					totalCount = element.total;
					element.elements.forEach((elem) => {
						formObj[elem.labelText] = elem.value;
						if (elem.valueType == 'radio' && elem.valueOther != '' && elem.value == '') {
							formObj[elem.labelText] = elem.valueOther;
						}
						if (elem.valueType == 'checkbox' && elem.valueOther != '') {
							if (formObj[elem.labelText] == '') {
								formObj[elem.labelText] = elem.valueOther;
							} else {
								formObj[elem.labelText] = formObj[elem.labelText] + ',' + elem.valueOther;
							}
						}
						if (i == 1) {
							obj[elem.labelText] = {
								id: elem.tid,
								elementType: elem.valueType,
								otherExist: elem.OtherExists
							};
						}
						if (elem.valueType == 'date') {
							if (elem.timestampForDate && elem.timestampForDate != null && elem.timestampForDate != '') {
								let newDate = new Date(Number(elem.timestampForDate));
								formObj[elem.labelText] = moment.utc(newDate).format('MM/DD/YYYY');
							} else {
								formObj[elem.labelText] = '';
							}
						}
					});
					formObj['submissionID'] = element.submissionID;
					formObj['slno'] = i;
					formObj['action'] = '';
					this1.formDataList.push(formObj);
					i++;
				});
				this1.machFormIds.push({ type: 'parent' });
				this1.machFormIds.push(obj);
				this1.rowData = this1.formDataList;
				this1.gridApi.hideOverlay();
			} else {
				this1.rowData = [];
				this1.gridApi.showNoRowsOverlay();
				$('.ag-row-stub').html(
					'<p style="margin-top:7px!important;text-align: center!important;">There are no data available.</p>'
				);
			}

			var server = {
				success: true,
				rows: this1.rowData
			};
			let res = server;
			let lastRowValue = params.request.startRow + res.rows.length;
			if (res.success) {
				const rows = res.rows;
				let lastRow = -1;
				if (res.rows.length < this1.cacheBlockSize || (totalCount != 0 && lastRowValue == totalCount)) {
					lastRow = params.request.startRow + res.rows.length;
				}
				params.successCallback(rows, lastRow);
			} else {
				params.failCallback();
			}
		});

}
function autosizeHeaders(event) {
	const MIN_HEIGHT = 35;
	if (event.finished !== false) {
		event.api.setHeaderHeight(MIN_HEIGHT);
		const headerCells = Array.from(document.querySelectorAll('#grid-wrapper .ag-header-cell-label'));
		let minHeight = MIN_HEIGHT;
		headerCells.forEach((element) => {
			minHeight = Math.max(minHeight, element.scrollHeight);
		});
		event.api.setHeaderHeight(minHeight);
	}
}
