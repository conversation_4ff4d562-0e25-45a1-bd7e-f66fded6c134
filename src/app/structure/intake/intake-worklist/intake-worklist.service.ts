import { Injectable, OnInit } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import { ApolloClient, createNetworkInterface } from 'apollo-client';
import { Apollo } from 'apollo-angular';
import { StructureService } from '../../structure.service';
import { ToolTipService } from '../../tool-tip.service';
import { FormsService } from '../../forms/forms.service';
import { Router, ActivatedRoute, Params, NavigationEnd } from '@angular/router';
import gql from 'graphql-tag';
import { configTimeZone } from '../../../../environments/environment';

declare var $: any;
declare var jQuery: any;

const getWorklistFormEntriesWithFilter = gql`
	query getFormDataWithFilters(
		$sessionId: String!
		$formID: Int
		$end: Int
		$start: Int
		$fromlabel: Int
		$fromCSS: Int
		$filterModel: [filter]
		$sortModel: [sortArray]
	) {
		getFormDataWithFilters(
			sessionId: $sessionId
			formID: $formID
			start: $start
			end: $end
			fromlabel: $fromlabel
			fromCSS: $fromCSS
			filterModel: $filterModel
			sortModel: $sortModel
		) {
			submissionID
			total
			elements {
				tid
				label
				labelText
				value
				valueType
				valueOther
				timestampForDate
				OtherExists
			}
		}
	}
`;

const getWorklistFormEntries = gql`
	query getFormData($sessionId: String!, $formID: Int, $end: Int, $start: Int, $fromlabel: Int, $fromCSS: Int) {
		getFormData(
			sessionId: $sessionId
			formID: $formID
			start: $start
			end: $end
			fromlabel: $fromlabel
			fromCSS: $fromCSS
		) {
			submissionID
			elements {
				tid
				label
				labelText
				value
				valueType
				valueOther
				timestampForDate
			}
		}
	}
`;
const getWorklistFormEntryDetails = gql`
	query getFormData($sessionId: String!, $formID: Int, $submissionId: Int) {
		getFormData(sessionId: $sessionId, formID: $formID, submissionId: $submissionId) {
			submissionID
			elements {
				tid
				label
				labelText
				value
				valueType
				valueOther
				timestampForDate
			}
		}
	}
`;
const getWorklistFormEntriesByGroupWithFilter = gql`
	query getFormDataWithUniqueIDWithFilters(
		$sessionId: String!
		$formID: Int
		$uniqueId: String!
		$end: Int
		$start: Int
		$fromlabel: Int
		$fromCSS: Int
		$filterModel: [filter]
		$sortModel: [sortArray]
	) {
		getFormDataWithUniqueIDWithFilters(
			sessionId: $sessionId
			formID: $formID
			uniqueId: $uniqueId
			start: $start
			end: $end
			fromlabel: $fromlabel
			fromCSS: $fromCSS
			filterModel: $filterModel
			sortModel: $sortModel
		) {
			submissionID
			total
			elements {
				tid
				label
				labelText
				value
				valueType
				valueOther
				timestampForDate
				OtherExists
			}
		}
	}
`;
const getWorklistFormEntriesByGroup = gql`
	query getFormDataWithUniqueID(
		$sessionId: String!
		$formID: Int
		$uniqueId: String!
		$end: Int
		$start: Int
		$fromlabel: Int
		$fromCSS: Int
	) {
		getFormDataWithUniqueID(
			sessionId: $sessionId
			formID: $formID
			uniqueId: $uniqueId
			start: $start
			end: $end
			fromlabel: $fromlabel
			fromCSS: $fromCSS
		) {
			submissionID
			elements {
				tid
				label
				labelText
				value
				valueType
				valueOther
				timestampForDate
			}
		}
	}
`;

const getWorklistFormElementIds = gql`
	query getFormDataWithUniqueID($sessionId: String!, $formID: Int, $uniqueId: String!) {
		getFormDataWithUniqueID(sessionId: $sessionId, formID: $formID, uniqueId: $uniqueId) {
			submissionID
			elements {
				tid
				label
				labelText
			}
		}
	}
`;
const getMachformFields = gql`
	query getFormElementDetails($sessionId: String!, $formID: Int, $elementId: Int) {
		getFormElementDetails(sessionId: $sessionId, formID: $formID, elementId: $elementId) {
			type
			options {
				optionId
				optionValue
			}
			hasOtherOption
			otherLabelText
		}
	}
`;
const getWorklistFiles = gql`
	query getFormData($sessionToken: String!, $formID: Int, $submissionId: Int, $elementType: String) {
		getFormData(sessionId: $sessionToken, formID: $formID, submissionId: $submissionId, elementType: $elementType) {
			submissionID
			elements {
				tid
				label
				labelText
				value
				valueType
				valueOther
				timestampForDate
				fileValues {
					name
					dbname
					pathTofile
				}
				filesCount
			}
		}
	}
`;
const getAllFormDataMultipleForm = gql`
	query getAllFormDataMultipleForm(
		$sessionToken: String!
		$formID: Int
		$mappingField: String
		$mappingFieldValue: String
	) {
		getAllFormDataMultipleForm(
			sessionId: $sessionToken
			formID: $formID
			mappingField: $mappingField
			mappingFieldValue: $mappingFieldValue
		) {
			submissionID
			elements {
				tid
				label
				labelText
				value
				tableValue
				timestampForDate
				valueOther
				valueType
				keyName
				filesCount
			}
		}
	}
`;
const getFormTags = gql`
	query getSessionTenant($sessionToken: String!) {
		getSessionTenant(sessionToken: $sessionToken) {
			formTags {
				id
				tagName
				tagMeta
			}
		}
	}
`;
const mutDeleteFormWorkList = gql`
	mutation updateFormWorklist($sessionToken: String!, $deleted: Int, $id: Int!, $name: String) {
		updateFormWorklist(sessionToken: $sessionToken, id: $id, params: { name: $name }, deleted: $deleted) {
			id
		}
	}
`;
const recallPatient = gql`
	mutation recallPatient($id: [ID!], $workflowType: WorkflowType!, $createdBy:Int! ) {
		recallPatient(id: $id, workflowType:$workflowType, createdBy:$createdBy) {
			status
		}
	}
`;
const getWorkListForms = gql`
	query getSessionTenant($sessionId: String!, $roleid: Int, $tagId: Int) {
		getSessionTenant(sessionToken: $sessionId) {
			WorklistForms(roleid: $roleid, tagId: $tagId) {
				id
				tagId
				formId
				tagName
				name
				facing
			}
		}
	}
`;
const mutDeleteWorklistEntry = gql`
	mutation deleteFormData($sessionId: String, $formID: Int, $submissionId: Int) {
		deleteFormData(sessionId: $sessionId, formID: $formID, submissionId: $submissionId) {
			submissionID
		}
	}
`;
const deleteWorklistColumnGroup = gql`
	mutation updateWorklistColumnGroup(
		$sessionToken: String!
		$id: Int!
		$status: Int
		$params: WorklistColumnGroupInput!
	) {
		updateWorklistColumnGroup(sessionToken: $sessionToken, id: $id, status: $status, params: $params) {
			id
		}
	}
`;
const deleteWorklistMenuGroup = gql`
	mutation updateWorklistMenuGroup(
		$sessionToken: String!
		$id: Int!
		$deleted: Int
		$params: WorklistMenuGroupInput!
	) {
		updateWorklistMenuGroup(sessionToken: $sessionToken, id: $id, deleted: $deleted, params: $params) {
			id
		}
	}
`;
const deleteWorklistMsgTemplate = gql`
	mutation updateWorklistMessageTemplate(
		$sessionToken: String!
		$id: Int!
		$deleted: Int
		$params: WorklistMessageTemplateInput!
	) {
		updateWorklistMessageTemplate(sessionToken: $sessionToken, id: $id, deleted: $deleted, params: $params) {
			id
		}
	}
`;
const updateSingleFormData = gql`
	mutation updateFormData($sessionId: String, $formID: Int, $submissionId: Int, $params: [elementInput]) {
		updateFormData(sessionId: $sessionId, formID: $formID, submissionId: $submissionId, params: $params) {
			submissionID
		}
	}
`;
const getTotalBasedOnStatus = gql`
	query getTotalBasedOnStatus($sessionId: String!, $formID: Int, $elementId: Int, $unique: Int, $uniqueId: String) {
		getTotalBasedOnStatus(
			sessionId: $sessionId
			formID: $formID
			elementId: $elementId
			unique: $unique
			uniqueId: $uniqueId
		) {
			total
			optionValue
			option
		}
	}
`;
const getAllAppNames = gql`
	query apps($sessionId: String!, $crossTenantId: Int) {
		apps(sessionId: $sessionId, crossTenantId: $crossTenantId) {
			name
			id
			guid
			description
			redirectUrl
			launchUrl
			imageName
			iconImagePath
			imageThumbName
		}
	}
`;

const getAllFormElementsDetails = gql`
	query getAllFormElementsDetails($sessionId: String!, $formID: Int) {
		getAllFormElementsDetails(sessionId: $sessionId, formID: $formID) {
			elementId
			elementTitle
			elementLabel
			elementType
		}
	}
`;
const insertFormData = gql`
	mutation insertFormData($sessionId: String, $formID: Int, $params: [elementInput]) {
		insertFormData(sessionId: $sessionId, formID: $formID, params: $params) {
			submissionID
		}
	}
`;
const saveWorklistApp = gql`
	mutation createAppWorklistMapping($params: String) {
		createAppWorklistMapping(params: $params) {
			status
		}
	}
`;
const updateWorklistApp = gql`
	mutation updateAppWorklistMapping($params: String) {
		updateAppWorklistMapping(params: $params) {
			status
		}
	}
`;
const miscellaneousRequired = gql`
query checkLists($patientId: ID!,$tenantIds:[Int],$type: [ListOfValueType!],$workflowId:ID,$patientEntryRequiredWorkflowId: ID,$bgColorRequired: Boolean) {
	checkLists(patientId: $patientId,tenantIds:$tenantIds,type: $type,workflowId:$workflowId,patientEntryRequiredWorkflowId: $patientEntryRequiredWorkflowId,
		bgColorRequired: $bgColorRequired) {
	  id
	  lovName
	  entryType
	  status
	  requiredItems {
		id
		itemName
		itemCode
		intakePatientEntry
		insuranceFollowupEntry
		backgroundColor
		controlType
		sortOrder
		itemValue
		linkedItemValueId
		itemId
		createdBy
		createdDate
		modifiedBy
		modifiedDate
		linkedItems {
		  id
		  itemValue
		}
	  }
	}
  }
`;
const miscellaneousCompleted = gql`
query checkLists($patientId: ID!,$tenantIds:[Int],$type: [ListOfValueType!],$workflowId:ID,$patientEntryRequiredWorkflowId: ID,$bgColorRequired: Boolean) {
	checkLists(patientId: $patientId,tenantIds:$tenantIds,type: $type,workflowId:$workflowId,patientEntryRequiredWorkflowId: $patientEntryRequiredWorkflowId,
		bgColorRequired: $bgColorRequired) {
	  id
	  lovName
	  entryType
	  status
	  completedItems {
		id
		itemName
		itemCode
		intakePatientEntry
		insuranceFollowupEntry
		backgroundColor
		controlType
		sortOrder
		itemValue
		linkedItemValueId
		itemId
		createdBy
		createdDate
		modifiedBy
		modifiedDate
		linkedItems {
		  id
		  itemValue
		}
	  }
	}
  }
`;
const checkLists= gql`

query checkLists($patientId: ID!,$tenantIds:[Int],$type: [ListOfValueType!],$workflowId:ID,$patientEntryRequiredWorkflowId: ID,$bgColorRequired: Boolean) {
	checkLists(patientId: $patientId,tenantIds:$tenantIds,type: $type,workflowId:$workflowId,patientEntryRequiredWorkflowId: $patientEntryRequiredWorkflowId,
		bgColorRequired: $bgColorRequired) {
	  id
	  lovName
	  entryType
	  status
	  requiredItems {
		id
		itemName
		itemCode
		intakePatientEntry
		insuranceFollowupEntry
		backgroundColor
		controlType
		sortOrder
		itemValue
		linkedItemValueId
		itemId
		createdBy
		createdDate
		modifiedBy
		modifiedDate
		linkedItems {
		  id
		  itemValue
		}
	  }
	  completedItems {
		id
		itemName
		itemCode
		intakePatientEntry
		insuranceFollowupEntry
		backgroundColor
		controlType
		sortOrder
		itemValue
		linkedItemValueId
		itemId
		createdBy
		createdDate
		modifiedBy
		modifiedDate
		linkedItems {
		  id
		  itemValue
		}
	  }
	}
  }
  
			`;
			
// mutation{updateFormData(sessionId:"AQIC5wM2LY4Sfcy8uaWabRT7-oG_unpLSCYYpUoMv_e74kQ.*AAJTSQACMDEAAlNLABQtMzg4NzAyMzkyNTI0NjI5NTYwNAACUzEAAA..*",formID:37523,submissionId:2,params:[{id:"5",value:"WWW updated"},{id:"6",value:"WWW updated"}]) {
//   submissionID
// }}
interface QueryResponse {
	getWorklistFormEntries: any;
}
@Injectable()
export class IntakeWorkListService {
	worklistDetails = {};
	timezone;
	systemUrlToken = '';
	papperWorkList: any;
	advancedSearchConfig={};
	singleSearchConfig = {};
	browserCache = 'sharedvariable';
	indexDbConfig = 'WORKLIST_DATA';
	constructor(
		private _http: Http,
		private router: Router,
		public _structureService: StructureService,
		public _formService: FormsService,
		private apollo: Apollo,
		public toolTipService: ToolTipService
	) {
		this.timezone = configTimeZone();
	}
	getMiscellaneousCompletedItems(variables) {
		const apiConfig = {
			method: 'GET',
			data: miscellaneousCompleted,
			variables:variables, 
			requestType: 'gql',
			use: 'pulseWorklistGraphApi',
			nested: false,
		};
		return this._structureService.requestData(apiConfig);
	}
	getMiscellaneousRequiredItems(variables) {
		const apiConfig = {
			method: 'GET',
			data: miscellaneousRequired,
			variables:variables, 
			requestType: 'gql',
			use: 'pulseWorklistGraphApi',
			nested: false,
		};
		return this._structureService.requestData(apiConfig);
	}
	getPaperWork(variables) {
		console.log(variables);
		//if (!this.papperWorkList) {
		const apiConfig = {
		method: 'GET',
		data: checkLists,
		variables:variables, 
		requestType: 'gql',
		use: 'pulseWorklistGraphApi',
		nested: false,
		noLoader: true
		};
		this.papperWorkList = this._structureService.requestData(apiConfig);
		//}
		return this.papperWorkList;
		} 
	getWorklistFormElementIds(formId) {
		const subscription = this.apollo.use('worklistGraphApi').watchQuery<QueryResponse>({
			query: getAllFormElementsDetails,
			variables: {
				sessionId: this._structureService.getCookie('authenticationToken'),
				formID: Number(formId)
			}
		});
		subscription.subscribe(({ data }) => {});
		return subscription;
	}
	getWidgetCount(formId, fieldId, uniqueClass) {
		const subscription = this.apollo.use('worklistGraphApi').watchQuery<QueryResponse>({
			query: getTotalBasedOnStatus,
			variables: {
				sessionId: this._structureService.getCookie('authenticationToken'),
				formID: Number(formId),
				elementId: fieldId,
				uniqueId: uniqueClass,
				unique: uniqueClass != '' ? 1 : 0
			}
		});
		subscription.subscribe(({ data }) => {});
		return subscription;
	}
	getWorkListFormDataWithFilter(formId, uniqueClass, start, end, formFieldFrom, filterModel, sortModel) {
		let query;
		let variables = {
			sessionId: this._structureService.getCookie('authenticationToken'),
			formID: Number(formId),
			start: start,
			end: end,
			filterModel: filterModel,
			sortModel: sortModel
		};
		if (formFieldFrom == 'fromlabel') {
			variables['fromlabel'] = 1;
			variables['fromCSS'] = 0;
		} else {
			variables['fromlabel'] = 0;
			variables['fromCSS'] = 1;
		}
		if (uniqueClass != '') {
			variables['uniqueId'] = uniqueClass;
			query = getWorklistFormEntriesByGroupWithFilter;
		} else {
			query = getWorklistFormEntriesWithFilter;
		}
		const subscription = this.apollo.use('worklistGraphApi').watchQuery<QueryResponse>({
			query: query,
			variables: variables,
			fetchPolicy: 'network-only'
		});
		subscription.subscribe(({ data }) => {});
		return subscription;
		
	}
	getWorkListFormData(formId, uniqueClass, start, end, formFieldFrom) {
		let query;
		let variables = {
			sessionId: this._structureService.getCookie('authenticationToken'),
			formID: Number(formId),
			start: start,
			end: end
		};
		if (formFieldFrom == 'fromlabel') {
			variables['fromlabel'] = 1;
			variables['fromCSS'] = 0;
		} else {
			variables['fromlabel'] = 0;
			variables['fromCSS'] = 1;
		}
		if (uniqueClass != '') {
			variables['uniqueId'] = uniqueClass;
			query = getWorklistFormEntriesByGroup;
		} else {
			query = getWorklistFormEntries;
		}
		const subscription = this.apollo.use('worklistGraphApi').watchQuery<QueryResponse>({
			query: query,
			variables: variables,
			fetchPolicy: 'network-only'
		});
		subscription.subscribe(({ data }) => {});
		return subscription;
	}
	getFormEntryDetails(formId, submissionId) {
		let variables = {
			sessionId: this._structureService.getCookie('authenticationToken'),
			formID: Number(formId),
			submissionId: submissionId
		};
		const subscription = this.apollo.use('worklistGraphApi').watchQuery<QueryResponse>({
			query: getWorklistFormEntryDetails,
			variables: variables,
			fetchPolicy: 'network-only'
		});
		subscription.subscribe(({ data }) => {});
		return subscription;
	}
	mutsaveFormWorkListQuery() {
		let Createworklist = `mutation createFormWorklist( $sessionToken:String!,$params : FormWorklistInput`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			Createworklist =
				Createworklist +
				`,
      $crossTenantId : Int!`;
		}
		Createworklist = Createworklist + `){createFormWorklist(sessionToken:$sessionToken params:$params`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			Createworklist = Createworklist + `,crossTenantId:$crossTenantId`;
		}
		Createworklist =
			Createworklist +
			`){
     id}
    }`;
		return gql`${Createworklist}`;
	}
	saveWorklist(params) {
		let variables: any = {
			params: params,
			sessionToken: this._structureService.getCookie('authID')
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
			method: 'POST',
			data: this.mutsaveFormWorkListQuery(),
			variables: variables,
			requestType: 'gql',
			use: ''
		};
		const responce = this._structureService.requestData(apiConfig);
		return responce;
	}
	getFormTags() {
		const variables = {
			sessionToken: this._structureService.getCookie('authenticationToken')
		};
		const apiConfig = {
			method: 'GET',
			data: getFormTags,
			variables: variables,
			requestType: 'gql',
			use: '',
			nested: false
		};
		return this._structureService.requestData(apiConfig);
	}
	getWorklistsQuery() {
		let worklists = `query getSessionTenant(
      $sessionToken: String!`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			worklists =
				worklists +
				`,
      $crossTenantId : Int`;
		}
		worklists =
			worklists +
			`){
      getSessionTenant(sessionToken:$sessionToken`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			worklists =
				worklists +
				`,
      crossTenantId:$crossTenantId`;
		}
		worklists =
			worklists +
			`){
      formWorklists{
        id
        name
        description
        active
        desktopMenu{
          menuName
          menuGroup{
            id
            name
            tenantId
          }
          menuIndex
        }
        reportForms{
          id
          name
        }
        statusForms{
          id
          name
        }
		  integrationSettings{
			integrationDataType
			enableFileCenter
			filingCenterPath
			integrationType
			progressNoteSubject
			adminConfig
			apiEndPoint
			authorizationKey
			callbackUrl
		  }
		  
      }
    }
  }
  `;
		return gql`${worklists}`;
	}

	getWorkLists() {
		let variables: any = {
			sessionToken: this._structureService.getCookie('authenticationToken')
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
			method: 'GET',
			data: this.getWorklistsQuery(),
			variables: variables,
			requestType: 'gql',
			use: '',
			nested: false,
			noLoader: true
		};
		return this._structureService.requestData(apiConfig);
	}
	recallPatient(variable) {
		return this.apollo.use('pulseWorklistGraphApi').mutate<any>({
			mutation: recallPatient,
			variables: variable
		  }).map(
			res => res
			);
	}
	deleteWorklist(listId, listName) {
		const variables = {
			id: Number(listId),
			deleted: 0,
			sessionToken: this._structureService.getCookie('authID'),
			name: listName
		};
		const apiConfig = {
			method: 'POST',
			data: mutDeleteFormWorkList,
			variables: variables,
			requestType: 'gql',
			use: ''
		};
		const responce = this._structureService.requestData(apiConfig);
		return responce;
	}
	mutUpdateFormWorkListQuery() {
		let updateworklist = `mutation updateFormWorklist( $sessionToken:String!,$params : FormWorklistInput!,$deleted: Int,$id: Int!`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			updateworklist =
				updateworklist +
				`,
      $crossTenantId : Int!`;
		}
		updateworklist =
			updateworklist + `){updateFormWorklist(sessionToken:$sessionToken params:$params,id: $id,deleted: $deleted`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			updateworklist = updateworklist + `,crossTenantId:$crossTenantId`;
		}
		updateworklist =
			updateworklist +
			`){
     id}
    }`;
		return gql`${updateworklist}`;
	}
	updateWorklist(params, listId) {
		let variables: any = {
			params: params,
			sessionToken: this._structureService.getCookie('authID'),
			deleted: 1,
			id: listId
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
			method: 'POST',
			data: this.mutUpdateFormWorkListQuery(),
			variables: variables,
			requestType: 'gql',
			use: ''
		};
		const responce = this._structureService.requestData(apiConfig);
		return responce;
	}
	getWorkListDetailsQuery() {
		let worklistDetails = `query getSessionTenant(
      $sessionToken: String!
      $id: Int`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			worklistDetails =
				worklistDetails +
				`,
      $crossTenantId : Int`;
		}
		worklistDetails =
			worklistDetails +
			`){
      getSessionTenant(sessionToken:$sessionToken`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			worklistDetails =
				worklistDetails +
				`,
      crossTenantId:$crossTenantId`;
		}
		worklistDetails =
			worklistDetails +
			`){
      formWorklists(id:$id){
        id
        name
        description
        active
        strategy
        associatedEntity
        desktopMenu{
          menuName
          menuGroup{
            id
            name
            tenantId
          }
          menuIndex
        }
        reportForms{
          id
          name
        }
        statusForms{
          id
          name
		}
		integrationSettings{
			integrationDataType
			enableFileCenter
			filingCenterPath
			integrationType
			progressNoteSubject
			adminConfig
			apiEndPoint
			authorizationKey
			callbackUrl
		  }
        reportFields{
          formId
          fieldName
          headerName
          allowSort
          displayIndex
          visibility
          valueType
          allowFilter
          filterType
          allowEdit
          allowBulkEdit
          allowRowGrouping
          groupName
          allowPrefillData
          prefillMethod
          apiEndpoint
          parameters
          fieldId
          checkboxSelection
          prefillFormOption
          prefillOptionForm
          prefillListOption
          prefillOptionFormField
          prefillExtraOptions
          fieldType
          mapField
	  showInEditor
	  largeCellEditor
          linkField
          clearFilter
          applyFilter
          includeInExport
          columnWidth
          enableEventAssociation
          cellEvent
          allowCellStyle
          cellStyles{
			allowRelatedFeild
            formField
            fieldValue
            fieldText
            expression
            cellStyleMethod
            bgColour
            fontColour
            cssStyleClass
            styleOption
            expressionType
            day
            month
            year
            value
            contentType
            iconClass
            iconColor
          }
          allowCellFormat
          cellFormat{
            cellValue
            expression
            formatOption
            formattedValue
          }
          allowCellValueChange
          newCellValue
          allowQuickSearch
          enableAutoSelect
          enableNotification
          notifySpecificValue
          notificationFieldValue
          push
          pushTemplate
          sms
          smsTemplate
          email
          emailTemplate
          notifyRolesOnValueChange
          notifyRecipientOnValueChange
          fieldValueDelimiter
          hideColumnBasedField
		  hideColumnValue
		  headerTooltip
		  enableHyperlink
		  associatedAction
		  enableTenantTimezone
        }
        dashboardWidgets{
          displayText
          displayIndex
          displayIcon
          iconColor
          formField
          textAlign
          rightBorder
          count
          class
          widgetValue
          hideColumns
          defaultWidget
        }
        singleWorklistAction{
            actionButton{
              actionLabel
              actionIcon
              actionLink
              buttonIndex
              cssClass
              tooltiptext
              actionType
              customJS
              customJsURL
              actionCallbackFunction
              buttonStyle
              actionMode
              actionButtonType
              formField
              selectionMode
              shortLink
              fieldValues
              mapField
              enableIntegration
              enableNotification
              push
              pushTemplate
              sms
              smsTemplate
              email
              emailTemplate
              notifyRolesOnAction
              notifyRecipientOnAction
              actionRoles
              actionPrivileges
              showOnlyLoginUser
              loginUserMatchField
              actionFields{
                associatedField
                fieldValues
              }
              actionMenu{
                itemName
                itemIndex
                itemActionLink
                cssClass
                itemActionType
                itemActionCallbackFunction
                itemFormField
              }
            }
          }
      }
    }
  }
  `;
		return gql`${worklistDetails}`;
	}
	getWorkListEditDetailsQuery() {
		let worklistDetails = `query getSessionTenant(
      $sessionToken: String!
      $id: Int`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			worklistDetails =
				worklistDetails +
				`,
      $crossTenantId : Int`;
		}
		worklistDetails =
			worklistDetails +
			`){
      getSessionTenant(sessionToken:$sessionToken`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			worklistDetails =
				worklistDetails +
				`,
      crossTenantId:$crossTenantId`;
		}
		worklistDetails =
			worklistDetails +
			`){
      formWorklists(id:$id){
        id
        name
        description
        active
        strategy
        associatedEntity
        desktopMenu{
          menuName
          menuGroup{
            id
            name
            tenantId
          }
          menuIndex
        }
        reportForms{
          id
          name
        }
        statusForms{
          id
          name
        }
        reportFields{
          formId
          fieldName
          headerName
          allowSort
          displayIndex
          visibility
          valueType
          allowFilter
          filterType
          allowEdit
          allowBulkEdit
          allowRowGrouping
          groupName
          allowPrefillData
          prefillMethod
          apiEndpoint
          parameters
          fieldId
          checkboxSelection
          prefillFormOption
          prefillOptionForm
          prefillListOption
          prefillOptionFormField
          prefillExtraOptions
          fieldType
          mapField
	  showInEditor
	  largeCellEditor
          linkField
          clearFilter
          applyFilter
          includeInExport
          columnWidth
          enableEventAssociation
          cellEvent
          allowCellStyle
          cellStyles{
			allowRelatedFeild
            formField
            fieldValue
            fieldText
            expression
            cellStyleMethod
            bgColour
            fontColour
            cssStyleClass
            styleOption
            expressionType
            day
            month
            year
            value
            contentType
            iconClass
            iconColor
          }
          allowCellFormat
          cellFormat{
            cellValue
            expression
            formatOption
            formattedValue
          }
          allowCellValueChange
          newCellValue
          allowQuickSearch
          enableAutoSelect
          enableNotification
          notifySpecificValue
          notificationFieldValue
          push
          pushTemplate
          sms
          smsTemplate
          email
          emailTemplate
          notifyRolesOnValueChange
          notifyRecipientOnValueChange
          fieldValueDelimiter
          hideColumnBasedField
		  hideColumnValue
		  headerTooltip
        }
        dashboardWidgets{
          displayText
          displayIndex
          displayIcon
          iconColor
          formField
          textAlign
          rightBorder
          count
          class
          widgetValue
          hideColumns
        }
        singleWorklistAction{
            actionButton{
              actionLabel
              actionIcon
              actionLink
              buttonIndex
              cssClass
              tooltiptext
              actionType
              customJS
              customJsURL
              actionCallbackFunction
              buttonStyle
              actionMode
              actionButtonType
              formField
              selectionMode
              shortLink
              fieldValues
              mapField
              enableIntegration
              enableNotification
              push
              pushTemplate
              sms
              smsTemplate
              email
              emailTemplate
              notifyRolesOnAction
              notifyRecipientOnAction
              actionRoles
              actionPrivileges
              showOnlyLoginUser
              loginUserMatchField
              actionFields{
                associatedField
                fieldValues
              }
              actionMenu{
                itemName
                itemIndex
                itemActionLink
                cssClass
                itemActionType
                itemActionCallbackFunction
                itemFormField
              }
            }
          }
      }
    }
  }
  `;
		return gql`${worklistDetails}`;
	}	
	getWorklistDetails(listId) {
		let variables: any = {
			sessionToken: this._structureService.getCookie('authenticationToken'),
			id: Number(listId)
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
			method: 'GET',
			data: this.getWorkListDetailsQuery(),
			variables: variables,
			requestType: 'gql',
			use: '',
			nested: false,
			noLoader: true
		};
		return this._structureService.requestData(apiConfig);
	}
	getWorklistEditDetails(listId) {
		let variables: any = {
			sessionToken: this._structureService.getCookie('authenticationToken'),
			id: Number(listId)
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
			method: 'GET',
			data: this.getWorkListEditDetailsQuery(),
			variables: variables,
			requestType: 'gql',
			use: '',
			nested: false,
			noLoader: true
		};
		return this._structureService.requestData(apiConfig);
	
	}
	getWorkListForms(tagId, roleId) {
		const variables = {
			sessionId: this._structureService.getCookie('authID'),
			tagId: tagId,
			roleid: roleId
		};
		const apiConfig = {
			method: 'GET',
			data: getWorkListForms,
			variables: variables,
			requestType: 'gql',
			use: '',
			noLoader: true
		};
		return this._structureService.requestData(apiConfig);
	}
	deleteWorklistEntry(formId, submissionID) {
		return this.apollo
			.use('worklistGraphApi')
			.mutate({
				mutation: mutDeleteWorklistEntry,
				variables: {
					sessionId: this._structureService.getCookie('authenticationToken'),
					formID: formId,
					submissionId: submissionID
				}
			})
			.map((res) => res);
	}
	saveWorklistAppMapping(params) {
		return this.apollo
			.use('pulseWorklistGraphApi')
			.mutate({
				mutation: saveWorklistApp,
				variables: {
					params: params
				}
			})
			.map((res) => res);
	}
	updateWorklistAppMapping(params) {
		return this.apollo
			.use('pulseWorklistGraphApi')
			.mutate({
				mutation: updateWorklistApp,
				variables: {
					params: params
				}
			})
			.map((res) => res);
	}
	createWorklistMenuGroupQuery() {
		let createMenuGp = `mutation createWorklistMenuGroup( $sessionToken:String!,$params : WorklistMenuGroupInput`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			createMenuGp =
				createMenuGp +
				`,
        $crossTenantId : Int!`;
		}
		createMenuGp = createMenuGp + `){createWorklistMenuGroup(sessionToken:$sessionToken params:$params`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			createMenuGp = createMenuGp + `,crossTenantId:$crossTenantId`;
		}
		createMenuGp =
			createMenuGp +
			`){
      id}
      }`;
		return gql`${createMenuGp}`;
	}
	createWorklistMenugroup(menuGroupName, createdBy) {
		let variables: any = {
			sessionToken: this._structureService.getCookie('authID'),
			params: {
				name: menuGroupName,
				status: 1,
				createdBy: createdBy
			}
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
			method: 'POST',
			data: this.createWorklistMenuGroupQuery(),
			variables: variables,
			requestType: 'gql',
			use: ''
		};
		const responce = this._structureService.requestData(apiConfig);
		return responce;
	}
	getWorklistMenuGroupQuery() {
		let menuGroup = `query getSessionTenant(
      $sessionToken: String!`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			menuGroup =
				menuGroup +
				`,
        $crossTenantId : Int`;
		}
		menuGroup =
			menuGroup +
			`){
        getSessionTenant(sessionToken:$sessionToken`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			menuGroup =
				menuGroup +
				`,
        crossTenantId:$crossTenantId`;
		}
		menuGroup =
			menuGroup +
			`){
        formWorklistMenuGroup{
          id
          tenantId
          name
          createdBy
          createdOn
        }
        }
    }`;
		return gql`${menuGroup}`;
	}

	getWorklistMenuGroup() {
		let variables: any = {
			sessionToken: this._structureService.getCookie('authID')
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
			method: 'GET',
			data: this.getWorklistMenuGroupQuery(),
			variables: variables,
			requestType: 'gql',
			use: ''
		};
		const responce = this._structureService.requestData(apiConfig);
		return responce;
	}

	deleteMenuGroup(deleteId, deleteName) {
		const variables = {
			sessionToken: this._structureService.getCookie('authID'),
			id: deleteId,
			deleted: 0,
			params: {
				name: deleteName
			}
		};
		const apiConfig = {
			method: 'POST',
			data: deleteWorklistMenuGroup,
			variables: variables,
			requestType: 'gql',
			use: ''
		};
		const responce = this._structureService.requestData(apiConfig);
		return responce;
	}
	updateWorklistMenuGroupQuery() {
		let updateMenuGp = `mutation updateWorklistMenuGroup( $sessionToken:String!,$params : WorklistMenuGroupInput!,$deleted: Int,$id: Int!`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			updateMenuGp =
				updateMenuGp +
				`,
      $crossTenantId : Int!`;
		}
		updateMenuGp =
			updateMenuGp +
			`){updateWorklistMenuGroup(sessionToken:$sessionToken params:$params,id: $id,deleted: $deleted`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			updateMenuGp = updateMenuGp + `,crossTenantId:$crossTenantId`;
		}
		updateMenuGp =
			updateMenuGp +
			`){
     id}
    }`;
		return gql`${updateMenuGp}`;
	}
	updateMenuGroup(updateId, updateName) {
		let variables: any = {
			sessionToken: this._structureService.getCookie('authID'),
			id: updateId,
			deleted: 1,
			params: {
				name: updateName
			}
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
			method: 'POST',
			data: this.updateWorklistMenuGroupQuery(),
			variables: variables,
			requestType: 'gql',
			use: ''
		};
		const responce = this._structureService.requestData(apiConfig);
		return responce;
	}
	createWorklistColumnGroupQuery() {
		let createColumnGp = `mutation createWorklistColumnGroup( $sessionToken:String!,$params : WorklistColumnGroupInput`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			createColumnGp =
				createColumnGp +
				`,
        $crossTenantId : Int!`;
		}
		createColumnGp = createColumnGp + `){createWorklistColumnGroup(sessionToken:$sessionToken params:$params`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			createColumnGp = createColumnGp + `,crossTenantId:$crossTenantId`;
		}
		createColumnGp =
			createColumnGp +
			`){
      id}
      }`;
		return gql`${createColumnGp}`;
	}
	createWorklistColumngroup(params) {
		let variables: any = {
			sessionToken: this._structureService.getCookie('authID'),
			params: params
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
			method: 'POST',
			data: this.createWorklistColumnGroupQuery(),
			variables: variables,
			requestType: 'gql',
			use: ''
		};
		const responce = this._structureService.requestData(apiConfig);
		return responce;
	}
	getWorklistColumnGroupQuery() {
		let columnGroup = `query getSessionTenant(
      $sessionToken: String!,$worklistId :Int`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			columnGroup =
				columnGroup +
				`,
        $crossTenantId : Int`;
		}
		columnGroup =
			columnGroup +
			`){
        getSessionTenant(sessionToken:$sessionToken`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			columnGroup =
				columnGroup +
				`,
        crossTenantId:$crossTenantId`;
		}
		columnGroup =
			columnGroup +
			`){
        formWorklistColumnGroup(worklistId:$worklistId){
          id
          tenantId
          name
          description
          createdBy
          createdOn
        }
      }
    }`;
		return gql`${columnGroup}`;
	}
	getWorklistColumnGroup(worklistId) {
		let variables: any = {
			sessionToken: this._structureService.getCookie('authID'),
			worklistId: worklistId
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
			method: 'GET',
			data: this.getWorklistColumnGroupQuery(),
			variables: variables,
			requestType: 'gql',
			use: ''
		};
		const responce = this._structureService.requestData(apiConfig);
		return responce;
	}
	deleteColumnGroup(deleteId, deleteName) {
		const variables = {
			sessionToken: this._structureService.getCookie('authID'),
			id: deleteId,
			status: 1,
			params: {
				name: deleteName
			}
		};
		const apiConfig = {
			method: 'POST',
			data: deleteWorklistColumnGroup,
			variables: variables,
			requestType: 'gql',
			use: ''
		};
		const responce = this._structureService.requestData(apiConfig);
		return responce;
	}
	updateWorklistColumnGroupQuery() {
		let updatecolumnGp = `mutation updateWorklistColumnGroup( $sessionToken:String!,$params : WorklistColumnGroupInput!,$status: Int,$id: Int!`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			updatecolumnGp =
				updatecolumnGp +
				`,
      $crossTenantId : Int!`;
		}
		updatecolumnGp =
			updatecolumnGp +
			`){updateWorklistColumnGroup(sessionToken:$sessionToken params:$params,id: $id,status: $status`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			updatecolumnGp = updatecolumnGp + `,crossTenantId:$crossTenantId`;
		}
		updatecolumnGp =
			updatecolumnGp +
			`){
     id}
    }`;
		return gql`${updatecolumnGp}`;
	}
	updateColumnGroup(updateId, params) {
		let variables: any = {
			sessionToken: this._structureService.getCookie('authID'),
			id: updateId,
			status: 0,
			params: params
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
			method: 'POST',
			data: this.updateWorklistColumnGroupQuery(),
			variables: variables,
			requestType: 'gql',
			use: ''
		};
		const responce = this._structureService.requestData(apiConfig);
		return responce;
	}
	getAllFormNames(tenantId) {
		const getAllFormNamesApi = this._structureService.apiBaseUrl + 'citus-health/v4/get-all-survey-names.php';
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('crossTenantId') !== this._structureService.getCookie('tenantId')
		) {
			tenantId.crossTenantId = this._structureService.getCookie('crossTenantId');
		}
		var apiConfig = { url: getAllFormNamesApi, requestType: 'http', data: tenantId };
		return this._structureService.requestData(apiConfig);
	}
	getFormsInType(params) {
		const getAllFormNamesApi = this._structureService.apiBaseUrl + 'citus-health/v4/get-all-survey-names.php';
		var apiConfig = { url: getAllFormNamesApi, requestType: 'http', data: params };
		return this._structureService.requestData(apiConfig);
	}
	updateSingleFormData(params, formId, submissionId) {
		return this.apollo
			.use('worklistGraphApi')
			.mutate({
				mutation: updateSingleFormData,
				variables: {
					sessionId: this._structureService.getCookie('authenticationToken'),
					formID: formId,
					submissionId: submissionId,
					params: params
				}
			})
			.map((res) => res);
	}
	getMachformFields(formId, elementId) {
		const subscription = this.apollo.use('worklistGraphApi').watchQuery<QueryResponse>({
			query: getMachformFields,
			variables: {
				sessionId: this._structureService.getCookie('authenticationToken'),
				formID: formId,
				elementId: elementId
			}
		});
		subscription.subscribe(({ data }) => {});
		return subscription;
	}
	getassociatedPatients(tenantid) {
		var data = 'tenantId=' + tenantid + '&isTenantRoles=' + undefined + '&roleId=3';
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			data = data + '&crossTenantId=' + this._structureService.getCookie('crossTenantId');
		}
		var apiConfig = {
			url: this._structureService.apiBaseUrl + 'citus-health/v4/get-tenant-associated-patient.php',
			requestType: 'http',
			data: data
		};
		return this._structureService.requestData(apiConfig);
	}
	getWorklistFiles(formId, submissionId) {
		const subscription = this.apollo.use('worklistGraphApi').watchQuery<QueryResponse>({
			query: getWorklistFiles,
			variables: {
				sessionToken: this._structureService.getCookie('authID'),
				formID: formId,
				submissionId: submissionId,
				elementType: 'file'
			},
			fetchPolicy: 'network-only'
		});
		subscription.subscribe(({ data }) => {});
		return subscription;
	}
	/*getWorklistDataUsingAPI(url, params, graphqlQuery) {
		let headers = new Headers();
		console.log(graphqlQuery);
		headers.append('Content-Type', 'application/x-www-form-urlencoded');
		headers.append('Authentication-Token', this._structureService.getCookie('authID'));
		let options = new RequestOptions({ headers: headers });
		
		var apiConfig = { url: url, requestType: 'http', data: params };
		return this._structureService.requestData(apiConfig);
	}*/
	// getWorklistDataUsingAPI(url, params, graphqlQuery = '') {
	// 	let headers = new Headers();
	// 	headers.append('Content-Type', 'application/x-www-form-urlencoded');
	// 	headers.append('Authentication-Token', this._structureService.getCookie('authID'));
	// 	let options = new RequestOptions({ headers: headers });
	// 	var apiConfig = {
	// 		url: url,
	// 		requestType: 'http',
	// 		data: params,
	// 		method: 'GET',
	// 		headers: {},
	// 		replaceHeaders: false
	// 	};
	// 	if (graphqlQuery) {
	// 		apiConfig.replaceHeaders = true;
	// 		apiConfig.headers = { 'Content-Type': 'application/json' };
	// 		apiConfig.method = 'POST';
	// 		console.log(typeof graphqlQuery);
	// 		console.log(graphqlQuery);
	// 		apiConfig.data = JSON.parse(graphqlQuery);
	// 	}
	// 	console.log(apiConfig);
	// 	return this._structureService.requestData(apiConfig);
	// }
	addFormEntry(formId, params) {
		return this.apollo
			.use('worklistGraphApi')
			.mutate({
				mutation: insertFormData,
				variables: {
					sessionId: this._structureService.getCookie('authenticationToken'),
					formID: formId,
					params: params
				}
			})
			.map((res) => res);
	}
	getChildFormData(formId, mappingField, mappingFieldValue) {
		const subscription = this.apollo.use('worklistGraphApi').watchQuery<QueryResponse>({
			query: getAllFormDataMultipleForm,
			variables: {
				sessionToken: this._structureService.getCookie('authID'),
				formID: formId,
				mappingField: mappingField,
				mappingFieldValue: mappingFieldValue
			},
			fetchPolicy: 'network-only'
		});
		subscription.subscribe(({ data }) => {});
		return subscription;
	}
	userToTenantTime() {
		let activeTimeZone = new Date().getTimezoneOffset() * -1 * -1 / 60 * -1;
		let timeToPassDay = [];
		let timeDifference;
		if (activeTimeZone > this.timezone) {
			timeDifference = -1 * Math.abs(this.timezone - activeTimeZone);
		} else {
			timeDifference = Math.abs(this.timezone - activeTimeZone);
		}
		if ((timeDifference + '').split('.').length == 2) {
			timeToPassDay = (timeDifference + '').split('.');
		} else {
			timeToPassDay = (timeDifference + '').split('.');
			timeToPassDay.push('0');
		}
		if (timeToPassDay[1] == '5') {
			if (timeDifference < 0) {
				timeToPassDay[1] = '-30';
			} else {
				timeToPassDay[1] = '30';
			}
		} else {
			timeToPassDay[1] = '0';
		}
		if (timeToPassDay[0] == '-0') {
			timeToPassDay[0] = '0';
		}

		return (timeToPassDay = timeToPassDay.map(function(time) {
			return parseInt(time, 10);
		}));
	}
	getOtherTenantRoles(roleId) {
		let headers = new Headers();
		headers.append('Content-Type', 'application/x-www-form-urlencoded');
		headers.append('Authentication-Token', this._structureService.getCookie('authID'));
		const apiURL =
			this._structureService.apiBaseUrl + 'citus-health/v4/get-roles-of-cross-tenant.php?roleId=' + roleId;
		var apiConfig = { url: apiURL, requestType: 'http' };
		return this._structureService.requestData(apiConfig);
	}
	getAllAppNames() {
		let variables: any = {
			sessionId: this._structureService.getCookie('authenticationToken'),
			crossTenantId: this._structureService.getCookie('crossTenantId')
		};
		const apiConfig = {
			method: 'GET',
			data: getAllAppNames,
			variables: variables,
			requestType: 'gql',
			use: 'signatureRequestApi',
			nested: false
		};
		return this._structureService.requestData(apiConfig);
	}
	getWorklistMenu() {
		this.getWorkLists().then((data) => {
			console.log(data);
			let workLists = [];
			if (data['getSessionTenant'] && data['getSessionTenant'].formWorklists) {
				workLists = JSON.parse(JSON.stringify(data['getSessionTenant'].formWorklists));
			}
			this._structureService.worklistMenu = [];
			let workListMenu = [];
			workLists.sort(function(a, b) {
				if (a.desktopMenu.menuIndex < b.desktopMenu.menuIndex) {
					return -1;
				}
				if (a.desktopMenu.menuIndex > b.desktopMenu.menuIndex) {
					return 1;
				}
				return 0;
			});

			workLists.forEach((element) => {
				if (element.description) {
					let newJson = element.description;
					newJson = newJson.replace(/'/g, '"');
					const metaData = JSON.parse(newJson);
					let actionLink = metaData.nameOfDesktopMenu.replace(/[^a-zA-Z ]/g, ' ').trim();
					actionLink = actionLink.replace(/\s+/g, '-').toLowerCase();
					const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
					if (
						((metaData.visibleToRoles &&
							metaData.visibleToRoles.split(',').indexOf(userDetails.roleId) !== -1) ||
							(metaData.allowCrossTenant == true &&
								metaData.visibleToOtherRoles &&
								metaData.visibleToOtherRoles.split(',').indexOf(userDetails.roleId) !== -1)) &&
						element.active &&
						metaData.showInMenu == true
					) {
						workListMenu.push({
							worklistId: element.id,
							workListName: element.name,
							menuGroupId: element.desktopMenu.menuGroup.id,
							menuTitle: element.desktopMenu.menuName,
							actionLink: `worklist/${actionLink}/${element.id}`,
							menuIcon: metaData.menuIcon
						});
					}
				}
			});
			this.getWorklistMenuGroup().then((group) => {
				let groups = JSON.parse(JSON.stringify(group['getSessionTenant'].formWorklistMenuGroup));
				groups.forEach((element) => {
					this._structureService.worklistMenu.push({
						name: element.name,
						worklists: workListMenu.filter((x) => x.menuGroupId == element.id)
					});
				});
			});
		});
	}

	getWorklistMessageTemplaeQuery() {
		let columnGroup = `query getSessionTenant(
      $sessionToken: String!`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			columnGroup =
				columnGroup +
				`,
        $crossTenantId : Int`;
		}
		columnGroup =
			columnGroup +
			`){
        getSessionTenant(sessionToken:$sessionToken`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			columnGroup =
				columnGroup +
				`,
        crossTenantId:$crossTenantId`;
		}
		columnGroup =
			columnGroup +
			`){
      formWorklistMessageTemplate{
        id
        name
        tenantId
        type
        content
        title
        subject
        status
        worklistId
        }
      }
    }`;
		return gql`${columnGroup}`;
	}
	getWorklistMessageTemplate() {
		let variables: any = {
			sessionToken: this._structureService.getCookie('authID')
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
			method: 'GET',
			data: this.getWorklistMessageTemplaeQuery(),
			variables: variables,
			requestType: 'gql',
			use: ''
		};
		const responce = this._structureService.requestData(apiConfig);
		return responce;
	}
	createWorklistMessageTemplateQuery() {
		let createTemplate = `mutation createWorklistMessageTemplate( $sessionToken:String!,$params : WorklistMessageTemplateInput`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			createTemplate =
				createTemplate +
				`,
        $crossTenantId : Int!`;
		}
		createTemplate = createTemplate + `){createWorklistMessageTemplate(sessionToken:$sessionToken params:$params`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			createTemplate = createTemplate + `,crossTenantId:$crossTenantId`;
		}
		createTemplate =
			createTemplate +
			`){
      id}
      }`;
		return gql`${createTemplate}`;
	}
	createWorklistMessageTemplate(params) {
		let variables: any = {
			sessionToken: this._structureService.getCookie('authID'),
			params: params
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
			method: 'POST',
			data: this.createWorklistMessageTemplateQuery(),
			variables: variables,
			requestType: 'gql',
			use: ''
		};
		const responce = this._structureService.requestData(apiConfig);
		return responce;
	}
	updateWorklistMessageTemplateQuery() {
		let updateTemplate = `mutation updateWorklistMessageTemplate( $sessionToken:String!,$params : WorklistMessageTemplateInput!,$id : Int!`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			updateTemplate =
				updateTemplate +
				`,
        $crossTenantId : Int!`;
		}
		updateTemplate =
			updateTemplate + `){updateWorklistMessageTemplate(sessionToken:$sessionToken params:$params id:$id`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			updateTemplate = updateTemplate + `,crossTenantId:$crossTenantId`;
		}
		updateTemplate =
			updateTemplate +
			`){
      id}
      }`;
		return gql`${updateTemplate}`;
	}
	updateWorklistMessageTemplate(params, id) {
		let variables: any = {
			sessionToken: this._structureService.getCookie('authID'),
			params: params,
			id: id
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
			method: 'POST',
			data: this.updateWorklistMessageTemplateQuery(),
			variables: variables,
			requestType: 'gql',
			use: ''
		};
		const responce = this._structureService.requestData(apiConfig);
		return responce;
	}
	deleteWorklistMessageTemplate(params, id) {
		const variables = {
			sessionToken: this._structureService.getCookie('authID'),
			id: id,
			deleted: 0,
			params: params
		};
		const apiConfig = {
			method: 'POST',
			data: deleteWorklistMsgTemplate,
			variables: variables,
			requestType: 'gql',
			use: ''
		};
		const responce = this._structureService.requestData(apiConfig);
		return responce;
	}
	getRoleBasedStaffs(roleId, status, type) {
		let variables: any = {
			sessionToken: this._structureService.getCookie('authenticationToken'),
			multiRoleId: roleId,
			status: status,
			type: type
		};
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		let apiConfig = {
			method: 'GET',
			data: this.getRoleBasedStaffsQuery(),
			variables: variables,
			requestType: 'gql',
			use: '',
			noLoader:true
		};
		//var responce = this.requestData_New(apiConfig);
		let responce = this._structureService.requestData(apiConfig);

		return responce;
	}
	getRoleBasedStaffsQuery() {
		let RoleBasedStaffs = `query getSessionTenant($sessionToken: String!, $multiRoleId: String, $status: Int!,$type:Int`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			RoleBasedStaffs =
				RoleBasedStaffs +
				`,
      $crossTenantId : Int!`;
		}

		RoleBasedStaffs = RoleBasedStaffs + `){getSessionTenant(sessionToken:$sessionToken`;
		if (
			this._structureService.getCookie('crossTenantId') &&
			this._structureService.getCookie('crossTenantId') !== 'undefined' &&
			this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
		) {
			RoleBasedStaffs = RoleBasedStaffs + `,crossTenantId:$crossTenantId`;
		}
		RoleBasedStaffs =
			RoleBasedStaffs +
			`){ 
	getMultipleRolesBasedStaffs(multiRoleId:$multiRoleId,status:$status,type:$type){
        id
        displayName
        mobile
        countryCode
        role{
          roleId
          roleName
        }
        firstName
        lastName
        emails {
          type
          value
          primary
        }
      }
    }
  }`;

		return gql`${RoleBasedStaffs}`;
	}
	
	getWorklistDataUsingAPI(url, params, graphqlQuery ='') {
		let headers = new Headers();
		headers.append('Content-Type', 'application/x-www-form-urlencoded');
		headers.append('Authentication-Token', this._structureService.getCookie('authID'));
		let options = new RequestOptions({ headers: headers }); 
		var apiConfig = { url: url, requestType: 'http', data: params, method: 'GET', headers: {}, replaceHeaders:false };
		if (graphqlQuery) {
		  apiConfig.replaceHeaders = true;
		  apiConfig.headers = { 'Content-Type': 'application/json'};
		  apiConfig.method = 'POST';
		  console.log(typeof graphqlQuery);
		  console.log(graphqlQuery);
		  apiConfig.data = JSON.parse(graphqlQuery);
		} 
		return this._structureService.requestData(apiConfig);
	  }
	  getWorklistDataUsingGraphQLAPI(url, query, variables, headers) {  
		let queryParams = gql`${query}`;
		const networkInterface = createNetworkInterface({
		  uri: url,
		});
		if(headers.hasOwnProperty('authorizationtoken') == false || headers.authorizationtoken == '') {
			headers.authorizationtoken = this._structureService.getCookie('authID');
		}
		networkInterface.use([{
			applyMiddleware(req, next)  {
				if (!req.options.headers) {
					req.options.headers = headers;
				}
				next();
			},
		}]);
		const client = new ApolloClient({
		  networkInterface
		});
		const subscription = client.query({
		  query: queryParams,
		  variables: variables, fetchPolicy: 'network-only'
		}).then((result) => {
			return result;
		}).catch((error) => {
			this._structureService.notifyMessage({
				messge: this.toolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG'),
				type: 'danger'
			});
			throw error;
		});
		return subscription;
	  }
	  saveWorklistStatePreferenceQuery(){
		let createPreference = `mutation manageUserPreference( $sessionToken:String!,$params : userPreferenceInput`;
		if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
		  createPreference = createPreference + `,
			$crossTenantId : Int!`;
		}
		createPreference = createPreference + `){manageUserPreference(sessionToken:$sessionToken params:$params`;
		if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
		  createPreference = createPreference + `,crossTenantId:$crossTenantId`;
		}
		createPreference = createPreference + `){
		  status
		  statusMessage
		  data {
			id
		  }}
		  }`;
		return gql`${createPreference}`;
	  }
	  saveWorklistStatePreference(state) {
		let variables: any = {
		  sessionToken: this._structureService.getCookie('authID'),
		  params: state
		};
		if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
		  variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
		  method: 'POST',
		  data: this.saveWorklistStatePreferenceQuery(),
		  variables: variables,
		  requestType: 'gql',
		  use: '',
		};
		const response = this._structureService.requestData(apiConfig);
		return response;
	  }
	  deleteWorklistStatePreferenceQuery(){
		let deletePreference = `mutation deleteUserPreference( $sessionToken:String!,$crossTenantId:Int,$params : deleteUserPreferenceInput`;
	   if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
		  deletePreference = deletePreference+ `,
			$crossTenantId : Int!`;
		}
		deletePreference = deletePreference + `){deleteUserPreference(sessionToken:$sessionToken, crossTenantId:$crossTenantId, params:$params`;
		if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
		  deletePreference= deletePreference+ `,crossTenantId:$crossTenantId`;
		}
		deletePreference = deletePreference + `){
		  status
		  statusMessage
		}
		  }`;
		  console.log(deletePreference); 
		return gql`${deletePreference}`;
	  }
	  deleteWorklistStatePreference(state) {
		let variables: any = {
		  sessionToken: this._structureService.getCookie('authID'),
		  params: state
		};
		if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
		  variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		console.log(variables);
		const apiConfig = {
		  method: 'POST',
		  data: this.deleteWorklistStatePreferenceQuery(),
		  variables: variables,
		  requestType: 'gql',
		  use: '',
		};
		const response = this._structureService.requestData(apiConfig);
		return response;
	  }
	  getWorklistStatePreferenceQuery() {
		  let stateQuery = `query getSessionTenant(
			$sessionToken: String!,$object_id: Int,$userId: Int,$tenantId: Int`;
		  if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
			stateQuery = stateQuery + `,
			  $crossTenantId : Int`;
		  }
		  stateQuery = stateQuery + `){
			  getSessionTenant(sessionToken:$sessionToken`;
		  if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
			stateQuery = stateQuery + `,
			  crossTenantId:$crossTenantId`;
		  }
		  stateQuery = stateQuery + `){
			getUserPeference(object_id: $object_id,userId: $userId,tenantId: $tenantId){
			  status
			  statusMessage
			  data{
				id
				meta
				object_id
				profile_key
			  }
			  }
			}
		  }`;
		  return gql`${stateQuery}`;
	  }
	  getWorklistStatePreference(variables) {
		variables.sessionToken = this._structureService.getCookie('authID');
		if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
		  variables.crossTenantId = Number(this._structureService.getCookie('crossTenantId'));
		}
		const apiConfig = {
		  method: 'GET',
		  data: this.getWorklistStatePreferenceQuery(),
		  variables: variables,
		  requestType: 'gql',
		  use: '',
		};
		const response = this._structureService.requestData(apiConfig);
		return response;
	  }
}
