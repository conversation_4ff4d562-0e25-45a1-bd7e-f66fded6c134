.cat__core__step__desc p {
    font-weight: bold !important;
}

.cat__core__step__digit {
    cursor: pointer;
}

.signature-req-block:hover {
    background: #e6e6e6;
}

.status-active {
    background-color: #e6e6e6;
}

.refill-dues {
    margin-bottom: 0 !important;
    margin-top: 8px;
}

.history-records {
    margin-top: 4px;
}

.batch-action-search {
    margin-top: 1px;
}
.new-modal {
    width: 390px;
    float: right;
    text-align: center;
    top: 175px;
    position: absolute;
    left: 110%;
    border: 1px solid #ccc;
}
.detail-modal-close {
    right: 0;
    position: absolute;
    padding: 5px;
    font-size: 1rem;
    z-index: 99;
}
.new-modal table {
    width: 100%;
}
.int-loader img {
    width: 50px;
    padding: 10px;
    margin: 0;
}
.my-tooltip {
    position: absolute;
    background: #f7f7f7;
    border: 1px solid #ccc;
    right: 22px;
    min-width: 200px;
    border-radius: 6px;
}
.my-tooltip:after, .my-tooltip:before {
    bottom: 100%;
    left: 70%;
    border: solid transparent;
    content: " ";
    height: 0;
    width: 0;
    position: absolute;
    pointer-events: none;
}

.my-tooltip:after {
    border-color: rgba(136, 183, 213, 0);
    border-bottom-color: #f7f7f7;
    border-width: 13px;
    margin-left: -13px;
}
.my-tooltip:before {
    border-color: rgba(194, 225, 245, 0);
    border-bottom-color: #ccc;
    border-width: 14px;
    margin-left: -14px;
}
.my-tooltip .content-block {
    padding: 15px 20px 15px 20px;
}
.intake-modal .modal-header{
    font-size: 16px;
    display:block;
    padding:0px;
}
.intake-modal .modal-title{
    display: inline-block;
    width: 90%;
}
.title-header{
    padding:15px;
}
/*.intake-modal .modal-body{
    background: #eceeef !important;
}*/    
.intake-modal h4{
    text-align: left;
    margin-top: 0;    
}
.intake-modal .modal-cls{
    text-align: left;
    margin-top: 10px;
    margin-bottom: 10px;
}
.intake-modal .modal-data{
    margin-top: 0;
    min-height: 35px;
    display:block;
    
}

.intake-label{
    width: 80%;
    display: inline-block;
    margin-left:10px;
}
.intakeouter{
    margin-bottom: 12px;
    padding-left:5%;
}
.intake-modal .btn{
    background-color: #0190fe;
    border-color: #0190fe;
    color: #fff;
}
.intake-modal .intake-checkbox{
    width: 20px; 
    height: 20px;
}
.intake-modal select{
    min-width: 20px; 
    height: 18px;
}
.checklist-block{
    margin-top:5px;
    display:block;
    overflow-y:auto;
    overflow-x:hidden;
    height:290px;
}
.checklist-header-block{
    display:block;    
}
.intake-label-header-first{
    margin-left: 10px !important;
    border-bottom: 2px solid #eceeef;    
}
.intake-label-header-item{
    margin-left: 0px;
    border-bottom: 2px solid #eceeef;    
}
.intake-label-header{
    margin-left: 0px;    
    border-bottom: 2px solid #eceeef;    
}
.checklist-patient-details  {
    border-bottom: 1px solid #eceeef;
    padding-left: 15px !important;
    background: #eceeef !important;
    padding-top: 10px;
    padding-bottom: 10px;
}

.checklist-item-list {
    
    margin-top: 2%;
    background: #ffffff !important;    
}

.recall-link-color {
    color:#ff3333 !important;
}
