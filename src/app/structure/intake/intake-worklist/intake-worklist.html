<!-- START: tables/datatables -->
<ejs-toast #element showCloseButton=true width=400 timeOut=0 [position]='position'>
    <ng-template #content>
        <div><i class="fa fa-exclamation-triangle"></i> This worklist has been updated. <a (click)="refreshGrid()"
                style="color:blue;text-decoration: underline;">Refresh</a></div>
    </ng-template>
</ejs-toast>
<!-- <ejs-toast #notificationMsg showCloseButton=true width=400 timeOut=0 [position]='position'>
    <ng-template #title>
            <div><i class="fa fa-exclamation-triangle"></i> Information</div>
    </ng-template>
    <ng-template #content>
        <div>Please wait..</div>
    </ng-template>
</ejs-toast> -->
<section class="card" style="margin-bottom: 0px !important;">
<section class="card" *ngIf="dataLoadingMsg">
    <div class="card-block mb-2 mt-2">
        <div class="wait-loading">
            <img src="assets/img/loader/loading.gif" />
        </div>
    </div>
</section>
<section class="card" *ngIf="!dataLoadingMsg" style="margin-bottom: 0px !important;">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>{{heading}}</strong>
            <a [hidden]="!addNewBtn" (click)="goToPage()" class="pull-right btn btn-sm btn-primary">{{addNewBtnText}}<i
                    class="ml-1"></i></a>
        </span>
        <!-- <span> 
            <button class="btn btn-sm float-sm-right"  style="background-color:#52b3aa;color:#ffffff;" (click)="refreshDashboard()">
                <i class="fa fa-refresh" aria-hidden="true"></i>&nbsp;Refresh
            </button>
        <button class="btn btn-sm float-sm-right"  style="background-color:#0190fe;color:#ffffff;" (click)="openInNewTab()()">
                <i class="fa fa-desktop" aria-hidden="true"></i>&nbsp;Open In New Tab
            </button>
         
             </span> -->
        </div>    
    <div class="card-block">  
        <!-- "[ '/' + urlPrefix +'/'+ initialRoute +'/hz-trace-results']" -->
        <div>
            <ol class="breadcrumb" style="margin-bottom: 10px;">
                <li class="breadcrumb-item" ><a (click)="goToHome()"> 
                    <!-- {{this._intakeService.breadcrumbAppName }} Home -->
                    {{this._intakeService.breadcrumbAppName == ' ' ? ' ' : 
                    this._intakeService.breadcrumbAppName + '-'}}Home
                    </a></li>
                    <!-- {{this._intakeService.breadcrumbAppName ? this._intakeService.breadcrumbAppName : 'Home'}}</a></li> -->
                <!-- <li class="breadcrumb-item"><a [routerLink]="['apps/' + guid]">Home-{{_intakeService.menuAppName}}</a></li> -->
                <!-- <li class="breadcrumb-item"><a [routerLink]="['apps/:guid']"> {{_intakeService.menuAppName}}</a></li> -->
                <li class="breadcrumb-item">{{heading}}</li>
                <li class="info-widget">
                    <!-- <span class="chatwith-modal-tip" style="position:absolute !important;"> -->
                         <span class="chatwith-modal-tip">
                        <img src="assets/modules/dummy-assets/common/img/chatwith-tip.png">
                        <span class="modal-footer-text-sign">{{helpMsg}}</span>
                    </span>
                </li>
            </ol>
            <div style="float:left;">

            </div>
        </div>
        <div class="row widgets-lists" style="padding:0px 10px;">
            <div class="{{widget.class}}  worklist-req-block"
                [class.widget-selected]="selectedWidget == widget.widgetValue" *ngFor="let widget of dashboardWidgets">
                <a href="javascript:void(0)"
                    (click)="widgetFiltering(widget.widgetValue, widget.formField, widget.hideColumns, widget.count)">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="cat__core__step cat__core no-border signed-block"
                                [class.widget-selected]="selectedWidget == widget.widgetValue" background-color:
                                #ececec;>
                                <div class="cat__core__step__desc">
                                    <span class="cat__core__step__digit" style="margin-right:0px !important;">
                                        <i class="{{widget.displayIcon}}"
                                            [ngStyle]="{'color': widget.iconColor}"></i></span>
                                    <span class="cat__core__step__title">{{widget.count}} <i
                                            class="fa fa-refresh fa-spin" [hidden]="widget.count !== null"
                                            style="font-size: 16px;"></i> </span>
                                    <label style="font-weight: bold;float: right;padding-right: 32px;cursor:pointer;">{{widget.displayText}}</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </a>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-12">
                    <div class="row">
                        <div class="col-sm-12 col-md-5">
                            <div class="due-action" *ngIf="dynamicFilter == true">
                                <span><input type="radio" class="mr-2 refill-dues" name="refill" value="due"
                                        (click)="setRefillValue('due',selectedDays,dynamicFilterField)"
                                        [(ngModel)]="refillDateType" [checked]="refillType">{{dynamicFilterLabel}}
                                    Due</span>
                                <span><input type="radio" class="mr-2 ml-2 refill-dues" name="refill" value="past"
                                        (click)="setRefillValue('past',selectedDays,dynamicFilterField)"
                                        [(ngModel)]="refillDateType" [checked]="!refillType"> Past
                                    {{dynamicFilterLabel}} Due</span>
                                <span><select class="form-control refill-due ml-2 mr-2" [(ngModel)]="selectedDays"
                                        (change)="filteringDate(selectedDays,dynamicFilterField)">
                                        <option value="" hidden>Select</option>
                                        <option value="7">7 days</option>
                                        <option value="14">14 days</option>
                                    </select></span>
                                <span>
                                    <button class="btn btn-default reset-btn"
                                        (click)="filteringDate('',dynamicFilterField)">
                                        <!-- <i class="fa fa-close" ></i> -->
                                        Reset
                                    </button>
                                </span>
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-7">
                            <!-- <label *ngIf="formId == 79842" class="mr-2 refill-dues" style="float:left"><strong>Refills Due in next</strong></label> -->
                            <div id="btn-action-patient" class="btn-group pull-right mb-2" [hidden]="!showCustomExport">
                                <button aria-expanded="false" [disabled]="disableWidget"
                                    class="btn btn-sm btn-default dropdown-toggle action-btn-user-settings-patient"
                                    data-toggle="dropdown" type="button"><i class="fa fa-file-excel-o"></i>
                                    Export</button>
                                <ul class="dropdown-menu">
                                    <a class="dropdown-item " href="javascript: void(0);" (click)="exportCSV()">Export
                                        CSV</a>
                                    <a class="dropdown-item" href="javascript: void(0);"
                                        (click)="exportExcel('xlsx')">Export Excel(.xlsx)</a>
                                    <a class="dropdown-item" href="javascript: void(0);"
                                        (click)="exportExcel('xml')">Export Excel(.xml)</a>
                                </ul>
                            </div>
                            <div id="btn-action-patient" class="btn-group pull-right mb-2" [hidden]="!showStateBtn">
                                <div class="dropdown">
                                    <button class="btn btn-secondary btn-sm dropdown-toggle" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        Worklist Session State
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                                      <a class="dropdown-item" (click)="saveStatePrefrence()" href="javascript: void(0);">Save</a>
                                      <a class="dropdown-item" (click)="clearStatePrefrence()" href="javascript: void(0);">Reset</a>
                                    </ul>
                                </div>
                               <!-- <button class="btn btn-sm btn-default" (click)="saveStatePrefrence()">Save Worklist Session State</button> -->
                            </div>
                            <!-- <input type="button" class="btn btn-sm btn-primary" value="Refills Due Next 7 Days" (click)="filteringDate(7,'Refill Date')"/> -->
                            <div *ngIf="hasUniqueId" class="pull-right mr-3 history-records">
                                <label class="mr-3"><strong>Show</strong></label>
                                <input type="radio" class="mr-2" name="list" value="listall"
                                    (click)="showAllEntries('all',$event)" [checked]="showAll">History Records
                                <input type="radio" class="mr-2 ml-2" name="list" value="listall"
                                    (click)="showAllEntries('less',$event)" [checked]="showLess">Most Recent
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-2">
                            <label>Page Size</label>
                            <select (change)="changePaginationSize($event)" class="form-control page-list" [(ngModel)]="pageSize">
                                <option *ngFor="let size of pageList" [hidden]="size == ''" value="{{size}}">{{size}}</option>
                            </select>
                        </div>
                        <div class="col-md-4" style="height:30px;padding-top: 3px;">
                            <div [hidden]="hideActionButtons == true || singleRowActions.length === 0">
                                <label class="mr-2">Actions:</label>
                                <span *ngFor="let action of singleRowActions">
                                    <a class="mr-3 {{action.cssClass}}" title="{{action.toolTip}}"
                                        [ngClass]="{'disabled':action.disable}" *ngIf="action.type == 'single'"
                                        style="color:#0190fe; cursor: pointer;" (click)="singleBtnAction(action)">
                                        <i class="{{action.iconClass}} mr-1"></i>{{action.label}}</a>
                                    <!-- <select class="action form-control" data-placeholder="Actions" (change)="onClick($event,element.label)">
                                        <option value="" hidden>{{action.label}}</option>
                                        <option *ngFor="let item of action.itemElements" value="{{item.action}}">{{item.label}}</option>
                                    </select> -->
                                </span>
                                <a class="mr-3" style="color:#0190fe; cursor: pointer;" (click)="bulkEditAction()"
                                    [hidden]="bulkEditFields.length == 0"> <i class="fa fa-pencil mr-1"></i>Bulk Edit
                                </a>
                                <label class="mr-4 ml-2" *ngIf="shortLinkActions.length > 0">|</label>
                                <span *ngFor="let action of shortLinkActions">
                                    <a class="mr-3 {{action.cssClass}}" [ngClass]="{'disabled':action.disable}"
                                        *ngIf="action.type == 'single'" style="color:#0190fe; cursor: pointer;"
                                        (click)="singleBtnAction(action)">
                                        <i class="{{action.iconClass}} mr-1"></i>{{action.label}}</a>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6" *ngIf="metaData.enableQuickSearch == true" > 
                            <div class="pull-right batch-action-search" *ngIf="rowModelType == 'clientSide'">
                                        <label>Search:</label>
                                        <label>
                                            <input type="text" #search class="input-sm form-control" placeholder="Search.."
                                                (keyup.enter)="searchFilterData(search.value)" [(ngModel)]="searchFieldText"/>
                                        </label>
                                        <label>
                                            <button class="btn btn-primary btn-sm reset-btn" [disabled]="searchFieldText == ''"
                                                (click)="searchFilterData(search.value)">
                                                Search
                                            </button>
                                            <button class="btn btn-default btn-sm reset-btn" (click)="searchFilterData('')">
                                                Reset
                                            </button>
                                                <button class="btn btn-default btn-sm reset-btn" 
                                                (click)="advanceSearch()">
                                                <img src="./assets/img/filter.png" data-toggle="tooltip" data-placement="top" title="Advance Search" class="adv-search" style="background-color: #acb7bf;height: 18px;">
                                                </button>
                                            </label>
                            </div>
                            <div class="pull-right" *ngIf="rowModelType == 'serverSide'">
                                        <label *ngIf="filterEnabledFields.length > 0">Search:</label>
                                        <label *ngIf="filterEnabledFields.length > 0" class="button-group custom-search">
                                            <button type="button" class="btn btn-default-outline btn-sm dropdown-toggle"
                                                style="border-top-right-radius:0px;border-bottom-right-radius:0px;"
                                                data-toggle="dropdown"><span class="fa fa-search"></span> <span
                                                    class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu filter-worklist">
                                                <li class="" *ngFor="let field of filterEnabledFields;">
                                                    <span (change)="checkboxSelection(field)">
                                                        <label>
                                                            <input class="form-check-input" type="checkbox" id="checkbox{{field.fieldId}}"
                                                                (change)="onChangeSearchField($event, field)"  value="{{field.fieldId}}" [checked]="field.enableAutoSelect">&nbsp;{{field.headerName}}
                                                        </label>
                                                    </span>
                                                </li>
                                            </ul>
                                        </label>
                                        <label *ngIf="filterEnabledFields.length > 0">
                                            <input type="text"
                                                style="padding-bottom: .52rem;margin-left: -5px;border-bottom-left-radius: 0px;border-top-left-radius: 0px;border-color:#d8d8d8;"
                                                [(ngModel)]="searchFieldText" class="input-sm form-control" [disabled]="!customSearch" placeholder="Search"
                                                (keyup.enter)="searchBasedField(searchFieldText)" />
                                        </label>
                                        <label *ngIf="filterEnabledFields.length > 0">
                                            <button class="btn btn-primary btn-sm reset-btn" [disabled]="searchFieldText == '' || selectedSearchFields.length == 0"
                                                (click)="searchBasedField(searchFieldText)">
                                                Search
                                            </button>
                                            <button class="btn btn-default btn-sm reset-btn" (click)="clearSearch()" [disabled]="!customSearch">
                                                Reset
                                            </button>
                                        </label>
                                        <label *ngIf="metaData.enableAdvanceSearch">
                                            <button class="btn btn-default btn-sm reset-btn" 
                                            (click)="advanceSearch()" >
                                                <img src="./assets/img/filter.png" data-toggle="tooltip" data-placement="top" title="Advance Search" class="adv-search" style="background-color: #acb7bf;height: 18px;">
                                            </button>
                                        </label>
                            </div>
                        </div>
                    </div>
                    <ng-container >
                            <app-advance-search #advanceSearchComp (valueChange)='advanceSearchBasedField($event)' [hidden]="isShow"
                            [advanceSearchCallback]="metaData.advanceSearchCallback" [worklistMetaData]="metaData" >
                            </app-advance-search>
                    </ng-container>
                    <div class="row" id="grid-wrapper" [ngStyle]="style">
                        <ag-grid-angular style="width: 100%; min-height: 500px;" class="ag-theme-balham"
                            [rowData]="rowData" [animateRows]="true" [columnDefs]="columnDefs"
                            [getRowHeight]="getRowHeight" [defaultColDef]="defaultColDef" [enableFilter]="true"
                            [enableSorting]="true" [rowStyle]="rowBorder" [frameworkComponents]="frameworkComponents"
                            (gridReady)="onGridReady($event)" [enableColResize]="true"
                            [overlayLoadingTemplate]="overlayLoadingTemplate"
                            [overlayNoRowsTemplate]="overlayNoRowsTemplate" (columnResized)="onColumnResized($event)"
                            (sortChanged)="sortChanged($event)" [icons]="icons" [enableRangeSelection]="true"
                            [rowSelection]='rowSelection' [autoGroupColumnDef]="autoGroupColumnDef"
                            (cellValueChanged)="onCellValueChanged($event)"
                            [groupRowInnerRenderer]="groupRowInnerRenderer" [components]="components"
                            [sideBar]="sideBar" [groupSelectsChildren]="groupSelectsChildren"
                            [suppressRowClickSelection]="true" [rowGroupPanelShow]="rowGroupPanelShow"
                            [detailCellRendererParams]="detailCellRendererParams" [masterDetail]="true"
                            (gridSizeChanged)="onGridSizeChanged($event)" (cellClicked)="onCellClicked($event)"
                            (selectionChanged)="onSelectionChanged($event)" [rowModelType]="rowModelType"
                            [cacheBlockSize]="cacheBlockSize" [maxBlocksInCache]="maxBlocksInCache"
                            [paginationPageSize]="paginationPageSize" [pagination]="pagination"
                            [isRowMaster]="isRowMaster" [suppressCsvExport]="suppressCsvExport"
                            [suppressExcelExport]="suppressExcelExport" [excelStyles]="excelStyles" [animateRows]="animateRows"
                            [enableCellChangeFlash]="cellChangeFlash" (bodyScroll)="onBodyScrollEvent($event)"
                            (dragStopped)="onColumnDragStopped($event)" (columnPinned)="onColumnStateChanged($event)"
                            (columnVisible)="onColumnStateChanged($event)" (columnPivotChanged)="onColumnStateChanged($event)"
                            (filterChanged)="onFilterChanged($event)">
                        </ag-grid-angular>
                        <div class="my-tooltip" style="display:none;">
                            <div class="button-block">
                                <button type="button" class="close detail-modal-close" (click)="closeDetailpop()" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="content-block">
                                <table style="text-align: center;" class="loader-table" *ngIf=!showResponse width="100%">
                                    <tbody>
                                        <tr>
                                            <td>
                                                <div class="int-loader">
                                                    <img src="./assets/modules/dummy-assets/common/img/spinner.svg" alt="loading">
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                                <table style="text-align: left;" *ngIf='showResponse' class="response-table">
                                    <tbody>
                                        <tr *ngIf='responseStatus?.integration_status'>
                                            <td>Status: </td>
                                            <td>{{responseStatus?.integration_status}}</td>       
                                        </tr>
                                        <tr *ngIf='responseStatus?.reference_id'>
                                            <td>Reference Id: </td>
                                            <td *ngIf='responseStatus'>{{responseStatus?.reference_id}}</td>           
                                        </tr>
                                        <tr *ngIf='responseStatus?.processedAt'>
                                            <td>Processed At: </td>
                                            <td *ngIf='responseStatus'>{{responseStatus?.processedAt}}</td>              
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <!-- <div [innerHTML]="structureFormContent"></div> -->
            </div>
        </div>
        <div class="modal" id="note-modal" data-backdrop="static" tabindex="-1" role="dialog"
  aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-bg">
      <div class="modal-content">
          <div class="modal-header">
              <h4 class="modal-title" id="exampleModalLabel">{{worklistMenuName}} for Patient</h4>
              <button title="Close" type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="closeBlogNoteModal()">
                  <span aria-hidden="true">&times;</span>
              </button>
          </div>
          <div class="modal-body"> 
            <app-notes-modal noteType="Blog" [dynamicData]="dynamicData" 
            [worklistEditData]="worklistEditData"
            [integrationSettings]="integrationSettings"
            [patientId]="patientId"
            [workflowId]="workflowId"
            [noteCatId]="noteCatId"
            [worklistMenuName]='worklistMenuName'
            fromDashboard="true"
            [parentWorklistName]='parentWorklistName' (closeModal)='closeBlogNoteModal()' (updateBlogList)='updateBlogList()' *ngIf="showModalComponent">
            </app-notes-modal>
            <app-dynamic
                [workflowId]='workflowId'
                [workflowType] = 'workflowType'
                [worklistEditData]='worklistEditData'
                [dynamicData]="dynamicData"
                fromDashboard="true" *ngIf="showModalComponent">
                </app-dynamic>
        </div>
        </div>
      </div>
    </div>
        <div class="modal fade data-modal intake-modal" 
        data-backdrop="static" data-keyboard="false"
        id="cellClickModel" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-md">
          <div class="modal-content">
            <div class="modal-header">
                <div class="container title-header">
                    <h4 class="modal-title" id="exampleModalLabel"> {{checklistTitle}}</h4>                    
                    <button style="float:right;" title="Close" type="button" class="close" (click)="closeDynamicModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="checklist-patient-details container">                
                    <div class="modal-cls">
                        <div class="row" style="width: 100%">
                            <div class="col-sm-3">
                            <div>MRN: {{cellClickDetails?.patientId}}</div>
                            </div>
                            <div class="col-sm-9">
                            <div>Patient Name: {{cellClickDetails?.name}}</div>
                            </div>
                        </div>
                    </div>                
                </div>
              
            </div>
            <div class="modal-body">            
            <div *ngIf="showLoader" class="loading-container">
                <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                <div class="loading-text">Loading Data...</div>
            </div>
            <div *ngIf="!showLoader" class="checklist-item-list" style="width: 100%">
                <div class="checklist-header-block" *ngIf= "papperWorkList && papperWorkList.entryType=='both'">
                <!-- *ngIf= "papperWorkList.entryType=='both'"> -->
                    <div class="row" >
                    <div class="col-sm-6" >
                        
                                <span class="intake-label-header-first intake-label-header-item"  >{{checklistItemTitle}}</span>
                       
                                                                     
                    </div> 
                     <div class="col-sm-2" >
                        <!-- <div class="row" > -->
                            <span class="intake-label-header"  >{{checklistRequiredTitle}}</span>                            
                        <!-- </div>                         -->
                    </div> 
                    <div class="col-sm-2">
                        <!-- <div class="row" > -->
                                <span class="intake-label-header"  >{{checklistCompletedTitle}}</span>
                        </div>
                                               
                    </div>
                </div>  
                <div class="checklist-header-block"  *ngIf= "papperWorkList && papperWorkList.entryType =='required'">                
                    <div class="col-sm-4">
                        <div class="row" >
                                <span class="intake-label-header-first intake-label-header"  >{{checklistRequiredTitle}}</span>
                          
                        </div>                       
                    </div>                    
                    <div class="col-sm-6" >
                        <div class="row" >
                                <span class="intake-label-header-item"  >{{checklistItemTitle}}</span>
                            </div>
                                          
                    </div> 
                </div>
                <div class="checklist-header-block" *ngIf= "papperWorkList && papperWorkList.
                entryType == 'completed' && !checklistShowMultipleEntry">
                    <div class="row">
                        <div class="col-sm-4" >
                            <span class="intake-label-header-first intake-label-header"  >{{checklistCompletedTitle}}</span>
                            
                        </div>
                        
                    <!-- </div>
                    <div class="row"> -->
                        <div class="col-sm-6" >
                            <span class="intake-label-header-item"  >{{checklistItemTitle}}</span>                            
                        </div>                       
                        
                    </div>
                </div>
                 <div class="checklist-header-block" *ngIf= "papperWorkList && papperWorkList.
                entryType == 'completed' && checklistShowMultipleEntry">                    
                    <div class="row">
                        <div class="col-sm-4" >
                            <span class="intake-label-header-item"  >{{checklistItemTitle}}</span>                            
                        </div>                       
                        
                    
                    <div class="col-sm-4">
                        
                            <span class="intake-label-header-first intake-label-header"  >Intake</span>
                            
                        
                        
                    </div>
                    <div class="col-sm-4">
                        
                            <span class="intake-label-header-first intake-label-header"  >Insurance Verification</span>
                            
                        </div>
                        
                    </div>
                </div>
                <div class="checklist-block" *ngIf= "papperWorkList && papperWorkList.entryType=='both'">
                    <!-- <div class="col-sm-6" > -->
                        <div class="modal-data" *ngFor="let checklistItem of papperWorkList.completedItems; let i = index">
                            <div class="row" >
                                <div class="col-sm-6" >
                                    <span class="intake-label"  >{{checklistItem.itemName}}</span>
                                </div>
                                <div class="col-sm-2" *ngIf= "papperWorkList.requiredItems[i].controlType == 'CHECKBOX'">
                                    <input class="intake-label intake-checkbox" disabled="true" type="checkbox" [checked]="papperWorkList.requiredItems[i].itemValue == 1" name="{{ papperWorkList.requiredItems[i].itemName }}" value="{{ papperWorkList.requiredItems[i].itemName }}" > 
                                </div>
                                <div class="col-sm-2" *ngIf= "papperWorkList.requiredItems[i].controlType == 'SELECTBOX'">
                                    <span class="intake-label" *ngIf="papperWorkList.requiredItems[i].linkedItemValueId === 0">
                                        No Item Selected    
                                    </span>
                                    <span *ngIf="papperWorkList.requiredItems[i].linkedItemValueId !== 0">
                                        <span class="intake-label" *ngFor="let item of papperWorkList.requiredItems[i].linkedItems">
                                            <span *ngIf="papperWorkList.requiredItems[i].linkedItemValueId === item.id">
                                                {{item.itemValue}}
                                            </span>                                    
                                        </span>
                                    </span>                                    
                                </div>
                                <div class="col-sm-2" *ngIf= "checklistItem.controlType == 'CHECKBOX'">
                                    <input class="intake-label intake-checkbox" disabled="true" type="checkbox" [checked]="checklistItem.itemValue == 1" name="{{ checklistItem.itemName }}" value="{{ checklistItem.itemName }}" > 
                                </div>
                                <div class="col-sm-2" *ngIf= "checklistItem.controlType == 'SELECTBOX'">                                                                                       
                                        <span class="intake-label" *ngIf="checklistItem.linkedItemValueId === 0">
                                            No Item Selected
                                        </span>
                                        <span *ngIf="checklistItem.linkedItemValueId !== 0">
                                            <span *ngFor="let item of checklistItem.linkedItems">
                                                <span class="intake-label" *ngIf="checklistItem.linkedItemValueId === item.id">
                                                    {{item.itemValue}}
                                                </span>                                    
                                            </span>
                                        </span>
                                </div>
                            </div>
                        </div>
                </div>  
                <div class="checklist-block"  *ngIf= "papperWorkList && papperWorkList.entryType =='required'">                
                    <div class="modal-data" *ngFor="let checklistItem of papperWorkList.requiredItems; 
                        let i = index">
                            <div class="row">
                                <div class="col-sm-4">
                                    <div  *ngIf= "checklistItem.controlType == 'CHECKBOX'">
                                        <input class="intake-label intake-checkbox" disabled="true" type="checkbox" [checked]="checklistItem.itemValue == 1" name="{{ checklistItem.itemName }}" value="{{ checklistItem.itemName }}" > 
                                    
                                    </div>
                                    <div  *ngIf= "checklistItem.controlType == 'SELECTBOX'">
                                            <!-- <select class="form-control" [(ngModel)]="checklistItem.linkedItemValueId">
                                                                
                                                <option *ngFor="let item of checklistItem.linkedItems" 
                                                value="{{item.id}}">
                                                            {{item.itemValue}}</option>
                                            </select> -->
                                            <span class="intake-label" *ngIf="checklistItem.linkedItemValueId === 0">
                                                No Item Selected
                                            </span>
                                            <span *ngIf="checklistItem.linkedItemValueId !== 0">
                                                <span *ngFor="let item of checklistItem.linkedItems">
                                                    <span class="intake-label" *ngIf="checklistItem.linkedItemValueId === item.id">
                                                        {{item.itemValue}}
                                                    </span>                                    
                                                </span>
                                            </span>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <span class="intake-label"  >{{checklistItem.itemName}}</span>
                                </div>
                            </div>
                        </div>
                </div> 
                <div class="checklist-block" *ngIf= "papperWorkList && papperWorkList.entryType == 'completed' && !checklistShowMultipleEntry">
                    <!-- <div class="col-sm-4"> -->
                        <div class="modal-data" *ngFor="let checklistItem of papperWorkList.completedItems; 
                        let i = index">
                            <div class="row">
                                <div class="col-sm-4">
                                    <div  *ngIf= "checklistItem.controlType == 'CHECKBOX'">
                                        <input class="intake-label intake-checkbox" disabled="tr   ue" type="checkbox" [checked]="checklistItem.itemValue == 1" name="{{ checklistItem.itemName }}" value="{{ checklistItem.itemName }}" > 
                                    
                                    </div>
                                    <div  *ngIf= "checklistItem.controlType == 'SELECTBOX'">
                                            <!-- <select class="form-control" [(ngModel)]="checklistItem.linkedItemValueId">
                                                                
                                                <option *ngFor="let item of checklistItem.linkedItems" 
                                                value="{{item.id}}">
                                                            {{item.itemValue}}</option>
                                            </select> -->
                                            <span class="intake-label" *ngIf="checklistItem.linkedItemValueId === 0">
                                                No Item Selected
                                            </span>
                                            <span *ngIf="checklistItem.linkedItemValueId !== 0">
                                                <span *ngFor="let item of checklistItem.linkedItems">
                                                    <span class="intake-label" *ngIf="checklistItem.linkedItemValueId == item.id">
                                                        {{item.itemValue}}
                                                    </span>                                    
                                                </span>
                                            </span>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <span class="intake-label"  [ngStyle]="{'background-color': checklistItem.backgroundColor}">{{checklistItem.itemName}}</span>
                                </div>
                            </div>
                        </div>
                    <!-- </div>
                    <div class="col-sm-6"> -->
                        <!-- <div class="modal-data" *ngFor="let checklistItem of papperWorkList.completedItems; let i = index">
                            <div class="row" >
                                <span class="intake-label"  >{{checklistItem.itemName}}</span>
                            </div>
                        </div> -->
                        
                    <!-- </div> -->
                    
                </div>
                 <div class="checklist-block" *ngIf= "papperWorkList && papperWorkList.entryType == 'completed' && checklistShowMultipleEntry">
                    <!-- <div class="col-sm-4"> -->
                        <div class="modal-data" *ngFor="let checklistItem of papperWorkList.completedItems; 
                        let i = index">
                            <div class="row mb-2 mt-2">
                                
                                <div class="col-sm-4" [ngStyle]="{'background-color': checklistItem.backgroundColor}">
                                    <span class="intake-label"  >{{checklistItem.itemName}}</span>
                                </div>
                                <div class="col-sm-4">
                                    <div  *ngIf= "checklistItem.controlType == 'CHECKBOX'">
                                        <input class="intake-label intake-checkbox" disabled="tr   ue" type="checkbox" [checked]="checklistItem.intakePatientEntry == 1" name="{{ checklistItem.itemName }}" value="{{ checklistItem.itemName }}" > 
                                    
                                    </div>
                                    <div  *ngIf= "checklistItem.controlType == 'SELECTBOX'">
                                           <span class="intake-label" *ngIf="checklistItem.intakePatientEntry === ''">
                                                No Item Selected
                                            </span>
                                            <span *ngIf="checklistItem.intakePatientEntry !== ''">
                                                <span *ngFor="let item of checklistItem.linkedItems">
                                                    <span class="intake-label" *ngIf="checklistItem.intakePatientEntry == item.id">
                                                        {{item.itemValue}}
                                                    </span>                                    
                                                </span>                                               
                                            </span>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div>
                                        <input class="intake-label intake-checkbox" disabled="tr   ue" type="checkbox" [checked]="checklistItem.insuranceFollowupEntry == 1" name="{{ checklistItem.itemName }}" value="{{ checklistItem.itemName }}" > 
                                    
                                    </div>
                                    <!--<div  *ngIf= "checklistItem.controlType == 'SELECTBOX'">
                                           <span class="intake-label" *ngIf="checklistItem.insuranceFollowupEntry=== ''">
                                                No Item Selected
                                            </span>
                                             <span *ngIf="checklistItem.insuranceFollowupEntry !== ''">
                                                <span *ngFor="let item of checklistItem.linkedItems">
                                                    <span class="intake-label" *ngIf="checklistItem.insuranceFollowupEntry == item.id">
                                                        {{item.itemValue}}
                                                    </span>                                    
                                                </span>                                               
                                            </span>
                                    </div>-->
                                </div>
                            </div>
                        </div>
                    
                   
                    
                </div>
            </div>
            </div>
            <div class="modal-footer">
              <button type="button" [ngClass]="{'btn-default':isApproveShow,'btn-primary':!isApproveShow}" 
              class="btn btn-secondary" (click)="closeDynamicModal()">Close</button>
              <!-- <button type="button" class="btn btn-primary" (click)="addNote()">Save Note</button> -->
            </div>
          </div>
        </div>
        </div>
        <div class="modal fade assign-modal" id="assign-modal" tabindex="-1" role="dialog"
            aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="exampleModalLabel">{{modalHeading}}</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="card-block">
                                <div class="cat__core__card-sidebar">
                                    <div class="cat__apps__messaging__header">
                                        <input
                                            class="form-control cat__apps__messaging__header__input ng-untouched ng-pristine ng-valid"
                                            id="staff-box" placeholder="Search Here"
                                            [ngModelOptions]="{standalone: true}" #userSearch [(ngModel)]="searchTexts">
                                        <i class="icmn-search"></i>
                                        <button></button>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12" style="background: #efeeee;padding: 1%;"
                                    [hidden]="modalType == 'generalStaff'">
                                    <span class="mb-2 ml-2"><strong>Language:</strong> {{staffLanguage}} </span><span
                                        *ngIf="staffLanguage == ''">No matching results.</span>
                                </div>
                                <div class="col-md-12">
                                    <div>
                                        <div *ngIf="staffLoadingMsg" class="mt-4">
                                            <div class="wait-loading">
                                                <img src="assets/img/loader/loading.gif" />
                                            </div>
                                        </div>
                                        <!--
                                        <div class="forwarding-behaviour-container" *ngIf="!staffLoadingMsg">
                                            <div class="forward-model-option-user-list"
                                                *ngFor="let staff of staffList | StaffSearchFilter: userSearch.value">
                                                <div class="forward-user-role"
                                                    *ngIf="staff.staffLanguage != '' && modalType == 'staff'"
                                                    (click)="assignesPatientStaff(staff.displayName,staff.id)">
                                                    {{staff.displayName}} ({{staff.staffLanguage}})
                                                </div>
                                                <div class="forward-user-role"
                                                    *ngIf="staff.staffLanguage == '' || modalType == 'generalStaff'"
                                                    (click)="assignesPatientStaff(staff.displayName,staff.id)">
                                                    {{staff.displayName}}
                                                </div>
                                            </div>
                                            <div *ngIf="staffList.length == 0" style="text-align: center;">
                                                No staffs available.
                                            </div>
                                            <div *ngIf="showAllMsg" class="mt-3" style="text-align: center;">
                                                <button class="btn btn-primary btn-sm" (click)="showAllStaffs()">Show
                                                    All</button>
                                            </div>
                                        </div>-->
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade bulk-edit" id="bulk-edit" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
            aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="exampleModalLabel">Bulk Edit</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="card-block">
                                <div class="cat__core__card-sidebar">
                                    <div class="">
                                        <label><strong>Property to update</strong></label>
                                        <select class="form-control" (change)="showFormElement($event)"
                                            id="update-field" [ngModelOptions]="{standalone: true}"
                                            [(ngModel)]="updateProperty">
                                            <option value="">Select</option>
                                            <option *ngFor="let action of bulkEditFields" value="{{action.fieldId}}">
                                                {{action.fieldName}}</option>
                                        </select>
                                    </div>
                                    <div class="">
                                        <app-dynamic-form [fields]="dynamicFields" (reload)="getFormDataBasedRowModel()"
                                            [updateData]="updateData" [fieldValueList]="fieldValueList"
                                            *ngIf="enableBulkEdit"></app-dynamic-form>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade edocs-modal" id="edocs-modal" tabindex="-1" role="dialog"
            aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="exampleModalLabel">Edocs</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body" [innerHtml]="fileContent">
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade date-modal" id="date-modal" tabindex="-1" role="dialog"
            aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-sm">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="exampleModalLabel">Schedule dates</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="">
                                <my-date-picker name="mydate" [options]="myDatePickerOptions" [(ngModel)]="dateModel"
                                    [selDate]="selectedDate" required></my-date-picker>
                                <span *ngIf="showDateError" class="mt-1" style="color:#ec0505;">Please select a
                                    date</span>
                                <br>
                            </div>

                            <div class="">
                                <button (click)="goToVisitPlanPage()" class="btn btn-primary btn-sm">Submit</button>
                                <button data-dismiss="modal" class="btn btn-default btn-sm">Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade therapy-modal" id="therapy-modal" tabindex="-1" role="dialog"
            aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="exampleModalLabel">Therapy</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body" style="border: solid 1px #dad8d8;">
                        <span *ngIf="loadMsg == true"><img src="./assets/img/loader/loading.gif" /></span>
                        <div class="row p-2" [hidden]="therapyDetails.length > 0 || showField">
                            <button class="btn btn-outline btn-sm mr-2" (click)="showField = true; createControls();"><i
                                    class="fa fa-plus"></i></button>
                            <label class="pt-1">Add Therapy</label>
                        </div>
                        <div class="row p-2" style="border-bottom: solid 1px #dad8d8;"
                            [hidden]="therapyDetails.length == 0">
                            <div class="col-md-3">
                                <h5 style="color: #0a81e8;">Therapy</h5>
                            </div>
                            <div class="col-md-3">
                                <h5 style="color: #0a81e8;">MOA</h5>
                            </div>
                            <div class="col-md-3">
                                <h5 style="color: #0a81e8;">Dosage</h5>
                            </div>
                            <div class="col-md-3">
                            </div>
                        </div>
                        <div class="row p-2" [hidden]="therapyDetails.length == 0"
                            *ngFor="let detail of therapyDetails;let i = index;">
                            <div class="col-md-3" [hidden]="field.visibility == false"
                                *ngFor="let field of editorDynamicFields">
                                {{detail[field.name]}}
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline btn-sm" (click)="editTherapy(detail.id);"><i
                                        class="fa fa-pencil"></i></button>
                                <button class="btn btn-outline btn-sm" (click)="deleteTherapy(detail.id)"><i
                                        class="icmn-bin"></i></button>
                                <button class="btn btn-outline btn-sm" *ngIf="(i+1) == therapyDetails.length"
                                    (click)="showField = true;"><i class="fa fa-plus"></i></button>
                            </div>
                        </div>
                        <div class="row p-2" [hidden]="!showField">
                            <!-- <app-editor-dynamic-form *ngIf="showField"  [fields]="editorDynamicFields" (reload)="getformData()" ></app-editor-dynamic-form> -->
                            <form class="row" [formGroup]="therapyForm" #th="ngForm" style="width:100%;">
                                <div class="col-md-3" [hidden]="field.visibility == false"
                                    *ngFor="let field of editorDynamicFields">
                                    <select formControlName="{{field.name}}" class="form-control"
                                        *ngIf="field.elementType == 'select'">
                                        <option value="">Select {{field.name}}</option>
                                        <option *ngFor="let opt of field.options" value="{{opt}}"
                                            [selected]="opt==field.value">{{opt}}</option>
                                    </select>
                                    <input type="text" *ngIf="field.elementType == 'textbox'" value="{{field.value}}"
                                        placeholder="Drug,Dose & Freq." formControlName="{{field.name}}"
                                        class="form-control" />
                                </div>
                                <div class="col-md-3">
                                    <button class="btn btn-primary btn-sm mt-1"
                                        (click)="addTherapy(th)">{{buttonLabel}}</button>
                                    <button class="btn btn-primary btn-sm mt-1"
                                        (click)="showField = false;cancelTherapy();">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade assign-patient-modal" id="assign-patient-modal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="exampleModalLabel">{{modalHeading}}</h4>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="row">
                                <div class="col-md-12 row">
                                    <div class="col-md-8">
                                        <div class="associate-search">
                                            <input type="text" class="form-control associate-search-input" id="associate-search-input" autocomplete="off" placeholder="Search Associated Patient..." (click)="openAssociateList()" (keyup)="clearSearchField()" (keyup.enter)="checkAssociatePatientWithTems()"
                                                [ngModelOptions]="{standalone: true}" [(ngModel)]="selectedPatientName" />
                                            <div class="asscoiate-actions searchBtn">
                                                <button [disabled]="associatePatientLoading" id="associate-search" (click)="checkAssociatePatientWithTems()" class="associate-search-button btn btn-sm btn-primary">Search</button>
                                                <button [disabled]="associatePatientLoading" class="associate-close" id="associate-close" (click)="closeSelectedAssociatePatient(true)" class="associate-search-button btn btn-sm btn-default">Reset</button>
                                            </div>
                                            <ul class="associate-ul" id="associate-ul">
                                                <li id="associate-li" class="associate-li selected" *ngIf="selectedAssosiatePatientName!=''" (click)="enableOrDisableUiLI(false, false)">{{selectedAssosiatePatientName}}</li>
                                                <li id="associate-li" class="associate-li" *ngIf="assosiatedPatients.length==0 && selectedAssosiatePatientName == ''">No item found</li>
                                                <li id="associate-li" class="associate-li" [ngClass]="{'selected': (selectedAssosiatePatient==assosiatedPatient.userId)}" *ngFor="let assosiatedPatient of assosiatedPatients" [hidden]="selectedAssosiatePatient==assosiatedPatient.userId" (click)="populateBasicPatient(assosiatedPatient.userId,assosiatedPatient.listDisplayName)">
                                                    {{assosiatedPatient.listDisplayName}}

                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
</section>
<!-- END: tables/datatables -->
