import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { DynamicBase } from './dynamic-base';
import { DynamicControlService } from './dynamic-control.service';
import { TextboxQuestion } from './dynamic-textbox';
import { IntakeWorkListService } from '../intake-worklist.service';
import { StructureService } from 'app/structure/structure.service';
declare var $: any;
@Component({
	selector: 'app-dynamic-form',
	templateUrl: './dynamic-form.component.html',
	providers: [ DynamicControlService ]
})
export class DynamicFormComponent implements OnInit {
	@Input() fields: DynamicBase<any>[] = [];
	@Output() reload = new EventEmitter<string>();

	form: FormGroup;
	payLoad = '';
	@Input() updateData;
	@Input() fieldValueList;
	@Input() hasOtherOption;

	constructor(
		private qcs: DynamicControlService,
		private _workListService: IntakeWorkListService,
		private _structureService: StructureService
	) {}

	ngOnInit() {
		let filterField: DynamicBase<any>[] = [];
		filterField = [];
		filterField = this.fields;
		filterField.forEach((field) => {
			if (field.fieldName != this.updateData[0].elementType) {
				field.show = false;
			} else {
				field.show = true;
			}
		});
		this.form = this.qcs.toFormGroup(filterField);
		//this.form.addControl('Others', new FormControl());
		this.form.addControl('othersc', new FormControl());
	}
	onSubmit() {
		this.payLoad = JSON.stringify(this.form.value);
		let updatedValue = [];
		let params = [];
		this.fields.forEach((field) => {
			if (field.show) {
				if (field.controlType == 'checkbox') {
					if (field.key == 'Others') {
						if (this.form.value[field.key]) {
							params.push({
								id: this.updateData[0].elementId + '_other',
								value: this.form.value['othersc']
							});
							updatedValue.push(this.form.value['othersc']);
						} else {
							if (this.updateData[0].hasOtherOption) {
								params.push({ id: this.updateData[0].elementId + '_other', value: '' });
							}
						}
					}
					const index = this.fieldValueList[field.fieldName].findIndex((x) => x.optionValue == field.label);
					if (index != -1) {
						if (this.form.value[field.key]) {
							params.push({
								id:
									this.updateData[0].elementId +
									'_' +
									this.fieldValueList[field.fieldName][index].optionId,
								value: 1
							});
							updatedValue.push(field.key);
						} else {
							params.push({
								id:
									this.updateData[0].elementId +
									'_' +
									this.fieldValueList[field.fieldName][index].optionId,
								value: 0
							});
						}
					}
				} else if (field.controlType == 'radio') {
					if (this.form.value[this.updateData[0].elementType] == 'Others') {
						params.push({ id: this.updateData[0].elementId + '_other', value: this.form.value['othersc'] });
						params.push({ id: this.updateData[0].elementId, value: 0 });
						updatedValue.push(this.form.value['othersc']);
					} else {
						if (this.updateData[0].hasOtherOption) {
							params.push({ id: this.updateData[0].elementId + '_other', value: '' });
						}
						let index = field['options'].findIndex((x) => x.optionId == this.form.value[field.key]);
						updatedValue.push(field['options'][index].optionValue);
						params.push({ id: this.updateData[0].elementId, value: this.form.value[field.key] });
					}
				} else if (field.controlType == 'textbox') {
					params.push({ id: this.updateData[0].elementId, value: this.form.value[field.key] });
				} else {
					params.push({ id: this.updateData[0].elementId, value: this.form.value[field.key] });
					let index = field['options'].findIndex((x) => x.optionId == this.form.value[field.key]);
					updatedValue.push(field['options'][index].optionValue);
				}
			}
		});
		// const params = [{ id: this.updateData[0].elementId, value: checkValues }];
		const count = this.updateData[0].entryId.length;
		let i = 1;
		this.updateData[0].entryId.forEach((element) => {
			this._workListService
				.updateSingleFormData(params, this.updateData[0].formId, element)
				.subscribe(({ data: response }) => {
					if (count == i) {
						$('#bulk-edit').modal('hide');
						let sendData = {
							rowData: this.updateData[0].rowData,
							updateData: [
								{ updateField: this.updateData[0].elementType, updatedValue: updatedValue.toString() }
							],
							submissionId: this.updateData[0].entryId,
							formId: this.updateData[0].formId,
							worklistId: this.updateData[0].worklistId,
							users: [],
							visibleToRoles: this.updateData[0].visibleToRoles,
							action: 'update'
						};
						this.sendWorklistUpdatePolling(sendData);
						this.reload.next();
					}
					i++;
				});
		});
		this.form.reset();
	}
	resetModal() {
		this.form.reset();
	}
	sendWorklistUpdatePolling(sendData) {
		if (sendData.visibleToRoles.split(',').length > 1) {
			const count = sendData.visibleToRoles.split(',').length;
			let i = 1;
			sendData.visibleToRoles.split(',').forEach((element) => {
				this._workListService.getRoleBasedStaffs(element, 0, 1).then((data) => {
					let staffList = JSON.parse(JSON.stringify(data['getSessionTenant'].roleBasedStaffs));
					staffList.forEach((element) => {
						sendData['users'].push(element.id);
					});
					if (i == count) {
						this._structureService.socket.emit('worklistDataUpdate', sendData);
					}
					i++;
				});
			});
		} else {
			this._workListService.getRoleBasedStaffs(sendData.visibleToRoles, 0, 1).then((data) => {
				let staffList = JSON.parse(JSON.stringify(data['getSessionTenant'].roleBasedStaffs));
				staffList.forEach((element) => {
					sendData['users'].push(element.id);
				});
				this._structureService.socket.emit('worklistDataUpdate', sendData);
			});
		}
	}
}
