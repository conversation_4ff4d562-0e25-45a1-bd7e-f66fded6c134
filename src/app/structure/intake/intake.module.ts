import { IndexedDbService } from './indexed-db.service';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AgGridModule } from 'ag-grid-angular';
import { IntakeService } from './intake.service';
import { IntakeWorkListService } from './intake-worklist/intake-worklist.service';
import { AppsHomeComponent } from './apps-home/apps-home.component';
// import { PulsemenuComponent } from './pulsemenu/pulsemenu.component';
import { AppsCenterComponent } from './apps-center/apps-center.component';
import { LiaisonEntryComponent} from './liaison-entry/liaison-entry.component';
import { FormWorklistDetailsComponent } from '../worklists/form-worklist-details.component';
import { FormWorklistComponent } from '../worklists/form-worklist.component';
import { IntakeWorklistComponent } from './intake-worklist/intake-worklist.component';
import { DynamicFormComponent } from './intake-worklist/dynamic-form/dynamic-form.component';
import { DynamicComponent } from './liaisons-edit-details/dynamic/dynamic.component';
import { DocumentsModule } from '../documents/documents.module';
import { FilterPipe, AssociatePatientSearchFilterPipe} from './filter.pipe';
import { DynamicFormQuestionComponent } from './intake-worklist/dynamic-form/dynamic-form-question.component';
import { DynamicBase } from './intake-worklist/dynamic-form/dynamic-base';
import { MyDatePickerModule } from 'mydatepicker';
import { Daterangepicker } from 'ng2-daterangepicker';
import { DateTimePickerModule} from 'ngx-datetime-picker';
import { ColorPickerModule } from 'ngx-color-picker';
import { MentionModule } from 'angular-mentions';
import { ToastModule } from '@syncfusion/ej2-angular-notifications';
import { LiaisonsEditDetailsComponent } from './liaisons-edit-details/liaisons-edit-details.component';
import { NotesModalComponent } from './notes-modal/notes-modal.component';
import { AdvanceSearchComponent } from './advance-search/advance-search.component';
import { IconCellRenderer } from './renderer/icon-cell-renderer.component';
import { ButtonRendererComponent } from './renderer/button-renderer.component';
import { GroupRowInnerRenderer } from './renderer/group-row-inner-renderer.component';
import { ContentCellRenderer } from './renderer/content-cell-renderer.component';
import { LinkCellRenderer } from './renderer/link-cell-renderer.component';
import { PopupCellRenderer } from './renderer/popup-cell-renderer.component';
import { ConstantService } from './constant.service';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { NgUploaderModule } from 'ngx-uploader';
import { NgxDocViewerModule } from 'ngx-doc-viewer';
import { AuthGuard } from 'app/guard/auth.guard';
import { TranslateModule } from '@ngx-translate/core';
export const routes: Routes = [
    { path: 'apps/:guid', component: AppsHomeComponent },
    { path: 'Appcenter', component: AppsCenterComponent, canActivate:[AuthGuard],data:{checkRoutingPrivileges:'enableAppCenter'}},
    // { path: 'laisan-edit-details', component: LiaisonsEditDetailsComponent },
	{ path: 'apps/:guid/:worklist/:worklistid', component: IntakeWorklistComponent },
	{ path: 'apps/:guid/:worklist/:details/:worklistid:/:id/edit', component: LiaisonsEditDetailsComponent},
	{ path: 'apps/:guid/:worklist/:details/:worklistid/:id', component: LiaisonsEditDetailsComponent}
	// `apps/${this.guid}/${this.worklistName}/details/${this.worklistid}/${editRowDetails['id']}/edit` 
]
@NgModule({
  imports: [
    CommonModule,
		RouterModule.forChild(routes),
		FormsModule,
		BrowserModule,
		AgGridModule.withComponents([ButtonRendererComponent, GroupRowInnerRenderer, IconCellRenderer, ContentCellRenderer, LinkCellRenderer, PopupCellRenderer]),
		ReactiveFormsModule,
		MyDatePickerModule,
		ColorPickerModule,
		MentionModule,
		ToastModule,
		DocumentsModule,
		Daterangepicker,
		NgUploaderModule,
		NgxDocViewerModule,
		NgMultiSelectDropDownModule.forRoot(),
		DateTimePickerModule,
                TranslateModule.forChild()
	],
	declarations: [
		AppsHomeComponent,
		// PulsemenuComponent,
		AppsCenterComponent,
		LiaisonEntryComponent,
		DynamicFormComponent,
		DynamicFormQuestionComponent,
		IntakeWorklistComponent,
        LiaisonsEditDetailsComponent,
        DynamicComponent,
        FilterPipe,
        AssociatePatientSearchFilterPipe,
        NotesModalComponent,
  		AdvanceSearchComponent,
  		AdvanceSearchComponent,
		ButtonRendererComponent,
		GroupRowInnerRenderer,
		IconCellRenderer,
		LinkCellRenderer,
		PopupCellRenderer,
		ContentCellRenderer
],
  providers: [
    IntakeService,IntakeWorkListService, IndexedDbService,ConstantService 
  ]
})
export class IntakeModule { }