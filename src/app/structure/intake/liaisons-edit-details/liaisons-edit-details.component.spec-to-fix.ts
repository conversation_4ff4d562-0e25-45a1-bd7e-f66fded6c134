import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { LiaisonsEditDetailsComponent } from './liaisons-edit-details.component';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { RouterTestingModule } from '@angular/router/testing';
import { StructureService } from 'app/structure/structure.service';
import { HttpModule } from '@angular/http';
import { Apollo, ApolloModule } from 'apollo-angular';
import { AuthService } from 'app/structure/shared/auth.service';
import { SharedService } from 'app/structure/shared/sharedServices';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { WorkListService } from 'app/structure/worklists/worklist.service';
import { FormsService } from 'app/structure/forms/forms.service';
import { IntakeWorkListService } from '../intake-worklist/intake-worklist.service';
import { IntakeService } from '../intake.service';
import { ConstantService } from '../constant.service';
import { IndexedDbService } from '../indexed-db.service';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs/observable/of';
import { provideClients } from 'test-utils';

describe('LiaisonsEditDetailsComponent', () => {
  let component: LiaisonsEditDetailsComponent;
  let fixture: ComponentFixture<LiaisonsEditDetailsComponent>;
  let workservice: WorkListService;
  const mockActivatedRoute = {
    params: of({
      guid: 'some-guid',
      worklist: 'some-worklist',
      worklistid: 'some-worklistid',
      id: 'some-id',
    }),
  };
  let formservice: FormsService;
  beforeEach(async(() => {
    TestBed.configureTestingModule({
      providers: [
        { provide: WorkListService, useValue: workservice },
        { provide: ActivatedRoute, useValue: mockActivatedRoute },
        StructureService,
        Apollo,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        TranslateService,
        IntakeService,
        ConstantService,
        IndexedDbService,
        { provide: FormsService, useValue: formservice },
        IntakeWorkListService,
      ],
      declarations: [LiaisonsEditDetailsComponent],
      imports: [
        RouterTestingModule,
        HttpModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF,
        }),
        TranslateModule.forRoot(),
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(LiaisonsEditDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
});
