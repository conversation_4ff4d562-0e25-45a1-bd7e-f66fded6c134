.non-profile{
  padding-top: 10px;
}
#personal-details span{
  display: block;
  margin-bottom: 0px;
  line-height: normal;
  padding-bottom: 0px;
} 
#personal-details span.badge{
  display: inline-block;
}
.liasion-entry-sec{
  padding-left: 20px;
  padding-right: 20px;
}
.liasion-details-section label {
  display: block;
}
.pah-profile-details-area{
  margin-left: 5px;
}
.pah-profile-img > img {
  width: 100%;
}
/* .folowup-details-sec .pah-profile-img img{
margin-top: 7px;
} */
.folowup-details-sec img {
width: auto;
min-width: 110px;
margin-top: 9px;
}
.folowup-details-sec{
margin-top: -7px;
margin-bottom: 5px !important;

}
/* .pah-custom-section .cat__apps__messaging__tab_pah--selected {
background: #f2f4f8 !important;
cursor: pointer;
color: black !important; 
} */
.name{font-size: 15px;color: #000;}

.liasion-details-col label, .liasion-details-col span{
font-weight: normal !important;
font-size: 13px;
}
.display-name .badge{
font-weight: normal !important;
font-size: 12px;
}
.display-name label{
margin-bottom: 0;
}
.allergies-details a{
font-weight: normal;
font-size: 13px;
}
.allergies-details span{
font-weight: bold;
color: #000;
}
.allergies-bottom-cnt{
/* margin-left: 15px;
margin-right: 15px; */
margin-left: 10px!important;
margin-right: 10px!important;
}
.allergies-bottom-cnt .card{
border: 0 !important;
}
.allergies-bottom-cnt .card-block{
    padding: 0 !important;
}
.col-xl-9.col-md-9.custom-content.patient-details-right section{
  padding-top: 0 !important;
  padding-right: 0 !important;
}
.allergies-bottom-cnt .chatwith-model-head-pah{
  border-right: 0 !important;
  background: #f9f9f9;
  padding-left: 20px;
  border-bottom: 1px solid #f1f0f6 !important;
  color: #7e7e7e;
  font-size: 14px;
  padding-top: 7px;
  padding-bottom: 7px;
}
.allergies-bottom-cnt .card{
  border: 0 !important;
}
.allergies-bottom-cnt .main-tabs{
  border-left: 1px solid #f1f0f6 !important;
  border-right: 1px solid #f1f0f6 !important;
  border-top: 1px solid #f1f0f6 !important;
}
.new-form .form-input {
  height: 30px !important;
display: block;
padding-left: 10px;
  font-size: 13px;
}
.new-form .col-sm-6 {
margin-bottom: 5px;
}
.patient-location {
  padding: 0 10px 0 15px;
  font-size: 13px;
}
.right-input{
  height: 30px !important;
  margin-bottom: 5px;
  padding-top: 0;
  padding-bottom: 0;
  font-size: 13px;
  position: relative;
} 
.new-form .col-sm-6 {
margin-bottom: 5px;
}
.allergies-bottom-cnt .sub-tabs .col-sm-5{
line-height: 30px;
}
.allergies-sec {
  display: block;
  padding: 20px 20px;
  display: block;
  margin: 10px 0;
}
.allergies-sec .col-sm-12{
  padding: 12px;
}
.liaisons-details-breadcrumb{
padding-bottom: 0 !important;
/* padding-top: 50px !important; */
}
.pah-custom-section {
margin-bottom: 10px !important;
} 
.liasion-details-col label {
margin-bottom: 2px;
}
.intakefollowup-left li {
    list-style-type: none;
  }

  .intakefollowup-left ul {
    position: unset !important;
    width: 100%;
    padding: 0;
    border: 0;
    margin-bottom: 10px;
    box-shadow: none;
  }

  .intakefollowup-left a {
    background: #f9f9f9;
    color: #3b3a42;
  }

  .intakefollowup-left .cat__apps__messaging__tab_pah--selected {
    background: #e4e8f1 !important;
    cursor: pointer;
    color: #3b3a42 !important;

  }

  .intakefollowup-left .badge {
    background: #7e7e7e !important;
    color: #fff !important;
    width: 20px !important;
    height: 20px !important;
    border: 0 !important;
    border-radius: 50%;
    padding-top: 5px;
    font-weight: 600;
  }

  .opr-btn{
    margin-right: 10px;
    border: none;
  }
  
.badge {
  display: inline-block;
  padding: .25em .4em;
  font-size: 75%;
  font-weight: 700;
  line-height: 1;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: .25rem;
}
#title-action-block{
  background-color: #fff;  
}
.card {
 display: block;
 width: 100%!important;
} 
.sticky {
  position: sticky;
  top: 64px;
  z-index: 99; 
  width: 100%;
}
.sticky-width{
  max-width: 44%!important;
}
.float-sm-right{
  margin-left: 5px;
}
/* submenu common css */
.pah-tab {
  border: none;
}
.intakefollowup-left{
  border:1px solid #eee!important;
}
#leftSidebar ul.dropdown-menu{
	margin:10px 0px!important;
	padding:0px!important;	 
}
#leftSidebar  ul.dropdown-menu li{	
	margin:3px 0px!important;
	padding:8px!important;	 
}
#leftSidebar ul.dropdown-menu li > a{
color:#7d7d7d!important;
background:transparent;
}
#leftSidebar ul.dropdown-menu li > a:hover , #leftSidebar ul.dropdown-menu li > a.cat__apps__messaging__tab_pah--selected{
  color:#85c7be!important;
  background:transparent;
}
#leftSidebar  ul.dropdown-menu li > a:before { content: '>'; }  
.dropdown > a.dropdown-submenu{
  display: block;
}
.disabled { 
    pointer-events: none; 
}
.counter-circle {
  height: 25px;
    width: 25px;
    background-color: #6b6b6b;
    border-radius: 50%;
    display: inline-block;
    padding: 1px;
    text-align: center;
    font-weight: bold;
    color: #fff;
    line-height: 25px;
}
.therapy-disabled {
  background-color: #c3c3c3;
  color: #fff !important;
  pointer-events: none;
}
.therapy-enabled {
  background-color: #515151;
  color: #fff !important;
}
.btn.btn-sm.btn-primary.float-sm-right{
  border:0 !important;
  }
  .disable-modal {
    pointer-events: none;
    background:#e7e7e7;
  }
