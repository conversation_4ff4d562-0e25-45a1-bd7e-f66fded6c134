import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, EventEmitter, Output, ɵConsole, ViewChild, Input } from '@angular/core';
import { Router, ActivatedRoute, Params, NavigationEnd } from '@angular/router';
import { StructureService } from '../../../structure/structure.service';
import { IntakeService } from '../intake.service';
import { WorkListService } from 'app/structure/worklists/worklist.service';
import { IntakeWorkListService } from '../intake-worklist/intake-worklist.service';
import { HostListener } from '@angular/core';
import { LiaisonEntryComponent } from '../liaison-entry/liaison-entry.component';
import { THIS_EXPR } from '@angular/compiler/src/output/output_ast';
import { ConstantService } from '../constant.service';
import { DynamicComponent } from './dynamic/dynamic.component';
import { SharedService } from '../../shared/sharedServices';
import { IndexedDbService } from '../indexed-db.service';
import { ToolTipService } from '../../tool-tip.service';
import { Subscription } from 'rxjs';
let moment = require('moment/moment');
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var swal: any;
declare var NProgress: any;
var momentTz = require('moment-timezone');
var jstz = require('jstz');
const timezone = jstz.determine();

@Component({
  selector: 'app-liaisons-edit-details',
  templateUrl: './liaisons-edit-details.component.html',
  styleUrls: ['./liaisons-edit-details.component.css']
})
export class LiaisonsEditDetailsComponent implements OnInit, OnDestroy {
  @ViewChild(DynamicComponent) dynamic: DynamicComponent;
  @Input('form') editLiaisonEntryForm;
  userDetails;
  patientStatus;
  dynamicData: any = [];
  integrationSettings: any = [];
  optionShow;
  parentShow;
  refreshTherapy = false;
  worklistEditData;
  metaData;
  clicked = false;
  dynamicTabs;
  metaArray: any = [];
  patientId = '';
  reportFields: any;
  dataLoadingMsg: boolean;
  guid: any;
  worklistName: string;
  worklistid: number;
  worklistDetails: any;
  formDataList = [];
  //heading: string;
  saveAction = false;
  patientAge: number;
  patientDob: any;
  liaisonNote: string;
  intakeNote: string;
  dynamicButton: any = [];
  showAddNewBtn = false;
  childWorklistTabs = [];
  noteType: string;
  noteCatId: number;
  loadingData: Boolean;
  entryFormType = null;
  initialTabId = null;
  rowData;
  showModalComponent = false;
  modalHeading = '';
  childWorklistId;
  therapyTxt = "Add New Therapy";
  parentWorklistName;
  parentWorklistId;
  toggleMenu = false;
  saveBtnStatus;
  disableSaveButton = false;
  workflowId;
  workflowType;
  worklistHeading;
  loadSpinner;
  disableReload;
  worklistView = 'form';
  selectedWorklist = '';
  showRecallBtn = false;
  userData = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
  userDataConfig = this._structureService.userDataConfig?JSON.parse(this._structureService.userDataConfig):{};
  worklistMenuName;
  timeLeft: number = 60;
  minLeft: number = 0;
  unlockLeft: number;
  interval;
  secInterval;
  refreshPage = false;
  enableSaveAction = false;
  therapyRecheckCompleted = false;
  therapyRecheckStatus = false;
  therapyRecheckReasonValue;
	private socketEventSubscriptions: Subscription;
  @ViewChild('laisionEntry') laisionEntry;
  constructor(private router: Router, private route: ActivatedRoute,
    private _structureService: StructureService,
    private _workListService: WorkListService,
    private _intakeworkListService: IntakeWorkListService,
    public _intakeService: IntakeService,
    private _constantService: ConstantService,
    private _sharedService: SharedService,
    private _indexedDbService: IndexedDbService,
    private toolTipService: ToolTipService
  ) {
    clearInterval(this.secInterval);
    clearInterval(this.interval);
    this.optionShow = "LiaisonEntry";
    this.route.params.subscribe((params: Params) => {
      this.worklistName = params.worklist;
      this.worklistHeading = this.worklistName.replace(/-/g, ' ');
      this.worklistid = params.worklistid;
      this.patientId = params.id;
      this.dataLoadingMsg = true;
      if (params.guid && params.worklist) {
        this._structureService.displayProgress.emit(false);
        this.guid = params.guid;
        this._structureService.appCenterGuid = this.guid;
      }

    });
    this._intakeService.therapyList = [];
    this._intakeService.moaList = [];
    this._intakeService.recheckReasons = [];
    // this._intakeService.blogNoteSubject = [];
    // this._intakeService.pnSubjects = [];
    this._intakeService.edocList = [];
  }
  @HostListener('click', ['$event'])
  onClick(event) {
    if (event.target.id == 'parent-worklist') {
      this.toggleMenu = !this.toggleMenu;
    } else if (event.target.id == 'child-worklist') {
      this.toggleMenu = true;
    }
  }
  @HostListener("window:beforeunload", ["$event"]) unloadHandler(event: Event) {
    if(this.metaData.enableLockingMode == true) {
      let lockDetails = {
        patientId: this.patientId,
        worklistId: this.worklistid,
        minLeft: this.minLeft,
        timeLeft: this.timeLeft
      };
      let lockPatientDetails = [];
      if(localStorage.getItem('lockPatient') && localStorage.getItem('lockPatient') != '') {
        lockPatientDetails = JSON.parse(localStorage.getItem('lockPatient'));
        lockPatientDetails.push(lockDetails);
        localStorage.setItem('lockPatient',JSON.stringify(lockPatientDetails));
      } else {
        lockPatientDetails.push(lockDetails);
        localStorage.setItem('lockPatient',JSON.stringify(lockPatientDetails));
      }
      localStorage.setItem('currentPatient',this.patientId);
    }
  }
  ngOnDestroy() {
    localStorage.removeItem('selectedTab');
    clearInterval(this.secInterval);
    clearInterval(this.interval);
    this._intakeService.currentPatient = '';
    //if(this.metaData.enableLockingMode == true) {
      if(this.refreshPage == false) {     
          this.unlockPatient();
      }
      if(localStorage.getItem('lockPatient') && localStorage.getItem('lockPatient') != ''){
        let lockPatient = JSON.parse(localStorage.getItem('lockPatient'));
        if(lockPatient.length > 0) {
          let index = lockPatient.findIndex(x=> x.worklistId == this.worklistid && x.patientId == this.patientId);
          lockPatient.splice(index,1);
          localStorage.setItem('lockPatient', JSON.stringify(lockPatient));
        } else {
          localStorage.setItem('lockPatient','');
        }
      }
    //}
    /**Unsubscribe all the socket event subscriptions */
    if(this.socketEventSubscriptions) this.socketEventSubscriptions.unsubscribe();
    
  }
  ngOnInit() {
    this._intakeService.enableSwal = false;
    if(this._intakeService.currentPatient == '' && !localStorage.getItem('currentPatient')){
      this.goBack();
      this.refreshPage = true;
    } else {
      localStorage.removeItem('currentPatient');
      let worklistid = this.route.snapshot.paramMap.get('worklistid');
      this.parentWorklistName = this.route.snapshot.paramMap.get('worklist');
      this.parentWorklistId = this.route.snapshot.paramMap.get('worklistid');
      if (this._intakeService.activePatientDetails.length > 0) {
        this.worklistEditData = JSON.parse(JSON.stringify(this._intakeService.activePatientDetails));
        this.convertDob(this.worklistEditData.dob);
        this.dataLoadingMsg = false;
        //this.loadingData = true;
        this.getAssociatedWorklists();
      } else {
        this.getParentWorklistDetails(worklistid, this.patientId);
      }
    }
    
    this._structureService.activePatientActivityDetails = [];
    $(document).ready(function () {
      $('#sidebarCollapse').on('click', function () {
        $('#sidebar').toggleClass('active');
      });

    });
    window.onscroll = function () { myFunction() };
    var navbar = document.getElementById("title-action-block");
    var contentDiv = document.getElementById("body-block");
    var sticky = navbar.offsetTop;
    function myFunction() {
      if (window.pageYOffset >= sticky) {
        navbar.classList.add("sticky");
        contentDiv.classList.add("liaisons-details-breadcrumb");
      } else {
        navbar.classList.remove("sticky");
        contentDiv.classList.remove("liaisons-details-breadcrumb");
      }
    }
    this.socketEventSubscriptions = this._structureService.subscribeSocketEvent('worklistDataUpdate').subscribe((data) => {
      	const refillData = data['formData'];
      	if (refillData.action == 'unlock') {
          /**Data polling while unlock a patient from dashboard action button */
          if(refillData['patientId'] == this.patientId && (refillData['lockLevel'] == 'app' || (refillData['lockLevel'] == 'worklist' &&  refillData['worklistId'] == this.worklistid))) {
            this._intakeService.enableSwal = true;
            swal(
              {
                title: '',
                type: 'warning',
                text: `Hi, Patient ${refillData['patientName']} record is unlocked by ${refillData['userName']} at ${refillData['lockedTime']} and your changes will not be saved.`,
                confirmButtonText: 'Ok',
                closeOnConfirm: true,
                customClass: 'swal-wide',
                html: true,
              },
              () => {
                this._intakeService.enableSwal = false;
                const zone = timezone.name();
                const now = momentTz.tz(new Date(), zone).format('MM/DD/YYYY h:mm:ss A');
                var activityData = {
                  activityName: 'Accept Unlcok Patient',
                  activityType: 'Unlock Patient',
                  activityDescription: `${this.userData.displayName} notified the unlock action of ${this.worklistEditData.firstName} ${this.worklistEditData.lastName} with MRN ${this.worklistEditData.patientId} by ${refillData['userName']} at ${now}`,
                };
                this._structureService.trackActivity(activityData);
              });
          }
      	}
    });
  }
  /**Get worklist details from cache */
	async getCacheData(worklistid) {
    if (this._intakeworkListService.browserCache == 'localstorage') {
      if (localStorage.getItem('worklistDetails') && localStorage.getItem('worklistDetails') != '') {
        let details = JSON.parse(localStorage.getItem('worklistDetails')).filter(x => Number(x.worklistId) == Number(worklistid) && x.edited == false);
        return details.length > 0 ? details[0].worklistDetails : [];
      } else {
        return [];
      }
    } else if (this._intakeworkListService.browserCache == 'indexdb') {
      let dataFromIdb: any = [];
      await this._indexedDbService.getByKeyFromIDb(this._intakeworkListService.indexDbConfig).then(
      (response) => {
        dataFromIdb = response;
      });
      if (dataFromIdb && !dataFromIdb.length) {
        return [];
      } else {
        let details = dataFromIdb.filter(x => Number(x.worklistId) == Number(worklistid) && x.edited == false);
        return details.length > 0 ? details[0].worklistDetails : [];
      }
    } else if(this._intakeworkListService.browserCache == 'sharedvariable') {
      const defIndex = this._sharedService.worklistDetails.findIndex(x => x.worklistId == worklistid && x.edited == false);
      if (defIndex == -1) {
        return [];
      } else {
        return this._sharedService.worklistDetails[defIndex].worklistDetails;
      }
    }
  }
    /**Set worklist details to the cache */
  setCacheData(worklistid, worklistDetails) {
    var promise = new Promise((resolve, reject) => {
      let worklistData = [{ 'worklistId': worklistid, 'worklistDetails': worklistDetails, 'edited': false }];
      if (this._intakeworkListService.browserCache == 'localstorage') {
      if (localStorage.getItem('worklistDetails') && localStorage.getItem('worklistDetails') != '') {
        let details = JSON.parse(localStorage.getItem('worklistDetails'));
        let index = details.findIndex(x=> x.worklistId == worklistid);
        if(index != -1) {
          details[index].worklistDetails = worklistDetails;
          details[index].edited = false;
        } else {
          details.push({ 'worklistId': worklistid, 'worklistDetails': worklistDetails, 'edited': false });
        }
        localStorage.setItem('worklistDetails', JSON.stringify(details));
      } else {
        localStorage.setItem('worklistDetails', JSON.stringify(worklistData));
      }
      } else if (this._intakeworkListService.browserCache == 'indexdb') {
      let dataFromIdb: any = [];
      this._indexedDbService.getByKeyFromIDb(this._intakeworkListService.indexDbConfig).then(
        (response) => {
        dataFromIdb = response;
        if (dataFromIdb && !dataFromIdb.length) {
          this._indexedDbService.addToIDb(this._intakeworkListService.indexDbConfig, worklistData);
        } else {
          let details = dataFromIdb;
          let index = details.findIndex(x=> x.worklistId == worklistid);
          if(index != -1) {
          details[index].worklistDetails = worklistDetails;
          details[index].edited = false;
          } else {
          details.push({ 'worklistId': worklistid, 'worklistDetails': worklistDetails, 'edited': false });
          }
          this._indexedDbService.addToIDb(this._intakeworkListService.indexDbConfig, details);
        }
        });
      } else if(this._intakeworkListService.browserCache == 'sharedvariable') {
      const defIndex = this._sharedService.worklistDetails.findIndex(x => x.worklistId == worklistid);
      if (defIndex == -1) {
        this._sharedService.worklistDetails.push({ 'worklistId': worklistid, 'worklistDetails': worklistDetails, 'edited': false });
      } else {
        this._sharedService.worklistDetails[defIndex].worklistId = worklistid;
        this._sharedService.worklistDetails[defIndex].worklistDetails = worklistDetails;
      }
      }
      resolve();
    });
    return promise;
  }
  getParentWorklistDetails(worklistId, patientId) {
    this.getCacheData(worklistId).then((data) => {
			this.worklistDetails = data;
			if (this.worklistDetails.length == 0) {
			  this._intakeworkListService.getWorklistDetails(worklistId).then((data) => {
				if (data['getSessionTenant']) {
				  this.worklistDetails = data['getSessionTenant'].formWorklists;
				  this.setCacheData(this.worklistDetails[0].id, this.worklistDetails).then(()=>console.log('cache'));
				  this.getIntakeWorklistEditdetails(worklistId, patientId);
				}
			  });
			} else {
			  this.getIntakeWorklistEditdetails(worklistId, patientId);
			}
		});
  }
  getIntakeWorklistEditdetails(worklistId, patientId) {
    let metaData;
    this.metaData = {};
    /**Get details of particular worklist */
    //this._intakeworkListService.getWorklistEditDetails(worklistId).then((data) => {
      this._structureService.displayMenuId.emit(worklistId);
    //  if (data['getSessionTenant']) {
    //    this.worklistDetails = data['getSessionTenant'].formWorklists;
        
        //this.heading = this.worklistDetails[0].name;
        /**Get the reportFields and sort  */
        //this.reportFields = JSON.parse(JSON.stringify(this.worklistDetails[0].reportFields));
        let actionButtons = JSON.parse(JSON.stringify(this.worklistDetails[0].singleWorklistAction));
        let index = actionButtons['actionButton'].findIndex(x=> x.actionCallbackFunction == 'editEntry');
        if(index != -1){
          let actionBtnFields = actionButtons['actionButton'][index].actionFields;
          let actionIndex = actionBtnFields.findIndex(x=> x.associatedField == 'patientStatus');
          if(actionIndex != -1) {
            if(actionBtnFields[actionIndex].fieldValues.indexOf('Completed') != -1){
              this.enableSaveAction = true;
            }
          }
        }
        let newJson = this.worklistDetails[0].description;
        newJson = newJson.replace(/'/g, '"');
        metaData = JSON.parse(newJson);
        this.metaData = metaData;
        let graphqlQuery = '';
        let url = this.metaData.endpoint;
        if (this.metaData.graphqlApi) {
          let url = this.metaData.graphqlEndpoint;
          let fieldList = this.metaData.fieldList.split(',');
          let fieldString = '';
          fieldList.forEach(field => {
            if (field.includes('.')) {
              let colArray = field.split('.');
              let endString = '';
              colArray.forEach((element, index) => {
                fieldString = ` ${fieldString} ${element} `;
                if (index !== colArray.length - 1) {
                  fieldString = ` ${fieldString} { `;
                  endString = ` ${endString} } `;
                }

              });
              fieldString = ` ${fieldString} ${endString}  `;
            } else {
              fieldString = `${fieldString} ${field}`;
            }
          });
          let newQuery = this.metaData.parameters.replace('$fields', fieldString);
          //newQuery = newQuery.replace('$fields', fieldString);
          let lockConfig = 0;
          let appLock;
          if(this.metaData.enableLockingMode == true) {
            lockConfig = 1;
            if(this.metaData.lockLevel == 'app') {
              appLock = true;
            } else {
              appLock = false;
            }
          }
          let variables = { id: patientId, lockConfig : lockConfig, createdBy: this.userData.userId, appLock : appLock };
          this._intakeworkListService.getWorklistDataUsingGraphQLAPI(url, newQuery, variables, this.prepareHeaderVariables()).then((data) => {
            let keyArray = Object.keys(data['data']);
            this.worklistEditData = JSON.parse(JSON.stringify(data['data'][keyArray[0]].data[0]));
            this.setEntryType(this.worklistDetails);
            this.dataLoadingMsg = false;
            this.convertDob(this.worklistEditData.dob);
            this.patientStatus = this.worklistEditData.patientStatus;
            if(this.enableSaveAction == true) {
              this.patientStatus = '';
            }
            if(this.metaData.enableLockingMode == true) {
              this.minLeft = this.metaData.lockTime > 0 ? this.metaData.lockTime-1 : this.metaData.lockTime;
              if(localStorage.getItem('lockPatient') && localStorage.getItem('lockPatient') != '') {
                let lockPatient = JSON.parse(localStorage.getItem('lockPatient'));
                let index = lockPatient.findIndex(x=> x.worklistId == this.worklistid && x.patientId == this.patientId);
                if(index != -1) {
                  this.minLeft = lockPatient[index].minLeft;
                  this.timeLeft = lockPatient[index].timeLeft;
                } 
                lockPatient.splice(index,1);
                if(lockPatient.length > 0) {
                  localStorage.setItem('lockPatient', JSON.stringify(lockPatient));
                } else {
                  localStorage.setItem('lockPatient','');
                }
              } else {
                if(this.worklistEditData.patientLock == true && !localStorage.getItem('currentPatient')) {
                  this.refreshPage = true;
                  this.goBack();
                }
              }
              this.startLockingCount(this.metaData.lockTime);
              const zone = timezone.name();
              let now = momentTz.tz(new Date(), zone).format('MM/DD/YYYY h:mm:ss A');
              var activityData = {
                activityName: 'Lock Patient',
                activityType: 'Lock a patient',
                activityDescription: this.userData.displayName + ' locked the patient ' + this.worklistEditData.firstName + ' ' + this.worklistEditData.lastName + ' with MRN ' + this.worklistEditData.patientId + 'for editing from ' + this.metaData.nameOfDesktopMenu + ' dashboard since ' + now,
              };
              this._structureService.trackActivity(activityData);
            }
            //this.loadingData = true;
            // this.worklistEditData['valueFormatter'] = this.convertDateFormat;
            // this.worklistEditData = data.patients[data[0]]
            if (this.worklistEditData.dob) {
              this.patientAge = this.ageFromDateOfBirthday(this.worklistEditData.dob);
            }
            this.getAssociatedWorklists();
          });
        }
      }
    //});
  getAssociatedWorklists() {
    let worklistid = this.route.snapshot.paramMap.get('worklistid');
    if (worklistid) {
      this._structureService.displayProgress.emit(false);
      this.loadingData = true;
      let childWorklists = [];
      if(localStorage.getItem('childWorklistTabs') && localStorage.getItem('childWorklistTabs') != '') {
        childWorklists = JSON.parse(localStorage.getItem('childWorklistTabs'));
      }
      let index = childWorklists.findIndex(x=> x.worklistid == this.parentWorklistId);
      if(index == -1) {
        this._intakeService.getWorklistCategoryMenuList(null, worklistid).then((data) => {
          this.loadingData = false;
          if (data['getWorklistMenu']) {
            let menuGroupList = [{'name': null, 'id': null, 'worklists':[]}];
            let categoryWorklist = [];
            data['getWorklistMenu'].childWorklist.forEach((element) => {
              if (element.metaData) {
                let newJson = element.metaData;
                newJson = newJson.replace(/'/g, '"');
                const metaData = JSON.parse(newJson);
                metaData['id'] = element.worklistId;
                let actionLink = metaData.nameOfDesktopMenu.replace(/[^a-zA-Z ]/g, ' ').trim();
                actionLink = actionLink.replace(/\s+/g, '-').toLowerCase();
                const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
                if (
                  ((metaData.visibleToRoles &&
                    metaData.visibleToRoles.split(',').indexOf(userDetails.roleId) !== -1) ||
                    (metaData.allowCrossTenant == true &&
                      metaData.visibleToOtherRoles &&
                      metaData.visibleToOtherRoles.split(',').indexOf(userDetails.roleId) !== -1)) &&
                      metaData.showInMenu !== true && element.active == true
                ) {
                  if(metaData.showTab && metaData.showTab == true && menuGroupList.findIndex(x=> x.id == element.menuGroupId) == -1) {
                    menuGroupList.push({'name': element.menuGroupName, 'id': element.menuGroupId, 'worklists': []});
                  }
                  categoryWorklist.push({
                    worklistId: element.worklistId,
                    workListName: element.workListName,
                    menuGroupId: element.menuGroupId,
                    actionLink: `worklist/${actionLink}/${element.id}`,
                    menuIcon: metaData.menuIcon,
                    nameOfDesktopMenu: metaData.nameOfDesktopMenu,
                    showTab: metaData.showTab,
                    statusCallback: metaData.enableStatusIndication == true ? metaData.statusIndicationCallback : '',
                    metaData: metaData
                  });
                }
              }
            });
            menuGroupList.forEach(element => {
              element.worklists = categoryWorklist.filter((x) => x.menuGroupId == element.id && (x.showTab && x.showTab == true))
            });
            menuGroupList[0].worklists = categoryWorklist.filter((x) => x.showTab == false || x.showTab == undefined);
            this.childWorklistTabs = menuGroupList;
            childWorklists.push({'worklistid': this.parentWorklistId, 'worklistName': this.parentWorklistName, 'childWorklists': this.childWorklistTabs})
            localStorage.setItem('childWorklistTabs', JSON.stringify(childWorklists));
            this.getWorklists(this.childWorklistTabs);
          }
        });
      } else {
        this.loadingData = false;
        this.childWorklistTabs = childWorklists[index].childWorklists;
        this.getWorklists(this.childWorklistTabs);
      }
    }
  }
  // getAssociatedWorklists1() {
  //   let worklistid = this.route.snapshot.paramMap.get('worklistid');
  //   if (worklistid) {
  //     this._structureService.displayProgress.emit(false);
  //     this.loadingData = true;
  //     let childWorklists = [];
  //     if(localStorage.getItem('childWorklistTabs') && localStorage.getItem('childWorklistTabs') != '') {
  //       childWorklists = JSON.parse(localStorage.getItem('childWorklistTabs'));
  //     }
  //     let index = childWorklists.findIndex(x=> x.worklistid == this.parentWorklistId);
  //     if(index == -1) {
  //       this._intakeService.getWorklistCategoryTabList(null, worklistid).then((data) => {
  //         this.loadingData = false;
  //         if (data && data.length > 0) {
  //           this.childWorklistTabs = data;
  //           childWorklists.push({'worklistid': this.parentWorklistId, 'worklistName': this.parentWorklistName, 'childWorklists': this.childWorklistTabs})
  //           localStorage.setItem('childWorklistTabs', JSON.stringify(childWorklists));
  //           this.getWorklists(data);
  //         }
  //       });
  //     } else {
  //       this.loadingData = false;
  //       this.childWorklistTabs = childWorklists[index].childWorklists;
  //       this.getWorklists(this.childWorklistTabs);
  //     }
  //   }
  // }
  getWorklists(childworklist) {
    if (childworklist[0].worklists[0].worklistId) {
      this.initialTabId = childworklist[0].worklists[0].worklistId;
    }
    this.loadingData = false;
    childworklist.forEach((elem)=>{
      elem.worklists.forEach(element => {
        this.metaArray.push(element.metaData);
      });
    });
    this.metaArray.sort(function (a, b) {
      if (a.tabIndex < b.tabIndex) { return -1; }
      if (a.tabIndex > b.tabIndex) { return 1; }
      return 0;
    });
    this.getCountsOfEachTab();
    if (localStorage.getItem('selectedTab')) {
      //  $('#leftSidebar >div#"' + localStorage.getItem('selectedTab') + '"').click();
      this.showTabData(localStorage.getItem('selectedTab'), null, 'reload');
    } else {
      var id;
      id = $('div.chatwith-model-head-pah:first').attr('id');
      if (id == null || id == undefined) {
        id = this.initialTabId;
      }
      this.showTabData(id, null, null);
    }
  }
  convertDob(dob){
    let timeZone = this.userDataConfig.tenant_timezoneName;
    //let data = moment.utc(new Date(dob));
    this.patientDob = moment.utc(new Date(dob)).format('MM/DD/YYYY');
    //this.patientDob = momentTz.tz((data), timeZone).format('MM/DD/YYYY');
  }
  respondBackToUser() {
    let sendData = {
      rowData: {},
      updateData: [
        { updateField: 'patientLock', updatedValue: false},
        { updateField: 'lockedAt', updatedValue: ''},
        { updateField: 'lockedUserId', updatedValue: ''},
        { updateField: 'lockedByUser', updatedValue: ''}
      ],
      patientId: this.patientId,
      worklistId: this.worklistid,
      users: [],
      action: 'update',
      clientId: this._structureService.socket.io.engine.id,
      lockLevel: this.metaData.lockLevel
    };
    this._intakeService.getStaffIdUsers({appGuid: this.guid}).then((data) => {
      const staffList = JSON.parse(JSON.stringify(data['getStaffIdUsers']));
      sendData['users'] = data['getStaffIdUsers'].staffIds;
      this._structureService.socket.emit('worklistDataUpdate', sendData);
    });
    //this.metaData.visibleToRoles = this.metaData.visibleToRoles ? this.metaData.visibleToRoles : localStorage.getItem('visibleToRole');
    // if (this.metaData.visibleToRoles && this.metaData.visibleToRoles != 1) {
			// this._intakeworkListService.getRoleBasedStaffs(this.metaData.visibleToRoles, 0, 1).then((data) => {
			// 	const staffList = JSON.parse(JSON.stringify(data['getSessionTenant'].getMultipleRolesBasedStaffs));
			// 	staffList.forEach((element) => {
			// 		sendData['users'].push(element.id);
			// 	});
      //   console.log('sendData',sendData);
			// 	this._structureService.socket.emit('worklistDataUpdate', sendData);
			// });
		// }
  }
  referalDetailsHandler(rowData: any) {
    this.rowData = rowData;
  }
  reloadTabDataHandler(e) {
    this.disableReload = e.status;
    this.loadSpinner = e.status;
    this.worklistView = e.type;
  }
  setEntryType(worklists) {
    this.showRecallBtn = false;
   // let index = worklists.findIndex(x => x.id == this.parentWorklistId);
    let newJson = worklists[0].description;
    newJson = newJson.replace(/'/g, '"');
    let metaData = JSON.parse(newJson);
    let callback = metaData.callbackFunctionName != null ? metaData.callbackFunctionName.trim().toUpperCase() : '';
    if (callback == 'LIAISONS') {
      this.workflowId = this._constantService.entryTypeIds['LIAISON'];
      this.workflowType = this._constantService.entryTypes['LIAISON'];
    } else if (callback == 'INTAKEFOLLOWUP') {
      this.workflowId = this._constantService.entryTypeIds['INTAKE'];
      this.workflowType = this._constantService.entryTypes['INTAKE'];
    } else if (callback == 'INITIALINSURANCEVERIFICATION') {
      this.workflowId = this._constantService.entryTypeIds['INITIAL_INS'];
      this.workflowType = this._constantService.entryTypes['INITIAL_INS'];
      if(this.worklistEditData && this.worklistEditData.patientStatus != null &&  this.worklistEditData.patientStatus.toLowerCase() == 'progress') {
        this.showRecallBtn = true;
      }
    } else if (callback == 'INSURANCEVERIFICATIONFOLLOWUP') {
      this.workflowId = this._constantService.entryTypeIds['INS_VERIFICATION'];
      this.workflowType = this._constantService.entryTypes['INS_VERIFICATION'];
    } else if (callback == 'PHARMACYFOLLOWUP') {
      this.workflowId = this._constantService.entryTypeIds['PHARMACY_FOLLOWUP'];
      this.workflowType = this._constantService.entryTypes['PHARMACY_FOLLOWUP'];
    }
  }
  getCountsOfEachTab() {
    const filter = this.metaArray.filter(
      worklist => worklist.enableCounterApi
    ).map(element => {
      const identifierName = this._constantService.countIdentifierNames[element.counterApiCallback.toLowerCase()];
      const identifier = this._constantService.countIdentifiers[element.counterApiCallback.toLowerCase()];
      const counterApiCallback = element.counterApiCallback.toLowerCase();
      switch (counterApiCallback) {
        case 'therapy':
         return {
           identifier: identifier,
           identifierName: identifierName,
           filter: [
             {
               column: 'patientId',
               type: 'equals',
               filter: this.patientId,
               filterTo: '',
               filterType: ''
             },
             {
               column: 'status',
               type: 'equals',
               filter: '1',
               filterTo: '',
               filterType: ''
             }
           ]
         };
          break;
        case 'userprogressnote':
          return {
            identifier: identifier,
            identifierName: identifierName,
            filter: [
              {
                column: 'mrn',
                type: 'equals',
                filter: this.worklistEditData.patientId,
                filterTo: '',
                filterType: ''
              }
            ]
          };
          break;
        case 'liaisonnote':
        case 'intakenote':
        case 'insurancenote':
        case 'pharmacynote':
          return {
            identifier: identifier,
            identifierName: identifierName,
            filter: [
              {
                column: 'patientId',
                type: 'equals',
                filter: this.patientId,
                filterTo: '',
                filterType: ''
              },
              {
                column: 'noteType',
                type: 'equals',
                filter: counterApiCallback.replace('note', ''),
                filterTo: '',
                filterType: ''
              }
            ]
          };
          break;
          default:
          return null;
      }
    }).filter(Boolean);
    this._intakeService.getWorklistCount(filter).then(data => {
      const counter = data['recordCounts'];
      this.childWorklistTabs.forEach(elem => {
        elem.worklists.forEach(element => {
          let idIndex = this.metaArray.findIndex(
            x => x.id === element.worklistId
          );
          if (
            this.workflowId ===
              this._constantService.entryTypeIds['INITIAL_INS'] &&
            this.metaArray[idIndex].callbackFunctionName ===
              'insuranceVerification'
          ) {
            this.metaArray[idIndex].statusIndicationCallback =
              'initialInsuranceVerificationStatus';
          }
          if (
            this.metaArray[idIndex].enableStatusIndication &&
            this.worklistEditData[
              this.metaArray[idIndex].statusIndicationCallback
            ] != null
          ) {
            element.status = this.worklistEditData[
              this.metaArray[idIndex].statusIndicationCallback
            ]
              .trim()
              .toUpperCase();
          }
          let index = counter.findIndex(
            x => x.identifierName === this.metaArray[idIndex].counterApiCallback
          );
          if (index !== -1) {
            element.count = counter[index].count;
          }
        });
      });
    });
  }
  convertDateFormat(params) {
    if (!params.value) {
      return '';
    }
    if (params.value || params.value != '') {
      const dateParts = params.value.toString().split('/');
      if (dateParts.length <= 1) {
        let newDate = new Date(Number(params.value));
        return moment.utc(newDate).format('MM/DD/YYYY');
      } else {
        return params.value;
      }
    } else {
      return '';
    }
  }
  startLockingCount(lockTime) {
    let now = new Date();
    let sendData = {
      rowData: {},
      updateData: [
        { updateField: 'patientLock', updatedValue: true},
        { updateField: 'lockedAt', updatedValue: now},
        { updateField: 'lockedUserId', updatedValue: this.userData.userId},
        { updateField: 'lockedByUser', updatedValue: this.userData.displayName},
      ],
      patientId: this.patientId,
      worklistId: this.worklistid,
      lockLevel: this.metaData.lockLevel,
      users: [],
      action: 'update',
      clientId: this._structureService.socket.io.engine.id
    };
    this._intakeService.getStaffIdUsers({appGuid: this.guid}).then((data) => {
      const staffList = JSON.parse(JSON.stringify(data['getStaffIdUsers']));
      sendData['users'] = data['getStaffIdUsers'].staffIds;
      this._structureService.socket.emit('worklistDataUpdate', sendData);
    });
    // if(this.metaData.visibleToRoles && this.metaData.visibleToRoles != '') {
			// this._intakeworkListService.getRoleBasedStaffs(this.metaData.visibleToRoles, 0, 1).then((data) => {
			// 	const staffList = JSON.parse(JSON.stringify(data['getSessionTenant'].getMultipleRolesBasedStaffs));
			// 	staffList.forEach((element) => {
			// 		sendData['users'].push(element.id);
      //   });
      //   this._structureService.socket.emit('worklistDataUpdate', sendData);
			// });
    // }
    
    this.secInterval = setInterval(() => {
      if(this.minLeft > 0) {
        if(this.timeLeft > 0) {
          this.timeLeft--;
        } 
        if(this.timeLeft == 0){
          if(this.minLeft > 0) {
            this.minLeft--;
          }
          this.timeLeft = 60;
        }
      } else {
        if(this.minLeft == 0 && this.timeLeft == 60 && lockTime != 1) {
          this.showEditAlertMsg('1');
        }
        if(this.minLeft == 0 && this.timeLeft == 0){
          this.timeLeft = 0;
          this.minLeft = 0;
          clearInterval(this.secInterval);
          clearInterval(this.interval);
          swal.close();
          $('#dynamic-worklist-modal').modal('hide');
          this.goBack();
        } else {
          this.timeLeft--;
        }
          
      }
    },1000)
  }
  showEditAlertMsg(unlockTime) {
    this._intakeService.enableSwal = true;
    swal(
      {
        title: '',
        type: 'warning',
        text: 'Your edit session of this patient will be automatically close after '+unlockTime+' min, please save your work.',
        confirmButtonText: 'Ok',
        closeOnConfirm: true,
        customClass: 'swal-wide',
        html: true,
      },
      () => {
        this._intakeService.enableSwal = false;
      });
  }
  /** Prepare header variables for api */
  prepareHeaderVariables() {
    let headers = {};
    if (this.metaData.enableAuthorization === true) {
      if (this.metaData.authtype === "token") {
        headers = {
          authorizationtoken: this._structureService.getCookie('authID')
        }
        // if (this.metaData.authsource === "callback") { 
        // 	headers = this[`${this.metaData.authCallbackFunction}`]();

        // }
      }
    }
    return headers;
  }
  goToHome() {
    this.router.navigate(['apps', this.guid]);
  }
  goBack() {
    this.router.navigate(['apps', this.guid, this.worklistName, this.worklistid]);
  }
  showTabData(data, name = null, reload = null) {
    let filterLiaisonsDetails = [];
    this.disableSaveButton = false;
    this.saveBtnStatus = '';
    this.parentShow = name;
    $('#' + data + '_1').show();
    let loading = reload;
    
    if (localStorage.getItem('selectedTab') == data && (reload == 'tab' || reload == 'refresh')) {
      this.loadSpinner = true;
      this.disableReload = true;
      loading = 'refresh';
    } else {
      this.loadSpinner = false;
    }
    //if (localStorage.getItem('selectedTab')!=data || reload=='reload')
    //  if (localStorage.getItem('selectedTab')!=data)
    //{
    this.selectedWorklist = localStorage.getItem('selectedTab');

    this.childWorklistId = data;
    if (data) {
      localStorage.setItem('selectedTab', data);
      this.dynamicData = [];
      this.dynamicButton = [];
      if (data == "LiaisonEntry") {
        this.dynamicData = { "tabName": data };
      } else {
        filterLiaisonsDetails = this.metaArray.filter(x => x.id == data);
        if(filterLiaisonsDetails.length == 0) {
          filterLiaisonsDetails.push(this.metaArray[0]);
        }
        if (filterLiaisonsDetails[0].nameOfDesktopMenu) {
           this.worklistMenuName = filterLiaisonsDetails[0].nameOfDesktopMenu;
        }
       if (filterLiaisonsDetails[0].addNewButton) {
          this.showAddNewBtn = true;
          if (filterLiaisonsDetails[0].callbackFunctionName == 'addUserTherapy' || filterLiaisonsDetails[0].callbackFunctionName == 'intakePaperWork' || filterLiaisonsDetails[0].callbackFunctionName == 'insuranceVerification' || filterLiaisonsDetails[0].callbackFunctionName == 'finalInsuranceVerification' || filterLiaisonsDetails[0].callbackFunctionName == 'intakeFinalReview' || filterLiaisonsDetails[0].callbackFunctionName == 'miscellaneous' || filterLiaisonsDetails[0].callbackFunctionName == 'insuranceVerificationItems' || filterLiaisonsDetails[0].callbackFunctionName == 'initialInsuranceVerificationItems') {
            this.disableSaveButton = true;
          }
        } else {
          this.showAddNewBtn = false;
        }
        this.dynamicData = { "patientId": this.patientId, "tabName": filterLiaisonsDetails[0].nameOfDesktopMenu, "pahworklistId": filterLiaisonsDetails[0].id, 'filterLiaisonsDetails': filterLiaisonsDetails, 'loading': loading };
        // this._structureService.activePatientActivityHub = true;
        let btnText = (
          typeof filterLiaisonsDetails[0].buttonText == 'undefined' || filterLiaisonsDetails[0].buttonText == '') ?
          'Add New' : filterLiaisonsDetails[0].buttonText;
        if(((this.workflowId == this._constantService.entryTypeIds['INITIAL_INS'] || this.workflowId == this._constantService.entryTypeIds['INS_VERIFICATION'] || this.workflowId == this._constantService.entryTypeIds['PHARMACY_FOLLOWUP']) ||
         (this.workflowId == this._constantService.entryTypeIds['LIAISON'] && this.worklistEditData.therapyStatus == 1 && this.worklistEditData.therapyRecheckRequired == false) ||
         (this.workflowId == this._constantService.entryTypeIds['INTAKE'] && this.worklistEditData.therapyStatus == 1 && this.worklistEditData.therapyRecheckRequired == false))
         && filterLiaisonsDetails[0].callbackFunctionName == 'addUserTherapy') {
          btnText = 'View Therapy';
        }
        this.dynamicButton = { "btnText": btnText, "btnCallback": filterLiaisonsDetails[0].callbackFunctionName }
      }
      this.optionShow = data;
      if (this.dynamicData.pahworklistId) {
        this._structureService.activePatientActivityHub = true;
        this._structureService.activePatientActivityDetails = [];
      }
    }
    //}
    setTimeout(() => {
      $('#' + data + '_1').hide();
    }, 2000);

  }
  reloadTab() {
    if (localStorage.getItem('selectedTab')) {
      this.showTabData(localStorage.getItem('selectedTab'), null, 'refresh');
      // this.showTabData(localStorage.getItem('selectedTab'),null,'reload');
    }
    this.getCountsOfEachTab();
  }
  recallPatient(e) {
    swal(
			{
				title: 'Are you sure?',
				text: 'You are going to recall this patient',
				type: 'warning',
				showCancelButton: true,
				cancelButtonClass: 'btn-default',
				confirmButtonClass: 'btn-warning',
				confirmButtonText: 'Ok',
				closeOnConfirm: true
			},
			() => {
        let patientId = [];
        patientId.push(this.worklistEditData.id);
        let variable = {
          id: patientId, 
          workflowType: this.workflowType,
          createdBy: this.userData.userId
        };
        this._intakeworkListService.recallPatient(variable).subscribe(({ data: response }) => {
        	/**Activity tracking */
        	var activityData = {
        		activityName: 'Recall Process',
        		activityType: 'Recall a patient',
        		activityDescription: this.userData.displayName + ' initiated recall process for the patient ' + this.worklistEditData.firstName + ' ' + this.worklistEditData.lastName + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.metaData.nameOfDesktopMenu + ' dashboard',
        	};	
          this._structureService.trackActivity(activityData);
          this.goBack();
        	/** */
        });
      });
	}
  ageFromDateOfBirthday(dateOfBirth: any): any {
    // const today = new Date();
    const birthDate = new Date(Number(dateOfBirth));
    // let age = today.getFullYear() - birthDate.getFullYear();
    // const m = today.getMonth() - birthDate.getMonth();
    let year = moment().diff(birthDate, 'years');
    let month = moment().diff(birthDate, 'month');
    let day = moment().diff(birthDate, 'day');
    // if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
    //   age--;
    // }
    if(year > 0){
      let text = (year == 1) ? 'Year' : 'Years';
      return year + ' ' + text;
    } else if(month > 0) {
      let text = (month == 1) ? 'Month' : 'Months';
      return month + ' ' + text;
    } else if(day > 0) {
      let text = (day == 1) ? 'Day' : 'Days';
      return day + ' ' + text;
    }

    //return age;

  }
  // Tabs for MainTasks
  updateLaisonEntry(entryType) {
    this.saveAction = true;
    this.laisionEntry.confirmLiaisonEntry(entryType);
  }
  showModal(type = '') {
    this.refreshTherapy = false;
    let entryType;
    switch (type) {
      case 'liaisonNote':
        this.showModalComponent = true;
        this.noteType = this._constantService.noteTypes['liaisonNote'];
        this.noteCatId = this._constantService.noteTypeIds['liaisonNote'];
        this.modalHeading = this.toolTipService.getTranslateData(
          'LABELS.LIAISON_NOTE'
        );
        break;
      case 'blogNote':
        this.showModalComponent = true;
        this.noteType = this._constantService.noteTypes['blogNote'];
        this.noteCatId = this._constantService.noteTypeIds['blogNote'];
        this.modalHeading = this.toolTipService.getTranslateDataWithParam(
          'LABELS.BLOG_NOTE',
          { worklist: this.worklistMenuName }
        );
        break;
      case 'intakeNote':
        this.showModalComponent = true;
        this.noteType = this._constantService.noteTypes['intakeNote'];
        this.noteCatId = this._constantService.noteTypeIds['intakeNote'];
        this.modalHeading = this.toolTipService.getTranslateData(
          'LABELS.INTAKE_NOTE'
        );
        break;
      case 'insuranceNote':
        this.showModalComponent = true;
        this.noteType = this._constantService.noteTypes['insuranceNote'];
        this.noteCatId = this._constantService.noteTypeIds['insuranceNote'];
        this.modalHeading = this.toolTipService.getTranslateData(
          'LABELS.INSURANCE_NOTE'
        );
        break;
      case 'pharmacyNote':
        this.showModalComponent = true;
        this.noteType = this._constantService.noteTypes['pharmacyNote'];
        this.noteCatId = this._constantService.noteTypeIds['pharmacyNote'];
        this.modalHeading = this.toolTipService.getTranslateData(
          'LABELS.PHARMACY_NOTE'
        );
        break;
      case 'liaisonEntry':
        this.disableSaveButton = true;
        entryType = this._constantService.entryTypes['LIAISON'];
        this.updateLaisonEntry(entryType);
        break;
      case 'intakeEntry':
        this.disableSaveButton = true;
        entryType = this._constantService.entryTypes['INTAKE'];
        this.updateLaisonEntry(entryType);
        break;
      case 'pharmacyEntry':
        this.disableSaveButton = true;
        entryType = this._constantService.entryTypes['PHARMACY_FOLLOWUP'];
        this.updateLaisonEntry(entryType);
        break;
      case 'addUserTherapy':
        this.showModalComponent = true;
        this.noteType = 'Therapy';
        this.noteCatId = 0;
        this.modalHeading = 'Therapy';
        break;
      case 'addEdoc':
        this.showModalComponent = true;
        this.noteType = 'eDoc';
        this.noteCatId = 0;
        this.modalHeading = 'Add eDocs';
        break;
      case 'intakePaperWork':
        this.disableSaveButton = true;
        this.saveChecklist('intakePaperWork');
        break;
      case 'insuranceVerification':
        this.disableSaveButton = true;
        this.saveChecklist('insuranceVerification');
        break;
      case 'intakeFinalReview':
        this.disableSaveButton = true;
        this.saveChecklist('intakeFinalReview');
        break;
      case 'miscellaneous':
        this.disableSaveButton = true;
        this.saveChecklist('miscellaneous');
        break;
      case 'finalInsuranceVerification':
        this.disableSaveButton = true;
        this.saveChecklist('finalInsuranceVerification');
        break;
      case 'insuranceVerificationItems':
        this.disableSaveButton = true;
        this.saveChecklist('insuranceVerificationItems');
        break;
      case 'initialInsuranceVerificationItems':
        this.disableSaveButton = true;
        this.saveChecklist('initialInsuranceVerificationItems');
        break;
      default:
        this.noteType = '';
        this.noteCatId = 0;
        break;
    }
    if (
      type == 'liaisonNote' ||
      type == 'blogNote' ||
      type == 'intakeNote' ||
      type == 'insuranceNote' ||
      type == 'pharmacyNote' ||
      type == 'addUserTherapy' ||
      type == 'addEdoc'
    ) {
      $('#dynamic-worklist-modal').modal('show');
    }
  }
  closeDynamicModal() {
    this.showModalComponent = false;
  }
  emitReloadGrid(event) {
    this.refreshTherapy = true;
    this.getCountsOfEachTab();
    this.showModalComponent = false;
    //this.reloadTab();
  }
  emitTherayStatus(event) {
    this.worklistEditData.therapyStatus = event.therapyStatus;
    this.dynamicButton = { "btnText": event.btnText, "btnCallback": 'addUserTherapy'};
      this.worklistEditData.therapyRecheck = event.therapyRecheck;
      this.worklistEditData.therapyRecheckReason = event.recheckReason;
      this.worklistEditData.therapyRecheckReasonLovId = event.recheckReasonId;
      this.worklistEditData.therapyRecheckRequired = event.therapyRecheckRequired;

    // if(event.therapyStatus == true) {
    //  this.showTherapyBtn = false;
    //  this.worklistEditData.therapyStatus = 1;
    //   this.dynamicButton = { "btnText": "View Therapy", "btnCallback": 'addUserTherapy'};

    // }
    //  if(event.therapyStatus == false) {
      
    //   this.worklistEditData.therapyStatus = 0;
    //   this.worklistEditData.therapyRecheck = true;
    //   this.worklistEditData.therapyRecheckReason = event.recheckReason;
    //   this.worklistEditData.therapyRecheckReasonLovId = event.recheckReasonId;
    //   this.worklistEditData.therapyRecheckRequired = false;
    //   // this.therapyRecheckReasonValue = event.recheckReason;
    //   this.therapyRecheckCompleted = true;
    //   // this.therapyRecheckStatus = true;
    //  }
    //  if(event.therapyRecheck == true) {
    //    this.worklistEditData.therapyRecheck = true;
    //    this.worklistEditData.therapyRecheckReason = event.recheckReason;
    //    this.worklistEditData.therapyRecheckReasonLovId = event.recheckReasonId;
    //    this.worklistEditData.therapyRecheckRequired = false;
    //  }

  }
  saveChecklist(callback) {
    this.dynamic.customFieldBtnAction(callback);
  }
  changeWorklistStatus(e) {
    /**send status of paper work in each actions performed */
    this.childWorklistTabs.forEach(element => {
      let index = element.worklists.findIndex(x => x.worklistId == this.optionShow);
      if (index != -1) {
        element.worklists[index].status = e.status;
        this.saveBtnStatus = e.status;
      }
      if(e.sameTab == false) {
        let cindex = element.worklists.findIndex(x => x.statusCallback == e.callback);

        if (cindex != -1) {
          element.worklists[cindex].status = e.status;
        }
      }
    });
    if(this.workflowId == this._constantService.entryTypeIds['INITIAL_INS'] && e.callback == 'insuranceVerificationStatus' && e.sameTab == true) {
      this.worklistEditData['initialInsuranceVerificationStatus'] = e.status;
    } else if (e.sameTab == true) {
      this.worklistEditData[e.callback] = e.status;
    }
    if(this.workflowId == this._constantService.entryTypeIds['INITIAL_INS'] && this.worklistEditData['initialInsuranceItemStatus'].toLowerCase() == 'completed' && this.worklistEditData['initialInsuranceVerificationStatus'].toLowerCase() == 'completed') {
      this.showRecallBtn = false;
    }
  }
  changeSaveBtnStatus(e) {
    /**Enable/disable save button in each service call */
    this.disableSaveButton = e;
  }
  worklistStatus(e) {
    /**send status of paper work in initial loading */
    this.saveBtnStatus = e;
  }
  updateInsurancePolicy(value) {
    this.worklistEditData['insurancePolicy'] = value;
  }
  getIntegrationSettings(value) {
    this.integrationSettings = value;
  }
  unlockPatient() {
    let variable = {
      id: [this.patientId],
      workflowType: this.workflowType,
      unlockedBy: Number(this.userData.userId)
    }
    clearInterval(this.secInterval);
    clearInterval(this.interval);
    if(!this.workflowType || this.workflowType == ''){
      variable.workflowType = localStorage.getItem('currentWorkflowType');
      let changePatientData = {
        updateData: [
          { updateField: 'patientLock', updatedValue: false},
          { updateField: 'lockedAt', updatedValue: ''},
          { updateField: 'lockedUserId', updatedValue: ''},
          { updateField: 'lockedByUser', updatedValue: ''}
        ],
        patientId: this.patientId,
        worklistId: this.worklistid,
        lockLevel: this.metaData.lockLevel,
      }
      localStorage.setItem('changePatientData', JSON.stringify(changePatientData));
      setTimeout(() => {
        this._intakeService.unlockPatient(variable).subscribe(({ data: response }) => {
          if(response['unallocatePatient'].status == 201 && response['unallocatePatient'].statusMessage.toLowerCase() == 'success') {
            this.respondBackToUser();
            const zone = timezone.name();
            let now = momentTz.tz(new Date(), zone).format('MM/DD/YYYY h:mm:ss A');
            var activityData = {
              activityName: 'Unlock Patient',
              activityType: 'Unlock a patient',
              activityDescription: this.userData.displayName + ' unlocked the patient ' + this.worklistEditData.firstName + ' ' + this.worklistEditData.lastName + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.metaData.nameOfDesktopMenu + ' dashboard edit page since ' + now,
            };
            this._structureService.trackActivity(activityData);
          }
        });
      },2000);
    } else {
      this._intakeService.unlockPatient(variable).subscribe(({ data: response }) => {
        if(response['unallocatePatient'].status == 201 && response['unallocatePatient'].statusMessage.toLowerCase() == 'success') {
          this.respondBackToUser();
          const zone = timezone.name();
          let now = momentTz.tz(new Date(), zone).format('MM/DD/YYYY h:mm:ss A');
          var activityData = {
            activityName: 'Unlock Patient',
            activityType: 'Unlock a patient',
            activityDescription: this.userData.displayName + ' unlocked the patient ' + this.worklistEditData.firstName + ' ' + this.worklistEditData.lastName + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.metaData.nameOfDesktopMenu + ' dashboard edit page since ' + now,
          };
          this._structureService.trackActivity(activityData);
        }
      });
    }
    
  }
}
