<section class="card pah-custom-section">
  <div class="card-header" id="title-action-block" >
    <span class="cat__core__title save-reload-back"> 
      <div class="row" >
        <div class="col-sm-5" >
          <strong id="invite-initiate-label">{{worklistHeading | titlecase}} Details</strong>
        </div>
        <div class="col-sm-7">
          <div *ngIf="!dataLoadingMsg">
            <a (click)="goBack()" class="btn btn-sm btn-primary float-sm-right">
                <i class="fa fa-long-arrow-left" aria-hidden="true"></i>&nbsp;Back
            </a>
            <button class="btn btn-sm float-sm-right" [disabled]="disableReload == true || saveBtnStatus == 'COMPLETED'" style="background-color:#52b3aa;color:#ffffff;" (click)="reloadTab()">
                <i class="fa fa-refresh" aria-hidden="true"></i>&nbsp;Reload
            </button>
            <button class="btn btn-sm float-sm-right" style="background-color:#ff3333;color:#ffffff;" *ngIf="showRecallBtn" (click)="recallPatient()">
              <i class="fa fa-refresh" aria-hidden="true"></i>&nbsp;Recall
          </button>
            <a *ngIf="optionShow != 'LiaisonEntry' && showAddNewBtn" 
            [ngClass]="(disableSaveButton == true || saveBtnStatus == 'COMPLETED' || patientStatus =='Completed') ? 'therapy-disabled':'therapy-enabled'" class="btn btn-sm float-sm-right" (click)="showModal(dynamicButton.btnCallback)">
                <i class="fa fa-floppy-o" aria-hidden="true"></i>&nbsp;{{dynamicButton.btnText}}
            </a>
            <span class="pull-right pt-2" style="font-size:13px;color:#0190fe;" *ngIf="metaData.enableLockingMode == true"><i class="fa fa-lock"></i> {{minLeft}} minute {{timeLeft}} seconds left</span>
            <!-- <button type="button" *ngIf="optionShow != 'LiaisonEntry' && showAddNewBtn" 
            [ngClass]="((!showTherapyBtn && parentWorklistName =='liaisons') || saveBtnStatus == 'COMPLETED' || patientStatus =='Completed') ?'therapy-disabled':'therapy-enabled'" class="btn btn-sm float-sm-right" (click)="showModal(dynamicButton.btnCallback)" 
            [attr.title]="!showTherapyBtn && parentWorklistName =='liaisons' ?
             'Therapy already completed for this patient' : null" >
                <i class="fa fa-floppy-o" aria-hidden="true" ></i>&nbsp;{{dynamicButton.btnText}}
             </button [disabled] = "!editLiaisonEntryForm.form.valid"> -->
            <!-- <button type="button" *ngIf="optionShow == 'LiaisonEntry'" 
              class="pull-right btn btn-sm btn-primary" 
            style="background-color:#515151;" [disabled]="clicked"
            (click)="updateLaisonEntry(); clicked = true;">Save</button> -->
            <!-- <a *ngIf="optionShow == 'LiaisonEntry'" class="pull-right btn btn-sm btn-primary opr-btn"
            style="background-color:#515151;" (click)="updateLaisonEntry()">
            <i class="fa fa-floppy-o" aria-hidden="true"></i>&nbsp;Save</a>
            -->
        </div>
      </div>
      </div>      
    </span>
  </div>
  <div class="card-block " id="body-block">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a (click)="goToHome()">{{this._intakeService.breadcrumbAppName}}-Home</a></li>
      <li class="breadcrumb-item"><a (click)="goBack()">{{worklistHeading| titlecase}}</a></li>
      <li class="breadcrumb-item">Details</li>
    </ol>
  </div>
  <section class="card" *ngIf="dataLoadingMsg">
    <div class="card-block mb-2 mt-2">
      <div class="wait-loading">
        <img src="assets/img/loader/loading.gif" />
      </div>
    </div>
  </section>
  <div class="row" *ngIf="!dataLoadingMsg" id="personal-details">
    <div class="col-sm-12">
       <div class="row" style="padding: 0px 22px;">
         <div class="col-sm-4" >

          <div class="row">
            <div class="col-sm-4" > 
              <div class="pah-profile-img">
                <img   src="assets/modules/dummy-assets/common/img/dummy-image.png"
                  alt="Alternative text to the image">               
              </div>
            </div>
            <div class="col-sm-8" >   
                <h6>{{worklistEditData?.name}} <span class="badge badge-success">{{worklistEditData?.hrPatStatus}}</span></h6>        
                <span><label>Gender:</label> {{worklistEditData?.gender | titlecase }}</span>
                <span><label>DOB:</label> {{patientDob}}</span>
                <span><label>Age:</label> {{patientAge}}</span>
                <span><label>MRN:</label> {{worklistEditData?.patientId}}</span>
            </div>
          </div> 
        </div>
         <div class="col-sm-4 non-profile">
          <span><label>Email:</label> {{worklistEditData?.email}}</span>
          <span><label>Phone:</label> {{worklistEditData?.phoneNumber}}</span>
          <span *ngIf="metaData.callbackFunctionName.toUpperCase() == 'LIAISONS' || metaData.callbackFunctionName.toUpperCase() == 'INTAKEFOLLOWUP'"><label>Insurance Coordinator:</label> {{worklistEditData?.caseManager}}</span>
          <span *ngIf="metaData.callbackFunctionName.toUpperCase() == 'INITIALINSURANCEVERIFICATION' || metaData.callbackFunctionName.toUpperCase() == 'INSURANCEVERIFICATIONFOLLOWUP'"><label>Insurance Coordinator:</label> {{worklistEditData?.insuranceRep}}</span>
        </div>         
        <div class="col-sm-4 non-profile" >
          <span><label>Address:</label> {{worklistEditData?.primaryAddress}}</span>
          <span><label>Shipping Address:</label> {{worklistEditData?.shippingAddress}}</span>
        </div>

       </div>
    </div>
  </div>

  <div>
    <div class="allergies-sec" *ngIf="!dataLoadingMsg">
      <!-- {{worklistEditData|json}} -->
      <div class="col-sm-12" style="background-color:#f2f4f8;">
        <div class="row allergies-details">
          <div class="col-sm-4">
            <a>
              <span>Allergies</span> <br>
              {{worklistEditData?.allergies ? worklistEditData?.allergies : 'NIL'}}
            </a>
          </div>
          <div class="col-sm-4">
            <a>
              <span>Precautions</span> <br>
              {{worklistEditData?.precautions ? worklistEditData?.precautions : 'NIL'}}
              
            </a>
          </div>
          <div class="col-sm-4">
            <a>
              <span>Primary Diagnosis</span> <br>
              {{worklistEditData?.diagnosis ? worklistEditData?.diagnosis : 'NIL'}}
            </a>

          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row allergies-bottom-cnt" *ngIf="!dataLoadingMsg">
    <nav class="col-lg-3" id="sidebar intake-left-nav">
      <section class="card">
        <div class="card-block">
          <div class="cat__core__card-sidebar">
            <div class="row">
            </div>
            <div class="main-tabs">
              <div class="chatwith-modal-tab pah-tab intakefollowup-left">
                <div *ngIf="loadingData" class="messageLoads loading-container">
                  <div class="lds-roller">
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                    <div></div>
                  </div>
                  <div class="loading-text">Loading ...</div>
                </div>

                <div *ngIf="!loadingData" id="leftSidebar" class="" style="width:100%;">
                  <div *ngFor="let menuGroup of childWorklistTabs" id="parent-worklist"
                    [hidden]="menuGroup.name != null && menuGroup.worklists.length > 0">
                    <div class="chatwith-model-head-pah" id="{{childWorklist.worklistId}}"
                      *ngFor="let childWorklist of menuGroup.worklists"
                      [class.cat__apps__messaging__tab_pah--selected]="optionShow==childWorklist.worklistId"
                      (click)="showTabData(childWorklist.worklistId,'','tab');toggleMenu = false;"><i class="" aria-hidden="true"></i>
                      {{childWorklist.nameOfDesktopMenu}}
                      <span class="counter-circle pull-right" [hidden]="!childWorklist.count || childWorklist.count == 0">{{childWorklist.count}}</span>
                      <i class="fa fa-refresh fa-spin" *ngIf="childWorklist.worklistId == selectedWorklist && worklistView == 'form'" [hidden]="loadSpinner == false"></i>
                      <span class="pull-right"><i class="fa fa-exclamation-triangle" *ngIf="childWorklist.status == 'PROGRESS'" style="color:#f9c429;"></i></span>
                        <span class="pull-right"><i class="fa fa-circle" *ngIf="childWorklist.status == 'COMPLETED'" style="color:#6ee14d;"></i></span>
                        <span class="pull-right"><i class="fa fa-times" *ngIf="childWorklist.status == 'NEW'" style="color:#de2727;"></i></span>
                    </div>

                  </div>
                  <div class="dropdown chatwith-model-head-pah" id="parent-worklist"  *ngFor="let menuGroup of childWorklistTabs"
                    [hidden]="menuGroup.name == null && menuGroup.worklists.length> 0"
                    [ngClass]="{'show': toggleMenu == true && parentShow == menuGroup.name}" (click)="parentShow = menuGroup.name">
                    <div class="dropdown-toggle dropdown-submenu" id="parent-worklist">{{menuGroup.name | titlecase}}

                      <!--<span class="caret badge badge-pill badge-dark ml-2 float-right">2</span>-->
                    </div>
                    <ul class="dropdown-menu">
                      <li *ngFor="let childData of menuGroup.worklists" id="child-worklist"
                        [class.cat__apps__messaging__tab_pah--selected]="optionShow==childData.worklistId" (click)="showTabData(childData.worklistId,menuGroup.name,'tab')">
                        <a id="childData.worklistId" id="child-worklist" >
                          <span class="glyphicon glyphicon-play" aria-hidden="true"></span>
                          {{childData.nameOfDesktopMenu}}
                        </a><span class="pull-right"><i class="fa fa-exclamation-triangle" *ngIf="childData.status == 'PROGRESS'" style="color:#f9c429;"></i></span>
                        <span class="pull-right"><i class="fa fa-circle" *ngIf="childData.status == 'COMPLETED'" style="color:#6ee14d;"></i></span>
                        <span class="pull-right"><i class="fa fa-times" *ngIf="childData.status == 'NEW'" style="color:#de2727;"></i></span>
                      </li>
                    </ul>
                  </div>
                  

                </div>
              </div>

              <div *ngIf="statusMessage" style="text-align: center;margin-top: 33px;">{{statusMessage}}</div>

            </div>
          </div>
        </div>
      </section>
    </nav>

    <div class="col-xl-9 col-lg-9 col-md-12 custom-content patient-details-right">
      <section class="card pah-custom-section">

        <div class="row sub-tabs">
          <!--  <div class="col-md-12" *ngIf="optionShow=='LiaisonEntry'">
            <app-liaison-entry #laisionEntry *ngIf="worklistEditData !=null" [worklistEditData]="worklistEditData">
            </app-liaison-entry>
          </div>
          -->
          <ng-container *ngFor="let metaDat of metaArray" class="col-md-12">
            <div class="col-md-12" *ngIf="optionShow==metaDat.id">
              <app-dynamic #laisionEntry [refreshTherapy]="refreshTherapy"  
              fromDashboard = "false"
              [lockConfig] = "metaData.enableLockingMode"
              [appLock]= "metaData.lockLevel"
              [parentWorklistName]='parentWorklistName' 
              [parentWorklistId]='parentWorklistId'
              [workflowId]='workflowId'
              [workflowType] = 'workflowType'
              [worklistEditData]='worklistEditData'
              [dynamicData]="dynamicData" 
              (reloadTabData)="reloadTabDataHandler($event)"
              (referalDetails)="referalDetailsHandler($event)"
              (worklistStatus)="worklistStatus($event)"
              (changeStatus)="changeWorklistStatus($event)"
              (changeBtnStatus)="changeSaveBtnStatus($event)"
              (insurancePolicy)="updateInsurancePolicy($event)"
              (integrationDetails)="getIntegrationSettings($event)">
              </app-dynamic>
              <!-- <app-notes-modal
                [dynamicData]="dynamicData" ></app-notes-modal> -->
              <!-- </app-notes-modal> -->
            </div>
          </ng-container>
        </div>

      </section>
    </div>
  </div>
  <div class="modal therapy-modal" id="dynamic-worklist-modal" data-backdrop="static" tabindex="-1" role="dialog"
  aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog" [ngClass]="(noteType == 'Therapy' || 'eDoc') ? 'modal-lg' : 'modal-md'">
      <div class="modal-content" [ngClass]="_intakeService.enableSwal == true ? 'disable-modal' : ''">
          <div class="modal-header">
              <h4 class="modal-title" id="exampleModalLabel">{{modalHeading}}</h4>
              <button title="Close" type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="closeDynamicModal()">
                  <span aria-hidden="true">&times;</span>
              </button>
          </div>
          <app-notes-modal *ngIf="showModalComponent"
          [patientId]="patientId"
          [rowData] ="rowData"
          [worklistMenuName]='worklistMenuName'
          [integrationSettings] = "integrationSettings"
          [childWorklistId]="childWorklistId"
          [noteType]='noteType' 
          [workflowId]='workflowId'
          [worklistEditData]='worklistEditData'
          [dynamicData]="dynamicData" 
          [parentWorklistName]='parentWorklistName'
          [noteCatId]='noteCatId'
          (reloadGrid)='emitReloadGrid($event)'
          (therapyStatus)='emitTherayStatus($event)'
          (closeModal)='closeDynamicModal()'
          fromDashboard='false'>
          </app-notes-modal>
        </div>
      </div>
    </div>
</section>
