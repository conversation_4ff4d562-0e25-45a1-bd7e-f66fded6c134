.search-afterfilter {
  padding-right: 30px;
  box-sizing: border-box;
}

.data-cnt label {
  color: #1d6472;
}

.data-cnt p {
  color: #818181;
}

.data-cnt .form-group {
  clear: both;
}

.custom-filter-select {
  -webkit-appearance: menulist !important;
  background-color: white;
  width: 156px;
  height: 27px;
  border-radius: 3px 3px 3px 3px;
  border-color: #d3d3d3a3;
  color: #717070;
  font-size: 12px;
}
.red-border-class {
  border: 1px solid red;
}
.no-border-class {
  border: 1px solid #d2d9e5;
}
.head-block {
  border-bottom: 1px solid #d4d4d4;
  font-size: 20px !important;
  color: #5d5d5d !important;
}
.reasonDropdown {
  float: left;
}
.selectRecheck{
  margin-left: 25px;
  padding: 0.5rem;
}
.radio-chkbox-container{
  padding:10px 0 0 0;
  }

.search-input {
  padding: 0.28rem 0.57rem;
  width: 50%;
  float: left;
  margin-right: 6px;
}
.responsive {
  width: 100%;
  height: auto;
}
.disable-modal {
  pointer-events: none;
  background:#e7e7e7;
}
.my-tooltip {
  position: absolute;
  background: #f7f7f7;
  border: 1px solid #ccc;
  right: 22px;
  min-width: 200px;
  border-radius: 6px;
}
.my-tooltip:after, .my-tooltip:before {
  bottom: 100%;
  left: 70%;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

.my-tooltip:after {
  border-color: rgba(136, 183, 213, 0);
  border-bottom-color: #f7f7f7;
  border-width: 13px;
  margin-left: -13px;
}
.my-tooltip:before {
  border-color: rgba(194, 225, 245, 0);
  border-bottom-color: #ccc;
  border-width: 14px;
  margin-left: -14px;
}
.my-tooltip .content-block {
  padding: 15px 20px 15px 20px;
}
.full-loader {
  position: fixed;
  top: 28%;
  left: 42%;
  height: auto;
  padding: 16px;
  z-index: 1050;
  overflow: auto; 
  text-align:center;
  color:#FFF;
}

.loader-overlay {
  position: fixed;
  top: 0%;
  left: 0%;
  width: 100%;
  height: 100%;
  background-color: black;
  z-index: 1050;
  -moz-opacity: 0.5;
  opacity: .5;
  filter: alpha(opacity=80);
}