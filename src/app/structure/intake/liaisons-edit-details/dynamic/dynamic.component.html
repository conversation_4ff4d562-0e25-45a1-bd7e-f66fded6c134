<div class="row" id="grid-wrapper">
  <ng-container *ngIf="worklistView=='grid'">
    <div class="col-md-12 mb-4 " *ngIf="worklistHeading != ''">
      <div class="head-block">
        <span class="">
          {{worklistHeading}}</span>
      </div>
    </div>

    <!-- <div>
      <span *ngIf="showLoading" style="color:teal;text-align: center;padding:20px 20px;">Please wait while we process
        your request...<i class="fa fa-refresh fa-spin"></i></span>
    </div> -->
    <div class="row form-status-view" *ngIf="!showLoading && worklistView=='grid'">
      <div class="col-sm-12">
        <div [hidden]="rowData.length == 0  && rowModelType  !='serverSide'">
          <ng-container *ngFor="let widget of dashboardWidgets">
            <button class="widget-btn" *ngIf="(widget.widgetValue!='Cancelled' && widget.widgetValue!='Canceled')"
              [class.cat__apps__messaging__tab_pah_button--selected]="optionShowButton==widget.widgetValue"
              (click)="updateColumnData(widget.widgetValue, widget.formField, widget.hideColumns, 'false', widget.count)"
              [class.disable-widget]="disableWidget" [disabled]="disableWidget"><i
                [ngStyle]="{'color': widget.iconColor}" class="{{widget.displayIcon}} pt-1 pb-1"></i>
              {{widget.count}}
              {{widget.displayText}}</button>
          </ng-container>
        </div>
      </div>
    </div>
    <!-- Actions from form worklist -->
    <div class="col-md-12 form-status-view" >
      <div class="col-md-8">
        <ng-container *ngFor="let field of customFields">
          <div class="col-md-12 row">
          <span *ngIf="field.fieldType == 'textbox' && (workflowId == _constantService.entryTypeIds['INITIAL_INS'] || workflowId == _constantService.entryTypeIds['INS_VERIFICATION'])">
            <label class="mr-2" style="float:left;">{{field.fieldLabel}}</label>
            <input type="text" name="customTextbox" [(ngModel)]="customTextboxValue"  class="form-control form-control input-sm mr-2" style="width:50%;float:left;"/>
            <button class="btn btn-primary btn-sm reset-btn" style="margin-top:0.12rem;" [disabled]="customTextboxValue == '' || worklistEditData.insurancePolicy == customTextboxValue" (click)="customFieldTextboxAction(field.fieldCallback)">Save</button>
          </span>
          <span *ngIf="field.fieldType == 'button' ">
            <button class="btn btn-sm btn-primary" [hidden]="disableCustomField"
              (click)="customFieldBtnAction(field.fieldCallback)">{{field.fieldLabel}}</button>
          </span>
          <div *ngIf="therapyRecheckStatus && field.fieldType == 'checkbox'">
            <span class="reasonDropdown">
              <label for="recheck">{{field.fieldLabel}}</label>
              <i class="fa fa-check-square" style="font-size:15px;color:#47cc47"></i>
            </span>
            <div class="ml-2 reasonDropdown">
              Recheck Reason: <label>{{therapyRecheckReasonValue}}</label>
            </div>
          </div>

          <span *ngIf="field.fieldType == 'dropdown'">
            <label>{{field.fieldLabel}} </label>
            <select class="form-control" (change)="customFieldDropdownAction($event)">
              <option>Select</option>
            </select>
          </span>
        </div>
        </ng-container>
      </div>
      <div class="col-md-4" *ngIf="!showLoading && worklistView=='grid' && showCustomExport">
        <div id="btn-action-patient" class="btn-group pull-right">
          <button aria-expanded="false" [disabled]="disableWidget"
            class="btn btn-sm btn-default dropdown-toggle action-btn-user-settings-patient" data-toggle="dropdown"
            type="button"><i class="fa fa-file-excel-o"></i> Export</button>
          <ul class="dropdown-menu">
            <a class="dropdown-item " href="javascript: void(0);" (click)="exportCSV()">Export CSV</a>
            <a class="dropdown-item" href="javascript: void(0);" (click)="exportExcel('xlsx')">Export Excel(.xlsx)</a>
            <a class="dropdown-item" href="javascript: void(0);" (click)="exportExcel('xml')">Export Excel(.xml)</a>
          </ul>
        </div>
      </div>
    </div>
    <div class="row form-status-view" *ngIf="metaData.showPageSize">
      <div class="col-md-3">
          <label style="margin-left:0.6rem;">Page Size</label>
          <select (change)="changePaginationSize($event)" class="form-control page-list" [(ngModel)]="pageSize">
              <option *ngFor="let size of pageList" [hidden]="size == ''" value="{{size}}">{{size}}</option>
          </select>
      </div>
            
  </div>
    <div class="row form-status-view" *ngIf="worklistView=='grid'">
      <div class="col-md-4">
        <div [hidden]="hideActionButtons == true || singleRowActions.length === 0" style="padding-left: 0.7rem;">
          <label class="mr-2">Actions:</label>
          <span *ngFor="let action of singleRowActions">
            <a class="mr-3 {{action.cssClass}}" [ngClass]="{'disabled':action.disable}"
              *ngIf="action.type == 'single' && !action.downloadStatus" style="color:#0190fe;"
              (click)="singleBtnAction(action.callbackfunction, action.actionLink, action.actionField, action.actionMode, action.selectionModel)">
              <i class="{{action.iconClass}} mr-1"></i>{{action.label}}</a>
            <a class="mr-3 {{action.cssClass}}" [ngClass]="{'disabled':action.disable}"
              *ngIf="action.type == 'single' && action.downloadStatus" style="color:#0190fe;"
              href="{{action.downloadLink}}" target="_blank" download="{{action.docMessage}}">
              <i class="{{action.iconClass}} mr-1"></i>{{action.label}}</a>
          </span>
        </div>
      </div>
      <div class="col-md-8"  *ngIf="metaData.enableQuickSearch == true">
        <div class="pull-right batch-action-search" *ngIf="rowModelType == 'clientSide'">
          <label>
            <input type="text" #search class="input-sm form-control search-input" placeholder="Search.."
              (keyup.enter)="searchFilterData(search.value)" [(ngModel)]="searchFieldText" />
            <button class="btn btn-primary btn-sm reset-btn" [disabled]="searchFieldText == ''"
              (click)="searchFilterData(search.value)">
              Search
            </button>
            <button class="btn btn-default btn-sm reset-btn" (click)="searchFilterData('')">
              Reset
            </button>
          </label>
        </div>
        <div class="pull-right"
          *ngIf="rowModelType == 'serverSide' && filterEnabledFields.length > 0">
          <label>Search:</label>
          <label class="button-group custom-search">
            <button type="button" class="btn btn-default-outline btn-sm dropdown-toggle"
              style="border-top-right-radius:0px;border-bottom-right-radius:0px;margin-bottom: 0px;"
              data-toggle="dropdown"><span class="fa fa-search"></span> <span class="caret"></span>
            </button>
            <ul class="dropdown-menu filter-worklist">
              <li class="" *ngFor="let field of filterEnabledFields;">
                <span (change)="checkboxSelection(field)">
                  <label>
                    <input class="form-check-input" type="checkbox"
                      id="checkbox{{field.fieldId}}" />&nbsp;{{field.headerName}}
                  </label>
                </span>
              </li>
            </ul>
          </label>
          <label>
            <input type="text"
              style="padding-top: .3rem;padding-bottom: .47rem;margin-left: -14px;border-bottom-left-radius: 0px;border-top-left-radius: 0px;border-color:#d8d8d8;"
              [(ngModel)]="searchFieldText" class="input-sm form-control" placeholder="Search"
              (keyup.enter)="searchBasedField(searchFieldText)" />
          </label>
          <label>
            <button class="btn btn-primary btn-sm reset-btn"
              [disabled]="searchFieldText == '' || selectedSearchFields.length == 0"
              (click)="searchBasedField(searchFieldText)" style="margin-bottom: 0px;">
              Search
            </button>
            <button class="btn btn-default btn-sm reset-btn" (click)="clearSearch()" style="margin-bottom: 0px;">
              Reset
            </button>
          </label>
        </div>
      </div>
    </div>



    <!-- search field-->
    <!-- <div class="row form-status-view" *ngIf="metaData.enableQuickSearch == true"> -->

    <!-- AG-GRID -->
    <div class="col-md-12 pah-ag-grid" id="grid-wrapper" *ngIf="worklistView=='grid'">
      <div id="newloader" *ngIf="showLoader">
        <div id="light" class="white_content full-loader" >
        <img style="width:50%;" src="./assets/modules/dummy-assets/common/img/spinner.svg" alt="loading">
        <div>
          <span id="textmessage" style="background: #7f7f7f;"></span>
        </div>
      </div>
      <div id="fade" class="black_overlay loader-overlay"></div></div>
      <ag-grid-angular *ngIf="rowModelType == 'clientSide'" style="width: 99%; height: 500px;" class="ag-theme-balham" [animateRows]="true"
        [suppressNoRowsOverlay]='suppressNoRow' [pagination]="pagination" [paginationPageSize]="paginationPageSize"
        [rowData]="rowData" [columnDefs]="columnDefs" [defaultColDef]="defaultColDef" [enableFilter]="true"
        [enableSorting]="true" (sortChanged)="sortChanged($event)" (bodyScroll)="onBodyScrollEvent($event)"
        [frameworkComponents]="frameworkComponents" (gridReady)="onGridReady($event)" [enableColResize]="true"
        [overlayLoadingTemplate]="overlayLoadingTemplate" [overlayNoRowsTemplate]="overlayNoRowsTemplate"
         (cellValueChanged)="onCellValueChanged($event)"
        (columnResized)="onColumnResized($event)" [icons]="icons" [enableRangeSelection]="true"
        [rowSelection]="rowSelection" [autoGroupColumnDef]="autoGroupColumnDef"
        [groupRowInnerRenderer]="groupRowInnerRenderer" [components]="components"
        [groupSelectsChildren]="groupSelectsChildren" [suppressRowClickSelection]="true"
        [rowGroupPanelShow]="rowGroupPanelShow" (gridSizeChanged)="onGridSizeChanged($event)"
        (selectionChanged)="onSelectionChanged($event)" [rowModelType]="rowModelType" [cacheBlockSize]="cacheBlockSize"
        [maxBlocksInCache]="maxBlocksInCache" [sideBar]="sideBar" [suppressCsvExport]="suppressCsvExport"
        [suppressExcelExport]="suppressExcelExport" [excelStyles]="excelStyles" >
      </ag-grid-angular>
      <ag-grid-angular *ngIf="rowModelType == 'serverSide'" style="width: 99%; height: 500px;" class="ag-theme-balham" [animateRows]="true"
        [suppressNoRowsOverlay]='suppressNoRow' [pagination]="pagination" [paginationPageSize]="paginationPageSize"
        [rowData]="rowData" [columnDefs]="columnDefs" [defaultColDef]="defaultColDef" [enableFilter]="true"
        [enableSorting]="true" (sortChanged)="sortChanged($event)" (bodyScroll)="onBodyScrollEvent($event)"
        [frameworkComponents]="frameworkComponents" (gridReady)="onGridReady($event)" [enableColResize]="true"
        [overlayLoadingTemplate]="overlayLoadingTemplate" [overlayNoRowsTemplate]="overlayNoRowsTemplate"
         (cellValueChanged)="onCellValueChanged($event)"
        (columnResized)="onColumnResized($event)" [icons]="icons" [enableRangeSelection]="true"
        [rowSelection]="rowSelection" [autoGroupColumnDef]="autoGroupColumnDef"
        [groupRowInnerRenderer]="groupRowInnerRenderer" [components]="components"
        [groupSelectsChildren]="groupSelectsChildren" [suppressRowClickSelection]="true"
        [rowGroupPanelShow]="rowGroupPanelShow" (gridSizeChanged)="onGridSizeChanged($event)"
        (selectionChanged)="onSelectionChanged($event)" [rowModelType]="rowModelType" [cacheBlockSize]="cacheBlockSize"
        [sideBar]="sideBar" [suppressCsvExport]="suppressCsvExport"
        [suppressExcelExport]="suppressExcelExport" [excelStyles]="excelStyles" >
      </ag-grid-angular>
      <!-- <span  style="color:teal;text-align: center;padding:20px 20px;"> There are no data available. </span> -->
    </div>
  </ng-container>
  <div *ngIf="worklistView=='form'">
    <div class="wait-loading" *ngIf="dataLoading && !reloading">
      <img src="assets/img/loader/loading.gif" />
    </div>
    <div class="col-md-12" *ngIf="!dataLoading">
      <app-liaison-entry #laisionEntry *ngIf="entryDetailsData!=null" [worklistEditData]="entryDetailsData"
        [entryFormType]="entryFormType" [form]="editLiaisonEntryForm">
      </app-liaison-entry>
    </div>
  </div>

  <!-- Therapy Modal -->
  <div class="modal therapy-modal" id="therapyModal" data-backdrop="static" tabindex="-1" role="dialog"
    aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
      <div class="modal-content" [ngClass]="_intakeService.enableSwal == true ? 'disable-modal' : ''">
        <div class="modal-header">
          <h4 class="modal-title" id="exampleModalLabel">Edit Therapy</h4>
          <button title="Close" type="button" class="close" data-dismiss="modal" aria-label="Close"
            (click)="closeTherapyModal()">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body">
          <div class="row p-2">
            <form class="row" [formGroup]="therapyForm" #th="ngForm" style="width:100%;">
              <div class="col-md-3">
                <select formControlName="therapy" class="form-control pointer" 
                  [ngClass]="(therapyForm.controls['therapy'].errors && (therapyForm.controls['therapy']?.dirty || therapyForm.controls['therapy']?.touched || submitted))?'red-border-class':'no-border-class'">
                  <option value="">Select Therapy</option>
                  <option *ngFor="let opt of therapyList" value="{{opt.id}}" [selected]="opt==Therapy">{{opt.name}}
                  </option>
                </select>
                <div *ngIf="exist" class="alert alert-danger">
                  Therapy already exist
                </div>
              </div>
              <div class="col-md-3">
                <select formControlName="moa" class="form-control pointer"
                  [ngClass]="(therapyForm.controls['moa'].errors && (therapyForm.controls['moa']?.dirty || therapyForm.controls['moa']?.touched || submitted))?'red-border-class':'no-border-class'">
                  <option value="">Select MOA</option>
                  <option *ngFor="let opt of moaList" value="{{opt.id}}" [selected]="opt==moa">{{opt.name}}
                  </option>
                </select>
              </div>
              <div class="col-md-3">
                <input type="text" placeholder="Drug,Dose & Freq." formControlName="drug" class="form-control"
                  [ngClass]="(therapyForm.controls['drug'].errors && (therapyForm.controls['drug']?.dirty || therapyForm.controls['drug']?.touched || submitted))?'red-border-class':'no-border-class'" />
              </div>
              <div class="col-md-3">
                <button class="btn btn-primary btn-sm mt-1 pull-right" style="margin:-10px"
                  (click)="cancelTherapy();">Cancel</button>
                <button class="btn btn-primary btn-sm mt-1 pull-right" style="margin:0px 13px"
                  (click)="addTherapy(th);submitted = true">Update</button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
    <!-- Edoc Modal -->
    <div class="modal edoc-modal" id="edocModal" data-backdrop="static" tabindex="-1" role="dialog"
    aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" style="max-width:1024px;">
      <div class="modal-content" [ngClass]="_intakeService.enableSwal == true ? 'disable-modal' : ''">
        <div class="modal-header">
          <h4 class="modal-title" id="exampleModalLabel">View eDocs</h4>
          <button title="Close" type="button" class="close" data-dismiss="modal" aria-label="Close"
            (click)="closeEdocModal()">
            <span aria-hidden="true">&times;</span>
          </button>
        </div>
        <div class="modal-body" *ngIf= "url != ''">
          <div *ngIf="fileExt == 'image'">
            <img src="{{url}}" class="responsive" width="600" height="400">
          </div>
          <div *ngIf="fileExt != 'image'">
          <ngx-doc-viewer url="{{url}}" viewer="url" style="width:100%;height:93vh;">
          </ngx-doc-viewer>
        </div>
        </div>
      </div>
    </div>
  </div>
</div>