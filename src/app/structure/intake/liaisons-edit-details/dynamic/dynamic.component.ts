import { Component, OnInit, Input, Output, EventEmitter, SimpleChange } from '@angular/core';
import { WorkListService } from '../../../worklists/worklist.service';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { IconCellRenderer } from '../../renderer/icon-cell-renderer.component';
import { ContentCellRenderer } from '../../renderer/content-cell-renderer.component';
import { PopupCellRenderer } from '../../renderer/popup-cell-renderer.component';
import { Router } from '@angular/router';
import { StructureService } from '../../../structure.service';
import { SharedService } from '../../../shared/sharedServices';
import {LicenseManager} from "ag-grid-enterprise";
import 'ag-grid-enterprise';
import 'ag-grid-angular';
import { IntakeService } from '../../intake.service';
import { IndexedDbService } from '../../indexed-db.service';
import { IntakeWorkListService } from '../../intake-worklist/intake-worklist.service';
import { ConstantService } from '../../constant.service';
import { ToolTipService } from '../../../tool-tip.service';
var jstz = require('jstz');
import { HostListener } from '@angular/core';
var momentTz = require('moment-timezone');
import { isBlank } from 'app/utils/utils';
declare var moment: any;
declare var swal: any;
declare var $: any;
declare var NProgress: any;
const timezone = jstz.determine();
import { NGXLogger } from 'ngx-logger';
@Component({
  selector: 'app-dynamic',
  templateUrl: './dynamic.component.html',
  styleUrls: ['./dynamic.component.css']
})

export class DynamicComponent implements OnInit {

  @Input('dynamicData') dynamicData: any;
  @Input('parentWorklistName') parentWorklistName: any;
  @Input('lockConfig') lockConfig: any;
  @Input('appLock') appLock: any;
  @Input('fromDashboard') fromDashboard: any;
  @Input('parentWorklistId') parentWorklistId: any;
  @Input('workflowId') workflowId: any;
  @Input('workflowType') workflowType: any;

  //Input to Get Variables from Parent Component
  @Input('refreshTherapy') refreshTherapy;
  @Input('userDetails') userDetails;
  @Input('userPatientId') userPatientId;
  @Input('worklistEditData') worklistEditData: any;
  @Output() referalDetails: EventEmitter<any> = new EventEmitter<any>();
  @Output() reloadTabData: EventEmitter<any> = new EventEmitter<any>();
  @Output() changeStatus: EventEmitter<any> = new EventEmitter<any>();
  @Output() changeBtnStatus: EventEmitter<any> = new EventEmitter<any>();
  @Output() insurancePolicy: EventEmitter<any> = new EventEmitter<any>();
  @Output() worklistStatus: EventEmitter<any> = new EventEmitter<any>();
  @Output() integrationDetails: EventEmitter<any> = new EventEmitter<any>();
  gridApi;
  gridColumnApi;
  overlayLoadingTemplate;
  overlayNoRowsTemplate;
  appName: string;
  blogNoteText: any;
  progressNoteText: any;
  userDet;
  userId;
  requestData;
  parameters;
  displayname;
  selectedRowDetails;
  metaData;
  value;
  optionShowButton;
  Default;
  isDetail = false;
  filterField;
  filterCheck;
  cellClickFn;
  rowGroupPanelShow;
  rowData: any = [];
  columnDefs: any = [];
  icons;
  frameworkComponents: any;
  dashboardWidgets: any;
  customFields: any;
  reportFields: any;
  actionFields: any;
  defaultColDef: any;
  formRowData = [];
  formId: number;
  worklistDetails: any;
  formDataList = [];
  heading: string;
  showTooltip = false;
  responseStatus;
  showResponse = false;
  machFormIds = [];
  machFormKeys = [];
  worklistName: string;
  patientId;
  worklistid: any;
  dataLoadingMsg: boolean;
  structureFormContent: any = '';
  uniqueClass = '';
  showAll = false;
  showLess = true;
  hasUniqueId;
  singleActionButtons = [];
  singleRowAllActions = [];
  singleRowActions = [];
  multipleActionButtons = [];
  batchSingleActionButtons = [];
  batchMultipleActionButtons = [];
  autoGroupColumnDef;
  groupRowInnerRenderer;
  components;
  sideBar: any;
  list = [];
  refillDateType = 'due';
  shortLinkActions = [];
  rowModelType;
  cacheBlockSize;
  maxBlocksInCache;
  pagination = true;
  paginationPageSize = 0;
  btnBehaviour = true;
  hideActionButtons = true;
  rowCount = 0;
  userData = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
  actionColumnPosition = '';
  formFieldFrom = '';
  noteData: any;
  rowSelection = '';
  showEdit = false;
  formField;
  hideBulkEdit = true;
  bulkEditFields = [];
  questions: any[];
  form: FormGroup;
  enableBulkEdit = false;
  fieldValueList = [];
  updateProperty = '';
  formFiles = [];
  fileContent;
  selectName;
  showNoData = false;
  mapFields = [];
  linkFields = [];
  datalist: any = [];
  totalRowData = [];
  disableWidget = true;
  filterText = '';
  previlages;
  suppressExcelExport = false;
  suppressCsvExport = false;
  previousHideColumns = [];
  hideColumns = [];
  showCustomExport = false;
  excelStyles;
  columnWidth;
  totalCount = 0;
  groupSelectsChildren;
  filterLabel = 'All';
  filterEnabledFields: any =[];
  selectedSearchFields = [];
  customSearch = false;
  searchFieldText = '';
  selectedWidgetField = '';
  selectedWidget = '';
  worklistView;
  entryDetailsData = [];
  worklistEditDataValue;
  entryFormType = '';
  worklistCallbackForm;
  dataLoading: Boolean;
  reloading: Boolean;
  editLiaisonEntryForm: FormGroup;
  therapyForm;
  therapyList = [];
  moaList = [];
  submitted = false;
  exist = false;
  editTherapyId;
  therapyId;
  therapyName;
  moaName;
  worklistHeading;
  checkListEntryType;
  checkListCreatedBy;
  checkListId;
  suppressNoRow = true;
  disableCustomField = true;
  checkListStatus;
  checklistStatusIndicator;
  associatedParentWorklists = [];
  customTextboxValue = '';
  recheckReason = [];
  therapyCompleteStatus = false;
  therapyReasonId;
  therapyReason;
  therapyRecheckStatus = false;
  therapyRecheckReasonValue;
  workflowTypeName;
  url;
  fileExt;
  noteType;
  pageList = [];
  pageSize;
  userDataConfig ;
  integrationSettings;
  showLoader = false;
  constructor(
    private _intakeWorklistService: IntakeWorkListService,
    private _workListService: WorkListService,
    private router: Router,
    private _structureService: StructureService,
    private _sharedService: SharedService,
    private _formBuild: FormBuilder,
    public _intakeService: IntakeService,
    public _constantService: ConstantService,
    private _indexedDbService: IndexedDbService,
    private ngxLogger: NGXLogger,
    private toolTipService: ToolTipService) {
    // Set License key for ag grid enterprose version.
    let agGridLicenseKey = !isBlank(this._structureService.agGridLicenceKey) ? atob(this._structureService.agGridLicenceKey) : this._structureService.agGridLicenceKey;
    if (agGridLicenseKey) {
      LicenseManager.setLicenseKey(agGridLicenseKey);
    }
  
    /**ag-grid loading message */
    this.overlayLoadingTemplate =
      '<span class="ag-overlay-loading-center">Please wait while we process your request</span>';
    this.overlayNoRowsTemplate = "<span>No data available to show.</span>";
    this.frameworkComponents = {
      iconCellRenderer: IconCellRenderer,
      contentCellRenderer: ContentCellRenderer,
      popupCellRenderer: PopupCellRenderer
    };
    this.groupRowInnerRenderer = "groupRowInnerRenderer";
    /**Default configuration of column */
    this.defaultColDef = {
      resizable: true,
      enableValue: true,
      enableRowGroup: true,
      headerClass: 'multiline',
      headerComponentParams: {
        template:
          '<div class="ag-cell-label-container" role="presentation">' +
          '  <span ref="eMenu" class="ag-header-icon ag-header-cell-menu-button"></span>' +
          '  <div ref="eLabel" class="ag-header-cell-label" role="presentation">' +
          '    <span ref="eSortOrder" class="ag-header-icon ag-sort-order" ></span>' +
          '    <span ref="eSortAsc" class="ag-header-icon ag-sort-ascending-icon" ></span>' +
          '    <span ref="eSortDesc" class="ag-header-icon ag-sort-descending-icon" ></span>' +
          '    <span ref="eSortNone" class="ag-header-icon ag-sort-none-icon" ></span>' +
          '    <span ref="eText" class="ag-header-cell-text" role="columnheader" ' +
          '    style="font-family: PT Sans, sans-serif;font-size: 1.02rem;' +
          '    font-weight: bold;color:#514d6a"></span>' +
          '    <span ref="eFilter" class="ag-header-icon ag-filter-icon"></span>' +
          '  </div>' +
          '</div>'
      },
      autoHeight: true
    };

    this.autoGroupColumnDef = {
      headerName: 'Group',
      width: 200,
      cellRenderer: "agGroupCellRenderer",
      cellRendererParams: { checkbox: true }
    };
    this.components = { datePicker: this.getDatePicker() };
    this.editLiaisonEntryForm = this._formBuild.group({
      id: [''],
      roomNumber: [''],
      locationId: [''],
      nextFollowup: ['null'],
      insCoordinator: [''],
      teachComplete: ['null'],
      teachReason: [''],
      hospitalDischarge: ['null'],
      hospitalChart: ['null'],
      timeCommitmentCalled: ['null'],
      vnaVerified: ['null'],
      liaisonComplete: ['null'],
      hospitalDischargeApproved: ['null'],
      timeCommitment: [''],
      insurancePolicy: [''],
      pharmacyReviewComplete: [''],
      rph_hour: [''],
      rph_minute: [''],
      tcRequested: [''],
      deliveryRequested: [''],
      noMixOrDeliveryRequired: [{value: ''}],
      excludedFromTcCutoffTime: [{value: ''}]
    });
  }
  setheight(event) {
    try {
      if (event.data) {
        if (event.data["AllowEditFormSubmitted"] && event.data["AllowEditFormSubmitted"] == true) {
          var datasourcee = ServerSideDatasourceApi(this, '');
          this.gridApi.setServerSideDatasource(datasourcee);
          this.gridApi.deselectAll();
          window.removeEventListener("message", this.setheight);
          this._structureService.pahEdit = false;
          this.showEdit = false;
        }
      }
    } catch (error) {
    }
  }
  @HostListener('click', ['$event'])
    onClick(event) {
        if ($(event.target).data('toggle') !== 'popover-status') { 
            $('[data-toggle="popover-status"]').popover('hide');
        }
    }
  // Onclick each Patient from left Menu Change all data in the page 
  ngOnChanges(changes: { [propKey: string]: SimpleChange }) {
    if (changes['dynamicData']) {
      this.suppressNoRow = true;
      this.showNoData = false;
      this.userDet = this.userDetails;
      this.userId = this.userPatientId;
      this.worklistName = this.dynamicData.tabName;
      this.patientId = this.dynamicData.patientId;
      this.worklistid = this.dynamicData.pahworklistId;
      this.optionShowButton = 'All';
      this.hideActionButtons = true;
      this.metaData = this.dynamicData.filterLiaisonsDetails[0];
      let metaData = this.metaData;
      this.worklistView = metaData.worklistView;
      this.worklistCallbackForm = metaData.callbackFunctionforForm;
      let worklistIdentifier = metaData.callbackFunctionName;
      if (worklistIdentifier == 'intakePaperWork') {
        this.worklistHeading = 'Intake Follow Up Paperwork Complete';
      } else if (worklistIdentifier == 'insuranceVerification') {
        this.worklistHeading = 'Intake Follow Up Insurance Verification Paperwork';
      } else if (worklistIdentifier == 'finalInsuranceVerification') {
        this.worklistHeading = 'Intake Follow Up Final Insurance Verification';
      } else if (worklistIdentifier == 'intakeFinalReview') {
        this.worklistHeading = 'Intake Follow Up Final Review';
      } else if (worklistIdentifier == 'insuranceVerificationItems' || worklistIdentifier == 'initialInsuranceVerificationItems') {
        this.worklistHeading = 'Insurance Verification Items';
      } else {
        this.worklistHeading = '';
      }
      if (this.worklistView == 'form') {
        this.showEntryForm(this.worklistCallbackForm);
      }
      if (this.dynamicData['loading'] && this.dynamicData['loading'] == 'refresh') {
        this.dataLoading = false;
        this.reloading = true;
      } else {
        this.dataLoading = true;
        this.reloading = false;
      }
      const index = this._structureService.activePatientActivityDetails.findIndex(x => x.tabId == this.dynamicData.pahworklistId);
      this.cacheBlockSize = metaData.InitCountVal;
      this.maxBlocksInCache = metaData.blocksize;
      this.paginationPageSize = Number(metaData.pageSize);
      if (index != -1 && metaData.rowModel != 'server' && this.fromDashboard == 'false') {
        this.getDefaultGrid();
        if (this.rowData && this.rowData.length == 0) {
          this.showNoData = true;
        }
      } else {
        if (this.dynamicData && this.dynamicData.tabName) {
          this.dataLoadingMsg = true;
          this.worklistName = this.dynamicData.tabName;
          this.worklistid = this.dynamicData.pahworklistId;
          this.dataLoadingMsg = true;
          if (metaData.rowModel == 'server') {
            this.rowModelType = "serverSide";
            if (metaData.dataLoadingType && metaData.dataLoadingType == 'pagination') {
              this.pagination = true;
              this.paginationPageSize = Number(metaData.pageSize);
            } else {
              this.pagination = false;
            }
          } else {
            this._structureService.activePatientActivityDetails.push({
              'tabId': this.dynamicData.pahworklistId
            });
            this.rowModelType = "clientSide";
          }
          if (this.dynamicData['loading'] && this.dynamicData['loading'] == 'refresh') {
            if (this.gridApi) {
              this.gridApi.showLoadingOverlay();
            }
            this.getformData();
          } else {
            this.getWorklistdetails();
          }
        }
      }
      if (this.parentWorklistName == 'intake-followup') {
        this.workflowTypeName = 'INTAKE';
      } else if (this.parentWorklistName == 'liaisons') {
        this.workflowTypeName = 'LIAISON';
      } else if (this.parentWorklistName == 'initial-insurance-verification') {
        this.workflowTypeName = 'INITIAL_INS';
      }
    }
    if (this.refreshTherapy == true) {
      this.getformData();
      this.refreshTherapy = false;
    }
    if (this.worklistEditData) {
      if (this.worklistEditData.therapyStatus == 1) {
        this.therapyCompleteStatus = true;
      }
      if (this.worklistEditData.therapyRecheck == true && this.worklistEditData.therapyRecheckReason != null && this.worklistEditData.therapyRecheckReasonLovId != null) {
        this.therapyRecheckStatus = true;
        this.therapyRecheckReasonValue = this.worklistEditData.therapyRecheckReason;
      } else {
        this.therapyRecheckStatus = false;
      }
      /**Get insurance policy value */
      this.customTextboxValue = this.worklistEditData.insurancePolicy != null ? this.worklistEditData.insurancePolicy : '';
    }
  }
  ngOnInit() {
    window.addEventListener("message", this.setheight.bind(this));
    this.displayname = this._structureService.getCookie('displayname');
    /**For checking whether add cancel form action or not */
    this.previlages = [];
    let manageTenants = this.userData.privileges;
    manageTenants = typeof (manageTenants) === 'undefined' ? this._structureService.getCookie('userPrivileges') : manageTenants;
    manageTenants = manageTenants.split(',');
    for (var i = 0; i < manageTenants.length; i++) {
      this.previlages[manageTenants[i]] = true;
    }
    this.excelStyles = [
      {
        id: "header",
        interior: {
          color: "#CCCCCC",
          pattern: "Solid"
        }
      }
    ];
    this.therapyForm = this._formBuild.group({
      therapy: ['', [Validators.required]],
      moa: ['', [Validators.required]],
      drug: ['', [Validators.required]],
    });

  }
  editTherapy(e) {
    $('#therapyModal').modal('show');
    this.therapyId = e.rowData['therapy'].id;
    this.editTherapyId = e.rowData.id;
    let index;
    index = this.rowData.findIndex(x => x.id == e.rowData.id);
    this.therapyForm.controls['therapy'].setValue(this.rowData[index]['therapy'].id);
    this.therapyForm.controls['moa'].setValue(this.rowData[index]['moa'].id);
    this.therapyForm.controls['drug'].setValue(this.rowData[index]['drug']);
  }
  onChange(value) {
    if (value != this.therapyId) {
      if (this.rowData && this.rowData.length) {
        for (var i = 0; i < this.rowData.length; i++) {
          if (this.rowData[i].therapy['id'] == value) {
            this.exist = true;
            break;
          } else {
            this.exist = false;
          }
        }
      }
    } else {
      this.exist = false;
    }
  }
  cancelTherapy() {
    $('#therapyModal').modal('hide');
    this.submitted = false;
  }
  closeTherapyModal() {
    $('#therapyModal').modal('hide');
  }
  addTherapy(f) {
    let index;
    const dataset = [];
    const dataObject = {};
    dataObject['id'] = this.editTherapyId;
    dataObject['patientId'] = this.patientId;
    dataObject['therapyId'] = f.value['therapy'];
    dataObject['moaId'] = f.value['moa'];
    dataObject['drug'] = f.value['drug'];
    dataObject['createdBy'] = this.userData.userId;
    dataObject['modifiedBy'] = this.userData.userId;
    dataset.push(dataObject);
    let datasetMod = { dataset, 'workflowType': this.workflowTypeName }
    this._intakeService.createTherapyData(datasetMod).subscribe((data) => {
      if (data.data['UserTherapiesCreate'].status == 201) {
        $('#therapyModal').modal('hide');
        this.therapyList.forEach(element => {
          if (f.value['therapy'] == element.id) {
            this.therapyName = element.name;
          }
          this.moaList.forEach(element => {
            if (f.value['moa'] == element.id) {
              this.moaName = element.name;
            }
            if (this.rowData && this.rowData.length) {
              index = this.rowData.findIndex(x => x.id == this.editTherapyId);
              let params = JSON.parse(JSON.stringify(this.rowData));
              params[index].id = this.editTherapyId;
              params[index].patientId = this.patientId;
              params[index].therapy = { id: f.value['therapy'], name: this.therapyName };
              params[index].moa = { id: f.value['moa'], name: this.moaName };
              params[index].drug = f.value['drug'];
              this.rowData = params;
            }
          });
        });
        this.hideActionButtons = true;
        var notify = $.notify('Success! Therapy Details Updated');
        setTimeout(function () {
          notify.update({ 'type': 'success', 'message': '<strong>  Success! Therapy Details Updated. </strong>' });
        }, 1000);
        this.getformData()
      }
    });
  }
  deleteTherapy(e) {
    swal({
      title: "Are you sure?",
      text: "You are going to delete this therapy entry " + e.rowData['therapy'].name,
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Ok",
      closeOnConfirm: true
    }, () => {
      this._intakeService.deleteTherapyData(e.rowData.id).subscribe((data) => {
        if (data.data['UserTherapiesDelete'].status == 201) {
          var notify = $.notify('Success ! Therapy Deleted');
          setTimeout(function () {
            notify.update({ 'type': 'success', 'message': '<strong>Success ! Therapy Deleted</strong>' });
          }, 1000);
          const filterList = this.rowData.filter(x => Number(x.id) !== Number(e.rowData.id));
          this.rowData = filterList;
          this.hideActionButtons = true;
          if (this.rowData && this.rowData.length == 0) {
            this.showNoData = true;
          }
          this.getformData()
        }
      });
    });
  }
  deleteEdoc(e) {
    swal({
      title: "Are you sure?",
      text: "You are going to delete this entry from eDocs",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Ok",
      closeOnConfirm: true
    }, () => {
      this._intakeService.deleteEdocData(e.rowData.id).subscribe((data) => {
        var notify = $.notify('Success ! eDoc Deleted');
        setTimeout(function () {
          notify.update({ 'type': 'success', 'message': '<strong>Success ! eDoc Deleted</strong>' });
        }, 1000);
        const filterList = this.rowData.filter(x => Number(x.id) !== Number(e.rowData.id));
        this.rowData = filterList;
        this.hideActionButtons = true;
        if (this.rowData && this.rowData.length == 0) {
          this.showNoData = true;
        }
      });
    });
  }
  viewEdoc(e) {
    this.fileExt = e.rowData.documentCategory;
    var s = e.rowData.filePath;
    s = s.substring(0, s.indexOf('?'));
    this.url = s;
    if (this.fileExt !== 'document') {
      $('#edocModal').modal('show');
    }
  }
  closeEdocModal() {
    $('#edocModal').modal('hide');
    this.url = '';
  }
  onCellValueChanged(e) {
    this.showHideCustomField(true);
    let items = 0;
    let this1 = this;
    this.gridApi.forEachNode(function (rowNode, index) {
      if (this1.metaData.callbackFunctionName == 'finalInsuranceVerification' && this1.workflowId == this1._constantService.entryTypeIds['INS_VERIFICATION']) {
        if (rowNode.data.insuranceFollowupEntry == true || (rowNode.data.insuranceFollowupEntry != true && rowNode.data.insuranceFollowupEntry != '' && rowNode.data.insuranceFollowupEntry != false)) {
          items++;
        }
      } else if (this1.metaData.callbackFunctionName == 'insuranceVerificationItems' && this1.checkListStatus == 'NEW') {
        if (rowNode.data.initialInsuranceEntry == true || rowNode.data.itemValueComplete == true || (rowNode.data.itemValueComplete != true && rowNode.data.itemValueComplete != '' && rowNode.data.itemValueComplete != false) || (rowNode.data.initialInsuranceEntry != true && rowNode.data.initialInsuranceEntry != '' && rowNode.data.initialInsuranceEntry != false)) {
          items++;
        }
      } else {
        if (rowNode.data.itemValueComplete == true || rowNode.data.itemValueRequired == true || (rowNode.data.itemValueComplete != true && rowNode.data.itemValueComplete != '' && rowNode.data.itemValueComplete != false) || (rowNode.data.itemValueRequired != true && rowNode.data.itemValueRequired != '' && rowNode.data.itemValueRequired != false)) {
          items++;
        }
      }
    });
    if (items > 0) {
      this.changeBtnStatus.emit(false);
    } else {
      this.changeBtnStatus.emit(true);
    }
  }
  showHideCustomField(change) {
    if (this.metaData.callbackFunctionName == 'intakePaperWork') {
      let checkedCount = this.rowData.filter(x => x.itemValueComplete == true).length;
      this.disableCustomField = (checkedCount > 0 && this.checkListStatus != 'COMPLETED') ? false : true;
    } else if (this.metaData.callbackFunctionName == 'insuranceVerification') {
      if (this.workflowId == this._constantService.entryTypeIds['INTAKE'] || this.workflowId == this._constantService.entryTypeIds['INS_VERIFICATION']) {
        this.disableCustomField = true;
      } else {
        let checkedCount = this.rowData.filter(x => x.itemValueComplete == true).length;
        this.disableCustomField = (checkedCount > 0 && this.checkListStatus != 'COMPLETED') ? false : true;
      }
    } else if (this.metaData.callbackFunctionName == 'initialInsuranceVerificationItems') {
      let checkedCount = this.rowData.filter(x => x.itemValueRequired == true).length;
      this.disableCustomField = (checkedCount > 0 && this.checkListStatus != 'COMPLETED') ? false : true;
    } else if (this.metaData.callbackFunctionName == 'miscellaneous' || this.metaData.callbackFunctionName == 'insuranceVerificationItems') {
      let count = 0;
      let this1 = this;
      let noneRequired = 0;
      let requiredCount = 0;
      let completeCount = 0;
      if (this.metaData.callbackFunctionName == 'insuranceVerificationItems' && this.checkListStatus == 'NEW') {
        requiredCount = this.rowData.filter(x => (x.initialInsuranceEntry == true || x.initialInsuranceEntry == 1)).length;
      } else {
        requiredCount = this.rowData.filter(x => (x.itemValueRequired == true || x.itemValueRequired == 1)).length;
      }
      completeCount = this.rowData.filter(x => (x.itemValueComplete == true || x.itemValueComplete == 1)).length;
      if (change == false) {
        if (this.metaData.callbackFunctionName == 'insuranceVerificationItems' && this.checkListStatus == 'NEW') {
          count = this.rowData.filter(x => (x.initialInsuranceEntry == true || x.initialInsuranceEntry == 1) && (x.itemValueComplete == true || x.itemValueComplete == 1)).length;
          if (this.rowData.filter(x => (x.noneRequired == true && x.initialInsuranceEntry == true)).length > 0) {
            noneRequired = 1;
          }
        } else {
          count = this.rowData.filter(x => (x.itemValueRequired == 1 || x.itemValueRequired == true) && (x.itemValueComplete == true || x.itemValueComplete == 1)).length;
          if (this.rowData.filter(x => (x.noneRequired == true && x.itemValueRequired == true)).length > 0) {
            noneRequired = 1;
          }
        }

      }
      if (change == true) {
        if (this.metaData.callbackFunctionName == 'insuranceVerificationItems' && this.checkListStatus == 'NEW') {
          this.gridApi.forEachNode(function (rowNode, index) {
            if ((rowNode.data.initialInsuranceEntry == true || rowNode.data.initialInsuranceEntry == 1) && (rowNode.data.itemValueComplete == true || rowNode.data.itemValueComplete == 1)) {
              count++;
            }
            if (rowNode.data.noneRequired == true && rowNode.data.initialInsuranceEntry == true) {
              noneRequired = 1;
            }
          });
        } else {
          this.gridApi.forEachNode(function (rowNode, index) {
            if ((rowNode.data.itemValueRequired == 1 || rowNode.data.itemValueRequired == true) && (rowNode.data.itemValueComplete == true || rowNode.data.itemValueComplete == 1)) {
              count++;
            }
            if (rowNode.data.noneRequired == true && rowNode.data.itemValueRequired == true) {
              noneRequired = 1;
            }
          });
        }

      }
      if (count == requiredCount && this.checkListStatus != 'COMPLETED' && completeCount != 0) {
        this.disableCustomField = false;
      } else if (noneRequired == 1 && this.checkListStatus != 'COMPLETED') {
        this.disableCustomField = false;
      } else {
        this.disableCustomField = true;
      }
    }
  }
  sortChanged(event) {
    autosizeHeaders(event);
  }
  onBodyScrollEvent(event) {
    if (event.direction == 'horizontal') {
      autosizeHeaders(event);
    }
  }
  onColumnResized(event) {
    autosizeHeaders(event);
    if (event.finished) {
      this.gridApi.resetRowHeights();
    }
  }
  /**For make the ag grid responsive */
  onGridSizeChanged(params) {
    var gridWidth = document.getElementById("grid-wrapper").offsetWidth;
    var columnsToShow = [];
    var columnsToHide = [];
    var totalColsWidth = 0;
    var allColumns = params.columnApi.getAllColumns();
    for (var i = 0; i < allColumns.length; i++) {
      let column = allColumns[i];
      totalColsWidth += column.getMinWidth();
      if (totalColsWidth > gridWidth) {
        columnsToHide.push(column.colId);
      } else {
        columnsToShow.push(column.colId);
      }
    }
    params.api.sizeColumnsToFit();
  }

  /**Grid ready function */
  onGridReady(params) {
    autosizeHeaders(params);
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.showLoadingOverlay();
    setTimeout(function () {
      params.api.resetRowHeights();
    }, 500);
    window.addEventListener('resize', function () {
      setTimeout(function () {
        //params.api.sizeColumnsToFit();
        params.api.resetRowHeights();
      });
    });
    let self = this;
    setTimeout(function () {
      self.reportFields.forEach(element => {
        if (element.clearFilter == true) {
          let filterInstance = self.gridApi.getFilterInstance(element.fieldName);
          if (filterInstance) {
            filterInstance.eClearButton.addEventListener("click", () => {
              self.gridApi.onFilterChanged();
            });
          }
        }
      });
    }, 5000);
    this.gridApi.setHeaderHeight(35);
    this.gridApi.setColumnDefs(this.columnDefs);
    this.onGridSizeChanged(params);
    this.getformData();
    //this.gridApi.sizeColumnsToFit();
    if (this.rowData.length > 0) {
      //this.gridApi.hideOverlay();
    }
  }
  /**Get worklist details from cache */
	async getCacheData(worklistid) {
    if (this._intakeWorklistService.browserCache == 'localstorage') {
      if (localStorage.getItem('worklistDetails') && localStorage.getItem('worklistDetails') != '') {
        let details = JSON.parse(localStorage.getItem('worklistDetails')).filter(x => Number(x.worklistId) == Number(worklistid) && x.edited == false);
        return details.length > 0 ? details[0].worklistDetails : [];
      } else {
        return [];
      }
    } else if (this._intakeWorklistService.browserCache == 'indexdb') {
      let dataFromIdb: any = [];
      await this._indexedDbService.getByKeyFromIDb(this._intakeWorklistService.indexDbConfig).then(
      (response) => {
        dataFromIdb = response;
      });
      if (dataFromIdb && !dataFromIdb.length) {
        return [];
      } else {
        let details = dataFromIdb.filter(x => Number(x.worklistId) == Number(worklistid) && x.edited == false);
        return details.length > 0 ? details[0].worklistDetails : [];
      }
    } else if(this._intakeWorklistService.browserCache == 'sharedvariable') {
      const defIndex = this._sharedService.worklistDetails.findIndex(x => x.worklistId == this.worklistid && x.edited == false);
      if (defIndex == -1) {
        return [];
      } else {
        return this._sharedService.worklistDetails[defIndex].worklistDetails;
      }
    }
  }
    /**Set worklist details to the cache */
  setCacheData(worklistid, worklistDetails) {
    var promise = new Promise((resolve, reject) => {
      let worklistData = [{ 'worklistId': worklistid, 'worklistDetails': worklistDetails, 'edited': false }];
      if (this._intakeWorklistService.browserCache == 'localstorage') {
      if (localStorage.getItem('worklistDetails') && localStorage.getItem('worklistDetails') != '') {
        let details = JSON.parse(localStorage.getItem('worklistDetails'));
        let index = details.findIndex(x=> x.worklistId == worklistid);
        if(index != -1) {
        details[index].worklistDetails = worklistDetails;
        details[index].edited = false;
        } else {
        details.push({ 'worklistId': worklistid, 'worklistDetails': worklistDetails, 'edited': false });
        }
        localStorage.setItem('worklistDetails', JSON.stringify(details));
      } else {
        localStorage.setItem('worklistDetails', JSON.stringify(worklistData));
      }
      } else if (this._intakeWorklistService.browserCache == 'indexdb') {
      let dataFromIdb: any = [];
      this._indexedDbService.getByKeyFromIDb(this._intakeWorklistService.indexDbConfig).then(
        (response) => {
        dataFromIdb = response;
        if (dataFromIdb && !dataFromIdb.length) {
          this._indexedDbService.addToIDb(this._intakeWorklistService.indexDbConfig, worklistData);
        } else {
          let details = dataFromIdb;
          let index = details.findIndex(x=> x.worklistId == worklistid);
          if(index != -1) {
          details[index].worklistDetails = worklistDetails;
          details[index].edited = false;
          } else {
          details.push({ 'worklistId': worklistid, 'worklistDetails': worklistDetails, 'edited': false });
          }
          this._indexedDbService.addToIDb(this._intakeWorklistService.indexDbConfig, details);
        }
        });
      } else if(this._intakeWorklistService.browserCache == 'sharedvariable') {
      const defIndex = this._sharedService.worklistDetails.findIndex(x => x.worklistId == worklistid);
      if (defIndex == -1) {
        this._sharedService.worklistDetails.push({ 'worklistId': worklistid, 'worklistDetails': worklistDetails, 'edited': false });
      } else {
        this._sharedService.worklistDetails[defIndex].worklistId = worklistid;
        this._sharedService.worklistDetails[defIndex].worklistDetails = worklistDetails;
      }
      }
      resolve();
    });
    return promise;
  }
  /**Get the configuration details of worklist and build ag grid */
  getWorklistdetails() {
    this.columnDefs = [];
    this.heading = '';
    this.formId = 0;
    this.dashboardWidgets = [];
    this.filterField = '';
    this.selectName = '';
    //this.rowData = [];
    //this.addNewBtnLink = '';
    this.uniqueClass = '';
    this.batchMultipleActionButtons = [];
    this.actionFields = [];
    this.batchSingleActionButtons = [];
    this.singleRowActions = [];
    this.singleRowAllActions = [];
    this.btnBehaviour = true;
    this.hideActionButtons = true;
    this.getCacheData(this.worklistid).then((data) => {
		this.worklistDetails = data;
			if (this.worklistDetails.length == 0) {
			  this._workListService.getWorklistDetails(this.worklistid).then((data) => {
				if (data['getSessionTenant']) {
				  this.worklistDetails = data['getSessionTenant'].formWorklists;
				  this.setCacheData(this.worklistDetails[0].id, this.worklistDetails).then(()=>console.log('cache'));
				  this.getWorklistMetaDetails();
				}
			  });
			} else {
			  this.getWorklistMetaDetails();
			}
		  });

  }
  getWorklistMetaDetails() {
    const defIndex = this._sharedService.worklistColumnDef.findIndex(
      (x) => x.worklistId == this.worklistid && x.workflowId == this.workflowId
    );
    if (defIndex != -1 && this._sharedService.worklistColumnDef[defIndex].edited == false) {
      this.columnDefs = this._sharedService.worklistColumnDef[defIndex].columnDef;
      this.singleRowAllActions = this._sharedService.worklistColumnDef[defIndex].actionColDef;
      this.singleRowActions = this._sharedService.worklistColumnDef[defIndex].actionColDef;
      this.shortLinkActions = this._sharedService.worklistColumnDef[defIndex].shortLinks;
      this.mapFields = this._sharedService.worklistColumnDef[defIndex].mapFields;
      this.linkFields = this._sharedService.worklistColumnDef[defIndex].linkFields;
      this.list = this._sharedService.worklistColumnDef[defIndex].fieldPrefillData;
    }
        this.heading = this.worklistDetails[0].name;
        if (this.worklistDetails[0].reportForms.length > 0) {
          this.formId = this.worklistDetails[0].reportForms[0].id;
        }
        this.dataLoadingMsg = false;
        let columnObj = {};
        this.actionFields = [];
        /**Get the meta data of worklist*/
        let newJson = this.worklistDetails[0].description;
        newJson = newJson.replace(/'/g, '"');
        let metaData = JSON.parse(newJson);
        this.metaData = metaData;
        this.noteData = metaData;
        if (this.metaData.callbackFunctionName == 'addUserTherapy') {
          if (this._intakeService.therapyList.length > 0) {
            this.therapyList = this._intakeService.therapyList;
          } else {
            this._intakeService.getTherapies().then((data) => {
              this.therapyList = data['getTherapies'];
              this._intakeService.therapyList = data['getTherapies'];
            });
          }
          if (this._intakeService.moaList.length > 0) {
            this.moaList = this._intakeService.moaList;
          } else {
            this._intakeService.getMoas().then((data) => {
              this.moaList = data['getMoas'];
              this._intakeService.moaList = data['getMoas'];
            });
          }
          if (this._intakeService.recheckReasons.length > 0) {
            this.recheckReason = this._intakeService.recheckReasons;
          } else {
            this._intakeService.getRecheckReasons().then((data) => {
              this.recheckReason = data['getRecheckReason'];
              this._intakeService.recheckReasons = data['getRecheckReason'];
            });
          }

        }
        if (this.worklistView == 'grid') {
          this.filterCheck = this.metaData.enableDynamicFilter;
          if (this.filterCheck == true) {
            this.selectName = this.metaData.prefixText;
            this.filterField = this.metaData.filterField;
          }
          this.hasUniqueId = metaData.hasUniqueId;
          if (metaData.hasUniqueId) {
            this.uniqueClass = metaData.uniqueIdClass;
          }
          this.customFields = this.worklistDetails[0].customFields ? this.worklistDetails[0].customFields : [];
          this.integrationSettings = (this.worklistDetails[0].integrationSettings && this.worklistDetails[0].integrationSettings != null) ? this.worklistDetails[0].integrationSettings : [];
          this.integrationDetails.emit(this.integrationSettings);
          this.associatedParentWorklists = this.worklistDetails[0].associatedParentWorklists ? this.worklistDetails[0].associatedParentWorklists : [];
          /**Default column width */
          this.columnWidth = this.metaData.defaultColumnWidth;
          /** Single/Multiple row selection */
          this.rowSelection = this.metaData.checkboxSelectionType;
          if (this.metaData.checkboxSelectionType == 'multiple') {
            this.groupSelectsChildren = true;
          } else {
            this.groupSelectsChildren = false;
          }
          /** form label from either label text or from css class */
          this.formFieldFrom = metaData.formFieldFrom;
          /**determine the position of action button */
          this.btnBehaviour = metaData.btnBehaviour;
          this.actionColumnPosition = metaData.btnPosition;
          /**set Lazy loading parameters */
          this.cacheBlockSize = Number(metaData.InitCountVal);
          this.maxBlocksInCache = Number(metaData.blocksize);
          //this.paginationPageSize = 0;
          /**Page size */
          this.paginationPageSize = Number(metaData.pageSize);
          this.pageSize = this.paginationPageSize;
          /**Show page size list */
          if (this.metaData.pageSizeCount) {
            this.pageList = this.metaData.pageSizeCount.split(',');
            this.pageList.splice(0, 0, Number(metaData.pageSize));
          }
          /*show row group panel*/
          if (this.metaData.enableRowGroup == true) {
            this.rowGroupPanelShow = 'always';
          } else {
            this.rowGroupPanelShow = '';
          }
          /**Get the reportFields and sort  */
          this.reportFields = JSON.parse(JSON.stringify(this.worklistDetails[0].reportFields));
          // this.setDynamicFields();
          this.reportFields.sort(function (a, b) {
            if (a.displayIndex < b.displayIndex) { return -1; }
            if (a.displayIndex > b.displayIndex) { return 1; }
            return 0;
          });
          /**Filter field */
          this.filterEnabledFields = this.reportFields.filter(x => x.allowQuickSearch == true && x.visibility == true);
          /**Check column definition is present in shared service */
          /**Save configuration data to shared service in initial loading and get that data ifrom shared service in further loading */
          const defIndex = this._sharedService.worklistColumnDef.findIndex(
            (x) => x.worklistId == this.worklistid && x.workflowId == this.workflowId
          );
          if (defIndex == -1) {
            this.setColumnDefinition(this.actionColumnPosition);
            this._sharedService.worklistColumnDef.push({
              worklistId: this.worklistid,
              workflowId: this.workflowId,
              columnDef: this.columnDefs,
              edited: false,
              actionColDef: this.singleRowActions,
              shortLinks: this.shortLinkActions,
              linkFields: this.linkFields,
              mapFields: this.mapFields,
              fieldPrefillData: this.list,
              callbackFunction: this.metaData.callbackFunctionName
            });
          } else {
              if (this._sharedService.worklistColumnDef[defIndex].edited ) {
                this.setColumnDefinition(this.actionColumnPosition);
                this._sharedService.worklistColumnDef[defIndex].edited = false;
                this._sharedService.worklistColumnDef[defIndex].columnDef = this.columnDefs;
                this._sharedService.worklistColumnDef[defIndex].workflowId = this.workflowId;
                this._sharedService.worklistColumnDef[defIndex].actionColDef = this.singleRowActions;
                this._sharedService.worklistColumnDef[defIndex].shortLinks = this.shortLinkActions;
                this._sharedService.worklistColumnDef[defIndex].mapFields = this.mapFields;
                this._sharedService.worklistColumnDef[defIndex].linkFields = this.linkFields;
                this._sharedService.worklistColumnDef[defIndex].fieldPrefillData = this.list;
              }
            }

          /**Showing add new button on right top corner */
          //this.addNewBtn = this.metaData.addNewButton;
          /**Set the dashboard widgets */
          this.dashboardWidgets = JSON.parse(JSON.stringify(this.worklistDetails[0].dashboardWidgets));
          this.dashboardWidgets.sort(function (a, b) {
            if (a.displayIndex < b.displayIndex) { return -1; }
            if (a.displayIndex > b.displayIndex) { return 1; }
            return 0;
          });
          if (this.dashboardWidgets.length > 0) {
            /**Set selected widget for showing that widget highlighted */
            this.selectedWidget = this.dashboardWidgets[0].widgetValue;
          }
          /**Set the cell click Function */
          this.cellClickFn = metaData.cellClickFn;

          const index = this._structureService.activePatientActivityDetails.findIndex(x => x.tabId == this.worklistid);
          if (index != -1) {
            this._structureService.activePatientActivityDetails[index].columnDefs = this.columnDefs;
            this._structureService.activePatientActivityDetails[index].dashboardWidgets = this.dashboardWidgets;
          }
          if (this.gridApi) {
            this.gridApi.setColumnDefs(this.columnDefs);
            this.gridApi.sizeColumnsToFit();
          }
          /**Set export option */
          if (metaData.enableExcelExport == false) {
            this.suppressExcelExport = true;
          }
          if (metaData.enableCsvExport == false) {
            this.suppressCsvExport = true;
          }
          if (metaData.enableCustomExport == true) {
            this.showCustomExport = true;
          }
          /**Set the right side panel of ag grid */
          this.sideBar = {
            toolPanels: []
          };
          let panelObj = {};
          if (metaData.filterPanel == 'true') {
            panelObj = {
              id: 'filters',
              labelDefault: 'Filters',
              labelKey: 'filters',
              iconKey: 'filter',
              toolPanel: 'agFiltersToolPanel'
            };
            this.sideBar.toolPanels.push(panelObj);
          }
          if (metaData.columnPanel == 'true') {
            panelObj = {
              id: 'columns',
              labelDefault: 'Columns',
              labelKey: 'columns',
              iconKey: 'columns',
              toolPanel: 'agColumnsToolPanel',
            };
            this.sideBar.toolPanels.push(panelObj);
          }
        }
        if (metaData.rowModel == 'server') {
          this.rowModelType = "serverSide";
          this.pagination = true;
          if (metaData.dataLoadingType && metaData.dataLoadingType == 'pagination') {
            this.pagination = true;
            this.paginationPageSize = Number(metaData.pageSize);
          } else {
            this.pagination = false;
          }
        } else {
          this.rowModelType = "clientSide";
          this.paginationPageSize = Number(metaData.pageSize);
          this.pagination = true;
        }
        if(this.worklistView == 'form') {
          this.getformData();
        }
  }
  getDatePicker() {
    function Datepicker() { }
    Datepicker.prototype.init = function (params) {
      // create the cell
      this.eInput = document.createElement('input');
      this.eInput.value = params.value;
      $(this.eInput).datepicker({
        dateFormat: 'dd-mm-yy',
        changeMonth: true,
        changeYear: true
      });
    };
    Datepicker.prototype.getGui = function () {
      return this.eInput;
    };
    Datepicker.prototype.afterGuiAttached = function () {
      this.eInput.focus();
      this.eInput.select();
    };
    Datepicker.prototype.getValue = function () {
      return this.eInput.value;
    };
    Datepicker.prototype.destroy = function () { };
    Datepicker.prototype.isPopup = function () {
      return false;
    };
    return Datepicker;
  }
  setColumnDefinition(position) {
    /**Action button cofiguration - single and multiple */
    let columnObj = {};
    this.actionFields = JSON.parse(JSON.stringify(this.worklistDetails[0].singleWorklistAction));
    if ((this.actionFields['actionButton']).length > 0) {
      this.actionFields['actionButton'].sort(function (a, b) {
        if (a.buttonIndex < b.buttonIndex) { return -1; }
        if (a.buttonIndex > b.buttonIndex) { return 1; }
        return 0;
      });

    }
    this.actionFields['actionButton'].forEach(element => {
      const obj = {
        label: element.actionLabel,
        iconClass: element.actionIcon,
        disable: false,
        toolTip: element.tooltiptext,
        onClick: '',
        type: '',
        buttonStyle: '',
        itemElements: [],
        callbackfunction: element.actionCallbackFunction,
        actionLink: element.actionLink,
        actionField: element.formField ? element.formField : '',
        actionMode: element.actionMode ? element.actionMode : '',
        selectionMode: element.selectionMode ? element.selectionMode : '',
        fieldValues: element.fieldValues,
        downloadLink: '',
        downloadStatus: false,
        docMessage: '',
        actionRole: element.actionRoles,
        actionPrivileges: element.actionPrivileges == null ? '' : element.actionPrivileges,
        actionFields: element.actionFields,
        showOnlyLoginUser: element.showOnlyLoginUser,
        loginUserMatchField: element.loginUserMatchField
      };

      if (element.actionButtonType == 'single') {
        obj.type = 'single';
        if (element.shortLink == true) {
          this.shortLinkActions.push(obj);
        } else {
          this.singleRowActions.push(obj);
          this.singleRowAllActions.push(obj);
        }
      } else {
        obj.type = 'multiple';
        obj.buttonStyle = element.buttonStyle;
        obj.onClick = this.multipleButtonAction.bind(this);
        element.actionMenu.forEach(item => {
          const itemObj = {
            label: item.itemName,
            disable: false,
            action: '',
            itemField: item.itemFormField,
          };
          if (item.itemActionType == 'link') {
            itemObj.action = item.itemActionLink;
          } else {
            itemObj.action = item.itemActionCallbackFunction;
          }
          obj.itemElements.push(itemObj);
        });
        this.singleRowActions.push(obj);
      }
    });
    if (position == 'first') {
      if (this.btnBehaviour) {
        this.columnDefs.push(
          {
            headerName: 'Actions',
            field: 'action',
            width: 300,
            sortable: false,
            filter: false,
            cellRenderer: 'buttonRenderer',
            cellRendererParams: {
              label: 'Label',
              iconElements: this.singleRowActions
            }
          }
        );
      }
    }
    /**Configure column definition */
    this.reportFields.forEach(element => {
      columnObj = {};
      /**Table column header name */
      columnObj['headerName'] = element.headerName;
      /**form field corresponding to each column */
      columnObj['field'] = element.fieldName;
      /**Set minimum width for column */
      if (element.columnWidth && element.columnWidth != '') {
        columnObj['minWidth'] = Number(element.columnWidth);
      } else {
        if (this.columnWidth && this.columnWidth != '') {
          columnObj['minWidth'] = Number(this.columnWidth);
        } else {
          columnObj['minWidth'] = 200;
        }
      }
      if (element.cellEvent) {
        columnObj['cellEvent'] = element.cellEvent;
      }
      /**check whether the column is visible or not*/
      if (element.visibility === false || element.visibility === null) {
        columnObj['hide'] = true;
      }
      /**check whether the cell can be edit*/
      if (element.allowEdit == true) {
        columnObj['editable'] = true;
      }
      /**Value formatter for null value */
      if (element.allowCellValueChange) {
        columnObj['valueFormatter'] = function cellFormatterFn(params) {
          if (params.value == '' || params.value == null) {
            if (element.newCellValue && element.newCellValue != '') {
              return element.newCellValue;
            } else {
              return params.value;
            }
          }
        };
      }
      if (element.mapField) {
        this.mapFields.push({ fieldName: element.fieldName, mapField: element.mapField, type: element.valueType });
      }
      if (element.linkField) {
        this.linkFields.push({ fieldName: element.fieldName, mapField: element.fieldId, linkField: element.linkField, type: element.valueType });
      }
      /**Set the column group feature*/
      let columnGroups = [];
      if (element.groupName != '' || element.groupName != '') {
        columnGroups.push(element.groupName);
        let obj = {
          headerName: element.headerName,
          field: element.fieldName,
        }
      }
      if (element.allowSort === false) {
        columnObj['sortable'] = false;
      }
      if (element.allowFilter === false) {
        columnObj['filter'] = false;
      } else {
        columnObj['filterParams'] = {
          newRowsAction: 'keep',
          suppressRemoveEntries: true,
          applyButton: element.applyFilter ? element.applyFilter : false,
          clearButton: element.clearFilter ? element.clearFilter : false,
          suppressAndOrCondition: true
        };
        if (element.filterType === 'Number_Filter') {
          columnObj['filter'] = 'agNumberColumnFilter';
        }
        if (element.filterType === 'Text_Filter') {
          columnObj['filter'] = 'agTextColumnFilter';
        }
        if (element.filterType === 'Date_filter') {
          columnObj['filter'] = 'agDateColumnFilter';
        }
      }
      /**Process date value*/
      if (element.valueType === 'date') {
        /**Show datepicker while editing */
        columnObj['cellEditor'] = 'datePicker';
        /**Implement date filter functionality */
        columnObj['comparator'] = this.dateComparator;
        //columnObj['getQuickFilterText'] = this.convertDateFormat;
        if (this.metaData.callbackFunctionName == 'blogNote') {
          columnObj['valueFormatter'] = this.convertBlogDateFormat;
          columnObj['getQuickFilterText'] = this.convertBlogDateFormat;
        } else {
          columnObj['valueFormatter'] = this.convertDateFormat;
          columnObj['getQuickFilterText'] = this.convertDateFormat;
        }
        columnObj['filterParams'] = {
          inRangeInclusive: true,
          newRowsAction: 'keep',
          suppressRemoveEntries: true,
          suppressAndOrCondition: true,
          applyButton: element.applyFilter ? element.applyFilter : false,
          clearButton: element.clearFilter ? element.clearFilter : false,
          comparator: function (filterLocalDateAtMidnight, cellValue) {
            let cellDate = moment.unix(Number(cellValue)).toLocaleString();
            if (moment(cellDate).format('MMM DD YYYY') < moment(filterLocalDateAtMidnight).format('MMM DD YYYY')) {
              return -1;
            } else if (moment(cellDate).format('MMM DD YYYY') > moment(filterLocalDateAtMidnight).format('MMM DD YYYY')) {
              return 1;
            } else {
              return 0;
            }
          }
        };
      }
      if (element.valueType === 'time') {
        columnObj['valueFormatter'] = this.convertTimeFormat;
      }
      if (element.allowRowGrouping) {
        columnObj['enableRowGroup'] = true;
      } else {
        columnObj['enableRowGroup'] = false;
      }
      /**Enable checkbox selection for each column */
      if (element.checkboxSelection) {
        columnObj['checkboxSelection'] = true;
        if (this.metaData.callbackFunctionName == 'addUserTherapy' &&
          ((this.worklistEditData.therapyRecheckRequired == true && this.worklistEditData.therapyStatus == 0) || this.workflowId == this._constantService.entryTypeIds['INITIAL_INS'] || this.workflowId == this._constantService.entryTypeIds['INS_VERIFICATION'])) {
          columnObj['checkboxSelection'] = false;
        }

        if (this.rowModelType == 'ClientSide') {
          if (this.rowSelection == 'multiple') {
            /**for adding checkbox in header also */
            columnObj['headerCheckboxSelection'] = true;
            /**Set the property of header check box */
            if (this.metaData.headerCheckboxMode == 'filtered') {
              columnObj['headerCheckboxSelectionFilteredOnly'] = true;
            } else {
              columnObj['headerCheckboxSelectionFilteredOnly'] = false;
            }
          }
        }
        /**For Master detail grid view */
        if (this.metaData.worklistType == 'master/detail') {
          columnObj['cellRenderer'] = 'agGroupCellRenderer';
        }
      }
      if (element.allowPrefillData == true) {
        if (element.prefillMethod == 'formOptions') {
          let formElementId: number;
          let formId: number;
          let formType = 'internal';
          let extraOptions = [];
          if (element.prefillFormOption == 'internal') {
            formId = this.formId;
            if (element.fieldId) {
              formElementId = element.fieldId;
            }
          } else {
            formType = 'external';
            formId = element.prefillOptionForm;
            formElementId = element.prefillOptionFormField;
            if (element.prefillExtraOptions && element.prefillExtraOptions != '') {
              extraOptions = element.prefillExtraOptions.split('||');
            }
          }
          columnObj['cellEditor'] = 'agRichSelectCellEditor';
          columnObj['cellEditorParams'] = {
            values: undefined
          };

          this._workListService.getMachformFields(formId, Number(formElementId)).refetch().then(({ data: response }) => {
            this.list[element.fieldName] = JSON.parse(JSON.stringify(response['getFormElementDetails'].options));
            this.list[element.fieldName].push({ 'type': formType });
            let elementList = [];
            response['getFormElementDetails'].options.forEach(element => {
              elementList.push(element.optionValue);
            });
            extraOptions.forEach(element => {
              elementList.push(element.trim());
            });
            this.setDropdownList(elementList, element.fieldName);
          });
        }
      }

      /**Cell style property */
      if (element.allowCellStyle == true && element.cellStyles) {
        let cssStyles = element.cellStyles.filter(x => x.contentType == "text");
        if (cssStyles.length > 0) {
          columnObj['cellStyle'] = function (params) {
            let columnValue;
            let formField;
            if (typeof params.value == 'string') {
              columnValue = params.value.toLowerCase();
            } else {
              columnValue = params.value;
            }
            for (let i = 0; i < cssStyles.length; i++) {
              let fieldValue;
              let allowRelatedFeild;
              allowRelatedFeild = cssStyles[i].allowRelatedFeild;
              if (allowRelatedFeild) {
                let relatedField = cssStyles[i].formField;
                if (params.data) {
                  columnValue = params.data[`${relatedField}`] != null ? params.data[`${relatedField}`].toString().toLowerCase() : '';
                }
              }

              if (typeof cssStyles[i].fieldValue == 'string') {

                fieldValue = cssStyles[i].fieldValue.toLowerCase();
              } else {
                fieldValue = cssStyles[i].fieldValue;
              }
              if (!cssStyles[i].expressionType || cssStyles[i].expressionType == '') {
                if((cssStyles[i].expression == '==' && columnValue == fieldValue) ||
                (cssStyles[i].expression == '!=' && columnValue != fieldValue) ||
                (cssStyles[i].expression == '>' && columnValue > fieldValue) ||
                (cssStyles[i].expression == '>=' && columnValue >= fieldValue) ||
                (cssStyles[i].expression == '<' && columnValue < fieldValue) ||
                (cssStyles[i].expression == '<=' && columnValue <= fieldValue) ||
                (cssStyles[i].expression == 'contains' && columnValue && columnValue.includes(fieldValue))) {
                    if(cssStyles[i].strikeThrough == true) {
                      return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour, "text-decoration":"line-through" };
                    } else {
                      return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                    }
                } else {
                  return { "color": "none", "background": "none", "text-decoration":"none" };
                }
              }
              if (cssStyles[i].expressionType && cssStyles[i].expressionType == 'day') {
                var date = moment(params.value);
                let dayValues = cssStyles[i].day.split(',');
                if (dayValues.indexOf(date.day().toString()) != -1) {
                  return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                } else if (dayValues.indexOf('today') != -1) {
                  var today = moment(new Date()).format('MM/DD/YYYY');
                  if (date.isSame(today) == true) {
                    return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                  }
                } else if (dayValues.indexOf('yesterday') != -1) {
                  var yesterday = moment().subtract(1, 'days').format('MM/DD/YYYY');
                  if (date.isSame(yesterday) == true) {
                    return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                  }
                } else if (dayValues.indexOf('tomorrow') != -1) {
                  var tomorrow = moment().add(1, 'days').format('MM/DD/YYYY');
                  if (date.isSame(tomorrow) == true) {
                    return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                  }
                }
              }
              if (cssStyles[i].expressionType && cssStyles[i].expressionType == 'month') {
                var date = moment(params.value);
                let monthValue = cssStyles[i].month.split(',');
                if (monthValue.indexOf(date.month().toString()) != -1) {
                  return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                }
              }
              if (cssStyles[i].expressionType && cssStyles[i].expressionType == 'year') {
                var date = moment(params.value);
                if (cssStyles[i].year == date.year()) {
                  return { color: cssStyles[i].fontColour, backgroundColor: cssStyles[i].bgColour };
                }
              }
            }

          }
        }
        let iconStyles = element.cellStyles.filter(x => x.contentType == "icon" || x.contentType == "both");
        let iconStyleOnly = element.cellStyles.filter(x => x.contentType == "icon");
        if (iconStyleOnly.length > 0) {
          columnObj['cellStyle'] = { textAlign: 'center' };
        }
        if (iconStyles.length > 0) {
          columnObj['cellRenderer'] = 'iconCellRenderer';
          let iconArray = [];
          for (let i = 0; i < iconStyles.length; i++) {
            let iconObj;
            iconObj = {
              iconColour: iconStyles[i].iconColor,
              iconClass: iconStyles[i].iconClass,
              fieldValue: iconStyles[i].fieldValue,
              expression: iconStyles[i].expression,
              expressionType: iconStyles[i].expressionType,
              day: iconStyles[i].day,
              month: iconStyles[i].month,
              year: iconStyles[i].year,
              contentType: iconStyles[i].contentType,
              allowRelatedField: iconStyles[i].allowRelatedFeild,
              relatedField: iconStyles[i].allowRelatedFeild == true ? iconStyles[i].formField : ''
            }
            iconArray.push(iconObj);
          }
          columnObj['cellRendererParams'] = {
            iconElements: iconArray
          }
        }
      }
      if (element.displayType && element.displayType == 'other') {
        let index = this.associatedParentWorklists.findIndex(x => x.worklistId == this.parentWorklistId);
        let allowEdit = true;
        if (index != -1) {
          allowEdit = this.associatedParentWorklists[index].allowEdit;
        }
        let contentObj = {
          displayTypeField: element.controlTypeMappingField,
          dataSourceField: element.dataSourceField == 'other' ? element.dataSourceTextField : element.dataSourceField,
          emptyCellField: element.fieldUsedForEmptyCell,
          emptyFieldValue: element.fieldValueForEmptyCell,
          allowEdit: allowEdit,
          checklistCallback: this.metaData.callbackFunctionName,
          workflowId: this.workflowId,
          entryTypeIds: this._constantService.entryTypeIds,
          therapyRecheck: this.worklistEditData['therapyRecheck']
        };
        columnObj['cellRenderer'] = 'contentCellRenderer';
        columnObj['cellRendererParams'] = {
          contentDetails: contentObj
        }
      }
      if(element.enableEventAssociation == true && element.cellEvent == 'showDetail') {
        let noteType = '';
        if (this.metaData.callbackFunctionName == 'liaisonNote') {
          noteType = 'liaison';
        }
        if (this.metaData.callbackFunctionName == 'intakeNote') {
          noteType = 'intake';
        }
        if (this.metaData.callbackFunctionName == 'insuranceNote') {
          noteType = 'insurance';
        }
        if (this.metaData.callbackFunctionName == 'blogNote') {
          noteType = 'blog';
        }
        let contentObj = {
          textBanner: true,
          noteType: noteType
        };

        columnObj['cellRenderer'] = 'popupCellRenderer';
        
        columnObj['cellRendererParams'] = {
          contentDetails: contentObj
        }
      }
      if (element.groupName != '' && element.groupName != null && element.visibility == true) {
        let groupIndex = this.columnDefs.findIndex(x => x.headerName == element.groupName && x.children.length != 0);
        if (groupIndex == -1) {
          let childObj = {
            children: []
          };
          childObj['headerName'] = element.groupName;
          childObj['marryChildren'] = true;
          childObj['children'].push(columnObj);
          this.columnDefs.push(childObj);
        } else {
          columnObj['columnGroupShow'] = 'open';
          this.columnDefs[groupIndex]['children'].push(columnObj);
        }
      } else {
        this.columnDefs.push(columnObj);
      }
    });
    if (position == 'last') {
      if (this.btnBehaviour) {
        this.columnDefs.push(
          {
            headerName: 'Actions',
            field: 'action',
            width: 300,
            sortable: false,
            filter: false,
            cellRenderer: 'buttonRenderer',
            cellRendererParams: {
              label: 'Label',
              iconElements: this.singleRowActions
            }
          }
        );
      }
    }
  }
  onSelectionChanged(event) {
    this.rowCount = event.api.getSelectedNodes().length;
    this.selectedRowDetails = event.api.getSelectedRows();
    if (this.rowCount == 0) {
      this.hideActionButtons = true;
    } else {
      let rowData = event.api.getSelectedNodes()[0].data;
      let filterAction = [];
      let privileges = Object.keys(this._structureService.privileges);
      let userRole = this._structureService.getCookie('userRole');
      this.singleRowAllActions.forEach(element => {
        /**Check privilege for showing action button */
        if ((element.actionPrivileges != '' && privileges.indexOf(element.actionPrivileges) != -1) || element.actionPrivileges == '') {
          let actionFields = [];
          if (element.actionField && element.actionField != '') {
            actionFields.push({ 'associatedField': element.actionField, 'fieldValues': element.fieldValues });
          } else if (element.actionFields) {
            actionFields = element.actionFields;
          }
          if (actionFields.length > 0) {
            let count = 0;
            let loginUserId = this.userData.userId;
            let loginDisplayName = this.userData.displayName;
            actionFields.forEach(field => {
              if (rowData.hasOwnProperty(field.associatedField)) {
                rowData[field.associatedField] = rowData[field.associatedField] == null ? '' : rowData[field.associatedField];
                if (field.fieldValues && field.fieldValues.split(',').findIndex(x => x == rowData[field.associatedField].toString()) != -1) {
                  if (element.showOnlyLoginUser == true) {
                    if (rowData[element.loginUserMatchField] == loginUserId || rowData[element.loginUserMatchField] == loginDisplayName) {
                      count++;
                    }
                  } else {
                    count++;
                  }
                }
              }
              if (field.fieldValues && field.fieldValues == 'Any') {
                if (rowData[field.associatedField]) {
                  count++;
                }
              }
            });
            if (count == actionFields.length) {
              filterAction.push(element);
            }
          } else {
            filterAction.push(element);
          }
        }
      });
      this.singleRowActions = [];
      if (filterAction.length > 0) {
        this.singleRowActions = filterAction;
      }
      this.hideActionButtons = false;
    }
  }
  /**Single button action */
  singleBtnAction(callbck, link, field, mode, selection) {
    let selectedData = this.selectedRowDetails;
    if (selectedData.length > 0) {
      this.hideActionButtons = false;
      let e = { rowData: selectedData[0], actionField: field, actionMode: mode, selectionMode: selection, link: link };
      if (link == '' || link == null) {
        if (callbck === 'editTherapy') {
          this.editTherapy(e)
        }
        if (callbck === 'resend') {
          this.resend(e)
        }
        if (callbck === 'deleteTherapy') {
          this.deleteTherapy(e)
        }
        if (callbck === 'deleteEdoc') {
          this.deleteEdoc(e)
        }
        if (callbck === 'viewEdoc') {
          this.viewEdoc(e)
        }
        if (callbck === 'voidBlogNote') {
          this.voidBlogNote(e)
        }
        if (callbck === 'unvoidBlogNote') {
          this.unvoidBlogNote(e)
        }
      } else {
        this.goToActionLink(link);
      }
    } else {
      this.hideActionButtons = true;
    }
  }
  goToActionLink(actionLink) {
    this.router.navigate([`${actionLink}`]);
  }
  convertBlogDateFormat(params) {
    if (!params.value) {
      return '';
    }
    let rowData = params.data;
    if (params.value && params.value != '' && rowData != null){
      let timeZone = 'America/New_York';
      let tenant_timezoneName =  window.localStorage.getItem('tenantTimezoneName');
      if(tenant_timezoneName && tenant_timezoneName != '') {
        if(tenant_timezoneName.toUpperCase() != 'UTC') { 
          timeZone = tenant_timezoneName;
        }
      } else {
        timeZone = momentTz.tz.guess();
      }
      let newDate = new Date(params.value);
      let data = moment.utc(newDate);
      return momentTz.tz((data), timeZone).format('lll z');
    } else {
      return '';
    }
  }
  convertDateFormat(params) {
    let timeZone = momentTz.tz.guess();
    if (!params.value) {
      return '';
    }
    if (params.value && params.value != '' && (params.value.toString().length != 10 && params.value.toString().length != 13)) {
      let newDate = new Date(params.value).getTime();
      var time = moment.utc(newDate);
      return momentTz.tz((time), timeZone).format('lll z');
    } else if (params.value && params.value != '' && params.value.toString().length == 10) {
      let newDate = new Date(Number(params.value) * 1000);
      var time = moment.utc(newDate);
      return momentTz.tz((time), timeZone).format('lll z');
      return time;
    } else if (params.value && params.value != '' && params.value.toString().length == 13) {
      let newDate = new Date(Number(params.value));
      var time = moment.utc(newDate);
      return momentTz.tz((time), timeZone).format('lll z');
      return time;
    } else {
      return '';
    }
  }
  convertTimeFormat(params) {
    if (!params.value) {
      return '';
    }
    if (params.value || params.value != '') {
      return moment(params.value, ['HH:mm']).format('h:mm A');
    }
  }
  dateComparator(date1, date2) {
    if (date1 === null && date2 === null) {
      return 0;
    }
    if (date1 === null) {
      return -1;
    }
    if (date2 === null) {
      return 1;
    }
    return date1 - date2;
  }
  multipleButtonAction(e) {
    
  }
  setDropdownList(list, field) {
    /**List staff name while edit cell */
    this.columnDefs.forEach(element => {
      if (element.field == field) {
        element.cellEditorParams.values = list;
      } else {
        if (element.children && element.children.length > 0) {
          let index = element.children.findIndex(x => x.field == field);
          if (index != -1) {
            element.children[index].cellEditorParams.values = list;
          }
        }
      }
    });
  }
  searchFilterData(params) {
    const filterModel = this.gridApi.getFilterModel();
    this.gridApi.setFilterModel(null);
    if (this.selectedWidget != 'All') {
      if (filterModel.hasOwnProperty(this.selectedWidgetField) == true) {
        const statusFilterComponent = this.gridApi.getFilterInstance(this.selectedWidgetField);
        statusFilterComponent.setModel({
          type: 'equals',
          filter: filterModel[this.selectedWidgetField].filter
        });
      }
    }
    if (params == '') {
      this.searchFieldText = '';
    }
    this.gridApi.setQuickFilter(params);
  }
  getformData() {
    if(this.fromDashboard == 'true') {
      this.gridApi.showLoadingOverlay();
    }
    this.suppressNoRow = false;
    if (this.metaData.dataSource && this.metaData.dataSource == 'API') {
      if (this.rowModelType == 'serverSide') {
        let widgetIndex = 0;
        if (this.dashboardWidgets.length > 0 && this.dashboardWidgets[widgetIndex].widgetValue != 'All') {
          this.optionShowButton = this.dashboardWidgets[widgetIndex].widgetValue;
          /**this is for showig data satisfying first widget condition */
          this.updateColumnData(this.dashboardWidgets[widgetIndex].widgetValue, this.dashboardWidgets[widgetIndex].formField, this.dashboardWidgets[widgetIndex].hideColumns, true, this.dashboardWidgets[widgetIndex].count);
        } else {
          var datasourcee = ServerSideDatasourceApi(this, '');
          this.gridApi.setServerSideDatasource(datasourcee);
        }
        if (this.rowData && this.rowData.length == 0) {
          this.showNoData = true;
        }
      } else {
        this.parameters = [];
        this.metaData.parameters.split('&').forEach(element => {

          let key = element.substring(
            element.lastIndexOf('{{') + 2,
            element.lastIndexOf('}}')
          );
          if (key == 'userId') {
            element = element.replace('{{' + key + '}}', this.userId);
          }
          if (key == 'logginUserId') {
            element = element.replace('{{' + key + '}}', this.userData.userId);
          }
          if (key == 'tenantId') {
            element = element.replace('{{' + key + '}}', this._structureService.getCookie('tenantId'));
          }
          if (key == 'crossTenantId') {
            element = element.replace('{{' + key + '}}', this._structureService.getCookie("crossTenantId"));
          }
          if (key == 'roleid') {
            element = element.replace('{{' + key + '}}', this.userData.roleId);
          }
          if (key == 'zone') {
            element = element.replace('{{' + key + '}}', timezone.name());
          }
          if (key == 'isForms') {
            element = element.replace('{{' + key + '}}', true);
          }
          if (key == 'isPrivilege') {
            element = element.replace('{{' + key + '}}', true);
          }
          if (key == 'searchText') {
            element = element.replace('{{' + key + '}}', null);
          }
          if (key == 'pah_patient_id') {
            element = element.replace('{{' + key + '}}', this.userPatientId);
          }
          if (key == 'limit') {
            element = element.replace('{{' + key + '}}', 0);
          }
          if (key == 'offset') {
            element = element.replace('{{' + key + '}}', 0);
          }
          this.parameters.push(element);
        });
        let graphqlQuery = '';
        let url = this.metaData.endpoint;
        if (this.metaData.graphqlApi) {
          let url = this.metaData.graphqlEndpoint;
          let fieldList = this.metaData.fieldList.split(',');
          let fieldString = '';
          fieldList.forEach(field => {
            if (field.includes('.')) {
              let colArray = field.split('.');
              let endString = '';
              colArray.forEach((element, index) => {
                fieldString = ` ${fieldString} ${element} `;
                if (index !== colArray.length - 1) {
                  fieldString = ` ${fieldString} { `;
                  endString = ` ${endString} } `;
                }

              });
              fieldString = ` ${fieldString} ${endString}  `;
            } else {
              fieldString = `${fieldString} ${field}`;
            }
          });
          let newQuery = this.metaData.parameters.replace('$fields', fieldString);
          let variables = {};
          if (this.worklistView == 'form') {
            const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
            if (this.lockConfig == true) {
              if (this.appLock == 'app') {
                variables = { id: this.patientId, lockConfig: 1, createdBy: userDetails.userId, appLock: true }
              } else {
                variables = { id: this.patientId, lockConfig: 1, createdBy: userDetails.userId, appLock: false }
              }
            } else {
              variables = { id: this.patientId };
            }
          } else {
            variables = { patientId: this.patientId };
          }
          if (this.metaData.callbackFunctionName == 'addEdoc') {
            variables = {
              "filter": [{
                "column": "patientId",
                "filter": this.patientId,
                "type": "equals",
                "filterCondition": "AND"
              }]
            };
          }
          if (this.metaData.parameters.includes('checklistWorklist')) {
            let tenantIds = [];
            if (
              this._structureService.getCookie('crossTenantId') &&
              this._structureService.getCookie('crossTenantId') !== 'undefined' &&
              this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
            ) {
              tenantIds.push(this._structureService.getCookie('crossTenantId'));
            } else {
              tenantIds.push(this._structureService.getCookie('tenantId'));
            }
            if (this.metaData.callbackFunctionName == 'intakePaperWork') {
              // variables.noteType='liaison'
              variables = { patientId: Number(this.patientId), type: 'INTKPPRWRK', workflowId: this.workflowId, tenantIds: tenantIds };
            }
            if (this.metaData.callbackFunctionName == 'insuranceVerification') {
              if (this.workflowId == this._constantService.entryTypeIds['INTAKE'] || this.workflowId == this._constantService.entryTypeIds['INS_VERIFICATION']) {
                variables = {
                  patientId: this.patientId,
                  type: 'ININSVER',
                  workflowId: this.workflowId,
                  patientEntryRequiredWorkflowId: this._constantService.entryTypeIds['INITIAL_INS'],
                  tenantIds: tenantIds
                };
              }
              if (this.workflowId == this._constantService.entryTypeIds['INITIAL_INS']) {
                variables = { patientId: this.patientId, type: 'ININSVER', workflowId: this.workflowId, tenantIds: tenantIds };
              }
            }
            if (this.metaData.callbackFunctionName == 'finalInsuranceVerification') {
              variables = {
                patientId: this.patientId,
                type: 'ININSVER',
                workflowId: this.workflowId,
                patientEntryRequiredWorkflowId: this._constantService.entryTypeIds['INITIAL_INS'],
                bgColorRequired: true,
                tenantIds: tenantIds
              };
            }
            if (this.metaData.callbackFunctionName == 'intakeFinalReview') {
              variables = { patientId: this.patientId, type: 'INTKFINREV', workflowId: this.workflowId, tenantIds: tenantIds };
            }
            if (this.metaData.callbackFunctionName == 'miscellaneous') {
              variables = { patientId: this.patientId, type: 'LIAISONMISC', workflowId: this.workflowId, tenantIds: tenantIds };
            }
            if (this.metaData.callbackFunctionName == 'insuranceVerificationItems' || this.metaData.callbackFunctionName == 'initialInsuranceVerificationItems') {
              variables = { patientId: this.patientId, type: 'INSVERITEMS', workflowId: this.workflowId, tenantIds: tenantIds };
            }

          }
          if (this.metaData.parameters.includes('userProgressBlogNote')) {
            variables = { patientId: this.worklistEditData.patientId };
          }
          if (this.metaData.parameters.includes('getUserNotes')) {
            variables = {patientId: this.patientId};
            if (this.metaData.callbackFunctionName == 'liaisonNote') {
              variables['noteType'] = 'liaison';
            } else if (this.metaData.callbackFunctionName == 'intakeNote') {
              variables['noteType'] = 'intake';
            } else if (this.metaData.callbackFunctionName == 'insuranceNote') {
              variables['noteType'] = 'insurance';
            } else if (this.metaData.callbackFunctionName == 'blogNote') {
              variables['noteType'] = 'blog';
            } else if (this.metaData.callbackFunctionName == 'pharmacyNote') {
              variables['noteType'] = 'pharmacy';
            }
          }
          this._intakeWorklistService.getWorklistDataUsingGraphQLAPI
            (url, newQuery, variables, this.prepareHeaderVariables()).then(
              (data) => {
              this.prepareApiData(JSON.parse(JSON.stringify(data)));
              if (data.data['checklistWorklist'] != null && (this.metaData.callbackFunctionName == 'intakePaperWork' || this.metaData.callbackFunctionName == 'insuranceVerification' || this.metaData.callbackFunctionName == 'finalInsuranceVerification' || this.metaData.callbackFunctionName == 'intakeFinalReview' || this.metaData.callbackFunctionName == 'miscellaneous' || this.metaData.callbackFunctionName == 'insuranceVerificationItems' || this.metaData.callbackFunctionName == 'initialInsuranceVerificationItems')) {
                this.checkListEntryType = data.data['checklistWorklist'].entryType;
                this.checkListCreatedBy = data.data['checklistWorklist'].createdBy;
                this.checkListId = data.data['checklistWorklist'].id;
                this.checkListStatus = data.data['checklistWorklist'].status != null ? data.data['checklistWorklist'].status.trim().toUpperCase() : '';
                this.worklistStatus.emit(this.checkListStatus);
                if (this.metaData.callbackFunctionName == 'insuranceVerificationItems') {
                  if (this.checkListStatus != 'NEW') {
                    this.gridColumnApi.setColumnVisible('initialInsuranceEntry', false);
                    this.gridColumnApi.setColumnVisible('itemValueRequired', true);
                  } else {
                    this.gridColumnApi.setColumnVisible('initialInsuranceEntry', true);
                    this.gridColumnApi.setColumnVisible('itemValueRequired', false);
                  }
                  this.gridApi.sizeColumnsToFit();
                }
                this.checklistStatusIndicator = this.metaData.enableStatusIndication == true ? this.metaData.statusIndicationCallback : '';
                this.showHideCustomField(false);
              }
              this.reloadTabData.emit({ type: this.worklistView, status: false });
            }, (error)=> {
              this.rowData = [];
              this.suppressNoRow = false;
              this.gridApi.showNoRowsOverlay();
              this.reloadTabData.emit({ type: this.worklistView, status: false });
            });
            
        } else {
          this.disableWidget = true;
          this._workListService.getWorklistDataUsingAPI(this.metaData.endpoint, this.parameters.join('&')).then((data) => {
            this.rowData = JSON.parse(JSON.stringify(data['response']));
            this.disableWidget = false;
            this.setDefaultWidgetConfig(this.rowData);
            if (this.rowData) {
              this.setDefaultGrid();
            }
            if (this.rowData && this.rowData.length == 0) {
              this.suppressNoRow = false;
              this.gridApi.showNoRowsOverlay();
              this.showNoData = true;
            } else {
              if (this.gridApi) {
              }
              let widgetIndex = 0;
              if (this.dashboardWidgets.length > 0 && this.dashboardWidgets[widgetIndex].widgetValue != 'All') {
                this.optionShowButton = this.dashboardWidgets[widgetIndex].widgetValue;
                this.updateColumnData(this.dashboardWidgets[widgetIndex].widgetValue, this.dashboardWidgets[widgetIndex].formField, this.dashboardWidgets[widgetIndex].hideColumns, false, this.dashboardWidgets[widgetIndex].count);
              }
            }
          });
        }
      }
    } else {
      if (this.rowModelType == 'serverSide') {
        var datasource = ServerSideDatasource(this, this._workListService);
        if (this.gridApi)
          this.gridApi.setServerSideDatasource(datasource);
      } else {
        this.disableWidget = true;
        this._workListService.getWorkListFormData(this.formId, this.uniqueClass, '', '', this.formFieldFrom).refetch().then(({ data: response }) => {
          if (this.uniqueClass !== '') {
            this.formRowData = JSON.parse(JSON.stringify(response['getFormDataWithUniqueID']));
          } else {
            this.formRowData = JSON.parse(JSON.stringify(response['getFormData']));
          }
          this.disableWidget = false;
          if (this.formRowData.length > 0) {
            this.formDataList = [];
            let i = 1;
            let obj = {};
            this.formRowData.forEach(element => {
              const formObj = {};
              element.elements.forEach(elem => {
                formObj[elem.labelText] = elem.value;
                if (elem.valueType == 'radio' && elem.valueOther != '') {
                  formObj[elem.labelText] = elem.valueOther;
                }
                if (elem.valueType == 'checkbox' && elem.valueOther != '') {
                  if (formObj[elem.labelText] == '') {
                    formObj[elem.labelText] = elem.valueOther;
                  } else {
                    formObj[elem.labelText] = formObj[elem.labelText] + ',' + elem.valueOther;
                  }
                }
                if (i == 1) {
                  obj[elem.labelText] = elem.tid;
                  this.machFormKeys.push(elem.labelText);
                }
                if (elem.valueType == 'date') {
                  if (elem.timestampForDate && elem.timestampForDate != null && elem.timestampForDate != '') {
                    let newDate = new Date(Number(elem.timestampForDate));
                    formObj[elem.labelText] = moment.utc(newDate).format('DD-MM-YYYY');
                  } else {
                    formObj[elem.labelText] = '';
                  }
                }
                formObj['keyname'] = elem.keyname;
              });
              formObj['submissionID'] = element.submissionID;
              formObj['slno'] = i;
              formObj['action'] = '';
              this.formDataList.push(formObj);
              i++;
            });
            this.machFormIds.push(obj);
            this.rowData = this.formDataList;
            this.setDefaultWidgetConfig(this.rowData);
          } else {
            this.rowData = [];
            this.suppressNoRow = false;
            this.gridApi.showNoRowsOverlay()
            this.setDefaultWidgetConfig(this.rowData);
          }
        });
      }
    }
  }
  /** Prepare header variables for api */
  prepareHeaderVariables() {
    let headers = {};
    if (this.metaData.enableAuthorization === true) {
      if (this.metaData.authtype === "token") {
        headers = {
          authorizationtoken: this._structureService.getCookie('authID')
        }
      }
    }
    return headers;
  }
  prepareApiData(data) {
    let rowData: any = data;
    if (this.metaData.graphqlApi) {
      let keyArray = Object.keys(data['data']);
      rowData = data['data'][keyArray[0]] == null ? [] : data['data'][keyArray[0]].data;

      this.entryDetailsData = rowData;
      this.dataLoading = false;
      if (this.worklistView == 'form') {
        this.worklistEntryForm(this.entryDetailsData);
      }
    }
    this.referalDetails.emit(rowData);
    if (rowData && rowData.length == 0) {
      this.suppressNoRow = false;
      this.gridApi.showNoRowsOverlay();
    } else {
      this.showNoData = false;
      if (this.metaData.worklistType == 'single') {
        this.rowData = rowData;
      } else {
        rowData.forEach(element => {
          if (element.formData) {
            let formDataList = [];
            let objKeys = Object.keys(element.formData);
            objKeys.forEach(keys => {
              if (element.formData[keys]) {
                let formObj = {};
                let i = 1;
                let obj = {};
                element.formData[keys].forEach(elem => {
                  let labelText = '';
                  if (elem.label.lastIndexOf('{{') == -1) {
                    labelText = elem.label;
                    if (i == 1) {
                      obj[elem.label] = { 'id': elem.element_id, 'elementType': elem.element_type };
                    }
                  } else {
                    let key = elem.label.substring(elem.label.lastIndexOf('{{') + 2, elem.label.lastIndexOf('}}'));
                    labelText = key;
                    if (i == 1) {
                      obj[key] = { 'id': elem.element_id, 'elementType': elem.element_type };
                    }
                  }
                  if (elem.element_type == 'date' && elem.value && elem.value != null && elem.value != '') {
                    let newDate = new Date(Number(elem.value) * 1000);
                    formObj[labelText] = moment.utc(newDate).format('MM/DD/YYYY');
                  } else {
                    formObj[labelText] = '';
                  }
                });
                this.machFormIds.push({ 'type': 'child' });
                this.machFormIds.push(obj);
                i++;
                formObj['submissionID'] = Number(keys);
                formDataList.push(formObj);
              }
            });
            element.callRecords = formDataList;
          } else {
            element.callRecords = element.day_wise_time_range;
          }
        });
        this.rowData = rowData;
      }
    }
    this.setDefaultWidgetConfig(this.rowData);
    if(this.metaData.callbackFunctionName == 'addUserTherapy') {
      this.changeBtnStatus.emit(false);
    }
  }
  worklistEntryForm(data) {
    if (data) {
      this.worklistEditDataValue = data[0];
      let locationObj: any = [];
      if (this.worklistEditDataValue.locationId != null && this.worklistEditDataValue.locationName != null) {
        locationObj.push({
          id: this.worklistEditDataValue.locationId,
          locationName: this.worklistEditDataValue.locationName
        });
      } else {
        locationObj = '';
      }
      const rphReviewTime= this.worklistEditDataValue.rphReviewTime;
      this.editLiaisonEntryForm.patchValue({
        id: this.worklistEditDataValue.id,
        roomNumber: this.worklistEditDataValue.roomNumber,
        nextFollowup: this.worklistEditDataValue.nextFollowup,
        insCoordinator: this.worklistEditDataValue.insCoordinator,
        teachComplete: this.worklistEditDataValue.teachComplete,
        teachReason: this.worklistEditDataValue.teachReason != null ? this.worklistEditDataValue.teachReason : '',
        hospitalDischarge: this.worklistEditDataValue.hospitalDischarge,
        hospitalChart: this.worklistEditDataValue.hospitalChart,
        vnaVerified: this.worklistEditDataValue.vnaVerified,
        liaisonComplete: this.worklistEditDataValue.liaisonComplete,
        timeCommitmentCalled: this.worklistEditDataValue.timeCommitmentCalled,
        hospitalDischargeApproved: this.worklistEditDataValue.hospitalDischargeApproved,
        timeCommitment: !isBlank(this.worklistEditDataValue.timeCommitment) && this.worklistEditDataValue.timeCommitment != 0 ? new Date(this.worklistEditDataValue.timeCommitment) : null,
        insurancePolicy: this.worklistEditDataValue.insurancePolicy != 'NULL' ? this.worklistEditDataValue.insurancePolicy : '',
        tcRequested: !isBlank(this.worklistEditDataValue.tcRequested) && this.worklistEditDataValue.tcRequested != 0 ?  new Date(this.worklistEditDataValue.tcRequested) : null,
        deliveryRequested: !isBlank(this.worklistEditDataValue.deliveryRequested) && this.worklistEditDataValue.deliveryRequested != 0 ? new Date(this.worklistEditDataValue.deliveryRequested) : null,
        noMixOrDeliveryRequired: this.worklistEditDataValue.noMixOrDeliveryRequired,
        excludedFromTcCutoffTime: this.worklistEditDataValue.excludedFromTcCutoffTime,
        pharmacyReviewComplete: this.worklistEditDataValue.pharmacyReviewComplete,
        rph_hour: typeof(rphReviewTime) == 'string' ? (rphReviewTime == '00:00' ? '' : rphReviewTime.split(':')[0]) : '',
        rph_minute: typeof(rphReviewTime) == 'string' ? (rphReviewTime == '00:00' ? '' : rphReviewTime.split(':')[1]) : ''
      });
      setTimeout(() => {
        this.editLiaisonEntryForm.patchValue({
          locationId: locationObj,
        });
      }, 1000);
      if (this.entryFormType == this._constantService.entryTypes['INTAKE'] &&
        this.worklistEditDataValue.therapyStatus == 1) {
        this.editLiaisonEntryForm.controls['insurancePolicy'].disable();
      }
    }

  }
  loadModal(type) {
    let formdataa = this.editLiaisonEntryForm.value;
    const msg = type == 'liaison' ? 'liaison entry' : 'pharmcy review';
    const config = {
      title: this.toolTipService.getTranslateData('MESSAGES.CONFIRM_MESSAGE'),
      text: this.toolTipService.getTranslateDataWithParam('MESSAGES.CONFIRM_LIAISON_PHARMACY',{ msg: msg }),
      type: "warning",
      showCancelButton: true,
      confirmButtonText: this.toolTipService.getTranslateData('BUTTONS.CONTINUE'),
      closeOnConfirm: true,
      confirmButtonClass: "btn-warning",
      cancelButtonClass: "btn-default",
      cancelButtonText: this.toolTipService.getTranslateData('BUTTONS.CANCEL')
    };
    this._structureService.showAlertMessagePopup(config).then((response)=>{
      if(response == true) {
        if(type == 'liaison') {
          $("#liaison").prop("checked", true);
          formdataa.liaisonComplete = true;
        }
        this.updateLiaisonEntry();
      } else {
        if(type == 'liaison') {
          $("#liaison").prop("checked", false);
          formdataa.liaisonComplete = false;
          this.editLiaisonEntryForm.patchValue({
            liaisonComplete: false
          })
        }
        this.changeBtnStatus.emit(false);
      }
    });
    
  }
  loadIntakeModal() {
    var elm = this;
    var intakeData = this.editLiaisonEntryForm.value;
    setTimeout(function () {
      swal({
        title: 'Are you sure?',
        text: "You are going to approve SOC of this patient",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        cancelButtonText: "Cancel",
        confirmButtonText: "Continue",

        closeOnConfirm: true
      },
        (confirm) => {
          if (confirm) {
            $("#intakeSoc").prop("checked", true);
            intakeData.hospitalDischargeApproved = true;
            elm.updateLiaisonEntry();
          } else {
            $("#intakeSoc").prop("checked", false);
            intakeData.hospitalDischargeApproved = false;
            elm.editLiaisonEntryForm.patchValue({
              hospitalDischargeApproved: false
            })
            elm.changeBtnStatus.emit(false);
          }

        })
    }, 1000);
  }
  loadTeachReasonModal() {
    var elm = this;
    var intakeData = this.editLiaisonEntryForm.value;
    setTimeout(function () {
      swal({
        title: '',
        text: "Please fill Teach Reason",
        type: "warning",
        customClass: 'swal-size-sm',
      },
        (confirm) => {
          elm.changeBtnStatus.emit(false);
        })
        , 1000;
    })
  }
  confirmLiaisonEntry(entryType) {
    if (entryType == this._constantService.entryTypes['LIAISON']
      && this.editLiaisonEntryForm.value['liaisonComplete'] == true) {
      this.loadModal('liaison');
    } else if (entryType == this._constantService.entryTypes['LIAISON'] && this.editLiaisonEntryForm.value['teachComplete'] == 0 &&
      this.editLiaisonEntryForm.value['teachReason'].trim() == '') {
      this.loadTeachReasonModal();
    } else if (entryType == this._constantService.entryTypes['INTAKE'] &&
      this.editLiaisonEntryForm.value['hospitalDischargeApproved'] == true) {
      this.loadIntakeModal();
    } else if (entryType == this._constantService.entryTypes['INTAKE'] && this.editLiaisonEntryForm.value['teachComplete'] == 0 &&
      this.editLiaisonEntryForm.value['teachReason'].trim() == '') {
      this.loadTeachReasonModal();
    } else if (entryType == this._constantService.entryTypes['PHARMACY_FOLLOWUP'] && this.editLiaisonEntryForm.value['pharmacyReviewComplete'] && 
      (isBlank(this.editLiaisonEntryForm.value['rph_hour']) || this.editLiaisonEntryForm.value['rph_hour'] === '00') && 
      (isBlank(this.editLiaisonEntryForm.value['rph_minute']) || this.editLiaisonEntryForm.value['rph_minute'] === '00')) {
      this.loadPharmacyCompleteCheckModal();
    } else if (entryType == this._constantService.entryTypes['PHARMACY_FOLLOWUP'] && this.editLiaisonEntryForm.value['pharmacyReviewComplete']) {
      this.loadModal('pharmacy');
    } else {
      this.updateLiaisonEntry();
    }
  }
  loadPharmacyCompleteCheckModal() {
    const config = {
      title: '',
      text: this.toolTipService.getTranslateData('VALIDATION_MESSAGES.PHARMACY_COMPLETE_VALIDATION'),
      type: "warning",
      showCancelButton: false,
      confirmButtonText: this.toolTipService.getTranslateData('BUTTONS.OK'),
      closeOnConfirm: true,
      confirmButtonClass: "btn-warning"
    };
    this._structureService.showAlertMessagePopup(config).then((response)=>{
      if(response == true) {
        this.changeBtnStatus.emit(false);
      }
    });
  }
  updateLiaisonEntry() {
    if (this.editLiaisonEntryForm.valid) {
      const formdata = this.editLiaisonEntryForm.value;
      this.ngxLogger.log('form values', this.editLiaisonEntryForm.value);
      let updatedFormValues = {};
      let rph_hour = formdata['rph_hour'];
      let rph_minute = formdata['rph_minute'];
      let editRphReviewTime = false;
      this.editLiaisonEntryForm['_forEachChild']((control, name) => {
        if (control.dirty) {
          if ((name === "nextFollowup" || name === "hospitalDischarge")) {
            if (control.value != null) {
              updatedFormValues[name] = new Date(control.value.formatted).getTime();
            } else {
              updatedFormValues[name] = '';
            }
          } else if (name === 'locationId') {
            updatedFormValues[name] = formdata['locationId'].length > 0 ? formdata['locationId'][0]['id'] : '';
          } else if (name === 'rph_hour') {
            rph_hour = control.value;
            editRphReviewTime = true;
          } else if (name === 'rph_minute') {
            editRphReviewTime = true;
            rph_minute = control.value;
          } else if((name === 'tcRequested' || name === 'deliveryRequested' || name === 'timeCommitment') && 
            this.entryFormType === this._constantService.entryTypes['INTAKE']) {
            updatedFormValues[name] = !isBlank(control.value) ? new Date(control.value).getTime() : 0;
          } else if((name === 'noMixOrDeliveryRequired' || name === 'excludedFromTcCutoffTime') && this.entryFormType === this._constantService.entryTypes['INTAKE']) {
            updatedFormValues[name] = control.value;
          } else if(name !== 'tcRequested' && name !== 'deliveryRequested' && name !== 'timeCommitment' && name !== 'noMixOrDeliveryRequired' && name !== 'excludedFromTcCutoffTime'){
              updatedFormValues[name] = control.value;
          }
        } else {
          if (name === 'rph_hour') {
            rph_hour = control.value;
            editRphReviewTime = true;
          } else if (name === 'rph_minute') {
            editRphReviewTime = true;
            rph_minute = control.value;
          } else if (name === 'pharmacyReviewComplete'){
            updatedFormValues[name] = control.value;
          }
        }
      });
      if(editRphReviewTime && this.entryFormType === this._constantService.entryTypes['PHARMACY_FOLLOWUP']) {
        updatedFormValues['rphReviewTime'] = `${rph_hour || '00'}:${rph_minute || '00'}`;
      }
      updatedFormValues['mrn'] = this.worklistEditData.patientId;
      let variables = {
        id: Number(formdata.id),
        values: updatedFormValues,
        workflowType: this.entryFormType
      }
      var count = Object.keys(updatedFormValues).length;
      if (count > 0) {
        NProgress.start();
        this._intakeService.updateLiaisonEntryDetails(variables).subscribe(
          res => {
            this.changeBtnStatus.emit(false);
            this.editLiaisonEntryForm.markAsPristine();
            let message, notifyType;
            if (res.data.patientUpdate['status'] == 201) {
              notifyType = 'success';
              let activityData= {
                activityType: this.entryFormType,
                activityDescription: this.userData.displayName +
                'edit' + this.entryFormType + 'with id= ' +
                formdata.id + 'for the patient' +
                this.worklistEditData.name + 'with MRN' +
                this.worklistEditData.id + 'from' + this.dynamicData.tabName + 'in' +
                this.parentWorklistName
              };
              switch (this.entryFormType) {
                case this._constantService.entryTypes['LIAISON']:
                  activityData['activityName'] = "Edit Liaison Entry";
                  message = this.toolTipService.getTranslateData('SUCCESS_MESSAGES.LIAISON_DETAILS');
                  break;
                case this._constantService.entryTypes['INTAKE']:
                  activityData['activityName'] = "Edit Intake Details";
                  message = this.toolTipService.getTranslateData('SUCCESS_MESSAGES.INTAKE_DETAILS');
                  break;
                case this._constantService.entryTypes['PHARMACY_FOLLOWUP']:
                  activityData['activityName'] = "Edit pharmacy Details";
                  message = this.toolTipService.getTranslateData('SUCCESS_MESSAGES.PHARMACY_DETAILS');
                  break;
              }
              this._structureService.trackActivity(activityData);
              updatedFormValues = {};
            } else if (res.data.patientUpdate['status'] == 500 || res.data.patientUpdate['status'] == 202) {
              notifyType = 'danger';
              switch (this.entryFormType) {
                case this._constantService.entryTypes['LIAISON']:
                  message = this.toolTipService.getTranslateData('ERROR_MESSAGES.LIAISON_DETAILS');
                  break;
                case this._constantService.entryTypes['INTAKE']:
                  message = this.toolTipService.getTranslateData('ERROR_MESSAGES.INTAKE_DETAILS');
                  break;
                case this._constantService.entryTypes['PHARMACY_FOLLOWUP']:
                  message = this.toolTipService.getTranslateData('ERROR_MESSAGES.PHARMACY_DETAILS');
                  break;
              }
            }
            setTimeout(() => {
              $.notify({ message: message }, { type: notifyType });
            }, 1000);
            NProgress.done();
          });
      } else {
        this.changeBtnStatus.emit(false);
      }
    } else {
      this.changeBtnStatus.emit(false);
    }
  }
  setDefaultGrid() {
    const index = this._structureService.activePatientActivityDetails.findIndex(x => x.tabId == this.worklistid);
    if (index != -1) {
      this._structureService.activePatientActivityDetails[index].gridApi = this.gridApi;
      this._structureService.activePatientActivityDetails[index].sideBar = this.sideBar;
      this._structureService.activePatientActivityDetails[index].cellClickFn = this.cellClickFn;
      this._structureService.activePatientActivityDetails[index].rowGroupPanelShow = this.rowGroupPanelShow;
      this._structureService.activePatientActivityDetails[index].filterCheck = this.filterCheck;
      this._structureService.activePatientActivityDetails[index].selectName = this.selectName;
      this._structureService.activePatientActivityDetails[index].filterField = this.filterField;
      this._structureService.activePatientActivityDetails[index].singleRowActions = this.singleRowAllActions;
      this._structureService.activePatientActivityDetails[index].rowData = this.rowData;
    }
  }
  getDefaultGrid() {
    const index = this._structureService.activePatientActivityDetails.findIndex(x => x.tabId == this.dynamicData.pahworklistId);
    if (index != -1) {
      this.gridApi = this._structureService.activePatientActivityDetails[index].gridApi;
      this.dashboardWidgets = this._structureService.activePatientActivityDetails[index].dashboardWidgets;
      this.cellClickFn = this._structureService.activePatientActivityDetails[index].cellClickFn;
      this.rowGroupPanelShow = this._structureService.activePatientActivityDetails[index].rowGroupPanelShow;
      this.sideBar = this._structureService.activePatientActivityDetails[index].sideBar;
      this.filterCheck = this._structureService.activePatientActivityDetails[index].filterCheck;
      this.selectName = this._structureService.activePatientActivityDetails[index].selectName;
      this.filterField = this._structureService.activePatientActivityDetails[index].filterField;
      this.singleRowActions = this._structureService.activePatientActivityDetails[index].singleRowActions;
      this.singleRowAllActions = this._structureService.activePatientActivityDetails[index].singleRowActions;
      this.columnDefs = this._structureService.activePatientActivityDetails[index].columnDefs;
      this.userPatientId = this._structureService.activePatientActivityDetails[index].patientId;
      this.rowData = this._structureService.activePatientActivityDetails[index].rowData;
      this.setDefaultWidgetConfig(this.rowData);
      this.disableWidget = false;
    }
  }
  setDefaultWidgetConfigApi() {
    this.dashboardWidgets.forEach(element => {
      let widgetValue = element.widgetValue.replace(/\s/g, "");
      if (this.totalRowData[widgetValue]) {
        element.count = this.totalRowData[widgetValue];
      } else {
        element.count = 0;
      }
    });
  }
  setDefaultWidgetConfig(rowData) {
    if (this.dashboardWidgets) {
      this.dashboardWidgets.forEach(element => {
        if (element.displayText == 'Auto Approved') {
          element.displayText = 'Auto Approved';
        } else {
          element.displayText = element.displayText;
        }
        element.count = this.getFilterCount(element.widgetValue, element.formField, rowData);
      });
    }
  }
  getFilterCount(filterText, filterField, rowData) {
    if (filterText === 'All') {
      return rowData.length;
    } else {
      const filteredData = rowData.filter(x => x[filterField] === filterText);
      return filteredData.length;
    }
  }
  /**custom filtering function of dashboard widget */
  updateColumnData(filterText, filterField, columns, check, count) {
    this.customSearch = false;
    /**show columns according to widget selection */
    this.selectedWidgetField = filterField;
    if (this.previousHideColumns.length > 0) {
      this.previousHideColumns.forEach(element => {
        this.gridColumnApi.setColumnVisible(element, true);
      });
    }
    this.hideColumns = (columns && columns != null) ? columns.split(',') : [];
    if (this.hideColumns.length > 0) {
      this.hideColumns.forEach(element => {
        this.gridColumnApi.setColumnVisible(element, false);
      });
      this.previousHideColumns = this.hideColumns;
    } else {
      this.previousHideColumns = [];
    }
    /***/
    this.filterText = '';
    this.gridApi.deselectAll();
    let filterModel = this.gridApi.getFilterModel();
    this.hideActionButtons = true;
    this.optionShowButton = filterText;
    if (filterText == 'All') {
      filterText = '';
    }
    if (filterField !== '' || filterField !== 'all') {
      const statusFilterComponent = this.gridApi.getFilterInstance(filterField);
      if (this.metaData.rowModel == 'server') {
        filterModel[filterField] = { type: 'equals', filter: filterText, filterType: 'text' };
        this.filterText = filterText;
        if (check == true) {
          /**In forms worklist list entries with status completed initially */
          this.gridApi.setFilterModel(filterModel);
          var datasourcee = ServerSideDatasourceApi(this, '');
          this.gridApi.setServerSideDatasource(datasourcee);
        } else {
          this.gridApi.setFilterModel(filterModel);
        }
      } else {
        if (filterText != '') {
          statusFilterComponent.setModel({
            type: 'equals',
            filter: filterText
          });
          this.gridApi.onFilterChanged();
          if (count == 0) {
            this.suppressNoRow = false;
            this.gridApi.showNoRowsOverlay();
          }
          /**Go to first page after applying filtering */
          this.gridApi.paginationGoToFirstPage();
        } else {
          this.gridApi.setFilterModel(null);
          this.gridApi.onFilterChanged();
        }
      }
    } else {
      this.gridApi.setFilterModel(null);
    }
  }
  updateColumnDataFilter(filterText, filterLabel) {
    this.filterLabel = filterLabel;
    let filterModel = this.gridApi.getFilterModel();
    let filterType;
    let filterValue = this.displayname;
    if (filterText == "others") {
      filterType = 'notEqual';
    } else if (filterText == "all") {
      filterValue = '';
    } else {
      filterType = 'equals';
    }
    const statusFilterComponent = this.gridApi.getFilterInstance(this.filterField);
    if (this.metaData.rowModel == 'server') {
      filterModel[this.filterField] = { type: filterType, filter: filterValue, filterType: 'text' };
      this.gridApi.setFilterModel(filterModel);
    } else {
      this.gridApi.setFilterModel(null);
      statusFilterComponent.setModel({
        type: filterType,
        filter: filterValue
      });
      this.gridApi.onFilterChanged();
    }
    this.gridApi.deselectAll();
  }
  exportCSV() {
    let self = this;
    let columnkey = [];
    this.reportFields.forEach(element => {
      if ((element.includeInExport == null || element.includeInExport == true) && this.hideColumns.indexOf(element.fieldName) == -1 && element.visibility == true) {
        columnkey.push(element.fieldName);
      }
    });
    let params: any = {
      columnKeys: columnkey,
      allColumns: false,
      fileName: self.heading,
      sheetName: self.heading
    };
    params.processCellCallback = function (params) {
      let index = self.reportFields.findIndex(x => x.fieldName == params.column.colDef.field);
      if (index != -1 && self.reportFields[index].valueType == 'date') {
        return self.convertDateFormat(params);
      } else {
        return params.value;
      }
    }
    this.gridApi.exportDataAsCsv(params);
  }
  exportExcel(mode) {
    let self = this;
    let columnkey = [];
    this.reportFields.forEach(element => {
      if ((element.includeInExport == null || element.includeInExport == true) && this.hideColumns.indexOf(element.fieldName) == -1 && element.visibility == true) {
        columnkey.push(element.fieldName);
      }
    });
    let params: any = {
      columnKeys: columnkey,
      allColumns: false,
      fileName: self.heading,
      exportMode: mode,
      sheetName: self.heading
    };
    params.processHeaderCallback = function (params) {
      return params.column.getColDef().headerName.toUpperCase();
    };
    params.processCellCallback = function (params) {
      let index = self.reportFields.findIndex(x => x.fieldName == params.column.colDef.field);
      if (index != -1 && self.reportFields[index].valueType == 'date') {
        return self.convertDateFormat(params);
      } else {
        return params.value;
      }
    }
    this.gridApi.exportDataAsExcel(params);
  }
  /**Change pagination size from ui */
	changePaginationSize(e) {
		this.paginationPageSize = e.target.value.trim();
    this.getformData();
	}
  /**search function in server side pagination */
  searchBasedField(searchText) {
    if (this.selectedSearchFields.length > 0) {
      if (this.selectedWidget == 'All') {
        this.gridApi.setFilterModel(null);
      }
      this.customSearch = true;
      var datasource = ServerSideDatasourceApi(this, '');
      this.gridApi.setServerSideDatasource(datasource);
    }
  }
  clearSearch() {
    if (this.searchFieldText != '') {
      var datasource = ServerSideDatasourceApi(this, '');
      this.gridApi.setServerSideDatasource(datasource);
      this.searchFieldText = '';
    }
  }
  checkboxSelection(field) {
    if (this.selectedSearchFields.findIndex(x => x.fieldId == field.fieldId) == -1) {
      this.selectedSearchFields.push(field);
    } else {
      this.selectedSearchFields = this.selectedSearchFields.filter(x => x.fieldId != field.fieldId);
    }
  }
  showEntryForm(formType = '') {
    switch (formType) {
      case 'liaisonEntry':
        this.entryFormType = this._constantService.entryTypes['LIAISON'];
        break;
      case 'intakeEntry':
        this.entryFormType = this._constantService.entryTypes['INTAKE'];
        break;
      case 'pharmacyEntry':
        this.entryFormType = this._constantService.entryTypes['PHARMACY_FOLLOWUP'];
        break;
    }
  }
  customFieldTextboxAction(callback) {
    if (callback == 'addInsurancePolicy') {
      NProgress.start();
      let params;
      let patientData;
      params = { 'insurancePolicy': this.customTextboxValue, 'mrn': this.worklistEditData.patientId };
      patientData = {
        'id': this.patientId,
        'params': params,
        'workflowType': this.workflowType
      }
      this._intakeService.updatePatientData(patientData).subscribe((data) => {
        NProgress.done();
        if (data.data['patientUpdate'].status == 201) {
          this.insurancePolicy.emit(this.customTextboxValue);
          setTimeout(() => {
            $.notify({ message: 'Insurance Policy Updated' }, { type: 'success' });
          }, 1000);
          /**Activity tracking */
          var activityData = {
            activityName: 'Update Patient Data',
            activityType: 'Update Insurance Policy',
            activityDescription: this.userData.displayName + ' update the insurance policy of value ' + this.customTextboxValue + 'for the patient ' + this.worklistEditData.firstName + ' ' + this.worklistEditData.lastName + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.metaData.nameOfDesktopMenu + ' worklist in ' + this.parentWorklistName + ' dashboard',
          };
          this._structureService.trackActivity(activityData);
          /** */
        }
      });
    }
  }
  customFieldBtnAction(callback) {
    let this1 = this;
    if (callback == 'IntakePaperWorkDone') {
      swal({
        title: "Are you sure?",
        text: "Please confirm that the paperwork has been completed?",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true
      }, (isConfirm) => {
        if (isConfirm) {
          let items = [];
          this.gridApi.forEachNode(function (rowNode, index) {
            if (rowNode.data.itemValueComplete == true) {
              items.push({
                entryType: this1.checkListEntryType,
                itemValue: 1,
                itemId: Number(rowNode.data.id)
              });
            } else if (rowNode.data.itemValueComplete != true && rowNode.data.itemValueComplete != '' && rowNode.data.itemValueComplete != false) {
              items.push({
                entryType: this1.checkListEntryType,
                linkedItemValueId: Number(rowNode.data.itemValueComplete),
                itemId: Number(rowNode.data.id)
              });
            }
          });
          if (items.length > 0) {
            let variable = {
              patientId: Number(this.patientId),
              mrn: this.worklistEditData.patientId,
              checklistId: Number(this.checkListId),
              workflowId: this.workflowId,
              statusCode: 3, // 3 for complete
              items: items,
              createdBy: (this.checkListCreatedBy && this.checkListCreatedBy != null) ? this.checkListCreatedBy : Number(this.userData.userId),
              modifiedBy: Number(this.userData.userId)
            };
            this.createCheckList(variable, 'COMPLETED');
          } else {
            this.changeBtnStatus.emit(false);
          }
        } else {
          this.changeBtnStatus.emit(false);
        }
      });
    } else if (callback == 'InsuranceVerificationPaperWorkDone') {
      let recheckRequired = true;
      if (this.worklistEditData.therapyRecheck == true) {
        this.gridApi.forEachNode(function (rowNode, index) {
          if (rowNode.data.recheckRequired == true && rowNode.data.itemValueComplete != true) {
            recheckRequired = false;
          }
        });
      }
      if (!recheckRequired) {
        swal(" ", "A recheck type must be selected", "warning");
      } else {
        swal({
          title: "Are you sure?",
          text: "Please confirm that the paperwork has been completed?",
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default",
          confirmButtonClass: "btn-warning",
          confirmButtonText: "Ok",
          closeOnConfirm: true
        }, (isConfirm) => {
          if (isConfirm) {
            let items = [];
            this.gridApi.forEachNode(function (rowNode, index) {
              if (rowNode.data.itemValueComplete == true) {
                if (rowNode.data.recheckRequired == true) {
                  items.push({
                    entryType: this1.checkListEntryType,
                    itemValue: 1,
                    itemId: Number(rowNode.data.id),
                    recheckRequired: true
                  });
                } else {
                  items.push({
                    entryType: this1.checkListEntryType,
                    itemValue: 1,
                    itemId: Number(rowNode.data.id)
                  });
                }

              } else if (rowNode.data.itemValueComplete != true && rowNode.data.itemValueComplete != '' && rowNode.data.itemValueComplete != false) {
                items.push({
                  entryType: this1.checkListEntryType,
                  linkedItemValueId: Number(rowNode.data.itemValueComplete),
                  itemId: Number(rowNode.data.id)
                });
              }
            });
            if (items.length > 0) {
              let variable = {
                patientId: Number(this.patientId),
                mrn: this.worklistEditData.patientId,
                checklistId: Number(this.checkListId),
                workflowId: this.workflowId,
                statusCode: 3, // 3 for complete
                items: items,
                enableTherapyRecheck: false,
                createdBy: (this.checkListCreatedBy && this.checkListCreatedBy != null) ? this.checkListCreatedBy : Number(this.userData.userId),
                modifiedBy: Number(this.userData.userId)
              };
              if(this.worklistEditData.initialInsuranceItemStatus.toUpperCase().trim() === 'COMPLETED') {
                variable['enableTherapyRecheck'] = true;
              }
              this.createCheckList(variable, 'COMPLETED');
            } else {
              this.changeBtnStatus.emit(false);
            }
          } else {
            this.changeBtnStatus.emit(false);
          }
        });
      }
    } else if (callback == 'MiscellaneousDone') {
      swal({
        title: "Are you sure?",
        text: "Please confirm that the paperwork has been completed?",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true
      }, (isConfirm) => {
        if (isConfirm) {
          let items = [];
          this.gridApi.forEachNode(function (rowNode, index) {
            if (rowNode.data.itemValueComplete == true) {
              items.push({
                entryType: this1.checkListEntryType == 'both' ? 'completed' : this1.checkListEntryType,
                itemValue: 1,
                itemId: Number(rowNode.data.id)
              });
            }
            if (rowNode.data.itemValueComplete != true && rowNode.data.itemValueComplete != '' && rowNode.data.itemValueComplete != false) {
              items.push({
                entryType: this1.checkListEntryType == 'both' ? 'completed' : this1.checkListEntryType,
                linkedItemValueId: Number(rowNode.data.itemValueComplete),
                itemId: Number(rowNode.data.id)
              });
            }
            if (rowNode.data.itemValueRequired == true) {
              items.push({
                entryType: this1.checkListEntryType == 'both' ? 'required' : this1.checkListEntryType,
                itemValue: 1,
                itemId: Number(rowNode.data.id)
              });
            }
            if (rowNode.data.itemValueRequired != true && rowNode.data.itemValueRequired != '' && rowNode.data.itemValueRequired != false) {
              items.push({
                entryType: this1.checkListEntryType == 'both' ? 'required' : this1.checkListEntryType,
                linkedItemValueId: Number(rowNode.data.itemValueRequired),
                itemId: Number(rowNode.data.id)
              });
            }
          });
          if (items.length > 0) {
            let variable = {
              patientId: Number(this.patientId),
              mrn: this.worklistEditData.patientId,
              checklistId: Number(this.checkListId),
              workflowId: this.workflowId,
              statusCode: 3,// 2 for save
              items: items,
              createdBy: (this.checkListCreatedBy && this.checkListCreatedBy != null) ? this.checkListCreatedBy : Number(this.userData.userId),
              modifiedBy: Number(this.userData.userId)
            };
            this.createCheckList(variable, 'COMPLETED');
          } else {
            this.changeBtnStatus.emit(false);
          }
        } else {
          this.changeBtnStatus.emit(false);
        }
      });
    } else if (callback == 'InsuranceVerificationItemsDone') {

      swal({
        title: "Are you sure?",
        text: "Please confirm that the paperwork has been completed?",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true
      }, (isConfirm) => {
        if (isConfirm) {
          let items = [];
          this.gridApi.forEachNode(function (rowNode, index) {
            if (rowNode.data.itemValueComplete == true) {
              items.push({
                entryType: this1.checkListEntryType == 'both' ? 'completed' : this1.checkListEntryType,
                itemValue: 1,
                itemId: Number(rowNode.data.id)
              });
            }
            if (rowNode.data.itemValueComplete != true && rowNode.data.itemValueComplete != '' && rowNode.data.itemValueComplete != false) {
              items.push({
                entryType: this1.checkListEntryType == 'both' ? 'completed' : this1.checkListEntryType,
                linkedItemValueId: Number(rowNode.data.itemValueComplete),
                itemId: Number(rowNode.data.id)
              });
            }
            if (rowNode.data.itemValueRequired == true) {
              items.push({
                entryType: this1.checkListEntryType == 'both' ? 'required' : this1.checkListEntryType,
                itemValue: 1,
                itemId: Number(rowNode.data.id)
              });
            }
            if (rowNode.data.itemValueRequired != true && rowNode.data.itemValueRequired != '' && rowNode.data.itemValueRequired != false) {
              items.push({
                entryType: this1.checkListEntryType == 'both' ? 'required' : this1.checkListEntryType,
                linkedItemValueId: Number(rowNode.data.itemValueRequired),
                itemId: Number(rowNode.data.id)
              });
            }
            if (this1.checkListStatus == 'NEW') {
              if (rowNode.data.initialInsuranceEntry == true) {
                items.push({
                  entryType: this1.checkListEntryType == 'both' ? 'required' : this1.checkListEntryType,
                  itemValue: 1,
                  itemId: Number(rowNode.data.id)
                });
              }
              if (rowNode.data.initialInsuranceEntry != true && rowNode.data.initialInsuranceEntry != '' && rowNode.data.initialInsuranceEntry != false) {
                items.push({
                  entryType: this1.checkListEntryType == 'both' ? 'required' : this1.checkListEntryType,
                  linkedItemValueId: Number(rowNode.data.itemValueRequired),
                  itemId: Number(rowNode.data.id)
                });
              }
            }
          });
          if (items.length > 0) {
            let variable = {
              patientId: Number(this.patientId),
              mrn: this.worklistEditData.patientId,
              checklistId: Number(this.checkListId),
              workflowId: this.workflowId,
              statusCode: 3,// 2 for save
              items: items,
              createdBy: (this.checkListCreatedBy && this.checkListCreatedBy != null) ? this.checkListCreatedBy : Number(this.userData.userId),
              modifiedBy: Number(this.userData.userId)
            };
            this.createCheckList(variable, 'COMPLETED');
          } else {
            this.changeBtnStatus.emit(false);
          }
        } else {
          this.changeBtnStatus.emit(false);
        }
      });

    } else if (callback == 'InitialInsuranceVerificationItemsDone') {
      swal({
        title: "Are you sure?",
        text: "Please confirm that the paperwork has been completed?",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true
      }, (isConfirm) => {
        if (isConfirm) {
          let items = [];
          this.gridApi.forEachNode(function (rowNode, index) {
            if (rowNode.data.itemValueRequired == true) {
              items.push({
                entryType: this1.checkListEntryType == 'both' ? 'required' : this1.checkListEntryType,
                itemValue: 1,
                itemId: Number(rowNode.data.id)
              });
            }
            if (rowNode.data.itemValueRequired != true && rowNode.data.itemValueRequired != '' && rowNode.data.itemValueRequired != false) {
              items.push({
                entryType: this1.checkListEntryType == 'both' ? 'required' : this1.checkListEntryType,
                linkedItemValueId: Number(rowNode.data.itemValueRequired),
                itemId: Number(rowNode.data.id)
              });
            }
          });
          if (items.length > 0) {
            let variable = {
              patientId: Number(this.patientId),
              mrn: this.worklistEditData.patientId,
              checklistId: Number(this.checkListId),
              workflowId: this.workflowId,
              statusCode: 3,// 2 for save
              items: items,
              enableTherapyRecheck : false,
              createdBy: (this.checkListCreatedBy && this.checkListCreatedBy != null) ? this.checkListCreatedBy : Number(this.userData.userId),
              modifiedBy: Number(this.userData.userId)
            };
            if(this.worklistEditData.initialInsuranceVerificationStatus.toUpperCase().trim() === 'COMPLETED') {
              variable['enableTherapyRecheck'] = true;
            }
            this.createCheckList(variable, 'COMPLETED');
          } else {
            this.changeBtnStatus.emit(false);
          }
        } else {
          this.changeBtnStatus.emit(false);
        }
      });

    } else if (callback == 'intakePaperWork') {
      let items = [];
      this.gridApi.forEachNode(function (rowNode, index) {
        if (rowNode.data.itemValueComplete == true) {
          items.push({
            entryType: this1.checkListEntryType,
            itemValue: 1,
            itemId: Number(rowNode.data.id)
          });
        } else if (rowNode.data.itemValueComplete != true && rowNode.data.itemValueComplete != '' && rowNode.data.itemValueComplete != false) {
          items.push({
            entryType: this1.checkListEntryType,
            linkedItemValueId: Number(rowNode.data.itemValueComplete),
            itemId: Number(rowNode.data.id)
          });
        }
      });
      if (items.length > 0) {
        let variable = {
          patientId: Number(this.patientId),
          mrn: this.worklistEditData.patientId,
          checklistId: Number(this.checkListId),
          workflowId: this.workflowId,
          statusCode: 2,// 2 for save
          items: items,
          createdBy: (this.checkListCreatedBy && this.checkListCreatedBy != null) ? this.checkListCreatedBy : Number(this.userData.userId),
          modifiedBy: Number(this.userData.userId)
        };
        this.createCheckList(variable, 'PROGRESS');
      } else {
        this.changeBtnStatus.emit(false);
      }
    } else if (callback == 'intakeFinalReview') {
      let items = [];
      this.gridApi.forEachNode(function (rowNode, index) {
        if (rowNode.data.itemValueComplete == true) {
          items.push({
            entryType: this1.checkListEntryType,
            itemValue: 1,
            itemId: Number(rowNode.data.id)
          });
        } else if (rowNode.data.itemValueComplete != true && rowNode.data.itemValueComplete != '' && rowNode.data.itemValueComplete != false) {
          items.push({
            entryType: this1.checkListEntryType,
            linkedItemValueId: Number(rowNode.data.itemValueComplete),
            itemId: Number(rowNode.data.id)
          });
        }
      });
      if (items.length > 0) {
        let variable = {
          patientId: Number(this.patientId),
          mrn: this.worklistEditData.patientId,
          checklistId: Number(this.checkListId),
          workflowId: this.workflowId,
          statusCode: 2,
          items: items,
          createdBy: (this.checkListCreatedBy && this.checkListCreatedBy != null) ? this.checkListCreatedBy : Number(this.userData.userId),
          modifiedBy: Number(this.userData.userId)
        };
        if (this.rowData.length == items.length) {
          swal({
            title: "Are you sure?",
            text: "Please confirm that the paperwork has been completed?",
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            confirmButtonText: "Ok",
            closeOnConfirm: true
          }, (isConfirm) => {
            if (isConfirm) {
              variable['statusCode'] = 3;
              this.createCheckList(variable, 'COMPLETED');
            } else {
              this.changeBtnStatus.emit(false);
            }
          });
        } else {
          this.createCheckList(variable, 'PROGRESS');
        }
      } else {
        this.changeBtnStatus.emit(false);
      }
    } else if (callback == 'miscellaneous') {
      let items = [];
      this.gridApi.forEachNode(function (rowNode, index) {
        if (rowNode.data.itemValueComplete == true) {
          items.push({
            entryType: this1.checkListEntryType == 'both' ? 'completed' : this1.checkListEntryType,
            itemValue: 1,
            itemId: Number(rowNode.data.id)
          });
        }
        if (rowNode.data.itemValueComplete != true && rowNode.data.itemValueComplete != '' && rowNode.data.itemValueComplete != false) {
          items.push({
            entryType: this1.checkListEntryType == 'both' ? 'completed' : this1.checkListEntryType,
            linkedItemValueId: Number(rowNode.data.itemValueComplete),
            itemId: Number(rowNode.data.id)
          });
        }
        if (rowNode.data.itemValueRequired == true) {
          items.push({
            entryType: this1.checkListEntryType == 'both' ? 'required' : this1.checkListEntryType,
            itemValue: 1,
            itemId: Number(rowNode.data.id)
          });
        }
        if (rowNode.data.itemValueRequired != true && rowNode.data.itemValueRequired != '' && rowNode.data.itemValueRequired != false) {
          items.push({
            entryType: this1.checkListEntryType == 'both' ? 'required' : this1.checkListEntryType,
            linkedItemValueId: Number(rowNode.data.itemValueRequired),
            itemId: Number(rowNode.data.id)
          });
        }
      });
      if (items.length > 0) {
        let variable = {
          patientId: Number(this.patientId),
          mrn: this.worklistEditData.patientId,
          checklistId: Number(this.checkListId),
          workflowId: this.workflowId,
          statusCode: 2,// 2 for save
          items: items,
          createdBy: (this.checkListCreatedBy && this.checkListCreatedBy != null) ? this.checkListCreatedBy : Number(this.userData.userId),
          modifiedBy: Number(this.userData.userId)
        };
        this.createCheckList(variable, 'PROGRESS');
      } else {
        this.changeBtnStatus.emit(false);
      }
    } else if (callback == 'insuranceVerification' && this.workflowId == this._constantService.entryTypeIds['INTAKE']) {
      let items = [];
      let yellowItems = [];
      this.gridApi.forEachNode(function (rowNode, index) {
        if(rowNode.data.backgroundColor != '') {
          yellowItems.push(rowNode.data);
        }
        if (rowNode.data.itemValueComplete == true || rowNode.data.itemValueComplete == 1) {
          if (rowNode.data.recheckRequired == true) {
            items.push({
              entryType: this1.checkListEntryType,
              itemValue: 1,
              itemId: Number(rowNode.data.id),
              recheckRequired: true
            });
          } else {
            items.push({
              entryType: this1.checkListEntryType,
              itemValue: 1,
              itemId: Number(rowNode.data.id)
            });
          }

        } else if (rowNode.data.itemValueComplete != true && rowNode.data.itemValueComplete != '' && rowNode.data.itemValueComplete != false) {
          items.push({
            entryType: this1.checkListEntryType,
            linkedItemValueId: Number(rowNode.data.itemValueComplete),
            itemId: Number(rowNode.data.id)
          });
        }
      });
      if (items.length > 0) {
        let variable = {
          patientId: Number(this.patientId),
          mrn: this.worklistEditData.patientId,
          checklistId: Number(this.checkListId),
          workflowId: this.workflowId,
          statusCode: 2,
          items: items,
          createdBy: (this.checkListCreatedBy && this.checkListCreatedBy != null) ? this.checkListCreatedBy : Number(this.userData.userId),
          modifiedBy: Number(this.userData.userId)
        };
        if (this.rowData.length == items.length) {
          swal({
            title: "Are you sure?",
            text: "Please confirm that the paperwork has been completed?",
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            confirmButtonText: "Ok",
            closeOnConfirm: true
          }, (isConfirm) => {
            if (isConfirm) {
              variable['statusCode'] = 3;
              this.createCheckList(variable, 'COMPLETED');
              if(yellowItems.length == 0) {
                let variable = {
                  patientId: Number(this.patientId),
                  mrn: this.worklistEditData.patientId,
                  checklistId: 1,
                  workflowId: this.workflowId,
                  statusCode: 3,
                  createdBy: (this.checkListCreatedBy && this.checkListCreatedBy != null) ? this.checkListCreatedBy : Number(this.userData.userId),
                  modifiedBy: Number(this.userData.userId)
                };
                this.updateFinalInsurance(variable);
              }
            } else {
              this.changeBtnStatus.emit(false);
            }
          });
        } else {
          this.createCheckList(variable, 'PROGRESS');
        }
      } else {
        this.changeBtnStatus.emit(false);
      }
    } else if (callback == 'insuranceVerification' && this.workflowId == this._constantService.entryTypeIds['INITIAL_INS']) {
      let items = [];
      this.gridApi.forEachNode(function (rowNode, index) {
        if (rowNode.data.itemValueComplete == true || rowNode.data.itemValueComplete == 1) {
          if (rowNode.data.recheckRequired == true) {
            items.push({
              entryType: this1.checkListEntryType,
              itemValue: 1,
              itemId: Number(rowNode.data.id),
              recheckRequired: true
            });
          } else {
            items.push({
              entryType: this1.checkListEntryType,
              itemValue: 1,
              itemId: Number(rowNode.data.id)
            });
          }

        } else if (rowNode.data.itemValueComplete != true && rowNode.data.itemValueComplete != '' && rowNode.data.itemValueComplete != false) {
          items.push({
            entryType: this1.checkListEntryType,
            linkedItemValueId: Number(rowNode.data.itemValueComplete),
            itemId: Number(rowNode.data.id)
          });
        }
      });
      if (items.length > 0) {
        let variable = {
          patientId: Number(this.patientId),
          mrn: this.worklistEditData.patientId,
          checklistId: Number(this.checkListId),
          workflowId: this.workflowId,
          statusCode: 2,
          items: items,
          createdBy: (this.checkListCreatedBy && this.checkListCreatedBy != null) ? this.checkListCreatedBy : Number(this.userData.userId),
          modifiedBy: Number(this.userData.userId)
        };
        this.createCheckList(variable, 'PROGRESS');
      } else {
        this.changeBtnStatus.emit(false);
      }
    } else if (callback == 'finalInsuranceVerification') {
      let items = [];
      this.gridApi.forEachNode(function (rowNode, index) {
        if (this1.workflowId == this1._constantService.entryTypeIds['INTAKE']) {
          if (rowNode.data.intakePatientEntry == true) {
            items.push({
              entryType: this1.checkListEntryType,
              itemValue: 1,
              itemId: Number(rowNode.data.id)
            });
          } else if (rowNode.data.intakePatientEntry != true && rowNode.data.intakePatientEntry != '' && rowNode.data.intakePatientEntry != false) {
            items.push({
              entryType: this1.checkListEntryType,
              linkedItemValueId: Number(rowNode.data.intakePatientEntry),
              itemId: Number(rowNode.data.id)
            });
          }
        } else {
          if (rowNode.data.insuranceFollowupEntry == true) {
            items.push({
              entryType: this1.checkListEntryType,
              itemValue: 1,
              itemId: Number(rowNode.data.id)
            });
          } else if (rowNode.data.insuranceFollowupEntry != true && rowNode.data.insuranceFollowupEntry != '' && rowNode.data.insuranceFollowupEntry != false) {
            items.push({
              entryType: this1.checkListEntryType,
              linkedItemValueId: Number(rowNode.data.insuranceFollowupEntry),
              itemId: Number(rowNode.data.id)
            });
          }
        }
      });
      if (items.length > 0) {
        let variable = {
          patientId: Number(this.patientId),
          mrn: this.worklistEditData.patientId,
          checklistId: Number(this.checkListId),
          workflowId: this.workflowId,
          statusCode: 2,
          items: items,
          createdBy: (this.checkListCreatedBy && this.checkListCreatedBy != null) ? this.checkListCreatedBy : Number(this.userData.userId),
          modifiedBy: Number(this.userData.userId)
        };
        if (this.rowData.length == items.length) {
          swal({
            title: "Are you sure?",
            text: "Please confirm that the paperwork has been completed?",
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            confirmButtonText: "Ok",
            closeOnConfirm: true
          }, (isConfirm) => {
            if (isConfirm) {
              variable['statusCode'] = 3;
              this.createCheckList(variable, 'COMPLETED');
            } else {
              this.changeBtnStatus.emit(false);
            }
          });
        } else {
          this.createCheckList(variable, 'PROGRESS');
        }
      } else {
        this.changeBtnStatus.emit(false);
      }
    } else if (callback == 'initialInsuranceVerificationItems') {
      let items = [];
      this.gridApi.forEachNode(function (rowNode, index) {
        if (rowNode.data.itemValueRequired == true) {
          items.push({
            entryType: this1.checkListEntryType == 'both' ? 'required' : this1.checkListEntryType,
            itemValue: 1,
            itemId: Number(rowNode.data.id)
          });
        }
        if (rowNode.data.itemValueRequired != true && rowNode.data.itemValueRequired != '' && rowNode.data.itemValueRequired != false) {
          items.push({
            entryType: this1.checkListEntryType == 'both' ? 'required' : this1.checkListEntryType,
            linkedItemValueId: Number(rowNode.data.itemValueRequired),
            itemId: Number(rowNode.data.id)
          });
        }
      });
      if (items.length > 0) {
        let variable = {
          patientId: Number(this.patientId),
          mrn: this.worklistEditData.patientId,
          checklistId: Number(this.checkListId),
          workflowId: this.workflowId,
          statusCode: 2,// 2 for save
          items: items,
          createdBy: (this.checkListCreatedBy && this.checkListCreatedBy != null) ? this.checkListCreatedBy : Number(this.userData.userId),
          modifiedBy: Number(this.userData.userId)
        };
        this.createCheckList(variable, 'PROGRESS');
      } else {
        this.changeBtnStatus.emit(false);
      }
    } else if (callback == 'insuranceVerificationItems') {
      let items = [];
      this.gridApi.forEachNode(function (rowNode, index) {
        if (rowNode.data.itemValueComplete == true) {
          items.push({
            entryType: this1.checkListEntryType == 'both' ? 'completed' : this1.checkListEntryType,
            itemValue: 1,
            itemId: Number(rowNode.data.id)
          });
        }
        if (rowNode.data.itemValueComplete != true && rowNode.data.itemValueComplete != '' && rowNode.data.itemValueComplete != false) {
          items.push({
            entryType: this1.checkListEntryType == 'both' ? 'completed' : this1.checkListEntryType,
            linkedItemValueId: Number(rowNode.data.itemValueComplete),
            itemId: Number(rowNode.data.id)
          });
        }
        if (rowNode.data.itemValueRequired == true) {
          items.push({
            entryType: this1.checkListEntryType == 'both' ? 'required' : this1.checkListEntryType,
            itemValue: 1,
            itemId: Number(rowNode.data.id)
          });
        }
        if (rowNode.data.itemValueRequired != true && rowNode.data.itemValueRequired != '' && rowNode.data.itemValueRequired != false) {
          items.push({
            entryType: this1.checkListEntryType == 'both' ? 'required' : this1.checkListEntryType,
            linkedItemValueId: Number(rowNode.data.itemValueRequired),
            itemId: Number(rowNode.data.id)
          });
        }
        if (this1.checkListStatus == 'NEW') {
          if (rowNode.data.initialInsuranceEntry == true) {
            items.push({
              entryType: this1.checkListEntryType == 'both' ? 'required' : this1.checkListEntryType,
              itemValue: 1,
              itemId: Number(rowNode.data.id)
            });
          }
          if (rowNode.data.initialInsuranceEntry != true && rowNode.data.initialInsuranceEntry != '' && rowNode.data.initialInsuranceEntry != false) {
            items.push({
              entryType: this1.checkListEntryType == 'both' ? 'required' : this1.checkListEntryType,
              linkedItemValueId: Number(rowNode.data.itemValueRequired),
              itemId: Number(rowNode.data.id)
            });
          }
        }
      });
      if (items.length > 0) {
        let variable = {
          patientId: Number(this.patientId),
          mrn: this.worklistEditData.patientId,
          checklistId: Number(this.checkListId),
          workflowId: this.workflowId,
          statusCode: 2,// 2 for save
          items: items,
          createdBy: (this.checkListCreatedBy && this.checkListCreatedBy != null) ? this.checkListCreatedBy : Number(this.userData.userId),
          modifiedBy: Number(this.userData.userId)
        };
        this.createCheckList(variable, 'PROGRESS');
      } else {
        this.changeBtnStatus.emit(false);
      }
    }
  }
  voidBlogNote(e){
    swal({
      title: "Are you sure?",
      text: "You are going to void this entry from Blog Note",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Ok",
      closeOnConfirm: true
    }, () => {
      let params = {
        "ids": e.rowData.id,
        "voided": true,
        "noteType":"blog"
      }
      this._intakeService.voidUserNote(params).subscribe((data) => {
        if(data.data['voidUserNote'].status == 201) {
          setTimeout(() => {
            $.notify({ message: 'Voided Blog Note Successfully' }, { type: 'success' });
          }, 1000);
          let index = this.rowData.findIndex(x=> x.id == e.rowData.id);
          if(index != -1) {
            this.rowData[index].voided = true;
          }
          this.gridApi.refreshCells({force: true});
          //this.getformData();
          this.gridApi.deselectAll();
          var activityData = {
            activityName: "Void Blog Note",
            activityType: 'Void/Unvoid Note',
            activityDescription: this.userData.displayName + ' void the blog note with id '+ e.rowData.id +' and subject '+ e.rowData.subject+' for the patient ' + this.worklistEditData.firstName + ' ' + this.worklistEditData.lastName + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.metaData.nameOfDesktopMenu + ' worklist in ' + this.parentWorklistName + ' dashboard'
          };
          this._structureService.trackActivity(activityData);
        } else {
          setTimeout(() => {
            $.notify({ message: 'Error! Some error occured, please try again.' },
              { type: 'danger' });
          }, 1000);
        }
      });
    });
  }
  unvoidBlogNote(e){
    swal({
      title: "Are you sure?",
      text: "You are going to unvoid this entry from Blog Note",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Ok",
      closeOnConfirm: true
    }, () => {
      let params = {
        "ids": e.rowData.id,
        "voided": false,
        "noteType":"blog"
      }
      this._intakeService.voidUserNote(params).subscribe((data) => {
        if(data.data['voidUserNote'].status == 201) {
          setTimeout(() => {
            $.notify({ message: 'Unvoided Blog Note Sucessfully' }, { type: 'success' });
          }, 1000);
          let index = this.rowData.findIndex(x=> x.id == e.rowData.id);
          if(index != -1) {
            this.rowData[index].voided = false;
          }
          this.gridApi.refreshCells({
              force: true
          });
          this.gridApi.deselectAll();
          var activityData = {
            activityName: "Unvoid Blog Note",
            activityType: 'Void/Unvoid Note',
            activityDescription: this.userData.displayName + ' unvoid the blog note with id '+ e.rowData.id +' and subject '+ e.rowData.subject+' for the patient ' + this.worklistEditData.firstName + ' ' + this.worklistEditData.lastName + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.metaData.nameOfDesktopMenu + ' worklist in ' + this.parentWorklistName + ' dashboard'
          };
          this._structureService.trackActivity(activityData);
        } else {
          setTimeout(() => {
            $.notify({ message: 'Error! Some error occured, please try again.' },
              { type: 'danger' });
          }, 1000);
        }
      });
    });
  }
  resend(e) {
    let dynamicTestData = this.dynamicData.filterLiaisonsDetails[0];
    let integrationSettings = this.integrationSettings.filter((x) => x.integrationType == 'progressNote');
    if (dynamicTestData.enableIntegration == true) {
      swal({
        title: "Are you sure?",
        text: "You are going to resend this progress note",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true
      }, (confirm) => {
        if (confirm) {
          let appName = localStorage.getItem('appName');
          if (dynamicTestData.callbackFunctionName == 'blogNote') {
            var entityTypeData = "BLOGNOTE";
          }
          else {
            entityTypeData = "LIAISONNOTE";
          }
          let params = {
            "patientId": this.patientId
          }
          this._intakeService.checkIntegraiondata(params).then((res) => {
            const resData = res;
            if (resData['status'] == 'Success') {
              var dataParams = {
                "integrationMode": "FC",
                "filePath": integrationSettings[0].filingCenterPath ? integrationSettings[0].filingCenterPath : '',
                "integrationType": (integrationSettings[0].enableFileCenter == true) ? 'PNFC' : '',
                "entityType": entityTypeData,
                "subject": e.rowData['subject'],
                "body": e.rowData['body'],
                "patientId": this.patientId,
                "callBackURL": integrationSettings[0].callbackUrl ? integrationSettings[0].callbackUrl : '',
                "workListType": "NextGen",
                "progressNoteType": integrationSettings[0].integrationDataType ? integrationSettings[0].integrationDataType : '',
                "appName": appName,
                "staffId": this.userData.userId
              }
              this._intakeService.integraiondata(dataParams).then((integrationRes) => {
                const integration = integrationRes;
                const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
                var activityData = {
                  activityName: "Send Progress Note",
                  activityType: 'Progress Note Integration',
                  activityDescription: this.userData.displayName + ' resend progress note for note id ' + e.rowData['id'] + ' and details ' + JSON.stringify(dataParams) + ' and get the response of ' + JSON.stringify(integrationRes) + ' for the patient ' + this.worklistEditData.firstName + ' ' + this.worklistEditData.lastName + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.metaData.nameOfDesktopMenu + ' worklist in ' + this.parentWorklistName + ' dashboard'
                };
                this._structureService.trackActivity(activityData);
                let noteParam: any;
                let noteType = '';
                if (this.metaData.callbackFunctionName == 'liaisonNote') {
                  noteType = 'liaison';
                }
                if (this.metaData.callbackFunctionName == 'blogNote') {
                  noteType = 'blog';
                }
                if (integration['status'].toLowerCase() == 'failed') {

                  noteParam = {
                    mrn: e.rowData['mrn'],
                    requestId: integration['request_id'] ? integration['request_id'] : "",
                    apiResponse: JSON.stringify(integrationRes),
                    noteType: noteType,
                    workflowId: this.workflowId,
                    createdBy: userDetails.userId,
                    integrationType: dataParams['integrationType'],
                    integrationStatus: 'Failed'
                  }
                } else {

                  noteParam = {
                    mrn: e.rowData['mrn'],
                    requestId: integration['request_id'] ? integration['request_id'] : "",
                    apiResponse: JSON.stringify(integrationRes),
                    noteType: noteType,
                    workflowId: this.workflowId,
                    createdBy: userDetails.userId,
                    integrationType: dataParams['integrationType']
                  }
                }
                this._intakeService.editNote(e.rowData['id'], noteParam).subscribe((res) => {
                  setTimeout(() => {
                    $.notify({ message: 'Success ! ' + 'Progress Note Resend Successfully' },
                      { type: 'success' });
                  }, 1000);
                  var activityData = {
                    activityName: "Resend Progress Note",
                    activityType: 'Progress note',
                    activityDescription: this.userData.displayName + ' resend progress note with id ' + e.rowData['id'] + ', subject ' + e.rowData['subject'] + ' and body ' + e.rowData['body'] + ') for the patient ' + this.worklistEditData.firstName + ' ' + this.worklistEditData.lastName + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.metaData.nameOfDesktopMenu + ' worklist in ' + this.parentWorklistName + ' dashboard'
                  };
                  this._structureService.trackActivity(activityData);
                  this.getformData();
                  this.hideActionButtons = true;
                });
              });
            } else {
              this._intakeService.enableSwal = true;
              swal({
                title: 'Are you sure?',
                text: "You are missing Staff ID and Patient MRN which are required to complete action with EHR integration.You can continue with action anyway, but data will not be sent to EHR.",
                type: "warning",
                showCancelButton: true,
                cancelButtonClass: "btn-default",
                confirmButtonClass: "btn-warning",
                cancelButtonText: "Cancel",
                confirmButtonText: "Continue",

                closeOnConfirm: true
              },
                () => {
                  this._intakeService.enableSwal = false;
                })
            }
          });
        }
      });
    } else {
      setTimeout(function () {
        swal({
          title: '',
          text: "Please enable integration in worklist",
          customClass: 'swal-size-sm',

        },
          (confirm) => {
          })
          , 1000;
      });
    }
  }
  updateFinalInsurance(variable) {
    this._intakeService.createChecklist(variable).subscribe((data) => {
      variable.workflowId = this._constantService.entryTypeIds['INS_VERIFICATION'];
      this._intakeService.createChecklist(variable).subscribe((data) => {
        this.changeStatus.emit({ 'status': 'COMPLETED', 'callback': 'finalVerificationStatus','sameTab': false });
      });
    });
    
  }
  createCheckList(variable, status) {
    this.showLoader = true;
    this._intakeService.createChecklist(variable).subscribe((data) => {
      if (data.data['UserCheckListCreate'].status == 201) {
        setTimeout(() => {
          $.notify({ message: '<strong>  Success! The Check List has been Updated. </strong>' }, { type: 'success' });
        }, 1000);
        if (variable.statusCode == 2) {
          /**Activity tracking */
          var activityData = {
            activityName: 'Save Checklist',
            activityType: this.metaData.nameOfDesktopMenu + ' Checklist',
            activityDescription: this.userData.displayName + ' save the checklist with the items (' + JSON.stringify(variable.items) + ') for the patient ' + this.worklistEditData.firstName + ' ' + this.worklistEditData.lastName + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.metaData.nameOfDesktopMenu + ' worklist in ' + this.parentWorklistName + ' dashboard',
          };
          this._structureService.trackActivity(activityData);
          /** */
        } else if (variable.statusCode == 3) {
          /**Activity tracking */
          var activityData = {
            activityName: 'Complete Checklist',
            activityType: this.metaData.nameOfDesktopMenu + ' Checklist',
            activityDescription: this.userData.displayName + ' complete the checklist with the items (' + JSON.stringify(variable.items) + ') for the patient ' + this.worklistEditData.firstName + ' ' + this.worklistEditData.lastName + ' with MRN ' + this.worklistEditData.patientId + ' from ' + this.metaData.nameOfDesktopMenu + ' worklist in ' + this.parentWorklistName + ' dashboard',
          };
          this._structureService.trackActivity(activityData);
          /** */
        }
        this.checkListStatus = status;
        this.showHideCustomField(false);
        this.changeStatus.emit({ 'status': status, 'callback': this.checklistStatusIndicator, 'sameTab': true });
        if (status == 'COMPLETED') {
          this.getformData();
        }
        if (this.metaData.callbackFunctionName == 'insuranceVerification' && status != 'COMPLETED') {
          this.getformData();
        }
        if (this.metaData.callbackFunctionName == 'insuranceVerificationItems' && status != 'COMPLETED') {
          this.getformData();
        }
        if (this.worklistEditData.therapyRecheck == true && this.workflowId == this._constantService.entryTypeIds['INITIAL_INS'] && (this.metaData.callbackFunctionName == 'initialInsuranceVerificationItems' || this.metaData.callbackFunctionName == 'insuranceVerification')) {
          this.updateChecklistStatus();
        }
        this.showLoader = false;
      } else {
        setTimeout(() => {
          $.notify({ message: 'Something Went Wrong' }, { type: 'warning' });
        }, 1000);
        this.showLoader = false;
      }
      this.changeBtnStatus.emit(false);
    });
  }
  updateChecklistStatus() {
    const userDetails = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
    let variables = {
      patientId: this.patientId,
      type: "INSVERITEMS",
      workflowId: 4
    }
    this._intakeWorklistService.getPaperWork(variables).then((data) => {
      let variable = {
        patientId: this.patientId,
        mrn: this.worklistEditData.patientId,
        checklistId: 4,
        workflowId: 4,
        statusCode: 2,
        createdBy: userDetails.userId,
        modifiedBy: userDetails.userId
      };
      if (data['checkLists'][0].status.toUpperCase() == 'COMPLETED') {
        this._intakeService.createChecklist(variable).subscribe((datas) => {
        });
      }
    });
    if (this.metaData.callbackFunctionName == 'insuranceVerification') {
      let patientData = {
        'id': this.patientId,
        'params': { modifiedBy: Number(this.userData.userId), 
          mrn : this.worklistEditData.patientId },
        'workflowType': this._constantService.entryTypes['INTAKE']
      }
      this._intakeService.updatePatientData(patientData).subscribe((data) => {

      });
    }
  }
}
function ServerSideDatasourceApi(this1, searchText) {
  let cache: any = [];
  let quickFilter;
  return {
    getRows(params) {
      $('.ag-paging-panel').css('visibility', 'visible');
      this1.disableWidget = true;
      let offset = params.request.startRow;
      let filterModel = params.request.filterModel;
      let finalFilterModel = {};
      if (this1.customSearch == false) {
        this1.searchFieldText = '';
        this1.filterEnabledFields.forEach(element => {
          $('#checkbox' + element.fieldId).prop("checked", false);
        });
        this1.selectedSearchFields = [];
      }
      if (this1.searchFieldText != '' && this1.selectedSearchFields.length > 0) {
        let filterFields = this1.reportFields.filter(x => x.allowFilter == true && x.valueType != 'checkbox');
        this1.selectedSearchFields.forEach(element => {
          if (element.valueType == 'number') {
            finalFilterModel[element.fieldName] = { type: 'equals', filter: this1.searchFieldText, filterType: 'number', filterCondition: 'OR' };
          } else if (element.valueType == 'date') {
            finalFilterModel[element.fieldName] = { type: 'equals', dateFrom: this1.searchFieldText, filterType: 'date', filterCondition: 'OR' };
          } else if (element.valueType == 'radio' || element.valueType == 'checkbox' || element.valueType == 'select') {
            finalFilterModel[element.fieldName] = { type: 'contains', filter: this1.searchFieldText, filterType: 'text', filterCondition: 'OR' };
          } else {
            finalFilterModel[element.fieldName] = { type: 'contains', filter: this1.searchFieldText, filterType: 'text', filterCondition: 'OR' };
          }
        });
      }
      if (Object.keys(filterModel).length > 0) {
        let filterModelKeys = Object.keys(filterModel);
        let widgetFilterKeys = filterModelKeys.filter(x => x == this1.selectedWidgetField);
        let otherModelKeys = filterModelKeys.filter(x => x != this1.selectedWidgetField);
        if (otherModelKeys.length > 0) {
          this1.searchFieldText = '';
          this1.filterEnabledFields.forEach(element => {
            $('#checkbox' + element.fieldId).prop("checked", false);
          });
          this1.selectedSearchFields = [];
          finalFilterModel = filterModel;
        }
        if (widgetFilterKeys.length > 0) {
          finalFilterModel[this1.selectedWidgetField] = filterModel[this1.selectedWidgetField];
        }
      }
      let sortModel = params.request.sortModel;
      let limit = Number(this1.cacheBlockSize);
      this1.parameters = [];
      if (this1.metaData.parameters) {
        this1.metaData.parameters.split('&').forEach(element => {
          let key = element.substring(
            element.lastIndexOf('{{') + 2,
            element.lastIndexOf('}}')
          );
          if (key == 'userId') {
            element = element.replace('{{' + key + '}}', this1.userId);
          }
          if (key == 'logginUserId') {
            element = element.replace('{{' + key + '}}', this1.userData.userId);
          }
          if (key == 'tenantId') {
            element = element.replace('{{' + key + '}}', this1._structureService.getCookie('tenantId'));
          }
          if (key == 'crossTenantId') {
            element = element.replace('{{' + key + '}}', this1._structureService.getCookie("crossTenantId"));
          }
          if (key == 'roleid') {
            element = element.replace('{{' + key + '}}', this1.userData.roleId);
          }
          if (key == 'zone') {
            element = element.replace('{{' + key + '}}', timezone.name());
          }
          if (key == 'isForms') {
            element = element.replace('{{' + key + '}}', true);
          }
          if (key == 'isPrivilege') {
            element = element.replace('{{' + key + '}}', true);
          }
          if (key == 'pah_patient_id') {
            element = element.replace('{{' + key + '}}', this1.userPatientId);
          }
          if (key == 'limit') {
            element = element.replace('{{' + key + '}}', limit);
          }
          if (key == 'offset') {
            element = element.replace('{{' + key + '}}', offset);
          }
          this1.parameters.push(element);
        });
      }
      if (searchText != '') {
        this1.parameters.push('searchText=' + searchText);
      }
      if (Object.keys(finalFilterModel).length > 0) {
        this1.parameters.push('filterModel=' + JSON.stringify(finalFilterModel));
      }
      if (sortModel.length > 0) {
        this1.parameters.push('sortModel=' + JSON.stringify(sortModel));
      }
      if (this1._structureService.getCookie("crossTenantId") && this1._structureService.getCookie('crossTenantId') !== 'undefined' && this1._structureService.getCookie('tenantId') !== this1._structureService.getCookie('crossTenantId')) {
        if (this1.parameters.indexOf('crossTenantId=' + this1._structureService.getCookie("crossTenantId")) < 0) {
          this1.parameters.push('crossTenantId=' + this1._structureService.getCookie("crossTenantId"));
        }
      }
      this1.totalRowData = [];
      let graphqlQuery = '';
        let url = this1.metaData.endpoint;
        if (this1.metaData.graphqlApi) {
          let url = this1.metaData.graphqlEndpoint;
          let fieldList = this1.metaData.fieldList.split(',');
          let fieldString = '';
          fieldList.forEach(field => {
            if (field.includes('.')) {
              let colArray = field.split('.');
              let endString = '';
              colArray.forEach((element, index) => {
                fieldString = ` ${fieldString} ${element} `;
                if (index !== colArray.length - 1) {
                  fieldString = ` ${fieldString} { `;
                  endString = ` ${endString} } `;
                }

              });
              fieldString = ` ${fieldString} ${endString}  `;
            } else {
              fieldString = `${fieldString} ${field}`;
            }
          });
          let newQuery = this1.metaData.parameters.replace('$fields', fieldString);
          let filterModelArray = [];
          Object.keys(finalFilterModel).forEach((filter) => {
            let filterObject = {
              column: filter,
              filter: finalFilterModel[filter].filter ? finalFilterModel[filter].filter.toString().trim() : '',
              filterTo: finalFilterModel[filter].filterTo,
              filterIn: finalFilterModel[filter].filterIn,
              type: finalFilterModel[filter].type,
              filterCondition: finalFilterModel[filter].filterCondition ? finalFilterModel[filter].filterCondition : 'AND'
            }
            filterModelArray.push(filterObject);
          });
          let variables={};
          if (this1.worklistView == 'form') {
            if (this1.lockConfig == true) {
              const userDetails = this1._structureService.userDetails?JSON.parse(this1._structureService.userDetails):{};
              if (this1.appLock == 'app') {
                variables = { id: this1.patientId, lockConfig: 1, createdBy: userDetails.userId, appLock: true }
              } else {
                variables = { id: this1.patientId, lockConfig: 1, createdBy: userDetails.userId, appLock: false }
              }
            } else {
              variables = { id: this1.patientId };
            }
          } else {
            variables = { patientId: this1.patientId };
          }
          if (this1.metaData.callbackFunctionName == 'addEdoc') {
            variables = {
              "filter": [{
                "column": "patientId",
                "filter": this1.patientId,
                "type": "equals",
                "filterCondition": "AND"
              }]
            };
          }
          if (this1.metaData.parameters.includes('checklistWorklist')) {
            let tenantIds = [];
            if (
              this1._structureService.getCookie('crossTenantId') &&
              this1._structureService.getCookie('crossTenantId') !== 'undefined' &&
              this1._structureService.getCookie('tenantId') !== this1._structureService.getCookie('crossTenantId')
            ) {
              tenantIds.push(this1._structureService.getCookie('crossTenantId'));
            } else {
              tenantIds.push(this1._structureService.getCookie('tenantId'));
            }
            if (this1.metaData.callbackFunctionName == 'intakePaperWork') {
              variables = { patientId: Number(this1.patientId), type: 'INTKPPRWRK', workflowId: this1.workflowId, tenantIds: tenantIds };
            }
            if (this1.metaData.callbackFunctionName == 'insuranceVerification') {
              if (this1.workflowId == this1._constantService.entryTypeIds['INTAKE'] || this1.workflowId == this1._constantService.entryTypeIds['INS_VERIFICATION']) {
                variables = {
                  patientId: this1.patientId,
                  type: 'ININSVER',
                  workflowId: this1.workflowId,
                  patientEntryRequiredWorkflowId: this1._constantService.entryTypeIds['INITIAL_INS'],
                  tenantIds: tenantIds
                };
              }
              if (this1.workflowId == this1._constantService.entryTypeIds['INITIAL_INS']) {
                variables = { patientId: this1.patientId, type: 'ININSVER', workflowId: this1.workflowId, tenantIds: tenantIds };
              }
            }
            if (this1.metaData.callbackFunctionName == 'finalInsuranceVerification') {
              variables = {
                patientId: this1.patientId,
                type: 'ININSVER',
                workflowId: this1.workflowId,
                patientEntryRequiredWorkflowId: this1._constantService.entryTypeIds['INITIAL_INS'],
                bgColorRequired: true,
                tenantIds: tenantIds
              };
            }
            if (this1.metaData.callbackFunctionName == 'intakeFinalReview') {
              variables = { patientId: this1.patientId, type: 'INTKFINREV', workflowId: this1.workflowId, tenantIds: tenantIds };
            }
            if (this1.metaData.callbackFunctionName == 'miscellaneous') {
              variables = { patientId: this1.patientId, type: 'LIAISONMISC', workflowId: this1.workflowId, tenantIds: tenantIds };
            }
            if (this1.metaData.callbackFunctionName == 'insuranceVerificationItems' || this1.metaData.callbackFunctionName == 'initialInsuranceVerificationItems') {
              variables = { patientId: this1.patientId, type: 'INSVERITEMS', workflowId: this1.workflowId, tenantIds: tenantIds };
            }

          }
          if (this1.metaData.parameters.includes('userProgressBlogNote')) {
            variables = { patientId: this1.worklistEditData.patientId };
          }
          if (this1.metaData.parameters.includes('getUserNotes')) {
            variables = { patientId: this1.patientId };
            if (this1.metaData.callbackFunctionName == 'liaisonNote') {
              variables['noteType'] = 'liaison';
            } else if (this1.metaData.callbackFunctionName == 'intakeNote') {
              variables['noteType'] = 'intake';
            } else if (this1.metaData.callbackFunctionName == 'insuranceNote') {
              variables['noteType'] = 'insurance';
            } else if (this1.metaData.callbackFunctionName == 'blogNote') {
              variables['noteType'] = 'blog';
            } else if (this1.metaData.callbackFunctionName == 'pharmacyNote') {
              variables['noteType'] = 'pharmacy';
            } else if (this1.metaData.callbackFunctionName == 'addUserTherapy') {
              variables = { patientId: this1.patientId };
            }
          }
          variables['startRow'] = offset;
          variables['endRow'] = params.request.endRow;
          variables['filter'] = filterModelArray;
          if(params.request.sortModel.length > 0) {
            variables['sorting'] = params.request.sortModel;
          }          
          getDataSourceGraphqlApi(params,url, newQuery, variables, this1);
        } else {
      this1._workListService.getWorklistDataUsingAPI(this1.metaData.endpoint, this1.parameters.join('&')).then((data) => {
        this1.disableWidget = false;
        this1.totalRowData = data;
        if (offset == 0) {
          this1.setDefaultWidgetConfigApi();
          if (data['totalCount']) {
            this.totalCount = data['totalCount'];
          }
        }
        this1.rowData = data['response'];
        this1.reportFields.forEach(element => {
          if (element.hideColumnBasedField && element.hideColumnBasedField != null && this1.rowData.length > 0) {
            if (this1.rowData[0][element.hideColumnBasedField] == element.hideColumnValue) {
              this1.gridColumnApi.setColumnVisible(element.fieldName, false);
            }
          }
        });
        if (this1.rowData && this1.rowData.length > 0) {
          var server = {
            success: true,
            rows: this1.rowData
          }
          let res = server;
          let lastRowValue = params.request.startRow + res.rows.length;
          let filterCount = data[this1.filterText.replace(/\s/g, "")];
          if (res.success) {
            const rows = res.rows;
            let lastRow = '';
            if ((res.rows.length < this1.cacheBlockSize) || (lastRowValue == filterCount) || (this.totalCount != 0 && lastRowValue == this.totalCount)) {
              lastRow = params.request.startRow + res.rows.length;
            }
            setTimeout(() => {
              cache = [];
              this1.gridApi.forEachNode(node => cache.push(node.data));
            }, 0)
            quickFilter = $(".search-afterfilter").val();
            if (quickFilter) {
              let filteredResults = cache.filter(row => {
                return row.toLowerCase().includes(quickFilter.toString().toLowerCase());
              })
              if (filteredResults.length != 0) {
                params.successCallback(filteredResults, filteredResults.length);
                return;
              }
              else {
                params.successCallback([], 0);
                this1.suppressNoRow = false;
                this1.gridApi.showNoRowsOverlay();
                return;
              }
            }
            params.successCallback(rows, lastRow);
          } else {
            params.failCallback();
          }
        } else {
          this1.rowData = [];
          let selectedTab = $(".cat__apps__messaging__tab_pah--selected").text().toLowerCase();
          if (selectedTab.trim() == this1.worklistName.toLowerCase()) {
            $('.ag-row-stub').html('<p style="margin-top:7px!important;text-align: center!important;">There are no ' + this1.filterText.toLowerCase() + ' ' + selectedTab + ' with this patient.</p>');
          }
          if (offset == 0) {
            $('.ag-paging-panel').css('visibility', 'hidden');
          }
        }
      });
    }
    }
  };

}
function getDataSourceGraphqlApi(params,url, newQuery, variables, this1) {
	this1._intakeWorklistService.getWorklistDataUsingGraphQLAPI(url, newQuery, variables, this1.prepareHeaderVariables()).then((data) => {
		let rowData: any = data;
		let keyArray = Object.keys(data['data']);
		rowData = JSON.parse(JSON.stringify(data['data'][keyArray[0]].data));
		this1.widgetData = data['data'][keyArray[0]].filterCount;
    
    this1.dataLoading = false;
    if (this1.worklistView == 'form') {
      this1.worklistEntryForm(rowData);
    }
    this1.referalDetails.emit(rowData);
		if (rowData.length == 0) {
			this1.rowData = [];
      this1.suppressNoRow = false;
      if(params.request.startRow == 0) {
			  this1.gridApi.showNoRowsOverlay();
      }
		} else {
      this1.showNoData = false;
			if (this1.metaData.worklistType == 'single') {
				this1.rowData = rowData;
			} else {
				rowData.forEach(element => {
					if (element.formData && element.formData.length > 0) {
						this1.machFormIds = [];
						let formDataList = [];
						let objKeys = Object.keys(element.formData);
						objKeys.forEach(keys => {
							if (element.formData[keys]) {
								let formObj = {};
								let i = 1;
								let obj = {};
								element.formData[keys].forEach(elem => {
									let labelText = '';
									if (elem.label.lastIndexOf('{{') == -1) {
										labelText = elem.label;
										if (i == 1) {
											obj[elem.label] = { 'id': elem.element_id, 'type': elem.element_type };
										}
									} else {
										let key = elem.label.substring(elem.label.lastIndexOf('{{') + 2, elem.label.lastIndexOf('}}'));
										labelText = key;
										if (i == 1) {
											obj[key] = { 'id': elem.element_id, 'type': elem.element_type };
										}
									}
									if (elem.element_type == 'date' && elem.value && elem.value != null && elem.value != '') {
										let newDate = new Date(Number(elem.value) * 1000);
										formObj[labelText] = moment.utc(newDate).format('MM/DD/YYYY');
									} else {
										formObj[labelText] = elem.value;
									}
								});
								this1.machFormIds.push({ 'type': 'child' });
								this1.machFormIds.push(obj);
								i++;
								formObj['submissionID'] = Number(keys);
								formDataList.push(formObj);
							}
						});
						element.callRecords = formDataList;
					} else {
						element.callRecords = element.day_wise_time_range;
					}
				});
				this1.rowData = rowData;
			}
      setTimeout(function () {
        this1.gridApi.resetRowHeights();
      }, 1000);
		}
    if(this1.metaData.callbackFunctionName == 'addUserTherapy') {
      this1.changeBtnStatus.emit(false);
    }
		this1.setDefaultWidgetConfig(this1.rowData);	
    this1.reloadTabData.emit({ type: this1.worklistView, status: false });	
		var server = {
			success: true,
			rows: this1.rowData,
		}
		let res = server;
		if (res.success) {
			const rows = res.rows;
			let lastRow = -1;
			if (res.rows.length < this1.cacheBlockSize) {
				lastRow = params.request.startRow + res.rows.length;
			}
			params.successCallback(rows, lastRow);
		} else {
			params.failCallback();
		}
	});
}
function ServerSideDatasource(this1, worklistService) {
  return {
    getRows(params) {
      this1.disableWidget = true;
      let start = params.request.startRow;
      let end = params.request.endRow;
      worklistService.getWorkListFormData(this1.formId, this1.uniqueClass, start, this1.cacheBlockSize, this1.formFieldFrom).refetch().then(({ data: response }) => {
        if (this1.uniqueClass !== '') {
          this1.formRowData = response['getFormDataWithUniqueID'];
        } else {
          this1.formRowData = response['getFormData'];
        }
        this1.disableWidget = false;
        if (this1.formRowData.length > 0) {
          this1.formDataList = [];
          let i = 1;
          let obj = {};
          this1.formRowData.forEach(element => {
            const formObj = {};
            element.elements.forEach(elem => {
              formObj[elem.labelText] = elem.value;
              if (elem.valueType == 'radio' && elem.valueOther != '') {
                formObj[elem.labelText] = elem.valueOther;
              }
              if (elem.valueType == 'checkbox' && elem.valueOther != '') {
                formObj[elem.labelText] = formObj[elem.labelText] + ',' + elem.valueOther;
              }
              if (i == 1) {
                obj[elem.labelText] = elem.tid;
                this1.machFormKeys.push(elem.labelText);
              }
              if (elem.valueType == 'date') {
                if (elem.timestampForDate && elem.timestampForDate != null && elem.timestampForDate != '') {
                  let newDate = new Date(Number(elem.timestampForDate));
                  formObj[elem.labelText] = moment.utc(newDate).format('DD-MM-YYYY');
                } else {
                  formObj[elem.labelText] = '';
                }
              }
              formObj['keyname'] = elem.keyname;
            });
            formObj['submissionID'] = element.submissionID;
            formObj['slno'] = i;
            formObj['action'] = '';
            this1.formDataList.push(formObj);
            i++;
          });
          this1.machFormIds.push(obj);
          this1.rowData = this1.formDataList;
          this1.setDefaultWidgetConfig(this1.rowData);
        }
        else {
          this1.rowData = [];
          this1.suppressNoRow = false;
          this1.gridApi.showNoRowsOverlay();
          this1.setDefaultWidgetConfig(this1.rowData);
        }
        var server = {
          success: true,
          rows: this1.rowData
        }
        let res = server;
        if (res.success) {
          const rows = res.rows;
          let lastRow = -1;
          if (res.rows.length < this1.cacheBlockSize) {
            lastRow = params.request.startRow + res.rows.length;
          }
          params.successCallback(rows, lastRow);
        } else {
          params.failCallback();
        }
      });
    }
  };
}
function autosizeHeaders(event) {
  const MIN_HEIGHT = 35;
  if (event.finished !== false) {
    event.api.setHeaderHeight(MIN_HEIGHT);
    const headerCells = Array.from(document.querySelectorAll('#grid-wrapper .ag-header-cell-label'));
    let minHeight = MIN_HEIGHT;
    headerCells.forEach((element) => {
      minHeight = Math.max(minHeight, element.scrollHeight);
    });
    event.api.setHeaderHeight(minHeight);
  }
}
