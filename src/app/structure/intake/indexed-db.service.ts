
import { Injectable, OnInit } from '@angular/core';
import <PERSON><PERSON> from 'dexie';

import 'rxjs/add/operator/toPromise';

@Injectable()
export class IndexedDbService {
    private db: any;
    public types = {
        WORKLIST_DATA: 'WORKLIST_DATA',
        LOCATION_DATA: 'LOCATION_DATA',
        INSURANCE_DATA:'INSURANCE_DATA',
        TEAM_DATA: 'TEAM_DATA',
        STATUS_DATA:'STATUS_DATA',
        SITE_DATA: 'SITE_DATA',
        APP_NAME_DATA: 'APP_NAME_DATA',
    }
    public responseData;

    constructor() {
        this.createDatabase();
    }

    createDatabase() {
        this.db = new Dexie('citusPulse');
        this.db.version(1).stores({
            cached_items: 'id,type,value'
        });
    }

    addToIDb(key: string, value: any) {
        const checkifExist = this.getByKeyFromIDb(key);
        if (checkifExist) {
            this.deleteByKeyFromIDb(key).then(() => {
                this.updateByKeyFromIDb(key, value)
                    .then(async () => {
                        const allItems = await this.db.cached_items.toArray();
                        console.log('saved in DB, DB is now', allItems);
                        // this.responseData = allItems;
                        // return this.responseData;
                    })
                    .catch(e => {
                        alert('Error: ' + (e.stack || e));
                    });
            });
        }
    }

    getByKeyFromIDb(key: string) {
        console.log('entered fn')
        const promise = new Promise((resolve, reject) => {      
            this.db.cached_items.get(key)
              .then(res => {
                const response = res.data;
                console.log('getByKeyFromIDb')
                console.log(response)
                resolve(response);
              }).catch(e => {
                console.log('catch getByKeyFromIDb')
                  resolve([]);
              });
          });
          return promise;
    }

    deleteByKeyFromIDb(key: string) {
        return this.db.cached_items.delete(key)
    }

    updateByKeyFromIDb(key: string, value: any) {
        return this.db.cached_items.put({ id: key, data: value });
    }
}
