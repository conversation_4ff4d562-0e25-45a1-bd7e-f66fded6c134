<section class="card">
   <div class="card-header">
      <div class="row">
         <div class="col-md-12">
            <div class="col-md-2" style="float: left;">
               <span class="cat__core__title">
                  <strong>App Center</strong>          
               </span>
            </div>
         </div>
      </div>
   </div> 
 
  <div class="card-block">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
      <li class="breadcrumb-item">App Center</li>
    </ol>
  </div>
   <div class="card-block">
<div class="wait-loading" *ngIf="dataLoadingMsg">
         <img src="./assets/img/loader/loading.gif" />
      </div>
      <div class="row" *ngIf="!dataLoadingMsg">
         <div class="col-md-12">
         <div class="tab-content">
            <div class="tab-pane active" id="home1" role="tabcard">
               <div class="row">
                  
                     <div class="col-lg-4"  *ngFor="let appName of appNames">
                           <div *ngIf="flag">
                           <div class="cat__core__widget" (click)="loadloggedInblock(appName)">
                              <div class="cat__core__step cat__core__step--pulse appscenter-card">
                                 <span class="cat__core__step__digit" style="width:100px">
                                    <img class="pulse-icon" width="100px" src="{{appName.iconImagePath}}{{appName?.imageName}}"
                                    onerror="this.src='assets/modules/dummy-assets/common/img/pulse.png'">
                                 </span>
                                 <div class="cat__core__step__desc">
                                    <p class="app-title">
                                       <span class="cat__core__step__title app-name">{{(appName.name.length > 40 )? (appName.name | slice:0:40)+'...':(appName.name)}}</span>
                                       <span class="custom-tooltip-name" *ngIf="appName.name.length > 40">{{ appName.name }}</span>
                                    </p>
                                    <p class="app-desc">    
                                       <span class="custom-tooltip" *ngIf="appName.description.length > 55">{{ appName.description }}</span>
                                       {{ (appName.description.length > 55 )? (appName.description | slice:0:55)+'...':(appName.description) }}
                                    </p>

                                    <!-- <span>Test 
                                        <i class="fa fa-info-circle" aria-hidden="true"></i>     
                                      
                                    </span> -->

                                    <!-- <span class="app-name-desc">{{appName.description}}</span>  -->
                                 </div>
                              </div>
                           </div>
                        </div> 
                  </div>
                  <div *ngIf="!flag">
                     No Apps found...
               </div>
                  </div>
               
            </div>
         </div>
         </div>
      </div>
   </div>
 <!-- Modal -->
 <div class="modal fade" id="exampleModalCenter" tabindex="-1" role="dialog" aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
   <div class="modal-dialog modal-dialog-centered" role="document">
     <div class="modal-content">
       <div class="modal-header">
         <h5 class="modal-title" id="exampleModalCenterTitle">App Center</h5>
         <button type="button" class="close" data-dismiss="modal" aria-label="Close">
           <span aria-hidden="true">&times;</span>
         </button>
       </div>
       <div class="modal-body">
         <div class="row">
            <div class="col-sm-12 text-center">
               <img src="" id="app-image" width="150"/>
            </div>
        </div>
        <div class="row">
         <div class="col-sm-12 text-center">
            <p id="app-name"></p>
         </div>
        </div>
        <div class="row">
         <div class="col-sm-12 text-center">
            <p id="app-desc"></p>
         </div>
        </div>
       </div>
       <div class="modal-footer-custom">
         <div class="row">
            <div class="col-sm-6 text-right">
               <button type="button" class="btn btn-cancel" data-dismiss="modal">Cancel</button>
            </div>
            <div class="col-sm-6 text-left">
               <button type="button" class="btn btn-continue" (click)="continueToApp()">Continue</button>
            </div>
         </div>
       </div>
     </div>
   </div>
 </div>
 </section>

 
  
  
  
  
 
 