

.form-trends table {
   width: 100%;
 }
 
 .form-trends th {
   color: #52b3aa;
   height: 35px;
   padding: 5px;
 }
 
 .form-trends td {
   color: #555;
   border: 1px solid #acb7bf;
   padding: 5px;
 }
 
 .row.form-trends {
   margin-top: 25px;
   margin-bottom: 10px;
 }
 
 .filter-reset-section {
   margin-top: 15px;
 }
 
 .formtype-tooltip {
   position: absolute;
   width: auto;
   right: 10px;
 }
 
 .formtype-section-tooltip {
   top: 5px;
 }
 
 
 /*layout styles*/
 
 .pulse-dashboard {
   background-color: #fff;
   border: 1px solid #e7e8ec;
 }
 
 .pulse-dashboard .breadcrumb-block .breadcrumb {
   background-color: #f3f4f8!important;
   border: 1px solid #fdfeff!important;
   -webkit-border-radius: 3px;
   -moz-border-radius: 3px;
   border-radius: 3px;
 }
 
 .pulse-dashboard .card-header {
   border-bottom: 1px solid #e7e8ec!important;
 }
 
 
 /*   
     .pulse-dashboard .pulse-icon-block .app-name-desc{
        color:#393744;
     }
     .pulse-dashboard .pulse-icon-block .app-name-desc{
        color:#0097d7;
      
     } */

 .app-background {
   background-color: #f3f4f8!important;
 }
 .cat__core__step{
     padding: 18px 20px;
 }
 .appscenter-card {
   cursor: pointer;
   width: 90%;
   height: 125px; 
   display: flex;
   background-color: #f7f7f7!important;
   border: 2px solid #fff;   
 }
   .cat__core__step--pulse {
   background-color: #f7f7f7!important; 
   -webkit-box-shadow: 3px 3px 5px 6px #fefefe;
   -moz-box-shadow:    3px 3px 5px 6px #fefefe;
   box-shadow:         3px 3px 5px 6px #fefefe;   
   }
   .cat__core__step__desc{
       padding-top: 0px; 
       float: left;
       display: block;
       width: 80%;
   }
   
   .cat__core__step__digit{
     /* width: 20%; */
     display: flex;
   margin-right:0.42rem ;
   }
   .app-desc {	
      color: #009ae4;	
      font-weight: bold;	
      font-size: 0.9vw;	
      /* display: flex;	 */
   }
   .custom-tooltip-name{
    font-size: 12px;	
   }
   .app-title .custom-tooltip-name {
    visibility: hidden;
    width: 284px;
    background-color: #edeaea;
    color: black;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 85%;
    margin-left: -60px;
    border: 1px solid #ccc;
    font-weight: normal;
   }
   .app-title .custom-tooltip-name::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 29%;
    margin-left: -10px;
    border-width: 8px;
    border-style: solid;
    border-color: #edeaea transparent transparent transparent;
  }
  .cat__core__step__title {
    font-size:1.14vw !important;
  }
  .app-title:hover .custom-tooltip-name {
    visibility: visible;
  }
  p.app-title:hover > .custom-tooltip {
    display: block!important;
  }
  .app-desc .custom-tooltip {
    visibility: hidden;
    width: 284px;
    background-color: #edeaea;
    color: black;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 85%;
    margin-left: -60px;
    border: 1px solid #ccc;
    font-weight: normal;
  }
  .app-desc .custom-tooltip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 29%;
    margin-left: -10px;
    border-width: 8px;
    border-style: solid;
    border-color: #edeaea transparent transparent transparent;
  }
  
  .app-desc:hover .custom-tooltip {
    visibility: visible;
  }
p.app-desc {
   cursor: pointer;
}
p.app-desc:hover > .custom-tooltip {
    display: block!important;
}
.modal-header{
  border-bottom: 1px solid #ccc;
}
.modal-title{
  font-weight: bold;
}
#app-name{
  font-size: 16px;
  font-weight: bold;
}
.modal-footer-custom{
  padding-bottom: 20px;
}
.btn-cancel{
  color:#fff;
  background-color: #acb7bf;
  border: 1px solid #acb7bf;
  border-radius: 20px;
  width: 120px;
}
.btn-continue{
  color:#fff;
  background-color: #f5a751;
  border: 1px solid #f5a751;
  border-radius: 20px;
  width: 120px;
}
.modal-dialog-centered{
  top: 15%;
}