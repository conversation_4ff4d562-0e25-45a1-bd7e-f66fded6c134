
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,EventEmitter, Input } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../../structure.service';
import { FormsService } from '../../forms/forms.service';
import { InboxService } from '../../inbox/inbox.service';
import { DatePipe } from '@angular/common';
import { ToolTipService } from '../../tool-tip.service';
import { WorkListService } from '../../worklists/worklist.service';
import { IntakeService } from '../intake.service';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chart: any;
declare var moment: any;
declare var swal: any;
@Component({
  selector: 'app-apps-center',
  templateUrl: './apps-center.component.html',
  styleUrls: ['./apps-center.component.css']
})
export class AppsCenterComponent implements OnInit {

  public loadDocumentActivityData: EventEmitter<boolean> = new EventEmitter();
  @Input('dynamicData') dynamicData: any;
  @Input() worklistEditData: any;
  appNames: any;
  appName: string;
  flag = true;
  dataLoadingMsg: Boolean;
  imgNull = "assets/modules/dummy-assets/common/img/pulse.png";
  appDetail: any;
  // worklistMenuPulse;
  constructor(
    private _structureService: StructureService,
    private route: ActivatedRoute,
    private router: Router,
    private _formsService: FormsService,
    private _inboxService: InboxService,
    private datePipe: DatePipe,
    public _workListService: WorkListService,
    public _intakeService:IntakeService,
    elementRef: ElementRef,
    renderer: Renderer,
    private _ToolTipService: ToolTipService,
  ) {
    this.dataLoadingMsg = true;
  }

  ngOnInit() {
    console.log(this.dynamicData);
    console.log(this.worklistEditData);
    this.getAppNames();
    console.log(this._intakeService.worklistMenuPulse);
  // alert(this.flag);
   console.log(this.flag) 
   
  }
  
  loadloggedInblock(appName){
    $( "#whologgedinid" ).trigger( "click" );
  
    this.loadPulseData(appName);
  }
  getAppNames(){
   if(!localStorage.getItem('appNames')) {
    this._intakeService.getAllAppNames().then((data) => {
      localStorage.setItem('appNames', JSON.stringify(data['apps']));
      console.log(data);
      this.dataLoadingMsg = false;
      this.appNames = data['apps'];
      console.log(this.appNames)
      if (this.appNames.length > 0) {
        this.flag = true;
      }
      else {

        this.flag = false;
        // console.log(this.appNames)
      }
    });
   } else {
    console.log(localStorage.getItem('appNames'));
    this.dataLoadingMsg = false;
    this.appNames = JSON.parse(localStorage.getItem('appNames'));
    if (this.appNames.length > 0) {
      this.flag = true;
    }
    else {
      this.flag = false;
    }
   }
}
worklistmenu(actionLink) {
  console.log(actionLink);

  this._structureService.appCenterGuid = localStorage.getItem('appGuid');
  //var res = actionLink.split('/');
  var url = 'apps/' + this._structureService.appCenterGuid + '/';
  var str = actionLink.replace('worklist/', url);
  this._structureService.displayProgress.emit(false);
  this.router.navigateByUrl(str);
}
loadMenu(app){ 
    this._intakeService.parentWorklistMenu = '';                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       
		localStorage.setItem('appGuid', app.guid);
		this._structureService.appCenterGuid = app.guid;
    localStorage.setItem('appName', app.name);
    this.appName = localStorage.getItem('appName');
    this._intakeService.menuAppName = this.appName;
    this._intakeService.breadcrumbAppName = this.appName;
    this._intakeService.getWorklistMenuPulse(app.guid, null).then((data) => {
      if(data && data.length > 0) {
        const worklistMenuPulse = data[0].worklists[0].actionLink;
        this._structureService.displayProgress.emit(false);
        localStorage.setItem('loadFirstDashboard', 'true');
        this.router.navigateByUrl(worklistMenuPulse);
      } else {
        const userData = this._structureService.userDetails?JSON.parse(this._structureService.userDetails):{};
        swal(
          {
            title: '',
            text: "No dashboards configured for this user's role : "+ userData.roleName,
            type: 'warning',
            confirmButtonClass: 'btn-warning',
            confirmButtonText: 'Ok',
            closeOnConfirm: true
          });
      }
      
    });
  
    
    // this.router.navigateByUrl(this.worklistMenuPulse);
		// window.location.reload();
}
  loadPulseData(appName){
    this.appDetail = appName;
		var img = 'assets/modules/dummy-assets/common/img/pulse.png';
		if (appName.imageName) {
			img = appName.iconImagePath + appName.imageName;
    }
    $('#exampleModalCenter').on('show.bs.modal', function(event) {
      $('#app-image').attr("src",img);
      $('#app-name').html('<h3>' + appName.name + '</h3>');
      $('#app-desc').html('Please click continue to launch the app');
    });
    $('#exampleModalCenter').modal(
      {
        backdrop: 'static', 
        keyboard: false
      }
    );
  }

  continueToApp(){
    
    $('#exampleModalCenter').modal('hide');
    this.loadMenu(this.appDetail);
  }
}

 