import { DatePipe } from '@angular/common';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Apollo, ApolloModule } from 'apollo-angular';
import { FormsService } from 'app/structure/forms/forms.service';
import { InboxService } from 'app/structure/inbox/inbox.service';
import { AuthService } from 'app/structure/shared/auth.service';
import { SharedService } from 'app/structure/shared/sharedServices';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { WorkListService } from 'app/structure/worklists/worklist.service';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { provideClients } from 'test-utils';
import { IntakeService } from '../intake.service';

import { AppsCenterComponent } from './apps-center.component';

describe('AppsCenterComponent', () => {
  let component: AppsCenterComponent;
  let fixture: ComponentFixture<AppsCenterComponent>;
  let formservice: FormsService;
  let inboxservice: InboxService;
  let workservice: WorkListService;
  let formServiceFake = jasmine.createSpyObj('structureMethod', ['getCookie']);
  beforeEach(async(() => {
    TestBed.configureTestingModule({
      providers: [
        StructureService,
        Apollo,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        TranslateService,
        { provide: FormsService, useValue: formservice },
        { provide: InboxService, useValue: inboxservice },
        DatePipe,
        { provide: WorkListService, useValue: workservice },
        IntakeService,
      ],
      declarations: [AppsCenterComponent],
      imports: [
        RouterTestingModule,
        HttpModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF,
        }),
        TranslateModule.forRoot(),
      ],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AppsCenterComponent);
    component = fixture.componentInstance;
    let _strService = fixture.debugElement.injector.get(StructureService);
    // component.params = { tenantId: "1"}
    let configData_string = JSON.stringify({
      date: '20220325',
      dayNumber: '5',
      userContactVerification: { mobileVerified: 0, emailVerified: 0 },
    });
    _strService.configData = configData_string;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
});
