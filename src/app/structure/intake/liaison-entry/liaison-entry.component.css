input[type=text]{
  border: 1px solid #ccc;  
  padding: 8px;
}
textarea{
  padding: 10px;
  border: 1px solid #ccc;
  outline: none;
} 
select.patient-location { 
  border: 1px solid #ccc;  
  padding: 8px 8px;
 }
 .patient-location{
	padding: 8px!important;
}
.form-check {  
  position: relative;
  display: block;
  margin-bottom: .5rem;
}
.form-check-inline{
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  padding-left: 0;
  margin-right: .75rem;
}
.form-check-label {
  padding-left: 2px!important;
  margin-bottom: 0;
}
.form-row {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  margin-right: -5px;
  margin-left: -5px;
}
/*
.mydp .btnpicker{
  width:20px!important;
}
.form-group{
  margin-bottom: 8px!important;
}*/
.form-check-inline{
  align-items: unset;
} 
.edit-form .row{
  margin-bottom: 5px;
}
.radio-chkbox-container{
padding:10px  0 0 0;
} 

.time-format {
    border: 1px solid #ccc;
    padding: 6px;
    border-radius: 4px;
}

.date-time input {
  font-size: 0.97rem !important;
  border: 1px solid #d1d1d1 !important;
}

.date-time button {
  font-size: 0.97rem !important;
  border: 1px solid #d1d1d1 !important;
}

.datepicker-close {
  padding-bottom: 0.37rem !important;
  font-size: 18px !important;
  border: 1px solid #d1d1d1;
  border-left: none;
  padding-top: 3px !important;
}

.edit-form {
  min-height: 625px;
}

.rphtime {
  padding: 8px;
}

