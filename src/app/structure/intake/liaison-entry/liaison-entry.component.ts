import { Component, OnInit, Input, SimpleChange, OnChanges } from '@angular/core';
import { StructureService } from '../../structure.service';
import 'ag-grid-enterprise';
import { IntakeService } from '../intake.service';
const moment = require('moment/moment');
import { IMyDpOptions } from 'mydatepicker';
import { IndexedDbService } from '../indexed-db.service';
import { ConstantService } from '../constant.service';
declare const $: any;
declare const swal: any;
@Component({
  selector: 'app-liaison-entry',
  templateUrl: './liaison-entry.component.html',
  styleUrls: ['./liaison-entry.component.css']
})
export class LiaisonEntryComponent implements OnChanges {
  @Input() worklistEditData;
  @Input() entryFormType;
  @Input('form') editLiaisonEntryForm;
  locationList;
  dropdownSettingsLocation = {};
  dropdownListLocation = [];
  selectedItemsLocation = [];
  worklistEditDataValue: any;
  formType: any;
  public myDatePickerOptions: IMyDpOptions = {
    dateFormat: 'mm/dd/yyyy',
    firstDayOfWeek: 'su'
  };
  locationData = this._indexedDbService.types.LOCATION_DATA;
  complete: boolean;
  showDateError: boolean;
  tcRequested;
  deliveryRequested;
  confirmedDelivery;
  showInLiaison = false;
  showInIntake = false;
  showInPharmacy = false;
  constructor(
    public _structureService: StructureService,
    private _intakeService: IntakeService,
    private _indexedDbService: IndexedDbService,
    public constantService: ConstantService
  ) {
  }
  ngOnChanges(changes: { [propKey: string]: SimpleChange }) {
    if (changes['worklistEditData'] || changes['editLiaisonEntryForm']) {
      this.worklistEditDataValue = this.worklistEditData[0];
      this.formType = this.entryFormType;
    }
    switch (this.formType) {
      case this.constantService.entryTypes['LIAISON']:
        this.showInLiaison = true;
        this.editLiaisonEntryForm.get('noMixOrDeliveryRequired').disable();
        this.editLiaisonEntryForm.get('excludedFromTcCutoffTime').disable();
        break;
      case this.constantService.entryTypes['INTAKE']:
        this.showInIntake = true;
        break;
      case this.constantService.entryTypes['PHARMACY_FOLLOWUP']:
        this.showInPharmacy = true;
        break;    
      default:
        break;
    }
    let nextFollowupDate = null;
    let hospitalDischargeDate = null;
    if (this.editLiaisonEntryForm.get('nextFollowup').value) {
      nextFollowupDate = this.convertDate(this.editLiaisonEntryForm.get('nextFollowup').value);
    }
    if (this.editLiaisonEntryForm.get('hospitalDischarge').value) {
      hospitalDischargeDate = this.convertDate(this.editLiaisonEntryForm.get('hospitalDischarge').value);
    }
    let teachComplete = null;
    if (this.editLiaisonEntryForm.get('teachComplete').value == 'NA') {
      teachComplete = 0;
    }else if (this.editLiaisonEntryForm.get('teachComplete').value == 'Yes') {
      teachComplete = 1;
    }
    let hospitalDischargeApproved = null;
    if (this.editLiaisonEntryForm.get('hospitalDischargeApproved').value == 'No') {
      hospitalDischargeApproved = false;
    } else if (this.editLiaisonEntryForm.get('hospitalDischargeApproved').value == 'Yes') {
      hospitalDischargeApproved = true;
    }
    let hospitalChart = null;
    if (this.editLiaisonEntryForm.get('hospitalChart').value == 'NA') {
      hospitalChart = 0;
    } else if (this.editLiaisonEntryForm.get('hospitalChart').value == 'Yes') {
      hospitalChart = 1;
    }
    if (this.editLiaisonEntryForm.get('tcRequested').value != null) {
      this.tcRequested = new Date(this.editLiaisonEntryForm.get('tcRequested').value);
    }
    if (this.editLiaisonEntryForm.get('deliveryRequested').value != null) {
      this.deliveryRequested = new Date(this.editLiaisonEntryForm.get('deliveryRequested').value);
    }
    if (this.editLiaisonEntryForm.get('timeCommitment').value != null) {
      this.confirmedDelivery = new Date(this.editLiaisonEntryForm.get('timeCommitment').value);
    }
    this.editLiaisonEntryForm.patchValue({
      nextFollowup: nextFollowupDate,
      teachComplete: teachComplete,
      hospitalDischarge: hospitalDischargeDate,
      hospitalChart: hospitalChart,
      hospitalDischargeApproved: hospitalDischargeApproved
    });
    this._structureService.socket.off('updateIndexedDb').on('updateIndexedDb', data => {
      switch (data.type) {
        case this.locationData:
          this.getLocationApi();
          break;
      }
    });
    this.getLocationApi();
  }
  convertTimeFormat(time) {
    if (!time) {
      return '';
    }
    if (time || time != '') {
      return moment(time).format('HH:mm:ss');
    }
  }
  convertDate(d) {
    if (d !== null || d) {
      const date1 = new Date(Number(d));
      const ngxDate: any = {
        date: { year: date1.getFullYear(), month: date1.getMonth() + 1, day: date1.getDate() },
        formatted: this.formatDate(date1)
      };
      return ngxDate;
    } else {
      return null;
    }
  }
  formatDate(date) {
    const d = new Date(date);
    let month = '' + (d.getMonth() + 1);
    let day = '' + d.getDate();
    const year = d.getFullYear();
    const hour = '0' + d.getHours();
    const minute = '0' + d.getMinutes();
    if (month.length < 2) {
      month = '0' + month;
    }
    if (day.length < 2) {
      day = '0' + day;
    }
    const dateVal = [year, month, day].join('/');
    return dateVal + ' ' + hour + ':' + minute;
  }
  async getLocation() {
    let dataFromIdb: any = [];
    await this._indexedDbService.getByKeyFromIDb(this.locationData).then(
      (response) => {
        dataFromIdb = response;
      });
    if (!dataFromIdb.length) {
      this.getLocationApi();
    } else {
      this.locationList = dataFromIdb;
    }
  }
  getLocationApi() {
    this._intakeService.getLocationList().then((data) => {
      this.locationList = data['getLocations'];
      this.dropdownListLocation = this.locationList;
      this._indexedDbService.addToIDb(this.locationData, data['getLocations']);
      this.selectedItemsLocation = [];
      this.dropdownSettingsLocation = {
        singleSelection: true,
        idField: 'id',
        textField: 'locationName',
        enableSearchFilter: true,
        allowSearchFilter: true,
      };
      let locationObj: any = [];
      if (this.worklistEditDataValue.locationId != null && this.worklistEditDataValue.locationName != null) {
        locationObj.push({
          id: this.worklistEditDataValue.locationId,
          locationName: this.worklistEditDataValue.locationName
        });
      } else {
        locationObj = '';
      }
      this.selectedItemsLocation = locationObj;
    });
  }
  ngOnDestroy() {
    this._structureService.socket.off('updateIndexedDb');
  }
  showLiaisonValidationMessage() {
    if (this.editLiaisonEntryForm.value['teachComplete'] == 0 && this.editLiaisonEntryForm.value['teachReason'].trim() == '') {
      const elm = this;
      const liaisonData = this.editLiaisonEntryForm.value;
      swal({
        title: '',
        text: 'Please fill Teach Reason before Liaison complete',
        type: 'warning',
        customClass: 'swal-size-sm',
      },
        (confirm) => {
          if (confirm) {
            $('#liaison').prop('checked', false);
            liaisonData.liaisonComplete = false;
            elm.editLiaisonEntryForm.patchValue({
              liaisonComplete: false
            });
          }
        });
    }
  }
  showIntakeValidationMessage() {
    if (this.editLiaisonEntryForm.value['teachComplete'] == 0 &&
      this.editLiaisonEntryForm.value['teachReason'] == '') {
      const elm = this;
      const intakeData = this.editLiaisonEntryForm.value;
      swal({
        title: '',
        text: 'Please fill Teach Reason before SOC Approved',
        type: 'warning'
      },
        (confirm) => {
          if (confirm) {
            $('#intakeSoc').prop('checked', false);
            intakeData.hospitalDischargeApproved = false;
            elm.editLiaisonEntryForm.patchValue({
              hospitalDischargeApproved: false
            });
          }
        });
    }
  }
  clearDateInput(field) {
    if (field === 'tcRequested') {
      this.tcRequested = null;
      this.editLiaisonEntryForm.controls['tcRequested'].value = this.tcRequested;
      this.editLiaisonEntryForm.controls['tcRequested'].markAsDirty();
    }
    if (field === 'deliveryRequested') {
      this.deliveryRequested = null;
      this.editLiaisonEntryForm.controls['deliveryRequested'].value = this.deliveryRequested;
      this.editLiaisonEntryForm.controls['deliveryRequested'].markAsDirty();
    }
    if (field === 'timeCommitment') {
      this.confirmedDelivery = null;
      this.editLiaisonEntryForm.controls['timeCommitment'].value = this.confirmedDelivery;
      this.editLiaisonEntryForm.controls['timeCommitment'].markAsDirty();
    }
  }
}
