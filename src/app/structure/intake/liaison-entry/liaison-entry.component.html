
<form class="edit-form" [formGroup]="editLiaisonEntryForm">
    <div class="col-md-6" *ngIf="showInLiaison">
        <div class="row mb-5">
            <label class="col-sm-7" style="font-weight:bold; font-size:18px;padding-left:1px">{{ 'LABELS.LIAISONS_COMPLETE' | translate }}</label>
            <div class="col-sm-5">
                <input type="checkbox" value="" formControlName="liaisonComplete" id="liaison" class="mt-2"
                    (click)="showLiaisonValidationMessage()" [checked]="editLiaisonEntryForm.value['liaisonComplete']==true">
            </div>
        </div>
    </div>
    <div class="row" *ngIf="formType != 'PHARMACY'">
        <div class=" col-md-6" *ngIf="showInLiaison">
            <div class="row">
                <div class="col-sm-5">
                    <label>{{ 'LABELS.ROOM_NUMBER' | translate }}</label>
                </div>
                <div class="col-sm-7">
                    <input type="text" formControlName="roomNumber" class="form-control"></div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="row">
                <div class="col-sm-5">
                    <label>{{ 'LABELS.PATIENT_LOCATION' | translate }}</label>
                </div>
                <div class="col-sm-7">
                    <ng-multiselect-dropdown [placeholder]="'Select Options'" [data]="dropdownListLocation"
                        [(ngModel)]="selectedItemsLocation" [settings]="dropdownSettingsLocation"
                        formControlName="locationId">
                    </ng-multiselect-dropdown>
                </div>
            </div>
        </div>
        <div class="col-md-6" *ngIf="showInLiaison">
            <div class="row">
                <div class="col-sm-5">
                    <label>{{ 'LABELS.NEXT_FOLLOWUP_DATE' | translate }}</label>
                </div>
                <div class="col-sm-7 discharge-date">
                    <my-date-picker name="nextFollowup" formControlName="nextFollowup" [options]="myDatePickerOptions">
                    </my-date-picker>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="row">
                <div class="col-sm-5">
                    <label>{{ 'LABELS.CASE_MANAGER' | translate }}</label>
                </div>
                <div class="col-sm-7"><input type="text" formControlName="insCoordinator" class="form-control"></div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="row">
                <div class="col-sm-5">
                    <label>{{ 'LABELS.HOSPITAL_DISCHARGE' | translate }}</label>
                </div>
                <div class="col-sm-7 discharge-date">
                    <my-date-picker formControlName="hospitalDischarge" [options]="myDatePickerOptions">
                    </my-date-picker>
                </div>
            </div>
        </div>
        <div class="col-md-6" *ngIf="showInLiaison">
            <div class="row">
                <div class="col-sm-5">
                    <label>{{ 'LABELS.PW_HOSPITAL_CHART' | translate }}</label>
                </div>
                <div class="col-sm-7">
                    <div class="form-check-inline">
                        <input class="mr-1" formControlName="hospitalChart" type="radio" id="R1"
                            [value]="0"> NA
                    </div>
                    <div class=" form-check-inline">
                        <input class="mr-1" formControlName="hospitalChart" type="radio" id="R2"
                            [value]="1"> Y
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6" *ngIf="showInLiaison">
            <div class="row">
                <div class="col-sm-5">
                    <label>{{ 'LABELS.VNA_VERIFIED' | translate }}</label>
                </div>
                <div class="col-sm-7">
                    <div class="form-check">
                        <input class="mt-1" formControlName="vnaVerified" type="checkbox" value=""
                            id="defaultCheck1" [checked]="editLiaisonEntryForm.value['vnaVerified']==true">
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6" *ngIf="showInLiaison">
            
        </div>
      
        <div class="col-md-6" *ngIf="showInIntake">
            <div class="row">
                <div class="col-sm-5">
                    <label>{{ 'LABELS.INSURANCE_POLICY' | translate }}</label>
                </div>
                <div class="col-sm-7"><input type="text" formControlName="insurancePolicy"
                        class="form-control float-left w-100 case-manager right-input"></div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="row">
                <div class="col-sm-5">
                    <label>{{ 'LABELS.TEACHING_COMPLETE' | translate }}</label>
                </div>
                <div class="col-sm-7">
                    <div class="form-check-inline">
                        <input class="mr-1" formControlName="teachComplete" type="radio" id="R1"
                            [value]="0"> NA
                    </div>
                    <div class=" form-check-inline">
                        <input class="mr-1" formControlName="teachComplete" type="radio" id="R2"
                            [value]="1"> Y
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="row" *ngIf="!editLiaisonEntryForm.get('teachComplete').value && editLiaisonEntryForm.get('teachComplete').value !==null">
                <div class="col-sm-5">
                    <label>{{ 'LABELS.TEACHING_REASON' | translate }}</label>
                </div>
                <div class="col-sm-7">
                    <textarea class="form-control" formControlName="teachReason" rows="2"></textarea>
                </div>
            </div>
        </div>
        
        <div class=" col-md-6" *ngIf="showInLiaison || showInIntake">
            <div class="row">
                <div class="col-sm-5">
                    <label>{{ 'LABELS.TIME_TC_EMAIL_SENT' | translate }}</label>
                </div>
                <div class="col-sm-7" style="display:inherit;">
                        <span class="full-width-date-time">
                    <ngx-datetime-picker [doNotCloseOnDateSet]="true" [disablePicker]="showInLiaison" [disableInput]="true" formControlName="tcRequested" [use24HourClock]="true" [(selectedDateTime)]="tcRequested"></ngx-datetime-picker>
                </span>
                    <span>
                        <button type="button" class="datepicker-close" [disabled]="showInLiaison" (click)="clearDateInput('tcRequested')"><i
                                class="fa fa-close"></i></button>
                    </span>
                </div>
            </div>
        </div>
        <div class=" col-md-6" *ngIf="showInLiaison || showInIntake">
            <div class="row">
                <div class="col-sm-5">
                    <label>{{ 'LABELS.DELIVERY_REQUESTED' | translate }}</label>
                </div>
                <div class="col-sm-7" style="display:inherit;">
                        <span class="full-width-date-time">
                    <ngx-datetime-picker [doNotCloseOnDateSet]="true" [disablePicker]="showInLiaison" [disableInput]="true" formControlName="deliveryRequested" [use24HourClock]="true" [(selectedDateTime)]="deliveryRequested" ></ngx-datetime-picker>
                        </span>
                    <span>
                        <button type="button" class="datepicker-close" [disabled]="showInLiaison" (click)="clearDateInput('deliveryRequested')"><i
                                class="fa fa-close"></i></button>
                    </span>
                </div>
            </div>
        </div>
        <div class=" col-md-6" *ngIf="showInLiaison || showInIntake">
            <div class="row">
                <div class="col-sm-5">
                    <label>{{ 'LABELS.CONFIRMED_DELIVERY' | translate }}</label>
                </div>
                <div class="col-sm-7" style="display:inherit;">
                        <span class="full-width-date-time">
                    <ngx-datetime-picker [doNotCloseOnDateSet]="true" [disablePicker]="showInLiaison" [disableInput]="true" formControlName="timeCommitment" [use24HourClock]="true" [(selectedDateTime)]="confirmedDelivery" ></ngx-datetime-picker>
                        </span>
                    <span>
                        <button type="button" class="datepicker-close" [disabled]="showInLiaison" (click)="clearDateInput('timeCommitment')"><i
                                class="fa fa-close"></i></button>
                    </span>
                </div>
            </div>
        </div>
        <div class=" col-md-6" *ngIf="showInLiaison || showInIntake">
            <div class="row">
                <div class="col-sm-5">
                    <label>{{ 'LABELS.NO_MIX_DELIVERY_REQUIRED' | translate }}</label>
                </div>
                <div class="col-sm-7">
                    <input class="mt-1" formControlName="noMixOrDeliveryRequired" type="checkbox"
                            id="defaultCheck1">
                </div>
            </div>
        </div>
        <div class=" col-md-6" *ngIf="showInLiaison || showInIntake">
            <div class="row">
                <div class="col-sm-5">
                    <label>{{ 'LABELS.EXCLUDE_FROM_TC_CUTOFF_TIME' | translate }}</label>
                </div>
                <div class="col-sm-7">
                    <input class="mt-1" formControlName="excludedFromTcCutoffTime" type="checkbox"
                            id="defaultCheck1" >
                </div>
            </div>
        </div>
        <div class="col-md-6" *ngIf="showInIntake">
            <div class="row">
                <div class="col-sm-5">
                    <label>{{ 'LABELS.SOC_APPROVED' | translate }}</label>
                </div>
                <div class="col-sm-7">
                    <input type="checkbox" value="" formControlName="hospitalDischargeApproved" id="intakeSoc"
                                class="mt-1" (click)="showIntakeValidationMessage()"
                                [checked]="editLiaisonEntryForm.value['hospitalDischargeApproved']==true">
                </div>
            </div>
        </div>
    </div>
    <div class="row" *ngIf="showInPharmacy">
        <div class="col-md-5 mb-5">
            <label>{{ 'LABELS.PHARMACY_REVIEW_COMPLETE' | translate }}</label>
        </div>
        <div class="col-md-7 mb-5">
            <input type="checkbox" formControlName="pharmacyReviewComplete"></div>
    
        <div class="col-sm-5">
            <label>{{ 'LABELS.RPH_REVIEW_TIME' | translate }}</label>
        </div>
        <div class="col-sm-7 row">
            <div class="col-md-4">
                <select id="hour" class="form-control rphtime" formControlName="rph_hour">
                    <option value="">{{ 'LABELS.SELECT' | translate }}</option>
                    <option value="{{hour}}" *ngFor="let hour of constantService.hours">{{hour}}</option>
                </select>
            </div>
            <div class="col-md-2 pl-0">
                <label for="hour" class="pt-2">{{ 'LABELS.HOUR' | translate }}</label>
            </div>
            <div class="col-md-4">
                <select id="minute" class="form-control rphtime" formControlName="rph_minute">
                    <option value="">{{ 'LABELS.SELECT' | translate }}</option>
                    <option value="{{minute}}" *ngFor="let minute of constantService.minutes">{{minute}}</option>
                </select>
            </div>
            <div class="col-md-2 pl-0">
                <label for="minute" class="pt-2">{{ 'LABELS.MINUTE' | translate }}</label>
            </div>
        </div>
    </div>
</form>
