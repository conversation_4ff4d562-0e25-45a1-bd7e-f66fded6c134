import { Http<PERSON><PERSON>, <PERSON>ttpHandler } from '@angular/common/http';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Apollo, ApolloModule } from 'apollo-angular';
import { FormsService } from 'app/structure/forms/forms.service';
import { AuthService } from 'app/structure/shared/auth.service';
import { SharedService } from 'app/structure/shared/sharedServices';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { provideClients } from 'test-utils';
import { IndexedDbService } from '../indexed-db.service';
import { IntakeService } from '../intake.service';

import { LiaisonEntryComponent } from './liaison-entry.component';

describe('LaisanDetailsComponent', () => {
  let component: LiaisonEntryComponent;
  let fixture: ComponentFixture<LiaisonEntryComponent>;
  let formservice: FormsService;
  beforeEach(async(() => {
    TestBed.configureTestingModule({
      providers:[HttpClient,HttpHandler,StructureService,Apollo,AuthService,SharedService,WorklistIndexdbService,ToolTipService,
        { provide: FormsService, useValue: formservice },IntakeService,IndexedDbService],
      declarations: [ LiaisonEntryComponent ],
      imports:[RouterTestingModule,HttpModule,ApolloModule.forRoot(provideClients),FormsModule,ReactiveFormsModule,
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF
        }), TranslateModule.forRoot()],
      schemas:[NO_ERRORS_SCHEMA],
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(LiaisonEntryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
});
