import { <PERSON><PERSON>nent, On<PERSON>nit, <PERSON>ementRef, <PERSON><PERSON>er, EventEmitter, Input, ViewChild, Output } from '@angular/core';
import { StructureService } from '../../structure.service';
import { InboxService } from '../../inbox/inbox.service';
import { DatePipe } from '@angular/common';
import { ToolTipService } from '../../tool-tip.service';
import { FormsService } from '../../forms/forms.service';
import { Router, ActivatedRoute, Params, NavigationEnd } from '@angular/router';
import gql from 'graphql-tag';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { configTimeZone } from '../../../../environments/environment';
import { Http, Headers, RequestOptions } from '@angular/http';
import { Apollo } from 'apollo-angular';
import { HttpClient } from "@angular/common/http";
import { FormsModule, FormBuilder, FormArray, ReactiveFormsModule } from '@angular/forms';
import 'ag-grid-enterprise';
import { IntakeService } from '../intake.service';
import { IntakeWorkListService } from '../intake-worklist/intake-worklist.service';
let moment = require('moment/moment');
import { IMyDpOptions, IMyDateModel } from 'mydatepicker';
import { IfObservable } from 'rxjs/observable/IfObservable';
import { DaterangepickerConfig } from 'ng2-daterangepicker';
import { IndexedDbService } from '../indexed-db.service';
import { IDropdownSettings } from 'ng-multiselect-dropdown';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var swal: any;
declare var NProgress: any
@Component({
  selector: 'app-advance-search',
  templateUrl: './advance-search.component.html',
  styleUrls: ['./advance-search.component.css']
})
export class AdvanceSearchComponent implements OnInit {

  advanceSearchForm: FormGroup;
  locationList;
  statusList;
  teamList;
  dropdownSettings = {};
  dropdownList = [];
  selectedItems = [];
  dropdownSettingsStatus = {};
  dropdownListStatus = [];
  selectedItemsStatus = [];
  dropdownSettingsSite = {};
  dropdownListSite = [];
  selectedItemsSite = [];
  dropdownSettingsLocation = {};
  dropdownSettingsInsurance = {};
  dropdownListLocation = [];
  dropdownListInsurance = [];
  selectedItemsLocation = [];
  selectedItemsInsurance=[];
  siteList;
  insuranceRepList;
  searchData;
  firstName = '';
  lastName = '';
  patientId = '';
  @Input() advanceSearchCallback;
  @Input() worklistMetaData;
  @Output() valueChange = new EventEmitter();
  picker1 = {
    startDate: moment().startOf('month').format('YYYY-MM-DD'),
    endDate: moment().endOf('month').format('YYYY-MM-DD'),
    opens: 'right',
    emitChangeOnSameDate: true,
    disableInput: true
  }
  picker2 = {
    startDate: moment().startOf('month').format('YYYY-MM-DD'),
    endDate: moment().endOf('month').format('YYYY-MM-DD'),
    opens: 'right',
    emitChangeOnSameDate: true,
    disableInput: true
  }
  eventLog = '';
  advanceSearchType = '';
  locationData = this._indexedDbService.types.LOCATION_DATA;
  insuranceData=this._indexedDbService.types.INSURANCE_DATA;
  teamData = this._indexedDbService.types.TEAM_DATA;
  statusData = this._indexedDbService.types.STATUS_DATA;
  siteData = this._indexedDbService.types.SITE_DATA;

  /* hospitialDischargeDate = {
    start: moment().subtract(12, 'month').format('YYYY-MM-DD'),
    end: moment().subtract(6, 'month').format('YYYY-MM-DD')
  }
  cprSocDate = {
    start: moment().subtract(12, 'month').format('YYYY-MM-DD'),
    end: moment().subtract(6, 'month').format('YYYY-MM-DD')
  } */

  constructor(private http: HttpClient,
    private router: Router,
    public _structureService: StructureService,
    public _formService: FormsService,
    private apollo: Apollo,
    private _formBuild: FormBuilder,
    private _intakeService: IntakeService,
    public daterangepickerOptions: DaterangepickerConfig,
    private _indexedDbService: IndexedDbService,
    public _intakeWorkListService: IntakeWorkListService,) {
    this.advanceSearchForm = this._formBuild.group({
      patientId: [''],
      firstName: [''],
      lastName: [''],
      hrPatStatus: [0],
      locationId: [0],
      siteId: [0],
      team: [0],
      hospitalDischarge: [''],
      cprSoc: [''],
      hospitalDischargeDate: [''],
      cprSocDate: [''],
      insuranceRep: {
        value: 0,
        disabled: true
      },
      //estThrough: [''],
      //actualThrough: [''],
    });
    this.daterangepickerOptions.settings = {
      autoUpdateInput: false,
      linkedCalendars: false,
      locale: { format: 'YYYY-MM-DD' },
      alwaysShowCalendars: false,
      autoApply: true,
      startDate: moment().startOf('month').format('YYYY-MM-DD'),
      endDate: moment().endOf('month').format('YYYY-MM-DD'),
      opens: "right",
      ranges: {
        'All': [moment("2014-01-01T00:00:00"), moment()],
        'Today': [moment().startOf('day'), moment()],
        'Yesterday': [moment().startOf('day').subtract(1, 'days'), moment().endOf('day').subtract(1, 'days')],
        'Last 7 Days': [moment().startOf('day').subtract(6, 'days'), moment()],
        'Last 30 Days': [moment().startOf('day').subtract(29, 'days'), moment()],
        'This Month': [moment().startOf('month'), moment().endOf('month')],
        'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
        'Year To Date': [moment().startOf('year'), moment()]
      }
    };
    this.getLocation();
    this.getTeam();
    this.getInsurance();
    this.getSite();
    this.getInsuranceRep();
    
  }
  ngOnInit() {
    // this.advanceSearchForm.patchValue({
    //   hospitalDischargeDate: moment().startOf('month').format('YYYY-MM-DD') + ' - ' + moment().endOf('month').format('YYYY-MM-DD'),
    //   cprSocDate: moment().startOf('month').format('YYYY-MM-DD') + ' - ' + moment().endOf('month').format('YYYY-MM-DD')
    // });
    this.getTeamApi();
    this.getSiteApi();
    this.getLocationApi();
    this.getPatientStatusApi();
    // this.dropdownListStatus = [
    //   { id: 1, status: 'Active' },
    //   { id: 2, status: 'On Hold' },
    //   { id: 3, status: 'InActive' },
    //   { id: 4, status: 'Cancelled' },
    //   { id: 5, status: 'Pending' }
    // ];
    // this.selectedItemsStatus = [];
    // this.dropdownSettingsStatus = {
    //   singleSelection: false,
    //   idField: 'id',
    //   textField: 'status',
    //   selectAllText: 'Select All',
    //   unSelectAllText: 'UnSelect All',
    //   allowSearchFilter: true,
    // };
    if (this.advanceSearchCallback) {
      this.advanceSearchType = this.getAdvanceSearchType(this.advanceSearchCallback);
      if (this.advanceSearchType == 'Insurance') {
        this.advanceSearchForm.controls['insuranceRep'].enable();
      }
    }
  }
  getAdvanceSearchType(advanceCallbacktype = ''): any {
    let advanceSearchType;
    switch (advanceCallbacktype) {
      case 'liaisonDashboard':
        advanceSearchType = 'Liaison';
        break;
      case 'intakeDashboard':
        advanceSearchType = 'Intake';
        break;
      case 'insuranceDashboard':
        advanceSearchType = 'Insurance';
        break;
      default:
        advanceSearchType = 'Liaison';
        break;
    }
    return advanceSearchType;
  }
  updateDate(value: any, field: any) {
    if (field == 'hospitalDischarge') {
      this.advanceSearchForm.patchValue({
        hospitalDischarge: value,
        hospitalDischargeDate: moment(value.start).format('YYYY-MM-DD') + ' - ' + moment(value.end).format('YYYY-MM-DD')
      });
      this.advanceSearchForm.get('hospitalDischarge').markAsDirty();
    }
    else if (field == 'cprSoc') {
      this.advanceSearchForm.patchValue({
        cprSoc: value,
        cprSocDate: moment(value.start).format('YYYY-MM-DD') + ' - ' + moment(value.end).format('YYYY-MM-DD')
      });
      this.advanceSearchForm.get('cprSoc').markAsDirty();
    }
  }
  getInsuranceRep() {
    this._intakeService.getInsuranceList().then((data) => {
      this.insuranceRepList = data['getInsurance'];
      this.dropdownListInsurance = this.insuranceRepList;
      console.log( this.insuranceRepList);
      this._indexedDbService.addToIDb(this.insuranceData, data['getInsurance']);
      this.selectedItemsInsurance = [];
      this.dropdownSettingsInsurance= {
        singleSelection: false,
        idField: 'id',
        textField: 'name',
        selectAllText: 'Select All',
        unSelectAllText: 'UnSelect All',
        allowSearchFilter: true,
        itemsShowLimit: 3,
      };
    });
  }
  
  async getLocation() {
    let dataFromIdb: any = [];
    await this._indexedDbService.getByKeyFromIDb(this.locationData).then(
      (response) => {
        dataFromIdb = response;
      });
    if (dataFromIdb && !dataFromIdb.length) {
      this.getLocationApi();
    } else {
      this.insuranceRepList = dataFromIdb;
    }
  }
  async getInsurance() {
    let dataFromIdb: any = [];
    await this._indexedDbService.getByKeyFromIDb(this.insuranceData).then(
      (response) => {
        dataFromIdb = response;
      });
    if (dataFromIdb && !dataFromIdb.length) {
      this.getInsuranceRep();
    } else {
      this.locationList = dataFromIdb;
    }
  }
  getLocationApi() {
    this._intakeService.getLocationList().then((data) => {
      this.locationList = data['getLocations'];
      this.dropdownListLocation = this.locationList;
      this._indexedDbService.addToIDb(this.locationData, data['getLocations']);
      this.selectedItemsLocation = [];
      this.dropdownSettingsLocation = {
        singleSelection: false,
        idField: 'id',
        textField: 'locationName',
        selectAllText: 'Select All',
        unSelectAllText: 'UnSelect All',
        enableSearchFilter: true,
        allowSearchFilter: true,
        itemsShowLimit: 3,
      };
    });
  }
  async getStatus() {
    let dataFromIdb: any = [];
    await this._indexedDbService.getByKeyFromIDb(this.statusData).then(
      (response) => {
        dataFromIdb = response;
      });
    if (dataFromIdb && !dataFromIdb.length) {
      this.getPatientStatusApi();
    } else {
      this.statusList= dataFromIdb;
    }
  }
  getPatientStatusApi() {
    this._intakeService.getPatientStatusData().then((data) => {
      this.statusList = data['getPatientStatus'];
      console.log(this.statusList);
      this.dropdownListStatus = this.statusList;
      console.log(this.dropdownListStatus);
      this._indexedDbService.addToIDb(this.locationData, data['getPatientStatus']);
      this.selectedItemsStatus = [];
      this.dropdownSettingsStatus = {
        singleSelection: false,
        idField: 'id',
        textField: 'name',
        selectAllText: 'Select All',
        unSelectAllText: 'UnSelect All',
        enableSearchFilter: true,
        allowSearchFilter: true,
        itemsShowLimit: 3,
      };
    });
  }
  async getTeam() {
    let dataFromIdb: any = [];
    await this._indexedDbService.getByKeyFromIDb(this.teamData).then(
      (response) => {
        dataFromIdb = response;
      });
    if (dataFromIdb && !dataFromIdb.length) {
      this.getTeamApi();
    } else {
      this.teamList = dataFromIdb;
    }
  }

  getTeamApi() {
    this._intakeService.getTeamList().then((data) => {
      this.teamList = data['getTeams'];
      this.dropdownList = this.teamList;
      this._indexedDbService.addToIDb(this.teamData, data['getTeams']);
      this.selectedItems = [];
      this.dropdownSettings = {
        singleSelection: false,
        idField: 'id',
        textField: 'name',
        selectAllText: 'Select All',
        unSelectAllText: 'UnSelect All',
        enableSearchFilter: true,
        allowSearchFilter: true,
        itemsShowLimit: 3
      };
    });
  }

  async getSite() {
    let dataFromIdb: any = [];
    await this._indexedDbService.getByKeyFromIDb(this.siteData).then(
      (response) => {
        dataFromIdb = response;
      });
    if (dataFromIdb && !dataFromIdb.length) {
      this.getSiteApi();
    } else {
      this.siteList = dataFromIdb;
    }
  }
  getSiteApi() {
    this._intakeService.getSiteList().then((data) => {
      this.siteList = data['getSites'];
      this.dropdownListSite = this.siteList;
      this._indexedDbService.addToIDb(this.siteData, data['getSites']);
      this.selectedItemsSite = [];
      this.dropdownSettingsSite = {
        singleSelection: false,
        idField: 'id',
        textField: 'name',
        selectAllText: 'Select All',
        unSelectAllText: 'UnSelect All',
        enableSearchFilter: true,
        allowSearchFilter: true,
        itemsShowLimit: 3
      };
    });
  }
  searchBasedField() {
    let selectedSearchFields = {};
    this.advanceSearchForm.patchValue({
      firstName : this.firstName != '' ? this.firstName.trim() :'',
      lastName : this.lastName != '' ? this.lastName.trim() : '',
      patientId : this.patientId != '' ? this.patientId.trim() : ''
    })
    if (this.advanceSearchCallback == 'insuranceDashboard') {
        this.advanceSearchForm.controls['insuranceRep'].enable();
    }
   // localStorage.setItem('searchForm', JSON.stringify(this.advanceSearchForm.value));
    this.advanceSearchForm['_forEachChild']((control, name) => {
      if ((control.value && control.value !='') && ( name != 'hospitalDischargeDate' && name != 'cprSocDate')) {
        var data = control.value;
        let filterData;
        let filterValue = [];
        if (name == 'locationId' || name == 'siteId' || name == 'team' 
        || name == 'hrPatStatus' || name == 'insuranceRep') {
          if (control.value.length > 1) {
            if (name == 'locationId') {
              filterValue = data.map((res) => res.id);
            } else if (name == 'hrPatStatus') {
              filterValue = data.map((res) => res.name);
            } else if (name == 'siteId') {
              filterValue = data.map((res) => res.id);
            } else if(name == 'team') {
              filterValue = data.map((res) => res.name);
            } else if (name == 'insuranceRep') {
              filterValue = data.map((res) => res.name);
            }
            selectedSearchFields[name] = {
              type: 'in',
              filterIn: filterValue,
              filterCondition: 'AND'
            };
          } else if (control.value.length == 1) {
            if (name == 'locationId') {
              data.map((res) => {
                filterData = res.id
              });
            } else if (name == 'hrPatStatus') {
              data.map((res) => {
                filterData = res.name
              });
            } else if (name == 'siteId') {
              data.map((res) => {
                filterData = res.id
              });
            } else if (name == 'team') {
              data.map((res) => {
                filterData = res.name
              });
            } else if (name == 'insuranceRep') {
                data.map((res) => {
                  filterData = res.name
                });
            }
            selectedSearchFields[name] = {
              type: 'equals',
              filter: filterData,
              filterCondition: 'AND'
            }
          }
        } else if (name == 'firstName' || name == 'lastName') {
          selectedSearchFields[name] = {
            type: 'startsWith',
            filter: control.value.trim(),
            filterCondition: 'AND'
          };
        } else if (typeof control.value === "object" && ( name == 'hospitalDischarge' || name == 'cprSoc')) {
          if (control.value.start) {
            const start = moment(control.value.start).format('YYYY-MM-DD');
            const end = moment(control.value.end).format('YYYY-MM-DD');
            if(start == end) {
              selectedSearchFields[name] = {
                type: 'contains',
                filter: start,
                filterCondition: 'AND'
              };
            } else {
              selectedSearchFields[name] = {
                type: 'inRange',
                filter: start,
                filterTo: end,
                filterCondition: 'AND'
              };
            }
          }
        } else {
          selectedSearchFields[name] = {
            type: 'contains',
            filter: control.value,
            filterCondition: 'AND'
          };
        }
      }
    });
    
    //if (Object.keys(selectedSearchFields).length) {
      // this.valueChange.emit(selectedSearchFields);
      // localStorage.setItem('searchFormCriteria', selectedSearchFields);
      this.valueChange.emit({ 'selectedFields': selectedSearchFields, 'type': 'save' });
      // if(this.worklistMetaData.enableSavingState == true && this.worklistMetaData.searchState == true) {
      // //  localStorage.setItem('advanceSearchFields', JSON.stringify(selectedSearchFields));
      //  // this._intakeWorkListService.advancedSearchConfig=selectedSearchFields;
      // }
      
       this._intakeWorkListService.advancedSearchConfig={'selectedSearchFields':selectedSearchFields,'searchForm':this.advanceSearchForm.value};
      
    //}
  }
  searchResetData() {
    let selectedSearchFields = {};
    let selectedResetFields = {};
    this.advanceSearchForm['_forEachChild']((control, name) => {
      if (control.dirty && control.value) {
        selectedResetFields[name] = control.value;
      }
    });
    this.advanceSearchForm.reset({
      patientId: [''],
      firstName: [''],
      lastName: [''],
      hrPatStatus: [],
      locationId: [],
      siteId: [],
      team: [],
      hospitalDischarge: [null],
      cprSoc: [null],
      insuranceRep: {
        value: 0,
        disabled: true
      },
      // hospitalDischargeDate: moment().startOf('month').format('YYYY-MM-DD') + ' - ' + moment().endOf('month').format('YYYY-MM-DD'),
      // cprSocDate: moment().startOf('month').format('YYYY-MM-DD') + ' - ' + moment().endOf('month').format('YYYY-MM-DD')
      hospitalDischargeDate: [''],
      cprSocDate: ['']

    });
    localStorage.removeItem('searchForm');
    localStorage.setItem('worklistFilterData', '');
    //if (Object.keys(selectedResetFields).length > 0) {
      this.valueChange.emit({ 'selectedFields': selectedSearchFields, 'type': 'reset' });
    //}
  }
  closeSearchForm() {
    let selectedSearchFields = {};
    //localStorage.removeItem('searchForm');
    this.valueChange.emit({ 'selectedFields': selectedSearchFields, 'type': 'close' });
  }
}