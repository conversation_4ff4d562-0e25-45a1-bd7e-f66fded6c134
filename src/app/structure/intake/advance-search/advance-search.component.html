<div class="row" class="mb-4 search-section">
  <div class="col-md-12 advanceSearchForm">
    <form [formGroup]="advanceSearchForm">
      <div class="row">
        <div class="form-group col-md-3">
          <label for="inputEmail4">Last Name Starts With</label>
          <input type="text" class="form-control adv-form-control" id="lastName" [(ngModel)]="lastName" formControlName="lastName" maxlength="40">
        </div>
        <div class="form-group col-md-3">
          <label for="inputPassword4">First Name Starts With</label>
          <input type="text" class="form-control adv-form-control" id="firstName" [(ngModel)]="firstName" formControlName="firstName" maxlength="40">
        </div>
        <div class="form-group col-md-3">
          <label for="exampleFormControlSelect1">Patient Status</label>
          <ng-multiselect-dropdown 
          [placeholder]="'Select Options'"
          [data]="dropdownListStatus"
          [(ngModel)]="selectedItemsStatus"
          [settings]="dropdownSettingsStatus"
          formControlName="hrPatStatus"
        >
        </ng-multiselect-dropdown>
          <!-- <select class="form-control" id="exampleFormControlSelect1" 
            formControlName="hrPatStatus">
            <option value="0">Select</option>
            <option value="New">New</option>
            <option value="Progress">Progress</option>
            <option value="Completed">Completed</option>
  
          </select> -->
        </div>
        <div class="form-group col-md-3">
          <label for="inputPassword4">MRN</label>
          <input type="text" class="form-control adv-form-control" id="patientId" [(ngModel)]="patientId" formControlName="patientId" maxlength="10">
        </div>
      </div>
      <div class="row">
        <div class="form-group col-md-3">
          <label for="inputEmail4">Team</label>
          <ng-multiselect-dropdown 
          [placeholder]="'Select Options'"
          [data]="dropdownList"
          [(ngModel)]="selectedItems"
          [settings]="dropdownSettings"
          formControlName="team"
        >
        </ng-multiselect-dropdown>
          <!-- <select class="form-control" formControlName="teamId">
            <option value="0">Select</option>
            <option [value]="team.id" *ngFor="let team of teamList"> {{team.name}} </option>
          </select>  -->
        </div>
        <div class="form-group col-md-3">
          <label for="inputPassword4">Site</label>
          <ng-multiselect-dropdown 
          [placeholder]="'Select Options'"
          [data]="dropdownListSite "
          [(ngModel)]="selectedItemsSite"
          [settings]="dropdownSettingsSite"
          formControlName="siteId"
        >
        </ng-multiselect-dropdown>
          <!-- <select class="form-control" formControlName="siteId">
            <option value="0">Select</option>
            <option [value]="site.id" *ngFor="let site of siteList"> {{site.name}} </option>
          </select> -->
        </div>
        <div class="form-group col-md-3">
          <label for="inputPassword4">Patient Location</label>
           <ng-multiselect-dropdown 
          [placeholder]="'Select Options'"
          [data]="dropdownListLocation "
          [(ngModel)]="selectedItemsLocation"
          [settings]="dropdownSettingsLocation"
          formControlName="locationId"
        >
        </ng-multiselect-dropdown>
          <!-- <select class="form-control" formControlName="locationId">
            <option value="0">Select</option>
            <option [value]="location.id" *ngFor="let location of locationList">{{location.locationName}} </option>
          </select> -->
        </div>

        <div class="form-group col-md-3" 
        *ngIf="advanceSearchType=='Insurance'">
          <label for="inputPassword4">Ins Ver Rep</label>
          <ng-multiselect-dropdown 
          [placeholder]="'Select Options'"
          [data]="dropdownListInsurance"
          [(ngModel)]="selectedItemsInsurance"
          [settings]="dropdownSettingsInsurance"
          formControlName="insuranceRep"
        >
        </ng-multiselect-dropdown>
          <!-- <select class="form-control" formControlName="insuranceRep">
            <option value="0">Select</option>
            <option [value]="insurance.id" *ngFor="let insurance of insuranceRepList; let i= index">
              {{insurance.name}} </option>
          </select> -->
        </div>
      </div>
      <div class="row">
        <!-- <div class="form-group col-md-3">
          <label for="inputEmail4">Est SOC</label>
          <input type="text" class="form-control" id="estSoc" formControlName="estSoc">
        </div>
        <div class="form-group col-md-3">
          <label for="inputPassword4">Through</label>
          <input type="text" class="form-control" id="estThrough" formControlName="estThrough">
      
        </div>
        -->

        <div class="form-group col-md-3">
          <label for="inputEmail4">HOSP DC</label>
          <div>
            <input readonly daterangepicker [options]="daterangepickerOptions"
              (selected)="updateDate($event,'hospitalDischarge')" class="intake-adv-date-range-field form-control adv-form-control"
              formControlName="hospitalDischargeDate">
          </div>
        </div>
        <div class="form-group col-md-3">
          <label for="inputEmail4">Actual Soc</label>
          <div>
            <input readonly daterangepicker [options]="daterangepickerOptions"
              (selected)="updateDate($event,'cprSoc')" class="intake-adv-date-range-field form-control adv-form-control" formControlName="cprSocDate">
          </div>
        </div>
        
        <div class="form-group col-md-6  action-buttons" style="padding-top: 2.17rem;">
            <button class="search-btn pull-right btn btn-sm btn-primary" (click)="searchBasedField()">Search</button>
            <button class="form-reset-btn pull-right btn btn-sm btn-secondary" (click)="searchResetData()">Reset</button>
            <button class="close-btn pull-right btn btn-sm btn-warning" (click)="closeSearchForm()">Close</button>
        </div>
        <!--<div class="form-group col-md-3">
          <label for="inputEmail4">Est Soc</label>

          <div class="input-group" daterangepicker
            (selected)="selectedDate($event, hospitialDischargeDate,'hospitalDischarge')">
            <span class="form-control uneditable-input" name="daterange">
              {{ hospitialDischargeDate.start  }} - {{ hospitialDischargeDate.end }}
            </span>
          </div>
        </div>

        <div class="form-group col-md-3">
          <label for="inputEmail4">Actual Soc</label>

          <div class="input-group" daterangepicker (selected)="selectedDate($event, cprSocDate,'cprSoc')">
            <span class="form-control uneditable-input" name="daterange">
              {{ cprSocDate.start  }} - {{ cprSocDate.end }}
            </span>
          </div>
        </div>

          <div class="form-group col-md-3">
          <label for="inputEmail4">Est Soc</label>
          <input [options]="daterangepickerOptions" (selected)="selectedDate($event)" daterangepicker
            class="form-control">

        </div>-->


      </div>
      
    </form>
  </div>
</div>
