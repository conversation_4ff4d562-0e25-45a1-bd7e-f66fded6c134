import { Http<PERSON><PERSON>, HttpHandler } from '@angular/common/http';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Apollo, ApolloModule } from 'apollo-angular';
import { PermissionService } from 'app/services/permission/permission.service';
import { FormsService } from 'app/structure/forms/forms.service';
import { AuthService } from 'app/structure/shared/auth.service';
import { SharedService } from 'app/structure/shared/sharedServices';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { DaterangepickerConfig } from 'ng2-daterangepicker';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { provideClients } from 'test-utils';
import { StoreService } from 'app/structure/shared/storeService';
import { IndexedDbService } from '../indexed-db.service';
import { IntakeWorkListService } from '../intake-worklist/intake-worklist.service';
import { IntakeService } from '../intake.service';
import { AdvanceSearchComponent } from './advance-search.component';

describe('AdvanceSearchComponent', () => {
  let component: AdvanceSearchComponent;
  let fixture: ComponentFixture<AdvanceSearchComponent>;
  let formservice: FormsService;
  beforeEach(async(() => {
    TestBed.configureTestingModule({
      providers: [
        HttpClient,
        HttpHandler,
        StructureService,
        Apollo,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        TranslateService,
        { provide: FormsService, useValue: formservice },
        IntakeService,
        DaterangepickerConfig,
        IndexedDbService,
        IntakeWorkListService,
        FormBuilder,
        PermissionService,
        StoreService
      ],
      declarations: [AdvanceSearchComponent],
      imports: [
        RouterTestingModule,
        HttpModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF,
        }),
        TranslateModule.forRoot(),
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AdvanceSearchComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
});