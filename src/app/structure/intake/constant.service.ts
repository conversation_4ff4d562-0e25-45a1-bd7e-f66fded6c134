import { Injectable } from '@angular/core';
@Injectable()
export class ConstantService {

    // entryTypes = [
    //     LIAISON => "LIAISON",
    //     INTAKE => "INTAKE",
    //     INITIAL_INS => "INITIAL_INS",
    //     INS_VERIFICATION => "INS_VERIFICATION"
    // ]
 entryTypes =  {
    LIAISON: 'LIAISON',
    INTAKE: 'INTAKE',
    INITIAL_INS: 'INITIAL_INS',
    INS_VERIFICATION: 'INS_VERIFICATION',
    PHARMACY_FOLLOWUP: 'PHARMACY'
};
entryTypeIds =  {
    LIAISON: 1,
    INTAKE: 2,
    INITIAL_INS: 3,
    INS_VERIFICATION: 4,
    PHARMACY_FOLLOWUP: 5
};
hours = ['00', '01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23'];
minutes = ['00', '15', '30', '45'];
noteTypes = {
    liaisonNote : 'Liaison',
    blogNote :  'Blog',
    intakeNote : 'Intake',
    insuranceNote : 'Insurance',
    pharmacyNote : 'Pharmacy'
};
noteTypeIds = {
    liaisonNote : 1,
    blogNote : 2,
    intakeNote : 3,
    insuranceNote : 4,
    pharmacyNote : 5
};
countIdentifierNames = {
    therapy : 'Therapy',
    userprogressnote : 'userProgressNote',
    liaisonnote : 'LiaisonNote',
    intakenote : 'IntakeNote',
    insurancenote : 'InsuranceNote',
    pharmacynote : 'PharmacyNote'
};
countIdentifiers = {
    therapy : 'patient_therapies',
    userprogressnote : 'userProgressNote',
    liaisonnote : 'notes',
    intakenote : 'notes',
    insurancenote : 'notes',
    pharmacynote : 'notes'
};
noteCharacterLimit = 50;
therapyForPharmacy = ['ENT', 'ESO'];
}