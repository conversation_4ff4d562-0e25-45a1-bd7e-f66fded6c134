import { Component, OnInit, ElementRef, Renderer } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';
import { Modal } from "../shared/commonModal"
import { DomSanitizer } from '@angular/platform-browser';
let moment = require('moment/moment');
import { siteSearchPipe } from './site.pipe';
import {Location} from '@angular/common';
import { ToolTipService } from '../tool-tip.service';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;
declare var swal: any;

@Component({
  selector: 'app-ops-tools',
  templateUrl: './filling-center-manager.html'
})

export class FillingCenterManagerComponent implements OnInit {

  searchKey="";
  folderLists=[];
  fileLists=[];
  defaultFolder;
  fileCenterType = "INCOMING";
  fileCenterTypeName="inbound";
  selectedFolder;
  activeRecord;
  newName='';
  ext='';
  fromServer=true;
  isModalOpen = false;
  pdfOrDocSrc = "";
  pdfOrDocUrl = "";
  showIframe = false;
  showVideoFrame = false;
  cancelLabel = false;
  modalBody;
  titleIncomingFilingCenter="To CitusHealth";
  titleOutgoingFilingCenter="From CitusHealth";
  folderName;
  environment;
  // update filing center
  availablemysites;
  selectedmysites;
  enablemysites;
  guids;
  guid;
  enable_multisite;
   // update filing center
  userDetails;
  stoploading;
  stoploadingfiles;
  userData;
  noFilingFolders=false;
  sIndex: number = 0;
  constructor(private route: ActivatedRoute,    
    private elementRef: ElementRef,
    private renderer: Renderer,
    private router: Router,
    private _location: Location,
    private _structureService: StructureService,
    private modals: Modal,
    private sanitizer: DomSanitizer,    
    private _ToolTipService: ToolTipService,
    private siteSearchPipe: siteSearchPipe,

  ) { 
    renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
      var targetElement;
      if(event.target.id == 'chkbox') {
       // this.detailsView();
      // alert('ok');
       //console.log(elementRef.nativeElement);
      }
       else if(event.target.id == 'signature-file'){

      
         console.log("this.folderName==",this.folderName);
        this.newName = this.activeRecord.name;
        this._structureService.setCookie('signatureRequestFilingCenter',this.folderName,1);
        this._structureService.setCookie('signatureRequestFilingCenterFile',this.newName,1);
        localStorage.setItem('filenamelinked', this.newName);
        
        this.router.navigate(['signature/new-signature']);  
      }
         else if(event.target.id == 'signature-file-inside') {
             console.log("this.folderName==",this.folderName);
        this.newName = this.activeRecord.name;
        this._structureService.setCookie('signatureRequestFilingCenter',this.folderName,1);
        this._structureService.setCookie('signatureRequestFilingCenterFile',this.newName,1);
        localStorage.setItem('filenamelinked', this.newName);
          this.router.navigate(['signature/new-signature']); 
        }
      else if(event.target.id == 'rename-file'){
       var re = /(?:\.([^.]+))?$/;
        this.ext = re.exec(this.activeRecord.name)[1]; 
        var flname = this.activeRecord.name.substr(0, this.activeRecord.name.lastIndexOf('.'));
        //this.newName = this.activeRecord.name;
        this.newName = flname;
        $('#exampleModal').modal('show');   
        $("#extension").val(this.ext); 
      }
      else if(event.target.id == 'delete-file')
      {
        swal({
          title: "Are you sure?",
          text: "You are going to archive the document.",
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default",
          confirmButtonClass: "btn-warning",
          confirmButtonText: "Ok",
          closeOnConfirm: true
      }, () => {

       this.deleteFile();

      });
      }
      else if(event.target.id == 'view-file')
        {
          console.log(this.activeRecord);
          this.preview();
        }
    });

  }

  ngOnInit() {     
    this.stoploading=true;
    this.stoploadingfiles=true;
 this._structureService.getPreferences().then(( 
      data ) => {
      if (data['getSessionTenant']) {
        if(data['getSessionTenant'].titleIncomingFilingCenter){
          this.titleIncomingFilingCenter=data['getSessionTenant'].titleIncomingFilingCenter;
        }
        if(data['getSessionTenant'].titleOutgoingFilingCenter){
          this.titleOutgoingFilingCenter=data['getSessionTenant'].titleOutgoingFilingCenter;
        }
        console.log(data['getSessionTenant']);
      }
      });

    this.environment = this._structureService.environment;
    
    this.userDetails = this._structureService.userDetails;
    this.userData = JSON.parse(this.userDetails);
      $("#citusHealthConnect").tooltip({
      title: "CitusHealth Connect will help setup and manage Filing Center Folders on your PC that you can use to share patient records from your EHR (CPR+ or others) with CitusHealth and vice-versa."
    });

    // update filing center
       if(this.userData.config.enable_multisite == 1){
          this.enablemysites  = 1;
          $(".sitesdiv").show();
          $(".classdiv").removeClass('col-md-9');
          $(".classdiv").removeClass('col-md-12');
          $(".classdiv").addClass('col-md-9');
       }else{
          this.enablemysites  = 0;
          $(".sitesdiv").hide();
          $(".classdiv").removeClass('col-md-9');
          $(".classdiv").addClass('col-md-12');
         
       }
       this.availablemysites = this.userData.mySites;
       this.selectedmysites = this.userData.mySites[0];
       this.guid =  this.userData.mySites[0].site_regid;
    
     // update filing center

    $('#nestable1').nestable();
    $('.cat__menu-right__action--menu-toggle').on('click', function(){
      $('body').toggleClass('cat__menu-right--visible');
    });
    var page = 'filing-center';
    $(".fil-cen-manager").tooltip({html:true, title: this._ToolTipService.getToolTip(page, 'FILECEN000001'), template: '<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner large"></div></div>' });
    
    this.getFolderLists();
  }



  closeModal() {
    //console.log("closeeeeeeeeeeeeeeee");
    this.modals.hide();
    this.isModalOpen = false;
    this.modalBody = "";
    this.pdfOrDocSrc = "";
    this.pdfOrDocUrl = "";
    this.showIframe = false;
    this.showVideoFrame = false;
    this.cancelLabel = false;
}
showImage(targetEvent) {
    //console.log("get click---image-", targetEvent);
    var elName = $(targetEvent.target).attr('src');
    if (!elName) {
        elName = $(targetEvent.target).attr('data-viewsrc');
    }
    if (elName.indexOf('thumb_180x116/') > -1)
        elName = elName.replace("thumb_180x116/", "");
    //console.log("this.modals.showModal----> " + this.modals.showModal);
    this.isModalOpen = true;
    this.cancelLabel = true;
    this.modalBody = '<img src="' + elName + '" class="imagepreview modal-img-viewer">';
    this.modals.show();
    //console.log("open modals------------" + this.modals.showModal);
}
showPdfOrDocs(url) {    
    this.pdfOrDocSrc = "https://docs.google.com/gview?url=" + url + "&efh=false&chrome=false&embedded=true#toolbar=0&navpanes=0&scrollbar=0";
    this.isModalOpen = true;
    this.cancelLabel = true;
    this.showIframe = true;
    this.modalBody = this.sanitizer.bypassSecurityTrustResourceUrl(this.pdfOrDocSrc);
    this.modals.show();
    //console.log("fffffff----> " + this.pdfOrDocSrc);
}
showPdf(targetEvent) {
    //console.log("get click----pdf");
    this.pdfOrDocSrc = "";
    this.pdfOrDocUrl = $(targetEvent.target).attr('data-src');
    //console.log("pppppppppppp---> " + this.pdfOrDocUrl);
    if (!this.pdfOrDocUrl) {
        this.pdfOrDocUrl = $(targetEvent.target).attr('data-viewsrc');
    }
    this.pdfOrDocSrc = "https://docs.google.com/gview?url=" + this.pdfOrDocUrl + "&efh=false&chrome=false&embedded=true#toolbar=0&navpanes=0&scrollbar=0";
    this.isModalOpen = true;
    this.cancelLabel = true;
    this.showIframe = true;
    this.modalBody = this.sanitizer.bypassSecurityTrustResourceUrl(this.pdfOrDocSrc);
    this.modals.show();
}
showVideo(targetEvent) {
    let videoUrl = $(targetEvent.target).attr('data-src');
    //console.log("pppppppppppp---> " + this.pdfOrDocUrl);
    if (!videoUrl) {
        videoUrl = $(targetEvent.target).attr('data-viewsrc');
    }
    this.isModalOpen = true;
    this.cancelLabel = true;
    this.showVideoFrame = true;
    this.modalBody = this.sanitizer.bypassSecurityTrustResourceUrl(videoUrl);
    this.modals.show();
}

  deleteFile()
  {
   this.enable_multisite = this.userData.config.enable_multisite;

    this._structureService.moveFilingCenterToArchive("0",this.fileCenterType, this.selectedFolder.folderName,this.activeRecord.name,this.enable_multisite,this.guid).subscribe(
      (data) => {
        console.log(data);
        var activityData = {
          activityName: "Document Archived",
          activityType: "Filing Center",
          activityDescription: this.activeRecord.name + " from the folder '"+this.selectedFolder.folderName+"("+this.fileCenterType+")' "+" archived"
        };

        this._structureService.trackActivity(activityData);
 
          if(data.data.moveFilingCenterToArchive!=null && data.data.moveFilingCenterToArchive.folder)
            {
              this.fileLists.filter((elem)=>{
                if(elem==this.activeRecord)
                  {
                    this.fileLists.splice(this.fileLists.indexOf(this.activeRecord), 1);
                   
                    this.populateDatatable();
                    $('#exampleModal').modal('hide'); 
                    console.log("success!*** The file archived")
                    var notify = $.notify('Success! The file archived.');
                    setTimeout(function () {
                      notify.update({ 'type': 'success', 'message': '<strong>Success! The file archived.</strong>' });
                    }, 1000);
                  }
             });


      }
    });
  }

  getFolderLists()
  {
    this.stoploading=false;
    this.stoploadingfiles=false;
    console.log('+++++ enablemulti +++++'+this.userData.config.enable_multisite)
     if(this.userData.config.enable_multisite == 1){
      
    this._structureService.getSiteTenantFilingCenterFolders(this.fileCenterType,this.guid).then(      
        (data) => {
          
          console.log(data);
         if (data['getSiteTenantFilingCenterFolders'] && data['getSiteTenantFilingCenterFolders'].length) {
           this.noFilingFolders=false
            this.folderLists = data['getSiteTenantFilingCenterFolders'];
            this.selectedFolder = data['getSiteTenantFilingCenterFolders'][0];
            this.defaultFolder=  data['getSiteTenantFilingCenterFolders'][0].folderName; 
      this._structureService.getSiteTenantFilingCenterContent(this.fileCenterType,this.defaultFolder,this.guid).then(
          (data) => { 
            this.folderName=this.defaultFolder;
            this.fileLists=[];
           
          if (data['getSiteTenantFilingCenterContent'] && data['getSiteTenantFilingCenterContent'].length) {
            this.fileLists = JSON.parse (JSON.stringify(data['getSiteTenantFilingCenterContent']));
            this.populateDatatable();       
            this.stoploading=true;     
            this.stoploadingfiles=true;
          }
          else {
            console.log('No Files');
            this.populateDatatable(); 
            this.stoploading=true;
            this.stoploadingfiles=true;
          }
  
      });
  
         }
         else{
           console.log("No folders");
           //this.folderLists=[];
           //this.fileLists=[];
           this.noFilingFolders=true
         }
    });
   }else{
    this._structureService.getTenantFilingCenterFolders(this.fileCenterType).then(      
        (data) => {
          
          console.log(data);
         if (data['getTenantFilingCenterFolders'] && data['getTenantFilingCenterFolders'].length) {
           this.noFilingFolders=false
            this.folderLists = data['getTenantFilingCenterFolders'];
            this.selectedFolder = data['getTenantFilingCenterFolders'][0];
            this.defaultFolder=  data['getTenantFilingCenterFolders'][0].folderName; 
      this._structureService.getTenantFilingCenterContent(this.fileCenterType,this.defaultFolder).then(
          (data) => { 
            this.folderName=this.defaultFolder;
            this.fileLists=[];
           
          if (data['getTenantFilingCenterContent'] && data['getTenantFilingCenterContent'].length) {
            this.fileLists = JSON.parse (JSON.stringify(data['getTenantFilingCenterContent']));
            this.populateDatatable();       
            this.stoploading=true;     
            this.stoploadingfiles=true;
          }
          else {
            console.log('No Files');
            this.populateDatatable(); 
            this.stoploading=true;
            this.stoploadingfiles=true;
          }
  
      });
  
         }
         else{
           console.log("No folders");
           //this.folderLists=[];
           //this.fileLists=[];
           this.noFilingFolders=true
         }
    });
    }
  }

  fetchFiles(folderName)
  {    
    this.stoploadingfiles=false;
    this.stoploading=false;
     if(this.userData.config.enable_multisite == 1){
      //adde by vineesh 
    this.stoploadingfiles=false;
    this.stoploading=false;
    this._structureService.getSiteTenantFilingCenterContent(this.fileCenterType,folderName,this.guid).then(
      (data) => { 
        this.folderName=folderName;
        this.fileLists=[];
        
        if (data['getSiteTenantFilingCenterContent'] && data['getSiteTenantFilingCenterContent'].length) {
          this.fileLists = JSON.parse (JSON.stringify(data['getSiteTenantFilingCenterContent']));
          this.populateDatatable();  
          this.stoploadingfiles=true;
          this.stoploading=true;
        }
        else {
          console.log('No Files');
          this.populateDatatable(); 
          this.stoploadingfiles=true;
          this.stoploading=true;        }

      }
    );
    }else{
    this._structureService.getTenantFilingCenterContent(this.fileCenterType,folderName, false).then(
      (data) => { 
        this.folderName=folderName;
        this.fileLists=[];
        
        if (data['getTenantFilingCenterContent'] && data['getTenantFilingCenterContent'].length) {
          this.fileLists = JSON.parse (JSON.stringify(data['getTenantFilingCenterContent']));
          this.populateDatatable();  
          this.stoploadingfiles=true;
          this.stoploading=true;
        }
        else {
          console.log('No Files');
          this.populateDatatable(); 
          this.stoploadingfiles=true;
          this.stoploading=true;        }

      }
    );

    }

  }

  renameFile()
  {
    //this.selectedFolder.folderName;
    //this.activeRecord.name;
    //this.newName;
   /* mutation {
      renameFilingCenterContent(sessionId: "AQIC5wM2LY4SfcyMYbAFdscCu-rK9aqq8D1Yt3HYBWJJ3C0.*AAJTSQACMDEAAlNLABM0MTMyNjA1NDcwMTU4NjU2NTM2AAJTMQAA*",params:{folder:{folderName:"Send to CPR+",type:"OUTGOING"},oldName:"Assignment of Benefit Consent_BioScrip 0000.pdf",newName:"Assignment of Benefit Consent_BioScrip 1111.pdf"}) {
        folder
        name
      }
    }*/

    if(this.newName.trim() == ""){
      $("#nameerror").css('display','block');
      return false;
    }else{
       $("#nameerror").css('display','none');
    }
    this.activeRecord.name = this.activeRecord.name.substr(0, this.activeRecord.name.lastIndexOf('.'));
    if(this.userData.config.enable_multisite == 1){
        this._structureService.renameSiteFile(this.selectedFolder.folderName,this.activeRecord.name,this.newName,this.fileCenterType,this.guid,this.ext).subscribe(
      (data) => {
        if(data.data.renameSiteFilingCenterContent!=null && data.data.renameSiteFilingCenterContent.folder)
          {
            console.log(data.data.renameSiteFilingCenterContent.name);

        
              var activityData = {
                activityName: "Document Renamed",
                activityType: "Filing Center",
                activityDescription: this.activeRecord.name + " from the folder '"+this.selectedFolder.folderName+"("+this.fileCenterType+")' "+" renamed as "+data.data.renameSiteFilingCenterContent.name
              };

              this._structureService.trackActivity(activityData);
       
            this.fileLists.filter((elem)=>{
              if(elem==this.activeRecord)
                {
                  this.activeRecord.name=data.data.renameSiteFilingCenterContent.name;
                   this.activeRecord.addedOn =  data.data.renameSiteFilingCenterContent.addedOn;
                  this.newName='';
                  this.populateDatatable();
                  $('#exampleModal').modal('hide'); 
                  var notify = $.notify('Success! The file renamed.');
                  setTimeout(function () {
                    notify.update({ 'type': 'success', 'message': '<strong>Success! The file renamed.</strong>' });
                  }, 1000);
                }
           });

          }
          else
          {
            $('#exampleModal').modal('hide'); 
            var notify = $.notify('Sorry! Error Occured.');
            setTimeout(function () {
              notify.update({ 'type': 'danger', 'message': '<strong>Sorry! Error Occured.</strong>' });
            }, 1000);
          }
      });
    }else{
    this._structureService.renameFile(this.selectedFolder.folderName,this.activeRecord.name,this.newName,this.fileCenterType,this.ext).subscribe(
      (data) => {
        if(data.data.renameFilingCenterContent!=null && data.data.renameFilingCenterContent.folder)
          {
            console.log(data.data.renameFilingCenterContent.name);

        
              var activityData = {
                activityName: "Document Renamed",
                activityType: "Filing Center",
                activityDescription: this.activeRecord.name + " from the folder '"+this.selectedFolder.folderName+"("+this.fileCenterType+")' "+" renamed as "+data.data.renameFilingCenterContent.name
              };

              this._structureService.trackActivity(activityData);
       
            this.fileLists.filter((elem)=>{
              if(elem==this.activeRecord)
                {
                  this.activeRecord.name=data.data.renameFilingCenterContent.name;
                  this.newName='';
                  this.populateDatatable();
                  $('#exampleModal').modal('hide'); 
                  var notify = $.notify('Success! The file renamed.');
                  setTimeout(function () {
                    notify.update({ 'type': 'success', 'message': '<strong>Success! The file renamed.</strong>' });
                  }, 1000);
                }
           });

          }
          else
          {
            $('#exampleModal').modal('hide'); 
            var notify = $.notify('Sorry! Error Occured.');
            setTimeout(function () {
              notify.update({ 'type': 'danger', 'message': '<strong>Sorry! Error Occured.</strong>' });
            }, 1000);
          }
      });
      }
  }

  getFiles(folder) {   
    this.selectedFolder=folder;
    if(this.stoploadingfiles==true){
      this.fetchFiles(folder.folderName);
    }
    
 }
 preview()
 {
   //alert('ok');
  //  this._structureService.preview().subscribe(
  //    (data)=>{
  //      console.log(JSON.parse(data));
  //    }
  //  );
 }
 changeServer(type)
 {
   console.log(type);
  if(type=="INCOMING"){
    this.fromServer = true;
    this.fileCenterTypeName="inbound";
  }
  else{
    this.fileCenterTypeName="outbound";
    this.fromServer = false;
   }
   console.log(this.fromServer);
   this.fileCenterType = type;
   if(this.stoploading==true){
    this.getFolderLists(); 
   }
  
 }

  populateDatatable()
  { 
    setTimeout(() => {
      if ( $.fn.DataTable.isDataTable('#file-manager') ) {
        $('#file-manager').DataTable().destroy();
        $('#file-manager tbody').empty();
      }

    var isTrue = false;    
    if(this.fileLists.length > 99){
      isTrue = true;
    }     
    $(document).ready(()=>{
      $('#file-manager').DataTable( {
          autoWidth: false,
          "order": [[ 3, "desc" ]],
          paging: isTrue,
          bInfo : isTrue,
          lengthMenu: [[100, 250, 500, 1000, -1], [100, 250, 500, 1000, 'All']],
          data: this.fileLists,
          fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
            $(nRow).on('click', () => {
              console.log(aData);
              this.activeRecord = aData;
            });
          },
          columns: [
              { title: "#" },
              { title: "Name", data: "name" },             
              { title: "Size", data: "size" },
              { title: "Last Modified", data: "addedOn" },
              { title: "Last Modified", data: "addedOn" },
              { title: "Actions" }
          ],
           columnDefs: [
          {
            data: null,
            orderable: false,
            "visible": false,
            width: "5%",
            render: () => {
              return `<input type="checkbox" id="chkbox" class="" name="mark"/> `
            },
            targets: 0
          },{
            data: null,
            orderable: true,
            width: "55%",
            targets: 1,
            render:(data, type, row)=>{
              let icon = '';
              if(row.type=="application/pdf")
                {
                  icon = '<i class="fa fa-file-pdf-o fa-2 m_right" aria-hidden="true"></i>';
                }
                else if(row.type=="image/jpeg" || row.type=="image/png" || row.type=="image/jpg")
                {
                  icon = '<i class="fa fa-file-image-o fa-2 m_right" aria-hidden="true"></i>';
                }
                else if(row.type=="application/vnd.openxmlformats-officedocument.wordprocessingml.document")
                {
                  icon = '<i class="fa fa-file-word-o fa-2 m_right" aria-hidden="true"></i>';
                }
                else{
                  icon = '<i class="fa fa-file-'+'text'+'-o fa-2 m_right" aria-hidden="true"></i>';
                }
              if(data){
              return icon+data;
              }else{
                return '';
              }

            }
          },{
            data: null,
            orderable: true,
            width: "10%",
            targets: 2,
            render: (data, type, row) => {
              if(data){
              return Math.round(data/1024)+" KB";
            }else{
              return '';
            }
            }
          },{
            data: null,
            orderable: true,
            orderData:4,
            width: "15%",
            targets: 3,
            render:(data, type, row) => {
              if(data){
              return moment.unix(data).format("MMM DD YYYY hh:mm A");
            }else{
              return '';
            }
            }
          },{
            data: null,
            orderable: true,
            visible:false,
            targets: 4,
            render: (document, type, row) => {
            if(document)  {
              return document;
            }else{
              return '';
            }      
            }
          },{
            data: null,
            orderable: false,
            width: "15%",
            render: (document, type, row) => {
              if(this.fromServer==true)
                {
                  if(row){
                  return `<div class="btn-group mr-2 mb-2 no-margin" aria-label="" role="group">
                  <button title="Signature Request" type="button" class="pull-right btn btn-sm" id="signature-file"  data-document-signature="${row}"><i id="signature-file-inside" class="fa fa-pencil-square-o" aria-hidden="true" data-document-signature-inside="${row}"></i></button>
                           
                  <button title="Rename Document" type="button" id="rename-file" data-document-rename="${row}" class="btn btn-sm icmn-pencil"></button>
                  <button title="Move to Archive" type="button" id="delete-file" class="btn btn-sm icmn-box-add"></button>
                  
                  <div class="btn-group" role="group" style="display:none;">
                      <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                          Options
                      </button>
                      <ul class="dropdown-menu">
                          <a class="dropdown-item" href="javascript: void(0);">Open with</a>
                          <li class="dropdown-divider"> </li>
                          <a class="dropdown-item" href="javascript: void(0);">Move to</a>
                          <a class="dropdown-item" href="javascript: void(0);">Add star</a>
                          <a class="dropdown-item" href="javascript: void(0);">Rename</a>
                          <li class="dropdown-divider"> </li>
                          <a class="dropdown-item" href="javascript: void(0);">Make a copy</a>
                          <a class="dropdown-item" href="javascript: void(0);">Download</a>
                          <a class="dropdown-item" href="javascript: void(0);">Delete</a>
                      </ul>
                  </div>
              </div>`
                  }else{
                    return '';
                  }
                }
                else{
                  if(row){
                  return `<div class="btn-group mr-2 mb-2" aria-label="" role="group">
                             
                  <button title="Rename Document" type="button" id="rename-file" data-document-rename="${row}" class="btn btn-sm icmn-pencil"></button>
                  <button title="Move to Archive" type="button" id="delete-file" class="btn btn-sm icmn-box-add"></button>                  
                  <div class="btn-group" role="group" style="display:none;">
                      <button type="button" class="btn btn-sm btn-primary dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                          Options
                      </button>
                      <ul class="dropdown-menu">
                          <a class="dropdown-item" href="javascript: void(0);">Open with</a>
                          <li class="dropdown-divider"> </li>
                          <a class="dropdown-item" href="javascript: void(0);">Move to</a>
                          <a class="dropdown-item" href="javascript: void(0);">Add star</a>
                          <a class="dropdown-item" href="javascript: void(0);">Rename</a>
                          <li class="dropdown-divider"> </li>
                          <a class="dropdown-item" href="javascript: void(0);">Make a copy</a>
                          <a class="dropdown-item" href="javascript: void(0);">Download</a>
                          <a class="dropdown-item" href="javascript: void(0);">Delete</a>
                      </ul>
                  </div>
              </div>`
                }else{
                  return '';
                }
                }

              
            },
            targets: -1
          },]
      } );
  } );
},1);

  }
   // update filing center

  getSites(sites) {   
    this.selectedmysites=sites;
  }
 changeSites(guid){
   this.guid = guid;
   console.log(guid)
   //if(this.stoploading==true){
    this.getFolderLists(); 
   //}
 }

 // update filing center
}

