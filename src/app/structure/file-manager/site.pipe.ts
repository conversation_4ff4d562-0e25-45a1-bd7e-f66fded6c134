import { Pipe, PipeTransform } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser'
import { DatePipe } from '@angular/common'; 




@Pipe({
  name: 'siteSearch'
})
export class siteSearchPipe implements PipeTransform {
  transform(forms:any, searchKey:any): any {
    if (!forms) { 
      return [];
    } else {
      if(searchKey) {
       var cnt = forms.filter(item => item['name'].toLowerCase().indexOf(searchKey.toLowerCase()) != -1).length;
       if(cnt == 0){
        return [-1];
       }else{
         return forms.filter(item => item['name'].toLowerCase().indexOf(searchKey.toLowerCase()) != -1);  
       }
       
      } else {
        return forms;
      }
    }
  }
}
