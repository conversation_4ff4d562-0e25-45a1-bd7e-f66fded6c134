<style>
    .drag_disabled {
        pointer-events: none!important;
    }
    
    .drag_enabled {
        pointer-events: all!important;
    }
    
    .dd-handle1 {
        display: block;
        height: 2.85rem;
        margin: 0.35rem 0;
        padding: 0.35rem 0.71rem;
        text-decoration: none;
        border: 1px solid #e4e9f0;
        background: #fff;
        border-radius: 3px;
        cursor: pointer;
    }
    
    .dd-handle1:hover {
        color: #fff;
        background: #0190fe;
        border-color: #0190fe;
    }
    
    .Hactive {
        color: #fff;
        background: #0190fe;
        border-color: #0190fe;
    }
    
    .filing-icons i img {
        width: 30px;
        margin-top: -3px;
        margin-right: 7px;
    }
    #nameerror{
        color:red;
        display:none;
        margin-right: 140px;
        width: 100%;
    }
</style>
<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Filing Center Manager</strong>
            <i class="fil-cen-manager icmn-info" data-placement="bottom"></i>
            
            <!--<a href="javascript:void(0);" class="pull-right btn btn-sm btn-primary mr-2 mb-2">Back</a>-->
        </span>

        <div class="pull-right" *ngIf="userData.config.enable_download_citus_health_connect == 1">
            <!--<p> Download CitusHealth Connect to get started</p>-->
            <a href="https://connect.fc.citushealth.com/update/CitusHealthConnect.exe"><button id="citusHealthConnect" type="button" data-placement="left" class="btn btn-info download-btn"> 
                    <span class="btn-windows"><i class="fa fa-windows" aria-hidden="true"></i></span>
                    <!--<span class="btn-apple"><i class="fa fa-apple" aria-hidden="true"></i></span>
                    <span class="btn-linux"><i class="fa fa-linux" aria-hidden="true"></i></span>-->
                    Download CitusHealth Connect</button></a>
        </div>

    </div>

    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item">Filing Center Manager</li>
        </ol>

        <div class="filecentermain">
            <div class="row filecenter">
         <!-- added by vineesh -->
          
            <div class="col-md-3 sitesdiv">
                <div class="cat__apps__messaging__header">
                <input id="search_frm"
                  class="form-control cat__apps__messaging__header__input"
                  placeholder="Search Site"
                  [(ngModel)]="searchKey"
                  #messageGroups
                />

                <span _ngcontent-c9="" _ngcontent-c2="" class=" resettip" data-original-title="Reset" data-placement="top" data-toggle="tooltip" title="" (click) = "messageGroups.value = ''">
                             <i _ngcontent-c9="" class="reset icmn-loop2" title="reset"></i>   </span>
              
              </div>
                 <div class="dd scrollsite" id="nestable0">
                     <ol class="dd-list">
                         <li class="dd-item no-drag" data-id="1" *ngFor="let mysite of availablemysites | siteSearch: messageGroups.value;let i = index">
                             <div *ngIf="mysite === -1" class="text-center">No matching items found</div>
                            <div *ngIf="mysite !== -1" title="{{mysite.name}}"  (click)="changeSites(mysite.site_regid); getSites(mysite)" class="dd-handle1 dd-sites" [ngClass]="{'Hactive': selectedmysites.id == mysite.id}"><i class="" aria-hidden="true"></i>
                              {{mysite.name}}
                            </div>
                        </li> 
                    </ol>
                </div>
            </div>
            <!-- added by vineesh -->
        <div class="classdiv col-md-9">
            <div class="from-to-btn-sec" style="margin-bottom: 14px;">
                <div class="nav-tabs-horizontal">
                    <ul class="nav nav-tabs filing-icons" role="tablist">
                        <!--  <li class="nav-item text-center" (click)="changeServer('INCOMING')">
                            <a class="nav-link active" href="javascript: void(0);" data-toggle="tab" data-target="#home1" role="tab" aria-expanded="true"><i><img src="../assets/img/filing-1.png"/></i><span>Sites</span></a>
                        </li> -->
                        <li class="nav-item text-center" (click)="changeServer('INCOMING')">
                            <a class="nav-link active" href="javascript: void(0);" data-toggle="tab" data-target="#home1" role="tab" aria-expanded="true"><i><img src="../assets/img/filing-1.png"/></i><span>Incoming ({{titleIncomingFilingCenter}})</span></a>
                        </li>
                        <li class="nav-item text-center" (click)="changeServer('OUTGOING')">
                            <a class="nav-link" href="javascript: void(0);" data-toggle="tab" data-target="#profile1" role="tab" aria-expanded="false"><i><img src="../assets/img/filing-2.png"/></i>Outgoing ({{titleOutgoingFilingCenter}})</a>
                        </li>
                    </ul> 

                </div>
            </div>
            <div class="col-md-9" [hidden]="!noFilingFolders">
                <i class="fa fa-exclamation-triangle" aria-hidden="true"></i> There are no {{fileCenterTypeName}} folders found
            </div>
            <div class="row filecenter_folders">
             <div class="col-md-3" [hidden]="noFilingFolders">
                <div class="dd" id="nestable1">

                    <ol class="dd-list" *ngFor="let folder of folderLists;let i = index">
                          <li class="dd-item no-drag" data-id="1" (click)="getFiles(folder)">
                            <div title="{{folder.folderName}}" class="dd-handle1 dd-folders" [ngClass]="{'Hactive': selectedFolder == folder}"><i class="fa fa-folder" [ngClass]="{'fa-folder-open': selectedFolder == folder}" aria-hidden="true"></i> {{folder.folderName}}</div>
                        </li>
                    </ol>
                    
                </div>

            </div>
            <div class="col-md-9" [hidden]="noFilingFolders" id="nestable2">
                <!--  <div class="row">
                   <div class="col-sm-4"></div>
                    <div class="col-sm-8" style="text-align:right;margin-bottom:10px;">
                    <button type="button" class="btn icmn-eye mr-2"></button>
                    <button type="button" class="btn icmn-info mr-2 cat__menu-right__action--menu-toggle"></button>
                    <button type="button" class="btn icmn-bin mr-2"></button>
                    <button type="button" class="btn icmn-download mr-2"></button>
                    
                    <button type="button" class="btn icmn-share2 mr-2"></button>
                    <button type="button" class="btn icmn-attachment mr-2"></button>
                    <button type="button" class="btn icmn-cog"></button>
                    </div>    
                </div> -->
                <table class="table table-hover" id="file-manager" width="100%">

                </table>
            </div>
        </div>

        </div>

</div>


</div>
    </div>

</section>
<!-- END: tables/datatables -->



<div class="modal forward-modal associated-patient" id="exampleModal" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="exampleModalLabel">Rename</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            </button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="form-group">
                        <label class="form-control">Please enter a new name for the item:</label>
                        <input type="text" class="form-control" name="newName" [(ngModel)]="newName">
                    </div>
                </form>

            </div>
            <div class="modal-footer">
                <span id="nameerror">Please enter a new name</span>
                <input type="hidden" id="extension">
                <button class="btn btn-success" (click)="renameFile()">Update</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
            </div>

        </div>

    </div>
</div>