import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { FillingCenterManagerComponent } from './filling-center-manager.citushealth';
import { FormsModule } from '@angular/forms';
import { siteSearchPipe} from './site.pipe'
import { AuthGuard } from 'app/guard/auth.guard';
export const routes: Routes = [
  { path: 'file-manager', component: FillingCenterManagerComponent,canActivate:[AuthGuard],data:{checkUserGroupPermission:'3'} },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    RouterModule.forChild(routes)
  ],
  declarations: [
    FillingCenterManagerComponent,
    siteSearchPipe
  ],providers: [ 
   siteSearchPipe
    
  ],

})

export class  FillingCenterManagerModule { }
