import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule,ReactiveFormsModule } from '@angular/forms';
import { registrationComponent } from './registration.citushealth';
import { SharedModule } from '../shared/sharedModule';
export const routes: Routes = [
  { path: 'registration', component: registrationComponent },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    BrowserModule,
    SharedModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes)
  ],
  declarations: [
    registrationComponent,
    
  ]

})

export class RegitrationModule { }
