import { Injectable } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import { Subject } from 'rxjs/Subject';
import 'rxjs/add/operator/toPromise';
import { StructureService } from '../structure.service';
import {APIs} from '../../constants/apis';
import { ToolTipService } from '../tool-tip.service';
@Injectable()
export class RegistrationService {
  validateToken='';
  signupApi='';
  registerUserInCmisApi='';
  forgotPasswordApi='';
  resetPasswordApi='';
  updateUserdetailsIcamppApi='';
  deleteAvatarURL='';
  signupCareGiverApi='';
  data={};
  setEnrolPasswordApi='';
  clearResetPasswordForm = false;
  constructor(
    private _http: Http,
    private readonly structureService:StructureService,
    private tooltipService: ToolTipService
    ) {
      this.validateToken = this._structureService.apiBaseUrl+'citus-health/'+this._structureService.version+'/get-tenant-roles-by-token.php';
      this.registerUserInCmisApi = this._structureService.apiBaseUrl+'citus-health/'+this._structureService.version+'/registerUserInCmis.php';
      this.signupApi = this._structureService.apiBaseUrl+'citus-health/'+this._structureService.version+'/chat-window-signup-forgerock.php';
      this.forgotPasswordApi = this._structureService.apiBaseUrl+'citus-health/'+this._structureService.version+'/forgot-password-icampp.php';
      this.resetPasswordApi = this._structureService.apiBaseUrl+'citus-health/'+this._structureService.version+'/forgot-password-icampp.php';
      this.updateUserdetailsIcamppApi = this._structureService.apiBaseUrl+'citus-health/'+this._structureService.version+'/update-userdetails-icampp.php';
      this.deleteAvatarURL= this._structureService.apiBaseUrl+'citus-health/'+this._structureService.version+'/delete-avatar.php';
      this.signupCareGiverApi = this._structureService.apiBaseUrl+'citus-health/'+this._structureService.version+'/'+APIs.checkPatientForCaregiver;
      this.setEnrolPasswordApi = this._structureService.apiBaseUrl+'citus-health/'+this._structureService.version+'/set-rx-password.php';
      
  }
  /** @deprecated instead use structureService */
  // eslint-disable-next-line no-underscore-dangle
  get _structureService() {
    return this.structureService;
  }
    
  createReferralCodes(reqData, wpEnrollUrl) {
    let apiURL = wpEnrollUrl + '/wp-json/api/wp/v2/signup';
    let headers = new Headers();
    headers.append('Content-Type', 'application/X-www-form-urlencoded');
    const options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
      this._http.post(apiURL, reqData, options)
        .toPromise()
        .then(
          res => {
            const result = res.json();
            resolve(result);
          },
          err => {
            reject(err);
          }
        );
    });
    return promise;
  }

  updateFirstLogin(reqData, wp_enroll_url) {
    const userDetails = this._structureService.getUserdata();
    reqData['tenantId'] = userDetails.config.token;
   // let apiURL = wp_enroll_url + '/wp-json/api/wp/v2/app-usage';
    const apiURL = this._structureService.apiBaseUrl + "citus-health/v4/app-usage-wrapper.php";
    let headers = new Headers();
    headers.append('Content-Type', 'application/X-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    const options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
      this._http.post(apiURL, reqData, options)
        .toPromise()
        .then(
          res => {
            const result = res.json();
            resolve(result);
          },
          err => {
            reject(err);
          }
        );
    });
    return promise;
  }

  validateRegistrationToken(registrationToken){
    /*let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.validateToken;
       this.data =  'registrationToken=' + registrationToken;
       this._http.post(apiURL,this.data,options)
        .toPromise()
        .then(
        res => {
          const vlaidateToken = res.json();
          resolve(vlaidateToken);
        }
        );
    });
    return promise;*/

    var apiConfig = {url: this.validateToken, requestType: 'http',data :'registrationToken=' + registrationToken};
    return this._structureService.requestData(apiConfig);
  }


 signup(data){
    /*let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.signupApi;
       this.data =  data;
       this._http.post(apiURL,this.data,options)
        .toPromise()
        .then(
        res => {
          const vlaidateToken = res.json();
          resolve(vlaidateToken);
        }
        );
    });
    return promise;*/
    var apiConfig = {url: this.signupApi, requestType: 'http',data :data};
    return this._structureService.requestData(apiConfig);

  }

  signupCareGiver(data,noLoader=false){
    var apiConfig = {url: this.signupCareGiverApi, requestType: 'http',data :data,noLoader:noLoader};
    return this._structureService.requestData(apiConfig);
  }

  setPassword(data){
    var apiConfig = {url: this.setEnrolPasswordApi, requestType: 'http',data :data};
    return this._structureService.requestData(apiConfig);
  }
  resetUserPassword(passwordData, logoutRequired?: boolean, displayname?:string) {  
    this.clearResetPasswordForm = false;
    const currentUserDetails = this._structureService.getUserdata();
    if (passwordData) {
      this.setPassword(passwordData)
        .then((data: any) => {
          if (data.status === 0) {
            const activityData = {
              activityName: "Reset Password",
              activityType: "Password Reset",
              activityDescription: displayname
              ? `Password reset successfully for user ${displayname}(${passwordData.username}) by ${currentUserDetails.displayName}`
              : `Password reset successfully for ${currentUserDetails.displayName}`
            };
            this._structureService.trackActivity(activityData);
            this._structureService.notifyMessage({
              messge: this.tooltipService.getTranslateData('SUCCESS_MESSAGES.PASSWORD_RESET_SUCCESSFULLY'),
              delay: 1000,
              type: 'success'
            });
            setTimeout(() => {
              if (logoutRequired) this._structureService.logout();
            }, 3000);
            this.clearResetPasswordForm = true;
          } else {
            const activityData = {
              activityName: "Password Reset Failed",
              activityType: "user access",
              activityDescription: displayname
              ? `Password reset failed  for user ${displayname}(${passwordData.username}) by (${currentUserDetails.displayName})`
              : `Password reset failed for (${currentUserDetails.displayName})`
            };
            this._structureService.trackActivity(activityData);
            this.clearResetPasswordForm = false;
            this._structureService.notifyMessage({
              messge: data.errorMessage || this.tooltipService.getTranslateData('ERROR_MESSAGES.PASSWORD_RESET_FAILED'),
              delay: 0
            });
          }
        })
        .catch((ex) => {});
    }
  }
 registerUserInCmis(registerUserInCmisData){
   let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.registerUserInCmisApi;
       this.data =  registerUserInCmisData;
       this._http.post(apiURL,this.data,options)
        .toPromise()
        .then(
        res => {
          const vlaidateToken = res.json();
          resolve(vlaidateToken);
        }
        );
    });
    return promise;

  }


 forgotPassword(data){
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.forgotPasswordApi;
       this.data =  data;
       this._http.post(apiURL,this.data,options)
        .toPromise()
        .then(
        res => {
          const vlaidateToken = res.json();
          resolve(vlaidateToken);
        }
        );
    });
    return promise;

  }
  /**
   * Handles the click event for resetting a user's password.
   *
   * @param targetElement - The DOM element that was clicked. It should contain a data attribute 'user' with the user's ID.
   * @param userList - An array of user objects. Each user object should have an 'id' and 'alternateName' property.
   *
   * @returns void
   *
   * This method checks if the target element and its 'user' data attribute are valid.
   * If valid, it retrieves the user ID from the data attribute, finds the corresponding user in the userList,
   * and calls the `resetPasswordByStaff` method with the user's ID and alternate name.
   */
  handleResetPasswordClick(targetElement, userList): void {
    if (!targetElement || !targetElement.data('user')) {
      return;
    }
    const userId = +targetElement.data('user');
    const user = userList.find((x) => +x.id === userId);
    if (user && user.alternateName && user.id) {
      const email = user.emails && user.emails[0] && user.emails[0].value ? user.emails[0].value : '';
      this.resetPasswordByStaff({ id: user.id, username: user.alternateName, email });
    }
  }
  /**
   * Determines if the user can reset their password.
   *
   * @param user - The user object containing user details.
   * @returns A boolean indicating whether the user can reset their password.
   *
   * The user can reset their password if:
   * - The user has a password.
   * - The user's registration type is not 7. (SSO user)
   * - The user does not have an SSO ID. (Okta user)
   * - The user's status is 'Active'.
   * @todo
   * - Update the payload for OKTA users (CHP-19291).
   */
  canResetPassword(user): boolean {
    // TODO: CHP-19291: Remove condition for OKTA users
    return user.password && user.registrationType !== 7 && user.status === 'Active';
  }
  /**
   * Resets the password for a user by staff.
   *
   * This method prompts the staff with a confirmation dialog before proceeding to reset the user's password.
   * If the confirmation is received, it sends a request to reset the password and handles the response accordingly.
   *
   * @param user - An object containing the username and id of the user whose password is to be reset.
   *
   * @remarks
   * - If either the username or id is missing from the user object, the method returns early without performing any action.
   * - The method uses `this.structureService` to get the current user details, show a confirmation popup, notify messages, and track activities.
   * - The method uses `this.tooltipService` to get translated messages for notifications.
   *
   * @todo
   * - Update the payload for OKTA users (CHP-19291).
   */
  resetPasswordByStaff(user: { username: string; id: string; email: string }): void {
    if (!user.username || !user.id) {
      return;
    }
    // TODO: CHP-19291: Update payload for OKTA users
    const userData = { input: { queryFilter: `uid eq '${user.username}'` } };
    const currentUserDetails = this.structureService.getUserdata();
    this.structureService
      .showAlertMessagePopup({
        text: this.tooltipService.getTranslateData('MESSAGES.RESET_PASSWORD_CONFIRMATION')
      })
      .then((confirm) => {
        if (confirm) {
          if (!user.email) {
            this.structureService.notifyMessage({
              message: this.tooltipService.getTranslateData('ERROR_MESSAGES.PASSWORD_RESET_NO_EMAIL_FOUND_FOR_USER'),
              type: 'danger'
            });
            return;
          }
          this.resetPassword(userData).then((data: { status: number; message?: string }) => {
            if (data.status === 50) {
              this.structureService.notifyMessage({
                message: this.tooltipService.getTranslateData('SUCCESS_MESSAGES.PASSWORD_RESET_LINK_SENT_SUCCESSFULLY'),
                type: 'success'
              });
              const activityData = {
                activityName: 'Reset Password',
                activityType: 'user access',
                activityDescription: `${currentUserDetails.userId} requested forgot password for ${user.id}`
              };
              this.structureService.trackActivity(activityData);
            } else {
              this.structureService.notifyMessage({
                message: this.tooltipService.getTranslateData('ERROR_MESSAGES.PASSWORD_RESET_LINK_FAILED'),
                type: 'danger'
              });
              const activityData = {
                activityName: 'Failure Reset Password',
                activityType: 'user access',
                activityDescription: `Forgot password request by ${currentUserDetails.userId} failed for ${user.id}[Error:${data.message}]`
              };
              this.structureService.trackActivity(activityData);
            }
          });
        }
      });
  }
  resetPassword(data){
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.resetPasswordApi;
       this.data =  data;
       this._http.post(apiURL,this.data,options)
        .toPromise()
        .then(
        res => {
          const vlaidateToken = res.json();
          resolve(vlaidateToken);
        }
        );
    });
    return promise;

  }
            

  updateUserdetailsIcampp(updateUserData){
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
     headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.updateUserdetailsIcamppApi;
       this.data =  updateUserData;
       this._http.post(apiURL,this.data,options)
        .toPromise()
        .then(
        res => {
          const vlaidateToken = res.json();
          resolve(vlaidateToken);
        }
        );
    });
    return promise;

  }

  deleteAvatar(updateUserData){
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
     headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.deleteAvatarURL;
    this.data =  updateUserData;
       this._http.post(apiURL,this.data,options)
        .toPromise()
        .then(
        res => {
          const vlaidateToken = res.json();
          resolve(vlaidateToken);
        }
        );
    });
    return promise;

  }
  
}

