<style>
    .cat__pages__login {
        margin-top: -65px !important;
    }

    .registration-btns {
        text-align: center;
        margin-bottom: 30px !important;
    }

    .registration-close {
        position: absolute;
        right: 30px;
        top: 15px;
    }

    .registration-close i {
        font-size: 22px;
        cursor: pointer;
    }
    /*.form-group.checkbox-outer .form-check,
    .form-group.checkbox-outer text {
        margin-left: 18px;
    }
    */

    .checkbox-outer {
        padding-left: 1.07rem;
        padding-right: 1.07rem;
    }

    .behalf-patient {
        display: none;
    }

    .cat__wizard>.content {
        margin-bottom: 0 !important;
    }

    .validate-reg-id {
        padding-bottom: 0 !important;
    }

    .no-margin {
        margin-bottom: 0 !important;
    }

    .registration-button-color {
        background-color: #3991a5!important;
        border-color: #3991a5!important;
    }
    a.privacy-link{
        color: #0088ff
    }
    a.privacy-link:hover, a.privacy-link:active, a.privacy-link:focus {
        text-decoration: underline !important;
        color: #076fca;
    }
    .toggle-eye{
      font-size: 20px;
      width: 35px;
      padding: 8px 2px 2px 3px;
      border: 1px solid #ccbc;
      background-color: #eee;
      border-radius: 0px 4px 4px 0px;
      cursor: pointer;
    }
</style>
<!-- START: pages/login-beta -->
<div class="cat__pages__login" style="background-image: url(assets/modules/pages/common/img/login/login-bg.jpg)">
    <div class="cat__pages__login__header">
        <div class="row">
            <div class="col-lg-6 logo-citus">
                <img src="./assets/modules/pages/common/img/login/account-logo-on-dark-bg.png" />
            </div>
            <div class="col-lg-6 header-info">
                <p>
                    <i class="fa fa-phone-square" aria-hidden="true"></i> (*************
                    <span><i class="fa fa-envelope" aria-hidden="true"></i> <EMAIL></span>


                </p>
            </div>
        </div>
    </div>
    <div class="cat__pages__login__block cat__pages__login__block--extended">

        <form  [formGroup]="messageTagForm"   (keydown.enter)="validateToken()">

        <div class="cat__pages__login__block__inner registration-panel">
            <a class="registration-close" (click)="navigateToPage('/login')"><i class="fa fa-times-circle" data-toggle="tooltip" aria-hidden="true"></i></a>
            <div class="cat__ecommerce__cart">
                <div id="cart-checkout" class="cat__wizard ">
                    <h3>
                        <i class="fa fa-user-circle cat__wizard__steps__icon"></i>
                        <span class="cat__wizard__steps__title">Validate Account</span>
                    </h3>
                    <section>
                        <div>
                            <div class="form-group">
                                <!-- <form class="new-form-1"> -->
                                <input id="registration_token" type="text" class="form-control" placeholder="Registration ID *" required>
                                <div class="alert alert-danger empty">
                                    {{errorMessageForToken}}
                                </div>
                                <!-- </form> -->
                            </div>
                        </div>

                    </section>



                    <h3>
                        <i class="fa fa-id-card cat__wizard__steps__icon"></i>
                        <span class="cat__wizard__steps__title">Role Selection</span>
                    </h3>
                    <section class="registration-step validate-reg-id">
                        <div class="form-group">
                            <span id="regid">6565123</span>
                        </div>
                        <hr>
                        <div class="form-group no-margin">
                            <input type="text" class="form-control" id="roletype" placeholder="" required="" readonly>
                        </div>

                        <div class="form-group no-margin">

                            <select class="form-control show-staff">
                            </select>
                        </div>

                        <!--<div class="form-group">
                            <div class="form-check" >
                                <label class="form-check-label">
                            <input class="form-check-input behalf-patient-check"  value="true" type="checkbox">
                            Signing up on behalf of patient.
                        </label>
                            </div>
                        </div>
                        <div class="form-group behalf-patient ">
                            <input type="text" class="form-control" id="yourname" placeholder="" required="">
                        </div>-->
                    </section>
                    <h3>
                        <i class="fa fa-check-circle cat__wizard__steps__icon"></i>
                        <span class="cat__wizard__steps__title">Confirmation</span>
                    </h3>

                    <section>

                        <div class="invoice-block">

                            <div class="form-group">

                                <label class="form-check-label"> Choose Site  </label>
                                    <select class="form-control select2" formControlName="tenantSites" id="tenantSites">
                                        <option *ngFor="let site of mySites" value="{{site.id}}"> {{site.name}} </option>
                                    </select>

                                  <p class="ac" id="site-message"></p>
                            </div>

                            <div class="form-group">
                                <input type= "text" class="form-control" id="email" placeholder="Email *" required="">

                                  <p class="ac" id="status-message"></p>
                            </div>
                            <div class="form-group" id="display_name_checking">
                                <input type="text" class="form-control" id="display_name" placeholder="Display Name *" required="">
                            </div>
                            <div class="form-group">
                                <input type="text" class="form-control" id="firstname" placeholder=" First Name *" required="">
                            </div>
                            <div class="form-group">
                                <input type="text" class="form-control" id="lastname" placeholder=" Last Name *" required="">
                            </div>
                            <div class="form-group row">
                                <div class="col-md-3 col-lg-2">
                                    <input type="tel" placeholder="" class="form-control editable-field" style="width:0;" id="phone" >
                                </div>
                                <div class="col-md-3 col-lg-2">
                                        <!-- <label class="form-control ccode">+{{countryCode}}</label> -->
                                        <input type="text" [(ngModel)]="countryCode" class="form-control editable-field" id="countryCode"  [ngModelOptions]="{standalone: true}" readonly>
                                    </div>
                                <div class="col-md-6 col-lg-8">
                                    <input type="text" class="form-control" id="us-phone-mask-input" placeholder="Cell Number *" required="">
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="input-group">
                                  <input  type="password" class="form-control password-toggle"  id="password" placeholder="Password *" required="">
                                    <span data-placement="top" data-toggle="tooltip"  data-original-title="Show password" data-animation="false"  toggle="#password-field"  class="fa fa-fw fa-eye-slash field_icon toggle-password toggle-eye"></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="input-group">
                                  <input type="password" class="form-control" id="confirmpassword" placeholder="Confirm Password *" required="">
                                </div>
                            </div>
                            <div class="form-group row" id="dob-parent" *ngIf="!showBehalfPatientCheck" >
                                <!-- <input type="text" class="form-control" id="dob" placeholder="DOB" required=""> -->
                                <!-- <div class='input-group date' id='datetimepicker1'>
                                    <input type='text' class="form-control" id="dob" />
                                    <span class="input-group-addon"><span class="glyphicon glyphicon-calendar fa fa-calendar"></span>
                                    </span>
                                </div> -->
                                <!-- <th class="schedule-admin-date">
                                    <div class='input-group date' id='dob-date-picker'>
                                        <input type='text' class="form-control" id="dob" placeholder="DOB MM/DD/YYYY" />
                                        <span class="input-group-addon">
                                            <span class="fa fa-calendar"></span>
                                        </span>
                                    </div>
                                </th> -->
                                <div class="col-md-3"><label class="label-dob" for="">Date Of Birth *</label></div>
                                <div class="col-md-9"><input type="text" class="form-control" id="dob-date-picker" required=""></div>
                            </div>
                            <div class="form-group" id="zip-parent" *ngIf="!showBehalfPatientCheck">
                                <input type="number" class="form-control" id="zip" placeholder="Zip *" required="">
                            </div>
                            <div class="form-group">
                                <div class="form-check">
                                    <label class="form-check-label">
                                    <input class="form-check-input" (click)="acceptTerm()" value="" id="acceptTerm" type="checkbox">
                                    I agree to the
                                    <a href="http://www.citushealth.com/eula" class="privacy-link" target="_blank" >
                                         terms of service and privacy policy.
                                    </a>
                            </label>
                                </div>
                            </div>

                        </div>


                    </section>

                </div>
            </div>


            <div class="form-group checkbox-outer" *ngIf="page==2  && !staff ">
                <div class="form-check">
                    <label class="form-check-label">
                            <input class="form-check-input behalf-patient-check" id="signing-behlfs" (click)="showBehalfPatientInput()"  value="true" type="checkbox" >
                            Signing up on behalf of patient.
                        </label>
                </div>
            </div>

            <div class="form-group checkbox-outer" *ngIf="page==2 && showBehalfPatientCheck">
                <input type="text" class="form-control" id="careGiverPatientFName" placeholder="Enter Patient First Name *" required="">
            </div>
            <div class="form-group checkbox-outer" *ngIf="page==2 && showBehalfPatientCheck">
                <input type="text" class="form-control" id="careGiverPatientLName" placeholder="Enter Patient Last Name *" required="">
            </div>
            <!-- <div class="form-group checkbox-outer" *ngIf="page==2 && showBehalfPatientCheck">
                <div class="col-md-12">
                    <input type="text" class="form-control col-md-4" id="careGiverPatientDOB" required="">
                </div>
            </div> -->
            <div class="form-group row checkbox-outer" id="careGiverPatientParent" *ngIf="page==2 && showBehalfPatientCheck" >
                <div class="col-md-3"><label class="label-dob" for="">Date Of Birth *</label></div>
                <div class="col-md-9"><input type="text" class="form-control" id="careGiverPatientDOB" required=""></div>
            </div>
            <div class="registration-btns">
                <button (click)="prevclick()" [disabled]="isCareGiverSignUp" class="btn btn-default margin-inline prv-btn registration-button-color" disabled>Previous</button>
                <button *ngIf="page!=3" (click)="validateToken()" class="next-btn btn btn-default margin-inline next-btn registration-button-color" [disabled]="manageNext">Next</button>
                <button id="registrationButtonClick" *ngIf="page==3" (click)="validateToken()" class="next-btn btn btn-default margin-inline next-btn registration-button-color finish-btn">Finish</button>

            </div>
        </div>
        </form>
    </div>



</div>
<!-- END: pages/login-beta -->
