import { Component, OnInit, HostListener} from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { RegistrationService } from './registration.service';
import { Subscription } from 'rxjs/Subscription';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { FormsModule, FormBuilder,FormGroup,FormControl } from '@angular/forms';
import {SharedService} from '../../structure/shared/sharedServices';
let moment = require('moment/moment')

declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;
declare var swal: any;
declare var NProgress: any;
@Component({
    selector: 'app-registration',
    templateUrl: './registration.html'
})


export class registrationComponent implements OnInit {
    registrationTokenValidated;
    registrationTokenConfirmation;
    registrationTenantId = 0;
    showBehalfPatientCheck = false;
    manageNext = false;
    currentTenantID:any;
    currentTenantCareGiverRoleId;
    patient_associated_id;
    isCareGiverSignUp = true;
    errorMessageForToken;
    messageTagForm: FormGroup;
    firstprev;
    mySites=[];
    mySitesArray:any;
  // mySites=[{id: "1", name: "site 121"},{id: "2", name: "Citus Demo Site 1"}];
    isSelectedClinician;
    registrationToken;
    siteName;
    selectedSite="";
    siteId;
    email;
    lastnext;
    displayName;
    firstname;
    lastname;
    cellnumber;
    password;
    page;
    confirmpassword;
    dob;
    zip;
    countryCode='';
    country_code= '';
    noPatientRole = true;
    dataResponseApi;
    prevcheck;
    registrationTokenEmpty;
    patientId;
    patientRoleId;
    formComplete;
    dataResponse: any;
    dataResponseUser: any;
    acceptTermsConditions;
    staff = true;
    familyMemberName;
    registrationTokenActivity;
    displayNameVisibility:any=0;

    careGiverPatientFName;
    careGiverPatientLName;
    careGiverPatientDOB; 
    @HostListener('document:click', ['$event'])
    clickout(event) {
        if (event && event.target) {
            var classArr = event.target.classList.value.split(" ");
            //password toggle icon press
            if(classArr.indexOf('toggle-password')>-1){
                if(classArr.indexOf('fa-eye-slash') !== -1){
                    $(".toggle-password").removeClass( "fa fa-eye-slash" ).addClass( "fa fa-eye" );
                    $('.toggle-password').attr('data-original-title', "Hide password").tooltip('show');
                }else {
                    $(".toggle-password").removeClass( "fa fa-eye").addClass( "fa fa-eye-slash" );
                    $('.toggle-password').attr('data-original-title', "Show password").tooltip('show');
                }
                var input = $(".password-toggle"); 
                input.attr('type') === 'password' ? input.attr('type','text') : input.attr('type','password');
                input.focus();
            }
            //
            if (classArr.indexOf('fa-user-circle') > -1) {
                this.patient_associated_id = false; 
                this.showBehalfPatientCheck = false;
                this.page = 1;
                if (this.page == 1) {
                    $(".prv-btn").attr('disabled', 'disabled');
                    this.registrationTokenValidated = false;
                }

                console.log($('.first').next());

                $('.first').next().removeClass('done').addClass('disabled');
                $('.last').removeClass('done').addClass('disabled');
                if($('.toggle-password').hasClass('fa-eye')){
                   this.setPasswordToggleiconDefault();
                }
            
            }
            else if (classArr.indexOf('fa-id-card') > -1) {
                console.log("patient_associated_id ::: ",this.patient_associated_id, "--", this.showBehalfPatientCheck,"----",this.staff,"----",this.page);
                this.page = 2;
                if (this.page == 2) {
                    this.registrationTokenConfirmation = false;
                }

                if(this.patient_associated_id && this.showBehalfPatientCheck){
                    var self = this;
                    setTimeout(function () {
                        //console.log("click hehalf1",$('input#signing-behlfs'));
                        $( "#signing-behlfs" ).prop( "checked", true );
                        //console.log(self.careGiverPatientFName, "***" , self.careGiverPatientLName , "***" , self.careGiverPatientDOB);
                        $("#careGiverPatientFName").val(self.careGiverPatientFName);
                        $("#careGiverPatientLName").val(self.careGiverPatientLName);
                        $("#careGiverPatientDOB").val(self.careGiverPatientDOB);

                        self.showBehalfPatientInput();
                      }, 1000);
                    //$("#signing-behlfs").toggle(this.checked);
                    // $("input#signing-behlfs").prop("checked", true);
                    // $("input#signing-behlfs").trigger("click");
                    
                }
                $('.last').removeClass('done').addClass('disabled');
                if($('.toggle-password').hasClass('fa-eye')){
                    this.setPasswordToggleiconDefault();
                }
            }
            else if (classArr.indexOf('fa-check-circle') > -1) {
                this.page = 3;


            } else if(classArr.indexOf('suggestion') > -1 || classArr.indexOf('correction') > -1) {
                var activityData = {
                    activityName: "User Registration",
                    activityType: "email suggestion",
                    activityDescription: "User selected suggested email - " + $('#verimail-email-data')[0].attributes[2].nodeValue  + " instead of " + $('#verimail-email-data')[0].attributes[1].nodeValue,
                    tenantId : this.registrationTenantId,
                };
                this._structureService.trackActivity(activityData);

            }
        }
    }

    setPasswordToggleiconDefault() {
        var input = $(".password-toggle");
        $(".toggle-password").removeClass( "fa fa-eye").addClass( "fa fa-eye-slash" );
        $('.toggle-password').attr('data-original-title', "Show password");
        input.attr('type','password');
        input.focus();
    }


    
    constructor(private route: ActivatedRoute,
        private router: Router, private registrationservice: RegistrationService, private _structureService: StructureService, private _ToolTipService: ToolTipService, private FormsModule: FormsModule,
        private _sharedService: SharedService, private _formBuild: FormBuilder,
    ) { }
    ngOnInit() {
        this.messageTagForm = new FormGroup({
            tenantSites: new FormControl({
                value: this.mySites,
                disabled: false
           },),
        });
        setTimeout(()=>{
            $("#phone").intlTelInput();
           // $("#phone").intlTelInput("setNumber", "+44");  
            $("#phone").on("countrychange", (e, countryData)=> {
                console.log("countryData ::: ", countryData ,countryData.dialCode, this);
                if(countryData.dialCode){
                    this.countryCode = countryData.dialCode;
                    console.log(this.countryCode);    
                    $("#phone").val('+'+this.countryCode);    
                    $("#countryCode").val('+'+this.countryCode);             
                }
            });

            this.getCountryCode();
        },1000);

    //     if(this.countryCode && this.countryCode!='') {
    //         this.countryCode = this.countryCode.replace(/\+/g,"");    
    //     }
    //     $("#phone").val('+'+this.countryCode);

        setTimeout(()=>{
            $("input#email").verimail({
                        denyTempEmailDomains: true,
                        messageElement: "p#status-message"
            });
        },1000);
        $(document).on("focus", "#us-phone-mask-input", function () {
            $(this).mask("(000) 000-000000");
        });
        $(document).on("focus", "#dob", function () {
            $(this).mask("00/00/0000");
        });
      
        this.errorMessageForToken = "Please provide valid Registration ID";
        //console.log("showBehalfPatientCheck*********************",this.showBehalfPatientCheck);
        console.log("this.registrationToken");
        this.registrationTokenValidated = false;
        this.registrationTokenConfirmation = false;
        this.prevcheck = false;
        this.lastnext = false;
        this.page = 1;
        this.acceptTermsConditions = false;
        this.registrationTokenEmpty = false;
        console.log(this.registrationTokenValidated);
        console.log("this.registrationTokenValidated");
       
        $(function () {
            $(".behalf-patient").css("display", "none");
            $(".steps ul li a").removeAttr("href");
            $("#cart-checkout").steps({
                headerTag: "h3",
                bodyTag: "section",
                transitionEffect: 0,
                autoFocus: true
            });
            $(".empty").hide();
            $(".registration-panel .actions").css("display", "none");
            var page = "registration";
            $(".cancel-registration").tooltip({ title: "Close" });

            //$(".cancel-registration").tooltip({ title:  this._ToolTipService.getToolTip(page,'REGWIZ0001') });

        });

        // tooltip initialization 
        setTimeout(function () {
            $('body').tooltip({selector: '[data-toggle="tooltip"]'});
            $('[data-toggle="tooltip"]').tooltip();
        },100);

    }
    acceptTerm() {
        if (this.acceptTermsConditions) {
            this.acceptTermsConditions = false
        }
        this.acceptTermsConditions = true
    }
    showBehalfPatientInput() {
        console.log("Function showBehalfPatientInput ::: ",this.showBehalfPatientCheck,$('input#signing-behlfs') );

        $(function () {
            $('#careGiverPatientDOB').combodate({
                format: 'DD-MM-YYYY',
                template:'MMM D YYYY',
                minYear: 1900,
                firstItem: 'empty',
                maxYear: (new Date()).getFullYear(),
                customClass: 'form-control select2  dob-sec',
                smartDays:true
            });
           // $('.select2').select2();
            $("#tenantSites").select2({
               // placeholder: "Choose site",
               allowClear: true
            });
            $('.select2').select2();
            $('.month').on('select2:select', function (e) {
                $('.day').select2('open');
            });$('.day').on('select2:select', function (e) {
                $('.year').select2('open');
            });
        })
        console.log($('input#signing-behlfs').is(':checked'))
        if (!$('input#signing-behlfs').is(':checked')) {
            this.showBehalfPatientCheck = false;
            $(".behalf-patient").css("display", "none");
            $("#roletype").val("Patient");
            console.log("showBehalfPatientCheck F ::: ",this.showBehalfPatientCheck);
        } else {
            console.log("else");
            this.showBehalfPatientCheck = true;
            $(".behalf-patient").css("display", "block");
            $("#roletype").val("Caregiver");
            console.log("showBehalfPatientCheck T ::: ",this.showBehalfPatientCheck);
        }
    }
    prevclick() {
        
        this.page = this.page - 1;
        if(this.patient_associated_id || this.showBehalfPatientCheck){
            console.log("Staff set false ");
            this.staff = false;
            $(".behalf-patient").css("display", "block");
        }
        if (this.page == 1) {
            $(".prv-btn").attr('disabled', 'disabled');
            this.registrationTokenValidated = false;
            $('.first').next().removeClass('done').addClass('disabled');
            $('.last').removeClass('done').addClass('disabled');
        }
        if (this.page == 2) {
            this.registrationTokenConfirmation = false;
            $('.last').removeClass('done').addClass('disabled');
        }
        console.log('page==prev', this.page)
        if (this.firstprev) {
            $('a[href="#previous"]').trigger("click");
            this.prevcheck = true;
        }
        if( $("#roletype").val()  == "Patient" || $("#roletype").val()  == "Caregiver" ){
            console.log('showBehalfPatientCheck---', this.page)
            //$(".behalf-patient").css("display", "block");
            //this.page = 2;
            this.staff = false;
        }

        if(this.patient_associated_id && this.showBehalfPatientCheck){
            if(this.patient_associated_id && this.showBehalfPatientCheck){
                var self = this;
                setTimeout(function () {
                    //console.log("click hehalf1",$('input#signing-behlfs'));
                    $( "#signing-behlfs" ).prop( "checked", true );
                    //console.log(self.careGiverPatientFName, "***" , self.careGiverPatientLName , "***" , self.careGiverPatientDOB);
                    $("#careGiverPatientFName").val(self.careGiverPatientFName);
                    $("#careGiverPatientLName").val(self.careGiverPatientLName);
                    $("#careGiverPatientDOB").val(self.careGiverPatientDOB);

                    self.showBehalfPatientInput();
                }, 1000);
            }
        } else {
            console.log("its false, so its staff - ", this.patient_associated_id , this.showBehalfPatientCheck);
        }
        if($('.toggle-password').hasClass('fa-eye')) { 
            this.setPasswordToggleiconDefault();
        }
        console.log('page==prev=lasttt', this.page)
        console.log("Prev Clicked! ----",this.patient_associated_id,"----",this.showBehalfPatientCheck,"----",this.staff,"----",this.page);
    }
    isValidEmailAddress(emailAddress) {
        var pattern = /^[-_a-z0-9+]+(\.[-_a-z0-9+]+)*(?:\+[0-9]+)?@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,})$/i;
        return pattern.test(emailAddress);
    };
    validateToken() {
        $('#registrationButtonClick').attr("disabled","disabled");
        //$(".finish-btn").prop("disabled", false);
        var self = this;
        console.log(this.mySites);
        console.log('page==next', this.page)
        $(".empty").hide();
        console.log("herethis.registrationTokenConfirmation===", this.registrationTokenConfirmation)
        console.log("herethis.registrationTokenValidated===", this.registrationTokenValidated)
        if (this.registrationTokenValidated) {
            $('#registrationButtonClick').removeAttr("disabled");
            if (this.registrationTokenConfirmation) {
                console.log(this.page);
                if (this.page == 3) {
                    console.log('lastpage==', this.page)
                    console.log("========================Thrid 333======================================")
                    console.log("here 333=====")
                    console.log('mysites in 3',this.mySites);
                    $('a[href="#next"]').trigger("click");
                    console.log("========================after======================================")
                    this.email = $("#email").val();
                     if(this.mySites.length!=0)
                     {
                       this.selectedSite= $("#tenantSites").val();
                     }
                     console.log(this.selectedSite);
                     console.log(this.mySites.length);
                    this.displayName = $("#display_name").val();
                    this.firstname = $("#firstname").val();
                    this.lastname = $("#lastname").val();
                    if(this.displayNameVisibility==1){
                        this.displayName = this.firstname+" "+this.lastname;
                    }
                    this.country_code = $("#countryCode").val();
                    this.cellnumber = $("#us-phone-mask-input").val();
                    this.cellnumber = this.cellnumber.replace(/[|&;$%@"<>()+, -]/g, "");
                    this.password = $("#password").val();
                    this.confirmpassword = $("#confirmpassword").val();
                    this.dob = $("#dob-date-picker").val();
                    this.zip = $("#zip").val();
                    this.familyMemberName = $("#yourname").val();
                    if (this.email && this.displayName && this.firstname && this.lastname && this.cellnumber && this.password && this.confirmpassword) {
                        if ((this.zip && !this.isSelectedClinician) || this.isSelectedClinician || this.showBehalfPatientCheck ||(this.selectedSite && this.mySites) ) {
                            this.formComplete = true;
                        }
                    }
                    // var details = 'userName=' + this.email + '&displayName=' + this.displayName + '&firstname=' + this.firstname + '&lastname=' + this.lastname + '&password=' + this.password + '&role=' + this.patientId + '&tenantRole=' +  this.patientRoleId + '&mail=' + this.email + '&mobile=' + this.cellnumber + '&registrationToken=' + this.registrationToken + "&environment=" + 'devl' + '&isAccept=' + true + "&dob=" + this.dob + "&zip=" + this.zip + "&isFamilyMember=false";
                    if (this.formComplete) {
                        if (!this.isValidEmailAddress(this.email)) {
                            this._structureService.notifyMessage({
                                messge: 'Email id not valid'
                            });
                        } else if ($("#password").val().length < 8) {
                            this._structureService.notifyMessage({
                                messge: 'Password must be 8 characters minimum'
                            });
                        } else if (!this.isSelectedClinician && !this.showBehalfPatientCheck && (!moment(this.dob, 'DD-MM-YYYY', true).isValid() || moment(this.dob,'DD-MM-YYYY').isSameOrAfter())) {
                            this._structureService.notifyMessage({
                                messge: 'Invalid date of birth'
                            });
                        } else if ($("#password").val() !== $("#confirmpassword").val()) {
                            this._structureService.notifyMessage({
                                messge: 'Password and Confirm Password mismatch'
                            });
                        } else if (!$('input#acceptTerm').is(':checked')) {
                            this._structureService.notifyMessage({
                                messge: 'Please read and accept the terms and conditions of CitusHealth Inc'
                            });
                        } else if (($("#tenantSites").val()== null) && (this.mySites.length!=0) ) {
                            this._structureService.notifyMessage({
                                messge: 'Please select a Site'
                            });
                        } 
                        
                        
                        else {
                            var userDOB = this.dob;
                            var patient_associated_id = 0;
                            var tenantRoleId = this.patientId;
                            if(this.patient_associated_id && !this.isSelectedClinician && this.showBehalfPatientCheck){
                                patient_associated_id = this.patient_associated_id;
                                tenantRoleId = this.currentTenantCareGiverRoleId.id;
                                userDOB = null;
                            } else {
                                console.log("ELSE-----------");
                            }
                            this.registrationToken = this.registrationToken.toLowerCase().replace("staff-", "");
                            var env = this._structureService.environment;
                            if (env)
                                env = env;
                            else
                                env = 'devl';

                                var details = 'userName=' + encodeURIComponent(this.email) + '&displayName=' + this.displayName + '&firstname=' + this.firstname + '&lastname=' + this.lastname + '&password=' + this.password + '&role=' + this.patientRoleId + '&tenantRole=' + tenantRoleId + '&mail=' + encodeURIComponent(this.email) + '&country_code=' + this.country_code + '&mobile=' + this.cellnumber + '&registrationToken=' + this.registrationToken + '&environment=' + env + '&isAccept=true&dob=' + userDOB + '&address=&state=&country=&city=&zip=' + this.zip + "&patient_associated_id=" + patient_associated_id+ '&isFamilyMember=false&familyMemberName=' + this.familyMemberName+ "&siteIds=" + this.selectedSite;
                                if (this.showBehalfPatientCheck) {
                                    console.log('-------showBehalfPatientCheck-------',this.showBehalfPatientCheck)
                                    var checkCaregiver = { "firstname": this.firstname, "lastname": this.lastname, "email": this.email, "operation": "checking" };
                                    // '&role=' + this.patientRoleId + '&tenantRole=' + this.patientId 
                                    this.registrationservice.signupCareGiver(checkCaregiver).then((data) => {
                                        var responceData:any = data;                                    
                                        console.log(details);
                                        var self=this;
                                        if(responceData.userExist === true){
                                            console.log(responceData);
                                            swal({
                                                title: `Caregiver Already Exists.`,
                                                text: ' Do you want to link the patient with this Caregiver?',
                                                type: "warning",
                                                showCancelButton: true,
                                                cancelButtonClass: "btn-default",
                                                confirmButtonClass: "btn-warning",
                                                confirmButtonText: "YES",
                                                cancelButtonText: "NO",
                                                closeOnConfirm: true
                                            },
                                            function (isConfirm) {
                                                if (isConfirm) {
                                                    self.registerUserDetails(details);
                                                } else {
                                                    swal("Cancelled", " :(", "error");
                                                }
                                            });

                                        } else if(responceData.userExist === false){
                                            self.registerUserDetails(details);

                                        }
                                    });
                                } else{
                                    this.registerUserDetails(details);
                                }
                        }
                    }
                    else {
                        this._structureService.notifyMessage({
                            messge: 'Please fill in all required fields'
                        });
                    }
                } else if (this.page == 2) {
                    console.log('checking sites');
                    console.log(this.mySites);
                    /*$("#dob-date-picker").datetimepicker({
                        format: 'MM/DD/YYYY',
                        useCurrent: false,
                        maxDate: moment().format('MMMM D, YYYY')
                    });*/
                    $(function () {
                        this.messageTagForm = this._formBuild.group({
                            tenantSites: [this.mySites]
                        }); 
                        $('#dob-date-picker').combodate({
                            format: 'DD-MM-YYYY',
                            template:'MMM D YYYY',
                            minYear: 1900,
                            firstItem: 'empty',
                            maxYear: (new Date()).getFullYear(),
                            customClass: 'form-control select2 dob-sec',
                            smartDays:true
                        });
                        $('.select2').select2();
                        $('.month').on('select2:select', function (e) {
                            $('.day').select2('open');
                        });$('.day').on('select2:select', function (e) {
                            $('.year').select2('open');
                        });
                    })
                    /*$("#dob-date-picker").on("dp.change", (e) => {
                        $('#dob-date-picker').data("DateTimePicker").date(moment().format('MM/DD/YYYY'));
                    });*/

                    if ($('input#signing-behlfs').is(':checked') && !$("#careGiverPatientFName").val() && !$("#careGiverPatientLName").val() && !$("#careGiverPatientDOB").val() ) {
                        this._structureService.notifyMessage({
                            messge: 'Please fill in all required fields'
                        });
                    } else if (this.isSelectedClinician && !$(".show-staff").val()) {
                        this._structureService.notifyMessage({
                            messge: 'Please fill in all required fields'
                        });
                    } else {
                        if (this.isSelectedClinician) {
                            $("#zip-parent").hide();
                            $("#dob-parent").hide();
                        }

                        this.registrationTokenConfirmation = true;
                        this.prevcheck = false;
                        console.log("herethis.prevcheck===", this.prevcheck)
                        console.log("herethis.registrationTokenConfirmation===", this.registrationTokenConfirmation)
                        console.log("herethis.registrationTokenValidated===", this.registrationTokenValidated)
                        this.page = this.page + 1;
                        console.log("here 444=====")
                        this.lastnext = true;
                        $('a[href="#next"]').trigger("click");

                        if (this.isSelectedClinician) {
                            this.patientId = $(".show-staff option:selected").val();
                            this.patientRoleId = $(".show-staff option:selected").attr("class");
                            // this.patientId = this.dataResponse[i].id;
                            // this.patientRoleId = this.dataResponse[i].roleId;
                            console.log("==================registrationTokenConfirmation===============================")
                            console.log(this.patientId)
                            console.log(this.patientRoleId)
                            console.log("==================registrationTokenConfirmation===============================")
                        }
                    }
                }
            }
            else {
                console.log("PAGE:::",this.page, this.showBehalfPatientCheck ," -- Staff -- " ,this.staff );
                if (this.page == 2) {
                    /*$("#dob-date-picker").datetimepicker({
                        format: 'MM/DD/YYYY',
                        useCurrent: false,
                        maxDate: moment().format('MMMM D, YYYY')
                    });*/

                    $(function () {
                        $('#dob-date-picker').combodate({
                            format: 'DD-MM-YYYY',
                            template:'MMM D YYYY',
                            minYear: 1900,
                            firstItem: 'empty',
                            maxYear: (new Date()).getFullYear(),
                            customClass: 'form-control select2  dob-sec',
                            smartDays:true
                        });
                        $('.select2').select2();
                        $('.month').on('select2:select', function (e) {
                            $('.day').select2('open');
                        });$('.day').on('select2:select', function (e) {
                            $('.year').select2('open');
                        });
                    })
                    /*$("#dob-date-picker").on("dp.change", (e) => {
                        $('#dob-date-picker').data("DateTimePicker").date(moment().format('MM/DD/YYYY'));
                    });*/

                    if ($('input#signing-behlfs').is(':checked') && !$("#careGiverPatientFName").val()) {
                        this._structureService.notifyMessage({
                            messge: 'Please fill in all required fields'
                        });
                    } else if (this.isSelectedClinician && !$(".show-staff").val()) {
                        this._structureService.notifyMessage({
                            messge: 'Please fill in all required fields'
                        });
                    } else {
                        if (this.isSelectedClinician) {
                            $("#zip-parent").hide();
                            $("#dob-parent").hide();
                        }

                        
                        console.log("herethis.prevcheck===", this.prevcheck)
                        console.log("herethis.registrationTokenConfirmation===", this.registrationTokenConfirmation)
                        console.log("herethis.registrationTokenValidated===", this.registrationTokenValidated)
                        
                        console.log("here 444=====",this.displayNameVisibility)
                        if(this.displayNameVisibility==1){
                             $('#display_name_checking').hide();
                        }else{
                             $('#display_name_checking').show();
                        }

                        if (this.isSelectedClinician) {
                            console.log("AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
                            this.staff = true;
                            this.registrationTokenConfirmation = true;
                            this.prevcheck = false;
                            this.page = this.page + 1;
                            this.lastnext = true;
                            $('a[href="#next"]').trigger("click");

                            this.patientId = $(".show-staff option:selected").val();
                            this.patientRoleId = $(".show-staff option:selected").attr("class");
                            // this.patientId = this.dataResponse[i].id;
                            // this.patientRoleId = this.dataResponse[i].roleId;
                            console.log("==================registrationTokenConfirmation===============================")
                            console.log(this.patientId)
                            console.log(this.patientRoleId)
                            console.log("==================registrationTokenConfirmation===============================")
                            console.log("ITS STAFF",this.isSelectedClinician);
                        } else {
                            console.log("BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB");
                            this.staff = false;
                            if (this.showBehalfPatientCheck) {
                                this.staff = false;
                                var self = this;

                                $("#zip-parent").hide();
                                $("#dob-parent").hide();

                                var careGiverPatientFName   = $("#careGiverPatientFName").val();
                                var careGiverPatientLName   = $("#careGiverPatientLName").val();
                                var careGiverPatientDOB     = $("#careGiverPatientDOB").val();

                                this.careGiverPatientFName = careGiverPatientFName;
                                this.careGiverPatientLName = careGiverPatientLName;
                                this.careGiverPatientDOB   = careGiverPatientDOB; 

                                var tmp = careGiverPatientDOB.split('-');
                                var careGiverPatientDOB_ymd = tmp[1]+'-'+tmp[0]+'-'+tmp[2];
                                
                                console.log("ITS PATIENT----",careGiverPatientFName,careGiverPatientLName,careGiverPatientDOB);

                                if(careGiverPatientFName && careGiverPatientLName && careGiverPatientDOB){
                                    var inputdata = { "firstname": careGiverPatientFName, "lastname": careGiverPatientLName, "dob": careGiverPatientDOB, "operation": "checking" };
                                    // '&role=' + this.patientRoleId + '&tenantRole=' + this.patientId 
                                    this.registrationservice.signupCareGiver(inputdata).then((data) => {
                                        var responceData:any = data;
                                        var vitualPatient = false;
                                        if(responceData.userExist === true){
                                            console.log(responceData);
                                            this.patient_associated_id = responceData.data.userid;
                                            var confirmMesssage = careGiverPatientFName+" "+careGiverPatientLName+" DOB("+careGiverPatientDOB_ymd+") already exists.";
                                            vitualPatient = false;

                                            swal({
                                                title: confirmMesssage,
                                                text: "Do you want to register as caregiver for this patient?",
                                                type: "warning",
                                                showCancelButton: true,
                                                cancelButtonClass: "btn-default",
                                                confirmButtonClass: "btn-warning",
                                                confirmButtonText: "Ok",
                                                closeOnConfirm: true
                                            },
                                            function (isConfirm) {
                                                if (isConfirm && vitualPatient == false) {
                                                    self.assosiateCareGiverToExistingPatient();
                                                } else {
                                                    swal("Cancelled", " :(", "error");
                                                }
                                            });

                                        } else if(responceData.userExist === false){
                                            var confirmMesssage = careGiverPatientFName+" "+careGiverPatientLName+" DOB("+careGiverPatientDOB+") not exists.";
                                            vitualPatient = true;
                                            self.insertVirtualPatient(careGiverPatientFName,careGiverPatientLName,careGiverPatientDOB);
                                        }
                                    }); 
                                } else {
                                    this._structureService.notifyMessage({
                                        messge: 'Please fill in all required fields'
                                    });
                                }  
                            } else {
                                $("#zip-parent").show();
                                $("#dob-parent").show();

                                this.registrationTokenConfirmation = true;
                                this.prevcheck = false;
                                this.page = this.page + 1;
                                this.lastnext = true;
                                $('a[href="#next"]').trigger("click");
                            } 
                        }
                    }
                }

            }

        } else {
            $('#registrationButtonClick').removeAttr("disabled");
            this.registrationToken = $("#registration_token").val();
            if (!this.registrationToken) {
                // this._structureService.notifyMessage({
                //     messge:'Please enter the registration ID provided by the Administrator'
                //   }); 
                this.errorMessageForToken = 'Please enter the registration ID provided by the Administrator';
                $(".empty").show();
            } else {
                console.log("==================ist page===============================")
                if (this.registrationToken && this.page == 1) {
                    $(".show-staff").css("display", "block");
                    $("#roletype").css("display", "block");
                    $(".checkbox-outer").css("display", "block");
                    this.registrationTokenEmpty = false;
                    this.registrationTokenActivity = this.registrationToken;
                    this.registrationToken = this.registrationToken.toLowerCase().replace("staff-", "");
                    this.manageNext = true;
                    this.registrationservice.validateRegistrationToken(this.registrationToken).then((data) => {
                        var responceData:any = data; 
                        var self = this;
                        console.log("After validation ::::::::::::::::: ",responceData[0]);
                        //this.mySites = responceData[0].Mysites;
                        console.log('tenant sites');
                        console.log(this.mySites);
                        this.manageNext = false;
                        if(responceData.length==0){
                            var notify = $.notify('Couldn\'t validate the registration ID. Please contact Administrator' );
                            setTimeout(function () {
                              notify.update({ 'type': 'danger', 'message': '<strong>Couldn\'t validate the registration ID. Please contact Administrator</strong>' });
                              
                            }, 1000);
                        }
                        this.currentTenantID = responceData[0].tenant_id;   
                        this.displayNameVisibility=responceData[0].displaNameConfigValue;
                        console.log("RESSS ::::::::::::::::: ",this.displayNameVisibility);
                        this.currentTenantCareGiverRoleId = responceData.filter(function(role) {
                            return role.roleName === "Caregiver";
                        })[0];

                        this.dataResponse = data;
                        //console.log(this.dataResponse.length,this.currentTenantCareGiverRoleId.id);

                        if (this.dataResponse.length) {
                            console.log("validateRegistrationToken :", data , responceData[0].tenant_id);
                            this.registrationTenantId = responceData[0].tenant_id? responceData[0].tenant_id : 0;

                            var activityData = {
                                activityName: "Registration ID Validation",
                                activityType: "user creation",
                                activityDescription: "Registration ID validation success-" + this.registrationTokenActivity,
                                tenantId : this.registrationTenantId
                            };
                            this._structureService.trackActivity(activityData);


                            this.page = this.page + 1;
                            this.isSelectedClinician = false;
                            this.showBehalfPatientCheck = false;
                           this.mySites = responceData[0].Mysites;
                           console.log('responceData');
                           console.log(responceData);
                    
                            console.log("here 111=====")
                            console.log(this.mySites);
                            console.log(typeof(this.mySites));
                            var html = $('a[href="#next"]').html();
                            var clinicianIdentifier = "staff-";
                            this.registrationToken = $("#registration_token").val();
                            $("#regid").html(this.registrationToken);
                            var len=Object.keys(this.mySites).length;
                            console.log('outside');
                           // $("#tenantSites option:selected").removeAttr("selected");
                            $("#tenantSites").attr("multiple", (this.staff) ? "multiple" : "");
                            console.log((this.staff));
                            if (this.staff) {
                                console.log('placeholder');
                                 $("#tenantSites").select2({
                                     placeholder: "Choose site",
                                    allowClear: true
                            });
                            //     $('#tenantSites').select2().on('change', function() {
                            //         $('#tenantSites').select2({data:data[$(this).val()]});
                            //     }).trigger('change');
                            //    // $('#tenantSites').val(null).trigger('change');
                              
                              }
                             
                            
                            for( var i = 0; i<len; i++){
                                var id = this.mySites[i]['id'];
                                var name = this.mySites[i]['name'];
                                
                                $("#tenantSites").append("<option value='"+id+"'>"+name+"</option>");
            
                            }
                            if (this.registrationToken && this.registrationToken.substring(0, clinicianIdentifier.length).toLowerCase() == clinicianIdentifier) {
                                $(".checkbox-outer").css("display", "none");
                                this.staff = true;
                                this.displayNameVisibility=0;
                                this.isSelectedClinician = true;
                                this.dataResponse.sort(this.compare);
                                var select = '<option value="">Select Role</option>';
                                for (let i = 0; i < this.dataResponse.length; i++) {
                                    if (this.dataResponse[i].roleId != 3) {
                                        select = select + '<option class=' + this.dataResponse[i].roleId + '  value=' + this.dataResponse[i].id + '>' + this.dataResponse[i].roleName + '</option>'
                                    }
                                }
                                $(".show-staff").html(select);
                                $(".checkbox-outer").css("display", "none");
                                this.patient_associated_id = false; 
                                this.showBehalfPatientCheck = false;
                            } else {
                                this.staff = false;
                                for (let i = 0; i < this.dataResponse.length; i++) {
                                    //console.log("dataResponse-------",this.dataResponse);
                                    if (this.dataResponse[i].roleId == 3 && this.dataResponse[i].roleName != "Caregiver" && this.dataResponse[i].roleName != "Alternate Contact") {
                                        this.patientId = this.dataResponse[i].id;
                                        this.patientRoleId = this.dataResponse[i].roleId;
                                        $("#roletype").val(this.dataResponse[i].roleName);
                                    }
                                    //   else{
                                    //        this.noPatientRole=false;
                                    //         this._structureService.notifyMessage({
                                    //         messge:'Patient Sign-Up is not available for this Account. Please feel free to contact us (800) 863-9130 or write <NAME_EMAIL>.'
                                    //     }); 
                                    //   }
                                }
                                //this.showBehalfPatientCheck = true;
                            }
                            if (this.isSelectedClinician) {
                                $("#roletype").css("display", "none");
                                $(".checkbox-outer").css("display", "none");
                                if(this.displayNameVisibility==1){
                                    $('#display_name_checking').hide();
                                }else{
                                    $('#display_name_checking').show();
                                }
                            } else {
                                $(".show-staff").css("display", "none");
                                $("#tenantSites").removeAttr("multiple", "multiple");
                            }
                            //  if(this.noPatientRole){
                            this.registrationTokenValidated = true;
                            console.log("here 222=====")
                            $('a[href="#next"]').trigger("click");
                            this.firstprev = true;
                            this.prevcheck = false;
                            $(".prv-btn").removeAttr("disabled");
                            // }

                        }
                        else {
                            this.registrationTenantId = 0;
                            $(".empty").show();
                            var activityData = {
                                activityName: "Failure Registration ID Validation",
                                activityType: "user creation",
                                activityDescription: "Registration ID validation failed-" + this.registrationTokenActivity,
                                tenantId : 0
                            };
                            this._structureService.trackActivity(activityData);
                        }
                    }).catch((ex) => {

                    });
                }   
                else {
                    this.registrationTokenEmpty = true;
                    this.errorMessageForToken = "Please provide valid Registration ID";
                    $(".empty").show();
                }
            }
        }
       /* const resultArray = Object.keys(this.mySites).map(index => {
            let person = this.mySites[index];
            return person;
        });*/
        console.log('page==next=lasttt', this.page);
        console.log(typeof(this.mySites));
        console.log('this.mySites',this.mySites);

        this.messageTagForm = this._formBuild.group({
            tenantSites: [this.mySites]
        });
         console.log('after bind');
    }
    registerUserDetails(details){
        $(".finish-btn").attr('disabled', 'disabled');
                        $(".prv-btn").attr('disabled', 'disabled');
                        this.registrationservice.signup(details).then((data) => {
                            this.dataResponseUser = data;
                            if (!this.dataResponseUser.uid) {
                                $(".finish-btn").removeAttr("disabled");
                                $(".prv-btn").removeAttr("disabled");
                                this._structureService.notifyMessage({
                                    messge: this.dataResponseUser.errorMessage
                                });
                                var activityData = {
                                    activityName: "Failure User Registration",
                                    activityType: "user creation",
                                    activityDescription: "User registration failed. " + this.dataResponseUser.errorMessage + ". Details: " + details,
                                    tenantId : this.registrationTenantId,
                                };
                                this._structureService.trackActivity(activityData);
                            } else {

                                if (this.patientRoleId != 3){
                                    // create shortcode
                                    const reqData = {
                                        "tenantId": this.registrationToken,
                                        "enrollSource": "chwebsite",
                                        "enrollType": "Enrollment",

                                        "userInfo": {
                                            "firstName": this.firstname,
                                            "lastName": this.lastname,
                                            "honorificPrefix": "",
                                            "honorificSuffix": "",
                                            "designation": "",
                                            "organization": this.dataResponseUser.tenantName,
                                            "email": this.email,
                                            "mobile": this.cellnumber,
                                            "countryCode": this.country_code
                                        }
                                    };
                                    this.registrationservice.createReferralCodes(reqData,this.dataResponseUser.wpEnrollUrl).then((data:any) => {
                                        if (data.status && data.status.statusCode != "0") {
                                            const activityData = {
                                                activityName: "Failure Referral Token Create - Signup",
                                                activityType: "manage user enrollment",
                                                activityDescription: "Referral token creation for the user " + this.displayName + "(" + this.dataResponseUser.uid + ") failed due to " + data.status.statusMessage,
                                                tenantId : this.registrationTenantId,
                                            };
                                            this._structureService.trackActivity(activityData);
                                        }
                                    });
                                }
                                var activityData = {
                                    activityName: "User Registration",
                                    activityType: "user creation",
                                    activityDescription: "New user registered (" + this.email + ")",
                                    tenantId : this.registrationTenantId,
                                };
                                if (this.dataResponseUser.isExistingUser) {
                                    activityData.activityName = "User Reactivation";
                                    activityData.activityDescription = "Reactivated the existing user (" + this.email + ")";
                                }
                                this._structureService.trackActivity(activityData);
                                var messge = "";
                                if (this.dataResponseUser.status == 1) {
                                    messge = ' Please login to your account.';
                                    /*this._structureService.notifyMessage({
                                        messge:'Registration Successful. Please login to your account.',
                                        type:'success'
                                    });*/
                                } else {
                                    messge = ' Please wait until the administrator approves your account.';
                                    /*this._structureService.notifyMessage({
                                        messge:'Registration Successful. Please wait until the administrator approves your account.',
                                        type:'success'
                                    });*/
                                }
                                var successMessage;
                                if (this.dataResponseUser.isExistingUser) {
                                    successMessage = this.dataResponseUser.isExistingUser + " ";
                                    /*this._structureService.notifyMessage({
                                        messge:'Registration Successful. '+this.dataResponseUser.isExistingUser,
                                        type:'success'
                                    });*/
                                } else {
                                    successMessage = 'Registration Successful.';
                                }
                                this._structureService.notifyMessage({
                                    messge: successMessage + messge,
                                    type: 'success'
                                });
                                this.router.navigate(['/login']);

                                // if (this.dataResponseUser.tenantCmisId) {
                                //     var registerUserInCmisData = {
                                //         "firstName": this.firstname,
                                //         "middleName": this.lastname,
                                //         "email": this.email,
                                //         "description": "user from " + this.dataResponseUser.tenantName + "tenant",
                                //         "tenantId": this.dataResponseUser.tenantId,
                                //         "userId": this.dataResponseUser.uid
                                //     };
                                //     console.log(registerUserInCmisData);
                                // }
                                // this.registrationservice.registerUserInCmis(registerUserInCmisData).then((data) => {
                                //     if (data) {

                                //         var activityData = {
                                //             activityName: "User Registration in CMIS",
                                //             activityType: "user creation - Completed",
                                //             activityDescription: "New user registered (" + self.email + ")",
                                //         };
                                //         this._structureService.trackActivity(activityData);

                                //     }
                                //     else {
                                //         var activityData = {
                                //             activityName: "Failure User Registration CMIS",
                                //             activityType: "user creation ",
                                //             activityDescription: "Failure user registration in ICAMPP (" + self.email + ")",
                                //         };
                                //         this._structureService.trackActivity(activityData);
                                //     }
                                //     console.log(data);
                                // }).catch((ex) => {
                                // });
                            }
                            //else {
                                //  this._structureService.notifyMessage({
                                //     messge:this.dataResponseUser.errorMessage,
                                //     type:'danger'
                                // });
                                //this.router.navigate(['/login']);
                                /*var activityData = {
                                    activityName: "Failure User Registration",
                                    activityType: "user creation",
                                    activityDescription: "User registeration failed. " + this.dataResponseUser.errorMessage + ". Details: " + details
                                };
                                this._structureService.trackActivity(activityData);*/
                            //}
                        }).catch((ex) => { });
    }


    assosiateCareGiverToExistingPatient(){
        this.registrationTokenConfirmation = true;
        this.prevcheck = false;
        this.page = this.page + 1;
        this.lastnext = true;
        this.isCareGiverSignUp = true;
        this.showBehalfPatientCheck = true;
        //console.log("THIS------------------",this);
        $('a[href="#next"]').trigger("click");
    }
    insertVirtualPatient(careGiverPatientFName,careGiverPatientLName,careGiverPatientDOB){
        this.isCareGiverSignUp = true;
        var inputdata = { "firstname": careGiverPatientFName, "lastname": careGiverPatientLName, "dob": careGiverPatientDOB, "tenantId": this.currentTenantID, "tenantRole": this.patientId, "operation": "createNew" };

        this.registrationservice.signupCareGiver(inputdata).then((data) => {
            var responceData:any = data;
            this.patient_associated_id = responceData.data.virtualPatientId;
            this.showBehalfPatientCheck = true;
            this.registrationTokenConfirmation = true;
            this.prevcheck = false;
            this.page = this.page + 1;
            this.lastnext = true;
            $('a[href="#next"]').trigger("click");

        });
    }
    onLoad() {
        this.registrationTokenValidated = false;
    }

    compare(a, b) {
        if (a.roleName < b.roleName)
            return -1;
        if (a.roleName > b.roleName)
            return 1;
        return 0;
    }

    getCountryCode(){
        this._structureService.getCurrentLocation().subscribe((data)=>{
            //console.log("getCurrentLocation ::: ", data);
            $("#phone").intlTelInput("setCountry", data.country.toLowerCase());     
            let cntryDetails =  $("#phone").intlTelInput("getSelectedCountryData");
            //console.log("getSelectedCountryData ::: ", cntryDetails);
            $("#phone").intlTelInput("setNumber", '+'+cntryDetails.dialCode); 
            this.countryCode=cntryDetails.dialCode;
            //console.log(this,this.countryCode);
            $("#countryCode").val('+'+this.countryCode);  
        });
      }
    navigateToPage(url) {
        this._structureService.navigateToPage(url);
    }

}