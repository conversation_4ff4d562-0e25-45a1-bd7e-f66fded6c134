import { Compo<PERSON>, OnInit, ViewChild, <PERSON>ement<PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/core';
import { Router } from '@angular/router';
import { DatePipe } from '@angular/common';
import { ISubscription, Subscription } from 'rxjs/Subscription';
import { Subject } from 'rxjs';
import {
  CONSTANTS,
  MessagePriority,
  UserGroup,
  DateRanges,
  LISTING_PAGE_DATERANGE_OPTIONS_MESSAGE,
  MessageArchiveType,
  PrivilegeKeys,
  CHECKED,
  Filter,
  NOTIFY_DELAY_TIME_COMMON,
  UserRoles,
  ChatAvatarOptions,
  MessageCategory,
  MessageType,
  DefaultPics,
  ContentTypes,
  MessageTagType,
  msgHistoryDateTimeFormat
} from 'app/constants/constants';
import { FormControl } from '@angular/forms';
import {
  GetChatMessages,
  GetChatMessagesResponse,
  GetChatMessagesResponseData,
  GetMaskedRepliesPayload,
  defaultGetMaskedRepliesPayload
} from 'app/models/message-center/messageCenter';
import { APIs } from 'app/constants/apis';
import { HttpService } from 'app/services/http/http.service';
import { InboxService } from './inbox.service';
import { StructureService } from '../structure.service';
import { userFilterPipe } from './inbox-modal-filter';
import { SharedService } from '../shared/sharedServices';
import { FormsService } from '../forms/forms.service';
import { ToolTipService } from '../tool-tip.service';
import { ChatService } from './chat.service';
import { isBlank } from '../../utils/utils';
import { DateRangeSelected } from '../shared/ch-daterange-picker/date-range-selected.interface';
import { StaticDataService } from '../static-data.service';
import { ManageTagsComponent } from './manage-tags/manage-tags.component';
import { Store, StoreService } from '../shared/storeService';
import { MessageService } from '../../services/message-center/message.service';
import * as moment from 'moment';

declare const $: any;
declare const swal: any;
declare const NProgress: any;
const jstz = require('jstz');

const timezone = jstz.determine();
@Component({
  selector: 'cat-page',
  templateUrl: './inbox.html',
  styleUrls: ['./inbox.citushealth.scss']
})
// TODO: Refactor this component CHP-11610
export class InboxComponent implements OnInit {
  @ViewChild('userSearchFromChat') srch: ElementRef;
  @ViewChild(ManageTagsComponent) manageTagsComponent: ManageTagsComponent;
  inboxData: any = [];
  priorityFilterValue = this.structureService.priorityFilterValue ? this.structureService.priorityFilterValue : 0;
  flagFilterValue: number = this.structureService.flagFilterValue ? this.structureService.flagFilterValue : 0;
  filteredTags = !isBlank(this.structureService.filteredTags) ? this.structureService.filteredTags : [];
  filteredMentions = !isBlank(this.structureService.filteredMentions) ? this.structureService.filteredMentions : [];
  filteredChatThreadType: number[];
  unReadMsg =  this.structureService.unreadMsg === 1 ? 1 : 0;
  inboxDataFirstPage: any = this.structureService.inboxDataFirstPage ? this.structureService.inboxDataFirstPage : [];
  selectedAll = false;
  searchData = {};
  userId = '';
  resetSearchh = false;
  iconPath = '';
  searchFlag = false;
  searchInboxkeyword = '';
  messageGroup: any = 'groups';
  noSearchData = false;
  privileges = this.structureService.getCookie('userPrivileges');

  config:any; 
  userDataConfig: any = {};
  userDetails:any;
  configData:any = {};
  userData:any = {};
  userList: any;
  onInboxData = false;
  userListChatwith: any;
  messageLoadBeforeArchive = false;
  inboxTotalMessageCount;
  perPage = 25;
  totalCount = 0;
  currentPage = 1;
  contentOffset = 0;
  contentLimit = 25;
  previousPage;
  clinicalUserDetails;
  activePatient;
  activeMessage: any;
  chatWithHeading;
  chatWith;
  hasChatWithGroup;
  reRoutedUser;
  modalHead;
  isSubListVisible = false;
  subCountIsVisible = true;
  chatroomSelected;
  rerouteUser: any = 'keep_forwarder';
  selectedBehaviour: any = 'keep_forwarder';
  forwardingBehaviour: number;
  selectedRow: number;
  forwardingStatus = false;
  messageForwardingBehaviour: any;
  optionShow: any = 'staff';
  inboxUnreadMessageCount;
  usersList: any;
  clinicianLoad: any = false;
  beforeLoad = false;
  defaultNurses: any;
  masterStaffsAvalable: any;
  defaultMasterStaffsAvalable: any;
  usersListByRoleId: any;
  scheduledPrimaryCar: any;
  primaryCareClinicians: any;
  primaryClinicalUsers: any;
  disableMessages = true;
  usersListScheduleCheck: any = [];
  inboxDataLength: any = 0;
  selectedInboxItems = false;
  messageReadData: any = {};
  chatroomIds: any = [];
  inventoryIds: any = [];
  signatureDocIds: any = [];
  formSenders: any = [];
  formRecipiers: any = [];
  notifyUsers: any = [];
  readAsButtonText = this.toolTipService.getTranslateData("LABELS.MARK_ALL_AS_READ");
  apiResult;
  archievedAdminMessage: any = '';
  tickedNormalMessageCount = 0;
  patientTopic = { count: localStorage.getItem('patientTopicCount') };
  infoBottomOverlay = false;
  configuringTenantData = false;
  qLimit: any = localStorage.getItem('qLimit') ? parseInt(localStorage.getItem('qLimit')) : 0;
  pageCountMessage: any = localStorage.getItem('pageCountMessage') ? +localStorage.getItem('pageCountMessage') : 1;
  pageLoadAfterLogin = false;
  updateInboxDataSubscriber: ISubscription;
  crossTenantChangeSubscriber: any;
  inboxUnreadMessageCountSubsc: ISubscription;
  reloadOnConfigChangeSubscriber: ISubscription;
  chatWithUserListDataSubs: any;
  newMessageGroupUpSubs: any;
  roterEventSubsc: any;
  chatWithUsersLoading = true;
  firstCheckedBox = '';
  inboxPageMessageCount = 0;
  hideLoadMore = true;
  doLoadmore = false;
  loadMoreCount = 1;
  messageLoader: any = {
    messages: true
  };
  chatWithLoader: any = {
    groups: true,
    staffs: true,
    otherTenantstaff: true,
    patients: true,
    otherTenantPatients: true
  };
  chatWithModalShown = false;
  noMoreItemsAvailable = {
    users: false
  };
  chatwithPageCount = {
    staffs: 0,
    patients: 0
  };
  loadMoremessage = {
    users: 'Load more'
  };
  searchClicked = false;
  selectSiteId: any;
  loadMoreSearchValue: any = undefined;
  callFromInitialCountChatWithUserlist = false;
  tempVar = 'temp';
  hideSiteSelection: boolean;
  eventsSubject: Subject<void> = new Subject<void>();
  patientSiteId: any = [];
  isFilter = false;
  isSiteFilterDisabled = false;
  showFilter = false;
  enableMultisite: any;
  preventMultipleCall = false;
  userGroupIds = CONSTANTS.userGroupIds;
  userRoles = CONSTANTS.userRoles;
  dateRangeFilterOptions = LISTING_PAGE_DATERANGE_OPTIONS_MESSAGE;
  private dateRange: DateRangeSelected;
  dateRanges = new FormControl();
  selectedThread;
  isUserPatient = false;
  isUserPartner = false;
  isUserStaff = false;
  confirmMessageOnArchiveBySingleUser = '';
  CHECKED = CHECKED;
  MESSAGE_PRIORITY = MessagePriority;
  FILTER = Filter;
  messagePriorityData = this.staticDataService.getPriorities();
  dateRangeStoreKey = Store.DATE_RANGE_FILTER_INBOX;
  resetAdvanceSearch = false;
  resetQuickSearch = false;
  chatAvatarOptions = ChatAvatarOptions;
  messageCategory = MessageCategory;
  defaultPics = DefaultPics;
  messageType = MessageType;
  maskedMessageSubList = [];
  isSubListFetching = false;
  labelSite = '';
  inboxUnreadMessageCountFiltered: number;
  labelSiteFilter = '';
  showFilterAppliedMessage: boolean;
  firstLoad = true;
  private eventSubscriptions: Subscription[] = [];
  defaultMsgPics: { [key: string]: string } = {
    'pdg': DefaultPics.PDG,
    'message-group': DefaultPics.MSGGROUP,  
    'broadcast':DefaultPics.BROADCAST     
  };
  constructor(
    private router: Router,
    public _inboxService: InboxService,
    public structureService: StructureService,
    private datePipe: DatePipe,
    private modalFilter: userFilterPipe,
    public _sharedService: SharedService,
    public _formsService: FormsService,
    public _ChatService: ChatService,
    private staticDataService: StaticDataService,
    renderer: Renderer,
    elementRef: ElementRef,
    private toolTipService: ToolTipService,
    private storeService: StoreService,
    private messageService: MessageService,
    private httpService: HttpService
  ) {
    const storedData = this.storeService.getStoredData(Store.ACTIVE_MESSAGE_ADVANCE_SEARCH_FILTER);
    if (!isBlank(storedData)) {
      this.flagFilterValue = +storedData.flag || 0;
      this.priorityFilterValue = +storedData.priority || 0;
      this.filteredTags = storedData.tags
        ? storedData.tags.split(',')
        : [];
      this.filteredChatThreadType = storedData.chatThreadTypes
        ? storedData.chatThreadTypes.split(',').map(Number)
        : [];
      this.structureService.priorityFilterValue = this.priorityFilterValue;
      this.structureService.flagFilterValue = this.flagFilterValue;
      this.structureService.filteredTags = this.filteredTags;
      this.structureService.inboxFilterApplied = Filter.ADVANCE;
      this.showFilterAppliedMessage = true;
      this.showAdvanceSearch();
    }
    this.labelSite = this.staticDataService.getSiteLabelBasedOnMultiAdmission('SITE');
    this.labelSiteFilter = this.staticDataService.getSiteLabelBasedOnMultiAdmission('FILTER_BY_SITES');
    this.config = this.structureService.userDataConfig;
    this.userDataConfig = JSON.parse(this.structureService.userDataConfig);
    this.userDetails = this.structureService.userDetails;
    this.configData = JSON.parse(this.config);
    this.userData = JSON.parse(this.userDetails);
    this.isUserPatient = +this.userData.group === UserGroup.PATIENT;
    this.isUserStaff = +this.userData.group !== UserGroup.PATIENT;
    this.isUserPartner = +this.userData.group === UserGroup.PARTNER;
    this.chatAvatarOptions = ChatAvatarOptions;
    this.messageCategory = MessageCategory;
    this.messageType = MessageType;
    this.defaultPics = DefaultPics;
    if (this.structureService.activeMessageClicked) {
      if (this.structureService.previousUrlNow !== '/inbox/chatroom') {
        this.structureService.inboxDataCurrentPage = 1;
        this.pageCountMessage = 1;
        localStorage.setItem('pageCountMessage','1');
        localStorage.setItem('messageInboxPagination', '1');
        this.structureService.activeMessageClicked = false;
      }
    }
    localStorage.setItem('pageCountMessage', this.pageCountMessage);
    this.enableMultisite = parseInt(this.userData.config.enable_multisite) == 1;
    if (this.userData.mySites.length == 1) {
      this.showFilter = true;
      this.selectSiteId = this.userData.mySites[0]['id'].toString();
    }
    renderer.listen(elementRef.nativeElement, 'click', (event,) => {
      if (!this.searchInboxkeyword) {
        $('#userSearchTxt').val('');
      }
      if (event.path && event.path.length) {
        let hideFlagAccord = true;
        event.path.forEach((ele) => {
          if (ele.classList && Object.keys(ele.classList).some((key) => ele.classList[key] === 'flag-tab')) {
            hideFlagAccord = false;
          }
        });
        if (hideFlagAccord) {
          $('.cat__apps__messaging__tab__time').removeClass('show');
        }
      }
    });
    if (this.structureService.previousUrlNow !== '/inbox/chatroom') localStorage.setItem('messageInboxPagination', '1');
    if (this.structureService.inboxDataFirstPage && this.structureService.previousUrlNow !== '/login') {
      if (this.flagFilterValue) {
        this.structureService.inboxDataFirstPage = this.structureService.inboxDataFirstPage.filter((msg) => {
          return +msg.thread_flag === this.flagFilterValue || +msg.msg_flag === this.flagFilterValue;
        });
      }
      this.inboxData = this.removeSelection(JSON.parse(JSON.stringify(this.structureService.inboxDataFirstPage)));
      this.currentPage = this.structureService.inboxDataCurrentPage;
    }
    if (this.structureService.previousUrlNow === '/inbox/chatroom' && this.structureService.inboxData && this.currentPage !== 1) {
      this.inboxData = this.removeSelection(this.structureService.inboxData);
    } else {
      this.currentPage = this.structureService.inboxDataCurrentPage = 1;
    }
    if (this.structureService.previousUrlNow !== '/inbox/chatroom') localStorage.setItem('messageInboxPagination', '1');
    if (this.structureService.inboxDataFirstPage && this.structureService.previousUrlNow !== '/login') {
      if (this.flagFilterValue) {
        this.structureService.inboxDataFirstPage = this.structureService.inboxDataFirstPage.filter((msg) => {
          return msg.thread_flag == this.flagFilterValue || msg.msg_flag == this.flagFilterValue;
        });
      }
      this.inboxData = JSON.parse(JSON.stringify(this.structureService.inboxDataFirstPage));
      this.currentPage = this.structureService.inboxDataCurrentPage;
    }
    if (this.structureService.previousUrlNow === '/inbox/chatroom' && this.structureService.inboxData && this.currentPage !== 1) {
      this.inboxData = this.structureService.inboxData;
    } else {
      this.currentPage = this.structureService.inboxDataCurrentPage = 1;
    }
    localStorage.setItem('url', this.router.url);
    this.crossTenantChangeSubscriber = this._sharedService.crossTenantChange.subscribe((onInboxData) => {
      this._sharedService.configuringTenantData = true;
      this.configuringTenantData = true;
      this.onInboxData = true;
      if (onInboxData.tenantdataconfigured) {
        this._sharedService.configuringTenantData = false;
        this.configuringTenantData = false;
        this.ngOnInit();
      }
    });

    if (!this.chatWithUserListDataSubs) {
      this.chatWithUserListDataSubs = this._sharedService.chatWithUserListData.subscribe((data) => {
        if (data.variableKey === 'defaultNurses') {
          this.defaultNurses = data.data;
          localStorage.setItem('defaultNurses', JSON.stringify(data.data));
        } else if (data.variableKey === 'usersListByRoleId') {
          this.usersListByRoleId = data.data;
          localStorage.setItem('usersListByRoleId', JSON.stringify(data.data));
        } else if (data.variableKey === 'clinicalUserDetails') {
          this.clinicalUserDetails = data.data;
          localStorage.setItem('clinicalUserDetails', JSON.stringify(data.data));
          this.userListChatwith = this.modalFilter.transform(this.clinicalUserDetails, 'staff');
          this.currentPage = 1;
        } else if (data.variableKey === 'masterStaffsAvalable') {
          this.masterStaffsAvalable = data.data;
          localStorage.setItem('masterStaffsAvalable', JSON.stringify(data.data));
        } else if (data.variableKey === 'defaultMasterStaffsAvalable') {
          this.defaultMasterStaffsAvalable = data.data;
          localStorage.setItem('defaultMasterStaffsAvalable', JSON.stringify(data.data));
        }
      });
    }

    if (!this.updateInboxDataSubscriber) {
      this.updateInboxDataSubscriber = this._sharedService.updateInboxData.subscribe((data) => {
        if (this.showFilterAppliedMessage && data.showFilterAppliedMessage) {
           this.structureService.notifySearchFilterApplied(this.showFilterAppliedMessage);
           this.showFilterAppliedMessage = false;
           this.firstLoad = false;
        }
        if (data.stopLoader) {
          this.messageLoader.messages = false;
          this.totalCount = parseInt(data.totalCount);
          this.structureService.inboxTotalMessageCount = this.totalCount;
          if (!isBlank(data.totalUnreadMessagesCount) && typeof data.totalUnreadMessagesCount === 'number') {
            this.inboxUnreadMessageCountFiltered = +data.totalUnreadMessagesCount;
          }
          localStorage.setItem('inboxTotalMessageCount', JSON.stringify(this.totalCount));
          this.beforeLoad = true;
        } else {
          if (data.inboxData) {
            // Delivery time update call
            const chatroomIds = [];
            data.inboxData.forEach((message) => {
              if (isBlank(message.deliveryTime)) {
                chatroomIds.push(message.chatroomId);
              }
            });
            if (chatroomIds.length > 0) {
              if (+localStorage.getItem('pageCountMessage') <= 1) {
                this._inboxService.updateDeliveryStatus({ delivery_time: new Date().getTime() / 1000 });
              }
            }
          }

          if (this.userId) {
            let dataRes: any = data.inboxData;
            if (data.inboxDataFirstPage) {
              this.inboxDataFirstPage = data.inboxDataFirstPage;
            }
            if (data.inboxTotalMessageCount) {
              this.totalCount = +data.inboxTotalMessageCount;
              this.structureService.inboxTotalMessageCount = this.totalCount;
              localStorage.setItem('inboxTotalMessageCount', JSON.stringify(this.totalCount));
            }
            this.structureService.inboxMessageResultsCount = this.structureService.inboxMessageResultsCount
              ? this.structureService.inboxMessageResultsCount
              : dataRes.length;
            this.messageLoader.messages = false;
            if (!this.structureService.inboxUnreadMessageCount || this.structureService.inboxUnreadMessageCount < 0) {
              this.structureService.inboxUnreadMessageCount = 0;
            }
            const resCounts = {
              messages: this.structureService.inboxUnreadMessageCount
            };
            this._sharedService.unreadCounts.emit({counts: resCounts, types: 'messages', trackRoot:{function: 'updateInboxData.subscribe', page:'inbox.citushealth.ts', lineNo: '215'}});
            if (this.structureService.inboxMessageResultsCount < 1) {
              this.hideLoadMore = true;
              this.messageLoadBeforeArchive = true;
            } else {
              this.hideLoadMore = false;
              this.messageLoadBeforeArchive = false;
            }
            if (data.inboxData && data.inboxData.length) this.resetInboxMarkAsRead(dataRes);
          }
        }
      });
    }
    if (this._sharedService.reloadInbox) {
      this.activate(false);
    }
    
    if (!this.inboxUnreadMessageCountSubsc) {
      this.inboxUnreadMessageCountSubsc = this._sharedService.inboxUnreadMessageCount.subscribe((unreadCountsData) => {
        const { totalUnreadMessagesCount, incrementCount } = unreadCountsData;
        if (totalUnreadMessagesCount >= 0) {
          this.inboxUnreadMessageCountFiltered = totalUnreadMessagesCount;
        } else if (typeof incrementCount !== 'undefined') {
          this.inboxUnreadMessageCountFiltered += incrementCount;
        }
        if (!this.inboxUnreadMessageCountFiltered || this.inboxUnreadMessageCountFiltered < 0) {
          this.inboxUnreadMessageCountFiltered = 0;
        }
      });
    }
    if (!this.reloadOnConfigChangeSubscriber) {
      this.reloadOnConfigChangeSubscriber = this._sharedService.reloadOnConfigChange.subscribe(() => {
        this.config = this.structureService.userDataConfig;
        this.userDataConfig = JSON.parse(this.structureService.userDataConfig);
        this.userDetails = this.structureService.userDetails;
        this.configData = JSON.parse(this.config);
        this.userData = JSON.parse(this.userDetails);
      });
    }
    if (this.structureService.resetLocalStorageData) {
      this.structureService.resetLocalStorageData = false;
      this.structureService.resetLocalStorageDataCommon(true);
    }
    this.confirmMessageOnArchiveBySingleUser = this.toolTipService.getTranslateData('MESSAGES.ARCHIVE_CHAT_SINGLE_USER');
  }
  getSiteIds(data: any, page = ''): void {
    this.selectSiteId = data['siteId'].toString();
    if (page === 'Forward') {
      this.showFilter = true;
      if (this.srch.nativeElement.value.trim()) {
        this.loadMoreUsers(this.optionShow, this.srch.nativeElement.value.trim());
      } else {
        this.initialiseStaffOrPatientListOnClick(this.optionShow);
      }
    } else if (
      this.structureService.previousUrlNow &&
      this.structureService.previousUrlNow !== '/login' &&
      this.structureService.previousUrlNow !== '/inbox/chatroom'
    ) {
      this.activate(false, 0, 1, false);
    } else {
      if (this.preventMultipleCall || this.structureService.getCookie('siteCrossId') || this.structureService.previousUrlNow === '/inbox/chatroom') {
        // call api when returning from inner page to list page
        if (this.structureService.previousUrlNow === '/inbox/chatroom') {
          this.currentPage = +localStorage.getItem('messageInboxPagination');
        }
        this.activate(false, 0, this.currentPage, false);
      }
      this.preventMultipleCall = true;
    }
  }

  hideDropdown(hideItem: any) {
    this.hideSiteSelection = hideItem.hideItem;
  }
  getChatroomUsers(chatroomId: any) {
    this.patientSiteId = [];
    this.isSiteFilterDisabled = false;
    this._ChatService.getUsersListByRoom(chatroomId, 'All', this.selectSiteId).then((result: any) => {
      result.data.allParticipants.forEach((element) => {
        if (+element.roleId === 3) {
          if (!this.patientSiteId.includes(+element.siteId)) this.patientSiteId.push(+element.siteId);
          this.isSiteFilterDisabled = true;
        }
      });
    });
  }
  getPdgData(chatroomId: any) {
    //used to fetch pdg patient data for forward.
    this.patientSiteId = [];
        this.isSiteFilterDisabled = false;
    this._ChatService.getChatroomMessages(chatroomId, true, 0, 0, '', '1', 0, 0, [], [], 'loadPdgData').then((data) => {
            if(data && JSON.parse(JSON.stringify(data))) {
                var result = JSON.parse(JSON.stringify(data));
                if(result && "isPdg" in result && result.isPdg == 1) {
                    if(result && "patient_data" in result && result.patient_data && result.patient_data.length && result.patient_data[0] && "siteId" in result.patient_data[0] && result.patient_data[0].siteId) {
                        this.patientSiteId.push(parseInt(result.patient_data[0].siteId));
                        this.isSiteFilterDisabled = true
                    }
                }
            }
        });
    }
    ngOnDestroy() {
        if(this.eventSubscriptions) {
            this.eventSubscriptions.forEach((subscription) => {
                subscription.unsubscribe();
            });
        }
        if(this.structureService.resetLocalStorageData) {
            this.structureService.resetLocalStorageData = false;
        }
        if(this.updateInboxDataSubscriber) {
            this.updateInboxDataSubscriber.unsubscribe();
        }
        if(this.crossTenantChangeSubscriber) {
            this.crossTenantChangeSubscriber.unsubscribe();
        }
        if(this.chatWithUserListDataSubs) {
            this.chatWithUserListDataSubs.unsubscribe();
        }
        if(this.roterEventSubsc) {
            this.roterEventSubsc.unsubscribe();
        }

        this._sharedService.reInitiateMessagePolling.emit({type: 'message'});
        if(this.newMessageGroupUpSubs) {
            this.newMessageGroupUpSubs.unsubscribe();
        }
        localStorage.setItem("searchInboxkeywordForPolling",this.searchInboxkeyword);
        if(this.reloadOnConfigChangeSubscriber) {
            this.reloadOnConfigChangeSubscriber.unsubscribe();
        }
        if (this.inboxUnreadMessageCountSubsc) {
            this.inboxUnreadMessageCountSubsc.unsubscribe();
        }
        if(!this.structureService.hideAdvanceSearchArea) this.structureService.hideAdvanceSearchArea = !this.structureService.hideAdvanceSearchArea;
    }
    ngOnInit() {
         this.eventSubscriptions.push(this._sharedService.$maskedMessageDeleteRestoreSubject.subscribe((args) => {
            if (args.isChildThread && args.parentChatRoomId && args.chatroomId && args.messageId) {
            const params: GetMaskedRepliesPayload = {
                chatRoomId: args.parentChatRoomId
            }
            this.httpService.doPost(APIs.getMaskedReplyMessages, {...defaultGetMaskedRepliesPayload,...params }, ContentTypes.FORM).subscribe((response) => {
                this.maskedMessageSubList = response.message;
                const index = this.inboxData.findIndex(x=> x.chatroomId === args.parentChatRoomId);
                this.inboxData[index].subList =  response.message;
                this.isSubListFetching = false;
                const subIndex = this.structureService.inboxDataFirstPage.findIndex((x) => x.chatroomId ===  args.chatroomId );
                if(subIndex !== -1) {
                    this.structureService.inboxDataFirstPage[subIndex].subList = response.message;
                }
            });  
            }
          }));
        if (localStorage.getItem('searchInboxkeywordInbox')) {
            this.searchInboxkeyword = localStorage.getItem('searchInboxkeywordInbox');
        }
        if (!isBlank(this.storeService.getStoredData(this.dateRangeStoreKey))) {
            this.dateRange = JSON.parse(this.storeService.getStoredData(this.dateRangeStoreKey));
        }
        this.showToastOnFilter();
        this.readAsButtonText = this.toolTipService.getTranslateData("LABELS.MARK_ALL_AS_READ");
        this.archievedAdminMessage = this.toolTipService.getTranslateData("SUCCESS_MESSAGES.USER_ARCHIVED_MESSAGE");
        if(this.structureService.branchSwitched) {
            this.dateRange = null;
            this.storeService.removeData(this.dateRangeStoreKey);
            this.resetSearch()
            this.structureService.branchSwitched = false;
        }
        this.structureService.flagFilterValue = this.flagFilterValue;
        localStorage.setItem("searchInboxkeywordForPolling",this.searchInboxkeyword);
        if(!isBlank(localStorage.getItem("searchInboxkeywordInbox")) && (this.structureService.previousUrlNow == "/inbox/chatroom")){
            console.log("searchInboxkeywordInbox",localStorage.getItem("searchInboxkeywordInbox"));
            var searchOnRefreshText = localStorage.getItem("searchInboxkeywordInbox");
            this.searchInboxkeyword = searchOnRefreshText;
            $(document).ready(()=>{
            $("#userSearchTxt").val(searchOnRefreshText);
            });
        }
        if(this.structureService.previousUrlNow == "/inbox/chatroom" && this.structureService.inboxData && this.currentPage != 1){
                this.inboxData = this.removeSelection(this.structureService.inboxData);
        }else{
            this.currentPage = this.structureService.inboxDataCurrentPage =1;
        }
        if(localStorage.getItem('inboxTotalMessageCount') && parseInt(localStorage.getItem('inboxTotalMessageCount').replace(/"/g,''))) {
            this.totalCount = parseInt(localStorage.getItem('inboxTotalMessageCount').replace(/"/g,''));
        }
        if(!this.structureService.previousUrlNow || this.structureService.previousUrlNow == "/" || this.structureService.previousUrlNow == "/login") {
            this.pageLoadAfterLogin = true;
        } else {
            this.pageLoadAfterLogin = false;
        }

        this.pageLoadAfterLogin = false;
        
        
        this.config = this.structureService.userDataConfig;       
        this.userDataConfig = JSON.parse(this.structureService.userDataConfig);       
        this.userDetails = this.structureService.userDetails;
        this.configData = JSON.parse(this.config);
        this.userData = JSON.parse(this.userDetails);
        this.userId = this.structureService.getCookie('userId');
        this.iconPath = this.structureService.iconPath;


        if (this.structureService.previousUrlNow == "/login" && this.userData.group == '3') {
           this.infoBottomOverlay = true;
        }

        if(!this.newMessageGroupUpSubs) {
            this.newMessageGroupUpSubs = this._sharedService.newMessageGroupUp.subscribe(() => {
                this.activate(false);
            });
        }

        if(!(this.structureService.previousUrlNow && this.structureService.previousUrlNow !== "/login" || this.structureService.previousUrlNow !== "/inbox/chatroom")) {
            this.messageLoader.messages = false;
        } 
        $(document).ready(()=>{
            $('#exampleModalRerouteInbox').on('hidden.bs.modal', ()=> {
                this.chatWithModalShown = false;
                this.loadMoreSearchValue = undefined;
                this.chatwithPageCount = {
                    staffs: 0,
                    patients: 0
                }
                this.noMoreItemsAvailable = {
                    users: false
                };
                this.userListChatwith = [];
                this.clinicalUserDetails = [];
                this.usersList = [];
                this.userList = [];
            });
            $('#exampleModalRerouteInbox').on('show.bs.modal', ()=> {
                this.chatWithModalShown = true;
                this.loadMoreSearchValue = undefined;
            });
        });
    }
    removeSelection(inboxData){
        inboxData.forEach(element => {
            element.markAsRead = false;
        });
        return inboxData;
    }
    showAdvanceSearch() {
        this.structureService.hideAdvanceSearchArea = !this.structureService.hideAdvanceSearchArea;
        const state = this.structureService.hideAdvanceSearchArea ? 'close' : 'open';
        var activityData = {
          activityName: `${state} advance search in inbox`,
          activityType: 'Advance Search',
          activityDescription: `${this.userData.displayName} ${state} advance search in inbox`,
        };
        this.structureService.trackActivity(activityData);
    }
    applyQuickSearch(event: any) {
        this.resetQuickSearch = false;
        this.filteredTags = [];
        if (this.structureService.inboxFilterApplied === Filter.ADVANCE) this.resetAdvanceSearch = true;
        this.structureService.inboxFilterApplied = Filter.QUICK;
        this.priorityFilterValue = event.highPriority ? 1 : 0;
        this.flagFilterValue = event.highFlag ? 3 : 0;
        this.filteredMentions = event.mention ? [Number(this.userData.userId)] : [];
        this.unReadMsg = event.unread ? 1 : 0;
        if (Object.keys(event).every((key) => !event[key])) this.structureService.inboxFilterApplied = this.structureService.inboxFilterApplied === Filter.QUICK ? '' : this.structureService.inboxFilterApplied;
        this.filterInbox();
    }
    applyAdvanceSearch(event: any) {
        this.resetAdvanceSearch = false;
        this.filteredMentions = [];
        this.unReadMsg = 0;
        if (this.structureService.inboxFilterApplied === Filter.QUICK) this.resetQuickSearch = true;
        this.structureService.inboxFilterApplied = Filter.ADVANCE;
        if (isBlank(event)) {
            this.flagFilterValue = 0;
            this.priorityFilterValue = 0;
            this.filteredTags = [];
            this.filteredChatThreadType = [];
            this.structureService.inboxFilterApplied = this.structureService.inboxFilterApplied === Filter.ADVANCE ? '' : this.structureService.inboxFilterApplied;
            this.storeService.storeData(Store.ACTIVE_MESSAGE_ADVANCE_SEARCH_FILTER, '');
        } else {
            this.flagFilterValue = !isBlank(event.flag) ? parseInt(`${event.flag}`) : 0;
            this.priorityFilterValue = !isBlank(event.priority) ? parseInt(`${event.priority}`) : 0;
            this.filteredTags = !isBlank(event.tags) ? event.tags.split(',') : [];
            this.filteredChatThreadType = !isBlank(event.chatThreadTypes) ? event.chatThreadTypes.split(',').map(Number) : [];
            this.storeService.storeData(Store.ACTIVE_MESSAGE_ADVANCE_SEARCH_FILTER, event);
        }
        this.filterInbox();
    } 
    filterInbox() {
        this.structureService.flagFilterValue = this.flagFilterValue;
        this.structureService.priorityFilterValue = this.priorityFilterValue;
        this.structureService.filteredTags = this.filteredTags;
        this.structureService.filteredMentions = this.filteredMentions;
        this.structureService.unreadMsg = this.unReadMsg;
        this.currentPage = 1;
        localStorage.setItem("messageInboxPagination",JSON.stringify(this.currentPage));
        this.pageCountMessage = 1;
        this.doLoadmore =false;
        this.hideLoadMore = false;
        this.loadPage(this.currentPage);
    }
    showData(data) {
        this.optionShow = data;
        if (this.optionShow == 'staff') {
            this.userListChatwith = this.modalFilter.transform(this.clinicalUserDetails, 'staff');
        } else {
            this.userListChatwith = this.modalFilter.transform(this.clinicalUserDetails, 'patient');
        }
    }
    chatWithModelTrigger() {
        if ($('#chatwith-button-topbar').length) {
            $('#chatwith-button-topbar').trigger("click");
        } else {
            this._sharedService.chatWith.emit(this.userData.group);
        }
    }



    setForwardBehaviour(forwardBehaviour) {
        this.selectedBehaviour = forwardBehaviour;
    }
    setRerouteUser(userdatas, index) {
        this.selectedRow = index;
        $('.reRouteBehaviour').removeAttr('disabled');
        this.reRoutedUser = userdatas;
        if (this.configData.message_forwarding_behaviour === 'user_preference') {
            $('.forwarding-behaviour-box').show();
            this.forwardingStatus = true;
        } else {
            $('.forwarding-behaviour-box').hide();
            this.forwardingStatus = false;
            this.messageForwardingBehaviour = this.configData.message_forwarding_behaviour;
        }
    }
    reRouteMessage() {
        if (this.forwardingStatus == true) {
            this.messageForwardingBehaviour = this.selectedBehaviour;
        }
        let fromId = (this.activeMessage.userid || this.activeMessage.from || this.activeMessage.createdby);
        let fromName = this.activeMessage.fromName;
        const eventId =this.activeMessage.id || "";
        let forwardedToName = this.reRoutedUser.displayname;
        let forwardedToId = this.reRoutedUser.userId;
        let chatroomId = this.activeMessage.chatroomId;
        let data = "toId=" + forwardedToId + "&message=" + this.activeMessage.message + "&chatroomId=" + (chatroomId) + '&rerouteBehaviour=' + this.messageForwardingBehaviour;
        $('#exampleModalRerouteInbox').modal('hide');
        $('#inviteForwardModalSearch').val('');
        let msgTxt = this.toolTipService.getTranslateDataWithParam('MESSAGES.CONFIRM_MESSAGE_FORWARD', {content: forwardedToName});
        let IsEnterClicked=false;
        $(document).keypress(function(event){
            if (event.keyCode == 13) {
                console.log("Enter pressed");
                IsEnterClicked=true;
               
            }
        });
        this.structureService.showAlertMessagePopup({
            text: msgTxt
        }).then((confirm) => {
            if(IsEnterClicked){
                IsEnterClicked=false;
                swal.close();
                $('#exampleModalRerouteInbox').modal('show');
                this.initialiseStaffOrPatientListOnClick(this.optionShow);
                this.selectedRow = -1;
                return false;
            }
            if(confirm) {
                const notify = $.notify(this.toolTipService.getTranslateData('MESSAGES.FORWARDING_MESSAGE'));
                this._inboxService.rerouteMessage(data).then((responseData: any) => {     
                    if (responseData.success) {
                        const successMsg = responseData && responseData.data && responseData.data.invitedMessage ? responseData.data.invitedMessage : this.toolTipService.getTranslateData('SUCCESS_MESSAGES.MESSAGE_FORWARDED_SUCCESSFULLY');
                        this._sharedService.forwardUserVideoCall.emit({ name:this.reRoutedUser.displayname,userid:this.reRoutedUser.userId,roomId:this.activeMessage.chatroomId });
                        setTimeout(() => {
                            notify.update({ 'type': CONSTANTS.notificationTypes.success, 'message': `<strong>${successMsg}</strong>` });
                        }, 1500);
                        let forwardBehaviour = '';
                        if (this.selectedBehaviour == 'remove_forwarder') {
                            let message = '{{fromName}} has exited from this chat session.';
                            let removeForwarder = message.replace(/{{fromName}}/g, this.userData.displayName);
                            this.structureService.socket.emit("userMessagetoServer", { data: removeForwarder, insert: false, chatRoomId: this.activeMessage.chatroomId, notification: true, tenantId: this.userData.tenantId, tenantName: this.userData.tenantName});
                            this.removeScopeMessageOnArchive(this.activeMessage.chatroomId);
                            forwardBehaviour = '. ' + this.userData.displayName + '(' + this.userData.userId + ') has been archived (exited) from this chat session also.';
                        } else {
                            this.activeMessage.forwardAction = 'to';
                            this.activeMessage.forwardName = forwardedToName;
                        }
                        const userForwardInitiatorNotificationData = {
                            sourceId: CONSTANTS.notificationSource.other,
                            sourceCategoryId: CONSTANTS.notificationSourceCategory.userForwardNotificationForInitiator
                        };
                        let deeplinking: any = {  state: 'eventmenu.group-chat', stateParams: { targetID: chatroomId,  targetName: 'group-chat' } };
                        this.structureService.sentPushNotification(fromId, 0, "Your chat session has been forwarded", '', deeplinking, '','',false, true, MessagePriority.NORMAL, eventId, userForwardInitiatorNotificationData);
                        const pushMessage = "You have a new forwarded chat session";
                        deeplinking = {
                            "state": "eventmenu.group-chat",
                            "stateParams": {
                                targetID: chatroomId,
                                targetName: "group-chat"
                            },
                            "activeMessage": {
                                sent: this.activeMessage.sent,
                                messageType: this.activeMessage.messageType||0,
                                baseId: this.activeMessage.baseChatroomId ||0,
                                userid: fromId,
                                fromName: '"'+fromName+'"',
                                message_group_id: this.activeMessage.message_group_id||0,
                                createdby: this.activeMessage.createdby||0
                            },
                            "tenantId": this.userData.tenantId,
                            "tenantName": this.userData.tenantName
                        };
                        if(this.activeMessage.selectedTenantId) {
                            deeplinking.activeMessage['selectedTenantId'] = this.activeMessage.selectedTenantId;
                        }
                        const userForwardRecipientNotificationData = {
                            sourceId: CONSTANTS.notificationSource.other,
                            sourceCategoryId: CONSTANTS.notificationSourceCategory.userForwardNotificationForRecipient
                        };
                        this.structureService.sentPushNotification(forwardedToId, 0, pushMessage, '', deeplinking, '','',false, true, MessagePriority.NORMAL, eventId, userForwardRecipientNotificationData);
                        var messageForward = '{{fromName}} has forwarded this chat session to {{forwardedToName}}';
                        var forwardChatSession = messageForward.replace(/{{fromName}}/g, this.userData.displayName);
                        forwardChatSession = forwardChatSession.replace(/{{forwardedToName}}/g, forwardedToName);
                        this.structureService.socket.emit("userMessagetoServer", { data: forwardChatSession, insert: false, chatRoomId: this.activeMessage.chatroomId, notificationToId: forwardedToId, tenantId: this.userData.tenantId, tenantName: this.userData.tenantName, updateMessageFromUnreadCount: true });
        
                        var activityLogreRouteMessage = this.userData.displayName + "(" + this.userData.userId + ") has forwarded this chat session to " + forwardedToName + "(" + forwardedToId + ") in chatroom (" + (chatroomId) + ")" + forwardBehaviour;
                        var activityData = {
                            activityName: "Reroute Chat Session",
                            activityType: "messaging",
                            activityLinkageId: chatroomId,
                            activityDescription: activityLogreRouteMessage
                        };
                        this.structureService.trackActivity(activityData);
        
        
                    } else {
                        const errorMsg = responseData.data && responseData.data.error && responseData.data.error.message ? responseData.data.error.message : this.toolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG');
                        setTimeout(() => {
                            notify.update({ 'type': CONSTANTS.notificationTypes.warning, 'message': `<strong>${errorMsg}</strong>` });
                        }, NOTIFY_DELAY_TIME_COMMON);
                    }
                }).catch(() => {  
                    setTimeout(() => {
                        notify.update({ 'type': CONSTANTS.notificationTypes.warning, 'message': `<strong>${this.toolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG')}</strong>` });
                    }, NOTIFY_DELAY_TIME_COMMON);
                });
            }            
            if(!confirm){
                swal.close();                
                $('#exampleModalRerouteInbox').modal('show');
                this.initialiseStaffOrPatientListOnClick(this.optionShow);
                this.selectedRow = -1;                
            }
        })
        this.isFilter = false;
    }
    timeConvert(time) {
        time = time.toString().match(/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/) || [time];
        if (time.length > 1) {
            time = time.slice(1);
            time[5] = +time[0] < 12 ? 'a' : 'p';
            time[0] = +time[0] % 12 || 12;
        }
        return time.join('');
    }
    replyForGroupChatSubList(e, chatroomId, message) {
        let setChatWithHeading: any = 0;
        if (message.chatWithRole == 'Caregiver') {
            setChatWithHeading = 1;
        }
        localStorage.setItem('setChatWithHeading', setChatWithHeading);

        let unreadMessageStatus = message.unread;
        if (parseInt(message.unread) || parseInt(message.unreadCount)) {
            this._sharedService.readInboxData.emit(message);
        }


        this.activeMessage = message; // Forward option from chat window.
        var chatWithDob = "";
        var fromDob = "";
        var chatwithDay = new Date(message.chatWithDob).getDay();
        var messageDay = new Date(message.dob).getDay();
        if (message.chatWithDob && !isNaN(chatwithDay) && message.chatWithRole != 'Caregiver') {
            chatWithDob = " DOB " + this.datePipe.transform(message.chatWithDob, 'MM/dd/yy');
        }
        if (message.dob && !isNaN(messageDay) && message.chatWithRole != 'Caregiver') {
            fromDob = " DOB " + this.datePipe.transform(message.dob, 'MM/dd/yy');
        }
        if (message.chatWithRole == 'Caregiver' || message.role == 'Caregiver') {
            fromDob = "";
            chatWithDob = "";
        }
        var chatwith = this.userData.userId === message.userid ? (message.chatWithRoleId === "3" ? message.chatWith + chatWithDob : message.chatWith + ", " + message.chatWithRole) : (message.grp === "3" ? message.fromName + fromDob : message.fromName + ", " + message.role);
        this.chatWithHeading = parseInt(message.message_group_id) && message.groupName ? message.groupName : (parseInt(message.messageType) && parseInt(message.messageType) != 2 ? "Message from " + ((message.userid !== this.userData.userId) ? message.fromName : "Me") : parseInt(message.messageType) != 2 ? "Chat with " + chatwith : message.title);
        var targetID = chatroomId;
        if (!message.isSelfMessage && message.isPatientInitiatedChat && message.messageUnreadCount) {
            var pushMessage = 'Your message is getting reviewed';

            let sentTime = message.messageOrder;
            this.structureService.socket.emit("confirmReviewPushNotification", { chatRoomId: targetID.toString(), messageUserId: message.userid || message.chatCreatedBy }, (data, type) => {
                if (type && data < sentTime) {
                    var deeplinking = {
                        "state": "eventmenu.group-chat",
                        "stateParams": {
                            targetID: targetID,
                            targetName: "group-chat"
                        },
                        "activeMessage": {
                            sent: sentTime,
                            messageType: this.activeMessage.messageType || 0,
                            baseId: this.activeMessage.baseChatroomId || 0,
                            userid: this.activeMessage.userId,
                            fromName: `"${this.activeMessage.chatHeading}"`,
                            message_group_id: this.activeMessage.message_group_id || 0,
                            createdby: this.activeMessage.createdby || 0
                        }
                    };
                    if(this.activeMessage.selectedTenantId) {
                        deeplinking.activeMessage['selectedTenantId'] = this.activeMessage.selectedTenantId;
                    }
                    const messageReviewNotificationData = {
                        sourceId: CONSTANTS.notificationSource.message,
                        sourceCategoryId: CONSTANTS.notificationSourceCategory.staffReviewingPatientMessage
                    };
                    this.structureService.sentPushNotification(message.userid || message.chatCreatedBy.toString(), 0, pushMessage, '', deeplinking, '','', undefined, undefined, undefined, undefined, messageReviewNotificationData);
                }
            });

        }
        this.activeMessage.chatWith = message.chatHeading;
        this.activeMessage.chatWithUserId = message.userid || message.chatCreatedBy;

        var activityData = {
            activityName: "Start Chat Session",
            activityType: "messaging",
            activityLinkageId: targetID,
            activityDescription: "Chat With - " + chatwith + " in Chatroom " + targetID + "." + " Client Time:" + new Date().getHours() + ':' + new Date().getMinutes() + ' ' + new Date().toString().match(/([A-Z]+[\+-][0-9]+.*)/)[1]
        };
        this.structureService.trackActivity(activityData);
        localStorage.setItem('targetId', targetID);
        localStorage.setItem('targetName', 'group-chat');
        localStorage.setItem('chatWithHeading', message.chatHeading || '');
        localStorage.setItem('activeMessage', JSON.stringify(this.activeMessage));
        localStorage.setItem('archived', 'false');
        this.router.navigate(['/inbox/chatroom']);
    }
    gotoChatRoom(evt, message) {
        this._sharedService.goToInnerPage = true;
        this._sharedService.innerPageFilter = true;
        // if(this.userData.group!=3){
        //console.log("Message::::::::::::::::::::::::::::::::::>>>>>>>>>",message);
        setTimeout(() => { 
            let setChatWithHeading: any = 0;
            if (message.chatWithRole == 'Caregiver') {
                setChatWithHeading = 1;
            }
            localStorage.setItem('setChatWithHeading', setChatWithHeading);

            let unreadMessageStatus = message.unread;
            if (message.unreadCount || message.hasUnreadMessages) {
                //console.log("emit inbox click-------------------------------");
                this._sharedService.readInboxData.emit(message);
            }

            if (evt && (((($(evt.target.parentElement).hasClass('from-name') && this.configData.show_prototypes === '1' && this.userData.group !== '3') || ($(evt.target).hasClass('icon-pending') && this.userData.privileges.indexOf('clinicianApprover') !== -1)) && (message.grp === '3' || (message.chatWithRoleId === '3' && message.grp === this.userData.group))) || $(evt.target.parentElement).hasClass('forward-link-col'))) {
                if ($(evt.target.parentElement).hasClass('from-name')) {
                    this.activePatient = message;
                    //$state.go('eventmenu.patient-profile');
                } else if ($(evt.target).hasClass('icon-pending')) {
                    this.activePatient = message;
                    //$scope.popover.show(evt);
                } else {
                    //$scope.selectModal.call(this, 'Reroute to');
                }
            } else if (evt.target.tagName !== 'A') {
                this.activeMessage = message; //Forwad option from chat window.
                var chatWithDob = "";
                var fromDob = "";
                var chatwithDay = new Date(message.chatWithDob).getDay();
                var messageDay = new Date(message.dob).getDay();
                if (message.chatWithDob && !isNaN(chatwithDay) && message.chatWithRole != 'Caregiver') {
                    chatWithDob = " DOB " + this.datePipe.transform(message.chatWithDob, 'MM/dd/yy');
                }
                if (message.dob && !isNaN(messageDay) && message.chatWithRole != 'Caregiver') {
                    fromDob = " DOB " + this.datePipe.transform(message.dob, 'MM/dd/yy');
                }
                var chatwith = "";
                if (message.chatWithRole == 'Caregiver' || message.role == 'Caregiver') {
                    let dob = message.caregiver_dob || message.chatWith_caregiver_dob;
                    fromDob = " DOB " + this.datePipe.transform(dob, 'MM/dd/yy');
                    chatWithDob = " DOB " + this.datePipe.transform(dob, 'MM/dd/yy');
                    chatwith = this.userData.userId === message.userid ? (message.chatWithRoleId === "3" ? ((message.caregiver_userid || message.chatWith_caregiver_userid) ? (message.caregiver_displayname || message.chatWith_caregiver_displayname)+chatWithDob+" ("+message.chatWith+")":message.chatWith + chatWithDob)  : message.chatWith + ", " + message.chatWithRole) : (message.grp === "3" ?((message.caregiver_userid || message.chatWith_caregiver_userid) ? (message.caregiver_displayname || message.chatWith_caregiver_displayname)+fromDob+" ("+message.fromName+")":message.fromName + fromDob) : message.fromName + ", " + message.role);
                }else{
                chatwith = this.userData.userId === message.userid ? (message.chatWithRoleId === "3" ? message.chatWith + chatWithDob : message.chatWith + ", " + message.chatWithRole) : (message.grp === "3" ? message.fromName + fromDob : message.fromName + ", " + message.role); 
                }
                
                this.chatWithHeading = parseInt(message.message_group_id) && message.groupName ? message.groupName : (parseInt(message.messageType) && parseInt(message.messageType) != 2 ? "Message from " + ((message.userid !== this.userData.userId) ? message.fromName : "Me") : parseInt(message.messageType) != 2 ? "Chat with " + chatwith : message.title);
                if(message.messageType == 1) {
                    this.chatWithHeading = 'Broadcast ' + this.chatWithHeading;
                }
                let targetID = message.chatroomId;
                if (!message.isSelfMessage && message.isPatientInitiatedChat && message.messageUnreadCount) {
                    var pushMessage = 'Your message is getting reviewed';

                    let sentTime = message.messageOrder;
                    this.structureService.socket.emit("confirmReviewPushNotification", { chatRoomId: targetID.toString(), messageUserId: message.userid || message.chatCreatedBy }, (data, type) => {
                        if (type && data < sentTime) {
                            var deeplinking = {
                                //"pushType": config.pushDeepLinkCategories.chat + '' + targetID,
                                "state": "eventmenu.group-chat",
                                "stateParams": {
                                    targetID: targetID,
                                    targetName: "group-chat"
                                },
                                "activeMessage": {
                                    sent: sentTime,
                                    messageType: this.activeMessage.messageType || 0,
                                    baseId: this.activeMessage.baseChatroomId || 0,
                                    userid: this.activeMessage.userid || this.activeMessage.chatCreatedBy,
                                    fromName: `"${this.activeMessage.chatHeading}"`,
                                    message_group_id: this.activeMessage.message_group_id || 0,
                                    createdby: this.activeMessage.createdby || 0
                                }
                            };
                            if(this.activeMessage.selectedTenantId) {
                                deeplinking.activeMessage['selectedTenantId'] = this.activeMessage.selectedTenantId;
                            }
                            const eventId = targetID || "";
                            const messageReviewNotificationData = {
                                sourceId: CONSTANTS.notificationSource.message,
                                sourceCategoryId: CONSTANTS.notificationSourceCategory.staffReviewingPatientMessage
                            };
                            this.structureService.sentPushNotification(message.userid || message.chatCreatedBy.toString(), 0, pushMessage, '', deeplinking, '','', false, true, MessagePriority.NORMAL, eventId.toString(), messageReviewNotificationData);
                        }
                    });

                }
                this.activeMessage.chatWith = message.chatHeading;
                this.activeMessage.chatWithUserId = message.userid || message.chatCreatedBy;
                var self = this;



                var activityData = {
                    activityName: "Start Chat Session",
                    activityType: "messaging",
                    activityLinkageId: targetID,
                    activityDescription: "Chat With - " + chatwith + " in Chatroom " + targetID + "." + " Client Time:" + new Date().getHours() + ':' + new Date().getMinutes() + ' ' + new Date().toString().match(/([A-Z]+[\+-][0-9]+.*)/)[1]
                };
                this.structureService.trackActivity(activityData);
                localStorage.setItem('targetId', targetID);
                localStorage.setItem('targetName', 'group-chat');
                localStorage.setItem('chatWithHeading', message.chatHeading || '');
                localStorage.setItem('activeMessage', JSON.stringify(this.activeMessage));
                localStorage.setItem('archived', 'false');
                this.router.navigate(['/inbox/chatroom']);

            }
        }, 100);
    }
    searchMessagesAndDocs() {
        this.selectedAll = false;
        this.currentPage = 1;
        localStorage.setItem("messageInboxPagination",JSON.stringify(this.currentPage));
        this.pageCountMessage = 1;
        this.doLoadmore =false;
        this.hideLoadMore = false;
        this.searchInboxkeyword = $("#userSearchTxt").val();
        localStorage.setItem("searchInboxkeywordForPolling",this.searchInboxkeyword);
        if (this.searchInboxkeyword) {
            this.searchFlag = true;
        } else {
            this.searchFlag = false;
        }
        this.activate(false);
    }
    changeMessagesCount() {
        var pLimit=parseInt(localStorage.getItem('qLimit'));
        console.log('pLimit',pLimit);console.log('this.qLimit',this.qLimit);
        this.doLoadmore =false;
        this.pageCountMessage = 1;
        localStorage.setItem('qLimit', this.qLimit);
        $('#userSearchTxt').val("");
        
        this.activate(true, this.qLimit,1);
    }
    getInboxCounts(types) {
        this.structureService.getInboxCounts(types,'',this.selectSiteId).then((inboxCounts)=> { 
            var resCounts = JSON.parse(JSON.stringify(inboxCounts));
            if(resCounts && resCounts.messages && resCounts.messages > 0) {
                this.structureService.inboxUnreadMessageCount = parseInt(resCounts.messages);
            } else {
                this.structureService.inboxUnreadMessageCount = 0;
            }
            this._sharedService.unreadCounts.emit({counts: resCounts, types: types, trackRoot:{function: 'getInboxCounts', page:'inbox.citushealth.ts', lineNo: '1017'}});
        }, (err)=> {
            this.structureService.inboxUnreadMessageCount = 0;
        });
    }
    updateInboxCounts(operationType, count=0) {
        const increment = (operationType == 'add' ? 1 : -1 ) * count;
        this.inboxUnreadMessageCountFiltered = +this.inboxUnreadMessageCountFiltered + increment > 0 ? +this.inboxUnreadMessageCountFiltered + increment : 0;
        this.structureService.inboxUnreadMessageCount = this.structureService.inboxUnreadMessageCount + increment > 0 ? this.structureService.inboxUnreadMessageCount + increment : 0;
        var resCounts = {
            "messages":this.structureService.inboxUnreadMessageCount
        }
        this._sharedService.unreadCounts.emit({counts: resCounts, types: 'messages', trackRoot:{function: 'updateInboxCounts', page:'inbox.citushealth.ts', lineNo: '1039'}});
    }
    activateForNewChatroomPolling(pollingData,qlimit=0,pageCountMessage=0) {
        this.messageService.getChatMessages({chatroomId: +pollingData.args.chatroomId}).subscribe( (response:any)=> {
            var data = [];
            const message = response && response.data && response.data.message ? response.data.message : [];
            if(message && message.chatroomId) {
                data.push(message);
                data = [...this.inboxData, ...data];    
            }
            console.log(data);
            if(data.length){
                data.map( (message)=> {
                    message.sent = message.sent ? message.sent : message.requestedOn;
                    message.maskedSent = message.sent;
                    if(!message.subList) {
                        message.subList = [];
                        message.subListIDs = [];
                    }
                    if (!parseInt(message.baseChatroomId)) {
                        if(!message.maskedUnreadCount) {
                            message.maskedUnreadCount = 0;
                        }
                        var maskedUnreadCountSet = false;
                        data.filter( (filteredData)=> {
                            if (message.messageType == '2') {
                                filteredData.initiatedBaseId = filteredData.baseChatroomId;
                            }
                            if (message.messageType == '2' && message.chatroomId == filteredData.baseChatroomId && parseInt(message.messagesCount) <= parseInt(filteredData.messagesCount)) {
                                if (!message.isSelfMessage) {
                                    message.baseChatroomId = 0;
                                    message.maskedUnreadCount = parseInt(message.maskedUnreadCount) + parseInt(filteredData.unreadCount);
                                    message.unreadCount = 0;
                                    message.hasUnreadMessages = false;
                                    filteredData.baseChatroomId = 0;
                                    filteredData.messageType = '2';
                                    filteredData.hiddenChatRoomId = message.chatroomId;
                                    message.movedToSublist = true;
                                } else {
                                    filteredData.showmaskedlogo = false
                                    message.subList.push(filteredData);
                                    message.subListIDs.push(filteredData.chatroomId);
                                    message.maskedUnreadCount = parseInt(message.maskedUnreadCount) + parseInt(filteredData.unreadCount);
                                    filteredData.sent = filteredData.sent ? filteredData.sent : filteredData.requestedOn;
                                    if (filteredData.sent > message.maskedSent) {
                                        message.maskedSent = filteredData.sent;
                                    }
                                    filteredData.movedToSublist = true;
                                }
                                maskedUnreadCountSet = true;
                            }
                        });
                        if(message.messageType == '2' && !maskedUnreadCountSet && !message.maskedUnreadCount) {
                            message.maskedUnreadCount = message.unreadCount
                        }
                        if (message.updated_date) {
                            message.maskedSent = message.updated_date;
                        }
                    } else {
                        if(message.baseChatroomId) {
                            message.showmaskedlogo = true
                        }
                    }
                });
                data = data.filter( (filteredData)=> {
                    return (filteredData.category == 'chat-messages' ? (parseInt(filteredData.baseChatroomId) == 0 || !filteredData.movedToSublist) : true);
                });
                this.resetInboxMarkAsRead(data,true);
                if(parseInt(message.unreadCount) > 0) {
                    this.updateInboxCounts('add',parseInt(message.unreadCount));
                }
                }
        })
    }
  showToastOnFilter() {
    const dateRange = JSON.parse(this.storeService.getStoredData(Store.DATE_RANGE_FILTER_INBOX));
    this.showFilterAppliedMessage = !isBlank(localStorage.getItem('searchInboxkeywordInbox')) || !isBlank(this.storeService.getStoredData(Store.ACTIVE_MESSAGE_ADVANCE_SEARCH_FILTER)) || !isBlank(dateRange) && !(dateRange.type === undefined || dateRange.type === DateRanges.LAST_THIRTY_DAYS);
    if (this.firstLoad || !this.showFilterAppliedMessage) {
        this.firstLoad = false;
        this.structureService.notifySearchFilterApplied(this.showFilterAppliedMessage);
        this.showFilterAppliedMessage = false;
    }
  }
  activate(loaderStatus, qlimit = 0, pageCountMessage = 1, fromPolling: any = false, pin = false) { 
        localStorage.setItem('pageCountMessage', String(pageCountMessage));
        NProgress.start();
        this.searchFlag = !!this.searchInboxkeyword;
        localStorage.setItem("searchInboxkeywordInbox",this.searchInboxkeyword);
        localStorage.setItem("searchInboxkeywordForPolling",this.searchInboxkeyword);
        const userData  = this.userData;
        if(!loaderStatus)
        this.messageLoader.messages = true;
        this.hideLoadMore = false;
        this.checkDateRangeFilterInboxSession();
        this.showToastOnFilter();
        if (this.userId && this.searchFlag) {
            const params: GetChatMessages = {
                loaderStatus,
                searchKeyword: this.searchInboxkeyword,
                archived: 0,
                pageCount: pageCountMessage,
                fromInboxPagination: true,
                flagValue: this.flagFilterValue,
                messagePriority: this.priorityFilterValue,
                tagIds: !isBlank(this.filteredTags)? this.filteredTags.join(',') : '',
                mentionedUsers: !isBlank(this.filteredMentions)? this.filteredMentions.join(',') : '',
                siteIds: this.selectSiteId,
                dateRange: this.dateRange,
                unread: !!this.unReadMsg,
                chatThreadTypes: this.filteredChatThreadType
            }
            this.messageService.getChatMessages(params).subscribe((response: GetChatMessagesResponse) => {
                let dataRes: any = response.data.messages;
                if (!dataRes || !dataRes.length) {
                    this.hideLoadMore = true;
                    this.messageLoadBeforeArchive = true;
                }
                if(this.doLoadmore && !loaderStatus){
                    dataRes = [...this.inboxData, ...dataRes];
                }
                this.loadMoreCount = dataRes.length;
                if(this.loadMoreCount && this.searchFlag){
                    this.noSearchData = true;
                    this.inboxData = [];
                } else {
                    this.noSearchData = false;
                    if(this.loadMoreCount < 20) this.hideLoadMore = true;
                }
                if (pageCountMessage === 1) {
                    this.totalCount = response.data.totalChatRoomsCount;
                    this.inboxData = [];
                    this.structureService.inboxData = [];
                    this.structureService.inboxDataFirstPage = [];
                    this.inboxUnreadMessageCountFiltered = +response.data.totalUnreadMessagesCount;
                    this.structureService.inboxTotalMessageCount = this.totalCount;
                    localStorage.setItem("inboxTotalMessageCount",JSON.stringify(this.totalCount));
                }
                NProgress.done();
                this.structureService.inboxMessageResultsCount = dataRes.length;
                if(dataRes.length) {
                dataRes.map((message) => {
                    message.sent = message.sent || message.requestedOn;
                    message.maskedSent = message.sent;
                    if(!message.subList) {
                        message.subList = [];
                        message.subListIDs = [];
                    }
                    if (!parseInt(message.baseChatroomId)) {
                        if(!message.maskedUnreadCount) {
                            message.maskedUnreadCount = 0;
                        }
                        let maskedUnreadCountSet = false;
                        dataRes.filter(function (filteredData) {
                            if (message.messageType == '2') {
                                filteredData.initiatedBaseId = filteredData.baseChatroomId;
                            }
                            if (+message.messageType === 2 && message.chatroomId == filteredData.baseChatroomId && parseInt(message.messagesCount) <= parseInt(filteredData.messagesCount)) {
                                if (+message.userid !== +userData.userId || +message.chatCreatedBy !== +userData.userId) {
                                    message.baseChatroomId = 0;
                                    message.maskedUnreadCount = parseInt(message.maskedUnreadCount) + parseInt(filteredData.unreadCount);
                                    message.unreadCount = 0;
                                    message.hasUnreadMessages = false;
                                    filteredData.baseChatroomId = 0;
                                    filteredData.messageType = '2';
                                    filteredData.hiddenChatRoomId = message.chatroomId;
                                    message.movedToSublist = true;
                                } else {
                                    filteredData.showmaskedlogo = false
                                    message.subList.push(filteredData);
                                    message.subListIDs.push(filteredData.chatroomId);
                                    message.maskedUnreadCount = parseInt(message.maskedUnreadCount) + parseInt(filteredData.unreadCount);
                                    filteredData.sent = filteredData.sent ? filteredData.sent : filteredData.requestedOn;
                                    if (filteredData.sent > message.maskedSent) {
                                        message.maskedSent = filteredData.sent;
                                    }
                                    filteredData.movedToSublist = true;
                                }
                                maskedUnreadCountSet = true;
                            }
                        });
                        if(message.messageType == '2' && !maskedUnreadCountSet && !message.maskedUnreadCount) {
                            message.maskedUnreadCount = message.unreadCount
                        }
                        if (message.updated_date) {
                            message.maskedSent = message.updated_date;
                        }
                    } else {
                        if(message.baseChatroomId) {
                            message.showmaskedlogo = true
                        }
                    }
                });
                console.log('--------------'+dataRes.length+'-------------');
                dataRes = dataRes.filter(function (filteredData) {
                    return (filteredData.category == 'chat-messages' ? (parseInt(filteredData.baseChatroomId) == 0 || !filteredData.movedToSublist) : true);
                });
                }
                this.resetInboxMarkAsRead(dataRes, loaderStatus, pageCountMessage === 1);
                this.searchFlag = false;
                this.messageLoader.messages = false;
            },() => {
                this.messageLoader.messages = false;
                NProgress.done();
            });
        } else {
            this._inboxService.setLocalStorageData();
            const params: GetChatMessages = {
                loaderStatus: loaderStatus,
                archived: 0,
                flagValue: this.flagFilterValue,
                messagePriority: this.priorityFilterValue,
                searchKeyword: '',
                fromInboxPagination: true,
                pageCount: pageCountMessage,
                tagIds: !isBlank(this.filteredTags)? this.filteredTags.join(',') : '',
                dateRange: this.dateRange,
                mentionedUsers: !isBlank(this.filteredMentions)? this.filteredMentions.join(',') : '',
                siteIds: this.selectSiteId,
                unread: !!this.unReadMsg,
                chatThreadTypes: this.filteredChatThreadType
            };
            this.messageService.getChatMessages(params).subscribe((data: GetChatMessagesResponse) => {
                this.inboxUnreadMessageCountFiltered = data.data.totalUnreadMessagesCount;
                let dataRes: any = data.data.messages;
                if(!isBlank(dataRes)){
                        //deliverytime update call
                        const chatroomIds = dataRes
                            .filter((message: GetChatMessagesResponseData) => message.chatroomId && isBlank(message.deliveryTime))
                            .map((message) => message.chatroomId);
                        if(chatroomIds.length)
                        {
                            if(pageCountMessage <= 1)
                                {
                                    this._inboxService.updateDeliveryStatus({delivery_time: parseInt(String(new Date().getTime()/1000))}).then((data)=>{
                                        console.log("deliverytime update call",data); 
                                    })
                                } 
                        }                      
                }
                if (pageCountMessage === 1) {
                    this.totalCount = +data.data.totalChatRoomsCount;
                    this.structureService.inboxTotalMessageCount = this.totalCount;
                    localStorage.setItem("inboxTotalMessageCount",JSON.stringify(this.totalCount));
                }
                NProgress.done();
                if((pageCountMessage === 1 || (fromPolling && fromPolling.args && fromPolling.args.type == 'messages')) && !pin) {                      
                    if(!fromPolling || (fromPolling && fromPolling.args && fromPolling.args.type == 'messages' && fromPolling.args.setCount && fromPolling.args.setCount == 1)) {
                        let types = 'messages';
                        this.getInboxCounts(types);
                    } else if((fromPolling && fromPolling.args && fromPolling.args.type == 'messages' && fromPolling.args.setCount && fromPolling.args.setCount == 2)) {
                        this.updateInboxCounts('add');
                    }
                }
                

                if (!dataRes.length) {
                    this.hideLoadMore = true;
                    this.messageLoadBeforeArchive = true;
                }
                if(!(pageCountMessage === 1 || (fromPolling && fromPolling.args && fromPolling.args.type == 'messages'))) {
                    if(this.doLoadmore && !loaderStatus){
                        dataRes = [...this.inboxData, ...dataRes];
                    }
                }
                this.structureService.inboxMessageResultsCount = dataRes.length;
                let unreadCount = 0;
                let unreadFormCount = 0;
                if(dataRes.length){
                dataRes.map(function (message) {
                    message.sent = message.sent ? message.sent : message.requestedOn;
                    message.maskedSent = message.sent;
                    if(!message.subList) {
                        message.subList = [];
                        message.subListIDs = [];
                    }
                    if (!parseInt(message.baseChatroomId)) {
                        if(!message.maskedUnreadCount) {
                            message.maskedUnreadCount = 0;
                        }
                        let maskedUnreadCountSet = false;
                        dataRes.filter(function (filteredData) {
                            if (message.messageType == '2') {
                                filteredData.initiatedBaseId = filteredData.baseChatroomId;
                            }
                            if (message.messageType == '2' && message.chatroomId == filteredData.baseChatroomId && parseInt(message.messagesCount) <= parseInt(filteredData.messagesCount)) {
                                if (!message.isSelfMessage) {
                                    message.baseChatroomId = 0;
                                    message.maskedUnreadCount = parseInt(message.maskedUnreadCount) + parseInt(filteredData.unreadCount);
                                    message.unreadCount = 0;
                                    message.hasUnreadMessages = false;
                                    filteredData.baseChatroomId = 0;
                                    filteredData.messageType = '2';
                                    filteredData.hiddenChatRoomId = message.chatroomId;
                                    message.movedToSublist = true;
                                } else {
                                    filteredData.showmaskedlogo = false
                                    message.subList.push(filteredData);
                                    message.subListIDs.push(filteredData.chatroomId);
                                    message.maskedUnreadCount = parseInt(message.maskedUnreadCount) + parseInt(filteredData.unreadCount);
                                    filteredData.sent = filteredData.sent ? filteredData.sent : filteredData.requestedOn;
                                    if (filteredData.sent > message.maskedSent) {
                                        message.maskedSent = filteredData.sent;
                                    }
                                    filteredData.movedToSublist = true;
                                }
                                maskedUnreadCountSet = true;
                            }
                        });
                        if(message.messageType == '2' && !maskedUnreadCountSet && !message.maskedUnreadCount) {
                            message.maskedUnreadCount = message.unreadCount
                        }
                        if (message.updated_date) {
                            message.maskedSent = message.updated_date;
                        }
                    } else {
                        if (message.baseChatroomId) {
                            message.showmaskedlogo = true
                        }
                    }
                });
                dataRes = dataRes.filter(function (filteredData) {
                    if(filteredData.category != 'chat-messages' || (filteredData.category == 'chat-messages' && (parseInt(filteredData.baseChatroomId) == 0 || filteredData.baseChatroomId == 'hide'))) {
                        unreadCount = unreadCount + (filteredData.messageType=='2' ? (parseInt(filteredData.maskedUnreadCount) ? parseInt(filteredData.maskedUnreadCount) : 0) : parseInt(filteredData.unreadCount || filteredData.unread));
                    }
                    if (filteredData.category=='structured-forms') {
                        unreadFormCount = unreadFormCount + parseInt(filteredData.unread);
                    }
                    return (filteredData.category == 'chat-messages' ? (parseInt(filteredData.baseChatroomId) == 0 || !filteredData.movedToSublist) : true);
                });
                
                }
                this.resetInboxMarkAsRead(dataRes,loaderStatus, pageCountMessage === 1);
                this.resetSearchh = false;
                this.messageLoader.messages = false;
            },() => {
                this.messageLoader.messages = false;
            }),() => {
                this.messageLoader.messages = false;
                NProgress.done();
            };
        }
    }
       /*
    ***** loadPage() for Pagination Implemented by A for inbox *****
    */
   loadPage(page: number){ 
    localStorage.setItem("messageInboxPagination",JSON.stringify(page));
    console.log("page",page);
        this.previousPage = page;
        this.currentPage = page;
        this.contentOffset = (page - 1) * this.contentLimit + 1;
        this.messageLoader.messages = false;
        this.pageCountMessage = this.pageCountMessage;
        var searchKeyword = this.searchInboxkeyword ? this.searchInboxkeyword : '';
        localStorage.setItem("searchInboxkeywordForPolling",this.searchInboxkeyword);
        var days =" All Days ";
        var actdescription =" , Filte: "+days;
        if(this.qLimit && this.qLimit != 0){
        
            days =this.qLimit+" Days "
            actdescription =" , Filte: "+days;           
        }
        if(searchKeyword){
        this.searchFlag = true; 
        actdescription = actdescription +", Search Keyword "+searchKeyword;  
        }
        this.doLoadmore =true;
        let unreadCount =this.inboxUnreadMessageCountFiltered;
        var activityData = {
            activityName: "Pagination Messages",
            activityType: "manage messaging",
            activityDescription: "Paginated "+this.pageCountMessage+ " Page, Unread Count: " +unreadCount+" "+ actdescription
        }       
        this.structureService.trackActivity(activityData);
        this.activate(true,this.qLimit,page);  
        this.resetSearchh = false;      
}
    loadMoreMessages(){ 
        $('.cat__apps__messaging__list button').attr('disabled',false);
        this.tickedNormalMessageCount=0;
        this.firstCheckedBox='';
        this.messageLoader.messages = false;
        this.pageCountMessage++;
        var searchKeyword = this.searchInboxkeyword ? this.searchInboxkeyword : '';
        localStorage.setItem("searchInboxkeywordForPolling",this.searchInboxkeyword);
        localStorage.setItem('pageCountMessage', this.pageCountMessage);
        var days =" All Days "
        var actdescription =" , Filte: "+days;
        if(this.qLimit && this.qLimit != 0){
            
                days =this.qLimit+" Days "
                actdescription =" , Filte: "+days;           
        }
        if(searchKeyword){
          this.searchFlag = true; 
          actdescription = actdescription +", Search Keyword "+searchKeyword;  
        }
        this.doLoadmore =true;
        let unreadCount =this.inboxUnreadMessageCountFiltered;
        let activityData = {
            activityName: "Load More Messages",
            activityType: "manage messaging",
            activityDescription: "Loaded "+this.pageCountMessage+ " Page, Unread Count: " +unreadCount+" "+ actdescription
        }
       
        this.structureService.trackActivity(activityData);

        
        this.activate(false,this.qLimit,this.pageCountMessage);        
    }
    resetInboxMarkAsRead(dataRes,loaderStatus=true, setInitialPageData=false) {
        const self= this;     
        dataRes.map((message) => {
            message.markAsRead = (self.inboxData.length && self.inboxData.some((y) => y.chatroomId === message.chatroomId && y.markAsRead)) || message.markAsRead;
            message.pinnedStatus = (self.inboxData.length && self.inboxData.some((y) => y.chatroomId === message.chatroomId && y.pinnedStatus)) || message.pinnedStatus;
            return this.restructureTags(message);
        });
        this.orderInboxData(dataRes);
        let pageCount = parseInt(localStorage.getItem("messageInboxPagination"));
        if(pageCount == 1 && setInitialPageData) {
            this.inboxDataFirstPage =  this.inboxData;
            this.structureService.inboxDataFirstPage = JSON.parse(JSON.stringify(this.inboxDataFirstPage));
        }
        this.totalCount=this.structureService.inboxTotalMessageCount;
        this.structureService.inboxTotalMessageCount = this.totalCount;
        localStorage.setItem("inboxTotalMessageCount",JSON.stringify(this.totalCount));
        this.searchFlag = false;    
        this.beforeLoad = true;
        this.inboxDataLength = this.inboxData.length;
        this.inboxPageMessageCount = this.inboxData.length;
        this.structureService.inboxData = this.inboxData;
        this.structureService.inboxDataCurrentPage = this.currentPage;
        this._sharedService.onInboxData.emit({inboxData : this.inboxData, emitUpdateInboxData: false});
        this.tickedNormalMessageCount = this.inboxData.filter((x)=> x.markAsRead).length;
        this.disableMessages = true;
    }
    expandSubMessageList(e, message, i) {
        this.chatroomSelected = message.chatroomId;
        this.isSubListVisible = !this.isSubListVisible;        
        if(this.isSubListVisible) {
            this.isSubListFetching = true;
            const params: GetMaskedRepliesPayload = {
                chatRoomId: message.chatroomId,
            }
            this.httpService.doPost(APIs.getMaskedReplyMessages, { ...defaultGetMaskedRepliesPayload, ...params }, ContentTypes.FORM).subscribe((response) => {
                this.maskedMessageSubList = response.message;
                this.inboxData[i].subList =  response.message;
                e.stopPropagation();
                this.isSubListFetching = false;
                const subIndex = this.structureService.inboxDataFirstPage.findIndex((x) => x.chatroomId === message.chatroomId);
                if(subIndex !== -1) {
                    this.structureService.inboxDataFirstPage[subIndex].subList = response.message;
                }
            });   
        }
    }
    deleteMaskedMessages(msg) {
        let IsEnterClicked = false;
        $(document).keypress((event)=> {
          if (event.keyCode == 13) {
              console.log("Enter pressed");
              IsEnterClicked=true;           
          }
        }); 
        this.structureService.showAlertMessagePopup({
            text: this.confirmMessageOnArchiveBySingleUser,
        }).then((confirm) => {
            console.log('IsEnterClicked',IsEnterClicked);
            if(IsEnterClicked){
              IsEnterClicked=false;
                swal.close();   
                return false;
            }
            if(confirm) {
            const data = {
                baseId: msg.baseChatroomId || 0,
                initiatedBaseId: msg.initiatedBaseId,
                userId: this.userId,
                createdby: msg.createdby,
                chatroomid: msg.chatroomId,
                subList: '',
                invitedStatus:msg.invited_status,
                displayname: this.userData.displayName
            }
        if(msg.subList && msg.subList.length) {
            const result = msg.subList.map(function(sub) {
                return sub.chatroomId;
            }).join(',');
            data.subList = result;
        }
        this._inboxService.deleteMaskedMessages(data).then((res) => {
            const response = JSON.parse(JSON.stringify(res));
            if(response.status) {
                const pollingMessage = (this.archievedAdminMessage).replace(/{{displayName}}/g, this.userData.displayName);
                let chatroomIds = [];
                if( data.invitedStatus == '0') {
                    chatroomIds.push(data.chatroomid);
                    if(data.subList != '') {
                        $.merge(chatroomIds,data.subList.split(','));
                    }
                } else {
                    chatroomIds.push(data.chatroomid);
                    if(data.userId == data.createdby) {
                        chatroomIds.push(data.initiatedBaseId);
                    }
                }
                chatroomIds.forEach(id => {
                    this.structureService.socket.emit("userMessagetoServer", { data: pollingMessage, notification: true, chatRoomId: id, insert: false, tenantId: this.userData.tenantId, tenantName: this.userData.tenantName });                    
                });
                this.notifyMessageArchiveStatus(true);
                var activityData = {
                    activityName: "Archive Messages",
                    activityType: "manage messaging",
                    activityDescription: this.userData.displayName + " archived messages of chatroom - (" + chatroomIds.join() + ")"
                };
                this.structureService.trackActivity(activityData);
                this.removeScopeMessageOnArchive(msg.id);
                var pageCountMessage = parseInt(localStorage.getItem("messageInboxPagination")) ?  parseInt(localStorage.getItem("messageInboxPagination")) :  pageCountMessage;
                console.log("pageCountMessage deleteMaskedMessages",pageCountMessage);
                this.loadPage(pageCountMessage);
            }
        });
    }
    })
    }
    deleteChatroomMessages(message, chatroomId, messageId, isBroadcast) {
        let IsEnterClicked=false;
        if ($('div .sa-confirm-button-container button').hasClass('hidebutton')){
            $('div .sa-confirm-button-container button').removeClass('hidebutton');
        }
        let confirmClass =   "btn-warning";
        $(document).off('click', '.SwalBtn1');
        $(document).on('click', '.SwalBtn1',()=> {
            // Archive for all users
            if(IsEnterClicked){
                IsEnterClicked=false;
                  swal.close();   
                  return false;
              }
            
        $('.cat__apps__messaging__list button').attr('disabled',false);
        this.tickedNormalMessageCount=0;
        this.confirmedDeleteChatroomMessages(chatroomId, MessageArchiveType.ALL_USER, messageId);
        this.hideLoadMore = this.messageLoadBeforeArchive;                              
        });     
        $(document).off('click', '.SwalBtn2');
        // Self archive. 'Archiving for me' button click
        $(document).on('click', '.SwalBtn2',(event)=> {
            if(IsEnterClicked){
                IsEnterClicked=false;
                  swal.close();   
                  return false;
              }
        $('.cat__apps__messaging__list button').attr('disabled',false);
        this.tickedNormalMessageCount=0;
        this.confirmedDeleteChatroomMessages(chatroomId, MessageArchiveType.SELF , messageId);
        this.hideLoadMore = this.messageLoadBeforeArchive;                               
        });

        $(document).keypress((event)=> {
          if (event.keyCode == 13) {
              console.log("Enter pressed");
              IsEnterClicked=true;    
              swal.close();       
          }
        });
        
        if(message.messageCategory !== MessageCategory.BROADCAST && message.messageCategory !== MessageCategory.MASKED  && this.userData.privileges.toString().split(',').includes(PrivilegeKeys.ARCHIVE_MESSAGE_FOLL_ALL_USER)){

            this._inboxService.getStaffCountInChatroom(chatroomId).then((response) => {  
               
                let staffCount = ( response.success && response.data['accessibleUserCount'] && parseInt(response.data['accessibleUserCount']) > 0)
                    ? response.data['accessibleUserCount'] : 0;
                if(staffCount > 1){
                let staffCountTxt = '';
                if(staffCount && staffCount != 0){
                    staffCountTxt = '('+staffCount+') ';
                }
           
            let chatThreadTxt = '';
            confirmClass = "btn-warning hidebutton";
            if (!$('div .sa-confirm-button-container button').hasClass('hidebutton')){
                $('div .sa-confirm-button-container button').addClass('hidebutton');
            }
            const chatThread = $('#chat-thread-'+chatroomId+' span').text();
            if(chatThread !== '' ){
                chatThreadTxt = '('+chatThread+') ';
            }

        const btnArchiveAll =`<button tabindex="0" class="SwalBtn1 confirm btn btn-lg btn-warning">${this.toolTipService.getTranslateData('BUTTONS.ARCHIVE_ALL_USERS')}</button>` ;
        const btnArchiveForMe = `<button class="SwalBtn2 confirm btn btn-lg btn-warning" tabindex="0" >${this.toolTipService.getTranslateData('BUTTONS.ARCHIVE_FOR_ME')}</button>`;

        const titleContent =`<span style="font-size: 18px;"> ${this.toolTipService.getTranslateDataWithParam('MESSAGES.ARCHIVE_CHAT_FOR_USERS', 
        {chatThreadTxt: chatThreadTxt, staffCountTxt: staffCountTxt})}</span><br><br>${btnArchiveAll}${btnArchiveForMe}`;
        
        this.structureService.showAlertMessagePopup({
            text: titleContent,
            html: true,
            confirmButtonClass: confirmClass         
        }).then((confirm) => {
             if(IsEnterClicked){
              IsEnterClicked=false;
                swal.close();   
                return false;
            }
            if(!confirm) {
                swal.close();
                if ($('div .sa-confirm-button-container button').hasClass('hidebutton')){
                    setTimeout(function(){ $('div .sa-confirm-button-container button').removeClass('hidebutton'); }, 2000);
                }                
            }
        });

        } else {
            confirmClass  =   "btn-warning";
            if ($('div .sa-confirm-button-container button').hasClass('hidebutton')){
                $('div .sa-confirm-button-container button').removeClass('hidebutton');
            }
            this.structureService.showAlertMessagePopup({
                text: this.confirmMessageOnArchiveBySingleUser,
                confirmButtonClass: confirmClass,
            }).then((confirm) => {
                if (IsEnterClicked) {
                  IsEnterClicked = false;
                    swal.close();   
                    return false;
                }
                if(confirm) {
                    this.deleteMyChatThread(chatroomId,isBroadcast,messageId);
                }
            });
          }
        }).catch(() => {
            this.structureService.notifyMessage({messge: this.toolTipService.getTranslateData('MESSAGES.ERROR_MSG_EXCEPTION_CATCH'),
            type: CONSTANTS.notificationTypes.danger});
        });;
    } else {
        confirmClass  =   "btn-warning";
        if ($('div .sa-confirm-button-container button').hasClass('hidebutton')){
            $('div .sa-confirm-button-container button').removeClass('hidebutton');
        }
         this.structureService.showAlertMessagePopup({
         text: this.confirmMessageOnArchiveBySingleUser,
         confirmButtonClass: confirmClass,
         }).then((confirm) => {
            if (confirm) {
                this.deleteMyChatThread(chatroomId,isBroadcast,messageId);
            }
         }); 
       }
    }   
    deleteMyChatThread(chatroomId,isBroadcast,messageId){      
        $('.cat__apps__messaging__list button').attr('disabled',false);
        this.tickedNormalMessageCount=0;
        this.confirmedDeleteChatroomMessages(chatroomId, MessageArchiveType.SELF, messageId);
        this.hideLoadMore = this.messageLoadBeforeArchive;                               
    }
    enterpressalert(e) {
        var code = (e.keyCode ? e.keyCode : e.which);
        if (code == 13) { //Enter keycode
            e.preventDefault();
            this.searchInboxkeyword = $("#userSearchTxt").val();
            localStorage.setItem("searchInboxkeywordForPolling",this.searchInboxkeyword);
            if (this.searchInboxkeyword) {
                this.searchMessagesAndDocs();
            }
        }
    }
    removeScopeMessageOnArchive(messageId) {
        
        let unreadCount = 0;
        if (Array.isArray(messageId)) {
            this.inboxData = this.inboxData.filter(msg=>{
                if(messageId.indexOf(msg.chatroomId) !== -1)
                unreadCount = unreadCount + (msg.messageType=='2' ? (parseInt(msg.maskedUnreadCount) ? parseInt(msg.maskedUnreadCount) : 0) : parseInt(msg.unreadCount || msg.unread));
                return messageId.indexOf(msg.chatroomId) === -1
            });
        } else {
            this.inboxData = this.inboxData.filter(msg=>{
                if(msg.chatroomId === messageId)
                unreadCount = unreadCount + (msg.messageType=='2' ? (parseInt(msg.maskedUnreadCount) ? parseInt(msg.maskedUnreadCount) : 0) : parseInt(msg.unreadCount || msg.unread));
                return msg.chatroomId !== messageId
            });
        }
        if(unreadCount) {
            if(parseInt(unreadCount.toString()) > 0) { 
                this.updateInboxCounts('sub', unreadCount);
            }
        }
        if(this.inboxData.length) {
            this.inboxDataLength = this.inboxData.length;
        } else {
            this.inboxDataLength = 0;
        }
        this.structureService.inboxData =this.inboxData;
        this.structureService.inboxDataCurrentPage = this.currentPage;
        
        this.onInboxData =true;
        this._sharedService.onInboxData.emit({inboxData : this.inboxData, emitUpdateInboxData: false});
           
        console.log("inboxDatalength=====>",this.inboxData.length);

        this.hideLoadMore = this.messageLoadBeforeArchive;  

        console.log("hideLoadMore=====>",this.hideLoadMore);
    }
    deleteSignatureDoc(docId, messageTo) {
        var deletedBy = 'from';
        if (messageTo == this.userId) {
            deletedBy = 'to';
        }
        this._inboxService.deleteSignatureDoc(docId, deletedBy).then((data) => {
            this.removeScopeMessageOnArchive(docId);
            var activityData = {
                activityName: "Archive Message",
                activityType: "manage esign communication",
                activityDescription: this.userData.displayName + " archived signature document message - " + docId
            };
            this.structureService.trackActivity(activityData);
        }).catch((ex) => {
        });
    }
    deleteInventoryCount(docId) {
        var data = { "id": docId, "level": 0, "userId": this.userId, 'status': 1 };
        this._inboxService.deleteInventoryCount(data).then((data) => {
            this.removeScopeMessageOnArchive(docId);
            var activityData = {
                activityName: "Archive Message",
                activityType: "manage inventory communication",
                activityDescription: this.userData.displayName + " archived inventory message - " + docId
            };
            this.structureService.trackActivity(activityData);
        }).catch((ex) => {
        });
    }
    setCliniciansRoleAvailableOnClick(setParams, init, loadmore, optionShow, searchValue) {
        if (this._inboxService.checkInfusionHours(false).isWorkingHours) {
            if (this.configData.clinician_roles_on_working_hours) {
                setParams.clinicianRolesAvaiable = this.configData.clinician_roles_on_working_hours;
            } else {
                setParams.isRoleAvailable = false;
            }
        } else {
            if (this.configData.clinician_roles_beyond_working_hours) {
                setParams.clinicianRolesAvaiable = this.configData.clinician_roles_beyond_working_hours;
            } else {
                setParams.isRoleAvailable = false;
            }
        }
        /** For Patient Login User Selection Condition -- Need To Generalize */
        this.scheduledPrimaryCar = 0;
        this.defaultNurses = [];
        localStorage.setItem("defaultNurses", JSON.stringify(this.defaultNurses));
        if (this._inboxService.checkInfusionHours(false).isWorkingHours) {
            this.usersList = "";
            if (this.configData.clinician_roles_on_working_hours) {
                if(init) {
                    if(optionShow=='patient') {
                        this.chatWithLoader.patients = true;
                    } else if(optionShow=='staff') {
                        this.chatWithLoader.staffs = true;
                    }
                }
                this.loadMoremessage.users = "Loading ....";
                this._inboxService.getUsersListByRoleId(this.configData.clinician_roles_on_working_hours, 0, null,null,(init ? 0 : ((optionShow == 'staff') ? this.chatwithPageCount.staffs :this.chatwithPageCount.patients)),((optionShow == 'staff') ? undefined : false),searchValue,this.selectSiteId ).then((users:any) => {
                    this.chatWithUsersLoading = false;
                    this.loadMoreSearchValue = searchValue;
                    if(!loadmore) {
                        this.usersList = users;
                    } else {
                        this.usersList = [...this.usersList, ...users];
                    }
                    if(users.length != 20)
                    this.noMoreItemsAvailable.users = true;

                    if(optionShow=='patient') {
                        this.chatwithPageCount.patients += 1;
                    } else if(optionShow=='staff') {
                        this.chatwithPageCount.staffs += 1;
                    }
                    this.loadMoremessage.users = "Load more";
                    this.userList = this.modalFilter.transform(this.usersList, optionShow);
                    if (this.userData.group === '3') {
                        this.userList = this.userList.filter(this._inboxService.scheduleSelectionFilter);
                    }
                    var pageCount = (init ? 0 : ((optionShow == 'staff') ? this.chatwithPageCount.staffs :this.chatwithPageCount.patients))
                    if (!this.userList.length && !pageCount) {
                        let messageNoClinician = this.toolTipService.getTranslateData("MESSAGES.NO_CLINICIAN_SCHEDULED");
                        var notify = $.notify(messageNoClinician);
                        setTimeout(function () {
                            notify.update({ 'type': 'danger', 'message': '<strong>' + messageNoClinician + '</strong>' });
                        }, 1000);
                    }
                    this.clinicianLoad = true;
                    if(init) {
                        if(optionShow=='patient') {
                            this.chatWithLoader.patients = false;
                        } else if(optionShow=='staff') {
                            this.chatWithLoader.staffs = false;
                        }
                    }
                }).catch((ex) => {
                    if(init) {
                        if(optionShow=='patient') {
                            this.chatWithLoader.patients = false;
                        } else if(optionShow=='staff') {
                            this.chatWithLoader.staffs = false;
                        }
                    }
                });
            } else {
                this.clinicianLoad = true;
                if (this.configData.default_clinician_roles_available_on_working_hour) {
                    this._inboxService.getUsersListByRoleId(this.configData.default_clinician_roles_available_on_working_hour, 0, true,this.selectSiteId ).then((result) => {
                        this.chatWithUsersLoading = false;
                        this.defaultNurses = result;
                        localStorage.setItem("defaultNurses", JSON.stringify(this.defaultNurses));
                    }).catch((ex) => {
                    });
                }
            }
        } else {
            this.usersListByRoleId = "";
            if (this.configData.clinician_roles_beyond_working_hours) {
                this._inboxService.getUsersListByRoleId(this.configData.clinician_roles_beyond_working_hours, 0, true,this.selectSiteId ).then((users) => {
                    this.chatWithUsersLoading = false;
                    this.usersListByRoleId = users;
                    localStorage.setItem("usersListByRoleId", JSON.stringify(this.usersListByRoleId));
                    this.clinicianLoad = true;
                }).catch((ex) => {
                });
            } else {
                this.clinicianLoad = true;
                if (this.configData.default_clinician_roles_available) {
                    this._inboxService.getUsersListByRoleId(this.configData.default_clinician_roles_available, 0, true,this.selectSiteId ).then((result) => {
                        this.defaultNurses = result;
                        localStorage.setItem("defaultNurses", JSON.stringify(this.defaultNurses));
                    }).catch((ex) => {
                    });
                }
            }
        }
        return setParams;
    }

    getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,init, loadMore, optionShow, searchValue ) {
        if(init) {
            if(optionShow=='patient') {
                this.chatWithLoader.patients = true;
            } else if(optionShow=='staff') {
                this.chatWithLoader.staffs = true;
            }
        }
        this.loadMoremessage.users = "Loading ....";
        this._inboxService.getUsersListByRoleId((clinicianRolesAvaiable ? clinicianRolesAvaiable : 3), 0, null,null,(init ? 0 : ((optionShow == 'staff') ? this.chatwithPageCount.staffs :this.chatwithPageCount.patients)),((optionShow == 'staff' && !clinicianRolesAvaiable) ? true : ((optionShow == 'staff' && clinicianRolesAvaiable) ? undefined : false)),searchValue,'','','','',this.selectSiteId ).then((data:any) => {
            this.chatWithUsersLoading = false;
            this.loadMoreSearchValue = searchValue;
            if(loadMore) {
                this.clinicalUserDetails = [...this.clinicalUserDetails, ...data];
            } else {
                this.clinicalUserDetails=data;
            }
            if(data.length != 20)
            this.noMoreItemsAvailable.users = true;

            if(optionShow=='patient') {
                this.chatwithPageCount.patients += 1;
            } else if(optionShow=='staff') {
                this.chatwithPageCount.staffs += 1;
            } 

            for (var _i = 0; _i < this.clinicalUserDetails.length; _i++) {
                this.clinicalUserDetails[_i].isScheduled = 1;
                if (this._inboxService.scheduleSelectionFilter(this.clinicalUserDetails[_i])) {
                    this.clinicalUserDetails[_i].isScheduled = 1;
                } else {
                    this.clinicalUserDetails[_i].isScheduled = 0;
                }
            }

            this.loadMoremessage.users = "Load more";
            this.userList = this.modalFilter.transform(this.clinicalUserDetails, optionShow);
            if (this.userData.group === '3') {
                this.userList = this.userList.filter(this._inboxService.scheduleSelectionFilter);
            }
            var pageCount = (init ? 0 : ((optionShow.toLowerCase() === UserRoles.staff.toLowerCase()) ? this.chatwithPageCount.staffs :this.chatwithPageCount.patients))
            if (!this.userList.length && !pageCount && +this.userData.group === UserGroup.PATIENT) {
                let messageNoClinician = this.toolTipService.getTranslateData('MESSAGES.NO_CLINICIAN_SCHEDULED');
                var notify = $.notify(messageNoClinician);
                setTimeout(function () {
                    notify.update({ 'type': 'danger', 'message': '<strong>' + messageNoClinician + '</strong>' });
                }, 1000);
            }
            if(init) {
                if(optionShow=='patient') {
                    this.chatWithLoader.patients = false;
                } else if(optionShow=='staff') {
                    this.chatWithLoader.staffs = false;
                }
            }

        }).catch((ex) => {
            if(init) {
                if(optionShow=='patient') {
                    this.chatWithLoader.patients = false;
                } else if(optionShow=='staff') {
                    this.chatWithLoader.staffs = false;
                }
            }
        });
    }
    initialiseStaffOrPatientListOnClick(optionShow) {
        $('.reRouteBehaviour').attr('disabled','disabled');
        var isRoleAvailable = true;
        var clinicianRolesAvaiable = null;
        var setCliniciansRoleAvailableResponse;
        if (this.userData.group == 3) {
            this.setCliniciansRoleAvailableOnClick({isRoleAvailable:isRoleAvailable,clinicianRolesAvaiable:clinicianRolesAvaiable},true,false,optionShow,undefined).then( (res)=>{
                setCliniciansRoleAvailableResponse = res;
                isRoleAvailable = setCliniciansRoleAvailableResponse.isRoleAvailable;
                clinicianRolesAvaiable = setCliniciansRoleAvailableResponse.clinicianRolesAvaiable;
                if(isRoleAvailable) {
                    this.getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,true,false, optionShow,undefined)
                } else {
                    this.chatWithUsersLoading = false;
                }
            })
            /** ***********************End Section ****************** */

        } else {
            if (isRoleAvailable) {
                if(isRoleAvailable) {
                    this.getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,true,false, optionShow, undefined)
                } else {
                    this.chatWithUsersLoading = false;
                }
            }
        }
    }
    searchOnEnter(event,optionShow) {
        if(optionShow == 'groups') {
        } else{
            if(event.keyCode == 13) {
                if(this.srch.nativeElement.value.trim()){
                    this.searchReroute(optionShow);
                } else {
                    this.reset(optionShow)
                }
            }
        }
   }
   searchReroute(optionShow) {
        if(optionShow == 'groups') {
            
        } else{
            this.chatwithPageCount = {
                staffs: 0,
                patients: 0
            }
            this.noMoreItemsAvailable = {
                users: false
            };
            this.userListChatwith = [];
            this.clinicalUserDetails = [];
            this.usersList = [];
            this.userList = [];
            this.loadMoreUsers(optionShow, this.srch.nativeElement.value.trim())
        } 
    }
    reset(optionShow) {
        this.srch.nativeElement.value = "";
        if(optionShow == 'groups') {
            
        } else {
            this.loadMoreSearchValue = undefined;
            this.chatwithPageCount = {
                staffs: 0,
                patients: 0
            }
            this.noMoreItemsAvailable = {
                users: false
            };
            this.userListChatwith = [];
            this.clinicalUserDetails = [];
            this.usersList = [];
            this.userList = [];
            this.loadMoreUsers(optionShow)
        }
        
    }
    resetSearch()
    {
        this.noSearchData = false;
        console.log("this.noSearchData",this.noSearchData);
        this.selectedAll = false;
        this.currentPage = 1;
        localStorage.setItem("messageInboxPagination",JSON.stringify(this.currentPage));
        if (this.searchInboxkeyword) {
            this.resetSearchh = true;
        } 
        this.searchInboxkeyword = '';
        localStorage.setItem("searchInboxkeywordForPolling",this.searchInboxkeyword);
        $('#userSearchTxt').val("");
        this.pageCountMessage = 1;
        this.doLoadmore =false;
        this.hideLoadMore = false;
        $('.inbox-flag-filter-dropdown select').select2('val','0');
        this.activate(false,this.qLimit);
    }
    loadMoreUsers(optionShow, searchKeyword=undefined) {
        if(!this.loadMoreSearchValue) {
            if(this.srch.nativeElement.value.trim()){
                this.loadMoreSearchValue = undefined;
                this.callFromInitialCountChatWithUserlist = true;
                this.chatwithPageCount = {
                    staffs: 0,
                    patients: 0
                }
                this.noMoreItemsAvailable = {
                    users: false
                };
                this.userListChatwith = [];
                this.clinicalUserDetails = [];
                this.usersList = [];
                this.userList = [];
                searchKeyword = this.srch.nativeElement.value.trim();
            }
        } else if(this.loadMoreSearchValue && !this.srch.nativeElement.value.trim()) {
            this.reset(optionShow)
            return false;
        } else if(this.loadMoreSearchValue == this.srch.nativeElement.value.trim()) {
        } else {
            this.loadMoreSearchValue = undefined;
            this.callFromInitialCountChatWithUserlist = true;
            this.chatwithPageCount = {
                staffs: 0,
                patients: 0
            }
            this.noMoreItemsAvailable = {
                users: false
            };
            this.userListChatwith = [];
            this.clinicalUserDetails = [];
            this.usersList = [];
            this.userList = [];
            searchKeyword = this.srch.nativeElement.value.trim();
        }
        var isRoleAvailable = true;
        var clinicianRolesAvaiable = null;
        var setCliniciansRoleAvailableResponse;
        if (this.userData.group == 3) {
            this.setCliniciansRoleAvailableOnClick({isRoleAvailable:isRoleAvailable,clinicianRolesAvaiable:clinicianRolesAvaiable},(this.userList.length ? false: true),(this.userList.length ? true: false),optionShow,searchKeyword).then( (res)=>{
                setCliniciansRoleAvailableResponse = res;
                isRoleAvailable = setCliniciansRoleAvailableResponse.isRoleAvailable;
                clinicianRolesAvaiable = setCliniciansRoleAvailableResponse.clinicianRolesAvaiable;
                if(isRoleAvailable) {
                    this.getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,(this.userList.length ? false: true),(this.userList.length ? true: false), optionShow,searchKeyword)
                } else {
                    this.chatWithUsersLoading = false;
                }
            })                
            /** ***********************End Section ****************** */
        } else {
            if(isRoleAvailable) {
                this.getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,(this.userList.length ? false: true),(this.userList.length ? true: false), optionShow,searchKeyword)
            } else {
                this.chatWithUsersLoading = false;
            }
        }
    }
    selectModal(category, message,chatroomId = 0) {
        if(+this.userData.group !== UserGroup.PATIENT) {
            this.chatWith = false;
            if (category === 'Chat With') {
                this.hasChatWithGroup = this.userData.group != 3;
                this.chatWith = true;
            } else if (category === 'Reroute to') {
                this.activeMessage = message;
                this.isFilter = true;
                this.getPatientSiteIdForMsgForward(message);
            }  else {
                this.chatWith = true;
            }
            this.modalHead = category;
            this.selectedRow = -1;
            $('#exampleModalRerouteInbox').modal('show');
            if ((category === 'Reroute to'&& this.showFilter) || category === 'Invite') {
                this.initialiseStaffOrPatientListOnClick(this.optionShow);
            }
        }
    }
    closeModal() {
        this.isFilter = false;
    }
    selectModal_old(category, message) {
        if (!this.clinicalUserDetails.length) {
            let messageNoClinician = this.toolTipService.getTranslateData('MESSAGES.NO_CLINICIAN_SCHEDULED');
            var notify = $.notify(messageNoClinician);
            setTimeout(function () {
                notify.update({ 'type': 'danger', 'message': '<strong>' + messageNoClinician + '</strong>' });
            }, 1000);
        } else {
            this.chatWith = false;
            if (category === 'Chat With') {
                this.hasChatWithGroup = this.userData.group != 3;
                this.chatWith = true;
            } else if (category === 'Reroute to') {
                this.activeMessage = message;
            } else {
                this.chatWith = true;
            }
            this.modalHead = category;
            $('#exampleModalRerouteInbox').modal('show')
            if (category === 'Reroute to' || category === 'Invite') {
                this.userList = this.modalFilter.transform(this.clinicalUserDetails, 'staff');
                if (this.userData.group === '3') {
                    this.userList = this.userList.filter(this._inboxService.scheduleSelectionFilter);
                }
            }          
        }
    }
    viewInventory(e, message) {
        message.unread = 0;
        message.unreadCount = 0;
        const activityData = {
            activityName: "Select Inventory",
            activityType: "inventory",
            activityDescription: this.userData.displayName + " has viewed inventory report(" + message.message + ") of " + message.patientName
        };
        this.structureService.trackActivity(activityData);
        let routingUrl = '/supplies/inventory-submissions/submission/' + message.userid + '/' + message.inventoryId + '/' + message.id;
        this.router.navigate([routingUrl]);
    }
    selectAll() {
        if ($("#selectall").prop('checked') == true) {
            this.selectedAll = true;
            this.selectedInboxItems = true;
            $('.cat__apps__messaging__list button').attr('disabled',true);
            this.buttonTextChange(false);
        } else {
            this.selectedAll = false;
            this.selectedInboxItems = false;
            this.tickedNormalMessageCount = 0;
            $('.cat__apps__messaging__list button').attr('disabled',false);
            this.buttonTextChange(false);
        }


        for (var i = 0; i < this.inboxData.length; i++) {
            this.inboxData[i].markAsRead = this.selectedAll;
            if (this.inboxData[i].markAsRead) {
                this.tickedNormalMessageCount = this.tickedNormalMessageCount + 1;
            } else {
                if(this.tickedNormalMessageCount > 0)
                this.tickedNormalMessageCount = this.tickedNormalMessageCount - 1;
            }
        }
    }
    buttonTextChange(condition) {
        if (condition) {
            this.readAsButtonText = this.toolTipService.getTranslateData("LABELS.MARK_AS_READ");
        } else {
            this.readAsButtonText = this.toolTipService.getTranslateData("LABELS.MARK_ALL_AS_READ");
        }
    }
    checkIfAllSelected(item) {
        var index = this.inboxData.indexOf(item);
        if (index > -1) {
            if (item.markAsRead) {
                this.inboxData[index].markAsRead = false;
                item.markAsRead = false;
                this.tickedNormalMessageCount = this.tickedNormalMessageCount - 1;
            } else {
                this.inboxData[index].markAsRead = true;
                item.markAsRead = true;
                this.tickedNormalMessageCount = this.tickedNormalMessageCount + 1;
            }
        }
        this.selectedAll = this.inboxData.every(function (item: any) {
            return item.markAsRead == true;
        })
        this.selectedInboxItems = this.inboxData.some(function (item: any) {
            return item.markAsRead == true;
        });
        if (this.selectedInboxItems) {
            if(this.selectedAll) {
                this.buttonTextChange(false);
            } else {
                this.buttonTextChange(true);
            }
        } else {
            this.buttonTextChange(false);
        }

        if(this.tickedNormalMessageCount > 1)
        {
            $('.cat__apps__messaging__list input:checkbox').each(function () {
                var dataId = ($(this).attr('id') ? $(this).attr('id') : "");
                if(dataId != "")
                {
                    if($("#"+dataId).prop('checked'))
                    {
                        $("#button-"+dataId).prop("disabled",true);
                    }
                    else
                    {
                        $("#button-"+dataId).prop("disabled",false);
                    }
                }
           });
        }
        else
        {
            $('.cat__apps__messaging__list').find('button').prop('disabled', true).map(function()
            {
                 $(this).prop("disabled",false);
            });
        }
    }

    readAllSelectedItem(condition) {
        this.tickedNormalMessageCount = 0;
        this.messageReadData = {};
        this.chatroomIds = [];
        this.inventoryIds = [];
        this.signatureDocIds = [];
        this.formSenders = [];
        this.formRecipiers = [];
        this.notifyUsers = [];
        let unreadCountList = true;
        let markRead = true;
        if (!condition) {
            markRead = false;
        }
        this.inboxData.map((messages) => {
            messages.markAsRead = !!messages.markAsRead;
            if (messages.markAsRead == markRead && messages.messageUnreadCount !== 0) {
                unreadCountList = false;
                this.chatroomIds.push(messages.chatroomId);
            }
        });
        if (unreadCountList) {
            let massageTitle = this.toolTipService.getTranslateData("WARNING.NO_UNREAD_MESSAGE_SELECT");
            if (!condition) {
                massageTitle = this.toolTipService.getTranslateData("WARNING.NO_UNREAD_MESSAGE_CURRENT_PAGE");
            }
            this.structureService.notifyMessage({
                messge: massageTitle,
                delay: 1000,
                type: 'warning'
            });
        } else {
            this.messageReadData.chatroomIds = this.chatroomIds;
            this.saveMessageStatusToRead(this.messageReadData);
        }
    }
    saveMessageStatusToRead(messageReadData) {
        let activityLog = 'Marked ';
        if (messageReadData.chatroomIds.length) {
            activityLog += `chatroom messages (${messageReadData.chatroomIds.join(', ')}) `;
        }
        if (messageReadData.readAll) {
            activityLog += 'All';
        }
        NProgress.start();

        let unreadCount = this.inboxUnreadMessageCountFiltered;
    
        this._inboxService.markallMessageAsRead(messageReadData).then((result) => {
            this.apiResult = result;
            if (this.unReadMsg) {
                this.inboxData = this.inboxData.filter((msg) => !this.apiResult.chatroomIds.includes(msg.chatroomId));
            }
    
            this.inboxData.map((message) => {
                if ((messageReadData.readAll && this.apiResult.counts.messages === '0' && this.apiResult.status) ||
                    this.apiResult.chatroomIds.includes(message.chatroomId)) {
                                        const initialUnreadCount = +message.messageType === 2 
                        ? (parseInt(message.maskedUnreadCount) || 0) 
                        : (parseInt(message.unreadCount) || parseInt(message.unread) || 0);
                    message.unreadCount = 0;
                    message.hasUnreadMessages = false;
                    message.unread = 0;
                    message.maskedUnreadCount = 0;
                    message.messageUnreadCount = 0;
                    message.markAsRead = false;
                    unreadCount -= initialUnreadCount;
                }
            });    
            unreadCount = Math.max(0, unreadCount);    
            this.structureService.inboxUnreadMessageCount = unreadCount;
            this.inboxUnreadMessageCountFiltered = unreadCount;
            this.resetInboxMarkAsRead(this.inboxData, false, true);
            this.getInboxCounts('messages');
            $('#selectall').prop('checked', false);
            this.selectAll();
            this.resetObjectValues();
            NProgress.done();
            activityLog += ' as read';
            const activityData = {
                activityName: "Read Message",
                activityType: "messaging",
                activityDescription: activityLog
            };
            this.structureService.trackActivity(activityData);
        });
    }
    resetObjectValues() {
        this.messageReadData = {};
        this.chatroomIds = [];
        this.inventoryIds = [];
        this.signatureDocIds = [];
        this.formSenders = [];
        this.formRecipiers = [];
        this.notifyUsers = [];
    }
    archiveInbox(condition) {
        this.disableMessages = false;
        let IsEnterClicked = false;
        $(document).keypress((event)=> {
          if (+event.keyCode === 13) {
              IsEnterClicked = true;           
          }
        });
        this.structureService.showAlertMessagePopup({
            text: this.confirmMessageOnArchiveBySingleUser,
        }).then((confirm) => {
            if(IsEnterClicked){
              IsEnterClicked=false;
                swal.close();   
                return false;
            }
            if(confirm) {
            this.tickedNormalMessageCount = 0;
            let chatroomIdsArchive = [];
            let maskedMessagesArchive = [];
            let maskedMessagesArchivedRoomIds = [];
            let inventoryIdsArchive = [];
            let signatureDocIdsArchive = [];
            let allIds = [];
            let message:any = [];
            let unreadCountList = true;
            this.messageReadData.userId = this.userData.userId;
            this.messageReadData.displayName = this.userData.displayName;
            let markRead = true;
            if (!condition) {
                markRead = false;
            }
            this.inboxData.map((messages) => {
                messages.markAsRead = !!messages.markAsRead;
                if (messages.markAsRead === markRead) {
                    if (messages) {
                        unreadCountList = false;
                        if(messages.messageType !== 2) {
                            chatroomIdsArchive.push(messages.chatroomId);
                        } else {
                            let maskedArchiveData = {
                                baseId: messages.baseChatroomId || 0,
                                initiatedBaseId: messages.initiatedBaseId,
                                userId: this.userId,
                                createdby: messages.createdby,
                                chatroomid: messages.chatroomId,
                                subList: '',
                                invitedStatus:messages.invited_status,
                                displayname: this.userData.displayName,
                                hiddenChatRoomId: messages.hiddenChatRoomId
                            }
                            if( messages.subList && messages.subList.length) {
                                let result = messages.subList.map((sub) => {
                                    return sub.chatroomId;
                                }).join(',');
                                maskedArchiveData.subList = result;
                            }
                            maskedMessagesArchive.push(maskedArchiveData);
                            if( maskedArchiveData.invitedStatus == '0') {
                                maskedMessagesArchivedRoomIds.push(maskedArchiveData.chatroomid);
                                if(maskedArchiveData.subList != '') {
                                    maskedMessagesArchivedRoomIds = $.merge(maskedMessagesArchivedRoomIds,maskedArchiveData.subList.split(','));
                                }	
                            } else {
                                maskedMessagesArchivedRoomIds.push(maskedArchiveData.chatroomid);
                                if(maskedArchiveData.userId == maskedArchiveData.createdby) {
                                    maskedMessagesArchivedRoomIds.push(maskedArchiveData.initiatedBaseId);
                                }
                            }
                            if(maskedArchiveData.hiddenChatRoomId) {
                                maskedMessagesArchivedRoomIds.push(maskedArchiveData.hiddenChatRoomId)
                            }
                        }
                    }
                    allIds.push(messages.chatroomId);
                }
            });

            if (unreadCountList) {
                this.structureService.notifyMessage({
                    messge: 'Select at least one message',
                    delay: 1000,
                    type: 'warning'
                });
            } else {
                this.messageReadData.chatroomIdsArchive = chatroomIdsArchive;
                this.messageReadData.maskedMessagesArchive = maskedMessagesArchive;
                this.messageReadData.maskedMessagesArchivedRoomIds = maskedMessagesArchivedRoomIds;
                this.messageReadData.inventoryIdsArchive = inventoryIdsArchive;
                this.messageReadData.signatureDocIdsArchive = signatureDocIdsArchive;
                this.messageReadData.allIds = allIds;
                this.messageReadData.message = message;
                this.archiveInboxItems(this.messageReadData);
            }
        }
        })
    };
    archiveInboxItems(messageReadData) {
        console.log("archiveInboxItems===>",messageReadData);
        var activityLog = 'Archived ';
        if (messageReadData.chatroomIdsArchive.length || messageReadData.maskedMessagesArchivedRoomIds.length) {
            messageReadData.chatroomIdsArchive = $.merge(messageReadData.chatroomIdsArchive, messageReadData.maskedMessagesArchivedRoomIds);
            activityLog += 'chatroom messages (' + messageReadData.chatroomIdsArchive.join(", ") + ') ';
        }
        if (messageReadData.inventoryIdsArchive.length) {
            activityLog += 'Inventories (' + messageReadData.inventoryIdsArchive.join(", ") + ') ';
        }
        if (messageReadData.signatureDocIdsArchive.length) {
            activityLog += 'Signature Document (' + messageReadData.signatureDocIdsArchive.join(", ") + ') ';
        }
        NProgress.start();
        this.pollingArchive(messageReadData.chatroomIdsArchive);
        this.removeScopeMessageOnArchive(messageReadData.allIds);
        var self = this;

        let notifyUsers = messageReadData.message;
        var notifyUsersPolling = [];
        notifyUsers.forEach(element => {
            if(element.notify_users) {
                let notifyUsersNew = element.notify_users.split(",");
                notifyUsersNew.forEach(user => {
                    notifyUsersPolling.push({"userid":user});
                    if(element.fromId!=this.userData.userId){
                        notifyUsersPolling.push({"userid":element.fromId});
                    }
                });
            }
        });
        if((messageReadData.message).length){
            messageReadData.message[0].inboxFormArchiveType = this.configData.inbox_form_archive_remove_self;
        }
        if(messageReadData.chatroomIdsArchive.length) {
            messageReadData.chatroomIdsArchive = messageReadData.chatroomIdsArchive.filter(id=>id!=null).filter(id=>id!=undefined)
        }
        this._inboxService.archiveInboxItems(messageReadData).then((result: any) => {
            if (result.status == 1) {
                self.totalCount = self.totalCount - messageReadData.chatroomIdsArchive.length;
                self.structureService.inboxTotalMessageCount = self.totalCount;
                localStorage.setItem("inboxTotalMessageCount",JSON.stringify(self.totalCount));
                console.log("compare length of archive all , selected message length - ",messageReadData.chatroomIdsArchive.length,"paginated data length - ",self.inboxPageMessageCount);
                if(messageReadData.chatroomIdsArchive.length == self.inboxPageMessageCount && self.currentPage > 1){
                    self.currentPage = 1;
                    localStorage.setItem("messageInboxPagination",JSON.stringify(self.currentPage));
                }
                messageReadData.allIds = "";
                $('#selectall').prop('checked', false);
                self.selectAll();
                self.resetObjectValues();
                NProgress.done();
                var pageCountMessage = parseInt(localStorage.getItem("messageInboxPagination")) ?  parseInt(localStorage.getItem("messageInboxPagination")) :  pageCountMessage;
                console.log("pageCountMessage archiveInboxItems",pageCountMessage);
                self.loadPage(pageCountMessage);
                var activityData = {
                    activityName: "Archive Messages",
                    activityType: "manage messaging",
                    activityDescription: activityLog
                };
                // success message for archive multiple chat thread
                this.notifyMessageArchiveStatus(true);
                self.structureService.trackActivity(activityData);
            }
            if(notifyUsersPolling.length && self.userData.group!=3){
                //self.structureService.socket.emit("inventoryCountOrSignatureDocMessages", notifyUsersPolling, 'message', 1); //polling for forms//Need to commnd after finish form inbox
                self.structureService.socket.emit("sendFormsToRecipients", notifyUsersPolling);
            }
            self.hideLoadMore =false;
            console.log("hideLoadMore=====>",self.hideLoadMore);
        });
    }
    pollingArchive(chatroomIds) {
        var pollingMessage = (this.archievedAdminMessage).replace(/{{displayName}}/g, this.userData.displayName);
        var j = 0;
        while (j < chatroomIds.length) {
            this.structureService.socket.emit("userMessagetoServer", { data: pollingMessage, notification: true, chatRoomId: chatroomIds[j], insert: false, tenantId: this.userData.tenantId, tenantName: this.userData.tenantName });
            j++;
        }
    }

    viewStructuredForm(event, message) {
        var a = message.unread;
        var b = message.unreadCount;
        if (message.unread !=0 || message.unreadCount!=0) {
            this._sharedService.readInboxData.emit(message);
        }
        message.unreadCount = 0;
        message.hasUnreadMessages = false;
        message.unread = 0;
        var data:any = {};
        data = message;
        data.loginUserId = this.userData.userId;
        var savaAsDraftStaff = 0;
        var savaAsDraftPatient = 0;
        if (this.userDataConfig.enable_form_save_draft_patient == 1 && message.enableSaveDraftPatient == 1) {
        savaAsDraftPatient = 1;
        }
        if (this.userDataConfig.enable_form_save_draft_staff == 1 && message.enableSaveDraftStaff == 1) {
        savaAsDraftStaff = 1;
        }
        message.savaAsDraftStaff = savaAsDraftStaff;
        message.savaAsDraftPatient = savaAsDraftPatient;
        this.structureService.setCookie('formData',encodeURIComponent(JSON.stringify(message)), 1);
        localStorage.setItem('formData', encodeURIComponent(JSON.stringify(message)));
        this._formsService.checkIsFormFilled(data).then(function(result: any){
            this._sharedService.updateUserInboxCounts.emit({getCount:true, type: 'forms'});
        });
        if(message.form_submission_id && message.form_submission_id!=0){
            this.router.navigate(['/forms/list/view']);
        }else{
            this.router.navigate(['/forms/view-form']);
        }
    }
    deleteFormEntry(message){
        var data:any = {};
        data = message;
       data.inboxFormArchiveType = this.configData.inbox_form_archive_remove_self;
        let notifyUsers = message.notify_users;
        notifyUsers = notifyUsers.split(",");
        var notifyUsersPolling = [];
        notifyUsers.forEach(element => {
            notifyUsersPolling.push({"userid":element});
        });
        if(message.fromId!=this.userData.userId){
            notifyUsersPolling.push({"userid":message.fromId});
        }
        if(this.userData.userId != message.recipient_id){
        notifyUsersPolling.push({"userid":message.recipient_id});
        }
        console.log(notifyUsersPolling);
        var self = this;
        this._formsService.deleteSentForm(data).then((result)=> {
            let resultdata = result;
            let notifyResult = result;
            if(self.userData.group!=3 && notifyUsersPolling.length){
                self.structureService.socket.emit("inventoryCountOrSignatureDocMessages", notifyUsersPolling, 'form'); //polling for forms//Need to commnd after finish form inbox
                self.structureService.socket.emit("sendFormsToRecipients", notifyUsersPolling);
                var description ="";
                var activityDataa: any = {};
                if(resultdata['status'] ==1){                   
                    if(resultdata['defaultfilingCenter']==false){
                        description ="";      
                    }else {
                        description ="and Moved as "+resultdata['filename']+" to default filing center	 - " + resultdata['defaultfilingCenter'];
                    }
                    if(notifyResult['defaultfilingCenter'] && this.userDataConfig.enable_progress_note_integration == 1 && notifyResult['progressNoteIntegration'] == true)
                    {
                      console.log("in notifyResult");
                      if (notifyResult['defaultfilingCenter'] !=false && notifyResult['identity_value_Patient'] !='' || notifyResult['identity_value_Patient'] != ""  || notifyResult['identity_value_Patient'] != null && notifyResult['patientAssociation'] == true 
                      && notifyResult['CPRUSERNO'] !='' || notifyResult['CPRUSERNO'] != null 
                      && notifyResult['CPRUSERNAME'] != null || notifyResult['CPRUSERNAME'] !='') {
                      activityDataa.activityName = "Form Copied to Filing Center as JSON";
                      activityDataa.activityType = "structured forms";
                      activityDataa.activityDescription = this.userData.displayName + " archived form " + result['form_name'] + " (formId " + result['form_id'] + ",sendId " + result['sent_id'] + ") successfully and copied the JSON file to filing center named " + notifyResult['defaultfilingCenter'];
                      this.structureService.trackActivity(activityDataa);
            
                      }
                      if ( notifyResult['defaultfilingCenter'] !=false && (notifyResult['identity_value_Patient']=='' || notifyResult['identity_value_Patient'] == "" || notifyResult['identity_value_Patient'] == null || notifyResult['patientAssociation'] == false 
                      || notifyResult['CPRUSERNO'] =='' || notifyResult['CPRUSERNO'] == null 
                      || notifyResult['CPRUSERNAME'] =='' || notifyResult['CPRUSERNAME'] == null)) {
                        activityDataa.activityName = "Failed Form Copy to Filing Center as JSON";
                        activityDataa.activityType = "structured forms";
                        var messageData= '';
                        var messageData1= '';
                        console.log("in defaultfilingCenter",notifyResult);
                        if(notifyResult['patientAssociation'] == false && notifyResult['staffFacing'] == "true")
                        {
                          messageData1 = "there is no patient associated with this form"; 
                        }
                         if((notifyResult['identity_value_Patient'] == '' || notifyResult['identity_value_Patient'] == null || notifyResult['identity_value_Patient'] == "") && notifyResult['patientAssociation'] == true)
                        {
                          messageData += ", MRN";  
                          if(notifyResult['patient_name'] && notifyResult['patient_name']!= "")
                          {
                              messageData += " of "+notifyResult['patient_name'];  
                          }
                          console.log("in MRN",messageData);
                        }
                        let f1=0;
                        let f2=0;
                         if(notifyResult['CPRUSERNO'] =='' || notifyResult['CPRUSERNO'] == null)
                        {
                            f1 =1;
                          messageData += ", USERNO";   
                        }
                         if(notifyResult['CPRUSERNAME'] =='' || notifyResult['CPRUSERNAME'] == null)
                        {
                            f2 =1;
                          messageData += ", USERNAME"; 
                        }
                        if((f1 == 1 || f2 == 1) && notifyResult['staff_name'] && notifyResult['staff_name'] != "")
                        {
                        messageData += " of "+notifyResult['staff_name']; 
                        }
                         if(notifyResult['patientAssociation']== false && notifyResult['staffFacing'] == "false")
                        {
                          messageData += "";   
                        }
                        var removeComa = messageData.charAt(0);
                        if(removeComa == ',')
                        {
                          messageData = messageData.substring(1);
                        }
                        console.log("messageData",messageData);
                        if(messageData1 && messageData)
                        {
                        var finalMessage = 'Generating Progress Note  for Form '+notifyResult['form_name']+' failed due to '+messageData1 +' and missing  ('+ messageData+')';
                        }
                        else if(messageData1)
                        {
                        var finalMessage = 'Generating Progress Note for Form '+notifyResult['form_name']+' failed due to '+messageData1;
                        }
                        else if(messageData)
                        {
                        var finalMessage = 'Generating Progress Note for Form '+notifyResult['form_name']+' failed due to missing ('+messageData+')';
                        }
                        else
                        {
                          var finalMessage = '';
                        }
                        console.log("messageData",messageData);
                        if(finalMessage)
                        {
                          console.log("finalMessage");
                        activityDataa.activityDescription = this.userData.displayName + " archived form " + result['form_name'] + " (formId " + result['form_id'] + ",sendId " + result['sent_id'] + ") failed to copy the JSON file to filing center named " + notifyResult['defaultfilingCenter'] + "because" + messageData;
                        this.structureService.trackActivity(activityDataa);
                        this.structureService.notifyMessage({
                          messge: finalMessage,
                          delay: 6000,
                          type: 'warning'
                        });
                      }
                    }
                    }
                } else {
                 description ="error in move to default filing center -"+resultdata['description'];    
                }      
                var activityData = {
                    activityName: "Archive Forms",
                    activityType: "manage forms",
                    activityDescription: this.userData.displayName + "("+this.userData.userId+") archived from inbox the form "+data.formName+"("+data.formId +") "+description
                };
                this.structureService.trackActivity(activityData);  
            }
        });
        this.removeScopeMessageOnArchive(message.id);
    }
    onthreadFlagChange(message) {
        this.expandFlagOPtions(message);
        var activityData = {
            activityName: "",
            activityType: "messaging",
            activityDescription: ""
        };
        var addMessageFlag =0;
        var description = "";
        var flagDetails = "";
        var prev_thread_flag = message.prev_thread_flag;
        var prev_flag_data_id = message.thread_flag_data_id;
        let pageCount = parseInt(localStorage.getItem("messageInboxPagination"));
        this.structureService.inboxData = this.inboxData;
        this.structureService.inboxDataCurrentPage = this.currentPage;
        if(pageCount == 1) {
            this.inboxDataFirstPage =  this.inboxData;
            this.structureService.inboxDataFirstPage = JSON.parse(JSON.stringify(this.inboxDataFirstPage));
        }
        this.structureService.updateChatThreadOrMsgFlag('thread', message).then((data)=>{
            if(data && data['status'] && data['status'] == 1 && data['flag_data_id']) {
                message.thread_flag_data_id = data['flag_data_id'];
                addMessageFlag = 1;                
            }
            if(data && data['status'] && data['status'] == 1) {
                if(this.flagFilterValue && (message.thread_flag!=this.flagFilterValue && message.msg_flag!=this.flagFilterValue)) {
                    this.inboxData = this.inboxData.filter(msg=>{
                        if(msg.id == message.id) {
                            if(this.totalCount) {
                                this.totalCount = this.totalCount-1;
                                this.structureService.inboxTotalMessageCount = this.totalCount;
                                localStorage.setItem("inboxTotalMessageCount",JSON.stringify(this.totalCount));
                            }
                        }
                        return msg.id != message.id
                    });
                }
                message.prev_thread_flag = message.thread_flag;
                if(message.thread_flag == 0) {
                    this.structureService.notifyMessage({
                        messge: 'Chatroom Flag Cleared Successfully',
                        type: 'success'
                    }); 
                } else {
                    this.structureService.notifyMessage({
                        messge: 'Chatroom Flagged Successfully',
                        type: 'success'
                    });
                }
                if(addMessageFlag) {
                    activityData.activityName = "Add Chatroom Flag";
                    description = "Add Chatroom flag done by ";
                } else {
                    if(message.thread_flag == 0) {
                        activityData.activityName = "Delete Chatroom Flag";
                        description = "Delete Chatroom flag done by ";
                    } else {
                        activityData.activityName = "Update Chatroom Flag";
                        description = "Update Chatroom flag done by ";
                    }                        
                }
                flagDetails = "The flag choosed is "+message.thread_flag+", previous flag is "+ prev_thread_flag +", thread_flag_data_id is "+message.thread_flag_data_id+" and prev_flag_data_id is "+prev_flag_data_id+".["+JSON.stringify(data)+"]";    
                activityData.activityDescription = `${description}${this.userData.displayName}(${this.userData.userId}) success for chatroom (${message.chatroomId}).${flagDetails}`;      

            } else {
                var flagchoosed = message.thread_flag;
                message.thread_flag = message.prev_thread_flag;
                this.structureService.notifyMessage({
                    messge: 'Chatroom Flagging Failed',
                    type: 'danger'
                });
                if(!message.thread_flag_data_id) {
                    activityData.activityName = "Failure Add Message Flag";
                    description = "Add message flag done by ";
                    flagDetails = "The flag choosed is "+flagchoosed+", thread_flag_data_id is "+message.thread_flag_data_id+" and prev_flag_data_id is "+prev_flag_data_id+".["+JSON.stringify(data)+"]";
                } else {
                    if(flagchoosed == 0) {
                        activityData.activityName = "Failure Delete Message Flag";
                        description = "Delete message flag done by ";
                    } else {
                        activityData.activityName = "Failure Update Message Flag";
                        description = "Update message flag done by ";
                    }
                    flagDetails = "The flag choosed is "+flagchoosed+", previous flag is "+ prev_thread_flag +", thread_flag_data_id is "+message.thread_flag_data_id+" and prev_flag_data_id is "+prev_flag_data_id+".["+JSON.stringify(data)+"]";    
                }
                
                activityData.activityDescription = `${description}${this.userData.displayName}(${this.userData.userId}) failed for chatroom (${message.chatroomId}).${flagDetails}`;
                
            }
            this.structureService.trackActivity(activityData); 


        }).catch((ex) => {
            var flagchoosed = message.thread_flag;
            message.thread_flag = message.prev_thread_flag
            if(!message.thread_flag_data_id) {
                activityData.activityName = "Failure Add Message Flag";
                description = "Add message flag done by ";
                flagDetails = "The flag choosed is "+flagchoosed+" and thread_flag_data_id is "+message.thread_flag_data_id+".["+JSON.stringify(ex)+"]";
            } else {
                if(flagchoosed == 0) {
                    activityData.activityName = "Failure Delete Message Flag";
                    description = "Delete message flag done by ";
                } else {
                    activityData.activityName = "Failure Update Message Flag";
                    description = "Update message flag done by ";
                }
                flagDetails = "The flag choosed is "+flagchoosed+", previous flag is "+ prev_thread_flag +" and thread_flag_data_id is "+message.thread_flag_data_id+".["+JSON.stringify(ex)+"]";    
            }
            
            activityData.activityDescription = `${description}${this.userData.displayName}(${this.userData.userId}) failed for chatroom (${message.chatroomId}).${flagDetails}`;
            this.structureService.trackActivity(activityData);
        });
    }
    expandFlagOPtions(message) {
        let openFlagOPtion = true;
        if($('.cat__apps__messaging__tab__time.thread-flag.item-'+message.chatroomId).hasClass('show')) {
            openFlagOPtion = false;
        }
        $('.cat__apps__messaging__tab__time').removeClass('show');
        if(openFlagOPtion)
        $('.cat__apps__messaging__tab__time.thread-flag.item-'+message.chatroomId).addClass('show');
    }
    /**On date range filter emits the value. */
    dateRangeSelected(event: DateRangeSelected) {
        this.dateRange = event;
        this.resetSearch();
    }
    /**
     * Check for date range filter sesson value
     * The session value should be applied only if the prop value is empty
     */
    private checkDateRangeFilterInboxSession() {
        if(!isBlank(this.storeService.getStoredData(this.dateRangeStoreKey)) && isBlank(this.dateRange)) {
            this.dateRange = JSON.parse(this.storeService.getStoredData(this.dateRangeStoreKey));
        }
    }

    manageTags(threadData: any) {
        this.manageTagsComponent.show({ ...threadData });
        this.selectedThread = threadData;
    }

    handleEmitTags(selectedTags: any) {
        const existingTags = selectedTags.filter(a => {
            const indexFound = isBlank(this.selectedThread.messageTagList)? -1 : this.selectedThread.messageTagList.findIndex(b => b.id === a.id && b.type === MessageTagType.THREAD);
            return indexFound > -1;
        });
        if(existingTags.length > 0) {
            this.structureService.showAlertMessagePopup({
                title: "",
                text: this.toolTipService.getTranslateDataWithParam('MESSAGES.TAGS_ALREADY_EXISTS', { tags: existingTags.map((tag) => tag.name).join(', ') }),
                type: 'error'
            });
        } else {
            this.processSaveTags(selectedTags);
        }
    }

    processSaveTags(selectedTags: any) {
        const request = {
            chatroomIds: [this.selectedThread.chatroomId],
            multiple: selectedTags.map((tag) => tag.id).join(';'),
            single: "",
            id: []
        } 
        this.selectedThread.messageTagList = !isBlank(this.selectedThread.messageTagList) ? [...this.selectedThread.messageTagList, ...selectedTags] : selectedTags;
        this.selectedThread = this.restructureTags(this.selectedThread);
        this.saveTagsToThreads(request);
    }

    saveTagsToThreads(request) {
        NProgress.start();
        this._inboxService.uploadChatTags(request, "addchattags", timezone.name()).then((results: any) => {
            if (results.success) {
                this.structureService.notifyMessage({
                    type: 'success',
                    messge: this.toolTipService.getTranslateData('LABELS.THREAD_TAGGED_SUCCESSFULLY'),
                });
            } else {
                this.structureService.notifyMessage({
                    messge: results.content
                });
            }
            NProgress.done();
            this.currentPage = 1;
            this.loadPage(this.currentPage);
            this.selectedThread = null;
        }).catch((error) => {
            NProgress.done();
            this.selectedThread = null;
            const errorMsg = error && error.data.errors  && error.data.errors.length && error.data.errors[0].message
                ? error.data.errors[0].message: this.toolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG');
                this.structureService.notifyMessage({ message: errorMsg });
        });
    }

      restructureTags(data) {
        data.allTags = [
            ...(isBlank(data.messageTagList) ? []: data.messageTagList).map((item) => {
                item.threadLevel = item.type === MessageTagType.THREAD;
                return item;
            })
        ];
        return data;  
    }

    removeMessageTags(tagId: Number, chatroomId: any) {
        const index = this.inboxData.findIndex((chatroom) => Number(chatroom.chatroomId) === Number(chatroomId));
        if (index > -1) {
            this.inboxData[index].messageTagList = this.inboxData[index].messageTagList.filter(tag => Number(tag.id) !== Number(tagId));
            this.inboxData[index] = this.restructureTags(this.inboxData[index]);
            let pageCount = parseInt(localStorage.getItem("messageInboxPagination"));
            this.structureService.inboxData = this.inboxData;
            this.structureService.inboxDataCurrentPage = this.currentPage;
            if (pageCount == 1) {
                this.inboxDataFirstPage =  this.inboxData;
                this.structureService.inboxDataFirstPage = JSON.parse(JSON.stringify(this.inboxDataFirstPage));
            }
        }
    }

    private confirmedDeleteChatroomMessages(chatroomId: number, type: number ,messageId?: number) {
        let activityData = {
            activityName: "Archive Messages",
            activityType: "manage messaging",
            activityDescription: `${this.userData.displayName} archived ${type === MessageArchiveType.ALL_USER ? 'all users in the' : 'messages of'} chatroom - ${chatroomId}`
        };
        this._inboxService.deleteChatroomMessages(chatroomId, type).then((data) => {
            if(data.success){
                this.totalCount = this.totalCount -1;
                this.structureService.inboxTotalMessageCount = this.totalCount;
                localStorage.setItem("inboxTotalMessageCount",JSON.stringify(this.totalCount));
                if(this.inboxPageMessageCount == 1 && this.currentPage > 1){
                    this.currentPage = this.currentPage - 1;
                    localStorage.setItem("messageInboxPagination",JSON.stringify(this.currentPage));
                }
                this.structureService.trackActivity(activityData);
                this.removeScopeMessageOnArchive(messageId);
                const pageCountMessage = parseInt(localStorage.getItem("messageInboxPagination")) || 1;
                if ($('div .sa-confirm-button-container button').hasClass('hidebutton')){
                    $('div .sa-confirm-button-container button').removeClass('hidebutton');
                }
                this.loadPage(pageCountMessage);    
            } else {
                activityData.activityDescription = `Archive ${type === MessageArchiveType.ALL_USER ? 'all users' : 'message'} by ${this.userData.displayName} failed for chatroom - ${chatroomId}`;
                this.structureService.trackActivity(activityData);
            }
            this.notifyMessageArchiveStatus(data.success);
        }).catch((ex) => {
            this.notifyMessageArchiveStatus(false);
            activityData.activityDescription = `${this.userData.displayName} Error occured while archiving ${ type === MessageArchiveType.ALL_USER ? 'all user initiated': ''} messages of chatroom - ${chatroomId}. Error caught:${ex.toString()}`;
            this.structureService.trackActivity(activityData);
        });
    }

    private notifyMessageArchiveStatus(success: boolean) {
        const notifyMessage = success ? this.toolTipService.getTranslateData('SUCCESS_MESSAGES.ARCHIVED_MESSAGE') 
        : this.toolTipService.getTranslateData('ERROR_MESSAGES.ARCHIVED_MESSAGE');
        const notify =   $.notify(notifyMessage);
            setTimeout(() => {
                notify.update({ type: success ? CONSTANTS.notificationTypes.success
                    : CONSTANTS.notificationTypes.warning ,
                     message: `<strong>${notifyMessage}</strong>`});
            }, 1000);
    }
    changePinUnpinStatus(chatroomId, action) {
        let swal_options = {
            title:  this.toolTipService.getTranslateData('MESSAGES.ARE_YOU_SURE'),
            text:  action === 'pin' ? this.toolTipService.getTranslateData('MESSAGES.CHAT_PIN') 
            : this.toolTipService.getTranslateData(
                'MESSAGES.CHAT_UNPIN'),
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            confirmButtonText: this.toolTipService.getTranslateData('BUTTONS.OK'),
            closeOnConfirm: true,
            cancelButtonText: this.toolTipService.getTranslateData('BUTTONS.CANCEL')
        }
        this.structureService.showAlertMessagePopup(
        swal_options).then((confirm) => {
            if(confirm) {
                NProgress.start();
                let chatroomIds = Array.isArray(chatroomId)? chatroomId: [chatroomId];
                this._inboxService.updateMessagePinUnpinStatus(chatroomIds, action).then((res)=>{
                    if(res['status'].code === 200) {
                        this.structureService.notifyMessage({
                            messge: action === 'pin' ? this.toolTipService.getTranslateData('SUCCESS_MESSAGES.CHAT_PIN') 
                            : this.toolTipService.getTranslateData(
                                'SUCCESS_MESSAGES.CHAT_UNPIN'),
                            delay: 1000,
                            type: 'success'
                        });
                        this.inboxData.forEach((elem)=>{
                            if(chatroomIds.includes(Number(elem.chatroomId))) {
                                elem.pinnedStatus = action === 'pin' ? true : false;
                                elem.markAsRead = false;
                            }
                        });
                        if(Number(localStorage.getItem("messageInboxPagination")) > 1 && action === 'pin')  {
                            this.inboxData = this.inboxData.filter(element => {
                                return !chatroomIds.includes(Number(element.chatroomId));
                            });
                            if(this.inboxData.length <= 10) {
                                this.activate(false, 0, 1, false,true);
                            }
                        } else {
                            this.orderInboxData(this.inboxData);
                        }
                        this.resetInboxMarkAsRead(this.inboxData,false, true);
                    } else {
                        this.structureService.notifyMessage({
                            messge: res['status'].message,
                            delay: 1000,
                            type: 'danger'
                        });
                    }
                    NProgress.done();
                }).catch((ex) => {
                    NProgress.done();
                    this.structureService.notifyMessage({
                        messge: this.toolTipService.getTranslateData('MESSAGES.ERROR_MSG_EXCEPTION_CATCH'),
                        delay: 1000,
                        type: 'danger'
                    });
                });
            }
        });
    }
    changePinUnpinStatusMultiple(selectedChats) {
        let markSelected = true;
        let chatroomIds = [];
        let sublistIds = [];
        let pinnedChat = 'pin';
        let maskedMessagesRoomIds = [];
            if (!selectedChats) {
                markSelected = false;
            }
            const thisref = this;
            this.inboxData.map(function (messages) {
                if (messages.markAsRead === markSelected) {
                    if(messages.pinnedStatus) {
                        pinnedChat = 'unpin';
                    }
                        if(messages.messageType != '2') {
                            chatroomIds.push(Number(messages.chatroomId));
                        } else {
                            if(messages.subList.length) {
                                sublistIds = messages.subList.map(function(sub) {
                                    return Number(sub.chatroomId);
                                });
                            }
                            if( messages.invited_status == '0') {
                                maskedMessagesRoomIds.push(Number(messages.chatroomId));
                                if(sublistIds.length > 0) {
                                    maskedMessagesRoomIds = $.merge(maskedMessagesRoomIds, sublistIds);
                                }	
                            } else {
                                maskedMessagesRoomIds.push(Number(messages.chatroomId));
                                if(thisref.userId == messages.createdby) {
                                    maskedMessagesRoomIds.push(Number(messages.initiatedBaseId));
                                }
                            }
                            if(messages.hiddenChatRoomId) {
                                maskedMessagesRoomIds.push(Number(messages.hiddenChatRoomId));
                            }
                        }
                }
            });
            if (chatroomIds.length || maskedMessagesRoomIds.length) {
                chatroomIds = $.merge(chatroomIds, maskedMessagesRoomIds);
            }
            this.changePinUnpinStatus(chatroomIds, pinnedChat);
    }
    orderInboxData(inboxData){
        let pinnedmsg = inboxData.filter(x=> x.pinnedStatus);
        let nonpinned = inboxData.filter(x=> !x.pinnedStatus);
        let sortField = 'messageOrder';
        pinnedmsg = pinnedmsg.sort((a,b) => (b[sortField] > a[sortField]) ? 1 : ((a[sortField] > b[sortField]) ? -1 : 0)); 
        nonpinned = nonpinned.sort((a,b) => (b[sortField] > a[sortField]) ? 1 : ((a[sortField] > b[sortField]) ? -1 : 0)); 
        this.inboxData = [...pinnedmsg, ...nonpinned]; 
    }
    /** For ngFor directive performace */
    trackByForInboxData(index, item): number {
        return item.chatroomId;
    }

    trackBySubThread(index, item): string { 
        return item.chatroomid;
    }

    private getPatientSiteIdForMsgForward(message: any) {
        this.patientSiteId = [];
        this.isSiteFilterDisabled = false;
        if(message && message.patientInfo && message.patientInfo.patientSiteId) {
            this.patientSiteId.push(+message.patientInfo.patientSiteId);
            this.isSiteFilterDisabled = true;
        }        
    }

    formatMessageDeletedTime(dateTimeUTC: string): string {
        return dateTimeUTC ? moment(dateTimeUTC).format(msgHistoryDateTimeFormat) : '';
    }
}
