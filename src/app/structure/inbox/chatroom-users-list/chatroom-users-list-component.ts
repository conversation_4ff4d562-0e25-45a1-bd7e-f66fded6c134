import { Component, Input, Output, EventEmitter, OnDestroy, ChangeDetectionStrategy } from '@angular/core';
import { StructureService } from 'app/structure/structure.service';
import { UserGroup } from 'app/constants/constants';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { ChatroomUserListType } from './chatroom-users-list.interface';

@Component({
  selector: 'app-chatroom-users-list',
  templateUrl: './chatroom-users-list-component.html',
  styleUrls: ['./chatroom-users-list-component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ChatroomUsersListComponent implements OnDestroy {
  @Input() title: string;
  @Input() items: any;
  @Input() listType: ChatroomUserListType;
  @Input() expanded = true;
  @Input() closed = false;
  @Input() toolTip = '';
  @Output() removeUser = new EventEmitter<any>();
  @Output() removeRole = new EventEmitter<any>();
  @Output() restoreUser = new EventEmitter<any>();
  chatroomUserListType = ChatroomUserListType;
  loggedUserNotPatient = false;
  archivedUserFlag = '';
  constructor(private structureService: StructureService, private toolTipService: ToolTipService) {
    this.loggedUserNotPatient = +this.userData.group !== UserGroup.PATIENT;
    this.archivedUserFlag = this.toolTipService.getTranslateData('WARNING.NO_LONGER_IN_CHAT');
  }
  get userData() {
    return JSON.parse(this.structureService.userDetails);
  }
  get manageChatRoomParticipants() {
    return this.userData && this.userData.privileges && this.userData.privileges.split(',').includes('manageChatRoomParticipants');
  }
  toggleExpand(): void {
    this.expanded = !this.expanded;
    if (this.items) {
      this.items.forEach((item) => {
        const currentItem = item;
        currentItem.toggled = false;
      });
    }
  }
  ngOnDestroy(): void {
    if (this.removeUser) this.removeUser.unsubscribe();
    this.items = [];
    this.title = '';
  }
  trackByUsers(index: number, item): string {
    return item.userId;
  }
  trackByRoles(index: number, item): string {
    return item.id;
  }

  restoreUsersUnderRole(item: any): void {
    const usersToRestore = item.members
      .filter((user) => user.deleted)
      .map((u) => {
        return { id: u.userId, displayName: u.displayName };
      });
    const emitValue = { role: { name: item.name, id: item.id, users: usersToRestore } };
    this.restoreUser.emit(emitValue);
  }

  restoreUserFromRoleList(user: any): void {
    const emitValue = { user: { id: user.userId, displayName: user.displayName } };
    this.restoreUser.emit(emitValue);
  }
}
