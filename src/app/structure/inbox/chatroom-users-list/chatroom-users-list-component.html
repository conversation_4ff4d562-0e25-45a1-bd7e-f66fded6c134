<div class="chatroom-section">
  <div class="section-header header-highlight" (click)="toggleExpand()" [ngClass]="{'list-selected': expanded}">
    <strong class="title"
      >{{ title }} <span class="info-icon"><i [chToolTip]="toolTip"></i></span
    ></strong>
    <span [ngClass]="expanded ? 'fa fa-chevron-up' : 'fa fa-chevron-down'"></span>
  </div>
  <div class="section-body" *ngIf="expanded">
    <ng-container *ngIf="listType === chatroomUserListType.ROLES; else participants">
      <ul>
        <li *ngFor="let item of items; trackBy: trackByRoles" class="row li-margin">
          <div class="col-md-9 d-inline-flex">
            <span class="role-name">{{ item.name }} </span>
          </div>
          <div class="col-md-1">
            <span
              class="hand-pointer"
              *ngIf="manageChatRoomParticipants && item.showRestoreBtn"
              (click)="restoreUsersUnderRole(item)"
              title="{{ 'TOOLTIPS.RESTORE_ROLE_INFO' | translate }}"
              ><i class="fa fa-undo"></i
            ></span>
          </div>
          <div class="col-md-1">
            <i class="action-button" [ngClass]="item.toggled ? 'fa fa-minus' : 'fa fa-plus'" (click)="item.toggled = !item.toggled"></i>
          </div>
          <div class="col-md-1 role-remove-div" *ngIf="!item.deleted && manageChatRoomParticipants">
            <span
              class="fa fa-trash delete-user"
              *ngIf="loggedUserNotPatient"
              (click)="removeRole.emit(item)"
              title="{{ 'TOOLTIPS.REMOVE_ROLE_FROM_CHAT' | translate }}"
            ></span>
          </div>
          <ng-template
            [ngTemplateOutlet]="subListRoleUsers"
            [ngTemplateOutletContext]="{ members: item.members, toggled: item.toggled }"
          ></ng-template>
        </li>
      </ul>
    </ng-container>
  </div>
</div>
<ng-template #participants>
  <ul>
    <ng-container *ngFor="let user of items">
      <div class="cat__top-bar__activity__item chatroom-member-drop" *ngIf="user.showParticipant">
        <div class="cat__apps__messaging__tab__avatar">
          <a class="cat__core__avatar cat__core__avatar--50" href="javascript:void(0);"
            ><img *ngIf="user.avatar" src="{{ user.avatar }}"
             outOfOfficeStatus [oooInfo]="user.oooInfo" [customClass]="'chatroom-users-circle-badge'" onerror="this.src='assets/img/file-404.png'" alt="Avatar" />
          </a>
        </div>
        <div class="cat__top-cat__apps__messaging__tab__content inbox-cont-row">
          <div class="cat__top-bar__activity__title">
            <div>
              <span *ngIf="!user.caregiver_userid" class="chatmember-name"
                >{{user.displayName}} <span *ngIf="user.naTagNames && user.naTagNames !== ''">({{user.naTagNames}}) </span></span
              >
              <span *ngIf="user.caregiver_userid" class="chatmember-name"
                >{{ user.displayName }}{{ !user.isSelfUser ? ' (' + user.caregiver_displayname + ')' : '' }}
              </span>
              <span
                class="fa fa-trash delete-user delete-icon-align"
                id="delete-user_{{ user.userId }}"
                title="{{'TOOLTIPS.REMOVE_PARTICIPANT_FROM_CHAT'| translate}}"
                *ngIf="loggedUserNotPatient && (manageChatRoomParticipants || +userData.userId === +user.userId)"
                (click)="removeUser.emit(user)"
              ></span>
              <span id="{{user.userId}}" *ngIf="user.isSelfUser">&nbsp;({{'LABELS.MESSAGE_USER.ME' | translate}})</span>
              <span *ngIf="!user.passwordStatus">(Virtual) </span>
              <span id="name_{{user.userId}}" *ngIf="+user.tenantid !== +userData.tenantId">&nbsp;[{{user.tenantName}}]</span>
            </div>
            <div *ngIf="loggedUserNotPatient">
              <div class="cat__top-bar__activity__descr" *ngIf="user.relation && !user.isSelfUser && user.roleName === 'Alternate Contact'">
                {{ 'LABELS.RELATION' | translate }}: {{ user.relation }}
              </div>
              <div class="cat__top-bar__activity__descr" *ngIf="user.mobile && !user.isSelfUser">
                {{'LABELS.MOBILE'| translate}}: {{user.mobile | phone}}
              </div>
              <ng-container *ngIf="user.isPatientGroup && !user.isSelfUser && user.roleName !== 'Alternate Contact'">
                <div class="cat__top-bar__activity__descr" *ngIf="!user.caregiver_dob && user.dob">
                  {{ 'LABELS.PATIENT_DOB' | translate }}: {{user.dob | date:'MM/dd/yyyy'}}
                </div>
                <div class="cat__top-bar__activity__descr" *ngIf="user.caregiver_dob">
                  {{ 'LABELS.PATIENT_DOB' | translate }}: {{user.caregiver_dob | date:'MM/dd/yyyy'}}
                </div>
                <div class="cat__top-bar__activity__descr" *ngIf="!user.caregiver_identity && user.IdentityValue">
                  {{ 'LABELS.PATIENT_MRN' | translate }}: {{user.IdentityValue}}
                </div>
                <div class="cat__top-bar__activity__descr" *ngIf="!user.IdentityValue && user.caregiver_identityvalue">
                  {{ 'LABELS.PATIENT_MRN' | translate }}: {{user.caregiver_identityvalue}}
                </div>
              </ng-container>
            </div>
          </div>
        </div>
      </div>
    </ng-container>
  </ul>
</ng-template>

<ng-template #subListRoleUsers let-members="members" let-toggled="toggled">
  <ul *ngIf="toggled" class="sub-list">
    <li *ngFor="let item of members; trackBy: trackByUsers" [ngClass]="{ 'text-muted': item.deleted }">
      <span class="sub-list-items"
        >{{ item.displayName }} <span *ngIf="item.deleted">({{ archivedUserFlag }})</span>
        <span
          class="float-right btn-restore"
          title="{{ 'TOOLTIPS.RESTORE_USER_INFO' | translate }}"
          *ngIf="item.deleted && loggedUserNotPatient && (manageChatRoomParticipants || +userData.userId === +item.userId)"
          (click)="restoreUserFromRoleList(item)"
        >
          <i class="fa fa-undo" aria-hidden="true"></i>
        </span>
      </span>
    </li>
  </ul>
</ng-template>
