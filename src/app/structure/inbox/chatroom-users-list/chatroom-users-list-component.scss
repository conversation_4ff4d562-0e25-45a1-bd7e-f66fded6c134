:root {
  ---common-background-color: #f2f4f8;
}
.chatroom-section {
  margin-bottom: 10px;
  font-family: inherit !important;
  .section-header {
    background-color: #ffffff;
    padding: 10px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      margin: 0;
      font-size: 14px;
      color: #393749;
      font-weight: bold;
    }
  }
  .header-highlight:hover {
    background-color: #f2f4f8 !important;
  }
  .delete-icon-align {
    position: relative;
    left: 2.5%;
  }
  .action-button {
    cursor: pointer;
  }
  ul .sub-list {
    width: 100%;
    li {
      padding: 0 2.5% 0 2.5%;
      .sub-list-items {
        border-bottom: 1px solid #e4e9f0;
        width: 100%;
        height: 30px;
        align-items: center;
        padding: 2%;
      }
    }
    li:last-child .sub-list-items {
      border-bottom: none;
    }
  }
  .role-name {
    font-weight: bold;
    text-transform: capitalize;
  }
  .role-name:hover {
    background-color: var(--common-background-color);
  }
}

.section-body {
  padding: 10px 10px 0 10px;
  background-color: #fff;
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

li {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 0;
}

.action-button {
  background: none;
  border: none;
  cursor: pointer;
  margin-left: 5px;
}

.list-selected {
  background-color: #f2f4f8 !important;
}

.li-margin {
  margin-left: 0.71rem;
  margin-right: 0.71rem;
  border-bottom: 1px solid #e4e9f0;
}

.li-margin:last-child {
  border-bottom: none;
}

.role-remove-div {
  padding-right: 0;
}

.info-icon {
  margin-left: 1mm;
}

.btn-restore {
  height: 20px;
  width: 20px;
  text-align: center;
  font-size: smaller;
  font-weight: bolder;
  align-items: center;
  display: flex;
  color: teal;
  cursor: pointer;
}
