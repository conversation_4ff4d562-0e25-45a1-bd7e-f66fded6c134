<!-- START: apps/messaging -->
<section class="card chatroom-card-cnt">
    <div id="tag-button" class="tag-button-div hide">
            <div class="modal-dialog" role="document">
                    <div class="modal-content tag-modal">
                        <div class="modal-header">
                            <h4 class="modal-title" id="messageTagModalLabel">Tag Message</h4>                                
                            <button type="button" class="close" (click)="closeTagModal()" aria-label="Close">
                                            <span aria-hidden="true">&times;</span>
                                            </button>
                        </div>
                        <div class="modal-body">
                            <form [formGroup]="messageTagForm" id="messageTagForm">
                                <div style="margin-bottom: 9px;">
                                    <select class="form-control select2 available-message-tag-select2" formControlName="messageTags" id="messageTags" multiple> 
                                        <option *ngFor="let tags of availableTags" value="{{tags.id}}">   {{tags.name}}  {{tags.typeName ? '(' : ''}}{{tags.typeName ? tags.typeName : ''}}{{tags.typeName ? ')' : ''}}{{ ((userData.crossTenantId &&  (userData.crossTenantId != existingTagstenantId)) && (activeMessage.selectedTenantId))  ? '[' : ''}} {{((userData.crossTenantId &&  (userData.crossTenantId != existingTagstenantId)) && (activeMessage.selectedTenantId)) ? existingTagstenantName  : ''}} {{((userData.crossTenantId &&  (userData.crossTenantId != existingTagstenantId)) && (activeMessage.selectedTenantId))  ? ']' : ''}}</option>
                                    </select>                                    
                                </div>
                                <label *ngIf="!allowIntegrationTags">{{ (_structureService.isMultiAdmissionsEnabled ? 'MESSAGES.ONLY_TAGS_WITHOUT_INTEGRATION_MA' : 'MESSAGES.ONLY_TAGS_WITHOUT_INTEGRATION') | translate }}</label><br>
                                <label class="message-tag-loading-message" *ngIf="getAllTagsAPILoading">Please wait while we load the message tags <i class="icmn-loader"><img src="../../../assets/img/loader/color.gif" class="menu-spinner"></i></label><br>
                                <label *ngIf="!getAllTagsAPILoading">{{taggedMessageDetails.length}} message(s) selected</label><br>
                                <div *ngIf="tagNotSelected"
                                            class="alert alert-danger">
                                            Select one or more Tags
                                </div>
                                <div [hidden]="(!patientFace || +userData.group === 3 || !allowIntegrationTags)">
                                    <div class="row">
                                        <div class="col-lg-12" [hidden]="!hideSiteSelection || (disablePatientSelectInTag && patientFace)">
                                            <span class="col-md-4">
                                                <app-select-sites [events]="eventsSubject.asObservable()" [selectedSiteIds]=siteIds [singleSelection]=true (siteIds)="getAssocSiteIds($event,'Associate Patient')" [crossSite]=false (hideDropdown)="hideDropdownAss($event)" [chatRoomFilter]=true>
                                                </app-select-sites>
                                            </span>
                                        </div>
                                    </div>
                                    <div class="row tag-msg-row">
                                        <div class="col-lg-12">
                                            <div class="form-group row">
                                                <div class="col-md-10 tag-msg-patient-search">
                                                    <input type="text" class="w-100;" [disabled]="selectedAssosiatePatientId && privileges.indexOf('PatientSelectioninMessageTagging')==-1" class="form-control" id="associate-search-input" autocomplete="off" placeholder="Search a Patient..." [disabled]="disablePatientSelectInTag" (click)="openAssociateList()"/>
                                            <div class="message-tag-tooltip icmn-info" style="left: 4% !important;"  *ngIf="(selectedAssosiatePatientId && privileges.indexOf('PatientSelectioninMessageTagging')==-1)"  data-toggle="tooltip" data-placement="top" title="Allows the selection of the Patient during Chat Messages Tagging when enabled"></div>
    
                                                                                    
                                                <ul class="associate-ul tag-msg-patient-search-ul" id="associate-ul">
                                                    <li id="associate-li" class="associate-li selected" *ngIf="selectedAssosiatePatientName!=''">{{selectedAssosiatePatientName}}</li>
                                                    <li id="associate-li" class="associate-li" *ngIf="assosiatedPatients.length==0">No item found</li>
                                                    <li id="associate-li" class="associate-li" [ngClass]="{'selected': (selectedAssosiatePatient==assosiatedPatient.userId)}" *ngFor="let assosiatedPatient of assosiatedPatients" [hidden]="selectedAssosiatePatient==assosiatedPatient.userId" (click)="selectAssosiatePatient(assosiatedPatient.userId,assosiatedPatient.listDisplayName,assosiatedPatient.externalSystemId)">
                                                            {{assosiatedPatient.listDisplayName}}
                                                    </li>
                                                </ul>
                                                </div>
                                                <div class="col-md-2 tag-msg-search-reset-btn" [hidden]="(selectedAssosiatePatientId && privileges.indexOf('PatientSelectioninMessageTagging')==-1)" >
                                                    <button [disabled]="associatePatientLoading || disablePatientSelectInTag" id="associate-search" (click)="checkAssociatePatientWithTems()"  class="associate-search-button btn btn-sm btn-primary" >{{'BUTTONS.SEARCH' | translate}}</button>
                                                    <button [disabled]="associatePatientLoading || disablePatientSelectInTag" class="associate-close" id="associate-close" (click)="closeSelectedAssociatePatient()"  class="associate-search-button btn btn-sm btn-default" >{{'BUTTONS.RESET' | translate}}</button>
                                                </div> 
                                            </div>
                                        </div>
                                    </div>
                                    <div *ngIf="userNotSelected && patientFace &&  userData.group !== '3' "
                                            class="alert alert-danger tag-msg-alert-div">
                                           {{'VALIDATION_MESSAGES.SELECT_PATIENT' | translate}}
                                    </div>
                                    <div class="tag-msg-admission-div" *ngIf="isMultiAdmissionsEnabled && selectedPatientId">
                                        <div class="col-lg-12 pl-0">
                                            <app-admissions-dropdown [selectedPatient]="selectedPatientId" [siteIds]="assocSiteId" (selectedItem)="setAdmissionId($event)" [selectedAdmissionId]="tagMsgSelectedAdmissionId" [disabled]="disablePatientSelectInTag"></app-admissions-dropdown> 
                                        </div>
                                    </div>
                                    <div *ngIf="admissionNotSelected &&  userData.group !== '3' "
                                            class="alert alert-danger tag-msg-alert-div">
                                           {{'VALIDATION_MESSAGES.ADMISSION_REQUIRED' | translate}}
                                    </div>
                                </div>
                            </form>
                            <label>Select more messages to tag together</label>
                            <div class="tag-modal-processing" *ngIf="disableButton"><label class="spinner-text">{{tagProcessingText}}</label></div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" (click)="closeTagModal()">Close</button>
                            <button type="button" class="btn btn-primary" id="submitTag" [disabled]="disableButton" (click)="submitText()" style="cursor:pinter;">Save</button>
                        </div>
                    </div>
            </div>
    </div>
    <div id="msginfo-button" class="tag-button-div hide">
        <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h4 class="modal-title" id="messageTagModalLabel">Message Info</h4>                                
                        <button type="button" class="close" (click)="closeTagModal()" aria-label="Close">
                                        <span aria-hidden="true">&times;</span>
                                        </button>
                    </div>
                    <div  class="modal-body" style="max-height:70%; width:100%;margin: 10px 0px;overflow-y: scroll;" > 
                        <label class="message-tag-loading-message" *ngIf="messageInfoLoading">
                            Please wait while data loading...
                        </label>  
                        <div *ngIf="messageInfo" id="msginfo-button_{{messageInfoDetails}}">
                            <div class="cat__apps__chat-block__content message-content message-content"> 
                                <div class="cat__apps__messaging__tab__text chat-message-container">
                                    <p compile="{{messageInfoData.messageText | autolinker}}"></p>
                                </div>
                                </div>
                                <hr class="hr-info"/>
                                <div class="readinfo">
                                    <p><b>Read by {{ (messageInfoData.readUsers && messageInfoData.readUsers.length)?messageInfoData.readUsers.length:0}} user(s)</b></p>
                                    <hr class="hr-read"/>
                                    <div class="user-list-sub " *ngFor="let readUsers of messageInfoData.readUsers;let j=index">
                                        <div class="cat__core__avatar cat__core__avatar--50 read-user-img">
                                            <img src="{{ readUsers.avatar }}" (error)="handleImageError($event)">
                                        </div>
                                        <span class="read-user">{{ readUsers.displayName }}&nbsp;{{readUsers.lastactivity ? (readUsers.lastactivity*1000 | dateshortyearFilter ) : ''}}</span>
                                    </div>
                                </div>
                                <div>
                                    <p><b>Delivered to {{ (messageInfoData.deliveryData && messageInfoData.deliveryData.length)?messageInfoData.deliveryData.length:0}} user(s)</b></p>
                                    <hr class="hr-read"/>
                                    <div class="user-list-sub " *ngFor="let deliveredUsers of messageInfoData.deliveryData;let j=index">
                                    <div class="cat__core__avatar cat__core__avatar--50 read-user-img">
                                        <img src="{{ deliveredUsers.avatar }}" (error)="handleImageError($event)">
                                        
                                    </div>
                                    <span class="read-user">{{ deliveredUsers.displayName }}&nbsp;{{deliveredUsers.deliverytime ? (deliveredUsers.deliverytime*1000 | dateshortyearFilter) : ''}}</span>
                                </div>
                            </div>
                        </div>  
                   
                        <div *ngIf="recipientmessageInfo" id="sender-msginfo-button_{{senderMessageInfoDetails}}">
  
                        <div class="cat__apps__chat-block__content message-content message-content">
                            <div class="cat__apps__messaging__tab__text chat-message-container">
                                <p compile="{{recipientMessageInfoDetails.msg | autolinker}}"></p>
                            </div>
                        </div>  
                        <hr class="hr-read"/>           
                        <label><b>Sent By</b> </label>
                        <hr class="hr-read"/>
                        
                        <div >
                            <div class="cat__core__avatar cat__core__avatar--50 read-user-img">
                                <img src="{{ recipientMessageInfoDetails.avatar }}" alt="Alternative text to the image2">
                                
                            </div>
                            <span class="read-user">{{recipientMessageInfoDetails.name}}&nbsp;{{recipientMessageInfoDetails.msg_sent*1000 | dateshortyearFilter}}</span>  
                        </div>    
                      </div>
                      <div id="message-history" class="mt-3" *ngIf="messageHistory && messageHistory.length > 0">
                        <p><b>{{'LABELS.MESSAGE_HISTORY' | translate}}</b></p>
                        <hr class="hr-read"/>
                        <ng-container *ngFor="let msg of messageHistory; let i = index;">
                            <span>{{ messageHistory.length > 1 ? messageHistory.length - i+'.' : ''}} {{ msg.actionHistory }}</span><br>
                        </ng-container>
                    </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" (click)="closeTagModal()" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
    </div>
<app-sign-pad (newcomp)="sendSignature($event);"></app-sign-pad>

<!-- Message Tag Modal -->
<div class="modal fade forward-modal message-tag-modal-container" id="messageTagModal" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="messageTagModalLabel">Tag Message</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                                </button>
            </div>
            <div class="modal-body">
                <form [formGroup]="messageTagForm" id="messageTagForm">
                    <div style="margin-bottom: 9px;">
                        <select class="form-control select2" formControlName="messageTags" id="messageTags" multiple> 
                            
                            <option *ngFor="let tags of availableTags" value="{{tags.id}}">   {{tags.name}}  {{tags.typeName ? '(' : ''}}{{tags.typeName ? tags.typeName : ''}}{{tags.typeName ? ')' : ''}}{{ ((userData.crossTenantId &&  (userData.crossTenantId != existingTagstenantId)) && (activeMessage.selectedTenantId))  ? '[' : ''}} {{((userData.crossTenantId &&  (userData.crossTenantId != existingTagstenantId)) && (activeMessage.selectedTenantId)) ? existingTagstenantName  : ''}} {{((userData.crossTenantId &&  (userData.crossTenantId != existingTagstenantId)) && (activeMessage.selectedTenantId))  ? ']' : ''}}</option>
                        </select>                                    
                    </div>
                    <div *ngIf="tagNotSelected"
                                class="alert alert-danger">
                                Select one or more Tags
                    </div>
                    <div [hidden]="(!patientFace ||  userData.group == '3')">
                        <select class="form-control select2" formControlName="users" id="users" > 
                            <option></option>
                            <option *ngFor="let users of usersListForTagMessage" value="{{users.userid}}"> {{(users.caregiver_displayname ? (users.passwordStatus == 'true' ? users.displayname + ' (' + users.caregiver_displayname + ')' + ' (Enrolled)' : users.displayname + ' (' + users.caregiver_displayname + ')' + ' (Virtual)') : (users.passwordStatus == 'true' ? users.displayname + ' (Enrolled)' : users.displayname+ ' (Virtual)'))}}</option>
                        </select>                                    
                    </div>
                    <div *ngIf="userNotSelected && patientFace &&  userData.group !== '3' "
                                class="alert alert-danger">
                                Select a Patient
                    </div>
                </form>
               <div class="tag-modal-processing" *ngIf="disableButton"><label class="spinner-text">{{tagProcessingText}}</label></div><br>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="submitTag" [disabled]="disableButton" (click)="submitText()" style="cursor:pinter;">Save</button>
            </div>
        </div>
    </div>
</div>
<div class="modal fade userlist-modal" id="userlistModal" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
         <div class="modal-body" *ngIf="showModal">
               <app-status-message-component [chatParticipants]="oooUsers" (ModalEvent)="closeStatusModal()"></app-status-message-component>
         </div>
    </div>
</div>
<!-- End Section -->
<!-- *************************Forward Popup Model**************** -->
<div class="modal fade forward-modal" id="exampleModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="exampleModalLabel">Forward To</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                                </button>
            </div>
            <div class="modal-body">
                <form>
                      <div class="card-block">
                        <div class="cat__core__card-sidebar">
                            <div class="col-sm-12" [hidden]="!hideForwardSiteSelection">
                                   <div class="row">
                                   <div style="padding-top: 6px;">
                                   <span>{{ 'LABELS.FILTER_BY_SITES' | translate }}</span>
                                   </div>
                                   <div style="width: 73%">
                                   <app-select-sites [filterType]=true [pushSites]=true [disableFilter]="isSiteFilterDisabled" [events]="eventsSubject.asObservable()" [selectedSiteIds]="tempPatientSiteId" (siteIds)="getForwardSiteIds($event,'Forward')" [crossSite]=false [dynamic]=dynamicFilterValue (hideDropdown)="hideDropdown($event,'Forward')" [chatRoomFilter]=true>
                                   </app-select-sites>
                                   </div>
                                   </div>
                                   </div>
                            <div class="cat__apps__messaging__header chat-with-wrapper-search">
                                <input [ngClass]="{'groups-srch-width-reroute': (optionShow=='groups' || optionShow=='staff' || optionShow=='partner' || optionShow=='patient'), 'others-srch-width':(optionShow!='groups' || optionShow!='staff' || optionShow!='partner' || optionShow!='patient') }" (keydown)="searchOnEnterForward($event,optionShow)" class="form-control cat__apps__messaging__header__input ng-untouched ng-pristine ng-valid" id="forward-modal-search-box" placeholder="Search Here" #userSearchFromForward>
                                <span *ngIf="(optionShow!='groups' || optionShow!='msggroups' || optionShow!='staff' || optionShow!='partner' || optionShow!='patient')" class="icmn-search serch"></span>
                                <button [disabled]="userSearchFromForward.value.trim() == ''" *ngIf="optionShow=='groups' || optionShow=='msggroups' || optionShow=='staff' || optionShow=='partner' || optionShow=='patient'" type="button" class="btn btn-sm btn-primary srchBtn" title="Search" (click)="searchRerouteForward(optionShow)" id="searchreroute">Search</button>
                                <button type="button" class="btn btn-sm btn-default resetBtn" *ngIf="optionShow=='groups' || optionShow=='msggroups' || optionShow=='staff' || optionShow=='partner' || optionShow=='patient'" title="Reset" (click)="resetForward(optionShow)" id="searchreroute_reset">Reset</button>
                            </div>
                        </div>
                    </div>                   
                    <div class="forwarding-behaviour-container">
                        <div class="forward-model-option-user-list" *ngFor="let userdatas of (userList | filterUsersList:userData.userId ); let i=index" [class.cat__apps__messaging__tab--selected]="i == selectedRowForward">
                            <div class="forward-user-role" (click)="setRerouteUser(userdatas, i)">
                                <label class="form-check-label">
                        <input checked="" class="form-check-input" name="userSelection" value="userdatas" type="radio"  [checked]="i === selectedRowForward" id="user_{{userdatas.displayname}}" >                                            
                    </label>
                                <i class="fa fa-clock-o" [hidden]="!userdatas.isScheduled"></i>
                                <p>{{userdatas.displayname}} <span *ngIf="userdatas.naTagNames && userdatas.naTagNames != '' && userdatas.naTagNames != 'null'">({{userdatas.naTagNames}}) </span> <span class="modal-user-role">[{{userdatas.role}}]</span></p>
                            </div>
                        </div>
                        <div class="chat-with-empty-data" *ngIf="userList && !(userList | filterUsersList:userData.userId ).length && !chatWithLoader.staffs" >No Clinicians Available. <span  *ngIf="userSearchFromForward.value"><span style="color: #0088ff;cursor: pointer;" (click)="resetForward(optionShow)"> Click Here </span> to Reset the Search.</span></div>
                        <div *ngIf="chatWithModalShown && chatWithLoader.staffs" class="loading-container">
                            <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                            <div class="loading-text">Loading Clinicians...</div>
                        </div>
                        <div style="text-align: center;width:100%;margin-top: 14px;float: left;" *ngIf="optionShow=='staff'">
                            <button type="button" [hidden]="!(chatWithModalShown && optionShow=='staff' && !noMoreItemsAvailable.users && userList && userList.length)" class="btn btn-sm btn-default" (click)="loadMoreUsers('staff',loadMoreSearchValue)" id="ld_more_staff">{{loadMoremessage.users}}</button>
                        </div>
                        <div style="text-align: center;width:100%;margin-top: 14px;float: left;" *ngIf="optionShow=='partner'">
                            <button type="button" [hidden]="!(chatWithModalShown && optionShow=='partner' && !noMoreItemsAvailable.users && userList && userList.length)" class="btn btn-sm btn-default" (click)="loadMoreUsers('partner',loadMoreSearchValue)" id="ld_more_partner">{{loadMoremessage.users}}</button>
                        </div>
                    </div>
                 


                    <div [hidden]="!forwardingStatus" class="forwarding-behaviour-box">
                        <h5>Forwarding Behaviors</h5>

                        <div class="form-check">
                            <label class="form-check-label">
                        <input [checked]="true" class="form-check-input" id="keep_forwarder"  name="forwardingSelection" value="keep_forwarder" type="radio" (click)="setForwardBehaviour('keep_forwarder')">
                        Keep me in chat session
                    </label>
                        </div>
                        <div class="form-check">
                            <label class="form-check-label">
                        <input checked="" class="form-check-input"  id="remove_forwarder"  name="forwardingSelection" value="remove_forwarder" type="radio"  (click)="setForwardBehaviour('remove_forwarder')">
                        Remove me from chat session
                    </label>
                        </div>
                    </div>

                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal" id="close_fwrd_mdal">Close</button>
                <button type="button"  id="fwrd_mdal" class="btn btn-primary reRouteBehaviour" disabled (click)="reRouteMessage()" style="cursor:pinter;">Forward</button>
            </div>
        </div>
    </div>
</div>
<modal [show-modal]="isModalOpen" [modal-retry]=true [title]="title" [sub-title]="subTitle" [modal-body]="modalBody" [show-header]="showHeader"
    [show-footer]="showFooter" [cancel-label]="cancelLabel" (closed)="closeModal()" [show-iframe]="showIframe" [show-video]="showVideoFrame"  [type]="documentType">
</modal>
<!-- *********************End Section*************************** -->


<!-- ******************** Invite Model ************************* -->
<div class="modal fade forward-modal" id="inviteModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="exampleModalLabel">Invite</h4>
                <button type="button" class="close" (click)="closeInviteModal()" aria-label="Close" id="close_invite" >
                                <span aria-hidden="true">&times;</span>
                                </button>
            </div>
            <div class="modal-body">
                <form>
                   
                    <div class="chatwith-modal-tab row" style="border-bottom: 1px solid #d9e2e6;">
                        <div *ngIf="configData.allow_virtual_patient && configData.allow_virtual_patient != 0 && (!activeMessage.is_patient_discussion_group || activeMessage.is_patient_discussion_group == 0)" class="chatwith-model-head col" (click)="showData('groups')" [class.cat__apps__messaging__tab--selected]="optionShow=='groups'" id="invite_pdg">PDGs</div>
                        <div class="chatwith-model-head col" (click)="showData('msggroups')" [class.cat__apps__messaging__tab--selected]="optionShow=='msggroups'" id="invite_msg_gps">Message Groups</div>
                        <div class="chatwith-model-head col" (click)="getRolesToInviteChat(0,'staffroles');" [class.cat__apps__messaging__tab--selected]="optionShow=='staffroles'" id="invite_staff_roles" >Staff Roles</div>
                        <div class="chatwith-model-head col" (click)="getUsersOnInviteToChat('','',0,'staff', true);" [class.cat__apps__messaging__tab--selected]="optionShow=='staff'" id="invite_staff" >Staff</div>
                        <div class="chatwith-model-head col" (click)="getRolesToInviteChat(0,'partnerroles');" [class.cat__apps__messaging__tab--selected]="optionShow=='partnerroles'" id="invite_partner_roles" >Partner Roles</div>
                        <div class="chatwith-model-head col" (click)="getUsersOnInviteToChat('','',0,'partner', true);" [class.cat__apps__messaging__tab--selected]="optionShow=='partner'" id="invite_partners">Partners</div>                            
                        <div class="chatwith-model-head col" *ngIf="privileges.indexOf('invitePatientsToChatroom')!==-1" (click)="showData('patient')" [class.cat__apps__messaging__tab--selected]="optionShow=='patient'" id="invite_patients" >Patients</div>
                        <div class="chatwith-model-head col" *ngIf="Invitealternates!=='' && this.showAlternattab == true" (click)="showData('alternatecontact',Invitealternates)" [class.cat__apps__messaging__tab--selected]="optionShow=='alternatecontact'" id="invite_contacts" >Associate Contacts</div>
                    </div>
                   <div class="row tab-height">
                    <div class="col-sm-6" style="padding-left: 19px" [hidden]="!hideInviteSiteSelection"
                        *ngIf="showSitefilter && (((optionShow=='staff' || optionShow=='partner' || optionShow=='patient'|| optionShow =='staffroles' || optionShow =='partnerroles')  && !multiSiteEnable && chatWithTenantFilter.filterEnabled && chatWithTenantFilter.tenants.length) || (multiSiteEnable && ((!enableCrossSite) || (enableCrossSite && optionShow=='patient'))))">
                        <div class="row">
                            <div style="padding-top: 6px;"> 
                            <span>{{ 'LABELS.FILTER_BY_SITES' | translate }}</span>
                            </div>
                            <div style="width: 73%">
                                <app-select-sites [filterType]=true [pushSites]=true [disableFilter]="isSiteFilterDisabled"
                                    [events]="eventsSubject.asObservable()" [selectedSiteIds]="tempPatientSiteId"
                                    (siteIds)="getInviteSiteIds($event,'Invite')" [crossSite]=true  [dynamic]=dynamicFilterValue
                                    (hideDropdown)="hideDropdown($event,'Invite')" [chatRoomFilter]=true>
                                </app-select-sites>
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6" style="padding-left: 19px" [hidden]="!hideInviteSiteSelection"
                    *ngIf="showSitefilter && ((multiSiteEnable && enableCrossSite) && (optionShow=='staff' || optionShow=='partner'))">
                    <div class="row">
                        <div style="padding-top: 6px;"> 
                        <span>{{ 'LABELS.FILTER_BY_SITES' | translate }}</span>
                        </div>
                        <div style="width: 73%">
                            <app-select-sites [filterType]=true [pushSites]=true [disableFilter]="isSiteFilterDisabled"
                                [events]="eventsSubject.asObservable()" [selectedSiteIds]="tempPatientSiteId"
                                (siteIds)="getInviteSiteIds($event,'Invite')" [crossSite]=true  [crossSiteCommunication]=true [dynamic]=dynamicFilterValue
                                (hideDropdown)="hideDropdown($event,'Invite')" [chatRoomFilter]=true>
                            </app-select-sites>
                        </div>
                    </div>
                </div>
                    <div [ngClass]="multiSiteEnable ? 'col-sm-6' : 'col-sm-12'" [hidden]="(optionShow!='patient' && optionShow!='msggroups' )">
                               <div class="chat-with-wrapper-search" [ngClass]="{'row':multiSiteEnable}">
                                    <div [ngClass]="{'col-sm-9':multiSiteEnable}">
                                    <input [ngClass]="{'groups-srch-width': optionShow=='groups' || optionShow=='msggroups', 'others-srch-width':optionShow!='groups'  || optionShow=='msggroups'}" (keydown)="searchOnEnter($event,optionShow)" class="form-control cat__apps__messaging__header__input ng-untouched ng-pristine ng-valid" id="invite-modal-search-box" id="userSearchTxt" placeholder="Search Here"
                                        #userSearch>
                                    </div>
                                    <div [ngClass]="{'clo-sm-3':multiSiteEnable}">
                                        <button type="button" [disabled]="!userSearch.value" class="btn btn-sm btn-primary srchBtn" *ngIf="optionShow=='groups' || optionShow=='msggroups'" title="Search" (click)="searchMsgGrp(optionShow)" id="search_msggroups">Search</button>
                                <button type="button" class="btn btn-sm btn-default resetBtn" *ngIf="optionShow=='groups' || optionShow=='msggroups'" title="Reset" (click)="reset(optionShow)" id="search_reset">Reset</button>
                            </div>
                                </div>
                        </div>
                    
                        <div [ngClass]="{'col-sm-6':(multiSiteEnable || (!multiSiteEnable && ( optionShow=='staff' || optionShow =='partner' || optionShow == 'patient' || optionShow =='staffroles' || optionShow =='partnerroles'))),'col-sm-12':(!multiSiteEnable && optionShow!='staff' && optionShow!='partner' && optionShow != 'patient' && optionShow !='staffroles' && optionShow !='partnerroles') || (!multiSiteEnable && !(chatWithTenantFilter.filterEnabled && chatWithTenantFilter.tenants.length))}">
                            <div class="chat-with-wrapper-search row" [ngClass]="{'filter-enabled': (((optionShow=='staff' || optionShow=='partner' || optionShow == 'patient' || optionShow =='staffroles' || optionShow =='partnerroles') && chatWithTenantFilter.filterEnabled && chatWithTenantFilter.tenants.length)&& multiSiteEnable), 'filter-disabled col-sm-12':(!((optionShow=='staff' || optionShow=='partner' || optionShow=='patient' || optionShow =='staffroles' || optionShow =='partnerroles') && chatWithTenantFilter.filterEnabled && chatWithTenantFilter.tenants.length) && !multiSiteEnable)}">
                                <div [ngClass]="{'col-sm-9': (optionShow =='staff' || optionShow =='partner')}">
                                <input [hidden]="optionShow!='staff' && optionShow!='partner'" (keydown)="searchOnEnterKeyPress($event,optionShow);"
                                [ngClass]="{'withfilter': (chatWithTenantFilter.filterEnabled && chatWithTenantFilter.tenants.length), 'withoutfilter':multiSiteEnable && !(chatWithTenantFilter.filterEnabled && chatWithTenantFilter.tenants.length),'withoutfilter-multisite':!multiSiteEnable && !(chatWithTenantFilter.filterEnabled && chatWithTenantFilter.tenants.length)}"
                                id="chat-with-modal-search-box-on-invite" class="form-control" placeholder="{{'PLACEHOLDERS.SEARCH_HERE'| translate}}" #srch>
                               </div>
                               <div [ngClass]="{'col-sm-3': (optionShow == 'staff' || optionShow == 'partner'),
                               'col-sm-12':(optionShow=='staffroles' || optionShow=='partnerroles')}">
                            <button [hidden]="optionShow!='staff' && optionShow!='partner'" [disabled]="!srch.value" type="button"
                                class="btn btn-sm btn-primary srchBtn" title="Search"
                                (click)="getUsersOnInviteToChat('',srch.value,tenantIDForInviteList,optionShow)"
                                id="search_btn_tenant">{{'BUTTONS.SEARCH' | translate}}</button>
                            <button [hidden]="optionShow!='staff' && optionShow!='partner'" type="button"
                                class="btn btn-sm btn-default resetBtn" title="Reset"
                                (click)="getUsersOnInviteToChat('','',tenantIDForInviteList,optionShow, true)" id="reset_btn_tenant">{{'BUTTONS.RESET' | translate}}</button>
                            <input *ngIf="optionShow=='staffroles' || optionShow=='partnerroles'" (keyup)="searchOnKeyPress($event)"
                                class="form-control cat__apps__messaging__header__input ng-untouched ng-pristine ng-valid"
                                id="invite-modal-search-box" id="userSearchTxtRoles" placeholder="Search Here" #userSearch>
                            <i *ngIf="optionShow=='staffroles' || optionShow=='partnerroles'" class="icmn-search serch rolesearchicon"></i>
                        </div>
                        </div>
                        </div>
                </div>
                    <div class="row">
                        <ul class="treeview treeview-section-ui rolesUl" *ngIf="optionShow=='staffroles'">
                            <!--Listing staff roles here-->
                            <li *ngFor="let roles of staffRolesList" class="roleslisting" id="staffroles_{{roles.RoleID}}">
                                    
                                    <i class="fa fa-plus  expand-roles-plus-{{roles.RoleID}}" (click)="getStaffByRoleID(roles.RoleID);" id="{{roles.RoleID}}"></i>
                                    <i class="fa fa-minus hide expand-roles-minus-{{roles.RoleID}}" (click)="getStaffByRoleID(roles.RoleID);" id="remove_{{roles.RoleID}}"></i>
                                    <input type="checkbox" name="middle"  [(ngModel)]="orderMain[roles.RoleID]" (change)="getStaffByRoleID(roles.RoleID,'1');" id="ordermain_{{roles.RoleID}}">
                                    <label for="middle" [ngClass]="(orderMain[roles.RoleID]) ? 'custom-checked' : 'custom-unchecked'" id="order_{{roles.RoleID}}">{{roles.RoleName}}</label>
                                    <span *ngIf="(isStaffsloadingTime==true && clickedRoleId==roles.RoleID)" style="right: 10%;margin-left: 67%;
                                    color:#acb7bf;" id="staff_loading">Loading...</span>
                                    <ul class="sub-item-panel hide sub-role-{{roles.RoleID}}">

                                    </ul>
                                   
                                    
                                </li>
                            <!--Accordian ends here..-->
                            <div *ngIf="optionShow=='staffroles' && !staffRolesList.length && isloadingTime!=true;" style="position: absolute;right:40%;">No Staff Roles Available.</div>
                            <button  class="btn btn-sm btn-default" disabled *ngIf="isloadingTime==true" style="position: absolute;right:40%;">
                                  <span *ngIf="isloadingTime==true">Loading Staff Roles...</span>  
                            </button>
                            <span style="position: absolute;right:40%;display:none" id="notFoundRoles">No Staff Roles Found..</span>

                                
                        </ul>
                        <ul class="treeview treeview-section-ui rolesUl" *ngIf="optionShow=='partnerroles'" id="partnerroles_dropdown">
                            <!--Listing partner roles here-->
                            <li *ngFor="let roles of staffRolesList" class="roleslisting" id="partnerroles_{{roles.RoleID}}">
                                    
                                    <i class="fa fa-plus  expand-roles-plus-{{roles.RoleID}}" (click)="getStaffByRoleID(roles.RoleID);" id="getpartnerByRoleID"></i>
                                    <i class="fa fa-minus hide expand-roles-minus-{{roles.RoleID}}" (click)="getStaffByRoleID(roles.RoleID);" id="hide_getpartnerByRoleID"></i>
                                    <input type="checkbox" name="middle"  [(ngModel)]="orderMain[roles.RoleID]" (change)="getStaffByRoleID(roles.RoleID,'1');" id="partner_check_{{roles.RoleID}}">
                                    <label for="middle" [ngClass]="(orderMain[roles.RoleID]) ? 'custom-checked' : 'custom-unchecked'">{{roles.RoleName}}</label>
                                    <span *ngIf="(isStaffsloadingTime==true && clickedRoleId==roles.RoleID)" style="right: 10%;margin-left: 67%;
                                    color:#acb7bf;" id="partner_loading">Loading...</span>
                                    <ul class="sub-item-panel hide sub-role-{{roles.RoleID}}">

                                    </ul>
                                   
                                    
                                </li>
                            <!--Accordian ends here..-->
                            <div *ngIf="optionShow=='partnerroles' && !staffRolesList.length && isloadingTime!=true;" style="position: absolute;right:40%;">No Partner Roles Available.</div>
                            <button  class="btn btn-sm btn-default" disabled *ngIf="isloadingTime==true" style="position: absolute;right:40%;">
                                  <span *ngIf="isloadingTime==true" id="partner_roles_loading" >Loading Partner Roles...</span>  
                            </button>
                            <span style="position: absolute;right:40%;display:none" id="notFoundRoles">No Partner Roles Found..</span>

                                
                        </ul>
                        <!--Listing all partner roles-->
                        <ul class="treeview treeview-section-ui" *ngIf="optionShow=='staff'">
                               <li *ngFor="let users of userListChatwith" id="staff_{{users.userid}}" [ngClass]="{'text-muted': users.noContactAvailable}" [chToolTip]="users.noContactAvailable? 'noContactUser': ''" data-animation="false">
                                  <input type="checkbox" name="cliniciansRoleUser-{{users.userid}}" id="role-{{users.userid}}" value="{{users.userid}}" class="cliniciansRoleUser" alt="{{users.userid}}" title="{{users.displayname}}" [(ngModel)]="order[users.userid]" (change)="formated('staff')" [disabled]="users.noContactAvailable">
                                  <label for="{{users.displayname}}" [ngClass]="order[users.userid] ? 'custom-checked' : 'custom-unchecked'">{{users.displayname}} </label><span *ngIf="users.naTagNames && users.naTagNames != ''">({{users.naTagNames}}) </span>
                                </li>
                               
                                    
                                    
                                    <div *ngIf="isloadingTime==false" style="position: absolute;right:40%;"><div *ngIf="initialLoadingListCount==0">No Clinicians Available <span  *ngIf="srch.value"><span style="color: #0088ff;cursor: pointer;" (click)="getUsersOnInviteToChat('','',tenantIDForInviteList,optionShow, true)" id="reset_search_process"> Click Here </span> to Reset the Search.</span></div></div>
                                    <div style="text-align: center;width:100%;margin-top: 14px;float: left;">
                                        <button type="button" [hidden]="( loadMoreStatus!=true ||  !userListChatwith.length)" class="btn btn-sm btn-default" (click)="getUsersOnInviteToChat(true,srch.value?srch.value:'',tenantIDForInviteList,optionShow)" id="loading_more_staff" >{{loadMoreButtonTextInvite}}</button>
                                        <button type="button" *ngIf="isloadingTime!=false" class="btn btn-sm btn-default" id="loading_staff">
                                         <span>Loading Staff....</span> 
                                        </button>
                                        
                                    </div>           
                        </ul>
                        <!--Listing all staffs Ends Here-->
                        <!--Listing all partners-->
                        <ul class="treeview treeview-section-ui" *ngIf="optionShow=='partner'">
                            <li *ngFor="let users of userListChatwith"  id="partner_{{users.userid}}" [ngClass]="{'text-muted': users.noContactAvailable}" [chToolTip]="users.noContactAvailable? 'noContactUser': ''" data-animation="false">
                               <input type="checkbox" [disabled]="users.noContactAvailable" name="cliniciansRoleUser-{{users.userid}}" id="role-{{users.userid}}" value="{{users.userid}}" class="cliniciansRoleUser" alt="{{users.userid}}" title="{{users.displayname}}" [(ngModel)]="order[users.userid]" (change)="formated('partner')">
                               <label for="{{users.displayname}}" [ngClass]="order[users.userid] ? 'custom-checked' : 'custom-unchecked'">{{users.displayname}} </label><span *ngIf="users.naTagNames && users.naTagNames != ''">({{users.naTagNames}}) </span>
                             </li>
                            
                                 
                                 
                                 <div *ngIf="isloadingTime==false" style="position: absolute;right:40%;"><div *ngIf="initialLoadingListCount==0">No Partners Available <span  *ngIf="srch.value"><span style="color: #0088ff;cursor: pointer;" (click)="getUsersOnInviteToChat('','',tenantIDForInviteList,optionShow, true)"> Click Here </span> to Reset the Search.</span></div></div>
                                 <div style="text-align: center;width:100%;margin-top: 14px;float: left;">
                                     <button type="button" [hidden]="( loadMoreStatus!=true ||  !userListChatwith.length)" class="btn btn-sm btn-default" (click)="getUsersOnInviteToChat(true,srch.value?srch.value:'',tenantIDForInviteList,optionShow)">{{loadMoreButtonTextInvite}}</button>
                                     <button type="button" *ngIf="isloadingTime!=false" class="btn btn-sm btn-default" id="loading_partners">
                                      <span>Loading Partners....</span> 
                                     </button>
                                     
                                 </div>           
                     </ul>
                     <ul class="treeview treeview-section-ui" *ngIf="optionShow =='alternatecontact'">
                        <div class="chat-with-empty-data" *ngIf="alernateContactsData.length==0" >No associate contact available.</div>                            
                        <li *ngFor="let users of alernateContactsData" id="contact" [ngClass]="{'text-muted': users.noContactAvailable}" [chToolTip]="users.noContactAvailable? 'noContactUser': ''" data-animation="false">
                           <input type="checkbox" [disabled]="users.noContactAvailable" name="cliniciansRoleAlternate-{{users.userid}}" id="role-{{users.userid}}" value="{{users.userid}}" class="cliniciansRoleUser" alt="{{users.userid}}" title="{{users.firstName}}" [(ngModel)]="order[users.userid]" (change)="formated('alternatecontact')">
                           <label *ngIf="users.roleName && users.roleName == 'Alternate Contact'" for="{{users.firstName}} {{users.lastName}}" [ngClass]="order[users.userid] ? 'custom-checked' : 'custom-unchecked'">{{users.patientFirstName}} {{users.patientLastName}} ({{users.firstName}} {{users.lastName}}{{', ' + users.relation}})</label>
                           <label *ngIf="users.roleName && users.roleName == 'Patient'"  for="{{users.firstName}} {{users.lastName}}" [ngClass]="order[users.userid] ? 'custom-checked' : 'custom-unchecked'">{{users.firstName}} {{users.lastName}} (Patient)</label>
                         </li>
                             
                             
                 </ul>
                     <!--Listing all partners Ends Here-->
                        <div class="col-md-12" *ngIf="optionShow=='patient'">
                            <!--     -->
                            <div class="forwarding-behaviour-container">
                                <div class="forward-model-option-user-list" *ngFor="let user of (userListChatwith| virtualPatientFilter | SearchFilter: userSearch.value);">
                                    <div class="forward-user-role" (click)="inviteToChat(user,null)">
                                        <i class="fa fa-clock-o" [hidden]="!user.isScheduled"></i>
                                        <p *ngIf="!user.caregiver_userid">{{user.displayname}} <span *ngIf="user.naTagNames && user.naTagNames != ''">({{user.naTagNames}}) </span> {{ user.roleId!='3' ? ' [' + user.role + ']' : '' }}{{user.role =='Caregiver' ?  ' (' + user.caregiver_displayname + ')' : '' }}
                                        </p>
                                        <p *ngIf="user.caregiver_userid">{{user.caregiver_displayname}} <span *ngIf="user.naTagNames && user.naTagNames != ''">({{user.naTagNames}}) </span> ({{user.displayname}})
                                        </p>
                                    </div>
                                </div>
                                
                            </div>
                        </div>
                        <div class="col-md-12" *ngIf="optionShow=='patient' && (userListChatwith| virtualPatientFilter | SearchFilter: userSearch.value).length == '0'">No Patients Available</div>
                        <!-- msg groups-->
                        <ul class="treeview treeview-section-ui" *ngIf="optionShow=='msggroups'">
                                <li *ngFor="let cliniciansGroup of memberDataGroupWise" 
                                class="role-{{cliniciansGroup.groupId}}"  id="patient-{{cliniciansGroup.groupId}}" >
                                    <i class="fa fa-plus expand-icon-{{cliniciansGroup.groupId}}" (click)="callAccordion(cliniciansGroup.groupId)"></i>
                                    <input type="checkbox" name="messageGroup" id="role-{{cliniciansGroup.groupId}}" value="{{cliniciansGroup.groupId}}" [(ngModel)]="orderGroupMain[cliniciansGroup.groupId]" (change)="checkboxChangedGroup($event)"/>
                                    <label for="middle" [ngClass]="orderGroupMain[cliniciansGroup.groupId] ? 'custom-checked' : 'custom-unchecked'">{{cliniciansGroup.groupName }}</label>
                                    <span *ngIf="(isGrouploadingTime==true && clickedGroup==cliniciansGroup.groupId)" style="right: 10%;margin-left: 67%; color:#acb7bf;">Loading...</span>                                   
                                    <ul class="sub-item-panel sub-item-panel-{{cliniciansGroup.groupId}}">
                                        <caption *ngIf="cliniciansGroup.userList && cliniciansGroup.userList.length">{{'TITLES.MEMBERS' | translate}}</caption>
                                        <li *ngFor="let cliniciansGroupUser of cliniciansGroup.userList">
                                            <span style="line-height: 30px;padding-left:5px;">{{(cliniciansGroupUser.caregiver_displayname ? cliniciansGroupUser.caregiver_displayname + " (" + cliniciansGroupUser.displayname + ")" : cliniciansGroupUser.displayName)}} 
                                                <span *ngIf="cliniciansGroupUser.naTagNames && cliniciansGroupUser.naTagNames != ''">({{cliniciansGroupUser.naTagNames}}) </span> 
                                                <span *ngIf="userData.tenantId!=cliniciansGroupUser.tenantId">[{{cliniciansGroupUser.tenantName}}]</span>
                                            </span>
                                        </li>
                                    </ul>                                    
                                    <ul class="sub-item-panel sub-item-panel-{{cliniciansGroup.groupId}}">
                                        <caption *ngIf="cliniciansGroup.roleList && cliniciansGroup.roleList.length">{{'TITLES.ROLES' | translate}}</caption> 
                                        <li *ngFor="let item of  cliniciansGroup.roleList">
                                            <span>{{ item.name }}</span>
                                        </li>
                                    </ul>
                                </li> 
                            </ul>
                            <div *ngIf="optionShow=='msggroups'" style="text-align: center;width:100%;margin-top: 14px;float: left;">
                                <div *ngIf="optionShow=='msggroups' && hideoptionShow && (memberDataGroupWise | filterUsersList:userData.userId).length == '0'">No Message Groups Available. <span style="color: #0088ff;cursor: pointer;" *ngIf="userSearch.value" (click)="reset(optionShow)">Click Here to Reset</span></div>
                                <button [disabled]="loadingGroups" type="button" [hidden]="hideLoadMore" class="btn btn-sm btn-default" (click)="loadMoreGroups(optionShow)">
                                        <span *ngIf="!loadingGroups" id="loading_groups" >Load More</span>
                                        <span *ngIf="loadingGroups" id="loading_more_groups">Loading Groups...</span>  
                                </button>
                            </div>
                        <!-- msg groups-->
                        <ul class="col-md-12" *ngIf="optionShow=='groups'">
                            <div class="pt-3 pl-3">
                                <app-pdg-members (selectedItems)="selectedPdgs($event)" [reset]="resetPdgData" [siteIds]="pdgSiteIds" [chatWithFilterTenantId]="chatWithTenantFilter.selectedTenant"></app-pdg-members>
                            </div>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <span _ngcontent-c0="" class="chatwith-modal-tip">
                 <img _ngcontent-c0="" src="./assets/modules/dummy-assets/common/img/chatwith-tip.png">
                 <span _ngcontent-c0="" *ngIf="optionShow=='staffroles' || optionShow=='partnerroles'" class="modal-footer-text">Choose staff role(s) to invite</span>
                 <span _ngcontent-c0="" *ngIf="optionShow=='staff' ||  optionShow=='patient'" class="modal-footer-text">{{((optionShow=='staff') ) ? 'Choose staff role(s)/name(s) to invite' : 'Click patient name to invite' }}</span>
                 <span _ngcontent-c0="" *ngIf="optionShow=='groups' || optionShow=='msggroups'" class="modal-footer-text">Choose Group name(s)/member(s) to invite</span>
                </span>
                <button *ngIf="optionShow=='staff' || optionShow=='partner' || optionShow=='staffroles' || optionShow=='partnerroles' || optionShow=='groups' || optionShow=='msggroups'|| optionShow=='alternatecontact'" type="button" class="btn btn-secondary" id="clear_selected" (click)="clearSelectedRoles((optionShow=='staff') ? 'staff' :(optionShow=='alternatecontact') ? 'alternatecontact' : ((optionShow=='staffroles') ? 'staffroles' : ((optionShow=='partnerroles') ? 'partnerroles' : ((optionShow=='partner') ? 'partner' :  ((optionShow=='msggroups') ? 'msggroups' : 'groups')))))" >Clear All</button>
                <button *ngIf="optionShow=='staff' || optionShow=='staffroles' || optionShow=='partnerroles' || optionShow=='groups' || optionShow=='partner' || optionShow=='msggroups' || optionShow=='alternatecontact'" type="button" class="btn btn-secondary" id="submit_selected"  [disabled]="(!selectedUserRoles.length && !messageGroupSelected)" (click)="submitSelectedRoles((optionShow=='staff') ? 'staff' : ((optionShow=='staffroles') ? 'staffroles' : ((optionShow=='partnerroles') ? 'partnerroles' :(optionShow=='alternatecontact') ? 'alternatecontact' :  ((optionShow=='partner') ? 'partner' : ((optionShow=='msggroups') ? 'msggroups' : 'groups')))))" >Add</button>
                <button type="button" class="btn btn-secondary" (click)="closeInviteModal()" id="close_inviteModal">Close</button>
            </div>
        </div>
    </div>
</div>
<!-- *********************End Section*************************** -->

<div id="loader" *ngIf="messageSettingsLoader" data-wordLoad="Please wait ..."></div>
<div id="stickThis"> 
  <div class="card-header">
    <h4 class="mt-1 mb-1 text-black">
        <label class="chat-header"><strong>{{ pageHeading }}</strong></label>       
        <div class="invite-btn">
            <span class="switch-field" *ngIf="applessMessaging && showAppLessMode">
                <img src="../../../assets/img/inapp-messaging.png" *ngIf="!INAPPButtonShown" id="inapp-message" class="message-toggle" [ngClass]="{ 'disabled': this.receiverIsNotContactable }" (click)="confirmAppLessModeUpdate(1)" >
                <img src="../../../assets/img/appless-messaging.png" *ngIf="INAPPButtonShown" id="appless-message" class="message-toggle" (click)="confirmAppLessModeUpdate(0)" >
            </span>
            <span data-toggle="tooltip" data-placement="top" title="Message Translation" class="message-translation-icon" [class.translate-selected]="toggleTranslation" id="translate_chat" *ngIf="configData.toggle_chat_translation == 1" (click)="translateMessages();"><i class="fa fa-language" ></i></span>
            <button   class="btn btn-default btn-sm btn-rounded" id="fwd_msg" *ngIf="showForwardButton"
            ng-hide="curMessage.messageType=='1'" [disabled]="!inviteEnableButton || !newMessagedetails.length ||messageFetching" (click)="selectModal('Reroute to','')"><i class="fa fa-share" aria-hidden="true"></i> Forward</button>
            <button  class="btn btn-default btn-sm btn-rounded" id="invt" [disabled]="!inviteEnableButton" *ngIf="showInviteButton" (click)="inviteModel('Invite','')"><i class="icmn-user-plus invite-icon"></i> Invite</button>
            <div class="cat__top-bar__item hidden-sm-down chatrrom-group-icon-for-members" *ngIf="chatroomUsersList && ((activeMessage.messageType!='1' && activeMessage.messageType!='2') || (activeMessage.messageType=='2' && activeMessage.createdby == userData.userId))" >
                <div class="dropdown" [class.show]="toggleChatroomUserList" id="div-parent-chatParticipants">
                    <a aria-expanded="false" class="dropdown-toggle" href="javascript: void(0);" id="a-chatParticipants" (click)="toggleChatroomUserList = !toggleChatroomUserList">
                        <i class="fa fa-users" aria-hidden="true"></i>
                        <span *ngIf="chatroomUsersListFull && chatroomUsersListFull.length" class="chat-participants-count">{{chatroomUsersCount}}</span>
                    </a>
                    <ul id="ul-chatParticipants" class="dropdown-menu dropdown-menu-right" [ngClass]="{'chatroom-list-dropdown':chatroomUsersList.length>3}">
                        <div class="cat__top-bar__activity" id="div-chatParticipants">
                           <app-chatroom-users-list id="participant-list-view" *ngIf="chatParticipants && chatParticipants.length" [title]="chatroomUsersListTitle" [items]="chatParticipants" [expanded]="toggleChatroomUserList" (removeUser)="showRemoveConfirmation($event)" [listType]="listType.PARTICIPANTS" [toolTip]="'chatroomParticipants'"></app-chatroom-users-list>
                           <hr>
                           <app-chatroom-users-list id="role-list-view" *ngIf="roleParticipants && roleParticipants.length" [title]="chatroomRolesListTitle" [items]="roleParticipants" [expanded]="toggleChatroomUserList" [listType]="listType.ROLES" (removeRole)="confirmToRemoveRole($event)" [toolTip]="'chatroomRoles'" (restoreUser)="restoreUser($event)"></app-chatroom-users-list>
                        </div>
                    </ul>
                </div>
            </div>
        </div>
    </h4>
</div>

<div class="breadcrumb-wrap chatroom-route" [ngClass]="{'subject-exists': (activeMessage.message_group_id != 0 && activeMessage.title)}">
    <ol class="breadcrumb">
        <li class="breadcrumb-item" id="msg_link"><a [routerLink]="['/inbox']" (click)="linkToInbox()" >Messages</a></li>
        <li class="breadcrumb-item" id="chat_link">Chat</li>
    </ol>
    <div class="chatroom-branch"  *ngIf="activeMessage.siteName && activeMessage.messageCategory === messageCategory.PDG">{{activeMessage.siteName}} </div>
</div> 
<div class="breadcrumb-wrap chatroom-subject" *ngIf="activeMessage.message_group_id != 0 && activeMessage.title">
    <ol class="breadcrumb">
        <li class="msg_grp_title dropdown subjectDropdown chatroon-subject chatroom-subject-section" align="right">
            <div class="row">
            <div class="col-md-11 chatroom-subject-text-container chatroom-subject-text-section" id="chatroom-subject-text-wrapper">
            <span>Subject: {{activeMessage.title}}</span>
            </div>
            <div class="col-md-1 chatroom-subject-text-container chatroom-subject-textField" id="chatroom-subject-edit-button-wrapper">
            <a  class="dropdown-toggle edit-messagegroup-title" *ngIf="userDetails.group != 3" data-toggle="dropdown"><i class="icmn-pencil"></i></a>
            <ul class="dropdown-menu mega-dropdown-menu-sub">
                <li>
                    <div>                 
                        <form action="#" [formGroup]="msgSubEdit" (ngSubmit)="updateChatSubject(f)" #f="ngForm">
                                <div class="form-body"> 
                                    <div class="form-group row">                                    
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <input formControlName="chatsubName" type="text" id="chatsubName" [(ngModel)]="chatsubName" class="form-control" placeholder="Subject" value="">
                                            <div *ngIf="msgSubEdit.controls['chatsubName'].errors&&(msgSubEdit.controls.chatsubName?.dirty ||msgSubEdit.controls.chatsubName?.touched || f.submitted)" class="alert alert-danger"> 
                                                    Subject cannot be empty
                                                </div>
                                            </div>
                                        </div> 
                                    </div> 
                                </div>
                                <div class="form-actions actionSubEdit">
                                        <button type="submit" class="btn btn-primary" id="updateChatsubject">Update</button>
                                        <button type="button" data-toggle="dropdown" class="btn btn-default actionSubCancel" id="cancelChatsubject" >Cancel</button>
                                </div> 
                        </form>
                    </div>
                </li>
            </ul>
            </div>
            </div>
        </li>

    </ol>
</div>
</div>

<section class="card chatroom-container-data-list  chatroom-section " [ngClass]="{'cat__core__card-with-sidebar cat__core__card-with-sidebar--large': (this.enableChatWindowSideBar == 1)}">
    <!-- Inbox Data Listing -->
     <div class="cat__core__card-sidebar " *ngIf="this.enableChatWindowSideBar == 1">
        <div class="cat__apps__messaging__header searchbar chatroom-searcbar">
            <input class="form-control cat__apps__messaging__header__input" (keydown)="backSpaceKey($event)" id="SearchTxt" placeholder="Searching..."
                #search>
            <i class="icmn-loader" *ngIf="searchFlag"><img src="./assets/img/loader/color.gif" class="menu-spinner"></i>
            <span _ngcontent-c2="" class=" searchtip" data-placement="top" data-toggle="tooltip" title="" data-original-title="Search"><i class="btnicn icmn-search"  id="search_docs_msgs" (click)="searchMessagesAndDocs()"></i></span>

            <span _ngcontent-c2="" class=" resettip" data-placement="top" data-toggle="tooltip" title=""   data-original-title="Reset">
            <i class="reset icmn-close" (click)="resetSearch()" id="search_docs_reset"></i>   </span>             
        </div>
        <div class="cat__apps__messaging__list inbox-data-container chatroom-messages-with-sublist">
            <div class="no-data" *ngIf="beforeLoad && !(inboxData.length)">No matching items found</div>               

            <div class="overlay-hidden-scroll"></div>
            <div class="cat__apps__messaging__tab row-selection chatroom-msg-tab tab-right-border" *ngFor="let message of (inboxData | sortBy : 'messageOrder' |  userPersistantMessageFilterFn);let i=index; trackBy: trackByForMessageInboxList"
                [class.cat__apps__messaging__tab--selected]="+message.chatroomId === +selectedRow || +message.chatroomId === +currentChatroomId || +message.baseChatroomId === +currentChatroomId || (subThreadSelected && +subThreadSelected.baseId === +message.chatroomId)">
                <div *ngIf="message" id="reload_chat_room" (click)="reloadChatRoom($event, message, i)">

                    <div class="cat__apps__messaging__tab__avatar avatar-position" *ngIf="message.messageCategory === messageCategory.GENERAL; else otherMessages"
                       id="pfle_{{message.chatroomId}}">
                        <a class="cat__core__avatar cat__core__avatar--50" href="javascript:void(0);">
                            <img [src]="message.chatAvatar === chatAvatarOptions.CLINICIAN || message.chatAvatar === chatAvatarOptions.PATIENT 
                            ? defaultPics.PROFILE : message.chatAvatar" 
                            outOfOfficeStatus [oooInfo]="message.oooInfo" 
                            [customClass]="'chatroom-inbox-list'"
                            (error)="this.src='assets/img/file-404.png'"
                            alt="Avatar">                       
                        </a>
                    </div>
                    <ng-template #otherMessages>
                        <div class="float-left cat__core__avatar cat__core__avatar--50" *ngIf="message.messageCategory === messageCategory.MASKED" 
                        (click)="message.maskedSubCount? expandSubMessageList($event, message, i) : reloadChatRoom($event, message, i)">
                            <img [src]="defaultPics.MASKED" draggable="false" alt="Masked">
                        </div>
                        <div class="float-left cat__core__avatar cat__core__avatar--50" *ngIf="message.messageCategory === messageCategory.MESSAGE_GROUP || message.messageCategory === messageCategory.PDG || message.messageCategory === messageCategory.BROADCAST">
                            <img [src]="defaultMsgPics[message.messageCategory]" draggable="false">
                        </div>
                    </ng-template>
                    <div class="cat__apps__messaging__tab__content inbox-cont-row" (click)="message.maskedSubCount? expandSubMessageList($event, message, i) : reloadChatRoom($event, message, i)">
                        <ng-container *ngTemplateOutlet="showMessagePriorityForChatThread; context: { message: message }"></ng-container>
                        <small class="cat__apps__messaging__tab__time chatroom-flag-tab thread-flag item-{{message.chatroomId}}" *ngIf="configData.enable_message_flagging == '1' && !message.isPatientInitatedChat">
                                <ng-container>                                                                
                                    <span chToolTip="flagChatT" data-animation="false" class="fa-stack flag-main-container dropdown-toggle" [ngClass]="{'high':(message.chatroomFlag === 3), 'medium':(message.chatroomFlag === 2), 'low':(message.chatroomFlag === 1)}" id="{{'span-dropdown-'+message.chatroomId}}" data-toggle="collapse"  aria-expanded="false">
                                        <span class="fa fa-stack-2x" [ngClass]="{'fa-flag':(message.chatroomFlag !== 0), 'fa-flag-o':(message.chatroomFlag === 0)}">
                                        </span>
                                    </span>
                                </ng-container >
                        </small>
                        <small class="cat__apps__messaging__tab__time chatroom-flag-tab-msg msg-flag item-{{message.chatroomId}}" *ngIf="configData.enable_message_flagging == '1' && !message.isPatientInitatedChat && message.msg_flag_data_id && message.messageFlag != 0">
                            <ng-container >                                                                
                                <span chToolTip="flagInboxM" data-animation="false" class="fa-stack flag-main-container dropdown-toggle" [ngClass]="{'high':(message.messageFlag === 3), 'medium':(message.messageFlag === 2), 'low':(message.messageFlag === 1)}" id="{{'span-dropdown-'+message.chatroomId}}">
                                    <span class="fa fa-stack-2x" [ngClass]="{'fa-flag':(message.messageFlag !== 0), 'fa-flag-o':(message.messageFlag === 0)}">
                                    </span>
                                </span>
                            </ng-container >
                        </small>
                        <small class="cat__apps__messaging__tab__time">
                            {{(+message.messageStatus === 1? (message.messageOrder)*1000 : formatMessageDeletedTime(message.messageDeletedTime)) | dateshortyearFilter}}
                        </small>
                        <small class="cht-unread-count-box">
                            <i class="unread-icon">
                                <span class="unread-count" *ngIf="message.unreadCount || message.maskedUnreadCount">{{message.maskedUnreadCount || message.messageUnreadCount}}</span>
                            </i>
                        </small>
                        <div [ngClass]="{'has-unread': message.hasUnreadMessages || message.maskedUnreadCount }">
                            <span *ngIf="message.messageCategory === messageCategory.GENERAL || message.messageCategory === messageCategory.BROADCAST; else groupThreads">{{message.chatHeading + (message.chatSubHeading? ', '+ message.chatSubHeading : '') }}</span>
                            <ng-template #groupThreads>
                                <div class="chatroom-msg-title">{{message.chatHeading + (message.chatSubHeading? ','+ message.chatSubHeading : '') }}</div>
                            </ng-template>
                        </div>                       
                        <div class="from-name"  *ngIf="configData.default_patients_workflow != 'alternate_contacts'  && message.message_group_id == 0 && message.messageType != 1 && message.messageType != 2 && (message.messageType == 0 && message.baseId == 0)" [ngSwitch]="userId==message.userid"
                            [ngClass]="{'has-unread':message.hasUnreadMessages || message.maskedUnreadCount>0}">
                            <div *ngSwitchCase="true">
                                <span *ngIf="(!message.patient_caregiver_displayname && userData.group != 3) || (userData.group == 3 && !message.chatWith_caregiver_displayname)">
                                    {{ (userData.group != 3 && message.patient_chatWith) ? message.patient_chatWith : message.chatWith}}, {{ (userData.group != 3 &&message.patient_chatWith) ? "Patient" : message.chatWithRole}}</span>
                                <span *ngIf="(message.patient_caregiver_displayname && userData.group != 3) || (userData.group == 3 && message.chatWith_caregiver_displayname)">{{userData.group != 3 && (message.patient_caregiver_displayname && message.patient_caregiver_displayname != '') ?message.patient_caregiver_displayname : message.chatWith_caregiver_displayname}} ({{ (userData.group != 3 && message.patient_chatWith) ? message.patient_chatWith : message.chatWith}}, {{ 'Caregiver'}})</span>
                                <img *ngIf="message.chatWithStatus==2" class="icon-pending" src="{{iconPath}}/pending-icon.png" alt="pending">
                            </div>
                            <div *ngSwitchCase="false">
                                <span *ngIf="(!message.patient_caregiver_displayname && userData.group != 3) || (userData.group == 3 && !message.caregiver_displayname)">
                                    {{ (userData.group != 3 && message.patient_chatWith)
                                        ? message.patient_chatWith : message.fromName}}, {{ (userData.group != 3 && message.patient_chatWith) ? "Patient" : message.role}}</span>
                                <span *ngIf="(message.patient_caregiver_displayname && userData.group != 3) || (userData.group == 3 && message.caregiver_displayname)">
                                     {{(userData.group != 3 && message.patient_caregiver_displayname && message.patient_caregiver_displayname != '') ?message.patient_caregiver_displayname
                                    : message.caregiver_displayname}} ({{ (userData.group != 3 && message.patient_chatWith) ? message.patient_chatWith : message.fromName}}, {{'Caregiver'}})</span>
                                <img *ngIf="message.fromStatus==2" class="icon-pending" src="{{iconPath}}/pending-icon.png" alt="pending">
                            </div>
                        </div>
                        <div class="from-name"  *ngIf="configData.default_patients_workflow == 'alternate_contacts'  && message.message_group_id == 0 && message.messageType != 1 && message.messageType != 2 && (message.messageType == 0 && message.baseId == 0)" [ngSwitch]="userId==message.userid"
                            [ngClass]="{'has-unread':message.hasUnreadMessages || message.maskedUnreadCount>0}">
                            <div *ngSwitchCase="true">
                                <span *ngIf="(!message.patient_caregiver_displayname && userData.group != 3) || (userData.group == 3 && !message.chatWith_caregiver_displayname)">
                                    {{ (userData.group != 3 && message.patient_chatWith) ? message.patient_chatWith : message.chatWith}}, {{ (userData.group != 3 &&message.patient_chatWith) ? "Patient" : message.chatWithRole}}</span>
                                <span *ngIf="(message.patient_caregiver_displayname && userData.group != 3) || (userData.group == 3 && message.chatWith_caregiver_displayname)">{{userData.group != 3 && (message.patient_caregiver_displayname && message.patient_caregiver_displayname != '') ?message.patient_caregiver_displayname : message.chatWith_caregiver_displayname}} ({{ (userData.group != 3 && message.patient_chatWith) ? message.patient_chatWith : message.chatWith}}, {{message.patient_chatWith_relation}})</span>
                                <img *ngIf="message.chatWithStatus==2" class="icon-pending" src="{{iconPath}}/pending-icon.png" alt="pending">
                            </div>
                            <div *ngSwitchCase="false">
                                <span *ngIf="(!message.patient_caregiver_displayname && userData.group != 3) || (userData.group == 3 && !message.caregiver_displayname)">
                                    {{ (userData.group != 3 && message.patient_chatWith)
                                        ? message.patient_chatWith : message.fromName}}, {{ (userData.group != 3 && message.patient_chatWith) ? "Patient" : message.role}}</span>
                                <span *ngIf="(message.patient_caregiver_displayname && userData.group != 3) || (userData.group == 3 && message.caregiver_displayname)">
                                     {{(userData.group != 3 && message.patient_caregiver_displayname && message.patient_caregiver_displayname != '') ?message.patient_caregiver_displayname
                                    : message.caregiver_displayname}} ({{ (userData.group != 3 && message.patient_chatWith) ? message.patient_chatWith : message.fromName}}, {{message.patient_chatWith_relation}})</span>
                                <img *ngIf="message.fromStatus==2" class="icon-pending" src="{{iconPath}}/pending-icon.png" alt="pending">
                            </div>
                        </div>
                        <div class="rerouted-by" *ngIf="message.messageForwarded">
                            <span class="rerouted-by-span">({{message.messageForwarded}})</span>
                        </div>
                        <div (click)="message.maskedSubCount  && isSubListVisible && message.chatroomId==chatroomSelected && reloadChatRoom($event, message, i)"
                            [ngClass]="{'has-unread':message.hasUnreadMessages || message.maskedUnreadCount}" class="cat__apps__messaging__tab__text chatroom-message-min-list">
                            <span *ngIf="message.maskedSubCount  && userData.group!=3 " [hidden]="isSubListVisible && message.chatroomId === +chatroomSelected" class="sublist-count">({{ message.maskedSubCount }})</span>
                
                            <span *ngIf="message.maskedSubCount" class="arrow-sub-mesage" (click)="expandSubMessageList($event, message, i); $event.stopPropagation()">
                                <i class="fa fa-2x fa-caret-right " [ngClass]="{'message-box-list-sublist-icon':isSubListVisible && message.chatroomId === +chatroomSelected}"></i>
                            </span>
                            <div class="cat__apps__messaging__tab__text" *ngIf="(message.messageCategory === messageCategory.MESSAGE_GROUP || message.messageCategory === messageCategory.PDG ) && message.chatSubject"><span class="msg_grp_title">Subject: {{ message.chatSubject }}</span></div>
                            <div class="cat__apps__messaging__tab__text" *ngIf="message.messageCategory === messageCategory.PDG && message.siteName">
                                <span class="msg_grp_title">{{ labelSite | translate }}: {{message.siteName}}</span></div>
                            <div *ngIf="userData.isAlternateContact">
                                <span *ngIf="message.patientInfo">{{'LABELS.PATIENT' | translate}}:&nbsp;{{message.patientInfo.patientFirstName}}&nbsp;{{message.patientInfo.patientLastName}}</span>       
                            </div>    
                            <div class="cat__apps__messaging__tab__text" *ngIf="message.messageCategory !== messageCategory.PDG">
                                <span [ngClass]="{'chatroom-masked-mainthread': message.chatroomId === +currentChatroomId,'chatroom-masked-mainthread-selected': message.chatroomId==currentChatroomId && message.maskedSubCount }" *ngIf="userDetails.group != 3 && message.isSelfMessage">{{ message.isSelfMessage ?'Me: ': ''}}</span>
                                <span *ngIf="userDetails.group == 3 &&(message.messageType === messageType.MESSAGE_GROUP || message.isSelfMessage)">
                                Me:</span>                                   
                            <span [ngClass]="{'chatroom-masked-mainthread': message.chatroomId === +currentChatroomId,'chatroom-masked-mainthread-selected': message.chatroomId === +currentChatroomId && message.maskedSubCount, 'font-italic': +message.messageStatus === 0 }" inboxCompile="{{message.message}}"></span>
                            </div>
                        </div>
                        <cat-tags [tags]="message.allTags" [maxAllowedTags]="1" [hideDelete]="true" [shorter]="true" *ngIf="isUserStaff || isUserPartner"></cat-tags>
                        <ng-container *ngIf="message.maskedSubCount">
                            <i class="loader-inner-small" *ngIf="isSubListFetching && message.chatroomId === +chatroomSelected"><img src="./assets/img/loader/color.gif"></i>
                            <ng-container *ngIf="isSubListVisible && !isSubListFetching && message.chatroomId === +chatroomSelected">
                                <div class="message-sublist-open chat-room-sublist message-sublist_{{subListData.chatroomid}}" *ngFor="let subListData of message.subList; let j=index" id="sublist_{{subListData.chatroomId}}" [ngClass]="{'sublist-selected': +subListData.chatroomid === +activeMessage.chatroomId}">
                                    <hr>
                                    <div class="message-sublist-details" (click)="reloadSubChatRoom($event, subListData, j); $event.stopPropagation();">
                                        <small class="sublist-unread-count-box sublist_count_{{message.chatroomId}}"  *ngIf="subListData.unreadCount!='0'">
                                            <i class="unread-icon">
                                                <span class="sublist-unread-count" *ngIf="subListData.unreadCount!='0'">{{subListData.unreadCount}}</span>
                                            </i>
                                        </small>
                                        <span class="message-sublist-name">{{userDetails.userId==subListData.userid?'Me':subListData.fromName}}:&nbsp;</span>
                                        <span class="message-sublist-description" [ngClass]="{'font-italic': +subListData.messageStatus === 0}" inboxCompile="{{subListData.message}}"></span>
                                    </div>
                                    <div class="message-sublist-date">{{(+subListData.messageStatus === 1? subListData.sent*1000: formatMessageDeletedTime(subListData.messageDeletedTime)) | dateshortyearFilter}}</div>
                                    <ng-container *ngTemplateOutlet="showMessagePrioritySubList; context: { message: subListData }"></ng-container>
                                </div>
                            </ng-container>
                        </ng-container>
                    </div>
                </div>
                <ng-template #showMessagePrioritySubList let-message="message">
                    <div class="priority-inbox-icon">
                        <ng-container *ngIf="(message.maskedUnreadCount>0 || message.unreadCount!='0') && message.unReadPriority != MESSAGE_PRIORITY.NORMAL && message.unReadPriority !== '0'; else readPriorityMsg">
                            <ng-container *ngTemplateOutlet="msgPriorityIcon; context: { priority: message.unReadPriority, class: message.unReadPriority == MESSAGE_PRIORITY.HIGH ? 'alert-fill bg-danger' : 'arrowdown-fill bg-primary' }"></ng-container>
                        </ng-container>
                        <ng-template #readPriorityMsg>
                            <ng-container *ngTemplateOutlet="msgPriorityIcon; context: { priority: message.readPriority, class: message.readPriority == MESSAGE_PRIORITY.HIGH ? 'alert-outline bg-danger' : 'arrowdown-outline bg-primary' }"></ng-container>
                        </ng-template>
                        <ng-template #msgPriorityIcon let-priority="priority" let-class="class">
                            <small *ngIf="priority != MESSAGE_PRIORITY.NORMAL && priority != 0"><i [ngClass]="class"></i></small>
                        </ng-template>
                        <small *ngIf="message.unReadMention === CHECKED.TRUE"><i class="at-fill bg-danger"></i></small>
                        <small *ngIf="message.unReadMention === CHECKED.FALSE && message.readMention === CHECKED.TRUE"><i class="at-outline bg-danger"></i></small>
                    </div>
                </ng-template>
                <ng-template #showMessagePriorityForChatThread let-message="message">
                    <div class="priority-inbox-icon">
                        <ng-container *ngIf="message.messageUnreadCount && +message.messagePriorityUnread !== MESSAGE_PRIORITY.NORMAL && +message.messagePriorityUnread !== 0; else readPriorityMsgForChatThread">
                            <ng-container *ngTemplateOutlet="msgPriorityIcon; context: { priority: message.messagePriorityUnread, class: message.messagePriorityUnread == MESSAGE_PRIORITY.HIGH ? 'alert-fill bg-danger' : 'arrowdown-fill bg-primary' }"></ng-container>
                        </ng-container>
                        <ng-template #readPriorityMsgForChatThread>
                            <ng-container *ngTemplateOutlet="msgPriorityIcon; context: { priority: message.messagePriorityRead, class: message.messagePriorityRead == MESSAGE_PRIORITY.HIGH ? 'alert-outline bg-danger' : 'arrowdown-outline bg-primary' }"></ng-container>
                        </ng-template>
                        <ng-template #msgPriorityIcon let-priority="priority" let-class="class">
                            <small *ngIf="priority && +priority !== MESSAGE_PRIORITY.NORMAL && +priority !== 0"><i [ngClass]="class"></i></small>
                        </ng-template>
                        <small *ngIf="+message.messageMentionUnread === +CHECKED.TRUE"><i class="at-fill bg-danger"></i></small>
                        <small *ngIf="+message.messageMentionUnread === +CHECKED.FALSE && +message.messageMentionRead === +CHECKED.TRUE"><i class="at-outline bg-danger"></i></small>
                    </div>
                </ng-template>
            </div>
            <div class="cat__apps__messaging__tab row-selection chatroom-msg-tab" *ngFor="let message of CSRMessages;let i=index">
                <div>
                    <div class="cat__apps__messaging__tab__avatar">
                        <a class="cat__core__avatar cat__core__avatar--50" href="javascript:void(0);">
                            <img  src="{{message.fromAvatar}}" >
                        </a>
                    </div>
                    <div class="cat__apps__messaging__tab__content inbox-cont-row">
                        <small class="cat__apps__messaging__tab__time">{{message.sent*1000 | dateshortyearFilter}}</small>
                        <small class="unread-count-box">
                        <i class="unread-icon">
                            <span class="unread-count" >1</span>
                        </i>
                        </small>
                        <div class="cat__apps__messaging__tab__name" [ngClass]="{'has-unread':message.hasUnreadMessages || message.maskedUnreadCount>0}">
                            <div>{{message.fromName}}, {{message.role}}</div>
                        </div>
                        <div [ngClass]="{'has-unread':message.hasUnreadMessages || message.maskedUnreadCount>0}" class="cat__apps__messaging__tab__text" inboxCompile="{{message.message}}"></div>
                    </div>
                </div>
            </div>
            <div *ngIf="messageLoader.messages" class="messageLoads loading-container">
                <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div></div>
                <div class="loading-text">Loading Messages...</div>
            </div>
        </div>
        <div *ngIf="totalCount" [hidden]="totalCount && totalCount == 0 && currentPage == 1" class="chatroom-pagination">
            <ngb-pagination 
            [collectionSize]="totalCount" 
            [pageSize]="contentLimit" 
            [page]="currentPage" 
            [maxSize]="2" 
            [rotate]="true" 
            [boundaryLinks]="false" 
            (pageChange)="loadPage($event)">
            </ngb-pagination>
    </div>
    </div> 
    <!-- Inbox Data Listing -->
    <div class="card-block">
        <div class="col-lg-12">
            <app-messages-filter [selectedTenantId]="activeMessage.selectedTenantId ? activeMessage.selectedTenantId : userData.tenantId" [resetAdvanceSearchForm]="resetAdvanceSearch" (advanceSearch)="filterChatRoomMessages($event)"></app-messages-filter>
        </div> 
        <div class="filter-block d-flex px-2 justify-content-between align-items-center"> 
            <div class="filter-message chatroom-filter-message message-load-dd" [hidden]="userData.group == 3" >
                <span class="show-msg"><b>Show Messages</b></span>
                <div class="large-tooltip show-msg-tooltip"><i class="show-filter icmn-info"></i></div>
                <span class="user-select">
                    <select id="messages-to-show" aria-controls="message-worklist" class="form-control input-sm input-show" [(ngModel)]="messageFilter" (change)="filterMessages()">
                        <option value="all" id="filter_all">All</option>
                        <option value="1" id="filter_user" >User</option>
                        <option value="0" id="filter_system" >System</option>
                    </select>
                </span>
                <app-messages-quick-filter (quickSearch)="applyQuickSearch($event)" [resetQuickFilter]="resetQuickSearch"></app-messages-quick-filter>
            </div>
            <div class="chatroom-search-message"> 
                <div class="chat-with-wrapper-search">
                    <input (keydown)="searchOnEnter($event)" autocomplete="off" class="message-search-input form-control cat__apps__messaging__header__input ng-untouched ng-pristine ng-valid" id="chat-with-modal-search-box" placeholder="Search..." #userSearchFromChat>
                    <span class="message-search-actions">
                        <button [disabled]="userSearchFromChat.value.trim() == ''" type="button" class="message-search-button btn btn-sm btn-primary" id="search_btn" title="Search" (click)="searchReroute()">Search</button>
                        <button type="button" class="message-search-reset btn btn-sm btn-default" title="Reset" (click)="resetChatMessageSearch()" id="reset_btn">Reset</button>
                        <button class="btn btn-sm btn-default reset-btn" [class.bg-primary]="chatroomFilterApplied === FILTER.ADVANCE" id="advance-search-button" style="margin-left:10px;" (click)="showAdvanceSearch()">
                            <img src="./assets/img/filter.png" data-toggle="tooltip" id="advance-search-button" data-placement="top" title="{{ 'TOOLTIPS.ADVANCE_SEARCH' | translate }}" class="adv-search" style="height: 18px;">
                        </button>
                    </span>
                </div>
            </div>
        </div>
        <div class="readmore-messages chatroom-messages" *ngIf="loadMoreButtonText">
            <button class="align-center oldMessage" [ngClass]="{'loadMoreHandIcon':!newMessagedetails.length ||messageFetching}"  [disabled]="!newMessagedetails.length ||messageFetching" (click)="loadMore()" id="load_more_btn">{{loadMoreButtonText}}</button>
        </div>
        <div class="cat__apps__messaging chatroom-message-listing chatroom-time-listing">
            <div class="custom-scroll cat__core__scrollable" id="chat-window-scroll">
                <div class="cat__apps__chat-block">
                    <div class="cat__apps__chat-block__item message-item clearfix" *ngFor="let mDetails of newMessagedetails;let i=index" [ngClass]="{'cat__apps__chat-block__item--right': mDetails.name!='Me','citus-notification':mDetails.name=='Administrator'}" [hidden]="!((userData.group !== '3' || mDetails.name!='Administrator') && ((mDetails.userid == 0 && messageFilter !=1) || (mDetails.userid != 0 && messageFilter !=0)) )">
                        <div class="cat__apps__chat-block__avatar avatar-position">
                            <a class="cat__core__avatar cat__core__avatar--50" href="javascript:void(0);">
                            <img src="{{mDetails.avatar}}" alt="Alternative text to the image" onerror="this.src='assets/img/file-404.png'" outOfOfficeStatus [oooInfo]="getOooInfo(mDetails.userid)">
                        </a>
                        </div>
                        <h5 *ngIf="mDetails.messageStatus === 1 && mDetails.priorityId !== MESSAGE_PRIORITY.NORMAL" class="priority-msg-icon priority-position"><i *ngIf="mDetails.priorityId === MESSAGE_PRIORITY.HIGH" class="alert-fill bg-danger"></i><i *ngIf="mDetails.priorityId === MESSAGE_PRIORITY.LOW" class="arrowdown-fill bg-primary"></i></h5>
                        <h5 *ngIf="mDetails.priorityId === MESSAGE_PRIORITY.NORMAL" class="priority-msg-icon priority-position"><i *ngIf="mDetails.showMention" class="at-fill bg-danger" aria-hidden="true"></i></h5>
                        <div class="chat-signature" [ngClass]="{'other_user_tag': mDetails.userid !=userData.userId} "   >
                            <div *ngIf="mDetails.insertionStatus != 1 && mDetails.name=='Me' && mDetails.name !='Administrator'" (click)="showMessageInfo('sender',userData.userId,mDetails)" >
                                <span data-toggle="tooltip" data-placement="top" title="" data-original-title="Message Info"  class="message-info-icon">  </span> 
                             </div>
                            <div *ngIf="mDetails.name!='Me' && mDetails.name !='Administrator'" (click)="showMessageInfo('recipient',i,mDetails)">      
                                <span  data-toggle="tooltip" data-placement="top" title="" data-original-title="Message Info" class="message-info-icon"> </span> 
                            </div>
                            <div *ngIf="mDetails.messageType !== messageType.BROADCAST && mDetails.insertionStatus != 1 && mDetails.name !== 'Administrator' && !this.receiverIsNotContactable && mDetails.messageStatus === 1" class="reply_message_div" id="message-div-{{mDetails.id}}" [attr.data-details]="mDetails|json" (click)="replyToMessage(mDetails)">
                                <span data-toggle="tooltip" id="reply_{{mDetails.id}}" data-placement="top" title="" [attr.data-original-title]="'TITLES.REPLY_MESSAGE' | translate"  class="reply-message-icon"></span>
                            </div>
                            <div *ngIf="mDetails.messageStatus === 1 && mDetails.insertionStatus != 1 &&!tagMultipleMessageOption && !mDetails.sign && mDetails.owner && privileges.indexOf('SignMessage')!==-1 && configData.sign_message_button!='popup_only'" id="open_chat_sign" (click)="openChatSignature(mDetails)">
                                <span data-toggle="tooltip" id="sign_block_{{mDetails.id}}" data-placement="top" title="" data-original-title="Sign Message"  class="sign-message-icon"></span>
                            </div>
                            <div class="tag_message_div" id="message-div-{{mDetails.id}}" [attr.data-details]="mDetails|json" *ngIf="mDetails.insertionStatus!=1 &&!tagMultipleMessageOption && privileges.indexOf('TagMessage')!==-1 && configData.show_form_features && configData.show_form_features!=='0' && mDetails.name !='Administrator' && mDetails.messageStatus === 1">
                                <span id="message-icon-{{mDetails.id}}" [attr.data-details]="mDetails|json"  data-toggle="tooltip" data-placement="top" title="" data-original-title="Tag Message"  class="tag-message-icon"></span>
                            </div>
                            <div class="trash_message_div" id="message-div-{{mDetails.id}}" [attr.data-details]="mDetails|json" *ngIf="mDetails.userid == userData.userId && mDetails.messageStatus === 1"  (click)="mDetails.getitems && mDetails.getitems.length > 0 || mDetails.tagedItems.length > 0 ? null :  deleteUndoMessage(mDetails,i,'DELETE')">
                            <span  id="message-icon-{{mDetails.id}}" [attr.data-details]="mDetails|json" data-toggle="tooltip" data-placement="top" [attr.title]=" (mDetails.getitems && mDetails.getitems.length > 0) || (mDetails.tagedItems.length > 0) ? ('TITLES.TAGGED_MESSAGE_CANNOT_BE_DELETED' | translate) : ('TITLES.DELETE_MESSAGE' | translate)" [ngClass]="{'disabled-icon': mDetails.getitems && mDetails.getitems.length > 0 || mDetails.tagedItems.length > 0 }" class="fa fa-trash fa-2x delete-msg-icon"></span> 
                            </div> 
                            <div class="round" *ngIf="mDetails.insertionStatus!=1 && tagMultipleMessageOption && privileges.indexOf('TagMessage')!==-1 && configData.show_form_features && configData.show_form_features!=='0' && mDetails.name !='Administrator' && mDetails.messageStatus === 1">
                                <input type="checkbox" class="tag-checkbox"  id="tag-checkbox-{{mDetails.id}}" (click)="tagMultipleButtonClick($event, mDetails)">
                                <label for="tag-checkbox-{{mDetails.id}}"></label>
                            </div>
                        </div>
                        <small class="cat__apps__messaging__tab__time flag-tab thread-flag item-{{mDetails.id}}" *ngIf="mDetails.messageStatus === 1 && configData.enable_message_flagging == '1' && activeMessage.createdbyGrp != 3 && mDetails.name != 'Administrator'">
                            <ng-container >
                                <span chToolTip="flagChatM" data-animation="false" class="fa-stack flag-main-container flag-icon dropdown-toggle" [ngClass]="{'high':(mDetails.msg_flag == '3'), 'medium':(mDetails.msg_flag == '2'), 'low':(mDetails.msg_flag == '1')}" id="{{'span-dropdown-'+mDetails.id}}" data-toggle="collapse"  aria-expanded="false" (click)="expandFlagOPtions(mDetails)">
                                    <span class="fa fa-stack-2x" [ngClass]="{'fa-flag':(mDetails.msg_flag != '0'), 'fa-flag-o':(mDetails.msg_flag == '0')}">
                                    </span>
                                </span>
                                <ul id="{{'dropdown-item-'+mDetails.id}}" class="collapse dropdown-menu flag-option">
                                    <form name="masgFlagForm">
                                        <li class="dropdown-item">
                                            <label>
                                                <input id="msg_flag_high" name="msgFlagGrp" type="radio" [(ngModel)]="mDetails.msg_flag" value="3" (change)="onthreadFlagChange(mDetails)"> High
                                            </label>
                                        </li>
                                        <li class="dropdown-item">
                                            <label>
                                                <input id="msg_flag_med"  name="msgFlagGrp" type="radio" [(ngModel)]="mDetails.msg_flag" value="2" (change)="onthreadFlagChange(mDetails)"> Medium
                                            </label>
                                        </li>
                                        <li class="dropdown-item">
                                            <label>
                                                <input  id="msg_flag_low"  name="msgFlagGrp" type="radio" [(ngModel)]="mDetails.msg_flag" value="1" (change)="onthreadFlagChange(mDetails)"> Low
                                            </label>
                                        </li>
                                        <li class="dropdown-item">
                                            <label>
                                                <input  id="msg_flag_clear" name="msgFlagGrp" type="radio" [(ngModel)]="mDetails.msg_flag" value="0" (change)="onthreadFlagChange(mDetails)"> Clear Flag
                                            </label>
                                        </li>
                                    </form>
                                </ul>
                            </ng-container >
                        </small>

                        <div class="cat__apps__chat-block__content message-content message-content-{{mDetails.id}}" >
                            <small class="cat__apps__messaging__tab__time">{{mDetails.time*1000 | dateshortyearFilter}}</small>
                            <div class="cat__apps__messaging__tab__name">{{ mDetails.name }}&nbsp;<span *ngIf="mDetails.messageStatus === 1 && mDetails.priorityId !== MESSAGE_PRIORITY.NORMAL" [ngClass]="mDetails.priorityId === MESSAGE_PRIORITY.HIGH ? 'badge badge-danger' : 'badge badge-primary'">{{ 'PRIORITIES.' + (MessagePriorityData | filter:mDetails.priorityId:'key')[0]?.value | translate }}</span></div>
                            <span class="msg-sending-status" chToolTip="MessagesendingInterrupted" data-animation="false" data-placement="left" *ngIf="mDetails.insertionStatus == 1 && mDetails.failed==false"> <i class="fa fa-clock-o message-pending" ></i>&nbsp;Sending..</span>
                            <span class="msg-sending-status" chToolTip="MessagesendingFailed" data-animation="false" data-placement="left" *ngIf="mDetails.insertionStatus == 1 && mDetails.failed==true">Sending Failed!</span>
                            <span class="msg-sent-status" *ngIf="mDetails.insertionStatus != 1 && mDetails.time > lastUserActivityTime && mDetails.class=='self'"> Sent</span>
                            <span class="msg-sent-status" *ngIf="mDetails.time <= lastUserActivityTime && mDetails.name!='Administrator' && mDetails.class=='self' && !mDetails.readUsers.length"> Sent</span>
                            <span class="msg-read-status" *ngIf="mDetails.time <= lastUserActivityTime && mDetails.name!='Administrator' && mDetails.class=='self' && mDetails.readUsers.length" id="show_read_users" (click)="show_read_users(i,mDetails)">
                                Read
                                <div class="user-list-main arrow_box show_read_users hide" id="show_read_users_{{ i }}">
                                        <p>Read by {{ (mDetails.readUsers && mDetails.readUsers.length)?mDetails.readUsers.length:0}} user(s)</p>
                                        <hr class="hr-read"/>
                                        <div class="user-list-sub " *ngFor="let readUsers of mDetails.readUsers;let j=index">
                                            <div class="cat__core__avatar cat__core__avatar--50 read-user-img">
                                                <img src="{{ readUsers.avatar }}" (error)="handleImageError($event)">
                                                
                                            </div>
                                            <span class="read-user">{{ readUsers.displayName }}</span>
                                        </div>
                                    </div>
                            </span>
                            
                            

                            <div class="cat__apps__messaging__tab__text chat-message-container">
                                    <span class="chat-wrap" *ngIf="mDetails.messageStatus === 1 && ((!toggleTranslation && configData.toggle_chat_translation == 1) || configData.chat_auto_translate != 1)" compile="{{mDetails.msg | autolinker}}" (modalBodyContent)="getModalBody($event)"></span>
                                    <span class="chat-wrap" *ngIf="mDetails.messageStatus === 1 && ((toggleTranslation && configData.toggle_chat_translation == 1) || (configData.chat_auto_translate == 1 && configData.toggle_chat_translation != 1))" compile="{{translatedMessagesDetails[i].message | autolinker}}"></span>
                                <div class="message-with-undo">    
                                    <span class="chat-wrap" *ngIf="mDetails.messageStatus === 0" [ngClass]="{'font-italic': +mDetails.messageStatus === 0}" compile="{{mDetails.message | autolinker}}  on {{mDetails.messageDeletedTime | date:deleteUndoDateFormat }}"></span>
                                    <div *ngIf="mDetails.userid == userData.userId && mDetails.messageStatus === 0 && isUndoAvailable(mDetails.messageDeletedTime)">
                                        <i class="fa fa-undo custom-icon" title="{{'TITLES.UNDO_MESSAGE' | translate}}" data-toggle="tooltip" data-placement="top" data-animation="false" (click)="deleteUndoMessage(mDetails,i,'UNDO')"></i>
                                    </div>
                                </div>
                                <div class="message-tag-wrap" *ngIf="userData.group !== '3' || mDetails.roleId==3">
                                <cat-tags [tags]="mDetails.getitems" [messageId]="mDetails.id" [chatRoomId]="currentChatroomId" [isThread]="false" (removeMessageTags)="removeMessageTags($event, mDetails.id)" [maxAllowedTags]="1" *ngIf="isUserStaff || isUserPartner"></cat-tags>
                            </div>
                            </div>
                            <div class="msg-cont-text sign" *ngIf="mDetails.sign && mDetails.sign !== 'false' && mDetails.owner  && mDetails.messageStatus === 1">
                                <img [src]="mDetails.sign" class="chat-sign-image">
                                <span class="remove-chat-sign" *ngIf="mDetails.owner === true" id="remove_sign" (click)="removeChatSignature(mDetails);"><i class="fa fa-trash" aria-hidden="true"></i>
                            </span>
                            </div>
                        </div>
                    </div>

                    <div *ngIf="imageUploadProgress" class="cat__apps__chat-block__item message-item clearfix">
                        <div class="cat__apps__chat-block__avatar">
                            <a class="cat__core__avatar cat__core__avatar--50" href="javascript:void(0);">
                            <img src="{{profilePic}}" alt="Alternative text to the image">
                        </a>
                        </div>
                        <div class="cat__apps__chat-block__content">
                            <div class="cat__apps__messaging__tab__name">Me</div>
                            <div class="progressbar">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" aria-valuenow="75" aria-valuemin="0"
                                        aria-valuemax="100" style="width: 75%">
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>


                </div>
            </div>
        </div>
        <!--Chat button area-->
        <div class="form-group mt-4 mb-0 floating-chat-area">
            <div  [hidden]="(chatroomUsersListFull.length <= 1 && activeMessage.messageType == 0)">
            <div class="hidden-panel" [hidden]="activeMessage.messageType!='1' ">
                <span>Please do not reply to this message</span>
            </div>
            <div class="hidden-panel" [ngClass]="{'no-pointer': disableReplySpan}" *ngIf="(activeMessage.messageType=='2' && !activeMessage.isSelfMessage && canReplyForMaskedMessage) ">
                <span class="masked-main-thread-reply" id="masked_message_reply" (click)="maskedMessageReply(activeMessage.chatroomId)">{{'LABELS.CLICK_TO_REPLY_MSG' | translate}}</span>
            </div>
            <div class="hidden-panel" [hidden]="activeMessage.messageType == '1' || (!doNotReplyStatus || userData.group != '3') ">
                <span>{{patientoffDutyMessage}}</span>
                <div class="row text-center" *ngIf="initiateChatPermission">
                    <div class="col-lg-12">
                        <button type="button"  class="double-verification-cnf-btn btn btn-outline-primary" id="start_chat_btn" (click)="chatWithModel(userData.group)"><span>Start Chat</span></button>
                    </div>
                </div>
            </div>
            <div class="hidden-panel" [hidden]="activeMessage.messageType == '1' || !showDoubleVerification">
                <span>{{showDoubleVerificationMsg}}</span>
                <div class="row">
                    <div class="col-lg-6">
                        <button type="button" class="double-verification-cnf-btn btn btn-outline-primary" id="set_dble_verfctn" (click)="setDoubleVerificationStatus(0)"><span>  Yes, Urgent  </span></button>
                    </div>
                    <div class="col-lg-6">
                        <button type="button" class="double-verification-cnf-btn btn btn-outline-secondary"  id="set_dble_verfctn_status" (click)="setDoubleVerificationStatus(1)"><span>Next Business Day Is Ok</span></button>
                    </div>
                </div>
            </div> </div>
            <div class="hidden-panel" *ngIf="(chatroomUsersListFull.length <= 1 && userData.group == '3'&& activeMessage.messageType == '0' ) " [hidden]="!initiateChatPermission">
                    
                    <div class="row text-center">
                        <div class="col-lg-12">
                            <div>
                                {{showNoUserInChatroomMsg}}
                                <button type="button" [disabled]="disableChatroom" class="newchat   btn btn-outline-primary" id="start_chat_btn" (click)="chatWithModel(userData.group)"><span>Start New Chat</span></button>
                            </div>
                        </div>
                    </div>
                </div>
           
            
            <div  class="messageSenddiv typing-box" [ngClass]="{ 'cat__core__card-with-sidebar': (this.enableChatWindowSideBar == 1 )}" [hidden]="(activeMessage.messageType=='1' || chatroomUsersList.length==1) || (doNotReplyStatus && userData.group == '3') || (activeMessage.messageType=='2' && activeMessage.createdby != userData.userId) || (showDoubleVerification) || (hideOnmaskedReplyGenerated)">
                <div class="not-contactable-info" *ngIf="this.receiverIsNotContactable">
                    <span class="user-not-contactable-outline bg-danger"></span>{{'TITLES.NOT_CONTACTABLE' | translate}}
                </div>
                <div [ngClass]="{'disabled': this.receiverIsNotContactable}">
                    <div class="chattype"></div>
                    <div class="w-100 pl-2 pt-1 pb-1 chat-error-bg" *ngIf="showNullErrorMsg">{{ 'VALIDATION_MESSAGES.CHAT_NULL_VALIDATION' | translate }}</div>
                    <!-- status message -->
                    <div class="w-100 pl-2 pt-1 pb-1 ooo-message" [ngClass]="offlineStatus ? 'ooo-message' : 'chat-error-bg'" *ngIf="!showNullErrorMsg && showOooMessage">
                        <a *ngIf="isMultipleOfflineUser" (click)="showStatusModal()">{{'LABELS.TWO_OR_MORE' | translate}}</a> {{oooMessage}} 
                        <i class="fa fa-close pull-right pr-2 pt-1" (click)="showOooMessage = false"></i>
                    </div>

                    <div *ngIf="withAttachment; else priorityMsg" class="imessage-container" [hidden]="!withAttachment" >
                        <ng-container *ngTemplateOutlet="priorityMsg"></ng-container>
                        <div [hidden]="!loadAttachment" class="blinking" id="load_attachment">
                            Loading...
                        </div>
                
                        <ul>
                        <li *ngFor="let browseView of selectedFiles; let i = index" [hidden]="loadAttachment" class='attach-image-container'>
                        <i class='icmn-cross remove-attachment-new' (click)='removeAttachment(browseView)' alt='browseView.id' ></i>
                            <div [innerHTML]="browseView.imageHtml" title="browseView.name"></div>
                            <div class="attachment-name">{{browseView.name}}</div>
                        </li>
                        </ul>

                    </div>
                    <ng-template #priorityMsg>
                        <small *ngIf="messagePriority === MESSAGE_PRIORITY.HIGH" class="h6 text-uppercase font-weight-bold text-danger d-flex align-items-center"><i class="alert-fill bg-danger" aria-hidden="true"></i>&nbsp;{{ 'PRIORITIES.HIGH' | translate }}</small>
                        <small *ngIf="messagePriority === MESSAGE_PRIORITY.LOW" class="h6 text-uppercase font-weight-bold text-primary d-flex align-items-center"><i class="arrowdown-fill bg-primary" aria-hidden="true"></i>&nbsp;{{ 'PRIORITIES.LOW' | translate }}</small>
                    </ng-template>
                    <div class="mention-users-dropdown-container">
                        <div id="mention-users-dropdown" class="dropdown-menu" role="menu" aria-labelledby="dropdownMenu">
                        <input type="button" *ngFor="let user of filteredMentionUsers" class="dropdown-item" [ngClass]="{ 'active' : user.active }" [attr.data-target]="user.userId" value="{{user.displayName}}">
                        </div>
                    </div>
                    <textarea #emojipicker class="textarea form-control adjustable-textarea" (keyup)=userType($event)  id="typearea" placeholder="Type here!" style="overflow: hidden; word-wrap: break-word; resize: none; height: 56px;" (keypress)="enterpressalert($event)"></textarea>
                    <div class="mt-3">
                        <button [disabled]="!sendActive || chatDisable" class="btn btn-primary" (click)="sendData()" id="sendata">
                        <i class="fa fa-send mr-2"></i>
                        Send
                    </button>

                    
                    <button type="button" class="btn btn-primary" *ngIf="joinChat  && joined" (click)="showVideoChat()" id="videoChatBtn" [disabled]="!(enableVideoButton && usersLoaded) || _sharedService.onVideoChat" [hidden]="!_commonVideoService.checkVideoPluginLoaded() || !(userData?.config?.enable_video_chat == '1' && privileges.indexOf('activateVideoChat') != -1) && (!joinChat)">
                        <i class="fa fa-video-camera mr-2"></i>
                        <span id="video-text"> 
                            <span>Join Call</span>  
                        </span>

                    </button>

                    <button class="btn btn-primary" *ngIf="!joinChat || !joined" (click)="videochat()" id="videobuttonId" [disabled]="!(enableVideoButton && usersLoaded) || _sharedService.onVideoChat" [hidden]="!_commonVideoService.checkVideoPluginLoaded() || !(userData?.config?.enable_video_chat == '1' && privileges.indexOf('activateVideoChat') != -1) && (!joinChat)">
                        <i class="fa fa-video-camera mr-2"></i>
                        Video Chat
                    </button> 

                    <div [ngClass]="chatWithUserType != 'virtual'?'form-popup':'form-popup appless-button-only'" id="videoPopup">
                        <div class="call_button_action">
                            <button *ngIf="chatWithUserType != 'virtual' " type="button" class="btn btn-primary" (click)="showVideoChat()" id="videoChatBtn" [disabled]="!(enableVideoButton && usersLoaded) || _sharedService.onVideoChat" [hidden]="!_commonVideoService.checkVideoPluginLoaded() || !(userData?.config?.enable_video_chat == '1' && privileges.indexOf('activateVideoChat') != -1) && (!joinChat)">
                                    <span id="video-text"> 
                                    <span *ngIf="!joinChat || !joined">InApp Call</span>    
                                </span>    
                            </button>
                            <button type="button" class="btn btn-primary" (click)="sendApplessData()" id="sendapplessdata" [disabled]="!(enableVideoButton && usersLoaded) || _sharedService.onVideoChat" [hidden]="!_commonVideoService.checkVideoPluginLoaded() || !(userData?.config?.enable_video_chat == '1' && userData?.config?.enable_appless_video_chat == '1' && privileges.indexOf('activateVideoChat') != -1) && (!joinChat)">AppLess Call</button>
                            <span class="close" (click)="closeForm()">×</span>
                        </div>
                    </div>
                
                    <button type="button"  class="btn btn-primary" (click)="initializeVideoChat()" [hidden]="_commonVideoService.checkVideoPluginLoaded() || !(userData?.config?.enable_video_chat == '1' && privileges.indexOf('activateVideoChat') != -1) ">
                        initiate Video Chat
                    </button>

                        <button class="btn btn-link" (click)="triggerFileChange()">
                        <input type="file" [disabled]="chatDisable" name="myfile" id="myfile" (change)="uploadFileConfirm($event)" style="display:none;" multiple >
                            <i class="fa fa-paperclip" aria-hidden="true"></i> Attach File
                        </button>
                        <div *ngIf="notPatient" class="btn-group btn-group-toggle btn-priority-group" data-toggle="buttons">
                            <label class="btn btn-light mb-0" data-toggle="tooltip" data-placement="top" title="{{ 'TOOLTIPS.MARK_MSG_HIGH_PRIORITY' | translate }}" (click)="this.messagePriority = MESSAGE_PRIORITY.HIGH" [ngClass]="{'active': messagePriority === MESSAGE_PRIORITY.HIGH}">
                            <input type="radio" name="messagePriority" id="high-msg-priority" autocomplete="off"> <i class="alert-fill bg-danger" aria-hidden="true"></i>
                            </label>
                            <label class="btn btn-light mb-0" data-toggle="tooltip" data-placement="top" title="{{ 'TOOLTIPS.SEND_NORMAL_MSG' | translate }}" (click)="this.messagePriority = MESSAGE_PRIORITY.NORMAL" [ngClass]="{'active': messagePriority === MESSAGE_PRIORITY.NORMAL}">
                            <input type="radio" name="messagePriority" id="normal-msg-priority" autocomplete="off" checked> <i class="fa fa-comment text-light" aria-hidden="true"></i>
                            </label>
                            <label class="btn btn-light mb-0" data-toggle="tooltip" data-placement="top" title="{{ 'TOOLTIPS.MARK_MSG_LOW_PRIORITY' | translate }}" (click)="this.messagePriority = MESSAGE_PRIORITY.LOW" [ngClass]="{'active': messagePriority === MESSAGE_PRIORITY.LOW}">
                            <input type="radio" name="messagePriority" id="low-msg-priority" autocomplete="off"> <i class="arrowdown-fill bg-primary" aria-hidden="true"></i>
                            </label>
                        </div>
                        <span class="chatroom-enterkey-option">
            <div class="">
                    <label class="form-check-label">
                            <input [disabled]="!sendActive" class="form-check-input" type="checkbox"  id="enterKeyCheck"  [checked]="chatEnterCheckboxValue" (change)="chatEnterKeyCheck($event)" >
                        Press Enter to Send
                    </label>
                </div>
                    </span>
                    </div>
                </div>
            </div>

        </div>
         <!--Chat button area end-->
    </div>
</section>
</section>



<a class="cat__core__scroll-top" href="javascript: void(0);" id="scroll_top" onclick="$('body, html').animate({'scrollTop': 0}, 500);"><i class="icmn-arrow-up"></i></a>

<!-- END: apps/messaging -->



