.msg_grp_title {
    text-transform: capitalize;
}

.filter-site {
    margin-left: -0.72rem;
    margin-right: -0.72rem;
    padding-top: 6px;
}

.ci-filter-dropdown .select2.select2-container {
    display: inline-block !important;
    width: 113px !important;
}

.select2-selection .select2-selection--single {
    width: 100px !important;
}

.checked-style .btn {
    margin-left: 0;
}

.checked-style {
    width: auto;
    display: inline-block;
}

.search-style {
    width: 400px;
    display: flex;
    gap: 10px;
    align-items: center;
}

.search-style input {
    width: 55%;
    display: inline-block;
}

.filtter-site-style .filter-site {
    padding-top: 0 !important;
}

.filtter-site-style .filter-site .selct-site-lbl span {
    font-weight: bold;
}

.button-style {
    display: inline-block;
    width: 45%;
}

.button-style .btn {
    position: relative !important;
    width: auto;
    left: 0 !important;
    right: 0;
}

.inbox-flag-filter-dropdown, .inbox-priority-filter-dropdown {
    margin-top: 0px !important;
    position: unset !important;
    width: 24%;
    display: inline-block;
}

.filtter-site-style {
    width: 30%;
    display: inline-block;
    margin-left: 0%;
}

.filtter-site-style .selct-site-drp {
    margin-top: 7px;
}

.card-header {
    margin-left: 3px;
    margin-right: 3px;
}

.label-position {
    padding-top: 14px;
}

.inbox-site-filter {
    position: initial;
}

.list-site-filter-align {
    right: -50px;
}

#date-range-select {
    i {
        position: relative;
        top: auto !important;
        right: auto !important;
    }
}

.priority-msg-icon {
    display: inline-block;

    .alert-fill, .alert-outline, .at-fill,
    .arrowdown-fill, .arrowdown-outline, .at-outline {
        display: inline-block;
    }
}

.message-sublist-details {
    .priority-msg-icon {
      position: relative;
      right: auto;
      float: right;
    }
  }

.searchbar {
    gap: 10px;
}

.gap-1 {
    gap: 1rem;
}

.tag-filter {
    width: 310px;
}

.flex-align-right {
    margin-left: auto !important;
}

:host ::ng-deep .inbox-data-container.inbox-message-list {
    .callout {
        display: none !important;
    }
    .mention {
        color: inherit;
        background: none!important;
        border: none;
        padding: 0!important;
        font-weight: inherit;
    }
}

.search-style, .search-style input, .button-style {
    width: auto;
}

.checked-style {
    margin-left: 10px;
    display: flex;
    align-items: center;
}

.search-style {
    margin-right: 25px;
}

.cat__apps__messaging__header i {
    display: inline-block;
    position: relative;
    right: auto !important;
    top: auto !important;
}

.cat__core__control.main.cat__core__control--checkbox {
    margin-bottom: 2.71rem;
}

.action-btn-container-inbox {
    left: 20px;
}

:host ::ng-deep ch-daterange-picker input {
    width: inherit !important;
}

app-messages-quick-filter {
    display: inline-block;
}

.pinned-icon {
    font-size:22px;
    position: unset !important;
}
