import { Injectable } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import 'rxjs/add/operator/toPromise';
import { isBlank, replaceSingleQuoteWithEmpty } from 'app/utils/utils';
import { StructureService } from '../structure.service';

@Injectable()
export class ChatService {
  chatroomMessagesApi = '';
  signatureAction = '';
  getUsersListByRoomApi;
  inviteUserToChatroomApi;
  checkEnablePushChatApi;
  translateApi;

  config:any;
  userDetails:any;
  configData:any = {};
  userData:any = {};
  data={};
  constructor(
    private _http: Http,
    private _structureService:StructureService
    ) {
      this.chatroomMessagesApi = `${this._structureService.apiBaseUrl}citus-health/${this._structureService.version}/socket-message.php`;
      this.signatureAction = `${this._structureService.apiBaseUrl}citus-health/${this._structureService.version}/chat-signature-upload.php`;
      this.getUsersListByRoomApi = `${this._structureService.apiBaseUrl}citus-health/${this._structureService.version}/get-room-users.php`;
      this.inviteUserToChatroomApi = `${this._structureService.apiBaseUrl}citus-health/${this._structureService.version}/invite-user-to-chatroom.php`;
      this.checkEnablePushChatApi = `${this._structureService.apiBaseUrl}citus-health/${this._structureService.version}/check-enable-push-chatroom.php`;
      this.translateApi = `${this._structureService.apiBaseUrl}citus-health/${this._structureService.version}/messages-translate.php`;

      this.config = this._structureService.userDataConfig;
      this.userDetails = this._structureService.userDetails;
      this.configData = JSON.parse(this.config);
      this.userData = JSON.parse(this.userDetails);
  }
  getChatroomMessages(
    chatRoomId,
    showChatHistory,
    first,
    last,
    searchContent = '',
    messageFilter = '',
    flagVlaue = 0,
    priorityValue = 0,
    tags = [],
    mentions = [],
    action = '',
    messageId = 0,
    isUpdateMessage = false,
    showAllMessageDeleteUndoHistory = false,
  ) {
    const headers = new Headers();
    let actionName = '';
    const { userDetails } = this._structureService;
    const userData = JSON.parse(userDetails);
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    const options = new RequestOptions({ headers });
    let searchAPi = '';
    let filterAPi = '';
    if (!isBlank(searchContent)) {
      searchAPi = `&searchContent=${encodeURIComponent(searchContent)}`;
    }
    if (!isBlank(messageFilter)) {
      filterAPi = `&messageFilter=${encodeURIComponent(messageFilter)}`;
    }
    if (!isBlank(tags)) {
      filterAPi = `&tagIds=${encodeURIComponent(tags.join(','))}`;
    }
    if (!isBlank(mentions)) {
      filterAPi = `&mentionedUsers=${encodeURIComponent(mentions.join(','))}`;
    }
    if (!isBlank(action)) {
      actionName = `&action=${encodeURIComponent(action)}`;
    }
    if (showAllMessageDeleteUndoHistory) {
        actionName += `&showAllMessageDeleteUndoHistory=${showAllMessageDeleteUndoHistory}`;
    }
    const promise = new Promise((resolve) => {
        let apiURL = `${this.chatroomMessagesApi}?room=${chatRoomId}`;
        if (isUpdateMessage) {
          apiURL += `&id=${messageId}&showAllMessageDeleteUndoHistory=${showAllMessageDeleteUndoHistory}`;
        } else {
          apiURL += `&page=${first}${searchAPi}${filterAPi}${actionName}&last=${last}&userId=${
            userData.userId
          }${
            this._structureService.getCookie('crossTenantId') &&
            this._structureService.getCookie('crossTenantId') !== 'undefined' &&
            this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
              ? `&crossTenantId=${this._structureService.getCookie('crossTenantId')}`
              : ''
          }&flagVlaue=${flagVlaue}&messagePriority=${priorityValue}`;
        }
      this._http
        .get(apiURL, options)
        .toPromise()
        .then((res) => {
          const result = res.json();
          resolve(result);
        });
    });
    return promise;
  }

 translateMessage(msg, chatroomId) {
            
    var data = {
    chatroomMessages: msg,
    ccLang: (navigator.language).split("-")[0]
    }

   let headers = new Headers();
   headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.translateApi;
       this.data =  data;
       this._http.post(apiURL,this.data,options)
        .toPromise()
        .then(
        res => {
          const vlaidateToken = {
            chatroomId: chatroomId,
            content:res.json()
          };
          resolve(vlaidateToken);
        }
        );
    });
    return promise;

             
 }

  uploadChatSignature(data,signImageData,action){
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
      if(signImageData)
                data['sign']=signImageData;
        const apiURL = this.signatureAction+"?action="+action;
        this._http.post(apiURL,data,options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          resolve(result);
        }
        );
    });
    return promise;
  }
  getUsersListByRoom(roomId,status="notRejected",siteIds='0', includeArchivedUsers = false){
    let userDetails = this._structureService.userDetails;
    let userData = JSON.parse(userDetails);
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    var apiURL = this.getUsersListByRoomApi+"?targetId="+roomId+"&status="+status+((this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? ("&crossTenantId=" + this._structureService.getCookie('crossTenantId')) : '');
    if(includeArchivedUsers) {
      apiURL += "&includeArchivedUsers=true";
    }
    if(userData.accessSecurityEnabled) {
        apiURL += "&accessSecurityEnabled="+userData.accessSecurityEnabled;
        apiURL += "&accessSecurityEsiValue="+userData.accessSecurityEsiValue;
        apiURL += "&accessSecurityIdentifierType="+userData.accessSecurityIdentifierType;
        apiURL += "&accessSecurityType="+userData.accessSecurityType;
      }
      if(userData.config.enable_multisite != "1") {
        apiURL += "&siteIds="+siteIds;
      }

       this._http.get(apiURL,options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          resolve(result);
        }
        );
    });
    return promise;
  }
  

  checkEnablePushChat(tenantId){
     // console.log("oooooooooooo"+data);
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {      
        const apiURL = this.checkEnablePushChatApi+"?tenantId="+tenantId;
        this._http.get(apiURL,options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          resolve(result);
        }
        );
    });
    return promise;
  }

  inviteUserToChatroom(data){
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {      
        const apiURL = this.inviteUserToChatroomApi;
        this._http.post(apiURL,data,options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          resolve(result);
        }
        );
    });
    return promise;
  }

  fileFormatTypeTofileTag(file, url, format, type, name, callBack) {
             var fileFormat;
             if (file && !url) {
                 type = file.type;
                 fileFormat = this.fileTypetoFileFormat(type);

             } else {
                 fileFormat = format;
                 if (name) {
                     var extension = name.split("@call--");
                     name = extension[0];
                 }
             }
             switch (fileFormat) {
                 case "image":
                     this.CreateTagUsingImage(file, url, type, name, function(result, fileDatas) {
                         callBack(result, fileDatas);
                     });
                     break;
                 case "video":
                     this.CreateTagUsingVideo(file, url, type, name, function(result, fileDatas) {
                         callBack(result, fileDatas);
                     });
                     break;
                 case "audio":
                     this.CreateTagUsingAudio(file, url, type, name, function(result, fileDatas) {
                         callBack(result, fileDatas);
                     });
                     break;
                 case "document":
                     this.CreateTagUsingDocument(file, url, type, name, function(result, fileDatas) {
                         callBack(result, fileDatas);
                     });

                     break;
                 case "pdf":
                     this.CreateTagUsingPdf(file, url, type, name, function(result, fileDatas) {
                         callBack(result, fileDatas);
                     });

                     break;
                 default:
                     return "";
                     //break;
             }

         }
         fileTypetoFileFormat(fileTypes) {
             var fileType = fileTypes;
             //var fileType = angular.lowercase(fileTypes);
             if (this.is_image(fileType)) {
                 return "image";
             } else if (this.is_video(fileType)) {
                 return "video";
             } else if (this.is_audio(fileType)) {
                 return "audio";
             } else if (this.is_document(fileType)) {
                 return "document"
             } else if (this.is_pdf(fileType)) {
                 return "pdf"
             } else {
                 return false;
             }
         }

         //************* check file with File type start***********************//
         is_image(fileType) {
             var pngMimes = ['image/x-png'];
             var jpegMimes = ['image/jpg', 'image/jpe', 'image/jpeg', 'image/pjpeg'];
             var imgMimes = ['image/gif', 'image/jpeg', 'image/png'];
             if (pngMimes.indexOf(fileType) >= 1) {
                 fileType = 'image/png';
             }
             if (jpegMimes.indexOf(fileType) >= 1) {
                 fileType = 'image/jpeg';
             }
             if (imgMimes.indexOf(fileType) >= 1) {
                 return true;
             } else {
                 return false;
             }
         }

         is_video(fileType) {
             if (fileType) {
                 var type = fileType.split("/");
                 if (type[0] === "video") {
                     return true;
                 } else {
                     return false;
                 }
             } else {
                 return false;
             }

         }

         is_audio(fileType) {
             if (fileType) {
                 var type = fileType.split("/");
                 if (type[0] === "audio") {
                     return true;
                 } else {
                     return false;
                 }
             } else {
                 return false;
             }
         }

         is_document(fileType) {
             var mimeDoc = ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/zip', 'application/msword', 'application/octet-stream', 'application/excel', 'application/vnd.ms-excel', 'application/msexcel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
             if (fileType) {
                 if (mimeDoc.indexOf(fileType) >= 0) {
                     return true;
                 } else {
                     return false;
                 }
             } else {
                 return false;
             }
         }

         is_pdf(fileType) {
             var mimePdf = ['application/pdf', 'application/x-download'];
             if (fileType) {
                 if (mimePdf.indexOf(fileType) >= 0) {
                     return true;
                 } else {
                     return false;
                 }
             } else {
                 return false;
             }
         }
         //************* check file with File type End***********************//
         //************* Format to tag ***********************//
         CreateTagUsingImage(file, URL, type, name, callback) {
             var url;
             if (file && !URL) {
                 var reader = new FileReader();
                 reader.readAsDataURL(file);
                 reader.onload = function(e) {
                     url = reader.result;
                     callback('<img ng-src="' + url + '" title="Image" src="' + url + '"/>');
                 };
                 reader.onerror = function(error) {
                     callback(false);
                 };
             } else if (URL) {

                //  var filename = URL.split("/").pop();
                //  if (URL.indexOf('thumb_180x116/') < 0)
                //      URL = URL.replace(filename, "thumb_180x116/" + filename);
                 var fileDatas = {
                     id: "id" + (new Date()).getTime(),
                     artist: name,
                     title: name,
                     duration: "",
                     mime: type,
                     url: URL
                 }
                 callback('<img data-mediaType="image" ng-src="' + URL + '" ng-click="showCmisImage($event)" title="'+name+'" src="' + URL + '"/>', fileDatas);
             }

         }

         CreateTagUsingVideo(file, url, type, name, successCallback) {
             var fileURL, videoTag;
             var control = "";
             var mask = "";
             var fileDatas = {};
             if (file && !url) {
                 fileURL = URL.createObjectURL(file);
                 control = "autoplay muted";
             } else {
                 fileURL = url;
                 var duration = "";
                 // getFileDuration(file, function(result) {
                 //     duration = result;
                 // });
                 var filename = fileURL.split("/").pop();
                 var filenameNotExt = filename.split(".");
                 filenameNotExt = filenameNotExt.shift();
                 //var imageUrl = fileURL.replace(filename, "videothumb/" + filenameNotExt + ".jpg");
                 var imageUrl = url + '?type=thumbnail';;
                 mask = '<img data-mediaType="video" data-src="' + fileURL + '" src="' + imageUrl + '"  ng-click="showVideo($event)" class=""/>';

                 fileDatas = {
                     id: "id" + (new Date()).getTime(),
                     artist: name,
                     title: name,
                     duration: duration,
                     mime: type,
                     url: url,
                     imgFile: imageUrl
                 }
             }
             if (mask != "") {
                 videoTag = mask;
             } else {
                 videoTag = '<video ' + control + '>' +
                     '<source src = "' + fileURL + '" type = "' + type + '">' +
                     '<i class="ion-videocamera"></i> Video</video> '
             }
             successCallback(videoTag, fileDatas);

         }

         CreateTagUsingAudio(file, url, type, name, success) {
             var fileURL;
             var control;
             var fileDatas = {};
             if (file && !url) {
                 fileURL = URL.createObjectURL(file);
             } else {
                 fileURL = url;
                 var duration = "";
                 // getFileDuration(file, function(result) {
                 //     duration = result;
                 // });
                 fileDatas = {
                     id: "id" + (new Date()).getTime(),
                     artist: name,
                     title: name,
                     duration: duration,
                     mime: type,
                     url: fileURL
                 }
             }
             success('<audio controls>' +
                 '<source src="' + fileURL + '" type="' + type + '">' +
                 '<i class="ion-headphone"></i> Audio' +
                 '</audio>', fileDatas);
         }

         CreateTagUsingDocument(file, url, type, title, successCallback) {
             const name = replaceSingleQuoteWithEmpty(title);
             var random = '2.png';
             if (file && !url) {
                 if (['application/excel', 'application/vnd.ms-excel', 'application/msexcel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'].indexOf(file.type) > -1) {
                     random = '4.png';
                 }
                 successCallback("<img  ng-src='./img/doc/" + random + "' title='Image'  src='"+this._structureService.iconPath+"doc/" + random + "'/>");
             } else {
                 var filename = url.substring(url.lastIndexOf('/') + 1);
                 filename = filename.substring(filename.lastIndexOf('/') + 1);
                 filename = filename.substring(filename.lastIndexOf('.') + 1);
                 if (["xl", "xls", "xlsx"].indexOf(filename) > -1) {
                     random = '4.png';
                 }
                 var fileDatas = {
                     id: "id" + (new Date()).getTime(),
                     artist: name,
                     title: name,
                     duration: "",
                     mime: type,
                     url: url
                 }
                 successCallback("<img data-mediaType='document' data-src='" + url + "' ng-click='showPdfOrDocs($event)' ng-src='./img/doc/" + random + "' title='"+name+"' class='file-thumbnail' src='"+this._structureService.iconPath+"doc/" + random + "'/>", fileDatas);
             }
         }

         CreateTagUsingPdf(file, url, type, title, successPdf) {
             const name = replaceSingleQuoteWithEmpty(title);
             //console.log("this._structureService.iconPath",this._structureService.iconPath);
             if (file && !url) {
                 successPdf("<img ng-src='./img/pdf/1.png' title='Image' src='"+this._structureService.iconPath+"pdf/1.png'/>");
             } else {
                 var fileDatas = {
                     id: "id" + (new Date()).getTime(),
                     artist: name,
                     title: name,
                     duration: "",
                     mime: type,
                     url: url
                 }
                 successPdf("<img data-mediaType='pdf' data-src='" + url + "' ng-click='showPdfOrDocs($event)' src='"+this._structureService.iconPath+"/pdf/1.png' title='"+name+"' class='file-thumbnail' src='"+this._structureService.iconPath+"/pdf/1.png'/>", fileDatas);
             }
         }

 
  
}

