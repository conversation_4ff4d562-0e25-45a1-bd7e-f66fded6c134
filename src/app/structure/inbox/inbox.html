
<section class="card chatwith-btn-panel">
    <div class="card-header text-center" *ngIf="configuringTenantData">
        <img src="./assets/img/loader/please-wait-tenant.gif" />
    </div>
    <div class="card-header row">
        <span class="cat__core__title col-md-6">
            <strong>{{'TITLES.MESSAGES' | translate}} <span *ngIf="inboxUnreadMessageCountFiltered && ((inboxUnreadMessageCountFiltered) > 0)">({{inboxUnreadMessageCountFiltered}})</span></strong>
        </span>

        <div class="filter-sites-wrapper inbox-site-filter" [hidden]="!hideSiteSelection || !_sharedService.viewItem">
            <div class="col-md-12 list-site-filter-align" >
            <div class="filter-site row">
                <div class="site-label">
        <span>{{ labelSiteFilter | translate }}</span> 
        </div>
        <div class="selct-site-drp col-md-8">
            <app-select-sites [events]="eventsSubject.asObservable()" [filterType]=true (siteIds)="getSiteIds($event)" (hideDropdown)="hideDropdown($event)">
            </app-select-sites>
    </div>
      </div>
      </div>
        </div>

    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="[userData.defaultPage || '/profile']">{{'BREADCRUMBS.HOME'| translate}}</a></li>
            <li class="breadcrumb-item">{{'TITLES.MESSAGES' | translate}}</li>
        </ol>
        <div class="row inbox-data-listing-container" *ngIf="!configuringTenantData">
            <div class="col-lg-12">
                <div class="mb-5">

                    <section class="card" order-id="card-3">
                        <!-- *************************Forward Popup Model**************** -->
                        <div class="modal fade forward-modal" id="exampleModalRerouteInbox" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                            <div class="modal-dialog" role="document">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h4 class="modal-title" id="exampleModalLabel">{{'LABELS.FORWARD_TO' | translate}}</h4>
                                        <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="closeModal()">
                                    <span aria-hidden="true">&times;</span>
                                    </button>
                                    </div>
                                    <div class="modal-body">
                                        <form>
                                            <div class="card-block">
                                                <div class="cat__core__card-sidebar">
                                                    <div class="col-sm-12" [hidden]="!hideSiteSelection">
                                                           <div class="row">
                                                           <div style="padding-top: 6px;">
                                                           <span>{{ 'LABELS.FILTER_BY_SITES' | translate }}</span>
                                                           </div>
                                                           <div style="width: 73%" *ngIf ="isFilter">
                                                           <app-select-sites [filterType]=true [disableFilter]="isSiteFilterDisabled" [events]="eventsSubject.asObservable()" [selectedSiteIds]="patientSiteId" (siteIds)="getSiteIds($event,'Forward')" (hideDropdown)="hideDropdown($event)">
                                                           </app-select-sites>
                                                           </div>
                                                           </div>
                                                           </div>
                                                    <div class="cat__apps__messaging__header chat-with-wrapper-search">
                                                        <input [ngClass]="{'groups-srch-width-reroute': (optionShow=='groups' || optionShow=='staff' || optionShow=='patient'), 'others-srch-width':(optionShow!='groups' || optionShow!='staff' || optionShow!='patient') }" (keydown)="searchOnEnter($event,optionShow)" class="form-control cat__apps__messaging__header__input ng-untouched ng-pristine ng-valid" id="chat-with-modal-search-box" placeholder="Search Here" #userSearchFromChat>
                                                        <span *ngIf="(optionShow!='groups' || optionShow!='staff' || optionShow!='patient')" class="icmn-search serch"></span>
                                                        <button [disabled]="userSearchFromChat.value.trim() == ''" *ngIf="optionShow=='groups' || optionShow=='staff' || optionShow=='patient'" type="button" class="btn btn-sm btn-primary srchBtn" title="{{'BUTTONS.SEARCH' | translate}}" (click)="searchReroute(optionShow)">{{'BUTTONS.SEARCH' | translate}}</button>
                                                        <button type="button" class="btn btn-sm btn-default resetBtn" *ngIf="optionShow=='groups' || optionShow=='staff' || optionShow=='patient'" title="{{'BUTTONS.RESET' | translate}}" (click)="reset(optionShow)">{{'BUTTONS.RESET' | translate}}</button>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="forwarding-behaviour-container">
                                                <div class="forward-model-option-user-list" *ngFor="let userdatas of (userList | filterUsersList:userData.userId ); let i=index" [class.cat__apps__messaging__tab--selected]="i == selectedRow">
                                                    <div class="forward-user-role" (click)="setRerouteUser(userdatas, i)">
                                                        <label class="form-check-label">
                                                <input checked="" class="form-check-input" name="userSelection" value="userdatas" type="radio"  [checked]="i === selectedRow" >                                            
                                            </label>
                                                        <i class="fa fa-clock-o" [hidden]="!userdatas.isScheduled"></i>
                                                        <p>{{userdatas.displayname}} <span *ngIf="userdatas.naTagNames && userdatas.naTagNames != '' && userdatas.naTagNames != 'null'">({{userdatas.naTagNames}}) </span> <span class="modal-user-role">[{{userdatas.role}}]</span></p>
                                                    </div>
                                                </div>
                                                <div class="chat-with-empty-data" *ngIf="userList && !(userList | filterUsersList:userData.userId ).length && !chatWithLoader.staffs" >No Clinicians Available. <span  *ngIf="userSearchFromChat.value"><span style="color: #0088ff;cursor: pointer;" (click)="reset(optionShow)"> Click Here </span> to Reset the Search.</span></div>
                                                <div *ngIf="chatWithModalShown && chatWithLoader.staffs" class="loading-container">
                                                    <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                                    <div class="loading-text">{{'MESSAGES.LOADING_CLINICIANS' | translate}}</div>
                                                </div>
                                                <div style="text-align: center;width:100%;margin-top: 14px;float: left;">
                                                    <button type="button" [hidden]="!(chatWithModalShown && optionShow=='staff' && !noMoreItemsAvailable.users && userList && userList.length)" class="btn btn-sm btn-default" (click)="loadMoreUsers('staff',loadMoreSearchValue)">{{loadMoremessage.users}}</button>
                                                </div>
                                            </div>
                                            <div [hidden]="!forwardingStatus" class="forwarding-behaviour-box">
                                                <h5>{{'TITLES.FORWARDING_BEHAVIORS' | translate}}</h5>
                                                <div class="form-check">
                                                    <label class="form-check-label">
                                                <input [checked]="true" class="form-check-input"  name="forwardingSelection" value="keep_forwarder" type="radio" (click)="setForwardBehaviour('keep_forwarder')">
                                                {{'LABELS.KEEP_ME_IN_CHAT_SESSION' | translate}}
                                            </label>
                                                </div>
                                                <div class="form-check">
                                                    <label class="form-check-label">
                                                <input checked="" class="form-check-input"  name="forwardingSelection" value="remove_forwarder" type="radio"  (click)="setForwardBehaviour('remove_forwarder')">
                                                {{'LABELS.REMOVE_ME_FROM_CHAT_SESSION' | translate}}
                                            </label>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="closeModal()">{{'BUTTONS.CLOSE' | translate}}</button>
                                        <button type="button" class="btn btn-primary reRouteBehaviour" disabled (click)="reRouteMessage()" style="cursor:pinter;">{{'BUTTONS.FORWARD' | translate}}</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    
                        <div class="card-block">
                            <div class="col-lg-12">
                                <app-messages-filter [isInbox]="true" [resetAdvanceSearchForm]="resetAdvanceSearch" (advanceSearch)="applyAdvanceSearch($event)"></app-messages-filter>
                              </div>
                            <div class="d-flex flex-wrap align-items-center justify-content-between cat__apps__messaging__header searchbar gap-1" id="cat__apps__messaging__header">
                                <div class="checked-style">
                                    <label [hidden]="beforeLoad && inboxDataLength==0" class="cat__core__control main cat__core__control--checkbox" (click)="selectAll();">
                                            <input type="checkbox" id="selectall"[checked]="selectedAll">
                                            <div class="cat__core__control__indicator" data-toggle="tooltip" data-placement="top" title="Select all for Read or Archive"></div>
                                        </label>
                                    <small [hidden]="beforeLoad && inboxDataLength==0" class="cat__apps__messaging__tab__time action-btn-container-inbox">
                                        <button aria-expanded="false" class="btn btn-sm btn-default dropdown-toggle action-btn" data-toggle="dropdown" type="button"> {{'BUTTONS.MORE' | translate}}</button>
                                        <ul class="dropdown-menu">
                                            <a class="dropdown-item" (click)="readAllSelectedItem(selectedInboxItems)" >{{readAsButtonText}}</a>
                                            <a class="dropdown-item" (click)="archiveInbox(selectedInboxItems)">{{((selectedInboxItems && tickedNormalMessageCount > 0) && !selectedAll) ? 'Archive' : 'Archive all'}}</a>
                                            <a class="dropdown-item" (click)="changePinUnpinStatusMultiple(selectedInboxItems)" *ngIf="(selectedInboxItems && tickedNormalMessageCount > 0) && !selectedAll">{{ 'LABELS.PINUNPIN' | translate }}</a>
                                            <a class="dropdown-item" (click)="changePinUnpinStatusMultiple(selectedInboxItems)" *ngIf="selectedAll">{{ 'LABELS.PINUNPINALL' | translate }}</a>
                                        </ul>
                                        </small>
                                    <app-messages-quick-filter [isInbox]="true" [resetQuickFilter]="resetQuickSearch" (quickSearch)="applyQuickSearch($event)"></app-messages-quick-filter>    
                                </div>
                                <div class="search-style">
                                    <input class="form-control cat__apps__messaging__header__input" [(ngModel)]="searchInboxkeyword" (keydown)="enterpressalert($event)" placeholder="Search..." id="userSearchTxt" #search>
                                    <i style=" right: -30px !important;" class="icmn-loader" *ngIf="searchFlag"><img src="./assets/img/loader/color.gif" class="menu-spinner"></i>
                                    <i style=" right: -30px !important;" class="icmn-loader" *ngIf="resetSearchh"><img src="./assets/img/loader/color.gif" class="menu-spinner"></i>
                                    <div class="button-style">
                                        <button type="button" [disabled]="!search.value" id="search_btn_inbox" class="btn btn-sm btn-primary" title="{{'TITLES.SEARCH' | translate}}" (click)="searchMessagesAndDocs()">{{'BUTTONS.SEARCH' | translate}}</button>
                                        <button type="button" class="btn btn-sm btn-default"   id="reset_btn_inbox" title="{{'TITLES.RESET' | translate}}" (click)="resetSearch()">{{'BUTTONS.RESET' | translate}}</button>
                                        <button class="btn btn-sm btn-default reset-btn" [class.bg-primary]="structureService.inboxFilterApplied === FILTER.ADVANCE" id="advance-search-button" style="margin-left:10px;" (click)="showAdvanceSearch()">
                                            <img src="./assets/img/filter.png" data-toggle="tooltip" id="advance-search-button" data-placement="top" title="{{ 'TOOLTIPS.ADVANCE_SEARCH' | translate }}" class="adv-search" style="height: 18px;">
                                        </button>
                                    </div>
                                    <div class="row">
                                        <div class="col-lg-5 text-right padding-top-ten">
                                            <label>{{'LABELS.DATE_RANGE' | translate}}
                                                <i chToolTip="defaultDateRangeInfo" class="icmn-info" data-animation="false">&nbsp;</i>:&nbsp;
                                            </label>
                                        </div>
                                        <div class="col-lg-7">
                                            <ch-daterange-picker [dateRangeFilterOptions]="dateRangeFilterOptions" [control]="dateRanges" [saveStateInto]="dateRangeStoreKey" (selectDateRange)="dateRangeSelected($event)" [keepSession]="true"></ch-daterange-picker>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="cat__core__card-sidebar inbox-data-container inbox-message-list">
                                <ch-loader [showLoader]=messageLoader.messages [showInTopPageCenter]=true></ch-loader>
                                <div class="cat__apps__messaging__list">
                                    <div class="no-data" *ngIf="!totalCount && currentPage === 1 && noSearchData">
                                        <span class="chat-with-link-span">{{'VALIDATION_MESSAGES.NO_MATCHING_ITEMS_FOUND' | translate}}</span>
                                    </div>
                                    <div class="no-data" *ngIf="beforeLoad && totalCount === 0 && currentPage === 1 && !noSearchData && !messageLoader.messages">
                                        <span class="chat-with-link-span">{{'MESSAGES.NO_MSG_INBOX' | translate}}</span>
                                    </div>
                                    <div class="no-data" *ngIf="beforeLoad && inboxDataLength==0 &&  pageCountMessage == 0 && disableMessages">
                                        <span class="chat-with-link-span" *ngIf="qLimit!='0' && loadMoreCount !== 0">No message threads have been created or updated in the last {{qLimit}} day(s). Please select a different date range to see earlier messages</span>
                                        <span class="chat-with-link-span" *ngIf="!qLimit && loadMoreCount === 0 && currentPage != 1">{{'MESSAGES.NO_MATCHING_ITEMS_FOUND' | translate}}</span>
                                    </div>
                                <span *ngIf="totalCount">
                                    <div class="cat__apps__messaging__tab row-selection inbox-msg-tab" [ngClass]="{'inbox-checked-item-color': message.markAsRead}" *ngFor="let message of inboxData | userPersistantMessageFilterFn;let i=index; trackBy: trackByForInboxData">
                                        <div *ngIf="message">
                                            <label class="cat__core__control cat__core__control--checkbox message-detail-checkbox">
                                                        <input type="checkbox" [id]="message.chatroomId" class="cat__core__control__indicator" [checked]="message.markAsRead" (change)="checkIfAllSelected(message);">
                                                    </label>
                                            <div class="cat__apps__messaging__tab__avatar message-detail-section avatar-position" *ngIf="message.messageCategory === messageCategory.GENERAL; else otherMessages"
                                             (click)="gotoChatRoom($event, message);">
                                             <a class="cat__core__avatar cat__core__avatar--50" href="javascript:void(0);">
                                                <div class="">
                                                  <img 
                                                    [src]="message.chatAvatar === chatAvatarOptions.CLINICIAN || message.chatAvatar === chatAvatarOptions.PATIENT 
                                                    ? defaultPics.PROFILE : message.chatAvatar"
                                                    (error)="this.src='assets/img/file-404.png'"
                                                    alt="Avatar" outOfOfficeStatus [oooInfo]="message.oooInfo">
                                                </div>
                                              </a>
                                            </div>
                                            <ng-template #otherMessages>
                                                <div class="float-left cat__core__avatar cat__core__avatar--50 message-detail-section" *ngIf="message.messageCategory === messageCategory.MESSAGE_GROUP || message.messageCategory === messageCategory.PDG || message.messageCategory === messageCategory.BROADCAST" (click)="gotoChatRoom($event, message)">
                                                    <img [src]="defaultMsgPics[message.messageCategory]" draggable="false">
                                                </div>
                                                
                                                <div class="float-left cat__core__avatar cat__core__avatar--50 message-detail-section" *ngIf="message.messageCategory === messageCategory.MASKED" (click)="message.maskedSubCount?expandSubMessageList($event, message, i):gotoChatRoom($event, message)">
                                                    <img [src]="defaultPics.MASKED" draggable="false">
                                                </div>
                                            </ng-template>                    
                                            <div>
                                                <div class="cat__apps__messaging__tab__content inbox-cont-row inbox-unread">
                                                    <div class="action-icons">
                                                        <small *ngIf="message.pinnedStatus" class="cat__apps__messaging__tab__time pinned-icon">
                                                            <ng-container>                                                                
                                                                <span chToolTip="unpinChat"><i class="fa fa-thumb-tack" (click)="changePinUnpinStatus(message.chatroomId,'unpin')"></i>
                                                                </span>
                                                            </ng-container>
                                                        </small>
                                                        <small class="cat__apps__messaging__tab__time flag-tab-msg msg-flag item-{{message.chatroomId}}" *ngIf="userDataConfig.enable_message_flagging == '1'&& message.messageFlag !== 0 && !message.isPatientInitatedChat">
                                                            <ng-container >                                                                
                                                                <span chToolTip="flagInboxM" data-animation="false" class="fa-stack flag-main-container dropdown-toggle" [ngClass]="{'high':(message.messageFlag == '3'), 'medium':(message.messageFlag == '2'), 'low':(message.messageFlag == '1')}" id="{{'span-dropdown-'+message.chatroomId}}">
                                                                    <span class="fa fa-stack-2x" [ngClass]="{'fa-flag':(message.messageFlag != '0'), 'fa-flag-o':(message.messageFlag == '0')}">
                                                                    </span>
                                                                </span>
                                                            </ng-container >
                                                        </small>
                                                        <small class="cat__apps__messaging__tab__time flag-tab thread-flag item-{{message.chatroomId}}" *ngIf="userDataConfig.enable_message_flagging == '1' && !message.isPatientInitatedChat">
                                                            <ng-container>                                                                
                                                                <span chToolTip="flagInboxT" data-animation="false" class="fa-stack flag-main-container dropdown-toggle" [ngClass]="{'high':(message.chatroomFlag == '3'), 'medium':(message.chatroomFlag == '2'), 'low':(message.chatroomFlag == '1')}" id="{{'span-dropdown-'+message.chatroomId}}" data-toggle="collapse"  aria-expanded="false" (click)="expandFlagOPtions(message)">
                                                                    <span class="fa fa-stack-2x" [ngClass]="{'fa-flag':(message.chatroomFlag != '0'), 'fa-flag-o':(message.chatroomFlag == '0')}">
                                                                    </span>
                                                                </span>
                                                                <ul id="{{'dropdown-item-'+message.chatroomId}}" class="collapse dropdown-menu">
                                                                    <form name="threadFlagForm">
                                                                        <li class="dropdown-item">
                                                                            <label>
                                                                                <input  name="threadFlagGrp" type="radio" [(ngModel)]="message.chatroomFlag" value="3" (change)="onthreadFlagChange(message)"> {{'OPTIONS.MESSAGE_THREAD.HIGH' | translate}}
                                                                            </label>
                                                                        </li>
                                                                        <li class="dropdown-item">
                                                                            <label>
                                                                                <input  name="threadFlagGrp" type="radio" [(ngModel)]="message.chatroomFlag" value="2" (change)="onthreadFlagChange(message)"> {{'OPTIONS.MESSAGE_THREAD.MEDIUM' | translate}}
                                                                            </label>
                                                                        </li>
                                                                        <li class="dropdown-item">
                                                                            <label>
                                                                                <input  name="threadFlagGrp" type="radio" [(ngModel)]="message.chatroomFlag" value="1" (change)="onthreadFlagChange(message)"> {{'OPTIONS.MESSAGE_THREAD.LOW' | translate}}
                                                                            </label>
                                                                        </li>
                                                                        <li class="dropdown-item">
                                                                            <label>
                                                                                <input  name="threadFlagGrp" type="radio" [(ngModel)]="message.chatroomFlag" value="0" (change)="onthreadFlagChange(message)"> {{'OPTIONS.MESSAGE_THREAD.CLEAR_FLAG' | translate}}
                                                                            </label>
                                                                        </li>
                                                                    </form>
                                                                </ul>
                                                            </ng-container >
                                                    </small>
                                                    <ng-container *ngTemplateOutlet="showMessagePriorityForChatThread; context: { message: message }"></ng-container>
                                                    <small class="tag_message_div cat__apps__messaging__tab__time tag-msg" *ngIf="isUserStaff && !isUserPartner">
                                                        <span id="message-icon" data-toggle="tooltip" data-placement="top" title="" data-original-title="Tag Threads"  class="tag-message-icon" style="margin-top: -8px;" (click)="manageTags(message)"></span>
                                                    </small>
                                                    </div>
                                                    <small class="cat__apps__messaging__tab__time" *ngIf="!isSubListVisible || message.chatroomId!=chatroomSelected">{{(+message.messageStatus === 1? (message.messageOrder)*1000 : formatMessageDeletedTime(message.messageDeletedTime)) | dateshortyearFilter}}
                                                    <ng-container >       

                                                    <button aria-expanded="false" id="{{'button-'+message.id}}" class="btn btn-sm btn-default dropdown-toggle action-btn" data-toggle="dropdown" type="button"> {{'GENERAL.ACTIONS' | translate}}</button>
                                                    <ul class="dropdown-menu">
                                                        <a class="dropdown-item" id="forward" *ngIf="privileges.indexOf('messageForwarding')!==-1"  [hidden]="message.messageCategory === messageCategory.BROADCAST || message.messageCategory === messageCategory.MASKED" (click)="selectModal('Reroute to', message,message.chatroomId)">{{'BUTTONS.FORWARD' | translate}}</a>
                                                        <a class="dropdown-item" id="archive" (click)="message.messageType !== messageType.MASKED && deleteChatroomMessages(message,message.chatroomId,message.id,message.messageType) || message.messageType === messageType.MASKED && deleteMaskedMessages(message)">{{'BUTTONS.ARCHIVE' | translate}}</a>
                                                        <a class="dropdown-item" *ngIf="!message.pinnedStatus" (click)="changePinUnpinStatus(message.chatroomId,'pin')">{{ 'LABELS.PIN' | translate }}</a>
                                                        <a class="dropdown-item" *ngIf="message.pinnedStatus" (click)="changePinUnpinStatus(message.chatroomId,'unpin')">{{ 'LABELS.UNPIN' | translate }}</a>
                                                    </ul>
                                                    </ng-container>
                                                    </small>
                                                    <div (click)="message.maskedSubCount ? expandSubMessageList($event, message, i) : gotoChatRoom($event, message);$event.stopPropagation()">
                                                        <small class="unread-count-box">
                                                        <i class="unread-icon">
                                                            <span class="unread-count" *ngIf="message.maskedUnreadCount > 0 || +message.unreadCount !== 0">{{message.maskedUnreadCount || message.unreadCount}}</span>
                                                        </i>
                                                    </small>
                                                        <div class="from-name" [ngClass]="{'has-unread': +message.unreadCount || message.maskedUnreadCount || message.hasUnreadMessages }" (click)="gotoChatRoom($event, message);$event.stopPropagation()">
                                                            <div id="chat-thread-{{message.chatroomId}}" class="inbox-msg-chatHeader" [ngClass]="{'has-unread': +message.unreadCount || message.hasUnreadMessages}" (click)="gotoChatRoom($event, message);$event.stopPropagation()">
                                                                <span>{{message.chatHeading + (message.chatSubHeading? ',&nbsp;'+ message.chatSubHeading : '') }}</span>
                                                            </div>
                                                        </div>
                                                        <div class="cat__apps__messaging__tab__text" *ngIf="message.messageType === messageType.GENERAL && message.caregiver_displayname"><span class="msg_grp_title">Patient: {{message.caregiver_displayname}}</span></div>
                                                        <div (click)="gotoChatRoom($event, message)" class="rerouted-by" *ngIf="message.messageForwarded"><span class="rerouted-by-span">({{message.messageForwarded}})</span></div>
                                                        <div class="cat__apps__messaging__tab__text" [ngClass]="{'has-unread':message.hasUnreadMessages || message.maskedUnreadCount>0}" *ngIf="(message.messageCategory === messageCategory.MESSAGE_GROUP || message.messageCategory === messageCategory.PDG) && message.chatSubject"><span class="msg_grp_title">{{'LABELS.SUBJECT' | translate}}: {{message.chatSubject}}</span></div>
                                                        <div (click)="message && message.maskedSubCount && message.maskedSubCount  && isSubListVisible && message.chatroomId==chatroomSelected && gotoChatRoom($event, message) " [ngClass]="{'has-unread':message.hasUnreadMessages || message.maskedUnreadCount>0}" class="cat__apps__messaging__tab__text">
                                                            <span *ngIf="message.maskedSubCount" [hidden]="isSubListVisible && message.chatroomId==chatroomSelected" class="sublist-count">({{ message.maskedSubCount }})</span>
                                                            <span class="arrow-sub-mesage" *ngIf="message.maskedSubCount" (click)="expandSubMessageList($event, message, i); $event.stopPropagation()">
                                                                <i class="fa fa-2x fa-caret-right " [ngClass]="{'message-box-list-sublist-icon':isSubListVisible && +message.chatroomId == +chatroomSelected}"></i>
                                                                                                                            
                                                            </span>
                                                        <div class="cat__apps__messaging__tab__text" *ngIf="message.messageCategory === messageCategory.PDG && message.siteName">
                                                            <span class="msg_grp_title">{{labelSite | translate}}: {{message.siteName}}</span></div>
                                                        <div class="cat__apps__messaging__tab__text" *ngIf="userData.isAlternateContact && message.messageCategory !== messageCategory.BROADCAST">
                                                            <span *ngIf="message.patientInfo">{{'LABELS.PATIENT' | translate}}:&nbsp;{{message.patientInfo.patientFirstName}}&nbsp;{{message.patientInfo.patientLastName}}</span>        
                                                        </div>    
                                                        <div class="cat__apps__messaging__tab__text" *ngIf="message.messageCategory !== messageCategory.PDG">
                                                            <span *ngIf="message.isSelfMessage" class="arrow-right-span">{{'LABELS.MESSAGE_USER.ME' | translate}}:</span>
                                                            <span inboxCompile="{{message.message}}" [ngClass]="{ 'font-italic': +message.messageStatus === 0}"></span>
                                                        </div>
                                                            <small class="cat__apps__sublist_messaging__tab__time" *ngIf="isSubListVisible && message.chatroomId==chatroomSelected">{{message.messageOrder*1000 | dateshortyearFilter}}</small>
                                                        </div>
                                                        <cat-tags [tags]="message.allTags" [chatRoomId]="message.chatroomId" (removeMessageTags)="removeMessageTags($event, message.chatroomId)" [maxAllowedTags]="1" *ngIf="isUserStaff && !isUserPartner && message.allTags && message.allTags.length"></cat-tags>
                                                        <ng-container *ngIf="message.maskedSubCount">
                                                            <i class="loader-inner-small" *ngIf="isSubListFetching && +message.chatroomId === +chatroomSelected"><img src="./assets/img/loader/color.gif"></i>
                                                            <ng-container *ngIf="isSubListVisible && !isSubListFetching && +message.chatroomId === +chatroomSelected">
                                                                <div class="message-sublist-open" *ngFor="let subListData of message.subList; let i = index; trackBy: trackBySubThread" id="sublist_{{ subListData.chatroomid }}">
                                                                    <hr>
                                                                    <div class="message-sublist-details" (click)="replyForGroupChatSubList($event, subListData.chatroomid, subListData)">
                                                                      <small class="sublist-unread-count-box" *ngIf="+subListData.unreadCount">
                                                                        <i class="unread-icon">
                                                                          <span class="sublist-unread-count">{{ subListData.unreadCount }}</span>
                                                                        </i>
                                                                      </small>
                                                                      <span class="message-sublist-name">{{ userData.userId === subListData.userid ? subListData.chatWith : subListData.fromName }}:&nbsp;</span>
                                                                      <ng-container *ngTemplateOutlet="showMessagePrioritySubList; context: { message: subListData }"></ng-container>
                                                                      <span    [ngClass]="{ 'font-italic': +subListData.messageStatus === 0 }" class="message-sublist-description" inboxCompile="{{ subListData.message }}"></span>
                                                                      <small class="cat__apps__sublist_messaging__tab__time">{{(+subListData.messageStatus === 1? (subListData.sent)*1000 : formatMessageDeletedTime(subListData.messageDeletedTime)) | dateshortyearFilter}}</small>
                                                                    </div>
                                                                  </div>
                                                            </ng-container>
                                                        </ng-container>
                                                        <div class="btn-group inbox-btn-row">

                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <ng-template #showMessagePrioritySubList let-message="message">
                                            <div class="priority-msg-icon">
                                                <ng-container *ngIf="(message.maskedUnreadCount>0 || message.unreadCount!='0') && message.unReadPriority != MESSAGE_PRIORITY.NORMAL && message.unReadPriority !== '0'; else readPriorityMsg">
                                                    <ng-container *ngTemplateOutlet="msgPriorityIcon; context: { priority: message.unReadPriority, class: message.unReadPriority == MESSAGE_PRIORITY.HIGH ? 'alert-fill bg-danger' : 'arrowdown-fill bg-primary' }"></ng-container>
                                                </ng-container>
                                                <ng-template #readPriorityMsg>
                                                    <ng-container *ngTemplateOutlet="msgPriorityIcon; context: { priority: message.readPriority, class: message.readPriority == MESSAGE_PRIORITY.HIGH ? 'alert-outline bg-danger' : 'arrowdown-outline bg-primary' }"></ng-container>
                                                </ng-template>
                                                <ng-template #msgPriorityIcon let-priority="priority" let-class="class">
                                                    <small *ngIf="priority != MESSAGE_PRIORITY.NORMAL && priority != 0"><i [ngClass]="class"></i></small>
                                                </ng-template>
                                                <small *ngIf="message.unReadMention === CHECKED.TRUE"><i class="at-fill bg-danger"></i></small>
                                                <small *ngIf="message.unReadMention === CHECKED.FALSE && message.readMention === CHECKED.TRUE"><i class="at-outline bg-danger"></i></small>
                                            </div>
                                        </ng-template>
                                        <ng-template #showMessagePriorityForChatThread let-message="message">
                                            <div class="priority-msg-icon">
                                                <ng-container *ngIf="message.messageUnreadCount && +message.messagePriorityUnread !== MESSAGE_PRIORITY.NORMAL && +message.messagePriorityUnread !== 0; else readPriorityMsgForChatThread">
                                                    <ng-container *ngTemplateOutlet="msgPriorityIcon; context: { priority: message.messagePriorityUnread, class: message.messagePriorityUnread == MESSAGE_PRIORITY.HIGH ? 'alert-fill bg-danger' : 'arrowdown-fill bg-primary' }"></ng-container>
                                                </ng-container>
                                                <ng-template #readPriorityMsgForChatThread>
                                                    <ng-container *ngTemplateOutlet="msgPriorityIcon; context: { priority: message.messagePriorityRead, class: message.messagePriorityRead == MESSAGE_PRIORITY.HIGH ? 'alert-outline bg-danger' : 'arrowdown-outline bg-primary' }"></ng-container>
                                                </ng-template>
                                                <ng-template #msgPriorityIcon let-priority="priority" let-class="class">
                                                    <small *ngIf="priority && +priority !== MESSAGE_PRIORITY.NORMAL && +priority !== 0"><i [ngClass]="class"></i></small>
                                                </ng-template>
                                                <small *ngIf="+message.messageMentionUnread === +CHECKED.TRUE"><i class="at-fill bg-danger"></i></small>
                                                <small *ngIf="+message.messageMentionUnread === +CHECKED.FALSE && +message.messageMentionRead === +CHECKED.TRUE"><i class="at-outline bg-danger"></i></small>
                                            </div>
                                        </ng-template>
                                    </div>
                                </span>
                       
                            <div  class="row" [hidden]="!totalCount && currentPage === 1">
                                <div class="col-sm-5" style="width:100%;margin-top: 26px;padding-left: 25px;">
                                    Showing {{((currentPage*25)-25)+1}} to {{(((currentPage*25)-25))+inboxData?.length}} of {{totalCount}} entries
                                </div>
                               <div class="col-sm-7 inbox-pagination" style="width:100%;margin-top: 20px;"> 
                                    <ngb-pagination 
                                    [collectionSize]="totalCount" 
                                    [pageSize]="contentLimit" 
                                    [(page)]="currentPage" 
                                    [maxSize]="8" 
                                    [rotate]="true" 
                                    responsive="true"
                                    [directionLinks]="true"
                                    aria-label="Pagination"
                                    (pageChange)="loadPage($event)">
                                    </ngb-pagination>
                                </div>
                        </div>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>
        </div>
        <cat-manage-tags (finalTagsEmit)="handleEmitTags($event)"></cat-manage-tags>
    </div>
</section>
<a class="cat__core__scroll-top" href="javascript: void(0);" onclick="$('body, html').animate({'scrollTop': 0}, 500);"><i class="icmn-arrow-up"></i></a>
