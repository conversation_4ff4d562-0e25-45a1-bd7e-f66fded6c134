import { Component, OnInit, Pipe, PipeTransform } from '@angular/core';
import { DatePipe } from '@angular/common';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Pipe({
  name: "sortBy",
  pure:false
})
export class SortPipe {
  transform(array: Array<string>, args: string): Array<string> {
if (array !== undefined) {
    array.sort((a: any, b: any) => {
      /*if(a.legth>10){
        a=a/1000;
      }
      if(b.length>10){
        b=b/1000;
      }*/
	      if (a[args] > b[args]) {
          return -1;
      } else
       if (a[args] < b[args]) {
        return 1;
      } else {
        return 0;
      }
    });
  }
    return array;
  }
}