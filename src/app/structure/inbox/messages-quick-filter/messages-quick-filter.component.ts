import { Component, EventEmitter, Input, OnChanges, OnInit, Output } from '@angular/core';
import { Filter, MessagePriority } from 'app/constants/constants';
import { StructureService } from 'app/structure/structure.service';
import { isBlank } from 'app/utils/utils';

@Component({
  selector: 'app-messages-quick-filter',
  templateUrl: './messages-quick-filter.component.html',
  styleUrls: ['./messages-quick-filter.component.scss']
})
export class MessagesQuickFilterComponent implements OnInit, OnChanges {
  DEFAULT_SEARCH_DATA = {
    highPriority: false,
    highFlag: false,
    mention: false,
    unread: false
  };
  quickSearchData;
  @Input() isInbox = false;
  @Input() resetQuickFilter = false;
  @Output() quickSearch = new EventEmitter();

  constructor(private structureService: StructureService) {}

  ngOnInit(): void {
    this.quickSearchData = JSON.parse(JSON.stringify(this.DEFAULT_SEARCH_DATA));
    if (this.isInbox && this.structureService.inboxFilterApplied === Filter.QUICK) {
      this.quickSearchData.highPriority =
      this.structureService.priorityFilterValue && this.structureService.priorityFilterValue === MessagePriority.HIGH;
      this.quickSearchData.highFlag = this.structureService.flagFilterValue && this.structureService.flagFilterValue === 3;
      this.quickSearchData.mention = !isBlank(this.structureService.filteredMentions);
      this.quickSearchData.unread = this.structureService.unreadMsg === 1 ? true : false;
    }
  }

  ngOnChanges() {
    if (this.resetQuickFilter) {
      this.quickSearchData = JSON.parse(JSON.stringify(this.DEFAULT_SEARCH_DATA));
    }
  }

  onClick(type: string) {
    if (type === 'alert') {
      this.quickSearchData.highPriority = !this.quickSearchData.highPriority;
    } else if (type === 'flag') {
      this.quickSearchData.highFlag = !this.quickSearchData.highFlag;
    } else if (type === 'mention') {
      this.quickSearchData.mention = !this.quickSearchData.mention;
    } else {
      this.quickSearchData.unread = !this.quickSearchData.unread;
    }
    this.quickSearch.emit(this.quickSearchData);
  }
}
