<div class="d-flex quick-filter gap-5px align-items-center justify-content-between">
  <span
    class="alert-fill"
    data-toggle="tooltip"
    data-placement="top"
    title=""
    [ngbTooltip]="tooltipTemplatePriorityHigh"
    (click)="onClick('alert')"
    [ngClass]="quickSearchData.highPriority ? 'bg-danger' : 'bg-secondary'"
  >
  </span>
  <span
  class="fa fa-stack-2x fa-flag"
  data-toggle="tooltip"
  data-placement="top"
  [ngbTooltip]="tooltipTemplateHigh"
  title=""
  (click)="onClick('flag')"
  [ngClass]="quickSearchData.highFlag ? 'text-danger' : 'text-secondary'"
>
</span>
  <span
    class="at-fill"
    data-toggle="tooltip"
    data-placement="top"
    title=""
    [ngbTooltip]="tooltipTemplateMentions"
    (click)="onClick('mention')"
    [ngClass]="quickSearchData.mention ? 'bg-danger' : 'bg-secondary'"
  >
  </span>
  <span *ngIf="isInbox"
    class="fa fa-envelope unread-icon"
    data-toggle="tooltip"
    data-placement="top"
    title=""
    [ngbTooltip]="tooltipTemplateUnreadMsg"
    (click)="onClick('unread')"
    [ngClass]="quickSearchData.unread ? 'text-danger' : 'text-secondary'"
  >
  </span>
</div>
<ng-template #tooltipTemplateHigh>
  <app-ch-tooltip [content]="'FLAG.HIGH' | translate"></app-ch-tooltip>
</ng-template>
<ng-template #tooltipTemplatePriorityHigh>
  <app-ch-tooltip [content]="'PRIORITIES.HIGH' | translate"></app-ch-tooltip>
</ng-template>
<ng-template #tooltipTemplateMentions>
  <app-ch-tooltip [content]="'TOOLTIPS.MY_MENTIONS' | translate"></app-ch-tooltip>
</ng-template>
<ng-template #tooltipTemplateUnreadMsg>
  <app-ch-tooltip [content]="'TOOLTIPS.UNREAD_MSG' | translate"></app-ch-tooltip>
</ng-template>
