import { Injectable } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import 'rxjs/add/operator/toPromise';
import { Router, NavigationEnd } from '@angular/router';
import { isBlank } from 'app/utils/utils';
import { HttpService } from 'app/services/http/http.service';
import { APIs } from 'app/constants/apis';
import { Observable } from 'rxjs';
import {
  ChatroomPayloadData,
  CreateChatroomData,
  CreateChatroomParameters,
  defaultCreateChatroomParameters
} from 'app/models/message-center/messageCenter';
import { ChatWithTypes } from 'app/constants/constants';
import { SharedService } from '../shared/sharedServices';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
// TODO: Revamp this service CHP-13394
declare const $: any;
declare const NProgress: any;
@Injectable()
export class InboxService {
  inboxListing = '';
  NewChatRoomOnPollingAPI = '';
  inboxData = '';
  searchApi = '';
  deleteChatMessagesApi = '';
  deleteMaskedMessageApi = '';
  deleteSignatureDocApi = '';
  deleteInventoryCountApi = '';
  getUsersListByRoleIdApi = '';
  getStaffCountinChatroomApi ='';
  getStaffRolesListByTenantApi = '';
  getRoleDetailsInitApi = '';
  queryParamStatus = '';
  reRouteApi = '';
  getAllMessageGroupsApi;
  getAllMessageGroupNamesApi;
  getPrimaryClinicianApi;
  config:any = {};
  userDetails;
  configData:any = {};
  siteConfig;
  public userData;
  uploadChatSignatureApi;
  getTagoptionsApi;
  getAlternatecontactsApi;
  uploadChatTagsApi;
  getTenantCliniciansApi;
  markallMessageAsReadApi;
  archiveInboxItemsApi;
  deliveryStatusFetchApi;
  updateDeliveryStatusApi;
  updateApplessStatusApi;
  messageToggleApi;
  data = {};
  needVirtualPatient = false;
  masterData;
  masterConfigData;
  masterSchedulerData;
  masterEscalatedSchedulerData;
  masterStaffsAvalable;
  defaultMasterStaffsAvalable;
  masterClinicianRolesAvaiable = '';
  masterDefaultClinicianRolesAvaiable = '';
  public tempData = 'test';
  patientCaregiverMessageRoutingApi;
  constructor(
    private _http: Http,
    private router: Router,
    public _structureService: StructureService,
    public _SharedService: SharedService,
    private httpService: HttpService,
    private toolTipService: ToolTipService
  ) {
    router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        if (event.url === '/blank') {
          this.router.navigate(['/inbox/chatroom']);
        }
      }
    });

    this.NewChatRoomOnPollingAPI = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/get-new-chat-message-thread-on-polling.php';
    this.deleteChatMessagesApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/delete-chatroom-messages.php';
    this.deleteMaskedMessageApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/archive-masked-message.php';
    this.deleteSignatureDocApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/delete-signature-doc.php';
    this.deleteInventoryCountApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/delete-inventory-count.php';
    this.getUsersListByRoleIdApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/get-tenant-users-by-roleid.php';
    this.getStaffRolesListByTenantApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/get-roles-of-tenants-with-active-users.php';
    this.getRoleDetailsInitApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/get-role-details-init.php';
    this.reRouteApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/reroute-message.php';
    this.getAllMessageGroupsApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/get-all-message-groups.php';
    this.getAllMessageGroupNamesApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/get-all-message-group-names.php';
    this.getPrimaryClinicianApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/get-primaryCare-clinicians.php';
    this.uploadChatSignatureApi =  this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/chat-signature-upload.php';
    this.getTagoptionsApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/get-all-tags.php';
    this.getAlternatecontactsApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/alternate-contacts.php';
    this.uploadChatTagsApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/chat-signature-upload.php';
    this.getTenantCliniciansApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/get-users-by-tenantid.php';
    this.markallMessageAsReadApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/update-marked-message-status.php';
    this.archiveInboxItemsApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/archive-inbox-items.php';
    this.getStaffCountinChatroomApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/get-accessible-users-count-in-chatroom.php';
    this.deliveryStatusFetchApi=this._structureService.apiBaseUrl+'citus-health/'+this._structureService.version+'/get-delivered-msg.php';
    this.updateDeliveryStatusApi=this._structureService.apiBaseUrl+'citus-health/'+this._structureService.version+'/get-undelivered-msg-and-update.php';
    this.patientCaregiverMessageRoutingApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/start-new-chat.php';
    this.updateApplessStatusApi = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/update-appless-video-status.php';    
    this.messageToggleApi = this._structureService.apiBaseUrl + 'citus-health/v5/appless-chat-mode.php';
    this.config = this._structureService.userDataConfig;
    this.userDetails = this._structureService.userDetails;
    this.configData = JSON.parse(this.config);
    this.userData = JSON.parse(this.userDetails);

    if (this.userData && this.userData.group === "3" && this.userData.isMaster === "0" && this.userData.master_details) {
      this.masterData = this.userData.master_details;
      this.masterConfigData = this.userData.master_config;
      this.masterSchedulerData = this.userData.masterSchedulerData;
      this.masterEscalatedSchedulerData = this.userData.masterEscalatedSchedulerData;
    }
    
}
  setLocalStorageData() {
    this.config = this._structureService.userDataConfig;
    this.userDetails = this._structureService.userDetails;
    this.configData = JSON.parse(this.config);
    this.userData = JSON.parse(this.userDetails);
  }

  deliveryStatusFetch(data) {
    const apiConfig = {
      requestType: 'http',
      url: this.deliveryStatusFetchApi,
      data
    };
    return this._structureService.requestData(apiConfig);
  }
  updateDeliveryStatus(data){
    console.log("updateDeliveryStatus", data);
    var apiConfig = {
      requestType: 'http',
      url: this.updateDeliveryStatusApi,
      data: data
    };

  return this._structureService.requestData(apiConfig)
  }
  getNewChatRoomOnPolling(pollingData, qlimit=0, pageCount=0, flagVlaue = 0) {
    if(localStorage.getItem('qLimit')) {
      qlimit = parseInt(localStorage.getItem('qLimit'));
    } else {
      qlimit = 0;
    }
    this.setLocalStorageData();
    var showChatHistory = false;
    if (this.configData.show_chat_history_to_new_participant && this.configData.show_chat_history_to_new_participant == '1') {
      showChatHistory = true;
    }
    var data = "toId=" + this._structureService.getCookie('userId') + "&tenantId=" + this._structureService.getCookie('tenantId') + "&archived=0" + "&showChatHistory=" + showChatHistory + "&formInbox=" + true + "&flagVlaue=" + flagVlaue;
    data = data + "&chatroomId=" + pollingData.args.chatroomId;
    if(pollingData.args.messageId) {
      data = data + "&messageId=" + pollingData.args.messageId;
    }
    if(pollingData.args.meessageFromMaskedChild) {
      data = data + "&meessageFromMaskedChild=" + pollingData.args.meessageFromMaskedChild
    }
    if (pageCount) { 
      data = data + "&pageCount="+pageCount;
    } else {
      data = data + "&pageCount=0";
    }
    if (qlimit!=0) {
        data = data + "&qlimit="+qlimit;
    }
    data = data + "&messagePolling=1";
    if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) {
      data = data+"&crossTenantId=" + this._structureService.getCookie('crossTenantId');
    }
    console.log(data)
    var apiConfig = { url: this.NewChatRoomOnPollingAPI, requestType: 'http', data: data, noLoader:true };
    console.log(apiConfig)
    return this._structureService.requestData(apiConfig);  
  }


  deleteMaskedMessages(data) {
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({headers: headers});
    const promise = new Promise( (resolve, reject)=> {
      const apiURL = this.deleteMaskedMessageApi
      this._http.post(apiURL, data, options)
        .toPromise()
          .then( (res)=> {
            const inboxDetails = res.json();
            resolve(inboxDetails);
          })
    });
    return promise;
  }
  
  getStaffCountInChatroom(chatRoomId) {
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({
      headers: headers
    });
    const promise: any = new Promise((resolve, reject) => {
      this.data = `?targetId=${chatRoomId}&status=notRejected&responseType=count`;
      const apiURL = `${this._structureService.apiBaseUrl}${APIs.getRoomUsers}${this.data}`;
            this._http.get(apiURL, options)
        .toPromise()
        .then(
        res => {
          const chatroomData = res.json();
          resolve(chatroomData);
        }
        ).catch((error) => {
          reject(error);
        });
    });
    return promise;
  }
  /**
   * 
   * @param chatroomId number, required
   * @param type number, expecting 1 - Self archive || 2 - All users
   * @returns promise of type any
   */
  deleteChatroomMessages(chatroomId: number, type: number) {
    const promise: any = new Promise((resolve, reject) => {
      this.httpService
        .doPut(APIs.deleteChatroomMessage, { chatroomId: +chatroomId, type })
        .toPromise()
        .then((res) => {
          resolve(res);
        })
        .catch((ex) => {
          reject(ex);
        });
    });
    return promise;
  }

  deleteSignatureDoc(docId, deletedBy) {
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
      const apiURL = this.deleteSignatureDocApi;

      this.data = "docId=" + docId + "&deletedBy=" + deletedBy
      this._http.post(apiURL, this.data, options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          resolve(result);
        }
        );
    });
    return promise;

  }

  deleteInventoryCount(data) {
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
      const apiURL = this.deleteInventoryCountApi;
      this._http.post(apiURL, data, options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          resolve(result);
        }
        );
    });
    return promise;
  }
  getUsersListByRoleId(roleId, tenantId, isTenantRoles,needUserPrivileges=false, pageCount=undefined, excludeRoleId=undefined, searchValue=undefined, chatWithFilterTenantId=undefined, excludeLogginedUser=undefined,messageGroupMemberIds ='',optionShow='',mySites="0",isSchedule=false) {
    let headers = new Headers();
    this.userData = JSON.parse(this._structureService.userDetails)?JSON.parse(this._structureService.userDetails):[];
    console.log(mySites);
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({
      headers: headers
    });
    // if(mySites == '') {
    //   mySites=  localStorage.getItem('siteId');
    // }
  //  const promise = new Promise((resolve, reject) => {
      const apiURL = this.getUsersListByRoleIdApi;
      if (tenantId) {
        this.queryParamStatus = "";
      } else {
        this.queryParamStatus = "&status=notRejected";
      }
      var queryParamRoleId = "";
      var queryParamMySites = "";
      if (roleId) {
        queryParamRoleId = "&roleId=" + roleId;
      }
      if (!isBlank(mySites)) {
        queryParamMySites = "&siteIds=" + mySites;
      }
      var queryParammemberId = "";
      if (messageGroupMemberIds) {
        queryParammemberId = "&messageGroupMemberIds=" + messageGroupMemberIds;
      }
      var optionShowSection = "";
      if (optionShow) {
        optionShowSection = "&optionShow=" + optionShow;
      }
      
      var queryPrivileges = "";
      if (needUserPrivileges) {
        queryPrivileges = "&getRolePrivileges=" + true;
      }
      var queryParamExcludeRoleId = "";
      var queryParamExcludeLogginedUser = "";
      var queryParamPageCount = "";
      var queryParamSearchValue = "";
      var queryParamChatWithFilterTenantId = "";
      if(typeof(excludeRoleId) != "undefined") {
        queryParamExcludeRoleId = "&excludeRoleId="+excludeRoleId
      }
      if(typeof(excludeLogginedUser) != "undefined" && excludeLogginedUser) {
        queryParamExcludeLogginedUser = "&excludeLogginedUser="+this.userData.userId;
      }
      if(typeof(pageCount) != "undefined") {
        queryParamPageCount = "&pageCount="+pageCount  
      }
      if(typeof(searchValue) != 'undefined') {
        queryParamSearchValue = "&searchKeyword="+searchValue
      }
      if(chatWithFilterTenantId && typeof(chatWithFilterTenantId) != 'undefined') {
        queryParamChatWithFilterTenantId = "&chatWithFilterTenantId="+chatWithFilterTenantId
      }
      if (this.userData.config.enable_appless_video_chat == '1') { //Appless videoconfiguration is enabled for tenant
        this.needVirtualPatient = true;
      } else {
        this.needVirtualPatient = false;
      }
      this.data = queryParamRoleId + queryParamMySites + this.queryParamStatus + "&isTenantRoles=" + isTenantRoles +  "&needVirtualPatients="+this.needVirtualPatient + queryPrivileges + queryParamPageCount + queryParamExcludeRoleId + queryParamExcludeLogginedUser + queryParamSearchValue + optionShowSection + queryParamChatWithFilterTenantId + queryParammemberId;
      console.log(this.data);
      this.data = this.data + "&isFromChat=1";
      const userData = this._structureService.getUserdata();
      this.data += (userData.nursing_agencies !="")?"&nursingAgencies="+userData.nursing_agencies:"";
      this.data += "&userGroup="+userData.group;
      this.data += "&isSchedule="+isSchedule;

      if(userData.accessSecurityEnabled && optionShow == 'patient') {
        this.data += "&accessSecurityEnabled="+userData.accessSecurityEnabled;
        this.data += "&accessSecurityEsiValue="+userData.accessSecurityEsiValue;
        this.data += "&accessSecurityIdentifierType="+userData.accessSecurityIdentifierType;
        this.data += "&accessSecurityType="+userData.accessSecurityType;
      }
      console.log(this.data);
      console.log("get-tenant-users-by-roleid.php : inbox service getUsersListByRoleId ==================")
      var apiConfig = {url: this.getUsersListByRoleIdApi, requestType: 'http', data: this.data};
      return this._structureService.requestData(apiConfig);
      // this._http.get(apiURL + this.data, options)
      //   .toPromise()
      //   .then(
      //   res => {
      //     const userDetails = res.json();
      //     resolve(userDetails);
      //   }
      //   );
   // });
   // return promise;    
  }

  getMaskedMessageRecipientList(recipientRoles, tenantId, isTenantRoles,siteId) {
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({
      headers: headers
    });
    const promise = new Promise((resolve, reject) => {
      const apiURL = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/get-masked-message-recipients.php';

      if (tenantId) {
        this.queryParamStatus = "";
      } else {
        this.queryParamStatus = "?status=notRejected";
        tenantId = this._structureService.getCookie('tenantId');
      }
      
      this.data = this.queryParamStatus + "&needVirtualPatients="+this.needVirtualPatient;
      const userData = this._structureService.getUserdata();

      this.data += "&recipientRoles="+recipientRoles;
     const id = (siteId != '0') ? siteId.join(",") : siteId;
      this.data += "&siteIds="+id;
      if (userData.accessSecurityEnabled) {
        this.data += "&accessSecurityEnabled=" + userData.accessSecurityEnabled;
        this.data += "&accessSecurityEsiValue=" + userData.accessSecurityEsiValue;
        this.data += "&accessSecurityIdentifierType=" + userData.accessSecurityIdentifierType;
        this.data += "&accessSecurityType=" + userData.accessSecurityType;
      }
      this._http.get(apiURL + this.data, options)
        .toPromise()
        .then(
        res => {
          const userDetails = res.json();
          resolve(userDetails);
        }
        );
    });
    return promise;
  }

  /***Get Staff roles with users****/
  getStaffRolesListByTenantId(tenantId,optionShow='',selectSiteIds='') {
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({
      headers: headers
    });
    const promise = new Promise((resolve, reject) => {
      const apiURL = this.getStaffRolesListByTenantApi;
      this.data = "?optionShow="+optionShow;
      if( selectSiteIds !='')  this.data += "&siteId=" + selectSiteIds ; 
     
      const userData = this._structureService.getUserdata();
      this.data += (userData.nursing_agencies !="")?"&nursingAgencies="+userData.nursing_agencies:"";
      console.log("get-tenant-users-by-roleid.php : inbox service getUsersListByRoleId ==================")
      this._http.get(apiURL + this.data, options)
        .toPromise()
        .then(
        res => {
          const userDetails = res.json();
          resolve(userDetails);
        }
        );
    });
    return promise;
  }
  getRoleDetailsInit(role_id="",selectSiteId="") {
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({
      headers: headers
    });
    console.log("this.selectSiteId",selectSiteId)
    const promise = new Promise((resolve, reject) => {
      const apiURL = this.  getRoleDetailsInitApi;
      this.data = "?roleid=" + role_id + "&site_id=" + selectSiteId; 
      const userData = this._structureService.getUserdata();
      console.log("get-role-details-init.php : inbox service getUsersListByRoleId ==================")
      NProgress.start();

      this._http.get(apiURL + this.data, options)
        .toPromise()
        .then(
        res => {
          const roleDetails = res.json();
          NProgress.done();
          resolve(roleDetails);
        }
        );
    });
    return promise;
  }
  getAllMessageGroups(tenantId, userId, isPrivilege=false,pdgId="",start=0,limit=null,searchKeyword="",selectedMembers=[],message=false, selectedAgency = "", isPdg = "",selectSiteIds='0') {
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
      const apiURL = this.getAllMessageGroupsApi;

      this.data = "?pdgId="+pdgId+"&selectedMembers="+selectedMembers.toString()+"&message="+message;
      if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) {
        this.data += "&crossTenantId=" + this._structureService.getCookie('crossTenantId') 
      }
      if(limit){
        this.data += "&start="+start+"&limit="+limit;
      }      
      if(isPdg){
        this.data += "&isPDG="+isPdg;
      } 
      if(!isBlank(selectSiteIds)) {
        this.data += "&siteIds="+selectSiteIds;
      }
      if(searchKeyword) {
        this.data += "&searchKeyword="+encodeURIComponent(searchKeyword);
      }
      const userData = this._structureService.getUserdata();
      if(this._structureService.getCookie('isFromInviteChat') && this._structureService.getCookie('isFromInviteChat') !='undefined' && this._structureService.getCookie('isFromInviteChat') == '1') {
        this.data += "&isFromInviteChat=1"
      }
      if(selectedAgency != "") {
        this.data += "&selectedAgency="+selectedAgency;
      }

      if(userData.accessSecurityEnabled  && isPdg == '1') {
        this.data += "&accessSecurityEnabled="+userData.accessSecurityEnabled;
        this.data += "&accessSecurityEsiValue="+userData.accessSecurityEsiValue;
        this.data += "&accessSecurityIdentifierType="+userData.accessSecurityIdentifierType;
        this.data += "&accessSecurityType="+userData.accessSecurityType;
      }

      NProgress.start();
      this._http.get(apiURL + this.data, options)
        .toPromise()
        .then(
        res => {
          const messageGroup = res.json();
          NProgress.done();
          resolve(messageGroup);
        }
        );
    });
    return promise;

  }
  getAllMessageGroupNames(tenantId, userId, isPrivilege=false,pdgId="",start=0,limit=null,searchKeyword="",selectedMembers=[],message=false, selectedAgency = "", isPdg = "",siteId="0") {
    var userDetails = JSON.parse(this._structureService.userDetails);
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
      const apiURL = this.getAllMessageGroupNamesApi;

      this.data = "?isPrivilege="+(isPrivilege?isPrivilege:'')+"&pdgId="+pdgId+"&selectedMembers="+selectedMembers.toString()+"&message="+message;
      if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) {
        this.data += "&crossTenantId=" + this._structureService.getCookie('crossTenantId') 
      }
      if(limit){
        this.data += "&start="+start+"&limit="+limit;
      }      
      if(isPdg){
        this.data += "&isPDG="+isPdg;
      } 
      if(searchKeyword) {
        this.data += "&searchKeyword="+encodeURIComponent(searchKeyword);
      }
      if(siteId !== undefined && userDetails.config.enable_multisite == '1') {
        this.data += "&siteIds="+siteId;
      } else {
        this.data += "&siteIds=0";
      }
      const userData = this._structureService.getUserdata();
      this.data += (userData.nursing_agencies !="")?"&nursingAgencies="+userData.nursing_agencies:"";
      if(this._structureService.getCookie('isFromInviteChat') && this._structureService.getCookie('isFromInviteChat') !='undefined' && this._structureService.getCookie('isFromInviteChat') == '1') {
        this.data += "&isFromInviteChat=1"
      }
      if(selectedAgency != "") {
        this.data += "&selectedAgency="+selectedAgency;
      }
      if(userData.accessSecurityEnabled  && isPdg == '1') {
        this.data += "&accessSecurityEnabled="+userData.accessSecurityEnabled;
        this.data += "&accessSecurityEsiValue="+userData.accessSecurityEsiValue;
        this.data += "&accessSecurityIdentifierType="+userData.accessSecurityIdentifierType;
        this.data += "&accessSecurityType="+userData.accessSecurityType;
      }
      NProgress.start();
      this._http.get(apiURL + this.data, options)
        .toPromise()
        .then(
        res => {
          const messageGroup = res.json();
          NProgress.done();
          resolve(messageGroup);
        }
        );
    });
    return promise;

  }

  public scheduleSelectionFilter(user) {
    var d = new Date();
    var currentHour = d.getHours();
    var currentMinutes = d.getMinutes();
    var currentInterval = currentHour + ":" + currentMinutes;
    var objRes;
    var availableUser;
    if (this.userData && this.userData.schedulerData) {
      var scheduleJson = this.userData.schedulerData[user.userId];
      if (scheduleJson) {
        objRes = JSON.parse(scheduleJson);
        if (objRes) {
          availableUser = this.checkSchedule(objRes, currentInterval);
          if (availableUser) {
            return 1;
          } else {
            return 0;
          }
        } else {
          return 0;
        }
      } else {
        return 0;
      }
    }
  }

  getMinutes(currentTime, scheduleInterval) {
    var currentTimesplit = currentTime.split(':');
    return currentTimesplit[0] * 60 + currentTimesplit[1] * 1;
  }

  checkSchedule(scheduleArray, currentTime) {
    var scheduleInterval = 30;
    var currentMin = this.getMinutes(currentTime, scheduleInterval);
    return scheduleArray.some(function (result) {
      var currentTimesplit = result.split(':');
      var getMin = currentTimesplit[0] * 60 + currentTimesplit[1] * 1;
      //var getMin = this.getMinutes(result, scheduleInterval);
      return getMin <= currentMin && currentMin <= getMin + scheduleInterval;
    });
  }

  clientToGmtTime(timeString, reverse, socketTimeZone, activeTimezone) {
    console.log("this._structureService.timezone", this._structureService.timezone);
    var hours, minutes, addHours, addMinutes, newMinutes, newHours;
    var currentTime = new Date();
    var currentTimezone;
    if (activeTimezone) {
      currentTimezone = activeTimezone * -1;
    } else {
      currentTimezone = currentTime.getTimezoneOffset();
    }


    console.log("currentTimezone", currentTimezone);
    currentTimezone = (currentTimezone / 60) * -1;

    let currentTimezoneTimeGap = <any>[];

    currentTimezoneTimeGap = (currentTimezone + '').split('.');
    if (currentTimezoneTimeGap[1] == 5) {
      currentTimezoneTimeGap[1] = 30;
    } else {
      currentTimezoneTimeGap[1] = 0;
    }
    var match = /(\d+):(\d+)/.exec(timeString);
    if (!reverse) {
      addHours = Number(currentTimezoneTimeGap[0] * -1) + (this._structureService.timezone);
      if (currentTimezone > 0) {
        addMinutes = Number(currentTimezoneTimeGap[1] * -1);
      } else {
        addMinutes = Number(currentTimezoneTimeGap[1]);
      }
    } else {
      if (socketTimeZone) {
        addHours = socketTimeZone - (this._structureService.timezone);
        addMinutes = 0;
      } else {
        addHours = Number(currentTimezoneTimeGap[0]) - (this._structureService.timezone);//Need Dynamic
        if (currentTimezone > 0) {
          addMinutes = Number(currentTimezoneTimeGap[1]);
        } else {
          addMinutes = Number(currentTimezoneTimeGap[1] * -1);
        }
      }
    }
    //console.log(addHours,addMinutes);
    hours = parseInt(match[1], 10);
    minutes = parseInt(match[2], 10);
    var hoursCorrection = 0;
    if ((hours + addHours) <= 0) {
      hoursCorrection = 24;
    }
    //console.log(hoursCorrection);
    newMinutes = ((hours + addHours + hoursCorrection) * 60) + minutes + addMinutes;
    newHours = Math.floor(newMinutes / 60) % 24;
    if (newHours < 10) {
      newHours = '0' + newHours;
    }
    newMinutes %= 60;
    var minuteString = (newMinutes >= 10 ? '' : '0') + newMinutes;
    //console.log('converted time-->' + newHours + ':' + minuteString);
    return newHours + ':' + minuteString;
  }
  checkBranchHours() {
    
    this.configData = JSON.parse(this._structureService.userDataConfig);
    let response: any = {};
    response = {
      isWorkingHours: false,
      homeInfusionStartTimeHours: '',
      homeInfusionStartTimeMinutes: '',
      homeInfusionEndTimeHours: '',
      homeInfusionEndTimeMinutes: ''
    };
    var currentDate = new Date();
    var currentDay = currentDate.getDay();
    var branchWorkingDay = this.configData.branch_working_days || "1,2,3,4,5,6,0";
    var currentHours = currentDate.getHours();
    var currentMinutes = currentDate.getMinutes();
    var homeInfusionStartTime = this.configData.branch_start_time || "9:0";
    var homeInfusionEndTime = this.configData.branch_end_time || "6:0";

    console.log(homeInfusionStartTime, homeInfusionEndTime);


    homeInfusionStartTime = this.clientToGmtTime(homeInfusionStartTime, true, '', '');
    homeInfusionEndTime = this.clientToGmtTime(homeInfusionEndTime, true, '', '');

    console.log("---------------------");
    console.log(currentHours, currentMinutes);
    console.log(homeInfusionStartTime, homeInfusionEndTime);
    console.log("---------------------");
    if (homeInfusionStartTime.indexOf(":") !== -1) {
      response.homeInfusionStartTimeHours = homeInfusionStartTime.split(":")[0] * 1;
      response.homeInfusionStartTimeMinutes = homeInfusionStartTime.split(":")[1] * 1;
    } else {
      response.homeInfusionStartTimeHours = 9;
      response.homeInfusionStartTimeMinutes = 0;
    }
    if (homeInfusionEndTime.indexOf(":") !== -1) {
      response.homeInfusionEndTimeHours = homeInfusionEndTime.split(":")[0] * 1;
      response.homeInfusionEndTimeMinutes = homeInfusionEndTime.split(":")[1] * 1;
    } else {
      response.homeInfusionEndTimeHours = 6;
      response.homeInfusionEndTimeMinutes = 0;
    }

    //this.configData
    if (branchWorkingDay.indexOf(currentDay) != -1) {
    if (response.homeInfusionEndTimeHours < response.homeInfusionStartTimeHours) {
      if ((currentHours > response.homeInfusionStartTimeHours || (currentHours === response.homeInfusionStartTimeHours && currentMinutes >= response.homeInfusionStartTimeMinutes)) ||
        (currentHours < response.homeInfusionEndTimeHours || (response.homeInfusionEndTimeHours === currentHours && currentMinutes <= response.homeInfusionEndTimeMinutes))) {
        response.isWorkingHours = true;
      }
    } else {
      if ((currentHours > response.homeInfusionStartTimeHours || (currentHours === response.homeInfusionStartTimeHours && currentMinutes >= response.homeInfusionStartTimeMinutes)) &&
        (currentHours < response.homeInfusionEndTimeHours || (response.homeInfusionEndTimeHours === currentHours && currentMinutes <= response.homeInfusionEndTimeMinutes))) {
        response.isWorkingHours = true;
      }
    }
    }
    console.log("response.isWorkingHours=====>>>>>>>>", response.isWorkingHours);
    return response;
  }
  checkSiteHours() {
    this.siteConfig = JSON.parse(this._structureService.userDetails);
    let response: any = {};
    response = {
      isWorkingHours: false,
      homeInfusionStartTimeHours: '',
      homeInfusionStartTimeMinutes: '',
      homeInfusionEndTimeHours: '',
      homeInfusionEndTimeMinutes: ''
    };
    var currentDate = new Date();
    var currentDay = currentDate.getDay();
    var branchWorkingDay = this.siteConfig.siteConfigs.branch_working_days || "1,2,3,4,5,6,0";
    var currentHours = currentDate.getHours();
    var currentMinutes = currentDate.getMinutes();
    var homeInfusionStartTime = this.siteConfig.siteConfigs.branch_start_time || "9:0";
    var homeInfusionEndTime = this.siteConfig.siteConfigs.branch_end_time || "6:0";

    console.log(homeInfusionStartTime, homeInfusionEndTime);


    // homeInfusionStartTime = this.clientToGmtTime(homeInfusionStartTime, true, '', '');
    // homeInfusionEndTime = this.clientToGmtTime(homeInfusionEndTime, true, '', '');

    console.log("---------------------");
    console.log(currentHours, currentMinutes);
    console.log(homeInfusionStartTime, homeInfusionEndTime);
    console.log("---------------------");
    if (homeInfusionStartTime.indexOf(":") !== -1) {
      response.homeInfusionStartTimeHours = homeInfusionStartTime.split(":")[0] * 1;
      response.homeInfusionStartTimeMinutes = homeInfusionStartTime.split(":")[1] * 1;
    } else {
      response.homeInfusionStartTimeHours = 9;
      response.homeInfusionStartTimeMinutes = 0;
    }
    if (homeInfusionEndTime.indexOf(":") !== -1) {
      response.homeInfusionEndTimeHours = homeInfusionEndTime.split(":")[0] * 1;
      response.homeInfusionEndTimeMinutes = homeInfusionEndTime.split(":")[1] * 1;
    } else {
      response.homeInfusionEndTimeHours = 6;
      response.homeInfusionEndTimeMinutes = 0;
    }

    //this.configData
    if (branchWorkingDay.indexOf(currentDay) != -1) {
    if (response.homeInfusionEndTimeHours < response.homeInfusionStartTimeHours) {
      if ((currentHours > response.homeInfusionStartTimeHours || (currentHours === response.homeInfusionStartTimeHours && currentMinutes >= response.homeInfusionStartTimeMinutes)) ||
        (currentHours < response.homeInfusionEndTimeHours || (response.homeInfusionEndTimeHours === currentHours && currentMinutes <= response.homeInfusionEndTimeMinutes))) {
        response.isWorkingHours = true;
      }
    } else {
      if ((currentHours > response.homeInfusionStartTimeHours || (currentHours === response.homeInfusionStartTimeHours && currentMinutes >= response.homeInfusionStartTimeMinutes)) &&
        (currentHours < response.homeInfusionEndTimeHours || (response.homeInfusionEndTimeHours === currentHours && currentMinutes <= response.homeInfusionEndTimeMinutes))) {
        response.isWorkingHours = true;
      }
    }
    }
    console.log("response.isWorkingHours=====>>>>>>>>", response.isWorkingHours);
    return response;
  }


  checkMasterBranchHours() {    
    let response: any = {};

    response = {
      isWorkingHours: false,
      homeInfusionStartTimeHours: '',
      homeInfusionStartTimeMinutes: '',
      homeInfusionEndTimeHours: '',
      homeInfusionEndTimeMinutes: ''
    };

    var currentDate = new Date();
    var currentDay = currentDate.getDay();
    var branchWorkingDay = this.userData.master_config.branch_working_days || "1,2,3,4,5,6,0";
    var currentHours = currentDate.getHours();
    var currentMinutes = currentDate.getMinutes();
    var homeInfusionStartTime = this.userData.master_config.branch_start_time || "9:0";
    var homeInfusionEndTime = this.userData.master_config.branch_end_time || "6:0";

    console.log(homeInfusionStartTime, homeInfusionEndTime);


    homeInfusionStartTime = this.clientToGmtTime(homeInfusionStartTime, true, '', '');
    homeInfusionEndTime = this.clientToGmtTime(homeInfusionEndTime, true, '', '');

    console.log("---------------------");
    console.log(currentHours, currentMinutes);
    console.log(homeInfusionStartTime, homeInfusionEndTime);
    console.log("---------------------");

    if (homeInfusionStartTime.indexOf(":") !== -1) {
      response.homeInfusionStartTimeHours = homeInfusionStartTime.split(":")[0] * 1;
      response.homeInfusionStartTimeMinutes = homeInfusionStartTime.split(":")[1] * 1;
    } else {
      response.homeInfusionStartTimeHours = 9;
      response.homeInfusionStartTimeMinutes = 0;
    }
    if (homeInfusionEndTime.indexOf(":") !== -1) {
      response.homeInfusionEndTimeHours = homeInfusionEndTime.split(":")[0] * 1;
      response.homeInfusionEndTimeMinutes = homeInfusionEndTime.split(":")[1] * 1;
    } else {
      response.homeInfusionEndTimeHours = 6;
      response.homeInfusionEndTimeMinutes = 0;
    }

    if (branchWorkingDay.indexOf(currentDay) != -1) {
      if (response.homeInfusionEndTimeHours < response.homeInfusionStartTimeHours) {
        if ((currentHours > response.homeInfusionStartTimeHours || (currentHours === response.homeInfusionStartTimeHours && currentMinutes >= response.homeInfusionStartTimeMinutes)) ||
          (currentHours < response.homeInfusionEndTimeHours || (response.homeInfusionEndTimeHours === currentHours && currentMinutes <= response.homeInfusionEndTimeMinutes))) {
          response.isWorkingHours = true;
        }
      } else {
        if ((currentHours > response.homeInfusionStartTimeHours || (currentHours === response.homeInfusionStartTimeHours && currentMinutes >= response.homeInfusionStartTimeMinutes)) &&
          (currentHours < response.homeInfusionEndTimeHours || (response.homeInfusionEndTimeHours === currentHours && currentMinutes <= response.homeInfusionEndTimeMinutes))) {
          response.isWorkingHours = true;
        }
      }
    }

    console.log("response.isWorkingHours=====>>>>>>>>", response.isWorkingHours);
    return response;
  }

  checkInfusionHours(ignoreBranchHour) {
    
    this.configData = JSON.parse(this._structureService.userDataConfig);
    let requiredConfig: any = {
      workingHour: this.configData.working_hour,
      branchWorkingDays: this.configData.branch_working_days || "1,2,3,4,5,6,0",
      chatStartTime: this.configData.home_infusion_start_time || "9:0",
      chatEndTime: this.configData.home_infusion_end_time || "6:0"
    };
    if(!ignoreBranchHour) {
      requiredConfig.branchStartTime = this.configData.branch_start_time || "9:0";
      requiredConfig.branchEndTime = this.configData.branch_end_time || "6:0";  
    }
  
    if(this.configData.enable_multisite === '1') {
      const siteConfig = JSON.parse(this._structureService.userDetails).siteConfigs;
      requiredConfig = {
        workingHour: siteConfig.working_hour,
        branchWorkingDays: siteConfig.branch_working_days || "1,2,3,4,5,6,0",
        chatStartTime: siteConfig.chat_start_time || "9:0",
        chatEndTime: siteConfig.chat_end_time || "6:0",
      };
      if(!ignoreBranchHour) {
        requiredConfig.branchStartTime = siteConfig.branch_start_time || "9:0";
        requiredConfig.branchEndTime = siteConfig.branch_end_time || "6:0";  
      }
    }
    let response: any = {
      isWorkingHours: false,
      homeInfusionStartTimeHours: '',
      homeInfusionStartTimeMinutes: '',
      homeInfusionEndTimeHours: '',
      homeInfusionEndTimeMinutes: ''
    };
    if(!ignoreBranchHour) {
      response.branchStartTimeHours = '';
      response.branchStartTimeMinutes = '';
      response.branchEndTimeHours = '';
      response.branchEndTimeMinutes = '';
    }
    const currentDate = new Date();
    const currentDay = currentDate.getDay();
    const currentHours = currentDate.getHours();
    const currentMinutes = currentDate.getMinutes();
    let homeInfusionStartTime = requiredConfig.chatStartTime;
    let homeInfusionEndTime = requiredConfig.chatEndTime;
    let branchStartTime = !ignoreBranchHour ? requiredConfig.branchStartTime : '';
    let branchEndTime = !ignoreBranchHour ? requiredConfig.branchEndTime : '';

    console.log(homeInfusionStartTime, homeInfusionEndTime);


    homeInfusionStartTime = this.clientToGmtTime(homeInfusionStartTime, true, '', '');
    homeInfusionEndTime = this.clientToGmtTime(homeInfusionEndTime, true, '', '');
    if(!ignoreBranchHour) {
        branchStartTime = this.clientToGmtTime(branchStartTime, true, '', '');
        branchEndTime = this.clientToGmtTime(branchEndTime, true, '', '');
    } 
    

    console.log("---------------------");  console.log(currentHours, currentMinutes); console.log(homeInfusionStartTime, homeInfusionEndTime);
    let use24HourEnabledBranchHours = false
    if(requiredConfig.workingHour && +requiredConfig.workingHour === 1){
      if (!ignoreBranchHour) {
        use24HourEnabledBranchHours = true;
      } else {
        response.isWorkingHours = true;
        return response;
      }
    }
    console.log("---------------------");
    if(use24HourEnabledBranchHours) {
      homeInfusionStartTime = branchStartTime;
      homeInfusionEndTime = branchEndTime;
    }
    if (homeInfusionStartTime.indexOf(":") !== -1) {
      response.homeInfusionStartTimeHours = homeInfusionStartTime.split(":")[0] * 1;
      response.homeInfusionStartTimeMinutes = homeInfusionStartTime.split(":")[1] * 1;
    } else {
      response.homeInfusionStartTimeHours = 9;
      response.homeInfusionStartTimeMinutes = 0;
    }
    if (homeInfusionEndTime.indexOf(":") !== -1) {
      response.homeInfusionEndTimeHours = homeInfusionEndTime.split(":")[0] * 1;
      response.homeInfusionEndTimeMinutes = homeInfusionEndTime.split(":")[1] * 1;
    } else {
      response.homeInfusionEndTimeHours = 6;
      response.homeInfusionEndTimeMinutes = 0;
    }

 
    
    if (requiredConfig.branchWorkingDays.indexOf(currentDay) !== -1) {
    if (response.homeInfusionEndTimeHours < response.homeInfusionStartTimeHours) {
      if ((currentHours > response.homeInfusionStartTimeHours || (currentHours === response.homeInfusionStartTimeHours && currentMinutes >= response.homeInfusionStartTimeMinutes)) ||
        (currentHours < response.homeInfusionEndTimeHours || (response.homeInfusionEndTimeHours === currentHours && currentMinutes <= response.homeInfusionEndTimeMinutes))) {
        response.isWorkingHours = true;
      }
    } else {
      if ((currentHours > response.homeInfusionStartTimeHours || (currentHours === response.homeInfusionStartTimeHours && currentMinutes >= response.homeInfusionStartTimeMinutes)) &&
        (currentHours < response.homeInfusionEndTimeHours || (response.homeInfusionEndTimeHours === currentHours && currentMinutes <= response.homeInfusionEndTimeMinutes))) {
        response.isWorkingHours = true;
      }
    }
    }
    console.log("response.isWorkingHours=====>>>>>>>>", response.isWorkingHours);
    return response;
  }

  checkMasterInfusionHours(useBranchHour) {
    let response: any = {};

    response = {
      isWorkingHours: false,
      homeInfusionStartTimeHours: '',
      homeInfusionStartTimeMinutes: '',
      homeInfusionEndTimeHours: '',
      homeInfusionEndTimeMinutes: ''
    };

    var currentDate = new Date();
    var currentDay = currentDate.getDay();
    var branchWorkingDay = this.userData.master_config.branch_working_days || "1,2,3,4,5,6,0";
    var currentHours = currentDate.getHours();
    var currentMinutes = currentDate.getMinutes();
    var homeInfusionStartTime = this.userData.master_config.home_infusion_start_time || "9:0";
    var homeInfusionEndTime = this.userData.master_config.home_infusion_end_time || "6:0";
    homeInfusionStartTime = this.clientToGmtTime(homeInfusionStartTime, true, '', '');
    homeInfusionEndTime = this.clientToGmtTime(homeInfusionEndTime, true, '', '');
    if (homeInfusionStartTime.indexOf(":") !== -1) {
      response.homeInfusionStartTimeHours = homeInfusionStartTime.split(":")[0] * 1;
      response.homeInfusionStartTimeMinutes = homeInfusionStartTime.split(":")[1] * 1;
    } else {
      response.homeInfusionStartTimeHours = 9;
      response.homeInfusionStartTimeMinutes = 0;
    }
    if (homeInfusionEndTime.indexOf(":") !== -1) {
      response.homeInfusionEndTimeHours = homeInfusionEndTime.split(":")[0] * 1;
      response.homeInfusionEndTimeMinutes = homeInfusionEndTime.split(":")[1] * 1;
    } else {
      response.homeInfusionEndTimeHours = 6;
      response.homeInfusionEndTimeMinutes = 0;
    }
   

    console.log('check master working hour');
    console.log('currentHours-->', currentHours);
    console.log('currentMinutes-->', currentMinutes);
    console.log('response.homeInfusionStartTimeHoursMaster-->', response.homeInfusionStartTimeHours);
    console.log('response.homeInfusionStartTimeMinutesMaster-->', response.homeInfusionStartTimeMinutes);
    console.log('response.homeInfusionEndTimeHoursMaster-->', response.homeInfusionEndTimeHours);
    console.log('response.homeInfusionEndTimeMinutesMaster-->', response.homeInfusionEndTimeMinutes);   

    if (this.userData.master_config.working_hour && this.userData.master_config.working_hour == 1) {
      if (useBranchHour) {
        response.isWorkingHours = true;
      } else {
        response.isWorkingHours = false;
      }
    } else if (branchWorkingDay.indexOf(currentDay) != -1) {
      if (response.homeInfusionEndTimeHours < response.homeInfusionStartTimeHours) {
        if ((currentHours > response.homeInfusionStartTimeHours || (currentHours === response.homeInfusionStartTimeHours && currentMinutes >= response.homeInfusionStartTimeMinutes)) ||
          (currentHours < response.homeInfusionEndTimeHours || (response.homeInfusionEndTimeHours === currentHours && currentMinutes <= response.homeInfusionEndTimeMinutes))) {
          response.isWorkingHours = true;
        }
      } else {
        if ((currentHours > response.homeInfusionStartTimeHours || (currentHours === response.homeInfusionStartTimeHours && currentMinutes >= response.homeInfusionStartTimeMinutes)) &&
          (currentHours < response.homeInfusionEndTimeHours || (response.homeInfusionEndTimeHours === currentHours && currentMinutes <= response.homeInfusionEndTimeMinutes))) {
          response.isWorkingHours = true;
        }
      }
    }   
    
    console.log("Master: response.isWorkingHours=====>>>>>>>>", response.isWorkingHours);
    return response;
  }

  rerouteMessage(data) {
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });

    const promise = new Promise((resolve, reject) => {
      const apiURL = this.reRouteApi;

      this._http.post(apiURL, data, options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          resolve(result);
        }
        );
    });
    return promise;
  }
  /**To create new chatroom for PDG, MSG and users */
  createChatroom(params: CreateChatroomParameters): Observable<any> {
    this.setLocalStorageData();
    let defaultValues = { ...defaultCreateChatroomParameters, ...params };
    if (defaultValues.createdWith !== 0 || defaultValues.messageGroupId !== 0 || defaultValues.roleId !== 0) {
        if(!isBlank(this.userData.caregiver_userid) && defaultValues.createdByAssociatedId === 0) { 
          defaultValues.createdByAssociatedId = this.userData.caregiver_userid; 
        }    
      return this.httpService.doPost(APIs.createChatroom, this.getCreateChatroomPayload(defaultValues));
    }
    return new Observable<any>();
  }

  getPrimaryCareClinician(primaryCare, tenantId) {
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
      const apiURL = this.getPrimaryClinicianApi;
      var data = { "primaryCare": primaryCare, "tenantId": tenantId };
      this._http.post(apiURL, data, options)
        .toPromise()
        .then(
        res => {
          const users = res.json();
          resolve(users);
        }
        );
    });
    return promise;

  }

  trackUserUnableToContact(catchedAt, cause, associatedUserId='') {
    var filteredMasterStaffs = [];
    var defaultNurses = JSON.parse(localStorage.getItem("defaultNurses"))?JSON.parse(localStorage.getItem("defaultNurses")):[];
    this.masterStaffsAvalable = JSON.parse(localStorage.getItem('masterStaffsAvalable'))?JSON.parse(localStorage.getItem('masterStaffsAvalable')):[];
    this.defaultMasterStaffsAvalable = JSON.parse(localStorage.getItem('defaultMasterStaffsAvalable'))?JSON.parse(localStorage.getItem('defaultMasterStaffsAvalable')):[];
    
    this.userData = JSON.parse(this._structureService.userDetails)?JSON.parse(this._structureService.userDetails):[];
    console.log('defaultNurses', defaultNurses);
    console.log('masterStaffsAvalable', this.masterStaffsAvalable);
    console.log('defaultMasterStaffsAvalable', this.defaultMasterStaffsAvalable);
    
    if(this.userData.group === "3" && this.userData.isMaster === "0" && this.userData.master_details){
      filteredMasterStaffs = this.masterStaffsAvalable;

      if(!filteredMasterStaffs.length) {
          filteredMasterStaffs = this.defaultMasterStaffsAvalable;
      }
    }
    
    console.log('filteredMasterStaffs', filteredMasterStaffs);

    console.log('this.userData.config.flex_site_patients_can_chat_with_internal_staffs', this.userData.config.flex_site_patients_can_chat_with_internal_staffs);
    
    if (!this.userData.master_config || (this.userData.config && this.userData.config.flex_site_patients_can_chat_with_internal_staffs == 1)) {
         for (var _i = 0; _i < filteredMasterStaffs.length; _i++) {
              defaultNurses.push(filteredMasterStaffs[_i]);
         }
    }

    if (defaultNurses && defaultNurses.length) {
      var createdWithUserIds: any = [];
      var createdWithUserNames: any = [];

      for (var i = 0; i < defaultNurses.length; i++) {
        createdWithUserIds.push(defaultNurses[i].userId);
        createdWithUserNames.push(defaultNurses[i].displayname);
      }

      createdWithUserIds = createdWithUserIds.join();
      createdWithUserNames = createdWithUserNames.join();
      console.log("defaultNurses", defaultNurses);
      const params: CreateChatroomParameters = { 
        createdWith: defaultNurses,
        createdByAssociatedId: +associatedUserId,
        chatWith: ChatWithTypes.STAFF
      };
      this.createChatroom(params).subscribe((response: any) => {
        const chatroomId = response.data;
        let activeMessageData: { [k: string]: any } = {};
        activeMessageData = {
          chatWithUserId: createdWithUserIds,
          chatWith: createdWithUserNames,
          chatroomid:chatroomId
        };
        localStorage.setItem('activeMessage', JSON.stringify(activeMessageData));
        const targetID: any = chatroomId;
        localStorage.setItem('targetId', targetID);
        localStorage.setItem('targetName', 'group-chat');
        let chatWithHeading;        
        localStorage.setItem('chatWithHeading', '');
        chatWithHeading = this.toolTipService.getTranslateDataWithParam('MESSAGES.CHAT_WITH_HEADING',
        {content: this.checkInfusionHours(false).isWorkingHours
          ? this.userData.firstDefaultNursesRoleNameDuringWorkingHour
          : this.userData.firstDefaultNursesRoleName });
        localStorage.setItem('chatWithHeadingHistory', chatWithHeading);
        localStorage.setItem('archived', 'false');
        if(this._structureService.currentUrlNow !== '/inbox/chatroom') {
            this.router.navigate(['/inbox/chatroom']);
        } else {
            this._SharedService.reloadChatroomWithNewThread.emit();
        }
      });
    } else {

      if (this.checkInfusionHours(false).isWorkingHours) {
        console.log("this.configData.no_clinician_message_on_working_hours", this.configData.no_clinician_message_on_working_hours);
        var notify = $.notify(this.configData.no_clinician_message_on_working_hours);
        setTimeout(function () {
          notify.update({ 'type': 'warning', 'message': this.configData.no_clinician_message_on_working_hours });
        }, 1000);
      } else {
        console.log("this.configData.no_clinician_message", this.configData.no_clinician_message);
        var notify = $.notify(this.configData.no_clinician_message);
        setTimeout(function () {
          notify.update({ 'type': 'warning', 'message': this.configData.no_clinician_message });
        }, 1000);
      }
    }

  }

  uploadChatSignature(data,signImageData,action){
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    if(signImageData){
      data['sign']=signImageData;
    }
    const promise = new Promise((resolve, reject) => {
      const apiURL = this.uploadChatSignatureApi+'?action='+action;
      this._http.post(apiURL, data, options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          resolve(result);
        }
        );
    });
    return promise;
  }

  getTagoptions(tag_type="1",fromchatroom=false,patientTenantIdvalue = 0, integrationDisabled?){
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    
    const promise = new Promise((resolve, reject) => {
      let apiURL = `${this.getTagoptionsApi}?tag_type=${tag_type}&patientTenantId=${patientTenantIdvalue}`;
      if (integrationDisabled) {
        apiURL = `${apiURL}&integrationEnabled=${!integrationDisabled}`;
      }
      const userData = this._structureService.getUserdata();
      apiURL += (userData.nursing_agencies !="")?"&nursingAgencies="+userData.nursing_agencies:"";

      this._http.get(apiURL, options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          resolve(result);
        }
        );
    });
    return promise;
  }
  getAlternatecontacts(tenantId,patientId){
    let headers = new Headers();
    if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){      
      tenantId = this._structureService.getCookie('crossTenantId');
    }
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    
    const promise = new Promise((resolve, reject) => {
    
      var  apiURL =  this.getAlternatecontactsApi+'?tenantId='+tenantId+"&patientId="+patientId+"&isActive=1&enrolled=1";
   
      this._http.get(apiURL, options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          resolve(result);
        }
        );
    });
    return promise;
  }

  uploadChatTags(data,action,timezone,patientIdValue=0){

    var userData = JSON.parse(this._structureService.userDetails);
    if(userData)
    {
      // TODO: esi_code_for_patient_identity decommisioned, remove usage if it is not causing any issue
      data['config_identity_value'] = userData.config.esi_code_for_patient_identity;
      data['config_staff_identity'] = userData.config_replica.esi_code_for_staff_identity;
      data['config_staff_name'] = userData.config_replica.esi_code_for_staff_name;
      data['enable'] = userData.config_replica.enable_progress_note_integration;
    }
    data['config_identity_value'] = patientIdValue!=0 ? patientIdValue :userData.config.esi_code_for_patient_identity;
    data.timezone=timezone;
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });    
    const promise = new Promise((resolve, reject) => {
      const apiURL = this.uploadChatTagsApi+'?action='+action+'&timezone='+userData.config.tenant_timezone+'&enable='+data['enable'];
      this._http.post(apiURL, data, options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          resolve(result);
        }, error => {
          reject(error.json());
        })
        .catch(
        error => {
          reject(error.json());
        }
        );
    });
    return promise;
  }

  getTenantClinicians(tenantId,fromChatroom=false){
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });    
    const promise = new Promise((resolve, reject) => {
      
      if(fromChatroom){
        var apiURL = this.getTenantCliniciansApi+'?tenantId='+tenantId;

      }else{
        var apiURL = (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this.getTenantCliniciansApi+'?tenantId='+tenantId+ "&crossTenantId=" + this._structureService.getCookie('crossTenantId') : this.getTenantCliniciansApi+'?tenantId='+tenantId;
      }
      this._http.get(apiURL, options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          resolve(result);
        }
        );
    });
    return promise;
  }

  markallMessageAsRead(messageDta){
    // messageDta.showChatHistory = false;
    var userData = JSON.parse(this._structureService.userDetails);
    // messageDta.tenantId = userData.tenantId;
    // messageDta.crossTenantId = userData.crossTenantId;
    // if(userData.config.show_chat_history_to_new_participant && userData.config.show_chat_history_to_new_participant=='1') {
    //   messageDta.showChatHistory = true;
    // }
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });    
    const promise = new Promise((resolve, reject) => {
      const apiURL = this.markallMessageAsReadApi;
      this._http.post(apiURL,messageDta, options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          console.log("markallMessageAsRead result-->",result);
          resolve(result);
        }
        );
    });
    return promise;

  }

  archiveInboxItems(messageDta){    
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });    
    const promise = new Promise((resolve, reject) => {
      const apiURL = this.archiveInboxItemsApi;
      this._http.post(apiURL,messageDta, options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          console.log("archiveInboxItems result-->",result);
          resolve(result);
        }
        );
    });
    return promise;
  }

  public masterScheduleSelectionFilter(user) {
    var d = new Date();
    var currentHour = d.getHours();
    var currentMinutes = d.getMinutes();
    var currentInterval = currentHour + ":" + currentMinutes;
    var objRes;
    var availableUser;
    if (this.userData && this.userData.masterSchedulerData) {
      var scheduleJson = this.userData.masterSchedulerData[user.userId];
      if (scheduleJson) {
        objRes = JSON.parse(scheduleJson);
        if (objRes) {
          availableUser = this.checkSchedule(objRes, currentInterval);
          if (availableUser) {
            return 1;
          } else {
            return 0;
          }
        } else {
          return 0;
        }
      } else {
        return 0;
      }
    }
  }
  patientCaregiverMessageRouting(data = {}){
   let headers = new Headers();
   headers.append('Content-Type', 'application/x-www-form-urlencoded');
   headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
   let options = new RequestOptions({ headers: headers });
   const promise = new Promise((resolve, reject) => {      
       const apiURL = this.patientCaregiverMessageRoutingApi;
    this._http.post(apiURL,data,options)
       .toPromise()
       .then(
       res => {
         const result = res.json();
         resolve(result);
       }
       );
   });
   return promise;
 }
  updateApplessStatus(data) {
    console.log(data);
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
      const apiURL = this.updateApplessStatusApi;
      this._http.post(apiURL, data, options)
        .toPromise()
        .then(
          res => {
            const result = res.json();
            console.log("appless update result-->", result);
            resolve(result);
          }
        );
    });
    return promise;
  }
  applessChatMode(params){
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Accept','application/json');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
      const apiURL = this.messageToggleApi;
      this.data = params;
      this._http.post(apiURL, this.data, options)
      .toPromise()
      .then(
      res => {
        const result = res.json();
        resolve(result);
      },err=>{
        resolve(err.json());
      });
    });
    return promise;
  }
  updateMessagePinUnpinStatus(chatroomIds, action){
    const promise: any = new Promise((resolve, reject) => {
      this.httpService
        .doPost(APIs.chatPinUnpin, {
          actionType: action,
          chatRoomIds: chatroomIds
        })
        .toPromise()
        .then(
          (res) => {
            resolve(res);
          })
          .catch((ex) => {
            reject(ex);
          });
    });
    return promise;
  }
  /** Get the new chatroom request payload */
  private getCreateChatroomPayload(chatRoomData: CreateChatroomParameters): CreateChatroomData {
    return {
      chatWith: chatRoomData.chatWith,
      data: this.getCreateChatroomUserData(chatRoomData),
      admissionId: this._structureService.isMultiAdmissionsEnabled && !isBlank(chatRoomData.admissionId) ? chatRoomData.admissionId : undefined
    }
  }
  /** Get the values for new chatroom data based on chatWith*/
  private getCreateChatroomUserData(chatRoomData: CreateChatroomParameters): ChatroomPayloadData {
    switch (chatRoomData.chatWith) {
      case ChatWithTypes.PATIENT:
          return chatRoomData.createdWithAssociatedId
              ? { userId: +chatRoomData.createdWith, associatedPatient: chatRoomData.createdWithAssociatedId }
              : { userId: +chatRoomData.createdWith };
      case ChatWithTypes.PARTNER:
      case ChatWithTypes.STAFF:
          return { userIds: chatRoomData.createdWith };
      case ChatWithTypes.MESSAGE_GROUP:
          return { groupId: chatRoomData.messageGroupId, topic: chatRoomData.topic };
      case ChatWithTypes.PATIENT_GROUP:
          const patientId = Array.isArray(chatRoomData.createdWith) ? chatRoomData.createdWith[0] : chatRoomData.createdWith;
          return { patientId, topic: chatRoomData.topic };
      case ChatWithTypes.ROLE:
          return { roleId: chatRoomData.roleId, associatedPatient: chatRoomData.createdByAssociatedId };
    }
  }
}
