import { Directive, ElementRef, Input, OnChanges } from '@angular/core';

@Directive({
  selector: '[inboxCompile]',
})
export class inboxCompileDirective implements OnChanges  {
  @Input() inboxCompile: string;
    constructor(private elRef: ElementRef) {
    }
   //ngAfterViewInit(): void {
    ngOnChanges(): void {
      //console.log("Inner Directive",this.inboxCompile);
      //console.log(this.inboxCompile);
       if (this.inboxCompile.match(/<img data-mediaType='image'/)||this.inboxCompile.match(/<img src=/) || this.inboxCompile.match(/<img data-mediaType="image"/))
            {
                this.inboxCompile = '<i class="fa fa-image"></i> Image';
            } else if (this.inboxCompile.match(/<img data-mediaType="video"/)||this.inboxCompile.match(/<video ><source src =/)) {
                this.inboxCompile = '<i class="fa fa-video-camera"></i> Video';
            } else if (this.inboxCompile.match(/<img data-mediaType='pdf'/)) {
                this.inboxCompile = '<i class="fa fa-file"></i> Pdf';
            } else if (this.inboxCompile.match(/<img data-mediaType='document'/)||this.inboxCompile.match(/<img data-src=/)) {
                this.inboxCompile = '<i class="fa fa-file-text "></i> Document';
            } else if (this.inboxCompile.match(/<audio controls><source src=/)) {
                this.inboxCompile = '<i class="fa fa-headphones"></i> Audio';
            }

            this.elRef.nativeElement.innerHTML = this.utf8Decode(this.inboxCompile);
    }
    utf8Decode(utf8String) {
        if (typeof utf8String != 'string') throw new TypeError('parameter ‘utf8String’ is not a string');
        // note: decode 3-byte chars first as decoded 2-byte strings could appear to be 3-byte char!
        const unicodeString = utf8String.replace(
            /[\u00e0-\u00ef][\u0080-\u00bf][\u0080-\u00bf]/g,  // 3-byte chars
            function(c) {  // (note parentheses for precedence)
                var cc = ((c.charCodeAt(0)&0x0f)<<12) | ((c.charCodeAt(1)&0x3f)<<6) | ( c.charCodeAt(2)&0x3f);
                return String.fromCharCode(cc); }
        ).replace(
            /[\u00c0-\u00df][\u0080-\u00bf]/g,                 // 2-byte chars
            function(c) {  // (note parentheses for precedence)
                var cc = (c.charCodeAt(0)&0x1f)<<6 | c.charCodeAt(1)&0x3f;
                return String.fromCharCode(cc); }
        );
        console.log("utf8 decode...");
        return unicodeString;
    }
}
 