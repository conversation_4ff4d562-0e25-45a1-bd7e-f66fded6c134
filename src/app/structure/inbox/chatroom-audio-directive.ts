import { Directive, ElementRef, Input, AfterViewInit } from '@angular/core';
import { OnChanges } from '@angular/core';


@Directive({
    selector: '[audio-blob]',
  })
  export class audioBlobDirective implements OnChanges {
  
    @Input() audioBlob: string;
    constructor(private elRef: ElementRef) {
        console.log('-------------------------audioBlobDirective-------');
    }
    ngOnChanges(): void {
        console.log('-------------------------audioBlobDirective-------')
      console.log(this.audioBlob);
    }
  }