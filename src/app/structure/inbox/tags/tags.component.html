<div class="tags-container">
  <span *ngFor="let tag of displayTags; index as t" [chToolTip]="getTooltip(tag)" data-animation="false">
    <ng-container *ngTemplateOutlet="tagTemplate; context: { tag: tag, shorter: shorter }"></ng-container>
  </span>
  <span
    class="overflow-count margin-right-5px"
    *ngIf="hiddenTags.length && maxAllowedTags !== 0"
    #popOver="ngbPopover"
    [ngbPopover]="viewRemainingTags"
    placement="right"
    container="body"
    (document:click)="popOver.close()"
    (click)="$event.stopPropagation()"
  >
    +{{ hiddenTags.length }}
  </span>
  <span class="overflow-count" *ngIf="hiddenTags.length && maxAllowedTags === 0">
    <i class="fa fa-tags" aria-hidden="true"></i>
    {{ 'LABELS.TAGS' | translate }}
  </span>
</div>
<ng-template let-tag="tag" let-shorter="shorter" #tagTemplate>
  <div class="tag-list tag-list-log" [style.background-color]="tag.bgColor" [style.color]="tag.fontColor" [style.background]="tag.bgColor">
    <i class="fa fa-caret-left" [style.color]="tag.bgColor ? tag.bgColor : '#8899a0'" aria-hidden="true"><span></span></i>
    <span
      [ngClass]="
        !hideDelete && ((isThread && tag.threadLevel) || (!isThread && !tag.threadLevel)) && !tag.enableIntegration ? 'message-tag-list-text' : ''
      "
      data-html="true"
      data-toggle="popover"
      data-animation="false"
      data-trigger="hover focus"
      data-placement="top"
      [style.color]="tag.fontColor ? tag.fontColor : '#ffffff'"
      >{{ tag.name }}</span
    >
    <span
      *ngIf="!hideDelete && ((isThread && tag.threadLevel) || (!isThread && !tag.threadLevel)) && !tag.enableIntegration"
      (click)="$event.stopPropagation(); removeTagsFromMessage(tag)"
      class="tag-close"
      >x</span
    >
  </div>
</ng-template>

<ng-template #viewRemainingTags>
  <div class="thread-level" *ngIf="threadLevelTags.length">
    <div class="header">{{ 'LABELS.THREAD' | translate }}</div>
    <div class="content">
      <span *ngFor="let tag of threadLevelTags; index as t" style="text-align: left">
        <ng-container *ngTemplateOutlet="tagTemplate; context: { tag: tag }"></ng-container>
      </span>
    </div>
  </div>
  <div class="message-level" *ngIf="messageLevelTags.length">
    <div class="header">{{ 'LABELS.MESSAGE' | translate }}</div>
    <div class="content">
      <span *ngFor="let tag of messageLevelTags; index as t" style="text-align: left">
        <ng-container *ngTemplateOutlet="tagTemplate; context: { tag: tag }"></ng-container>
      </span>
    </div>
  </div>
</ng-template>
