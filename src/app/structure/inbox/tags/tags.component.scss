:host ::ng-deep .tooltip-inner {
  width: 300px !important;
  padding: 20px !important;
  padding-left: 15px !important;
  border-radius: 1rem !important;
}

.overflow-count {
  border: 1px solid #7f8fa5;
  color: #3a68a7;
  background-color: #e4e9f0;
  border-radius: 5px;
  padding: 0px 8px;
  font-size: 13px;
  font-weight: bold;
  height: 20px;
  cursor: pointer;

  &.margin-right-5px {
    margin-left: 5px;
  }
  &:hover {
    color: #3975c7;
    background-color: #c4d6ea;
  }
}

.thread-level,
.message-level {
  min-height: 50px;
  padding: 15px;
  text-align: left;
  margin: -5px;
  border-radius: 5px;

  .header {
    color: #000;
    font-weight: bold;
    font-size: smaller;
    text-decoration: underline;
  }
  .content {
    padding-top: 5px;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
  }
}

.thread-level {
  margin-bottom: 10px;
}


.tag-label {
  display: inline-block;
  width: auto;
  height: 20px;
  background-color: rgb(136, 155, 160);
  -webkit-border-radius: 3px 4px 4px 3px;
  -moz-border-radius: 3px 4px 4px 3px;
  border-radius: 3px 4px 4px 3px;
  border-left: 1px solid rgb(136, 155, 160);
  border-right-color: rgb(136, 155, 160);
  margin-left: 10px;
  position: relative;
  color: white;
  font-weight: 300;
  font-size: 12px;
  line-height: 20px;
  padding: 0 10px 0 10px;
  span.shorter {
    max-width: 70px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

/* Makes the triangle */
.tag-label:before {
  right: 100%;
  top: 0;
  border: solid transparent;
  content: " ";
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
  border-right-color: inherit;
  border-width: 10px;
  margin-top: 0;
}

.tags-container {
  display: flex;
  height: 20px;
}

.tag-list {
  width: max-content;
}