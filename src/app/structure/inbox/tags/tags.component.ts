/* eslint-disable no-underscore-dangle */
import { Component, EventEmitter, Input, Output, ViewChild } from '@angular/core';
import { NgbPopover } from '@ng-bootstrap/ng-bootstrap';
import { CONSTANTS } from 'app/constants/constants';
import { MessageViewerService } from 'app/structure/message/message-viewer.service';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { isBlank } from 'app/utils/utils';

@Component({
  selector: 'cat-tags',
  templateUrl: './tags.component.html',
  styleUrls: ['./tags.component.scss']
})
export class TagsComponent {
  @ViewChild('popOver') viewRemainingTagsRef: NgbPopover;
  userData;
  _tags = [];
  displayTags = [];
  hiddenTags = [];
  threadLevelTags = [];
  messageLevelTags = [];
  @Output() removeMessageTags = new EventEmitter<number>();
  @Input() isThread = true;
  @Input() chatRoomId;
  @Input() messageId;
  @Input() hideDelete = false;
  @Input() shorter;
  _maxAllowedTags = 1;
  @Input() set maxAllowedTags(max: any) {
    this._maxAllowedTags = max;
    this.setTags();
  }
  get maxAllowedTags() {
    return this._maxAllowedTags;
  }
  @Input() set tags(tags: any) {
    this._tags = tags;
    this.setTags();
  }

  get tags() {
    return this._tags;
  }

  setTags() {
    this.displayTags = [];
    this.hiddenTags = [];
    this.threadLevelTags = [];
    this.messageLevelTags = [];
    if (this.tags) {
      const tags = [];
      this.tags.forEach((tag: any) => {
        if (!tags.find((item) => item.id === tag.id && item.threadLevel === false)) {
          const metaData = !isBlank(tag.metaData) ? JSON.parse(tag.metaData) : {};
          tags.push({
            ...tag,
            enableIntegration: !isBlank(metaData) && !isBlank(metaData.enableIntegration) && Boolean(metaData.enableIntegration)
          });
        }
      });
      if (this.maxAllowedTags === 0) {
        this.hiddenTags = tags;
      } else if (tags.length > this.maxAllowedTags) {
        this.displayTags = tags.slice(0, this.maxAllowedTags);
        this.hiddenTags = tags.slice(this.maxAllowedTags);
      } else {
        this.displayTags = tags;
      }
    }
    if (this.hiddenTags.length) {
      this.hiddenTags.forEach((tag) => {
        if (tag.threadLevel) this.threadLevelTags.push(tag);
        else this.messageLevelTags.push(tag);
      });
    }
  }

  constructor(private toolTipService: ToolTipService, private structureService: StructureService, private msgViewerService: MessageViewerService) {
    this.userData = JSON.parse(this.structureService.userDetails);
  }

  removeTagsFromMessage(tag: any) {
    const config = {
      text: this.toolTipService.getTranslateData('MESSAGES.ARE_YOU_SURE_REMOVE_TAG')
    };
    this.structureService.showAlertMessagePopup(config).then((response) => {
      if (response) {
        if (this.viewRemainingTagsRef && this.viewRemainingTagsRef.isOpen()) this.viewRemainingTagsRef.close();
        const data = {
          tagid: tag.id,
          tagInfo: tag,
          chatroomId: this.chatRoomId,
          messageId: this.messageId ? this.messageId : 0,
          userId: this.userData.userId,
          type: 0,
          apId: '',
          tenantId: this.userData.tenantId,
          user: this.userData.displayName,
          createdBy: this.userData.userId
        };
        this.msgViewerService.deleteMessageTags(data).then((response:any) => {
          if(response.success) {
            this.structureService.notifyMessage({
              type: 'success',
              messge: this.toolTipService.getTranslateData('MESSAGES.TAG_REMOVED_SUCCESSFULLY')
            });
            let activityLogReRouteMessage = '';
            this.removeMessageTags.emit(tag.id);
            if (data.messageId === 0) {
              activityLogReRouteMessage = `${this.userData.displayName}(${this.userData.userId}) removed tag(${tag.name}) for the chat log (${this.chatRoomId})`;
            } else {
              activityLogReRouteMessage = `${this.userData.displayName}(${this.userData.userId}) removed tag(${tag.name}) for the message (${this.messageId}) in chat log (${this.chatRoomId})`;
            }
            const activityData = {
              activityName: 'Remove Tag Message',
              activityType: 'message',
              activityDescription: activityLogReRouteMessage
            };
            this.structureService.trackActivity(activityData);
          } else if(this.structureService.isMultiAdmissionsEnabled) {
            this.structureService.notifyMessage({
              messge: this.toolTipService.getTranslateData('ERROR_MESSAGES.USER_NO_ACCESS_TO_PATIENT_SITE'),
              type: CONSTANTS.notificationTypes.error,
              delay: 3000
            });
          }
        }).catch((error:any) => {
          let message = this.toolTipService.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG');
          if(error.status.code === 422 && this.structureService.isMultiAdmissionsEnabled){
            message = this.toolTipService.getTranslateData('ERROR_MESSAGES.USER_NO_ACCESS_TO_PATIENT_SITE');
          } else if(error.data.errors){
            message = error.data.errors[0].message || message;
          }
          this.structureService.notifyMessage({
            messge: message,
            type: CONSTANTS.notificationTypes.error,
            delay: 3000
          });
        });
      }
    });
  }

  getTooltip(tag) {
    if (tag.threadLevel) return 'tagInboxThreadLevel';
    return 'tagInboxMessageLevel';
  }
}
