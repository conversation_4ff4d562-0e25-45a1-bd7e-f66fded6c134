import { Component, OnInit, Pipe, Injector,PipeTransform } from '@angular/core';
import { InboxService } from './inbox.service';
import { DatePipe } from '@angular/common';
import { StructureService } from '../structure.service';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Pipe({
    name: 'unreadmessageFilter',
    pure: false
})
export class unreadmessageFilterPipe implements PipeTransform {
    constructor( public _structureService: StructureService) {}
    //public get _structureService() { return this._injector.get(StructureService); }

    
    userDetails = this._structureService.userDetails;
    userData = JSON.parse(this.userDetails);
    transform(arr: any, exponent: string): any {
        var totalUnReadMessages = 0;
        //console.log("unreadmessageFilterPipe",  arr);
        if (arr != undefined) {
            for (var i = 0; i < arr.length; i++) {
                if (arr[i].category){
                    if (arr[i].category !== 'signature-documents') {
                        if(arr[i].category == 'structured-forms') {
                            if(!(arr[i].staff_facing == '1' && arr[i].fromId == this.userData.userId || (arr[i].fromId == this.userData.userId && !arr[i].form_submission_id))) {
                                if(((arr[i].sender_read=='0' && this.userData.userId==arr[i].fromId) || (!arr[i].form_submission_id && arr[i].read=='0' && this.userData.userId==arr[i].recipient_id) || (this.userData.userId!=arr[i].recipient_id && this.userData.userId!=arr[i].fromId)) && arr[i].unreadCount!=0) {
                                    totalUnReadMessages = totalUnReadMessages + parseInt(arr[i].unreadCount || arr[i].unread);
                                }
                            }
                        } else {
                            totalUnReadMessages = totalUnReadMessages + parseInt(arr[i].unreadCount || arr[i].unread);
                        }
                        var subList = arr[i].subList;
                        if (subList) {
                            for (var j = 0; j < subList.length; j++) {
                                totalUnReadMessages = totalUnReadMessages + parseInt(subList[j].unreadCount || subList[j].unread || 0);
                            }
                        }
                    }
                }    
            }
        }
        //console.log("toalmessage" +totalUnReadMessages);
        return totalUnReadMessages;
    }
}

@Pipe({
    name: 'userPersistantMessageFilterFn'
})
export class userPersistantMessageFilterFnPipe implements PipeTransform {

    userDetails:any;
    userData:any = {};
    scheduleFilter;
    constructor(
        public _inboxService: InboxService,public _structureService: StructureService
    ) {
        this.userDetails = this._structureService.userDetails;
        this.userData = JSON.parse(this.userDetails);
        this.scheduleFilter = this._inboxService.scheduleSelectionFilter(this.userData);
    }
    transform(searchItems: any, criteria: any): any {
        //console.log("this.scheduleFilter",this.scheduleFilter);
        if (!searchItems)
            return searchItems;
        if (searchItems != undefined && Object.keys(searchItems).length != 0) {
            return searchItems.filter(item => {
                if (item != "" && item != null) {
                    if (item.category == 'structured-forms' && this.userData.group!=3) {
                        return true;
                    } else if ((item.category !== 'signature-documents') || (item.category == 'inventory-counts') || item.escalated === "0" || (item.escalated === '1' && this.scheduleFilter)) {
                        return true;
                    }else{
                        return false;
                    }
                }
                return false;
            });

        }
    }
}
