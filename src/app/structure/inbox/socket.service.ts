import { Injectable } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import { Subject } from 'rxjs/Subject';
import 'rxjs/add/operator/toPromise';
import { StructureService } from '../structure.service';
import { io } from "socket.io-client";
@Injectable()
export class SocketService {
  inboxListing=''; 
  config:any;
  userDetails:any;
  configData:any = {};
  userData:any = {};
  data={};
  socket;
  constructor(
    private _http: Http,
    private _structureService:StructureService
    ) {
     // this.socket = io(_structureService.socketConnectionUrl);
      
      this.config = this._structureService.userDataConfig;
      this.userDetails = this._structureService.userDetails;
      this.configData = JSON.parse(this.config);
      this.userData = JSON.parse(this.userDetails);
  }

  SocketServices(configData) {
       /*   var socket = io(configData.socketBaseUrl, { secure: true , auth:{
            token : this.userData.authenticationToken
          }});*/
          //"Client Connected From "+config.platform +" App"
          var clientConnectedFrom = '';
          console.log(configData.messages.clientConnectedFrom);
          if(configData.messages.clientConnectedFrom && configData.messages.clientConnectedFrom != ''){
            clientConnectedFrom = configData.messages.clientConnectedFrom.replace(/{{platform}}/g,configData.platform);
          }            
         // socket.emit("hellow",{'connectionFrom': clientConnectedFrom });        
         // return socket;
      }

 
  
}

