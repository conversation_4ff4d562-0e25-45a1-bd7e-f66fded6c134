import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { HttpClientModule } from '@angular/common/http';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap'; // For inbox Pagination Implemented by A
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { InboxComponent } from './inbox.citushealth';
import { ChatroomComponent } from './chatroom.citushealth';
import { inboxCompileDirective } from './inbox-directive';
import { compileDirective } from './chatroom-directive';
import { dateshortFilterPipe, filterPatientDiscussionGroupPipe, OrderByPipe } from './inbox-filter.pipes';
import { userPersistantMessageFilterFnPipe } from './inbox-unreadmessage.pipes';
import { virtualPatientFilterPipe } from './chatroom-filterVirtualPatient.pipes';
import { SearchFilterPipe, SearchFilterRoleTreeViewPipe, SearchRoleFilterPipe, pluralizeFilterPipe } from './inbox-search.pipes';
import { userFilterPipe } from './inbox-modal-filter';
import { PhonePipe } from './inbox-modal-filter';
import { filterUsersListPipe } from './inbox-filterUsersList.pipe';
import { SortPipe } from './inbox-date-filter.pipes';
import { Modal } from '../shared/commonModal';
import { SharedModule } from '../shared/sharedModule';
import { CommonVideoService } from '../../../assets/lib/universal-video/common-video.service';
import { Vidyo } from '../../../assets/lib/universal-video/vidyo/vidyo.service';
import { AuthGuard } from '../../guard/auth.guard';
import { TagsComponent } from './tags/tags.component';
import { ManageTagsComponent } from './manage-tags/manage-tags.component';
import { MessagesFilterComponent } from './messages-filter/messages-filter.component';
import { MessagesQuickFilterComponent } from './messages-quick-filter/messages-quick-filter.component';
import { ChatroomUsersListComponent } from './chatroom-users-list/chatroom-users-list-component';

export const routes: Routes = [
  {
    path: 'inbox',
    component: InboxComponent,
    canActivate: [AuthGuard],
    data: {
      checkRoutingConfig: 'enable_message_center'
    }
  },
  {
    path: 'inbox/chatroom',
    component: ChatroomComponent,
    canActivate: [AuthGuard],
    data: {
      checkRoutingConfig: 'enable_message_center'
    }
  },
  { path: 'inbox/chatrooms', component: ChatroomComponent },
  { path: 'inbox/chatroom/status/:id', component: ChatroomComponent }
];

@NgModule({
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
    FormsModule,
    SharedModule,
    FormsModule,
    ReactiveFormsModule,
    HttpClientModule,
    NgbModule.forRoot(),
    AngularMultiSelectModule
  ],
  declarations: [
    InboxComponent,
    ChatroomComponent,
    inboxCompileDirective,
    dateshortFilterPipe,
    // unreadmessageFilterPipe,
    compileDirective,
    virtualPatientFilterPipe,
    SearchFilterPipe,
    userFilterPipe,
    filterUsersListPipe,
    Modal,
    SortPipe,
    userPersistantMessageFilterFnPipe,
    PhonePipe,
    SearchFilterRoleTreeViewPipe,
    SearchRoleFilterPipe,
    pluralizeFilterPipe,
    filterPatientDiscussionGroupPipe,
    OrderByPipe,
    TagsComponent,
    ManageTagsComponent,
    MessagesFilterComponent,
    MessagesQuickFilterComponent,
    ChatroomUsersListComponent
  ],
  providers: [DatePipe, AuthGuard, userFilterPipe, SearchFilterPipe, Modal, SortPipe, PhonePipe, CommonVideoService, Vidyo],
  exports: [Modal]
})
export class InboxModule {}
