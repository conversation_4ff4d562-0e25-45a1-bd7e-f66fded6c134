import { Component, OnInit, Pipe, PipeTransform } from '@angular/core';
import { DatePipe } from '@angular/common';
import { InboxComponent } from './inbox.citushealth';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Pipe({
  name: 'filterUsersList'
})
export class filterUsersListPipe implements PipeTransform {
    transform(items:any[], exponent: string): any {
        if (!items)
        return items;        
        return items.filter(item =>{
           for (let key in item ) {
             //console.log(item,exponent);
             if((""+item.userId!=exponent)){
                return true;
             }
           }
           return false;
        });


    }
}