import { Component, OnInit, Pipe, PipeTransform } from '@angular/core';
import { DatePipe } from '@angular/common';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Pipe({
  name: 'virtualPatientFilter'
})
export class virtualPatientFilterPipe implements PipeTransform {
    transform(arr: any[], exponent: string): any {
        //console.log("NEW FILTERRRRRRRRRRRRRRRRR",arr);
         /*for (var i = 0; i < arr.length; i++) {
            return arr[i].status != '7';
         }*/
         if (!arr)
        return arr;        
        return arr.filter(item =>{
           for (let key in item ) {
             //console.log(item,exponent);
             if((item.status!='7')){
                return true;
             }
           }
           return false;
        });
    }
}
 