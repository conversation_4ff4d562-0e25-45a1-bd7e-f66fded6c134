import { TestBed, ComponentFixture } from '@angular/core/testing';
import { InboxComponent } from './inbox.citushealth';
import { InboxService } from './inbox.service';
import { StructureService } from '../structure.service';
import { SharedService } from '../shared/sharedServices';
import { FormsService } from '../forms/forms.service';
import { ToolTipService } from '../tool-tip.service';
import { ChatService } from './chat.service';
import { StaticDataService } from '../static-data.service';
import { Store, StoreService } from '../shared/storeService';
import { MessageService } from '../../services/message-center/message.service';
import { HttpService } from 'app/services/http/http.service';
import { DatePipe } from '@angular/common';
import { userFilterPipe } from './inbox-modal-filter';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { of } from 'rxjs/observable/of';
import { CommonTestingModule } from 'app/utils/common-testing.module';
import { userPersistantMessageFilterFnPipe } from './inbox-unreadmessage.pipes';
import { filterUsersListPipe } from './inbox-filterUsersList.pipe';
import { ManageSitesService } from '../manage-sites/manage-sites.service';
import { GlobalDataShareService } from '../shared/global-data-share.service';
import { RouterTestingModule } from '@angular/router/testing';
import { Router, RouterModule } from '@angular/router';
import { Observable } from 'rxjs';
describe('InboxComponent', () => {
    let component: InboxComponent;
    let fixture: ComponentFixture<InboxComponent>;
    let inboxService: any;
    let structureService: any;
    let sharedService: any;
    let formsService: any;
    let toolTipService: any;
    let chatSpy: any;
    let staticDataService: any;
    let storeService: any;
    let messageSpy: any;
    let httpService: any;
    let routerSpy: any;
    beforeEach(() => {
        localStorage.setItem(Store.DATE_RANGE_FILTER_INBOX, JSON.stringify({ startDate: '', endDate: '' }));
        const inboxSpy = {
            scheduleSelectionFilter: jasmine.createSpy('scheduleSelectionFilter'),
            getInboxData: jasmine.createSpy('getInboxData'),
            setLocalStorageData: jasmine.createSpy('setLocalStorageData')
        }
        const structureSpy  = {
            getCookie: jasmine.createSpy('getCookie'),
            userDataConfig: JSON.stringify({ tenantId: 1 }),
            userDetails: JSON.stringify({ userId: 1, enable_cross_site: '1', config: { enable_multisite: '1' }, mySites: [{id: 1, name: 'Site1'}, {id: 2, name: 'Site2'}] }),
            notifySearchFilterApplied: jasmine.createSpy('notifySearchFilterApplied'),
            trackActivity: jasmine.createSpy('trackActivity'),
            getInboxCounts : jasmine.createSpy('getInboxCounts').and.returnValue(Promise.resolve({})),
            subscribeSocketEvent: jasmine.createSpy('subscribeSocketEvent').and.returnValue(of({})),
          };

        const sharedSpy = {
            sessionRefresh: of({ privileges: [] }),
            getSiteLabelBasedOnMultiAdmission: jasmine.createSpy('getSiteLabelBasedOnMultiAdmission'),
            crossTenantChange: of({}),
            chatWithUserListData: of({}),
            updateInboxData: of({}),
            inboxUnreadMessageCount: of({}),
            reloadOnConfigChange: of({}),
            newMessageGroupUp: of({}),
            onInboxData: { emit: jasmine.createSpy('emit'), subscribe: jasmine.createSpy('subscribe') },
            $maskedMessageDeleteRestoreSubject: of({}),
        };
        messageSpy = {
            getChatMessages: jasmine.createSpy('getChatMessages').and.returnValue(of({data: {totalUnreadMessagesCount: 0, messages: []}}))
        }
        const formsSpy = jasmine.createSpyObj('FormsService', ['']);
        const toolTipSpy = jasmine.createSpyObj('ToolTipService', ['getTranslateData']);
        chatSpy =  {
            getUsersListByRoom: jasmine.createSpy('getUsersListByRoom').and.returnValue(Promise.resolve([])),
            getChatroomMessages: jasmine.createSpy('getChatroomMessages').and.returnValue(Promise.resolve({isPdg: 1, patient_data: [{siteId: 1}]})),
        };
        const staticDataSpy = jasmine.createSpyObj('StaticDataService', ['getPriorities', 'getSiteLabelBasedOnMultiAdmission']);
        const httpSpy = jasmine.createSpyObj('HttpService', ['']);

        TestBed.configureTestingModule({
            declarations: [InboxComponent, userFilterPipe, userPersistantMessageFilterFnPipe, filterUsersListPipe],
            imports: [CommonTestingModule, RouterTestingModule, RouterModule],
            providers: [
                { provide: InboxService, useValue: inboxSpy },
                { provide: StructureService, useValue: structureSpy },
                { provide: SharedService, useValue: sharedSpy },
                { provide: FormsService, useValue: formsSpy },
                { provide: ToolTipService, useValue: toolTipSpy },
                { provide: ChatService, useValue: chatSpy },
                { provide: StaticDataService, useValue: staticDataSpy },
                { provide: MessageService, useValue: messageSpy },
                { provide: HttpService, useValue: httpSpy },
                DatePipe,
                ManageSitesService,
                GlobalDataShareService,
                StoreService,
            ],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();
        routerSpy = TestBed.get(Router);
        fixture = TestBed.createComponent(InboxComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });

    it('should initialize component with default values', () => {
        expect(component.currentPage).toBe(1);
        expect(component.perPage).toBe(25);
        expect(component.inboxData).toEqual([]);
    });

    it('should call filterInbox on applyQuickSearch', () => {
        spyOn(component, 'filterInbox');
        component.applyQuickSearch({ highPriority: true, highFlag: false, mention: false, unread: true });
        expect(component.filterInbox).toHaveBeenCalled();
    });

    it('should call filterInbox on applyAdvanceSearch', () => {
        spyOn(component, 'filterInbox');
        component.applyAdvanceSearch({ priority: 1, flag: 3, mentions: [1], unread: 1 });
        expect(component.filterInbox).toHaveBeenCalled();
    });

    it('should call getChatroomUsers on getChatroomUsers', () => {
        component.getChatroomUsers(1);
        expect(chatSpy.getUsersListByRoom).toHaveBeenCalledWith(1, 'All', undefined);
    });

    it('should call getChatMessages on activateForNewChatroomPolling', () => {
        const pollingData = { args: { chatroomId: 1 } };
        messageSpy.getChatMessages.and.returnValue(of({}));
        component.activateForNewChatroomPolling(pollingData);
        expect(messageSpy.getChatMessages).toHaveBeenCalledWith({ chatroomId: 1 });
    });

    it('should return site IDs on getSiteIds', () => {
        const siteId = {siteId: [1,2]}
        component.getSiteIds(siteId);
        expect(component.selectSiteId).toEqual('1,2');
    });
});