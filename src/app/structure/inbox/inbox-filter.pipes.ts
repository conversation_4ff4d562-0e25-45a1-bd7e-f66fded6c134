import { Component, OnInit, Pipe, PipeTransform } from '@angular/core';
import { DatePipe } from '@angular/common';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Pipe({
  name: 'dateshortFilter'
})
export class dateshortFilterPipe implements PipeTransform {
    transform(value: any, exponent: string): any {
    var shortFormat:any = (new Date(value).toDateString() === new Date().toDateString()) ? 'hh:mm a' : 'MMM dd hh:mm a';
    var datePipe = new DatePipe("en-US"); 
    value = datePipe.transform(value, shortFormat);
    return value;
    }
}

@Pipe({
  name: 'dateshortyearFilter'
})
export class dateshortyearFilterPipe implements PipeTransform {
    transform(value: any, exponent: string): any {
    var shortFormat:any = (new Date(value).toDateString() === new Date().toDateString()) ? 'hh:mm a' : (new Date(value).getFullYear() != new Date().getFullYear()) ? 'MMM dd y hh:mm a' : 'MMM dd hh:mm a';
    var datePipe = new DatePipe("en-US"); 
    value = datePipe.transform(value, shortFormat);
    return value;
    }
}
 
@Pipe({  
  name: 'filterPatientDiscussionGroup'
 })
export class filterPatientDiscussionGroupPipe implements PipeTransform {
transform(items:any[], exponent: string): any {
        if (!items)
        return items;        
        return items.filter(item =>{
           for (let key in item ) {
             exponent = (exponent==undefined ? '1' : exponent);
             if((!exponent)){
               return item.pdgroup!='1';
             }else{
               return true;
             }
           }
           return false;
        });
    }
}

@Pipe({  
  name: 'orderBy'
 })
export class OrderByPipe implements PipeTransform {
transform(users: any[], field: string): any[] {
    users.sort((a: any, b: any) => {
      var nameA=a.name.toLowerCase(), nameB=b.name.toLowerCase();
      if (nameA < nameB) {
        return -1;
      } else if (nameA > nameB) {
        return 1;
      } else {
        return 0;
      }
    });
    return users;
  }
}
 