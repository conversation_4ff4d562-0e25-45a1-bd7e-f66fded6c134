@import '../../../assets/css/message';

#chat-with-modal-search-box {
  width: auto;
}
.ooo-message{
  background-color: #f0a8b0;
  color: #000;
}
.ooo-message a {
  text-decoration: underline;
}


.cat__apps__chat-block__item:not(.cat__apps__chat-block__item--right) {
  margin-right: 5rem;
  .priority-msg-icon {
    right: 3.5rem;
  }

  .priority-msg-icon:has(.fa-exclamation) {
    right: 4rem;
  }
}

.priority-position {
  position: absolute;
}

.cat__apps__chat-block__item--right {
  .cat__apps__chat-block__content {
    margin-left: 5rem;
  }

  .priority-msg-icon {
    left: 3.5rem;
  }

  .priority-msg-icon:has(.fa-exclamation) {
    left: 4rem;
  }
}

.btn-priority-group {
  label {
    width: 3.5rem;
  }
  .alert-fill,
  .arrowdown-fill {
    margin-top: 2px;
    margin-left: -2px;
  }
}

.priority-inbox-icon {
  display: inline-block;
  float: right;
  margin-right: -16px;
  margin-top: 13px;

  .alert-fill,
  .alert-outline,
  .at-fill,
  .arrowdown-fill,
  .arrowdown-outline,
  .at-outline {
    display: inline-block;
  }
}

.chat-room-sublist {
  .priority-inbox-icon {
    margin-top: -20px;
    margin-right: -80px;
  }
}

div#stickThis {
  background-color: #fff !important;
  border: 0 !important;
  border-radius: 0 !important;
  color: #292929 !important;
  text-align: left !important;
  margin-left: 0px !important;
  position: sticky;
  z-index: 890;
}

#stickThis {
  padding: 5px;
  background-color: #ccc;
  text-align: center;
  border: 2px solid #444;
  -webkit-border-radius: 10px;
  border-radius: 10px;
}

#stickThis.stick {
  position: sticky;
  top: 0;
  z-index: 890;
  border-radius: 0 0 10px 10px;
  -webkit-box-shadow: 0px 2px 6px -2px rgba(0, 0, 0, 0.75);
  -moz-box-shadow: 0px 2px 6px -2px rgba(0, 0, 0, 0.75);
  box-shadow: 0px 2px 6px -2px rgba(0, 0, 0, 0.75);
}

.treeview {
  width: 100% !important;
}

.msg_grp_title {
  text-transform: capitalize;
}

.user-list-main {
  position: absolute;
  z-index: 99;
  background: aquamarine;
}

.user-list-sub {
  clear: both;
  margin-bottom: 5px;
}

.hide {
  display: none;
}

.cat__core__scrollable {
  overflow: initial !important;
}

.hr-read {
  border-width: 1px;
  height: 1px;
  border: none;
  color: #fff;
  background-color: #fff;
  margin-top: -15px;
}

.associate-search {
  position: relative;
}

.rolesearchicon {
  position: absolute;
  right: 30px !important;
  top: 13px !important;
}

input#associate-search-input {
  width: 70%;
}

input#associate-search-input.active {
  border: 1px solid #1790fe;
  border-radius: 4px 4px 0 0px;
}

ul.associate-ul {
  list-style-type: none;
  padding: 0;
  display: none;
  margin: 0;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #0190fe;
  width: 95.8%;
  border-radius: 0 0px 4px 4px;
  border-top: none;
  position: absolute;
  background: #fff;
  z-index: 99;
}

.withoutfilter {
  width: 80% !important;
}

.withfilter {
  width: 80% !important;
}

ul li.associate-li {
  margin-top: -1px;
  padding: 0.42rem 1.14rem;
  text-decoration: none;
  position: relative;
  cursor: pointer;
}

ul li.associate-li:hover {
  background: #d2d9e5 !important;
  color: #222034 !important;
}

ul li.associate-li.selected {
  background: #d2d9e5 !important;
  color: #222034 !important;
}

ul li.associate-li.selected {
  background: #d2d9e5 !important;
  color: #222034 !important;
}

.asscoiate-actions {
  position: absolute;
  left: 73%;
  top: 6px;
  width: 31%;
}

.associate-search {
  position: relative;
}

input#associate-search-input {
  width: 100%;
}

input#associate-search-input.active {
  border: 1px solid #1790fe;
  border-radius: 4px 4px 0 0px;
}

.associate-close {
  display: none;
  cursor: pointer;
  position: absolute;
  top: 50%;
  right: 21%;
  padding: 4px 16px;
  transform: translate(0%, -50%);
  font-size: 18px;
}

.associate-close:hover {
  background: #bbb;
}

.associate-create {
  z-index: 1;
}

.read-user {
  padding-left: 10px;
  padding-right: 19px;
  padding-bottom: 5px;
  display: inline-block;
  font-size: 12px;
  word-break: break-word;
  white-space: nowrap;
  text-align: left;
}

.read-user-img {
  width: 24px !important;
  height: 24px !important;
  float: left;
}

.accordion-show {
  display: block !important;
}

.accordion-hide {
  display: none !important;
}

.arrow_box {
  position: absolute;
  background: #88b7d5;
  border-radius: 5px;
  color: #fff;
  margin: 0 auto;
  text-align: center;
  width: auto;
  padding: 5px 10px;
  white-space: nowrap;
  right: -17px;
  top: 30px;
}

.chat-with-wrapper-filter-tenant-label {
  padding: 10px;
  padding-right: 0px;
  max-width: 23%;
}

.arrow_box:after {
  border-color: rgba(136, 183, 213, 0);
  border-bottom-color: #88b7d5;
  border-width: 10px;
  margin-left: 20px;
  bottom: 100%;
  right: 17px;
  border: solid transparent;
  content: ' ';
  height: 0;
  width: 0;
  position: absolute;
  pointer-events: none;
}

.loadMoreHandIcon {
  cursor: default !important;
}

.tag-button-div {
  min-width: 469px;
  z-index: 999;
  position: absolute;
  top: 40%;
  left: 30%;
}

.tag-message-icon.selected {
  background: url(../../../assets//modules/dummy-assets/common/img/tag-icon-hover.png);
}

.tag-select-bg {
  background-color: #aac1d6;
}

.round {
  position: relative;
  width: 40px;
  height: 45px;
  margin-top: 6px;
  margin-left: 15px;
}

.round label {
  background-color: #cccccc54;
  border: 1px solid #11c1f34d;
  border-radius: 50%;
  cursor: pointer;
  height: 28px;
  left: 0;
  position: absolute;
  top: 0;
  width: 28px;
}

.round label:after {
  border: 2px solid #fff;
  border-top: none;
  border-right: none;
  content: '';
  height: 6px;
  left: 7px;
  opacity: 0;
  position: absolute;
  top: 8px;
  transform: rotate(-45deg);
  width: 12px;
}

.round input[type='checkbox'] {
  visibility: hidden;
}

.round input[type='checkbox']:checked + label {
  background-color: #11c1f3;
  border-color: #11c1f3;
}

.round input[type='checkbox']:checked + label:after {
  opacity: 1;
}

.other_user_tag {
  right: 43px !important;
  top: 36px !important;
}

.tooltip-inner {
  max-width: 350px !important;
  text-align: left;
}

.tooltip li {
  text-align: left;
  list-style: none;
}

.message-tag-loading-message {
  color: #1d6372 !important;
}

.message-pending {
  color: #ec0e0e;
  font-size: large;
}

.hr-info {
  border-bottom-width: 0.5px;
  color: #fff;
  background-color: #f6f9fa;
  margin-top: 10px;
}

#msginfo-button {
  right: 0px !important;
  z-index: 9999 !important;
  top: 5.5% !important;
  position: fixed !important;
  height: 89% !important;
  left: auto !important;
}

#msginfo-button .modal-dialog {
  height: 100%;
}

#msginfo-button .modal-content {
  height: 100%;
}

div#videoPopup {
  display: none;
  position: absolute;
  bottom: 65px;
  border: 1px solid #cccccc;
  z-index: 9;
  padding: 10px;
  background-color: #81888e;
}

div#videoPopup::after {
  content: '';
  position: absolute;
  left: 50%;
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid #81888e;
  clear: both;
  bottom: -6px;
}

div#videoPopup .btn {
  padding-left: 20px;
  margin-right: 10px;
}

div#videoPopup .close {
  float: right;
  font-size: 1.5rem;
  font-weight: 700;
  line-height: 1;
  color: #ffffff;
  text-shadow: 0 1px 0 #fff;
  opacity: 5;
  position: relative;
  top: 0px;
  right: 5px;
}

.appless-button-only {
  width: 215px;
  text-align: center;
}

.mTitle {
  padding-top: 7px;
}

.chat-error-bg {
  background: #ededed;
}

.tag-filter .tags-select {
  width: 310px;
  display: inline-block;
}

.gap-1 {
  gap: 1rem;
}

.chatrrom-group-icon-for-members {
  top: 5px;
}

:host ::ng-deep .inbox-data-container.chatroom-messages-with-sublist {
  .callout {
    display: none;
  }
  .mention {
    color: inherit;
    background: none !important;
    border: none;
    padding: 0 !important;
    font-weight: inherit;
  }
}

.mention-users-dropdown-container {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1000;
}

#mention-users-dropdown {
  max-height: 100px;
  overflow-y: auto;
  padding-inline-start: 0px !important;
  min-width: 10rem;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 1rem;
  color: #292b2c;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 0.25rem;
  input.active,
  input:hover {
    background: #eef0f4;
    cursor: pointer;
  }
}

div#stickThis {
  margin-bottom: -10px;
  padding: 0;
}

.show-msg-tooltip {
  margin-left: 5px;
}

.large-tooltip {
  margin-right: 5px;
}

 :host ::ng-deep .select-tags .tag-label span {
    width: 40px;
 }
.chat-message-container .download-wrapper {
        margin-top: 11px;
}
.chat-message-container .download-wrapper .fa-download {
    cursor: pointer;
    margin-left: 16px;
}
.chat-message-container .cat__apps__messaging__tab__text img {
    cursor: pointer !important;
}
app-messages-quick-filter {
  display: inline-block;
  margin-top: 3px;
  margin-left: 5px;
}

:host ::ng-deep .select-tags .tag-label span {
  width: 40px;
}
.chatroom-list-dropdown {
  height: auto;
  max-height: 280px;
  overflow-y: auto;
}

.chat-participants-count {
  font-family: inherit;
  font-weight: bolder;
  font-size: 16px;
  color: black;
  position: absolute;
  bottom: 5.5mm;
  right: 1.5mm;
}
#role-list-view {
  position: relative;
  bottom: 1.5mm;
}
#participant-list-view {
  position: relative;
  top: 2mm;
}

#ul-chatParticipants{
  padding-bottom: 0px;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.download-progress {
  background-color: #F2F4F8;
   position: relative;
   left: 3mm;
   margin-right: 2mm;
   top: 6px;
}

.tag-modal {
 width: 120%;
}
.tag-msg-row {
  padding-right: 4.5em;
}
.tag-msg-patient-search {
  padding-left: 3mm;
}
.tag-msg-patient-search-ul {
  width:95.8%;
}
.tag-msg-search-reset-btn {
  display: flex;
  align-items: center;
  gap: 5%;
}
.tag-msg-alert-div{
  margin-top:50px
}
.tag-msg-admission-div {
  padding-right: 3.7em;
}
.chat-header {
  width: 80%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
@media (max-width: 1750px) {
  .chat-header {
    width: 75%;
  }
}
@media (max-width: 1470px) {
  .chat-header {
    width: 65%;
  }
}
@media (max-width: 1100px) {
  .chat-header {
    width: 55%;
  }
}
@media (max-width: 820px) {
  .chat-header {
    width: 45%;
  }
}

.not-contactable-info {
  position: absolute;
  top: -20px;
  left: 0;
  display: flex;
  align-items: center;
  font-weight: bold;
  span {
    margin-right: 5px;
  }
}
.custom-icon{
  color: #0190fe;
  margin-left: 5px;
}

.message-with-undo {
  display: flex;
  align-items: center;
}

.delete-msg-icon {
  width: 32px;
  height: 32px;
  display: block;
  padding: 6px;
}

.disabled-icon {
  color: gray; 
}

.chat-signature {
  max-width: 200px;
}
.tag-message-icon{
  padding: 20px;
}