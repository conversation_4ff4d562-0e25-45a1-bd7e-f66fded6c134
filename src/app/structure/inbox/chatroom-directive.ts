import { Directive, ElementRef, Input, AfterViewInit, Output, EventEmitter } from '@angular/core';
import { OnChanges } from '@angular/core';
import { StructureService } from '../structure.service';
import { isBlank } from 'app/utils/utils';
import { PdfViewService } from '../pdfview.service';
import { ToolTipService } from '../tool-tip.service';

@Directive({
  selector: '[compile]',
})
export class compileDirective implements OnChanges {

  @Input() compile: string;
  @Output() modalBodyContent = new EventEmitter();
  constructor(private elRef: ElementRef,private _structureService:StructureService, private PdfViewService: PdfViewService, private tooltipService: ToolTipService) {

  }
  ngOnChanges(): void {
    // console.log(this.compile);  
    var resetValue;
    var dataValue;
    if (this.compile.search("data-mediaType='pdf'") != -1) {
      dataValue = this.compile;
      var hrefStrings = dataValue.substring(parseInt(dataValue.lastIndexOf("data-src=")) + 9, dataValue.lastIndexOf(".pdf'"));
      var appendedString = " data-view=" + hrefStrings + ".pdf'";
      // resetValue = this.compile.splice(parseInt(dataValue.lastIndexOf(".pdf'"))+5,0,appendedString);
      dataValue = dataValue.replace(" src='./img"," src='"+this._structureService.iconPath+""); 
      resetValue = dataValue;
    } else {
      resetValue = this.compile.replace(" src='./img"," src='"+this._structureService.iconPath+""); 
    }
    this.elRef.nativeElement.innerHTML = this.utf8Decode(resetValue);
    this.addActiveMention();
    setTimeout(() => {
      this.addDownloadIcons();
    }, 0);
  }

  addActiveMention() {
    const elements = this.elRef.nativeElement.getElementsByClassName('mention');
    for (const element of elements) {
      const attrib = element.getAttribute('attr.data-target');
      if (!isBlank(attrib) && attrib === Number(JSON.parse(this._structureService.userDetails).userId)) element.classList.add('active');
    }
  }
  addDownloadIcons(): void {
    const images = this.elRef.nativeElement.getElementsByTagName('img');
   
    for (const img of images) {
      if (img.parentElement.classList.contains('image-download-wrapper') || img.classList.contains('emojioneemoji')) {
        continue;
      }
      const imageTitle = img.getAttribute('title') || '';
      const fileUrl = img.getAttribute('data-src') || img.getAttribute('ng-src') || img.getAttribute('src');
      const wrapperDiv = document.createElement('div');
      wrapperDiv.style.display = 'flex';
      wrapperDiv.style.alignItems = 'center';
      wrapperDiv.style.gap = '8px';
      wrapperDiv.className = 'image-download-wrapper';
  
      const parent = img.parentNode;
      parent.replaceChild(wrapperDiv, img);
      wrapperDiv.appendChild(img);

      const downloadIcon = document.createElement('i');
      downloadIcon.className = 'fa fa-download text-primary download-wrapper';
      downloadIcon.style.cursor = 'pointer';
      downloadIcon.setAttribute('data-toggle', 'tooltip');
      downloadIcon.setAttribute('data-placement', 'top');
      downloadIcon.setAttribute('title', imageTitle);

      // Spinner container
      const spinnerContainer = document.createElement('div');
      spinnerContainer.style.display = 'none';
      spinnerContainer.style.alignItems = 'center';
      spinnerContainer.className = 'download-progress';
      const spinner = document.createElement('div');
      spinner.className = 'spinner-border text-primary';
      spinner.setAttribute('role', 'status');
      const spinnerText = document.createElement('span');
      spinnerText.innerText = this.tooltipService.getTranslateData('MESSAGES.DOWNLOADING_PROGRESS');
      spinnerContainer.appendChild(spinner);
      spinnerContainer.appendChild(spinnerText);

      downloadIcon.addEventListener('click', () => {
        spinnerContainer.style.display = 'flex';
        this.PdfViewService.showPdfViewer(fileUrl).then((modalBody) => {
          this.modalBodyContent.emit(modalBody);
          this.PdfViewService.downloadPdfViewer(imageTitle);
          spinnerContainer.style.display = 'none';
        },() => {
          spinnerContainer.style.display = 'none';
        });
      });
      wrapperDiv.appendChild(downloadIcon);
      wrapperDiv.appendChild(spinnerContainer);
    }
  }
  
  utf8Decode(utf8String) {
    if (typeof utf8String != 'string') throw new TypeError('parameter ‘utf8String’ is not a string');
      // note: decode 3-byte chars first as decoded 2-byte strings could appear to be 3-byte char!
      const unicodeString = utf8String.replace(
          /[\u00e0-\u00ef][\u0080-\u00bf][\u0080-\u00bf]/g,  // 3-byte chars
          function(c) {  // (note parentheses for precedence)
              var cc = ((c.charCodeAt(0)&0x0f)<<12) | ((c.charCodeAt(1)&0x3f)<<6) | ( c.charCodeAt(2)&0x3f);
              return String.fromCharCode(cc); }
      ).replace(
          /[\u00c0-\u00df][\u0080-\u00bf]/g,                 // 2-byte chars
          function(c) {  // (note parentheses for precedence)
              var cc = (c.charCodeAt(0)&0x1f)<<6 | c.charCodeAt(1)&0x3f;
              return String.fromCharCode(cc); }
      );
      console.log("utf8 decode...");
      return unicodeString;
  }
}
