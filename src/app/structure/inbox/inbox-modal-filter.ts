import { Pipe, PipeTransform } from '@angular/core';
import { CONSTANTS } from 'app/constants/constants';
import { isBlank, isEnabled, validateEmail } from 'app/utils/utils';
import { StructureService } from '../structure.service';

//TODO: Scope for revamp
@Pipe({
  name: 'userFilter'
})
export class userFilterPipe implements PipeTransform {

  constructor(private structureService: StructureService) {}

  transform(value: any, exponent?: string): any {
    console.log(value, exponent);
    let newResult = [];
    if (value) {
      newResult = value
        .filter((item) => {
            if (!exponent) return true;
            return exponent === 'staff'
              ? item.roleId.toString() !== CONSTANTS.userGroupIds.patient.toString() 
              && item.roleId.toString() !== CONSTANTS.userGroupIds.partner.toString()
              : item.roleId.toString() === CONSTANTS.userGroupIds[exponent].toString()
          }
        )
        .map((user) => {
          const newItem = {
            ...user,
            noContactAvailable: !this.isUserContactable(user),
            isContactNotOptedIn: !this.isContactOptedIn(user)
          };
          if (!isBlank(user.alternateContacts)) {
            newItem.alternateContacts = user.alternateContacts.map((altUsr) => ({
              ...altUsr,
              noContactAvailable: !this.isUserContactable(altUsr),
              isContactNotOptedIn: !this.isContactOptedIn(altUsr)
            }));
          }
          return newItem;
        });
    }
    return newResult;
  }
  private isUserContactable(user) {
    if (user.password) return true;
    return !user.password && this.structureService.isConfigEnabled('enable_appless_model') && this.isContactOptedIn(user);
  }

  private isContactOptedIn(user) {
    return (validateEmail(user.email) && (isEnabled(user, 'enableEmailNotifications') || isEnabled(user, 'enable_email_notifications')))
    || (!isBlank(user.mobile) && (!isBlank(user.countryCode) || !isBlank(user.country_code)) && (isEnabled(user, 'enableSmsNotifications') || isEnabled(user, 'enable_sms_notifications')));
  }
}

@Pipe({
  name: 'phone'
})
export class PhonePipe implements PipeTransform {
  transform(tel, args) {
    if (!tel) {
      return '';
    }
    var value = tel.toString().trim().replace(/^\+/, '');
    if (value.match(/[^0-9]/)) {
      return tel;
    }
    var phn, number;
    switch (value.length) {
      case 1:
      case 2:
      case 3:
        phn = value;
        break;

      default:
        phn = value.slice(0, 3);
        number = value.slice(3);
    }
    if (number) {
      if (number.length > 3) {
        number = number.slice(0, 3) + '-' + number.slice(3, 7);
      }

      return ('(' + phn + ') ' + number).trim();
    } else {
      return '(' + phn;
    }
  }
}
