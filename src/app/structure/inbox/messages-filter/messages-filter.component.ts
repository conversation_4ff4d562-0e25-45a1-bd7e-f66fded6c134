import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ControlType } from 'app/structure/shared/advance-search/control-type';
import { StaticDataService } from 'app/structure/static-data.service';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { isBlank } from 'app/utils/utils';
import { Filter, MessageType, PrivilegeKeys, UserGroup } from 'app/constants/constants';
import { StoreService, Store } from 'app/structure/shared/storeService';
import { InboxService } from '../inbox.service';

@Component({
  selector: 'app-messages-filter',
  templateUrl: './messages-filter.component.html'
})
export class MessagesFilterComponent implements OnInit {
  @Input() isInbox = false;
  @Input() selectedTenantId = 0;
  @Input() resetAdvanceSearchForm = false;
  @Output() advanceSearch = new EventEmitter();
  modelData = {
    tags: []
  };
  priorityOptions = this.staticDataService
    .getPriorities()
    .map((item) => ({ key: String(item.key), value: this.toolTipService.getTranslateData(`PRIORITIES.${item.value}`) }));
  flagOptions = [
    {
      key: '3',
      value: this.toolTipService.getTranslateData('FLAG.HIGH')
    },
    {
      key: '2',
      value: this.toolTipService.getTranslateData('FLAG.MEDIUM')
    },
    {
      key: '1',
      value: this.toolTipService.getTranslateData('FLAG.LOW')
    }
  ];
  chatThreadTypes = [
    {
      key: String(MessageType.PDG),
      value: this.toolTipService.getTranslateData('OPTIONS.MESSAGE_TYPES.PDG')
    },
    {
      key: String(MessageType.MESSAGE_GROUP),
      value: this.toolTipService.getTranslateData('OPTIONS.MESSAGE_TYPES.MSG_GROUP')
    },
    {
      key: String(MessageType.MASKED),
      value: this.toolTipService.getTranslateData('OPTIONS.MESSAGE_TYPES.MASKED')
    },
    {
      key: String(MessageType.BROADCAST),
      value: this.toolTipService.getTranslateData('OPTIONS.MESSAGE_TYPES.BROADCAST')
    },
    {
      key: String(MessageType.PATIENT_INITIATED),
      value: this.toolTipService.getTranslateData('OPTIONS.MESSAGE_TYPES.PATIENT_INITIATED')
    },
    {
      key: String(MessageType.STAFF_TO_PATIENT),
      value: this.toolTipService.getTranslateData('OPTIONS.MESSAGE_TYPES.STAFF_TO_PATIENT')
    },
    {
      key: String(MessageType.STAFF_TO_STAFF),
      value: this.toolTipService.getTranslateData('OPTIONS.MESSAGE_TYPES.STAFF_TO_STAFF')
    }
  ];
  advanceSearchControls;

  constructor(
    private toolTipService: ToolTipService,
    private staticDataService: StaticDataService,
    public structureService: StructureService,
    private inboxService: InboxService,
    private storeService: StoreService
  ) {}

  ngOnInit() {
    this.advanceSearchControls = [];
    const userData = JSON.parse(this.structureService.userDetails);
    if (
      (userData && Number(userData.group) !== UserGroup.PATIENT && Number(userData.group) !== UserGroup.PARTNER) ||
      userData.privileges.toString().split(',').includes(PrivilegeKeys.TAG_MESSAGE)
    ) {
      this.advanceSearchControls.push(
        new ControlType({
          key: 'tags',
          label: this.toolTipService.getTranslateData('LABELS.FILTER_BY_TAGS'),
          value: [],
          controlType: 'dropdown-tag-search',
          settings: {
            singleSelection: false,
            text: this.toolTipService.getTranslateData('LABELS.SELECT_TAGS'),
            selectAllText: this.toolTipService.getTranslateData('LABELS.ALL_TAGS'),
            unSelectAllText: this.toolTipService.getTranslateData('LABELS.CLEAR_TAGS'),
            classes: 'select-tags',
            enableSearchFilter: true,
            enableFilterSelectAll: true,
            badgeShowLimit: 2
          },
          options: [],
          defaultOptions: [],
          order: 6,
          class: 'col-md-4'
        })
      );
    }
    this.advanceSearchControls = [
      ...this.advanceSearchControls,
      new ControlType({
        key: 'priority',
        label: this.toolTipService.getTranslateData('LABELS.FILTER_BY_PRIORITY'),
        value:
          this.isInbox && this.structureService.inboxFilterApplied === Filter.ADVANCE && this.structureService.priorityFilterValue
            ? this.priorityOptions.filter((item) => item.key === String(this.structureService.priorityFilterValue))
            : '',
        controlType: 'single-select-dropdown',
        options: this.priorityOptions,
        defaultOptions: this.priorityOptions,
        order: 6,
        class: 'col-md-4'
      }),
      new ControlType({
        key: 'flag',
        label: this.toolTipService.getTranslateData('LABELS.FILTER_BY_FLAG'),
        value:
          this.isInbox && this.structureService.inboxFilterApplied === Filter.ADVANCE && this.structureService.flagFilterValue
            ? this.flagOptions.filter((item) => item.key === String(this.structureService.flagFilterValue))
            : '',
        controlType: 'single-select-dropdown',
        options: this.flagOptions,
        defaultOptions: this.flagOptions,
        order: 6,
        class: 'col-md-4'
      }),
      new ControlType({
        key: 'chatThreadTypes',
        label: this.toolTipService.getTranslateData('LABELS.MSG_TYPE'),
        value:
          this.storeService.getStoredData(Store.ACTIVE_MESSAGE_ADVANCE_SEARCH_FILTER) &&
          this.storeService.getStoredData(Store.ACTIVE_MESSAGE_ADVANCE_SEARCH_FILTER).chatThreadTypes
            ? this.chatThreadTypes
                .filter((item) =>
                  this.storeService.getStoredData(Store.ACTIVE_MESSAGE_ADVANCE_SEARCH_FILTER).chatThreadTypes.includes(Number(item.key))
                )
                .map((i) => ({ key: i.key, value: i.value }))
            : '',
        controlType: 'dropdown',
        order: 6,
        class: `${userData && Number(userData.group) !== UserGroup.PATIENT ? 'col-md-4' : 'd-none'}`,
        options: this.chatThreadTypes,
        defaultOptions: this.chatThreadTypes
      })
    ];
    this.getMessageTags();
  }

  getMessageTags() {
    const args: any = [1, false, this.selectedTenantId];
    this.inboxService.getTagoptions(...args).then((result: any) => {
      if (result) {
        const messageTags = result.map((item) => {
          return { ...item, itemName: item.name };
        });
        const selectedTags =
          this.isInbox && this.structureService.inboxFilterApplied === Filter.ADVANCE && !isBlank(this.structureService.filteredTags)
            ? this.structureService.filteredTags
            : [];
        if (!isBlank(selectedTags)) {
          this.modelData.tags = messageTags.filter((item) => selectedTags.includes(String(item.id)));
          this.advanceSearchControls[0].value = messageTags.filter((item) => selectedTags.includes(String(item.id)));
        }
        this.advanceSearchControls[0].options = messageTags;
        this.advanceSearchControls[0].defaultOptions = messageTags;
      }
    });
  }

  applyAdvanceSearch(event: any) {
    this.advanceSearch.emit(event);
  }
}
