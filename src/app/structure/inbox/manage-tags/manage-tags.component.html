<div
  class="modal fade forward-modal message-tag-modal-container"
  id="messageTagModalNew"
  role="dialog"
  aria-labelledby="exampleModalLabel"
  aria-hidden="true"
>
  <div class="modal-dialog modal-dialog-centered" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title" id="messageTagModalLabel">{{ 'LABELS.TAG_MANAGER' | translate }}</h4>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <form [formGroup]="messageTagForm" id="messageTagForm">
          <span><i class="fa fa-info-circle"></i>&nbsp;{{ 'MESSAGES.SHOW_ONLY_INTEGRATION_DISABLED_TAGS' | translate }}</span>
          <div style="margin-bottom: 9px" class="mt-2">
            <select class="form-control select2" formControlName="messageTags" id="messageTags" multiple>
              <option *ngFor="let tags of availableTags" [value]="tags.id">
                {{ tags.name }} {{ tags.typeName ? '(' : '' }}{{ tags.typeName ? tags.typeName : '' }}{{ tags.typeName ? ')' : '' }}
              </option>
            </select>
          </div>
        </form>
        <br />
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="close()">{{ 'BUTTONS.CLOSE' | translate }}</button>
        <button type="button" class="btn btn-primary" id="submitTag" [disabled]="disableButton" (click)="addTags()" style="cursor: pinter">
          {{ 'BUTTONS.SAVE' | translate }}
        </button>
      </div>
    </div>
  </div>
</div>
