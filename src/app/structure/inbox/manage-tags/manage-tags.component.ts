import { Component, EventEmitter, Output } from '@angular/core';
import { Form<PERSON>uilder, FormControl, FormGroup } from '@angular/forms';
import { UserFacingType } from 'app/constants/constants';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { isBlank } from 'app/utils/utils';
import { InboxService } from '../inbox.service';

declare const $: any;
@Component({
  selector: 'cat-manage-tags',
  templateUrl: './manage-tags.component.html',
  styleUrls: ['./manage-tags.component.scss']
})
export class ManageTagsComponent {
  availableTags = [];
  disableButton = false;
  getAllTagsAPILoading = false;
  chatroomUsersListFull: any = [];
  messageTagForm = new FormGroup({
    messageTags: new FormControl()
  });
  userData;
  existingTagsTenantId;
  activeInstanceData;
  selectedThread;
  @Output() finalTagsEmit: EventEmitter<any> = new EventEmitter<any>();

  constructor(
    private fb: FormBuilder,
    private inboxService: InboxService,
    private structureService: StructureService,
    private toolTipService: ToolTipService
  ) {
    if (this.structureService.userDetails) {
      this.userData = JSON.parse(this.structureService.userDetails);
    }
  }

  show(threadDetails) {
    this.selectedThread = threadDetails;
    this.openTagManager();
    $('#messageTagModalNew').modal({ backdrop: 'static', keyboard: false }, 'show');
  }

  close() {
    this.selectedThread = null;
    $('#messageTagModalNew').modal('hide');
    this.messageTagForm = this.fb.group({
      messageTags: ['']
    });
  }

  openTagManager() {
    this.getTags();
    setTimeout(() => {
      $('#messageTags').trigger('change');
      $('#users').trigger('change');
    }, 600);

    $('#messageTags')
      .select2({ placeholder: this.toolTipService.getTranslateData('LABELS.SELECT_ONE_OR_MORE_TAGS') })
      .on('change', (e) => {
        this.disableButton = isBlank(e.target.value);
      });
  }

  getTags() {
    this.disableButton = true;
    const args: any = [1];
    if (this.selectedThread && this.selectedThread.selectedTenantId) {
      args.push(true);
      args.push(this.selectedThread.selectedTenantId);
    } else {
      args.push(0);
      args.push(false);
    }
    // integrationEnabled status as true
    args.push(true);
    this.inboxService
      .getTagoptions(...args)
      .then((result: any) => {
        if (result) {
          this.availableTags = result;
          this.availableTags = this.availableTags.filter((availableTag) => {
            return (
              (this.userData.group === '3' &&
                (availableTag.userType === UserFacingType.PATIENT || availableTag.userType === UserFacingType.STAFF_PATIENT)) ||
              (this.userData.group !== '3' &&
                (availableTag.userType === UserFacingType.STAFF ||
                  availableTag.userType === UserFacingType.STAFF_PATIENT ||
                  availableTag.userType === null))
            );
          });
        }
        this.getAllTagsAPILoading = false;
      })
      .catch(() => {
        this.getAllTagsAPILoading = false;
      });
  }

  addTags() {
    const selectedTags = $('#messageTags')
      .val()
      .map((tagId) => {
        const id = tagId
          .substr(tagId.indexOf(':') + 1)
          .replace(/'/g, '')
          .replace(/\s/g, '');
        return this.availableTags.find((tag) => Number(tag.id) === Number(id));
      });
    this.finalTagsEmit.emit(selectedTags);
    this.close();
  }
}
