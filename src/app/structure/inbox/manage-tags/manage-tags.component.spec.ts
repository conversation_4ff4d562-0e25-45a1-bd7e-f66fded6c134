import { NO_ERRORS_SCHEMA } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Apollo, ApolloModule } from 'apollo-angular';
import { PermissionService } from 'app/services/permission/permission.service';
import { ColorPickerService } from 'ngx-color-picker';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { provideClients } from 'test-utils';
import { ManageTagsComponent } from './manage-tags.component';
import { StructureService } from 'app/structure/structure.service';
import { AuthService } from 'app/structure/shared/auth.service';
import { SharedService } from 'app/structure/shared/sharedServices';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { InboxService } from '../inbox.service';
import { StoreService } from 'app/structure/shared/storeService';

describe('MessageTagTypeEditComponent', () => {
  let component: ManageTagsComponent;
  let fixture: ComponentFixture<ManageTagsComponent>;
  let inboxservice: InboxService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      providers: [
        StructureService,
        Apollo,
        AuthService,
        SharedService,
        ToolTipService,
        WorklistIndexdbService,
        TranslateService,
        ColorPickerService,
        PermissionService,
        { provide: InboxService, useValue: inboxservice },
        StoreService
      ],
      declarations: [ManageTagsComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        RouterTestingModule,
        HttpModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF,
        }),
        TranslateModule.forRoot(),
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  });

  beforeEach(() => {
    inboxservice = TestBed.get(InboxService);
    fixture = TestBed.createComponent(ManageTagsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
});
