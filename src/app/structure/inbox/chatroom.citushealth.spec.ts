import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CommonModule, DatePipe } from '@angular/common';
import { ChatroomComponent } from './chatroom.citushealth';
import { StructureService } from '../structure.service';
import { ChatService } from './chat.service';
import { StaticDataService } from '../static-data.service';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NO_ERRORS_SCHEMA, NgModule } from '@angular/core';
import { CommonTestingModule } from 'app/utils/common-testing.module';
import { TranslateModule } from '@ngx-translate/core';
import { filterUsersListPipe } from './inbox-filterUsersList.pipe';
import { SearchFilterPipe, SearchFilterRoleTreeViewPipe, SearchRoleFilterPipe, pluralizeFilterPipe } from './inbox-search.pipes';
import { virtualPatientFilterPipe } from './chatroom-filterVirtualPatient.pipes';
import { userPersistantMessageFilterFnPipe } from './inbox-unreadmessage.pipes';
import { SortPipe } from './inbox-date-filter.pipes';
import { PhonePipe } from './inbox-modal-filter';
import { InboxService } from 'app/structure/inbox/inbox.service';
import { Modal } from '../shared/commonModal';
import { SharedModule } from '../shared/sharedModule';
import { PdfViewService } from 'app/structure/pdfview.service';
import { HttpService } from 'app/services/http/http.service';
import { MaskedMessageService } from '../masked-message/masked-message.service';
import { FormsService } from '../forms/forms.service';
import { CommonVideoService } from "../../../assets/lib/universal-video/common-video.service";
import { GlobalDataShareService } from './../../structure/shared/global-data-share.service';
import { ToolTipService } from '../tool-tip.service';
import { Vidyo } from '../../../assets/lib/universal-video/vidyo/vidyo.service';
import { MessageService } from 'app/services/message-center/message.service';
import { UserService } from 'app/services/user/user.service';
import { ManageSitesService } from '../manage-sites/manage-sites.service';
import { configMock, userDataMock } from '../../../mocks/user-data-config';
import { of } from 'rxjs/observable/of';
import { Subject } from 'rxjs/Subject';
import { BehaviorSubject } from 'rxjs/BehaviorSubject';
@NgModule({
    declarations: [PhonePipe, SortPipe, userPersistantMessageFilterFnPipe, filterUsersListPipe, SearchFilterPipe, SearchFilterRoleTreeViewPipe, SearchRoleFilterPipe, pluralizeFilterPipe, virtualPatientFilterPipe],
    exports: [PhonePipe, SortPipe, userPersistantMessageFilterFnPipe, filterUsersListPipe, SearchFilterPipe, SearchFilterRoleTreeViewPipe, SearchRoleFilterPipe, pluralizeFilterPipe, virtualPatientFilterPipe]
})
class TestModule {}

describe('ChatroomComponent', () => {
    let component: ChatroomComponent;
    let fixture: ComponentFixture<ChatroomComponent>;
    let structureService: StructureService;
    let chatService: ChatService;
    let inboxService: InboxService;
    let staticDataService: StaticDataService;
    let maskedMessageService: MaskedMessageService;
    let pdfViewService: PdfViewService;
    let httpService: HttpService;
    let modal: Modal;
    let formsService: FormsService;
    let messageService: MessageService;
    let userService: UserService;
    let inboxServiceMock = jasmine.createSpyObj('InboxService', ['scheduleSelectionFilter', 'checkInfusionHours', 'getTagoptions']);
    let structureServiceMock: any;
    let fetchChatRoomMessagesMock: Subject<number>;
    let chatServiceMock:any;
    let socketConnectedMock: BehaviorSubject<boolean>;
    beforeEach(async () => {
        localStorage.setItem('activeMessage', JSON.stringify({ }));
        fetchChatRoomMessagesMock = new Subject<number>();
        socketConnectedMock = new BehaviorSubject<boolean>(false);
        structureServiceMock = jasmine.createSpyObj('StructureService', ['fetchChatRoomMessages', 'getApiBaseUrl', 'subscribeSocketEvent','socketConnected', 'hasInitiateChatPermission', 'userDataConfig', 'getCookie', 'getUserdata', 'userDetails']);
        structureServiceMock.fetchChatRoomMessages = fetchChatRoomMessagesMock;
        structureServiceMock.socketConnected = socketConnectedMock.asObservable();
        chatServiceMock = jasmine.createSpyObj('ChatService', ['getChatroomMessages', 'getUsersListByRoom']);
        chatServiceMock.getChatroomMessages.and.returnValue(new Promise((resolve, reject) => {
            // Simulate async operation
            resolve('Chatroom messages loaded');
        }));
        chatServiceMock.getUsersListByRoom.and.returnValue(new Promise((resolve, reject) => {
            // Simulate async operation
            resolve('Users list loaded');
        }));
        inboxServiceMock.getTagoptions.and.returnValue(new Promise((resolve, reject) => {
            // Simulate async operation
            resolve('Tags loaded');
        }));
        // Mock the userDataConfig property
        structureServiceMock.userDataConfig = JSON.stringify(configMock); // Valid JSON
        structureServiceMock.userDetails = JSON.stringify(userDataMock);
        await TestBed.configureTestingModule({
            declarations: [ChatroomComponent],
            imports: [CommonModule, SharedModule, TestModule, HttpClientTestingModule, RouterTestingModule, FormsModule, ReactiveFormsModule, TranslateModule.forRoot(), CommonTestingModule],
            providers: [
                MaskedMessageService, 
                HttpService, 
                PdfViewService, 
                Modal, 
                DatePipe, 
                {provide: StructureService, useValue: structureServiceMock },  
                { provide: ChatService, useValue: chatServiceMock },
                { provide: UserService, useValue: userService },   
                { provide: MessageService, useValue: messageService },  
                { provide: CommonVideoService, useValue: jasmine.createSpyObj('CommonVideoService', ['checkVideoPluginLoaded']) }, 
                GlobalDataShareService,  
                ToolTipService, 
                { provide: InboxService, useValue: inboxServiceMock }, 
                { provide: FormsService, useValue: formsService }, 
                StaticDataService, 
                Vidyo,
                ManageSitesService
            ],
            schemas: [NO_ERRORS_SCHEMA]
        }).compileComponents();
    });
    beforeEach(() => {
        fixture = TestBed.createComponent(ChatroomComponent);
        component = fixture.componentInstance;
        structureService = TestBed.get(StructureService);
        chatService = TestBed.get(ChatService);
        inboxService = TestBed.get(InboxService);
        staticDataService = TestBed.get(StaticDataService);
        maskedMessageService = TestBed.get(MaskedMessageService);
        pdfViewService = TestBed.get(PdfViewService);
        httpService = TestBed.get(HttpService);
        modal = TestBed.get(Modal);
        (structureService.subscribeSocketEvent as jasmine.Spy).and.returnValue(of({users: []}));
        (inboxServiceMock.checkInfusionHours as jasmine.Spy).and.returnValue(of({ isWorkingHours: true}));
        fixture.componentInstance.config = JSON.stringify(configMock);
        fixture.componentInstance.userData = {config: configMock};
        fixture.componentInstance.userDataConfig = {config: configMock};
        fixture.componentInstance.activeMessage = {};
        fixture.componentInstance.privileges = '';
        fixture.detectChanges();
    });

    it('should create', () => {
        expect(component).toBeTruthy();
    });
    
    it('should initialize component', () => {
        spyOn(component, 'ngOnInit').and.callThrough();
        component.ngOnInit();
        expect(component.ngOnInit).toHaveBeenCalled();
    });

    it('should call ChatRoomUsers on initialization', () => {
        spyOn(component, 'ChatRoomUsers');
        component.ngOnInit();
        expect(component.ChatRoomUsers).toHaveBeenCalled();
    });

    it('should verify userDataConfig from StructureService', () => {
        expect(structureService.userDataConfig).toBe(JSON.stringify(configMock));
    });

    it('should handle file upload', () => {
        component.pageHeading = 'Test User';
        const event = { target: { files: [{ name: 'test.png' }] } };
        spyOn(component, 'isValidFile').and.returnValue(true);
        component.uploadFileConfirm(event);
        expect(component.withAttachment).toBe(true);
    });

    it('should load more messages', () => {
        spyOn(component, 'loadMore').and.callThrough();
        component.loadMore(true);
        expect(component.loadMore).toHaveBeenCalled();
    });

    it('should show image modal', () => {
        const targetEvent = { target: { getAttribute: () => 'test.jpg' } };
        component.showImage(targetEvent);
        expect(component.isModalOpen).toBe(true);
    });

    it('should close modal', () => {
        component.closeModal();
        expect(component.isModalOpen).toBe(false);
    });

    it('should call subscribeSocketEvent with onGoingVideoCall', () => {
        component.ngOnInit();
        expect(component._structureService.subscribeSocketEvent).toHaveBeenCalledWith('onGoingVideoCall');
    });
});
