import { TestBed } from '@angular/core/testing';
import { UserGroup } from 'app/constants/constants';
import { userFilterPipe, PhonePipe } from './inbox-modal-filter';
import { StructureService } from '../structure.service';

describe('userFilterPipe', () => {
  let pipe: userFilterPipe;
  let structureService;

  beforeEach(() => {
    const spy = jasmine.createSpyObj('StructureService', ['isConfigEnabled']);
    TestBed.configureTestingModule({
      providers: [userFilterPipe, { provide: StructureService, useValue: spy }]
    });
    pipe = TestBed.get(userFilterPipe);
    structureService = TestBed.get(StructureService);
  });

  it('should filter users by patient role', () => {
    const users = [{ roleId: 15 }, { roleId: UserGroup.PATIENT }, { roleId: UserGroup.PARTNER }];
    const result = pipe.transform(users, 'patient');
    expect(result.length).toBe(1);
    expect(result[0].roleId).toBe(UserGroup.PATIENT);
  });

  it('should filter users by partner role', () => {
    const users = [{ roleId: 15 }, { roleId: UserGroup.PATIENT }, { roleId: UserGroup.PARTNER }];
    const result = pipe.transform(users, 'partner');
    expect(result.length).toBe(1);
    expect(result[0].roleId).toBe(UserGroup.PARTNER);
  });

  it('should filter users by staff role', () => {
    const users = [{ roleId: 15 }, { roleId: UserGroup.PATIENT }, { roleId: UserGroup.PARTNER }];
    const result = pipe.transform(users, 'staff');
    expect(result.length).toBe(1);
    expect(result[0].roleId).toBe(15);
  });

  it('should filter users based on role', () => {
    const users = [
      { roleId: 15, password: true },
      { roleId: UserGroup.PATIENT, password: false },
      { roleId: UserGroup.PARTNER, password: false }
    ];
    const result = pipe.transform(users, 'staff');
    expect(result.length).toBe(1);
    expect(result[0].roleId).toBe(15);
  });

  it('should mark users as contactable if password is true', () => {
    const users = [{ roleId: UserGroup.PATIENT, password: true, email: '', mobile: '', countryCode: '' }];
    const result = pipe.transform(users, 'patient');
    expect(result[0].noContactAvailable).toBe(false);
  });

  it('should mark users as not contactable if conditions are not met', () => {
    structureService.isConfigEnabled.and.returnValue(true);

    const users = [{ roleId: UserGroup.PATIENT, password: false, email: '', mobile: '', countryCode: '' }];
    const result = pipe.transform(users, 'patient');
    expect(result[0].noContactAvailable).toBe(true);
  });

  it('should mark users as contactable if email is valid and email notifications are enabled', () => {
    structureService.isConfigEnabled.and.returnValue(true);

    const users = [
      { roleId: UserGroup.PATIENT, password: false, email: '<EMAIL>', enable_email_notifications: '1', mobile: '', countryCode: '' }
    ];
    const result = pipe.transform(users, 'patient');
    expect(result[0].noContactAvailable).toBe(false);
  });

  it('should mark users as contactable if mobile and countryCode are not blank and SMS notifications are enabled', () => {
    structureService.isConfigEnabled.and.returnValue(true);

    const users = [
      { roleId: UserGroup.PATIENT, password: false, email: '', mobile: '**********', countryCode: '1', enable_sms_notifications: '1' }
    ];
    const result = pipe.transform(users, 'patient');
    expect(result[0].noContactAvailable).toBe(false);
  });

  it('should mark users as not contactable if email is invalid and mobile is blank', () => {
    structureService.isConfigEnabled.and.returnValue(true);

    const users = [{ roleId: UserGroup.PATIENT, password: false, email: 'invalid', mobile: '', countryCode: '' }];
    const result = pipe.transform(users, 'patient');
    expect(result[0].noContactAvailable).toBe(true);
  });

  it('should mark users as not contactable if config is disabled', () => {
    structureService.isConfigEnabled.and.returnValue(false);

    const users = [{ roleId: UserGroup.PATIENT, password: false, email: '<EMAIL>', mobile: '**********', countryCode: '1' }];
    const result = pipe.transform(users, 'patient');
    expect(result[0].noContactAvailable).toBe(true);
  });
});

describe('PhonePipe', () => {
  let pipe: PhonePipe;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [PhonePipe]
    });
    pipe = TestBed.get(PhonePipe);
  });

  it('should format phone number correctly', () => {
    const result = pipe.transform('**********', null);
    expect(result).toBe('(*************');
  });

  it('should return empty string if no phone number is provided', () => {
    const result = pipe.transform('', null);
    expect(result).toBe('');
  });

  it('should return original value if phone number contains non-numeric characters', () => {
    const result = pipe.transform('abc123', null);
    expect(result).toBe('abc123');
  });

  it('should format short phone numbers correctly', () => {
    const result = pipe.transform('123', null);
    expect(result).toBe('(123');
  });
});
