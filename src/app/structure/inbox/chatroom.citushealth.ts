import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ViewChild, ChangeDetector<PERSON>ef, HostListener, ViewEncapsulation } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { StructureService } from '../structure.service';
import { ChatService } from './chat.service';
import { InboxService } from './inbox.service';
import { Router, ActivatedRoute, Params, NavigationEnd } from '@angular/router';
import { DatePipe } from '@angular/common';
import { userFilterPipe } from './inbox-modal-filter';
import { Modal } from "../shared/commonModal";
import { DomSanitizer } from '@angular/platform-browser';
import { SharedService } from '../shared/sharedServices';
import { SignPadComponent } from './../shared/signPad/sign-pad.component';
import { MaskedMessageService } from '../masked-message/masked-message.service';
import { FormsService } from '../forms/forms.service';
import { CommonVideoService } from "../../../assets/lib/universal-video/common-video.service";
import { GlobalDataShareService } from './../../structure/shared/global-data-share.service';
import { ToolTipService } from '../tool-tip.service';
import { Observable, Subject, Subscription } from 'rxjs';
import { getDistinctArray, isBlank, messageCenterGUID, getOooStatusBasedOnTime,isNil, convertUTCToTimeZoneDateTimeSplit } from 'app/utils/utils';
import { getCaretCoordinates } from 'app/utils/mention-utils';
import { CHECKED, CONSTANTS, CharCodes, Filter, MessagePriority, UserGroup, NOTIFY_DELAY_TIME_COMMON, avatarUrlDefault, avatarUrlThumbs, MessageDeliveryStatus, MessageType, ChatAvatarOptions, DefaultPics, ContentTypes, MessageTagType, GroupType, MessageCategory, IntegrationType,DateFormat, msgHistoryDateTimeFormat } from '../../constants/constants';
import { NGXLogger } from 'ngx-logger';
import { StaticDataService } from '../static-data.service';
import { PdfViewService } from 'app/structure/pdfview.service';
import { HttpService } from 'app/services/http/http.service';
import { APIs } from 'app/constants/apis';
import { MessageService } from 'app/services/message-center/message.service';
import { GetChatMessages, GetMaskedRepliesPayload, defaultGetMaskedRepliesPayload, GroupsListingRequest, ManageChatParticipantsPayload, RestoreType} from 'app/models/message-center/messageCenter';
import { UserService } from 'app/services/user/user.service';
import { ChatroomUserListType, UpdateChatroomParticipantsResponse } from './chatroom-users-list/chatroom-users-list.interface';

var jstz = require('jstz');
const timezone = jstz.determine();
let moment = require('moment/moment');
declare var $: any;
declare var autosize: any;
declare var swal: any;
declare var NProgress: any;
@Component({
    selector: 'app-chatroom',
    templateUrl: './chatroom.html',
    styleUrls: ['./chatroom.scss']
})
//TODO: Scope for code revamp CHP-13396
export class ChatroomComponent implements OnInit {
    @ViewChild('userSearch') srch:ElementRef;
    @ViewChild('userSearchFromChat') search:ElementRef;
    @ViewChild('userSearchFromForward') searchForward:ElementRef; 
    @ViewChild('emojipicker') emojipicker: ElementRef;
    documentType: any = "";
    checkboxValue: boolean = false;
    showOooMessage: boolean = false;
    oooMessage: string;
    offlineStatus : boolean = false;
    isMultipleOfflineUser = false;
	showModal: boolean = false;
    inboxData: any ;
    inboxDataFirstPage: any = this._structureService.inboxDataFirstPage ? this._structureService.inboxDataFirstPage : [];
    searchData = {};
    userId = '';
    iconPath = '';
    CSRMessages = [];
    usersArray=[];
    prevText;
    alernateContactsData =[];
    showAlternattab =false;
    searchFlag = false;
    disableButton = false;
    inviteEnableButton = false;
    totalCount;
    contentLimit = 25;
    currentPage = 1;
    contentOffset=0;
    assocSiteId : any;
    siteIdEmpty = false;
    assocSelected:any;
    pageCountMessage: any = 0;
    previousPage;
    Invitealternates='';
    existingTagstenantId;
    existingTagstenantName;
    loadingGroups=true;
    searchInboxkeyword: string;
    triggeredChangeEvent=false;
    initiatorCallTune = false;
    hideInitiatorAudioBtn = false;
    privileges = this._structureService.getCookie('userPrivileges');
    chatEnterToSend = this._structureService.getCookie('chatEnterToSend');
    chatEnterCheckboxValue: boolean = false;
    messageFilter: any = "1";
    patId;
    page= 0;
    last = 0;
    associatePatientLoading=false;
    assosiatedPatients: any = [];
    selectedAssosiatePatient: any = '';
    selectedPatient: any = '';
    selectedAssosiatePatientName:any='';
    selectedAssosiatePatientId=false;
    reloadChatRoom: Function;
    refreshTimeout: Function;
    hideoptionShow = true;
    selectedRow: Number;
    selectedRowForward: Number;
    loadMoreStatus=false;
    uersListForRolesAdding=[];
    isloadingTime=false;
    isStaffsloadingTime=false;
    loadMoreButtonText = 'Loading..';
    loadMoreButtonTextInvite='Loading..';
    noMessage =false;
    selectedchatroom=false;
    tenantIDForInviteList=0;
    browserVideoSupport = true;
    internalSiteId = null;
    staffRolesList=[];
    UsergroupIds;
    initialLoadingListCount=0;
    crossTenantOptionsChatWith=[];
    chatWithTenantFilter:any = {
        selectedTenant : null,
        tenants: [],
        filterEnabled: false,
        enabledReset: false
    };
    invitePageCount=0;
    organizationSwitchPrivilege = false;
    selectedTenant:any;
    chatWithUserType:any ='enrolled';
    moreToLoad;
    data: any;
    sign: any;
    enablePushResponse: any;
    newMessagedetails = [];
    chtRoomUsers;
    lastUserActivityTime;
    enableChatWindowSideBar;
    chatroomUsersList: any=[];
    filteredMentionUsers: any=[];
    chatroomUsersListFull: any=[];
    messageSettingsLoader:boolean;
    INAPPButtonShown:boolean = true;
    recipient:boolean = false;
    showAppLessMode:boolean = false;
    msgGrpSites : any=[];
    pdgAssosiatedPatientArray: any = [];
    messageFetching: any = false;
    allowVideoChat = true;
    optionShow: any = 'staff';
    isArchievedChatroom = false;//Need to get from chatroom users
    userTyping;
    userListChatwith: any;
    clinicianDataRoleWise: any;
    usersl: any;
    otherTenantClinicianDataRoleWise:any;
    memberDataGroupWise: any;
    selectedRoleWiseRecipients = [];
    staffsUnderRolesList=[];
    selectedRoleWiseRecipientsName = '';
    setupComponentStatus: any;
    userDataConfig: any;
    checkComponentStatus = false;
    order = [];
    orderMain = [];
    orderMainRoles = [];
    orderGroup = {};
    orderGroupMain = [];
    orderMainGroups = [];
    selectedUserRoles = [];
    allMessageGroups = [];
    offset = 0;
    limit = 25;
    hideLoadMore = false;
    invitedResponse: any;
    column3 = false;
    column6 = false;
    clinicalUserDetails;
    userListChatroom: any;
    chatWithHeading;
    chatWith;
    hasChatWithGroup;
    modalHead;
    ispdgs;
    userList: any;
    messageGroup: any = 'groups';
    messageGroupUsersList=[];
    archived: any = localStorage.getItem('archived');
    reRoutedUser;
    forwardingStatus = false;
    selectedBehaviour: any = 'keep_forwarder';
    messageForwardingBehaviour: any;
    usersList: any;
    clinicianLoad: any;
    defaultNurses: any;
    usersListByRoleId: any;
    scheduledPrimaryCar: any;
    primaryCareClinicians: any;
    primaryClinicalUsers: any;
    typing: any = false;
    timeout: any = undefined;
    timeoutFunction: Function;
    userCheckMessageRead:Function;
    userType: Function;
    usersListScheduleCheck: any = [];
    translatedMessagesDetails = [];
    uploadData;
    userDisplayName;
    profilePic;
    sourceLanguage;
    isModalOpen = false;
    title;
    responseTranslation;
    subTitle;
    modalBody;
    cancelLabel = false;
    imageUploadProgress = false;
    avatarBasePath;
    translatedMessages;
    pdfOrDocSrc = "";
    pdfOrDocUrl = "";
    pdfOrDoc = "";
    showIframe = false;
    showVideoFrame = false;
    beforeLoad = false;
    toggleTranslation = false;
    autoTranslateFailureCount: any = 0;
    isSignpad = false;
    signMessageData;
    messageTagForm: FormGroup;
    availableTags = [];
    messageInfoDetails=[];
    recipientMessageInfoDetails:any={};
    messageInfoData={};
    messageInfo=false;
    recipientmessageInfo=false;
    messageInfoLoading=true;
    usersListForTagMessage = [];
    clickedRoleId=0;
    selectedTags;
    selectedUserForTag;
    taggedMessageDetails: any=[];
    tagTrackActivityText: any = "added";
    approver: any;
    formApproverUsersListSelection: any;
    userNotSelected: any = false;
    tagNotSelected: any = false;
    goToNewChat: Function;
    caregiverTitle: any;
    tagPatient:any='';
    userMessagePolling;
    messageReplyTimeout;
    selectedPatientId:any = '';
    tDiff;
    doNotReplyStatus;
    showDoubleVerification = false;
    showDoubleVerificationStatus = 0;
    showDoubleVerificationMsg = 'Is this an urgent matter that requires immediate attention or is it ok to have one of our staff respond when the office opens for the next business day? Please choose ‘yes, urgent’ for urgent attention.';
    showNoUserInChatroomMsg = 'This chat thread has been closed.';
    setDoubleVerificationStatus: Function;
    branchHourChangeTimer: any;
    timeoutRefresh;
    patientoffDutyMessage;
    startTimeoutRefresh;
    tempCountManage = true;
    joinChat = false;
    patient_identity;
    isSubListVisible = false;
    chatroomSelected;
    patientFace: any = false;
    newMessageFlag = false;
    flag = true;
    count: any = '';
    selectedFiles = [];
    loadAttachment = false;
    withAttachment = false;
    tagProcessingText;
    withAttachmentEvent: any;
    reloadSubscriber: any;
    allMembers = [];
    videoTiles = [];
    messageGroupSubscriber:any;
    enableVideoButton: boolean = false;
    microphonePrivacy: boolean = false;
    videoFull: boolean = false;
    applessMessaging:any;
    cameraPrivacy: boolean = false
    chatDisable:boolean;
    previousLength=0;
    dialogMessage: string = ''
    initiatorEnd:boolean = false;
    onVideoCall:boolean = false;
    enableVideoChatButton;      
    usersLoaded:boolean = false;
    msgSubEdit; 
    chatsubName;
	joined:boolean = false;
    crossTenantOptions = [];
    otherTenantStaffList = [];
    crossTenantName:string;
    myAssociatedPatient = null;
    filterMessage=0;
    searchMessage =0;
    updateInboxDataSubscriber: any;
    updateActiveMessageInChatroomSubscriber: any;
    showHeader;
    showFooter;
    tagMultipleMessageOption:Boolean=false;
    canReplyForMaskedMessage:Boolean=false;
    chatWithModalShown: boolean = false;
    disableReplySpan : Boolean = false;
    isEnabled: boolean = false;
    noMoreItemsAvailable = {
        users: false
    };
    chatwithPageCount = {
        staffs: 0,
        patients: 0,
        partner: 0
    }
    loadMoreSearchValue:any = undefined;
    loadMoremessage = {
        users: 'Load more'
    };
    callFromInitialCountChatWithUserlist:boolean = false;
    chatWithUsersLoading = true;
    chatWithLoader:any = {
        groups: true,
        staffs: true,
        partner: true,
        otherTenantstaff: true,
        patients: true,
        otherTenantPatients: true
    };
    hideOnmaskedReplyGenerated = false;


    messageLoader:any = {
        messages : true
    }
    isGrouploadingTime=false;
    clickedGroup;
    messageGroupSelected=false;
    dummyCheckedGroups = [];

    messageFlagCount:number = 0;
    priorityFilterValue = 0;
    filteredTags = [];
    filteredMentions = [];
    flagFilterValue:number = 0;
    getAllTagsAPILoading:boolean = false;
    chatroomUsersInvite = new Subject();
    forwardUserList = new Subject();
    invitedUserOnVideoCall: any;
    frwdChatroomId: any;
    reloadChatroomWithNewThreadSubs;
    selectSiteId = '';
    chatroomPatientSiteId:any = '0';
    hideSiteSelection:boolean;
    hideInviteSiteSelection: boolean = true;
    hideForwardSiteSelection: boolean;
    hideAssociateSiteSelection:boolean;
    eventsSubject: Subject<void> = new Subject<void>();
    tabSelectedData:any;
    isForwardSiteSelected :boolean;
    isInviteSiteSelected : boolean;
    staffUnderRole:any = [];
    expandedRole:any = [];
    tempChatroomPatientSiteId:any = '0';
    patientSiteId:any = [];
    isFilter = false;
    isSiteFilterDisabled = false;
    searchValue = '';
    showForwardModal = false;
    showInviteModal = false;
    inviteSiteId = '0';
    forwardSiteId = '0';
    siteIds :any;
    multiSiteEnable: boolean;
    enableCrossSite: boolean;
    disableChatroom : any;
    sendActive:boolean=false;

    fromModelPage : any;
    isEnterToFrwrdSiteFun : boolean;
    dynamicFilterValue = false;
    tempPatientSiteId:any;
    loadSiteFilter = false;
    selecteGroupOrPdgTab = false;
    isPdg:boolean = false;
    isMsgGrp:boolean = false;
    callChatRoomUsers:boolean = false;
    showNullErrorMsg = false;
    UserGroup = UserGroup;
    MESSAGE_PRIORITY = MessagePriority;
    FILTER = Filter;
    CHECKED = CHECKED;
    messagePriority = MessagePriority.NORMAL;
    MessagePriorityData = this.staticDataService.getPriorities();
    dropdownSettings;
    isUserPatient = false;
    isUserPartner = false;
    isUserStaff = false;
    resetAdvanceSearch = false;
    resetQuickSearch = false;
    chatroomFilterApplied = '';
    userGroup = UserGroup;
    isPdgChat = '';
    noImage : string;
    messageType = MessageType;
    defaultPics = DefaultPics;
    chatAvatarOptions = ChatAvatarOptions;
    maskedMessageSubList = [];
    isSubListFetching = false;
    messageCategory = MessageCategory;
    subThreadSelected = {};
    chatroomUsersListTitle = '';
    chatroomRolesListTitle = '';
    chatParticipants: any[];
    roleParticipants: any[];
    allParticipants: any[];
    toggleChatroomUserList = false;
    private socketEventSubscriptions: Subscription[] = [];
    selectedRolesFromGroup: any[];
    chatroomSiteIds = '0';
    chatroomUsersCount = 0;
    showSitefilter = false;
    pdgSiteIds: any;
    resetPdgData = false;
    labelSite = '';
    deleteUndoDateFormat = DateFormat.MMDDYY_HHMM_A;
    selectedPdgGroups: any[];
    admissionId = '';
    isMultiAdmissionsEnabled = false;
    admissionNotSelected = false;
    tagMsgSelectedAdmissionId = ''; //To pre-populate admission id tagged with message
    patientTagInfo: any;
    patientInfo = [];
    disablePatientSelectInTag = false;
    selectedMessageData;
    initiateChatPermission = false;
    allowIntegrationTags = true;
    videoCallEndByInitiator: Subscription;
    receiverIsNotContactable: boolean = false;
    oooUsers: any[];
    messageHistory: any[];
     //Close the participants list div on user click happens outside
     @HostListener('document:click', ['$event'])
        clickout(event) {
        if (!this.toggleChatroomUserList) {
            return;
        }
        const dropdown = document.getElementById('div-parent-chatParticipants');
        if (!dropdown.contains(event.target) ) {
            // Clicked outside the dropdown, close it
            this.toggleChatroomUserList = false;
        }
    }
    $messageDeleteRestoreSubject: Subscription;
    defaultMsgPics: { [key: string]: string } = {
        'pdg': DefaultPics.PDG,
        'message-group': DefaultPics.MSGGROUP,  
        'broadcast':DefaultPics.BROADCAST     
    };
    constructor(
        private router: Router,
        private _http: HttpClient,
        public _structureService: StructureService,
        private staticDataService: StaticDataService,
        private _ChatService: ChatService,
        private chRef: ChangeDetectorRef,
        private _inboxService: InboxService,
        private datePipe: DatePipe,
        private modalFilter: userFilterPipe,
        private route: ActivatedRoute,
        private modals: Modal,
        private sanitizer: DomSanitizer,
        public _sharedService: SharedService,
        private _formBuild: FormBuilder,
        private _MaskedMessageService: MaskedMessageService,
        elementRef: ElementRef,
        renderer: Renderer,
        public _formsService: FormsService,
        public _commonVideoService: CommonVideoService,
        private ngxLogger: NGXLogger,
        public _GlobalDataShareService:GlobalDataShareService,
        private _ToolTipService: ToolTipService,
        private PdfViewService : PdfViewService,
        private httpService:HttpService,        
        private messageService: MessageService,
        private userService: UserService
    ) { 
        this.labelSite = this.staticDataService.getSiteLabelBasedOnMultiAdmission('SITE');
        this.isMultiAdmissionsEnabled = this._structureService.isMultiAdmissionsEnabled;      
        this.chatroomUsersListTitle = this._ToolTipService.getTranslateData('TITLES.CHATROOM_PARTICIPANTS');
        this.chatroomRolesListTitle = this._ToolTipService.getTranslateData('TITLES.CHATROOM_ROLES');;
        this.noImage = `${avatarUrlDefault}/profile-pic-patient.png`;
        this.messageType = MessageType;
        this.defaultPics = DefaultPics;
        this.messageCategory = MessageCategory;
        this.userGroup = UserGroup;
        this.chatAvatarOptions = ChatAvatarOptions;
        if(this._structureService.previousUrlNow != "/inbox") {
            localStorage.setItem('pageCountMessage', this.pageCountMessage);
            localStorage.setItem('messageInboxPagination', this.currentPage.toString());     
        }
        this.sendActive = false;                                                                                  
        this.activeMessage = JSON.parse(localStorage.getItem('activeMessage'));    
        document.addEventListener("serviceworker:notificationClick",function(e:CustomEvent) {
            document.getElementById('video-call-notify').style.display = 'none'
            setTimeout(() => {
                if (localStorage.getItem('enterdOninit') == 'false') {
                    router.navigateByUrl('/ChatroomComponent', { skipLocationChange: true }).then(() =>
                        router.navigate(["inbox/chatroom/status/"+e.detail.status]));
                }
            }, 2000);
        })
        this.currentChatroomId = localStorage.getItem('targetId');
        if(!this.reloadChatroomWithNewThreadSubs) {
            this.reloadChatroomWithNewThreadSubs = this._sharedService.reloadChatroomWithNewThread.subscribe(
                (data)=> {
                this._structureService.socket.emit("leaveChatRoom", this.currentChatroomId);
                localStorage.setItem("hideOnmaskedReplyGenerated", "false");
                this.disableReplySpan = false;
                this._inboxService.needVirtualPatient = false;
                this.sendActive = true;
                if (this.branchHourChangeTimer) {
                   clearTimeout(this.branchHourChangeTimer);
                }
                localStorage.setItem('enterdOninit','false');
                document.removeEventListener('chatPaste',() => {});
                this._sharedService.reInitiateMessagePolling.emit({type: 'message'});
                localStorage.setItem("searchInboxkeywordForPolling",'');
                this.ngOnInit();
                }
            );
        }

        router.events.subscribe(event => {
            if (event instanceof NavigationEnd) {
                if (event.url === '/blank') {
                    this.router.navigate(['/inbox/chatroom']);
                    this.sendActive = true;
                }
            }
        });
        this.applessMessaging = ((this.userData && this.userData.config && "enable_appless_messaging" in this.userData.config && this.userData.config.enable_appless_messaging == '1') ? true : false);
        this.enableVideoChatButton = this._sharedService.enableVideoChatButton.subscribe(
            (data) => {
                this.enableVideoButton = data;
                this.allowVideoChat = true;
                if(this._commonVideoService.autoRestart){
                    this.showVideoChat();
                }
            }
        );
        this.activeMessage = JSON.parse(localStorage.getItem('activeMessage'));        
        this.reloadSubscriber = this._sharedService.reloadOnConfigChange.subscribe(a => {
            this.currentChatroomId = localStorage.getItem('targetId');
            this.activeMessage = JSON.parse(localStorage.getItem('activeMessage'));
            this._sharedService.currentVideoChatRoomId.emit({chatRoomId:this.currentChatroomId,pageHeading:this.pageHeading});
            
            this.config = this._structureService.userDataConfig;           
            this.userDetails = this._structureService.userDetails;
            this._sharedService.currentVideoChatRoomId.emit({chatRoomId:this.currentChatroomId,pageHeading:this.pageHeading});
            this.configData = JSON.parse(this.config);
            this.userData = JSON.parse(this.userDetails);
            if(this.userData.organizationMasterId !=0 && this.userData.crossTenantsDetails && this.userData.crossTenantsDetails.length){
                this.crossTenantOptions = this.userData.crossTenantsDetails;
                this.crossTenantOptions.forEach((tenant)=> {
                    if(tenant.id == this._structureService.getCookie("crossTenantId")) {
                        this.crossTenantName = tenant.tenantName;
                    }
                });
            } else {
                this.crossTenantOptions = [];
            }
            this.resetPageAndLimit();
            this.showDoubleVerification = false;
            this.showDoubleVerificationStatus = 0;
            this.doNotReplyStatus = false;
            let currentText = $('.emojionearea-editor').html();
            setTimeout(() => {
                $('.emojionearea-editor').html(currentText);
            });
            this.ngOnInit();
            this.checkVideoChatVariables()
        })
        setTimeout(function () {
            $('body').tooltip({ selector: '[data-toggle="tooltip"]' });
        }, 100);
                                                                                                                                            
        renderer.listen(elementRef.nativeElement, 'click', (event) => {
            if(!this.searchInboxkeyword){
                $("#SearchTxt").val("");
            }
            let ngClick = $(event.target).attr('ng-click');
            if(ngClick !== "undefined"){
                let mimeType = $(event.target).attr('data-mediatype');
                if (typeof ngClick !== "undefined" && ngClick.indexOf('showPdf') !== -1 && mimeType == "pdf") {
                    this.showPdf(event);
                } else if (typeof ngClick !== "undefined" && (ngClick.indexOf('showPdfOrDocs') !== -1 || ngClick.indexOf('showCmisPdfOrDocs') !== -1)) {
                    this.showPdf(event);
                } else if (typeof ngClick !== "undefined" && (ngClick.indexOf('showImage') !== -1 || ngClick.indexOf('showCmisImage') !== -1)) {
                    this.showImage(event);
                } else if (typeof ngClick !== "undefined" && ngClick.indexOf('showVideo') !== -1) {
                    this.showVideo(event);
                }
            }
            if($("#tag-button").css("display")=="block"){
                var idDetails = ['associate-search-input','associate-li','associate-ul','associate-search'];
                if(!idDetails.includes(event.target.id) ){
                let clear =false;
                if(this.selectedAssosiatePatient){
                    $("input#associate-search-input").val(this.selectedAssosiatePatientName);
                }
                this.enableOrDisableUiLI(false,clear);
                }else {
        
                }
            }
            $(".show_read_users").hide();
            if(this.activeMessage.messageType!='1'){
                var height=0;
                $(".cat__apps__chat-block__content").each(function(value){
                    height = height+$(this).height();
                });
                $(".cat__apps__chat-block").css({height:height});
            }
            if(event.target.className.indexOf('tag-message-icon')>-1 || event.target.className.indexOf('tag_message_div')>-1){
                this.tagMultipleButtonClick(event,"");
                setTimeout(function () {
                    if($(".tooltip")[0]!=undefined){
                        $(".tooltip").tooltip("hide");
                    }
                }, 100);
            }
            if(event.path && event.path.length) {
                let hideFlagAccord = true;
                event.path.forEach((ele)=>{
                    if(ele.classList && Object.keys(ele.classList).some((key)=>ele.classList[key] == 'flag-tab')) {
                        hideFlagAccord = false    
                    }    
                });
                if(hideFlagAccord) {
                    $('.cat__apps__messaging__tab__time').removeClass('show');
                }
            }

        });
        renderer.listen(elementRef.nativeElement, 'keyup', (event) => {
            var code = (event.keyCode ? event.keyCode : event.which);
            if ($(event.target).hasClass('emojionearea-editor')) {
                this.userType(event);                
            }else if(this.isModalOpen && code==27){
                this.closeModal();
            }
        });
        
        if(this._structureService.inboxDataFirstPage && this._structureService.previousUrlNow != "/login")
        this.inboxData = this.restructureTags(JSON.parse(JSON.stringify(this._structureService.inboxDataFirstPage)));
        if(this._structureService.inboxData && this._structureService.previousUrlNow != "/inbox" && this._structureService.inboxDataCurrentPage !=1){
            this.inboxData = this.restructureTags(this._structureService.inboxData);
            this.currentPage = this._structureService.inboxDataCurrentPage;
        }
        
        if(!this.updateActiveMessageInChatroomSubscriber) {
            this.updateActiveMessageInChatroomSubscriber = this._sharedService.updateActiveMessageInChatroom.subscribe( (data)=> {
                if(data.updateTopic) {
                    var topic = data.title;
                    data = JSON.parse(localStorage.getItem('activeMessage'));
                    data.title = topic;
                    this.chatsubName = topic;
                }
                localStorage.setItem('activeMessage', JSON.stringify(data));
                this.activeMessage = data;
            });
        }
       
        if(!this.updateInboxDataSubscriber) {
            this.updateInboxDataSubscriber = this._sharedService.updateInboxData.subscribe(
                (data) => {
                    if(data.stopLoader) {
                       this.messageLoader.messages = false;
                        this.totalCount = parseInt(data.totalCount);
                        this._structureService.inboxTotalMessageCount = this.totalCount;
                        localStorage.setItem("inboxTotalMessageCount",JSON.stringify(this.totalCount));
                    } else {                        
                        var userId = this.userId;
                        var self =this;
                        if (userId && this.searchFlag) {
                        } else {
                            if(this._structureService.inboxDataCurrentPage == 1){
                            this.inboxData = this.restructureTags(data.inboxData);
                            if(data.inboxDataFirstPage) {
                                this.inboxDataFirstPage = data.inboxDataFirstPage;
                            }
                            if(data.inboxTotalMessageCount) {
                                this.totalCount = parseInt(data.inboxTotalMessageCount)
                            }
                            this.messageLoader.messages = false;
                            if(!data.disablescroll) {
                                this.inboxListScrollTop();
                            }
                        }
                        }
                    }
                }
            );
        }
            this.userCheckMessageRead = function(){ 
                var aLocationData = location.href.split('/');
                var iTargetChatId = aLocationData[aLocationData.length - 1];
                this._ChatService.getChatroomMessages(iTargetChatId, true, 0,0, "","all", 0, 0, []).then((data) => {
                    if(data){
                        var aInboxData = data.content;
                        var iRead       = 0;
                        for(var ikey in aInboxData){
                            if(aInboxData[ikey].messageType > 0){
                                iRead = 1;
                                break;
                            }
                        }
                        
                    }
                });
            }
          this.chatroomUsersInvite.subscribe((list) => {
            this._structureService.socket.emit("videoChatForInviteUser", {
                roomId: this.currentChatroomId,
                users: this.invitedUserOnVideoCall,
                chatroomUsersList: list,
                from: this.userData.displayName,
                fromId: this.userData.userId,
                heading: this.pageHeading,
                activeMessage: JSON.stringify(this.activeMessage)
            });
            let usersForPush = [];
            let pushNoti = false;
            this.invitedUserOnVideoCall.forEach((value, key) => {
                pushNoti = true;
                value.images = this._structureService.getApiBaseUrl()+'citus-health/avatars/profile-pic-clinician-nurse-default.png'
                var userindex = this.chatroomUsersList.findIndex(user => user.userId == value.userid);
                if(userindex != -1 && this.chatroomUsersList[userindex].avatar != ''){
                    var profilePic = this.chatroomUsersList[userindex].avatar;
                    value.images = this._structureService.getApiBaseUrl()+'citus-health/avatars/'+ profilePic;
                }
                value.viewTile = true;
                value.message = 'Connecting..';
                usersForPush.push({userid: this.chatroomUsersList[userindex].userId});
            })
            var inviteTileData = {
                users: this.invitedUserOnVideoCall,
                chatroomUsersList: list 
            }
            this._sharedService.inviteUserVideoTile.emit({ inviteTileData });
            if(pushNoti){
                let deeplinking = {
                    "state": "eventmenu.group-chat",
                    "stateParams": {
                        targetID: this.currentChatroomId,
                        targetName: "group-chat"
                    },
                    "activeMessage": {
                        sent: this.activeMessage.sent,
                        messageType: this.activeMessage.messageType||0,
                        baseId: this.activeMessage.baseChatroomId||0,
                        userid: this.userData.userId,
                        fromName: '"'+this.userData.displayName+'"',
                        message_group_id: this.activeMessage.message_group_id||0,
                        createdby: this.activeMessage.createdby||0,
                        chatWithHeading: ''
                    }
                };
                if(this.activeMessage.message_group_id && this.activeMessage.groupName) {
                    deeplinking.activeMessage.chatWithHeading = this.activeMessage.groupName;
                }
                const videoCallNotificationData = {
                    sourceId: CONSTANTS.notificationSource.message,
                    sourceCategoryId: CONSTANTS.notificationSourceCategory.videoCallNotification
                };
                this._structureService.sentPushNotification(usersForPush, 0, 'Incoming video call from {{tenantName}}. Tap to open the App and Join Call.', undefined, deeplinking, '', '', undefined, undefined, undefined, undefined, videoCallNotificationData);
            }
        });
        

        this._sharedService.forwardUserVideoCall.subscribe((frwdUser) => {
            this.invitedUserOnVideoCall = [{name:frwdUser.name,userid:frwdUser.userid}];
            this.frwdChatroomId = frwdUser.roomId;
            this.currentChatroomId = frwdUser.roomId;
            this.ChatRoomUsers(false, true, true); 
            
        });
        
        this.forwardUserList.subscribe((data) => {
            var list = data['userList'];
            this._sharedService.videoUserListUpdate.emit({list});
            this._structureService.socket.emit("updateUserListFromInbox", {
                users:data['userList'],
                roomId:data['roomId']
            }); 
        });
        this._sharedService.listUpdateOnKicked.subscribe((data) => {
            this.ChatRoomUsers();
        });
        this.sendActive = true;
        this._sharedService.setDoubleVarification.subscribe((data) => {
            this.showDoubleVerification = true;
            this.showDoubleVerificationStatus = 0;
        });
        //Checking the browser is video support or not
        var nav = <any>navigator;
        var isWebRTCAvailable = (nav.getUserMedia  || (nav.mediaDevices ? nav.mediaDevices.getUserMedia : undefined)) ? true : false;
        if(!isWebRTCAvailable){
          this.browserVideoSupport=false;
        }
        //End of Checking the browser is video support or not
    }
    getInviteSiteIds(data: any, page : string) {
        this.selecteGroupOrPdgTab = false;
        if((this.isInviteSiteSelected && this.fromModelPage == 'Invite') || (!this.isInviteSiteSelected && !this.isEnterToFrwrdSiteFun)) {
            this.selectSiteId = data['siteId'].toString();
            this.pdgSiteIds = this.selectSiteId;
            this.loadSiteFilter = true;
            this.initGroups();
            this.getData(this.fromModelPage);
        }
        this.isInviteSiteSelected = true;
    }
    getForwardSiteIds(data: any, page : string) {
        this.isEnterToFrwrdSiteFun = true;
        if(this.isForwardSiteSelected && this.fromModelPage == 'Forward') {
            this.selectSiteId = data['siteId'].toString();
            this.getData(this.fromModelPage);
        }
        this.isForwardSiteSelected = true;
    }
    getData(page) {
        if (this.tabSelectedData == 'partnerroles' || this.tabSelectedData == 'staffroles') {
            if (this.staffUnderRole.length > 0) {
                this.staffUnderRole.forEach((element) => {
                    if ($("ul.sub-role-" + element).hasClass("accordion-show")) {
                        this.accordianActionForStaffRoles(element);
                    }
                    $("ul.sub-role-" + element + " li").remove();
                });
                this.staffUnderRole = [];
                this.staffRolesList = this._sharedService.tempStaffRoleList;
            }
            this.chatroomPatientSiteId = this.selectSiteId;
            this.inviteSiteId = this.selectSiteId;
            if(!this.multiSiteEnable)
            this.getRolesToInviteChat(0, this.tabSelectedData);
        } else if (this.tabSelectedData === 'msggroups') { 
            if (this.expandedRole.length > 0) {
                this.expandedRole.forEach((element) => {
                    if ($(".expand-icon-" + element).hasClass("fa-minus")) {
                        $(".expand-icon-" + element).addClass("fa-plus");
                        $(".expand-icon-" + element).removeClass("fa-minus");
                        $(".sub-item-panel-" + element).removeClass("showall");
                    $(".sub-item-panel-" + element + " li").remove();
                    }
                });
                this.expandedRole = [];
            }
                this.chatroomPatientSiteId = this.selectSiteId;
                this.memberDataGroupWise = [];
                this.allMessageGroups = [];
                this.messageGroup = [];
                this.getAllMessageGroupDetails(this.userData.tenantId, this.userData.userId,this.searchValue,this.ispdgs);
        }
        else {
            if(page === 'Invite') {
                this.chatroomPatientSiteId = this.selectSiteId;
                this.inviteSiteId = this.selectSiteId;
                this.getUsersOnInviteToChat('', this.searchValue, 0, this.tabSelectedData);
            } else if (page === 'Forward') {
                this.chatroomPatientSiteId = this.selectSiteId;
                this.forwardSiteId = this.selectSiteId;
                this.searchRerouteForward(this.tabSelectedData);
            }
        }
    }
    hideDropdownAss(hideItem : any){
        this.hideSiteSelection = hideItem.hideItem;
      }
    hideDropdown(hideItem: any,value:string) {
        if(value == 'Associate Patient') {
            this.hideAssociateSiteSelection = hideItem.hideItem;
        }else if(value == 'Forward') {
            this.hideForwardSiteSelection = hideItem.hideItem;
        }else if(value == 'Invite') {
            this.hideInviteSiteSelection = hideItem.hideItem;
        }
    }
    @ViewChild(SignPadComponent) childpad: SignPadComponent;
    currentChatroomId = localStorage.getItem('targetId');
    activeMessage = JSON.parse(localStorage.getItem('activeMessage'));
    pageHeading = localStorage.getItem('chatWithHeading');
    config = this._structureService.userDataConfig;
    userDetails = this._structureService.userDetails;
    configData = JSON.parse(this.config);
    userData = JSON.parse(this.userDetails);
    listType = ChatroomUserListType;
    ngOnDestroy() {
        this._structureService.socket.emit("leaveChatRoom", this.currentChatroomId);
        this._structureService.joinChatroomId = 0;
        localStorage.setItem("hideOnmaskedReplyGenerated", "false");
        this._inboxService.needVirtualPatient = false;
        if (this.branchHourChangeTimer) {
            clearTimeout(this.branchHourChangeTimer);
        }
        this.reloadSubscriber.unsubscribe();
        this.enableVideoChatButton.unsubscribe();	
        localStorage.setItem('enterdOninit','false')
        if(this.updateInboxDataSubscriber) {
            this.updateInboxDataSubscriber.unsubscribe();
        }
        if(this.messageGroupSubscriber) {
            this.messageGroupSubscriber.unsubscribe();
        }
        document.removeEventListener('chatPaste',() => {});

        this._sharedService.reInitiateMessagePolling.emit({type: 'message'});
        localStorage.setItem("searchInboxkeywordForPolling",'');
        if(this.reloadChatroomWithNewThreadSubs) {
            this.reloadChatroomWithNewThreadSubs.unsubscribe();
        }
        if(!this._structureService.hideAdvanceSearchArea) this._structureService.hideAdvanceSearchArea = !this._structureService.hideAdvanceSearchArea;
        /**Unsubscribe all the socket event subscriptions */
        this.socketEventSubscriptions.forEach(subscription => {
            if(subscription) subscription.unsubscribe();
        });
        this.videoCallEndByInitiator.unsubscribe();
    }
    closeModal() {
        this.modals.hide();
        this.isModalOpen = false;
        this.modalBody = "";
        this.pdfOrDocSrc = "";
        this.pdfOrDocUrl = "";
        this.showIframe = false;
        this.showVideoFrame = false;
        this.cancelLabel = false;
    }
    closeInviteModal() {
        var elements = $(".fa-minus[class*=expand-icon-]");
        for (var i = 0; i < elements.length; i++) {
            if(elements[i].parentNode.attributes['class']){
            var roleID = elements[i].parentNode.attributes['class'].value.split('-')[1];
            }else{
                var roleID = elements[i].parentNode.parentNode.attributes['class'].value.split('-')[1];
            }
            if ($(".expand-icon-" + roleID).hasClass("fa-plus")) {
                $(".expand-icon-" + roleID).removeClass("fa-plus");
                $(".expand-icon-" + roleID).addClass("fa-minus");
                $(".sub-item-panel-" + roleID).addClass("showall");
            } else {
                $(".expand-icon-" + roleID).removeClass("fa-minus");
                $(".expand-icon-" + roleID).addClass("fa-plus");
                $(".sub-item-panel-" + roleID).removeClass("showall");
            }
        }
        if ($('#inviteModal').hasClass('show')) {
            $('#inviteModal').modal('hide');
        }
        this.inviteSiteId = '';
        this.clearSelectedRoles(this.optionShow);
        this.loadSiteFilter = false;
        this.showSitefilter = false;
        $('#chat-with-modal-search-box-on-invite').val('');
    }
    showImage(targetEvent) {
        this.documentType = '';
        var elName = $(targetEvent.target).attr('src');
        if (!elName) {
            elName = $(targetEvent.target).attr('data-viewsrc');
        }
        elName = elName.split("?")[0] + "?type='image'";
        if (elName.indexOf('thumb_180x116/') > -1)
            elName = elName.replace("thumb_180x116/", "");
        this.isModalOpen = true;
        this.cancelLabel = true;
        this.modalBody = '<div class="chat-image-viewer"><img src="' + elName + '" class="imagepreview modal-img-viewer"></div>';
        this.modals.show();
    }
    showPdfOrDocs(targetEvent) {
        this.pdfOrDocSrc = "";
        this.pdfOrDocUrl = $(targetEvent.target).attr('data-src');
        if (!this.pdfOrDocUrl) {
            this.pdfOrDocUrl = $(targetEvent.target).attr('data-viewsrc');
        }
        this.pdfOrDocSrc = "https://docs.google.com/gview?url=" + this.pdfOrDocUrl + "&efh=false&chrome=false&embedded=true#toolbar=0&navpanes=0&scrollbar=0";
        this.isModalOpen = true;
        this.cancelLabel = true;
        this.showIframe = true;
        this.modalBody = this.sanitizer.bypassSecurityTrustResourceUrl(this.pdfOrDocSrc);
        this.modals.show();
    }
    showPdf(targetEvent) {
        this.documentType = targetEvent.target.attributes.title.textContent;
        this.pdfOrDoc = $(targetEvent.target).attr('data-src') || $(targetEvent.target).attr('data-viewsrc');
        let originalpdfOrDoc = this.pdfOrDoc.replace(/\?[^'"]*$/, '');
        this.isModalOpen = true;
        this.cancelLabel = true;
        this.showIframe = true;
    
        this.PdfViewService.showPdfViewer(originalpdfOrDoc).then((modalBody) => {
            this.modalBody = modalBody;
            this.modals.show();
        });
    }
    showVideo(targetEvent) {
        let videoUrl = $(targetEvent.target).attr('data-src');
        if (!videoUrl) {
            videoUrl = $(targetEvent.target).attr('data-viewsrc');
        }
        var self = this;
        if (videoUrl.includes(this.userData.cmisFileBaseUrl)) {
            this._http.get(videoUrl, { responseType: 'arraybuffer' })
                .toPromise()
                .then(
                    response => {
                        let video = new Blob([new Uint8Array(response)], { type: $(targetEvent.target).attr('data-mediatype') });
                        self.modalBody = self.sanitizer.bypassSecurityTrustResourceUrl(window.URL.createObjectURL(video));
                    }
                );
        } else {
            this.modalBody = this.sanitizer.bypassSecurityTrustResourceUrl(videoUrl);
        }
        this.isModalOpen = true;
        this.cancelLabel = true;
        this.showVideoFrame = true;
        this.modals.show();
    }
    timeConvert(time) {
        time = time.toString().match(/^([01]\d|2[0-3])(:)([0-5]\d)(:[0-5]\d)?$/) || [time];
        if (time.length > 1) {
            time = time.slice(1);
            time[5] = +time[0] < 12 ? 'a' : 'p';
            time[0] = +time[0] % 12 || 12;
        }
        return time.join('');
    }

    checkPushNotification(){
        localStorage.setItem('enterdOninit','true')
        if (this._sharedService.pushNotification == true) {
            var status = this.route.snapshot.params['id']
            if(status){
                if (status.includes('accepted')) {
                    this._sharedService.acceptPushNotify.emit('accepted')
                }
                else if (status.includes('rejected')) {
                    this._sharedService.acceptPushNotify.emit('rejected')
                }
            }
        }
    }
    initializeVideoChat(){
        if (!this._commonVideoService.checkVideoPluginLoaded()) {
            var options={
                userData:this.userData,
                maxParticipants:0,
                autostart:true
            }
            this._commonVideoService.init('vidyo', options);
        }
        else {
            this.enableVideoButton = (this._sharedService.videoCallReceived) ? false : true;
        }
    }
    ngOnInit() {
        this.hasChatPermission();
        $(document).ready(function () {
            var window_top = $(window).scrollTop();
            function sticktothetop() {
                var window_top = $(window).scrollTop();         
                if (window_top == 0) {
                    $('#stickThis').removeClass('stick');            
                } else {
                    $('#stickThis').addClass('stick');                   
                }
              }
              $(function() {
                $(window).scroll(sticktothetop);           
              });
        });
        this.inviteEnableButton = false;
        this.pdgAssosiatedPatientArray = [];
        this.patientTagInfo = {};
        this.patientInfo = [];
        this.multiSiteEnable = this.userData.config.enable_multisite == 1 ? true : false;
        this.enableCrossSite= this.userData.enable_cross_site == 1 ?true :false;
        this.hideOnmaskedReplyGenerated = Boolean(localStorage.getItem('hideOnmaskedReplyGenerated')) ? true : false;
        var searchInboxkeyword ='';
        searchInboxkeyword  = localStorage.getItem("searchInboxkeywordInbox");
        this.patientSiteId = [];
        this.tempChatroomPatientSiteId = '0';
        this.chatroomPatientSiteId = '0';
        this.chatroomSiteIds = '0';
        this.isSiteFilterDisabled = false;
        if($("#tag-button").is(':visible')) {
            this.closeTagModal();
        }
        if(this.selectedFiles.length) {
            this.withAttachment = false;
            this.selectedFiles = [];
            $(".emojionearea-editor").attr("placeholder", "Type here!");
            this.loadAttachment = false;
        }
        if(!isBlank(searchInboxkeyword)){
            $(document).ready(()=>{
            $('.chatroom-card-cnt .chatroom-section .searchbar input#SearchTxt').val(this.searchInboxkeyword);
            });
            this.searchInboxkeyword=searchInboxkeyword;
        }
        localStorage.setItem("searchInboxkeywordForPolling",searchInboxkeyword);
        this.totalCount = localStorage.getItem('inboxTotalMessageCount');
        if(this.totalCount) {
            this.totalCount = localStorage.getItem('inboxTotalMessageCount').replace(/"/g,'');
            this.totalCount = parseInt(this.totalCount);
        }
        this.userDataConfig = JSON.parse(this._structureService.userDetails);
        this.enableChatWindowSideBar = this.userDataConfig.config.enable_chat_window_sidebar;
		this.checkPushNotification();
        this.currentChatroomId = localStorage.getItem('targetId');
        this.activeMessage = JSON.parse(localStorage.getItem('activeMessage'));
        if(!("baseChatroomId" in this.activeMessage))
        this.activeMessage.baseChatroomId = 0;
        this.selectedRow = +this.activeMessage.baseChatroomId || +this.activeMessage.baseId || +this.activeMessage.chatroomId || this.activeMessage.chatroomid;
        this.config = this._structureService.userDataConfig;
        this.userDetails = this._structureService.userDetails;
        this.configData = JSON.parse(this.config);
        if(location.href.indexOf("/status") != -1){ 
            this.userCheckMessageRead();
        }
        if(this.configData.default_category_of_message_display!=''&&this.configData.default_category_of_message_display!='undefined')
        {
            this.messageFilter=this.configData.default_category_of_message_display;
        }
        if(this._structureService.inboxData && this._structureService.previousUrlNow == "/inbox" && this._structureService.inboxDataCurrentPage !=1){
            this.inboxData = this.restructureTags(this._structureService.inboxData);
                this.currentPage = this._structureService.inboxDataCurrentPage;
        }
        this.userData = JSON.parse(this.userDetails);
        this.isUserPatient = this.userData.group == UserGroup.PATIENT;
        this.isUserStaff = this.userData.group != UserGroup.PATIENT;
        this.isUserPartner = this.userData.group == UserGroup.PARTNER;
        this.filterMessage=0;  
        this.searchMessage =0;
        this.column6 = false;
        if(this.chatEnterToSend=='true'){            
            this.chatEnterCheckboxValue = true;
        }else{
            this.chatEnterCheckboxValue = false;
        }
        if(this.userData.organizationMasterId != 0 && this.userData.crossTenantsDetails && this.userData.crossTenantsDetails.length){
            this.column6 = false;
            if(
                this.userData.crossTenantsDetails.length > 1 
                && this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication') !== -1 
                && this.configData.allow_multiple_organization == 1
                && (this.configData.enable_nursing_agencies_visibility_restrictions != 1 || (this.configData.enable_nursing_agencies_visibility_restrictions == 1 && this.userData.nursing_agencies == ""))
            ){
                this.column3 = true;
                this.crossTenantOptions = this.userData.crossTenantsDetails;
                this.crossTenantOptions.forEach((tenant)=> {
                    if(tenant.id == this._structureService.getCookie("crossTenantId")) {
                        this.crossTenantName = tenant.tenantName;
                    }
                });
                this._structureService.getOtherTenantStaffUsers().then((data) => {
                    if (data['getSessionTenant']) {
                        this.otherTenantStaffList = data['getSessionTenant'].otherTenantStaffUsers;
                        this.setOtherTenantClinicianDataRoleWise();
                    } else {
                        this._structureService.deleteCookie('authenticationToken');
                        this.router.navigate(['/login']);
                    }
                });
            }else{
                this.column3 = false;
                this.crossTenantOptions = [];
            }           
            
        } else {
            this.column3 = false;
            this.crossTenantOptions = [];
            if(this.privileges.indexOf('invitePatientsToChatroom')==-1){
                this.column6 = true;
            }
        }
        
        this._sharedService.sessionRefresh.subscribe(
            (userData) => {
                this.privileges = userData.privileges;
                this.hasChatPermission();
            }
        );
        var page = "chat-rooms";        
        $(".show-filter").tooltip({ html:true,title: this._ToolTipService.getToolTip(page, 'CHATROOM000001'),placement:'bottom' });
        
        $('#messageTags').on('change', (e) => {  
            if (e.target.value !== '') {
                this.tagNotSelected = false;
            } 
          });
        $('#users').on('change', (e) => {  
                    if (e.target.value !== '') {
                        this.userNotSelected = false;
                    } 
                });
          
        this.socketEventSubscriptions.push(
            this._structureService.subscribeSocketEvent('onGoingVideoCall').subscribe((data) => {
                this.joinChat = true;
                this._sharedService.onGoingChatrooom = data.roomId
                if(this._sharedService.onGoingChatrooom == ''){
                    this.setLocalStorageParticipants('true');
                }
                this.checkVideoChat()
            })
        );
        
        /**
         * Message Tag functionality
         */
           
        this.existingTagstenantId  =this.userData.tenantId;
        if(this.privileges.indexOf('TagMessage')!==-1  && this.configData.show_form_features && this.configData.show_form_features!=='0') {
            this._inboxService.getTenantClinicians(this.userData.tenantId,true).then((tenantUsers) => {
                this.formApproverUsersListSelection = tenantUsers;
            });
        }
        var self = this;
        $('#users').select2({
            placeholder: "Select a patient"
        });
        var $eventSelect = $("#messageTags");
        $eventSelect.select2({
            placeholder: "Select one or more tags"
        });
        var self = this;
        var checkPatientFacig = [];
        $eventSelect.on("change", function (e) {
            checkPatientFacig = [];
            var messageTag = $("#messageTags").val();
            if (messageTag.length) {
                messageTag.forEach(element => {
                    var member = { id: "" };
                    var id = element.substr(element.indexOf(":") + 1);
                    id = id.replace(/'/g, "");
                    member.id = id.replace(/\s/g, '');
                    var tagId = Number(member.id);
                    if (tagId) {
                        self.availableTags.map(function (tags) {
                            if (tags.id == tagId && tags.meta) {
                                let meta = JSON.parse(tags.meta);
                                if (meta.patientFacing == true) {
                                    checkPatientFacig.push(1);
                                } else {
                                    checkPatientFacig.push(0);
                                }
                            }
                        });
                    }
                });
                if (checkPatientFacig.indexOf(1) != -1) {
                    self.patientFace = true;
                } else {
                    self.patientFace = false;
                }
            } else {
                self.patientFace = false;
            }
        });
        this.messageTagForm = this._formBuild.group({
            users: [''],
            messageTags: ['']
        });

        var self = this;
        this.avatarBasePath = this._structureService.getApiBaseUrl() + "citus-health/avatars/";
        $(() => {
            autosize($('.textarea'));
            $('#enterKeyCheck').popover();
            $('[data-toggle="tooltip"]').tooltip();
            if ($(".textarea").hasClass( "emojionearea" ) && typeof($("div.emojionearea").emojionearea) != 'undefined'){               
                $("div.emojionearea").emojionearea.data('emojioneArea').trigger('change');                
            } else {
                if($(".emojionearea").length === 0) { 
                    $(".textarea").emojioneArea({
                        pickerPosition: "top",
                        filtersPosition: "bottom",
                        tones: false,
                        tonesStyle: "bullet",
                        autocomplete: false,
                        hidePickerOnBlur: false,
                        attributes: {
                          id   : "compose_msg"
                        },
                        events: {
                            keydown: (editor, e) => {
                                if ($('#mention-users-dropdown').css('display') !== 'none') {
                                    if ((e.keyCode === 9 || e.keyCode === 13)) {
                                        this.stopEvent(e);
                                        const user = this.filteredMentionUsers.find((user) => user.active);
                                        this.selectMentionUser(user, e);
                                        return false;
                                    }
                                    if (e.keyCode === 38) {
                                        this.stopEvent(e);
                                        this.activatePreviousItem();
                                        return false;
                                    } else if (e.keyCode === 40) {
                                        this.stopEvent(e);
                                        this.activateNextItem();
                                        return false;
                                    }
                                }
                            },
                            keyup: (editor, e) => {
                                const selection: any = window.getSelection();
                                if (this.notPatient && selection.rangeCount) {
                                    const range = selection.getRangeAt(0);
                                    const lastLine = range.startContainer.nodeValue;
                                    const tempFilterData = JSON.stringify(this.filteredMentionUsers);
                                    const coords = getCaretCoordinates(editor[0], editor[0].selectionStart, null);
                                    if (lastLine && (lastLine.startsWith('@') || lastLine.includes('@'))) {
                                        const first = lastLine.slice(0, range.startOffset);
                                        const lastIndex = first.lastIndexOf('@');
                                        const searchString = first.substring(lastIndex + 1, first.length);
                                        if (lastIndex === 0 || [CharCodes.SPACE, CharCodes.NBSP, CharCodes.NO_BREAK].includes(first.charCodeAt(lastIndex - 1))) {
                                            $('#mention-users-dropdown').show();
                                            const top = (coords.top + coords.height) > $('.emojionearea-editor').height() ? $('.emojionearea-editor').height() : (coords.top + coords.height);
                                            $('.mention-users-dropdown-container').css('left', coords.left - (0.12 * coords.left)).css('top', top).prop('x-custom-offset', selection.baseOffset + 1);
                                            const mentionUsers = this.chatroomUsersList.filter((user) => user.userId !== this.userData.userId);
                                            if (searchString === '') {
                                                this.filteredMentionUsers = mentionUsers;
                                            } else {
                                                this.filteredMentionUsers = mentionUsers.filter((user) => user.displayName && user.displayName.toLowerCase().includes(searchString.toLowerCase()))
                                            }
                                            if (this.filteredMentionUsers.length === 0) $('#mention-users-dropdown').hide();
                                            if (tempFilterData !== JSON.stringify(this.filteredMentionUsers)) {
                                                this.filteredMentionUsers.forEach((user, index) => {
                                                    if (index === 0) user.active = true;
                                                    else user.active = false;
                                                });
                                                document.getElementById('mention-users-dropdown').scrollTop = 0
                                            }
                                        } else {
                                            $('#mention-users-dropdown').hide();
                                        }
                                    } else {
                                        $('#mention-users-dropdown').hide();
                                    }
                                    // Hide mention users dropdown on click outside
                                    $(document).click((event) => { 
                                        const $target = $(event.target);
                                        if(!$target.closest('#mention-users-dropdown').length && 
                                        $('#mention-users-dropdown').is(":visible")) {
                                          $('#mention-users-dropdown').hide();
                                        }        
                                    });
                                }
                             }
                         }
                    });
                    setTimeout(() => {
                          $('#mention-users-dropdown').on('click', 'input', (e) => {
                            this.stopEvent(e);
                            const userId = e.target.getAttribute('data-target');
                            const user = this.filteredMentionUsers.find((user) => user.userId === userId);
                            if (user) this.selectMentionUser(user, e);
                          });
                    }, 500);
                }
            }
            $('#compose_msg').html(" ");
            // Prevent drag and drop within the #compose_msg element
            $(document).on('dragstart dragover drop', '#compose_msg', (event) => {
                event.preventDefault();
            });            
        });
        document.addEventListener('chatPaste', (eventData: CustomEvent) => {
            var items = eventData.detail.items;
            for (var index in items) {
                var item = items[index];
                var event={
                    target:{
                        files:[]
                    }
                };
                if (item.kind === 'file') {
                    var blob = item.getAsFile();
                    var dateTime= String(new Date().getTime());
                    dateTime=dateTime+".png";
                    var file=new File([blob], dateTime,{type: "image/png"});
                    event.target.files.push(file);
                    this.uploadFileConfirm(event);
                }
            }
        });
        self.get_all_tenants();
        $(document).ready(()=>{
            
            $('body').on('change','#chatWithTenantFilter-invite',(event)=>{
                    if(event.target.value){
                        self.changeTenant(event.target.value);
                        
                    }
            });
            $('#exampleModal').on('hidden.bs.modal', ()=> {
                this.chatWithModalShown = false;
                this.loadMoreSearchValue = undefined;
                this.chatwithPageCount = {
                    staffs: 0,
                    patients: 0,
                    partner:0
                }
                this.noMoreItemsAvailable = {
                    users: false
                };
                this.userListChatwith = [];
                this.clinicalUserDetails = [];
                this.usersList = [];
                this.userList = [];
            });
            $('#exampleModal').on('show.bs.modal', ()=> {
                this.chatWithModalShown = true;
                this.loadMoreSearchValue = undefined;
            });
        });

        this.timeoutFunction = function () {
            self.typing = false;
            self._structureService.socket.emit("typing", false);
        }
        this.userType = function (e) {
            var code = (e.keyCode ? e.keyCode : e.which);
            if (code !== 13) {
                if (this.typing === false || $('.emojionearea-editor').val() != "") {
                    this.typing = true;
                    this._structureService.socket.emit('typing', 'typing...');
                    clearTimeout(this.timeout);
                    this.timeout = setTimeout(this.timeoutFunction, 2000);
                } else {
                    if(this.typing){
                        clearTimeout(this.timeout);
                        this.timeout = setTimeout(this.timeoutFunction, 2000);
                    }
                }
            }else if (code  == 13) { //Enter keycode
                if ($("#enterKeyCheck").prop('checked') == true) {
                    if(!e.shiftKey) {
                        e.preventDefault();
                        clearTimeout(this.timeout);
                        this.timeoutFunction();
                        this.sendData(); 
                    }
                }else if(e.ctrlKey){
                    e.preventDefault();
                    clearTimeout(this.timeout);
                    this.timeoutFunction();
                    this.sendData();
                }
            }

        }

        /** For Patient Login User Selection Condition -- Need To Generalize */
        this.scheduledPrimaryCar = 0;
        this.defaultNurses = [];
        localStorage.setItem("defaultNurses", JSON.stringify(this.defaultNurses));
        if (this.userData.group == 3) {
            if (this._inboxService.checkInfusionHours(false).isWorkingHours) {
                this.usersList = "";
                if (this.configData.clinician_roles_on_working_hours) {
                    this._inboxService.getUsersListByRoleId(this.configData.clinician_roles_on_working_hours, 0, true).then((users) => {
                        this.usersList = users;
                        this.userListChatwith = this.modalFilter.transform(this.usersList, 'staff');
                        this.setClinicianDataRoleWise();
                        this.clinicianLoad = true;
                    }).catch((ex) => {
                    });
                } else {
                    this.clinicianLoad = true;
                }
                if (this.configData.default_clinician_roles_available_on_working_hour) {
                    this._inboxService.getUsersListByRoleId(this.configData.default_clinician_roles_available_on_working_hour, 0, true).then((result) => {
                        this.defaultNurses = result;
                        localStorage.setItem("defaultNurses", JSON.stringify(this.defaultNurses));
                    }).catch((ex) => {
                    });

                }
            } else {
                this.usersListByRoleId = "";
                if (this.configData.clinician_roles_beyond_working_hours) {
                    this._inboxService.getUsersListByRoleId(this.configData.clinician_roles_beyond_working_hours, 0, true).then((users) => {
                        this.usersListByRoleId = users;
                        localStorage.setItem("usersListByRoleId", JSON.stringify(this.usersListByRoleId)); 
                        this.clinicianLoad = true;
                    }).catch((ex) => {
                    });
                } else {
                    this.clinicianLoad = true;
                }
                if (this.configData.default_clinician_roles_available) {
                    this._inboxService.getUsersListByRoleId(this.configData.default_clinician_roles_available, 0, true).then((result) => {
                        this.defaultNurses = result;
                        localStorage.setItem("defaultNurses", JSON.stringify(this.defaultNurses));
                    }).catch((ex) => {
                    });
                }
            }
        }
        /** End Section */
        this.userId = this._structureService.getCookie('userId');
        this.iconPath = this._structureService.iconPath;
        if(!this._structureService.inboxDataFirstPage || !this._structureService.previousUrlNow) {
        } else {
            var userId = this.userId;
            var self =this;
            if(!this._sharedService.reloadInbox) {
                if (userId && this.searchFlag) {
                } else {
                    if(this._structureService.inboxData && this._structureService.previousUrlNow == "/inbox" && this._structureService.inboxDataCurrentPage !=1){
                        this.inboxData = this.restructureTags(this._structureService.inboxData);
                        this.currentPage = this._structureService.inboxDataCurrentPage;
                    }else{
                        this.inboxData = this.restructureTags(JSON.parse(JSON.stringify(this._structureService.inboxDataFirstPage)));
                        this.currentPage = this._structureService.inboxDataCurrentPage = 1;

                    }
                    this._structureService.inboxMessageResultsCount = this.inboxData.length;
                    this.inboxListScrollTop();
                }
                this.messageLoader.messages = false;
            } else {
            	this._sharedService.reloadInbox = false;
                this.activate();
            }
        }
        if (this.activeMessage && +this.activeMessage.messageType !== MessageType.BROADCAST) {
            this.callChatRoomUsers = true;
        }
        var authtoken='';
        if(this._structureService.getCookie('authenticationToken')){
            authtoken=this._structureService.getCookie('authenticationToken');
        }
        this._structureService.fetchChatRoomMessages.subscribe((chatroomId)=>{
            this.currentChatroomId = chatroomId.toString();
            this.resetChatMessageSearch();
        });
        if(this._structureService.joinChatroomId !== +this.currentChatroomId && this._structureService.joinToAppDone) {
            this._structureService.socket.emit("joinToChatroom", {user: this.userData.userId, room: this.currentChatroomId.toString(), name: this.userData.displayName,tenantId: this.userData.tenantId, tenantKey:this.userData.tenantKey,tenantName:this.userData.tenantName,sites:this.userData.mySites, avatar: this.userData.profileImageThumbUrl, citusRole: this.userData.group, patientReminderCheckingType: this.userData.config.patient_reminder_checking_type, eventSource: JSON.stringify({ environment: 'Desktop', component: CONSTANTS.joinChatroomEventSourceComponent.chatPageInitialize}) }, (data) => { 
                if(data === 'true') {
                    this._structureService.joinChatroomId = +this.currentChatroomId;
                }
            });
        }
        this.socketEventSubscriptions.push(
            this._structureService.subscribeSocketEvent('lastUserActivity').subscribe((lastUserActivity) => {
                this.lastUserActivityTime = lastUserActivity;
            })
        );
        this.socketEventSubscriptions.push(
            this._structureService.subscribeSocketEvent('signatureFromUser').subscribe((data) => {
                if (data) {
                    for (var i = 0; i < this.newMessagedetails.length; i++) {
                        if (this.newMessagedetails[i].id == data.id) {
                            this.newMessagedetails[i].sign = data.sign;
                        }
                    }
                }
            })
        );
        this.socketEventSubscriptions.push(
            this._structureService.subscribeSocketEvent('tagApproveAdmin').subscribe((data) => {
                for (var i = 0; i < this.newMessagedetails.length; i++) {
                    if (this.newMessagedetails[i].id == data.id) {
                        this.newMessagedetails[i].tag = true;
                        this.newMessagedetails[i].tagSign = true;
                    }
                }
            })
        );
        this.socketEventSubscriptions.push(
            this._structureService.subscribeSocketEvent('removesignatureFromUser').subscribe((data) => {
                if (data) {
                    for (var i = 0; i < this.newMessagedetails.length; i++) {
                        if (this.newMessagedetails[i].id == data.id) {
                            this.newMessagedetails[i].sign = false;
                        }
                    }
                }
            })
        );
        /*****************Message reply timeout checkings*****************/

        this.userMessagePolling = false;
        this.messageReplyTimeout = this.userData.config.message_reply_timeout;
        this.tDiff = this.activeMessage.messageOrder ? moment().diff(moment(this.activeMessage.messageOrder * 1000), 'minutes') : 0;
        if (this.userData.siteConfigs.working_hour == '1' && this.userData.config.enable_double_verification == '1' && this.userData.group == '3' && (!this.activeMessage.messageType || this.activeMessage.messageType == 0)) {
            var branch_start_hour, branch_start_minutes, branch_end_hour, branch_end_minutes, current_time, current_time_hour, current_time_minutes, current_time_seconds;
            current_time = moment().format('HH:mm:ss');
            current_time_hour = current_time.split(':')[0];
            current_time_minutes = current_time.split(':')[1];
            current_time_seconds = current_time.split(':')[2];
            if (this.userData.siteConfigs.branch_start_time.indexOf(':')) {
                branch_start_hour = this.userData.siteConfigs.branch_start_time.split(':')[0];
                branch_start_minutes = this.userData.siteConfigs.branch_start_time.split(':')[1];
            }
            if (this.userData.siteConfigs.branch_end_time.indexOf(':')) {
                branch_end_hour = this.userData.siteConfigs.branch_end_time.split(':')[0];
                branch_end_minutes = this.userData.siteConfigs.branch_end_time.split(':')[1];
            }
            var branchHourChangeTimeInSec = 0;
            var self = this;
            if (this._inboxService.checkInfusionHours(false).isWorkingHours) {
                branchHourChangeTimeInSec = moment(moment().format('YYYY-MM-DD') + ' ' + this.userData.siteConfigs.branch_end_time).diff(moment().hour(current_time_hour).minute(current_time_minutes).seconds(current_time_seconds), 'seconds');
                this.branchHourChangeTimer = setTimeout(() => {
                        if(("restrict_to_branch_hour" in self.userData.config && self.userData.config.restrict_to_branch_hour == 0) && this.tDiff >= (this.messageReplyTimeout*60) && self.userData.group == '3'){
                            self.doNotReplyStatus = true;
                            self.showDoubleVerification = false;
                            self.showDoubleVerificationStatus = 0;
                        } else {
                            if("restrict_to_branch_hour" in self.userData.config && self.userData.config.restrict_to_branch_hour == 1 && self.userData.group == '3') {
                                self.doNotReplyStatus = true;
                                self.showDoubleVerification = false;
                                self.showDoubleVerificationStatus = 0;
                            } else {
                                self.doNotReplyStatus = false;
                                if (!this._inboxService.checkInfusionHours(false).isWorkingHours && this.userData.siteConfigs.working_hour == '1' && this.userData.config.enable_double_verification == '1' && this.userData.group == '3' && (!this.activeMessage.messageType || this.activeMessage.messageType == 0)) {
                                    this.showDoubleVerification = true;
                                }
                            }
                        }
                }, branchHourChangeTimeInSec * 1000);
            } else {
                if (moment(moment().format('YYYY-MM-DD') + ' ' + current_time).isAfter(moment(moment().format('YYYY-MM-DD') + ' ' + this.userData.siteConfigs.branch_start_time))) {
                    var actualStartTime = moment(moment().add(1, 'days').format('YYYY-MM-DD') + ' ' + this.userData.siteConfigs.branch_start_time);
                    branchHourChangeTimeInSec = Math.abs(moment().diff(moment().hour(23).minute(59).seconds(59), 'seconds')) + Math.abs(moment(actualStartTime.format('YYYY-MM-DD') + ' 00:01').diff(moment(actualStartTime.format('YYYY-MM-DD ') + this.userData.siteConfigs.branch_start_time).hour(branch_start_hour).minute(branch_start_minutes).seconds(0), 'seconds')) + 61;
                } else {
                    branchHourChangeTimeInSec = Math.abs(moment(moment().format('YYYY-MM-DD') + ' ' + this.userData.siteConfigs.branch_start_time).diff(moment().hour(current_time_hour).minute(current_time_minutes).seconds(current_time_seconds), 'seconds'));
                }
                this.branchHourChangeTimer = setTimeout(() => {
                    if("restrict_to_branch_hour" in self.userData.config && self.userData.config.restrict_to_branch_hour == 1 && Number(self.userData.group) === CONSTANTS.userGroupIds.patient) {
                        self.doNotReplyStatus = false;
                    } else {
                        self.showDoubleVerificationStatus = 0;
                        self.showDoubleVerification = false;
                    }
                    
                }, branchHourChangeTimeInSec * 1000);
            }
        }

        if (("restrict_to_branch_hour" in this.userData.config && this.userData.config.restrict_to_branch_hour == 0) && this.tDiff >= (this.messageReplyTimeout * 60) && this.userData.group == '3') {
            this.doNotReplyStatus = true;
            this.showDoubleVerification = false;
            this.showDoubleVerificationStatus = 0;
        } else {
            if("restrict_to_branch_hour" in this.userData.config && this.userData.config.restrict_to_branch_hour == 1 && this.userData.group == '3') {
                if(this._inboxService.checkInfusionHours(false).isWorkingHours) {
                    this.doNotReplyStatus = false;
                } else {
                    this.doNotReplyStatus = true;
                    this.showDoubleVerification = false;
                    this.showDoubleVerificationStatus = 0;
                }
            } else {
                this.doNotReplyStatus = false;
                if (!this._inboxService.checkInfusionHours(false).isWorkingHours && this.userData.siteConfigs.working_hour == '1' && this.userData.config.enable_double_verification == '1' && this.userData.group == '3' && (!this.activeMessage.messageType || this.activeMessage.messageType == 0)) {
                    this.showDoubleVerification = true;
                }
            }
        }
        this.setDoubleVerificationStatus = function (status) {
            this.showDoubleVerificationStatus = status;
            this.showDoubleVerification = false;
            var verificationChoosed = '';
            if (status) {
                verificationChoosed = 'Next Business Day Is Ok';
            } else {
                verificationChoosed = 'Yes, Urgent';
            }
            var activityData = {
                activityName: "Double Verification",
                activityType: "messaging",
                activityDescription: this.userData.displayName + "(" + this.userData.userId + ") has choosed " + verificationChoosed + " " + "in chatroom-" + this.currentChatroomId
            };
            this._structureService.trackActivity(activityData);
        }
        this.timeoutRefresh = this.messageReplyTimeout * 60 > this.tDiff ? (this.messageReplyTimeout * 60) - this.tDiff : 0;
        if (("restrict_to_branch_hour" in this.userData.config && this.userData.config.restrict_to_branch_hour == 0) && this.tDiff < (this.messageReplyTimeout * 60) && this.tDiff > 0) {
            var self = this;
            setTimeout(() => {
                if (self.userMessagePolling) {
                    self.doNotReplyStatus = false;
                    if (!self._inboxService.checkInfusionHours(false).isWorkingHours && self.userData.siteConfigs.working_hour == '1' && self.userData.config.enable_double_verification == '1' && self.userData.group == '3' && (!self.activeMessage.messageType || self.activeMessage.messageType == 0)) {
                        self.showDoubleVerification = true;
                    }
                } else {
                    self.doNotReplyStatus = true;
                    if (self.userData.group == '3' && self._inboxService.checkInfusionHours(false).isWorkingHours) {
                        self.showDoubleVerification = false;
                        self.showDoubleVerificationStatus = 0;
                    }
                }
            }, this.timeoutRefresh * 60 * 1000);
        }
        this.patientoffDutyMessage = this.getPatientoffDutyMessage(this.activeMessage.chatWithName);
        /*****************End Message reply timeout checkings*****************/

        this.reloadChatRoom = function (evt, message, index) {
            this.showOooMessage = false;
            this.allParticipants = [];
            this.roleParticipants = [];
            this.chatParticipants = [];
            this.chatroomUsersListFull = [];
            this.receiverIsNotContactable = false;
            this.admissionId = '';
            if ($('#mention-users-dropdown').is(":visible")) {
                $('#mention-users-dropdown').hide();
            }    
            $('#compose_msg').html(" ");            
            this.toggleTranslation = false;
            if($("#tag-button").is(':visible')) {
                this.closeTagModal();
            }
            if($("#msginfo-button").is(':visible')) {
                this.closeTagModal();
            }
            if($("#sender-msginfo-button").is(':visible')) {
                this.closeTagModal();
            }
            this.config = this._structureService.userDataConfig;

            this.configData = JSON.parse(this.config);
            if(this.configData.default_category_of_message_display!=''&&this.configData.default_category_of_message_display!='undefined')
            {
                this.messageFilter=this.configData.default_category_of_message_display;
            }
            this.inviteEnableButton = false;
            this.pdgAssosiatedPatientArray = [];
            this.patientTagInfo = {};
            this.patientInfo  = [];
            this.selectedchatroom = true;
            this.canReplyForMaskedMessage = false;
            this.search.nativeElement.value = ""; 
            this.flag = true; 
            if ($('.subjectDropdown').hasClass('show')) {
                $('.subjectDropdown .dropdown-toggle').dropdown("toggle");
            }
            evt.stopPropagation();
           
            if (+this.currentChatroomId !== +message.chatroomid || +this.currentChatroomId !== +message.chatroomId ) {                
                this.flagFilterValue = 0;                
                if(this.imageUploadProgress) {
                    this.clearProgressbarscope();
                }
                if(this.selectedFiles.length) {
                    this.withAttachment = false;
                    this.selectedFiles = [];
                    $(".emojionearea-editor").attr("placeholder", "Type here!");
                    this.loadAttachment = false;
                }
                this.caregiverTitle = '';
                let setChatWithHeading: any = 0;
                if (message.chatWithRole == 'Caregiver') {
                    setChatWithHeading = 1;
                }
                localStorage.setItem('setChatWithHeading', setChatWithHeading);

                setTimeout( function(){
                    $('.overlay-hidden-scroll').show();
                })
                $('#chattype').html('');
                let unreadMessageStatus = message.unread;
                this.updateInboxDataOnRead();
                if (parseInt(message.unread) || parseInt(message.unreadCount) || message.hasUnreadMessages) {
                    this._sharedService.readInboxData.emit(message);
                }
                this.inboxData.map((inputMessage) => {
                if ((+inputMessage.chatroomId === +message.chatroomId) && !message.maskedSubCount) {
                    inputMessage.unreadCount = 0;
                    inputMessage.unread = 0;
                    inputMessage.maskedUnreadCount = 0;
                    inputMessage.hasUnreadMessages = false;
                }
                return inputMessage;
            });
                if (evt && (((($(evt.target.parentElement).hasClass('from-name') && this.configData.show_prototypes === '1' && this.userData.group !== '3') || ($(evt.target).hasClass('icon-pending') && this.userData.privileges.indexOf('clinicianApprover') !== -1)) && (message.grp === '3' || (message.chatWithRoleId === '3' && message.grp === this.userData.group))) || $(evt.target.parentElement).hasClass('forward-link-col'))) {
                    if ($(evt.target.parentElement).hasClass('from-name')) {
                        this.activePatient = message;
                    } else if ($(evt.target).hasClass('icon-pending')) {
                        this.activePatient = message;
                    } 
                    setTimeout( function(){
                        $('.overlay-hidden-scroll').hide();
                    })
                } else if (evt.target.tagName !== 'A') {
                    this.activeMessage = message; //Forwad option from chat window.
                    var chatWithDob = "";
                    var fromDob = "";
                    var chatwithDay = new Date(message.chatWithDob).getDay();
                    var messageDay = new Date(message.dob).getDay();
                    this.patientSiteId = [];
                    this.tempChatroomPatientSiteId = '0';
                    this.chatroomPatientSiteId = '0';
                    this.chatroomSiteIds = '0';
                    this.isSiteFilterDisabled = false;
                    if (message.chatWithDob && !isNaN(chatwithDay) && message.chatWithRole != 'Caregiver') {
                        chatWithDob = " DOB " + this.datePipe.transform(message.chatWithDob, 'MM/dd/yy');
                    }
                    if (message.dob && !isNaN(messageDay) && message.chatWithRole != 'Caregiver') {
                        fromDob = " DOB " + this.datePipe.transform(message.dob, 'MM/dd/yy');
                    }
                    var chatwith = "";
                    var chatWithRole_n_Name = message.chatWith ? message.chatWith + ", " + message.chatWithRole : "";
                    var chatFromRole_n_Name = message.fromName ? message.fromName + ", " + message.role : "";
                    if (message.chatWithRole == 'Caregiver' || message.role == 'Caregiver') {
                        let dob = message.caregiver_dob || message.chatWith_caregiver_dob;
                        fromDob = " DOB " + this.datePipe.transform(dob, 'MM/dd/yy');
                        chatWithDob = " DOB " + this.datePipe.transform(dob, 'MM/dd/yy');
                        chatwith = this.userData.userId === message.userid ? (message.chatWithRoleId === "3" ? ((message.caregiver_userid || message.chatWith_caregiver_userid) ? (message.caregiver_displayname || message.chatWith_caregiver_displayname) + chatWithDob + " (" + message.chatWith + ")" : message.chatWith + chatWithDob) : message.chatWith + ", " + message.chatWithRole) : (message.grp === "3" ? ((message.caregiver_userid || message.chatWith_caregiver_userid) ? (message.caregiver_displayname || message.chatWith_caregiver_displayname) + fromDob + " (" + message.fromName + ")" : message.fromName + fromDob) : message.fromName + ", " + message.role);
                    } else {
                        chatwith = this.userData.userId === message.userid ? (message.chatWithRoleId === "3" ? message.chatWith + chatWithDob : chatWithRole_n_Name) : (message.grp === "3" ? message.fromName + fromDob : chatFromRole_n_Name);
                    }
                    this.chatWithHeading = parseInt(message.message_group_id) && message.groupName ? message.groupName : (parseInt(message.messageType) && parseInt(message.messageType) != 2 ? "Message from " + ((message.userid !== this.userData.userId) ? chatFromRole_n_Name : "Me") : parseInt(message.messageType) != 2 ? "Chat with " + chatwith : message.title);
                    if(message.messageType == 1) {
                        this.chatWithHeading = 'Broadcast ' + this.chatWithHeading;
                    }
                    var targetID = message.chatroomId;
                    if (!message.isSelfMessage && message.isPatientInitiatedChat && message.messageUnreadCount) {
                        var pushMessage = 'Your message is getting reviewed';
                        let sentTime = message.messageOrder;
                        this._structureService.socket.emit("confirmReviewPushNotification", { chatRoomId: targetID.toString(), messageUserId: message.userid || message.chatCreatedBy }, (data, type) => {
                            if (type && data < sentTime) {
                                let deeplinking:any = {
                                    state: "eventmenu.group-chat",
                                    stateParams: {
                                        targetID: targetID,
                                        targetName: "group-chat"
                                    },
                                    activeMessage: {
                                        sent: message.sent,
                                        messageType: message.messageType || 0,
                                        baseId: message.baseChatroomId || 0,
                                        userid: message.userid || message.chatCreatedBy,
                                        fromName: `"${message.chatHeading}"`,
                                        message_group_id: message.message_group_id || 0,
                                        createdby: message.createdby || 0
                                    }
                                };
                                if(message.message_group_id && message.groupName) {
                                    deeplinking.activeMessage.chatWithHeading = message.groupName;
                                }
                                if(message.selectedTenantId) {
                                    deeplinking.activeMessage.selectedTenantId = message.selectedTenantId;
                                }
                                const eventId= message.id || '';
                                const messageReviewNotificationData = {
                                    sourceId: CONSTANTS.notificationSource.message,
                                    sourceCategoryId: CONSTANTS.notificationSourceCategory.staffReviewingPatientMessage
                                };
                                this._structureService.sentPushNotification(message.userid || message.chatCreatedBy.toString(), 0, pushMessage, undefined, deeplinking, '', '',false, true, MessagePriority.NORMAL, eventId, messageReviewNotificationData);
                            }
                        });
                    }
                    this.activeMessage.chatWith = message.chatHeading;
                    this.activeMessage.chatWithUserId = message.userid || message.chatCreatedBy;
                    const self = this;
                    this.patientoffDutyMessage = this.getPatientoffDutyMessage(message.chatWithName);

                    /*****************Message reply timeout checkings*****************/

                    this.userMessagePolling = false;
                    this.messageReplyTimeout = this.userData.config.message_reply_timeout;
                    this.tDiff = message.messageOrder ? moment().diff(moment(message.messageOrder * 1000), 'minutes') : 0;
                    if (this.tDiff >= (this.messageReplyTimeout * 60) && this.userData.group == '3') {
                        this.doNotReplyStatus = true;
                        this.showDoubleVerification = false;
                        this.showDoubleVerificationStatus = 0;
                    } else {
                        this.doNotReplyStatus = false;
                        if (!this._inboxService.checkInfusionHours(false).isWorkingHours && this.userData.siteConfigs.working_hour == '1' && this.userData.config.enable_double_verification == '1' && this.userData.group == '3' && (!this.activeMessage.messageType || this.activeMessage.messageType == 0)) {
                            this.showDoubleVerification = true;
                        }
                    }


                    this.timeoutRefresh = (this.messageReplyTimeout * 60) - this.tDiff;
                    if (this.tDiff < (this.messageReplyTimeout * 60) && this.tDiff > 0) {

                        setTimeout(function () {
                            if (self.userMessagePolling) {
                                self.doNotReplyStatus = false;
                                if (!self._inboxService.checkInfusionHours(false).isWorkingHours && self.userData.siteConfigs.working_hour == '1' && self.userData.config.enable_double_verification == '1' && self.userData.group == '3' && (!self.activeMessage.messageType || self.activeMessage.messageType == 0)) {
                                    self.showDoubleVerification = true;
                                }
                            } else {
                                self.doNotReplyStatus = true;
                                if (self.userData.group == '3') {
                                    self.showDoubleVerification = false;
                                    self.showDoubleVerificationStatus = 0;
                                }
                            }
                        }, this.timeoutRefresh * 60 * 1000);
                    }

                    /*****************End Message reply timeout checkings*****************/
                    this.selectedRow = message.chatroomid;
                    this._structureService.socket.emit("leaveChatRoom", this.currentChatroomId);
                    var activityData = {
                        activityName: "End Chat Session",
                        activityType: "messaging",
                        activityLinkageId: this.currentChatroomId,
                        activityDescription: "Exited from Chatroom ID - " + this.currentChatroomId
                    };
                    this._structureService.trackActivity(activityData);
                    if (chatwith == undefined || chatwith == 'undefined') {
                        this.chatWithHeading = message.pageHeading;
                        chatwith = this.chatWithHeading;
                    }
                    var activityData = {
                        activityName: "Start Chat Session",
                        activityType: "messaging",
                        activityLinkageId: targetID,
                        activityDescription: "Chat With - " + chatwith + " in Chatroom " + targetID
                    };
                    this._structureService.trackActivity(activityData);
                    $('.chattype').html('');
                    localStorage.setItem('targetId', targetID);
                    localStorage.setItem('targetName', 'group-chat');
                    localStorage.setItem('chatWithHeading', this.chatWithHeading);
                    localStorage.setItem('activeMessage', JSON.stringify(this.activeMessage));
                    this.currentChatroomId = targetID;
                    this._sharedService.currentVideoChatRoomId.emit({chatRoomId:this.currentChatroomId,pageHeading:this.pageHeading});
                    this.newMessagedetails = [];
                    this.translatedMessagesDetails = [];
                    this.page = 0;
                    this.last = 0;
                    this.socketEventSubscriptions.push(
                        this._structureService.subscribeSocketEvent('userLeave').subscribe((data) => {
                        })
                    )
                    var authtoken='';
                    if(this._structureService.getCookie('authenticationToken')){
                        authtoken=this._structureService.getCookie('authenticationToken');
                    }
					this._structureService.socket.emit("joinToChatroom", { token_auth:authtoken,user: this.userData.userId, room: this.currentChatroomId.toString(), name: this.userData.displayName,tenantId: this.userData.tenantId, tenantKey:this.userData.tenantKey,tenantName:this.userData.tenantName,sites:this.userData.mySites, avatar: this.userData.profileImageThumbUrl, citusRole: this.userData.group, patientReminderCheckingType: this.userData.config.patient_reminder_checking_type, eventSource: JSON.stringify({ environment: 'Desktop', component: CONSTANTS.joinChatroomEventSourceComponent.chatPageChatSwitch}) }, function (data) { });
                    this.socketEventSubscriptions.push(
                        this._structureService.subscribeSocketEvent('lastUserActivity').subscribe((lastUserActivity) => {
                            this.lastUserActivityTime = lastUserActivity;
                        })
                    );
                    this.loadMore(true);
                    if (this.activeMessage && +this.activeMessage.messageType !== MessageType.BROADCAST) {
                        this.callChatRoomUsers = true;
                    }
                }
            }
            this.isMaskedSubthreadSelected(message.baseId, index);     
            this.checkVideoChat();
        }
        this.loadMore(true);
        this.refreshTimeout = function () {
            this.doNotReplyStatus = true;
            this.userMessagePolling = false;
        };
        this.socketEventSubscriptions.push(
            this._structureService.subscribeSocketEvent('exitVideoChat').subscribe((result) => {
                this.joinChat = false;
                this.hideInitiatorAudioBtn = false;
                this._sharedService.applessVideo = false;
                this._sharedService.applessVideoChatroomDetails = {};
            })
        );
        this._structureService.socketConnected.subscribe(connected => {
            if(connected) {
                this._structureService.socket.off("userMessage").on("userMessage", (msg, user, images, messageId) => {
                    window.localStorage.setItem('chUserMessages', '');
                    this.sendUserMessages(msg, user, images, messageId);
                });
            }
        });
        this.socketEventSubscriptions.push(
            this._structureService.subscribeSocketEvent('updateUserMessage').subscribe((msg) => {
                const chatroomId = this.activeMessage.chatroomId || this.activeMessage.chatroomid;
                if (!isBlank(chatroomId) && +chatroomId === +msg.chatroomId) {
                    if("status" in msg && msg.status && msg.status == 'duplicate') {
                        this.newMessagedetails = this.newMessagedetails.filter(item => item.id != msg.clientId);
                    } else {
                        let deliverySqlData = {}, chatroom_id = '';
                        for (let i = 0; i < this.newMessagedetails.length; i += 1) { 
                            if (msg.id == null) {
                                this.newMessagedetails[i].failed = true;
                            }
                            if (this.newMessagedetails[i].id == msg.unique) {       
                                if (msg.unique == msg.unsendId) {
                                    this.newMessagedetails[i].insertionStatus = 0;
                                }
                                this.newMessagedetails[i].id = msg.id;
                                this.newMessagedetails[i].info_available = 1;
                                if (this.newMessagedetails[i].name != "Me") 
                                {
                                    chatroom_id = this.newMessagedetails[i].chatRoomId;
                                }
                            }
                        }
                        if (chatroom_id) {
                            deliverySqlData = {
                                chatroom_id,
                                delivery_time: new Date().getTime() / 1000,
                                read_time: new Date().getTime() / 1000,
                            }
                            this._inboxService.updateDeliveryStatus(deliverySqlData);
                        }
                    }
                }
            })
        );
        this.socketEventSubscriptions.push(
            this._structureService.subscribeSocketEvent('updateUserMessageReadStatus').subscribe((msg) => {
                for (var i = 0; i < this.newMessagedetails.length; i++) {
                    if(this.newMessagedetails[i].name =="Me"){
                    if (msg.id !=null && this.newMessagedetails[i].id == msg.id) {
                        this.newMessagedetails[i].readUsers = msg.readUsers;
                    }else if(msg.id==null){
                        var oldReadUsers = this.newMessagedetails[i].readUsers;
                        if(oldReadUsers && oldReadUsers.length){
                            setMessageReadUsers(this.newMessagedetails[i],msg);                    
                        }else{
                            if(parseInt(this.newMessagedetails[i].userid) != msg.readUsers.userid){
                                this.newMessagedetails[i].readUsers.push(msg.readUsers);
                            }                        
                        }
                    }
                }
                }

            })
        );
        this.socketEventSubscriptions.push(
            this._structureService.subscribeSocketEvent('updateMessageTags').subscribe((data) => {
                if(data && this.userData.userId != data.updatedBy && +this.currentChatroomId === +data.currentChatroomId) {
                    for (var i = 0; i < this.newMessagedetails.length; i++) {
                        if(data.messageIds.includes(this.newMessagedetails[i].id) && data.assosiatedPatient)
                        {
                            this.newMessagedetails[i].patientFirstName = data.assosiatedPatient.firstname,
                            this.newMessagedetails[i].patientLastName = data.assosiatedPatient.lastname,
                            this.newMessagedetails[i].patientDob = data.assosiatedPatient.dob,
                            this.newMessagedetails[i].patientDisplayName = data.assosiatedPatient.displayname,
                            this.newMessagedetails[i].patientCaregiverDisplayName = data.assosiatedPatient.caregiver_displayname,
                            this.newMessagedetails[i].passwordStatus = data.assosiatedPatient.passwordStatus,
                            this.newMessagedetails[i].patient = data.assosiatedPatient.userId;
                            this.newMessagedetails[i].patientTagInfo = data.patientTagInfo;
                        }
                        if (data.messageIds.indexOf(this.newMessagedetails[i].id)>-1) {
                            this.newMessagedetails[i].tag = true;
                            this.newMessagedetails[i].getitems = this.newMessagedetails[i].getitems ? [...this.newMessagedetails[i].getitems, ...data.tagNames] : data.tagNames;
                            if (data.removedTagId) {
                                this.newMessagedetails[i].getitems = this.newMessagedetails[i].getitems.filter(tag => Number(tag.id) !== Number(data.removedTagId));
                                this.removePatientAssociationFromMessage(i);
                            }
                            this.newMessagedetails[i].tagSign = (data.autoApprove)?"true":"false";
                        }
                    }
                }
            })
        );
        function setMessageReadUsers(newMessagedetails,msg){
            var oldReadUsers = newMessagedetails.readUsers;
            var userExist = false;
            for(var k=0;k<oldReadUsers.length;k++){
                if(msg.readUsers.userid==parseInt(oldReadUsers[k].userid)){
                    userExist =true;
                }
            }
            if(!userExist && parseInt(newMessagedetails.userid) != msg.readUsers.userid){
                newMessagedetails.readUsers.push(msg.readUsers);
            }
        }
        this.socketEventSubscriptions.push(
            this._structureService.subscribeSocketEvent('userTyping').subscribe((data) => {
                this.clearOrAddUserTypingMessage(data);
            })
        );
        this.socketEventSubscriptions.push(
            this._structureService.subscribeSocketEvent('userLeave').subscribe(() => {
            })
        );
        this.socketEventSubscriptions.push(
            this._structureService.subscribeSocketEvent('updateChatRoomList').subscribe((data) => {
                var list = data.users;
                var userList = [];
                var updatedUserList = [];
                list.forEach(user => {
                    var newIndex = this.chatroomUsersList.findIndex(val=>val.userId == user.userId);
                    if(newIndex == -1){
                        var avatarPic = this._structureService.getApiBaseUrl()+"citus-health/avatars/"+((user.avatar && user.avatar != '')?user.avatar:'profile-pic-clinician-nurse-default.png');
                        userList.push({userid:user.userId,name:user.displayName,images:avatarPic,viewTile:true,message: 'Connecting...'});
                    }
                });
                if(userList.length > 0){
                    this._sharedService.inviteUserVideoTile.emit({ userList });
                    
                }
                this.ChatRoomUsers();
            })
        );

        var isRoleAvailable = true;
        var clinicianRolesAvaiable = null;
        if (this.userData.group == 3) {
            if (this._inboxService.checkInfusionHours(false).isWorkingHours) {
                if (this.configData.clinician_roles_on_working_hours) {
                    clinicianRolesAvaiable = this.configData.clinician_roles_on_working_hours;
                } else {
                    isRoleAvailable = false;
                }
            } else {
                if (this.configData.clinician_roles_beyond_working_hours) {
                    clinicianRolesAvaiable = this.configData.clinician_roles_beyond_working_hours;
                } else {
                    isRoleAvailable = false;
                }
            }
            if (isRoleAvailable) {
                this._inboxService.getUsersListByRoleId(clinicianRolesAvaiable, 0, true).then((data) => {
                    this.clinicalUserDetails = data;
                    for (var _i = 0; _i < this.clinicalUserDetails.length; _i++) {
                        this.clinicalUserDetails[_i].isScheduled = 1;
                        if (this._inboxService.scheduleSelectionFilter(this.clinicalUserDetails[_i])) {
                            this.clinicalUserDetails[_i].isScheduled = 1;
                        } else {
                            this.clinicalUserDetails[_i].isScheduled = 0;
                        }
                    }
                    if(this._structureService.messageGroups && this._structureService.messageGroups.length){
                        this.messageGroup =this._structureService.messageGroups;
                        this.initInvite();
                    }
                    this.setClinicianDataRoleWise();
                }).catch((ex) => {
                });
            }else{
                if(this._structureService.messageGroups && this._structureService.messageGroups.length){
                        this.messageGroup = this._structureService.messageGroups;
                }
            }
        }
        this._sharedService.videoChatInitialize.emit(); //initializeVideoChat
        let sharedDetails = this._GlobalDataShareService.getVideoChatDetails();   
        this.msgSubEdit = this._formBuild.group({
            chatsubName: ['', [Validators.required,this.noWhitespaceValidator]]
        });      
        var msgself = this;
        $( document ).ready(function() {              
            $('.actionSubEdit .actionSubCancel').on('click', function(event){
                $('.subjectDropdown .dropdown-toggle').dropdown("toggle");
            });            
            $('.edit-messagegroup-title').on('click', function(event){ 
                msgself.chatsubName = msgself.activeMessage.title;
            });
            $('.subjectDropdown .dropdown-menu').on("click.bs.dropdown", function (e) {                
                if(e.target.className == "btn btn-primary" || e.target.className == "btn btn-default actionSubCancel") {
                    
                } else {
                    e.stopPropagation(); 
                    e.preventDefault();
                }
            });                   
        });
        var self=this;

        this.messageGroupSubscriber = this._sharedService.patientDiscussionGroup.subscribe(
            (groupItem) => {
                this.messageGroup = groupItem;
            });
            this.dropdownSettings = {
                singleSelection: false,
                text: this._ToolTipService.getTranslateData('LABELS.SELECT_TAGS'),
                selectAllText: this._ToolTipService.getTranslateData('LABELS.ALL_TAGS'),
                unSelectAllText: this._ToolTipService.getTranslateData('LABELS.CLEAR_TAGS'),
                classes: "select-tags",
                enableSearchFilter: true,
                enableFilterSelectAll: true,
                badgeShowLimit: 2
            };
            this.getAllTagsAPILoading = true;
            this.getAllTagsOnTagButtonClick();  
            this.updateChatRoomParticipant();           
            this.getAllTagsOnTagButtonClick();  
            this.videoCallEndByInitiator = this._sharedService.videoCallEndByInitiator.subscribe((data) => {
                if (data && data.chatroomId && +data.chatroomId === +this.currentChatroomId) {
                    // If initiator ends the call, then participant should not be able to join the chat
                    this.joinChat = false;
                    this.joined = false;    
                }
            });
            this.socketEventSubscriptions.push(
              this._sharedService.$messageDeleteRestore.subscribe((data) => {
                   if(data && data.chatroomId && +data.chatroomId === +this.currentChatroomId && data.messageId) {
                      this.handleMessageOnStatusChange(data);
                   }
                })
            );           
        }//End Of init function
    
    // General setting function ends

    initInvite() {
        let messageGroup: any;
        messageGroup = this.messageGroup;
        if (messageGroup.length) {
            this.memberDataGroupWise = [];
            this.memberDataGroupWise = messageGroup;
            this.memberDataGroupWise.sort(function (a, b) {
                if (a.name < b.name) return -1;
                if (a.name > b.name) return 1;
                return 0;
            });
        }
    }
    clearOrAddUserTypingMessage(data){
        if (data) {
            $(".chattype").html(data);
        } else {
            $(".chattype").html('');
        }
    }
    initGroups(){
        this.offset = 0;
        this.allMessageGroups = [];
        this.memberDataGroupWise = [];
        this.messageGroup = [];
        this.hideLoadMore = false;
        this.prevText = null;
    }
    
    reset(optionShow='') {
        if (optionShow === 'groups') {
            this.ispdgs = '1';
        } else {
            this.ispdgs = optionShow === 'msggroups' ? '0' : '';
        }
        if(this.optionShow === 'msggroups') {
            this.initGroups();
            $('#userSearchTxt').val("");
            this.getAllMessageGroupDetails(this.userData.tenantId, this.userData.userId,'',this.ispdgs);
        }
    }
     
    searchMsgGrp(optionShow='') {
        if(optionShow == 'groups') {
            this.ispdgs = '1';
        }else if(optionShow == 'msggroups'){
            this.ispdgs = '0';
        }else{
            this.ispdgs = '';
        }
       if($('#userSearchTxt').val().trim()){
           this.initGroups();
           this.getAllMessageGroupDetails(this.userData.tenantId, this.userData.userId,$('#userSearchTxt').val().trim(), this.ispdgs);    
       } 
    }

    loadMoreGroups(optionShow='') {
       if(optionShow == 'groups') {
            this.ispdgs = '1';
        }else if(optionShow == 'msggroups'){
            this.ispdgs = '0';
        }else{
            this.ispdgs = '';
        }
        if(!this.prevText) {
            if($('#userSearchTxt').val().trim()){
                this.searchMsgGrp(optionShow);      
            } else {
                this.offset = this.offset+25;
                this.getAllMessageGroupDetails(this.userData.tenantId, this.userData.userId,'',this.ispdgs);
            }
        } else if(this.prevText && !$('#userSearchTxt').val().trim()) {
            this.reset();
        } else if(this.prevText == $('#userSearchTxt').val().trim()) {
            this.offset = this.offset+25;
            this.getAllMessageGroupDetails(this.userData.tenantId, this.userData.userId,$('#userSearchTxt').val().trim(),this.ispdgs);
        } else {
            this.searchMsgGrp(optionShow);
        }
     }
     getAssocSiteIds(data: any, page :string){
        this.assocSiteId = data.siteId;
        if (page == 'Associate Patient') {
          if (this.assocSiteId !== "0") {
            this.assocSiteId = this.assocSiteId.toString();
            this.siteIdEmpty = false;
          }
          this.closeSelectedAssociatePatient(); // Clear the selection on site change
        }
    }
     selectAssosiatePatient(selectedAssociatePatientId,selectedAssociatePatientName,externalSystemId = this.userData.config.esi_code_for_patient_identity) {
        this.userNotSelected = false;
        this.patient_identity =externalSystemId;
        this.selectedAssosiatePatient = selectedAssociatePatientId;//$("#assosiatedPatients").val();
        this.selectedUserForTag = this.selectedAssosiatePatient;
        this.selectedAssosiatePatientName = selectedAssociatePatientName;
        $("input#associate-search-input").val(selectedAssociatePatientName);
        this.selectedPatient = selectedAssociatePatientName;
        this.selectedPatientId = this.selectedAssosiatePatient;
        this.enableOrDisableUiLI(false,false);
        var activityData = {
          activityName: "Select Associate Patient",
          activityType: "structured forms",
          activityDescription: this.userData.displayName+"("+this.userData.userId + ") selected Associate Patient -"+this.selectedAssosiatePatientName+"("+ this.selectedAssosiatePatient +")" 
        };
        this._structureService.trackActivity(activityData);
    
      }
     checkAssociatePatientWithTems(){
        let searchTerm =  $("#associate-search-input").val();
        if(searchTerm!=""){
          this.getAssociatePatients('',searchTerm,this.assocSiteId);
        }
      }
      getAssociatePatients(form='', search:string="",assocSiteId=0){   
        $("#associate-search").text(" ").text("Loading");
        this.associatePatientLoading = true;
        if(this.activeMessage.selectedTenantId){
            var patientMessage=true;
        }else{
            patientMessage=false;
        }
        this.userService.getAssociatedPatientsLists(form, search,0,this.assocSiteId).subscribe((result: any) => {
          this.associatePatientLoading = false;
          $("#select2-assosiatedPatients-container .select2-selection__placeholder").html("").html("Select Associated Patient");
          $("#assosiatedPatients").attr("disabled", false);
          this.assosiatedPatients = result.filter((result) => {
            let date = "";
            if (result.caregiver_dob) {
              date = result.caregiver_dob;
              let dobDay = new Date(date).getDay();
              if (date && !isNaN(dobDay)) {
                date = date.replace(/-/g, '/');
                try {
                  date = moment(date).format('MM/DD/YYYY');
                }
                catch (e) {
                  date = '';
                }
              } else {
                date = "";
              }
            } else {
              date = "";
            }
            result.caregiver_dob_formatted = date;
            date = "";
            if (result.dob) {
              date = result.dob;
              let dobDay = new Date(date).getDay();
              if (date && !isNaN(dobDay)) {
                date = date.replace(/-/g, '/');
                try {
                  date = moment(date).format('MM/DD/YYYY');
                }
                catch (e) {
                  date = '';
                }
              } else {
                date = "";
              }
            } else {
              date = "";
            }
            result.dob_formatted = date;
            let listDisplayName =(result.caregiver_displayname && (result.relation_type &&  result.relation_type!=2))?((result.dob_formatted)?(result.displayname+' ('+result.caregiver_displayname +') - ' + result.dob_formatted):(result.displayname+' ('+result.caregiver_displayname +')')):((result.dob_formatted)?(result.displayname+ ' - ' +result.dob_formatted):result.displayname);
            listDisplayName = listDisplayName+(result.IdentityValue != null ? " [MRN: "+result.IdentityValue+"]":"");
            listDisplayName =listDisplayName +(result.passwordStatus == 'true' || result.passwordStatus == true ? " (Enrolled)" : " (Virtual)");
            if (result.sitename != "") {
                listDisplayName = listDisplayName + " " + "-" + " " + result.sitename;
              }
            result.listDisplayName = listDisplayName;
            return true;
          });
          $("#associate-search").text("").text("Search");
          if(search !=""){        
            this.enableOrDisableUiLI(true,false);
          }
        });
      }
      closeSelectedAssociatePatient(){
        if(this.selectedAssosiatePatient){
          this.enableOrDisableUiLI(false,true);
          this.selectedAssosiatePatientName="";
        }else if($("input#associate-search-input").val() !=""){
          $("input#associate-search-input").val("");
          $("#associate-search").text(" ").text("Search");
          this.associatePatientLoading = false;      
        }
        this.assosiatedPatients = []; 
        this.selectedAssosiatePatient = '';
        this.selectedPatientId = '';
        this.selectedPatient = '';
        this.userNotSelected = true;
      }
     openAssociateList(){
        if(!this.associatePatientLoading){
          if(this.assosiatedPatients.length){
            this.enableOrDisableUiLI(true,false);
          }
        }
      }
      enableOrDisableUiLI(condition,clear:boolean=true){
        if(condition){
          $("ul.associate-ul").css('display','block');
          $("input#associate-search-input").addClass("active");
        }else{
         $("ul.associate-ul").css('display','none');
         $("input#associate-search-input").removeClass("active");
        }
        if(clear){
          $("input#associate-search-input").val('');
        }
      }
    getAllMessageGroupDetails(tenantId, userId, searchKeyword="", isPdg=""){
        this.loadingGroups = true
        if(tenantId && userId){
            this.searchValue = searchKeyword;
             this._structureService.setCookie('isFromInviteChat', '1', 1);
             let groupType = GroupType.MSGGROUP;
             if(isPdg === '1') {
               groupType = GroupType.PATIENTGROUP;
             }
             const page = this.offset/this.limit;
             let parameter : GroupsListingRequest = {
               data: {
                 pagination: {
                     page: page > 0  ? page + 1 : 1,
                     limit: this.limit
                 }
               }
             };
             if(!isBlank(searchKeyword)) {
               parameter['data'].filter = {
                 search : searchKeyword
               };
             }
             if(!isBlank(this.chatroomPatientSiteId) && +this.chatroomPatientSiteId !== 0) {
                parameter['siteIds'] = typeof this.chatroomPatientSiteId === 'string' ? this.chatroomPatientSiteId.split(',').map((num) => {return +num}) : [+this.chatroomPatientSiteId];
             } else if(!isBlank(this.chatroomSiteIds) && +this.chatroomSiteIds !== 0) {
                parameter['siteIds'] = this.chatroomSiteIds.toString().split(',').map((num) => {return +num});
             }
             this.messageService.fetchGroupData(parameter, groupType).subscribe((messageGroups) => {
                if(messageGroups.success){
                    const data = groupType === GroupType.MSGGROUP ? messageGroups.data['messageGroups'] : messageGroups.data['patientGroups'];
                    if(searchKeyword && searchKeyword!=""){
                        this.prevText = searchKeyword;
                    }
                    this.loadingGroups = false;
                    this.hideoptionShow = true;
                    if(!data || data && data.length==0) {
                       this.hideLoadMore = true;
                    } else {
                        if (data.length == 25) {
                            this.hideLoadMore = false;
                        } else {
                            this.hideLoadMore = true;
                        }
                        this.allMessageGroups = [...this.allMessageGroups, ...data];
                        this.messageGroup = this.allMessageGroups;
                        this._structureService.messageGroups = this.messageGroup;
                        this.initInvite();
                        this._structureService.setCookie('isFromInviteChat', '', -1);
                    }  
                } else {
                    this._structureService.setCookie('isFromInviteChat', '', -1);
                }       
            });   
        }
    }

    noWhitespaceValidator(control: FormControl) {
        let isWhitespace = (control.value || '').trim().length === 0;
        let isValid = !isWhitespace;
        return isValid ? null : { 'whitespace': true }
    }
    updateChatSubject(f) {
         if (!f.valid) {
        var notify = $.notify('Failed! Please provide a subject');
        setTimeout(function () {
            notify.update({ 'type': 'danger', 'message': '<strong>Failed! Please provide a subject</strong>' });
        }, 1000);
        return false;
        } 
        const formObjData = this.msgSubEdit.value;
        var updateSub = {
            name: formObjData.chatsubName
        }
    
        let existStatus = 0;
        let res:any;
        this._structureService.updateChatSubject(updateSub).then((result) => {
        res = result;
        
        if(res.status == 0){ 
            existStatus = 1;
            this._structureService.notifyMessage({
              messge:'Warning!  Subject already exists'
            });
          }else{
            const updateChatSubjectAdminMessage = this.userData.displayName + " has updated the group subject to " + formObjData.chatsubName;
            const chatroomId = this.activeMessage.chatroomId || this.activeMessage.chatroomid || this.activeMessage.chatroom_id;
            this._structureService.socket.emit("userMessagetoServer", { data: updateChatSubjectAdminMessage,topic:formObjData.chatsubName, insert: true, chatRoomId: chatroomId.toString() , notification: true, tenantId: this.userData.tenantId, tenantName: this.userData.tenantName, messageType: this.activeMessage.messageType }, (errorData)=> {
                this._structureService.notifyMessage({
                    messge:'Warning!  ' + (errorData.failureReason == "notAbleToChatCategory" ? 'You have no privilege to view/send messages.' : 'This feature is not enabled for your tenant. Please contact support for more information'),
                    delay: 1000,
                    type: 'warning'
                });
                this.goToDefaultPage();
            });
           var activityData = {
              activityName: "Update Message Group Subject",
              activityType: "manage message group",
              activityDescription: this.userData.displayName + " has updated the message group subject to "+formObjData.chatsubName,
              activityLinkageId:this.currentChatroomId
            }; 
            this._structureService.trackActivity(activityData);
            $('.subjectDropdown .dropdown-toggle').dropdown("toggle");
            var notify = $.notify('Success! Subject updated');
            setTimeout(function () {
              notify.update({ 'type': 'success', 'message': '<strong>Success! Subject updated</strong>' });
            }, 1000); 
            this.activeMessage.title = formObjData.chatsubName;
            this.activeMessage.chatSubject = formObjData.chatsubName;   
            var index= this.inboxData.findIndex(x=> +x.chatroomId === +this.currentChatroomId);
            var strIndex = this._structureService.inboxDataFirstPage.findIndex(x=> +x.chatroomId === +this.currentChatroomId);
            if(this.inboxData && this.inboxData.length && index != -1 && this.inboxData[index]) {
            if(this.inboxData[index].title)
            this.inboxData[index].title = formObjData.chatsubName;
            this.inboxData[index].chatSubject = formObjData.chatsubName;
            }
            if(this._structureService.inboxDataFirstPage && this._structureService.inboxDataFirstPage.length && strIndex != -1 && this._structureService.inboxDataFirstPage[strIndex]) {
            if(this._structureService.inboxDataFirstPage[strIndex].title)
            this._structureService.inboxDataFirstPage[strIndex].title = formObjData.chatsubName;
            this._structureService.inboxDataFirstPage[strIndex].chatSubject = formObjData.chatsubName;;
            }
            localStorage.setItem('activeMessage', JSON.stringify(this.activeMessage));
          }  
        });
   }
    selectAllRoleUsers(data) {
        this.orderMainRoles = [];
        var self = this
        this.orderMain.forEach(function (value, key) {
            if (data == key) {
                if (value) {
                    self.orderMainRoles.push(key);
                    $('.userRole-' + data).each(function () {
                        var alt = $(this).attr("alt")
                        self.order[alt] = true;
                    });
                } else {
                    $('.userRole-' + data).each(function () {
                        var alt = $(this).attr("alt")
                        self.order[alt] = false;
                    });
                }
            }
        });
        setTimeout(() => {
            this.formated('staff');
        }, 100);
    }
    selectAllGroupUsers(data) {
        this.orderMainGroups = [];
        var self = this
        this.orderGroupMain.forEach(function (value, key) {
            if (data == key) {
                if (value) {
                    self.orderMainGroups.push(key);
                    $('.userRole-' + data).each(function () {
                        var alt = $(this).attr("alt")
                        self.orderGroup[String(alt)] = true;
                    });
                } else {
                    $('.userRole-' + data).each(function () {
                        var alt = $(this).attr("alt")
                        self.orderGroup[String(alt)] = false;
                    });
                }
            }
        });
        setTimeout(() => {
            this.formated('groups');
        }, 100);
    }
    formated(type) {
        let order = null;
        order =  (type === 'groups' || type === 'msggroups' )? this.orderGroup : this.order;
        Object.keys(order).forEach(key => {
            if (order[key]) {
              if (!this.selectedUserRoles.includes(key)) {
                // Push if no elements found for the key
                this.selectedUserRoles.push(key);
              }
            } else {
              // Filter only selected users  
              this.selectedUserRoles = this.selectedUserRoles.filter(role => role !== key);
            }
        });
    }
    setCheckBoxListner() {
        this.setupComponentStatus = setInterval(() => {
            if (this.checkComponentStatus) {
                $('input[type="checkbox"]').change(checkboxChanged);
                clearInterval(this.setupComponentStatus);
            }
        }, 100);
        function checkboxChanged() {
            var $this = $(this),
                checked = $this.prop("checked"),
                container = $this.parent(),
                siblings = container.siblings();

            container.find('input[type="checkbox"]')
                .prop({
                    indeterminate: false,
                    checked: checked
                })
                .siblings('label')
                .removeClass('custom-checked custom-unchecked custom-indeterminate')
                .addClass(checked ? 'custom-checked' : 'custom-unchecked');

            checkSiblings(container, checked);
        }

        function checkSiblings($el, checked) {
            var parent = $el.parent().parent(),
                all = true,
                indeterminate = false;

            $el.siblings().each(function () {
                return all = ($(this).children('input[type="checkbox"]').prop("checked") === checked);
            });

            if (all && checked) {
                parent.children('input[type="checkbox"]')
                    .prop({
                        indeterminate: false,
                        checked: checked
                    })
                    .siblings('label')
                    .removeClass('custom-checked custom-unchecked custom-indeterminate')
                    .addClass(checked ? 'custom-checked' : 'custom-unchecked');

                checkSiblings(parent, checked);
            }
            else if (all && !checked) {
                indeterminate = parent.find('input[type="checkbox"]:checked').length > 0;

                parent.children('input[type="checkbox"]')
                    .prop("checked", checked)
                    .prop("indeterminate", indeterminate)
                    .siblings('label')
                    .removeClass('custom-checked custom-unchecked custom-indeterminate')
                    .addClass(indeterminate ? 'custom-indeterminate' : (checked ? 'custom-checked' : 'custom-unchecked'));

                checkSiblings(parent, checked);
            }
            else {
                $el.parents("li").children('input[type="checkbox"]')
                    .prop({
                        indeterminate: true,
                        checked: false
                    })
                    .siblings('label')
                    .removeClass('custom-checked custom-unchecked custom-indeterminate')
                    .addClass('custom-indeterminate');
            }
        }
    }
    callAccordion(roleID, eve) {
       if ($(".expand-icon-" + roleID).hasClass("fa-plus")) {
            if(this.optionShow == 'msggroups' || this.optionShow ==='groups'){
                var messageGroupId = roleID;
                if (this.expandedRole.length > 0) {
                    this.expandedRole.forEach(element => {
                        if (element !== messageGroupId) {
                            this.expandedRole.push(messageGroupId);
                        }
                    });
                }else {
                   this.expandedRole.push(messageGroupId);
                }
                this.clickedGroup=roleID;
                this.isGrouploadingTime = true;
                const groupType =  this.optionShow === 'groups' ? GroupType.PATIENTGROUP : GroupType.MSGGROUP;
                this.getGroupMembersByGroupId(messageGroupId, false, groupType);
              }  
            $(".expand-icon-" + roleID).removeClass("fa-plus");
            $(".expand-icon-" + roleID).addClass("fa-minus");
            $(".sub-item-panel-" + roleID).addClass("showall");
        } else {
            $(".expand-icon-" + roleID).removeClass("fa-minus");
            $(".expand-icon-" + roleID).addClass("fa-plus");
            $(".sub-item-panel-" + roleID).removeClass("showall");
        }
    } 
    sendUserMessages(msg, user, images, messageId)
    {
        if((this.activeMessage.chatroomId || this.activeMessage.chatroomid || this.currentChatroomId) == msg.chatroomId) {
            if(("restrict_to_branch_hour" in this.userData.config && this.userData.config.restrict_to_branch_hour == 0) && user != 'Administrator') {
            this.doNotReplyStatus = false;
            }
            this.userMessagePolling = true;
            this.tempCountManage = false;
            this.clearOrAddUserTypingMessage(false);
            if (this.startTimeoutRefresh) {
                clearTimeout(this.startTimeoutRefresh);
            }
            var self = this;
            if(("restrict_to_branch_hour" in this.userData.config && this.userData.config.restrict_to_branch_hour == 0) && user != 'Administrator') {
            this.startTimeoutRefresh = setTimeout(function () {
                if (self.userData.group == '3') {
                    self.refreshTimeout();
                }
            }, self.messageReplyTimeout * 60 * 60 * 1000);
            }
            if (!msg.failed) {
                this.messagePriority = MessagePriority.NORMAL;
            }
            if (!this.messageFetching && this.flagFilterValue == 0) {
                var isPdf = false;
                var image = false;
                var userName, userClass, owner = false, sign = false, language = '';
                if (msg.userId == this.userData.userId) {
                    userName = "Me";
                    userClass = "self";
                    owner = true;
                    msg.avatar = this.userData.profileImageThumbUrl;
                    language = navigator.language;
                } else {
                    userName = msg.caregiverUserid ? msg.caregiverDisplayname + " (" + user + ")" : user
                    userClass = "other";
                    language = msg.language;
                }
                    var time;
                    var insertionStatus=0;
                    
                    if(msg.time)
                    {
                        time=msg.time;
                    }
                    if(msg.sender_time)
                    {
                        time=msg.sender_time;
                    }
                    
                    if(msg.insertionStatus){
                        insertionStatus=msg.insertionStatus;

                    }
                    const mentionedUsers = msg.mentionedUsers ? msg.mentionedUsers : [];
                var details = {
                    "fileDownload": this.getFileFromMessage(msg.data),                    
                    "id": msg.id,
                    "name": userName,
                    "class": userClass,
                    "avatar": msg.avatar,
                    "msg": msg.data,
                    "message": msg.data,
                    "time": time,
                    "isImage": image,
                    "isPdf": isPdf,
                    "sign": sign,
                    "owner": owner,
                    "userid": msg.userId,
                    "tag": 'false',
                    "tagSign": "false",
                    "readUsers":[],
                    "language": language,
                    "msg_flag": 0,
                    "prev_msg_flag": 0,
                    "msg_flag_data_id": null,
                    "insertionStatus":insertionStatus,
                    "deliveredUsers":[],
                    "msg_sent":msg.time,
                    "chatRoomId": this.currentChatroomId,
                    "info_available":msg.info_available,  
                    "failed":msg.failed,
                    messageStatus : 1,
                    tagedItems: [],
                    messageDeletedTime: '',
                    priorityId: msg.priorityId,
                    msgDisplayName: user,
                    showMention: mentionedUsers.indexOf(Number(this.userData.userId)) !== -1
                };

                if (user == "Administrator") {
                    this.ChatRoomUsers();
                } 

                var messageIndex;
              
                if(msg.id!=msg.uniqueId)
                {
                    messageIndex = this.pushMessageWithouTranslate(details);                  
                }
                else if(msg.userId != this.userData.userId){
                      messageIndex = this.pushMessageWithouTranslate(details);
                 }
               if (((parseInt(this.userData.config.toggle_chat_translation) && this.toggleTranslation) || (!parseInt(this.userData.config.toggle_chat_translation) && parseInt(this.userData.config.chat_auto_translate))) && userName != 'Me' && (msg.language.split('-')[0] != navigator.language.split('-')[0])) {
                    var self = this;
                    this._ChatService.translateMessage(encodeURIComponent(msg.data), this.currentChatroomId).then(function (res) {
                        var responseTranslation = JSON.parse(JSON.stringify(res));
                        responseTranslation = responseTranslation.content;
                       self.translatedMessagesDetails[messageIndex].message = responseTranslation.data.translations[0].translatedText;
                        self.translatedMessagesDetails = self.translatedMessagesDetails.slice();
                        var ccLanguage = (navigator.language).split("-")[0];
                        var targetIdTrans = localStorage.getItem('targetId');
                        self.setTranslationMessages(msg.data, responseTranslation.data.translations[0].detectedSourceLanguage, ccLanguage, '', targetIdTrans, self.userData.userId);
                    }, function (errMsg) {
                        self.autoTranslateFailureCount++;
                        if (self.autoTranslateFailureCount < 2) {
                            self._structureService.notifyMessage({
                                messge: 'Translation services are temporarily unavailable. This issue has been reported to the CitusHealth support team. We apologize for the inconvenience.'
                            });
                            var activityData = {
                                activityName: "Failure Auto Translate",
                                activityType: "manage messaging",
                                activityDescription: "Chat auto translation failed - " + errMsg
                            };
                            self._structureService.trackActivity(activityData);
                            var updateServiceHealthData = {
                                key: "callbell-auto-translate-service",
                                status: "down",
                                message: 'System is not operational'
                            };
                        }
                    });
                }
                this.scrollBottom();
            }
        }
    }

   checkUniqueid(msg,callback) {
     callback(msg.id.toString() === msg.uniqueId.toString());
   }
    async getGroupMembersByGroupId(groupId,choose=false, groupType){
        const groupIds = typeof(groupId) === 'string' ? [+groupId] : groupId;
        let parameter : GroupsListingRequest = {
          data: {
            groupIds: groupIds,
          }
        };
        if(groupType === GroupType.PATIENTGROUP) {
            if(typeof(groupId) === 'string') {
                parameter = {
                    data: {
                      patientId: +groupId,
                    }
                };
            } else {
                parameter = {
                    data: {
                        patients: groupId.map((user) => {
                            return {
                                id: +user.patientId,
                                ...(this.isMultiAdmissionsEnabled && { admissionId: user.admissionId })
                            };
                        })
                    }
                };
            }
          
        }
        await this.messageService.fetchGroupData(parameter, groupType).toPromise().then((messageGroups) => {
          if(messageGroups && messageGroups.data) {
            let responseData = messageGroups.data["memberDetails"] || [];
            this.selectedRolesFromGroup = messageGroups.data["selectedRoles"] || [];
            if (responseData && responseData.length || ( messageGroups.data["selectedRoles"] && messageGroups.data["selectedRoles"].length)){
                responseData.sort((a, b) => {
                    if (a.displayName.toLowerCase() < b.displayName.toLowerCase()) return -1;
                    if (a.displayName.toLowerCase() > b.displayName.toLowerCase()) return 1;
                    return 0;
                });
                if (typeof groupId === "object" && groupId && groupId.length) {
                    if (responseData && responseData.length) {
                        responseData.forEach((value, key) => {
                            this.selectedUserRoles.push(value);
                        });
                    }
                } else {
                    let selectedMessageGroupIndex = this.memberDataGroupWise.findIndex(group => group.groupId === String(groupId));
                    if(groupType === GroupType.PATIENTGROUP) {
                    	selectedMessageGroupIndex = this.memberDataGroupWise.findIndex(group => group.patientId === String(groupId));
                    }
                    this.memberDataGroupWise[selectedMessageGroupIndex].userList = responseData;
                    this.memberDataGroupWise[selectedMessageGroupIndex].roleList =  messageGroups.data["selectedRoles"] || [];
                }
            }
            this.isGrouploadingTime = false;
          }
        });  
        return;
    }
    checkboxChangedGroup(event) {     
      if(event.target.name === 'messageGroup') {        
        this.clickedGroup = String(event.target.value);
        const messageGroupId = +JSON.parse(event.target.value);
        if(event.target.checked) {
          this.messageGroupSelected = true;          
          this.dummyCheckedGroups.push(messageGroupId);
          } else {
            if(this.dummyCheckedGroups && this.dummyCheckedGroups.length) {
                let index = this.dummyCheckedGroups.indexOf(messageGroupId);
                this.dummyCheckedGroups.splice(index, 1);     
            }
            if(isBlank(this.dummyCheckedGroups)) {
                this.messageGroupSelected = false;
            }            
        }
      }
    }
    clearAllRoleWiseUsers() {
        var val = [];
        var j = 0;

        for (var c = 0; c <= 1; c++) {
            $(':checkbox:checked:not(".select-sites :checkbox, .select-tags :checkbox")').each(function (i) {
                if($(this)[0].id !="enterKeyCheck"){
                    if (c == 0) {
                        if ($(this).val() == "on") {
                            $(this).trigger("click");
                        }
                    } else {
                        if ($(this).val() != "on") {
                            $(this).trigger("click");
                        }
                    }
                }
            });
        }
        this.selectedRoleWiseRecipients = [];
        this.selectedRoleWiseRecipientsName = "";
    }
    clearSelectedRoles(type) {
        this.selectedUserRoles = [];
        this.selectedRolesFromGroup = [];
        this.dummyCheckedGroups = [];
        this.selectedPdgGroups = [];
        if (type == 'groups' || type == 'msggroups') {
            this.orderGroup = {};
            this.orderGroupMain = [];
            this.orderMainGroups = [];
            this.resetPdgData = true;
        } else {
            this.order = [];
            this.orderMain = [];
            this.orderMainRoles = [];
        }
    }
    setOtherTenantClinicianDataRoleWise() {
        this.otherTenantClinicianDataRoleWise = [];
        if (this.otherTenantStaffList.length) {
            this.otherTenantStaffList.forEach(value => {
                var user = { 
                    id: value.id, 
                    name: value.displayName,
                    naTags: (value.naTags && value.naTags != 'null' && value.naTags != null)?  value.naTags : "",
                    naTagNames: (value.naTags && value.naTags != 'null' && value.naTags != null)?  value.naTagNames : ""
                };
                var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id };
                if (value.role.id) {
                  if (!this.otherTenantClinicianDataRoleWise[value.role.id]) {
                    this.otherTenantClinicianDataRoleWise[value.role.id] = {};
                  }
                  if (!('userList' in this.otherTenantClinicianDataRoleWise[value.role.id])) {
                    this.otherTenantClinicianDataRoleWise[value.role.id]['userList'] = [];
                  }
                  if (!('roleData' in this.otherTenantClinicianDataRoleWise[value.role.id])) {
                    this.otherTenantClinicianDataRoleWise[value.role.id]['roleData'] = {};
                  }
                  if (!('tenantId' in this.otherTenantClinicianDataRoleWise[value.role.id])) {
                    this.otherTenantClinicianDataRoleWise[value.role.id]['tenantId'] = null;
                  }
                  this.otherTenantClinicianDataRoleWise[value.role.id]['roleData'] = roleData;
                  this.otherTenantClinicianDataRoleWise[value.role.id]['userList'].push(user);
                  this.otherTenantClinicianDataRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
                  this.otherTenantClinicianDataRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
                }
              });
            this.otherTenantClinicianDataRoleWise = this.otherTenantClinicianDataRoleWise.filter(function (item) {
                return true;
            });
            this.otherTenantClinicianDataRoleWise.sort(function (a, b) {
                if (a.roleData.name < b.roleData.name) return -1;
                if (a.roleData.name > b.roleData.name) return 1;
                return 0;
            });
        }
    }
    setClinicianDataRoleWise() {
        if (this.userListChatwith.length) {
            this.clinicianDataRoleWise = [];
            this.userListChatwith.forEach(value => {
                var role = { 
                    id: value.userid, 
                    userId: value.userid, 
                    roleId: value.roleId, 
                    role: value.role, 
                    displayname: value.displayname, 
                    caregiver_displayname: null, 
                    caregiver_userid: null, 
                    isScheduled: value.isScheduled,
                    naTags: (value.naTags && value.naTags != 'null' && value.naTags != null)?  value.naTags : "",
                    naTagNames: (value.naTags && value.naTags != 'null' && value.naTags != null)?  value.naTagNames : ""
                };

                var roleData = { id: value.tenantRoleId, name: value.role, tenantRoleId: value.tenantRoleId };

                if (value.userid != this.userData.userId) {
                    if (value.role == 'Caregiver') {
                        role.caregiver_displayname = value.caregiver_displayname;
                        role.caregiver_userid = value.caregiver_userid;
                    }

                    if (value.tenantRoleId) {
                        if (!this.clinicianDataRoleWise[value.tenantRoleId]) {
                            this.clinicianDataRoleWise[value.tenantRoleId] = {};
                        }
                        if (!('userList' in this.clinicianDataRoleWise[value.tenantRoleId])) {
                            this.clinicianDataRoleWise[value.tenantRoleId]['userList'] = [];
                        }
                        if (!('roleData' in this.clinicianDataRoleWise[value.tenantRoleId])) {
                            this.clinicianDataRoleWise[value.tenantRoleId]['roleData'] = {};
                        }
                        this.clinicianDataRoleWise[value.tenantRoleId]['roleData'] = roleData;
                        this.clinicianDataRoleWise[value.tenantRoleId]['userList'].push(role);
                        value.dualRoles =JSON.parse(value.dualRoles);
                        if(value.dualRoles && value.dualRoles.length>1){
                            value.dualRoles.forEach(dualEach => {
                              if(value.tenantRoleId !=dualEach.tenantUsersRoleId){
                                if (!this.clinicianDataRoleWise[dualEach.tenantUsersRoleId]) {
                                  this.clinicianDataRoleWise[dualEach.tenantUsersRoleId] = {};                    
                                }
                                if (!('userList' in this.clinicianDataRoleWise[dualEach.tenantUsersRoleId])) {
                                  this.clinicianDataRoleWise[dualEach.tenantUsersRoleId]['userList'] = [];
                                }
                                this.clinicianDataRoleWise[dualEach.tenantUsersRoleId]['userList'].push(role);
                              }
                            })
                        }
                    }
                }
            });
            this.clinicianDataRoleWise = this.clinicianDataRoleWise.filter(function (item) {
                return true;
            });
            this.clinicianDataRoleWise.sort(function (a, b) {
                if (a.roleData.name < b.roleData.name) return -1;
                if (a.roleData.name > b.roleData.name) return 1;
                return 0;
            });
        }
        this.checkComponentStatus = true;
    }
    searchOnEnterForward(event,optionShow='') {
        if(optionShow == 'groups' || optionShow == 'msggroups') {
        } else{
            if(event.keyCode == 13) {
                if(this.searchForward.nativeElement.value.trim()){
                    this.searchRerouteForward(optionShow);
                } else {
                    this.resetForward(optionShow)
                }
            }
        }
   }
   searchRerouteForward(optionShow) { 
        if(optionShow == 'groups' || optionShow == 'msggroups') {
            
        } else{
            this.chatwithPageCount = {
                staffs: 0,
                patients: 0,
                partner:0
            }
            this.noMoreItemsAvailable = {
                users: false
            };
            this.userListChatwith = [];
            this.clinicalUserDetails = [];
            this.usersList = [];
            this.userList = [];
            this.loadMoreUsers(optionShow, this.searchForward.nativeElement.value.trim())
        } 
    }
    resetForward(optionShow) {
        this.searchForward.nativeElement.value = "";
        this.selectedRowForward = -1;
        if(optionShow == 'groups' || optionShow == 'msggroups') {
            
        } else {
            this.loadMoreSearchValue = undefined;
            this.chatwithPageCount = {
                staffs: 0,
                patients: 0,
                partner: 0
            }
            this.noMoreItemsAvailable = {
                users: false
            };
            this.userListChatwith = [];
            this.clinicalUserDetails = [];
            this.usersList = [];
            this.userList = [];
            this.loadMoreUsers(optionShow)
        }
    }
    loadMoreUsers(optionShow, searchKeyword=undefined) {
        if(!this.loadMoreSearchValue) {
            if(this.searchForward.nativeElement.value.trim()){
                this.loadMoreSearchValue = undefined;
                this.callFromInitialCountChatWithUserlist = true;
                this.chatwithPageCount = {
                    staffs: 0,
                    patients: 0,
                    partner: 0
                }
                this.noMoreItemsAvailable = {
                    users: false
                };
                this.userListChatwith = [];
                this.clinicalUserDetails = [];
                this.usersList = [];
                this.userList = [];
                searchKeyword = this.searchForward.nativeElement.value.trim();
            }
        } else if(this.loadMoreSearchValue && !this.searchForward.nativeElement.value.trim()) {
            this.resetForward(optionShow)
            return false;
        } else if(this.loadMoreSearchValue == this.searchForward.nativeElement.value.trim()) {
        } else {
            this.loadMoreSearchValue = undefined;
            this.callFromInitialCountChatWithUserlist = true;
            this.chatwithPageCount = {
                staffs: 0,
                patients: 0,
                partner:0
            }
            this.noMoreItemsAvailable = {
                users: false
            };
            this.userListChatwith = [];
            this.clinicalUserDetails = [];
            this.usersList = [];
            this.userList = [];
            searchKeyword = this.searchForward.nativeElement.value.trim();
        }
        var isRoleAvailable = true;
        var clinicianRolesAvaiable = null;
        var setCliniciansRoleAvailableResponse;
        if (this.userData.group == 3) {
            this.setCliniciansRoleAvailableOnClick({isRoleAvailable:isRoleAvailable,clinicianRolesAvaiable:clinicianRolesAvaiable},(this.userList.length ? false: true),(this.userList.length ? true: false),optionShow,searchKeyword).then( (res)=>{
                setCliniciansRoleAvailableResponse = res;
                isRoleAvailable = setCliniciansRoleAvailableResponse.isRoleAvailable;
                clinicianRolesAvaiable = setCliniciansRoleAvailableResponse.clinicianRolesAvaiable;
                if(isRoleAvailable) {
                    this.getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,(this.userList.length ? false: true),(this.userList.length ? true: false), optionShow,searchKeyword)
                } else {
                    this.chatWithUsersLoading = false;
                }
            })                
            /** ***********************End Section ****************** */
        } else {
            if(isRoleAvailable) {
                this.getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,(this.userList.length ? false: true),(this.userList.length ? true: false), optionShow,searchKeyword)
            } else {
                this.chatWithUsersLoading = false;
            }
        }
    }


    setCliniciansRoleAvailableOnClick(setParams, init, loadmore, optionShow, searchValue) {
        if (this._inboxService.checkInfusionHours(false).isWorkingHours) {
            if (this.configData.clinician_roles_on_working_hours) {
                setParams.clinicianRolesAvaiable = this.configData.clinician_roles_on_working_hours;
            } else {
                setParams.isRoleAvailable = false;
            }
        } else {
            if (this.configData.clinician_roles_beyond_working_hours) {
                setParams.clinicianRolesAvaiable = this.configData.clinician_roles_beyond_working_hours;
            } else {
                setParams.isRoleAvailable = false;
            }
        }
        /** For Patient Login User Selection Condition -- Need To Generalize */
        this.scheduledPrimaryCar = 0;
        this.defaultNurses = [];
        localStorage.setItem("defaultNurses", JSON.stringify(this.defaultNurses));
        if (this._inboxService.checkInfusionHours(false).isWorkingHours) {
            this.usersList = "";
            if (this.configData.clinician_roles_on_working_hours) {
                if(init) {
                    if(optionShow=='patient') {
                        this.chatWithLoader.patients = true;
                    } else if(optionShow=='staff') {
                        this.chatWithLoader.staffs = true;
                    } else if(optionShow=='partner') {
                        this.chatWithLoader.partner = true;
                    }
                }
                this.loadMoremessage.users = "Loading ....";
                this._inboxService.getUsersListByRoleId(this.configData.clinician_roles_on_working_hours, 0, null,null,(init ? 0 : ((optionShow == 'staff') ? this.chatwithPageCount.staffs : ((optionShow == 'partner') ? this.chatwithPageCount.partner :this.chatwithPageCount.patients))),((optionShow == 'staff') ? undefined : false),searchValue,undefined,undefined,'',optionShow).then((users:any) => {
                    this.chatWithUsersLoading = false;
                    this.loadMoreSearchValue = searchValue;
                    if(!loadmore) {
                        this.usersList = users;
                    } else {
                        this.usersList = [...this.usersList, ...users];
                    }
                    if(users.length != 20)
                    this.noMoreItemsAvailable.users = true;

                    if(optionShow=='patient') {
                        this.chatwithPageCount.patients += 1;
                    } else if(optionShow=='staff') {
                        this.chatwithPageCount.staffs += 1;
                    }
                    else if(optionShow=='partner') {
                        this.chatwithPageCount.partner += 1;
                    }
                    this.loadMoremessage.users = "Load more";
                    this.userList = this.modalFilter.transform(this.usersList, optionShow);
                    if (this.userData.group === '3') {
                        this.userList = this.userList.filter(this._inboxService.scheduleSelectionFilter);
                    }
                    var pageCount = (init ? 0 : ((optionShow == 'staff') ? this.chatwithPageCount.staffs :((optionShow == 'partner') ? this.chatwithPageCount.partner :this.chatwithPageCount.patients)))
                    if (!this.userList.length && !pageCount) {
                        let messageNoClinician = 'No clinicians are scheduled at this time.';
                        var notify = $.notify(messageNoClinician);
                        setTimeout(function () {
                            notify.update({ 'type': 'danger', 'message': '<strong>' + messageNoClinician + '</strong>' });
                        }, 1000);
                    }
                    this.clinicianLoad = true;
                    if(init) {
                        if(optionShow=='patient') {
                            this.chatWithLoader.patients = false;
                        } else if(optionShow=='staff') {
                            this.chatWithLoader.staffs = false;
                        } else if(optionShow=='partner') {
                            this.chatWithLoader.partner = false;
                        }
                    }
                }).catch((ex) => {
                    if(init) {
                        if(optionShow=='patient') {
                            this.chatWithLoader.patients = false;
                        } else if(optionShow=='staff') {
                            this.chatWithLoader.staffs = false;
                        } else if(optionShow=='partner') {
                            this.chatWithLoader.partner = false;
                        }
                    }
                });
            } else {
                this.clinicianLoad = true;
                if (this.configData.default_clinician_roles_available_on_working_hour) {
                    this._inboxService.getUsersListByRoleId(this.configData.default_clinician_roles_available_on_working_hour, 0, true).then((result) => {
                        this.chatWithUsersLoading = false;
                        this.defaultNurses = result;
                        localStorage.setItem("defaultNurses", JSON.stringify(this.defaultNurses));
                    }).catch((ex) => {
                    });
                }
            }
        } else {
            this.usersListByRoleId = "";
            if (this.configData.clinician_roles_beyond_working_hours) {
                this._inboxService.getUsersListByRoleId(this.configData.clinician_roles_beyond_working_hours, 0, true).then((users) => {
                    this.chatWithUsersLoading = false;
                    this.usersListByRoleId = users;
                    localStorage.setItem("usersListByRoleId", JSON.stringify(this.usersListByRoleId));
                    this.clinicianLoad = true;
                }).catch((ex) => {
                });
            } else {
                this.clinicianLoad = true;
                if (this.configData.default_clinician_roles_available) {
                    this._inboxService.getUsersListByRoleId(this.configData.default_clinician_roles_available, 0, true).then((result) => {
                        this.defaultNurses = result;
                        localStorage.setItem("defaultNurses", JSON.stringify(this.defaultNurses));
                    }).catch((ex) => {
                    });
                }
            }
        }
        return setParams;
    }
    getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,init, loadMore, optionShow, searchValue) {
        this.UsergroupIds = clinicianRolesAvaiable;
        if(init) {
            if(optionShow=='patient') {
                this.chatWithLoader.patients = true;
                this.UsergroupIds = clinicianRolesAvaiable;
            } else if(optionShow=='staff') {
                this.chatWithLoader.staffs = true;
                this.UsergroupIds = 3;
            } else if(optionShow=='partner') {
                this.chatWithLoader.partner = true;
                this.UsergroupIds = 20;
            }
        }
        
        this.loadMoremessage.users = "Loading ....";
        this._inboxService.getUsersListByRoleId((this.UsergroupIds ? this.UsergroupIds : 3), 0, null,null,(init ? 0 : ((optionShow == 'staff') ? this.chatwithPageCount.staffs : ((optionShow == 'partner') ? this.chatwithPageCount.partner :this.chatwithPageCount.patients))),((optionShow == 'staff' && !clinicianRolesAvaiable) ? true : ((optionShow == 'staff' && clinicianRolesAvaiable) ? undefined : false)),searchValue,undefined,undefined,'',optionShow,this.chatroomPatientSiteId).then((data:any) => {
            this.chatWithUsersLoading = false;
            this.loadMoreSearchValue = searchValue;
            if(loadMore) {
                this.clinicalUserDetails = [...this.clinicalUserDetails, ...data];
            } else {
                this.clinicalUserDetails=data;
            }
            if(data.length != 20)
            this.noMoreItemsAvailable.users = true;
            if(optionShow=='patient') {
                this.chatwithPageCount.patients += 1;
            } else if(optionShow=='staff') {
                this.chatwithPageCount.staffs += 1;
            } else if(optionShow=='partner') {
                this.chatwithPageCount.partner += 1;
            } 
            for (var _i = 0; _i < this.clinicalUserDetails.length; _i++) {
                this.clinicalUserDetails[_i].isScheduled = 1;
                if (this._inboxService.scheduleSelectionFilter(this.clinicalUserDetails[_i])) {
                    this.clinicalUserDetails[_i].isScheduled = 1;
                } else {
                    this.clinicalUserDetails[_i].isScheduled = 0;
                }
            }
            this.loadMoremessage.users = "Load more";
            this.userList = this.modalFilter.transform(this.clinicalUserDetails, optionShow);
            if (this.userData.group === '3') {
                this.userList = this.userList.filter(this._inboxService.scheduleSelectionFilter);
            }
            var pageCount = (init ? 0 : ((optionShow == 'staff') ? this.chatwithPageCount.staffs :((optionShow == 'partner') ? this.chatwithPageCount.partner :this.chatwithPageCount.patients)))
            if (!this.userList.length && !pageCount && this.userData.group == 3) {
                let messageNoClinician = 'No clinicians are scheduled at this time.';
                var notify = $.notify(messageNoClinician);
                setTimeout(function () {
                    notify.update({ 'type': 'danger', 'message': '<strong>' + messageNoClinician + '</strong>' });
                }, 1000);
            }
            if(init) {
                if(optionShow=='patient') {
                    this.chatWithLoader.patients = false;
                } else if(optionShow=='staff') {
                    this.chatWithLoader.staffs = false;
                } else if(optionShow=='partner') {
                    this.chatWithLoader.partner = false;
                }
            }

        }).catch((ex) => {
            if(init) {
                if(optionShow=='patient') {
                    this.chatWithLoader.patients = false;
                } else if(optionShow=='staff') {
                    this.chatWithLoader.staffs = false;
                } else if(optionShow=='partner') {
                    this.chatWithLoader.partner = false;
                }
            }
        });
    }



    searchOnEnter(event, optionShow='') {
        if(event.keyCode == 13) {
            if($('#userSearchTxt').val().trim()){
                this.searchMsgGrp(optionShow);
            }else if(this.search.nativeElement.value.trim()){
                this.resetPageAndLimit();
                this.loadMore(true, this.search.nativeElement.value.trim());
            }
        }
       }
    async submitSelectedRoles(type) {
        if((type == 'groups' && this.selectedPdgGroups.length) || (type == 'msggroups' && this.dummyCheckedGroups.length)) {
            const groupType =  type === 'groups' ? GroupType.PATIENTGROUP : GroupType.MSGGROUP;
            const groupIds = type === 'groups' ? this.selectedPdgGroups : this.dummyCheckedGroups;
            await this.getGroupMembersByGroupId(groupIds, false, groupType);
        }
        var selectedUserRoles = [];
        var selectUserswithIds = [];
        if (type == 'groups' || type == 'msggroups') {
            var selectedUserRole = [];
            this.selectedUserRoles.forEach((value, key) => {
                if (selectedUserRole.indexOf(value) == -1) {
                    selectedUserRole.push(value);
                }
                var displayName = '';
                if (value.caregiver_displayname) {
                    displayName = value.caregiver_displayname + " (" + value.displayName + ")";
                } else {
                    displayName = value.displayName || value.displayName || value.name;
                }
                    selectedUserRoles.push({ id: value.id, name: encodeURIComponent(displayName)});
                    selectUserswithIds.push({id:value.id});
                this.usersl = selectedUserRoles.map(function(item) {
                    return item['name'];
                  });
            });
            this.selectedUserRoles = selectedUserRole;
        }
        if (this.selectedUserRoles.length || this.selectedRolesFromGroup.length || type == 'groups') {
            var invitedBy = this.userData.displayName;
            var invitedById = this.userData.userId;
            var activeMessage = JSON.parse(localStorage.getItem('activeMessage'));
            var usersList = [];
            if (type == 'groups' || type == 'msggroups') {
                usersList = typeof this.clinicalUserDetails!== 'undefined'?this.clinicalUserDetails:[];
                usersList = usersList.concat(this.messageGroupUsersList);
                usersList = usersList.concat(this.otherTenantStaffList);
            } else if(type == 'otherTenantstaff') {
                usersList = this.otherTenantStaffList
            } 
            else if (type == 'alternatecontact') {
                usersList = this.alernateContactsData;
            }
            else {
                if(typeof this.clinicianDataRoleWise!== 'undefined'){
                usersList = [];
                this.clinicianDataRoleWise.map( (roleWise)=> {
                    if(roleWise.userList) {
                        usersList = usersList.concat(roleWise.userList)
                    }
                });
                }else{
                    usersList = this.userListChatwith;
                }
                //usersList = this.userListChatwith;
            }
            var selectedUserIds = [];
            this.usersl = '';
            usersList.forEach((value, key) => {
                var userId;
                 userId = value.userid || value.id;
                if (this.selectedUserRoles.indexOf(userId) > -1) {
                    var displayName = '';
                    if (value.caregiver_displayname) {
                        displayName = value.caregiver_displayname + " (" + value.displayname + ")";
                    } else {
                        displayName = value.displayname || value.displayName || value.name;
                    }
                    if(selectedUserIds.indexOf(userId) == -1) {
                        var selectedUserRolesdata = {};
                        selectedUserRolesdata = { id: userId, name: encodeURIComponent(displayName)};
                        if (this.userData.config.default_patients_workflow == 'alternate_contacts') { 
                            if(value.roleId && value.roleId == '3') {
                                if(userId != this.Invitealternates) {
                                    selectedUserRolesdata = { id: userId, name: encodeURIComponent(displayName), associatedId : this.Invitealternates};
                                } else {
                                    selectedUserRolesdata = { id: userId, name: encodeURIComponent(displayName)};
                                }
                            }
                        }
                        selectedUserIds.push(userId);
                        if (type == 'alternatecontact') {
                            if(userId != this.Invitealternates) {
                                selectUserswithIds.push({ id: userId, associatedId: this.Invitealternates });
                            } else {
                                selectUserswithIds.push({ id: userId });
                            }
                        }
                        else {
                            selectUserswithIds.push({ id: userId });
                        }
                        selectedUserRoles.push(selectedUserRolesdata);
                    }
                    this.usersl = selectedUserRoles.map(function(item) {
                        return item['name'];
                      });
                }
            });
            $('#inviteModal').modal('hide');
            var sentRoles = [];
           this.orderMain.map((e,key) => {
               if(e === true) {
                sentRoles.push(key)
               }
            })
            let data = {};
            if ( this.optionShow === 'staffroles' ||  this.optionShow === 'partnerroles') {
                data = `&chatroomId=${this.currentChatroomId}&inviteRoles=${JSON.stringify(sentRoles)}&siteIds=${this.selectSiteId.toString()}`;
            } else if(this.optionShow === 'groups' || this.optionShow === 'msggroups') {
                const category = type === 'groups' ? 'pdg' : 'messageGroup'; 
                let groupIds = this.dummyCheckedGroups;
                if (type === 'groups') {
                    groupIds = this._structureService.isMultiAdmissionsEnabled ? this.selectedPdgGroups : 
                    this.selectedPdgGroups.map((group) => group.patientId);
                }
                data = `&chatroomId=${this.currentChatroomId}&category=${category}&ids=${JSON.stringify(groupIds)}`;
            } else {
                data = `&chatroomId=${this.currentChatroomId}&multipleInvite=${JSON.stringify(selectUserswithIds)}`;
            }
            var msgtxt ="You are going to invite selected user(s) to this chat session";
            var IsEnterClicked=false;

            $(document).keypress(function(event){
                if (event.keyCode == 13) {
                    IsEnterClicked=true;
                }
            });

            swal({
                title: "Are you sure?",
                text: msgtxt,
                type: "warning",
                showCancelButton: true,
                cancelButtonClass: "btn-default",
                confirmButtonClass: "btn-warning",
                cancelButtonText: "Cancel",
                confirmButtonText: "OK",
                allowOutsideClick:true,
                closeOnConfirm: true
            },(confirm) => {
                if(IsEnterClicked){
                    IsEnterClicked=false;
                    this.clearSelectedRoles(this.optionShow);
                    swal.close();
                    $('#inviteModal').modal('show');
                   this.clearSelectedRoles(this.optionShow);
                    return false;
                }
        if(confirm) {
            NProgress.start();          
            this._ChatService.inviteUserToChatroom(data).then((result: any) => {
                NProgress.done();
                if (result.success) {
                    this.invitedResponse = result.data;
                     if(this.invitedResponse.invitedUsersInfo && this.invitedResponse.invitedUsersInfo.length) {
                        // Push socket need userid instead of userId
                        this.invitedResponse.invitedUsersInfo.forEach(user => {
                            user.userid = user.userId;
                        });
                    }                
                    if(this.messageFilter == 0) {
                        this.loadMoreButtonText = '';
                    }
                    this.frwdChatroomId = this.currentChatroomId;
                    this.ChatRoomUsers(false, true,true);
                    this.filterMessages('message');
                    if(this.invitedResponse.invitedMessage && this.invitedResponse.inviteStatus) {
                        this._structureService.notifyMessage({type: CONSTANTS.notificationTypes.success, messge: this.invitedResponse.invitedMessage, delay: 1500});
                    }
                    let invitedUsers = '';
                    let inviteUserIds = [];
                    if(this.invitedResponse.invitedUsersInfo) this.invitedResponse.invitedUsersInfo.forEach((value, key) => {
                        if (key == 0) {
                            invitedUsers = invitedUsers + value.name + " (" + value.userid + ")"
                        } else {
                            invitedUsers = invitedUsers + ', ' + value.name + " (" + value.userid + ")"
                        }
                        inviteUserIds.push(parseInt(value.userid));
                    });
                    let inviteRolesIds = this.invitedResponse.invitedRoles ? this.invitedResponse.invitedRoles.map(role => (+role.roleId)) : [];
                    if (this._sharedService.applessVideoChatroomDetails) {
	                    let inviteUserApplessDataParams = {
	                        chatroomId: this.currentChatroomId,
	                        roomKey: this._sharedService.applessVideoChatroomDetails.roomKey,
	                        roomPin: this._sharedService.applessVideoChatroomDetails.roomPin,
	                        host: this._sharedService.applessVideoChatroomDetails.host,
	                        roomName: this._sharedService.applessVideoChatroomDetails.roomName,
	                        action: "invite",
	                        videoId: this._sharedService.videoId,
	                        inviteUsers: inviteUserIds,
                            inviteRoles: inviteRolesIds
	                    };
	                    this._structureService.sendApplessData(inviteUserApplessDataParams).then((response: any) => {
	                        
	                    });
                    }
                    let activityData = {
                        activityName: "Invite to Chat Session",
                        activityType: "messaging",
                        activityLinkageId: this.currentChatroomId,
                        activityDescription: invitedBy + " (" + invitedById + ") has" + " invited " + invitedUsers + " to Chatroom ID - " + this.currentChatroomId
                    };
                    this._structureService.trackActivity(activityData);
                    if(this.invitedResponse && this.invitedResponse.inviteFailedStatus) {
                        let failedUsersMsg = '';
                        let failedRolesMsg = '';
                        let failedUsersList = this.invitedResponse.failedUsersInfo ? this.invitedResponse.failedUsersInfo : [];
                        let failedRolesList = this.invitedResponse.failedRoles ? this.invitedResponse.failedRoles : [];
                        if(this.invitedResponse.failedUsersInfo && this.invitedResponse.failedUsersInfo.length) {
                            let failedUsers = this.invitedResponse.failedUsersInfo.map(user => user.displayName).join(', ');
                            failedUsersMsg += `${failedUsers} ${failedUsersList.length > 1? 'users' :'user' }`;
                        }
                        if(this.invitedResponse.failedRoles && this.invitedResponse.failedRoles.length) {
                            let failedRoles = this.invitedResponse.failedRoles.map(user => user.roleName).join(', ');
                            failedRolesMsg = `${failedRoles} ${failedRolesList.length > 1? 'roles' :'role' }`;                       
                        }
                        const msg = `${failedUsersMsg}${failedUsersMsg && failedRolesMsg ? ' and ' : ''}${failedRolesMsg}${(failedRolesList.length > 1 || failedUsersList.length > 1 || (failedUsersMsg && failedRolesMsg)) ? ' are ' : ' is'}`;

                        this._structureService.notifyMessage({type: CONSTANTS.notificationTypes.warning, messge: this._ToolTipService.getTranslateDataWithParam('MESSAGES.USERS_ALREADY_INVITED', {content: msg}), delay: 5000 })                        
                    }
                } else {
                    const error = result.data && result.data.error && result.data.error.message ? result.data.error.message 
                    : this._ToolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG');
                    this._structureService.notifyMessage({type: CONSTANTS.notificationTypes.warning, messge: error});
                }                
                this.closeInviteModal();
            },() => {
                this._structureService.notifyMessage({type: CONSTANTS.notificationTypes.warning, messge: this._ToolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG')});
                NProgress.done();
            });
        }
        if(!confirm){
            swal.close();
                $('#inviteModal').modal('show');
            this.clearSelectedRoles(this.optionShow);        
        }
    }  
    )
        }
    }
    enterpressalert(e) {
        var code = (e.keyCode ? e.keyCode : e.which);
        if (code == 13) { //Enter keycode
            if ($("#enterKeyCheck").prop('checked') == true) {
                e.preventDefault();
                this.sendData();
            }else if(e.ctrlKey && code == 13){
                e.preventDefault();
                this.sendData();
            }
        }
    }
    chatEnterKeyCheck(event) {
        if(event.target.checked){
           this._structureService.setCookie('chatEnterToSend', 'true', 1);
        }else{
           this._structureService.deleteCookie('chatEnterToSend'); 
        }
       }
    backSpaceKey(e) {
        var code = (e.keyCode ? e.keyCode : e.which);
        if(code == 13) {
            this.searchMessagesAndDocs();
        }
        localStorage.setItem("searchInboxkeywordForPolling",this.searchInboxkeyword);
    }
    showData(data,patId=null) {
        this.messageGroupSelected = false;
        $('#userSearchTxt').val("");
        $('#chat-with-modal-search-box-on-invite').val('');
        this.optionShow = data;
        this.tabSelectedData = data;
        if (this.optionShow == 'staff') {
            this.hideoptionShow = false;
            this.userListChatwith = this.modalFilter.transform(this.clinicalUserDetails, 'staff');
            if(this.userListChatwith)
            this.hideoptionShow = true;
        }else if (this.optionShow == 'partner') {
            this.hideoptionShow = false;
            this.userListChatwith = this.modalFilter.transform(this.clinicalUserDetails, 'partner');
            if(this.userListChatwith)
            this.hideoptionShow = true;
        } else if(this.optionShow == 'groups'){
            this.pdgSiteIds = !isBlank(this.chatroomPatientSiteId) ? this.chatroomPatientSiteId : this.chatroomSiteIds;
            this.selecteGroupOrPdgTab = true;
            this.hideoptionShow = false;
            this.reset(this.optionShow);
        }else if(this.optionShow == 'msggroups'){
            this.selecteGroupOrPdgTab = true;
            this.hideoptionShow = false;
            this.reset(this.optionShow);
        } else if(this.optionShow=='otherTenantstaff'){
            setTimeout(() => {
                if (this.otherTenantClinicianDataRoleWise.length) {
                    let id = this.otherTenantClinicianDataRoleWise[this.otherTenantClinicianDataRoleWise.length-1].tenantId;
                    if(!$('#staffAccordion'+id).hasClass('fa-minus')){
                        this.callAccordion(this.otherTenantClinicianDataRoleWise[this.otherTenantClinicianDataRoleWise.length-1].tenantId,'');
                    }
                }
            }, 500);            
        } 
        else if(this.optionShow == 'alternatecontact'){
            
        } else {
            this.userListChatwith = this.modalFilter.transform(this.clinicalUserDetails, 'patient');
        }
        this.clearSelectedRoles(data)
        
    }

    pushMessageWithouTranslate(details) {
        var messageIndex = this.newMessagedetails.push(details);
        var data = { "message": details.msg, "chatRoomId": this.currentChatroomId, "userId": this.userData.userId,"msg_flag": details.msg_flag, "prev_msg_flag": details.msg_flag, "msg_flag_data_id": details.msg_flag_data_id };
        this.translatedMessagesDetails.push(data);
        this.translatedMessagesDetails = this.translatedMessagesDetails.slice();
        this.scrollBottom();
        return messageIndex - 1;
    }
    chatWithModel(grp) {
        this._sharedService.chatWith.emit(grp);
    }
    getMentionedUsers() {
        const mentionedUsers = [];
        const element = this.removeElementsByClass(document.getElementById('compose_msg').cloneNode(true), ['callout-content']);
        const elements = element.getElementsByClassName('mention');
        for (let i = 0; i < elements.length; i += 1) {
            const attrib = elements[i].getAttribute('attr.data-target');
            if (!isBlank(attrib)) mentionedUsers.push(Number(attrib));
        }
        return mentionedUsers;
    }
    getRepliedTo() {
        const repliedTo = [];
        const element = document.getElementById('compose_msg');
        const elements = element.getElementsByClassName('callout-content');
        for (let i = 0; i < elements.length; i += 1) {
            const attrib = elements[i].getAttribute('attr.data-target');
            if (!isBlank(attrib)) repliedTo.push(Number(attrib));
        }
        return repliedTo.filter((elem, index, self) => index === self.indexOf(elem));
    }
    formatMessage(message) {
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = message;
        this.removeElementsByClass(tempDiv, ['close']);
        const elements = tempDiv.getElementsByClassName('mention');
        Array.from(elements).forEach((el) => {
            const input = (<HTMLInputElement> el);
            if (input.value) {
                const mention = document.createElement('span');
                mention.classList.add('mention');
                mention.setAttribute('attr.data-target', input.getAttribute("attr.data-target"));
                mention.innerHTML = input.value;
                input.parentNode.replaceChild(mention, input);
            }
        });
        return tempDiv.innerHTML;
    }
    sendData() {
        if (this.withAttachment) {
            this.uploadedFile(this.withAttachmentEvent);
        } else {
            let sent = new Date().getTime() / 1000;
            const messageText = this.formatMessage(($('.emojionearea-editor').html() ? ($('.emojionearea-editor').html()).replace(/&nbsp;/gi, " ").trim() : ''));
            let deeplinking:any = {
                "state": "eventmenu.group-chat",
                "stateParams": {
                    targetID: this.currentChatroomId,
                    targetName: localStorage.getItem('targetName')
                },
                "priorityId": this.messagePriority,
                "activeMessage": {
                    sent: new Date().getTime() / 1000,
                    messageType: this.activeMessage.messageType||0,
                    baseId: this.activeMessage.baseChatroomId||0,
                    userid: this.userData.userId,
                    fromName: '"'+this.userData.displayName+'"',
                    message_group_id: this.activeMessage.message_group_id||0,
                    createdby: this.activeMessage.createdby||0
                }
            };
            if(this.activeMessage.message_group_id && this.activeMessage.groupName) {
                deeplinking.activeMessage.chatWithHeading = this.activeMessage.groupName;
            }
            if(this.activeMessage.selectedTenantId) {
                deeplinking.activeMessage.selectedTenantId = this.activeMessage.selectedTenantId;
            }
            const mentionedUsers = this.getMentionedUsers();
            const repliedTo = this.getRepliedTo();
            $(".emojionearea-editor").html("");
            if ($(".emojionearea-button").hasClass("active"))
                $(".emojionearea-button-close").click();
            if (!isBlank(messageText) && !CONSTANTS.patternForNewline.test(messageText) && !CONSTANTS.patternForBreak.test(messageText)
            && !CONSTANTS.patternForMultipleBreaks.test(messageText)) {
                this.showNullErrorMsg = false;
                var userMessagetoServerData: { [k: string]: any } = {};
                userMessagetoServerData = {
                    id: messageCenterGUID( this.currentChatroomId, this.userData.userId ),
                    data: messageText,
                    insert: true,
                    userId: this.userData.userId,
                    chatroomId: this.currentChatroomId.toString(),
                    displayName: this.userData.displayName,
                    sentTimeCheck: new Date().getTime() / 1000,
                    baseId: this.activeMessage.baseChatroomId,
                    messageType: this.activeMessage.messageType,
                    doubleVerificationStatus: this.showDoubleVerificationStatus,
                    tenantId: this.userData.tenantId,
                    tenantName: this.userData.tenantName,
                    language: navigator.language,
                    sender_time: parseInt(String(new Date().getTime()/1000)),
                    insertionStatus:1,  
                    failed:false,
                    priorityId: this.messagePriority,
                    mentionedUsers,
                    repliedTo
                }
                if(!isBlank(this.chatroomPatientSiteId) ) {
                    userMessagetoServerData.chatroomPatientSiteId = this.chatroomPatientSiteId;                                                   
                }
                userMessagetoServerData.environment = this._structureService.environment;
                userMessagetoServerData.tenantId = this.userData.tenantId;
                userMessagetoServerData.serverBaseUrl = this._structureService.serverBaseUrl;
                userMessagetoServerData.apiVersion = this._structureService.version;
                if (this.userData.privileges.indexOf('messageEscalation') !== -1 || this.userData.privileges.indexOf('messageReminder') !== -1) {
                    if (!this.configData.message_escalation_behavior || ((this.configData.message_escalation_behavior === 'escalate_first' && !this.newMessagedetails.length) || (this.configData.message_escalation_behavior === 'escalate_all' && this.activeMessage.messageType != 2 && (this.activeMessage.baseChatroomId == '0' || !this.activeMessage.baseChatroomId)))) {//&& !this._inboxService.checkInfusionHours(true).isWorkingHours
                        userMessagetoServerData.escalationTime = this.userData.config.escalation_time * 1;
                    }
                    userMessagetoServerData.scheduleData = this.userData.escalatedSchedulerData;
                    userMessagetoServerData.scheduleInterval = 30;

                    if(this.userData.master_details && this.userData.masterEnabled == "1") {
                        userMessagetoServerData.masterEscalationTime = this.userData.master_config.escalation_time * 1;
                        userMessagetoServerData.masterScheduleData = this.userData.masterEscalatedSchedulerData;
                        userMessagetoServerData.masterScheduleInterval = 30;    
                    }
                }
                userMessagetoServerData.workingHour = this._inboxService.checkInfusionHours(false).isWorkingHours;//Escalation mail checking :: 
                userMessagetoServerData.caregiverDisplayname = this.userData.caregiver_displayname;
                userMessagetoServerData.caregiverUserid = this.userData.caregiver_userid;
                if(this.myAssociatedPatient) {
                    userMessagetoServerData.caregiverDisplayname = this.myAssociatedPatient.caregiver_displayname;
                    userMessagetoServerData.caregiverUserid = this.myAssociatedPatient.caregiver_userid;
                }
                if(this.userData.master_details && this.userData.masterEnabled == "1") {
                    userMessagetoServerData.masterEnabled = 1;
                    userMessagetoServerData.isMaster = 0;
                    userMessagetoServerData.masterTenantId = this.userData.master_details.id;
                } else if(!this.userData.master_details && this.userData.masterEnabled == "1") {
                    userMessagetoServerData.masterEnabled = 1;
                    userMessagetoServerData.isMaster = 1;
                } else if(this.userData.masterEnabled == "0") {
                    userMessagetoServerData.masterEnabled = 0;
                    userMessagetoServerData.isMaster = 0;
                }
                if(this.userData.masterEnabled == "1" && this.userData.config.flex_site_patients_can_chat_with_internal_staffs == "1" && this.userData.isMaster == "0") {
                    userMessagetoServerData.includeMasterStaffs = 1;
                } else {
                    userMessagetoServerData.includeMasterStaffs = 0;
                }
                userMessagetoServerData.messageConfig = {
                    messageEscalation: this.userData.privileges.indexOf('messageEscalation') !== -1,
                    messageReminder: this.userData.privileges.indexOf('messageReminder') !== -1,
                    userCitusRole: this.userData.group
                }
                if(this.userData.master_details && this.userData.masterEnabled == "1") {
                    userMessagetoServerData.masterWorkingHour =  this._inboxService.checkMasterBranchHours().isWorkingHours;
                    userMessagetoServerData.masterAllow24HourWorking = this.userData.master_config.working_hour ? this.userData.master_config.working_hour : 0;
                    userMessagetoServerData.masterStaffSmsNotificationEnabled = this.userData.master_config.staff_message_sms_notifcation ? this.userData.master_config.staff_message_sms_notifcation : 0;
                    userMessagetoServerData.masterMessageConfig = {
                        messageEscalation: this.userData.privileges.indexOf('messageEscalation')!==-1,
                        messageReminder: this.userData.privileges.indexOf('messageReminder')!==-1,
                        userCitusRole: this.userData.group
                    }
                }
                if(+this.activeMessage.messageType === 2  && +this.activeMessage.baseChatroomId === 0 && this.activeMessage.maskedSubCount) {
                    userMessagetoServerData.maskedMessageMainThreadMessageWithChild = true;
                }
                    var anyUserMessageExists = this.newMessagedetails.some( function(messageDetails) {
                        return messageDetails.userid != 0;
                    });
                    let args:any = {type: 'messages', setCount: 2, chatroomId: this.currentChatroomId,message: messageText, sentTime: userMessagetoServerData.sentTimeCheck.toString()}
                    if(!anyUserMessageExists) {
                        args.firstUserMessageFromChatroom = true;
                        userMessagetoServerData.firstUserMessageFromChatroom = true;
                    } else {
                        args.selfUpdate = true;
                    }
                    let pushData:any = {
                        "chatRoomOrToId": this.currentChatroomId,
                        "userId": this.userData.userId,
                        "message": `You have a new message to review`,
                        "environment": this._structureService.getEnvironment(),
                        "tenantId": '',
                        "pushDeepLink": '',
                        "citusRoleId": '',
                        "privilegeKey": '',
                        "pushNotificationUpdated": true
                      };
                    if (deeplinking) {
                        pushData.pushDeepLink = deeplinking;
                    }
                    if(this._inboxService.checkInfusionHours(true).isWorkingHours == true || this.userData.group != '3' || (deeplinking && deeplinking.stateParams && deeplinking.stateParams.eventId)) {
                        userMessagetoServerData.pushData = pushData;
                    }


                    this._sharedService.messagePollingSelfUpdate.emit({data: "Polling:true", args:args});
                    userMessagetoServerData.sent = sent;
                    userMessagetoServerData.language = navigator.language;
                    userMessagetoServerData.additionalDataToResend = {
                        targetName: localStorage.getItem('targetName'),
                        message_group_id: this.activeMessage.message_group_id||0,
                        createdby: ((this.activeMessage && this.activeMessage.createdby)? this.activeMessage.createdby : 0)    
                    }
                    if(this.activeMessage.message_group_id && this.activeMessage.groupName) {
                        userMessagetoServerData.additionalDataToResend.chatWithHeading = this.activeMessage.groupName;
                    }
                    if(this.activeMessage && "selectedTenantId" in this.activeMessage && this.activeMessage.selectedTenantId) {
                        userMessagetoServerData.additionalDataToResend.selectedTenantId = this.activeMessage.selectedTenantId;
                    }
                    this.storeMessageDataToLocal( userMessagetoServerData );
                      this.sendUserMessages(userMessagetoServerData,userMessagetoServerData.displayName, false, 0);
                      if (this._structureService.socket.connected) {  
                        this._structureService.socket.emit("userMessagetoServer", userMessagetoServerData, (errorData)=> {
                            this._structureService.notifyMessage({
                                messge:this._ToolTipService.getTranslateData('WARNING.WARNING') + (errorData.failureReason == "notAbleToChatCategory" ? this._ToolTipService.getTranslateData('WARNING.NO_PRIVILEGE_CHAT_VIEW_SEND') : this._ToolTipService.getTranslateData('WARNING.CHAT_NOT_ENABLED')),
                                delay: NOTIFY_DELAY_TIME_COMMON,
                                type: 'warning'
                            });
                            this.goToDefaultPage();
                        });
                        const messageSendNotificationData = {
                            sourceId: CONSTANTS.notificationSource.message,
                            sourceCategoryId: CONSTANTS.notificationSourceCategory.messageSendNotification
                        };
                        this._structureService.sentPushNotification(this.currentChatroomId, this.userData.userId, "You have a new message to review", undefined, deeplinking, undefined, this.showDoubleVerificationStatus,false,false, undefined, undefined, messageSendNotificationData);
                     } 
                autosize.destroy(document.querySelectorAll('textarea'));
                $('.emojionearea-editor').html('');
                $(".emojionearea-editor :input").val('');
                this.scrollBottom();
                if(this.noMessage){ 
                        if(this.messageFilter!=0)
                        {
                        this.loadMoreButtonText = "";        
                        }
                      this.noMessage =false; 
                 }              
                if (this.newMessagedetails.length == 0 && !("message" in this.activeMessage)) {
                    var actMessage = JSON.parse(localStorage.getItem('activeMessage'));
                    var profilePicByRole = (this.userData.group === '3' ? 'patient' : ((this.userData.group === '2' || this.userData.group === '6' || this.userData.group === '8') ? 'clinician-nurse-default' : 'clinician'));
                    var chatWithAvatar = this._structureService.apiBaseUrl + 'citus-health/avatars/profile-pic-' + profilePicByRole + '.png';
                    let splitData: any = actMessage.chatWith.split(",");
                    if (splitData.length) {
                        actMessage.chatWith = splitData[splitData.length - 1];
                    }                   
                    let initialMessage:any = {
                        "createdby": this.userData.userId,
                        "userid": this.userData.userId,
                        "ccmUserid": this.userData.userId,
                        "chatroomid": this.currentChatroomId,
                        "message": messageText,
                        "fromAvatar": this._structureService.getCookie('profileImageUrl'),
                        "chatWithAvatar": chatWithAvatar,
                        "fromName": this.userData.displayName,
                        "chatWith": actMessage.chatWith ? actMessage.chatWith : this.userData.displayName,
                        "message_group_id": actMessage.message_group_id ? actMessage.message_group_id : "0",
                        "messageType": "0",
                        "groupName": actMessage.message_group_name ? actMessage.message_group_name : '',
                        "chatWithRole": actMessage.chatwithUserRole ? actMessage.chatwithUserRole : this._structureService.getCookie('userRole'),
                        "role": this._structureService.getCookie('userRole'),
                        "unread": "0",
                        "sent": sent,
                        "branch":actMessage.branch ? actMessage.branch : '',
                        "unreadCount": "0",
                        "hasUnreadMessages": false,
                        "category": "chat-messages",
                        "markAsRead": false,
                        "escalated": "0",
                        "pageHeading": localStorage.getItem('chatWithHeading'),
                        "forwardAction": null,
                        "forwardId": null,
                        "forwardName": null,
                        "dob": actMessage.chatwithDob ? actMessage.chatwithDob : '',
                        "title":actMessage.title,
                        "invited_status":0,
                        priorityId: this.messagePriority,
                        isSelfMessage: true,
                        deliveryTime: sent,
                        messageOrder: sent
                    };
                    if(actMessage.patient_caregiver_displayname) {
                        initialMessage.patient_caregiver_displayname = actMessage.patient_caregiver_displayname;
                    }
                   if(!this.selectedchatroom){ 
                        this.inboxData.unshift(initialMessage);
                      this.selectedchatroom = true;
                   }   
                    this.inboxData = this.restructureTags(this.inboxData.slice());
                    initialMessage.baseChatroomId = 0;
                }


                /** loop Inbox Data and update last Message */
                var inboxDataSelf = this;
                this.inboxData = this.restructureTags(this.inboxData.map( (results)=> {
                    if(results.messageType == '2' && results.initiatedBaseId == '0' && this.activeMessage.baseChatroomId == results.chatroomid) {
                        results.hiddenChatRoomId = results.chatroomid;
                        results.initiatedBaseId = results.chatroomid;
                        results.chatroomid = this.activeMessage.chatroomId || this.activeMessage.chatroomid+"";
                        results.chatWithAvatar = results.fromAvatar;
                        results.dob = results.chatWithDob;
                        results.chatWithAvatar = this.userData.profileImageUrl;
                        results.chatWithDob = this.userData.dob;
                        results.createdby =this.userData.userId;
                        var fname = results.fname;
                        var lname = results.lname;
                        results.fname = results.f_fname;
                        results.lname = results.f_lname;
                        results.f_fname = fname;
                        results.f_lname = lname;
                        results.fromName = this.userData.displayName;
                        results.userid = this.userData.userid;
                        results.messagesCount = (parseInt(results.messagesCount)+1)+'';                        
                    }
                    if (results.chatroomid == inboxDataSelf.currentChatroomId) {
                        results.message = messageText;
                        results.sent = parseInt(sent + '')+'';
                        if(results.maskedSent) {
                            results.maskedSent = parseInt(sent + '')+'';
                        }
                    }
                    if (isBlank(results.subList)) {
                        results.subList = [];
                    } 
                    if (isBlank(results.subListIDs)) {
                        results.subListIDs = [];
                    }
                    this.inboxListScrollTop();                   
                    return results;
                }));
            } else {
                const relatedDivs = $(this.emojipicker.nativeElement).siblings('div.emojionearea');
                if(relatedDivs.length) {
                    this.showNullErrorMsg = true;
                    setTimeout(()=> {
                        this.showNullErrorMsg = false;
                    }, 5000);
                } else {
                    $.notify({ message: this._ToolTipService.getTranslateData('MESSAGES.MSG_SENDING_FAILED') }, { type: 'danger' }, { delay: 5000 });
                }
            }
        }
    };
    maskedMessageReply (chatRoomId) {
        this.hideOnmaskedReplyGenerated = true;
        localStorage.setItem("hideOnmaskedReplyGenerated", "true");
        this.disableReplySpan = true;
        let patientReplyData: any = {"chatroomId": chatRoomId};
        this._MaskedMessageService.maskMessagePatientReply(patientReplyData).then((result) => {
            const args = {type: 'messages', setCount: 0, chatroomId: result['chatroomId'], selfUpdate: true, maskedMessageReplyClick: true, maskedMsgBaseId: chatRoomId}
            this._sharedService.messagePollingSelfUpdate.emit({data: "Polling:true", args});
            let resp: any = result;
            if (resp['chatroomId']) {
                let activeMessageData: { [k: string]: any } = {};
                activeMessageData = {
                    baseChatroomId: chatRoomId,
                    chatWithUserId: '',
                    chatWith: '',
                    chatroomid: resp['chatroomId'],
                    chatwithUserRole: '',
                    chatwithDob: '',
                    message_group_id: 0
                };
                localStorage.setItem('activeMessage', JSON.stringify(activeMessageData));
                localStorage.setItem('targetId', resp['chatroomId']);
                localStorage.setItem('targetName', 'group-chat');
                this.resetPageAndLimit();
                if(this._structureService.currentUrlNow != '/inbox/chatroom') {
                    this.router.navigate(['/inbox/chatroom']);
                } else {
                    this._sharedService.reloadChatroomWithNewThread.emit();
                }
            } else {
                $.notify({ message: "Error while creating chat session." }, { type: 'danger' }, { delay: 5000 });
            }
        });
    }
    searchMessagesAndDocs() {
        this.searchInboxkeyword = $("#SearchTxt").val();
        if(this.searchInboxkeyword) {
            this.currentPage = 1;
            localStorage.setItem("messageInboxPagination",JSON.stringify(this.currentPage));
            localStorage.setItem('pageCountMessage', '1');
            this.pageCountMessage = 1;
            this.searchFlag = !!this.searchInboxkeyword;
            localStorage.setItem("searchInboxkeywordForPolling",this.searchInboxkeyword);
            this.activate();    
        }
    }
    resetSearch()
    {
        this.searchFlag = false;
        this.searchInboxkeyword = '';
        this._sharedService.reloadInbox = true;
        localStorage.setItem("searchInboxkeywordForPolling",this.searchInboxkeyword);
        localStorage.setItem("searchInboxkeywordInbox",this.searchInboxkeyword);
        this.currentPage = 1;
        localStorage.setItem("messageInboxPagination",JSON.stringify(this.currentPage));
        localStorage.setItem('pageCountMessage', '1');
        this.pageCountMessage = 1;
        $('#SearchTxt').val("");
        this.activate();
    }

    viewInventory(e, message) {
        message.unread = 0;
        message.unreadCount = 0;
        var inventoryId = message.inventoryId;
        var userId = message.userid;
        var activityData = {
            activityName: "Select Inventory",
            activityType: "inventory",
            activityDescription: this.userData.displayName + " has viewed inventory report(" + message.message + ") of " + message.patientName
        };
        this._structureService.trackActivity(activityData);
        let routingUrl = '/supplies/inventory-submissions/submission/' + message.userid + '/' + message.inventoryId + '/' + message.id;
        this.router.navigate([routingUrl]);
    }

    setTranslationMessages(message, translatedText, fromLang, toLang, chatRoomId, userId) {
        var fromText = '';
        var translated = '';
        if (typeof translatedText == 'object') {
            fromText = JSON.stringify(message);
            translated = JSON.stringify(translatedText);
        } else {
            fromText = message;
            translated = translatedText;
        }
        var data = { "message": fromText, "translation": translated, "fromLang": fromLang, "toLang": toLang, "chatRoomId": chatRoomId, "userId": userId };
        var translatedDataold = JSON.parse(localStorage.getItem('translatedData')) || [];
        translatedDataold.push(data);
        localStorage.setItem('translatedData', JSON.stringify(translatedDataold));
        var translatedDataold = JSON.parse(localStorage.getItem('translatedData')) || [];
    }
    htmlFromText(str) {
        str = str
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;')
            .replace(/`/g, '&#x60;')
            .replace(/(?:\r\n|\r|\n)/g, '\n')
            .replace(/(\n+)/g, '<div>$1</div>')
            .replace(/\n/g, '<br/>')
            .replace(/<br\/><\/div>/g, '</div>');
        return str;
    }
    translateMessages() {
        this.toggleTranslation = !this.toggleTranslation;
        if (this.newMessagedetails.length && ((parseInt(this.userData.config.toggle_chat_translation) && this.toggleTranslation) || (!parseInt(this.userData.config.toggle_chat_translation) && parseInt(this.userData.config.chat_auto_translate)))) {
            var messageToBeTranslated = "";
            let messageExistsToTranslate = false;
            for (let i in this.newMessagedetails) {
                if(parseInt(this.newMessagedetails[i].userid)) {
                    if(this.newMessagedetails[i].language && this.newMessagedetails[i].language.split('-')[0] != navigator.language.split('-')[0]) {
                        messageExistsToTranslate = true;
                    } else {
                        if(!this.newMessagedetails[i].language) {
                            messageExistsToTranslate = true;    
                        }
                    }
                }
                messageToBeTranslated += encodeURIComponent(this.newMessagedetails[i].msg) + "&q=";
            }
            if(messageExistsToTranslate) {
                this._ChatService.translateMessage(messageToBeTranslated.substring(0, messageToBeTranslated.length - 3), this.currentChatroomId).then((res)=> {
                    var resultResponse = JSON.parse(JSON.stringify(res));
                    var chatroomId = resultResponse.chatroomId;
                    resultResponse = resultResponse.content;
                    if (this.currentChatroomId == chatroomId) {
                        this.populateMessageList({content:this.newMessagedetails.reverse(), activity:(this.loadMoreButtonText? 1 : 0)}, resultResponse.data.translations, 0);
                    }
                });
            }
        }
    }
    loadMore(first,fromWhere = false) {
        var showChatHistory = false;
        if (this.configData.show_chat_history_to_new_participant && this.configData.show_chat_history_to_new_participant == '1') {
            showChatHistory = true;
        }
        //Fetch All messages from DB - Make Api Call
        this.messageFetching = true;
        this.loadMoreButtonText = "Loading..";
        var searchContent ="";
        if(fromWhere){
            searchContent = this.htmlFromText(this.search.nativeElement.value.trim());
        }
        this._ChatService.getChatroomMessages(this.currentChatroomId, showChatHistory, this.page, this.last,searchContent,this.messageFilter, this.flagFilterValue, this.priorityFilterValue, this.filteredTags, this.filteredMentions).then((data) => {
            if (data) {
                if (JSON.parse(JSON.stringify(data)).chatroomid == this.currentChatroomId) {
                    const result = JSON.parse(JSON.stringify(data));
                    this.selectedMessageData = result;
                    this.pageHeading = result.title;
                    this.admissionId = result.admissionId || '';
                    if(result && "appLessMode" in result && (result.appLessMode === true || result.appLessMode === false)) {
                        this.showAppLessMode = true;
                        this.INAPPButtonShown = result.appLessMode;
                    }
                    if(result.createdBy != this.userData.userId) {
                        this.recipient = true;
                    }
                    this.isPdgChat = !isBlank(result.isPdg) ? result.isPdg.toString() : '';
                    this.patientTagInfo = result && result.patientTagInfo &&  result.patientTagInfo.patientId ? result.patientTagInfo : {};
                    this.patientInfo = result && result.patient_data &&  result.patient_data.length ? result.patient_data : [];
                    if (first) {
                        if(result && "isPdg" in result && result.isPdg && result.isPdg == 1) {
                            this.inviteEnableButton = true;
                            this.isPdg = true;
                            if("patient_data" in result && result.patient_data && result.patient_data.length && result.patient_data[0] && "roleId" in result.patient_data[0] && result.patient_data[0].roleId == 3 && "userId" in result.patient_data[0] && result.patient_data[0].userId == this.userData.userId) {
                               this.myAssociatedPatient = result.patient_data[0];
                            }
                            if (this.userData.config.default_patients_workflow == 'alternate_contacts') {
                                if(("patient_data" in result && result.patient_data && result.patient_data.length && result.patient_data[0] && "roleId" in result.patient_data[0] && result.patient_data[0].roleId == 3) && !("roleName" in result.patient_data[0] && result.patient_data[0].roleName == 'Alternate Contact' && "userId" in result.patient_data[0] && result.patient_data[0].userId != this.userData.userId)) {
                                    this.myAssociatedPatient = result.patient_data[0]; 
                                }
                            } else {
                                if(("patient_data" in result && result.patient_data && result.patient_data.length && result.patient_data[0] && "roleId" in result.patient_data[0] && result.patient_data[0].roleId == 3) && !("userId" in result.patient_data[0] && result.patient_data[0].userId != this.userData.userId)) {
                                    this.myAssociatedPatient = result.patient_data[0]; 
                                }
                            }
                            this.pdgAssosiatedPatientArray = ("patient_data" in result && result.patient_data &&  result.patient_data.length) ? result.patient_data : [];
                            this.chatWithHeading = ("title" in result && result.title) ? result.title : '';
                            localStorage.setItem('chatWithHeading', (("title" in result && result.title) ? result.title : ''));
                        } 
                        else if(result && result.isPdg == 0 && result.messageGroupSites && this.multiSiteEnable) {
                            this.isMsgGrp = true;
                            this.msgGrpSites = new Array();
                            this.msgGrpSites = result.messageGroupSites.split(",");
                            this.msgGrpSites.forEach ((elements)=>{
                                this.patientSiteId.push(parseInt(elements)); 
                            });
                            this.chatroomPatientSiteId = 0;
                            this.chatroomSiteIds = result.messageGroupSites;
                            this.isSiteFilterDisabled = true;
                            this.tempChatroomPatientSiteId = this.chatroomPatientSiteId;
                          }
                        
                        else {
                            this.isMsgGrp = false;
                            this.isPdg = false;
                            this.pdgAssosiatedPatientArray = [];
                        }
                        if("patient_data" in result && result.patient_data && result.patient_data.length && result.patient_data[0] && "roleId" in result.patient_data[0] && result.patient_data[0].roleId == 3 && this.multiSiteEnable) {
                            this.chatroomPatientSiteId = result.patient_data[0].siteId;
                            this.patientSiteId.push(parseInt(result.patient_data[0].siteId));
                            this.isSiteFilterDisabled = true;
                            this.tempChatroomPatientSiteId = this.chatroomSiteIds = this.chatroomPatientSiteId;
                        }
                        this.newMessagedetails = [];
                        this.translatedMessagesDetails = [];
                        $(".cat__apps__chat-block").css({height:'100px'});
                    }
                    this.messageFetching = false;
                    this.populateMessageList(data, 0, 1);
                    if("baseId" in result && result.content.length) {
                        this.activeMessage.baseChatroomId = result.baseId;
                    }
                    if("messageGrpId" in result && result.messageGrpId && parseInt(result.messageGrpId) > 0) {
                        this.activeMessage.message_group_id = result.messageGrpId;
                        this.activeMessage.title = result.messageGrpTitle;
                    }
                    var currentChatroomId = result.chatroomid;
                   if (window.localStorage.getItem("chUserMessages")) {
                        var jsonS = window.localStorage.getItem("chUserMessages");
                        var obj = JSON.parse(jsonS);
                        for(var key=0;key<obj.Messages.length;key++)
                        {
                            var localdata=obj.Messages[key];
                            this.sendUserMessages(localdata,localdata.displayName, false, 0)

                        }
                     }
                     const deliverySqlData={
                        user_id: this.userData.userId,
                        chatroom_id: this.currentChatroomId,
                        read_time: new Date().getTime() / 1000,
                    }
                    this._inboxService.updateDeliveryStatus(deliverySqlData).then((data) => {
                        
                    })
                    /**
                     * Translate message when populating...
                     */
                    if (result.content.length && ((parseInt(this.userData.config.toggle_chat_translation) && this.toggleTranslation) || (!parseInt(this.userData.config.toggle_chat_translation) && parseInt(this.userData.config.chat_auto_translate)))) {
                        var messageToBeTranslated = "";
                        let messageExistsToTranslate = false;
                        for (let i in result.content) {
                            if(parseInt(result.content[i].userid)) {
                                if(result.content[i].language && result.content[i].language.split('-')[0] != navigator.language.split('-')[0]) {
                                    messageExistsToTranslate = true;
                                } else {
                                    if(!result.content[i].language) {
                                        messageExistsToTranslate = true;    
                                    }
                                }
                            }
                            messageToBeTranslated += encodeURIComponent(result.content[i].message) + "&q=";
                        }
                        var self = this;
                        if(messageExistsToTranslate) {
                        this._ChatService.translateMessage(messageToBeTranslated.substring(0, messageToBeTranslated.length - 3), currentChatroomId).then(function (res) {
                            var resultResponse = JSON.parse(JSON.stringify(res));
                            var chatroomId = resultResponse.chatroomId;
                            resultResponse = resultResponse.content;
                            if (self.currentChatroomId == chatroomId) {
                                var condition = 0;
                                if(first && self.search.nativeElement.value.trim()){
                                    condition = 1;
                                }
                                self.populateMessageList(result, resultResponse.data.translations.reverse(), condition);
                            }
                        }, function (errMsg) {
                            this.autoTranslateFailureCount++;
                            if (this.autoTranslateFailureCount < 2) {
                                this._structureService.notifyMessage({
                                    messge: 'Translation services are temporarily unavailable. This issue has been reported to the CitusHealth support team. We apologize for the inconvenience.'
                                });
                                var activityData = {
                                    activityName: "Failure Auto Translate",
                                    activityType: "manage messaging",
                                    activityDescription: "Chat auto translation failed - " + errMsg
                                };
                                this._structureService.trackActivity(activityData);
                                var updateServiceHealthData = {
                                    key: "callbell-auto-translate-service",
                                    status: "down",
                                    message: 'System is not operational'
                                };
                            }
                            if (this.first) {
                                var condition = false;
                                if(this.search.nativeElement.value.trim()){
                                    condition = true;
                                }
                                this.scrollBottom(condition);
                            }
                        });
                        }
                    }
                    /**
                     * End Of Translate message when populating...
                     */
                    this.page++;
                }
            } else {
                this.moreToLoad = false;
                this.admissionId = '';
            }
            if(this.callChatRoomUsers) {
                this.callChatRoomUsers = false;
                this.ChatRoomUsers();
            }
        }).catch(() => {
            this.admissionId = '';
        });
        if(this.searchMessage && searchContent){           
            var activityData = {
                activityName: "Search Messages",
                activityType: "manage messaging",
                activityDescription: this.userData.displayName + "(" + this.userData.userId + ") has searched with key: " + searchContent + " " + "in chatroom-" + this.currentChatroomId
            };
            this._structureService.trackActivity(activityData);
        } else if(this.filterMessage == 1 ){
            var messgekeys=[];
            messgekeys['1']="User Generated Messages";
            messgekeys['0']="System Generated messages";
            messgekeys['all']="All Messages";

            var activityData = {
                activityName: "Filter Messages",
                activityType: "manage messaging",
                activityDescription: this.userData.displayName + "(" + this.userData.userId + ") has filter with " +  messgekeys[this.messageFilter] + " " + "in chatroom-" + this.currentChatroomId
            };
            this._structureService.trackActivity(activityData);
        }
        
    }
    scrollBottom(condition =false) {
        if(this.newMessagedetails.length < 4 ){
            var stickThisHeight = $("#stickThis").height();
            var scrolToHeight   = stickThisHeight;            
        }else{
            scrolToHeight = 999999;
        }
       
        setTimeout(function () {
            if ($('.chatroom-message-listing .cat__apps__chat-block__item').length) {
                if(condition){
                    $('.chatroom-message-listing').animate({ scrollTop: 0 }, 'slow');
                    $(window).scrollTop(0);
                }else{
                    $('.chatroom-message-listing').animate({ scrollTop: scrolToHeight }, 'slow');
                    $(window).scrollTop(scrolToHeight);
                }
            }
        }, 1);
    }
    populateMessageList(message, translatedMessages, first) {
        var messages = message.content;
        if(messages.length){
        var messageLength;
        var ccLanguage = (navigator.language).split("-")[0];
        if (translatedMessages) {
            messageLength = translatedMessages.length;
        } else {
            messageLength = messages.length;
        }
        messageLength = messages.length;

        if (message.activity == 1 && message.content && messageLength >= 20) {
            this.loadMoreButtonText = "Load earlier messages";
        } else {
            this.loadMoreButtonText = "";
        }
        if (first) {
            var newMessagedetails = [];
        }
        if (translatedMessages) {
            messages = messages.reverse();
        }
        for (var key = 0; key < messageLength; key++) {
            var value = messages[key];
            var originalText = value.msg;
            if (translatedMessages) {
                this.translatedMessages = [];
                if (value.userid != this.userData.userId) {
                    if (this.translatedMessagesDetails[key] && this.translatedMessagesDetails[key].message) {
                        this.translatedMessagesDetails[key].message = translatedMessages[key].translatedText;
                    } else {
                        let data = {
                            "message": translatedMessages[key].translatedText,
                            "chatRoomId": this.currentChatroomId,
                            "userId": this.userData.userId
                        };
                        this.translatedMessagesDetails[key] = data;
                    }
                    this.sourceLanguage = translatedMessages[key].detectedSourceLanguage;
                    var targetIdTrans = localStorage.getItem('targetId');
                    this.setTranslationMessages(originalText, value.msg, this.sourceLanguage, ccLanguage, targetIdTrans, this.userData.userId);
                } else {
                    let data = {
                        "message": value.message,
                        "chatRoomId": this.currentChatroomId,
                        "userId": this.userData.userId
                    };
                    this.translatedMessagesDetails[key] = data;
                }
            } else {
                var userName, userClass, owner = false,sender_time,insertionStatus=0,sendontim;
                if (value.userid == this.userData.userId) {
                    userName = "Me";
                    userClass = "self";
                    if(value.sender_time){
                        sender_time=value.sender_time;
                        value.sent=sender_time;
                    }
                    if(value.insertionStatus)
                    {
                        insertionStatus=value.insertionStatus
                    }
                    owner = true;
                } else {
                    if(value.sender_time){
                        sendontim=value.sender_time;
                    }
                    else{
                        sendontim= value.sent;
                    }
                    userName = (value.message_sender_assoc_userid) ? value.message_sender_assoc_displayname + " (" + value.displayName + ")" : value.displayName;
                    userClass = "other";
                }
                var getitems = "";
                if (value.tag) {
                    getitems = value.tagedItems
                }
                var sign = value.sign != 'false' ? this._structureService.apiBaseUrl + "writable/filetransfer/uploads/" + value.sign : false;
                var readUsers = []
                var readUsers_length = 0;
                if(value.userid == this.userData.userId){
                    readUsers = value.readUsers;
                    if(readUsers !== null){
                        for (var ind = 0; ind < readUsers.length; ind++) {
                            if(readUsers[ind].userid == this.userData.userId ) {
                                readUsers.splice(ind, 1);
                            }
                        }
                        readUsers_length = readUsers.length;     
                    }
                }
                const mentionedUsers = value.mentionedUsers ? value.mentionedUsers : [];
                var details = {
                    "fileDownload": this.getFileFromMessage(value.message),                    
                    "id": value.id,
                    "name": userName,
                    "class": userClass,
                    "avatar": value.avatar,
                    "msg": value.message,
                    "message": value.message,
                    "time": value.sent,
                    "userid": value.userid,
                    "sign": sign,
                    "roleId":value.roleId,
                    "owner": owner,
                    "tag": value.tag,
                    "tagSign": value.tagSign,
                    "readUsers": readUsers,
                    "readUsers_length" :readUsers_length,
                    "patientFirstName": value.pfirstname,
                    "patientLastName": value.plastname,
                    "patientDob": value.pdob,
                    "patientDisplayName": value.pdisplayname,
                    "patientCaregiverDisplayName": value.pcaregiver_displayname,
                    "passwordStatus" : value.passwordStatus,
                    "patient" : value.patient,
                    getitems,
                    "msg_flag": value.msg_flag,
                    "prev_msg_flag": value.prev_msg_flag,
                    "msg_flag_data_id": value.msg_flag_data_id,
                    "language": value.language,
                    "insertionStatus":insertionStatus,
                    "deliveredUsers": [],
                    "msg_sent": sendontim,
                    "chatRoomId": this.currentChatroomId,
                    patientTagInfo: value.patientTagInfo,
                    "info_available":value.info_available,  
                    "failed":value.failed,
                    messageStatus : value.messageStatus,
                    tagedItems: value.tagedItems || [],
                    messageDeletedTime: value.messageDeletedTime,
                    priorityId: value.priorityId,
                    mentionedUsers,
                    msgDisplayName: value.displayName,
                    showMention: mentionedUsers.indexOf(Number(this.userData.userId)) !== -1,
                    messageType: value.messageType
                }
                this.newMessagedetails.unshift(details );
                let data = {
                    "message": value.message,
                    "chatRoomId": this.currentChatroomId,
                    "userId": this.userData.userId,
                    "msg_flag": value.msg_flag,
                    "prev_msg_flag": value.prev_msg_flag,
                    "msg_flag_data_id": value.msg_flag_data_id
                };
                if(value.msg_flag_data_id) {
                    this.messageFlagCount++;
                }
                this.translatedMessagesDetails.unshift(data);
                this.last = value.id;
            }
            this.translatedMessagesDetails = this.translatedMessagesDetails.slice();
        }
        var condition = false;
        if(first && this.search.nativeElement.value.trim()){
            condition = true;
        }
        this.scrollBottom(condition);
        setTimeout(() => {
            $('.overlay-hidden-scroll').hide();
        });
        this.searchMessage =0;
        this.filterMessage =0;
        this.noMessage =false;
    }else if(!this.newMessagedetails.length && this.search.nativeElement.value.trim()){
        this.loadMoreButtonText = "No Messages Available";
        this.noMessage =true;
        $(".cat__apps__chat-block").css({height:"100px"}); 
    }else if(!this.newMessagedetails.length){
        this.loadMoreButtonText = "No Messages Available";
        this.noMessage =true;
        $(".cat__apps__chat-block").css({height:"100px"}); 
    }else{
        this.loadMoreButtonText = "";
    }
    }
    ChatRoomUsers(setheading=false, invite = false, forward = false) {
        this.isForwardSiteSelected = false;
        this.isInviteSiteSelected = false;
        this.loadSiteFilter = false;
        this.isEnterToFrwrdSiteFun = false;
        if(!this.isPdg && !this.isMsgGrp && !this.patientInfo.length) {
            this.patientSiteId = [];
            this.tempChatroomPatientSiteId = '0';
            this.chatroomPatientSiteId = '0';
            this.isSiteFilterDisabled = false;
        }
        
        let chatwithCgiver = localStorage.getItem('setChatWithHeading');
        var roomUsers = [];
        let roomUsersBeforeInvite = [];
        let notificationToId = [];
        if (invite) {
            roomUsersBeforeInvite = this.chatroomUsersList.map((user) => user.userId);
        }
        this.chatroomUsersList = [];
        this.config = this._structureService.userDataConfig;
        this.configData = JSON.parse(this.config);
        this._ChatService.getUsersListByRoom(this.currentChatroomId,'notRejected',this._sharedService.chatroomSiteId, true).then((response:any) => {
            const data = response.data || [];
            let distinctArray = [];
            if(data) {
                this.allParticipants = data.allParticipants || [];
                this.chatParticipants = data.chatParticipants || [];
                this.roleParticipants = data.roleParticipants || [];
                this.allParticipants = this.modalFilter.transform(this.allParticipants);
                this.showOutOfOfficeMessage(this.allParticipants);
                this.handleMessageParticpantsData();
                distinctArray =  getDistinctArray(this.allParticipants, 'userId').map((user) => ({...user, deleted: +user.deleted})).filter((item) =>  !item.deleted );
                this.chatroomUsersCount = distinctArray.length;
                if (invite) {
                    notificationToId = distinctArray.reduce((userArray: any, user) => {
                        if (!roomUsersBeforeInvite.includes(user.userId)) {
                            userArray.push({
                                userid: user.userId,
                                roomid: this.currentChatroomId,
                                name: user.displayName
                            });
                        }
                        return userArray;
                    }, []);
                    if (notificationToId.length) {
                        this.invitedUserOnVideoCall = notificationToId;
                        this._structureService.socket.emit("userMessagetoServer", { data: this.invitedResponse.invitedMessage, insert: false, notificationToId: notificationToId, tenantId: this.userData.tenantId, tenantName: this.userData.tenantName });
                        /**Push notification */
                        if(this.userData.config.show_push_notification_for_chat_room_invite == '1' && this.userData.config.show_chat_history_to_new_participant && this.userData.config.show_chat_history_to_new_participant=='1'){
                            let pushMessage = "You have been added to a new chat session";
                            let activeMessage = JSON.parse(localStorage.getItem('activeMessage'));
                            let deeplinking:any = {
                                "state": "eventmenu.group-chat",
                                "stateParams": {
                                    targetID: this.currentChatroomId,
                                    targetName: "group-chat"
                                },
                                "activeMessage": {
                                    sent: activeMessage.sent,
                                    messageType: activeMessage.messageType||0,
                                    baseId: activeMessage.baseChatroomId||0,
                                    userid: activeMessage.userid,
                                    fromName: '"'+activeMessage.fromName+'"',
                                    message_group_id: activeMessage.message_group_id||0,
                                    createdby: activeMessage.createdby||0
                                },
                                "tenantId": this.userData.tenantId,
                                "tenantName": this.userData.tenantName
                            };
                            if(activeMessage.message_group_id && activeMessage.groupName) {
                                deeplinking.activeMessage.chatWithHeading = activeMessage.groupName;
                            }
                            if(activeMessage.selectedTenantId) {
                                deeplinking.activeMessage.selectedTenantId = activeMessage.selectedTenantId;
                            }
                            const eventId = activeMessage.id || (this.invitedResponse && this.invitedResponse.eventId) || "";
                            const userInviteNotificationData = {
                                sourceId: CONSTANTS.notificationSource.other,
                                sourceCategoryId: CONSTANTS.notificationSourceCategory.userInviteNotification
                            };
                            this._structureService.sentPushNotification(notificationToId, 0, pushMessage, '', deeplinking, '','',false, true, MessagePriority.NORMAL, eventId, userInviteNotificationData);
                        }
                    }
                }
                distinctArray.map((obj) => {
                    if (!isBlank(obj.mobile)) {
                        obj.mobile = obj.mobile.trim().replace(obj.country_code, '');
                    }
                    if (isBlank(obj.email)) {
                        obj.email = obj.username;
                    }
                    return obj;
                });
                distinctArray = this.modalFilter.transform(distinctArray);
                const receivers = distinctArray.filter((userArray: any, user) => userArray.userId !== this.userData.userId);
                this.receiverIsNotContactable = receivers.length === 1 && receivers[0].roleId === "3" && receivers[0].noContactAvailable;
            }
            

            //Add alert when pdg with only one participant
            if(+this.isPdgChat === 1 && distinctArray && distinctArray.length === 1 && distinctArray[0].userId === this.userData.userId) {
                this._structureService.showAlertMessagePopup({
                    showCancelButton: false,
                    title: '',
                    text: this._ToolTipService.getTranslateData('MESSAGES.NO_ACTIVE_PARTICIPANTS_IN_PDG')
                });        
            } 
            if(!isBlank(distinctArray[0].patient_transfer) && !isBlank(distinctArray[0].error) && +distinctArray[0].error === 3){
              this.disableChatroom = true;
              this.showNoUserInChatroomMsg = this._ToolTipService.getTranslateData('MESSAGES.CHAT_THREAD_CLOSED_NO_ACCESS_TO_SITE')
            }
           this.filteredMentionUsers = this.chatroomUsersList = distinctArray;            
           if (!isBlank(this.filteredMentionUsers[0])) {
            this.filteredMentionUsers[0].active = true
           }
           this.chatroomUsersListFull = getDistinctArray(data.allParticipants, 'userId').map((user) => ({...user, deleted: +user.deleted})).filter((item) => !item.deleted );
           this.chatDisable=false;
           var virtualUsersCount=0;
           var enrolledUsercount=0;
	    for (var i = 0; i < this.chatroomUsersListFull.length; i++) {                    
	                if (this.chatroomUsersListFull[i].roleId == 3 && this.chatroomUsersListFull[i].userId == this.userData.userId && !this.isPdg && !this.isMsgGrp) { 
	                    this.myAssociatedPatient = this.chatroomUsersListFull[i];    
                }
            //if room have virtual user exist  then disabling  the chat   
            if(this.chatroomUsersListFull[i].passwordStatus){
                enrolledUsercount++;
            } 
            if(this.chatroomUsersListFull[i].passwordStatus == false ) { 
                virtualUsersCount++;
            }  
            
	    }
        if(virtualUsersCount >0 && enrolledUsercount == 1 && this.applessMessaging == false ) {
            this.chatDisable=true;
            this.chatWithUserType="virtual";
            setTimeout(() => {
                 $(".emojionearea").addClass("disable_textarea");
                 $(".emojionearea-editor").attr("placeholder", "A Virtual(not enrolled) user can be sent a link to video chat but text message chatting is not supported for virtual users");
              },5000);
        } else {
            if($(".disable_textarea").length>0) {
                $(".emojionearea").removeClass("disable_textarea");
                $(".emojionearea-editor").attr("placeholder","Type here!");
            }
            this.chatWithUserType="enrolled";
        }
        //End of if room have virtual user exist then  disabling  the chat
        this.chatroomUsersList.forEach((users) => {
            if(users && users.country_code && users.country_code.indexOf('++') === 0) {
                users.country_code = users.country_code.replace('++', '+');
            }
            if(users && users.mobile && users.mobile.indexOf('++') === 0) {
                users.mobile = users.mobile.replace('++', '+');
            }
        });
        this.count = (this.chatroomUsersList.length - 2) ? " +" + (this.chatroomUsersList.length - 2) : "";
            let patientInitiated = null;
            if(this.hideOnmaskedReplyGenerated) {
                this.hideOnmaskedReplyGenerated = false;
                localStorage.setItem("hideOnmaskedReplyGenerated", "false");
            }
            for (var i = 0; i < this.chatroomUsersList.length; i++) {
                if(this.chatroomUsersList[i].userId == this.activeMessage.createdby) {
                    this.canReplyForMaskedMessage = true;
                }                
                if (chatwithCgiver == '1' && this.pageHeading.indexOf(this.chatroomUsersList[i].caregiver_displayname) == -1) {
                   
                } else {
                    this.caregiverTitle = '';
                }


                if (roomUsers.indexOf(this.chatroomUsersList[i].userId) === -1) {
                    roomUsers.push(this.chatroomUsersList[i].userId);
                    if (this.userData.config.default_patients_workflow == 'alternate_contacts') {
                        if (this.chatroomUsersList[i].roleId == 3 && this.chatroomUsersList[i].roleName == 'Alternate Contact' && this.chatroomUsersList[i].userId != this.userData.userId) {
                            patientInitiated = this.chatroomUsersList[i];
                        } else {
                            if (this.chatroomUsersList[i].roleId == 3) { 
                                this.myAssociatedPatient = this.chatroomUsersList[i];                            
                            }
                        }
                    } else{
                        if (this.chatroomUsersList[i].roleId == 3 && this.chatroomUsersList[i].userId != this.userData.userId) {
                            patientInitiated = this.chatroomUsersList[i];
                        } else {
                            if (this.chatroomUsersList[i].roleId == 3) { 
                                this.myAssociatedPatient = this.chatroomUsersList[i];                            
                            }
                        }
                    }
                }
              
            }
            if(this.activeMessage && this.activeMessage.title){
            this.msgSubEdit.patchValue({
               chatsubName: this.activeMessage.title
            });
            }
            if (roomUsers.indexOf(this.userData.userId) == -1 && !this.disableChatroom) {
                if (!this.activeMessage.archived) {
                    this._structureService.socket.emit("leaveChatRoom", this.currentChatroomId);
                    this.inboxData = this.restructureTags(this.inboxData.filter( (message)=> {
                        if(message.chatroomid != this.currentChatroomId) {
                           return true; 
                        }
                    }));
                    this._structureService.inboxData = this.inboxData;
                    if(this._structureService.currentUrlNow == '/inbox/chatroom') {
                    swal({
                        title: '',
                        text: this._ToolTipService.getTranslateData('MESSAGES.PDG_CHAT_MEMBER_ALERT_MESSSAGE'),
                        showCancelButton: false,
                        type: 'success',
                        confirmButtonText: 'Go to Inbox',
                        html: true,
                        confirmButtonClass: 'btn-success'
                      }, ()=>{
                        this.router.navigate(['/inbox']);
                      });
			document.addEventListener('keydown', (event) => {
                            if (event.keyCode === 27) {
                                this.router.navigate(['/inbox']);
                            }
                        }, { once: true });
                    }
                }
                this.isArchievedChatroom = true;
            } else {
                this.isArchievedChatroom = false;
            }

            this.checkVideoChatVariables();

            if(this.userData.config.default_patients_workflow != 'alternate_contacts' || (this.userData.group != '3')) {
                if (patientInitiated && this.activeMessage.messageType != 1) {
                    if((this.activeMessage.messageType == 2 && this.activeMessage.baseChatroomId == 0) || (this.activeMessage.message_group_id && this.activeMessage.message_group_id != 0)) {
                        return false;
                    }                    
                }
                if (localStorage.getItem('chatWithHeading') != '' && !patientInitiated && this.chatroomUsersList.length > 2 && ((this.activeMessage.message_group_id == '0' && this.activeMessage.messageType == "0") || (this.activeMessage.messageType == undefined && this.activeMessage.message_group_id == '0') || (this.pageHeading.indexOf(", ") != -1 && this.pageHeading.indexOf(" +") == -1 && ((this.activeMessage.messageType && this.activeMessage.messageType == "0") || this.activeMessage.messageType == undefined)))) {
                    this.flag = false;
                }
            }

            var chatroomvidoDetails = this._GlobalDataShareService.getVideoChatDetails();
            if((chatroomvidoDetails['initiator'] == null && chatroomvidoDetails['joinee']['roomId'] == this.currentChatroomId) || (chatroomvidoDetails['initiator']['roomId'] == this.currentChatroomId)){
                if(forward){
                   var frwdData = {userList:this.chatroomUsersList,roomId:this.frwdChatroomId};
                    this.forwardUserList.next(frwdData);
                } 
                if (invite && this._sharedService.onVideoChat ) {
                    this.chatroomUsersInvite.next(this.chatroomUsersList);
                }
                if(!invite && this._sharedService.onVideoChat){
                    var list = this.chatroomUsersList
                    this._sharedService.videoUserListUpdate.emit({list});
                }
            }
            if(!this.isPdg) {
              this.inviteEnableButton = true; 
            }
        }).catch(() => {
            this.inviteEnableButton = true;
        });
    }
    removeChatSignature(data) {
        if (data) {
            let removeSignData = {id: data.id}
            this._ChatService.uploadChatSignature(removeSignData, false, 'removeSignature').then(result => {
                var results: any = result;
                if (results.success) {
                    for (var i = 0; i < this.newMessagedetails.length; i++) {
                        if (this.newMessagedetails[i].id == results.content) {
                            this.newMessagedetails[i].sign = false;
                        }
                    }
                    this._structureService.socket.emit("removeSignature", {
                        'id': data.id, 'userId': this.userData.userId,
                        'chatroomId': this.currentChatroomId
                    });
                    var activityData = {
                        activityName: "Remove Sign Message",
                        activityType: "remove messaging",
                        activityDescription: this.userData.displayName + " Remove sign from message (" + data.id + ") in chat session " + this.currentChatroomId + "."
                    };
                    this._structureService.trackActivity(activityData);
                } else {
                    var notify = $.notify(results.content);
                    setTimeout(function () {
                        notify.update({ 'type': 'warning', 'message': results.content });
                    }, 1000);
                }
            }).catch((ex) => {
            });
        }
    }
    removeAttachment(selectedFile) {
        var self = this;
        swal({
            title: "Are you sure?",
            text: "Do you want to remove this attachment",
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            confirmButtonText: "Ok",
            closeOnConfirm: true
        },
            function (isConfirm) {
                if (isConfirm) {
                    var tempLength = self.selectedFiles.length;
                    var index = self.selectedFiles.indexOf(selectedFile);
                    if (index > -1)
                        self.selectedFiles.splice(index, 1);

                    if (tempLength <= 1) {
                        self.withAttachment = false;
                        self.selectedFiles = [];
                        $(".emojionearea-editor").attr("placeholder", "Type here!");
                        self.loadAttachment = false;
                    }
                }
            });
    }
    isValidFile = function (filename) {
        var ext = filename.split('.').pop();
        var allowedExt = ["png", "jpg", "jpeg", "jpe", "bmp", "pdf", "doc", "docx", "word", "xl", "xls", "xlsx"];
        if (allowedExt.indexOf(ext.toLowerCase()) >= 0) {
            return true;
        } else {
            this.withAttachment = false;
            return false;
        }
    };
    uploadFileConfirm(event) {

        if ($(".emojionearea-button").hasClass("active"))
            $(".emojionearea-button-close").click();

        let submittedUser = this.pageHeading;
        if (submittedUser.indexOf('DOB') != -1) {
            submittedUser = submittedUser.substring(0, submittedUser.indexOf('DOB'));
        }
        submittedUser = submittedUser ? submittedUser.replace(/Chat with/g, '') : this.pageHeading;
        this.loadAttachment = true;

        this.withAttachment = true;
        this.withAttachmentEvent = event;
        var allowed_extensions = new Array("png", "jpg", "jpeg", "jpe", "bmp", "pdf", "doc", "docx", "word", "xl", "xls", "xlsx");
        if (allowed_extensions.indexOf(event.target.files[0].name.split('.').pop().toLowerCase()) != -1) {

            let fileList: FileList = event.target.files;
            this.uploadData = fileList;
            var c = 0;
            if (this.selectedFiles) {
                c = this.selectedFiles.length;
            }
            var uploadSuccess = true;
            var msgErrType = '';
            var msgErrSize = '';
            for (var x in this.uploadData) {
                if (this.uploadData[x].name && this.uploadData[x].size) {
                    var a = { 'id': c, 'temp_id': c, 'details': this.uploadData[x], 'name': this.uploadData[x].name, 'size': this.uploadData[x].size, 'imageHtml': '' };
                    var fileValid = this.isValidFile(this.uploadData[x].name);
                    if (this.uploadData[x].size > 20971520) {
                        if (msgErrType) {
                            msgErrType = msgErrType + ", ";
                        }
                        msgErrType = msgErrType + this.uploadData[x].name;
                    } else if (fileValid == false) {
                        if (msgErrType) {
                            msgErrType = msgErrType + ", ";
                        }
                        msgErrType = msgErrType + this.uploadData[x].name;
                    } else {
                        this.selectedFiles.push(a);
                        c = c + 1;
                    }


                    if (parseInt(x) == (this.uploadData.length - 1)) {
                        if (msgErrSize != '') {
                            this.withAttachment = false;
                            this._structureService.notifyMessage({
                                messge: 'You have uploaded an large file'
                            });
                            uploadSuccess = false;
                        }
                        if (msgErrType != '') {
                            this.withAttachment = false;
                            this._structureService.notifyMessage({
                                messge: 'You have uploaded an invalid file type'
                            });
                            uploadSuccess = false;
                        }
                        if (c > 10) {
                            this.withAttachment = false;
                            this._structureService.notifyMessage({
                                messge: 'You have uploaded more than 10 file'
                            });
                            uploadSuccess = false;
                        }
                    }
                }
            }

            if (uploadSuccess == true) {
                this.asyncFunctionForImageHTML(0);

            } else {
                this.selectedFiles = [];
                this.withAttachment = false;
                $(".emojionearea-editor").attr("placeholder", "Type here!");
                this.loadAttachment = false;
            }

        } else {
            this._structureService.notifyMessage({
                messge: 'You have uploaded an invalid file type'
            });
            this.selectedFiles = [];
            this.withAttachment = false;
            $(".emojionearea-editor").attr("placeholder", "Type here!");
            this.loadAttachment = false;
        }

    }

    asyncFunctionForImageHTML(counter) {
        var self = this;
        if (counter === undefined)
            counter = 0;
        if (counter >= this.selectedFiles.length) {
            this.loadAttachment = false;
            $(".emojionearea-editor").attr("placeholder", "Add comment or Send");
            return;
        }

        if (this.selectedFiles[counter].details.size) {
            var audio_extensions = new Array("mp3", "mp2", "aac", "amr");
            var video_extensions = new Array("mpe", "avi", "mp4", "movie", "qt", "mov");
            if (audio_extensions.indexOf(this.selectedFiles[counter].name.split('.').pop().toLowerCase()) != -1) {
                var result = '<img src="../assets/img/audio.png" class="preview-av" >';
                this.selectedFiles[counter].imageHtml = result;
                result = "";
                counter++;
                this.asyncFunctionForImageHTML(counter);

            } else if (video_extensions.indexOf(this.selectedFiles[counter].name.split('.').pop().toLowerCase()) != -1) {
                var result = '<img src="../assets/img/video.png" class="preview-av" >';
                this.selectedFiles[counter].imageHtml = result;
                result = "";
                counter++;
                this.asyncFunctionForImageHTML(counter);

            } else {
                this._ChatService.fileFormatTypeTofileTag(this.selectedFiles[counter].details, false, false, false, false, function (result) {
                    self.selectedFiles[counter].imageHtml = result;
                    result = "";
                    counter++;
                    self.asyncFunctionForImageHTML(counter);
                });
            }
        }
    }
    loadPage(page: number){ 
        localStorage.setItem("messageInboxPagination",JSON.stringify(page));
            this.previousPage = page;
            this.currentPage = page;
            this.contentOffset = (page - 1) * this.contentLimit + 1;
            this.messageLoader.messages = false;
            this.pageCountMessage = page;
            localStorage.setItem('pageCountMessage', JSON.stringify(page));
            this._structureService.inboxDataCurrentPage = page;
            this.activate(false,false,false,page);      
    }
    resetInboxMarkAsRead(dataRes,loaderStatus=true) {   
        this.inboxData = this.restructureTags(dataRes);
        this.totalCount=this._structureService.inboxTotalMessageCount;
    }
    cmisMetadataSet(fileData, fileName, chatRoomId) {
        var data = JSON.stringify({
            "attributes": {
                "data": [{
                    "userId": this.userData.userCmisId,
                    "actualUserId": this.userData.userId,
                    "owner": this.userData.userCmisId,
                    "userType": "owner",
                    "objectType": "file",
                    "fileType": fileData.view,
                    "displayName": fileName,
                    "parentFolderId": 'nil',
                    "isDeleted": false,
                    "createdOn": new Date().getTime(),
                    "modifiedOn": new Date().getTime(),
                    "chatRoomId": chatRoomId,
                    "chatType": this.activeMessage.message_group_id ? 'groupChat' : 'normal',
                    "sourceType": 'attachment',
                }]
            }
        });
        return data;
    }
    uploadedFile(event) {
        this.withAttachment = false;
        this.uploadData = [];
        let selectedFiles = this.selectedFiles;
        this.selectedFiles = [];
        const messagePriority = this.messagePriority;
        const mentionedUsers = this.getMentionedUsers();
        const repliedTo = this.getRepliedTo();
        this.messagePriority = MessagePriority.NORMAL
        $(".emojionearea-editor").attr("placeholder", "Type here!");
        var editorText = this.formatMessage($('.emojionearea-editor').html());
        let messageText = this.formatMessage($('.emojionearea-editor').html() ? ($('.emojionearea-editor').html()).replace(/&nbsp;/gi, " ").trim() : '');
        $('.emojionearea-editor').html('');
        if ($(".emojionearea-button").hasClass("active"))
            $(".emojionearea-button-close").click();
        
        let deeplinking:any = {
            "state": "eventmenu.group-chat",
            "stateParams": {
                targetID: this.currentChatroomId,
                targetName: localStorage.getItem('targetName')
            },
            "priorityId": messagePriority,
            "activeMessage": {
                sent: new Date().getTime() / 1000,
                messageType: this.activeMessage.messageType||0,
                baseId: this.activeMessage.baseChatroomId||0,
                userid: this.userData.userId,
                fromName: '"'+this.userData.displayName+'"',
                message_group_id: this.activeMessage.message_group_id||0,
                createdby: this.activeMessage.createdby||0
            }
        };
        if(this.activeMessage.message_group_id && this.activeMessage.groupName) {
            deeplinking.activeMessage.chatWithHeading = this.activeMessage.groupName;
        }

        var allowed_extensions = new Array("png", "jpg", "jpeg", "jpe", "bmp", "pdf", "doc", "docx", "word", "xl", "xls", "xlsx");

        if (allowed_extensions.indexOf(selectedFiles[0].details.name.split('.').pop().toLowerCase()) != -1) {
            var fd = new FormData();
            let fileList: FileList = event.target.files;
            var socketIndex = 0;
            var self = this;
            var imagesHtml = "";
            let fileName = ""
            for (let x in selectedFiles) {
                this.uploadData = selectedFiles[x].details;
                var type = this.uploadData.type;
                fd.append("file", this.uploadData);
                fd.append("user", this.userData.userId);
                fd.append("chatRoomId", this.currentChatroomId);
                var result = selectedFiles[x].imageHtml;
                fileName = selectedFiles[x].name
                let newFileName = fileName
                self.showProgressbar();
                var userProfilePicUploadUrl = self._structureService.apiBaseUrl + 'citus-health/' + self._structureService.version + "/chatfile-upload.php";
                
                var promise = new Promise((resolve, reject) => {
                    $.ajax({
                        url: userProfilePicUploadUrl,
                        type: "POST",
                        data: fd,
                        processData: false, // tell jQuery not to process the data
                        contentType: false, // tell jQuery not to set contentType,
                        headers: {
                            "Authentication-Token": self.userData.authenticationToken
                        },
                        progress: function (e) {
                            if (e.lengthComputable) {
                                $('.progress-bar-animated').css('width', '' + (100 * e.loaded / e.total) + '%');
                            }
                            else {
                                console.warn('Content Length not reported!');
                            }
                        }
                    }).done((datas) => {
                        $('.progress-bar-animated').css('width', '100%');
                        var data = JSON.parse(datas);
                        let chatRoomId = data.chatRoomId;
                        var dataLen = data.length;
                        if (data.success) {
                            var metaData = self.cmisMetadataSet(data, newFileName, chatRoomId);
                            var selcterFileName=selectedFiles[x].name;
                            let sent = new Date().getTime() / 1000;
                            self._structureService.uploadFileToCmis(data.msg, metaData).subscribe((filedata) => {
                                let file: any = filedata;
                                file = JSON.parse(file.data.uploadFileToCmis.cmisFileData);
                                if (file.status == 200) {
                                    if (file.results.organizationId) {
                                        if (data.view == 'image') {
                                            var fileUrl = self.userData.cmisFileBaseUrl + file.results.attachment[0].attributes.fileshareId + '.json?type=thumbnail';
                                        } else if (data.view == 'document' || data.view == 'pdf') {
                                            var fileUrl = self.userData.cmisFileBaseUrl + file.results.attachment[0].attributes.fileshareId + '.json';
                                        } else {
                                            var fileUrl = self.userData.cmisFileBaseUrl + file.results.attachment[0].attributes.fileshareId + '.json';
                                        }
                                        var fileName = file.results.attributes.data[0].displayName.substring(0, file.results.attributes.data[0].displayName.lastIndexOf('.'));

                                        self._ChatService.fileFormatTypeTofileTag(selectedFiles[x].details, fileUrl, data.view, file.results.attributes.data[0].fileType, file.results.attributes.data[0].displayName, (resulttag, fileDatas) => {
                                            self.clearProgressbarscope();
                                            let resultData: any = resulttag;
                                            imagesHtml = imagesHtml ? imagesHtml + "<br>" + resultData : resultData;
                                            if (self._structureService.socket.connected && socketIndex == (selectedFiles.length - 1)) {
                                                var userMessagetoServerData: { [k: string]: any } = {};
                                                var pollingData = messageText = messageText ? imagesHtml + "<br><comment class='comment-attachment'><span>" + messageText + "</span></comment>" : imagesHtml;
                                                
                                                $('.emojionearea-editor').html("");
                                               
                                                userMessagetoServerData = {
                                                    id: messageCenterGUID( this.currentChatroomId, this.userData.userId ),
                                                    data: pollingData,
                                                    insert: true,
                                                    userId: self.userData.userId,
                                                    chatroomId: chatRoomId.toString(),
                                                    displayName: self.userData.displayName,
                                                    sentTimeCheck: new Date().getTime() / 1000,
                                                    baseId: self.activeMessage.baseChatroomId,
                                                    messageType: self.activeMessage.messageType,
                                                    doubleVerificationStatus: self.showDoubleVerificationStatus,
                                                    tenantId : self.userData.tenantId,
                                                    tenantName : self.userData.tenantName,
                                                    language: navigator.language,
                                                    priorityId: messagePriority,
                                                    mentionedUsers,
                                                    repliedTo
                                                }
                                                if(!isBlank(self.chatroomPatientSiteId)) {
                                                    userMessagetoServerData.chatroomPatientSiteId = self.chatroomPatientSiteId;                                                   
                                                }
                                                userMessagetoServerData.environment = self._structureService.environment;
                                                userMessagetoServerData.tenantId = self.userData.tenantId;
                                                userMessagetoServerData.serverBaseUrl = self._structureService.serverBaseUrl;
                                                userMessagetoServerData.apiVersion = self._structureService.version;

                                                if (self.userData.privileges.indexOf('messageEscalation') !== -1 || self.userData.privileges.indexOf('messageReminder') !== -1) {
                                                    if (!self.configData.message_escalation_behavior || ((self.configData.message_escalation_behavior === 'escalate_first' && !self.newMessagedetails.length) || (self.configData.message_escalation_behavior === 'escalate_all' && self.activeMessage.messageType != 2 && (self.activeMessage.baseChatroomId == '0' || !self.activeMessage.baseChatroomId)))) {//&& !self._inboxService.checkInfusionHours(true).isWorkingHours
                                                        userMessagetoServerData.escalationTime = self.userData.config.escalation_time * 1;
                                                    }
                                
                                                    userMessagetoServerData.scheduleData = self.userData.escalatedSchedulerData;
                                                    userMessagetoServerData.scheduleInterval = 30;
                                
                                                    if(self.userData.master_details && self.userData.masterEnabled == "1") {
                                                        userMessagetoServerData.masterEscalationTime = self.userData.master_config.escalation_time * 1;
                                                        userMessagetoServerData.masterScheduleData = self.userData.masterEscalatedSchedulerData;
                                                        userMessagetoServerData.masterScheduleInterval = 30;    
                                                    }
                                                }

                                                userMessagetoServerData.workingHour = self._inboxService.checkInfusionHours(false).isWorkingHours;//Escalation mail checking :: 
                                                userMessagetoServerData.allow24HourWorking = self.userData.siteConfigs.working_hour ? self.userData.siteConfigs.working_hour : 0;
                                                userMessagetoServerData.smsNotificationEnabled = self.userData.config.patient_message_sms_notifcation_beyond_branch_24hr ? self.userData.config.patient_message_sms_notifcation_beyond_branch_24hr : 0;
                                                userMessagetoServerData.patientReminderTime = self.userData.config.patient_reminder_time * 1;
                                                userMessagetoServerData.patientReminderTypes = self.userData.config.patient_reminder_types;
                                                userMessagetoServerData.messageReplyTimeout = self.userData.config.message_reply_timeout;
                                                userMessagetoServerData.caregiverDisplayname = self.userData.caregiver_displayname;
                                                userMessagetoServerData.caregiverUserid = self.userData.caregiver_userid;

                                                if(self.myAssociatedPatient) {
                                                    userMessagetoServerData.caregiverDisplayname = self.myAssociatedPatient.caregiver_displayname;
                                                    userMessagetoServerData.caregiverUserid = self.myAssociatedPatient.caregiver_userid;
                                                }

                                                if(self.userData.master_details && self.userData.masterEnabled == "1") {
                                                    userMessagetoServerData.masterEnabled = 1;
                                                    userMessagetoServerData.isMaster = 0;
                                                    userMessagetoServerData.masterTenantId = self.userData.master_details.id;
                                                } else if(!self.userData.master_details && self.userData.masterEnabled == "1") {
                                                    userMessagetoServerData.masterEnabled = 1;
                                                    userMessagetoServerData.isMaster = 1;
                                                } else if(self.userData.masterEnabled == "0") {
                                                    userMessagetoServerData.masterEnabled = 0;
                                                    userMessagetoServerData.isMaster = 0;
                                                }

                                                if(self.userData.masterEnabled == "1" && self.userData.config.flex_site_patients_can_chat_with_internal_staffs == "1" && self.userData.masterEnabled == "0") {
                                                    userMessagetoServerData.includeMasterStaffs = 1;
                                                } else {
                                                    userMessagetoServerData.includeMasterStaffs = 0;
                                                }

                                                userMessagetoServerData.messageConfig = {
                                                    messageEscalation: self.userData.privileges.indexOf('messageEscalation') !== -1,
                                                    messageReminder: self.userData.privileges.indexOf('messageReminder') !== -1,
                                                    userCitusRole: self.userData.group
                                                }

                                                if(self.userData.master_details && self.userData.masterEnabled == "1") {
                                                    userMessagetoServerData.masterWorkingHour =  self._inboxService.checkMasterBranchHours().isWorkingHours;
                                                    userMessagetoServerData.masterAllow24HourWorking = self.userData.master_config.working_hour ? self.userData.master_config.working_hour : 0;
                                                    userMessagetoServerData.masterStaffSmsNotificationEnabled = self.userData.master_config.staff_message_sms_notifcation ? self.userData.master_config.staff_message_sms_notifcation : 0;
                                                    userMessagetoServerData.masterMessageConfig = {
                                                        messageEscalation: self.userData.privileges.indexOf('messageEscalation')!==-1,
                                                        messageReminder: self.userData.privileges.indexOf('messageReminder')!==-1,
                                                        userCitusRole: self.userData.group
                                                    }
                                                }
                                                if(self.activeMessage.messageType == '2' && self.activeMessage.baseChatroomId == 0 && self.activeMessage.maskedSubCount) {
                                                    userMessagetoServerData.maskedMessageMainThreadMessageWithChild = true;
                                                }
                                                
                                                self._structureService.socket.emit("userMessagetoServer", userMessagetoServerData, (errorData)=> {
                                                    this._structureService.notifyMessage({
                                                        messge:'Warning!  ' + (errorData.failureReason == "notAbleToChatCategory" ? 'You have no privilege to view/send messages.' : 'This feature is not enabled for your tenant. Please contact support for more information'),
                                                        delay: 1000,
                                                        type: 'warning'
                                                    });
                                                    this.goToDefaultPage();
                                                });
                                                let article = 'a';
                                                if (data.view == "image") {
                                                    article = 'an';
                                                }
                                                const messageSendNotificationData = {
                                                    sourceId: CONSTANTS.notificationSource.message,
                                                    sourceCategoryId: CONSTANTS.notificationSourceCategory.messageSendNotification
                                                };
                                                self._structureService.sentPushNotification(chatRoomId, self.userData.userId, "You have a new message to review", '', '', '', self.showDoubleVerificationStatus, false, true, messagePriority, undefined, messageSendNotificationData);

                                                autosize.destroy(document.querySelectorAll('textarea'));
                                                $('.emojionearea-editor').html('');
                                                $(".emojionearea-editor :input").val('');
                                                self.scrollBottom();
                                                selectedFiles = [];
                                                self.withAttachment = false;
                                                var description='';
                                                if(data.view){
                                                  description =' has attached '+article+' ' + data.view+'('+data.msg+') in chatroom- '+data.chatRoomId;
                                                }
                                                else{
                                                  description +=' attachment ('+ selcterFileName +') failed in Chatrooom - '+chatRoomId
                                                }
                                                const activityData = {
                                                activityName: 'Chat Room Attachment',
                                                activityType: 'Messaging',
                                                activityDescription: self.userData.displayName + '('+self.userData.userId+')'+description
                                                    
                                                };
                                                self._structureService.trackActivity(activityData);
                                                if (!self.newMessagedetails.length) {
                                                    var actMessage = JSON.parse(localStorage.getItem('activeMessage'));
                                                    var profilePicByRole = (self.userData.group === '3' ? 'patient' : ((self.userData.group === '2' || self.userData.group === '6' || self.userData.group === '8') ? 'clinician-nurse-default' : 'clinician'));
                                                    var chatWithAvatar = self._structureService.apiBaseUrl + 'citus-health/avatars/profile-pic-' + profilePicByRole + '.png';
                                                    let splitData: any = actMessage.chatWith.split(",");
                                                    if (splitData.length) {
                                                        actMessage.chatWith = splitData[splitData.length - 1];
                                                    }                                                    
                                                    let initialMessage:any = {
                                                        "createdby": self.userData.userId,
                                                        "userid": self.userData.userId,
                                                        "ccmUserid": self.userData.userId,
                                                        "chatroomId": self.currentChatroomId,
                                                        "message": messageText,
                                                        "fromAvatar": self._structureService.getCookie('profileImageUrl'),
                                                        "chatAvatar": chatWithAvatar,
                                                        "fromName": self.userData.displayName,
                                                        "chatHeading": actMessage.chatWith ? actMessage.chatWith : self.userData.displayName,
                                                        "message_group_id": actMessage.message_group_id ? actMessage.message_group_id : "0",
                                                        "groupName": actMessage.message_group_name ? actMessage.message_group_name : '',
                                                        "chatSubHeading": (actMessage.chatWithRole ? actMessage.chatWithRole : (actMessage.chatwithUserRole? actMessage.chatwithUserRole : self._structureService.getCookie('userRole'))),
                                                        "role": self._structureService.getCookie('userRole'),
                                                        "unread": "0",
                                                        "sent":  sent+"",
                                                        "messageOrder": sent+"",
                                                        "siteName":actMessage.branch ? actMessage.branch : '',
                                                        "unreadCount": 0,
                                                        "messagesUnreadCount": 0,
                                                        "hasUnreadMessages": false,
                                                        "messageCategory": +actMessage.message_group_id ? this.messageCategory.MESSAGE_GROUP : actMessage.is_patient_discussion_group ? this.messageCategory.PDG : this.messageCategory.GENERAL,
                                                        "markAsRead": false,
                                                        "escalated": "0",
                                                        "pageHeading": localStorage.getItem('chatWithHeading'),
                                                        "forwardAction": null,
                                                        "forwardId": null,
                                                        "forwardName": null,
                                                        "dob": actMessage.chatwithDob ? actMessage.chatwithDob : '',
                                                        "title":actMessage.title,
                                                        "invited_status":0,
                                                        priorityId: messagePriority,
                                                        mentionedUsers,
                                                        repliedTo,
                                                        chatSubject: actMessage.title,
                                                        messageType: +actMessage.message_group_id ? this.messageType.MESSAGE_GROUP : actMessage.is_patient_discussion_group ? this.messageType.PDG : this.messageType.GENERAL,
                                                        isSelfMessage: true
                                                    };
                                                    if(actMessage.patient_caregiver_displayname) {
                                                        initialMessage.patient_caregiver_displayname = actMessage.patient_caregiver_displayname;
                                                    }
                                                    self.inboxData.unshift(initialMessage);
                                                    self.inboxData = self.inboxData.slice();
                                                    initialMessage.baseChatroomId = 0;
                                                }

                                                /** loop Inbox Data and update last Message */
                                                var inboxDataSelf = self;
                                                self.inboxData = self.inboxData.map((results)=> {
                                                    if(results.messageType == '2' && results.initiatedBaseId == '0' && self.activeMessage.baseChatroomId == results.chatroomId) {
                                                        results.hiddenChatRoomId = results.chatroomId;
                                                        results.initiatedBaseId = results.chatroomId;
                                                        results.chatroomId = self.activeMessage.chatroomId || self.activeMessage.chatroomid+"";
                                                        results.chatWithAvatar = results.fromAvatar;
                                                        results.dob = results.chatWithDob;
                                                        results.chatWithAvatar = self.userData.profileImageUrl;
                                                        results.chatWithDob = self.userData.dob;
                                                        results.createdby =self.userData.userId;
                                                        var fname = results.fname;
                                                        var lname = results.lname;
                                                        results.fname = results.f_fname;
                                                        results.lname = results.f_lname;
                                                        results.f_fname = fname;
                                                        results.f_lname = lname;
                                                        results.fromName = self.userData.displayName;
                                                        results.userid = self.userData.userid;
                                                        results.messagesCount = (parseInt(results.messagesCount)+1)+'';                                                        
                                                    }
                                                    if (results.chatroomId == inboxDataSelf.currentChatroomId) {
                                                        results.message = messageText;
                                                        results.sent = parseInt(sent + '')+'';
                                                        results.deliveryTime = results.sent;
                                                        results.messageOrder = results.sent;
                                                        if(results.maskedSent) {
                                                            results.maskedSent = parseInt(sent + '')+'';
                                                        }
                                                    }
                                                    this.inboxListScrollTop();                
                                                    return results;
                                                });
                                                                                              

                                            } else {
                                                $('.emojionearea-editor').html(editorText);
                                            }
                                            socketIndex++;
                                        });
                                    } else {
                                        $('.emojionearea-editor').html(editorText);
                                    }
                                } else {
                                    $('.emojionearea-editor').html(editorText);
                                }
                            });
                        } else {
                            $('.emojionearea-editor').html(editorText);
                        }
                    });
                });
            }
        } else {
            $('.emojionearea-editor').html(editorText);
            this._structureService.notifyMessage({
                messge: 'You have uploaded an invalid file type'
            });
        }
    }
    goToDefaultPage() {
        setTimeout(() => {
            this.router.navigate([this.userData.defaultPage || '/profile']);
        }, 2000);
    }
    showProgressbar() {
        this.scrollBottom();
        this.imageUploadProgress = true;
        this.userDisplayName = this.userData.displayName;
        this.profilePic = this.userData.profileImageUrl;
    }

    clearProgressbarscope() {
        this.imageUploadProgress = false;
        this.userDisplayName = '';
        this.profilePic = '';
    }
    triggerFileChange() {
        $('#myfile').val('');
        $('#myfile').trigger("click");
    }
    inviteModel() { 
        this.Invitealternates = '';
        this.alernateContactsData = [];
        this.isFilter = true;
        this.showInviteModal = true;
        this.fromModelPage = 'Invite';
        this.selectSiteId = '';
        this.tempPatientSiteId = this.inviteSiteId = this.patientSiteId;
        this.showSitefilter = true;
        if(this.tempPatientSiteId.length > 0) {
            this.dynamicFilterValue = true;
        }
        this.chatroomPatientSiteId = (!isBlank(this.tempChatroomPatientSiteId) && this.tempChatroomPatientSiteId != '0') ? this.tempChatroomPatientSiteId : (this.showForwardModal && this.inviteSiteId == '0' && this.chatroomPatientSiteId != '0') ? '0' :  (((isBlank(this.tempChatroomPatientSiteId) || this.tempChatroomPatientSiteId == '0') && this.inviteSiteId != '0' && !this.multiSiteEnable) || ((isBlank(this.tempChatroomPatientSiteId) || this.tempChatroomPatientSiteId == '0') && this.inviteSiteId != '0' && this.multiSiteEnable && this.loadSiteFilter))? this.inviteSiteId: this.chatroomPatientSiteId;
        this.showForwardModal = false;
        $('#inviteModal').modal('show');
        $('#invite-modal-search-box').val('');
        $('#invite-modal-search-box').focus();
        if(this.loadSiteFilter || (this.multiSiteEnable && this.tempPatientSiteId.length > 0 && !this.loadSiteFilter && (this.inviteSiteId != '0' || this.forwardSiteId != '0')) || ( this.multiSiteEnable && this.tempPatientSiteId.length == 0 && !this.loadSiteFilter) || (!this.loadSiteFilter && !this.multiSiteEnable)){
            this.getUsersOnInviteToChat('','',0,'staff');
        }
        var currentUsers=[];
        if (this.userData.config.default_patients_workflow == 'alternate_contacts') {
            this._ChatService.getUsersListByRoom(this.currentChatroomId, 'All',this.chatroomPatientSiteId).then((response: any) => {
                const data = response.data;
                if(data) {
                    if(data.allParticipants) {
                        this.chatroomUsersList = getDistinctArray(data.allParticipants, 'userId');
                    }                    
                }
            });
            this.chatroomUsersList.forEach(user => {
                currentUsers.push(user.userId);
                if (user.roleId == '3' && user.roleName=="Patient") {
                    this.Invitealternates= (user.userid || user.userId);
                } else if(user.roleId == '3' && user.roleName == 'Alternate Contact' && user.caregiver_userid && parseInt(user.caregiver_userid) > 0) {
                    this.Invitealternates = user.caregiver_userid;
                }
            });
            var request = {

                "tenantId": this.userData.tenantId,
                "patientId": this.Invitealternates ? this.Invitealternates : ''
            };
            this._structureService.getAlternateContactsAndPatientDataByPatientId(request).then((response: any) => {
                if(response.data && response.data.length)
                {                
                    response.data.forEach(e => {
                        if (currentUsers.indexOf(e.userId) === -1) {
                            this.alernateContactsData.push({
                                firstName: e.firstName,
                                lastName: e.lastName,
                                email: e.email,
                                displayName: e.firstName + ' ' + e.lastName,
                                userid: e.userId,
                                countryCode: e.countryCode,
                                password: e.password,
                                ESIValue: e.ESIValue,
                                isVirtual: e.isVirtual,
                                status: e.status,
                                contactId: e.userId,
                                patientFirstName: e.patientFirstName,
                                patientLastName: e.patientLastName,
                                patientDisplayName: e.patientFirstName + e.patientLastName,
                                relation: e.relation,
                                roleId: e.roleId,
                                roleName:e.roleName,
                                mobile: e.mobile,
                                noContactAvailable: e.contactId ? !e.password && !e.mobile && !e.email : !e.mobile && !e.email
                            });
                        }
                    })
                }
                this.showAlternattab = response.data && this.alernateContactsData.length ? true : false;
                
            })
           }
        }
        confirmAppLessModeUpdate(type) {
            var self = this;
            if(!self.recipient) {
                let  textType=(type == 1)? 'AppLess':'InApp';
                swal({
                    title: "Are you sure ?",
                    text: "You are going to change the messaging mode to "+textType,
                    type: "warning",
                    showCancelButton: true,
                    cancelButtonClass: "btn-default",
                    confirmButtonText: "Ok",
                    confirmButtonClass: "btn-warning",
                    cancelButtonText: "Cancel",
                    closeOnConfirm: true,
                    closeOnCancel: true
                },
                    function (isConfirm) {
                        if (isConfirm) {
                            self.appLessModeUpdate(type,true);
                        }else {
                            self.appLessModeUpdate(type,false);
                        }
                });
            }else {
                self._structureService.notifyMessage({
                    messge: 'You have no privilege to update AppLess mode.',
                    delay: 1000,
                    type: "danger"
                }); 
            }
        }
        appLessModeUpdate(type,confirm) {
            if(confirm){
                this.messageSettingsLoader=true;
                this.INAPPButtonShown = !this.INAPPButtonShown;
                let param={'appLessMode':type,'chatRoomId':this.currentChatroomId};
                this._inboxService.applessChatMode(param).then((result) => {
                    this.messageSettingsLoader=false;
                    let applessChatModeUpdateResponse:any = result;
                    if(applessChatModeUpdateResponse && "success" in applessChatModeUpdateResponse && type == 1 && applessChatModeUpdateResponse.success == false){
                        this.INAPPButtonShown = false;
                        $('#appless-message').prop("checked",true);
                        this._structureService.notifyMessage({
                            messge: 'The app less message mode update failed.',
                            delay: 1000,
                            type: "danger"
                        }); 
                    } else if(applessChatModeUpdateResponse && "success" in applessChatModeUpdateResponse  && type == 0 && applessChatModeUpdateResponse.success == false){
                        this.INAPPButtonShown = true;
                        this._structureService.notifyMessage({
                            messge: 'The app less message mode update failed.',
                            delay: 1000,
                            type: "danger"
                        }); 
                    }
                    if(!applessChatModeUpdateResponse || (applessChatModeUpdateResponse && !("success" in applessChatModeUpdateResponse))) {
                        this._structureService.notifyMessage({
                            messge: 'The app less message mode update failed.',
                            delay: 1000,
                            type: "danger"
                        });
                    }

                });
            }
        }
    inviteToChat(user, group) {
        let data = {};
        let invitedBy = this.userData.displayName;
        let userName = user.displayname;
        let userId = user.userId;
        let invitedById = this.userData.userId;
        let activeMessage = JSON.parse(localStorage.getItem('activeMessage')); 
        if (group) {         
            let msgGrpId = activeMessage.message_group_id;
            let selectedUserRoles = [];
            this.clinicalUserDetails.forEach((value, key) => {
                if (group.memberIds.split(',').indexOf(value.userid + '') > -1) {
                    let displayName = '';
                    if (value.caregiver_displayname) {
                        displayName = value.caregiver_displayname + " (" + value.displayname + ")";
                    } else {
                        displayName = value.displayname;
                    }
                    selectedUserRoles.push({ id: value.userid, name: displayName });
                }
            });
            data = `&chatroomId=${this.currentChatroomId}&multipleInvite=${JSON.stringify(selectedUserRoles)}&invitedBy=${invitedBy}&msgGrpId=${msgGrpId}&invitedById=${invitedById}`;        
            } else {            
            let inviteUserAssociatedId = user.caregiver_userid;
            let msgGrpId = activeMessage.message_group_id;
            data = `userId=${userId}
              &chatroomId=${this.currentChatroomId}
              &userName=${userName}
              &invitedBy=${invitedBy}
              &msgGrpId=${msgGrpId}
              &invitedById=${invitedById}
              &associatedId=${inviteUserAssociatedId}
              &addUserToGroup=${this.userData.config.add_user_to_group_on_invite_to_chat_session}`;

        }       
        $('#inviteModal').modal('hide');
        let msgtxt = this._ToolTipService.getTranslateDataWithParam('MESSAGES.INVITE_USER_TO_CHAT', {userName} );
        swal({
            title: "Are you sure?",
            text: msgtxt,
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            cancelButtonText: "Cancel",
            confirmButtonText: "Ok",
            closeOnConfirm: true
          },(confirm) => {
            if(confirm) {     
            this._ChatService.inviteUserToChatroom(data).then((result: any) => {
            this.invitedResponse = result.data;
            const eventId = activeMessage.id || (this.invitedResponse && this.invitedResponse.eventId) || "";

	    let inviteUserIds = this.invitedResponse.invitedUsersInfo ? this.invitedResponse.invitedUsersInfo.map(user => (parseInt(user.userid))) : [];
        let inviteRolesIds = this.invitedResponse.invitedRoles ? this.invitedResponse.invitedRoles.map(role => (+role.roleId)): [];
                let inviteUserApplessDataParams = {
                    chatroomId: this.currentChatroomId,
                    roomKey: this._sharedService.applessVideoChatroomDetails.roomKey,
                    roomPin: this._sharedService.applessVideoChatroomDetails.roomPin,
                    host: this._sharedService.applessVideoChatroomDetails.host,
                    roomName: this._sharedService.applessVideoChatroomDetails.roomName,
                    action: "invite",
                    videoId: this._sharedService.videoId,
                    inviteUsers: inviteUserIds,
                    inviteRoles: inviteRolesIds
                };
                this._structureService.sendApplessData(inviteUserApplessDataParams).then((response: any) => {
                    
                });
                const userInviteNotificationData = {
                    sourceId: CONSTANTS.notificationSource.other,
                    sourceCategoryId: CONSTANTS.notificationSourceCategory.userInviteNotification
                };
            if (group) {
                if (this.invitedResponse.inviteStatus === 1) {
                    this._structureService.socket.emit("userMessagetoServer", { data: this.invitedResponse.invitedMessage, insert: false, notificationToId: this.invitedResponse.invitedUsersInfo ? this.invitedResponse.invitedUsersInfo : [], tenantId: this.userData.tenantId, tenantName: this.userData.tenantName });
                    this.ChatRoomUsers();
                    this._structureService.notifyMessage({type: CONSTANTS.notificationTypes.success, messge: this.invitedResponse.invitedMessage});                    
                    const pushMessage = "You have been added to a new chat session";
                    let deeplinking:any = {
                        "state": "eventmenu.group-chat",
                        "stateParams": {
                            targetID: this.currentChatroomId,
                            targetName: "group-chat"
                        },
                        "activeMessage": {
                            sent: activeMessage.sent,
                            messageType: activeMessage.messageType||0,
                            baseId: activeMessage.baseChatroomId||0,
                            userid: activeMessage.userid,
                            fromName: '"'+activeMessage.fromName+'"',
                            message_group_id: activeMessage.message_group_id||0,
                            createdby: activeMessage.createdby||0
                        },
                        "tenantId": this.userData.tenantId,
                        "tenantName": this.userData.tenantName
                    };
                    if(activeMessage.message_group_id && activeMessage.groupName) {
                        deeplinking.activeMessage.chatWithHeading = activeMessage.groupName;
                    }
                    if(activeMessage.selectedTenantId) {
                        deeplinking.activeMessage.selectedTenantId = activeMessage.selectedTenantId;
                    }
                    if(this.userData.config.show_chat_history_to_new_participant && this.userData.config.show_chat_history_to_new_participant=='1'){
                        this._structureService.sentPushNotification(this.invitedResponse.invitedUsersInfo, 0, pushMessage, '', deeplinking, '','',false, true, MessagePriority.NORMAL, eventId, userInviteNotificationData);
                    }
                } 
                if(this.invitedResponse.inviteFailedStatus) {
                    this._structureService.notifyMessage({type: CONSTANTS.notificationTypes.warning, messge: this.invitedResponse.invitedMessage});
                }
                if ($('#inviteModal').hasClass('show')) {
                    $('#inviteModal').modal('hide');
                }
            } else {
                if (this.invitedResponse.inviteStatus === 1) {
                    this._structureService.socket.emit("userMessagetoServer", { data: this.invitedResponse.invitedMessage, insert: false, notificationToId: userId, tenantId: this.userData.tenantId, tenantName: this.userData.tenantName });
                    if ($('#inviteModal').hasClass('show')) {
                        $('#inviteModal').modal('hide');
                    }
                    this.ChatRoomUsers();
                    this._structureService.notifyMessage({type: CONSTANTS.notificationTypes.success, messge: this.invitedResponse.invitedMessage});
                    const pushMessage = "You have been added to a new chat session";
                    let deeplinking:any = {
                       "state": "eventmenu.group-chat",
                        "stateParams": {
                            targetID: this.currentChatroomId,
                            targetName: "group-chat"
                        },
                        "activeMessage": {
                            sent: activeMessage.sent,
                            messageType: activeMessage.messageType||0,
                            baseId: activeMessage.baseChatroomId||0,
                            userid: activeMessage.userid,
                            fromName: '"'+activeMessage.fromName+'"',
                            message_group_id: activeMessage.message_group_id||0,
                            createdby: activeMessage.createdby||0
                        },
                        "tenantId": this.userData.tenantId,
                        "tenantName": this.userData.tenantName
                    };
                    if(activeMessage.message_group_id && activeMessage.groupName) {
                        deeplinking.activeMessage.chatWithHeading = activeMessage.groupName;
                    }
                    if(activeMessage.selectedTenantId) {
                        deeplinking.activeMessage.selectedTenantId = activeMessage.selectedTenantId;
                    }
                    if(this.userData.config.show_push_notification_for_chat_room_invite == '1' && this.userData.config.show_chat_history_to_new_participant && this.userData.config.show_chat_history_to_new_participant=='1'){
                        this._structureService.sentPushNotification(userId, 0, pushMessage, '', deeplinking, '','',false, true, MessagePriority.NORMAL, eventId, userInviteNotificationData);
                    }
               
                   
                    var activityData = {
                        activityName: "Invite to Chat Session",
                        activityType: "messaging",
                        activityLinkageId: this.currentChatroomId,
                        activityDescription: invitedBy + " (" + invitedById + ") has" + " invited " + userId + " (" + userName + ")" + " to Chatroom ID - " + this.currentChatroomId
                    };
                    this._structureService.trackActivity(activityData);
                } 
                if(this.invitedResponse.inviteFailedStatus) {
                    if ($('#inviteModal').hasClass('show')) {
                        $('#inviteModal').modal('hide');
                    }
                    this._structureService.notifyMessage({type: CONSTANTS.notificationTypes.warning, messge: this.invitedResponse.invitedMessage});                    
                }
            }

        });
    }
    if(!confirm){
        swal.close();
        $('#inviteModal').modal('show');
        
    }
  })
    }
    get isMaskedChild(): boolean {
        return this.selectedMessageData && this.selectedMessageData.baseId && typeof +this.selectedMessageData.baseId === 'number' && +this.selectedMessageData.baseId !== 0;
      }
    get isGeneralMessageOrGroup(): boolean {
        return this.selectedMessageData && [MessageType.GENERAL, MessageType.PDG, MessageType.MESSAGE_GROUP].includes(+this.selectedMessageData.messageType);
    }
    get showInviteForwardButtons(): boolean {
        return this.archived === 'false' && this.isGeneralMessageOrGroup && !this.isMaskedChild;
    }
    get showInviteButton(): boolean {
        return this.privileges.indexOf('inviteToChat') !== -1 && this.showInviteForwardButtons;
    }
    get showForwardButton(): boolean {
        return this.privileges.indexOf('messageForwarding') !== -1 && this.showInviteForwardButtons;
    }
    updateInboxCounts(operationType, count=0) {
        if(operationType == 'add') {
            this._structureService.inboxUnreadMessageCount = this._structureService.inboxUnreadMessageCount + (count ? count : 1);
        } else if(operationType == 'sub') {
            this._structureService.inboxUnreadMessageCount = this._structureService.inboxUnreadMessageCount - (count ? count : 1);
        }
        if(!this._structureService.inboxUnreadMessageCount || this._structureService.inboxUnreadMessageCount < 0) {
            this._structureService.inboxUnreadMessageCount = 0;
        }
        var resCounts = {
            "messages":this._structureService.inboxUnreadMessageCount
        }
        this._sharedService.unreadCounts.emit({counts: resCounts, types: 'messages', trackRoot:{function: 'updateInboxCounts', page:'chatroom.citushealth.ts', lineNo: '5032'}});
    }
    getInboxCounts(types) {
        this._structureService.getInboxCounts(types).then((inboxCounts)=> { 
            var resCounts = JSON.parse(JSON.stringify(inboxCounts));
            this._structureService.inboxUnreadMessageCount = parseInt(resCounts.messages);
            if(!this._structureService.inboxUnreadMessageCount || this._structureService.inboxUnreadMessageCount < 0) {
                this._structureService.inboxUnreadMessageCount = 0;
            }
            this._sharedService.unreadCounts.emit({counts: resCounts, types: types, trackRoot:{function: 'getInboxCounts', page:'chatroom.citushealth.ts', lineNo: '5039'}});
        }, (err)=> {
            this._structureService.inboxUnreadMessageCount = 0;
        });
    }
    activateForNewChatroomPolling(pollingData) {
        this.messageService.getChatMessages({chatroomId: +pollingData.args.chatroomId}).subscribe((response:any) => {
            let data = [];
            const message = response && response.data && response.data.message ? response.data.message : [];
            if(message && message.chatroomId) {
                data.push(message);
                data = [...this.inboxData, ...data];
            }
            if(data.length) {                
                this.inboxData = this.restructureTags(data);
        		this._sharedService.onInboxData.emit({inboxData : this.inboxData, emitUpdateInboxData: true, onNewmessageRecieved:true});
                if(parseInt(message.unreadCount) > 0){
                this.updateInboxCounts('add',parseInt(message.unreadCount));
                }
            }
        })
    }
    activate(disableScroll=false, messagePolling:any = false, noloader=false,qlimit=0,page=0) {
        let searchKeyword = this.searchInboxkeyword ? this.searchInboxkeyword : '';
        localStorage.setItem("searchInboxkeywordForPolling",this.searchInboxkeyword);
        this.searchFlag = !!searchKeyword;
        if(!noloader) this.messageLoader.messages = true;
        const params : GetChatMessages = {
            searchKeyword: this.searchInboxkeyword,
            pageCount: this.pageCountMessage,
            fromInboxPagination: true,
            archived: 0
        }
        if (this.userId && this.searchFlag) {
            NProgress.start();
            const pageCountMessage = localStorage.getItem('pageCountMessage') ? parseInt(localStorage.getItem('pageCountMessage')) : 1;
            this.messageService.getChatMessages(params).subscribe((data) => {
                this.beforeLoad = true;
                let dataRes: any = data['data'].messages;
                if(pageCountMessage <= 1){
                    this.totalCount = data['data'].totalChatRoomsCount;
                    this._structureService.inboxTotalMessageCount = this.totalCount;
                    }
                this._structureService.inboxMessageResultsCount = dataRes.length;                
                this.inboxData = this.restructureTags(dataRes);
                this.searchFlag = false;
                this._sharedService.onInboxData.emit({inboxData : this.inboxData, emitUpdateInboxData: true,inboxTotalMessageCount: this._structureService.inboxTotalMessageCount});
                this.ChatRoomUsers();
                this.messageLoader.messages = false;
                NProgress.done();
            },() => {
                this.messageLoader.messages = false;
                NProgress.done();
            });
        } else {
        let pageCountMessage = +localStorage.getItem('pageCountMessage') ||  1;
            params.searchKeyword = '';
            NProgress.start();
            this.messageService.getChatMessages(params).subscribe((data) => {
                let dataRes: any = data['data'].messages;
                if(pageCountMessage <= 1){
                    this.totalCount = data['data'].totalChatRoomsCount;
                    this._structureService.inboxTotalMessageCount = this.totalCount;
                    }
                this._structureService.inboxMessageResultsCount = dataRes.length;

                if(messagePolling && messagePolling.args && messagePolling.args.setCount && messagePolling.args.setCount == 1) {
                    let types = 'messages';
                    this.getInboxCounts(types);
                } else if(messagePolling && messagePolling.args && messagePolling.args.setCount && messagePolling.args.setCount == 2 && messagePolling.args.chatroomId) {
                    if(this.currentChatroomId != messagePolling.args.chatroomId) {
                        this.updateInboxCounts('add')
                    }
                }
                this.inboxData = this.restructureTags(dataRes);
                if(pageCountMessage <= 1) {
                    this.inboxDataFirstPage =  this.inboxData;
                    this._structureService.inboxDataFirstPage = JSON.parse(JSON.stringify(this.inboxDataFirstPage));
                }
                this._structureService.inboxData = this.inboxData;
                this.messageLoader.messages = false;
                this._sharedService.onInboxData.emit({inboxData : this.inboxData, emitUpdateInboxData: true, onNewmessageRecieved:true, inboxTotalMessageCount: this._structureService.inboxTotalMessageCount});                
                if(!disableScroll){
                this.inboxListScrollTop();
                }
                NProgress.done();
            },() => {
                this.messageLoader.messages = false;
                NProgress.done();
            });
        }
    }
    tagMessageDivShow(event,mDetails){
        if(!$("#tag-button").is(':visible')) {
            $("#tag-button").show();//.css( {top:(event.pageY-cardHeader-topBar), left: (event.pageX-messagListWidth-469)});
            setTimeout(function () {
            $("#tag-checkbox-"+mDetails.id).prop("checked",true); 
           
            },500);
        }
        $('.available-message-tag-select2').prop('disabled', true);
        this.getAllTagsOnTagButtonClick();

        $("#message-icon-"+mDetails.id).addClass("selected");   
        $(".message-content-"+mDetails.id).addClass("tag-select-bg");  
    }
    getAllTagsOnTagButtonClick() {
        if(this.getAllTagsAPILoading) {
            if(this.activeMessage.selectedTenantId) {
                var patientTenantIdvalue = this.activeMessage.selectedTenantId ? this.activeMessage.selectedTenantId : this.userData.tenantId;
                this.existingTagstenantId = patientTenantIdvalue;
                this._inboxService.getTagoptions("1" ,true,patientTenantIdvalue, !this.allowIntegrationTags).then((result: any) => {
                    if (result && Array.isArray(result)) {
                        this.availableTags = result;
                        var self = this;                 
                        this.availableTags = this.availableTags.filter(function (availableTag) { 
                            return (((self.userData.group == '3') &&((availableTag.userType =='patient-facing') || (availableTag.userType =='staff-patient-facing') )) || ((self.userData.group != '3') && ((availableTag.userType =='staff-facing' )||(availableTag.userType =='staff-patient-facing') || (availableTag.userType ==null) )));
                        }).map((item) => {
                            return {...item, itemName: item.name};
                        });
                    }
                    $('.available-message-tag-select2').prop('disabled', false);
                    this.getAllTagsAPILoading = false;
                }).catch((ex) => {
                    $('.available-message-tag-select2').prop('disabled', false);
                    this.getAllTagsAPILoading = false;
                });
            } else {               
                this.existingTagstenantId = this._structureService.getCookie('crossTenantId') ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId;
                this._inboxService.getTagoptions("1" ,false, 0, !this.allowIntegrationTags).then((result: any) => {
                    if (result && Array.isArray(result)) {
                        this.availableTags = result;
                        var self = this;                 
                        this.availableTags = this.availableTags.filter(function (availableTag) { 
                            return (((self.userData.group == '3') &&((availableTag.userType =='patient-facing') || (availableTag.userType =='staff-patient-facing') )) || ((self.userData.group != '3') && ((availableTag.userType =='staff-facing' )||(availableTag.userType =='staff-patient-facing') || (availableTag.userType ==null) )));
                        }).map((item) => {
                            return {...item, itemName: item.name};
                        });
                    }
                    $('.available-message-tag-select2').prop('disabled', false);
                    this.getAllTagsAPILoading = false;
                }).catch((ex) => {
                    $('.available-message-tag-select2').prop('disabled', false);
                    this.getAllTagsAPILoading = false;
                });
            }
            if(this.existingTagstenantId != this.userData.tenantId ){
                if(this.activeMessage.branch){
                    this.existingTagstenantName = this.activeMessage.branch;
                }else{
                    this.crossTenantOptions.forEach((tenant) => {
                        if (tenant.id == this.existingTagstenantId) {
                            this.existingTagstenantName = tenant.tenantName;
                        }
                    });
                }
            } else{
                this.existingTagstenantName =this.userData.tenantName;
            }
        }
    }
    tagMultipleButtonClick(event, messageData: any) {
        this.disableButton = false;
        this.disablePatientSelectInTag = false;
        if(event.pageX != 0) {
            if(isBlank(messageData)) {
                this.getAllTagsAPILoading = true;
            }
            let associatedPatient = undefined;
            const message = messageData ? JSON.parse(JSON.stringify(messageData)) :  this.getEventDetails(event);
            const isPatientChat = !isBlank(this.pdgAssosiatedPatientArray) || !isBlank(this.patientInfo);
            this.tagMsgSelectedAdmissionId = '';
            if (isPatientChat) {
                associatedPatient = this.getAssociatedPatient();
            }
            if(messageData ==""||messageData==undefined) {
                messageData = this.getEventDetails(event);
                this.tagMultipleMessageOption =true;
            }
            if(!this.isMessageIconSelected(messageData.id)){
                this.tagMessageDivShow(event,messageData);
            }            
            let index = -1;
            const allowIntegrationTags = this.allowIntegrationTags;
            const patientIds = [];
            let patientTagInfo;
            this.taggedMessageDetails.forEach((value,key)=>{
                if(value.id == messageData.id){
                    index = key;
                }
                if (+value.patient > 0 && value.id != messageData.id) {
                    const patientId = this.getPatientIdFromPatientTagInfo(value.patientTagInfo);
                    if (!patientIds.includes(patientId)) {
                        patientIds.push(patientId);
                        patientTagInfo = value.patientTagInfo;
                    }
                }
            });
            if (+message.patient > 0 && index === -1) {
                const patientId = this.getPatientIdFromPatientTagInfo(message.patientTagInfo);
                if (!patientIds.includes(patientId) && !isBlank(patientId)) {
                    patientIds.push(patientId);
                    patientTagInfo = message.patientTagInfo;
                    if (patientTagInfo) {
                        associatedPatient = this.getAssociatedPatientArrayFromPatientData(patientTagInfo);
                    }
                }
            }
            if (patientTagInfo && !isPatientChat) {
                associatedPatient = this.getAssociatedPatientArrayFromPatientData(patientTagInfo);
            }
            this.allowIntegrationTags = isPatientChat || patientIds.length <= 1;
            if (allowIntegrationTags !== this.allowIntegrationTags) {
                this.getAllTagsAPILoading = true;
                this.getAllTagsOnTagButtonClick();
            }
            this.disablePatientSelectInTag = patientIds.length === 1 || isPatientChat;
            const resetTags = patientIds.length > 1;
            const resetPatient = patientIds.length !== 1 && !isPatientChat;
            if (resetPatient) {
                associatedPatient = undefined;
                this.resetPatientSelections();
            }
            if(index === -1) {
                this.userNotSelected = false;
                this.tagNotSelected = false;
                this.selectedAssosiatePatientId = false;
                if (isPatientChat || !resetPatient) {
                    this.assignSelectedPatient(associatedPatient);
                }
                this.addMessagForTag(messageData);
            }else{
                this.removeMessageForTag(index,messageData.id);
            }
            this.selectAssosiatePatient(this.selectedPatientId, this.selectedPatient);
            if (resetTags) {
               this.deselectAllTags();
            }
        }
        if(!this.getAllTagsAPILoading){
            $('.available-message-tag-select2').prop('disabled', false);
        }
    }
    private getEventDetails(event) {
        return JSON.parse($(event.target).attr("data-details"));
    }
    private isMessageIconSelected(id) {
        return $("#message-icon-" + id).hasClass('selected');
    }
    private resetPatientSelections() {
        this.selectedPatient = undefined;
        this.selectedPatientId = undefined;
        this.selectedAssosiatePatient = undefined;
        this.selectedAssosiatePatientName = '';
        this.selectedAssosiatePatientId = false;
        this.tagMsgSelectedAdmissionId = undefined;
    }
    private addMessagForTag(messageData) {
        this.tagMultipleMessageOption = true;
        setTimeout(() => {
            $("#messageTags").trigger("change");
            $("#users").trigger("change");
        }, 600);
        this.taggedMessageDetails.push(messageData);
    }
    private assignSelectedPatient(associatedPatient) {
        if (associatedPatient.userId || associatedPatient.associatedUserId) {
            this.selectedPatientId = associatedPatient.associatedUserId || associatedPatient.userId;
            this.selectedAssosiatePatientId = true;
            this.formatSelectedPatient(associatedPatient);
        }
    }
    private formatSelectedPatient(patientData) {
        if (patientData.formattedDisplayname) {
            this.selectedPatient = patientData.formattedDisplayname;
        } else {
            let dobFormatted = '';
            if (patientData.caregiver_dob) {
                dobFormatted = moment(patientData.caregiver_dob).format('MM/DD/YYYY');
            } else if (patientData.dob) {
                dobFormatted = moment(patientData.dob).format('MM/DD/YYYY');
            }
            const displayName = patientData.caregiver_displayname ? patientData.caregiver_displayname : patientData.displayName;
            this.selectedPatient = dobFormatted ? `${displayName} - ${dobFormatted}` : displayName;
            const mrnValue = !isBlank(patientData.IdentityValue) ? patientData.IdentityValue : patientData.caregiver_identityvalue;
            const mrn = !isBlank(mrnValue) ? `[${this._ToolTipService.getTranslateData('LABELS.MRN')}: ${mrnValue}]` : '';
            const passwordStatus = patientData.caregiver_displayname ? patientData.caregiver_passwordStatus : patientData.passwordStatus;
            const userStatus = [true, 'true'].includes(passwordStatus) ? 'LABELS.ENROLLED' : 'LABELS.VIRTUAL';
            this.selectedPatient += `${mrn} (${this._ToolTipService.getTranslateData(userStatus)})`;
        }
        this.patient_identity = patientData.patient_identity;
    }
    private removeMessageForTag(index, id) {
        this.taggedMessageDetails.splice(index, 1);
        setTimeout(() => {
            $("#tag-checkbox-" + id).prop("checked", false);
        });
        $("#message-icon-" + id).removeClass("selected");
        $(".message-content-" + id).removeClass("tag-select-bg");
        if (this.taggedMessageDetails.length === 0) {
            this.closeTagModal();
        }
    }
    private getAssociatedPatient() {
        let associatedPatient;
        const patientInfo = !isBlank(this.pdgAssosiatedPatientArray) ? this.pdgAssosiatedPatientArray : this.patientInfo;
        if (!isBlank(patientInfo)) {
            associatedPatient = patientInfo[0];
            this.setTagAdmissionIdFromPatientData(associatedPatient);
        }
        return associatedPatient;
    }
    private getAssociatedPatientArrayFromPatientData(patientTagInfo) {
        this.setTagAdmissionIdFromPatientData(patientTagInfo);
        return patientTagInfo.patient_data;
    }
    private getAdmissionIdFromPatientData(patientTagInfo) {
        return this.isMultiAdmissionsEnabled && patientTagInfo && !isBlank(patientTagInfo.admissionId) ? patientTagInfo.admissionId: '';
    }
    private getPatientIdFromPatientTagInfo(patientTagInfo) {
        return patientTagInfo && !isBlank(patientTagInfo.patientId) ? `${patientTagInfo.patientId}${this.getAdmissionIdFromPatientData(patientTagInfo)}` : '';
    }
    private setTagAdmissionIdFromPatientData(patientTagInfo) {
        this.tagMsgSelectedAdmissionId = this.getAdmissionIdFromPatientData(patientTagInfo);
    }
    private deselectAllTags() {
        this.tagTrackActivityText = 'updated';
        this.messageTagForm = this._formBuild.group({
            users: [this.selectedPatient],
            messageTags: ['']
        });
        setTimeout(() => {
            const selectOneTagLabel = this._ToolTipService.getTranslateData('LABELS.SELECT_ONE_OR_MORE_TAGS');
            const selectOnePatientLabel = this._ToolTipService.getTranslateData('VALIDATION_MESSAGES.SELECT_PATIENT');
            $("#messageTags").select2({ placeholder:  selectOneTagLabel });
            $("#users").select2({ placeholder: selectOnePatientLabel });
        }, 50);
    }
    closeTagModal(){
        $("#tag-button").hide();
        $("#msginfo-button").hide();
        $("#sender-msginfo-button").hide();
        $(".tag-checkbox").hide();
        this.availableTags = [];
        this.taggedMessageDetails=[];
        this.enableOrDisableUiLI(false,true);
        this.selectedAssosiatePatientName = '';
        this.selectedAssosiatePatient = '';
        this.selectedPatient='';
        this.selectedPatientId='';
        this.patientFace = false;
        this.tagMultipleMessageOption = false;
        this.clearAllRoleWiseUsers();
         this.clearSelectedRoles(false);
         $(".message-content").each(function(){
            $(this).removeClass('tag-select-bg');
        })
         $(".tag-message-icon").each(function(){
             $(this).removeClass('selected');
         });
         this.messageTagForm = this._formBuild.group({
            users: [''],
            messageTags: ['']
        }); 
        this.assosiatedPatients=[];
    }

    selectModal(category, message) {
       this.optionShow = 'staff';
        if(this.userData.group != 3) {
            this.chatWith = false;
            if (category === 'Chat With') {
                this.chatroomPatientSiteId = this.tempChatroomPatientSiteId ? this.tempChatroomPatientSiteId : '0';
                this.hasChatWithGroup = this.userData.group != 3;
                this.chatWith = true;
            } else if (category === 'Reroute to') {
                this.fromModelPage = 'Forward';
                this.showForwardModal = true;
                this.chatroomPatientSiteId = (!isBlank(this.tempChatroomPatientSiteId) && this.tempChatroomPatientSiteId != '0') ? this.tempChatroomPatientSiteId : (this.showInviteModal && this.forwardSiteId == '0' && this.chatroomPatientSiteId != '0') ? '0' :   (((isBlank(this.tempChatroomPatientSiteId) || this.tempChatroomPatientSiteId == '0') && this.forwardSiteId != '0' && !this.multiSiteEnable) || ((isBlank(this.tempChatroomPatientSiteId) || this.tempChatroomPatientSiteId == '0') && this.forwardSiteId != '0' && this.multiSiteEnable && this.loadSiteFilter)) ? this.forwardSiteId: this.chatroomPatientSiteId;
                this.showInviteModal = false;
                this.activeMessage = message;   
                this.activeMessage = JSON.parse(localStorage.getItem('activeMessage')); 
                this.isFilter = true;
                this.tempPatientSiteId =  this.patientSiteId;
                if(this.tempPatientSiteId.length > 0){
                this.dynamicFilterValue = true;}
                this.tabSelectedData = this.optionShow;
            } else {
                this.chatroomPatientSiteId = this.tempChatroomPatientSiteId ? this.tempChatroomPatientSiteId : '0';
                this.chatWith = true;
            }
            this.modalHead = category;
            $('#exampleModal').modal('show');
            $('#forward-modal-search-box').val('');
            $('#forward-modal-search-box').focus();
            this.selectedRowForward = -1;
            if (category === 'Reroute to' || category === 'Invite') {
                this.initialiseStaffOrPatientListOnClick(this.optionShow);
            } 
        } 
   }
    initialiseStaffOrPatientListOnClick(optionShow) {
        $('.reRouteBehaviour').attr('disabled','disabled');
        var isRoleAvailable = true;
        var clinicianRolesAvaiable = null;
        var setCliniciansRoleAvailableResponse;
        if (this.userData.group == 3) {
            this.setCliniciansRoleAvailableOnClick({isRoleAvailable:isRoleAvailable,clinicianRolesAvaiable:clinicianRolesAvaiable},true,false,optionShow,undefined).then( (res)=>{
                setCliniciansRoleAvailableResponse = res;
                isRoleAvailable = setCliniciansRoleAvailableResponse.isRoleAvailable;
                clinicianRolesAvaiable = setCliniciansRoleAvailableResponse.clinicianRolesAvaiable;
                if(isRoleAvailable) {
                    this.getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,true,false, optionShow,undefined)
                } else {
                    this.chatWithUsersLoading = false;
                }
            })
            /** ***********************End Section ****************** */

        } else {
            if (isRoleAvailable) {
                if(isRoleAvailable) {
                    this.getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,true,false, optionShow, undefined)
                } else {
                    this.chatWithUsersLoading = false;
                }
            }
        }
    }


    setRerouteUser(userdatas, index) {
        this.selectedRowForward = index;
        $('.reRouteBehaviour').removeAttr('disabled');
        this.reRoutedUser = userdatas;
        if (this.configData.message_forwarding_behaviour === 'user_preference') {
            $('.forwarding-behaviour-box').show();
            this.forwardingStatus = true;
        } else {
            $('.forwarding-behaviour-box').hide();
            this.forwardingStatus = false;
        }
    }
    setForwardBehaviour(forwardBehaviour) {
        this.selectedBehaviour = forwardBehaviour;
    }
    reRouteMessage() {
        let activeMessage = JSON.parse(localStorage.getItem('activeMessage'));
        if (this.forwardingStatus == true) {
            this.messageForwardingBehaviour = this.selectedBehaviour;
        } else {
            this.messageForwardingBehaviour = this.configData.message_forwarding_behaviour;
        }
        let fromId = (activeMessage.userid || activeMessage.from || activeMessage.createdby);
        const eventId = activeMessage.id || "";
        let fromName = activeMessage.fromName;
        let forwardedToName = this.reRoutedUser.displayname;
        let inviteUserAssociatedId = this.reRoutedUser.caregiver_userid;
        let forwardedToId = this.reRoutedUser.userId;
        let chatroomId = (localStorage.getItem('targetId') || activeMessage.chatroomId || activeMessage.chatroomid)
        let data = `toId=${forwardedToId}&message=${activeMessage.message}&chatroomId=${chatroomId}&rerouteBehaviour=${this.messageForwardingBehaviour}&associatedId=${inviteUserAssociatedId}`;
        $('#exampleModal').modal('hide');
        $('#inviteForwardModalSearch').val('');
        let msgtxt = `${this._ToolTipService.getTranslateDataWithParam('MESSAGES.CONFIRM_MESSAGE_FORWARD', {content: forwardedToName})}`;
        let IsEnterClicked = false;
        $(document).keypress(function(event){
            if (event.keyCode == 13) {
                IsEnterClicked = true;
            }
        });
        swal({
            title: "Are you sure?",
            text: msgtxt,
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            cancelButtonText: "Cancel",
            confirmButtonText: "Ok",
            closeOnConfirm: true,
            allowOutsideClick:true,
            },(confirm) => {
            if(IsEnterClicked){
                IsEnterClicked=false;
                swal.close();
                $('#exampleModal').modal('show');
                $('#forward-modal-search-box').val('');
                $('#forward-modal-search-box').focus();
                this.initialiseStaffOrPatientListOnClick(this.optionShow);
                this.selectedRowForward = -1;
                return false;
            }
            if(confirm) {
                const notify = $.notify(this._ToolTipService.getTranslateData('MESSAGES.FORWARDING_MESSAGE'));
                this._inboxService.rerouteMessage(data).then((responseData: any) => {
                    if (responseData.success) {
                        this.invitedUserOnVideoCall = [{name:this.reRoutedUser.displayname,userid:this.reRoutedUser.userId}];
                        const userForwardInitiatorNotificationData = {
                            sourceId: CONSTANTS.notificationSource.other,
                            sourceCategoryId: CONSTANTS.notificationSourceCategory.userForwardNotificationForInitiator
                        };
                        let deeplinking:any = { state: 'eventmenu.group-chat', stateParams: {targetID: chatroomId, targetName: 'group-chat' } };
                        this._structureService.sentPushNotification(fromId, 0, "Your chat session has been forwarded", '', deeplinking, '', '',false, true, MessagePriority.NORMAL, eventId, userForwardInitiatorNotificationData);
                        var pushMessage = "You have a new forwarded chat session";
                        if(this._sharedService.applessVideo){
                            let params = {
                                chatroomId:chatroomId,
                                roomKey:this._sharedService.applessVideoChatroomDetails.roomKey,
                                roomPin:this._sharedService.applessVideoChatroomDetails.roomPin,
                                host:this._sharedService.applessVideoChatroomDetails.host,
                                roomName:this._sharedService.applessVideoChatroomDetails.roomName,
                                action:"forwardChat",
                                videoId:this._sharedService.videoId,
                                forwardUser:parseInt(forwardedToId)
                            };
                            this._structureService.sendApplessData(params);
                        }
                        deeplinking = {
                            "state": "eventmenu.group-chat",
                            "stateParams": {
                                targetID: chatroomId,
                                targetName: "group-chat"
                            },
                            "activeMessage": {
                                sent: activeMessage.sent,
                                messageType: activeMessage.messageType||0,
                                baseId: activeMessage.baseChatroomId||0,
                                userid: fromId,
                                fromName: '"'+fromName+'"',
                                message_group_id: activeMessage.message_group_id||0,
                                createdby: activeMessage.createdby||0
                            },
                            "tenantId": this.userData.tenantId,
                            "tenantName": this.userData.tenantName
                        };
                        if(activeMessage.message_group_id && activeMessage.groupName) {
                            deeplinking.activeMessage.chatWithHeading = activeMessage.groupName;
                        }
                        if(activeMessage.selectedTenantId) {
                            deeplinking.activeMessage.selectedTenantId = activeMessage.selectedTenantId;
                        }
                        this.filterMessages('message');
                        const userForwardRecipientNotificationData = {
                            sourceId: CONSTANTS.notificationSource.other,
                            sourceCategoryId: CONSTANTS.notificationSourceCategory.userForwardNotificationForRecipient
                        };
                        this._structureService.sentPushNotification(forwardedToId, 0, pushMessage, '', deeplinking, '','',false, true, MessagePriority.NORMAL, eventId, userForwardRecipientNotificationData);
                        let forwardMessage = '{{fromName}} has forwarded this chat session to {{forwardedToName}}';
                        let forwardChatSession = forwardMessage.replace(/{{fromName}}/g, this.userData.displayName);
                        forwardChatSession = forwardChatSession.replace(/{{forwardedToName}}/g, forwardedToName);
                        this._structureService.socket.emit("userMessagetoServer", { data: forwardChatSession, insert: false, chatRoomId: chatroomId.toString(), notificationToId: forwardedToId, tenantId: this.userData.tenantId, tenantName: this.userData.tenantName, updateMessageFromUnreadCount: true });
                        let activityLogreRouteMessage = this.userData.displayName + "(" + this.userData.userId + ") has forwarded this chat session to " + forwardedToName + "(" + forwardedToId + ") in chatroom (" + (chatroomId) + ")";
                        let activityData = {
                            activityName: "Reroute Chat Session",
                            activityType: "messaging",
                            activityLinkageId: chatroomId,
                            activityDescription: activityLogreRouteMessage
                        };
                        this._structureService.trackActivity(activityData);
                        setTimeout(() => {
                            notify.update({ 'type': CONSTANTS.notificationTypes.success, 'message': `<strong>${this._ToolTipService.getTranslateData('SUCCESS_MESSAGES.MESSAGE_FORWARDED_SUCCESSFULLY')}</strong>` });
                        }, 1500);
                        let forwardBehaviour = '';
                        if (this.messageForwardingBehaviour == 'remove_forwarder') {
                            var message = '{{fromName}} has exited from this chat session.';
                            var removeForwarder = message.replace(/{{fromName}}/g, this.userData.displayName);
                            const chatroomId = activeMessage.chatroomId || activeMessage.chatroomid ||activeMessage.chatroom_id;
                            this._structureService.socket.emit("userMessagetoServer", { data: removeForwarder, insert: false, chatRoomId: chatroomId.toString(), notification: true, tenantId: this.userData.tenantId, tenantName: this.userData.tenantName });
                            let activityLogreRouteMessage = this.userData.displayName + "(" + this.userData.userId + ") has forwarded this chat session to " + forwardedToName + "(" + forwardedToId + ") in chatroom (" + (chatroomId) + ")" + forwardBehaviour;
                            let activityData = {
                                activityName: "Reroute Chat Session",
                                activityType: "messaging",
                                activityLinkageId: chatroomId,
                                activityDescription: activityLogreRouteMessage
                            };
                            this._structureService.trackActivity(activityData);
                            this.removeScopeMessageOnArchive(chatroomId);
                            forwardBehaviour = '. ' + this.userData.displayName + '(' + this.userData.userId + ') has been archived (exited) from this chat session also.';
                            this.router.navigate(['/inbox']);
                        } else {
                            this.frwdChatroomId = chatroomId;
                            this.ChatRoomUsers(false,false,true);
                            this.activeMessage.forwardAction = 'to';
                            this.activeMessage.forwardName = forwardedToName;
                        }      
                    } else {
                        const errorMsg = responseData.data && responseData.data.error && responseData.data.error.message ? responseData.data.error.message : this._ToolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG');
                        setTimeout(() => {
                            notify.update({ 'type': CONSTANTS.notificationTypes.warning, 'message': `<strong>${errorMsg}</strong>` });
                        }, NOTIFY_DELAY_TIME_COMMON);
                    }
                }).catch(() => {
                    setTimeout(() => {
                        notify.update({ 'type': CONSTANTS.notificationTypes.warning, 'message': `<strong>${this._ToolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG')}</strong>` });
                    }, NOTIFY_DELAY_TIME_COMMON);
                });

           
            }
            else{
            }
            if(!confirm){
                swal.close();
                $('#exampleModal').modal('show');
                $('#forward-modal-search-box').val('');
                $('#forward-modal-search-box').focus();
                this.initialiseStaffOrPatientListOnClick(this.optionShow);
                this.selectedRowForward = -1;                
            }
        })
        var yx = document.getElementsByClassName("btn-warning");
        yx[0].setAttribute("id", "frwrd_cnfrm_btn");
    }
    removeScopeMessageOnArchive(chatroomId) {
        let unreadCount = 0;
        if (Array.isArray(chatroomId)) {
            this.inboxData = this.restructureTags(this.inboxData.filter(msg=>{
                if(chatroomId.indexOf(msg.chatroomId) !== -1)
                unreadCount = unreadCount + (msg.messageType=='2' ? (parseInt(msg.maskedUnreadCount) ? parseInt(msg.maskedUnreadCount) : 0) : parseInt(msg.unreadCount || msg.unread));
                return chatroomId.indexOf(msg.chatroomId) === -1
            }));
        } else {
            this.inboxData = this.restructureTags(this.inboxData.filter(msg=>{
                if(+msg.chatroomId === +chatroomId)
                unreadCount = unreadCount + (msg.messageType=='2' ? (parseInt(msg.maskedUnreadCount) ? parseInt(msg.maskedUnreadCount) : 0) : parseInt(msg.unreadCount || msg.unread));
                return +msg.chatroomId !== +chatroomId
            }));
        }
        if(unreadCount) {
            if(parseInt(unreadCount.toString()) > 0) { 
                this.updateInboxCounts('sub', unreadCount);
            }
        }
        this._structureService.inboxData =this.inboxData;
        this._sharedService.onInboxData.emit({inboxData : this.inboxData, emitUpdateInboxData: false});
    }
    showRemoveConfirmation(user) {
        var activeMessage = JSON.parse(localStorage.getItem('activeMessage'));
        var removeUserData = { "userId": user.userId, "chatroomId": activeMessage.chatroomId || activeMessage.chatroomid};
        if(user.caregiver_userid){
            var displayname = user.caregiver_displayname+'('+user.displayName+')';
        }else{
             displayname = user.displayName;
        }
        const userToRemove = ((removeUserData.userId == this.userData.userId) ? 'yourself' : displayname + 
        (!user.caregiver_userid && user.dob && user.roleId == 3?' - '+this.datePipe.transform(user.dob, 'MM/dd/yyyy'):"")+(user.IdentityValue && user.roleId == 3?' [MRN: '+user.IdentityValue+']':"")+(user.caregiver_dob && user.roleId == 3?' - '+this.datePipe.transform(user.caregiver_dob, 'MM/dd/yyyy'):"")+(user.caregiver_identityvalue && user.roleId == 3?' [MRN: '+user.caregiver_identityvalue+']':""));
        this._structureService.showAlertMessagePopup({
            text: this._ToolTipService.getTranslateDataWithParam('MESSAGES.CONFIRM_USER_REMOVE_FROM_CHAT',  {userToRemove})
        }).then( 
            (isConfirm) => {
                if (isConfirm) {
                    this.httpService.doPost(APIs.removeChatRoomParticipants, removeUserData).subscribe((result: any) => {
                        if (result.success) {
                            const index =  this.chatroomUsersList.findIndex((item) => item.userId === user.userId);
                            this.chatroomUsersList.splice(index, 1);
                            const message = +user.userId === +this.userData.userId ? 'MESSAGES.CHATROOM_REMOVE_SELF_SUCCESS' : 'MESSAGES.CHATROOM_REMOVE_PARTICIPANT_SUCCESS';
                            this._structureService.notifyMessage({
                                messge: this._ToolTipService.getTranslateDataWithParam(message, {
                                    participantName: user.displayName,
                                    fromName: this.userData.displayName
                                }),
                                type: CONSTANTS.notificationTypes.success
                            });
                            this.ChatRoomUsers();
                            this.last = 0;
                            this.loadMore(true);// Call chat room message API to load the message room and to get updated chat title
                        } else {
                            const errorMsg = this._ToolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG');
                            this._structureService.notifyMessage({ messge: errorMsg });
                        }
                    });
                }
        });
    }
    replyToMessage(mDetails) {
        const container = document.createElement('div');

        //callout-content
        const calloutContent = document.createElement('div');
        calloutContent.classList.add('callout-content');
        calloutContent.contentEditable = "false";
        calloutContent.setAttribute('attr.data-target', mDetails.id);

        //callout
        const callout = document.createElement('div');
        callout.classList.add('callout', 'callout-default');
        
        //Name
        const name = document.createElement('small');
        name.innerHTML = mDetails.msgDisplayName;
        callout.appendChild(name);

        //time
        const time = document.createElement('small');
        time.classList.add('callout-time')
        time.innerHTML = moment(Number(mDetails.time) * 1000).format('MMM DD YYYY hh:mm A');
        callout.appendChild(time);

        //Message
        let msg = document.createElement('div');
        msg.classList.add('callout-message');
        msg.innerHTML = mDetails.message;
        msg = this.removeElementsByClass(msg, ['callout-content']);
        callout.appendChild(msg);

        //Close
        const closeButton = document.createElement('span');
        closeButton.setAttribute('id', 'remove-reply')
        closeButton.innerHTML = '<span onclick="event.target.parentNode.parentNode.parentNode.removeChild(event.target.parentNode.parentNode)" aria-hidden="true">&times;</span>';
        closeButton.classList.add('close'); 
        calloutContent.appendChild(callout)
        container.appendChild(calloutContent);
        callout.appendChild(closeButton);

        container.innerHTML = container.innerHTML + '&nbsp;';
        let tempDiv = document.createElement('div');
        tempDiv.innerHTML = $('.emojionearea-editor').html().replace(/\&nbsp;/g, '');
        tempDiv.innerHTML = tempDiv.innerHTML + container.innerHTML;
        $('.emojionearea-editor').html(tempDiv.innerHTML);
        setTimeout(() => {
            this.moveCursorToEnd();
        }, 100);
    }
    removeElementsByClass(containerElement, classNames) {
        classNames.forEach((className) => {
            let elements = containerElement.getElementsByClassName(className);
            while(elements.length > 0){
                elements[0].parentNode.removeChild(elements[0]);
            }
        });
        return containerElement;
    }
    openChatSignature(mDetails) {
        this.childpad.show_popup();
        var selectedMessage = mDetails;
        this.signMessageData = selectedMessage;
    }
    sendSignature(data) {
        if (data.signature) {
            this.isSignpad = true;
            if (data.close) {
                this.isSignpad = false;
            }
            if (this.isSignpad) {
                var self = this;
                var dataJson = {};
                var sigImg = data.signature;
                this.signMessageData.sign = data.signature;
                let uploadSignData = {
                    id : this.signMessageData.id,
                    sign : data.signature
                };
                this._inboxService.uploadChatSignature(uploadSignData, sigImg, 'uploadSignature').then(function (result) {
                    var res: any = result;
                    if (res.success) {
                        var userSignature = self._structureService.apiBaseUrl + "writable/filetransfer/uploads/" + res.content;
                        var index = self.newMessagedetails.indexOf(data);
                        if (index > -1) {
                            self.newMessagedetails[index].sign = userSignature;
                        }
                        var activityData = {
                            activityName: "Sign Message",
                            activityType: "messaging",
                            activityDescription: self.userData.displayName + " signed the message (" + self.signMessageData.id + ") in chat session " + self.currentChatroomId + "."
                        };
                        self._structureService.trackActivity(activityData);
                        self._structureService.socket.emit("signatureToUser", {
                            'id': self.signMessageData.id, 'sign': userSignature, 'userId': self.userData.userId,
                            'chatroomId': self.currentChatroomId
                        });
                    } else {
                        self._structureService.notifyMessage({
                            messge: res.content
                        });
                    }
                });
            }
        } else {
            if (!data.close) {
                this._structureService.notifyMessage({
                    messge: 'Draw your Sign.'
                });
            }
        }
    }
    submitText() {
        this.userNotSelected = false;
        this.admissionNotSelected = false;
        this.tagNotSelected = false;
        var self = this;
        let selectedTags = [];
        this.selectedTags = $('#messageTags').val();
        if(this.selectedTags && this.selectedTags !='' ){
            this.disableButton = true;          
        }
        var selectedUser = $("input#associate-search-input").val();
        this.selectedPatient = selectedUser;
        var selectedPatientName = this.selectedPatient.split('-')[0];
        if(this.patientFace && this.allowIntegrationTags) {
            if(!selectedUser){
                this.userNotSelected = true;
                this.disableButton = false;
                return;
            } else if(this.isMultiAdmissionsEnabled && selectedUser && !this.admissionId) {
                this.admissionNotSelected = true;
                this.disableButton = false;
                return;
            }
        }
        
        this.selectedUserForTag = this.selectedPatientId;
        var multiple = '';
        var autoApprove = true;
        var tagNames: any = [];
        if (this.selectedTags) {
            this.selectedTags.forEach(element => {
                var member = { id: "" };
                var id = element.substr(element.indexOf(":") + 1);
                id = id.replace(/'/g, "");
                member.id = id.replace(/\s/g, '');
                selectedTags.push(Number(member.id));
                multiple = multiple + member.id + ";";
                var tId = member.id;
                this.availableTags.forEach(res => {
                    if (res.id == tId) {
                        if (res.meta) {
                            var metaTag = JSON.parse(res.meta);
                            if (metaTag.approvalRequired) {
                                autoApprove = false;
                            }
                        }
                        var getItems = { "id": tId, "name": res.name, "patient": !this.patientFace ? 0 : this.selectedUserForTag, "fontColor": res.fontColor, "bgColor": res.bgColor, "typeName": res.typeName, metaData: res.meta };
                        tagNames.push(getItems);
                    }
                });
            });
        }
        if (this.selectedTags != null && this.selectedPatient != null && tagNames && tagNames.length > 0 ) {
            const allExistingTags = [];
            this.taggedMessageDetails.forEach((message) => {
                if(!isBlank(message.getitems)) {
                    const existingTags = tagNames.filter(a => {
                        const indexFound = message.getitems.findIndex(b => Number(b.id) === Number(a.id));
                        return indexFound > -1;
                    });
                    existingTags.forEach((item) => {
                        if (allExistingTags.findIndex(tag => tag.name == item.name) == -1) {
                            allExistingTags.push(item);
                        }
                    })
                }
            });
            if(allExistingTags.length > 0) {
                this._structureService.showAlertMessagePopup({
                    title: "",
                    text: this._ToolTipService.getTranslateDataWithParam('MESSAGES.TAGS_ALREADY_EXISTS', { tags: allExistingTags.map((tag) => tag.name).join(', ') }),
                    type: 'error'
                });
                this.disableButton = false;
            } else {
                this.processTagMessage(multiple, tagNames, autoApprove);
            }
        }
    }
    processTagMessage(multiple, tagNames, autoApprove) {
        let sendData: any = {};
        const admissionId = this.isMultiAdmissionsEnabled && this.patientFace && +tagNames[0].patient && this.admissionId ? this.admissionId : undefined;
        const msgTagIntegrationBody = {
            "message_tag_id": tagNames[0].id,
            "patient_id": tagNames[0].patient || undefined,
            admissionId,
            action: IntegrationType.ADD_MESSAGE_TAG,
        };
        this.messageService.checkMessageIntegrationStatus(msgTagIntegrationBody).subscribe((data) => {
            if (data.success) {
              this.onsubmitText(sendData, multiple, tagNames, autoApprove,this.patient_identity, admissionId);              
            }
        }, () => {
            const message = this._structureService.isMultiAdmissionsEnabled ?
            this._ToolTipService.getTranslateData('ERROR_MESSAGES.USER_NO_ACCESS_TO_PATIENT_SITE') : this._ToolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG');
            this._structureService.notifyMessage({ message });
        });
    }
    onsubmitText(sendData, multiple, tagNames, autoApprove,patientIdValue=this.userData.config.esi_code_for_patient_identity, admissionId?: string) {
        var message_ids = [];
        this.taggedMessageDetails.forEach((value) => {
            message_ids.push(value.id);
        })
        sendData.chatroomIds = [this.currentChatroomId];
        sendData.id = message_ids;
        sendData.multiple = multiple;
        sendData.single = !this.patientFace ? 0 : this.selectedUserForTag;
        sendData.admissionId = admissionId || undefined;
        if ((this.patientFace && sendData.single && sendData.multiple) || (!this.patientFace && sendData.multiple) || !this.allowIntegrationTags) {
            NProgress.start();
            this.disableButton = true;
            if (this.disableButton == true) {
                this.tagProcessingText = "Processing...";
            }
            this._inboxService.uploadChatTags(sendData, "addchattags", timezone.name(),patientIdValue).then((result) => {
                if (this.disableButton == true) {
                    this.tagProcessingText = "Still working...";
                }
                var self = this;
                var results: any = result;
                var notifyResult: any = result;
                if (results.success) {
                    var resultTagedItems = results.content;
                    if (resultTagedItems[1] && resultTagedItems[1]['defaultfilingCenter']) {
                        var activityData = {
                            activityName: "Tag Message Copied to Filing Center",
                            activityType: "Filing Center",
                            activityDescription: self.userData.displayName + " " + self.tagTrackActivityText + " tag for the messages (" + JSON.stringify(message_ids) + ") in chat session " + self.currentChatroomId + ". Moved to filing center named -" + resultTagedItems[1]['defaultfilingCenter'] + " and file named as " + resultTagedItems[2]['Filename']
                        };
                        self._structureService.trackActivity(activityData);
                    }
                    let filterArray = this.assosiatedPatients.find(x => +x.userId === +this.selectedPatientId);
                    if (this.patientFace && !filterArray && this.selectedPatientId) {
                        filterArray = {
                            userId: this.selectedPatientId,
                            listDisplayName: this.selectedPatient,
                        }
                    }
                    let patientTagInfo = undefined;
                    for (var i = 0; i < this.newMessagedetails.length; i++) {
                        if (message_ids.includes(this.newMessagedetails[i].id) && filterArray) {
                            this.newMessagedetails[i].patientFirstName = filterArray.firstname;
                            this.newMessagedetails[i].patientLastName = filterArray.lastname;
                            this.newMessagedetails[i].patientDob = filterArray.dob;
                            this.newMessagedetails[i].patientDisplayName = filterArray.displayname;
                            this.newMessagedetails[i].patientCaregiverDisplayName = filterArray.caregiver_displayname;
                            this.newMessagedetails[i].passwordStatus = filterArray.passwordStatus;
                            this.newMessagedetails[i].patient = filterArray.userId;
                            this.newMessagedetails[i].patientTagInfo = {
                                patientId: filterArray.userId,
                                admissionId: admissionId || undefined,
                                patient_data: {
                                    ...filterArray,
                                    formattedDisplayname: filterArray.listDisplayName
                                }
                            }
                            patientTagInfo = this.newMessagedetails[i].patientTagInfo;
                        }
                        if (message_ids.indexOf(this.newMessagedetails[i].id) > -1) {
                            this.newMessagedetails[i].tag = true;
                            this.newMessagedetails[i].getitems = this.newMessagedetails[i].getitems ? [...this.newMessagedetails[i].getitems, ...tagNames] : tagNames;
                            this.newMessagedetails[i].tagSign = (autoApprove) ? "true" : "false";
                        }
                    }
                    this._structureService.socket.emit("updateMessageTags", { patientTagInfo, autoApprove: autoApprove, tagNames, messageIds: message_ids, assosiatedPatient: filterArray, currentChatroomId: self.currentChatroomId, updatedBy: self.userData.userId });
                    this.closeTagModal();
                    var activityData = {
                        activityName: "Tag Message",
                        activityType: "forms",
                        activityDescription: self.userData.displayName + " " + self.tagTrackActivityText + " tag for the message (" + JSON.stringify(message_ids) + ") in chat session " + self.currentChatroomId + "."
                    };
                    self._structureService.trackActivity(activityData);

                    self.approver = 'the approver';
                    var formApproverUserid = [];
                    var formApproverUseridDisplayName = [];
                    var formApprover = [];
                    if (self.configData.form_approver_clinician && self.configData.form_approver_clinician != "") {
                        var formApproverData = self.configData.form_approver_clinician.split(",");
                        formApproverData.forEach(value => {
                            var userIds = { userid: value };
                            formApproverUserid.push(userIds);
                            formApproverUseridDisplayName.push(value);
                        });
                        self.formApproverUsersListSelection.filter(function (userData) {
                            if (formApproverUseridDisplayName.indexOf(userData.userid) != -1) {
                                formApprover.push(userData.displayname);
                            }
                        });
                        self.approver = formApprover.toString();
                    }
                    NProgress.done();
                    if (self.userDataConfig.config.enable_progress_note_integration == 1 && notifyResult.enableIntegration == true && ((notifyResult.triggerOn == "add-tag") || (notifyResult.triggerOn == true && notifyResult.approvalRequired != true))) {
                        if (notifyResult.identity_value_Patient == "" || notifyResult.identity_value_Patient == null
                            || notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null || notifyResult.integrationFC == ''
                            || notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null) {
                            var messageData = '';
                            var messageData1 = '';
                            if (notifyResult.integrationFC == '' || notifyResult.integrationFC == null) {
                                messageData1 += "there is no folder selection in Default Outgoing Filing Center for Integration";
                            }
                            if (notifyResult.identity_value_Patient == '' || notifyResult.identity_value_Patient == null) {
                                messageData += ", MRN ";
                                if (notifyResult.patient_name && notifyResult.patient_name != "") {
                                    messageData += " of " + notifyResult.patient_name;
                                }
                            }
                            let f1 = 0;
                            let f2 = 0;
                            if (notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null) {
                                f1 = 1;
                                messageData += ", USERNO";
                            }
                            if (notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null) {
                                f2 = 1;
                                messageData += ", USERNAME";
                            }
                            if ((f1 == 1 || f2 == 1) && notifyResult.staff_name && notifyResult.staff_name != "") {
                                messageData += " of " + notifyResult.staff_name;
                            }
                            var removeComa = messageData.charAt(0);
                            if (removeComa == ',') {
                                messageData = messageData.substring(1);
                            }
                            if (messageData1) {
                                var finalMessage = 'Generating Progress Note failed due to missing (' + messageData1 + ')';
                            }
                            else if (messageData) {
                                var finalMessage = 'Generating Progress Note failed due to missing (' + messageData + ')';
                            }
                            else {
                                var finalMessage = '';
                            }
                            if (finalMessage) {
                                activityData.activityDescription = self.userData.displayName + " " + self.tagTrackActivityText + " tag for the messages (" + JSON.stringify(message_ids) + ") in chat session " + self.currentChatroomId + ".not moved to filing center because -" + finalMessage;
                                self._structureService.trackActivity(activityData);
                            }
                        }
                    }
                    if (!autoApprove) {
                        let tagMessageSuccess = 'Your message has been tagged and sent to ' + self.approver + ' for approval';
                        self._structureService.notifyMessage({
                            messge: tagMessageSuccess,
                            type: 'success'
                        });

                        if (formApproverUserid.length) {
                            self._structureService.sentPushNotification(formApproverUserid, 0, 'You have a new message for approval', '', '', '', '');
                        } else {
                            self._structureService.sentPushNotification(0, 0, 'You have a new message for approval', 'formApprover', '', '', '');
                        }
                    } else {
                        let tagMessageSuccess = 'Your message has been tagged and auto approved';
                        self._structureService.notifyMessage({
                            messge: tagMessageSuccess,
                            type: 'success'
                        });
                    }
                } else {
                    self._structureService.notifyMessage({
                        messge: results.content
                    });
                }

            }).catch((error) => {
                NProgress.done();
                this.disableButton = false;
                this.tagProcessingText = '';
                const errorMsg = error && error.data.errors  && error.data.errors.length && error.data.errors[0].message
                ? error.data.errors[0].message: this._ToolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG');
                this._structureService.notifyMessage({ message: errorMsg });
            });
            
        } else {
            var message = "Select Values";
            if (!sendData.single && this.patientFace) {
                this.tagNotSelected = false;
                this.userNotSelected = true;
                message = "Select a Patient";
            }
            else {
                this.userNotSelected = false;
                this.tagNotSelected = true;
                message = "Select one or more Tags";
            }
        }
    }
    /** End Section */
    removeMessageTags(tagId: Number, messageId: any) {
        const index = this.newMessagedetails.findIndex((message) => Number(message.id) === Number(messageId));
        if (index > -1) {
            this.newMessagedetails[index].getitems = this.newMessagedetails[index].getitems.filter(tag => Number(tag.id) !== Number(tagId));
            this.removePatientAssociationFromMessage(index);
            this._structureService.socket.emit("updateMessageTags", { patientTagInfo: this.newMessagedetails[index].patientTagInfo, autoApprove: false, tagNames: [], removedTagId: tagId, messageIds: [messageId], currentChatroomId: this.currentChatroomId, updatedBy: this.userData.userId });
        }
    }
    private removePatientAssociationFromMessage(index: number) {
        const patientTags = this.newMessagedetails[index].getitems.filter(tag => !isBlank(tag.patient));
        this.newMessagedetails[index].tagedItems = patientTags;
        if (isBlank(patientTags)) {
            this.newMessagedetails[index].patient = '';
            this.newMessagedetails[index].patientTagInfo = undefined;
        }
    }
    replyForGroupChatSubList(e, chatroomId, message) {
        this.pdgAssosiatedPatientArray = [];
        this.patientTagInfo = [];
        let setChatWithHeading: any = 0;
        if (message.chatWithRole == 'Caregiver') {
            setChatWithHeading = 1;
        }
        localStorage.setItem('setChatWithHeading', setChatWithHeading);

        let unreadMessageStatus = message.unread;
        if (parseInt(message.unread) || parseInt(message.unreadCount)) {
            this._sharedService.readInboxData.emit(message);
        }

        this.activeMessage = message; //Forwad option from chat window.
        var chatWithDob = "";
        var fromDob = "";
        var chatwithDay = new Date(message.chatWithDob).getDay();
        var messageDay = new Date(message.dob).getDay();
        if (message.chatWithDob && !isNaN(chatwithDay) && message.chatWithRole != 'Caregiver') {
            chatWithDob = " DOB " + this.datePipe.transform(message.chatWithDob, 'MM/dd/yy');
        }
        if (message.dob && !isNaN(messageDay) && message.chatWithRole != 'Caregiver') {
            fromDob = " DOB " + this.datePipe.transform(message.dob, 'MM/dd/yy');
        }
        if (message.chatWithRole == 'Caregiver' || message.role == 'Caregiver') {
            fromDob = "";
            chatWithDob = "";
        }
        var chatwith = this.userData.userId === message.userid ? (message.chatWithRoleId === "3" ? message.chatWith + chatWithDob : message.chatWith + ", " + message.chatWithRole) : (message.grp === "3" ? message.fromName + fromDob : message.fromName + ", " + message.role);
        this.chatWithHeading = parseInt(message.message_group_id) && message.groupName ? message.groupName : (parseInt(message.messageType) ? "Message from " + ((message.userid !== this.userData.userId) ? message.fromName : "Me") : "Chat with " + chatwith);
        var targetID = chatroomId;
        if (!message.isSelfMessage && message.isPatientInitiatedChat && message.messageUnreadCount) {
            var pushMessage = 'Your message is getting reviewed';
            let sentTime = message.messageOrder;
            this._structureService.socket.emit("confirmReviewPushNotification", { chatRoomId: targetID.toString(), messageUserId: message.userid || message.chatCreatedBy }, (data, type) => {
                if (type && data < sentTime) {
                    let deeplinking:any = {
                        state: "eventmenu.group-chat",
                        stateParams: {
                            targetID: chatroomId,
                            targetName: "group-chat"
                        },
                        activeMessage: {
                            sent: message.sent,
                            messageType: message.messageType || 0,
                            baseId: message.baseChatroomId || 0,
                            userid: message.userid || message.chatCreatedBy,
                            fromName: `"${message.chatHeading}"`,
                            message_group_id: message.message_group_id || 0,
                            createdby: message.createdby || 0
                        }
                    };
                    if(message.message_group_id && message.groupName) {
                        deeplinking.activeMessage.chatWithHeading = message.groupName;
                    }
                    if(message.selectedTenantId) {
                        deeplinking.activeMessage.selectedTenantId = message.selectedTenantId;
                    }
                    const messageReviewNotificationData = {
                        sourceId: CONSTANTS.notificationSource.message,
                        sourceCategoryId: CONSTANTS.notificationSourceCategory.staffReviewingPatientMessage
                    };
                    this._structureService.sentPushNotification(message.userid || message.chatCreatedBy.toString(), 0, pushMessage, undefined, deeplinking, '', '', undefined, undefined, undefined, undefined, messageReviewNotificationData);
                }
            });

        }
        this.activeMessage.chatWith = message.chatHeading;
        this.activeMessage.chatWithUserId = message.userid || message.chatCreatedBy;

        var self = this;
        var activityData = {
            activityName: "Start Chat Session",
            activityType: "messaging",
            activityLinkageId: targetID,
            activityDescription: "Chat With - " + chatwith + " in Chatroom " + targetID + "." + " Client Time:" + new Date().getHours() + ':' + new Date().getMinutes() + ' ' + new Date().toString().match(/([A-Z]+[\+-][0-9]+.*)/)[1]
        };
        this._structureService.trackActivity(activityData);
        localStorage.setItem('targetId', targetID);
        localStorage.setItem('targetName', 'group-chat');
        localStorage.setItem('chatWithHeading', this.chatWithHeading);
        localStorage.setItem('activeMessage', JSON.stringify(this.activeMessage));
        localStorage.setItem('archived', 'false');
        this.router.navigate(['/inbox/chatroom']);
    }
    expandSubMessageList(e, message, i) {
        this.isSubListVisible = !this.isSubListVisible;
        this.chatroomSelected = message.chatroomId;        
        if(this.isSubListVisible) {
            this.isSubListFetching = true;        
            const params: GetMaskedRepliesPayload = {
                chatRoomId: message.chatroomId,
            }
            this.httpService.doPost(APIs.getMaskedReplyMessages, { ...defaultGetMaskedRepliesPayload, ...params }, ContentTypes.FORM).subscribe((response) => {
                this.inboxData[i].subList = response.message;
                this.config = this._structureService.userDataConfig;
                this.configData = JSON.parse(this.config);                
                if (!isBlank(this.configData.default_category_of_message_display)) {
                    this.messageFilter=this.configData.default_category_of_message_display;
                }
                const subIndex = this._structureService.inboxDataFirstPage.findIndex((x) => x.chatroomId === message.chatroomId);
                if(subIndex !== -1) {
                    this._structureService.inboxDataFirstPage[subIndex].subList = response.message;
                }       
                this.isSubListFetching = false;
            });    
        }
    }
    viewStructuredForm(event, message) {
        var data: any = {};
        data = message;
        data.loginUserId = this.userData.userId;
        this._structureService.setCookie('formData', encodeURIComponent(JSON.stringify(message)), 1);
        localStorage.setItem('formData', encodeURIComponent(JSON.stringify(message)));
        this._formsService.checkIsFormFilled(data).then(function (result: any) {
            this._sharedService.updateUserInboxCounts.emit({getCount:true, type: 'forms'});
        });
        if (message.form_submission_id && message.form_submission_id != 0) {
            this.router.navigate(['/forms/list/view']);
        } else {
            this.router.navigate(['/forms/view-form']);
        }
    }

    showVideoChat(){
        /* Avoid Double click: start */
        var videoOption = document.getElementById("videoPopup");
        videoOption.style.display = "none";
        if(this.allowVideoChat){
            this.allowVideoChat = false;
        }else{
            return false;
        }
        /* Avoid Double click: ends */
        this._sharedService.showVideoChat.emit({chatRoomList:this.chatroomUsersList,chatRoomId:this.currentChatroomId,pageHeading:this.pageHeading,joinChat:this.joinChat,activeMessage:this.activeMessage,allowVideoChat:this.allowVideoChat,chatroomPatientSiteId :this.chatroomPatientSiteId});  
        this._sharedService.onGoingChatrooom =  this.currentChatroomId
    }
    sendApplessData() {
        var videoOption = document.getElementById("videoPopup");
        videoOption.style.display = "none";
        this._sharedService.applessVideo = true;
        this.showVideoChat();        
    }
   
    videochat(){
      if(this.browserVideoSupport) { //Browser video support
        if (this.userData.config.enable_appless_video_chat == '1') {
            var videoOption = document.getElementById("videoPopup");
            videoOption.style.display = "block";
        } else{
            this.showVideoChat();
        }
      } else { //Browser video doesn't support
        this._structureService.notifyMessage({
            messge: 'Video Chat feature is not supported in the Browser you are using. Use either of Chrome, Safari or IE/Edge latest version',
            type: 'warning'
        });
      }
    }
    closeForm(){
        var videoOption = document.getElementById("videoPopup");
        videoOption.style.display = "none";
    }
    setLocalStorageParticipants(condition){
        localStorage.setItem('participant', condition);
      }
      checkVideoChatVariables(){
          if(this.chatroomUsersList.length>0 && this.currentChatroomId ){
              this.usersLoaded = true;
          }
      }
      checkVideoChat(){
        if (this._sharedService.onGoingChatrooom == this.currentChatroomId) {
            if (this.currentChatroomId != this._sharedService.callInitiatedRoomId){
                this.joined = true;
                this.joinChat = true;
            }
        } else {
            this.joined = false
            this.joinChat = false;
        }
      }
      show_read_users(id,messageDetails){
        this.getMessageInfo(messageDetails.id, MessageDeliveryStatus.READ).subscribe((msg) => {
            if (msg && this.newMessagedetails[id].id === msg[0].message_id) {
                msg.forEach(element => {
                    element.avatar = `${avatarUrlThumbs}${element.avatar}`;
                });
                this.newMessagedetails[id].readUsers = msg;
                event.stopPropagation();
                $('.show_read_users').hide();
                this.resetReadUserHeight();
                $('#show_read_users_'+id).toggle();
                var offsetTop =  $("#show_read_users_"+id)[0].offsetParent.offsetTop;
                var height =$(".cat__apps__chat-block").height();
                var scrollHeight = $("#show_read_users_"+id)[0].scrollHeight;
                var addHeight= (height -offsetTop)+scrollHeight;
                $(".cat__apps__chat-block").css({height:addHeight});
                $("#chat-window-scroll").animate({ scrollTop: $("#chat-window-scroll").height() }, 1000);           
            }
        });
        var activityLogreRouteMessage = this.userData.displayName + "(" + this.userData.userId + ") clicked read users button , Details ("+JSON.stringify(messageDetails)+")";
        var activityData = {
            activityName: "Open Read Users",
            activityType: "messaging",
            activityLinkageId: messageDetails.id,
            activityDescription: activityLogreRouteMessage
        };
        this._structureService.trackActivity(activityData);            
    }
      showMessageInfo(user,userId,messageDetails){
        if($("#msginfo-button").is(':visible')) {
            $('#msginfo-button').hide();
        }
        this.messageInfo=false;
        this.recipientmessageInfo=false; 
        this.messageInfoLoading=true;
        this.messageHistory = [];
        if(user=='sender'){
            this.messageInfoData["messageText"]=messageDetails.msg;
            if(!$("#msginfo-button").is(':visible')) {
              $('#msginfo-button').show();
              if(messageDetails.info_available == 1){
                const deliverySqlData={
                    chatroom_id: messageDetails.chatRoomId,
                    message_id: messageDetails.id,
                    user_id:userId,
                }
               this.getMessageInfo(deliverySqlData.message_id,MessageDeliveryStatus.ALL).subscribe((data) => {
                    if(data){
                        let readData = [];   
                        let deliveryData = [];    
                        data.forEach((readuser)=> {
                            let userAvatar;
                            if(readuser.avatar) {
                                userAvatar = `${avatarUrlThumbs}${readuser.avatar}`;
                            }
                            if(readuser.read_time) {
                                readData.push({avatar:userAvatar,displayName:readuser.displayName,lastactivity:readuser.read_time}); 
                            } 
                            if(readuser.deliverytime) {
                                deliveryData.push({avatar:userAvatar,displayName:readuser.displayName,deliverytime:readuser.deliverytime});
                            }                           
                            });
                        this.messageInfoData["readUsers"] = readData;
                        this.messageInfoData["deliveryData"] = deliveryData;
                        this.messageInfoLoading = false;
                        this.messageInfo = true;
                    }
                });
              }
              else{
                let userData = [];
                let readData = [];
                this.chatroomUsersList.forEach((user) => {
                    if(user.userId!=userId){
                        let userAvatar= this._structureService.getApiBaseUrl()+'citus-health/avatars/profile-pic-clinician-nurse-default.png'
                        if(user.avatar != ''){
                            userAvatar = `${avatarUrlThumbs}${user.avatar}`;
                        }
                        userData.push({avatar:userAvatar,displayName:user.displayName,deliverytime:messageDetails.time});
                        readData.push({avatar:userAvatar,displayName:user.displayName,lastactivity:messageDetails.time}); 
                     }                                 
                }) 
                this.messageInfoData["deliveryData"] = userData;
                this.messageInfoData["readUsers"] = readData;
                this.messageInfo = true;  
              }
            }
        }
        else if(user=='recipient'){ 
            if(!$("#msginfo-button").is(':visible')) {
              $("#msginfo-button").show();
              this.recipientMessageInfoDetails=messageDetails;
              this.messageInfoLoading=false;
              this.recipientmessageInfo=true;
            }
        }
        if(!isBlank(messageDetails.id)) {
            this._structureService.getMessageHistory(+messageDetails.id).then((result: any) => {                
                if(result && result.getDeleteUndoHistoryByMessageId && result.getDeleteUndoHistoryByMessageId.deleteUndoHistory && !isBlank(result.getDeleteUndoHistoryByMessageId.deleteUndoHistory)) {
                    const deleteHistory:any = result.getDeleteUndoHistoryByMessageId.deleteUndoHistory;
                    deleteHistory && deleteHistory.forEach((history: any) => { 
                        const actionTime = moment(history.actionTime).format(msgHistoryDateTimeFormat);
                        const action =  this._ToolTipService.getTranslateData(+history.actionType === 0 ? 'MESSAGES.MSG_HISTORY.DELETED': 'MESSAGES.MSG_HISTORY.RESTORED');
                        const actionHistory = this._ToolTipService.getTranslateDataWithParam('MESSAGES.MSG_HISTORY.MESSAGE', { actionTime, action });
                        this.messageHistory.push({ actionHistory });
                    });
                }
            });
        }     
   }
    resetReadUserHeight(){
        var height=0;
        $(".cat__apps__chat-block__content").each(function(value){
            height = height+$(this).height();
        });
        $(".cat__apps__chat-block").css({height:height});
      }
    searchReroute(optionShow = false) {
        this.searchMessage =1;
        this.resetPageAndLimit();
        this.loadMore(true, this.search.nativeElement.value.trim());
    }
    showAdvanceSearch() {
        this._structureService.hideAdvanceSearchArea = !this._structureService.hideAdvanceSearchArea;
        const state = this._structureService.hideAdvanceSearchArea ? 'close' : 'open';
        var activityData = {
          activityName: `${state} advance search in chatroom`,
          activityType: 'Advance Search',
          activityDescription: `${this.userData.displayName} ${state} advance search in chatroom ${this.currentChatroomId}`,
        };
        this._structureService.trackActivity(activityData);
    }
    applyQuickSearch(event: any) {
        this.resetQuickSearch = false;
        this.filteredTags = [];
        if (this.chatroomFilterApplied === Filter.ADVANCE) this.resetAdvanceSearch = true;
        this.chatroomFilterApplied = Filter.QUICK;
        this.priorityFilterValue = event.highPriority ? 1 : 0;
        this.flagFilterValue = event.highFlag ? 3 : 0;
        this.filteredMentions = event.mention ? [Number(this.userData.userId)] : [];
        if (Object.keys(event).every((key) => !event[key])) this.chatroomFilterApplied = this.chatroomFilterApplied === Filter.QUICK ? '' : this.chatroomFilterApplied;
        this.filterMessages('quick');
    }
    filterChatRoomMessages(event: any) {
        this.resetAdvanceSearch = false;
        this.filteredMentions = [];
        if (this.chatroomFilterApplied === Filter.QUICK) this.resetQuickSearch = true;
        this.chatroomFilterApplied = Filter.ADVANCE;
        if (isBlank(event)) {
            this.flagFilterValue = 0;
            this.priorityFilterValue = 0;
            this.filteredTags = [];
            this.chatroomFilterApplied = this.chatroomFilterApplied === Filter.ADVANCE ? '' : this.chatroomFilterApplied;
        } else {
            this.flagFilterValue = !isBlank(event.flag) ? parseInt(`${event.flag}`) : 0;
            this.priorityFilterValue = !isBlank(event.priority) ? parseInt(`${event.priority}`) : 0;
            this.filteredTags = !isBlank(event.tags) ? event.tags.split(',') : [];
        }
        this.filterMessages('advance');
    }
    filterMessages(type='message') {
        if(type == 'message') 
        this.filterMessage=1;        
        if(this.messageFilter == 0 && (this.flagFilterValue != 0 || this.priorityFilterValue != 0 || !isBlank(this.filteredTags))) {
            this.priorityFilterValue = 0;
            this.flagFilterValue = 0;
            this.filteredTags = [];
            this.resetAdvanceSearch = true;
            this.resetQuickSearch = true;
            var notify = $.notify('Advance and quick filter reset to `All Messages`');
            setTimeout(function () {
                notify.update({ 'type': 'warning', 'message': '<strong>Advance filter reset to `All Messages`</strong>' });
            }, 1000);
        }
        this.resetPageAndLimit();
        this.loadMore(true,this.search.nativeElement.value.trim());
    }
    linkToInbox()
    {
        this._structureService.inboxClicked(true);
    }
    resetChatMessageSearch() {
        this.search.nativeElement.value = "";        
        this.resetPageAndLimit();
        this.loadMore(true);
    }
    resetPageAndLimit(){
        this.last = 0;
        this.page = 1;
    }
    /***New Code By Jithin*/
    /**Staffs Listing Function***/
    getUsersOnInviteToChat(loadmore='',searchKeyword='',tenant=0,optionShow='', reset = false){
        this.messageGroupSelected = false;
       if(optionShow){
        this.optionShow=optionShow;
        }else{
        this.optionShow='staff';
        }
        if(this.optionShow === 'staff' || this.optionShow === 'partner') {
            this.tempPatientSiteId = !isBlank(this.selectSiteId) ? this.selectSiteId : (!isBlank(this.tempPatientSiteId) ? this.tempPatientSiteId :  this.userData.mySites.map((x)=> x.id));
        }
        this.searchValue = searchKeyword;
        this.tabSelectedData = optionShow;
        if(!loadmore) this.clearSelectedRoles(this.optionShow);
        this.isloadingTime=loadmore!=''?false:true;
        if(tenant!=0) this.tenantIDForInviteList = tenant;
         else  this.tenantIDForInviteList=this.chatWithTenantFilter.selectedTenant;
        this.get_all_tenants();
         
        this.loadMoreButtonTextInvite="Load More";
        if(loadmore==''){
            this.invitePageCount= 0;
            this.userListChatwith=[];
        }else{
            this.loadMoreButtonTextInvite="Loading...";
            this.invitePageCount+= 1;
        } 
        if(reset){
            searchKeyword = undefined;
            $('#chat-with-modal-search-box-on-invite').val('');
        }
        if(!isBlank(optionShow) && (optionShow === 'staff' || optionShow === 'partner' )){
            this.selecteGroupOrPdgTab = false;
        }
        if((!this.selecteGroupOrPdgTab && (this.userData && this.userData.config && this.userData.config.enable_multisite != 1)) || (this.userData && this.userData.config && this.userData.config.enable_multisite == 1)) {
            let siteId = !isBlank(this.selectSiteId) ? this.selectSiteId : this.chatroomPatientSiteId;
            if(this.isMsgGrp) {
                siteId = this.chatroomSiteIds;
            }    
        this._inboxService.getUsersListByRoleId((this.optionShow=='staff'?3:20),this.tenantIDForInviteList,null,false,this.invitePageCount,(this.optionShow=='staff'?true:false),searchKeyword,this.tenantIDForInviteList,true,'',optionShow,siteId).then((users) => {
            this.previousLength = this.userListChatwith.length && this.invitePageCount ? this.userListChatwith.length : 0;
            if(Array.isArray(users)) {
                users = users.map((item) => {
                    return {
                        ...item,
                        noContactAvailable: !item.password && !item.email && !item.mobile
                    }
                });
            }
            if (this.userListChatwith.length) this.userListChatwith = this.invitePageCount ? this.userListChatwith.concat(users) : users;
            else this.userListChatwith = users;
            this.initialLoadingListCount=this.userListChatwith.length?this.userListChatwith.length:0;
            if (this.userListChatwith.length % 20 === 0 && this.previousLength !== this.userListChatwith.length){
                this.loadMoreStatus=true;
                this.loadMoreButtonTextInvite = this._ToolTipService.getTranslateData('BUTTONS.LOAD_MORE');
            } else {
                this.loadMoreStatus=false;
            }
            this.isloadingTime=false;
        });
    }
    }
    /**Roles Listing funciton*/
     getRolesToInviteChat(tenant=0,optionShow=''){
        this.messageGroupSelected = false;
        this.clearSelectedRoles(optionShow);
        $("#userSearchTxtRoles").val('');
        $('#chat-with-modal-search-box-on-invite').val('');
        $("#notFoundRoles").hide();
        this.staffRolesList=[];
        this.isloadingTime=true;
        if(optionShow){
        this.optionShow=optionShow;
        }else{
        this.optionShow='partnerroles';    
        }
        this.tabSelectedData = optionShow;
        if(tenant!=0) this.tenantIDForInviteList = tenant;
        else  this.tenantIDForInviteList=this.chatWithTenantFilter.selectedTenant;
        this.get_all_tenants();
        if(!isBlank(optionShow) && (optionShow === 'staffroles' || optionShow === 'partnerroles' )){
            this.selecteGroupOrPdgTab = false;
        }
        if((!this.selecteGroupOrPdgTab && (this.userData && this.userData.config && this.userData.config.enable_multisite != 1)) || (this.userData && this.userData.config && this.userData.config.enable_multisite == 1)){
        this._inboxService.getStaffRolesListByTenantId(this.tenantIDForInviteList,this.optionShow,this.selectSiteId).then((data)=> {
            this.staffRolesList = Array.isArray(data)?data:[];
            this._sharedService.tempStaffRoleList = this.staffRolesList;
            if(this.staffRolesList.length){
                this.staffRolesList.sort(function (a, b) {              
                    if (a.RoleName.toLowerCase() < b.RoleName.toLowerCase()) return -1;
                    if (a.RoleName.toLowerCase() > b.RoleName.toLowerCase()) return 1;
                    return 0;
                  }); 
            }
            this.isloadingTime=false;
        });
    }
     }
     getStaffByRoleID(RoleID,fromRoles=''){
        this.clickedRoleId=0;
        this.clickedRoleId=RoleID; 
        if (this.staffUnderRole.length > 0) {
            this.staffUnderRole.forEach(element => {
                if (element !== RoleID) {
                    this.staffUnderRole.push(RoleID);
                }
            });
        }else {
           this.staffUnderRole.push(RoleID);
        }
         if (this.isSiteFilterDisabled) {
             this.selectSiteId = this.chatroomPatientSiteId;
         }
        if(RoleID!='' && RoleID!=undefined){
        if($("ul.sub-role-"+RoleID+" li").length==0){
            this.isStaffsloadingTime=true;
            this._structureService.getRoleBasedStaffs(RoleID,0,1,false,this.tenantIDForInviteList,this.selectSiteId).then(( data ) => {
                this.staffsUnderRolesList=[];
                this.staffsUnderRolesList=data['getSessionTenant']!=null ?data['getSessionTenant']['roleBasedStaffs']:[];
                if(this.staffsUnderRolesList == null){
                    this.staffsUnderRolesList = [];
                  }
                  if(this.configData.enable_nursing_agencies_visibility_restrictions == '1' && this.userData.nursing_agencies != '' && this.configData.na_staffs_chat_with_pharmacy == '0'){
                    var nur_login = this.userData.nursing_agencies.split(',');
            
                    nur_login.forEach(element => {                
                              this.staffsUnderRolesList = this.staffsUnderRolesList.filter(function (members) {
                                if(members.naTags != null && members.naTags != '' && members.naTags.split(',').indexOf(element) != -1){
                                   return true;                   
                                   }       
                              });   
                      });
               
                } else if(this.configData.enable_nursing_agencies_visibility_restrictions == '1' && this.userData.nursing_agencies != '' && this.configData.na_staffs_chat_with_pharmacy == '1'){
                    var nur_login = this.userData.nursing_agencies.split(',');
                     nur_login.forEach(element => {                
                      this.staffsUnderRolesList = this.staffsUnderRolesList.filter(function (members) {
                        if(members.naTags == null || members.naTags == '' || members.naTags.split(',').indexOf(element) != -1){
                           return true;                   
                           }else{
                             return false;
                           }
                      });           
                  });
                  }
                if(this.userListChatwith.length) this.userListChatwith=this.userListChatwith.concat(this.staffsUnderRolesList);
                else this.userListChatwith=this.staffsUnderRolesList;
                if(this.staffsUnderRolesList.length){
                    var itemsProcessed = 0; 
                   this.staffsUnderRolesList.forEach((user) => {
                        $("ul.sub-role-"+RoleID).append("<li><input type='checkbox' alt='"+user.id+"' class='hiddencheckbox userRole-"+this.clickedRoleId+"' value='"+user.id+"' style='display:none;'><span class='username-"+user.id+"'>"+user.displayName+"</span></li>");
                        if(user.naTagNames && user.naTagNames != ''){
                            $('.username-'+user.id).after('<span >('+user.naTagNames+')</span>');
                        }
                        itemsProcessed++;
                        if(itemsProcessed === this.staffsUnderRolesList.length) {
                            this.isStaffsloadingTime=false;
                            if(fromRoles=='') this.accordianActionForStaffRoles(RoleID);
                            else this.selectAllRoleUsers(RoleID);
                          }
                    });
                    }else{
                        $("ul.sub-role-"+RoleID).append("<li class='no-user-found' style='color:red;'>No users found</li>"); 
                        this.isStaffsloadingTime=false;
                        if(fromRoles=='') this.accordianActionForStaffRoles(RoleID);
                    } 
            }); 
        }else{
            if(fromRoles=='') this.accordianActionForStaffRoles(RoleID);
            else this.selectAllRoleUsers(RoleID);
        }
        }
     }
    //Client Side Search for Roles Listing
    searchOnKeyPress(event){
      var search_key_word=$("#userSearchTxtRoles").val().trim().toLowerCase();
      var key = event.keyCode || event.charCode;
      if( key == 8 || key == 46 ){
            $(".rolesUl li.roleslisting").filter(function() {
                $(this).toggle($(this).find("label").text().toLowerCase().indexOf(search_key_word) > -1);
                
              });
              if($(".rolesUl li.roleslisting").find("label").text().toLowerCase().indexOf(search_key_word)==-1) $("#notFoundRoles").show();
                else $("#notFoundRoles").hide();
      }else{
        $(".rolesUl li.roleslisting").filter(function() {
            $(this).toggle($(this).find("label").text().toLowerCase().indexOf(search_key_word) > -1)
            
          });
          if($(".rolesUl li.roleslisting").find("label").text().toLowerCase().indexOf(search_key_word)==-1) $("#notFoundRoles").show();
          else $("#notFoundRoles").hide();
      }
    }
    accordianActionForStaffRoles(RoleID=undefined){
       if(RoleID!=undefined){
           /***Accordian function here***/
        if($("ul.sub-role-"+RoleID).hasClass("accordion-show")){
            $("ul.sub-role-"+RoleID).addClass('accordion-hide').removeClass('accordion-show');
            $("i.expand-roles-plus-"+RoleID).addClass('accordion-show').removeClass('accordion-hide');
            $("i.expand-roles-minus-"+RoleID).addClass('accordion-hide').removeClass('accordion-show');
        }else{
            if(this.isloadingTime==false){
            $("i.expand-roles-plus-"+RoleID).addClass('accordion-hide').removeClass('accordion-show');
            $("i.expand-roles-minus-"+RoleID).addClass('accordion-show').removeClass('accordion-hide');
            $("ul.sub-role-"+RoleID).addClass("accordion-show").removeClass('accordion-hide');
            }
        } 
        /*****Accordian Ends Here****/ 
       }
    }
    /**get tenants with privilages***/
    get_all_tenants(){
        if (this.userData.organizationMasterId != 0 
            && this.userData.crossTenantsDetails.length > 1 
            && this.privileges.indexOf('allowOrganizationSwitching') != -1 && this.privileges.indexOf('allowRoleTenantSwitching') != -1
            && this.configData.allow_multiple_organization == 1 
            && this.userData.masterEnabled == '0'
            && this.configData.enable_nursing_agencies_visibility_restrictions != 1       
        ) {
            this.organizationSwitchPrivilege = true;
            this.crossTenantOptions = this.userData.crossTenantsDetails;
            this.crossTenantOptions.forEach((tenant) => {
                if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
                    this.selectedTenant = tenant;
                    this.chatWithTenantFilter.selectedTenant = tenant.id;
                    this._GlobalDataShareService.setSelectedTenantDetails(tenant);
                }
            });
        } else if(this.userData.organizationMasterId != 0
            && this.userData.crossTenantsDetails.length > 1
            && this.userData.isMaster =='1' 
            && this.userData.masterEnabled == '1'
            && this.privileges.indexOf('allowOrganizationSwitching') != -1 && this.privileges.indexOf('allowRoleTenantSwitching') != -1
        ) {
            this.organizationSwitchPrivilege = true;
            this.crossTenantOptions = this.userData.crossTenantsDetails;
            this.crossTenantOptions.forEach((tenant) => {
                if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
                    this.selectedTenant = tenant;
                    this.chatWithTenantFilter.selectedTenant = tenant.id;
                    this._GlobalDataShareService.setSelectedTenantDetails(tenant);
                }
            });
        } else if(this.userData.organizationMasterId != 0
            && this.userData.crossTenantsDetails.length > 1
            && this.userData.isMaster =='0' 
            && this.userData.masterEnabled == '1'
        ) {
            this.organizationSwitchPrivilege = false;
            this.crossTenantOptions = this.userData.crossTenantsDetails;
            this.crossTenantOptions.forEach((tenant) => {
                if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
                    this.selectedTenant = tenant;
                    this.chatWithTenantFilter.selectedTenant = tenant.id;
                    this._GlobalDataShareService.setSelectedTenantDetails(tenant);
                }
            });
        } else if(this.configData.enable_nursing_agencies_visibility_restrictions == 1) {
            if(this.userData.nursing_agencies == "") {
                if (this.userData.organizationMasterId != 0 
                    && this.userData.crossTenantsDetails.length > 1 
                    && this.privileges.indexOf('allowOrganizationSwitching') != -1 && this.privileges.indexOf('allowRoleTenantSwitching') != -1
                    && this.configData.allow_multiple_organization == 1 
                    && this.userData.masterEnabled == '0'       
                ) {
                    this.organizationSwitchPrivilege = true;
                    this.crossTenantOptions = this.userData.crossTenantsDetails;
                    this.crossTenantOptions.forEach((tenant) => {
                        if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
                            this.selectedTenant = tenant;
                            this.chatWithTenantFilter.selectedTenant = tenant.id;
                            this._GlobalDataShareService.setSelectedTenantDetails(tenant);
                        }
                    });
                }
    
            } else {
                this.organizationSwitchPrivilege = false;
                this.crossTenantOptions = this.userData.crossTenantsDetails;
                this.crossTenantOptions.forEach((tenant) => {
                    if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
                        this.selectedTenant = tenant;
                        this.chatWithTenantFilter.selectedTenant = tenant.id;
                        this._GlobalDataShareService.setSelectedTenantDetails(tenant);
                    }
                });
            }
        }
        if (this.userData.organizationMasterId != 0 
            && this.userData.crossTenantsDetails 
            && this.userData.crossTenantsDetails.length > 1) {
            this.crossTenantOptionsChatWith = this.userData.crossTenantsDetails;
            this.chatWithTenantFilter.tenants = this.crossTenantOptionsChatWith;
            this.crossTenantOptionsChatWith.forEach((tenant) => {
                if (tenant.id == this._structureService.getCookie("crossTenantId")) {
                    this.crossTenantName = tenant.tenantName;
                }
            });
            if ((this.crossTenantOptionsChatWith.length > 1
                && this.privileges.indexOf('allowOrganizationSwitching') != -1
                && this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication') !== -1 
                && this.configData.allow_multiple_organization == 1
                && this.userData.masterEnabled == '0'
                && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
            || (this.crossTenantOptionsChatWith.length > 1 
                && this.userData.isMaster =='1' 
                && this.userData.masterEnabled == '1')
            || (this.crossTenantOptionsChatWith.length > 1 
                && this.userData.isMaster =='0' 
                && this.userData.masterEnabled == '1'
                && this.userData.group !='3')
            || (this.crossTenantOptionsChatWith.length > 1
                && this.privileges.indexOf('allowOrganizationSwitching') != -1
                && this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication') !== -1 
                && this.configData.allow_multiple_organization == 1
                && this.userData.masterEnabled == '0'
                && this.configData.enable_nursing_agencies_visibility_restrictions == 1
                && this.userData.nursing_agencies == "")
            ) {
               if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1') {
                    this.internalSiteId = this.userData.master_details.id;
                }
                this.chatWithLoader.otherTenantstaff = true;
                this.chatWithTenantFilter.tenants = this.chatWithTenantFilter.tenants.filter((tenant)=> {
                    if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1'){
                        if(this.internalSiteId == tenant.id) {
                            return true;
                        } else {
                            return false;
                        }
                    } else {
                        return true;
                    }
                });
                if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1') {
                    if(this.userData.master_details.id != this.userData.tenantId) {
                        this.crossTenantOptionsChatWith.map((tenant)=> {
                            if(tenant.id == this.userData.tenantId) {
                                this.chatWithTenantFilter.tenants.unshift(tenant);
                            }
                        });
                    }
                }
            } else { 
                this.crossTenantOptionsChatWith = [];
                this.chatWithTenantFilter.tenants = [];
            }
        }else {
            this.crossTenantOptionsChatWith = [];
            this.chatWithTenantFilter.tenants = [];
        }   
        this.setChatWithTenantFilterSelect();
    }
    setChatWithTenantFilterSelect() {
        if(this.selectedTenant) {
            this.chatWithTenantFilter.selectedTenant =  this.selectedTenant.id;
        } else {
            this.chatWithTenantFilter.selectedTenant = this._structureService.getCookie('tenantId');
        }
                if((this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
                && this.configData.allow_multiple_organization==1 
                && this.userData.crossTenantsDetails.length > 1 
                && this.userData.masterEnabled == '0'
                && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
            || (this.userData.masterEnabled == '1' 
                && this.userData.isMaster == '1')
            || (this.userData.masterEnabled == '1' 
                && this.userData.isMaster == '0'
                && this.userData.group !='3')
            || (this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
                && this.configData.allow_multiple_organization==1 
                && this.userData.crossTenantsDetails.length > 1 
                && this.userData.masterEnabled == '0'
                && this.configData.enable_nursing_agencies_visibility_restrictions == '1'
                && this.userData.nursing_agencies == '')) {
                  
                   this.assignChatWithTenantFilterData(); 
                    
                } else {
                    
                    this.chatWithTenantFilter.filterEnabled = false;
                    this.chatWithTenantFilter.tenants = [];
                }
    }
    assignChatWithTenantFilterData() {
        this.triggeredChangeEvent=true;
        this.chatWithTenantFilter.filterEnabled = true;
        setTimeout( ()=> {
        if($("#chatWithTenantFilter-invite").length) {
              $('#chatWithTenantFilter-invite').select2({
               dropdownParent: $("#inviteModal")
             });
              $("#chatWithTenantFilter-invite").css("text-overflow", "ellipsis");
        } else {
           this.chatWithTenantFilter.setTenantDropDown = true;
        }
    });
    }
    changeTenant(tenantId){
        $('#chat-with-modal-search-box-on-invite').val('');
        if(this.optionShow=='staff' || this.optionShow=='partner') this.getUsersOnInviteToChat('','',tenantId,this.optionShow);
        else this.getRolesToInviteChat(tenantId,this.optionShow);
        
    }
    //for staffs liting only
    searchOnEnterKeyPress(event,optionShow=''){
        var keyCode = event.which || event.keyCode;
        if (keyCode === 13) {
          this.getUsersOnInviteToChat('',event.target.value,this.tenantIDForInviteList,optionShow);
        }
    }

    updateInboxDataOnRead() {
        let inboxData = [];
        if(this.currentPage == 1) {
            inboxData = this._structureService.inboxDataFirstPage;
        } else {
            inboxData = this.inboxData;
        }
        const index = inboxData.findIndex(x=> x.chatroomid === this.currentChatroomId);
        if (inboxData && inboxData.length && index != -1 && inboxData[index]) {
            inboxData[index].readPriority = (Number(inboxData[index].unReadPriority) !== 0 && (Number(inboxData[index].unReadPriority) < Number(inboxData[index].readPriority) || Number(inboxData[index].readPriority) === 0)) ? Number(inboxData[index].unReadPriority) : Number(inboxData[index].readPriority);
            inboxData[index].unReadPriority = 0;
            inboxData[index].readMention = inboxData[index].readMention === '0' ? inboxData[index].unReadMention : inboxData[index].readMention;
            inboxData[index].unReadMention = '0';
        }
        if(this.currentPage == 1) {
            this._structureService.inboxDataFirstPage = inboxData;
            this.inboxData = this._structureService.inboxDataFirstPage;
        } else {
            this.inboxData = inboxData;
        }
        this._structureService.inboxData = this.inboxData;
    }

    updateInboxDataOnFlagChange(messageFlagCount,mDetails, type, data={}) {
        var inboxData = [];
        if(this.currentPage == 1) {
            inboxData = this._structureService.inboxDataFirstPage;
        } else {
            inboxData = this.inboxData;
        }
        var strIndex = inboxData.findIndex(x=> x.chatroomid === this.currentChatroomId);
        if(messageFlagCount == 1) {
            if(inboxData && inboxData.length && strIndex != -1 && inboxData[strIndex]) {
                if(type) {
                    inboxData[strIndex].msg_flag = mDetails.msg_flag;
                } else {
                    if(data['flag_data_id']) {
                        inboxData[strIndex].msg_flag_data_id = data['flag_data_id'];                            
                    } else {
                        inboxData[strIndex].msg_flag_data_id = mDetails.msg_flag_data_id;
                    }
                    inboxData[strIndex].msg_flag = mDetails.msg_flag;
                }
            }
        } else {
            if(inboxData && inboxData.length && strIndex != -1 && inboxData[strIndex]) {
                if(!mDetails.msg_flag_data_id || mDetails.msg_flag_data_id != inboxData[strIndex].msg_flag_data_id) {
                    if(parseInt(mDetails.msg_flag) > parseInt(inboxData[strIndex].msg_flag)) {
                        if(type) {
                            inboxData[strIndex].msg_flag = mDetails.msg_flag;
                        } else {
                            if(data['flag_data_id']) {
                                inboxData[strIndex].msg_flag_data_id = data['flag_data_id'];                            
                            } else {
                                inboxData[strIndex].msg_flag_data_id = mDetails.msg_flag_data_id;
                            }
                            inboxData[strIndex].msg_flag = mDetails.msg_flag;
                        }
                    } else if(parseInt(mDetails.msg_flag) < parseInt(inboxData[strIndex].msg_flag)) {
                        var higherFlag = {
                            id:null,
                            value: -1
                        }
                        this.newMessagedetails.map((msgData)=>{
                            if(msgData.msg_flag > higherFlag.value) {
                                higherFlag.value = msgData.msg_flag;
                                higherFlag.id = msgData.msg_flag_data_id;
                            }
                        })
                        if(higherFlag.value > -1) {
                            if(type){
                                inboxData[strIndex].msg_flag = higherFlag.value;
                            } else {
                                inboxData[strIndex].msg_flag = higherFlag.value;
                                inboxData[strIndex].msg_flag_data_id = higherFlag.id;
                            }
                        }
                    }
                } else {
                    if(parseInt(mDetails.msg_flag) > parseInt(inboxData[strIndex].msg_flag)) {
                        inboxData[strIndex].msg_flag = mDetails.msg_flag;
                    } else if(parseInt(mDetails.msg_flag) < parseInt(inboxData[strIndex].msg_flag)) {
                        var higherFlag = {
                            id:null,
                            value: -1
                        }
                        this.newMessagedetails.map((msgData)=>{
                            if(msgData.msg_flag > higherFlag.value) {
                                higherFlag.value = msgData.msg_flag;
                                higherFlag.id = msgData.msg_flag_data_id;
                            }
                        })
                        if(higherFlag.value > -1) {
                            if(type){
                                inboxData[strIndex].msg_flag = higherFlag.value;
                            } else {
                                inboxData[strIndex].msg_flag = higherFlag.value;
                                inboxData[strIndex].msg_flag_data_id = higherFlag.id;
                            }
                        }
                    }
                }
            }
        }
        if(this.currentPage == 1) {
            this._structureService.inboxDataFirstPage = inboxData;
            this.inboxData = this.restructureTags(this._structureService.inboxDataFirstPage);
        } else {
            this.inboxData = this.restructureTags(inboxData);
        }
        this._structureService.inboxData = this.inboxData;
    }
    onthreadFlagChange(mDetails) {
        this.expandFlagOPtions(mDetails);
        var message = {
            id: mDetails.id,
            msg_flag_data_id: mDetails.msg_flag_data_id,
            chatroomid:this.currentChatroomId,
            msg_flag: mDetails.msg_flag
        }
        var activityData = {
            activityName: "",
            activityType: "messaging",
            activityDescription: ""
        };
        var addMessageFlag =0;
        var description = "";
        var flagDetails = "";
        var prev_msg_flag = mDetails.prev_msg_flag;
        var prev_flag_data_id = mDetails.msg_flag_data_id;
        if(this.flagFilterValue && (mDetails.msg_flag!=this.flagFilterValue)) {
            this.newMessagedetails = this.newMessagedetails.filter(msg=>{
                return msg.id != mDetails.id
            });
            if(!this.newMessagedetails.length) {
                this.loadMoreButtonText = "No Messages Available";
                this.noMessage =true;
                $(".cat__apps__chat-block").css({height:"100px"}); 
            } else {
                this.loadMoreButtonText = "";
                this.noMessage =false;
            }
        }
        var messageFlagCount = this.messageFlagCount;
        messageFlagCount++;
        this.updateInboxDataOnFlagChange(messageFlagCount,mDetails,1);
        this._structureService.updateChatThreadOrMsgFlag('msg', message).then((data)=>{
            if(data && data['status'] && data['status'] == 1 && data['flag_data_id']) {                
                this.messageFlagCount++;                
                mDetails.msg_flag_data_id = data['flag_data_id']; 
                addMessageFlag = 1;               
            }
            if(data && data['status'] && data['status'] == 1) {
                mDetails.prev_msg_flag = mDetails.msg_flag;
                if(mDetails.msg_flag == 0) {
                    this._structureService.notifyMessage({
                        messge: 'Message Flag Cleared Successfully',
                        type: 'success'
                    });
                } else {
                    this._structureService.notifyMessage({
                        messge: 'Message Flagged Successfully',
                        type: 'success'
                    });
                }
                this.updateInboxDataOnFlagChange(this.messageFlagCount,mDetails,0, data);
                setTimeout( ()=>{
                    if(this._structureService.currentUrlNow != '/inbox/chatroom') {
                        this._sharedService.onInboxData.emit({inboxData : ((parseInt(localStorage.getItem('pageCountMessage')) == 0) ? this._structureService.inboxDataFirstPage : this._structureService.inboxData), emitUpdateInboxData: true, inboxDataFirstPage: this._structureService.inboxDataFirstPage});
                    }
                });
                if(addMessageFlag) {
                    activityData.activityName = "Add Message Flag";
                    description = "Add message flag done by ";
                } else {
                    if(mDetails.msg_flag == 0) {
                        activityData.activityName = "Delete Message Flag";
                        description = "Delete message flag done by ";
                    } else {
                        activityData.activityName = "Update Message Flag";
                        description = "Update message flag done by ";
                    }                        
                }
                flagDetails = "The flag choosed is "+mDetails.msg_flag+", previous flag is "+ prev_msg_flag +", msg_flag_data_id is "+mDetails.msg_flag_data_id+" and prev_msg_flag_data_id is "+ prev_flag_data_id +".["+JSON.stringify(data)+"]";    
                activityData.activityDescription = description + this.userData.displayName +"(" + this.userData.userId + ") success for message (" + mDetails.id + ") in chatroom (" + this.currentChatroomId +")."+flagDetails;
            } else {
                var flagchoosed = mDetails.msg_flag;
                mDetails.msg_flag = mDetails.prev_msg_flag;
                this.updateInboxDataOnFlagChange(this.messageFlagCount,mDetails,1);
                if(!mDetails.msg_flag_data_id) {
                    activityData.activityName = "Failure Add Message Flag";
                    description = "Add message flag done by ";
                    flagDetails = "The flag choosed is "+flagchoosed+" and msg_flag_data_id is "+mDetails.msg_flag_data_id+".["+JSON.stringify(data)+"]";
                } else {
                    if(flagchoosed == 0) {
                        activityData.activityName = "Failure Delete Message Flag";
                        description = "Delete message flag done by ";
                    } else {
                        activityData.activityName = "Failure Update Message Flag";
                        description = "Update message flag done by ";
                    }
                    flagDetails = "The flag choosed is "+flagchoosed+", previous flag is "+ prev_msg_flag +", msg_flag_data_id is "+mDetails.msg_flag_data_id+" and prev_msg_flag_data_id is "+ prev_flag_data_id +".["+JSON.stringify(data)+"]";    
                }
                activityData.activityDescription = description + this.userData.displayName +"(" + this.userData.userId + ") failed for message (" + mDetails.id + ") in chatroom (" + this.currentChatroomId +")."+flagDetails;
                this._structureService.notifyMessage({
                    messge: 'Message Flagging Failed',
                    type: 'danger'
                });
            }
            this._structureService.trackActivity(activityData);     
        }).catch((ex) => {
            var flagchoosed = mDetails.msg_flag;
            mDetails.msg_flag = mDetails.prev_msg_flag;
            this.updateInboxDataOnFlagChange(this.messageFlagCount,mDetails,1);
            if(!mDetails.msg_flag_data_id) {
                activityData.activityName = "Failure Add Message Flag";
                description = "Add message flag done by ";
                flagDetails = "The flag choosed is "+flagchoosed+" and msg_flag_data_id is "+mDetails.msg_flag_data_id+".["+JSON.stringify(ex)+"]";
            } else {
                if(flagchoosed == 0) {
                    activityData.activityName = "Failure Delete Message Flag";
                    description = "Delete message flag done by ";
                } else {
                    activityData.activityName = "Failure Update Message Flag";
                    description = "Update message flag done by ";
                }
                flagDetails = "The flag choosed is "+flagchoosed+", previous flag is "+ prev_msg_flag +" and msg_flag_data_id is "+mDetails.msg_flag_data_id+".["+JSON.stringify(ex)+"]";    
            }
            activityData.activityDescription = description + this.userData.displayName +"(" + this.userData.userId + ") failed for message (" + mDetails.id + ") in chatroom (" + this.currentChatroomId +")."+flagDetails;
            this._structureService.trackActivity(activityData);
        });
    }
    expandFlagOPtions(mDetails) {
        let openFlagOPtion = true;
        if($('.cat__apps__messaging__tab__time.thread-flag.item-'+mDetails.id).hasClass('show')) {
            openFlagOPtion = false;
        }
        $('.cat__apps__messaging__tab__time').removeClass('show');
        if(openFlagOPtion)
        $('.cat__apps__messaging__tab__time.thread-flag.item-'+mDetails.id).addClass('show');
    } 

    /**
     * Function to store message details to local storage for temporary usage
     * @param object userMessagetoServerData message payload
     */
    storeMessageDataToLocal(userMessagetoServerData){
        //check the message payload already stored in local or not
        if (!window.localStorage.getItem("chUserMessages")) {
            //Store message payload locally
            const userDatas = {
                "Messages": [userMessagetoServerData]
            }
            window.localStorage.setItem("chUserMessages", JSON.stringify(userDatas));
        } else {
            //update message data in local
            let jsonS = window.localStorage.getItem("chUserMessages");
            let obj = JSON.parse(jsonS);
            obj['Messages'].push(userMessagetoServerData);
            jsonS = JSON.stringify(obj);
            window.localStorage.setItem("chUserMessages", jsonS);
        }
    }

    restructureTags(dataRes) {
        return dataRes.map((data) => {
            data.allTags = [
                ...(isBlank(data.messageTagList) ? []: data.messageTagList).map((item) => {
                    item.threadLevel = item.type === MessageTagType.THREAD;
                    return item;
                })
            ];
            return data;
        });      
    }

    moveCursorToEnd() {
        const editableDiv: any = document.getElementsByClassName('emojionearea-editor')[0];
        if (editableDiv) {
            let range, selection;
            if (document.createRange)//Firefox, Chrome, Opera, Safari, IE 9+
            {
                range = document.createRange();//Create a range (a range is a like the selection but invisible)
                range.selectNodeContents(editableDiv);//Select the entire contents of the element with the range
                range.collapse(false);//collapse the range to the end point. false means collapse to end rather than the start
                selection = window.getSelection();//get the selection object (allows you to change selection)
                selection.removeAllRanges();//remove any selections already made
                selection.addRange(range);//make the range you have just created the visible selection
            }
        }
      }

      activateNextItem() {
        // adjust scrollable-menu offset if the next item is out of view
        let listEl: HTMLElement = document.getElementById('mention-users-dropdown');
        let activeEl = listEl.getElementsByClassName('active').item(0);
        if (activeEl) {
          let nextLiEl: HTMLElement = <HTMLElement> activeEl.nextSibling;
          if (nextLiEl && nextLiEl.nodeName == "LI") {
            let nextLiRect: ClientRect = nextLiEl.getBoundingClientRect();
            if (nextLiRect.bottom > listEl.getBoundingClientRect().bottom) {
              listEl.scrollTop = nextLiEl.offsetTop + nextLiRect.height - listEl.clientHeight;
            }
          }
        }
        // select the next item
        const index = this.filteredMentionUsers.findIndex((user) => user.active);
        if(!isBlank(this.filteredMentionUsers[index+1])) {
            this.filteredMentionUsers[index].active = false;
            this.filteredMentionUsers[index+1].active = true
        }
      }
    
      activatePreviousItem() {
        // adjust the scrollable-menu offset if the previous item is out of view
        let listEl: HTMLElement = document.getElementById('mention-users-dropdown');
        let activeEl = listEl.getElementsByClassName('active').item(0);
        if (activeEl) {
          let prevLiEl: HTMLElement = <HTMLElement> activeEl.previousSibling;
          if (prevLiEl && prevLiEl.nodeName == "LI") {
            let prevLiRect: ClientRect = prevLiEl.getBoundingClientRect();
            if (prevLiRect.top < listEl.getBoundingClientRect().top) {
              listEl.scrollTop = prevLiEl.offsetTop;
            }
          }
        }
        // select the previous item
        const index = this.filteredMentionUsers.findIndex((user) => user.active);
        if(!isBlank(this.filteredMentionUsers[index-1])) {
            this.filteredMentionUsers[index].active = false;
            this.filteredMentionUsers[index-1].active = true
        };
      }

      stopEvent(event: any) {
        if (!event.wasClick) {
          event.preventDefault();
          event.stopPropagation();
          event.stopImmediatePropagation();
        }
      }

      get notPatient() {
        return this.userData.group != UserGroup.PATIENT;
      }

      selectMentionUser(user, e) {
        const selection = <Selection>window.getSelection();
        if (selection.rangeCount) {
            const range = selection.getRangeAt(0);
            const container = range.startContainer;
            const lastLine = container.nodeValue;
            if (lastLine && lastLine.includes('@')) {
                const first = lastLine.slice(0, range.startOffset);
                const last = lastLine.slice(range.endOffset, lastLine.length);
                const lastIndex = first.lastIndexOf('@')
                const updatedLine = `${first.substring(0, lastIndex)}<input type="button" value="${user.displayName}" attr.data-target="${
                user.userId
                }" class='mention'></input>&NoBreak;${last}`;
                $('#mention-users-dropdown').hide();
                if(!isBlank(this.filteredMentionUsers[0])) {
                    this.filteredMentionUsers[0].active = true
                }
                this.filteredMentionUsers.forEach((user, index) => {
                    if (index === 0) user.active = true;
                    else user.active = false;
                });
                const fragment = document.createRange().createContextualFragment(updatedLine);
                const tempDiv = document.createElement('div');
                tempDiv.appendChild(fragment);
                const parent = container.parentNode;
                parent.replaceChild(tempDiv, container);
                selection.removeAllRanges();
                while (tempDiv.firstChild) {
                    parent.insertBefore(tempDiv.firstChild, tempDiv);
                }
                parent.removeChild(tempDiv);
            }
        }
        setTimeout(() => {
            this.moveCursorToEnd();
        });
    }
    private getMessageInfo(messageId: number, deliveryStatus: number): Observable<any> {
        const apiUrl = `${APIs.deliveryStatusFetchApi}/${messageId}/${deliveryStatus}`;
        return this.httpService.doGet(apiUrl);
    }

    private deleteMessagechatroom(data): Observable<any> {
        const apiUrl = `${APIs.deleteMessageChatroom}`;
        return this.httpService.doPut(apiUrl,data );
    }
    private getDeletedChatroomDetails(messageId: number, deliveryStatus: number): Observable<any> {
        const apiUrl = `${APIs.deliveryStatusFetchApi}/${messageId}/${deliveryStatus}`;
        return this.httpService.doGet(apiUrl);
    }
    
    handleImageError(event: any) {
        event.target.src = this.noImage;
    }

    /* Downloading Files Functionalities */

    getFileName(str: string): string | null {
        const regex = CONSTANTS.allowedFileDownloads;
        const match = str.match(regex);
        return match ? match[1] : null;
    }

    getModalBody(modalContent) {
        this.modalBody = modalContent;
    }
    getFileFromMessage(message: string) {
        let docNames = [];
        let docLinks = [];
        let downloaded = [];
        const regex = CONSTANTS.downloadFileLink;
        let match;
        while ((match = regex.exec(message)) !== null) {
	        let originalJsonUrl = match[1].replace(/\?[^'"]*$/, '');
	        docLinks.push(originalJsonUrl);
            downloaded.push(true);
        }
        let messageTitles = message.split("title");
        for (let i = 1; i < messageTitles.length; i++) {
            let docName = this.getFileName(messageTitles[i]);
            if(docName !== null){
                docNames.push(docName);
            }
        }
        return { docNames, docLinks, downloaded };
    }

    /**To increase the ngFor perfomance in DOM */ 
    trackByForMessageInboxList(i, item): number {
        return item.chatroomId;
    }

    isMaskedSubthreadSelected(baseChatroomId, i){
       if(this.inboxData[i].subList && this.inboxData[i].subList.length) {
            this.subThreadSelected  = this.inboxData[i].subList.find(item => +item.baseId === +baseChatroomId && +item.chatroomid === +this.currentChatroomId);           
        } 
    }
    reloadSubChatRoom (evt, message, i) {
        this.toggleTranslation = false;
        if($("#tag-button").is(':visible')) {
            this.closeTagModal();
        }            
        this.config = this._structureService.userDataConfig;
        this.configData = JSON.parse(this.config);            
        if(!isBlank(this.configData.default_category_of_message_display)) {
            this.messageFilter = this.configData.default_category_of_message_display;
        }
        if (parseInt(message.unreadCount)) {
            if (parseInt(message.unread) || parseInt(message.unreadCount)) {
                this._sharedService.readInboxData.emit(message);
            }
            this.inboxData.map((inputMessage) => {
                if (+inputMessage.chatroomId === +message.baseChatroomId) {
                    inputMessage.maskedUnreadCount = inputMessage.maskedUnreadCount - message.unreadCount;
                    inputMessage.hasUnreadMessages = !!inputMessage.maskedUnreadCount;
                } else if (message.baseId && this.inboxData[i].SubList && +inputMessage.chatroomId === +message.baseId) {
                    //Update unread count for masked message sub thread
                    const index = this.inboxData[i].SubList.findIndex((item) => item.chatroomid === message.chatroomid);
                    if (index !== -1) {
                        this.inboxData[i].SubList[index].unreadCount = 0;
                        this.inboxData[i].SubList[index].unread = 0;
                    }
                }   
                if (inputMessage.chatroomId === +message.chatroomid) {
                    inputMessage.unreadCount = 0;
                    inputMessage.unread = 0;
                    inputMessage.hasUnreadMessages = false;
                }
                return inputMessage;
            });
        }
        setTimeout(() => {
            message.chatroomId = message.chatroomid;
            message.maskedUnreadCount = message.unreadCount;
            this.reloadChatRoom(evt, message, i)
        }, 10);
    }
    private inboxListScrollTop() {
        setTimeout(() => {
            if ($('.inbox-data-container .cat__apps__messaging__tab--selected').length) {
                const topPos = $('.inbox-data-container .cat__apps__messaging__tab--selected').offset().top;
                const sTop = $('.inbox-data-container').scrollTop();
                const topPosition = topPos + sTop;
                $('.inbox-data-container').animate({ scrollTop: topPosition - 360 }, 'slow');
            }
        }, 1000);
    }
    private handleMessageParticpantsData() {
        if (this.chatParticipants) {
            const defaultPic = `${this._structureService.getApiBaseUrl()}citus-health/avatars/profile-pic-clinician-nurse-default.png`;
            const groupId = this.activeMessage.message_group_id || '';
            const isGroupIdEmpty = !groupId || groupId === '0' || groupId === '';
            this.chatParticipants.forEach((participant) => {
                const isPatientGroup = +participant.roleId === UserGroup.PATIENT; 
                participant.avatar = participant.avatar ? this.avatarBasePath + participant.avatar : defaultPic;
                participant.isPatientGroup = isPatientGroup;
                participant.isSelfUser =  +participant.userId === +this.userData.userId;
                participant.showParticipant = isGroupIdEmpty || (!isGroupIdEmpty && !isPatientGroup);                
            });
            if(this.roleParticipants && this.allParticipants) {
                this.roleParticipants = this.roleParticipants.map(participant => {
                    // Find matching users based on assignedRoles
                    const matchingUsers = this.allParticipants.filter(user => {
                      return +user.participantRole === +participant.id;
                    });
                    if(matchingUsers.some((item) =>  +item.deleted === 1)) {
                        participant.showRestoreBtn = true;
                    }
                    // Add matching users as a sub-item in the participant object
                    return {
                      ...participant,
                      members: matchingUsers.map(user => ({
                        ...user,
                        deleted: +user.deleted
                      }))
                    };
                  });
            }
        }
    }
    confirmToRemoveRole(role) {
        this._structureService.showAlertMessagePopup({
            title: this._ToolTipService.getTranslateDataWithParam('MESSAGES.CONFIRM_MESSAGE_ROLE_FROM_CHAT', {roleName: role.name}),
            text: ' '
        }).then((initialConfirm) => {
            if(initialConfirm) {
                setTimeout(() => {
                    this._structureService.showAlertMessagePopup({
                        text: this._ToolTipService.getTranslateData('MESSAGES.CONFIRM_REMOVE_USERS_FROM_ROLE'),
                        type: CONSTANTS.notificationTypes.error
                    }).then((confirm) => {
                        if(confirm) {
                            const data = { roleId: +role.id, chatRoomId: +this.currentChatroomId };
                            this.httpService.doDelete(APIs.removeChatroomRoleParticipant, data).subscribe((response:any) => {
                            if(response.success) {
                                this._structureService.notifyMessage({
                                    messge: this._ToolTipService.getTranslateDataWithParam('SUCCESS_MESSAGES.REMOVE_ROLE_FROM_CHAT_SUCCESS', {roleName: role.name}),
                                    type: CONSTANTS.notificationTypes.success
                                });
                                this.ChatRoomUsers();
                                this.last = 0;
                                this.loadMore(true);// Call chat room message API to load the message room and to get updated chat title
                            } else {
                                this._structureService.notifyMessage({
                                    messge: this._ToolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG'),
                                });  
                            }
                            });
                        }                       
                    });
                },100);               
            }
        });         
    }
    hasChatPermission() {
        this.initiateChatPermission = this._structureService.hasInitiateChatPermission();
    }
    getPatientoffDutyMessage(heading): string {
        const chatTimeoutMessage = this._ToolTipService.getTranslateDataWithParam('MESSAGES.PATIENT_CHAT_TIMEOUT_MSG', { chatHeading: heading });
        return this.initiateChatPermission 
        ? `${chatTimeoutMessage} ${this._ToolTipService.getTranslateData('MESSAGES.CHAT_WITH_SUPPORT_TEAM')}`
        : chatTimeoutMessage;
    }
    selectedPdgs(item) {
        this.selectedPdgGroups = [];
        item.forEach((element) => { 
            this.selectedPdgGroups.push({patientId: +element.id, admissionId: element.admissionId});
        });
        this.messageGroupSelected = !isBlank(this.selectedPdgGroups);
        this.resetPdgData = false;
    }
    /**
     * Get the selected admission data
     * @param admission admission GUID
     */
    setAdmissionId(admission: any) {
        this.admissionId = admission && admission.id || '';
        this.admissionNotSelected = false;
    }
    /**
     * To restore users under the role
     */
    restoreUser(item) {
        const staffOrRole = item.user ? item.user.displayName : item.role.name;
        let message = '';
        let staffNames = '';
        let restoreType: RestoreType = item.user ? 'roleUser' : 'role';
        let id = 0;
        if (item.user) {
            id = +item.user.id;
            message = this._ToolTipService.getTranslateDataWithParam('MESSAGES.RESTORE_STAFF_TO_CHAT', { staffName: staffOrRole });
            staffNames = item.user.displayName;
        } else {
            id = +item.role.id;
            message = this._ToolTipService.getTranslateDataWithParam('MESSAGES.RESTORE_STAFF_IN_ROLE_TO_CHAT', { roleName: staffOrRole });
            staffNames = item.role.users.map((item) => item.displayName).join(', ');
        }
        this._structureService.showAlertMessagePopup({
            text: message,
        }).then((confirm) => {
            if (confirm) {
                const payload: ManageChatParticipantsPayload = {
                    data: {
                        id,
                        chatRoomId: +this.currentChatroomId,
                        type: restoreType
                    },
                    action: 'restore'
                };

                this.httpService.doPost(APIs.manageChatParticipants, payload).subscribe((response: any) => {
                    if (response.success) {
                        this.ChatRoomUsers();
                        this.updateChatroomTitle();
                        this._structureService.notifyMessage({
                            messge: this._ToolTipService.getTranslateDataWithParam('SUCCESS_MESSAGES.USER_RESTORE_TO_CHATROOM', { staffNames: staffNames }),
                            type: CONSTANTS.notificationTypes.success
                        });
                    } else {
                        this._structureService.notifyMessage({
                            messge: this._ToolTipService.getTranslateData('ERROR_MESSAGES.USER_RESTORE_TO_CHATROOM')
                        });
                    }
                });

            }
        });
    }
     updateChatroomTitle() {
        this._ChatService.getChatroomMessages(this.currentChatroomId, false, this.page, this.last,"",this.messageFilter, this.flagFilterValue, this.priorityFilterValue, this.filteredTags, this.filteredMentions).then((data) => {
            if (data) {
                const result = JSON.parse(JSON.stringify(data));
				this.pageHeading = result.title;
            }
        }); 
    }
    /**
     * // status 1 = restoreParticipants 
     *    status 0 = deleteParticipants
     */
    updateChatRoomParticipant():void {
        this._structureService.subscribeSocketEvent('updateChatroomParticipants').subscribe((response: UpdateChatroomParticipantsResponse) => {
            if(+this.currentChatroomId === +response.room && +response.initiatorUserId !== +this.userData.userId) {
                this.ChatRoomUsers();
                this.updateChatroomTitle();
            }
        });
    }
    showOutOfOfficeMessage(chatParticipants : any[]) {
        this.oooUsers = chatParticipants.filter((data) => getOooStatusBasedOnTime(data.oooInfo).status && 
        (data.oooInfo.isOutOfOffice || !isBlank(data.oooInfo.message)) && 
        +data.userId !== +this.userData.userId);
        this.oooMessage = '';
        if (this.oooUsers.length) {
            const offlineUsers = this.oooUsers.filter((data) => data.oooInfo.isOutOfOffice);
            this.showOooMessage = true;
            if (this.oooUsers.length === 1) {
                this.isMultipleOfflineUser = false;
                if (offlineUsers.length === 1) {
                    this.oooMessage = !isBlank(offlineUsers[0].oooInfo.message) ? `${this.oooUsers[0].displayName} : ${offlineUsers[0].oooInfo.message}` : this._ToolTipService.getTranslateDataWithParam('LABELS.OUT_OF_OFFICE_SINGLE_USER_MSG', { displayName: offlineUsers[0].displayName });
                } else {
                    this.oooMessage = `${this.oooUsers[0].displayName} : ${this.oooUsers[0].oooInfo.message}`;
                }
                this.offlineStatus = offlineUsers.length === 1;
            } else {
                this.isMultipleOfflineUser = true;
                this.oooMessage = offlineUsers.length === this.oooUsers.length ? this._ToolTipService.getTranslateData('LABELS.OUT_OF_OFFICE_ON_MULTIPLE_USER_MSG') : this._ToolTipService.getTranslateData('LABELS.OUT_OF_OFFICE_OFF_MULTIPLE_USER_MSG');
                this.offlineStatus = offlineUsers.length > 0;
            }
        } else {
            this.showOooMessage = false;
        }        
    }
    getOooInfo(userId) {
        const matchingParticipant = !isBlank(this.allParticipants) && this.allParticipants.find((participant) => +participant.userId === +userId);
        return matchingParticipant && matchingParticipant.oooInfo ? matchingParticipant.oooInfo : null;
    }
	showStatusModal(){
        $('#userlistModal').modal('show');
        this.showModal = true;
    }
	closeStatusModal(){
        this.showModal = false;
    }

    formatMessageDeletedTime(dateTimeUTC: string): string {
      return dateTimeUTC ? moment(dateTimeUTC).format(msgHistoryDateTimeFormat) : '';
    }

    deleteUndoMessage(mDetails, i: number, action: String,) {
        let deletedData = {
            id: mDetails.id,
            action
        }
        this._structureService.showAlertMessagePopup({
            text: this._ToolTipService.getTranslateDataWithParam('MESSAGES.DELETE_MESSAGE_CONFIRM',{ action: action === "UNDO" ? "restore" : action.toLowerCase() }),
            type: CONSTANTS.notificationTypes.warning
        }).then((confirm) => {
            if(confirm) {
                this.deleteMessagechatroom(deletedData).subscribe({
                    next: (data) => {
                        if (data.status) {
                            this._ChatService.getChatroomMessages(this.currentChatroomId, false, 0, 0, '', '1', 0, 0, this.filteredTags, this.filteredMentions, '', data.messageId, true).then((response) => {
                                const chatMessageResponse = JSON.parse(JSON.stringify(response));
                                let updatedMessage = chatMessageResponse.content.find(message => +message.id === mDetails.id);
                                updatedMessage.sign = updatedMessage.sign != 'false' ? `${this._structureService.apiBaseUrl}writable/filetransfer/uploads/${updatedMessage.sign}`  : false;
                                const index = this.newMessagedetails.findIndex(message => +message.id === updatedMessage.id);
                                if (index > -1) {
                                    this.newMessagedetails = this.newMessagedetails.map((message, i) => {
                                        if (i === index) {
                                            return { ...message, ...updatedMessage};
                                        }
                                        return message;
                                    });
                                }
                            });
                            this._structureService.notifyMessage({
                                messge: data.message,
                                type: 'success'
                            });
                        } else {
                            const message =  this._ToolTipService.getTranslateDataWithParam('MESSAGES.DELETE_OR_UNDO_FAILED', { action: action === "UNDO" ? "restore" : action.toLowerCase() });
                            this._structureService.notifyMessage({ message });
                        }
                    },
                    error: (err) => {
                        console.log("Error", err);
                    }
                })
            }                       
        });
    }
    isUndoAvailable(deletedTime: string): boolean {
        const currentTime = moment();
        const deletedDate = convertUTCToTimeZoneDateTimeSplit(
            deletedTime,
            '',
            moment.tz.guess(),
            DateFormat.MMDDYY_HMA,
        );
        const deletedMoment = moment(deletedDate, DateFormat.MMDDYY_HMA,);
        return currentTime.diff(deletedMoment, 'minutes') <= 30;
    }
    /** To Handle the message status on delete/restore */
    handleMessageOnStatusChange(data): void {
        const url = `${APIs.chatroomMessages}?room=${data.chatroomId}&id=${data.messageId}`;
        this.httpService.doGet(url).subscribe((response) => {
            const updatedMessage = response.content.find(message => +message.id === +data.messageId);
            const index = this.newMessagedetails.findIndex(message => +message.id === +updatedMessage.id);
            if (index > -1) {
                this.newMessagedetails[index] = { ...this.newMessagedetails[index], ...updatedMessage };
                this.newMessagedetails[index].fileDownload = this.getFileFromMessage(updatedMessage.message)              
                this.newMessagedetails[index].msg = this.newMessagedetails[index].message;                
            }
        });
    }
}
