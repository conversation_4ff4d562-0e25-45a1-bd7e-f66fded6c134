import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { BrowserModule } from '@angular/platform-browser';
import { ReactiveFormsModule } from '@angular/forms';
import { FormsModule } from '@angular/forms';
import { ColorPickerModule } from 'ngx-color-picker';
import { NgUploaderModule } from 'ngx-uploader';
import { ScheduleComponent, ManageCalendarComponent, ManageVisitTypeComponent, ManageAccessoriesComponent, ManageCleanTimeComponent } from './schedule.component';
import { NurseScheduleService } from './nurse-schedule.service';
// import { AddEducationMaterialComponent } from './add-education-material.component';
// import { UpdateEducationMaterialComponent } from './update-education-material.component';
// import { ViewEducationMaterialComponent } from './view-education-material-list.component';
// import { Modal } from './educationModal';
import {
	MessageGroupSearchFilterPipe,
	SearchRoleFilterSchedulePipe,
	SearchFilterPipe,
	SearchGroupFilterSchedulePipe,
	SearchRoleFilterMsgGrpsPipe,
	SearchFilterRoleTreeViewPipe,
	pluralizeScheduleFilterPipe,
	SearchRoleFilterScheduleGroupsPipe
} from './messagegroup-search.pipes';
import { filterMessageGroupPipe } from './messagegroup-search.pipes';

import { NgbModule } from '@ng-bootstrap/ng-bootstrap';

export const routes: Routes = [
	{ path: 'booked-scheduler/schedule', component: ScheduleComponent },
	{ path: 'booked-scheduler/manage-calendar', component: ManageCalendarComponent },
	{ path: 'booked-scheduler/manage-visit-type', component: ManageVisitTypeComponent },
	{ path: 'booked-scheduler/manage-accessories', component: ManageAccessoriesComponent },
	{ path: 'booked-scheduler/manage-cleantime', component: ManageCleanTimeComponent },
];

@NgModule({
	imports: [
		CommonModule,
		FormsModule,
		BrowserModule,
		ReactiveFormsModule,
		RouterModule.forChild(routes),
		NgUploaderModule,
		ColorPickerModule,
		NgbModule.forRoot()
	],
	declarations: [
		MessageGroupSearchFilterPipe,
		SearchRoleFilterSchedulePipe,
		SearchRoleFilterScheduleGroupsPipe,
		SearchFilterPipe,
		filterMessageGroupPipe,
		SearchGroupFilterSchedulePipe,
		pluralizeScheduleFilterPipe,
		SearchFilterRoleTreeViewPipe,
		SearchRoleFilterMsgGrpsPipe,
		ScheduleComponent,
		ManageCalendarComponent,
		ManageVisitTypeComponent,
		ManageAccessoriesComponent,
		ManageCleanTimeComponent
	],
	providers: [ MessageGroupSearchFilterPipe, filterMessageGroupPipe, NurseScheduleService ]
})
export class BookedSchedulerModule {}
