import { Injectable, OnInit } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import { Subject } from 'rxjs/Subject';
import 'rxjs/add/operator/toPromise';
import { StructureService } from '../structure.service';
import { SharedService } from '../shared/sharedServices';
import { Router, ActivatedRoute, Params, NavigationEnd } from '@angular/router';
import { from } from 'rxjs/observable/from';


declare var $: any;
declare var jQuery: any;
declare var NProgress: any;


@Injectable()
export class NurseScheduleService {
    checkNurseScheduleCalendarConfiguredUrl = '';
    checkNurseScheduleHasCalendarConfiguredUrl = '';
    constructor(
        private _http: Http,
        public _structureService: StructureService,
    ) {
        this.checkNurseScheduleCalendarConfiguredUrl = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/check-nurse-schedule-calendar-configured.php';
        this.checkNurseScheduleHasCalendarConfiguredUrl = this._structureService.apiBaseUrl + 'citus-health/' + this._structureService.version + '/nurse-schedule-tenant-calendar-configured.php';
    }

    checkNurseScheduleCalendarConfigured() {
        let headers = new Headers();
        headers.append('Content-Type', 'application/x-www-form-urlencoded');
        headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
        let options = new RequestOptions({ headers: headers });
        const promise = new Promise((resolve, reject) => {
            var data = "tenantId=" + this._structureService.getCookie('tenantId');
            this._http.post(this.checkNurseScheduleCalendarConfiguredUrl, data, options)
            .toPromise()
            .then(res => {
                const scheduleCalendarConfigurationDetails = res.json();
                resolve(scheduleCalendarConfigurationDetails);
            },
            err => {
              reject(err);
            });
        });
        return promise;        
    }
    /**
     * Check for calendar has enable/ created for the logged in Tenant.
     */
    hasCalendarEnabledForTenant() {
        let headers = new Headers();
        headers.append('Content-Type', 'application/x-www-form-urlencoded');
        headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
        let options = new RequestOptions({ headers: headers });
        const promise = new Promise((resolve, reject) => {

            //  this.setCookie('tenantname', userData.tenantName, 1);
            
            var data = "tenantId=" + this._structureService.getCookie('tenantId')+"&tenantName="+this._structureService.getCookie('tenantname');

            this._http.post(this.checkNurseScheduleHasCalendarConfiguredUrl, data, options)
            .toPromise()
            .then(res => {
                const scheduleCalendarConfigurationDetails = res.json();
                resolve(scheduleCalendarConfigurationDetails);
            },
            err => {
              reject(err);
            });
        });
        return promise;          
    }

}