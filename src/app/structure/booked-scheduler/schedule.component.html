<!-- START: forms/basic-forms-elements -->
<!-- <form [formGroup]="accountSettings" (ngSubmit)="updateSettings()"> -->
    <section class="card">
        <div class="card-header">
            <span class="cat__core__title">
            <strong>Nurse Schedule</strong>
        </span>
        </div>
        <div class="card-block">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
                <li class="breadcrumb-item"><a (click)="redirectToCalendar()">Nurse Schedule </a></li>

            </ol>
            <div class="wait-loading" *ngIf="dataLoadingMsg">
                <img src="./assets/img/loader/loading.gif" />
            </div>
            <!-- <div class="row"> -->
                <!-- <iframe src="{{formsUrl}}"></iframe> -->
                <!-- <iframe  height="1442" allowTransparency="true" frameborder="0" scrolling="no" style="width:100%;border:none" src='{{formsUrl}}' ></iframe> -->
                <div [hidden]="!IframeContent" [innerHTML]="IframeContent"></div>
                <div class="single-page-block" [hidden]="scheduleCalendarDetails.dontShowWarning">
                    <div class="text-center max-width-500">
                        <h1 class="font-size-36 mb-2">Warning!</h1>
                        <p>Please contact your admin. It looks like they haven't configured the nurse schedule for your tenant.</p>
                        <a [routerLink]="['/inbox']" class="btn btn-link">Go Back</a>
                    </div> 
                </div>
            <!-- </div> -->
        </div>
    </section>
<!-- </form> -->
<!-- END: forms/basic-forms-elements -->