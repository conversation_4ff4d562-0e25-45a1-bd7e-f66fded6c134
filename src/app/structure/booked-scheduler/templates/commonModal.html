<div class="modal-backdrop fade in" [style.display]="showModal ? 'block' : 'none'"></div>
<div class="modal forward-modal" tabindex="-1" role="dialog" style="display: block"
    [style.display]="showModal ? 'block' : 'none'">
    <div class="modal-dialog" [ngClass]="{'modal-iframe-height':modalIframeBody}">
        <div class="modal-content"
            [ngClass]="{'modal-iframe-height':modalIframeBody,'video-transparent':modalVideoBody}">
            <div class="modal-header" [ngClass]="{'modal-header-hide':cancelLabel}">
                <h4 class="modal-title">{{title}}</h4>
                <button type="button" class="close" [ngClass]="{'video-close-button-position':modalVideoBody}"
                    data-dismiss="modal" (click)="hide()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" *ngIf="!modalIframeBody && !modalVideoBody" [innerHTML]="modalBody">
            </div>
            <div class="modal-body" *ngIf="modalIframeBody" [ngClass]="{'modal-iframe-height':modalIframeBody}">
                <iframe class="e2e-iframe-trusted-src" id="pdfIframe" scrolling="no" [src]="modalBody" width="100%"
                    height="100%" onload="javascript:this.contentWindow.location.hash=':0.page.20';">
                </iframe>
            </div>
            <div class="modal-body" *ngIf="modalVideoBody" class="centerme">
                <video poster="assets/modules/dummy-assets/common/img/Loading_icon.gif" [src]="modalBody"
                    class="centerme" controls="controls" autoplay></video>
            </div>

            <div *ngIf="showFooter" class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>