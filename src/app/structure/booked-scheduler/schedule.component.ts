import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';

import { DomSanitizer } from '@angular/platform-browser';
import { NurseScheduleService } from './nurse-schedule.service';
import { type } from 'os';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;
declare var swal: any;
declare var NProgress: any;

@Component({
	selector: 'app-schedule',
	templateUrl: './schedule.component.html'
})
export class ScheduleComponent implements OnInit {
	// Scheduler section.
	IframeContent: any = '';
	dataLoadingMsg: boolean = true;
	scheduleCalendarDetails: any = {
		"schedule_id": 0,
		"name": '',
		"schedule_tenant_id": 0,
		"dontShowWarning": true
	};
	constructor(
		private route: ActivatedRoute,
		private router: Router,
		private _structureService: StructureService,
		public _nurseScheduleService: NurseScheduleService,
		private sanitizer: DomSanitizer
	) { }

	ngOnInit() {
		this.iniazializeCalendar();
	}

	redirectToCalendar() {
		this.dataLoadingMsg = true;
		this.iniazializeCalendar();
	}

	iniazializeCalendar() {
		window.addEventListener("message", (event) => {
			try {
				if (event && event.data) {
					var data;
					if (typeof (event.data) == "object") {
						data = JSON.stringify(event.data);
					} else {
						data = event.data;
					}
					var eventdata = data.split("=");
					console.log(eventdata);
					if (eventdata[0] == "booked_iframe_height") {
						this.dataLoadingMsg = false;
					}
				}
			} catch (error) {
				this.dataLoadingMsg = false;
				console.log("error", error);
			}
		}, false);
		this._nurseScheduleService.checkNurseScheduleCalendarConfigured().then((data: any) => {
			console.log(data)
			if (data && data.schedule_id && data.schedule_id != 0) {
				this.scheduleCalendarDetails = data;
				var Url = this._structureService.serverBaseUrl + '/booked/Web/authenticate_user_embed.php?resumeUrl=' + "MY_CALENDAR" + '&fromCallBell=1&Authentication-Token=' + this._structureService.getCookie('authenticationToken') + '&schedule_calendar=' + this.scheduleCalendarDetails.schedule_id;

				console.log("formsUrl------------------>", Url);
				var html = '<iframe onload="javascript:parent.scrollTo(0,0);" height="1000" allowTransparency="true" frameborder="0" scrolling="no" style="width:100%;border:none" src="' + Url + '" ></iframe>';
				this.IframeContent = this.sanitizer.bypassSecurityTrustHtml(html);
			} else {
				this.setError(1);
			}
		}, (err) => {
			this.setError(2);
		}).catch((ex) => {
			this.setError(3);
		});
	}
	setError(id) {
		console.log('errrorrr caleed from' + id)
		this.scheduleCalendarDetails = {
			"schedule_id": 0,
			"name": '',
			"schedule_tenant_id": 0,
			"dontShowWarning": false
		};
		this.dataLoadingMsg = false;
		this.IframeContent = null;
		const warning_msg = `Please contact your admin. It looks like they haven't configured the nurse schedule for your tenant.`;
		const notify = $.notify(warning_msg);
		setTimeout(function () {
			notify.update({ 'type': 'warning', 'message': '<strong>' + warning_msg + '</strong>' });
		});
	}

}

@Component({
	selector: 'nurse-schedule-manage-calendar',
	templateUrl: './templates/manage-calendar.html'
})
export class ManageCalendarComponent implements OnInit {
	IframeContent: any = '';
	dataLoadingMsg: boolean = true;
	tenantId: any;
	constructor(
		private route: ActivatedRoute,
		private router: Router,
		private _structureService: StructureService,
		private sanitizer: DomSanitizer,
		public _nurseScheduleService: NurseScheduleService,
	) { }
	ngOnInit() {

		this.tenantId = this._structureService.getCookie('tenantId');


		this._nurseScheduleService.hasCalendarEnabledForTenant().then((data: any) => {
			window.addEventListener("message", (event) => {
				try {
					if (event && event.data) {
						var data;
						if (typeof (event.data) == "object") {
							data = JSON.stringify(event.data);
						} else {
							data = event.data;
						}
						var eventdata = data.split("=");

						if (eventdata[0] == "booked_iframe_loaded") {
							this.dataLoadingMsg = false;
						}
					}
				} catch (error) {
					console.log("error");
					this.dataLoadingMsg = false;
				}
			}, false);
			var Url = this._structureService.serverBaseUrl + '/booked/Web/authenticate_user_embed.php?resumeUrl=' + "MANAGE_SCHEDULES_ADD_OR_EDIT" + '&fromCallBell=1&Authentication-Token=' + this._structureService.getCookie('authenticationToken');


			console.log("formsUrl------------------>", Url);

			var html = '<iframe onload="javascript:parent.scrollTo(0,0);" height="1000" allowTransparency="true" frameborder="0" scrolling="no" style="width:100%;border:none" src="' + Url + '" ></iframe>';
			this.IframeContent = this.sanitizer.bypassSecurityTrustHtml(html);

		}, (err) => {
			//this.setError(2);
		}).catch((ex) => {
			//this.setError(3);	
		});


	}

}


// Manage Visit Type Component
@Component({
	selector: 'nurse-schedule-manage-visittype',
	templateUrl: './templates/manage-visit-type.html'
})
export class ManageVisitTypeComponent implements OnInit {
	IframeContent: any = '';
	dataLoadingMsg: boolean = true;
	constructor(
		private route: ActivatedRoute,
		private router: Router,
		private _structureService: StructureService,
		private sanitizer: DomSanitizer
	) { }
	ngOnInit() {

		window.addEventListener("message", (event) => {
			try {
				if (event && event.data) {
					var data;
					if (typeof (event.data) == "object") {
						data = JSON.stringify(event.data);
					} else {
						data = event.data;
					}
					console.log(data)
					var eventdata = data.split("=");
					if (eventdata[0] == "booked_iframe_visit_type_height") {
						this.dataLoadingMsg = false;
						if (typeof (parseInt(eventdata[1])) == "number") {
							$("#nurse-schedule-visit-types").height(eventdata[1] + "px")
						}

					}
				}
			} catch (error) {
				console.log("error");
				this.dataLoadingMsg = false;
			}
		}, false);
		var Url = this._structureService.serverBaseUrl + '/booked/Web/authenticate_user_embed.php?resumeUrl=' + "MANAGE_VISIT_TYPES_ADD_OR_EDIT" + '&fromCallBell=1&Authentication-Token=' + this._structureService.getCookie('authenticationToken');

		console.log("formsUrl----->>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>------------------>", Url);
		var html = '<iframe id="nurse-schedule-visit-types" onload="javascript:parent.scrollTo(0,0);" height="1000" allowTransparency="true" frameborder="0" scrolling="no" style="width:100%;border:none" src="' + Url + '" ></iframe>';
		this.IframeContent = this.sanitizer.bypassSecurityTrustHtml(html);

	}

}

// Manage Accessories Component
@Component({
	selector: 'nurse-schedule-manage-accessories',
	templateUrl: './templates/manage-accessories.html'
})
export class ManageAccessoriesComponent implements OnInit {
	IframeContent: any = '';
	dataLoadingMsg: boolean = true;
	constructor(
		private route: ActivatedRoute,
		private router: Router,
		private _structureService: StructureService,
		private sanitizer: DomSanitizer
	) { }
	ngOnInit() {

		window.addEventListener("message", (event) => {
			try {
				if (event && event.data) {
					var data;
					if (typeof (event.data) == "object") {
						data = JSON.stringify(event.data);
					} else {
						data = event.data;
					}
					console.log(data)
					var eventdata = data.split("=");

					if (eventdata[0] == "booked_iframe_accessory_height") {
						this.dataLoadingMsg = false;
						if (typeof (parseInt(eventdata[1])) == "number") {
							$("#nurse-schedule-accessories").height(eventdata[1] + "px")
						}
					}
				}
			} catch (error) {
				console.log("error");
				this.dataLoadingMsg = false;
			}
		}, false);
		var Url = this._structureService.serverBaseUrl + '/booked/Web/authenticate_user_embed.php?resumeUrl=' + "MANAGE_ACCESSORIES_ADD_OR_EDIT" + '&fromCallBell=1&Authentication-Token=' + this._structureService.getCookie('authenticationToken');

		console.log("formsUrl----->>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>------------------>", Url);
		var html = '<iframe id="nurse-schedule-accessories" onload="javascript:parent.scrollTo(0,0);" height="1000" allowTransparency="true" frameborder="0" scrolling="no" style="width:100%;border:none" src="' + Url + '" ></iframe>';
		this.IframeContent = this.sanitizer.bypassSecurityTrustHtml(html);

	}

}



// Manage Clean Time Component. 
@Component({
	selector: 'nurse-schedule-manage-cleantime',
	templateUrl: './templates/manage-cleantime.html'
})
export class ManageCleanTimeComponent implements OnInit {
	IframeContent: any = '';
	dataLoadingMsg: boolean = false;
	constructor(
		private route: ActivatedRoute,
		private router: Router,
		private _structureService: StructureService,
		private sanitizer: DomSanitizer
	) { }
	ngOnInit() {

		window.addEventListener("message", (event) => {
			try {
				if (event && event.data) {
					var data;
					if (typeof (event.data) == "object") {
						data = JSON.stringify(event.data);
					} else {
						data = event.data;
					}

					var eventdata = data.split("=");
					if (eventdata[0] == "booked_iframe_accessory_height") {
						this.dataLoadingMsg = false;
						if (typeof (parseInt(eventdata[1])) == "number") {
							$("#nurse-schedule-cleantime").height(eventdata[1] + "px")
						}
					}
				}
			} catch (error) {
				console.log("error");
				this.dataLoadingMsg = false;
			}
		}, false);
		var Url = this._structureService.serverBaseUrl + '/booked/Web/authenticate_user_embed.php?resumeUrl=' + "MANAGE_CLEANING_TIME_ADD_OR_EDIT" + '&fromCallBell=1&Authentication-Token=' + this._structureService.getCookie('authenticationToken');

		console.log("formsUrl----->>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>------------------>", Url);
		var html = '<iframe id="nurse-schedule-cleantime" onload="javascript:parent.scrollTo(0,0);" height="1000" allowTransparency="true" frameborder="0" scrolling="no" style="width:100%;border:none" src="' + Url + '" ></iframe>';
		this.IframeContent = this.sanitizer.bypassSecurityTrustHtml(html);

	}

}
