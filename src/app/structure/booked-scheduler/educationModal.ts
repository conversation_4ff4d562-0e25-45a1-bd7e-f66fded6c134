import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';

// const template: string = require('./templates/commonModal.html');

@Component({
    selector: 'modal',
    templateUrl:'./templates/commonModal.html'
})

export class Modal implements OnInit {
    @Input('show-modal') showModal: boolean;
    @Input('show-header') showHeader: boolean;
    @Input('show-footer') showFooter: boolean;
    @Input('show-iframe') modalIframeBody: boolean;
    @Input('show-video') modalVideoBody: boolean;
    @Input('title') title: string;
    @Input('sub-title') subTitle: string;
    @Input('modal-body') modalBody: String;
    @Input('cancel-label') cancelLabel: Boolean;
    @Input('positive-label') positiveLabel: string;

    @Output('closed') closeEmitter: EventEmitter<ModalResult> = new EventEmitter<ModalResult>();
    @Output('loaded') loadedEmitter: EventEmitter<Modal> = new EventEmitter<Modal>();
    @Output() positiveLabelAction = new EventEmitter();

    constructor() { }

    ngOnInit() {
        this.loadedEmitter.next(this);
    }

    show() {
        console.log("show............");
        this.showModal = true;
    }

    hide() {
        this.showModal = false;
        this.closeEmitter.next({
            action: ModalAction.POSITIVE
        });
    }

    positiveAction() {
        this.positiveLabelAction.next(this);
        return false;
    }

    cancelAction() {
        this.showModal = false;
        this.closeEmitter.next({
            action: ModalAction.CANCEL
        });
        return false;
    }
}

export enum ModalAction { POSITIVE, CANCEL }

export interface ModalResult {
    action: ModalAction;
}