<div class="autocomplete time-container">
  <input type="text" [formControlName]="controlName" placeholder="{{ 'LABELS.ENTER_TIME' | translate }}" (click)="toggleOptions()" class="form-control time-label" />
  <ul class="time-options-new autocomplete" *ngIf="showOptions">
    <li
      class="list-item"
      *ngFor="let time of timeOptions"
      (click)="selectScheduleTime(time)"
      [ngClass]="{ selected: time === inputFormControl.value }"
    >
      {{ time }}
    </li>
  </ul>
</div>

<div *ngIf="inputFormControl.invalid && (inputFormControl.dirty || inputFormControl.touched)">
  <div *ngIf="inputFormControl.errors?.pattern" class="alert alert-danger">{{ 'LABELS.INVALID_TIME_FORMAT' | translate }}</div>
</div>
