import { Component, ElementRef, HostListener, Input, OnInit, SkipSelf } from '@angular/core';
import { ControlContainer, FormControl } from '@angular/forms';

@Component({
  selector: 'ch-timepicker',
  templateUrl: './ch-time-picker.component.html',
  styleUrls: ['./ch-time-picker.component.scss'],
  viewProviders: [
    {
      provide: ControlContainer,
      useFactory: (container: ControlContainer) => container,
      deps: [[new SkipSelf(), ControlContainer]]
    }
  ]
})
export class TimePickerComponent implements OnInit {
  @Input() controlName: string;
  @Input() inputFormControl: FormControl;
  timeOptions: string[] = [];
  showOptions = false;
  @HostListener('document:click', ['$event'])
  clickout(event) {
    if (!this.eRef.nativeElement.contains(event.target)) {
      this.showOptions = false;
    }
  }

  constructor(private eRef: ElementRef) {}

  ngOnInit() {
    this.generateTimeOptions();
  }

  toggleOptions() {
    this.showOptions = true;
    const time = this.inputFormControl.value;
    if (time) {
      const selectedTimeIndex = this.timeOptions.indexOf(time);
      setTimeout(() => {
        const optionElement = document.querySelector(`.time-options-new li:nth-child(${selectedTimeIndex + 1})`);
        if (optionElement) {
          optionElement.scrollIntoView({ block: 'nearest' });
        }
      });
    }
  }

  selectScheduleTime(time: string) {
    this.showOptions = false;
    this.inputFormControl.setValue(time);
  }

  generateTimeOptions() {
    const startHour = 0; // Start from 12:00 AM
    for (let hour = startHour; hour < 24 + startHour; hour++) {
      const formattedHour = `0${hour % 12 === 0 ? 12 : hour % 12}`.slice(-2);
      for (let minute = 0; minute < 60; minute += 15) {
        const formattedMinute = `0${minute}`.slice(-2);
        const period = hour < 12 ? 'AM' : 'PM';
        this.timeOptions.push(`${formattedHour}:${formattedMinute} ${period}`);
      }
    }
  }
}
