import { Component, OnInit, Pipe, PipeTransform } from '@angular/core';
import { DatePipe } from '@angular/common';
import { StructureService } from './../../structure/structure.service';
//declare var $: any;
//declare var jQuery: any;
//declare var autosize: any;
//declare var Ladda: any;
//declare var Chartist: any;

@Pipe({
  name: 'signatureRequestUnreadCount',
  pure: false
})
// export class signatureRequestUnreadCountPipe implements PipeTransform {
//     transform(arr: any, exponent: string): any {
//         console.log(exponent)
//         var totalUnReadMessages = 0;
//         if(arr != undefined){
//             for (var i = 0; i < arr.length; i++) {
//                 if (arr[i].associateSignatureProcess) {
//                     if (!(exponent == arr[i].ownerId || (arr[i].associateSignatureByUsers.userId && arr[i].associateSignatureByUsers.userId == exponent) && !arr[i].isRead)) {
//                     }        
//                 }
//             }
//         }            
//         // console.log("signatureRequestUnreadCount---->>>>"+totalUnReadMessages);
//         return totalUnReadMessages;
//     }
// }
export class signatureRequestUnreadCountPipe implements PipeTransform {
    transform(arr: any, exponent: string): any {
        var totalUnReadMessages = 0;
        if(arr != undefined){
            for (var i = 0; i < arr.length; i++) {
                var addCount = true;
                if (arr[i] && arr[i].ownerId && arr[i].type) {
                    if (arr[i].associateSignatureProcess) {
                        if (!arr[i].associateSignatureProcess || exponent == arr[i].ownerId || (arr[i].associateSignatureByUsers.userId && arr[i].associateSignatureByUsers.userId == exponent)) {
                            
                        } else {
                            addCount = false;
                        }

                    }
                } else {
                    addCount = false;    
                }
                if(addCount == true) {
                    if (arr[i].signatureStatus == 'SIGNED') {
                        if (arr[i].accountLevelArchived) {
                        } else if (arr[i].archivedUsers && arr[i].archivedUsers.includes(exponent)) {
                        } else {
                            if(!arr[i].isRead) {
                                totalUnReadMessages++;
                            }
                        }
                    } else if (arr[i].signatureStatus == "PENDING" || arr[i].signatureStatus == "SIGN_APPROVAL") {
                        if (arr[i].accountLevelArchived) {
                        } else if (arr[i].archivedUsers && arr[i].archivedUsers.includes(exponent)) {
                        } else {
                            if(!arr[i].isRead) {
                                totalUnReadMessages++;
                            }
                        }
                    }
                }    
            }
        }            
        // console.log("signatureRequestUnreadCount---->>>>"+totalUnReadMessages);
        return totalUnReadMessages;
    }
}


@Pipe({
    name: 'scheduleSelectionFilter',
    pure: false
})
export class unReadMessagesCountPipe implements PipeTransform {
    constructor(public _structureService:StructureService){}
    transform(user: any): any {
        var d = new Date();
        var currentHour = d.getHours();
        var currentMinutes = d.getMinutes();
        var currentInterval = currentHour + ":" + currentMinutes;
        
        var userData = JSON.parse(this._structureService.userDetails);
        var objRes;
        var availableUser;
        if (userData && userData.schedulerData) {
            var scheduleJson = userData.schedulerData[user.userId];
            if (scheduleJson) {
                objRes = JSON.parse(scheduleJson);
                if (objRes) {
                    availableUser =true;
                    if (availableUser) {
                        return true;
                    } else {
                        return false;
                    }
                }
            }
        }
    }
}

@Pipe({
    name: 'timeZoneOffsetFormat',
    pure: false
})
export class timeZoneOffsetFormatPipe implements PipeTransform {
    transform(offset: any): any {
        offset = offset.split(',')[0];
        let operator, hour, minutes, offsetCopy;
        
        if (offset > 0) {
            operator = '+';
        } else {
            operator = '-';
            offset = offset*-1;
        }
        offsetCopy = offset;
        offset = offset/60;
        hour = parseInt(offset);
        offset = offsetCopy;
        minutes = offset%60;
        if (hour < 10) {
            hour = '0' + hour;
        }
        if (minutes < 10) {
            minutes = '0' + minutes;
        }
        return '(UTC' + operator + hour + ":" + minutes + ')';
    }
}

@Pipe({
    name: 'dateFormat',
    pure: false
})
export class dateFormatPipe extends DatePipe implements PipeTransform {
    transform(date: any): any {
        var dobDay = new Date(date).getDay();
        if (date && !isNaN(dobDay)) {
            date = date.replace(/-/g, '/');
            return super.transform(date, 'MM/dd/yyyy');
        } else {
            return "";
        }
    }
}


@Pipe({
    name: 'secondaryUsers',
    pure: false
})
export class secondaryUsers extends DatePipe implements PipeTransform {
    transform(data: any,priSecUsers:any): any {        
        let retData = data.filter((row)=>{
            if(!priSecUsers.includes(row.id)) {
                return row;
            }
        });        
        return retData;
    }
}
