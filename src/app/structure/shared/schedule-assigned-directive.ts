import { Directive, ElementRef, Input, Renderer2, OnChang<PERSON>, AfterViewInit  } from '@angular/core';
declare var $: any;
@Directive({
    selector: '[scheduleIcon]',
})
export class scheduleIconDirective implements OnChanges, AfterViewInit {
    @Input() scheduleIcon: any;
    constructor(private elRef: ElementRef, private renderer: Renderer2 ) {
        
    }
    ngOnChanges(changes): void {
        if(changes.scheduleIcon.currentValue.schedulesAssigned == 'oncall') {
            // this.elRef.nativeElement.src = "assets/modules/dummy-assets/common/img/icon1.png";
            // this.renderer.removeClass(this.elRef.nativeElement, 'element-hide');
            $('#schedule-icon-oncall-' + changes.scheduleIcon.currentValue.id).removeClass('element-hide');
            $('#schedule-icon-esc-' + changes.scheduleIcon.currentValue.id).removeClass('element-hide');
            $('#schedule-icon-esc-' + changes.scheduleIcon.currentValue.id).addClass('element-hide');            
        } else if(changes.scheduleIcon.currentValue.schedulesAssigned == 'esc') {
            // this.elRef.nativeElement.src = "assets/modules/dummy-assets/common/img/icon2.png";
            // this.renderer.removeClass(this.elRef.nativeElement, 'element-hide');
            $('#schedule-icon-oncall-' + changes.scheduleIcon.currentValue.id).removeClass('element-hide');
            $('#schedule-icon-oncall-' + changes.scheduleIcon.currentValue.id).addClass('element-hide');
            $('#schedule-icon-esc-' + changes.scheduleIcon.currentValue.id).removeClass('element-hide');
        } else if(changes.scheduleIcon.currentValue.schedulesAssigned == 'empty') {
            // this.renderer.removeClass(this.elRef.nativeElement, 'element-hide');
            // this.renderer.addClass(this.elRef.nativeElement, 'element-hide');
            $('#schedule-icon-oncall-' + changes.scheduleIcon.currentValue.id).removeClass('element-hide');
            $('#schedule-icon-oncall-' + changes.scheduleIcon.currentValue.id).addClass('element-hide');
            $('#schedule-icon-esc-' + changes.scheduleIcon.currentValue.id).removeClass('element-hide');
            $('#schedule-icon-esc-' + changes.scheduleIcon.currentValue.id).addClass('element-hide');
        } else {
            //this.elRef.nativeElement.src = "assets/modules/dummy-assets/common/img/icon3.png";
            //this.renderer.removeClass(this.elRef.nativeElement, 'element-hide');
            $('#schedule-icon-oncall-' + changes.scheduleIcon.currentValue.id).removeClass('element-hide');
            $('#schedule-icon-esc-' + changes.scheduleIcon.currentValue.id).removeClass('element-hide');
        }
        
    }
    ngAfterViewInit(): void {
        if(this.scheduleIcon.schedulesAssigned == 'oncall') {
            //this.elRef.nativeElement.src = "assets/modules/dummy-assets/common/img/icon1.png";
            //this.renderer.removeClass(this.elRef.nativeElement, 'element-hide');
            $('#schedule-icon-oncall-' + this.scheduleIcon.id).removeClass('element-hide');
            $('#schedule-icon-esc-' + this.scheduleIcon.id).removeClass('element-hide');
            $('#schedule-icon-esc-' + this.scheduleIcon.id).addClass('element-hide'); 
        } else if(this.scheduleIcon.schedulesAssigned == 'esc') {
            //this.elRef.nativeElement.src = "assets/modules/dummy-assets/common/img/icon2.png";
            //this.renderer.removeClass(this.elRef.nativeElement, 'element-hide');
            $('#schedule-icon-oncall-' + this.scheduleIcon.id).removeClass('element-hide');
            $('#schedule-icon-oncall-' + this.scheduleIcon.id).addClass('element-hide');
            $('#schedule-icon-esc-' + this.scheduleIcon.id).removeClass('element-hide');
        } else if(this.scheduleIcon.schedulesAssigned == 'empty') {
            //this.renderer.removeClass(this.elRef.nativeElement, 'element-hide');
            //this.renderer.addClass(this.elRef.nativeElement, 'element-hide');
            $('#schedule-icon-oncall-' + this.scheduleIcon.id).removeClass('element-hide');
            $('#schedule-icon-oncall-' + this.scheduleIcon.id).addClass('element-hide');
            $('#schedule-icon-esc-' + this.scheduleIcon.id).removeClass('element-hide');
            $('#schedule-icon-esc-' + this.scheduleIcon.id).addClass('element-hide');
        } else {
            //this.elRef.nativeElement.src = "assets/modules/dummy-assets/common/img/icon3.png";
            //this.renderer.removeClass(this.elRef.nativeElement, 'element-hide');
            $('#schedule-icon-oncall-' + this.scheduleIcon.id).removeClass('element-hide');
            $('#schedule-icon-esc-' + this.scheduleIcon.id).removeClass('element-hide');
        }
    }
}