import { TestBed, ComponentFixture } from '@angular/core/testing';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpModule } from '@angular/http';
import { ApolloModule } from 'apollo-angular';
import { NgxLoggerLevel, LoggerModule } from 'ngx-logger';
import { DaterangepickerConfig } from 'ng2-daterangepicker';
import 'rxjs/add/observable/from';
import { provideClients } from 'test-utils';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import * as moment from 'moment';
import { PermissionService } from 'app/services/permission/permission.service';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from '../../tool-tip.service';
import { ChDaterangePickerComponent } from './ch-daterange-picker.component';
import { AuthService } from '../auth.service';
import { SharedService } from '../sharedServices';
import { StoreService } from '../storeService';
import { SharedModule } from '../sharedModule';

describe('ChDaterangePickerComponent', () => {
  let component: ChDaterangePickerComponent;
  let fixture: ComponentFixture<ChDaterangePickerComponent>;
  let structureService: StructureService;
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      providers: [
        ToolTipService,
        DaterangepickerConfig,
        StructureService,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        PermissionService,
        StoreService
      ],
      imports: [
        TranslateModule.forRoot(),
        FormsModule,
        ReactiveFormsModule,
        HttpModule,
        SharedModule,
        RouterTestingModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF
        })
      ]
    }).compileComponents();
  });

  beforeEach(() => {
    structureService = TestBed.get(StructureService);
    fixture = TestBed.createComponent(ChDaterangePickerComponent);
    component = fixture.componentInstance;
    component.dateRangeFilterOptions = ['All', 'Today'];
    component.saveStateInto = 'testSession';

    const store = {};
    spyOn(sessionStorage, 'getItem').and.callFake((key) => {
      return store[key];
    });
    spyOn(sessionStorage, 'setItem').and.callFake((key, value) => {
      store[key] = `${value}`;
      return store[key];
    });
    fixture.detectChanges();
  });

  it('It should create the component', () => {
    expect(component).toBeTruthy();
  });
  it('It should update the date range picker value for All', () => {
    component.updateDateRangePickerValue({ startDate: '', endDate: '' });
    expect(component.control.value).toEqual('LABELS.DATE_RANGES.ALL');
  });
  it('It should show date range warning', () => {
    const value = {
      start: moment(),
      end: moment(),
      label: 'LABELS.DATE_RANGES.ALL'
    };
    spyOn(structureService, 'notifyMessage');
    component.selectedDateRangeFilter(value);
    expect(structureService.notifyMessage).toHaveBeenCalled();
  });
  it('It should emit data on load', () => {
    component.emitOnLoad = true;
    component.dateRangeFilterOptions = [];
    spyOn(component.selectDateRange, 'emit');
    component.ngOnInit();
    expect(component.selectDateRange.emit).toHaveBeenCalled();
  });
  it('It should reset dateRange', () => {
    component.saveStateInto = '';
    component.openPosition = 'right';
    spyOn(component, 'getDefaultDateRange').and.returnValue({ startDate: '2014-01-01', endDate: '2024-01-30', type: 'Last 30 days' });
    const dateRange = component.resetDateRange(true);
    expect(component.getDefaultDateRange).toHaveBeenCalled();
    expect(dateRange).toEqual({ startDate: '2014-01-01', endDate: '2024-01-30', type: 'Last 30 days' });
  });
  it('It should emit the date range as null when a user selected All', () => {
    const value = {
      start: moment(),
      end: moment(),
      label: 'All'
    };
    component.selectedDateRangeFilter(value);

    let emittedValue;
    component.selectDateRange.subscribe((item) => {
      emittedValue = item;
    });
    const output = {
      startDate: '',
      endDate: ''
    };
    component.selectDateRange.emit(output);
    expect(emittedValue).toEqual(output);
  });

  it('It should emit the selected date range ', () => {
    const value = {
      start: moment(),
      end: moment()
    };
    component.selectedDateRangeFilter(value);

    let emittedValue;
    component.selectDateRange.subscribe((item) => {
      emittedValue = item;
    });
    const output = {
      startDate: '2023-08-18',
      endDate: '2023-08-18'
    };
    component.selectDateRange.emit(output);
    expect(emittedValue).toEqual(output);
  });

  it('It should show the warning when a user selected All option or more than 6 months', () => {
    const value = {
      start: moment('2023-01-18'),
      end: moment('2023-08-18'),
      label: 'All'
    };
    component.showDataLoadWarning = true;
    component.selectedDateRangeFilter(value);
    expect(component.showDataLoadWarning).toBeTruthy();
  });

  it('should return default date range when keepSession is false and no stored data', () => {
    component.keepSession = false;
    component.saveStateInto = 'testSession';
    spyOn(component['storeService'], 'getStoredData').and.returnValue(null);
    const defaultDateRange = component.getDefaultDateRange();
    const result = component.resetDateRange(true);
    expect(result).toEqual(defaultDateRange);
  });

  it('should return default date range when keepSession is true and no stored data', () => {
    component.keepSession = true;
    component.saveStateInto = 'testSession';
    spyOn(component['storeService'], 'getStoredData').and.returnValue(null);
    const defaultDateRange = component.getDefaultDateRange();
    const result = component.resetDateRange(true);
    expect(result).toEqual(defaultDateRange);
  });

  it('should return stored date range when keepSession is true and stored data exists', () => {
    component.keepSession = true;
    component.saveStateInto = 'testSession';
    const storedData = JSON.stringify({ startDate: moment(), endDate: moment() });
    spyOn(component['storeService'], 'getStoredData').and.returnValue(storedData);
    const result = component.resetDateRange(true);
    expect(result.type).toEqual('LAST_THIRTY_DAYS');
  });

  it('should remove stored data and return default date range when navigating to a different module', () => {
    component.keepSession = false;
    component.saveStateInto = 'testSession';
    spyOn(component['storeService'], 'getStoredData').and.returnValue(JSON.stringify({ startDate: '2023-08-18', endDate: '2023-08-18' }));
    spyOn(component['storeService'], 'removeData');
    component['structureService'].previousUrlNow = '/module1';
    component['structureService'].currentUrlNow = '/module2';
    const defaultDateRange = component.getDefaultDateRange();
    const result = component.resetDateRange(true);
    expect(component['storeService'].removeData).toHaveBeenCalledWith('testSession');
    expect(result).toEqual(defaultDateRange);
  });

  it('should not remove stored data and return stored date range when navigating within the same module', () => {
    component.keepSession = false;
    component.saveStateInto = 'testSession';
    const storedData = JSON.stringify({ startDate: '2023-08-18', endDate: '2023-08-18' });
    spyOn(component['storeService'], 'getStoredData').and.returnValue(storedData);
    spyOn(component['storeService'], 'removeData');
    component['structureService'].previousUrlNow = '/module1';
    component['structureService'].currentUrlNow = '/module1';
    const result = component.resetDateRange(true);
    expect(component['storeService'].removeData).not.toHaveBeenCalled();
    expect(result).toEqual(JSON.parse(storedData));
  });
});
