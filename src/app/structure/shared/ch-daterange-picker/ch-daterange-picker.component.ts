import { Component, Input, Output, EventEmitter, ChangeDetectionStrategy, OnInit, OnDestroy } from '@angular/core';
import { FormControl } from '@angular/forms';
import { DaterangepickerConfig } from 'ng2-daterangepicker';
import { DateFormat, CONSTANTS, DateRanges } from 'app/constants/constants';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { getMonthRangeData, isBlank } from 'app/utils/utils';
import * as moment from 'moment';
import { StructureService } from 'app/structure/structure.service';
import { DateRangeSelected } from './date-range-selected.interface';
import { StoreService } from '../storeService';

@Component({
  selector: 'ch-daterange-picker',
  providers: [DaterangepickerConfig],
  templateUrl: './ch-daterange-picker.component.html',
  styleUrls: ['./ch-daterange-picker.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ChDaterangePickerComponent implements OnInit, OnDestroy {
  @Output() selectDateRange = new EventEmitter<object>();
  @Input() saveStateInto: string;
  @Input() defaultDateRangeType = DateRanges.LAST_THIRTY_DAYS;
  @Input() dateRangeFilterOptions: Array<string>;
  @Input() control: FormControl;
  @Input() openPosition: string;
  @Input() showDateRangeLabel = false;
  @Input() chToolTipKey = 'defaultDateRangeInfo';
  @Input() emitOnLoad = false;
  // keepSession - To keep the date range filter until user session ends.
  // No need to reset the date range filter when user navigates to other centers.
  @Input() keepSession = false;
  @Input() clearValue: boolean;
  @Input() showDataLoadWarning = true;
  @Input() defaultDateRangeValue: string;
  selectedDateRangeType = <string>DateRanges.LAST_THIRTY_DAYS;
  dateRangeValues = {
    [DateRanges.ALL]: [moment().add(1, 'day'), moment().add(1, 'day')],
    [DateRanges.TODAY]: [moment().startOf('day'), moment()],
    [DateRanges.YESTERDAY]: [moment().startOf('day').subtract(1, 'days'), moment().endOf('day').subtract(1, 'days')],
    [DateRanges.THIS_WEEK]: [moment().startOf('week'), moment().endOf('week')],
    [DateRanges.LAST_SEVEN_DAYS]: [moment().startOf('day').subtract(6, 'days'), moment()],
    [DateRanges.LAST_THIRTY_DAYS]: [moment().startOf('day').subtract(30, 'days'), moment()],
    [DateRanges.LAST_NINETY_DAYS]: [moment().startOf('day').subtract(90, 'days'), moment()],
    [DateRanges.LAST_SIX_MONTHS]: [moment().startOf('day').subtract(6, 'months'), moment()],
    [DateRanges.THIS_MONTH]: [moment().startOf('month'), moment().endOf('month')],
    [DateRanges.LAST_MONTH]: [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
  };
  monthRange = {
    LAST_THIRTY_DAYS: 30,
    LAST_NINETY_DAYS: 90,
    LAST_SIX_MONTHS: 6
  };
  monthRangeType = {
    LAST_THIRTY_DAYS: <moment.unitOfTime.DurationConstructor>'days',
    LAST_NINETY_DAYS: <moment.unitOfTime.DurationConstructor>'days',
    LAST_SIX_MONTHS: <moment.unitOfTime.DurationConstructor>'months'
  };

  constructor(
    private tooltipService: ToolTipService,
    private structureService: StructureService,
    private dateRangePickerOptions: DaterangepickerConfig,
    private storeService: StoreService
  ) {
    this.control = new FormControl(''); // Initialize the FormControl
    // Create options for date range picker
    this.dateRangePickerOptions.settings = {
      locale: { format: DateFormat.MMDDYY_FORMAT_SLASH },
      alwaysShowCalendars: false,
      autoUpdateInput: false,
      linkedCalendars: true,
      autoApply: true,
      parentEl: 'body'
    };
  }

  ngOnInit() {
    const selectedDateRange = this.resetDateRange(true);
    this.updateDateRangePickerValue(selectedDateRange);
    if (this.emitOnLoad) {
      this.selectDateRange.emit({
        ...selectedDateRange,
        type: this.selectedDateRangeType,
        changeDetectedOnInit: this.selectedDateRangeType !== this.defaultDateRangeType
      });
    }
    if(!this.dateRangeFilterOptions.includes('custom')) {
      this.dateRangePickerOptions.settings.ranges = this.getDateRanges(this.dateRangeFilterOptions);
    }
  }

  /**
   * Function to reset or retrieve data from the session
   * @param resetFlag : true for ngOnInit,  false for ngOnDestroy
   * @returns DateRangeSelected
   */
  public resetDateRange(resetFlag = true) {
    let selectedDateRange: DateRangeSelected;
    if (!this.keepSession) {
      // Get date range filter from the local storage
      if (!isBlank(this.saveStateInto) && !isBlank(this.storeService.getStoredData(this.saveStateInto))) {
        // Reset session for date range when the user navigate from other centers
        const currentModule = this.structureService.previousUrlNow.split('/');
        const previousModule = this.structureService.currentUrlNow.split('/');
        // Set default date range filter if any navigation issues occurred.
        if (currentModule.length > 1 && previousModule.length > 1) {
          if (currentModule.length >= 2 && previousModule.length >= 2 && currentModule[1].toLowerCase() !== previousModule[1].toLowerCase()) {
            this.storeService.removeData(this.saveStateInto);
            if (resetFlag) selectedDateRange = this.getDefaultDateRange();
          } else if (resetFlag) {
            selectedDateRange = JSON.parse(this.storeService.getStoredData(this.saveStateInto));
          }
        } else {
          selectedDateRange = this.getDefaultDateRange();
        }
      } else if (resetFlag) {
        selectedDateRange = this.getDefaultDateRange();
      }
    } else {
      const savedData = this.storeService.getStoredData(this.saveStateInto);
      const rangeType = JSON.parse(savedData) && JSON.parse(savedData).type ? JSON.parse(savedData).type : this.defaultDateRangeType;
      selectedDateRange =
        this.saveStateInto && savedData && ['CUSTOM_RANGE', DateRanges.ALL].includes(rangeType)
          ? JSON.parse(this.storeService.getStoredData(this.saveStateInto))
          : this.getDefaultDateRange(rangeType);
    }
    if (selectedDateRange && selectedDateRange.type) {
      this.selectedDateRangeType = selectedDateRange.type;
    }
    if (!isBlank(this.openPosition)) {
      this.dateRangePickerOptions.settings.opens = this.openPosition;
    }
    return selectedDateRange;
  }

  /**
   * Function to get the default date range and save to session
   * @returns  selectedDateRange <DateRangeSelected>
   */
  public getDefaultDateRange(rangeType = this.defaultDateRangeType) {
    const selectedDateRange: DateRangeSelected =
      rangeType === DateRanges.ALL
        ? { startDate: '', endDate: '' }
        : getMonthRangeData(DateFormat.YYMMDD_FORMAT_HYPHEN, this.monthRange[rangeType], this.monthRangeType[rangeType]);
    if (!isBlank(this.saveStateInto)) {
      this.storeService.storeData(this.saveStateInto, JSON.stringify({ ...selectedDateRange, type: rangeType }));
    }
    return { ...selectedDateRange, type: rangeType };
  }

  /**
   * Function to update date range picker in UI
   * This will show daterange in the input box when we select any options except 'All'
   * This will show 'all' in the input box when we select option 'All'
   * @param dateRange DateRangeSelected which will contain selected start and end date
   */
  public updateDateRangePickerValue(dateRange: DateRangeSelected) {
    if (!isBlank(dateRange.startDate) && !isBlank(dateRange.endDate)) {
      this.control.patchValue(
        `${moment(dateRange.startDate).format(DateFormat.MMDDYY_FORMAT_SLASH)} - ${moment(dateRange.endDate).format(DateFormat.MMDDYY_FORMAT_SLASH)}`
      );
      this.dateRangePickerOptions.settings.startDate = moment(dateRange.startDate).format(DateFormat.MMDDYY_FORMAT_SLASH);
      this.dateRangePickerOptions.settings.endDate = moment(dateRange.endDate).format(DateFormat.MMDDYY_FORMAT_SLASH);
    } else {
      this.control.patchValue(this.tooltipService.getTranslateData('LABELS.DATE_RANGES.ALL'));
      this.dateRangePickerOptions.settings.startDate = moment().add(1, 'day');
      this.dateRangePickerOptions.settings.endDate = moment().add(1, 'day');
    }
  }

  /**
   * Function to get the details from ch-daterange-picker when a user selected a date range
   * @param value object Values get from the ch-daterange-picker
   */
  public selectedDateRangeFilter(value: any) {
    const translatedData = this.tooltipService.getTranslateData('LABELS.DATE_RANGES');
    this.selectedDateRangeType = <string>(Object.keys(translatedData).find((key) => translatedData[key] === value.label) || 'CUSTOM_RANGE');
    let selectedDateRange: DateRangeSelected;
    // check whether user selected All option or not
    if (this.selectedDateRangeType === DateRanges.ALL) {
      // set start date and end date as null when user select all
      selectedDateRange = {
        startDate: '',
        endDate: ''
      };
    } else {
      // set start date and end date when user select option other than all
      selectedDateRange = {
        startDate: value.start.format(DateFormat.YYMMDD_FORMAT_HYPHEN),
        endDate: value.end.format(DateFormat.YYMMDD_FORMAT_HYPHEN)
      };
    }
    this.control.setValue(selectedDateRange);
    this.updateDateRangePickerValue(selectedDateRange);
    if (!isBlank(this.saveStateInto)) {
      this.storeService.storeData(this.saveStateInto, JSON.stringify({ ...selectedDateRange, type: this.selectedDateRangeType }));
    }
    this.selectDateRange.emit({ ...selectedDateRange, type: this.selectedDateRangeType });
    this.showDateRangeWarning(value, selectedDateRange);
  }

  /**
   * Function to get the date range picker options
   * @param ranges array of daterange options, empty array, not provided
   * use 'custom' value for ranges to show only the calendar for pickindate range
   * @returns Object dateRanges
   */
  private getDateRanges(ranges?: Array<string>): object {
    const translatedData = this.tooltipService.getTranslateData('LABELS.DATE_RANGES');
    const dateRanges = {};
    if (!isBlank(ranges) && ranges.length) {
      ranges.forEach((opt) => {
        dateRanges[translatedData[opt]] = this.dateRangeValues[opt];
      });
    } else {
      Object.keys(DateRanges).forEach((key) => {
        dateRanges[translatedData[key]] = this.dateRangeValues[key];
      });
    }
    return dateRanges;
  }
  /**
   *If 'All' option or more than 6 months is selected,and if showDataLoadWarning is true.
   It should show the warning as the record fetching will get some time
   */
  private showDateRangeWarning(value, selectedDateRange) {
    if (
      this.showDataLoadWarning &&
      (moment(selectedDateRange.startDate).diff(moment(selectedDateRange.endDate), 'months') < -6 ||
        value.label === this.tooltipService.getTranslateData('LABELS.DATE_RANGES.ALL'))
    ) {
      const data = {
        messge: this.tooltipService.getTranslateData('MESSAGES.MORE_TIME_TO_LOAD_RECORDS'),
        type: CONSTANTS.notificationTypes.warning
      };
      this.structureService.notifyMessage(data);
    }
  }
  ngOnDestroy(): void {
    this.resetDateRange(false);
  }
  ngOnChanges(): void {
    if (this.clearValue) {
      if(!isBlank(this.defaultDateRangeValue) && this.defaultDateRangeValue === this.defaultDateRangeType) {
        this.updateDateRangePickerValue({startDate: moment().startOf('day').subtract(30, 'days').format(DateFormat.YYMMDD_FORMAT_HYPHEN), endDate: moment().format(DateFormat.YYMMDD_FORMAT_HYPHEN)});
      } else {
        this.updateDateRangePickerValue({startDate:'', endDate: ''});
      }
    }
  }
}
