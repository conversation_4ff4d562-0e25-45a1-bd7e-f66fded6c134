<div *ngIf="showDateRangeLabel; else dateRangePicker" class="date-picker-container">
  <div class="date-picker-label">
    {{ 'LABELS.DATE_RANGE' | translate }}
    <i [chToolTip]="chToolTipKey" class="icmn-info date-range-info" data-animation="false"><span class="date-range-label-colon">:</span></i>
  </div>
  <div [ngTemplateOutlet]="dateRangePicker" class="date-picker-content"></div>
</div>
<ng-template #dateRangePicker>
  <input daterangepicker class="form-control adv-form-control" [formControl]="control" (selected)="selectedDateRangeFilter($event)" />
</ng-template>
