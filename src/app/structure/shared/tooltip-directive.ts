import { Directive, ElementRef, Input, AfterViewInit, Renderer2 } from '@angular/core';
import { ToolTipService } from '../tool-tip.service';
declare var $: any;
@Directive({
    selector: '[chToolTip]',
})
export class ToolTipDirective implements AfterViewInit {
    @Input() chToolTip: string;
    toolTipTiitle;
    constructor(private elRef: ElementRef, private _ToolTipService: ToolTipService,private renderer: Renderer2 ) {
    }
    ngAfterViewInit(): void {
        console.log("tooltip--->", this.chToolTip);
        if (this.chToolTip){
            this.toolTipTiitle = this._ToolTipService.getToolTipTitle(this.chToolTip);
            console.log("toolTipTiitle-----> ",this.toolTipTiitle);
        }

        if (this.toolTipTiitle) {
            this.elRef.nativeElement.setAttribute("data-original-title", this.toolTipTiitle);
            console.log("this.elRef.nativeElement---->",this.elRef.nativeElement.tagName);
            if(this.elRef.nativeElement.tagName == 'I'){
            this.renderer.addClass(this.elRef.nativeElement, "icmn-info");
            }
            $(this.elRef.nativeElement).tooltip();
        }
    }
}