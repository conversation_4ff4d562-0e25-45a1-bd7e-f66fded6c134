<div class="row dataTables_wrapper" *ngIf="showSearchBar">
  <div class="col-sm-2">
    <div class="dataTables_length" id="datatable-list_length">
      <label>
        {{ 'LABELS.SHOW' | translate }}
        <select
          [(ngModel)]="rowsPerPage"
          name="datatable-list_length"
          aria-controls="datatable-list"
          class="form-select form-select-sm"
          (change)="dataChanges.emit({ rowsPerPage: rowsPerPage })"
        >
          <option [value]="option" *ngFor="let option of rowsPerPageOptions">{{ option }}</option>
        </select>
        {{ 'LABELS.ENTRIES' | translate }}
      </label>
    </div>
  </div>
  <div class="col-sm-3">
    <div id="datatable-list_filter" class="">
      <input
        type="search"
        class="form-control form-control-sm"
        placeholder="Search"
        aria-controls="datatable-list"
        [value]="searchText"
        (change)="searchText = $event.target.value"
        (keyup.enter)="onKeyUp($event)"
      />
    </div>
  </div>
  <div class="col-sm-2">
    <button
      class="btn btn-sm btn-info search-in-table"
      title="{{ 'BUTTONS.SEARCH' | translate }}"
      type="submit"
      (click)="dataChanges.emit({ searchText: searchText })"
    >
      {{ 'BUTTONS.SEARCH' | translate }}
    </button>
    <button
      style="margin-left: 10px"
      class="btn btn-sm btn-default reset-in-table"
      title="{{ 'BUTTONS.RESET' | translate }}"
      type="submit"
      (click)="searchText = ''; dataChanges.emit({ reset: true })"
    >
      {{ 'BUTTONS.RESET' | translate }}
    </button>
  </div>
  <div class="col-sm-4" *ngIf="showDateFilter">
    <ch-daterange-picker
      [dateRangeFilterOptions]="dateRangeFilterOptions"
      [saveStateInto]="dateRangeStoreKey"
      [showDateRangeLabel]="true"
      [defaultDateRangeType]="defaultDateRangeType"
      [emitOnLoad]="true"
      [chToolTipKey]="chToolTipKey"
      (selectDateRange)="dataChanges.emit({ dateRangeSelected: $event })"
    >
    </ch-daterange-picker>
  </div>
</div>
<section class="card" *ngIf="tableData">
  <table class="table table-hover" [id]="" width="100%">
    <thead class="thead-light" *ngIf="tableData.length > 0">
      <tr>
        <th class="th-col-style1 sorting" *ngFor="let header of tableHeaders">{{ header.label | translate }}</th>
      </tr>
    </thead>
    <tr *ngFor="let item of tableData; let i = index" id="{{ i }}">
      <td class="th-col-style2" *ngFor="let header of tableHeaders">
        <ng-container [ngSwitch]="header.type">
          <span *ngSwitchCase="'text'">{{ item[header.key] }}</span>
          <span *ngSwitchCase="'callback'">{{ header.callback && header.callback(item) }}</span>
          <div *ngSwitchCase="'actionButtons'" class="btn-group mr-2 mb-2 no-margin">
            <span *ngFor="let button of header.buttons">
              <a
                class="pull-right btn btn-sm"
                [title]="button.title(item) | translate"
                [id]="button.id"
                (click)="button.action && button.action(item)"
                [hidden]="button.hide && button.hide(item)"
                [ngClass]="{ disabled: button.isEnabled && !button.isEnabled(item) }"
              >
                <i [class]="button.class(item)"></i>
              </a>
            </span>
          </div>
        </ng-container>
      </td>
    </tr>
    <tr *ngIf="tableData.length === 0">
      <td colspan="6" style="text-align: center">{{ noDataText | translate }}</td>
    </tr>
  </table>
</section>
<div class="row" *ngIf="totalCount > 0">
  <div class="col-sm-5 pagination-footer">
    {{
      'LABELS.PAGINATION_COUNT_TABLE_FOOTER'
        | translate
          : {
              fromCount: (currentPage - 1) * rowsPerPage + 1,
              totalCount: totalCount,
              toCount: (currentPage - 1) * rowsPerPage + tableData?.length
            }
    }}
  </div>
  <div class="col-sm-7 inbox-pagination" *ngIf="totalCount > rowsPerPage">
    <ngb-pagination
      [collectionSize]="totalCount"
      [pageSize]="rowsPerPage"
      [(page)]="currentPage"
      [maxSize]="8"
      [rotate]="true"
      responsive="true"
      [directionLinks]="true"
      aria-label="Pagination"
      (pageChange)="dataChanges.emit({ navigatePages: $event })"
    >
    </ngb-pagination>
  </div>
</div>
