import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { Apollo, ApolloModule } from 'apollo-angular';
import { HttpService } from 'app/services/http/http.service';
import { PermissionService } from 'app/services/permission/permission.service';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { provideClients } from 'test-utils';
import { DataTableComponent } from './data-table.component';
import { AuthService } from '../auth.service';
import { SharedService } from '../sharedServices';
import { StoreService } from '../storeService';

describe('DataTableComponent', () => {
  let component: DataTableComponent;
  let fixture: ComponentFixture<DataTableComponent>;
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [DataTableComponent],
      providers: [
        HttpService,
        TranslateService,
        StructureService,
        Apollo,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        PermissionService,
        StoreService
      ],
      imports: [
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        FormsModule,
        ReactiveFormsModule,
        RouterTestingModule,
        HttpModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF
        })
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });
  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });
  beforeEach(() => {
    fixture = TestBed.createComponent(DataTableComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
  describe('onKeyUp', () => {
    let dataChangeSpy;
    beforeEach(() => {
      dataChangeSpy = jasmine.createSpy('dataChangeSpy');
      component.dataChanges.subscribe(dataChangeSpy);
    });
    it('should emit dataChanges event with reset flag when search text is empty', () => {
      const event = { target: { value: '' } };
      component.onKeyUp(event);
      expect(component.searchText).toBe('');
      expect(dataChangeSpy).toHaveBeenCalledWith({ reset: true });
    });

    it('should emit dataChanges event with searchText when search text is not empty', () => {
      const searchText = 'example';
      const event = { target: { value: searchText } };
      component.onKeyUp(event);
      expect(component.searchText).toBe(searchText);
      expect(dataChangeSpy).toHaveBeenCalledWith({ searchText });
    });
  });
});
