import { Component, EventEmitter, Input, Output } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { DateRanges, LISTING_PAGE_DATERANGE_OPTIONS } from '../../../constants/constants';

@Component({
  selector: 'app-data-table',
  templateUrl: './data-table.component.html',
  styleUrls: ['./data-table.component.scss'],
  providers: [TranslatePipe]
})
export class DataTableComponent {
  @Input() currentPage = 1;
  @Input() rowsPerPage = 25;
  @Input() rowsPerPageOptions: number[] = [25, 50];
  @Input() totalCount = 0;
  @Input() showSearchBar = true;
  @Input() tableData: any[] = [];
  @Input() tableHeaders: any[] = [];
  @Input() noDataText = '';
  @Input() defaultDateRangeType = DateRanges.LAST_THIRTY_DAYS;
  @Input() showDateFilter = true;
  @Input() dateRangeStoreKey;
  @Input() chToolTipKey = 'defaultDateRangeInfo';
  @Input() dateRangeFilterOptions = LISTING_PAGE_DATERANGE_OPTIONS;
  @Output() navigatePages = new EventEmitter();
  @Output() changeLimit = new EventEmitter();
  @Output() search = new EventEmitter();
  @Output() dateRangeSelected = new EventEmitter();
  @Output() dataChanges = new EventEmitter();
  searchText = '';
  onKeyUp(event: any) {
    this.searchText = event.target.value;
    if (!this.searchText) {
      this.dataChanges.emit({ reset: true });
    } else {
      this.dataChanges.emit({ searchText: this.searchText });
    }
  }
}
