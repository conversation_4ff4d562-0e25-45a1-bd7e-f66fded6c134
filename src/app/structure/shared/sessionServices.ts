import { Component, Injectable, Injector ,Input, Output, EventEmitter } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Apollo } from 'apollo-angular';
import { Subject } from 'rxjs/Subject';
import { Router } from '@angular/router';
import { io } from "socket.io-client";
import { isPresent } from 'app/utils/utils';
import { WorklistIndexdbService} from './../../structure/worklists/worklist-indexdb.service';
import {
  configEducationMaterialUrl,
  configBaseUrl,
  configTimeZone,
  configPlatform,
  configVersion,
  configEnvironment,
  configactivityTrackingApi,
  configSocketIo,
  configPushServerAPIUrl
} from "../../../environments/environment";
import { StructureService } from './../../structure/structure.service';
import { AuthService } from './auth.service';
import { SharedService } from './sharedServices';
import { CommonVideoService } from "../../../assets/lib/universal-video/common-video.service";
import { RegistrationService } from '../registration/registration.service';
import { ToolTipService } from './../../structure/tool-tip.service';
import {CONSTANTS} from './../../constants/constants';
import { PermissionService } from 'app/services/permission/permission.service';
import { CONFIG } from 'custom-configs';

declare var $: any;
declare var moment: any;
declare var swal: any;
declare var Notification;
declare var NProgress: any;
declare var TelemetryAgent:any;
declare const notifyMe:any;

interface QueryResponse {
  getAccounts: any;
  staffRoles: any;
  getSessionTenant: any;
  getTenantPatientRoles: any;
  getAccount: any;
  removeUserRole: any;
  deleteTag: any;
  getChatRoomMembers: any;
}

@Injectable()
export class SessionService {
  public loginObservable;
  public activePatientActivityDetails = [];
  pulseMenuShow: any = true;
  displayProgress: EventEmitter<any> = new EventEmitter<any>();
  activePatientActivityHubPatientId: any;
  activePatientActivityHubTabId: any;
  activePatientActivityHubFilterPahDetails: any = [];
  //assetsUrl:string= (configEducationMaterialUrl() && configEducationMaterialUrl().indexOf('assets.')!=-1) ? configEducationMaterialUrl().replace("/c/education-materials","").replace("devl.","") : "https://assets.citushealth.com";
  apiBaseUrl;
  timezone;
  platform;
  rootActivity;
  parentActivity;
  activityHierarchy;
  version;
  environment;
  inboxMessageResultsCount = 0;
  isPrivate:any;
  activityTrackingApi;
  flag: any = 0;
  updateCookie:any = false;
  telemetryObject: any;
  activityLogFileName = "activity_log.json";
  socket ;
  pushServerAPIUrl;
  countNProgress = 0;
  countNProgressArr = [];
  isLogin = false;
  signatureRequestInboxData:any;
  activePatientActivityHub: any = false;
  inboxData;
  archiveData;
  loginUserDetails = [];
  loginUserDetailsForRouteCheck:any = {};
  userDetails:any;
  userDataConfig:any;
  signatureListServerSidePagination = false;
  loginDetails: any;
  flagstop=true;
  
  constructor(private http: HttpClient, private _injector: Injector,private apollo: Apollo,public _worklistIndexdbService :WorklistIndexdbService,private _idpAuth: AuthService,
    private permissionService: PermissionService) {
    this.loginObservable = new Subject<string>();
    this.http = http;
    //this._structureService = _structureService;
    this.apiBaseUrl = configBaseUrl();
    this.timezone = configTimeZone();
    this.platform = configPlatform();
    this.version = configVersion();
    this.environment = configEnvironment();
    this.activityTrackingApi = configactivityTrackingApi();
    this.pushServerAPIUrl = configPushServerAPIUrl();
    
   }
  
    // Helper property to resolve the service dependency.
  public get _router() { return this._injector.get(Router); }
  
  public get _structureService() { return this._injector.get(StructureService); }

  public get _sharedService() { return this._injector.get(SharedService); }

  public get  registrationservice() { return this._injector.get(RegistrationService); }
  
  public get _commonVideoService() { return this._injector.get(CommonVideoService); }
  
  public get _ToolTipService(){ return this._injector.get(ToolTipService); } 
  
  ssoBrandingURLChecking(useHash = false): boolean{
    return this._structureService.ssoBrandingURLChecking(useHash);
  }
  
  get appVersion() {
    // eslint-disable-next-line no-underscore-dangle
    return this._sharedService.appVersion;
  }
  getSessionData() {  
            var activeTimeZone = ((((new Date().getTimezoneOffset()) * -1) * -1) / 60) * -1;

            var timeToPassDay = [];
            var timeDifference;
            if(activeTimeZone > this.timezone) {
                timeDifference = -1 * Math.abs(this.timezone - activeTimeZone);
            } else {
                timeDifference = Math.abs(this.timezone - activeTimeZone);
            }
            if(((timeDifference) + '').split('.').length == 2) {
                timeToPassDay = ((timeDifference) + '').split('.');
            } else {
                timeToPassDay = ((timeDifference) + '').split('.');
                timeToPassDay.push("0");
            }
            
            if(timeToPassDay[1] == '5') {
                if(timeDifference < 0) {
                    timeToPassDay[1] = '-30';       
                } else {
                    timeToPassDay[1] = '30';
                }
            } else {
                timeToPassDay[1] = '0';
            }
            if(timeToPassDay[0] == '-0') {
                timeToPassDay[0] = '0';
            }
            
            timeToPassDay = timeToPassDay.map(function(time) {
                return parseInt(time, 10);
            });
    let data = {
      date : moment().add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('YYYYMMDD'),
      dayNumber : moment().add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').day(),
      currentTime : moment().add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('HH:mm'),
      clientLoginedTimeZone : (new Date().getTimezoneOffset()) * -1
    };
           
            let loaddata:any = {

              data: '&date=' + data.date + '&dayNumber=' + data.dayNumber + '&currentTime=' + data.currentTime
                   + '&clientLoginedTimeZone=' + data.clientLoginedTimeZone 
                  };
                      
   let headers = new HttpHeaders({
     'Content-Type': 'application/json', 
     'Authentication-Token': localStorage.getItem('authenticationToken')  
     });

   let options = {headers: headers}
     console.log('loaddata===>'+JSON.stringify(loaddata.data));
      const promise = new Promise((resolve, reject) => {
        const apiURL = this.apiBaseUrl + "citus-health/v4/get-user-session-data-by-token.php?"+loaddata.data;           
                this.http.get(apiURL, options)
                .toPromise()
                .then(
                res => {
                  const result:any = res;
                  console.log('result--->',result);
                  if(result && "response" in result && result.response && "authenticationToken" in result.response) {
                    if ("status" in result.response && (result.response.status === 0 || result.response.status === 6)) {
                      this.setCookie('authenticationToken', result.response.authenticationToken, 1); 
                      const enableSavesignatureToProfile = result.response.config ? result.response.config.enable_save_signature_to_profile : '';
                      if (enableSavesignatureToProfile === '1') {
                        sessionStorage.setItem('useSavedSign', JSON.stringify(result.response.useSavedSign));
                      }
                      const oktaStorage = localStorage.getItem('okta-token-storage');
                      const previousOktaTokenExpiry = JSON.parse(localStorage.getItem('oktaTokenExpiry'));
                      const  oktaTokenDetails = JSON.parse(oktaStorage);

                      if (previousOktaTokenExpiry && oktaTokenDetails && oktaTokenDetails.accessToken && previousOktaTokenExpiry !== JSON.parse(oktaStorage).accessToken.expiresAt) {
                        this._structureService.updateOktaAccessToken();
                      }
                    }
                  }
                  resolve(result);
                }
                );     
      });
      return promise;
            
   // })
  }
  
  getCookie(name: string,trackEmptyToken = true) {
    var userData = this.getUserdata();
    //console.log('getCookie---userdat--',userData)
    //console.log("GetCookie... Enter===> "+name);
    const ca: Array<string> = document.cookie.split(';');
    const caLen: number = ca.length;
    const cookieName = `${name}=`;
    let c: string;
    var self=this;
    for (let i = 0; i < caLen; i += 1) {
      c = ca[i].replace(/^\s+/g, '');
      if (c.indexOf(cookieName) === 0) {
        var returnData = c.substring(cookieName.length, c.length);
        if(name=="authenticationToken"){
          if((returnData=="" || (userData && userData['authenticationToken']==""))&& trackEmptyToken){ 
            let activityData = {
            activityName: "Tocken issue",
            activityType: 'token not found',
            activityDescription: "Token empty For User (" + (userData && userData['displayName']) + "),userId ("+(userData && userData['userId'])+"), cookieName("+cookieName+"), cookie compared key("+c+")"
          };
          self.trackActivity(activityData);

        }

          returnData = (returnData && returnData !="" )?returnData:(userData)?userData['authenticationToken']:"";
           
        }
        return returnData;
      }
    }
    return '';
  }

  getUserdata() {
    if (this.userDetails) {
      return JSON.parse(this.userDetails);
    }
    else{
      return {};
    }
  }

  trackActivity(activityData,logLevel=1) {

    var linkageId = activityData.activityLinkageId ? activityData.activityLinkageId : '';
    var userData:any = {};     
    if(this.userDetails){
      userData = JSON.parse(this.userDetails); 
    }
    var userId = (userData && userData.userId) || activityData.userId || 0;
    var latitude = (userData && userData.latitude) || "";
    var longitude = (userData && userData.longitude) || "";
    var tenantId = (userData && userData.tenantId) || activityData.tenantId || 0;
    var location_enabled = 0;
    var notification_enabled = 0;
    var notification_description = 'unknown';
    //console.log("Notification: ",Notification.permission);
    if ("Notification" in window) {
      if(Notification.permission == "granted")
        notification_enabled = 1;
      notification_description = Notification.permission;
    }
    if (latitude || longitude) {
      location_enabled = 1;
    }

    if (this.inboxMessageResultsCount) {
      activityData.activityDescription = activityData.activityDescription + "." + " Inbox message results count: " + this.inboxMessageResultsCount;
      this.inboxMessageResultsCount = 0;
    }
    activityData.activityDescription = encodeURIComponent(activityData.activityDescription) + "." + " Client Time: " + new Date()+". "+"Notification permission is: "+notification_description+", appVersion: "+this.appVersion;
    if(this.isPrivate && (this.isPrivate == 'Private browsing' || this.isPrivate == 'Cannot detect Private browsing')) {
      activityData.activityDescription = activityData.activityDescription + ". (" + this.isPrivate + ")";
    }

    if (activityData.activityDescription === 'Current Page - start. New Page - login') {
      this.resetActivity();
    }
    let data = "activityName=" + activityData.activityName
      + "&activityType=" + activityData.activityType
      + "&activityDescription=" + activityData.activityDescription
      + "&createdBy=" + userId
      + "&rootId=" + (this.rootActivity || 0)
      + "&parentId=" + (this.parentActivity || 0)
      + "&activityHierarchy=" + (this.activityHierarchy || '')
      + "&appVersion=" + this.version
      + "&tenantId=" + tenantId
      + "&environment=" + this.environment
      + "&platform=" + this.platform
      + "&testcase=''"
      + "&linkageId=" + linkageId
      + "&latitude=" + latitude
      + "&longitude=" + longitude
      + "&location_enabled=" + location_enabled
      + "&notification_enabled=" + notification_enabled;
      if(activityData.createdFor){
        data += "&createdFor=" + activityData.createdFor;
      }
      data += "&deskVersion=" + this.appVersion;
      if(this.getCookie('crossTenantId') && this.getCookie('crossTenantId') !== 'undefined' && this.getCookie('tenantId')!==this.getCookie('crossTenantId')){
        data += "&crossTenantId=" + this.getCookie('crossTenantId');
      } else {
        data += "&crossTenantId=" + tenantId;
      }
    //console.log("data", data);

    
  if(logLevel==1 || logLevel==3){

      let headers = new HttpHeaders({
        'Content-Type': 'application/json',
        'Authentication-Token': this.getCookie('authenticationToken',false)
       });
      let options = {headers: headers }
      const promise = new Promise((resolve, reject) => {
        // const apiURL = this.activityTrackingApi;
        const apiURL = this.apiBaseUrl + 'citus-health/v4/track-activity.php';
        this.http.post(apiURL, data, options)
          .toPromise()
          .then(
          res => {
            let activityId: any = res;
            console.log("this.flag",this.flag);
           if(activityId && activityId.updateCookie)
           {
            this.updateCookie = activityId.updateCookie;
            var self = this;
            if(this.updateCookie == true && this.flag == 0){
             setTimeout(function () {
              swal({
                title: "Are you sure?",
                text: "We noticed you have an older version of CitusHealth. Please click on `Refresh` button to clear your browser cache.",
                type: "warning",
                showCancelButton: true,
                cancelButtonClass: "btn-default",
                confirmButtonClass: "btn-warning",
                cancelButtonText: "Cancel",
                confirmButtonText: "Refresh",
                closeOnConfirm: true
              }, (confirm) => {
                if(confirm) {
                  window.location.reload(true);
                }
                else
                {
                  self.flag = 1;
                }
              })
            }, 1000);
            }
            if (activityData.activityName === 'User Logout' || activityData.activityName === 'Session Timeout') {
              this.activityHierarchy = '';
            } else {
              activityId = parseInt(activityId.activity_id);
              if (activityId.activity_id) {
                this.setRootActivity(activityId.activity_id);
                this.setParentActivity(activityId.activity_id);
              }
            }
          }
          else
          {
            if (activityData.activityName === 'User Logout' || activityData.activityName === 'Session Timeout') {
              this.activityHierarchy = '';
            } else {
              activityId = parseInt(activityId);
              if (activityId) {
                this.setRootActivity(activityId);
                this.setParentActivity(activityId);
              }
            }
          }




            let teleCustomData = { customData: {} };
            teleCustomData.customData = {
              "Activity Id": activityId,
              "Created By": userId,
              "Activity Type": activityData.activityType,
              "Activity Name": activityData.activityName,
              "Activity Description": activityData.activityDescription,
              "Root Id": (this.rootActivity || 0),
              "Parent Id": (this.parentActivity || 0),
              "Activity Hierarchy": (this.activityHierarchy || ''),
              "App Version": this.version,
              "Tenant Id": tenantId,
              "Environment": this.environment,
              "Platform": this.platform,
              "Test Case": '',
              "Role Name": (userData && userData.roleName) || '',
              "Role Id": (userData && userData.roleId) || ''
            };
            if (activityData.activityName == 'User Login') {

              this.telemetryObject.pageData.setNotifyLogin(function (requestData) {
                requestData['statName'] = activityData.activityName;
                requestData['userName'] = (userData && userData.displayName) || '';
                return requestData;
              }, teleCustomData);
            } else if (activityData.activityName == 'User Logout') {
              this.telemetryObject.pageData.setNotifyLogout(function (requestData) {
                requestData['statName'] = activityData.activityName;
                return requestData;
              }, teleCustomData);
            } 
               console.log('this.telemetryObject'+JSON.stringify(this.telemetryObject)); 
               this.telemetryObject.pageData.Event(activityData.activityType, activityData.activityName,
                 activityData.activityDescription, 1, teleCustomData, this._router.url);

            resolve([]);
          }
          );
      });
      return promise;
    }
  }

  setRootActivity(activityId) {
    if (!this.rootActivity) {
      this.rootActivity = activityId;
    }
  }

  setParentActivity(activityId) {
    this.parentActivity = activityId;
    this.activityHierarchy = (this.activityHierarchy || '') + activityId + (activityId ? '::' : '');
  }

  resetActivity() {
    this.rootActivity = '';
    this.parentActivity = '';
    this.activityHierarchy = '';
  }

  requestData(apiConfig) {
    let subscription;
    let subscriptionConfig;
    //console.log("countNProgress - ", this.countNProgress);
    let requestConfig = {
      requestType: apiConfig.requestType ? apiConfig.requestType : 'gql', // gql or http
      method: apiConfig.method ? apiConfig.method : 'GET',                // GET or POST
      data: apiConfig.data ? apiConfig.data : '',                         // if gql its
      variables: apiConfig.variables ? apiConfig.variables : {},
      use: apiConfig.use ? apiConfig.use : 'default',
      url: apiConfig.url ? apiConfig.url : '',
      nested: apiConfig.nested ? apiConfig.nested : false,
      noLoader: apiConfig.noLoader ? apiConfig.noLoader : false,          // no need loader- noLoader=true
      useCache: apiConfig.useCache ? apiConfig.useCache : false,          // for subscribe only or refetch
      replaceHeaders: apiConfig.replaceHeaders ? true : false,
      headerParams: apiConfig.replaceHeaders ? apiConfig.headers : null   
    };

    if (requestConfig.noLoader == false) {
      this.countNProgress += 1;
      if (this.countNProgress > 0) {
        NProgress.start();
      }
    }

    if (requestConfig.requestType == 'gql') {
      const promise = new Promise((resolve, reject) => {
          this.subRequestData(requestConfig,apiConfig, function(data) {
            resolve(data);
          });
      });
      return promise;
    } else if (requestConfig.requestType == 'http') {
      const apiURL = requestConfig.url;
      const data = requestConfig.data;
      const authenticationToken = this.getCookie('authenticationToken');
      console.log('authenticationToken'+authenticationToken);
      const headers = new HttpHeaders();
      if(!requestConfig.replaceHeaders) {
        headers.append('Content-Type', 'application/X-www-form-urlencoded');
        headers.append('Authentication-Token', authenticationToken);
      } else {
        console.log(requestConfig.headerParams);
        for (var key in requestConfig.headerParams) {
          if (requestConfig.headerParams.hasOwnProperty(key)) {
            headers.append(key, requestConfig.headerParams[key]);
          }
        }
      }     
    
      const options = {headers: headers }
      const promise = new Promise((resolve, reject) => {
        this.http.post(apiURL, data, options)
          .toPromise()
          .then(
          res => {
            
            const result:any = res;
            if(result.status==401){
              if(result.method && result.method!="OPTIONS"){
                var messages={
                  requestType:requestConfig.requestType,
                  method:requestConfig.method,
                  url:requestConfig.url,
                  data : requestConfig.data,
                  authenticationToken : authenticationToken
                }
                this.apiLogout(messages);
              }
              reject(result);
            }else{
              if (requestConfig.noLoader == false) {
                this.countNProgress -= 1;
                if (this.countNProgress == 0 && requestConfig.nested == false) {
                  NProgress.done();
                }
              }
              
              resolve(result);
            }
          },err => {
            var requestDetails = {
              method: requestConfig.method,
              type: requestConfig.requestType,
              endpoint: requestConfig.url
            };
            let activityData = {
              activityName: "API Exception",
              activityType: 'exception handler',
              activityDescription: "[CitusHealth Error] - request data - " + JSON.stringify(requestDetails) + ". Error message - " + err + " (" + JSON.stringify(requestConfig) + ") For User (displayname :"+this.getCookie('displayname')+",userId : "+this.getCookie('userId')+",token : "+authenticationToken+")"
            };
            this.trackActivity(activityData);
            if (requestConfig.noLoader == false) {
              this.countNProgress -= 1;
              if (this.countNProgress == 0) {
                this.countNProgressArr = [];
                NProgress.done();
              }
            }
            reject(err);
          });
      });

      return promise;

    }
  }

  generateVidyoTocken(params) {
    console.log('----generateVidyoTocken-----');
    console.log(params);
    let apiConfig = {
        method: 'POST',
        apiBaseUrl: this.pushServerAPIUrl,
        url: this.pushServerAPIUrl+'generateVidyoTocken',
        headers: {
            'Content-Type': 'application/json'
        },
        data: JSON.stringify(params),
        requestType: 'http',
        replaceHeaders: true
    };
    console.log(apiConfig)
    let responce = this.requestData(apiConfig);
    return responce;
  }

  apiLogout(messages:any={}){
    console.log("applogout callllll");
    let activityData = {
      activityName: "Session Timeout",
      activityType: 'user access',
      activityDescription:  "User (displayname :"+this.getCookie('displayname')+",userId : "+this.getCookie('userId')+",token : "+this.getCookie('authenticationToken')+") session has expired due to API authentication expired"
    };
    if(messages && messages.requestType){
      
      var userData = JSON.parse(this.userDetails);
      var displayName = this.getCookie('displayname');
      var userId = this.getCookie('userId');
      var authenticationToken = this.getCookie('authenticationToken');
      displayName = (displayName)?displayName:(userData && userData.displayName)?userData.displayName:"";
      userId = (userId)?userId:(userData && userData.userId)?userData.userId:"";
      authenticationToken = (messages.authenticationToken)?messages.authenticationToken:(userData && userData.authenticationToken)?userData.authenticationToken:"";

      activityData.activityDescription = "User (displayname :"+displayName+",userId : "+userId+",token : "+authenticationToken+" ) automatically logout due to invalid token ("+JSON.stringify(messages)+")";
    }
    this.trackActivity(activityData);
    this.logout(true);
  }

  logout(condition=false) { 
    $.notifyClose();
    this.countNProgress = 0;
    if (this.countNProgress == 0) {
      NProgress.done();
    }
    
    var userData = JSON.parse(this.userDetails);
    var authToken = '';
    if (userData && userData.authenticationToken) {
        authToken = userData.authenticationToken;
    }
    var data = {};
    $('#session-set-script').remove();
    var apiURL=this.apiBaseUrl+ 'citus-health/'+this.version+'/chat-window-logout.php';

    $.ajax({
    type: 'POST',
    url: apiURL,
    data: data,
    headers: {
        "Authentication-Token": authToken
      },
    success: function (res) {


    },
    error: function (jqXHR, textStatus, errorThrown) {

    }
    });
    let sourceTenantId = localStorage.getItem('sourceTenantId');
    let sourceTenantLabel = localStorage.getItem('sourceTenantLabel');
    this.userDetails = null;
    this.userDataConfig = null;
    localStorage.clear();
    this._worklistIndexdbService.deleteByKeyFromIDb('WORKLIST_DATA');
    if (sourceTenantId) {
      localStorage.setItem('sourceTenantId', sourceTenantId);
    }
    if (sourceTenantLabel) {
      localStorage.setItem('sourceTenantLabel', sourceTenantLabel);
    }
    if(condition){
      localStorage.setItem('logoutSessionOut',"true");
    }
    let chatEnterToSend = this.getCookie('chatEnterToSend');
    this.deleteCookie('authenticationToken');
    this.deleteCookie('userLogin');
    this.deleteCookie('tenantId');
    this.deleteCookie('userRole');
    this.deleteCookie('userPrivileges');
    this.deleteCookie('userPrivilegesReplica');
    this.deleteCookie('userDataConfig');
   

    document.cookie.split(";").forEach(function(c) { document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); });
    this._structureService.socket.emit("forseDisconnect");
    if (chatEnterToSend) {
      this.setCookie('chatEnterToSend',chatEnterToSend,1);
    }

    this.isLogin = false;
    this.loginObservable.next('ok');
    this.setCookie('loggedName',null,1);
    this.setCookie('loggedId',null,1);
    this.signatureRequestInboxData='';

    let UserDetailsTAgent:any={};
    UserDetailsTAgent.name ='';
    UserDetailsTAgent.email = '';
    if(this.telemetryObject){
      this.telemetryObject.supportWidget.setUserData(UserDetailsTAgent);
      this.telemetryObject.pageData.setUserData(UserDetailsTAgent);
    }
    this.signatureRequestInboxData='';
    //for clearing data in pah //
    this.activePatientActivityDetails = [];
    this.activePatientActivityHub = false;
    this.pulseMenuShow = true;
    this.displayProgress.emit(true);
    this.activePatientActivityHubPatientId = '';
    this.activePatientActivityHubTabId = '';
    this.activePatientActivityHubFilterPahDetails = [];
    this.inboxData = [];
    this.archiveData = [];
    this._router.navigate(['/login']);
    this._idpAuth.startSignoutMainWindow();

  }


  subRequestData(requestConfig,apiConfig, cb){
    if (requestConfig.method == 'POST') {
      const subscription_Post = this.apollo.use(requestConfig.use).mutate({
        mutation: requestConfig.data,
        variables: requestConfig.variables
      });
      subscription_Post.subscribe(({ data }) => {
        if (requestConfig.noLoader == false) {
          this.countNProgress -= 1;
          if (this.countNProgress == 0 && requestConfig.nested == false) {
            this.countNProgressArr = [];
            NProgress.done();
          }
        }
        console.log('1111111111Refetch Result: ', data);
          NProgress.done();
          cb(data); 
        // resolve(data);
      }, (error) => {
        console.log("graphql post error===>",error);
        var errorMessage = '';
        if(error.graphQLErrors.length && error.graphQLErrors[0].message){
          console.log("graphQLErrors==>",error.graphQLErrors[0].message);
            errorMessage = error.graphQLErrors[0].message;
          }
          if(error.networkError && error.networkError.message){
            console.log("networkErrors==>",error.networkError.message);
          errorMessage = error.networkError.message;
        }
        if(errorMessage =="Invalid Token"){
          var messages={
            requestType:requestConfig.requestType,
            method:requestConfig.method,
            url:requestConfig.url,
            use:requestConfig.use,
            data : {
              mutation: requestConfig.data,
              variables: requestConfig.variables
            }
          }
          this.apiLogout(messages);
        }else if(errorMessage =="Failed to fetch" && apiConfig.count <= 3){
          apiConfig.count= apiConfig.count + 1;
          console.log("after apiConfig.count -> ",apiConfig.count)
          this.subRequestData(requestConfig,apiConfig, function(data) {
            cb(data);
          });
        }else{
          var requestDetails = {
            method: requestConfig.method,
            type: requestConfig.requestType,
            endpoint: requestConfig.use
          };
          let activityData = {
            activityName: "GraphQL Exception",
            activityType: 'exception handler',
            activityDescription: "[CitusHealth Error] - request data - " + JSON.stringify(requestDetails) + ". Error message - " + error + " (" + JSON.stringify(requestConfig) + ")"
          };
          this.trackActivity(activityData);
          if (requestConfig.noLoader == false) {
            this.countNProgress -= 1;
            if (this.countNProgress == 0 && requestConfig.nested == false) {
              this.countNProgressArr = [];
              NProgress.done();
            }
          }
          cb(error);
        }
      });
    } else if (requestConfig.method == 'GET') {
      let subscription;
      let subscriptionConfig;
      console.log('GQL Get', requestConfig.data);

      subscriptionConfig = {
        query: requestConfig.data,
      };

      if (apiConfig.variables) {
        subscriptionConfig.variables = apiConfig.variables;
      }
      console.log("subscriptionConfig===================> ",subscriptionConfig);
      subscription = this.apollo.use(requestConfig.use).watchQuery<QueryResponse>(subscriptionConfig);

      subscription.subscribe(({ data }) => {
        if (requestConfig.useCache) {
          console.log('subscribed Result: ', data);
          cb(data);
        }
      });
      if (!requestConfig.useCache) {
        subscription.refetch().then(({ data }) => {
          if (requestConfig.noLoader == false) {
            this.countNProgress -= 1;
            if (this.countNProgress == 0 && requestConfig.nested == false) {
              this.countNProgressArr = [];
              NProgress.done();
            }
          }
          console.log('222222222Refetch Result: ', data);
            NProgress.done();
            cb(data);
        }, (error) => {
          console.log("graphql get error===>",error);
          var invalidErrorMessage = '';
          if(error.graphQLErrors.length && error.graphQLErrors[0].message){
            console.log("graphQLErrors==>",error.graphQLErrors[0].message);
            invalidErrorMessage = error.graphQLErrors[0].message;
          }
          if(error.networkError && error.networkError.message){
            console.log("networkErrors==>",error.networkError.message);
            invalidErrorMessage = error.networkError.message;
          }
          if(invalidErrorMessage =="Invalid Token"){
            console.log("enter to logout function-----------");
            var messages={
              requestType:requestConfig.requestType,
              method:requestConfig.method,
              url:requestConfig.url,
              use:requestConfig.use,
              data : {
                mutation: requestConfig.data,
                variables: requestConfig.variables
              }
            }
            this.apiLogout(messages);
            
          }else if(invalidErrorMessage == "Failed to fetch" && apiConfig.count <= 3){
            apiConfig.count = apiConfig.count + 1 ;
            console.log("after apiConfig.count -> ",apiConfig.count)
            this.subRequestData(requestConfig,apiConfig, function(data) {
              cb(data);
            });            
          }else{
            console.log("enter elseeeeeeeeee");
            var requestDetails = {
              method: requestConfig.method,
              type: requestConfig.requestType,
              endpoint: requestConfig.use
            };
            let activityData = {
              activityName: "GraphQL Exception",
              activityType: 'exception handler',
              activityDescription: "[CitusHealth Error] - request data - " + JSON.stringify(requestDetails) + ". Error message - " + error + " (" + JSON.stringify(requestConfig) + ")"
            };
            this.trackActivity(activityData);
            if (requestConfig.noLoader == false) {
              this.countNProgress -= 1;
              if (this.countNProgress == 0) {
                this.countNProgressArr = [];
                NProgress.done();
              }
            }
            cb(error);
          }
        });
      }
    }
  }

  deleteCookie(name) {
    this.setCookie(name, '', -1);
  }

  setCookie(name: string, value: string, expireDays: number, path: string = '/') {
    const d: Date = new Date();
    d.setTime(d.getTime() + 365 * 24 * 60 * 60 * 1000);
    const expires = `expires=${d.toUTCString()}`;
    const cpath: string = path ? `; path=/` : '';
    document.cookie = `${name}=${value}; ${expires}${cpath};Secure`;
  }

  callTelemetryAgentLoad(){ 
      
    var UserDetails = {
        name:"",
        email:"",
        toEmail:"",
        message:"",
        mobile:"",
        tenantId:"",
        tenantName:"",
        userId:"",
        userType:"",
        enableSupportWidgetBranding:"",
        enable_support_widget_branding:"",
        app_name:"",
        support_widget_from_email:"",
        support_widget_email_color_code:"",
        SMTP_domain:"",
        logo:""
    };
    var userEmail  = localStorage.getItem('profileUsername');
    if (userEmail) {
        UserDetails.email = userEmail;
    } 
    var userName   = this._structureService.getCookie('displayname');
    if (userName) {
        UserDetails.name = userName;
    }
    var tenantId = this._structureService.getCookie('tenantId');
    if(tenantId){
       UserDetails.tenantId = tenantId;
    }
    
    
    var userDetails = this.userDetails?JSON.parse(this.userDetails):{};
    console.log('userDetails-->',userDetails);
    var config = this.userDataConfig? JSON.parse(this.userDataConfig):{};
    if(!(Object.keys(userDetails).length === 0)){
      var userData = userDetails; 
      console.log('userData-->'+userDetails);
      if(userDetails){
          var tenantName = userData.tenantName;
          if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
              tenantName =this._structureService.getCookie("crossTenantName");        
          }

       
        var mobile =userData.mobile;
        var group =userData.group;
        UserDetails.userId =userData.userId;
        if(mobile){
            UserDetails.mobile = mobile;
        }
        UserDetails.tenantName = tenantName;

    }
      
      if(!(Object.keys(config).length === 0)){
      var configData = config;
      var toEmail = this._structureService.getSupportWidgetRecipientEmail();
      if(toEmail){
          UserDetails.toEmail = toEmail;
      } 
      var enableSupportWidgetBranding =configData.enable_support_widget_branding;
      if(enableSupportWidgetBranding){
        UserDetails.enableSupportWidgetBranding = enableSupportWidgetBranding;
    } 
    if(configData.enable_support_widget_branding && configData.enable_support_widget_branding == 1){
        let configDetails = config;
        UserDetails.enable_support_widget_branding = configDetails.enable_support_widget_branding;
        UserDetails.support_widget_from_email = configDetails.support_widget_from_email;
        UserDetails.support_widget_email_color_code = configDetails.support_widget_email_color_code;
        UserDetails.app_name = configDetails.app_name;
        UserDetails.SMTP_domain = configDetails.SMTP_domain;
        UserDetails.logo = configDetails.support_widget_email_logo ? this.apiBaseUrl+ CONSTANTS.siteLogoUrl +configDetails.support_widget_email_logo : "";
      }
                var userType = 'Staff';
      if (userData.group === '3'){
        var message= configData.message_for_clinical_services_in_patient_feedback;  
                    userType = 'Patient';
      } else {
                  if (userData.group === '20') {
                    userType = 'Partner';
                }
                var message = configData.message_for_clinical_services_in_feedback_form;
            }
           
            if (message) {
                UserDetails.message = message;
            }
            if (userType) {
                UserDetails.userType = userType;
            }
        }
    }
    
    this.telemetryObject = TelemetryAgent.getInstance({ 
      apiKey: 'd6a373d3-12a7-371e-b216-2d0a416ad050',
      releaseStage: this._structureService.releaseStage,
      userData: UserDetails,
      userDataConfig: config
    });    
    
  }

  convertSchedulerDataToClientTZ(schedulerData, socketTimeZone, self) {
    let convertedSchedulerData = [];
    if (schedulerData != 'null' && JSON.parse(schedulerData).length) {
      JSON.parse(schedulerData).forEach(function (value, key) {
        if (value) {
          convertedSchedulerData.push(self.clientToGmtTime(value, true, socketTimeZone));
        }
      });
      return convertedSchedulerData;
    } else {
      return 0;
    }
  }

  clientToGmtTime(timeString, reverse, socketTimeZone: any = '', activeTimezone: any = '') {
    let hours, minutes, addHours, addMinutes, newMinutes, newHours;
    const currentTime = new Date();
    let currentTimezone;
    if (activeTimezone) {
      currentTimezone = activeTimezone * -1;
    } else {
      currentTimezone = currentTime.getTimezoneOffset();
    }
    currentTimezone = (currentTimezone / 60) * -1;
    const currentTimezoneTimeGap = (currentTimezone + '').split('.');
    if (currentTimezoneTimeGap[1] == '5') {
      currentTimezoneTimeGap[1] = '30';
    } else {
      currentTimezoneTimeGap[1] = '0';
    }
    const match = /(\d+):(\d+)/.exec(timeString);
    if (!reverse) {
      addHours = (parseInt(currentTimezoneTimeGap[0]) * -1) + this.timezone;
      if (currentTimezone > 0) {
        addMinutes = parseInt(currentTimezoneTimeGap[1]) * -1;
      } else {
        addMinutes = parseInt(currentTimezoneTimeGap[1]);
      }
    } else {
      if (socketTimeZone) {
        addHours = socketTimeZone - this.timezone;
        addMinutes = 0;
      } else {
        addHours = parseInt(currentTimezoneTimeGap[0]) - this.timezone;
        if (currentTimezone > 0) {
          addMinutes = parseInt(currentTimezoneTimeGap[1]);
        } else {
          addMinutes = parseInt(currentTimezoneTimeGap[1]) * -1;
        }
      }
    }
    hours = parseInt(match[1], 10);
    minutes = parseInt(match[2], 10);
    let hoursCorrection = 0;
    if ((hours + addHours) <= 0) {
      hoursCorrection = 24;
    }
    newMinutes = ((hours + addHours + hoursCorrection) * 60) + minutes + addMinutes;
    newHours = Math.floor(newMinutes / 60) % 24;
    if (newHours < 10) {
      newHours = '0' + newHours;
    }
    newMinutes %= 60;
    const minuteString = (newMinutes >= 10 ? '' : '0') + newMinutes;
    return newHours + ':' + minuteString;
  }

  userloginSuccess(userData, formLandingPage = '') {
    let profileUsername = userData.username;
    if ((userData.status === 0 || userData.status === 6)) {
      if (userData.roleName === 'Super Admin') {
        userData.tenantId = 1;
      }
      console.log("cookieeeeeeeeeeeeeeeeeeeeeee",this._sharedService.assetsUrl)
      this.setCookie("assetsUrl",this._sharedService.assetsUrl,1);
      localStorage.setItem('assetsUrl', this._sharedService.assetsUrl);
      this.setCookie('authenticationToken', userData.authenticationToken, 1);
      localStorage.setItem('authenticationToken', userData.authenticationToken);
      this.setCookie('userLogin', 'isLogin', 1);
      this.setCookie('tenantId', userData.tenantId, 1);

      if(userData.group === '3' && userData.mySites && userData.mySites.length == 1){
        this.setCookie('siteId', userData.mySites[0].id, 1);
        this.setCookie('siteName', userData.mySites[0].name, 1);
        }

      this.setCookie('crossTenantId',userData.crossTenantId,1);
      this.setCookie('organizationName',userData.tenantName,1);
      this.setCookie('userRole', userData.roleName, 1);
      this.setCookie('userPrivileges', userData.privileges, 1);
      this.setCookie('userPrivilegesReplica', userData.privileges_replica, 1);
      this.setCookie('userConfig', userData.config, 1);
      this.setCookie('userConfigReplica', userData.config_replica, 1);
      this.setCookie('status',userData.status,1);
      this.setCookie('tenantname', userData.tenantName, 1);
      localStorage.setItem('tenantTimezoneName', userData.config.tenant_timezoneName);
      localStorage.setItem('notificationSound', userData.notificationSoundName);
      /**Inbox */
      this.setCookie('showPrototype', userData.config.show_prototypes, 1);
      //this.setCookie('schedule', userData.schedulerData, 1);
      /**Ends Inbox */
      this.setCookie('showChatHistory', userData.config.show_chat_history_to_new_participant, 1);

      this.setCookie('showChatHistoryNewParticipant', userData.config.show_chat_history_to_new_participant, 1);
      this.setCookie('displayname', userData.displayName, 1);

      /* ****** */
      console.log('cookieauthenticationToken', this.getCookie('authenticationToken'));
      for (let userId in userData.schedulerData) {
        let schedulerData = this.convertSchedulerDataToClientTZ(userData.schedulerData[userId], null, this);
        if (schedulerData) {
          userData.schedulerData[userId] = JSON.stringify(schedulerData);
        }
      }
      for (let userId in userData.escalatedSchedulerData) {
        let escalatedSchedulerData = this.convertSchedulerDataToClientTZ(userData.escalatedSchedulerData[userId], -4, this);
        if (escalatedSchedulerData) {
          userData.escalatedSchedulerData[userId] = JSON.stringify(escalatedSchedulerData);
        }
      }
      
      if(userData.masterEnabled === "1" && userData.isMaster === "0"){
        for (let userId in userData.masterSchedulerData) {
          let masterSchedulerData = this.convertSchedulerDataToClientTZ(userData.masterSchedulerData[userId], null, this);
          if(masterSchedulerData) {
            userData.masterSchedulerData[userId] = JSON.stringify(masterSchedulerData);
          }
        }
      for (let userId in userData.masterEscalatedSchedulerData) {
          let masterEscalatedSchedulerData = this.convertSchedulerDataToClientTZ(userData.masterEscalatedSchedulerData[userId], -4, this);
          if(masterEscalatedSchedulerData) {
            userData.masterEscalatedSchedulerData[userId] = JSON.stringify(masterEscalatedSchedulerData);
          }
      }
      console.log('-------------master scheduler data after conversion--------------');
      console.log('------------master oncallll schedules------------')
      console.log(userData.masterSchedulerData);
      console.log('------------------------------------------');
      console.log('------------master escalation schedules------------')
      console.log(userData.masterEscalatedSchedulerData);
      console.log('------------------------------------------');
      }
      if(userData.config.signature_serverside=="1"){
        this.signatureListServerSidePagination=true;
      }

      /* ******* */
      this.setCookie('schedule', userData.schedulerData, 1);

      var manageConfig = userData.config;
      var defaultPage = '/profile';
      var nursingAgencyUser = false;
      var enablePages =[];
      enablePages.push('/profile');
      if(manageConfig.enable_nursing_agencies_visibility_restrictions && manageConfig.enable_nursing_agencies_visibility_restrictions == '1' && manageConfig.nursing_agencies != "") {
          nursingAgencyUser = true;
      }
      if(manageConfig.enable_message_center && manageConfig.enable_message_center == '1') {
        defaultPage = '/inbox';
      } else if(manageConfig.show_document_tagging && manageConfig.show_document_tagging == '1') {
        if((userData.privileges.indexOf('viewAllSignedDocs') == -1 && userData.privileges.indexOf('manageTenants') == -1) || nursingAgencyUser) {
          defaultPage = '/signature/signature-requests-list';          
        } else if((userData.privileges.indexOf('viewAllSignedDocs') != -1 || userData.privileges.indexOf('manageTenants') != -1) && !nursingAgencyUser) {
          defaultPage = '/signature/signed-documents-list';         
        } else {
          defaultPage = this.permissionService.getDefaultPageFormCenter(manageConfig, userData.privileges); 
        }
      } else {
        defaultPage = this.permissionService.getDefaultPageFormCenter(manageConfig, userData.privileges);
      }
      //get enablled pages .
      if(manageConfig.enable_message_center && manageConfig.enable_message_center == '1') {
        enablePages.push('/inbox'); 
      } 
      if(manageConfig.show_document_tagging && manageConfig.show_document_tagging == '1') {
        if((userData.privileges.indexOf('viewAllSignedDocs') == -1 && userData.privileges.indexOf('manageTenants') == -1) || nursingAgencyUser) {
          enablePages.push('/signature/signature-requests-list');          
        } 
        if((userData.privileges.indexOf('viewAllSignedDocs') != -1 || userData.privileges.indexOf('manageTenants') != -1) && !nursingAgencyUser) {
          enablePages.push('/signature/signed-documents-list'); 
        } 
        enablePages.push(this.permissionService.getDefaultPageFormCenter(manageConfig, userData.privileges));
       
      }
      if (isPresent(formLandingPage)) {
        defaultPage = formLandingPage;
      }
      enablePages.push(this.permissionService.getDefaultPageFormCenter(manageConfig, userData.privileges));
      
      //get enablled pages end.
      if (userData.accessSecurityEnabled && userData.accessSecurityWorklistActionLink) {
        userData.config.defaultPage = userData.accessSecurityWorklistActionLink;
        userData.defaultPage = userData.accessSecurityWorklistActionLink;
      } else { 
        if(userData.routingPage && userData.routingPage !="")
        { 
          if(enablePages.indexOf(userData.routingPage) != -1)
          { 
            
                userData.config.defaultPage = userData.routingPage;
                userData.defaultPage = userData.routingPage;
            
          }else{ 
            userData.config.defaultPage = defaultPage;
            userData.defaultPage = defaultPage;
          }
        } else {
          userData.config.defaultPage = defaultPage;
          userData.defaultPage = defaultPage;
        }

      }
      localStorage.setItem('profileUsername', profileUsername);
      this.userDetails = JSON.stringify(userData);
      this.userDataConfig = JSON.stringify(userData.config);
      console.log('userDetails-->',this.userDetails);
      this._structureService.userDetails = this.userDetails;
      this._structureService.userDataConfig = this.userDataConfig;
      console.log('userDetails-->',this._structureService.userDetails);
      //this.setCookie('authenticationToken', userData.authenticationToken, 1);
      /** New Telemtry Configuration */
      let UserDetailsTAgent: any = {};
      UserDetailsTAgent.name = userData.displayName;
      UserDetailsTAgent.email = profileUsername;
      UserDetailsTAgent.userId = userData.userId;
      var userType = 'Staff';
      if (userData.mobile) {
        UserDetailsTAgent.mobile = userData.mobile;
      } else {
        UserDetailsTAgent.mobile = '';
      }
      
      // console.log(userData.config.message_for_clinical_services_in_feedback_form);
      
      var toEmail = this._structureService.getSupportWidgetRecipientEmail();
      if(toEmail){
        UserDetailsTAgent.toEmail = toEmail;
       }
       var tenantId =userData.tenantId;  
       if(tenantId){
        UserDetailsTAgent.tenantId = tenantId;
       }
       
       var tenantName = userData.tenantName;  
       if(tenantName){
        UserDetailsTAgent.tenantName = tenantName;
       }
    
      if(userData.config.enable_support_widget_branding && userData.config.enable_support_widget_branding == 1){
         let configDetails = userData.config;        
        UserDetailsTAgent.enable_support_widget_branding =configDetails.enable_support_widget_branding;
        UserDetailsTAgent.support_widget_from_email = configDetails.support_widget_from_email;
        UserDetailsTAgent.support_widget_email_color_code = configDetails.support_widget_email_color_code;
        UserDetailsTAgent.app_name = configDetails.app_name;
        if(configDetails.SMTP_domain) {
          UserDetailsTAgent.SMTP_domain = configDetails.SMTP_domain;
        }
        UserDetailsTAgent.logo = this.apiBaseUrl+configDetails.support_widget_email_logo ?  this.apiBaseUrl+ CONSTANTS.siteLogoUrl +configDetails.support_widget_email_logo : "";
       }
       if (userData.group === '3'){
        var message= userData.config.message_for_clinical_services_in_patient_feedback;  
        userType = 'Patient';
       } else {
        if (userData.group === '20') {
          userType = 'Partner';
        }
        var message= userData.config.message_for_clinical_services_in_feedback_form;  
       }            
       if(message){
        UserDetailsTAgent.message = message;
      }
      if (userType) {
        UserDetailsTAgent.userType = userType;
      }
      if(this.telemetryObject){ 
      this.telemetryObject.supportWidget.setUserData(UserDetailsTAgent);
      this.telemetryObject.pageData.setUserData(UserDetailsTAgent);
      }

      //this.telemetryObject.problems.setUserData(UserDetailsTAgent);  
      /** End Section */
      this.setCookie('profileImageThumbUrl', userData.profileImageThumbUrl, 1);
      this.setCookie('profileImageUrl', userData.profileImageUrl, 1);
      let self = this;
      /**Signature request */
      this.setCookie('authID', userData.authenticationToken, 1);
      this.setCookie('userId', userData.userId, 1);
      /** Ends Signature request */
      this.loginUserDetails = userData;
      this.loginUserDetailsForRouteCheck = userData;
    } 
    
    return userData;
  
  }

  loginSuccess(userData){
    var profileUsername = localStorage.getItem('profileUsername');
    userData.username = profileUsername;
    this.loginDetails = userData;
    this._structureService.agGridLicenceKey = CONFIG.AG_GRID_LICENSE_KEY;
      if (this.loginDetails.status === 0 || this.loginDetails.status === 6) {
        this._structureService.socketConnect();
        localStorage.setItem('cmisApiBaseUrl', this.loginDetails.cmisApiBaseUrl);
          let self=this;
          var sourceTenantId = '0';
          var title = 'CitusHealth';
          if(localStorage.getItem('sourceTenantId')) {
            sourceTenantId = localStorage.getItem('sourceTenantId');
            title = self.loginDetails.config.app_name;
          }
          console.log('_structureService.userDetails',self._structureService.userDetails);
          var userDataConfig = self._structureService.userDataConfig;
          var userDetail = self._structureService.userDetails;
          var iconPath = self._sharedService.assetsUrl+'/a/'+sourceTenantId+'/img/account-notification-logo.png';
          notifyMe(title,'',iconPath, 'Web push notification enabled successfully', null,userDetail,userDataConfig,function(response) {
            console.log("enter to login notifyme...");
            console.log('#notifyme response');
            console.log(response)
            if(!response.NotificationAvailable) {
              self._structureService.notifyMessage({
                messge:response.status ? 'Web push notifications are disabled/unsupported in this browser' : 'Web push notifications are unsupported in this browser',
                delay:1000,
                type:'warning'
              });  
            }
          });
          //console.log('++++++++++++++++++');
          //console.log('---------------------')
          if(self.loginDetails.config.enable_video_chat=="1"){
            this._structureService.generateVidyoTocken({source:'login', userName:  self.loginDetails.displayName+'-'+self.loginDetails.tenantName}).then((data)=> {
              console.log('---generateVidyoTocken----response-'+JSON.stringify(data));
              this._sharedService.validVidyoToken.emit(data);
            });
          }

           this.flagstop=true;

        if(this.loginDetails.status !== 6 ){
          let options = {
            maxParticipants: 5,
            userData: this.loginDetails
          }
          if(!this._commonVideoService.checkVideoPluginLoaded()){
            console.log('Login vidyo init ----- SUCCESS')
            this._commonVideoService.init('vidyo', options);
          }
          else{
            console.log('Login vidyo init not called')
          }
          this._structureService.inboxUnreadMessageCount = 0;
          this._structureService.inboxDataFirstPage = [];
          if (this.loginDetails.group === '3'){

            var patientTopicCount:any = 0;
            if(this.loginDetails.config.show_infusion_support == '1'){
                patientTopicCount += 1;
            }
            if(this.loginDetails.config.enable_when_is_my_nurse_coming == '1'){
                patientTopicCount += 1;
            }
            if(this.loginDetails.config.enable_where_is_my_delivery == '1'){
                patientTopicCount += 1;
            }
  
            console.log("patientTopicCount login:::", patientTopicCount);
            localStorage.setItem('patientTopicCount',patientTopicCount);
            if(this._router.navigated ){
              this._router.navigate([this.loginDetails.defaultPage || '/profile']);
            }

            
          }else{
            if(this._router.navigated ){
              this._router.navigate([this.loginDetails.defaultPage || '/profile']);
            }
          }
        }else{
          this._router.navigate(['reset-password/enrol']);
        }
        console.log("===================1================");
      } else if(this.loginDetails.status === 2) {
        console.log("================2===================");
        var notify = $.notify('Please contact administrator to activate this account');
        setTimeout(function() {
            notify.update({'type': 'danger', 'message': '<strong>Please contact administrator to activate this account</strong>'});
        }, 1000);
        
        var activityData = {
              activityName: "Failure User Auto Login",
              activityType: "user access",
              activityDescription: "User account activation pending (" +  profileUsername + ")"
            };
        this._structureService.trackActivity(activityData);
  
      } else if(this.loginDetails.status === 10) {
        var notify = $.notify({'message':this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')},
            {
                allow_dismiss: true,
                delay: 0
            });
        setTimeout(function() {
          notify.update({'type': 'danger', 'message': '<strong>'+this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')+'</strong>', 'delay':0});
        }, 5000);

        var activityData = {
          activityName: "Failure User Auto Login",
          activityType: "user access",
          activityDescription: "Account Disabled"
        };
      } else if(this.loginDetails.status === 8) {
        var notify = $.notify({'message':this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')},
            {
                allow_dismiss: true,
                delay: 0
            });
        setTimeout(function() {
          notify.update({'type': 'danger', 'message': '<strong>'+this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')+'</strong>', 'delay':0});
        }, 5000);

        var activityData = {
          activityName: "Failure User Auto Login",
          activityType: "user access",
          activityDescription: "Account Deleted"
        };
      } else if(this.loginDetails.status === 1) {
        var notify = $.notify({'message':this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')},
            {
                allow_dismiss: true,
                delay: 0
            });
        setTimeout(function() {
          notify.update({'type': 'danger', 'message': '<strong>'+this._ToolTipService.getTranslateData('MESSAGES.NO_SITE_FOR_STAFF')+'</strong>', 'delay':0});
        }, 5000);

        var activityData = {
          activityName: "Failure User Auto Login",
          activityType: "user access",
          activityDescription: "Account Disabled"
        };
      } else if(this.loginDetails.status === 5) {
        console.log("===============4====================");
        var notify = $.notify('User has been discharged');
        setTimeout(function() {
            notify.update({'type': 'danger', 'message': '<strong>User has been discharged</strong>'});
        }, 1000);

        var activityData = {
          activityName: "Failure User Auto Login",
          activityType: "user access",
          activityDescription: "User discharged (" + profileUsername + ")"
        };
        this._structureService.trackActivity(activityData);
      } else if(this.loginDetails.code === 0 || this.loginDetails.code === 401 || this.loginDetails.status === 1) {
          console.log("==================3=================");
          var notify = $.notify('Invalid username or password');
          setTimeout(function() {
              notify.update({'type': 'danger', 'message': '<strong>Invalid username or password</strong>'});
          }, 1000);
  
          var activityData = {
            activityName: "Failure User Auto Login",
            activityType: "user access",
            activityDescription: "User authentication failed (" + profileUsername + "). " + (this.loginDetails.message ? this.loginDetails.message : "")
          };
        this._structureService.trackActivity(activityData);
      } else {
        console.log("================5===================");
        var notify = $.notify(this.loginDetails.message);
        setTimeout(function() {
            notify.update({'type': 'danger', 'message': '<strong>'+this.loginDetails.message+'</strong>'});
        }, 1000);

        var activityData = {
          activityName: "Failure User Auto Login",
          activityType: "user access",
          activityDescription:  "Server error (" + profileUsername + ")"
        };
        this._structureService.trackActivity(activityData);
      }
     
  }

}
