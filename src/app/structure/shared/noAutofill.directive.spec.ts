import { NoAutofillDirective } from './noAutofill.directive';
import { ElementRef, Renderer2 } from '@angular/core';
import { TestBed, ComponentFixture } from '@angular/core/testing';
import { Component } from '@angular/core';

@Component({
  template: `<form><input noAutofill></form>`
})
class TestComponent {}

describe('NoAutofillDirective', () => {
  let fixture: ComponentFixture<TestComponent>;
  let directive: NoAutofillDirective;
  let el: ElementRef;
  let renderer: Renderer2;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [TestComponent, NoAutofillDirective],
    });

    fixture = TestBed.createComponent(TestComponent);
    el = fixture.debugElement.query(el => el.name === 'input').injector.get(ElementRef);
    renderer = fixture.debugElement.injector.get(Renderer2);
    directive = new NoAutofillDirective(el, renderer);
  });

  it('should create an instance', () => {
    expect(directive).toBeTruthy();
  });

  it('should find parent form', () => {
    const form = directive['findParentForm'](el.nativeElement);
    expect(form).toBeTruthy();
    expect(form.tagName).toBe('FORM');
  });

  it('should add dummy fields to the form', () => {
    const form = directive['findParentForm'](el.nativeElement);
    directive['addDummyFields'](form);

    const dummyStart = form.querySelector('input[type="text"]') as HTMLElement;
    const dummyEnd = form.querySelector('input[type="password"]') as HTMLElement;

    expect(dummyStart).toBeTruthy();
    expect(dummyStart.getAttribute('autocomplete')).toBe('off');
    expect(dummyStart.style.display).toBe('none');

    expect(dummyEnd).toBeTruthy();
    expect(dummyEnd.getAttribute('autocomplete')).toBe('new-password');
    expect(dummyEnd.style.display).toBe('none');
  });

  it('should set no-autofill-added attribute to the form', () => {
    directive.ngAfterViewInit();
    const form = directive['findParentForm'](el.nativeElement);
    expect(form.getAttribute('no-autofill-added')).toBe('true');
  });
});