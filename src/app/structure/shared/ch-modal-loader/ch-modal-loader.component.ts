import { Component, Input, OnInit } from "@angular/core";

@Component({
    selector: 'app-ch-modal-loader',
    templateUrl: './ch-modal-loader.component.html',
    styleUrls: ['./ch-modal-loader.component.scss']
})
export class ChModalLoaderComponent implements OnInit {

    @Input() message:string;
    constructor() { 
    }

    ngOnInit() { 
    }
    show_popup() {
        document.getElementById('light').style.display='block';
        document.getElementById('fade').style.display='block'
    }
    hide_popup() {
        document.getElementById('light').style.display='none';
        document.getElementById('fade').style.display='none';
    }
}