// This directive is used to prevent autofill in input fields. It adds two dummy fields.
// Make sure to have unique id and names for the input fields.

// How It Works
// Directive Placement: Attach the noAutofill directive to any input inside a form.
// The directive will locate the parent form and inject dummy fields automatically.
// Minimizes Code Changes: No need to add dummy inputs manually in templates.
// Prevents Autofill Aggressively: Chrome sees the dummy fields as the real ones for autofill, leaving the actual form inputs untouched.

import { Directive, ElementRef, Renderer2, AfterViewInit } from '@angular/core';

@Directive({
  selector: '[noAutofill]'
})
export class NoAutofillDirective implements AfterViewInit {
  constructor(private el: ElementRef, private renderer: Renderer2) {}

  ngAfterViewInit() {
    const form = this.findParentForm(this.el.nativeElement);

    if (form && !form.hasAttribute('no-autofill-added')) {
      this.addDummyFields(form);
      form.setAttribute('no-autofill-added', 'true'); // Ensure it's only added once
    }
  }

  private findParentForm(element: HTMLElement): HTMLElement | null {
    while (element) {
      if (element.tagName === 'FORM') {
        return element;
      }
      element = element.parentElement;
    }
    return null;
  }

  private addDummyFields(form: HTMLElement): void {
    // Add a fake input at the start of the form
    const dummyStart = this.renderer.createElement('input');
    this.renderer.setAttribute(dummyStart, 'type', 'text');
    this.renderer.setAttribute(dummyStart, 'autocomplete', 'off');
    this.renderer.setStyle(dummyStart, 'display', 'none');
    this.renderer.insertBefore(form, dummyStart, form.firstChild);

    // Add a fake input at the end of the form
    const dummyEnd = this.renderer.createElement('input');
    this.renderer.setAttribute(dummyEnd, 'type', 'password');
    this.renderer.setAttribute(dummyEnd, 'autocomplete', 'new-password');
    this.renderer.setStyle(dummyEnd, 'display', 'none');
    this.renderer.appendChild(form, dummyEnd);
  }
}
