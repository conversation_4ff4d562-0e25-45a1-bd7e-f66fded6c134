import { Directive, ElementRef, Renderer2, Input, OnChanges, SimpleChanges, Output, EventEmitter } from '@angular/core';
import { OutOfOfficeInfo } from 'app/models/login';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { isBlank, getOooStatusBasedOnTime } from 'app/utils/utils';

@Directive({
  selector: '[outOfOfficeStatus]'
})
export class OutOfOfficeStatusDirective implements OnChanges {
  @Input() oooInfo: OutOfOfficeInfo;
  @Input() customClass = 'circle-badge';
  @Input() returnOnly = false; // Flag to control DOM manipulation
  @Output() badgeStatusInfo = new EventEmitter<{ message: string; color: string; status: boolean }>(); // Output for required data
  constructor(private el: ElementRef, private renderer: Renderer2, private toolTipService: ToolTipService) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.customClass) {
      this.customClass = changes.customClass.currentValue;
    }
    if (changes.oooInfo) {
      this.oooInfo = changes.oooInfo.currentValue;
      this.removeBusySymbol(this.el.nativeElement);
      this.addBusySymbol();
    }
  }

  addBusySymbol() {
    const avatarContainer = this.el.nativeElement;
    if (avatarContainer) {
      const badgeStatusInfo = getOooStatusBasedOnTime(this.oooInfo);
      if (this.oooInfo && (this.oooInfo.isOutOfOffice || !isBlank(this.oooInfo.message)) && badgeStatusInfo.status) {
        this.createBusySymbol(avatarContainer, badgeStatusInfo);
        if (badgeStatusInfo.timeToEndOutOfOfficeInfo > 0) {
          setTimeout(() => {
            this.removeBusySymbol(avatarContainer);
          }, badgeStatusInfo.timeToEndOutOfOfficeInfo);
        }
      } else {
        if (badgeStatusInfo.timeToStartOutOfOfficeInfo > 0) {
          setTimeout(() => {
            this.addBusySymbol();
          }, badgeStatusInfo.timeToStartOutOfOfficeInfo);
        }
      }
    }
  }
  createBusySymbol(container: HTMLElement, badgeStatusInfo) {
    if (this.returnOnly) {
      this.badgeStatusInfo.emit(badgeStatusInfo);
      return;
    }
    const oooBadge = this.renderer.createElement('i');
    const classes = ['fa', 'fa-minus', 'red-badge'];
    if (this.customClass) classes.push(this.customClass);
    classes.forEach((className) => this.renderer.addClass(oooBadge, className));
    this.renderer.setStyle(oooBadge, 'background-color', badgeStatusInfo.color);
    this.renderer.insertBefore(container.parentElement, oooBadge, container.nextSibling);
    this.toolTipService.getTranslateDataPipe(badgeStatusInfo.message).subscribe((translatedMessage) => {
      this.renderer.setAttribute(oooBadge, 'title', translatedMessage);
    });
  }
  removeBusySymbol(container: HTMLElement) {
    if (this.returnOnly) {
      this.badgeStatusInfo.emit(null);
      return;
    }
    const existingBadge = container.parentElement.querySelector('.red-badge');
    if (existingBadge) {
      this.renderer.removeChild(container.parentElement, existingBadge);
    }
  }
}
