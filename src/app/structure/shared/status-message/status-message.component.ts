import { Component, OnInit, OnDestroy, Output, EventEmitter, Input, ViewEncapsulation, ChangeDetectionStrategy } from '@angular/core';
import { StructureService } from '../../structure.service';

@Component({
  selector: 'app-status-message-component',
  templateUrl: './status-message.component.html',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StatusMessageComponent implements OnInit, OnDestroy {
  @Output() ModalEvent = new EventEmitter<void>();
  @Input() chatParticipants: any;
  chatData: any;
  usersListByRoom: any;
  currentChatroomId = localStorage.getItem('targetId');

  constructor(private structureService: StructureService) {}

  ngOnInit() {
    this.structureService.loadMicroFrontend();
    this.chatData = this.chatParticipants;
  }

  ngOnDestroy(): void {
    this.structureService.unLoadMicroFrontend();
  }
  handleCloseModal() {
    this.ModalEvent.emit();
  }
}
