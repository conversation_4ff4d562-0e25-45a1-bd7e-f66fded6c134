import { Component, Input, Output, EventEmitter } from '@angular/core';
import { UserGroup } from 'app/constants/constants';
import { NGXLogger } from 'ngx-logger';
import { HttpService } from 'app/services/http/http.service';
import { HttpParams } from '@angular/common/http';
import { APIs } from 'app/constants/apis';
import { isBlank } from 'app/utils/utils';
import { StructureService } from 'app/structure/structure.service';
import { userFilterPipe } from 'app/structure/inbox/inbox-modal-filter';
import * as moment from 'moment';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { PdgResponseData } from './pdg-dropdown.component.interface';
import { UserListParams } from '../user-dropdown/user-dropdown.interface';
import { LoadMoreItems, ScrollType, SearchComponentSettings } from '../search-select/search-select.interface';

@Component({
  selector: 'app-pdg-dropdown',
  templateUrl: './pdg-dropdown.component.html'
})
export class PatientGroupComponent {
  @Input() siteIds;
  @Output() selectedItem = new EventEmitter<any>();
  searchFieldLabel = 'LABELS.SEARCH_PDG';
  totalCount = 0;
  currentPage = 0;
  searchResult: any[] = [];
  selectedPdg: any;
  searchComponentSettings: SearchComponentSettings = {
    placeHolderText: 'LABELS.SEARCH_PATIENT',
    fieldName: 'LABELS.PATIENT',
    allowLazyLoading: true,
    lazyLoadType: ScrollType.INFINITE
  };

  constructor(
    private httpService: HttpService,
    private ngxLogger: NGXLogger,
    private structureService: StructureService,
    private modalFilter: userFilterPipe,
    private tooltipService: ToolTipService
  ) {}

  doSearch(searchData: LoadMoreItems): void {
    let params = new HttpParams();
    const currentPage = searchData.loadMore ? this.currentPage + 1 : 0;
    this.currentPage = currentPage; // Update state separately
    const requestParams = this.createParams(searchData, currentPage);
    const keys = Object.keys(requestParams);
    keys.forEach((key) => {
      params = params.set(key, requestParams[key]);
    });
    this.httpService
      .doGet(APIs.getTenantUsers, {
        params
      })
      .subscribe(
        (response: PdgResponseData) => this.handlePdgListResponse(response, searchData.loadMore),
        (error) => this.ngxLogger.error('Failed to fetch group data', error)
      );
  }

  private createParams(searchData: LoadMoreItems, currentPage: number): UserListParams {
    const loginUserData = JSON.parse(this.structureService.userDetails);
    const requestParams: UserListParams = {
      status: 'notRejected',
      roleId: UserGroup.PATIENT,
      optionShow: 'patient',
      excludeLogginedUser: loginUserData.userId,
      isTenantRoles: 'null',
      isFromChat: 1,
      userGroup: loginUserData.group,
      isSchedule: false,
      pageCount: currentPage,
      siteIds: this.siteIds,
      excludeRoleId: false,
      searchKeyword: searchData.searchText,
      needVirtualPatients: true
    };
    if (!isBlank(loginUserData.nursing_agencies)) {
      requestParams.nursingAgencies = loginUserData.nursing_agencies;
    }
    if (loginUserData.accessSecurityEnabled) {
      requestParams.accessSecurityEnabled = loginUserData.accessSecurityEnabled;
      requestParams.accessSecurityEsiValue = loginUserData.accessSecurityEsiValue;
      requestParams.accessSecurityIdentifierType = loginUserData.accessSecurityIdentifierType;
      requestParams.accessSecurityType = loginUserData.accessSecurityType;
    }
    return requestParams;
  }

  private handlePdgListResponse(response: any, loadMore: boolean): void {
    if (response && response.length) {
      let userData = this.modalFilter.transform(response, 'patient');
      userData = userData.map(({ name, ...user }) => {
        const patientData = {
          firstName: user.firstname,
          lastName: user.lastname,
          dob: !isBlank(user.dob) ? `${this.tooltipService.getTranslateData('LABELS.DOB')} ${moment(user.dob).format('MM/DD/YYYY')}` : '',
          mrn: !isBlank(user.IdentityValue) ? user.IdentityValue : ''
        };
        const formattedName = this.tooltipService.getTranslateDataWithParam('LABELS.PDG_TOPIC', patientData);
        return { id: +user.userId, name: formattedName };
      });
      this.totalCount = response.length;
      const pdgList = userData;
      this.searchResult = loadMore ? [...this.searchResult, ...pdgList] : pdgList;
    } else {
      this.searchResult = [];
    }
  }

  doReset(): void {
    this.searchResult = [];
    this.selectedPdg = '';
    this.currentPage = 0;
  }

  onItemSelect(item): void {
    this.selectedPdg = item || '';
    this.selectedItem.emit({
      users: [
        this.searchResult.find((pdg) => {
          return pdg.id === this.selectedPdg.id;
        })
      ]
    });
  }
}
