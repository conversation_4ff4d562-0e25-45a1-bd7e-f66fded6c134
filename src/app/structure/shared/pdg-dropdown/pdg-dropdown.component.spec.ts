import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpService } from 'app/services/http/http.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { APIs } from 'app/constants/apis';
import { UserGroup } from 'app/constants/constants';
import { of } from 'rxjs/observable/of';
import { HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { CommonTestingModule } from 'app/utils/common-testing.module';
import { StructureService } from 'app/structure/structure.service';
import { PatientGroupComponent } from './pdg-dropdown.component';
import { LoadMoreItems } from '../search-select/search-select.interface';
import { PdgResponseData } from './pdg-dropdown.component.interface';
import { userFilterPipe } from '../../inbox/inbox-modal-filter';
import { UserListParams } from '../user-dropdown/user-dropdown.interface';

describe('PatientGroupComponent', () => {
  let component: PatientGroupComponent;
  let httpService: HttpService;
  let fixture: ComponentFixture<PatientGroupComponent>;
  let tooltipService: ToolTipService;
  let structureService: StructureService;
  let modalFilter: { transform: jasmine.Spy };
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [CommonTestingModule]
    }).compileComponents();
    const mockUserData = {
      userId: 12345,
      group: 67890,
      accessSecurityEnabled: false
    };
    structureService = TestBed.get(StructureService);
    structureService.userDetails = JSON.stringify(mockUserData);
    httpService = TestBed.get(HttpService);
    tooltipService = TestBed.get(ToolTipService);
    modalFilter = TestBed.get(userFilterPipe);
    fixture = TestBed.createComponent(PatientGroupComponent);
    component = fixture.componentInstance;
    component.siteIds = '1,2,3';
    fixture.detectChanges();
  });
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit selected item', () => {
    const item = { id: 1, name: 'Item 1' };
    component.searchResult = [
      { id: 1, name: 'Item 1' },
      { id: 2, name: 'Item 2' }
    ];
    spyOn(component.selectedItem, 'emit');
    component.searchResult = [item, { id: 2, name: 'Item 2' }];
    component.selectedPdg = item;
    component.onItemSelect(item);
    expect(component.selectedItem.emit).toHaveBeenCalledWith({ users: [item] });
  });

  it('should reset search', () => {
    spyOn(component.selectedItem, 'emit');
    component.searchResult = [{ id: 1, name: 'Item 1' }];
    component.doReset();
    expect(component.searchResult).toEqual([]);
  });

  it('should call httpService.doGet with correct parameters when searchData.loadMore is false', () => {
    const searchData: LoadMoreItems = { searchText: 'test', loadMore: false, startDate: '', endDate: '' };
    const response: PdgResponseData = {
      success: true,
      data: {
        totalPDGCount: '10',
        patientGroups: [
          { id: 1, name: 'Group 1' },
          { id: 2, name: 'Group 2' }
        ]
      }
    };
    const mockParams = new HttpParams().set('param1', 'value1').set('param2', 'value2');

    spyOn(component, 'createParams').and.returnValue({
      param1: 'value1',
      param2: 'value2'
    });
    spyOn(httpService, 'doGet').and.returnValue(of(response));

    component.doSearch(searchData);

    expect((component as any).createParams).toHaveBeenCalledWith(searchData, 0);
    expect(httpService.doGet).toHaveBeenCalledWith(APIs.getTenantUsers, { params: mockParams });
    expect(component.currentPage).toBe(0);
  });

  it('should call httpService.doGet with correct parameters when searchData.loadMore is true', () => {
    component.currentPage = 1;
    const searchData: LoadMoreItems = {
      loadMore: true,
      searchText: 'test',
      startDate: '',
      endDate: ''
    };

    const response: PdgResponseData = {
      success: true,
      data: {
        totalPDGCount: '10',
        patientGroups: []
      }
    };

    const mockParams = new HttpParams().set('param1', 'value1').set('param2', 'value2');

    spyOn(component, 'createParams').and.returnValue({
      param1: 'value1',
      param2: 'value2'
    });

    spyOn(httpService, 'doGet').and.returnValue(of(response));
    component.doSearch(searchData);

    expect((component as any).createParams).toHaveBeenCalledWith(searchData, 2);
    expect(httpService.doGet).toHaveBeenCalledWith(APIs.getTenantUsers, { params: mockParams });
    expect(component.currentPage).toBe(2);
  });

  it('should handle error and call ngxLogger.error when httpService.doGet fails', () => {
    const searchData: LoadMoreItems = { searchText: 'test', loadMore: false, startDate: '', endDate: '' };
    const error = new Error('Some error');
    const errorObservable = new Observable<any>((observer) => {
      observer.error(error);
    });
    spyOn(httpService, 'doGet').and.returnValue(errorObservable);
    spyOn((component as any).ngxLogger, 'error');
    component.doSearch(searchData);
    expect((component as any).ngxLogger.error).toHaveBeenCalledWith('Failed to fetch group data', error);
  });

  it('should handle non-empty response and load more', () => {
    const mockResponse = [
      { userId: '1', firstname: 'John', lastname: 'Doe', dob: '1990-01-01', IdentityValue: '12345' },
      { userId: '2', firstname: 'Jane', lastname: 'Smith', dob: '1992-02-02', IdentityValue: '67890' }
    ];
    const transformedResponse = [
      { firstname: 'John', lastname: 'Doe', dob: '01/01/1990', IdentityValue: '12345', userId: '1' },
      { firstname: 'Jane', lastname: 'Smith', dob: '02/02/1992', IdentityValue: '67890', userId: '2' }
    ];
    const formattedName = 'Formatted Name';

    spyOn(modalFilter, 'transform').and.returnValue(transformedResponse);
    spyOn(tooltipService, 'getTranslateDataWithParam').and.returnValue(formattedName);

    component.searchResult = [{ id: 0, name: 'Old Result' }];
    (component as any).handlePdgListResponse(mockResponse, true);

    expect(component.totalCount).toBe(mockResponse.length);
    expect(component.searchResult).toEqual([
      { id: 0, name: 'Old Result' },
      { id: 1, name: formattedName },
      { id: 2, name: formattedName }
    ]);
  });

  it('should handle non-empty response without load more', () => {
    const mockResponse = [{ userId: '1', firstname: 'John', lastname: 'Doe', dob: '1990-01-01', IdentityValue: '12345' }];
    const transformedResponse = [{ firstname: 'John', lastname: 'Doe', dob: '01/01/1990', IdentityValue: '12345', userId: '1' }];
    const formattedName = 'Formatted Name';

    spyOn(modalFilter, 'transform').and.returnValue(transformedResponse);
    spyOn(tooltipService, 'getTranslateDataWithParam').and.returnValue(formattedName);
    (component as any).handlePdgListResponse(mockResponse, false);

    expect(component.totalCount).toBe(mockResponse.length);
    expect(component.searchResult).toEqual([{ id: 1, name: formattedName }]);
  });

  it('should handle empty response', () => {
    (component as any).handlePdgListResponse([], true);

    expect(component.totalCount).toBe(0);
    expect(component.searchResult).toEqual([]);
  });

  it('should create the correct params without nursing agencies and access security', () => {
    const mockUserData = {
      userId: 12345,
      group: 67890,
      accessSecurityEnabled: false
    };
    structureService.userDetails = JSON.stringify(mockUserData);

    const searchData: LoadMoreItems = {
      searchText: 'testSearch',
      loadMore: false
    };
    const currentPage = 1;
    const expectedParams: UserListParams = {
      status: 'notRejected',
      roleId: UserGroup.PATIENT,
      optionShow: 'patient',
      excludeLogginedUser: 12345,
      isTenantRoles: 'null',
      isFromChat: 1,
      userGroup: 67890,
      isSchedule: false,
      pageCount: currentPage,
      siteIds: component.siteIds,
      excludeRoleId: false,
      searchKeyword: 'testSearch',
      needVirtualPatients: true
    };

    const result = component['createParams'](searchData, currentPage);

    expect(result).toEqual(expectedParams);
  });

  it('should create params with nursing agencies and access security fields when conditions are met', () => {
    const mockLoginUserData = {
      userId: 123,
      group: 'someGroup',
      nursing_agencies: 'SomeAgency',
      accessSecurityEnabled: true,
      accessSecurityEsiValue: 'ESI123',
      accessSecurityIdentifierType: 'ID_TYPE',
      accessSecurityType: 'ACCESS_TYPE'
    };
    spyOn(JSON, 'parse').and.returnValue(mockLoginUserData);
    const searchData = { searchText: 'test' } as LoadMoreItems;
    const result = (component as any).createParams(searchData, 1);
    expect(result.nursingAgencies).toBe('SomeAgency');
    expect(result.accessSecurityEnabled).toBe(true);
    expect(result.accessSecurityEsiValue).toBe('ESI123');
    expect(result.accessSecurityIdentifierType).toBe('ID_TYPE');
    expect(result.accessSecurityType).toBe('ACCESS_TYPE');
  });

  it('should create params without nursing agencies and access security fields when conditions are not met', () => {
    const mockLoginUserData = {
      userId: 123,
      group: 'someGroup',
      nursing_agencies: null,
      accessSecurityEnabled: false
    };
    spyOn(JSON, 'parse').and.returnValue(mockLoginUserData);
    const searchData = { searchText: 'test' } as LoadMoreItems;
    const result = (component as any).createParams(searchData, 1);
    expect(result.nursingAgencies).toBeUndefined();
    expect(result.accessSecurityEnabled).toBeUndefined();
  });
});
