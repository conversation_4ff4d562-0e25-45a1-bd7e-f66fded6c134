import { Component, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { StructureService } from 'app/structure/structure.service';
import { Item } from '../search-select/search-select.interface';
import { SelectionEvent, SelectedItem } from './patient-dropdown.component.interface';
import { isBlank } from 'app/utils/utils';

@Component({
  selector: 'app-patient-dropdown',
  templateUrl: './patient-dropdown.component.html',
  styleUrls: ['./patient-dropdown.component.scss']
})
export class PatientDropdownComponent implements OnChanges {
  @Input() siteIds;
  @Input() searchType = 'pdg';
  @Input() listType = '';
  @Input() requestParams;
  @Input() disableContactlessUsers = false;
  @Input() hideSubList = false;
  @Input() reset = false;
  @Input() includePDGInfo = false;
  @Input() customClass = '';
  @Output() selectedItem = new EventEmitter<SelectedItem>();
  @Output() selectedAdmissionId = new EventEmitter<number>();
  totalCount = 0;
  currentPage = 1;
  userDataConfig;
  searchResult: Item[] = [];
  selectedPatient: SelectionEvent | null = null;
  constructor(private structureService: StructureService) {
    this.userDataConfig = JSON.parse(this.structureService.userDataConfig);
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (!isBlank(changes.reset) && changes.reset.currentValue) {
      this.reset = changes.reset.currentValue;
    }
  }
  setPatient(event: SelectedItem): void {
    this.selectedPatient = event && event.users && event.users.length ? event.users[0] : null;
    if (!this.structureService.isMultiAdmissionsEnabled || isBlank(this.selectedPatient) || isBlank(this.selectedPatient.id)) {
      this.emitSelection(this.selectedPatient && this.selectedPatient.id ? [this.selectedPatient] : [], [], event.reset);
    }
  }

  setAdmissionId(event: SelectionEvent): void {
    let admissions: SelectionEvent[] = event && event.id ? [{ id: event.id, name: event.name }] : [];
    if (this.includePDGInfo) {
      admissions = event && event.id ? [event] : [];
    }
    const users = this.selectedPatient && this.selectedPatient.id ? [this.selectedPatient] : [];
    this.emitSelection(users, admissions);
  }

  private emitSelection(users: SelectionEvent[] = [], admissions: SelectionEvent[] = [], reset = false): void {
    this.selectedItem.emit({ users, admissions, reset });
    this.reset = false;
  }
}
