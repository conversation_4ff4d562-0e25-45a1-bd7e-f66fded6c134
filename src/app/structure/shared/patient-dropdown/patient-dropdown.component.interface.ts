export interface PdgResponseData {
  success: boolean;
  data: {
    totalPDGCount: string;
    patientGroups: any[];
  };
}

export interface SelectionEvent {
  id: string;
  name?: string;
  admissionId?: string;
  admissionName?: string;
  isPublic?: boolean;
  allowMultiThreadChat?: boolean;
  chatRoomId?: string;
  chatRoomCreatedBy?: string;
  topic?: string;
  isParticipant?: boolean;
}

export interface SelectedItem {
  users: any[];
  admissions?: SelectionEvent[];
  reset?: boolean;
}
