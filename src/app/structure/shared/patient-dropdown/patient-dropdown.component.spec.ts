import { ComponentFixture, TestBed } from '@angular/core/testing';
import { StructureService } from 'app/structure/structure.service';
import { CommonTestingModule } from 'app/utils/common-testing.module';
import { PatientDropdownComponent } from './patient-dropdown.component';
import { SelectionEvent } from './patient-dropdown.component.interface';

describe('PatientDropdownComponent', () => {
  let component: PatientDropdownComponent;
  let structureService: StructureService;
  let fixture: ComponentFixture<PatientDropdownComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [CommonTestingModule]
    }).compileComponents();
    structureService = TestBed.get(StructureService);
    structureService.userDataConfig = JSON.stringify({});
    fixture = TestBed.createComponent(PatientDropdownComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
  describe('setPatient', () => {
    it('should set selectedPatient when setPatient is called', () => {
      const item = { users: [{ id: '1', name: 'John Doe' }] };
      structureService.userDetails = JSON.stringify({ enable_multi_admissions: '1' });
      spyOn(component.selectedItem, 'emit');
      component.setPatient(item);
      expect(component.selectedPatient).toEqual(item.users[0]);
    });

    it('should call emitSelection when setPatient is called', () => {
      const item = { users: [{ id: '1', name: 'John Doe' }], reset: false };
      structureService.userDetails = JSON.stringify({ enable_multi_admissions: '0' });
      spyOn(component.selectedItem, 'emit');
      component.setPatient(item);
      expect(component.selectedPatient).toEqual(item.users[0]);
      expect(component.selectedItem.emit).toHaveBeenCalledWith({ users: item.users, admissions: [], reset: false });
      expect(component.reset).toEqual(false);
    });
  });
  describe('setAdmissionId', () => {
    it('should emit selectedItem event when setAdmissionId is called', () => {
      const event: SelectionEvent = { id: '' };
      spyOn(component.selectedItem, 'emit');
      component.setAdmissionId(event);
      expect(component.selectedItem.emit).toHaveBeenCalledWith({ users: [], admissions: [], reset: false });
      expect(component.reset).toEqual(false);
    });
    it('should emit selectedItem event when setAdmissionId is called: pdg info included', () => {
      const event: SelectionEvent = { id: '1' };
      component.includePDGInfo = true;
      spyOn(component.selectedItem, 'emit');
      component.setAdmissionId(event);
      expect(component.selectedItem.emit).toHaveBeenCalledWith({ users: [], admissions: [{ id: '1' }], reset: false });
      expect(component.reset).toEqual(false);
    });
  });
  describe('ngOnChanges', () => {
    it('should set reset to true when ngOnChanges is called', () => {
      component.reset = false;
      component.ngOnChanges({ reset: { currentValue: true, previousValue: null, firstChange: true, isFirstChange: () => true } });
      expect(component.reset).toEqual(true);
    });
  });
});
