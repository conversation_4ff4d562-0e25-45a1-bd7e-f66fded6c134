<div class="search-container">
  <div class="row" *ngIf="searchType === 'pdg'">
    <div class="col-md-3 pt-1">
      <div>{{ 'LABELS.PATIENT' | translate }}</div>
    </div>
    <div class="col-md-8">
      <app-pdg-dropdown [siteIds]="siteIds" (selectedItem)="setPatient($event)"></app-pdg-dropdown>
    </div>
  </div>
  <div class="row" *ngIf="searchType === 'patient'">
    <div [ngClass]="customClass ? customClass : 'col-md-3 pt-1'">
      <div>{{ 'LABELS.PATIENT' | translate }}</div>
    </div>
    <div class="col-md-8">
      <app-user-dropdown
        [multiSelection]="false"
        [requestParams]="requestParams"
        [userType]="searchType"
        [disableContactlessUsers]="disableContactlessUsers"
        (selectedUserDetails)="setPatient($event)"
        [siteIds]="siteIds"
        [hideSubList]="hideSubList"
        [reset]="reset"
        [listType]="listType"
      >
      </app-user-dropdown>
    </div>
  </div>
  <ng-container *ngIf="+userDataConfig.enable_multi_admissions && selectedPatient && selectedPatient.id">
    <div class="row">
      <div [ngClass]="customClass ? customClass : 'col-md-3 pt-1'">
        <div>{{ 'ADMISSION.LABELS.ADMISSION' | translate }}</div>
      </div>
      <div class="col-md-8">
        <app-admissions-dropdown
          [selectedPatient]="selectedPatient.id"
          (selectedItem)="setAdmissionId($event)"
          [siteIds]="siteIds"
          [includePDGInfo]="includePDGInfo"
        ></app-admissions-dropdown>
      </div>
    </div>
  </ng-container>
</div>
