import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges } from '@angular/core';
import { StructureService } from 'app/structure/structure.service';
import { GroupsListingRequest } from 'app/models/message-center/messageCenter';
import { MessageService } from 'app/services/message-center/message.service';
import { isBlank, formatUserName } from 'app/utils/utils';
import { UserListParams } from 'app/structure/shared/user-dropdown/user-dropdown.interface';
import { GroupType } from '../../../constants/constants';
import { ToolTipService } from 'app/structure/tool-tip.service';

@Component({
  selector: 'app-pdg-members',
  templateUrl: './pdg-members.component.html',
  styleUrls: ['./pdg-members.component.scss']
})
export class PdgMembersComponent implements OnInit, OnChanges {
  private site: string;
  @Input() set siteIds(value: string) {
    this.site = value;
    this.setUserListParams();
  }

  get siteIds(): string {
    return this.site;
  }
  @Input() chatWithFilterTenantId: number;
  @Input() reset = false;
  @Output() selectedItems = new EventEmitter<any>();

  showMembers = false;
  pdgRoles: any[] = [];
  pdgMembers: any[] = [];
  isLoading = false;
  selectedAdmissions: any[] = [];
  selectedPatientIds: any[] = [];
  admissionDetails: any;
  selectedUsers: any[] = [];
  isMultiAdmissionsEnabled: boolean;
  requestParams: UserListParams;

  constructor(private messageService: MessageService, private structureService: StructureService, private tooltipService: ToolTipService) {
    this.isMultiAdmissionsEnabled = this.structureService.isMultiAdmissionsEnabled;
  }

  ngOnInit(): void {
    this.setUserListParams();
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (!isBlank(changes.reset) && changes.reset.currentValue) {
      this.reset = changes.reset.currentValue;
    }
  }
  /**
   * Get group members by group id
   */
  getGroupMembers(): void {
    this.showMembers = !this.showMembers;
    if (this.showMembers && this.isPatientOrAdmissionSelected) {
      this.isLoading = true;
      const parameter: GroupsListingRequest = {
        data: {
          patients: [
            {
              id: +this.selectedPatientIds[0].userId,
              ...(this.isMultiAdmissionsEnabled && this.selectedAdmissions.length && { admissionId: this.selectedAdmissions[0].id })
            }
          ]
        }
      };
      this.messageService
        .fetchGroupData(parameter, GroupType.PATIENTGROUP)
        .toPromise()
        .then((groupData) => {
          this.isLoading = false;
          if (groupData && groupData.success && groupData.data) {
            this.pdgMembers = groupData.data.memberDetails || [];
            this.pdgRoles = groupData.data.selectedRoles || [];
          }
        });
    }
  }

  get isPatientOrAdmissionSelected(): boolean {
    // Check if either patient or admission is selected and multi admission on/off case
    return (this.selectedPatientIds.length && !this.isMultiAdmissionsEnabled) || (this.selectedAdmissions.length && this.isMultiAdmissionsEnabled);
  }
  /**
   * @param item
   * Set selected patient id and admission id
   */
  setPatientId(item: any): void {
    this.showMembers = false;
    this.selectedPatientIds = item.users;
    this.selectedAdmissions = item.admissions;
    if (
      this.isPatientOrAdmissionSelected &&
      !this.isUserAlreadySelected(this.selectedPatientIds[0].userId, this.selectedAdmissions.length ? this.selectedAdmissions[0].id : '')
    ) {
      const user = this.selectedPatientIds[0];
      const userToAdd = { ...user, displayname: user.name };
      if (this.selectedAdmissions.length && this.selectedAdmissions[0].id) {
        // Format the user name with admission name
        userToAdd.displayname = formatUserName(
          { patientName: user.name, admission: this.selectedAdmissions[0].name },
          'admission',
          this.tooltipService
        );
        userToAdd.admissionId = this.selectedAdmissions[0].id;
      }
      this.selectedUsers.push(userToAdd);
    }
    if (isBlank(this.selectedPatientIds) && item.reset) {
      this.selectedUsers = [];
    }
    this.selectedItems.emit(this.selectedUsers);
    this.reset = false;
  }
  /**
   * @param pdg
   * Remove selected patient
   */
  removePatient(pdg: any): void {
    this.selectedUsers = this.selectedUsers.filter((item) => item.userId !== pdg.userId);
    this.selectedItems.emit(this.selectedUsers);
  }
  /**
   * Set user list params for the user dropdown component
   */
  private setUserListParams(): void {
    const userData = JSON.parse(this.structureService.userDetails);
    this.requestParams = {
      status: 'notRejected',
      roleId: 3,
      optionShow: 'patient',
      excludeLogginedUser: userData.userId,
      isTenantRoles: 'null',
      isFromChat: 1,
      userGroup: userData.group,
      isSchedule: false,
      pageCount: 0,
      excludeRoleId: false,
      siteIds: this.siteIds,
      needVirtualPatients: userData.config.enable_appless_video_chat === '1'
    };
    if (!isBlank(this.chatWithFilterTenantId)) {
      this.requestParams.chatWithFilterTenantId = this.chatWithFilterTenantId;
    }
  }

  private isUserAlreadySelected(userId: number, admissionId: string): boolean {
    return this.selectedUsers.some((user) => user.userId === userId && (this.isMultiAdmissionsEnabled ? user.admissionId === admissionId : true));
  }
}
