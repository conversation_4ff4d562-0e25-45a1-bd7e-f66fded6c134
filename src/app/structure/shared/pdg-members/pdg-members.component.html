<app-patient-dropdown
  [searchType]="'patient'"
  [hideSubList]="true"
  [requestParams]="requestParams"
  [siteIds]="siteIds"
  (selectedItem)="setPatientId($event)"
  [reset] = "reset"
></app-patient-dropdown>

<div class="row">
  <div class="col-md-2"></div>
  <div class="col-md-8" *ngIf="isPatientOrAdmissionSelected">
    <span class="accordion-header">
      {{ 'LABELS.VIEW_MEMBERS' | translate }}
      <span
        [ngClass]="showMembers ? 'fa fa-chevron-circle-up' : 'fa fa-chevron-circle-down'" 
        (click)="getGroupMembers()"
      ></span>
    </span>
    <div class="accordion-box" *ngIf="showMembers">
      <div class="accordion-content">
        <div *ngIf="isLoading" class="loader">
          {{ 'MESSAGES.LOADING_MSG' | translate }}
        </div>
        <div *ngIf="!isLoading">
          <ul *ngIf="pdgMembers.length">
            <h6 class="member-cls">{{ 'TITLES.MEMBERS' | translate }}</h6>
            <li *ngFor="let member of pdgMembers" class="pl-4">
              {{ member.displayName }}
            </li>
          </ul>
          <ul *ngIf="pdgRoles.length">
            <h6 class="role-cls">{{ 'TITLES.ROLES' | translate }}</h6>
            <li *ngFor="let role of pdgRoles" class="pl-4">
              {{ role.name }}
            </li>
          </ul>
          <span *ngIf="!pdgMembers.length && !pdgRoles.length" class="pl-3">
            {{ 'LABELS.NO_MEMBERS_AVAILABLE_IN_PDG' | translate }}
          </span>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row" *ngIf="selectedUsers.length">
  <div class="col-md-11">
    <app-accordion
      [userList]="selectedUsers"
      [showListWhenUsersSelected]="true"
      (removedUser)="removePatient($event)"
    ></app-accordion>
  </div>
</div>
