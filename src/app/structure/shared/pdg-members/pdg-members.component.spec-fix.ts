import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CommonTestingModule } from 'app/utils/common-testing.module';
import { MessageService } from 'app/services/message-center/message.service';
import { StructureService } from 'app/structure/structure.service';
import { PdgMembersComponent } from './pdg-members.component';

describe('PdgMembersComponent', () => {
  let component: PdgMembersComponent;
  let structureService: StructureService;
  let fixture: ComponentFixture<PdgMembersComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [CommonTestingModule],
      providers: [MessageService]
    }).compileComponents();
    structureService = TestBed.get(StructureService);
    structureService.userDetails = JSON.stringify({});
    fixture = TestBed.createComponent(PdgMembersComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});