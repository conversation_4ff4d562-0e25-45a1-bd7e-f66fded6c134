import { Injectable } from '@angular/core';
import { StructureService } from './../../structure/structure.service';
@Injectable()
export class GlobalDataShareService {
  filingCenter = {
    fileUrlDownload:null,
    fileTargetName:null,
    documentOwner:null,
    type:null,
    defaultOutgoingFilingCenter:null,
    exchangeData:null
  };
  filingCenterActivity=null;
  signatureRequestInboxData=null;
  signatureRequestPollingInboxData=null;
  currentVideoChatDetails = {initiator:null,joinee:null};
  selectedTenantDetails;
  chatWithTenantFilterDetails;
  userData;
  privileges;
  configData;
  isOtherTenantPatients;
  constructor(public _structureService:StructureService) {}
  
  setFilingCenterCopyDetails(fileUrlDownload,fileTargetName,documentOwner,type,defaultOutgoingFilingCenter,exchangeData){
    this.filingCenter.fileUrlDownload=fileUrlDownload;
    this.filingCenter.fileTargetName=fileTargetName;
    this.filingCenter.documentOwner=documentOwner;
    this.filingCenter.exchangeData=exchangeData;
    this.filingCenter.type=type;
    this.filingCenter.defaultOutgoingFilingCenter=defaultOutgoingFilingCenter;
  }
  getFilingCenterCopyDetails(){
    return this.filingCenter;
  }
  clearFilingCenterCopyDetails(){
    this.filingCenter.fileUrlDownload=null;
    this.filingCenter.fileTargetName=null;
    this.filingCenter.documentOwner=null;
    this.filingCenter.exchangeData==null;
    this.filingCenter.type=null;
    this.filingCenter.defaultOutgoingFilingCenter=null;
  }
  setFilingCenterActivity(activity){
    this.filingCenterActivity = activity;
  }
  getFilingCenterActivity(){
    return this.filingCenterActivity;
  }
  clearFilingCenterActivity(){
    this.filingCenterActivity=null;
  }
  setSignatureRequestInboxData(obj){
    this.setSignatureRequestInboxData = obj;
  }
  setSignatureRequestPollingInboxData(obj){
    this.signatureRequestPollingInboxData = obj;
  }
  getSignatureRequestInboxData(){
    return this.setSignatureRequestInboxData;
  }
  getSignatureRequestPollingInboxData(){
    return this.signatureRequestPollingInboxData;
  }
  setVideoChatDetails(details,type){
    if(type=='initiator'){
      this.currentVideoChatDetails.initiator = details;
      this.currentVideoChatDetails.joinee = null;
    }else{
      this.currentVideoChatDetails.joinee = details;
      this.currentVideoChatDetails.initiator = null;
    }  
  }

  getVideoChatDetails(){    
    return this.currentVideoChatDetails;
  }

  setSelectedTenantDetails(tenantDetails){
    this.selectedTenantDetails = tenantDetails;
  }

  getselectedTenantDetails(){
    return this.selectedTenantDetails;
  }
  setchatWithTenantFilterDetails(tenantDetails){
    this.chatWithTenantFilterDetails = tenantDetails;
  }

  getchatWithTenantFilterDetails(){
    return this.chatWithTenantFilterDetails;
  }
  getTenantIds(privileges) {
    
    let config = this._structureService.userDataConfig;
                
                let userDetails = this._structureService.userDetails;
                this.configData = JSON.parse(config);
                this.userData = JSON.parse(userDetails);
    
    this.privileges = privileges;
    if (this.userData.organizationMasterId != 0 
      && this.userData.crossTenantsDetails.length > 1
      && this.privileges.indexOf('allowOrganizationSwitching') != -1
      && this.privileges.indexOf('allowMultipleOrganizationsPatientCommunication') != -1 
      && this.configData.allow_multiple_organization == 1 
      && this.userData.masterEnabled != 1
      && this.configData.enable_nursing_agencies_visibility_restrictions != 1
  ) {
      console.log('111111111111111111');
      //Master is not enabled and user with allowMultipleOrganizationsPatientCommunication privilege
      this.isOtherTenantPatients = true;
       
      
  } else if(this.userData.organizationMasterId != 0
      && this.userData.crossTenantsDetails.length > 1
      && this.userData.masterEnabled == '1'
      && this.userData.isMaster =='1') {
      // Master enabled and user is a staff logged in with allowMultipleOrganizationsPatientCommunication privilege
      this.isOtherTenantPatients = true;
      
  } else if((this.userData.organizationMasterId != 0
      && this.userData.crossTenantsDetails.length > 1
      && this.userData.isMaster =='0' 
      && this.userData.masterEnabled == '1'
      && this.userData.group != '3')) {

      this.isOtherTenantPatients = false;        

  } else if(this.configData.enable_nursing_agencies_visibility_restrictions == 1) {
      if(this.userData.nursing_agencies == "") {
          if (this.userData.organizationMasterId != 0 
              && this.userData.crossTenantsDetails.length > 1 
              && this.privileges.indexOf('allowOrganizationSwitching') != -1 && this.privileges.indexOf('allowRoleTenantSwitching') != -1
              && this.configData.allow_multiple_organization == 1 
              && this.userData.masterEnabled == '0'       
          ) {
              this.isOtherTenantPatients = true;              
          }
      } else {
          this.isOtherTenantPatients = false;
      }
  } else {
      this.isOtherTenantPatients = false;
  }





if(
  (this.userData.organizationMasterId != 0 
  && this.userData.crossTenantsDetails.length > 1 
  && this.privileges.indexOf('allowOrganizationSwitching') != -1 
  && this.privileges.indexOf('allowMultipleOrganizationsPatientCommunication') != -1 
  && this.configData.allow_multiple_organization == 1 
  && this.userData.masterEnabled != 1 
  && this.configData.enable_nursing_agencies_visibility_restrictions != 1
  )
  ||
  (this.userData.organizationMasterId != 0 
  && this.userData.crossTenantsDetails.length > 1 
  && this.userData.masterEnabled == '1' 
  && this.userData.isMaster =='1')
  ||
  (this.configData.enable_nursing_agencies_visibility_restrictions == 1 
  && this.userData.nursing_agencies == "" 
  && this.userData.organizationMasterId != 0 
  && this.userData.crossTenantsDetails.length > 1 
  && this.privileges.indexOf('allowOrganizationSwitching') != -1 
  && this.privileges.indexOf('allowRoleTenantSwitching') != -1 
  && this.configData.allow_multiple_organization == 1 
  && this.userData.masterEnabled == '0'       
  )
) {
  console.log('FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF');
  console.log(this.userData);
  console.log(this.userData.crossTenantsDetails);
  let tenantIds = this.userData.crossTenantsDetails.map((row)=>{
    return row.id;
  })
  return tenantIds;
} else {
  console.log('MMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMMM');
  return [this.userData.tenantId];
}





  }

}
