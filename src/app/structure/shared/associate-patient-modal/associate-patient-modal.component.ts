import { DatePipe } from '@angular/common';
import { ChangeDetectorRef, Component, EventEmitter, HostListener, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators } from '@angular/forms';
import { ModalComponent } from 'app/structure/signatures/modal.component';
import { StructureService } from 'app/structure/structure.service';
import { EnrollService } from 'app/structure/user-registration/enroll.service';
import { isBlank, IsEmail } from 'app/utils/utils';
import * as moment from 'moment';
import { Subject } from 'rxjs';
import { CONSTANTS, DateFormat, LabelValues } from 'app/constants/constants';
import { associatePatientModalConfig } from '../../shared/associate-patient-modal/associate-patient-modal.interface';
import { GlobalDataShareService } from '../global-data-share.service';
import { ToolTipService } from '../../tool-tip.service';

declare let $: any;
declare let swal: any;

@Component({
  selector: 'app-associate-patient-modal',
  templateUrl: './associate-patient-modal.component.html',
  styleUrls: ['./associate-patient-modal.component.scss'],
  providers: [DatePipe]
})
export class AssociatePatientModalComponent implements OnInit {
  @ViewChild(ModalComponent) child: ModalComponent;
  @Input() selectedSites;
  @Input() title = 'Associate Patient';
  @Input() config = associatePatientModalConfig;
  @Output() closeModal = new EventEmitter<boolean>();
  @Output() patientDetails = new EventEmitter();
  @Output() selectedAssocPatient = new EventEmitter();
  userData: any;
  siteIdEmpty = false;
  assocSiteId: any;
  userConfig: any;
  isSiteMandatory = false;
  assocSelected: any;
  eventsSubject: Subject<void> = new Subject<void>();
  selectedAssocSiteId: any;
  newPatient: FormGroup;
  hasData = false;
  searchRequired = true;
  noRecordMessage = false;
  disableSearch = true;
  disableClear = true;
  hideSiteSelection: boolean;
  showPatientForm = false;
  searchString;
  patientDataLoadingMsg = false;
  patientTable;
  datam;
  associatePatientData = {
    email: '',
    firstname: '',
    lastname: '',
    dob: '',
    cell: '',
    zipcode: '',
    tenantId: '',
    operation: 0,
    dobDay: null,
    dobMonth: null,
    dobYear: null,
    mrn: null
  };
  data = {
    tag_id: '',
    documentReferanceId: 0,
    allowPersonalSignature: false,
    allowPendingApproveSignature: false,
    documentName: '',
    recipient: { id: 0, cmisId: '', displayName: '', avatar: '' },
    associate: { id: 0, cmisId: '', displayName: '', avatar: '' },
    fileup: '',
    patient: { id: 0, cmisId: '', displayName: '', avatar: '' }
  };
  searchResetFlag = 0;
  searchInTenantIds;
  totalCountInviteSearch;
  searchData = [];
  dataLoadingMsg;
  iscellNumber = false;
  isEmail = false;
  isFirstName = false;
  isLastName = false;
  isZipCode = false;
  isDob = false;
  isReqEmail = false;
  isReqFirstName = false;
  isReqLastName = false;
  isReqZipCode = false;
  isReqDob = false;
  userDataConfig;
  isEmailValid = IsEmail;
  patientNotExistMsg: any;
  isPatientFormSubmitted = false;
  constructor(
    public structureService: StructureService,
    private enrollService: EnrollService,
    private fb: FormBuilder,
    private datePipe: DatePipe,
    public globalDataShareService: GlobalDataShareService,
    private tooltipService: ToolTipService,
    private cdRef: ChangeDetectorRef
  ) {
    this.userData = JSON.parse(this.structureService.userDetails);
    this.userDataConfig = JSON.parse(this.structureService.userDataConfig);
    this.userConfig = this.structureService.getUserdata().config;
    this.title = this.tooltipService.getTranslateData('TITLES.ASSOCIATE_PATIENT');
  }
  @HostListener('click', ['$event'])
  onClick(event: MouseEvent) {
    const targetElement = event.target as HTMLElement;
    if (targetElement.id === LabelValues.ASSOCIATE_PATIENT_CLICK) {
      const patientDetailId = $(event.target).data('id');
      const patientIndex = this.searchData.findIndex((x) => x.userId.toString() === patientDetailId.toString());
      let selectedAssocPatientName;
      if (this.userData.config && this.userData.config.enable_multisite && +this.userData.config.enable_multisite === 1) {
        selectedAssocPatientName = `${this.searchData[patientIndex].listDisplayName} - ${this.searchData[patientIndex].site_name}`;
      } else {
        selectedAssocPatientName = this.searchData[patientIndex].listDisplayName;
      }
      const selectedPatientDetails = {
        patientDetailId,
        selectedAssocPatientName,
        siteId: this.searchData[patientIndex].site_id,
        searchData: this.searchData
      };
      this.selectedAssocPatient.emit(selectedPatientDetails); // To emit the selected patient details from table
      const table = $('#patient-list').DataTable();
      table.destroy(true);
      this.closeAssociateModal();
      this.searchClear();
      $('#assocModal').modal('hide');
    }
  }
  ngOnChanges() {
    this.emitEventToSelectSites('displaySelectedSitesOnly');
  }
  ngOnInit() {
    this.searchInTenantIds = this.globalDataShareService.getTenantIds(this.structureService.getCookie('userPrivileges'));
    this.newPatient = this.fb.group({
      searchInput: [],
      mrn: []
    });
    $(() => {
      $('#dob-date-picker').combodate({
        format: DateFormat.DDMMYY_FORMAT_HYPHEN,
        template: 'MMM D YYYY',
        minYear: 1900,
        firstItem: 'empty',
        maxYear: new Date().getFullYear(),
        customClass: 'form-control select2 dob-sec',
        smartDays: true
      });

      $('.month, .day, .year').select2();
      $('.month').on('select2:select', (e) => {
        this.associatePatientData.dobMonth = $(e.target).val();
        $('.day').select2('open');
      });
      $('.day').on('select2:select', (e) => {
        $('.year').select2('open');
        this.associatePatientData.dobDay = $(e.target).val();
      });
      $('.year').on('select2:select', (e) => {
        this.associatePatientData.dobYear = $(e.target).val();
      });
    });
    this.getVirtualPatientOptions();
    this.searchClear();
    this.patientNotExistMsg = '';
  }

  closeAssociateModal() {
    this.isPatientFormSubmitted = false;
    this.siteIdEmpty = false;
    this.isSiteMandatory = false;
    this.selectedAssocSiteId = '0';
    this.emitEventToSelectSites('removeSelectedSite');
    this.closeModal.emit(true);
    this.clearDobValues();
    this.showPatientForm = false;
    this.newPatient.reset();
    this.searchClear();
  }
  emitEventToSelectSites(status): void {
    this.assocSelected = status;
    this.eventsSubject.next(this.assocSelected);
  }
  getAssocSiteIds(data: any, page: string) {
    this.assocSiteId = data.siteId;
    this.selectedAssocSiteId = data.siteId;
    if (page === LabelValues.ASSOCIATE_PATIENT) {
      if (this.assocSiteId.toString() !== '0') {
        this.assocSiteId = this.assocSiteId.toString();
        this.siteIdEmpty = false;
      }
      this.searchClear();
    }
  }
  searchClear() {
    this.newPatient.patchValue({
      searchInput: ''
    });
    this.hasData = false;
    this.searchRequired = false;
    this.noRecordMessage = false;
    this.disableSearch = true;
    this.disableClear = true;
  }
  hideDropdown(hideItem: any) {
    this.hideSiteSelection = hideItem.hideItem;
  }
  searchAssociatePatientWithTems() {
    this.showPatientForm = false;
    this.searchRequired = true;
    this.hasData = false;
    const formdata = this.newPatient.value;
    this.searchString = formdata.searchInput;
    const searchStringValue = IsEmail(this.searchString) === '' ? this.searchString : '';
    this.patientNotExistMsg = this.tooltipService.getTranslateDataWithParam('VALIDATION_MESSAGES.PATIENT_NOT_EXIST', {
      searchString: searchStringValue
    });
    const searchTerm = $('#searchTxt').val();
    if (!isBlank(searchTerm)) {
      this.loadTable('refresh');
    }
  }
  loadTable(refresh = '') {
    this.patientDataLoadingMsg = true;
    const self = this; // self set for the data table.
    let datas: any;
    if (!isBlank(refresh)) {
      localStorage.removeItem('DataTables_patient-list_/');
    }
    if (this.patientTable) {
      this.patientTable.destroy();
    }
    $('.data-table').html('<table class= "table table-hover table-responsive" id = "patient-list" width = "100%"> </table>');
    const x = (this.patientTable = $('#patient-list').DataTable({
      autoWidth: false,
      responsive: true,
      bServerSide: true,
      bpagination: true,
      stateSave: true,
      bInfo: true,
      bFilter: false,
      lengthMenu: [
        [25, 50],
        [25, 50]
      ],
      ajax: function (dat, callback, settings) {
        let orderData;
        let searchText = '';
        let orderby;
        if (
          settings.oAjaxData.start !== 0 &&
          self.datam &&
          self.datam.aaData &&
          self.datam.aaData.length === 1 &&
          settings._iDisplayStart !== 0 &&
          isBlank(searchText)
        ) {
          settings.oAjaxData.start = settings.oAjaxData.start - settings.oAjaxData.length;
          settings._iDisplayStart = settings.oAjaxData.start;
        }
        const formdata = self.newPatient.value;
        if (formdata.searchInput && self.searchResetFlag === 0) {
          dat.start = 0;
          settings.oAjaxData.search.value = formdata.searchInput;
          settings.oAjaxData.start = 0;
          settings._iDisplayStart = 0;
          self.searchResetFlag = 1;
        }

        this.userData = JSON.parse(self.structureService.userDetails);
        let enrollSearch = {
          operation: 'associatedPatientSearch',
          firstname: formdata.searchInput,
          limit: dat.length,
          offset: dat.start,
          orderData: orderData,
          orderby: orderby,
          searchText: formdata.searchInput,
          searchInTenantIds: self.searchInTenantIds.join(),
          siteIds: parseInt(self.assocSiteId)
        };

        self.structureService.searchInvites(enrollSearch).then((dataa) => {
          self.disableSearch = true;
          datas = {};
          self.datam = {};
          if (dataa['data'].length === 0) {
            self.noRecordMessage = true;
          } else {
            self.noRecordMessage = false;
          }
          if (dat.start === 0) {
            this.totalCt = dataa['totalCount'];
            self.structureService.setCookie('totalCountInvitePatientSearch', this.totalCt, 1);
            self.totalCountInviteSearch = this.totalCt;
          } else {
            if (self.totalCountInviteSearch) {
              this.totalCt = self.totalCountInviteSearch;
            } else {
              this.totalCt = self.structureService.getCookie('totalCountInvitePatientSearch');
              self.totalCountInviteSearch = this.totalCt;
            }
          }
          datas = dataa['data'] ? dataa['data'] : [];
          self.searchData = datas;
          let draw;
          let total;
          if (datas && datas.length === 0 && isBlank(searchText)) {
            draw = 0;
            total = 0;
            $('#hide-table').css('display', 'none');
          } else {
            self.hasData = true;
            self.searchData = datas.filter((result) => {
              let date = '';
              if (result.caregiver_dob) {
                date = result.caregiver_dob;
                let dobDay = new Date(date).getDay();
                if (date && !isNaN(dobDay)) {
                  date = date.replace(/-/g, '/');
                  try {
                    date = moment(date).format(DateFormat.MMDDYY_FORMAT_SLASH);
                  } catch (e) {
                    date = '';
                  }
                } else {
                  date = '';
                }
              } else {
                date = '';
              }
              result.caregiver_dob_formatted = date;
              date = '';
              if (result.dob) {
                date = result.dob;
                let dobDay = new Date(date).getDay();
                if (date && !isNaN(dobDay)) {
                  date = date.replace(/-/g, '/');
                  try {
                    date = moment(date).format(DateFormat.MMDDYY_FORMAT_SLASH);
                  } catch (e) {
                    date = '';
                  }
                } else {
                  date = '';
                }
              } else {
                date = '';
              }
              result.dob_formatted = date;
              let listDisplayName = result.caregiver_displayname ? `${result.displayname}(${result.caregiver_displayname})` : result.displayname;
              listDisplayName += result.dob_formatted ? `-${result.dob_formatted}` : '';
              listDisplayName += result.IdentityValue ? `[MRN: ${result.IdentityValue} ]` : '';
              listDisplayName += result.passwordStatus.toString() === 'true' ? ' (Enrolled)' : ' (Virtual)';

              result.listDisplayName = listDisplayName;
              return true;
            });
            $('#hide-table').css('display', 'block');
            draw = dat.draw;
            total = this.totalCt;
          }
          self.datam = {
            draw: draw,
            recordsTotal: total,
            recordsFiltered: total,
            aaData: datas
          };
          callback(self.datam);
          self.patientDataLoadingMsg = false;
        });
      },
      initComplete: function () {},
      fnDrawCallback: function () {
        if (self.searchData.length === 0) {
          let table = $('#patient-list').DataTable();
          table.destroy(true);
          self.noRecordMessage = true;
          self.patientDataLoadingMsg = false;
        } else {
          self.noRecordMessage = false;
          self.dataLoadingMsg = true;
        }
      },
      columns: [
        { title: '#' },
        { title: 'First Name', data: 'firstname' },
        { title: 'Last Name', data: 'lastname' },
        { title: 'DOB', data: 'dob' },
        { title: 'MRN', data: 'IdentityValue' },
        { title: 'Source', data: 'registration_type' },
        { title: 'Last Login', data: 'last_login' },
        { title: 'Actions', className: 'details-control' }
      ],
      columnDefs: [
        {
          data: null,
          width: '10%',
          orderable: false,
          targets: 0,
          render: function (data, type, row, meta) {
            let roNo = meta.row + 1 + meta.settings._iDisplayStart;
            return roNo;
          }
        },
        {
          data: null,
          orderable: false,
          width: '10%',
          targets: 1
        },
        {
          data: null,
          orderable: false,
          width: '10%',
          targets: 2
        },
        {
          data: null,
          orderable: false,
          width: '10%',
          render: (data, type, row) => {
            if (data) {
              let dateParts = data.split('-');
              if (dateParts.length > 1) {
                let date = new Date(row.dob);
                return moment(date).format('MM-DD-YYYY');
              } else {
                return '';
              }
            } else {
              return '';
            }
          },
          targets: 3
        },
        {
          data: null,
          orderable: false,
          render: (data) => {
            if (data === 0) {
              return 'Manual';
            } else if (data === 1 || data === 2) {
              return 'Enroll';
            } else if (data === 3) {
              return 'CHIE';
            } else if (data === 4) {
              return 'Import';
            } else if (data === 5) {
              return 'Consolo';
            } else if (data === 6) {
              return 'User Center';
            } else {
              return '';
            }
          },
          width: '15%',
          targets: 5
        },
        {
          data: null,
          orderable: false,
          render: (data, type, row) => {
            if (row.password && row.password) {
              if (data && data != null) {
                return this.datePipe.transform(data * 1000, 'mediumDate') + ' ' + this.datePipe.transform(data * 1000, 'shortTime');
              } else {
                return 'Not Logged in';
              }
            } else {
              return '<span style="font-size: 12px;" class="badge badge-default mr-2 mb-2">Virtual Patient</span>';
            }
          },
          width: '20%',
          targets: 6
        },
        {
          data: null,
          orderable: false,
          targets: 4,
          width: '15%'
        },
        {
          data: null,
          width: '10%',
          orderable: false,
          targets: 7,
          render: (data) => {
            let actions = ` <div class="btn-group mr-2 mb-2 no-margin">`;
            if(!this.config.actions.showOnlyDetailView) {
              if (data.userRole.toString() === 'Caregiver') {
                actions += `<a class="pull-right btn btn-sm" style="background-color: #9fa4a7;" title = ${this.tooltipService.getTranslateData(
                  'TITLES.CAREGIVER_IS_NOT_ALLOWED'
                )}> <i class="fa fa-user-plus"> </i> </a> `;
              } else if (data.tenantId.toString() === this.userData.crossTenantId.toString()) {
                actions += `<a class="pull-right btn btn-sm " title = ${this.tooltipService.getTranslateData(
                  'TITLES.ASSOCIATE_PATIENT'
                )} id = "associatePatientClick" data-id="${data.userid}" > <i id="associatePatientClick" data-id="${
                  data.userid
                }" class="fa fa-user-plus" > </i> </a > `;
              } else {
                actions += `<a class="pull-right btn btn-sm" style="background-color: #9fa4a7;" title = ${this.tooltipService.getTranslateData(
                  'TITLES.PATIENT_FROM_OTHER_BRANCH_NOT_ALLOWED'
                )}> <i class="fa fa-user-plus"> </i> </a> `;
              }
            }
            actions += `<a class="pull-right btn btn-sm details-control1" title="${this.tooltipService.getTranslateData(
              'TITLES.DETAILS'
            )}"> <i class="fa fa-eye" > </i></a >
                    </div>`;
            return actions;
          }
        }
      ]
    }));

    const detailRows = [];
    $('#patient-list tbody').off('click', '.details-control1');
    $('#patient-list tbody').on('click', '.details-control1', (event) => {
      const tr = $(event.currentTarget).closest('tr');
      const row = x.row(tr);

      if (row.child.isShown()) {
        row.child.hide();
        tr.removeClass('shown');
      } else {
        row.child(this.formatSubTable(row.data())).show();
        tr.addClass('shown');
      }
    });
    x.on('draw', function () {
      $.each(detailRows, function (i, id) {
        $(`#${id}.details-control1`).trigger('click');
      });
    });
  }
  addPatient() {
    if (this.userData && +this.userData.config.enable_multisite === 1 && !isBlank(this.userData.mySites) && this.userData.mySites.length === 1) {
      this.siteIdEmpty = false;
      this.assocSiteId = this.userData.mySites[0].id.toString();
    } else if (this.userData && this.userData.config && +this.userData.config.enable_multisite !== 1) {
      this.siteIdEmpty = false;
    }
    const userDetails = this.structureService.getUserdata();
    const msg = this.tooltipService.getTranslateData('LABELS.ASSOCIATE_PATIENT_CONFIRM_MESSAGE');
    this.searchRequired = false;
    this.hasData = false;
    let self = this;
    if (+userDetails.config.enable_progress_note_integration === 1) {
      swal(
        {
          title: '',
          text: msg,
          type: 'error',
          showCancelButton: true,
          cancelButtonClass: 'btn-default',
          confirmButtonClass: 'btn-warning',
          confirmButtonText: 'Ok',
          closeOnConfirm: true
        },
        function (isConfirm) {
          if (isConfirm) {
            self.isSiteMandatory = true;
            self.disableSearch = true;
            self.disableClear = true;
            self.showPatientForm = true;
            self.newPatient.patchValue({ searchInput: null });
            self.loadTable(null);
          } else {
            self.newPatient.patchValue({ searchInput: null });
          }
        }
      );
    } else {
      this.disableSearch = true;
      self.disableClear = true;
      this.showPatientForm = true;
      this.newPatient.patchValue({ searchInput: null });
      this.loadTable(null);
      self.isSiteMandatory = true;
    }
  }

  formatSubTable(row) {
    const err = '';
    let email = '';
    if (!row.username.includes('unknown_')) {
      email = IsEmail(row.username);
    }
    let xtraDetails = `<table cellpadding="5" cellspacing="0" border="0" style="width:100%;padding-left:50px;">${err}`;

    xtraDetails += `<tr>
                        <td>Branch :</td>
                        <td>${row.tenantName}</td>
                    </tr>
                    <tr>
                        <td>Email :</td>
                        <td>${email}</td>
                    </tr>
                    <tr>
                        <td>Created On :</td>
                        <td>${moment(row.created_at).format('LLL')}</td>
                    </tr>
                    `;
    return xtraDetails;
  }

  disableSearchButton() {
    this.searchRequired = true;
    this.patientDataLoadingMsg = false;
    this.noRecordMessage = false;
    const searchValue = this.newPatient.controls.searchInput.value;
    if (isBlank(searchValue)) {
      this.disableSearch = true;
      this.disableClear = true;
    } else {
      this.disableClear = false;
      this.disableSearch = false;
    }
  }
  getVirtualPatientOptions() {
    this.structureService.getVirtualPatientOptions().then((data) => {
      const parsedResponceData = data;
      const vitualpsettings = JSON.parse(parsedResponceData['getVirtualPatientOptions']);
      if (vitualpsettings.cellNumber.view && vitualpsettings.cellNumber.required) {
        this.iscellNumber = true;
        this.newPatient.addControl('cellno', new FormControl(null, Validators.required));
      } else if (vitualpsettings.cellNumber.view && !vitualpsettings.cellNumber.required) {
        this.iscellNumber = true;
        this.newPatient.addControl('cellno', new FormControl(null));
      }

      if (vitualpsettings.email.view && vitualpsettings.email.required) {
        this.isEmail = true;
        this.isReqEmail = true;
        this.newPatient.addControl('email', new FormControl(null, [Validators.required, Validators.pattern(CONSTANTS.emailValidationRegex)]));
      } else if (vitualpsettings.email.view && !vitualpsettings.email.required) {
        this.isEmail = true;
        this.newPatient.addControl('email', new FormControl(null, Validators.pattern(CONSTANTS.emailValidationRegex)));
      }

      if (vitualpsettings.firstName.view && vitualpsettings.firstName.required) {
        this.isFirstName = true;
        this.isReqFirstName = true;
        this.newPatient.addControl('firstName', new FormControl(null, Validators.required));
      } else if (vitualpsettings.firstName.view && !vitualpsettings.firstName.required) {
        this.isFirstName = true;
        this.newPatient.addControl('firstName', new FormControl(null));
      }
      if (vitualpsettings.lastName.view && vitualpsettings.lastName.required) {
        this.isLastName = true;
        this.isReqLastName = true;
        this.newPatient.addControl('lastName', new FormControl(null, Validators.required));
      } else if (vitualpsettings.lastName.view && !vitualpsettings.lastName.required) {
        this.isLastName = true;
        this.newPatient.addControl('lastName', new FormControl(null));
      }
      if (vitualpsettings.zipCode.view && vitualpsettings.zipCode.required) {
        this.isZipCode = true;
        this.isReqZipCode = true;
        this.newPatient.addControl('zipcode', new FormControl(null, Validators.required));
      } else if (vitualpsettings.zipCode.view && !vitualpsettings.zipCode.required) {
        this.isZipCode = true;
        this.newPatient.addControl('zipcode', new FormControl(null));
      }
      if (vitualpsettings.dob.view && vitualpsettings.dob.required) {
        this.isDob = true;
        this.isReqDob = true;
        this.newPatient.addControl('dobDay', new FormControl(null, Validators.required));
        this.newPatient.addControl('dobMonth', new FormControl(null, Validators.required));
        this.newPatient.addControl('dobYear', new FormControl(null, Validators.required));
      } else if (vitualpsettings.dob.view && !vitualpsettings.dob.required) {
        this.isDob = true;
        this.newPatient.addControl('dobDay', new FormControl(null));
        this.newPatient.addControl('dobMonth', new FormControl(null));
        this.newPatient.addControl('dobYear', new FormControl(null));
      }
      if (this.structureService.isMRNFieldMandatory) {
        this.newPatient.get('mrn').setValidators([Validators.required]);
        this.newPatient.get('mrn').updateValueAndValidity();
      } else {
        this.newPatient.get('mrn').clearValidators();
        this.newPatient.get('mrn').updateValueAndValidity();
      }
    });
  }
  createNewAssoc(form) {
    this.isPatientFormSubmitted = true;
    if (
      this.userData &&
      this.userData.config &&
      this.userData.config.enable_multisite &&
      +this.userData.config.enable_multisite === 1 &&
      (isBlank(this.selectedAssocSiteId) || this.selectedAssocSiteId.toString() === '0')
    ) {
      this.siteIdEmpty = true;
    } else {
      this.siteIdEmpty = false;
    }
    if (!isBlank(this.userData.mySites) && this.userData.mySites.length === 1) {
      this.siteIdEmpty = false;
      this.assocSiteId = this.userData.mySites[0]['id'].toString();
    }

    if (!form.valid || this.siteIdEmpty) {
      return false;
    }
    if (!this.structureService.isMultiPartMRNEnabled && !isBlank(form.value.mrn)) {
      const mrndata = {
        MRNumber: form.value.mrn,
        operation: 'mrnChecking'
      };
      this.enrollService.getPatientForCaregiver(mrndata).then((d: any) => {
        if (d.esiExist) {
          let date = d.dob;
          const dobDay = new Date(date).getDay();
          if (date && !isNaN(dobDay)) {
            date = date.replace(/-/g, '/');
            try {
              date = moment(date).format(DateFormat.MMDDYY_FORMAT_SLASH);
            } catch (e) {
              date = '';
            }
          } else {
            date = '';
          }
          const msg = this.tooltipService.getTranslateDataWithParam('MESSAGES.MRN_ALREADY_EXIST_WITH_NAME', {
            firstName: d.firstname,
            lastName: d.lastname,
            date,
            mrn: mrndata.MRNumber
          });
          const notify = $.notify(msg);
          setTimeout(() => {
            notify.update({ type: 'danger', message: `<strong>${msg}</strong>` });
          }, 1000);
        } else {
          this.submitPatientDetails(form);
        }
      });
    } else {
      this.submitPatientDetails(form);
    }
  }
  submitPatientDetails(form) {
    if (
      this.userData &&
      this.userData.config &&
      this.userData.config.enable_multisite &&
      +this.userData.config.enable_multisite === 1 &&
      (isBlank(this.assocSiteId) || this.assocSiteId.toString() === '0')
    ) {
      this.siteIdEmpty = true;
    }
    if (!this.siteIdEmpty) {
      const patientDetails = {
        siteId: this.assocSiteId,
        formData: form
      };
      this.patientDetails.emit(patientDetails);
      this.closeAssociateModal();
    }
  }
  clearDobValues() {
    this.associatePatientData.dobDay = null;
    this.associatePatientData.dobMonth = null;
    this.associatePatientData.dobYear = null;
    const dobDatePicker = document.getElementById('dob-date-picker');
    const combodateInstance = $(dobDatePicker).data('combodate');
    const dobDatePickerHtml = document.getElementById('dob-date-picker') as HTMLInputElement;
    if (combodateInstance) {
      combodateInstance.destroy();
    }
    dobDatePickerHtml.value = '';
    this.cdRef.detectChanges();
    $(dobDatePicker).combodate({
      format: 'DD-MM-YYYY',
      template: 'MMM D YYYY',
      minYear: 1900,
      firstItem: 'empty',
      maxYear: new Date().getFullYear(),
      customClass: 'form-control select2 dob-sec',
      smartDays: true
    });
    $('.month, .day, .year').select2();
    $('.month').on('select2:select', (e) => {
      this.associatePatientData.dobMonth = $(e.target).val();
      $('.day').select2('open');
    });
    $('.day').on('select2:select', (e) => {
      $('.year').select2('open');
      this.associatePatientData.dobDay = $(e.target).val();
    });
    $('.year').on('select2:select', (e) => {
      this.associatePatientData.dobYear = $(e.target).val();
    });
    this.cdRef.detectChanges();
  }
}
