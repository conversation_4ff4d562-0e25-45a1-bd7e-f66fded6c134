<form (ngSubmit)="createNewAssoc(associatePatientForm)" [formGroup]="newPatient" novalidate
  #associatePatientForm="ngForm">
  <div class="modal-header row">
    <h4 class="modal-title col-md-5" id="exampleModalLabel">
      {{ title }}
    </h4>
    <button type="button" id="close_popup" class="close" aria-label="Close" (click)="closeAssociateModal()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <section>
      <div class="alert alert-info dupilcate-search-info">
        {{ 'LABELS.ALERT_INFO' | translate }}

      </div>
      <br>
      <br>
      <div class="form-group row" [hidden]="this.userData.mySites.length <= 1">
        <label class="col-md-2 control-label"> {{ 'LABELS.SITE' | translate }}:
          <span class="red-color" *ngIf="isSiteMandatory">*</span>
        </label>
        <span class="col-md-4">
          <app-select-sites [events]="eventsSubject.asObservable()" [hideApplyFilter]=true
            [selectedSiteIds]=selectedSites [singleSelection]=true
            (siteIds)="getAssocSiteIds($event,'Associate Patient')" (hideDropdown)="hideDropdown($event)"
            [siteSelection]="true">
          </app-select-sites>
        </span>
      </div>
      <div class="alert alert-danger" *ngIf="siteIdEmpty">
        {{ 'LABELS.EMPTY_SITE' | translate }}
      </div>
      <div class="form-group row">
        <label class="col-md-2 control-label">{{ 'LABELS.SEARCH_PATIENT' | translate }}: <i
            chToolTip="searchPatientDuplicate" data-animation="false" class="pl-3"></i></label>
        <div class="col-md-4">
          <input type="text" class="form-control" formControlName="searchInput" id="searchTxt"
            (keyup.enter)="searchAssociatePatientWithTems()" (keyup)="disableSearchButton()" />
        </div>
        <div class="col-md-1">
          <button type="button" class="btn btn-sm btn-search btn-info" id="button-search"
            (click)="searchAssociatePatientWithTems()" [disabled]="disableSearch">
            {{ 'BUTTONS.SEARCH' | translate }}
          </button>
        </div>
        <div class="col-md-1">
          <button type="button" class="btn btn-sm btn-search btn-info" [disabled]="disableClear" id="reset_search"
            (click)="searchClear()">
            {{ 'BUTTONS.CLEAR' | translate }}
          </button>
        </div>
        <label class="pt-8 control-label">OR</label>
        <div class="col-md-3">
          <button type="button" class="btn btn-sm btn-search btn-info" id="button-add-patient-details"
            (click)="addPatient()">
            {{ 'LABELS.ADD_PATIENT' | translate }}
          </button>
        </div>
      </div>
      <div class="row" *ngIf="searchRequired">
        <div class="col-lg-12">
          <div class="mb-5">
            <h6>
              <div *ngIf="hasData" class="alert alert-info dupilcate-search-info">
                {{ 'LABELS.NEXT_STEP_ALERT' | translate }}
              </div>
            </h6>
            <div class="wait-loading" *ngIf="patientDataLoadingMsg">
              <img src="./assets/img/loader/loading.gif" />
            </div>
            <div class="data-table">
              <table class="table table-hover table-responsive" id="patient-list" width="100%"></table>
            </div>
          </div>

        </div>
        <div class="col-lg-2"></div>
        <div class="col-lg-8">
          <div class="alert alert-info" *ngIf="noRecordMessage">
            {{patientNotExistMsg}}
          </div>
        </div>

      </div>
      <div class="invoice-block" [hidden]="!showPatientForm">
        <div class="form-group" *ngIf="isEmail">
          <label class="form-label">{{ 'LABELS.PATIENT_EMAIL' | translate }}<span class="red-color"
              *ngIf="isReqEmail">*</span></label>
          <input type="text" class="form-control" id="email" name="email"
            placeholder="{{ 'LABELS.PATIENT_EMAIL' | translate }}" [formControl]="newPatient.controls['email']"
            [(ngModel)]="associatePatientData.email" />
          <div class="alert alert-danger" *ngIf="
                newPatient.controls.email.errors &&
                newPatient.controls.email.errors.required &&
                (newPatient.controls.email.dirty ||
                  newPatient.controls.email.touched ||
                  isPatientFormSubmitted)
              ">
            {{ 'VALIDATION_MESSAGES.EMPTY_EMAIL' | translate }}
          </div>
          <div class="alert alert-danger" *ngIf="
                newPatient.controls.email.errors &&
                newPatient.controls.email.errors.pattern &&
                (newPatient.controls.email.dirty ||
                  newPatient.controls.email.touched ||
                  isPatientFormSubmitted)
              ">
            {{ 'VALIDATION_MESSAGES.SITE_DETAILS_OFFICE_EMAIL' | translate }}
          </div>
        </div>
        <div class="form-group" *ngIf="isFirstName">
          <label class="form-label">{{ 'LABELS.FIRST_NAME' | translate }}<span class="red-color"
              *ngIf="isReqFirstName">*</span></label>
          <input type="text" class="form-control" id="firstname" name="firstName"
            placeholder="{{ 'PLACEHOLDERS.PATIENT_FIRST_NAME' | translate }}"
            [formControl]="newPatient.controls['firstName']" [(ngModel)]="associatePatientData.firstName" />
          <div class="alert alert-danger" *ngIf="
                !newPatient.controls.firstName.valid &&
                (newPatient.controls.firstName.dirty ||
                  newPatient.controls.firstName.touched ||
                  isPatientFormSubmitted)
              ">
            {{ 'VALIDATION_MESSAGES.FIRST_NAME_EMPTY' | translate }}
          </div>
        </div>

        <div class="form-group" *ngIf="isLastName">
          <label class="form-label">{{ 'LABELS.LAST_NAME' | translate }}<span class="red-color"
              *ngIf="isReqLastName">*</span></label>
          <input type="text" class="form-control" id="lastname" name="lastName"
            placeholder="{{ 'LABELS.PATIENT_LAST_NAME' | translate }}" [formControl]="newPatient.controls['lastName']"
            [(ngModel)]="associatePatientData.lastName" />
          <div class="alert alert-danger" *ngIf="
                !newPatient.controls.lastName.valid &&
                (newPatient.controls.lastName.dirty ||
                  newPatient.controls.lastName.touched ||
                  isPatientFormSubmitted)
              ">
            {{ 'VALIDATION_MESSAGES.LAST_NAME_EMPTY' | translate }}
          </div>
        </div>
        <div class="form-group" *ngIf="iscellNumber">
          <label class="form-label">{{ 'LABELS.CELL_NUMBER' | translate }}<span class="red-color"
              *ngIf="isReqcellNumber">*</span></label>
          <input type="text" class="form-control" id="us-phone-mask-input" name="cellno"
            placeholder="{{ 'PLACEHOLDERS.PATIENT_CELL_NUMBER' | translate }}"
            [formControl]="newPatient.controls['cellno']" [(ngModel)]="associatePatientData.cellno" />
          <div class="alert alert-danger" *ngIf="
                !newPatient.controls.cellno.valid &&
                (newPatient.controls.cellno.dirty ||
                  newPatient.controls.cellno.touched ||
                  isPatientFormSubmitted)
              ">
            {{ 'VALIDATION_MESSAGES.CELL_NUMBER_EMPTY' | translate }}
          </div>
        </div>

        <div class="form-group row" id="dob-parent" [hidden]="!isDob">
          <div class="col-md-3">
            <label class="label-doba" for="">{{ 'LABELS.DATE_OF_BIRTH' | translate }}<span class="red-color"
                *ngIf="isReqDob">*</span></label>
          </div>
          <div class="col-md-9">
            <input type="text" class="form-control" id="dob-date-picker" required="" />
            <div *ngIf="isDob">
              <input type="hidden" class="form-control" id="dobDay" name="dobDay"
                [formControl]="newPatient.controls['dobDay']" [(ngModel)]="associatePatientData.dobDay" />
              <input type="hidden" class="form-control" id="dobMonth" name="dobMonth"
                [formControl]="newPatient.controls['dobMonth']" [(ngModel)]="associatePatientData.dobMonth" />
              <input type="hidden" class="form-control" id="dobYear" name="dobYear"
                [formControl]="newPatient.controls['dobYear']" [(ngModel)]="associatePatientData.dobYear" />
              <div class="alert alert-danger" *ngIf="
                    (!newPatient.controls.dobYear.valid ||
                      !newPatient.controls.dobMonth.valid ||
                      !newPatient.controls.dobDay.valid) &&
                    isPatientFormSubmitted
                  ">
                {{ 'VALIDATION_MESSAGES.DOB_EMPTY' | translate }}
              </div>
            </div>
          </div>
        </div>
        <div class="form-group" *ngIf="isZipCode">
          <label class="form-label">{{ 'LABELS.ZIP_CODE' | translate }}<span class="red-color"
              *ngIf="isReqZipCode">*</span></label>
          <input type="text" class="form-control" id="zipcode" name="zipcode"
            placeholder="{{ 'LABELS.ZIP_CODE' | translate }}" [formControl]="newPatient.controls['zipcode']"
            [(ngModel)]="associatePatientData.zipcode" />
          <div class="alert alert-danger" *ngIf="
                !newPatient.controls.zipcode.valid &&
                (newPatient.controls.zipcode.dirty ||
                  newPatient.controls.zipcode.touched ||
                  isPatientFormSubmitted)
              ">
            {{ 'VALIDATION_MESSAGES.ZIP_CODE_EMPTY' | translate }}
          </div>
        </div>

        <div class="form-group">
          <label class="form-label">{{ 'LABELS.MRN' | translate }}
            <span class="red-color" *ngIf="structureService.isMRNFieldMandatory"> * </span>
          </label>
          <input type="text" class="form-control" id="MRN" name="MRN" placeholder="{{ 'LABELS.MRN' | translate }}"
            [formControl]="newPatient.controls['mrn']" [(ngModel)]="associatePatientData.mrn" appTrimSpaces />
          <div class="alert alert-danger"
            *ngIf="!newPatient.controls.mrn.valid && (newPatient.controls.mrn.dirty || newPatient.controls.mrn.touched || isPatientFormSubmitted)">
            {{ 'VALIDATION_MESSAGES.MRN_EMPTY' | translate }}
          </div>
        </div>
      </div>
    </section>
  </div>
  <div class="modal-footer" *ngIf="showPatientForm">
    <span class="chatwith-modal-tip">
      <img src="./assets/modules/dummy-assets/common/img/chatwith-tip.png" />
      <span class="modal-footer-text"><span class="red-color">*</span>{{ 'LABELS.REQUIRED_INFO' | translate }}</span>
    </span>
    <button class="reset_button" type="reset" id="resetpform">
      {{ 'BUTTONS.RESET' | translate }}
    </button>
    <input type="submit" id="s_button" class="btn btn-success" value="Save">
    <button type="button" id="c_button" class="btn btn-secondary" (click)="closeAssociateModal()">
      {{ 'BUTTONS.CANCEL' | translate }}
    </button>
  </div>
</form>