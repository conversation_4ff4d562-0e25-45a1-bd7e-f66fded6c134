import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Daterangepicker } from 'ng2-daterangepicker';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { NgbDatepickerModule } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ToolTipDirective } from './tooltip-directive';
import { TrimSpacesDirective } from './trim-spaces.directive';
import { unreadmessageFilterPipe } from '../inbox/inbox-unreadmessage.pipes';
import { signatureRequestUnreadCountPipe, unReadMessagesCountPipe, timeZoneOffsetFormatPipe, dateFormatPipe, secondaryUsers } from './sharedPipe';
import { scheduleIconDirective } from '../shared/schedule-assigned-directive';
import { PhoneNumberValidatorDirective } from './phone-number-validator.directive';
import { RequiredIndicatorDirective } from './required-indicator.directive';
import { SignPadComponent } from '../shared/signPad/sign-pad.component';
import { VerifyEmComponent } from '../shared/verifyEM/verify-em.component';
import { TopBarSearchFilterPipe } from '../../components/top-bar/topbar-chat-search.pipes';
import { SignaturePadModule } from '../../../assets/lib/angular2-signaturepad';
import { VideoTutorialComponent } from '../shared/videoTutorial/video-tutorial.component';
import { SelectSitesComponent } from '../shared/select-sites/select-sites.component';
import { HomeSiteComponent } from './home-site/home-site.component';
import { xssInputValidator } from './xssInputValidator.directive';
import { AdvanceSearchComponent } from '../shared/advance-search/advance-search.component';
import { AdmissionsDropdownComponent } from '../shared/admissions-dropdown/admissions-dropdown.component';
import { AdmissionsTableComponent } from '../shared/admissions-table/admissions-table.component';
import { DataTableComponent } from './data-table/data-table.component';
import { dateshortyearFilterPipe } from '../inbox/inbox-filter.pipes';
import { chatLogDirective } from '../../structure/message/chatlog-directive';
import { MultiDatePicker } from './multi-date-picker/multi-date-picker.component';
import { UserSearchComponent } from './user-search/user-search.component';
import { AccordionComponent } from './accordion/accordion.component';
import { RecipientsComponent } from './recipients/recipients.component';
import { FormHistoryViewComponent } from './form-history-view/form-history-view.component';
import { ResendDocumentFormModalComponent } from './resend-document-form-modal/resend-document-form-modal.component';
import { ChDaterangePickerComponent } from './ch-daterange-picker/ch-daterange-picker.component';
import { SearchAndSelectComponent } from './search-select/search-select.component';
import { TimePickerComponent } from './ch-time-picker/ch-time-picker.component';
import { FilterPipe } from '../profile/filter.pipe';
import { AutolinkerPipe } from './pipes/autolinker/autolinker.pipe';
import { LoaderComponent } from './ch-loader/ch-loader.component';
import { UserListViewComponent } from './ch-user-list-view/ch-user-list-view.component';
import { ChTooltipComponent } from './ch-tooltip/ch-tooltip.component';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { MessagePriorityComponent } from './message-priority/message-priority.component';
import { AssociatePatientModalComponent } from './associate-patient-modal/associate-patient-modal.component';
import { ChModalLoaderComponent } from './ch-modal-loader/ch-modal-loader.component';
import { UserDropdownComponent } from './user-dropdown/user-dropdown.component';
import { PatientDropdownComponent } from './patient-dropdown/patient-dropdown.component';
import { PatientGroupComponent } from './pdg-dropdown/pdg-dropdown.component';
import { UpdateAssocPatientComponent } from './update-assoc-patient/update-assoc-patient.component';
import { PdgMembersComponent } from './pdg-members/pdg-members.component';
import { SelectAdmissionSitesComponent } from './select-admission-sites/select-admission-sites.component';
import { ResetPasswordComponent } from './reset-password/reset-password.component';
import { OutOfOfficeStatusDirective } from './directives/outofoffice-status/outofoffice-status.directive';
import { OutOfOfficeComponent } from './out-of-office/out-of-office.component';
import { StatusMessageComponent } from './status-message/status-message.component';
import { NoAutofillDirective } from './noAutofill.directive';

@NgModule({
  imports: [
    SignaturePadModule,
    CommonModule,
    AngularMultiSelectModule,
    TranslateModule.forChild(),
    FormsModule,
    ReactiveFormsModule,
    NgMultiSelectDropDownModule.forRoot(),
    NgbDatepickerModule,
    Daterangepicker,
    NgbModule.forRoot(),
  ],
  declarations: [
    AdmissionsDropdownComponent,
    AdmissionsTableComponent,
    DataTableComponent,
    ToolTipDirective,
    TrimSpacesDirective,
    VideoTutorialComponent,
    unreadmessageFilterPipe,
    SelectSitesComponent,
    SelectAdmissionSitesComponent,
    signatureRequestUnreadCountPipe,
    unReadMessagesCountPipe,
    timeZoneOffsetFormatPipe,
    dateFormatPipe,
    scheduleIconDirective,
    PhoneNumberValidatorDirective,
    RequiredIndicatorDirective,
    SignPadComponent,
    VerifyEmComponent,
    TopBarSearchFilterPipe,
    secondaryUsers,
    xssInputValidator,
    AdvanceSearchComponent,
    HomeSiteComponent,
    dateshortyearFilterPipe,
    chatLogDirective,
    MultiDatePicker,
    FormHistoryViewComponent,
    UserSearchComponent,
    AccordionComponent,
    RecipientsComponent,
    ResendDocumentFormModalComponent,
    ChDaterangePickerComponent,
    TimePickerComponent,
    SearchAndSelectComponent,
    FilterPipe,
    AutolinkerPipe,
    LoaderComponent,
    UserListViewComponent,
    ChTooltipComponent,
    MessagePriorityComponent,
    AssociatePatientModalComponent,
    ChModalLoaderComponent,
    UserDropdownComponent,
    PatientDropdownComponent,
    PatientGroupComponent,
    UpdateAssocPatientComponent,
    PdgMembersComponent,
    ResetPasswordComponent,
    OutOfOfficeStatusDirective,
    OutOfOfficeComponent,
    StatusMessageComponent,
    NoAutofillDirective
  ],
  exports: [
    AdmissionsDropdownComponent,
    AdmissionsTableComponent,
    DataTableComponent,
    ToolTipDirective,
    TrimSpacesDirective,
    VideoTutorialComponent,
    unreadmessageFilterPipe,
    SelectSitesComponent,
    SelectAdmissionSitesComponent,
    TranslateModule,
    signatureRequestUnreadCountPipe,
    unReadMessagesCountPipe,
    timeZoneOffsetFormatPipe,
    dateFormatPipe,
    scheduleIconDirective,
    PhoneNumberValidatorDirective,
    RequiredIndicatorDirective,
    SignPadComponent,
    VerifyEmComponent,
    TopBarSearchFilterPipe,
    secondaryUsers,
    xssInputValidator,
    AdvanceSearchComponent,
    HomeSiteComponent,
    dateshortyearFilterPipe,
    chatLogDirective,
    MultiDatePicker,
    RecipientsComponent,
    FormHistoryViewComponent,
    UserSearchComponent,
    AccordionComponent,
    ResendDocumentFormModalComponent,
    ChDaterangePickerComponent,
    TimePickerComponent,
    SearchAndSelectComponent,
    FilterPipe,
    AutolinkerPipe,
    LoaderComponent,
    UserListViewComponent,
    ChTooltipComponent,
    MessagePriorityComponent,
    AssociatePatientModalComponent,
    ChModalLoaderComponent,
    UserDropdownComponent,
    PatientDropdownComponent,
    PatientGroupComponent,
    UpdateAssocPatientComponent,
    PdgMembersComponent,
    ResetPasswordComponent,
    OutOfOfficeStatusDirective,
    OutOfOfficeComponent,
    StatusMessageComponent,
    NoAutofillDirective
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class SharedModule {}
