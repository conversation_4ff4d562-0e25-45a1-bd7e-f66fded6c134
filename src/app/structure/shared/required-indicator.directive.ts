import { Directive, ElementRef, OnInit } from '@angular/core';
import { NgControl } from '@angular/forms';

@Directive({
    selector: '[appRequiredIndicator]'
})
export class RequiredIndicatorDirective implements OnInit {
    constructor(private el: ElementRef, private ngControl: NgControl) { }

    ngOnInit() {
        if (this.ngControl && this.ngControl.control) {
            if (this.ngControl.control.validator) {
                const validators = this.ngControl.control.validator({} as any);
                //Find the input is added with required validator or not
                if (validators && validators.required) {
                    const inputId = this.el.nativeElement.id;
                    if (inputId) {
                        // Find the label element with a "for" attribute matching the input's id
                        const labelElement = document.querySelector(`label[for="${inputId}"]`);
                        if (labelElement) {
                            // Create an asterisk element
                            const asteriskElement = document.createElement('span');
                            asteriskElement.textContent = ' *';
                            // Append the asterisk element to the label
                            labelElement.appendChild(asteriskElement);
                        }
                    }
                }
            }
        }
    }
}

