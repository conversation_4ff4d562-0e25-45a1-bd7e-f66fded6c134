<div class="input-group">
  <input
    class="input-picker"
    readonly="true"
    ngbDatepicker
    [dayTemplate]="customDay"
    #d="ngbDatepicker"
    [autoClose]="false"
    [displayMonths]="1"
    firstDayOfWeek="7"
  />
  <input
    class="form-control"
    id="dob"
    name="dob"
    [(ngModel)]="dates"
    readonly="true"
  />
  <span
    class="input-group-addon"
    (click)="d.toggle(); $event.stopPropagation()"
    (document:click)="closeFix($event, d)"
  >
    <span class="fa fa-calendar"></span>
  </span>
</div>

<ng-template #customDay let-date="date" let-focused="focused">
  <span
    class="custom-day"
    (click)="onDateSelection($event, date)"
    [class.focused]="focused"
    [class.range]="
      isFrom(date) || isTo(date) || isInside(date) || isHovered(date)
    "
    [class.faded]="isHovered(date) || isInside(date)"
    [class.selected]="isDateSelected(date)"
    (mouseenter)="hoveredDate = date"
    (mouseleave)="hoveredDate = null"
  >
    {{ date.day }}
  </span>
</ng-template>
