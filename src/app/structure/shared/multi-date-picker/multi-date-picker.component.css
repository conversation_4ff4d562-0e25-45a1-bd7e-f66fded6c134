.custom-day {
    text-align: center;
    padding: 0.185rem 0.25rem;
    display: inline-block;
    height: 2rem;
    width: 2.4rem;
    border-radius: 3px;
}

.custom-day.range,
.custom-day:hover {
    background-color: #ACB7BF;
    color: white;
}

.custom-day.faded {
    background-color: #ACB7BF;
}

.custom-day.selected {
    background-color: #0190FE;
    color: #fff;
}

.input-picker {
    visibility: hidden;
    max-height: 0;
    position: absolute;
    top: 85%;
}

::ng-deep .input-picker + ngb-datepicker {
    width: 260px;
}

::ng-deep .ngb-dp-month {
    width: 100%;
}

::ng-deep .ngb-dp-weekdays,
::ng-deep .ngb-dp-week {
    justify-content: space-evenly;
}

::ng-deep .ngb-dp-weekday {
    font-weight: bold;
    color: black !important;
    font-style: normal;
    font-size: inherit;
}