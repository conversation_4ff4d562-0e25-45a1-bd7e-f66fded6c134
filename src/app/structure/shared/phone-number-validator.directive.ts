import { Directive, HostListener } from '@angular/core';
import { NgControl } from '@angular/forms';
import { removePhoneNumberMasking } from 'app/utils/utils';

@Directive({
  selector: '[appPhoneNumberValidator]',
})
export class PhoneNumberValidatorDirective {
  constructor(public ngControl: NgControl) {}

  //Trigger when the cursor focused to phone number field
  @HostListener('focus', ['$event.target.value'])
  //Trigger when the phone number field value changed
  @HostListener('blur', ['$event.target.value'])

  /**
   * Function to validate the respective controller value
   * @param maskedPhoneNumber 
   */
  validatePhoneNumber(maskedPhoneNumber) {
    const unmaskedPhoneNumber = removePhoneNumberMasking(maskedPhoneNumber);
    this.ngControl.control.setValue(unmaskedPhoneNumber); //Set only numbers to the phone number controller
    //check the phone number length is less than 7 
    if (unmaskedPhoneNumber.length < 7 && unmaskedPhoneNumber.length != 0) {
      this.ngControl.control.setErrors({ incorrect: true }); //set error if the phone number length is less than 7
    } else {
      this.ngControl.control.setErrors(null); //remove error from the phone number
    }
  }
}
