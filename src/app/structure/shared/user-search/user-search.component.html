<div class="form-group row user-search">
  <div class="col-md-9 inline-flex">
    <input
      [disabled]="disabled"
      type="text"
      class="form-control"
      [ngClass]="showList ? 'active' : ''"
      autocomplete="off"
      [(ngModel)]="searchKeyword"
      [ngModelOptions]="{standalone: true}"
      placeholder="{{ 'PLACEHOLDERS.SEARCH_NAME' | translate }}"
      (click)="showUserList()"
      (keyup)="checkSearchFieldCleared()"
      (keydown)="searchOnEnterKeyPress($event)"
      xssInputValidate="{{fieldName | translate}}"
    />
    <ul
      class="recipient-ul"
      [ngStyle]="{ display: showList ? 'block' : 'none' }">
      <ul class="user-list-ul" *ngIf="!userList.length && !flagUserListLoading">
        <li>
          {{ 'VALIDATION_MESSAGES.NO_ITEM_FOUND' | translate }}
        </li>
      </ul>
      <ul class="user-list-ul">
        <li class="user-list-li" *ngIf="flagUserListLoading">
          {{ 'MESSAGES.LOADING_DATA' | translate }}
        </li>
        <li
          class="user-list-li"
          *ngFor="let user of userList; trackBy: trackByUserId"
          (click)="onUserSelectChange(user)">
          {{ user.displayname }}
        </li>
      </ul>
    </ul>
  </div>
  <div class="col-md-3 recipientt-actions patient-search-reset-button">
    <button
      *ngIf="showSearchBtn"
      [disabled]="disableBtnOnFetch || flagUserListLoading"
      id="btn-recipient-search"
      class="recipientt-search-button btn btn-sm btn-primary"
      (click)="searchUser()"
    >
      {{'BUTTONS.SEARCH' | translate}}
    </button>
    <button
      *ngIf="showResetBtn"
      [disabled]="disableBtnOnFetch"
      id="recipientt-close"
      class="recipientt-search-button btn btn-sm btn-default"
      (click)="resetUserList()"
    >
      {{ 'BUTTONS.RESET' | translate }}
    </button>
  </div>
</div>
