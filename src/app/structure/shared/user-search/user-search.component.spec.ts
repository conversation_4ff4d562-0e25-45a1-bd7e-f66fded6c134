import { ComponentFixture, TestBed } from '@angular/core/testing';
import { UserSearchComponent } from './user-search.component';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { VisitScheduleService } from 'app/structure/visit-schedule/visit-scheduler.service';
import { StructureService } from 'app/structure/structure.service';
import { ElementRef, ChangeDetectorRef } from '@angular/core';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { xssInputValidator } from '../xssInputValidator.directive';
import { HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { Apollo, ApolloModule } from 'apollo-angular';
import { provideClients } from 'test-utils';
import { AuthService } from '../auth.service';
import { SharedService } from '../sharedServices';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { HttpService } from 'app/services/http/http.service';
import { StoreService } from '../storeService';

describe('UserSearchComponent', () => {
  let component: UserSearchComponent;
  let fixture: ComponentFixture<UserSearchComponent>;

  let structureServiceSpy: StructureService = jasmine.createSpyObj(
    'StructureService',
    ['userDetails']
  );

  beforeEach(async () => {
    structureServiceSpy.userDetails = JSON.stringify({});
    await TestBed.configureTestingModule({
      declarations: [UserSearchComponent, xssInputValidator],
      providers: [
        HttpService,
        VisitScheduleService,
        { provide: StructureService, useValue: structureServiceSpy },
        ToolTipService,
        TranslateService,
        Apollo,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        StoreService
      ],
      imports: [
        TranslateModule.forRoot(),
        FormsModule,
        HttpModule,
        RouterTestingModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF,
        }),
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(UserSearchComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

});
