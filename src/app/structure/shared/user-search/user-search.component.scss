$ul-li-top-bottom-padding: 0.42rem;
$ul-li-right-padding: 0px;
$ul-left-padding: 0px;
$li-left-padding: 1mm;
.user-search {

    .recipient-ul {
        list-style-type: none;
        padding: 0;
        display: none;
        margin: 0;
        max-height: 200px;
        overflow-y: auto;
        border: 1px solid #0190fe;
        border-radius: 0 0px 4px 4px;
        border-top: none;
        position: absolute !important;
        background: #fff;
        z-index: 99;
        width: 96.6% !important;
        top: -10% !important;
    }

    .user-list-ul {
        margin-top: -1px;
        padding: $ul-li-top-bottom-padding $ul-li-right-padding $ul-li-top-bottom-padding $ul-left-padding;
        text-decoration: none;
        position: relative;
        cursor: pointer;
    }

    .user-list-li {
        margin-top: -1px;
        padding: $ul-li-top-bottom-padding $ul-li-right-padding $ul-li-top-bottom-padding $li-left-padding;
        text-decoration: none;
        position: relative;
        cursor: pointer;
    }

    ul {

        li.user-list-li:hover,
        li.recipient-li:hover {
            background: #d2d9e5 !important;
            color: #222034 !important;
        }

        li.recipient-li.selected,
        li.user-list-li.selected {
            background: #d2d9e5 !important;
            color: #222034 !important;
        }
    }
}