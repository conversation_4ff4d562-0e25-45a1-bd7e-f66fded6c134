/**
 * This interface contains models for the common user search component
 */
export interface UserSearchConfig {
    searchBtnText?: string,
    resetBtnText?: string,
    fieldName: string
}

export interface UserList {
    grp?: string,
    userid?: string,
    username?: string,
    displayname?: string,
    firstname?: string,
    lastname?: string,
    dob?: string,
    company_nursing_agency?: string,
    gender?: string,
    country_code?: string,
    mobile?: string,
    home_phone?: string,
    work_phone?: string,
    address?: string,
    city?: string,
    state?: string,
    country?: string,
    password?: string,
    user_job_type?: string,
    status?: string,
    site_id?: string,
    user_type?: string,
    organization?:string,
    shippingAddress?:string
  }

export interface TrackActivity {
    activityName: string,
    activityType: string,
    activityDescription: string,
  }
