import {
  Component,
  OnInit,
  OnDestroy,
  Input,
  Output,
  EventEmitter,
  HostListener,
  ElementRef,
  ChangeDetectionStrategy,
  SimpleChange,
  ChangeDetectorRef,
} from '@angular/core';
import { TrackActivity, UserList, UserSearchConfig } from './user-search.interface';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { GetUserResource } from 'app/structure/visit-schedule/interface/visit-schedule.interface';
import { CONSTANTS } from 'app/constants/constants';
import { VisitScheduleService } from 'app/structure/visit-schedule/visit-scheduler.service';
import { isBlank } from 'app/utils/utils';
import { StructureService } from 'app/structure/structure.service';
import { Subject } from 'rxjs/Subject';
import { distinctUntilChanged } from 'rxjs/operators';
import { Observable, Subscription } from 'rxjs';
import * as moment from 'moment';

@Component({
  selector: 'app-user-search',
  templateUrl: './user-search.component.html',
  styleUrls: ['./user-search.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
/**
 * This shared component is used to fetch the users list.
 */
export class UserSearchComponent implements OnInit, OnDestroy {
  // Input Decorators
  @Input() disabled = false;
  @Input() configUserSearch: UserSearchConfig;
  @Input() showSearchBtn = true;
  @Input() showResetBtn = true;
  @Input() userType = CONSTANTS.visitUserListTypes.staff;
  @Input() siteIds = '0';
  @Input() fieldName = '';
  @Input() selectedUserId = '';

  // Output Decorators
  @Output() onUserSelected: EventEmitter<string> = new EventEmitter<string>();
  @Output() onUserDataFetched: EventEmitter<UserList[]> = new EventEmitter<
    UserList[]
  >();

  // Class Properties
  searchKeyword = '';
  disableBtnOnFetch = false;
  flagUserListLoading = false;
  showList = false;
  searchKeySubject = new Subject<string>();
  userListSubject = new Subject<UserList[]>();
  private userDataObservable$: Observable<UserList[]>;
  private sessionUserData: any;
  private userSearchSubscriptions: Subscription[] = [];
  private _userList: UserList[] = [];
  private trackActivityData: TrackActivity = {
    activityDescription:'',
    activityName: '',
    activityType: ''
  };

  get userList(): UserList[] {
    return this._userList;
  }

  set userList(userData: UserList[]) {
    this._userList = userData;
    this.userListSubject.next(userData);
  }

  constructor(
    private toolTipService: ToolTipService,
    private visitScheduleService: VisitScheduleService,
    private structureService: StructureService,
    private elementRef: ElementRef,
    private changeDetectorRef: ChangeDetectorRef,
  ) {
    this.sessionUserData = JSON.parse(this.structureService.userDetails);
  }

  // Event listener to handle user clicks to hide the dropdown if the target is not the native element
  @HostListener('document:click', ['$event.target'])
  onClick(target: any) {
    if (!this.elementRef.nativeElement.contains(target)) {
      this.showList = false;
    }
  }

  // Class methods
  ngOnInit(): void {
    this.userSearchSubscriptions.push(
      this.searchKeySubject.pipe(distinctUntilChanged()).subscribe((value) => {
        if (!isBlank(value)) {
          this.fetchUserList();
          this.showUserList();
        }
      })
    );
    this.userDataObservable$ = this.userListSubject.asObservable();
    this.userSearchSubscriptions.push(
      this.userDataObservable$.subscribe((userData) => {
        this.onUserDataFetched.emit(userData);
      })
    );
  }

  /** The method to fetch user lists
   * @param onPrefillSelectedUser optional param, to bind the selected user data at the time of edit
   */
  fetchUserList(onPrefillSelectedUser?: boolean): void {
    const param: GetUserResource = {
      pageCount: 0,
      type: this.userType,
      siteId: this.siteIds ? this.siteIds : '0',
      searchKeyword: this.searchKeyword
    };
    if (onPrefillSelectedUser) {
      param['id'] = this.selectedUserId;
      delete param.searchKeyword;
    }
    this.flagUserListLoading = true;
    this.visitScheduleService.getUserResourceList(param).subscribe(
      (response) => {
        if (!isBlank(response)) {
          const virtual =
            this.userType === CONSTANTS.visitUserListTypes.patients ? this.toolTipService.getTranslateData('LABELS.PATIENT_VIRTUAL') : '';
          this.userList = response
            .filter((data) => {
              if (data.userid.toString() !== this.sessionUserData.userId) {
                return true;
              }
            })
            .map((item) => {
              if (this.userType === CONSTANTS.visitUserListTypes.patients) {
                const caregiver = !isBlank(item.caregiver_userid) ? `(${item.caregiver_displayname})` : '';
                const dob = !isBlank(item.dob) ? moment(item.dob).format(CONSTANTS.patientDobFormat) : '';
                const mrn = !isBlank(item.IdentityValue) ? ` [MRN:${item.IdentityValue}] ` : '';
                item.displayname = item.displayname + caregiver + dob + mrn + virtual;
              } else if (+item.grp === CONSTANTS.userGroupIds.partner) {
                const userTypeAndOrg = !isBlank(item.organization) ? ` (${item.organization})` : '';

                item.displayname = `${item.displayname} (Partner)${userTypeAndOrg}`;
              }

              return item;
            });
        }
        this.flagUserListLoading = false;
        // Pre-populating the selected user data
        if (onPrefillSelectedUser && !isBlank(this.userList) && this.userList.length) {
          const userPrefillData = this.userList.filter((user) => user.userid === this.selectedUserId);
          this.searchKeyword = !isBlank(userPrefillData) && !isBlank(userPrefillData[0].displayname) ? userPrefillData[0].displayname : '';
        }
        this.changeDetectorRef.detectChanges();
      }, (error) => {
        this.trackActivityData = {
          activityDescription: `An error occurred while fetching user lists in the user-search-component. Error details:${error}`,
          activityName: 'Error',
          activityType: 'Error handling'
        };
      });
  }

  // Do the search. Emits the next value
  searchUser(): void {
    this.searchKeySubject.next(this.searchKeyword.trim());
  }

  // Resets the properties to it's initial state.
  resetUserList(): void {
    this.onUserSelected.emit('');
    this.userList = [];
    this.showList = false;
    this.searchKeyword = '';
    this.searchKeySubject.next('');
  }

  // To show the list/dropdown
  showUserList(): void {
    this.showList = true;
  }

  // Check the search field empty, to the reset in that case. Its invoke on '(keyUp)'
  checkSearchFieldCleared(): void {
    if (isBlank(this.searchKeyword)) {
      this.resetUserList();
    }
  }

  // Do the search on EnterKey press
  searchOnEnterKeyPress(event): void {
    if (event.keyCode === CONSTANTS.enterKeyCode) {
      this.searchKeySubject.next(this.searchKeyword.trim());
    }
  }

  /**
   * To optimize the ngFor directive performance
   * @param index a number
   * @users an object which contains the data
   */
  trackByUserId(index: number, users) {
    return users.userId;
  }

  // On user selection changes
  onUserSelectChange(user): void {
    this.searchKeyword = user.displayname;
    this.showList = false;
    this.onUserSelected.emit(user);
  }

  /**
   * To prefill the search area with selected user details.
   * At the time of edit. The search field will be prefilled with the user,
   * received from the parent component.
   */
  prefillSelectedUser(): void {
    if (!isBlank(this.selectedUserId)) {
      this.fetchUserList(true);
    }
  }

  /**
   * To handle the selectedUserId input value change and other onChange functionalities
   * @param changes an object with key which is type of SimpleChange
   */
  ngOnChanges(changes: SimpleChange) {
    if (changes['selectedUserId'] && !isBlank(changes['selectedUserId'].currentValue)) {
      this.prefillSelectedUser();
    } else if (changes['siteIds'] && !changes['siteIds'].firstChange) {
      this.resetUserList();
    }
  }

  //On control leaves the component
  ngOnDestroy(): void {
    //Unsubscribe all the subscriptions
    this.userSearchSubscriptions.forEach((subscribed) => {
      if (subscribed) subscribed.unsubscribe();
    });
  }
}
