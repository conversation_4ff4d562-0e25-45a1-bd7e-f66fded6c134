import { Component, Input } from '@angular/core';

// A common loader component.
// If showInPageCenter is true, it will show the loader in the center of the page. Keep the <ch-loader><ch-loader>
// at the bottom of the HTML page. To use this as a loader to show content loading/ in progress, don't pass showInPageCenter as true;
// E.g In Education center > Create Education Materials > Patients, tags, and staffs tabs
@Component({
  selector: 'ch-loader',
  template: `<div class="ch-loader" *ngIf="showLoader && !showLoaderMessage" [ngClass]="{ 'page-center': showInPageCenter, 'page-top-center': showInTopPageCenter }">
    <div class="loader-spinner">
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
    </div>
  </div>
  <div class="ch-loader-overlay" *ngIf="showLoaderMessage" [ngClass]="{ 'page-center': showInPageCenter, 'page-top-center': showInTopPageCenter }">
    <img  style="width: 75px;" src="./assets/modules/dummy-assets/common/img/spinner.svg" alt="loading">
    <div *ngIf="showLoaderMessage" class="ch-loader-message">{{ showLoaderMessage }}</div>
  </div>`,
  styleUrls: ['./ch-loader.scss']
})
/**
 * showLoader to show/hide the loader. Type Boolean
 * showInPageCenter to show the loader in the center of the page. Type Boolean
 */
export class LoaderComponent {
  @Input() showLoader = false;
  @Input() showInPageCenter = false;
  @Input() showInTopPageCenter = false;
  @Input() showLoaderMessage: string = '';
}
