
<div class="row" class="mb-3 search-section" [hidden]="showAdvanceSearchArea == true">
  <div *ngIf="showLoader" class="loading-container">
    <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
    <div class="loading-text">{{'MESSAGES.LOADING_DATA' | translate}}</div>
  </div>
  <div class="col-md-12 advanceSearchForm" *ngIf="!showLoader">
    <form [formGroup]="advanceSearchForm">
      <div class="row">
        <div *ngFor="let dynamicControl of dynamicControls; let i=index;" class="form-group {{dynamicControl.key}}-drop" [ngClass]="dynamicControl?.class  ? dynamicControl.class : 'col-md-3'">
          <ng-container *ngIf="dynamicControl.show">
	   <ng-container [ngSwitch]="dynamicControl.controlType">
            <label [attr.for]="dynamicControl.key">{{dynamicControl.label}}</label>
            <input *ngSwitchCase="'textbox'" [placeholder]="dynamicControl.label" [formControlName]="dynamicControl.key"
              [id]="dynamicControl.key" (keyup)="disableSearchBtn()" class="form-control adv-form-control" [type]="dynamicControl.type">
            <ch-daterange-picker *ngSwitchCase="'date-range-picker'" [id]="dynamicControl.key"
              (selectDateRange)="onDateRangeChange($event, dynamicControl.key)" [clearValue]="clearDate" [defaultDateRangeValue]="defaultDateRangeValue" [dateRangeFilterOptions]="dateRangeFilterOptions"></ch-daterange-picker>
            <ng-multiselect-dropdown *ngSwitchCase="'dropdown'" [placeholder]="'Select Options'"
              [data]="dynamicControl.options" (click)="focusInputField(dynamicControl.key)" (onSelect)="disableSearchBtn('multiselect','single',dynamicControl.key)" (onDeSelect)="disableSearchBtn('multiselect','single', dynamicControl.key)"
              (onSelectAll)="disableSearchBtn('multiselect','multi',dynamicControl.key)" (onDeSelectAll)="disableSearchBtn('multiselect','multi-del',dynamicControl.key)" [settings]="dropdownSettings" [formControl]="advanceSearchForm.controls[dynamicControl['key']]">
            </ng-multiselect-dropdown>
            <ng-multiselect-dropdown (click)="focusInputField(dynamicControl.key)" *ngSwitchCase="'single-select-dropdown'" [placeholder]="'Select Option'"
              [data]="dynamicControl.options" (onSelect)="disableSearchBtn('singleselect','single',dynamicControl.key)" (onDeSelect)="disableSearchBtn('singleselect','single', dynamicControl.key)"
              [settings]="singleDropdownSettings" [formControl]="advanceSearchForm.controls[dynamicControl['key']]">
            </ng-multiselect-dropdown>
            <angular2-multiselect (click)="focusInputField(dynamicControl.key)" (onSelect)="disableSearchBtn('singleselect','single',dynamicControl.key)" (onDeSelectAll)="clearDropdownValue(dynamicControl.key)" (onDeSelect)="disableSearchBtn('singleselect','single',dynamicControl.key)" *ngSwitchCase="'dropdown-search'" [formControlName]="dynamicControl.key" name="dynamicControl.key" [data]="dynamicControl.options" [settings]="searchDropdownSettings">                                                                                          
              <c-search>
                  <ng-template>
                      <input type="text"
                      placeholder="{{'PLACEHOLDERS.SEARCH' | translate}}" name="clinicianSearch" 
                      class="custom-input-area searchText{{i}}" #searchInput id="searchText{{i}}" (keyup.enter)= "onSearchClick(searchInput.value,dynamicControl.key,i)"
                      />                                       
                      <i class="btn-icon-search icmn-search" (click)="onSearchClick(searchInput.value,dynamicControl.key,i)"></i>                     
                      <i class="btn-icon-reset icmn-loop2" (click)="resetDropdownSearch(dynamicControl.key,i)"></i>
                      <label *ngIf="loadMsg" class="loading-label">{{msgContent}}</label>
                  </ng-template>
              </c-search>
          </angular2-multiselect>
          <angular2-multiselect (click)="focusInputField(dynamicControl.key)" (onSelect)="disableSearchBtn('multiselect','single',dynamicControl.key)" (onSelectAll)="disableSearchBtn('multiselect','multi',dynamicControl.key)"
            (onDeSelect)="disableSearchBtn('multiselect','single',dynamicControl.key)" (onDeSelectAll)="disableSearchBtn('multiselect','multi',dynamicControl.key)"  *ngSwitchCase="'dropdown-tag-search'" [formControlName]="dynamicControl.key" [(ngModel)]="modelData[dynamicControl.key]" name="dynamicControl.key" [data]="dynamicControl.options" [settings]="dynamicControl.settings ? dynamicControl.settings : searchDropdownSettings">
              <c-badge>
                  <ng-template let-tag="item">
                      <span class="tag-label" [style.background-color]="tag.bgColor" [style.color]="tag.fontColor" [style.border-right-color]="tag.bgColor"><span>{{tag.itemName}}</span></span>
                  </ng-template>
              </c-badge>
              <c-item>
                  <ng-template let-tag="item">
                      <span class="tag-label" [style.background-color]="tag.bgColor" [style.color]="tag.fontColor" [style.border-right-color]="tag.bgColor">{{tag.itemName}}</span>
                  </ng-template>
              </c-item>
          </angular2-multiselect>
        <div class="row mt-1" *ngIf="dynamicControl.extraOption == true"  id="div-staff-partner-select">                                 
          <div class="col-sm-4">
              <input type="radio" [checked]="selectedType === 'staffs'" name="availabilityCheck" value="staffs" 
              (change)="onTypeChange($event,dynamicControl.key, i)"/><label class="ml-1">{{'LABELS.STAFF' | translate}}</label>
          </div>
          <div class="col-sm-4">
              <input type="radio" [checked]="selectedType === 'partners'" name="availabilityCheck" value="partners" 
              (change)="onTypeChange($event,dynamicControl.key, i)"/><label class="ml-1"> {{'LABELS.PARTNER' | translate}}</label>
          </div>
          <div class="col-sm-3">
              <input type="radio" [checked]="selectedType === 'staff-partner'" name="availabilityCheck" value="staff-partner" 
              (change)="onTypeChange($event,dynamicControl.key, i)"/><label class="ml-1"> {{'LABELS.ALL' | translate}}</label>
          </div>
      </div>      
          </ng-container>
          </ng-container>
        </div>
        <div class="form-group col-md-12 action-buttons" style="padding-top: 2.17rem;">
          <button class="search-btn pull-right btn btn-sm btn-primary" data-toggle="tooltip" data-placement="top" [disabled]="!enableSearchReset && !keepSubmitBtnEnabled" (click)="applySearch()" [ngbTooltip]="tooltipSubmitTemplate">{{'BUTTONS.SUBMIT' | translate}}</button>
          <button class="form-reset-btn pull-right btn btn-sm btn-secondary" data-toggle="tooltip" data-placement="top"  (click)="resetSearch()" [ngbTooltip]="tooltipResetTemplate">{{'BUTTONS.RESET' | translate}}</button>
          <button *ngIf="showClose" class="close-btn pull-right btn btn-sm btn-warning" (click)="closeSearchForm()" data-toggle="tooltip" data-placement="top" [ngbTooltip]="tooltipCloseTemplate">{{'BUTTONS.CLOSE' | translate}}</button>
          <div class="pull-right mr-2" *ngIf="advancedSearchView">
            <input type="checkbox" id="saveAsDefault" formControlName="isDefault" />
            <label for="saveAsDefault" class="ml-1"
              >{{ 'LABELS.SAVE_AS_DEFAULT' | translate }} <i class="icmn-info" chToolTip="saveDefaultAdvFilter"></i
            ></label>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
<ng-template #tooltipSubmitTemplate>
  <app-ch-tooltip [content]="'BUTTONS.SUBMIT' | translate"></app-ch-tooltip>
</ng-template>
<ng-template #tooltipResetTemplate>
  <app-ch-tooltip [content]="'BUTTONS.RESET' | translate"></app-ch-tooltip>
</ng-template>
<ng-template #tooltipCloseTemplate>
  <app-ch-tooltip [content]="'BUTTONS.CLOSE' | translate"></app-ch-tooltip>
</ng-template>
