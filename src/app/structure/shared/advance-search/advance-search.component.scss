.advanceSearchForm{
padding-top: 20px;
}
.advanceSearchForm input, .advanceSearchForm select, .advanceSearchForm .input-group{
    border: 1px solid #cccccc;
}
.advanceSearchForm .action-buttons button.close-btn,
.advanceSearchForm .action-buttons button.form-reset-btn{
    margin-right: 10px;
}

.advanceSearchForm .action-buttons button.form-reset-btn{
    background-color: #acb7bf;
    border: 1px solid #acb7bf;
}
.search-section {
    border: 1px solid #e2e2e2;
    padding-bottom: .9rem;
    margin-top: 1rem;
}

.adv-form-control {
    padding: 0.55rem .14rem !important;
    border: 1px solid #adadad !important;
  }
  
  .btn-icon-search{
    background-color: #1f9dfe;
    border-color: #1f9dfe;
    border: 1px;
    border-radius: 0.2rem;
    font-size: 10px;
    padding: 5px;
    cursor: pointer;
  }

  .btn-icon-reset{
    background-color: #acb7bf;
    border-color: #acb7bf;
    border: 1px;
    border-radius: 0.2rem;
    font-size: 10px;
    padding: 5px;
    cursor: pointer;
  }

  .custom-input-area{
    border: none !important;
    width: 70%;
    height: 100%;
    outline: none;
  }

  .loading-label {
    font-size: 14px;
    padding-top: 0.5rem;
  }

  :host ::ng-deep .select-tags {
    .list-area {
        width: max-content !important;
    }

    .lazyContainer {
        position: sticky !important;
    }

    .c-token{
        background: transparent !important;
        padding-right: 0px !important;
        &:first-child {
            padding-left: 0px !important;
        }
        .tag-label {
            width: 101%;
        }
        .c-remove {
            right: -2px !important;
            top: 14px !important;
        }
    }

    
    .c-btn {
        border-color: #adadad !important;
        height: 35px !important;
        span {
            font-size: 1rem;
        }
        .countplaceholder {
            right: 30px;
        }
        .clear-all {
            display: none;
        }
    }

    .tag-label {
        display: inline-block;
        width: auto;
        height: 20px;
        background-color: rgb(136, 155, 160);
        -webkit-border-radius: 3px 4px 4px 3px;
        -moz-border-radius: 3px 4px 4px 3px;
        border-radius: 3px 4px 4px 3px;
        border-left: 1px solid rgb(136, 155, 160);
        border-right-color: rgb(136, 155, 160);
        margin-left: 10px;
        position: relative;
        color: white;
        font-weight: 300;
        font-size: 12px;
        line-height: 20px;
        padding: 0 10px 0 10px;
        span {
            max-width: 70px;
            display: inline-block;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
    
    /* Makes the triangle */
    .tag-label:before {
        right: 100%;
        top: 0;
        border: solid transparent;
        content: " ";
        height: 0;
        width: 0;
        position: absolute;
        pointer-events: none;
        border-right-color: inherit;
        border-width: 10px;
        margin-top: 0;
    }
}