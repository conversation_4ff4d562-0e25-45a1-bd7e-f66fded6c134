import { NO_ERRORS_SCHEMA } from '@angular/core';
import * as moment from 'moment';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Apollo, ApolloModule } from 'apollo-angular';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { VisitScheduleService } from 'app/structure/visit-schedule/visit-scheduler.service';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { provideClients } from 'test-utils';
import { HttpService } from 'app/services/http/http.service';
import { PermissionService } from 'app/services/permission/permission.service';
import { DaterangepickerConfig } from 'ng2-daterangepicker';
import { CONSTANTS } from 'app/constants/constants';
import { AuthService } from '../auth.service';
import { SharedService } from '../sharedServices';
import { AdvanceSearchComponent } from './advance-search.component';
import { StoreService } from '../storeService';
import { DateRangeSelected } from '../ch-daterange-picker/date-range-selected.interface';

describe('AdvanceSearchComponent', () => {
  let component: AdvanceSearchComponent;
  let fixture: ComponentFixture<AdvanceSearchComponent>;
  const userDetailsString = JSON.stringify('{"config": {"enable_multisite": "1"}}');
  let structservice: StructureService;
  beforeEach(async(() => {
    structservice = jasmine.createSpyObj('StructureService', ['userDetails', 'trackActivity']);
    structservice.userDetails = userDetailsString;
    TestBed.configureTestingModule({
      providers: [
        HttpService,
        FormBuilder,
        { provide: StructureService, useValue: structservice },
        Apollo,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        TranslateService,
        VisitScheduleService,
        PermissionService,
        StoreService,
        DaterangepickerConfig
      ],
      declarations: [AdvanceSearchComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        RouterTestingModule,
        HttpModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF
        }),
        TranslateModule.forRoot()
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AdvanceSearchComponent);
    component = fixture.componentInstance;
    component.userData = structservice.userDetails;
    component.dynamicControls = [
      {
        key: 'testControl',
        value: 'testValue',
        required: true,
        controlType: 'textbox',
        label: 'Test Control',
        order: 1,
        settings: {},
        type: 'string',
        class: '',
        extraOption: true,
        show: true,
        options: [],
        defaultOptions: []
      }
    ];
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the form with dynamic controls', () => {
    component.ngOnInit();
    expect(component.advanceSearchForm.contains('testControl')).toBeTruthy();
    expect(component.advanceSearchForm.get('testControl').value).toBe('testValue');
  });

  it('should reset the form when resetForm is true', () => {
    component.resetForm = true;
    component.ngOnChanges({});
    expect(component.advanceSearchForm.pristine).toBeTruthy();
  });

  it('should emit advanceSearch event with modified search input on applySearch', () => {
    spyOn(component.advanceSearch, 'emit');
    component.ngOnInit();
    component.advanceSearchForm.get('testControl').setValue('newValue');
    component.applySearch();
    expect(component.advanceSearch.emit).toHaveBeenCalledWith({ testControl: 'newValue' });
  });

  xit('should reset the form and emit advanceSearch event with empty object on resetSearch', () => {
    spyOn(component.advanceSearch, 'emit');
    component.ngOnInit();
    component.resetSearch();
    expect(component.advanceSearchForm.pristine).toBeTruthy();
    expect(component.advanceSearch.emit).toHaveBeenCalledWith({});
  });

  it('should emit closeAdvanceSection event on closeSearchForm', () => {
    spyOn(component.closeAdvanceSection, 'emit');
    component.closeSearchForm();
    expect(component.closeAdvanceSection.emit).toHaveBeenCalledWith(true);
  });

  it('should update enableSearchReset based on form values', () => {
    component.ngOnInit();
    component.advanceSearchForm.get('testControl').setValue('newValue');
    component.disableSearchBtn();
    expect(component.enableSearchReset).toBeTruthy();
  });

  it('should update form control value on onDateChange', () => {
    component.ngOnInit();
    const event = { start: moment() };
    component.onDateChange(event, 'testControl');
    expect(component.advanceSearchForm.get('testControl').value).toBe(moment(event.start).format(CONSTANTS.dateRangePicker.dateFormat));
  });

  it('should update form control value on onDateRangeChange', () => {
    component.ngOnInit();
    const event: DateRangeSelected = { startDate: moment().toString(), endDate: moment().add(1, 'day').toString() };
    component.onDateRangeChange(event, 'testControl');
    expect(component.advanceSearchForm.get('testControl').value).toBe(`${event.startDate} - ${event.endDate}`);
  });

  it('should call getSearchList on onSearchClick', () => {
    spyOn(component, 'getSearchList');
    component.ngOnInit();
    component.onSearchClick('searchText', 'type', 0);
    expect(component.getSearchList).toHaveBeenCalledWith('searchText', 'type', 0);
  });

  it('should clear form control value on clearDropdownValue', () => {
    //component.dynamicControls = [{ key: 'testControl', value: 'testValue', required: true, controlType: 'textbox' }];
    component.ngOnInit();
    component.clearDropdownValue('testControl');
    expect(component.advanceSearchForm.get('testControl').value).toBe('');
  });

  xit('should reset dropdown search on resetDropdownSearch', () => {
    spyOn(component, 'getSearchList');
    //component.dynamicControls = [{ key: 'testControl', value: 'testValue', required: true, controlType: 'textbox' }];
    component.ngOnInit();
    component.resetDropdownSearch('type', 0);
    expect(component.getSearchList).toHaveBeenCalledWith('', 'type', 0);
  });

  xit('should update selectedType and call getSearchList on onTypeChange', () => {
    spyOn(component, 'getSearchList');
    //component.dynamicControls = [{ key: 'testControl', value: 'testValue', required: true, controlType: 'textbox' }];
    component.ngOnInit();
    const event = { target: { value: 'newType' } };
    component.onTypeChange(event, 'testControl', 0);
    expect(component.selectedType).toBe('newType');
    expect(component.getSearchList).toHaveBeenCalledWith('', 'newType', 0);
  });
});
