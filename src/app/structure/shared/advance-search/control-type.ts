export class ControlType<T> {
    value: T|undefined;
    key: string;
    label: string;
    required: boolean;
    order: number;
    controlType: string;
    settings: any;
    type: string;
    class: string;
    extraOption: boolean;
    show: boolean;
    options: {key: string, value: string}[];
    defaultOptions: {key: string, value: string}[];
  
    constructor(options: {
        value?: T;
        key?: string;
        label?: string;
        required?: boolean;
        order?: number;
        controlType?: string;
        settings?: any;
        type?: string;
        class?: string;
        extraOption?: boolean;
        show?: boolean;
        options?: {key: string, value: string}[];
        defaultOptions?: {key: string, value: string}[];
          } = {}) {
          this.value = options.value;
          this.key = options.key || '';
          this.label = options.label || '';
          this.required = !!options.required;
          this.order = options.order === undefined ? 1 : options.order;
          this.controlType = options.controlType || '';
          this.settings = options.settings || undefined;
          this.type = options.type || '';
          this.class = options.class || '';
          this.options = options.options || [];
          this.extraOption = options.extraOption || false;
          this.show = options.show === undefined ? true : options.show;
          this.defaultOptions = options.defaultOptions || [];
        }
  }
  