import { Component, OnInit, ElementRef, EventEmitter, Input, ViewChild, Output, SimpleChanges, OnChanges } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { ControlType } from './control-type';
import { StructureService } from '../../structure.service';
import { VisitScheduleService } from '../../visit-schedule/visit-scheduler.service';
import { GetUserResource } from '../../visit-schedule/interface/visit-schedule.interface';
import { isBlank } from 'app/utils/utils';
import { DateRangeSelected } from '../ch-daterange-picker/date-range-selected.interface';
import * as moment from 'moment';
import { DaterangePickerComponent, DaterangepickerConfig } from 'ng2-daterangepicker';
import { TranslateService } from '@ngx-translate/core';
import { CONSTANTS, DateRanges } from 'app/constants/constants';
import { Store, StoreService } from '../storeService';
import { ToolTipService } from '../../tool-tip.service';

declare var $: any;

@Component({
  selector: 'app-advance-search',
  templateUrl: './advance-search.component.html',
  styleUrls: ['./advance-search.component.scss']
})
export class AdvanceSearchComponent implements OnInit, OnChanges {
  advanceSearchForm: FormGroup;
  @Input() dynamicControls: ControlType<string>[] | null = [];
  @Input() advanceSearchFormData: object = null;
  @Input() resetForm;
  @Input() resetAdvanceSearchForm;
  @Input() worklist;
  @Input() showClose = true;
  @Output() advanceSearch = new EventEmitter();
  @Output() closeAdvanceSection = new EventEmitter();
  @Output() updateOptions = new EventEmitter<{ id: string; index: number; searchText: string; siteIds: [any] }>();
  @Output() selectedFilters = new EventEmitter();
  @ViewChild('searchInput') searchInput: ElementRef;
  @Input() showLoader;
  dropdownSettings = {};
  singleDropdownSettings = {};
  searchDropdownSettings = {};
  showAdvanceSearchArea = false;
  userData: any;
  enableSearchReset = false;
  searchText = '';
  @Input() loadMsg = false;
  @Input() msgContent = "No data available";
  selectedType = 'staffs';
  @ViewChild(DaterangePickerComponent)
  private dateRangePicker: DaterangePickerComponent;
  @Input() modelData = {};
  @Input() defaultFilterColumns;
  @Input() keepSubmitBtnEnabled = false;
  clearDate = false;
  @Input() tabType = '';
  defaultDateRangeValue =  DateRanges.LAST_THIRTY_DAYS;
  dateRangeFilterOptions = ["custom"];
  @Input() advancedSearchView: boolean;
  constructor(
    public _structureService: StructureService,
    private _scheduleService: VisitScheduleService,
    public dateRangePickerOptions: DaterangepickerConfig,
    private translate: TranslateService,
    private elementRef: ElementRef,
    private storeService: StoreService,
    private tooltipService: ToolTipService
  ) {
    this.userData = JSON.parse(this._structureService.userDetails);
  }
  focusInputField(key){
    const keySelector = 'div.' + key + '-drop  input[type="text"]';
    const input = this.elementRef.nativeElement.querySelector(keySelector);
    input.focus();
  }
  ngOnInit() {
    this.dropdownSettings = {
      singleSelection: false,
      idField: 'key',
      textField: 'value',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      allowSearchFilter: true,
      itemsShowLimit: 3,
    };
    this.singleDropdownSettings = {
      singleSelection: true,
      idField: 'key',
      textField: 'value',
      selectAllText: 'Select All',
      unSelectAllText: 'UnSelect All',
      allowSearchFilter: true,
      itemsShowLimit: 3,
    };
    this.searchDropdownSettings = {
      singleSelection: true,
      text: 'Select',
      selectAllText: 'Select all',
      unSelectAllText: 'Clear all',
      enableSearchFilter: true,
      enableFilterSelectAll: true,
      badgeShowLimit: 2,
      searchPlaceholderText: 'Search',
      noDataLabel: ''
    };
    this.advanceSearchForm = this.toFormGroup(this.dynamicControls as ControlType<string>[]);
    this.enableSearchReset = false;
    this.assignClinicianType();
  }

  private async getDateRanges() {
    const translatedData = await this.translate.get('LABELS.DATE_RANGES').toPromise().then();
    const dateRanges = {};
    dateRanges[translatedData.ALL] = [moment(CONSTANTS.dateRangePickerOptions.minDate), moment()];
    dateRanges[translatedData.TODAY] = [moment().startOf('day'), moment()];
    dateRanges[translatedData.YESTERDAY] = [moment().startOf('day').subtract(1, 'days'), moment().endOf('day').subtract(1, 'days')];
    dateRanges[translatedData.THIS_WEEK] = [moment().startOf('week'), moment().endOf('week')];
    dateRanges[translatedData.TWO_WEEKS] = [moment().startOf('day'), moment().add(13, 'days')];
    dateRanges[translatedData.THREE_WEEKS] = [moment().startOf('day'), moment().add(20, 'days')];
    dateRanges[translatedData.LAST_SEVEN_DAYS] = [moment().startOf('day').subtract(6, 'days'), moment()];
    dateRanges[translatedData.LAST_THIRTY_DAYS] = [moment().startOf('day').subtract(29, 'days'), moment()];
    dateRanges[translatedData.THIS_MONTH] = [moment().startOf('month'), moment().endOf('month')];
    dateRanges[translatedData.LAST_MONTH] = [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')];
    dateRanges[translatedData.YEAR_TO_DATE] = [moment().startOf('year'), moment()];
    return dateRanges;
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.loadMsg === undefined && changes.msgContent === undefined) {
      if (this.resetForm == true) {
        this.advanceSearchForm.reset();
      } else if (changes.resetAdvanceSearchForm === undefined) {
        this.advanceSearchForm = this.toFormGroup(this.dynamicControls as ControlType<string>[]);
      } else if (this.resetAdvanceSearchForm) {
        Object.keys(this.modelData).forEach((key) => {
          this.modelData[key] = null;
        });
        this.advanceSearchForm.reset();
      }
      this.enableSearchReset = false;
    }
    if (changes.tabType && changes.tabType.currentValue) {
      this.assignClinicianType();
    }
  }

  /**
   * Configure form controls after initializing the view 
   */
  ngAfterViewInit(){
    this.advanceSearchForm = this.toFormGroup(this.dynamicControls as ControlType<string>[]);
  }

  /**
   * Configure form controls for advance search form
   * @param dynamicControls field details
   * @returns FormGroup
   */
  toFormGroup(dynamicControls: ControlType<string>[]) {
    const group: any = {};
    dynamicControls.forEach((control) => {
      const controlValue =
        this.advanceSearchFormData && this.advanceSearchFormData[control.key]
          ? this.advanceSearchFormData[control.key]
          : (!isBlank(control.value)? control.value : '');
      const requiredValidator = control.required ? [Validators.required] : [];
      group[control.key] = new FormControl(controlValue, requiredValidator);
    });
    return new FormGroup(group);
  }

  applySearch() {
    this.clearDate = false;
    if (this.tabType === 'myVisit') {
      this.storeService.storeData(Store.MY_VISIT_CLINICIAN_TYPE, this.selectedType);
    } else if (this.tabType === 'allVisit') {
      this.storeService.storeData(Store.ALL_VISIT_CLINICIAN_TYPE, this.selectedType);
    }
    const modifiedSearchInput = this.getSelectedKeyValues();
    this.advanceSearch.emit(modifiedSearchInput);
    const activityData = {
      activityName: `Apply advance search in ${this.worklist} worklist`,
      activityType: 'Advance Search',
      activityDescription: `${this.userData.displayName} apply advance search in 
      ${this.worklist} worklist with search input ${JSON.stringify(modifiedSearchInput)}`,
    };
    this._structureService.trackActivity(activityData);
  }
  resetSearch() {
    this.clearDate = !this.clearDate; // Toggle clearDate to trigger ngOnChanges in child component
    /**Reset advance search form and result */
    const activityData = {
      activityName: `Reset advance search in ${this.worklist} worklist`,
      activityType: 'Advance Search',
      activityDescription: this.userData.displayName + ` reset advance search in ${this.worklist} worklist`,
    };
    this._structureService.trackActivity(activityData);
    this.advanceSearchForm.reset();
    this.advanceSearchForm.controls.formWorklistColumns.setValue(this.defaultFilterColumns);
    const searchOutput = {};
    this.selectedType = 'staffs';
    this.advanceSearch.emit(searchOutput);
    this.disableSearchBtn();
    if (this.tabType === 'myVisit') {
      this.storeService.removeData(Store.MY_VISIT);
      this.storeService.removeData(Store.MY_VISIT_CLINICIAN_TYPE);
    } else if (this.tabType === 'allVisit') {
      this.storeService.removeData(Store.ALL_VISIT);
      this.storeService.removeData(Store.ALL_VISIT_CLINICIAN_TYPE);
    }

    const visitChairIndex = this.dynamicControls.findIndex((elem) => elem.key === 'visitChair');
    if (visitChairIndex >= 0) this.dynamicControls[visitChairIndex].show = false;
    /**For clear the search field inside dropdown and reset the options */
    let index = 0;
    /* eslint-disable no-param-reassign */
    this.dynamicControls.forEach((elem)=> {
      if (elem.controlType === 'dropdown-search') {
        const input = this.elementRef.nativeElement.querySelector(`.searchText${index}`);

        if (input) {
          input.value = '';
        }
        elem.options = elem.defaultOptions;
      } else {
        if (elem.value && Array.isArray(elem.value)) {
          elem.value = [] as any;
        } else {
          elem.value = '';
        }
        elem.options = [...elem.options];
      }
      index++;
    });
    if (!isBlank(this.dateRangePicker)) {
      this.dateRangePicker.datePicker.setStartDate(moment(CONSTANTS.dateRangePickerOptions.minDate));
      this.dateRangePicker.datePicker.setEndDate(moment());
    }
  }
  closeSearchForm() {
    /**For hide advance search section */
    this.closeAdvanceSection.emit(true);
    const activityData = {
      activityName: `Close advance search in ${this.worklist} worklist`,
      activityType: 'Advance Search',
      activityDescription: this.userData.displayName + ` close advance search in ${this.worklist} worklist`,
    };
    this._structureService.trackActivity(activityData);
  }
  disableSearchBtn(control = '', valueType = '', controlName = '') {
    const searchOutput = this.advanceSearchForm.getRawValue();
    if (control == 'multiselect' && valueType == 'multi-del') {
      searchOutput[controlName] = [];
    }
    this.enableSearchReset = Object.keys(searchOutput).some((x) => {
      const value = searchOutput[x];
      if (control == 'multiselect') {
        return !isBlank(value) && value.length > 0;
      }
      return !isBlank(value);
    });
    if (control == 'multiselect' && valueType == 'multi') {
      this.enableSearchReset = true;
    }
    const site = !isBlank(searchOutput.site) ? searchOutput.site : this.getAllSitesIds();
    if (controlName === 'site') {
      this.advanceSearchForm.controls.visitTypeLocation.setValue('');
      this.advanceSearchForm.controls.visitChair.setValue('');
      const index = this.dynamicControls.findIndex((elem) => elem.key === 'visitTypeLocation');
      if (index >= 0) this.dynamicControls[index].options = [];
      const visitChairIndex = this.dynamicControls.findIndex((elem) => elem.key === 'visitChair');
      if (visitChairIndex >= 0) this.dynamicControls[visitChairIndex].show = false;
      this.updateOptions.emit({
        id: null,
        searchText: '',
        index,
        siteIds: site.map((s) => s.key)
      });
    }
    if (controlName === 'visitTypeLocation') {
      this.advanceSearchForm.controls.visitChair.setValue('');
      const index = this.dynamicControls.findIndex((elem) => elem.key === 'visitChair');
      if (isBlank(searchOutput.visitTypeLocation)) {
        this.dynamicControls[index].show = false;
      } else {
        this.dynamicControls[index].show = true;
        const [visitTypeLocation] = searchOutput.visitTypeLocation;
        this.updateOptions.emit({
          id: visitTypeLocation.id,
          index,
          searchText: '',
          siteIds: site.map((s) => s.key)
        });
      }
    }
  }

  /**
   * Format date on entering manually
   * @param dynamicControl 
   */
  onDateChange(event, key) {
    const control = this.advanceSearchForm.get(key);
    control.setValue(moment(event.start).format(CONSTANTS.dateRangePicker.dateFormat));
    // + ' - ' + moment(event.end).format(CONSTANTS.dateRangePicker.dateFormat));
    this.disableSearchBtn('dateRangePicker','single',key);
  }

  onSearchClick(searchText, type, index) {
    this.dynamicControls[index].options = [];
    if (type !== 'activityLogsType' && type !== 'activityLogsUser' && type !== 'visitTypeLocation' && type !== 'visitChair') {
      this.msgContent = "Loading...";
      this.loadMsg = true;
      if (type === 'patient') {
        type = 'patients';
      }
      if(type === 'staff') {
        type = this.selectedType;
      }
      this.getSearchList(searchText,type,index);
    } else {
      let id = null;
      let site = this.advanceSearchForm.controls.site.value || this.getAllSitesIds();
      if (type === 'visitChair') {
        const [visitTypeLocation] = this.advanceSearchForm.controls.visitTypeLocation.value;
        id = visitTypeLocation.id;
        site = [];
      }
      this.updateOptions.emit({
        id,
        index,
        searchText,
        siteIds: site.map((s) => s.key)
      });
    }
  }
  clearDropdownValue(key){
    this.advanceSearchForm.controls[key].setValue('');
    this.disableSearchBtn('singleselect','single',key);
  }
  resetDropdownSearch(type, index) {
    this.searchText = '';
    this.searchInput.nativeElement.value = '';
    const input = this.elementRef.nativeElement.querySelector('.searchText' + index);
    input.value = '';
    this.onSearchClick(this.searchText, type, index)
    this.dynamicControls[index].options = [];
    this.msgContent = 'Loading...';
    this.loadMsg = true;
    if (type === 'patient' || type === 'staff') {
      type = type === 'patient' ? 'patients' : this.selectedType;
      this.getSearchList('', type, index);
    }
  }
  onTypeChange(e,key,index) {
    this.selectedType = e.target.value;
    this.searchText = '';
    this.searchInput.nativeElement.value = '';
    this.loadMsg = true;
    this.advanceSearchForm.controls[key].setValue('');
    this.disableSearchBtn('singleselect', 'single', key);
    this.getSearchList('',this.selectedType,index);
  }
  onDateRangeChange(event: DateRangeSelected, key) {
    const control = this.advanceSearchForm.get(key);
    control.setValue(`${event.startDate} - ${event.endDate}`);
    this.disableSearchBtn('dateRangePicker', 'single', key);
  }
  getSearchList(searchKeyword,type,index) {
    const userParam : GetUserResource = {
      type: type,
      offset: CONSTANTS.contentOffset,
      limit: CONSTANTS.contentLimit,
      searchKeyword: searchKeyword
    };
    this._scheduleService.getUserResourceList(userParam).subscribe(data => {
      let resourceList = [];
      if (data.length > 0) {
        resourceList = data.map((x) => {
          return { id: x.userid, itemName: type === 'patients' 
          ? this._scheduleService.formatPatientName(x) 
          : x.displayname };
        });
        this.loadMsg = false;
      } else {
        this.msgContent = 'No data available.';
      }
      this.dynamicControls[index].options = resourceList;
    });
  }

  getAllSitesIds() {
    const index = this.dynamicControls.findIndex((elem) => elem.key === 'site');
    return index !== -1 ? this.dynamicControls[index].options || [] : [];
  }
  private validateDate(controlName){
    const dates = this.advanceSearchForm.get(controlName).value;
    if (isBlank(dates)) {
      return true;
    }
    const [start, end] = dates.split(' - ');
    return (
      moment(start, CONSTANTS.dateRangePickerOptions.dateFormat, true).isValid() &&
      moment(end, CONSTANTS.dateRangePickerOptions.dateFormat, true).isValid()
    );
  }

  assignClinicianType() {
    const myVisitStaffType = this.storeService.getStoredData(Store.MY_VISIT_CLINICIAN_TYPE);
    const allVisitStaffType = this.storeService.getStoredData(Store.ALL_VISIT_CLINICIAN_TYPE);

    if (this.tabType === 'myVisit' && myVisitStaffType) {
      this.selectedType = myVisitStaffType;
    } else if (this.tabType === 'allVisit' && allVisitStaffType) {
      this.selectedType = allVisitStaffType;
    } else {
      this.selectedType = 'staffs';
    }
  }
  /** To get the selected values from the dynamic filter options
   * @returns modifiedSearchInput with selected key values
   */
  getSelectedKeyValues(): any {
    const searchOutput = this.advanceSearchForm.getRawValue();
    const searchKeys = Object.keys(searchOutput);
    const modifiedSearchInput: any = {};
    /** Format search input and send to listing component */
    searchKeys.forEach((x) => {
      if (searchOutput[x] !== '' && searchOutput[x] !== null) {
        if (this.worklist === 'schedule') {
          if (typeof searchOutput[x] === 'string') {
            modifiedSearchInput[x] = searchOutput[x].trim();
          } else if (
            !isBlank(searchOutput[x][0]) &&
            Object.keys(searchOutput[x][0]).length > 0 &&
            (Object.keys(searchOutput[x][0]).indexOf('key') !== -1 || Object.keys(searchOutput[x][0]).indexOf('id') !== -1)
          ) {
            const idArray = searchOutput[x].map((x) => x.key || x.id);
            modifiedSearchInput[x] = idArray.join(',');
          } else {
            modifiedSearchInput[x] = searchOutput[x].join(',');
          }
        } else {
          if (x === 'integrationStatus' && searchOutput[x].length > 0) {
            searchOutput[x] = searchOutput[x].map((element) => {
              if (element === 'None') {
                const statusValue = this.worklist === 'document' ? '' : 'Blank';
                return statusValue;
              }
              return element;
            });
            if (this.worklist === 'document') {
              modifiedSearchInput[x] = searchOutput[x];
            } else {
              modifiedSearchInput[x] = searchOutput[x].join(',');
            }
          }
          if (x !== 'integrationStatus') {
            if (typeof searchOutput[x] === 'string') {
              modifiedSearchInput[x] = searchOutput[x].trim();
            } else if (!isBlank(searchOutput[x]) && Array.isArray(searchOutput[x]) && searchOutput[x].length > 0) {
              modifiedSearchInput[x] = searchOutput[x]
                .reduce((acc, item) => {
                  if (typeof item === 'object' && (item.key || item.id)) {
                    acc.push((item.key || item.id).toString());
                  } else if (typeof item === 'string' || typeof item === 'number') {
                    acc.push(item.toString());
                  }
                  return acc;
                }, [])
                .join(',');
            } else {
              modifiedSearchInput[x] = searchOutput[x].toString(',');
            }
          }
        }
      }
    });
    if (searchOutput.staff) {
      modifiedSearchInput.staffClinician = searchOutput.staff || {};
    }
    if (searchOutput.patient) {
      modifiedSearchInput.patientData = searchOutput.patient || {};
    }
    if (searchOutput.visitChair) {
      modifiedSearchInput.visitChairData = searchOutput.visitChair || {};
    }
    if (this.advancedSearchView && Object.keys(modifiedSearchInput).length > 0) {
      modifiedSearchInput.isDefault = searchOutput.isDefault;
    }
    this.selectedFilters.emit(modifiedSearchInput);
    return modifiedSearchInput;
  }
}
