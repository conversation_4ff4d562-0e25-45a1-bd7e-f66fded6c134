import { Injectable } from '@angular/core';
import { isBlank } from 'app/utils/utils';
import { CHStore } from './chStore.model';

export enum Store {
  VS_MY_AVAILABILITY_STATUS_FILTER = 'vsMyAvailabilityStatusFilter',
  VS_MY_AVAILABILITY_SITE_FILTER = 'vsMyAvailabilitySiteFilter',
  VS_OTHER_AVAILABILITY_STATUS_FILTER = 'vsOtherAvailabilityStatusFilter',
  VS_OTHER_AVAILABILITY_SITE_FILTER = 'vsOtherAvailabilitySiteFilter',
  VS_OTHER_AVAILABILITY_CLINICIAN_TYPE = 'vsOtherAvailabilityClinicianType',
  VS_OTHER_AVAILABILITY_CLINICIAN = 'vsOtherAvailabilityClinician',
  SITE_FILTER_DATA = 'siteFilterData',
  SEARCH_STAFF_LIST = 'searchStaffList',
  SEARCH_PARTNER_LIST = 'searchPartnerList',
  SEARCH_PATIENT_LIST = 'searchPatientList',
  SEARCH_USER_TAG_TYPES = 'searchUserTagTypes',
  SEARCH_BANNER_ALERT = 'searchBannerAlert',
  ADVANCE_SEARCH_ALL_SIGNTURE_WORKLIST_SIGNED = 'advanceSearchAllSignatureWorkListSigned',
  ADVANCE_SEARCH_ALL_SIGNTURE_WORKLIST_PENDING = 'advanceSearchAllSignatureWorkListPending',
  ADVANCE_SEARCH_ALL_SIGNTURE_WORKLIST_ARCHIVE = 'advanceSearchAllSignatureWorkListArchived',
  SEARCH_CHATLOG_MSG = 'searchChatLogMsg',
  DATE_RANGE_FILTER_ARCHIVED_MESSAGE = 'dateRangeFilterArchivedMessage',
  SEARCH_MESSAGE_TAG_LIST = 'searchMessageTagList',
  SEARCH_MY_FORM_WORK_LIST_COMPLETED = 'searchMyFormWorkListCompleted',
  SEARCH_MY_FORM_WORK_LIST_PENDING = 'searchMyFormWorkListPending',
  SEARCH_MY_FORM_WORK_LIST_ARCHIVED = 'searchMyFormWorkListArchived',
  SEARCH_MY_FORM_WORK_LIST_DRAFTS = 'searchMyFormWorkListdraft',
  SEARCH_USER_TAGS = 'searchUserTags',
  SEARCH_MY_SIGNATURE_WORK_LIST_SIGNED = 'searchMySignatureWorkListSigned',
  SEARCH_MY_SIGNATURE_WORK_LIST_PENDING = 'searchMySignatureWorkListPending',
  SEARCH_MY_SIGNATURE_WORK_LIST_ARCHIVED = 'searchMySignatureWorkListArchived',
  SEARCH_ALL_SIGNATURE_WORK_LIST_SIGNED = 'searchAllSignatureWorkListSigned',
  SEARCH_ALL_SIGNATURE_WORK_LIST_PENDING = 'searchAllSignatureWorkListPending',
  SEARCH_ALL_SIGNATURE_WORK_LIST_ARCHIVED = 'searchAllSignatureWorkListArchived',
  SEARCH_DOCUMENT_TYPES = 'searchDocumentTypes',
  SEARCH_MANAGE_SITE = 'searchManageSite',
  ADVANCE_SEARCH_MY_SIGNTURE_WORKLIST_SIGNED = 'advanceSearchMySignatureWorkListSigned',
  ADVANCE_SEARCH_MY_SIGNTURE_WORKLIST_PENDING = 'advanceSearchMySignatureWorkListPending',
  ADVANCE_SEARCH_MY_SIGNTURE_WORKLIST_ARCHIVE = 'advanceSearchMySignatureWorkListArchived',
  DATE_RANGE_FILTER_FORMS = 'dateRangeFilterForms',
  DATE_RANGE_FILTER_INBOX = 'dateRangeFilterInbox',
  DATE_RANGE_FILTER_CHAT_LOGS = 'dateRangeFilterChatLogs',
  DATE_RANGE_FILTER_DOCUMENTS = 'dateRangeFilterDocuments',
  DATE_RANGE_FILTER_ADMISSIONS = 'dateRangeFilterAdmissions',
  SEARCH_VIEW_EDU_MATERIALS = 'searchViewEduMaterial',
  SEARCH_EDU_MATERIALS = 'searchEduMaterial',
  SEARCH_STAFF_ROLE = 'searchStaffRole',
  SEARCH_PARTNER_ROLE = 'searchPartnerRole',
  SEARCH_SCHEDULE_FORMS = 'searchScheduleForms',
  SEARCH_TAGGED_MSG = 'searchTaggedMsg',
  SEARCH_ALL_FORM_WORK_LIST_COMPLETED = 'searchAllFormWorkListCompleted',
  SEARCH_ALL_FORM_WORK_LIST_PENDING = 'searchAllFormWorkListPending',
  SEARCH_ALL_FORM_WORK_LIST_ARCHIVED = 'searchAllFormWorkListArchived',
  SEARCH_ALL_FORM_WORK_LIST_DRAFTS = 'searchAllFormWorkListdraft',
  SEARCH_FORM_TYPES = 'searchFormTypes',
  ACTIVE_MESSAGE_ADVANCE_SEARCH_FILTER = 'activeMessageAdvanceSearchFilter',
  SEARCH_MESSAGE_TAG_TYPE_LIST = 'searchMessageTagTypeList',
  USER_INVITE_SEARCH_PATIENT = 'userInviteSearchPatient',
  USER_INVITE_SEARCH_STAFF = 'userInviteSearchStaff',
  USER_INVITE_SEARCH_PARTNER = 'userInviteSearchPartner',
  USER_INVITE_PATIENT_SEARCH_BY = 'userInviteSearchBy',
  USER_INVITE_STAFF_SEARCH_BY = 'userInviteStaffSearchBy',
  USER_INVITE_PARTNER_SEARCH_BY = 'userInvitePartnerSearchBy',
  SELECTED_FORM_TAB = 'selectedFormTab',
  PDG_EDIT = 'pdgEdit',
  PATIENTS_IN_USER_SETTINGS_PAGE = 'patientsInUserSettingsPage',
  ALL_VISIT_CHAIR_LIST = 'allVisitChairList',
  MY_VISIT_CHAIR_LIST = 'myVisitChairList',
  ALL_VISIT = 'allVisit',
  MY_VISIT = 'myVisit',
  MY_VISIT_CLINICIAN_TYPE = 'myVisitClinicianType',
  ALL_VISIT_CLINICIAN_TYPE = 'allVisitClinicianType',
  SEARCH_MANAGE_DOCUMENT_FOLDER = 'searchManageDocumentFolder',
  SEARCH_MANAGE_DOCUMENT_UPLOAD = 'searchManageDocumentUpload',
}

@Injectable()
export class StoreService {
  private storage: Storage = localStorage;
  /**
   * Function to store data in the session storage
   * @param key Name of the session
   * @param value The value to be stored
   */
  storeData(key: string, value: any): void {
    const chStoredData: CHStore = this.retrieveData();
    if (chStoredData) chStoredData[key] = value;
    this.storage.setItem('chStore', JSON.stringify(chStoredData));
  }

  /**
   * Retrieve the whole session data from the session storage
   * @returns CHStore , The session data
   */
  private retrieveData(): CHStore {
    const storedData = this.storage.getItem('chStore');
    return storedData ? JSON.parse(storedData) : {};
  }

  /**
   * Remove data from the session storage
   * @param key session to be removed
   */
  removeData(key?: string): void {
    if (!isBlank(key)) {
      const chStoredData: CHStore = this.retrieveData();
      delete chStoredData[key];
      this.storage.setItem('chStore', JSON.stringify(chStoredData));
    } else {
      this.storage.removeItem('chStore');
    }
  }

  /**
   * Function to get a particular data from the session
   * @param key Session name that need to be retrieved
   * @returns any, The data stored in the session
   */
  getStoredData(key?: string): any {
    const chStoredData: CHStore = this.retrieveData();
    if (!isBlank(key) && chStoredData) {
      return chStoredData[key] || null;
    }
    return chStoredData;
  }
}
