import { Directive, ElementRef, HostListener, Input } from '@angular/core';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { FormGroup } from '@angular/forms';
import { isBlank } from 'app/utils/utils';
@Directive({
  selector: '[xssInputValidate]',
})
export class xssInputValidator {
  private lblFieldName = '';
  @Input() set xssInputValidate(fieldName: string) {
    //This should be the input field's label name/text, & its used to highlight the field in the notify message.
    this.lblFieldName = fieldName;
  }
  //This property is used to determine  whether to accept html tags '<>' or not. 
  //Pass the value as 'html' to accept the <> tags, and checks only for <script> tags.
  @Input() fieldType = '';
  //This input decorative will have the form group control and form control name, to update form control value.
  @Input() xssFormControl :{'formGroup': FormGroup, 'formControl': string};
  constructor(
    private elementRef: ElementRef,
    private _structureService: StructureService,
    private _ToolTipService: ToolTipService
  ) {}


  @HostListener('blur') onBlur() {
    if (this.validateInputValues()) {
      this.elementRef.nativeElement.value = ''; //This will empty the field value
      if(!isBlank( this.xssFormControl)){
        //To update the form control value. An empty value will be set to form control.
        this.xssFormControl.formGroup.get(this.xssFormControl.formControl).setValue('');
        this.xssFormControl.formGroup.controls[this.xssFormControl.formControl].updateValueAndValidity();  
      }
      if(this.fieldType === 'html'){
            this._structureService.notifyMessage({
                    messge: this._ToolTipService.getTranslateDataWithParam('MESSAGES.INVALID_INPUT_SCRIPT_MSG',{fieldName: this.lblFieldName}),
                    delay: 500,
                    type: 'warning'
                });
      } else {
                this._structureService.notifyMessage({
                    messge: this._ToolTipService.getTranslateDataWithParam('MESSAGES.INVALID_INPUT_MSG',{fieldName: this.lblFieldName}),
                    delay: 500,
                    type: 'warning'
                });
      }
    }
  }

  validateInputValues(): boolean {
    //validate the input value only if the element's type is text/textarea
    if (this.elementRef.nativeElement.type === 'text' 
    || this.elementRef.nativeElement.type === 'textarea') {
      //check for any script tags included in the received value
      if (this.fieldType === 'html') {
        return this.elementRef.nativeElement.value.search(
          '<script.*>\n*.*\n*<\/script>'
        ) >= 0
          ? true
          : false;
      } else {
        return this.elementRef.nativeElement.value.search('<(.|\n)*?>') >= 0
          ? true
          : false;
      }
    }
  }
}
