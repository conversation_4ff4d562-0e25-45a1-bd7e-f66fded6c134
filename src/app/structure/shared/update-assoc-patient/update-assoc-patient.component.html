<div
  class="modal fade sd-modal-overlay"
  id="myModal"
  tabindex="-1"
  role="dialog"
  aria-labelledby="exampleModalLabel"
  aria-hidden="true"
  data-backdrop="false"
>
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="exampleModalLabel">{{ 'TITLES.ASSOCIATE_PATIENT' | translate }}</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="closeAssocPatient()">
          <span aria-hidden="true"></span>
        </button>
      </div>
      <div class="modal-body user-selection">
        <div class="forward-model-option-user-list" *ngIf="data.activeSignedDocument && !data.hidePatientName">
          <div class="forward-user-role" style="cursor: default">
            <label class="form-check-label w-100">
              <input
                id="current-associated-patient"
                checked="checked"
                class="form-check-input"
                name="userSelection"
                value="data.activeSignedDocument"
                type="radio"
              />
              <p>
                {{
                  data.activeSignedDocument.caregiver_displayname
                    ? data.nameAssociatedPatient + ' (' + data.activeSignedDocument.caregiver_displayname + ')'
                    : data.nameAssociatedPatient
                }}
                <span
                  *ngIf="
                    data.activeSignedDocument &&
                    data.activeSignedDocument.naTags &&
                    data.activeSignedDocument.naTags !== '' &&
                    data.activeSignedDocument.naTags !== 'null' &&
                    data.activeSignedDocument.naTags !== null
                  "
                  >({{ data.activeSignedDocument.naTagNames }})</span
                >
                <span
                  *ngIf="
                    data.activeSignedDocument &&
                    data.activeSignedDocument.admissionName &&
                    data.activeSignedDocument.admissionName !== '' &&
                    data.activeSignedDocument.admissionName !== null
                  "
                  >({{ 'ADMISSION.LABELS.ADMISSION' | translate }}: {{ data.activeSignedDocument.admissionName }})</span
                >
                <span class="modal-user-role"></span>
              </p>
            </label>
          </div>
        </div>
        <div class="card-block">
          <div class="row" *ngIf="hideSiteSelectionElm && !data.hideSiteFilter">
            <label class="col-md-3 label-position">{{ filterBySiteLabel | translate }}</label>
            <span class="col-md-9">
              <app-select-sites
                [events]="eventsSubject.asObservable()"
                [singleSelection]="true"
                (siteIds)="assocPatientSiteIds($event)"
                (hideDropdown)="hideSiteSelectionElm($event)"
              >
              </app-select-sites>
            </span>
          </div>
          <div class="row">
            <div class="col-md-12">
              <app-patient-dropdown
                [searchType]="'patient'"
                [siteIds]="siteIds"
                (selectedItem)="setPatientAdmissionDetails($event)"
                [requestParams]="userRequestParams"
                [hideSubList] = "true"
              ></app-patient-dropdown>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" id="cancel_btn" class="btn btn-secondary" data-dismiss="modal" (click)="closeAssocPatient()">
          {{ 'BUTTONS.CANCEL' | translate }}
        </button>
        <button type="button" id="update_btn" class="btn btn-primary" (click)="updatedAssociatePatientCMIS()" [disabled]="disableUpdateAssocPatient">
          {{ updatedAssociatePatientButtonText }}
        </button>
      </div>
    </div>
  </div>
</div>
