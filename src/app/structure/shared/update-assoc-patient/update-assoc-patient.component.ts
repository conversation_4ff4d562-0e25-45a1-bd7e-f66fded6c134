import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Subject } from 'rxjs';
import { isBlank } from '../../../utils/utils';
import { StructureService } from '../../structure.service';
import { ToolTipService } from '../../tool-tip.service';
import { StaticDataService } from 'app/structure/static-data.service';

declare const $: any;

@Component({
  selector: 'app-update-assoc-patient',
  templateUrl: './update-assoc-patient.component.html',
  styleUrls: ['./update-assoc-patient.component.scss']
})
export class UpdateAssocPatientComponent implements OnInit, OnChanges {
  @Input() data = {
    documentID: '',
    nameAssociatedPatient: '',
    activeSignedDocument: {} as any,
    hidePatientName: false,
    hideSiteFilter: false
  };
  @Input() showUpdateAssocPatient = false;
  @Output() updateAssocPatient: EventEmitter<any> = new EventEmitter<any>();
  disableUpdateAssocPatient = true;
  updatedAssociatePatientButtonText = this.toolTipService.getTranslateData('TITLES.UPDATE_ASSOCIATE_PATIENT');
  selectedAssociatePatient;
  selectedAssociatePatientAdmission;
  userRequestParams;
  siteIds;
  hideSiteSelection: boolean;
  eventsSubject: Subject<void> = new Subject<void>();
  filterBySiteLabel = 'LABELS.FILTER_BY_SITES';

  constructor(private structureService: StructureService, private toolTipService: ToolTipService, private staticDataService: StaticDataService) {}

  ngOnInit() {
    const userData = this.structureService.getUserdata();
    this.filterBySiteLabel = this.staticDataService.getSiteLabelBasedOnMultiAdmission('FILTER_BY_SITES');
    this.userRequestParams = {
      status: 'notRejected',
      roleId: 3,
      optionShow: 'patient',
      excludeLogginedUser: userData.userId,
      isTenantRoles: 'null',
      isFromChat: 0,
      userGroup: userData.group,
      isSchedule: false
    };
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes.showUpdateAssocPatient && changes.showUpdateAssocPatient.currentValue) {
      $('#myModal').modal('show');
      this.emitEventToSelectSites('displaySelectedSitesOnly');
    }
  }

  hideSiteSelectionElm(hideItem: any){
    this.hideSiteSelection = hideItem.hideItem;
  }

  assocPatientSiteIds(data: any): void {
    if (data.siteId) {
      this.siteIds = data.siteId !== '0' ? data.siteId.toString() : '';
      this.userRequestParams.siteIds = this.siteIds;
    }
  }

  closeAssocPatient() {
    this.emitEventToSelectSites('removeSelectedSite');
    $('#myModal').modal('hide');
    this.updateAssocPatient.emit(null);
  }

  emitEventToSelectSites(status): void {
    this.eventsSubject.next(status);
  }

  updatedAssociatePatientCMIS() {
    if (
      this.selectedAssociatePatient &&
      !isBlank(this.selectedAssociatePatient.userId) &&
      (!this.structureService.isMultiAdmissionsEnabled || this.selectedAssociatePatientAdmission)
    ) {
      this.updateAssociatePatientButtonText(true);
      this.disableUpdateAssocPatient = true;
      const admissionGuid = this.structureService.isMultiAdmissionsEnabled ? this.selectedAssociatePatientAdmission : '';
      this.structureService
        .updateAssociatePatient(
          this.data.documentID,
          this.selectedAssociatePatient.userId,
          admissionGuid,
          this.data.activeSignedDocument.senderTenant
        )
        .subscribe(() => {
          this.updateAssociatePatientButtonText();
          $('#myModal').modal('hide');
          const activityData = {
            activityName: 'Updated Associate Patient For Signed Document:',
            activityType: 'Updated Associate Patient',
            activityDescription: `Updated Associate Patient For Signed Document: ${this.data.documentID}`
          };
          this.structureService.trackActivity(activityData);
          const notify = $.notify(this.toolTipService.getTranslateData('SUCCESS_MESSAGES.PATIENT_ASSOCIATED_TO_DOCUMENT'));
          setTimeout(() => {
            notify.update({
              type: 'success',
              message: `<strong> ${this.toolTipService.getTranslateData('SUCCESS_MESSAGES.PATIENT_ASSOCIATED_TO_DOCUMENT')} </strong>`
            });
          }, 1000);
          this.emitEventToSelectSites('removeSelectedSite');
          this.updateAssocPatient.emit(this.selectedAssociatePatient);
        });
    }
  }

  updateAssociatePatientButtonText = (condition = false) => {
    if (condition) {
      this.updatedAssociatePatientButtonText = this.toolTipService.getTranslateData('MESSAGES.PLEASE_WAIT_UPDATING_MSG');
    } else {
      this.updatedAssociatePatientButtonText = this.toolTipService.getTranslateData('TITLES.UPDATE_ASSOCIATE_PATIENT');
    }
  };

  setPatientAdmissionDetails(event) {
    this.selectedAssociatePatient = event && !isBlank(event.users) ? event.users[0] : '';
    this.selectedAssociatePatientAdmission = event && !isBlank(event.admissions) ? event.admissions[0].id : '';
    this.disableUpdateAssocPatient = !(
      !isBlank(this.selectedAssociatePatient) &&
      (!this.structureService.isMultiAdmissionsEnabled || !isBlank(this.selectedAssociatePatientAdmission))
    );
  }
}
