import { Injectable } from '@angular/core';
import * as OktaSignIn from '@okta/okta-signin-widget'; // Import the module with '*'
import { oktaBaseUrl, oktaClientId, oktaIssuer, oktaRedirectUri, oktaPostLogoutRedirectUri, oktaClassicEngine } from 'environments/environment';
import { HttpService } from 'app/services/http/http.service';
import { NGXLogger } from 'ngx-logger';
import { OktaAuthService } from '@okta/okta-angular';
import { StructureService } from '../../../structure/structure.service';

@Injectable()
export class OktaService {
  stepWidget;
  constructor(
    private structureService: StructureService,
    private httpService: HttpService,
    private logger: NGXLogger,
    private oktaAuth: OktaAuthService
  ) {
    this.stepWidget = new OktaSignIn({
      issuer: oktaIssuer,
      baseUrl: oktaBaseUrl,
      clientId: oktaClientId,
      redirectUri: oktaRedirectUri,
      postLogoutRedirectUri: oktaPostLogoutRedirectUri,
      // state: this.getState(),
      useClassicEngine: oktaClassicEngine,
      scopes: ['openid', 'email', 'profile'],
      authParams: {
        responseType: ['token', 'id_token'],
        pkce: false,
        display: 'page',
        loggingLevel: 'debug'
      },
      // tokenManager: {
      //   autoRenew: true,
      //   expireEarlySeconds: 30
      // },
      idps: [{ type: 'Federated', id: '0oa2bv08nlqsFoGmO0h8' }],
      features: {
        rememberMe: false,
        showPasswordToggleOnSignInPage: true
      },
      i18n: {
        en: {
          // Labels
          'primaryauth.title': ' ',
          'primaryauth.username.placeholder': 'Username',
          'primaryauth.username.tooltip': 'Enter your Username',
          'primaryauth.password.placeholder': 'Password',
          'primaryauth.password.tooltip': 'Enter your Password',
          // Errors
          'error.username.required': 'Please enter your Username',
          'oform.errorbanner.title': 'Invalid Username or Password',
          'error.password.required': 'Please enter a Password',
          'errors.********': 'Sign in failed. Username or Password entered is incorrect'
        }
      }
    });
    this.stepWidget.on('tokenManager:token', (token) => {
      // Handle the obtained token, e.g., store it securely or perform additional actions
      this.logger.log('SRC Token obtained:', token);
    });
    // this.oktaAuth.getTokenManager().on('expired', async (key, expiredToken) => {
    //   console.log('Token with key', key, ' has expired:');
    //   console.log(expiredToken);
    //   try {
    //     const newToken = await this.oktaAuth.getAccessToken();
    //     const newIdToken = await this.oktaAuth.getIdToken();
    //     console.log('Renewed Access Token:', newToken, 'Renewed ID Token:', newIdToken);
    //   } catch (error) {
    //     console.error('Error renewing token:', error);
    //   }
    // });
  }
  checkUserExists(username) { // TODO: ssoid call here
    return username === '<EMAIL>';
  }
  getStepWidget() {
    return this.stepWidget;
  }
  // Add new method to update IDP configuration
  updateIdpConfiguration(idpId: string) {
    if (idpId && this.stepWidget) {
      // Update the widget config with the new IDP ID
      this.stepWidget.authClient.options.idps = [{ type: 'Federated', id: idpId }];
    }
  }
}
