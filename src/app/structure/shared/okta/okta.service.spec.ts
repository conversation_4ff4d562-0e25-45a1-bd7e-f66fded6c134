import { TestBed, inject } from '@angular/core/testing';
import { CommonTestingModule } from 'app/utils/common-testing.module';
import { OKTA_CONFIG, OktaAuthService } from '@okta/okta-angular';
import { OktaService } from './okta.service';

describe('OktaService', () => {
  beforeEach(() => {
    const oktaConfig = {
      issuer: 'https://test.com/oauth2/aus239',
      clientId: '0oa97',
      redirectUri: 'https://testokta.com/login/callback',
      useClassicEngine: true,
      scopes: ['openid', 'profile', 'email'],
      authParams: {
        responseType: ['token', 'id_token'],
        pkce: false,
        display: 'page',
        prompt: 'none',
        loggingLevel: 'debug'
      },
      tokenManager: {
        autoRenew: true,
        expireEarlySeconds: 30
      }
    };
    TestBed.configureTestingModule({
      providers: [OktaService, { provide: OKTA_CONFIG, useValue: oktaConfig }, OktaAuthService],
      imports: [CommonTestingModule]
    });
  });

  it('should be created', inject([OktaService], (service: OktaService) => {
    expect(service).toBeTruthy();
  }));
});
