import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA, SimpleChanges } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { Apollo, ApolloModule } from 'apollo-angular';
import { HttpService } from 'app/services/http/http.service';
import { PermissionService } from 'app/services/permission/permission.service';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { provideClients } from 'test-utils';
import { of } from 'rxjs/observable/of';
import { APIs } from 'app/constants/apis';
import { AdmissionButtonActions, CONSTANTS, DateFormat } from 'app/constants/constants';
import { Observable } from 'rxjs';
import * as moment from 'moment';
import { AuthService } from '../auth.service';
import { SharedService } from '../sharedServices';
import { StoreService } from '../storeService';
import { AdmissionsTableComponent } from './admissions-table.component';

describe('AdmissionsTableComponent', () => {
  let component: AdmissionsTableComponent;
  let fixture: ComponentFixture<AdmissionsTableComponent>;
  let httpService: HttpService;
  let structureService: StructureService;
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [AdmissionsTableComponent],
      providers: [
        HttpService,
        TranslateService,
        StructureService,
        Apollo,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        PermissionService,
        StoreService
      ],
      imports: [
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        FormsModule,
        ReactiveFormsModule,
        RouterTestingModule,
        HttpModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF
        })
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  });
  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });
  beforeEach(() => {
    fixture = TestBed.createComponent(AdmissionsTableComponent);
    httpService = TestBed.get(HttpService);
    structureService = TestBed.get(StructureService);
    spyOn(structureService, 'notifyMessage').and.callThrough();
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
  describe('ngOnChanges', () => {
    it('should call dataChanges method with reload true when selectedPatient changes', () => {
      const changes: SimpleChanges = {
        selectedPatient: {
          currentValue: 'newPatient',
          previousValue: 'oldPatient',
          isFirstChange: () => false,
          firstChange: false
        }
      };
      spyOn(component, 'dataChanges');
      component.ngOnChanges(changes);
      expect(component.dataChanges).toHaveBeenCalledWith({ reload: true });
    });
    it('should call dataChanges method with reload true when admissionEvents force to reload', () => {
      const admissionEvents = { reload: true };
      const changes: SimpleChanges = {
        events: {
          currentValue: { admissionEvents },
          previousValue: null,
          isFirstChange: () => false,
          firstChange: false
        }
      };
      spyOn(component, 'dataChanges');
      component.ngOnChanges(changes);
      expect(component.dataChanges).toHaveBeenCalledWith(admissionEvents);
    });
  });
  describe('getAllAdmissions', () => {
    beforeEach(() => {
      component.requestBody = {
        currentPage: 0,
        patientIds: [],
        rowsPerPage: CONSTANTS.contentLimit,
        searchKey: '',
        sortBy: 'startDate',
        sortDirection: 'DESC'
      };
      component.totalAdmissionsCount = 0;
      component.admissionsList = [];
    });
    it('should fetch all admissions and update the component properties', () => {
      // Arrange
      const selectedPatient = 123;
      component.selectedPatient = selectedPatient;
      const responseBody = {
        page: {
          totalElements: 2
        },
        content: [
          { admissionId: 1, status: 'ACTIVE' },
          { admissionId: 2, status: 'INACTIVE' }
        ]
      };
      spyOn(httpService, 'doPost').and.returnValue(of(responseBody));
      component.getAllAdmissions();
      expect(httpService.doPost).toHaveBeenCalledWith(APIs.getAdmissionsEndPoint, {
        currentPage: 0,
        patientIds: [selectedPatient],
        rowsPerPage: CONSTANTS.contentLimit,
        searchKey: '',
        sortBy: 'startDate',
        sortDirection: 'DESC'
      });
      expect(component.totalAdmissionsCount).toBe(responseBody.page.totalElements);
      expect(component.admissionsList).toEqual(responseBody.content);
    });

    it('should handle error response and reset component properties', () => {
      const selectedPatient = 123;
      component.selectedPatient = selectedPatient;
      spyOn(httpService, 'doPost').and.returnValue(Observable.throw('Error'));
      component.getAllAdmissions();
      expect(httpService.doPost).toHaveBeenCalledWith(APIs.getAdmissionsEndPoint, {
        currentPage: 0,
        patientIds: [selectedPatient],
        rowsPerPage: CONSTANTS.contentLimit,
        searchKey: '',
        sortBy: 'startDate',
        sortDirection: 'DESC'
      });
      expect(component.totalAdmissionsCount).toBe(0);
      expect(component.admissionsList).toEqual([]);
    });
  });
  describe('buttonActions', () => {
    let admission;
    let action;
    beforeEach(() => {
      admission = { admissionId: 1 };
      structureService = TestBed.get(StructureService);
      action = 'EDIT';
    });
    it('should emit the selected item when action is not INACTIVATE, ACTIVATE, or DELETE', () => {
      const emitSpy = spyOn(component.selectedItem, 'emit');
      component.buttonActions(admission, action);
      expect(emitSpy).toHaveBeenCalledWith({ admission, action });
    });
    it('should show alert message popup and call doPut when action is INACTIVATE', () => {
      const showAlertMessagePopupSpy = spyOn(structureService, 'showAlertMessagePopup').and.returnValue(Promise.resolve(true));
      spyOn(httpService, 'doPut').and.returnValue(of({}));
      component.buttonActions(admission, AdmissionButtonActions.INACTIVATE);
      expect(showAlertMessagePopupSpy).toHaveBeenCalled();
    });
    it('should show alert message popup and call doPut when action is ACTIVATE', () => {
      const showAlertMessagePopupSpy = spyOn(structureService, 'showAlertMessagePopup').and.returnValue(Promise.resolve(true));
      spyOn(httpService, 'doPut').and.returnValue(of({}));
      component.buttonActions(admission, AdmissionButtonActions.ACTIVATE);
      expect(showAlertMessagePopupSpy).toHaveBeenCalled();
    });
    it('should show alert message popup and call doPut when action is DELETE', () => {
      const showAlertMessagePopupSpy = spyOn(structureService, 'showAlertMessagePopup').and.returnValue(Promise.resolve(true));
      spyOn(httpService, 'doPut').and.returnValue(of({}));
      component.buttonActions(admission, AdmissionButtonActions.DELETE);
      expect(showAlertMessagePopupSpy).toHaveBeenCalled();
    });
    it('should show alert message popup and show message when api throws error', () => {
      const showAlertMessagePopupSpy = spyOn(structureService, 'showAlertMessagePopup').and.returnValue(Promise.resolve(true));
      spyOn(httpService, 'doPut').and.returnValue(Observable.throw('Error'));
      component.buttonActions(admission, AdmissionButtonActions.DELETE);
      expect(showAlertMessagePopupSpy).toHaveBeenCalled();
    });
  });
  describe('dataChanges', () => {
    beforeEach(() => {
      spyOn(component, 'getAllAdmissions');
    });
    it('should call getAllAdmissions with updated rowsPerPage', () => {
      const event = { rowsPerPage: 10 };
      component.dataChanges(event);
      expect(component.requestBody.rowsPerPage).toBe(event.rowsPerPage);
      expect(component.requestBody.currentPage).toBe(0);
      expect(component.getAllAdmissions).toHaveBeenCalled();
    });
    it('should call getAllAdmissions with updated searchKey', () => {
      const event = { searchText: 'test' };
      component.dataChanges(event);
      expect(component.requestBody.currentPage).toBe(0);
      expect(component.requestBody.searchKey).toBe(`%${event.searchText}%`);
      expect(component.getAllAdmissions).toHaveBeenCalled();
    });
    it('should call getAllAdmissions with updated currentPage', () => {
      const event = { navigatePages: 2 };
      component.dataChanges(event);
      expect(component.requestBody.currentPage).toBe(event.navigatePages - 1);
      expect(component.getAllAdmissions).toHaveBeenCalled();
    });
    it('should call getAllAdmissions with updated startDate and endDate', () => {
      const startDate = new Date('2022-01-01');
      const endDate = new Date('2022-01-31');
      const event = { dateRangeSelected: { startDate, endDate } };
      component.dataChanges(event);
      expect(component.requestBody.startDate).toBe(moment(startDate).format(DateFormat.YYMMDD_FORMAT_HYPHEN));
      expect(component.requestBody.endDate).toBe(moment(endDate).format(DateFormat.YYMMDD_FORMAT_HYPHEN));
      expect(component.requestBody.currentPage).toBe(0);
      expect(component.getAllAdmissions).toHaveBeenCalled();
    });
    it('should call getAllAdmissions with all data if startDate/endDate is empty', () => {
      const startDate = '';
      const endDate = '';
      const event = { dateRangeSelected: { startDate, endDate } };
      component.dataChanges(event);
      expect(component.requestBody.currentPage).toBe(0);
      expect(component.getAllAdmissions).toHaveBeenCalled();
    });
  });
  describe('setNoDataText', () => {
    beforeEach(() => {
      component.admissionsList = [];
    });
    it('should set noDataText to "ADMISSION.MESSAGES.ADMISSION_WAIT" when admissionsList is empty and before API call', () => {
      component.setNoDataText(true);
      expect(component.noDataText).toBe('ADMISSION.MESSAGES.ADMISSION_WAIT');
    });
    it('should set noDataText to "ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE_DATE_FILTER_LAST_NINETY_DAYS" when admissionsList is empty and date filter is applied for last 90 days', () => {
      component.requestBody.startDate = moment().subtract(90, 'days').format(DateFormat.YYMMDD_FORMAT_HYPHEN);
      component.requestBody.endDate = moment().format(DateFormat.YYMMDD_FORMAT_HYPHEN);
      component.setNoDataText(false);
      expect(component.noDataText).toBe('ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE_DATE_FILTER_LAST_NINETY_DAYS');
    });
    it('should set noDataText to "ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE_DATE_FILTER" when admissionsList is empty and date filter is applied', () => {
      component.requestBody.startDate = '2022-01-01';
      component.requestBody.endDate = '2022-01-31';
      component.setNoDataText(false);
      expect(component.noDataText).toBe('ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE_DATE_FILTER');
    });
    it('should set noDataText to "ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE_SEARCH" when admissionsList is empty and search is applied', () => {
      component.requestBody.searchKey = 'test';
      component.setNoDataText(false);
      expect(component.noDataText).toBe('ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE_SEARCH');
    });
    it('should set noDataText to "ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE" when admissionsList is empty and no filters are applied', () => {
      component.setNoDataText(false);
      expect(component.noDataText).toBe('ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE');
    });
    it('should not change noDataText when admissionsList is not empty', () => {
      component.admissionsList = [{ admissionId: 1 }];
      component.setNoDataText(false);
      expect(component.noDataText).toBe('');
    });
  });
});
