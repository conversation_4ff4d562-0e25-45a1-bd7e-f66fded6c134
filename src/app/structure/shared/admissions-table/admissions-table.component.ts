import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { StructureService } from 'app/structure/structure.service';
import * as moment from 'moment';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { isBlank } from 'app/utils/utils';
import { AdmissionButtonActions, AdmissionRecordStatus, CONSTANTS, DateFormat, DateRanges, UserGroup } from 'app/constants/constants';
import { HttpService } from '../../../services/http/http.service';
import { APIs } from '../../../constants/apis';
import { Store } from '../storeService';
import { DataTableComponent } from '../data-table/data-table.component';

declare const NProgress: any;

@Component({
  selector: 'app-admissions-table',
  templateUrl: './admissions-table.component.html',
  providers: [DataTableComponent]
})
export class AdmissionsTableComponent implements OnChanges {
  defaultDateRangeType = DateRanges.LAST_NINETY_DAYS;
  @Input() events;
  @Input() isEditable = true;
  noDataText = '';
  chToolTipKey = 'defaultDateRangeInfo90';
  admissionButtonActions = AdmissionButtonActions;
  userData;
  tableHeaders;
  @Input() selectedPatient;
  @Output() selectedItem: EventEmitter<any> = new EventEmitter<any>();
  admissionsList: any = [];
  totalAdmissionsCount = 0;
  dateRangeStoreKey = Store.DATE_RANGE_FILTER_ADMISSIONS;
  dateRangeFilterOptions = [DateRanges.ALL, DateRanges.LAST_NINETY_DAYS];
  requestBody: {
    currentPage: number;
    patientIds: number[];
    rowsPerPage: number;
    searchKey: string;
    sortBy: string;
    sortDirection: string;
    startDate?: string;
    endDate?: string;
  } = {
    currentPage: 0,
    patientIds: [],
    rowsPerPage: CONSTANTS.contentLimit,
    searchKey: '',
    sortBy: 'startDate',
    sortDirection: 'DESC'
  };
  constructor(
    private httpService: HttpService,
    public structureService: StructureService,
    private toolTipService: ToolTipService,
    private cd: ChangeDetectorRef
  ) {
    this.userData = this.structureService.getUserdata();
    this.setTableHeaders();
  }
  setTableHeaders() {
    this.tableHeaders = [
      { label: 'LABELS.LINE_OF_BUSINESS', key: 'lineOfService', type: 'text' },
      { label: 'ADMISSION.LABELS.ADMISSION_STATUS', key: 'status', type: 'text' },
      { label: 'ADMISSION.LABELS.ADMISSION_ID', key: 'externalAdmissionId', type: 'text' },
      { label: 'ADMISSION.LABELS.ADMISSION_MRN', key: 'mrn', type: 'text' },
      {
        label: 'LABELS.START_DATE',
        key: 'startDate',
        type: 'callback',
        callback: (item) => (item.startDate ? moment.utc(item.startDate).format(DateFormat.MMDDYY_FORMAT_SLASH) : '')
      },
      {
        label: 'LABELS.END_DATE',
        key: 'endDate',
        type: 'callback',
        callback: (item) => (item.endDate ? moment.utc(item.endDate).format(DateFormat.MMDDYY_FORMAT_SLASH) : '')
      },
      { label: 'ADMISSION.LABELS.SITE', key: 'siteName', type: 'text' },
      {
        label: 'GENERAL.ACTIONS',
        type: 'actionButtons',
        buttons: [
          {
            title: () => 'ADMISSION.LABELS.VIEW_ADMISSION',
            id: 'view-admission',
            class: () => 'fa fa-eye',
            hide: () => this.isEditable,
            action: (item) => this.buttonActions(item, AdmissionButtonActions.VIEW)
          },
          {
            title: () => 'ADMISSION.LABELS.EDIT_ADMISSION',
            id: 'edit-admission',
            class: () => 'fa fa-pencil',
            hide: () => !this.isEditable,
            action: (item) => this.buttonActions(item, AdmissionButtonActions.EDIT)
          },
          {
            title: () => 'ADMISSION.LABELS.ACTIVATE_ADMISSION',
            id: 'activate-admission',
            class: () => 'fa fa-check',
            hide: (item) => item.citusStatus === AdmissionRecordStatus.ACTIVE || !this.isEditable,
            action: (item) => this.buttonActions(item, AdmissionButtonActions.ACTIVATE)
          },
          {
            title: () => 'ADMISSION.LABELS.INACTIVATE_ADMISSION',
            id: 'inactivate-admission',
            class: () => 'fa fa-ban',
            hide: (item) => item.citusStatus === AdmissionRecordStatus.INACTIVE || !this.isEditable,
            action: (item) => this.buttonActions(item, AdmissionButtonActions.INACTIVATE)
          },
          {
            title: () => 'ADMISSION.LABELS.DELETE_ADMISSION',
            id: 'delete-admission',
            hide: () => true,
            class: () => 'fa fa-trash',
            action: (item) => this.buttonActions(item, AdmissionButtonActions.DELETE)
          }
        ]
      }
    ];
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes.selectedPatient && !changes.selectedPatient.isFirstChange()) {
      this.dataChanges({ reload: true });
    } else if (changes.events && !changes.events.isFirstChange()) {
      const { admissionEvents } = changes.events.currentValue;
      if (admissionEvents && admissionEvents.reload) {
        this.dataChanges(admissionEvents);
      }
    }
  }
  getAllAdmissions() {
    NProgress.start();
    this.admissionsList = [];
    this.setNoDataText(true);
    this.requestBody.patientIds = [this.selectedPatient];
    this.httpService.doPost(APIs.getAdmissionsEndPoint, this.requestBody).subscribe(
      (res: any) => {
        NProgress.done();
        // loader stop
        this.totalAdmissionsCount = (res.page && res.page.totalElements) || 0;
        this.admissionsList = [...res.content];
        this.setNoDataText(false);
      },
      () => {
        NProgress.done();
        this.totalAdmissionsCount = 0;
        this.admissionsList = [];
        this.setNoDataText(false);
      }
    );
  }
  buttonActions(admission: any, action?: AdmissionButtonActions) {
    if (action === AdmissionButtonActions.INACTIVATE || action === AdmissionButtonActions.ACTIVATE || action === AdmissionButtonActions.DELETE) {
      let status: AdmissionRecordStatus;
      const title = 'LABELS.ARE_YOU_SURE';
      let text: string;
      let notifyMessage: string;
      let notifyMessageError: string;
      if (action === AdmissionButtonActions.INACTIVATE) {
        status = AdmissionRecordStatus.INACTIVE;
        text = 'ADMISSION.MESSAGES.ADMISSION_INACTIVATE_CONFIRM';
        notifyMessage = 'ADMISSION.MESSAGES.ADMISSION_INACTIVATE_SUCCESS';
        notifyMessageError = 'ADMISSION.MESSAGES.ADMISSION_INACTIVATE_FAILED';
      } else if (action === AdmissionButtonActions.ACTIVATE) {
        status = AdmissionRecordStatus.ACTIVE;
        text = 'ADMISSION.MESSAGES.ADMISSION_ACTIVATE_CONFIRM';
        notifyMessage = 'ADMISSION.MESSAGES.ADMISSION_ACTIVATE_SUCCESS';
        notifyMessageError = 'ADMISSION.MESSAGES.ADMISSION_ACTIVATE_FAILED';
      } else {
        status = AdmissionRecordStatus.DELETED;
        text = 'ADMISSION.MESSAGES.ADMISSION_DELETE_CONFIRM';
        notifyMessage = 'ADMISSION.MESSAGES.ADMISSION_DELETE_SUCCESS';
        notifyMessageError = 'ADMISSION.MESSAGES.ADMISSION_DELETE_FAILED';
      }
      this.structureService
        .showAlertMessagePopup({
          title: this.toolTipService.getTranslateData(title),
          text: this.toolTipService.getTranslateData(text)
        })
        .then((confirm) => {
          if (confirm) {
            this.httpService
              .doPut(APIs.admissionStatusByIdEndPoint.replace(/{admissionId}/g, admission.admissionId).replace(/{status}/, status.toString()), {})
              .subscribe(
                (response) => {
                  notifyMessage = this.toolTipService.getTranslateData(notifyMessage);
                  this.structureService.notifyMessage({ type: 'success', message: notifyMessage });
                  const activityLogMessage = `Patient(${this.selectedPatient}) with admission id (${admission.admissionId}) update status change to ${status} by ${this.userData.displayName}(${this.userData.userId})`;
                  const activityData = {
                    activityName: 'Patient admission status update',
                    activityType: 'Patient admission status update',
                    activityDescription: activityLogMessage
                  };
                  this.structureService.trackActivity(activityData);
                  Object.assign(admission, { citusStatus: response.citusStatus });
                },
                () => {
                  notifyMessageError = this.toolTipService.getTranslateData(notifyMessageError);
                  this.structureService.notifyMessage({ type: 'danger', message: notifyMessageError });
                }
              );
          }
        });
    } else {
      this.selectedItem.emit({ admission, action });
    }
  }
  dataChanges(event) {
    if (event.rowsPerPage) {
      this.requestBody.rowsPerPage = event.rowsPerPage;
      this.requestBody.currentPage = 0;
      this.getAllAdmissions();
    } else if (event.searchText || event.reset) {
      const searchText = event.reset ? '' : event.searchText;
      this.requestBody.currentPage = 0;
      this.requestBody.searchKey = !isBlank(searchText) ? `%${searchText}%` : '';
      this.getAllAdmissions();
    } else if (event.navigatePages) {
      this.requestBody.currentPage = event.navigatePages - 1;
      this.getAllAdmissions();
    } else if (event.dateRangeSelected) {
      const { startDate, endDate } = event.dateRangeSelected;
      if (startDate && endDate) {
        this.requestBody.startDate = moment(startDate).format(DateFormat.YYMMDD_FORMAT_HYPHEN);
        this.requestBody.endDate = moment(endDate).format(DateFormat.YYMMDD_FORMAT_HYPHEN);
      } else {
        this.requestBody.startDate = '';
        this.requestBody.endDate = '';
      }
      this.requestBody.currentPage = 0;
      this.getAllAdmissions();
    } else if (event.reload) {
      this.getAllAdmissions();
    }
  }
  setNoDataText(beforeApiCall: boolean) {
    if (this.admissionsList.length === 0) {
      const { startDate, endDate } = this.requestBody;
      if (beforeApiCall) {
        this.noDataText = 'ADMISSION.MESSAGES.ADMISSION_WAIT';
      } else if (moment(endDate).isSame(moment(), 'day') && moment(endDate).diff(moment(startDate), 'days') === 90) {
        this.noDataText = 'ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE_DATE_FILTER_LAST_NINETY_DAYS';
      } else if (this.requestBody.searchKey) {
        this.noDataText = 'ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE_SEARCH';
      } else if (this.requestBody.startDate && this.requestBody.endDate) {
        this.noDataText = 'ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE_DATE_FILTER';
      } else {
        this.noDataText = 'ADMISSION.MESSAGES.NO_ADMISSIONS_AVAILABLE';
      }
      this.cd.detectChanges();
    }
  }
}
