import { ElementRef } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { StructureService } from 'app/structure/structure.service';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Apollo, ApolloModule } from 'apollo-angular';
import { HttpModule } from '@angular/http';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { provideClients } from 'test-utils';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { PermissionService } from 'app/services/permission/permission.service';
import { AuthService } from '../auth.service';
import { SharedService } from '../sharedServices';
import { xssInputValidator } from '../xssInputValidator.directive';
import { UserListViewComponent } from './ch-user-list-view.component';

describe('UserListViewComponent', () => {
  let component: UserListViewComponent;
  let fixture: ComponentFixture<UserListViewComponent>;
  const mockElementRef: ElementRef = { nativeElement: document.createElement('div') };
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [UserListViewComponent, xssInputValidator],
      providers: [
        TranslateService,
        StructureService,
        Apollo,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        { provide: ElementRef, useValue: mockElementRef },
        PermissionService
      ],
      imports: [
        TranslateModule.forRoot(),
        FormsModule,
        RouterTestingModule,
        HttpModule,
        RouterTestingModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF
        })
      ]
    }).compileComponents();
  });
  beforeEach(() => {
    fixture = TestBed.createComponent(UserListViewComponent);
    component = fixture.componentInstance;
  });

  it('It should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('The trackBy method should return the userId', () => {
    const userId = component.trackByUserId(0, { userId: '1234', userDisplayName: 'Test' });
    expect(userId).toEqual('1234');
  });

  it('It should emit the selected item, if multiselect is disabled', () => {
    component.enableMultiSelect = false;
    spyOn(component.selectedUser, 'emit');
    const item = { userId: '1234', userDisplayName: 'Test' };
    component.selectUser(item);
    expect(component.selectedUser.emit).toHaveBeenCalledWith(item);
  });

  it('It should emit the selected items in an array, if multiselect is enabled', () => {
    component.enableMultiSelect = true;
    spyOn(component.selectedUser, 'emit');
    const item = [
      { userId: '1234', userDisplayName: 'Test', selected: true },
      { userId: '12345', userDisplayName: 'Test1', selected: true },
      { userId: '123456', userDisplayName: 'Test2', selected: false }
    ];
    const itemsSelected = [
      { userId: '1234', userDisplayName: 'Test', selected: true },
      { userId: '12345', userDisplayName: 'Test1', selected: true }
    ];
    component.userList = item;
    component.selectUser(item);
    expect(component.selectedUser.emit).toHaveBeenCalledWith(itemsSelected);
  });
});
