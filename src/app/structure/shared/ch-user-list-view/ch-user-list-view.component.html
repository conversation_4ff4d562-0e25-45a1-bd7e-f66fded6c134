<div id="ch-user-list-view">
  <ul class="treeview treeview-section-ui" *ngIf="userList && userList.length; else noData">
    <ng-container *ngIf="!enableMultiSelect; else multiselect">
      <li class="forward-model-option-user-list" *ngFor="let user of userList; trackBy: trackByUserId" (click)="selectUser(user)">
        <div>{{ user.userDisplayName }}</div>
      </li>
    </ng-container>
  </ul>
</div>

<ng-template #multiselect let-user>
  <li class="forward-model-option-user-list" *ngFor="let user of userList; trackBy: trackByUserId">
    <input type="checkbox" name="user{{ user.userId }}" id="{{ user.userId }}" value="{{ user.userId }}" [(ngModel)]="user.selected" />
    <label for="user{{ user.userId }}" class="custom-checkbox" [ngClass]="{ 'custom-checked': user.selected, 'custom-unchecked': !user.selected }">
      {{ user.userDisplayName }}
    </label>
  </li>
</ng-template>
<ng-template #noData>
  <div class="label-no-data text-center">{{ 'MESSAGES.NO_DATA_AVAILABLE' | translate }}</div>
</ng-template>
