import { Component, Input, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'ch-user-list-view',
  templateUrl: './ch-user-list-view.component.html',
  styleUrls: ['./ch-user-list-view.component.scss']
})
export class UserListViewComponent {
  @Input() userList: any[];
  @Output() selectedUser = new EventEmitter<any>();
  @Input() enableMultiSelect = false;

  selectUser(user: any): void {
    if (this.enableMultiSelect) {
      this.selectedUser.emit(
        this.userList.filter((item) => {
          return item.selected;
        })
      );
    } else this.selectedUser.emit(user);
  }

  trackByUserId(index, user) {
    return user.userId;
  }
}
