import { NO_ERRORS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import { Apollo, ApolloModule } from 'apollo-angular';
import { ManageSitesService } from 'app/structure/manage-sites/manage-sites.service';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { AuthService } from '../auth.service';
import { GlobalDataShareService } from '../global-data-share.service';
import { SharedService } from '../sharedServices';
import { SelectSitesComponent } from './select-sites.component';
import { of } from 'rxjs/observable/of';
import { provideClients } from 'test-utils';

describe('SelectSitesComponent', () => {
  let component: SelectSitesComponent;
  let fixture: ComponentFixture<SelectSitesComponent>;
  const userDetailsString = JSON.stringify({
    date: '20220325',
    dayNumber: '5',
    config: { enable_multisite: '1' },
    userContactVerification: { mobileVerified: 0, emailVerified: 0 },
  });
  let structservice: StructureService;
  beforeEach(async(() => {
    structservice = jasmine.createSpyObj('StructureService', ['userDetails']);
    structservice.userDetails = userDetailsString;
    TestBed.configureTestingModule({
      providers: [
        { provide: StructureService, useValue: structservice },
        FormBuilder,
        ManageSitesService,
        Apollo,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        GlobalDataShareService,
      ],
      declarations: [SelectSitesComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        HttpModule,
        ApolloModule.forRoot(provideClients),
        RouterTestingModule,
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF,
        }),
        TranslateModule.forRoot(),
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SelectSitesComponent);
    component = fixture.componentInstance;
    component.userData = structservice.userDetails;
    const events = of(null);
    component.events = events;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
});
