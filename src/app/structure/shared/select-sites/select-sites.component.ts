import { Component, OnInit, OnDestroy, Input, Output, EventEmitter,SimpleChange } from '@angular/core';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { ManageSitesService } from '../../manage-sites/manage-sites.service';
import { StructureService } from './../../../structure/structure.service';
import { Subscription, Observable } from 'rxjs';
import { SharedService } from '../sharedServices';
import { isBlank } from 'app/utils/utils';

declare var $: any;

@Component({
    selector: 'app-select-sites',
    templateUrl: './select-sites.component.html',
    styleUrls: ['./select-sites.component.css']
})
export class SelectSitesComponent implements OnInit, OnDestroy {
    @Output() readonly siteIds: EventEmitter<any> = new EventEmitter<any>();
    @Output() readonly siteName: EventEmitter<any> = new EventEmitter<any>();
    @Output() readonly hideDropdown: EventEmitter<any> = new EventEmitter<any>();
    @Output() readonly applySiteFilter: EventEmitter<any> = new EventEmitter<any>();
    @Input() singleSelection: boolean;
    @Input() userId: Number;
    @Input() filterType: boolean;
    @Input() disableFilter: boolean;
    @Input() selectedSiteIds: any; // Pass selected site ids in [34,69,89] format
    @Input() siteSelection: boolean;
    @Input() showUnassigned: boolean;
    @Input() dynamic = false;
    @Input() pushSites = false;
    @Input() crossSite = false;
    @Input() crossSiteCommunication = false;
    @Input() hideApplyFilter: boolean;
    @Input() isEditPage: boolean;
    @Input() popupSelectSite: boolean;
    @Input() messageGroup: boolean;
    @Input() popupFilter: boolean;
    @Input() isApplyFilter: boolean;
    @Input() fromDashboard: boolean;
    @Input() chatRoomFilter: boolean; // To fix preserve site filter state in active message list
    private eventsSubscription: Subscription;
    @Input() events: Observable<void>;
    @Input() bannerAlert: boolean;
    @Input() newChatTabChange = false; // For resetting site filter on tab change in new chat popup
    @Input() isMessageGroupEdit: boolean;
    @Input() badgeShowLimit = 2;
    @Input() selectAllOnLoad = false;
    @Input() allowSiteSelectForSingleSiteUser = false; // Purposefully Initialized as false to avoid impact on existing functionality

    siteList: FormGroup;
    siteDetails = [];
    selectedItem = [];
    dropdownSettings = {};
    items = [];
    showItem = false;
    enableMultisite: any;
    enableCrosssite:any;
    userData: any;
    selectedSites: any;
    userEditPage: boolean;
    innerPageWithFilter: boolean;
    selectedItemsCopy : any;
    previousSiteSelection: any;
    selectedItemCopy : any;
    isOpenSiteFilter = false;
    goToInnerPage = false;
    userDetails: any;
    defaultSiteFilter: any;
    siteDetailsTemp = []; // To store site details for single site user's edit feature
    private socketEventSubscriptions: Subscription;
    constructor(
        private _formBuild: FormBuilder,
        private _managesitesService: ManageSitesService,
        private _sharedService: SharedService,
        private _structureService: StructureService,
    ) {
       
    }
    
    ngOnInit() {
        console.log(this.selectedSiteIds, 'idssssssssssssss');
        this.userData = JSON.parse(this._structureService.userDetails);
        this.enableMultisite = parseInt(this.userData.config.enable_multisite) == 1;
        this.enableCrosssite = this.userData.enable_cross_site ? 1:0
         if(this.userData.config.enable_multisite !== "1"){
            /* if(this.userData && this.userData.mySites && this.userData.mySites.id){
            let ids = [];
            let name = [];
            ids.push(`${this.userData.mySites.id}`);
            if(this.userData.mySites.name)
            name.push(`${this.userData.mySites.name}`);
            this.siteIds.emit({ siteId: ids});
            this.siteName.emit({ siteNames: name});
            } 
             if(this.crossSite === false)
            this.hideDropdown.emit({ hideItem: true }); */
            if(this.crossSite == true){
            if(!this.fromDashboard && !this.bannerAlert){
                this.singleSelection = true;
            }else{
                this.singleSelection = false; 
            }
            if(!this.bannerAlert)
            this.filterType=true;
            this.selectedSiteIds = this.userData.crossSiteId.split`,`.map(x=>+x);
            // this.dynamic = true;
            }
            if(this.crossSiteCommunication == true){
                this.singleSelection = true;
                this.filterType=true;
                this.selectedSiteIds = this.userData.enabledCrosssites.split`,`.map(x=>+x);
                this.dynamic = true;
                }
            

        } 
        setTimeout(() => {
            if(this.singleSelection){
                this.hideApplyFilter = true;
            }
            this.selectedSites = this._sharedService.selectedSites; // Set selected sites from list page filter to selectedSites variable.
            if (!this.filterType|| this.popupSelectSite) {
                this.eventsSubscription = this.events.subscribe((data) => this.reset(data));
            }
            this.siteList = this._formBuild.group({
                site: ['']
            });

            this._managesitesService.getSitesByUserId(this.userId,this.enableMultisite,this.crossSite,this.crossSiteCommunication).then((response) => {
               let selectedSiteArray = this.selectedSiteIds;
               this.goToInnerPage = this._sharedService.goToInnerPage;
               if(typeof this.selectedSiteIds == 'string'){
                   selectedSiteArray =  this.selectedSiteIds.split(',').map(Number); 
               }
                if (response) {
                    response.forEach((item) => {
                        if(!isBlank(this.dynamic) || !this.enableMultisite ){
                            if(!isBlank(item.name) && !isBlank(item.id) && isBlank(this.siteDetails.find(s => s.id == item.id))){
                               this.siteDetails.push({ id: item.id, itemName: item.name });
                               if (this.selectAllOnLoad) this.selectedItem.push({ id: item.id, itemName: item.name });
                            }
                        }
                            if (this.isEditPage) {
                            console.log("filterType",this.filterType);
                            if (this.filterType) {
                                console.log("idssss",this.selectedSiteIds,typeof this.selectedSiteIds,item.id,typeof item.id)
                                if(selectedSiteArray !== undefined){
                                    if (selectedSiteArray.includes(item.id)) {
                                        if(isBlank(this.items.find(s => s.id == item.id))){
                                            this.items.push({ id: item.id, itemName: item.name });
                                        }
                                        
                                    }
                                }
                            }

                        } else {
                            if (!this.selectedSiteIds) {
                                    this.filterType ? ((isBlank(this.items.find(s => s.id == item.id)))? this.items.push({ id: item.id, itemName: item.name }) : '') : '';
                             } else {
                                console.log("filterType",this.filterType);
                                if (this.filterType) {
                                    console.log("idssss",this.selectedSiteIds,typeof this.selectedSiteIds,item.id,typeof item.id)
                                    if(selectedSiteArray !== undefined){
                                        if (selectedSiteArray.includes(item.id)) {
                                           
                                            if(isBlank(this.items.find(s => s.id == item.id))){
                                                this.items.push({ id: item.id, itemName: item.name });
                                            }
                                    
                                        }
                                    }
                                }
                                
                            }
                        }
                    });
                    this.showItem = (this.enableMultisite && this.siteDetails.length > 1) || (!this.enableMultisite && (this.crossSite === true && this.siteDetails.length > 1) || this.siteDetails.length > 1)|| (this.enableMultisite && this.enableCrosssite === true || this.siteDetails.length > 1);
                    console.log("this.hideItem",this.showItem);
                    //this.hideDropdown.emit({ hideItem: this.showItem });
                    this.pushUnassignedSite();
                        if (this.filterType || !this.showItem) {
                            // Display sites of selected user for user edit and single site user.
                            if(this._sharedService.goToInnerPage && !this.selectedSiteIds && !this.popupSelectSite && !this.chatRoomFilter){
                                this.selectedItem = this.selectedSites; 
                                this._sharedService.goToInnerPage = false;
                            }else {
                                 this.selectedItem = (!this.showItem) ? this.siteDetails : this.items;
                                 if(this.allowSiteSelectForSingleSiteUser && this.enableMultisite && this.siteDetails.length === 1 && 
                                !this.selectedSiteIds.includes(this.siteDetails[0].id)) {
                                    this.selectedItem = [];
                                }
                            }
                            if(this.selectedSiteIds || this.chatRoomFilter) {
                                this.userEditPage = true; // Identifying user goes from user edit page to listing page.
                            }
                        } else if (this._sharedService.goToInnerPage && !this._sharedService.innerPageFilter) {
                            // Display selected sites if list page when returning back to the list page when inner page have no site filter
                            this.selectedItem = this.selectedSites;
                            this._sharedService.goToInnerPage = false;
                        } else if (this._sharedService.innerPageFilter) {
                            // Display selected sites when returning back to the list page when inner page have site filter
                            this.innerPageWithFilter = true; // Identifying user goes from inner page with filter to listing page.
                            this._sharedService.innerPageFilter = false;
                        }
                        if(this._sharedService.siteFilterApplyButton){
                            this.isApplyFilter = true;
                        }
                        const defaultSiteFilter = this.getDefaultSiteFilter();
                        if(!isBlank(defaultSiteFilter) && defaultSiteFilter != 0 &&
                            this.filterType === true && 
                            (isBlank(this.isEditPage) || !this.isEditPage) && 
                            (isBlank(this.siteSelection) || !this.siteSelection) && 
                            isBlank(this.selectedSiteIds)) {
                            this.selectedItem = this.goToInnerPage ? this.selectedSites : defaultSiteFilter;
                            this._sharedService.selectedItemCopy = this.selectedItem;
                        }
                       this.emitItems();
                }
            });
        }, 3000);
        console.log("selectedSiteIds",this.selectedSiteIds,this.singleSelection);
        this.dropdownSettings = {
            singleSelection: this.singleSelection,
            text: "Select Site",
            selectAllText: 'All my sites',
            unSelectAllText: 'Clear all sites',
            classes: "select-sites",
            enableSearchFilter: true,
            enableFilterSelectAll: (this.singleSelection) ? false : true,
            badgeShowLimit: this.badgeShowLimit
        };
        this.previousSiteSelection = this.events.subscribe((res:any)=> {
            if(res == 'selectPreviousSite') {
                this.reset(res);
            }
        })
    }
    ngOnDestroy() {
        if (this.userEditPage || this.innerPageWithFilter) {
            // Set sites choosed in list page filter in local storage if inner page is user edit page or inner page with site filter
            this._sharedService.selectedSites = this.selectedSites;
        } else {
            // Set sites choosed in list page filter in local storage if inner page have no site filter
           if(!this.popupFilter){
            if(!this.hideApplyFilter && this.isApplyFilter){
                this._sharedService.selectedSites =  this.selectedItem;
            }else if(!this.isApplyFilter && this.selectedItem && (Object.keys(this.selectedItem).length)>=1){
                if(isBlank(this._sharedService.selectedItemCopy)){
                    this.selectedItemCopy = this.siteDetails;
                }else{
                    this.selectedItemCopy = this._sharedService.selectedItemCopy;
                }
                this._sharedService.selectedSites =  this.selectedItemCopy;
            }else{
                
                if(this.siteDetails.length > 0){
                    this._sharedService.selectedSites =  this.siteDetails;
                 }else{
                    this._sharedService.selectedSites = this._sharedService.selectedSites;
                 }
                 
            }
           }
        }
        if (!isBlank(this.previousSiteSelection)) {
            this.previousSiteSelection.unsubscribe();
        }
        /**Unsubscribe all the socket event subscriptions */
        if(this.socketEventSubscriptions) this.socketEventSubscriptions.unsubscribe();
    }
    onItemSelect(): void {
        if(this.hideApplyFilter && !this.isMessageGroupEdit){
            this.emitItems();
        }
        this.isApplyFilter = false;
    }
    OnItemDeSelect(): void {
        if(this.enableMultisite && this.allowSiteSelectForSingleSiteUser && !this.siteDetails.length && this.siteDetailsTemp.length === 1) {
            // Re-assign site details for single site user edit feature
            this.siteDetails = this.siteDetailsTemp;
        }
        if(this.hideApplyFilter && !this.isMessageGroupEdit){
            this.emitItems();
        }else if(this.isMessageGroupEdit && !this.isOpenSiteFilter){
            this.emitItems();
        }
        this.isApplyFilter = false;
    }
    onSelectAll(): void {
        this.selectedItem = this.selectedItem.filter(item => item.id !== -1);
        this.siteDetails = this.siteDetails.filter(item => item.id !== -1);
        if(this.hideApplyFilter && !this.isMessageGroupEdit){
        this.emitItems();
        }
    }
    onDeSelectAll(): void {
        this.selectedItem = [];
        this.pushUnassignedSite();
        if(this.hideApplyFilter && !this.isMessageGroupEdit){
            this.emitItems();
        }else if(this.isMessageGroupEdit && !this.isOpenSiteFilter){
            this.emitItems();
        }
    }
    onFilterSelectAll(): void {
        if(this.hideApplyFilter && !this.isMessageGroupEdit){
        this.emitItems();
        }
    }
    onFilterDeSelectAll(): void {
        if(this.hideApplyFilter && !this.isMessageGroupEdit){
        this.emitItems();
        }
    }
    onClose(): void {
        this.isOpenSiteFilter = false;
        if(this.isMessageGroupEdit){
            this.emitItems();
        }
    }
  onOpen(): void {
    this.isOpenSiteFilter = true;
    if (this.enableMultisite && this.allowSiteSelectForSingleSiteUser && this.siteDetails.length === 1 && !this.siteDetailsTemp.length) {
      // To store site details for single site user edit feature. Sometimes site details will be empty for single site user on item deselect.
      // Instead of calling API to fetch site details, we are storing the site details in siteDetailsTemp and using it when site details is empty.
      this.siteDetailsTemp = Object.assign([], this.siteDetails);
    }
  }
    applyFilter(): void {
        this.isApplyFilter = true;
        this._sharedService.siteFilterApplyButton = true;
        this._sharedService.selectedItemCopy = this.selectedItem;
        this.applySiteFilter.emit({filter: true});
        this.emitItems();
    }
    emitItems(): void {
        let ids = [];
        const name = [];
        if(this.selectedItem){
            this.selectedItem.forEach((value) => {
                if(value.id != -1){
                    ids.push(`${value.id}`);
                    name.push(`${value.itemName}`);
                }else{
                   this.selectedItem = []; 
                   this.selectedItem.push({id: -1, itemName: 'Unassigned sites'});
                   ids.push('-1');
                }
                if(ids.includes('-1')){
                    ids = [];
                    ids.push('-1');
                }
               
            });
            this.showItem = (this.enableMultisite && this.siteDetails.length > 1) || (!this.enableMultisite && (this.crossSite === true && this.siteDetails.length > 1) || this.siteDetails.length > 1) || (this.enableMultisite && this.enableCrosssite === true || this.siteDetails.length > 1);
            this.hideDropdown.emit({ hideItem: this.showItem });

        localStorage.setItem('siteId', JSON.stringify({ siteId: (ids.length != 0 && ((!this.showItem && this.siteSelection) || this.showItem)) ? ids : '0' }));
        this.siteIds.emit({ siteId: (ids.length != 0 && ((!this.showItem && this.siteSelection) || this.showItem)) ? ids : '0' });
        this.siteName.emit({ siteNames: name });
     }
    }
    reset(data): void {
        let selectedSiteArray = this.selectedSiteIds;
        if(typeof this.selectedSiteIds == 'string'){
            selectedSiteArray =  this.selectedSiteIds.split(',').map(Number); 
        }
        if (!isBlank(data)) {
            setTimeout(() => {
                if (data == 'displaySelectedSitesOnly') {
                    this.selectedItemsCopy = this.selectedItem;
                    this.selectedItem = [];
                    if (this.selectedSiteIds) {
                        const length = (this.selectedSiteIds != 0) ? this.selectedSiteIds.length : 0;
                            this.siteDetails = [];
                            this._managesitesService.getSitesByUserId(this.userId,this.enableMultisite,this.crossSite,this.crossSiteCommunication).then((response) => {
                                if (response) {
                                    response.forEach((item) => {
                                        if(isBlank(this.siteDetails.find(s => s.id == item.id))){
                                        this.siteDetails.push({ id: item.id, itemName: item.name });
                                        }
                                    });
                                    if(length !==0){
                                        var oTempSelectedSite = [];
                                        for(var key in selectedSiteArray){
                                            oTempSelectedSite[key]=selectedSiteArray[key].toString();
                                        }
                                        //const userFilter = this.siteDetails.filter(user => selectedSiteArray.includes((user.id).toString()));
                                        const userFilter = this.siteDetails.filter(user => oTempSelectedSite.includes((user.id).toString()));
                                        this.siteDetails = userFilter;
                                    }
                                }
                            });
                    }
                }else if (data == 'popup') {
                    this.selectedItemsCopy = this.selectedItem;
                    this.selectedItem = [];
                    if (this.selectedSiteIds) {
                        const length = (this.selectedSiteIds != 0) ? this.selectedSiteIds.length : 0;
                           // this.siteDetails = [];
                            this._managesitesService.getSitesByUserId(this.userId,this.enableMultisite,this.crossSite,this.crossSiteCommunication).then((response) => {
                               if (!isBlank(response)) {
                                    let popup = [];
                                    response.forEach((item) => {
                                            if(isBlank(this.siteDetails.find(s => s.id == item.id))){
                                                this.siteDetails.push({ id: item.id, itemName: item.name });
                                            }
                                           
                                        if (selectedSiteArray.includes(item.id)) {
                                            if(this.singleSelection)
                                            popup = [{ id: item.id, itemName: item.name }];
                                            else {
                                                if(isBlank(popup.find(s => s.id == item.id))){
                                                    popup.push({ id: item.id, itemName: item.name });
                                                }
                                            }
                                           
                                        }
                                        if (((!selectedSiteArray) || (selectedSiteArray && !selectedSiteArray.length)) && this.pushSites) {
                                            this.filterType ? ((isBlank(popup.find(s => s.id == item.id)))? (popup.push({ id: item.id, itemName: item.name })) : '') : '';
                                        }
                                    });
                                    this.selectedItem = popup;
                                    this.emitItems();
                                } else {
                                    this.selectedItem = [];
                                    this.emitItems();
                                }
                            });
                    } 
                }else if(data == 'dynamicSiteUpdate'){
                    console.log('else if dynamic update');
                    this.dynamicSiteUpdate();
                } else if (data == 'selectPreviousSite') {
                    const chosenSite = this.siteDetails.filter(item=>
                         item.id == this.selectedSiteIds[0]
                    );
                    this.selectedItem = chosenSite;
                    if(this.isMessageGroupEdit){
                        //To reset site selector with previously selected values
                        let chosenSites = [];
                        if(typeof this.selectedSiteIds === 'string'){
                            this.selectedSiteIds.split(',').forEach(element => {
                                chosenSites.push(this.siteDetails.find(s=> s.id == element));                            
                            });
                            this.selectedItem = chosenSites;   
                        } else {
                            this.selectedSiteIds.forEach(element => {
                                chosenSites.push(this.siteDetails.find(s=> s.id == element));                            
                            });
                            this.selectedItem = chosenSites;   
                        }                     
                    }
                } else if(data == 'closePopup'){
                    if(this.newChatTabChange && !this.enableMultisite){
                        // resetting site filter in new chat pop up on tab change multisite off case
                        this.selectedItem = [];
                        if (this.selectedSiteIds) {
                                this._managesitesService.getSitesByUserId(this.userId,this.enableMultisite,this.crossSite,this.crossSiteCommunication).then((response) => {
                                   if (!isBlank(response)) {
                                        let branchArray = []; //hold the selected branch details.
                                        response.forEach((item) => {
                                                if(isBlank(this.siteDetails.find(s => s.id == item.id))){
                                                    this.siteDetails.push({ id: item.id, itemName: item.name });
                                                }
                                               
                                            if (selectedSiteArray.includes(item.id)) { //setting the matched branch details
                                                if(this.singleSelection)
                                                branchArray = [{ id: item.id, itemName: item.name }];
                                                else
                                                branchArray.push({ id: item.id, itemName: item.name });
                                            }
                                            if (((!selectedSiteArray) || (selectedSiteArray && !selectedSiteArray.length)) && this.pushSites) {
                                                this.filterType ? (branchArray.push({ id: item.id, itemName: item.name })) : '';
                                            }
                                        });
                                        this.selectedItem = branchArray;
                                        this.emitItems();
                                    } else {
                                        this.selectedItem = [];
                                        this.emitItems();
                                    }
                                });
                        }
                    }else{
                        this.selectedItem = [];
                        const defaultSiteFilter = this.getDefaultSiteFilter();
                        if(!isBlank(defaultSiteFilter) && defaultSiteFilter != 0) {
                            this.selectedItem = defaultSiteFilter;
                        } else {
                            this._managesitesService.getSitesByUserId(this.userId,this.enableMultisite,this.crossSite,this.crossSiteCommunication).then((response) => {
                                if (response) {
                                    response.forEach((item) => {
                                        if(isBlank(this.selectedItem.find(s => s.id == item.id))){
                                            this.selectedItem.push({ id: item.id, itemName: item.name });
                                        }
                                    });
                                }
                            });
                        }
                    }
                   
                } else if('removeSelectedSite'){
                    this.selectedItem = this.selectedItemsCopy;
                } 
       });
        } else {
            this.selectedItem = [];
        }
    }
    dynamicSiteUpdate() : Promise<any> {
        return new Promise(async (resolve) => {
        console.log('dynamic site function');
        var userDetails = JSON.parse(this._structureService.userDetails);
        console.log('usrr ',userDetails.mySites);
                var userDetails = JSON.parse(this._structureService.userDetails);
                userDetails.mySites = {};
                
                this._structureService.userDetails = JSON.stringify(userDetails);
                console.log('isAlternateContact');
                console.log(userDetails.isAlternateContact);
                if (userDetails.isAlternateContact == true || userDetails.roleName == "Caregiver") {
                    userDetails.userId = userDetails.caregiver_userid;
                }
                this._managesitesService.getSitesByUserId(userDetails.userId,this.enableMultisite,this.crossSite,this.crossSiteCommunication).then((res) => {  
                    console.log(res);
                    console.log('getSitesByUserId');
                    userDetails.mySites = res;
                    
                    this._structureService.userDetails = JSON.stringify(userDetails);
                    console.log('UPDATED == ',JSON.parse(this._structureService.userDetails))
                    if (res) {
                        this.siteDetails = [];
                        var newSelectedArray = [];
                        res.forEach((item) => {
                            if(isBlank(this.siteDetails.find(s => s.id == item.id))){
                                this.siteDetails.push({ id: item.id, itemName: item.name });
                            }
                            var deleteSelectSite = this.selectedItem.findIndex(value => value.id == item.id);
                            if(deleteSelectSite != -1){
                                newSelectedArray.push(this.selectedItem[deleteSelectSite]);
                            } 
                        });
                        this.selectedItem = newSelectedArray;
                        console.log('site details == ', this.siteDetails);
                        console.log('selected Items new == ', this.selectedItem);
                        var itemShow = false;
                        if(this.enableMultisite && this.siteDetails.length > 1){
                            itemShow = true;
                        }
                        console.log("Item view = ",itemShow);
                        this.hideDropdown.emit({ hideItem: itemShow });
                        this._sharedService.viewItem = itemShow;
                        this.emitItems();
                        resolve(true);
                        
                    }
                });  
    });
    }
    pushUnassignedSite(): void {
        const idArray = [];
        if(!this.siteSelection && isBlank(this.selectedSiteIds) && !isBlank(this.showUnassigned)){
            this.siteDetails.forEach((value) => {
              idArray.push(value.id);
            });
            if(!idArray.includes(-1)){
                this.siteDetails.push({id: -1, itemName: 'Unassigned sites'});
            }
        }
    }

    /* For pop resetting values in select for edit fields, 
       @selectedSiteIds has value and @dynamic has value true, 
       this reset calls and reset the value of pop up edit */
    ngOnChanges(changes: { [propKey: string]: SimpleChange }) {
        if (changes['selectedSiteIds'] && this.dynamic == true){
           console.log("dynamic",this.dynamic,changes['selectedSiteIds']);
            if(this.messageGroup){
                const siteData = [];
                if(typeof(this.selectedSiteIds) == 'number'){
                    siteData.push(this.selectedSiteIds);
                    this.selectedSiteIds = siteData;
                } else if(typeof(this.selectedSiteIds) == 'string'){
                    this.selectedSiteIds = this.selectedSiteIds.split(',').map(Number);
                }
            }
            this.reset('popup');
            
        }
        this.socketEventSubscriptions = this._structureService.subscribeSocketEvent('updatedUserSites').subscribe((data) => {
            this.dynamicSiteUpdate().then((res) => {
                if(res){
                    if(data.data.updatedBy !== this.userData.userId){
                        this._sharedService.emitToTopBar.emit({ toTopBar: true });

                    }
                    
                }
            });
        }); 
}
getDefaultSiteFilter() {
    const userDetails = JSON.parse(this._structureService.userDetails);
    if(!isBlank(userDetails.defaultSitesFilter) && userDetails.defaultSitesFilter != 0) {
        userDetails.defaultSitesFilter = userDetails.defaultSitesFilter.map(function(obj) {
            return { ...obj, ['itemName']: obj['name'] };
        });
    } 
    return userDetails.defaultSitesFilter;
}
}
