<form class="" id="site-list" name="site-list" #f="ngForm">
    <div class="form-body">
        <div class="form-group row">
            <div class="col-md-12" [ngClass]="{'disable-filter':disableFilter, 'row':!hideApplyFilter,'custom-site-filter': (filterType && (!selectedSiteIds || fromDashboard))}">
                <span class="" [ngClass]="{'col-md-10':!hideApplyFilter}">
                    <angular2-multiselect [data]="siteDetails" [(ngModel)]="selectedItem" name="site"
                    [settings]="dropdownSettings" (onSelect)="onItemSelect()" (onDeSelect)="OnItemDeSelect()"
                    (onSelectAll)="onSelectAll()" (onDeSelectAll)="onDeSelectAll()"
                    (onFilterSelectAll)="onFilterSelectAll()" (onFilterDeSelectAll)="onFilterDeSelectAll()" (onClose)="onClose()" (onOpen)="onOpen()">
                </angular2-multiselect>
                </span>
                 
                <span class="apply-filter-class btn btn-sm btn-info searchBPdg col-md-2" *ngIf="!hideApplyFilter" (click)="applyFilter()">Apply</span>
       </div>
      </div>
    </div>
</form>