/**
 * This interface contains models for the common user search component
 */

export interface UserListParams {
  status?: string;
  roleId?: number;
  siteIds?: string;
  messageGroupMemberIds?: string;
  optionShow?: string;
  getRolePrivileges?: string;
  excludeRoleId?: boolean;
  excludeLogginedUser?: number;
  pageCount?: number;
  searchKeyword?: string;
  chatWithFilterTenantId?: number;
  isTenantRoles?: string;
  needVirtualPatients?: boolean;
  isFromChat?: number;
  nursingAgencies?: boolean;
  userGroup?: number;
  isSchedule?: boolean;
  accessSecurityEnabled?: boolean;
  accessSecurityEsiValue?: string;
  accessSecurityIdentifierType?: string;
  accessSecurityType?: string;
}
