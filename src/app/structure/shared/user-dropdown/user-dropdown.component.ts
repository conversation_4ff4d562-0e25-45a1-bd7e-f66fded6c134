import { Component, EventEmitter, Input, Output, OnChanges, SimpleChanges } from '@angular/core';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { isBlank, formatUserName } from 'app/utils/utils';
import { HttpParams } from '@angular/common/http';
import { userFilterPipe } from 'app/structure/inbox/inbox-modal-filter';
import * as moment from 'moment';
import { HttpService } from '../../../services/http/http.service';
import { APIs } from '../../../constants/apis';
import { SearchComponentSettings, ScrollType, LoadMoreItems } from '../search-select/search-select.interface';
import { UserListParams } from '../user-dropdown/user-dropdown.interface';

@Component({
  selector: 'app-user-dropdown',
  templateUrl: './user-dropdown.component.html',
  styleUrls: ['./user-dropdown.component.scss']
})
export class UserDropdownComponent implements OnChanges {
  @Input() userType;
  @Input() listType = '';
  @Input() requestParams: UserListParams;
  @Input() multiSelection = false;
  @Input() selectedUsers;
  @Input() disableContactlessUsers = false;
  @Input() siteIds;
  @Input() hideSubList = false;
  @Input() reset = false;
  @Output() selectedUserDetails: EventEmitter<any> = new EventEmitter<any>();
  userData: any = [];
  searchComponentSettings: SearchComponentSettings;
  pageCount = 0;
  currentUsersListCount = 0;

  constructor(private httpService: HttpService, public modalFilter: userFilterPipe, private structureService: StructureService, private tooltipService: ToolTipService) {
    this.searchComponentSettings = {
      allowLazyLoading: true,
      enableTagging: false,
      fieldName: 'TITLES.SEARCH',
      placeHolderText: 'TITLES.SEARCH',
      multiSelection: this.multiSelection,
      lazyLoadType: ScrollType.INFINITE
    };
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (!isBlank(changes.requestParams) && !isBlank(changes.requestParams.currentValue)) {
      this.requestParams = changes.requestParams.currentValue;
    }
    if (!isBlank(changes.reset) && !isBlank(changes.reset.currentValue)) {
      this.reset = changes.reset.currentValue;
    }
    if (!isBlank(changes.multiSelection) && !isBlank(changes.multiSelection.currentValue)) {
      this.searchComponentSettings.multiSelection = changes.multiSelection.currentValue;
    }
    switch (this.userType) {
      case 'patient':
        this.searchComponentSettings.fieldName = 'LABELS.PATIENT';
        this.searchComponentSettings.placeHolderText = 'LABELS.SEARCH_PATIENT';
        break;
      case 'staff':
        this.searchComponentSettings.fieldName = 'LABELS.STAFF';
        this.searchComponentSettings.placeHolderText = 'LABELS.SEARCH_STAFF';
        break;
      case 'partner':
        this.searchComponentSettings.fieldName = 'LABELS.PARTNER';
        this.searchComponentSettings.placeHolderText = 'LABELS.SEARCH_PARTNER';
        break;
      default:
        this.searchComponentSettings.fieldName = 'TITLES.SEARCH';
        this.searchComponentSettings.placeHolderText = 'TITLES.SEARCH';
        break;
    }
  }
  getAllUsers(event?: LoadMoreItems) {
    if (event.searchText !== '') {
      let params = new HttpParams();
      this.requestParams.searchKeyword = event.searchText;
      this.requestParams.pageCount = event && event.loadMore ? this.requestParams.pageCount + 1 : 0;
      const loginUserData = JSON.parse(this.structureService.userDetails);
      if (!isBlank(loginUserData.nursing_agencies)) {
        this.requestParams.nursingAgencies = loginUserData.nursing_agencies;
      }
      if (loginUserData.accessSecurityEnabled && this.userType === 'patient') {
        this.requestParams.accessSecurityEnabled = loginUserData.accessSecurityEnabled;
        this.requestParams.accessSecurityEsiValue = loginUserData.accessSecurityEsiValue;
        this.requestParams.accessSecurityIdentifierType = loginUserData.accessSecurityIdentifierType;
        this.requestParams.accessSecurityType = loginUserData.accessSecurityType;
      }
      const keys = Object.keys(this.requestParams);
      keys.forEach((key) => {
        params = params.set(key, this.requestParams[key]);
      });
      this.httpService.doGet(APIs.getTenantUsers, { params }).subscribe((res) => {
        let userData = this.modalFilter.transform(res, this.requestParams.optionShow);
        userData = userData.map(({ name, ...user }) => {
          const patientData = {
            firstName: user.firstname,
            lastName: user.lastname,
            dob: !isBlank(user.dob) ? `${this.tooltipService.getTranslateData('LABELS.DOB')} ${moment(user.dob).format('MM/DD/YYYY')}` : '',
            mrn: !isBlank(user.IdentityValue) ? user.IdentityValue : ''
          };
          const formattedName =
            this.listType === 'pdg'
              ? this.tooltipService.getTranslateDataWithParam('LABELS.PDG_TOPIC', patientData)
              : formatUserName(user, this.userType, this.tooltipService);
          let subList = [];
          if (this.userType === 'patient' && !this.hideSubList) {
            subList = user.alternateContacts;
            subList.forEach((item) => {
              item.name = formatUserName(item, 'alternateContact', this.tooltipService);
            });
          }
          return { id: user.userId, name: formattedName, subList, ...user };
        });
        this.currentUsersListCount = userData.length;
        this.userData = event.loadMore ? this.userData.concat(userData) : userData;
      });
    }
  }
  onUsersSelect(event) {
    const selectedUsers = Array.isArray(event) ? event : [event];
    this.selectedUserDetails.emit({ users: selectedUsers, reset: !isBlank(event.reset) && event.reset ? event.reset : false });
    this.reset = false;
  }
}
