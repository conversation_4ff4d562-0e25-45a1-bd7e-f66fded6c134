import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CommonTestingModule } from 'app/utils/common-testing.module';
import { UserDropdownComponent } from './user-dropdown.component';

describe('UserDropdownComponent', () => {
  let component: UserDropdownComponent;
  let fixture: ComponentFixture<UserDropdownComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [CommonTestingModule]
    }).compileComponents();
    fixture = TestBed.createComponent(UserDropdownComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.userType).toBeUndefined();
    expect(component.requestParams).toBeUndefined();
    expect(component.multiSelection).toBeFalsy();
    expect(component.selectedUsers).toBeUndefined();
    expect(component.userData).toEqual([]);
    expect(component.pageCount).toBe(0);
    expect(component.currentUsersListCount).toBe(0);
    const expectedSettings = {
      allowLazyLoading: true,
      enableTagging: false,
      fieldName: 'TITLES.SEARCH',
      placeHolderText: 'TITLES.SEARCH',
      multiSelection: false,
      lazyLoadType: 'infinite'
    };
    expect(component.searchComponentSettings).toEqual(expectedSettings);
  });

  it('should emit selectedUserDetails event on user select', () => {
    spyOn(component.selectedUserDetails, 'emit');
    const user = { id: 1, name: 'John Doe' };
    component.onUsersSelect(user);
    expect(component.selectedUserDetails.emit).toHaveBeenCalledWith({ users: [user], reset: false });
  });
});
