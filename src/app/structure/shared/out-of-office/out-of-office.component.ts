import { Component, OnInit, OnDestroy, ChangeDetectionStrategy, ViewEncapsulation, Output, EventEmitter } from '@angular/core';
import { StructureService } from '../../structure.service';
import * as moment from 'moment';
@Component({
  selector: 'out-of-office',
  templateUrl: './out-of-office.component.html',
  styleUrls: ['./out-of-office.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class OutOfOfficeComponent implements OnInit, OnDestroy {
  userData: any;
  @Output() outOfOfficeDetails = new EventEmitter<any>();
constructor(private structureService: StructureService) {}

  ngOnInit(): void {
    this.structureService.loadMicroFrontend();
     this.userData = this.structureService.userDetails ? JSON.parse(this.structureService.userDetails) : '';
  }
  getoutOfOfficeDetails(data: any): void {
    this.outOfOfficeDetails.emit(data.detail);
  }

  ngOnDestroy(): void {
    this.structureService.unLoadMicroFrontend();
  }
}
