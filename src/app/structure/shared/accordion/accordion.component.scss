.accordion {
    border: 1px solid #ccc;
    border-radius: 1mm;
    font-family: <PERSON><PERSON>;
    height: auto;
    margin-top: 2mm;
    min-width: 700px;
    .accordion-header {
        padding: 3mm;
        border-bottom: 1px solid #ccc;
        height: 40px;

        label {
            float: left !important;
        }

        span {
            float: right !important;
            color: #ccc;
            font-style: normal;
            cursor: pointer;
        }
    }

    .accordion-body {
        max-height: 200px !important;
        min-height: auto;
        overflow-y: auto !important;

        label {
            margin-top: 3mm;
            width: 100%;
            text-align: center !important;
        }

        .accordion-item {
            display: block;
            float: left;
            color: #fff;
            background: #789;
            padding: 5px;
            padding-right: 25px;
            margin: 4px;
            border-radius: 3px;
            line-height: 1.78rem;
            color: #74708d;
            background: #e4e9f0;
            border-color: #e4e9f0;

            span {
                cursor: pointer;
            }
        }
    }
}
