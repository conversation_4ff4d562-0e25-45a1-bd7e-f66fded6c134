import { Component, Input, Output, EventEmitter } from '@angular/core';
import { Users } from './accordion.interface';

@Component({
  selector: 'app-accordion',
  templateUrl: './accordion.component.html',
  styleUrls: ['./accordion.component.scss'],
})
export class AccordionComponent {
  // Decorative
  @Input() userList: Users[] = [];
  @Output() removedUser = new EventEmitter<Users>();
  @Input() headerName = '';
  @Input() showListWhenUsersSelected = false;
  // Properties
  toggled = false;

  /**
   * To remove user from the selected list. Emits the user Id
   * @param user type of Users
   * @returns void
   */
  removeUser(user: Users): void {
    const userRemoved = user;
    userRemoved.selected = false;
    this.removedUser.emit(userRemoved);
  }
  /**
   *
   * @param index index of the item
   * @param user data, any type
   * @returns
   */
  trackById(index, user) {
    return user.userId;
  }
}
