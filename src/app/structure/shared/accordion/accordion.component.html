<div class="accordion">
    <div class="accordion-header">
        <label>{{headerName? headerName: 'LABELS.ACCORDION_HEADER' | translate}}</label>
        <span *ngIf="!showListWhenUsersSelected" [ngClass]="toggled? 'fa fa-chevron-up':'fa fa-chevron-down'" (click)="toggled = !toggled"></span>
    </div>
    <div class="accordion-body" *ngIf="toggled || (showListWhenUsersSelected && userList.length)">
        <label *ngIf="!userList.length">{{'MESSAGES.NO_USER_SELECTED' | translate }}</label>
        <span *ngFor="let user of userList; trackBy:trackById" class="accordion-item">
            {{user.displayname}}
            <span class="fa fa-remove" (click)="removeUser(user)"></span>
        </span>
    </div>
</div>