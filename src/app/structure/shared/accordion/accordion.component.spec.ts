import { TestBed, ComponentFixture } from '@angular/core/testing';
import { AccordionComponent } from './accordion.component';
import { Users } from './accordion.interface';
import { By } from '@angular/platform-browser';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
describe('AccordionComponent', () => {
  let component: AccordionComponent;
  let fixture: ComponentFixture<AccordionComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [AccordionComponent],
      providers: [TranslateService],
      imports: [TranslateModule.forRoot()],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(AccordionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  //Test suites
  it('It should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('It should show the users list when array is assigned with values', () => {
    const userList: Users[] = [
      {
        tenantId: '100',
        tenantName: 'Unit Test',
        siteId: '100',
        siteName: 'Site-1',
        dualRoles: '',
        userid: '100001',
        userId: '100001',
        name: '<EMAIL>',
        displayname: 'Test Staff-1',
        countryCode: '+1',
        firstname: 'Test',
        lastname: 'Staff-1',
        status: '1',
        roleId: '2',
        dob: '01/01/2023',
        role: 'Gastrology',
        tenantRoleId: '12000',
        cmisid: '',
        selected: true,
      },
      {
        tenantId: '100',
        tenantName: 'Unit Test',
        siteId: '100',
        siteName: 'Site-1',
        dualRoles: '',
        userid: '100002',
        userId: '100002',
        name: '<EMAIL>',
        displayname: 'Test2 Staff-2',
        countryCode: '+1',
        firstname: 'Test2',
        lastname: 'Staff-2',
        status: '1',
        roleId: '2',
        dob: '01/01/2023',
        role: 'Gastrology',
        tenantRoleId: '12000',
        cmisid: '',
        selected: true,
      },
    ];
    component.userList = userList;
    component.toggled = true;
    fixture.detectChanges();

    const userListItems = fixture.debugElement.queryAll(
      By.css('.accordion-item')
    );
    expect(userListItems.length).toBe(userList.length);

    userListItems.forEach((item, index) => {
      const user = userList[index];
      expect(item.nativeElement.textContent).toContain(user.displayname);
    });
  });

  it('It should emit the selected item', () => {
    let removedItem = '';
    component.removedUser.subscribe((item) => {
      removedItem = item.userId;
    });
    const user = {
      tenantId: '100',
      tenantName: 'Unit Test',
      siteId: '100',
      siteName: 'Site-1',
      dualRoles: '',
      userid: '100001',
      userId: '100001',
      name: '<EMAIL>',
      displayname: 'Test Staff-1',
      countryCode: '+1',
      firstname: 'Test',
      lastname: 'Staff-1',
      status: '1',
      roleId: '2',
      dob: '01/01/2023',
      role: 'Gastrology',
      tenantRoleId: '12000',
      cmisid: '',
      selected: true,
    };
    component.removeUser(user);
    expect(removedItem).toBe('100001');
  });
});
