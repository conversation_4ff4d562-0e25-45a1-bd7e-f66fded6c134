import { Component, ElementRef, Input, AfterViewInit, Renderer2 } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { videoTutorialService } from '../../video-tutorial.service';
import { StructureService } from '../../structure.service';
declare var $: any;
@Component({
  selector: 'video-modal,[chToolTips]',
  templateUrl: './video-tutorial.component.html',
  styles: [`
  .black_overlay {
    display: none;
    position: fixed;
    top: 0%;
    left: 0%;
    width: 100%;
    height: 100%;
    background-color: black;
    z-index: 1001;
    -moz-opacity: 0.5;
    opacity: .5;
    filter: alpha(opacity=80);
  }
  .white_content {
    display: none;
    position: fixed;
    top: 25%;
    left: 50%;   
    height: auto;
    padding: 16px;   
    z-index: 1002;
    overflow: auto;
    text-align:center;
    color:#FFF;
  }
  `]
})

export class VideoTutorialComponent implements AfterViewInit {
  @Input() video_button:string;
  @Input() video_url: string  ='';
  @Input() videoPage: string  ='';
  
  video_src = this.sanitizer.bypassSecurityTrustResourceUrl('');    
  data_src; 
  constructor(private elRef: ElementRef,private _structureService: StructureService, private _videoTutorialService: videoTutorialService,private sanitizer: DomSanitizer,private renderer: Renderer2 ) {
  }
  ngAfterViewInit(): void {   
      var data_src =  this._videoTutorialService.getVideoUrl(this.video_url); 
      this.video_src = this.sanitizer.bypassSecurityTrustResourceUrl('');        
  }

  show_popup()
  {
   let vdourl = this.video_url;
   console.log(this.video_button);
   var data_src =  this._videoTutorialService.getVideoUrl(vdourl); 
  this.video_src = this.sanitizer.bypassSecurityTrustResourceUrl(data_src);
   $('#videotutorial').modal('show'); 

   
   var userDetails = this._structureService.userDetails;
   var userData = JSON.parse(userDetails);

   var activityData = {
    activityName: "Play Video Tutorial",
    activityType: "video tutorial",
    activityDescription: userData.displayName + "(" + userData.userId + ") play Video Tutorial From page: " +  this.videoPage
};
this._structureService.trackActivity(activityData);

  
  }  
  hide_popup()
  {
    document.getElementById('videotutorial').style.display='none';
    document.getElementById('fade').style.display='none';       
    let vid = <HTMLVideoElement>document.getElementById("videoID");      
    vid.pause();
    
    var userDetails = this._structureService.userDetails;
    var userData = JSON.parse(userDetails);
 
    var activityData = {
     activityName: "Close Video Tutorial",
     activityType: "video tutorial",
     activityDescription: userData.displayName + "(" + userData.userId + ") Close Video Tutorial From page: " +  this.videoPage
 };
 this._structureService.trackActivity(activityData);      
  }
  

}
