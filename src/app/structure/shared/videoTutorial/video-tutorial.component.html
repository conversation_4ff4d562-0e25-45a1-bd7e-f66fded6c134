  
  <button _ngcontent-c4="" class="{{video_button}}"  id="vdeoid" (click)="show_popup()">
        <i _ngcontent-c4="" class="fa fa-video-camera mr-2"></i>
        Video Tutorial
    </button>
  <div id="fade" class="black_overlay"></div>

  <div class="modal fade bd-example-modal-lg forward-modal videotutorial" id="videotutorial"   data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel"
    aria-hidden="true">
    <div class="modal forward-modal" role="dialog" style="display: block;" tabindex="-1">
        <div class="modal-dialog" ng-reflect-klass="modal-dialog" ng-reflect-ng-class="[object Object]">
            <div class="modal-content video-transparent" ng-reflect-klass="modal-content" ng-reflect-ng-class="[object Object]">
                <div class="modal-header modal-header-hide" ng-reflect-klass="modal-header" ng-reflect-ng-class="[object Object]">
                    <h4 class="modal-title"></h4>
                    <button aria-label="Close" (click)="hide_popup()" class="close video-close-button-position" data-dismiss="modal" type="button" ng-reflect-klass="close" ng-reflect-ng-class="[object Object]">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <!--bindings={
      "ng-reflect-ng-if": "false"
    }-->
                <!--bindings={
      "ng-reflect-ng-if": "false"
    }-->
                <!--bindings={
      "ng-reflect-ng-if": "true"
    }--><div class="centerme">
                    <video autoplay="false" id="videoID" preload="none" class="centerme" controls="controls" poster="assets/modules/dummy-assets/common/img/Loading_icon.gif" [src]="video_src"></video>
                </div>
                
                <!--bindings={}-->
            </div>
        </div>
    </div>
</div>