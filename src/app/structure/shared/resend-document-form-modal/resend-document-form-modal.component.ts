import {
  Component,
  Input,
  Output,
  EventEmitter,
  ChangeDetectorRef,
  ChangeDetectionStrategy,
  OnChanges,
  HostListener,
} from '@angular/core';
import { StructureService } from 'app/structure/structure.service';
import { isBlank } from 'app/utils/utils';
import { HttpService } from 'app/services/http/http.service';
import { HttpParams } from '@angular/common/http';
import { APIs } from 'app/constants/apis';
import { RecipientList } from './resend-document-form-modal.interface';
import { CONSTANTS } from 'app/constants/constants';
import { ToolTipService } from 'app/structure/tool-tip.service';
declare const $;
@Component({
  selector: 'app-resend-document-form',
  templateUrl: './resend-document-form-modal.component.html',
  styleUrls: ['./resend-document-form-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ResendDocumentFormModalComponent implements OnChanges {
  //Input Decorative
  @Input() showModal = false;
  @Input() getRecipientsFor = '';
  @Input() patientId = '';
  @Input() entityId = '';
  @Input() extraData = {
    admissionId: ''
  };

  //Output Decorative
  @Output() eventEmitterSelectedRecipients = new EventEmitter<
    RecipientList[]
  >();
  @Output() onClose = new EventEmitter<boolean>();
  //Class properties
  recipientList: RecipientList[];
  selectedRecipientsList: RecipientList[];
  tenantId = '';
  loaderFlag = false;

  constructor(
    private structureService: StructureService,
    private httpService: HttpService,
    private tooltipService: ToolTipService,
    private changeDetectorRef: ChangeDetectorRef
  ) {
    this.tenantId = JSON.parse(this.structureService.userDetails).tenantId;
    if (
      !isBlank(this.structureService.getCookie('crossTenantId')) &&
      this.structureService.getCookie('tenantId') !==
        this.structureService.getCookie('crossTenantId')
    ) {
      this.tenantId = this.structureService.getCookie('crossTenantId');
    }
  }

  //Handle the onClose emit on outside click
  @HostListener('document:click', ['$event.target'])
  onClick(target) {
    if (target.id === 'recipientsModal') {
      this.onClose.emit(true);
    }
  }

  /**To fetch the recipients list
   * @returns void
   */
  fetchRecipientsList() {
    if (
      !isBlank(this.patientId) &&
      (!this.structureService.isMultiAdmissionsEnabled || (!isBlank(this.extraData) && !isBlank(this.extraData.admissionId))) &&
      !isBlank(this.entityId) &&
      !isBlank(this.getRecipientsFor)
    ) {
      this.loaderFlag = true;
      //Fetch the recipients list
      const httpParams = new HttpParams()
        .set('tenantId', this.tenantId)
        .set('patientId', this.patientId)
        .set('admissionId', this.structureService.isMultiAdmissionsEnabled ? this.extraData && this.extraData.admissionId : '')
        .set('entityId', this.entityId)
        .set('getRecipients', this.getRecipientsFor.toLowerCase())
        .set('isActive', '1')
        .set('chatRoomInvite', 'true');
      this.httpService
        .doGet(APIs.getAlternateContactCaregiverPatient, { params: httpParams })
        .subscribe((response) => {
          if (response.success && response.data) {
            const data = response.data;
            this.recipientList = [
              !isBlank(data.caregiver)
                ? {
                    userId: data.caregiver.userId,
                    displayName: data.caregiver.displayName,
                    selected: data.caregiver.selected ? true : false,
                  }
                : null,
              !isBlank(data.patient)
                ? {
                    userId: data.patient.userId,
                    displayName: data.patient.displayName,
                    selected: data.patient.selected ? true : false,
                  }
                : null,
              ...(data.alternateContacts || []).map((contact) => ({
                userId: contact.userId,
                displayName:
                  contact.displayName + ' (' + contact.relation + ')',
                selected: contact.selected ? true : false,
              })),
            ].filter((item) => !isBlank(item));
            //Push the selected items to the array
            this.selectedRecipientsList = this.recipientList.filter((item) => {
              return item.selected;
            });
          }
          this.loaderFlag = false;
          this.changeDetectorRef.detectChanges();
        });
    }
  }

  ngOnChanges() {
    if (this.showModal) {
      this.fetchRecipientsList();
    }
    $('#recipientsModal').modal('show');
  }

  /**
   * Confirm before send. No action if cancelled.
   * @returns void
   */
  confirmToSend(): void {
    let recipientsName = '';
    if (!isBlank(this.selectedRecipientsList)) {
      if (this.selectedRecipientsList.length > 1) {
        recipientsName = this.selectedRecipientsList
          .slice(0, -1)
          .map((item) => item.displayName)
          .join(',');
        recipientsName +=
          ' and ' + this.selectedRecipientsList.slice(-1)[0]['displayName'];
      } else {
        recipientsName += this.selectedRecipientsList[0]['displayName'];
      }
    }

    const type =
      this.getRecipientsFor === CONSTANTS.contentType.documents
        ? CONSTANTS.contentType.document
        : CONSTANTS.contentType.form;
    const config = {
      text: this.tooltipService.getTranslateDataWithParam(
        'MESSAGES.FORM_OR_DOCUMENT_RESEND_CONFIRM',
        { type: type, recipientsName: recipientsName }
      ),
    };
    this.structureService.showAlertMessagePopup(config).then((confirm) => {
      if (confirm) {
        this.eventEmitterSelectedRecipients.emit(this.selectedRecipientsList);
        this.closeModal();
      }
    });
  }

  /**On select and un-select the recipients, the selectedRecipientsList will be updated
   * @param recipient recipient list object of type RecipientList
   * @returns void
   */
  onRecipientsChange(recipient: RecipientList): void {
    if (
      recipient.selected &&
      this.selectedRecipientsList.findIndex(
        (item) => item.userId === recipient.userId
      ) === -1
    ) {
      this.selectedRecipientsList.push(recipient);
    } else if (!recipient.selected) {
      this.selectedRecipientsList = this.selectedRecipientsList.filter(
        (item) => {
          return item.userId !== recipient.userId;
        }
      );
    }
  }

  /**To select all the recipients
   * @returns void
   */
  selectAll(): void {
    if (!isBlank(this.recipientList)) {
      this.recipientList.forEach((item) => {
        item.selected = true;
      });
      this.selectedRecipientsList = this.recipientList;
    }
  }

  /**To clear all the selected recipients
   * @returns void
   */
  clearAll(): void {
    if (!isBlank(this.recipientList)) {
      this.recipientList.forEach((item) => {
        item.selected = false;
      });
    }
    this.selectedRecipientsList = [];
  }

  /**
   * To improve the ngFor directive performance
   * @param index array element index
   * @param recipient object of type RecipientList
   * @returns string
   */
  trackByUserId(index, recipient: RecipientList) {
    return recipient.userId;
  }

  closeModal(): void {
    $('#recipientsModal').modal('hide');
    this.onClose.emit(true);
  }
}
