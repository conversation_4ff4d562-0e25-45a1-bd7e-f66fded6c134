import { TestBed, ComponentFixture } from '@angular/core/testing';
import { ResendDocumentFormModalComponent } from './resend-document-form-modal.component';
import { StructureService } from '../../structure.service';
import { ToolTipService } from '../../tool-tip.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { FormsModule } from '@angular/forms';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpModule } from '@angular/http';
import { ApolloModule } from 'apollo-angular';
import { NgxLoggerLevel, LoggerModule } from 'ngx-logger';
import { AuthService } from 'app/structure/shared/auth.service';
import { SharedService } from '../sharedServices';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { <PERSON>tt<PERSON><PERSON><PERSON><PERSON>, HttpClient } from '@angular/common/http';
import { HttpService } from 'app/services/http/http.service';
import { testRecipientData } from 'test-data';
import 'rxjs/add/observable/from';
import { Observable } from 'rxjs';
import { By } from '@angular/platform-browser';
import { CONSTANTS } from 'app/constants/constants';
import { provideClients } from 'test-utils';

describe('ResendDocumentFormComponent', () => {
  let component: ResendDocumentFormModalComponent;
  let fixture: ComponentFixture<ResendDocumentFormModalComponent>;

  const spyStructureService = jasmine.createSpyObj('StructureService', [
    'getUserDetails',
    'getCookie',
    'showAlertMessagePopup',
  ]);

  const spyHttpService = jasmine.createSpyObj('HttpService', ['doGet']);

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ResendDocumentFormModalComponent],
      providers: [
        { provide: StructureService, useValue: spyStructureService },
        ToolTipService,
        TranslateService,
        WorklistIndexdbService,
        AuthService,
        SharedService,
        HttpHandler,
        HttpClient,
        { provide: HttpService, useValue: spyHttpService },
      ],
      imports: [
        TranslateModule.forRoot(),
        HttpModule,
        FormsModule,
        RouterTestingModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF,
        }),
      ],
    }).compileComponents();
  });

  beforeEach(() => {
    spyStructureService.userDetails = JSON.stringify({ tenantId: 90 });
    spyStructureService.getCookie.and.callFake((id) => {
      if (id === 'crossTenantId') {
        return 100;
      } else if (id === 'tenantId') {
        return 90;
      }
      return '';
    });
    fixture = TestBed.createComponent(ResendDocumentFormModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('It should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('It should show the recipients on input() changes', async () => {
    const testData = testRecipientData();
    spyHttpService.doGet.and.returnValue(Observable.of(testData));
    component.showModal = true;
    component.patientId = '10001';
    component.entityId = '100';
    component.getRecipientsFor = 'Forms';

    component.ngOnChanges();

    fixture.whenStable().then(() => {
      expect(component.loaderFlag).toBe(false);

      expect(component.recipientList).toEqual([
        {
          userId: '2598963',
          displayName: 'Caregiver Patient',
          selected: false,
        },
        {
          userId: '2599699',
          displayName: 'demo',
          selected: false,
        },
        {
          userId: '2599700',
          displayName: 'Demo Branch 2 alter (sister)',
          selected: true,
        },
        {
          userId: '2599701',
          displayName: 'Test Branch 2 alternate (father)',
          selected: true,
        },
      ]);

      expect(component.selectedRecipientsList).toEqual([
        {
          userId: '2599700',
          displayName: 'Demo Branch 2 alter (sister)',
          selected: true,
        },
        {
          userId: '2599701',
          displayName: 'Test Branch 2 alternate (father)',
          selected: true,
        },
      ]);
    });

    const modal = fixture.debugElement.queryAll(By.css('#recipientsModal'));
    expect(modal).toBeTruthy();
  });

  it('It should emit the selected users on confirm', () => {
    spyStructureService.showAlertMessagePopup.and.returnValue(
      Promise.resolve(true)
    );
    component.selectedRecipientsList = [
      {
        userId: '2599700',
        displayName: 'Demo Branch 2 alter (sister)',
        selected: true,
      },
      {
        userId: '2599701',
        displayName: 'Test Branch 2 alternate (father)',
        selected: true,
      },
    ];
    component.getRecipientsFor = CONSTANTS.contentType.forms;
    component.confirmToSend();

    let emittedValue = [];
    let isModalClosed: boolean;

    component.eventEmitterSelectedRecipients.subscribe((item) => {
      emittedValue = item;
    });

    component.onClose.subscribe((item) => {
      isModalClosed = item;
    });

    component.eventEmitterSelectedRecipients.emit(
      component.selectedRecipientsList
    );
    expect(emittedValue).toEqual(component.selectedRecipientsList);

    component.onClose.emit(true);
    expect(isModalClosed).toBe(true);
  });

  it('It should close the modal', () => {
    const modalElement = document.createElement('div');
    modalElement.id = 'recipientsModal';
    modalElement.setAttribute('hidden', 'false');
    document.body.appendChild(modalElement);

    spyOn(component.onClose, 'emit');

    component.closeModal();

    expect(modalElement.hasAttribute('hidden')).toBe(true);

    expect(component.onClose.emit).toHaveBeenCalledWith(true);
  });

  it('It should push and pop the selected values on checkbox change', () => {
    component.selectedRecipientsList = [
      {
        userId: '2599701',
        displayName: 'Test Branch 2 alternate (father)',
        selected: true,
      },
    ];

    let user = {
      userId: '2599700',
      displayName: 'Demo Branch 2 alter (sister)',
      selected: true,
    };

    component.onRecipientsChange(user);
    expect(component.selectedRecipientsList).toContain(user);

    user = {
      userId: '2599701',
      displayName: 'Test Branch 2 alternate (father)',
      selected: false,
    };

    component.onRecipientsChange(user);
    expect(component.selectedRecipientsList).not.toContain(user);
  });

  it('It should select all recipients', () => {
    component.recipientList = [
      {
        userId: '2598963',
        displayName: 'Caregiver Patient',
        selected: false,
      },
      {
        userId: '2599699',
        displayName: 'demo',
        selected: false,
      },
      {
        userId: '2599700',
        displayName: 'Demo Branch 2 alter (sister)',
        selected: false,
      },
      {
        userId: '2599701',
        displayName: 'Test Branch 2 alternate (father)',
        selected: false,
      },
    ];

    component.selectAll();

    expect(component.selectedRecipientsList).toEqual([
      {
        userId: '2598963',
        displayName: 'Caregiver Patient',
        selected: true,
      },
      {
        userId: '2599699',
        displayName: 'demo',
        selected: true,
      },
      {
        userId: '2599700',
        displayName: 'Demo Branch 2 alter (sister)',
        selected: true,
      },
      {
        userId: '2599701',
        displayName: 'Test Branch 2 alternate (father)',
        selected: true,
      },
    ]);
  });

  it('It should unselect all recipients', () => {
    component.recipientList = [
      {
        userId: '2598963',
        displayName: 'Caregiver Patient',
        selected: true,
      },
      {
        userId: '2599699',
        displayName: 'demo',
        selected: true,
      },
      {
        userId: '2599700',
        displayName: 'Demo Branch 2 alter (sister)',
        selected: true,
      },
      {
        userId: '2599701',
        displayName: 'Test Branch 2 alternate (father)',
        selected: true,
      },
    ];

    component.clearAll();

    expect(component.recipientList).toEqual([
      {
        userId: '2598963',
        displayName: 'Caregiver Patient',
        selected: false,
      },
      {
        userId: '2599699',
        displayName: 'demo',
        selected: false,
      },
      {
        userId: '2599700',
        displayName: 'Demo Branch 2 alter (sister)',
        selected: false,
      },
      {
        userId: '2599701',
        displayName: 'Test Branch 2 alternate (father)',
        selected: false,
      },
    ]);

    expect(component.selectedRecipientsList).toEqual([]);
  });

  it('It should return the user Id ', () => {
    const userId = component.trackByUserId(0, {
      userId: '2598963',
      displayName: 'Caregiver Patient',
      selected: false,
    });
    expect(userId).toEqual('2598963');
  });
 
  it('It should emit onClose on user clicks on the recipients Modal', () => {
    //spy on onClose event emit method
    spyOn(component.onClose, 'emit');

    //Create a mock even target with id as recipientModal
    const mockEventTarget = { id: 'recipientsModal' };

    //Mock event click
    component.onClick(mockEventTarget);

    //Expect the onClose event called with true
    expect(component.onClose.emit).toHaveBeenCalledWith(true);
  });

  it('It should not emit onClose on user clicks on the any other area', () => {
    //spy on onClose event emit method
    spyOn(component.onClose, 'emit');

    //Create a mock even target with id as recipientModal
    const mockEventTarget = { id: '' };

    //Mock event click
    component.onClick(mockEventTarget);

    //Expect the onClose event should not be called
    expect(component.onClose.emit).not.toHaveBeenCalledWith();
  });
});
