<div class="modal fade bd-example-modal-lg forward-modal" role="dialog" aria-labelledby="myLargeModalLabel"
    aria-hidden="true" id="recipientsModal">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title">{{'TITLES.CHOOSE_RECIPIENTS' | translate}}</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="closeModal()">
                    <i aria-hidden="true">&times;</i>
                </button>
            </div>
            <div class="modal-body">
                <div *ngIf="recipientList && recipientList.length">
                    <ul class="treeview treeview-section-ui">
                        <li class="forward-model-option-user-list"
                            *ngFor="let recipient of recipientList; trackBy: trackByUserId">
                            <input type="checkbox" name="recipients{{recipient.userId}}" id="{{recipient.userId}}"
                                value="{{recipient.userId}}" [(ngModel)]="recipient.selected"
                                (ngModelChange)="onRecipientsChange(recipient)" (ngModelChange)="(recipient)">
                            <label class="custom-checkbox" [ngClass]="{'custom-checked': recipient.selected,
                            'custom-unchecked': !recipient.selected}">{{recipient.displayName}}</label>
                        </li>
                    </ul>
                </div>
                <div *ngIf="loaderFlag" class="loading-container">
                    <div class="lds-roller">
                        <!-- Empty div elements are given for CSS transition -->
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                        <div></div>
                    </div>
                    <div class="loading-text">{{'MESSAGES.LOADING_DATA' | translate}}</div>
                </div>
                <div *ngIf="!loaderFlag && (!recipientList || !recipientList.length)">
                    <div class="no-data">{{'MESSAGES.NO_RECIPIENTS' | translate}}</div>
                </div>
            </div>
            <div class="modal-footer">
                <div class="col-md-6">
                    <button class="btn btn-secondary" (click)="clearAll()">
                        {{'BUTTONS.CLEAR_ALL' | translate}}
                    </button>
                    <button class="btn btn-secondary" (click)="selectAll()">
                        {{'BUTTONS.SELECT_ALL' | translate}}
                    </button>
                </div>
                <div class="col-md-6">
                    <button class="btn btn-default float-right" (click)="closeModal()">{{'BUTTONS.CANCEL' |
                        translate}}
                    </button>
                    <button class="btn btn-primary float-right btn-send"
                        *ngIf="selectedRecipientsList && selectedRecipientsList.length"
                        (click)="confirmToSend()">{{'BUTTONS.SEND' | translate}}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>