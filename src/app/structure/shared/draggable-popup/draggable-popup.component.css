.header-background {
  background-color: #1d6372;
  min-height: 50px;
  padding: 20px;
  color: white;
  font-size: large;
  cursor: pointer;
  border-bottom: 1px solid #1d6372;
}
.header-background .close-button-color {
  font-size: 18px;
}
.modal-footer-border {
  border: 1px solid #1d6372;
  border-top: 0px solid #1d6372;
}
.modal-body-border {
  border: 1px solid #1d6372;
  border-top: 0px solid #1d6372;
  border-bottom: 0px solid #1d6372;
}
.send-fly-button {
  padding-right: 2%;
  cursor: pointer;
}
.send-fly-button i {
  font-size: 20px;
  color: #1d6372;
}
.close-button-color {
  color: white;
}
.adjustable-textarea-chat {
  overflow: hidden;
  overflow-wrap: break-word;
  resize: none;
  height: 56px;
}

.chat__apps__messaging {
    overflow-x: hidden;
    height: 200px;
}
.cat__apps__chat-block {
    height: 100px;
}
.cat__apps__messaging__tab__text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
}