<div class="modal-header header-background">
    <span>{{title}}</span>
    <button type="button" class="close close-button-color" (click)="close()" title="{{ 'TOOLTIPS.CLICK_CLOSE' | translate }}" aria-hidden="true">
        X
    </button>
</div>

<div class="modal-body modal-body-border">
    <div class="card-block">
        <div class="chat__apps__messaging">
            <div class="custom-scroll chat__core__scrollable" id="chat-window-scroll">
                <div class="cat__apps__chat-block">
                    <div class="cat__apps__chat-block__item clearfix"
                        *ngFor="let mDetails of newMessagedetails;let i=index;"
                        [ngClass]="{'cat__apps__chat-block__item--right': mDetails.name!='Me','citus-notification':mDetails.name=='Administrator'}">
                        <div class="cat__apps__chat-block__avatar avatar-position">
                            <a class="cat__core__avatar cat__core__avatar--50" href="javascript:void(0);">
                                <img src="{{mDetails.avatar}}" alt="{{title}}" outOfOfficeStatus [oooInfo]="getOooInfo(mDetails.userid)" 
                                [customClass]="'visit-avatar-circle-badge'"
                                onerror="this.src='assets/img/file-404.png'">
                            </a>
                        </div>

                        <div class="cat__apps__chat-block__content message-content message-content-{{mDetails.id}}">
                            <small class="cat__apps__messaging__tab__time">{{mDetails.time*1000 |
                                dateshortyearFilter}}</small>
                            <div class="cat__apps__messaging__tab__name">{{mDetails.name}}</div>
                            <div class="cat__apps__messaging__tab__text chat-message-container">
                                <span *ngIf="mDetails.messageStatus === 1; else messageHistory" compile="{{mDetails.msg | autolinker}}"></span>
                                <ng-template #messageHistory>
                                    <ng-container *ngIf="mDetails.deleteUndoHistory.length > 1; else singleMessageHistory">
                                        <ng-container *ngFor="let msg of mDetails.deleteUndoHistory; let i = index;">
                                            <span class="font-italic">{{mDetails.deleteUndoHistory.length - i}}. {{ msg.actionHistory }}</span><br>
                                        </ng-container>
                                    </ng-container>
                                    <ng-template #singleMessageHistory>
                                        <span class="font-italic">{{ mDetails.deleteUndoHistory[0].actionHistory }}</span>
                                    </ng-template>                    
                                </ng-template>
                            </div>
                        </div>
                    </div>
                    <div id="chat-bottom"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="modal-footer modal-footer-border">
    <textarea class="textarea form-control adjustable-textarea-chat" [(ngModel)]="typeArea" id="typeArea" xssInputValidate=""
        placeholder="{{ 'PLACEHOLDERS.TYPE_HERE' | translate }}"></textarea>
    <span *ngIf="typeArea" class="pull-right send-fly-button" (click)="sendData()"><i class="fa fa-send mr-2" title="{{ 'TOOLTIPS.SEND_MESSAGE' | translate }}"></i>
    </span>
</div>
