import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { messageCenterGUID } from 'app/utils/utils';
import { CONSTANTS } from 'app/constants/constants';
import { SharedService } from '../sharedServices';
import { StructureService } from './../../../structure/structure.service';
import { ChatService } from './../../../structure/inbox/chat.service';
import { InboxService } from './../../../structure/inbox/inbox.service';
import { ToolTipService } from '../../tool-tip.service';
import { MessageService } from 'app/services/message-center/message.service';

declare const $: any;
@Component({
  selector: 'app-draggable-popup',
  templateUrl: './draggable-popup.component.html',
  styleUrls: ['./draggable-popup.component.css'],
})
export class DraggablePop<PERSON><PERSON><PERSON>ponent implements <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>roy {
  newMessagedetails = [];
  visitDetails: any;
  userData: any;
  messageFilter: any = '1';
  activeMessage: any;
  typing = false;
  userType: Function;
  timeoutFunction: Function;
  timeout: any = undefined;
  visitChatDetails: any;
  title: string;
  typeArea = '';

  constructor(
    public structureService: StructureService,
    private activeModal: NgbActiveModal,
    private _sharedService: SharedService,
    private _structureService: StructureService,
    private _ChatService: ChatService,
    private _inboxService: InboxService,
    private messageService: MessageService,
    private _ToolTipService: ToolTipService,
    renderer: Renderer,
    elementRef: ElementRef
  ) {
    renderer.listen(elementRef.nativeElement, 'keyup', (event) => {
      if (this.typeArea) {
        this.userType(event);
      }
    });
  }

  ngOnInit() {
    this.visitDetails = this._sharedService.activeVisitChat;
    this.activeMessage = this._sharedService.visitChatDetails.activeMessages;
    this.visitChatDetails = this._sharedService.visitChatDetails.data;
    this.userData = JSON.parse(this._structureService.userDetails);
    this.chatRoomSocketFunctions();
    this.getChatRoomMessages();
    $(document).ready(function () {
      const modalContent: any = $('.modal-content');
      modalContent.draggable({
        handle: '.modal-header',
      });
    });
    const self = this;
    this.timeoutFunction = function () {
      self.typing = false;
      self._structureService.socket.emit('typing', false);
    };
    this.userType = function (e) {
      const code = e.keyCode ? e.keyCode : e.which;
      if (code !== 13) {
        if (this.typing === false || this.typeArea !== '') {
          this.typing = true;
          this._structureService.socket.emit('typing', 'typing...');
          clearTimeout(this.timeout);
          this.timeout = setTimeout(this.timeoutFunction, 2000);
        } else {
          if (this.typing) {
            clearTimeout(this.timeout);
            this.timeout = setTimeout(this.timeoutFunction, 2000);
          }
        }
      }
    };
  }
  ngAfterViewInit() {
    this.scrollBottom();
  }
  chatRoomSocketFunctions(): void {
    this._structureService.socket.emit(
      'joinToChatroom',
      {
        user: this.userData.userId,
        room: this.visitChatDetails.chatroomId,
        name: this.userData.displayName,
        tenantId: this.userData.tenantId,
        tenantKey: this.userData.tenantKey,
        tenantName: this.userData.tenantName,
        sites: this.userData.mySites,
        avatar: this.userData.profileImageThumbUrl,
        citusRole: this.userData.group,
        patientReminderCheckingType:
          this.userData.config.patient_reminder_checking_type,
        eventSource: JSON.stringify({ environment: 'Desktop', component: CONSTANTS.joinChatroomEventSourceComponent.scheduleCenterChat})
      },
      function (data) {}
    );
    this._structureService.socket
      .off('userMessage')
      .on('userMessage', (msg, user, images, messageId) => {
        window.localStorage.setItem('chUserMessages', '');
        this.sendUserMessages(msg, user);
      });
    this._structureService.socket
      .off('userTyping')
      .on('userTyping', (data) => {});
    this._structureService.socket
      .off('updateUserMessage')
      .on('updateUserMessage', (msg) => {
      let chatroom_id = '';
        this.newMessagedetails.forEach((key) => {
          if (msg.unique === msg.unsendId) {
            if (key.insertionStatus) {
              key.insertionStatus = 0;
            }
          }
          if (msg.id == null) {
          key.failed = true;
        }
        if (key.id === msg.unique) {
          key.id = msg.id;
          key.info_available = 1;
          if (key.name !== 'Me') {
            chatroom_id = key.chatRoomId;
          }
        }
      });
      if(chatroom_id) {
        this.updateDeliveryStatus(chatroom_id);
      }
    });
    this._structureService.subscribeSocketEvent('updateChatMessageDeleteStatus').subscribe((data) => {
      if (+this.visitChatDetails.chatroomId === data.chatroomId && !data.isMaskedChatThread) {
        this._ChatService.getChatroomMessages(data.chatroomId, false, 0, 0, '', '1', 0, 0, [], [], '', data.messageId, true, true).then((response) => {
          const chatMessageResponse = JSON.parse(JSON.stringify(response));
          let updatedMessage = chatMessageResponse.content.find(message => +message.id === data.messageId);
          updatedMessage.msg = updatedMessage.message;
          const index = this.newMessagedetails.findIndex(message => +message.id === updatedMessage.id);
          if (index > -1) {
              this.newMessagedetails = this.newMessagedetails.map((message, i) => {
                  if (i === index) {
                      return { ...message, ...updatedMessage};
                  }
                  return message;
              });
              this.newMessagedetails = this.messageService.handleMessageDeleteUndoHistoryData(this.newMessagedetails);
          }
        });
      } 
    });
  }
  updateDeliveryStatus(chatroom_id: string): void {
    const deliverySqlData = {
      user_id: this.userData.userId,
      chatroom_id,
      delivery_time: new Date().getTime() / 1000,
      read_time: new Date().getTime() / 1000
    };
    this._inboxService.updateDeliveryStatus(deliverySqlData);
  }
  getChatRoomMessages(): void {
    let showChatHistory = false;
    if (
      this.userData.config.show_chat_history_to_new_participant &&
      this.userData.config.show_chat_history_to_new_participant == '1'
    ) {
      showChatHistory = true;
    }
    if (
      this.userData.config.default_category_of_message_display !== '' &&
      this.userData.config.default_category_of_message_display !== 'undefined'
    ) {
      this.messageFilter =
        this.userData.config.default_category_of_message_display;
    }
    this._ChatService
      .getChatroomMessages(
        this.visitChatDetails.chatroomId,
        showChatHistory,
        0,
        0,
        '',
        this.messageFilter,
        0,
        0,
        [],
        [],
        '',
        0,
        false,
        true,
      )
      .then((data) => {
        this.populateMessageList(data);
        if(this.newMessagedetails.length) {
          this.newMessagedetails = this.messageService.handleMessageDeleteUndoHistoryData(this.newMessagedetails);
        }
      });
  }
  populateMessageList(message): void {
    this.title = message.title.replace('Chat With', 'Chat with');
    const messages = message.content;
    if (messages && messages.length) {
      const chatRoomId = this.activeMessage.data.message.chatroomId;
      this.updateDeliveryStatus(chatRoomId);
      if (this.structureService.inboxDataFirstPage && chatRoomId) {
        let activeMessageIndex = -1;
        if (this.structureService.inboxDataFirstPage) {
          activeMessageIndex = this.structureService.inboxDataFirstPage.findIndex((item) => +item.chatroomId === +chatRoomId);
        }
        if (activeMessageIndex > -1) {
          this._sharedService.readInboxData.emit(this.structureService.inboxDataFirstPage[activeMessageIndex]);
        }
      }
      messages.forEach((key) => {
        const value = key;
        let userName,
          userClass,
          owner = false,
          senderTime,
          insertionStatus = 0,
          sendOnTime;
        if (value.userid === parseInt(this.userData.userId)) {
          userName = 'Me';
          userClass = 'self';
          if (value.sender_time) {
            senderTime = value.sender_time;
            value.sent = senderTime;
          }
          if (value.insertionStatus) {
            insertionStatus = value.insertionStatus;
          }
          owner = true;
        } else {
          if (value.sender_time) {
            sendOnTime = value.sender_time;
          } else {
            sendOnTime = value.sent;
          }
          userName = value.message_sender_assoc_userid
            ? value.message_sender_assoc_displayname +
              ' (' +
              value.displayName +
              ')'
            : value.displayName;
          userClass = 'other';
        }
        let getitems = '';
        if (value.tag) {
          getitems = value.tagedItems;
        }
        const sign =
          value.sign !== 'false'
            ? this._structureService.apiBaseUrl +
              CONSTANTS.fileUploadsUrl +
              value.sign
            : false;
        let readUsers = [];
        let readUsersLength = 0;
        if (value.userid === this.userData.userId) {
          readUsers = value.readUsers;
          if (readUsers !== null) {
            for (let ind = 0; ind < readUsers.length; ind++) {
              if (readUsers[ind].userid === this.userData.userId) {
                readUsers.splice(ind, 1);
              }
            }
            readUsersLength = readUsers.length;
          }
        }
        const details = {
          id: value.id,
          name: userName,
          class: userClass,
          avatar: value.avatar,
          msg: value.message,
          message: value.message,
          time: value.sent,
          userid: value.userid,
          sign: sign,
          roleId: value.roleId,
          owner: owner,
          tag: value.tag,
          tagSign: value.tagSign,
          readUsers: readUsers,
          readUsers_length: readUsersLength,
          patientFirstName: value.pfirstname,
          patientLastName: value.plastname,
          patientDob: value.pdob,
          patientDisplayName: value.pdisplayname,
          patientCaregiverDisplayName: value.pcaregiver_displayname,
          passwordStatus: value.passwordStatus,
          patient: value.patient,
          getitems: getitems,
          msg_flag: value.msg_flag,
          prev_msg_flag: value.prev_msg_flag,
          msg_flag_data_id: value.msg_flag_data_id,
          language: value.language,
          insertionStatus: insertionStatus,
          deliveredUsers: [],
          msg_sent: sendOnTime,
          messageStatus: value.messageStatus,
          deleteUndoHistory: value.deleteUndoHistory,
          chatRoomId: this.visitChatDetails.chatroomId,
          info_available: value.info_available,
          failed: value.failed,
        };
        this.newMessagedetails.unshift(details);
      });
    }
    document.getElementById('chat-bottom').scrollIntoView({ behavior: 'smooth' });
  }
  sendData(): void {
    const messageText = this.typeArea.replace(CONSTANTS.textMessageSpace, '<br />');
    const sent = new Date().getTime() / 1000;
    let userMessagetoServerData: { [k: string]: any } = {};
    if (messageText !== '') {
      userMessagetoServerData = {
        id: messageCenterGUID( this.visitChatDetails.chatroomId, this.userData.userId ),
        data: messageText,
        insert: true,
        userId: this.userData.userId,
        chatroomId: this.visitChatDetails.chatroomId,
        displayName: this.userData.displayName,
        sentTimeCheck: new Date().getTime() / 1000,
        baseId: 0,
        messageType: '',
        doubleVerificationStatus: 0,
        tenantId: this.userData.tenantId,
        tenantName: this.userData.tenantName,
        language: navigator.language,
        sender_time: parseInt(String(new Date().getTime() / 1000)),
        insertionStatus: 1,
        failed: false,
      };
      userMessagetoServerData.environment = this._structureService.environment;
      userMessagetoServerData.tenantId = this.userData.tenantId;
      userMessagetoServerData.serverBaseUrl =
        this._structureService.serverBaseUrl;
      userMessagetoServerData.apiVersion = this._structureService.version;
      if (
        this.userData.privileges.indexOf('messageEscalation') !== -1 ||
        this.userData.privileges.indexOf('messageReminder') !== -1
      ) {
        userMessagetoServerData.scheduleData =
          this.userData.escalatedSchedulerData;
        userMessagetoServerData.scheduleInterval = 30;

        if (
          this.userData.master_details &&
          this.userData.masterEnabled === '1'
        ) {
          userMessagetoServerData.masterEscalationTime =
            this.userData.master_config.escalation_time * 1;
          userMessagetoServerData.masterScheduleData =
            this.userData.masterEscalatedSchedulerData;
          userMessagetoServerData.masterScheduleInterval = 30;
        }
      }
      userMessagetoServerData.workingHour =
        this._inboxService.checkInfusionHours(false).isWorkingHours;
      userMessagetoServerData.caregiverDisplayname =
        this.userData.caregiver_displayname;
      userMessagetoServerData.caregiverUserid = this.userData.caregiver_userid;
      if (this.userData.master_details && this.userData.masterEnabled === '1') {
        userMessagetoServerData.masterEnabled = 1;
        userMessagetoServerData.isMaster = 0;
        userMessagetoServerData.masterTenantId =
          this.userData.master_details.id;
      } else if (
        !this.userData.master_details &&
        this.userData.masterEnabled === '1'
      ) {
        userMessagetoServerData.masterEnabled = 1;
        userMessagetoServerData.isMaster = 1;
      } else if (this.userData.masterEnabled === '0') {
        userMessagetoServerData.masterEnabled = 0;
        userMessagetoServerData.isMaster = 0;
      }
      if (
        this.userData.masterEnabled === '1' &&
        this.userData.config
          .flex_site_patients_can_chat_with_internal_staffs === '1' &&
        this.userData.isMaster === '0'
      ) {
        userMessagetoServerData.includeMasterStaffs = 1;
      } else {
        userMessagetoServerData.includeMasterStaffs = 0;
      }
      userMessagetoServerData.messageConfig = {
        messageEscalation:
          this.userData.privileges.indexOf('messageEscalation') !== -1,
        messageReminder:
          this.userData.privileges.indexOf('messageReminder') !== -1,
        userCitusRole: this.userData.group,
      };
      if (this.userData.master_details && this.userData.masterEnabled === '1') {
        userMessagetoServerData.masterWorkingHour =
          this._inboxService.checkMasterBranchHours().isWorkingHours;
        userMessagetoServerData.masterAllow24HourWorking = this.userData
          .master_config.working_hour
          ? this.userData.master_config.working_hour
          : 0;
        userMessagetoServerData.masterStaffSmsNotificationEnabled = this
          .userData.master_config.staff_message_sms_notifcation
          ? this.userData.master_config.staff_message_sms_notifcation
          : 0;
        userMessagetoServerData.masterMessageConfig = {
          messageEscalation:
            this.userData.privileges.indexOf('messageEscalation') !== -1,
          messageReminder:
            this.userData.privileges.indexOf('messageReminder') !== -1,
          userCitusRole: this.userData.group,
        };
      }
      const anyUserMessageExists = this.newMessagedetails.some(function (
        messageDetails
      ) {
        return messageDetails.userid !== 0;
      });
      const args: any = {
        type: 'messages',
        setCount: 2,
        chatroomId: this.visitChatDetails.chatroomId,
        message: messageText,
        sentTime: userMessagetoServerData.sentTimeCheck.toString(),
      };
      if (!anyUserMessageExists) {
        args.firstUserMessageFromChatroom = true;
        userMessagetoServerData.firstUserMessageFromChatroom = true;
      } else {
        args.selfUpdate = true;
      }
      const pushData: any = {
        chatRoomOrToId: this.visitChatDetails.chatroomId,
        userId: this.userData.userId,
        message: this._ToolTipService.getTranslateData(
          'MESSAGES.MESSAGE_TO_REVIEW'
        ),
        environment: this._structureService.getEnvironment(),
        tenantId: '',
        pushDeepLink: '',
        citusRoleId: '',
        privilegeKey: '',
        pushNotificationUpdated: true,
      };
      if (
        this._inboxService.checkInfusionHours(true).isWorkingHours ===
          true ||
        this.userData.group !== '3'
      ) {
        userMessagetoServerData.pushData = pushData;
      }
      this._sharedService.messagePollingSelfUpdate.emit({
        data: 'Polling:true',
        args: args,
      });
      userMessagetoServerData.sent = sent;
      userMessagetoServerData.language = navigator.language;
      if (!window.localStorage.getItem('chUserMessages')) {
        const userDatas = {
          Messages: [userMessagetoServerData],
        };
        window.localStorage.setItem(
          'chUserMessages',
          JSON.stringify(userDatas)
        );
      } else {
        let jsonS = window.localStorage.getItem('chUserMessages');
        const obj = JSON.parse(jsonS);
        obj['Messages'].push(userMessagetoServerData);
        jsonS = JSON.stringify(obj);
        window.localStorage.setItem('chUserMessages', jsonS);
      }
    }
    this.sendUserMessages(
      userMessagetoServerData,
      userMessagetoServerData.displayName
    );
    if (this._structureService.socket.connected) {
      this._structureService.socket.emit(
        'userMessagetoServer',
        userMessagetoServerData,
        (errorData) => {}
      );
    }
    this.typeArea = '';
  }
  sendUserMessages(msg, user): void {
    let userName,
      userClass,
      owner = false,
      language = '',
      insertionStatus = 0,
      time;
    const sign = false,
      isPdf = false,
      image = false;
    if (msg.userId === this.userData.userId) {
      userName = 'Me';
      userClass = 'self';
      owner = true;
      msg.avatar = this.userData.profileImageThumbUrl;
      language = navigator.language;
    } else {
      userName = msg.caregiverUserid
        ? msg.caregiverDisplayname + ' (' + user + ')'
        : user;
      userClass = 'other';
      language = msg.language;
    }
    if (msg.time) {
      time = msg.time;
    }
    if (msg.sender_time) {
      time = msg.sender_time;
    }
    if (msg.insertionStatus) {
      insertionStatus = msg.insertionStatus;
    }
    const details = {
      id: msg.id,
      name: userName,
      class: userClass,
      avatar: msg.avatar,
      msg: msg.data,
      message: msg.data,
      time: time,
      isImage: image,
      isPdf: isPdf,
      sign: sign,
      owner: owner,
      userid: msg.userId,
      tag: 'false',
      tagSign: 'false',
      readUsers: [],
      language: language,
      msg_flag: 0,
      prev_msg_flag: 0,
      msg_flag_data_id: null,
      insertionStatus: insertionStatus,
      deliveredUsers: [],
      msg_sent: msg.time,
      messageStatus: 1,
      chatRoomId: this.visitChatDetails.chatroomId,
      info_available: msg.info_available,
      failed: msg.failed,
    };
    if (msg.id !== msg.uniqueId) {
      this.newMessagedetails.push(details);
    } else if (msg.userId !== this.userData.userId) {
      this.newMessagedetails.push(details);
    }
    this.scrollBottom();
  
  }
  scrollBottom() {
    setTimeout(()=>{
        document.getElementById('chat-bottom').scrollIntoView({ behavior: 'smooth' });
      }, 1000);
  }
  close() {
    this.activeModal.close();
    this._structureService.socket.emit(
      'leaveChatRoom',
      this.visitChatDetails.chatroomId
    );
  }
  ngOnDestroy() {
    this._sharedService.visitChatDetails = [];
  }
  getOooInfo(userId) {
    const userData = JSON.parse(this._structureService.userDetails);
    return +userId === +userData.userId ? userData.oooInfo : null;
  }
}
