import { NO_ERRORS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { DraggablePopupComponent } from './draggable-popup.component';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SharedService } from '../sharedServices';
import { StructureService } from 'app/structure/structure.service';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpModule } from '@angular/http';
import { ApolloModule } from 'apollo-angular';
import { AuthService } from '../auth.service';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { ChatService } from 'app/structure/inbox/chat.service';
import { InboxService } from 'app/structure/inbox/inbox.service';
import { dateshortyearFilterPipe } from 'app/structure/inbox/inbox-filter.pipes';
import { provideClients } from 'test-utils';
import { AutolinkerPipe } from '../pipes/autolinker/autolinker.pipe';
import { MessageService } from 'app/services/message-center/message.service';

describe('DraggablePopupComponent', () => {
  let component: DraggablePopupComponent;
  let fixture: ComponentFixture<DraggablePopupComponent>;
  let chatservice: ChatService;
  let inboxservice: InboxService;
  let messageservice: MessageService;
  let sharedservice: SharedService;
  let structservice: StructureService;
  let structureServiceMock: any;
  beforeEach(async(() => {
    structureServiceMock = {
      userDetails: JSON.stringify({
        userId: 1,
        oooInfo: 'Out of Office',
      }),
    };
    TestBed.configureTestingModule({
      providers: [
        TranslateService,
        NgbActiveModal,
        { provide: SharedService, useValue: sharedservice },
        { provide: StructureService, useValue: structureServiceMock },
        AuthService,
        WorklistIndexdbService,
        ToolTipService,
        { provide: ChatService, useValue: chatservice },
        { provide: InboxService, useValue: inboxservice },
        { provide: MessageService, useValue: messageservice }
      ],
      declarations: [DraggablePopupComponent, dateshortyearFilterPipe, AutolinkerPipe],
      imports: [
        TranslateModule.forRoot(),
        RouterTestingModule,
        HttpModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF
        })
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DraggablePopupComponent);
    component = fixture.componentInstance;
    sharedservice = TestBed.get(SharedService);
    chatservice = TestBed.get(ChatService);
    messageservice = TestBed.get(MessageService);
    inboxservice = TestBed.get(InboxService);

    component.visitDetails = {};
    component.activeMessage = {};
    component.visitChatDetails = {};
    component.userData = {
      userId: 1,
      displayName: 'Test User',
      tenantId: 1,
      tenantKey: 'testkey',
      tenantName: 'Test Tenant',
      mySites: []
    };
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  describe('getOooInfo', () => {
    it('should return oooInfo if userId matches', () => {
      const result = component.getOooInfo(1);
      expect(result).toBe('Out of Office');
    });

    it('should return null if userId does not match', () => {
      const result = component.getOooInfo(2);
      expect(result).toBeNull();
    });
  });
});
