/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA, DebugElement } from '@angular/core';
import { HttpService } from 'app/services/http/http.service';
import { StructureService } from 'app/structure/structure.service';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { HttpModule } from '@angular/http';
import { HttpClient } from '@angular/common/http';
import { RouterTestingModule } from '@angular/router/testing';
import { provideClients } from 'test-utils';
import { ApolloModule } from 'apollo-angular';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { TranslateModule } from '@ngx-translate/core';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { PermissionService } from 'app/services/permission/permission.service';
import { CONSTANTS } from 'app/constants/constants';
import { StoreService } from '../storeService';
import { SharedService } from '../sharedServices';
import { AuthService } from '../auth.service';
import { RecipientsComponent } from './recipients.component';

describe('RecipientsComponent', () => {
  let component: RecipientsComponent;
  let fixture: ComponentFixture<RecipientsComponent>;
  let debugElement: DebugElement;
  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [RecipientsComponent],
      providers: [
        HttpService,
        StructureService,
        HttpClient,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        PermissionService,
        StoreService
      ],
      imports: [
        AngularMultiSelectModule,
        HttpModule,
        RouterTestingModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF
        }),
        TranslateModule.forRoot()
      ],
      schemas: [NO_ERRORS_SCHEMA]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(RecipientsComponent);
    component = fixture.componentInstance;
    debugElement = fixture.debugElement;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
  it('should initialize dropdown settings on ngOnInit', () => {
    component.placeholder = 'Select Recipient';
    component.ngOnInit();
    expect(component.dropdownSettings).toEqual({
      text: 'Select Recipient',
      classes: 'send-form-doc-recipients',
      enableSearchFilter: true,
      unSelectAllText: CONSTANTS.clearAll,
      badgeShowLimit: CONSTANTS.badgeCountMultiSelect
    });
  });

  it('should call getRecipientsList on ngOnChanges if patientId is not blank', () => {
    spyOn(component, 'getRecipientsList');
    component.patientId = '123';
    component.ngOnChanges();
    expect(component.getRecipientsList).toHaveBeenCalled();
  });

  it('should not call getRecipientsList on ngOnChanges if patientId is blank', () => {
    spyOn(component, 'getRecipientsList');
    component.patientId = '';
    component.ngOnChanges();
    expect(component.getRecipientsList).not.toHaveBeenCalled();
  });

  it('should emit selected recipients on emitRecipientList', () => {
    spyOn(component.recipients, 'emit');
    component.selectedRecipients = [{ id: '1' }, { id: '2' }];
    component.emitRecipientList();
    expect(component.recipients.emit).toHaveBeenCalledWith('1,2');
  });

  it('should populate recipientList on getRecipientsList success', () => {
    const response = {
      success: true,
      data: {
        caregiver: { userId: '1', displayName: 'Caregiver' },
        patient: { userId: '2', displayName: 'Patient' },
        alternateContacts: [{ userId: '3', displayName: 'Contact' }]
      }
    };
    spyOn(component['httpService'], 'doGet').and.returnValue({
      subscribe: (callback) => callback(response)
    });
    component.getRecipientsList();
    expect(component.recipientList).toEqual([
      { id: '1', itemName: 'Caregiver' },
      { id: '2', itemName: 'Patient' },
      { id: '3', itemName: 'Contact' }
    ]);
  });
});
