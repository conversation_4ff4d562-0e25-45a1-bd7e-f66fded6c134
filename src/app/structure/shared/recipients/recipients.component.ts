import { Component, Input, OnInit, Output, EventEmitter, OnChanges } from '@angular/core';
import { StructureService } from 'app/structure/structure.service';
import { APIs } from 'app/constants/apis';
import { CONSTANTS } from 'app/constants/constants';
import { HttpParams } from '@angular/common/http';
import { isBlank } from 'app/utils/utils';
import { HttpService } from 'app/services/http/http.service';

@Component({
  selector: 'app-recipients',
  templateUrl: './recipients.component.html',
  styleUrls: ['./recipients.component.css']
})
export class RecipientsComponent implements OnInit, OnChanges {
  dropdownSettings;
  recipientList = [];
  selectedRecipients = [];
  @Input() placeholder: string;
  @Input() patientId: string;
  @Input() admissionId: string;
  @Output() readonly recipients: EventEmitter<string> = new EventEmitter<string>();
  constructor(private structureService: StructureService, private httpService: HttpService) {}
  ngOnInit() {
    this.dropdownSettings = {
      text: this.placeholder,
      classes: 'send-form-doc-recipients',
      enableSearchFilter: true,
      unSelectAllText: CONSTANTS.clearAll,
      badgeShowLimit: CONSTANTS.badgeCountMultiSelect,
    };
  }
  ngOnChanges() {
    this.selectedRecipients = [];
    this.recipientList = [];
    if (!isBlank(this.patientId)) {
      this.getRecipientsList();
    }
  }
  getRecipientsList() {
    let tenantId = this.structureService.getCookie('tenantId');
    if (
      this.structureService.getCookie('crossTenantId') &&
      this.structureService.getCookie('crossTenantId') !== 'undefined' &&
      this.structureService.getCookie('tenantId') !== this.structureService.getCookie('crossTenantId')
    ) {
      tenantId = this.structureService.getCookie('crossTenantId');
    }
    this.httpService
      .doGet(APIs.getAlternateContactCaregiverPatient, {
        params: new HttpParams()
          .set('tenantId', tenantId)
          .set('patientId', this.patientId)
          .set('admissionId', this.structureService.isMultiAdmissionsEnabled ? this.admissionId : '')
          .set('isActive', '1')
          .set('chatRoomInvite', 'true')
      })
      .subscribe((response) => {
        if (response.success && response.data) {
          const data = response['data'];
          const existingUserIds = new Set([
            !isBlank(data.careGiver) ? data.careGiver.userId : null,
            !isBlank(data.patient) ? data.patient.userId : null,
          ]);
          
          this.recipientList = [
            (!isBlank(data.caregiver))?
            {
              id: data.caregiver.userId,
              itemName: data.caregiver.displayName
            } : null,
            (!isBlank(data.patient))?
            {
              id: data.patient.userId,
              itemName: data.patient.displayName
            } : null,              
            ...(data.alternateContacts || [])
              .map((contact) => ({
                id: contact.userId,
                itemName: contact.displayName,
              }))
              .filter((contact) => {
                if (existingUserIds.has(contact.id)) {
                  return false;
                }
                existingUserIds.add(contact.id);
                return true;
              }),
          ].filter((item) => !isBlank(item));
        }
      });
  }
  emitRecipientList() {
    const recipients = this.selectedRecipients.map((x) => x.id);
    this.recipients.emit(recipients.toString());
  }
}
