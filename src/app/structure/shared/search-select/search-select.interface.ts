/**
 * This interface contains models for the common search and select component
 */

export enum ScrollType {
  INFINITE = 'infinite',
  VIRTUAL = 'virtual'
}

export interface SearchComponentSettings {
  allowLazyLoading?: boolean;
  lazyLoadType?: ScrollType;
  placeHolderText?: string;
  enableTagging?: boolean;
  fieldName?: string;
  multiSelection?: boolean;
}

export interface Item {
  id: string | number;
  name: string;
  reset?: boolean;
}

export interface ScrollEventData {
  end: number;
}

export const defaultSettings: SearchComponentSettings = {
  allowLazyLoading: false,
  enableTagging: false,
  lazyLoadType: ScrollType.INFINITE,
  multiSelection: false
};

export interface LoadMoreItems {
  searchText: string;
  loadMore: boolean;
  startDate?: string;
  endDate?: string;
}
