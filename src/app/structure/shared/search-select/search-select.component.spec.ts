import { ElementRef, SimpleChanges } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CommonTestingModule } from 'app/utils/common-testing.module';
import { SearchAndSelectComponent } from './search-select.component';
import { Item } from './search-select.interface';

describe('SearchAndSelectComponent', () => {
  let component: SearchAndSelectComponent;
  let fixture: ComponentFixture<SearchAndSelectComponent>;
  const mockElementRef: ElementRef = { nativeElement: document.createElement('div') };
  let mockElement: HTMLElement;
  beforeEach(async () => {
    mockElement = document.createElement('div');
    mockElement.innerHTML = '<button id="btn-load-more">Load more</button>';
    await TestBed.configureTestingModule({
      providers: [{ provide: ElementRef, useValue: mockElementRef }],
      imports: [CommonTestingModule]
    }).compileComponents();
  });
  beforeEach(() => {
    fixture = TestBed.createComponent(SearchAndSelectComponent);
    component = fixture.componentInstance;
  });

  it('It should create the component', () => {
    expect(component).toBeTruthy();
  });
  it('It should hide the dropdown if user clicks on the outside of the dropdown', () => {
    component.showListDrpDwn = true;
    const target = document.createElement('div');
    target.setAttribute('id', 'outer-div');
    spyOn(mockElementRef.nativeElement, 'contains').and.returnValue(false);
    component.onSearchSelectClick(target);
    expect(component.showListDrpDwn).toBe(false);
  });

  it('It should not close the dropdown if user clicks inside of the dropdown', () => {
    component.showListDrpDwn = true;
    const target = document.createElement('div');
    target.setAttribute('id', 'btn-load-more');
    spyOn(mockElementRef.nativeElement, 'contains').and.returnValue(true);
    component.onSearchSelectClick(target);
    expect(component.showListDrpDwn).toBeTruthy();
  });

  it('It should search based on the text entered and show the items list', () => {
    const searchTerm = 'Test';
    spyOn(component.loadMoreItems, 'emit');
    component.searchText = searchTerm;
    component.doSearch();
    expect(component.showListDrpDwn).toBeTruthy();
  });

  it('It should reset the values of dropdown and search text', () => {
    component.resetList();
    expect(component.itemList).toEqual([]);
    expect(component.showListDrpDwn).toBeFalsy();
    expect(component.searchText).toBe('');
    expect(component.showAddNewItem).toBeFalsy();
  });

  it('It should search for the text entered and fetch more results', () => {
    spyOn(component.loadMoreItems, 'emit');
    component.searchText = 'Test';
    component.loadMore();
    expect(component.loadMoreItems.emit).toHaveBeenCalledWith({ searchText: component.searchText, loadMore: true });
  });

  it('It should add the new item into the dropdown and selected as default', () => {
    spyOn(component.selectedItem, 'emit');
    const searchTerm = 'Test';
    const item: Item = { id: '-1', name: searchTerm };
    component.newListItem = searchTerm;
    component.itemList = [];
    component.addNewTag();
    expect(component.itemList).toContain(item);
    expect(component.showAddNewItem).toBeFalsy();
    expect(component.newListItem).toBe(item.name);
    expect(component.showListDrpDwn).toBeFalsy();
    expect(component.selectedItem.emit).toHaveBeenCalledWith(item);
  });

  it('It should reset the dropdown list and selection if the search area is cleared', () => {
    component.searchText = '';
    component.onSearchTextCleared();
    expect(component.showListDrpDwn).toBeFalsy();
    expect(component.selectedItemValue).toBe('');
    expect(component.showAddNewItem).toBeFalsy();
  });

  it('The trackBy method should return the id', () => {
    const id = component.trackById(0, { id: '1234', name: 'Test' });
    expect(id).toEqual('1234');
  });

  it('It should emit the selected item', () => {
    spyOn(component.selectedItem, 'emit');
    const item: Item = { id: '1234', name: 'Test' };
    component.onItemSelect(item);
    expect(component.selectedItemValue).toBe(item.name);
    expect(component.showListDrpDwn).toBeFalsy();
    expect(component.selectedItem.emit).toHaveBeenCalledWith(item);
  });

  it('If itemList value changes, it should show new item tag if no exact match found and show the items in the dropdown', (done) => {
    component.itemList = [
      { id: 101, name: 'Testing Content' },
      { id: 102, name: 'Test Subject' }
    ];
    let changes: SimpleChanges = {
      itemList: {
        currentValue: component.itemList,
        previousValue: {},
        firstChange: true,
        isFirstChange: () => {
          return true;
        }
      }
    };
    component.settings.enableTagging = true;
    component.searchText = 'Tes';
    component.ngOnChanges(changes);
    expect(component.showAddNewItem).toBeTruthy();
    expect(component.loadingItems).toBeFalsy();
    changes = {
      reset: {
        currentValue: true,
        previousValue: false,
        firstChange: true,
        isFirstChange: () => {
          return true;
        }
      }
    };
    component.ngOnChanges(changes);
    setTimeout(() => {
      expect(component.itemList).toEqual([]);
      expect(component.showListDrpDwn).toBeFalsy();
      expect(component.searchText).toBe('');
      expect(component.showAddNewItem).toBeFalsy();
      done();
    });
  });
  it('If itemList value changes it should not show new item tag if exact match found and show the items in the dropdown', () => {
    component.itemList = [
      { id: 101, name: 'Testing Content' },
      { id: 102, name: 'Test Subject' }
    ];
    const changes: SimpleChanges = {
      itemList: {
        currentValue: component.itemList,
        previousValue: {},
        firstChange: true,
        isFirstChange: () => {
          return true;
        }
      }
    };
    component.settings.enableTagging = true;
    component.searchText = 'Test Subject';
    component.ngOnChanges(changes);
    expect(component.showAddNewItem).toBeFalsy();
  });

  it('It should search based on the text entered on EnterKey press', () => {
    spyOn(component.loadMoreItems, 'emit');
    component.searchText = 'Test';
    component.searchOnEnterKeyPress();
    expect(component.loadMoreItems.emit).toHaveBeenCalledWith({ searchText: component.searchText, loadMore: false });
  });
});
