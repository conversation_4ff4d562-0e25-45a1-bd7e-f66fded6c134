<div class="form-group row item-search align-items-baseline">
  <div class="col-md-10" id="div-content">
    <input
      [disabled]="disabled"
      type="text"
      class="form-control"
      [ngClass]="showListDrpDwn ? 'active' : ''"
      autocomplete="off"
      [(ngModel)]="selectedItemValue"
      [ngModelOptions]="{ standalone: true }"
      placeholder="{{ settings.placeHolderText | translate }}"
      (keyup)="onSearchTextCleared()"
      (keydown.enter)="searchOnEnterKeyPress()"
      (click)="showListDrpDwn = true"
      xssInputValidate="{{ settings.fieldName | translate }}"
    />
    <span *ngIf="showResetBtn && !disabled" class="close-icon-btn" (click)="clearSearchText()">&#10006;</span>
    <ng-container *ngIf="itemList.length > 0 || showAddNewItem; else loadingData">
      <div class="relative-position w-100">
        <ul class="item-list-ul" [ngStyle]="{ display: showListDrpDwn ? 'block' : 'none' }">
          <ng-container *ngIf="showAddNewItem; else noData">
            <li class="list-li" (click)="addNewTag()">{{ newListItem }}&nbsp;{{ '(new)' }}</li>
          </ng-container>
          <ng-container *ngIf="itemList && itemList.length">
            <ng-container *ngFor="let item of itemList; trackBy: trackById">
              <li class="list-li no-margin"  [chToolTip]="(disableContactlessUsers && item.noContactAvailable) ? 'noContactUser': ''" [ngClass]="{'grey-text': (item.noContactAvailable && disableContactlessUsers)}" data-animation = "false">
                <div class="row">
                  <div class="item-container col-md-1 hand-pointer" *ngIf="settings.multiSelection">
                    <input  type="checkbox" [disabled]="item.noContactAvailable && disableContactlessUsers" [checked]="isSelected(item)" (change)="toggleSelection(item)" />
                    <span *ngIf="settings.multiSelection" class="checkmark"></span>
                  </div>
                  <div class="item-container hand-pointer" [ngClass]="settings.multiSelection ? 'col-md-10' : 'col-md-11'" (click)="!settings.multiSelection &&  onItemSelect(item) || settings.multiSelection && toggleSelection(item)">
                    <span [ngClass]="item.noContactAvailable && disableContactlessUsers ? 'grey-text' : 'recipient-li-color'">{{ item.name }}</span>
                  </div>
                  <div class="item-container hand-pointer col-md-1" *ngIf="item.subList && item.subList.length > 0">
                    <span (click)="toggleAlternateContacts(item)" class="expand-icon"><i [ngClass]="item.showSubList ? 'fa fa-minus-circle' : 'fa fa-plus-circle'"></i></span>
                  </div>
                </div>
              </li>
              <ul class="contact-ul m-0" *ngIf="item.showSubList">
                <li *ngFor="let subItem of item.subList" class="list-li" [ngClass]="{'grey-text': (subItem.noContactAvailable && disableContactlessUsers)}"  (click)="onItemSelect(item, subItem)" data-animation = "false" [chToolTip]="(disableContactlessUsers && subItem.noContactAvailable) ? 'noContactUser': ''" >
                  <span [ngClass]="{'grey-text': (subItem.noContactAvailable && disableContactlessUsers)}" class="pl-4 hand-pointer row">{{ subItem.name }}</span>
                </li>
              </ul>
            </ng-container>
      
            <li *ngIf="loadingItems" class="load-more-loader">{{ 'MESSAGES.LOAD_MORE_RESULTS' | translate }}</li>
      
            <li class="load-more-li" *ngIf="settings.allowLazyLoading && settings.lazyLoadType === lazyLoadType.INFINITE && !hideLoadMoreBtn">
              <button type="button" class="btn btn-sm btn-default" id="btn-load-more" (click)="loadMore()" [disabled]="loadingItems">
                {{ 'BUTTONS.LOAD_MORE' | translate }}
              </button>
            </li>
          </ng-container>
        </ul>
      </div>
      <div *ngIf="displaySelectedItem">
        <ng-container *ngFor="let item of selectedItems">
          <span class="badge badge-primary">{{ item.name }}</span>
        </ng-container>
      </div>
    </ng-container>
    <ng-container *ngIf="showNoData">
      <ng-template [ngTemplateOutlet]="noData"></ng-template>
    </ng-container>
    <div *ngIf="errorMessage" class="alert alert-danger">
      {{ errorMessage | translate }}
    </div>
  </div>
  <div class="col-md-2 admission-buttons">
    <button
      type="button"
      [disabled]="loadingItems || disabled || !selectedItemValue"
      id="btn-search"
      class="recipientt-search-button btn btn-sm btn-primary"
      (click)="doSearch()"
    >
      {{ 'BUTTONS.SEARCH' | translate }}
    </button>
    <button type="button" [disabled]="disabled" id="btn-close" class="recipientt-search-button btn btn-sm btn-default" (click)="resetList()">
      {{ 'BUTTONS.RESET' | translate }}
    </button>
  </div>
</div>

<ng-template #loadingData>
  <div class="relative-position w-100">
    <ul class="item-list-ul" *ngIf="loadingItems">
      <li class="list-li">
        {{ 'MESSAGES.LOADING_DATA' | translate }}
      </li>
    </ul>
  </div>
</ng-template>
<ng-template #noData>
  <div class="relative-position w-100">
    <ul *ngIf="!itemList.length && searchText" class="item-list-ul">
      <li class="list-li">
        {{ 'VALIDATION_MESSAGES.NO_ITEM_FOUND' | translate }}
      </li>
    </ul>
  </div>
</ng-template>
