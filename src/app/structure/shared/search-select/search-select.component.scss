.item-search {
  .item-list-ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid rgb(1, 144, 254);
    border-radius: 0 0px 4px 4px;
    border-top: none;
    position: absolute !important;
    background: #fff;
    z-index: 99;
    width: 100% !important;
  }

  .list-li {
    margin-top: -1px;
    padding: 0.42rem 1.14rem;
    text-decoration: none;
    position: relative;
  }

  ul {
    li.list-li:hover {
      background: #d2d9e5 !important;
      color: #222034 !important;
    }

    li.list-li.selected {
      background: #d2d9e5 !important;
      color: #222034 !important;
    }

    li.load-more-li,
    li.add-new-li {
      text-align: center;
      padding-top: 2mm;
      padding-bottom: 2mm;
    }

    li.load-more-loader {
      background-color: #f4f4f4;
      background-repeat: no-repeat;
      background-position-x: 100%;
      padding-left: 1rem;
    }

    .no-data {
      padding: 0;
      margin: 0;

      li {
        text-align: center;
      }
    }
  }
}

.recipientt-search-button {
  width: 100%;
}

.admission-buttons {
  display: flex;
  align-items: center;
  gap: 5%;
}
.grey-text {
  color: #c0bdd0 !important;
  cursor: not-allowed;
}

.close-icon-btn {
  font-size: 16px;
  color: #999;
  position: absolute;
  right: 18px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}

.close-icon-btn:hover {
  color: #333;
}

.relative-position {
  position: relative !important;
}

#div-content {
  input {
    padding-right: 7%;
  }
}
