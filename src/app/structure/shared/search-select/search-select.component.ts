import { Component, Input, Output, EventEmitter, HostListener, ElementRef, ChangeDetectionStrategy, SimpleChanges, OnChanges } from '@angular/core';
import { isBlank, isNil } from 'app/utils/utils';
import { CONSTANTS } from 'app/constants/constants';
import { Item, defaultSettings, ScrollType, LoadMoreItems } from './search-select.interface';

@Component({
  selector: 'app-search-select',
  templateUrl: './search-select.component.html',
  styleUrls: ['./search-select.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
/**
 * This shared component is used to implement search and select.
 * Data to be listed should be sent from parent component
 * This component doesn't have any API service dependency.
 */
export class SearchAndSelectComponent implements OnChanges {
  // Input Decorators
  @Input() disabled = false;
  @Input() settings = defaultSettings;
  @Input() itemList: Item[];
  @Input() reset = false;
  @Input() totalCount = 0;
  @Input() selectedItemProp;
  @Input() errorMessage = '';
  @Input() displaySelectedItem = false;
  @Input() disableContactlessUsers = false;
  @Input() siteIds = '';
  // FIXME: need to change checkContentLimit, contentLimit, and currentListCount after implementing the new API for user listing
  @Input() checkContentLimit = false;
  @Input() contentLimit = CONSTANTS.contentLimit;
  @Input() currentListCount = 0;
  // Output Decorators
  @Output() selectedItem = new EventEmitter<Item | Item[]>();
  @Output() loadMoreItems = new EventEmitter<LoadMoreItems>();
  @Output() addNewItem = new EventEmitter<Item>();
  @Output() resetListItems = new EventEmitter<LoadMoreItems>();
  // Class Properties
  searchText = '';
  selectedItemValue = '';
  loadingItems = false;
  showListDrpDwn = false;
  lazyLoadType = ScrollType;
  showAddNewItem = false;
  hideLoadMoreBtn = false;
  newListItem = '';
  selectedItems: Item[] = [];
  showNoData = false;
  showResetBtn = false;
  constructor(private elementRef: ElementRef) {}
  /**
   * Event listener to handle  clicks to hide the dropdown if the target is not the native element
   */
  @HostListener('document:click', ['$event.target'])
  onSearchSelectClick(target: any) {
    if (!this.elementRef.nativeElement.contains(target) && target.id !== 'btn-load-more') {
      this.showListDrpDwn = false;
    }
  }
  /**
   * Do the search, emits the next value
   * @returns void
   */
  doSearch(): void {
    this.itemList = [];
    this.loadingItems = true;
    this.searchText = this.selectedItemValue;
    this.loadMoreItems.emit({ searchText: this.searchText, loadMore: false });
    this.showListDrpDwn = true;
    if (this.settings.enableTagging && !isBlank(this.searchText)) {
      this.showAddNewItem = true;
    }
  }
  /**
   * Resets the properties to it's initial state
   * Add the properties here in this method to reset
   * @returns void
   */
  resetList(): void {
    this.itemList = [];
    this.showListDrpDwn = false;
    this.searchText = '';
    this.showAddNewItem = false;
    this.loadingItems = false;
    this.selectedItems = [];
    this.selectedItem.emit({ id: null, name: null, reset: true });
    this.resetListItems.emit({ searchText: '', loadMore: false });
    this.showResetBtn = false;
    this.selectedItemValue = '';
  }
  /**
   * On 'Load more' button clicked in the dropdown to fetch more results
   * @returns void
   */
  loadMore(): void {
    this.loadingItems = true;
    this.loadMoreItems.emit({ searchText: this.searchText, loadMore: true });
  }
  /**
   * If the new tag config is enabled and no exact match found add the item
   * New item will be selected and pushed to the array
   */
  addNewTag(): void {
    const newItem = { id: '-1', name: this.newListItem };
    this.itemList.push(newItem);
    this.onItemSelect(newItem);
    this.showAddNewItem = false;
    this.hideLoadMoreBtn = true;
  }
  /**
   * Check the search field empty, to the reset in that case. Its invoke on '(keyUp)'
   * @returns void
   */
  onSearchTextCleared(): void {
    if (isBlank(this.selectedItemValue)) {
      this.selectedItem.emit({ id: null, name: null, reset: false });
      this.showResetBtn = false;
      this.selectedItemValue = '';
    }
  }
  /**
   *  Do the search on EnterKey press
   * @param event Event object
   * @returns void
   */
  searchOnEnterKeyPress(): void {
    this.loadingItems = true;
    this.showListDrpDwn = true;
    this.searchText = this.selectedItemValue;
    this.loadMoreItems.emit({ searchText: this.searchText, loadMore: false });
  }
  /**
   * To optimize the ngFor directive performance
   * @param index a number
   * @item an object which contains the data
   */
  trackById(index: number, item: Item) {
    return item.id;
  }
  /**
   * On  selection changes
   * @param item selected item object
   * @returns void
   */
  onItemSelect(item: Item | any, alternateContact = null): void {
    if (
      !this.disableContactlessUsers ||
      (isBlank(alternateContact) && !item.noContactAvailable) ||
      (!isBlank(alternateContact) && !alternateContact.noContactAvailable)
    ) {
      const selectedData = item;
      this.selectedItemValue = !isBlank(alternateContact) ? alternateContact.displayName : item.name;
      this.showListDrpDwn = false;
      if (!isBlank(alternateContact)) {
        selectedData.contactId = alternateContact.contactId;
      } else if (Object.prototype.hasOwnProperty.call(item, 'contactId')) {
        delete selectedData.contactId;
      }
      this.selectedItem.emit(selectedData);
    }
    this.showResetBtn = !isBlank(this.selectedItemValue);
  }
  toggleAlternateContacts(item: any): void {
    item.showSubList = !item.showSubList;
  }
  /**
   * To handle the input decorator value change
   * @param changes an object with key which is type of SimpleChanges
   * @returns void
   */
  ngOnChanges(changes: SimpleChanges): void {
    this.showNoData = false;
    if (!isBlank(changes.itemList)) {
      if (!isBlank(changes.itemList.currentValue)) {
        this.checkForNewTagging();
      } else if (!isBlank(this.searchText)) {
        if (this.settings.enableTagging) {
          this.showAddNewItem = true;
          this.newListItem = this.searchText;
        } else {
          this.showNoData = true;
        }
      }
      this.loadingItems = false;
    }
    setTimeout(() => {
      if ((!isBlank(changes.reset) && changes.reset.currentValue) || (!isBlank(changes.siteIds) && !isNil(changes.siteIds.previousValue) &&
      changes.siteIds.currentValue !== changes.siteIds.previousValue)) {
        this.resetList();
        this.reset = false;
      }
    });
    if (changes.selectedItemProp && changes.selectedItemProp.currentValue !== changes.selectedItemProp.previousValue) {
      if (this.settings.multiSelection) {
        this.updateSelectedItem(changes.selectedItemProp.currentValue);
      } else {
        this.onItemSelect(changes.selectedItemProp.currentValue);
      }
    }
    this.hideLoadMoreBtn = this.itemList.length === this.totalCount || this.itemList.length % CONSTANTS.contentLimit !== 0;
    // FIXME: need to remove this condition after implementing the new API for user listing
    if (this.checkContentLimit) {
      this.hideLoadMoreBtn = this.currentListCount !== this.contentLimit;
    }
  }
  /**
   * Check for new tagging enabled, if enabled show the add new button
   * @returns void
   */
  private checkForNewTagging() {
    if (
      this.settings.enableTagging &&
      !isBlank(this.searchText) &&
      isBlank(
        this.itemList.find((item) => {
          return item.name.toLocaleLowerCase() === this.searchText.toLocaleLowerCase();
        })
      )
    ) {
      this.showAddNewItem = true;
      this.newListItem = this.searchText;
    } else this.showAddNewItem = false;
  }
  /**
   * for update the selected items in case of multi select dropdown
   * @param item selected item object
   * @returns void
   */
  updateSelectedItem(items: Item[]): void {
    this.selectedItems = items;
  }
  /**
   * On  selection changes of checkbox used in the multi select dropdown and emit the selected items
   * @param item selected item object
   * @returns void
   */
  toggleSelection(item: Item): void {
    this.showResetBtn = true;
    const index = this.selectedItems.findIndex((selectedItem) => selectedItem.id === item.id);
    if (index >= 0) {
      this.selectedItems.splice(index, 1);
    } else {
      this.selectedItems.push(item);
    }
    this.selectedItem.emit(this.selectedItems);
  }
  /**
   * for making the checkbox on or off
   * @param item selected item object
   * @returns boolean
   */
  isSelected(item: Item): boolean {
    return this.selectedItems.some((selectedItem) => selectedItem.id === item.id);
  }
  /**
   * For clear the search text and emit the reset event
   */
  clearSearchText() {
    this.showListDrpDwn = false;
    this.showResetBtn = false;
    this.selectedItem.emit({ id: null, name: null, reset: false });
    this.selectedItemValue = '';
  }
}
