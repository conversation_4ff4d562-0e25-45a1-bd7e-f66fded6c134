import { ComponentFixture, TestBed } from '@angular/core/testing';
import { CommonTestingModule } from 'app/utils/common-testing.module';
import { StructureService } from 'app/structure/structure.service';
import { ResetPasswordComponent } from './reset-password.component';
import * as CryptoJS from 'crypto-js';
import { CONFIG } from 'custom-configs';

describe('ResetPasswordComponent', () => {
  let component: ResetPasswordComponent;
  let fixture: ComponentFixture<ResetPasswordComponent>;
  let structureService: StructureService;
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [CommonTestingModule]
    }).compileComponents();
    structureService = TestBed.get(StructureService);
    structureService.userDetails = JSON.stringify({ name: 'testUser' });
    fixture = TestBed.createComponent(ResetPasswordComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize userData on ngOnInit', () => {
    spyOn(structureService, 'loadMicroFrontend');
    component.ngOnInit();
    expect(component.userData).toEqual({ name: 'testUser' });
    expect(structureService.loadMicroFrontend).toHaveBeenCalled();
  });

  it('should emit password details on getPasswordDetails', () => {
    const passwordData = {
      oldPassword: 'oldPassword',
      password: 'newPassword'
    };
    spyOn(component.passwordDetails, 'emit');
    const mockData = { detail: 'JTdCJTIyb2xkUGFzc3dvcmEyMyUyMiUyQyUyMnBhc3N3b3JkJTIyJTNBJTIydmFsaWRQYXNzd29yZCU0MDEyMyUyMiU3RA==' };
    spyOn(component, 'decryptData').and.returnValue(passwordData);
    component.getPasswordDetails(mockData);
    expect(component.passwordDetails.emit).toHaveBeenCalledWith(passwordData);
  });

  it('should remove micro-app element on ngOnDestroy', () => {
    const element = document.createElement('div');
    element.id = 'micro-app';
    document.body.appendChild(element);

    component.ngOnDestroy();
  });
  it('should decrypt data correctly', () => {
    const encryptedText = 'JTdCJTIyb2xkUGFzc3dvcmEyMyUyMiUyQyUyMnBhc3N3b3JkJTIyJTNBJTIydmFsaWRQYXNzd29yZCU0MDEyMyUyMiU3RA==';
    const mockDecryptedData = {
      oldPassword: 'oldPassword',
      password: 'newPassword'
    };
    const mockBytes = { toString: () => JSON.stringify(mockDecryptedData) };
    spyOn(CryptoJS.AES, 'decrypt').and.returnValue(mockBytes);
    const decryptedData = component.decryptData(encryptedText);
    expect(decryptedData).toEqual(mockDecryptedData);
    expect(CryptoJS.AES.decrypt).toHaveBeenCalledWith(encryptedText, CONFIG.DATA_ENCRYPTION_SECRET_KEY);
  });
});
