import { Component, OnInit, On<PERSON><PERSON>roy, ChangeDetectionStrategy, ViewEncapsulation, Output, EventEmitter, Input } from '@angular/core';
import { StructureService } from '../../structure.service';
import { isBlank } from 'app/utils/utils';
import * as CryptoJS from 'crypto-js';
import { CONFIG } from 'custom-configs';

@Component({
  selector: 'reset-password',
  templateUrl: './reset-password.component.html',
  styleUrls: ['./reset-password.component.scss'],
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ResetPasswordComponent implements OnInit, OnDestroy {
  @Output() passwordDetails = new EventEmitter<any>();
  @Input() hideCurrentPassword = false;
  @Input() clearForm = false;
  userData: any;
  constructor(private readonly structureService: StructureService) {}

  ngOnInit(): void {
    this.userData = this.structureService.userDetails ? JSON.parse(this.structureService.userDetails) : '';
    this.structureService.loadMicroFrontend();
  }
  getPasswordDetails(data): void {
    const decryptedData = !isBlank(data) && !isBlank(data.detail) ? this.decryptData(data.detail) : '';
    if (!isBlank(decryptedData)) {
      this.passwordDetails.emit(decryptedData);
    }
  }
  decryptData(encryptedText: string) {
    const bytes = CryptoJS.AES.decrypt(encryptedText, CONFIG.DATA_ENCRYPTION_SECRET_KEY);
    const decryptedData = bytes.toString(CryptoJS.enc.Utf8);
    return JSON.parse(decryptedData);
  }
  ngOnDestroy(): void {
    const microAppElement = document.getElementById('micro-app');
    if (microAppElement) {
      microAppElement.remove();
    }
  }
}
