import { AfterViewInit, Component, EventEmitter, Input, OnChanges, Output, SimpleChanges } from '@angular/core';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { SearchComponentSettings, ScrollType, LoadMoreItems } from '../search-select/search-select.interface';
import { CONSTANTS } from '../../../constants/constants';
import { HttpService } from '../../../services/http/http.service';
import { APIs } from '../../../constants/apis';
import { isBlank } from '../../../utils/utils';
import { Admission } from 'app/structure/users/admission';

declare const NProgress: any;
declare const $: any;
@Component({
  selector: 'app-admissions-dropdown',
  templateUrl: './admissions-dropdown.component.html'
})
export class AdmissionsDropdownComponent implements OnChanges, AfterViewInit {
  @Input() selectedPatient;
  @Input() selectedAdmissionId;
  @Input() siteIds;
  @Input() errorMessage = '';
  @Input() includePDGInfo = false;
  @Input() disabled = false;
  @Output() selectedItem: EventEmitter<any> = new EventEmitter<any>();
  admissionsList: any = [];
  selectedAdmission;
  resetSearchSelect = false;
  msgGrpSubjectOffset = CONSTANTS.contentOffset;
  totalAdmissionsCount = 0;
  searchComponentSettings: SearchComponentSettings;

  constructor(private httpService: HttpService, private toolTipService: ToolTipService) {
    this.searchComponentSettings = {
      allowLazyLoading: true,
      enableTagging: false,
      lazyLoadType: ScrollType.INFINITE,
      fieldName: 'ADMISSION.LABELS.ADMISSION',
      placeHolderText: 'ADMISSION.LABELS.SELECT_ADMISSIONS'
    };
  }

  ngAfterViewInit() {
    $('.signature-admission-info').tooltip({
      html: true,
      title: this.toolTipService.getToolTip('signature-new-signature', 'SIGN000013'),
      template: '<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner large"></div></div>'
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.selectedPatient && changes.selectedPatient.currentValue !== changes.selectedPatient.previousValue) {
      this.resetSearchSelect = true;
    }
  }

  searchForAdmissions(event: LoadMoreItems) {
    this.getAllAdmissions(event);
  }

  resetForAdmissions(event?: LoadMoreItems) {
    this.selectedAdmission = '';
    this.selectedItem.emit();
    this.msgGrpSubjectOffset = CONSTANTS.contentOffset;
    this.resetSearchSelect = false;
    if (this.selectedPatient) {
      this.getAllAdmissions(event);
    }
  }

  getAllAdmissions(event?: LoadMoreItems) {
    // loader start
    NProgress.start();
    const postData = {
      currentPage:
        event && event.loadMore ? (this.msgGrpSubjectOffset += CONSTANTS.currentPage) : (this.msgGrpSubjectOffset = CONSTANTS.contentOffset),
      patientIds: Array.isArray(this.selectedPatient) ? this.selectedPatient : [this.selectedPatient],
      siteIds: !isBlank(this.siteIds) && this.siteIds.toString() !== '0' ? this.siteIds.toString().split(',') : [],
      rowsPerPage: CONSTANTS.contentLimit,
      searchKey: event && !isBlank(event.searchText) ? `%${event.searchText}%` : '',
      sortBy: 'startDate',
      sortDirection: 'DESC'
    };
    let requestFields = 'admissionId,admissionName';
    if (this.includePDGInfo) {
      requestFields = `${requestFields},allowMultiChatThread,isDiscussionGroupPublic,chatRoomId,chatRoomCreatedBy,topic,participant`;
    }
    const url = `${APIs.getAdmissionsListEndPoint}?requestedFields=${requestFields}`;
    this.httpService.doPost(url, postData).subscribe(
      (res: any) => {
        // loader stop
        this.totalAdmissionsCount = res.page.totalElements || 0;
        let admissionData = res.content;
        admissionData = admissionData.map((item: any) => ({
          id: item.admissionId,
          name: item.admissionName,
          isPublic: this.includePDGInfo ? +item.discussionGroupPublic : undefined,
          allowMultiThreadChat: this.includePDGInfo ? +item.allowMultiChatThread : undefined,
          chatRoomId: this.includePDGInfo ? item.chatRoomId : undefined,
          chatRoomCreatedBy: this.includePDGInfo ? item.chatRoomCreatedBy : undefined,
          topic: this.includePDGInfo ? item.topic : undefined,
          isParticipant: this.includePDGInfo ? +item.participant : undefined
        }));
        this.admissionsList = event && event.loadMore ? this.admissionsList.concat(admissionData) : [...admissionData];
        if (this.selectedAdmissionId) {
          this.selectedAdmission = this.admissionsList.find((x) => x.id === this.selectedAdmissionId);
          if (!this.selectedAdmission && this.disabled) {
            this.getAdmissionDetails();
          }
        }
        NProgress.done();
      },
      () => {
        this.admissionsList = [];
        NProgress.done();
      }
    );
  }

  getAdmissionDetails(): void {
    if (this.selectedAdmissionId) {
      const url = APIs.admissionByIdEndPoint.replace(/{admissionId}/g, this.selectedAdmissionId);
      this.httpService.doGet(url).subscribe((selectedAdmission: Admission) => {
        this.selectedAdmission = { id: selectedAdmission.admissionId, name: selectedAdmission.admissionName };
      });
    }
  }

  onSelect(event) {
    this.selectedItem.emit(event);
  }
}
