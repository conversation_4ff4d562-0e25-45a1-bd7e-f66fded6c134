import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { NO_ERRORS_SCHEMA, SimpleChanges } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpService } from '../../../services/http/http.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { of } from 'rxjs/observable/of';
import { APIs } from '../../../constants/apis';
import { AdmissionsDropdownComponent } from './admissions-dropdown.component';
import { Observable } from 'rxjs';
import { HttpModule } from '@angular/http';
import { Apollo, ApolloModule } from 'apollo-angular';
import { PermissionService } from 'app/services/permission/permission.service';
import { StructureService } from 'app/structure/structure.service';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { provideClients } from 'test-utils';
import { AuthService } from '../auth.service';
import { SharedService } from '../sharedServices';
import { StoreService } from '../storeService';

describe('AdmissionsDropdownComponent', () => {
  let component: AdmissionsDropdownComponent;
  let fixture: ComponentFixture<AdmissionsDropdownComponent>;
  let httpService: HttpService;
  let toolTipService: ToolTipService;

  beforeEach(() => {
   TestBed.configureTestingModule({
     declarations: [AdmissionsDropdownComponent],
     providers: [
       HttpService,
       TranslateService,
       StructureService,
       Apollo,
       AuthService,
       SharedService,
       WorklistIndexdbService,
       ToolTipService,
       PermissionService,
       StoreService
     ],
     imports: [
       HttpClientTestingModule,
       TranslateModule.forRoot(),
       FormsModule,
       ReactiveFormsModule,
       RouterTestingModule,
       HttpModule,
       ApolloModule.forRoot(provideClients),
       LoggerModule.forRoot({
         serverLoggingUrl: '',
         level: NgxLoggerLevel.OFF,
         serverLogLevel: NgxLoggerLevel.OFF
       })
     ],
     schemas: [NO_ERRORS_SCHEMA]
   }).compileComponents();
 });

  beforeEach(() => {
    fixture = TestBed.createComponent(AdmissionsDropdownComponent);
    httpService = TestBed.get(HttpService);
    toolTipService = TestBed.get(ToolTipService);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  describe('ngOnChanges', () => {
    it('should reset search select when selectedPatient changes', () => {
      const changes: SimpleChanges = {
        selectedPatient: {
          currentValue: 'newPatient',
          previousValue: 'oldPatient',
          isFirstChange: () => false,
          firstChange: false
        }
      };
      component.ngOnChanges(changes);
      expect(component.resetSearchSelect).toBe(true);
    });
  });

  describe('searchForAdmissions', () => {
    it('should call getAllAdmissions', () => {
      const event = { loadMore: true, searchText: 'test' };
      spyOn(component, 'getAllAdmissions');
      component.searchForAdmissions(event);
      expect(component.getAllAdmissions).toHaveBeenCalledWith(event);
    });
  });

  describe('resetForAdmissions', () => {
    it('should reset admissions and call getAllAdmissions', () => {
      const event = { loadMore: true, searchText: 'test' };
      spyOn(component, 'getAllAdmissions');
      spyOn(component.selectedItem, 'emit');
      component.selectedPatient = 'patient';
      component.resetForAdmissions(event);
      expect(component.selectedAdmission).toBe('');
      expect(component.selectedItem.emit).toHaveBeenCalled();
      expect(component.resetSearchSelect).toBe(false);
      expect(component.getAllAdmissions).toHaveBeenCalledWith(event);
    });
  });

  describe('getAllAdmissions', () => {
    it('should fetch admissions list and update component properties', () => {
      const event = { loadMore: true, searchText: 'test' };
      const response = {
        page: { totalElements: 1 },
        content: [{ admissionId: '1', admissionName: 'Test Admission' }]
      };
      spyOn(httpService, 'doPost').and.returnValue(of(response));
      component.getAllAdmissions(event);
      expect(httpService.doPost).toHaveBeenCalled();
      expect(component.totalAdmissionsCount).toBe(1);
      expect(component.admissionsList.length).toBe(1);
    });
    it('should fetch admissions list and update component properties: includePDGInfo', () => {
      const event = { loadMore: true, searchText: 'test' };
      const response = {
        page: { totalElements: 1 },
        content: [{ admissionId: '1', admissionName: 'Test Admission' }]
      };
      component.includePDGInfo = true;
      spyOn(httpService, 'doPost').and.returnValue(of(response));
      component.getAllAdmissions(event);
      expect(httpService.doPost).toHaveBeenCalled();
      expect(component.totalAdmissionsCount).toBe(1);
      expect(component.admissionsList.length).toBe(1);
     });
     it('should fetch admissions list and update component properties: get admission', () => {
      const event = { loadMore: true, searchText: 'test' };
      const response = {
        page: { totalElements: 1 },
        content: [{ admissionId: '1', admissionName: 'Test Admission' }]
      };
      component.selectedAdmissionId = '2';
      component.disabled = true;
      spyOn(component, 'getAdmissionDetails').and.callThrough();
      spyOn(httpService, 'doPost').and.returnValue(of(response));
      component.getAllAdmissions(event);
      expect(httpService.doPost).toHaveBeenCalled();
      expect(component.getAdmissionDetails).toHaveBeenCalled();
      expect(component.totalAdmissionsCount).toBe(1);
      expect(component.admissionsList.length).toBe(1);
    });

    it('should handle error response and reset component properties', () => {
      const event = { loadMore: true, searchText: 'test' };
      spyOn(httpService, 'doPost').and.returnValue(Observable.throw('Error'));
      component.getAllAdmissions(event);
      expect(httpService.doPost).toHaveBeenCalled();
      expect(component.totalAdmissionsCount).toBe(0);
      expect(component.admissionsList).toEqual([]);
    });
  });

  describe('getAdmissionDetails', () => {
    it('should fetch admission details and update selectedAdmission', () => {
      const admission = { admissionId: '1', admissionName: 'Test Admission' };
      spyOn(httpService, 'doGet').and.returnValue(of(admission));
      component.selectedAdmissionId = '1';
      component.getAdmissionDetails();
      expect(httpService.doGet).toHaveBeenCalled();
      expect(component.selectedAdmission).toEqual({ id: '1', name: 'Test Admission' });
    });
  });

  describe('onSelect', () => {
    it('should emit selected item', () => {
      spyOn(component.selectedItem, 'emit');
      const event = { id: '1', name: 'Test Admission' };
      component.onSelect(event);
      expect(component.selectedItem.emit).toHaveBeenCalledWith(event);
    });
  });
});