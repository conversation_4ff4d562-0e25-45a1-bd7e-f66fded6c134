import { Injectable, EventEmitter } from '@angular/core';
import {
  configAssetsUrl
} from "../../../environments/environment";
import { CONFIG } from 'custom-configs';
import { Subject } from 'rxjs';
declare var moment: any;
@Injectable()
export class SharedService {
  appVersion = '5.77.0-20250311001';
  apolloClientName: String = "citus-web";
  assetsUrl: string = configAssetsUrl();
  onInboxData: EventEmitter<any> = new EventEmitter();
  updateInboxData: EventEmitter<any> = new EventEmitter();
  timeZoneList=[];
  
  inboxUnreadMessageCount: EventEmitter<any> = new EventEmitter();
  onPollingInboxData: EventEmitter<any> = new EventEmitter();
  signatureRequestInboxData: EventEmitter<any> = new EventEmitter<any>();
  signatureRequestPollingInboxData: EventEmitter<any> = new EventEmitter<any>();
  signatureRequestPendingInboxData: EventEmitter<any> = new EventEmitter<any>();
  readSignatureRequest: EventEmitter<any> = new EventEmitter<any>();
  readInboxData: EventEmitter<any> = new EventEmitter<any>();
  newMessageGroup: EventEmitter<any> = new EventEmitter<any>();
  newMessageGroupUp: EventEmitter<any> = new EventEmitter<any>();
  userDataUpdate: EventEmitter<any> = new EventEmitter<any>();
  chatWith: EventEmitter<any> = new EventEmitter<any>();
  sessionRefresh: EventEmitter<any> = new EventEmitter<any>();
  startIntro: EventEmitter<any> = new EventEmitter<any>();
  startIntroCallBack: EventEmitter<any> = new EventEmitter<any>();
  startInvitationIntro: EventEmitter<any> = new EventEmitter<any>();
  onPatientTopicCountChange: EventEmitter<any> = new EventEmitter();
  onAllowEditChange: EventEmitter<any> = new EventEmitter();
  joincall: EventEmitter<any> = new EventEmitter();  
  reloadOnConfigChange: EventEmitter<any> = new EventEmitter();
  endVideoCall: EventEmitter<any> = new EventEmitter<any>();
  videoChatDisconnect: EventEmitter<any> = new EventEmitter<any>();
  videoCallReceived:boolean = false;
  videoCall:boolean = false;
  applessVideo:boolean = false;
  videoCallEnd: EventEmitter<any> = new EventEmitter<any>();
  playAudioForCall: EventEmitter<any> = new EventEmitter<any>();
  muteInitiatorAudio: EventEmitter<any> = new EventEmitter<any>();
  vidyoClient: EventEmitter<any> = new EventEmitter<any>();
  initiatorReload: EventEmitter<any> = new EventEmitter<any>();
  validVidyoToken: EventEmitter<any> = new EventEmitter<any>();
  videoChanged: EventEmitter<any> = new EventEmitter<any>();
  rendered: EventEmitter<any> = new EventEmitter<any>();
  connectorAvailable: EventEmitter<any> = new EventEmitter<any>();
  currentVideoChatRoomId: EventEmitter<any> = new EventEmitter<any>();  
  videoChatInitialize: EventEmitter<any> = new EventEmitter<any>();
  enableVideoChatButton: EventEmitter<any> = new EventEmitter<any>();  
  showVideoChat: EventEmitter<any> = new EventEmitter<any>();
  acceptCallData: EventEmitter<any> = new EventEmitter<any>();
  pushNotificationCall: EventEmitter<any> = new EventEmitter<any>();
  videoCallEndByInitiator = new EventEmitter<any>();
  callInitiatedRoomId:string = ''
  onVideoChat:boolean = false;
  videoId:any='';
  videoFull:boolean = false;
  disconnectEnable:boolean = false;
  onGoingChatrooom:string = '';
  formsType:string="";
  viewFormBackActiveTab:string="";
  tenantIdFromSSO;
  ssoEmailId: string;
  myFormsType:string="";
  FormsPage:string="";
  goToInnerPage : boolean; // identifying user goes to inner page without site filter
  innerPageFilter : boolean; // identifying user goes to inner page with site filter
  pageFrom:string="";
  mySignatureType:string="";
  formFilterValue:String="";
  showSignatureSuccessMessage:boolean=false;
  signatureSuccessMessage : string = "";
  applayDatatablesaveState:boolean=false;
  configuringTenantData:boolean = false;
  crossTenantChange: EventEmitter<any> = new EventEmitter();
  acceptPushNotify: EventEmitter<any> = new EventEmitter<any>();
  patientDiscussionGroup: EventEmitter<any> = new EventEmitter<any>();
  chatWithUserListData: EventEmitter<any> = new EventEmitter<any>();
  pushNotification:boolean = false;
  reloadInbox:boolean = false;
  worklistColumnDef = [];
  worklistDetails = [];
  worklistState = [];
  patientActivityHubTab = {};
  userWorklistPrefrenceIds = [];
  updateUserInboxCounts: EventEmitter<any> = new EventEmitter<any>();
  reInitiateMessagePolling: EventEmitter<any> = new EventEmitter<any>();
  unreadCounts: EventEmitter<any> = new EventEmitter<any>();
  updateSignatureRequestUnreadCount : EventEmitter<any> = new EventEmitter<any>();
  messageLoader:any = {
    messages : true
  }
  hideLoadMore = true;
  showAlternateTab=false;
  alternateData: any = [];
  formdataWithAlternate : any=[];
  messagePollingSelfUpdate: EventEmitter<any> = new EventEmitter<any>();
  updateActiveMessageInChatroom: EventEmitter<any> = new EventEmitter<any>();
  mobileoremailverification: EventEmitter<any> = new EventEmitter<any>();
  mobileOrEmailVerified: EventEmitter<any> = new EventEmitter<any>();
  idpLoginSuccess: EventEmitter<any> = new EventEmitter<any>();
  idpLoginMessage: EventEmitter<any> = new EventEmitter<any>();
  inviteUserVideoTile: EventEmitter<any> = new EventEmitter<any>();
  videoUserListUpdate: EventEmitter<any> = new EventEmitter<any>();
  forwardUserVideoCall: EventEmitter<any> = new EventEmitter<any>();
  listUpdateOnKicked: EventEmitter<any> = new EventEmitter<any>();
  reloadChatroomWithNewThread: EventEmitter<any> = new EventEmitter<any>();
  reloadConfig: EventEmitter<any> = new EventEmitter<any>();
  emitToTopBar: EventEmitter<any> = new EventEmitter<any>();
  setDoubleVarification: EventEmitter<any> = new EventEmitter<any>();
  maxRemoteSourcesChanged:EventEmitter<any> = new EventEmitter<any>();
  formSubmissionEvent: EventEmitter<any> = new EventEmitter<any>();
  tempStaffRoleList:any;
  dashboardSummaryChartDateFilterOptions = {
    autoUpdateInput: false,
    linkedCalendars: false,
    locale: { format: 'YYYY-MM-DD' },
    alwaysShowCalendars: false,
    autoApply: true,
    ranges: {
      'All': [moment("2014-01-01T00:00:00"), moment()],
      'Today': [moment().startOf('day'), moment()],
      'Yesterday': [moment().startOf('day').subtract(1, 'days'), moment().endOf('day').subtract(1, 'days')],
      'Last 7 Days': [moment().startOf('day').subtract(6, 'days'), moment()],
      'Last 30 Days': [moment().startOf('day').subtract(29, 'days'), moment()],
      'This Month': [moment().startOf('month'), moment().endOf('month')],
      'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
      'Year To Date': [moment().startOf('year'), moment()]
    }
  }
  exportOptions= {
    autoUpdateInput: false,
    linkedCalendars: false,
    locale: { format: 'MM-DD-YYYY' },
    alwaysShowCalendars: false,
    autoApply : true, 
    opens: 'center',
     ranges: {
        'Today': [moment(), moment()],
        'Yesterday': [moment().subtract(2, 'days'), moment().subtract(1, 'days')],
        'Last 7 Days': [moment().subtract(6, 'days'), moment()],
       }
}
  dashboardTrendChartDateFilterOptions = {
    autoUpdateInput: false,
    linkedCalendars: false,
    locale: { format: 'YYYY-MM-DD' },
    alwaysShowCalendars: false,
    autoApply: true,
    startDate: moment().startOf('month'),
    endDate: moment().endOf('month'),
    ranges: {
      'All': [moment("2014-01-01T00:00:00"), moment()],
      'Last 7 days by Day': [moment().subtract(6, 'days'), moment()],
      'Last 30 days by Week': [moment().subtract(29, 'days'), moment()],
      'This Month by Week': [moment().startOf('month'), moment().endOf('month')],
      'Last Month by Week': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
      'Year to Date by Month': [moment().startOf('year'), moment()],
      'Year to Date by Quarter': [moment().startOf('year'), moment()],
      'Last Year by Quarter': [moment().subtract(1, 'year').startOf('year'), moment().subtract(1, 'year').endOf('year')]

    }
  }
    viewItem: boolean= true; 
    chatroomSiteId = '0';
    chatLogCount = 0;
    dashboadselectedTab:any;
    defaultFolderFromFilingCenter:any;
    selectedSiteForDoc:any;
    siteFilterApplyButton : boolean;
    selectedItemCopy : any;
    selectedSites : any;
    backToLitPage : any;
    preInfiniteCodeException : any;
    externalIntegrationToken = CONFIG.EXTERNAL_INTEGRATION_TOKEN;
    activeVisitChat : any;
    visitChatDetails: any;
    applessVideoChatroomDetails: any;
    $messageDeleteRestore: Subject<any> = new Subject<any>();
    $maskedMessageDeleteRestoreSubject: Subject<any> = new Subject<any>();
}
