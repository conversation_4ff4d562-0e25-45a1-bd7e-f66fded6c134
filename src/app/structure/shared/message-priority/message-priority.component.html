<div class="show-priority-box">
  <div class="show-priority-icon">
    <small *ngIf="messagePriority === MESSAGE_PRIORITY.HIGH" class="h6 text-uppercase font-weight-bold text-danger d-flex align-items-center"
      ><i class="alert-fill bg-danger" aria-hidden="true"></i>&nbsp;{{ 'PRIORITIES.HIGH' | translate }}</small
    >
    <small *ngIf="messagePriority === MESSAGE_PRIORITY.LOW" class="h6 text-uppercase font-weight-bold text-primary d-flex align-items-center"
      ><i class="arrowdown-fill bg-primary" aria-hidden="true"></i>&nbsp;{{ 'PRIORITIES.LOW' | translate }}</small
    >
  </div>
  <div class="btn-group btn-group-toggle btn-priority-group" data-toggle="buttons">
    <label
      class="btn btn-light mb-0"
      data-toggle="tooltip"
      data-placement="top"
      title="{{ 'TOOLTIPS.MARK_MSG_HIGH_PRIORITY' | translate }}"
      (click)="setPriorityValue(MESSAGE_PRIORITY.HIGH)"
      [ngClass]="{ active: messagePriority === MESSAGE_PRIORITY.HIGH }"
    >
      <input type="radio" name="messagePriority" id="high-msg-priority" autocomplete="off">
      <i class="alert-fill bg-danger" aria-hidden="true"></i>
    </label>
    <label
      class="btn btn-light mb-0"
      data-toggle="tooltip"
      data-placement="top"
      title="{{ 'TOOLTIPS.SEND_NORMAL_MSG' | translate }}"
      (click)="setPriorityValue(MESSAGE_PRIORITY.NORMAL)"
      [ngClass]="{ active: messagePriority === MESSAGE_PRIORITY.NORMAL }"
    >
      <input type="radio" name="messagePriority" id="normal-msg-priority" autocomplete="off" checked />
      <i class="fa fa-comment text-light" aria-hidden="true"></i>
    </label>
    <label
      class="btn btn-light mb-0"
      data-toggle="tooltip"
      data-placement="top"
      title="{{ 'TOOLTIPS.MARK_MSG_LOW_PRIORITY' | translate }}"
      (click)="setPriorityValue(MESSAGE_PRIORITY.LOW)"
      [ngClass]="{ active: messagePriority === MESSAGE_PRIORITY.LOW }"
    >
      <input type="radio" name="messagePriority" id="low-msg-priority" autocomplete="off" />
      <i class="arrowdown-fill bg-primary" aria-hidden="true"></i>
    </label>
  </div>
</div>
