import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MessagePriority } from 'app/constants/constants';

@Component({
  selector: 'app-message-priority',
  templateUrl: './message-priority.component.html',
  styleUrls: ['./message-priority.component.scss']
})
export class MessagePriorityComponent implements OnInit {
  @Output() selectedPriority = new EventEmitter<MessagePriority>();
  MESSAGE_PRIORITY = MessagePriority;
  @Input() messagePriority = MessagePriority.NORMAL;
  constructor() {}

  ngOnInit() {
    this.selectedPriority.emit(MessagePriority.NORMAL);
  }
  setPriorityValue(value) {
    this.messagePriority = value;
    this.selectedPriority.emit(this.messagePriority);
  }
}
