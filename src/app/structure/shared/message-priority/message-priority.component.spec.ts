import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { MessagePriority } from 'app/constants/constants';
import { TranslateModule } from '@ngx-translate/core';
import { MessagePriorityComponent } from './message-priority.component';

describe('MessagePriorityComponent', () => {
  let component: MessagePriorityComponent;
  let fixture: ComponentFixture<MessagePriorityComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [MessagePriorityComponent],
      imports: [TranslateModule.forRoot()]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MessagePriorityComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should emit selected priority on ngOnInit', () => {
    spyOn(component.selectedPriority, 'emit');
    component.ngOnInit();
    expect(component.selectedPriority.emit).toHaveBeenCalledWith(MessagePriority.NORMAL);
  });

  it('should set priority value and emit selected priority on setPriorityValue', () => {
    const value = MessagePriority.HIGH;
    spyOn(component.selectedPriority, 'emit');
    component.setPriorityValue(value);
    expect(component.messagePriority).toBe(value);
    expect(component.selectedPriority.emit).toHaveBeenCalledWith(value);
  });
});
