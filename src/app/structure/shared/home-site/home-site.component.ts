import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  <PERSON>Change,
  OnDestroy,
  ChangeDetectionStrategy,
} from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { ManageSitesService } from '../../manage-sites/manage-sites.service';
import { StructureService } from '../../structure.service';
import { Subscription, Observable } from 'rxjs';
import { SharedService } from '../sharedServices';
import { isBlank } from '../../../utils/utils';

declare let $: any;

@Component({
  selector: 'app-home-site',
  templateUrl: './home-site.component.html',
  styleUrls: ['./home-site.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class HomeSiteComponent implements OnInit, OnDestroy {
  @Output() readonly siteIds: EventEmitter<any> = new EventEmitter<any>();
  @Output() readonly siteName: EventEmitter<any> = new EventEmitter<any>();
  @Input() singleSelection = true;
  @Input() userId: number;
  @Input() filterType: boolean;
  @Input() disableFilter: boolean;
  @Input() selectedSiteIds: any;
  @Input() selectedSiteIdsOnEdit: '0'; // Used to bind home site selector while site selection changes on edit.
  @Input() selectedHomSiteId = 0; // Used at the time of edit, to select the home site from selected site list.
  @Input() siteSelection: boolean;
  @Input() showUnassigned: boolean;
  @Input() dynamic = false;
  @Input() pushSites = false;
  @Input() crossSite = false;
  @Input() crossSiteCommunication = false;
  @Input() isEditPage: boolean;
  private eventsSubscription: Subscription;
  @Input() events: Observable<void>;
  siteList: FormGroup;
  siteDetails = [];
  selectedItem: any = [];
  dropdownSettings = {};
  items = [];
  showItem = false;
  enableMultisite: any;
  enableCrossSite: any;
  userData: any;
  selectedSites: any;
  userEditPage: boolean;
  innerPageWithFilter: boolean;
  selectedItemsCopy: any;
  previousSiteSelection: any;
  selectedItemCopy: any;
  isOpenSiteFilter = false;
  siteData = [];
  homeSiteDropdown = [];

  constructor(
    private _formBuild: FormBuilder,
    private _managesitesService: ManageSitesService,
    private _sharedService: SharedService,
    private _structureService: StructureService
  ) {}

  ngOnInit() {
    this.userData = JSON.parse(this._structureService.userDetails);
    this.enableMultisite = parseInt(this.userData.config.enable_multisite) == 1;
    this.enableCrossSite = this.userData.enable_cross_site ? 1 : 0;
    if (this.userData.config.enable_multisite !== '1') {
      if (this.crossSite) {
        this.filterType = true;
        this.selectedSiteIds = this.userData.crossSiteId.split`,`.map(
          (x) => +x
        );
      }
      if (this.crossSiteCommunication) {
        this.filterType = true;
        this.selectedSiteIds = this.userData.enabledCrosssites.split`,`.map(
          (x) => +x
        );
        this.dynamic = true;
      }
    }

    /**
      * Add Set time out to wait getting page specific data
        like any configuration in edit page and handle the site id name 
        in edit page after checking the user config data like 
        multisite and cross site
      */
    setTimeout(() => {
      this.selectedSites = this._sharedService.selectedSites;
      // Set selected home site from list page filter to selectedSites variable.
      if (!this.filterType) {
        this.eventsSubscription = this.events.subscribe((data) =>
          this.reset(data)
        );
      }
      this.siteList = this._formBuild.group({
        site: [''],
      });

      this._managesitesService
        .getSitesByUserId(
          this.userId,
          this.enableMultisite,
          this.crossSite,
          this.crossSiteCommunication
        )
        .then((response) => {
          let selectedSiteArray = this.selectedSiteIds;
          if (typeof this.selectedSiteIds == 'string') {
            selectedSiteArray = this.selectedSiteIds.split(',').map(Number);
          }
          if (response) {
            response.forEach((item) => {
              if (!isBlank(this.dynamic) || !this.enableMultisite) {
                if (
                  !isBlank(item.name) &&
                  !isBlank(item.id) &&
                  isBlank(this.siteDetails.find((s) => s.id == item.id))
                ) {
                  this.siteDetails.push({ id: item.id, itemName: item.name });

                  this.siteData = this.siteDetails;
                }
              }
              if (this.isEditPage) {
                if (this.filterType) {
                  if (selectedSiteArray !== undefined) {
                    if (selectedSiteArray.includes(item.id)) {
                      this.items.push({ id: item.id, itemName: item.name });
                    }
                  }
                }
              } else {
                if (!this.selectedSiteIds) {
                  this.filterType
                    ? this.items.push({ id: item.id, itemName: item.name })
                    : '';
                } else {
                  if (this.filterType) {
                    if (selectedSiteArray !== undefined) {
                      if (selectedSiteArray.includes(item.id)) {
                        this.items.push({ id: item.id, itemName: item.name });
                      }
                    }
                  }
                }
              }
            });
            this.showItem =
              (this.enableMultisite && this.siteDetails.length > 1) ||
              (!this.enableMultisite &&
                this.crossSite === true &&
                this.siteDetails.length > 1) ||
              this.siteDetails.length > 1 ||
              (this.enableMultisite && this.enableCrossSite === true) ||
              this.siteDetails.length > 1;
            if (this.items && this.items.length && this.isEditPage) {
              this.homeSiteDropdown = this.items;
              if (this.selectedHomSiteId) {
                const tempSelectedItem = this.homeSiteDropdown.find((site) => {
                  return site.id == this.selectedHomSiteId;
                });
                this.selectedItem = tempSelectedItem ? [tempSelectedItem] : [];
              }
            }
            this.emitItems();
          }
        });
    }, 3000);
    this.dropdownSettings = {
      singleSelection: this.singleSelection,
      text: 'Select Home Site',
      selectAllText: 'All Home sites',
      unSelectAllText: 'Clear all sites',
      classes: 'home-site',
      enableSearchFilter: true,
      enableFilterSelectAll: this.singleSelection ? false : true,
      badgeShowLimit: 2,
    };
  }

  onItemSelect(): void {
    this.emitItems();
  }
  OnItemDeSelect(): void {
    this.emitItems();
  }
  onSelectAll(): void {
    this.selectedItem = this.selectedItem.filter((item) => item.id !== -1);
    this.siteDetails = this.siteDetails.filter((item) => item.id !== -1);
    this.emitItems();
  }
  onDeSelectAll(): void {
    this.selectedItem = [];
    this.pushUnassignedSite();
    this.emitItems();
  }
  onFilterSelectAll(): void {
    this.emitItems();
  }
  onFilterDeSelectAll(): void {
    this.emitItems();
  }
  applyFilter(): void {
    this._sharedService.siteFilterApplyButton = true;
    this._sharedService.selectedItemCopy = this.selectedItem;
    this.emitItems();
  }
  emitItems(): void {
    let ids = [];
    const name = [];
    if (this.selectedItem) {
      this.selectedItem.forEach((value) => {
        if (value.id != -1) {
          ids.push(`${value.id}`);
          name.push(`${value.itemName}`);
        } else {
          this.selectedItem = [];
          this.selectedItem.push({ id: -1, itemName: 'Unassigned sites' });
          ids.push('-1');
        }
        if (ids.includes('-1')) {
          ids = [];
          ids.push('-1');
        }
      });
      this.showItem =
        (this.enableMultisite && this.siteDetails.length > 1) ||
        (!this.enableMultisite &&
          this.crossSite === true &&
          this.siteDetails.length > 1) ||
        this.siteDetails.length > 1 ||
        (this.enableMultisite && this.enableCrossSite === true) ||
        this.siteDetails.length > 1;

      localStorage.setItem(
        'siteId',
        JSON.stringify({
          siteId:
            ids.length != 0 &&
            ((!this.showItem && this.siteSelection) || this.showItem)
              ? ids
              : '0',
        })
      );
      this.siteIds.emit({
        siteId:
          ids.length != 0 &&
          ((!this.showItem && this.siteSelection) || this.showItem)
            ? ids
            : '0',
      });
      this.siteName.emit({ siteNames: name });
    }
  }

  /** 
   * reset() Called when event emitted to home site component 
     from the parent component.Used to trigger change in site 
     component based on change in parent component and remove 
     the selected sites from the filter.
     *@param  <obj> data 
     *@return void
 */
  reset(data): void {
    let selectedSiteArray = this.selectedSiteIds;
    if (typeof this.selectedSiteIds == 'string') {
      selectedSiteArray = this.selectedSiteIds.split(',').map(Number);
    }
    if (!isBlank(data)) {
      setTimeout(() => {
        if (data == 'displaySelectedSitesOnly') {
          this.selectedItemsCopy = this.selectedItem;
          this.selectedItem = [];
          if (this.selectedSiteIds) {
            const selectedSiteLength =
              this.selectedSiteIds != 0 ? this.selectedSiteIds.length : 0;
            this.siteDetails = [];
            this._managesitesService
              .getSitesByUserId(
                this.userId,
                this.enableMultisite,
                this.crossSite,
                this.crossSiteCommunication
              )
              .then((response) => {
                if (response) {
                  response.forEach((item) => {
                    if (
                      isBlank(
                        this.siteDetails.find(
                          (details) => details.id == item.id
                        )
                      )
                    ) {
                      this.siteDetails.push({
                        id: item.id,
                        itemName: item.name,
                      });
                    }
                  });
                  if (selectedSiteLength !== 0) {
                    let oTempSelectedSite = [];
                    for (let key in selectedSiteArray) {
                      oTempSelectedSite[key] =
                        selectedSiteArray[key].toString();
                    }
                    const userFilter = this.siteDetails.filter((user) =>
                      oTempSelectedSite.includes(user.id.toString())
                    );
                    this.siteDetails = userFilter;
                  }
                }
              });
          }
        } else if (data == 'dynamicSiteUpdate') {
          this.dynamicSiteUpdate();
        } else if ('removeSelectedSite') {
          this.selectedItem = this.selectedItemsCopy;
        }
      });
    } else {
      this.selectedItem = [];
    }
  }

  /**
   * dynamicSiteUpdate() returns Promise 
     based on the passed void as @param 
   * @return selected Site ids as a promise when s dynamically 
     updating the home site of a logged in user by another user.
 */

  dynamicSiteUpdate(): Promise<any> {
    return new Promise(async (resolve) => {
      let userDetails = JSON.parse(this._structureService.userDetails);

      userDetails.mySites = {};

      this._structureService.userDetails = JSON.stringify(userDetails);
      if (
        userDetails.isAlternateContact == true ||
        userDetails.roleName == 'Caregiver'
      ) {
        userDetails.userId = userDetails.caregiver_userid;
      }
      this._managesitesService
        .getSitesByUserId(
          userDetails.userId,
          this.enableMultisite,
          this.crossSite,
          this.crossSiteCommunication
        )
        .then((res) => {
          userDetails.mySites = res;

          this._structureService.userDetails = JSON.stringify(userDetails);
          if (res) {
            this.siteDetails = [];
            let newSelectedArray = [];
            res.forEach((item) => {
              if (isBlank(this.siteDetails.find((s) => s.id == item.id))) {
                this.siteDetails.push({ id: item.id, itemName: item.name });
              }
              let deleteSelectSite = this.selectedItem.findIndex(
                (value) => value.id == item.id
              );
              if (deleteSelectSite != -1) {
                newSelectedArray.push(this.selectedItem[deleteSelectSite]);
              }
            });
            this.selectedItem = newSelectedArray;
            let itemShow = false;
            if (this.enableMultisite && this.siteDetails.length > 1) {
              itemShow = true;
            }
            this._sharedService.viewItem = itemShow;
            this.emitItems();
            resolve(true);
          }
        });
    });
  }
  pushUnassignedSite(): void {
    const idArray = [];
    if (
      !this.siteSelection &&
      isBlank(this.selectedSiteIds) &&
      !isBlank(this.showUnassigned)
    ) {
      this.siteDetails.forEach((value) => {
        idArray.push(value.id);
      });
      if (!idArray.includes(-1)) {
        this.siteDetails.push({ id: -1, itemName: 'Unassigned sites' });
      }
    }
  }

  /* For  resetting values in select based on selected site, 
       @selectedSiteIds has value and @dynamic has value true, 
       this reset calls and reset the value in the onchange of select site 
   */
  ngOnChanges(changes: { [propKey: string]: SimpleChange }) {
    if (changes['selectedSiteIds'] && this.dynamic == true) {
      if (parseInt(this.selectedSiteIds) !== 0) {
        this.homeSiteDropdown = this.siteData.filter((item) =>
          this.selectedSiteIds.includes(item.id.toString())
        );
      } else {
        this.homeSiteDropdown=[];
        this.onDeSelectAll();
      }
      if (
        this.selectedItem[0] &&
        this.selectedSiteIds.findIndex(
          (item) => item == this.selectedItem[0].id
        ) == -1
      ) {
        this.onDeSelectAll();
      }
    } else if (
      changes['selectedSiteIdsOnEdit'] &&
      this.dynamic == true &&
      this.isEditPage
    ) {
      //This block of code is get executed if the home site selector is called from an edit page
      if (this.selectedSiteIdsOnEdit !== '0') {
        this.homeSiteDropdown = this.siteData.filter((item) =>
          this.selectedSiteIdsOnEdit.includes(item.id.toString())
        );
        //Select the home site if user does have any home site
        if (this.selectedHomSiteId) {
          const tempSelectedItem = this.homeSiteDropdown.find((site) => {
            return site.id == this.selectedHomSiteId;
          });
          this.selectedItem = tempSelectedItem ? [tempSelectedItem] : [];
          this.emitItems();
        }  else {
          this.onDeSelectAll();
        }
      } else {
        this.onDeSelectAll();
        this.homeSiteDropdown =[];
      }
    }

    this._structureService.socket
      .off('updatedUserSites')
      .on('updatedUserSites', (data) => {
        this.dynamicSiteUpdate().then((res) => {
          if (res) {
            if (data.data.updatedBy !== this.userData.userId) {
              this._sharedService.emitToTopBar.emit({ toTopBar: true });
            }
          }
        });
      });
  }
  ngOnDestroy(): void {
    //Unsubscribe all the subscriptions here
    if (this.eventsSubscription) this.eventsSubscription.unsubscribe();
  }
}
