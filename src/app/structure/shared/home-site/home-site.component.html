<form class="" id="home-site-list" name="home-site-list" #f="ngForm">
    <div class="form-body">
        <div class="row">
            <div class="col-md-12" [ngClass]="{'disable-filter':disableFilter}">
                <span>
                    <angular2-multiselect [data]="homeSiteDropdown" [(ngModel)]="selectedItem" name="site"
                    [settings]="dropdownSettings" (onSelect)="onItemSelect()" (onDeSelect)="OnItemDeSelect()"
                    (onSelectAll)="onSelectAll()" (onDeSelectAll)="onDeSelectAll()"
                    (onFilterSelectAll)="onFilterSelectAll()" (onFilterDeSelectAll)="onFilterDeSelectAll()">
                </angular2-multiselect>
                </span>
       </div>
      </div>
    </div>
</form>