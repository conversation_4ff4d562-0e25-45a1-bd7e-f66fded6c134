import { Injectable, EventEmitter } from '@angular/core';
import { Http, Headers, RequestOptions, Response } from '@angular/http';
import { Observable } from 'rxjs/Rx';
import { environment } from '../../../environments/environment';
declare const Oidc: any;

const settings: any = {
  authority: 'https://offeringsolutions-sts.azurewebsites.net',
  client_id: 'angularImplicitClient',
  redirect_uri: window.location.origin,
  post_logout_redirect_uri: 'http://localhost:4200/',
  response_type: 'id_token token',
  scope: 'openid profile email',
  silent_redirect_uri: 'http://localhost:4200/silent-renew.html',
  automaticSilentRenew: true,
  accessTokenExpiringNotificationTime: 4,
  // silentRequestTimeout:10000,

  filterProtocolClaims: true,
  loadUserInfo: true
};

@Injectable()
export class AuthService {
  mgr;
  //mgr: UserManager = new UserManager(settings);
  userLoadededEvent: EventEmitter<any> = new EventEmitter<any>();
  currentUser: any;
  loggedIn = false;

  authHeaders: Headers;


  constructor(private http: Http) {
  }
  idpConfigurationSet(settings) {
    console.log("idpConfigurationSet");
    Oidc.Log.logger = console;
    Oidc.Log.level = Oidc.Log.INFO;
    const setting: any = {
      authority: settings.authority,
      client_id: settings.client_id,
      redirect_uri: settings.redirect_uri,
      scope: settings.scope,
      post_logout_redirect_uri: window.location.origin,
      response_type: settings.response_type && settings.response_type !='' ? settings.response_type : 'id_token',
      automaticSilentRenew: true,
      accessTokenExpiringNotificationTime: 4,
      filterProtocolClaims: true,
      loadUserInfo: true
    };
    this.mgr = new Oidc.UserManager(setting);
  }
  userLogginCheck() {
    console.log("userLogginCheck ");
    this.mgr.getUser()
      .then((user) => {
        if (user) {
          this.loggedIn = true;
          this.currentUser = user;
          this.userLoadededEvent.emit(user);
        }
        else {
          this.loggedIn = false;
        }
      })
      .catch((err) => {
        this.loggedIn = false;
      });

    this.mgr.events.addUserLoaded((user) => {
      this.currentUser = user;
      this.loggedIn = !(user === undefined);
      if (!environment.production) {
        console.log('authService addUserLoaded', user);
      }

    });

    this.mgr.events.addUserUnloaded(() => {
      if (!environment.production) {
        console.log('user unloaded');
      }
      this.loggedIn = false;
    });
  }
  isLoggedInObs() {
    this.mgr.getUser().then((user) => {
      if (user) {
        return true;
      } else {
        return false;
      }
    }).catch((err) => {
      return false;
    });

    /*return Observable.fromPromise(this.mgr.getUser()).map<User, boolean>((user) => {
      if (user) {
        return true;
      } else {
        return false;
      }
    });*/
  }

  clearState() {
    this.mgr.clearStaleState().then(function () {
      console.log('clearStateState success');
    }).catch(function (e) {
      console.log('clearStateState error', e.message);
    });
  }

  getUser() {
    const promise = new Promise((resolve, reject) => {
      this.mgr.getUser().then((user) => {
        this.currentUser = user;
        console.log('got user', user);
        //this.userLoadededEvent.emit(user);
        resolve(user);
      }).catch(function (err) {
        console.log(err);
        resolve(false);
      });
    });
    return promise;
  }

  removeUser() {
    this.mgr.removeUser().then(() => {
      this.userLoadededEvent.emit(null);
      console.log('user removed');
    }).catch(function (err) {
      console.log(err);
    });
  }

  startSigninMainWindow(login_hint: string) {
    this.mgr.signinRedirect({ login_hint }).then(function () {
      console.log('signinRedirect done');
    }).catch(function (err) {
      console.log(err);
    });
  }
  endSigninMainWindow() {
    const promise = new Promise((resolve, reject) => {
      this.mgr.signinRedirectCallback().then(function (user) {
        console.log('signed in', user);
        resolve(user);
      }).catch(function (err) {
        console.log(err);
        resolve(false);
      });
    });
    return promise;
  }

  startSignoutMainWindow() {
    if (this.mgr) {
      this.mgr.getUser().then(user => {
        const options =  user && user.id_token ? { id_token_hint: user.id_token } : null;
        return this.mgr.signoutRedirect(options).then(resp => {
          console.log('signed out', resp);
          setTimeout(5000, () => {
            console.log('testing to see if fired...');
          });
        }).catch(function (err) {
          console.log(err);
        });
      });
    }
  };

  endSignoutMainWindow() {
    this.mgr.signoutRedirectCallback().then(function (resp) {
      console.log('signed out', resp);
    }).catch(function (err) {
      console.log(err);
    });
  };
  /**
   * Example of how you can make auth request using angulars http methods.
   * @param options if options are not supplied the default content type is application/json
   */
  AuthGet(url: string, options?: RequestOptions): Observable<Response> {

    if (options) {
      options = this._setRequestOptions(options);
    }
    else {
      options = this._setRequestOptions();
    }
    return this.http.get(url, options);
  }
  /**
   * @param options if options are not supplied the default content type is application/json
   */
  AuthPut(url: string, data: any, options?: RequestOptions): Observable<Response> {

    let body = JSON.stringify(data);

    if (options) {
      options = this._setRequestOptions(options);
    }
    else {
      options = this._setRequestOptions();
    }
    return this.http.put(url, body, options);
  }
  /**
   * @param options if options are not supplied the default content type is application/json
   */
  AuthDelete(url: string, options?: RequestOptions): Observable<Response> {

    if (options) {
      options = this._setRequestOptions(options);
    }
    else {
      options = this._setRequestOptions();
    }
    return this.http.delete(url, options);
  }
  /**
   * @param options if options are not supplied the default content type is application/json
   */
  AuthPost(url: string, data: any, options?: RequestOptions): Observable<Response> {

    let body = JSON.stringify(data);

    if (options) {
      options = this._setRequestOptions(options);
    } else {
      options = this._setRequestOptions();
    }
    return this.http.post(url, body, options);
  }


  private _setAuthHeaders(user: any): void {
    this.authHeaders = new Headers();
    this.authHeaders.append('Authorization', user.token_type + ' ' + user.access_token);
    if (this.authHeaders.get('Content-Type')) {

    } else {
      this.authHeaders.append('Content-Type', 'application/json');
    }
  }
  private _setRequestOptions(options?: RequestOptions) {
    if (this.loggedIn) {
      this._setAuthHeaders(this.currentUser);
    }
    if (options) {
      options.headers.append(this.authHeaders.keys[0], this.authHeaders.values[0]);
    } else {
      options = new RequestOptions({ headers: this.authHeaders });
    }

    return options;
  }

}


