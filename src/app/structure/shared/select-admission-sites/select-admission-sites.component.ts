import { Component, OnInit, Input, Output, EventEmitter, OnChanges, SimpleChanges } from '@angular/core';
import { APIs } from 'app/constants/apis';
import { HttpService } from 'app/services/http/http.service';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { isBlank, isPresent } from 'app/utils/utils';

@Component({
  selector: 'app-select-admission-sites',
  templateUrl: './select-admission-sites.component.html'
})
export class SelectAdmissionSitesComponent implements OnInit, OnChanges {
  @Output() readonly sites: EventEmitter<any> = new EventEmitter<any>();
  @Input() patientId;
  @Input() selectedSiteIds;
  @Input() selectFirstByDefault = true;
  siteDetails = [];
  selectedItem = [];
  hideDropdown = true;
  dropdownSettings = {};
  constructor(private httpService: HttpService, private toolTipService: ToolTipService, private structureService: StructureService) {}
  ngOnChanges(changes: SimpleChanges): void {
    if (
      changes.patientId &&
      isPresent(this.patientId) &&
      changes.patientId &&
      (isBlank(this.siteDetails) || changes.patientId.currentValue !== changes.patientId.previousValue)
    ) {
      this.getAllAdmissionSites();
    }
    if (changes.selectedSiteIds) {
      this.setDefaultSite();
    }
  }
  ngOnInit() {
    this.dropdownSettings = {
      singleSelection: true,
      text: this.toolTipService.getTranslateData('LABELS.SELECT_SITES'),
      classes: 'myclass custom-class',
      enableSearchFilter: true,
      enableFilterSelectAll: true
    };
  }
  getAllAdmissionSites() {
    if (isBlank(this.patientId)) {
      return;
    }
    this.httpService.doPost(APIs.getAdmissionSitesEndPoint, { patientIds: [this.patientId] }).subscribe(
      (res: any) => {
        if (res && res.content) {
          this.siteDetails = res.content.map((site) => ({ ...site, id: site.siteId, itemName: site.siteName })) || [];
        }
        this.setDefaultSite();
      },
      () => {
        this.setDefaultSite();
      }
    );
  }
  setDefaultSite() {
    if (isBlank(this.siteDetails)) {
      const userData = this.structureService.getUserdata();
      this.siteDetails = userData.mySites.map((site) => ({ ...site, id: site.id, itemName: site.name })) || [];
    }
    if (this.selectFirstByDefault) {
      this.selectedItem = [this.siteDetails[0]];
    } else {
      let selectedSiteIds = [];
      if (typeof this.selectedSiteIds === 'string') {
        selectedSiteIds = this.selectedSiteIds.split(',');
      } else if (typeof this.selectedSiteIds === 'number') {
        selectedSiteIds = [this.selectedSiteIds];
      }
      selectedSiteIds = !isBlank(this.selectedSiteIds) ? this.selectedSiteIds.map(Number) : [];
      this.selectedItem = this.siteDetails.filter((site) => selectedSiteIds.includes(+site.id));
    }
    this.hideDropdown = false;
    this.emitItems();
  }
  deselectItem() {
    this.selectedItem = [];
    this.emitItems();
  }
  emitItems(): void {
    this.sites.emit({
      hideDropdown: this.hideDropdown,
      selectedItem: this.selectedItem,
      siteNames: this.selectedItem.map((site) => site.itemName),
      siteId: this.selectedItem.map((site) => site.id)
    });
  }
}
