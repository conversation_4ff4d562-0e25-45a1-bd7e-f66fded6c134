
.white_content{
    font-family: Lato;
    /* font-size: 1.5rem; */
    text-align: center;
    box-sizing: border-box;
    color: #333;
    font-size:1rem;
    
}
#verify-mobile {
    display: none;
}
#verify-email{
    display: none;
}

#dialog {
    border: solid 1px #ccc;
    margin: 10px auto;
    padding: 10px 35px 10px 13px;
    /* padding: 10px 13px; */
    display: inline-block;
    box-shadow: 0 0 4px #ccc;
    background-color: #e4e9f0;
    overflow: hidden;
    position: relative;
    /* max-width: 450px; */
    
}
      
#dialog h3 {
    margin: 0 0 0 0px; 
    padding: 0;
    font-size: 1rem;
    /* line-height: 1.25; */
}

#dialog span {
    /* font-size: 83%; */
    display: block;
}

#dialog #form {
    /* max-width: 240px; */
    margin: 6px auto 0;
}

#dialog input {
    margin: 0 5px;
    text-align: center;
    height: 50px;
    font-size: 50px;
    border: solid 1px #ccc;
    box-shadow: 0 0 5px #ccc inset;
    outline: none;
    width: 40px;
    transition: all .2s ease-in-out;
    border-radius: 3px;
}
    
#dialog input:focus {
    border-color: #1790fe;
    box-shadow: 0 0 5px #1790fe inset;
}
    
#dialog input:selection {
    background: transparent;
}

/* #dialog button {
    margin:  30px 0 50px;
    width: 100%;
    padding: 6px;
    background-color: #B85FC6;
    border: none;
    text-transform: uppercase;
} */

.btn-embossed{
    margin: 17px 0 0px 36%;
    /* width: 17%; */
    /* padding: 6px; */
    background-color: #1790fe;
    border: none;
    text-transform: uppercase;
    display: none;
}

#dialog button.close {
    border: solid 2px;
    border-radius: 30px;
    line-height: 19px;
    font-size: 120%;
    width: 22px;
    position: absolute;
    right: 5px;
    top: 6px;
}    

button.btn.btn-small.send-again:hover {
    background-color: #099be6 !important;
    border-color: #099be6 !important;
    color: #fff !important;
}
button.btn.btn-small.send-again{
    margin-top: 6px !important;
}

#dialog div {
    position: relative;
    z-index: 1;
}

#dialog img {
    position: absolute;
    bottom: -70px;
    right: -63px;
}
.me-timmer{
    color: red;
}
button.btn.btn-small {
    height: 30px;
    font-weight: 200 !important;
    line-height: 1 !important;
    padding: 0.57rem 0.5rem !important;
}

