<div id="verify_datas">
    <div id="verify-mobile" class="white_content">
        <div id="dialog" *ngIf="!verifyMobCodeSend" >
            Sending 4 digit code to your number, Please wait...
        </div>
        <div id="dialog" *ngIf="verifyMobCodeSend">
            <button class="close" (click)="resetVerification({mobile:true})" >×</button>
            <h3>Please enter the 4-digit verification code we sent via SMS:</h3>
            <span>(we want to make sure it's you before we contact to you)</span>
            <span *ngIf="mobileVerificationTimmerText!=''">Time left : <label class='me-timmer'>{{mobileVerificationTimmerText}}</label></span>
            <div id="form">
                <input id="mob_1" class="verify_input mob_input" type="text" maxLength="1" size="1" min="0" max="9" pattern="[0-9]{1}" />
                <input id="mob_2" class="verify_input mob_input" type="text" maxLength="1" size="1" min="0" max="9" pattern="[0-9]{1}" />
                <input id="mob_3" class="verify_input mob_input" type="text" maxLength="1" size="1" min="0" max="9" pattern="[0-9]{1}" />
                <input id="mob_4" class="verify_input mob_input" type="text" maxLength="1" size="1" min="0" max="9" pattern="[0-9]{1}" />
                <div><button type="button" id="verify_mobcode" class="btn btn-primary btn-embossed" (click)="verifyMobileOrEmail({mobile:true})">Verify Now</button></div>
            </div>
            <div>
                Didn't receive the code?
                <button type="button" class="btn btn-small send-again" (click)="show_popup({mobile:true,formData:{countryCode:countryCode,pcellno:mobileNumber}})" >Send code again</button>
            </div>
            <!-- <img src="http://jira.moovooz.com/secure/attachment/10424/VmVyaWZpY2F0aW9uLnN2Zw==" alt="test" /> -->
        </div>
    </div>
    <div id="verify-email" class="white_content"> 
        <div id="dialog" *ngIf="!verifyEmailCodeSend" >
            Sending 4 digit code to your email, Please wait...
        </div>
        <div id="dialog" *ngIf="verifyEmailCodeSend">
            <button class="close" (click)="resetVerification({email:true})">×</button>
            <h3>Please enter the 4-digit verification code we sent via email:</h3>
            <span>(we want to make sure it's you before we contact to you)</span>
            <span *ngIf="emailVerificationTimmerText !=''">Time left : <label class='me-timmer'>{{emailVerificationTimmerText}}</label></span>
            <div id="form">
                <input class="verify_input email_input" type="text" maxLength="1" size="1" min="0" max="9" pattern="[0-9]{1}" />
                <input class="verify_input email_input" type="text" maxLength="1" size="1" min="0" max="9" pattern="[0-9]{1}" />
                <input class="verify_input email_input" type="text" maxLength="1" size="1" min="0" max="9" pattern="[0-9]{1}" />
                <input class="verify_input email_input" type="text" maxLength="1" size="1" min="0" max="9" pattern="[0-9]{1}" />
                <div><button type="button" id="verify_emailcode" class="btn btn-primary btn-embossed" (click)="verifyMobileOrEmail({email:true})">Verify Now</button></div>
            </div>
            <div>
                Didn't receive the code?
                <button type="button" class="btn btn-small send-again" (click)="show_popup({email:true,formData:{pemail:emailId}})" >Send code again</button>
            </div>
        </div>
    </div>
</div>