import { Component, OnInit, OnD<PERSON>roy} from '@angular/core';
import { NgForm, FormBuilder, FormGroup, Validators, ValidatorFn, AbstractControl, FormControl } from '@angular/forms';
import { StructureService } from '../../structure.service';
import { SharedService } from '../../shared/sharedServices';
import { Subscription } from 'rxjs';

declare var $: any;

@Component({
    selector: 'app-verify-em',
    templateUrl: './verify-em.component.html',
    styleUrls: ['./verify-em.component.css']
  })

  export class VerifyEmComponent implements OnInit, OnDestroy {
    body;
    mobileVerificationCode;
    verifyMobileCodeEnter =false;
    verifyMobCodeSend =false;
    mobileVerificationTimmerText;
    mobileTimmer;
    mobileNumber;
    countryCode;
    emailVerificationCode;
    verifyEmailCode =false;
    verifyEmailCodeSend =false;
    emailVerificationTimmerText;
    emailTimmer;
    emailId;
    private socketEventSubscriptions: Subscription;
    constructor(
      private _structureService :StructureService,
        private _sharedService: SharedService,
    ) {  
      this.socketEventSubscriptions = this._structureService.subscribeSocketEvent('verifyEmailOrMobileSend').subscribe((response) => {
        console.log ('verifyEmailOrMobileSend response ====>' , response);
        var message ="Verification code Send,Please check";
        var type="success";
        if(response.mobile){
          if(response.success){
            message = "Verification code send to your mobile number please check";          
            this.verifyMobCodeSend =true;
            clearInterval(this.mobileTimmer);
            var fiveMinutes = 60 * 10;
            this.mobileVerificationTimmer(fiveMinutes);
          }else{
            type="danger";
            message=response.message;
            //verification code send failed
            this.resetVerification({mobile:true});
          }
        }else if(response.email){
          console.log("Email verification response...",response)
          if(response.success){
            console.log("Email verification success Enter...");
            message= "Verification code send to your Email please check";
            this.verifyEmailCodeSend =true;
            clearInterval(this.emailTimmer);
            var fiveMinutes = 60 * 10;
            this.emailVerificationTimmer(fiveMinutes);
          }else{
            console.log("Email verification error...",response.message)
            type="danger";
            message=response.message;
            //verification code send failed
            this.resetVerification({mobile:true});
          }
        }
        this.showMessage({type:type,message:message});    
    });
    }
    
    ngOnInit() {

    }

    ngOnDestroy() {
      /**Unsubscribe all the socket event subscriptions */
      if(this.socketEventSubscriptions) this.socketEventSubscriptions.unsubscribe();
    }
    setEventListner(){
      console.log("body====> ",this.body);
      this.body.on('keyup', '.verify_input', this.goToNextInput);
      this.body.on('keydown', '.verify_input', this.onKeyDown);
      this.body.on('click', '.verify_input', this.onFocus);
    }

    show_popup(params){
      if(params.mobile){
        console.log("clicked mobile verification==> ",params);
        if(params.formData && params.formData.countryCode && params.formData.countryCode !="" && params.formData.pcellno && params.formData.pcellno !=""){
          this.mobileVerificationCode = this.generateOTP();
          this.mobileNumber = params.formData.pcellno
          this.countryCode = params.formData.countryCode
          var data ={
            mobile:true,
            mobileNumber:this.mobileNumber,
            countryCode:this.countryCode,
            code:this.mobileVerificationCode
          }
          this._sharedService.mobileoremailverification.emit(data);
          //document.getElementById("verify-mobile").style.display='block';
          this.showOrHideVerificationPopup({condition:true,mobile:true});
          $("#oldPhoneNumber").val(params.formData.countryCode+params.formData.pcellno);
        }else{
          console.log("Phone number or country code missing");           
        }           
      }else if(params.email){
        if(params.formData && params.formData.pemail && params.formData.pemail !=""){
          this.emailVerificationCode = this.generateOTP();
          this.emailId =params.formData.pemail
          var emaildata:any ={
            email:true,
            emailId:this.emailId,
            code:this.emailVerificationCode
          }
          this._sharedService.mobileoremailverification.emit(emaildata);
          //document.getElementById("verify-email").style.display='block';
          this.showOrHideVerificationPopup({condition:true,email:true});
        }else{
          console.log("Not a valid email id")
        }
      } 
      this.body = $('#verify_datas');
      this.setEventListner();
    }

    goToNextInput(e) {
      console.log("goToNextInput Enter");
      var key = e.which,
        t = $(e.target);
       var nextInput='.verify_input';
        console.log("t======> ",t);
        console.log("t[0].className==>"+t[0].className);
        if(t[0].className.indexOf("mob_input")>-1){
          nextInput = ".mob_input";
        }else{
          nextInput = ".email_input";
        }
        console.log("nextInput====> "+nextInput);
        var sib = t.next(nextInput);
      if (key != 9 && (key < 48 || key > 57) && (key < 96 || key > 105)) {
        e.preventDefault();
        return false;
      }
  
      if (key === 9) {
        return true;
      }
      console.log("sib======> ",sib);
      if (!sib.length) {
        console.log("Sib.length ===000000===> " +'input'+nextInput);
        console.log("$('input'+nextInput).length==> "+$('input'+nextInput).length);
        $('input'+nextInput).each(function(index,element) {
          console.log("index===> "+index);
          console.log("iddddd===> "+$(this)[0].id);
          console.log("$(this).val()===> "+$(this).val());
          if(!$(this).val()){
            console.log("No value======>"+$(this)[0].id);
              sib =$(this);
              return false;
          }
        });
        console.log("sib length after..........",sib.length);
        console.log(sib);
        if(!sib.length){
          console.log("fffffffffffffffffffffffffffffff")
          this.verifyMobileCodeEnter = true;
          console.log(" this.verifyMobileCodeEnter==>"+ this.verifyMobileCodeEnter);
          var params={};
          if(nextInput.indexOf("mob")>-1){
            params={mobile:true};
          }else if(nextInput.indexOf("email")>-1){
            params={email:true};
          }
          openVerifyButton(params);
        }
        //sib = $('#verify-mobile').find('.verify_input').eq(0);
      }else{
        console.log("sib falseeeeeeeeeeeeeee")
      }
      sib.select().focus();
    }

    onKeyDown(e) {
      var key = e.which;
      if (key === 9 || (key >= 48 && key <= 57) ||(key >= 96 && key <= 105)) {
        return true;
      }
  
      e.preventDefault();
      return false;
    }

    onFocus(e) {
      $(e.target).select();
    }
    showOrHideVerificationPopup(params){
      if(params.condition){
        if(params.mobile){
          document.getElementById("verify-mobile").style.display='block';
        }else if(params.email){
          document.getElementById("verify-email").style.display='block';
        }
      }else{
        if(params.mobile){
          document.getElementById("verify-mobile").style.display='none';
        }else if(params.email){
          document.getElementById("verify-email").style.display='none';
        }
      }
    }
    mobileVerificationTimmer(duration){
      var timer = duration, minutes, seconds;
      this.mobileTimmer = setInterval(()=> {
          minutes = parseInt((timer / 60).toString(), 10);
          seconds = parseInt((timer % 60).toString(), 10);

          minutes = minutes < 10 ? "0" + minutes : minutes;
          seconds = seconds < 10 ? "0" + seconds : seconds;

          //display.textContent = minutes + ":" + seconds;
          this.mobileVerificationTimmerText = minutes + ":" + seconds;
          //console.log("this.mobileVerificationTimmerText===> "+this.mobileVerificationTimmerText);
          if (--timer < 0) {
              this.mobileVerificationTimmerText="";
              this.resetVerification({mobile:true});              
          }
      }, 1000);
    }

    emailVerificationTimmer(duration){
      console.log("email verification timmer===>")
      var timer = duration, minutes, seconds;
      this.emailTimmer = setInterval(()=>{
          minutes = parseInt((timer / 60).toString(), 10);
          seconds = parseInt((timer % 60).toString(), 10);

          minutes = minutes < 10 ? "0" + minutes : minutes;
          seconds = seconds < 10 ? "0" + seconds : seconds;

          //display.textContent = minutes + ":" + seconds;
          this.emailVerificationTimmerText = minutes + ":" + seconds;
          //console.log("this.emailVerificationTimmerText===> "+this.emailVerificationTimmerText);
          if (--timer < 0) {
            console.log("emailVerificationTimmerText END........");
              this.emailVerificationTimmerText="";
              this.resetVerification({email:true});
          }
      }, 1000);
    }

    generateOTP() { 
      var digits = '0123456789'; 
      let OTP = ''; 
      for (let i = 0; i < 4; i++ ) { 
          OTP += digits[Math.floor(Math.random() * 10)]; 
      } 
      //console.log("OTP is==>"+OTP);
      return OTP; 
    }

    verifyMobileOrEmail(params){
      console.log("verifyMobileOrEmail==> ",params);
      if(params.mobile){
        console.log("verifyMobileOrEmail==Mobile");
        var mobVCode ="";
        var mobInput= $('input.mob_input');
        var mobInputLength = mobInput.length;
        mobInput.each((index,elem)=> {
          //console.log("mobInput==> ",elem.value);
          if(elem.value!=""){
            mobVCode =mobVCode+elem.value;
          }
          //console.log("mobVCode==> "+mobVCode);
          if(index == (mobInputLength-1)){
            //console.log("mobVCode==> "+mobVCode);
            //console.log("this.mobileVerificationCode==> "+this.mobileVerificationCode);
            if(this.mobileVerificationCode == parseInt(mobVCode)){
              //code match do something
              console.log("Mobile verification code success===>");
              var data ={
                mobile:true,
                code:this.mobileVerificationCode
              }
              this._sharedService.mobileOrEmailVerified.emit(data);
              this.resetVerification({mobile:true});
            }else{
              this.showMessage({type:"danger",message:"Code Mismatch please try again"});
            }
          }
        });
      }else if(params.email){
        var emailVCode ="";
        var emailInput= $('input.email_input');
        var emailInputLength = emailInput.length;
        emailInput.each((index,elem)=> {
          if(elem.value!=""){
            emailVCode =emailVCode+elem.value;
          }
          if(index == (emailInputLength-1)){
            if(this.emailVerificationCode == parseInt(emailVCode)){
              //code match do something
              console.log("Mobile verification code success===>");
              var data ={
                email:true,
                code:this.emailVerificationCode
              }
              this._sharedService.mobileOrEmailVerified.emit(data);
              this.resetVerification({email:true});
            }else{
              this.showMessage({type:"danger",message:"Code Mismatch please try again"});
            }
          }
        });
      }
    }

    showMessage(data){
      this._structureService.notifyMessage({
        messge: data.message,
        type: data.type,
        allow_dismiss: true,
        delay: 0
      }); 
    }
   
    resetVerification(params){
      params.condition=false;
      this.showOrHideVerificationPopup(params);
      if(params.mobile){
        this.verifyMobCodeSend =false;
        this.mobileVerificationCode =false;
        this.verifyMobileCodeEnter =false;
        this.mobileVerificationTimmerText="";
        clearInterval(this.mobileTimmer);
        this.countryCode='';
        this.mobileNumber='';
      }else if(params.email){
        this.emailVerificationCode;
        this.verifyEmailCode =false;
        this.verifyEmailCodeSend =false;
        this.emailVerificationTimmerText;
        clearInterval(this.emailTimmer);
        this.emailId='';
      }
    
    }

  }
  function openVerifyButton(params){
    if(params.mobile){
      document.getElementById("verify_mobcode").style.display='block';
    }else if(params.email){ 
      document.getElementById("verify_emailcode").style.display='block';
    }
  }
