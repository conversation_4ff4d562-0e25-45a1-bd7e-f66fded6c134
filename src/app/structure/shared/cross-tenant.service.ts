import { Injectable } from '@angular/core';
import 'rxjs/add/operator/toPromise';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';
import { SharedService } from './sharedServices';
@Injectable()
export class CrossTenantService {
  
  crossTenantId  = this._structureService.getCookie('crossTenant');
  constructor(private _structureService:StructureService,public _SharedService:SharedService,
    private router: Router,) {}
  setInboxData(tenantdataconfigured){
    console.log("CrossTenantService         --- this.crossTenantId==============>",this.crossTenantId)
    var userData = this._structureService.getUserdata(); 
    // if(tenantdataconfigured) {
      this._SharedService.crossTenantChange.emit({crossTenantId:this.crossTenantId,tenantdataconfigured:tenantdataconfigured});
      if(this._structureService.canActivateRouteCheck(false))
      if(userData.defaultPage){
       this.router.navigate([userData.defaultPage || '/profile']);
      } else {
       this.router.navigate(['/inbox']);
      }
    // }
	// if(this.router.url.indexOf("inbox/chatroom")>-1 || this.router.url.indexOf("inbox/chatrooms")>-1) {
    //   this.router.navigate(['/inbox']);
    // } else if((this.router.url.indexOf("message/message")>-1 && this.router.url.substr(this.router.url.lastIndexOf('/') + 1) == 'message') || this.router.url.indexOf("message/add-message-groups")>-1 || this.router.url.indexOf("message/edit-message-group")>-1) {
    //   this.router.navigate(['message/message']);
    // } else if(this.router.url.indexOf("message/patient-discussion-groups")>-1 || this.router.url.indexOf("message/add-patient-discussion-groups")>-1 || this.router.url.indexOf("message/edit-patient-discussion-groups")>-1) {
    //   this.router.navigate(['message/patient-discussion-groups']);
    // }  else if(this.router.url.indexOf("message/tag-definitions")>-1 || this.router.url.indexOf("message/add-message-tag")>-1 || this.router.url.indexOf("message/edit-tag/")>-1) {
    //   this.router.navigate(['message/tag-definitions']);
    // } else if(this.router.url.indexOf("message/tag-type")>-1 || this.router.url.indexOf("message/add-message-tag-type")>-1 || this.router.url.indexOf("message/edit-tag-type/")>-1) {
    //   this.router.navigate(['message/tag-type']);
    // } else if(this.router.url.indexOf("message/logs")>-1) {
    //   this.router.navigate(['message/logs']);
    // } else if(this.router.url.indexOf("users/staff")>-1) {
    //   this.router.navigate(['users/staff']);
    // } else if(this.router.url.indexOf("users/partners")>-1) {
    //   this.router.navigate(['users/partners']);
    // } else if(this.router.url.indexOf("users/patients")>-1) {
    //   this.router.navigate(['users/patients']);
    // } else if(this.router.url.indexOf("users/user-tag")>-1) {
    //   this.router.navigate(['users/user-tags']);
    // } else if(this.router.url.indexOf("/users/tag-type")>-1 || this.router.url.indexOf("/users/add-user-tag-type")>-1 || this.router.url.indexOf("/users/edit-tag-type")>-1) {
    //   this.router.navigate(['/users/tag-type']);
    // } else if(this.router.url.indexOf("/supplies/inventory-templates")>-1 || this.router.url.indexOf("/supplies/add-inventory-type")>-1 || this.router.url.indexOf("supplies/inventory-subcategory")>-1 || this.router.url.indexOf("supplies/add-inventory-subcategory")>-1) {
    //   this.router.navigate(['/supplies/inventory-templates']);
    // } else if(this.router.url.indexOf("/supplies/inventory-submissions")>-1) {
    //   this.router.navigate(['/supplies/inventory-submissions']);
    // } else if(this.router.url.indexOf("/masked/message")>-1) {
    //   this.router.navigate(['/masked/message']);
    // } else if(this.router.url.indexOf("/message/message-broadcast")>-1) {
    //   this.router.navigate(['/message/message-broadcast']);
    // } else if(this.router.url.indexOf("/archive")>-1) {
    //   this.router.navigate(['/archive']);
    // }else if(this.router.url.indexOf("/roles-and-privileges/staff/privileges")>-1) {
    //   this.router.navigate(['/roles-and-privileges/staff/role']);
    // }else if(this.router.url.indexOf("/roles-and-privileges/partner/privileges")>-1) {
    //   this.router.navigate(['/roles-and-privileges/partner/role']);
    // }else if(this.router.url.indexOf("/forms/add-form-tag")>-1) {
    //   this.router.navigate(['/forms/form-tags']);
    // }else if(this.router.url.indexOf("/forms/add-form-shortcut")>-1) {
    //   this.router.navigate(['/forms/form-shortcuts']);
    // } else if(this.router.url.indexOf("/user-registrations/register/")>-1){
    //   this.router.navigate(['/user-registrations']);
    // }
  }
}

