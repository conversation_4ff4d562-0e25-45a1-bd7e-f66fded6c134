<div id="lightpad" class="white_content">
    <div class="sign-pad" >
        <signature-pad id="spad" [options]="signaturePadOptions" (onBeginEvent)="drawStart()" (onEndEvent)="drawComplete()"></signature-pad>
        </div>
        <div>
            <button class="btn btn-info" (click)="sendSignature()">
                <span [ngClass]="buttonSave ? 'save' : 'accept'">
                    {{ buttonSave ? ('BUTTONS.SAVE' | translate) : ('BUTTONS.ACCEPT' | translate) }}
                </span>
            </button>
            <button class="btn btn-info" (click)="clear()">{{'BUTTONS.CLEAR' | translate}}</button>
            <button class="btn btn-info" (click)="sendSignature(true)">{{'BUTTONS.CANCEL' | translate}}</button>
    </div>

</div>
<div id="fadepad" class="black_overlay"></div>
