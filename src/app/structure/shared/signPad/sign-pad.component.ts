import { Component, OnInit,ViewChild,Output,EventEmitter,Input } from '@angular/core';
import { SignaturePad } from '../../../../assets/lib/angular2-signaturepad/signature-pad';
//import { SignaturePad } from 'angular2-signaturepad/signature-pad';
import {NgbModal, ModalDismissReasons} from '@ng-bootstrap/ng-bootstrap';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';

@Component({
  selector: 'app-sign-pad',
  templateUrl: './sign-pad.component.html',
  styleUrls: ['./sign-pad.component.css']
})
export class SignPadComponent implements OnInit {

  closeResult: string;
  @Input() buttonSave :boolean;
  @Output('send') sendsign = new EventEmitter();
  userDetails: any;
  @Output('newcomp') newc = new EventEmitter();
  signature='';
  @ViewChild(SignaturePad) signaturePad: SignaturePad;
  public signaturePadOptions: Object = { // passed through to szimek/signature_pad constructor
    'minWidth': 0.1,
    'canvasWidth': 490,
    'canvasHeight': 180
  };

  constructor(private modalService: NgbModal, private structureService: StructureService,private toolTipService:ToolTipService) { 
    this.userDetails = structureService.getUserdata();
  }

  ngOnInit() {
  }

  open(content) {
    this.modalService.open(content).result.then((result) => {
      this.closeResult = `Closed with: ${result}`;
    }, (reason) => {
      this.closeResult = `Dismissed ${this.getDismissReason(reason)}`;
    });
  }

   private getDismissReason(reason: any): string {
    if (reason === ModalDismissReasons.ESC) {
      return 'by pressing ESC';
    } else if (reason === ModalDismissReasons.BACKDROP_CLICK) {
      return 'by clicking on a backdrop';
    } else {
      return  `with: ${reason}`;
    }
  }

  sendSignature(isClose = false) {
    const obj: any = {
      signature: this.signature,
      close: isClose
    }
    const savedUserSignatre = sessionStorage.getItem('savedUserSignature') ? sessionStorage.getItem('savedUserSignature') : this.userDetails.savedUserSignature ? this.userDetails.savedUserSignature : '';
    const enableSaveSignFlow = sessionStorage.getItem('enableSaveSignFlow') ? JSON.parse(sessionStorage.getItem('enableSaveSignFlow')) : null;
    const showUserSignaturePopup = sessionStorage.getItem('showUserSignaturePopup') ? JSON.parse(sessionStorage.getItem('showUserSignaturePopup')) : null;
    if (this.structureService.isEnableSaveSignatureConfig() && enableSaveSignFlow && !isClose && !this.buttonSave) {
      if (!savedUserSignatre && showUserSignaturePopup) {
        this.structureService
      .showAlertMessagePopup({
            text: this.toolTipService.getTranslateData('MESSAGES.SAVE_SIGNATURE_TO_PROFILE'), // If the sign is not in the profile.
            cancelButtonText: this.toolTipService.getTranslateData('BUTTONS.NO'),
            confirmButtonText: this.toolTipService.getTranslateData('BUTTONS.YES')
          })
          .then((confirm) => {
            if (confirm) {
              obj.saveToProfile = true;
            } else {
              obj.saveToProfile = false;
            }
            this.newc.emit(obj);
            this.clear();
          });
      } else {
        this.newc.emit(obj);
        this.clear();
      }
    } else {
      this.newc.emit(obj);
      this.clear();
      if (this.buttonSave && !isClose) {
        this.sendsign.emit(obj);
      }
    }
    this.hide_popup();
  }
  ngAfterViewInit() {
    // this.signaturePad is now available
    this.signaturePad.set('minWidth', 0.1); // set szimek/signature_pad options at runtime
    this.signaturePad.clear(); // invoke functions from szimek/signature_pad API
  }
 
  drawComplete() {
    // will be notified of szimek/signature_pad's onEnd event
    //console.log(this.signaturePad.toDataURL());
    this.signature = this.signaturePad.toDataURL('image/png', 0.1);
  }
 
  drawStart() {
    // will be notified of szimek/signature_pad's onBegin event
    //console.log('begin drawing');
  }


  show_popup()
  {
    document.getElementById('lightpad').style.display='block';
    document.getElementById('fadepad').style.display='block'
  }

  helloo()
  {
    //console.log('helloo');
  }
  
  hide_popup()
  {   
    document.getElementById('lightpad').style.display='none';
    document.getElementById('fadepad').style.display='none';
    
  }

  public clear(): void {
    this.signaturePad.clear();
    this.signature = '';
  }

}
