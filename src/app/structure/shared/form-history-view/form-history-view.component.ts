import {
  Component,
  OnChanges,
  Input,
  Output,
  EventEmitter,
} from '@angular/core';
import { FormpPipe } from 'app/structure/forms/formp.pipe';
import { FormsService } from 'app/structure/forms/forms.service';
import { StructureService } from 'app/structure/structure.service';
import { isBlank } from 'app/utils/utils';
import { ToolTipService } from 'app/structure/tool-tip.service';
declare let $: any;
@Component({
  selector: 'app-form-history',
  templateUrl: './form-history-view.component.html',
})
export class FormHistoryViewComponent implements OnChanges {
  @Input('showHistory') showHistory: boolean;
  @Input('showDraftHistory') showDraftHistory: boolean;
  @Input('formDetails') formDetails: any;
  @Input('isActive') isActive: string;
  @Output() closeModal = new EventEmitter();
  @Output() closeDraftModal = new EventEmitter();
  historyLoaded: boolean;
  historyData = [];
  userData;
  caregiverfname;
  caregiverlname;
  historyDataLength = 0;
  constructor(
    public _formsService: FormsService,
    private formpPipe: FormpPipe,
    public _structureService: StructureService,
    public toolTipService: ToolTipService,
  ) {}

  ngOnChanges() {
    this.userData = JSON.parse(this._structureService.userDetails);
    if (this.showHistory === true) {
      $('#historyDetailsModel').modal('show');
      this.getFormHistory();
    }
    if (this.showDraftHistory === true) {
      this.getDraftFormHistory();
    }
  }
  setWebHookTime(element): any {
    return (
      (this.formpPipe.transform(parseInt(element.created_on) * 1000).length >= 9
        ? ' on '
        : ' at ') +
      this.formpPipe.transform(parseInt(element.created_on) * 1000)
    );
  }
  getFormHistory() {
    this.historyLoaded = false;
    this.historyData = [];
    const datacheck = {
      formid: this.formDetails.form_id,
      submissionid: this.formDetails.form_submission_id,
      sendid: this.formDetails.sent_id,
      recipientid: this.formDetails.recipient_id,
      active: this.isActive,
    };
    this._formsService.detailsHistoryForm(datacheck).then(
      (data: any) => {
        let i = 1;
        let pagenavigation = '';
        let submissionshown = '';
        let filetype = '';
        let nameFile = '';
        let updatedOn = '';
        data.forEach((element) => {
          pagenavigation = '';
          submissionshown = '';
          if (!isBlank(element.navigated_pages) && element.navigated_pages != '0') {
            pagenavigation =
              ' on page navigation from ' + element.navigated_pages;
          }
          if (element.submission_id != '0' && !isBlank(element.submission_id)) {
            submissionshown = ' (Draft ID : ' + element.submission_id + ')';
          }
          if (element.archivefaxq == 1) {
            nameFile = element.archivefaxqfile.split(',')[0];
          } else if (element.archivedirectlink == 1) {
            nameFile = element.archivedirectlinkfile.split(',')[0];
          } else if (element.submitfaxq == 1) {
            nameFile = element.submitfaxqfile.split(',')[0];
          } else if (element.submitdirectlink == 1) {
            nameFile = element.submitdirectlinkfile.split(',')[0];
          }
          filetype = nameFile.split('.').pop().trim();
          if (filetype == 'tif') {
            filetype = 'TIFF';
          } else if (filetype == 'pdf') {
            filetype = 'PDF';
          }

          const createdOn = !isBlank(element.created_on) ? this.formpPipe.transform(
            parseInt(element.created_on) * 1000
          ) : '';
          if (createdOn.length >= 9) {
            updatedOn = ' on ' + createdOn;
          } else {
            updatedOn = ' at ' + createdOn;
          }
          if (element.send == 1) {
            if (element.formSendMode == 'appless') {
              element.patient_email = !isBlank(element.patient_email) ? element.patient_email : '';
              element.patient_mob = !isBlank(element.patient_mob) ? element.patient_mob : '';
              element.patient_name = !isBlank(element.patient_name) ? element.patient_name : '';
              element.created_on =
                element.recepient_firstname +
                ' ' +
                element.recepient_lastname +
                ' sent AppLess (MagicLink) to ' +
                element.patient_email;
              if (!isBlank(element.patient_email) && !isBlank(element.patient_mob)) {
                element.created_on += ' and ';
              }
              if (!isBlank(element.patient_mob)) {
                element.created_on += ' mobile number ' + element.patient_mob;
              }
              const applessSendLink = !isBlank(element.appless_link_send_on) ? this.formpPipe.transform(
                parseInt(element.appless_link_send_on) * 1000
              ) : '';
              element.created_on += applessSendLink.length >= 9
                  ? ' on ' + applessSendLink
                  : ' at ' + applessSendLink;
              clearRecipientName();
            } else {
              if (
                this.userData.config.progress_note_integration_mode ==
                  'webhook' &&
                element.webhookSendPN
              ) {
                this._formsService
                  .setLifeCycleHistory(element, element.webhookSendPN, 'Send')
                  .then((data: any) => {
                    element.created_on = data + this.setWebHookTime(element);
                    clearRecipientName();
                  });
              } else {
                element.created_on = ' sent' + updatedOn;
              }
            }
          } else if (!isBlank(element.sendchie) && [1, 2].includes(+element.sendchie)) {
            const sentVerbiage = createdOn.length >= 9 ? 'MESSAGES.SENT_ON' : 'MESSAGES.SENT_AT';
            element.created_on = ` ${this.toolTipService.getTranslateDataWithParam(sentVerbiage, { dateTime: createdOn })} (${
              +element.sendchie === 1 ? 'CHIE' : 'AFS'
            })`;
          } else if (element.submitted == 1) {
            if (
              element.asso_patient != 0 && !isBlank(element.asso_patientname)
            ) {
              this.caregiverfname = element.recepient_firstname;
              this.caregiverlname = element.recepient_lastname;
              element.recepient_firstname = element.asso_patientname;
              element.recepient_lastname =
                ' (' + this.caregiverfname + ' ' + this.caregiverlname + ')';
            }
            element.created_on = ' submitted' + updatedOn;
            if (!isBlank(element.submission_id)) {
              element.created_on +=
                ' (Submission ID : ' + element.submission_id + ')';
            }
          } else if (
            element.webhookSubmitPN &&
            this.userData.config.progress_note_integration_mode == 'webhook'
          ) {
            this._formsService
              .setLifeCycleHistory(element, element.webhookSubmitPN, 'Submit')
              .then((data: any) => {
                element.created_on = data + this.setWebHookTime(element);
                clearRecipientName();
              });
          } else if (
            element.webhoookDocExchangeSubmit &&
            this.userData.config.documet_exchange_mode == 'wh'
          ) {
            this._formsService
              .setLifeCycleHistory(
                element,
                element.webhoookDocExchangeSubmit,
                'Submit'
              )
              .then((data: any) => {
                element.created_on = data + this.setWebHookTime(element);
                clearRecipientName();
              });
          } else if (element.allowedited == 1 && element.formSendMode == '') {
            element.created_on = ' allow edit performed' + updatedOn;
          } else if (
            element.allowedited == 1 &&
            element.formSendMode == 'appless'
          ) {
            let msg = '  allow edit performed ';
            element.patient_email = !isBlank(element.patient_email) ? element.patient_email : '';
            element.patient_mob = !isBlank(element.patient_mob) ? element.patient_mob : '';
            element.patient_name = !isBlank(element.patient_name) ? element.patient_name : '';
            msg += 'and  AppLess (MagicLink) sent to';
            if (!isBlank(element.patient_email)) {
              msg += ' email ' + element.patient_email;
            }
            if (!isBlank(element.patient_mob) && !isBlank(element.patient_email)) {
              msg += ' and ';
            }
            if (!isBlank(element.patient_mob)) {
              msg += '  mobile number ' + element.patient_mob;
            }
            msg += updatedOn;
            element.created_on = msg;
          } else if (element.reviewed) {
            const message =
              element.reviewed == 1 ? 'reviewed' : 'review canceled';
            element.created_on = message + updatedOn;
          } else if (element.restored == 1) {
            element.created_on = ' restored' + updatedOn;
          } else if (element.archived == 1) {
            element.created_on = ' archived' + updatedOn;
          }  else if (element.cancelled === 1) {
            element.created_on = ' canceled' + updatedOn;
          } else if (
            element.webhookArchivePN &&
            this.userData.config.progress_note_integration_mode == 'webhook'
          ) {
            this._formsService
              .setLifeCycleHistory(element, element.webhookArchivePN, 'Archive')
              .then((data: any) => {
                element.created_on = data + this.setWebHookTime(element);
                clearRecipientName();
              });
          } else if (
            element.webhoookDocExchangeArchive &&
            this.userData.config.documet_exchange_mode == 'wh'
          ) {
            this._formsService
              .setLifeCycleHistory(
                element,
                element.webhoookDocExchangeArchive,
                'Archive'
              )
              .then((data: any) => {
                element.created_on = data + this.setWebHookTime(element);
                clearRecipientName();
              });
          } else if (element.alloweditsubmit == 1) {
            if (
              element.asso_patient != 0 &&
              !isBlank(element.asso_patientname)
            ) {
              this.caregiverfname = element.recepient_firstname;
              this.caregiverlname = element.recepient_lastname;
              element.recepient_firstname = element.asso_patientname;
              element.recepient_lastname =
                ' (' + this.caregiverfname + ' ' + this.caregiverlname + ')';
            }
            element.created_on = ' edit completed' + updatedOn;
          } else if (element.denyedited == 1) {
            element.created_on = ' deny edit performed' + updatedOn;
          } else if (element.resend == 1) {
            element.created_on = ' sent reminder' + updatedOn;
          } else if (
            element.reminder == 1 &&
            element.formSendMode != 'appless'
          ) {
            if (
              this.userData.config.progress_note_integration_mode ==
                'webhook' &&
              element.webhookReminderPN
            ) {
              this._formsService
                .setLifeCycleHistory(
                  element,
                  element.webhookReminderPN,
                  'Reminder'
                )
                .then((data: any) => {
                  element.created_on = data + this.setWebHookTime(element);
                });
            } else {
              element.created_on = 'Auto reminder sent' + updatedOn;
            }
            clearRecipientName();
          } else if (
            element.reminder == 1 &&
            element.formSendMode == 'appless'
          ) {
            element.patient_email = !isBlank(element.patient_email) ? element.patient_email : '';
            element.patient_mob = !isBlank(element.patient_mob) ? element.patient_mob : '';
            element.patient_name = !isBlank(element.patient_name) ? element.patient_name : '';
            let message = ' AppLess (MagicLink)  auto reminder sent to';
            if (!isBlank(element.patient_email)) {
              message += ' email ' + element.patient_email;
            }
            if (!isBlank(element.patient_mob) && !isBlank(element.patient_email)) {
              message += ' and ';
            }
            if (!isBlank(element.patient_mob)) {
              message += ' mobile number ' + element.patient_mob;
            }
            message += updatedOn;
            element.created_on = message;
            if (
              this.userData.config.progress_note_integration_mode ==
                'webhook' &&
              element.webhookReminderPN
            ) {
              this._formsService
                .setLifeCycleHistory(
                  element,
                  element.webhookReminderPN,
                  'Reminder'
                )
                .then((data: any) => {
                  element.created_on = data + this.setWebHookTime(element);
                });
            }
            clearRecipientName();
          } else if (element.savedasdraft == 1) {
            if (i == data.length) {
              element.created_on =
              (element.sync_save == 0 ? ' draft manually created ': ' draft synced ') +
                pagenavigation +
                updatedOn +
                ' ' +
                submissionshown;
            } else {
              element.created_on =
              (element.sync_save == 0 ? ' draft manually modified ': ' draft synced ') +
                pagenavigation +
                updatedOn +
                ' ' +
                submissionshown;
            }
          } else if (element.autosavedasdraft == 1) {
            if (i == data.length) {
              element.created_on =
                ' draft auto  created  ' +
                pagenavigation +
                updatedOn +
                ' ' +
                submissionshown;
            } else {
              element.created_on =
                ' draft auto  modified  ' +
                pagenavigation +
                updatedOn +
                ' ' +
                submissionshown;
            }
          } else if (
            element.validation_error == '1' &&
            element.navigated_pages != '' &&
            element.navigated_pages != '0'
          ) {
            element.created_on =
              ' tried page navigation but field validation failed and keeping on the page ' +
              element.navigated_pages +
              updatedOn;
          } else if (!isBlank(element.file_name) && !isBlank(element.file_field_id)) {
            let deleteduploaded = '';
            if (element.file_upload == 0) {
              deleteduploaded = 'deleted from';
            } else {
              deleteduploaded = 'uploaded to';
            }
            element.created_on =
              ' File ' +
              deleteduploaded +
              ' field - ' +
              element.file_field_name +
              ' by ' +
              element.recepient_firstname +
              ' ' +
              element.recepient_lastname +
              updatedOn +
              ' (File Name: ' +
              element.file_name +
              ')';
            clearRecipientName();
          } else if (element.archivefaxq == 1) {
            element.created_on =
              ' Form sent to Fax Queue as ' +
              filetype +
              ' for ' +
              element.archivepatientname +
              ' on archive by ' +
              element.recepient_firstname +
              ' ' +
              element.recepient_lastname +
              updatedOn +
              ' (File Name: ' +
              element.archivefaxqfile +
              ')';
              clearRecipientName();
          } else if (element.archivedirectlink == 1) {
            element.created_on =
              ' Form Direct Linked to Patient Chart as ' +
              filetype +
              ' for ' +
              element.archivepatientname +
              ' on archive by ' +
              element.recepient_firstname +
              ' ' +
              element.recepient_lastname +
              updatedOn +
              ' (File Name: ' +
              element.archivedirectlinkfile +
              ')';
              clearRecipientName();
          } else if (element.archivePN == 1) {
            element.created_on =
              ' Form sent to Progress Note for ' +
              element.archivepatientname +
              '  on archive by ' +
              element.recepient_firstname +
              ' ' +
              element.recepient_lastname +
              updatedOn +
              ' (File Name: ' +
              element.archivePNfile +
              ')';
              clearRecipientName();
          } else if (element.archivePHI == 1) {
            element.created_on =
              ' Form sent to PHI for ' +
              element.archivepatientname +
              ' on archive by ' +
              element.recepient_firstname +
              ' ' +
              element.recepient_lastname +
              updatedOn +
              ' (File Name: ' +
              element.archivePHIfile +
              ')';
              clearRecipientName();
          } else if (element.sendPN == 1) {
            element.created_on =
              ' Form sent to Progress Note for ' +
              element.archivepatientname +
              '  on send by ' +
              element.recepient_firstname +
              ' ' +
              element.recepient_lastname +
              updatedOn +
              ' (File Name: ' +
              element.sendpnfile +
              ')';
              clearRecipientName();
          } else if (element.reminderPN == 1) {
            element.created_on =
              ' Form sent to Progress Note for ' +
              element.archivepatientname +
              ' on reminder by ' +
              element.recepient_firstname +
              ' ' +
              element.recepient_lastname +
              updatedOn +
              ' (File Name: ' +
              element.reminderpnfile +
              ')';
              clearRecipientName();
          } else if (element.submitfaxq == 1) {
            if (element.submitgenerate == 1) {
              element.created_on =
                'Form sent to Fax Queue as ' +
                filetype +
                ' for ' +
                element.archivepatientname +
                "  via 'Send to EHR' by " +
                element.recepient_firstname +
                ' ' +
                element.recepient_lastname +
                updatedOn +
                ' (File Name: ' +
                element.submitfaxqfile +
                ')';
                clearRecipientName();
            } else {
              if (element.stafffacingsubmit == 1) {
                element.created_on = element.created_on =
                  ' Form sent to Fax Queue as ' +
                  filetype +
                  ' ' +
                  ' on submission by ' +
                  element.archivepatientname +
                  updatedOn +
                  ' (File Name: ' +
                  element.submitfaxqfile +
                  ')';
                  clearRecipientName();
              } else {
                element.created_on =
                  ' Form sent to Fax Queue as ' +
                  filetype +
                  ' for ' +
                  element.archivepatientname +
                  ' on submission by ' +
                  element.recepient_firstname +
                  ' ' +
                  element.recepient_lastname +
                  updatedOn +
                  ' (File Name: ' +
                  element.submitfaxqfile +
                  ')';
                  clearRecipientName();
              }
            }
          } else if (element.submitdirectlink == 1) {
            if (element.submitgenerate == 1) {
              element.created_on =
                'Form Direct Linked to Patient Chart as ' +
                filetype +
                ' for ' +
                element.archivepatientname +
                "  via 'Send to EHR' by " +
                element.recepient_firstname +
                ' ' +
                element.recepient_lastname +
                updatedOn +
                ' (File Name: ' +
                element.submitdirectlinkfile +
                ')';
                clearRecipientName();
            } else {
              if (element.stafffacingsubmit == 1) {
                element.created_on =
                  ' Form Direct Linked to Patient Chart as ' +
                  filetype +
                  ' ' +
                  ' on submission by ' +
                  element.archivepatientname +
                  updatedOn +
                  ' (File Name: ' +
                  element.submitdirectlinkfile +
                  ')';
                  clearRecipientName();
              } else {
                element.created_on =
                  ' Form Direct Linked to Patient Chart as ' +
                  filetype +
                  ' for ' +
                  element.archivepatientname +
                  ' on submission by ' +
                  element.recepient_firstname +
                  ' ' +
                  element.recepient_lastname +
                  updatedOn +
                  ' (File Name: ' +
                  element.submitdirectlinkfile +
                  ')';
                  clearRecipientName();
              }
            }
          } else if (element.submitPN == 1) {
            if (element.submitgenerate == 1) {
              element.created_on =
                ' Form sent to Progress Note for ' +
                element.archivepatientname +
                "  via 'Send to EHR' by " +
                element.recepient_firstname +
                ' ' +
                element.recepient_lastname +
                updatedOn +
                ' (File Name: ' +
                element.submitPNfile +
                ')';
                clearRecipientName();
            } else {
              if (element.stafffacingsubmit == 1) {
                element.created_on =
                  ' Form sent to Progress Note ' +
                  ' on submission by ' +
                  element.archivepatientname +
                  updatedOn +
                  ' (File Name: ' +
                  element.submitPNfile +
                  ')';
                  clearRecipientName();
              } else {
                element.created_on =
                  ' Form sent to Progress Note for ' +
                  element.archivepatientname +
                  ' on submission by ' +
                  element.recepient_firstname +
                  ' ' +
                  element.recepient_lastname +
                  updatedOn +
                  ' (File Name: ' +
                  element.submitPNfile +
                  ')';
                  clearRecipientName();
              }
            }
          } else if (element.submitPHI == 1) {
            if (element.submitgenerate == 1) {
              element.created_on =
                ' Form sent to PHI for ' +
                element.archivepatientname +
                "  via 'Send to EHR' by " +
                element.recepient_firstname +
                ' ' +
                element.recepient_lastname +
                updatedOn +
                ' (File Name: ' +
                element.submitPHIFile +
                ')';
                clearRecipientName();
            } else {
              if (element.stafffacingsubmit == 1) {
                element.created_on =
                  ' Form sent to PHI ' +
                  ' on submission by ' +
                  element.archivepatientname +
                  updatedOn +
                  ' (File Name: ' +
                  element.submitPHIFile +
                  ')';
                  clearRecipientName();
              } else {
                element.created_on =
                  ' Form sent to PHI for ' +
                  element.archivepatientname +
                  ' on submission by ' +
                  element.recepient_firstname +
                  ' ' +
                  element.recepient_lastname +
                  updatedOn +
                  ' (File Name: ' +
                  element.submitPHIFile +
                  ')';
                  clearRecipientName();
              }
            }
          } else if (element.appless_link_clicked == 1) {
            clearRecipientName();
            element.patient_email = !isBlank(element.patient_email) ? element.patient_email : '';
            element.patient_mob = !isBlank(element.patient_mob) ? element.patient_mob : '';
            element.patient_name = !isBlank(element.patient_name) ? element.patient_name : '';
            const message =
              'AppLess (MagicLink) accessed by ' + element.patient_name;
            const applessLinkClick = !isBlank(element.appless_link_clicked_on) ?  this.formpPipe.transform(
              parseInt(element.appless_link_clicked_on) * 1000
            ) : '';
            const clickedOn = applessLinkClick.length >= 9
                ? ' on ' + applessLinkClick
                : ' at ' + applessLinkClick;
            element.created_on =
              message + ' by clicking link ' + element.urlType + clickedOn;
          } else if (element.appless_access_code_send == 1) {
            clearRecipientName();
            element.patient_email = !isBlank(element.patient_email) ? element.patient_email : '';
            element.patient_mob = !isBlank(element.patient_mob) ? element.patient_mob : '';
            element.patient_name = !isBlank(element.patient_name) ? element.patient_name : '';
            let message = 'AppLess (MagicLink) verification  code sent to ';
            if (element.appless_access_code_send_medium == 1) {
              message += ' email ' + element.patient_email;
            } else {
              message += ' mobile number ' + element.patient_mob;
            }
            const applessCodeSend = !isBlank(element.appless_code_send_on) ? this.formpPipe.transform(
              parseInt(element.appless_code_send_on) * 1000
            ) : '';
            const sendOn = applessCodeSend.length >= 9
                ? ' on ' + applessCodeSend
                : ' at ' + applessCodeSend;
            element.created_on = message + sendOn;
          } else if (element.appless_access_code_resend == 1) {
            clearRecipientName();
            element.patient_email = !isBlank(element.patient_email) ? element.patient_email : '';
            element.patient_mob = !isBlank(element.patient_mob) ? element.patient_mob : '';
            element.patient_name = !isBlank(element.patient_name) ? element.patient_name : '';
            let message =
              ' AppLess (MagicLink) code requested by ' +
              element.patient_name +
              ' and code is sent to ';
            if (element.appless_access_code_send_medium == 1) {
              message += element.patient_email;
            } else {
              message += element.patient_mob;
            }
            const applessCodeResend =  !isBlank(element.appless_code_resend_on) ? this.formpPipe.transform(
              parseInt(element.appless_code_resend_on) * 1000
            ) : '';
            const resendOn =
              applessCodeResend.length >= 9
                ? ' on ' + applessCodeResend
                : ' at ' + applessCodeResend;
            element.created_on = message + resendOn;
          } else if (element.appless_access_code_verified == 1) {
            clearRecipientName();
            element.patient_email = !isBlank(element.patient_email) ? element.patient_email : '';
            element.patient_mob = !isBlank(element.patient_mob) ? element.patient_mob : '';
            element.patient_name = !isBlank(element.patient_name) ? element.patient_name : '';
            let message =
              'AppLess (MagicLink) access code successfully verified for ' +
              element.patient_name +
              ' at ';
            if (element.appless_access_code_send_medium == 1) {
              message += element.patient_email;
            } else {
              message += element.patient_mob;
            }
            const applessCodeVerified = !isBlank(element.appless_access_code_verified_on) ? this.formpPipe.transform(
              parseInt(element.appless_access_code_verified_on) * 1000
            ) : '';
            const verifiedOn = applessCodeVerified.length >= 9
                ? ' on ' + applessCodeVerified
                : ' at ' + applessCodeVerified;
            element.created_on = message + verifiedOn;
          } else if (element.appless_access_code_verified == 0) {
            element.patient_email = !isBlank(element.patient_email) ? element.patient_email : '';
            element.patient_mob = !isBlank(element.patient_mob) ? element.patient_mob : '';
            element.patient_name = !isBlank(element.patient_name) ? element.patient_name : '';
            let message =
              'AppLess (MagicLink) access code verification failed for ' +
              element.patient_name +
              ' at ';
            if (element.appless_access_code_send_medium == 1) {
              message += element.patient_email;
            } else {
              message += element.patient_mob;
            }
            const applessCodeVerified = !isBlank(element.appless_access_code_verified_on) ? this.formpPipe.transform(
              parseInt(element.appless_access_code_verified_on) * 1000
            ) : '';
            const verifiedOn = applessCodeVerified.length >= 9
                ? ' on ' + applessCodeVerified
                : ' at ' + applessCodeVerified;
            element.created_on = message + verifiedOn;
            clearRecipientName();
          } else if (element.appless_access_granted == 1) {
            clearRecipientName();
            element.patient_email = !isBlank(element.patient_email) ? element.patient_email : '';
            element.patient_mob = !isBlank(element.patient_mob) ? element.patient_mob : '';
            element.patient_name = !isBlank(element.patient_name) ? element.patient_name : '';
            let message =
              'AppLess (MagicLink) access to form for ' +
              element.patient_name +
              ' ';
            if (element.appless_access_code_send_medium == 1) {
              message += element.patient_email;
            } else {
              message += element.patient_mob;
            }
            message += ' was granted ';
            const applessAccessgrand = !isBlank(element.appless_access_granted_on) ? this.formpPipe.transform(
              parseInt(element.appless_access_granted_on) * 1000
            ) : '';
            const grandOn = applessAccessgrand.length >= 9
                ? ' on ' + applessAccessgrand
                : ' at ' + applessAccessgrand;
            element.created_on = message + grandOn;
          } else if (element.appless_reminder_send == 1) {
            element.patient_email = !isBlank(element.patient_email) ? element.patient_email : '';
            element.patient_mob = !isBlank(element.patient_mob) ? element.patient_mob : '';
            let message =
              element.recepient_firstname +
              ' ' +
              element.recepient_lastname +
              ' sent AppLess Reminder for ' +
              element.patient_name +
              ' via ';
            if (!isBlank(element.patient_email)) {
              message += element.patient_email;
            }
            if (!isBlank(element.patient_mob) && !isBlank(element.patient_email)) {
              message += ' and ';
            }
            if (!isBlank(element.patient_mob)) {
              message += element.patient_mob;
            }
            const applessReminder = !isBlank(element.appless_reminder_send_on) ? this.formpPipe.transform(
              parseInt(element.appless_reminder_send_on) * 1000
            ) : '';
            const reminderOn =
              applessReminder.length >= 9
                ? ' on ' + applessReminder
                : ' at ' + applessReminder;
            element.created_on = message + reminderOn;
            clearRecipientName();
          } else if (
            element.esignature_log == 1 ||
            element.esignature_log == 2
          ) {
            this._formsService
              .setESignatureAuditLogLifeCycleHistory(
                element,
                createdOn
              )
              .then((signData) => {
                clearRecipientName();
                element.created_on = signData;
              });
          } else if (element.appless_access_download_link === 1 && 
            !isBlank(element.appless_send_download_link_recipient)) {
            /**Recipient accessed the appless link for download the comlpleted form. */
            const applessLink = !isBlank(element.appless_link_clicked_on) ? this.formpPipe.transform(
              parseInt(element.appless_link_clicked_on) * 1000
            ) : '';
            const accessOn = applessLink.length >= 9 ? ' on ' + applessLink : ' at ' + applessLink;
            element.created_on =
              'AppLess (MagicLink) accessed by ' +
              element.appless_send_download_link_recipient +
              ' by clicking link' +
              accessOn;
              clearRecipientName();
          } else if (element.appless_completed_form_download === 1 && 
            !isBlank(element.appless_send_download_link_recipient)) {
            /**Recipient downloaded the comlpleted form. */
            const downloadLink = !isBlank(element.appless_link_clicked_on) ? this.formpPipe.transform(
              parseInt(element.appless_link_clicked_on) * 1000
            ) : '';
            const downloadOn = downloadLink.length >= 9 ? ' on ' + downloadLink : ' at ' + downloadLink;
            element.created_on = element.appless_send_download_link_recipient +
              ' downloaded the completed form' +
              downloadOn;
              clearRecipientName();
          } else if (
            !isBlank(element.appless_send_download_link) && 
            !isBlank(element.appless_send_download_link_recipient) && element.appless_resend_completed_form !== 1
          ) {
            /**Sent appless link to recipients for downloading the completed form */
            element.created_on = 'AppLess (MagicLink) for completed form download sent to ' +
              element.appless_send_download_link_recipient + '(';
            if(!isBlank(element.patient_email)) {
              element.created_on += element.patient_email;
              if(!isBlank(element.patient_mob)) {
                element.created_on += ' and ' + element.patient_mob;
              }
            } else if(!isBlank(element.patient_mob)) {
              element.created_on += element.patient_mob;
            }
            element.created_on +=  ')' + updatedOn;
            clearRecipientName();
          } else if (element.appless_resend_completed_form === 1 &&
            !isBlank(element.appless_send_download_link_recipient)) {
            /**Resent the appless link to recipients for downloading */
            element.created_on =
              'resent AppLess (MagicLink) for completed form download to ' +
              element.appless_send_download_link_recipient +
              '(';
              if(!isBlank(element.patient_email)) {
                element.created_on += element.patient_email;
                if(!isBlank(element.patient_mob)) {
                  element.created_on += ' and ' + element.patient_mob;
                }
              } else if(!isBlank(element.patient_mob)) {
                element.created_on += element.patient_mob;
              }
              element.created_on +=  ')' + updatedOn;
          } else if (!isBlank(element.appless_download_recipient_selection)) {
            /** User selected the recipients for sending appless link for download the completed form. */
            const recipientNamesArray = element.appless_download_recipient_selection.split(',');
            let recipientNames = element.appless_download_recipient_selection;
            if(recipientNamesArray.length > 1) {
              recipientNames =
              recipientNamesArray.slice(0, -1).join(', ') +
              ' and ' +
              recipientNamesArray.slice(-1);
            }
            element.created_on = 'selected the form download recipient as ' +
              recipientNames +
              updatedOn +
              submissionshown;
          } else if(!isBlank(element.appless_download_remove_recipient_selection)) {
            const recipientNamesArray = element.appless_download_remove_recipient_selection.split(',');
            let recipientNames = element.appless_download_remove_recipient_selection;
            if(recipientNamesArray.length > 1) {
              recipientNames =
              recipientNamesArray.slice(0, -1).join(', ') +
              ' and ' +
              recipientNamesArray.slice(-1);
            }
            element.created_on =
              'removed ' +
              recipientNames +
              ' from the recipients for completed form download' +
              updatedOn;
          } else if (element.pendingToCompleted === 1) {
              element.created_on = `${this.toolTipService.getTranslateData('LABELS.COMPLETED_THE_FORM_ON')} ${updatedOn}`
          }
          i = i + 1;

          function clearRecipientName(): void {
            element.recepient_firstname = '';
            element.recepient_lastname = '';
          }
        });
        this.historyData = data;
        this.historyDataLength = this.historyData.length;
        this.historyLoaded = true;
      },
      () => {
        $('#historyDetailsModel').modal('hide');
        this.showFailedMsg();
      }
    );
  }
  getDraftFormHistory() {
    const formid = this.formDetails.form_id;
    const subid = this.formDetails.form_submission_id;
    $('#draftHistoryDetailsModel').modal('show');
    this.historyLoaded = false;
    this.historyData = [];
    const datacheck: any = {
      formid: formid,
      submissionid: subid,
    };
    this._formsService.detailsDraftForm(datacheck).then(
      (data: any) => {
        let i = 1;
        let autostring = '';
        let pagenavigation = '';
        let submissionshown = '';
        let updatedOn = '';
        data.forEach((element) => {
          autostring = 'draft manually ';
          if (element.auto_save == 1) {
            autostring = 'draft auto ';
          }
          pagenavigation = '';
          submissionshown = '';
          if (!isBlank(element.navigated_pages) && element.navigated_pages != '0') {
            pagenavigation =
              ' on page navigation from ' + element.navigated_pages;
          }
          if (!isBlank(element.submission_id) && element.submission_id != '0') {
            submissionshown = ' (Draft ID : ' + element.submission_id + ')';
          }
          const modifyOn = !isBlank(element.modifiedAT) ? this.formpPipe.transform(
            parseInt(element.modifiedAT) * 1000
          ) : '';
          if (modifyOn.length >= 9) {
            updatedOn = ' on ' + modifyOn;
          } else {
            updatedOn = ' at ' + modifyOn;
          }
          const createOrModfy = i === data.length ? 'created ' : 'modified ';
          if (
            element.validation_error == '1' &&
            element.navigated_pages != '' &&
            element.navigated_pages != '0'
          ) {
            element.modifiedAT =
              ' tried page navigation but field validation failed and keeping on the page ' +
              element.navigated_pages +
              updatedOn;
          } else if (!isBlank(element.file_name) && !isBlank(element.file_field_id)) {
            let deleteduploaded = '';
            if (element.file_upload == 0) {
              deleteduploaded = 'deleted from';
            } else {
              deleteduploaded = 'uploaded to';
            }
            element.modifiedAT =
              ' File ' +
              deleteduploaded +
              ' field - ' +
              element.file_field_name +
              ' by ' +
              element.sendername +
              updatedOn +
              ' (File Name: ' +
              element.file_name +
              ')';
            element.sendername = '';
          } else if (
            element.esignature_log == 1 ||
            element.esignature_log == 2
          ) {
            this._formsService
              .setESignatureAuditLogLifeCycleHistory(
                element,
                modifyOn
              )
              .then((signData) => {
                element.sendername = '';
                element.modifiedAT = signData;
              });
          } else if (!isBlank(element.appless_download_recipient_selection)) {
            const recipientNamesArray = element.appless_download_recipient_selection.split(',');
            let recipientNames = element.appless_download_recipient_selection;
            if(recipientNamesArray.length > 1) {
              recipientNames =
              recipientNamesArray.slice(0, -1).join(', ') +
              ' and ' +
              recipientNamesArray.slice(-1);
            }
            element.modifiedAT =
              ' selected the form download recipient as ' +
              recipientNames +
              updatedOn +
              submissionshown;
          } else if (!isBlank(element.appless_download_remove_recipient_selection)) {
            const recipientNamesArray = element.appless_download_remove_recipient_selection.split(',');
            let recipientNames = element.appless_download_remove_recipient_selection;
            if(recipientNamesArray.length > 1) {
              recipientNames =
              recipientNamesArray.slice(0, -1).join(', ') +
              ' and ' +
              recipientNamesArray.slice(-1);
            }
            element.modifiedAT =
              ' removed ' +
              recipientNames + ' from the recipients for completed form download' +
              updatedOn +
              submissionshown;
          } else {
            element.modifiedAT =
              ' ' +
              autostring +
              createOrModfy +
              pagenavigation +
              updatedOn +
              submissionshown;
            if (element.sync_save == 1) {
              element.modifiedAT = element.modifiedAT.replace('manually created', 'synced');
              element.modifiedAT = element.modifiedAT.replace('manually modified', 'synced');
            }
          }
          i++;
        });
        this.historyData = data;
        this.historyDataLength = this.historyData.length;
        this.historyLoaded = true;
      },
      () => {
        $('#draftHistoryDetailsModel').modal('hide');
        this.showFailedMsg();
      }
    );
  }
  showFailedMsg() {
    this.historyLoaded = true;
    const msg = this.toolTipService.getTranslateData('ERROR_MESSAGES.FAILED_TO_FETCH');
    setTimeout(function () {
      $.notify(
        { message:  msg},
        { type: 'danger' }
      );
    }, 1000);
  }
  closeHistoryModal() {
    this.closeModal.emit(true);
  }
  closeDraftHistoryModal() {
    this.closeDraftModal.emit(true);
  }
}
