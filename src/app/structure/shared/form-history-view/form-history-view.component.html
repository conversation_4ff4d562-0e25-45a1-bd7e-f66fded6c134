<div class="modal fade bd-example-modal-lg forward-modal" id="historyDetailsModel" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="exampleModalLabel">{{'LABELS.FORM_LIFE_CYCLE_HISTORY' | translate}}</h4>
                <button type="button" class="close" data-dismiss="modal" (click)="closeHistoryModal()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="page-wrap">
                    <div style="text-align:center;width:100%;" *ngIf="!historyLoaded">
                        <img src="./assets/img/loader/loading.gif" />
                    </div>
                    <div class="table-responsive" *ngIf="historyLoaded">
                        <table class="table table-striped" *ngIf="historyData.length">
                            <tr *ngFor="let item of historyData; let i = index">
                                <td>{{ historyDataLength-i }}</td>
                                <td  >{{ item.recepient_firstname }} {{ item.recepient_lastname }} {{ item.created_on }}</td>
                            </tr>
                        </table>
                        <div style="text-align:center;width:100%;" *ngIf="!historyData.length">
                            <p>{{'LABELS.HISTORY_NO_DETAILS_MSG' | translate}}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal fade bd-example-modal-lg forward-modal" id="draftHistoryDetailsModel" tabindex="-1" role="dialog" aria-labelledby="myLargeModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="exampleModalLabels">{{'LABELS.DRAFT_ACTIVITY_HISTORY' | translate}}</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="closeDraftHistoryModal()">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div id="page-wrap">
                    <div style="text-align:center;width:100%;" *ngIf="!historyLoaded">
                        <img src="./assets/img/loader/loading.gif" />
                    </div>
                    <div class="table-responsive" *ngIf="historyLoaded">
                        <table class="table table-striped" *ngIf="historyData.length">
                            <!-- <tr>
                                <th>#</th>
                                <th>Date TIme</th>
                                
                                <th>Sent To</th>
                            </tr> -->
                            <tr *ngFor="let item of historyData; let i = index">
                                <td>{{historyDataLength-i}}</td>
                                <!-- <td></td> -->
                                
                                <td>{{ item.sendername }} {{ item.modifiedAT }}</td>
                            </tr>
                        </table>
                        <div style="text-align:center;width:100%;" *ngIf="!historyData.length">
                            <p>{{'LABELS.DRAFT_HISTORY_NO_DETAILS_MSG' | translate}}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>