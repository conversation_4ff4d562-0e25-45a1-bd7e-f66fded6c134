import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormHistoryViewComponent } from './form-history-view.component';
import { FormsService } from 'app/structure/forms/forms.service';
import { StructureService } from 'app/structure/structure.service';
import { TranslateModule } from '@ngx-translate/core';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { RouterTestingModule } from '@angular/router/testing';
import { provideClients } from 'test-utils';
import { ApolloModule } from 'apollo-angular';
import { AuthService } from '../auth.service';
import { SharedService } from '../sharedServices';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { HttpClientModule } from '@angular/common/http';
import { HttpModule } from '@angular/http';
import { FormpPipe } from 'app/structure/forms/formp.pipe';
import { DatePipe } from '@angular/common';
import { Observable } from 'rxjs/Observable';
import { formHistoryData, draftFormHistoryData } from 'test-data';
import { PermissionService } from 'app/services/permission/permission.service';
import { StoreService } from '../storeService';

describe('FormHistoryViewComponent', () => {
  let component: FormHistoryViewComponent;
  let fixture: ComponentFixture<FormHistoryViewComponent>;
  const spyFormService = jasmine.createSpyObj('FormService', [
    'detailsHistoryForm',
    'detailsDraftForm',
  ]);
  const mockJQuery = jasmine.createSpyObj('$', ['modal', 'notify']);
  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [FormHistoryViewComponent],
      providers: [
        StructureService,
        HttpClient,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        {provide: FormsService, useValue: spyFormService},
        FormpPipe,
        DatePipe,
        PermissionService,
        StoreService
      ],
      imports: [
        RouterTestingModule,
        HttpClientModule,
        HttpModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF,
        }),
        TranslateModule.forRoot(),
      ],
    }).compileComponents();
    fixture = TestBed.createComponent(FormHistoryViewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
  it('should emit true while close history modal', () => {
    const expectedValue = true;
    let emittedValue: boolean;
    component.closeModal.subscribe((value) => {
      emittedValue = value;
    });
    component.closeHistoryModal();
    expect(emittedValue).toEqual(expectedValue);
  });
  it('should emit true while close draft history modal', () => {
    const expectedValue = true;
    let emittedValue: boolean;
    component.closeDraftModal.subscribe((value) => {
      emittedValue = value;
    });
    component.closeDraftHistoryModal();
    expect(emittedValue).toEqual(expectedValue);
  });
  it('should return the form history details and show in modal', async () => {
    const testData = formHistoryData();
    spyFormService.detailsHistoryForm.and.returnValue(Observable.of(testData));
    component.formDetails = {
      form_id: 13530135,
      form_submission_id: 5,
      sent_id: 2183208,
      recipient_id: 2598381,
    };
    component.isActive = 'completed';
    component.getFormHistory();
    fixture.whenStable().then(() => {
      expect(component.historyLoaded).toBe(true);
      expect(component.historyData).toBe(testData);
    });
    const divElements = fixture.nativeElement.querySelectorAll('tr');
    expect(component.historyData.length).toBe(divElements.length);
  });
  it('should return the draft form history details and show in modal', async () => {
    const testData = draftFormHistoryData();
    spyFormService.detailsDraftForm.and.returnValue(Observable.of(testData));
    component.formDetails = {
      form_id: 12578201,
      form_submission_id: 59,
    };
    component.getDraftFormHistory();
    fixture.whenStable().then(() => {
      expect(component.historyLoaded).toBe(true);
      expect(component.historyData).toBe(testData);
      const divElements = fixture.nativeElement.querySelectorAll('tr');
      expect(component.historyData.length).toBe(divElements.length);
    });
  });
  it('should handle failure to fetch history details', () => {
    mockJQuery.modal.and.returnValue(mockJQuery);
    spyOn(window, '$').and.returnValue(mockJQuery);
    spyFormService.detailsDraftForm.and.returnValue(Promise.reject());
    component.formDetails = {
      form_id: 12578201,
      form_submission_id: 59,
    };
    component.getDraftFormHistory();
    fixture.whenStable().then(() => {
      expect(mockJQuery.modal).toHaveBeenCalledWith('hide');
      expect(component.showFailedMsg).toBeTruthy();
    });
  });
  it('should handle failure to fetch draft history details', async() => {
    mockJQuery.modal.and.returnValue(mockJQuery);
    spyOn(window, '$').and.returnValue(mockJQuery);
    spyFormService.detailsHistoryForm.and.returnValue(Promise.reject());
    component.formDetails = {
      form_id: 13530135,
      form_submission_id: 5,
      sent_id: 2183208,
      recipient_id: 2598381,
    };
    component.getFormHistory();
    fixture.whenStable().then(() => {
      expect(mockJQuery.modal).toHaveBeenCalledWith('hide');
      expect(component.showFailedMsg).toBeTruthy();
    });
  });
  /**To correct */
  // it('should show the notify msg', async () => {
  //   mockJQuery.notify.and.returnValue({ update: jasmine.createSpy('modal') });
  //   component.showFailedMsg();
  //   expect(component.historyLoaded).toBe(true);
  //   await new Promise((resolve) => setTimeout(resolve, 1000));
  //   expect(mockJQuery.notify).toHaveBeenCalledWith({
  //     type: 'warning',
  //     message: '<strong>Failed to fetch details.</strong>',
  //   });
  // });
  
});
