import { Component } from '@angular/core';
import { Observable } from 'rxjs';
import { Router, ActivatedRoute, Params } from "@angular/router";
import { Breadcrumb } from './breadcrumb.interface';
import { BreadcrumbService } from './breadcrumb.service';

@Component({
  selector: 'app-breadcrumb',
  templateUrl: './breadcrumb.component.html',
  //styleUrls: ['./breadcrumb.component.scss']
})
export class BreadcrumbComponent {

  breadcrumbs$: Observable<Breadcrumb[]>;

  constructor(private readonly breadcrumbService: BreadcrumbService) {
    breadcrumbService.setBreadcrumb();
    this.breadcrumbs$ = breadcrumbService.breadcrumbs$;
  }
}