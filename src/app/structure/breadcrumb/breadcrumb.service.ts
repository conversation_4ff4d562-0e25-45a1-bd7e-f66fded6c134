import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Data, NavigationEnd, ActivationEnd, Router } from '@angular/router';
import { BehaviorSubject } from 'rxjs';
import { filter } from 'rxjs/operators';
import { Breadcrumb } from './breadcrumb.interface';
import { ToolTipService } from './../tool-tip.service';

@Injectable()
export class BreadcrumbService {

  // Subject emitting the breadcrumb hierarchy
  private readonly _breadcrumbs$ = new BehaviorSubject<Breadcrumb[]>([]);
  // Observable exposing the breadcrumb hierarchy
  readonly breadcrumbs$ = this._breadcrumbs$.asObservable();
  constructor(private router: Router,
    private _ToolTipService: ToolTipService) {
  }
  setBreadcrumb() {
    const root = this.router.routerState.snapshot.root;
    const homeLabel = this._ToolTipService.getTranslateData('BREADCRUMBS.HOME') == undefined ? 'Home' : this._ToolTipService.getTranslateData('BREADCRUMBS.HOME');
    const breadcrumbs: Breadcrumb[] = [{label: homeLabel, url: "/inbox"}];

    this.addBreadcrumb(root, [], breadcrumbs);
    // Emit the new hierarchy
    this._breadcrumbs$.next(breadcrumbs);
  }
  private addBreadcrumb(route: ActivatedRouteSnapshot, parentUrl: string[], breadcrumbs: Breadcrumb[]) {
    if (route) {
      // Construct the route URL
      const routeUrl = parentUrl.concat(route.url.map(url => url.path));
      // Add an element for the current route part
      let label = "";
      let url = "";
      if (route.data.breadcrumb && routeUrl != parentUrl) {
        label = this.getTranslateLabel(route.data);
        url = '/' + routeUrl.join('/');
      } else if (route.data.responseData && route.data.responseData.breadcrumb && routeUrl != parentUrl) {
        label = route.data.responseData.breadcrumb;
        url = '/' + routeUrl.join('/');
      }
      const breadcrumb = {
        label: label,
        url: url
      };
      let index = breadcrumbs.findIndex(x => x.label == breadcrumb.label);
      if (index == -1 && label != '') {
        breadcrumbs.push(breadcrumb);
      }
      // Add another element for the next route part
      this.addBreadcrumb(route.firstChild, routeUrl, breadcrumbs);
    }
  }
  private getTranslateLabel(data: Data) {
    const label = typeof data.breadcrumb === 'function' ? data.breadcrumb(data) : data.breadcrumb;
    const translateLabel = label != '' ? this._ToolTipService.getTranslateData('BREADCRUMBS.' + label.replace(/\s+/g, '_').toUpperCase()) : '';
    return translateLabel == undefined ? label : translateLabel;
  }
}