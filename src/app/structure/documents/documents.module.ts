import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { BrowserModule } from '@angular/platform-browser';
import { ReactiveFormsModule } from '@angular/forms';
import { FormsModule } from '@angular/forms';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { StaffComponent } from './staff.citushealth';
import { PatientsComponent } from './patients.citushealth';
import { InstitutionalComponent } from './institutional.citushealth';
import { TagDefinitionsComponent } from './tag-definitions/tag-definitions.component';
import { TaggedFormsComponent } from './tagged-forms.component';
import { SharedModule } from '../shared/sharedModule';
import { DocumentCategoriesComponent } from './document-categories.component';
import { AddDocumentCategoriesComponent } from './add-document-categories.component';
import { MessageDirective } from './tagged-forms.directive'
import { AuthGuard } from '../../guard/auth.guard';
import { Daterangepicker } from 'ng2-daterangepicker';
import { ManageDocumentsComponent } from './manage-documents.component';
import { Modal } from './view-document';
export const routes: Routes = [
  {
    path: 'documents/manage-documents', component: ManageDocumentsComponent, canActivate: [AuthGuard], data: {
      checkRoutingConfig: 'enable_document_management',
      checkUserGroupPermission: '3'
    }  
  }, 
  { path: 'documents/staff', component: StaffComponent },
  { path: 'documents/patients', component: PatientsComponent },
  { path: 'documents/institutional', component: InstitutionalComponent },
  {
    path: 'documents/tag-definitions', component: TagDefinitionsComponent, canActivate: [AuthGuard], data: {
      checkRoutingPrivileges: 'manageTenants,superAdmin', checkRoutingConfig: 'show_document_tagging'
    }  
  },
  { path: 'documents/tagged-forms', component: TaggedFormsComponent,canActivate:[AuthGuard], data: {checkRoutingConfig: 'enable_message_center',checkUserGroupPermission:'3'} },
  {
    path: 'documents/categories', component: DocumentCategoriesComponent, canActivate: [AuthGuard], data: {
      checkRoutingPrivileges: 'manageTenants,superAdmin'
    }  
  },
  { path: 'documents/categories/add', component: AddDocumentCategoriesComponent },
  { path: 'documents/categories/:id', component: AddDocumentCategoriesComponent }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    BrowserModule,
    ReactiveFormsModule,
    SharedModule,
    AngularMultiSelectModule,
    Daterangepicker,
    RouterModule.forChild(routes)
  ],
  declarations: [
    StaffComponent,
    PatientsComponent,
    InstitutionalComponent,
    TagDefinitionsComponent,
    TaggedFormsComponent,
    DocumentCategoriesComponent,
    AddDocumentCategoriesComponent,
    MessageDirective,
    ManageDocumentsComponent,
    Modal   
  ],
  providers: [Modal],
  exports: [TaggedFormsComponent, ManageDocumentsComponent]
})

export class DocumentsModule { }
