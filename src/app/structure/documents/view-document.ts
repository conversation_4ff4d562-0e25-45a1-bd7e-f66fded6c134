import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import * as FileSaver from 'file-saver';
@Component({
    selector: 'modal-document',
    templateUrl: './view-doc.html'
})

export class Modal implements OnInit {
    @Input('show-modal') showModal: boolean;
    @Input('show-header') showHeader: boolean;
    @Input('show-footer') showFooter: boolean;
    @Input('show-iframe') modalIframeBody: boolean;
    @Input('show-video') modalVideoBody: boolean;
    @Input('title') title: string;
    @Input('sub-title') subTitle: string;
    @Input('modal-body') modalBody: String;
    @Input('cancel-label') cancelLabel: Boolean;
    @Input('positive-label') positiveLabel: string;
    @Output('closed') closeEmitter: EventEmitter<ModalResult> = new EventEmitter<ModalResult>();
    @Output('loaded') loadedEmitter: EventEmitter<Modal> = new EventEmitter<Modal>();
    @Output() positiveLabelAction = new EventEmitter();
    constructor( private http:HttpClient) { }

    ngOnInit() {
        this.loadedEmitter.next(this);
    }

    show() {
        this.showModal = true;
    }

    hide() {
        this.showModal = false;
        this.closeEmitter.next({
            action: ModalAction.POSITIVE
        });
    }

    positiveAction() {
        this.positiveLabelAction.next(this);
        return false;
    }

    cancelAction() {
        this.showModal = false;
        this.closeEmitter.next({
            action: ModalAction.CANCEL
        });
        return false;
    }

    downloadDocument(doUrl) {     
        this.getDocument(doUrl).subscribe((data)=>{
            var file = new Blob([data], {type: 'application/pdf'});            
            FileSaver.saveAs(file, this.subTitle+".pdf");
        });
    }

    getDocument(doUrl) {
        let headers = new HttpHeaders();
        headers = headers.set('Accept', 'application/pdf');
        return this.http.get(doUrl.changingThisBreaksApplicationSecurity, {responseType: 'arraybuffer',headers:headers});   
    }
}

export enum ModalAction { POSITIVE, CANCEL }

export interface ModalResult {
    action: ModalAction;
}