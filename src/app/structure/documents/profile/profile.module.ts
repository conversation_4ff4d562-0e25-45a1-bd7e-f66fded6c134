import { NgModule }      from '@angular/core';
import { CommonModule }  from '@angular/common';
import { Routes, RouterModule }  from '@angular/router';
import { BrowserModule } from '@angular/platform-browser';
import { FormsModule } from '@angular/forms';
import { Profile } from './profile.citushealth';

export const routes: Routes = [
  { path: 'profile', component: Profile },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    BrowserModule,
    RouterModule.forChild(routes)
  ],
  declarations: [
    Profile,
  ]

})

export class ProfileModule { }
