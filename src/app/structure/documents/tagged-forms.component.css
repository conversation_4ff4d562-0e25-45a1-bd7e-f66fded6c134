.action-button-panel {
    width: 14%;
}    
.action-button-panel a {
    background: #399fb6;
    border-radius: 5px;
    color: #fff;
    width: 20px;
    height: 20px;
    margin-right: 10px;
    padding: 5px;
}    
#details {
    background-color: white;
    display: none;
}    
.signed-docs-table td {
    vertical-align: middle;
}
.tagfilter{
	display: flex;
	width: 100% !important;
	margin-top: 7px;
}
.tagfilter .site-label, .tagfilter app-select-sites{
	flex-basis: 100%;
}
 .tagfilter app-select-sites{
     margin-left: -241px;
 }
 .tagmsgs
 {
    position: absolute;
    width: 57%;
    margin-left: 397px;
 }
.sd-modal-overlay {
    background-color: rgba(0, 0, 0, 0.5) !important;
}    
.data-cnt { 
    /* width: 600px;
    margin: auto; */
}    
.data-cnt label {
    color: #1d6472;
}    
.data-cnt p {
    color: #818181;
}    
.data-cnt .form-group {
    clear: both;
}    
.tags-sec span {
    border: 1px solid #c1c1c1;
    padding: 2px 12px;
    margin-right: 10px;
    border-radius: 10px;
    margin-bottom: 6px;
    float: left;
}
.tags-sec img.tag-icon {
    width: 25px;
}
.tag-message{
    border: 1px solid #c1c1c1;
    padding: 9px 12px;
    margin-right: 10px;
    border-radius: 10px;
    margin-bottom: 6px;
    position: relative;
}
.box {
    width: auto;
    min-height: 40px;
    background: #c2c2c2;
    position: relative;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px;
    display: inline-block;
    float: left;
    clear: left;
}
/*.box:before {
    content: "";
    position: absolute;
    right: 100%;
    top: 32%;
    width: 0;
    height: 0;
    border-top: 7px solid transparent;
    border-right: 13px solid #c2c2c2;
    border-bottom: 7px solid transparent;
}*/
.filingcenter-file-format{
    height: 50px !important;
}
.resize-Approve-Sign{
    width: 153px;
    height: 43px;
}