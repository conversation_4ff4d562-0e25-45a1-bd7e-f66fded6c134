import { Directive, ElementRef, Input, AfterViewInit } from '@angular/core';
import { OnChanges } from '@angular/core';
import { StructureService } from '../structure.service';

@Directive({
  selector: '[compile]',
})
export class MessageDirective implements OnChanges {

  @Input() compile: string;
  constructor(private elRef: ElementRef,private _structureService:StructureService) {

  }
  ngOnChanges(): void {
    // console.log(this.compile);  
    var resetValue;
    var dataValue;
    if (this.compile.search("data-mediaType='pdf'") != -1) {
      dataValue = this.compile;
      var hrefStrings = dataValue.substring(parseInt(dataValue.lastIndexOf("data-src=")) + 9, dataValue.lastIndexOf(".pdf'"));
      var appendedString = " data-view=" + hrefStrings + ".pdf'";
      // resetValue = this.compile.splice(parseInt(dataValue.lastIndexOf(".pdf'"))+5,0,appendedString);
      dataValue = dataValue.replace(" src='./img"," src='"+this._structureService.iconPath+""); 
      resetValue = dataValue;
    } else {
      resetValue = this.compile.replace(" src='./img"," src='"+this._structureService.iconPath+""); 
    }
    this.elRef.nativeElement.innerHTML = resetValue;
  }
}
