<section class="card">
  <div class="card-header">
    <span class="cat__core__title">
      <strong> {{ 'LABELS.DOCUMENT_TYPES' | translate }} <i class="document-tag icmn-info" data-toggle="tooltip" data-placement="right"></i> </strong>
      <a (click)="addDocumentTag()" class="pull-right btn btn-sm btn-primary">{{ 'LABELS.ADD_DOCUMENT_TYPE' | translate }}<i class="ml-1"></i></a>
    </span>
  </div>
  <div class="card-block">
    <ol class="breadcrumb">
      <li class="breadcrumb-item">
        <a [routerLink]="['/inbox']">{{ 'TITLES.HOME' | translate }}</a>
      </li>
      <li class="breadcrumb-item">
        <a [routerLink]="['/documents/tag-definitions']">{{ 'LABELS.DOCUMENTS' | translate }}</a>
      </li>
      <li class="breadcrumb-item">{{ 'LABELS.DOCUMENT_TYPES' | translate }}</li>
    </ol>
    <div class="row">
      <div class="col-lg-12">
        <div style="text-align: center; width: 100%" *ngIf="dataLoadingMsg">
          <img src="./assets/img/loader/loading.gif" alt="loading" />
        </div>
        <div class="mb-5" [hidden]="dataLoadingMsg">
          <table class="table table-hover" id="documentTagTypeTable" width="100%"></table>
        </div>
      </div>
    </div>
  </div>
</section>
