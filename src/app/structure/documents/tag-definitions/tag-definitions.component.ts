/* eslint-disable no-underscore-dangle */
/* eslint-disable no-param-reassign */
import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TagType } from 'app/constants/constants';
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { TagService } from 'app/services/tag/tag.service';
import { Store, StoreService } from 'app/structure/shared/storeService';

declare const $: any;
declare const NProgress: any;

@Component({
  selector: 'app-module-tags',
  templateUrl: './tag-definitions.component.html'
})
export class TagDefinitionsComponent implements OnInit {
  tagList = [];
  tagDeleted;
  editRowId: any;
  isLoad = false;
  tagDefinitionEdit: FormGroup;
  userInfo;
  tenantId;
  dataLoadingMsg = true;
  type = 3;
  showEditErrorMessage = false;

  userDetails: any;
  userData: any = {};
  dataTable: any;
  documentTagRowData: any;
  initialLoad = true;
  searchResetFlag: number;
  datam: any;
  totalCt: any;
  totalCount: any;
  constructor(
    private router: Router,
    private tagService: TagService,
    private formBuild: FormBuilder,
    public structureService: StructureService,
    private toolTipService: ToolTipService,
    private storeService: StoreService
  ) {
    this.userDetails = this.structureService.userDetails;
    this.userData = JSON.parse(this.userDetails);
    this.userInfo = this.structureService.loginUserDetails;
    this.tenantId = this.userInfo.tenantId;
  }
  ngOnInit() {
    const page = 'documents-tag-definitions';
    $('.document-tag').tooltip({ title: this.toolTipService.getToolTip(page, 'CHATD00003') });
    this.getDocumentTagList();
  }
  getDocumentTagList() {
    const {
      'INFO_MESSAGES.NO_DATA_AVAILABLE': noDataAvailable,
      'BUTTONS.SEARCH': searchLabel,
      'BUTTONS.RESET': resetLabel,
      'LABELS.DOCUMENT_NAME': documentNameLabel,
      'GENERAL.ACTIONS': actionsLabel,
      'LABELS.REMOVE': removeLabel
    } = this.toolTipService.getTranslateData([
      'INFO_MESSAGES.NO_DATA_AVAILABLE',
      'BUTTONS.SEARCH',
      'BUTTONS.RESET',
      'LABELS.DOCUMENT_NAME',
      'GENERAL.ACTIONS',
      'LABELS.REMOVE'
    ]);
    if (this.dataTable) {
      this.dataTable.destroy();
    }
    this.dataTable = $('#documentTagTypeTable').DataTable({
      autoWidth: false,
      responsive: true,
      bprocessing: true,
      bServerSide: true,
      bpagination: true,
      bsorting: true,
      retrieve: true,
      stateSave: true,
      bsearching: true,
      bInfo: true,
      lengthMenu: [
        [25, 50],
        [25, 50]
      ],
      language: {
        infoEmpty: noDataAvailable
      },
      fnDrawCallback: this.fnDrawCallbackEvent.bind(this),
      fnRowCallback: this.fnRowCallbackEvent.bind(this),
      dom:
        "<'row justify-content-between'<'col-sm-4 'l><'col-sm-8 row justify-content-end'f<'searchButton ml-3'>>>" +
        "<'row'<'col-sm-12'tr>>" +
        "<'row'<'col-sm-5'i><'col-sm-7'p>>",
      initComplete: this.initCompleteEvent.bind(this),
      ajax: this.getDocumentTags.bind(this),
      order: [[1, 'asc']],
      columns: [{ title: '#' }, { title: documentNameLabel, data: 'tagName' }, { title: actionsLabel, data: '' }],
      columnDefs: [
        {
          data: null,
          orderable: false,
          render: (data, type, row, meta) => {
            return meta.row + 1 + meta.settings._iDisplayStart;
          },
          width: '1%',
          targets: 0
        },
        {
          data: null,
          targets: 1,
          width: '80%'
        },
        {
          data: null,
          targets: 2,
          width: '19%',
          orderable: false,
          render: (data, type, row) => {
            return `<a data-type-remove="${row}" id="removeType" class="cat__core__link--underlined" href="javascript: void(0);">
                      <small><i class="icmn-cross"></i></small> ${removeLabel}
                    </a>`;
          }
        }
      ]
    });
    // search and reset action of data table
    $(document).on('click', '.resetTagType', this.resetEvent.bind(this));
    $(document).on('click', '.searchTagType', this.searchEvent.bind(this));
  }
  initCompleteEvent() {
    const { 'BUTTONS.SEARCH': searchLabel, 'BUTTONS.RESET': resetLabel } = this.toolTipService.getTranslateData(['BUTTONS.SEARCH', 'BUTTONS.RESET']);
    $('.dataTables_filter label input').attr('placeholder', searchLabel);
    $('.dataTables_filter label input').unbind();
    $('#documentTagTypeTable_filter input').on('keydown', (e) => {
      if (e.which === 13) {
        let tblValue = $('#documentTagTypeTable_filter input').val();
        if (tblValue) {
          tblValue = tblValue.replace('”', '"');
          tblValue = tblValue.replace('‘', "'");
          tblValue = tblValue.replace('’', "'");
          this.searchResetFlag = 0;
          this.dataTable.search(tblValue).draw();
        }
      }
    });
    $('#documentTagTypeTable_filter input').on('keypress', () => {
      $('.searchTagType').prop('disabled', false);
    });
    $('#documentTagTypeTable_filter input').on('keyup', () => {
      const tblFilterValue = $('#documentTagTypeTable_filter input').val();
      if (!tblFilterValue) {
        $('.searchTagType').prop('disabled', true);
      }
    });
    $('div.searchButton').html(
      `<button disabled="true" class="btn btn-sm btn-info searchTagType" title="${searchLabel}" type="submit">${searchLabel}</button>
          <button style="margin-left:10px;" class="btn btn-sm btn-default resetTagType" title="${resetLabel}" type="submit">${resetLabel}</button>`
    );
    const documentTagTypeTableFilterValue = $('#documentTagTypeTable_filter input').val();
    if (documentTagTypeTableFilterValue) {
      $('.searchTagType').prop('disabled', false);
    }
    $('#documentTagTypeTable_filter input').on('paste', () => {
      setTimeout(() => {
        const text = $('#documentTagTypeTable_filter input').val();
        if (text) {
          $('.searchTagType').prop('disabled', false);
        }
      }, 100);
    });
    $('.buttons-collection').click(() => {
      setTimeout(() => {
        if ($('.buttons-collection').attr('aria-expanded') === 'true' && $('.dt-button-collection').find('button').length === 0) {
          $('.dt-button-collection').remove();
          $('.dt-button-background').remove();
          $('.buttons-collection').attr('aria-expanded', 'false');
        }
      }, 500);
    });
  }
  fnRowCallbackEvent(nRow, aData) {
    $(nRow).attr('role', 'button');
    $(nRow).on('click', (event) => {
      this.documentTagRowData = aData;
      if (event.target.id === 'removeType') {
        this.removeTag(this.documentTagRowData);
      } else {
        this.editDocumentTag(aData.id);
      }
    });
  }
  resetEvent() {
    this.storeService.removeData(Store.SEARCH_DOCUMENT_TYPES);
    this.structureService.notifySearchFilterApplied(false);
    this.dataTable.search('').draw();
    $('.searchTagType').prop('disabled', true);
  }
  fnDrawCallbackEvent(oSettings) {
    if (oSettings._iRecordsTotal < oSettings._iDisplayLength || oSettings._iRecordsTotal === 0 || oSettings.aoData.length === 0) {
      $('.dataTables_paginate').hide();
    } else {
      $('.dataTables_paginate').show();
    }
    if (oSettings.aoData.length === 0) {
      $('.dataTables_info').hide();
    } else {
      $('.dataTables_info').show();
    }
  }
  searchEvent() {
    let value = $('#documentTagTypeTable_wrapper #documentTagTypeTable_filter label input').val();
    if (value) {
      value = value.replace('â', '"');
      value = value.replace('â', "'");
      value = value.replace('â', "'");
      const searchText = value;
      if (!searchText || this.initialLoad) {
        this.structureService.notifySearchFilterApplied(false);
      }
      this.searchResetFlag = 0;
      this.dataTable.search(value).draw();
    } else {
      this.dataTable.search('').draw();
    }
  }
  getDocumentTags(dat, callback, settings) {
    this.dataLoadingMsg = true;
    NProgress.start();
    let orderData;
    let searchTextfile = '';

    const columnNumber = dat.order[0].column ? dat.order[0].column : '';
    const orderby = dat.order[0].dir ? dat.order[0].dir : '';
    if (columnNumber !== 0) {
      orderData = dat.columns[columnNumber].data ? dat.columns[columnNumber].data : '';
    } else {
      orderData = '';
    }
    const searchStoredTextFile = this.storeService.getStoredData(Store.SEARCH_DOCUMENT_TYPES);
    if (dat.search.value) {
      searchTextfile = dat.search.value;
      if (this.initialLoad) {
        this.structureService.notifySearchFilterApplied(true);
      }
    } else if (this.initialLoad && searchStoredTextFile && searchStoredTextFile.searchTextfile) {
      searchTextfile = searchStoredTextFile.searchTextfile;
      this.structureService.notifySearchFilterApplied(true);
    } else {
      this.storeService.removeData(Store.SEARCH_DOCUMENT_TYPES);
      this.structureService.notifySearchFilterApplied(false);
    }
    if (searchTextfile && this.searchResetFlag === 0) {
      dat.start = 0;
      settings.oAjaxData.search.value = searchTextfile;
      settings.oAjaxData.start = 0;
      settings._iDisplayStart = 0;
      this.searchResetFlag = 1;
    }
    if (!searchStoredTextFile || (searchStoredTextFile && searchStoredTextFile.searchTextfile !== searchTextfile)) {
      this.storeService.storeData(Store.SEARCH_DOCUMENT_TYPES, { searchTextfile });
    }
    $('#documentTagTypeTable_filter input').val(searchTextfile);
    if (
      settings.oAjaxData.start !== 0 &&
      this.datam &&
      this.datam.aaData &&
      this.datam.aaData.length === 1 &&
      settings._iDisplayStart !== 0 &&
      searchTextfile === ''
    ) {
      settings.oAjaxData.start -= settings.oAjaxData.length;
      settings._iDisplayStart = settings.oAjaxData.start;
    }
    const page = settings.oAjaxData.start / settings.oAjaxData.length + 1;
    this.structureService
      .getDocumentTagList({ searchText: searchTextfile, limit: dat.length, page, orderData, orderBy: orderby.toUpperCase() })
      .subscribe(({ data }) => {
        this.tagList = data.getSessionTenant.documentTags.data;
        this.dataLoadingMsg = false;
        this.initialLoad = false;
        NProgress.done();
        let datas = [];
        this.datam = {};
        if (dat.start === 0) {
          this.totalCt = data.getSessionTenant.documentTags.totalCount;
          this.totalCount = this.totalCt;
        } else if (this.totalCount) {
          this.structureService.setCookie('totalCount', this.totalCt, 1);
          this.totalCt = this.totalCount;
        } else {
          this.totalCt = this.structureService.getCookie('totalCount');
          this.totalCount = this.totalCt;
        }
        datas = data.getSessionTenant.documentTags.data ? data.getSessionTenant.documentTags.data : [];
        let total;
        if (datas && datas.length === 0 && searchTextfile === '') {
          total = 0;
        } else {
          total = this.totalCt;
        }
        this.datam = {
          draw: dat.draw,
          recordsTotal: total || 0,
          recordsFiltered: total || 0,
          aaData: datas
        };
        callback(this.datam);
      });
  }
  noWhitespaceValidator(control: FormControl) {
    const isWhitespace = (control.value || '').trim().length === 0;
    const isValid = !isWhitespace;
    return isValid ? null : { whitespace: true };
  }
  editCell(cellId) {
    this.editRowId = cellId;
  }
  cancelUpdate() {
    this.editRowId = '';
    this.showEditErrorMessage = false;
    this.getDocumentTagList();
  }
  removeTag(tag) {
    if (!tag || !tag.id) {
      return;
    }
    this.tagService.removeTag({ tagId: tag.id, tagType: TagType.documentTag }).then((success) => {
      if (success) {
        this.tagDeleted = true;
        this.getDocumentTagList();
      }
      this.router.navigate(['/documents/tag-definitions']);
    });
  }
  showErrorOnRemoveTag() {
    this.structureService.notifyMessage({
      message: this.toolTipService.getTranslateData('MESSAGES.DOCUMENT_TYPE_REMOVE_FAILED'),
      type: 'error'
    });
  }
  initTags() {
    return this.formBuild.group({
      name: ['', [Validators.required, this.noWhitespaceValidator]],
      id: ['', Validators.required]
    });
  }

  addDocumentTag() {
    this.structureService.setCookie('tagId', '', 1);
    this.router.navigate(['/signature/request-signature/add-document']);
  }
  editDocumentTag(id) {
    this.router.navigate(['/signature/request-signature/add-document']);
    this.structureService.setCookie('tagId', id, 1);
  }
  showEditError() {
    this.showEditErrorMessage = true;
  }
}
