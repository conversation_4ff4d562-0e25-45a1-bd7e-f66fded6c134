import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { Router } from '@angular/router';
import { StructureService } from 'app/structure/structure.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { TagService } from 'app/services/tag/tag.service';
import { StoreService } from 'app/structure/shared/storeService';
import { of } from 'rxjs/observable/of';
import { TagType } from 'app/constants/constants';
import { CommonTestingModule } from 'app/utils/common-testing.module';
import { TagDefinitionsComponent } from './tag-definitions.component';

declare const $: any;

describe('TagDefinitionsComponent', () => {
  let component: TagDefinitionsComponent;
  let fixture: ComponentFixture<TagDefinitionsComponent>;
  let structureService: StructureService;
  let tagService: TagService;
  let router: Router;
  let toolTipService: ToolTipService;
  let formBuild: FormBuilder;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [TagDefinitionsComponent],
      imports: [CommonTestingModule],
      providers: [StructureService, ToolTipService, TagService, StoreService]
    }).compileComponents();
    structureService = TestBed.get(StructureService);
    structureService.userDetails = JSON.stringify({ tenantId: 1 });
    tagService = TestBed.get(TagService);
    router = TestBed.get(Router);
    toolTipService = TestBed.get(ToolTipService);
    formBuild = TestBed.get(FormBuilder);
    fixture = TestBed.createComponent(TagDefinitionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });
  afterEach(() => {
    if (fixture.nativeElement && 'remove' in fixture.nativeElement) {
      (fixture.nativeElement as HTMLElement).remove();
    }
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize component', () => {
    spyOn(component, 'getDocumentTagList');
    component.ngOnInit();
    expect(component.getDocumentTagList).toHaveBeenCalled();
  });

  it('should get document tag list', () => {
    spyOn(structureService, 'getDocumentTagList').and.returnValue(of({ data: { getSessionTenant: { documentTags: { data: [], totalCount: 0 } } } }));
    component.getDocumentTagList();
    expect(component.dataLoadingMsg).toBe(false);
  });

  it('should handle no whitespace validator', () => {
    const control = formBuild.control(' ');
    const result = component.noWhitespaceValidator(control);
    expect(result).toEqual({ whitespace: true });
  });

  it('should edit cell', () => {
    component.editCell(1);
    expect(component.editRowId).toBe(1);
  });

  it('should cancel update', () => {
    spyOn(component, 'getDocumentTagList');
    component.cancelUpdate();
    expect(component.editRowId).toBe('');
    expect(component.showEditErrorMessage).toBe(false);
    expect(component.getDocumentTagList).toHaveBeenCalled();
  });
  describe('removeTag', () => {
    it('should remove tag', () => {
      spyOn(tagService, 'removeTag').and.returnValue(Promise.resolve(true));
      spyOn(component, 'getDocumentTagList');
      component.removeTag({ id: 1 });
      expect(tagService.removeTag).toHaveBeenCalledWith({ tagId: 1, tagType: TagType.documentTag });
    });
    it('Should not call api if tag id is not present', () => {
      spyOn(tagService, 'removeTag');
      component.removeTag({ id: null });
      expect(tagService.removeTag).not.toHaveBeenCalled();
    });
  });

  it('should show error on remove tag', () => {
    spyOn(structureService, 'notifyMessage');
    component.showErrorOnRemoveTag();
    expect(structureService.notifyMessage).toHaveBeenCalledWith({
      message: toolTipService.getTranslateData('MESSAGES.DOCUMENT_TYPE_REMOVE_FAILED'),
      type: 'error'
    });
  });

  it('should initialize tags', () => {
    const formGroup = component.initTags();
    expect(formGroup.controls.name).toBeTruthy();
    expect(formGroup.controls.id).toBeTruthy();
  });

  it('should add document tag', () => {
    spyOn(router, 'navigate');
    spyOn(structureService, 'setCookie');
    component.addDocumentTag();
    expect(structureService.setCookie).toHaveBeenCalledWith('tagId', '', 1);
    expect(router.navigate).toHaveBeenCalledWith(['/signature/request-signature/add-document']);
  });

  it('should edit document tag', () => {
    spyOn(router, 'navigate');
    spyOn(structureService, 'setCookie');
    component.editDocumentTag(1);
    expect(structureService.setCookie).toHaveBeenCalledWith('tagId', 1, 1);
    expect(router.navigate).toHaveBeenCalledWith(['/signature/request-signature/add-document']);
  });

  it('should show edit error', () => {
    component.showEditError();
    expect(component.showEditErrorMessage).toBe(true);
  });
  describe('searchEvent', () => {
    beforeEach(() => {
      spyOn(component.dataTable, 'search').and.returnValue({ draw: () => null });
      spyOn(structureService, 'notifySearchFilterApplied');
    });
    it('should search event', () => {
      spyOn(component, 'getDocumentTagList');
      component.searchEvent();
      expect(component.searchEvent).toBeTruthy();
    });
    it('should handle searchEvent correctly', () => {
      const searchInput = 'test search';
      spyOn($.fn, 'val').and.returnValue(searchInput);
      component.searchEvent();
      expect(structureService.notifySearchFilterApplied).toHaveBeenCalledWith(false);
      expect(component.searchResetFlag).toBe(0);
    });
  });
  describe('resetEvent', () => {
    it('should reset search', () => {
      spyOn(component.dataTable, 'search').and.returnValue({ draw: () => null });
      spyOn(structureService, 'notifySearchFilterApplied');
      component.resetEvent();
      expect(structureService.notifySearchFilterApplied).toHaveBeenCalledWith(false);
    });
  });

  describe('fnRowCallbackEvent', () => {
    it('should set documentTagRowData and call editDocumentTag when removeType is not clicked', () => {
      const row = document.createElement('tr');
      const aData = { id: 1 };
      spyOn(component, 'editDocumentTag');

      component.fnRowCallbackEvent(row, aData);

      row.click();
      expect(component.documentTagRowData).toBe(aData);
      expect(component.editDocumentTag).toHaveBeenCalledWith(aData.id);
    });
  });
});
