import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { StructureService } from '../structure.service';
import { ToolTipService } from "../tool-tip.service";
import { Router,ActivatedRoute } from '@angular/router';
import { Guid } from "guid-typescript";

declare var $: any;

@Component({
  selector: 'app-add-document-categories',
  templateUrl: './add-document-categories.component.html'
})
export class AddDocumentCategoriesComponent implements OnInit {

  frmCategory: FormGroup;
  showClose=false;
  filingCenters;
  folderName;
  folderLists;
  guid;
  editRecord;
  multisite=false;
  editId = "";
  fileNameFormat = "{FirstName}-{LastName}-{DOB}-{createdOn}";
  heading = "Add Category";
  
  userDetails:any = {};
  statusShow = true;
  authorizationKey;
  apiEndPoint;
  noteTypes: any = [];
  showLoader: boolean;
     userData: any;
     selectedExtDocumentName: any;
     selectedExtDocumentId: any;
     noteTypesCount: any;
     documentTypeSet: any;
     enableDocType: boolean;
     selectedCatValue: any;
     selectedTypeValue: any;
     selectedTypeName: any;
	enableSubData = false;
	noteCount: any = 0;

  constructor(private _formBuild: FormBuilder,private _structureService: StructureService,private router: Router,private route: ActivatedRoute,    private _ToolTipService: ToolTipService,
) {
     this.userDetails = JSON.parse(this._structureService.userDetails);
   this.guid = Guid.create(); 
    console.log(this.guid);
    route.params.subscribe(val => {
      if(this.route.snapshot.params['id']){
        this.editId = this.route.snapshot.params['id'];
        this.heading = "Edit Category";
      } else {
        this.heading = "Add Category";
      }
     });
     console.log("this.userDetails", this.userDetails);
    //  if(this.userDetails.config.enable_esi_value_in_document_category == '1') {
      this.frmCategory = new FormGroup({
        categoryName: new FormControl(null, Validators.required),
        filingcenter: new FormControl(null),
        categoryCode:new FormControl(this.guid.value, Validators.required),
        fileNameFormat: new FormControl(this.fileNameFormat),
        esiValue: new FormControl(null),
        catTypeName:new FormControl(null),
      status: new FormControl(1)
      });
    //  } else {
    //   this.frmCategory = new FormGroup({
    //     categoryName: new FormControl(null, Validators.required),
    //     filingcenter: new FormControl(null),
    //     categoryCode:new FormControl(this.guid.value, Validators.required),
    //     fileNameFormat: new FormControl(this.fileNameFormat)      
    //   });
    //  }
    
    
   }

  ngOnInit() {
    this.getFolderLists();
    $('#sfilingCenterss').on('change', (e) => {
        console.log($('#sfilingCenterss').val());       
        setTimeout(()=>{
          $('#select2-sfilingCenterss-container').removeAttr('title');
        },1);      
        this.showClose=true;
        this.folderName = {folderName:$('#sfilingCenterss').val(),type:'OUTGOING'}        
      }
    );  
    console.log("user details man 11111111",this.userDetails);
    console.log("user details man 1111111122222",this.userDetails.config.enable_multisite);
     if(this.userDetails.config.enable_multisite==1)
         {
          this.multisite =true;
         }

    this.userData = this._structureService.getUserdata();
    this.apiEndPoint = this.userData.config.document_category_code;
    console.log('apiEndPoint', this.apiEndPoint);
    this.authorizationKey = this.userData.config.api_integration_authorization_key;
    console.log('authorizationKey', this.authorizationKey);
      
  } 

  toggleStatus(status, value) {
    if (status === 'Active') {
        this.frmCategory.patchValue({
            status: value
        });
    }
    if (status === 'Inactive') {
        this.frmCategory.patchValue({
            status: value
        });
    }
}

getExtDocData(reload){
     this.showLoader = true;
     this.enableDocType = false;
     this.enableSubData = false;
     this._structureService.getNoteTypes('documentType').then((data) => {
     if(data){
          console.log('data', data);
          if(data['issue']){
               if(data['issue'][0]['diagnostics']['statusCode'] == 412){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 1;
               } else if(data['issue'][0]['diagnostics']['statusCode'] == 406){
                    this.noteCount = 0;
                    this.showLoader = false;
                    this.noteTypesCount = 4;
                    } 
          } else if (data['documentCategory']) {
               this.showLoader = false;
               console.log('data', data);
             //   this.noteTypes = data['documentCategory'][0]['documentType'];
               this.noteTypes = data['documentCategory'];
               this.noteCount  = this.noteTypes.length;
               console.log(this.noteTypes);
             this.noteTypesCount = -1;
          } else if (data['message']) {
             if(data['message'] == 412){
               this.noteCount = 0;
               this.showLoader = false;
               this.noteTypesCount = 1;
             } else if(data['message'] == 404){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 0;
             } else if(data['message'] == 500){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 0;
             } else if(data['message'] == 0){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 0;
             } else if(data['message'] == 401){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 2;
             } else if(data['message'] == "Invalid Parameter"){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 3;
             } else if(data['message'] == 406){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.noteTypesCount = 4;
             }
             }				  				
        }else{					
          this.showLoader = false;
          this.noteTypesCount = 0;
        }	       
   })
   if(reload != 0){
     $('#bulk-edit').modal('show');
   }
}
clearExtDocData(){
     this.frmCategory.patchValue({
          esiValue: "",
     });
     this.selectedTypeValue = "";
}
closeDynamicModal() {
     this.enableDocType = false;   
     this.enableSubData = false;
     $('#bulk-edit').modal('hide');
}
onSelectCategory(event){
     if(event.target.value == ""){
		this.enableSubData = false
	}
     this.selectedCatValue = event.target.value;     
     this.documentTypeSet = this.noteTypes.find(function (item) {          
          return item.id == event.target.value;     
     });
     console.log("saaa", this.documentTypeSet)
     this.documentTypeSet = this.documentTypeSet['documentType']
     this.enableDocType = true;   
}
onSelectType(event){
     this.selectedTypeValue = event.target.value;     
     this.selectedTypeName = event.target.selectedOptions[0].text;
     if(event.target.value != "" && this.selectedCatValue != ""){
          this.enableSubData = true
     }else{
          this.enableSubData = false
     }
}
setSelectedData(){
     this.frmCategory.patchValue({
          esiValue: this.selectedTypeName,
     });
     this.enableDocType = false;   
     this.enableSubData = false;
     $('#bulk-edit').modal('hide');
}
  saveCategory(f) {
    if(!f.valid) {
      return false;
    }

    console.log(f.value);

    
    let userDetails = JSON.parse(this._structureService.userDetails);
    
    let metaData = {fileNameFormat:f.value.fileNameFormat,filingcenter:(f.value.filingcenter)?f.value.filingcenter.folderName:"",folderPath:""};
    if(metaData.filingcenter) {
     let path = this.folderLists.filter((row)=>{
        if(row.folderName == metaData.filingcenter) {
          return true;
        }
      });
      console.log('path >>>>');
      metaData.folderPath = path[0].folderPath;
      console.log(path);
    }
        
    let data = {
      typeName:f.value.categoryName,
      typeCode:f.value.categoryCode,
      tagMeta:JSON.stringify(metaData),
      createdBy:userDetails.userId,
      status:f.value.status,
     //  esiValue: f.value.esiValue,
      esiValue: this.userDetails.config.enable_API_based_integration_for_category_code == '1'?this.selectedTypeValue:f.value.esiValue,
      extDocType: this.userDetails.config.enable_API_based_integration_for_category_code == '1'? f.value.esiValue:f.value.catTypeName
    }

    console.log(data);
    //return false;
     this._structureService.createDocumentCategory(data,this.editId).then(
      (res) => {
        if(this.editId) {
          if(res['updateDocumentTagType'].typeName != null){

            if(res['updateDocumentTagType'].typeName == "exist"){
              var notify = $.notify('Document Category Code already exists');
              setTimeout(()=> {
                notify.update({'type': 'danger', 'message': '<strong>Document Category Code already exists</strong>'});   
              }, 1000);
             } else if(res['updateDocumentTagType'].typeName == "esiexist"){
              var notify = $.notify('Document Category ESI value already exists');
              setTimeout(()=> {
                  notify.update({'type': 'danger', 'message': '<strong>Document Category ESI value already exists</strong>'});   
              }, 1000);
             } else if(res['updateDocumentTagType'].typeName == "nameexist"){
              var notify = $.notify('Document Category Name already exists');
              setTimeout(()=> {
                  notify.update({'type': 'danger', 'message': '<strong>Document Category Name already exists</strong>'});   
              }, 1000);
             } else {
              var notify = $.notify('Success! Document Category Updated');
              setTimeout(()=> {
                  notify.update({'type': 'success', 'message': '<strong>Success! Document Category Updated</strong>'});   
              }, 1000);
      
              var activityData = {
                activityName: "update document category",
                activityType: "manage document category",
                activityDescription: userDetails.displayName+" updated document category - "+res['updateDocumentTagType'].typeName
              };    
              this._structureService.trackActivity(activityData);
              this.router.navigate(['/documents/categories']);
             }
           }
        } else {
          if(res['createDocumentTagType'].typeCode == "exist"){
            var notify = $.notify('Document Category Code already exists');
            setTimeout(()=> {
                notify.update({'type': 'danger', 'message': '<strong>Document Category Code already exists</strong>'});   
            }, 1000);
           } else if(res['createDocumentTagType'].typeCode == "esiexist"){
            var notify = $.notify('Document Category ESI value already exists');
            setTimeout(()=> {
                notify.update({'type': 'danger', 'message': '<strong>Document Category ESI value already exists</strong>'});   
            }, 1000);
           } else if(res['createDocumentTagType'].typeCode == "nameexist"){
            var notify = $.notify('Document Category Name already exists');
            setTimeout(()=> {
                notify.update({'type': 'danger', 'message': '<strong>Document Category Name already exists</strong>'});   
            }, 1000);
           } else {
            var notify = $.notify('Success! Document Category Created');
            setTimeout(()=> {
                notify.update({'type': 'success', 'message': '<strong>Success! Document Category Created</strong>'});   
            }, 1000);
    
            var activityData = {
              activityName: "add document category",
              activityType: "manage document category",
              activityDescription: userDetails.displayName+" created document category - "+res['createDocumentTagType'].typeName
            };    
            this._structureService.trackActivity(activityData);
            this.router.navigate(['/documents/categories']);
           }
        }
        
      });

    console.log(f.value); 
  }

  clearFilingCenter(){
    this.showClose=false;
    this.folderName ='';
    $('#sfilingCenterss').val('');
    $('#sfilingCenterss').select2({
      allowClear: false,
      placeholder: 'Select Filing Center',
      data:this.filingCenters
  });
}

  getFolderLists() {
       if (this.editId) {
            this._structureService.getDocumentCategory(this.editId).then((data) => {
                 this.editRecord = data['getDocumentTagTypeById'][0];
                 let meta;
                 try {
                      meta = JSON.parse(this.editRecord.tagMeta);
                 } catch (e) {
                      console.log(e);
                 }
                if (this.userDetails.config.enable_API_based_integration_for_category_code == '1') {
                    this.frmCategory.patchValue({
                         categoryName: this.editRecord.typeName,
                         filingcenter: (meta) ? meta.filingcenter : "",
                         categoryCode: this.editRecord.typeCode,
                         fileNameFormat: (meta) ? meta.fileNameFormat : this.fileNameFormat,
                         status: this.editRecord.status,
                         esiValue: this.editRecord.extDocType?this.editRecord.extDocType:""
                    });
                    this.selectedTypeValue = this.editRecord.esiValue ? this.editRecord.esiValue : "";
               } else if (this.userDetails.config.enable_esi_value_in_document_category == '1') {
                    this.frmCategory.patchValue({
                         categoryName: this.editRecord.typeName,
                         filingcenter: (meta) ? meta.filingcenter : "",
                         categoryCode: this.editRecord.typeCode,
                         fileNameFormat: (meta) ? meta.fileNameFormat : this.fileNameFormat,
                         status: this.editRecord.status,
                         esiValue: this.editRecord.esiValue ? this.editRecord.esiValue:"",
                         catTypeName: this.editRecord.extDocType ? this.editRecord.extDocType: ""
                    });
               }else {
                      this.frmCategory.patchValue({
                           categoryName: this.editRecord.typeName,
                           filingcenter: (meta) ? meta.filingcenter : "",
                           categoryCode: this.editRecord.typeCode,
                           fileNameFormat: (meta) ? meta.fileNameFormat : this.fileNameFormat,
                           esiValue: null
                      });
                 }
                 this._structureService.getTenantFilingCenterFolders('INCOMING').then((data) => {
                      if (data && data['getTenantFilingCenterFolders'] && data['getTenantFilingCenterFolders'].length) {
                           this.folderLists = JSON.parse(JSON.stringify(data['getTenantFilingCenterFolders']));
                           // this.folderLists.unshift({folderName:"Select Filing Center",type:"OUTGOING"});
                           $(".selectd").attr("disabled", false);
                           $('.selectd').select2({
                                placeholder: "Select Filing Center"
                           });
                           this.folderName = '';

                           this.filingCenters = [];
                           console.log(this.folderName);
                           console.log(this.folderLists);

                           for (let i = 0; i < this.folderLists.length; i++) {
                                var fname = this.folderLists[i].folderName;
                                var ftype = this.folderLists[i].type;
                                var id = '{"folderName":"' + fname + '","type":"' + ftype + '"}';
                                var item = {
                                     id: fname,
                                     text: fname
                                }
                                this.filingCenters.push(item);
                           }

                           /* var AJAX_OPTIONS = [
                             { id: '1', text: 'Choice 1' },
                             { id: '2', text: 'Choice 2' },
                             { id: '3', text: 'Choice 3' },
                             { id: '4', text: 'Choice 4' },
                             { id: '5', text: 'Choice 5' }
                           ]; */
                           $('#sfilingCenterss').select2({
                                allowClear: true,
                                placeholder: 'Select Filing Center',
                                data: this.filingCenters
                           });
                           $('#sfilingCenterjson').select2({
                                allowClear: true,
                                filingcenter: 'Select Filing Center',
                                data: this.filingCenters
                           });
                           if (meta && meta.filingcenter && this.filingCenters.length) {
                                $('#sfilingCenterss').val(meta.filingcenter).trigger('change');
                           }

                           this.frmCategory.get('categoryCode').disable();
                      } else {
                           $('.selectd').select2({
                                placeholder: "No Filing Centers Available"
                           });
                           $(".selectd").attr("disabled", true);
                           console.log("No folders");
                      }
                 });

            });
       } else {
            this._structureService.getTenantFilingCenterFolders('INCOMING').then((data) => {
                 if (data && data['getTenantFilingCenterFolders'] && data['getTenantFilingCenterFolders'].length) {
                      this.folderLists = JSON.parse(JSON.stringify(data['getTenantFilingCenterFolders']));
                      // this.folderLists.unshift({folderName:"Select Filing Center",type:"OUTGOING"});
                      $(".selectd").attr("disabled", false);
                      $('.selectd').select2({
                           placeholder: "Select Filing Center"
                      });
                      this.folderName = '';

                      this.filingCenters = [];
                      console.log(this.folderName);
                      console.log(this.folderLists);

                      for (let i = 0; i < this.folderLists.length; i++) {
                           var fname = this.folderLists[i].folderName;
                           var ftype = this.folderLists[i].type;
                           var id = '{"folderName":"' + fname + '","type":"' + ftype + '"}';
                           var item = {
                                id: fname,
                                text: fname
                           }
                           this.filingCenters.push(item);
                      }

                      /* var AJAX_OPTIONS = [
                        { id: '1', text: 'Choice 1' },
                        { id: '2', text: 'Choice 2' },
                        { id: '3', text: 'Choice 3' },
                        { id: '4', text: 'Choice 4' },
                        { id: '5', text: 'Choice 5' }
                      ]; */
                      $('#sfilingCenterss').select2({
                           allowClear: true,
                           placeholder: 'Select Filing Center',
                           data: this.filingCenters
                      });
                      $('#sfilingCenterjson').select2({
                           allowClear: true,
                           filingcenter: 'Select Filing Center',
                           data: this.filingCenters
                      });
                 } else {
                      $('.selectd').select2({
                           placeholder: "No Filing Centers Available"
                      });
                      $(".selectd").attr("disabled", true);
                      console.log("No folders");
                 }
            });

       }

  }

}
