import { Component, On<PERSON>nit, ElementRef, Renderer, ViewChild, Input, Output, EventEmitter } from '@angular/core';
import { MessageService } from 'app/services/message-center/message.service';
import { StructureService } from '../structure.service';
import { Router } from '@angular/router';
import { DatePipe } from '@angular/common';
import { SignPadComponent} from '../shared/signPad/sign-pad.component';
import { SharedService } from '../shared/sharedServices';
import { isInteger } from '@ng-bootstrap/ng-bootstrap/util/util';
import { FormsService } from '../forms/forms.service';
import { InboxService } from '../inbox/inbox.service';
import { Subject } from "rxjs";
import { isBlank } from 'app/utils/utils';
import { ToolTipService } from '../tool-tip.service';
import { StaticDataService } from '../static-data.service';
import { CONSTANTS, IntegrationType } from 'app/constants/constants';
import { StoreService, Store } from '../shared/storeService';

let moment = require('moment/moment');
var jstz = require('jstz');
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;
declare var jsPDF: any;
declare var swal:any;
const timezone = jstz.determine();
declare var NProgress: any;

@Component({
  selector: 'app-tagged-forms',
  templateUrl: './tagged-forms.component.html',
  styleUrls: ['./tagged-forms.component.css'],
  providers: [DatePipe]
})
export class TaggedFormsComponent implements OnInit {

  dTable;
  isDetail=false;
  filingCenters=[];
  selectedAssociatePatientName;
  patientAssociateRole;
  selectedAssociatePatient;
  activeTaggedForm;
  folderNameDefault;  
  taggedForms;
  folderType='';
  moveFilingCenter=false;
  filename='';
  downloadUrl;
  approvedBy;
  sendType;
  alltags='';
  folderLists;
  folderName;
  tagsList=[];
  imgUrl;
  userRole;
  pdfUrl;
  approveSignUrl;
  patientName;
  isFilingCenter;
  dob;
  dateOfBirth;
  noTags=false;
  createdOn;
  msgId;
  msgrpIds=[];
  showClose;
  signature;
  tags;
  description;
  userData;
  isApproveShow=false;
  approveFile =false;
  createdBy;
  outGoingFileCenter;
  integrationFC = '';
  fileNameFormat;
  enableIntegration;
  enableIntegrationfortag = false;
  crossTenantChangeSubscriber:any;
  dataLoadingMsg=true;
    taggedFormsList = [];    
    newObject = {id : 0,
    patientName:'',
    createdBy:'',
    createdOn:'',
    status:'',    
  };
  hideContent = false;
  responseData = [];
  integrationDetails;
  enable_worklist = false;
  patient_esi = 0;
  selectedExport=false;
  eventsSubject: Subject<void> = new Subject<void>();
  hideSiteSelection:boolean;
  selectedSiteId = false;
  siteIds:any;
  isSiteFilter= false;
  viewPage = true;
  admissionName= '';
  admissionId = '';
  labelSite = '';
  labelSiteFilter = '';
  initialLoad = true;
  public daterange: any = {};

    @Input() tagDetails: any;
    @Output() reload = new EventEmitter<string>();
    constructor(
    private router: Router,
    public _structureService: StructureService,
    private datePipe: DatePipe,
    private _formsService: FormsService,
    public _SharedService: SharedService,
    private _inboxService: InboxService,
    private toolTipService: ToolTipService,
    private staticDataService: StaticDataService,
    elementRef: ElementRef,
    renderer: Renderer,
    private messageService: MessageService,
    private storeService: StoreService
  ) {
       this.labelSite = this.toolTipService.getTranslateData(this.staticDataService.getSiteLabelBasedOnMultiAdmission('SITE'));
       this.labelSiteFilter = this.staticDataService.getSiteLabelBasedOnMultiAdmission('FILTER_BY_SITES');
      renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
        var targetElement;
        if(event.target.id == 'viewdetails') {
          this._SharedService.goToInnerPage = true;
          this.detailsView();
        }
        else if(event.target.id == 'deleteDetails'){
          this.deleteMsgTag();
        }
        else if(event.target.id == 'generateFiles'){
          this.generateFiles();
        }
      });
      this.crossTenantChangeSubscriber = this._SharedService.crossTenantChange.subscribe((onInboxData) => {
        if(this.router.url.indexOf('/documents/tagged-forms') > -1) {
          this.ngOnInit();
        }
      });
    }
    @ViewChild(SignPadComponent) childpad: SignPadComponent;

  ngOnInit() {
      this.userData = this._structureService.getUserdata(); 


      if(this.userData.config.enable_filing_center=="1" && this.userData.config.progress_note_integration_mode !="webhook" ) {       
          this.isFilingCenter= true;
      }else{        
        this.isFilingCenter= false;
      }
      console.log("????????????????????????????????????????????????????????????",this.userData.config.enable_integration_status_worklist)
      if(this.userData.config.enable_integration_status_worklist == 1){
       this.enable_worklist = true;
      }
      if(this.userData.config.enable_filing_center=="1" || this.userData.config.progress_note_integration_mode =="webhook" ) {       
        this.approveFile= true;
    }else{        
      this.approveFile= false;
    }


      if(this.tagDetails && this.tagDetails.show == true) {
        this.hideContent = true;
      } else {
        this.getAlldocumentsSigned();
      }
      if(this.isFilingCenter && this.userData.config.enable_multisite != 1) {
        this.getFolderLists();
      }else {
        if(this.tagDetails && this.tagDetails.show == true) {
          this.detailsView();
        }
      }
      $('#sfilingCenterss').on(
        'change',
        (e) => {
          console.log($('#sfilingCenterss').val());
          /* console.log(e);
          var m = $(e.target).val();
          console.log(m); */
          this.showClose=true;
          this.folderName = {folderName:$('#sfilingCenterss').val(),type:'OUTGOING'}        
          console.log(this.folderName);
          
        }
      );

    
  }
  ngOnDestroy() {
    if(this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
  }
  getSiteIds(data: any): void {
    this.selectedSiteId = true;
    this.isSiteFilter = true;
    console.log(data.siteId);
    this.siteIds =  data['siteId'].toString();;
    this.loadDataSignedDocs();
    //this.getSignatureCounts();
 }
  hideDropdown(hideItem : any){
    this.hideSiteSelection = hideItem.hideItem;
  }
  clearFilingCenter(){
    this.showClose=false;
    this.folderName ='';
    $('#sfilingCenterss').val('');
    $('#sfilingCenterss').select2({
      allowClear: false,
      placeholder: 'Select Filing Center',
      data:this.filingCenters
  });
  }

  popover(ids, action) {
    console.log('$$$$$$$$$$$$$$$$$$$$$$$');
    console.log(ids)
    console.log(action);
    if (action == 'loader') {
      $('.inte-popover').hide();
      $('#' + ids).show();
      $('#' + ids + ' > .pop-content > .int-loader').show();
      $('#' + ids + ' > .pop-content > .int-main-content').hide();
    } else if (action == 'show') {
    //  this.fillPopOver(ids);
      $('#' + ids + ' > .pop-content > .int-loader').hide();
      $('#' + ids + ' > .pop-content > .int-main-content').show();
    } else if (action == "close") {
      $('#' + ids + ' > .pop-content > .int-loader').hide();
      $('#' + ids + ' > .pop-content > .int-main-content').hide();
      $('#' + ids).hide();
    }
  }
  popoverNotSftp(ids, action, sent_on) {
    if (action == 'loader') {

      $('.inte-popover').hide();
      $('#' + ids).show();
      $('#' + ids + ' > .pop-content > .int-loader').show();
      $('#' + ids + ' > .pop-content > .int-loader').css("display", "block");
      $('#' + ids + ' > .pop-content > .int-main-content').hide();
    } else if (action == 'show') {
      this.fillPopOverNotSftp(ids, sent_on);
      $('#' + ids + ' > .pop-content > .int-loader').hide();
      $('#' + ids + ' > .pop-content > .int-main-content').show();
    } else if (action == "close") {
      $('#' + ids + ' > .pop-content > .int-loader').hide();
      $('#' + ids + ' > .pop-content > .int-main-content').hide();
      $('#' + ids).hide();
    }
  }
  fillPopOverNotSftp(ids, sent_on) {
    let outputHtml = '';
    if(this.integrationDetails.type == 'inte-status-not-sftp'){
      let response = this.integrationDetails.response;
      let diagnostics = (response.hasOwnProperty("diagnostics")) ? response.diagnostics : {};

      if(response.hasOwnProperty("integration_status") && typeof(response.integration_status) != 'undefined'){
        var status_message = (response.integration_status == 'Pending') ? 'Waiting for response from CHIE' : response.integration_status;
        outputHtml += `Status: ${status_message}<br />`;
      }

      if(response.hasOwnProperty("mrn") && typeof(response.mrn) != 'undefined'){
        outputHtml += `MRN: ${response.mrn}<br />`;
      }
  
      if(response.hasOwnProperty("reference_id") && typeof(response.reference_id) != 'undefined'){
        outputHtml += `Reference ID: ${response.reference_id}<br />`;
      }
  
      if(response.hasOwnProperty("processedAt") && typeof(response.processedAt) != 'undefined'){
        outputHtml += `Processed At: ${response.processedAt}`;
      }
    }
    $('#int-content-' + sent_on).html(outputHtml);
  }

openSignpad(sendType)
{
  //integration start
  if (this.activeTaggedForm.tags.length != null && this.activeTaggedForm.patient.id != 0 ) {
    const msgTagIntegrationBody = {
      "message_tag_id": this.activeTaggedForm.tagIds,
      "patient_id": this.activeTaggedForm.patient.id,
      admissionId: this._structureService.isMultiAdmissionsEnabled && this.admissionId? this.admissionId: undefined,
      action: IntegrationType.SEND_TO_EHR
    };
    this.messageService.checkMessageIntegrationStatus(msgTagIntegrationBody).subscribe((data) => {
      if (data.success) {
        this.onSubmitText(sendType);
      }
    }, () => {
      const message = this._structureService.isMultiAdmissionsEnabled ?
      this.toolTipService.getTranslateData('ERROR_MESSAGES.USER_NO_ACCESS_TO_PATIENT_SITE') : this.toolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG');
      this._structureService.notifyMessage({ message });
    });
  } else {
    this.onSubmitText(sendType);
  }
  //integration end
  }  
  onSubmitText(sendType) { 
    this.sendType = sendType;
    if (this.sendType == "ApproveFile") {
      if (this.noTags) {
        var notify = $.notify('No tags available.');
        setTimeout(function () {
          notify.update({ 'type': 'warning', 'message': '<strong>No tags available.</strong>' });
        }, 1000);
        return false;
      }
      let tagMeta = this.activeTaggedForm.tags[0].tagMeta;

      if(tagMeta && tagMeta!='null' && tagMeta!=null){
     
      let metaValues = JSON.parse(tagMeta);

      this.enableIntegration = metaValues.enableIntegration;

      }

      console.log('checking');
      console.log(this.enableIntegration);
      
      if (this.fileNameFormat.trim() == "" && this.enableIntegration==true) {
     
        var self = this;
        setTimeout(function (){
          swal({
            title: "Are you sure?",
            text: "Do you want to use default filename format?",
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            confirmButtonText: "Ok",
            closeOnConfirm: true
          }, () => {
              self.fileNameFormat = "{tagname}-{FirstName}-{LastName}-{DOB}-{createdOn}-{signedOn}";
              self.generateFilename();
              self.childpad.show_popup();
          });
        }, 1000);
      } else {
     
        this.childpad.show_popup();
      }
    } else {
    
      this.childpad.show_popup();
    }
  }
  
  generateFiles(){
    swal({
      title: "Are you sure?",
      text: "You are going to generate files for this tagged message",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Ok",
      closeOnConfirm: true
    }, (confirm) => {
      if (confirm) {
    
    NProgress.start();
    console.log("**************************",this.activeTaggedForm)
    let sendData: any = {};
    this.msgrpIds =[];
    var messageData = '';
    let msgtagMeta = this.activeTaggedForm.tags[0].tagMeta;
    var taggedMessageDetails = JSON.parse(this.activeTaggedForm.taggedMessageGroups);
    if(taggedMessageDetails){
    for(let i=0;i<taggedMessageDetails.length;i++){
      this.msgrpIds.push(taggedMessageDetails[i].message_id);
    }    
    }
    // TODO: esi_code_for_patient_identity decommisioned, remove usage if it is not causing any issue
    this.patient_esi=this.userData.config.esi_code_for_patient_identity;
    sendData.id = this.msgrpIds;
    sendData.taggedFrom = this.activeTaggedForm.taggedFrom;
    sendData.multiple = this.activeTaggedForm.tagIds;
    sendData.single = this.activeTaggedForm.patient.id?this.activeTaggedForm.patient.id:0;
   // sendData.userid = this.userData.userId;
   var integration;
    if(this.activeTaggedForm.integrationStatus == '' || this.activeTaggedForm.integrationStatus == null){
       integration = 0;
    }else{
      integration = 1;
    }
    if(msgtagMeta && msgtagMeta!='null' && msgtagMeta!=null){

      let tagmetaValues = JSON.parse(msgtagMeta);
      if(tagmetaValues.enableIntegration==true || tagmetaValues.enableIntegration == "true"){
       integration = 1;
    }else{
      integration = 0;
    }
  }
    sendData.integration = integration;
    if(this.activeTaggedForm.signedStatus == 'AutoApproved' || this.activeTaggedForm.signedStatus == 'Approved')
    {
    this._inboxService.uploadChatTags(sendData, "generateFile", timezone.name(),this.patient_esi).then((notifyResult:any) => {
    if (notifyResult.copyLocation) {
       this._structureService.notifyMessage({
       messge: 'Files generated successfully',
       delay: 1000,
       type: 'success'
       });
       if (notifyResult.reload == 1) {
       this.dTable.ajax.reload( null, false );
       }

   }
   else {
    
      if (this.userData.config.enable_progress_note_integration == 1 && notifyResult.enableIntegration == true && (notifyResult.triggerOn == "add-tag" || notifyResult.triggerOn == "approve-tag" )) {
        if (notifyResult.identity_value_Patient == "" || notifyResult.identity_value_Patient == null
            || notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null 
            || notifyResult.integrationFC == '' || notifyResult.integrationFC == null
            || notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null) {
            var messageData = '';
            var messageData1 = '';
            var messageData2 = '';

            if (notifyResult.integrationFC == '' || notifyResult.integrationFC == null) {
                messageData1 += "there is no folder selection in Default Outgoing Filing Center for Integration";
            }
            if(notifyResult.patient_name == ' '|| notifyResult.patient_name == null){
              messageData2 += "patient associated";

            } else
            if (notifyResult.identity_value_Patient == '' || notifyResult.identity_value_Patient == null) {
                messageData += ", MRN ";
                if (notifyResult.patient_name && notifyResult.patient_name != "") {
                    messageData += " of " + notifyResult.patient_name;
                }
            }
            
            let f1 = 0;
            let f2 = 0;
            if (notifyResult.CPRUSERNO == '' || notifyResult.CPRUSERNO == null) {
                f1 = 1;
                messageData += ", USERNO";
            }
            if (notifyResult.CPRUSERNAME == '' || notifyResult.CPRUSERNAME == null) {
                f2 = 1;
                messageData += ", USERNAME";
            }
            if ((f1 == 1 || f2 == 1) && notifyResult.staff_name && notifyResult.staff_name != "") {
                messageData += " of " + notifyResult.staff_name;
            }
            var removeComa = messageData.charAt(0);
            if (removeComa == ',') {
                messageData = messageData.substring(1);
            }
            if (messageData1) {
                var finalMessage = 'Generating Progress Note failed due to missing (' + messageData1 + ')';
            }
            else if (messageData) {
                var finalMessage = 'Generating Progress Note failed due to missing (' + messageData + ')';
            }
            else if (messageData2) {
              var finalMessage = 'Generating Progress Note failed due to missing (' + messageData2 + ')';
          }
            else {
                var finalMessage = '';
            }
            
          }
        }else if(notifyResult.enableIntegration == false || this.userData.config.enable_progress_note_integration != 1){
          var finalMessage = 'Generating Progress Note failed as (INTEGRATION IS DISABLED)';

        }

       if(notifyResult.ignorechecking == 0 || notifyResult.ignorechecking == ""){
        this._structureService.notifyMessage({
        messge: finalMessage,
        delay: 1000,
        type: 'danger'
        });
       }
      }
  
    NProgress.done();

  }).catch((error) => {
    NProgress.done();
    const errorMsg = error && error.data.errors  && error.data.errors.length && error.data.errors[0].message
    ? error.data.errors[0].message: this.toolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG');
    this._structureService.notifyMessage({ message: errorMsg });
  });
  }
  }
});

  }


deleteMsgTag(){
  var IsEnterClicked=false;
  $(document).keypress((event)=> {
    if (event.keyCode == 13) {
        console.log("Enter pressed");
        IsEnterClicked=true;           
    }
  }); 
  swal({
    title: "Are you sure?",
    text: "You are going to remove this tagged message",
    type: "warning",
    showCancelButton: true,
    cancelButtonClass: "btn-default",
    confirmButtonClass: "btn-warning",
    confirmButtonText: "Ok",
    closeOnConfirm: true
}, (confirm) => {
  console.log('IsEnterClicked',IsEnterClicked);
  if(IsEnterClicked){
    IsEnterClicked=false;
      swal.close();   
      return false;
  }
  if(confirm) {
    var taggedMessageDetails = JSON.parse(this.activeTaggedForm.taggedMessageGroups);
    var messageIds = '';
    if(taggedMessageDetails){
    for(let i=0;i<taggedMessageDetails.length;i++){
      messageIds += taggedMessageDetails[i].message_id+',';
    }  
  }
   messageIds =  messageIds.replace(/,$/,"");
  console.log("this.activeTaggedForm",this.activeTaggedForm,messageIds)
    this._structureService.deleteTagForm(this.activeTaggedForm.id,messageIds,this.activeTaggedForm.tagStatus).subscribe((data)=>{
      if(data['data']['chatSignatureApproveTaggedMessageArchive'].signatureImage === 'success') {
        this._structureService.notifyMessage({message: this.toolTipService.getTranslateData('SUCCESS_MESSAGES.TAGGED_MSG_DELETED'), type: CONSTANTS.notificationTypes.success});
      } else {
        let errorMsg = this.toolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG');
          if(this._structureService.isMultiAdmissionsEnabled) {
            errorMsg = this.toolTipService.getTranslateData('ERROR_MESSAGES.USER_NO_ACCESS_TO_PATIENT_SITE');
          }
        this._structureService.notifyMessage({message: errorMsg});
      }
      this.dTable.ajax.reload( null, false );
    });
  }

});
}
getFolderLists()
{
  
  this._structureService.getTenantFilingCenterFolders('OUTGOING').then(      
      (data) => {
        console.log(data['getTenantFilingCenterFolders']);
       if (data['getTenantFilingCenterFolders'] && data['getTenantFilingCenterFolders'].length) {
          this.folderLists = data['getTenantFilingCenterFolders'];            
          this.folderName ='';
          
                      this.filingCenters = [];
                      console.log(this.folderName);
                      console.log(this.folderLists);
          
                     for(let i=0;i<this.folderLists.length;i++){
                       var fname = this.folderLists[i].folderName;
                       var ftype = this.folderLists[i].type;
                       var id = '{"folderName":"'+fname+'","type":"'+ftype+'"}';
                        var item = {id:fname,text:fname}
                        this.filingCenters.push(item);
                     }

                     $('#sfilingCenterss').select2({
                      allowClear: true,
                      placeholder: 'Select Filing Center',
                      data:this.filingCenters
                  });
       }
       else{
         console.log("No folders");
       }
       if(this.tagDetails && this.tagDetails.show == true) {
        this.detailsView();
      }
  });
}

getAlldocumentsSigned(){
  //this.loadDataSignedDocs();
  // this._structureService.getTaggedFormsAll(timezone.name()).then((res) => {
  //   this.taggedFormsList = res['getSessionTenant'] ? JSON.parse(JSON.stringify(res['getSessionTenant']['taggedFormsAll'])) : [];  
  //   console.log(this.taggedFormsList);
  //   this.loadDataSignedDocs();
  // } );
   
}

getPdfTaggedForm(){
  var newWindow:any = window.open(this._structureService.serverBaseUrl+"/webapp/www/img/gif-loader.gif");
  var data = {"tagIds":'',"patientId":'',"tenantId":this._structureService.getCookie('tenantId'),"tenantName":this._structureService.getCookie('tenantName')};
  this._structureService.generateTaggedFormReportPdf(data,3, timezone.name()).then((data)=>{
    console.log(data);
    if(data && data!='') {
      this.downloadUrl = this._structureService.apiBaseUrl +'/citus-health/v4/tagged-form-download.php?filetoken='+data;
    } else {
      this.downloadUrl = this._structureService.serverBaseUrl+"/webapp/www/img/error-icon.png"
    }
    /*var link = document.createElement("a");
    document.body.appendChild(link);
    link.setAttribute("type", "hidden"); 
    link.setAttribute("target", "_blank"); 
    link.download = 'tagged-from';
    link.href = this.downloadUrl ;
    link.click();*/
    //window.open(this.downloadUrl, "_blank");
    newWindow.location = this.downloadUrl;
  });
  var activityData = {
    activityName: "Download All Tagged Messages",
    activityType: "Tagged Messages",
    activityDescription: this.userData.displayName + " downloaded about all tagged message"

  };
  this._structureService.trackActivity(activityData);
}

getPdfReport(){
  var newWindow:any = window.open(this._structureService.serverBaseUrl+"/webapp/www/img/gif-loader.gif");
  this._structureService.getPdfReport({messageId:this.msgId,tenantId:this.userData.tenantId,tenantName:this.userData.tenantName,msgrpIds:this.msgrpIds}).subscribe((data:any)=>{
      console.log(data);
      if(data && "data" in data && "chatSignatureApproveTaggedMessageDownload" in data.data && "signatureImage" in data.data.chatSignatureApproveTaggedMessageDownload && data.data.chatSignatureApproveTaggedMessageDownload.signatureImage && data.data.chatSignatureApproveTaggedMessageDownload.signatureImage != '') {
        this.downloadUrl  = this._structureService.apiBaseUrl +'/citus-health/v4/tagged-form-download.php?filetoken='+data.data.chatSignatureApproveTaggedMessageDownload.signatureImage;
      } else {
        this.downloadUrl = this._structureService.serverBaseUrl+"/webapp/www/img/error-icon.png"
      }
     // console.log(data['data']['chatSignatureApproveTaggedMessageDownload'].signatureImage);
    
      /*var link = document.createElement("a");
      document.body.appendChild(link);
      link.setAttribute("type", "hidden"); 
      link.setAttribute("target", "_blank"); 
      link.download = 'tagged-from';
      link.href = this.downloadUrl ;
      link.click();*/
      //window.open(this.downloadUrl, "_blank");
      newWindow.location = this.downloadUrl;
      /* link.setAttribute("href", this.downloadUrl);
      link.setAttribute("download", this.downloadUrl);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link); */
  });
  var activityData = {
    activityName: "Download Tagged Messages",
    activityType: "Tagged Messages",
    activityDescription: this.userData.displayName + " downloaded tagged message("+ this.activeTaggedForm.message +"["+ this.activeTaggedForm.messageId+"]) datas- ("+ JSON.stringify(this.activeTaggedForm) +")"

  };
  this._structureService.trackActivity(activityData);
}
generateFilename(){
   this.fileNameFormat = this.fileNameFormat.replace('{tagname}',this.activeTaggedForm.tags[0].tagName);    
   if(this.activeTaggedForm.patient.firstName!=null){
    this.fileNameFormat = this.fileNameFormat.replace('{FirstName}',this.activeTaggedForm.patient.firstName);    
  }
  else{
    this.fileNameFormat = this.fileNameFormat.replace('{FirstName}','');    
  }
  if(this.activeTaggedForm.patient.lastName!=null){
    this.fileNameFormat = this.fileNameFormat.replace('{LastName}',this.activeTaggedForm.patient.lastName);    
  }
  else{
    this.fileNameFormat = this.fileNameFormat.replace('{LastName}','');    
  }
  if(this.dob && this.dob!=null){
    this.fileNameFormat = this.fileNameFormat.replace('{DOB}',this.dob);    
  }
  else{
    this.fileNameFormat = this.fileNameFormat.replace('{DOB}','');    
  }
  if(this.createdOn!=null){
    let crOn = this.createdOn;
    crOn = crOn.replace(/\//g,'').replace(/:/g,'').replace(/-/g,'').replace(/ /g,'');
    this.fileNameFormat = this.fileNameFormat.replace('{createdOn}',crOn);    
  }
  else{
    this.fileNameFormat = this.fileNameFormat.replace('{createdOn}','');    
  }
  var date:Date = new Date();
  var datetostring = Date.parse(date.toString())/1000;
  var cdate = this.datePipe.transform(datetostring*1000,"MM/dd/yyyy")+' '+this.datePipe.transform(datetostring*1000,"shortTime");
  cdate = cdate.replace(/\//g,'').replace(/:/g,'').replace(/-/g,'').replace(/ /g,'');
  this.fileNameFormat = this.fileNameFormat.replace('{signedOn}',cdate); 
  this.fileNameFormat = this.fileNameFormat.replace(/ /g, "-");
}

CopyToClipboard(containerid) {

  console.log('~~~~~~~~~~~~~~~~~~~~');
  console.log(containerid);
  

  let para = $('#' + containerid)[0] as HTMLInputElement;
  // let para = document.getElementById(containerid) as HTMLInputElement;
  this.selectElementText(para) 

  if (window.getSelection) { // all modern browsers and IE9+
    let selectedText = window.getSelection().toString()
    console.log(selectedText);


    const el = document.createElement('textarea');
    el.value = selectedText;
    document.body.appendChild(el);
    el.select();
    document.execCommand('copy');
    document.body.removeChild(el);
    this.selectElementText(para);

  }

}
selectElementText(el) {
  var range = document.createRange() // create new range object
  range.selectNodeContents(el) // set range to encompass desired element text
  var selection = window.getSelection() // get Selection object from currently user selected text
  selection.removeAllRanges() // unselect any user selected text (if any)
  selection.addRange(range) // add range to Selection object to select it
}
detailsView(){ 
    this._SharedService.innerPageFilter = true; 
  if(this.tagDetails) {
    this.activeTaggedForm =  this.tagDetails.activeForm;
  }
  if (this.isDetail == true) {
    this.viewPage = true;
  }
  else
  {
    console.log('in else');
    this.viewPage = false;
  }
  
  this.tagsList=[];
  this.msgrpIds=[];
  if(this.activeTaggedForm.patient.id!='0'){
    this.patientName=this.activeTaggedForm.patient.displayName;
    if(this.activeTaggedForm.patient.dateOfBirth)
    {
    let dateVar = this.activeTaggedForm.patient.dateOfBirth.split('-');
    this.dateOfBirth = dateVar[1]+'/'+dateVar[2]+'/'+dateVar[0];
    this.dob= dateVar[1]+'-'+dateVar[2]+'-'+dateVar[0];
    }
    else
    {
      this.dob= '';
    }
  }
  console.log("timezone.name()",timezone.name())
  this.createdOn = new Date(this.activeTaggedForm.sentOn).toLocaleString('en-US', { timeZone: timezone.name() }).replace(/:\d{2}\s/,' ');
  this.createdOn  = this.createdOn.replace( /,/g, "" );
  if (this.activeTaggedForm.sentOn.indexOf('-') > -1)
    {
      this.createdOn = new Date(this.activeTaggedForm.sentOn).toLocaleString('en-US', { timeZone: timezone.name() }).replace(/:\d{2}\s/,' ');
      this.createdOn  = this.createdOn.replace( /,/g, "" );
    }else{

      this.createdOn  =  this.datePipe.transform(this.activeTaggedForm.sentOn*1000,"MM/dd/yyyy")+' '+this.datePipe.transform(this.activeTaggedForm.sentOn*1000,"shortTime");
    }
  if(this.activeTaggedForm.tags && this.activeTaggedForm.tags.length > 0){
    console.log(this.activeTaggedForm.tags.length);
    this.activeTaggedForm.tags.filter((data)=>{
      if(data.tagName.trim()!=''){
        this.tagsList.push(data.tagName);
      }
    });    

    let tagMeta = this.activeTaggedForm.tags[0].tagMeta;
    this.fileNameFormat = "";
    console.log(tagMeta);
    this.folderName = "Select Filing Center";
    if(tagMeta && tagMeta!='null' && tagMeta!=null){
      this.noTags = false;
      let metaValues = JSON.parse(tagMeta);
      this.fileNameFormat = metaValues.fileSaveFormat;
      if(this.fileNameFormat.trim()==""){
        this.fileNameFormat = "";
      }else{
        this.generateFilename();
      }
      
      if(this.isFilingCenter && this.userData.config.enable_multisite != 1){
        if(this.folderLists && this.folderLists.length>0){
          this.folderNameDefault = this.folderLists.filter((row)=>{
            if(row.folderName == metaValues.outgoingFilingCenter){
              return true;
            }
          })
        }else {
          var notify = $.notify('Error in loading filing centers.');
          setTimeout(function () {
            notify.update({ 'type': 'warning', 'message': '<strong>Error in loading filing centers.</strong>' });
          }, 1000);
        }
       
        if(this.folderNameDefault && this.folderNameDefault[0]){
          console.log(this.folderNameDefault[0]);
          $('#sfilingCenterss').val(this.folderNameDefault[0].folderName).trigger('change');
         // $('#sfilingCenterss').val(this.folderNameDefault[0].folderName);
          this.folderName= this.folderNameDefault[0];
        }else{
          this.clearFilingCenter();
          this.folderName = "Select Filing Center";
        }
      }
      
     // console.log(this.folderName[0]);
     var activityData = {
      activityName: "View Tagged Messages in Detail",
      activityType: "Tagged Messages",
      activityDescription: this.userData.displayName + " viewed/closed tagged message("+ this.activeTaggedForm.message +"["+ this.activeTaggedForm.messageId+"]) datas- ("+ JSON.stringify(this.activeTaggedForm) +")"

    };
    this._structureService.trackActivity(activityData);
    }
    else{
      /*this.noTags = true;
      this.folderName = "Select Filing Center";
      this.fileNameFormat="";*/
    }
  }
  this.msgId = this.activeTaggedForm.messageId;
  this.tags="Progress Note";
  if(this.activeTaggedForm.taggedMessageGroups && this.activeTaggedForm.taggedMessageGroups !=null){
    console.log("this.activeTaggedForm.taggedMessageGroups==========>"+typeof this.activeTaggedForm.taggedMessageGroups);
    console.log("this.activeTaggedForm.taggedMessageGroups==========>"+this.activeTaggedForm.taggedMessageGroups);
    var taggedMessageDetails = JSON.parse(this.activeTaggedForm.taggedMessageGroups);
    console.log("taggedMessageDetails1111111111==========>" +typeof taggedMessageDetails);
   //taggedMessageDetails = JSON.parse(taggedMessageDetails);
    console.log("taggedMessageDetails==========>" +typeof taggedMessageDetails);
    this.description=[];
    if(taggedMessageDetails){
    for(let i=0;i<taggedMessageDetails.length;i++){
      console.log("taggedMessageDetails[i].message========> ",taggedMessageDetails[i]);
      this.msgrpIds.push(taggedMessageDetails[i].message_id);
      // this.description = this.description +"<span class='tag-message box arrow-left-bottom' style='border: 1px solid #c1c1c1;padding: 2px 12px;border-radius: 10px;position: relative;top: 4px;'>"+taggedMessageDetails[i].message+"</span>";
      //var messageTime =  moment(taggedMessageDetails[i].createdDate*1000).utcOffset(this.userData.config.tenant_timezone).format("MM/DD/Y h:m:s A");
      // var localTime = new Date(taggedMessageDetails[i].createdDate*1000).toLocaleString("en-US", {timeZone: this.userData.config.tenant_timezoneName});
      // var messageTime = moment(localTime).format("MM/DD/Y h:m:s A");
      var messageTime = new Date(taggedMessageDetails[i].createdDate).toLocaleString('en-US', { timeZone: this.userData.config.tenant_timezoneName }).replace(/:\d{2}\s/,' ');
      messageTime  = messageTime.replace( /,/g, "" );
      if (taggedMessageDetails && taggedMessageDetails[i] && taggedMessageDetails[i].createdDate && taggedMessageDetails[i].createdDate.toString().indexOf('-') > -1)
        {
          console.log("messageTime - if")
          messageTime = new Date(taggedMessageDetails[i].createdDate).toLocaleString('en-US', { timeZone: this.userData.config.tenant_timezoneName }).replace(/:\d{2}\s/,' ');
          messageTime  = messageTime.replace( /,/g, "" );
        }else{
          console.log("messageTime - e;lse")
          messageTime  = taggedMessageDetails[i].createdDate ? this.datePipe.transform(taggedMessageDetails[i].createdDate*1000,"MM/dd/yyyy")+' '+this.datePipe.transform(taggedMessageDetails[i].createdDate*1000,"shortTime") : '';
        }


      console.log("this.userData.config.tenant_timezone==");
      console.log(this.userData.config.tenant_timezone,this.userData.config.tenant_timezoneName);
      console.log("messageTime",this.activeTaggedForm.sentOn,taggedMessageDetails[i].createdDate,messageTime)
      console.log(taggedMessageDetails[i].createdDate);
      console.log("messageTime")
      console.log(messageTime)
      var pushData = (taggedMessageDetails[i].createUserFirstName ? taggedMessageDetails[i].createUserFirstName+" " :'' )+(taggedMessageDetails[i].createUserLastName ? taggedMessageDetails[i].createUserLastName + " " : '')+messageTime;
      pushData = pushData ? pushData +" : "+taggedMessageDetails[i].message : taggedMessageDetails[i].message;
      this.description.push(pushData);
      /* if(i<taggedMessageDetails.length-1){
        this.description= this.description+"<br>";

      }*/
    }
    }
  }else{
    this.description = this.activeTaggedForm.message;
  }
  if(this.activeTaggedForm.signedStatus=="Pending"){
    this.isApproveShow=true;
  }
  else{
    this.isApproveShow=false;
  }
  const patient = !isBlank(this.activeTaggedForm.patient);
  const admission = !isBlank(this.activeTaggedForm.patient.admission);
  if(this._structureService.isMultiAdmissionsEnabled && patient && admission) {
    this.admissionName = this.activeTaggedForm.patient.admission.admissionName || '';
    this.admissionId = this.activeTaggedForm.patient.admission.id || '';
  }
  this.createdBy = this.activeTaggedForm.createdUser.displayName;
  this.approvedBy = (this.activeTaggedForm.signedStatus=="AutoApproved")?"Auto Approved":this.activeTaggedForm.approvedUserDisplayName;
  this.approveSignUrl = "";
  if(this.activeTaggedForm.sign!=null && this.activeTaggedForm.sign!='null'){
  this.approveSignUrl = this._structureService.getApiBaseUrl() + "writable/filetransfer/uploads/"+this.activeTaggedForm.sign;
  }
  this.outGoingFileCenter='';
  //this.fileNameFormat="{tagname}-{FirstName}-{LastName}-{DOB}";
  this.isDetail=!this.isDetail; 
  this.imgUrl = "";
  this.pdfUrl = "";
  if(this.activeTaggedForm.createdSIGN!=null){
    this.imgUrl =  this._structureService.getApiBaseUrl() + "writable/filetransfer/uploads/"+this.activeTaggedForm.createdSIGN;    
   
  }
  this.dTable.ajax.reload( null, false );
}
downloadPdf()
{
  $('.buttons-pdf').trigger('click');
}
   selectedDate(value: any, datepicker?: any) {
    // this is the date  selected
    console.log("selectedDate",value);
    // return false;
  this.selectedExport = true;
    // any object can be passed to the selected event and it will be passed back here
    datepicker.start = value.start;
    datepicker.end = value.end;
 
    // use passed valuable to update state
    this.daterange.start = value.start;
    this.daterange.end = value.end;
    this.daterange.label = value.label;
    var table = $('#examplesigned').DataTable();
    if(this.selectedExport == true)
    table.button('.buttons-excel').trigger();
  }
   toTimestamp(strDate){
    var datum = Date.parse(strDate);
   return (datum/1000).toString();
   }
    loadDataSignedDocs(){   
      this.dataLoadingMsg = true;

      var self = this;
      let datas: any;
           if (this.dTable) {
              this.dTable.destroy();
            }else{
              this._structureService.resetDataTable();
            }
            $(()=> {            
          this.dTable = $('#examplesigned').DataTable({ 
            autoWidth: false,
            responsive: true,
            bprocessing: true,
            bServerSide: true,
            bpagination: true,
            bsorting: true,
            retrieve: true,
            bsearching: true,
            stateSave: false,
            bInfo: true,
            lengthMenu: [ [ 25, 50 ], [ 25, 50 ] ],
            order: [[ 6, "desc" ]],
            fnDrawCallback: function(oSettings) {
              console.log('oSettings', oSettings);
              (
                oSettings._iRecordsTotal == 0 ||
                oSettings._iRecordsTotal < oSettings._iDisplayLength ||
                oSettings.aoData.length == 0
              ) ? $('.dataTables_paginate').hide() : $('.dataTables_paginate').show();

              (oSettings.aoData.length == 0) ? $('.dataTables_info').hide() : $('.dataTables_info').show();
            },
            fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
              $(nRow).on('click', (e) => {
                console.log(aData,"datatatata");
                this.activeTaggedForm = aData;
                if(e.target.id == "inte-status-not-sftp") {
                  let ids = 'inte-status-not-sftp-' + this.activeTaggedForm['sentOn'];
                  let idtenant = (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') :  this.userData.tenantId;
                  this.popoverNotSftp(ids, 'loader', this.activeTaggedForm['sentOn']);
                  this._formsService.getIntegrationDetails(
                    'not-sftp', 
                    this.activeTaggedForm['requestId'],
                    idtenant,
                    this.activeTaggedForm['patient']['id']
                  ).then((row) => {
                    this.integrationDetails = row;
                    this.integrationDetails.type = 'inte-status-not-sftp';
                    this.popoverNotSftp(ids, 'show', this.activeTaggedForm['sentOn']);
                    console.log(row);
                    console.log('#inte-status-not-sftp-' + this.activeTaggedForm['sentOn']);
                  });
                }else if (e.target.id.substring(0, 9) == "int-close") {
                  let ids = 'inte-status-not-sftp-' + this.activeTaggedForm['sentOn'].replace('int-close-', "");
                  this.popover(ids, 'close');
                }else if (e.target.id == "int-copy-sent") {
                  this.CopyToClipboard('int-content-' + this.activeTaggedForm['sentOn']);
                } 
              });
            },
            dom:
        "<'row'<'col-sm-4 'l><'col-sm-4'f><'col-sm-2 searchButton'>>" +
        "<'row'<'col-sm-12'tr>>" +
        "<'row'<'col-sm-5'i><'col-sm-7'p>>",
          buttons: [
            {
              extend: 'excel',
              text: 'All Pages',
              title: 'Tagged Messages',
              exportOptions: {
                columns:this.enable_worklist == true?[0,1,2,3,4,6,7]:[0,1,2,3,4,6]
              },
              action: function ( e, dt, node, config ) {
              var selfButton = this;
              var oldStart = dt.settings()[0]._iDisplayStart;
              dt.one('preXhr', function (e, s, data) {
                  data.start = 0;
                  data.length = this.totalCt;
                  dt.one('preDraw', function (e, settings) {
                      $.fn.dataTable.ext.buttons.excelHtml5.action.call(selfButton, e, dt, node, config);
                      dt.one('preXhr', function (e, s, data) {
                          settings._iDisplayStart = oldStart;
                          data.start = oldStart;
                      });
                      setTimeout(dt.ajax.reload, 0);
                      return false;
                  });
              });
              dt.ajax.reload();
              }
      }],
            initComplete: function(){
              $('.dataTables_filter label input').attr('placeholder','Search');
              $('.dataTables_filter label input').unbind();
              $("div.dataTables_filter input").on('keydown', function(e) {
              if (e.which == 13) {
                var value = $("div.dataTables_filter input").val();
                if(value)
                {
                value = value.replace('”','"');
                value = value.replace("‘","'");
                value = value.replace("’","'");
                self.storeService.storeData(Store.SEARCH_TAGGED_MSG, value);
                self.dTable.search(value).draw();
              }
              else
              {
                self.storeService.removeData(Store.SEARCH_TAGGED_MSG);
                self.dTable.search('').draw();
                self._structureService.notifySearchFilterApplied(false);
                }
              }
              });
              $("div.dataTables_filter input").on('keypress', function(e) {
              $(".searchBEdu").prop('disabled', false);
              });
              $("div.dataTables_filter input").on('keyup', function(e) {
              var value = $("div.dataTables_filter input").val();
              if(value)
              {
              }
              else
              $(".searchBEdu").prop('disabled', true);
              });
              $("div.searchButton")
              .html('<button disabled="true" class="btn btn-sm btn-info searchBEdu" title="Search" type="submit">Search</button>'+
              '<button style="margin-left:10px;" class="btn btn-sm btn-default resetBEdu" title="Reset" type="submit">Reset</button>');
              var value =  $("div.dataTables_filter input").val();
              if(value)
              {
              $(".searchBEdu").prop('disabled', false);
              }
              $("div.dataTables_filter input").on('paste', function(event) {
              console.log("eeeeeeeeeeeeeeeeee",event);
              var element = this;
              var text ;
              setTimeout(function () {
                text = $(element).val();
                if(text)
                {
                $(".searchBEdu").prop('disabled', false);
                }
              }, 100);
              });
              $(".buttons-collection").click(function(event) {
                setTimeout(function () {
                  if($(".buttons-collection").attr("aria-expanded") === "true" && $(".dt-button-collection").find("button").length == 0){
                    $(".dt-button-collection").remove();
                    $(".dt-button-background").remove();
                    $(".buttons-collection").attr("aria-expanded","false");
                  }
                },500);
            });
              },
            ajax: function(dat, callback, settings) {
              let orderData;
              let searchText;
              let orderby;
              let limit;
      let startDate = '';
        let endDate = '';
        let label = '';
      
              var i = dat.order[0].column ? dat.order[0].column : '';
              orderby = dat.order[0].dir ? dat.order[0].dir : '';
              if (isInteger(i)) {
                orderData = dat.columns[i].data ? dat.columns[i].data : '';
              } else {
                orderData = 'id';
              }
              
              if (!isBlank(dat.search.value)) {
                searchText = dat.search.value;
              } else if (!isBlank(self.storeService.getStoredData(Store.SEARCH_TAGGED_MSG))) {
                searchText = self.storeService.getStoredData(Store.SEARCH_TAGGED_MSG);
                $('div.dataTables_filter input').val(searchText);
                if(self.initialLoad) self._structureService.notifySearchFilterApplied(true);
              }
       if(self.selectedExport == true){
          startDate = self.toTimestamp(self.daterange.start);
          endDate = self.toTimestamp(self.daterange.end);
          label = self.daterange.label;
          self.selectedExport = false;
        }
              console.log('ORDERRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRR');
              console.log(i);
              console.log(dat);
              console.log(orderData);
              console.log(orderby);
              console.log('ORDERRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRR');
      
              console.log(
                'searchData-fields',
                'i - ',
                i,
                'offset - ',
                dat.start,
                'orderby - ',
                orderby,
                'orderData - ',
                orderData,
                'searchText - ',
                searchText
              );
            let  siteIds = self.siteIds;
            this.userRole = self._structureService.getCookie('userRole');
            
              self._structureService.getTaggedFormsAllPaginated(dat.length, dat.start, orderData, orderby, searchText,startDate,endDate,siteIds)
                .then((resultData) => {
                  self.initialLoad = false;
                  this.dataLoadingMsg = false;
                  datas = {};
                  this.datam = {};
                  // if (dat.start == 0) {
                    // this.totalCt = resultData['getSessionTenant'].assignedMaterialPagination.totalCount;
                    this.totalCt = (resultData['getSessionTenant'] && resultData['getSessionTenant']['taggedFormsAllPaginated'] && resultData['getSessionTenant']['taggedFormsAllPaginated']['totalCount'])
                    ? resultData['getSessionTenant']['taggedFormsAllPaginated']['totalCount'] : 0
                  // }
                  datas = [];
                  datas = (resultData['getSessionTenant'] && resultData['getSessionTenant']['taggedFormsAllPaginated'] && resultData['getSessionTenant']['taggedFormsAllPaginated']['data'])
                    ? resultData['getSessionTenant']['taggedFormsAllPaginated']['data']
                    : [];

                    console.log("datas",datas)
                    this.responseData =datas;
                    console.log("datas",this.responseData)

                  this.groupList = datas;
                  let draw;
                  let total;
                  if (datas && datas.length == 0 && searchText == '') {
                    draw = 0;
                    total = 0;
                  } else {
                    draw = dat.draw;
                    total = this.totalCt;
                  }
      
                  this.datam = {
                    draw: draw,
                    recordsTotal: total,
                    recordsFiltered: total,
                    aaData: datas
                  };
                  callback(this.datam);
                });
            },
            columns: [
              {title: "#"},
              {title: "Message Tags", data: 'tagnames'},
              {title: "Patient Name", data: 'patient.displayName'},
              {title: this.toolTipService.getTranslateData('ADMISSION.LABELS.ADMISSION'), data: 'patient.admission.admissionName'},
              {title: this.labelSite, data: 'siteName'},
              {title: "Created By", data: 'createdUser.displayName'},
              {title: "Created On", data: 'sentOn'},
              {title: "Created On", data: 'sentOn'}, 
              {title: "Status", data: 'signedStatus'},
              {title: "Integration status", data: 'integrationStatus' },     
              {title: "Actions"}
            ],
            columnDefs: [
            {
              data: null,
              orderable: false,
              width: "5%",
              targets: 0,
              render: function (data, type, row, meta) {
                let roNo = meta.row + 1 + meta.settings._iDisplayStart;
                return roNo;
              }
             },
             { 
              data: null,
              targets: 1,
              width: "20%",
            },
             { 
               data: null,
               targets: 2,
               width: "15%",
               render : function (data, type, row) {
                return data == 'null null' ? '' : data;
              }
            },
            {
              data: null,
              targets: 3,
              width: '15%',
              visible: this._structureService.isMultiAdmissionsEnabled,
              render: (data, type, row) => {
                return row.patient && row.patient.admission ? row.patient.admission.admissionName : '';
              }
            },
            { 
              data: null,
              visible: (self.userRole != 'Patient' && self.hideSiteSelection),
              targets: 4,
              orderData:4,
              width: "20%",
              render:(document,type,row)=>{ 
                if(row.siteName!=null){
                  return row.siteName;
              }else{
                  return null;
              }
              }
            },
            { 
              data: null,
              targets: 5,
              width: "15%",
            },
            { 
              data: null,
              targets: 6,
              orderData:6,
              width: "20%",
              render:(document,type,row)=>{ 
                if (row.sentOn && row.sentOn.toString().indexOf('-') > -1)
                  {
                    let a = new Date(row.sentOn).toLocaleString('en-US', { timeZone: timezone.name() }).replace(/:\d{2}\s/,' ');
                    return a.replace( /,/g, "" );
                  }else{

                    return this.datePipe.transform(row.sentOn*1000,"MM/dd/yyyy")+' '+this.datePipe.transform(row.sentOn*1000,"shortTime");
                  }
              }
            },
            { 
              data: null,
              targets: 7,
             // visible:false,
              orderData:7,
              width: "20%",
              render:(document,type,row)=>{ 
                if (row.sentOn && row.sentOn.toString().indexOf('-') > -1)
                  {
                    let a = new Date(row.sentOn).toLocaleString('en-US', { timeZone: timezone.name() }).replace(/:\d{2}\s/,' ');
                    return a.replace( /,/g, "" );
                  }else{

                    return this.datePipe.transform(row.sentOn*1000,"MM/dd/yyyy")+' '+this.datePipe.transform(row.sentOn*1000,"shortTime");
                  }
              }
            },{ 
              data: null,
              targets: 6,
              visible:false,
              render:(document,type,row)=>{
                if (row.sentOn.toString().indexOf('-') > -1)
                  {                                   
                    var dateTimeParts = document.split(' ');
                    var timeParts = dateTimeParts[1].split(':');
                    var dateParts = dateTimeParts[0].split('-');              
                    var date = new Date(parseInt(dateParts[0]), parseInt(dateParts[1], 10) - 1, parseInt(dateParts[2]), parseInt(timeParts[0]), parseInt(timeParts[1]));
                    return date.getTime()/1000;

                  } else{
                    return row.sentOn;
                  } 
              }
            },            
            { 
              data: null,
              targets: 9,
              width: "10%",
             visible: this.enable_worklist == true,
             

              render:function(document,type,row){
                

                let sentPopover = ``;
                let statusHtml = '';

          let popoverSent = `<div id="inte-status-not-sftp-${row.sentOn}" class="fade show bs-popover-right inte-popover" style="display:none;"
          >
            <div class="popover-arrow"></div>
            <div class="pop-title">
                <button type="button" id="int-close-${row.sentOn}" class="close fa fa-close"></button>

                <div>
                    <i class="copy-ico fa fa-files-o" id="int-copy-sent" aria-hidden="true"></i>
                </div>
            </div>
            <div class="pop-content">
                <div class="int-loader">
                    <img src="./assets/modules/dummy-assets/common/img/spinner.svg" alt="loading">
                </div>
                <div class="int-main-content" id="int-content-${row.sentOn}">
                    ${sentPopover}
                </div>
            </div>    
           </div>`;
           let style;
           let text;
           let color_code;
           if(row.requestId && row.requestId!= null && row.requestId != ""){
            if(row.integrationStatus == "pending"){
              style = 'badge-warning';
              text = 'Processing';
              color_code = '#f39834';
              }         
    
              if(row.integrationStatus == "completed"){
                style = 'badge-success';
                text = 'Completed';
                color_code = '#46be8a';
              }
    
              if(row.integrationStatus == "failed"){
                style = 'badge-danger';
                text = 'Failed';
                color_code = '#fb434a';
              }
              
              statusHtml += `
                <div style="position:relative;">
                <span class="badge ${style} mr-2 mb-2">${text}</span>
                <span style="font-size: 14px; color: ${color_code}; cursor: pointer;">
                <i class='icmn-info'id="inte-status-not-sftp"></i>
                </span>
              ${popoverSent}
              </div>`;
           } else{
            statusHtml = '';
           }
           
         return statusHtml;
          
              }
            },
            { 
              data: null,
              targets:8,
              width: "10%",
              render:function(document,type,row){
                let actionsColor = 'badge-success';  
                let actionText=row.signedStatus;
                if(row.signedStatus=="Pending"){
                  actionsColor = 'badge-danger';
                }else if(row.signedStatus=="AutoApproved"){
                  actionText="Auto Approved";
                }
                let actions = '<span class="badge '+actionsColor +' mr-2 mb-2">'+actionText+'</span>';                  
                return actions;
              }
            }, {
                data: null,
                orderable: false,
                render: function (document, type, row) {
                let actions = '';     
                let integrationFc=""; 
                if(row.tags && row.tags.length > 0){
                let tagMeta = row.tags[0].tagMeta;
                if(tagMeta && tagMeta!='null' && tagMeta!=null){               
                let metaValues = JSON.parse(tagMeta);          
                self.enableIntegrationfortag = metaValues.enableIntegration;
                self.integrationFC = metaValues.integrationFC;
                if(self.userData.config.progress_note_integration_mode !== 'webhook'){
                  integrationFc=self.integrationFC;
                }
                }
              }
                actions +='<a title="View" class="cat__core__link--underlined mr-3" id="viewdetails" target="_blank"><i id="viewdetails" class="icmn-eye"></i></a><a title="Delete" class="cat__core__link--underlined mr-3" id="deleteDetails" target="_blank"><small><i id="deleteDetails" class="icmn-cross"></i></small></a>'
               if((row.signedStatus == 'AutoApproved' || row.signedStatus == 'Approved') && self.enableIntegrationfortag == true &&  (integrationFc || self.userData.config.enable_multisite == "1" || self.userData.config.enable_multisite == 1) ){
                let tooltip = "Send to EHR";
                if (self.userData.config.label_for_file_generation) {
                  tooltip = self.userData.config.label_for_file_generation;
                }
                actions +=`<a title="${tooltip}" class="cat__core__link--underlined mr-3" id="generateFiles" target="_blank"><i id="generateFiles" class="icmn-folder-upload"></i</a>`
               }
               
                return actions;
              },
              width: "15%",
              targets: 10
            }]
          });

          $(document).on('click', '.resetBEdu',(event)=> {
            self.storeService.removeData(Store.SEARCH_TAGGED_MSG);
            self.dTable.search('').draw();
            self._structureService.notifySearchFilterApplied(false);
            $(".searchBEdu").prop('disabled', true);
            });
            $(document).on('click', '.searchBEdu',(event)=> {
            var value = $('#examplesigned_wrapper #examplesigned_filter label input').val();
            if(value)
            {
            value = value.replace('”','"');
            value = value.replace("‘","'");
            value = value.replace("’","'");
            self.dTable.search(value).draw();
            self.storeService.storeData(Store.SEARCH_TAGGED_MSG, value);
            }
            else
            {
            self.dTable.search('').draw();
            }
             });
      
          this.dataLoadingMsg = false;
                  });

    }

    sendSignature(sign){
      if((sign.close==true)||(sign.close=="true")){
        return false;
      }
      this.signature = sign.signature;
      if(this.sendType=="ApproveFile"){
        if(this.noTags){
          var notify = $.notify('No tags available.');
          setTimeout(function () {
            notify.update({ 'type': 'warning', 'message': '<strong>No tags available.</strong>' });
          }, 1000);
          return false;
        }
        if (this.userData.config.progress_note_integration_mode != "webhook") {
          this.moveFilingCenter = true;
        }
        else {
          this.moveFilingCenter = false;
        }
        this.folderType = 'OUTGOING';
        /* if(this.userData.config.progress_note_integration_mode !="webhook"){
        if(this.folderName == "Select Filing Center"){
          var notify = $.notify('Select Outgoing Filing Center.');
          setTimeout(function () {
            notify.update({ 'type': 'warning', 'message': '<strong>Select Outgoing Filing Center.</strong>' });
          }, 1000);
          
          return false;
        }
        else{
          this.folderName = this.folderName.folderName;
        }
      } */
      this.folderName = ''; 
        this.approveMessage();
      }else{
        this.folderName = '';
        this.filename = '';
        this.folderType = '';
        this.moveFilingCenter = false;
        this.approveMessage();
      }
      
    }
    isArray(obj : any ) {
      return Array.isArray(obj)
    }
    approveMessage(){
      
      this._structureService.approveMessage({sign:this.signature,messageId:this.msgId,userID: this.userData.userId,tenantId:this.userData.tenantId,tenantName:this.userData.tenantName,filename:this.fileNameFormat,folder:this.folderName,folderType:this.folderType,moveFilingCenter:this.moveFilingCenter,msgrpIds:this.msgrpIds}).subscribe((data)=>{
        var messageData = '';
        if(data['data']['chatSignatureApproveTaggedMessage'].signatureImage && data['data']['chatSignatureApproveTaggedMessage'].signatureImage !== 'failed'){
          this.isApproveShow=false;
          this.approveSignUrl = this._structureService.getApiBaseUrl() + "writable/filetransfer/uploads/"+data['data']['chatSignatureApproveTaggedMessage'].signatureImage;
          this.approvedBy = this.userData.displayName;
          // let indexVar = this.taggedFormsList.indexOf(this.activeTaggedForm);
          // if(indexVar>-1){
            if(this.activeTaggedForm){
            // this.taggedFormsList[indexVar].approvedUserDisplayName=this.approvedBy;
            // this.taggedFormsList[indexVar].sign = data['data']['chatSignatureApproveTaggedMessage'].signatureImage;
            // this.taggedFormsList[indexVar].signedStatus="Approved";
            var notify = $.notify('Success! The message has been approved.');
            setTimeout(function () {
              notify.update({ 'type': 'success', 'message': '<strong>Success! The message has been approved.</strong>' });
            }, 1000);
            this.loadDataSignedDocs();

         if(this.moveFilingCenter){
              var activityDat = {
                activityName: "Copied the Tagged Message to Filing center",
                activityType: "Filing Center",
                activityDescription: this.userData.displayName+ " approved message tagged by "+this.createdBy +" copied to filing center named "+this.folderName+"("+this.folderType+") as "+this.fileNameFormat
              };
              this._structureService.trackActivity(activityDat);
         }
          var activityData = {
            activityName: "Approve Tagged Message",
            activityType: "forms",
            activityDescription: this.userData.displayName + " approved message("+this.msgId+") tagged by "+this.createdBy

          };
          this._structureService.trackActivity(activityData);
            
          }
        }
        else {
          let errorMsg = this.toolTipService.getTranslateData('ERROR_MESSAGES.MESSAGE_APPROVAL');
          if(this._structureService.isMultiAdmissionsEnabled && data['errors'] && data['errors'].message === 'Invalid admissionId') {
            errorMsg = this.toolTipService.getTranslateData('ERROR_MESSAGES.USER_NO_ACCESS_TO_PATIENT_SITE');
          }
          this._structureService.notifyMessage({ messge: errorMsg });
        }
        
        var notifyResult: any = data;
        notifyResult=data['data']['chatSignatureApproveTaggedMessage'];
        if(this.userData.config.enable_progress_note_integration == 1 && notifyResult.enableIntegration == "true")
        {
          if(notifyResult.triggerOn == "approve-tag" && notifyResult.approvalRequired == "true")
          {
            var messageData1 = '';
            if(notifyResult.integrationFC  == "" || notifyResult.integrationFC == null)
            {
              messageData1 += "there is no folder selection in Default Outgoing Filing Center for Integration";    
            }
            if(notifyResult.identityValuePatient == '' || notifyResult.identityValuePatient == null)
            {
              messageData += ", MRN";   
              if(notifyResult.patientName && notifyResult.patientName!= "")
            {
                messageData += " of "+notifyResult.patientName;  
            }  
            }
            let f1=0;
            let f2=0;
            if(notifyResult.CPRUSERNO =='' || notifyResult.CPRUSERNO == null)
            {
              f1 =1;
              messageData += ", USERNO";   
            }
            if(notifyResult.CPRUSERNAME =='' || notifyResult.CPRUSERNAME == null)
            {
              f2 =1;
              messageData += ", USERNAME"; 
            }
            if((f1 == 1 || f2 == 1)  && notifyResult.staffName && notifyResult.staff_name!= "")
            {
            messageData += " of "+notifyResult.staffName; 
            }
            var removeComa = messageData.charAt(0);
            if(removeComa == ',')
            {
            messageData = messageData.substring(1);
            }
            if(messageData1 != '' && messageData !='')
            {
            var finalMessage = 'Generating Progress Note failed due to '+messageData1 +' and missing  ('+ messageData+')';
            console.log("finalMessage",finalMessage);
          }
          else if(messageData1 && messageData == '')
          {
            var finalMessage = 'Generating Progress Note failed due to '+messageData1;
            console.log("finalMessage",finalMessage);
          }
          else if(messageData && messageData1 == '')
          {
            var finalMessage = 'Generating Progress Note failed due to missing ('+messageData+')';
            console.log("finalMessage",finalMessage);
            }
            else
            {
                var finalMessage = '';
            }
          }
        }
        if(finalMessage)
        {
        var activityDat = {
          activityName: "Failed Coping the Tagged Message to Filing center",
          activityType: "Filing Center",
          activityDescription: this.userData.displayName+ " approved message tagged by "+this.createdBy +"failed copied to filing center named "+this.folderName+"("+this.folderType+") as "+this.fileNameFormat + 'due to '+finalMessage
        };
        this._structureService.trackActivity(activityDat);
      }
      this.reload.next();
      },()=>{
        this._structureService.notifyMessage({
          messge: this.toolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG')
        });
      });
    }

    compare(a,b) {
      //console.log(a.sent);
      if (a.timestamp > b.timestamp)
         return -1;
      if (a.sent < b.sent)
        return 1;
        return 0;
    }










}
