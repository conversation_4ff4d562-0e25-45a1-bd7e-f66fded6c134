import { Component, OnInit, Renderer, ElementRef, Input } from '@angular/core';
import { StructureService } from '../structure.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject } from 'rxjs';
import { isBlank, isContainXssTag } from 'app/utils/utils';
import { DomSanitizer } from '@angular/platform-browser';
import { Modal } from './view-document';
import { Router } from '@angular/router';
import { ToolTipService } from '../tool-tip.service';
import { HttpService } from 'app/services/http/http.service';
import { APIs } from 'app/constants/apis';
import { Store, StoreService } from '../shared/storeService';
import { CONSTANTS } from 'app/constants/constants';
import * as moment from 'moment';

declare const $: any;
declare const NProgress: any;
@Component({
  selector: 'app-manage-documents',
  templateUrl: './manage-documents.component.html'
})
export class ManageDocumentsComponent implements OnInit {
  @Input() loadDocumentFor = '';
  uploadDocumentFile: FormGroup;

  documentDataTable;
  documentRowData: any;
  manageDocuments;
  fileUploadBtn = '';
  docFileUpdate = '';
  updateFileID;
  pdfOrDocSrc = '';
  isModalOpen = false;
  cancelLabel = false;
  showIframe = false;
  modalBody;
  title;
  subTitle;
  showHeader;
  showFooter;
  docFile;
  dropiFy;
  dropiFyEvent;
  dynamic: boolean = false;
  invalidFileType = false;
  signDocFile = [];
  crudStatus: boolean = true;
  showInTemplate: boolean = true;
  disableFolderddl: boolean = false;
  ddlSiteID: any = '0';
  FillingCenterdTable;
  FillingFolderRowData: any;
  folderFilingName;
  folderFilingAction;
  eventsSubject: Subject<void> = new Subject<void>();
  showSiteSelection: boolean;
  siteIds: string[] = [];
  filterSiteIds: any = '0';
  singleSelection: boolean = false;
  tableDataCallback;
  totalCount;
  totalCt: any;
  userData: any;
  searchResetFlag = 0;
  editSiteData: any;
  editFolderForm: boolean = false;
  showFolderCenter: boolean = true;
  showDocCenter: boolean = false;
  dataLoadingMsg = true;
  initialLoad = true;
  translations: Record<string, string> = {};
  selection: 'type' | 'category' = 'type';
  ddlDocumentTypes: any = [];
  selectedDocumentTypes;
  documentTypesSettings = {
    singleSelection: false,
    text: '',
    primaryKey: 'id',
    labelKey: 'tagName',
    classes: 'myclass custom-class',
    enableSearchFilter: true,
    enableFilterSelectAll: true
  };
  documentCategorySettings = {
    singleSelection: true,
    text: '',
    primaryKey: 'typeCode',
    labelKey: 'typeName',
    classes: 'myclass custom-class',
    enableSearchFilter: true,
    enableFilterSelectAll: false
  };
  ddlDocumentCategories = [];
  selectedDocumentCategory: any;
  allowedFileExtensions = CONSTANTS.allowedFileExtensions;
  isTypeLinkedToCategory: boolean;
  preventMultipleClick = false;
  constructor(
    public structureService: StructureService,
    private elementRef: ElementRef,
    private renderer: Renderer,
    private httpService: HttpService,
    private formBuild: FormBuilder,
    private sanitizer: DomSanitizer,
    private modals: Modal,
    private router: Router,
    private tooltipService: ToolTipService,
    private storeService: StoreService
  ) {
    this.renderer.listen(this.elementRef.nativeElement, 'click', (event, target) => {
      if (event.target.id === 'archiveDocumentFile') {
        this.structureService
          .showAlertMessagePopup({
            text: this.tooltipService.getTranslateData('MESSAGES.DOCUMENT_FILE_ARCHIVE_CONFIRM'),
            type: 'warning'
          })
          .then((confirm: boolean) => {
            if (confirm) {
              this.archiveDocumentFile(this.documentRowData.id);
              const activityData = {
                activityName: 'Archived Document File',
                activityType: 'Document Management',
                activityDescription: `${this.userData.displayName} has archived the document ${this.documentRowData.documentName}`
              };
              this.structureService.trackActivity(activityData);
            }
          });
      } else if (event.target.id === 'viewDocument') {
        this.showPdf(this.documentRowData);
        const activityData = {
          activityName: 'View Document File',
          activityType: 'Document Management',
          activityDescription: `${this.userData.displayName} has viewed the document ${this.documentRowData.documentName}`
        };
        this.structureService.trackActivity(activityData);
      } else if (event.target.id === 'renameFile') {
        const activityData = {
          activityName: 'Rename Document File',
          activityType: 'Document Management',
          activityDescription: `${this.userData.displayName} has renamed the document ${this.documentRowData.documentName}`
        };
        this.structureService.trackActivity(activityData);
        this.updateFile(this.documentRowData);
      } else if (event.target.id === 'designFile') {
        this.router.navigate(['/signature/signature-requests/document-self-service/' + this.documentRowData.id]);
      }
    });
  }
  get isUnlinkedModal() {
    return this.loadDocumentFor.toString() === 'unlinked';
  }
  ngOnInit() {
    this.getTranslations();
    this.initialLoad = true;

    this.formInit();
    this.userData = this.structureService.getUserdata();
    this.dropiFyEvent = $('.dropify').dropify({
      messages: {
        default: 'Drag and drop a file here or click',
        replace: 'Drag and drop or click to add',
        remove: 'Remove',
        error: 'Ooops, something wrong happend.'
      },
      error: {
        fileSize: 'The file size is too big (10 MB max).',
        minWidth: 'The image width is too small ({{ value }}}px min).',
        maxWidth: 'The image width is too big ({{ value }}}px max).',
        minHeight: 'The image height is too small ({{ value }}}px min).',
        maxHeight: 'The image height is too big ({{ value }}px max).',
        imageFormat: 'The image format is not allowed ({{ value }} only).'
      }
    });
    this.dropiFy = this.dropiFyEvent.data('dropify');
  }

  /** form controls Initialization */
  formInit() {
    this.uploadDocumentFile = this.formBuild.group({
      ddlDocumentTypes: [''],
      ddlDocumentCategory: [''],
      fileup: ['', [Validators.required]],
      fileName: ['']
    });
  }
  getTranslations() {
    this.tooltipService
      .getTranslateDataPipe([
        'BUTTONS.ARCHIVE',
        'BUTTONS.VIEW',
        'BUTTONS.EDIT',
        'INFO_MESSAGES.NO_DATA_AVAILABLE',
        'BUTTONS.SEARCH',
        'BUTTONS.RESET',
        'LABELS.DOCUMENT_NAME',
        'LABELS.FILE_TYPE',
        'LABELS.SITES',
        'LABELS.FILE_SIZE',
        'LABELS.UPLOADED_ON',
        'GENERAL.ACTIONS',
        'BUTTONS.SAVE',
        'MESSAGES.FILE_UPDATE_FAILED',
        'MESSAGES.FILE_UPLOAD_FAILED',
        'LABELS.DOCUMENT_TYPES',
        'LABELS.DOCUMENT_CATEGORY',
        'LABELS.SOURCE'
      ])
      .subscribe((translations) => {
        this.translations = translations;
        this.documentCategorySettings.text = this.translations['LABELS.DOCUMENT_CATEGORY'];
        this.documentTypesSettings.text = this.translations['LABELS.DOCUMENT_TYPES'];
      });
  }

  /** Document template  Listing */
  listAllFiles() {
    const {
      'BUTTONS.ARCHIVE': archiveLabel,
      'BUTTONS.VIEW': viewLabel,
      'BUTTONS.EDIT': editLabel,
      'INFO_MESSAGES.NO_DATA_AVAILABLE': noDataAvailable,
      'BUTTONS.SEARCH': searchLabel,
      'BUTTONS.RESET': resetLabel,
      'LABELS.DOCUMENT_NAME': documentNameLabel,
      'LABELS.FILE_TYPE': fileTypeLabel,
      'LABELS.SITES': sitesLabel,
      'LABELS.FILE_SIZE': fileSizeLabel,
      'LABELS.UPLOADED_ON': uploadedOnLabel,
      'GENERAL.ACTIONS': actionsLabel,
      'LABELS.DOCUMENT_TYPES': documentTypesLabel,
      'LABELS.DOCUMENT_CATEGORY': documentCategoryLabel,
      'LABELS.SOURCE': source
    } = this.translations;
    this.dataLoadingMsg = true;
    if (this.documentDataTable) {
      this.documentDataTable.destroy();
    } else {
      this.structureService.resetDataTable();
    }
    let documents: any;
    this.documentDataTable = $('#tblDocuments').DataTable({
      autoWidth: false,
      responsive: true,
      bprocessing: true,
      bServerSide: true,
      bpagination: true,
      bsorting: true,
      retrieve: true,
      stateSave: true,
      bsearching: true,
      bInfo: true,
      lengthMenu: [
        [25, 50],
        [25, 50]
      ],
      language: {
        infoEmpty: noDataAvailable
      },
      fnDrawCallback: (oSettings) => {
        if (oSettings._iRecordsTotal < oSettings._iDisplayLength || oSettings._iRecordsTotal == 0 || oSettings.aoData.length == 0) {
          $('.dataTables_paginate').hide();
        } else {
          $('.dataTables_paginate').show();
        }
        if (oSettings.aoData.length == 0) {
          $('.dataTables_info').hide();
        } else {
          $('.dataTables_info').show();
        }
      },
      fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
        $(nRow).on('click', () => {
          this.documentRowData = aData;
        });
      },
      dom:
        "<'row justify-content-between'<'col-sm-4 'l><'col-sm-8 row justify-content-end'f<'searchButton ml-3'>>>" +
        "<'row'<'col-sm-12'tr>>" +
        "<'row'<'col-sm-5'i><'col-sm-7'p>>",
      initComplete: () => {
        $('.dataTables_filter label input').attr('placeholder', searchLabel);
        $('.dataTables_filter label input').unbind();
        $('#tblDocuments_filter').on('keydown', (e) => {
          if (e.which == 13) {
            this.searchResetFlag = 0;
            let tblValue = $('#tblDocuments_filter input').val();
            if (tblValue) {
              tblValue = tblValue.replace('”', '"');
              tblValue = tblValue.replace('‘', "'");
              tblValue = tblValue.replace('’', "'");
              this.documentDataTable.search(tblValue).draw();
            } else {
              this.documentDataTable.search('').draw();
            }
          }
        });
        $('#tblDocuments_filter input').on('keypress', (e) => {
          $('.searchFile').prop('disabled', false);
        });
        $('#tblDocuments_filter input').on('keyup', (e) => {
          let tblFilterValue = $('#tblDocuments_filter input').val();
          if (tblFilterValue) {
          } else $('.searchFile').prop('disabled', true);
        });
        $('div.searchButton').html(
          `<button disabled="true" class="btn btn-sm btn-info searchFile" title="${searchLabel}" type="submit">${searchLabel}</button>
            <button style="margin-left:10px;" class="btn btn-sm btn-default resetFile" title="${resetLabel}" type="submit">${resetLabel}</button>`
        );
        let tblDocumentsFilterValue = $('#tblDocuments_filter input').val();
        if (tblDocumentsFilterValue) {
          $('.searchFile').prop('disabled', false);
        }
        $('#tblDocuments_filter input').on('paste', () => {
          let text;
          setTimeout(() => {
            text = $('#tblDocuments_filter input').val();
            if (text) {
              $('.searchFile').prop('disabled', false);
            }
          }, 100);
        });
        $('.buttons-collection').click((event) => {
          setTimeout(() => {
            if ($('.buttons-collection').attr('aria-expanded') === 'true' && $('.dt-button-collection').find('button').length == 0) {
              $('.dt-button-collection').remove();
              $('.dt-button-background').remove();
              $('.buttons-collection').attr('aria-expanded', 'false');
            }
          }, 500);
        });
      },

      ajax: (dat, callback, settings) => {
        this.dataLoadingMsg = true;
        NProgress.start();
        let orderData;
        let searchTextfile = '';
        let orderby;

        let columnNumber = dat.order[0].column ? dat.order[0].column : '';
        orderby = dat.order[0].dir ? dat.order[0].dir : '';
        if (columnNumber != 0) {
          orderData = dat.columns[columnNumber].data ? dat.columns[columnNumber].data : '';
        } else {
          orderData = '';
        }
        const searchStoredTextFile = !this.isUnlinkedModal && this.storeService.getStoredData(Store.SEARCH_MANAGE_DOCUMENT_UPLOAD);
        if (dat.search.value) {
          searchTextfile = dat.search.value;
          if (this.initialLoad) {
            this.structureService.notifySearchFilterApplied(true);
          }
        } else if (this.initialLoad && searchStoredTextFile && searchStoredTextFile.searchTextfile) {
          searchTextfile = searchStoredTextFile.searchTextfile;
          this.structureService.notifySearchFilterApplied(true);
        } else if (!this.isUnlinkedModal) {
          this.storeService.removeData(Store.SEARCH_MANAGE_DOCUMENT_UPLOAD);
          this.structureService.notifySearchFilterApplied(false);
        }
        if (searchTextfile && this.searchResetFlag == 0) {
          dat.start = 0;
          settings.oAjaxData.search.value = searchTextfile;
          settings.oAjaxData.start = 0;
          settings._iDisplayStart = 0;
          this.searchResetFlag = 1;
        }
        if (!this.isUnlinkedModal && (!searchStoredTextFile || (searchStoredTextFile && searchStoredTextFile.searchTextfile !== searchTextfile))) {
          this.storeService.storeData(Store.SEARCH_MANAGE_DOCUMENT_UPLOAD, { searchTextfile });
        }
        $('#tblDocuments_filter input').val(searchTextfile);
        if (
          settings.oAjaxData.start != 0 &&
          this.tableDataCallback &&
          this.tableDataCallback.aaData &&
          this.tableDataCallback.aaData.length === 1 &&
          settings._iDisplayStart != 0 &&
          searchTextfile == ''
        ) {
          settings.oAjaxData.start = settings.oAjaxData.start - settings.oAjaxData.length;
          settings._iDisplayStart = settings.oAjaxData.start;
        }
        this.structureService
          .getFileListLazy(dat.length, dat.start, searchTextfile, orderData, orderby.toUpperCase(), this.filterSiteIds, '', this.isUnlinkedModal)
          .then((res: any) => {
            this.dataLoadingMsg = false;
            this.initialLoad = false;
            NProgress.done();
            this.tableDataCallback = {};
            if (dat.start == 0) {
              this.totalCt = res.documents.totalCount;
              this.totalCount = this.totalCt;
            } else {
              if (this.totalCount) {
                this.structureService.setCookie('totalCount', this.totalCt, 1);
                this.totalCt = this.totalCount;
              } else {
                this.totalCt = this.structureService.getCookie('totalCount');
                this.totalCount = this.totalCt;
              }
            }
            documents = res.documents ? res.documents.data : [];
            let total;
            if (documents && documents.length == 0 && searchTextfile == '') {
              total = 0;
            } else {
              total = this.totalCt;
            }
            this.tableDataCallback = {
              draw: dat.draw,
              recordsTotal: total || 0,
              recordsFiltered: total || 0,
              aaData: documents
            };
            callback(this.tableDataCallback);
          });
      },
      order: [[8, 'desc']],
      columns: [
        { title: '#' }, // 0
        { title: documentNameLabel, data: 'documentName' }, // 1 orderable
        { title: documentTypesLabel, data: 'documentTypes' }, // 2 not orderable
        { title: documentCategoryLabel, data: 'documentCategoryName' }, // 3 orderable
        { title: source, data: 'source' }, // 4 orderable
        { title: sitesLabel, data: 'sites' }, // 5 not orderable
        { title: fileTypeLabel, data: 'fileType' }, // 6 orderable
        { title: fileSizeLabel, data: 'fileSize' }, // 7 orderable
        { title: uploadedOnLabel, data: 'createdOn' }, // 8 orderable
        { title: actionsLabel, data: '' } // 9 not orderable
      ],
      columnDefs: [
        {
          data: null,
          orderable: false,
          render: (data, type, row, meta) => {
            return meta.row + 1 + meta.settings._iDisplayStart;
          },
          width: '1%',
          targets: 0 // index column
        },
        {
          data: null,
          targets: 1, // documentName
          width: '8%'
        },
        {
          data: null,
          targets: 2, // documentTypes
          width: '22%',
          orderable: false,
          visible: !this.isUnlinkedModal,
          render: (document, type, row) => {
            if (isBlank(row.documentTypes)) {
              return ' ';
            }
            return row.documentTypes.map((documentType) => documentType.name).join(', ');
          }
        },
        {
          data: null,
          targets: 3, // documentCategoryName
          width: '10%',
          visible: this.structureService.isDocumentCategoryEnabled && !this.isUnlinkedModal
        },
        {
          data: source,
          targets: 4, // source
          width: '8%',
          orderable: false
        },
        {
          data: null,
          targets: 5, // sites
          width: '18%',
          orderable: false,
          render: (document, type, row) => {
            if (!isBlank(row.sites)) {
              return row.sites.map((site) => site.name).join(', ');
            } else {
              return '';
            }
          }
        },
        {
          data: null,
          targets: 6, // fileType
          width: '6%',
          render: (document, type, row) => {
            if (row.fileType === '') {
              return ' ';
            }
            return row.fileType.split('/').pop();
          }
        },
        {
          data: null,
          targets: 7, // fileSize
          width: '6%',
          render: (document, type, row) => {
            if (row.fileSize == '' || row.fileSize == '0') {
              return '0 KB';
            } else {
              return this.fileSize(row.fileSize);
            }
          }
        },
        {
          data: null,
          targets: 8, // createdOn
          width: '8%',
          render: (document, type, row) => {
            if (row.createdOn == '') {
              return '-';
            } else {
              return moment.unix(row.createdOn).format('MMM DD YYYY hh:mm A');
            }
          }
        },
        {
          data: null,
          targets: 9, // actions
          width: '18%',
          orderable: false,
          visible: !this.isUnlinkedModal,
          render: (data, type, row) => {
            return `<div class="btn-group mr-2 mb-2 no-margin" aria-label="" role="group" >
                        <button title="${viewLabel}" type="button" class="pull-right btn btn-sm icmn-eye" id="viewDocument" data-document-signature-inside="${row}"></button>
                        <button title="${archiveLabel}" type="button" class="pull-right btn btn-sm icmn-box-add" id="archiveDocumentFile"></button>
                        <button title="${editLabel}" type="button" id="renameFile" data-document-rename="${row}" class="btn btn-sm icmn-pencil"></button>
                        <button title="Design" hidden type="button" id="designFile" data-document-rename="${row}" class="btn btn-sm icmn-file-text"></button>
                  </div>`;
          }
        }
      ]
    });

    //search and reset action of datatable
    $(document).on('click', '.resetFile', (event) => {
      this.storeService.removeData(Store.SEARCH_MANAGE_DOCUMENT_UPLOAD);
      this.structureService.notifySearchFilterApplied(false);
      this.documentDataTable.search('').draw();
      $('.searchFile').prop('disabled', true);
    });
    $(document).on('click', '.searchFile', (event) => {
      let value = $('#tblDocuments_wrapper #tblDocuments_filter label input').val();
      if (value) {
        value = value.replace('â', '"');
        value = value.replace('â', "'");
        value = value.replace('â', "'");
        const searchText = value;
        if (!searchText || this.initialLoad) {
          this.structureService.notifySearchFilterApplied(false);
        }
        this.searchResetFlag = 0;
        this.documentDataTable.search(value).draw();
      } else {
        this.documentDataTable.search('').draw();
      }
    });
  }

  /** archiveDocumentFile() performes archive functionality of file */
  archiveDocumentFile(fileId) {
    const url = `${APIs.documentSelfServiceFileManagement}?fileId=${fileId}`;
    this.httpService.doDelete(url).subscribe(
      (res: any) => {
        if (res.status.code == 200) {
          // track activity
          const activityData = {
            activityName: 'Archive Document File',
            activityType: 'Document Management',
            activityDescription: `${this.userData.displayName} document file (${fileId}) archived successfully`
          };
          this.structureService.trackActivity(activityData);
          this.initAfterUpdate();
          if (res.data && res.data.message) {
            this.structureService.notifyMessage({ message: res.data.message, type: 'success' });
          }
        }
      },
      (error) => {
        this.initAfterUpdate();
        if (error.status && error.status.message) {
          this.structureService.notifyMessage({ message: error.status.message, type: 'danger' });
        }
        // track activity
        const activityData = {
          activityName: 'Archive Document File',
          activityType: 'Document Management',
          activityDescription: `${this.userData.displayName} document file (${fileId}) archive failed`
        };
        this.structureService.trackActivity(activityData);
      }
    );
  }
  /**btnUploadClick() reset file upload form controls*/
  btnUploadClick() {
    if (this.preventMultipleClick) return;
    this.preventMultipleClick = true;
    this.getDocumentTypes();
    this.fileUploadBtn = 'BUTTONS.SAVE';
    this.uploadDocumentFile.reset();
    this.dynamic = false;
    this.editSiteData = [];
    this.isTypeLinkedToCategory = false;
    this.ddlDocumentCategories = [];
    this.ddlDocumentTypes = [];
    this.selectedDocumentTypes = [];
    this.selectedDocumentCategory = [];
    this.selection = 'type';
    this.formInit();
    this.removeFile();
    this.docFileUpdate = '';
    this.showUploadModal();
  }
  /** getDocumentTypes() returns all document types */
  getDocumentTypes(fileData?) {
    this.structureService
      .getDocumentTagList({ excludeCategoryAssociatedType: this.structureService.isDocumentCategoryEnabled })
      .subscribe(({ data }) => {
        const ddlDocumentTypes = data.getSessionTenant.documentTags.data;
        this.ddlDocumentTypes = JSON.parse(JSON.stringify(ddlDocumentTypes));
        if (fileData && fileData.documentTypes && fileData.documentTypes.length > 0 && this.selection === 'type') {
          const selectedDocumentTypes = fileData.documentTypes.map((documentType) => documentType.id);
          this.selectedDocumentTypes = this.ddlDocumentTypes.filter(({ tagGuid }) => {
            return selectedDocumentTypes.includes(tagGuid);
          });
          this.isTypeLinkedToCategory = selectedDocumentTypes.length !== this.selectedDocumentTypes.length;
        }
      });
  }
  getDocumentCategories(fileData?) {
    this.structureService.getDocumentTagtype().then((data: any) => {
      const documentTagTypes = data && data.getSessionTenant && data.getSessionTenant.documentTagTypes;
      this.ddlDocumentCategories = JSON.parse(JSON.stringify(documentTagTypes));
      if (this.selection === 'category' && fileData && fileData.documentCategoryName) {
        this.selectedDocumentCategory = this.ddlDocumentCategories.filter((documentCategory) => {
          return documentCategory.typeCode === fileData.documentCategoryGuid;
        });
      }
    });
  }
  changeSelection(selection: 'type' | 'category') {
    if (selection !== this.selection) {
      this.selection = selection;
      this.documentCategorySettings.singleSelection = selection === 'category';
      if (selection === 'type') {
        this.getDocumentTypes();
      } else {
        this.getDocumentCategories();
      }
    }
  }
  /** onUploadDocumentFile() verify and selet valid file when selecting a file */
  onUploadDocumentFile(event): void {
    let fileList: FileList = event.target.files;
    if (fileList.length > 0) {
      let fileExt = fileList[0].name.split('.').pop().toLowerCase();
      if (this.allowedFileExtensions.includes(fileExt) && fileList[0].size <= 10485760 && !isContainXssTag(fileList[0].name)) {
        this.docFile = fileList[0].name;
        let fileToUpload = fileList[0];
        this.signDocFile.push(fileToUpload);
        this.uploadDocumentFile.controls.fileup.setErrors(null);
      } else {
        this.uploadDocumentFile.controls['fileup'].setErrors({ incorrect: true });
        $('.dropify-clear').trigger('click');
      }
    } else {
      this.docFile = '';
    }
  }

  /** showDocumentCenter() for Upload file button show and hide */
  showDocumentCenter() {
    this.dataLoadingMsg = true;
    this.showFolderCenter = false;
    this.showDocCenter = true;
    this.initialLoad = true;
    this.listAllFiles();
  }

  /** removeFile() for clear selected file from dropiFy */
  removeFile() {
    this.docFile = '';
    this.uploadDocumentFile.controls['fileup'].reset();
    this.dropiFy.resetPreview();
    this.dropiFy.clearElement();
    this.signDocFile = [];
    $('.dropify-error').css('display', 'none');
    $('.dropify-wrapper').removeClass('has-error');
  }

  /** removeFile() for clear file while updating */
  removeUpdateFile() {
    this.docFileUpdate = '';
    this.uploadDocumentFile.controls['fileup'].reset();
  }

  /** fileManipulation() for performing save and upate functionality */
  fileManipulation() {
    if (this.preventMultipleClick) return;
    this.preventMultipleClick = true;
    this.dataLoadingMsg = true;
    const sendData = new FormData();
    if (this.fileUploadBtn === 'BUTTONS.SAVE' || (this.signDocFile.length > 0 && !this.docFileUpdate)) {
      sendData.append('file[]', this.signDocFile[0]);
    }
    if (this.fileUploadBtn !== 'BUTTONS.SAVE' && this.updateFileID) {
      sendData.append('fileId', this.updateFileID);
    }
    sendData.append('client', this.structureService.socket.io.engine.id);
    if (this.selection === 'type' && this.selectedDocumentTypes && this.selectedDocumentTypes.length > 0) {
      this.selectedDocumentTypes.forEach((documentType) => {
        sendData.append('documentTypeIds[]', documentType.tagGuid);
      });
    } else if (
      this.selection === 'category' &&
      this.selectedDocumentCategory &&
      this.selectedDocumentCategory[0] &&
      this.selectedDocumentCategory[0].typeCode
    ) {
      sendData.append('documentCategoryId', this.selectedDocumentCategory[0].typeCode);
    }
    sendData.append('fileName', this.uploadDocumentFile.controls.fileName.value);
    if (!isBlank(this.siteIds)) {
      this.siteIds.forEach((siteId) => {
        sendData.append('siteIds[]', siteId);
      });
    } else if (!this.showSiteSelection) {
      // Single site access
      this.userData.mySites.forEach((site) => {
        sendData.append('siteIds[]', site.id);
      });
    }
    NProgress.start();
    this.cancel();
    const errorMessage = this.translations[this.fileUploadBtn !== 'BUTTONS.SAVE' ? 'MESSAGES.FILE_UPDATE_FAILED' : 'MESSAGES.FILE_UPLOAD_FAILED'];
    this.httpService.doPostForm(APIs.documentSelfServiceFileManagement, sendData).subscribe(
      (res: any) => {
        if (res.success) {
          this.initAfterUpdate();
          this.structureService.notifyMessage({ message: res.data.message, type: 'success' });
          const activityData = {
            activityName: 'Upload Document File',
            activityType: 'Document Management',
            activityDescription: `${this.userData.displayName} document file uploaded successfully`
          };
          this.structureService.trackActivity(activityData);
        } else {
          const activityData = {
            activityName: 'Upload Document File',
            activityType: 'Document Management',
            activityDescription: `${this.userData.displayName} document file upload failed`
          };
          this.structureService.trackActivity(activityData);
          this.initAfterUpdate();
          this.structureService.notifyMessage({ message: res.message ? res.message : errorMessage, type: 'danger' });
        }
      },
      () => {
        const activityData = {
          activityName: 'Upload Document File',
          activityType: 'Document Management',
          activityDescription: `${this.userData.displayName} document file upload failed`
        };
        this.structureService.trackActivity(activityData);
        this.structureService.notifyMessage({ message: errorMessage, type: 'danger' });
        this.initAfterUpdate();
      }
    );
  }

  /** Initialize Control after Update */
  initAfterUpdate() {
    NProgress.done();
    this.cancel();
    this.dataLoadingMsg = true;
    this.initialLoad = true;
    this.selection = undefined;
    this.documentDataTable.search('').draw();
    this.ddlSiteID = '0';
    this.showInTemplate = true;
    this.removeFile();
  }
  cancel() {
    if ($('#UploadFileModal').hasClass('show')) {
      setTimeout(() => {
        this.selection = undefined;
        $('#UploadFileModal').modal('hide');
        this.preventMultipleClick = false;
      }, 200);
    }
  }

  /** Update File */
  updateFile(fileData) {
    this.selection = 'type';
    this.ddlDocumentCategories = [];
    this.ddlDocumentTypes = [];
    this.selectedDocumentTypes = [];
    this.selectedDocumentCategory = [];
    this.isTypeLinkedToCategory = false;
    if (fileData.documentCategoryName && this.structureService.isDocumentCategoryEnabled) {
      this.selection = 'category';
      this.getDocumentCategories(fileData);
    } else {
      this.getDocumentTypes(fileData);
    }
    this.editSiteData = fileData.sites.map((site) => site.id);
    this.fileUploadBtn = 'BUTTONS.UPDATE';
    this.uploadDocumentFile.patchValue({
      fileName: fileData.documentName
    });
    this.updateFileID = fileData.id;
    this.docFileUpdate = fileData.documentOriginalName;
    this.showInTemplate = fileData.enableFile;
    this.removeFile();
    this.showUploadModal();
  }
  showUploadModal() {
    setTimeout(() => {
      $('#UploadFileModal').modal('show');
      this.preventMultipleClick = false;
    }, 200);
  }
  /** getSiteIds() for select siteIDs while creating folder */
  getSiteIds(siteId: any) {
    this.filterSiteIds = siteId['siteId'].toString();
    this.ddlSiteID = this.filterSiteIds;
    this.showDocumentCenter();
  }

  /** getSiteIds() for get siteIDs (Multisite Case) */
  getSiteIdsInvite(siteId: any) {
    this.siteIds = siteId.siteId === '0' ? [] : siteId.siteId;
  }

  /** hideDropdown() for display and hide based on multisite activation */
  hideDropdown(hideItem: any) {
    this.showSiteSelection = hideItem.hideItem;
  }

  /** showFileInSignature() for handling Show the file during Signature Request process toggle button in file upload section */
  showFileInSignature(satus) {
    this.showInTemplate = satus;
  }

  /** showPdf() for view uploaded files*/
  showPdf(targetEvent): void {
    if (!targetEvent.documentUniqueName || isBlank(targetEvent.documentUniqueName)) {
      this.structureService.notifyMessage({ message: this.tooltipService.getTranslateData('MESSAGES.ERROR_NO_SUCH_FILE_FOUND'), type: 'danger' });
    } else {
      this.isModalOpen = true;
      this.cancelLabel = true;
      this.showIframe = true;
      this.subTitle = targetEvent.documentUniqueName;
      // Below URL will be replaced with fetching blob from server with authentication header
      this.pdfOrDocSrc = `${this.structureService.getApiBaseUrl()}writable/filetransfer/uploads/document-self-service/${this.userData.tenantId}/${targetEvent.documentUniqueName}.pdf#toolbar=0`;
      this.modalBody = this.sanitizer.bypassSecurityTrustResourceUrl(this.pdfOrDocSrc);
      this.modals.show();
    }
  }

  /** closeModal() for close modal window*/
  closeModal() {
    this.modals.hide();
    this.isModalOpen = false;
    this.modalBody = '';
    this.pdfOrDocSrc = '';
    this.showIframe = false;
    this.cancelLabel = false;
  }

  /** fileSize() returns converted file size*/
  fileSize(bytes) {
    if (bytes == 0) {
      return '0.00 B';
    }
    let e = Math.floor(Math.log(bytes) / Math.log(1024));
    return (bytes / Math.pow(1024, e)).toFixed(2) + ' ' + ' KMGTP'.charAt(e) + 'B';
  }
}
