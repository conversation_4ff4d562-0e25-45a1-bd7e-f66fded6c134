<section class="card">
  <div class="card-header">
    <div class="row">
      <div class="cat__core__title col-md-6">
        <strong [translate]="isUnlinkedModal ? 'LABELS.UNLINKED_DOCUMENTS' : 'LABELS.MANAGE_DOCUMENTS'"></strong>
      </div>
      <div class="filter-sites-wrapper col-md-6" [hidden]="!showSiteSelection || !showDocCenter">
        <div class="col-md-12" style="float: right">
          <div class="site-label col-md-4">
            <span translate="LABELS.FILTER_BY_SITES"></span>
          </div>
          <div class="col-md-8" style="width: 73%">
            <app-select-sites
              [events]="eventsSubject.asObservable()"
              [filterType]="true"
              (hideDropdown)="hideDropdown($event)"
              (siteIds)="getSiteIds($event)"
            >
            </app-select-sites>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="card-block">
    <ol class="breadcrumb" *ngIf="!isUnlinkedModal">
      <li class="breadcrumb-item"><a [routerLink]="['/inbox']" translate="MENU.INBOX_AS_HOME"></a></li>
      <li class="breadcrumb-item" translate="LABELS.MANAGE_DOCUMENTS"></li>
    </ol>
    <div class="row" *ngIf="!isUnlinkedModal">
      <div class="col-sm-12">
        <ul class="float-right">
          <button
            type="button"
            class="float-right btn btn-sm btn-primary"
            (click)="btnUploadClick()"
            [disabled]="preventMultipleClick"
            translate="LABELS.UPLOAD_FILE"
          ></button>
        </ul>
      </div>
    </div>
    <div class="row" *ngIf="isUnlinkedModal && totalCount > 0">
      <div class="col-sm-12">
        <div class="col-sm-12">
          <div class="alert alert-info">
            <i class="self-inventory icmn-info mr-2"></i><strong translate="WARNING.REACH_ADMIN_TO_LINK_DOCUMENT"></strong>
          </div>
        </div>
      </div>
    </div>
    <div class="row">
      <div class="col-sm-12">
        <div class="col-sm-12">
          <div class="tab-content">
            <div class="tab-pane active" id="tabDocumentTemplate" role="tabcard" aria-expanded="true">
              <div class="row">
                <div style="text-align: center; width: 100%" *ngIf="dataLoadingMsg">
                  <img src="./assets/img/loader/loading.gif" />
                </div>
                <div class="col-sm-12" [hidden]="dataLoadingMsg">
                  <table class="table table-hover" id="tblDocuments" width="100%"></table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<div id="UploadFileModal" class="modal fade" role="dialog">
  <div class="modal-dialog" style="max-width: 600px">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" translate="LABELS.FILE_UPLOAD"></h5>
        <button type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
      <div class="modal-body">
        <form [formGroup]="uploadDocumentFile" class="form-horizontal" id="uploadDocumentFile">
          <div class="form-body">
            <div class="form-group row" *ngIf="structureService.isDocumentCategoryEnabled">
              <div class="col-sm-12 d-flex flex-row flex-nowrap justify-content-evenly">
                <div class="col-sm-6" (click)="changeSelection('type')">
                  <input type="radio" id="docType" name="selection" value="type" [checked]="selection === 'type'" />
                  <label for="docType" translate="LABELS.DOCUMENT_TYPES"></label>
                </div>
                <div class="col-sm-6" (click)="changeSelection('category')">
                  <input type="radio" id="docCategory" name="selection" value="category" [checked]="selection === 'category'" />
                  <label for="docCategory" translate="LABELS.DOCUMENT_CATEGORY"></label>
                </div>
              </div>
            </div>
            <div class="form-group row" *ngIf="selection === 'type'">
              <div class="col-sm-4">
                <label class="control-label" translate="LABELS.DOCUMENT_TYPES"></label>
              </div>
              <div class="col-sm-8">
                <angular2-multiselect
                  [data]="ddlDocumentTypes"
                  [(ngModel)]="selectedDocumentTypes"
                  [settings]="documentTypesSettings"
                  formControlName="ddlDocumentTypes"
                  id="ddlDocumentTypes"
                ></angular2-multiselect>
              </div>
            </div>
            <div class="form-group row" *ngIf="selection === 'type' && isTypeLinkedToCategory">
              <div class="col-sm-12">
                <div class="alert alert-warning" translate="WARNING.DOCUMENT_TYPE_LINKED_TO_CATEGORY"></div>
              </div>
            </div>
            <div class="form-group row" *ngIf="selection === 'category'">
              <div class="col-sm-4">
                <label class="control-label" translate="LABELS.DOCUMENT_CATEGORY"></label>
              </div>
              <div class="col-sm-8">
                <angular2-multiselect
                  [data]="ddlDocumentCategories"
                  [(ngModel)]="selectedDocumentCategory"
                  [settings]="documentCategorySettings"
                  formControlName="ddlDocumentCategory"
                  id="ddlDocumentCategory"
                ></angular2-multiselect>
              </div>
            </div>
            <div class="form-group row" *ngIf="showSiteSelection">
              <label class="col-sm-4 control-label"><label translate="LABELS.SITES"></label> *</label>
              <div class="col-sm-8">
                <app-select-sites
                  [events]="eventsSubject.asObservable()"
                  [hideApplyFilter]="true"
                  (hideDropdown)="hideDropdown($event)"
                  [singleSelection]="false"
                  [filterType]="true"
                  (siteIds)="getSiteIdsInvite($event)"
                  [selectedSiteIds]="editSiteData"
                  [dynamic]="true"
                >
                </app-select-sites>
              </div>
            </div>
            <div class="form-group row">
              <div class="col-sm-4">
                <label class="control-label"><label translate="LABELS.UPLOAD_FILE"></label>*</label>
              </div>
              <div class="col-sm-8" [hidden]="docFileUpdate">
                <span>
                  <div class="uploads" *ngIf="docFile?.length > 0">
                    <div class="upload-item">
                      <div class="upload-item-top">
                        <div>
                          <span class="filename"
                            >{{ docFile }}
                            <div class="doc-file-remove">
                              <span class="x" (click)="removeFile()">x</span>
                            </div>
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </span>
                <label class="upload-button">
                  <input
                    type="file"
                    class="dropify"
                    formControlName="fileup"
                    name="fileup"
                    data-height="100"
                    ngFileSelect
                    (change)="onUploadDocumentFile($event)"
                    [attr.data-allowed-file-extensions]="allowedFileExtensions"
                    data-max-file-size="10M"
                  />
                </label>
                <div
                  *ngIf="
                    uploadDocumentFile.controls.fileup.invalid &&
                    (uploadDocumentFile.controls.fileup?.dirty || uploadDocumentFile.controls.fileup?.touched)
                  "
                  class="alert alert-danger"
                  translate="LABELS.PLEASE_SELECT_VALID_FILE"
                ></div>
              </div>
              <div class="col-sm-8" [hidden]="!docFileUpdate">
                <div class="uploads">
                  <div class="upload-item">
                    <div class="upload-item-top">
                      <div style="float: left">
                        <span class="filename">{{ docFileUpdate }}</span>
                        <div class="doc-file-remove">
                          <span class="x" id="remv" (click)="removeUpdateFile()">x</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="form-group row">
              <div class="col-sm-4">
                <label class="control-label" [translate]="'LABELS.DOCUMENT_NAME'"></label> <i chToolTip="DOCUMENT_NAME_DEFAULT"></i>
              </div>
              <div class="col-sm-8">
                <input
                  type="text"
                  class="form-control"
                  formControlName="fileName"
                  name="fileName"
                  [placeholder]="'LABELS.DOCUMENT_NAME' | translate"
                />
              </div>
            </div>
          </div>
          <div class="float-left">
            <button
              type="submit"
              (click)="fileManipulation()"
              class="btn btn-primary swal-btn-info"
              id="send_broadcast"
              [disabled]="
                preventMultipleClick ||
                (fileUploadBtn === 'BUTTONS.SAVE' || !docFileUpdate || (siteIds.length === 0 && showSiteSelection)
                  ? !(uploadDocumentFile.valid && ((siteIds && siteIds.length > 0) || !showSiteSelection))
                  : '')
              "
              translate
            >
              {{ fileUploadBtn }}
            </button>
            <button type="button" class="btn btn-default" id="cancel-upload" translate="BUTTONS.CANCEL" (click)="cancel()"></button>
          </div>
        </form>
      </div>
    </div>
  </div>
</div>

<modal-document
  *ngIf="isModalOpen"
  class="loading-container"
  [show-modal]="isModalOpen"
  [title]="title"
  [sub-title]="subTitle"
  [modal-body]="modalBody"
  [show-header]="showHeader"
  [show-footer]="showFooter"
  [cancel-label]="cancelLabel"
  (closed)="closeModal()"
  [show-iframe]="showIframe"
>
</modal-document>
