<div class="modal-backdrop fade in" [style.display]="showModal ? 'block' : 'none'"></div>
<div class="modal forward-modal" tabindex="-1" role="dialog" style="display: block"
    [style.display]="showModal ? 'block' : 'none'">
    <div class="modal-dialog" [ngClass]="{'modal-iframe-height':modalIframeBody}">
        <div class="modal-content"
            [ngClass]="{'modal-iframe-height':modalIframeBody,'video-transparent':modalVideoBody}">
            <div class="modal-body" *ngIf="modalIframeBody" [ngClass]="{'modal-iframe-height':modalIframeBody}">
                <div>
                    <button type="button" class="close" data-dismiss="modal" (click)="hide()" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <iframe class="e2e-iframe-trusted-src" id="pdfIframe" scrolling="no" [src]="modalBody" width="100%"
                    height="100%" onload="javascript:this.contentWindow.location.hash=':0.page.20';">
                </iframe>
            </div>
            <div class="modal-footer" *ngIf="modalBody">
                <a type="button" class="btn btn-sm btn-primary" (click)="downloadDocument(modalBody)" download>Download
                    <i class="fa fa-download" aria-hidden="true"></i></a>
            </div>
        </div>
    </div>
</div>