<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>{{heading}}</strong>
            
        </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/documents/categories']">Documents</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/documents/categories']">Document Categories</a></li>
            <li class="breadcrumb-item">{{heading}}</li>
        </ol>
        <div class="row">
            <div class="col-lg-12">                
                <div class="mb-5">
                    <form class="form-horizontal" [formGroup]="frmCategory" (ngSubmit)="saveCategory(f)"  novalidate #f="ngForm">
                        <div class="form-body">
                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Document Category Name * </label>
                                </div>
                                <div class="col-md-6">
                                    <input class="form-control" type="text" placeholder="Document Category Name" name="categoryName" [formControl]="frmCategory.controls['categoryName']" id="categoryName">
                                    <div class="alert alert-danger" *ngIf="!frmCategory.controls.categoryName.valid && (frmCategory.controls.categoryName.dirty || frmCategory.controls.categoryName.touched || f.submitted)">
                                        Document Category Name cannot be empty.
                                    </div>

                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Document Category Code * </label>
                                </div>
                                <div class="col-md-6">
                                    <input class="form-control" type="text" placeholder="Document Category categoryCode" name="categoryCode" [formControl]="frmCategory.controls['categoryCode']" id="categoryCode" disabled>
                                    <div class="alert alert-danger" *ngIf="!editId && !frmCategory.controls.categoryCode.valid && (frmCategory.controls.categoryCode.dirty || frmCategory.controls.categoryCode.touched || f.submitted)">
                                        Document Category Code cannot be empty.
                                    </div>

                                </div>
                            </div>

                            <div class="form-group row" [hidden]= "userData.config.enable_esi_value_in_document_category != 1 ||(userData.config.enable_API_based_integration_for_category_code == '1' && userData.config.enable_esi_value_in_document_category == 1)" >
                            <div class="col-md-9">
                                <div class="exterLabel" >
                                    <label class="control-label">External Document Category Name </label><i chToolTip="externalDocCat" style="margin-left: 3px;"></i>
                                </div>
                                <div class="exterContent">
                                    <input class="form-control" type="text"  placeholder="External Document Category Name" name="catTypeName" [formControl]="frmCategory.controls['catTypeName']" id="catTypeName">
                                </div>
                            </div>
                            </div>

                            <div class="form-group row" [hidden]= "userData.config.enable_esi_value_in_document_category != 1">
                                <div class="col-md-9">
                                <div class="exterLabel" >
                                    <label class="control-label">External Document Category Code </label><i chToolTip="externalDocCatCode" style="margin-left: 3px;"></i>
                                </div>
                                <div class="exterContent">
                                    <input class="form-control" type="text" [readonly] =  "userData.config.enable_API_based_integration_for_category_code == '1'" placeholder="External Document Category Code" name="esiValue" [formControl]="frmCategory.controls['esiValue']" id="esiValue">
                                </div>
                            </div>
                            <div class="col-md-2 lookup" *ngIf = "userData.config.enable_API_based_integration_for_category_code == '1'">
                                    <button  type="button" (click) = "getExtDocData(1)"><i title="Lookup the document types from external systems" class="fa fa-search handButton" ></i></button >
                                    <button type= "button"  (click) = "clearExtDocData()"><i class="fa fa-close handButton" title="Reset the value of external document type"></i></button>
                            </div>
                           </div>
                            

                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Default File Name format <!--<i class="default-filename icmn-info" data-toggle="tooltip" data-placement="right"></i> --></label>
                                </div>
                                <div class="col-md-6">
                                    <input class="form-control" type="text" placeholder="Default File Name format" name="fileNameFormat" [formControl]="frmCategory.controls['fileNameFormat']" id="fileNameFormat">
                                </div>
                            </div>

                            <div [hidden] = "statusShow" class="form-group row">
                                <label class="col-md-3 control-label">Status </label>
                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" name="status" id="statusActive" [ngClass]="{'active': frmCategory.controls['status'].value == 0}" (click)="toggleStatus('Active',0)">
                                        Active
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" name="status" id="statusInactive" [ngClass]="{'active': frmCategory.controls['status'].value == 2}" (click)="toggleStatus('Inactive',2)">
                                        Inactive
                                    </button>
                                </div>
                            </div>

                            <div class="form-group row" [hidden]="multisite">
                                <div class="col-md-3">
                                    <label class="control-label">Default Incoming Filing Center<!--<i class="default-filing-center icmn-info" data-toggle="tooltip" data-placement="right"></i> --></label>
                                </div>
                                <div class="col-md-6">
                                    <input type="hidden" id="sfilingCenterss" value="" style="width:300px;" />
                                    <i *ngIf="showClose" title="Clear" (click)="clearFilingCenter()" style="position:absolute;top:14px;right:37px;font-size: 9px;cursor: pointer;" class="icmn-cross"></i>
                                    <select style="display:none;" class="form-control" [(ngModel)]="folderName" [formControl]="frmCategory.controls['filingcenter']" name="filingcenter" id="filingcenter">                                           
                                            <option *ngFor="let g of folderLists;" [ngValue]="g">
                                                {{g.folderName}}
                                            </option>
                                    </select>
                                </div>
                            </div>                                                      
                           
                            <div class="form-group row">
                                <div class="col-md-6">
                                    <button type="submit" class="addmessagetag btn btn-primary">
                                      <span *ngIf="heading == 'Add Category'">Add</span>
                                      <span *ngIf="heading == 'Edit Category'">Update</span>
                                    </button>
                                    <a [routerLink]="['/documents/categories']" class="btn btn-default">Cancel</a>
                                    <button type="reset" style="display:none;" id="hideReset" class="btn btn-success">Reset</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade bulk-edit" id="bulk-edit" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="exampleModalLabel">Document Type</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="closeDynamicModal()" style="margin-right: 7px;"
            >
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                    <div *ngIf="showLoader" class="loading-container">
                            <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                            <div class="loading-text">Loading Data...</div>
                        </div>
                <form>
                    <div *ngIf="!showLoader && noteCount != 0" class="card-block">
                        <div class="cat__core__card-sidebar">
                        <label><strong>Document Category</strong></label>
                            <div class="doc-cat-tab" >
                                <select class="form-control doc-cat-class"  (change)="onSelectCategory($event)"  id="doc-category" >
                                    <option value="">Select</option>
                                    <option *ngFor="let noteType of noteTypes;let i = index" value="{{noteType.id}}">
                                        {{noteType.value}} </option>
                                </select>
                                <button type= "button" (click) = "getExtDocData(0)"><i class="fa fa-refresh get-ext-data-class handButton" title="Refresh the data from external system"></i></button>

                            </div>
                            <div class="">
                                    <label><strong>Document Type</strong></label>
                                    <select [disabled] = "!enableDocType"  class="form-control doc-type-class" (change)="onSelectType($event)" id="doc-type" >
                                        <option value="">Select</option>
                                        <option *ngFor="let documentTypeSet of documentTypeSet;let i = index" value="{{documentTypeSet.id}}">
                                            {{documentTypeSet.name}}</option>
                                    </select>
                            </div>
                        </div>
                    </div>
                </form>
                <div style="text-align:center;width:100%;" *ngIf="!showLoader && noteTypesCount == 0">
                    <p>We are experiencing issues with getting response from a third-party service. Please try after some time or check the end point url configured.</p>
                </div>
                <div style="text-align:center;width:100%;" *ngIf="!showLoader && noteTypesCount == 2">
                <p>Authentication failed, please check the authentication token configured.</p>
                </div>
                <div style="text-align:center;width:100%;" *ngIf="!showLoader && noteTypesCount == 1">
                <p>Invalid or missing required parameters in the partner token to connect the third party application</p>
                </div>
                <div style="text-align:center;width:100%;" *ngIf="!showLoader && noteTypesCount == 3">
                <p>Invalid or missing required parameters to connect the third party application.</p>
                </div>
                <div style="text-align:center;width:100%;" *ngIf="!showLoader && noteTypesCount == 4">
                <p>Invalid or missing required client id to connect the third party application.</p>
                </div>
            </div>
            <div class="modal-footer">
                <div class="footer-padding">
                    <button type="button" [disabled] = "!enableSubData" class="btn btn-primary" (click)="setSelectedData()">Ok</button>
                    <button type="button" class="btn btn-primary" (click)="closeDynamicModal()">Close</button>
                </div>
                  </div>
        </div>
    </div>
</div>
   
</section>