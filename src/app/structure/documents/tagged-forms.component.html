<!-- START: tables/datatables -->
<app-sign-pad (newcomp)="sendSignature($event);"></app-sign-pad>
<section class="card">
    <div class="tagmsgs"  [hidden]="!hideSiteSelection || !viewPage">
        <div class="col-sm-10">
            <span style="width: 76%" class="tagfilter">
                <span class="site-label user-sites">{{ labelSiteFilter | translate }} </span>
                <app-select-sites  [events]="eventsSubject.asObservable()"  [filterType]=true (siteIds)="getSiteIds($event)" (hideDropdown)="hideDropdown($event)">
                </app-select-sites>
            </span>
    </div>
    </div>
  <div class="card-header">
      <span class="cat__core__title">
          <strong [hidden]="hideContent == true">Tagged Messages</strong>
          <a class="pull-right btn btn-sm btn-primary" [hidden]="isDetail" (click)="getPdfTaggedForm()">Download <i class="ml-1"></i></a>          
          <a class="pull-right btn btn-sm btn-primary" *ngIf="!isApproveShow" [hidden]="!isDetail" (click)="getPdfReport()">Download<i class="ml-1"></i></a>          
      </span>
  </div>
  <div class="card-block">
      <ol class="breadcrumb" [hidden]="hideContent == true">
          <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
          <li class="breadcrumb-item"><a >Message Settings</a></li>
            <li class="breadcrumb-item">Tagged Messages</li>
      </ol>


        <div class="" [hidden]="!isDetail">
            <div class="col-sm-12">

                <div class="data-cnt">
                    <div class="row form-group">
                        <label class="col-sm-2 control-label">Tags :</label>
                        <div class="col-sm-4 tags-sec">
                            <p><span *ngFor="let tag of tagsList"><img class="tag-icon" src="../../../assets/modules/dummy-assets/common/img/tag-icon.png"/>{{tag}}</span>
                                <!-- <span>Dolor sit amet</span> --></p>
                        </div>
                        <!-- <label *ngIf="isApproveShow && isFilingCenter" for="sel1" class="col-md-2">Outgoing Filing Center:</label>
                        <div class="col-sm-4" [hidden]="!(isApproveShow && isFilingCenter)">
                            <input [hidden]="!isApproveShow" type="hidden" id="sfilingCenterss" value="" />
                        </div>
                        <i [hidden]="!isApproveShow" title="Clear" *ngIf="showClose" (click)="clearFilingCenter()" class="icmn-cross close-file-center"></i>                        
                        <select style="display:none;" *ngIf="isApproveShow && isFilingCenter" class="form-control col-md-4" [(ngModel)]="folderName" name="filingcenter" id="filingcenter">                                           
                            <option value="Select Filing Center">Select Filing Center</option>
                        <option *ngFor="let g of folderLists;" [ngValue]="g">
                            {{g.folderName}}
                        </option>
                        </select> -->
                    </div>
                    <div class="row form-group tags-sec">
                        <label class="col-sm-2 control-label">Message(s) Tagged :</label>
                        <label class="col-sm-4 control-label tagged-message" *ngIf="!isArray(description)" [innerHTML]="description "></label>
                        <div class="col-sm-4 control-label tagged-message" *ngIf="isArray(description)">
                            <span *ngFor="let text of description;" class="tag-message box arrow-left-bottom chat-message-container" compile="{{text | autolinker}}"></span>
                        </div>
                        <!-- <label *ngIf="isApproveShow && isFilingCenter" class="col-md-2" for="email">File Name :</label>
                        <input *ngIf="isApproveShow && isFilingCenter" class="form-control col-md-4 filingcenter-file-format" type="text" placeholder="File Saving Format" [(ngModel)]="fileNameFormat" id="tagName">
                    --> </div>
                    <div class="row form-group" [hidden]="activeTaggedForm && activeTaggedForm.patient.displayName=='null null'">
                        <label class="col-sm-2 control-label">Patient Name :</label>
                        <div class="col-sm-4">
                            <p>{{patientName}}</p>
                        </div>
                    </div>
                  
                    <div class="row form-group" *ngIf="_structureService.isMultiAdmissionsEnabled && admissionName">
                        <label class="col-sm-2 control-label">{{ 'ADMISSION.LABELS.ADMISSION' | translate }} :</label>
                        <div class="col-sm-4">
                            <p>{{admissionName}}</p>
                        </div>
                    </div>
                    <div class="row form-group" [hidden]="activeTaggedForm && activeTaggedForm.patient.displayName=='null null'">
                        <label class="col-sm-2 control-label">DOB (mm/dd/yyyy) :</label>
                        <div class="col-sm-4">
                            <p>{{dateOfBirth}}</p>
                        </div>
                        <!-- <div class="col-sm-6">
                            <p></p>
                        </div> -->
                    </div>
                    <div class="row form-group">
                        <label class="col-sm-2 control-label">Created on :</label>
                        <div class="col-sm-6">
                            <p>{{createdOn}}</p>
                        </div>
                    </div>
                    <div class="row form-group tags-sec">
                        <label class="col-sm-2 control-label">Created by :</label>
                        <div class="col-sm-6">
                            <p>{{createdBy}}</p>                            
                        </div>
                    </div>

                    <div class="row form-group tags-sec" *ngIf="imgUrl">
                        <label class="col-sm-2 control-label">Creator Signature :</label>
                        <div class="col-sm-6">                            
                            <img id="imageid" class="resize-Approve-Sign" [src]="imgUrl">
                        </div>
                    </div>

                    <div class="row form-group tags-sec" *ngIf="!isApproveShow">
                        <label class="col-sm-2 control-label">Approved by :</label>
                        <div class="col-sm-6">
                            <p>{{approvedBy}}</p>                           
                        </div>
                    </div>

                    <div class="row form-group tags-sec" *ngIf="approveSignUrl">                       
                        <label class="col-sm-2 control-label">Approver Signature :</label>
                        <div class="col-sm-6">                            
                            <img id="approveSign" class="resize-Approve-Sign" [src]="approveSignUrl">
                        </div>
                    </div>

                    <a class="pull-right btn btn-sm" [ngClass]="{'btn-default':isApproveShow,'btn-primary':!isApproveShow}" (click)="detailsView()" [hidden]="hideContent == true">Close <i class="ml-1"></i></a>
                    <button *ngIf="isApproveShow && approveFile" [disabled]="noTags" class="pull-right btn btn-sm btn-primary" style="margin-right:2%;" (click)="openSignpad('ApproveFile')">Approve Message <i class="ml-1"></i></button>
                    <!-- <button *ngIf="isApproveShow" class="pull-right btn btn-sm btn-primary" style="margin-right:2%;" (click)="openSignpad('Approve')">Approve Message <i class="ml-1"></i></button> -->

                </div>

            </div>


        </div>



        <div class="row" [hidden]="isDetail">
            <div class="col-lg-12">
                <div class="mb-5">
                    <div id="example1_wrapper" class="dataTables_wrapper container-fluid dt-bootstrap4 no-footer">
                        <div class="row">
                            <div class="col-sm-12 col-md-6">
                            </div>
                        </div>
                    </div>
                    <div class="wait-loading" *ngIf="dataLoadingMsg">
                        <img src="./assets/img/loader/loading.gif" />
                    </div>
                     <div class="exportData" *ngIf="!dataLoadingMsg" [hidden]="userData.enable_export_data != '1'">
                        <button daterangepicker [options]="_SharedService.exportOptions" (selected)="selectedDate($event, daterange)" class="btn btn-sm btn-default">Export</button>
                    </div>
                    <table class="table table-hover" id="examplesigned" width="100%" [hidden]="hideContent == true"></table>
                </div>
            </div>

        </div>
    </div>
    <div>
        <canvas id="signCanvas" width="100" height="150"></canvas>
    </div>
</section>
<!-- END: tables/datatables -->
