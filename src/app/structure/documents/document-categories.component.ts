import { Component, OnInit,Renderer,ElementRef } from '@angular/core';
import { Router } from '@angular/router';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';

declare var $: any;
declare var swal:any;

@Component({
  selector: 'app-document-categories',
  templateUrl: './document-categories.component.html'
})
export class DocumentCategoriesComponent implements OnInit {

  dTable;
  documentCategories;
  activeCategory;
  dataLoadingMsg = false;

  constructor(
    private renderer: Renderer,
    private structureService: StructureService,
    private toolTipService: ToolTipService,
    private elementRef: ElementRef,
    private router: Router
  ) {
    this.renderer.listen(this.elementRef.nativeElement, 'click', (event) => {
      if (event.target.id === 'editDocumentCategory') {
       this.router.navigate(['documents/categories/' + this.activeCategory.id]);
      }
      if (event.target.id === 'deleteDocumentCategory') {
          swal({
            title: 'Are you sure?',
            text: 'You are going to delete the category',
            type: 'warning',
            showCancelButton: true,
            cancelButtonClass: 'btn-default',
            confirmButtonClass: 'btn-warning',
            confirmButtonText: 'Ok',
            closeOnConfirm: true
        }, () => {

        this.deleteDocumentCategory();
        });
      }
    });

   }

  ngOnInit() {
    this.getAllDocumentCategories();
  }

  getAllDocumentCategories() {
    this.dataLoadingMsg = true;
    this.structureService.getDocumentTagtype().then((data: any) => {
      this.dataLoadingMsg = false;
      if (data.getSessionTenant && data.getSessionTenant.documentTagTypes) {
        this.documentCategories = data.getSessionTenant.documentTagTypes;
        this.populateDataTable();
      }
    });
  }

  deleteDocumentCategory() {
    const userDetails = JSON.parse(this.structureService.userDetails);
    this.structureService.createDocumentCategory('', this.activeCategory.id, true).then((res: any) => {
      if (res && res.errors && res.errors.length > 0 && res.errors[0].message) {
        this.structureService.notifyMessage({
          message: this.toolTipService.getTranslateData('MESSAGES.DOCUMENT_CATEGORY_REMOVE_FAILED'),
          type: 'error'
        });
      } else {
        this.structureService.notifyMessage({
          message: this.toolTipService.getTranslateData('MESSAGES.DOCUMENT_CATEGORY_REMOVE_SUCCESS'),
          type: 'success'
        });
        const activityData = {
          activityName: 'delete document category',
          activityType: 'manage document category',
          activityDescription: `${userDetails.displayName} deleted document category - ${this.activeCategory.typeCode}`
        };
        this.structureService.trackActivity(activityData);
      }
      this.getAllDocumentCategories();
    });
  }

  populateDataTable() {
    if (this.dTable) {
      this.dTable.destroy();
    }
    var isTrue = false;    
    if(this.documentCategories.length > 99){
      isTrue = true;
    }
  this.dTable = $('#doc-cat-list').DataTable({
    autoWidth: false,
    /* "order": [[ 4, "desc" ]], */
    responsive: true,
    //bDeferRender:true,
    //processing: true,
    // oLanguage: {
    // sLoadingRecords: "Please wait - loading..."
    // },
    retrieve: true,
    //pagination: true,
    serching: true,  
    paging: isTrue,
    bInfo: isTrue,
    lengthMenu: [[100, 250, 500, 1000, -1], [100, 250, 500, 1000, 'All']],            
    data: this.documentCategories,
    fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
      $(nRow).on('click', () => {
        console.log(aData);
        this.activeCategory = aData;
      });
    },
    columns: [
      {title: "#"},
      {title: "Category Name", data: 'typeName'},
      {title: "Category Code", data: 'typeCode'},
      {title: "Default File Name Format", data: 'tagMeta'},
      {title: "Status", data: 'status'},
      {title: "Actions", data: ''},
    ],
    columnDefs: [
    {
      data: null,
      orderable: false,
      width: "1%",
      targets: 0
     },
     { 
      data: null,
      targets: 1,
      width: "20%"     
    },
    { 
      data: null,
      targets: 2,
      width: "20%"     
    },
    { 
      data: null,
      targets: 3,
      width: "20%",
      render: (data, type, row) => { 
        if(data) {          
          let metaData;

          try {
            metaData = JSON.parse(data);
          } catch(e) {
              console.log(e); // error in the above string (in this case, yes)!
          }
          if(metaData) {
            return metaData.fileNameFormat;
          } else {
            return "";
          }

          
        }
       }
    },
    { 
      data: null,
      render: function (data, type, row) {
        let statusClass;
        let statusName;
        switch (data) {
          case 0:
            statusClass = 'success';
            statusName = 'Active';
            break;
          case 2:
            statusClass = 'danger';
            statusName = 'Inactive';
            break;         
          default:
            break;
        }
        
        return `<span class="badge badge-${statusClass} mr-2 mb-2">${statusName}</span>`;
      },
      targets: 4,
      width: "15%",
      visible: false   
    },
    { 
      data: null,
      targets: 5,
      width: "20%",
      render: (data, type, row) => {        
        let actions = '';
        actions += `
        <a id="editDocumentCategory" href="javascript: void(0);" class="cat__core__link--underlined mr-3">
          <i class="icmn-pencil"></i> Edit</a>
        <a id="deleteDocumentCategory" href="javascript: void(0);" class="cat__core__link--underlined mr-3">
          <i id="deleteDocumentCategory" class="icmn-cross"></i> Delete</a>
        `;
       return actions;
      }
    }]
  });

  this.dTable.on( 'order.dt search.dt', () => {
    if(this.dTable.column(0, {search:'applied', order:'applied'}).nodes() && this.dTable.column(0, {search:'applied', order:'applied'}).nodes().length) {
      this.dTable.column(0, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
          cell.innerHTML = i+1;
      });
    }
  }).draw();
  }

}
