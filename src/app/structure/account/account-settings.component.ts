import { Component, OnInit } from '@angular/core';
import { StructureService } from '../../structure/structure.service';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { SignService } from '../signatures/sign.service';
let moment = require('moment/moment');
import * as io from "socket.io-client";
import { configTimeZone } from "../../../environments/environment";
import { SharedService } from '../../structure/shared/sharedServices';
import { EnrollService } from '../user-registration/enroll.service';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;
declare var swal: any;
import { Subject } from 'rxjs';

@Component({
  selector: 'app-account-settings',
  templateUrl: './account-settings.component.html'
})
export class AccountSettingsComponent implements OnInit {

  tenantTimeZone;
  tenantTimeZoneData:any = {};
  timezone;
  timezones:Array<{offset: string, city: string, name: string, isDst: string, current_offset: string}>;
  hiddenField: true;
  selectedOptions = [];
  selectedOptionsnotification=[];
   selectedOptionstagged = [];
  taggedUsers = [];
  userList;
  defaultUserList = [];
  defaultUsers = [];
  defaultUsersNotificationEnrollment=[];
  defaultUserssmsNotificationEnrollment=[];
  defaultUsersemailNotificationEnrollment=[];
  selectedUserListRecvEsc = [];
  defaultUserreferal=[];
  member;
  userInfo;
  userData;
  accountId;
  selectedUsers;
  selectedDefaultUsers;
  errorMessageAccountName = 'Account Name cannot be empty';
  errorMessageRegId = 'Registration Id cannot be empty';
  errorMessageAccountKey = 'Account Key cannot be empty';
  pageType = 'add';
  userRole;
  isDisabled = false;
  isFilingCenter=false;
  documentsfromFilingCenterFolderOut;
  filingCentersOut = [];  
  folderName; 
  fromFilingCenter;
  tenantId;
  selectedUserList;
  selectedUserListclinicalLiaison;
  staffsAwareEnrollByEmail;
  staffsAwareEnrollBySms;
  settings;
  hidePreferences = [];
  staffRoles: any = [];
  partnerRoles: any = [];
  partnerCategories: any = [];
  account = {
    key: '',
    name: '',
    tenantConfigValue: {
      registrationId: ''
    },
    configurationSettings: [],
    masterEnabled:false,
    isMaster:false,
    enableApplessWorkflow: 0,
    tokenExpiryTime: '2880',
    verificationRememberTime: '60',
    verificationTokenExpiryTime: '10'
  };
  previlages = {
    superAdmin: false,

  };
  conversationTypeStatus = 0;
  maskedMessagestatus = 0;
  preferences;
  FilingCenterChatLog;
  fileSavingFormatChatLogFilingCenterValue;
  messageForwardBehaviorObject = [
    {
      value: "Keep_initial_user_and_forwarded_user_in_the_same_session",
      text: "Keep forwarding user in chat session"
    },
    {
      value: "Remove_initial_user_but_add_forwarded_user_to_session",
      text: "Remove forwarding user from chat session"
    },
    {
      value: "Let_user_decide_per_message",
      text: "Allow user to select preference"
    }


  ];
  allReminderTypes = [{ id: "push", name: "Push" }/* , { id: "email", name: "Email" } , { id: "sms", name: "SMS" }*/];
  enrollmentReminderTypes = [{ id: "email", name: "Email" },{ id: "sms", name: "SMS" }];
  allBranchDays = [
    { id: '1', name: 'Monday' },
    { id: '2', name: 'Tuesday' },
    { id: '3', name: 'Wednesday' },
    { id: '4', name: 'Thursday' },
    { id: '5', name: 'Friday' },
    { id: '6', name: 'Saturday' },
    { id: '0', name: 'Sunday' }
  ];

  accountPreferences: FormGroup;
  teanntRoles;
  teanntRolesMasked;
  dataLoadingMsg = true;
  crossTenantChangeSubscriber:any;
  nursingAgencies = [];
  nursingAgenciesforManageMessageGroupsSelected = [];
  nursingAgenciesforManageEducationMaterialsSelected = [];
  partnerTags = [];
  staffTags = [];
  optionTab:any;
  eventsSubject: Subject<void> = new Subject<void>();
  siteId:any;
  siteIdss:any;
  showSiteSelection: any;
  /**Multisite Enabled **/
  multiSiteEnable: boolean;
  constructor(
    private _structureService: StructureService,
    private _enrollService: EnrollService,
    private _sharedService: SharedService,
    private route: ActivatedRoute,
    private router: Router,
    private _formBuild: FormBuilder,
    private _signService: SignService
  ) {
    this.optionTab = (localStorage.getItem('calender-Schedule') === "true") ? 'message' : 'general';
    this.timezone = configTimeZone();
    this.timezones = [];
    route.params.subscribe(val => {

      route.params.subscribe(val => {
        this.accountId = this.route.snapshot.params['accountId'];
        if (typeof (this.accountId) !== 'undefined') {
          this.pageType = 'edit';
         // this.getAccount(this.accountId);
        }
      });



      this.userInfo = this._structureService.loginUserDetails;
      this.accountId =  (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') :  this.userInfo.tenantId;
      this.accountId = typeof (this.accountId) === 'undefined' ? this._structureService.getCookie('tenantId') : this.accountId;
      this.userRole = typeof (this.userInfo.roleName) === 'undefined' ? this._structureService.getCookie('userRole') : this.userInfo.roleName;
      if (this.userRole === 'Super Admin') {
        this.previlages.superAdmin = true;
      }
    })
    this.crossTenantChangeSubscriber = this._sharedService.crossTenantChange.subscribe((onInboxData) => {
      if(this.router.url.indexOf('/account/account-settings') > -1) {
        this.ngOnInit();
      }
    });
  }

  ngOnDestroy() {
    //$('#intro-btn').attr('hidden',true);
    this._structureService.unSubsc();
    if (this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
    if (localStorage.getItem("calender-Schedule")) {
      localStorage.removeItem("calender-Schedule");
    }
  }
  ngOnInit() {

    var config = this._structureService.userDataConfig;
    var userDetails = this._structureService.userDetails;
    var configData = JSON.parse(config);
    this.userData = JSON.parse(userDetails);
    
    this.multiSiteEnable = this.userData.config.enable_multisite == 1 ? true : false;
    this._structureService.introJsBtnHidden = false;

    this._structureService.getPartnerReferrals(0).then((res) => {
      this.defaultUserreferal = res['getSessionTenant']['formPartnerRefferalTags'];
      console.log(this.defaultUserreferal);
    });

    if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') != 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
      this.tenantId = this._structureService.getCookie('crossTenantId');
    } else {
      this.tenantId = this._structureService.getCookie('tenantId');
    }

    $('.select2').select2({
      placeholder: function () {
        $(this).data('placeholder');
      }
    });
    $(document).ready(() => {
    $('#defaultUser').on('change', (e) => {
      this.selectedOptions = [];
      this.selectedOptions = $(e.target).val();
    });

    $('#defaultUser').on('change', (e) => {
      this.selectedOptions = [];
      this.selectedOptions = $(e.target).val();
    });

    $('#patientReminderTypes').on('change', (e) => {
      console.log('#######################');
      console.log($(e.target).val());
      var patientReminderTypes = [];
      var xy = $(e.target).val();
      if (xy) {
        xy.forEach(element => {
          var types = {
            id: ""
          };
          var id = element.substr(element.indexOf(":") + 1);
          id = id.replace(/'/g, "");
          types.id = id.replace(/\s/g, '');
          patientReminderTypes.push(types);
        });

        this.accountPreferences.patchValue({
          patientReminderTypesValidate: patientReminderTypes
        });
      }
    });

    $('#branchDays').on('change', (e) => {
      console.log('#######################');
      console.log($(e.target).val());
      var branchDays = [];
      var xy = $(e.target).val();
      if (xy) {
        xy.forEach(element => {
          var types = {
            id: ""
          };
          var id = element.substr(element.indexOf(":") + 1);
          id = id.replace(/'/g, "");
          types.id = id.replace(/\s/g, '');
          branchDays.push(types.id);
        });

        this.accountPreferences.patchValue({
          branchDays: branchDays
        });
      }
    });

    $('#enrollmentReminderTypes').on('change', (e) => {
      console.log('#######################');
      console.log($(e.target).val());
      var enrollmentReminderTypes = [];
      var xy = $(e.target).val();
      if (xy) {
        xy.forEach(element => {
          var types = {
            id: ""
          };
          var id = element.substr(element.indexOf(":") + 1);
          id = id.replace(/'/g, "");
          types.id = id.replace(/\s/g, '');
          enrollmentReminderTypes.push(types.id);
        });

        this.accountPreferences.patchValue({
          enrollmentReminderTypes: enrollmentReminderTypes
        });
      }
    });

    $('#notificationOnErollment').on('change', (e) => {
      this.selectedOptionsnotification = [];
      this.selectedOptionsnotification = $(e.target).val();
      console.log(this.selectedOptionsnotification);
    });

    $('#defaultStaffRole').on('change', (e) => {
      console.log($(e.target).val());
    });

    $('#defaultPartnerRole').on('change', (e) => {
      console.log($(e.target).val());
    });
   
    // get User Categories based on Tenant
    this._structureService.getTenantUserCategories(this._structureService.getCookie("crossTenantId"), 'invite').then((data: any) => {      
      this.partnerCategories = data;
    });
  });

    // Id of patient group is 3

   const tagTypes = ["2"]; // Message Tag =1, User Tag =2 , Document Tag =3

   const staffTagGetData = '?group=2&enroll=1';
   this._enrollService.getTagsByGroup(staffTagGetData, tagTypes).then((data: any) => {
     this.staffTags = data;
   });

   const partnerTagGetData =  '?group=20&enroll=1';
   this._enrollService.getTagsByGroup(partnerTagGetData, tagTypes).then((data: any) => {
     this.partnerTags = data;
   });
   $(document).ready(() => {
    $('#tenantTimeZone').on('change', (e) => {
      this.tenantTimeZone = $(e.target).val();
      this.tenantTimeZoneData = this.timezones.find((zone, i)=>{return zone.offset == this.tenantTimeZone});

      this.accountPreferences.patchValue({
        tenantTimeZone: this.tenantTimeZone
      });

      let tenantTimeZone = this.tenantTimeZoneData.offset.split(',')[0];
      tenantTimeZone = tenantTimeZone.split(',')[0];
      tenantTimeZone = (((tenantTimeZone) * -1) / 60) * -1;

      let timeToPassDay = [];
      let timeDifference;

      if (tenantTimeZone > this.timezone) {
        timeDifference = Math.abs(this.timezone - tenantTimeZone);
      } else {
        timeDifference = -1 * Math.abs(this.timezone - tenantTimeZone);
      }

      if (((timeDifference) + '').split('.').length == 2) {
        timeToPassDay = ((timeDifference) + '').split('.');
      } else {
        timeToPassDay = ((timeDifference) + '').split('.');
        timeToPassDay.push("0");
      }

      if (timeToPassDay[1] == '5') {
        if (timeDifference < 0) {
          timeToPassDay[1] = '-30';
        } else {
          timeToPassDay[1] = '30';
        }
      } else {
        timeToPassDay[1] = '0';
      }

      if (timeToPassDay[0] == '-0') {
        timeToPassDay[0] = '0';
      }

      timeToPassDay = timeToPassDay.map(function (time) {
        return parseInt(time, 10);
      });

      if (this.preferences.messageDestinationConfig.startTime && this.preferences.messageDestinationConfig.startTime.indexOf(":") !== -1) {
        let startTime = moment(moment().format('YYYY-MM-DD') + ' ' + this.preferences.messageDestinationConfig.startTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('hh:mm A');

        if (startTime.indexOf(":") !== -1) {
          var homeInfusionStartTime = startTime.split(' ');
          startTime = homeInfusionStartTime[0];

          if (homeInfusionStartTime[1] == 'AM') {
            this.accountPreferences.patchValue({
              homeInfusionStartTime: 12,
              homeinfusionStart: startTime
            });
          } else {
            this.accountPreferences.patchValue({
              homeInfusionStartTime: 24,
              homeinfusionStart: startTime
            });
          }
        }
      }

      if (this.preferences.messageDestinationConfig.endTime && this.preferences.messageDestinationConfig.endTime.indexOf(":") !== -1) {
        let endTime = moment(moment().format('YYYY-MM-DD') + ' ' + this.preferences.messageDestinationConfig.endTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('hh:mm A');

        if (endTime.indexOf(":") !== -1) {
          var homeInfusionEndTime = endTime.split(' ');
          endTime = homeInfusionEndTime[0];

          if (homeInfusionEndTime[1] == 'AM') {
            this.accountPreferences.patchValue({
              homeinfusionEndTime: 12,
              homeinfusionEnd: endTime
            });
          } else {
            this.accountPreferences.patchValue({
              homeinfusionEndTime: 24,
              homeinfusionEnd: endTime
            });
          }
        }
      }

      if (this.preferences.messageDestinationConfig.branchStartTime && this.preferences.messageDestinationConfig.branchStartTime.indexOf(":") !== -1) {
        let startTime = moment(moment().format('YYYY-MM-DD') + ' ' + this.preferences.messageDestinationConfig.branchStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('hh:mm A');
        if (startTime.indexOf(":") !== -1) {
          var branchStartTime = startTime.split(' ');
          startTime = branchStartTime[0];
          if (branchStartTime[1] == 'AM') {
            this.accountPreferences.patchValue({
              branchStartTime: 12,
              branchStart: startTime
            });
          } else {
            this.accountPreferences.patchValue({
              branchStartTime: 24,
              branchStart: startTime
            });
          }
        }
      }

      if (this.preferences.messageDestinationConfig.branchEndTime && this.preferences.messageDestinationConfig.branchEndTime.indexOf(":") !== -1) {
        let endTime = moment(moment().format('YYYY-MM-DD') + ' ' + this.preferences.messageDestinationConfig.branchEndTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('hh:mm A');
        if (endTime.indexOf(":") !== -1) {
          var branchEndTime = endTime.split(' ');
          endTime = branchEndTime[0];
          if (branchEndTime[1] == 'AM') {
            this.accountPreferences.patchValue({
              branchEndTime: 12,
              branchEnd: endTime
            });
          } else {
            this.accountPreferences.patchValue({
              branchEndTime: 24,
              branchEnd: endTime
            });
          }
        }
      }
    });
    /*$(document).on("focus", "#homeinfusionStart", function () {
      if(!$('#homeinfusionStart').val()){
        $(this).mask("00:00");
      }
    });
    $(document).on("focus", "#homeinfusionEnd", function () {
      if(!$('#homeinfusionEnd').val()){
        $(this).mask("00:00");
      }
    });*/
    $(document).on("keyup", "#homeinfusionStart", function () {
      if (!$('#homeinfusionStart').val()) {
        $(this).mask("00:00");
      }
    });

    $(document).on("keyup", "#homeinfusionEnd", function () {
      if (!$('#homeinfusionEnd').val()) {
        $(this).mask("00:00");
      }
    });

    $(document).on("keyup", "#branchStart", function () {
      if (!$('#branchStart').val()) {
        $(this).mask("00:00");
      }
    });

    $(document).on("keyup", "#branchEnd", function () {
      if (!$('#branchEnd').val()) {
        $(this).mask("00:00");
      }
    });

    $('body').on("keypress", "input[type=text]", function (e) {
      var code = (e.keyCode ? e.keyCode : e.which);
      if (code == 13) {
        e.preventDefault();
      }
    });

    $('#taggedUser').on('change', (e) => {
      this.selectedOptionstagged = [];
      this.selectedOptionstagged = $(e.target).val()
    });
  });

    this._signService.getAllTenantRoles().then((data) => {
      this.teanntRolesMasked = data;
      this.teanntRolesMasked = this.teanntRolesMasked.filter((data)=>{
        if( data.citus_role_id!='3'){
          return true;
        }
      }) 
      this.teanntRoles = data;
      this.teanntRoles = this.teanntRoles.filter((data1) => {
        if (data1.citus_role_id != '3') {
          return true;
        }
      });

      this.staffRoles = data;

      this.staffRoles = this.staffRoles.filter((data1) => {
        if (data1.citus_role_id != '3' && data1.citus_role_id != '20') {
          return true;
        }
      });

      this.partnerRoles = data;

      this.partnerRoles = this.partnerRoles.filter((data1) => {
        if (data1.citus_role_id == '20') {
          return true;
        }
      });
    }).catch((ex) => {
      //this.loginFailed = true;
    });

    this.userData = this._structureService.getUserdata();

    if(this.userData.config.enable_nursing_agencies_visibility_restrictions && this.userData.config.enable_nursing_agencies_visibility_restrictions == 1) {
      this._structureService.getNursingAgencyTagsByGroup('?tenantId='+this._structureService.getCookie('crossTenantId')).then((data:any[]) => {
        this.nursingAgencies = data;
      });
    }

    if (this.userData.config.enable_filing_center == "1") {
      this.isFilingCenter = true;
    } else {
      this.isFilingCenter = false;
    }

    if (this.isFilingCenter) {
      var typess = "OUTGOING";
      this._structureService.getTenantFilingCenterFolders(typess).then(

        (data) => {
          this.filingCentersOut = [];
          console.log("data", data);
          this.documentsfromFilingCenterFolderOut = data['getTenantFilingCenterFolders'];

          for (let i = 0; i < this.documentsfromFilingCenterFolderOut.length; i++) {
            var fname = this.documentsfromFilingCenterFolderOut[i].folderName;
            var ftype = this.documentsfromFilingCenterFolderOut[i].type;
            var item = {
              id: fname,
              text: fname
            }
            this.filingCentersOut.push(item);
          }
        });
    }

    this.accountPreferences = this._formBuild.group({
      officeEmail: [''],
      helplinePhone: [''],
      contactEmail: [''],
      officePhone: [''],
      address: [''],
      sessionTimeout: ['', Validators.required],
      sessionTimeoutWarning: ['', Validators.required],
      fileSavingFormatChatLogFilingCenter: ['{initiator}-{startTime}-{endTime}-{downloadOn}'],
      defaultOutgoingFilingCenterChatLog: [''],
      partnerPatientReferralIntroduction: [''],
      chatAutoTranslate: [''],
      chatInfusionSupport: [''],
      chatEnableWhenIsMyNurseComing: [''],
      chatEnableWhereIsMyDelivery: [''],
      gaTracking: [''],
      watchtowerTracking: [''],
      showProtypes: [''],
      showInfusionSupport: [''],
      messageTagging: [''],
      userTagging: [''],
      documentTagging: [''],
      allowPatientDiscussionGroup: [''],
      allowPatientSelfInventory: [''],
      patientsToInitiateChatDuringDay: [''],
      messageForwardBehavior: [''],
      taggedMessageApprover: [''],
      showChatHistoryToNewParticipant: [''],
      showPatientStatusDashboard: [''],
      enableDisplayName: [''],
      enableMultiThreadPdg: [''],
      patientSignupWithAdminApproval: [''],
      clinicianSignupWithAdminApproval: [''],
      rolesChatWithUsNow: [''],
      rolesWhereIsMyDelivery: [''],
      rolesWhenNurseArrive: [''],
      rolesIhaveQuestion: [''],
      homeinfusionStart: ['', Validators.required],
      homeinfusionEnd: ['', Validators.required],
      homeInfusionStartTime: ['12'],
      homeinfusionEndTime: ['24'],
      branchStart: ['', Validators.required],
      branchEnd: ['', Validators.required],
      branchStartTime: ['12'],
      branchEndTime: ['24'],
      rolesDefaultNurses: [''],
      conversationType: [''],
      chatRole1DisplayName: [''],
      chatRole2DisplayName: [''],
      chatRole1MessageTemplate: [''],
      chatRole2MessageTemplate: [''],
      initiationMessage: [''],
      chatRole1: [''],
      chatRole2: [''],
      partnerReferralFormTag: [''],
      enableFilingCenter: [''],
      allowPrefillingPartialEnrollment: [''],
      caregiverSeesEnrollmentInvitationData: [''],
      maskedDiscussionGroupReceiptRole: [''],
      enableMaskedDiscussionGroup: [''],
      patientReminderTime: ['', Validators.required],
      enrollmentReminderTypes: [''],
      tenantTimeZone: ['', Validators.required],
      branchDays: [''],
      patientReminderTypes: [''],
      patientReminderTypesValidate: ['', Validators.required],

      //general settings

      registrationId: [''],
      tenantKey: [''],
      name: ['', [Validators.required, this.noWhitespaceValidator]],
      escalationTime: ['', Validators.required],
      titleIncomingFilingCenter: [''],
      titleOutgoingFilingCenter: [''],

      noClinicianMessage: ['', [Validators.required, this.noWhitespaceValidator]],
      newPatientChatWelcomeMessage: [''],

      defaultUser: [''],
      defaultUsersNotificationEnrollment: [''],
      defaultUserssmsNotificationEnrollment: [''],
      defaultUsersemailNotificationEnrollment: [''],
      escalationBehaviour: [''],
      showPatientChatwithModal: [''],
      nursingAgenciesforManageMessageGroups:[''],
      nursingAgenciesforManageEducationMaterials: [''],
      nursingAgenciesThatPatientsAllowedToChatWithPharmacy:[''],
      nursingAgenciesThatStaffsAllowedToChatWithPharmacy:[''],
      defaultStaffRole:[''],
      defaultPartnerRole:[''],
      defaultPartnerCategory:[''],
      enrollMessage:[''],
      defaultStaffTags:[''],
      defaultPartnerTags:[''],
      flexSitePatientsCanChatWithInternalSiteStaffs:[''],
      tokenExpiryTime:[''],
      verificationRememberTime:[''],
      verificationTokenExpiryTime:['']
    });

    this._structureService.getAllTimeZones().then((timezones:any)=> {
      if(timezones && timezones.length) {
        this.timezones = timezones;
        this._structureService.getPreferences().then((data) => {
          if (data['getSessionTenant']) {
            //--------------------------------------------------------------------
            this.account = data['getSessionTenant'];

            console.log(this.account);

            if (this.account.configurationSettings.length > 0) {
              this.account.configurationSettings.forEach(element => {
                if (element.disable) {
                  this.hidePreferences.push(element.configuration);
                }
              });
            }

            this.accountPreferences.patchValue({
              escalationTime: this.account['escalationTimeInSec'],
              titleIncomingFilingCenter: this.account['titleIncomingFilingCenter'],
              titleOutgoingFilingCenter: this.account['titleOutgoingFilingCenter'],
              defaultUser: this.account['defaultUserToReceiveEscalatedMessage'],
              escalationBehaviour: this.account['messageEscalationBehavior'],
            });

            if (this.account['patientsToInitiateChatDuringDay']) {
              this.accountPreferences.patchValue({
                noClinicianMessage: this.account['noClinicianMessage']
              });
              this.accountPreferences.controls['noClinicianMessage'].setValidators(null);
              this.accountPreferences.controls['noClinicianMessage'].updateValueAndValidity();

              this.accountPreferences.controls['homeinfusionStart'].setValidators(null);
              this.accountPreferences.controls['homeinfusionStart'].updateValueAndValidity();

              this.accountPreferences.controls['homeinfusionEnd'].setValidators(null);
              this.accountPreferences.controls['homeinfusionEnd'].updateValueAndValidity();

            } else {
              this.accountPreferences.patchValue({
                noClinicianMessage: this.account['noClinicianMessage']
              });
              this.accountPreferences.controls['noClinicianMessage'].setValidators([Validators.required, this.noWhitespaceValidator]);
              this.accountPreferences.controls['noClinicianMessage'].updateValueAndValidity();

              this.accountPreferences.controls['homeinfusionStart'].setValidators(Validators.required);
              this.accountPreferences.controls['homeinfusionStart'].updateValueAndValidity();

              this.accountPreferences.controls['homeinfusionEnd'].setValidators(Validators.required);
              this.accountPreferences.controls['homeinfusionEnd'].updateValueAndValidity();
            }

            this.userList = this.account['privilegedStaffUsers'];
            this.selectedOptions = [];

            if (this.account['defaultUserToReceiveEscalatedMessage'].length > 0) {
              var idString = '';
              var idArray = [];
              for (var k = 0; k < this.account['defaultUserToReceiveEscalatedMessage'].length; k++) {
                this.selectedOptions.push(this.account['defaultUserToReceiveEscalatedMessage'][k].id);
              }
            }

            this.selectedDefaultUsers = this.account['defaultUserToReceiveEscalatedMessage'];

            for (var i = 0; i < this.account['privilegedStaffUsers'].length; i++) {
              this.member = {
                id: "",
                displayName: ""
              };
              this.member.id = this.account['privilegedStaffUsers'][i].id;
              this.member.displayName = this.account['privilegedStaffUsers'][i].displayName;
              this.defaultUserList.push(this.member);
            }

            for (var j = 0; j < this.account['defaultUserToReceiveEscalatedMessage'].length; j++) {
              this.member = {
                id: "",
                displayName: ""
              };
              this.member.id = this.account['defaultUserToReceiveEscalatedMessage'][j].id;
              this.member.displayName = this.account['defaultUserToReceiveEscalatedMessage'][j].displayName;
            }

            this.selectedUserListRecvEsc = [];

            if(this.account.tokenExpiryTime) {
              this.accountPreferences.patchValue({
                tokenExpiryTime: this.account['tokenExpiryTime'],
              });
            } else {
              this.accountPreferences.patchValue({
                tokenExpiryTime: 2880,
              });
            }

            if(this.account.verificationRememberTime) {
              this.accountPreferences.patchValue({
                verificationRememberTime: this.account['verificationRememberTime'],
              });
            } else {
              this.accountPreferences.patchValue({
                verificationRememberTime: '60',
              });
            }

            if(this.account.verificationTokenExpiryTime) {
              this.accountPreferences.patchValue({
                verificationTokenExpiryTime: this.account['verificationTokenExpiryTime'],
              });
            } else {
              this.accountPreferences.patchValue({
                verificationTokenExpiryTime: '10',
              });
            }

            for (var i = 0; i < this.account['privilegedStaffUsers'].length; i++) {
              var ismatch = false;

              if (this.selectedDefaultUsers.length > 0) {
                for (var j = 0; j < this.account['defaultUserToReceiveEscalatedMessage'].length; j++) {
                  if (this.account['privilegedStaffUsers'][i].id == this.account['defaultUserToReceiveEscalatedMessage'][j].id) {
                    var obj = {
                      'id': this.account['privilegedStaffUsers'][i].id,
                      'displayName': this.account['privilegedStaffUsers'][i].displayName,
                      'status': true
                    };
                    this.selectedUserListRecvEsc[i] = obj;
                    break
                  } else {
                    var obj = {
                      'id': this.account['privilegedStaffUsers'][i].id,
                      'displayName': this.account['privilegedStaffUsers'][i].displayName,
                      'status': false
                    };
                    this.selectedUserListRecvEsc[i] = obj;
                  }
                }
              } else {
                var obj = {
                  'id': this.account['privilegedStaffUsers'][i].id,
                  'displayName': this.account['privilegedStaffUsers'][i].displayName,
                  'status': false
                };
                this.selectedUserListRecvEsc[i] = obj;
              }

            }

            setTimeout(() => {
              $('#defaultUser').select2('data', this.selectedUsers);
            }, 1000);
            //--------------------------------------------------------------------

            this.hidePreferences = [];
            this.dataLoadingMsg = false;
            this.preferences = data['getSessionTenant'];
            console.log("this.preferences ----", this.preferences);

            if (this.preferences.configurationSettings.length > 0) {
              this.preferences.configurationSettings.forEach(element => {
                if (element.disable) {
                  this.hidePreferences.push(element.configuration);
                }
              });
            }

            var arrayformtags = [];
            console.log("just" + "before");
            console.log(this.preferences.partnerReferralFormTag);
            console.log(this.preferences);

            if (this.preferences.partnerReferralFormTag) {
              console.log(this.preferences.partnerReferralFormTag);
              for (var i = 0; i < this.preferences.partnerReferralFormTag.length; i++) {
                console.log(i);
                arrayformtags.push(this.preferences.partnerReferralFormTag[i].id);
              }
            }

            if ((this.preferences.defaultOutgoingFilingCenterChatLog != '') && (this.isFilingCenter)) {
              this.FilingCenterChatLog = this.preferences.defaultOutgoingFilingCenterChatLog;
              this.folderName = this.FilingCenterChatLog;
              this.fileSavingFormatChatLogFilingCenterValue = this.preferences.fileSavingFormatChatLogFilingCenter;
            }

            console.log(this.fileSavingFormatChatLogFilingCenterValue);

            if (this.preferences.tenantTimezone && this.preferences.tenantTimezone != "") {
              this.tenantTimeZone = this.preferences.tenantTimezone;
            } else {
              this.tenantTimeZone = ((new Date().getTimezoneOffset()) * -1);
            }
            this.tenantTimeZoneData = this.timezones.find((zone, i)=>{return zone.offset == this.tenantTimeZone});;
            if(this.preferences.patientMessageSmsNotifcationBeyondBranch24hr) {
              this.allReminderTypes.push({id: "sms", name: "SMS"});
            }
            if(this.preferences.enableEmailForPatient) {
              this.allReminderTypes.push({id: "email", name: "Email"});
            } 
            // code to verify whether the administrator partner manage security rules and configurations enabled	
            let index= this.partnerCategories.findIndex(x=> x.id == this.preferences.defaultPartnerCategory);	
            if(index == -1) {
              this.preferences.defaultPartnerCategory = '';
            }
            //console.log( JSON.stringify(this.preferences ));
            this.accountPreferences.patchValue({
              sessionTimeout: this.preferences.sessionTimeoutInSec,
              sessionTimeoutWarning: this.preferences.sessionTimeoutWarningInSec,
              partnerPatientReferralIntroduction: this.preferences.partnerPatientReferralIntroductiontext,
              messageForwardBehavior: this.preferences.messageForwardBehavior,
              chatAutoTranslate: this.preferences.chatAutoTranslate,
              chatInfusionSupport: this.preferences.infusionSupport,
              chatEnableWhereIsMyDelivery: this.preferences.enableWhereIsMyDelivery,
              chatEnableWhenIsMyNurseComing: this.preferences.enableWhenIsMyNurseComing,
              gaTracking: this.preferences.gATracking,
              watchtowerTracking: this.preferences.watchTowerTracking,
              showProtypes: this.preferences.showPrototypes,
              showInfusionSupport: this.preferences.infusionSupport,
              messageTagging: this.preferences.messageTagging,
              userTagging: this.preferences.userTagging,
              documentTagging: this.preferences.documentTagging,
              allowPatientDiscussionGroup: this.preferences.patientDiscussionGroup,
              allowPatientSelfInventory: this.preferences.patientSelfInventory,
              patientsToInitiateChatDuringDay: this.preferences.patientsToInitiateChatDuringDay,
              taggedMessageApprover: this.preferences.taggedMessageApprover,
              showChatHistoryToNewParticipant: this.preferences.showChatHistoryToNewParticipant,
              showPatientStatusDashboard: this.preferences.showPatientStatusDashboard,
              enableMultiThreadPdg: this.preferences.enableMultiThreadPdg,
              // enableDisplayName:this.preferences.enableDisplayName,
              enableDisplayName: true,
              patientSignupWithAdminApproval: this.preferences.patientSignupWithAdminApproval,
              clinicianSignupWithAdminApproval: this.preferences.clinicianSignupWithAdminApproval,
              rolesChatWithUsNow: this.preferences.messageDestinationConfig.rolesChatWithUsNow ? this.preferences.messageDestinationConfig.rolesChatWithUsNow : [],
              rolesWhereIsMyDelivery: this.preferences.messageDestinationConfig.rolesWhereIsMyDelivery ? this.preferences.messageDestinationConfig.rolesWhereIsMyDelivery : [],
              rolesWhenNurseArrive: this.preferences.messageDestinationConfig.rolesWhenNurseArrive ? this.preferences.me8ssageDestinationConfig.rolesWhenNurseArrive : [],
              rolesIhaveQuestion: this.preferences.messageDestinationConfig.rolesIhaveQuestion ? this.preferences.messageDestinationConfig.rolesIhaveQuestion : [],
              rolesDefaultNurses: this.preferences.messageDestinationConfig.rolesDefaultNurses ? this.preferences.messageDestinationConfig.rolesDefaultNurses : [],
              conversationType: this.preferences.conversationType,
              chatRole1DisplayName: this.preferences.chatRole1DisplayName,
              chatRole2DisplayName: this.preferences.chatRole2DisplayName,
              chatRole1MessageTemplate: this.preferences.chatRole1MessageTemplate,
              chatRole2MessageTemplate: this.preferences.chatRole2MessageTemplate,
              initiationMessage: this.preferences.initiationMessage,
              chatRole1: this.preferences.chatRole1,
              //partnerReferralFormTag:["264"],
              partnerReferralFormTag: arrayformtags,
              maskedDiscussionGroupReceiptRole: this.preferences.maskedDiscussionGroupReceiptRole,
              chatRole2: this.preferences.chatRole2,
              enableFilingCenter: this.preferences.enableFilingCenter,
              allowPrefillingPartialEnrollment: this.preferences.allowPrefillingPartialEnrollment,
              caregiverSeesEnrollmentInvitationData: this.preferences.caregiverSeesEnrollmentInvitationData,
              enableMaskedDiscussionGroup: this.preferences.enableMaskedDiscussionGroup,
              patientReminderTime: this.preferences.patientReminderTime / 60,
              enrollmentReminderTypes: (this.preferences.enrollmentReminderTypes) ? this.preferences.enrollmentReminderTypes.map(a => a.id) : [],
              tenantTimeZone: (this.tenantTimeZone) ? this.tenantTimeZone : ((new Date().getTimezoneOffset()) * -1),
              branchDays: (this.preferences.branchDays) ? this.preferences.branchDays.map(a => a.id) : [],
              patientReminderTypes: this.preferences.patientReminderTypes,
              patientReminderTypesValidate: this.preferences.patientReminderTypes,
              showPatientChatwithModal: this.preferences.showPatientChatwithModal,
              fileSavingFormatChatLogFilingCenter: this.fileSavingFormatChatLogFilingCenterValue,
              defaultOutgoingFilingCenterChatLog: [''],
              newPatientChatWelcomeMessage: this.preferences.newPatientChatWelcomeMessage,
              defaultStaffRole: this.preferences.defaultStaffRole,
              defaultPartnerRole: this.preferences.defaultPartnerRole,
              defaultPartnerCategory:this.preferences.defaultPartnerCategory,
              enrollMessage: this.preferences.enrollMessage,
              defaultStaffTags: this.preferences.defaultStaffTags.split(','),
              defaultPartnerTags: this.preferences.defaultPartnerTags.split(',')
            });
            if(this.preferences.defaultSites && this.preferences.defaultSites !="" && this.preferences.defaultSites !="0" && this.multiSiteEnable){
              this.siteIdss=  (this.preferences.defaultSites.split(',').map(Number));
              }else{
                this.siteIdss = [];
              }
            this.conversationTypeStatus = this.preferences.conversationType;
            console.log("conversationTypeStatus: ", this.conversationTypeStatus);
            console.log("tenantTimeZone: ", this.tenantTimeZoneData);
            let tenantTimeZone = this.tenantTimeZoneData.offset;
            tenantTimeZone = tenantTimeZone.split(',')[0];
            tenantTimeZone = (((tenantTimeZone) * -1) / 60) * -1;

            let timeToPassDay = [];
            let timeDifference;
            console.log(this.timezone, tenantTimeZone)

            if (tenantTimeZone > this.timezone) {
              timeDifference = Math.abs(this.timezone - tenantTimeZone);
            } else {
              timeDifference = -1 * Math.abs(this.timezone - tenantTimeZone);
            }

            if (((timeDifference) + '').split('.').length == 2) {
              timeToPassDay = ((timeDifference) + '').split('.');
            } else {
              timeToPassDay = ((timeDifference) + '').split('.');
              timeToPassDay.push("0");
            }

            if (timeToPassDay[1] == '5') {
              if (timeDifference < 0) {
                timeToPassDay[1] = '-30';
              } else {
                timeToPassDay[1] = '30';
              }
            } else {
              timeToPassDay[1] = '0';
            }

            if (timeToPassDay[0] == '-0') {
              timeToPassDay[0] = '0';
            }

            timeToPassDay = timeToPassDay.map(function (time) {
              return parseInt(time, 10);
            });

            console.log('timeToPassDay--->', timeToPassDay);

            if (this.preferences.messageDestinationConfig.startTime && this.preferences.messageDestinationConfig.startTime.indexOf(":") !== -1) {
              // let startTime = this._structureService.clientToGmtTime(this.preferences.messageDestinationConfig.startTime, true, undefined, undefined);
              // startTime = this._structureService.convertTO12Hour(startTime);
              let startTime = moment(moment().format('YYYY-MM-DD') + ' ' + this.preferences.messageDestinationConfig.startTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('hh:mm A');
              if (startTime.indexOf(":") !== -1) {
                var homeInfusionStartTime = startTime.split(' ');
                console.log(homeInfusionStartTime);
                startTime = homeInfusionStartTime[0];
                if (homeInfusionStartTime[1] == 'AM') {
                  this.accountPreferences.patchValue({
                    homeInfusionStartTime: 12,
                    homeinfusionStart: startTime
                  });
                } else {
                  this.accountPreferences.patchValue({
                    homeInfusionStartTime: 24,
                    homeinfusionStart: startTime
                  });
                }
              }
            }

            if (this.preferences.messageDestinationConfig.endTime && this.preferences.messageDestinationConfig.endTime.indexOf(":") !== -1) {
              // let endTime = this._structureService.clientToGmtTime(this.preferences.messageDestinationConfig.endTime, true, undefined, undefined);
              // endTime = this._structureService.convertTO12Hour(endTime);
              let endTime = moment(moment().format('YYYY-MM-DD') + ' ' + this.preferences.messageDestinationConfig.endTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('hh:mm A');
              
              if (endTime.indexOf(":") !== -1) {
                var homeInfusionEndTime = endTime.split(' ');
                console.log(homeInfusionEndTime);
                endTime = homeInfusionEndTime[0];
                if (homeInfusionEndTime[1] == 'AM') {
                  this.accountPreferences.patchValue({
                    homeinfusionEndTime: 12,
                    homeinfusionEnd: endTime
                  });
                } else {
                  this.accountPreferences.patchValue({
                    homeinfusionEndTime: 24,
                    homeinfusionEnd: endTime
                  });
                }
              }
            }

            /* */
            if (this.preferences.messageDestinationConfig.branchStartTime && this.preferences.messageDestinationConfig.branchStartTime.indexOf(":") !== -1) {
              // let startTime = this._structureService.clientToGmtTime(this.preferences.messageDestinationConfig.branchStartTime, true, undefined, undefined);
              // startTime = this._structureService.convertTO12Hour(startTime);
              let startTime = moment(moment().format('YYYY-MM-DD') + ' ' + this.preferences.messageDestinationConfig.branchStartTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('hh:mm A');
              if (startTime.indexOf(":") !== -1) {
                var branchStartTime = startTime.split(' ');
                console.log(branchStartTime);
                startTime = branchStartTime[0];
                if (branchStartTime[1] == 'AM') {
                  this.accountPreferences.patchValue({
                    branchStartTime: 12,
                    branchStart: startTime
                  });
                } else {
                  this.accountPreferences.patchValue({
                    branchStartTime: 24,
                    branchStart: startTime
                  });
                }
              }
            }

            if (this.preferences.messageDestinationConfig.branchEndTime && this.preferences.messageDestinationConfig.branchEndTime.indexOf(":") !== -1) {
              // let endTime = this._structureService.clientToGmtTime(this.preferences.messageDestinationConfig.branchEndTime, true, undefined, undefined);
              // endTime = this._structureService.convertTO12Hour(endTime);
              let endTime = moment(moment().format('YYYY-MM-DD') + ' ' + this.preferences.messageDestinationConfig.branchEndTime).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('hh:mm A');
              if (endTime.indexOf(":") !== -1) {
                var branchEndTime = endTime.split(' ');
                console.log(branchEndTime);
                endTime = branchEndTime[0];
                if (branchEndTime[1] == 'AM') {
                  this.accountPreferences.patchValue({
                    branchEndTime: 12,
                    branchEnd: endTime
                  });
                } else {
                  this.accountPreferences.patchValue({
                    branchEndTime: 24,
                    branchEnd: endTime
                  });
                }
              }
            }

            /* */
            var rolesChatWithUsNowInput = [];
            var rolesWhereIsMyDeliveryInput = [];
            var rolesWhenNurseArriveInput = [];
            var rolesIhaveQuestionInput = [];
            var rolesDefaultNursesInput = [];
            var chatRole1Input = [];
            var chatRole2Input = [];
            var partnerReferralFormTagInput = [];
            var maskedDiscussionGroupReceiptRoleInput = [];
            var patientReminderTypesInput = [];
            var nursingAgenciesforManageMessageGroupsInput = [];
            var nursingAgenciesforManageEducationMaterialsInput = [];

            if (this.preferences.messageDestinationConfig && this.preferences.patientsToInitiateChatDuringDay) {
              this.preferences.messageDestinationConfig.chatwithUsNowRolesAfterDay.map(function (roles) {
                rolesChatWithUsNowInput.push(String(roles.id));
              });

              this.preferences.messageDestinationConfig.whereIsMyDeliveryRolesAfterDay.map(function (roles) {
                rolesWhereIsMyDeliveryInput.push(String(roles.id));
              });

              this.preferences.messageDestinationConfig.whenWillMyNurseArriveRolesAfterDay.map(function (roles) {
                rolesWhenNurseArriveInput.push(String(roles.id));
              });

              this.preferences.messageDestinationConfig.iNeedHelpOrIHaveAQuestionRolesAfterDay.map(function (roles) {
                rolesIhaveQuestionInput.push(String(roles.id));
              });

              this.preferences.messageDestinationConfig.defaultClinicianRolesAfterDay.map(function (roles) {
                rolesDefaultNursesInput.push(String(roles.id));
              });
              console.log(rolesChatWithUsNowInput);
            } else if (this.preferences.messageDestinationConfig && !this.preferences.patientsToInitiateChatDuringDay) {
              this.preferences.messageDestinationConfig.chatwithUsNowRolesDuringDay.map(function (roles) {
                rolesChatWithUsNowInput.push(String(roles.id));
              });

              this.preferences.messageDestinationConfig.whereIsMyDeliveryRolesDuringDay.map(function (roles) {
                rolesWhereIsMyDeliveryInput.push(String(roles.id));
              });

              this.preferences.messageDestinationConfig.whenWillMyNurseArriveRolesDuringDay.map(function (roles) {
                rolesWhenNurseArriveInput.push(String(roles.id));
              });

              this.preferences.messageDestinationConfig.iNeedHelpOrIHaveAQuestionRolesDuringDay.map(function (roles) {
                rolesIhaveQuestionInput.push(String(roles.id));
              });

              this.preferences.messageDestinationConfig.defaultClinicianRolesDuringDay.map(function (roles) {
                rolesDefaultNursesInput.push(String(roles.id));
              });
              console.log(rolesChatWithUsNowInput);
            }

            if (this.preferences.chatRole1) {
              this.preferences.chatRole1.map(function (roles) {
                chatRole1Input.push(String(roles.id));
              });
            }

            if (this.preferences.partnerReferralFormTag) {
              this.preferences.partnerReferralFormTag.map(function (roles) {
                partnerReferralFormTagInput.push(String(roles.id));
              });
            }

            if (this.preferences.chatRole2) {
              this.preferences.chatRole2.map(function (roles) {
                chatRole2Input.push(String(roles.id));
              });
            }

            if (this.preferences.maskedDiscussionGroupReceiptRole) {
              this.preferences.maskedDiscussionGroupReceiptRole.map(function (roles) {
                maskedDiscussionGroupReceiptRoleInput.push(String(roles.id));
              });
            }       

            if (this.preferences.patientReminderTypes) {
              this.preferences.patientReminderTypes.map(function (types) {
                patientReminderTypesInput.push(String(types.id));
              });
            }

            this.accountPreferences.patchValue({
              rolesChatWithUsNow: rolesChatWithUsNowInput,
              rolesWhereIsMyDelivery: rolesWhereIsMyDeliveryInput,
              rolesWhenNurseArrive: rolesWhenNurseArriveInput,
              rolesIhaveQuestion: rolesIhaveQuestionInput,
              rolesDefaultNurses: rolesDefaultNursesInput,
              chatRole1: chatRole1Input,
              partnerReferralFormTag: partnerReferralFormTagInput,
              maskedDiscussionGroupReceiptRole: maskedDiscussionGroupReceiptRoleInput,
              chatRole2: chatRole2Input,
              patientReminderTypes: patientReminderTypesInput,
              nursingAgenciesforManageMessageGroups: this.preferences.nursingAgenciesforManageMessageGroups,
              nursingAgenciesforManageEducationMaterials: this.preferences.nursingAgenciesforManageEducationMaterials,
              nursingAgenciesThatPatientsAllowedToChatWithPharmacy:this.preferences.nursingAgenciesThatPatientsAllowedToChatWithPharmacy,
              nursingAgenciesThatStaffsAllowedToChatWithPharmacy: this.preferences.nursingAgenciesThatStaffsAllowedToChatWithPharmacy,
              flexSitePatientsCanChatWithInternalSiteStaffs:this.preferences.flexSitePatientsCanChatWithInternalSiteStaffs
            });

            console.log("this.accountPreferences", this.accountPreferences);

            setTimeout(function () {
              $(".select2").select2({});
            }, 500);

            this.selectedOptionstagged = [];

            if (this.preferences.taggedMessageApprover.length > 0) {
              var idString = '';
              var idArray = [];
              for (var k = 0; k < this.preferences.taggedMessageApprover.length; k++) {
                this.selectedOptionstagged.push(this.preferences.taggedMessageApprover[k].id);
              }
            }

            this.selectedUserList = [];

            for (var i = 0; i < data['getSessionTenant']['staffUsers'].length; i++) {
              var ismatch = false;

              if (data['getSessionTenant']['taggedMessageApprover'].length > 0) {
                for (var j = 0; j < data['getSessionTenant']['taggedMessageApprover'].length; j++) {
                  if (data['getSessionTenant']['staffUsers'][i].id == data['getSessionTenant']['taggedMessageApprover'][j].id) {
                    var obj = {
                      'id': data['getSessionTenant']['staffUsers'][i].id,
                      'displayName': data['getSessionTenant']['staffUsers'][i].displayName,
                      'status': true
                    };
                    this.selectedUserList[i] = obj;
                    break
                  } else {
                    var obj = {
                      'id': data['getSessionTenant']['staffUsers'][i].id,
                      'displayName': data['getSessionTenant']['staffUsers'][i].displayName,
                      'status': false
                    };
                    this.selectedUserList[i] = obj;

                  }
                }
              } else {
                var obj = {
                  'id': data['getSessionTenant']['staffUsers'][i].id,
                  'displayName': data['getSessionTenant']['staffUsers'][i].displayName,
                  'status': false
                };
                this.selectedUserList[i] = obj;
              }
            }

            this.selectedUserListclinicalLiaison = [];

            for (var i = 0; i < data['getSessionTenant']['staffUsers'].length; i++) {
              var ismatch = false;
              if (data['getSessionTenant']['clinicalLiaison'].length > 0) {
                for (var j = 0; j < data['getSessionTenant']['clinicalLiaison'].length; j++) {
                  if (data['getSessionTenant']['staffUsers'][i].id == data['getSessionTenant']['clinicalLiaison'][j].id) {
                    var obj = {
                      'id': data['getSessionTenant']['staffUsers'][i].id,
                      'displayName': data['getSessionTenant']['staffUsers'][i].displayName,
                      'status': true
                    };
                    this.selectedUserListclinicalLiaison[i] = obj;
                    break
                  } else {
                    var obj = {
                      'id': data['getSessionTenant']['staffUsers'][i].id,
                      'displayName': data['getSessionTenant']['staffUsers'][i].displayName,
                      'status': false
                    };
                    this.selectedUserListclinicalLiaison[i] = obj;

                  }
                }
              } else {
                var obj = {
                  'id': data['getSessionTenant']['staffUsers'][i].id,
                  'displayName': data['getSessionTenant']['staffUsers'][i].displayName,
                  'status': false
                };
                this.selectedUserListclinicalLiaison[i] = obj;
              }
            }

            console.log("============================================================")
            console.log("============================================================")
            console.log(this.selectedUserListclinicalLiaison);
            console.log("============================================================")

            this.staffsAwareEnrollBySms = [];
            console.log("============================================================")
            console.log("============================================================")
            console.log("============================================================")

            for (var i = 0; i < data['getSessionTenant']['staffUsers'].length; i++) {
              var ismatch = false;
              if (data['getSessionTenant']['staffsAwareEnrollBySms'].length > 0) {
                for (var j = 0; j < data['getSessionTenant']['staffsAwareEnrollBySms'].length; j++) {
                  if (data['getSessionTenant']['staffUsers'][i].id == data['getSessionTenant']['staffsAwareEnrollBySms'][j].id) {
                    var obj = {
                      'id': data['getSessionTenant']['staffUsers'][i].id,
                      'displayName': data['getSessionTenant']['staffUsers'][i].displayName,
                      'status': true
                    };
                    this.staffsAwareEnrollBySms[i] = obj;
                    break
                  } else {
                    var obj = {
                      'id': data['getSessionTenant']['staffUsers'][i].id,
                      'displayName': data['getSessionTenant']['staffUsers'][i].displayName,
                      'status': false
                    };
                    this.staffsAwareEnrollBySms[i] = obj;

                  }
                }
              } else {
                var obj = {
                  'id': data['getSessionTenant']['staffUsers'][i].id,
                  'displayName': data['getSessionTenant']['staffUsers'][i].displayName,
                  'status': false
                };
                this.staffsAwareEnrollBySms[i] = obj;
              }

            }
            console.log("============================================================")
            console.log("============================================================")
            console.log(this.staffsAwareEnrollBySms);
            console.log("============================================================")

            this.staffsAwareEnrollByEmail = [];
            console.log("============================================================")
            console.log("============================================================")
            console.log("============================================================")
            for (var i = 0; i < data['getSessionTenant']['staffUsers'].length; i++) {
              var ismatch = false;
              if (data['getSessionTenant']['staffsAwareEnrollByEmail'].length > 0) {
                for (var j = 0; j < data['getSessionTenant']['staffsAwareEnrollByEmail'].length; j++) {
                  if (data['getSessionTenant']['staffUsers'][i].id == data['getSessionTenant']['staffsAwareEnrollByEmail'][j].id) {
                    var obj = {
                      'id': data['getSessionTenant']['staffUsers'][i].id,
                      'displayName': data['getSessionTenant']['staffUsers'][i].displayName,
                      'status': true
                    };
                    this.staffsAwareEnrollByEmail[i] = obj;
                    break
                  } else {
                    var obj = {
                      'id': data['getSessionTenant']['staffUsers'][i].id,
                      'displayName': data['getSessionTenant']['staffUsers'][i].displayName,
                      'status': false
                    };
                    this.staffsAwareEnrollByEmail[i] = obj;

                  }
                }
              } else {
                var obj = {
                  'id': data['getSessionTenant']['staffUsers'][i].id,
                  'displayName': data['getSessionTenant']['staffUsers'][i].displayName,
                  'status': false
                };
                this.staffsAwareEnrollByEmail[i] = obj;
              }

            }
            console.log("============================================================")
            console.log("============================================================")
            console.log(this.staffsAwareEnrollByEmail);
            console.log("============================================================")

          } else {
            this._structureService.deleteCookie('authenticationToken');
            this.router.navigate(['/login']);
          }

          var arrayOfElements = [
            'introduction-text',
            'language-translation',
            'clinical-faq',
            'enable-nurse-coming-and-delivery',
            'enable-where-is-my-delivery',
            'enable-masked-discussion-group',
            'access-enrollment-invitation-data-patient',
            'access-enrollment-invitation-data-caregiver',
            'popup-patient-chat',
            'masked-discussion-group-recipient-role',
            'escalation-time',
            'session-timeout',
            'session-timeout-warning',
            'patient-reminder-time',
            'patient-reminder-types',
            'default-user-received-escalated-message',
            'allow-patient-chat-hours',
            'no-staff-available-message',
            'patient-messaging',
            'roles-default-users',
            'user-enrollment-notification'
          ];
          
          setTimeout(() => {
            this._structureService.setIntroOptions('account,account-settings', arrayOfElements, true, (conditionalParams) => {
              if (conditionalParams.step) {
                this._sharedService.startIntroCallBack.emit({
                  value: true,
                  step: conditionalParams.step
                });
              } else {
                this._sharedService.startIntroCallBack.emit({
                  value: true
                });
              }
              this._structureService.introJsObject.oncomplete(() => {
                this._structureService.introJsObject.exit();
                delete this._structureService.introJsObject;
              });
              this._structureService.introJsObject.onexit(() => {
                delete this._structureService.introJsObject;
                delete this._structureService.introSteps;
              });
            });
          });
        });
      }
    });
  }

  isValidTime(time) {
    var pattern = /^(1[0-2]|0?[1-9]):[0-5][0-9]$/;
    return pattern.test(time);
  };

  // General setting function starts
  getAccount(accountId) {
    this._structureService.getAccount(accountId).then((
      data) => {
      if (data['getSessionTenant']) {
        this.account = data['getSessionTenant'];

        if (this.account.configurationSettings.length > 0) {
          this.account.configurationSettings.forEach(element => {
            if (element.disable) {
              this.hidePreferences.push(element.configuration);
            }

          });
        }

        this.accountPreferences.patchValue({
          escalationTime: this.account['escalationTimeInSec'],
          titleIncomingFilingCenter: this.account['titleIncomingFilingCenter'],
          titleOutgoingFilingCenter: this.account['titleOutgoingFilingCenter'],
          defaultUser: this.account['defaultUserToReceiveEscalatedMessage'],
          escalationBehaviour: this.account['messageEscalationBehavior']
        });

        if (this.account['patientsToInitiateChatDuringDay']) {
          this.accountPreferences.patchValue({
            noClinicianMessage: this.account['noClinicianMessage']
          });

          this.accountPreferences.controls['noClinicianMessage'].setValidators(null);
          this.accountPreferences.controls['noClinicianMessage'].updateValueAndValidity();
          this.accountPreferences.controls['homeinfusionStart'].setValidators(null);
          this.accountPreferences.controls['homeinfusionStart'].updateValueAndValidity();
          this.accountPreferences.controls['homeinfusionEnd'].setValidators(null);
          this.accountPreferences.controls['homeinfusionEnd'].updateValueAndValidity();
        } else {
          this.accountPreferences.patchValue({
            noClinicianMessage: this.account['noClinicianMessage']
          });

          this.accountPreferences.controls['noClinicianMessage'].setValidators([Validators.required, this.noWhitespaceValidator]);
          this.accountPreferences.controls['noClinicianMessage'].updateValueAndValidity();
          this.accountPreferences.controls['homeinfusionStart'].setValidators(Validators.required);
          this.accountPreferences.controls['homeinfusionStart'].updateValueAndValidity();
          this.accountPreferences.controls['homeinfusionEnd'].setValidators(Validators.required);
          this.accountPreferences.controls['homeinfusionEnd'].updateValueAndValidity();
        }

        this.userList = this.account['staffUsers'];

        if (this.account['defaultUserToReceiveEscalatedMessage'].length > 0) {
          var idString = '';
          var idArray = [];
          for (var k = 0; k < this.account['defaultUserToReceiveEscalatedMessage'].length; k++) {
            this.selectedOptions.push(this.account['defaultUserToReceiveEscalatedMessage'][k].id);
          }
        }

        this.selectedDefaultUsers = this.account['defaultUserToReceiveEscalatedMessage'];

        for (var i = 0; i < this.account['staffUsers'].length; i++) {
          this.member = {
            id: "",
            displayName: ""
          };
          this.member.id = this.account['staffUsers'][i].id;
          this.member.displayName = this.account['staffUsers'][i].displayName;
          this.defaultUserList.push(this.member);
        }

        for (var j = 0; j < this.account['defaultUserToReceiveEscalatedMessage'].length; j++) {
          this.member = {
            id: "",
            displayName: ""
          };
          this.member.id = this.account['defaultUserToReceiveEscalatedMessage'][j].id;
          this.member.displayName = this.account['defaultUserToReceiveEscalatedMessage'][j].displayName;
        }

        this.selectedUserListRecvEsc = [];

        for (var i = 0; i < this.account['staffUsers'].length; i++) {
          var ismatch = false;
          if (this.selectedDefaultUsers.length > 0) {
            for (var j = 0; j < this.account['defaultUserToReceiveEscalatedMessage'].length; j++) {
              if (this.account['staffUsers'][i].id == this.account['defaultUserToReceiveEscalatedMessage'][j].id) {
                var obj = {
                  'id': this.account['staffUsers'][i].id,
                  'displayName': this.account['staffUsers'][i].displayName,
                  'status': true
                };
                this.selectedUserListRecvEsc[i] = obj;
                break
              } else {
                var obj = {
                  'id': this.account['staffUsers'][i].id,
                  'displayName': this.account['staffUsers'][i].displayName,
                  'status': false
                };
                this.selectedUserListRecvEsc[i] = obj;
              }
            }
          } else {
            var obj = {
              'id': this.account['staffUsers'][i].id,
              'displayName': this.account['staffUsers'][i].displayName,
              'status': false
            };
            this.selectedUserListRecvEsc[i] = obj;
          }

        }
        setTimeout(() => {
          $('#defaultUser').select2('data', this.selectedUsers);
        }, 1000);
      } else {
        this._structureService.deleteCookie('authenticationToken');
        this.router.navigate(['/login']);
      }
    });
  }
  // General setting function ends

  noWhitespaceValidator(control: FormControl) {
    let isWhitespace = (control.value || '').trim().length === 0;
    let isValid = !isWhitespace;
    return isValid ? null : {
      'whitespace': true
    }
  }

  togglePreference(preference, value) {
    if (preference === 'showChatHistoryToNewParticipant') {
      this.accountPreferences.patchValue({
        showChatHistoryToNewParticipant: value
      });
    } else if (preference === 'showPatientStatusDashboard') {
      this.accountPreferences.patchValue({
        showPatientStatusDashboard: value
      });
    } else if (preference === 'enableMultiThreadPdg') {
      this.accountPreferences.patchValue({
        enableMultiThreadPdg: value
      });
    } else if (preference === 'enableDisplayName') {
      this.accountPreferences.patchValue({
        enableDisplayName: true
      });
    } else if (preference === 'chatAutoTranslate') {
      this.accountPreferences.patchValue({
        chatAutoTranslate: value
      });
    } else if (preference === 'showInfusionSupport') {
      this.accountPreferences.patchValue({
        showInfusionSupport: value
      });
    } else if (preference === 'gaTracking') {
      this.accountPreferences.patchValue({
        gaTracking: value
      });
    } else if (preference === 'watchtowerTracking') {
      this.accountPreferences.patchValue({
        watchtowerTracking: value
      });
    } else if (preference === 'showProtypes') {
      this.accountPreferences.patchValue({
        showProtypes: value
      });
    } else if (preference === 'messageTagging') {
      this.accountPreferences.patchValue({
        messageTagging: value
      });
    } else if (preference === 'userTagging') {
      this.accountPreferences.patchValue({
        userTagging: value
      });
    } else if (preference === 'documentTagging') {
      this.accountPreferences.patchValue({
        documentTagging: value
      });
    } else if (preference === 'allowPatientDiscussionGroup') {
      this.accountPreferences.patchValue({
        allowPatientDiscussionGroup: value
      });
    } else if (preference === 'allowPatientSelfInventory') {
      this.accountPreferences.patchValue({
        allowPatientSelfInventory: value
      });
    } else if (preference === 'showPatientChatwithModal') {
      this.accountPreferences.patchValue({
        showPatientChatwithModal: value
      });
    } else if (preference === 'patientsToInitiateChatDuringDay') {
      if (value) {
        this.accountPreferences.controls['noClinicianMessage'].setValidators(null);
        this.accountPreferences.controls['noClinicianMessage'].updateValueAndValidity();

        this.accountPreferences.controls['homeinfusionStart'].setValidators(null);
        this.accountPreferences.controls['homeinfusionStart'].updateValueAndValidity();

        this.accountPreferences.controls['homeinfusionEnd'].setValidators(null);
        this.accountPreferences.controls['homeinfusionEnd'].updateValueAndValidity();

        this.accountPreferences.patchValue({
          noClinicianMessage: this.account['noClinicianMessage']
        });
      } else {
        this.accountPreferences.controls['noClinicianMessage'].setValidators([Validators.required, this.noWhitespaceValidator]);
        this.accountPreferences.controls['noClinicianMessage'].updateValueAndValidity();

        this.accountPreferences.controls['homeinfusionStart'].setValidators(Validators.required);
        this.accountPreferences.controls['homeinfusionStart'].updateValueAndValidity();

        this.accountPreferences.controls['homeinfusionEnd'].setValidators(Validators.required);
        this.accountPreferences.controls['homeinfusionEnd'].updateValueAndValidity();

        this.accountPreferences.patchValue({
          noClinicianMessage: this.account['noClinicianMessage']
        });
      }

      this.accountPreferences.patchValue({
        patientsToInitiateChatDuringDay: value
      });
    } else if (preference === 'chatInfusionSupport') {
      this.accountPreferences.patchValue({
        chatInfusionSupport: value
      });
    } else if (preference === 'chatEnableWhenIsMyNurseComing') {
      this.accountPreferences.patchValue({
        chatEnableWhenIsMyNurseComing: value
      });
    } else if (preference === 'chatEnableWhereIsMyDelivery') {
      this.accountPreferences.patchValue({
        chatEnableWhereIsMyDelivery: value
      });
    } else if (preference === 'patientSignupWithAdminApproval') {
      this.accountPreferences.patchValue({
        patientSignupWithAdminApproval: value
      });
    } else if (preference === 'clinicianSignupWithAdminApproval') {
      this.accountPreferences.patchValue({
        clinicianSignupWithAdminApproval: value
      });
    } else if (preference === 'enableFilingCenter') {
      this.accountPreferences.patchValue({
        enableFilingCenter: value
      });
    } else if (preference === 'allowPrefillingPartialEnrollment') {
      this.accountPreferences.patchValue({
        allowPrefillingPartialEnrollment: value
      });
    } else if (preference === 'caregiverSeesEnrollmentInvitationData') {
      this.accountPreferences.patchValue({
        caregiverSeesEnrollmentInvitationData: value
      });
    } else if (preference === 'enableMaskedDiscussionGroup') {
      this.accountPreferences.patchValue({
        enableMaskedDiscussionGroup: value
      });
    } else if (preference === 'nursingAgenciesThatPatientsAllowedToChatWithPharmacy') {
      var SetValue;
      if(value == true || value == 1) {
        SetValue = 1;
      } else {
        SetValue = 0;
      }
      this.accountPreferences.patchValue({
        nursingAgenciesThatPatientsAllowedToChatWithPharmacy: SetValue
      });
    } else if (preference === 'nursingAgenciesThatStaffsAllowedToChatWithPharmacy') {
      if(value == true) {
        SetValue = 1;
      } else {
        SetValue = 0;
      }
      this.accountPreferences.patchValue({
        nursingAgenciesThatStaffsAllowedToChatWithPharmacy: SetValue
      });
    } else if (preference === 'flexSitePatientsCanChatWithInternalSiteStaffs') {
      if(value == true) {
        SetValue = 1;
      } else {
        SetValue = 0;
      }
      this.accountPreferences.patchValue({
        flexSitePatientsCanChatWithInternalSiteStaffs: SetValue
      });
    } else if (preference === 'nursingAgenciesforManageMessageGroups') {
      var SetValue;
      if(value == true || value == 1) {
        SetValue = 1;
      } else {
        SetValue = 0;
      }
      this.accountPreferences.patchValue({
        nursingAgenciesforManageMessageGroups: SetValue
      });
    } else if (preference === 'nursingAgenciesforManageEducationMaterials') {
      if(value == true) {
        SetValue = 1;
      } else {
        SetValue = 0;
      }
      this.accountPreferences.patchValue({
        nursingAgenciesforManageEducationMaterials: SetValue
      });
    }


    setTimeout(() => {
      this.reInitialiseIntroOptions(this._structureService.introJsObject ? true : false);
    })
  }

  reInitialiseIntroOptions(restart) {
    this._structureService.introSteps = [];
    this._structureService.introStepsCopy = [];

    var IntroElements = [
      'introduction-text',
      'language-translation',
      'clinical-faq',
      'enable-nurse-coming-and-delivery',
      'enable-where-is-my-delivery',
      'enable-masked-discussion-group',
      'access-enrollment-invitation-data-patient',
      'access-enrollment-invitation-data-caregiver',
      'popup-patient-chat',
      'masked-discussion-group-recipient-role',
      'escalation-time',
      'session-timeout',
      'session-timeout-warning',
      'patient-reminder-time',
      'patient-reminder-types',
      'default-user-received-escalated-message',
      'allow-patient-chat-hours',
      'no-staff-available-message',
      'patient-messaging',
      'roles-default-users',
      'user-enrollment-notification'
    ];

    for (var k in IntroElements) {
      var id = '#' + (IntroElements && IntroElements.length ? IntroElements[k] : k);
      if ($(id).length && !$(id)[0].hidden) {
        this._structureService.introSteps.push({
          element: id,
          intro: (IntroElements && IntroElements.length ? this._structureService.introJsOptions[IntroElements[k]] : this._structureService.introJsOptions[k]),
        });
        this._structureService.introStepsCopy.push({
          element: id,
          intro: (IntroElements && IntroElements.length ? this._structureService.introJsOptions[IntroElements[k]] : this._structureService.introJsOptions[k]),
        });
      }
    }

    if (restart) {
      setTimeout(() => {
        this._sharedService.startIntro.emit({
          value: true,
          step: this._structureService.introJsObject._currentStep
        });
      });
    }
  }

  toggleConversationType(status) {
    console.log(status);
    this.conversationTypeStatus = status;
  }
  getSiteId(data :any) {
    this.siteId =  data['siteId'].toString();
  }
  updateSettings(f) {
    var patientReminderTypes = [];
    var enrollmentReminderTypes = [];
    var branchDays = [];
    console.log(f);
    let validate = true;
    let folderNameArr = $('#sfilingCenterss').val();
    this.folderName = folderNameArr;

    if ((!f.valid) || (!validate)) {
      this._structureService.notifyMessage({
        messge: 'Please fill all the required fields.'
      });

      return false;
    }

    if (this.folderName) {
      this.fromFilingCenter = this.folderName;
    } else {
      this.fromFilingCenter = '';

    }

    this.accountPreferences.patchValue({
      defaultOutgoingFilingCenterChatLog: this.fromFilingCenter
    });

    if ($('#patientReminderTypes').val()) {
      $('#patientReminderTypes').val().forEach(element => {
        var types = {
          id: ""
        };
        var id = element.substr(element.indexOf(":") + 1);
        id = id.replace(/'/g, "");
        types.id = id.replace(/\s/g, '');
        patientReminderTypes.push(types);
      });

      this.accountPreferences.patchValue({
        patientReminderTypesValidate: patientReminderTypes
      });
    }

    if ($('#tenantTimeZone').val()) {
      this.accountPreferences.patchValue({
        tenantTimeZone: $('#tenantTimeZone').val()
      });
      localStorage.setItem('tenantTimezoneName', $('#tenantTimeZone').text());
    }

    if ($('#enrollmentReminderTypes').val()) {
      $('#enrollmentReminderTypes').val().forEach(element => {
        var types = {
          id: ""
        };
        var id = element.substr(element.indexOf(":") + 1);
        id = id.replace(/'/g, "");
        types.id = id.replace(/\s/g, '');
        enrollmentReminderTypes.push(types);
      });
    }

    if ($('#branchDays').val()) {
      $('#branchDays').val().forEach(element => {
        var types = {
          id: ""
        };
        var id = element.substr(element.indexOf(":") + 1);
        id = id.replace(/'/g, "");
        types.id = id.replace(/\s/g, '');
        branchDays.push(types);
      });
    }

    //console.log('updateSettings : patientReminderTypes =>', this.accountPreferences.value['patientReminderTypes'], $('#rolesChatWithUsNow').val(), patientReminderTypes);
    if (!this.accountPreferences.value['homeinfusionStart'] && !this.accountPreferences.value['patientsToInitiateChatDuringDay']) {
      this._structureService.notifyMessage({
        messge: 'Patient Chat Start Time value cannot be empty'
      });
    } else if (!this.accountPreferences.value['homeinfusionEnd'] && !this.accountPreferences.value['patientsToInitiateChatDuringDay']) {
      this._structureService.notifyMessage({
        messge: 'Patient Chat End Time value cannot be empty'
      });
    } else if (!this.isValidTime(this.accountPreferences.value['homeinfusionStart']) && !this.accountPreferences.value['patientsToInitiateChatDuringDay']) {
      this._structureService.notifyMessage({
        messge: 'Patient Chat Start Time is not valid'
      });
    } else if (!this.isValidTime(this.accountPreferences.value['homeinfusionEnd']) && !this.accountPreferences.value['patientsToInitiateChatDuringDay']) {
      this._structureService.notifyMessage({
        messge: 'Patient Chat End Time is not valid'
      });
    } else if (!this.accountPreferences.value['branchStart']) {
      this._structureService.notifyMessage({
        messge: 'Branch Start Time value cannot be empty'
      });
    } else if (!this.accountPreferences.value['branchEnd']) {
      this._structureService.notifyMessage({
        messge: 'Branch End Time value cannot be empty'
      });
    } else if (!this.isValidTime(this.accountPreferences.value['branchStart'])) {
      this._structureService.notifyMessage({
        messge: 'Branch Start Time is not valid'
      });
    } else if (!this.isValidTime(this.accountPreferences.value['branchEnd'])) {
      this._structureService.notifyMessage({
        messge: 'Branch End Time is not valid'
      });
    } else if (!patientReminderTypes.length) {
      this._structureService.notifyMessage({
        messge: 'Patient Reminder Types value cannot be empty'
      });
    } else {
      swal({
        title: "Are you sure?",
        text: "You are updating the account settings configuration",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true
      }, () => {

        this.taggedUsers = [];
        this.defaultUsers = [];

        if (this.selectedOptionstagged) {
          this.selectedOptionstagged.forEach(element => {
            this.member = {
              id: ""
            };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            this.member.id = id.replace(/\s/g, '');
            this.taggedUsers.push(this.member);
          });
        }

        this.accountPreferences.patchValue({
          taggedMessageApprover: this.taggedUsers
        });

        if (this.selectedOptions) {
          this.selectedOptions.forEach(element => {
            this.member = {
              id: ""
            };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            this.member.id = id.replace(/\s/g, '');
            this.defaultUsers.push(this.member);
          });
        }

        this.accountPreferences.patchValue({
          defaultUser: this.defaultUsers
        });

        this.selectedOptionsnotification = [];
        this.selectedOptionsnotification = $("#notificationOnErollment").val();

        if (this.selectedOptionsnotification) {
          this.selectedOptionsnotification.forEach(element => {
            this.member = {
              id: ""
            };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            this.member.id = id.replace(/\s/g, '');
            this.defaultUsersNotificationEnrollment.push(this.member);
          });
        }

        this.accountPreferences.patchValue({
          //defaultUsersNotificationEnrollment: this.defaultUsersNotificationEnrollment
        });

        //============defaultUserssmsNotificationEnrollment==============
        this.selectedOptionsnotification = [];
        this.selectedOptionsnotification = $("#smsNotificationOnErollment").val();
        if (this.selectedOptionsnotification) {
          this.selectedOptionsnotification.forEach(element => {
            this.member = {
              id: ""
            };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            this.member.id = id.replace(/\s/g, '');
            this.defaultUserssmsNotificationEnrollment.push(this.member);
          });
        }

        this.accountPreferences.patchValue({
          // defaultUserssmsNotificationEnrollment: this.defaultUserssmsNotificationEnrollment
        });

        //============defaultUsersemailNotificationEnrollment==============
        this.selectedOptionsnotification = [];
        this.selectedOptionsnotification = $("#emailNotificationOnErollment").val();
        if (this.selectedOptionsnotification) {
          this.selectedOptionsnotification.forEach(element => {
            this.member = {
              id: ""
            };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            this.member.id = id.replace(/\s/g, '');
            this.defaultUsersemailNotificationEnrollment.push(this.member);
          });
        }
        this.accountPreferences.patchValue({
          // defaultUsersemailNotificationEnrollment: this.defaultUsersemailNotificationEnrollment
        });

        const formObjData = this.accountPreferences.value;
        console.log(formObjData);

        formObjData.defaultStaffRole = $('#defaultStaffRole').val();
        formObjData.defaultPartnerRole = $('#defaultPartnerRole').val();
        formObjData.defaultPartnerCategory = $('#defaultPartnerCategory').val();

        console.log(formObjData);

        if (this.account['patientsToInitiateChatDuringDay']) {
          formObjData.noClinicianMessageDuringWorkingHours = this.account['noClinicianMessageDuringWorkingHours'];
          formObjData.noClinicianMessages = this.accountPreferences.value['noClinicianMessage'];

        } else {
          formObjData.noClinicianMessages = this.account['noClinicianMessage'];
          formObjData.noClinicianMessageDuringWorkingHours = this.account['noClinicianMessageDuringWorkingHours'];
        }

        if (this.account['fileSavingFormatChatLogFilingCenter']) {
          formObjData.fileSavingFormatChatLogFilingCenter = this.account['fileSavingFormatChatLogFilingCenter'];
        } else {
          formObjData.fileSavingFormatChatLogFilingCenter = '{initiator}-{downloadOn}';
        }

        var rolesChatWithUsNow = [];
        var rolesWhenNurseArrive = [];
        var rolesWhereIsMyDelivery = [];
        var rolesIhaveQuestion = [];
        var rolesDefaultNurses = [];
        var chatRole1 = [];
        var chatRole2 = [];
        var partnerReferralFormTag = [];
        var maskedDiscussionGroupReceiptRole = [];
        var defaultStaffTags = [];
        var defaultPartnerTags = [];
        

        if (this.multiSiteEnable) {
          console.log(typeof this.siteId);
          formObjData.defaultSites = (this.siteId !="0") ? this.siteId : "";
          console.log(formObjData.defaultSites + 'defautl Site ');
        }
        if ($('#defaultStaffTags').val()) {
          $('#defaultStaffTags').val().forEach(element => {
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            id = id.replace(/\s/g, '');
            defaultStaffTags.push(id);
          });
        }

        formObjData.defaultStaffTags = defaultStaffTags.join(',');

        if ($('#defaultPartnerTags').val()) {
          $('#defaultPartnerTags').val().forEach(element => {
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            id = id.replace(/\s/g, '');
            defaultPartnerTags.push(id);
          });
        }

        formObjData.defaultPartnerTags = defaultPartnerTags.join(',');

        if ($('#rolesChatWithUsNow').val()) {
          $('#rolesChatWithUsNow').val().forEach(element => {
            var member = {
              id: ""
            };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
            rolesChatWithUsNow.push(member);
          });
        }

        if ($('#rolesWhenNurseArrive').val()) {
          $('#rolesWhenNurseArrive').val().forEach(element => {
            var member = {
              id: ""
            };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
            rolesWhenNurseArrive.push(member);
          });
        }

        if ($('#rolesWhereIsMyDelivery').val()) {
          $('#rolesWhereIsMyDelivery').val().forEach(element => {
            var member = {
              id: ""
            };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
            rolesWhereIsMyDelivery.push(member);
          });
        }

        if ($('#rolesIhaveQuestion').val()) {
          $('#rolesIhaveQuestion').val().forEach(element => {
            var member = {
              id: ""
            };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
            rolesIhaveQuestion.push(member);
          });
        }

        if ($('#rolesDefaultNurses').val()) {
          $('#rolesDefaultNurses').val().forEach(element => {
            var member = {
              id: ""
            };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
            rolesDefaultNurses.push(member);
          });
        }

        if ($('#chatRole1').val()) {
          $('#chatRole1').val().forEach(element => {
            var member = {
              id: ""
            };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
            chatRole1.push(member);
          });
        }

        if ($('#partnerReferralFormTag').val()) {
          $('#partnerReferralFormTag').val().forEach(element => {
            var member = {
              id: ""
            };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
            partnerReferralFormTag.push(member);
          });
        }

        if ($('#maskedDiscussionGroupReceiptRole').val()) {
          $('#maskedDiscussionGroupReceiptRole').val().forEach(element => {
            var member = {
              id: ""
            };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
            maskedDiscussionGroupReceiptRole.push(member);
          });
        }    

        if ($('#chatRole2').val()) {
          $('#chatRole2').val().forEach(element => {
            var member = {
              id: ""
            };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
            chatRole2.push(member);
          });
        }

        var homeInfusionStartTimeHours;
        var homeInfusionStartTimeMinutes;
        var homeInfusionEndTimeHours;
        var homeInfusionEndtTimeMinutes;
        var homeinfusionEndSave;
        var homeinfusionStartSave;
        let tenantTimeZone = this.tenantTimeZoneData.offset;
        tenantTimeZone = tenantTimeZone.split(',')[0];
        tenantTimeZone = (((tenantTimeZone) * -1) / 60) * -1;
        let timeToPassDay = [];
        let timeDifference;

        if (tenantTimeZone > this.timezone) {
          timeDifference = -1 * Math.abs(this.timezone - tenantTimeZone);
        } else {
          timeDifference = Math.abs(this.timezone - tenantTimeZone);
        }

        if (((timeDifference) + '').split('.').length == 2) {
          timeToPassDay = ((timeDifference) + '').split('.');
        } else {
          timeToPassDay = ((timeDifference) + '').split('.');
          timeToPassDay.push("0");
        }

        if (timeToPassDay[1] == '5') {
          if (timeDifference < 0) {
            timeToPassDay[1] = '-30';
          } else {
            timeToPassDay[1] = '30';
          }
        } else {
          timeToPassDay[1] = '0';
        }

        if (timeToPassDay[0] == '-0') {
          timeToPassDay[0] = '0';
        }

        timeToPassDay = timeToPassDay.map(function (time) {
          return parseInt(time, 10);
        });

        console.log('timeToPassDay--->', timeToPassDay);

        if (this.accountPreferences.value['homeInfusionStartTime'] && this.accountPreferences.value['homeInfusionStartTime'] == 24) {
          if (this.accountPreferences.value['homeinfusionStart'] && this.accountPreferences.value['homeinfusionStart'].indexOf(":") !== -1) {
            homeInfusionStartTimeHours = this.accountPreferences.value['homeinfusionStart'].split(":")[0] * 1;
            homeInfusionStartTimeMinutes = this.accountPreferences.value['homeinfusionStart'].split(":")[1];
            homeInfusionStartTimeHours = homeInfusionStartTimeHours != 12 ? homeInfusionStartTimeHours + 12 : 12;
            homeinfusionStartSave = homeInfusionStartTimeHours + ":" + homeInfusionStartTimeMinutes;
          }
        } else {
          if (this.accountPreferences.value['homeinfusionStart'] && this.accountPreferences.value['homeinfusionStart'].indexOf(":") !== -1) {
            homeInfusionStartTimeHours = this.accountPreferences.value['homeinfusionStart'].split(":")[0] * 1;
            homeInfusionStartTimeMinutes = this.accountPreferences.value['homeinfusionStart'].split(":")[1];
            if (homeInfusionStartTimeHours == 12) {
              homeInfusionStartTimeHours = 0;
            }
            homeinfusionStartSave = homeInfusionStartTimeHours + ":" + homeInfusionStartTimeMinutes;
          }
        }

        if (this.accountPreferences.value['homeinfusionEndTime'] && this.accountPreferences.value['homeinfusionEndTime'] == 24) {
          if (this.accountPreferences.value['homeinfusionEnd'] && this.accountPreferences.value['homeinfusionEnd'].indexOf(":") !== -1) {
            homeInfusionEndTimeHours = this.accountPreferences.value['homeinfusionEnd'].split(":")[0] * 1;
            homeInfusionEndtTimeMinutes = this.accountPreferences.value['homeinfusionEnd'].split(":")[1];
            homeInfusionEndTimeHours = homeInfusionEndTimeHours != 12 ? homeInfusionEndTimeHours + 12 : 12;
            homeinfusionEndSave = homeInfusionEndTimeHours + ":" + homeInfusionEndtTimeMinutes;
          }
        } else {
          if (this.accountPreferences.value['homeinfusionEnd'] && this.accountPreferences.value['homeinfusionEnd'].indexOf(":") !== -1) {
            homeInfusionEndTimeHours = this.accountPreferences.value['homeinfusionEnd'].split(":")[0] * 1;
            homeInfusionEndtTimeMinutes = this.accountPreferences.value['homeinfusionEnd'].split(":")[1];
            
            if (homeInfusionEndTimeHours == 12) {
              homeInfusionEndTimeHours = 0;
            }

            homeinfusionEndSave = homeInfusionEndTimeHours + ":" + homeInfusionEndtTimeMinutes;
          }
        }
        if (this.accountPreferences.value['homeinfusionStart'] && this.accountPreferences.value['homeinfusionEnd']) {
          // homeinfusionStartSave = this._structureService.clientToGmtTime(homeinfusionStartSave, false, undefined, undefined);
          // homeinfusionEndSave = this._structureService.clientToGmtTime(homeinfusionEndSave, false, undefined, undefined);
          var start_time = homeinfusionStartSave;
          var end_time = homeinfusionEndSave;
          console.log(start_time, end_time);
          if ((start_time.split(':')[0] * 1) < 10) {
            start_time = '0' + start_time.split(':')[0] + ':' + start_time.split(':')[1];
          }

          if ((end_time.split(':')[0] * 1) < 10) {
            end_time = '0' + end_time.split(':')[0] + ':' + end_time.split(':')[1];
          }

          homeinfusionStartSave = moment(moment().format('YYYY-MM-DD') + ' ' + start_time).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('HH:mm');
          homeinfusionEndSave = moment(moment().format('YYYY-MM-DD') + ' ' + end_time).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('HH:mm');
          console.log(homeinfusionStartSave, homeinfusionEndSave);
        }

        /* */
        var branchStartTimeHours;
        var branchStartTimeMinutes;
        var branchEndTimeHours;
        var branchEndtTimeMinutes;
        var branchEndSave;
        var branchStartSave;
        var branchStartValue;
        var branchEndValue;
        var chatStart;
        var chatStartTime;
        var chatEnd;
        var chatEndTime;
        let branchStart =  this.accountPreferences.value['branchStartTime'] == 12 ? "AM" : "PM";
        let branchEnd =  this.accountPreferences.value['branchEndTime'] == 12 ? "AM" : "PM";
        branchStartValue = this.accountPreferences.value['branchStart']+" "+branchStart;
        branchEndValue = this.accountPreferences.value['branchEnd']+" "+branchEnd;
        chatStart =  this.accountPreferences.value['homeInfusionStartTime'] == 12 ? "AM" : "PM";
        chatStartTime = this.accountPreferences.value['homeinfusionStart'];
        chatEnd =  this.accountPreferences.value['homeinfusionEndTime'] == 12 ? "AM" : "PM";
        chatEndTime = this.accountPreferences.value['homeinfusionEnd'];
        
        
        if (this.accountPreferences.value['branchStartTime'] && this.accountPreferences.value['branchStartTime'] == 24) {
          if (this.accountPreferences.value['branchStart'] && this.accountPreferences.value['branchStart'].indexOf(":") !== -1) {
            branchStartTimeHours = this.accountPreferences.value['branchStart'].split(":")[0] * 1;
            branchStartTimeMinutes = this.accountPreferences.value['branchStart'].split(":")[1];
            branchStartTimeHours = branchStartTimeHours != 12 ? branchStartTimeHours + 12 : 12;
            branchStartSave = branchStartTimeHours + ":" + branchStartTimeMinutes;
          }
        } else {
          if (this.accountPreferences.value['branchStart'] && this.accountPreferences.value['branchStart'].indexOf(":") !== -1) {
            branchStartTimeHours = this.accountPreferences.value['branchStart'].split(":")[0] * 1;
            branchStartTimeMinutes = this.accountPreferences.value['branchStart'].split(":")[1];
            if (branchStartTimeHours == 12) {
              branchStartTimeHours = 0;
            }
            branchStartSave = branchStartTimeHours + ":" + branchStartTimeMinutes;
          }
        }

        if (this.accountPreferences.value['branchEndTime'] && this.accountPreferences.value['branchEndTime'] == 24) {
          if (this.accountPreferences.value['branchEnd'] && this.accountPreferences.value['branchEnd'].indexOf(":") !== -1) {
            branchEndTimeHours = this.accountPreferences.value['branchEnd'].split(":")[0] * 1;
            branchEndtTimeMinutes = this.accountPreferences.value['branchEnd'].split(":")[1];
            branchEndTimeHours = branchEndTimeHours != 12 ? branchEndTimeHours + 12 : 12;
            branchEndSave = branchEndTimeHours + ":" + branchEndtTimeMinutes;
          }
        } else {
          if (this.accountPreferences.value['branchEnd'] && this.accountPreferences.value['branchEnd'].indexOf(":") !== -1) {
            branchEndTimeHours = this.accountPreferences.value['branchEnd'].split(":")[0] * 1;
            branchEndtTimeMinutes = this.accountPreferences.value['branchEnd'].split(":")[1];
            if (branchEndTimeHours == 12) {
              branchEndTimeHours = 0;
            }
            branchEndSave = branchEndTimeHours + ":" + branchEndtTimeMinutes;
          }
        }

        // branchStartSave = this._structureService.clientToGmtTime(branchStartSave, false, undefined, undefined);
        // branchEndSave = this._structureService.clientToGmtTime(branchEndSave, false, undefined, undefined);
        var start_time = branchStartSave;
        var end_time = branchEndSave;
        console.log(start_time, end_time);

        if ((start_time.split(':')[0] * 1) < 10) {
          start_time = '0' + start_time.split(':')[0] + ':' + start_time.split(':')[1];
        }

        if ((end_time.split(':')[0] * 1) < 10) {
          end_time = '0' + end_time.split(':')[0] + ':' + end_time.split(':')[1];
        }

        branchStartSave = moment(moment().format('YYYY-MM-DD') + ' ' + start_time).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('HH:mm');
        branchEndSave = moment(moment().format('YYYY-MM-DD') + ' ' + end_time).add(timeToPassDay[0], 'hours').add(timeToPassDay[1], 'm').format('HH:mm');
        console.log(branchStartSave, branchEndSave);
        /* */
        var messageDestinationConfig = {}
        var siteConfig ={}
        if (!this.accountPreferences.value['patientsToInitiateChatDuringDay']) {
          messageDestinationConfig = {
            chatwithUsNowRolesAfterDay: [],
            chatwithUsNowRolesDuringDay: rolesChatWithUsNow,
            iNeedHelpOrIHaveAQuestionRolesAfterDay: [],
            iNeedHelpOrIHaveAQuestionRolesDuringDay: rolesIhaveQuestion,
            whenWillMyNurseArriveRolesAfterDay: [],
            whenWillMyNurseArriveRolesDuringDay: rolesWhenNurseArrive,
            whereIsMyDeliveryRolesAfterDay: [],
            whereIsMyDeliveryRolesDuringDay: rolesWhereIsMyDelivery,
            defaultClinicianRolesDuringDay: rolesDefaultNurses,
            defaultClinicianRolesAfterDay: [],
            startTime: homeinfusionStartSave,
            endTime: homeinfusionEndSave,
            branchStartTime: branchStartSave,
            branchEndTime: branchEndSave
          }
        } else {
          messageDestinationConfig = {
            chatwithUsNowRolesAfterDay: rolesChatWithUsNow,
            chatwithUsNowRolesDuringDay: [],
            iNeedHelpOrIHaveAQuestionRolesAfterDay: rolesIhaveQuestion,
            iNeedHelpOrIHaveAQuestionRolesDuringDay: [],
            whenWillMyNurseArriveRolesAfterDay: rolesWhenNurseArrive,
            whenWillMyNurseArriveRolesDuringDay: [],
            whereIsMyDeliveryRolesAfterDay: rolesWhereIsMyDelivery,
            whereIsMyDeliveryRolesDuringDay: [],
            defaultClinicianRolesAfterDay: rolesDefaultNurses,
            defaultClinicianRolesDuringDay: [],
            startTime: homeinfusionStartSave,
            endTime: homeinfusionEndSave,
            branchStartTime: branchStartSave,
            branchEndTime: branchEndSave
          }
        }
        if(this.userData.config.enable_multisite != 1){
          siteConfig = {     
            branchStartTime: branchStartValue,
            branchEndTime: branchEndValue,
            chatStartTime:chatStartTime,
            chatEndTime:chatEndTime,
            chatEnd:chatEnd,
            chatStart:chatStart,
          }
          formObjData.siteConfig = siteConfig;
        }
        formObjData.messageDestinationConfig = messageDestinationConfig;
        formObjData.chatRole1 = chatRole1;
        formObjData.partnerReferralFormTag = partnerReferralFormTag;
        formObjData.chatRole2 = chatRole2;
        formObjData.maskedDiscussionGroupReceiptRole = maskedDiscussionGroupReceiptRole;           
        formObjData.patientReminderTypesValidate = patientReminderTypes;
        formObjData.enrollmentReminderTypes = enrollmentReminderTypes;
        formObjData.branchDays = branchDays;
        formObjData.patientReminderTime = formObjData.patientReminderTime * 60;
        

        let fileSavingValue = '';
        if ($('#fileSavingFormatChatLogFilingCenter').val() != '') {
          fileSavingValue = $('#fileSavingFormatChatLogFilingCenter').val();
        } else {
          fileSavingValue = '{initiator}-{downloadOn}';
        }

        formObjData.fileSavingFormatChatLogFilingCenter = fileSavingValue;

        this.accountPreferences.patchValue({
          fileSavingFormatChatLogFilingCenter: fileSavingValue
        });

        //console.log("formObjData : ",formObjData, chatRole1 , chatRole2);
        this.isDisabled = true;

        var updatedBy = {
          displayName: this.userData['displayName'],
          userid: this.userData.userId ? this.userData.userId : 0,
        };

        console.log("loginUserDetails- ", this.userData, updatedBy);
        var self = this;
        //formObjData.partnerPatientReferralIntroductiontext="ssssssssss";
        console.log("formObjData=============================");
        console.log("formObjData=============================");
        console.log("formObjData=============================");
        console.log(formObjData);
        console.log($("#partnerPatientReferralIntro").html() + "=====*************")
        console.log("formObjData=============================");
        console.log("formObjData=============================");
        console.log("formObjData=============================");

        this._structureService.updatePreferences(formObjData).then((data) => {
          this.isDisabled = false;
          //var notify = $.notify('Success! Account preferences & features updated');
          setTimeout(() => {
            //notify.update({ 'type': 'success', 'message': '<strong>Success! Account preferences & features updated</strong>' });
            var notify = $.notify('Success! Account Settings updated');
            setTimeout(function () {
              notify.update({
                'type': 'success',
                'message': '<strong>Success! Account Settings updated</strong>'
              });
            }, 500);
            const activityData = {
              activityName: 'Account Settings Update',
              activityType: "Account Settings",
              activityDescription: `Account Settings updated by `+JSON.stringify(updatedBy) +`the new data is `+JSON.stringify(formObjData) +``,
            };
            self._structureService.trackActivity(activityData);
            var updateConfigPollingtoServer = {
              configurationType: "updateTenant",
              role: 0,
              tenantid: (self._structureService.getCookie('crossTenantId') && self._structureService.getCookie('crossTenantId') != 'undefined' && self._structureService.getCookie('tenantId') !== self._structureService.getCookie('crossTenantId')) ? self._structureService.getCookie('crossTenantId') : self._structureService.getCookie('tenantId'),
              updatedBy: updatedBy,
            };
            self._structureService.socket.emit("updateConfigPollingtoServer", updateConfigPollingtoServer);

            if(this.account.masterEnabled && this.account.isMaster) {
              var updateMasterConfigPollingtoServer = {
                configurationType: "updateMaster",
                role: 0,
                tenantid: (self._structureService.getCookie('crossTenantId') && self._structureService.getCookie('crossTenantId') != 'undefined' && self._structureService.getCookie('tenantId') !== self._structureService.getCookie('crossTenantId')) ? self._structureService.getCookie('crossTenantId') : self._structureService.getCookie('tenantId'),
                updatedBy: updatedBy,
              };
              self._structureService.socket.emit("updateMasterConfigPollingtoServer", updateMasterConfigPollingtoServer);
            }
            // this.updateGeneralSettings();
          }, 1000);

          var arrayOfElements = [
            'introduction-text',
            'language-translation',
            'clinical-faq',
            'enable-nurse-coming-and-delivery',
            'enable-where-is-my-delivery',
            'enable-masked-discussion-group',
            'access-enrollment-invitation-data-patient',
            'access-enrollment-invitation-data-caregiver',
            'popup-patient-chat',
            'masked-discussion-group-recipient-role',
            'escalation-time',
            'session-timeout',
            'session-timeout-warning',
            'patient-reminder-time',
            'patient-reminder-types',
            'default-user-received-escalated-message',
            'allow-patient-chat-hours',
            'no-staff-available-message',
            'patient-messaging',
            'roles-default-users',
            'user-enrollment-notification'
          ];

          setTimeout(() => {
            this._structureService.setIntroOptions('account,account-settings', arrayOfElements, true, (conditionalParams) => {
              if (conditionalParams.step) {
                this._sharedService.startIntroCallBack.emit({
                  value: true,
                  step: conditionalParams.step
                });
              } else {
                this._sharedService.startIntroCallBack.emit({
                  value: true
                });
              }

              this._structureService.introJsObject.oncomplete(() => {
                this._structureService.introJsObject.exit();
                delete this._structureService.introJsObject;
              });

              this._structureService.introJsObject.onexit(() => {
                delete this._structureService.introJsObject;
                delete this._structureService.introSteps;
              });
            });
          });
        });
      });
    }
  }

  updateGeneralSettings() {
    console.log("formObjData=============================");
    console.log("formObjData=============================");
    console.log("formObjData=============================");
    console.log(this.accountPreferences.value);
    console.log("formObjData=============================");
    console.log("formObjData=============================");
    console.log("formObjData=============================");
    /* swal({
       title: "Are you sure?",
       text: "You are updating the general settings configuration",
       type: "warning",
       showCancelButton: true,
       cancelButtonClass: "btn-default",
       confirmButtonClass: "btn-warning",
       confirmButtonText: "Ok",
       closeOnConfirm: true
     }, () => {*/
    console.log('#####################################################');

    if (this.selectedOptions) {
      this.selectedOptions.forEach(element => {
        this.member = {
          id: ""
        };
        var id = element.substr(element.indexOf(":") + 1);
        id = id.replace(/'/g, "");
        this.member.id = id.replace(/\s/g, '');
        this.defaultUsers.push(this.member);
      });
    }

    this.accountPreferences.patchValue({
      defaultUser: this.defaultUsers
    });

    let formObjData = this.accountPreferences.value;

    if (this.account['patientsToInitiateChatDuringDay']) {
      formObjData.noClinicianMessageDuringWorkingHours = this.account['noClinicianMessageDuringWorkingHours'];
      formObjData.noClinicianMessages = this.accountPreferences.value['noClinicianMessage'];
    } else {
      formObjData.noClinicianMessages = this.account['noClinicianMessage'];
      formObjData.noClinicianMessageDuringWorkingHours = this.accountPreferences.value['noClinicianMessageDuringWorkingHours'];
    }

    console.log("formObjData=============================");
    console.log("formObjData=============================");
    console.log("formObjData=============================");
    console.log(formObjData);
    console.log("formObjData=============================");
    console.log("formObjData=============================");
    console.log("formObjData=============================");

    var updatedBy = {
      displayName: this.userData.displayName,
      userid: this.userData.userId ? this.userData.userId : 0,
    };

    console.log("loginUserDetails- ", this.userData, updatedBy);

    var updateConfigPollingtoServer = {
      configurationType: "updateTenant",
      role: 0,
      tenantid: (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') != 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this._structureService.getCookie('tenantId'),
      updatedBy: updatedBy,
    };

    var self = this;

    if (this.pageType === 'edit') {
      this.isDisabled = true;
      this._structureService.updateAccount(formObjData).then((data) => {
          this.isDisabled = false;
          var notify = $.notify('Success! Account Settings updated');
          setTimeout(function () {
            notify.update({
              'type': 'success',
              'message': '<strong>Success! Account Settings updated</strong>'
            });
            self._structureService.socket.emit("updateConfigPollingtoServer", updateConfigPollingtoServer);
          }, 1000);
        }
      );
    } else {
      this.isDisabled = true;
      this._structureService.createAccount(formObjData).subscribe((data) => {
          this.isDisabled = false;
          var notify = $.notify('Success! Account Settings updated');
          setTimeout(function () {
            notify.update({
              'type': 'success',
              'message': '<strong>Success! Account Settings updated</strong>'
            });
          }, 1000);
        }
      );
    }
    // });
  }
  showTab(data){
      this.optionTab = data;
      if(this.optionTab == 'message'){
        var activityData = {
            activityName: "Account setting Tab switching",
            activityType: "Configuration",
            activityDescription: this.userData.displayName + " has selected tab messages from tab General"
         }; 
        this._structureService.trackActivity(activityData);
      }
     this.ngOnInit();
  }
  hideDropdown(hideItem: any) {
    this.showSiteSelection = hideItem.hideItem;
}
}
