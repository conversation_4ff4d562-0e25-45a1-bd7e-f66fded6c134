<!-- START: forms/basic-forms-elements -->
<style>
    .adjust-content{
      padding-top: 36px;
    }  
   .chatwith-model-head-pah{
    background: #fff;
    padding: 10px;
    width: 13%;
    text-align: center;
}
.cat__apps__messaging__tab_pah--selected{
    background: #9ea5a4 !important;
    cursor: pointer;
    color:white;
}
.tab-title {
        border-bottom: 1px solid #d6d6d6;
        cursor: pointer;
    }
  </style>
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong data-intro id="introduction-text">Account Settings</strong>
            <!-- <a href="https://v4-alpha.getbootstrap.com/components/forms/" target="_blank" class="btn btn-sm btn-primary ml-2">Official Documentation <i class="icmn-link ml-1"></i></a> -->
        </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item">Account Settings</li>
        </ol>
        <div class="main-tabs ">
            <div class="chatwith-modal-tab tab-title">
                <div class="chatwith-model-head-pah" (click)="showTab('general')"
                    [class.cat__apps__messaging__tab_pah--selected]="optionTab=='general'">General</div>
                <div class="chatwith-model-head-pah" (click)="showTab('message')"
                    [class.cat__apps__messaging__tab_pah--selected]="optionTab=='message'">Messages</div>

            </div>
        </div>
        <div class="col-lg-12 adjust-content" *ngIf="optionTab==='general'">
            <div class="mb-5">

                <form [hidden]="dataLoadingMsg" action="#" (ngSubmit)="updateSettings(f)" #f="ngForm" [formGroup]="accountPreferences">
                    <div class="form-body">
                        <div class="row">
                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('sessionTimeoutInSec')== -1">
                                <label class="control-label col-md-10">Account Name * </label>
                                <div class="col">
                                    <input type="text" id="lastName" class="form-control" placeholder="Account Name" formControlName="name" [(ngModel)]="account.name">
                                    <div *ngIf="accountPreferences.controls['name'].errors&&(accountPreferences.controls.name.dirty ||accountPreferences.controls.name.touched || f.submitted)" class="alert alert-danger">
                                        Account Name cannot be empty
                                    </div>
                                </div>
                            </div>
                            <div class="no-padding form-group col-md-6">
                                <label class="control-label col-md-10">Account Time Zone
                                    <!--i chToolTip="PREFET0023"></i-->
                                </label>
                                <div class="col">
                                    <select class="form-control select2" formControlName="tenantTimeZone" id="tenantTimeZone" data-placeholder="None Selected">
                                        <option *ngFor="let timezone of timezones" value="{{timezone.offset}}"> {{timezone.name}} </option>
                                    </select>
                                    <div *ngIf="accountPreferences.controls['tenantTimeZone'].errors && (accountPreferences.controls.tenantTimeZone?.dirty || accountPreferences.controls.tenantTimeZone?.touched || f.submitted)" class="alert alert-danger">
                                        Account Time Zone is required
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <h5 class="text-black"><strong>Preferences</strong></h5>
                    <div class="form-actions"></div>

                    <div class="form-body">
                        <div class="row">
                            <!-- added from general setting start -->
                            <div *ngIf="previlages.superAdmin" class="form-group row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Registration ID *</label>
                                        <input *ngIf="account.registrationId != null" type="text" id="firstName" class="form-control" placeholder="Registration ID" formControlName="registrationId" [(ngModel)]="account.registrationId">
                                        <input *ngIf="account.registrationId == null" type="text" id="firstName" class="form-control" placeholder="Registration ID" formControlName="registrationId">
                                        <div *ngIf="accountPreferences.controls['registrationId'].hasError('required')&&(accountPreferences.controls.registrationId?.dirty ||accountPreferences.controls.registrationId?.touched || f.submitted)" class="alert alert-danger">
                                            {{errorMessageRegId}}
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Account Key *</label>
                                        <input type="text" id="firstName" class="form-control" placeholder="Account Key" formControlName="tenantKey" [(ngModel)]="account.key">
                                        <div *ngIf="accountPreferences.controls['tenantKey'].hasError('required')&&(accountPreferences.controls.tenantKey?.dirty ||accountPreferences.controls.tenantKey?.touched || f.submitted)" class="alert alert-danger">
                                            {{errorMessageAccountKey}}
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <!-- <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('sessionTimeoutInSec')== -1">
                              <label class="control-label col-md-10">Account Name * </label>
                              <div class="col">                              
                                  <input type="text" id="lastName" class="form-control" placeholder="Account Name" formControlName="name" [(ngModel)]="account.name">
                                  <div *ngIf="accountPreferences.controls['name'].errors&&(accountPreferences.controls.name?.dirty ||accountPreferences.controls.name?.touched)"
                                      class="alert alert-danger">
                                      Account Name cannot be empty
                                  </div>
                               </div>
                              
                          </div> -->


                            <!-- added from general setting end -->

                            <div class="no-padding form-group col-md-6" [hidden]="hidePreferences.indexOf('messageForwardBehavior') != -1">
                                <label class="control-label col-md-10">Message Forwarding Behavior
                                        <i chToolTip="PREFET0003"></i></label>

                                <div class="col">
                                    <select class="form-control" formControlName="messageForwardBehavior"> Select 
                                            <option value="Forwarding_not_allowed"> Select </option>
                                             <option *ngFor="let behaviour of messageForwardBehaviorObject" value="{{behaviour.value}}" >{{behaviour.text}}</option>

                                         </select>
                                </div>
                            </div>

                            <!--/span-->
                            <!-- <div class="no-padding form-group col-md-6 tagged-message-approver" [hidden]="!accountPreferences.controls['messageTagging'].value ||(hidePreferences.indexOf('messageTagging') != -1)">

                                <label class="control-label col-md-10">Tagged Message Approver
                                    <i chToolTip="PREFET0004"></i></label>
                                <div class="col">
                                    <select class="form-control select2" data-placeholder="None Selected" id="taggedUser" multiple> Select
                                             <option *ngFor="let user of selectedUserList"  value="{{user.id}}" [selected]="user.status ==true"   >{{user.displayName}} </option>

                                        </select>
                                </div>

                            </div> -->
                            <!--/span-->

                        </div>
                        <div class="row">

                            <div data-intro id="language-translation" class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('chatAutoTranslate')==-1">
                                <label class="control-label col-md-10">Language Translation
                                <i chToolTip="PREFET0005"></i></label>


                                <div class="btn-group col-md-3">

                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['chatAutoTranslate'].value}" (click)="togglePreference('chatAutoTranslate',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['chatAutoTranslate'].value}" (click)="togglePreference('chatAutoTranslate',false)">
                                                              No
                                         </button>

                                </div>

                            </div>

                            <!-- <div data-intro id="clinical-faq" class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('infusionSupport')==-1 && userData.tenantId<64">

                                <label class="control-label col-md-10">Show Clinical FAQs
                                <i chToolTip="PREFET0006"></i></label>


                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['chatInfusionSupport'].value}" (click)="togglePreference('chatInfusionSupport',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['chatInfusionSupport'].value}" (click)="togglePreference('chatInfusionSupport',false)">
                                                              No
                                         </button>

                                </div>


                            </div>

                            <div data-intro id="enable-nurse-coming-and-delivery" class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('enableWhenIsMyNurseComing')==-1 && userData.tenantId<64">
                                <label class="control-label col-md-10">Enable When Is My Nurse Coming
                                <i chToolTip="PREFET0017"></i></label>
                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['chatEnableWhenIsMyNurseComing'].value}" (click)="togglePreference('chatEnableWhenIsMyNurseComing',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['chatEnableWhenIsMyNurseComing'].value}" (click)="togglePreference('chatEnableWhenIsMyNurseComing',false)">
                                                              No
                                         </button>
                                </div>
                            </div>
                            <div data-intro id="enable-where-is-my-delivery" class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('enableWhereIsMyDelivery')==-1 && userData.tenantId<64">
                                <label class="control-label col-md-10">Enable Where Is My Delivery
                                <i chToolTip="PREFET0018"></i></label>
                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['chatEnableWhereIsMyDelivery'].value}" (click)="togglePreference('chatEnableWhereIsMyDelivery',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['chatEnableWhereIsMyDelivery'].value}" (click)="togglePreference('chatEnableWhereIsMyDelivery',false)">
                                                              No
                                         </button>
                                </div>
                            </div> -->

                            <div class="no-padding form-group  col-md-6" *ngIf="hidePreferences.indexOf('gATracking')==-1">

                                <label class="control-label  col-md-8">GA Tracking
                                <i chToolTip=""></i></label>


                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['gaTracking'].value}" (click)="togglePreference('gATracking',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['gaTracking'].value}" (click)="togglePreference('gaTracking',false)">
                                                              No
                                         </button>
                                </div>
                            </div>
                            <div class="no-padding form-group  col-md-6" *ngIf="hidePreferences.indexOf('watchTowerTracking') == -1">

                                <label class="control-label col-md-10">Watchtower Tracking
                                <i chToolTip=""></i></label>




                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['watchtowerTracking'].value}" (click)="togglePreference('watchtowerTracking',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['watchtowerTracking'].value}" (click)="togglePreference('watchtowerTracking',false)">
                                                              No
                                         </button>
                                </div>

                            </div>

                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('showPrototypes')== -1">
                                <label class="control-label col-md-10">Show Prototypes
                                <i chToolTip=""></i></label>



                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['showProtypes'].value}" (click)="togglePreference('showProtypes',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['showProtypes'].value}" (click)="togglePreference('showProtypes',false)">
                                                              No
                                         </button>
                                </div>

                            </div>
                            <!--<div  class="form-group col-md-6" >
                                <label class="control-label col-md-10">Show Infusion Support
                                <i chToolTip="PREFET0006"></i></label>
                            

                               
                                    <div class="btn-group col-md-2">
                                        <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['showInfusionSupport'].value}"
                                            (click)="togglePreference('showInfusionSupport',true)">
                                                            Yes
                                            </button>
                                        <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['showInfusionSupport'].value}"
                                            (click)="togglePreference('showInfusionSupport',false)">
                                                                No
                                            </button>
                                    </div>
                                
                            </div>-->


                            <!-- <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('messageTagging')==-1">
                                <label class="control-label col-md-10">Message Tagging
                                        <i chToolTip="PREFET0015"></i>   </label>



                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['messageTagging'].value}" (click)="togglePreference('messageTagging',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['messageTagging'].value}" (click)="togglePreference('messageTagging',false)">
                                                              No
                                         </button>
                                </div>

                            </div> -->
                            <div [hidden]="true" class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('userTagging')==-1">
                                <label class="control-label col-md-10">User Tagging
                                    <i class="user-tagging icmn-info" ></i> </label>



                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['userTagging'].value}" (click)="togglePreference('userTagging',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['userTagging'].value}" (click)="togglePreference('userTagging',false)">
                                                              No
                                         </button>
                                </div>

                            </div>



                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('documentTagging')==-1">
                                <label class="control-label col-md-10">Enable Signature Request
                                <i chToolTip="PREFET0009"></i></label>



                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['documentTagging'].value}" (click)="togglePreference('documentTagging',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['documentTagging'].value}" (click)="togglePreference('documentTagging',false)">
                                                              No
                                         </button>
                                </div>

                            </div>
                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('showChatHistoryToNewParticipant')==-1">
                                <label class="control-label col-md-10">Show Chat History To New Group Member
                                <i chToolTip="PREFET0010"></i></label>



                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['showChatHistoryToNewParticipant'].value}" (click)="togglePreference('showChatHistoryToNewParticipant',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['showChatHistoryToNewParticipant'].value}" (click)="togglePreference('showChatHistoryToNewParticipant',false)">
                                                              No
                                         </button>
                                </div>

                            </div>
                            <!-- <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('enableFilingCenter')==-1">
                                <label class="control-label col-md-10">Enable Filing Center
                                </label>



                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['enableFilingCenter'].value}" (click)="togglePreference('enableFilingCenter',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['enableFilingCenter'].value}" (click)="togglePreference('enableFilingCenter',false)">
                                                              No
                                         </button>
                                </div>

                            </div> -->
                            <div data-intro id="enable-masked-discussion-group" class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('enableMaskedDiscussionGroup')==-1">
                                <label class="control-label col-md-10">Enable Masked Discussion Group <i chToolTip="PREFET0019"></i>
                                </label>



                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['enableMaskedDiscussionGroup'].value}" (click)="togglePreference('enableMaskedDiscussionGroup',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['enableMaskedDiscussionGroup'].value}" (click)="togglePreference('enableMaskedDiscussionGroup',false)">
                                                              No
                                         </button>
                                </div>

                            </div>
                            <!--========================================-->
                            <!-- <div data-intro id="access-enrollment-invitation-data-patient" class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('allowPrefillingPartialEnrollment')==-1">
                                <label class="control-label col-md-10">  Allow User to Choose Whether a Patient Sees Prepopulated Information in Their Enrollment Invitation Link
                                </label>



                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['allowPrefillingPartialEnrollment'].value}" (click)="togglePreference('allowPrefillingPartialEnrollment',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['allowPrefillingPartialEnrollment'].value}" (click)="togglePreference('allowPrefillingPartialEnrollment',false)">
                                                              No
                                         </button>
                                </div>

                            </div> -->
                            <!-- <div data-intro id="access-enrollment-invitation-data-caregiver" class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('caregiverSeesEnrollmentInvitationData')==-1">
                                <label class="control-label col-md-10"> Allow User to Choose Whether Caregiver Sees Enrollment Invitation Data
                                </label>



                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['caregiverSeesEnrollmentInvitationData'].value}" (click)="togglePreference('caregiverSeesEnrollmentInvitationData',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['caregiverSeesEnrollmentInvitationData'].value}" (click)="togglePreference('caregiverSeesEnrollmentInvitationData',false)">
                                                              No
                                         </button>
                                </div>

                            </div> -->
<!--                             <div data-intro id="popup-patient-chat" class="no-padding form-group col-md-6">
                                <label class="control-label col-md-10"> Allow a Patient to See a List of Staff Members they Can Choose From to Chat With During Branch Hours
                                    <i chToolTip="PREFET0024"></i></label>
                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['showPatientChatwithModal'].value}" (click)="togglePreference('showPatientChatwithModal',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['showPatientChatwithModal'].value}" (click)="togglePreference('showPatientChatwithModal',false)">
                                                              No
                                         </button>
                                </div>

                            </div> -->
                            <!--========================================-->

                            <!--<div class="row">-->
                            <div data-intro id="masked-discussion-group-recipient-role" class="no-padding form-group col-md-6" [hidden]="!accountPreferences.controls['enableMaskedDiscussionGroup'].value">

                                <label class="control-label col-md-10">Masked Discussion Group Recipient Roles <i chToolTip="PREFET0020"></i>
                                    </label>
                                <div class="col">
                                    <select class="form-control select2" data-placeholder="None Selected" formControlName="maskedDiscussionGroupReceiptRole" id="maskedDiscussionGroupReceiptRole" multiple>  
                                    <option *ngFor="let teanntRole of teanntRolesMasked" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                </select>
                                </div>
                            </div>
                            <!--</div>-->

                            <!--<div class="form-group row" [hidden]="conversationTypeStatus != 1">
                                <label class="col-md-3 control-label">Masked Discussion Group Receipt Roles</label>
                                <div class="col-md-3">
                                    <select class="form-control select2" data-placeholder="None Selected" formControlName="maskedDiscussionGroupReceiptRole" id="maskedDiscussionGroupReceiptRole" multiple>  
                                    <option *ngFor="let teanntRole of teanntRoles" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                </select>
                                </div>

                            </div>-->

                            <!-- <div class="no-padding form-group col-md-6" [hidden]="!accountPreferences.controls['patientSignupWithAdminApproval'].value">
                                <label class="control-label col-md-10">Show pending patients status in dashboard
                                <i chToolTip="PREFET0014"></i></label>

                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['showPatientStatusDashboard'].value}" (click)="togglePreference('showPatientStatusDashboard',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['showPatientStatusDashboard'].value}" (click)="togglePreference('showPatientStatusDashboard',false)">
                                                              No
                                         </button>
                                </div>

                            </div> -->
                            <div data-intro id="enable-multi-thread-pdg" class="no-padding form-group col-md-6">
                                <label class="control-label col-md-10">Enable Multi Thread PDG <i chToolTip="PREFET0029"></i>
                                </label>



                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['enableMultiThreadPdg'].value}" (click)="togglePreference('enableMultiThreadPdg',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['enableMultiThreadPdg'].value}" (click)="togglePreference('enableMultiThreadPdg',false)">
                                                              No
                                         </button>
                                </div>

                            </div>
                            <div data-intro class="no-padding form-group col-md-6" [hidden]="userData.config.enable_nursing_agencies_visibility_restrictions != 1">

                                <label class="control-label col-md-10">Allow Nursing Agencies to Manage Message Groups</label>
                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['nursingAgenciesforManageMessageGroups'].value}" (click)="togglePreference('nursingAgenciesforManageMessageGroups', true)">
                                        Yes
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['nursingAgenciesforManageMessageGroups'].value}" (click)="togglePreference('nursingAgenciesforManageMessageGroups', false)">
                                        No
                                    </button>
                                </div>
                            </div>
                            <div data-intro class="no-padding form-group col-md-6" [hidden]="userData.config.enable_nursing_agencies_visibility_restrictions != 1">

                                <label class="control-label col-md-10">Allow Nursing Agencies to Manage Education Materials</label>

                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['nursingAgenciesforManageEducationMaterials'].value}" (click)="togglePreference('nursingAgenciesforManageEducationMaterials', true)">
                                        Yes
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['nursingAgenciesforManageEducationMaterials'].value}" (click)="togglePreference('nursingAgenciesforManageEducationMaterials', false)">
                                        No
                                    </button>
                                </div>
                            </div>

                            <div data-intro class="no-padding form-group col-md-6" [hidden]="userData.config.enable_nursing_agencies_visibility_restrictions != 1">

                                <label class="control-label col-md-10">Allow Nursing Agency Patients to chat with Pharmacy</label>
                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['nursingAgenciesThatPatientsAllowedToChatWithPharmacy'].value}" (click)="togglePreference('nursingAgenciesThatPatientsAllowedToChatWithPharmacy', true)">
                                        Yes
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['nursingAgenciesThatPatientsAllowedToChatWithPharmacy'].value}" (click)="togglePreference('nursingAgenciesThatPatientsAllowedToChatWithPharmacy', false)">
                                        No
                                    </button>
                                </div>
                            </div>

                            <div data-intro class="no-padding form-group col-md-6" [hidden]="userData.config.enable_nursing_agencies_visibility_restrictions != 1">
                                <label class="control-label col-md-10">Allow Nursing Agency Staffs to chat with Pharmacy</label>
                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['nursingAgenciesThatStaffsAllowedToChatWithPharmacy'].value}" (click)="togglePreference('nursingAgenciesThatStaffsAllowedToChatWithPharmacy', true)">
                                        Yes
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['nursingAgenciesThatStaffsAllowedToChatWithPharmacy'].value}" (click)="togglePreference('nursingAgenciesThatStaffsAllowedToChatWithPharmacy', false)">
                                        No
                                    </button>
                                </div>
                            </div>

                            <div data-intro class="no-padding form-group col-md-6" [hidden]="account.masterEnabled == false || (account.masterEnabled == true && account.isMaster == true)">
                                <label class="control-label col-md-10">Route Patient Chat Messages to Master Tenant</label>
                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['flexSitePatientsCanChatWithInternalSiteStaffs'].value}" (click)="togglePreference('flexSitePatientsCanChatWithInternalSiteStaffs', true)">
                                        Yes
                                    </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['flexSitePatientsCanChatWithInternalSiteStaffs'].value}" (click)="togglePreference('flexSitePatientsCanChatWithInternalSiteStaffs', false)">
                                        No
                                    </button>
                                </div>
                            </div>

                            <div class="no-padding form-group col-md-6" [hidden]="true">
                                <label class="control-label col-md-10">Hide Patient Display Name		
                                </label>

                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['enableDisplayName'].value}" (click)="togglePreference('enableDisplayName',true)">		
                                                            Yes		
                                        </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['enableDisplayName'].value}" (click)="togglePreference('enableDisplayName',false)">		
                                                                No		
                                        </button>
                                </div>

                            </div>

                            <div id="escalation-time" class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('escalationTimeInSec')==-1">

                                <label class="control-label col-md-10">Escalation Time (Minutes) *
                                                <i chToolTip="MSGES00001"></i></label>
                                <div class="col">
                                    <input type="text" id="lastName" class="form-control" formControlName="escalationTime" placeholder="Escalation Time (Minutes)" pattern="^[1-9][0-9]*$">
                                    <div *ngIf="accountPreferences.controls['escalationTime'].errors &&accountPreferences.controls['escalationTime'].errors.pattern" class="alert alert-danger">
                                        Escalation Time Value need to be integer
                                    </div>
                                    <div *ngIf="accountPreferences.controls['escalationTime'].hasError('required')&&(accountPreferences.controls.escalationTime?.dirty ||accountPreferences.controls.escalationTime?.touched || f.submitted)" class="alert alert-danger">
                                        Escalation Time Value cannot be empty
                                    </div>
                                </div>
                            </div>

                            <!--/span-->

                            <!-- from general settings starts -->


                            <!-- from general settings ends -->


                        </div>

                        <div class="row">
                            <div id="session-timeout" class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('sessionTimeoutInSec')== -1">
                                <label class="control-label col-md-10">Session Timeout (Minutes) *
                                        <i chToolTip="PREFET0001"></i></label>
                                <div class="col">
                                    <input type="text" formControlName="sessionTimeout" id="firstName" class="form-control" placeholder="Session Timeout (Minutes)" value="" pattern="^[1-9][0-9]*$">

                                    <div *ngIf="accountPreferences.controls['sessionTimeout'].errors &&accountPreferences.controls['sessionTimeout'].errors.pattern" class="alert alert-danger">
                                        Session Timeout Value need to be integer
                                    </div>
                                    <div *ngIf="accountPreferences.controls['sessionTimeout'].hasError('required')&&(accountPreferences.controls.sessionTimeout?.dirty ||accountPreferences.controls.sessionTimeout?.touched || f.submitted)" class="alert alert-danger">
                                        Session Timeout Value cannot be empty
                                    </div>
                                </div>
                                <!-- <span class="help-block"> This is Session Timeout (Minutes) </span> -->
                            </div>

                            <!--/span-->

                            <!--error class has-error-->
                            <div id="session-timeout-warning" class="no-padding form-group  col-md-6" *ngIf="hidePreferences.indexOf('sessionTimeoutWarningInSec')== -1">
                                <label class="control-label col-md-10">Session Timeout Warning (Seconds) * 
                                        <i chToolTip="PREFET0002"></i></label>
                                <div class="col">
                                    <input type="text" id="lastName" formControlName="sessionTimeoutWarning" class="form-control" placeholder="Session Timeout Warning (Seconds)" value="" pattern="^[1-9][0-9]*$">
                                    <!-- <span class="help-block"> This is Session Timeout Warning (Seconds) </span> -->
                                    <div *ngIf="accountPreferences.controls['sessionTimeoutWarning'].errors &&accountPreferences.controls['sessionTimeoutWarning'].errors.pattern" class="alert alert-danger">
                                        Session Timeout Warning Value need to be integer
                                    </div>

                                    <div *ngIf="accountPreferences.controls['sessionTimeoutWarning'].hasError('required')&&(accountPreferences.controls.sessionTimeoutWarning?.dirty ||accountPreferences.controls.sessionTimeoutWarning?.touched || f.submitted)" class="alert alert-danger">
                                        Session Timeout Warning Value cannot be empty
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div id="patient-reminder-time" class="no-padding form-group col-md-6">
                                <label class="control-label col-md-10">Patient Reminder Time (Hours) * 
                                    <i chToolTip="PREFET0016"></i>
                                </label>
                                <div class="col">
                                    <input type="text" formControlName="patientReminderTime" id="patientReminderTime" class="form-control" placeholder="Patient Reminder Time (Hours)" value="" pattern="^[0-9]*(\.[0-9]{0,2})?$">

                                    <div *ngIf="accountPreferences.controls['patientReminderTime'].errors &&accountPreferences.controls['patientReminderTime'].errors.pattern" class="alert alert-danger">
                                        Patient Reminder Time Value need to be integer or decimal
                                    </div>
                                    <div *ngIf="accountPreferences.controls['patientReminderTime'].hasError('required')&&(accountPreferences.controls.patientReminderTime?.dirty ||accountPreferences.controls.patientReminderTime?.touched || f.submitted)" class="alert alert-danger">
                                        Patient Reminder Time Value cannot be empty
                                    </div>

                                </div>
                            </div>

                            <div id="patient-reminder-types" class="no-padding form-group col-md-6">
                                <label class="control-label col-md-10">Patient Reminder Types * <i chToolTip="PREFET0021"></i></label>
                                <div class="col">
                                    <select class="form-control select2" formControlName="patientReminderTypes" data-placeholder="None Selected" id="patientReminderTypes" multiple>  
                                    <option *ngFor="let reminderType of allReminderTypes" value="{{reminderType.id}}"> {{reminderType.name}} </option>
                                </select>
                                    <div *ngIf="accountPreferences.controls['patientReminderTypesValidate'].hasError('required')&&(accountPreferences.controls.patientReminderTypesValidate?.dirty ||accountPreferences.controls.patientReminderTypesValidate?.touched || f.submitted)" class="alert alert-danger">
                                        Patient Reminder Types value cannot be empty
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div id="enrollment-reminder-types" class="no-padding form-group col-md-6">
                                <label class="control-label col-md-10">Enrollment Reminder Types
                                        <i chToolTip="PREFET0023"></i>
                                    </label>
                                <div class="col">
                                    <select class="form-control select2" formControlName="enrollmentReminderTypes" data-placeholder="None Selected" id="enrollmentReminderTypes" multiple>
                                            <option *ngFor="let reminderType of enrollmentReminderTypes" value="{{reminderType.id}}"> {{reminderType.name}} </option>
                                        </select>
                                    <!-- <div *ngIf="accountPreferences.controls['patientReminderTypesValidate'].hasError('required')&&(accountPreferences.controls.patientReminderTypesValidate?.dirty ||accountPreferences.controls.patientReminderTypesValidate?.touched || f.submitted)"
                                                                        class="alert alert-danger">
                                                                        Patient Reminder Types value cannot be empty
                                                                    </div> -->
                                </div>
                            </div>
                            <div id="token-expiry-time" *ngIf="account.enableApplessWorkflow" class="no-padding form-group col-md-6">
                                <label class="control-label col-md-10">AppLess (MagicLink) Token Expiration Time (Minutes) <i chToolTip="PREFET0026"></i>
                                </label>
                                <div class="col">
                                    <input type="text" formControlName="tokenExpiryTime" id="tokenExpiryTime" class="form-control" placeholder="AppLess (MagicLink) Token Expiration Time (Minutes)" value="" pattern="^[0-9]*(\.[0-9]{0,2})?$">

                                    <div *ngIf="accountPreferences.controls['tokenExpiryTime'] && accountPreferences.controls['tokenExpiryTime'].errors &&accountPreferences.controls['tokenExpiryTime'].errors.pattern" class="alert alert-danger">
                                        AppLess (MagicLink) Token Expiration Time Value need to be integer or decimal
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div id="verification-remember-time" *ngIf="account.enableApplessWorkflow" class="no-padding form-group col-md-6">
                                <label class="control-label col-md-10">AppLess (MagicLink) Time to Remember Verification Code (Days) <i chToolTip="PREFET0027"></i>
                                </label>
                                <div class="col">
                                    <input type="text" formControlName="verificationRememberTime" id="verificationRememberTime" class="form-control" placeholder="AppLess (MagicLink) Time to Remember Verification Code (Days)" value="" pattern="^([-][1])|^([0-9]*)$">

                                    <div *ngIf="accountPreferences.controls['verificationRememberTime'] && accountPreferences.controls['verificationRememberTime'].errors &&accountPreferences.controls['verificationRememberTime'].errors.pattern" class="alert alert-danger">
                                        AppLess (MagicLink) Time to Remember Verification Code (Days) Value need to be integer
                                    </div>
                                </div>
                            </div>
                            <div id="verification-token-expiry-time" *ngIf="account.enableApplessWorkflow" class="no-padding form-group col-md-6">
                                <label class="control-label col-md-10">AppLess (MagicLink) Verification Code Expiration Time (Minutes) <i chToolTip="PREFET0028"></i>
                                </label>
                                <div class="col">
                                    <input type="text" formControlName="verificationTokenExpiryTime" id="verificationTokenExpiryTime" class="form-control" placeholder="AppLess (MagicLink) Verification Code Expiration Time (Minutes)" value="" pattern="^(0*[1-9][0-9]*(\.[0-9]+)?|0+\.[0-9]*[1-9][0-9]*)$">

                                    <div *ngIf="accountPreferences.controls['verificationTokenExpiryTime'] && accountPreferences.controls['verificationTokenExpiryTime'].errors &&accountPreferences.controls['verificationTokenExpiryTime'].errors.pattern" class="alert alert-danger">
                                        AppLess (MagicLink) Verification Code Expiration Time (Minutes) Value need to be integer or decimal greater than 0
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>

                    <h5 id="introduction-text-preferences-and-features" class="text-black"><strong>Features </strong></h5>
                    <div class="form-actions"></div>

                    <div class="form-body">
                        <div class="row">
                            <div id="default-user-received-escalated-message" class="no-padding form-group col-md-6" [hidden]="hidePreferences.indexOf('defaultUserToReceiveEscalatedMessage')!=-1 || this.multiSiteEnable">

                                <label class="control-label col-md-10">Default User Who Receives Escalated Messages
                                            <i chToolTip="MSGES00002"></i></label>



                                <div class="col">
                                    <select class="form-control select2" data-placeholder="None Selected" id="defaultUser" multiple> Select 
                                                    <option *ngFor="let user of selectedUserListRecvEsc"  value="{{user.id}}" [selected]="user.status ==true"   >{{user.displayName}} </option>                                                                                                                                                            
                                                </select>
                                </div>
                            </div>


                            <!--<div class="no-padding form-group col-md-6">

                                <label class="control-label col-md-10">Form Tags for Partner Referral Initiation
                                            <i chToolTip="MSGES00002"></i></label>



                                <div class="col">
                                    <select class="form-control select2" data-placeholder="None Selected" formControlName="partnerReferralFormTag" id="partnerReferralFormTag" multiple> Select 
                                                    <option *ngFor="let user of defaultUserreferal"  value="{{user.id}}"   >{{user.tagMeta}} </option>                                                                                                                                                            
                                                </select>
                                </div>
                            </div>-->
                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf( 'messageEscalationBehavior')==-1">

                                <label class="control-label col-md-10">Message Escalation Behavior
                                               <!--  <i chToolTip="MSGES00002"></i>--></label>



                                <div class="col">
                                    <select class="form-control" formControlName="escalationBehaviour"> 
                                        <option value="Escalations_disabled" > Select </option>
                                        <option value="Escalate_first_message_only"> Escalate 1st message </option>
                                        <option value="Escalate_all_messages"> Escalate all messages </option>
                                    </select>
                                </div>
                            </div>

                            <!-- <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf( 'patientDiscussionGroup')==-1">

                                <label class="control-label col-md-10">Allow Patient Discussion Group
                                    <i chToolTip="PREFET0011"></i></label>



                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{ 'active': accountPreferences.controls[ 'allowPatientDiscussionGroup'].value}" (click)="togglePreference(
                                'allowPatientDiscussionGroup',true)">
                                                            Yes
                                            </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{ 'active': !accountPreferences.controls[ 'allowPatientDiscussionGroup'].value}" (click)="togglePreference(
                                'allowPatientDiscussionGroup',false)">
                                                                No
                                            </button>
                                </div>
                            </div> -->

                            <!-- <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf( 'patientSelfInventory')==-1">

                                <label class="control-label col-md-10">Allow Patients To Create Supply Counts
                                    <i chToolTip="PREFET0012"></i></label>



                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{ 'active': accountPreferences.controls[ 'allowPatientSelfInventory'].value}" (click)="togglePreference(
                                'allowPatientSelfInventory',true)">
                                                            Yes
                                            </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{ 'active': !accountPreferences.controls[ 'allowPatientSelfInventory'].value}" (click)="togglePreference(
                                'allowPatientSelfInventory',false)">
                                                                No
                                            </button>
                                </div>

                            </div> -->








                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf( 'patientSignupWithAdminApproval')==-1">


                                <label class="control-label col-md-10">Patient Signup With Admin Approval
                                    <i chToolTip="selfInventory" ></i>        </label>



                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{ 'active': accountPreferences.controls[ 'patientSignupWithAdminApproval'].value}" (click)="togglePreference(
                                'patientSignupWithAdminApproval',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{ 'active': !accountPreferences.controls[ 'patientSignupWithAdminApproval'].value}" (click)="togglePreference(
                                'patientSignupWithAdminApproval',false)">
                                                              No
                                         </button>
                                </div>

                            </div>
                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf( 'clinicianSignupWithAdminApproval')==-1">

                                <label class="control-label col-md-10">Clinician Signup With Admin Approval
                                    <i chToolTip="patientInitiate" ></i>       </label>



                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{ 'active': accountPreferences.controls[ 'clinicianSignupWithAdminApproval'].value}" (click)="togglePreference(
                                'clinicianSignupWithAdminApproval',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{ 'active': !accountPreferences.controls[ 'clinicianSignupWithAdminApproval'].value}" (click)="togglePreference(
                                'clinicianSignupWithAdminApproval',false)">
                                                              No
                                         </button>
                                </div>

                            </div>
                            <div id="allow-patient-chat-hours" class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf( 'patientsToInitiateChatDuringDay')==-1 && !this.multiSiteEnable">

                                <label class="control-label col-md-10">Allow Patients to Chat 24 Hours
                                <i chToolTip="PREFET0013"></i></label>
                                <div class="btn-group col-md-2">
                                    <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{ 'active': accountPreferences.controls[ 'patientsToInitiateChatDuringDay'].value}" (click)="togglePreference(
                                'patientsToInitiateChatDuringDay',true)">
                                                           Yes
                                         </button>
                                    <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{ 'active': !accountPreferences.controls[ 'patientsToInitiateChatDuringDay'].value}" (click)="togglePreference(
                                'patientsToInitiateChatDuringDay',false)">
                                                              No
                                         </button>
                                </div>
                            </div>

                            <div id='no-staff-available-message' class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf( 'noClinicianMessage')==-1 && !accountPreferences.controls[ 'patientsToInitiateChatDuringDay'].value && !this.multiSiteEnable">

                                <label class="control-label col-md-10">No Staff Member Available Message *
                                <i chToolTip="MSGES00003"></i></label>



                                <div class="col">
                                    <textarea formControlName="noClinicianMessage" type="text" class="form-control"></textarea>
                                    <div *ngIf="accountPreferences.controls[ 'noClinicianMessage'].errors&&(accountPreferences.controls.noClinicianMessage?.dirty ||accountPreferences.controls.noClinicianMessage?.touched || f.submitted)" class="alert
                                alert-danger">
                                        Message cannot be empty
                                    </div>
                                </div>
                            </div>
                            <!--<div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf( 'noClinicianMessage')==-1 && !accountPreferences.controls[ 'patientsToInitiateChatDuringDay'].value">

                                <label class="control-label col-md-10">Partner Patient Referral Initiation Introduction
                                <i chToolTip="MSGES00003"></i></label>



                                <div class="col">
                                    <textarea formControlName="partnerPatientReferralIntroduction" class="form-control" id="partnerPatientReferralIntro"></textarea>

                                </div>
                            </div>-->
                            <div class="no-padding form-group col-md-6">
                                <label class="control-label col-md-10">Site Working Day(s) * <i chToolTip="MSGES00024"></i></label>

                                <div class="col">
                                    <select class="form-control select2" formControlName="branchDays" data-placeholder="None Selected" id="branchDays" multiple>
                                        <option *ngFor="let branchDay of allBranchDays" value="{{branchDay.id}}"> {{branchDay.name}} </option>
                                    </select>
                                    <div *ngIf="accountPreferences.controls[ 'branchDays'].errors&&(accountPreferences.controls.branchDays?.dirty ||accountPreferences.controls.branchDays?.touched || f.submitted)" class="alert
                                                            alert-danger">
                                                            Site day(s) cannot be empty
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 control-label"><b>Site Hours</b> <i chToolTip="MSGES00025"></i></label>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 control-label">Start Time *</label>
                            <div class="col">
                                <input class="form-control" type="text" formControlName="branchStart" id="branchStart" placeholder="hh:mm">
                                <div *ngIf="accountPreferences.controls[ 'branchStart'].hasError( 'required')&&(accountPreferences.controls.branchStart?.dirty ||accountPreferences.controls.branchStart?.touched || f.submitted)" class="alert alert-danger">
                                    Time is required
                                </div>
                            </div>
                            <div class="col">
                                <select class="form-control" formControlName="branchStartTime" id="branchStartTime">
                                        <option value="12">AM</option>
                                        <option value="24">PM</option>
                                    </select>
                            </div>
                            <label class="col-md-3 control-label">End Time *</label>
                            <div class="col">
                                <input class="form-control" type="text" formControlName="branchEnd" id="branchEnd" placeholder="hh:mm">
                                <div *ngIf="accountPreferences.controls[ 'branchEnd'].hasError( 'required')&&(accountPreferences.controls.branchEnd?.dirty ||accountPreferences.controls.branchEnd?.touched || f.submitted)" class="alert alert-danger">
                                    Time is required
                                </div>
                            </div>
                            <div class="col">
                                <select class="form-control" formControlName="branchEndTime" id="branchEndTime">
                                        <option value="12">AM</option>
                                        <option value="24">PM</option>
                                    </select>
                            </div>
                        </div>
                        <!-- <div class="form-group row">
                            <label class="col-md-3 control-label"><b>Partner Patient Referral </b></label>
                        </div>
                        <div class="form-group row">
                            <label class="control-label col-md-3">Form Tags for Partner Referral Initiation
                                            <i chToolTip="MSGES00012"></i></label>



                            <div class="col-md-3">
                                <select class="form-control select2" data-placeholder="None Selected" formControlName="partnerReferralFormTag" id="partnerReferralFormTag" multiple> Select 
                                                    <option *ngFor="let user of defaultUserreferal"  value="{{user.id}}"   >{{user.tagMeta}} </option>                                                                                                                                                            
                                                </select>
                            </div>

                            <label class="control-label col-md-3">Partner Patient Referral Initiation Introduction
                                <i chToolTip="MSGES00023"></i></label>



                            <div class="col-md-3">
                                <textarea formControlName="partnerPatientReferralIntroduction" class="form-control" id="partnerPatientReferralIntro"></textarea>

                            </div>
                        </div> -->



                        <div class="form-group row" *ngIf="!accountPreferences.controls[ 'patientsToInitiateChatDuringDay'].value">
                            <label style="min-width:51%;" class="col-md-3 control-label"><b>Enter the Hours Patients are Permitted to Start Chats</b> <i chToolTip="MSGES00027"></i></label>
                            <!-- <label *ngIf="accountPreferences.controls[ 'patientsToInitiateChatDuringDay'].value" class="col-md-3 control-label"><b>Branch Hours</b></label> -->
                        </div>


                        <div id="patient-permitted-start-chat-hours" class=" form-group row" *ngIf="!accountPreferences.controls[ 'patientsToInitiateChatDuringDay'].value">
                            <!-- *ngIf="!accountPreferences.controls[ 'patientsToInitiateChatDuringDay'].value" -->
                            <label class="col-md-3 control-label">Start Time *</label>
                            <div class="col">
                                <input class="form-control" type="text" formControlName="homeinfusionStart" id="homeinfusionStart" placeholder="hh:mm">
                                <div *ngIf="accountPreferences.controls[ 'homeinfusionStart'].hasError( 'required')&&(accountPreferences.controls.homeinfusionStart?.dirty ||accountPreferences.controls.homeinfusionStart?.touched || f.submitted)
                             " class="alert alert-danger">
                                    Time is required
                                </div>
                            </div>
                            <div class="col">
                                <select class="form-control" formControlName="homeInfusionStartTime" id="homeInfusionStartTime">
                                        <option value="12">AM</option>
                                        <option value="24">PM</option>
                                    </select>
                            </div>
                            <label class="col-md-3 control-label">End Time *</label>
                            <div class="col">
                                <input class="form-control" type="text" formControlName="homeinfusionEnd" id="homeinfusionEnd" placeholder="hh:mm">
                                <div *ngIf="accountPreferences.controls[ 'homeinfusionEnd'].hasError( 'required')&&(accountPreferences.controls.homeinfusionEnd?.dirty ||accountPreferences.controls.homeinfusionEnd?.touched || f.submitted)" class="alert
                                alert-danger">
                                    Time is required
                                </div>
                            </div>
                            <div class="col">
                                <select class="form-control" formControlName="homeinfusionEndTime" id="homeinfusionEndTime">
                                        <option value="12">AM</option>
                                        <option value="24">PM</option>
                                    </select>
                            </div>
                        </div>


                        <div class="form-group row" *ngIf="hidePreferences.indexOf( 'patientsToInitiateChatDuringDay')==- 1  && !this.multiSiteEnable">
                            <label class="col-md-3 control-label"><b>Patient Messaging</b></label>
                        </div>
                        <div id="patient-messaging" *ngIf="hidePreferences.indexOf( 'patientsToInitiateChatDuringDay')==- 1 && !this.multiSiteEnable">
                            <div class="form-group row">
                                <!-- <label class="col-md-3 control-label" [hidden]="conversationTypeStatus==1">Roles allowed to be in the Message Routing Schedule <i chToolTip="MSGES00026"></i></label>
                                <label class="col-md-3 control-label" [hidden]="conversationTypeStatus !=1">Roles allowed to be in the Message Routing Schedule <i chToolTip="MSGES00026"></i></label>
                                <div class="col-md-3">
                                    <select class="form-control select2" data-placeholder="None Selected" formControlName="rolesChatWithUsNow" id="rolesChatWithUsNow" multiple>  
                                        <option *ngFor="let teanntRole of teanntRoles" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                    </select>
                                </div> -->
                                <!-- <label *ngIf="userData.tenantId<64" class="col-md-3 control-label" [hidden]="!accountPreferences.controls[ 'chatEnableWhereIsMyDelivery'].value">Roles for "Where Is My Delivery?"</label>
                                <div *ngIf="userData.tenantId<64" class="col-md-3" [hidden]="!accountPreferences.controls[ 'chatEnableWhereIsMyDelivery'].value">
                                    <select class="form-control select2" data-placeholder="None Selected" formControlName="rolesWhereIsMyDelivery" id="rolesWhereIsMyDelivery" multiple>  
                                        <option *ngFor="let teanntRole of teanntRoles" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                    </select>
                                </div> -->
                                <label class="col-md-3 control-label">Roles to Receive Patient Chats by Default <i chToolTip="MSGES00028"></i></label>
                                <div class="col-md-6">
                                    <select class="form-control select2" data-placeholder="None Selected" formControlName="rolesDefaultNurses" id="rolesDefaultNurses" multiple>  
                                        <option *ngFor="let teanntRole of teanntRoles" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                    </select>
                                </div>
                            </div>
                            <!-- <div class="form-group row" *ngIf="userData.tenantId<64">
                                <label class="col-md-3 control-label" [hidden]="!accountPreferences.controls[ 'chatEnableWhenIsMyNurseComing'].value">Roles for "When Is My Nurse Coming?"</label>
                                <div class="col-md-3" [hidden]="!accountPreferences.controls[ 'chatEnableWhenIsMyNurseComing'].value">
                                    <select class="form-control select2" data-placeholder="None Selected" formControlName="rolesWhenNurseArrive" id="rolesWhenNurseArrive" multiple>  
                                        <option *ngFor="let teanntRole of teanntRoles" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                    </select>
                                </div>
                                <label class="col-md-3 control-label" [hidden]="!accountPreferences.controls[ 'chatInfusionSupport'].value">Roles for "I Need Help/I Have A Question"</label>
                                <div class="col-md-3" [hidden]="!accountPreferences.controls[ 'chatInfusionSupport'].value">
                                    <select class="form-control select2" data-placeholder="None Selected" formControlName="rolesIhaveQuestion" id="rolesIhaveQuestion" multiple>  
                                        <option *ngFor="let teanntRole of teanntRoles" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                    </select>
                                </div>
                            </div> -->
                        </div>                                              
                        <div id="roles-default-users" class="form-group row" *ngIf="hidePreferences.indexOf( 'patientsToInitiateChatDuringDay')==- 1 && !this.multiSiteEnable">
                            <!-- <label class="col-md-3 control-label">Roles for "Default Users"</label>
                            <div class="col-md-3">
                                <select class="form-control select2" data-placeholder="None Selected" formControlName="rolesDefaultNurses" id="rolesDefaultNurses" multiple>  
                                        <option *ngFor="let teanntRole of teanntRoles" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                    </select>
                            </div> -->
                            <label class="col-md-3 control-label">Welcome Message for New Patient on Initiate Chat</label>
                            <div class="col-md-6">
                                <textarea type="text" class="form-control" formControlName="newPatientChatWelcomeMessage"></textarea>
                            </div>
                        </div>
                      <!--   <div class="form-group row">
                            <label class="col-md-3 control-label">Email/sms message received after complete the enrollment</label>
                            <div class="col-md-6">
                                <textarea type="text" class="form-control" formControlName="enrollMessage"></textarea>
                            </div>
                        </div> -->
                        <!-- <div class="form-group row">
                            <label class="col-md-3 control-label">Staff Who Receives Push Notification on Enrollment</label>
                            <div class="col-md-3">
                                <select class="form-control select2" data-placeholder="None Selected" id="notificationOnErollment" multiple>  
                                    <option *ngFor="let user of selectedUserListclinicalLiaison"  value="{{user.id}}" [selected]="user.status ==true"   >{{user.displayName}} </option>     
                                </select>
                            </div>
                        </div> -->
                        <!-- <div class="form-group row" [hidden]="!accountPreferences.controls['chatInfusionSupport'].value">
                            <label class="col-md-3 control-label"><b>Conversation Type</b></label>
                        </div>
                        <div class="form-body" [hidden]="!accountPreferences.controls['chatInfusionSupport'].value">
                            <div class="form-group row">
                                <label class="col-md-3 control-label">Conversation Type <i chToolTip="PREFET0022"></i></label>
                                <div class="col-md-3">
                                    <select class="form-control" data-placeholder="None Selected" formControlName="conversationType" (change)="toggleConversationType($event.target.value)">
                                            <option value="0" selected>Direct Chat</option>
                                            <option value="1" >Patient Choice</option>
                                    </select>
                                </div>
                                <label class="col-md-3 control-label" [hidden]="conversationTypeStatus !=1">Initiation Message</label>
                                <div class="col-md-3" [hidden]="conversationTypeStatus !=1">
                                    <input type="text" id="chat_role2_displayname" formControlName="initiationMessage" class="form-control" value="">
                                </div>
                            </div>
                            <div class="form-group row" [hidden]="conversationTypeStatus !=1">
                                <label class="col-md-3 control-label">Conversation Role 1 Display Name</label>
                                <div class="col-md-3">
                                    <input type="text" id="chat_role1_displayname" formControlName="chatRole1DisplayName" class="form-control" value="">
                                </div>
                                <label class="col-md-3 control-label">Conversation Role 2 Display Name</label>
                                <div class="col-md-3">
                                    <input type="text" id="chat_role2_displayname" formControlName="chatRole2DisplayName" class="form-control" value="">
                                </div>
                            </div>
                            <div class="form-group row" [hidden]="conversationTypeStatus !=1">
                                <label class="col-md-3 control-label">Conversation Roles 1</label>
                                <div class="col-md-3">
                                    <select class="form-control select2" data-placeholder="None Selected" formControlName="chatRole1" id="chatRole1" multiple>  
                                        <option *ngFor="let teanntRole of teanntRoles" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                    </select>
                                </div>
                                <label class="col-md-3 control-label">Conversation Roles 2</label>
                                <div class="col-md-3">
                                    <select class="form-control select2" data-placeholder="None Selected" formControlName="chatRole2" id="chatRole2" multiple>  
                                        <option *ngFor="let teanntRole of teanntRoles" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group row" [hidden]="conversationTypeStatus !=1">
                                <label class="col-md-3 control-label">Conversation Roles 1 Message Template</label>
                                <div class="col-md-3">
                                    <textarea type="text" class="form-control" formControlName="chatRole1MessageTemplate"></textarea>
                                </div>
                                <label class="col-md-3 control-label">Conversation Roles 2 Message Template</label>
                                <div class="col-md-3">
                                    <textarea type="text" class="form-control" formControlName="chatRole2MessageTemplate"></textarea>
                                </div>
                            </div>
                        </div> -->

                        <h5 class="text-black" id="introduction-text-preferences-and-features"><strong>Integration Settings for User Registrations </strong></h5>
                        <div class="form-actions"></div>
                        <div class="form-body">
                            <div class="form-group row">
                                <label class="col-md-3 control-label">Default Staff Role <i chToolTip="INTGN01"></i></label>
                                <div class="col-md-3">
                                    <select class="form-control" data-placeholder="None Selected" formControlName="defaultStaffRole" id="defaultStaffRole">  
                                        <option value="" selected>Select Role</option>
                                        <option *ngFor="let staffRole of staffRoles" value="{{staffRole.id}}"> {{staffRole.name}} </option>
                                    </select>
                                </div>
                                <label class="col-md-3 control-label">Default Staff Tags <i chToolTip="INTGN02"></i></label>
                                <div class="col-md-3">
                                    <select class="form-control select2" data-placeholder="None Selected" formControlName="defaultStaffTags" id="defaultStaffTags" multiple>  
                                        <option *ngFor="let staffTag of staffTags" value="{{staffTag.id}}"> {{staffTag.tag_name}} </option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                    <label class="col-md-3 control-label">Default Partner Role <i chToolTip="INTGN03"></i></label>
                                    <div class="col-md-3">
                                        <select class="form-control" data-placeholder="None Selected" formControlName="defaultPartnerRole" id="defaultPartnerRole">  
                                            <option value="" selected>Select Role</option>
                                            <option *ngFor="let partnerRole of partnerRoles" value="{{partnerRole.id}}"> {{partnerRole.name}} </option>
                                        </select>
                                    </div>
                                    <label class="col-md-3 control-label">Default Partner Tags <i chToolTip="INTGN04"></i></label>
                                    <div class="col-md-3">
                                        <select class="form-control select2" data-placeholder="None Selected" formControlName="defaultPartnerTags" id="defaultPartnerTags" multiple>  
                                            <option *ngFor="let partnerTag of partnerTags" value="{{partnerTag.id}}"> {{partnerTag.tag_name}} </option>
                                        </select>
                                    </div>
                                    
                                </div>
                                <div class="form-group row">
                                    <label class="col-md-3 control-label" [hidden]="!multiSiteEnable">{{ 'LABELS.ACCOUNT_SITES' | translate }} <i chToolTip=""></i></label>
                                    <div class="col-md-3" [hidden]="!multiSiteEnable">
                                        <app-select-sites *ngIf="siteIdss" [events]="eventsSubject.asObservable()" [hideApplyFilter]=true   [filterType]=true [siteSelection]="true"  [selectedSiteIds]="siteIdss" (siteIds)="getSiteId($event)" (hideDropdown)="hideDropdown($event)">
                                        </app-select-sites>
                                    </div>
                                    

                                    <label class="col-md-3 control-label"  [hidden]="userData.config.enable_patient_info_from_third_party_app != 1">Default Partner Category <i chToolTip="INTGN05"></i></label>
                                    <div class="col-md-3" [hidden]="userData.config.enable_patient_info_from_third_party_app != 1">
                                        <select class="form-control" data-placeholder="None Selected" formControlName="defaultPartnerCategory" id="defaultPartnerCategory">                               
                                            <option value="" selected>Select Partner Category</option>                                            
                                            <option *ngFor="let category of partnerCategories" [value]="category.id">
                                                {{category.displayName }}
                                            </option>                                             
                                        </select>
                                    </div>
                                </div>
                        </div>
 <!--                        <div class="form-group row">
                            <label class="col-md-3 control-label"><b>User Enrollment</b></label>
                        </div>
                        <div id="user-enrollment-notification">
                            <div class="form-group row">
                                <label class="col-md-3 control-label">Staff Who Receives Push Notification on Enrollment</label>
                                <div class="col-md-3">
                                    <select class="form-control select2" data-placeholder="None Selected" id="notificationOnErollment" multiple>  
                                            <option *ngFor="let user of selectedUserListclinicalLiaison"  value="{{user.id}}" [selected]="user.status ==true"   >{{user.displayName}} </option>     
                                        </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-md-3 control-label">Staff Who Receives SMS Notification on Enrollment</label>
                                <div class="col-md-3">
                                    <select class="form-control select2" data-placeholder="None Selected" id="smsNotificationOnErollment" multiple>  
                                        <option *ngFor="let user of staffsAwareEnrollBySms"  value="{{user.id}}" [selected]="user.status ==true"   >{{user.displayName}} </option>     
                                    </select>
                                </div>
                                <label class="col-md-3 control-label">Staff Who Receives Email Notification on Enrollment</label>
                                <div class="col-md-3">
                                    <select class="form-control select2" data-placeholder="None Selected" id="emailNotificationOnErollment" multiple>  
                                        <option *ngFor="let user of staffsAwareEnrollByEmail"  value="{{user.id}}" [selected]="user.status ==true"   >{{user.displayName}} </option>     
                                    </select>
                                </div>
                            </div>
                        </div> -->
                        <!-- <div class="form-group row" [hidden]="!isFilingCenter">
                            <label class="col-md-3 control-label"><b>Filing Center</b></label>
                        </div>
                        <div class="form-group row" [hidden]="!isFilingCenter">

                            <label class="col-md-3 control-label">Default Filing Center for Chat Log (From Citus Health)</label>
                            <div class="col-md-3">

                                <select class="form-control  select2-forms" data-placeholder="Select Outgoing Filing Center" id="sfilingCenterss">  
                                    <option value="">Select Outgoing Filing Center</option>
                                    <option *ngFor="let filingCenters of filingCentersOut"  value="{{filingCenters.id}}" [selected]="filingCenters.id === FilingCenterChatLog"  >{{filingCenters.text}} </option>     
                                    </select>
                                

                            </div>



                            <label class="col-md-3 control-label" [hidden]="!isFilingCenter">File Saving Format [Filing Center for Chat Log]</label>
                            <div class="col-md-3" [hidden]="!isFilingCenter">
                                <input type="text" id="fileSavingFormatChatLogFilingCenter" class="form-control" formControlName="fileSavingFormatChatLogFilingCenter" placeholder="Enter Filename Format" />                               
                            </div>

                        </div>
                        <div class="form-group row" [hidden]="!isFilingCenter">
                            <label class="col-md-3 control-label" [hidden]="!isFilingCenter">Title for Incoming Filing Center</label>
                            <div class="col-md-3" [hidden]="!isFilingCenter">
                                <input type="text" id="titleIncomingFilingCenter" class="form-control" formControlName="titleIncomingFilingCenter" placeholder="Enter the Title for Incoming Filing Center" />
                                <div *ngIf="fileSavingError" class="alert alert-danger">
                                    File Saving Format cannot be empty
                                </div>
                            </div>

                            <label class="col-md-3 control-label" [hidden]="!isFilingCenter">Title for Outgoing Filing Center</label>
                            <div class="col-md-3" [hidden]="!isFilingCenter">
                                <input type="text" id="titleOutgoingFilingCenter" class="form-control" formControlName="titleOutgoingFilingCenter" placeholder="Enter the Title for Outgoing Filing Center" />
                                <div *ngIf="fileSavingError" class="alert alert-danger">
                                    File Saving Format cannot be empty
                                </div>
                            </div>

                        </div> -->
                    </div>


                    <div class="form-actions">
                        <!--  <button type="button" [disabled]="!accountPreferences.valid || isDisabled" class="btn btn-primary">Submit</button> -->
                        <button type="submit" [disabled]="isDisabled" class="btn btn-primary">Submit</button>
                        <button type="button" [routerLink]="[ '/inbox']" class="btn btn-default">Cancel</button>
                    </div>
                </form>
                <!-- End Horizontal Form -->
            </div>
        </div>
        <div class="adjust-content" *ngIf="optionTab==='message'">
            <app-manage-routing-rules></app-manage-routing-rules>
        </div>
    </div>
</section>
<!-- END: forms/basic-forms-elements -->
