<!-- START: forms/basic-forms-elements -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Message Escalation (Wireframe)</strong>
            <!-- <a href="https://v4-alpha.getbootstrap.com/components/forms/" target="_blank" class="btn btn-sm btn-primary ml-2">Official Documentation <i class="icmn-link ml-1"></i></a> -->
        </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/dashboard']" >Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/accounts']" >Account</a></li>
            <li class="breadcrumb-item">Message Escalation</li>
        </ol>
        <div class="row">
            <div class="col-lg-12">
                <div class="mb-5">
                    <!-- <h5 class="text-black"><strong>Horizontal Form</strong></h5>
                    <p class="text-muted">Element: read <a href="https://v4-alpha.getbootstrap.com/components/forms/" target="_blank">official documentation<small><i class="icmn-link ml-1"></i></small></a></p> -->
                    <!-- Horizontal Form -->

                    <form   [formGroup]="messageEscalation" (ngSubmit)="updateMessageEscalation()" >
                        <div class="form-body">
                            <!-- <h3 class="form-section">Message Escalation</h3> -->
                            <div class="row">
                            
                            <div class="col-md-6">
                                <!--error class has-error-->
                                <div class="form-group">
                                    <label class="control-label">Escalation Time (Minutes)</label>
                                    <input type="text" class="form-control" formControlName="escalationTime" placeholder="Escalation Time (Minutes)" value="1">
                                    <!-- <span class="help-block"> This is Escalation Time (Minutes) </span> -->
                                </div>
                            </div>
                            <!--/span-->
                            <div class="col-md-6">
                                <div class="form-group">
                                    
                                    <label>Message Escalation Behavior</label>
                                    <i class="view-tooltip icmn-info" ></i>
                                    <select class="form-control" formControlName="escalationBehaviour"> 
                                        <option> Select </option>
                                        <option value="escalate_first"> Escalate 1st message </option>
                                        <option value="escalate_all"> Escalate all messages </option>
                                    </select>
                                </div>
                            </div>
                            <!--/span-->
                            
                            
                        </div>
                        <div class="row">
                            
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Default User who Receives Escalated Messages</label>
                                    <select class="form-control" formControlName="defaultUser"> Select 
                                        <option> None Selected </option>
                                        <option *ngFor="let user of defaultUserList;let i=index" value="{{user.id}}" >{{user.displayName}} </option>
                                        
                                    </select>
                                </div>
                            </div>
                            <!--/span-->
                        </div>
                            
                            
                            
                        </div>
                         <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Submit</button>
                            <button type="button" class="btn btn-default">Cancel</button>
                        </div>
                    </form>
                      <div class="alert alert-success" role="alert" *ngIf="settingsupdated"> <button type="button" class="close" data-dismiss="alert" aria-label="Close"> <span aria-hidden="true">&times;</span> </button> <strong>Success!</strong> <a href="#" class="alert-link">Updated Successfully </a> </div>
                    <!-- End Horizontal Form -->
                </div>
            </div>
        </div>
        
        
      
        
        
    </div>
</section>
<!-- END: forms/basic-forms-elements -->