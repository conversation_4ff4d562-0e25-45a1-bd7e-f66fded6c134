import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';
@Component({
  selector: 'app-accounts',
  templateUrl: './accounts.html'
})

export class AccountsComponent implements OnInit {
  accounts:any = {};
  totalAccounts;
  constructor(
    private _structureService: StructureService,
    private route: ActivatedRoute,
    private router: Router
  ) {
  }
  ngOnInit() {
    this.getAccounts();
  }
  getAccounts() {
    this._structureService.getAccounts().refetch().then(({ data }) => {
      this.accounts = data['getAccounts'];
      this.totalAccounts = Object.keys(this.accounts['data']).length;
    });
  }
}
