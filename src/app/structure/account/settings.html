<!-- START: forms/basic-forms-elements -->
<form [formGroup]="accountSettings" (ngSubmit)="updateSettings()">
    <section class="card">
        <div class="card-header">
            <span class="cat__core__title">
            <strong>General Setup</strong>
        </span>
        </div>
        <div class="card-block">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
                <li class="breadcrumb-item"><a href="javascript: void(0);">Account Settings</a></li>
                <li class="breadcrumb-item">General Setup</li>
            </ol>
            <div class="row">
                <div class="col-lg-12">
                    <div class="mb-5">
                        <div class="form-body">
                            <div *ngIf="previlages.superAdmin" class="form-group row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Registration ID *</label>
                                        <input *ngIf="account.registrationId != null" type="text" id="firstName" class="form-control" placeholder="Registration ID"
                                            formControlName="registrationId" [(ngModel)]="account.registrationId">
                                        <input *ngIf="account.registrationId == null" type="text" id="firstName" class="form-control" placeholder="Registration ID"
                                            formControlName="registrationId">
                                        <div *ngIf="accountSettings.controls['registrationId'].hasError('required')&&(accountSettings.controls.registrationId?.dirty ||accountSettings.controls.registrationId?.touched)"
                                            class="alert alert-danger">
                                            {{errorMessageRegId}}
                                        </div>
                                    </div>
                                </div>
                                <!--/span-->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Account Key *</label>
                                        <input type="text" id="firstName" class="form-control" placeholder="Account Key" formControlName="tenantKey" [(ngModel)]="account.key">
                                        <div *ngIf="accountSettings.controls['tenantKey'].hasError('required')&&(accountSettings.controls.tenantKey?.dirty ||accountSettings.controls.tenantKey?.touched)"
                                            class="alert alert-danger">
                                            {{errorMessageAccountKey}}
                                        </div>
                                    </div>
                                </div>
                                <!--/span-->
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <!--error class has-error-->
                                    <div class="form-group">
                                        <label class="control-label">Account Name *</label>
                                        <input type="text" id="lastName" class="form-control" placeholder="Account Name" formControlName="name" [(ngModel)]="account.name">
                                        <div *ngIf="accountSettings.controls['name'].errors&&(accountSettings.controls.name?.dirty ||accountSettings.controls.name?.touched)"
                                            class="alert alert-danger">
                                            Account Name cannot be empty
                                        </div>
                                    </div>
                                </div>
                                <!--/span-->
                                <div class="col-md-6" *ngIf="hiddenField">
                                    <div class="form-group">
                                        <label class="control-label">Address</label>
                                        <input readonly type="text" id="firstName" class="form-control" placeholder="Address" formControlName="address">
                                        <!-- <span class="help-block"> This is Registration ID </span> -->
                                    </div>
                                </div>
                                <!--/span-->
                            </div>
                            <div class="row" *ngIf="hiddenField">
                                <div class="col-md-6">
                                    <!--error class has-error-->
                                    <div class="form-group">
                                        <label class="control-label">Office Email</label>
                                        <input readonly type="email" id="lastName" class="form-control" placeholder="Office Email" formControlName="officeEmail">
                                        <!-- <span class="help-block"> This is Account Name </span> -->
                                    </div>
                                </div>
                                <!--/span-->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Contact Email</label>
                                        <input readonly type="email" id="firstName" class="form-control" placeholder="Contact Email" formControlName="contactEmail">
                                        <!-- <span class="help-block"> This is Registration ID </span> -->
                                    </div>
                                </div>
                                <!--/span-->
                            </div>
                            <div class="row" *ngIf="hiddenField">
                                <div class="col-md-6">
                                    <!--error class has-error-->
                                    <div class="form-group">
                                        <label class="control-label">Office Phone</label>
                                        <input readonly type="text" id="lastName" class="form-control" placeholder="Office Phone" formControlName="officePhone">
                                        <!-- <span class="help-block"> This is Account Name </span> -->
                                    </div>
                                </div>
                                <!--/span-->
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="control-label">Helpline Phone</label>
                                        <input readonly type="email" id="firstName" class="form-control" placeholder="Helpline Phone" formControlName="helplinePhone">
                                        <!-- <span class="help-block"> This is Registration ID </span> -->
                                    </div>
                                </div>
                                <!--/span-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Default Card w/ Nav -->
    <section class="card" [hidden]="(hidePreferences.indexOf('escalationTimeInSec')!=-1) 
    && (hidePreferences.indexOf('defaultUserToReceiveEscalatedMessage')!=-1)
    && (hidePreferences.indexOf('messageEscalationBehavior')!=-1)
    ">
        <div class="card-header">
            <span class="cat__core__title">
            <strong>Message Escalation </strong>
        </span>
        </div>
        <div class="card-block">
            <div class="col-lg-12">
                <div class="mb-5">
                    <div class="form-body">
                        <div class="row">
                            <div class="col-md-6" *ngIf="hidePreferences.indexOf('escalationTimeInSec')==-1">
                                <!--error class has-error-->
                                <div class="form-group">
                                    <label class="control-label">Escalation Time (Minutes)* </label>
                                    <i chToolTip="MSGES00001"></i>
                                    <input type="text" id="lastName" class="form-control" formControlName="escalationTime" placeholder="Escalation Time (Minutes)"
                                        pattern="^[1-9][0-9]*$">
                                    <div *ngIf="accountSettings.controls['escalationTime'].errors &&accountSettings.controls['escalationTime'].errors.pattern"
                                        class="alert alert-danger">
                                        Escalation Time Value need to be integer
                                    </div>
                                    <div *ngIf="accountSettings.controls['escalationTime'].hasError('required')&&(accountSettings.controls.escalationTime?.dirty ||accountSettings.controls.escalationTime?.touched)"
                                        class="alert alert-danger">
                                        Escalation Time Value cannot be empty
                                    </div>
                                    <!-- <span class="help-block"> This is Escalation Time (Minutes) </span> -->
                                </div>
                            </div>
                            <div class="col-md-6"  [hidden]="hidePreferences.indexOf('defaultUserToReceiveEscalatedMessage')!=-1">
                                <div class="form-group">
                                    <label>Default User Who Receives Escalated Messages </label>
                                    <i chToolTip="MSGES00002"></i>
                                    <select class="form-control select2" data-placeholder="None Selected" id="defaultUser" multiple> Select 
                                        <option *ngFor="let user of selectedUserList"  value="{{user.id}}" [selected]="user.status ==true"   >{{user.displayName}} </option>                                                                                                                                                            
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div *ngIf="hidePreferences.indexOf('messageEscalationBehavior')==-1" class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Message Escalation Behavior</label>
                                    <!--<i class="message-escalation icmn-info" ></i>-->
                                    <select class="form-control" formControlName="escalationBehaviour"> 
                                        <option value="Escalations_disabled" > Select </option>
                                        <option value="Escalate_first_message_only"> Escalate 1st message </option>
                                        <option value="Escalate_all_messages"> Escalate all messages </option>
                                    </select>
                                </div>
                            </div>
                            <!--/span-->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- END: forms/basic-forms-elements -->
    <!-- Default Card w/ Nav -->
    <section class="card" *ngIf="hidePreferences.indexOf('noClinicianMessage')==-1">
        <div class="card-header">
            <span class="cat__core__title">
            <strong>Custom Messages </strong>
        </span>
        </div>
        <div class="card-block">
            <div class="col-lg-12">
                <div class="mb-5">
                    <div class="form-body">
                        <div class="col-md-12" >
                            <div class="form-group">
                                <label>No Staff Member Available Message*</label>
                                <i chToolTip="MSGES00003"></i>
                                <textarea  formControlName="noClinicianMessage" type="text" class="form-control"></textarea>
                                <div *ngIf="accountSettings.controls['noClinicianMessage'].errors&&(accountSettings.controls.noClinicianMessage?.dirty ||accountSettings.controls.noClinicianMessage?.touched)"
                                    class="alert alert-danger">
                                    Message cannot be empty
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- END: forms/basic-forms-elements -->
    <section class="card">
        <div class="card-block">
            <div class="col-lg-12">
                <div class="mb-5">
                    <button type="submit" [disabled]="!accountSettings.valid || isDisabled" class="btn btn-primary">Submit</button>
                    <button type="button" [routerLink]="['/inbox']" class="btn btn-default">Cancel</button>
                </div>
            </div>
        </div>
    </section>
</form>
<!-- END: forms/basic-forms-elements -->