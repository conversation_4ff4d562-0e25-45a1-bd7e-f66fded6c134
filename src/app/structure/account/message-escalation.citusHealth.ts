import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';
import { FormBuilder, FormGroup, Validators, FormControl, FormArray } from '@angular/forms';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Component({
  selector: 'app-message',
  templateUrl: './message-escalation.html'
})

export class MessageEscalationComponent implements OnInit {
  escalationTime = '';
  escalationMessageBehaviour = '';
  defaultUserList = [];
  messageEscalation: FormGroup;
  settingsupdated=false;
  constructor(private route: ActivatedRoute,
    private router: Router,
    private _formBuild: FormBuilder,
    private _structureService: StructureService
  ) { }
  ngOnInit() {
    this.messageEscalation = this._formBuild.group({
      escalationTime: '',
      escalationBehaviour: '',
      defaultUser: ''
    });
    $(function () {
      $('.view-tooltip').tooltip({
        title: 'Message Escalation Behavior'
      });

    });

    this._structureService.getEscalationMessageDetails().refetch().then(({ data }) => {
      this.messageEscalation.patchValue({
        escalationTime: data.getAccount.escaltionTime,
        escalationBehaviour: data.getAccount.escalationBehaviour,
        defaultUser: data.getAccount.defaultUser,
      });
      this.escalationMessageBehaviour = data.getAccount.escalationBehaviour;

    });
    this._structureService.getDefaultUserMessageEscalations().refetch().then(({ data: resultData }) => {

      this.defaultUserList = resultData['getDefaultUserMessageEscalations'];
    });

  }
  updateMessageEscalation() {    
     const formObjData = this.messageEscalation.value;     
     this._structureService.updateMessageEscalationSettings(formObjData);
     this.settingsupdated = true;
  }
}

