<!-- START: forms/basic-forms-elements -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Preferences & Features</strong>
            <!-- <a href="https://v4-alpha.getbootstrap.com/components/forms/" target="_blank" class="btn btn-sm btn-primary ml-2">Official Documentation <i class="icmn-link ml-1"></i></a> -->
        </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/account/preferences']">Account Settings</a></li>
            <li class="breadcrumb-item">Preferences & Features</li>
        </ol>
        <div class="col-lg-12">
            <div class="mb-5">

                <form [hidden]="dataLoadingMsg" action="#" [formGroup]="accountPreferences">

                    <h5 class="text-black"><strong>Preferences</strong></h5>
                    <div class="form-actions"></div>

                    <div class="form-body">
                        <div class="row">


                            <div class="no-padding form-group col-md-6 " *ngIf="hidePreferences.indexOf('sessionTimeoutInSec')== -1">
                                <label class="control-label col-md-8">Session Timeout (Minutes)*
                                    <i chToolTip="PREFET0001"></i></label>
                                <div class="col">
                                    <input type="text" formControlName="sessionTimeout" id="firstName" class="form-control" placeholder="Session Timeout (Minutes)" value="" pattern="^[1-9][0-9]*$">

                                    <div *ngIf="accountPreferences.controls['sessionTimeout'].errors &&accountPreferences.controls['sessionTimeout'].errors.pattern" class="alert alert-danger">
                                        Session Timeout Value need to be integer
                                    </div>
                                    <div *ngIf="accountPreferences.controls['sessionTimeout'].hasError('required')&&(accountPreferences.controls.sessionTimeout?.dirty ||accountPreferences.controls.sessionTimeout?.touched)" class="alert alert-danger">
                                        Session Timeout Value cannot be empty
                                    </div>
                                </div>
                                <!-- <span class="help-block"> This is Session Timeout (Minutes) </span> -->
                            </div>

                            <!--/span-->

                            <!--error class has-error-->
                            <div class="no-padding form-group  col-md-6" *ngIf="hidePreferences.indexOf('sessionTimeoutWarningInSec')== -1">
                                <label class="control-label col-md-8">Session Timeout Warning (Seconds)* 
                                    <i chToolTip="PREFET0002"></i></label>
                                <div class="col">
                                    <input type="text" id="lastName" formControlName="sessionTimeoutWarning" class="form-control" placeholder="Session Timeout Warning (Seconds)" value="" pattern="^[1-9][0-9]*$">
                                    <!-- <span class="help-block"> This is Session Timeout Warning (Seconds) </span> -->
                                    <div *ngIf="accountPreferences.controls['sessionTimeoutWarning'].errors &&accountPreferences.controls['sessionTimeoutWarning'].errors.pattern" class="alert alert-danger">
                                        Session Timeout Warning Value need to be integer
                                    </div>

                                    <div *ngIf="accountPreferences.controls['sessionTimeoutWarning'].hasError('required')&&(accountPreferences.controls.sessionTimeoutWarning?.dirty ||accountPreferences.controls.sessionTimeoutWarning?.touched)" class="alert alert-danger">
                                        Session Timeout Warning Value cannot be empty
                                    </div>
                                </div>
                            </div>

                            <!--/span-->





                            <div class="no-padding form-group col-md-6" [hidden]="hidePreferences.indexOf('messageForwardBehavior') != -1">
                                <label class="control-label col-md-8">Message Forwarding Behavior
                                        <i chToolTip="PREFET0003"></i></label>

                                <div class="col">
                                    <select class="form-control" formControlName="messageForwardBehavior"> Select 
                                            <option value="Forwarding_not_allowed"> Select </option>
                                             <option *ngFor="let behaviour of messageForwardBehaviorObject" value="{{behaviour.value}}" >{{behaviour.text}}</option>

                                         </select>
                                </div>
                            </div>

                            <!--/span-->
                            <div class="no-padding form-group col-md-6 tagged-message-approver" [hidden]="!accountPreferences.controls['messageTagging'].value ||(hidePreferences.indexOf('messageTagging') != -1) ">

                                <label class="control-label col-md-8">Tagged Message Approver
                                    <i chToolTip="PREFET0004"></i></label>
                                <div class="col">
                                    <select class="form-control select2" data-placeholder="None Selected" id="taggedUser" multiple> Select
                                            <!--<option *ngFor="let staffUser of selectedUserList"  value="{{staffUser.id}}" selected  >{{staffUser.displayName}}</option>-->
                                             <option *ngFor="let user of selectedUserList"  value="{{user.id}}" [selected]="user.status ==true"   >{{user.displayName}} </option>
                                            <!--<option value="test" selected>test</option> <option value="test1" selected>test1</option>-->

                                        </select>
                                </div>

                            </div>
                            <!--/span-->

                        </div>
                        <div class="row">

                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('chatAutoTranslate')==-1">
                                <label class="control-label col-md-8">Language Translation
                                <i chToolTip="PREFET0005"></i></label>


                                <div class="btn-group col-md-3">

                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['chatAutoTranslate'].value}" (click)="togglePreference('chatAutoTranslate',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['chatAutoTranslate'].value}" (click)="togglePreference('chatAutoTranslate',false)">
                                                              No
                                         </button>

                                </div>

                            </div>

                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('infusionSupport')==-1">

                                <label class="control-label col-md-8">Show Clinical FAQs
                                <i chToolTip="PREFET0006"></i></label>


                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['chatInfusionSupport'].value}" (click)="togglePreference('chatInfusionSupport',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['chatInfusionSupport'].value}" (click)="togglePreference('chatInfusionSupport',false)">
                                                              No
                                         </button>

                                </div>


                            </div>
                            <div class="no-padding form-group  col-md-6" *ngIf="hidePreferences.indexOf('gATracking')==-1">

                                <label class="control-label  col-md-8">GA Tracking
                                <i chToolTip=""></i></label>


                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['gaTracking'].value}" (click)="togglePreference('gATracking',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['gaTracking'].value}" (click)="togglePreference('gaTracking',false)">
                                                              No
                                         </button>
                                </div>
                            </div>
                            <div class="no-padding form-group  col-md-6" *ngIf="hidePreferences.indexOf('watchTowerTracking') == -1">

                                <label class="control-label col-md-8">Watchtower Tracking
                                <i chToolTip=""></i></label>




                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['watchtowerTracking'].value}" (click)="togglePreference('watchtowerTracking',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['watchtowerTracking'].value}" (click)="togglePreference('watchtowerTracking',false)">
                                                              No
                                         </button>
                                </div>

                            </div>

                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('showPrototypes')== -1">
                                <label class="control-label col-md-8">Show Prototypes
                                <i chToolTip=""></i></label>



                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['showProtypes'].value}" (click)="togglePreference('showProtypes',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['showProtypes'].value}" (click)="togglePreference('showProtypes',false)">
                                                              No
                                         </button>
                                </div>

                            </div>
                            <!--<div  class="form-group col-md-6" >
                                <label class="control-label col-md-8">Show Infusion Support
                                <i chToolTip="PREFET0006"></i></label>
                            

                               
                                    <div class="btn-group col-md-2">
                                        <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['showInfusionSupport'].value}"
                                            (click)="togglePreference('showInfusionSupport',true)">
                                                            Yes
                                            </button>
                                        <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['showInfusionSupport'].value}"
                                            (click)="togglePreference('showInfusionSupport',false)">
                                                                No
                                            </button>
                                    </div>
                                
                            </div>-->


                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('messageTagging')==-1">
                                <label class="control-label col-md-8">Message Tagging
                                            <i chToolTip="PREFET0015"></i>  </label>



                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['messageTagging'].value}" (click)="togglePreference('messageTagging',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['messageTagging'].value}" (click)="togglePreference('messageTagging',false)">
                                                              No
                                         </button>
                                </div>

                            </div>
                            <div [hidden]="true" class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('userTagging')==-1">
                                <label class="control-label col-md-8">User Tagging
                                    <i class="user-tagging icmn-info" ></i> </label>



                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['userTagging'].value}" (click)="togglePreference('userTagging',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['userTagging'].value}" (click)="togglePreference('userTagging',false)">
                                                              No
                                         </button>
                                </div>

                            </div>



                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('documentTagging')==-1">
                                <label class="control-label col-md-8">Enable Signature Request
                                <i chToolTip="PREFET0009"></i></label>



                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['documentTagging'].value}" (click)="togglePreference('documentTagging',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['documentTagging'].value}" (click)="togglePreference('documentTagging',false)">
                                                              No
                                         </button>
                                </div>

                            </div>
                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('showChatHistoryToNewParticipant')==-1">
                                <label class="control-label col-md-8">Show Chat History To New Group Member
                                <i chToolTip="PREFET0010"></i></label>



                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['showChatHistoryToNewParticipant'].value}" (click)="togglePreference('showChatHistoryToNewParticipant',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['showChatHistoryToNewParticipant'].value}" (click)="togglePreference('showChatHistoryToNewParticipant',false)">
                                                              No
                                         </button>
                                </div>

                            </div>
                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('enableFilingCenter')==-1">
                                <label class="control-label col-md-8">Enable Filing Center
                                </label>



                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['enableFilingCenter'].value}" (click)="togglePreference('enableFilingCenter',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['enableFilingCenter'].value}" (click)="togglePreference('enableFilingCenter',false)">
                                                              No
                                         </button>
                                </div>

                            </div>

                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('enableMaskedDiscussionGroup')==-1">
                                <label class="control-label col-md-8">Enable Masked Discussion Group
                                </label>



                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['enableMaskedDiscussionGroup'].value}" (click)="togglePreference('enableMaskedDiscussionGroup',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['enableMaskedDiscussionGroup'].value}" (click)="togglePreference('enableMaskedDiscussionGroup',false)">
                                                              No
                                         </button>
                                </div>

                            </div>


                            <div class="no-padding form-group col-md-6" [hidden]="!accountPreferences.controls['patientSignupWithAdminApproval'].value">
                                <label class="control-label col-md-8">Show pending patients status in dashboard
                                <i chToolTip="PREFET0014"></i></label>

                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['showPatientStatusDashboard'].value}" (click)="togglePreference('showPatientStatusDashboard',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['showPatientStatusDashboard'].value}" (click)="togglePreference('showPatientStatusDashboard',false)">
                                                              No
                                         </button>
                                </div>

                            </div>
                            <div class="no-padding form-group col-md-6" [hidden]="true">
                                <label class="control-label col-md-8">Hide Patient Display Name		
                                </label>

                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['enableDisplayName'].value}" (click)="togglePreference('enableDisplayName',true)">		
                                                            Yes		
                                        </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['enableDisplayName'].value}" (click)="togglePreference('enableDisplayName',false)">		
                                                                No		
                                        </button>
                                </div>

                            </div>


                        </div>

                    </div>

                    <h5 class="text-black"><strong>Features </strong></h5>
                    <div class="form-actions"></div>

                    <div class="form-body">
                        <div class="row">
                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('patientDiscussionGroup')==-1">

                                <label class="control-label col-md-8">Allow Patient Discussion Group
                                    <i chToolTip="PREFET0011"></i></label>



                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['allowPatientDiscussionGroup'].value}" (click)="togglePreference('allowPatientDiscussionGroup',true)">
                                                            Yes
                                            </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['allowPatientDiscussionGroup'].value}" (click)="togglePreference('allowPatientDiscussionGroup',false)">
                                                                No
                                            </button>
                                </div>
                            </div>

                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('patientSelfInventory')==-1">

                                <label class="control-label col-md-8">Allow Patients To Create Supply Counts
                                    <i chToolTip="PREFET0012"></i></label>



                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['allowPatientSelfInventory'].value}" (click)="togglePreference('allowPatientSelfInventory',true)">
                                                            Yes
                                            </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['allowPatientSelfInventory'].value}" (click)="togglePreference('allowPatientSelfInventory',false)">
                                                                No
                                            </button>
                                </div>

                            </div>

                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('patientSignupWithAdminApproval')==-1">


                                <label class="control-label col-md-8">Patient Signup With Admin Approval
                                    <i class="self-inventory icmn-info" ></i>        </label>



                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['patientSignupWithAdminApproval'].value}" (click)="togglePreference('patientSignupWithAdminApproval',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['patientSignupWithAdminApproval'].value}" (click)="togglePreference('patientSignupWithAdminApproval',false)">
                                                              No
                                         </button>
                                </div>

                            </div>
                            <div class="no-padding form-group col-md-6" *ngIf="hidePreferences.indexOf('clinicianSignupWithAdminApproval')==-1">

                                <label class="control-label col-md-8">Clinician Signup With Admin Approval
                                    <i class="patient-initiate icmn-info" ></i>       </label>



                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['clinicianSignupWithAdminApproval'].value}" (click)="togglePreference('clinicianSignupWithAdminApproval',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['clinicianSignupWithAdminApproval'].value}" (click)="togglePreference('clinicianSignupWithAdminApproval',false)">
                                                              No
                                         </button>
                                </div>

                            </div>
                            <div class="no-padding form-group col-md-6 " *ngIf="hidePreferences.indexOf('patientsToInitiateChatDuringDay')==-1">

                                <label class="control-label col-md-8">Allow Patients to Chat 24 Hours
                                <i chToolTip="PREFET0013"></i></label>



                                <div class="btn-group col-md-2">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': accountPreferences.controls['patientsToInitiateChatDuringDay'].value}" (click)="togglePreference('patientsToInitiateChatDuringDay',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !accountPreferences.controls['patientsToInitiateChatDuringDay'].value}" (click)="togglePreference('patientsToInitiateChatDuringDay',false)">
                                                              No
                                         </button>
                                </div>
                            </div>

                        </div>


                        <div class="form-group row">
                            <label style="min-width:51%;" *ngIf="!accountPreferences.controls['patientsToInitiateChatDuringDay'].value" class="col-md-3 control-label"><b>Enter the Hours Patients are Permitted to Start Chats</b></label>
                            <label *ngIf="accountPreferences.controls['patientsToInitiateChatDuringDay'].value" class="col-md-3 control-label"><b>Branch Hours</b></label>
                        </div>


                        <div class=" form-group row">
                            <!-- *ngIf="!accountPreferences.controls['patientsToInitiateChatDuringDay'].value" -->
                            <label class="col-md-3 control-label">Start Time</label>
                            <div class="col">
                                <input class="form-control" type="text" formControlName="homeinfusionStart" id="homeinfusionStart" placeholder="hh:ss">
                                <!-- <div *ngIf="accountPreferences.controls['homeinfusionStart'].hasError('required')&&(accountPreferences.controls.homeinfusionStart?.dirty ||accountPreferences.controls.homeinfusionStart?.touched)" class="alert alert-danger">
                                    Not a valid Time!
                                </div> -->
                            </div>
                            <div class="col">
                                <select class="form-control" formControlName="homeInfusionStartTime" id="homeInfusionStartTime">
                                        <option value="12">AM</option>
                                        <option value="24">PM</option>
                                    </select>
                            </div>
                            <label class="col-md-3 control-label">End Time</label>
                            <div class="col">
                                <input class="form-control" type="text" formControlName="homeinfusionEnd" id="homeinfusionEnd" placeholder="hh:ss">
                                <!-- <div *ngIf="accountPreferences.controls['homeinfusionEnd'].hasError('required')&&(accountPreferences.controls.homeinfusionEnd?.dirty ||accountPreferences.controls.homeinfusionEnd?.touched)" class="alert alert-danger">
                                    Not a valid Time!
                                </div> -->
                            </div>
                            <div class="col">
                                <select class="form-control" formControlName="homeinfusionEndTime" id="homeinfusionEndTime">
                                        <option value="12">AM</option>
                                        <option value="24">PM</option>
                                    </select>
                            </div>
                        </div>


                        <div class="form-group row" *ngIf="hidePreferences.indexOf('patientsToInitiateChatDuringDay')== -1">
                            <label class="col-md-3 control-label" [hidden]="conversationTypeStatus == 1">Roles for "Chat with us Now"</label>
                            <label class="col-md-3 control-label" [hidden]="conversationTypeStatus != 1">Roles for "Topic Not Listed? Chat With Us Now"</label>
                            <div class="col-md-3">
                                <select class="form-control select2" data-placeholder="None Selected" formControlName="rolesChatWithUsNow" id="rolesChatWithUsNow" multiple>  
                                        <option *ngFor="let teanntRole of teanntRoles" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                    </select>
                            </div>
                            <label class="col-md-3 control-label">Roles for "Where Is My Delivery?"</label>
                            <div class="col-md-3">
                                <select class="form-control select2" data-placeholder="None Selected" formControlName="rolesWhereIsMyDelivery" id="rolesWhereIsMyDelivery" multiple>  
                                        <option *ngFor="let teanntRole of teanntRoles" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                    </select>
                            </div>
                        </div>
                        <div class="form-group row" *ngIf="hidePreferences.indexOf('patientsToInitiateChatDuringDay')== -1">
                            <label class="col-md-3 control-label">Roles for "When Is My Nurse Coming?"</label>
                            <div class="col-md-3">
                                <select class="form-control select2" data-placeholder="None Selected" formControlName="rolesWhenNurseArrive" id="rolesWhenNurseArrive" multiple>  
                                        <option *ngFor="let teanntRole of teanntRoles" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                    </select>
                            </div>
                            <label class="col-md-3 control-label">Roles for "I Need Help/I Have A Question"</label>
                            <div class="col-md-3">
                                <select class="form-control select2" data-placeholder="None Selected" formControlName="rolesIhaveQuestion" id="rolesIhaveQuestion" multiple>  
                                        <option *ngFor="let teanntRole of teanntRoles" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                    </select>
                            </div>
                        </div>
                        <div class="form-group row" *ngIf="hidePreferences.indexOf('patientsToInitiateChatDuringDay')== -1">
                            <label class="col-md-3 control-label">Roles for "Default Users"</label>
                            <div class="col-md-3">
                                <select class="form-control select2" data-placeholder="None Selected" formControlName="rolesDefaultNurses" id="rolesDefaultNurses" multiple>  
                                        <option *ngFor="let teanntRole of teanntRoles" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                    </select>
                            </div>
                        </div>

                    </div>

                    <h5 class="text-black" [hidden]="!accountPreferences.controls['chatInfusionSupport'].value"><strong>Conversation Type</strong></h5>
                    <div class="form-actions" [hidden]="!accountPreferences.controls['chatInfusionSupport'].value"></div>
                    <div class="form-body" [hidden]="!accountPreferences.controls['chatInfusionSupport'].value">
                        <div class="form-group row">
                            <label class="col-md-3 control-label">Conversation Type</label>
                            <div class="col-md-3">
                                <select class="form-control" data-placeholder="None Selected" formControlName="conversationType" (change)="toggleConversationType($event.target.value)">
                                        <option value="0" selected>Direct Chat</option>
                                        <option value="1" >Patient Choice</option>
                                </select>
                            </div>
                            <label class="col-md-3 control-label" [hidden]="conversationTypeStatus != 1">Initiation Message</label>
                            <div class="col-md-3" [hidden]="conversationTypeStatus != 1">
                                <input type="text" id="chat_role2_displayname" formControlName="initiationMessage" class="form-control" value="">
                            </div>
                        </div>
                        <div class="form-group row" [hidden]="conversationTypeStatus != 1">
                            <label class="col-md-3 control-label">Conversation Role 1 Display Name</label>
                            <div class="col-md-3">
                                <input type="text" id="chat_role1_displayname" formControlName="chatRole1DisplayName" class="form-control" value="">
                            </div>
                            <label class="col-md-3 control-label">Conversation Role 2 Display Name</label>
                            <div class="col-md-3">
                                <input type="text" id="chat_role2_displayname" formControlName="chatRole2DisplayName" class="form-control" value="">
                            </div>
                        </div>
                        <div class="form-group row" [hidden]="conversationTypeStatus != 1">
                            <label class="col-md-3 control-label">Conversation Roles 1</label>
                            <div class="col-md-3">
                                <select class="form-control select2" data-placeholder="None Selected" formControlName="chatRole1" id="chatRole1" multiple>  
                                    <option *ngFor="let teanntRole of teanntRoles" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                </select>
                            </div>
                            <label class="col-md-3 control-label">Conversation Roles 2</label>
                            <div class="col-md-3">
                                <select class="form-control select2" data-placeholder="None Selected" formControlName="chatRole2" id="chatRole2" multiple>  
                                    <option *ngFor="let teanntRole of teanntRoles" value="{{teanntRole.id}}"> {{teanntRole.name}} </option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group row" [hidden]="conversationTypeStatus != 1">
                            <label class="col-md-3 control-label">Conversation Roles 1 Message Template</label>
                            <div class="col-md-3">
                                <textarea type="text" class="form-control" formControlName="chatRole1MessageTemplate"></textarea>
                            </div>
                            <label class="col-md-3 control-label">Conversation Roles 2 Message Template</label>
                            <div class="col-md-3">
                                <textarea type="text" class="form-control" formControlName="chatRole2MessageTemplate"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" [disabled]="!accountPreferences.valid || isDisabled" (click)="updateSettings();" class="btn btn-primary">Submit</button>
                        <button type="button" [routerLink]="['/inbox']" class="btn btn-default">Cancel</button>
                    </div>
                </form>
                <!-- End Horizontal Form -->
            </div>
        </div>

    </div>
</section>
<!-- END: forms/basic-forms-elements -->