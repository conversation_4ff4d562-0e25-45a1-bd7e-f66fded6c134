import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Component({
  selector: 'app-clinicain-role',
  templateUrl: './clinician-role.html'
})

export class ClinicianRoleComponent implements OnInit {
  roleList = [];
  constructor(private route: ActivatedRoute,
    private router: Router,
    private _structureService: StructureService
  ) { }
  ngOnInit() {

    $(function () {

      $('.select2-tags').select2({
        tags: true,
        tokenSeparators: [',', ' ']
      });
    });
    this._structureService.getClinicianRoles().refetch().then(({ data }) => {
      this.roleList = data['getClinicianRoles'];
    });

  }
}

