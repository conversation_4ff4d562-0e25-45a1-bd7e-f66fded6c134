<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Accounts (List Works [Read], Create, Update, Delete TODO [Write])</strong>
            <a [routerLink]="['/account/settings']" class="pull-right btn btn-sm btn-primary">Add Account <i class="ml-1"><!-- --></i></a>
        </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']" >Home</a></li>
            <li class="breadcrumb-item">Account</li>
            
        </ol>
        <div class="row">
            <div class="col-lg-12">
                <!-- <h5 class="text-black"><strong>Manage Tenants</strong></h5>
                <p class="text-muted">Element: read <a href="https://datatables.net/" target="_blank">official documentation<small><i class="icmn-link ml-1"></i></small></a></p> -->
                <div class="mb-5">
                    <table class="table table-hover nowrap" id="example1" width="100%">
                        <thead>
                            <tr>
                                <th> # </th>
                                <th> Account Name </th>
                                <!--<th>  </th>-->
                            </tr>
                        </thead>                        
                        <tbody>
                            <tr class="odd gradeX" *ngFor="let account of accounts.data; let i = index"  routerLink="/account/settings/{{account.id}}">
                                <td> {{i+1}} </td>
                                <td> {{account.name}} </td>			
                                <!--<td><a routerLink="/account/settings/{{account.id}}" ><i class="icmn-eye"></i></a></td>-->
                            </tr>
                        </tbody>
                        <tbody *ngIf="totalAccounts == 0">
                              <tr ><td colspan="6">No records Found</td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>        
    </div>
</section>
<!-- END: tables/datatables -->
