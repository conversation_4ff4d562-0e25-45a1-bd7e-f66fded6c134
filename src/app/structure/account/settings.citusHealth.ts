import { Component, OnInit } from '@angular/core';
import { StructureService } from '../structure.service';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import * as io from "socket.io-client";
declare var $: any;
declare var swal: any;

@Component({
  selector: 'app-account-settings',
  templateUrl: './settings.html'
})

export class SettingsComponent implements OnInit {
  hiddenField: false;
  selectedOptions = [];
  defaultUserList = [];
  defaultUsers = [];
  selectedUsers;
  selectedDefaultUsers;
  accountId;
  isDisabled=false;
  member;
  userList;
  defaultUserError = false;
  selectedUserList;
  account = {
    key: '',
    name: '',
    tenantConfigValue: {
      registrationId: ''
    },
    configurationSettings: []
  };
  userInfo;
  userData;
  userRole;
  previlages = {
    superAdmin: false,

  };
  accountSettings: FormGroup;
  errorMessageAccountName = 'Account Name cannot be empty';
  errorMessageRegId = 'Registration Id cannot be empty';
  errorMessageAccountKey = 'Account Key cannot be empty';
  pageType = 'add';
  settingsupdated = false;
   hidePreferences = [];

  constructor(
    private _structureService: StructureService,
    private route: ActivatedRoute,
    private router: Router,
    private _formBuild: FormBuilder
  ) {
    route.params.subscribe(val => {
      this.accountId = this.route.snapshot.params['accountId'];
      if (typeof (this.accountId) !== 'undefined') {
        this.pageType = 'edit';
        this.getAccount(this.accountId);
      }
    });

    route.params.subscribe(val => {
      this.userInfo = this._structureService.loginUserDetails;
      this.accountId = this.userInfo.tenantId;
      this.accountId = typeof (this.accountId) === 'undefined' ? this._structureService.getCookie('tenantId') : this.accountId;
      this.userRole = typeof (this.userInfo.roleName) === 'undefined' ? this._structureService.getCookie('userRole') : this.userInfo.roleName;
      if (this.userRole === 'Super Admin') {
        this.previlages.superAdmin = true;
      }
    })
  }

  ngOnInit() {

 
    var config            = this._structureService.userDataConfig;
    var userDetails       = this._structureService.userDetails;
    var configData        = JSON.parse(config);
    this.userData = JSON.parse(userDetails);

    $('.select2').select2({
        placeholder: function(){
            $(this).data('placeholder');
        }
      }
    );
    $('#defaultUser').on(
      'change',
      (e) => {
        this.selectedOptions = [];
        this.selectedOptions = $(e.target).val();
      }
    );
    this.setFormControl();
  }

  setFormControl() {
    this.accountSettings = this._formBuild.group({
      registrationId: [''],
      tenantKey: [''],
      name: ['', [Validators.required, this.noWhitespaceValidator]],
      officeEmail: [''],
      helplinePhone: [''],
      contactEmail: [''],
      officePhone: [''],
      address: [''],
      escalationTime: ['', Validators.required],
      
      noClinicianMessage: ['', [Validators.required, this.noWhitespaceValidator]],
      
      
      defaultUser: [''],
      escalationBehaviour:[''],
      
    });
  }

  setSelected(event) {
    console.log(event);
  }

  getAccount(accountId) {
    this._structureService.getAccount(accountId).then((
       data ) => {
      if (data['getSessionTenant']) {
        this.account = data['getSessionTenant'];
        if (this.account.configurationSettings.length>0) {
           this.account.configurationSettings.forEach(element => {
              if (element.disable) {
                  this.hidePreferences.push(element.configuration);
              }
              
           });
        }
       
        this.accountSettings.patchValue({
          escalationTime: this.account['escalationTimeInSec'],
          defaultUser: this.account['defaultUserToReceiveEscalatedMessage'],
          escalationBehaviour: this.account['messageEscalationBehavior'],
        
          
        });
        
        if (this.account['patientsToInitiateChatDuringDay']){
          this.accountSettings.patchValue({
              noClinicianMessage :this.account['noClinicianMessage']
          });
           
        } else {
           this.accountSettings.patchValue({
              noClinicianMessage : this.account['noClinicianMessageDuringWorkingHours']
           });
        }
        this.userList = this.account['staffUsers'];
        if (this.account['defaultUserToReceiveEscalatedMessage'].length > 0) {
          var idString = '';
          var idArray = [];
          for (var k = 0; k < this.account['defaultUserToReceiveEscalatedMessage'].length; k++) {
            this.selectedOptions.push(this.account['defaultUserToReceiveEscalatedMessage'][k].id);
          }
        }
        this.selectedDefaultUsers = this.account['defaultUserToReceiveEscalatedMessage'];
        for (var i = 0; i < this.account['staffUsers'].length; i++) {
          this.member = {
            id: "",
            displayName: ""
          };
          this.member.id = this.account['staffUsers'][i].id;
          this.member.displayName = this.account['staffUsers'][i].displayName;
          this.defaultUserList.push(this.member);
        }
        for (var j = 0; j < this.account['defaultUserToReceiveEscalatedMessage'].length; j++) {
          this.member = {
            id: "",
            displayName: ""
          };
          this.member.id = this.account['defaultUserToReceiveEscalatedMessage'][j].id;
          this.member.displayName = this.account['defaultUserToReceiveEscalatedMessage'][j].displayName;
        }
        this.selectedUserList = [];
        for (var i = 0; i < this.account['staffUsers'].length; i++) {
          var ismatch = false;
          if (this.selectedDefaultUsers.length > 0) {
            for (var j = 0; j < this.account['defaultUserToReceiveEscalatedMessage'].length; j++) {
              if (this.account['staffUsers'][i].id == this.account['defaultUserToReceiveEscalatedMessage'][j].id) {
                var obj = { 'id': this.account['staffUsers'][i].id, 'displayName': this.account['staffUsers'][i].displayName, 'status': true };
                this.selectedUserList[i] = obj;
                break
              } else {
                var obj = { 'id': this.account['staffUsers'][i].id, 'displayName': this.account['staffUsers'][i].displayName, 'status': false };
                this.selectedUserList[i] = obj;
              }
            }
          } else {
            var obj = { 'id': this.account['staffUsers'][i].id, 'displayName': this.account['staffUsers'][i].displayName, 'status': false };
            this.selectedUserList[i] = obj;
          }

        }
        setTimeout(() => {
          $('#defaultUser').select2('data', this.selectedUsers);
        }, 1000);
      } else {
        this._structureService.deleteCookie('authenticationToken');
        this.router.navigate(['/login']);
      }
    }
    );
  }

  noWhitespaceValidator(control: FormControl) {
    let isWhitespace = (control.value || '').trim().length === 0;
    let isValid = !isWhitespace;
    return isValid ? null : { 'whitespace': true }
  }

  updateSettings() {

    swal({
      title: "Are you sure?",
      text: "You are updating the general settings configuration",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Ok",
      closeOnConfirm: true
    }, () => {

      if (this.selectedOptions) {
        this.selectedOptions.forEach(element => {
          this.member = { id: "" };
          var id = element.substr(element.indexOf(":") + 1);
          id = id.replace(/'/g, "");
          this.member.id = id.replace(/\s/g, '');
          this.defaultUsers.push(this.member);
        });
      }
      this.accountSettings.patchValue({
        defaultUser: this.defaultUsers
      });
     
       let formObjData = this.accountSettings.value;
        if (this.account['patientsToInitiateChatDuringDay']){
           formObjData.noClinicianMessageDuringWorkingHours = this.account['noClinicianMessageDuringWorkingHours'];
           formObjData.noClinicianMessages = this.accountSettings.value['noClinicianMessage'];
       
           
        } else {
        
          formObjData.noClinicianMessages = this.account['noClinicianMessage']; 
          formObjData.noClinicianMessageDuringWorkingHours =this.accountSettings.value['noClinicianMessage'];  

          
          
        }
        console.log(formObjData);
        var updatedBy = {
          displayName : this.userData.displayName,
          userid : this.userData.userId ?this.userData.userId : 0,
        };
        console.log("loginUserDetails- ",this.userData, updatedBy);
        var updateConfigPollingtoServer = {
          configurationType: "updateTenant",
          role: 0,
          tenantid: this._structureService.getCookie('tenantId'),
          updatedBy: updatedBy,
        };  
        var self = this; 
      if (this.pageType === 'edit') {
        this.isDisabled=true;
        this._structureService.updateAccount(formObjData).then(
          (data) => {
            this.isDisabled=false;
            var notify = $.notify('Success! Account updated');
            setTimeout(function () {
              notify.update({ 'type': 'success', 'message': '<strong>Success! Account updated</strong>' });
              self._structureService.socket.emit("updateConfigPollingtoServer", updateConfigPollingtoServer);
            }, 1000);
          }
        );
      } else {
        this.isDisabled=true;
        this._structureService.createAccount(formObjData).subscribe(
          (data) => {
            this.isDisabled=false;
            var notify = $.notify('Success! Account updated');
            setTimeout(function () {
              notify.update({ 'type': 'success', 'message': '<strong>Success! Account updated</strong>' });
            }, 1000);
          }
        );
      }
    });
  }
}

