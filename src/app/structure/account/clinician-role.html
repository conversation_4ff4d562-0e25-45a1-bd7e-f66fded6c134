<!-- START: forms/basic-forms-elements -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Clinician Role (Wireframe)</strong>
            <!-- <a href="https://v4-alpha.getbootstrap.com/components/forms/" target="_blank" class="btn btn-sm btn-primary ml-2">Official Documentation <i class="icmn-link ml-1"></i></a> -->
        </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/dashboard']" >Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/accounts']" >Account</a></li>
            <li class="breadcrumb-item">Clinician Role</li>
        </ol>
        <div class="row">
            <div class="col-lg-12">
                <div class="mb-5">
                    <!-- <h5 class="text-black"><strong>Horizontal Form</strong></h5>
                    <p class="text-muted">Element: read <a href="https://v4-alpha.getbootstrap.com/components/forms/" target="_blank">official documentation<small><i class="icmn-link ml-1"></i></small></a></p> -->
                    <!-- Horizontal Form -->

                    <form action="#">
                        <div class="form-body">
                            <!-- <h3 class="form-section">Clinician Role</h3> -->
                            
                            <div class="row">
                            
                           
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Clinician Roles for "Chat With Us Now"</label>
                                    <select class="form-control select2-tags" multiple> Select 
                                        <option> None Selected </option>
                                        <option *ngFor="let role of roleList" value="role.id">  {{role.displayName}} [{{role.userRole.roleName}}] </option>
                                       
                                    </select>
                                </div>
                            </div>
                            <!--/span-->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Clinician Roles for "I Need Help/I Have a Question"</label>
                                    <select class="form-control select2-tags" multiple> Select 
                                        <option> None Selected </option>
                                        <option *ngFor="let role of roleList" value="role.id">  {{role.displayName}} [{{role.userRole.roleName}}] </option>
                                        
                                    </select>
                                </div>
                            </div>
                            <!--/span-->
                        </div>

                        <div class="row">
                            
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Clinician Roles for "Where is My Delivery"</label>
                                    <select class="form-control select2-tags" multiple> Select 
                                        <option> None Selected </option>
                                      <option *ngFor="let role of roleList" value="role.id">  {{role.displayName}} [{{role.userRole.roleName}}] </option>
                                        
                                    </select>
                                </div>
                            </div>
                            <!--/span-->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Clinician Roles for "When will My Nurse Arrive"</label>
                                    <select class="form-control select2-tags" multiple> Select 
                                        <option> None Selected </option>
                                        <option *ngFor="let role of roleList" value="role.id">  {{role.displayName}} [{{role.userRole.roleName}}] </option>
                                        
                                    </select>
                                </div>
                            </div>
                            <!--/span-->
                        </div>

                        <div class="row">
                            
                            
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Default Clinician Roles</label>
                                    
                                    <select class="form-control select2-tags" multiple> Select 
                                       <option> None Selected </option>
                                       <option *ngFor="let role of roleList" value="role.id">  {{role.displayName}} [{{role.userRole.roleName}}] </option>
                                    </select>
                                </div>
                            </div>
                            <!--/span-->
                           
                            <div class="col-md-6 ">
                                <div class="form-group">
                                    <label>No Clinician Message</label>
                                    <textarea type="text" class="form-control">No clinicians are scheduled After working hours. Please contact CitusHealth Infusion Services office at <a href='tel:**************'>**************</a> for assistance.</textarea>
                                </div>
                            </div>
                        </div>
                            
                            
                        </div>
                         <div class="form-actions">
                            <button type="button" class="btn btn-primary">Submit</button>
                            <button type="button" class="btn btn-default">Cancel</button>
                        </div>
                    </form>
                    <!-- End Horizontal Form -->
                </div>
            </div>
        </div>
        
        
      
        
        
    </div>
</section>
<!-- END: forms/basic-forms-elements -->