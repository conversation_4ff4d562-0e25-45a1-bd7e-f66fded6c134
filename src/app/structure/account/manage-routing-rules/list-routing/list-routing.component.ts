import { Component, OnInit,  ElementRef, Renderer } from '@angular/core';
import { ManageRoutingRulesService } from './../manage-routing-rules.service';
import { StructureService } from '../../../structure.service';
import { isInteger } from "@ng-bootstrap/ng-bootstrap/util/util";
import { isBlank } from 'app/utils/utils';
declare var $: any;
declare var swal: any;
declare var NProgress: any;

@Component({
  selector: 'app-list-routing',
  templateUrl: './list-routing.component.html',
  styleUrls: ['./list-routing.component.css']
})
export class ListRoutingComponent implements OnInit {
    dTable;
    datam;
    searchText = "";
    dataLoadingMsg = true;
    editRule : boolean;
    totalCount = 50;
    routingRuleId: any;
  constructor( private _manageRoutingRulesService : ManageRoutingRulesService,
    private _structureService: StructureService,
    elementRef: ElementRef,  
    renderer: Renderer,) {
    renderer.listen(elementRef.nativeElement, "click", (event, target) => {
        if(event.target.id == 'deletegrp') {
            this.deleteRule(this.routingRuleId.id);
           }
        if (event.target.id == "editgrp" || event.target.id == "editgrp-icon") {
            this._structureService.setCookie(
                "routingRuleId",
                this.routingRuleId.id,
                1
            );
            this.editRule = true;
           $('#addModal').modal({ backdrop: 'static', keyboard: false });
        }
    });
   }

  ngOnInit() {
    this.listRoutingRules();
  }
  listRoutingRules(): void {
    this.dataLoadingMsg = true;
    var self = this;
    let datas: any;
    if (this.dTable) {
        this.dTable.destroy();
    }

    // Array holding selected row IDs
    $(() => {
        this.dTable = $("#routing-rules").DataTable({
            autoWidth: false,
            responsive: true,
            bprocessing: true,
            bServerSide: true,
            bpagination: true,
            bsorting: true,
            retrieve: true,
            bInfo: true,
            lengthMenu: [
                [5, 50],
                [5, 50]
            ],
            
            fnDrawCallback: function (oSettings) {
                if (
                    oSettings._iRecordsTotal == 0 ||
                    oSettings._iRecordsTotal < oSettings._iDisplayLength ||
                    oSettings.aoData.length == 0
                ) {
                    $(".dataTables_paginate").hide();
                } else {
                    $(".dataTables_paginate").show();
                }
                if (oSettings.aoData.length == 0) {
                    $(".dataTables_info").hide();
                }
            },
            fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
                $(nRow).on("click", () => {


                    console.log("KKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK", iDisplayIndex);
                    console.log(aData);
                    console.log("KKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK", iDisplayIndexFull);


                    this.routingRuleId = aData;
                });
            },
            dom:
                "<'row'<'col-sm-4 'l><'col-sm-4'f><'col-sm-2 searchButton'>>" +
                "<'row'<'col-sm-12'tr>>" +
                "<'row'<'col-sm-5'i><'col-sm-7'p>>",
            initComplete: function () {
                $(".dataTables_filter label input").attr("placeholder", "Search");
                $(".dataTables_filter label input").unbind();
                $("div.dataTables_filter input").on("keydown", function (e) {
                    if (e.which == 13) {
                        var value = $("div.dataTables_filter input").val();
                        if (value) {
                            value = value.replace("â", '"');
                            value = value.replace("â", "'");
                            value = value.replace("â", "'");
                            self.dTable.search(value).draw();
                        } else {
                            self.dTable.search("").draw();
                        }
                    }
                });
                $(".list-tooltip").tooltip({
                    title:"Sort by Route Type"
                });
                $("div.dataTables_filter input").on("keypress", function (e) {
                    $(".searchBView").prop("disabled", false);
                });
                $("div.dataTables_filter input").on("keyup", function (e) {
                    var value = $("div.dataTables_filter input").val();
                    if (value) {
                    } else $(".searchBView").prop("disabled", true);
                });
                $("div.searchButton").html(
                    `<button disabled="true" class="btn btn-sm btn-info searchBView" title="Search" type="submit">Search</button>
            <button style="margin-left:10px;" class="btn btn-sm btn-default resetBView" title="Reset" type="submit">Reset</button>`
                );
                var value = $("div.dataTables_filter input").val();
                if (value) {
                    $(".searchBView").prop("disabled", false);
                }
                $("div.dataTables_filter input").on("paste", function (event) {
                    var element = this;
                    var text;
                    setTimeout(function () {
                        text = $(element).val();
                        if (text) {
                            $(".searchBView").prop("disabled", false);
                        }
                    }, 100);
                });
                $(".buttons-collection").click(function (event) {
                    setTimeout(function () {
                        if ($(".buttons-collection").attr("aria-expanded") === "true" && $(".dt-button-collection").find("button").length == 0) {
                            $(".dt-button-collection").remove();
                            $(".dt-button-background").remove();
                            $(".buttons-collection").attr("aria-expanded", "false");
                        }
                    }, 500);
                });

            },
            ajax: function (dat, callback, settings) {
                self.dataLoadingMsg = true;
                NProgress.start();
                let orderData;
                let searchText;
                let orderby;
                let limit;

                var i = dat.order[0].column ? dat.order[0].column : "";
                orderby = dat.order[0].dir ? dat.order[0].dir : "";
                if (isInteger(i)) {
                    orderData = dat.columns[i].data ? dat.columns[i].data : "";
                } else {
                    orderData = 'id';
                }
                searchText = dat.search.value ? dat.search.value : "";
                self._manageRoutingRulesService.RoutingRuleList(
                        dat.length ? dat.length : this.totalCt,
                        dat.start,
                        orderData,
                        orderby,
                        searchText
                    )
                    .then(resultData => {
                        self.dataLoadingMsg = false;
                        NProgress.done();
                        datas = [];
                        self.datam = {};
                        datas =  (resultData['RoutingRuleList'] && resultData['RoutingRuleList']["data"])
                        ? resultData['RoutingRuleList']["data"]
                        : [];
                        if (dat.start == 0) {
                            this.totalCt = resultData["RoutingRulePagination"].totalCount;
                        }
                        let draw;
                        let total;

                        if (datas && datas.length == 0 && searchText == '') {
                            draw = 0;
                            total = 0;
                        } else {
                            draw = dat.draw;
                            total = this.totalCt;
                        }

                        self.datam = {
                            draw: draw,
                            recordsTotal: total,
                            recordsFiltered: total,
                            aaData: datas
                        };
                        callback(self.datam);
                    });
            },
            columns: [

                { title: "Routing Rule Name" , data: "name"},
                { title: "Roles", data: "receiveRoles" },
                { title: "Status", data : "enabled" },
                { title: "Patient Message Route By&nbsp;<i class='list-tooltip icmn-info' data-toggle='tooltip' data-placement='right'></i>", data : "routingType"},
                { title: "Allow Patient to Select Role", data: "enabledRoleSelection"},
                { title: "Actions" }
            ],
            columnDefs: [
                {
                    targets: 0,
                    searchable: false,
                    orderable: true,
                    width: "15%",
                    className: "dt-body-center",
                    render: (document, type, row) => {
                        if (row.name) {
                            return row.name;
                        } else {
                            return "";
                        }
                    }
                },
                {
                    data: null,
                    targets: 1,
                    width: "10%",
                    orderable: true,
                    render: function (data, type, row) {
                        if (row.receiveRoles) {
                            return row.receiveRoles;
                        } else {
                            return "";
                        }
                    }
                },
                {
                    data: null,
                    targets: 2,
                    width: "10%",
                    orderable: true,
                    render: function (data, type, row) {
                        if (!isBlank(row.enabled)) {
                          if(row.enabled === 1){
                              return 'Enabled';
                          }else{
                              return 'Disabled';
                          }
                        } else {
                            return "";
                        }
                    }
                },
                {
                    data: null,
                    targets: 3,
                    width: "20%",
                    orderable: true,
                    render: function (data, type, row) {
                        if (row.routingTypeName) {
                            return row.routingTypeName;
                        } else {
                            return "";
                        }
                    }
                },
                {
                    data: null,
                    targets: 4,
                    width: "20%",
                    orderable: true,
                    render: function (data, type, row) {
                        if (!isBlank(row.enabledRoleSelection)) {
                            if(row.enabledRoleSelection === 1){
                                return 'ON';
                            }else{
                                return 'OFF';
                            }
                          } else {
                              return "";
                          }
                    }
                },

                {
                    data: null,
                    orderable: false,
                    render: (data, type, row) => {
                        let actions = '';

                        actions += `<a id="editgrp" href="javascript: void(0);" class="cat__core__link--underlined mr-3"><i id="editgrp" class="icmn-pencil"></i> Edit</a>
                  <a id="deletegrp" href="javascript: void(0);" class="cat__core__link--underlined mr-3"><i id="deletegrp" class="icmn-cross"></i> Delete</a>
                  `;

                        return actions;
                    },
                    width: "14%",
                    targets: 5
                }
            ],
            language: {
                emptyTable: "No item found."
            },
              order: [[5,"desc"]]
        });

        $(document).on("click", ".resetBView", event => {
            self.dTable.search("").draw();
            $(".searchBView").prop("disabled", true);
        });
        $(document).on("click", ".searchBView", event => {
            var value = $(
                "#routing-rules_wrapper #routing-rules_filter label input"
            ).val();
            if (value) {
                value = value.replace("â", '"');
                value = value.replace("â", "'");
                value = value.replace("â", "'");
                self.dTable.search(value).draw();
            } else {
                self.dTable.search("").draw();
            }
        });
    });

}
addRoutingRule() {
    $('#addModal').modal({ backdrop: 'static', keyboard: false });
}
createRule(data) : void {
    if(data.ruleCreated){
        this.listRoutingRules(); 
    }
}
deleteRule(id){
    swal({
        title: "Are you sure?",
        text: "You are going to delete this rule",
        type: "warning",
        showCancelButton: true,
        cancelButtonClass: "btn-default",
        confirmButtonClass: "btn-warning",
        confirmButtonText: "Ok",
        closeOnConfirm: true
      },(confirm) => {
        if(confirm) {
            this._manageRoutingRulesService.deleteRoutingRule(id).subscribe((data)=>{
                if(data  && data['data'] && data['data']['deleteRoutingRule'].message){
                    var notify = $.notify('Success! Message routing rule deleted');
                    setTimeout(function () {
                      notify.update({
                        'type': 'success',
                        'message': '<strong>Success! Message routing rule deleted</strong>'
                      });
                    });
                    var activityData = {
                        activityName: "Routing rule deletion success",
                        activityType: "Manage Routing rules",
                        activityDescription: data['data']['deleteRoutingRule'].message
                     };
                    this.listRoutingRules();
                }else{
                    var activityData = {
                        activityName: "Routing rule deletion failed",
                        activityType: "Manage Routing rules",
                        activityDescription: data['data']['deleteRoutingRule'].message
                     };
                    $.notify('Message routing rule deletion failed', 'danger');
                }
                this._structureService.trackActivity(activityData);
            });
        }
      });
  
}

}
