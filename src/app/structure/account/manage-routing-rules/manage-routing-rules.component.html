<style>
    .title-class{
        font-size: 20px;
    }
    .content-position{
        margin-top: 35px;
    }
    .disabled {
    pointer-events:none; 
    opacity:0.6;
    }
    
</style>
<div class="col-lg-12">
    <div class="mb-5">
        <div class="row">
            <label class="col-md-4 ">{{ 'LABELS.MESSAGE_ROUTING_TYPE' | translate }} <i
                chToolTip="Message Routing Type"></i></label>
                <div class="col-md-8 input-div-class" style="margin-left:-40px;"> 
                    <label class="form-check-label">
                    <input type="radio" id="routes" value="user-tag" onclick="return false;" name="routes" class="" [checked]="routingType == 'schedule'" (click)="selectRoutingType('schedule')">
                    Calendar Schedules Based</label>
                    <label class="form-check-label">
                    <input type="radio" id="routes" value="site" onclick="return false;" name="routes" class="" [checked]="routingType == 'rule'" (click)="selectRoutingType('rule')">
                    Rules Based</label>
                </div>
        </div>
        <div class="nav-tabs-horizontal" style="margin-top: 20px;">
            <ul class="nav nav-tabs mb-4" role="tablist">
                <li id="user-reg-patient-tab">
                    <ul class="nav nav-tabs">
                        <li class="nav-item">
                            <a class="nav-link" [ngClass]="{ active: routingTypeTab === 'schedule' }"
                                (click)="showActiveConfig('schedule')" data-target="#tabschedule" data-toggle="tab"
                                href="javascript: void(0);" role="tab" aria-expanded="true"> Calendar Schedule Based Routing Configuration</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" [ngClass]="{ active: routingTypeTab === 'rule' }" (click)="showActiveConfig('rule')"
                                data-target="#tabrule" data-toggle="tab" href="javascript: void(0);" role="tab"
                                aria-expanded="false"> Rule Based Routing
                                Configuration
                            </a>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
        <div *ngIf="routingTypeTab === 'rule'">
        <app-list-routing></app-list-routing>
        </div>
        <div *ngIf="routingTypeTab === 'schedule'">
             <app-routing-schedule-calendar></app-routing-schedule-calendar>
        </div>
        </div>
       