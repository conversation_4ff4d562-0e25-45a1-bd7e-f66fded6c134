import { Component, OnInit, ElementRef, Renderer, Input, Output, EventEmitter } from '@angular/core';
import { Router } from '@angular/router';
import { StructureService } from '../../structure.service';
import { ManageRoutingRulesService } from './manage-routing-rules.service';
import { EnrollService } from '../../user-registration/enroll.service';
import { DatePipe } from "@angular/common";
import { ToolTipService } from '../../tool-tip.service';
import { isInteger } from "@ng-bootstrap/ng-bootstrap/util/util";
import { isBlank } from 'app/utils/utils';
declare var $: any;
declare var swal: any;
declare var NProgress: any;

@Component({
    selector: 'app-manage-routing-rules',
    templateUrl: './manage-routing-rules.component.html',
    styleUrls: ['./manage-routing-rules.component.css']
})
export class ManageRoutingRulesComponent implements OnInit {
    dTable;
    datam;
    searchText = "";
    dataLoadingMsg = true;
    totalCount = 50;
    routingRuleId: any;
    showAddPage:boolean;
    routingType : string='';
    routingTypeTab : string ='';
    editRule : boolean;
    staffRoles: any;
    userData;
    rolesToReceivePatientChats: any;
   
    constructor(private router: Router,
        private _structureService: StructureService,
        private _toolTipService: ToolTipService,
        private _manageRoutingRulesService : ManageRoutingRulesService,
        elementRef: ElementRef,
        private _enrollService: EnrollService,
        renderer: Renderer,) {
    }

    ngOnInit() {
        console.log("routingType-1",this.routingType);
        var userDetails = this._structureService.userDetails;
        this.userData = JSON.parse(userDetails);
        /* this._enrollService.getTenantRolesByPrivilege('allowEnrollmentInitiation').then((data: any) => {
            this.staffRoles = data.filter(a => a.citus_role_id !== '20');
        });
        $('#roles-to-receive-patient-chats').select2({
            placeholder: function () {
              $(this).data('placeholder');
            }
          }); */
        this._structureService.getUpdateTenantConfigs().then((config) => {
            if(config['getSessionTenant'] && config['getSessionTenant']['getTenantConfigData'] && config['getSessionTenant']['getTenantConfigData']['configs'] && config['getSessionTenant']['getTenantConfigData']['configs'][0])
            this.routingType = config['getSessionTenant']['getTenantConfigData']['configs'][0].value;
            if(localStorage.getItem('calender-Schedule') === "true")
            this.routingTypeTab = (localStorage.getItem('calender-Schedule') === "true") ? 'schedule' : 'schedule';
            else
            this.routingTypeTab = this.routingType;
        });
    }
   

  
    addScheduleRoutingRule() {
        $('#addModalSchedule').modal({ backdrop: 'static', keyboard: false });
    }
    selectRoutingType(type) {
        console.log("type",type,this.routingType);
        if(this. routingType != type){
        var self = this;
        swal(
        {
            title: "Are you sure?",
            text:
            type == "rule"
                ? self._toolTipService.getTranslateData('MESSAGES.RULE_SWAL_MESSAGE')
                : self._toolTipService.getTranslateData('MESSAGES.SCHEDULE_SWAL_MESSAGE'),
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            confirmButtonText: "Yes",
            cancelButtonText: "No",
            closeOnConfirm: true,
        },
        function (isConfirm) {  
        console.log("resultttttttttttttttttttttt",isConfirm);
        if (isConfirm) {    
        console.log("innnnnnnnnnnnnnnnnnnnnnnnn")
        self.routingType = type;  
        self.routingTypeTab = self.routingType;
        let data = {
            "configValue":type
        };
        self._structureService.createUpdateTenantConfigs(data).then((config) => {
            console.log("type",config);
            if(config['createUpdateTenantConfigs'] && config['createUpdateTenantConfigs'].status && config['createUpdateTenantConfigs'].status == true){
                var message = "Success! Message Routing Type updated";
                var notify = $.notify(message);
                setTimeout(function () {
                notify.update({
                    'type': 'success',
                    'message': '<strong>'+message+'</strong>'
                });
                }, 500);
            const activityData = {
              activityName: 'Message Routing Type Update',
              activityType: "Message Routing Type",
              activityDescription: `Message Routing Type by `+ self.routingType +` by the user`+ self.userData.displayName,
            };
            self._structureService.trackActivity(activityData);
            }else{
                var message = "Failed! Message Routing Type update failed";
                var notify = $.notify(message);
                setTimeout(function () {
                notify.update({
                    'type': 'success',
                    'message': '<strong>'+message+'</strong>'
                });
                }, 500);
            }

        });
        }
        });
        }
    }
    showActiveConfig(type){
        console.log("showActiveConfig",type);
        this.routingTypeTab =type;
    }

}
