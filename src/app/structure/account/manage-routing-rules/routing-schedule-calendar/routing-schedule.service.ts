import { Injectable } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import { Subject } from 'rxjs/Subject';
import 'rxjs/add/operator/toPromise';
import { StructureService } from '../../../structure.service';
import gql from 'graphql-tag';
import { Apollo } from 'apollo-angular';
@Injectable()
export class RoutingScheduleService {
  userDetails:any; 
  userData:any = {};
  getUserSchedulesApi = '';
  constructor(
    private _http: Http,
    private apollo: Apollo,
    private _structureService:StructureService
  ) {
    this.userDetails = this._structureService.userDetails; 
    this.userData = JSON.parse(this.userDetails);
    this.getUserSchedulesApi = this._structureService.apiBaseUrl+'citus-health/'+this._structureService.version;
  }
  getUserAllSchedulesQuery(userId = 0,calendarStartDate,calendarEndDate){
    console.log("!userId",!Number(userId),calendarStartDate,calendarEndDate)
    let query =`query allScheduleRowData($userId: Int!,$calendarStartDate: String,$calendarEndDate: String)
    {
      allScheduleRowData(TableFilter:
        {userId:$userId,calendarStartDate: $calendarStartDate,calendarEndDate: $calendarEndDate}) {
        requestedUserId
        
       singleDaySchedule{
        tags{
          id
          tagName
        }
        sites
        staffUsers{
          userId
          firstName
          lastName
          displayName
        }
        title
        ranges{
          start
          end
        }
        dow`;
        if(!Number(userId)){
          query +=` username`;
        }
        query +=` occurrence_affected_dates
        occurrence_affected_dates_time
        continuous_schedule
        eventStartDate
        eventEndDate 
        scheduleName
        routingType
        scheduleType
        color
        referenceIdSchedule
        schRecurrenceType
        savedTimeZoneSchedule
        dayNumber
        updatedDate
        scheduleEnd
        parentReferenceIdSchedule
        timeSpanSchedule
        recurrenceOverrideBehaviour
        specificDayRecurringType
        start
        end    
      }
      multipleDaySchedule{
        parent{
          title
          ranges{
            start
            end
          }
          tags{
            id
            tagName
          }
          sites
          staffUsers{
            userId
            firstName
            lastName
            displayName
          }
          routingType`;
          if(!Number(userId)){
            query +=` username`;
          }
          query +=` 
          scheduleType
          occurrence_affected_dates
          occurrence_affected_dates_time
          scheduleName
          color
          referenceIdSchedule
          schRecurrenceType
          savedTimeZoneSchedule
          dayNumber
          updatedDate
          scheduleEnd
          parentReferenceIdSchedule
          timeSpanSchedule
          start
          end
        }
        child{
          title
          ranges{
            start
            end
          }
          tags{
            id
            tagName
          }
          sites
          staffUsers{
            userId
            firstName
            lastName
            displayName
          }
          routingType`;
          if(!Number(userId)){
            query +=` username`;
          }
          query +=` 
          scheduleType
          color
          occurrence_affected_dates
          occurrence_affected_dates_time
          scheduleName
          referenceIdSchedule
          schRecurrenceType
          savedTimeZoneSchedule
          dayNumber
          updatedDate
          scheduleEnd
          parentReferenceIdSchedule
          timeSpanSchedule
          start
          end
        }
      }
    }
    }
    `;
    console.log("query====>",query);
    return  gql`${query}`;
  }
  getUserSchedulesQuery(){

    let query =`query scheduleRowData($limit:Int,$offset:Int,$searchText:String,$orderData: scheduleOrder,$orderBy: scheduleOrderBy)
    {
      scheduleRowData(TableFilter:
        {limit:$limit,offset:$offset,searchText:$searchText,orderData: $orderData,orderby: $orderBy}) {
        requestedUserId
        totalCount
       singleDaySchedule{
        tags{
          id
          tagName
        }
        sites
        staffUsers{
          userId
          firstName
          lastName
          displayName
        }
        title
        ranges{
          start
          end
        }
        dow
        recurringFrom
        recurringTo
        occurrence_affected_dates
        occurrence_affected_dates_time
        continuous_schedule
        eventStartDate
        eventEndDate 
        scheduleName
        routingType
        scheduleType
        color
        referenceIdSchedule
        schRecurrenceType
        savedTimeZoneSchedule
        dayNumber
        updatedDate
        scheduleEnd
        parentReferenceIdSchedule
        timeSpanSchedule
        recurrenceOverrideBehaviour
        specificDayRecurringType
        start
        end    
      }
       multipleDaySchedule{
        child{
          start
        }
        
        parent{
          start
        }
      }
    }
    }
    `;
    console.log("query====>",query);
    return  gql`${query}`;
  }
  getUserSchedules(userId,calendar = true,params:any = {},calendarStartDate="",calendarEndDate="") {
    console.log("getUserSchedules",userId,calendar);
    let variables = { 
    userId : Number(userId),
    offset : params.offset,
    limit : params.limit,
    orderData : params.orderData,
    orderBy : params.orderby,
    calendarStartDate : calendarStartDate,
    calendarEndDate : calendarEndDate,
    searchText : params.searchText};
    let apiConfig = {
      method: 'GET',
      noLoader:false,
      use: "multiSite",
      data: (calendar)? this.getUserAllSchedulesQuery(userId,calendarStartDate,calendarEndDate) : this.getUserSchedulesQuery(),
      variables: variables,
      requestType: 'gql',
    };
    let response = this._structureService.requestData(apiConfig);
    return response;
    /* const apiURL = this.getUserSchedulesApi + '/get-user-schedules-by-schedule-type.php';
    this.userDetails = localStorage.getItem('userDetails');
    this.userData = JSON.parse(this.userDetails);
    let data = '';
    if(userId) {
      data =  "?userId=" + userId + "&tenantId=" + this._structureService.getCookie('tenantId');
    } else {
      data =  "?tenantId=" + this._structureService.getCookie('tenantId');
    }
    if(this.userData.config.enable_nursing_agencies_visibility_restrictions == 1) {
      data += "&nursingAgencyEnabled=1";
    }    
    let postData = {};
    if(this.userData.config.weekend_days && this.userData.config.weekend_days.length && this.userData.config.weekend_days.length > 1) {
      postData = {
        "weekendDays" : this.userData.config.weekend_days
      }
    }
    let apiConfig = { url: apiURL+data, requestType: 'http', data: postData};
    let response = this._structureService.requestData(apiConfig);
    return response; */
  }
  saveUserSchedules(param, data) {
    const apiURL = this.getUserSchedulesApi + '/save-user-schedule-desktop.php';
    let dataParam =  '?tenantId='+ param.tenantId + '&scheduleFormat=' + param.scheduleFormat + '&formattedDate=' + param.formattedDate + '&dayStatus=' + param.dayStatus + '&weekStatus=' + param.weekStatus + '&typeOfSchedule=' + param.typeOfSchedule + '&mergeRecurring=' + param.mergeRecurring + '&getDay=' + param.getDay + '&escalated_schedule='+ param.escalated_schedule + '&formattedEndDate=' + param.formattedEndDate + '&timeSpan=' + param.timeSpan + '&savedTimeZone=' + param.savedTimeZone;
    if(param.isNightShift) {
      dataParam = dataParam + '&isNightShift=' + param.isNightShift + '&formattedDateNightShift=' + param.formattedDateNightShift + '&dayNumberNightShift=' + param.dayNumberNightShift; 
    }
    let apiConfig = { url: apiURL + dataParam, requestType: 'http', data: data };
    let response = this._structureService.requestData(apiConfig);
    return response;
  }

  editUserSchedule(param, data) {
   /*  const apiURL = this.getUserSchedulesApi + '/edit-user-schedule-based-on-timezone.php';
    let dataParam = '';
    if(param.action == 'clearSlot') {
      dataParam =  '?userId='+ param.userId +'&tenantId='+ param.tenantId + "&action=" + param.action + '&scheduleFormat=' + param.scheduleFormat + '&formattedDate=' + param.formattedDate + '&dayStatus=' + param.dayStatus + '&weekStatus=' + param.weekStatus + '&typeOfSchedule=' + param.typeOfSchedule + '&mergeRecurring=' + param.mergeRecurring + '&getDay=' + param.getDay + '&schedulesToClear='+ param.schedulesToClear;
    } else {
      var count = 0;
      for(var key in param) {
        if(count == 0) {
          dataParam = '?' + key + '=' + param[key];
        } else {
          dataParam += '&' + key + '=' + param[key];
        }
        count++;
      }
    }
    let apiConfig = { url: apiURL + dataParam, requestType: 'http', data: data };
    let response = this._structureService.requestData(apiConfig);
    return response; */
    let variables = { 
      staffUsers : (!param.calendar) ? param.staffUsers.toString().split`,`.map(x=>+x) : param.userId.toString().split`,`.map(x=>+x),
      // id: param.referenceId.toString().split`,`.map(x=>+x),
      id: param.referenceId,
      name: param.name,
      routingType: parseInt(param.routingType),
      routingBy: param.routingBy,
      startDate: param.startDate,
      endDate: param.endDate,
      startTime: param.startTime,
      endTime: param.endTime,
      eventStartDate: (param.recurrenceType != "1") ? param.eventStartDate : "",
      eventEndDate: (param.recurrenceType != "1") ? param.eventEndDate : "",
      editType:param.type
    };
    console.log("variables",variables)
    let apiConfig = {
      method: 'POST',
      noLoader:false,
      use: "multiSite",
      data: this.editUserSchedulesQuery(),
      variables: variables,
      requestType: 'gql',
    };
    let response = this._structureService.requestData(apiConfig);
    return response;

  }
  editUserSchedulesQuery(){
    let query =`mutation updateRoutingSchedule($id: Int!,$name:String!,$routingType:Int,$routingBy:String,$startDate:String,$endDate:String,$startTime:String,$endTime:String,$eventStartDate:String,$eventEndDate:String,$editType:Int,$staffUsers:[Int])
    {
      updateRoutingSchedule(
        params:{
          id:$id
          name: $name
          routingType: $routingType
          routingBy: $routingBy
          
          startDate: $startDate
          endDate: $endDate
          
          startTime: $startTime
          endTime: $endTime
          
          eventStartDate: $eventStartDate
          eventEndDate: $eventEndDate
          
          editType:$editType
          staffUsers:$staffUsers
        }) {
          message
          status
        }
      }
    `;
    console.log("query====>",query);
    return  gql`${query}`;
  }
  deleteUserSchedule(param, data) {
    let variables = {staffs : param.userId.toString().split`,`.map(x=>+x),id:param.referenceId.toString().split`,`.map(x=>+x),deleteType:param.type,startDate:data.dateRangeStart ,endDate:data.dateRangeEnd};
    console.log("variables",variables)
    let apiConfig = {
      method: 'POST',
      noLoader:false,
      use: "multiSite",
      data: this.deleteUserSchedulesQuery(),
      variables: variables,
      requestType: 'gql',
    };
    let response = this._structureService.requestData(apiConfig);
    return response;
  }
  deleteUserSchedulesQuery(){
    let query =`mutation deleteRoutingSchedule($staffs: [Int!],$id:[Int!],$deleteType:Int,$startDate:String,$endDate:String)
    {
      deleteRoutingSchedule(staffs:$staffs,id:$id,deleteType:$deleteType,startDate:$startDate,endDate:$endDate) {
        message
        status
    }
    }
    `;
    console.log("query====>",query);
    return  gql`${query}`;
  }
  addRoutingSchedule(data) {
    return this.apollo.use('multiSite').mutate({
        mutation: this.createRoutingScheduleMutation(),
        variables: {
            name: data.name,
            startDate: data.startDate,
            endDate: data.endDate,
            routingBy: data.routingBy,
            routingType:data.routingType,
            staffUsers: data.staffUsers,
            startTime: data.startTime,
            endTime: data.endTime,
            eventStartDate: ( data.pattern != 1) ? data.eventStartDate : "",
            eventEndDate: ( data.pattern != 1) ? data.eventEndDate : "",
            oncallEscalationType: data.oncallEscalationType,
            pattern: data.pattern.toString()
        }
    }).map(
        res => res
    );
}
createRoutingScheduleMutation() {
    let createRule = `
mutation createRoutingSchedule(
  $name:String!,
  $startDate:String,
  $endDate:String,
  $routingBy:String,
  $routingType:Int,
  $staffUsers:String,
  $startTime:String,
  $endTime:String,
  $eventStartDate:String,
  $eventEndDate:String,
  $oncallEscalationType:String,
  $pattern:String
 ){
createRoutingSchedule(
  params:{
    name:$name,
    startDate:$startDate,
    endDate:$endDate,
    routingBy:$routingBy,
    routingType:$routingType,
    staffUsers:$staffUsers,
    startTime:$startTime,
    endTime:$endTime,
    eventStartDate:$eventStartDate,
    eventEndDate:$eventEndDate,
    oncallEscalationType:$oncallEscalationType,
    pattern:$pattern
    })
        {message status id}
    }
`;
    return gql`${createRule}`;
}
}

