<style>
    .cat__apps__messaging__tab__content.all_staff {
        padding-top: 1rem!important;
    }
    .schedule-left-panel{
        height: auto !important;
        overflow-y: hidden !important;
        overflow-x: hidden !important;
        padding-right: 0 !important;
    }
    .searchbar{
        padding-right: 30px !important;
    }
    .cat__apps__messaging__header i{
        right: 12px !important;
    }
    .active{
        color: #0190fe !important;
    }
    .input-dropdown{
        height: 45px !important;
        width:100%;
    }
    .toggle-schedule{
        cursor: pointer;
    }
    input.schedule-type {
        position: absolute;
        clip: rect(0,0,0,0);
        pointer-events: none;
    }
    .schedule-type-container {
        float: right;
        margin-right: 10px;
        background-color: #ffffff;
        display: block;
    }
    .schedule-type-container label.selected {
        background-color: #1d6372 !important;
        border-color:#1d6372 !important; 
    }
    .schedule-type-container label {
        width: 135px !important;
        background-color: #33bcda;
        border-color: #33bcda;
    }
/*     .recipient-ul li.render-manipulate { 
    position: absolute !important;
    }
    .recipient-ul1 li.render-manipulate { 
    position: absolute !important;
    } */
    .schedule-type-container label img{
        width:14px;
        height: 14px;
        margin-top: -3px;
    }
    .save-schedule-type-container {
        vertical-align: middle;
    }
    .save-schedule-type-container button {
        margin-bottom: 0px!important;
    }
   .schedule-admin-date{
       width: 200px;
   }
   .schedule-admin-oncall{
    -moz-box-shadow:    inset 0 0 10px #eeeeee;
    -webkit-box-shadow: inset 0 0 10px #eeeeee;
    box-shadow:         inset 0 0 10px #eeeeee;
    border:1px solid #eeeeee;
    border-radius: 3px 0 0 3px;
    padding: 7px 35px;
   }
   .schedule-admin-oncall input, .schedule-admin-escal input{
       position: relative;
       top: 2px;
   }
   .schedule-admin-escal{
    -moz-box-shadow:    inset 0 0 10px #eeeeee;
    -webkit-box-shadow: inset 0 0 10px #eeeeee;
    box-shadow:         inset 0 0 10px #eeeeee;
    border:1px solid #eeeeee;
    border-radius: 0 3px 3px 0;
    padding: 7px 35px;
    margin-left: -5px;
   }
   /* .recurrence-type-error {
       color: red;
   } */
   .recurrence-type-error select {
       border-color: red;
   }
   .schedule-error-msg{
    position: absolute;
    right: 50px;
    padding-top: 4px;
   }
   #schedule-date-picker input {
       font-weight: bolder;
   }
   .cat__core__avatar img {
       height: 100% !important;
   }
  
   .cat__apps__messaging__tab #schedule-icon-container img{
        width: 30px;
        height: 30px;
        margin: 5px 2px 0 2px;
        border: 1px solid #b1b1b1;
        border-radius: 6px;
        padding: 1px;
   }
   .cat__apps__messaging__tab--selected #schedule-icon-container img{
        border: 1px solid #fff !important;
    }

    .cat__apps__messaging__tab #schedule-icon-withbg img{
        width: 30px;
        height: 30px;
        margin: 5px 2px 0 2px;
        border: 2px solid #b1b1b1;
        background: #F8F9EA;
        border-radius: 6px;
        padding: 1px;
   }
   .cat__apps__messaging__tab--selected #schedule-icon-withbg img{
        border: 1px solid #fff !important;
    }
    .event-action-confirmation {
        background-color: white;
        border: 2px solid white;
    }
    .event-action-confirmation-head{
        padding: 10px;
    }
    .event-action-confirmation-footer{
        border-top: 1px solid #eaeaea;
        padding: 5px;
        margin-top: 15px;
    }
    .event-action-confirmation-head.event-oncall {
        background-color:rgba(71, 178, 236, 0.3);
    }
    .event-action-confirmation-head.event-esc {
        background-color:#76d1bd;
    }
    .schedule-popover{
        box-shadow: 0 0 7px 1px rgba(0,0,0,.4);
        display:block;position:fixed;z-index:9999999
    }
    .event-action-confirmation-footer button span{
        font-family: "PT Sans", sans-serif !important;
    }
    .title-right-icons img{
        width: 20px;
        margin-right: 5px;
    }
    .title-right-icons{
        float: right;
    }
    .title-icons{margin-right: 0 !important;}
    .schedule-type-container .btn-oncall{
        background-color: #53e1ff !important;
        border-color: #53e1ff !important;
        width: 210px !important;
    }
    .schedule-type-container .btn-esc{
        background-color: #1eb996 !important;
        border-color: #1eb996 !important;
        width: 275px !important;
    }
    
    .schedule-type-container .btn-oncall.selected{
        background-color: #01c5ef !important;
    border-color: #01c5ef !important;
    }
    
    .schedule-type-container .btn-esc.selected{
        background-color: #19a082 !important;
        border-color: #19a082 !important;
    }
    .modal.sch.introjs-fixParent { z-index:999999997 !important; }
    .modal.sch.introjs-fixParent .introjs-fixedTooltip { z-index: 999999998 !important; }
    .modal.sch.introjs-fixParent .introjs-showElement { z-index: ********* !important; }
    .clear-btn {
        position: absolute;
        right: 20px;
        top: 10px;
    }
    .modal-body {
        padding-bottom: 0px;
    }

    /* Absolute Center Spinner */
    .loading {
        /* position: fixed; */
        z-index: 99999999;
        height: 2em;
        width: 2em;
        overflow: show;
        margin: auto;
        /* top: 0;
        left: 0;
        bottom: 0;
        right: 0; */
    }

    /* Transparent Overlay */
    .schedule-card.disable-pointer-events {
        pointer-events: none !important;
    }
    .loading:before {
        content: '';
        display: block;
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.3);
        z-index: 98;
    }

    /* :not(:required) hides these rules from IE9 and below */
    .loading:not(:required) {
        /* hide "loading..." text */
        font: 0/0 a;
        color: transparent;
        text-shadow: none;
        background-color: transparent;
        border: 0;
        z-index: 99999999;
    }

    .loading:not(:required):after {
        content: '';
        display: block;
        font-size: 10px;
        width: 1em;
        height: 1em;
        margin-top: -0.5em;
        -webkit-animation: spinner 1500ms infinite linear;
        -moz-animation: spinner 1500ms infinite linear;
        -ms-animation: spinner 1500ms infinite linear;
        -o-animation: spinner 1500ms infinite linear;
        animation: spinner 1500ms infinite linear;
        border-radius: 0.5em;
        -webkit-box-shadow: rgba(0, 0, 0, 0.75) 1.5em 0 0 0, rgba(0, 0, 0, 0.75) 1.1em 1.1em 0 0, rgba(0, 0, 0, 0.75) 0 1.5em 0 0, rgba(0, 0, 0, 0.75) -1.1em 1.1em 0 0, rgba(0, 0, 0, 0.5) -1.5em 0 0 0, rgba(0, 0, 0, 0.5) -1.1em -1.1em 0 0, rgba(0, 0, 0, 0.75) 0 -1.5em 0 0, rgba(0, 0, 0, 0.75) 1.1em -1.1em 0 0;
        box-shadow: rgba(0, 0, 0, 0.75) 1.5em 0 0 0, rgba(0, 0, 0, 0.75) 1.1em 1.1em 0 0, rgba(0, 0, 0, 0.75) 0 1.5em 0 0, rgba(0, 0, 0, 0.75) -1.1em 1.1em 0 0, rgba(0, 0, 0, 0.75) -1.5em 0 0 0, rgba(0, 0, 0, 0.75) -1.1em -1.1em 0 0, rgba(0, 0, 0, 0.75) 0 -1.5em 0 0, rgba(0, 0, 0, 0.75) 1.1em -1.1em 0 0;
        position: absolute;
        top: 30%;
        z-index: 99999999;
    }

    /* Animation */

    @-webkit-keyframes spinner {
        0% {
            -webkit-transform: rotate(0deg);
            -moz-transform: rotate(0deg);
            -ms-transform: rotate(0deg);
            -o-transform: rotate(0deg);
            transform: rotate(0deg);
        }
        100% {
            -webkit-transform: rotate(360deg);
            -moz-transform: rotate(360deg);
            -ms-transform: rotate(360deg);
            -o-transform: rotate(360deg);
            transform: rotate(360deg);
        }
    }
    @-moz-keyframes spinner {
        0% {
            -webkit-transform: rotate(0deg);
            -moz-transform: rotate(0deg);
            -ms-transform: rotate(0deg);
            -o-transform: rotate(0deg);
            transform: rotate(0deg);
        }
        100% {
            -webkit-transform: rotate(360deg);
            -moz-transform: rotate(360deg);
            -ms-transform: rotate(360deg);
            -o-transform: rotate(360deg);
            transform: rotate(360deg);
        }
    }
    @-o-keyframes spinner {
        0% {
            -webkit-transform: rotate(0deg);
            -moz-transform: rotate(0deg);
            -ms-transform: rotate(0deg);
            -o-transform: rotate(0deg);
            transform: rotate(0deg);
        }
        100% {
            -webkit-transform: rotate(360deg);
            -moz-transform: rotate(360deg);
            -ms-transform: rotate(360deg);
            -o-transform: rotate(360deg);
            transform: rotate(360deg);
        }
    }
    @keyframes spinner {
        0% {
            -webkit-transform: rotate(0deg);
            -moz-transform: rotate(0deg);
            -ms-transform: rotate(0deg);
            -o-transform: rotate(0deg);
            transform: rotate(0deg);
        }
        100% {
            -webkit-transform: rotate(360deg);
            -moz-transform: rotate(360deg);
            -ms-transform: rotate(360deg);
            -o-transform: rotate(360deg);
            transform: rotate(360deg);
        }
    }
    .event-action-confirm-user-name-added {
        padding-top: 0px!important;
        padding-bottom: 0px!important;
    }
    .event-action-confirm-user-name-data {
        padding-bottom: 2px!important;
        font-weight: bold;
    }
</style>
<!-- START: tables/datatables -->
<!-- <section id="card-title" class="card" style="margin-top:10px;">
     
    </div> -->
    <!-- <div class="form-group row content-position"> -->
        <!-- <label class="col-md-5 control-label title-class"><b>Message Routing Schedule</b><i chToolTip="Calendar Schedules Based Routing Rules"></i></label> -->
       <!--  <div class="pull-right" style="position: absolute;right: 0px;color: grey;margin-top: -5px;">
        <i class="fa fa-2x fa-th active" style="padding-right: 10px;!important
        "></i>
        <i class="fa fa-2x fa-list"></i>
        </div> -->
       
<!-- </section> -->
<div class="row">
<div class="col-md-12" style="margin-top:30px;margin-bottom: 25px;">
        <span id="schedule-header" class="cat__core__title">
            <strong>Patient Message Routing Schedules</strong>
        </span>
        <!-- <div class="pull-right" style="position: absolute;right: 0px;color: grey;margin-top: -5px;"> -->
        <i (click)="toggleView('list')" title="List View" class="fa fa-2x fa-list pull-right toggle-schedule" id="list-toggle" style="padding-right: 200px;"></i>
        <i (click)="toggleView('block')" title="Calendar View" class="fa fa-2x fa-th pull-right toggle-schedule active" id="block-toggle" style="padding-right: 5px;"></i>
        <!-- </div> -->
        <button class="pull-right btn btn-sm btn-primary ci-filter-dropdown" style="margin-top:5px;" (click)="addScheduleRoutingRule()" id="add-routing-rule">Add New Routing Schedule</button>
</div>
</div>
<section class="row" *ngIf="calendar"> 
    <div class="row">
        <div class="col-sm-3">
            <section id="card-clinicians" class="card">
                
                <div class="card-block card-list schedule-left-panel">
                    <div class="cat__core__card-sidebar">
                        
                            
                        <!-- <div id="search-bar" class="cat__apps__messaging__header searchbar">
                            <input class="form-control cat__apps__messaging__header__input" placeholder="Search....." #searchStaffName/>
                            <i class="icmn-search"></i>
                            <button></button>
                        </div> -->
                        <div class="cat__apps__messaging__header searchbar chatroom-searcbar">
                            <input class="form-control cat__apps__messaging__header__input" (keydown)="SearchOnEnter($event)" id="SearchTxt" placeholder="Search..."
                                #search>
                                <div style="margin-right: 90px;">
                            <!-- <i class="icmn-loader" *ngIf="searchFlag"><img src="./assets/img/loader/color.gif" class="menu-spinner"></i> -->
                            <span _ngcontent-c2="" class=" searchtip" data-placement="top" data-toggle="tooltip" title="" data-original-title="Search"><i class="btnicn icmn-search" (click)="searchFn()"></i></span>
            
                            <span _ngcontent-c2="" class=" resettip" data-placement="top" data-toggle="tooltip" title="" data-original-title="Reset">
                            <i class="reset icmn-loop2" (click)="resetSearch()"></i>   </span>     
                                </div>
                                    
                        </div>
                            <div class="no-data" *ngIf="!(staffListSide && staffListSide.length) && loadingData == false">No matching items found</div>   
                            <div *ngIf="loadingData == true" class="messageLoads loading-container">
                                <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div></div>
                                <div class="loading-text">Loading Staffs...</div>
                            </div>            
                            
                        <div id="staff-users-list-to-schedule" class="inbox-data-container schedule-user-container">
                        <div class="cat__apps__messaging__list" *ngIf="staffListSide && staffListSide.length" [hidden]="allstaffhide">
                            <a href="javascript: void(0);">
                                <div class="cat__apps__messaging__tab cat__apps__messaging__tab__0" (click)="getUserSchedule()" [ngClass]="{'cat__apps__messaging__tab--selected': selectedStaff.id==='0'}">
                                    <div class="cat__apps__messaging__tab__avatar">
                                        <a class="cat__core__avatar cat__core__avatar--50" href="javascript:void(0);">
                                            <img src="./assets/img/all_user.jpg" (error)="updateImageUrl($event.target)">
                                        </a>
                                    </div>
                                    <div class="cat__apps__messaging__tab__content all_staff">
                                        <div class="cat__apps__messaging__tab__name title-icons">All Staff
                                        </div>
                                        <div class="cat__apps__messaging__tab__text"></div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        <div *ngFor="let staff of staffListSide ; let i=index" class="cat__apps__messaging__list">

                            <a href="javascript: void(0);">
                                <div class="cat__apps__messaging__tab cat__apps__messaging__tab__{{staff.id}}" (click)="getUserSchedule(staff)" [ngClass]="{'cat__apps__messaging__tab--selected': selectedStaff.id===staff.id}">
                                    <div class="cat__apps__messaging__tab__avatar avatar-position">
                                        <a class="cat__core__avatar cat__core__avatar--50" href="javascript:void(0);" outOfOfficeStatus [oooInfo]="staff.oooInfo" [customClass]="'routing-schedule-badge'">
                                            <img [src]="profileImageBaseUrl+staff.avatar"  (error)="updateImageUrl($event.target)">
                                        </a>
                                    </div>
                                    <div class="cat__apps__messaging__tab__content">
                                        <div class="cat__apps__messaging__tab__name title-icons">{{staff.displayName != '' ? staff.displayName : ''}}
                                            <span [scheduleIcon]="{'schedulesAssigned': staff.schedulesAssigned, 'id': staff.id}" class="title-right-icons">
                                                <i><img id='schedule-icon-oncall-{{staff.id}}' class="" src="./assets/modules/dummy-assets/common/img/m.jpg"></i>
                                                <i><img id='schedule-icon-esc-{{staff.id}}' class="" src="./assets/modules/dummy-assets/common/img/e.jpg"></i>
                                            </span>
                                        </div>
                                        <div class="cat__apps__messaging__tab__text">{{staff.role && staff.role.displayName != '' ? staff.role.displayName : 'nill'}}</div>
                                    </div>
                                </div>
                            </a>
                        </div>
                        </div>

                        
                        <!--<div class="cat__apps__messaging__list">

                            <a href="javascript: void(0);">
                                <div class="cat__apps__messaging__tab" (click)="editGroupName(group)" [ngClass]="{'cat__apps__messaging__tab--selected': selected.id===group.id}">
                                    <div class="cat__apps__messaging__tab__avatar">
                                        <a class="cat__core__avatar cat__core__avatar--50" href="javascript:void(0);">
                                            <img src="{{_structureService.imageBaseUrl}}/thumbs/{{group.createdUser.avatar}}" alt="Alternative text to the image">
                                        </a>
                                    </div>
                                    <div class="cat__apps__messaging__tab__content">
                                        <div class="cat__apps__messaging__tab__name">Staff Name3</div>
                                        <div class="cat__apps__messaging__tab__text">Role Name</div>
                                        
                                    </div>
                                </div>
                            </a>
                        </div>-->
                        

                    <div *ngIf="totalCount" [hidden]="totalCount && totalCount == 0 && currentPage == 1" class="chatroom-pagination">
                        <ngb-pagination 
                        [collectionSize]="totalCount" 
                        [pageSize]="contentLimit" 
                        [page]="currentPage" 
                        [maxSize]="2" 
                        [rotate]="true" 
                        [boundaryLinks]="false" 
                        (pageChange)="loadPage($event)">
                        </ngb-pagination>
                </div>


                    </div>

                </div>
                
            </section>
        </div>
        <div class="col-sm-9">
            <div class="row">
                <section class="card schedule-card">
                    <div class="card-header">
                        <span class="cat__core__title">
                            <strong>{{selectedStaff.displayName}}</strong>
                        </span>
                        <div class="row">
                            <div id='schedule-type-filter-btn-group' class="btn-group schedule-type-container"><!--[hidden]="addNewSchedule"-->
                                <label class="btn btn-default btn-oncall active" style="background-color: #3a9fb6;" [ngClass]="{'selected': selectedScheduleType == 2}">
                                    <input class="schedule-type" type="radio" [(ngModel)]="selectedScheduleType" value="2" (click)="selectScheduleType(2)"><img src='assets/modules/dummy-assets/common/img/calender-call.png'/>&nbsp; Message Routing Schedule 
                                </label>
                                <label class="btn btn-default btn-esc" [ngClass]="{'selected': selectedScheduleType == 1}">
                                    <input class="schedule-type" type="radio" [(ngModel)]="selectedScheduleType" value="1" (click)="selectScheduleType(1)"><img src='assets/modules/dummy-assets/common/img/calender-lady.png'/>&nbsp; Escalation Message Routing Schedule 
                                </label>                        
                                <label class="btn btn-default active" style="background-color: #1d6372;" [ngClass]="{'selected': selectedScheduleType == 3}"><!--[hidden]="addNewSchedule" -->
                                    <input class="schedule-type" type="radio" [(ngModel)]="selectedScheduleType" value="3" (click)="selectScheduleType(3)"> All </label>
                            </div>
                        </div>
                        <!-- <div class='fc-button-group schedule-type-container' [hidden]="!addNewSchedule">
                            <button id='schedule-save-button' [disabled] = "scheduleRecurrenceType == '0'" type='button' class='btn btn-success mr-2 mb-3 schedule-save-button' (click)="saveScheduleSelection()">Save</button>
                            <button id='schedule-cancel-button' type='button' class='btn btn-primary mr-2 mb-3 schedule-cancel-button' (click)="cancelScheduleSelection()">Cancel</button>
                            <button id='schedule-clear-button' type='button' class='btn mr-2 mb-3 schedule-cancel-button' (click)="clearScheduleSelection()">Clear Selections</button>
                        </div>-- -->
                        <!-- <div class='fc-button-group schedule-type-container' [hidden]="!addNewSchedule">
                            <button id='schedule-clear-button' type='button' class='btn mr-2 mb-3 schedule-cancel-button' (click)="clearScheduleSelection()">Clear Slots</button>
                        </div> -->
                    </div>
                    <div [hidden]="!addNewSchedule">
                        <table class="table fc-schedule-selection">
                            <thead>
                                <tr>
                                    <th class="schedule-admin-date">
                                        <div class='input-group date col-lg-4' id='schedule-date-picker'>
                                            <input readonly="readonly" type='text' class="form-control" />
                                            <span class="input-group-addon">
                                                <span class="fa fa-calendar"></span>
                                            </span>
                                        </div>
                                    </th>
                                    <th class='save-schedule-type-container text-right' [hidden]="true"> <!--[hidden]="!addNewSchedule"-->
                                        <button id='schedule-clear-button' type='button' class='btn mr-2 mb-3 schedule-cancel-button' (click)="clearSlots(false)">Clear Slots</button>
                                    </th>
                                    <!-- <th class="recurrence-type-container">
                                        <select class='form-control' id='schedule-type' (change)="scheduleRecurrenceTypeChange($event.target.value)" [(ngModel)]="scheduleRecurrenceType">
                                            <option [value]='0'>Select the schedule events</option>
                                            <option [value]='1'>Non Recurring [Day Schedule]</option>
                                            <option id='schedule-type-2' [value]='2'></option>
                                            <option [value]='3'>Recurring on all days</option>
                                            <option [value]='4'>Recurring on all week days [Mon-Fri]</option>
                                        </select>
                                    </th> -->
                                </tr>
                                <!-- <tr>
                                    <div class="error-list schedule-error-msg">
                                        <ul class="recurrence-type-error element-hide">Please choose recurrence type</ul>
                                    </div>
                                </tr> -->
                            </thead>
                        </table>
                    </div>
                    <div class="card-block" *ngIf="addNewSchedule" [hidden]="1">
                        <div class="row">
                            <div class="col-lg-6">
                                <h5 class="text-black">How would you like the conflict(s) resolved for future 'Non recurring schedules (Day schedules)' ?</h5>
                                <div class="mb-5">
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input checked="" class="form-check-input" name="nonRecurringBehaviour" type="radio" [value]="1" [(ngModel)]="scheduleBehaviour.noRecurence">
                                            Keep future schedule as it is
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input class="form-check-input" name="nonRecurringBehaviour" type="radio" [value]="2" [(ngModel)]="scheduleBehaviour.noRecurence">
                                            Replace {{selectedStaff.displayName}}'s future specific day schedule with this schedule
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input class="form-check-input" name="nonRecurringBehaviour" type="radio" [value]="3" [(ngModel)]="scheduleBehaviour.noRecurence">
                                            Merge {{selectedStaff.displayName}}'s future specific day schedule with this schedule
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-6">
                                <h5 class="text-black">How would you like the conflict(s) resolved for 'Recurring' schedules?</h5>
                                <div class="mb-5">
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input class="form-check-input" name="recurringBehaviour" (change)="recurrenceBehaviourChange(scheduleBehaviour.recurence)" type="radio" [value]="2" [(ngModel)]="scheduleBehaviour.recurence">
                                            Merge {{selectedStaff.displayName}}'s Recurring schedules with this schedule
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <label class="form-check-label">
                                            <input checked="" class="form-check-input" name="recurringBehaviour" (change)="recurrenceBehaviourChange(scheduleBehaviour.recurence)" type="radio" [value]="1" [(ngModel)]="scheduleBehaviour.recurence">
                                            Replace {{selectedStaff.displayName}}'s recurring schedules with this schedule by removing other days's recurring schedules
                                        </label>
                                    </div>
                                    <div class="form-check" [hidden]="scheduleRecurrenceType !=2">
                                        <label class="form-check-label">
                                            <input class="form-check-input" name="recurringBehaviour" (change)="recurrenceBehaviourChange(scheduleBehaviour.recurence)" type="radio" [value]="3" [(ngModel)]="scheduleBehaviour.recurence">
                                            Replace {{selectedStaff.displayName}}'s  recurring schedules with this schedule by keeping other day's recurring schedules
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-block">                    
                        <div class="calendar-loader loading" [ngStyle]="calendarLoaderVisible.status ? {'visibility': 'visible','z-index': '*********'} : {'visibility': 'hidden'}" >Loading&#8230;</div>
                        <div id="cat__apps__calendar__container" class="row">

                            <div class="cat__apps__calendar"></div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
</section>
<section *ngIf="!calendar">
    
    <table class="table table-hover" id="routing-schedule" width="100%"></table>
</section>
<div class="row event-action-confirmation schedule-popover">
    <div class="col-lg-12 event-action-confirmation-head" [ngClass]="selectedEditSchedule['selectedScheduleType'] == 2 ? 'event-oncall' : 'event-esc'">
        <span>{{selectedEditSchedule.actionModalTitle}}</span>
    </div>
    <div class="col-lg-12 event-action-confirmation-head event-action-confirm-user-name-data" *ngIf="selectedStaff.id == 0">
        <span class="user-title">
            <i class="fa fa-user"></i> &nbsp; {{selectedEditSchedule.event.username}}
        </span>
    </div>
    <div class="col-lg-12 event-action-confirmation-head" [ngClass]="selectedStaff.id == 0 ? 'event-action-confirm-user-name-added' : ''">
        <span>{{selectedEditSchedule.event.title}}</span>
    </div>
    <div class="col-lg-12 event-action-confirmation-footer">
        <!-- <button class="btn btn-icon btn-link fa fa-pencil" *ngIf="selectedEditSchedule.startDate == selectedEditSchedule.currentDate" type="button" (click) = "setModalConstraints('edit', 0)"> Edit </button> -->
        <button class="btn btn-icon btn-link fa fa-pencil" *ngIf="selectedEditSchedule.event.schRecurrenceType == 1" type="button" (click) = "setModalConstraints('edit', 0)"> <span>Edit</span> </button>
        <div class="btn-group" role="group" *ngIf="selectedEditSchedule.event.schRecurrenceType > 1" aria-label="Button group with nested dropdown"><!--*ngIf="selectedEditSchedule.startDate != selectedEditSchedule.currentDate"-->
            <div class="btn-group" role="group">
                <button id="btnGroupDrop1" type="button" class="btn btn-icon btn-link fa fa-pencil dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> <span>Edit</span> </button>
                <div class="dropdown-menu" aria-labelledby="btnGroupDrop1">
                    <button class="btn dropdown-item"  (click)="setModalConstraints('edit', 1)"  >Edit occurrence</button>
                    <button class="btn dropdown-item" (click)="setModalConstraints('edit', 2)">Edit series</button>
                    <!-- <button class="btn dropdown-item" (click)="setEditRangeConstraints('edit', 3)" [disabled]="true">Edit Range</button> -->
                </div>
            </div>
        </div>
        <!-- (click)=selectedEditSchedule.event.referenceIdSchedule ? editSchedule('edit') : saveScheduleSelection() -->
        <!-- <button type="button" [disabled]="(selectedEditSchedule.startTime == selectedEditSchedule.actualStartTime && selectedEditSchedule.endTime == selectedEditSchedule.actualEndTime && selectedEditSchedule.event.referenceIdSchedule) || (!selectedEditSchedule.endTime || !selectedEditSchedule.startTime) || selectedEditSchedule.validationErrorStatus" (click)="selectedEditSchedule.event.referenceIdSchedule ? editSchedule() : saveScheduleSelection()" class="btn btn-success">Save Changes</button> -->
        <!-- <button id="popover-delete" type="button" *ngIf="selectedEditSchedule.event.referenceIdSchedule && selectedEditSchedule.event.schRecurrenceType == 1" class="btn btn-icon btn-link fa fa-trash" (click)="editSchedule('delete', 0)">Delete</button> -->
        <button id="popover-delete" type="button" *ngIf="selectedEditSchedule.event.schRecurrenceType == 1" class="btn btn-icon btn-link fa fa-trash" (click)="editSchedule('delete', 0)"><span>Delete</span></button>
        <div *ngIf="selectedEditSchedule.event.schRecurrenceType > 1" class="btn-group" role="group" aria-label="Button group with nested dropdown"><!--*ngIf="selectedEditSchedule.event.referenceIdSchedule && selectedEditSchedule.event.schRecurrenceType != 1"-->
            <div class="btn-group" role="group">
                <button id="btnGroupDrop1" type="button" class="btn btn-icon btn-link fa fa-trash dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> <span>Delete</span> </button>
                <div class="dropdown-menu" aria-labelledby="btnGroupDrop1">
                    <button class="btn dropdown-item" (click)="editSchedule('delete', 1)">Delete occurrence</button>
                    <button class="btn dropdown-item" (click)="editSchedule('delete', 2)">Delete series</button>
                    <!-- <button class="btn dropdown-item" (click)="setModalConstraints('delete', 3)" [disabled]="true">Delete Range</button>editSchedule('delete', 3) -->
                </div>
            </div>
        </div>
        <button type="button" class="btn btn-icon btn-link fa fa-close" (click)="closeEventActionConfirmation()" ><span> Cancel</span></button>
    </div>
</div>
<!-- END: tables/datatables -->
<!-- Modal -->
<div class="modal sch introJs-Bs3-modal" id="edit-schedule-modal">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <!-- <h5 class="modal-title">{{selectedEditSchedule.title}}</h5> -->
                <!-- <h5 class="modal-title">{{selectedEditSchedule.userEvent == 'select' ? 'Add' : 'Edit' }} slot for {{selectedStaff.displayName ? selectedStaff.displayName : selectedEditSchedule.event.username}} <span>[{{selectedEditSchedule.actionModalTitle}}]</span></h5> -->
                <h5><b>{{title}} Patient Message Routing Schedule Settings</b></h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <!-- <div class="intro-btn-container sch">
                    <button id="intro-btn-sch" *ngIf="selectedEditSchedule.userEvent == 'select'" type="button" class="btn btn-success btn-sm btn-rounded" (click)="startIntro()"><span>Train Me</span></button>
                </div> -->
            </div>
            <div class="modal-body">
                <div class="form-group row">
                    <label class="col-md-3 control-label"
                      >{{ 'LABELS.SCHEDULE_NAME' | translate}} * <i chToolTip="ScheduleName" data-animation="false"></i
                    ></label>
                    <div class="col-md-8">
                        <input
                        type="text"
                        [disabled]="(selectedEditSchedule.event.referenceIdSchedule && calendar)"
                        [(ngModel)]="scheduleName"
                        class="form-control"
                        id="routing-rule-name"
                        name="routing-rule-name"
                        placeholder="Schedule Name"
                      />
                     <!--  <div
                        *ngIf="
                          routingRules.controls['routingRuleName'].errors &&
                          (routingRules.controls.routingRuleName?.dirty ||
                            routingRules.controls.routingRuleName?.touched ||
                            f.submitted)
                        "
                        class="alert alert-danger"
                      >
                        Name is required
                      </div> -->
                    </div>
                  </div>
                  <div class="form-group row">
                    <label class="col-md-3 control-label"
                      >{{ 'LABELS.ROUTE_THE_PATIENT_MESSAGES_BY' | translate}}
                      <i chToolTip="routePatientMessagesBy" data-animation="false"></i
                    ></label>
                    <div class="col-md-5 input-div-class">
                     <label class="form-check-label">
                      <input
                        type="radio"
                        id="route"
                        value="1"
                        [disabled]="(selectedEditSchedule.event.referenceIdSchedule)"
                        [(ngModel)]="patientRoute"
                        name="route"
                        class="user-tag-class"
                        [checked]="(patientRoute == '1')"
                        (click)="setVisible(1)"
                      />
                      Message User Tag(s)</label>
                      <label [hidden]="(userData && userData.config && userData.config.enable_multisite != '1')" class="form-check-label">
                      <input
                        type="radio"
                        id="routeTag"
                        value="2"
                        [hidden]="(userData && userData.config && userData.config.enable_multisite != '1')"
                        [disabled]="(selectedEditSchedule.event.referenceIdSchedule)"
                        [(ngModel)]="patientRoute"
                        [checked]="(patientRoute == '2')"
                        name="routeTag"
                        class="site-class"
                        (click)="setVisible(2)"
                      />
                      Site(s)</label>
                      <label class="form-check-label">
                      <input
                        type="radio"
                        id="routeTag"
                        value="3"
                        [disabled]="(selectedEditSchedule.event.referenceIdSchedule)"
                        [(ngModel)]="patientRoute"
                        [checked]="(patientRoute == '3')"
                        name="routeTag"
                        class="tenant-class"
                        (click)="setVisible(3)"
                      />
                      Tenant</label>
                    </div>
                  </div>
                  <div class="routing-method-class">
                    <div class="form-group row" id="select-site" [hidden]="!hideSiteSelection || selectedMethod != 2">
                      <label class="col-md-3 control-label"
                        >Select Site *
                        <i chToolTip="Site"></i>
                      </label>
                      <div class="col-md-3">
                        <app-select-sites
                          [events]="eventsSubject.asObservable()"
                          [singleSelection]="false"
                          (hideDropdown)="hideDropdown($event)"
                          [siteSelection]="true"
                          [selectedSiteIds]="editSiteData"
                          [filterType] = "filterType"
                          [dynamic] = "filterType"
                          [hideApplyFilter]=true
                          [disableFilter]="(selectedEditSchedule.event.referenceIdSchedule) ? true : false"
                          (siteIds)="getSiteIds($event)"
                        >
                        </app-select-sites>
                        <div class="alert alert-danger site-position" *ngIf="siteRequired">
                          Please select site.
                        </div>
                      </div>
                    </div>
                    <div class="form-group row" id="select-site" [hidden]="selectedMethod != 1">
                      <label class="col-md-3 control-label"
                        >Patient Message User Tags * 
                        <i chToolTip="patientMessageUserTag" data-animation="false"></i>
                      </label>
                      <div class="col-md-6 select-rec1">
                        <div id="tags1">
                          <div class="recipient-search-area-schedule1">
                            <div class="input-dropdown">
                              <input
                                type="text"
                                class="form-control"
                                id="tagsInputSchedule"
                                autocomplete="off"
                                value=""
                                (keydown)="searchMessageTag($event)"
                                [disabled]="(selectedEditSchedule.event.referenceIdSchedule)"
                                placeholder="Search message user tags"
                              />
              
                              <ul class="associate-ul recipient-ul1" id="recipient-ul1">
                                <li
                                  id="recipient-li"
                                  class="associate-li recipient-li"
                                  *ngIf="responsePatient && responsePatient.length == 0"
                                >
                                  No item found
                                </li>
              
                                <li
                                  id="li-{{ tag.id }}"
                                  class="associate-li recipient-li"
                                  [ngClass]="{
                                    'li-selected': checkUserExist(tag.id,'tag')
                                  }"
                                  *ngFor="let tag of responsePatient"
                                  (click)="setSelectedRecipients(tag, tag.id,'tag')"
                                >
                                  {{ tag.tag_name }}
                                </li>
              
                                <li
                                  class="render-manipulate"
                                  *ngIf="responsePatient && responsePatient.length > 0"
                                >
                                  <input
                                    type="button"
                                    class="recipient-select-all btn"
                                    (click)="selectAllUser('tag')"
                                    value="Select All"
                                  />
              
                                  <input
                                    type="button"
                                    class="recipient-class-clear btn"
                                    (click)="closeSelectedRecipient(true,'tag')"
                                    value="Clear All"
                                  />
              
                                  <input
                                    type="button"
                                    class="recipient-class-done btn"
                                    *ngIf="responsePatient && responsePatient.length > 0"
                                    (click)="doneSelectedRecipient('tag')"
                                    value="Done"
                                  />
                                </li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="col-md-3" >
                        <button
                          type="button"
                          [disabled]="(recipientLoading1 || (selectedEditSchedule.event.referenceIdSchedule))"
                          id="recipient-search"
                          (click)="checkRecipientWithTems('tag')"
                          class="recipient-search-button btn btn-sm btn-primary"
                        >
                          {{buttonUser1}}
                        </button>
        
                        <button
                          type="button"
                          [disabled]="(recipientLoading1|| (selectedEditSchedule.event.referenceIdSchedule))"
                          id="recipient-close"
                          (click)="closeSelectedRecipient(false,'tag')"
                          class="
                            recipient-search-button
                            btn btn-sm btn-default
                            recipient-close
                          "
                        >
                          Reset
                        </button>
                      </div>
                    </div>
                    <!-- <div class="row" *ngIf="messageTagStaffRequired">
                      <span class="col-md-3"> </span>
                      <span class="col-md-5">
                        <div class="alert alert-danger site-position">
                          Message User Tag is required
                        </div>
                      </span>
                    </div> -->
                  </div>
                  <div class="form-group row">
                    <label class="col-md-3 control-label"
                      >{{ 'LABELS.SELECT_STAFF' | translate }} *
                      <i chToolTip=""></i>
                    </label>
                    <div class="col-md-6 select-rec">
                      <div id="tags">
                        <div class="recipient-search-area-schedule">
                          <div class="input-dropdown">
                            <input
                              type="text"
                              class="form-control"
                              id="userInput"
                              autocomplete="off"
                              value=""
                              (keydown)="searchStaffOnEnter($event)"
                              [disabled]="(patientRoute == '' || (patientRoute == '1' && selectedTags.length==0) || (patientRoute == '2' && this.siteId == '0') || (selectedEditSchedule.event.referenceIdSchedule  && calendar))"
                              placeholder="Search staff"
                            />
              
                            <ul class="associate-ul recipient-ul" id="recipient-ul">
                              <li
                                id="recipient-li"
                                class="associate-li recipient-li"
                                *ngIf="staffList && staffList.length == 0"
                              >
                                No item found
                              </li>
              
                              <li
                                id="li-{{ staff.userId }}"
                                class="associate-li recipient-li"
                                [ngClass]="{
                                  'li-selected': checkUserExist(staff.userId,'staff')
                                }"
                                *ngFor="let staff of staffList"
                                (click)="setSelectedRecipients(staff, staff.userId,'staff')"
                              >
                                {{ staff.displayname }}
                              </li>
              
                              <li
                                class="render-manipulate"
                                *ngIf="staffList && staffList.length > 0"
                              >
                                <input
                                  type="button"
                                  class="recipient-select-all btn"
                                  (click)="selectAllUser('staff')"
                                  value="Select All"
                                />
              
                                <input
                                  type="button"
                                  class="recipient-class-clear btn"
                                  (click)="closeSelectedRecipient(true,'staff')"
                                  value="Clear All"
                                />
              
                                <input
                                  type="button"
                                  class="recipient-class-done btn"
                                  *ngIf="staffList && staffList.length > 0"
                                  (click)="doneSelectedRecipient('staff')"
                                  value="Done"
                                />
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="col-md-3">
                        <button
                          type="button"
                          [disabled]="(recipientLoading || (patientRoute == '' || (patientRoute == '1' && selectedTags.length==0) || (patientRoute == '2' && this.siteId == '0'))|| (selectedEditSchedule.event.referenceIdSchedule && calendar))"
                          id="recipient-search"
                          (click)="checkRecipientWithTems('staff')"
                          class="recipient-search-button btn btn-sm btn-primary"
                        >
                          {{buttonUser}}
                        </button>
          
                        <button
                          type="button"
                          [disabled]="(recipientLoading || (patientRoute == '' || (patientRoute == '1' && selectedTags.length==0) || (patientRoute == '2' && this.siteId == '0'))|| (selectedEditSchedule.event.referenceIdSchedule  && calendar))"
                          id="recipient-close"
                          (click)="closeSelectedRecipient(false,'staff')"
                          class="
                            recipient-search-button
                            btn btn-sm btn-default
                            recipient-close
                          "
                        >
                          Reset
                        </button>
                      </div>
                  </div>
                <div id='schedule-settings-sch-type-time'>
                <div class="form-group row">
                    <label for="lgFormGroupInput" class="col-sm-3 col-form-label col-form-label-lg">{{ 'LABELS.SCHEDULE_TYPE' | translate}}</label>
                    <div class="col-sm-9">
                        <div class='fc-button-group'>
                            <label class="form-check-label schedule-admin-oncall">
                                <input  [(ngModel)]="selectedEditSchedule.selectedScheduleType" class="form-check-input" id="scheduleType1" name="scheduleType" type="radio" [value]="2" [checked]="selectedEditSchedule.selectedScheduleType == 2" (click)="recipientSearchName=''" [disabled]="(selectedEditSchedule.event.referenceIdSchedule)">
                                Message Routing Schedule <i chToolTip="SSONC0001" data-animation="false"></i><!--[hidden]="!selectedStaff.autoMessageEscalationPrivilege"-->
                            </label>
                            <!-- [hidden]='!selectedStaff.autoMessageEscalationPrivilege' -->
                            <label class="form-check-label schedule-admin-escal" >
                                <input [(ngModel)]="selectedEditSchedule.selectedScheduleType" class="form-check-input" id="scheduleType2" name="scheduleType" type="radio" [value]="1" (click)="recipientSearchName=''" [checked]="selectedEditSchedule.selectedScheduleType == 1" [disabled]="(selectedEditSchedule.event.referenceIdSchedule)">
                                Escalation Message Routing Schedule <i chToolTip="SSESC0001" data-animation="false"></i>
                            </label>
                            <label [hidden]='!clearSlot.action' class="form-check-label schedule-admin-escal">
                                <input [(ngModel)]="selectedEditSchedule.selectedScheduleType" class="form-check-input" id="scheduleType3" name="scheduleType" type="radio" [value]="3" (click)="recipientSearchName=''">
                                All
                            </label>
                        </div>
                    </div>
                </div>
                <div [hidden]="selectedEditSchedule.event.referenceIdSchedule && selectedEditSchedule.action == 'delete'" class="form-group row" [ngClass] = "{'recurrence-type-error': (selectedEditSchedule.validation.timeRange && !selectedEditSchedule.allDay) || (!clearSlot.action && selectedEditSchedule.startTime == '' && !selectedEditSchedule.allDay)}">
                    <label class="col-sm-3 col-form-label-lg">Start *</label>
                    <div class="col-sm-4">
                        <div class='input-group date' id='schedule-start-date-picker-modal'><!--schedule-date-picker-modal-->
                            <input readonly="readonly" type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="fa fa-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <div class="col-sm-2" >
                        <select [hidden]="!this.selectedEditSchedule.allDay" [disabled]="true" class='form-control'>
                            <option value="00:00">12:00 AM</option>
                        </select>
                        <select [hidden]="this.selectedEditSchedule.allDay" id="schedule-edit-start-time " class='form-control' [(ngModel)]="selectedEditSchedule.startTime" (change)="scheduleEditTimeRangeChange($event.target.value, 'start')">
                            <option value="">-- Select --</option>
                            <option value="00:00">12:00 AM</option>
                            <option value="00:30">12:30 AM</option>
                            <option value="01:00">01:00 AM</option>
                            <option value="01:30">01:30 AM</option>
                            <option value="02:00">02:00 AM</option>
                            <option value="02:30">02:30 AM</option>
                            <option value="03:00">03:00 AM</option>
                            <option value="03:30">03:30 AM</option>
                            <option value="04:00">04:00 AM</option>
                            <option value="04:30">04:30 AM</option>
                            <option value="05:00">05:00 AM</option>
                            <option value="05:30">05:30 AM</option>
                            <option value="06:00">06:00 AM</option>
                            <option value="06:30">06:30 AM</option>
                            <option value="07:00">07:00 AM</option>
                            <option value="07:30">07:30 AM</option>
                            <option value="08:00">08:00 AM</option>
                            <option value="08:30">08:30 AM</option>
                            <option value="09:00">09:00 AM</option>
                            <option value="09:30">09:30 AM</option>
                            <option value="10:00">10:00 AM</option>
                            <option value="10:30">10:30 AM</option>
                            <option value="11:00">11:00 AM</option>
                            <option value="11:30">11:30 AM</option>
                            <option value="12:00">12:00 PM</option>
                            <option value="12:30">12:30 PM</option>
                            <option value="13:00">01:00 PM</option>
                            <option value="13:30">01:30 PM</option>
                            <option value="14:00">02:00 PM</option>
                            <option value="14:30">02:30 PM</option>
                            <option value="15:00">03:00 PM</option>
                            <option value="15:30">03:30 PM</option>
                            <option value="16:00">04:00 PM</option>
                            <option value="16:30">04:30 PM</option>
                            <option value="17:00">05:00 PM</option>
                            <option value="17:30">05:30 PM</option>
                            <option value="18:00">06:00 PM</option>
                            <option value="18:30">06:30 PM</option>
                            <option value="19:00">07:00 PM</option>
                            <option value="19:30">07:30 PM</option>
                            <option value="20:00">08:00 PM</option>
                            <option value="20:30">08:30 PM</option>
                            <option value="21:00">09:00 PM</option>
                            <option value="21:30">09:30 PM</option>
                            <option value="22:00">10:00 PM</option>
                            <option value="22:30">10:30 PM</option>
                            <option value="23:00">11:00 PM</option>
                            <option value="23:30">11:30 PM</option>
                        </select> 
                    </div>
                    <div class="col-sm-2" [hidden]="(this.selectedEditSchedule.disableAllDaySelector)">
                    <label class="form-check-label ">
                        <input class="form-check-input" type="checkbox" value="1" [(ngModel)]="selectedEditSchedule.allDay" (change)="allDayChange()"> All day
                    </label></div>
                </div>
                <div class="form-group row" [hidden]="selectedEditSchedule.event.referenceIdSchedule && selectedEditSchedule.action == 'delete'" [ngClass] = "{'recurrence-type-error': (selectedEditSchedule.validation.timeRange && !selectedEditSchedule.allDay) || (!clearSlot.action && selectedEditSchedule.endTime == '' && !selectedEditSchedule.allDay)}">
                    <label class="col-sm-3 col-form-label-lg">End *</label>
                    <div class="col-sm-4">
                        <div class='input-group date' id='schedule-end-date-picker-modal'><!--schedule-selection-end-date-picker-modal-->
                            <input readonly="readonly" type='text' class="form-control" />
                            <!--<span class="input-group-addon" [hidden]='selectedEditSchedule.edit_type == 1'>--><!--[hidden]='selectedEditSchedule.event.referenceIdSchedule'-->
                            <span class="input-group-addon">
                                <span class="fa fa-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <!-- <label for="schedule-edit-end-time" class="col-sm-3 col-form-label-lg">End Time *</label> -->
                    <div class="col-sm-2">
                        <select [hidden]="!this.selectedEditSchedule.allDay" [disabled]="true" class='form-control'>
                            <option value="24:00">12:00 AM</option>
                        </select>
                        <select [hidden]="this.selectedEditSchedule.allDay" id="schedule-edit-end-time" class='form-control' [(ngModel)]="selectedEditSchedule.endTime" (change)="scheduleEditTimeRangeChange($event.target.value, 'end')">
                            <option value="">-- Select --</option>
                            <option id="schedule-edit-end-time-00:30" value="00:30">12:30 AM</option>
                            <option id="schedule-edit-end-time-01:00" value="01:00">01:00 AM</option>
                            <option id="schedule-edit-end-time-01:30" value="01:30">01:30 AM</option>
                            <option id="schedule-edit-end-time-02:00" value="02:00">02:00 AM</option>
                            <option id="schedule-edit-end-time-02:30" value="02:30">02:30 AM</option>
                            <option id="schedule-edit-end-time-03:00" value="03:00">03:00 AM</option>
                            <option id="schedule-edit-end-time-03:30" value="03:30">03:30 AM</option>
                            <option id="schedule-edit-end-time-04:00" value="04:00">04:00 AM</option>
                            <option id="schedule-edit-end-time-04:30" value="04:30">04:30 AM</option>
                            <option id="schedule-edit-end-time-05:00" value="05:00">05:00 AM</option>
                            <option id="schedule-edit-end-time-05:30" value="05:30">05:30 AM</option>
                            <option id="schedule-edit-end-time-06:00" value="06:00">06:00 AM</option>
                            <option id="schedule-edit-end-time-06:30" value="06:30">06:30 AM</option>
                            <option id="schedule-edit-end-time-07:00" value="07:00">07:00 AM</option>
                            <option id="schedule-edit-end-time-07:30" value="07:30">07:30 AM</option>
                            <option id="schedule-edit-end-time-08:00" value="08:00">08:00 AM</option>
                            <option id="schedule-edit-end-time-08:30" value="08:30">08:30 AM</option>
                            <option id="schedule-edit-end-time-09:00" value="09:00">09:00 AM</option>
                            <option id="schedule-edit-end-time-09:30" value="09:30">09:30 AM</option>
                            <option id="schedule-edit-end-time-10:00" value="10:00">10:00 AM</option>
                            <option id="schedule-edit-end-time-10:30" value="10:30">10:30 AM</option>
                            <option id="schedule-edit-end-time-11:00" value="11:00">11:00 AM</option>
                            <option id="schedule-edit-end-time-11:30" value="11:30">11:30 AM</option>
                            <option id="schedule-edit-end-time-12:00" value="12:00">12:00 PM</option>
                            <option id="schedule-edit-end-time-12:30" value="12:30">12:30 PM</option>
                            <option id="schedule-edit-end-time-13:00" value="13:00">01:00 PM</option>
                            <option id="schedule-edit-end-time-13:30" value="13:30">01:30 PM</option>
                            <option id="schedule-edit-end-time-14:00" value="14:00">02:00 PM</option>
                            <option id="schedule-edit-end-time-14:30" value="14:30">02:30 PM</option>
                            <option id="schedule-edit-end-time-15:00" value="15:00">03:00 PM</option>
                            <option id="schedule-edit-end-time-15:30" value="15:30">03:30 PM</option>
                            <option id="schedule-edit-end-time-16:00" value="16:00">04:00 PM</option>
                            <option id="schedule-edit-end-time-16:30" value="16:30">04:30 PM</option>
                            <option id="schedule-edit-end-time-17:00" value="17:00">05:00 PM</option>
                            <option id="schedule-edit-end-time-17:30" value="17:30">05:30 PM</option>
                            <option id="schedule-edit-end-time-18:00" value="18:00">06:00 PM</option>
                            <option id="schedule-edit-end-time-18:30" value="18:30">06:30 PM</option>
                            <option id="schedule-edit-end-time-19:00" value="19:00">07:00 PM</option>
                            <option id="schedule-edit-end-time-19:30" value="19:30">07:30 PM</option>
                            <option id="schedule-edit-end-time-20:00" value="20:00">08:00 PM</option>
                            <option id="schedule-edit-end-time-20:30" value="20:30">08:30 PM</option>
                            <option id="schedule-edit-end-time-21:00" value="21:00">09:00 PM</option>
                            <option id="schedule-edit-end-time-21:30" value="21:30">09:30 PM</option>
                            <option id="schedule-edit-end-time-22:00" value="22:00">10:00 PM</option>
                            <option id="schedule-edit-end-time-22:30" value="22:30">10:30 PM</option>
                            <option id="schedule-edit-end-time-23:00" value="23:00">11:00 PM</option>
                            <option id="schedule-edit-end-time-23:30" value="23:30">11:30 PM</option>
                            <option id="schedule-edit-end-time-24:00" value="24:00">12:00 AM</option>
                        </select>
                    </div>
                </div>
                </div>
                <div id="sch-event-type-recurring">
                <div class="form-group row" id="sch-event-type">
                    <label for="lgFormGroupInput" class="col-sm-3 col-form-label col-form-label-lg">{{ 'LABELS.PATTERN_LABEL' | translate}} {{!selectedEditSchedule.event.referenceIdSchedule ? '*' : ''}}</label>
                    <div class="col-sm-7" *ngIf = '(!selectedEditSchedule.event.referenceIdSchedule)' [ngClass] = "{'recurrence-type-error': selectedEditSchedule.validation.recurrenceType}">
                        <select class='form-control' id='modal-schedule-type' (change)="scheduleRecurrenceTypeChange($event.target.value,calendar,(calendar)? false : true)" [(ngModel)]="scheduleRecurrenceType">
                            <option [value]='0'>Select the schedule events</option>
                            <option [value]='1'>Non Recurring [Day Schedule]</option>
                            <option id='modal-schedule-type-2' [value]='2'></option>
                            <option [value]='3'>Recurring on all days</option>
                            <option [value]='4'>Recurring on all weekdays [Mon-Fri]</option>
                            <option *ngIf="userData.config.weekend_days && userData.config.weekend_days.length && userData.config.weekend_days.length > 1" [value]='7'>Recurring on all weekend days [{{days[userData.config.weekend_days[0]]}}-{{days[userData.config.weekend_days[userData.config.weekend_days.length-1]]}}]</option>
                        </select>
                    </div>
                    <div class="col-sm-7" *ngIf = '(selectedEditSchedule.event.referenceIdSchedule)' [ngSwitch]="scheduleRecurrenceType">
                        <p *ngSwitchCase="1" class="form-control-static"> Non Recurring [Day Schedule] </p>
                        <p *ngSwitchCase="2" id='modal-schedule-type-2-label' class="form-control-static"> </p>
                        <p *ngSwitchCase="3" class="form-control-static"> Recurring on all days </p>
                        <p *ngSwitchCase="4" class="form-control-static"> Recurring on all weekdays [Mon-Fri] </p>
                        <p [hidden]="!(userData.config.weekend_days && userData.config.weekend_days.length && userData.config.weekend_days.length > 1)" *ngSwitchCase="7" class="form-control-static">Recurring on all weekend days [{{days[userData.config.weekend_days[0]]}}-{{days[userData.config.weekend_days[userData.config.weekend_days.length-1]]}}]</p>
                    </div>
                </div>
                <div class="form-group row sch-edit-from" [hidden]="scheduleRecurrenceType==0 || scheduleRecurrenceType==1 || (selectedEditSchedule['action'] == 'edit' && selectedEditSchedule['edit_type'] == 1)">
                    <label for="lgFormGroupInput" class="col-sm-3 col-form-label col-form-label-lg">From *</label>
                    <div class="col-sm-7" [ngClass] = "{'recurrence-type-error': selectedEditSchedule.validation.recurrenceType}">
                        <div class='input-group date' id='schedule-from-date-picker-modal'>
                            <input readonly="readonly" type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="fa fa-calendar"></span>
                            </span>
                        </div>
                    </div>
                </div>
                <div class="form-group row" [hidden]='scheduleRecurrenceType==0 || scheduleRecurrenceType==1 || (selectedEditSchedule["action"] == "edit" && selectedEditSchedule["edit_type"] == 1)'>
                    <label for="lgFormGroupInput" class="col-sm-3 col-form-label col-form-label-lg">To </label>
                    <div class="col-sm-7">
                        <div class='input-group date' id='schedule-to-date-picker-modal'>
                            <input readonly="readonly" type='text' class="form-control" />
                            <span class="input-group-addon">
                                <span class="fa fa-calendar"></span>
                            </span>
                        </div>
                    </div>
                    <button [hidden]="selectedEditSchedule.event.referenceIdSchedule && selectedEditSchedule.action == 'delete'" class="btn btn-icon btn-link fa fa-trash" (click)="clearToDateRangeSelection()" type="button"></button>
                </div>
                </div>
                <div class="form-group row" [hidden]="!clearSlot.action" [ngSwitch]="selectedEditSchedule['selectedScheduleType']">
                    <span *ngSwitchCase="2" class="col-sm-8 text-danger">Oncall schedules starting from current day will be {{(selectedEditSchedule.startTime != '' && selectedEditSchedule.endTime != '') ? 'changed' : 'deleted'}}</span>
                    <span *ngSwitchCase="1" class="col-sm-8 text-danger">Escalation schedules starting from current day will be {{(selectedEditSchedule.startTime != '' && selectedEditSchedule.endTime != '') ? 'changed' : 'deleted'}}</span>
                    <span *ngSwitchCase="3" class="col-sm-8 text-danger">All schedules starting from current day will be {{(selectedEditSchedule.startTime != '' && selectedEditSchedule.endTime != '') ? 'changed' : 'deleted'}}</span>
                </div>
                <div class="form-group row" [hidden]="!clearSlot.action" [ngSwitch]="selectedEditSchedule['selectedScheduleType']">
                    <span *ngSwitchCase="2" class="col-sm-8 text-danger">Oncall recurring schedules starting from current day will be deleted</span>
                    <span *ngSwitchCase="1" class="col-sm-8 text-danger">Escalation recurring schedules starting from current day will be deleted</span>
                    <span *ngSwitchCase="3" class="col-sm-8 text-danger">All recurring schedules starting from current day will be deleted</span>
                </div>
                <!-- <button type="button"  *ngIf="selectedEditSchedule.action != 'edit'" (click)="showOtherClinicians()" class="btn btn-success" style="width: 100%;"> Add same slot for other clinicians </button> -->
                <div class="form-group row" [hidden]="!showClinicians || selectedEditSchedule.action == 'edit'" style="margin-top: 10px;margin-bottom:0px">
                    <input class="form-control" id="recipientSearchName" #recipientSearch placeholder="Search Clinician(s)" [(ngModel)]="recipientSearchName" style="width:90%"/>
                    <span class="clear-btn" (click)="clearSelectedRoles();">
                        <i chToolTip="MSGGP00004"></i>
                        <i><img class="clear-btn-img" src="./assets/img/reset-list-icon.png"></i>
                    </span>
                    <ul class="treeview treeview-section-ui" style="width:100%;">
                        <li *ngFor="let cliniciansRole of (selectedEditSchedule.selectedScheduleType == 1 ? clinicianDataRoleWiseByPrivilege :clinicianDataRoleWise) | searchRolefilter : 'roleData.name' : recipientSearch.value"  [hidden]="cliniciansRole.searchHideStatus" class="role-{{cliniciansRole.roleData.id}}" >
                            <i class="fa fa-plus expand-icon-{{cliniciansRole.roleData.id}}" (click)="callAccordion(cliniciansRole.roleData.id)" [ngClass]="{'fa-minus':recipientSearch.value || (rolesContainingAnySelection.length && rolesContainingAnySelection.indexOf(cliniciansRole.roleData.id)!=-1) , 'fa-plus':!recipientSearch.value || (!rolesContainingAnySelection.length && rolesContainingAnySelection.indexOf(cliniciansRole.roleData.id)==-1)}"></i>
                            <input type="checkbox" name="middle" id="role-{{cliniciansRole.roleData.id}}" [(ngModel)]="orderMain[cliniciansRole.roleData.id]" (change)="selectAllRoleUsers(cliniciansRole.roleData.id)">
                            <label for="middle" [ngClass]="orderMain[cliniciansRole.roleData.id] ? 'custom-checked' : 'custom-unchecked'">{{cliniciansRole.roleData.name }}</label>
                            <ul class="sub-item-panel sub-item-panel-{{cliniciansRole.roleData.id}}" [ngClass]="{'showall':(recipientSearch.value || (rolesContainingAnySelection.length && rolesContainingAnySelection.indexOf(cliniciansRole.roleData.id)!=-1))}">
                                <li *ngFor="let cliniciansRoleUser of cliniciansRole.userList | searchfilterroletreeview: 'name' : (cliniciansRole.filterUserStatus ? recipientSearch.value : '')" [hidden]="cliniciansRoleUser.searchHideStatus">
                                    <input type="checkbox" name="cliniciansRoleUser[]" id="role-{{cliniciansRole.roleData.id}}-{{cliniciansRoleUser.id}}" value="{{cliniciansRoleUser.id}}" class="cliniciansRoleUser userRole-{{cliniciansRole.roleData.id}}" alt="{{cliniciansRoleUser.id}}" title="{{cliniciansRoleUser.name}}" [(ngModel)]="order[cliniciansRoleUser.id]" (change)="formated(cliniciansRole.roleData.id)">
                                    <label for="{{cliniciansRoleUser.name}}" [ngClass]="order[cliniciansRoleUser.id] ? 'custom-checked' : 'custom-unchecked'">{{cliniciansRoleUser.name}}</label>
                                </li>
                            </ul>
                        </li>
                        <li *ngIf="!((selectedEditSchedule.selectedScheduleType == 1 ? clinicianDataRoleWiseByPrivilege :clinicianDataRoleWise) | searchRolefilter : 'roleData.name' : recipientSearch.value).length" >No Recipients Available</li>
					</ul>
                </div>
            </div>
            <div class="modal-footer" *ngIf='!clearSlot.action'>
                <div id='sch-edit-add-action-btn'>
                <button type="button" *ngIf="selectedEditSchedule.action == 'save' || selectedEditSchedule.action == 'edit'" [disabled]="(selectedEditSchedule.startTime == selectedEditSchedule.actualStartTime && selectedEditSchedule.endTime == selectedEditSchedule.actualEndTime && selectedEditSchedule.event.schRecurrenceType == scheduleRecurrenceType && ((selectedEditSchedule.event.scheduleType == 'ONC' && selectedScheduleType == 2) || (selectedEditSchedule.event.scheduleType == 'ESC' && selectedScheduleType == 1) ) && selectedEditSchedule.event.referenceIdSchedule) || (selectedEditSchedule.validationErrorStatus && !selectedEditSchedule.allDay) || (scheduleRecurrenceType==0 && selectedEditSchedule.allDay) || ((scheduleName == ''|| patientRoute == '' || (patientRoute == '1' && selectedTags.length==0) || (patientRoute == '2' && this.siteId == '0') || selectedStaff1.length == 0) && selectedEditSchedule.action == 'save')" (click)="selectedEditSchedule.event.referenceIdSchedule && selectedEditSchedule.action == 'edit' ?  editSchedule('edit', this.selectedEditSchedule.edit_type) : saveScheduleSelection()" class="btn btn-success"> Save </button><!--*ngIf="selectedEditSchedule.startDate == selectedEditSchedule.currentDate"-->
                <!-- <div *ngIf="selectedEditSchedule.startDate != selectedEditSchedule.currentDate" class="btn-group" role="group" aria-label="Button group with nested dropdown">
                    <div class="btn-group" role="group">
                        <button id="btnGroupDrop1" type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false"> Edit </button>
                        <div class="dropdown-menu" aria-labelledby="btnGroupDrop1">
                            <button class="dropdown-item" [disabled]="true">Edit this event only</button>
                            <button class="dropdown-item" (click)="editSchedule('edit', 2)">Edit all future events</button>
                            <button class="dropdown-item" (click)="editSchedule('edit', 3)">Edit range</button>
                        </div>
                    </div>
                </div> -->
                <!-- (click)=selectedEditSchedule.event.referenceIdSchedule ? editSchedule('edit') : saveScheduleSelection() -->
                <!-- <button type="button" [disabled]="(selectedEditSchedule.startTime == selectedEditSchedule.actualStartTime && selectedEditSchedule.endTime == selectedEditSchedule.actualEndTime && selectedEditSchedule.event.referenceIdSchedule) || (!selectedEditSchedule.endTime || !selectedEditSchedule.startTime) || selectedEditSchedule.validationErrorStatus" (click)="selectedEditSchedule.event.referenceIdSchedule ? editSchedule() : saveScheduleSelection()" class="btn btn-success">Save Changes</button> -->
                <button *ngIf="selectedEditSchedule.event.referenceIdSchedule && selectedEditSchedule.action == 'delete'" id="popover-delete" type="button" class="btn btn-danger" (click)="editSchedule('delete', this.selectedEditSchedule.edit_type)">Delete</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                </div>
            </div>
            <div class="modal-footer" *ngIf='clearSlot.action'>
                <button type="button" [hidden]="clearSlot.confirmView" (click)="clearSlot.confirmView = true" class="btn btn-success">Save</button>
                <button type="button" [hidden]="clearSlot.confirmView" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" [hidden]="!clearSlot.confirmView" (click)="clearSlots(true)" class="btn btn-success">Confirm</button>
                <button type="button" [hidden]="!clearSlot.confirmView" data-dismiss="modal" class="btn btn-secondary" >Cancel</button>
            </div>
        </div>
    </div>
</div>
