import { Injectable } from '@angular/core';
import { StructureService } from '../../structure.service';
import gql from 'graphql-tag';
import { Apollo } from 'apollo-angular';
import { variable } from '@angular/compiler/src/output/output_ast';

@Injectable()
export class ManageRoutingRulesService {

  constructor( 
      private _structureService: StructureService,
      private apollo: Apollo) { }

  RoutingRuleList(limit, offset, orderData, orderby, searchText) {
   let variables: any = {
        searchText: searchText,
        limit: limit,
        offset: offset,
        orderData: orderData,
        orderby:orderby
    };
    let apiConfig = {
        method: 'GET',
        data: this.getRoutingRuleListQuery(),
        variables: variables,
        requestType: 'gql',
        use: "multiSite",
        nested: false,
        noLoader: true,
        count: 0
    };
    return this._structureService.requestData(apiConfig);
}
getRoutingRuleListQuery() : any {
    let query = `query RoutingRuleList($offset: Int, $limit: Int, $orderby : String , $orderData : String, $searchText: String!) {
        RoutingRuleList(TableFilter:{offset:$offset, limit:$limit, orderby:$orderby, orderData:$orderData, searchText:$searchText}){
            data {
              id
              routingType
              receiveRoles
              name
              enabled
              enabledRoleSelection
              routingTypeName
            }
        }
        RoutingRulePagination(
            TableFilter:{offset:$offset,searchText:$searchText}) {
                totalCount
            }
    }`;
            return gql`${query}`;
}
addRoutingRule(data) {
    return this.apollo.use('multiSite').mutate({
        mutation: this.createRoutingRuleMutation(),
        variables: {
            name: data.routingRuleName,
            enabled: (data.enableMessageRoutingRule) ? 1 : 0,
            routingBy: data.routingBy,
            routingType: parseInt(data.routingType),
            receiveRoles: (data.rolesToReceivePatientChats).toString(),
            enableRoleSelection: (data.enableAllowPatientToChatWith) ? 1 : 0,
            escalateUsers : (data.escalationStafMembers).toString()
        }
    }).map(
        res => res
    );
}
createRoutingRuleMutation() {
    let createRule = `
mutation createRoutingRule(
  $name:String!,
  $enabled:Int,
  $routingBy:String,
  $routingType:Int,
  $receiveRoles:String,
  $enableRoleSelection:Int,
  $escalateUsers:String
 ){
createRoutingRule(
  params:{
    name:$name,
    enabled:$enabled,
    routingBy:$routingBy,
    routingType:$routingType,
    receiveRoles : $receiveRoles,
    enableRoleSelection : $enableRoleSelection,
    escalateUsers : $escalateUsers
 })
      {message warning id}
  }
`;
    return gql`${createRule}`;
}
getRuleDetails(ruleId) {
    let variables: any = {
         ruleId: ruleId
     };
     let apiConfig = {
         method: 'GET',
         data: this.getRuleDetailsQuery(),
         variables: variables,
         requestType: 'gql',
         use: "multiSite",
         nested: false,
         noLoader: true,
         count: 0
     };
     return this._structureService.requestData(apiConfig);
 }
 getRuleDetailsQuery() : any {
    let query = `query getRulesDetails($ruleId: Int) {
        getRulesDetails(ruleId: $ruleId){
              id
              name
              enabled
              enabledRoleSelection
              ruleTypeData{
                id
                tagName
              }
              routingType
              routingTypeName
              routingBy
              staffId
              receiveRoles
        }
    }`;
            return gql`${query}`;
}
updateRoutingRule(data) {
    return this.apollo.use('multiSite').mutate({
        mutation: this.updateRoutingRuleMutation(),
        variables: {
            id: data.id,
            name: data.routingRuleName,
            enabled: (data.enableMessageRoutingRule) ? 1 : 0,
            routingBy: data.routingBy,
            routingType: parseInt(data.routingType),
            receiveRoles: (data.rolesToReceivePatientChats).toString(),
            enabledRoleSelection: (data.enableAllowPatientToChatWith) ? 1 : 0,
            escalateUsers : (data.escalationStafMembers).toString()
        }
    }).map(
        res => res
    );
}
updateRoutingRuleMutation() {
    let updateRule = `
mutation updateRoutingRule(
  $id:Int!,
  $name:String!,
  $enabled:Int,
  $routingBy:String,
  $routingType:Int,
  $receiveRoles:String,
  $enabledRoleSelection:Int,
  $escalateUsers:String
 ){
updateRoutingRule(
  params:{
    id:$id,
    name:$name,
    enabled:$enabled,
    routingBy:$routingBy,
    routingType:$routingType,
    receiveRoles : $receiveRoles,
    enabledRoleSelection : $enabledRoleSelection,
    escalateUsers : $escalateUsers
 })
      {message warning id}
  }
`;
    return gql`${updateRule}`;
}
deleteRoutingRule(data){
    return this.apollo.use('multiSite').mutate({
        mutation: this.deleteRoutingRuleMutation(),
        variables: {
            id: data
        }
    }).map(
        res => res
    );
    
}

deleteRoutingRuleMutation() {
    let deleteRule = `
mutation deleteRoutingRule(
  $id:[Int!]
 ){
    deleteRoutingRule(id:$id)
      {message status}
  }
`;
    return gql`${deleteRule}`;
}
}


