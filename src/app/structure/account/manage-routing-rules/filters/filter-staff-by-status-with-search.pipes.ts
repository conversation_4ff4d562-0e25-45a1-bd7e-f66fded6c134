import { Component, OnInit, Pipe, PipeTransform } from '@angular/core';
declare var $: any;

@Pipe({
  name: 'FilterStaffByStatusWithSearch'
})
export class FilterStaffByStatusWithSearchPipe implements PipeTransform {
    transform(staffs: any[], searchString: string, status): any[] {
      if (!staffs) { 
        return [];
      } else {
        if(searchString) {
          return staffs.filter(staff => staff['status'] == status.status && staff['displayName'].toLowerCase().indexOf(searchString.toLowerCase()) != -1);  
        } else {
          return staffs.filter(staff => staff['status'] == status.status);
        }
      }
    }
}

@Pipe({
  name: 'searchfilter'
 })
 

 export class SearchFilterPipe implements PipeTransform {
  transform(items: any[], field: string, value: string): any[] {
     //console.log(items,field,value)
    if (!items){
        //console.log('not item')
        //return items;
     return [];
    } else{
        //console.log('elseeeee not item')
        if(value) {
         //console.log('elseeeee not item', value, items);
         //return items.filter(item=>item.name.toLowerCase().indexOf(value.toLowerCase()) != -1);
 
         var result = $.map(items, function(item) { 
             if(item.name.toLowerCase().indexOf(value.toLowerCase()) != -1){
                 item.searchHideStatus = false;
                 return item;
             }
         });
 
         //console.log("result ::: ",result);
         return result;
 
        } else {
             var result = $.map(items, function(item) { 
                 item.searchHideStatus = false;
                 return item;
             });
             return result;
        }
        //return items;
    //return items.filter(it => it[field] == value);
    }
  }
}

@Pipe({
  name: 'TimeZoneFormat'
})
export class TimeZoneFormatPipe implements PipeTransform {
    transform(timeZoneOffset: any): any {
      console.log('timeZone.offset---->' );
      console.log(timeZoneOffset)
      if (!timeZoneOffset) { 
        return '(UTC-00:00)';
      } else {
        let timezone = (((timeZoneOffset.split(',')[0]) * -1) / 60) * -1
        const timezoneGap = ((timezone) + '').split('.');
        if (timezoneGap[1] == '5') {
          timezoneGap[1] = '30';
        } else {
          timezoneGap[1] = '00';
        }
        if((parseInt(timezoneGap[0]) > -10 && parseInt(timezoneGap[0]) < 10)) {
          if(parseInt(timezoneGap[0]) < 0) {
            timezoneGap[0] = '0' + Math.abs(parseInt(timezoneGap[0]));
          } else {
            timezoneGap[0] = '0' + timezoneGap[0];
          }
        }
        if(parseInt(timezoneGap[0]) < -9) {
          timezoneGap[0] = Math.abs(parseInt(timezoneGap[0])) + '';
        }
        console.log(timezone,timezoneGap)
        return '(UTC' + (parseInt(timeZoneOffset.split(',')[0]) > 0 ? '+' : '-') + timezoneGap[0] + ":" + timezoneGap[1] + ')';
      }
    }
}

@Pipe({
  name: 'ResetSelectedUsers'//FilterStaffByPrivilege
})
export class ResetSelectedUsersPipe implements PipeTransform {
  transform(staffs: any, scheduleType: number): any {
    $(':checkbox:checked').each( function(i) {
      $(this).prop('checked', false); 
    });

    $('.label-check-box-role').each( function(i) {
      if($(this).hasClass('custom-checked')) {
        $(this).removeClass('custom-checked');
        $(this).addClass('custom-unchecked');
      }
      if($(this).hasClass('custom-indeterminate')) {
        $(this).removeClass('custom-indeterminate');
        $(this).addClass('custom-unchecked');
      }
    });
    $('.label-check-box-role-user').each( function(i) {
      if($(this).hasClass('custom-checked')) {
        $(this).removeClass('custom-checked');
        $(this).addClass('custom-unchecked')
      }
    });
    $('.role-expand').each( function(i) {
      if($(this).hasClass('fa-minus')) {
        $(this).removeClass('fa-minus');
        $(this).addClass('fa-plus');
      }
    });
    $('.sub-item-panel').each( function(i) {
      if($(this).hasClass('showall')) {
        $(this).removeClass('showall')
      }
    });
    return staffs;
  }
}

// return function(offset) {
//   var operator, hour, minutes;
//   if (offset > 0) {
//       operator = '+';
//   } else {
//       operator = '-';
//       offset = offset*-1;
//   }
//   hour = parseInt(offset/60);
//   minutes = offset%60;
//   if (hour < 10) {
//       hour = '0' + hour;
//   }
//   if (minutes < 10) {
//       minutes = '0' + minutes;
//   }
//   return '(UTC' + operator + hour + ":" + minutes + ')';
// };
