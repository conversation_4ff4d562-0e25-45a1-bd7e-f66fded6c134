import { Component, OnInit, Input, Output, EventE<PERSON>ter ,  Renderer,
    ElementRef,} from '@angular/core';
import { AnonymousSubject, Subject } from 'rxjs';
import { EnrollService } from '../../../user-registration/enroll.service';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule, NgForm } from '@angular/forms';
import { ManageSitesService } from '../../../manage-sites/manage-sites.service';
import { StructureService } from '../../../structure.service';
import { ManageRoutingRulesService } from './../manage-routing-rules.service';
import { ToolTipService } from '../../../tool-tip.service';

import { isBlank } from 'app/utils/utils';


declare var $: any;

@Component({
    selector: 'app-add-routing-rules',
    templateUrl: './add-routing-rules.component.html',
    styleUrls: ['./add-routing-rules.component.css']
})
export class AddRoutingRulesComponent implements OnInit {
    routingRules: FormGroup;
    siteId: any;
    showSiteSelection: any;
    eventsSubject: Subject<void> = new Subject<void>();
    @Output() readonly ruleCreated: EventEmitter<any> = new EventEmitter<any>();
    selectedMethod: number;
    staffRoles: any;
    messageRoutingRule=true;
    allowPatientToSelectRole=false;
    @Input() routingRuleData: any;
    scheduleStaff: any;
    userData: any;
    recipientLoading = false;
    patientTags: any = [];
    selectedRecipientIds: any = [];
    responsePatient: any = [];
    rolesToReceivePatientChats;
    escalationStaffMembers;
    siteRequired : boolean;
    selectedRoles;
    staffMembers;
    selectedRecipients: any = [];
    selectedRecipientNames: any = [];
    stafffillfirst = false;
    messageTagStaffRequired : boolean;
    escalationStaffRequired : boolean;
    editRule : boolean;
    editRuleSiteData : any;
    filterType = true;
    title = 'Add';
    submitButton = 'Save';
    errorMessage : any;
    displayErrorMessage = false;
    isSiteFilterDisabled : boolean;
    displayTag : boolean;
    disableSubmitButton : boolean;
    tagsArray : any = [];
    allSelectedTag : any = [];
    routingTypeData : any;
    routingType : any;
    visible : boolean;
    constructor(
        private _enrollService: EnrollService,
        private _formBuild: FormBuilder,
        private _manageSitesService: ManageSitesService,
        private _structureService: StructureService,
        private _manageRoutingRulesService : ManageRoutingRulesService,
        private _ToolTipService: ToolTipService,
        renderer: Renderer,
        elementRef: ElementRef

        ) {
           this.userData = JSON.parse(this._structureService.userDetails);
           renderer.listen(elementRef.nativeElement, "click", (event, target) => {
            let eventClass = $(event.target).attr("class");
            if (eventClass == "remove") {
              this.removeSelectedRecipient(event.target.id);
            }
           })
          }

    ngOnInit() {
       this.routingRules = this._formBuild.group({
            escalationStaffMembers :[''],
            rolesToReceivePatientChats :[''],
            routingRuleName : [''],
            routingType : ['1', Validators.required]
        });
        $("#roles-to-receive-patient-chats").on('change', (e) => {
            var m = $(e.target).val();
            var selectedRecipientRoles= [];
            this.selectedRoles = m;
            if(this.selectedRoles) {
              this.selectedRoles.forEach(element => {
                  var member  = { id:""};
                  var id=element.substr(element.indexOf(":") + 1);
                  id = id.replace(/'/g, "");
                  member.id = id.replace(/\s/g, '');                
                    selectedRecipientRoles.push(Number(member.id));
                  });
             }
            this.routingRules.patchValue({
                rolesToReceivePatientChats: selectedRecipientRoles.map(String)
            });   
        }); 
        $("#escalation-staff-members").on('change', (e) => {
            var m = $(e.target).val();
            var staffMembers= [];
            this.staffMembers = m;
            if(this.staffMembers) {
              this.staffMembers.forEach(element => {
                  var member  = { id:""};
                  var id=element.substr(element.indexOf(":") + 1);
                  id = id.replace(/'/g, "");
                  member.id = id.replace(/\s/g, '');                
                  staffMembers.push(Number(member.id));
              });
             }
            this.routingRules.patchValue({
                escalationStaffMembers: staffMembers.map(String)
            });
        });
        this.fetchStaffList();
        this.selectedMethod = 1;
        this._enrollService.getTenantRolesByPrivilege('allowEnrollmentInitiation').then((data: any) => {
            this.staffRoles = data;
        });
        var self=this;
        $('#escalation-staff-members').select2({
          placeholder: function () {
            $(this).data('placeholder');
          }
        });
        $('#roles-to-receive-patient-chats').select2({
            placeholder: function () {
              $(this).data('placeholder');
            }
          });
          
    }
    ngOnChanges(){
        if(!isBlank(this.routingRuleData) && !isBlank(this.routingRuleData.id)){
            this.editRule = true;
            this.submitButton = 'Update';
            this.title = 'Edit';
            this.getRuleDetails(this.routingRuleData.id);
        }
    }
    closePopup(){
        this.submitButton = 'Save';
        this.title = 'Add';
        this.routingRules.reset();
        this.siteRequired = false;
        this.messageTagStaffRequired = false;
        this.displayErrorMessage = false;
        this.editRule = false;
        this.isSiteFilterDisabled = false;
        this.allSelectedTag = [];
        this.visible=false;
        $(".display-content").attr('disabled', false);
        $(".display-button").attr('disabled', false);
        this.displayTag = false;
        this.routingRules.controls['routingRuleName'].clearValidators();
        this.routingRules.controls['routingRuleName'].updateValueAndValidity();
        this.routingRules.controls['rolesToReceivePatientChats'].clearValidators();
        this.routingRules.controls['rolesToReceivePatientChats'].updateValueAndValidity();
        this.routingRules.controls['escalationStaffMembers'].clearValidators();
        this.routingRules.controls['escalationStaffMembers'].updateValueAndValidity();
        $('#roles-to-receive-patient-chats').val('');
        $('#roles-to-receive-patient-chats').val($('#roles-to-receive-patient-chats').val()).trigger('change');
        $('#escalation-staff-members').val('');
        $('#escalation-staff-members').val($('#escalation-staff-members').val()).trigger('change');
        this.closeSelectedRecipient();
        this.messageRoutingRule =  true;
        this.allowPatientToSelectRole = false;
        this.selectedMethod = 1; 
        this.routingRules.patchValue({
            routingType: '1'
        });
        this.ruleCreated.emit({ ruleCreated : true});
        this.eventsSubject.next();
        $('#addModal').modal('hide');
    }
    getRuleDetails(id : number){
        this.editRuleSiteData = [];
        this._manageRoutingRulesService.getRuleDetails(id).then((data) => {
            if(data && data['getRulesDetails'] && data['getRulesDetails'].name){
                this.routingTypeData = data['getRulesDetails'].ruleTypeData;
                this.routingType  = Number(data['getRulesDetails'].routingType);
                this.routingRules.patchValue({
                    routingRuleName: data['getRulesDetails'].name,
                    rolesToReceivePatientChats: (data['getRulesDetails'].receiveRoles).split(','),
                    escalationStaffMembers: (data['getRulesDetails'].staffId).split(','),
                    routingType : data['getRulesDetails'].routingType
                });
                this.selectedMethod = data['getRulesDetails'].routingType;
                if(this.selectedMethod == 2){
                    this.editRuleSiteData = (data['getRulesDetails'].routingBy).split(',').map(i => {
                     return Number(i);
                });
                }else {
                    this.selectedRecipients.forEach((val) => {
                    this.removeSelectedRecipient(val);
                 });
         
               if(data['getRulesDetails'].ruleTypeData) {
                var arrayUsertags = []
                for(var i=0;i<(data['getRulesDetails'].ruleTypeData).length;i++) {
                     arrayUsertags.push((data['getRulesDetails'].ruleTypeData[i].id).toString());
                     let selectedIndex = this.selectedRecipients.indexOf((data['getRulesDetails'].ruleTypeData[i].id).toString());
                     if (selectedIndex == -1) {
                          this.selectedRecipients.push((data['getRulesDetails'].ruleTypeData[i].id).toString());
                          this.selectedRecipientNames.push({ text: data['getRulesDetails'].ruleTypeData[i].tagName });
                     }
                     $("<span />", {
                        "class": 'tag-span',
                        'id': (data['getRulesDetails'].ruleTypeData[i].id).toString(),
                        text: data['getRulesDetails'].ruleTypeData[i].tagName,
                        insertBefore: $(".message-search-area")
                     }).append("<span class='remove' id=" + parseInt(data['getRulesDetails'].ruleTypeData[i].id) + ">x</span>");
                }
                }
                }
                $(document).ready(()=>{
                    $('#roles-to-receive-patient-chats').val($('#roles-to-receive-patient-chats').val()).trigger('change');
                    $('#escalation-staff-members').val($('#escalation-staff-members').val()).trigger('change');
                 });
                this.messageRoutingRule = (data['getRulesDetails'].enabled)==1 ? true : false;
                this.disableRule(this.messageRoutingRule);
                this.allowPatientToSelectRole = (data['getRulesDetails'].enabledRoleSelection)==1 ? true : false;      
            }
            var activityData = {
                activityName: "Edit Routing Rule",
                activityType: "Manage Routing rule",
                activityDescription: this.userData.displayName + " has selected rule " + data['getRulesDetails'].name + " to update"
             }; 
            this._structureService.trackActivity(activityData);
        });
     
    }
    getSiteIds(siteId: any) {
        this.siteId = siteId.siteId;
        if(this.selectedMethod ==2 && !isBlank(this.editRuleSiteData)){
            this.siteRequired = (isBlank(this.siteId) || this.siteId=='0');
       }
        this.fetchStaffList().then((val) => {
            if (val && val=='fromedit') {
                const escalatedArray = this.routingRules.value.escalationStaffMembers;
                $('#escalation-staff-members').val(escalatedArray.map(String));
                $(document).ready(()=>{
                    $('#escalation-staff-members').val($('#escalation-staff-members').val()).trigger('change');
                });
            }
        });
       
    }
    hideDropdown(hideItem: any) {
        this.showSiteSelection = hideItem.hideItem;
       
    }
    setVisible(type) {
        if(this.editRule){
             if(this.routingType !=2 && type==2){
                this.editRuleSiteData= [];
            }
            this.allSelectedTag = this.routingTypeData.concat(this.tagsArray);
            this.allSelectedTag = this.allSelectedTag.filter((item, index)=> {
                return (this.selectedRecipients.includes((item.id).toString()) && this.allSelectedTag.indexOf(item) === index);
            });
            this.visible = true;
           
            if(type==1 && isBlank(this.routingTypeData)){
                $(".tag-span").remove();
                this.selectedRecipients = [];
                this.visible = false;
            }
        }else{
                $(".tag-span").remove();
                this.eventsSubject.next();
                this.selectedRecipients = [];
        }
        this.messageTagStaffRequired=false;
        this.selectedMethod = type;
        if(this.selectedMethod!= 2){
           //this.siteId = [];
        }else if(this.editRule) {
            if(!this.editRuleSiteData){
                this.filterType = false;
            }
        }
        this.fetchStaffList();
    }
    populateMessageRoutingRule(type){
        this.messageRoutingRule = type;
        this.disableRule(this.messageRoutingRule);
    }
    disableRule(data) {
        if(!data){
            if(!this.editRule){
                $(".display-button").attr('disabled', true);
                this.allowPatientToSelectRole = false;
            }
            this.displayTag = true;
            $(".display-content").attr('disabled', true);
            this.isSiteFilterDisabled = true;
           
        }else {
            this.displayTag = false;
            $(".display-content").attr('disabled', false);
            $(".display-button").attr('disabled', false);
            this.isSiteFilterDisabled = false;
            if(!this.editRule){
                this.allowPatientToSelectRole = false;  
            }
        }
    }
    populateAllowPatientToSelectRole(type){
        if(!this.messageRoutingRule){
            return false;
        }
        this.allowPatientToSelectRole = type;
    }
    fetchStaffList() : Promise<any> {
        return new Promise(async (resolve) => {
        this._manageSitesService.getStaffList(this.userData.tenantId,'',this.siteId,true).then((result)=>{
            if(result){
              this.scheduleStaff = result; 
              if(this.editRule){
                 resolve('fromedit');
              }
           }
          });
        });
    }
    addRoutingRules(){
        this.routingRules.controls['routingRuleName'].setValidators([Validators.required]);
        this.routingRules.controls['routingRuleName'].updateValueAndValidity();  
        this.routingRules.controls['rolesToReceivePatientChats'].setValidators([Validators.required]); 
        this.routingRules.controls['rolesToReceivePatientChats'].updateValueAndValidity(); 
        this.routingRules.controls['escalationStaffMembers'].setValidators([Validators.required]);  
        this.routingRules.controls['escalationStaffMembers'].updateValueAndValidity();        
        if(this.selectedMethod ==2){
            this.siteRequired = ((isBlank(this.siteId) || this.siteId=='0') && this.showSiteSelection);
            if(this.siteRequired){
                return false;
            }
        }
        if(this.selectedMethod ==1){
            if(isBlank(this.selectedRecipients)){
                this.messageTagStaffRequired = true;
                return false;
            }
        }
        if (!this.routingRules.valid) {
          return false;
        }
        var rolesToReceivePatientChats= [];
        this.rolesToReceivePatientChats = $('#roles-to-receive-patient-chats').val();
        if(this.rolesToReceivePatientChats) {
          this.rolesToReceivePatientChats.forEach(element => {
              var member  = { id:""};
              var id=element.substr(element.indexOf(":") + 1);
              id = id.replace(/'/g, "");
              member.id = id.replace(/\s/g, '');
                rolesToReceivePatientChats.push(Number(member.id));
              });
         }
         var escalationStaffMembers = [];
         this.escalationStaffMembers = $('#escalation-staff-members').val();
         if(this.escalationStaffMembers) {
            this.escalationStaffMembers.forEach(element => {
                var member  = { id:""};
                var id=element.substr(element.indexOf(":") + 1);
                id = id.replace(/'/g, "");
                member.id = id.replace(/\s/g, '');
                escalationStaffMembers.push(Number(member.id));
                });
           }
        const formObjData = this.routingRules.value;
        formObjData.escalationStafMembers = escalationStaffMembers;
        formObjData.rolesToReceivePatientChats = rolesToReceivePatientChats;
        formObjData.enableMessageRoutingRule = this.messageRoutingRule;
        formObjData.enableAllowPatientToChatWith = this.allowPatientToSelectRole;
        if (this.selectedMethod == 1) {
            formObjData.routingBy = (this.selectedRecipients).toString();
        } else if (this.selectedMethod == 2) {
            formObjData.routingBy = (this.siteId).toString();
        } else if (this.selectedMethod == 3) {
            formObjData.routingBy = null;
        }
        this.disableSubmitButton = true;
        if(this.editRule){
            formObjData.id = this.routingRuleData.id;
            this._manageRoutingRulesService.updateRoutingRule(formObjData).subscribe((data) => {
             if(data && data['data'] && data['data']['updateRoutingRule'].id){
                var notify = $.notify('Success! Message routing rule updated');
                setTimeout(function () {
                    notify.update({
                      'type': 'success',
                      'message': '<strong>Success! Message routing rule updated</strong>'
                    });
                  }, 1000);
                  if(!isBlank(data['data']['updateRoutingRule'].warning)){
                    var notifyWarning = $.notify(data['data']['updateRoutingRule'].warning);
                    setTimeout(function () {
                        notify.update({
                          'type': 'success',
                          'message': '<strong>'+notifyWarning+'</strong>'
                        });
                      });
                }
                  var activityData = {
                    activityName: "Routing rule updation success",
                    activityType: "Manage Routing rules",
                    activityDescription: data['data']['updateRoutingRule'].message
                 };
                  this.closePopup();
             }else{  
                var activityData = {
                    activityName: "Routing rule updation failed",
                    activityType: "Manage Routing rules",
                    activityDescription: data['data']['updateRoutingRule'].message
                 };
                this.errorMessage = (data['data']['updateRoutingRule'].message);
                this.displayErrorMessage = true;
                this.disableSubmitButton = false;
             }
             this._structureService.trackActivity(activityData);
         })
        }else{
            this._manageRoutingRulesService.addRoutingRule(formObjData).subscribe( (data) => {
                if(data && data['data'] && data['data']['createRoutingRule'].id){
                    var notify = $.notify('Success! Message routing rule created');
                    setTimeout(function () {
                      notify.update({
                        'type': 'success',
                        'message': '<strong>Success! Message routing rule created</strong>'
                      });
                    }, 1000);
                    if(!isBlank(data['data']['createRoutingRule'].warning)){
                        var notifyWarning = $.notify(data['data']['createRoutingRule'].warning);
                        setTimeout(function () {
                            notify.update({
                              'type': 'success',
                              'message': '<strong>'+notifyWarning+'</strong>'
                            });
                          });
                    }
                   var activityData = {
                        activityName: "Routing rule creation success",
                        activityType: "Manage Routing rules",
                        activityDescription: data['data']['createRoutingRule'].message
                     };
                    this.closePopup();
                }else{
                    var activityData = {
                        activityName: "Routing rule creation failed",
                        activityType: "Manage Routing rules",
                        activityDescription: data['data']['createRoutingRule'].message
                     };
                    this.errorMessage = (data['data']['createRoutingRule'].message);
                    this.displayErrorMessage = true;
                    this.disableSubmitButton = false;
                }
                this._structureService.trackActivity(activityData);
            });
        }
     
}
 
    checkRecipientWithTems() {
        var textValue = $("#tagsInput").val();
        var searchText = textValue;//.replace(/[^a-z0-9\+\-\.\#]/ig,'');
        if (textValue != "") {
           this.setRecipient(searchText);
        }
    }
      setRecipient(searchKeyword: any = "") {
        if (!this.recipientLoading) {
            this.recipientLoading = true;
           $("#recipient-search").text(" ").text("Loading...");
            const userData: any = this._structureService.getUserdata();
            const tagGetData =  '?group=3&routing=1' + "&searchKeyword=" + searchKeyword;
            const tagTypes = ["2"]; // Message Tag =1, User Tag =2 , Document Tag =3
            this._enrollService.getTagsByGroup(tagGetData, tagTypes).then((data: any) => {
              this.recipientLoading = false;
              this.messageTagStaffRequired = false;
              if(data){
                this.responsePatient = data;
              }
              $("#recipient-search").text(" ").text("Search");
              this.enableOrDisableUiLI(true, false, "R");  
            }).then(function () {
              this.recipientLoading = false;
    
            }).catch((ex) => {
              this.recipientLoading = false;
            });
        }
      }

  enableOrDisableUiLI(condition, clear: boolean = true, from: any = "") {
    if (condition) {
      if (from == "R") {
        if ($('ul#associate-ul').css('display') == 'block') {
          this.enableOrDisableUiLI(false, false);
        }
        $("#tagsInput").addClass('ul-active');
        $("ul#recipient-ul").css('display', 'block');
      } else {
        if ($('#recipient-ul').css('display') == 'block') {
          this.enableOrDisableUiLI(false, false, "R");
        }
        $("ul#associate-ul").css('display', 'block');
        $("input#associate-search-input").addClass("active");
      }

    } else {
      if (from == "R") {
        $("#tagsInput").removeClass("ul-active");
        $("ul#recipient-ul").css('display', 'none');
      } else {
        $("ul#associate-ul").css('display', 'none');
        $("input#associate-search-input").removeClass("active");
      }

    }
    if (clear) {
      if (from == "R") {
        $("#tagsInput").val("");
        $("#tagsInput").attr("placeholder", "Search User Tags");
      } else {
        $("input#associate-search-input").val('');
      }

    }
  }
  checkUserExist(user) {
    if (this.selectedRecipients.indexOf(String(user)) > -1) {
      return true;
    }
    return false;
  }
  setSelectedRecipients(users, user) {
    let selectedIndex = this.selectedRecipients.indexOf(user);
    if (selectedIndex == -1) {
      this.selectedRecipients.push(user);
     this.selectedRecipientNames.push({ text: users.tag_name });
    this.setSelectedRecipientForms(false, users);
    } else {
     this.removeSelectedRecipient(user);
    }
  }
  doneSelectedRecipient() {
    if (this.selectedRecipients && this.selectedRecipients.length)
      this.enableOrDisableUiLI(false, true, "R");
      $('#tagsInput').text('');
  }
  setSelectedRecipientForms(fromRemove = false, users: any = "") {
    if (this.selectedRecipients && this.selectedRecipients.length > 0) {
     $('#btm-send-form-list').attr('disabled', true);
      var recipients = this.selectedRecipients;
      var recipientName = this.selectedRecipientNames;
      if (recipients.length) {
        this.stafffillfirst = true;
      }
      else {
        this.stafffillfirst = false;
      }
      recipients = recipients.filter(a => a.indexOf('tag') === -1);
      var recipi = this.selectedRecipients;
      if (recipients.length <= 0 && recipi.filter(a => a.indexOf('tag-') !== -1).map(a => a.replace('tag-', '')).join() == "") {
        this.stafffillfirst = false;
      }
    if (!fromRemove) {
       var textData = users.tag_name;
        $("<span />", {
                 "class": 'tag-span',
                 'id': users.id,
                 text: textData,
                 insertBefore: $(".message-search-area")
       }).append("<span class='remove' id=" + users.id + ">x</span>");
        this.tagsArray.push({id : parseInt(users.id), tagName : users.tag_name});
    }
    }
  }
  removeSelectedRecipient(id) {
    var index = this.selectedRecipients.indexOf(id);
    if (index > -1) {
      if (this.selectedRecipients.length == 1) {
           this.deleteRemovedUserFromList(index, id);
        
      } else {
        this.deleteRemovedUserFromList(index, id);
      }
    }
  }
  deleteRemovedUserFromList(index, id) {
    var index2 = this.allSelectedTag.indexOf(id);
    delete this.allSelectedTag[index2];
    delete this.selectedRecipients[index];
    this.setOrResetSelectedItemList(id);
    this.setSelectedRecipientForms(true);
    this.selectedRecipients = this.selectedRecipients.filter((id) => { return id != "" });
  }
  setOrResetSelectedItemList(id) {
    $("#" + id).remove();
    return true;
  }
  selectAllRecipients() {
    if (this.responsePatient && this.responsePatient.length > 0) {
      this.responsePatient.forEach(element => {
        var id = element.id;
        var index = this.selectedRecipients.indexOf(id);
        if (index == -1) {
          this.setSelectedRecipients(element, id);
        }
      });
    }
  }
  closeSelectedRecipient(condition: boolean = false) {
      this.allSelectedTag = [];
      this.routingTypeData = [];
      this.tagsArray = [];
      this.visible = false;
    $("#recipient-search").text(" ").text("Search");
    if (this.selectedRecipients.length == 1) {
          this.resetRecipient(condition);
    } else {
      this.resetRecipient(condition);
    }

  }
  resetRecipient(condition: boolean = false) {
    this.selectedRecipients = [];
    $(".tag-span").remove();
    if (!condition)
      this.enableOrDisableUiLI(false, true, "R");
  }
  messageUserTagOnEnter(event){
    if (event.keyCode === 13) { 
        var searchkey=$(event.target).val();
        if(searchkey){
            this.checkRecipientWithTems();       
        }
      }
  }
}
