
<div class="close-popup"> <button
    type="button"
    class="close"
   (click)="closePopup()"
    aria-label="Close"
  >
    <span aria-hidden="true">&times;</span>
  </button></div>
             <form  class="form-horizontal" #f="ngForm" [formGroup]="routingRules" (ngSubmit)="addRoutingRules()">
                <div class="header-class">
                    <h5><b>{{title}} Patient Message Routing Rule Settings</b></h5>
    
                </div>
                

                <div class="modal-body popup-width">
                    <div class="form-group row">
                        <label class="col-md-3 control-label">{{ 'LABELS.ENABLE_MESSAGE_ROUTING_RULE' | translate}} <i chToolTip="enableMessageRoutingRule" data-animation="false"></i> 
                        </label>
                        <div class="btn-group col-md-5">
                            <div [ngClass]="{'active': messageRoutingRule}" class="btn btn-outline-success btn-sm" (click)="populateMessageRoutingRule(true)">
                                Enable
                            </div>
                            <div [ngClass]="{'active': !messageRoutingRule}" class="btn btn-outline-default btn-sm" (click)="populateMessageRoutingRule(false)">
                                Disable
                            </div>
                        </div>
                    </div>
                        <div class="form-group row">
                            <label class="col-md-3 control-label">{{ 'LABELS.ALLOW_PATIENT_TO_SELECT_THE_ROLE_TO_CHAT_WITH' | translate}}
                                <i chToolTip="allowPatientToSelectRole" data-animation="false"></i>
                            </label>
                            <div class="btn-group col-md-5 div-height">
                                <div [ngClass]="{'active': allowPatientToSelectRole}" class="btn btn-outline-success btn-sm display-content" (click)="populateAllowPatientToSelectRole(true)">
                                    Enable
                                </div>
                                <div [ngClass]="{'active': !allowPatientToSelectRole}" class="btn btn-outline-default btn-sm display-content" (click)="populateAllowPatientToSelectRole(false)">
                                    Disable
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 control-label">{{ 'LABELS.MESSAGE_ROUTING_RULE_NAME' | translate}} * <i chToolTip="ruleName" data-animation="false"></i></label>
                            <div class="col-md-5">
                                <input type="text" class="form-control display-content"  id="routing-rule-name" name="routing-rule-name" formControlName="routingRuleName">
                                <div *ngIf="routingRules.controls['routingRuleName'].errors && (routingRules.controls.routingRuleName?.dirty || routingRules.controls.routingRuleName?.touched || f.submitted)" class="alert alert-danger">
                                    Name is required
                                </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 control-label">{{ 'LABELS.ROUTE_THE_PATIENT_MESSAGES_BY' |translate}} * <i chToolTip="routePatientMessagesBy" data-animation="false"></i></label>
                            <div class="col-md-5 input-div-class">
                                <label class="form-check-label">
                                <input type="radio" id="route" value=1  class="display-content" formControlName="routingType" (click)="setVisible(1)">
                                Message User Tag(s)</label>
                                <label class="form-check-label" *ngIf="userData.config.enable_multisite == 1">
                                <input type="radio" *ngIf="userData.config.enable_multisite == 1" id="route" value=2  class="display-content" formControlName="routingType" (click)="setVisible(2)">
                                Site(s)</label>
                                <label class="form-check-label">
                                <input type="radio" id="route" value=3  class="display-content" formControlName="routingType" (click)="setVisible(3)">
                                Tenant</label>
                            </div>
                        </div>
                        <div class="routing-method-class">
                            <div class="form-group row" id="select-site" [hidden]="!showSiteSelection || selectedMethod != 2">
                                
                                <label class="col-md-3 control-label">Select Site *
                                </label>
                                <div class="col-md-3">
                                    <span *ngIf="!editRule">
                                        <app-select-sites  [events]="eventsSubject.asObservable()"  [hideApplyFilter]=true [disableFilter]="isSiteFilterDisabled" [siteSelection]="true" [singleSelection]=false (siteIds)="getSiteIds($event)" (hideDropdown)="hideDropdown($event)">
                                        </app-select-sites>
                                    </span>
                                    <span *ngIf="editRule">
                                        <app-select-sites  [filterType]="filterType" [events]="eventsSubject.asObservable()" [isEditPage]=true [hideApplyFilter]=true [siteSelection]="true" [disableFilter]="isSiteFilterDisabled" [dynamic]=true [selectedSiteIds]="editRuleSiteData" [singleSelection]=false (siteIds)="getSiteIds($event)" (hideDropdown)="hideDropdown($event)">
                                        </app-select-sites>
                                    </span>
                                       
                                        <div class="alert alert-danger site-position" *ngIf="siteRequired">
                                            Please select site.
                                        </div>
                                </div>
                            </div>
                            <div class="form-group row" id="select-site" [hidden]="selectedMethod != 1">
                                <label class="col-md-3 control-label">Patient Message User Tags *<i chToolTip="patientMessageUserTag" data-animation="false"></i>
                                </label>
                                <div class="col-md-5">
                                    <div id="tags" [ngClass]="{'display-tag':displayTag }">
                                        <div class="message-search-area">
                                            <!-- <span *ngFor="let data of allSelectedTag" [hidden]="!visible" class="tag-span" id="{{data.id}}">{{data.tagName}}<span class="remove" id="{{data.id}}">x</span></span> -->
                                            <div class="input-dropdown">
                                               
                                                <input type="text" [ngClass]="{'display-tag':displayTag }"
                                                    class="form-control" id="tagsInput" autocomplete="off" value="" (keydown)="messageUserTagOnEnter($event)"
                                                    placeholder="Search message user tags" />
                                  
                                                <ul class="associate-ul recipient-ul" id="recipient-ul">
        
                                                    <li id="recipient-li" class="associate-li recipient-li"
                                                        *ngIf="responsePatient && responsePatient.length == 0">
                                                        No item found
                                                    </li>
        
                                                    <li id="li-{{ tag.id }}" class="associate-li recipient-li"
                                                        [ngClass]="{
                                                   'li-selected': checkUserExist(tag.id)
                                                 }" *ngFor="let tag of responsePatient"
                                                        (click)="setSelectedRecipients(tag, tag.id)">
                                                        {{ tag.tag_name }}
                                                    </li>
        
                                                    <li class="render-manipulate"
                                                        *ngIf="responsePatient && responsePatient.length > 0">
                                                        <input type="button" class="recipient-select-all btn"
                                                            (click)="selectAllRecipients()" value="Select All" />
        
                                                        <input type="button" class="recipient-class-clear btn"
                                                            (click)="closeSelectedRecipient(true)"
                                                            value="Clear All" />
        
                                                        <input type="button" class="recipient-class-done btn"
                                                            *ngIf="responsePatient && responsePatient.length > 0"
                                                            (click)="doneSelectedRecipient()" value="Done" />
        
                                                    </li>
                                                </ul>
                                               
                        
                                            </div>
                                            
        
                                            <div>
        
                                                <button type="button" [disabled]="recipientLoading"
                                                    id="recipient-search" (click)="checkRecipientWithTems()"
                                                    class="recipient-search-button btn btn-sm btn-primary">
                                                    Search
                                                </button>
        
                                                <button type="button" [disabled]="recipientLoading"
                                                    id="recipient-close" (click)="closeSelectedRecipient()"
                                                    class="recipient-search-button btn btn-sm btn-default recipient-close">
                                                    Reset
                                                </button>
        
                                            </div>
                                        </div>
                                    </div>
                                  
                                </div>
                              
                            </div>
                            <div class="row"  *ngIf="messageTagStaffRequired">
                                <span class="col-md-3">
    
                                </span>
                                <span class="col-md-5">
                                    <div class="alert alert-danger site-position">
                                        Message User Tag is required
                                    </div>
                                </span>
                                
                            </div>
                           
        
                        </div>
                        <div class="form-group row">
                            <label class="col-md-3 control-label">{{ 'LABELS.ROLES_TO_RECEIVE_PATIENT_CHATS' | translate}} * <i chToolTip="rolesToReceivePatientChats" data-animation="false"></i></label>
                            <div class="col-md-5">
                                    <select  class="form-control select2 display-content" id="roles-to-receive-patient-chats" data-placeholder="Select Roles" formControlName="rolesToReceivePatientChats" multiple>
                                        <option *ngFor="let role of staffRoles" value="{{role.id}}"> {{role.name}} </option>
                                    </select>
                                    <div *ngIf="routingRules.controls['rolesToReceivePatientChats'].errors && (routingRules.controls.rolesToReceivePatientChats?.dirty || routingRules.controls.rolesToReceivePatientChats?.touched || f.submitted)" class="alert alert-danger">
                                        Roles is required
                                    </div>
                            </div>
                        </div>
                        <div class="form-group row">
                            <label  class="col-md-3 control-label">{{ 'LABELS.ESCALATION_STAFF_MEMBERS' | translate}} * <i chToolTip="escalationStaffMembers" data-animation="false"></i></label>
                            <div class="col-md-5">
                                    <select class="form-control select2 display-content" id="escalation-staff-members" formControlName="escalationStaffMembers" data-placeholder="Escalation staff members" multiple>
                                        <option *ngFor="let staff of scheduleStaff" value="{{staff.userId}}"> {{staff.displayname}} </option>
                                    </select>
                                    <div *ngIf="routingRules.controls['escalationStaffMembers'].errors && (routingRules.controls.escalationStaffMembers?.dirty || routingRules.controls.escalationStaffMembers?.touched || f.submitted)" class="alert alert-danger">
                                        Escalation Staff is required
                                    </div>
                            </div>
                        </div>
                    
                   
                    </div>
                <span *ngIf="displayErrorMessage" class="error-message-class">{{errorMessage}}</span>                
                <div class="footer-class">
                    <div id='sch-edit-add-action-btn'>
                        <button type="submit" class="btn btn-success display-button" [disabled]="disableSubmitButton"> {{submitButton}} </button>
                        <button type="button" class="btn btn-secondary" data-dismiss="modal" (click)="closePopup()">Cancel</button>
                    </div>
                </div>
            </form>
