import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AddRoutingRulesComponent } from './add-routing-rules.component';

describe('AddRoutingRulesComponent', () => {
  let component: AddRoutingRulesComponent;
  let fixture: ComponentFixture<AddRoutingRulesComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ AddRoutingRulesComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AddRoutingRulesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
