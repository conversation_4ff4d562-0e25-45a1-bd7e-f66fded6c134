 .header-class {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding: 15px;
}
.footer-class {
    display: -webkit-box;
    display: -ms-flexbox;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: end;
    -ms-flex-pack: end;
    justify-content: flex-end;
    padding: 15px;
}
.form-position {
    margin-top: 38px;
}
.close-popup {
    margin-top: 13px;
    margin-right: 12px;
}
.user-tag-class {
    width: 59px;
    height: 12px;
    margin: -1px -5px 7px -23px;
}
.user-tag-label-class {
    position: absolute;
    margin: -1px -28px 7px -19px;
}
.site-class {
   width: 59px;
   height: 12px;
   margin: -1px -28px 7px 97px;
}
.site-label-class {
  position: absolute;
  position: absolute;
  margin: -1px -28px 7px 6px;
}
.tenant-class {
    width: 59px;
    height: 12px;
    margin: 1px -16px 11px 43px;
}
.tenant-class-multisite-off {
    width: 59px;
    height: 12px;
    margin: 1px -16px 11px 100px; 
}
.tenant-label-class {
   position: absolute;
   margin: -1px -28px 7px -3px;
}
.input-div-class {
    padding-top: 3px;
}
.div-height {
    height: 31px;
}
.routing-method-class {
    padding-top: 15px;
}
.popup-width {
    width: 149%;
}
.error-message-class {
    color : #ff0000;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
}
.display-tag {
    pointer-events: none;
    background-color: #eef0f4;
}