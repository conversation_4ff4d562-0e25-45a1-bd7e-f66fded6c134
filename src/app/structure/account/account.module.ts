import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { ReactiveFormsModule } from '@angular/forms';
import { AccountsComponent } from './accounts.citusHealth';
import { SettingsComponent } from './settings.citusHealth';
// import { ToolTipDirective } from '../shared/tooltip-directive';
import { MessageEscalationComponent } from './message-escalation.citusHealth';
import { PreferranceFeaturesComponent } from './preferrance-and-features.citusHealth';
import { ClinicianRoleComponent } from './clinician-role.citusHealth';
import { SharedModule } from '../shared/sharedModule';
import { AccountSettingsComponent } from './account-settings.component';
import { AuthGuard } from '../../guard/auth.guard';
import { ManageRoutingRulesComponent } from './manage-routing-rules/manage-routing-rules.component';
import { AddRoutingRulesComponent } from './manage-routing-rules/add-routing-rules/add-routing-rules.component';
import { RoutingScheduleCalendarComponent } from './manage-routing-rules/routing-schedule-calendar/routing-schedule-calendar';
import {NgbModule} from '@ng-bootstrap/ng-bootstrap';
import { FilterStaffByStatusWithSearchPipe,SearchFilterPipe, TimeZoneFormatPipe, ResetSelectedUsersPipe } from './manage-routing-rules/filters/filter-staff-by-status-with-search.pipes';
import { SearchFilterRoleTreeViewPipe, SearchRoleFilterPipe } from './manage-routing-rules/filters/filter-staff-by-role-or-user-search.pipes';
import { ListRoutingComponent } from './manage-routing-rules/list-routing/list-routing.component';

export const routes: Routes = [
  //{ path: 'accounts', component: AccountsComponent },
  { path: 'account/settings', component: SettingsComponent },
  {
    path: 'account/account-settings/:accountId', component: AccountSettingsComponent, canActivate: [AuthGuard],
    data: {
      expression: 'privileges.manageTenants',checkRoutingPrivileges:'manageTenants'
    }
  },
  //{ path: 'account/message-escalation', component: MessageEscalationComponent },
  { path: 'account/preferences', component: PreferranceFeaturesComponent },
  //{ path: 'account/clinician-role', component: ClinicianRoleComponent },
  { path: 'account/settings/:accountId', component: SettingsComponent },
  { path: 'account/add-routing-rule', component: AddRoutingRulesComponent },
  { path: 'account/schedule-calender', component: RoutingScheduleCalendarComponent }
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule.forChild(routes),
    SharedModule,
    NgbModule.forRoot()
  ],
  declarations: [
    AccountsComponent,
    SettingsComponent,
    MessageEscalationComponent,
    PreferranceFeaturesComponent,
    AccountSettingsComponent,
    ClinicianRoleComponent,
    ManageRoutingRulesComponent,
    AddRoutingRulesComponent,
    RoutingScheduleCalendarComponent,
    FilterStaffByStatusWithSearchPipe,
    SearchFilterRoleTreeViewPipe,
    SearchRoleFilterPipe,
    SearchFilterPipe, 
    TimeZoneFormatPipe,
    ResetSelectedUsersPipe,
    ListRoutingComponent,
    // toolTipDirective
  ]

})

export class AccountModule { }
