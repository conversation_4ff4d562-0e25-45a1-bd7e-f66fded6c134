import { Component, OnInit } from '@angular/core';
import { StructureService } from '../../structure/structure.service';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { SignService } from '../signatures/sign.service';
import * as io from "socket.io-client";
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;
declare var swal: any;


@Component({
  selector: 'app-preference',
  templateUrl: './preferrance-and-features.html'
})
export class PreferranceFeaturesComponent implements OnInit {

  selectedOptions = [];
  taggedUsers = [];
  member;
  userInfo;
  userData;  
  accountId;
  userRole;
  isDisabled = false;
  tenantId;
  selectedUserList;
  settings;
  hidePreferences = [];
  previlages = {
    superAdmin: false,

  };
  conversationTypeStatus = 0;
  preferences;
  messageForwardBehaviorObject = [
    {
      value: "Forwarding_not_allowed",
      text: ""
    },
    {
      value: "Keep_initial_user_and_forwarded_user_in_the_same_session",
      text: "Keep forwarding user in chat session"
    },
    {
      value: "Remove_initial_user_but_add_forwarded_user_to_session",
      text: "Remove forwarding user from chat session"
    },
    {
      value: "Let_user_decide_per_message",
      text: "Allow user to select preference"
    }


  ];

  accountPreferences: FormGroup;
  teanntRoles;
  dataLoadingMsg = true;
  constructor(
    private _structureService: StructureService,
    private route: ActivatedRoute,
    private router: Router,
    private _formBuild: FormBuilder,
    private _signService: SignService,

  ) {
    route.params.subscribe(val => {
      this.userInfo = this._structureService.loginUserDetails;
      this.accountId = this.userInfo.tenantId;
      
      this.accountId = typeof (this.accountId) === 'undefined' ? this._structureService.getCookie('tenantId') : this.accountId;
      this.userRole = typeof (this.userInfo.roleName) === 'undefined' ? this._structureService.getCookie('userRole') : this.userInfo.roleName;
      if (this.userRole === 'Super Admin') {
        this.previlages.superAdmin = true;
      }
    })
  }
  ngOnInit() {

    var config            = this._structureService.userDataConfig;
    var userDetails       = this._structureService.userDetails;
    var configData        = JSON.parse(config);
    this.userData = JSON.parse(userDetails);
    this.tenantId = this._structureService.getCookie('tenantId');    
    $('.select2').select2({
        placeholder: function(){
            $(this).data('placeholder');
        }
      }
    );    
    $(document).on("keyup", "#homeinfusionStart", function () {
      if(!$('#homeinfusionStart').val()){
        $(this).mask("00:00");
      }
    });
    $(document).on("keyup", "#homeinfusionEnd", function () {
      if(!$('#homeinfusionEnd').val()){
        $(this).mask("00:00");
      }
    });
    $('body').on("keypress","input[type=text]",function(e){
      var code = (e.keyCode ? e.keyCode : e.which);
      if (code == 13) { 
          e.preventDefault();
      }
    });
    $('#taggedUser').on(
      'change',

      (e) => {
        this.selectedOptions = [];
        this.selectedOptions = $(e.target).val()
      }
    );
    this._signService.getAllTenantRoles().then((data) => {
      this.teanntRoles = data;
    }).catch((ex) => {
      //this.loginFailed = true;
    });
    this.accountPreferences = this._formBuild.group({

      officeEmail: [''],
      helplinePhone: [''],
      contactEmail: [''],
      officePhone: [''],
      address: [''],
      sessionTimeout: ['', Validators.required],
      sessionTimeoutWarning: ['', Validators.required],
      chatAutoTranslate: [''],
      chatInfusionSupport: [''],
      gaTracking: [''],
      watchtowerTracking: [''],
      showProtypes: [''],
      showInfusionSupport: [''],
      messageTagging: [''],
      userTagging: [''],
      documentTagging: [''],
      allowPatientDiscussionGroup: [''],
      allowPatientSelfInventory: [''],
      patientsToInitiateChatDuringDay: [''],
      messageForwardBehavior: [''],
      taggedMessageApprover: [''],
      showChatHistoryToNewParticipant: [''],
      showPatientStatusDashboard:[''],
      enableDisplayName:[''],
      patientSignupWithAdminApproval: [''],
      clinicianSignupWithAdminApproval: [''],
      rolesChatWithUsNow: [''],
      rolesWhereIsMyDelivery: [''],
      rolesWhenNurseArrive: [''],
      rolesIhaveQuestion: [''],
      homeinfusionStart: [''],
      homeinfusionEnd: [''],
      homeInfusionStartTime: ['12'],
      homeinfusionEndTime: ['24'],
      rolesDefaultNurses:[''],
      conversationType: [''],
      chatRole1DisplayName:[''],
      chatRole2DisplayName:[''],
      chatRole1MessageTemplate: [''],
      chatRole2MessageTemplate: [''],
      initiationMessage: [''],
      chatRole1: [''],
      chatRole2: [''],
      enableFilingCenter: [''],
      enableMaskedDiscussionGroup: ['']
    });
    
    this._structureService.getPreferences().then(( 
      data ) => {
      if (data['getSessionTenant']) {
        this.dataLoadingMsg = false;
        this.preferences = data['getSessionTenant'];
        if (this.preferences.configurationSettings.length>0) {
           this.preferences.configurationSettings.forEach(element => {
              if (element.disable) {
                  this.hidePreferences.push(element.configuration);
              }
           });
        }
        
       
        this.accountPreferences.patchValue({
          sessionTimeout: this.preferences.sessionTimeoutInSec,
          sessionTimeoutWarning: this.preferences.sessionTimeoutWarningInSec,
          messageForwardBehavior: this.preferences.messageForwardBehavior,
          chatAutoTranslate: this.preferences.chatAutoTranslate,
          chatInfusionSupport: this.preferences.infusionSupport,
          gaTracking: this.preferences.gATracking,
          watchtowerTracking: this.preferences.watchTowerTracking,
          showProtypes: this.preferences.showPrototypes,
          showInfusionSupport: this.preferences.infusionSupport,
          messageTagging: this.preferences.messageTagging,
          userTagging: this.preferences.userTagging,
          documentTagging: this.preferences.documentTagging,
          allowPatientDiscussionGroup: this.preferences.patientDiscussionGroup,
          allowPatientSelfInventory: this.preferences.patientSelfInventory,
          patientsToInitiateChatDuringDay: this.preferences.patientsToInitiateChatDuringDay,
          taggedMessageApprover: this.preferences.taggedMessageApprover,
          showChatHistoryToNewParticipant: this.preferences.showChatHistoryToNewParticipant,
          showPatientStatusDashboard:this.preferences.showPatientStatusDashboard,
         // enableDisplayName:this.preferences.enableDisplayName,
          enableDisplayName:true,
          patientSignupWithAdminApproval: this.preferences.patientSignupWithAdminApproval,
          clinicianSignupWithAdminApproval: this.preferences.clinicianSignupWithAdminApproval,
          rolesChatWithUsNow: this.preferences.messageDestinationConfig.rolesChatWithUsNow ? this.preferences.messageDestinationConfig.rolesChatWithUsNow : [],
          rolesWhereIsMyDelivery: this.preferences.messageDestinationConfig.rolesWhereIsMyDelivery ? this.preferences.messageDestinationConfig.rolesWhereIsMyDelivery : [],
          rolesWhenNurseArrive: this.preferences.messageDestinationConfig.rolesWhenNurseArrive ? this.preferences.me8ssageDestinationConfig.rolesWhenNurseArrive : [],
          rolesIhaveQuestion: this.preferences.messageDestinationConfig.rolesIhaveQuestion ? this.preferences.messageDestinationConfig.rolesIhaveQuestion : [],
          rolesDefaultNurses: this.preferences.messageDestinationConfig.rolesDefaultNurses ? this.preferences.messageDestinationConfig.rolesDefaultNurses : [],
          conversationType: this.preferences.conversationType,
          chatRole1DisplayName: this.preferences.chatRole1DisplayName,
          chatRole2DisplayName: this.preferences.chatRole2DisplayName,
          chatRole1MessageTemplate: this.preferences.chatRole1MessageTemplate,
          chatRole2MessageTemplate: this.preferences.chatRole2MessageTemplate,
          initiationMessage: this.preferences.initiationMessage,
          chatRole1: this.preferences.chatRole1,
          chatRole2: this.preferences.chatRole2,
          enableFilingCenter: this.preferences.enableFilingCenter,
          enableMaskedDiscussionGroup: this.preferences.enableMaskedDiscussionGroup
        });
        this.conversationTypeStatus = this.preferences.conversationType;
        console.log("conversationTypeStatus: ",this.conversationTypeStatus);
        if (this.preferences.messageDestinationConfig.startTime && this.preferences.messageDestinationConfig.startTime.indexOf(":") !== -1) {
          let startTime = this._structureService.clientToGmtTime(this.preferences.messageDestinationConfig.startTime, true, undefined, undefined);
          startTime = this._structureService.convertTO12Hour(startTime);
          if (startTime.indexOf(":") !== -1) {
            var homeInfusionStartTime = startTime.split(' ');
            console.log(homeInfusionStartTime);
            startTime = homeInfusionStartTime[0];
            if (homeInfusionStartTime[1] == 'AM') {
              this.accountPreferences.patchValue({
                homeInfusionStartTime: 12,
                homeinfusionStart: startTime
              });
            } else {
              this.accountPreferences.patchValue({
                homeInfusionStartTime: 24,
                homeinfusionStart: startTime
              });
            }
          }
        }

        if (this.preferences.messageDestinationConfig.endTime && this.preferences.messageDestinationConfig.endTime.indexOf(":") !== -1) {
          let endTime = this._structureService.clientToGmtTime(this.preferences.messageDestinationConfig.endTime, true, undefined, undefined);
          endTime = this._structureService.convertTO12Hour(endTime);
          if (endTime.indexOf(":") !== -1) {
            var homeInfusionEndTime = endTime.split(' ');
            console.log(homeInfusionEndTime);
            endTime = homeInfusionEndTime[0];
            if (homeInfusionEndTime[1] == 'AM') {
              this.accountPreferences.patchValue({
                homeinfusionEndTime: 12,
                homeinfusionEnd: endTime
              });
            } else {
              this.accountPreferences.patchValue({
                homeinfusionEndTime: 24,
                homeinfusionEnd: endTime
              });
            }
          }
        }
        var rolesChatWithUsNowInput = [];
        var rolesWhereIsMyDeliveryInput = [];
        var rolesWhenNurseArriveInput = [];
        var rolesIhaveQuestionInput = [];
        var rolesDefaultNursesInput = [];

        var chatRole1Input = [];
        var chatRole2Input = [];

        if (this.preferences.messageDestinationConfig && this.preferences.patientsToInitiateChatDuringDay) {
          this.preferences.messageDestinationConfig.chatwithUsNowRolesAfterDay.map(function (roles) {
            rolesChatWithUsNowInput.push(String(roles.id));
          });
          this.preferences.messageDestinationConfig.whereIsMyDeliveryRolesAfterDay.map(function (roles) {
            rolesWhereIsMyDeliveryInput.push(String(roles.id));
          });
          this.preferences.messageDestinationConfig.whenWillMyNurseArriveRolesAfterDay.map(function (roles) {
            rolesWhenNurseArriveInput.push(String(roles.id));
          });
          this.preferences.messageDestinationConfig.iNeedHelpOrIHaveAQuestionRolesAfterDay.map(function (roles) {
            rolesIhaveQuestionInput.push(String(roles.id));
          });
          this.preferences.messageDestinationConfig.defaultClinicianRolesAfterDay.map(function (roles) {
            rolesDefaultNursesInput.push(String(roles.id));
          });
          console.log(rolesChatWithUsNowInput);


        } else if (this.preferences.messageDestinationConfig && !this.preferences.patientsToInitiateChatDuringDay) {
          this.preferences.messageDestinationConfig.chatwithUsNowRolesDuringDay.map(function (roles) {
            rolesChatWithUsNowInput.push(String(roles.id));
          });
          this.preferences.messageDestinationConfig.whereIsMyDeliveryRolesDuringDay.map(function (roles) {
            rolesWhereIsMyDeliveryInput.push(String(roles.id));
          });
          this.preferences.messageDestinationConfig.whenWillMyNurseArriveRolesDuringDay.map(function (roles) {
            rolesWhenNurseArriveInput.push(String(roles.id));
          });
          this.preferences.messageDestinationConfig.iNeedHelpOrIHaveAQuestionRolesDuringDay.map(function (roles) {
            rolesIhaveQuestionInput.push(String(roles.id));
          });
          this.preferences.messageDestinationConfig.defaultClinicianRolesDuringDay.map(function (roles) {
            rolesDefaultNursesInput.push(String(roles.id));
          });
          console.log(rolesChatWithUsNowInput);
        }

        if(this.preferences.chatRole1){
          this.preferences.chatRole1.map(function (roles) {
            chatRole1Input.push(String(roles.id));
          });
        }
        if(this.preferences.chatRole2){
          this.preferences.chatRole2.map(function (roles) {
            chatRole2Input.push(String(roles.id));
          });
        }
        
        this.accountPreferences.patchValue({
          rolesChatWithUsNow: rolesChatWithUsNowInput,
          rolesWhereIsMyDelivery: rolesWhereIsMyDeliveryInput,
          rolesWhenNurseArrive: rolesWhenNurseArriveInput,
          rolesIhaveQuestion: rolesIhaveQuestionInput,
          rolesDefaultNurses: rolesDefaultNursesInput,
          chatRole1: chatRole1Input,
          chatRole2: chatRole2Input,
        });
        setTimeout(function () {
          $(".select2").select2({});
        }, 500)
        if (this.preferences.taggedMessageApprover.length > 0) {
          var idString = '';
          var idArray = [];
          for (var k = 0; k < this.preferences.taggedMessageApprover.length; k++) {
            this.selectedOptions.push(this.preferences.taggedMessageApprover[k].id);
          }

        }

        this.selectedUserList = [];

        for (var i = 0; i < data['getSessionTenant']['staffUsers'].length; i++) {
          var ismatch = false;
          if (data['getSessionTenant']['taggedMessageApprover'].length > 0) {
            for (var j = 0; j < data['getSessionTenant']['taggedMessageApprover'].length; j++) {
              if (data['getSessionTenant']['staffUsers'][i].id == data['getSessionTenant']['taggedMessageApprover'][j].id) {
                var obj = { 'id': data['getSessionTenant']['staffUsers'][i].id, 'displayName': data['getSessionTenant']['staffUsers'][i].displayName, 'status': true };
                this.selectedUserList[i] = obj;
                break
              } else {
                var obj = { 'id': data['getSessionTenant']['staffUsers'][i].id, 'displayName': data['getSessionTenant']['staffUsers'][i].displayName, 'status': false };
                this.selectedUserList[i] = obj;

              }
            }
          } else {
            var obj = { 'id': data['getSessionTenant']['staffUsers'][i].id, 'displayName': data['getSessionTenant']['staffUsers'][i].displayName, 'status': false };
            this.selectedUserList[i] = obj;
          }

        }
      } else {
        this._structureService.deleteCookie('authenticationToken');
        this.router.navigate(['/login']);
      }


    }
    );

  }
  isValidTime(time) {
    var pattern = /^(1[0-2]|0?[1-9]):[0-5][0-9]$/;
    return pattern.test(time);
  };
  togglePreference(preference, value) {
    if (preference === 'showChatHistoryToNewParticipant') {
      this.accountPreferences.patchValue({
        showChatHistoryToNewParticipant: value
      });
    }else if (preference === 'showPatientStatusDashboard') {
      this.accountPreferences.patchValue({
        showPatientStatusDashboard: value
      });
    }else if (preference === 'enableDisplayName') {
      this.accountPreferences.patchValue({
        enableDisplayName: true
      });
    }else if (preference === 'chatAutoTranslate') {
      this.accountPreferences.patchValue({
        chatAutoTranslate: value
      });
    } else if (preference === 'showInfusionSupport') {
      this.accountPreferences.patchValue({
        showInfusionSupport: value
      });
    } else if (preference === 'gaTracking') {
      this.accountPreferences.patchValue({
        gaTracking: value
      });
    } else if (preference === 'watchtowerTracking') {
      this.accountPreferences.patchValue({
        watchtowerTracking: value
      });
    } else if (preference === 'showProtypes') {
      this.accountPreferences.patchValue({
        showProtypes: value
      });
    } else if (preference === 'messageTagging') {
      this.accountPreferences.patchValue({
        messageTagging: value
      });
    } else if (preference === 'userTagging') {
      this.accountPreferences.patchValue({
        userTagging: value
      });
    } else if (preference === 'documentTagging') {
      this.accountPreferences.patchValue({
        documentTagging: value
      });
    } else if (preference === 'allowPatientDiscussionGroup') {
      this.accountPreferences.patchValue({
        allowPatientDiscussionGroup: value
      });
    } else if (preference === 'allowPatientSelfInventory') {
      this.accountPreferences.patchValue({
        allowPatientSelfInventory: value
      });
    } else if (preference === 'patientsToInitiateChatDuringDay') {

      this.accountPreferences.patchValue({
        patientsToInitiateChatDuringDay: value
      });
    } else if (preference === 'chatInfusionSupport') {
      this.accountPreferences.patchValue({
        chatInfusionSupport: value
      });
    } else if (preference === 'patientSignupWithAdminApproval') {
      this.accountPreferences.patchValue({
        patientSignupWithAdminApproval: value
      });
    } else if (preference === 'clinicianSignupWithAdminApproval') {
      this.accountPreferences.patchValue({
        clinicianSignupWithAdminApproval: value
      });
    }else if (preference === 'enableFilingCenter') {
      this.accountPreferences.patchValue({
        enableFilingCenter: value
      });
    }
    else if (preference === 'enableMaskedDiscussionGroup') {
      this.accountPreferences.patchValue({
        enableMaskedDiscussionGroup: value
      });
    }
  }

  toggleConversationType(status){
    console.log(status);
    this.conversationTypeStatus = status;
  }
  updateSettings() {
    swal({
      title: "Are you sure?",
      text: "You are updating the preferences & features configuration",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Ok",
      closeOnConfirm: true
    }, () => {
      if(!this.accountPreferences.value['homeinfusionStart'] && !this.accountPreferences.value['patientsToInitiateChatDuringDay']){
        this._structureService.notifyMessage({
          messge:'Start Time value cannot be empty'
        });
      }else if(!this.accountPreferences.value['homeinfusionEnd'] && !this.accountPreferences.value['patientsToInitiateChatDuringDay']){
        this._structureService.notifyMessage({
          messge:'End Time value cannot be empty'
        });
      }else if (!this.isValidTime(this.accountPreferences.value['homeinfusionStart'])) {
        this._structureService.notifyMessage({
          messge:'Start Time is not valid'
        });
      } else if (!this.isValidTime(this.accountPreferences.value['homeinfusionEnd'])) {
        this._structureService.notifyMessage({
          messge:'End Time is not valid'
        });
      } else {
        if (this.selectedOptions) {
          this.selectedOptions.forEach(element => {
            this.member = { id: "" };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            this.member.id = id.replace(/\s/g, '');
            this.taggedUsers.push(this.member);
          });
        }
        this.accountPreferences.patchValue({
          taggedMessageApprover: this.taggedUsers
        });
        const formObjData = this.accountPreferences.value;
        console.log(formObjData);

        var rolesChatWithUsNow = [];
        var rolesWhenNurseArrive = [];
        var rolesWhereIsMyDelivery = [];
        var rolesIhaveQuestion = [];
        var rolesDefaultNurses = [];

        var chatRole1 = [];
        var chatRole2 = [];

        if ($('#rolesChatWithUsNow').val()) {
          $('#rolesChatWithUsNow').val().forEach(element => {
            var member = { id: "" };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
            rolesChatWithUsNow.push(member);
          });
        }

        if ($('#rolesWhenNurseArrive').val()) {
          $('#rolesWhenNurseArrive').val().forEach(element => {
            var member = { id: "" };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
            rolesWhenNurseArrive.push(member);
          });
        }

        if ($('#rolesWhereIsMyDelivery').val()) {
          $('#rolesWhereIsMyDelivery').val().forEach(element => {
            var member = { id: "" };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
            rolesWhereIsMyDelivery.push(member);
          });
        }

        if ($('#rolesIhaveQuestion').val()) {
          $('#rolesIhaveQuestion').val().forEach(element => {
            var member = { id: "" };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
            rolesIhaveQuestion.push(member);
          });
        }

        if ($('#rolesDefaultNurses').val()) {
          $('#rolesDefaultNurses').val().forEach(element => {
            var member = { id: "" };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
            rolesDefaultNurses.push(member);
          });
        }

        if ($('#chatRole1').val()) {
          $('#chatRole1').val().forEach(element => {
            var member = { id: "" };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
            chatRole1.push(member);
          });
        }

        if ($('#chatRole2').val()) {
          $('#chatRole2').val().forEach(element => {
            var member = { id: "" };
            var id = element.substr(element.indexOf(":") + 1);
            id = id.replace(/'/g, "");
            member.id = id.replace(/\s/g, '');
            chatRole2.push(member);
          });
        }

        var homeInfusionStartTimeHours;
        var homeInfusionStartTimeMinutes;
        var homeInfusionEndTimeHours;
        var homeInfusionEndtTimeMinutes;
        var homeinfusionEndSave;
        var homeinfusionStartSave;
        if (this.accountPreferences.value['homeInfusionStartTime'] && this.accountPreferences.value['homeInfusionStartTime'] == 24) {
          if (this.accountPreferences.value['homeinfusionStart'] && this.accountPreferences.value['homeinfusionStart'].indexOf(":") !== -1) {
            homeInfusionStartTimeHours = this.accountPreferences.value['homeinfusionStart'].split(":")[0] * 1;
            homeInfusionStartTimeMinutes = this.accountPreferences.value['homeinfusionStart'].split(":")[1];
            homeInfusionStartTimeHours = homeInfusionStartTimeHours != 12 ? homeInfusionStartTimeHours + 12 : 12;
            homeinfusionStartSave = homeInfusionStartTimeHours + ":" + homeInfusionStartTimeMinutes;
          }
        } else {
          if (this.accountPreferences.value['homeinfusionStart'] && this.accountPreferences.value['homeinfusionStart'].indexOf(":") !== -1) {
            homeInfusionStartTimeHours = this.accountPreferences.value['homeinfusionStart'].split(":")[0] * 1;
            homeInfusionStartTimeMinutes = this.accountPreferences.value['homeinfusionStart'].split(":")[1];
            if (homeInfusionStartTimeHours == 12) {
              homeInfusionStartTimeHours = 0;
            }
            homeinfusionStartSave = homeInfusionStartTimeHours + ":" + homeInfusionStartTimeMinutes;
          }
        }
        
        if (this.accountPreferences.value['homeinfusionEndTime'] && this.accountPreferences.value['homeinfusionEndTime'] == 24) {
          if (this.accountPreferences.value['homeinfusionEnd'] && this.accountPreferences.value['homeinfusionEnd'].indexOf(":") !== -1) {
            homeInfusionEndTimeHours = this.accountPreferences.value['homeinfusionEnd'].split(":")[0] * 1;
            homeInfusionEndtTimeMinutes = this.accountPreferences.value['homeinfusionEnd'].split(":")[1];
            homeInfusionEndTimeHours = homeInfusionEndTimeHours != 12 ? homeInfusionEndTimeHours + 12 : 12;
            homeinfusionEndSave = homeInfusionEndTimeHours + ":" + homeInfusionEndtTimeMinutes;
          }
        } else {
          if (this.accountPreferences.value['homeinfusionEnd'] && this.accountPreferences.value['homeinfusionEnd'].indexOf(":") !== -1) {
            homeInfusionEndTimeHours = this.accountPreferences.value['homeinfusionEnd'].split(":")[0] * 1;
            homeInfusionEndtTimeMinutes = this.accountPreferences.value['homeinfusionEnd'].split(":")[1];
            if (homeInfusionEndTimeHours == 12) {
              homeInfusionEndTimeHours = 0;
            }
            homeinfusionEndSave = homeInfusionEndTimeHours + ":" + homeInfusionEndtTimeMinutes;
          }
        }
        homeinfusionStartSave = this._structureService.clientToGmtTime(homeinfusionStartSave, false, undefined, undefined);
        homeinfusionEndSave = this._structureService.clientToGmtTime(homeinfusionEndSave, false, undefined, undefined);
        var messageDestinationConfig = {}
        if (!this.accountPreferences.value['patientsToInitiateChatDuringDay']) {
          messageDestinationConfig = {
            chatwithUsNowRolesAfterDay: [],
            chatwithUsNowRolesDuringDay: rolesChatWithUsNow,
            iNeedHelpOrIHaveAQuestionRolesAfterDay: [],
            iNeedHelpOrIHaveAQuestionRolesDuringDay: rolesIhaveQuestion,
            whenWillMyNurseArriveRolesAfterDay: [],
            whenWillMyNurseArriveRolesDuringDay: rolesWhenNurseArrive,
            whereIsMyDeliveryRolesAfterDay: [],
            whereIsMyDeliveryRolesDuringDay: rolesWhereIsMyDelivery,
            defaultClinicianRolesDuringDay: rolesDefaultNurses,
            defaultClinicianRolesAfterDay: [],
            startTime: homeinfusionStartSave,
            endTime: homeinfusionEndSave
          }
        } else {
          messageDestinationConfig = {
            chatwithUsNowRolesAfterDay: rolesChatWithUsNow,
            chatwithUsNowRolesDuringDay: [],
            iNeedHelpOrIHaveAQuestionRolesAfterDay: rolesIhaveQuestion,
            iNeedHelpOrIHaveAQuestionRolesDuringDay: [],
            whenWillMyNurseArriveRolesAfterDay: rolesWhenNurseArrive,
            whenWillMyNurseArriveRolesDuringDay: [],
            whereIsMyDeliveryRolesAfterDay: rolesWhereIsMyDelivery,
            whereIsMyDeliveryRolesDuringDay: [],
            defaultClinicianRolesAfterDay: rolesDefaultNurses,
            defaultClinicianRolesDuringDay: [],
            startTime: homeinfusionStartSave,
            endTime: homeinfusionEndSave
          }
        }
        formObjData.messageDestinationConfig = messageDestinationConfig;
        formObjData.chatRole1 = chatRole1;
        formObjData.chatRole2 = chatRole2;
        console.log(formObjData.messageDestinationConfig);
        //console.log("formObjData : ",formObjData, chatRole1 , chatRole2);
        this.isDisabled=true;
       
        var updatedBy = {
          displayName : this.userData['displayName'],
          userid : this.userData.userId ?this.userData.userId : 0,
        };
        console.log("loginUserDetails- ",this.userData , updatedBy);
        var self = this;
        this._structureService.updatePreferences(formObjData).then(
          (data) => {
            this.isDisabled=false;
            var notify = $.notify('Success! Account preferences & features updated');
            setTimeout(function () {
              notify.update({ 'type': 'success', 'message': '<strong>Success! Account preferences & features updated</strong>' });
              
              var updateConfigPollingtoServer = {
                configurationType: "updateTenant",
                role: 0,
                tenantid: self._structureService.getCookie('tenantId'),
                updatedBy: updatedBy,
              }; 
              self._structureService.socket.emit("updateConfigPollingtoServer", updateConfigPollingtoServer);
            }, 1000);

          }
        );
      }
    });

  }
}

