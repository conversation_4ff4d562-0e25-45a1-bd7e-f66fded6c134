
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';

import { RouterModule, Routes } from '@angular/router';
import { CustomFieldComponent } from './custom-field/custom-field.component';
import { AuthGuard } from '../../guard/auth.guard';
import { SharedModule } from '../shared/sharedModule';
export const routes: Routes = [
  {
    path: 'form',
    canActivate: [AuthGuard],
    data: { checkUserGroupPermission:'3' }, // checkRoutingPrivileges need to be finalized and will be added here
    children: [
      { path: 'custom-fields', component: CustomFieldComponent },
      { path: '**', redirectTo: 'custom-fields' } 
    ]
  }
];

@NgModule({
  imports: [CommonModule, SharedModule, RouterModule.forChild(routes)],
  declarations: [CustomFieldComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class CustomFieldGenerationModule {}