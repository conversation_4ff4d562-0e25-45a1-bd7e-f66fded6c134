import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ChangeDetectionStrategy, ViewEncapsulation } from '@angular/core';
import { StructureService } from '../../structure.service';
@Component({
  selector: 'app-custom-field',
  templateUrl: './custom-field.component.html',
})
export class CustomFieldComponent implements OnInit {

  userData: any;
  submittedData: any[] = [];

  constructor(private structureService: StructureService) {}

  ngOnInit(): void {
    this.userData = JSON.parse(this.structureService.userDetails);
    this.structureService.loadMicroFrontend();
  }

  ngOnDestroy(): void {
    this.structureService.unLoadMicroFrontend();
  }
 
  handleDataSubmission(data: any) {
    let message;
    let type;
    if (data.detail && data.detail.success && data.detail.status.message) {
      message = data.detail.status.message;
      type = 'success';

    } else if (data.detail && data.detail.errors && data.detail.errors.length > 0) {
      message = data.detail.errors[0].message;
      type = 'danger';
  
    }
    if (message && type) {
      this.structureService.notifyMessage({
        message,
        delay: 1000,
        type
      });
    }
  }
}
