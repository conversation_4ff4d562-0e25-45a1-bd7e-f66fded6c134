import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { CustomFieldComponent } from './custom-field.component';
import { StructureService } from '../../structure.service';
import { RouterTestingModule } from '@angular/router/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
describe('CustomFieldComponent', () => {
  let component: CustomFieldComponent;
  let fixture: ComponentFixture<CustomFieldComponent>;
  let structureServiceSpy: StructureService;

  beforeEach(async(() => {
    structureServiceSpy = jasmine.createSpyObj('StructureService', [
      'loadMicroFrontend',
      'unLoadMicroFrontend',
      'notifyMessage'
    ]);
    structureServiceSpy.userDetails = JSON.stringify({ name: 'Test User' });

    TestBed.configureTestingModule({
      declarations: [CustomFieldComponent],
      imports: [RouterTestingModule],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
      providers: [{ provide: StructureService, useValue: structureServiceSpy }]
    }).compileComponents();
  }));

  beforeEach(() => {
    
    fixture = TestBed.createComponent(CustomFieldComponent);
    component = fixture.componentInstance;
    component.userData = { name: 'Test User' };
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  it('should call structureService.loadMicroFrontend on init', () => {
    component.ngOnInit();
    expect(structureServiceSpy.loadMicroFrontend).toHaveBeenCalled();
  });

  it('should call structureService.unLoadMicroFrontend on destroy', () => {
    component.ngOnDestroy();
    expect(structureServiceSpy.unLoadMicroFrontend).toHaveBeenCalled();
  });

  it('should call notifyMessage on successful data submission', () => {
    const mockData = { detail: { success: true, message: 'Success message' } };
    component.handleDataSubmission(mockData);
    expect(structureServiceSpy.notifyMessage).toHaveBeenCalledWith({
      messge: 'Success message',
      delay: 1000,
      type: 'success'
    });
  });

  it('should call notifyMessage on failed data submission', () => {
    const mockData = { detail: { success: false, errors: [{ message: 'Error message' }] } };
    component.handleDataSubmission(mockData);
    expect(structureServiceSpy.notifyMessage).toHaveBeenCalledWith({
      messge: 'Error message',
      delay: 1000,
      type: 'danger'
    });
  });
});