import { Injectable, Pipe, PipeTransform } from '@angular/core';
declare var $: any;
declare var jQuery: any;

@Pipe({
    name: 'pluralizefilter'
   })
   
   @Injectable()
   export class pluralizeFilterPipe implements PipeTransform {
    transform(items: string, field: string, value: string): string {
        var pluralLetter = items.search(/(s|sh|ch|x|z)$/)!=-1?'es':'s';
        //new RegExp(/(s|sh|ch|x|z)$/).test(name)
        //console.log("pluralizeFilterPipe ::: ", items, items+pluralLetter);
        return items+pluralLetter;
    }
   }
@Pipe({
    name: 'caregiverOrAlternate',
})
export class caregiverOrAlternatePipe implements PipeTransform {
    transform(items: any[], filter: String): any {
        if (!items || !filter) {
            return items;
        }
        return items.filter(item => (('name' in item && item.name !== filter) || !('name' in item)));
    }
}