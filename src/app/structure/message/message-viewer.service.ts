import { Injectable } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import { Subject } from 'rxjs/Subject';
import 'rxjs/add/operator/toPromise';
import { StructureService } from '../structure.service';
@Injectable()
export class MessageViewerService {
data;
generateChatReportUrl;
generateChatReportUrlMovefilingCenter;
getUserDetailsByIdUrl;
getPatientUserDetailsUrl;
updateMessageDetailsApi;
deleteMessageTagDataApi;
messageCommentDataApi;
iconPath;
  constructor(
    private _http: Http,
    private _structureService:StructureService
  ) {
    
    this.generateChatReportUrl = _structureService.apiBaseUrl+"citus-health/" + _structureService.version + "/generate-chatReport-pdf.php";
    this.generateChatReportUrlMovefilingCenter = _structureService.apiBaseUrl+"citus-health/" + _structureService.version + "/generate-chatReport-infiling-center-pdf.php";
    this.getUserDetailsByIdUrl = _structureService.apiBaseUrl+"citus-health/" + _structureService.version + "/get-userdetails-by-userid.php";
    this.getPatientUserDetailsUrl = _structureService.apiBaseUrl+"citus-health/" + _structureService.version + "/get-patient-user-details.php";
    this.updateMessageDetailsApi = _structureService.apiBaseUrl+"citus-health/" + _structureService.version + "/update-message-from-chat-log.php";
    this.deleteMessageTagDataApi = _structureService.apiBaseUrl+"citus-health/" + _structureService.version + "/delete-message-tag-from-chat-log.php";
    this.messageCommentDataApi = _structureService.apiBaseUrl+"citus-health/" + _structureService.version + "/create-message-comment-from-chat-log.php";
  }

    generateChatReportMovefilingCenter(generateChatReportPdfData, type){
    var data = '';
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.generateChatReportUrlMovefilingCenter;
    let data = "chatRoomId=" + generateChatReportPdfData.chatRoomDetails.chatRoomId + "&initiater=" + generateChatReportPdfData.chatRoomDetails.initiater + "&initiaterType=" + generateChatReportPdfData.chatRoomDetails.initiaterType + "&timezoneOffset=" + generateChatReportPdfData.timezoneOffset + "&startDate=" + generateChatReportPdfData.startDate + "&endDate=" + generateChatReportPdfData.endDate+ "&userId="+generateChatReportPdfData.userId + "&filteredUsers=" + generateChatReportPdfData.filteredUsers+ "&formattedStartDate="+generateChatReportPdfData.formattedStartDate + "&formattedEndDate="+generateChatReportPdfData.formattedEndDate+"&requestFrom="+type+"&timezone="+generateChatReportPdfData.timezoneName+"&zone="+generateChatReportPdfData.zone+"&downloadTime="+generateChatReportPdfData.downloadTime;

       this._http.post(apiURL,data,options)
        .toPromise()
        .then(
          
        res => {
          const result = res;
          resolve(result);
        }
        );
    });
    return promise;
  }
  generateChatReportPdf(generateChatReportPdfData, type){
    var data = '';
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.generateChatReportUrl;
    let data = "chatRoomId=" + generateChatReportPdfData.chatRoomDetails.chatRoomId + "&initiater=" + generateChatReportPdfData.chatRoomDetails.initiater + "&initiaterType=" + generateChatReportPdfData.chatRoomDetails.initiaterType + "&timezoneOffset=" + generateChatReportPdfData.timezoneOffset + "&startDate=" + generateChatReportPdfData.startDate + "&endDate=" + generateChatReportPdfData.endDate+ "&userId="+generateChatReportPdfData.userId + "&filteredUsers=" + generateChatReportPdfData.filteredUsers+ "&formattedStartDate="+generateChatReportPdfData.formattedStartDate + "&formattedEndDate="+generateChatReportPdfData.formattedEndDate+"&requestFrom="+type+"&timezone="+generateChatReportPdfData.timezoneName+"&zone="+generateChatReportPdfData.zone+"&downloadTime="+generateChatReportPdfData.downloadTime;

       this._http.post(apiURL,data,options)
        .toPromise()
        .then(
          
        res => {
          const result = res;
          resolve(result);
        }
        );
    });
    return promise;
  }
  
  getPatientUserDetails(id){
    var data = '';
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.getPatientUserDetailsUrl+"?userId=" + id;

       this._http.post(apiURL,data,options)
        .toPromise()
        .then(
          
        res => {
          const result = res.json();
          resolve(result);
        }
        );
    });
    return promise;
  }
  getUserDetailsById(id){
    var data = '';
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.getUserDetailsByIdUrl+"?userId=" + id;

       this._http.post(apiURL,data,options)
        .toPromise()
        .then(
          
        res => {
          const result = res;
          resolve(result);
        }
        );
    });
    return promise;
  }
  updateMessageDetails(saveDetails,patientIdValue=0){
    var data:any = saveDetails;
    console.log("UpdateData",data);
    let data1 = JSON.stringify(data);
    console.log("UpdateData",data1);

    var userData = JSON.parse(this._structureService.userDetails);
    // TODO: esi_code_for_patient_identity decommisioned, remove usage if it is not causing any issue
    if(userData)
    {
      data['config_identity_value'] = userData.config.esi_code_for_patient_identity;
      data['config_staff_identity'] = userData.config_replica.esi_code_for_staff_identity;
      data['config_staff_name'] = userData.config_replica.esi_code_for_staff_name;
      data['enable'] = userData.config.enable_progress_note_integration;
    }
    data['config_identity_value'] = patientIdValue!=0 ? patientIdValue :userData.config.esi_code_for_patient_identity;
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.updateMessageDetailsApi;
      this._http.post(apiURL,data,options)
      .toPromise()
      .then(
      res => {
        const result = res.text();
        resolve(result);
      });
    });
    return promise;
  }
  deleteMessageTags(data){
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.deleteMessageTagDataApi;
       this._http.post(apiURL,data,options)
        .toPromise()
        .then(
          res => {
          const result = res.json();
          resolve(result);
        }
        ).catch(err => {
          const error = err.json();
          reject(error);
        });
    });
    return promise;
  }
  saveMessageComment(data){
    let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.messageCommentDataApi;
       this._http.post(apiURL,data,options)
        .toPromise()
        .then(
          
        res => {
          const result = res.text();
          resolve(result);
        }
        );
    });
    return promise;
  }
    
}