
                        <!-- Filter dropdown as found in form-worklist component, will be rearranged once datatable loads its search elements -->
                        <label class="button-group custom-search dtMsgGroups__filter" hidden>
                            <button type="button" class="btn btn-default-outline btn-sm dropdown-toggle"
                                style="border-top-right-radius:0px;border-bottom-right-radius:0px;"
                                data-toggle="dropdown"><span class="fa fa-search"></span> <span
                                    class="caret"></span>
                            </button>
                            <ul class="dropdown-menu filter-worklist">
                                <li class="" *ngFor="let field of filteredFields;">
                                    <span (change)="filterCheckboxHandler(field)">
                                        <!-- the .form-check-input margin-left is overridden under datatable, so the label's margin-left to counter it is no longer needed -->
                                        <label style="margin-left: 0;">
                                            <input class="form-check-input" type="checkbox" id="dtMsgGroupsFilter__checkbox{{field.fieldId}}"
                                                value="{{field.fieldId}}" [checked]="field.enableAutoSelect">&nbsp;{{field.headerName}}
                                        </label>
                                    </span>
                                </li>
                            </ul>
                        </label>
