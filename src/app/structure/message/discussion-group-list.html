<style>
    table.existing-members td {
        padding-left: 0px!important;
        padding-right: 0px!important;
    }
</style>
<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Edit Patient Discussion Groups</strong>
        </span>
        <span class="pull-right">
            <button class="btn btn-sm btn-primary" (click)="goToList()">{{'BUTTONS.BACK' | translate}}</button>
        </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/message']">Message Center</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/patient-discussion-groups']">Patient Discussion Groups</a></li>
            <li class="breadcrumb-item">Edit Patient Discussion Groups </li>
        </ol>

    </div>
</section>
<section class="row chatroom-section">

    <div class="col-lg-12">
        <section class="card">
            <div class="card-header">
                <span class="cat__core__title"><!--*ngIf="editGroup||addGroup"-->
                     <strong>Edit Patient Discussion Group {{pdgName}}</strong>	<!--*ngIf="editGroup"-->	
                </span>
            </div>
            <div class="card-block">

                <form class="form-horizontal" [formGroup]="messageGroup" (ngSubmit)="onBlurMethodForId(selected.patientId,f)" #f="ngForm" autocomplete="off">
                    
                    <div [hidden]="!allowSave && !privilegeManageAllMessageGroup" class="form-group row">
                        <!--&& !addGroup -->
                        <label class="col-md-3 control-label">Make this Group Public <i class="make-public icmn-info" ></i></label>
                        <div class="col-md-3">

                            <div class="btn-group" style="margin-left:25px;">
                                <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': selectedisPublic}"
                                    (click)="togglePublic(true)">
                                  Yes
                            </button>
                                <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !selectedisPublic}"
                                    (click)="togglePublic(false)">
                                No
                            </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <label class="control-label">Allow multi thread PDG</label>
                            <i class="multi-chat-thread icmn-info" ></i>
                        </div>
                        <div class="col-md-3">
                            <div class="btn-group" data-toggle="buttons" style="margin-left:25px;">
                                <button aria-pressed="true" class="btn btn-outline-success btn-sm"  [ngClass]="{'active': selectedAllowMultiThread}" (click)="toggleMultiThreadOption(true)"> Yes </button>
                                <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !selectedAllowMultiThread}" (click)="toggleMultiThreadOption(false)" > No </button>
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">

                    </div>

                    <!-- <div class="form-group row" *ngIf="isNursingAgencyEnabled == 1">
                        <label *ngIf="userData.nursing_agencies == ''" class="col-md-3 control-label">User Tags
                        </label>                        
                        <div class="col-md-6" *ngIf="userData.nursing_agencies == ''">
                            <select name="nursingAgencyUserTag" formControlName="nursingAgencyUserTag"
                                id="nursing-agency-user-tags" class="form-control select2111">
                                <option value="">Select User Tag </option>
                                <option *ngFor="let tag of nursingAgencyTags" value="{{tag.id}}">
                                    {{tag.tag_name }}
                                </option>
                            </select>
                        </div>
                        <label *ngIf="userData.nursing_agencies != ''" class="col-md-3 control-label">User Tags *
                        </label>
                        <div class="col-md-6" *ngIf="userData.nursing_agencies != ''">
                            <select name="nursingAgencyUserTag" formControlName="nursingAgencyUserTag"
                                id="nursing-agency-user-tags" class="form-control select2111" required>
                                <option value="">Select User Tag </option>
                                <option *ngFor="let tag of nursingAgencyTags" value="{{tag.id}}">
                                    {{tag.tag_name }}
                                </option>
                            </select>
                            <div *ngIf="messageGroup.controls['nursingAgencyUserTag'].errors&&(messageGroup.controls.nursingAgencyUserTag?.dirty || messageGroup.controls.nursingAgencyUserTag?.touched)"
                                class="alert alert-danger">
                                Please select a user tag
                            </div>
                        </div>
                    </div> -->

                    <div class="row">
                        <div class="col-lg-4">
                            <br>

                            <section class="card existing">
                                <div class="card-header">
                                    <span class="cat__core__title">
                                    <strong>Existing Members</strong>
                                </span>
                                </div>
                                <div class="card-block">
                                    <div class="cat__apps__messaging__header">
                                        <input type="text"  id="existingMembers" class="form-control cat__apps__messaging__header__input" placeholder="Search..." #existingMembers autocomplete="off"/>
                                        <i class="icmn-search"></i>
                                        <button type="button"></button>
                                    </div>
                                    <div class="member-list">
                                    <table class="table table-hover nowrap existing-members" id="example1">

                                        <tbody>
                                            <tr *ngFor="let member of selectedGroupMembers | MessageGroupSearchFilter:existingMembers.value;">
                                                <td *ngIf="!selected.patientUser || member.id != selected.patientUser.id">{{member.displayName}}&nbsp;
                                                    <span *ngIf="member.status == '0'">(Inactive)</span>
                                                    <span *ngIf="member.status == '2'">(Pending)</span>
                                                    <span *ngIf="member.status == '8'">(Deleted)</span>&nbsp;<span *ngIf="member.naTagNames">({{member.naTagNames}})&nbsp;</span>
                                                    <br><span *ngIf="userData.tenantId!=member.tenantId">[{{member.tenantName}}]</span> </td>
                                                <td *ngIf="!selected.patientUser || member.id != selected.patientUser.id"> <a [hidden]="!canEdit" href="javascript: void(0);" (click)="removeMember(member.id);"><small><i class="icmn-cross"></i></small></a></td>
                                                <!--*ngIf="editGroup || addGroup"-->
                                            </tr>


                                        </tbody>
                                    </table>
                                    </div>
                                </div>
                                <div *ngIf="noMemberError" class="card-footer">
                                    <div class="alert alert-danger">Group members cannot be empty </div>
                                </div>
                            </section>
                            <section class="card existing role-list">
                                <div class="card-header role-header">
                                    <span class="cat__core__title">
                                        <strong>Existing Roles</strong>
                                    </span>
                                </div>
                                <div class="card-block">
                                    <div class="member-list">
                                        <table class="table table-hover nowrap" id="example1">
                                            <tbody *ngFor="let role of checkedRoleIds; let i = index"> 
                                                <tr>
                                                    <td class="width-80"> {{role.name}}&nbsp;<br><span *ngIf="userData.tenantId!=role.tenantId">[{{role.tenantName}}]</span></td>
                                                   <td> 
                                                        <a *ngIf= "role.defaultRole !== '1'" href="javascript: void(0);"  (click)="removeExistingRole(role.id);"><small><i class="icmn-cross"></i></small></a>
                                                        <small *ngIf= "role.defaultRole ===  '1'" class="disabled-pdg" title="{{ 'TOOLTIPS.DELETE_ROLES_PDG' | translate }}"><i class="icmn-cross"></i></small>
                                                    </td>
                                                    <td> <i class="fa fa-plus hand-pointer expand-icon-{{role.id}} expand-icon-color " (click)="callAccordion(role.id,i,'selectedRoles')" ></i> </td> 
                                                </tr>
                                                <tr>
                                                    <span *ngIf="(!usersInRole && clickedRole==role.id)" class="loader-message">{{ 'MESSAGES.NO_USER_FOUND' | translate }}</span>
                                                    <span *ngIf="(isStaffsloadingTime && clickedRole==role.id)" class="loader-message">{{ 'MESSAGES.LOADING_MSG' | translate }}</span>
                                                    <ul class="sub-item-panel sub-item-panel-{{role.id}}">
                                                        <li *ngFor="let user of role.userList" [hidden]="user.searchHideStatus">
                                                       <span style="line-height: 30px;padding-left:5px;">{{user.name}}<span *ngIf="user.naTagNames">({{user.naTagNames}})&nbsp;</span></span>
                                                        </li>
                                                    </ul>
                                                    
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </section>
                        </div>
                        <div class="col-lg-8 pull-right" id="staffListblock">
                            <br>

                            <section class="card">

                                <div class="card-header">
                                    <span class="cat__core__title">
                                    <strong>New Roles/Members</strong> 
                                    <input style="margin-top:18px;" [disabled]="!membersLoaded" [hidden]="!canEdit" type="button" class="btn btn-sm btn-primary pull-right" id="add_member" (click)="addMember();" name="" value="Add" />
                                </span>
                                </div>
                                <div class="card-block">                                  
                                    <div class="form-group row">
                                        <div class="chatwith-modal-tab chatwith-modal-tab-mar" style="margin-left: 55px;">
                                            <div class="chatwith-model-head col-md"  (click)="optionShow = 'groups'" [class.cat__apps__messaging__tab--selected]="optionShow=='groups'">PDGs</div>
                                            <div class="chatwith-model-head col-md"  (click)="showData('msggroups')" [class.cat__apps__messaging__tab--selected]="optionShow=='msggroups'">Message Groups</div>
                                            <div class="chatwith-model-head col-md"  (click)="showData('staffroles')" [class.cat__apps__messaging__tab--selected]="optionShow=='staffroles'"> Staff Roles </div>
                                            <div class="chatwith-model-head col-md"  (click)="showData('staffs')" [class.cat__apps__messaging__tab--selected]="optionShow=='staffs'" >Staff</div>
                                            <div class="chatwith-model-head col-md" (click)="showData('partnerroles')" [class.cat__apps__messaging__tab--selected]="optionShow=='partnerroles'"> Partner Roles </div>
                                            <div class="chatwith-model-head col-md" (click)="showData('partner')" [class.cat__apps__messaging__tab--selected]="optionShow=='partner'" >Partners</div>
                                            
                                        </div>
                                       
                                        <div class="row chat-with-wrapper-filter"   style="width: 100%;margin-top: 20px;" >
                                            <div class="col-sm-5" style="padding-left: 19px">
                                                <div class="row" *ngIf="((optionShow !='groups') && (optionShow !='msggroups') && chatWithTenantFilter.filterEnabled && chatWithTenantFilter.tenants.length  && !multiSiteEnable )" [hidden]="!hideSiteSelection">
                                                    <div class="site-label">
                                            <span>Site:&nbsp;</span>
                                            </div>
                                            <div style="width: 70%">
                                                <app-select-sites [events]="eventsSubject.asObservable()" [crossSite]=true (hideDropdown)="hideDropdown($event)" [hideApplyFilter]=true (siteIds)="getBranchIds($event)">
                                                </app-select-sites>
                                        </div>
                                          </div>
                                            </div>
                                            <!-- <div class="col-md-4 chat-with-wrapper-filter-tenant" 
                                                 *ngIf="((optionShow !='groups') && (optionShow !='msggroups') && chatWithTenantFilter.filterEnabled && chatWithTenantFilter.tenants.length)">
                                                        <div class="row">
                                                            <div class="col-md-5 chat-with-wrapper-filter-tenant-label">
                                                        <span>{{ 'LABELS.FILTER_BY_SITES' | translate }}</span>
                                                            </div>
                                                            <div class="col-md-9 chat-with-wrapper-filter-tenant-select">
                                                        <select class="form-control" data-placeholder="None Selected" id="messageTenantFilter"  #branchname   >
                                                                    <option *ngFor="let tenant of chatWithTenantFilter.tenants" value="{{tenant.id}}" [selected]="tenant.id == chatWithTenantFilter.selectedTenant"  > {{tenant.tenantName}} </option>
                                                                </select>
                                                            </div>
                                                        </div>
                                                    </div> -->
                                                 
                                                    <!-- Removed site filter display in roles listing,because of dynamic updation issue -->
                                                 <!-- <div class="col-sm-6" style="padding-left: 19px">
                                                        <div class="row">
                                                            <div class="site-label">
                                                    <span>Site(s):&nbsp;</span>
                                                    </div>
                                                    <div style="width: 70%">
                                                    <app-select-sites [filterType]=true (siteIds)="getSiteIds($event)" [singleSelection]="singleSelection"
                                                    [selectedSiteIds]="editedSite"  >
                                                    </app-select-sites>
                                                </div>
                                                  </div>
                                                    </div> -->

                                            <div class="chat-with-wrapper-search col-sm-7" [hidden]="optionShow === 'groups'" [ngClass]="{'filter-enabled': ((optionShow !='groups') && (optionShow !='msggroups') && chatWithTenantFilter.filterEnabled && chatWithTenantFilter.tenants.length)}">
                                            <div class="row">     
                                                <div class="col-sm-9">   
                                                <input type="text" [(ngModel)]="searchText" [hidden]="optionShow !='staffroles' && optionShow !='partnerroles'" [ngModelOptions]="{standalone: true}" class="search-width-grp form-control cat__apps__messaging__header__input" autocomplete="off"
                                            placeholder="Search Role(s)..." #newMembers (keyup)="searchOnKeyPress($event)" id="userSearchTxtRoles" />
                                            <div style="width: 92%;"  >       
                                            <input (keydown)="searchOnEnter($event,optionShow)" [hidden]="optionShow ==='staffroles' || optionShow ==='partnerroles'" class="form-control cat__apps__messaging__header__input groups-srch-width" id="chat-with-modal-search-box" placeholder="Search Here" #userSearch>
                                                </div>
                                            </div>
                                            <div class="col-sm-3"> 
                                                <button [hidden]="optionShow ==='staffroles' || optionShow ==='partnerroles'" [disabled]="this.srch.nativeElement.value.trim() == ''" type="button" class="btn btn-sm btn-primary srchBtn" title="Search" (click)="search(optionShow)">Search</button>
                                                <button [hidden]="optionShow ==='staffroles' || optionShow ==='partnerroles'" type="button" class="btn btn-sm btn-default resetBtn" title="Reset" (click)="reset(optionShow)">Reset</button>
                                            </div>
                                        </div>
                                            </div>
                                                </div>
                                               
                                            <div *ngIf="optionShow=='staffroles'"  class="col-md-12">                                              
                                            <div class="chat-with-empty-data" *ngIf="!(newMemberListByRoleWise).length && !chatWithLoader.otherTenantstaff" >No Staff Roles Available.<span  *ngIf="userSearch.value"><span style="color: #0088ff;cursor: pointer;" (click)="reset(optionShow)"> Click Here </span> to Reset the Search.</span></div>
                                            <div class="chat-with-empty-data" style="display:none" id="notFoundRoles" >No Staff Roles Available..</div>     
                                            <div *ngIf="chatWithLoader.otherTenantstaff" class="loading-container">
                                                <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                                <div class="loading-text">Loading Staff Roles...</div>
                                            </div>                                          
                                        </div>
                                        <div *ngIf="optionShow=='partnerroles'"  class="col-md-12">                                              
                                            <div class="chat-with-empty-data" *ngIf="!(newMemberListByRoleWise).length && !chatWithLoader.otherTenantstaff" >No Partner Roles Available.<span  *ngIf="userSearch.value"><span style="color: #0088ff;cursor: pointer;" (click)="reset(optionShow)"> Click Here </span> to Reset the Search.</span></div>
                                            <div class="chat-with-empty-data" style="display:none" id="notFoundRoles" >No Partner Roles Available..</div>     
                                            <div *ngIf="chatWithLoader.otherTenantstaff" class="loading-container">
                                                <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                                <div class="loading-text">Loading Partner Roles...</div>
                                            </div>                                          
                                        </div>
                                        <div *ngIf="optionShow=='staffs'"  class="col-md-12">
                                              
                                            <div class="chat-with-empty-data" *ngIf="!(userListChatwith).length && !chatWithLoader.staffs" >No Clinicians Available.<span  *ngIf="userSearch.value"><span style="color: #0088ff;cursor: pointer;" (click)="reset(optionShow)"> Click Here </span> to Reset the Search.</span></div>
                                               <div *ngIf="chatWithLoader.staffs" class="loading-container">
                                                <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                                <div class="loading-text">Loading Clinicians...</div>
                                            </div>                                            
                                            </div>
                                            <div *ngIf="optionShow=='partner'"  class="col-md-12">
                                              
                                                <div class="chat-with-empty-data" *ngIf="!(userListChatwith).length && !chatWithLoader.partner" >No partners Available.<span  *ngIf="userSearch.value"><span style="color: #0088ff;cursor: pointer;" (click)="reset(optionShow)"> Click Here </span> to Reset the Search.</span></div>
                                                   <div *ngIf="chatWithLoader.partner" class="loading-container">
                                                    <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                                    <div class="loading-text">Loading Partners...</div>
                                                </div>                                            
                                                </div>   
                                        <ul class="treeview treeview-section-ui"  style="width:100%;" *ngIf="optionShow ==='staffroles' && (newMemberListByRoleWise.length && !chatWithLoader.otherTenantstaff) ">
                                            <li *ngFor="let cliniciansRole of newMemberListByRoleWise ; let i = index" 
                                               class="role-{{cliniciansRole.roleData.id}} roleslisting">
                                                <i class="fa fa-plus expand-icon-{{cliniciansRole.roleData.id}}" (click)="callAccordion(cliniciansRole.roleData.id,i)" ></i>
                                                <input type="checkbox" name="staffRoleWise" id="role-{{cliniciansRole.roleData.id}}" value="{{cliniciansRole.roleData|json}}" (change)="checkboxChanged($event,i)">
                                                <label for="staffRoleWise" class="custom-unchecked">{{cliniciansRole.roleData.name }}</label>
                                                <span *ngIf="(usersInRole==false && clickedRole==cliniciansRole.roleData.id)" style="right: 10%;margin-left: 50%; color:#acb7bf;">No Users Found..</span>

                                                <ul class="sub-item-panel sub-item-panel-{{cliniciansRole.roleData.id}}">
                                                    <li class='fa fa-caret-right' style="display: list-item!important;" *ngFor="let cliniciansRoleUser of cliniciansRole.userList " [hidden]="cliniciansRoleUser.searchHideStatus">
                                                       <!--   <input type="checkbox" (change)="checkboxChanged($event)" name="cliniciansRoleUser[]" [attr.data-roleSet]="cliniciansRole.roleData.id" id="role-{{cliniciansRole.roleData.id}}-{{cliniciansRoleUser.id}}"
                                                            value="{{cliniciansRoleUser.id}}" class="cliniciansRoleUser" title="{{cliniciansRoleUser.name}}"> -->
                                                        <span class="ag-grid-cell" style="line-height: 30px;padding-left:5px;">{{cliniciansRoleUser.name}}&nbsp;<span *ngIf="cliniciansRoleUser.naTagNames">({{cliniciansRoleUser.naTagNames}})&nbsp;</span></span>
                                                    </li>
                                                </ul>
                                            </li>
                                            <!-- <li *ngIf="!(newMemberListByRoleWise .length && chatWithLoader.otherTenantstaff) ">No Users Available</li> -->
                                        </ul>
                                        <ul class="treeview treeview-section-ui"  style="width:100%;" *ngIf="optionShow ==='partnerroles' && (newMemberListByRoleWise.length && !chatWithLoader.otherTenantstaff) ">
                                            <li *ngFor="let cliniciansRole of newMemberListByRoleWise ; let i = index" 
                                               class="role-{{cliniciansRole.roleData.id}} roleslisting">
                                                <i class="fa fa-plus expand-icon-{{cliniciansRole.roleData.id}}" (click)="callAccordion(cliniciansRole.roleData.id,i)" ></i>
                                                <input type="checkbox" name="staffRoleWise" id="role-{{cliniciansRole.roleData.id}}" value="{{cliniciansRole.roleData|json}}" (change)="checkboxChanged($event,i)">
                                                <label for="staffRoleWise" class="custom-unchecked">{{cliniciansRole.roleData.name }}</label>
                                                <span *ngIf="(usersInRole==false && clickedRole==cliniciansRole.roleData.id)" style="right: 10%;margin-left: 50%; color:#acb7bf;">No Users Found..</span>

                                                <ul class="sub-item-panel sub-item-panel-{{cliniciansRole.roleData.id}}">
                                                    <li class='fa fa-caret-right' style="display: list-item!important;" *ngFor="let cliniciansRoleUser of cliniciansRole.userList " [hidden]="cliniciansRoleUser.searchHideStatus">
                                                       <!--   <input type="checkbox" (change)="checkboxChanged($event)" name="cliniciansRoleUser[]" [attr.data-roleSet]="cliniciansRole.roleData.id" id="role-{{cliniciansRole.roleData.id}}-{{cliniciansRoleUser.id}}"
                                                            value="{{cliniciansRoleUser.id}}" class="cliniciansRoleUser" title="{{cliniciansRoleUser.name}}"> -->
                                                        <span class="ag-grid-cell" style="line-height: 30px;padding-left:5px;">{{cliniciansRoleUser.name}}&nbsp;<span *ngIf="cliniciansRoleUser.naTagNames">({{cliniciansRoleUser.naTagNames}})&nbsp;</span></span>
                                                    </li>
                                                </ul>
                                            </li>
                                            <!-- <li *ngIf="!(newMemberListByRoleWise .length && chatWithLoader.otherTenantstaff) ">No Users Available</li> -->
                                        </ul>
                                        <ul class="treeview treeview-section-ui" style="width:100%;" *ngIf="optionShow ==='staffs'">
                                            <li *ngFor="let cliniciansRoleUser of userListChatwith " >
                                                
                                                        <input type="checkbox" (change)="checkboxChanged($event)" name="cliniciansRoleUser[]" [attr.data-roleSet]="cliniciansRoleUser.roleId" id="role-{{cliniciansRoleUser.roleId}}-{{cliniciansRoleUser.id}}"
                                                            value="{{cliniciansRoleUser.userid}}" class="cliniciansRoleUser" title="{{cliniciansRoleUser.displayname}}">
                                                        <label for="{{cliniciansRoleUser.displayname}}" class="custom-unchecked">{{cliniciansRoleUser.displayname}}&nbsp;<span *ngIf="cliniciansRoleUser.naTagNames">({{cliniciansRoleUser.naTagNames}})</span></label>
                                                    </li>
                                                
                                            <!-- <li *ngIf="!((userListChatwith ).length && chatWithLoader.staffs)">No Users Available</li> -->
                                        </ul>
                                        <ul class="treeview treeview-section-ui" style="width:100%;" *ngIf="optionShow ==='partner'">
                                            <li *ngFor="let cliniciansRoleUser of userListChatwith " >
                                                
                                                        <input type="checkbox" (change)="checkboxChanged($event)" name="cliniciansRoleUser[]" [attr.data-roleSet]="cliniciansRoleUser.roleId" id="role-{{cliniciansRoleUser.roleId}}-{{cliniciansRoleUser.id}}"
                                                            value="{{cliniciansRoleUser.userid}}" class="cliniciansRoleUser" title="{{cliniciansRoleUser.displayname}}">
                                                        <label for="{{cliniciansRoleUser.displayname}}" class="custom-unchecked">{{cliniciansRoleUser.displayname}}&nbsp;<span *ngIf="cliniciansRoleUser.naTagNames">({{cliniciansRoleUser.naTagNames}})</span></label>
                                                    </li>
                                                
                                            <!-- <li *ngIf="!((userListChatwith ).length && chatWithLoader.staffs)">No Users Available</li> -->
                                        </ul>
                                        <ul class="w-100" *ngIf="optionShow ==='groups'">
                                            <li>
                                                <app-pdg-members (selectedItems)="setSelectedPDGs($event)" [siteIds]="editedSite" [reset]="resetPdgMembers"></app-pdg-members>
                                            </li>
                                        </ul>
                                        <ul class="treeview treeview-section-ui" style="width:100%;" *ngIf="optionShow ==='msggroups'">
                                            <li *ngFor="let cliniciansGroup of memberDataGroupWise"
                                                class="role-{{cliniciansGroup.groupId}}">
                                                <i class="fa fa-plus expand-icon-{{cliniciansGroup.groupId}}" (click)="callAccordion(cliniciansGroup.groupId)"></i>
                                                <input type="checkbox" name="messageGroup" id="role-{{cliniciansGroup.groupId}}" value="{{cliniciansGroup.groupId}}" (change)="checkboxChanged($event)">
                                                <label for="middle" class="custom-unchecked">{{cliniciansGroup.groupName }}</label>
                                                <span *ngIf="(isGrouploadingTime==true && clickedGroup==cliniciansGroup.groupId)" style="right: 10%;margin-left: 67%; color:#acb7bf;">Loading...</span>
                                                <ng-template [ngTemplateOutlet]="membersLists" [ngTemplateOutletContext]="{cliniciansGroup: cliniciansGroup, group: 'msg'}"></ng-template>
                                                <ng-template [ngTemplateOutlet]="roleLists" [ngTemplateOutletContext]="{cliniciansGroup: cliniciansGroup, group: 'msg'}"></ng-template>
                                            </li>                                           
                                        </ul>
                                        <div *ngIf="optionShow === 'msggroups'"  class="col-md-12">
                                            <div *ngIf="!(memberDataGroupWise).length  && !MessageGroupWithLoader.groups" class="row"><span>{{'MESSAGES.NO_MSG_GRP_AVAILABLE' | translate}}</span> <span  *ngIf="srch.nativeElement.value.trim()"><a href="javascript:void(0);" class="click-here" (click)="reset(optionShow)"> {{'MESSAGES.CLICK_HERE' | translate}} </a> {{'MESSAGES.TO_RESET_SEARCH' | translate}}</span></div>
    
                                                <div *ngIf="MessageGroupWithLoader.groups && optionShow !== 'groups'" class="loading-container">
                                                    <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                                    <div class="loading-text">Loading Groups...</div>
                                                </div>    
    
                                        </div>
                                        <div *ngIf="optionShow === 'msggroups'"  class="col-md-12">
                                            
                                            <div *ngIf="optionShow === 'msggroups'" style="text-align: center;width:100%;margin-top: 14px;float: left;">
                                            <button [disabled]="MessageGroupWithLoader.groups" *ngIf="!MessageGroupWithLoader.groups" type="button" [hidden]="hideLoadMore" class="btn btn-sm btn-default" (click)="loadMoreGroups(optionShow)">
                                                    <span *ngIf="!MessageGroupWithLoader.groups">Load More</span>                                                 
                                            </button>
                                            </div>

                                    </div>                                       
                                    <div *ngIf="optionShow=='staffs'"  class="col-md-12">      
                                        <div style="text-align: center;width:100%;margin-top: 14px;float: left;">
                                            <button type="button" [hidden]="!( optionShow =='staffs' && !noMoreItemsAvailable.users && userListChatwith.length)" class="btn btn-sm btn-default" (click)="loadMoreUsers('staffs',loadMoreSearchValue, true)">{{loadMoremessage.users}}</button>
                                        </div>
                                        </div>
                                        <div *ngIf="optionShow=='partner'"  class="col-md-12">      
                                            <div style="text-align: center;width:100%;margin-top: 14px;float: left;">
                                                <button type="button" [hidden]="!( optionShow =='partner' && !noMoreItemsAvailable.users && userListChatwith.length)" class="btn btn-sm btn-default" (click)="loadMoreUsers('partner',loadMoreSearchValue, true)">{{loadMoremessage.users}}</button>
                                            </div>
                                            </div>
                                    </div>
                                </div>
                                <div *ngIf="addMemberError" class="card-footer">
                                    <div class="alert alert-danger">Please select member </div>
                                </div>
                            </section>
                            <!-- <input type="button" class="btn btn-sm btn-primary" (click)="addMember();" name="" value="Add"/> -->

                        </div>
                    </div>

                    <div class="form-actions">
                       <button type="submit"   [hidden]="!allowSave && !privilegeManageAllMessageGroup" id="submit_disccn_gp" class="btn btn-primary" [disabled]="disableButton || !canEdit">{{'BUTTONS.UPDATE' | translate}}</button>
                        <a [routerLink]="['/message/patient-discussion-groups']"  id="cancle_disccn_gp" class="btn btn-default">{{'BUTTONS.CANCEL' | translate}}</a>
                    </div>

                </form>

            </div>
        </section>

    </div>
</section>
<!-- END: tables/datatables -->
<ng-template #membersLists let-cliniciansGroup="cliniciansGroup" let-group="group">
  <ul class="sub-item-panel sub-item-panel-{{ group === 'pdg' ? cliniciansGroup.patientId : cliniciansGroup.groupId }}">
    <caption *ngIf="cliniciansGroup.userList && cliniciansGroup.userList.length">
      {{
        'TITLES.MEMBERS' | translate
      }}
    </caption>
    <li *ngFor="let member of cliniciansGroup.userList">
      <span class="sub-item"
        >{{ member.displayName }}
        <span *ngIf="member.naTagNames && member.naTagNames !== ''">({{ member.naTagNames }}) </span>
        <span *ngIf="+userData.tenantId !== +member.tenantId">[{{ member.tenantName }}]</span>
      </span>
    </li>
  </ul>
</ng-template>
<ng-template #roleLists let-cliniciansGroup="cliniciansGroup" let-group="group">
  <ul class="sub-item-panel sub-item-panel-{{ group === 'pdg' ? cliniciansGroup.patientId : cliniciansGroup.groupId }}">
    <caption *ngIf="cliniciansGroup.selectedRoles && cliniciansGroup.selectedRoles.length">
      {{
        'TITLES.ROLES' | translate
      }}
    </caption>
    <li *ngFor="let role of cliniciansGroup.selectedRoles">
      <span class="sub-item">{{ role.name }}&nbsp;</span>
    </li>
  </ul>
</ng-template>
