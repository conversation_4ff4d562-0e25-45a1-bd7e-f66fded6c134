import { Component, OnInit, ElementRef, Renderer } from '@angular/core';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, FormControl, FormArray } from '@angular/forms';
import { TagService } from 'app/services/tag/tag.service';
import { isBlank } from 'app/utils/utils';
import { CONSTANTS, TagType } from 'app/constants/constants';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { SharedService } from '../shared/sharedServices';
import { StoreService, Store } from '../shared/storeService';

let activeMessageTag = null;
declare const $: any;
@Component({
  selector: 'app-message-tags',
  templateUrl: './tag-definitions.html'
})
export class TagDefinitionsComponent implements OnInit {
  messageTag = [];
  tagList = [];
  dTable;
  userData;
  filingCenters=[];
  editRowId: any;
  showClose=false;
  showClosetag=false;
  tagDefenitionAdd: FormGroup;
  tagDefenitionEdit: FormGroup;
  userInfo;
  tagDeleted;
  activeMessageTag = null;
  tenantId;
  folderLists;
  dataLoadingMsg=true;
  type = 1;
  editCellPrevValue;
  showAddErrorMessage = false;
  showEditErrorMessage = false;
  folderName;
  isFilingCenter=false;
  isLinux=false;
  isWindows=false;
  tagtypeList;
  approvalRequired;
  outcomeMeasures;
  crossTenantChangeSubscriber:any;
  constructor(
    private router: Router,
    private _formBuild: FormBuilder,
    public _structureService: StructureService,
    private _ToolTipService: ToolTipService,
    private storeService: StoreService,
    public _SharedService: SharedService,
    private tagService: TagService,
    elementRef: ElementRef,
    renderer: Renderer
  ) {
    this.userInfo = this._structureService.loginUserDetails;    
    this.tenantId = this.userInfo.tenantId;
    this.tagDefenitionAdd = new FormGroup({
      tagName: new FormControl(null, Validators.required),
      filingcenter: new FormControl(null),
      fileSaveFormat: new FormControl('{tagname}-{FirstName}-{LastName}-{DOB}-{createdOn}-{signedOn}'),
    });

    renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
      var targetElement;
      if(event.target.id == 'editMsgTag') {
        console.log(this.activeMessageTag);
        this.editCell();
      }
      else if(event.target.id == 'deleteMsgTag'){
        this.removeTag();
      }
      else if(event.target.id == 'editMsgTagOnly'){
       // alert('ok');
       $('.update'+activeMessageTag.id).show();
       $('.cancel'+activeMessageTag.id).show();
       $('.delete'+activeMessageTag.id).hide();
       $('.edit'+activeMessageTag.id).hide();
       $('#editinp'+activeMessageTag.id).show();
       $('#lbl'+activeMessageTag.id).hide();
      }
      else if(event.target.id == "cancelMsgTag"){
        $('.update'+activeMessageTag.id).hide();
        $('.cancel'+activeMessageTag.id).hide();
        $('.delete'+activeMessageTag.id).show();
        $('.edit'+activeMessageTag.id).show();
        $('#editinp'+activeMessageTag.id).hide();
        $('#lbl'+activeMessageTag.id).show();
      }
      else if(event.target.id == "updateMsgTagOnly"){
        this.updateMessageTag($('#editinp'+activeMessageTag.id).val());
       // alert($('#editinp'+activeMessageTag.id).val());
      }      
    });
    this.crossTenantChangeSubscriber = this._SharedService.crossTenantChange.subscribe((onInboxData) => {
      if(this.router.url.indexOf('/message/tag-definitions') > -1) {
        this.ngOnInit();
      }
    });

  }
  ngOnInit() {
     this._structureService.getMessageTagtype().then(( data ) => {
      if(data['getSessionTenant']['messageTagTypes']) {
          this.tagtypeList=data['getSessionTenant']['messageTagTypes'];
          console.log("data===================");
          console.log(data);
          console.log("data===================");
      }
       $('#tagtype').select2({
            allowClear: true,
            placeholder: 'Select Tag Type',
            data:this.tagtypeList
        });
     });

    /*let deviceInfo = this.deviceService.getDeviceInfo();
    console.log(deviceInfo.os);
    if(deviceInfo.os=="linux"){
      this.isLinux = true;
    }
    else if(deviceInfo.os=="windows"){
      this.isWindows = true;
    }*/
    this.userData = this._structureService.getUserdata();  
    if(this.userData.config.enable_filing_center=="1") {
      console.log('##########################################################');
        this.isFilingCenter= true;
    }else{
      console.log('$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$$');
      this.isFilingCenter= false;
      this.tagDefenitionAdd.removeControl('filingcenter');
      this.tagDefenitionAdd.removeControl('fileSaveFormat');
    }
    var page = 'message-tag-definitions';
    $(".message-tag").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSGRP00001') });
    $(".default-filing-center").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSGRP00002') });
    $(".default-filename").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSGRP00003') });
    $(".download-connect").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSGRP00004') });
    $(".default-tagtype").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSGRP00005') });
    $(".default-outcomeMeasure").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSGRP00007') });
    $(".default-approvalRequired").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSGRP00008') });
    $('.default-messagecategorycode').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00009') });
		$('.default-messagetypeid').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00009') });
    $('.selectd').select2({
      placeholder: "Loading Filing Center..."
    });  
    $('#tagtype').attr("placeholder","Select Tag Type"); 
    $('#tagtype').select2({
      placeholder: "Select Tag Type"
    });   
    $(".selectd").attr("disabled", true);
    /*this.tagDefenitionAdd = this._formBuild.group({
      name: ['', [Validators.required,this.noWhitespaceValidator]],
      filingcenter: ['', [Validators.required,this.noWhitespaceValidator]]
    });*/
   

  $('#tagtype').on(
      'change',
      (e) => {
        console.log($('#tagtype').val());
        console.log("XXXXXXXXXXXXXXXXXXXXXXXXXXXXXX")
        /* console.log(e);
        var m = $(e.target).val();
        console.log(m); */
      //  $('.select2-selection__rendered').removeAttr('title');
     
      
        this.showClosetag=true;
      
        
      }
    );

    $('#sfilingCenterss').on(
      'change',
      (e) => {
        console.log($('#sfilingCenterss').val());
        /* console.log(e);
        var m = $(e.target).val();
        console.log(m); */
      //  $('.select2-selection__rendered').removeAttr('title');
      setTimeout(()=>{
        $('#select2-sfilingCenterss-container').removeAttr('title');
      },1)
      
        this.showClose=true;
        this.folderName = {folderName:$('#sfilingCenterss').val(),type:'OUTGOING'}        
        console.log(this.folderName);
        
      }
    );
    
    
    this.getMessageTagList();
    if(this.isFilingCenter){
    this.getFolderLists();
    }
  }
  ngOnDestroy() {
    if(this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
  }
  clearFilingCenter(){
    this.showClose=false;
    this.folderName ='';
    $('#sfilingCenterss').val('');
    $('#sfilingCenterss').select2({
      allowClear: false,
      placeholder: 'Select Filing Center',
      data:this.filingCenters
  });
}

cleartagtype(){
   $("#tagtype").val('');
    this.showClosetag=false;
    $('#tagtype').select2({
      allowClear: false,
      placeholder: 'Select Tag Type',
      data:this.tagtypeList
  });
}
  getMessageTagList() {
    this.tagDefenitionEdit = this._formBuild.group({
      tagNames: this._formBuild.array([
        //this.initTags()
      ])
    });
    this._structureService.getMessageTagList(this.type).then(( data ) => {
      if(data['getSessionTenant']) {
          this.tagList=[];
          this.tagList = data['getSessionTenant']['messageTags'];
          console.log(this.tagList);
          this.dataLoadingMsg = false;
          var isTrue = false;    
          if(this.tagList.length > 99){
            isTrue = true;
          }
          const tagControl = <FormArray>this.tagDefenitionEdit.controls['tagNames'];                     
          this.loadDataSignedDocs();  
      }else{
          this._structureService.deleteCookie('authenticationToken');
           this.router.navigate(['/login']);
      }
    });
    
  }

  
  noWhitespaceValidator(control: FormControl) {
    let isWhitespace = (control.value || '').trim().length === 0;
    let isValid = !isWhitespace;
    return isValid ? null : { 'whitespace': true }
 }
  editCell() {
    /*console.log(cellId,value);
    this.editCellPrevValue= value;
    this.editRowId = cellId;*/
   // if(this.isFilingCenter){
      this.router.navigate(['message/edit-tag/'+activeMessageTag.id]);
   // }
    
    
  }
  cancelUpdate(i) {
     
     this.editRowId = '';
     this.showEditErrorMessage=false;
     this.getMessageTagList();
  }
 /* populateMsgtagsOnly(){
    
     
        if (this.dTable) {
           this.dTable.destroy();
         }
         var isTrue = false;    
         if(this.tagList.length > 99){
           isTrue = true;
         }
       this.dTable = $('#dtMessageTagsOnly').DataTable({           
         autoWidth: false,
         "order": [[ 1, "asc" ]],
         responsive: true,
         //bDeferRender:true,
         //processing: true,
         // oLanguage: {
         // sLoadingRecords: "Please wait - loading..."
         // },
         retrieve: true,
         //pagination: true,
         serching: true,  
         paging: isTrue,
         bInfo: isTrue,
         lengthMenu: [[100, 250, 500, 1000, -1], [100, 250, 500, 1000, 'All']],            
         data: this.tagList,
         fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
           $(nRow).on('click', () => {           
             activeMessageTag = aData;
           });
         },
         'fnCreatedRow': function (nRow, aData, iDataIndex) {
          $(nRow).attr('id', 'my' + iDataIndex); // or whatever you choose to set as the id
      },
         columns: [
           {title: "#"},
           {title: "Message Tags", data: 'tagName'},           
           {title: "Actions"}
         ],
         columnDefs: [
         {
           data: null,
           orderable: false,
           width: "5%",
           targets: 0
          },
          { 
            data: null,
            targets: 1,
            width: "25%",
            render: function (document, type, row) {
              let actions = '';                
              actions +=`
             <label id="lbl${row.id}"> ${document} </label>
              <input type="text" name="abc" id="editinp${row.id}" class="hidden-cl" value="${document}" >
              `
              return actions;
            }
         },
         {
             data: null,
             orderable: false,
             render: function (document, type, row) {
             let actions = '';                
             actions +=`
             <a id="editMsgTagOnly" href="javascript: void(0);" class="delete${row.id} cat__core__link--underlined mr-3"><i id="editMsgTagOnly" class="icmn-pencil"></i> Edit</a>
             <a id="deleteMsgTag" href="javascript: void(0);" class="edit${row.id} cat__core__link--underlined mr-3"><i id="deleteMsgTag" class="icmn-cross"></i> Delete</a>
             <a id="updateMsgTagOnly" href="javascript: void(0);" class="update${row.id} hidden-cl cat__core__link--underlined mr-3"><i class="icmn-checkmark"></i> Update</a>
             <a id="cancelMsgTag" href="javascript: void(0);" class="update${row.id} hidden-cl cat__core__link--underlined mr-3"><i class="icmn-cross"></i> Cancel</a>
             `
             return actions;
           },
           width: "15%",
           targets: 2
         }]
       });
    
       this.dTable.on( 'order.dt search.dt', () => {
         this.dTable.column(0, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
             cell.innerHTML = i+1;
         });
       }).draw();
       
       this.dataLoadingMsg=false;
    }*/

  loadDataSignedDocs(){


    if (this.dTable) {
       this.dTable.destroy();
     }else{
      if(this._structureService.previousUrlNow.indexOf("/message/add-message-tag")==-1 && this._structureService.previousUrlNow.indexOf("/message/edit-tag/")==-1){
        console.log("loadDataSignedDocs : this._structureService.previousUrlNow Enter condition")
        this._structureService.resetDataTable();
      }
      
     }
     var isTrue = false;    
     if(this.tagList.length > 99){
       isTrue = true;
     }
   this.dTable = $('#dtMessageTags').DataTable({           
     autoWidth: false,
     "order": [[ 1, "asc" ]],
     responsive: true,
     retrieve: true,
     serching: true, 
     stateSave: true, 
     paging: isTrue,
     bInfo: isTrue,
     lengthMenu: [[100, 250, 500, 1000, -1], [100, 250, 500, 1000, 'All']],            
     data: this.tagList,
     fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
       $(nRow).off().on('click', () => {
         console.log(aData)
         activeMessageTag = aData;
       });
     },
     columns: [
       {title: "#"},
       {title: "Message Tags", data: 'tagName'},
       {title: "Default Outgoing Filing Center", data: 'tagMeta'},
       {title: "Default Filename Format", data: 'tagMeta'},
       {title: "Message Tag Type", data: 'type'},
       {title: "Approval Required", data: 'tagMeta'},
       {title: "Part Of Outcome Measure", data: 'tagMeta'},
       {title: "Patient Association", data: 'tagMeta'},
       {title: "Actions"}
     ],
     columnDefs: [
     {
       data: null,
       orderable: false,
       width: "5%",
       targets: 0
      },
      { 
        data: null,
        targets: 1,
        width: "10%",        
     },
     { 
       data: null,
       targets: 2,
       width: "20%",
       visible:this.isFilingCenter,
       render:function(document,type,row){
        // console.log(document);
        /* let tg='';
         for(let i=0;i<document.length;i++){
           if(document[i] && document[i].tagName && document[i].tagName!=""){                    
               tg+=document[i].tagName+', ';
           }
         }
         return tg.replace(/,\s*$/, "");*/
       
         let parsejson={integrationFC:'',fileSaveFormatIntegration:''};
         if(document && document!=null && document!='' && document!='null'){
           parsejson = JSON.parse(document);               
         }
         if(parsejson.integrationFC){
           return parsejson.integrationFC;
         }else{
          return "";
         }
       }
     },
     { 
       data: null,
       targets: 3,
       width: "15%",
       visible:this.isFilingCenter,
       render:function(document,type,row){             
        let parsejson1={integrationFC:'',fileSaveFormatIntegration:''};
        if(document && document!=null && document!='' && document!='null'){
          parsejson1 = JSON.parse(document);               
        }
        if(parsejson1.fileSaveFormatIntegration){
          return parsejson1.fileSaveFormatIntegration;
        }else{
         return "";
        }
       }
     },
      { 
        data: null,
        targets: 4,
        width: "5%",
               render:function(document,type,row){
        if(document.typeName){
          return document.typeName;
        }
       
          return "";
         
       }
     },
      { 
        data: null,
        targets: 5,
        width: "5%",
        render:function(document,type,row){
        let parsejson1={outgoingFilingCenter:'',fileSaveFormat:'',approvalRequired:''};
       if(document && document!=null && document!='' && document!='null'){
          parsejson1 = JSON.parse(document);               
        }
        if(parsejson1.approvalRequired){
          //return parsejson1.approvalRequired;
          return '<span class="badge badge-success mr-2 mb-2">Yes</span>';
        }else{
         return '<span class="badge badge-danger mr-2 mb-2">No</span>';
        }
         
       }
     },
     { 
        data: null,
        targets: 6,
        width: "5%",
        visible:false,
        render:function(document,type,row){
        let parsejson1={outgoingFilingCenter:'',fileSaveFormat:'',summarizeOutcomeMeasure:''};
       if(document && document!=null && document!='' && document!='null'){
          parsejson1 = JSON.parse(document);               
        }
        if(parsejson1.summarizeOutcomeMeasure){
          //return parsejson1.summarizeOutcomeMeasure;
         return '<span class="badge badge-success mr-2 mb-2">Yes</span>';
        }else{
         return '<span class="badge badge-danger mr-2 mb-2">No</span>';
        }
         
       }
     },
     { 
      data: null,
      targets: 7,
      width: "5%",
      render:function(document,type,row){
        let docParsed:any={};
        if(document && document!=null && document!='' && document!='null'){
          docParsed= JSON.parse(document);               
        }
        let actionsColor = 'badge-success';
        let actionText = 'Yes'  
        if(docParsed.patientFacing==false || docParsed.patientFacing== undefined){
          actionsColor = 'badge-danger';
          actionText = 'No' 
        }
        let actions = '<span class="badge '+actionsColor +' mr-2 mb-2">'+actionText+'</span>';                  
        return actions;
      }
    },
     {
         data: null,
         orderable: false,
         render: function (document, type, row) {
         let actions = '';                
         actions +=`
         <a id="editMsgTag" href="javascript: void(0);" class="cat__core__link--underlined mr-3"><i id="editMsgTag" class="icmn-pencil"></i> Edit</a>
         <a id="deleteMsgTag" href="javascript: void(0);" class="cat__core__link--underlined mr-3"><i id="deleteMsgTag" class="icmn-cross"></i> Delete</a>
         `
         return actions;
       },
       width: "15%",
       targets: 8
     }]
   });
   if(this.storeService.getStoredData(Store.SEARCH_MESSAGE_TAG_LIST) && !isBlank(this.storeService.getStoredData(Store.SEARCH_MESSAGE_TAG_LIST))) {
    this.dTable.search(this.storeService.getStoredData(Store.SEARCH_MESSAGE_TAG_LIST)).draw();
    this._structureService.notifySearchFilterApplied(true);
   }
   
   this.dTable.on( 'search.dt', () => {
    const searchValue = this.dTable.search(); 
    this.storeService.storeData(Store.SEARCH_MESSAGE_TAG_LIST, searchValue);
    if(isBlank(searchValue)) {
      this.storeService.removeData(Store.SEARCH_MESSAGE_TAG_LIST);
      this._structureService.notifySearchFilterApplied(false);
    }
   }).draw();
   this.dTable.on( 'order.dt search.dt', () => {
    if(this.dTable.column(0, {search:'applied', order:'applied'}).nodes() && this.dTable.column(0, {search:'applied', order:'applied'}).nodes().length) {
      this.dTable.column(0, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
          cell.innerHTML = i+1;
      });
    }
   }).draw();
   this.dataLoadingMsg=false;
}

  removeTag() {
    this.tagService.removeTag({ tagId: activeMessageTag.id, tagType: TagType.messageTag, tagName: activeMessageTag.tagName }).then((success) => {
      if (success) {
        this.tagList = this.tagList.filter(obj => obj.id !== activeMessageTag.id);
        this.loadDataSignedDocs();
      }
    });
  }
  initTags() {
    return this._formBuild.group({
      name: ['', [Validators.required,this.noWhitespaceValidator]],
      id: ['', Validators.required]
    });
  }
  initTagAdd() {
    return this._formBuild.group({
      name: ['', [Validators.required,this.noWhitespaceValidator]]
    });
  }
  updateMessageTag(tagValue) {
      let tagExists;
      tagExists = this.tagList.filter((row)=>{
        if(row.tagName.toLowerCase()==tagValue.trim().toLowerCase()){
          return true;
        }
      })
      if(tagExists.length>0)
        {
          var notify = $.notify('Message Tag already exists');
          setTimeout(function() {
              notify.update({'type': 'warning', 'message': '<strong>Message Tag already exists</strong>'});
          }, 1000);
          return false;
        }

      
      const id = Number(activeMessageTag.id);
      this.tenantId = typeof (this.tenantId) === 'undefined' ? this._structureService.getCookie('tenantId') : this.tenantId;
      const params = {
        name: tagValue,
        type: Number(this.type),
        tenantId: Number(this.tenantId)
      };
      this._structureService.updateMessageTag(id, params).subscribe(
        (data) => {
           this.tagDefenitionEdit.reset();
        var notify = $.notify('Success! Message Tag updated');
          setTimeout(function() {
              notify.update({'type': 'success', 'message': '<strong>Success! Message Tag updated</strong>'});
              $("#cancelUpdate").show();
          }, 1000);
          this.getMessageTagList();
          this.editRowId = '';
        }
      );
   // })
  }
  getFolderLists()
  {
    
    this._structureService.getTenantFilingCenterFolders('OUTGOING').then(      
        (data) => {
          console.log(data);
         if (data['getTenantFilingCenterFolders'] && data['getTenantFilingCenterFolders'].length) {
            this.folderLists = JSON.parse(JSON.stringify(data['getTenantFilingCenterFolders']));  
           // this.folderLists.unshift({folderName:"Select Filing Center",type:"OUTGOING"});
            $(".selectd").attr("disabled", false);
            $('.selectd').select2({
              placeholder: "Select Filing Center"
           });          
            this.folderName ='';

            this.filingCenters = [];
            console.log(this.folderName);
            console.log(this.folderLists);

           for(let i=0;i<this.folderLists.length;i++){
             var fname = this.folderLists[i].folderName;
             var ftype = this.folderLists[i].type;
             var id = '{"folderName":"'+fname+'","type":"'+ftype+'"}';
              var item = {id:fname,text:fname}
              this.filingCenters.push(item);
           }
          
            /* var AJAX_OPTIONS = [
              { id: '1', text: 'Choice 1' },
              { id: '2', text: 'Choice 2' },
              { id: '3', text: 'Choice 3' },
              { id: '4', text: 'Choice 4' },
              { id: '5', text: 'Choice 5' }
          ]; */
          $('#sfilingCenterss').select2({
            allowClear: true,
            placeholder: 'Select Filing Center',
            data:this.filingCenters
        });

         }
         else{
          $('.selectd').select2({
            placeholder: "No Filing Centers Available"
          });  
          $(".selectd").attr("disabled", true);
           console.log("No folders");
         }
    });
  }
  addMessageTag(form) {
    $('.addmessagetag').attr( "disabled", "disabled" );
    console.log(form.valid);
     var tagtype="";
     tagtype= $("#tagtype").val();
    console.log("=======tagtype=======");
    console.log(tagtype);
    if(!form.valid){    
    // var notify = $.notify('Please Enter the Message Tag Details');
    //     setTimeout(function() {
    //         notify.update({'type': 'warning', 'message': '<strong>Please Enter the Message Tag Details</strong>'});
    //     }, 1000);
         $('.addmessagetag').removeAttr("disabled");
  }
  
 
    let tagExists;
    if(form.value.tagName.trim()!='') {
    this.tenantId = typeof (this.tenantId) === 'undefined' ? this._structureService.getCookie('tenantId') : this.tenantId;
    let formFolderName = '';
    let formFileSaveFormat = '';
    tagExists = this.tagList.filter((row)=>{
      if(row.tagName.toLowerCase()==form.value.tagName.trim().toLowerCase()){
        return true;
      }
    })
    if(tagExists.length>0)
      {
        var notify = $.notify('Message Tag already exists');
        setTimeout(function() {
            notify.update({'type': 'warning', 'message': '<strong>Message Tag already exists</strong>'});
        }, 1000);
         $('.addmessagetag').removeAttr("disabled");
        return false;
      }
    if(!form.valid) {
      var notify = $.notify('Please Enter the Message Tag Details');
        setTimeout(function() {
            notify.update({'type': 'warning', 'message': '<strong>Please Enter the Message Tag Details</strong>'});
        }, 1000);
         $('.addmessagetag').removeAttr("disabled");
        return false;
    }
    console.log(this.isFilingCenter);
    if(this.isFilingCenter){
      console.log(this.folderName);
      if(!this.folderName){
        formFolderName='';
      }else{
        formFolderName = this.folderName.folderName;
      }
    }
    if(this.isFilingCenter){
      console.log(form.value.fileSaveFormat);
      formFileSaveFormat = form.value.fileSaveFormat;
    }
     let tagMeta = {outgoingFilingCenter:formFolderName,fileSaveFormat:formFileSaveFormat,summarizeOutcomeMeasure: this.outcomeMeasures,approvalRequired:this.approvalRequired};
    console.log(tagMeta);
    var saveTag = { "id":'',"name": form.value.tagName, "type": Number(this.type), "tenantId": Number(this.tenantId), "tagMeta": tagMeta,"typeId":Number(tagtype) };    
    this._structureService.saveMessageTag(saveTag).then((result) => {
      console.log(result);
     var notify = $.notify('Success! Message Tag created');
     setTimeout(function() {
         notify.update({'type': 'success', 'message': '<strong>Success! Message Tag created</strong>'});
     }, 1000);
     $('.addmessagetag').removeAttr("disabled");
     $('#hideReset').trigger('click');
     this.showClose=false;
     this.folderName ='';
     this.clearFilingCenter();
     this.tagDefenitionAdd = new FormGroup({
      tagName: new FormControl(null, Validators.required),
      filingcenter: new FormControl(),
      fileSaveFormat: new FormControl('{tagname}-{FirstName}-{LastName}-{DOB}-{createdOn}-{signedOn}'),
    });
    $('.selectd').select2({
      placeholder: "Select Filing Center"
   });  
   this.folderName = this.folderLists[0];
      this.getMessageTagList();
      
    }).catch((ex) => {
       //this.loginFailed = true;
    });
    }
}
hideAddError(){
   this.showAddErrorMessage=false;
     
}
 togglePreference(preference, value) {
    if (preference === 'outcomeMeasures') {
      console.log(value);
         this.outcomeMeasures=value;
      this.tagDefenitionAdd.patchValue({
        outcomeMeasures: value
      });
    }
       if (preference === 'approvalRequired') {
         console.log(value);
      
         this.approvalRequired=value
      this.tagDefenitionAdd.patchValue({
        approvalRequired: value
      });
    }
 }
showAddError(){
  this.showAddErrorMessage=true;
}
showEditError(){
  this.showEditErrorMessage=true;
}
  showSiteAccessMessage(): void {
    this._structureService.notifyMessage({
      messge: this._ToolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG'),
      type: CONSTANTS.notificationTypes.error,
      delay: 3000
    });
  }
}

