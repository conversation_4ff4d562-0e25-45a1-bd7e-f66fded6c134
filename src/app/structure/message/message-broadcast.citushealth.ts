import { Component, OnInit, EventEmitter } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import {
  FormsModule,
  FormBuilder,
  FormGroup,
  Validators,
  FormControl,
  FormArray,
  ReactiveFormsModule,
} from '@angular/forms';
import { SignService } from '../signatures/sign.service';
import { StructureService } from '../structure.service';
import { pluralizeFilterPipe } from './broadcast-message.pipes';
import { ToolTipService } from '../tool-tip.service';
import { MessageBroadcastService } from './message-broadcast.service';
import {
  UploadOutput,
  UploadInput,
  UploadFile,
  humanizeBytes,
} from 'ngx-uploader';
import { SharedService } from './../shared/sharedServices';
import * as io from 'socket.io-client';
import { Subject } from 'rxjs';
import { CONSTANTS, MessagePriority } from 'app/constants/constants';

declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;
declare var swal: any;
declare var NProgress: any;
@Component({
  selector: 'app-message-broadcast',
  templateUrl: './message-broadcast.html',
})
export class MessageBroadCastComponent implements OnInit {
  messageBroacast: FormGroup;
  userRoles: any;
  messageText: any;
  teanntRoles: any;
  userTags: any;
  tananntRolesAndTags: any;
  attachedFile: any;
  selectedOptions;
  selectedFileNames = [];
  member;
  taggedUsers = [];
  privileges = this._structureService.getCookie('userPrivileges');

  config: any;
  userDetails: any;
  configData: any = {};
  userData: any = {};
  cmisFileUploadData = [];
  filesAttached = {
    pdf: 0,
    document: 0,
    image: 0,
    audio: 0,
    video: 0,
  };
  /*
  uploadFile: any;
  hasBaseDropZoneOver: boolean = false;
  sizeLimit = 2000000;
  */
  //
  formData: FormData;
  files: UploadFile[];
  uploadInput: EventEmitter<UploadInput>;
  humanizeBytes: Function;
  dragOver: boolean;
  uploadFlag: boolean;
  broadcastData: any;
  pushNotifyMsg = '';
  selectedUserRoles = [];
  selectedUserTags = [];
  crossTenantChangeSubscriber: any;
  caregiverOrAlternateArgs: String = 'Alternate Contact';
  siteId: any;
  hideSiteSelection: boolean;
  eventsSubject: Subject<void> = new Subject<void>();
  prioritySelected = MessagePriority.NORMAL;
  
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private _formBuild: FormBuilder,
    private _signService: SignService,
    private _structureService: StructureService,
    private _ToolTipService: ToolTipService,
    private _messageBroadcastService: MessageBroadcastService,
    public _SharedService: SharedService
  ) {
    this.config = this._structureService.userDataConfig;
    this.userDetails = this._structureService.userDetails;
    this.configData = JSON.parse(this.config);
    this.userData = JSON.parse(this.userDetails);
    this.files = []; // local uploading files array
    this.uploadInput = new EventEmitter<UploadInput>(); // input events, we use this to emit data to ngx-uploader
    this.humanizeBytes = humanizeBytes;
    this.crossTenantChangeSubscriber =
      this._SharedService.crossTenantChange.subscribe((onInboxData) => {
        if (this.router.url.indexOf('/message/message-broadcast') > -1) {
          this.ngOnInit();
        }
      });
  }
  ngOnInit() {
    this.config = this._structureService.userDataConfig;
    this.userDetails = this._structureService.userDetails;
    this.configData = JSON.parse(this.config);
    this.userData = JSON.parse(this.userDetails);
    $('#taggedUser').on(
      'change',
      (e) => (this.selectedOptions = $(e.target).val())
    );
    if (
      this.userData &&
      this.userData.config &&
      this.userData.config.default_patients_workflow &&
      this.userData.config.default_patients_workflow == 'alternate_contacts'
    )
      this.caregiverOrAlternateArgs = 'Caregiver';
    //console.log("selectedOptions: ", this.selectedOptions);
    let tananntRolesAndTags = [];
    this._messageBroadcastService
      .getAllTenantRolesForActiveUser()
      .then((data: any) => {
        this.teanntRoles = Object.assign([], data);
        tananntRolesAndTags = data;
        this._messageBroadcastService
          .getActiveUserTags()
          .then((data: any) => {
            this.userTags = Object.assign([], data);
            for (var dataItem in data) {
              tananntRolesAndTags.unshift(data[dataItem]);
            }
            this.tananntRolesAndTags = tananntRolesAndTags;
          })
          .catch((ex) => {
            //this.loginFailed = true;
            this.tananntRolesAndTags = tananntRolesAndTags;
          });
      })
      .catch((ex) => {
        //this.loginFailed = true;
        this._messageBroadcastService
          .getActiveUserTags()
          .then((data: any) => {
            this.userTags = data;
            for (var dataItem in data) {
              tananntRolesAndTags.unshift(data[dataItem]);
            }
            this.tananntRolesAndTags = tananntRolesAndTags;
          })
          .catch((ex) => {
            //this.loginFailed = true;
            this.tananntRolesAndTags = tananntRolesAndTags;
          });
      });
    this.messageBroacast = this._formBuild.group({
      userRoles: ['', Validators.required],
      messageText: ['', Validators.required],
      attachedFile: [''],
    });

    $('.dropify').dropify({
      messages: {
        default:
          'Drag and drop a file here or click,<br>multiple file upload enabled.',
        replace: 'Drag and drop or click to add',
        remove: 'Remove',
        error: 'Ooops, something wrong happend.',
      },
      error: {
        fileSize: 'The file size is too big ({{ value }} max).',
        minWidth: 'The image width is too small ({{ value }}}px min).',
        maxWidth: 'The image width is too big ({{ value }}}px max).',
        minHeight: 'The image height is too small ({{ value }}}px min).',
        maxHeight: 'The image height is too big ({{ value }}px max).',
        imageFormat: 'The image format is not allowed ({{ value }} only).',
      },
    });

    $('.select2-tags').select2({
      placeholder: 'Select Recipients',
      // tags: true,
      //tokenSeparators: [',', ' ']
    });

    $('.select2-tags').on('select2:open', function (event) {
      $('span.select2-container--open').attr('id', 'multi_select');
    });

    // var $rec_select = $('select.select2-tags');

    var page = 'messages-broadcast-message';
    $('.view-tooltip').tooltip({
      title: this._ToolTipService.getToolTip(page, 'MSGBC00001'),
    });
    $('.site-tooltip').tooltip({
      title: this._ToolTipService.getToolTip(page, 'MSGBC00002'),
    });
  }
  ngOnDestroy() {
    if (this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
  }
  metaDataSet(uploadedFiles, callBack) {
    let metaData = {};
    for (var i = 0; i < uploadedFiles.length; i++) {
      metaData[i] = {
        attributes: {
          data: [
            {
              userId: this.userData.userCmisId,
              actualUserId: this.userData.userId,
              owner: this.userData.userCmisId,
              userType: 'owner',
              objectType: 'file',
              fileType: uploadedFiles[i].format,
              displayName: uploadedFiles[i].details.name,
              parentFolderId: 'nil',
              isDeleted: false,
              createdOn: new Date().getTime(),
              modifiedOn: new Date().getTime(),
              chatRoomId: 0,
              chatType: 'broadcast_message',
              sourceType: 'attachment',
              fileOriginalName: uploadedFiles[i].name,
              broadcastMessage: this.messageBroacast.value['messageText']
                ? this.messageBroacast.value['messageText']
                : '',
            },
          ],
        },
      };
      if (i == uploadedFiles.length - 1) {
        callBack(metaData);
      }
    }
  }
  /* file upload starts*/
  onUploadOutput(output: UploadOutput): void {
    console.log('Output: ', output);
    let self = this;
    if (output.type === 'allAddedToQueue') {
      // when all files added in queue
      // uncomment this if you want to auto upload files when added
      // const event: UploadInput = {
      //   type: 'uploadAll',
      //   url: 'http://192.168.1.90/call-bell-hybrid-communication/cometchat/citus-health/v4/chatfile-upload.php',
      //   method: 'POST',
      //   headers: {
      //     'Authentication-Token': this._structureService.getCookie('authenticationToken'),
      //   },
      //   data: { user: '835' },
      //   concurrency: 0
      // };
      // console.log("Event:::: ",event);
      // this.uploadInput.emit(event);
    } else if (
      output.type === 'addedToQueue' &&
      typeof output.file !== 'undefined'
    ) {
      // add file to array when added
      if (output.file.size > 20971520) {
        //if( output.file.size > 1048576 ){
        $.notify(
          {
            message:
              'Sorry for the inconvenience! ' +
              output.file.name +
              ' file size more than 20 mb not supported.',
          },
          {
            type: 'danger',
          },
          {
            delay: 5000,
          }
        );
      } else if (!this.isValidFile(output.file.name)) {
        $.notify(
          {
            message:
              'Sorry for the inconvenience! ' +
              output.file.name +
              ' files types are not supported.',
          },
          {
            type: 'danger',
          },
          {
            delay: 5000,
          }
        );
      } else if (this.files.length >= 10) {
        $.notify(
          {
            message:
              "Sorry for the inconvenience! More than 10 files can't be uploaded at a time.",
          },
          {
            type: 'danger',
          },
          {
            delay: 5000,
          }
        );
      } else {
        this.files.push(output.file);
      }
    } else if (
      output.type === 'uploading' &&
      typeof output.file !== 'undefined'
    ) {
      // update current data in files array for uploading file
      const index = this.files.findIndex(
        (file) =>
          typeof output.file !== 'undefined' && file.id === output.file.id
      );
      this.files[index] = output.file;
    } else if (output.type === 'removed') {
      // remove file from array when removed
      this.files = this.files.filter(
        (file: UploadFile) => file !== output.file
      );

      console.log('this.files.length :', this.files, this.files.length);
      if (this.files.length == 0) {
        $('.dropify-clear').trigger('click');
        this.removeAllFiles();
      }
    } else if (output.type === 'dragOver') {
      this.dragOver = true;
    } else if (output.type === 'dragOut') {
      this.dragOver = false;
    } else if (output.type === 'drop') {
      this.dragOver = false;
    }

    if (output.type === 'done') {
      console.log('Done : ', output.file);
      if (
        output.file.response.msg &&
        output.file.response.view &&
        output.file.type
      ) {
        var imgdata = {
          id: output.file.fileIndex,
          details: output.file,
          name: output.file.response.msg,
          format: output.file.response.view,
          size: output.file.size,
          type: output.file.type,
        };
        this.selectedFileNames.push(imgdata);
        console.log('selectedFileNames: ', this.selectedFileNames);
        //console.log(this.files.length,'-----',this.selectedFileNames.length);
        if (this.files.length == this.selectedFileNames.length) {
          this.uploadFlag = true;
          //console.log(" **xxxxx*** ",this.pushNotifyMsg);
          this.metaDataSet(this.selectedFileNames, function (data) {
            let metaData = JSON.stringify(data);
            let fileUniqueName = 'multipleupload';
            self._structureService
              .uploadFileToCmis(fileUniqueName, metaData)
              .subscribe((filedata) => {
                let cmisFileData: any = filedata;
                cmisFileData = JSON.parse(
                  cmisFileData.data.uploadFileToCmis.cmisFileData
                );
                self.sendBroadCast(self.pushNotifyMsg, cmisFileData);
              });
          });
          //this.sendBroadCast(this.pushNotifyMsg);
        } else {
          //NProgress.done();
        }
      } else {
        // $.notify({
        //   message: "Some error occured, please try again."
        // },{
        //   type: 'danger'
        // },{
        //   delay: 5000
        // });
        NProgress.done();
        this.resetBroadCastForm('');
        swal('Sent Failed!', 'Some error occured, please try again.', 'error');
      }
    }
  }

  startUpload(): void {
    const event: UploadInput = {
      type: 'uploadAll',
      url:
        this._structureService.apiBaseUrl +
        'citus-health/v4/chatfile-upload.php',
      //url: 'http://192.168.1.90/call-bell-hybrid-communication/cometchat/citus-health/v4/chatfile-upload.php',
      method: 'POST',
      headers: {
        'Authentication-Token': this._structureService.getCookie(
          'authenticationToken'
        ),
      },
      data: { user: this._structureService.getCookie('userId') },
    };
    this.uploadInput.emit(event);
    console.log('Check this : ', this.files, this.uploadInput, event);
  }
  sendBroadCastFinish(filesAttachedFailed, messageText, rolesName) {
    var self = this;
    if (this.cmisFileUploadData) {
      this.broadcastData.cmisFileUploadData = this.cmisFileUploadData;
    }
    this._messageBroadcastService
      .sendBroadcastMessage(this.broadcastData)
      .then((response: any) => {
        let users = response.data;
        if (response.success) {
          var size = 0,
            key,
            configurationNotEnabled = false;
          if (
            users &&
            users.length &&
            'configurationNotEnabled' in users[0] &&
            users[0]['configurationNotEnabled'] == 1
          ) {
            configurationNotEnabled = true;
          } else {
            for (key in users) {
              if (users.hasOwnProperty(key)) size++;
            }
          }
          if (size > 0 && !configurationNotEnabled) {
            //console.log(users.length);
            self._structureService.socket.emit('userPollingtoServer', users);
            var args = {
              type: 'messages',
              setCount: 2,
              chatroomId: users[0].chatroom_id,
              message: self.broadcastData.message,
              sentTime: (new Date().getTime() / 1000).toString(),
              selfUpdate: true,
            };
            self._SharedService.messagePollingSelfUpdate.emit({
              data: 'Polling:true',
              args: args,
            });
            //console.log(" ***** ",pushNotifyMsg);
            var pushMessage = '';
            for (let key in this.filesAttached) {
              if (this.filesAttached[key] > 0) {
                if (this.filesAttached[key] == 1) {
                  switch (key) {
                    case 'pdf':
                      pushMessage = pushMessage + 'Shared a pdf';
                      break;
                    case 'document':
                      pushMessage = pushMessage + ', Shared a document';
                      break;
                    case 'image':
                      pushMessage = pushMessage + ', Shared an image';
                      break;
                    case 'audio':
                      pushMessage = pushMessage + ', Shared an audio';
                      break;
                    case 'video':
                      pushMessage = pushMessage + ', Shared a video';
                      break;
                  }
                } else {
                  switch (key) {
                    case 'pdf':
                      pushMessage = pushMessage + 'Shared pdfs';
                      break;
                    case 'document':
                      pushMessage = pushMessage + ', Shared documents';
                      break;
                    case 'image':
                      pushMessage = pushMessage + ', Shared images';
                      break;
                    case 'audio':
                      pushMessage = pushMessage + ', Shared audios';
                      break;
                    case 'video':
                      pushMessage = pushMessage + ', Shared videos';
                      break;
                  }
                }
              }
            }

            if (messageText) {
              pushMessage = messageText + '. ' + pushMessage;
            } else {
              pushMessage = pushMessage;
            }
            const messageSendNotificationData = {
              sourceId: CONSTANTS.notificationSource.message,
              sourceCategoryId: CONSTANTS.notificationSourceCategory.messageSendNotification
            };
            self._structureService.sentPushNotification(
              users,
              0,
              'You have a new message to review',
              '',
              '',
              '',
              '',
              undefined,
              undefined,
              this.prioritySelected,
              undefined,
              messageSendNotificationData
            );
            this.prioritySelected = MessagePriority.NORMAL; // reset priority back to normal
            var activityLogMessageBroadcast =
              self.userData.displayName +
              ' send broadcast message to ' +
              rolesName;
            var activityData = {
              activityName: 'Message Broadcast',
              activityType: 'messaging',
              activityDescription: activityLogMessageBroadcast,
            };
            this._structureService.trackActivity(activityData);

            var msg = 'Message Broadcast Sent Successfully.';
            if (filesAttachedFailed != '') {
              msg =
                msg + 'File upload failed for file(s) ' + filesAttachedFailed;
            }
            NProgress.done();
            swal('Sent Successfully!', msg, 'success');
            this.resetBroadCastForm('');
          } else {
            NProgress.done();
            if (configurationNotEnabled) {
              this._structureService.notifyMessage({
                messge:
                  'Warning!  This feature is not enabled for your tenant. Please contact support for more information',
                delay: 1000,
                type: 'warning',
              });
              this.router.navigate([this.userData.defaultPage || '/profile']);
            } else {
              swal(
                'Message Sending Failed!',
                'There are no recipient exits in the selected role.',
                'warning'
              );
            }
          }
        } else if (response.error === 'Invalid Input') {
          let activityData = {
            activityName: 'Error Check Send Broadcast Messages',
            activityType: 'sendBroadcastMessages',
            activityDescription: `Error occured while sending broadcast messsages due to invalid input data.`,
          };
          this._structureService.notifyMessage({
            messge: this._ToolTipService.getTranslateData(
              'MESSAGES.ERROR_MSG_INVALID_INPUT'
            ),
            delay: 1000,
            type: 'warning',
          });
          this._structureService.trackActivity(activityData);
        } else {
          // NProgress.done();
          let activityData = {
            activityName: 'Broadcast Message',
            activityType: 'communication',
            activityDescription: `Broadcast message sending failed due to ${response.status.message}`,
          };
          this._structureService.notifyMessage({
            messge: this._ToolTipService.getTranslateData(
              'MESSAGES.MSG_SENDING_FAILED'
            ),
            delay: 1000,
            type: 'danger',
          });
          this._structureService.trackActivity(activityData);
        }
      })
      .catch((ex) => {
        NProgress.done();
        this.resetBroadCastForm('');
      });
  }

  sendBroadCast(pushNotifyMsg, cmisFileData) {
    var rolesName = '';
    var self = this;
    this.teanntRoles.forEach((t_res) => {
      this.selectedUserRoles.forEach((s_res) => {
        if (t_res.id == s_res.id) {
          if (rolesName) {
            rolesName = rolesName + ', ' + t_res.name;
          } else {
            rolesName = t_res.name;
          }
        }
      });
    });
    this.userTags.forEach((tag) => {
      this.selectedUserTags.forEach((res_tag) => {
        if (tag.id == res_tag.id) {
          if (rolesName) {
            rolesName = rolesName + ', ' + tag.tag_name;
          } else {
            rolesName = tag.tag_name;
          }
        }
      });
    });
    var messageText = this.messageBroacast.value['messageText'];
    var imgPath = '';
    let filesAttachedFailed = '';
    var count = 0;
    if (cmisFileData) {
      for (var key in cmisFileData) {
        let FileData = JSON.parse(cmisFileData[key]);
        if (FileData.status == 200) {
          this.cmisFileUploadData.push(FileData.results);
          if (FileData.results.organizationId) {
            let type = FileData.results.attributes.data[0].fileType;
            if (type == 'image') {
              this.filesAttached.image = this.filesAttached.image + 1;
              var fileUrl =
                this.userData.cmisFileBaseUrl +
                FileData.results.attachment[0].attributes.fileshareId +
                '.json?type=thumbnail';
            } else if (type == 'document' || type == 'pdf') {
              if (type == 'document') {
                this.filesAttached.document = this.filesAttached.document + 1;
              } else {
                this.filesAttached.pdf = this.filesAttached.pdf + 1;
              }
              var fileUrl =
                this.userData.cmisFileBaseUrl +
                FileData.results.attachment[0].attributes.fileshareId +
                '.json?type=pdf';
            } else {
              if (type == 'video') {
                this.filesAttached.video = this.filesAttached.video + 1;
              } else {
                this.filesAttached.audio = this.filesAttached.audio + 1;
              }
              var fileUrl =
                this.userData.cmisFileBaseUrl +
                FileData.results.attachment[0].attributes.fileshareId +
                '.json';
            }           
            const fileName =  FileData.results.attributes.data[0].displayName;
            this._messageBroadcastService.fileFormatTypeTofileTagSendBroadcast(
              this.selectedFileNames[key].details,
              fileUrl,
              fileName,
              this.selectedFileNames[key].format,
              FileData.results.attachment[0].attributes.mimeType,
              function (result, filedata, type, file) {
                count = count + 1;
                self.broadcastData.attachmentStr += result.message;
                
                if (self.selectedFileNames.length == count) {
                  self.sendBroadCastFinish(
                    filesAttachedFailed,
                    messageText,
                    rolesName
                  );
                }
              }
            );
          }
        } else {
          count = count + 1;
          if (filesAttachedFailed == '') {
            filesAttachedFailed = FileData.name;
          } else {
            filesAttachedFailed = filesAttachedFailed + ', ' + FileData.name;
          }
          if (self.selectedFileNames.length == count) {
            self.sendBroadCastFinish(
              filesAttachedFailed,
              messageText,
              rolesName
            );
          }
        }
      }
    } else {
      self.sendBroadCastFinish(filesAttachedFailed, messageText, rolesName);
    }
  }

  sendBroadCast1(pushNotifyMsg) {
    var rolesName = '';
    this.teanntRoles.forEach((t_res) => {
      this.selectedUserRoles.forEach((s_res) => {
        if (t_res.id == s_res) {
          if (rolesName) {
            rolesName = rolesName + ', ' + t_res.name;
          } else {
            rolesName = t_res.name;
          }
        }
      });
    });
    console.log('rolesName:::::->', rolesName);
    var messageText = this.messageBroacast.value['messageText'];
    var imgPath = '';
    for (var i = 0; i < this.selectedFileNames.length; i++) {
      var fileUrl =
        this._structureService.apiBaseUrl +
        'writable/filetransfer/uploads/' +
        this._structureService.getCookie('userId') +
        '/' +
        this.selectedFileNames[i].format +
        '/' +
        this.selectedFileNames[i].name;
      //var fileUrl = "http://192.168.1.90/call-bell-hybrid-communication/cometchat/writable/filetransfer/uploads/" + this._structureService.getCookie('userId') + "/" + this.selectedFileNames[i].format + "/" + this.selectedFileNames[i].name;
      console.log(fileUrl, this.selectedFileNames[i]);

      var resp = this._messageBroadcastService.fileFormatTypeTofileTag(
        this.selectedFileNames[i].details,
        fileUrl,
        this.selectedFileNames[i].name,
        this.selectedFileNames[i].format,
        this.selectedFileNames[i].type
      );
      console.log('Resp:', resp[0].message);
      imgPath = resp[0].message;
      this.broadcastData.message =
        this.broadcastData.message + '<br><br>' + imgPath;
    }
    // console.log('bmess:', this.broadcastData.message);
    // console.log('B L:', this.files.length);
    var self = this;
    this._messageBroadcastService
      .sendBroadcastMessage(this.broadcastData)
      .then((users: any) => {
        var size = 0,
          key,
          configurationNotEnabled = false;
        if (
          users &&
          users.length &&
          'configurationNotEnabled' in users[0] &&
          users[0]['configurationNotEnabled'] == 1
        ) {
          configurationNotEnabled = true;
        } else {
          for (key in users) {
            if (users.hasOwnProperty(key)) size++;
          }
        }

        if (size > 0 && !configurationNotEnabled) {
          //console.log(users.length);
          self._structureService.socket.emit('userPollingtoServer', users);

          //console.log(" ***** ",pushNotifyMsg);
          var pushMsg = 'You have a new message to review';
          /*if(messageText){
          pushMsg = messageText+". "+pushNotifyMsg;  
        } else {
          pushMsg = pushNotifyMsg; 
        }*/
          console.log('PN----------:', pushMsg, ' ***** ', this.pushNotifyMsg);
          // $.notify({
          //   message: pushMsg
          // },{
          //   type: 'info'
          // },{
          //   delay: 5000
          // });

          var deeplinking = {
            //"pushType": config.pushDeepLinkCategories.chat + '' + targetID,
            state: 'eventmenu.group-chat',
            stateParams: {
              targetID: users[0].chatroom_id,
              targetName: 'group-chat',
            },
            activeMessage: {
              sent: new Date().getTime() / 1000,
              messageType: 1,
              baseId: 0,
              userid: self.userData.userid,
              fromName: '"' + self.userData.displayName + '"',
              message_group_id: 0,
              createdby: self.userData.userid,
            },
            priorityId: this.prioritySelected
          };
          const messageSendNotificationData = {
            sourceId: CONSTANTS.notificationSource.message,
            sourceCategoryId: CONSTANTS.notificationSourceCategory.messageSendNotification
          };
          self._structureService.sentPushNotification(
            users,
            0,
            pushMsg,
            '',
            deeplinking,
            '',
            '',
            undefined,
            undefined,
            this.prioritySelected,
            undefined,
            messageSendNotificationData
          );

          var activityLogMessageBroadcast =
            self.userData.displayName +
            ' send broadcast message to' +
            rolesName;
          var activityData = {
            activityName: 'Message Broadcast',
            activityType: 'messaging',
            activityDescription: activityLogMessageBroadcast,
          };
          this._structureService.trackActivity(activityData);

          swal(
            'Sent Successfully!',
            'Message Broadcast Sent Successfully.',
            'success'
          );
          this.resetBroadCastForm('');
        } else {
          if (configurationNotEnabled) {
            this._structureService.notifyMessage({
              messge:
                'Warning!  This feature is not enabled for your tenant. Please contact support for more information',
              delay: 1000,
              type: 'warning',
            });
            this.router.navigate([this.userData.defaultPage || '/profile']);
          } else {
            swal(
              'Message Sending Failed!',
              'There are no recipient exits in the selected role.',
              'warning'
            );
          }
        }
      })
      .catch((ex) => {
        this.resetBroadCastForm('');
      });
  }
  cancelUpload(id: string): void {
    this.uploadInput.emit({ type: 'cancel', id: id });
  }
  isValidFile(filename) {
    var ext = filename.split('.').pop().toLowerCase();
    var allowedExt = [
      'gif',
      'png',
      'jpg',
      'jpeg',
      'jpe',
      'mpeg',
      'bmp',
      'mpg',
      'mpe',
      'avi',
      'mp4',
      'movie',
      'qt',
      'mov',
      'pdf',
      'mp3',
      'mp2',
      'doc',
      'docx',
      'word',
      'xl',
      'xls',
      'xlsx',
      'aac',
      'amr',
    ];
    if (allowedExt.indexOf(ext) >= 0) {
      console.log('S-ext-', ext);
      this.pushNotificationMessage(ext);
      return true;
    } else {
      console.log('E-ext-', ext);
      return false;
    }
  }

  resetBroadCastForm(data) {
    this.emitEventToSelectSites();
    if (data) {
      var selectedOptionsLength = 0;
      if (typeof this.selectedOptions == 'object') {
        selectedOptionsLength = this.selectedOptions.length;
        console.log('Form Reset: Obj ', this.selectedOptions.length);
      }
      var self = this;
      if (
        (this.selectedOptions && selectedOptionsLength > 0) ||
        this.messageBroacast.value['messageText'] ||
        this.files.length > 0
      ) {
        swal(
          {
            title: 'Do you want to leave this page?',
            text: 'Changes you made may not be saved.',
            type: 'warning',
            showCancelButton: true,
            cancelButtonClass: 'btn-default',
            confirmButtonClass: 'btn-warning',
            confirmButtonText: 'Ok',
            closeOnConfirm: true,
          },
          function (isConfirm) {
            if (isConfirm) {
              self.resetBroadCastForm('');
            }
          }
        );
      } else {
        self.resetBroadCastForm('');
      }
    } else {
      this.files = [];
      this.selectedFileNames = [];
      this.removeAllFiles();
      console.log('A L:', this.files.length);
      $('#taggedUser').val(null).trigger('change');
      var drEvent = $('.dropify').dropify();
      drEvent.on('dropify.beforeClear', function (event, element) {
        return true;
      });
      $('.dropify-clear').trigger('click');
      this.messageBroacast = this._formBuild.group({
        userRoles: ['', Validators.required],
        messageText: ['', Validators.required],
        attachedFile: [''],
      });
      this.pushNotifyMsg = '';
      NProgress.done();
    }
    this.filesAttached = {
      pdf: 0,
      document: 0,
      image: 0,
      audio: 0,
      video: 0,
    };
    this.cmisFileUploadData = [];
  }
  removeFile(id: string): void {
    this.uploadInput.emit({ type: 'remove', id: id });
  }

  removeAllFiles(): void {
    this.uploadInput.emit({ type: 'removeAll' });
  }
  /* file upload ends*/
  sendBroadCastMessage() {
    console.log('------>' + navigator.onLine);
    console.log('------>' + this._structureService.socket.connected);
    if (navigator.onLine && this._structureService.socket.connected) {
      var selected = [];
      var tagsSelected = [];
      if (this.selectedOptions) {
        this.selectedOptions.forEach((element) => {
          this.member = { id: '' };
          var id = element.substr(element.indexOf(':') + 1);
          id = id.replace(/'/g, '');
          id = id.replace(/\s/g, '');
          if (id.indexOf('tag-') != -1) {
            this.member.id = id.replace('tag-', '');
          } else {
            this.member.id = id;
          }
          if (this.taggedUsers.indexOf(id.toString()) == -1) {
            if (id.indexOf('tag-') != -1) {
              tagsSelected.push({ id: id.replace('tag-', '') });
            } else {
              this.taggedUsers.push({ id: id });
              selected.push({ id: id });
            }
          }
        });
      }
      console.log('selected: ', selected);

      if (
        (selected.length > 0 || tagsSelected.length > 0) &&
        (this.files.length > 0 || this.messageBroacast.value['messageText'])
      ) {
        this.selectedUserRoles = selected;
        this.selectedUserTags = tagsSelected;
        this.broadcastData = {
          message: this.messageBroacast.value['messageText'],
          selectedRoles: selected,
          selectedTags: tagsSelected,
          siteIds: this.siteId.toString(),
          attachmentStr: '',
          priorityId: this.prioritySelected
        };
        var self = this;
        let confirmMesssage = '';
        let patientInfoMessage = this.checkForPatient();
        if (this.messageBroacast.value['messageText']) {
          confirmMesssage = `${this._ToolTipService.getTranslateData('MESSAGES.GOING_TO_SEND_MSG')}. ${patientInfoMessage}`;
        } else {
          confirmMesssage =
            'Sending an attachment without an associated message is not a good idea. Would you like to send the attachment without a message that accompanies it? '+ patientInfoMessage;
        }

        swal(
          {
            title: 'Are you sure?',
            text: confirmMesssage,
            type: 'warning',
            showCancelButton: true,
            cancelButtonClass: 'btn-default',
            confirmButtonClass: 'btn-warning',
            confirmButtonText: 'Ok',
            closeOnConfirm: true,
          },

          function (isConfirm) {
            if (isConfirm) {
              if (self.files.length > 0) {
                NProgress.start();
                self.startUpload();
              } else {
                NProgress.start();
                self.sendBroadCast('', null);
              }
            } else {
              //swal("Cancelled", "Your imaginary file is safe :)", "error");
            }
          }

          // $('body').append(x[0]);
        );

        var x = document.getElementsByClassName('btn-warning');
        if (x != undefined) {
          x[0].setAttribute('id', 'unique_cnfrm_btn');
        }

        var y = document.getElementsByClassName('btn-default');
        if (y != undefined) {
          y[0].setAttribute('id', 'unique_cancel_btn');
        }
      } else if (selected.length < 1 && tagsSelected.length < 1) {
        $.notify(
          {
            message: 'Please choose recipient(s)',
          },
          {
            type: 'danger',
          },
          {
            delay: 5000,
          }
        );
      } else if (
        this.files.length < 1 &&
        !this.messageBroacast.value['messageText']
      ) {
        $.notify(
          {
            message: 'Enter message or attach atleast one file',
          },
          {
            type: 'danger',
          },
          {
            delay: 5000,
          }
        );
      }
    } else {
      this._structureService.notifyMessage({
        messge: 'Your Connection Lost. Trying to Reconnect..',
        delay: 3000,
        type: 'warning',
      });
    }
  }
  pushNotificationMessage(ext) {
    var oneFile;
    var manyFile;
    console.log('push-> ', this.pushNotifyMsg, 'EXE', ext);
    var allowedExtImg = [
      'gif',
      'png',
      'jpg',
      'jpeg',
      'jpe',
      'mpeg',
      'bmp',
      'mpg',
      'mpe',
    ];
    var allowedExtVid = ['avi', 'mp4', 'movie', 'qt', 'mov'];
    var allowedExtPdf = ['pdf'];
    var allowedExtAud = ['mp3', 'mp2', 'aac', 'amr'];
    var allowedExtDoc = ['doc', 'docx', 'word', 'xl', 'xls', 'xlsx'];

    if (allowedExtImg.indexOf(ext) >= 0) {
      oneFile = 'Shared an image';
      manyFile = 'Shared images';
      if (this.pushNotifyMsg.indexOf(oneFile) >= 0) {
        this.pushNotifyMsg = this.pushNotifyMsg.replace(oneFile, manyFile);
      } else {
        if (this.pushNotifyMsg) {
          this.pushNotifyMsg = this.pushNotifyMsg + ', ' + oneFile;
        } else {
          this.pushNotifyMsg = oneFile;
        }
      }
    }
    if (allowedExtVid.indexOf(ext) >= 0) {
      oneFile = 'Shared a video';
      manyFile = 'Shared videos';
      if (this.pushNotifyMsg.indexOf(oneFile) >= 0) {
        this.pushNotifyMsg = this.pushNotifyMsg.replace(oneFile, manyFile);
      } else {
        if (this.pushNotifyMsg) {
          this.pushNotifyMsg = this.pushNotifyMsg + ', ' + oneFile;
        } else {
          this.pushNotifyMsg = oneFile;
        }
      }
    }
    if (allowedExtPdf.indexOf(ext) >= 0) {
      oneFile = 'Shared a pdf';
      manyFile = 'Shared pdfs';
      if (this.pushNotifyMsg.indexOf(oneFile) >= 0) {
        this.pushNotifyMsg = this.pushNotifyMsg.replace(oneFile, manyFile);
      } else {
        if (this.pushNotifyMsg) {
          this.pushNotifyMsg = this.pushNotifyMsg + ', ' + oneFile;
        } else {
          this.pushNotifyMsg = oneFile;
        }
      }
    }
    if (allowedExtAud.indexOf(ext) >= 0) {
      oneFile = 'Shared an audio';
      manyFile = 'Shared audios';
      if (this.pushNotifyMsg.indexOf(oneFile) >= 0) {
        this.pushNotifyMsg = this.pushNotifyMsg.replace(oneFile, manyFile);
      } else {
        if (this.pushNotifyMsg) {
          this.pushNotifyMsg = this.pushNotifyMsg + ', ' + oneFile;
        } else {
          this.pushNotifyMsg = oneFile;
        }
      }
    }
    if (allowedExtDoc.indexOf(ext) >= 0) {
      oneFile = 'Shared a document';
      manyFile = 'Shared documents';
      if (this.pushNotifyMsg.indexOf(manyFile) >= 0) {
        // no changes
      } else if (this.pushNotifyMsg.indexOf(oneFile) >= 0) {
        this.pushNotifyMsg = this.pushNotifyMsg.replace(oneFile, manyFile);
      } else {
        if (this.pushNotifyMsg) {
          this.pushNotifyMsg = this.pushNotifyMsg + ', ' + oneFile;
        } else {
          this.pushNotifyMsg = oneFile;
        }
      }
    }
    console.log('pushNotificationMessage:::::::::::: ', this.pushNotifyMsg);
  }
  getSiteIds(siteId: any) {
    this.siteId = siteId.siteId;
  }
  hideDropdown(hideItem: any) {
    this.hideSiteSelection = hideItem.hideItem;
  }
  emitEventToSelectSites(): void {
    this.eventsSubject.next();
  }

  selectedPriority(value) {
    this.prioritySelected = value;
  }

  /**
 * To check if the selected roles or tags includes patient
 * @returns a message if the selected roles or tags includes patient, with count
 */
checkForPatient(): string {
  let patientCount = 0;
  let patientRoleCount = 0;
  let patientTagCount = 0;

  this.selectedOptions.forEach((element) => {
    const tagMatch = element.match(/tag-(\d+)/);
    if (tagMatch) {
      const tag = this.userTags.find(tag => tag.id == tagMatch[1]);
      if (tag && tag.patientCount) {
        patientCount += tag.patientCount;
        patientTagCount++;
      }
    } else {
      const roleId = element.split(':')[1].trim().replace(/'/g, '');
      const role = this.teanntRoles.find(role => role.id == roleId);
      if (role && +role.citus_role_id === 3) {
        patientRoleCount++;
      }
    }
  });

  const messages = [
    patientRoleCount ? this._ToolTipService.getTranslateDataWithParam('WARNING.PATIENT_ROLE_COUNT', { patientRoleCount }) : '',
    patientTagCount ? this._ToolTipService.getTranslateDataWithParam('WARNING.PATIENT_TAG_COUNT', { patientTagCount, patientCount }) : ''
  ].filter(Boolean);

  return messages.length ? this._ToolTipService.getTranslateDataWithParam('WARNING.PATIENT_COUNT', { message: messages.join(' & ') }) : '';
}

}
