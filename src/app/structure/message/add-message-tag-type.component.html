<!-- START: tables/datatables --> 
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
          <strong>Add Message Tag Type</strong>                      
      </span>

    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/message']">Message Settings</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/tag-type']">Message Tag Types</a></li>
            <li class="breadcrumb-item">Add Message Tag Type</li>
        </ol>
        <div class="row">
            <div class="col-lg-12">              
                <div class="mb-5">
                    <form class="form-horizontal" (ngSubmit)="addMessageTagType(f)" [formGroup]="tagTypeAdd" novalidate #f="ngForm">
                        <div class="form-body">
                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Message Tag Type Name * </label>
                                </div>
                                <div class="col-md-6">
                                    <input class="form-control" type="text" placeholder="Message Tag Type Name " [formControl]="tagTypeAdd.controls['tagTypeName']" name="tagTypeName" id="tagTypeName">
                                    <div class="alert alert-danger" *ngIf="!tagTypeAdd.controls.tagTypeName.valid && (tagTypeAdd.controls.tagTypeName.dirty || tagTypeAdd.controls.tagTypeName.touched || f.submitted)">
                                        Message Tag Type Name cannot be empty.
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Tag Type Description </label>
                                </div>
                                <div class="col-md-6">
                                    <textarea class="form-control" [formControl]="tagTypeAdd.controls['tagTypeDesc']" placeholder="Tag Type Description"></textarea>
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">User Type * </label>
                                    <i class="message-type-tt icmn-info"></i>
                                </div>
                                <div class="col-md-6">
                                    <select class="form-control" [formControl]="tagTypeAdd.controls['tagUserType']"  id="tagUserType" >
                                        <option value="">Select User Type</option>
                                        <option [selected]="tagTypeAdd.controls['tagUserType'].value == 'staff-facing'" value="staff-facing">Staff Facing</option>
                                        <option [selected]="tagTypeAdd.controls['tagUserType'].value == 'patient-facing'" value="patient-facing">Patient Facing</option>
                                        <option [selected]="tagTypeAdd.controls['tagUserType'].value == 'staff-patient-facing'" value="staff-patient-facing">Both Staff & Patient Facing</option>

                                    </select>
                                    <div *ngIf="tagTypeAdd.controls['tagUserType'].hasError('required')&&(tagTypeAdd.controls.tagUserType?.dirty ||tagTypeAdd.controls.tagUserType?.touched || f.submitted)" class="alert alert-danger">
                                        User Type cannot be empty
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Background Color </label>
                                </div>
                                <div class="col-md-6">
                                    <input class="form-control" [formControl]="tagTypeAdd.controls['bgColor']" [(colorPicker)]="bgColour" [cpWidth]="'230px'" [cpHeight]="'197px'" [cpPositionRelativeToArrow]="false" [cpAlphaChannel]="'disabled'" [cpOutputFormat]="'hex'" style="width:50px;"
                                        [style.background]="bgColour" [value]="" />
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Font Color </label>
                                </div>
                                <div class="col-md-6">
                                    <input class="form-control" [formControl]="tagTypeAdd.controls['fontColor']" [(colorPicker)]="fontColour" [cpWidth]="'230px'" [cpHeight]="'197px'" [cpPositionRelativeToArrow]="false" [cpAlphaChannel]="'disabled'" [cpOutputFormat]="'hex'" style="width:50px;"
                                        [style.background]="fontColour" [value]="" />
                                </div>
                            </div>


                            <div class="form-group row">
                                <div class="col-md-6">
                                    <button class="btn btn-primary addmsgtagtype">Add</button>
                                    <a [routerLink]="['/message/tag-type']" class="btn btn-default">Cancel</a>
                                    <button type="reset" style="display:none;" id="hideReset" class="btn btn-success">Reset</button>
                                </div>
                            </div>
                        </div>
                    </form>                   
                </div>
            </div>
        </div>
    </div>
</section>
<!-- END: tables/datatables -->