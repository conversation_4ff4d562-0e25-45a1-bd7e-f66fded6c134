import { Directive, ElementRef, Input, AfterViewInit } from '@angular/core';
import { isBlank } from 'app/utils/utils';
import { StructureService } from '../structure.service';

@Directive({
  selector: '[compile]',
})
export class chatLogDirective implements AfterViewInit  {    

  @Input() compile: string;
    constructor(private elRef: ElementRef, private structureService: StructureService) {       

    }
    ngAfterViewInit(): void {
       // console.log(this.compile);  
                        var resetValue;
                        var dataValue;
                        if(this.compile.search("data-mediaType='pdf'")!=-1){
                             dataValue = this.compile;
                            var hrefStrings = dataValue.substring(parseInt(dataValue.lastIndexOf("data-src="))+9,dataValue.lastIndexOf(".pdf'"));
                            var appendedString = " data-view="+hrefStrings+".pdf'";
                            // resetValue = this.compile.splice(parseInt(dataValue.lastIndexOf(".pdf'"))+5,0,appendedString);
                            resetValue = dataValue;
                        } else {
                            resetValue = this.compile;
                        }
            this.elRef.nativeElement.innerHTML = resetValue;
            this.addActiveMention();
    }

    addActiveMention() {
      const elements = this.elRef.nativeElement.getElementsByClassName('mention');
      for (const element of elements) {
        const attrib = element.getAttribute('attr.data-target');
        if (!isBlank(attrib) && Number(attrib) === Number(JSON.parse(this.structureService.userDetails).userId)) {
          element.classList.add('active');
        }
      }
    }
}
 