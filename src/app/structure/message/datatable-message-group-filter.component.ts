import { Component, Input, Output, EventEmitter } from '@angular/core';

/**
 * Base class to provide member props and method to mimick worklist filter functionality for Message Groups DataTable search
 */
@Component({
  selector: 'dt-msg-group-filter-dropdown',
  templateUrl: './datatable-message-group-filter.component.html'
})
export class DataTableMsgGroupFilterComponent {

  public filterEnabledFields = [];
  public selectedSearchFields = [];
  @Input() filteredFields = [];
  @Input() selectedFields = [];
  @Input() filterCheckboxHandler;
  @Output() childSelectedFields = new EventEmitter();
  
  constructor() {
  
    /* @TODO: 1) find out which of these should be default or not, 2) cache user settings via localStorage if worklist is doing something similar

    filterEnableFields mimics worklistDetails.reportFields, which is of an array of WorklistReportField (gql type), way more fields than we're interested in, we need the following:

    fieldId: a unique integer, we're not using it to map fields on the backend like worklist but at least we can keep track of the filters and determine which type to pass to the backend
    enableAutoSelect: Determines which filters are selected by default
    headerName: The text users see in the dropdown option
    */
    this.filterEnabledFields = [{
      fieldId: 1,
      enableAutoSelect: true,
      headerName: 'Member Name',
    }, {
      fieldId: 2,
      enableAutoSelect: true,
      headerName: 'Member Role Name',
    },];
    
    // Set default filter options in data by default, too
    this.filterEnabledFields.map(field => { if (field.enableAutoSelect) this.selectedSearchFields.push(field) });
    
    // defaults; below is example of intention except imagine left side as @Inputs are overridden i.e. in [] brackets and right side is being provided by wrapping parent class
    this.filteredFields = this.filterEnabledFields;
    this.selectedFields = this.selectedSearchFields;
    this.filterCheckboxHandler = this.checkboxSelection;
  }
  
  /**
   * Record selected filter options
   *
   * This function is almost a verbatim copy from in form-worklist component
   *
   * For some reason, even if parent class provides this as input, this base class' method is used regardless, so we had to @Output its results.
   */
  checkboxSelection(field) {
    if (this.selectedSearchFields.findIndex(x => x.fieldId === field.fieldId) === -1) {
      this.selectedSearchFields.push(field);
    } else {
      this.selectedSearchFields = this.selectedSearchFields.filter(x => x.fieldId !== field.fieldId);
    }
    
    // Send filter changes up to parent class
    this.childSelectedFields.emit(this.selectedSearchFields);
  }
  
  /**
   * Receive and set filter changes 
   *
   * This method is intended to be inherited by a parent class and used to capture this class' output
   */
  setSelectedSearchFields(selectedSearchFields: any[]) {
    this.selectedSearchFields = selectedSearchFields;
  }

}

