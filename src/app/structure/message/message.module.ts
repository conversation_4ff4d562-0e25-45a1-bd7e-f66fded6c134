import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { BrowserModule } from '@angular/platform-browser';
import { ReactiveFormsModule } from '@angular/forms';
import { FormsModule } from '@angular/forms';
import { MessageComponent } from './message.citushealth';
import { MessageGroupComponent } from './messagegroup.citushealth';
import { DiscussionGroupComponent } from './discussion-group-list.citushealth';
import { MessageGroupMemberComponent } from './addgroupmember.citushealth';
import { EditMessageGroupComponent } from './editmessagegroup.citushealth';
import { PatientDiscussionGroupComponent } from './patient-discussion.citushealth';
import { PatientDiscussionGroupMemberComponent } from './patient-discussion-group-members.citushealth';
import { MessageBroadCastComponent } from './message-broadcast.citushealth';
import { LogsComponent } from './logs.messages';
import { MessageViewerComponent } from './message-viewer.citushealth';
import { TagDefinitionsComponent } from './tag-definitions.messages';
import { NgUploaderModule } from 'ngx-uploader';
import { chatLogdateshortFilterPipe } from './chatlog-date-filter.pipes';
import { SharedModule } from '../shared/sharedModule';
import { InboxModule } from '../inbox/inbox.module';
import {
	MessageGroupSearchFilterPipe,
	SearchRoleFilterSchedulePipe,
	SearchFilterPipe,
	virtualStaffFilterPipe,
	SearchGroupFilterSchedulePipe,
	SearchRoleFilterMsgGrpsPipe,
	SearchFilterRoleTreeViewPipe,
	pluralizeScheduleFilterPipe
} from './messagegroup-search.pipes';
import { filterMessageGroupPipe } from './messagegroup-search.pipes';
import { MessageTagEditComponent } from './message-tag-edit.component';
import { MessageTagTypeComponent } from './message-tag-type.component';
import { MessageTagTypeEditComponent } from './message-tag-type-edit.component';
import { ColorPickerModule } from 'ngx-color-picker';
import { AddMessageTagComponent } from './add-message-tag.component';
import { AddMessageTagTypeComponent } from './add-message-tag-type.component';
import { DataTableMsgGroupFilterComponent } from './datatable-message-group-filter.component';
import { ListPatientDiscussionGroupComponent } from './list-patient-discussion-group.component';
import { AddPatientDiscussionGroupComponent } from './add-patient-discussion-group.component';
import { ListMessageGroupComponent } from './list-message-group.component';
import { AddMessageGroupComponent } from './add-message-group.component';
import { pluralizeFilterPipe,caregiverOrAlternatePipe } from './broadcast-message.pipes';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { AuthGuard } from '../../guard/auth.guard';
import { Daterangepicker } from 'ng2-daterangepicker';

export const routes: Routes = [
	{ path: 'message/add-message-groups', component: AddMessageGroupComponent, canActivate:[AuthGuard], data: {
		checkRoutingConfig: 'enable_message_center', checkUserGroupPermission:'3'
	}},
	{ path: 'message/edit-message-group', component: MessageComponent, canActivate:[AuthGuard], data: {
		checkRoutingConfig: 'enable_message_center', checkUserGroupPermission:'3'
	} },
	{ path: 'message/message', component: ListMessageGroupComponent, canActivate:[AuthGuard], data: {
		checkRoutingConfig: 'enable_message_center'
	}},
	{ path: 'message/messagegroup', component: MessageGroupComponent,canActivate:[AuthGuard], data: {checkRoutingConfig: 'enable_message_center'} },
	{ path: 'message/add-patient-discussion-groups', component: AddPatientDiscussionGroupComponent, canActivate:[AuthGuard], data: {
		checkRoutingConfig: 'enable_message_center', checkUserGroupPermission:'3'
	}},
	{ path: 'message/edit-patient-discussion-groups', component: DiscussionGroupComponent, canActivate:[AuthGuard], data: {
		checkRoutingConfig: 'enable_message_center',checkUserGroupPermission:'3'
	}},
	{ path: 'message/patient-discussion-groups', component: ListPatientDiscussionGroupComponent, canActivate:[AuthGuard], data: {
		checkRoutingConfig: 'enable_message_center',checkUserGroupPermission:'3'
	}},
	{ path: 'message/addgroupmember', component: MessageGroupMemberComponent,canActivate:[AuthGuard], data: {checkRoutingConfig: 'enable_message_center'} },
	{ path: 'message/editmessagegroup', component: EditMessageGroupComponent,canActivate:[AuthGuard], data: {checkRoutingConfig: 'enable_message_center'} },
	{ path: 'message/patient-discussion', component: PatientDiscussionGroupComponent,canActivate:[AuthGuard], data: {checkRoutingConfig: 'enable_message_center'} },
	{ path: 'message/patient-discussion/add-group-member', component: PatientDiscussionGroupMemberComponent,canActivate:[AuthGuard], data: {checkRoutingConfig: 'enable_message_center'} },
	{ path: 'message/message-broadcast', component: MessageBroadCastComponent, canActivate:[AuthGuard], data: {
		checkRoutingConfig: 'enable_message_center', checkRoutingPrivileges : 'superAdmin,sendBroadcaseMessage'
	}},
	{ path: 'message/logs', component: LogsComponent,canActivate:[AuthGuard], data: {checkRoutingConfig: 'enable_message_center',checkRoutingPrivileges:'manageTenants,manageChatLogs'} },
	{ path: 'message/logs/chat-logs', component: MessageViewerComponent,canActivate:[AuthGuard], data: {checkRoutingConfig: 'enable_message_center'} },
	{ path: 'message/tag-definitions', component: TagDefinitionsComponent,canActivate:[AuthGuard], data: {checkRoutingConfig: 'enable_message_center',checkRoutingPrivileges:'superAdmin,manageTenants'} },
	{ path: 'message/edit-tag/:id', component: MessageTagEditComponent,canActivate:[AuthGuard], data: {checkRoutingConfig: 'enable_message_center', checkUserGroupPermission:'3'} },
	{ path: 'message/tag-type', component: MessageTagTypeComponent,canActivate:[AuthGuard], data: {checkRoutingConfig: 'enable_message_center',checkRoutingPrivileges:'superAdmin,manageTenants'} },
	{ path: 'message/add-message-tag', component: AddMessageTagComponent,canActivate:[AuthGuard], data: {checkRoutingConfig: 'enable_message_center', checkUserGroupPermission:'3'} },
	{ path: 'message/edit-tag-type/:id', component: MessageTagTypeEditComponent,canActivate:[AuthGuard], data: {checkRoutingConfig: 'enable_message_center', checkUserGroupPermission:'3'} },
	{ path: 'message/add-message-tag-type', component: AddMessageTagTypeComponent,canActivate:[AuthGuard], data: {checkRoutingConfig: 'enable_message_center', checkUserGroupPermission:'3'} },
	{ path: 'message/add-patient-discussion-groups', component: AddMessageTagTypeComponent,canActivate:[AuthGuard], data: {checkRoutingConfig: 'enable_message_center'} }
];

@NgModule({
	imports: [
		CommonModule,
		FormsModule,
		BrowserModule,
		ReactiveFormsModule,
		RouterModule.forChild(routes),
		NgUploaderModule,
		ColorPickerModule,
		SharedModule,
		InboxModule,
  		Daterangepicker,
		NgbModule.forRoot()
	],
	declarations: [
		MessageComponent,
		MessageGroupComponent,
		DiscussionGroupComponent,
		MessageGroupMemberComponent,
		EditMessageGroupComponent,
		PatientDiscussionGroupComponent,
		PatientDiscussionGroupMemberComponent,
		MessageBroadCastComponent,
		LogsComponent,
		MessageViewerComponent,
		TagDefinitionsComponent,
		chatLogdateshortFilterPipe,
		MessageGroupSearchFilterPipe,
		filterMessageGroupPipe,
		MessageTagEditComponent,
		MessageTagTypeComponent,
		MessageTagTypeEditComponent,
		AddMessageTagComponent,
		AddMessageTagTypeComponent,
    DataTableMsgGroupFilterComponent,
		ListPatientDiscussionGroupComponent,
		AddPatientDiscussionGroupComponent,
		ListMessageGroupComponent,
		AddMessageGroupComponent,
		pluralizeFilterPipe,
		caregiverOrAlternatePipe,
		SearchRoleFilterSchedulePipe,
		SearchGroupFilterSchedulePipe,
		SearchFilterPipe,
		virtualStaffFilterPipe,
		SearchRoleFilterMsgGrpsPipe,
		SearchFilterRoleTreeViewPipe,
		pluralizeScheduleFilterPipe
	],
	providers: [ MessageGroupSearchFilterPipe, filterMessageGroupPipe ],
	exports: [ MessageViewerComponent ]
})
export class MessageModule {}
