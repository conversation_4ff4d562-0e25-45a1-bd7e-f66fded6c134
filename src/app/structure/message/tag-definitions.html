<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Message Tags <i class="message-tag icmn-info" data-toggle="tooltip" data-placement="right"></i></strong>            
            <a [routerLink]="['/message/add-message-tag']" class="pull-right btn btn-sm btn-primary">Add Message Tag <i class="ml-1"></i></a>
        </span>

    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/message']">Message Settings</a></li>
            <li class="breadcrumb-item">Message Tags</li>
        </ol>
        <div class="row">
            <div class="col-lg-12">
                <!-- <h5 class="text-black"><strong>Tag Definitions</strong></h5>
                <p class="text-muted">Element: read <a href="https://datatables.net/" target="_blank">official documentation<small><i class="icmn-link ml-1"></i></small></a></p> -->
                <div class="mb-5">
                  <!--  <form class="form-horizontal" [formGroup]="tagDefenitionAdd" novalidate #f="ngForm">
                         <div class="form-body">
                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Message Tag Name * </label>
                                </div>
                                <div class="col-md-6">
                                    <input class="form-control" type="text" placeholder="Message Tag Name" name="tagName" [formControl]="tagDefenitionAdd.controls['tagName']" id="tagName">
                                    <div class="alert alert-danger" *ngIf="!tagDefenitionAdd.controls.tagName.valid && (tagDefenitionAdd.controls.tagName.dirty || tagDefenitionAdd.controls.tagName.touched || f.submitted)">
                                        Message Tag Name cannot be empty.
                                    </div>

                                </div>
                            </div>
                            <div class="form-group row" [hidden]="!isFilingCenter">
                                <div class="col-md-3">
                                    <label class="control-label">Default Outgoing Filing Center <i class="default-filing-center icmn-info" data-toggle="tooltip" data-placement="right"></i></label>
                                </div>
                                <div class="col-md-6">
                                    <input type="hidden" id="sfilingCenterss" value="" style="width:300px;" />
                                    <i *ngIf="showClose" title="Clear" (click)="clearFilingCenter()" style="position:absolute;top:14px;right:37px;font-size: 9px;cursor: pointer;" class="icmn-cross"></i>
                                    <select style="display:none;" class="form-control" [(ngModel)]="folderName" [formControl]="tagDefenitionAdd.controls['filingcenter']" name="filingcenter" id="filingcenter">                                           
                                            <option *ngFor="let g of folderLists;" [ngValue]="g">
                                                {{g.folderName}}
                                            </option>
                                    </select>                                
                                </div>
                            </div>
                            <div class="form-group row" [hidden]="!isFilingCenter">
                                <div class="col-md-3">
                                    <label class="control-label">Default File Name format <i class="default-filename icmn-info" data-toggle="tooltip" data-placement="right"></i></label>
                                </div>
                                <div class="col-md-6">
                                    <input class="form-control" type="text" placeholder="Default File Name format" name="fileSaveFormat" [formControl]="tagDefenitionAdd.controls['fileSaveFormat']" id="fileSaveFormat">
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Message Tag Type <i class="default-tagtype icmn-info" data-toggle="tooltip" data-placement="right"></i></label>
                                </div>


                                <div class="col-md-6">
                                    <input type="hidden" id="tagtypesss" value="" style="width:300px;" />
                                    <i *ngIf="showClosetag" title="Clear" (click)="cleartagtype()" style="z-index:9999; position:absolute;top:14px;right:37px;font-size: 9px;cursor: pointer;" class="icmn-cross"></i>

                                    <select class="form-control selectdd" id="tagtype"> Select Tag Type
                                        <option></option>
                                        <option *ngFor="let type of tagtypeList" data-value='{{type.tagTypeId}}'>{{type.typeName}} </option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Summarize as Outcome Measure <i class="default-outcomeMeasure icmn-info" data-toggle="tooltip" data-placement="right"></i></label>
                                </div>
                                <div class="btn-group col-md-6">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': outcomeMeasures}" (click)="togglePreference('outcomeMeasures',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !outcomeMeasures}" (click)="togglePreference('outcomeMeasures',false)">
                                                              No
                                         </button>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Approval Required <i class="default-approvalRequired icmn-info" data-toggle="tooltip" data-placement="right"></i></label>
                                </div>
                                <div class="btn-group col-md-6">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': approvalRequired}" (click)="togglePreference('approvalRequired',true)">
                                                           Yes
                                         </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !approvalRequired}" (click)="togglePreference('approvalRequired',false)">
                                                              No
                                         </button>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-6">
                                    <button type="submit" (click)="addMessageTag(f)" class="addmessagetag btn btn-primary">Add</button>
                                    <button type="reset" style="display:none;" id="hideReset" class="btn btn-success">Reset</button>
                                </div>
                            </div>
                        </div> 
                    </form>-->
                    <form class="new-form" [formGroup]="tagDefenitionEdit">
                        <div class="wait-loading" *ngIf="dataLoadingMsg">
                            <img src="./assets/img/loader/loading.gif" />
                        </div>                       
                        <table class="table table-hover" id="dtMessageTags" width="100%"></table>
                        <table class="table table-hover" id="dtMessageTagsOnly" width="100%"></table>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- END: tables/datatables -->