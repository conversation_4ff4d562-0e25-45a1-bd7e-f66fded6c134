<!-- START: apps/messaging -->
<section class="card">
    <!-- Message Tag Modal -->
    <div class="modal fade forward-modal message-tag-modal-container" id="messageTagModal" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content" style="border:1px solid grey">
                <div class="modal-header">
                    <h4 class="modal-title" id="messageTagModalLabel">Message Tags</h4>
                    <button type="button" class="close" (click)="closeTagModal()" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                    </button>
                </div>
                <div class="modal-body">
                    <form [formGroup]="messageTagForm" id="messageTagForm">
                        <div style="margin-bottom: 9px;">
                            <select class="form-control select2" formControlName="messageTags" id="messageTags" multiple> 
                                
                                <option *ngFor="let tags of availableTags" value="{{tags.id}}" > {{tags.name}} {{tags.typeName ? '(' : ''}}{{tags.typeName ? tags.typeName : ''}}{{tags.typeName ? ')' : ''}} </option>
                            </select>
                        </div>
                        <div *ngIf="tagNotSelected"
                        class="alert alert-danger">
                        Select one or more Tags
                        </div>
                        <div [hidden]="(!patientFace ||  userData.group == '3')">
                            <div class="col-lg-12"  [hidden]="!hideSiteSelection || (selectedAssosiatePatientName && patientFace)">
                                <span class="col-md-4">
                                    <app-select-sites [events]="eventsSubject.asObservable()"  [singleSelection]=true (siteIds)="getAssocSiteIds($event,'Associate Patient')" (hideDropdown)="hideDropdown($event)">
                                    </app-select-sites>
                                </span>
                            </div>
                            <div class="col-lg-12">
                            <input type="text" class="form-control associate-search-input" (keydown)="searchOnEnter($event)"  value="{{selectedPatient}}" id="associate-search-input" autocomplete="off" placeholder="Search a Patient..." (click)="openAssociateList()"/>
                            <div class="asscoiate-actions">
                                <button [disabled]="associatePatientLoading" id="associate-search" (click)="checkAssociatePatientWithTems()" style="margin-top: -115px;"  class="associate-search-button btn btn-sm btn-primary" >Search</button>
                                <button [disabled]="associatePatientLoading" class="associate-close" id="associate-close" (click)="closeSelectedAssociatePatient()" style="margin-top: -115px;" class="associate-search-button btn btn-sm btn-default" >Reset</button>
                            </div> 
                                <ul class="associate-ul" id="associate-ul">
                                    <li id="associate-li" class="associate-li selected" *ngIf="selectedAssosiatePatientName!=''">{{selectedAssosiatePatientName}}</li>
                                    <li id="associate-li" class="associate-li" *ngIf="assosiatedPatients.length==0">No item found</li>
                                    <li id="associate-li" class="associate-li" [ngClass]="{'selected': (selectedAssosiatePatient==assosiatedPatient.userId)}" *ngFor="let assosiatedPatient of assosiatedPatients" [hidden]="selectedAssosiatePatient==assosiatedPatient.userId" (click)="selectAssosiatePatient(assosiatedPatient.userId,assosiatedPatient.listDisplayName)">
                                            {{assosiatedPatient.listDisplayName}}
                                    </li>
                                </ul>
                            </div>
                        </div>
            <div *ngIf="userNotSelected && patientFace &&  userData.group !== '3' "
                        class="alert alert-danger">
                        Select a Patient
            </div>
                        <div style="margin-top: 10px;">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary"  (click)="closeTagModal()">Close</button>
                    <button type="button" class="btn btn-primary" (click)="submitText()" style="cursor:pinter;">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- End Section -->
    <!-- Message Comment Modal -->
    <div class="modal fade forward-modal message-comment-modal-container" id="messageCommentModal" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="messageTagModalLabel">Message Comment</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                    </button>
                </div>
                <div class="modal-body">
                    <form [formGroup]="messageCommentForm" id="messageCommentForm">
                        <div style="margin-top: 10px;">
                            <textarea class="form-control" rows="3" type="text" formControlName="chatMessage" id="chat-message"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" (click)="submitComment()" style="cursor:pinter;">Save</button>
                </div>
            </div>
        </div>
    </div>

    <!-- End Section -->
    <div class="card-header">
        <h4 class="mt-1 mb-1 text-black">
            <strong [hidden]="hideElement">Chat Logs</strong>
            <div [hidden]="!hideElement" class="btn-group">
                <select class="action form-control" data-placeholder="Actions" [(ngModel)]="download" (change)="downloadPdf($event)" style="background-color: #0190fe;color:#ffffff;padding: 6px;width: 100px;">
                    <option value="0"  hidden>Download</option>
                    <option value="1" style="background-color: #ffffff;color: #000000;">{{'OPTIONS.CHAT_LOG_DOWNLOAD.IDENTITIES_UN_REDACT' | translate}}</option>
                    <option value="2" style="background-color: #ffffff;color: #000000;">{{'OPTIONS.CHAT_LOG_DOWNLOAD.IDENTITIES_REDACT' | translate }}</option>
                    <option value="3" style="background-color: #ffffff;color: #000000;">{{'OPTIONS.CHAT_LOG_DOWNLOAD.FILING_IDENTITIES_UN_REDACT' | translate}}</option>
                    <option value="4" style="background-color: #ffffff;color: #000000;">{{'OPTIONS.CHAT_LOG_DOWNLOAD.FILING_IDENTITIES_REDACT' | translate}}</option>
                </select>
            </div>
            <span class="pull-right">
            <small class="cat__apps__messaging__tab__time ">
                <button [hidden]="hideElement" aria-expanded="false" class="btn btn-sm btn-primary dropdown-toggle action-btn" data-toggle="dropdown" type="button"> Download</button>
                <ul class="dropdown-menu dropdown-menu-right">
                    <a (click)="generateChatReport(false, false)" class="dropdown-item">{{'OPTIONS.CHAT_LOG_DOWNLOAD.IDENTITIES_UN_REDACT' | translate}}<i class="ml-1"></i></a>
                    <a (click)="generateChatReport(true, false)" class="dropdown-item">{{'OPTIONS.CHAT_LOG_DOWNLOAD.IDENTITIES_REDACT' | translate }}<i class="ml-1"></i></a>
                    <a (click)="generateChatReport(false, true)"class="dropdown-item">{{'OPTIONS.CHAT_LOG_DOWNLOAD.FILING_IDENTITIES_UN_REDACT' | translate}}<i class="ml-1"></i></a>
                    <a (click)="generateChatReport(true, true)" class="dropdown-item">{{'OPTIONS.CHAT_LOG_DOWNLOAD.FILING_IDENTITIES_REDACT' | translate}}<i class="ml-1"></i></a>
                </ul>
                </small>
                <a (click)="backClicked()" class="btn btn-sm btn-primary" style="margin-top: 4px!important;" [hidden] = "hideElement">Back<i class="ml-1"></i></a>
            </span>
        </h4>
        <span>
                                  
    </span>
    </div>
    <div class="breadcrumb-wrap" [hidden] = "hideElement">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/logs']">Message Settings</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/logs']">Chat Logs</a></li>
            <li class="breadcrumb-item">Logs</li>
        </ol>
    </div>
    <div class="card-block">
        <div style="text-align:center;width:100%;" *ngIf="dataLoadingMsg">
            <img src="./assets/img/loader/loading.gif" />
        </div>
        <div class="card-block">
            <div class="cat__apps__messaging">
                <div class="height-700 custom-scroll cat__core__scrollable">
                    <div class="cat__apps__chat-block">
                        <div class="cat__apps__chat-block__item clearfix" *ngFor="let chat of chats index as i">
                            <div class="cat__apps__chat-block__avatar avatar-position">
                                <a class="cat__core__avatar cat__core__avatar--50" href="javascript:void(0);">
                                    <img [src]="chat.sender.avatar ? avatarImagePath + chat.sender.avatar : defaultAvatarImageUrl" (error)="updateImageUrl($event.target)" alt="Alternative text to the image" outOfOfficeStatus [oooInfo]="chat.sender.oooInfo"/>
                                </a>
                            </div>
                            <div class="cat__apps__chat-block__content chatlog-more-dropdown-block message-tag-list-chat-log">
                                <small class="cat__apps__messaging__tab__time">{{chat.formattedDate*1000 | dateshortFilter }}</small>
                                <div class="cat__apps__messaging__tab__name">{{chat.sender.name.formattedName}}:&nbsp;<span *ngIf="chat.message.priorityId !== MESSAGE_PRIORITY.NORMAL" [ngClass]="chat.message.priorityId === MESSAGE_PRIORITY.HIGH ? 'badge badge-danger' : 'badge badge-primary'">{{ 'PRIORITIES.' + (MessagePriorityData | filter:chat.message.priorityId:'key')[0]?.value | translate }}</span></div>
                                <div class="cat__apps__messaging__tab__text chat-log chat-message-container">
                                    <span *ngIf="chat.message.messageStatus === 1; else messageHistory" compile="{{chat.message.text | autolinker}}"></span>
                                    <ng-template #messageHistory>
                                        <ng-container *ngFor="let msg of chat.message.deleteUndoHistory; let i = index;">
                                            <span class="font-italic">{{chat.message.deleteUndoHistory.length - i}}. {{ msg.actionHistory }}</span><br>
                                        </ng-container>
                                    </ng-template>
                                    <div class="message-tag-wrap">
                                        <span *ngIf="chat.attachedTags+chat.approvedDetails.approverTags" class="cat__menu-left__icon fa fa-tags" style="float: left;position: relative;top: 3px;left:-5px;"></span>
                                        <span *ngIf="chat.attachedTags+chat.approvedDetails.approverTags" style="float: left;margin-right: 5px;">Tags:</span>
                                        <div class="tag-list tag-list-log" [style.background-color]="tagsList.type.bgColor" [style.color]="tagsList.type.fontColor" [style.background]="tagsList.type.bgColor" *ngFor="let tagsList of chat.attachedTags index as t" (mouseenter)='over(i,t,chat.taggedUser,tagsList,1)'
                                            (mouseleave)='over(i,t,chat.taggedUser,tagsList,1)'>
                                            <i class="fa fa-caret-left" [style.color]="tagsList.type.bgColor ? tagsList.type.bgColor : '#8899a0'" aria-hidden="true"><span></span></i><span class="message-tag-list-text enterKeyCheck_{{i}}{{t}}" data-html="true"
                                                data-toggle="popover" data-animation="false" data-trigger="hover focus" data-placement="top" [style.color]="tagsList.type.fontColor ? tagsList.type.fontColor : '#ffffff'">{{tagsList.tagName}}</span>
                                            <span *ngIf="chat.approverSign && privileges.indexOf('chatLogDeleteSignedTag')!==-1" class="tag-close" (click)="removeTagsFromMessage(tagsList.tagId,chat.message.id,userData.userId,t,this,i,0,'')">x</span>
                                            <span *ngIf="!chat.approverSign && privileges.indexOf('chatLogDeleteTag')!==-1" class="tag-close" (click)="removeTagsFromMessage(tagsList.tagId,chat.message.id,userData.userId,t,this,i,0,'')">x</span>

                                        </div>
                                        <div class="tag-list tag-list-log" [style.background-color]="tagsList.type.bgColor" [style.color]="tagsList.type.fontColor" [style.background]="tagsList.type.bgColor" *ngFor="let tagsList of chat.approvedDetails.approverTags index as t" (mouseenter)='over(i,t,chat.taggedUser,tagsList,0)'
                                            (mouseleave)='over(i,t,chat.taggedUser,tagsList,1)'>
                                            <i class="fa fa-caret-left" [style.color]="tagsList.type.bgColor ? tagsList.type.bgColor : '#8899a0'" aria-hidden="true"><span></span></i><span class="message-tag-list-text enterKeyCheck__{{i}}{{t}}" data-html="true"
                                                data-toggle="popover" data-animation="false" data-trigger="hover focus" data-placement="top" [style.color]="tagsList.type.fontColor ? tagsList.type.fontColor : '#ffffff'">{{tagsList.tagName}}</span>
                                            <span *ngIf="chat.approverSign && privileges.indexOf('chatLogDeleteSignedTag')!==-1" class="tag-close" (click)="removeTagsFromMessage(tagsList.id,chat.message.id,userData.userId,t,this,i,1,chat.approvedDetails.approverId)">x</span>
                                            <span *ngIf="!chat.approverSign && privileges.indexOf('chatLogDeleteTag')!==-1" class="tag-close" (click)="removeTagsFromMessage(tagsList.id,chat.message.id,userData.userId,t,this,i,1,chat.approvedDetails.approverId)">x</span>
                                        </div>
                                    </div>
                                    <div class="message-tag-comment-box" id="comment-disp-{{i}}" style="display:none;">
                                        <div>
                                            <div class="messagetag-comment">
                                                <div>
                                                    <div class="dropdown-more-profile"><img src="{{profileImageUrl}}" alt="Alternative text to the image" /></div>
                                                    <div class="dropdown-more-profile-name"><span class="name">Peter George</span> <span> added a comment - 12/08/2017 10:26 AM</span></div>
                                                </div>
                                                <div class="messagetag-profile-decription">
                                                    Lorem ipsum is simply dummy text of the printing and typesetting industry.
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <p class="msg-cont-text sign" *ngIf="chat.sign">
                                    <img [src]="signUrl+chat.sign" class="chat-sign-image">
                                </p>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

    </div>
    <modal [show-modal]="isModalOpen" [modal-retry]=true [title]="title" [sub-title]="subTitle" [modal-body]="modalBody" [show-header]="showHeader"
    [show-footer]="false" [cancel-label]="cancelLabel" (closed)="closeModal()" [show-iframe]="showIframe" [show-video]="showVideoFrame">
	</modal>
</section>
<!-- END: apps/messaging -->
