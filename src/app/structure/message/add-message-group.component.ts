import { <PERSON>mpo<PERSON>, <PERSON><PERSON>ni<PERSON>, <PERSON>Child,ElementRef } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { filterMessageGroupPipe } from './messagegroup-search.pipes';
import { SharedService } from './../shared/sharedServices';
import { InboxService } from './../inbox/inbox.service';
import { userFilterPipe } from './../../structure/inbox/inbox-modal-filter';
import { GlobalDataShareService } from './../../structure/shared/global-data-share.service';
import { GroupsListingRequest, MessageGroupRequest } from '../../models/message-center/messageCenter';
import { MessageService } from 'app/services/message-center/message.service';
import { GroupType, CONSTANTS } from 'app/constants/constants';
import { Location } from '@angular/common';

declare var $: any;
declare var swal: any;

var async = require("async");
import each from 'async/each';
import { Subject } from 'rxjs';
import { isBlank } from 'app/utils/utils';
@Component({
  selector: 'app-add-message-group',
  templateUrl: './add-message-group.component.html',
  styleUrls: ['./message-common-component.scss']
})
export class AddMessageGroupComponent implements OnInit {
  @ViewChild('userSearch') srch:ElementRef;
  @ViewChild('newMembers') srchs:ElementRef;
  editGroupMessage = false;
  searchInboxkeyword;
  addGroupMessage = false;
  groupList = [];
  staffList = [];
  otherTenantStaffList = [];
  loadingGroups = true;
  memberList = [];
  otherTenantMemberList = [];
  selected: any = {};
  newMemberList = [];
  prevText;
  newMemberOtherTenantList = [];
  newMemberListByRoleWise = [];
  newMemberOtherTenantListByRoleWise = [];
  newMember;
  existingMemberIds = [];
  selectedGroupMembers = [];
  selectedGroupMemberIds = [];
  checkedIds = [];
  testArr = [];
  selectedisPublic;
  selectedAllowMultiThread = true;
  messageGroup: FormGroup;
  updateParams;
  userDetails: any;
  userData;
  allowSave = true;
  privilegeManageAllMessageGroup = false;
  addMemberError = false;
  noMemberError = false;
  disableButton = true;
  setupComponentStatus: any;
  checkComponentStatus = false;
  offset=0;
  limit = 25;
  allMessageGroups = [];
  hideLoadMore = false;
  MessageGroupWithLoader:any = {
    groups: true,
    staffs: true,
    partner:true,
    otherTenantstaff: true
  }
  ispdgs;
  UsergroupIds;
  
  configData:any = {};
  clinicalUserDetails;
  allUserDetailsInMsgGroup = [];
  messageGroups: any = 'groups';
  newMessageGroups;
  column3 = false
  memberDataGroupWise: any;
  newallUserDetailsInMsgGroup = [];
  optionShow: any = 'staff';
  searchText = '';
  searchTexts =  '';
  crossTenantOptions = [];
  crossTenantName:String;
  enableCommunicationWithInternalstaffs = false;
  internalSiteId: any;
  crossTenatsForBranch = [];
  staffUserList:any=[];
  usersListByRoleId:any;
  usersList:any;
  staffTenantFilter:any = {
    selectedTenant : null,
    tenants: [],
    filterEnabled: false,
    enabledReset: false
};
loadMoreSearchValue:any = undefined;
callFromInitialCountChatWithUserlist:boolean = false;
chatwithPageCount = {
  staffs: 0,
  partner:0
}
noMoreItemsAvailable = {
  users: false
};
chatWithUsersLoading = true;
staffLoader:any = {
  groups: true,
  staffs: true,
  partner: true,
  otherTenantstaff: true,
}

loadMoremessage = {
  users: 'Load more'
};
scheduledPrimaryCar:any;
defaultNurses:any;
clinicianLoad:any=false;
selectedTenant:any;
crossTenantOptionsChatWith = [];
selectedExistingMembers = [];
staffRolesList=[];
isloadingTime=true;   
tenantIDForInviteList;
clickedRoleId;
staffsUnderRolesList = [];
userList=[];
membersLoaded =true;
dummyCheckedRoleIds=[];
roleBasedStaffs =[];
selectedroleBasedStaffs =[];
checkedRoleIds = [];
checkedIdsWithRole=[];
removedRoleUsers=[];
MemberListByRoleWiseStaff = [];
usersInRole = true;
chatWithLoader:any = {
  groups: true,
  staffs: true,
  partner: true,
  otherTenantstaff: false,
  patients: true,
  otherTenantPatients: true
}
privileges = this._structureService.getCookie('userPrivileges');
isStaffsloadingTime = false;
clickedRole=0;
isLoadingRoles = false;
MemberListByRoleWisepartner = [];
clickedGroup;
isGrouploadingTime = false;
dummyCheckedGroupMembers=[];
messageGroupId:any;
nursing_agency_on = false;
eventsSubject: Subject<void> = new Subject<void>();
  hideSiteSelection:boolean;
  hideSiteSelectionApply:boolean;
  selectSiteId: any;
  messageSiteId: any;
  expandedRoles= [];
  singleSelection:boolean = false;
  multiSiteEnable: boolean;
  enableCrossSite: boolean;
  multiTenant: boolean;
  selectedBranchId: any;
  preventMultipleCall : boolean = true;
  apply : boolean = false;
  usersSiteId : any;
  switchSite : boolean = false;
  selectedGroupRoles = [];
  resetPdgMembers = false;
  constructor(private route: ActivatedRoute,
  private router: Router,
  private _structureService: StructureService,
  private _ToolTipService: ToolTipService,
  private _formBuild: FormBuilder,
  private MessageGroupSearchFilter: filterMessageGroupPipe,
  public _SharedService: SharedService,
  private _inboxService: InboxService,
  public modalFilter:userFilterPipe,
  public _GlobalDataShareService:GlobalDataShareService,
  private messageService: MessageService,
  private location: Location
  ) { this.configData = this._structureService.userDataConfig?JSON.parse(this._structureService.userDataConfig):{}; }

  ngOnInit() {
    this.optionShow = 'staff';
    
    this.userDetails = this._structureService.userDetails;
    this.userData = JSON.parse(this.userDetails);
    if(this._structureService.getCookie('siteCrossId')){
        this.switchSite = true;
    }
    this.usersSiteId = this._structureService.getCookie('siteCrossId') ? this._structureService.getCookie('siteCrossId') : this.userData.mySites[0].id;
  
    // if (this.userData.config.enable_nursing_agencies_visibility_restrictions == 1 && this.userData.nursing_agencies != "") {
    //   this.nursing_agency_on = true;
    //   this.optionShow = 'partner';

		// }
    // this.hideSiteSelection = this.userData.mySites.length == 1 ? true : false;
   this.multiSiteEnable = this.userData.config.enable_multisite == 1 ? true : false;
   this.enableCrossSite= this.userData.enable_cross_site == 1 ?true :false;
    if (this._structureService.getCookie('userPrivileges').indexOf('manageAllMessageGroup') != -1 || this._structureService.getCookie('userPrivileges').indexOf('manageTenants') != -1) {
      this.privilegeManageAllMessageGroup = true;
    }
    if(this.userData.organizationMasterId !=0 && this.userData.crossTenantsDetails && this.userData.crossTenantsDetails.length && this.userData.masterEnabled == "0"){
      if(
        this.userData.crossTenantsDetails.length > 1 
         && this._structureService.getCookie('userPrivileges').indexOf('allowMultipleOrganizationsStaffCommunication')!==-1
         && this.configData.allow_multiple_organization==1
         && (this.configData.enable_nursing_agencies_visibility_restrictions != 1 || (this.configData.enable_nursing_agencies_visibility_restrictions == 1 && this.userData.nursing_agencies == ""))){
        this.column3 = true;
        this.crossTenantOptions = this.userData.crossTenantsDetails;
        this.staffTenantFilter.filterEnabled = true;
      	this.staffTenantFilter.setTenantDropDown = true;
        this.crossTenantOptions.forEach((tenant)=> {
          if(tenant.id == this._structureService.getCookie("crossTenantId")) {
              this.crossTenantName = tenant.tenantName;
              this.staffTenantFilter.selectedTenant = tenant.id;
              this._GlobalDataShareService.setSelectedTenantDetails(tenant);
             this.staffTenantFilter.tenants = this.userData.crossTenantsDetails;
           }
        });
        this.crossTenantOptions.forEach((tenant) => {
          if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
              this.selectedTenant = tenant;
              this.staffTenantFilter.selectedTenant = tenant.id;
              this._GlobalDataShareService.setSelectedTenantDetails(tenant);
            this.staffTenantFilter.tenants = this.userData.crossTenantsDetails;
            this.crossTenantOptions.forEach((tenant) => {
              if (tenant.id == this._structureService.getCookie("crossTenantId")) {
                  this.crossTenantName = tenant.tenantName;
              }
          });
         }          
        });
      }else{
        this.crossTenantOptions = [];
        this.column3 = false;
      }
      
    } else if (this.userData.masterEnabled == "1" && this.userData.isMaster == "1") {
      this.column3 = true;
      this.crossTenantOptions = this.userData.crossTenantsDetails;
      this.crossTenantOptions.forEach((tenant)=> {
        if(tenant.id == this._structureService.getCookie("crossTenantId")) {
            this.crossTenantName = tenant.tenantName;
            this.staffTenantFilter.selectedTenant = tenant.id;
              this._GlobalDataShareService.setSelectedTenantDetails(tenant);
            this.staffTenantFilter.tenants = this.userData.crossTenantsDetails;
         
        }
      });
      this.crossTenantOptions.forEach((tenant) => {
        if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
          this.crossTenantName = tenant.tenantName;
          this.selectedTenant = tenant;
          this.staffTenantFilter.selectedTenant = tenant.id;
          this._GlobalDataShareService.setSelectedTenantDetails(tenant);
          this.staffTenantFilter.tenants = this.userData.crossTenantsDetails;
            this.crossTenantOptions.forEach((tenant) => {
              if (tenant.id == this._structureService.getCookie("crossTenantId")) {
                  this.crossTenantName = tenant.tenantName;
              }
          });
        }
      });

    } else if(this.userData.masterEnabled == "1" && this.userData.isMaster == "0") {
      this.column3 = true;
      this.crossTenantOptions = this.userData.crossTenantsDetails;
      this.crossTenantOptions.forEach((tenant)=> {
        if(tenant.id == this._structureService.getCookie("crossTenantId")) {
          this.crossTenantName = tenant.tenantName;
            this.staffTenantFilter.selectedTenant = tenant.id;
            this._GlobalDataShareService.setSelectedTenantDetails(tenant);
            this.selectedTenant = tenant;
            this.staffTenantFilter.tenants = this.userData.crossTenantsDetails;
            
        }
      });
      this.crossTenantOptions.forEach((tenant) => {
        if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
            this.selectedTenant = tenant;
            this.staffTenantFilter.selectedTenant = tenant.id;
            this._GlobalDataShareService.setSelectedTenantDetails(tenant);
            this.crossTenantOptions = this.userData.crossTenantsDetails;
            this.staffTenantFilter.tenants = this.crossTenantOptions;
            this.crossTenantOptions.forEach((tenant) => {
              if (tenant.id == this._structureService.getCookie("crossTenantId")) {
                  this.crossTenantName = tenant.tenantName;
              }
          });
        }
      });
      this.enableCommunicationWithInternalstaffs = true;
      this.internalSiteId = this.userData.master_details.id;
      
     
      if (this.userData.organizationMasterId != 0 
        && this.userData.crossTenantsDetails 
        && this.userData.crossTenantsDetails.length > 1) {
        this.crossTenantOptionsChatWith = this.userData.crossTenantsDetails;
        this.staffTenantFilter.tenants = this.crossTenantOptions;
        this.crossTenantOptions.forEach((tenant) => {
            if (tenant.id == this._structureService.getCookie("crossTenantId")) {
                this.crossTenantName = tenant.tenantName;
            }
        });
        if ((this.crossTenantOptions.length > 1
            && this.privileges.indexOf('allowOrganizationSwitching') != -1
            && this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication') !== -1 
            && this.configData.allow_multiple_organization == 1
            && this.userData.masterEnabled == '0'
            && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
        || (this.crossTenantOptions.length > 1 
            && this.userData.isMaster =='1' 
            && this.userData.masterEnabled == '1')
        || (this.crossTenantOptions.length > 1 
            && this.userData.isMaster =='0' 
            && this.userData.masterEnabled == '1'
            && this.userData.group !='3')
        || (this.crossTenantOptions.length > 1
            && this.privileges.indexOf('allowOrganizationSwitching') != -1
            && this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication') !== -1 
            && this.configData.allow_multiple_organization == 1
            && this.userData.masterEnabled == '0'
            && this.configData.enable_nursing_agencies_visibility_restrictions == 1
            && this.userData.nursing_agencies == "")
        ) {
           if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1') {
                this.internalSiteId = this.userData.master_details.id;
            }
            this.staffLoader.otherTenantstaff = true;
            this.staffTenantFilter.tenants = this.staffTenantFilter.tenants.filter((tenant)=> {
                if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1'){
                    if(this.internalSiteId == tenant.id) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return true;
                }
            });
            if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1') {
                if(this.userData.master_details.id != this.userData.tenantId) {
                    this.crossTenantOptions.map((tenant)=> {
                        if(tenant.id == this.userData.tenantId) {
                            this.staffTenantFilter.tenants.unshift(tenant);
                        }
                    });
                }
            }
            
        } else { 
            this.crossTenantOptionsChatWith = [];
            this.staffTenantFilter.tenants = [];
            
        }
      }
    } else {
      this.column3 = false
      this.crossTenantOptions = [];
    }
    var page = "message-groups";
    $(".refresh-page").tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGGP00001') });
    $(".new-message-group").tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGGP00002') });
    $(".make-public").tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGGP00003') });
    $(".multi-chat-thread").tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGGP00006') });
    $(".clear-btn-img").tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGGP00004') });
    $(".toolSelect").tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGGP00005') });
    $('body').on("keypress", "input[type=text]", function (e) {
      var code = (e.keyCode ? e.keyCode : e.which);
      if (code == 13) {
        e.preventDefault();
      }
    });
    
    this._structureService.previousUrl = 'message/editmessagegroup';

    if (this.userData.nursing_agencies != '') {
			this.messageGroup = this._formBuild.group({
        messageGroupName: ['', [Validators.required, this.noWhitespaceValidator]],
        nursingAgencyUserTag: [''],
      });
		} else {
			this.messageGroup = this._formBuild.group({
        messageGroupName: ['', [Validators.required, this.noWhitespaceValidator]],
        nursingAgencyUserTag: [''],        
      });
		}
   
   //  this.setChatWithTenantFilterSelect();
    setTimeout( ()=> {
      $('#staffTenantFilter').select2({
          dropdownParent: $("#staffListblock")
      });
    //  $("#staffTenantFilter").val(this.staffTenantFilter.selectedTenant).trigger("change");
      $("#staffTenantFilter").css("text-overflow", "ellipsis");
      });
    // if(this.optionShow == 'staff'){
    // this.staffUserList = [];
    // //this.loadMoreUsers(this.optionShow)
    // }
        this.getGroups(true);
       
    
    
  $('body').on('change','#staffTenantFilter',(event)=>{
    if(event.target.value)  {
    var previousSelectedTenant = this.staffTenantFilter.tenants.find((tenant) => tenant.id == this.staffTenantFilter.selectedTenant);
     var currentSelectedTenant = this.staffTenantFilter.tenants.find((tenant) => tenant.id == this.staffTenantFilter.selectedTenant);
            var activityData = {
                activityName: "Message Group " + ((this.optionShow == 'staff') ? 'Staff ' : (this.optionShow == 'partner') ? 'partner ' : ((this.optionShow == 'patient') ? 'Patient ' : ' ')) +"Tenant Switching",
                activityType: "messaging",
                activityDescription: this.userData.displayName + " (" + this.userData.userId + ") has selected tenant " + currentSelectedTenant.tenantName + '(' + currentSelectedTenant.id +')' + ((previousSelectedTenant) ? (' from tenant '+ previousSelectedTenant.tenantName + '(' + previousSelectedTenant.id +')') : ''),
                tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
            };
            this._structureService.trackActivity(activityData);
        this.filterBranch(event);
        
    }
});
  }
  emitEventToUserTags(): void {
		this.eventsSubject.next();
        }
        
   getSiteIds(data:any,siteselect=0) {
      this.selectedGroupMembers = [];
      if(this.userData.mySites && this.userData.mySites.length == 1) {
        this.selectSiteId = this.userData.mySites[0].id;
        } else {
          this.selectSiteId = data['siteId'].toString();
        }
        this.messageSiteId = this.selectSiteId;
       let existingRoles = [];
       if(!isBlank(this.checkedRoleIds)) {
        this.checkedRoleIds.forEach(val => {
          existingRoles.push(val.id)
         });
        var status = 0;
        let chosenSiteId;
        chosenSiteId = this.multiTenant ? this.selectedBranchId : this.selectSiteId;
        this.roleBasedStaffs = [];
        var isMultiple = existingRoles.length > 1 ? true : false;
        this._structureService.getRoleBasedStaffs(existingRoles.toString(), status,1,false,this.staffTenantFilter.selectedTenant, chosenSiteId, isMultiple).then(( data ) => {
          this.membersLoaded =true;
          let parsedResponceData = JSON.parse(JSON.stringify(data));
        this.selectedroleBasedStaffs = parsedResponceData.getSessionTenant['roleBasedStaffs'];
        this.addMemberToList(this.checkedRoleIds); 
      });
       }
      
     if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles'){
      this.usersInRole=true;
      this.expandedRoles.forEach((roleID) => {
        $(".expand-icon-" + roleID).removeClass("fa-minus");
        $(".expand-icon-" + roleID).addClass("fa-plus");
        $(".sub-item-panel-" + roleID).removeClass("showall");
        $(".sub-item-panel-" + roleID + " li").remove();

      })
     
    } else if(this.optionShow=='groups' || this.optionShow=='msggroups') {
      this.expandedRoles.forEach((roleID) => {
        $(".expand-icon-" + roleID).removeClass("fa-minus");
        $(".expand-icon-" + roleID).addClass("fa-plus");
        $(".sub-item-panel-" + roleID).removeClass("showall");
        $(".sub-item-panel-" + roleID + " li").remove();

      })
    }
    //Update new members/roles list on site select
    if(this.multiSiteEnable && !this.enableCrossSite && (this.optionShow !== "staffroles" && this.optionShow !== "partnerroles")){ 
      this.search(this.optionShow);
    } else if(this.multiSiteEnable && this.enableCrossSite && (this.optionShow === "msggroups")) {
      this.search(this.optionShow);
    }
}
	
    getSiteIdsApply(data: any, siteselect = 0) {
        this.selectSiteId = data['siteId'].toString();
        let existingRoles = [];
        if (!isBlank(this.checkedRoleIds)) {
            this.checkedRoleIds.forEach(val => {
                existingRoles.push(val.id)
            });
            var status = 0;
            let chosenSiteId;
            chosenSiteId = this.multiTenant ? this.selectedBranchId : this.selectSiteId;
            this.roleBasedStaffs = [];
            var isMultiple = existingRoles.length > 1 ? true : false;
            this._structureService.getRoleBasedStaffs(existingRoles.toString(), status, 1, false, this.staffTenantFilter.selectedTenant, chosenSiteId, isMultiple).then((data) => {
                this.membersLoaded = true;
                let parsedResponceData = JSON.parse(JSON.stringify(data));
                this.selectedroleBasedStaffs = parsedResponceData.getSessionTenant['roleBasedStaffs'];
                this.addMemberToList(this.checkedRoleIds);
            });
        }

        if (this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
            this.usersInRole = true;
            this.expandedRoles.forEach((roleID) => {
                $(".expand-icon-" + roleID).removeClass("fa-minus");
                $(".expand-icon-" + roleID).addClass("fa-plus");
                $(".sub-item-panel-" + roleID).removeClass("showall");
                $(".sub-item-panel-" + roleID + " li").remove();

            })

        } else if (this.optionShow == 'groups' || this.optionShow == 'msggroups') {
            this.expandedRoles.forEach((roleID) => {
                $(".expand-icon-" + roleID).removeClass("fa-minus");
                $(".expand-icon-" + roleID).addClass("fa-plus");
                $(".sub-item-panel-" + roleID).removeClass("showall");
                $(".sub-item-panel-" + roleID + " li").remove();

            })
        }
        if(!this.preventMultipleCall && this.apply){
           
            if(this.selectSiteId != '0'){
                this.preventMultipleCall = true;
            }
        }
     if(this.preventMultipleCall && this.apply){
            this.search(this.optionShow);
            this.preventMultipleCall = false;
     }
     this.apply = true;
        
    }
      getBranchIds(data:any) {
        if(this.optionShow !='groups' && this.optionShow !='msggroups' && this.staffTenantFilter.filterEnabled && this.staffTenantFilter.tenants.length && !this.multiSiteEnable) {
        this.multiTenant = true;
        this.selectedBranchId = data['siteId'].toString();
        if(!this.preventMultipleCall){
            this.search(this.optionShow);
        }
        this.preventMultipleCall = false;
        }
      }
      hideDropdown(hideItem : any){
        this.hideSiteSelection = hideItem.hideItem;
        }
        hideDropdownApply(hideItem : any){
            this.hideSiteSelectionApply = hideItem.hideItem;
            }
  checkboxChanged(event,indexing) {
    this.addMemberError = false;
    var $this = $(event.target),
      checked = $this.prop("checked"),
      container = $this.parent(),
      siblings = container.siblings();
      if(event.target.name=="messageGroup"){
        this.clickedGroup=String(event.target.value);
        this.membersLoaded =false;
        if(event.target.checked){
          var value= JSON.parse(event.target.value);
          var messageGroupId =String(value);
          this.messageGroupId=messageGroupId;        
          if(this.dummyCheckedGroupMembers.indexOf(messageGroupId)==-1){
            this.dummyCheckedGroupMembers.push(+messageGroupId);
          }
          if(this.dummyCheckedGroupMembers && this.dummyCheckedGroupMembers.length > 0){
            this.membersLoaded = true;
          }
        } else {
            var value= JSON.parse(event.target.value);
            var messageGroupId =String(value);  
            let index = this.dummyCheckedGroupMembers.indexOf(messageGroupId);
            this.dummyCheckedGroupMembers.splice(index, 1);     
            this.membersLoaded =true;
        }
      }
      if(event.target.name=="staffRoleWise"){
        var value= JSON.parse(event.target.value);
        if(event.target.checked){
          if(this.dummyCheckedRoleIds.indexOf(value.id)==-1)
            this.dummyCheckedRoleIds.push(value.id);
           var  roleID =value.id;
           this.clickedRole=roleID;
        }else{
          let index = this.dummyCheckedRoleIds.indexOf(value.id);
          this.dummyCheckedRoleIds.splice(index, 1);
          var  roleID =value.id;
        //  this.dummyCheckedRoleIds.forEach((value)=>{
          
          this.selectedroleBasedStaffs = this.selectedroleBasedStaffs.filter(function (selectedRole) {             
            return selectedRole.selectedRoleid != roleID;
          });
          this.newMemberListByRoleWise[indexing]['userList'] = [];
            //  });
          this.membersLoaded =true;

        }
      }

    container.find('input[type="checkbox"]')
      .prop({
        indeterminate: false,
        checked: checked
      })
      .siblings('label')
      .removeClass('custom-checked custom-unchecked custom-indeterminate')
      .addClass(checked ? 'custom-checked' : 'custom-unchecked');

    this.checkSiblings(container, checked);
  }

  checkSiblings($el, checked) {
    var parent = $el.parent().parent(),
      all = true,
      indeterminate = false;

    $el.siblings().each(function () {
      return all = ($(this).children('input[type="checkbox"]').prop("checked") === checked);
    });

    if (all && checked) {
      parent.children('input[type="checkbox"]')
        .prop({
          indeterminate: false,
          checked: checked
        })
        .siblings('label')
        .removeClass('custom-checked custom-unchecked custom-indeterminate')
        .addClass(checked ? 'custom-checked' : 'custom-unchecked');

      this.checkSiblings(parent, checked);
    }
    else if (all && !checked) {
      indeterminate = parent.find('input[type="checkbox"]:checked').length > 0;

      parent.children('input[type="checkbox"]')
        .prop("checked", checked)
        .prop("indeterminate", indeterminate)
        .siblings('label')
        .removeClass('custom-checked custom-unchecked custom-indeterminate')
        .addClass(indeterminate ? 'custom-indeterminate' : (checked ? 'custom-checked' : 'custom-unchecked'));

      this.checkSiblings(parent, checked);
    } else {
      $el.parents("li").children('input[type="checkbox"]')
        .prop({
          indeterminate: true,
          checked: false
        })
        .siblings('label')
        .removeClass('custom-checked custom-unchecked custom-indeterminate')
        .addClass('custom-indeterminate');
    }
  }

  callAccordion(roleID, eve, list) {
    if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
      this.isStaffsloadingTime = true;
      this.clickedRole=roleID;
    }
    if(this.optionShow == 'msggroups' || this.optionShow ==='groups') {
      this.isGrouploadingTime = true;
      this.clickedGroup=roleID;
    }
    if ($(".expand-icon-" + roleID).hasClass("fa-plus")) { 
      if (this.expandedRoles.length>0) {
        this.expandedRoles.map((elem, i) => {
          if( elem != roleID) {
            this.expandedRoles.push(roleID)
          }
        })
      } else {
        this.expandedRoles.push(roleID); 

      }
      if(list === "selectedRoles"){
				this.isStaffsloadingTime = true;
				this.getRoleBasedStaff(roleID,eve,true,list);
				this.clickedRole=roleID;
			} else if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles'){
        this.getRoleBasedStaff(roleID,eve,true);

      } else if(this.optionShow == 'msggroups' || this.optionShow ==='groups'){
        const groupType =  this.optionShow === 'groups' ? GroupType.PATIENTGROUP : GroupType.MSGGROUP;
        this.getGroupMembersByGroupId(roleID, true, groupType);
      }  
      $(".expand-icon-" + roleID).removeClass("fa-plus");
      $(".expand-icon-" + roleID).addClass("fa-minus");
      $(".sub-item-panel-" + roleID).addClass("showall");
    } else {
      this.isStaffsloadingTime = false;
      this.isGrouploadingTime = false;
      this.usersInRole = true;

      $(".expand-icon-" + roleID).removeClass("fa-minus");
      $(".expand-icon-" + roleID).addClass("fa-plus");
      $(".sub-item-panel-" + roleID).removeClass("showall");
    }
  }


  searchOnKeyPress(event){
  var search_key_word=$("#userSearchTxtRoles").val().trim().toLowerCase();
  var key = event.keyCode || event.charCode;
  if( key == 8 || key == 46 ){
    
        $("li.roleslisting").filter(function() {
            $(this).toggle($(this).find("label").text().toLowerCase().indexOf(search_key_word) > -1);
            
          });
          if($("li.roleslisting").find("label").text().toLowerCase().indexOf(search_key_word)==-1) $("#notFoundRoles").show();
            else $("#notFoundRoles").hide();
  }else{
    $("li.roleslisting").filter(function() {
        $(this).toggle($(this).find("label").text().toLowerCase().indexOf(search_key_word) > -1)
        
      });
      if($("li.roleslisting").find("label").text().toLowerCase().indexOf(search_key_word)==-1) $("#notFoundRoles").show();
      else $("#notFoundRoles").hide();
      
  }
}
  getRoleBasedStaff(roleId,eve,flagmembersappend=false, list=''){
    this.usersInRole=true;
    let chosenSiteId;
    const roleList = list === 'selectedRoles' ? this.checkedRoleIds : this.newMemberListByRoleWise;
    if (!('userList' in roleList[eve])) {
      roleList[eve]['userList'] = [];
    }
    this.membersLoaded =false;
    chosenSiteId = this.multiTenant ? this.selectedBranchId : this.selectSiteId;
    this._structureService.getRoleBasedStaffs(roleId, 0,1,false,this.staffTenantFilter.selectedTenant, chosenSiteId).then(( data ) => {
      this.membersLoaded =true;
      let parsedResponceData = JSON.parse(JSON.stringify(data));
      this.roleBasedStaffs = parsedResponceData.getSessionTenant['roleBasedStaffs'];
      if(this.roleBasedStaffs == null){
        this.roleBasedStaffs = [];
        this.usersInRole=false;
      }
      if(this.configData.enable_nursing_agencies_visibility_restrictions == '1' && this.userData.nursing_agencies != '' && this.configData.na_staffs_chat_with_pharmacy == '0'){
        var nur_login = this.userData.nursing_agencies.split(',');
        nur_login.forEach(element => {                
          this.roleBasedStaffs = this.roleBasedStaffs.filter(function (members) {
            if(members.naTags != null && members.naTags != '' && members.naTags.split(',').indexOf(element) != -1){
              return true;                   
            }
          });             
        });
        if(this.roleBasedStaffs.length == 0){
          this.usersInRole=false;
        }
      } else if(this.configData.enable_nursing_agencies_visibility_restrictions == '1' && this.userData.nursing_agencies != '' && this.configData.na_staffs_chat_with_pharmacy == '1'){
        var nur_login = this.userData.nursing_agencies.split(',');
        nur_login.forEach(element => {                
          this.roleBasedStaffs = this.roleBasedStaffs.filter(function (members) {
            if(members.naTags == null || members.naTags == '' || members.naTags.split(',').indexOf(element) != -1){
              return true;                   
            } else {
              return false;
            }
          });           
        });
      }
      this.roleBasedStaffs.forEach(value => {  
        value.selectedRoleid = roleId; 
      });
      if(roleList[eve]['userList'] && roleList[eve]['userList'].length == 0){  
        this.roleBasedStaffs.forEach(value => {  
          value.selectedRoleid = roleId; 
          var user = { id: value.id, name: value.displayName, selectedRoleid:value.selectedRoleid, naTags:value.naTags,naTagNames:value.naTagNames};
          roleList[eve]['userList'].push(user);                   
        });
      } else {
        roleList[eve]['userList'] = [];
        this.roleBasedStaffs.forEach(value => {  
          value.selectedRoleid = roleId; 
          var user = { id: value.id, name: value.displayName, selectedRoleid:value.selectedRoleid, naTags:value.naTags,naTagNames:value.naTagNames};
          roleList[eve]['userList'].push(user);                  
        }); 
      }   
      if(!flagmembersappend){            
        this.selectedroleBasedStaffs = [...this.selectedroleBasedStaffs, ...this.roleBasedStaffs];
      }    
      if(list === 'selectedRoles'){
        this.checkedRoleIds = roleList;
      } else {
        this.newMemberListByRoleWise = roleList;
      }
      this.isStaffsloadingTime = false;
    });   
  }
      getGroupMembersByGroupId(groupId, choose=false, groupType) {
        const groupIds = typeof(groupId) === 'string' ? [+groupId] : groupId;
        let parameter : GroupsListingRequest = {
          data: {
            groupIds: groupIds,
          }
        };
        if(groupType === GroupType.PATIENTGROUP) {
          const data: any = { patients: groupIds };
          parameter = {
            data
          };
        }
        parameter['siteIds'] = this.getSelectedSiteIds();
        this.messageService.fetchGroupData(parameter, groupType).subscribe((messageGroups) => {
          if(messageGroups && messageGroups.data) {
            const groupMembers = messageGroups.data["memberDetails"];
            const groupRoles = messageGroups.data["selectedRoles"] || [];
            if(groupMembers && groupMembers.length){
              groupMembers.sort((a, b) => {
                  if (a.displayName.toLowerCase() < b.displayName.toLowerCase()) return -1;
                  if (a.displayName.toLowerCase() > b.displayName.toLowerCase()) return 1;
                  return 0;
              });
            }
            if(!choose) {
              this.selectedGroupRoles.push(...groupRoles);
              const selectedRoleIds = [...this.selectedGroupRoles.map(x=> (+x.roleId)), ...this.checkedRoleIds.map(x=> +(x.id))];
              let memberDetails = groupMembers.filter(obj => {
                const roles = JSON.parse(obj.roleIds) as number[];
                return !roles.some(role => selectedRoleIds.includes(+role));
              });
              if(groupMembers.length !== memberDetails.length) {
                this._structureService.notifyMessage({
                  messge: this._ToolTipService.getTranslateData('WARNING.MEMBER_WITH_SAME_ROLE_EXISTS'),
                  type: CONSTANTS.notificationTypes.warning
                });
              }
              this.selectedGroupMembers.push(...memberDetails);
            }
            this.isGrouploadingTime = false;
            if ((groupMembers && groupMembers.length) || (groupRoles && groupRoles.length)) {
              if (typeof groupId === "object" && groupId && groupId.length) {
                  this.addMemberToList(groupId);
              } else {
                let selectedMessageGroupIndex = this.memberDataGroupWise.findIndex(group => group.groupId === String(groupId));
                if(groupType === GroupType.PATIENTGROUP) {
                  selectedMessageGroupIndex = this.memberDataGroupWise.findIndex(group => group.patientId === String(groupId));
                }
                this.memberDataGroupWise[selectedMessageGroupIndex].userList = groupMembers;
                this.memberDataGroupWise[selectedMessageGroupIndex].selectedRoles = messageGroups.data["selectedRoles"] || [];
              }
              this.membersLoaded = true;              
            }
          }
        });      
      }
  showData(data) {
    this.optionShow = data;
    this.dummyCheckedGroupMembers=[];
    this.searchText = this.searchTexts =  '';
    if(data=='groups' || data=='msggroups') {
      this.reset(this.optionShow);
    }
    if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {  
      this.chatWithLoader.otherTenantstaff = true;

      this.setChatWithTenantFilterSelect();
      // this.staffTenantFilter.filterEnabled = true;
       this.getStaffRoles(this.optionShow);
  } 
  }
  getStaffRoles(optionShow='') { 
    /* if (this._structureService.messageGroupsWithPrivilege && this._structureService.messageGroupsWithPrivilege.length) {
      console.log("Enter first ifffffffff");
      console.log(this._structureService.messageGroupsWithPrivilege);
      this.setmessageGroup(this._structureService.messageGroupsWithPrivilege); */
    //} else {
     //this.getAllMessageGroupDetails(this.userData.tenantId, this.userData.userId);
    //}
    var selectedtenant = this.staffTenantFilter.selectedTenant ? this.staffTenantFilter.selectedTenant :this.userData.tenantId;
    var selectedTenantName = this.staffTenantFilter.selectedTenantName ? this.staffTenantFilter.selectedTenantName :this.userData.tenantName;
    this.staffList = [];
   
  this._inboxService.getStaffRolesListByTenantId(selectedtenant,optionShow,this.selectedBranchId).then((data)=> {
      if (data) {
        this.isLoadingRoles = false;

        //this.groupList = data['getSessionTenant'].messageGroupsPagination.data;

       // this.groupListNames = this.groupList;
       this.staffRolesList = Array.isArray(data)?data:[];
       this.staffRolesList.forEach(value => {
        var staffrollData = { id: value.RoleID, displayName: value.RoleName, tenantId:value.tenant_id, tenantName:value.TenantName, citusRoleId:value.citus_role_id, siteId:value.siteId, siteName:value.siteName};
        this.staffList.push(staffrollData);
       });
        this.staffList.slice();       
        this.newMemberListByRoleWise = [];       
        this.newMemberList = [];     
        if(this.staffList.length > 0){
          this.staffList.forEach(value => {
            if (value.id) {
              this.newMember = {
                id: "",
                displayName: "",
                role: {},
                tenantId: null,
                tenantName: "",
                otherTenantStaff: ((value.tenantId && (this.userData.tenantId != value.tenantId)) ? true : false)
              }
              
              this.newMember.tenantId = value.tenantId;
              this.newMember.tenantName = value.tenantName;              
              var roleData = { id: value.id, name: value.displayName, tenantId:value.tenantId, tenantName:value.tenantName, tenantRoleId:value.tenantId, citusRoleId:value.citusRoleId,siteId:value.siteId, siteName:value.siteName};            
              if (!this.newMemberListByRoleWise[value.id]) {
                this.newMemberListByRoleWise[value.id] = {};
              }
              this.newMemberListByRoleWise[value.id]['roleData'] = roleData;
              this.newMemberListByRoleWise[value.id]['tenantId'] = value.tenantId;
              this.newMemberListByRoleWise[value.id]['tenantName'] = value.tenantName;              
            }
          });
        }  
        this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter( function(a) {
          return (a && a.tenantId);
        });
        
       
        if(optionShow == 'staffroles'){
          this.MemberListByRoleWiseStaff =this.newMemberListByRoleWise;
        } else if(optionShow == 'partnerroles'){
          this.MemberListByRoleWisepartner =this.newMemberListByRoleWise;
        }
          if(this.checkedRoleIds.length){
          this.checkedRoleIds.forEach((value)=>{   
           this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter(function (members) {
          return members.roleData.id != value.id;
          });
        });
        }

        this.newMemberListByRoleWise.sort(function (a, b) {
          if (a.roleData.name < b.roleData.name) return -1;
          if (a.roleData.name > b.roleData.name) return 1;
          return 0;
        });       
        this.chatWithLoader.otherTenantstaff = false;

      } else {
        this.chatWithLoader.otherTenantstaff = false;

        this._structureService.deleteCookie('authenticationToken');
        this.router.navigate(['/login']);
      }
    });
  }
  showDataStaff(data){
    this.staffUserList = [];
    this.clinicalUserDetails = [];
    this.usersList = [];
    this.srch.nativeElement.value = "";
    var initialData = data;
    var tabSelectedFrom = '';
    if(data) {
        tabSelectedFrom = this.optionShow;
    }
    this.staffTenantFilter.enabledReset = true;
   
    this.optionShow=data;
    
    if(this.optionShow == 'staff' || this.optionShow == 'partner') {
        this.staffTenantFilter.enabledReset = false;
        this.setChatWithTenantFilterSelect();
    }
    if(this.optionShow=='staff' || this.optionShow == 'partner'){
        this.initialiseStaffOrPatientList(this.optionShow);
    }     
    var activityData = {
        activityName: "Add Message Group With Tab Switching",
        activityType: "messaging",
        activityDescription: this.userData.displayName + " (" + this.userData.userId + ") has selected tab " + this.optionShow + ((tabSelectedFrom) ? (' from tab '+ tabSelectedFrom) : ''),
        tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
    };
    this._structureService.trackActivity(activityData);
}
filterBranch(event){
  $("#userSearchTxtRoles").val('');
  $("#notFoundRoles").hide();
  if(event.target.value)  {
    var previousSelectedTenant = this.staffTenantFilter.tenants.find((tenant) => tenant.id == this.staffTenantFilter.selectedTenant);
    this.staffTenantFilter.selectedTenant = event.target.value;
    this.staffTenantFilter.selectedTenantName = event.target.selectedOptions.text;
    if(this.staffTenantFilter.filterEnabled) {
      if(this.optionShow =='staff' || this.optionShow == 'partner'){
        this.reset(this.optionShow);
      } 
      if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
        this.chatWithLoader.otherTenantstaff =true;
        this.isLoadingRoles = true;
        this.getStaffRoles(this.optionShow);
    } 
       
        var currentSelectedTenant = this.staffTenantFilter.tenants.find((tenant) => tenant.id == this.staffTenantFilter.selectedTenant);
        var activityData = {
            activityName: "Chat With " + ((this.optionShow == 'staff') ? 'Staff ' : (this.optionShow == 'partner') ? 'partner' : ((this.optionShow == 'patient') ? 'Patient ' : ' ')) +"Tenant Switching",
            activityType: "messaging",
            activityDescription: this.userData.displayName + " (" + this.userData.userId + ") has selected tenant " +  ((currentSelectedTenant && currentSelectedTenant.tenantName) ? currentSelectedTenant.tenantName + '(' + currentSelectedTenant.id +')' + ((previousSelectedTenant) ? (' from tenant '+ previousSelectedTenant.tenantName + '(' + previousSelectedTenant.id +')') : '') : ''),
            tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
        };
        this._structureService.trackActivity(activityData);
    } else {
        this.staffTenantFilter.enabledReset = true;
    }
}



}

initialiseStaffOrPatientList(optionShow) {
  if(this.staffTenantFilter.setTenantDropDown && this.staffTenantFilter.selectedTenant) {
      this.staffTenantFilter.setTenantDropDown = false;
      setTimeout( ()=> {
        $('#staffTenantFilter').select2({
            dropdownParent: $("#staffListblock")
        });
       // $("#staffTenantFilter").val(this.staffTenantFilter.selectedTenant).trigger("change");
        $("#staffTenantFilter").css("text-overflow", "ellipsis");
    });
     
  }
  this.loadMoreSearchValue = undefined;
  this.callFromInitialCountChatWithUserlist = true;
  this.chatwithPageCount = {
      staffs: 0,
      partner:0
  }
  this.noMoreItemsAvailable = {
      users: false
  };
  this.staffUserList = [];
  this.clinicalUserDetails = [];
  this.usersList = [];
  var isRoleAvailable = true;
  var clinicianRolesAvaiable = null;
  var setCliniciansRoleAvailableResponse;
   
      if(isRoleAvailable) {
          this.getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,true,false, optionShow, undefined)
      } else {
          this.chatWithUsersLoading = false;
      }
  
}

getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,init, loadMore, optionShow, searchValue) {
  var self=this;
  let chatWithFilterTenantId = null;
  let chosenSiteId;
  this.UsergroupIds = clinicianRolesAvaiable;
  if(init) {
    if(optionShow=='staff') {
        this.staffLoader.staffs = true;
        this.UsergroupIds = 3; 
    }
    if(optionShow=='partner') {
        this.staffLoader.partner = true; 
        this.UsergroupIds = 20;
    }
  }else{
    if(optionShow=='staff') { 
      this.UsergroupIds = 3;
    }
    if(optionShow=='partner') { 
        this.UsergroupIds = 20;
    }
  }
  if(optionShow == 'staff' || optionShow == 'partner') {
      chatWithFilterTenantId = this.staffTenantFilter.selectedTenant;
  }
  this.loadMoremessage.users = "Loading ....";
  chosenSiteId = this.multiTenant ? this.selectedBranchId : this.selectSiteId;
  this._inboxService.getUsersListByRoleId((this.UsergroupIds ? this.UsergroupIds : 3), 0, null,null,(init ? 0 : ((optionShow == 'staff') ? this.chatwithPageCount.staffs : (optionShow == 'partner') ? this.chatwithPageCount.partner : '')),((optionShow == 'staff' && !clinicianRolesAvaiable) ? true : ((optionShow == 'staff' && clinicianRolesAvaiable) ? undefined : false)),searchValue, chatWithFilterTenantId,undefined,'',optionShow, chosenSiteId).then((data:any) => {
   // this.preventMultipleCall = false;
    this.chatWithUsersLoading = false;
      this.loadMoreSearchValue = searchValue;
      if(loadMore) {
          this.clinicalUserDetails = [...this.clinicalUserDetails, ...data];
      } else {
          this.clinicalUserDetails = data;
      }
      if(data.length != 20)
          this.noMoreItemsAvailable.users = true;
  
      if(optionShow=='staff') {
          this.chatwithPageCount.staffs += 1;
      }
      if(optionShow=='partner') {
          this.chatwithPageCount.partner += 1;
      }
  
      for(var _i=0;_i < this.clinicalUserDetails.length;_i++){
          this.clinicalUserDetails[_i].isScheduled=1;
          if(this._inboxService.scheduleSelectionFilter(this.clinicalUserDetails[_i])){
              this.clinicalUserDetails[_i].isScheduled = 1;
          }else{
              this.clinicalUserDetails[_i].isScheduled = 0;
          }
      }

      this.loadMoremessage.users = "Load more";        
      this.staffUserList = this.modalFilter.transform(this.clinicalUserDetails, optionShow);
      
      // if(this.selectedGroupMembers.length != 0){
      // this.selectedGroupMembers.forEach(element => {       
      //   var user = self.staffUserList.find((user) => user.userid === element.id);
      //   if (user.userid != '') {
      //    this.staffUserList = this.staffUserList.filter(function (members) {
      //     return members.userid != user.userid;
      //         });
      //       }        
          
      //     });
      //    }
         if(this.selectedGroupMembers.length != 0){

         this.staffUserList = this.staffUserList.filter(
          (x) => !this.selectedGroupMembers.some((y) => y.id == x.userid)
        ); 
         }

      if(init) {
       if(optionShow=='staff') {
              this.staffLoader.staffs = false;
          }
        if(optionShow=='partner') {
            this.staffLoader.partner = false;
        }
      }
  }).catch((ex) => {
      if(init) {
          if(optionShow=='staff') {
              this.staffLoader.staffs = false;
          }
          if(optionShow=='partner') {
            this.staffLoader.partner = false;
        }
      }
  });
  }



setChatWithTenantFilterSelect() {
  if(this.selectedTenant) {
      this.staffTenantFilter.selectedTenant =  this.selectedTenant.id;
  } else {
      this.staffTenantFilter.selectedTenant = this._structureService.getCookie('tenantId');
  }
  switch(this.optionShow) {
      case 'staff': {
          if((this.userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
          && this.configData.allow_multiple_organization==1 
          && this.userData.crossTenantsDetails.length > 1 
          && this.userData.masterEnabled == '0'
          && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
      || (this.userData.masterEnabled == '1' 
          && this.userData.isMaster == '1')
      || (this.userData.masterEnabled == '1' 
          && this.userData.isMaster == '0'
          && this.userData.group !='3')
      || (this.userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
          && this.configData.allow_multiple_organization==1 
          && this.userData.crossTenantsDetails.length > 1 
          && this.userData.masterEnabled == '0'
          && this.configData.enable_nursing_agencies_visibility_restrictions == '1'
          && this.userData.nursing_agencies == '')) {
             this.assignChatWithTenantFilterData(); 
          } else {
              this.staffTenantFilter.filterEnabled = false;
              this.staffTenantFilter.tenants = [];
          }
          break;
      }
      case 'partner': {
        if((this.userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
        && this.configData.allow_multiple_organization==1 
        && this.userData.crossTenantsDetails.length > 1 
        && this.userData.masterEnabled == '0'
        && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
    || (this.userData.masterEnabled == '1' 
        && this.userData.isMaster == '1')
    || (this.userData.masterEnabled == '1' 
        && this.userData.isMaster == '0'
        && this.userData.group !='3')
    || (this.userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
        && this.configData.allow_multiple_organization==1 
        && this.userData.crossTenantsDetails.length > 1 
        && this.userData.masterEnabled == '0'
        && this.configData.enable_nursing_agencies_visibility_restrictions == '1'
        && this.userData.nursing_agencies == '')) {
           this.assignChatWithTenantFilterData(); 
        } else {
            this.staffTenantFilter.filterEnabled = false;
            this.staffTenantFilter.tenants = [];
        }
        break;
    }
      case 'staffroles': {
        if((this.userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
        && this.configData.allow_multiple_organization==1 
        && this.userData.crossTenantsDetails.length > 1 
        && this.userData.masterEnabled == '0'
        && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
    || (this.userData.masterEnabled == '1' 
        && this.userData.isMaster == '1')
    || (this.userData.masterEnabled == '1' 
        && this.userData.isMaster == '0'
        && this.userData.group !='3')
    || (this.userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
        && this.configData.allow_multiple_organization==1 
        && this.userData.crossTenantsDetails.length > 1 
        && this.userData.masterEnabled == '0'
        && this.configData.enable_nursing_agencies_visibility_restrictions == '1'
        && this.userData.nursing_agencies == '')) {
           this.assignChatWithTenantFilterData(); 
        } else {
            this.staffTenantFilter.filterEnabled = false;
            this.staffTenantFilter.tenants = [];
        }
        break;
    } 
    case 'partnerroles': {
      if((this.userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
      && this.configData.allow_multiple_organization==1 
      && this.userData.crossTenantsDetails.length > 1 
      && this.userData.masterEnabled == '0'
      && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
  || (this.userData.masterEnabled == '1' 
      && this.userData.isMaster == '1')
  || (this.userData.masterEnabled == '1' 
      && this.userData.isMaster == '0'
      && this.userData.group !='3')
  || (this.userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
      && this.configData.allow_multiple_organization==1 
      && this.userData.crossTenantsDetails.length > 1 
      && this.userData.masterEnabled == '0'
      && this.configData.enable_nursing_agencies_visibility_restrictions == '1'
      && this.userData.nursing_agencies == '')) {
         this.assignChatWithTenantFilterData(); 
      } else {
          this.staffTenantFilter.filterEnabled = false;
          this.staffTenantFilter.tenants = [];
      }
      break;
  }     

      default: {
          this.staffTenantFilter.filterEnabled = false;
          this.staffTenantFilter.tenants = [];
          break;
      } 
  }
}

assignChatWithTenantFilterData() {
  this.staffTenantFilter.filterEnabled = true;

  if($("#staffTenantFilter").length) {
      setTimeout( ()=> {
          $('#staffTenantFilter').select2({
              dropdownParent: $("#staffListblock")
          });
          $("#staffTenantFilter").css("text-overflow", "ellipsis");
         if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles'){
       //  $("#staffTenantFilter").val(this.staffTenantFilter.selectedTenant).trigger("change");
         }
          
      });
  } else {
      this.staffTenantFilter.setTenantDropDown = true;
  }
}

resetStaff(optionShow) {
  this.srch.nativeElement.value = "";
  if(optionShow == 'staff' || optionShow == 'partner') {
  
      this.loadMoreSearchValue = undefined;
      this.callFromInitialCountChatWithUserlist = true;
      this.chatwithPageCount = {
          staffs: 0,
          partner:0
      }
      this.noMoreItemsAvailable = {
          users: false
      };
      this.staffUserList = [];
      this.clinicalUserDetails = [];
      this.usersList = [];
      this.loadMoreUsers(optionShow)
  }
  
}

searchStaff(optionShow) {
  if(optionShow == 'staff' || optionShow == 'partner') {      
      this.callFromInitialCountChatWithUserlist = true;
      this.chatwithPageCount = {
          staffs: 0,
          partner: 0
      }
      this.noMoreItemsAvailable = {
          users: false
      };
      this.staffUserList = [];
      this.clinicalUserDetails = [];
      this.usersList = [];
      this.loadMoreUsers(optionShow, this.srch.nativeElement.value.trim())
  } 
}

loadMoreUsers(optionShow, searchKeyword=undefined, notInit=false) {
  if(!this.loadMoreSearchValue) {
      if(this.srch.nativeElement.value.trim()){
          this.loadMoreSearchValue = undefined;
          this.callFromInitialCountChatWithUserlist = true;
          this.chatwithPageCount = {
              staffs: 0,
              partner: 0
          }
          this.noMoreItemsAvailable = {
              users: false
          };
          notInit = false;
          this.staffUserList = [];
          this.clinicalUserDetails = [];
          this.usersList = [];
          searchKeyword = this.srch.nativeElement.value.trim();
      }
  } else if(this.loadMoreSearchValue && !this.srch.nativeElement.value.trim()) {
      this.reset(optionShow)
      return false;
  } else if(this.loadMoreSearchValue == this.srch.nativeElement.value.trim()) {
  } else {
      this.loadMoreSearchValue = undefined;
      this.callFromInitialCountChatWithUserlist = true;
      this.chatwithPageCount = {
          staffs: 0,
          partner:0
      }
      this.noMoreItemsAvailable = {
          users: false
      };
      notInit = false;
      this.staffUserList = [];
      this.clinicalUserDetails = [];
      this.usersList = [];
      searchKeyword = this.srch.nativeElement.value.trim();
  }
  var isRoleAvailable = true;
  var clinicianRolesAvaiable = null;
  var setCliniciansRoleAvailableResponse;
  
      if(isRoleAvailable) {
          this.getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,(!notInit ? true : false),(!notInit ? false : true), optionShow,searchKeyword)
      } else {
          this.chatWithUsersLoading = false;
      }
  

}

  removeDuplicates( arr, prop ) {
    let obj = {};
    return Object.keys(arr.reduce((prev, next) => {
      if(next && !obj[next[prop]]) obj[next[prop]] = next; 
      return obj;
    }, obj)).map((i) => obj[i]);
  }

  initGroups(){
    this.offset = 0;
    this.allMessageGroups = [];
    this.memberDataGroupWise = [];
    this.hideLoadMore = false;
    this.prevText = "";
}

reset(optionShow ='groups') {
  this.srch.nativeElement.value = "";
  if(optionShow == 'groups' || optionShow == 'msggroups') {
	    this.initGroups();    
	    this.searchTexts = "";
	    this.srch.nativeElement.value = "";
	    this.getAllMessageGroupDetails("",optionShow);
	  } else if(optionShow == 'staff' || optionShow == 'partner') { 
	    this.loadMoreSearchValue = undefined;
	    this.callFromInitialCountChatWithUserlist = true;
	    this.chatwithPageCount = {
          staffs: 0,
          partner:0
	    }
	    this.noMoreItemsAvailable = {
	        users: false
	    };
	    this.staffUserList = [];
	    this.clinicalUserDetails = [];
	    this.usersList = [];
	    this.loadMoreUsers(optionShow)
	}
}


// searchOnEnter(event) {
//   if(event.keyCode == 13) {
//       if(this.searchText){
//           this.search();
//       }
//   }
//  }

search(optionShow='groups') {
  if(optionShow === 'msggroups') {
        this.initGroups();
        if(this.srch.nativeElement.value.trim() || !isBlank(this.selectSiteId)|| this.multiTenant){
            this.getAllMessageGroupDetails(this.srch.nativeElement.value.trim(),optionShow);    
        }
    }

    if (this.optionShow === 'staffroles' || this.optionShow === 'partnerroles') {
        this.getStaffRoles(this.optionShow);
    } else if (optionShow === 'staff' || optionShow === 'partner') {
        this.callFromInitialCountChatWithUserlist = true;
        this.chatwithPageCount = {
            staffs: 0,
            partner: 0
        }

        this.noMoreItemsAvailable = {
            users: false
        };
        this.staffUserList = [];
        this.clinicalUserDetails = [];
        this.usersList = [];
        this.loadMoreUsers(optionShow, this.srch.nativeElement.value.trim())
    }  
}



loadMoreGroups(optionShow='') {
  if(!this.prevText) {
    if(this.srch.nativeElement.value.trim()){
    this.search();      
    } else {
    this.offset = this.offset+25;
    this.getAllMessageGroupDetails('',optionShow);
    }
    } else if(this.prevText && !this.srch.nativeElement.value.trim()) {
        this.reset(optionShow);
    } else if(this.prevText == this.srch.nativeElement.value.trim()) {
        this.offset = this.offset+25;
        this.getAllMessageGroupDetails(this.srch.nativeElement.value.trim(),optionShow);
    } else {
        this.search();
    }
}
  getAllMessageGroupDetails(searchKeyword="",optionShow="") {
    let groupType = '';
    if(optionShow == 'groups') {
      this.ispdgs = '1';
      groupType = GroupType.PATIENTGROUP;
    }else if(optionShow == 'msggroups'){
      this.ispdgs = '0';
      groupType = GroupType.MSGGROUP;
    }
    this.loadingGroups = true;
    this.MessageGroupWithLoader.groups = true;
    const page = this.offset/this.limit;
    let parameter : GroupsListingRequest = {
      data: {
        pagination: {
            page: page > 0  ? page + 1 : 1,
            limit: this.limit
        }
      }
    };
    if(!isBlank(searchKeyword)) {
      parameter['data'].filter = {
        search : searchKeyword
      };
    }
    parameter['siteIds'] = this.getSelectedSiteIds();
    this.messageService.fetchGroupData(parameter, groupType).subscribe((messageGroups) => {
      if(messageGroups.success){
        this.memberDataGroupWise = groupType === GroupType.MSGGROUP ? messageGroups.data['messageGroups'] : messageGroups.data['patientGroups'];
        if(searchKeyword) {
          this.prevText = searchKeyword;
        }
        this.loadingGroups = false;
        if(isBlank(this.memberDataGroupWise)) {
          this.hideLoadMore = true;
        } else {
          if(this.memberDataGroupWise.length === 25) {
            this.hideLoadMore = false;
          } else {
            this.hideLoadMore = true;
          }
          this.allMessageGroups = [...this.allMessageGroups, ...this.memberDataGroupWise];
        }
        this.messageGroups = this.allMessageGroups;
        this.newMessageGroups = this.allMessageGroups;
        this.newallUserDetailsInMsgGroup = this.allUserDetailsInMsgGroup;
        var i = 0;
        this.memberDataGroupWise = this.allMessageGroups;
        this.memberDataGroupWise.sort(function (a, b) {
          if (a.groupName < b.groupName) return -1;
            if (a.groupName > b.groupName) return 1;
              return 0;
        });
        this.MessageGroupWithLoader.groups = false;
      } else {
        this.loadingGroups = false;
      }
    });
  }
  getSelectedSiteIds() {
    if (this.multiSiteEnable && !isBlank(this.messageSiteId) && +this.messageSiteId !== 0) {
      return typeof this.messageSiteId === 'string' ? this.messageSiteId.split(',').map((num) => {return +num}) : [this.messageSiteId];
    } else {
      return [this.userData.mySites[0].id];
    }
  }
  getGroups(refresh) {
    this.setChatWithTenantFilterSelect();
    if(this.optionShow == 'staff' || this.optionShow == 'partner'){
      this.staffUserList = [];
      this.loadMoreUsers(this.optionShow);
    }
  }

  noWhitespaceValidator(control: FormControl) {
    let isWhitespace = (control.value || '').trim().length === 0;
    let isValid = !isWhitespace;
    return isValid ? null : { 'whitespace': true }
  }
  clearMessageGroupSelection() {
    this.checkedIds = [];
    $('input[type="checkbox"]').prop({
      indeterminate: false,
      checked: false
    }).siblings('label')
      .removeClass('custom-checked custom-unchecked custom-indeterminate')
      .addClass('custom-unchecked');
  }
  messageGroupSelectionAll()
  {
    $('input[type="checkbox"]').prop({
      indeterminate: true,
      checked: true
    }).siblings('label')
      .removeClass('custom-checked custom-unchecked custom-indeterminate')
      .addClass('custom-checked');
  }

  removeMember(id,selectedRoleid = '') {
    var self = this;
    var i = 0;
    this.selectedGroupMembers = this.selectedGroupMembers.filter(function (members) {
      if (members.id != id) {
        return true;
      } else {
        self.selectedGroupMembers = self.selectedGroupMembers.filter(function (users) {
          return users.id != String(members.id);
        });
        self.selectedGroupMemberIds = self.selectedGroupMemberIds.filter(function (id) {
          return id != members.id;
        });
        let messagegropmemberfromalllist = null;
        messagegropmemberfromalllist = self.allUserDetailsInMsgGroup.find(user => user.id == members.id);
        if (messagegropmemberfromalllist) {
          self.newallUserDetailsInMsgGroup.push(messagegropmemberfromalllist);
        }
        // self.memberDataGroupWise = [];
          if(self.checkedRoleIds.length>0){
            var user = self.clinicalUserDetails.find((user) => user.userid == members.id);
            if (user && user.userid) {
              self.staffUserList.push(user);
              self.staffUserList = self.staffUserList.slice();
            }                     
            self.staffUserList.sort(function (a, b) {              
              if (a.displayname.toLowerCase() < b.displayname.toLowerCase()) return -1;
              if (a.displayname.toLowerCase() > b.displayname.toLowerCase()) return 1;
              return 0;
            });


            if( members.dualRoles && typeof members.dualRoles == "string"){
              members.dualRoles = JSON.parse(members.dualRoles);
            }
            self.checkedRoleIds.forEach((value)=>{
              if(members.role && members.role.id && value.id==members.role.id){
                self.removedRoleUsers.push({id:id,roleId:value.id});
              }else if(members.dualRoles && members.dualRoles.length>1){
                members.dualRoles.forEach((dual)=>{
                  if(dual.tenantUsersRoleId == value.id){
                    self.removedRoleUsers.push({id:id,roleId:value.id});
                  }
                });
              } 
              
            });
          }
          if(self.checkedIdsWithRole && self.checkedIdsWithRole[id]){
            delete self.checkedIdsWithRole[id];
          }
          /* if (self.newMessageGroups.length) {
            self.newMessageGroups.forEach(value => {
              self.memberDataGroupWise[value.id] = {}
              self.memberDataGroupWise[value.id]['groupData'] = { id: value.id, name: value.name };
              self.memberDataGroupWise[value.id]['userList'] = value.memberDetails.filter(member => self.selectedGroupMemberIds.indexOf(member.id) == -1);
            });
            self.memberDataGroupWise = self.memberDataGroupWise.filter(item => item.userList.length != 0);
            self.memberDataGroupWise.sort(function (a, b) {
              if (a.groupData.name < b.groupData.name) return -1;
              if (a.groupData.name > b.groupData.name) return 1;
              return 0;
            });
          } */

        if (typeof(members) == 'object' && !members.otherTenantStaff) {
          if(self.checkedRoleIds.length == 0){
          var user = self.clinicalUserDetails.find((user) => user.userid == members.id);

          if (user && user.userid) {
            self.staffUserList.push(user);
            self.staffUserList = self.staffUserList.slice();
          }

          self.staffUserList.sort(function (a, b) {              
            if (a.displayname.toLowerCase() < b.displayname.toLowerCase()) return -1;
            if (a.displayname.toLowerCase() > b.displayname.toLowerCase()) return 1;
            return 0;
          });
       }

          /*if (self.checkedRoleIds.length > 0){

            var user = self.clinicalUserDetails.find((user) => user.userid == members.id);

            if (user && user.userid) {
              self.staffUserList.push(user);
              self.staffUserList = self.staffUserList.slice();
            }

            self.staffUserList.sort(function (a, b) {              
              if (a.displayname.toLowerCase() < b.displayname.toLowerCase()) return -1;
              if (a.displayname.toLowerCase() > b.displayname.toLowerCase()) return 1;
              return 0;
            });

            self.checkedRoleIds.forEach((value) => {
              if(value.id==members.role.id){
                self.removedRoleUsers.push({id:id,roleId:value.id});
              } else if(members.dualRoles && members.dualRoles.length>1){
                members.dualRoles.forEach((dual)=>{
                  if(dual.tenantUsersRoleId == value.id){
                    self.removedRoleUsers.push({id:id,roleId:value.id});
                  }
                });
              }                
            });
          }

          if(self.checkedIdsWithRole && self.checkedIdsWithRole[id]) {
            delete self.checkedIdsWithRole[id];
          }

          var user = self.memberList.find((user)=>user.id == members.id);

           if(user.id) {
            self.newMemberList.push(user);
            self.newMemberList = self.newMemberList.slice();
          } 

          self.newMemberListByRoleWise = [];*/
          self.newMemberList.forEach(value => {
            if (value.role && value.role.id) {
              var user = {
                id: value.id,
                name: value.displayName,
                naTags: "",
                naTagNames: ""
              };

              if (value.naTags && value.naTags != null && value.naTags != 'null') {
                user.naTags = value.naTags;
                user.naTagNames = value.naTagNames;
              }
              
              var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id, tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles };
              if (!self.newMemberListByRoleWise[value.role.id]) {
                self.newMemberListByRoleWise[value.role.id] = {};
              }
              if (!('userList' in self.newMemberListByRoleWise[value.role.id])) {
                self.newMemberListByRoleWise[value.role.id]['userList'] = [];
              }
              if (!('roleData' in self.newMemberListByRoleWise[value.role.id])) {
                self.newMemberListByRoleWise[value.role.id]['roleData'] = {};
              }
              self.newMemberListByRoleWise[value.role.id]['roleData'] = roleData;
              self.newMemberListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
              self.newMemberListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
              self.newMemberListByRoleWise[value.role.id]['userList'].push(user);
            }
          });

          self.newMemberListByRoleWise = self.newMemberListByRoleWise.filter( function(a) {
            return (a && a.tenantId);
          });

          self.newMemberListByRoleWise.sort(function (a, b) {
            if (a.roleData.name < b.roleData.name) return -1;
            if (a.roleData.name > b.roleData.name) return 1;
            return 0;
          });
          
        } else if(typeof(members) == 'object' && members.otherTenantStaff) {
          var user = self.otherTenantMemberList.find((user)=>user.id==members.id);
          if(user && user.id) {
            self.newMemberOtherTenantList.push(user);
            self.newMemberOtherTenantList = self.newMemberOtherTenantList.slice();
          }

          self.newMemberOtherTenantListByRoleWise = [];
          self.newMemberOtherTenantList.forEach(value => {
            if (value.role && value.role.id) {                
              var user = {
                id: value.id,
                name: value.displayName,
                naTags: "",
                naTagNames: ""
              };

              if (value.naTags && value.naTags != null && value.naTags != 'null') {
                user.naTags = value.naTags;
                user.naTagNames = value.naTagNames;
              }

              var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id };
              if (!self.newMemberOtherTenantListByRoleWise[value.role.id]) {
                self.newMemberOtherTenantListByRoleWise[value.role.id] = {};
              }
              if (!('userList' in self.newMemberOtherTenantListByRoleWise[value.role.id])) {
                self.newMemberOtherTenantListByRoleWise[value.role.id]['userList'] = [];
              }
              if (!('roleData' in self.newMemberOtherTenantListByRoleWise[value.role.id])) {
                self.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = {};
              }
              self.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = roleData;
              self.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
              self.newMemberOtherTenantListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
              self.newMemberOtherTenantListByRoleWise[value.role.id]['userList'].push(user);
            }
          });

          self.newMemberOtherTenantListByRoleWise = self.newMemberOtherTenantListByRoleWise.filter( function(a) {
            return (a && a.tenantId);
          });

          self.newMemberOtherTenantListByRoleWise.sort(function (a, b) {
            if (a.roleData.name < b.roleData.name) return -1;
            if (a.roleData.name > b.roleData.name) return 1;
            return 0;
          });
        } else {
          let setCurrentTenant = false;
          let setOtherTenant = false;
          if(members.tenantId ==  self.selectedTenant) {
            setCurrentTenant = true
            var user = self.clinicalUserDetails.find((user)=>user.id==members.id);
            if(user.id) {
              self.newMemberList.push(user);
              self.newMemberList = self.newMemberList.slice();
            }            
          } 

          if(setCurrentTenant) {
            self.newMemberList.forEach(value => {
              if (value.role && value.role.id) {
                var user = {
                  id: value.id,
                  name: value.displayName,
                  naTags: "",
                  naTagNames: ""
                };

                if (value.naTags && value.naTags != null && value.naTags != 'null') {
                  user.naTags = value.naTags;
                  user.naTagNames = value.naTagNames;
                }

                var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id, tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles  };
                if (!self.newMemberListByRoleWise[value.role.id]) {
                  self.newMemberListByRoleWise[value.role.id] = {};
                }
                if (!('userList' in self.newMemberListByRoleWise[value.role.id])) {
                  self.newMemberListByRoleWise[value.role.id]['userList'] = [];
                }
                if (!('roleData' in self.newMemberListByRoleWise[value.role.id])) {
                  self.newMemberListByRoleWise[value.role.id]['roleData'] = {};
                }
                self.newMemberListByRoleWise[value.role.id]['roleData'] = roleData;
                self.newMemberListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
                self.newMemberListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
                self.newMemberListByRoleWise[value.role.id]['userList'].push(user);
              }
            });

            self.newMemberListByRoleWise = self.newMemberListByRoleWise.filter( function(a) {
              return (a && a.tenantId);
            });

            self.newMemberListByRoleWise.sort(function (a, b) {
              if (a.roleData.name < b.roleData.name) return -1;
              if (a.roleData.name > b.roleData.name) return 1;
              return 0;
            });
          }
          if(setOtherTenant) {
            self.newMemberOtherTenantListByRoleWise = [];
            self.newMemberOtherTenantList.forEach(value => {
              if (value.role && value.role.id) {
                var user = {
                  id: value.id,
                  name: value.displayName,
                  naTags: "",
                  naTagNames: ""
                };

                if (value.naTags && value.naTags != null && value.naTags != 'null') {
                  user.naTags = value.naTags;
                  user.naTagNames = value.naTagNames;
                }
                var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id, tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles};

                if (!self.newMemberOtherTenantListByRoleWise[value.role.id]) {
                  self.newMemberOtherTenantListByRoleWise[value.role.id] = {};
                }
                if (!('userList' in self.newMemberOtherTenantListByRoleWise[value.role.id])) {
                  self.newMemberOtherTenantListByRoleWise[value.role.id]['userList'] = [];
                }
                if (!('roleData' in self.newMemberOtherTenantListByRoleWise[value.role.id])) {
                  self.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = {};
                }
                self.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = roleData;
                self.newMemberOtherTenantListByRoleWise[value.role.id]['userList'].push(user);
                self.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
                self.newMemberOtherTenantListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
                
              }
            });
        
            self.newMemberOtherTenantListByRoleWise = self.newMemberOtherTenantListByRoleWise.filter( function(a) {
              return (a && a.tenantId);
            });
            self.newMemberOtherTenantListByRoleWise.sort(function (a, b) {
              if (a.roleData.name < b.roleData.name) return -1;
              if (a.roleData.name > b.roleData.name) return 1;
              return 0;
            });
          }
  
          self.memberDataGroupWise = [];
          if (self.newallUserDetailsInMsgGroup.length) {
            self.memberDataGroupWise = [];
            self.newMessageGroups.forEach(value => {
              self.memberDataGroupWise[value.id] = {}
              self.memberDataGroupWise[value.id]['groupData'] = { id: value.id, name: value.name };
              self.memberDataGroupWise[value.id]['userList'] = [];
              self.newallUserDetailsInMsgGroup.forEach((user, key) => {
                if (user.id != self.userData.userId) {
                  if (value.memberIds.split(',').indexOf(user.id + '') > -1) {
                    var role = { id: user.id, name: user.name, tenantId: user.tenantId, tenantName: user.tenantName };
                    // var foundIds = self.memberDataGroupWise[value.id]['userList'].filter(x => x.id.indexOf(user.id) !== -1);
                    // if(foundIds.length == 0)
                    // self.memberDataGroupWise[value.id]['userList'].push(role);
                    // self.memberDataGroupWise[value.id]['userList'] = self.memberDataGroupWise[value.id]['userList'].filter(x => !self.selectedGroupMembers.some(y => y.id == x.id));
                  }
                }
              });
            });
            self.memberDataGroupWise = self.memberDataGroupWise.filter(item=> item.userList.length != 0);
            self.memberDataGroupWise.sort(function (a, b) {
              if (a.groupData.name.toLowerCase() < b.groupData.name.toLowerCase()) return -1;
              if (a.groupData.name.toLowerCase() > b.groupData.name.toLowerCase()) return 1;
              return 0;
            });
          }
        }
        return false;
      }
    });

    this.disableSubmit();

    /* if(this.optionShow=='groups' || this.optionShow=='msggroups') {
      this.reset(this.optionShow);
    }*/
  }
  addMember() {
    var self = this;
    this.addMemberError = false;
    $(':checkbox:checked').each(function (i) {
      var name = this.name;
      if (name == "cliniciansRoleUser[]") {
        self.checkedIds.push($(this).val());
        
      } else if(name=="staffRoleWise"){
        var selectedRole =JSON.parse($(this).val());       
        if(self.dummyCheckedRoleIds.indexOf(selectedRole.id)>-1){
          var exist =false;
          if(self.checkedRoleIds.length){
            self.checkedRoleIds.forEach((value)=>{             
              if(value.id ==selectedRole.id){
                exist = true;
              }
            });
          }          
          if(!exist){
            self.checkedRoleIds.push(selectedRole);
            self.removeRoleStaffs(selectedRole);
          }
        }      
      } 
    });
    if (this.checkedIds.length > 0) {
      this.addMemberToList(this.checkedIds);
      this.srch.nativeElement.value = "";
      this.checkedIds = [];
      this.addMemberError = false;
    } else if (this.checkedRoleIds.length > 0 && this.optionShow !== 'groups' && this.optionShow !== 'msggroups') {
      this.addMemberToList(this.checkedRoleIds);
    } else if(this.optionShow === 'groups' || this.optionShow === 'msggroups') {
      this.resetPdgMembers = true;
      const groupType = this.optionShow === 'groups' ? GroupType.PATIENTGROUP : GroupType.MSGGROUP;
      this.clearMessageGroupSelection();    
      if(this.dummyCheckedGroupMembers && this.dummyCheckedGroupMembers.length > 0) {
        this.getGroupMembersByGroupId(this.dummyCheckedGroupMembers, false, groupType);
        this.dummyCheckedGroupMembers = [];
        var liLength = $('#treeview li').length;
        this.offset = 25 - liLength;
      }
    } else {
      this.addMemberError = true;
    }

  }
  removeRoleStaffs(role) {
    this.selectedGroupMembers = this.selectedGroupMembers.filter((member) => {
      let memberRoleIds = (typeof member.roleIds === 'string') ? JSON.parse(member.roleIds) : member.roleIds;
      if(!memberRoleIds.includes(+role.id)) {
        return member;
      }
    });
  }
  removeExistingRole(roleID){
    if(this.checkedRoleIds.length){
      this.checkedRoleIds.forEach((value,key) => {
        if(value.id==roleID){
          this.checkedRoleIds.splice(key,1);
          if(value.citusRoleId != 20){
            var selectedRoletenant =this.MemberListByRoleWiseStaff.filter(e => e.tenantId === value.tenantId);
             }else if((value.citusRoleId == 20)){
               var selectedRoletenant =this.MemberListByRoleWisepartner.filter(e => e.tenantId === value.tenantId);
             }
         //var selectedRoletenant =this.MemberListByRoleWise.filter(e => e.tenantId === value.tenantId);
         if(selectedRoletenant.length > 0){
          this.removeRoleFromExistingMember(roleID,value.citusRoleId);
         }else {
          if(this.selectedGroupMembers && this.selectedGroupMembers.length > 0){    
                      
              this.selectedGroupMembers = this.selectedGroupMembers.filter((members)=>
             { 
              if (members.role != roleID) {              
                return true;
              } 
            });
        }

         }

         
        }    
      })
    }
  }
  removeRoleFromExistingMember(roleId,citusRoleId){ 
    var self = this;   
    if(citusRoleId != 20){
      var MemberListByRoleWiseArr =  self.MemberListByRoleWiseStaff;
      } else if((citusRoleId == 20)){
      var MemberListByRoleWiseArr =  self.MemberListByRoleWisepartner;
     }
      MemberListByRoleWiseArr.forEach((value,key) => {
        
        if(value.roleData.id==roleId){ 
          if(self.selectedGroupMembers && self.selectedGroupMembers.length > 0){   

            if( value.userList == undefined) {
              value.userList =[]; 
              let i=0;
              this.testArr.forEach(element => {
                if(element.role == roleId){
                  value.userList[i]={ id: element.id, name: element.name};
                  }
                i++;
              }); 
              
            }
            
          value.userList.map(user => {            
            self.selectedGroupMembers = self.selectedGroupMembers.filter((members)=>
           { 
            if (members.id != user.id) {              
              return true;
            } else{
              
              var users = self.clinicalUserDetails.find((users) => users.userid == members.id);            
              if (users && users.userid) {
                self.staffUserList.push(users);
                self.staffUserList = self.staffUserList.slice();
              }                     
             
             



            }
          });
          self.selectedroleBasedStaffs = self.selectedroleBasedStaffs.filter(function (selectedmembers) { console.log(selectedmembers.id +"!="+ user.id);
            if (selectedmembers.id != user.id) {              
              return true;
            } 
          });
          
        });
      }
      var memberRole = value;  
          
     if(this.optionShow == 'staffroles' && citusRoleId != 20){
      self.newMemberListByRoleWise.push(memberRole);
     } else if(this.optionShow == 'partnerroles' && citusRoleId == 20){
      self.newMemberListByRoleWise.push(memberRole);
     }
              self.newMemberListByRoleWise = self.newMemberListByRoleWise.slice();                               
              self.newMemberListByRoleWise.sort(function (a, b) {              
                if (a.roleData.name.toLowerCase() < b.roleData.name.toLowerCase()) return -1;
                if (a.roleData.name.toLowerCase() > b.roleData.name.toLowerCase()) return 1;
                return 0;
             
              });
              
              this.newMemberListByRoleWise.forEach(element => {
                if(element.roleData.id == roleId){
                 element.userList = [];
                }
              });
        
              }
             });
             self.staffUserList.sort(function (a, b) {              
              if (a.displayname.toLowerCase() < b.displayname.toLowerCase()) return -1;
              if (a.displayname.toLowerCase() > b.displayname.toLowerCase()) return 1;
              return 0;
            }); 
             self.selectedGroupMembers = self.selectedGroupMembers.slice();      
  }

  addMemberToList(checkedIds) {
    if(this.optionShow == 'staff' || this.optionShow == 'partner') {
      let staffInSelectedRole = false;
      this.clinicalUserDetails.forEach(element => {
        if (this.checkedIds.includes(element.userid)) {
          let memberRoleIds = JSON.parse(element.dualRoles).map(item => item.tenantUsersRoleId);
          if(!this.checkedRoleIds.some(item => memberRoleIds.includes(+item.id))) {
            this.newMember = {
              id: "",
              displayName: "",
              role: {},
              tenantId: null,
              tenantName: "",
              otherTenantStaff: false,
              naTags: "",
              naTagNames: "",
              tenantRoleId: ""
            };
            this.newMember.id = element.userid;
            this.newMember.displayName = element.displayname;
            this.newMember.role = element.role;
            this.newMember.tenantId = element.tenantId;
            this.newMember.tenantName = element.tenantName;
            this.newMember.siteId = element.siteId;
            this.newMember.siteName = element.siteName;
            this.newMember.tenantRoleId = element.tenantRoleId;
            this.newMember.roleIds = JSON.parse(element.dualRoles).map(item => item.tenantUsersRoleId);
            if(element.naTags && element.naTags != ""){
              this.newMember.naTags = element.naTags;
              this.newMember.naTagNames = element.naTagNames;
            }
            if ( this.selectedGroupMembers.findIndex(x => x.id === element.userid) === -1){
              this.selectedGroupMembers.push(this.newMember);
              this.selectedGroupMemberIds.push(this.newMember.id);
            }
            this.selectedGroupMembers = this.selectedGroupMembers.slice();
            this.selectedGroupMemberIds = this.selectedGroupMemberIds.slice();

            this.newallUserDetailsInMsgGroup = this.newallUserDetailsInMsgGroup.filter(function (members) {
              return members.userid != element.userid;
            });

            this.newMemberList = this.newMemberList.filter(function (members) {
              return members.id != element.userid;
            });

            this.staffUserList = this.staffUserList.filter(function (members) {
              return members.userid != element.userid;
            });
          } else {
            staffInSelectedRole = true;
          }
        }
      });
      if (staffInSelectedRole) {
        this._structureService.notifyMessage({
          messge: this._ToolTipService.getTranslateData('WARNING.MEMBER_WITH_SAME_ROLE_EXISTS'),
          type: CONSTANTS.notificationTypes.warning
        });
      }
      this.newMemberListByRoleWise = [];
      this.newMemberList.forEach(value => {
        if (value.role && value.role.id) {
          var user = { id: value.id, name: value.displayName };
          var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id };
          if (!this.newMemberListByRoleWise[value.role.id]) {
            this.newMemberListByRoleWise[value.role.id] = {};
          }
          if (!('userList' in this.newMemberListByRoleWise[value.role.id])) {
            this.newMemberListByRoleWise[value.role.id]['userList'] = [];
          }
          if (!('roleData' in this.newMemberListByRoleWise[value.role.id])) {
            this.newMemberListByRoleWise[value.role.id]['roleData'] = {};
          }
          this.newMemberListByRoleWise[value.role.id]['roleData'] = roleData;
          this.newMemberListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
          this.newMemberListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
          this.newMemberListByRoleWise[value.role.id]['userList'].push(user);
        }
      });

      this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter( function(a) {
        return (a && a.tenantId);
      });

      this.newMemberListByRoleWise.sort(function (a, b) {
        if (a.roleData.name < b.roleData.name) return -1;
        if (a.roleData.name > b.roleData.name) return 1;
        return 0;
      });
    } else if (this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
      this.staffList.forEach(element => {       
        this.checkedRoleIds.forEach((rolevalue,key) => {  
          if(rolevalue.id ==element.id){
          if(!isBlank(this.selectedroleBasedStaffs)) {
            this.selectedroleBasedStaffs.forEach(users => {
              if (this.selectedGroupMembers.findIndex(x => x.id === users.id) === -1) {
                this.newMember = {
                  id: "",
                  displayName: "",
                  role: {},
                  tenantId: null,
                  tenantName: "",
                  otherTenantStaff: false,
                  naTags: "",
                  naTagNames: ""
                };

                this.newMember.role = element.id;           
                this.newMember.tenantId = element.tenantId;
                this.newMember.tenantName = element.tenantName; 
                this.newMember.id = users.id;
                this.newMember.displayName = users.displayName;
                this.newMember.siteId = element.siteId;
                this.newMember.siteName = element.siteName;
                var user = { id: this.newMember.id, name: this.newMember.displayName, role:this.newMember.role};

                if(element.naTags && element.naTags != ""){
                  this.newMember.naTags = element.naTags;
                  this.newMember.naTagNames = element.naTagNames;
                }

                this.testArr.push(user);
                this.selectedGroupMemberIds.push(this.newMember.id);
                this.newallUserDetailsInMsgGroup = this.newallUserDetailsInMsgGroup.filter(function (members) {
                  return members.userid != element.userid;
                });
              }
              
              this.selectedGroupMembers = this.selectedGroupMembers.slice();
              this.selectedGroupMemberIds = this.selectedGroupMemberIds.slice();
              
            })
          }

          this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter(function (members) {
            return members.roleData.id != element.id;
          });
          //Delete a user if the user is added to GP members when user role is checked 
          if(this.removedRoleUsers.length >0 ){
            this.removedRoleUsers.forEach((value,key)=>{
              if(value.id ==element.id && value.roleId==element.role.id){
                this.removedRoleUsers.splice(key,1);
              }
            })
          }
        }
      })
        //Delete user from removedRoleUsers if its role is unchecked
        if(this.removedRoleUsers.length >0 && this.removedRoleUsers.indexOf(element.id)>-1){
          var detect = false;
          this.checkedRoleIds.forEach((value)=>{
            if(value.id ==element.role.id){
              detect =true;
            }
          });
          if(!detect){
            this.removedRoleUsers.splice(this.removedRoleUsers.indexOf(element.id),1);
          }
        }
      });
      this.newMemberListByRoleWise.sort(function (a, b) {
        if (a.roleData.name < b.roleData.name) return -1;
        if (a.roleData.name > b.roleData.name) return 1;
        return 0;
      });
    } else {
      let setCurrentTenant = false;
      let setOtherTenant = false;
      let filtered:any = [];
      this.selectedGroupMembers.forEach(value => {
        if (value && value.id && filtered.indexOf(value.id) == -1 && value.id != null && value.id != undefined) {
            filtered.push(value.id);
        }
      });
      this.allUserDetailsInMsgGroup.forEach(value => {
        if (filtered.includes(value.id)) {
          this.newMember = {
            id: "",
            displayName: "",
            role: {},
            tenantId: null,
            tenantName: "",
            otherTenantStaff: ((value.tenantId && (this.userData.tenantId != value.tenantId )) ? true : false),
            naTags: "",
            naTagNames: ""
          }

          this.newMember.id = value.id;
          this.newMember.displayName = value.name;
          this.newMember.role = value.roleId;
          this.newMember.tenantId = value.tenantId;
          this.newMember.tenantName = value.tenantName;

          if (value.naTags && value.naTags != null && value.naTags != 'null') {
            this.newMember.naTags = value.naTags;
            this.newMember.naTagNames = value.naTagNames;
          }
          console.log("this.selectedGroupMembers.findIndex(x => x.id === value.id)",this.selectedGroupMembers.findIndex(x => x.id === value.id));
          if ( this.selectedGroupMembers.findIndex(x => x.id === value.id) === -1){
            this.selectedGroupMembers.push(this.newMember);
            this.selectedGroupMemberIds.push(this.newMember.id);
          }
          this.selectedGroupMembers = this.selectedGroupMembers.slice();
          this.selectedGroupMemberIds = this.selectedGroupMemberIds.slice();

          this.newallUserDetailsInMsgGroup = this.newallUserDetailsInMsgGroup.filter(function (members) {
            return members.id != value.id;
          });

          if(value.tenantId == this.userData.tenantId) {
            setCurrentTenant = true
            this.newMemberList = this.newMemberList.filter(function (members) {
              return members.id != value.id;
            });            
          } else {
            setOtherTenant = true;
            this.newMemberOtherTenantList = this.newMemberOtherTenantList.filter(function (members) {
              return members.id != value.id;
            });
          }
        }
      });
      if(setCurrentTenant) {
        this.newMemberListByRoleWise = [];
        this.newMemberList.forEach(value => {
          if (value.role && value.role.id) {
            var user = {
              id: value.id,
              name: value.displayName,
              naTags: "",
              naTagNames: ""
            };

            if (value.naTags && value.naTags != null && value.naTags != 'null') {
              user.naTags = value.naTags;
              user.naTagNames = value.naTagNames;
            }

            var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id };        
            if (!this.newMemberListByRoleWise[value.role.id]) {
              this.newMemberListByRoleWise[value.role.id] = {};
            }
            if (!('userList' in this.newMemberListByRoleWise[value.role.id])) {
              this.newMemberListByRoleWise[value.role.id]['userList'] = [];
            }
            if (!('roleData' in this.newMemberListByRoleWise[value.role.id])) {
              this.newMemberListByRoleWise[value.role.id]['roleData'] = {};
            }
            this.newMemberListByRoleWise[value.role.id]['roleData'] = roleData;
            this.newMemberListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
            this.newMemberListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
            this.newMemberListByRoleWise[value.role.id]['userList'].push(user);
          }
        });
        this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter( function(a) {
          return (a && a.tenantId);
        });
        this.newMemberListByRoleWise.sort(function (a, b) {
          if (a.roleData.name < b.roleData.name) return -1;
          if (a.roleData.name > b.roleData.name) return 1;
          return 0;
        });
      }
      if(setOtherTenant) {
        this.newMemberOtherTenantListByRoleWise = [];
        this.newMemberOtherTenantList.forEach(value => {
          if (value.role && value.role.id) {
            var user = {
              id: value.id,
              name: value.displayName,
              naTags: "",
              naTagNames: ""
            };

            if (value.naTags && value.naTags != null && value.naTags != 'null') {
              user.naTags = value.naTags;
              user.naTagNames = value.naTagNames;
            }

            var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id };        
            if (!this.newMemberOtherTenantListByRoleWise[value.role.id]) {
              this.newMemberOtherTenantListByRoleWise[value.role.id] = {};
            }
            if (!('userList' in this.newMemberOtherTenantListByRoleWise[value.role.id])) {
              this.newMemberOtherTenantListByRoleWise[value.role.id]['userList'] = [];
            }
            if (!('roleData' in this.newMemberOtherTenantListByRoleWise[value.role.id])) {
              this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = {};
            }
            this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = roleData;
            this.newMemberOtherTenantListByRoleWise[value.role.id]['userList'].push(user);
            this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
            this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
            
          }
        });
    
        this.newMemberOtherTenantListByRoleWise = this.newMemberOtherTenantListByRoleWise.filter( function(a) {
          return (a && a.tenantId);
        });
        this.newMemberOtherTenantListByRoleWise.sort(function (a, b) {
          if (a.roleData.name < b.roleData.name) return -1;
          if (a.roleData.name > b.roleData.name) return 1;
          return 0;
        });
      }
      this.selectedGroupMembers = this.removeDuplicates(this.selectedGroupMembers,"id");
      console.log("selectedGroupMembers",this.selectedGroupMembers,this.selectedGroupMembers,this.selectedGroupMembers.length);
      this.selectedGroupRoles.forEach((role)=>{
        let roleIndex = this.checkedRoleIds.findIndex(r => r.id === role.roleId);
        if(roleIndex < 0) {
          this.checkedRoleIds.push({id: role.roleId, name: role.name, tenantId: role.tenant_id});
          this.removeRoleStaffs({id: role.roleId, name: role.name, tenantId: role.tenant_id});
        }
      });
    }
    this.disableSubmit();
  }

  searchOnEnter(event, optionShow) {
    if (optionShow == 'staff' || optionShow == 'groups' || optionShow == 'partner' || optionShow == 'msggroups' ) {
      if (event.keyCode == 13) {
        event.preventDefault();

        if (this.srch.nativeElement.value.trim()) {
          this.search(optionShow);
        } else {
          this.reset(optionShow)
        }
      }
    }
  }

  updateMessageGroup(id, f) {
    if (!f.valid || this.noMemberError) {
      if (!f.valid) {
        $('input.ng-invalid').focus();
      } else if (this.noMemberError) {
        $('html, body').animate({
          scrollTop: $(".card.existing .card-block").offset().top
        });
      }
      return false;
    }
    this.disableButton = true;
    if (this.selectedGroupMembers.length > 0 || this.checkedRoleIds.length > 0) {
      let selectedRoleIds = []; 
			if(this.checkedRoleIds.length){
			  this.checkedRoleIds.forEach((value,key) => {
				selectedRoleIds.push(Number(value.id));  
			  })
			}
      this.selectedGroupMembers = this.selectedGroupMembers.map((member) => {
        return { id: member.id.toString(), displayName: member.displayName, tenantId: parseInt(member.tenantId), tenantName:member.tenantName };
      })
      let updateParams : MessageGroupRequest = {
        data :{
          name: this.messageGroup.controls['messageGroupName'].value,
          isPublic: this.selectedisPublic,
          allowMultiThreadChat : this.selectedAllowMultiThread,
          members: this.selectedGroupMembers.map((member)=> { return +member.id}),
          roles: selectedRoleIds
        }
      }
      if (this.multiSiteEnable && !isBlank(this.messageSiteId) && +this.messageSiteId !== 0) {
        updateParams['siteIds'] = typeof this.messageSiteId === 'string' ? this.messageSiteId.split(',').map((num) => {return +num}) : [this.messageSiteId];
      } else {
        updateParams['siteIds'] = [this.userData.mySites[0].id];
      }
      const isPublic = this.selectedisPublic ? 'public' : 'not public';
      const allowMultiThreadChat = this.selectedAllowMultiThread ? 'multi thread chat is allowed' : 'multi thread chat is not allowed';
      const selectedGroupMembersNames = this.selectedGroupMembers.map(x=> x.displayName).toString();
      this.messageService.createMessageGroup(updateParams).subscribe(response => {
        if(response.success) {
          let newMessageGroupData = {
            status: 'new',
            id: response.data.messageGroupId,
            name: this.messageGroup.controls['messageGroupName'].value,
            createdBy: this.userData.userId,
            tenantId: this.userData.tenantId,
            isPublic: this.selectedisPublic,
            allowMultiThreadChat: this.selectedAllowMultiThread,
            pdgroup: '0',
            members: '',
            memberIds: '',
            cmisIds: ''
          };
          this._SharedService.newMessageGroup.emit(newMessageGroupData);
          const activityLogMessage = `${this.userData.displayName} added new Message Group(${this.messageGroup.controls['messageGroupName'].value}), members are ${selectedGroupMembersNames}, branch is ${this.userData.tenantId} and this group is ${isPublic} and ${allowMultiThreadChat} for this group`;
          const activityData = {
            activityName: 'Create Message Group',
            activityType: 'manage group messaging',
            activityDescription: activityLogMessage
          };
          this._structureService.trackActivity(activityData);
          this._structureService.notifyMessage({
            messge: this._ToolTipService.getTranslateData('SUCCESS_MESSAGES.MSGGROUP_CREATE'),
            type: CONSTANTS.notificationTypes.success
          });
          this.disableButton = false;
          setTimeout(()=> { 
            this.router.navigate(['/message/message']);
          }, 1000);
        } else {
          this.disableSubmit();
        }
      }, error => {
        this.disableSubmit();
      });
    } else {
      this.noMemberError = true;
    }
  }
  togglePublic(value) {
    this.selectedisPublic = value;
  }
  toggleMultiThreadOption(value) {
    this.selectedAllowMultiThread = value;
  }
  disableSubmit() {
    if (this.selectedGroupMembers.length || this.checkedRoleIds.length) {
      this.disableButton = false;
      this.noMemberError = false;
    } else {
      this.disableButton = true;
      this.noMemberError = true;
    }
  }
  goToList() {
    this.location.back();
  }
  setSelectedPDGs(pdgData: any) {
		this.dummyCheckedGroupMembers = pdgData && pdgData.map(item => ({ id: +item.id,
      ...(this._structureService.isMultiAdmissionsEnabled && { admissionId: item.admissionId }) }));
    this.resetPdgMembers = false;
	}
  
}
