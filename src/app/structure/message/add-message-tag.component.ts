import { Component, OnInit, ElementRef, Renderer } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import {
	FormsModule,
	FormBuilder,
	FormGroup,
	Validators,
	FormControl,
	FormArray,
	ReactiveFormsModule
} from '@angular/forms';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';

declare var $: any;
declare var swal: any;
@Component({
	selector: 'app-add-message-tag',
	templateUrl: './add-message-tag.component.html'
})
export class AddMessageTagComponent implements OnInit {
	messageTag = [];
	tagList = [];
	dTable;
	userData;
	userDataConfig: any = '';
	userConfig: any;
	isEnableMessageCategory= false;
	isEnableApiBasedIntegrationCategory= false;
	// externalMessageTagCategoryLabel='';
	// externalMessageTagTypeIdLabel='';
	filingCenters = [];
	editRowId: any;
	showClose = false;
	showClosetag = false;
	showCloseInt = false;
	tagDefenitionAdd: FormGroup;
	tagDefenitionEdit: FormGroup;
	userInfo;
	userTagType;
	tagDeleted;
	activeMessageTag;
	tenantId;
	folderLists;
	dataLoadingMsg = true;
	type = 1;
	editCellPrevValue;
	showAddErrorMessage = false;
	showEditErrorMessage = false;
	isProgressNoteIntegration = false;
	folderName;
	folderNameJson;
	isFilingCenter = false;
	isLinux = false;
	isWindows = false;
	tagtypeList;
	approvalRequired;
	outcomeMeasures;
	patientFacing = false;
	enableNotification = false;
	enableIntegration = false;
	nursingAgencyTags: any = [];
	nursingAgencyUserTagSelected: any = [];
	isNursingAgencyEnabled = 0;
	isMessageYypeCategoryCodeEnabled=0;
	enableIntrationDiv=false;
	multiSiteEnable=false;
	authorizationKey;
  	apiEndPoint;
  	noteTypes: any = [];
  	showLoader: boolean;
     selectedExtDocumentName: any;
     selectedExtDocumentId: any;
     noteTypesCount: any;
     documentTypeSet: any;
     enableDocType: boolean;
     selectedCatValue: any;
     selectedTypeValue: any;
     selectedTypeName: any;
	noteTypeCategory: any;
	isEnableApiBasedIntegrationType: boolean;
	msgTagTypes: any;
	msgTagTypeCount: any;
	msgTypeSet: any;
	selectedCatName: any;
	catLabel: string;
	typeLabel: string;
	isEnableMessageType: boolean = false;
	enableSubData = false;
	noteCount: any = 0;
	msgCount: any ;
	constructor(
		private route: ActivatedRoute,
		private router: Router,
		private _formBuild: FormBuilder,
		private _structureService: StructureService,
		private _ToolTipService: ToolTipService,
		elementRef: ElementRef,
		renderer: Renderer
	) {
		this.userInfo = this._structureService.loginUserDetails;
		this.tenantId = this.userInfo.tenantId;
		this.tagDefenitionAdd = new FormGroup({
			tagCategory: new FormControl(null),
			tagCategoryId: new FormControl(null),
			tagName: new FormControl(null,Validators.required),
			tagTypeId: new FormControl(null),
			tagType: new FormControl(null),
			filingcenter: new FormControl(null),
			fileSaveFormat: new FormControl('{tagname}-{FirstName}-{LastName}-{DOB}-{createdOn}-{signedOn}'),
			fileSaveFormatIntegration: new FormControl('{MRN}-{UniqueNO}'),
			nursingAgencyUserTag: new FormControl(null),
			messageTypeID:new FormControl(null),
			messageCategoryCode:new FormControl(null),
			messagecatecode:new FormControl(null),
			messagecatetypeid:new FormControl(null)
		});
	}
	ngOnInit() {
	this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
	this.userConfig = this._structureService.getUserdata().config;
	this.isEnableMessageCategory = (this.userDataConfig.enable_external_message_category_code_in_message_tag == 1) ? true:false;
	this.isEnableMessageType = (this.userDataConfig.enable_external_message_type_id_in_message_tag == 1) ? true:false;	

	this.isEnableApiBasedIntegrationCategory = (this.userDataConfig.enable_API_based_integration_for_message_category == 1) ? true:false;
	this.isEnableApiBasedIntegrationType = (this.userDataConfig.enable_API_based_integration_for_message_type == 1) ? true:false;
	if(this.isEnableApiBasedIntegrationCategory && this.isEnableApiBasedIntegrationType){
		this.catLabel = "External Message Tag Category"
		this.typeLabel = "External Message Tag Type Id"
	}else{
		this.catLabel = "External Message Tag Category Name"
		this.typeLabel = "External Message Tag Type Name"
	}
	// this.externalMessageTagCategoryLabel = this.userDataConfig.label_for_external_msg_tag_category ? this.userDataConfig.label_for_external_msg_tag_category : 'External Message Tag Category';
	// this.externalMessageTagTypeIdLabel = this.userDataConfig.label_for_external_msg_tag_type_id ? this.userDataConfig.label_for_external_msg_tag_type_id : 'External Message Tag Type Id';
	this.userData = this._structureService.getUserdata();
    this.apiEndPoint = this.userData.config.api_integration_end_point;
    console.log('apiEndPoint', this.apiEndPoint);
    this.authorizationKey = this.userData.config.api_integration_authorization_key;
    console.log('authorizationKey', this.authorizationKey);
		this._structureService.getMessageTagtype().then((data) => {
			if (data['getSessionTenant']['messageTagTypes']) {
				this.tagtypeList = data['getSessionTenant']['messageTagTypes'];
			}
			$('#tagtype').select2({
				allowClear: true,
				placeholder: 'Select Tag Type',
				data: this.tagtypeList
			});
			$('#msgtagcategory').select2({
				allowClear: true,
				placeholder: ' Select Message Tag Category',
				data: this.tagtypeList
			});
			$('#msgtagtypeid').select2({
				allowClear: true,
				placeholder: 'Select Message Tag Type Id',
				data: this.tagtypeList
			});
		});
		this.userData = this._structureService.getUserdata();	
		this.multiSiteEnable = this.userData.config.enable_multisite == 1 ? true : false;
		if (this.userData.config.enable_filing_center == '1') {
			this.isFilingCenter = true;
		} else {
			this.isFilingCenter = false;
			this.tagDefenitionAdd.removeControl('filingcenter');
			this.tagDefenitionAdd.removeControl('fileSaveFormat');
			this.tagDefenitionAdd.removeControl('fileSaveFormatIntegration');
		}
		if (this.userData.config.progress_note_integration_data_format == 'json') {
			this.isProgressNoteIntegration = true;
		} else {
			this.isProgressNoteIntegration = false;
			//this.tagDefenitionAdd.removeControl('filingcenter');
			//this.tagDefenitionAdd.removeControl('fileSaveFormat');
			//this.tagDefenitionAdd.removeControl('fileSaveFormatIntegration');
		}

		/**
     * Get User tags with tag type (Nursing Agency) BioMatrix -  Nursing tags.
     */

		if (this.userData.config.enable_nursing_agencies_visibility_restrictions == 1) {
			this.getNursingAgencyTags();
			this.isNursingAgencyEnabled = this.userData.config.enable_nursing_agencies_visibility_restrictions;
		}
		if (this.userData.config.enable_message_type_id_category_code == 1) {
			
			this.isMessageYypeCategoryCodeEnabled = this.userData.config.enable_message_type_id_category_code;
		}
		console.log('this.isFilingCenter');
		console.log(this.isFilingCenter);
		console.log('this.isFilingCenter');
		var page = 'message-tag-definitions';
		$('.message-tag').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00001') });
		$('.message-tag-name').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00019') });
		$('.default-filing-center').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00002') });
		$('.default-filename').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00003') });
		$('.download-connect').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00004') });
		$('.default-tagtype').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00005') });
		$('.default-outcomeMeasure').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00007') });
		$('.default-approvalRequired').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00008') });
		$('.default-patientFacing').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00009') });
		$('.default-messagecategorycode').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00011') });
		$('.default-messagetypeid').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00012') });
		$('.default-enableIntegration').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00015') });
		$('.default-enableNotification').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00022') });
		$('.default-messagecatetypeid').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00020') });
		$('.default-messagecatecode').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00021') });
		
		if(this.isEnableApiBasedIntegrationCategory && this.isEnableApiBasedIntegrationType){
			$('.default-messagetagcategory').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00013') });
			$('.default-messagetagtype').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00016') });

		}else{
			$('.default-messagetagcategory').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00017') });
			$('.default-messagetagtype').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00018') });
		}
		
		$('.default-messagetagcategoryid').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00014') });
		$('.default-messagetagtypeid').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00016') });

		
		console.log($('.default-messagetypeid').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00012') }));
		
		console.log($('.default-messagetagcategory').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00013') }));
		
		$('.selectd').select2({
			placeholder: 'Loading Filing Center...'
		});
		$('#tagtype').attr('placeholder', 'Select Tag Type');
		$('#tagtype').select2({
			placeholder: 'Select Tag Type'
		});
		$('.selectd').attr('disabled', true);

		$('#tagtype').on('change', (e) => {
			this.showClosetag = true;
			var tagid = $('#tagtype').val();
			this.tagtypeList.filter((data, index) => {
				if (data.tagTypeId == tagid) {
					this.userTagType = data.userType;
				}
			});
			if (this.userTagType == 'patient-facing') {
				this.patientFacing = false;
			}
		});
		if (this.isFilingCenter) {
			$('#sfilingCenterss').on('change', (e) => {
				console.log($('#sfilingCenterss').val());
				setTimeout(() => {
					$('#select2-sfilingCenterss-container').removeAttr('title');
				}, 1);
				this.showClose = true;
				this.folderName = { folderName: $('#sfilingCenterss').val(), type: 'OUTGOING' };
			});
			this.getFolderLists();
		}
		if (this.isFilingCenter) {
			$('#sfilingCenterjson').on('change', (e) => {
				console.log($('#sfilingCenterjson').val());
				setTimeout(() => {
					$('#select2-sfilingCenterjson-container').removeAttr('title');
				}, 1);
				this.showCloseInt = true;
				this.folderNameJson = { folderName: $('#sfilingCenterjson').val(), type: 'OUTGOING' };
			});
			this.getFolderLists();
		}
		setTimeout(function() {
			$('#nursing-agency-user-tags').select2({});
		}, 500);
	}

	clearFilingCenter() {
		this.showClose = false;
		this.folderName = '';
		$('#sfilingCenterss').val('');
		$('#sfilingCenterss').select2({
			allowClear: false,
			placeholder: 'Select Filing Center',
			data: this.filingCenters
		});
	}

	clearFilingCenterjson() {
		this.showCloseInt = false;
		this.folderName = '';
		$('#sfilingCenterjson').val('');
		$('#sfilingCenterjson').select2({
			allowClear: false,
			placeholder: 'Select Filing Center',
			data: this.filingCenters
		});
	}
	cleartagtype() {
		$('#tagtype').val('');
		this.showClosetag = false;
		$('#tagtype').select2({
			allowClear: false,
			placeholder: 'Select Tag Type',
			data: this.tagtypeList
		});
	}
	noWhitespaceValidator(control: FormControl) {
		let isWhitespace = (control.value || '').trim().length === 0;
		let isValid = !isWhitespace;
		return isValid ? null : { whitespace: true };
	}

	initTags() {
		return this._formBuild.group({
			name: [ '', [ Validators.required, this.noWhitespaceValidator ] ],
			id: [ '', Validators.required ]
		});
	}
	initTagAdd() {
		return this._formBuild.group({
			name: [ '', [ Validators.required, this.noWhitespaceValidator ] ]
		});
	}

	getFolderLists() {
		this._structureService.getTenantFilingCenterFolders('OUTGOING').then((data) => {
			if (data && data['getTenantFilingCenterFolders'] && data['getTenantFilingCenterFolders'].length) {
				this.folderLists = JSON.parse(JSON.stringify(data['getTenantFilingCenterFolders']));
				// this.folderLists.unshift({folderName:"Select Filing Center",type:"OUTGOING"});
				$('.selectd').attr('disabled', false);
				$('.selectd').select2({
					placeholder: 'Select Filing Center'
				});
				this.folderName = '';

				this.filingCenters = [];
				console.log(this.folderName);
				console.log(this.folderLists);

				for (let i = 0; i < this.folderLists.length; i++) {
					var fname = this.folderLists[i].folderName;
					var ftype = this.folderLists[i].type;
					var id = '{"folderName":"' + fname + '","type":"' + ftype + '"}';
					var item = { id: fname, text: fname };
					this.filingCenters.push(item);
				}

				/* var AJAX_OPTIONS = [
              { id: '1', text: 'Choice 1' },
              { id: '2', text: 'Choice 2' },
              { id: '3', text: 'Choice 3' },
              { id: '4', text: 'Choice 4' },
              { id: '5', text: 'Choice 5' }
          ]; */
				$('#sfilingCenterss').select2({
					allowClear: true,
					placeholder: 'Select Filing Center',
					data: this.filingCenters
				});
				$('#sfilingCenterjson').select2({
					allowClear: true,
					placeholder: 'Select Filing Center',
					data: this.filingCenters
				});
			} else {
				$('.selectd').select2({
					placeholder: 'No Filing Centers Available'
				});
				$('.selectd').attr('disabled', true);
				console.log('No folders');
			}
		});
	}
	getExtDocData(reload){
		this.showLoader = true;
		if(this.isEnableApiBasedIntegrationCategory==true && this.isEnableApiBasedIntegrationType == true){
			this.enableDocType = false;  
			this.enableSubData = false;
			this._structureService.getNoteTypes('noteCategory').then((data) => {
				if(data){				 
				  console.log('data', data);
				  if(data['issue']){
					  if(data['issue'][0]['diagnostics']['statusCode'] == 412){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 1;
					  } else if(data['issue'][0]['diagnostics']['statusCode'] == 406){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 4;
					  }
				  }else if(data['messageTagCategory']){
					this.showLoader = false;
					this.noteTypes = data['messageTagCategory'];
					this.noteTypes = this.noteTypes['items'];
					this.noteCount  = this.noteTypes.length;
					this.noteTypesCount = -1;
					this.msgTagTypeCount = -1;
					console.log(this.noteTypes);
				  }else if(data['message']){
					if(data['message'] == 412){
					  this.noteCount = 0;
					  this.showLoader = false;
					  this.noteTypesCount = 1;
					} else if(data['message'] == 404){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 0;
					} else if(data['message'] == 500){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 0;
					} else if(data['message'] == 0){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 0;
					} else if(data['message'] == 401){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 2;
					} else if(data['message'] == "Invalid Parameter"){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 3;
					} else if(data['message'] == 406){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 4;
					}
					}				  				
				}else{					
				  this.showLoader = false;
				  this.noteTypesCount = 0;
				}				
			})			
		}
		// else if(this.isEnableApiBasedIntegrationCategory==false && this.isEnableApiBasedIntegrationType == true){
		// 	this.getMsgTagTypes();
		// } 
		
	  if(reload != 0){
		$('#bulk-edit').modal('show');
	  }
   }
   clearExtDocData(){
		this.tagDefenitionAdd.patchValue({
			tagCategory:"",
			tagTypeId: ""
				});
		this.selectedTypeValue = "";
		this.selectedCatValue = "";
   }
   closeDynamicModal() {
		this.enableDocType = false;   
		this.enableSubData = false;
		$('#bulk-edit').modal('hide');
   }
   getMsgTagTypes(cat=""){
	//    if(cat == ""){
	// 	this._structureService.getNoteTypes('documentType').then((data) => {
	// 	this.showLoader = false;
	// 	console.log('data', data);
	// 	this.noteTypes = data['documentCategory'];
	// 	this.noteTypesCount  = this.noteTypes.length;
	// 	console.log(this.noteTypes);
	// 	}).catch((ex) => {
	// 		this.showLoader = false;
	// 		this.noteTypesCount = 0;
	// 	});   
	// 	   }else{
			this._structureService.getNoteTypes('noteType',cat).then((data) => {
				console.log('data', data);	
				if(data){				 
					console.log('data', data);
					if(data['issue']){
						if(data['issue'][0]['diagnostics']['statusCode'] == 412){
						  this.noteCount = 0;
						  this.showLoader = false;
						  this.msgTagTypeCount = 1;
						} else if(data['issue'][0]['diagnostics']['statusCode'] == 406){
							this.noteCount = 0;
							this.showLoader = false;
							this.msgTagTypeCount = 4;
						  }
					}else if(data['messageTagType']){
					  this.showLoader = false;
					  this.msgTagTypes = data['messageTagType'];
					  this.msgCount  = this.msgTagTypes.length;				
				 	  console.log(this.msgTagTypes);
				 	  this.msgTypeSet = this.msgTagTypes['items']
					  this.noteTypesCount = -1;
					  this.msgTagTypeCount = -1;
					}else if(data['message']){
					  if(data['message'] == 412){
						this.msgCount = 0;
						this.showLoader = false;
						this.msgTagTypeCount = 1;
					  } else if(data['message'] == 404){
						  this.msgCount = 0;
						  this.showLoader = false;
						  this.msgTagTypeCount = 0;
					  } else if(data['message'] == 500){
						  this.msgCount = 0;
						  this.showLoader = false;
						  this.msgTagTypeCount = 0;
					  } else if(data['message'] == 0){
						  this.msgCount = 0;
						  this.showLoader = false;
						  this.msgTagTypeCount = 0;
					  } else if(data['message'] == 401){
						  this.msgCount = 0;
						  this.showLoader = false;
						  this.msgTagTypeCount = 2;
					  } else if(data['message'] == "Invalid Parameter"){
						  this.msgCount = 0;
						  this.showLoader = false;
						  this.msgTagTypeCount = 3;
					  } else if(data['message'] == 406){
						  this.msgCount = 0;
						  this.showLoader = false;
						  this.msgTagTypeCount = 4;
					  }
					}				  				
				  }else{					
					this.showLoader = false;
				  	this.msgTagTypeCount = 0;
				  }								
			})	
		   //}	
   }
   onSelectCategory(event){
	// this.tagDefenitionAdd.patchValue({
    //     tagCategory:"",
    //     tagTypeId: ""
    //       });
    //   this.selectedTypeValue = ""
    //   this.selectedCatValue = ""
    //   this.selectedCatName = ""
	//   this.selectedTypeName = ""
	if(event.target.value == ""){
		this.enableSubData = false
	}
		this.selectedCatValue = event.target.value;     
		this.selectedCatName =  event.target.selectedOptions[0].text;
		if(this.isEnableApiBasedIntegrationCategory==true && this.isEnableApiBasedIntegrationType == true && this.selectedCatValue !=""){
		this.getMsgTagTypes(this.selectedCatValue);
		} 
		// else if(this.isEnableApiBasedIntegrationCategory==false && this.isEnableApiBasedIntegrationType == true){
		// 	this.msgTagTypes = this.noteTypes.find(function (item) {          
		// 		return item.id == event.target.value;     
		//    });
		//    console.log("saaa", this.msgTagTypes)
		//    this.msgTypeSet = this.msgTagTypes['documentType']
		// }

		// this._structureService.getNoteTypes('noteType','ProgressNote').then((data) => {
		// 	console.log('data', data);
		// 	this.msgTagTypes = data['messageTagCategory'];
		// 	this.msgTagTypeCount  = this.msgTagTypes.length;
		// 	console.log(this.msgTagTypes);
		// 	this.msgTypeSet = this.msgTagTypes['messageTagType']
		// });				
		this.enableDocType = true;   
   }
   onSelectType(event){
	   	this.selectedTypeValue = event.target.value;     
		this.selectedTypeName = event.target.selectedOptions[0].text;
		if(event.target.value != "" && this.selectedCatValue != ""){
			this.enableSubData = true
		}else{
			this.enableSubData = false
		}
   }
   setSelectedData(){
		this.tagDefenitionAdd.patchValue({
			 tagCategory:this.selectedCatName,
			 tagTypeId: this.selectedTypeName
		});
		this.enableDocType = false;   
		this.enableSubData = false;

		$('#bulk-edit').modal('hide');
   }
	addMessageTag(form) {
		$('.addmessagetag').attr('disabled', 'disabled');
		console.log(form.valid);
		var tagtype = '';
		tagtype = $('#tagtype').val();
		console.log('=======tagtype=======');
		console.log(tagtype);
	
		if (!form.valid) {
			// var notify = $.notify('Please Enter the Message Tag Details');
			//     setTimeout(function() {
			//         notify.update({'type': 'warning', 'message': '<strong>Please Enter the Message Tag Details</strong>'});
			//     }, 1000);
			$('.addmessagetag').removeAttr('disabled');
		}
		if(form.value.tagName)
		form.value.tagName = form.value.tagName.trim();

		let tagExists;
		if (form.value.tagName != '') {
			this.tenantId =
				typeof this.tenantId === 'undefined' ? this._structureService.getCookie('tenantId') : this.tenantId;
			let formFolderName = '';
			let formFileSaveFormat = '';
			let fileSaveFormatIntegration = '';
			tagExists = this.tagList.filter((row) => {
				if (row.tagName.toLowerCase() == form.value.tagName.trim().toLowerCase()) {
					return true;
				}
			});
			if (tagExists.length > 0) {
				var notify = $.notify('Message Tag already exists');
				setTimeout(function() {
					notify.update({ type: 'warning', message: '<strong>Message Tag already exists</strong>' });
				}, 1000);
				$('.addmessagetag').removeAttr('disabled');
				return false;
			}
			if (!form.valid) {
				var notify = $.notify('Please Enter the Message Tag Details');
				setTimeout(function() {
					notify.update({
						type: 'warning',
						message: '<strong>Please Enter the Message Tag Details</strong>'
					});
				}, 1000);
				$('.addmessagetag').removeAttr('disabled');
				return false;
			}
			console.log(this.isFilingCenter);
			if (this.isFilingCenter) {
				console.log(this.folderName);
				if (!this.folderName) {
					formFolderName = '';
				} else {
					formFolderName = this.folderName.folderName;
				}
			}
			if (this.isFilingCenter&&this.userData.config.progress_note_integration_mode != 'webhook') {
				console.log(form.value.fileSaveFormat);
				formFileSaveFormat = form.value.fileSaveFormat;
				fileSaveFormatIntegration = form.value.fileSaveFormatIntegration;
			}
			if (this.folderNameJson && this.folderNameJson.folderName) {
				this.folderNameJson.folderName = this.folderNameJson.folderName;
			} else {
				this.folderNameJson = { folderName: '' };
			}

			let selectedNursingTags = [];
			this.nursingAgencyUserTagSelected = $('#nursing-agency-user-tags').val();
			if (this.nursingAgencyUserTagSelected) {
				this.nursingAgencyUserTagSelected.forEach((element) => {
					let member = { id: '' };
					let id = element.substr(element.indexOf(':') + 1);
					id = id.replace(/'/g, '');
					member.id = id.replace(/\s/g, '');
					selectedNursingTags.push(Number(member.id));
				});
			}
				var triggeron=$("#triggerOn").val();
				if(this.userData.config.enable_progress_note_integration == '1'){       
					triggeron = triggeron ? triggeron :'add-tag';
				}else{
					triggeron ='';
				}
				console.log(form.value.tagTypeId);
				console.log(form.value.tagCategory);
				if((this.isEnableMessageCategory && !this.isEnableMessageType) && ((!this.isEnableApiBasedIntegrationCategory || !this.isEnableApiBasedIntegrationType) || (!this.isEnableApiBasedIntegrationCategory && !this.isEnableApiBasedIntegrationType))){
					var externalIntegrationSettingsList = {
						messageTagCategoryId:form.value.messagecatetypeid,
						messageTagCategoryName: "",
						messageTagTypeId: form.value.messagecatecode,
						messageTagTypeName:""
					}
				} else {
					 externalIntegrationSettingsList = {
						messageTagCategoryId:(this.isEnableApiBasedIntegrationCategory == true && this.isEnableApiBasedIntegrationType == true) ?this.selectedCatValue:form.value.tagCategoryId,
						messageTagCategoryName: (this.isEnableApiBasedIntegrationCategory == true && this.isEnableApiBasedIntegrationType == true) ? form.value.tagCategory:form.value.tagCategory,
						messageTagTypeId: (this.isEnableApiBasedIntegrationCategory == true && this.isEnableApiBasedIntegrationType == true)  ? this.selectedTypeValue:form.value.tagType,
						messageTagTypeName: (this.isEnableApiBasedIntegrationCategory == true && this.isEnableApiBasedIntegrationType == true) ?form.value.tagTypeId:form.value.tagTypeId,
					}
				}
				
			let tagMeta = {
				outgoingFilingCenter: formFolderName,
				fileSaveFormat: formFileSaveFormat,
				summarizeOutcomeMeasure: this.outcomeMeasures,
				approvalRequired: this.approvalRequired,
				patientFacing: this.patientFacing,
				enableIntegration: this.enableIntegration,
				enableNotification: this.enableNotification,
				integrationFC: this.folderNameJson.folderName,
				triggeron: triggeron,
				fileSaveFormatIntegration: fileSaveFormatIntegration,
				nursingAgencyUserTagSelected: selectedNursingTags ? selectedNursingTags : [],
				messageCategoryCode:form.value.messageCategoryCode,
				messageTypeID:form.value.messageTypeID,
				externalIntegrationSettings: externalIntegrationSettingsList
			};
             console.log(tagMeta);
			var saveTag = {
				id: '',
				name: form.value.tagName,	
				type: Number(this.type),
				tenantId:
					this._structureService.getCookie('crossTenantId') &&
					this._structureService.getCookie('crossTenantId') != 'undefined' &&
					this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')
						? Number(this._structureService.getCookie('crossTenantId'))
						: Number(this.tenantId),
				tagMeta: tagMeta,
				typeId: Number(tagtype)
			};
			console.log(saveTag);
			let res: any;
			this._structureService
				.saveMessageTag(saveTag)
				.then((result) => {
				 res = result;
				 if(res.error!=="Invalid Input")
				  {
					if (res.status == false) {
						var notify = $.notify('Message Tag already exists');
						setTimeout(function() {
							notify.update({ type: 'warning', message: '<strong>Message Tag already exists</strong>' });
						}, 1000);
						$('.addmessagetag').removeAttr('disabled');
					} else {
						console.log(result);
						var notify = $.notify('Success! Message Tag created');
						setTimeout(function() {
							notify.update({
								type: 'success',
								message: '<strong>Success! Message Tag created</strong>'
							});
						}, 1000);

						var activityData = {
							activityName: 'add message tag',
							activityType: 'manage message tag',
							activityDescription:
								this.userData.displayName + ' created message tag - ' + form.value.tagName
						};
						this._structureService.trackActivity(activityData);
						this.router.navigate([ '/message/tag-definitions' ]);
						$('.addmessagetag').removeAttr('disabled');
						$('#hideReset').trigger('click');
						this.showClose = false;
						this.folderName = '';
						if (this.isFilingCenter) this.clearFilingCenter();
						this.tagDefenitionAdd = new FormGroup({
							tagName: new FormControl(null, Validators.required),
							filingcenter: new FormControl(),
							fileSaveFormat: new FormControl(
								'{tagname}-{FirstName}-{LastName}-{DOB}-{createdOn}-{signedOn}'
							),
							fileSaveFormatIntegration: new FormControl('{MRN}-{UniqueNO}')
						});
						$('.selectd').select2({
							placeholder: 'Select Filing Center'
						});
						this.folderName = this.folderLists[0];
						//this.router.navigate(['/message/tag-definitions']);
					}

				} else {
					let activityData = {
						activityName: "Error Add Message Tag",
						activityType: "messagetag",
						activityDescription: `Error occured while Adding Message Tag due to invalid input. `
					  }; 
					  this._structureService.notifyMessage({
						messge: this._ToolTipService.getTranslateData('MESSAGES.ERROR_MSG_INVALID_INPUT'),
						delay: 1000,
						type: 'warning'
					  });
					  this._structureService.trackActivity(activityData);
					  $('.addmessagetag').attr('disabled', false);
					}
				
				})
				.catch((ex) => {
					//this.loginFailed = true;
				});
		}
	}
	hideAddError() {
		this.showAddErrorMessage = false;
	}
	togglePreference(preference, value) {
		if(this.userData.config.progress_note_integration_mode !== 'webhook'){
			if(this.multiSiteEnable){
			  this.enableIntrationDiv=true;
			}
		}
		if (preference === 'outcomeMeasures') {
			console.log(value);
			this.outcomeMeasures = value;
			this.tagDefenitionAdd.patchValue({
				outcomeMeasures: value
			});
		}
		if (preference === 'approvalRequired') {
			console.log(value);
			if (value) {
				$('#triggerOn').val('approve-tag');
			} else {
				$('#triggerOn').val('add-tag');
			}
			this.approvalRequired = value;
			this.tagDefenitionAdd.patchValue({
				approvalRequired: value
			});
		}
		if (preference === 'patientFacing') {
			console.log(value);
			this.patientFacing = value;
			this.tagDefenitionAdd.patchValue({
				patientFacing: value
			});
		}
		if (preference === 'enableIntegration') {
			console.log(value);
			this.enableIntegration = value;
			this.tagDefenitionAdd.patchValue({
				enableIntegration: value
			});
			if(!this.enableIntegration){
				this.tagDefenitionAdd.patchValue({
				  tagCategory:"",
				  tagTypeId: ""
					});
				this.selectedTypeValue = "";
				this.selectedCatValue = "";
			  }
		}
		if (preference === 'enableNotification') {
			this.enableNotification = value;
		}
	}
	showAddError() {
		this.showAddErrorMessage = true;
	}
	showEditError() {
		this.showEditErrorMessage = true;
	}

	getNursingAgencyTags() {
		const userData: any = this._structureService.getUserdata();
		const tagGetData = '?userId=' + userData.userId + '&tenantId=' + userData.tenantId;
		const tagTypes = [ '2' ]; // Message Tag =1, User Tag =2 , Document Tag =3
		this._structureService.getNursingAgencyTagsByGroup(tagGetData, tagTypes).then((data: any) => {
			this.nursingAgencyTags = data;
		});
	}
}
