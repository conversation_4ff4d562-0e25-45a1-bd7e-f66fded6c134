<!-- START: tables/datatables -->
<section class="card">
  <div class="card-header row">
    <span class="cat__core__title col-md-4">
      <strong>Patient Discussion Groups</strong>
    </span>
    <div class="col-md-5">
      <div class="col-md-12" style="float: right; display: contents" [hidden]="!hideSiteSelection">
        <div class="filter-site row">
          <div class="site-label">
            <span>{{ labelSiteFilter | translate }}</span>
          </div>
          <div class="col-md-8" style="width: 73%">
            <app-select-sites
              [events]="eventsSubject.asObservable()"
              [filterType]="true"
              (siteIds)="getSiteIds($event)"
              (hideDropdown)="hideDropdown($event)"
            >
            </app-select-sites>
          </div>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <button id="add_pat_disc_gp" class="pull-right btn btn-sm btn-primary" (click)="openPatientCreationModal()">
        {{ 'LABELS.ADD_PATIENT' | translate }}<i class="ml-1"></i>
      </button>
    </div>
  </div>
  <div class="card-block">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
      <li class="breadcrumb-item"><a [routerLink]="['/message/message']">Message Center</a></li>
      <li class="breadcrumb-item">Patient Discussion Groups</li>
    </ol>

    <div class="row page-height">
      <div class="col-md-8 ml-5">
        <app-patient-dropdown [siteIds]="selectSiteId" (selectedItem)="setPdgIdAndAdmissionId($event)"></app-patient-dropdown>
        <div class="form-group row">
          <div class="col-md-3 pt-1"></div>
          <div class="col-md-8 pt-2 btn-edit-div-width" *ngIf="selectedPatientId">
            <button
              class="btn btn-primary btn-sm btn-edit pt-2 p-2 float-right"
              type="button"
              [disabled]="+userConfig.enable_multi_admissions && !admissionId"
              (click)="doEdit()"
            >
              <i class="fa fa-pencil mr-1"></i>{{ 'BUTTONS.EDIT_PDG' | translate }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
<!-- END: tables/datatables -->
<!-- Modal for adding new patient -->
<div class="modal fade forward-modal associated-patient" id="assocModal" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-llg" role="PDG">
    <div class="modal-content">
      <app-associate-patient-modal
        [title]="'LABELS.ADD_PATIENT' | translate"
        [config]="addPatientConfig"
        [selectedSites]="selectSiteId"
        (closeModal)="handleModalClose($event)"
        (patientDetails)="getPatientData($event)"
        data-animation="false"
      ></app-associate-patient-modal>
    </div>
  </div>
</div>
<ch-loader [showInPageCenter]="true" [showLoader]="!selectSiteId"></ch-loader>
