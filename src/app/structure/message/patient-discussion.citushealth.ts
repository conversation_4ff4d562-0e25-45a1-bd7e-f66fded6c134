import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';

declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Component({
  selector: 'app-patient-discussion',
  templateUrl: './patient-discussion.html'
})


export class PatientDiscussionGroupComponent implements OnInit {
  
   editGroup = false;
  addGroup = false;
  editGroupMessage = false;
  groupList = [];
  staffList = [];
  memberList = [];
  selected;
  newMemberList=[];
  newMember;
  existingMemberIds=[];
  selectedGroupMembers=[];
  selectedGroupNewMembers=[];
  checkedIds=[];
  selectedisPublic;
  messageGroup:FormGroup;
  updateParams;
  constructor(private route: ActivatedRoute,
    private router: Router,
    private _structureService: StructureService,
    private _formBuild: FormBuilder

  ) { }

  ngOnInit() {
    console.log('testing');
    this.messageGroup = this._formBuild.group({
       messageGroupName : [''],


    });
    this._structureService.previousUrl = 'message/editmessagegroup';
    this._structureService.getUserList().then(({ data }) => {

      this.staffList = data['getSessionTenant'].staffUsers;
      
      
    });
  


  }

  showMessageGroupDetails(groupId){
       this._structureService.getMessageGroupMembers(groupId).refetch().then(({ data }) => {

          this.memberList = data['getMessageGroupMembers'];
        
          
        });
  }
  gotoMessageGroup() {
    this.router.navigate(['/message/messagegroup']);
  }
  editMessageGroup() {
    this.router.navigate(['/message/editmessagegroup']);
  }
  addGroupMember() {
    this.router.navigate(['/message/addgroupmember']);
  }

  editGroupName(group) {
    this.editGroupMessage= false;
    this.checkedIds=[];
    this.editGroup = true;
    this.addGroup = false;
    this.selected = group;
    this.selectedGroupMembers =[];
    this.selected.groupMembers.forEach(element => {
         this.newMember  ={
              id:"",
              displayName:""
         };
         this.newMember.id=element.id;
         this.newMember.displayName=element.displayName;
         this.selectedGroupMembers.push(this.newMember);

    });
   
    this.selectedisPublic = group.isPublic;
    this.messageGroup.patchValue({
       messageGroupName:this.selected.name
    });
    group.groupMembers.forEach(value => {
      this.existingMemberIds.push(value.id);
    });
   
    this.staffList.forEach(element => {
        this.newMember  ={
              id:"",
              displayName:""
         }
        if (!this.existingMemberIds.includes(element.id)){
            this.newMember.id=element.id;
            this.newMember.displayName=element.displayName;
            this.newMemberList.push(this.newMember);
        }
        
          
    });
   
    
  }
  saveGroupname() {
    this.editGroup = false;
    this.addGroup = false;
    
  }
  addMessageGroup() {
    this.editGroupMessage= false;

    this.editGroup = false;
    this.addGroup = true;
    this.checkedIds=[];
    this.editGroup = true;
    this.addGroup = false;
    this.selected = {};
    this.selectedGroupMembers =[];
   
    this.selectedisPublic = false;
    this.messageGroup.patchValue({
       messageGroupName:''
    });
    this.existingMemberIds=[];
   
   
    this.staffList.forEach(element => {
        this.newMember  ={
              id:"",
              displayName:""
         }
        if (!this.existingMemberIds.includes(element.id)){
            this.newMember.id=element.id;
            this.newMember.displayName=element.displayName;
            this.newMemberList.push(this.newMember);
        }
        
          
    });
  }
  cancel() {
    this.editGroup = false;
    this.addGroup = false;
   
   
    
  }
  selectGroup(group){
    this.selected=group;
    
  }
  updateCheckedOptions(id,event){
    
    if (event.target.checked) {
        this.checkedIds.push(id);
    } else {

      var index = this.checkedIds.indexOf(id);
      if (index > -1) {
          this.checkedIds.splice(index, 1);
      }

    }
    
   
  }

  removeMember(id){
       this.selectedGroupMembers.splice(id, 1);
  }
  addMember(){
    
    this.staffList.forEach(element => {
        this.newMember  ={
              id:"",
              displayName:""
         }
        if (this.checkedIds.includes(element.id)){
            this.newMember.id=element.id;
            this.newMember.displayName=element.displayName;
            this.selectedGroupMembers.push(this.newMember);
            var index = this.newMemberList.findIndex(x => x.id==element.id);
            this.newMemberList.splice(index,1);
            

        }
       
        
          
    });
     
  }
  updateMessageGroup(id){
    this.updateParams ={
        name:this.messageGroup.controls['messageGroupName'].value,
        isPublic:this.selectedisPublic,
        type:"Patient_Discussion_Group",
        id:id,
        groupMembers:this.selectedGroupMembers
    }
    
  
    this._structureService.createMessageGroup(this.updateParams);
    this.editGroupMessage= true;
    this.router.navigate(['/message/message']);
  

   

  }
  togglePublic(value){
    this.selectedisPublic = value;
  }
  deleteGroup(id){
       this._structureService.deleteMessageGroup(id);
      
         this.router.navigate(['/message/message']);
   
  }

}

