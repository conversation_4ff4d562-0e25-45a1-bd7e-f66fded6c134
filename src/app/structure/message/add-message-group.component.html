<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Add Message Groups  </strong>
        </span>
        <span class="pull-right">
            <button class="btn btn-sm btn-primary" (click)="goToList()">{{'BUTTONS.BACK' | translate}}</button>
        </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/message']">Message Center</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/message']">Message Groups</a></li>
            <li class="breadcrumb-item">Add Message Groups</li>
        </ol>

    </div>
</section>
<section class="row chatroom-section">
    <div class="col-lg-12">
        <section class="card">
            <div class="card-block">

                <form class="form-horizontal" [formGroup]="messageGroup" (ngSubmit)="updateMessageGroup(selected.id,f)" #f="ngForm">



                    <div class="form-group row">
                        <div class="col-md-3">
                            <label>Message Group Name *</label>
                        </div>
                        <div class="col-md-6">
                            <input type="text" [readonly]="!allowSave && !privilegeManageAllMessageGroup" class="form-control" formControlName="messageGroupName"
                                placeholder="Message Group Name" id="messagegroupName">
                            <div *ngIf="messageGroup.controls['messageGroupName'].errors&&(messageGroup.controls.messageGroupName?.dirty ||messageGroup.controls.messageGroupName?.touched || f.submitted)"
                                class="alert alert-danger">
                                Please enter group name
                            </div>
                        </div>
                    </div>
                    <div class="form-group row" *ngIf="multiSiteEnable" [hidden]="!hideSiteSelection">
                        <div class="col-md-3">
                            <label class="control-label">Site * <i chToolTip="Site"></i></label>
                        </div>
                        <div class="col-md-6">
                                <app-select-sites  [events]="eventsSubject.asObservable()"  [hideApplyFilter]=true [singleSelection]=singleSelection
                                (siteIds)="getSiteIds($event,1)" (hideDropdown)="hideDropdown($event)">
                                </app-select-sites>
                            <span id="error-form" class="error"></span>
                        </div>
                    </div>
                    <div class="form-group row" [hidden]="!allowSave && !privilegeManageAllMessageGroup">
                        <div class="col-md-3">
                            <label class="control-label">Make this Group Public </label>
                            <i class="make-public icmn-info"></i>
                        </div>
                        <div class="col-md-6">

                            <div class="btn-group">
                                <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': selectedisPublic}"
                                    (click)="togglePublic(true)">
                                  Yes
                            </button>
                                <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !selectedisPublic}"
                                    (click)="togglePublic(false)">
                                No
                            </button>
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-md-3">
                            <label class="control-label">Allow multi thread chat</label>
                            <i class="multi-chat-thread icmn-info"></i>
                        </div>
                        <div class="col-md-6">
                            <div class="btn-group">
                                <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': selectedAllowMultiThread}" (click)="toggleMultiThreadOption(true)"> Yes </button>
                                <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !selectedAllowMultiThread}" (click)="toggleMultiThreadOption(false)"> No </button>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-4">
                            <br>

                            <section class="card existing">
                                <div class="card-header">
                                    <span class="cat__core__title">
                                    <strong>Existing Members</strong>
                                </span>
                                </div>
                                <div class="card-block">
                                    <div class="cat__apps__messaging__header">
                                        <input class="form-control cat__apps__messaging__header__input"  id="search_member" placeholder="Search Member(s)..." #existingMembers/>
                                        <i class="icmn-search"></i>
                                        <button type="button"></button>
                                    </div>
                                    <div class="member-list">
                                        <table class="table table-hover nowrap dataTable msg-group">

                                            <tbody>
                                                <tr *ngFor="let member of selectedGroupMembers | MessageGroupSearchFilter:existingMembers.value;">
                                                    <td>{{member.displayName}}&nbsp;<span *ngIf="member.naTagNames && member.naTagNames != ''">({{member.naTagNames}}) </span>&nbsp;<br><span *ngIf="userData.tenantId!=member.tenantId">[{{member.tenantName}}]</span></td>
                                                    <td> <a [hidden]="!allowSave && !privilegeManageAllMessageGroup" href="javascript: void(0);"
                                                            (click)="removeMember(member.id);"><small><i class="icmn-cross"></i></small></a></td>
                                                </tr>


                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <div *ngIf="noMemberError" class="card-footer">
                                    <div class="alert alert-danger">Group members cannot be empty </div>
                                </div>
                            </section>

                            <section class="card existing role-list">
                                <div class="card-header role-header">
                                    <span class="cat__core__title">
                                        <strong>Existing Roles</strong>
                                    </span>
                                </div>
                                <div class="card-block">
                                    <div class="member-list role-member-list">
                                        <table class="table table-hover nowrap" id="example1">
                                            <tbody *ngFor="let role of checkedRoleIds; let i = index"> 
                                                <tr>
                                                    <td class="width-80"> {{role.name}}&nbsp;<br><span *ngIf="userData.tenantId!=role.tenantId">[{{role.tenantName}}]</span></td>
                                                    <td> <a href="javascript: void(0);" (click)="removeExistingRole(role.id);"><small><i class="icmn-cross"></i></small></a></td>
                                             
                                                    <td> <i class="fa fa-plus expand-icon-{{role.id}} expand-icon-color" (click)="callAccordion(role.id,i,'selectedRoles')" ></i> </td> 
                                                </tr>
                                                <tr>
                                                    <span *ngIf="(!usersInRole && clickedRole == role.id)" class="loader-message">{{ 'MESSAGES.NO_USER_FOUND' | translate }}</span>
                                                    <span *ngIf="(isStaffsloadingTime && clickedRole==role.id)" class="loader-message">{{ 'MESSAGES.LOADING_MSG' | translate }}</span>
                                                    <ul class="sub-item-panel sub-item-panel-{{role.id}}">
                                                        <li *ngFor="let user of role.userList" [hidden]="user.searchHideStatus">
                                                       <span style="line-height: 30px;padding-left:5px;">{{user.name}}<span *ngIf="user.naTagNames">({{user.naTagNames}})&nbsp;</span></span>
                                                        </li>
                                                    </ul>
                                                    
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </section>
                        </div>
                        <div class="col-lg-8 pull-right">
                            <br>

                            <section class="card" [hidden]="!allowSave && !privilegeManageAllMessageGroup">

                                <div class="card-header">
                                    <span class="cat__core__title">
                                    <strong>New Roles/Members</strong>
                                    <input type="button" style="margin-top:18px;"  [disabled]="!membersLoaded" [hidden]="!allowSave && !privilegeManageAllMessageGroup" class="btn btn-sm btn-primary pull-right"
                                    (click)="addMember();" name="" value="Add" />
                                </span>
                                </div>
                                <div class="card-block">

                              

                                    <div class="form-group row">
                                        
                                        <div class="chatwith-modal-tab chatwith-modal-tab-mar" style="margin-left: 55px;">                                       
                                         <div class="chatwith-model-head col-md" (click)="optionShow = 'groups'" [class.cat__apps__messaging__tab--selected]="optionShow=='groups'">PDGs</div>
                                        <div class="chatwith-model-head col-md" (click)="showData('msggroups')" [class.cat__apps__messaging__tab--selected]="optionShow=='msggroups'">Message Groups</div>
                                        <div class="chatwith-model-head col-md"  (click)="showData('staffroles')" [class.cat__apps__messaging__tab--selected]="optionShow=='staffroles'"> Staff Roles </div>
                                        <div class="chatwith-model-head col-md"  (click)="showDataStaff('staff')" [class.cat__apps__messaging__tab--selected]="optionShow=='staff'">Staff</div>
                                        <div class="chatwith-model-head col-md" (click)="showData('partnerroles')" [class.cat__apps__messaging__tab--selected]="optionShow=='partnerroles'"> Partner Roles </div>
                                        <div class="chatwith-model-head col-md" (click)="showDataStaff('partner')" [class.cat__apps__messaging__tab--selected]="optionShow=='partner'">Partners</div>    

                                        </div>
                                       
                                       
                                        <div class="row chat-with-wrapper-filter"   style="width: 100%;margin-top: 20px;" >
                                            <!-- <div class="col-md-5 chat-with-wrapper-filter-tenant"  *ngIf="((optionShow =='staff' ||  optionShow =='partner' || optionShow == 'staffroles' || optionShow == 'partnerroles') && staffTenantFilter.filterEnabled && staffTenantFilter.tenants.length)"> -->
                                                <!-- <div class="row">
                                                    <div class="col-md-3 chat-with-wrapper-filter-tenant-label" style="padding:10px">
                                                        <span>Branch:&nbsp;</span>
                                                    </div>
                                                    <div class="col-md-9 chat-with-wrapper-filter-tenant-select" id="staffListblock">
                                                        <select class="form-control" data-placeholder="None Selected" id="staffTenantFilter"   (change)="filterBranch($event)" >
                                                            <option *ngFor="let tenant of staffTenantFilter.tenants" value="{{tenant.id}}" [selected]="tenant.id == staffTenantFilter.selectedTenant" > {{tenant.tenantName}} </option>
                                                        </select>
                                                    </div>
                                                </div> -->
                                                <!-- || (optionShow == 'staff' && userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 && configData.allow_multiple_organization!==1 ) -->
                                            <!-- </div> -->
                                            <div class="col-sm-6" style="padding-left: 19px" >
                                                <div class="row" *ngIf="((optionShow !='groups') && (optionShow !='msggroups') && staffTenantFilter.filterEnabled && staffTenantFilter.tenants.length  && !multiSiteEnable )" [hidden]="!hideSiteSelection"
                                                >
                                                    <div class="site-label">
                                                    <span>Site:&nbsp;</span>
                                                </div>
                                                
                                                <div style="width: 80%" >
                                                    <app-select-sites [events]="eventsSubject.asObservable()" (siteIds)="getBranchIds($event)" (hideDropdown)="hideDropdown($event)" [hideApplyFilter]=true [crossSite]=true>
                                                    </app-select-sites>
                                                </div>

                                          </div>
                                          <div class="row"  *ngIf="((enableCrossSite && multiSiteEnable ) && (optionShow=='staff' || optionShow=='partner'))" [hidden]="!hideSiteSelectionApply"
                                          >
                                              <div class="site-label">
                                              <span>Site:&nbsp;</span>
                                          </div>
                                          
                                          <div style="width: 80%" *ngIf="((enableCrossSite && multiSiteEnable ) && (optionShow=='staff' || optionShow=='partner'))">
                                              <app-select-sites [events]="eventsSubject.asObservable()" (siteIds)="getSiteIdsApply($event)" (hideDropdown)="hideDropdownApply($event)"  [selectedSiteIds]="messageSiteId" [crossSiteCommunication]=true [messageGroup]=true [dynamic] = true>
                                              </app-select-sites>
                                          </div>
                                      
                                    </div>
                                            </div>
                                            <div class="chat-with-wrapper-search col-sm-6" [ngClass]="{'filter-enabled': ((optionShow !='groups') && (optionShow !='msggroups') && staffTenantFilter.filterEnabled && staffTenantFilter.tenants.length)}">
                                                <div class="row">
                                                <div class="col-sm-9">
                                                    
                                                <input type="text" [(ngModel)]="searchText" [hidden]="optionShow !='staffroles' && optionShow !='partnerroles'" [ngModelOptions]="{standalone: true}" class="search-width-grp form-control cat__apps__messaging__header__input" placeholder="Search Role(s)..." #newMembers (keyup)="searchOnKeyPress($event)" id="userSearchTxtRoles" />
                                            

                                                    <div style="width: 92%;">
                                                    <input [hidden]="optionShow ==='staffroles' || optionShow ==='partnerroles' || optionShow === 'groups'" (keydown)="searchOnEnter($event,optionShow)" class="form-control cat__apps__messaging__header__input groups-srch-width" id="chat-with-modal-search-box" placeholder="Search Here" #userSearch>
                                                    </div>
                                                </div>
                                                <div class="col-sm-3">
                                                <button [hidden]="optionShow ==='staffroles' || optionShow ==='partnerroles' || optionShow ==='groups'" [disabled]="this.srch.nativeElement.value.trim() == ''" type="button" class="btn btn-sm btn-primary srchBtn" title="Search" (click)="search(optionShow)">Search</button>
                                                <button [hidden]="optionShow ==='staffroles' || optionShow ==='partnerroles' || optionShow ==='groups'" type="button" class="btn btn-sm btn-default resetBtn" title="Reset" (click)="reset(optionShow)">Reset</button>
                                                </div>
                                                </div>
                                               </div>
                                               <span *ngIf="(isLoadingRoles==true)" style="right: 10%; margin-left: 10%; color:#acb7bf;">Loading Roles...</span>


                                        </div>

                                        <ul class="treeview treeview-section-ui" style="width:100%;" *ngIf="optionShow=='staff'">
                                          <li class="forward-model-option-user-list" *ngFor="let user of staffUserList;">                                                   
                                            <input type="checkbox" name="cliniciansRoleUser[]" id = "{{user.userid}}" (change)="checkboxChanged($event)"  value="{{user.userid}}">
                                            <label for="cliniciansRoleUser[]" class="custom-unchecked">{{user.displayname}} <span *ngIf="user.naTagNames && user.naTagNames != ''">({{user.naTagNames}}) </span></label>
                                         </li>
                                         <li class="chat-with-empty-data" *ngIf="!(staffUserList).length && !staffLoader.staffs" >No Clinicians Available. <span  *ngIf="userSearch.value"><span style="color: #0088ff;cursor: pointer;" (click)="reset(optionShow)"> Click Here </span> to Reset the Search.</span></li>                    
                                            <div *ngIf="staffLoader.staffs" class="loading-container">
                                            <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                            <div class="loading-text">Loading Clinicians... </div>                                                 
                                                   
                                                </div>
                                                <div style="text-align: center;width:100%;margin-top: 14px;float: left;">
                                                    <button type="button" [hidden]="!(optionShow=='staff' && !noMoreItemsAvailable.users && staffUserList.length)" class="btn btn-sm btn-default" (click)="loadMoreUsers('staff',loadMoreSearchValue, true)">{{loadMoremessage.users}}</button>
                                                  </div>
                                             </ul>

                                             <ul class="treeview treeview-section-ui" style="width:100%;" *ngIf="optionShow=='partner'">
                                                <li class="forward-model-option-user-list" *ngFor="let user of staffUserList;">                                                   
                                                  <input type="checkbox" name="cliniciansRoleUser[]" id = "{{user.userid}}" (change)="checkboxChanged($event)"  value="{{user.userid}}">
                                                  <label for="cliniciansRoleUser[]" class="custom-unchecked">{{user.displayname}}</label>
                                                  <span *ngIf="user.naTagNames && user.naTagNames != ''">({{user.naTagNames}}) </span>
                                               </li>
                                               <li class="chat-with-empty-data" *ngIf="!(staffUserList).length && !staffLoader.partner" >No Partners Available. <span  *ngIf="userSearch.value"><span style="color: #0088ff;cursor: pointer;" (click)="reset(optionShow)"> Click Here </span> to Reset the Search.</span></li>                    
                                                  <div *ngIf="staffLoader.partner" class="loading-container">
                                                  <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                                  <div class="loading-text">Loading Partners... </div>                                                 
                                                         
                                                      </div>
                                                      <div style="text-align: center;width:100%;margin-top: 14px;float: left;">
                                                          <button type="button" [hidden]="!(optionShow=='partner' && !noMoreItemsAvailable.users && staffUserList.length)" class="btn btn-sm btn-default" (click)="loadMoreUsers('partner',loadMoreSearchValue, true)">{{loadMoremessage.users}}</button>
                                                        </div>
                                                   </ul>

                                                    <ul class="treeview treeview-section-ui rolesUl" style="width:100%;" *ngIf="optionShow ==='staffroles'">
                                                        <!-- | searchRolefilterScheduleMsgGrps : 'roleData.name' : newMembers.value ; -->
                                                        <li  *ngFor="let cliniciansRole of newMemberListByRoleWise;  let i = index" 
                                                             class="role-{{cliniciansRole.roleData.id}} roleslisting">
                                                            <i class="fa fa-plus expand-icon-{{cliniciansRole.roleData.id}}" (click)="callAccordion(cliniciansRole.roleData.id,i)"></i>
                                                            <input type="checkbox" name="staffRoleWise" id="role-{{cliniciansRole.roleData.id}}" value="{{cliniciansRole.roleData|json}}" (change)="checkboxChanged($event,i)">
                                                            <label for="staffRoleWise" class="custom-unchecked">{{cliniciansRole.roleData.name }}</label>
                                                            <span *ngIf="(isStaffsloadingTime==true && clickedRole==cliniciansRole.roleData.id)" class="loader-message">Loading...</span>
                                                            <span *ngIf="(usersInRole==false && clickedRole==cliniciansRole.roleData.id)" style="right: 10%;margin-left: 50%; color:#acb7bf;">No Users Found..</span>
                                                     <ul class="sub-item-panel sub-item-panel-{{cliniciansRole.roleData.id}}" >
                                                                <li *ngFor="let cliniciansRoleUser of cliniciansRole.userList " [hidden]="cliniciansRoleUser.searchHideStatus">
                                                                    <span style="line-height: 30px;padding-left:5px;">{{cliniciansRoleUser.name}} <span *ngIf="cliniciansRoleUser.naTagNames && cliniciansRoleUser.naTagNames != ''">({{cliniciansRoleUser.naTagNames}}) </span></span>
                                                                </li>
                                                            </ul>
                                                        </li>
                                                        <li *ngIf="!newMemberListByRoleWise .length && !chatWithLoader.otherTenantstaff">No Staff Roles Available</li>
                                                        <span style="position: absolute;right:40%;display:none" id="notFoundRoles">No Staff Roles Found..</span>
                                                        <div *ngIf="chatWithLoader.otherTenantstaff" class="loading-container">
                                                            <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                                            <div class="loading-text">Loading Staff Roles...</div>
                                                        </div>          
                                                    </ul>

                                                    <!--<div *ngIf="optionShow=='roles'"  class="col-md-12">                                              
                                                        <div class="chat-with-empty-data" *ngIf="!(newMemberListByRoleWise).length && !chatWithLoader.otherTenantstaff" >No Roles Available.{{chatWithLoader.otherTenantstaff}} <span  *ngIf="userSearch.value"><span style="color: #0088ff;cursor: pointer;" (click)="reset(optionShow)"> Click Here </span> to Reset the Search.</span></div>
                                                              
                                                                                       
                                                    </div> -->

                                                <ul class="treeview treeview-section-ui rolesUl" style="width:100%;" *ngIf="optionShow ==='partnerroles'">
                                                        <!-- | searchRolefilterScheduleMsgGrps : 'roleData.name' : newMembers.value ; -->
                                                        <li  *ngFor="let cliniciansRole of newMemberListByRoleWise;  let i = index" 
                                                             class="role-{{cliniciansRole.roleData.id}} roleslisting">
                                                            <i class="fa fa-plus expand-icon-{{cliniciansRole.roleData.id}}" (click)="callAccordion(cliniciansRole.roleData.id,i)"></i>
                                                            <input type="checkbox" name="staffRoleWise" id="role-{{cliniciansRole.roleData.id}}" value="{{cliniciansRole.roleData|json}}" (change)="checkboxChanged($event,i)">
                                                            <label for="staffRoleWise" class="custom-unchecked">{{cliniciansRole.roleData.name }}</label>
                                                            <span *ngIf="(isStaffsloadingTime==true && clickedRole==cliniciansRole.roleData.id)" class="loader-message">Loading...</span>
                                                            <span *ngIf="(usersInRole==false && clickedRole==cliniciansRole.roleData.id)" style="right: 10%;margin-left: 50%; color:#acb7bf;">No Users Found..</span>

                                                            <ul class="sub-item-panel sub-item-panel-{{cliniciansRole.roleData.id}}" >
                                                                <li *ngFor="let cliniciansRoleUser of cliniciansRole.userList " [hidden]="cliniciansRoleUser.searchHideStatus">
                                                                  <span style="line-height: 30px;padding-left:5px;">{{cliniciansRoleUser.name}} <span *ngIf="cliniciansRoleUser.naTagNames && cliniciansRoleUser.naTagNames != ''">({{cliniciansRoleUser.naTagNames}}) </span></span>
                                                                </li>
                                                            </ul>
                                                        </li>
                                                        <li *ngIf="!newMemberListByRoleWise .length && !chatWithLoader.otherTenantstaff">No Partner Roles Available</li>
                                                        <span style="position: absolute;right:40%;display:none" id="notFoundRoles">No Partner Roles Found..</span>
                                                        <div *ngIf="chatWithLoader.otherTenantstaff" class="loading-container">
                                                            <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                                            <div class="loading-text">Loading Partner Roles...</div>
                                                        </div>          
                                                    </ul>

                                        <ul class="w-100" *ngIf="optionShow ==='groups'">
                                            <li>
                                                <app-pdg-members (selectedItems)="setSelectedPDGs($event)" [siteIds]="selectSiteId" [reset]="resetPdgMembers"></app-pdg-members>
                                            </li>
                                        </ul>
                                        <ul class="treeview treeview-section-ui" style="width:100%;" *ngIf="optionShow ==='msggroups'">
                                            <li *ngFor="let cliniciansGroup of memberDataGroupWise;"
                                                class="role-{{cliniciansGroup.groupId}}">
                                                <i class="fa fa-plus expand-icon-{{cliniciansGroup.groupId}}" (click)="callAccordion(cliniciansGroup.groupId)"></i>
                                                <input type="checkbox" name="messageGroup" id="role-{{cliniciansGroup.groupId}}" value="{{cliniciansGroup.groupId}}" (change)="checkboxChanged($event)">
                                                <label for="middle" class="custom-unchecked">{{cliniciansGroup.groupName }}</label>
                                                <span *ngIf="(isGrouploadingTime==true && clickedGroup==cliniciansGroup.groupId)" class="loader-message">Loading...</span>
                                                <ng-template [ngTemplateOutlet]="membersLists" [ngTemplateOutletContext]="{cliniciansGroup: cliniciansGroup, group: 'msg'}"></ng-template>
                                                <ng-template [ngTemplateOutlet]="roleLists" [ngTemplateOutletContext]="{cliniciansGroup: cliniciansGroup, group: 'msg'}"></ng-template>
                                            </li>                                           
                                        </ul>
                                    </div>
                                    <div *ngIf="optionShow === 'msggroups'" >
                                        <div *ngIf="!(memberDataGroupWise).length  && !MessageGroupWithLoader.groups" class="row">
                                            <span *ngIf="optionShow === 'msggroups'" >{{'MESSAGES.NO_MSG_GRP_AVAILABLE' | translate}}
                                            </span><span  *ngIf="srch.nativeElement.value.trim()">
                                            <a href="javascript:void(0);" class="click-here" (click)="reset(optionShow)">{{'MESSAGES.CLICK_HERE' | translate}}</a> {{'MESSAGES.TO_RESET_SEARCH' | translate}}</span></div>

                                        <div *ngIf="MessageGroupWithLoader.groups && optionShow !== 'groups'" class="loading-container">
                                            <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                            <div class="loading-text">Loading Groups...</div>
                                        </div> 
                                        
                                        <div *ngIf="optionShow === 'msggroups'" style="text-align: center;width:100%;margin-top: 14px;float: left;">
                                        <button [disabled]="MessageGroupWithLoader.groups" *ngIf="!MessageGroupWithLoader.groups" type="button" [hidden]="hideLoadMore" class="btn btn-sm btn-default" (click)="loadMoreGroups(optionShow)">
                                                <span *ngIf="!MessageGroupWithLoader.groups">Load More</span>
                                              <!--  <span *ngIf="loadingGroups">Loading Groups...</span>  -->
                                        </button>
                                        </div>


                                    </div>
                                </div>
                                <div *ngIf="addMemberError" class="card-footer">
                                    <div class="alert alert-danger">Please select member </div>
                                </div>
                            </section>
                            <!-- <input type="button" [hidden]="!allowSave && !addGroup && !privilegeManageAllMessageGroup" class="btn btn-sm btn-primary" (click)="addMember();" name="" value="Add"/> -->

                        </div>
                    </div>

                    <div class="form-actions">
                        <!--*ngIf="editGroup||addGroup"-->
                       <button type="submit" [disabled]="!messageGroup.valid || disableButton || selectSiteId == 0" class="btn btn-primary">Submit</button>
                        <!--*ngIf="addGroup"-->
                        <!-- <button *ngIf="editGroup" type="submit" [disabled]="!messageGroup.valid || disableButton" [hidden]="!allowSave && !privilegeManageAllMessageGroup" class="btn btn-primary">Update</button> -->

                        <a [routerLink]="['/message/message']" class="btn btn-default">Cancel</a>
                    </div>
                </form>

            </div>
        </section>
    </div>
</section>
<!-- END: tables/datatables -->
<ng-template #membersLists let-cliniciansGroup="cliniciansGroup" let-group="group">
  <ul class="sub-item-panel sub-item-panel-{{ group === 'pdg' ? cliniciansGroup.patientId : cliniciansGroup.groupId }}">
    <caption *ngIf="cliniciansGroup.userList && cliniciansGroup.userList.length">
      {{
        'TITLES.MEMBERS' | translate
      }}
    </caption>
    <li *ngFor="let member of cliniciansGroup.userList">
      <span class="sub-item"
        >{{ member.displayName }}
        <span *ngIf="member.naTagNames && member.naTagNames !== ''">({{ member.naTagNames }}) </span>
        <span *ngIf="+userData.tenantId !== +member.tenantId">[{{ member.tenantName }}]</span>
      </span>
    </li>
  </ul>
</ng-template>
<ng-template #roleLists let-cliniciansGroup="cliniciansGroup" let-group="group">
  <ul class="sub-item-panel sub-item-panel-{{ group === 'pdg' ? cliniciansGroup.patientId : cliniciansGroup.groupId }}">
    <caption *ngIf="cliniciansGroup.selectedRoles && cliniciansGroup.selectedRoles.length">
      {{
        'TITLES.ROLES' | translate
      }}
    </caption>
    <li *ngFor="let role of cliniciansGroup.selectedRoles">
      <span class="sub-item">{{ role.name }}&nbsp;</span>
    </li>
  </ul>
</ng-template>
