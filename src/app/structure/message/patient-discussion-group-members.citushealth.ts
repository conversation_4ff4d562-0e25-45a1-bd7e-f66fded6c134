import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';
import { FormBuilder, FormGroup, Validators, FormControl, FormArray } from '@angular/forms';


declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Component({
  selector: 'app-messagegroup',
  templateUrl: './patient-discussion-group-members.html'
})

export class PatientDiscussionGroupMemberComponent implements OnInit {
  GroupMembers: FormGroup;
  membersList = [];
  AddGroupMembers = this._formBuild.group({
      member: '',
    });
  constructor(private route: ActivatedRoute,
    private router: Router,
    private _formBuild: FormBuilder,
    private _structureService: StructureService
  ) { }
  ngOnInit() {
    this.GroupMembers = this._formBuild.group({
      member: '',
    })
  }
  linkToInbox()
  {
   // console.log("hii clicke meeeeeeeeeeeeeeeee");
    this._structureService.inboxClicked(true);
  }
  
  addGroupMember() {
    this.AddGroupMembers = this._formBuild.group({
      member: '',
    })

    this.membersList.push(this.GroupMembers.controls['member'].value);

    console.log(this.GroupMembers.controls['member']);
    console.log(this.membersList);

  }
  gotToMessageGroup() {
    console.log(this._structureService.previousUrl);
    this.router.navigate([this._structureService.previousUrl]);
  }
}

