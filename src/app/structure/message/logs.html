<section class="card">
    <div class="card-header row">
        <span class="cat__core__title col-md-6">
            <strong>Chat Logs</strong>
        </span>

        <div class="filter-sites-wrapper" [hidden]="!hideSiteSelection">
            <div class="col-md-12" style="right: -40px">
                <div class="filter-site row">
                    <div class="site-label">
                    <span>{{ filterSiteLabel | translate }}</span>
                    </div>
                    <div class="col-md-8" style="width: 73%">
                        <app-select-sites [events]="eventsSubject.asObservable()"  [filterType]=true (siteIds)="getSiteIds($event)" (hideDropdown)="hideDropdown($event)">
                        </app-select-sites>
                    </div>
                </div>
            </div>
        </div>

    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item"><a>Message Settings</a></li>
            <li class="breadcrumb-item">Chat Logs</li>
        </ol>
    </div>

    <div class="col-lg-12">
        <section class="card">
            <div class="card-header">
            </div>
            <div class="card-block">
                <div style="text-align:center;width:100%;" *ngIf="dataLoadingMsg">
                    <img src="./assets/img/loader/loading.gif" />
                </div>
                 <div class="exportData" *ngIf="!dataLoadingMsg" [hidden]="userData.enable_export_data != '1'">
                    <button daterangepicker [options]="_SharedService.exportOptions" (selected)="selectedDate($event, daterange)" class="btn btn-sm btn-default load-more">Export</button>
                    </div>
                    <div class="row">
                        <div class="col-lg-8"></div>
                        <div class="col-lg-4 date-range-align">
                            <div class="row">
                                <div class="col-lg-6 text-right padding-top-ten">
                                    <label>{{'LABELS.DATE_RANGE' | translate}}
                                        <i chToolTip="defaultDateRangeInfo" class="icmn-info" data-animation="false">&nbsp;</i>:&nbsp;
                                    </label>
                                </div>
                                <div class="col-lg-6">
                                    <ch-daterange-picker [keepSession]="true" 
                                    [dateRangeFilterOptions]="dateRangeFilterOptions" 
                                    [control]="dateRange" [saveStateInto]="dateRangeStoreKey" 
                                    (selectDateRange)="onSelectDateRange($event)" 
                                    [emitOnLoad]="true"></ch-daterange-picker>
                                </div>
                            </div>
                        </div>     
                    </div>   
                <table class="table table-hover nowrap click-pointer" id="chatroom-messages" width="100%">

                </table>

            </div>
        </section>
    </div>
</section>