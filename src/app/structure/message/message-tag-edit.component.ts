import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
//import { Ng2DeviceService } from 'ng2-device-detector'; 
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
declare var $:any;
@Component({
  selector: 'app-message-tag-edit',
  templateUrl: './message-tag-edit.component.html',
  styleUrls: ['./message-tag-edit.component.css']
})
export class MessageTagEditComponent implements OnInit {
  tagDefenitionAdd: FormGroup;
  tagName='';
  tagCategory='';
  tagTypeId='';
  folderName;
  msgTypeName;
  showClose=false;
  showCloseJson =false;
  showCloseMsg=false;
  showClosetag=false;
  filingCenters=[];
  tagtypeListMsg = [];
  userTagType;
  userData;
  folderLists;
  isFilingCenter;
  fileSaveFormat;
  messageTypeID;
  messageCategoryCode;
  fileSaveFormatIntegration;
  tag_type;
  tagList;
  tag_meta='';
  isDisabled = false;
  tag_id;
  tenantId;
  type = 1;
  updateId;
  tagtypeList=[];
  isLinux = false;
  isWindows = false;
  tagtypeid;
  folderNameJson;
  outcomeMeasures;
  approvalRequired;
  patientFacing = false;
  enableNotification = false;
	enableIntegration = false;
  isProgressNoteIntegration;
  nursingAgencyTags: any = [];
  nursingAgencyUserTagSelected: any = [];
  isNursingAgencyEnabled = 2;
  isMessageYypeCategoryCodeEnabled=0;
  enableIntrationDiv=false;
	multiSiteEnable=false;
  userDataConfig: any = '';
	userConfig: any;
	isEnableMessageCategory= false;
  isEnableApiBasedIntegrationCategory= false;
  authorizationKey;
  	apiEndPoint;
  	noteTypes: any = [];
  	showLoader: boolean;
     selectedExtDocumentName: any;
     selectedExtDocumentId: any;
     noteTypesCount: any;
     documentTypeSet: any;
     enableDocType: boolean;
     selectedCatValue: any;
     selectedTypeValue: any;
     selectedTypeName: any;
  tagTypeName: any;
  noteTypeCategory: any;
  isEnableApiBasedIntegrationType: boolean;
  selectedCatName: any;
  msgTagTypes: any;
  msgTagTypeCount: any;
  msgTypeSet: any;
  catLabel: string;
  typeLabel: string;
  tagCategoryId: any;
  tagType: any;
  isEnableMessageType: boolean = false;
  enableSubData = false;
  noteCount: any = 0;
	msgCount: any ;
	// externalMessageTagCategoryLabel='';
	// externalMessageTagTypeIdLabel='';
  constructor(private router: Router,
    public _structureService: StructureService,
    private route: ActivatedRoute,
    private _ToolTipService: ToolTipService,
    //private deviceService: Ng2DeviceService
  ) {
    this.tagDefenitionAdd = new FormGroup({
      tagName: new FormControl(null, Validators.required),
      tagCategory: new FormControl(null),
      tagCategoryId: new FormControl(null),
      tagType: new FormControl(null),
      tagTypeId: new FormControl(null),
      msgTagType: new FormControl(null),
      filingcenter: new FormControl(null),
      fileSaveFormat: new FormControl(null),
      fileSaveFormatIntegration: new FormControl(null),
      filingcenterjson: new FormControl(null),
      nursingAgencyUserTag: new FormControl(null),
      messageTypeID:new FormControl(null),
      messageCategoryCode:new FormControl(null),
      messagecatecode:new FormControl(null),
	    messagecatetypeid:new FormControl(null)
    });
   }


  ngOnInit() {  
  this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
	this.userConfig = this._structureService.getUserdata().config;
	this.isEnableMessageCategory = (this.userDataConfig.enable_external_message_category_code_in_message_tag == 1) ? true:false;	
  this.isEnableMessageType = (this.userDataConfig.enable_external_message_type_id_in_message_tag == 1) ? true:false;	

  this.isEnableApiBasedIntegrationCategory = (this.userDataConfig.enable_API_based_integration_for_message_category == 1) ? true:false;
	this.isEnableApiBasedIntegrationType = (this.userDataConfig.enable_API_based_integration_for_message_type == 1) ? true:false;
  if(this.isEnableApiBasedIntegrationCategory && this.isEnableApiBasedIntegrationType){
		this.catLabel = "External Message Tag Category"
		this.typeLabel = "External Message Tag Type Id"
	}else{
		this.catLabel = "External Message Tag Category Name"
		this.typeLabel = "External Message Tag Type Name"
	}
  
  // this.externalMessageTagCategoryLabel = this.userDataConfig.label_for_external_msg_tag_category ? this.userDataConfig.label_for_external_msg_tag_category : 'External Message Tag Category';
	// this.externalMessageTagTypeIdLabel = this.userDataConfig.label_for_external_msg_tag_type_id ? this.userDataConfig.label_for_external_msg_tag_type_id : 'External Message Tag Type Id';
	
    /*let deviceInfo = this.deviceService.getDeviceInfo();
    console.log(deviceInfo.os);
    if(deviceInfo.os=="linux"){
      this.isLinux = true;
    }
    else if(deviceInfo.os=="windows"){
      this.isWindows = true;
    }*/
    this.userData = this._structureService.getUserdata(); 
    /**
    * Get User tags with tag type (Nursing Agency) BioMatrix -  Nursing tags.
    */
     this.multiSiteEnable = this.userData.config.enable_multisite == 1 ? true : false;
     if(this.userData.config.progress_note_integration_mode !== 'webhook'){
			if(this.multiSiteEnable){
			  this.enableIntrationDiv=true;
			}
		}
    this.apiEndPoint = this.userData.config.api_integration_end_point;
    console.log('apiEndPoint', this.apiEndPoint);
    this.authorizationKey = this.userData.config.api_integration_authorization_key;
    console.log('authorizationKey', this.authorizationKey);
    if (this.userData.config.enable_nursing_agencies_visibility_restrictions == 1) {
      this.getNursingAgencyTags();
      this.isNursingAgencyEnabled = this.userData.config.enable_nursing_agencies_visibility_restrictions;
    }	if (this.userData.config.enable_message_type_id_category_code == 1) {
		
			this.isMessageYypeCategoryCodeEnabled = this.userData.config.enable_message_type_id_category_code;
		}

    
    if(this.userData.config.enable_filing_center=="1") {      
      this.isFilingCenter= true;
    }else{     
      this.isFilingCenter= false;
    }
    if(this.userData.config.progress_note_integration_data_format=="json") {      
    this.isProgressNoteIntegration= true;
    }else{      
    this.isProgressNoteIntegration= false;

    }
    console.log("this.isFilingCenter")
    console.log(this.isFilingCenter)
    console.log("this.isFilingCenter")
    var page = 'message-tag-definitions';
    $(".message-tag").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSGRP00001') });
    $('.message-tag-name').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00019') });
    $(".default-filing-center").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSGRP00002') });
    $(".default-filename").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSGRP00003') });  
    $(".default-tagtype").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSGRP00005') });
    $(".default-outcomeMeasure").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSGRP00007') });
    $(".default-approvalRequired").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSGRP00008') });
    $(".default-patientFacing").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSGRP00009') });
    $('.default-messagecategorycode').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00011') });
    $('.default-messagetypetooltip').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00012') });
    $('.default-enableIntegration').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00015') });
    $('.default-enableNotification').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00022') });
    $('.default-messagecatetypeid').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00020') });
	  $('.default-messagecatecode').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00021') });
    if(this.isEnableApiBasedIntegrationCategory && this.isEnableApiBasedIntegrationType){
			$('.default-messagetagcategory').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00013') });
			$('.default-messagetagtype').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00016') });

		}else{
			$('.default-messagetagcategory').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00017') });
			$('.default-messagetagtype').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00018') });
		}
		$('.default-messagetagcategoryid').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00014') });
		$('.default-messagetagtypeid').tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGRP00016') });
    $('.selectd').select2({
      placeholder: "Loading Filing Center"
    });  
    $(".selectdd").attr("disabled", false);
    $('.selectdd').select2({
    placeholder: "Select Message Tag Type"
    }); 
    $(".selectd").attr("disabled", true);
    $('#sfilingCenter').on(
      'change',
      (e) => {
        var m = $(e.target).val();
        console.log(m);
        this.folderName = JSON.parse(m);
        console.log(this.folderName);
        
      }
    );



     $('#tagtype').on(
      'change',
      (e) => {
        console.log($('#tagtype').val());
        console.log("XXXXXXXXXXXXXXXXXXXXXXXXXXXXXX")
        /* console.log(e);
        var m = $(e.target).val();
        console.log(m); */
      //  $('.select2-selection__rendered').removeAttr('title');
     
      
        this.showClosetag = true;
      
        
      }
    );
    this.route.params.subscribe((params: Params) => {
      this.updateId = params['id']; 
      this._structureService.getDocumentTagDetails(params['id']).then((res) => { 
        let d = JSON.parse(JSON.stringify(res));        
        let tag_meta =JSON.parse(d.tag_meta);
        this.tagName = d.tag_name;
        this.tag_meta = d.tag_meta;
        if((this.isEnableMessageCategory && !this.isEnableMessageType) && ((!this.isEnableApiBasedIntegrationCategory || !this.isEnableApiBasedIntegrationType) || (!this.isEnableApiBasedIntegrationCategory && !this.isEnableApiBasedIntegrationType))){
          if(tag_meta['messageCategoryCode'] && tag_meta['messageCategoryCode'] != "" && tag_meta['messageTypeID'] && tag_meta['messageTypeID'] != ""){
            this.tagDefenitionAdd.patchValue({
              messagecatecode:tag_meta['messageCategoryCode'],
		          messagecatetypeid:tag_meta['messageTypeID']
            });
          }else if(tag_meta['externalIntegrationSettings']){
            this.tagDefenitionAdd.patchValue({
              messagecatecode:tag_meta['externalIntegrationSettings']['messageTagTypeId'] ? tag_meta['externalIntegrationSettings']['messageTagTypeId']:"",
		          messagecatetypeid:tag_meta['externalIntegrationSettings']['messageTagCategoryId'] ? tag_meta['externalIntegrationSettings']['messageTagCategoryId']:""
            });
          }        
        }
        if(tag_meta['messageCategoryCode'] && tag_meta['messageCategoryCode'] != "" && this.isEnableMessageCategory && this.isEnableMessageType && (!this.isEnableApiBasedIntegrationType || !this.isEnableApiBasedIntegrationCategory)){
          this.tagType = tag_meta['messageCategoryCode'];
        }
        if(tag_meta['messageTypeID'] && tag_meta['messageTypeID'] != "" && this.isEnableMessageCategory && this.isEnableMessageType && (!this.isEnableApiBasedIntegrationType || !this.isEnableApiBasedIntegrationCategory)){
         this.tagCategoryId = tag_meta['messageTypeID'];
        }
        
        if(this.isEnableApiBasedIntegrationCategory && this.isEnableApiBasedIntegrationType && tag_meta['externalIntegrationSettings']){
          this.tagCategory = tag_meta['externalIntegrationSettings']['messageTagCategoryName'] ? tag_meta['externalIntegrationSettings']['messageTagCategoryName']:"";
          this.selectedCatValue = tag_meta['externalIntegrationSettings']['messageTagCategoryId'] ? tag_meta['externalIntegrationSettings']['messageTagCategoryId']:"";
          this.tagTypeId = tag_meta['externalIntegrationSettings']['messageTagTypeId'] ? tag_meta['externalIntegrationSettings']['messageTagTypeId']:"";
          this.selectedTypeValue = this.tagTypeId;
          this.tagTypeName = tag_meta['externalIntegrationSettings']['messageTagTypeName'] ? tag_meta['externalIntegrationSettings']['messageTagTypeName']:"";
          this.tagDefenitionAdd.patchValue({
            tagTypeId: this.tagTypeName
          });
        }
        
        if(this.isEnableMessageCategory && this.isEnableMessageType && !this.isEnableApiBasedIntegrationType && tag_meta['externalIntegrationSettings']){
          this.tagCategory = tag_meta['externalIntegrationSettings']['messageTagCategoryName'] ? tag_meta['externalIntegrationSettings']['messageTagCategoryName']:"";
          this.tagCategoryId = tag_meta['externalIntegrationSettings']['messageTagCategoryId'] ? tag_meta['externalIntegrationSettings']['messageTagCategoryId']:"";          
        }
        
        if(this.isEnableMessageType && this.isEnableMessageCategory && !this.isEnableApiBasedIntegrationType && tag_meta['externalIntegrationSettings']){                    
          this.tagTypeId = tag_meta['externalIntegrationSettings']['messageTagTypeName'] ? tag_meta['externalIntegrationSettings']['messageTagTypeName']:"";
          this.tagType = tag_meta['externalIntegrationSettings']['messageTagTypeId'] ? tag_meta['externalIntegrationSettings']['messageTagTypeId']:"";
        }
        
        this.tag_type=d.tag_type;
        this.tag_id = d.id;
        this.tagtypeid=d.tag_type_id;
        console.log("data===================");
        console.log("data===================");
        console.log("data===================");
        console.log(this.tagtypeid);
        console.log("data===================");
        console.log("data===================");
        console.log("data===================");
        //$("#tagtype select").val(this.tagtypeid).trigger("change");
        $('#tagtype option[value='+this.tagtypeid+']').attr('selected','selected');
        if(this.tagtypeid && this.tagtypeid!=0){
             this.showClosetag=true;
        }
        console.log(d);
       // this.getMessageTagList();
        if( this.isFilingCenter){
          this.getFolderLists();
        }
        if(this.tag_meta && this.tag_meta!='null' && this.tag_meta!='')
        {                
          let meta =JSON.parse(this.tag_meta);
          if(meta.triggeron){
            $("#triggerOn").val(meta.triggeron);
          } else {
            $("#triggerOn").val("");
          }
          if(meta.summarizeOutcomeMeasure){
            this.outcomeMeasures=meta.summarizeOutcomeMeasure;
          }
          if(meta.approvalRequired){
            this.approvalRequired=meta.approvalRequired;
          }
          if(meta.patientFacing){
            this.patientFacing=meta.patientFacing;
          }
          if(meta.enableIntegration){
            this.enableIntegration=meta.enableIntegration;
          }
          if (meta.enableNotification) {
            this.enableNotification = meta.enableNotification;
          }
          if (meta.nursingAgencyUserTagSelected){
            this.nursingAgencyUserTagSelected = meta.nursingAgencyUserTagSelected;
          }
          if(this.isMessageYypeCategoryCodeEnabled == 1){
            if(meta.messageTypeID){
              this.messageTypeID=meta.messageTypeID;
            }else{
              this.messageTypeID="";
            }
            if(meta.messageCategoryCode){
              this.messageCategoryCode=meta.messageCategoryCode;
            }else{
              this.messageCategoryCode="";
            }
          }
          console.log('TAGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGG');
          console.log(meta);
          console.log(this.nursingAgencyUserTagSelected.map(String));
          console.log('TAGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGGG');

          this.tagDefenitionAdd.patchValue({
            nursingAgencyUserTag: this.nursingAgencyUserTagSelected.map(String)
          });


        }
         this._structureService.getMessageTagtype().then(( data ) => {
      if(data['getSessionTenant']['messageTagTypes']) {
          var typeslist=JSON.parse (JSON.stringify(data['getSessionTenant']['messageTagTypes']));
          console.log('#######################################################');
          for(var i=0;i<typeslist.length;i++){

            //var id = '{"bgColor":"'+typeslist[i].bgColor+'","createdBy":"'+typeslist[i].createdBy+'","createdOn":"'+typeslist[i].createdOn+'","fontColor":"'+typeslist[i].fontColor+'","selected":"'+typeslist[i].selected+'","tagTypeId":"'+typeslist[i].tagTypeId+'","typeDescription":"'+typeslist[i].typeDescription+'","typeName":"'+typeslist[i].typeName+'"}';;
           // var item = {bgColor:typeslist[i].bgColor,createdBy:typeslist[i].createdBy,createdOn:typeslist[i].createdOn,fontColor:typeslist[i].fontColor,selected:typeslist[i].selected,tagTypeId:typeslist[i].tagTypeId,typeDescription:typeslist[i].typeDescription,typeName:typeslist[i].typeName};
            var item = {id:typeslist[i].tagTypeId,text:typeslist[i].typeName,type:typeslist[i].userType}
            this.tagtypeListMsg.push(item);
            if(typeslist[i].tagTypeId==this.tagtypeid){
              console.log(typeslist[i])
              this.tagtypeList[i]=typeslist[i];
              this.tagtypeList[i].selected=true;
            }
            this.tagtypeList[i]=typeslist[i];
          }

          console.log(this.tagtypeList);

          setTimeout(function() {      
            
            $('#msgTagtypess').select2({
                allowClear: true,
                placeholder: 'Select Tag Type',
                data:this.tagtypeListMsg
            });
            $('#msgTagtypess').val(this.tagtypeid).trigger('change');     
            $("#nursing-agency-user-tags").select2({});      
          }, 500);          
          console.log("data===================");
          console.log(data);
          console.log("data===================");
      }
      })   
    })
    })


    $('#sfilingCenterss').on(
      'change',
      (e) => {
        console.log($('#sfilingCenterss').val());
        /* console.log(e);
        var m = $(e.target).val();
        console.log(m); */
        setTimeout(()=>{
          $('#select2-sfilingCenterss-container').removeAttr('title');
        },1)
        if($('#sfilingCenterss').val().trim()!=''){
          this.showClose=true;
        }        
        this.folderName = {folderName:$('#sfilingCenterss').val(),type:'OUTGOING'}        
        console.log(this.folderName);
        
      }
    );
    $('#sfilingCenterjson').on(
      'change',
      (e) => {
        console.log($('#sfilingCenterjson').val());       
        setTimeout(()=>{
          $('#select2-sfilingCenterjson-container').removeAttr('title');
        },1);      
        this.showCloseJson=true;
        this.folderNameJson = {folderName:$('#sfilingCenterjson').val(),type:'OUTGOING'}        
      }
    );

    $('#msgTagtypess').on(
      'change',
      (e) => {
        console.log($('#msgTagtypess').val());

        var tagid=$('#msgTagtypess').val();
        this.tagtypeListMsg.filter((data,index)=>{
          if(data.id== tagid)
            {
             this.userTagType=   data.type;               
            }
        });

        if(this.userTagType =='patient-facing') {
          this.patientFacing=false;
        }

        /* console.log(e);
        var m = $(e.target).val();
        console.log(m); */
        setTimeout(()=>{
         // $('#select2-sfilingCenterss-container').removeAttr('title');
        },1)
        if($('#msgTagtypess').val().trim()!=''){
          this.showCloseMsg=true;
        }        
        //this.msgTypeName = {folderName:$('#sfilingCenterss').val(),type:'OUTGOING'}        
        //console.log(this.msgTypeName);
        
      }
    );
    setTimeout(function () {
      $("#nursing-agency-user-tags").select2({});
    }, 500); 
  }
  naviBack()
  {
    this.router.navigate(['/message/tag-definitions']);
  }
  getMessageTagtype
  clearFilingCenter(){
    this.showClose=false;
    this.folderName ='';
    $('#sfilingCenterss').val('');
    $('#sfilingCenterss').select2({
      allowClear: false,
      placeholder: 'Select Filing Center',
      data:this.filingCenters
  });
}
clearFilingCenterjson(){
  this.showCloseJson=false;
  this.folderNameJson ='';
  $('#sfilingCenterjson').val('');
  $('#sfilingCenterjson').select2({
    allowClear: false,
    placeholder: 'Select Filing Center',
    data:this.filingCenters
});
}
clearMsgTagType(){
     this.showCloseMsg=false;
     this.msgTypeName ='';
     $('#msgTagtypess').val('');
     $('#msgTagtypess').select2({
       allowClear: false,
       placeholder: 'Select Tag Type',
       data:this.tagtypeListMsg
   });
}
cleartagtype(){

  $("#tagtype").val('');
  $("#tagtype").select2({});
  console.log(this.tagtypeList);
  this.showClosetag=false;
  setTimeout(()=>{
    $("#tagtype").select2("open");
   /* $('#tagtype').select2({
      allowClear: false,
      placeholder: 'Select Tag Type',
      data:this.tagtypeList
  }); */
  },1)
  
}

  getMessageTagList() {
    
    this._structureService.getMessageTagList(this.type).then(( data ) => {
      if(data['getSessionTenant']) {
          this.tagList=[];
          this.tagList = data['getSessionTenant']['messageTags'];
                  
      }
      
    });
    
  }
  getExtDocData(reload){
		this.showLoader = true;
		if(this.isEnableApiBasedIntegrationCategory==true && this.isEnableApiBasedIntegrationType == true){
      this.enableDocType = false;  
      this.enableSubData = false; 
			this._structureService.getNoteTypes('noteCategory').then((data) => {
        if(data){				 
				  console.log('data', data);
				  if(data['issue']){
					  if(data['issue'][0]['diagnostics']['statusCode'] == 412){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 1;
					  } else if(data['issue'][0]['diagnostics']['statusCode'] == 406){
							this.noteCount = 0;
							this.showLoader = false;
							this.noteTypesCount = 4;
						  }
				  }else if(data['messageTagCategory']){
					this.showLoader = false;
					this.noteTypes = data['messageTagCategory'];
					this.noteTypes = this.noteTypes['items'];
					this.noteCount  = this.noteTypes.length;
					this.noteTypesCount = -1;
					this.msgTagTypeCount = -1;
					console.log(this.noteTypes);
				  }else if(data['message']){
					if(data['message'] == 412){
					  this.noteCount = 0;
					  this.showLoader = false;
					  this.noteTypesCount = 1;
					} else if(data['message'] == 404){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 0;
					} else if(data['message'] == 500){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 0;
					} else if(data['message'] == 0){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 0;
					} else if(data['message'] == 401){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 2;
					} else if(data['message'] == "Invalid Parameter"){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 3;
					} else if(data['message'] == 406){
						this.noteCount = 0;
						this.showLoader = false;
						this.noteTypesCount = 4;
					}
					}				  				
				}else{					
				  this.showLoader = false;
				  this.noteTypesCount = 0;
				}		
			})
    }
    // else if(this.isEnableApiBasedIntegrationCategory==false && this.isEnableApiBasedIntegrationType == true){
		// 	this.getMsgTagTypes();
		// } 
		
	  if(reload != 0){
		$('#bulk-edit').modal('show');
	  }
   }
   clearExtDocData(){
		this.tagDefenitionAdd.patchValue({
			tagCategory:"",
			tagTypeId: ""
				});
		this.selectedTypeValue = "";
		this.selectedCatValue = "";
   }
   closeDynamicModal() {
    this.enableDocType = false;
    this.enableSubData = false;   
		$('#bulk-edit').modal('hide');
   }
   getMsgTagTypes(cat=""){
    // if(cat == ""){
    //   this._structureService.getNoteTypes('documentType').then((data) => {
    //     this.showLoader = false;
    //     console.log('data', data);
    //     this.noteTypes = data['documentCategory'];
    //     this.noteTypesCount  = this.noteTypes.length;
    //     console.log(this.noteTypes);
    //     }).catch((ex) => {
    //       this.showLoader = false;
    //       this.noteTypesCount = 0;
    //     });
    //      }
    //      else{
          this._structureService.getNoteTypes('noteType',cat).then((data) => {
            if(data){
              console.log('data', data);
              if(data['issue']){
                if(data['issue'][0]['diagnostics']['statusCode'] == 412){
                  this.noteCount = 0;
                  this.showLoader = false;
                  this.msgTagTypeCount = 1;
                } else if(data['issue'][0]['diagnostics']['statusCode'] == 406){
                    this.noteCount = 0;
                    this.showLoader = false;
                    this.msgTagTypeCount = 4;
                    }                
              }else if(data['messageTagType']){
                this.showLoader = false;
                this.msgTagTypes = data['messageTagType'];
                this.msgCount  = this.msgTagTypes.length;				
                 console.log(this.msgTagTypes);
                 this.msgTypeSet = this.msgTagTypes['items']
                this.noteTypesCount = -1;
                this.msgTagTypeCount = -1;
              }else if(data['message']){
                if(data['message'] == 412){
                this.msgCount = 0;
                this.showLoader = false;
                this.msgTagTypeCount = 1;
                } else if(data['message'] == 404){
                  this.msgCount = 0;
                  this.showLoader = false;
                  this.msgTagTypeCount = 0;
                } else if(data['message'] == 500){
                  this.msgCount = 0;
                  this.showLoader = false;
                  this.msgTagTypeCount = 0;
                } else if(data['message'] == 0){
                  this.msgCount = 0;
                  this.showLoader = false;
                  this.msgTagTypeCount = 0;
                } else if(data['message'] == 401){
                  this.msgCount = 0;
                  this.showLoader = false;
                  this.msgTagTypeCount = 2;
                } else if(data['message'] == "Invalid Parameter"){
                  this.msgCount = 0;
                  this.showLoader = false;
                  this.msgTagTypeCount = 3;
                } else if(data['message'] == 406){
                  this.msgCount = 0;
                  this.showLoader = false;
                  this.msgTagTypeCount = 4;
                }
              }				  				
              }else{					
              this.showLoader = false;
              this.msgTagTypeCount = 0;
              }	
          })
          //  }	
    
     }
     onSelectCategory(event){
      // this.tagDefenitionAdd.patchValue({
      //   tagCategory:"",
      //   tagTypeId: ""
      //     });
      // this.selectedTypeValue = ""
      // this.selectedCatValue = ""
      // this.selectedCatName = ""
      // this.selectedTypeName = ""
      if(event.target.value == ""){
        this.enableSubData = false
      }
      this.selectedCatValue = event.target.value;     
      this.selectedCatName =  event.target.selectedOptions[0].text;
      if(this.isEnableApiBasedIntegrationCategory==true && this.isEnableApiBasedIntegrationType == true){
      this.getMsgTagTypes(this.selectedCatValue);
      }
      // else if(this.isEnableApiBasedIntegrationCategory==false && this.isEnableApiBasedIntegrationType == true){
      //   this.msgTagTypes = this.noteTypes.find(function (item) {          
      //     return item.id == event.target.value;     
      //    });
      //    console.log("saaa", this.msgTagTypes)
      //    this.msgTypeSet = this.msgTagTypes['documentType']
      // }

      // this._structureService.getNoteTypes('noteType','ProgressNote').then((data) => {
      // 	console.log('data', data);
      // 	this.msgTagTypes = data['messageTagCategory'];
      // 	this.msgTagTypeCount  = this.msgTagTypes.length;
      // 	console.log(this.msgTagTypes);
      // 	this.msgTypeSet = this.msgTagTypes['messageTagType']
      // });				
      this.enableDocType = true;   
     }
   onSelectType(event){
		this.selectedTypeValue = event.target.value;     
    this.selectedTypeName = event.target.selectedOptions[0].text;
    if(event.target.value != "" && this.selectedCatValue != ""){
			this.enableSubData = true
		}else{
			this.enableSubData = false
		}
   }
   setSelectedData(){
		this.tagDefenitionAdd.patchValue({
			tagCategory:this.selectedCatName,
			tagTypeId: this.selectedTypeName
		});
    this.enableDocType = false;   
    this.enableSubData = false;
		$('#bulk-edit').modal('hide');
   }
  updateMessageTag(form)
  {
     var tagtype=0;
     tagtype= $('#tagtype').val();
    
    if(!tagtype){
      tagtype=0;
    }
    console.log("=======tagtype=======");
    console.log(tagtype);
    console.log(form.value);
    if(!form.valid){    
      return false;
  }    
  let tagExists;  
    if(form.value.tagName.trim()!='') {
    this.tenantId = typeof (this.tenantId) === 'undefined' ? this._structureService.getCookie('tenantId') : this.tenantId;   
    let filcentre='';
    if(form.value.filingcenter && form.value.filingcenter.folderName && form.value.filingcenter.folderName!="undefined" && form.value.filingcenter.folderName!="Select Filing Center"){
      filcentre=form.value.filingcenter.folderName;
    }else{
      filcentre='';
    }

    let selectedNursingTags = [];
    this.nursingAgencyUserTagSelected = $('#nursing-agency-user-tags').val();
    if (this.nursingAgencyUserTagSelected) {
      this.nursingAgencyUserTagSelected.forEach((element) => {
        let member = { id: '' };
        let id = element.substr(element.indexOf(':') + 1);
        id = id.replace(/'/g, '');
        member.id = id.replace(/\s/g, '');
        selectedNursingTags.push(Number(member.id));
      });
    }

    let fileNameFormat = "";
    let fileSaveFormatIntegration="";
    if(form.value.fileSaveFormat && form.value.fileSaveFormat!="undefined" && form.value.fileSaveFormat!=''){
      fileNameFormat = form.value.fileSaveFormat;
    }
    if(form.value.fileSaveFormatIntegration && form.value.fileSaveFormatIntegration!="undefined" && form.value.fileSaveFormatIntegration!=''){
      fileSaveFormatIntegration = form.value.fileSaveFormatIntegration;
    }
    tagExists = this.tagList.filter((row)=>{
      if(row.tagName.toLowerCase()==form.value.tagName.trim().toLowerCase() && row.id!=this.updateId){
        return true;
      }
    })
    if(tagExists.length>0)
      {
        var notify = $.notify('Message Tag already exists');
        setTimeout(function() {
            notify.update({'type': 'warning', 'message': '<strong>Message Tag already exists</strong>'});
        }, 1000);
        return false;
      }

      var triggeron=$("#triggerOn").val();
      if(this.userData.config.enable_progress_note_integration){       
        triggeron = triggeron ? triggeron :'add-tag';
      }else{
        triggeron ='';
      }
      
      //hide Summarize as Outcome Measure. 
      this.outcomeMeasures = false;
      if((this.isEnableMessageCategory && !this.isEnableMessageType) && ((!this.isEnableApiBasedIntegrationCategory || !this.isEnableApiBasedIntegrationType) || (!this.isEnableApiBasedIntegrationCategory && !this.isEnableApiBasedIntegrationType))){
        var externalIntegrationSettingsList = {
          messageTagCategoryId:form.value.messagecatetypeid,
          messageTagCategoryName: "",
          messageTagTypeId: form.value.messagecatecode,
          messageTagTypeName:""
        }
      } else {
         externalIntegrationSettingsList = {
          messageTagCategoryId:(this.isEnableApiBasedIntegrationCategory == true && this.isEnableApiBasedIntegrationType == true) ? this.selectedCatValue:form.value.tagCategoryId,
          messageTagCategoryName: (this.isEnableApiBasedIntegrationCategory == true && this.isEnableApiBasedIntegrationType == true)? form.value.tagCategory:form.value.tagCategory,
          messageTagTypeId: (this.isEnableApiBasedIntegrationCategory == true && this.isEnableApiBasedIntegrationType == true) ? this.selectedTypeValue:form.value.tagType,
          messageTagTypeName: (this.isEnableApiBasedIntegrationCategory == true && this.isEnableApiBasedIntegrationType == true) ? form.value.tagTypeId:form.value.tagTypeId
        }
      }
      let tagMeta = { outgoingFilingCenter: filcentre, 
        fileSaveFormat: fileNameFormat, 
        summarizeOutcomeMeasure: this.outcomeMeasures, 
        approvalRequired: this.approvalRequired, 
        patientFacing: this.patientFacing, 
        enableNotification: this.enableNotification,
        enableIntegration: this.enableIntegration, 
        integrationFC: (this.folderNameJson)?this.folderNameJson.folderName : "", 
        triggeron: triggeron,
        fileSaveFormatIntegration: fileSaveFormatIntegration, 
        nursingAgencyUserTagSelected: selectedNursingTags ? selectedNursingTags : [],
        messageCategoryCode:form.value.messageCategoryCode,
        messageTypeID:form.value.messageTypeID,
        externalIntegrationSettings: externalIntegrationSettingsList
      }

      console.log("------------tagMeta------------------");
      console.log(tagMeta);

    var updateTag = { "id":this.updateId,"name": form.value.tagName, "type": Number(this.type), "tenantId": (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId'):Number(this.tenantId), "tagMeta": tagMeta,"typeId":Number(tagtype)};    
   
    this.isDisabled=true;
    let res:any
    this._structureService.saveMessageTag(updateTag).then((data) => {
   res=data;
   
  if(res.error!=="Invalid Input")
  {
     
      if(res.status==false){
       var notify = $.notify('Message Tag already exists');
        setTimeout(function() {
            notify.update({'type': 'warning', 'message': '<strong>Message Tag already exists</strong>'});
        }, 1000);
          $('.update-message-tag').removeAttr("disabled");
      }

else{
  this.isDisabled=false;
      var notify = $.notify('Success! Message Tag updated');
      setTimeout(()=> {
          notify.update({'type': 'success', 'message': '<strong>Success! Message Tag updated</strong>'});
          this.router.navigate(['/message/tag-definitions']);
      }, 1000);

      var activityData = {
        activityName: "update message tag",
        activityType: "manage message tag",
        activityDescription: this.userData.displayName+" updated message tag - "+form.value.tagName
      };    
      this._structureService.trackActivity(activityData);
}
    

} else {
  let activityData = {
      activityName: "Error Update Message Tag",
      activityType: "messagetag",
      activityDescription: `Error occured while Updating Message Tag due to invalid input. `
    }; 
    this._structureService.notifyMessage({
      messge: this._ToolTipService.getTranslateData('MESSAGES.ERROR_MSG_INVALID_INPUT'),
      delay: 1000,
      type: 'warning'
    });
    this._structureService.trackActivity(activityData);
    this.isDisabled=false;
  }

    }).catch((ex) => {
      //this.loginFailed = true;
    });

    
    }
  }
   togglePreference(preference, value) {
    if(this.userData.config.progress_note_integration_mode !== 'webhook'){
			if(this.multiSiteEnable){
			  this.enableIntrationDiv=true;
			}
		}
    if (preference === 'outcomeMeasures') {
      console.log(value);
         this.outcomeMeasures=value;
      this.tagDefenitionAdd.patchValue({
        outcomeMeasures: value
      });
    }
    if (preference === 'approvalRequired') {
      console.log(value);
	if (value) {
		$('#triggerOn').val('approve-tag');
	} else {
		$('#triggerOn').val('add-tag');
	}
      this.approvalRequired=value
      this.tagDefenitionAdd.patchValue({
        approvalRequired: value
      });
    }
    if (preference === 'patientFacing') {
      console.log(value);
      this.patientFacing=value
      this.tagDefenitionAdd.patchValue({
        patientFacing: value
      });
    }
    if (preference === 'enableIntegration') {
      console.log(value);
      this.enableIntegration=value
      if(!this.enableIntegration){
        this.tagDefenitionAdd.patchValue({
          tagCategory:"",
          tagTypeId: ""
            });
        this.selectedTypeValue = "";
        this.selectedCatValue = "";
      }
      this.tagDefenitionAdd.patchValue({
        enableIntegration: value
      });
    }
   
    if (preference === 'enableNotification') {
      this.enableNotification = value;
    }
    
 }
  getFolderLists()
  {
    this._structureService.getTenantFilingCenterFolders('OUTGOING').then(     
        (data) => {
          console.log(data);
         if (data['getTenantFilingCenterFolders'] && data['getTenantFilingCenterFolders'].length) {
          this.folderLists =[];
            let folder = data['getTenantFilingCenterFolders'];            
            for(let i=0;i<folder.length;i++){              
              let f = {folderName:folder[i].folderName,type:folder[i].type,selected:false};
              this.folderLists.push(f);
            }
            this.folderName ='';
            $(".selectd").attr("disabled", false);
            $('.selectd').select2(
              
              {
              placeholder: "Select Filing Center"
           });  


           this.filingCenters = [];
           console.log(this.folderName);
           console.log(this.folderLists);

          for(let i=0;i<this.folderLists.length;i++){
            var fname = this.folderLists[i].folderName;
            var ftype = this.folderLists[i].type;
            var id = '{"folderName":"'+fname+'","type":"'+ftype+'"}';
             var item = {id:fname,text:fname}
             this.filingCenters.push(item);
          }


            if(this.tag_meta && this.tag_meta!='null' && this.tag_meta!='')
              {                
                let meta =JSON.parse(this.tag_meta);
                console.log(this.folderLists);
               // this.folderName = meta.outgoingFilingCenter;
                this.folderLists.filter((data,index)=>{
                  if(data.folderName==meta.outgoingFilingCenter)
                    {
                      this.folderName = data; 
                      this.folderLists[index].selected = true;                                          
                      
                    }
                });
                if(meta.summarizeOutcomeMeasure){
                  this.outcomeMeasures=meta.summarizeOutcomeMeasure;
                }
                if(meta.approvalRequired){
                  this.approvalRequired=meta.approvalRequired;
                }
                console.log(this.folderName)

                $('#sfilingCenterss').select2({
                  allowClear: true,
                  placeholder: 'Select Filing Center',
                  data:this.filingCenters
                });

                $('#sfilingCenterss').val(this.folderName.folderName).trigger('change');


                $('#sfilingCenterjson').select2({
                  allowClear: true,
                  placeholder: 'Select Filing Center',
                  data:this.filingCenters
                });

                $('#sfilingCenterjson').val(meta.integrationFC).trigger('change');

                if(meta.fileSaveFormat){
                  this.fileSaveFormat=meta.fileSaveFormat;
                }else{
                  this.fileSaveFormat="";
                }
                if(meta.fileSaveFormatIntegration){
                  this.fileSaveFormatIntegration=meta.fileSaveFormatIntegration;
                }else{
                  this.fileSaveFormatIntegration="";
                }
                if(meta.messageTypeID){
                  this.messageTypeID=meta.messageTypeID;
                }else{
                  this.messageTypeID="";
                }
                if(meta.messageCategoryCode){
                  this.messageCategoryCode=meta.messageCategoryCode;
                }else{
                  this.messageCategoryCode="";
                }
              }
              else{
                this.fileSaveFormat="";
                this.fileSaveFormatIntegration="";
              }

              this.folderLists.unshift({folderName:"Select Filing Center",type:"OUTGOING"});


         }
         else{

          $(".selectd").attr("disabled", false);
          $('.selectd').select2(
            
            {
            placeholder: "No Filing Center Available"
         }); 
           console.log("No folders");
         }
    });
  }

  getNursingAgencyTags() {
    const userData: any = this._structureService.getUserdata();
    const tagGetData = '?userId=' + userData.userId + '&tenantId=' + userData.tenantId;
    const tagTypes = ['2']; // Message Tag =1, User Tag =2 , Document Tag =3
    this._structureService.getNursingAgencyTagsByGroup(tagGetData, tagTypes).then((data: any) => {
      this.nursingAgencyTags = data;
    });
  }

}
