<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
          <strong>Add Patient Discussion Groups </strong>
          <!--<a [routerLink]="['/message/messagegroup']" class="pull-right btn btn-sm btn-primary">Add Message Group<i class="ml-1"></i></a>-->
      </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/message']">Message Center</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/patient-discussion-groups']">Patient Discussion Groups</a></li>
            <li class="breadcrumb-item">Add Patient Discussion Groups</li>
        </ol>

    </div>
<!-- </section>
<section class="row chatroom-section"> -->

    <div class="col-lg-12">
        <section class="card">
            <div class="card-block">

                <form class="form-horizontal" [formGroup]="messageGroup" (ngSubmit)="onBlurMethodForId(false,f)" #f="ngForm" autocomplete="off">
                    <div class="form-group row" [hidden]="!hideSiteSelection" *ngIf="multiSiteEnable  || userData.crossTenantsDetails.length>1">
                        <div class="col-md-3"> 
                            <label class="control-label">Site * <i chToolTip="Site"></i></label>
                        </div>
                        <div class="col-md-6">
                            <app-select-sites  [events]="eventsSubject.asObservable()"  [singleSelection]=singleSelection
                             (siteIds)="getSiteIds($event)" (hideDropdown)="hideDropdown($event)" [hideApplyFilter]=true [crossSite]=crossTenant>
                             </app-select-sites>
                            <span id="error-form" class="error"></span>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-md-3">
                            <label>First Name *</label>
                        </div>
                        <div class="col-md-6">
                            <input type="text" class="form-control" formControlName="messageGroupName" placeholder="First Name" id="messageGroupfirstName" autocomplete="off">
                            <div *ngIf="messageGroup.controls['messageGroupName'].errors&&(messageGroup.controls.messageGroupName?.dirty ||messageGroup.controls.messageGroupName?.touched || f.submitted)"
                                class="alert alert-danger">
                                Please enter first name
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-md-3">
                            <label>Last Name *</label>
                        </div>
                        <div class="col-md-6">
                            <input type="text" class="form-control" formControlName="messageGroupLastName" placeholder="Last Name" id="messageGrouplastName" autocomplete="off">
                            <div *ngIf="messageGroup.controls['messageGroupLastName'].errors&&(messageGroup.controls.messageGroupLastName?.dirty ||messageGroup.controls.messageGroupLastName?.touched || f.submitted)"
                                class="alert alert-danger">
                                Please enter last name
                            </div>
                        </div>
                    </div>
                    <div class="form-group row" *ngIf="userData.config.associate_pdg_with_patient_id ==1 "  >
                        <div class="col-md-3">                            
                            <label class="control-label"  >{{ userData.config.field_label_of_patient_association_in_pdg ? userData.config.field_label_of_patient_association_in_pdg: 'ID#'}}
                                <span *ngIf="userData.config.make_mrn_field_mandatory_in_patient_invite == '1'">*</span>
                            </label>
                       
                        </div>
                        <div class="col-md-6">
                            <input type="text" class="form-control" id="patientUniqueId" formControlName="patientUniqueId" placeholder="{{ userData.config.field_label_of_patient_association_in_pdg ? userData.config.field_label_of_patient_association_in_pdg: 'ID#'}}" autocomplete="off">                            
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-md-3">
                            <label>Date of Birth (MM/DD/YYYY) *</label>
                        </div>
                        <div class="col-md-6">
                            <input type="text" class="form-control" formControlName="messageGroupDob" id="date-mask-input" pattern="(0[1-9]|1[012])[- /.](0[1-9]|[12][0-9]|3[01])[- /.](19|20)\d\d" autocomplete="off">
                            <div *ngIf="messageGroup.controls['messageGroupDob'].errors&&(messageGroup.controls.messageGroupDob?.dirty ||messageGroup.controls.messageGroupDob?.touched || f.submitted)"
                                class="alert alert-danger">
                                Please enter valid date of birth
                            </div>
                        </div>
                    </div>
                    <div [hidden]="!allowSave && !privilegeManageAllMessageGroup" class="form-group row">
                        <!--&& !addGroup -->
                        <label class="col-md-3 control-label">Make this Group Public <i class="make-public icmn-info" ></i></label>
                        <div class="col-md-3">

                            <div class="btn-group">
                                <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': selectedisPublic}"
                                    (click)="togglePublic(true)">
                                Yes
                          </button>
                                <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !selectedisPublic}"
                                    (click)="togglePublic(false)">
                              No
                          </button>
                            </div>
                        </div>
                    </div>
                    <div class="form-group row">
                        <div class="col-md-3">
                            <label class="control-label">Allow multi thread PDG</label>
                            <i class="multi-chat-thread icmn-info"></i>
                        </div>
                        <div class="col-md-6">
                            <div class="btn-group">
                                <button type="button" aria-pressed="true" class="btn btn-outline-success btn-sm" [ngClass]="{'active': selectedAllowMultiThread}" (click)="toggleMultiThreadOption(true)"> Yes </button>
                                <button type="button" aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !selectedAllowMultiThread}" (click)="toggleMultiThreadOption(false)"> No </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- <div class="form-group row" *ngIf="isNursingAgencyEnabled == 1">
                        <label *ngIf="userData.nursing_agencies == ''" class="col-md-3 control-label">User Tags</label>
                        <div class="col-md-6" *ngIf="userData.nursing_agencies == ''">
                            <select name="nursingAgencyUserTag" formControlName="nursingAgencyUserTag"
                                id="nursing-agency-user-tags" class="form-control select2111">
                                <option value="">Select User Tag </option>
                                <option *ngFor="let tag of nursingAgencyTags" value="{{tag.id}}">
                                    {{tag.tag_name }}
                                </option>
                            </select>
                        </div>
                        <label *ngIf="userData.nursing_agencies != ''" class="col-md-3 control-label">User Tags *</label>
                        <div class="col-md-6" *ngIf="userData.nursing_agencies != ''">
                            <select name="nursingAgencyUserTag" formControlName="nursingAgencyUserTag"
                                id="nursing-agency-user-tags" class="form-control select2111" required>
                                <option value="">Select User Tag </option>
                                <option *ngFor="let tag of nursingAgencyTags" value="{{tag.id}}">
                                    {{tag.tag_name }}
                                </option>
                            </select>
                            <div *ngIf="messageGroup.controls['nursingAgencyUserTag'].errors&&(messageGroup.controls.nursingAgencyUserTag?.dirty || messageGroup.controls.nursingAgencyUserTag?.touched)"
                                class="alert alert-danger">
                                Please select a user tag
                            </div>
                        </div>
                    </div> -->

                    <div class="row">
                        <div class="col-lg-4">
                            <br>

                            <section class="card existing member-card">
                                <div class="card-header">
                                    <span class="cat__core__title">
                                  <strong>Existing Members</strong>
                              </span>
                                </div>
                                <div class="card-block">
                                    <div class="cat__apps__messaging__header">
                                        <input type="text" class="form-control cat__apps__messaging__header__input" id="search_member" placeholder="Search Member(s)..." #existingMembers autocomplete="off"/>
                                        <i class="icmn-search"></i>
                                        <!-- <button></button> -->
                                    </div>
                                    <div class="member-list">
                                    <table class="table table-hover nowrap" id="example1">

                                        <tbody>
                                            <tr *ngFor="let member of selectedGroupMembers | MessageGroupSearchFilter:existingMembers.value;">
                                                <td>{{member.displayName}}<br>&nbsp;<span *ngIf="member.naTagNames">({{member.naTagNames}})&nbsp;</span><br><span *ngIf="userData.tenantId!=member.tenantId">[{{member.tenantName}}]</span></td>
                                                <td> <a href="javascript: void(0);" (click)="removeMember(member.id);"><small><i class="icmn-cross"></i></small></a></td>
                                                <!--*ngIf="editGroup || addGroup"-->
                                            </tr>


                                        </tbody>
                                    </table>
                                    </div>
                                </div>
                                <div *ngIf="noMemberError" class="card-footer">
                                    <div class="alert alert-danger">Group members cannot be empty </div>
                                </div>
                            </section>
                            <section class="card existing role-list">
                                <div class="card-header role-header">
                                    <span class="cat__core__title">
                                        <strong>Existing Roles</strong>
                                    </span>
                                </div>
                                <div class="card-block">
                                    <div class="member-list role-member-list">
                                        <table class="table table-hover nowrap" id="example1">
                                            <tbody>
                                                <tr *ngFor="let role of checkedRoleIds">
                                                    <td> {{role.name}}&nbsp;<br><span *ngIf="userData.tenantId!=role.tenantId">[{{role.tenantName}}]</span></td>
                                                    <td> <a href="javascript: void(0);" (click)="removeExistingRole(role.id);"><small><i class="icmn-cross"></i></small></a></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </section>
                        </div>
                        <div class="col-lg-8 pull-right" id="staffListblock">
                            <br>
                            <section class="card">

                                <div class="card-header">
                                    <span class="cat__core__title">
                                        <strong>New Roles/Members</strong>
                                    </span>
                                    <input style="margin-top:18px;" [disabled]="!membersLoaded" id="add_mbr" type="button" class="btn btn-sm btn-primary pull-right" (click)="addMember();" name="" value="Add" />
                                </div>
                                <div class="card-block">
                                    <div class="form-group row">
                                        <div class="chatwith-modal-tab chatwith-modal-tab-mar" style="margin-left: 55px;">
                                            <div class="chatwith-model-head col-md"  (click)="showData('groups')" [class.cat__apps__messaging__tab--selected]="optionShow=='groups'">PDGs</div>
                                            <div class="chatwith-model-head col-md"  (click)="showData('msggroups')" [class.cat__apps__messaging__tab--selected]="optionShow=='msggroups'">Message Groups</div>
                                            <div class="chatwith-model-head col-md"  (click)="showData('staffroles')" [class.cat__apps__messaging__tab--selected]="optionShow=='staffroles'"> Staff Roles </div>
                                            <div class="chatwith-model-head col-md"  (click)="showData('staffs')" [class.cat__apps__messaging__tab--selected]="optionShow=='staffs'" >Staff</div>
                                            <div class="chatwith-model-head col-md" (click)="showData('partnerroles')" [class.cat__apps__messaging__tab--selected]="optionShow=='partnerroles'"> Partner Roles </div>
                                            <div class="chatwith-model-head col-md" (click)="showData('partner')" [class.cat__apps__messaging__tab--selected]="optionShow=='partner'" >Partners</div>
                                            
                                        </div>
                                       
                                        <div class="row chat-with-wrapper-filter"   style="width: 100%;margin-top: 20px;" >
                                            <!-- <div class="col-md-4 chat-with-wrapper-filter-tenant"  *ngIf="((optionShow !='groups') && (optionShow !='msggroups') && chatWithTenantFilter.filterEnabled && chatWithTenantFilter.tenants.length)">
                                                <div class="row">
                                                    <div class="col-md-3 chat-with-wrapper-filter-tenant-label">
                                                        <span>{{ 'LABELS.FILTER_BY_SITES' | translate }}</span>
                                                    </div>
                                                    <div class="col-md-9 chat-with-wrapper-filter-tenant-select">
                                                        
                                                        <select class="form-control" data-placeholder="None Selected" id="messageTenantFilter"  #branchname   >
                                                            <option *ngFor="let tenant of chatWithTenantFilter.tenants" value="{{tenant.id}}" [selected]="tenant.id == chatWithTenantFilter.selectedTenant" > {{tenant.tenantName}} </option>
                                                        </select>
                                                    </div>
                                                </div> -->
                                                <!-- *ngIf="((optionShow !='groups') && (optionShow !='msggroups') && chatWithTenantFilter.filterEnabled && chatWithTenantFilter.tenants.length)"> -->
                                               <!-- Removed site filtration from roles listing page  -->
                                                <div class="col-sm-5" style="padding-left: 19px">
                                                <div class="row" *ngIf="((optionShow !='groups') && (optionShow !='msggroups') && chatWithTenantFilter.filterEnabled && chatWithTenantFilter.tenants.length  && !multiSiteEnable )" [hidden]="!hideSiteSelection">
                                                <div class="site-label">
                                                    <span>Site:&nbsp;</span>
                                                </div>
                                                <div style="width: 70%">
                                                    <app-select-sites [events]="eventsSubject.asObservable()" [hideApplyFilter]=true [crossSite]=true (hideDropdown)="hideDropdown($event)" (siteIds)="getBranchIds($event)">
                                                    </app-select-sites>
                                            </div>
                                              </div>
                                                </div>
                                            <!-- </div> -->
                                            <div class="chat-with-wrapper-search col-sm-7" [ngClass]="{'filter-enabled': ((optionShow !='groups') && (optionShow !='msggroups') && chatWithTenantFilter.filterEnabled && chatWithTenantFilter.tenants.length)}">
                                            <div class="row"> 
                                            <div class="col-sm-9">  
                                            <input type="text" [(ngModel)]="searchText" [hidden]="optionShow !='staffroles' && optionShow !='partnerroles'" [ngModelOptions]="{standalone: true}" class="search-width-grp form-control cat__apps__messaging__header__input" autocomplete="off"
                                            placeholder="Search Role(s)..." #newMembers (keyup)="searchOnKeyPress($event)" id="userSearchTxtRoles" />
                                            <div style="width: 92%;">        
                                            <input (keydown)="searchOnEnter($event,optionShow)" [hidden]="optionShow ==='staffroles' || optionShow ==='partnerroles'" class="form-control cat__apps__messaging__header__input groups-srch-width" id="chat-with-modal-search-box" placeholder="Search Here" #userSearch>
                                            </div>
                                            </div>
                                            <div class="col-sm-3">
                                                <button [hidden]="optionShow ==='staffroles' || optionShow ==='partnerroles'" [disabled]="this.srch.nativeElement.value.trim() == ''" type="button" class="btn btn-sm btn-primary srchBtn" title="Search" (click)="search(optionShow)">Search</button>
                                                <button [hidden]="optionShow ==='staffroles' || optionShow ==='partnerroles'" type="button" class="btn btn-sm btn-default resetBtn" title="Reset" (click)="reset(optionShow)">Reset</button>
                                            </div>
                                            </div>
                                            </div>
                                            </div>
                                        <div *ngIf="optionShow=='staffroles'"  class="col-md-12">                                              
                                            <div class="chat-with-empty-data"  *ngIf="!(newMemberListByRoleWise).length && !chatWithLoader.otherTenantstaff" >No Staff Roles Available. <span  *ngIf="userSearch.value"><span style="color: #0088ff;cursor: pointer;" (click)="reset(optionShow)"> Click Here </span> to Reset the Search.</span></div>
                                            <div class="chat-with-empty-data"  style="display:none" id="notFoundRoles" >No Staff Roles Available..</div>     
                                            <div *ngIf="chatWithLoader.otherTenantstaff" class="loading-container">
                                                <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                                <div class="loading-text">Loading Staff Roles...</div>
                                            </div>                                          
                                        </div>
                                        <div *ngIf="optionShow=='partnerroles'"  class="col-md-12">                                              
                                            <div class="chat-with-empty-data"  *ngIf="!(newMemberListByRoleWise).length && !chatWithLoader.otherTenantstaff" >No Partner Roles Available. <span  *ngIf="userSearch.value"><span style="color: #0088ff;cursor: pointer;" (click)="reset(optionShow)"> Click Here </span> to Reset the Search.</span></div>
                                            <div class="chat-with-empty-data"  style="display:none" id="notFoundRoles" >No Partner Roles Available..</div>     
                                            <div *ngIf="chatWithLoader.otherTenantstaff" class="loading-container">
                                                <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                                <div class="loading-text">Loading Partner Roles...</div>
                                            </div>                                          
                                        </div>
                                        <div *ngIf="optionShow=='staffs'"  class="col-md-12">
                                              
                                        <div class="chat-with-empty-data" *ngIf="!(userListChatwith).length && !chatWithLoader.staffs" >No Clinicians Available. <span  *ngIf="userSearch.value"><span style="color: #0088ff;cursor: pointer;" (click)="reset(optionShow)"> Click Here </span> to Reset the Search.</span></div>
    
                                        <div *ngIf="chatWithLoader.staffs" class="loading-container">
                                            <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                            <div class="loading-text">Loading Clinicians...</div>
                                        </div>                                     
                                   
                                    </div>
                                    <div *ngIf="optionShow=='partner'"  class="col-md-12">
                                              
                                        <div class="chat-with-empty-data" *ngIf="!(userListChatwith).length && !chatWithLoader.partner" >No Partner Available. <span  *ngIf="userSearch.value"><span style="color: #0088ff;cursor: pointer;" (click)="reset(optionShow)"> Click Here </span> to Reset the Search.</span></div>
    
                                        <div *ngIf="chatWithLoader.partner" class="loading-container">
                                            <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                            <div class="loading-text">Loading Partners...</div>
                                        </div>                                     
                                   
                                    </div>
                                        <ul class="treeview treeview-section-ui"  style="width:100%;" *ngIf="optionShow ==='staffroles' && (newMemberListByRoleWise.length && !chatWithLoader.otherTenantstaff) ">
                                            
                                            <li *ngFor="let cliniciansRole of newMemberListByRoleWise ; let i = index" 
                                                 class="role-{{cliniciansRole.roleData.id}} roleslisting">
                                                <i class="fa fa-plus expand-icon-{{cliniciansRole.roleData.id}}" (click)="callAccordion(cliniciansRole.roleData.id,i)"></i>
                                                <input type="checkbox" name="staffRoleWise" id="role-{{cliniciansRole.roleData.id}}" value="{{cliniciansRole.roleData|json}}" (change)="checkboxChanged($event,i)">
                                                <label for="staffRoleWise" class="custom-unchecked">{{cliniciansRole.roleData.name }}</label>
                                                <span *ngIf="(usersInRole==false && clickedRole==cliniciansRole.roleData.id)" style="right: 10%;margin-left: 50%; color:#acb7bf;">No Users Found..</span>
                                            
                                                <ul class="sub-item-panel sub-item-panel-{{cliniciansRole.roleData.id}}" >
                                                    <li class='fa fa-caret-right' style="display: list-item!important;"  *ngFor="let cliniciansRoleUser of cliniciansRole.userList "  [hidden]="cliniciansRoleUser.searchHideStatus">
                                                       <!--  <input type="checkbox" (change)="checkboxChanged($event,i)" name="cliniciansRoleUser[]" [attr.data-roleSet]="cliniciansRole.roleData.id" id="role-{{cliniciansRole.roleData.id}}-{{cliniciansRoleUser.id}}"
                                                            value="{{cliniciansRoleUser.id}}" class="cliniciansRoleUser" title="{{cliniciansRoleUser.name}}"> -->
                                                        <span class="ag-grid-cell" style="line-height: 30px;padding-left:5px;">{{cliniciansRoleUser.name}}&nbsp;<span *ngIf="cliniciansRoleUser.naTagNames">({{cliniciansRoleUser.naTagNames}})&nbsp;</span></span>
                                                    </li>
                                                </ul>
                                            </li>
                                           <!--  <li *ngIf="!(newMemberListByRoleWise .length && chatWithLoader.otherTenantstaff) ">No Users Available</li> -->
                                        </ul>

                                        <ul class="treeview treeview-section-ui"  style="width:100%;" *ngIf="optionShow ==='partnerroles' && (newMemberListByRoleWise.length && !chatWithLoader.otherTenantstaff) ">
                                            
                                            <li *ngFor="let cliniciansRole of newMemberListByRoleWise ; let i = index" 
                                                 class="role-{{cliniciansRole.roleData.id}} roleslisting">
                                                <i class="fa fa-plus expand-icon-{{cliniciansRole.roleData.id}}" (click)="callAccordion(cliniciansRole.roleData.id,i)"></i>
                                                <input type="checkbox" name="staffRoleWise" id="role-{{cliniciansRole.roleData.id}}" value="{{cliniciansRole.roleData|json}}" (change)="checkboxChanged($event,i)">
                                                <label for="staffRoleWise" class="custom-unchecked">{{cliniciansRole.roleData.name }}</label>
                                                <span *ngIf="(usersInRole==false && clickedRole==cliniciansRole.roleData.id)" style="right: 10%;margin-left: 50%; color:#acb7bf;">No Users Found..</span>

                                                <ul class="sub-item-panel sub-item-panel-{{cliniciansRole.roleData.id}}" >
                                                    <li class='fa fa-caret-right' style="display: list-item!important;"  *ngFor="let cliniciansRoleUser of cliniciansRole.userList "  [hidden]="cliniciansRoleUser.searchHideStatus">
                                                       <!--  <input type="checkbox" (change)="checkboxChanged($event,i)" name="cliniciansRoleUser[]" [attr.data-roleSet]="cliniciansRole.roleData.id" id="role-{{cliniciansRole.roleData.id}}-{{cliniciansRoleUser.id}}"
                                                            value="{{cliniciansRoleUser.id}}" class="cliniciansRoleUser" title="{{cliniciansRoleUser.name}}"> -->
                                                        <span class="ag-grid-cell" style="line-height: 30px;padding-left:5px;">{{cliniciansRoleUser.name}}&nbsp;<span *ngIf="cliniciansRoleUser.naTagNames">({{cliniciansRoleUser.naTagNames}})&nbsp;</span></span>
                                                    </li>
                                                </ul>
                                            </li>
                                           <!--  <li *ngIf="!(newMemberListByRoleWise .length && chatWithLoader.otherTenantstaff) ">No Users Available</li> -->
                                        </ul>
                                        
                                        <ul class="treeview treeview-section-ui" style="width:100%;" *ngIf="optionShow ==='staffs'">
                                               
                                                    <li *ngFor="let cliniciansRoleUser of userListChatwith " >
                                                        <input type="checkbox" (change)="checkboxChanged($event)" name="cliniciansRoleUser[]" [attr.data-roleSet]="cliniciansRoleUser.roleId" id="role-{{cliniciansRoleUser.roleId}}-{{cliniciansRoleUser.id}}"
                                                            value="{{cliniciansRoleUser.userid}}" class="cliniciansRoleUser" title="{{cliniciansRoleUser.displayname}}">
                                                        <label for="{{cliniciansRoleUser.displayname}}" class="custom-unchecked">{{cliniciansRoleUser.displayname}}&nbsp;<span *ngIf="cliniciansRoleUser.naTagNames">({{cliniciansRoleUser.naTagNames}})</span></label>
                                                    </li>
                                                
                                            <!-- <li *ngIf="!((userListChatwith ).length && chatWithLoader.staffs)">No Users Available</li> -->
                                        </ul>
                                        <ul class="treeview treeview-section-ui" style="width:100%;" *ngIf="optionShow ==='partner'">
                                               
                                            <li *ngFor="let cliniciansRoleUser of userListChatwith " >
                                                <input type="checkbox" (change)="checkboxChanged($event)" name="cliniciansRoleUser[]" [attr.data-roleSet]="cliniciansRoleUser.roleId" id="role-{{cliniciansRoleUser.roleId}}-{{cliniciansRoleUser.id}}"
                                                    value="{{cliniciansRoleUser.userid}}" class="cliniciansRoleUser" title="{{cliniciansRoleUser.displayname}}">
                                                <label for="{{cliniciansRoleUser.displayname}}" class="custom-unchecked">{{cliniciansRoleUser.displayname}}&nbsp;<span *ngIf="cliniciansRoleUser.naTagNames">({{cliniciansRoleUser.naTagNames}})</span></label>
                                            </li>
                                        
                                        <!-- <li *ngIf="!((userListChatwith ).length && chatWithLoader.staffs)">No Users Available</li> -->
                                      </ul>
                                        <ul class="treeview treeview-section-ui" style="width:100%;" *ngIf="optionShow ==='groups'">
                                            <li *ngFor="let cliniciansGroup of memberDataGroupWise"
                                                class="role-{{cliniciansGroup.id}}" [hidden]="cliniciansGroup.searchHideStatus && optionShow ==='groups'">
                                                <i class="fa fa-plus expand-icon-{{cliniciansGroup.id}}" (click)="callAccordion(cliniciansGroup.id)"
                                                    ></i>
                                                <input type="checkbox" name="messageGroup" id="role-{{cliniciansGroup.id}}" value="{{cliniciansGroup.id}}" (change)="checkboxChanged($event)">
                                                <label for="middle" class="custom-unchecked">{{cliniciansGroup.name }}</label>
                                                <span *ngIf="(isGrouploadingTime==true && clickedGroup==cliniciansGroup.id)" style="right: 10%;margin-left: 50%; color:#acb7bf;">Loading...</span>
                                                <ul class="sub-item-panel sub-item-panel-{{cliniciansGroup.id}}" >
                                                    <li *ngFor="let cliniciansRoleUser of cliniciansGroup.userList">
                                                        <span style="line-height: 30px;padding-left:5px;">{{cliniciansRoleUser.name}}&nbsp;
                                                            <span *ngIf="cliniciansRoleUser.naTagNames && cliniciansRoleUser.naTagNames != ''">({{cliniciansRoleUser.naTagNames}})&nbsp;</span>&nbsp;
                                                            <span *ngIf="userData.tenantId!=cliniciansRoleUser.tenantId">[{{cliniciansRoleUser.tenantName}}]</span>
                                                        </span>
                                                    </li>
                                                </ul>
                                            </li>                                            
                                        </ul>
                                        <ul class="treeview treeview-section-ui" style="width:100%;" *ngIf="optionShow ==='msggroups'">
                                            <li *ngFor="let cliniciansGroup of memberDataGroupWise"
                                                class="role-{{cliniciansGroup.id}}">
                                                <i class="fa fa-plus expand-icon-{{cliniciansGroup.id}}" (click)="callAccordion(cliniciansGroup.id)"
                                                    ></i>
                                                <input type="checkbox" name="messageGroup" id="role-{{cliniciansGroup.id}}" value="{{cliniciansGroup.id}}" (change)="checkboxChanged($event)">
                                                <label for="middle" class="custom-unchecked">{{cliniciansGroup.name }}</label>
                                                <span *ngIf="(isGrouploadingTime==true && clickedGroup==cliniciansGroup.id)" style="right: 10%;margin-left: 67%; color:#acb7bf;">Loading...</span>
                                                <ul class="sub-item-panel sub-item-panel-{{cliniciansGroup.id}}" >
                                                    <li *ngFor="let cliniciansRoleUser of cliniciansGroup.userList">
                                                        <span style="line-height: 30px;padding-left:5px;">{{cliniciansRoleUser.name}}&nbsp;
                                                            <span *ngIf="cliniciansRoleUser.naTagNames && cliniciansRoleUser.naTagNames != ''">({{cliniciansRoleUser.naTagNames}})&nbsp;</span>&nbsp;
                                                            <span *ngIf="userData.tenantId!=cliniciansRoleUser.tenantId">[{{cliniciansRoleUser.tenantName}}]</span>
                                                        </span>
                                                    </li>
                                                </ul>
                                            </li>                                            
                                        </ul>
                                        <div *ngIf="optionShow=='groups' || optionShow=='msggroups'"  class="col-md-12">
                                            <!--<div *ngIf="!(memberDataGroupWise).length" class="row">No Groups Available. <span  *ngIf="searchMembers.value"><span style="color: #0088ff;cursor: pointer;padding-left: 3px;" (click)="reset(optionShow)"> Click Here </span> to Reset the Search.</span></div>
                                            <button [disabled]="loadingGroups" type="button" [hidden]="hideLoadMore" class="btn btn-sm btn-default" (click)="loadMoreGroups()">
                                                    <span *ngIf="!loadingGroups">Load More</span>
                                                    <span *ngIf="loadingGroups">Loading Groups...</span>  
                                            </button>-->

                                            <div *ngIf="!(memberDataGroupWise).length  && !MessageGroupWithLoader.groups" class="row">No <span *ngIf="optionShow=='groups'" >&nbsp; PDGs &nbsp;</span> <span *ngIf="optionShow=='msggroups'" >&nbsp; Message Groups &nbsp;</span> Available. <span  *ngIf="searchMembers && searchMembers.value"><span style="color: #0088ff;cursor: pointer;padding-left: 3px;" (click)="reset(optionShow)"> Click Here </span> to Reset the Search.</span></div>

                                            <div *ngIf="MessageGroupWithLoader.groups" class="loading-container">
                                                <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                                                <div class="loading-text">Loading Groups...</div>
                                            </div> 
                                            
                                            <div *ngIf="optionShow=='groups' || optionShow=='msggroups'" style="text-align: center;width:100%;margin-top: 14px;float: left;">
                                            <button [disabled]="MessageGroupWithLoader.groups" *ngIf="!MessageGroupWithLoader.groups" type="button" [hidden]="hideLoadMore" class="btn btn-sm btn-default" (click)="loadMoreGroups(optionShow)">
                                                    <span *ngIf="!MessageGroupWithLoader.groups">Load More</span>
                                                  <!--  <span *ngIf="loadingGroups">Loading Groups...</span>  -->
                                            </button>
                                            </div>

                                        </div>                                   
                                        <div *ngIf="optionShow=='staffs'"  class="col-md-12">     
                                        <div style="text-align: center;width:100%;margin-top: 14px;float: left;">
                                            <button type="button" [hidden]="!( optionShow =='staffs' && !noMoreItemsAvailable.users && userListChatwith.length)" class="btn btn-sm btn-default" (click)="loadMoreUsers('staffs',loadMoreSearchValue, true)">{{loadMoremessage.users}}</button>
                                        </div>
                                    </div>
                                    <div *ngIf="optionShow=='partner'"  class="col-md-12">     
                                        <div style="text-align: center;width:100%;margin-top: 14px;float: left;">
                                            <button type="button" [hidden]="!( optionShow =='partner' && !noMoreItemsAvailable.users && userListChatwith.length)" class="btn btn-sm btn-default" (click)="loadMoreUsers('partner',loadMoreSearchValue, true)">{{loadMoremessage.users}}</button>
                                        </div>
                                    </div>
                                    </div>
                                </div>
                                <div *ngIf="addMemberError" class="card-footer">
                                    <div class="alert alert-danger">Please select member </div>
                                </div>
                            </section>
                            <!-- <input type="button" class="btn btn-sm btn-primary" (click)="addMember();" name="" value="Add"/> -->

                        </div>
                    </div>

                    <div class="form-actions">
                        <!--*ngIf="editGroup||addGroup"-->
                        <button type="submit" [disabled]="!messageGroup.valid || disableButton || selectSiteId == 0" class="btn btn-primary" id="submit_btn">Submit</button>
                        <!-- <button *ngIf="editGroup"  type="button"  [disabled]="disableButton" [hidden]="!allowSave && !privilegeManageAllMessageGroup"  (click)="updateMessageGroup(selected.id)" class="btn btn-primary">Update</button> -->
                        <a [routerLink]="['/message/patient-discussion-groups']" class="btn btn-default">Cancel</a>
                    </div>

                </form>

            </div>
        </section>

    </div>
</section>
<!-- END: tables/datatables -->
