import { Component, OnInit } from '@angular/core';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;
import { ToolTipService } from '../tool-tip.service';
import { StructureService } from '../structure.service';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { DatePipe } from '@angular/common';
import { chatLogdateshortFilterPipe } from './chatlog-date-filter.pipes';
import { SharedService } from './../shared/sharedServices';
import { Subject } from 'rxjs';
import { isBlank } from 'app/utils/utils';
import { DateRanges, LISTING_PAGE_DATERANGE_OPTIONS, MessageType } from 'app/constants/constants';
import { Store, StoreService } from '../shared/storeService';
import { StaticDataService } from '../static-data.service';
import { FormControl } from '@angular/forms';

let moment = require('moment/moment');
@Component({
  selector: 'app-log-message',
  templateUrl: './logs.html',
  styleUrls: ['./logs.css'],
  providers: [DatePipe,chatLogdateshortFilterPipe]
})

export class LogsComponent implements OnInit {
  initiator;
  logMessages = [];
  userChats = [];
  userChatList = [];
  logMessagesCount;
  selectedId;
  recordFound;
  dTable;
  searchResetFlag =0;
  datam;
  senderId;
  limit;
  userRole;
  initiatorFromLogs;
  dataLoadingMsg = true;
  crossTenantChangeSubscriber:any;
    selectedExport=false;
    userData;
    selectSiteId;
    hideSiteSelection: boolean;
    eventsSubject: Subject<void> = new Subject<void>();
  public daterange: any = {};
  dateRangeFilterOptions = LISTING_PAGE_DATERANGE_OPTIONS;
  applyDateRangeFilter = false;
  dateRangeStoreKey = Store.DATE_RANGE_FILTER_CHAT_LOGS;
  filterOnSite = false;
  filterSiteLabel = 'LABELS.FILTER_BY_SITES';
  siteLabel = 'LABELS.SITE';
  dateRange = new FormControl();
  dateRangeType = DateRanges.LAST_THIRTY_DAYS;
  showToastMsg = false;
  initialLoad = false;
  constructor(private route: ActivatedRoute,
    private router: Router,
    private _structureService: StructureService,
    private chatLogdateshortFilterPipe: chatLogdateshortFilterPipe,
    private _ToolTipService: ToolTipService,
    private datePipe: DatePipe,
    public _SharedService:SharedService,
    private staticDataService: StaticDataService,
    private storeService: StoreService) {
      this.crossTenantChangeSubscriber = this._SharedService.crossTenantChange.subscribe((onInboxData) => {
        if(this.router.url.substr(this.router.url.lastIndexOf('/') + 1) == 'logs') {
          this.ngOnInit();
        }
      });
      this.filterSiteLabel = this.staticDataService.getSiteLabelBasedOnMultiAdmission('FILTER_BY_SITES');
      this.siteLabel = this._ToolTipService.getTranslateData(this.staticDataService.getSiteLabelBasedOnMultiAdmission('SITE'));
  }

  ngOnInit() {
    this.initialLoad = true;
    this.userData = this._structureService.getUserdata(); 
    this.logMessagesCount = 0;
    this.limit=25;
   // this.userChatRoom();
     var page = 'chat-logs';
 $(".fetch-entries-info").tooltip({ title: this._ToolTipService.getToolTip(page, 'CHATLOG000002') });

     
  }
  ngOnDestroy() {
    if(this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
  }
  getSiteIds(data:any){
    this.selectSiteId = data['siteId'].toString();
    this.filterOnSite = true;
    this.populateDatatable();
    }
   hideDropdown(hideItem : any){
       this.hideSiteSelection = hideItem.hideItem;
   }
changechatroomscount(){
        var val=  $("#chatroom-messages-to-show").val();
        this.limit=val;
        //  this._structureService.getuserChatrooms(this.limit,0).then(( data ) => {      
        // this.userChatList = data['getSessionTenant'] ? JSON.parse(JSON.stringify(data['getSessionTenant']['chatRooms']) ) : [];             
      //  this.userChatList = this.userChatList.filter((req) => {
      //     if (req && req.messageCount!=0) {
      //      return true;
      //     } 
      //   });
        this.populateDatatable();
    // });
}


  userChatRoom() {
   
    // this._structureService.getuserChatrooms(this.limit,0).then(( data ) => {   
    //   console.log(data)  
    //     this.userChatList = data['getSessionTenant'] ? JSON.parse(JSON.stringify(data['getSessionTenant']['chatRooms']) ) : [];
        
    //     var ob=data['getSessionTenant']['chatRooms'];
    //     for(var i = 0; i <  ob.length; i++) {
    //       var obj =  ob[i];
      
    //       console.log(obj.id);
    //       console.log(obj.sender.displayName)
    //   }
        

      //  this.userChatList = this.userChatList.filter((req) => {
      //     if (req && req.messageCount!=0) {
      //      return true;
      //     } 
      //   });
        this.populateDatatable();
    // });
  }
    selectedDate(value: any, datepicker?: any) {
    // this is the date  selected
    console.log("selectedDate",value);
    // return false;
  this.selectedExport = true;
    // any object can be passed to the selected event and it will be passed back here
    datepicker.start = value.start;
    datepicker.end = value.end;
 
    // use passed valuable to update state
    this.daterange.start = value.start;
    this.daterange.end = value.end;
    this.daterange.label = value.label;
    var table = $('#chatroom-messages').DataTable();
    if(this.selectedExport == true)
    table.button('.buttons-excel').trigger();
  }
   toTimestamp(strDate){
    var datum = Date.parse(strDate);
   return (datum/1000).toString();
   }
  populateDatatable()
  {
    if (this.dTable) {
      this.dTable.fnDestroy();
      this._structureService.resetDataTable();
    }else{
      if(this._structureService.previousUrlNow.indexOf("/message/logs/chat-logs")==-1){  
        this._structureService.resetDataTable();
      }
    }
    var self=this;
    this.userRole = self._structureService.getCookie('userRole');
    // this.userChatList.reverse();
    var isTrue = false;
    if (this.userChatList.length > 100) {
      isTrue = true;
    }
    let datas: any;
    $(()=>{
    this.dTable = $('#chatroom-messages').dataTable({
      autoWidth: false,
      responsive: true,
      bprocessing: true,
      bServerSide: true,
      bpagination: true,
      bsorting: true,
      retrieve: true,
      stateSave: true,
      bsearching: true,              
      bInfo: true, 
      lengthMenu: [[25, 50,], [25,50]],
      fnDrawCallback: function(oSettings) {
        console.log('oSettings',oSettings);
        if (oSettings._iRecordsTotal < oSettings._iDisplayLength || oSettings._iRecordsTotal == 0 || oSettings.aoData.length ==0) {
          $('.dataTables_paginate').hide();
      }
      else
      {
          $('.dataTables_paginate').show();
      }
      if(oSettings.aoData.length ==0) 
      {
        $('.dataTables_info').hide();
      }
      else
      {
        $('.dataTables_info').show();
      }
      },
      fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
        $(nRow).on('click', () => {
          this._SharedService.goToInnerPage = true;
          this._SharedService.innerPageFilter = true;
          let type='';
          let groupname = '';
          if(aData.group.name) {
            groupname = aData.group.name;
            type = 'Group Name';
          } else {
            type = 'Initiator';
            groupname = 'nill';
          }
          this.senderId= aData.sender.id.split(",");
          this.initiatorFromLogs = parseInt(this.senderId[0]);
          localStorage.setItem('chatLogDetails', JSON.stringify({ id: aData.id, type: type, initiator: this.initiatorFromLogs, groupname: groupname}));
          this.router.navigate(['/message/logs/chat-logs']);
        });
      },
      dom:
			"<'row'<'col-sm-3'l><'col-sm-3'f><'col-sm-2 searchButton'>>" +
			"<'row'<'col-sm-12'tr>>" +
			"<'row'<'col-sm-5'i><'col-sm-7'p>>",
			  buttons: [
				{
				  extend: 'excel',
				  text: 'All Pages',
				  title: 'Chat logs',
				  exportOptions: {
            columns:[0,1,2,3,4,6,8]
				  },
				  action: function ( e, dt, node, config ) {
				  var selfButton = this;
				  var oldStart = dt.settings()[0]._iDisplayStart;
				  dt.one('preXhr', function (e, s, data) {
					  data.start = 0;
					  data.length = this.totalCt;
					  dt.one('preDraw', function (e, settings) {
						  $.fn.dataTable.ext.buttons.excelHtml5.action.call(selfButton, e, dt, node, config);
						  dt.one('preXhr', function (e, s, data) {
							  settings._iDisplayStart = oldStart;
							  data.start = oldStart;
						  });
						  setTimeout(dt.ajax.reload, 0);
						  return false;
					  });
				  });
				  dt.ajax.reload();
				  }
		  }],
     
      initComplete: function() {
        if(self.applyDateRangeFilter){
          $('div.dataTables_filter input').val('');
          self.storeService.removeData(Store.SEARCH_CHATLOG_MSG);
          self.resetFilterNotifyMsg();
          self.applyDateRangeFilter = false;
        }
        $('.dataTables_filter label input').attr('placeholder','Search');
        $('.dataTables_filter label input').unbind();
        $("div.dataTables_filter input").on('keydown', function(e) {
          if (e.which == 13) {
            var value = $("div.dataTables_filter input").val();
            if(value)
            {
            value = value.replace('”','"');
            value = value.replace("‘","'");
            value = value.replace("’","'");
            self.searchResetFlag = 0;
            self.dTable.fnFilter(value);
          }
          else
          {
              self.dTable.fnFilter("");
            }
          }
        });
        $("div.dataTables_filter input").on('keypress', function(e) {
          $(".searchBChat").prop('disabled', false);
        });
        $("div.dataTables_filter input").on('keyup', function(e) {
          var value = $("div.dataTables_filter input").val();
          if(value)
          {
          }
          else
          $(".searchBChat").prop('disabled', true);
        });
        $("div.searchButton")
        .html('<button disabled="true" class="btn btn-sm btn-info searchBChat" title="Search" type="submit">Search</button>'+
        '<button style="margin-left:10px;" class="btn btn-sm btn-default resetBChat" title="Reset" type="submit">Reset</button>');
        var value =  $("div.dataTables_filter input").val();
        if(value)
        {
          $(".searchBChat").prop('disabled', false);
        }
        $("div.dataTables_filter input").on('paste', function(event) {
          console.log("eeeeeeeeeeeeeeeeee",event);
          var element = this;
          var text ;
          setTimeout(function () {
            text = $(element).val();
            if(text)
            {
              $(".searchBChat").prop('disabled', false);
            }
        }, 100);
        });
        $(".buttons-collection").click(function(event) {
					setTimeout(function () {
					  if($(".buttons-collection").attr("aria-expanded") === "true" && $(".dt-button-collection").find("button").length == 0){
						$(".dt-button-collection").remove();
						$(".dt-button-background").remove();
						$(".buttons-collection").attr("aria-expanded","false");
					  }
					},500);
				});
     },
      "ajax": function (dat, callback, settings) {
        let orderData;
        let  searchText = '';
        let orderby;
        let startDate = '';
        let endDate = '';
        let label = '';
        console.log('searchData',dat);
        var i = dat.order[0].column ? dat.order[0].column : '';
        orderby =dat.order[0].dir ? dat.order[0].dir : '';
        if(i != 0)
        {
         orderData =dat.columns[i].data ? dat.columns[i].data : '';
        }
        else
        {
         orderData='';
        }
        if( !self.applyDateRangeFilter )
          searchText =dat.search.value ? dat.search.value : '';
        if(searchText &&  self.searchResetFlag == 0){
          settings.oAjaxData.search.value = searchText;
          self.searchResetFlag = 1;
        }
        if(self.applyDateRangeFilter || (searchText &&  self.searchResetFlag == 0)) {
          dat.start = 0;
          settings.oAjaxData.start = 0;
          settings._iDisplayStart = 0;
        }

         if(self.selectedExport == true){
          startDate = self.toTimestamp(self.daterange.start);
          endDate = self.toTimestamp(self.daterange.end);
          label = self.daterange.label;
          self.selectedExport = false;
        }
        if (self.filterOnSite) {
          dat.start = 0;
          self.filterOnSite = false;
        }
        if (!isBlank(dat.search.value)) {
          searchText = dat.search.value;
          self.storeService.storeData(Store.SEARCH_CHATLOG_MSG, searchText);
        } else if (!isBlank(self.storeService.getStoredData(Store.SEARCH_CHATLOG_MSG))) {
          searchText = self.storeService.getStoredData(Store.SEARCH_CHATLOG_MSG);
          $('div.dataTables_filter input').val(searchText);
          if(self.initialLoad) self._structureService.notifySearchFilterApplied(true);
        }
        if(self.dateRangeType != DateRanges.LAST_THIRTY_DAYS && self.initialLoad) {
          self._structureService.notifySearchFilterApplied(true);
        }
        self._structureService.getuserChatroomsLazy(dat.length, dat.start,searchText.trim(),orderData,orderby,startDate,endDate,self.selectSiteId).then((dataa) => {
          self.initialLoad = false;
            datas = {};
          self.datam = {};
          if (dat.start == 0) {
            this.totalCt = dataa['getSessionTenant'].chatRoomsPagination ? (dataa['getSessionTenant'].chatRoomsPagination.totalCount ? dataa['getSessionTenant'].chatRoomsPagination.totalCount : 0) : null;
            self._SharedService.chatLogCount = this.totalCt;
          }
          datas = dataa['getSessionTenant'] ? dataa['getSessionTenant']['chatRoomsPagination']['data'] : [];
          self.userChatList = datas;
          let draw;
          let total;
          if(datas && datas.length == 0 && searchText == '') 
          {
              total = 0;
            }
            else
            {
              total = self._SharedService.chatLogCount;
          }
          self.datam = {
          "draw": dat.draw,
          "recordsTotal": total,
          "recordsFiltered": total, 
           "aaData": datas
            };
            callback(self.datam)
        });
      },
      order: [[ 6, "desc" ]],
      columns: [
        { title: "#" },
        { title: `${this._ToolTipService.getTranslateData('LABELS.INITIATOR')} / ${this._ToolTipService.getTranslateData('LABELS.GROUP_NAME')}`, data: 'initiator' },
        { title: this._ToolTipService.getTranslateData('LABELS.CHAT_WITH'), data: 'participants' },
        { title: this._ToolTipService.getTranslateData('ADMISSION.LABELS.ADMISSION'), data: 'admissionName', visible: (self._structureService.isMultiAdmissionsEnabled) },
        { title: this.siteLabel, data: 'sitename'},
        { title: this._ToolTipService.getTranslateData('LABELS.SUBJECT'), data: 'topic' },
        { title: this._ToolTipService.getTranslateData('LABELS.TIME_START'), data: 'formattedDateStart' },
        { title: this._ToolTipService.getTranslateData('LABELS.STARTTIME'), data: 'formattedDateStart' },
        { title: this._ToolTipService.getTranslateData('LABELS.TIME_END'), data: 'formattedDate' },
        { title: this._ToolTipService.getTranslateData('LABELS.ENDTIME'), data: 'formattedDate' },
        { title: this._ToolTipService.getTranslateData('LABELS.MESSAGE_COUNT'), data: 'messageCount' }
      ],
      columnDefs: [ {
        data: null,
        orderable: false,
        render: function (data, type, row,meta) {
          return meta.row+1+meta.settings._iDisplayStart;
         },
        width: "5%",
        targets: 0
      },  {
          data: null,
          orderable: false,
        render: function (data, type, row) {
          if (row.chatThreadType && (row.chatThreadType === MessageType.PDG || row.chatThreadType === MessageType.MESSAGE_GROUP)
            && row.group && row.group.name) {
            return row.group.name;
          }
          return row.initiator;         
        },
         width: "15%",
        targets: 1
      }, {
        data: null,
        render: function (data, type, row) {
          let participants = [];
          for (var i = data.length - 1; i >= 0; i--) {
            participants.push(data[i].participant.displayName);
          }
          return participants.join(', ');
        },
        width: "60%",
          targets: 2,
          orderable: false,

      },{
        visible: (self._structureService.isMultiAdmissionsEnabled),
        width: "60%",
        targets: 3,
        orderable: true,
        render: function (data, type, row,meta) {
          if(row.admissionName) {
            return row.admissionName
          } 
          return '';
        },

      },{
        data: null,
        visible: (self.userRole != 'Patient' && self.hideSiteSelection),
        render: function (data, type, row) {
          if(row.sitename == null) {
            return '-';
          } else {
            return row.sitename;
          }
        },
        width: "60%",
          targets: 4,
          orderable: true,

      },{
        data: null,
        render: function (data, type, row) {
          if(row.topic == 'test' || row.topic == null) {
            return '-';
          } else {
            return row.topic;
          }
        },
        width: "60%",
          targets: 5,
          orderable: false,

      }, {
          data: null,
          orderData: 6,
          orderable: false,
          render: (data, type, row) => {
            try  {
             
              if (data) {
                 return this.chatLogdateshortFilterPipe.transform(data*1000);
              } else {
                return null;
              }
               
            }
            catch(err) {
               
                 return this.chatLogdateshortFilterPipe.transform(new Date(Date.parse(data)));
            }
           
            
          },
          width: "10%",
          targets: 6
      }, {
        data: null,
        visible: false,
        
        targets: 7
      },
      {
          data: null,
        orderData: 8,
        orderable: false,

          render: (data, type, row) => {
            try  {
             
              if (data) {
                 return this.chatLogdateshortFilterPipe.transform(new Date(data*1000));
              } else {
                return null;
              }
               
            }
            catch(err) {
               
                 return this.chatLogdateshortFilterPipe.transform(new Date(data*1000));
            }
           
            
          },
          width: "10%",
          targets: 8
      }, {
        data: null,
        visible: false,
       
        targets: 9
      },{
        data: null,
        orderable: false,
        render: function (data, type, row) {
          return `<span class="badge badge-pill badge-info mr-2 mb-2">${data}</span>`;
        },
        width: "10%",
        targets: 10
      }]
    });
    $(document).on('click', '.resetBChat',(event)=> {
      this.dTable.fnFilter("");
      self.storeService.removeData(Store.SEARCH_CHATLOG_MSG);
      $(".searchBChat").prop('disabled', true);
      self.resetFilterNotifyMsg();
    });
    $(document).on('click', '.searchBChat',(event)=> {
           var value = $('#chatroom-messages_wrapper #chatroom-messages_filter label input').val();
           if(value)
           {
           value = value.replace('”','"');
           value = value.replace("‘","'");
           value = value.replace("’","'");
           this.searchResetFlag = 0;
           this.dTable.fnFilter(value);
           }
           else
           {
             this.dTable.fnFilter("");
           }
     });
    // this.dTable.on('order.dt search.dt', () => {
    //   if(this.dTable.column(0, {search:'applied', order:'applied'}).nodes() && this.dTable.column(0, {search:'applied', order:'applied'}).nodes().length) {
    //     this.dTable.column(0, { search: 'applied', order: 'applied' }).nodes().each(function (cell, i) {
    //       cell.innerHTML = i + 1;
    //     });
    //   }
    // }).draw();
    // setTimeout(() => {
    //   this.userChatList.forEach( (item, key) => {
    //     this.dTable.row.add(item);
    //   } );
    //   this.dTable.draw();
    // }, 100);
    this.dataLoadingMsg = false;
    $(".fetch-entries").removeClass("element-hide");
  });
  }
  updateImageUrl(target) {
    console.log('iamgeUrl');
    target.src = this._structureService.imageBaseUrl+'profile-pic-clinician.png';
  }


  inArray(needle, haystack) {
    var length = haystack.length;
    for (var i = 0; i < length; i++) {
      if (haystack[i] == needle) return true;
    }
    return false;
  }

  removeClass(classname, element) {
    var cn = element.className;
    var rxp = new RegExp("s?b" + classname + "b", "g");
    cn = cn.replace(rxp, '');
    element.className = cn;
  }

  searchUser() {
    var search = $("#searchText").val();
    this._structureService.getlogMessages(search);
    this.logMessagesCount = Object.keys(this.logMessages).length;

    this._structureService.logMessagesObservable.subscribe(response => {
      if (response === 'ok') {
        this.logMessages = this._structureService.logUsersList;
        var userId = this.logMessages[0].id;
        this.initiator = this.logMessages[0].displayName;
        document.getElementById('log' + userId).classList.add('cat__apps__messaging__tab--selected');
        //this._structureService.getuserChatrooms(userId);
        this._structureService.chatMessagesObservable.subscribe(response => {
          if (response === 'ok') {
            this.userChatList = this._structureService.chatRoomList;
            if (this.userChatList.length > 0) {
              this.recordFound = true;
            } else {
              this.recordFound = false;
            }
          }
        })

        setTimeout(() => {
          document.getElementById('log' + userId).classList.add('cat__apps__messaging__tab--selected');
        }, 1);
      }
    });
  }

  /**
   * Function to reload the data table when date range is selected
   */
  onSelectDateRange(event){
    if (event.type) {
      this.dateRangeType = event.type;
    }
    if(isBlank(event.changeDetectedOnInit)) {
      this.applyDateRangeFilter = true;
      this.populateDatatable();
    }
    this.resetFilterNotifyMsg();
  }
  resetFilterNotifyMsg(){
    if(this.dateRangeType === DateRanges.LAST_THIRTY_DAYS && isBlank(this.storeService.getStoredData(Store.SEARCH_CHATLOG_MSG))) {
      this._structureService.notifySearchFilterApplied(false);
    }
  }
}
