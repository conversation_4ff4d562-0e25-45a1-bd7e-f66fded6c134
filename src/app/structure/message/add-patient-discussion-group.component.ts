import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { RegistrationService } from '../registration/registration.service';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { InboxService } from './../inbox/inbox.service';
import { filterMessageGroupPipe } from './messagegroup-search.pipes';
import { SharedService } from './../shared/sharedServices';
import { GlobalDataShareService } from './../shared/global-data-share.service';
import {
  FormsModule,
  FormBuilder,
  FormGroup,
  Validators,
  FormControl,
  FormArray,
  ReactiveFormsModule,
} from '@angular/forms';
import { Subject } from 'rxjs';
import { isBlank } from 'app/utils/utils';
import { isNull } from 'util';

declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;
declare var swal: any;
declare var NProgress: any;
declare var moment: any;
@Component({
  selector: 'app-add-patient-discussion-group',
  templateUrl: './add-patient-discussion-group.component.html',
  styleUrls: ['./patient-discussion-group-add-edit.css'],
})
export class AddPatientDiscussionGroupComponent implements OnInit {
  @ViewChild('userSearch') srch: ElementRef;
  @ViewChild('searchMembers') srchs: ElementRef;
  editGroupMessage = false;
  usersInRole = true;
  clickedRole = 0;
  addGroupMessage = false;
  searchInboxkeyword;
  groupList = [];
  staffList = [];
  otherTenantStaffList = [];
  memberList = [];
  otherTenantMemberList = [];
  offset = 0;
  limit = 25;
  allMessageGroups = [];
  hideLoadMore = false;
  selected;
  prevText;
  newMemberList = [];
  roleBasedStaffs = [];
  selectedroleBasedStaffs = [];
  newMemberOtherTenantList = [];
  newallUserDetailsInMsgGroup = [];
  selectedTenant: any;
  newMessageGroups = [];
  newMemberListByRoleWise = [];
  newMemberOtherTenantListByRoleWise = [];
  groupListNames = [];
  newMember;
  existingMemberIds = [];
  selectedGroupMembers = [];
  selectedGroupMemberIds = [];
  checkedIds = [];
  checkedRoleIds = [];
  checkedIdsWithRole = [];
  dummyCheckedRoleIds = [];
  removedRoleUsers = [];
  selectedisPublic = false;
  selectedAllowMultiThread = true;
  blockConfirm = true;
  messageGroup: FormGroup;
  updateParams;
  userDetails: any;
  userData;
  allowSave = true;
  membersLoaded = true;
  privilegeManageAllMessageGroup = false;
  addMemberError = false;
  MemberListByRoleWiseStaff = [];
  MemberListByRoleWisepartner = [];

  noMemberError = false;
  disableButton = true;
  loadingGroups = true;
  createdPatientIdIndex: any;
  messageGroups: any = 'groups';
  optionShow: any = 'staffs';
  userListChatwith: any;
  memberDataGroupWise: any = [];

  config: any = {};
  configData: any = {};
  clinicalUserDetails;
  allUserDetailsInMsgGroup = [];
  searchText = '';
  searchTexts = '';
  crossTenantOptions = [];
  staffRolesList = [];
  crossTenantName: String;
  newPDGSubscriber: any;
  patientAssoicatedId;
  flagPatient = false;
  MessageGroupWithLoader: any = {
    groups: true,
    staffs: true,
    partner: true,
    otherTenantstaff: true,
  };
  ispdgs;
  UsergroupIds;
  chatWithTenantFilter: any = {
    selectedTenant: null,
    selectedTenantName: null,
    tenants: [],
    filterEnabled: false,
    enabledReset: false,
  };
  chatWithUsersLoading = true;
  clinicianRolesAvaiable = null;
  chatWithModalShown: boolean = false;
  callFromInitialCountChatWithUserlist: boolean = false;
  noMoreItemsAvailable = {
    users: false,
  };
  chatwithPageCount = {
    staffs: 0,
    partner: 0,
    patients: 0,
  };
  loadMoremessage = {
    users: 'Load more',
  };
  chatWithLoader: any = {
    groups: true,
    staffs: true,
    partner: true,
    otherTenantstaff: false,
    patients: true,
    otherTenantPatients: true,
  };
  usersList: any;
  loadMoreSearchValue: any = undefined;
  privileges = this._structureService.getCookie('userPrivileges');
  crossTenantOptionsChatWith = [];
  enableCommunicationWithInternalstaffs = false;
  internalSiteId: any;
  crossTenatsForBranch = [];
  column3 = false;
  nursingAgencyTags: any = [];
  isNursingAgencyEnabled = 0;
  testArr = [];
  memberRoles = [];
  crossTenantRoles = [];
  memberRoleStaffs = [];
  crossTenantRoleStaffs = [];
  secondaryId;
  primaryId;
  primaryRolename;
  selectedcrossmemberRoleStaffs = [];
  registeredPdgRoles = [];
  member_role_detail1: any = [];
  member_role_detail2: any = [];
  rolename1: any = [];

  clickedGroup;
  isGrouploadingTime = false;
  dummyCheckedGroupMembers = [];
  messageGroupId: any;
  nursing_agency_on = false;
  eventsSubject: Subject<any> = new Subject<any>();
  selectSiteId: any;
  singleSelection: boolean = true;
  hideSiteSelection: boolean;
  multiTenant: boolean = false;
  selectedBranchId: any;
  multiSiteEnable: boolean;
  crossTenant: boolean;
  preventMultipleCall: boolean;
  usersSiteId: any;
  switchSite: boolean = false;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private _structureService: StructureService,
    private _ToolTipService: ToolTipService,
    private _inboxService: InboxService,
    private _formBuild: FormBuilder,
    private MessageGroupSearchFilter: filterMessageGroupPipe,
    public _GlobalDataShareService: GlobalDataShareService,
    public _SharedService: SharedService,
    private registrationservice: RegistrationService
  ) {
    this.config = this._structureService.userDataConfig;
    this.configData = JSON.parse(this.config);
  }

  ngOnInit() {
    $('#notFoundRoles').hide();

    this.userDetails = this._structureService.userDetails;
    this.userData = JSON.parse(this.userDetails);
    if (this._structureService.getCookie('siteCrossId')) {
      this.switchSite = true;
    }
    this.usersSiteId = this._structureService.getCookie('siteCrossId')
      ? this._structureService.getCookie('siteCrossId')
      : this.userData.mySites[0].id;
    this.multiSiteEnable =
      this.userData.config.enable_multisite == 1 ? true : false;
    if (
      this.userData &&
      this.userData.config &&
      this.userData.config.enable_multi_thread_pdg &&
      this.userData.config.enable_multi_thread_pdg == '0'
    )
      this.selectedAllowMultiThread = false;
    else this.selectedAllowMultiThread = true;
    // console.log("selectedAllowMultiThread",this.userData,this.userData.config.enable_multi_thread_pdg,this.selectedAllowMultiThread);
    // if (this.userData.config.enable_nursing_agencies_visibility_restrictions == 1 && this.userData.nursing_agencies != "") {
    //   this.nursing_agency_on = true;
    //   this.optionShow = 'partner';

    // }
    if (
      this._structureService
        .getCookie('userPrivileges')
        .indexOf('manageAllMessageGroup') != -1 ||
      this._structureService
        .getCookie('userPrivileges')
        .indexOf('manageTenants') != -1
    ) {
      this.privilegeManageAllMessageGroup = true;
    }
    // this.hideSiteSelection = this.userData.mySites.length == 1 ? true : false;
    if (
      this.configData.automatically_create_pdg_on_patient_enroll == 1 ||
      (this.configData.allow_multiple_organization == 1 &&
        this.userData.config.enable_multisite != 1)
    ) {
      if (
        this.configData.cross_tenant_roles_for_pdg_create_automatically != '' &&
        this.configData.member_roles_for_pdg_which_create_automatically != ''
      ) {
        var roleslist =
          this.configData.member_roles_for_pdg_which_create_automatically +
          ',' +
          this.configData.cross_tenant_roles_for_pdg_create_automatically;
        this.disableButton = false;
      } else if (
        this.configData.cross_tenant_roles_for_pdg_create_automatically == ''
      ) {
        roleslist =
          this.configData.member_roles_for_pdg_which_create_automatically;
        this.disableButton = false;
      } else if (
        this.configData.member_roles_for_pdg_which_create_automatically == ''
      ) {
        roleslist =
          this.configData.cross_tenant_roles_for_pdg_create_automatically;
        this.disableButton = false;
      } else {
        roleslist = '';
        this.disableButton = true;
      }
      console.log(
        'this.configData.member_roles_for_pdg_which_create_automatically',
        this.configData.member_roles_for_pdg_which_create_automatically
      );
      this.registeredPdgRoles = roleslist.split(',');
      var currentTenant = this._structureService.getCookie('tenantId');
      var status = 0;
      console.log(
        'this.configData.member_roles_for_pdg_which_create_automatically',
        this.registeredPdgRoles
      );
      if (roleslist != '') {
        this._inboxService.getRoleDetailsInit('', '0').then((roles) => {
          if (roles) {
            this.member_role_detail1 = Array.isArray(roles) ? roles : [];
            console.log(
              this.member_role_detail1,
              '___________________________'
            );
            this.getExistingMembersList();
          }
        });
      }
    }

    if (
      (this.privileges.indexOf(
        'allowMultipleOrganizationsStaffCommunication'
      ) !== -1 &&
        this.configData.allow_multiple_organization == 1 &&
        this.userData.crossTenantsDetails.length > 1 &&
        this.userData.masterEnabled == '0' &&
        this.configData.enable_nursing_agencies_visibility_restrictions != 1) ||
      (this.userData.masterEnabled == '1' && this.userData.isMaster == '1') ||
      (this.userData.masterEnabled == '1' &&
        this.userData.isMaster == '0' &&
        this.userData.group != '3') ||
      (this.privileges.indexOf(
        'allowMultipleOrganizationsStaffCommunication'
      ) !== -1 &&
        this.configData.allow_multiple_organization == 1 &&
        this.userData.crossTenantsDetails.length > 1 &&
        this.userData.masterEnabled == '0' &&
        this.configData.enable_nursing_agencies_visibility_restrictions ==
          '1' &&
        this.userData.nursing_agencies == '')
    ) {
      this.chatWithTenantFilter.filterEnabled = true;
      this.chatWithTenantFilter.setTenantDropDown = true;
    } else {
      this.chatWithTenantFilter.filterEnabled = false;
      this.chatWithTenantFilter.tenants = [];
    }
    if (
      this.userData.organizationMasterId != 0 &&
      this.userData.crossTenantsDetails &&
      this.userData.crossTenantsDetails.length &&
      this.userData.masterEnabled == '0'
    ) {
      if (
        this.userData.crossTenantsDetails.length > 1 &&
        this._structureService
          .getCookie('userPrivileges')
          .indexOf('allowMultipleOrganizationsStaffCommunication') !== -1 &&
        this.configData.allow_multiple_organization == 1 &&
        (this.configData.enable_nursing_agencies_visibility_restrictions != 1 ||
          (this.configData.enable_nursing_agencies_visibility_restrictions ==
            1 &&
            this.userData.nursing_agencies == ''))
      ) {
        this.column3 = true;
        this.crossTenatsForBranch = this.crossTenantOptions =
          this.userData.crossTenantsDetails;
        this.crossTenantOptionsChatWith = this.userData.crossTenantsDetails;
        this.chatWithTenantFilter.tenants = this.crossTenantOptionsChatWith;
        this.crossTenantOptions.forEach((tenant) => {
          if (tenant.id == this._structureService.getCookie('crossTenantId')) {
            this.crossTenantName = tenant.tenantName;
            this.selectedTenant = tenant;
            this.chatWithTenantFilter.selectedTenant = tenant.id;
            this._GlobalDataShareService.setSelectedTenantDetails(tenant);
          }
        });
      } else {
        this.crossTenatsForBranch = this.crossTenantOptions = [];
        this.column3 = false;
      }
    } else if (
      this.userData.masterEnabled == '1' &&
      this.userData.isMaster == '1'
    ) {
      this.column3 = true;
      this.crossTenatsForBranch = this.crossTenantOptions =
        this.userData.crossTenantsDetails;
      this.crossTenantOptionsChatWith = this.userData.crossTenantsDetails;
      this.chatWithTenantFilter.tenants = this.crossTenantOptionsChatWith;
      this.crossTenantOptions.forEach((tenant) => {
        if (tenant.id == this._structureService.getCookie('crossTenantId')) {
          this.crossTenantName = tenant.tenantName;
          this.selectedTenant = tenant;
          this.chatWithTenantFilter.selectedTenant = tenant.id;
          this._GlobalDataShareService.setSelectedTenantDetails(tenant);
        }
      });
    } else if (
      this.userData.masterEnabled == '1' &&
      this.userData.isMaster == '0'
    ) {
      this.column3 = true;
      this.crossTenantOptions = this.userData.crossTenantsDetails;
      this.crossTenantOptions.forEach((tenant) => {
        if (tenant.id == this._structureService.getCookie('crossTenantId')) {
          this.crossTenantName = tenant.tenantName;
          this.chatWithTenantFilter.selectedTenant = tenant.id;
          this._GlobalDataShareService.setSelectedTenantDetails(tenant);
          this.selectedTenant = tenant;
          this.chatWithTenantFilter.tenants = this.userData.crossTenantsDetails;
        }
      });
      this.crossTenantOptions.forEach((tenant) => {
        if (
          tenant.id ==
          (this._structureService.getCookie('crossTenantId') &&
          this._structureService.getCookie('crossTenantId') != 'undefined'
            ? this._structureService.getCookie('crossTenantId')
            : this.userData.tenantId)
        ) {
          this.selectedTenant = tenant;
          this.chatWithTenantFilter.selectedTenant = tenant.id;
          this._GlobalDataShareService.setSelectedTenantDetails(tenant);
          this.crossTenantOptions = this.userData.crossTenantsDetails;
          this.chatWithTenantFilter.tenants = this.crossTenantOptions;
          this.crossTenantOptions.forEach((tenant) => {
            if (
              tenant.id == this._structureService.getCookie('crossTenantId')
            ) {
              this.crossTenantName = tenant.tenantName;
            }
          });
        }
      });
      this.enableCommunicationWithInternalstaffs = true;
      this.internalSiteId = this.userData.master_details.id;

      if (
        this.userData.organizationMasterId != 0 &&
        this.userData.crossTenantsDetails &&
        this.userData.crossTenantsDetails.length > 1
      ) {
        this.crossTenantOptionsChatWith = this.userData.crossTenantsDetails;
        this.chatWithTenantFilter.tenants = this.crossTenantOptions;
        this.crossTenantOptions.forEach((tenant) => {
          if (tenant.id == this._structureService.getCookie('crossTenantId')) {
            this.crossTenantName = tenant.tenantName;
          }
        });
        if (
          (this.crossTenantOptions.length > 1 &&
            this.privileges.indexOf('allowOrganizationSwitching') != -1 &&
            this.privileges.indexOf(
              'allowMultipleOrganizationsStaffCommunication'
            ) !== -1 &&
            this.configData.allow_multiple_organization == 1 &&
            this.userData.masterEnabled == '0' &&
            this.configData.enable_nursing_agencies_visibility_restrictions !=
              1) ||
          (this.crossTenantOptions.length > 1 &&
            this.userData.isMaster == '1' &&
            this.userData.masterEnabled == '1') ||
          (this.crossTenantOptions.length > 1 &&
            this.userData.isMaster == '0' &&
            this.userData.masterEnabled == '1' &&
            this.userData.group != '3') ||
          (this.crossTenantOptions.length > 1 &&
            this.privileges.indexOf('allowOrganizationSwitching') != -1 &&
            this.privileges.indexOf(
              'allowMultipleOrganizationsStaffCommunication'
            ) !== -1 &&
            this.configData.allow_multiple_organization == 1 &&
            this.userData.masterEnabled == '0' &&
            this.configData.enable_nursing_agencies_visibility_restrictions ==
              1 &&
            this.userData.nursing_agencies == '')
        ) {
          if (
            this.userData.isMaster == '0' &&
            this.userData.masterEnabled == '1'
          ) {
            this.internalSiteId = this.userData.master_details.id;
          }
          this.chatWithLoader.otherTenantstaff = true;
          this.chatWithTenantFilter.tenants =
            this.chatWithTenantFilter.tenants.filter((tenant) => {
              if (
                this.userData.isMaster == '0' &&
                this.userData.masterEnabled == '1'
              ) {
                if (this.internalSiteId == tenant.id) {
                  return true;
                } else {
                  return false;
                }
              } else {
                return true;
              }
            });
          if (
            this.userData.isMaster == '0' &&
            this.userData.masterEnabled == '1'
          ) {
            if (this.userData.master_details.id != this.userData.tenantId) {
              this.crossTenantOptions.map((tenant) => {
                if (tenant.id == this.userData.tenantId) {
                  this.chatWithTenantFilter.tenants.unshift(tenant);
                }
              });
            }
          }
        } else {
          this.crossTenantOptionsChatWith = [];
          this.chatWithTenantFilter.tenants = [];
        }
      }
    } else {
      this.column3 = false;
      this.crossTenatsForBranch = this.crossTenantOptions = [];
    }
    console.log(this.privilegeManageAllMessageGroup);

    if (this.userData.nursing_agencies != '') {
      this.messageGroup = this._formBuild.group({
        messageGroupName: [
          '',
          [Validators.required, this.noWhitespaceValidator],
        ],
        messageGroupLastName: [
          '',
          [Validators.required, this.noWhitespaceValidator],
        ],
        messageGroupDob: ['', [Validators.required]],
        messageGroupBranch: ['0'],
        nursingAgencyUserTag: [''],
        patientUniqueId: [''],
      });
    } else {
      this.messageGroup = this._formBuild.group({
        messageGroupName: [
          '',
          [Validators.required, this.noWhitespaceValidator],
        ],
        messageGroupLastName: [
          '',
          [Validators.required, this.noWhitespaceValidator],
        ],
        messageGroupDob: ['', [Validators.required]],
        messageGroupBranch: ['0'],
        nursingAgencyUserTag: [''],
        patientUniqueId: [''],
      });
    }

    this._structureService.previousUrl = 'message/editmessagegroup';
    if (this.optionShow == 'staffs' || this.optionShow == 'partner') {
      this.userListChatwith = [];
      this.loadMoreUsers(this.optionShow, this.srch.nativeElement.value.trim());
      this.preventMultipleCall = false;
    }
    this.getGroups(true);

    setTimeout(() => {
      if ($('#messageTenantFilter').length) {
        $('#messageTenantFilter').select2({
          dropdownParent: $('#staffListblock'),
        });
        $('#messageTenantFilter').css('text-overflow', 'ellipsis');
        // this.selectElementTenant?$("#chatWithTenantFilter-invite").val(this.selectElementTenant).trigger("change"):$("#chatWithTenantFilter-invite").val(this.chatWithTenantFilter.selectedTenant).trigger("change");
      } else {
        // this.chatWithTenantFilter.setTenantDropDown = true;
      }
    });
    $('body').on('change', '#messageTenantFilter', (event) => {
      if (event.target.value) {
        var previousSelectedTenant = this.chatWithTenantFilter.tenants.find(
          (tenant) => tenant.id == this.chatWithTenantFilter.selectedTenant
        );
        var currentSelectedTenant = this.chatWithTenantFilter.tenants.find(
          (tenant) => tenant.id == this.chatWithTenantFilter.selectedTenant
        );
        var activityData = {
          activityName:
            'patientdiscussion Group ' +
            (this.optionShow == 'staffs'
              ? 'Staff '
              : this.optionShow == 'partner'
              ? 'partner '
              : this.optionShow == 'patient'
              ? 'Patient '
              : ' ') +
            'Tenant Switching',
          activityType: 'messaging',
          activityDescription:
            this.userData.displayName +
            ' (' +
            this.userData.userId +
            ') has selected tenant ' +
            currentSelectedTenant.tenantName +
            '(' +
            currentSelectedTenant.id +
            ')' +
            (previousSelectedTenant
              ? ' from tenant ' +
                previousSelectedTenant.tenantName +
                '(' +
                previousSelectedTenant.id +
                ')'
              : ''),
          tenantId:
            this._structureService.getCookie('crossTenantId') &&
            this._structureService.getCookie('crossTenantId') != 'undefined' &&
            this._structureService.getCookie('tenantId') !==
              this._structureService.getCookie('crossTenantId')
              ? this._structureService.getCookie('crossTenantId')
              : this.userData.tenantId,
        };
        this._structureService.trackActivity(activityData);
        this.filterBranch(event);
      }
    });
    var page = 'patient-discussion-groups';
    $('.refresh-page').tooltip({
      title: this._ToolTipService.getToolTip(page, 'PTDGP00001'),
    });
    $('.new-discussion-group').tooltip({
      title: this._ToolTipService.getToolTip(page, 'PTDGP00002'),
    });
    $('.make-public').tooltip({
      title: this._ToolTipService.getToolTip(page, 'PTDGP00003'),
    });
    $('.multi-chat-thread').tooltip({
      title: this._ToolTipService.getToolTip(page, 'PTDGP00006'),
    });
    $('.clear-btn-img').tooltip({
      title: this._ToolTipService.getToolTip(page, 'PTDGP00004'),
    });
    $('.toolSelect').tooltip({
      title: this._ToolTipService.getToolTip(page, 'PTDGP00005'),
    });

    $('body').on('keypress', 'input[type=text]', function (e) {
      var code = e.keyCode ? e.keyCode : e.which;
      console.log(e.keyCode);
      if (code == 13) {
        e.preventDefault();
      }
    });
    this.newPDGSubscriber =
      this._SharedService.patientDiscussionGroup.subscribe((clickedItem) => {
        this.getGroups(false);
      });

    /**
     * Get User tags with tag type (Nursing Agency) BioMatrix -  Nursing tags.
     */
    if (
      this.userData.config.enable_nursing_agencies_visibility_restrictions == 1
    ) {
      this.getNursingAgencyTags();
      this.isNursingAgencyEnabled =
        this.userData.config.enable_nursing_agencies_visibility_restrictions;
    }

    /*     $('body').on('change','#chatWithTenantFilter',(event)=>{
      if(event.target.value)  {
          var previousSelectedTenant = this.chatWithTenantFilter.tenants.find((tenant) => tenant.id == this.chatWithTenantFilter.selectedTenant);
          this.chatWithTenantFilter.selectedTenant = event.target.value;
          if(this.chatWithTenantFilter.enabledReset) {
              this.reset(this.optionShow);
              var currentSelectedTenant = this.chatWithTenantFilter.tenants.find((tenant) => tenant.id == this.chatWithTenantFilter.selectedTenant);
              var activityData = {
                  activityName: "Chat With " + ((this.optionShow == 'staffs') ? 'Staff ' : ((this.optionShow == 'patient') ? 'Patient ' : ' ')) +"Tenant Switching",
                  activityType: "messaging",
                  activityDescription: this.userData.displayName + " (" + this.userData.userId + ") has selected tenant " + currentSelectedTenant.tenantName + '(' + currentSelectedTenant.id +')' + ((previousSelectedTenant) ? (' from tenant '+ previousSelectedTenant.tenantName + '(' + previousSelectedTenant.id +')') : ''),
                  tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
              };
              this._structureService.trackActivity(activityData);
          } else {
              this.chatWithTenantFilter.enabledReset = true;
          }
      }
  }); */
    console.log(
      'HH**',
      this.userData.config.field_label_of_patient_association_in_pdg
    );
    if (
      this.userData.config.make_mrn_field_mandatory_in_patient_invite == '1' &&
      this.userData.config.associate_pdg_with_patient_id == '1'
    ) {
      console.log('##################################');
      this.messageGroup
        .get('patientUniqueId')
        .setValidators([Validators.required]);
      this.messageGroup.get('patientUniqueId').updateValueAndValidity();
    }
    this.crossTenant =
      this.userData.crossTenantsDetails.length > 1 ? true : false;
  }
  emitEventToUserTags(): any {
    this.eventsSubject.next();
  }

  getSiteIds(data: any) {
    if (this.userData.config.enable_multisite == 1) {
      this.selectedGroupMembers = [];
      this.checkedRoleIds = [];
    }
    if (
      this.userData.mySites &&
      this.userData.mySites.length == 1 &&
      this.userData.crosstenantSites.length == 1
    ) {
      this.selectSiteId = this.userData.mySites[0].id;
    } else {
      this.selectSiteId = data['siteId'].toString();
    }
    if (this.selectSiteId == 0) {
      this.disableButton = true;
    } else {
      this.disableButton = false;
    }
    console.log('this.selectSiteId', this.selectSiteId);
    if (
      this.userData.config.enable_multisite == 1 &&
      this.selectSiteId &&
      this.selectSiteId != '0'
    ) {
      this._inboxService
        .getRoleDetailsInit('', this.selectSiteId)
        .then((roles) => {
          if (roles) {
            this.member_role_detail1 = Array.isArray(roles) ? roles : [];
            console.log(
              this.member_role_detail1,
              '___________________________'
            );
            this.getExistingMembersList(false);
          }
        });
    }
    //to do CHPR-1107
    if (
      this.preventMultipleCall &&
      (!this.userData.config.enable_multisite ||
        this.userData.config.enable_multisite != 1) &&
      this.userData.config.enable_multisite == 1 &&
      this.optionShow != 'msggroups'
    ) {
      this.search(this.optionShow);
    }
  }
  getBranchIds(data: any) {
    if (
      this.optionShow != 'groups' &&
      this.optionShow != 'msggroups' &&
      this.chatWithTenantFilter.filterEnabled &&
      this.chatWithTenantFilter.tenants.length &&
      this.userData.config.enable_multisite != 1
    ) {
      this.multiTenant = true;
      this.selectedBranchId = data['siteId'].toString();
      this.search(this.optionShow);
    }
  }
  hideDropdown(hideItem: any) {
    this.hideSiteSelection = hideItem.hideItem;
  }
  getExistingMembersList(isMultiple = false) {
    var currentTenant = this._structureService.getCookie('tenantId');
    var status = 0;
    let existingRoles = [];
    if (!isBlank(this.checkedRoleIds)) {
      this.checkedRoleIds.forEach((val) => {
        existingRoles.push(val.id);
      });
    }
    this.member_role_detail1.forEach((role) => {
      let reqRoleIds = isMultiple ? existingRoles.toString() : role['id'];
      this._structureService
        .getRoleBasedStaffs(
          reqRoleIds,
          status,
          1,
          false,
          role['tenant_id'],
          this.selectSiteId,
          isMultiple
        )
        .then((data) => {
          let parsedResponceData = JSON.parse(JSON.stringify(data));
          this.memberRoleStaffs =
            parsedResponceData.getSessionTenant['roleBasedStaffs'];

          if (this.memberRoleStaffs) {
            this.memberRoleStaffs.forEach((ele) => {
              if (ele != null) {
                ele.assignRoles.forEach((e) => {
                  if (e['isPrimary'] == true) {
                    this.primaryId = e['tenantUsersRoleId'];
                    this.primaryRolename = e['roleName'];
                  }
                  if (e['isPrimary'] == false) {
                    this.secondaryId = e['tenantUsersRoleId'];
                  }
                });
                var ten_id;
                var ten_name;
                if (currentTenant == role['tenant_id']) {
                  ten_id = currentTenant;
                  ten_name = '';
                } else {
                  ten_id = role['tenant_id'];
                  ten_name = role['name'];
                }
                this.newMember = {
                  id: '',
                  displayName: '',
                  role: {},
                  tenantId: null,
                  tenantName: '',
                  seconRoleId: '',
                  primaryId: '',
                  otherTenantStaff: false,
                  naTags: '',
                  naTagNames: '',
                };
                this.newMember.role = role['id'];
                this.newMember.tenantId = ten_id;
                this.newMember.tenantName = ten_name;
                this.newMember.seconRoleId = this.secondaryId;
                this.newMember.primaryId = this.primaryId;
                this.newMember.id = ele.id;
                this.newMember.displayName = ele.displayName;
                this.newMember.displayname = ele.displayName;
                this.newMember.citusRoleId = role['citus_role_id'];
                this.newMember.naTags = ele.naTags;
                this.newMember.naTagNames = ele.naTagNames;
                this.newMember.siteId = role['siteId'];
                this.newMember.siteName = role['siteName'];

                if (
                  this.configData
                    .enable_nursing_agencies_visibility_restrictions == '1' &&
                  this.userData.nursing_agencies != '' &&
                  this.configData.na_staffs_chat_with_pharmacy == '0'
                ) {
                  var nur_login = this.userData.nursing_agencies.split(',');
                  if (ele.naTags != null && ele.naTags != '') {
                    var user_nur = ele.naTags.split(',');
                  } else {
                    user_nur = [];
                  }

                  user_nur.forEach((element1) => {
                    nur_login.forEach((element) => {
                      if (element1 == element) {
                        this.selectedGroupMembers.push(this.newMember);
                        this.userListChatwith = this.userListChatwith.filter(
                          function (members) {
                            return members.userid != ele.id;
                          }
                        );
                      }
                    });
                  });

                  var roleData = {
                    id: this.newMember.role,
                    name: role['role_name'],
                    tenantRoleId: this.newMember.role,
                    tenantId: ten_id,
                    tenantName: ten_name,
                    citusRoleId: role['citus_role_id'],
                    siteId: role['siteId'],
                    siteName: role['siteName'],
                  };

                  this.setEditcheckedRoleIds(roleData);

                  var user = {
                    id: this.newMember.id,
                    name: this.newMember.displayName,
                    role: this.newMember.role,
                    seconRoleId: this.newMember.seconRoleId,
                  };
                  this.testArr.push(user);
                } else if (
                  this.configData
                    .enable_nursing_agencies_visibility_restrictions == '1' &&
                  this.userData.nursing_agencies != '' &&
                  this.configData.na_staffs_chat_with_pharmacy == '1'
                ) {
                  var nur_login = this.userData.nursing_agencies.split(',');
                  if (ele.naTags != null && ele.naTags != '') {
                    var user_nur = ele.naTags.split(',');
                  } else {
                    user_nur = [];
                    this.selectedGroupMembers.push(this.newMember);
                    this.userListChatwith = this.userListChatwith.filter(
                      function (members) {
                        return members.userid != ele.id;
                      }
                    );
                  }

                  user_nur.forEach((element1) => {
                    nur_login.forEach((element) => {
                      if (element1 == element) {
                        this.selectedGroupMembers.push(this.newMember);
                        this.userListChatwith = this.userListChatwith.filter(
                          function (members) {
                            return members.userid != ele.id;
                          }
                        );
                      }
                    });
                  });

                  var roleData = {
                    id: this.newMember.role,
                    name: role['role_name'],
                    tenantRoleId: this.newMember.role,
                    tenantId: ten_id,
                    tenantName: ten_name,
                    citusRoleId: role['citus_role_id'],
                    siteId: role['siteId'],
                    siteName: role['siteName'],
                  };

                  this.setEditcheckedRoleIds(roleData);

                  var user = {
                    id: this.newMember.id,
                    name: this.newMember.displayName,
                    role: this.newMember.role,
                    seconRoleId: this.newMember.seconRoleId,
                  };
                  this.testArr.push(user);
                } else {
                  this.selectedGroupMembers.push(this.newMember);
                  this.userListChatwith = this.userListChatwith.filter(
                    function (members) {
                      return members.userid != ele.id;
                    }
                  );
                  var roleData = {
                    id: this.newMember.role,
                    name: role['role_name'],
                    tenantRoleId: this.newMember.role,
                    tenantId: ten_id,
                    tenantName: ten_name,
                    citusRoleId: role['citus_role_id'],
                    siteId: role['siteId'],
                    siteName: role['siteName'],
                  };

                  this.setEditcheckedRoleIds(roleData);

                  var user = {
                    id: this.newMember.id,
                    name: this.newMember.displayName,
                    role: this.newMember.role,
                    seconRoleId: this.newMember.seconRoleId,
                  };
                  this.testArr.push(user);
                }
              }
            });
          } else {
            var roleData = {
              id: role['id'],
              name: role['role_name'],
              tenantRoleId: role['id'],
              tenantId: role['tenant_id'],
              tenantName: role['name'],
              citusRoleId: role['citus_role_id'],
              siteId: role['siteId'],
              siteName: role['siteName'],
            };

            this.setEditcheckedRoleIds(roleData);
          }

          this.memberRoleStaffs = [];
        });
    });
  }
  onBlurMethodForId(id, f) {
    if (!f.valid || this.noMemberError) {
      if (!f.valid && !this.noMemberError) {
        $('input.ng-invalid')[0].focus();
      } else if (this.noMemberError) {
        $('html, body').animate({
          scrollTop: $('.card.existing .card-block').offset().top,
        });
      }
      return false;
    }
    this.disableButton = true;
    NProgress.start();
    console.log('blur', this.messageGroup.controls['messageGroupName'].value);
    console.log(
      'blur',
      this.messageGroup.controls['messageGroupLastName'].value
    );
    console.log('blur', this.messageGroup.controls['messageGroupDob'].value);
    console.log('blur', this.messageGroup.controls['patientUniqueId'].value);
    console.log(
      'blur',
      this.messageGroup.controls['nursingAgencyUserTag'].value
    );
    var self = this;
    var messageGroupName;
    var messageGroupLastName;
    var messageGroupDob;
    var patientUniqueId;
    var messageGroup;
    var nursingAgencyTags;
    var branch;
    var esiPatient = this.userData.config.esi_code_for_patient_identity;
    messageGroupName =
      this.messageGroup.controls['messageGroupName'].value.trim();
    messageGroupLastName =
      this.messageGroup.controls['messageGroupLastName'].value.trim();
    messageGroupDob =
      this.messageGroup.controls['messageGroupDob'].value.trim();
    patientUniqueId =
      this.messageGroup.controls['patientUniqueId'].value.trim();
    nursingAgencyTags =
      this.messageGroup.controls['nursingAgencyUserTag'].value;
    branch = this.messageGroup.controls['messageGroupBranch'].value;
    console.log('branchbranchbranchbranchbranchbranchbranch', branch);
    var idValue = this.userData.config.field_label_of_patient_association_in_pdg
      ? this.userData.config.field_label_of_patient_association_in_pdg
      : 'ID#';
    if (patientUniqueId) {
      messageGroup =
        this.messageGroup.controls['messageGroupName'].value.trim() +
        ' ' +
        this.messageGroup.controls['messageGroupLastName'].value.trim() +
        ' DOB ' +
        this.messageGroup.controls['messageGroupDob'].value.trim();
      console.log('blur', messageGroup);
      this._structureService
        .checkUniqueIdEXists(
          patientUniqueId,
          esiPatient,
          messageGroup,
          (messageGroupName = null),
          (messageGroupLastName = null),
          (messageGroupDob = null),
          branch,
          true
        )
        .then((data) => {
          let res: any = data;
          if(isBlank(res.getSessionTenant) && !isBlank(res)){
            this._structureService.notifyMessage({
              messge: res.toString().split(':')[2],
              delay: 1000,
              type: 'danger'
            });
            NProgress.done();
          } else if (!isNull(res.getSessionTenant.checkExternalIntegrationSystem)) {
            if (data['getSessionTenant']) {
              let resultData =
                data['getSessionTenant']['checkExternalIntegrationSystem'];
              let dateParts;
              let date = '';
              if (
                resultData.dateOfBirth &&
                resultData.dateOfBirth.includes('-')
              ) {
                dateParts = resultData.dateOfBirth.split('-');
                if (dateParts[2].length == 1)
                  date =
                    dateParts[1] + '/0' + dateParts[2] + '/' + dateParts[0];
                else
                  date = dateParts[1] + '/' + dateParts[2] + '/' + dateParts[0];
              } else {
                date = resultData.dateOfBirth;
              }
              let messageGroupNew =
                resultData.firstName +
                ' ' +
                resultData.lastName +
                ' DOB ' +
                date;
              console.log('blur', messageGroup);
              console.log(
                'dataaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
                resultData
              );
              console.log(
                'dataaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
                self.selected,
                patientUniqueId
              );
              if (!resultData.pstatus && !resultData.pmstatus) {
                this.updateMessageGroup(id, f);
              }
              console.log(
                resultData.pstatus +
                  ' == 1 && ' +
                  resultData.status +
                  ' == 1 && ' +
                  messageGroup +
                  ' !== ' +
                  messageGroupNew +
                  ' && ' +
                  patientUniqueId +
                  ' !== ' +
                  resultData.IdentityValue
              );
              if (resultData.pstatus == 1) {
                console.log('vguygu');
                if (
                  resultData.firstName == messageGroupName &&
                  resultData.lastName == messageGroupLastName &&
                  date == messageGroupDob &&
                  resultData.IdentityValue == patientUniqueId
                ) {
                  this.updateMessageGroup(id, f);
                } else if (resultData.pmstatus != 1) {
                  return swal(
                    {
                      title: 'Are you sure?',
                      text:
                        'Patient ' +
                        resultData.firstName +
                        ' ' +
                        resultData.lastName +
                        ' DOB ' +
                        date +
                        ' already exist with this ' +
                        idValue +
                        ' (' +
                        patientUniqueId +
                        '). Do you want to overwrite?',
                      type: 'warning',
                      showCancelButton: true,
                      cancelButtonClass: 'btn-default',
                      confirmButtonClass: 'btn-warning',
                      confirmButtonText: 'Ok',
                      closeOnConfirm: true,
                    },
                    function (isConfirm) {
                      if (isConfirm) {
                        self.messageGroup.patchValue({
                          messageGroupName: resultData.firstName,
                          messageGroupLastName: resultData.lastName,
                          messageGroupDob: date,
                          patientUniqueId: resultData.IdentityValue,
                        });
                        self.blockConfirm = false;
                        self.updateMessageGroup(id, f, resultData.patientId);
                      } else {
                        self.disableButton = false;
                        NProgress.done();
                      }
                    }
                  );
                } else if (resultData.pmstatus == 1) {
                  var notify = $.notify(
                    'PDG ' +
                      resultData.message +
                      ' already exist with this ' +
                      idValue +
                      ' (' +
                      patientUniqueId +
                      ')'
                  );
                  setTimeout(function () {
                    notify.update({
                      type: 'danger',
                      message:
                        '<strong>PDG ' +
                        resultData.message +
                        ' already exist with this ' +
                        idValue +
                        ' (' +
                        patientUniqueId +
                        ')</strong>',
                    });
                  }, 5000);
                  self.disableButton = false;
                  NProgress.done();
                } else {
                  this.updateMessageGroup(id, f);
                  NProgress.done();
                }
              }
            }
          } else {
            this.disableButton = true;
            let activityData = {
              activityName: 'Error Check External integration System',
              activityType: 'checkExternalIntegrationSystem',
              activityDescription: `Error occured while checking External integration System : due to invalid input data.`,
            };
            this._structureService.notifyMessage({
              messge: this._ToolTipService.getTranslateData(
                'MESSAGES.ERROR_MSG_INVALID_INPUT'
              ),
              delay: 1000,
              type: 'warning',
            });
            this.disableButton = false;
            this._structureService.trackActivity(activityData);
          }
        });
    } else {
      this.updateMessageGroup(id, f);
    }
  }

  ngOnDestroy() {
    if (this.newPDGSubscriber) {
      this.newPDGSubscriber.unsubscribe();
    }
  }
  setEditcheckedRoleIds(role) {
    var exist = false;
    if (this.checkedRoleIds && this.checkedRoleIds.length > 0) {
      this.checkedRoleIds.forEach((value) => {
        if (value.id == role.id) {
          exist = true;
        }
      });
    }
    if (!exist) {
      this.checkedRoleIds.push(role);
    }
  }

  searchOnKeyPress(event) {
    var search_key_word = $('#userSearchTxtRoles').val().trim().toLowerCase();
    var key = event.keyCode || event.charCode;
    if (key == 8 || key == 46) {
      $('li.roleslisting').filter(function () {
        $(this).toggle(
          $(this).find('label').text().toLowerCase().indexOf(search_key_word) >
            -1
        );
      });
      if (
        $('li.roleslisting')
          .find('label')
          .text()
          .toLowerCase()
          .indexOf(search_key_word) == -1
      )
        $('#notFoundRoles').show();
      else $('#notFoundRoles').hide();
    } else {
      $('li.roleslisting').filter(function () {
        $(this).toggle(
          $(this).find('label').text().toLowerCase().indexOf(search_key_word) >
            -1
        );
      });
      if (
        $('li.roleslisting')
          .find('label')
          .text()
          .toLowerCase()
          .indexOf(search_key_word) == -1
      )
        $('#notFoundRoles').show();
      else $('#notFoundRoles').hide();
    }
  }
  checkboxChanged(event, indexing) {
    this.addMemberError = false;
    var $this = $(event.target),
      checked = $this.prop('checked'),
      container = $this.parent(),
      siblings = container.siblings();
    console.log('event========>', indexing);
    console.log(event);
    console.log(event.target.name);
    console.log('event.target.name->', event.target.name);
    if (event.target.name == 'messageGroup') {
      // this.isGrouploadingTime = true;
      this.clickedGroup = String(event.target.value);
      this.membersLoaded = false;
      console.log('event messageGroup ->');
      console.log('----event.target.checked  ->', event.target.checked);
      if (event.target.checked) {
        console.log('Enter event.target.checked ');
        console.log('----event.target.value  ->', event.target.value);
        var value = JSON.parse(event.target.value);
        var messageGroupId = String(value);
        this.messageGroupId = messageGroupId;
        console.log(
          '->',
          value,
          typeof value,
          messageGroupId,
          typeof messageGroupId
        );
        if (this.dummyCheckedGroupMembers.indexOf(messageGroupId) == -1) {
          this.dummyCheckedGroupMembers.push(messageGroupId);
          console.log('messageGroupId', messageGroupId);
        }
        if (
          this.dummyCheckedGroupMembers &&
          this.dummyCheckedGroupMembers.length > 0
        ) {
          this.membersLoaded = true;
          console.log(
            '->dummyCheckedGroupMembers',
            this.dummyCheckedGroupMembers
          );
        }
        // this.getMessageGroupMembersByGroupId( this.messageGroupId);
        // var selectedMessageGroup = this.memberDataGroupWise.findIndex(group => group.id === String(messageGroupId));
        // console.log("-> after add button",selectedMessageGroup,this.memberDataGroupWise,this.memberDataGroupWise[selectedMessageGroup]);
        // console.log("->this.selectedGroupMembers",this.selectedGroupMembers);
        // console.log("->this.selectedGroupMembers",this.selectedGroupMembers);
      } else {
        // this.isGrouploadingTime = false;
        console.log('Enter not event.target.checked ');
        console.log('->', this.memberDataGroupWise);
        var value = JSON.parse(event.target.value);
        var messageGroupId = String(value);
        console.log(
          'this.dummyCheckedGroupMembers.indexOf(messageGroupId)',
          this.dummyCheckedGroupMembers.indexOf(messageGroupId)
        );
        let index = this.dummyCheckedGroupMembers.indexOf(messageGroupId);
        this.dummyCheckedGroupMembers.splice(index, 1);
        this.membersLoaded = true;
      }
    }
    if (event.target.name == 'staffRoleWise') {
      this.membersLoaded = false;
      var value = JSON.parse(event.target.value);
      console.log('Enter event.target.name==staffRoleWise', value);
      if (event.target.checked) {
        this.clickedRole = roleID;

        console.log('Enter event.target.checked ');
        if (this.dummyCheckedRoleIds.indexOf(value.id) == -1)
          this.dummyCheckedRoleIds.push(value.id);
        var roleID = value.id;
        this.getRoleBasedStaff(roleID, indexing);
      } else {
        this.newMemberListByRoleWise[indexing]['userList'] = [];
        console.log('Enter not event.target.checked ');
        let index = this.dummyCheckedRoleIds.indexOf(value.id);
        this.dummyCheckedRoleIds.splice(index, 1);
        console.log(this.selectedroleBasedStaffs);
        console.log(this.dummyCheckedRoleIds);
        var roleID = value.id;
        // this.dummyCheckedRoleIds.forEach((value)=>{
        this.selectedroleBasedStaffs = this.selectedroleBasedStaffs.filter(
          function (selectedRole) {
            return selectedRole.selectedRoleid != roleID;
          }
        );
        //  });
        this.membersLoaded = true;
      }
      console.log('this.dummyCheckedRoleIds========>');
      console.log(this.dummyCheckedRoleIds);
      console.log(this.selectedroleBasedStaffs);
    }
    container
      .find('input[type="checkbox"]')
      .prop({
        indeterminate: false,
        checked: checked,
      })
      .siblings('label')
      .removeClass('custom-checked custom-unchecked custom-indeterminate')
      .addClass(checked ? 'custom-checked' : 'custom-unchecked');

    this.checkSiblings(container, checked);
  }

  checkSiblings($el, checked) {
    var parent = $el.parent().parent(),
      all = true,
      indeterminate = false;

    $el.siblings().each(function () {
      return (all =
        $(this).children('input[type="checkbox"]').prop('checked') === checked);
    });

    if (all && checked) {
      parent
        .children('input[type="checkbox"]')
        .prop({
          indeterminate: false,
          checked: checked,
        })
        .siblings('label')
        .removeClass('custom-checked custom-unchecked custom-indeterminate')
        .addClass(checked ? 'custom-checked' : 'custom-unchecked');

      this.checkSiblings(parent, checked);
    } else if (all && !checked) {
      indeterminate = parent.find('input[type="checkbox"]:checked').length > 0;

      parent
        .children('input[type="checkbox"]')
        .prop('checked', checked)
        .prop('indeterminate', indeterminate)
        .siblings('label')
        .removeClass('custom-checked custom-unchecked custom-indeterminate')
        .addClass(
          indeterminate
            ? 'custom-indeterminate'
            : checked
            ? 'custom-checked'
            : 'custom-unchecked'
        );

      this.checkSiblings(parent, checked);
    } else {
      $el
        .parents('li')
        .children('input[type="checkbox"]')
        .prop({
          indeterminate: true,
          checked: false,
        })
        .siblings('label')
        .removeClass('custom-checked custom-unchecked custom-indeterminate')
        .addClass('custom-indeterminate');
    }
  }

  callAccordion(roleID, eve) {
    console.log(eve);
    if (this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
      console.log('-->staffroles/partnerroles');
      this.membersLoaded = false;
      this.clickedRole = roleID;
    }
    if (this.optionShow == 'msggroups' || this.optionShow === 'groups') {
      console.log('-->msggroups/groups');
      this.membersLoaded = false;
      this.isGrouploadingTime = true;
      this.clickedGroup = roleID;
    }
    if ($('.expand-icon-' + roleID).hasClass('fa-plus')) {
      console.log('-->expand-icon');
      if (
        this.optionShow == 'staffroles' ||
        this.optionShow == 'partnerroles'
      ) {
        console.log('-->staffroles/partnerroles');
        this.membersLoaded = false;
        this.getRoleBasedStaff(roleID, eve, true);
      }
      if (this.optionShow == 'msggroups' || this.optionShow === 'groups') {
        console.log('-->msggroups');
        var messageGroupId = roleID;
        this.getMessageGroupMembersByGroupId(messageGroupId, true);
      }
      $('.expand-icon-' + roleID).removeClass('fa-plus');
      $('.expand-icon-' + roleID).addClass('fa-minus');
      $('.sub-item-panel-' + roleID).addClass('showall');
    } else {
      console.log('-->fa-minus');
      this.isGrouploadingTime = false;
      this.usersInRole = true;

      $('.expand-icon-' + roleID).removeClass('fa-minus');
      $('.expand-icon-' + roleID).addClass('fa-plus');
      $('.sub-item-panel-' + roleID).removeClass('showall');
    }
  }

  initialiseStaffOrPatientList(optionShow) {
    if (
      this.chatWithTenantFilter.setTenantDropDown &&
      this.chatWithTenantFilter.selectedTenant
    ) {
      this.chatWithTenantFilter.setTenantDropDown = false;
      setTimeout(() => {
        $('#messageTenantFilter').select2({
          dropdownParent: $('#staffListblock'),
        });
        // $("#messageTenantFilter").val(this.chatWithTenantFilter.selectedTenant).trigger("change");
        $('#messageTenantFilter').css('text-overflow', 'ellipsis');
      });
    }
    console.log('initialiseStaffOrPatientList.optionShow', optionShow);
    this.loadMoreSearchValue = undefined;
    this.callFromInitialCountChatWithUserlist = true;
    this.chatwithPageCount = {
      staffs: 0,
      partner: 0,
      patients: 0,
    };
    this.noMoreItemsAvailable = {
      users: false,
    };
    this.userListChatwith = [];
    this.clinicalUserDetails = [];
    this.usersList = [];
    var isRoleAvailable = true;
    var clinicianRolesAvaiable = null;
    var setCliniciansRoleAvailableResponse;

    if (isRoleAvailable) {
      this.getUsersListByRoleIdAvailableOnClick(
        clinicianRolesAvaiable,
        true,
        false,
        optionShow,
        undefined
      );
    } else {
      this.chatWithUsersLoading = false;
    }

    /** ***********************End Section ****************** */
  }
  filterBranch(event) {
    if (this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
      $('#userSearchTxtRoles').val('');
      $('#notFoundRoles').hide();
    }
    if (event.target.value) {
      var previousSelectedTenant = this.chatWithTenantFilter.tenants.find(
        (tenant) => tenant.id == this.chatWithTenantFilter.selectedTenant
      );
      this.chatWithTenantFilter.selectedTenant = event.target.value;
      this.chatWithTenantFilter.selectedTenantName =
        event.target.selectedOptions.text;
      if (this.chatWithTenantFilter.filterEnabled) {
        if (this.optionShow == 'staffs' || this.optionShow == 'partner') {
          this.reset(this.optionShow);
        }
        if (
          this.optionShow == 'staffroles' ||
          this.optionShow == 'partnerroles'
        ) {
          this.chatWithLoader.otherTenantstaff = true;
          this.getStaffRoles(this.optionShow);
        }

        var currentSelectedTenant = this.chatWithTenantFilter.tenants.find(
          (tenant) => tenant.id == this.chatWithTenantFilter.selectedTenant
        );
        var activityData = {
          activityName:
            'Chat With ' +
            (this.optionShow == 'staffs'
              ? 'Staff '
              : this.optionShow == 'partner'
              ? 'partner'
              : this.optionShow == 'patient'
              ? 'Patient '
              : ' ') +
            'Tenant Switching',
          activityType: 'messaging',
          activityDescription:
            this.userData.displayName +
            ' (' +
            this.userData.userId +
            ') has selected tenant ' +
            currentSelectedTenant.tenantName +
            '(' +
            currentSelectedTenant.id +
            ')' +
            (previousSelectedTenant
              ? ' from tenant ' +
                previousSelectedTenant.tenantName +
                '(' +
                previousSelectedTenant.id +
                ')'
              : ''),
          tenantId:
            this._structureService.getCookie('crossTenantId') &&
            this._structureService.getCookie('crossTenantId') != 'undefined' &&
            this._structureService.getCookie('tenantId') !==
              this._structureService.getCookie('crossTenantId')
              ? this._structureService.getCookie('crossTenantId')
              : this.userData.tenantId,
        };
        this._structureService.trackActivity(activityData);
      } else {
        this.chatWithTenantFilter.enabledReset = true;
      }
    }
  }
  getStaffRoles(optionShow = '') {
    $('#notFoundRoles').hide();
    /* if (this._structureService.messageGroupsWithPrivilege && this._structureService.messageGroupsWithPrivilege.length) {
      console.log("Enter first ifffffffff");
      console.log(this._structureService.messageGroupsWithPrivilege);
      this.setmessageGroup(this._structureService.messageGroupsWithPrivilege); */
    //} else {
    //this.getAllMessageGroupDetails(this.userData.tenantId, this.userData.userId);
    //}
    var selectedtenant = this.chatWithTenantFilter.selectedTenant
      ? this.chatWithTenantFilter.selectedTenant
      : this.userData.tenantId;
    //var selectedTenantName = this.chatWithTenantFilter.selectedTenantName ? this.chatWithTenantFilter.selectedTenantName :this.userData.tenantName;
    this.staffList = [];

    this._inboxService
      .getStaffRolesListByTenantId(
        selectedtenant,
        optionShow,
        this.selectedBranchId
      )
      .then((data) => {
        if (data) {
          //this.groupList = data['getSessionTenant'].messageGroupsPagination.data;

          // this.groupListNames = this.groupList;
          this.staffRolesList = Array.isArray(data) ? data : [];
          this.staffRolesList.forEach((value) => {
            var staffrollData = {
              id: value.RoleID,
              displayName: value.RoleName,
              tenantId: value.tenant_id,
              tenantName: value.TenantName,
              naTags: value.naTags,
              naTagNames: value.naTagNames,
              citusRoleId: value.citus_role_id,
              siteId: value.siteId,
              siteName: value.siteName,
            };
            this.staffList.push(staffrollData);
          });
          this.staffList.slice();
          this.newMemberListByRoleWise = [];
          this.newMemberList = [];
          console.log('this.staffList', this.staffList);
          if (this.staffList.length > 0) {
            this.staffList.forEach((value) => {
              if (value.id) {
                this.newMember = {
                  id: '',
                  displayName: '',
                  role: {},
                  tenantId: null,
                  tenantName: '',
                  otherTenantUser:
                    value.tenantId && this.userData.tenantId != value.tenantId
                      ? true
                      : false,
                };

                console.log('this.staffList', this.staffList);
                this.newMember.tenantId = value.tenantId;
                this.newMember.tenantName = value.tenantName;
                var roleData = {
                  id: value.id,
                  name: value.displayName,
                  tenantId: value.tenantId,
                  tenantName: value.tenantName,
                  tenantRoleId: value.tenantId,
                  citusRoleId: value.citusRoleId,
                  siteId: value.siteId,
                  siteName: value.siteName,
                };
                console.log('this.roleData', roleData);

                if (!this.newMemberListByRoleWise[value.id]) {
                  this.newMemberListByRoleWise[value.id] = {};
                }
                this.newMemberListByRoleWise[value.id]['roleData'] = roleData;
                this.newMemberListByRoleWise[value.id]['tenantId'] =
                  value.tenantId;
                this.newMemberListByRoleWise[value.id]['tenantName'] =
                  value.tenantName;
              }
              // this.MemberListByRoleWise =this.newMemberListByRoleWise;
            });
          }
          console.log(
            'this.newMemberListByRoleWise',
            this.newMemberListByRoleWise
          );
          this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter(
            function (a) {
              return a && a.tenantId;
            }
          );

          if (optionShow == 'staffroles') {
            this.MemberListByRoleWiseStaff = this.newMemberListByRoleWise;
          } else if (optionShow == 'partnerroles') {
            this.MemberListByRoleWisepartner = this.newMemberListByRoleWise;
          }
          console.log(
            'MemberListByRoleWiseStaff',
            this.MemberListByRoleWiseStaff
          );
          console.log(
            'MemberListByRoleWisepartner',
            this.MemberListByRoleWisepartner
          );
          console.log('this.checkedRoleIds', this.checkedRoleIds);
          if (this.checkedRoleIds.length) {
            this.checkedRoleIds.forEach((value) => {
              console.log('this.checkedRoleIdsvalue.id', value.id);
              console.log(
                'this.newMemberListByRoleWise',
                this.newMemberListByRoleWise
              );
              this.newMemberListByRoleWise =
                this.newMemberListByRoleWise.filter(function (members) {
                  console.log(
                    'this.members.roleData.id.id',
                    members.roleData.id
                  );
                  console.log('this.members', members);
                  return members.roleData.id != value.id;
                });
            });
          }
          this.newMemberListByRoleWise.sort(function (a, b) {
            if (a.roleData.name < b.roleData.name) return -1;
            if (a.roleData.name > b.roleData.name) return 1;
            return 0;
          });
          this.chatWithLoader.otherTenantstaff = false;
        } else {
          this.chatWithLoader.otherTenantstaff = false;
          this._structureService.deleteCookie('authenticationToken');
          this.router.navigate(['/login']);
        }
      });
  }
  getMessageGroupMembersByGroupId(messageGroupId, choose = false) {
    console.log(
      '-->this.memberDataGroupWise--->',
      this.memberDataGroupWise,
      typeof messageGroupId,
      messageGroupId
    );
    this.membersLoaded = false;
    var self = this;
    this._structureService
      .getMessageGroupMembersByGroupId(messageGroupId, this.selectSiteId)
      .then((data) => {
        // let responseData = data["messageGroupMembers"];
        let responseData = data['messageGroupMembers'].filter(
          (item) => item.roleId != '3'
        );
        if (responseData && responseData.length) {
          responseData.sort(function (a, b) {
            if (a.name.toLowerCase() < b.name.toLowerCase()) return -1;
            if (a.name.toLowerCase() > b.name.toLowerCase()) return 1;
            return 0;
          });
        }
        if (choose == false)
          this.selectedGroupMembers = [
            ...this.selectedGroupMembers,
            ...responseData,
          ];
        console.log(
          '-->responseData.length',
          responseData,
          typeof messageGroupId,
          messageGroupId
        );
        this.isGrouploadingTime = false;
        if (responseData && responseData.length != 0) {
          if (
            typeof messageGroupId == 'object' &&
            messageGroupId &&
            messageGroupId.length > 0
          ) {
            if (responseData && responseData.length > 0) {
              self.addMemberToList(messageGroupId);
            }
          } else {
            let selectedMessageGroupIndex = this.memberDataGroupWise.findIndex(
              (group) => group.id === String(messageGroupId)
            );
            console.log(
              '-->selectedMessageGroup',
              selectedMessageGroupIndex,
              this.memberDataGroupWise,
              this.memberDataGroupWise[selectedMessageGroupIndex]
            );
            this.memberDataGroupWise[selectedMessageGroupIndex].userList =
              responseData;
          }
          this.membersLoaded = true;
        }
      });
  }
  showData(data) {
    $('#notFoundRoles').hide();
    this.dummyCheckedGroupMembers = [];
    this.optionShow = data;
    this.searchTexts = '';
    this.chatWithTenantFilter.selectedTenant = '';
    this.srch.nativeElement.value = '';
    this.searchText = this.searchTexts = '';
    if (data == 'groups' || data == 'msggroups') {
      this.reset(this.optionShow);
    }
    if (this.optionShow == 'staffs' || this.optionShow == 'partner') {
      this.chatWithTenantFilter.enabledReset = false;
      this.setChatWithTenantFilterSelect();
      this.initialiseStaffOrPatientList(this.optionShow);
    }
    if (this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
      this.roleBasedStaffs = [];
      this.selectedroleBasedStaffs = [];
      this.chatWithLoader.otherTenantstaff = true;
      console.log(this.chatWithTenantFilter.filterEnabled);
      this.setChatWithTenantFilterSelect();
      console.log(this.chatWithTenantFilter.filterEnabled);
      this.getStaffRoles(this.optionShow);
    }

    //this.setCheckBoxListner();
  }

  assignChatWithTenantFilterData() {
    this.chatWithTenantFilter.filterEnabled = true;

    if ($('#messageTenantFilter').length) {
      setTimeout(() => {
        $('#messageTenantFilter').select2({
          dropdownParent: $('#staffListblock'),
        });
        $('#messageTenantFilter').css('text-overflow', 'ellipsis');
        if (
          this.optionShow == 'staffroles' ||
          this.optionShow == 'partnerroles'
        ) {
          //  $("#messageTenantFilter").val(this.chatWithTenantFilter.selectedTenant).trigger("change");
        }
      });
    } else {
      this.chatWithTenantFilter.setTenantDropDown = true;
    }
  }
  setChatWithTenantFilterSelect() {
    if (this.selectedTenant) {
      this.chatWithTenantFilter.selectedTenant = this.selectedTenant.id;
    } else {
      this.chatWithTenantFilter.selectedTenant =
        this._structureService.getCookie('tenantId');
    }
    switch (this.optionShow) {
      case 'staffs': {
        if (
          (this.privileges.indexOf(
            'allowMultipleOrganizationsStaffCommunication'
          ) !== -1 &&
            this.configData.allow_multiple_organization == 1 &&
            this.userData.crossTenantsDetails.length > 1 &&
            this.userData.masterEnabled == '0' &&
            this.configData.enable_nursing_agencies_visibility_restrictions !=
              1) ||
          (this.userData.masterEnabled == '1' &&
            this.userData.isMaster == '1') ||
          (this.userData.masterEnabled == '1' &&
            this.userData.isMaster == '0' &&
            this.userData.group != '3') ||
          (this.privileges.indexOf(
            'allowMultipleOrganizationsStaffCommunication'
          ) !== -1 &&
            this.configData.allow_multiple_organization == 1 &&
            this.userData.crossTenantsDetails.length > 1 &&
            this.userData.masterEnabled == '0' &&
            this.configData.enable_nursing_agencies_visibility_restrictions ==
              '1' &&
            this.userData.nursing_agencies == '')
        ) {
          this.assignChatWithTenantFilterData();
        } else {
          this.chatWithTenantFilter.filterEnabled = false;
          this.chatWithTenantFilter.tenants = [];
        }
        break;
      }
      case 'partner': {
        if (
          (this.privileges.indexOf(
            'allowMultipleOrganizationsStaffCommunication'
          ) !== -1 &&
            this.configData.allow_multiple_organization == 1 &&
            this.userData.crossTenantsDetails.length > 1 &&
            this.userData.masterEnabled == '0' &&
            this.configData.enable_nursing_agencies_visibility_restrictions !=
              1) ||
          (this.userData.masterEnabled == '1' &&
            this.userData.isMaster == '1') ||
          (this.userData.masterEnabled == '1' &&
            this.userData.isMaster == '0' &&
            this.userData.group != '3') ||
          (this.privileges.indexOf(
            'allowMultipleOrganizationsStaffCommunication'
          ) !== -1 &&
            this.configData.allow_multiple_organization == 1 &&
            this.userData.crossTenantsDetails.length > 1 &&
            this.userData.masterEnabled == '0' &&
            this.configData.enable_nursing_agencies_visibility_restrictions ==
              '1' &&
            this.userData.nursing_agencies == '')
        ) {
          this.assignChatWithTenantFilterData();
        } else {
          this.chatWithTenantFilter.filterEnabled = false;
          this.chatWithTenantFilter.tenants = [];
        }
        break;
      }
      case 'staffroles': {
        if (
          (this.privileges.indexOf(
            'allowMultipleOrganizationsStaffCommunication'
          ) !== -1 &&
            this.configData.allow_multiple_organization == 1 &&
            this.userData.crossTenantsDetails.length > 1 &&
            this.userData.masterEnabled == '0' &&
            this.configData.enable_nursing_agencies_visibility_restrictions !=
              1) ||
          (this.userData.masterEnabled == '1' &&
            this.userData.isMaster == '1') ||
          (this.userData.masterEnabled == '1' &&
            this.userData.isMaster == '0' &&
            this.userData.group != '3') ||
          (this.privileges.indexOf(
            'allowMultipleOrganizationsStaffCommunication'
          ) !== -1 &&
            this.configData.allow_multiple_organization == 1 &&
            this.userData.crossTenantsDetails.length > 1 &&
            this.userData.masterEnabled == '0' &&
            this.configData.enable_nursing_agencies_visibility_restrictions ==
              '1' &&
            this.userData.nursing_agencies == '')
        ) {
          this.assignChatWithTenantFilterData();
        } else {
          this.chatWithTenantFilter.filterEnabled = false;
          this.chatWithTenantFilter.tenants = [];
        }
        break;
      }
      case 'partnerroles': {
        if (
          (this.privileges.indexOf(
            'allowMultipleOrganizationsStaffCommunication'
          ) !== -1 &&
            this.configData.allow_multiple_organization == 1 &&
            this.userData.crossTenantsDetails.length > 1 &&
            this.userData.masterEnabled == '0' &&
            this.configData.enable_nursing_agencies_visibility_restrictions !=
              1) ||
          (this.userData.masterEnabled == '1' &&
            this.userData.isMaster == '1') ||
          (this.userData.masterEnabled == '1' &&
            this.userData.isMaster == '0' &&
            this.userData.group != '3') ||
          (this.privileges.indexOf(
            'allowMultipleOrganizationsStaffCommunication'
          ) !== -1 &&
            this.configData.allow_multiple_organization == 1 &&
            this.userData.crossTenantsDetails.length > 1 &&
            this.userData.masterEnabled == '0' &&
            this.configData.enable_nursing_agencies_visibility_restrictions ==
              '1' &&
            this.userData.nursing_agencies == '')
        ) {
          this.assignChatWithTenantFilterData();
        } else {
          this.chatWithTenantFilter.filterEnabled = false;
          this.chatWithTenantFilter.tenants = [];
        }
        break;
      }
      default: {
        this.chatWithTenantFilter.filterEnabled = false;
        this.chatWithTenantFilter.tenants = [];
        break;
      }
    }
  }
  removeDuplicates(arr, prop) {
    let obj = {};
    console.log('arrarrarrarrarrarrarr', arr);
    return Object.keys(
      arr.reduce((prev, next) => {
        // console.log("objobjobjobjobjobjobjobjobjobj",obj,!obj,prev,next,prop,!obj[next],!obj[next[prop]]);
        if (next && next[prop]) {
          if (!obj[next[prop]]) {
            obj[next[prop]] = next;
          }
          return obj;
        }
      }, obj)
    ).map((i) => obj[i]);
  }

  setmessageGroup(grpdata) {
    this.messageGroups = grpdata;
    this.newMessageGroups = grpdata;
    let allUserDetailsInMsgGroup: any = grpdata;
    // allUserDetailsInMsgGroup.map((val) => this.allUserDetailsInMsgGroup = this.allUserDetailsInMsgGroup.concat(val.memberDetails));
    // this.allUserDetailsInMsgGroup = this.removeDuplicates(this.allUserDetailsInMsgGroup, 'id');
    this.newallUserDetailsInMsgGroup = this.allUserDetailsInMsgGroup;
    let messageGroup: any;
    messageGroup = grpdata;
    //this.memberDataGroupWise = [];
    /*if (messageGroup.length) {
      messageGroup.forEach(value => {
        this.memberDataGroupWise[value.id] = {}
        this.memberDataGroupWise[value.id]['groupData'] = { id: value.id, name: value.name };
        this.memberDataGroupWise[value.id]['userList'] = value.memberDetails;
        this.memberDataGroupWise[value.id]['userList'] = this.memberDataGroupWise[value.id]['userList'].filter(x => !this.selectedGroupMembers.some(y => y.id == x.id));
      });
      this.memberDataGroupWise = this.memberDataGroupWise.filter(item => item.userList.length != 0);
      this.memberDataGroupWise.sort(function (a, b) {
        if (a.groupData.name.toLowerCase() < b.groupData.name.toLowerCase()) return -1;
        if (a.groupData.name.toLowerCase() > b.groupData.name.toLowerCase()) return 1;
        return 0;
      });
    }*/
    this.memberDataGroupWise = messageGroup;
    console.log('-->memberDataGroupWise-->', this.memberDataGroupWise);
    this.memberDataGroupWise.sort(function (a, b) {
      if (a.name < b.name) return -1;
      if (a.name > b.name) return 1;
      return 0;
    });
    console.log(
      'this.memberDataGroupWise 22222@@@@@@@@@@@@@@@@@@@->',
      this.memberDataGroupWise
    );
  }

  initGroups() {
    this.offset = 0;
    this.allMessageGroups = [];
    this.messageGroups = [];
    this.memberDataGroupWise = [];
    this.hideLoadMore = false;
    this.prevText = '';
  }
  getRoleBasedStaff(roleId, eve, populate = false) {
    let chosenSiteId;
    this.usersInRole = true;
    eve = this.newMemberListByRoleWise.findIndex(
      (member) => member.roleData.id === roleId
    );
    console.log(eve);
    var status = 0;
    var newstaff = [];
    if (!('userList' in this.newMemberListByRoleWise[eve])) {
      this.newMemberListByRoleWise[eve]['userList'] = [];
    }
    chosenSiteId = this.multiTenant ? this.selectedBranchId : this.selectSiteId;

    this._structureService
      .getRoleBasedStaffs(
        roleId,
        status,
        1,
        false,
        this.chatWithTenantFilter.selectedTenant,
        chosenSiteId
      )
      .then((data) => {
        let parsedResponceData = JSON.parse(JSON.stringify(data));
        this.roleBasedStaffs =
          parsedResponceData.getSessionTenant['roleBasedStaffs'];
        if (this.roleBasedStaffs == null) {
          this.roleBasedStaffs = [];
          this.usersInRole = false;
        }
        if (
          this.configData.enable_nursing_agencies_visibility_restrictions ==
            '1' &&
          this.userData.nursing_agencies != '' &&
          this.configData.na_staffs_chat_with_pharmacy == '0'
        ) {
          var nur_login = this.userData.nursing_agencies.split(',');

          nur_login.forEach((element) => {
            this.roleBasedStaffs = this.roleBasedStaffs.filter(function (
              members
            ) {
              if (
                members.naTags != null &&
                members.naTags != '' &&
                members.naTags.split(',').indexOf(element) != -1
              ) {
                return true;
              }
            });
          });
          if (this.roleBasedStaffs.length == 0) {
            this.usersInRole = false;
          }
        } else if (
          this.configData.enable_nursing_agencies_visibility_restrictions ==
            '1' &&
          this.userData.nursing_agencies != '' &&
          this.configData.na_staffs_chat_with_pharmacy == '1'
        ) {
          var nur_login = this.userData.nursing_agencies.split(',');
          nur_login.forEach((element) => {
            this.roleBasedStaffs = this.roleBasedStaffs.filter(function (
              members
            ) {
              if (
                members.naTags == null ||
                members.naTags == '' ||
                members.naTags.split(',').indexOf(element) != -1
              ) {
                return true;
              } else {
                return false;
              }
            });
          });
        }

        if (this.newMemberListByRoleWise[eve]['userList'].length == 0) {
          this.roleBasedStaffs.forEach((value) => {
            value.selectedRoleid = roleId;
            var user = {
              id: value.id,
              name: value.displayName,
              naTags: value.naTags,
              naTagNames: value.naTagNames,
            };
            if (this.checkedIds.indexOf(value.id) === -1)
              this.checkedIds.push(value.id);
            this.newMemberListByRoleWise[eve]['userList'].push(user);
          });
        }
        if (!populate) {
          console.log(this.selectedroleBasedStaffs);
          console.log(this.roleBasedStaffs);
          this.selectedroleBasedStaffs = [
            ...this.selectedroleBasedStaffs,
            ...this.roleBasedStaffs,
          ];
        }
        this.membersLoaded = true;
        console.log(this.newMemberListByRoleWise);
      });
  }
  searchOnEnter(event, optionShow) {
    if (
      optionShow == 'staffs' ||
      optionShow == 'groups' ||
      optionShow == 'msggroups' ||
      optionShow == 'partner'
    ) {
      if (event.keyCode == 13) {
        event.preventDefault();

        if (this.srch.nativeElement.value.trim()) {
          this.search(optionShow);
        } else {
          this.reset(optionShow);
        }
      }
    }
  }
  reset(optionShow = 'groups') {
    this.srch.nativeElement.value = '';
    if (optionShow == 'groups' || optionShow == 'msggroups') {
      this.initGroups();
      this.searchTexts = '';
      this.srch.nativeElement.value = '';
      this.getAllMessageGroupDetails(
        this.userData.tenantId,
        this.userData.userId,
        '',
        optionShow
      );
    } else if (optionShow == 'staffs' || optionShow == 'partner') {
      this.loadMoreSearchValue = undefined;
      this.callFromInitialCountChatWithUserlist = true;
      this.chatwithPageCount = {
        staffs: 0,
        partner: 0,
        patients: 0,
      };
      this.noMoreItemsAvailable = {
        users: false,
      };
      this.userListChatwith = [];
      this.clinicalUserDetails = [];
      this.usersList = [];
      this.loadMoreUsers(optionShow);
    }
  }

  search(optionShow = 'groups') {
    if (optionShow == 'groups' || optionShow == 'msggroups') {
      this.initGroups();
      if (
        this.srch.nativeElement.value.trim() ||
        !isBlank(this.selectSiteId) ||
        this.multiTenant
      ) {
        this.getAllMessageGroupDetails(
          this.userData.tenantId,
          this.userData.userId,
          this.srch.nativeElement.value.trim(),
          optionShow
        );
      }
    }
    if (this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
      this.getStaffRoles(this.optionShow);
    } else if (optionShow == 'staffs' || optionShow == 'partner') {
      this.callFromInitialCountChatWithUserlist = true;
      this.chatwithPageCount = {
        staffs: 0,
        partner: 0,
        patients: 0,
      };
      this.noMoreItemsAvailable = {
        users: false,
      };
      this.userListChatwith = [];
      this.clinicalUserDetails = [];
      this.usersList = [];
      this.loadMoreUsers(optionShow, this.srch.nativeElement.value.trim());
    }
  }
  loadMoreUsers(optionShow, searchKeyword = undefined, notInit = false) {
    if (!this.loadMoreSearchValue) {
      if (this.srch.nativeElement.value.trim()) {
        this.loadMoreSearchValue = undefined;
        this.callFromInitialCountChatWithUserlist = true;
        this.chatwithPageCount = {
          staffs: 0,
          partner: 0,
          patients: 0,
        };
        this.noMoreItemsAvailable = {
          users: false,
        };
        notInit = false;
        this.userListChatwith = [];
        this.clinicalUserDetails = [];
        this.usersList = [];
        searchKeyword = this.srch.nativeElement.value.trim();
      }
    } else if (
      this.loadMoreSearchValue &&
      !this.srch.nativeElement.value.trim()
    ) {
      this.reset(optionShow);
      return false;
    } else if (
      this.loadMoreSearchValue == this.srch.nativeElement.value.trim()
    ) {
    } else {
      this.loadMoreSearchValue = undefined;
      this.callFromInitialCountChatWithUserlist = true;
      this.chatwithPageCount = {
        staffs: 0,
        partner: 0,
        patients: 0,
      };
      this.noMoreItemsAvailable = {
        users: false,
      };
      notInit = false;
      this.userListChatwith = [];
      this.clinicalUserDetails = [];
      this.usersList = [];
      searchKeyword = this.srch.nativeElement.value.trim();
    }
    var isRoleAvailable = true;
    var clinicianRolesAvaiable = null;
    var setCliniciansRoleAvailableResponse;
    if (isRoleAvailable) {
      this.getUsersListByRoleIdAvailableOnClick(
        clinicianRolesAvaiable,
        !notInit ? true : false,
        !notInit ? false : true,
        optionShow,
        searchKeyword
      );
    } else {
      this.chatWithUsersLoading = false;
    }
  }
  getUsersListByRoleIdAvailableOnClick(
    clinicianRolesAvaiable,
    init,
    loadMore,
    optionShow,
    searchValue
  ) {
    let chatWithFilterTenantId = null;
    this.UsergroupIds = clinicianRolesAvaiable;
    let chosenSiteId;
    if (init) {
      if (optionShow == 'staffs') {
        this.chatWithLoader.staffs = true;
        this.UsergroupIds = 3;
      }
      if (optionShow == 'partner') {
        this.chatWithLoader.partner = true;
        this.UsergroupIds = 20;
      }
    } else {
      if (optionShow == 'staffs') {
        this.UsergroupIds = 3;
      }
      if (optionShow == 'partner') {
        this.UsergroupIds = 20;
      }
    }
    if (optionShow == 'staffs' || optionShow == 'partner') {
      chatWithFilterTenantId = this.chatWithTenantFilter.selectedTenant;
    }
    this.loadMoremessage.users = 'Loading ....';
    console.log('hiiiiiiiiiiiii');
    chosenSiteId = this.multiTenant ? this.selectedBranchId : this.selectSiteId;
    this._inboxService
      .getUsersListByRoleId(
        this.UsergroupIds ? this.UsergroupIds : 3,
        0,
        null,
        null,
        init
          ? 0
          : optionShow == 'staffs'
          ? this.chatwithPageCount.staffs
          : optionShow == 'partner'
          ? this.chatwithPageCount.partner
          : this.chatwithPageCount.patients,
        optionShow == 'staffs' && !clinicianRolesAvaiable
          ? true
          : optionShow == 'staffs' && clinicianRolesAvaiable
          ? undefined
          : false,
        searchValue,
        chatWithFilterTenantId,
        undefined,
        '',
        optionShow,
        chosenSiteId
      )
      .then((data: any) => {
        this.preventMultipleCall = true;
        console.log('hiiiiiiiiiiiii');
        console.log('data', data);

        this.chatWithUsersLoading = false;
        this.loadMoreSearchValue = searchValue;
        if (loadMore) {
          this.clinicalUserDetails = [...this.clinicalUserDetails, ...data];
        } else {
          this.clinicalUserDetails = data;
        }

        if (data.length != 20) this.noMoreItemsAvailable.users = true;

        if (optionShow == 'staffs') {
          this.chatwithPageCount.staffs += 1;
        }
        if (optionShow == 'partner') {
          this.chatwithPageCount.partner += 1;
        }
        console.log(this.clinicalUserDetails);
        for (var _i = 0; _i < this.clinicalUserDetails.length; _i++) {
          this.clinicalUserDetails[_i].isScheduled = 1;
          if (
            this._inboxService.scheduleSelectionFilter(
              this.clinicalUserDetails[_i]
            )
          ) {
            this.clinicalUserDetails[_i].isScheduled = 1;
          } else {
            this.clinicalUserDetails[_i].isScheduled = 0;
          }
        }
        console.log(this.clinicalUserDetails);
        this.loadMoremessage.users = 'Load more';

        this.userListChatwith = this.clinicalUserDetails;

        this.userListChatwith = this.userListChatwith.filter(
          (x) => !this.selectedGroupMembers.some((y) => y.id == x.userid)
        );

        if (init) {
          if (optionShow == 'staffs') {
            this.chatWithLoader.staffs = false;
          }
          if (optionShow == 'partner') {
            this.chatWithLoader.partner = false;
          }
        }
      })
      .catch((ex) => {
        if (init) {
          if (optionShow == 'staffs') {
            this.chatWithLoader.staffs = false;
          }
          if (optionShow == 'partner') {
            this.chatWithLoader.partner = false;
          }
        }
      });
    console.log(this.clinicalUserDetails);
    console.log(this.userListChatwith);
  }
  loadMoreGroups(optionShow = '') {
    if (!this.prevText) {
      if (this.srch.nativeElement.value.trim()) {
        console.log('Initialise search...');
        this.search();
      } else {
        console.log('Continue with pagination');
        this.offset = this.offset + 25;
        this.getAllMessageGroupDetails(
          this.userData.tenantId,
          this.userData.userId,
          this.srch.nativeElement.value.trim(),
          optionShow
        );
      }
    } else if (this.prevText && !this.srch.nativeElement.value.trim()) {
      console.log('Reset called');
      this.reset(optionShow);
    } else if (this.prevText == this.srch.nativeElement.value.trim()) {
      console.log('Continue pagination with search');
      this.offset = this.offset + 25;
      this.getAllMessageGroupDetails(
        this.userData.tenantId,
        this.userData.userId,
        this.srch.nativeElement.value.trim(),
        optionShow
      );
    } else {
      console.log('Initialise search...');
      this.search();
    }
  }

  getAllMessageGroupDetails(
    tenantId,
    userId,
    searchKeyword = '',
    optionShow = ''
  ) {
    let selectedSiteId = this.selectSiteId;
    if (optionShow == 'groups') {
      this.ispdgs = '1';
    } else if (optionShow == 'msggroups') {
      if (this.userData.config.enable_multisite == 1) {
        selectedSiteId = '0';
      }
      this.ispdgs = '0';
    }
    this.loadingGroups = true;
    this.MessageGroupWithLoader.groups = true;

    this._inboxService
      .getAllMessageGroupNames(
        tenantId,
        userId,
        true,
        '',
        this.offset,
        this.limit,
        searchKeyword,
        [],
        false,
        '',
        this.ispdgs,
        selectedSiteId
      )
      .then((data: any) => {
        if (searchKeyword) {
          this.prevText = searchKeyword;
        }
        this.loadingGroups = false;
        if (!data || (data && data.length == 0)) {
          this.hideLoadMore = true;
        } else {
          if (data.length == 25) {
            this.hideLoadMore = false;
          } else {
            this.hideLoadMore = true;
          }
          this.allMessageGroups = [...this.allMessageGroups, ...data];
          this.setmessageGroup(this.allMessageGroups);
          this._structureService.messageGroupsWithPrivilege =
            this.allMessageGroups;
        }

        this.MessageGroupWithLoader.groups = false;
      })
      .catch((ex) => {
        this.MessageGroupWithLoader.groups = false;
      });
  }

  getGroups(refresh) {
    console.log('hhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhhh');
    /* if (this._structureService.messageGroupsWithPrivilege && this._structureService.messageGroupsWithPrivilege.length) {
      console.log("Enter first ifffffffff");
      console.log(this._structureService.messageGroupsWithPrivilege);
      this.setmessageGroup(this._structureService.messageGroupsWithPrivilege); */
    //} else {
    //this.getAllMessageGroupDetails(this.userData.tenantId, this.userData.userId);
    //}

    /* this._structureService.getPatientDiscussionGroups(this.searchInboxkeyword,11).then((data) => {
      if (data['getSessionTenant']) {
        //this.groupList = data['getSessionTenant'].messageGroupsPagination.data;

       // this.groupListNames = this.groupList;
        this.staffList = data['getSessionTenant'].staffUsers;
        this.otherTenantStaffList = data['getSessionTenant'].otherTenantStaffUsers;
        this.newMemberListByRoleWise = [];
        this.newMemberOtherTenantListByRoleWise = [];
        this.newMemberList = [];
        this.newMemberOtherTenantList = [];
        this.staffList.forEach(value => {
          if (value.role && value.role.id) {
            this.newMember = {
              id: "",
              displayName: "",
              role: {},
              tenantId: null,
              tenantName: "",
              otherTenantStaff: ((value.role.tenantId && (this.userData.tenantId != value.role.tenantId)) ? true : false),
              naTags: "",
              naTagNames: ""
            }
            this.newMember.id = value.id;
            this.newMember.displayName = value.displayName;
            this.newMember.role = value.role;
            this.newMember.dualRoles = value.dualRoles;
            this.newMember.tenantId = value.role.tenantId;
            this.newMember.tenantName = value.role.tenantName;
            if(value.naTags && value.naTags != null && value.naTags != 'null') {
              this.newMember.naTags = value.naTags;
              this.newMember.naTagNames = value.naTagNames;
            }
            this.newMemberList.push(this.newMember);
            this.memberList.push(this.newMember);
            var user = { id: value.id, name: value.displayName, naTags: "", naTagNames: "" };

            if(value.naTags && value.naTags != null && value.naTags != 'null') {
              user.naTags = value.naTags;
              user.naTagNames = value.naTagNames;
            }

            var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id, tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles};
            
            if (!this.newMemberListByRoleWise[value.role.id]) {
              this.newMemberListByRoleWise[value.role.id] = {};
            }

            if (!('userList' in this.newMemberListByRoleWise[value.role.id])) {
              this.newMemberListByRoleWise[value.role.id]['userList'] = [];
            }
            if (!('roleData' in this.newMemberListByRoleWise[value.role.id])) {
              this.newMemberListByRoleWise[value.role.id]['roleData'] = {};
            }

            if(value.dualRoles && value.dualRoles.length>1){
              value.dualRoles.forEach(dualEach => {
                if(value.role.id !=dualEach.tenantUsersRoleId){
                  if (!this.newMemberListByRoleWise[dualEach.tenantUsersRoleId]) {
                    this.newMemberListByRoleWise[dualEach.tenantUsersRoleId] = {};                    
                  }
                  if (!('userList' in this.newMemberListByRoleWise[dualEach.tenantUsersRoleId])) {
                    this.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'] = [];
                  }
                  if(this.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'].findIndex(x => x.id == user.id) == -1) {
                    this.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'].push(user);
                  }
                }
              })
            }

            this.newMemberListByRoleWise[value.role.id]['roleData'] = roleData;
            this.newMemberListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
            this.newMemberListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;

            if(this.newMemberListByRoleWise[value.role.id]['userList'].findIndex(x => x.id == user.id) == -1) {
              this.newMemberListByRoleWise[value.role.id]['userList'].push(user);
            }           
          }
        });

        this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter( function(a) {
          return (a && a.tenantId);
        });

        this.newMemberListByRoleWise.sort(function (a, b) {
          if (a.roleData.name < b.roleData.name) return -1;
          if (a.roleData.name > b.roleData.name) return 1;
          return 0;
        });

        this.otherTenantStaffList.forEach(value => {
          if (value.role && value.role.id) {
            if(this.enableCommunicationWithInternalstaffs == true) {
              if(this.internalSiteId == value.role.tenantId) {
                this.newMember = {
                  id: "",
                  displayName: "",
                  role: {},
                  tenantId: null,
                  tenantName: "",
                  otherTenantUser: ((value.role.tenantId && (this.userData.tenantId != value.role.tenantId)) ? true : false)
                }
                this.newMember.id = value.id;
                this.newMember.displayName = value.displayName;
                this.newMember.role = value.role;
                this.newMember.dualRoles =value.dualRoles ;
                this.newMember.tenantId = value.role.tenantId;
                this.newMember.tenantName = value.role.tenantName;
                if(value.naTags && value.naTags != null && value.naTags != 'null') {
                  this.newMember.naTags = value.naTags;
                  this.newMember.naTagNames = value.naTagNames;
                }
                this.newMemberOtherTenantList.push(this.newMember);
                this.otherTenantMemberList.push(this.newMember);
                var user = { id: value.id, name: value.displayName, naTags: "", naTagNames: "" };

                if(value.naTags && value.naTags != null && value.naTags != 'null') {
                  user.naTags = value.naTags;
                  user.naTagNames = value.naTagNames;
                }

                var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id , tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles};
                if (!this.newMemberOtherTenantListByRoleWise[value.role.id]) {
                  this.newMemberOtherTenantListByRoleWise[value.role.id] = {};
                }
                if (!('userList' in this.newMemberOtherTenantListByRoleWise[value.role.id])) {
                  this.newMemberOtherTenantListByRoleWise[value.role.id]['userList'] = [];
                }
                if (!('roleData' in this.newMemberOtherTenantListByRoleWise[value.role.id])) {
                  this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = {};
                }
                if (!('tenantId' in this.newMemberOtherTenantListByRoleWise[value.role.id])) {
                  this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] = null;
                }
                if(value.dualRoles && value.dualRoles.length>1){
                  value.dualRoles.forEach(dualEach => {
                    if(value.role.id !=dualEach.tenantUsersRoleId){
                      if (!this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]) {
                        this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId] = {};                    
                      }
                      if (!('userList' in this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId])) {
                        this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]['userList'] = [];
                      }
                      this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]['userList'].push(user);
                    }
                  })
                }
                this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = roleData;
                this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
                this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;

                this.newMemberOtherTenantListByRoleWise[value.role.id]['userList'].push(user);             
              }
            } else {
              this.newMember = {
                id: "",
                displayName: "",
                role: {},
                tenantId: null,
                tenantName: "",
                otherTenantUser: ((value.role.tenantId && (this.userData.tenantId != value.role.tenantId)) ? true : false)
              }
              this.newMember.id = value.id;
              this.newMember.displayName = value.displayName;
              this.newMember.role = value.role;
              this.newMember.dualRoles = value.dualRoles 
              this.newMember.tenantId = value.role.tenantId;
              this.newMember.tenantName = value.role.tenantName;
              this.newMemberOtherTenantList.push(this.newMember);
              this.otherTenantMemberList.push(this.newMember);
              var user = { id: value.id, name: value.displayName, naTags: "", naTagNames: "" };

              if(value.naTags && value.naTags != null && value.naTags != 'null') {
                user.naTags = value.naTags;
                user.naTagNames = value.naTagNames;
              }

              var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id, tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles};
              if (!this.newMemberOtherTenantListByRoleWise[value.role.id]) {
                this.newMemberOtherTenantListByRoleWise[value.role.id] = {};
              }
              if (!('userList' in this.newMemberOtherTenantListByRoleWise[value.role.id])) {
                this.newMemberOtherTenantListByRoleWise[value.role.id]['userList'] = [];
              }
              if (!('roleData' in this.newMemberOtherTenantListByRoleWise[value.role.id])) {
                this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = {};
              }
              if (!('tenantId' in this.newMemberOtherTenantListByRoleWise[value.role.id])) {
                this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] = null;
              }
              this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = roleData;
              this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
              this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;

              this.newMemberOtherTenantListByRoleWise[value.role.id]['userList'].push(user);
             
            }            
          }
        });

        this.newMemberOtherTenantListByRoleWise = this.newMemberOtherTenantListByRoleWise.filter( function(a) {
          return (a && a.tenantId);
        });

        this.newMemberOtherTenantListByRoleWise.sort(function (a, b) {
          if (a.roleData.name < b.roleData.name) return -1;
          if (a.roleData.name > b.roleData.name) return 1;
          return 0;
        });
      } else {
        this._structureService.deleteCookie('authenticationToken');
        this.router.navigate(['/login']);
      }
    }); */
  }

  noWhitespaceValidator(control: FormControl) {
    let isWhitespace = (control.value || '').trim().length === 0;
    let isValid = !isWhitespace;
    return isValid ? null : { whitespace: true };
  }

  refreshPage() {
    this.getGroups(true);
  }

  selectGroup(group) {
    this.selected = group;
  }

  clearPatientGroupSelection() {
    this.checkedIds = [];
    //this.checkedRoleIds=[];
    $('input[type="checkbox"]')
      .prop({
        indeterminate: false,
        checked: false,
      })
      .siblings('label')
      .removeClass('custom-checked custom-unchecked custom-indeterminate')
      .addClass('custom-unchecked');
  }
  patientGroupSelectionAll() {
    $('input[type="checkbox"]')
      .prop({
        indeterminate: true,
        checked: true,
      })
      .siblings('label')
      .removeClass('custom-checked custom-unchecked custom-indeterminate')
      .addClass('custom-checked');
  }
  removeMember(id) {
    /*    swal({
      title: "Are you sure?",
      text: "You are removing member from this patient discussion group",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Ok",
      closeOnConfirm: true
    }, () => { */

    var self = this;

    this.selectedGroupMembers = this.selectedGroupMembers.filter(function (
      members
    ) {
      if (members.id != id) {
        return true;
      } else {
        self.selectedGroupMembers = self.selectedGroupMembers.filter(function (
          users
        ) {
          return users.id != members.id;
        });
        self.selectedGroupMemberIds = self.selectedGroupMemberIds.filter(
          function (id) {
            return id != members.id;
          }
        );
        let messagegropmemberfromalllist = null;
        messagegropmemberfromalllist = self.allUserDetailsInMsgGroup.find(
          (user) => user.id == members.id
        );
        if (messagegropmemberfromalllist) {
          self.newallUserDetailsInMsgGroup.push(messagegropmemberfromalllist);
        }
        // self.memberDataGroupWise = [];
        console.log('self.checkedRoleIds==============');
        console.log('self.checkedRoleIds==============', self.checkedRoleIds);
        console.log('self.checkedRoleIds==============');
        console.log('self.checkedRoleIds==============');
        if (self.checkedRoleIds.length > 0) {
          console.log(self.clinicalUserDetails);

          var user = self.clinicalUserDetails.find(
            (user) => user.userid == members.id
          );
          // console.log(user.userid +"==" +members.id);
          if (user && user.userid) {
            self.userListChatwith.push(user);
            self.userListChatwith = self.userListChatwith.slice();
          }
          self.userListChatwith.sort(function (a, b) {
            if (a.displayname.toLowerCase() < b.displayname.toLowerCase())
              return -1;
            if (a.displayname.toLowerCase() > b.displayname.toLowerCase())
              return 1;
            return 0;
          });
          console.log(self.userListChatwith);

          if (members.dualRoles && typeof members.dualRoles == 'string') {
            members.dualRoles = JSON.parse(members.dualRoles);
          }
          console.log('self.checkedRoleIds.length=======');
          self.checkedRoleIds.forEach((value) => {
            if (
              members.role &&
              members.role.id &&
              value.id == members.role.id
            ) {
              self.removedRoleUsers.push({ id: id, roleId: value.id });
            } else if (members.dualRoles && members.dualRoles.length > 1) {
              members.dualRoles.forEach((dual) => {
                if (dual.tenantUsersRoleId == value.id) {
                  self.removedRoleUsers.push({ id: id, roleId: value.id });
                }
              });
            }
          });
          console.log(' self.removedRoleUsers=======> ', self.removedRoleUsers);
        }
        if (self.checkedIdsWithRole && self.checkedIdsWithRole[id]) {
          delete self.checkedIdsWithRole[id];
        }
        /*if (self.newMessageGroups.length) {
            self.newMessageGroups.forEach(value => {
              self.memberDataGroupWise[value.id] = {}
              self.memberDataGroupWise[value.id]['groupData'] = { id: value.id, name: value.name };
              self.memberDataGroupWise[value.id]['userList'] = value.memberDetails.filter(member => self.selectedGroupMemberIds.indexOf(member.id) == -1);
            });
            self.memberDataGroupWise = self.memberDataGroupWise.filter(item => item.userList.length != 0);
            self.memberDataGroupWise.sort(function (a, b) {
              if (a.groupData.name < b.groupData.name) return -1;
              if (a.groupData.name > b.groupData.name) return 1;
              return 0;
            });
          }*/
        if (
          typeof members == 'object' &&
          members.hasOwnProperty('otherTenantUser') &&
          !members.otherTenantUser
        ) {
          var user = self.memberList.find((user) => user.id == members.id);
          if (user.id) {
            self.newMemberList.push(user);
            self.newMemberList = self.newMemberList.slice();
          }
          self.newMemberListByRoleWise = [];
          self.newMemberList.forEach((value) => {
            if (value.role && value.role.id) {
              var user = { id: value.id, name: value.displayName };
              var roleData = {
                id: value.role.id,
                name: value.role.displayName,
                tenantRoleId: value.role.id,
                tenantId: value.role.tenantId,
                tenantName: value.role.tenantName,
                dualRoles: value.dualRoles,
              };
              if (!self.newMemberListByRoleWise[value.role.id]) {
                self.newMemberListByRoleWise[value.role.id] = {};
              }
              if (
                !('userList' in self.newMemberListByRoleWise[value.role.id])
              ) {
                self.newMemberListByRoleWise[value.role.id]['userList'] = [];
              }
              if (
                !('roleData' in self.newMemberListByRoleWise[value.role.id])
              ) {
                self.newMemberListByRoleWise[value.role.id]['roleData'] = {};
              }
              self.newMemberListByRoleWise[value.role.id]['roleData'] =
                roleData;
              self.newMemberListByRoleWise[value.role.id]['tenantId'] =
                value.role.tenantId;
              self.newMemberListByRoleWise[value.role.id]['tenantName'] =
                value.role.tenantName;
              self.newMemberListByRoleWise[value.role.id]['userList'].push(
                user
              );
            }
          });
          self.newMemberListByRoleWise = self.newMemberListByRoleWise.filter(
            function (a) {
              return a && a.tenantId;
            }
          );
          self.newMemberListByRoleWise.sort(function (a, b) {
            if (a.roleData.name < b.roleData.name) return -1;
            if (a.roleData.name > b.roleData.name) return 1;
            return 0;
          });
        } else if (
          typeof members == 'object' &&
          members.hasOwnProperty('otherTenantUser') &&
          members.otherTenantUser
        ) {
          var user = self.clinicalUserDetails.find(
            (user) => user.userid == members.id
          );
          console.log(user);
          /*  if (user.userid) {
              self.userListChatwith.push(user);
              self.userListChatwith = self.userListChatwith.slice();
            }    */
          self.userListChatwith.sort(function (a, b) {
            if (a.displayname.toLowerCase() < b.displayname.toLowerCase())
              return -1;
            if (a.displayname.toLowerCase() > b.displayname.toLowerCase())
              return 1;
            return 0;
          });
          console.log(self.userListChatwith);
        } else {
          console.log(self.clinicalUserDetails);
          console.log(members);
          let setCurrentTenant = false;
          let setOtherTenant = false;
          if (members.tenantId == self.selectedTenant) {
            setCurrentTenant = true;
            var user = self.clinicalUserDetails.find(
              (user) => user.userid == members.id
            );
            if (user.id) {
              self.newMemberList.push(user);
              self.newMemberList = self.newMemberList.slice();
            }
          } /* else {
              setOtherTenant = true;
              var user = self.clinicalUserDetails.find((user) => user.userid == members.id);
              if (user.id) {
                self.newMemberList.push(user);
                self.newMemberList = self.newMemberList.slice();
              }
            } */
          if (setCurrentTenant) {
            // self.newMemberListByRoleWise = [];
            self.newMemberList.forEach((value) => {
              if (value.role && value.role.id) {
                var user = { id: value.id, name: value.displayName };
                var roleData = {
                  id: value.role.id,
                  name: value.role.displayName,
                  tenantRoleId: value.role.id,
                  tenantId: value.role.tenantId,
                  tenantName: value.role.tenantName,
                  dualRoles: value.dualRoles,
                };
                if (!self.newMemberListByRoleWise[value.role.id]) {
                  self.newMemberListByRoleWise[value.role.id] = {};
                }
                if (
                  !('userList' in self.newMemberListByRoleWise[value.role.id])
                ) {
                  self.newMemberListByRoleWise[value.role.id]['userList'] = [];
                }
                if (
                  !('roleData' in self.newMemberListByRoleWise[value.role.id])
                ) {
                  self.newMemberListByRoleWise[value.role.id]['roleData'] = {};
                }
                self.newMemberListByRoleWise[value.role.id]['roleData'] =
                  roleData;
                self.newMemberListByRoleWise[value.role.id]['tenantId'] =
                  value.role.tenantId;
                self.newMemberListByRoleWise[value.role.id]['tenantName'] =
                  value.role.tenantName;
                self.newMemberListByRoleWise[value.role.id]['userList'].push(
                  user
                );
              }
            });
            self.newMemberListByRoleWise = self.newMemberListByRoleWise.filter(
              function (a) {
                return a && a.tenantId;
              }
            );
            self.newMemberListByRoleWise.sort(function (a, b) {
              if (a.roleData.name < b.roleData.name) return -1;
              if (a.roleData.name > b.roleData.name) return 1;
              return 0;
            });
          }
          if (setOtherTenant) {
            self.newMemberOtherTenantListByRoleWise = [];
            self.newMemberOtherTenantList.forEach((value) => {
              if (value.role && value.role.id) {
                var user = { id: value.id, name: value.displayName };
                var roleData = {
                  id: value.role.id,
                  name: value.role.displayName,
                  tenantRoleId: value.role.id,
                  tenantId: value.role.tenantId,
                  tenantName: value.role.tenantName,
                  dualRoles: value.dualRoles,
                };
                if (!self.newMemberOtherTenantListByRoleWise[value.role.id]) {
                  self.newMemberOtherTenantListByRoleWise[value.role.id] = {};
                }
                if (
                  !(
                    'userList' in
                    self.newMemberOtherTenantListByRoleWise[value.role.id]
                  )
                ) {
                  self.newMemberOtherTenantListByRoleWise[value.role.id][
                    'userList'
                  ] = [];
                }
                if (
                  !(
                    'roleData' in
                    self.newMemberOtherTenantListByRoleWise[value.role.id]
                  )
                ) {
                  self.newMemberOtherTenantListByRoleWise[value.role.id][
                    'roleData'
                  ] = {};
                }
                self.newMemberOtherTenantListByRoleWise[value.role.id][
                  'roleData'
                ] = roleData;
                self.newMemberOtherTenantListByRoleWise[value.role.id][
                  'userList'
                ].push(user);
                self.newMemberOtherTenantListByRoleWise[value.role.id][
                  'tenantId'
                ] = value.role.tenantId;
                self.newMemberOtherTenantListByRoleWise[value.role.id][
                  'tenantName'
                ] = value.role.tenantName;
              }
            });

            self.newMemberOtherTenantListByRoleWise =
              self.newMemberOtherTenantListByRoleWise.filter(function (a) {
                return a && a.tenantId;
              });
            self.newMemberOtherTenantListByRoleWise.sort(function (a, b) {
              if (a.roleData.name < b.roleData.name) return -1;
              if (a.roleData.name > b.roleData.name) return 1;
              return 0;
            });
          }
          // self.memberDataGroupWise = [];
          if (self.newallUserDetailsInMsgGroup.length) {
            self.memberDataGroupWise = [];
            self.newMessageGroups.forEach((value) => {
              self.memberDataGroupWise[value.id] = {};
              self.memberDataGroupWise[value.id]['groupData'] = {
                id: value.id,
                name: value.name,
              };
              self.memberDataGroupWise[value.id]['userList'] = [];
              self.newallUserDetailsInMsgGroup.forEach((user, key) => {
                if (user.id != self.userData.userId) {
                  if (value.memberIds.split(',').indexOf(user.id + '') > -1) {
                    var role = {
                      id: user.id,
                      name: user.name,
                      tenantId: user.tenantId,
                      tenantName: user.tenantName,
                    };
                    var foundIds = self.memberDataGroupWise[value.id][
                      'userList'
                    ].filter((x) => x.id.indexOf(user.id) !== -1);
                    if (foundIds.length == 0)
                      self.memberDataGroupWise[value.id]['userList'].push(role);
                    self.memberDataGroupWise[value.id]['userList'] =
                      self.memberDataGroupWise[value.id]['userList'].filter(
                        (x) =>
                          !self.selectedGroupMembers.some((y) => y.id == x.id)
                      );
                  }
                }
              });
            });
            self.memberDataGroupWise = self.memberDataGroupWise.filter(
              (item) => item.userList.length != 0
            );
            self.memberDataGroupWise.sort(function (a, b) {
              if (a.groupData.name < b.groupData.name) return -1;
              if (a.groupData.name > b.groupData.name) return 1;
              return 0;
            });
          }
        }
        return false;
      }
    });
    var isPublic = this.selectedisPublic ? 'Public' : ' Not public';
    var allowMultiThreadChat = this.selectedAllowMultiThread
      ? 'multi thread chat is allowed'
      : 'multi thread chat is not allowed';
    var activityLogMessage =
      this.userData.displayName +
      ' Removed a user ( ' +
      id +
      ' ) from Group(' +
      this.messageGroup.controls['messageGroupName'].value +
      ' ' +
      this.messageGroup.controls['messageGroupLastName'].value +
      ' DOB ' +
      this.messageGroup.controls['messageGroupDob'].value +
      '),branch is ' +
      this.messageGroup.controls['messageGroupBranch'].value +
      ' and this group is ' +
      isPublic +
      ' and ' +
      allowMultiThreadChat +
      ' for this group';
    var activityData = {
      activityName: 'Removed a user from Discussion Group',
      activityType: 'manage group messaging',
      activityDescription: activityLogMessage,
    };
    console.log('activityDate :::', activityData);
    this._structureService.trackActivity(activityData);
    // console.log(self.newGroupMemberList);
    if (this.selectedGroupMembers.length == 0) {
      this.disableButton = true;
      this.noMemberError = true;
    } else {
      this.disableButton = false;
    }
    // });
    /*if (this.optionShow == 'groups' || this.optionShow == 'msggroups') {
      this.reset(this.optionShow);
    }*/
  }

  addMember() {
    var self = this;
    this.addMemberError = false;
    $(':checkbox:checked').each(function (i) {
      var name = this.name;
      console.log('checked name===>' + name);
      if (name == 'cliniciansRoleUser[]') {
        //remove duplicate.
        console.log('addMember= This=====>');

        self.checkedIds.push($(this).val());
        if (
          $(this)[0].attributes['data-roleset'] &&
          $(this)[0].attributes['data-roleset'].value
        ) {
          console.log($(this)[0].attributes['data-roleset'].value);
          self.checkedIdsWithRole[$(this).val()] =
            $(this)[0].attributes['data-roleset'].value;
        }
      } else if (name == 'staffRoleWise') {
        var selectedRole = JSON.parse($(this).val());
        console.log(this.checkedIds);
        if (self.dummyCheckedRoleIds.indexOf(selectedRole.id) > -1) {
          var exist = false;
          if (self.checkedRoleIds.length) {
            self.checkedRoleIds.forEach((value) => {
              if (value.id == selectedRole.id) {
                exist = true;
              }
            });
          }
          if (!exist) {
            self.checkedRoleIds.push(selectedRole);
            self.selectedGroupMembers = self.selectedGroupMembers.filter((member)=>{
              if(member.tenantRoleId !== selectedRole.id) {
                return member;
              }
            });
          }
        }
      } else if (name == 'messageGroup') {
        var selectedMessageGroup = JSON.parse($(this).val());
        console.log(
          ' addMember- messageGroup - >> ',
          selectedMessageGroup,
          self.dummyCheckedGroupMembers,
          self.selectedGroupMembers
        );
        if (self.dummyCheckedGroupMembers.indexOf(selectedMessageGroup) > -1) {
          var exist = false;
          if (self.checkedIds.length) {
            self.checkedIds.forEach((value) => {
              if (value.id == selectedMessageGroup) {
                exist = true;
              }
            });
          }
          if (!exist) {
            self.checkedIds.push(selectedMessageGroup);
          }
        }
      }
    });

    if (this.checkedIds.length > 0) {
      this.addMemberToList(this.checkedIds);
      /* if(this.optionShow=='groups' || this.optionShow=='msggroups') {
      var liLength = $('#treeview li').length;
      this.offset = 25-liLength;
      this.getAllMessageGroupDetails(this.userData.tenantId, this.userData.userId,'',this.optionShow);
      this.srch.nativeElement.value = "";
    }*/
      this.noMemberError = false;
      this.checkedIds = [];
      this.addMemberError = false;
    } else if (
      this.checkedRoleIds.length > 0 &&
      this.optionShow != 'groups' &&
      this.optionShow != 'msggroups'
    ) {
      this.addMemberToList(this.checkedRoleIds);
    } else if (this.optionShow == 'groups' || this.optionShow == 'msggroups') {
      console.log('-> after add button', this.dummyCheckedGroupMembers);
      this.clearPatientGroupSelection();
      if (
        this.dummyCheckedGroupMembers &&
        this.dummyCheckedGroupMembers.length > 0
      ) {
        self.getMessageGroupMembersByGroupId(this.dummyCheckedGroupMembers);
        console.log('->this.selectedGroupMembers', this.selectedGroupMembers);
        var liLength = $('#treeview li').length;
        this.offset = 25 - liLength;
        this.srch.nativeElement.value = '';
        console.log('>>selectedGroupMembers', this.dummyCheckedGroupMembers);
      } else {
        this.addMemberError = true;
      }

      //...............
    }
  }
  addMemberToList(checkedIds) {
    if (this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
      console.log(' this.checkedRoleIds', this.roleBasedStaffs);

      this.staffList.forEach((element) => {
        this.checkedRoleIds.forEach((rolevalue, key) => {
          if (rolevalue.id == element.id) {
            console.log(this.selectedroleBasedStaffs);
            this.selectedroleBasedStaffs.forEach((users) => {
              console.log(this.selectedGroupMembers);

              if (
                this.selectedGroupMembers.findIndex(
                  (x) => x.id === users.id
                ) === -1
              ) {
                //
                this.newMember = {
                  id: '',
                  displayName: '',
                  role: {},
                  tenantId: null,
                  tenantName: '',
                };
                this.newMember.role = element.id;
                this.newMember.tenantId = parseInt(element.tenantId);
                this.newMember.tenantName = element.tenantName;
                this.newMember.id = users.id;
                this.newMember.displayName = users.displayName;
                this.newMember.siteId = element.siteId;
                this.newMember.siteName = element.siteName;
                this.newMember.naTags = users.naTags;
                this.newMember.naTagNames = users.naTagNames;
                var user = {
                  id: this.newMember.id,
                  name: this.newMember.displayName,
                  role: this.newMember.role,
                };
                this.testArr.push(user);
                console.log('x.id ==users.id', users.id);
                this.selectedGroupMembers.push(this.newMember);
                this.selectedGroupMemberIds.push(this.newMember.id);
              }

              this.selectedGroupMembers = this.selectedGroupMembers.slice();
              this.selectedGroupMemberIds = this.selectedGroupMemberIds.slice();
            });

            console.log(
              'newMemberListByRoleWise========before filter========>'
            );
            console.log(this.newMemberListByRoleWise);

            this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter(
              function (members) {
                return members.roleData.id != element.id;
              }
            );
            console.log('newMemberListByRoleWise========after filter========>');
            console.log(this.newMemberListByRoleWise);
            //Delete a user if the user is added to GP members when user role is checked
            if (this.removedRoleUsers.length > 0) {
              this.removedRoleUsers.forEach((value, key) => {
                if (value.id == element.id && value.roleId == element.role.id) {
                  this.removedRoleUsers.splice(key, 1);
                }
              });
            }
          }
        });
        //Delete user from removedRoleUsers if its role is unchecked
        if (
          this.removedRoleUsers.length > 0 &&
          this.removedRoleUsers.indexOf(element.id) > -1
        ) {
          var detect = false;
          this.checkedRoleIds.forEach((value) => {
            if (value.id == element.role.id) {
              detect = true;
            }
          });
          if (!detect) {
            this.removedRoleUsers.splice(
              this.removedRoleUsers.indexOf(element.id),
              1
            );
          }
        }
      });

      this.newMemberListByRoleWise.sort(function (a, b) {
        if (a.roleData.name < b.roleData.name) return -1;
        if (a.roleData.name > b.roleData.name) return 1;
        return 0;
      });
    } else if (this.optionShow == 'staffs' || this.optionShow == 'partner') {
      this.clinicalUserDetails.forEach((element) => {
        if (this.checkedIds.includes(element.userid)) {
          this.newMember = {
            id: '',
            displayName: '',
            role: {},
            tenantId: null,
            tenantName: '',
            otherTenantUser: true,
            naTags: '',
            naTagNames: '',
            tenantRoleId: ""
          };
          this.newMember.id = element.userid;
          this.newMember.displayName = element.displayname;
          this.newMember.role = element.role;
          this.newMember.dualRoles = element.dualRoles;
          this.newMember.tenantId = parseInt(element.tenantid);
          this.newMember.tenantName = element.tenantName;
          this.newMember.siteId = element.siteId;
          this.newMember.siteName = element.siteName;
          this.newMember.naTags = element.naTags;
          this.newMember.naTagNames = element.naTagNames;
          this.newMember.tenantRoleId = element.tenantRoleId;
          console.log(this.newMember);
          if (
            this.selectedGroupMembers.findIndex(
              (x) => x.id === element.userid
            ) === -1
          ) {
            this.selectedGroupMembers.push(this.newMember);
            this.selectedGroupMemberIds.push(this.newMember.id);
          }
          this.selectedGroupMembers = this.selectedGroupMembers.slice();
          console.log(this.selectedGroupMembers);
          this.selectedGroupMemberIds = this.selectedGroupMemberIds.slice();
          this.newallUserDetailsInMsgGroup =
            this.newallUserDetailsInMsgGroup.filter(function (members) {
              return members.id != element.userid;
            });
          this.userListChatwith = this.userListChatwith.filter(function (
            members
          ) {
            return members.userid != element.userid;
          });
        }
      });
      this.newMemberOtherTenantListByRoleWise = [];
      this.newMemberOtherTenantList.forEach((value) => {
        if (value.role && value.role.id) {
          var user = {
            id: value.id,
            name: value.displayName,
            naTags: '',
            naTagNames: '',
          };
          var roleData = {
            id: value.role.id,
            name: value.role.displayName,
            tenantRoleId: value.role.id,
            tenantId: value.role.tenantId,
            tenantName: value.role.tenantName,
            dualRoles: value.dualRoles,
          };
          if (!this.newMemberOtherTenantListByRoleWise[value.role.id]) {
            this.newMemberOtherTenantListByRoleWise[value.role.id] = {};
          }
          if (
            !(
              'userList' in
              this.newMemberOtherTenantListByRoleWise[value.role.id]
            )
          ) {
            this.newMemberOtherTenantListByRoleWise[value.role.id]['userList'] =
              [];
          }
          if (
            !(
              'roleData' in
              this.newMemberOtherTenantListByRoleWise[value.role.id]
            )
          ) {
            this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] =
              {};
          }

          this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] =
            roleData;
          this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] =
            value.role.tenantId;
          this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantName'] =
            value.role.tenantName;
          this.newMemberOtherTenantListByRoleWise[value.role.id][
            'userList'
          ].push(user);

          if (value.dualRoles && value.dualRoles.length > 1) {
            value.dualRoles.forEach((dualEach) => {
              if (value.role.id != dualEach.tenantUsersRoleId) {
                if (
                  !this.newMemberOtherTenantListByRoleWise[
                    dualEach.tenantUsersRoleId
                  ]
                ) {
                  this.newMemberOtherTenantListByRoleWise[
                    dualEach.tenantUsersRoleId
                  ] = {};
                }
                if (
                  !(
                    'userList' in
                    this.newMemberOtherTenantListByRoleWise[
                      dualEach.tenantUsersRoleId
                    ]
                  )
                ) {
                  this.newMemberOtherTenantListByRoleWise[
                    dualEach.tenantUsersRoleId
                  ]['userList'] = [];
                }
                this.newMemberOtherTenantListByRoleWise[
                  dualEach.tenantUsersRoleId
                ]['userList'].push(user);
              }
            });
          }
        }
      });

      this.newMemberOtherTenantListByRoleWise =
        this.newMemberOtherTenantListByRoleWise.filter(function (a) {
          return a && a.tenantId;
        });

      this.newMemberOtherTenantListByRoleWise.sort(function (a, b) {
        if (a.roleData.name < b.roleData.name) return -1;
        if (a.roleData.name > b.roleData.name) return 1;
        return 0;
      });
      this.memberDataGroupWise.map((group) => {
        group.userList = group.userList.filter(
          (user) => this.checkedIds.indexOf(user.id) == -1
        );
      });
      this.memberDataGroupWise = this.memberDataGroupWise.filter(
        (item) => item.userList.length != 0
      );
    } else {
      let setCurrentTenant = false;
      let setOtherTenant = false;
      this.allUserDetailsInMsgGroup.forEach((value) => {
        if (this.checkedIds.includes(value.id)) {
          this.newMember = {
            id: '',
            displayName: '',
            role: {},
            tenantId: null,
            tenantName: '',
            naTags: '',
            naTagNames: '',
          };
          this.newMember.id = value.id;
          this.newMember.displayName = value.name;
          this.newMember.role = value.role ? value.role : [];
          this.newMember.tenantId = parseInt(value.tenantId);
          this.newMember.tenantName = value.tenantName;

          if (value.naTags && value.naTags != null && value.naTags != 'null') {
            this.newMember.naTags = value.naTags;
            this.newMember.naTagNames = value.naTagNames;
          }

          if (
            this.selectedGroupMembers.findIndex((x) => x.id === value.id) === -1
          ) {
            this.selectedGroupMembers.push(this.newMember);
            this.selectedGroupMemberIds.push(this.newMember.id);
          }
          this.selectedGroupMembers = this.selectedGroupMembers.slice();
          this.selectedGroupMemberIds = this.selectedGroupMemberIds.slice();
          this.newallUserDetailsInMsgGroup =
            this.newallUserDetailsInMsgGroup.filter(function (members) {
              return members.id != value.id;
            });
          if (value.tenantId == this.userData.tenantId) {
            setCurrentTenant = true;
            this.newMemberList = this.newMemberList.filter(function (members) {
              return members.id != value.id;
            });
          } else {
            setOtherTenant = true;
            this.newMemberOtherTenantList =
              this.newMemberOtherTenantList.filter(function (members) {
                return members.id != value.id;
              });
          }
        }
      });
      if (setCurrentTenant) {
        this.newMemberListByRoleWise = [];
        this.newMemberList.forEach((value) => {
          if (value.role && value.role.id) {
            var user = {
              id: value.id,
              name: value.displayName,
              naTags: value.naTags,
              naTagNames: value.naTagNames,
            };
            var roleData = {
              id: value.role.id,
              name: value.role.displayName,
              tenantRoleId: value.role.id,
              tenantId: value.role.tenantId,
              tenantName: value.role.tenantName,
              dualRoles: value.dualRoles,
            };
            if (!this.newMemberListByRoleWise[value.role.id]) {
              this.newMemberListByRoleWise[value.role.id] = {};
            }
            if (!('userList' in this.newMemberListByRoleWise[value.role.id])) {
              this.newMemberListByRoleWise[value.role.id]['userList'] = [];
            }
            if (!('roleData' in this.newMemberListByRoleWise[value.role.id])) {
              this.newMemberListByRoleWise[value.role.id]['roleData'] = {};
            }

            if (value.dualRoles && value.dualRoles.length > 1) {
              value.dualRoles.forEach((dualEach) => {
                if (value.role.id != dualEach.tenantUsersRoleId) {
                  if (
                    !this.newMemberListByRoleWise[dualEach.tenantUsersRoleId]
                  ) {
                    this.newMemberListByRoleWise[dualEach.tenantUsersRoleId] =
                      {};
                  }
                  if (
                    !(
                      'userList' in
                      this.newMemberListByRoleWise[dualEach.tenantUsersRoleId]
                    )
                  ) {
                    this.newMemberListByRoleWise[dualEach.tenantUsersRoleId][
                      'userList'
                    ] = [];
                  }
                  if (
                    this.newMemberListByRoleWise[dualEach.tenantUsersRoleId][
                      'userList'
                    ].findIndex((x) => x.id == user.id) == -1
                  ) {
                    this.newMemberListByRoleWise[dualEach.tenantUsersRoleId][
                      'userList'
                    ].push(user);
                  }
                }
              });
            }

            this.newMemberListByRoleWise[value.role.id]['roleData'] = roleData;
            this.newMemberListByRoleWise[value.role.id]['tenantId'] =
              value.role.tenantId;
            this.newMemberListByRoleWise[value.role.id]['tenantName'] =
              value.role.tenantName;
            if (
              this.newMemberListByRoleWise[value.role.id]['userList'].findIndex(
                (x) => x.id == user.id
              ) == -1
            ) {
              this.newMemberListByRoleWise[value.role.id]['userList'].push(
                user
              );
            }
          }
        });
        this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter(
          function (a) {
            return a && a.tenantId;
          }
        );
        this.newMemberListByRoleWise.sort(function (a, b) {
          if (a.roleData.name < b.roleData.name) return -1;
          if (a.roleData.name > b.roleData.name) return 1;
          return 0;
        });
      }
      if (setOtherTenant) {
        this.newMemberOtherTenantListByRoleWise = [];
        this.newMemberOtherTenantList.forEach((value) => {
          if (value.role && value.role.id) {
            var user = {
              id: value.id,
              name: value.displayName,
              naTags: '',
              naTagNames: '',
            };

            if (
              value.naTags &&
              value.naTags != null &&
              value.naTags != 'null'
            ) {
              user.naTags = value.naTags;
              user.naTagNames = value.naTagNames;
            }

            var roleData = {
              id: value.role.id,
              name: value.role.displayName,
              tenantRoleId: value.role.id,
              tenantId: value.role.tenantId,
              tenantName: value.role.tenantName,
              dualRoles: value.dualRoles,
            };
            if (!this.newMemberOtherTenantListByRoleWise[value.role.id]) {
              this.newMemberOtherTenantListByRoleWise[value.role.id] = {};
            }
            if (
              !(
                'userList' in
                this.newMemberOtherTenantListByRoleWise[value.role.id]
              )
            ) {
              this.newMemberOtherTenantListByRoleWise[value.role.id][
                'userList'
              ] = [];
            }
            if (
              !(
                'roleData' in
                this.newMemberOtherTenantListByRoleWise[value.role.id]
              )
            ) {
              this.newMemberOtherTenantListByRoleWise[value.role.id][
                'roleData'
              ] = {};
            }

            if (value.dualRoles && value.dualRoles.length > 1) {
              value.dualRoles.forEach((dualEach) => {
                if (value.role.id != dualEach.tenantUsersRoleId) {
                  if (
                    !this.newMemberOtherTenantListByRoleWise[
                      dualEach.tenantUsersRoleId
                    ]
                  ) {
                    this.newMemberOtherTenantListByRoleWise[
                      dualEach.tenantUsersRoleId
                    ] = {};
                  }
                  if (
                    !(
                      'userList' in
                      this.newMemberOtherTenantListByRoleWise[
                        dualEach.tenantUsersRoleId
                      ]
                    )
                  ) {
                    this.newMemberOtherTenantListByRoleWise[
                      dualEach.tenantUsersRoleId
                    ]['userList'] = [];
                  }
                  this.newMemberOtherTenantListByRoleWise[
                    dualEach.tenantUsersRoleId
                  ]['userList'].push(user);
                }
              });
            }

            this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] =
              roleData;
            this.newMemberOtherTenantListByRoleWise[value.role.id][
              'userList'
            ].push(user);
            this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] =
              value.role.tenantId;
            this.newMemberOtherTenantListByRoleWise[value.role.id][
              'tenantName'
            ] = value.role.tenantName;
          }
        });

        this.newMemberOtherTenantListByRoleWise =
          this.newMemberOtherTenantListByRoleWise.filter(function (a) {
            return a && a.tenantId;
          });
        this.newMemberOtherTenantListByRoleWise.sort(function (a, b) {
          if (a.roleData.name < b.roleData.name) return -1;
          if (a.roleData.name > b.roleData.name) return 1;
          return 0;
        });
      }

      /* this.memberDataGroupWise = [];
      if (this.newallUserDetailsInMsgGroup.length) {
        this.memberDataGroupWise = [];
        this.newMessageGroups.forEach(value => {
          this.memberDataGroupWise[value.id] = {}
          this.memberDataGroupWise[value.id]['groupData'] = { id: value.id, name: value.name };
          this.memberDataGroupWise[value.id]['userList'] = [];
          this.newallUserDetailsInMsgGroup.forEach((user, key) => {
            if (user.id != this.userData.userId) {
              if (value.memberIds.split(',').indexOf(user.id + '') > -1) {
                var role = { id: user.id, name: user.name, tenantId: user.tenantId, tenantName: user.tenantName, natags: user.naTags, naTagNames: user.naTagNames };
                
                this.memberDataGroupWise[value.id]['userList'].push(role);
                
              }
            }            
          });
        });
        this.memberDataGroupWise = this.memberDataGroupWise.filter(function (item) {
          if (item.userList.length != 0) {
            return true;
          } else {
            return false;
          }

        });*/
      // }
      this.memberDataGroupWise.sort(function (a, b) {
        if (a.name < b.name) return -1;
        if (a.name > b.name) return 1;
        return 0;
      });
    }
    this.selectedGroupMembers = this.removeDuplicates(
      this.selectedGroupMembers,
      'id'
    );
    // this.memberDataGroupWise = this.memberDataGroupWise.filter(item => item.userList.length > 0);

    var isPublic = this.selectedisPublic ? 'Public' : ' Not public';
    var allowMultiThreadChat = this.selectedAllowMultiThread
      ? 'multi thread chat is allowed'
      : 'multi thread chat is not allowed';
    var activityLogMessage =
      this.userData.displayName +
      ' Added users ( ' +
      JSON.stringify(this.checkedIds) +
      ' ) to Group(' +
      this.messageGroup.controls['messageGroupName'].value +
      ' ' +
      this.messageGroup.controls['messageGroupLastName'].value +
      ' DOB ' +
      this.messageGroup.controls['messageGroupDob'].value +
      '),branch is ' +
      this.messageGroup.controls['messageGroupBranch'].value +
      ' and this group is ' +
      isPublic +
      ' and ' +
      allowMultiThreadChat +
      ' for this group';

    var activityData = {
      activityName: 'Add Users to Discussion Group',
      activityType: 'manage group messaging',
      activityDescription: activityLogMessage,
    };
    console.log('activityDate :::', activityData);
    this._structureService.trackActivity(activityData);
    if (this.selectedGroupMembers.length == 0) {
      this.disableButton = true;
      this.noMemberError = true;
    } else {
      this.disableButton = false;
      this.noMemberError = false;
    }
  }

  updateMessageGroup(id, f, patientId = null) {
    // console.log(this.messageGroup.controls['messageGroupBranch'].value);
    const patientUniqueId = this.messageGroup.controls['patientUniqueId'].value.trim();
    const tagMRN = (!isBlank(patientUniqueId))? ' [MRN: '+patientUniqueId+']': '';
    let dob = this.messageGroup.controls['messageGroupDob'].value
      .trim()
      .replace(/-/g, '/');
    this.messageGroup.patchValue({ messageGroupDob: dob });
    this.disableButton = true;
    if (this.selectedGroupMembers.length > 0) {
      this.noMemberError = false;
      /*} else {
      this.noMemberError = false;
    }*/
      console.log('ffffffffffffffffffffffffffffffffffff', f);
      if (!f.valid || this.noMemberError) {
        if (!f.valid) {
          $('input.ng-invalid')[0].focus();
        } else if (this.noMemberError) {
          $('html, body').animate({
            scrollTop: $('.card.existing .card-block').offset().top,
          });
        }
        return false;
      }
      let selectedRoleIds = [];
      console.log('this.checkedRoleIds=============>');
      console.log('this.checkedRoleIds=============>', this.checkedRoleIds);
      if (this.checkedRoleIds.length) {
        this.checkedRoleIds.forEach((value, key) => {
          selectedRoleIds.push(value.id.toString());
        });
      }
      console.log('selectedRoleIds===============>');
      console.log('selectedRoleIds===============>', selectedRoleIds);
      console.log(
        'this.checkedIdsWithRole=========================================='
      );
      console.log(this.checkedIdsWithRole);
      var selectedGroupMembers = this.selectedGroupMembers.map((member) => {
        return {
          id: member.id.toString(),
          displayName: member.displayName,
          tenantId: parseInt(member.tenantId),
          tenantName: member.tenantName,
        };
      });
      /* var selectedGroupMembers = this.selectedGroupMembers.filter((item)=>{
      console.log("item=========",item);
      if(selectedRoleIds.indexOf(item.role.id)>-1){
        console.log("Enter indexof option..........")
        return false;
      }else if(item.dualRoles && item.dualRoles.length>1){
        console.log("Dual Role option............");
        console.log("this.checkedIdsWithRole[item.id]====>"+this.checkedIdsWithRole[item.id]);
        var roleDetect = false;
        item.dualRoles.forEach((value)=>{
          console.log("dualRole valueeeeee Each....")
          if(!value.isPrimary && selectedRoleIds.indexOf(value.tenantUsersRoleId)>-1 && parseInt(this.checkedIdsWithRole[item.id])==value.tenantUsersRoleId){
            console.log("fetch role......")
            roleDetect =true;
          }
        })
        console.log("roleDetect===> "+roleDetect);
        if(roleDetect){
          return false;
        }
      }
      return true;
    }).map((member) => {
          return { id: member.id, displayName: member.displayName, tenantId: member.tenantId, tenantName: member.tenantName };
    });
*/
      console.log('Filter selectedGroupMembers===============>');
      console.log(selectedGroupMembers);
      // this.selectedGroupMembers = this.selectedGroupMembers.map((member) => {
      //   if(selectedRoleIds.indexOf(member.role.id)==-1){
      //     return { id: member.id, displayName: member.displayName, tenantId: member.tenantId, tenantName: member.tenantName };
      //   }
      // })

      console.log('this.selectedGroupMembers===============>');
      console.log(
        'this.selectedGroupMembers===============>',
        this.selectedGroupMembers
      );
      console.log('this.removedRoleUsers================>');
      console.log(this.removedRoleUsers);
      //if (this.selectedGroupMembers.length > 0) {
      // return true;

      console.log(id + ' != false');
      if (!id) {
        const formObjData = this.messageGroup.value;
        console.log(
          '--------------------formObjData---------------',
          formObjData
        );
        formObjData.createdBy = this.userData.userId;
        // TODO: esi_code_for_patient_identity decommisioned, remove usage if it is not causing any issue
        formObjData.esiPatient =
          this.userData.config.esi_code_for_patient_identity;
        var newName =
          this.messageGroup.controls['messageGroupName'].value.trim() +
          ' ' +
          this.messageGroup.controls['messageGroupLastName'].value.trim() +
          ' DOB ' +
          this.messageGroup.controls['messageGroupDob'].value.trim() +
          tagMRN;
        var index = this.groupListNames.findIndex(
          (x) =>
            x.name.replace(/\s/g, '').toLowerCase() ==
            newName.replace(/\s/g, '').toLowerCase()
        );
        if (index === -1) {
          var inputdata;
          /* if (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') != 'undefined' && this._structureService.getCookie('tenantId') !== this._structureService.getCookie('crossTenantId')) {
            inputdata = { "firstname": this.messageGroup.controls['messageGroupName'].value, "lastname": this.messageGroup.controls['messageGroupLastName'].value, "dob": this.messageGroup.controls['messageGroupDob'].value, "tenantId": this._structureService.getCookie('crossTenantId'), "operation": "checking" };
          } else {
            inputdata = { "firstname": this.messageGroup.controls['messageGroupName'].value, "lastname": this.messageGroup.controls['messageGroupLastName'].value, "dob": this.messageGroup.controls['messageGroupDob'].value, "tenantId": this.userData.tenantId, "operation": "checking" };
          }*/
          var bRanchh = '';
          if (this.messageGroup.controls['messageGroupBranch'].value == '0') {
            bRanchh = this.userData.crossTenantId;
          } else {
            bRanchh = this.messageGroup.controls['messageGroupBranch'].value;
          }
          inputdata = {
            firstname:
              this.messageGroup.controls['messageGroupName'].value.trim(),
            lastname:
              this.messageGroup.controls['messageGroupLastName'].value.trim(),
            dob: this.messageGroup.controls['messageGroupDob'].value.trim(),
            operation: 'checking',
          };
          //If MRN is provided, do MRN check
					if(!isBlank(patientUniqueId)){
						inputdata.MRNumber = patientUniqueId,
						inputdata.operation = 'mrnChecking';
					}
          this.registrationservice
            .signupCareGiver(inputdata, true)
            .then((data) => {
              var responceData: any = data;
              var vitualPatient = false;
              console.log('this.blockConfirm', this.blockConfirm);
              if (this.blockConfirm == false) {
                var patient_associated_id =
                  patientId != null ? patientId : responceData.data.userid;
                var confirmMesssage =
                  this.messageGroup.controls['messageGroupName'].value.trim() +
                  ' ' +
                  this.messageGroup.controls[
                    'messageGroupLastName'
                  ].value.trim() +
                  ' DOB(' +
                  this.messageGroup.controls['messageGroupDob'].value.trim() +
                  ') already exists.';
                vitualPatient = false;
                var self = this;
                self.newMember = {
                  id: '',
                  displayName: '',
                };
                self.newMember.id = patient_associated_id.toString();
                self.newMember.displayName =
                  self.messageGroup.controls['messageGroupName'].value.trim() +
                  ' ' +
                  self.messageGroup.controls[
                    'messageGroupLastName'
                  ].value.trim();
                selectedGroupMembers.push(self.newMember);
                self.updateParams = {
                  name:
                    self.messageGroup.controls[
                      'messageGroupName'
                    ].value.trim() +
                    ' ' +
                    self.messageGroup.controls[
                      'messageGroupLastName'
                    ].value.trim() +
                    ' DOB ' +
                    self.messageGroup.controls['messageGroupDob'].value.trim() +
                    tagMRN,
                  isPublic: self.selectedisPublic,
                  allowMultiThreadChat: self.selectedAllowMultiThread,
                  type: 'Patient_Discussion_Group',
                  id: id,
                  branch:
                    self.messageGroup.controls[
                      'messageGroupBranch'
                    ].value.trim(),
                  patientUniqueId:
                    self.messageGroup.controls['patientUniqueId'].value.trim(),
                  groupMembers: selectedGroupMembers,
                  roles: selectedRoleIds,
                  removedMembers: self.removedRoleUsers,
                };
                self.createMessageGroup();
              } else if(responceData.userExist && inputdata.operation === 'mrnChecking'){
                this._structureService.notifyMessage({
                  messge: this._ToolTipService.getTranslateDataWithParam('MESSAGES.PDG_PATIENT_ALREADY_EXIST',
                  {idValue: 'MRN',patientUniqueId:patientUniqueId}),
                  delay: 1000,
                  type: 'danger'
                });	
              } else {
                if (responceData.userExist === true) {
                  console.log(responceData);
                  var patient_associated_id = responceData.data.userid;
                  var confirmMesssage =
                    this.messageGroup.controls[
                      'messageGroupName'
                    ].value.trim() +
                    ' ' +
                    this.messageGroup.controls[
                      'messageGroupLastName'
                    ].value.trim() +
                    ' DOB(' +
                    this.messageGroup.controls['messageGroupDob'].value.trim() +
                    ') already exists.';
                  vitualPatient = false;
                  var self = this;
                  swal(
                    {
                      title: confirmMesssage,
                      text: 'Do you want to  continue with this patient?',
                      type: 'warning',
                      showCancelButton: true,
                      cancelButtonClass: 'btn-default',
                      confirmButtonClass: 'btn-warning',
                      confirmButtonText: 'Ok',
                      closeOnConfirm: true,
                    },
                    function (isConfirm) {
                      if (isConfirm) {
                        self.newMember = {
                          id: '',
                          displayName: '',
                        };
                        self.newMember.id = patient_associated_id.toString();
                        self.newMember.displayName =
                          self.messageGroup.controls[
                            'messageGroupName'
                          ].value.trim() +
                          ' ' +
                          self.messageGroup.controls[
                            'messageGroupLastName'
                          ].value.trim();
                        selectedGroupMembers.push(self.newMember);
                        self.updateParams = {
                          name:
                            self.messageGroup.controls[
                              'messageGroupName'
                            ].value.trim() +
                            ' ' +
                            self.messageGroup.controls[
                              'messageGroupLastName'
                            ].value.trim() +
                            ' DOB ' +
                            self.messageGroup.controls[
                              'messageGroupDob'
                            ].value.trim() +
                            tagMRN,
                          isPublic: self.selectedisPublic,
                          allowMultiThreadChat: self.selectedAllowMultiThread,
                          type: 'Patient_Discussion_Group',
                          id: id,
                          branch:
                            self.messageGroup.controls[
                              'messageGroupBranch'
                            ].value.trim(),
                          patientUniqueId:
                            self.messageGroup.controls[
                              'patientUniqueId'
                            ].value.trim(),
                          groupMembers: selectedGroupMembers,
                          roles: selectedRoleIds,
                          removedMembers: self.removedRoleUsers,
                        };
                        self.flagPatient = true;
                        self.patientAssoicatedId = patient_associated_id;
                        self.createMessageGroup();
                      } else {
                        //swal("Cancelled", " :(", "error");
                        NProgress.done();
                      }
                    }
                  );
                } else {
                  var virtualPatientData;
                  virtualPatientData = {
                    email: '',
                    firstname:
                      this.messageGroup.controls[
                        'messageGroupName'
                      ].value.trim(),
                    lastname:
                      this.messageGroup.controls[
                        'messageGroupLastName'
                      ].value.trim(),
                    dob: moment(
                      this.messageGroup.controls['messageGroupDob'].value.trim()
                    ).format('MMM D,YYYY'),
                    countryCode: '',
                    cell: '',
                    zipcode: '',
                    mrn: this.messageGroup.controls[
                      'patientUniqueId'
                    ].value.trim(),
                    externalSystemId:
                      this.userData.config.esi_code_for_patient_identity,
                    companyNursingAgency: '',
                    tenantRoleId: '',
                    source: '',
                    userType: 'patient',
                    tags: [],
                    mobileVerified: 0,
                    emailVerified: 0,
                    virtualPatientId: '',
                    userCategoryId: '',
                    siteIds: (this.selectSiteId) ? this.selectSiteId :'0' ,
                    isFromPDG: true,
                  };

                  /** 

                  createVirtualUser() returns a promise 

                  based on the passed in tag name 

                  @param <object> virtualPatientData 

                  @return <object> response 

              */
                  this._structureService
                    .createVirtualUser(virtualPatientData)
                    .then((response: any) => {
                      let result = response;
                      if (result.error !== 'Invalid Input') {
                        NProgress.start();
                        if (result.status == 1)
                          this.newMember = {
                            id: '',
                            displayName: '',
                          };
                        this.newMember.id =
                          result && result.userId
                            ? result.userId.toString()
                            : '';
                        this.newMember.displayName =
                          result && result.userId
                            ? virtualPatientData.firstname +
                              ' ' +
                              virtualPatientData.lastname
                            : '';
                        selectedGroupMembers.push(this.newMember);
                        this.updateParams = {
                          name:
                            this.messageGroup.controls[
                              'messageGroupName'
                            ].value.trim() +
                            ' ' +
                            this.messageGroup.controls[
                              'messageGroupLastName'
                            ].value.trim() +
                            ' DOB ' +
                            this.messageGroup.controls[
                              'messageGroupDob'
                            ].value.trim() +
                            tagMRN,
                          isPublic: this.selectedisPublic,
                          allowMultiThreadChat: this.selectedAllowMultiThread,
                          type: 'Patient_Discussion_Group',
                          id: id,
                          branch:
                            this.messageGroup.controls[
                              'messageGroupBranch'
                            ].value.trim(),
                          patientUniqueId:
                            this.messageGroup.controls[
                              'patientUniqueId'
                            ].value.trim(),
                          nursingAgencyUserTag:
                            this.messageGroup.controls['nursingAgencyUserTag']
                              .value,
                          groupMembers: selectedGroupMembers,
                          roles: selectedRoleIds,
                          removedMembers: this.removedRoleUsers,
                        };
                      } else {
                        let activityData = {
                          activityName: 'Error Create virtual Patient',
                          activityType: 'virtualPatient',
                          activityDescription: `Error occured due to failure of API:create user.`,
                        };
                        this._structureService.notifyMessage({
                          messge: this._ToolTipService.getTranslateData(
                            'MESSAGES.ERROR_MSG_INVALID_INPUT'
                          ),
                          delay: 1000,
                          type: 'warning',
                        });
                        NProgress.done();
                        this.disableButton = false;
                        this._structureService.trackActivity(activityData);
                      }
                      this.createMessageGroup();
                    });
                }
              }
            });
        } else {
          var index = selectedGroupMembers.indexOf(this.newMember);
          if (index > -1) {
            selectedGroupMembers.splice(index, 1);
          }
          NProgress.done();
          this.disableButton = false;
          var notify = $.notify(
            'Error! Patient Discussion Group already exists'
          );
          setTimeout(function () {
            notify.update({
              type: 'danger',
              message:
                '<strong>Error! Patient Discussion Group already exists</strong>',
            });
          }, 5000);
        }
      } else {
        if (this.selected.patientUser) {
          console.log(this.messageGroup.controls);
          var params = {
            displayName:
              this.messageGroup.controls['messageGroupName'].value.trim() +
              ' ' +
              this.messageGroup.controls['messageGroupLastName'].value.trim(),
            firstName:
              this.messageGroup.controls['messageGroupName'].value.trim(),
            lastName:
              this.messageGroup.controls['messageGroupLastName'].value.trim(),
            dateOfBirth:
              this.messageGroup.controls['messageGroupDob'].value.trim(),
            status: 'Active',
          };
          this._structureService.updateUser(
            parseInt(this.selected.patientUser.id),
            params,
            null,
            false,
            null,
            null,
            null,
            'pdg',
            true
          );
        }
        this.newMember = {
          id: '',
          displayName: '',
        };
        this.newMember.id = this.selected.patientUser.id.toString();
        //this.newMember.displayName=this.selected.patientUser.displayName;
        this.newMember.displayName =
          this.messageGroup.controls['messageGroupName'].value.trim() +
          ' ' +
          this.messageGroup.controls['messageGroupLastName'].value.trim();
        selectedGroupMembers.push(this.newMember);

        this.updateParams = {
          name:
            this.messageGroup.controls['messageGroupName'].value.trim() +
            ' ' +
            this.messageGroup.controls['messageGroupLastName'].value.trim() +
            ' DOB ' +
            this.messageGroup.controls['messageGroupDob'].value.trim() +
            tagMRN,
          isPublic: this.selectedisPublic,
          allowMultiThreadChat: this.selectedAllowMultiThread,
          type: 'Patient_Discussion_Group',
          id: id,
          branch: this.messageGroup.controls['messageGroupBranch'].value.trim(),
          patientUniqueId:
            this.messageGroup.controls['patientUniqueId'].value.trim(),
          groupMembers: selectedGroupMembers,
          roles: selectedRoleIds,
          removedMembers: this.removedRoleUsers,
        };

        var isPublic = this.selectedisPublic ? 'public' : 'not public';
        var allowMultiThreadChat = this.selectedAllowMultiThread
          ? 'multi thread chat is allowed'
          : 'multi thread chat is not allowed';
        var selectedGroupMembersNames = '';
        var comma = '';

        $.each(selectedGroupMembers, function (key, value) {
          //console.log('displayName: ' + value.displayName + ' | id: ' +value.id);
          selectedGroupMembersNames =
            selectedGroupMembersNames + comma + value.displayName;
          comma = ', ';
        });

        this._structureService
          .createMessageGroup(this.updateParams, true, this.selectSiteId)
          .then((response) => {
            let msgId: any = response;
            if (msgId.createMessageGroup.id != 0) {
              //this.selectedGroupMembers= [];
              this.getGroups(false);

              var activityLogMessage =
                this.userData.displayName +
                ' updated Discussion Group(' +
                this.messageGroup.controls['messageGroupName'].value.trim() +
                ' ' +
                this.messageGroup.controls[
                  'messageGroupLastName'
                ].value.trim() +
                ' DOB ' +
                this.messageGroup.controls['messageGroupDob'].value.trim() +
                '), members are ' +
                selectedGroupMembersNames +
                ' ,branch is ' +
                this.messageGroup.controls['messageGroupBranch'].value.trim() +
                ' and this group is ' +
                isPublic +
                ' and ' +
                allowMultiThreadChat +
                ' for this group';

              var activityData = {
                activityName: 'Update Discussion Group',
                activityType: 'manage group messaging',
                activityDescription: activityLogMessage,
              };
              console.log(
                'activityData Update Discussion Group :::',
                activityData
              );
              this._structureService.trackActivity(activityData);

              NProgress.done();
              var notify = $.notify(
                'Success! Patient Discussion Group updated'
              );
              this.disableButton = false;
              setTimeout(function () {
                notify.update({
                  type: 'success',
                  message:
                    '<strong>Success! Patient Discussion Group updated</strong>',
                });
              }, 5000);
            } else {
              NProgress.done();
              this.disableButton = false;
              var notify = $.notify(
                'Error! Patient Discussion Group already exists'
              );
              setTimeout(function () {
                notify.update({
                  type: 'danger',
                  message:
                    '<strong>Error! Patient Discussion Group already exists</strong>',
                });
              }, 5000);
            }
          });
      }
    } else {
      this.disableButton = true;
      this.noMemberError = true;
    }
  }

  createMessageGroup() {
    const patientUniqueId = this.messageGroup.controls['patientUniqueId'].value.trim();
		const tagMRN = (!isBlank(patientUniqueId))? ' [MRN: '+patientUniqueId+']': '';
    this._structureService
      .createMessageGroup(this.updateParams, true, this.selectSiteId)
      .then((response) => {
        let msgId: any = response;
        if (msgId.createMessageGroup.id != 0) {
          if (this.flagPatient == true) {
            this._structureService
              .addPatientToExternalIntegrationSystemPatient(
                this.userData.config.esi_code_for_patient_identity,
                this.patientAssoicatedId,
                this.messageGroup.controls['patientUniqueId'].value,
                msgId.createMessageGroup.id
              )
              .subscribe((data) => {
                console.log(
                  'addPatientToExternalIntegrationSystemPatient',
                  data['data']['addPatientToExternalIntegrationSystemPatient']
                );
              });
          }
          let newMessageGroupData = {
            status: 'new',
            id: msgId.createMessageGroup.id,
            name:
              this.messageGroup.controls['messageGroupName'].value +
              ' ' +
              this.messageGroup.controls['messageGroupLastName'].value +
              ' DOB ' +
              this.messageGroup.controls['messageGroupDob'].value +
              tagMRN,
            createdBy: this.userData.userId,
            tenantId:
              this._structureService.getCookie('crossTenantId') &&
              this._structureService.getCookie('crossTenantId') !=
                'undefined' &&
              this._structureService.getCookie('tenantId') !==
                this._structureService.getCookie('crossTenantId')
                ? this._structureService.getCookie('crossTenantId')
                : this.userData.tenantId,
            isPublic: this.selectedisPublic,
            allowMultiThreadChat: this.selectedAllowMultiThread,
            branch: this.messageGroup.controls['messageGroupBranch'].value,
            patientUniqueId:
              this.messageGroup.controls['patientUniqueId'].value,
            pdgroup: '1',
            members: '',
            memberIds: '',
            cmisIds: '',
            citusRoleId: 3,
            memberDetails: this.updateParams.groupMembers,
            type: 'group',
          };
          this._structureService.socket.emit(
            'newUserSignUp',
            newMessageGroupData
          );
          var isPublic = this.selectedisPublic ? 'public' : 'not public';
          var allowMultiThreadChat = this.selectedAllowMultiThread
            ? 'multi thread chat is allowed'
            : 'multi thread chat is not allowed';
          var selectedGroupMembersNames = '';
          var comma = '';

          $.each(this.selectedGroupMembers, function (key, value) {
            selectedGroupMembersNames =
              selectedGroupMembersNames + comma + value.displayName;
            comma = ', ';
          });

          this._SharedService.newMessageGroup.emit(newMessageGroupData);

          var activityLogMessage =
            this.userData.displayName +
            ' created Discussion Group(' +
            this.messageGroup.controls['messageGroupName'].value +
            ' ' +
            this.messageGroup.controls['messageGroupLastName'].value +
            ' DOB ' +
            this.messageGroup.controls['messageGroupDob'].value +
            '), members are ' +
            selectedGroupMembersNames +
            ' ,branch is ' +
            this.messageGroup.controls['messageGroupBranch'].value +
            ' and this group is ' +
            isPublic +
            ' and ' +
            allowMultiThreadChat +
            ' for this group';

          var activityData = {
            activityName: 'Create Discussion Group',
            activityType: 'manage group messaging',
            activityDescription: activityLogMessage,
          };
          console.log('activityDate :::', activityData);
          this._structureService.trackActivity(activityData);

          NProgress.done();
          //this.getGroups(true);
          var notify = $.notify('Success! Patient Discussion Group created');
          this.disableButton = false;
          setTimeout(function () {
            notify.update({
              type: 'success',
              message:
                '<strong>Success! Patient Discussion Group created</strong>',
            });
          }, 1000);

          setTimeout(() => {
            this.router.navigate(['/message/patient-discussion-groups']);
          }, 1000);
        } else {
          NProgress.done();
          this.disableButton = false;
          var notify = $.notify(
            'Error! Patient Discussion Group already exists'
          );
          setTimeout(function () {
            notify.update({
              type: 'danger',
              message:
                '<strong>Error! Patient Discussion Group already exists</strong>',
            });
          }, 5000);
        }
      });
  }

  togglePublic(value) {
    this.selectedisPublic = value;
  }
  toggleMultiThreadOption(value) {
    this.selectedAllowMultiThread = value;
  }
  deleteGroup(id) {
    swal(
      {
        title: 'Are you sure?',
        text: 'You will not be able to recover this patient discussion group',
        type: 'warning',
        showCancelButton: true,
        cancelButtonClass: 'btn-default',
        confirmButtonClass: 'btn-warning',
        confirmButtonText: 'Ok',
        closeOnConfirm: true,
      },
      () => {
        this._structureService.deleteMessageGroup(id).subscribe((data) => {
          console.log('data delete--->>>>> ', data);
          let msgId: any = data;
          let newMessageGroupData = {
            status: 'delete',
            id: msgId.data.deleteMessageGroup.id,
            citusRoleId: 3,
            userId: this.userData.userid,
            tenantId: this.userData.tenantId,
            type: 'group',
          };
          this._structureService.socket.emit(
            'newUserSignUp',
            newMessageGroupData
          );
          // for(let i=0;i<this.messageGroups.length;i++){
          //   if(this.messageGroups[i]!=undefined && newMessageGroupData.id== this.messageGroups[i].id){
          //     delete this.messageGroups[i];
          //   }
          // }
          //this.getGroups(true);
        });
      }
    );
  }
  removeExistingRole(roleID) {
    if (this.checkedRoleIds.length) {
      this.checkedRoleIds.forEach((value, key) => {
        console.log(
          'removeExistingRole==========>' + key + ' roleId=====> ' + roleID
        );
        console.log(value);
        if (value.id == roleID) {
          console.log(
            'MemberListByRoleWiseStaff',
            this.MemberListByRoleWiseStaff
          );

          this.checkedRoleIds.splice(key, 1);
          if (value.citusRoleId != 20) {
            var selectedRoletenant = this.MemberListByRoleWiseStaff.filter(
              (e) => e.tenantId === value.tenantId
            );
          } else if (value.citusRoleId == 20) {
            var selectedRoletenant = this.MemberListByRoleWisepartner.filter(
              (e) => e.tenantId === value.tenantId
            );
          }
          //  var selectedRoletenant =this.MemberListByRoleWise.filter(e => e.tenantId === value.tenantId);
          console.log(selectedRoletenant);
          if (selectedRoletenant.length > 0) {
            this.removeRoleFromExistingMember(roleID, value.citusRoleId);
          } else {
            console.log(this.selectedGroupMembers);
            if (
              this.selectedGroupMembers &&
              this.selectedGroupMembers.length > 0
            ) {
              this.selectedGroupMembers = this.selectedGroupMembers.filter(
                (members) => {
                  if (members.role != roleID) {
                    return true;
                  } else {
                    if (
                      this.optionShow == 'staffs' &&
                      members.citusRoleId != 20
                    ) {
                      this.userListChatwith.push(members);
                      this.userListChatwith = this.userListChatwith.slice();
                    } else if (
                      this.optionShow == 'partner' &&
                      members.citusRoleId == 20
                    ) {
                      this.userListChatwith.push(members);
                      this.userListChatwith = this.userListChatwith.slice();
                    }

                    this.userListChatwith.sort(function (a, b) {
                      if (
                        a.displayname.toLowerCase() <
                        b.displayname.toLowerCase()
                      )
                        return -1;
                      if (
                        a.displayname.toLowerCase() >
                        b.displayname.toLowerCase()
                      )
                        return 1;
                      return 0;
                    });
                  }
                }
              );
            }
          }
        }
      });
    }
  }
  removeRoleFromExistingMember(roleId, citusRoleId) {
    var self = this;
    if (citusRoleId != 20) {
      var MemberListByRoleWiseArr = self.MemberListByRoleWiseStaff;
    } else if (citusRoleId == 20) {
      var MemberListByRoleWiseArr = self.MemberListByRoleWisepartner;
    }

    console.log(MemberListByRoleWiseArr);
    MemberListByRoleWiseArr.forEach((value, key) => {
      if (value.roleData.id == roleId) {
        if (self.selectedGroupMembers && self.selectedGroupMembers.length > 0) {
          if (value.userList == undefined) {
            value.userList = [];
            console.log('testArray?????????????????????????', this.testArr);

            let i = 0;
            this.testArr.forEach((element) => {
              if (element.role == roleId) {
                value.userList[i] = { id: element.id, name: element.name };
              }
              i++;
            });
          }
          value.userList.map((user) => {
            self.selectedGroupMembers = self.selectedGroupMembers.filter(
              (members) => {
                if (members.id != user.id) {
                  return true;
                } else {
                  var users = self.clinicalUserDetails.find(
                    (users) => users.userid == members.id
                  );
                  console.log(users);
                  if (users && users.userid) {
                    self.userListChatwith.push(users);
                    self.userListChatwith = self.userListChatwith.slice();
                  }
                }
              }
            );
            self.selectedroleBasedStaffs = self.selectedroleBasedStaffs.filter(
              function (selectedmembers) {
                console.log(selectedmembers.id + '!=' + user.id);
                if (selectedmembers.id != user.id) {
                  return true;
                }
              }
            );
          });
        }
        var memberRole = value;
        if (this.optionShow == 'staffroles' && citusRoleId != 20) {
          self.newMemberListByRoleWise.push(memberRole);
        } else if (this.optionShow == 'partnerroles' && citusRoleId == 20) {
          self.newMemberListByRoleWise.push(memberRole);
        }
        //self.newMemberListByRoleWise.push(memberRole);
        self.newMemberListByRoleWise.forEach((mem) => {
          console.log(mem.roleData.id);
          if (mem.roleData.id == roleId) {
            mem['userList'] = [];
          }
        });
        self.newMemberListByRoleWise = self.newMemberListByRoleWise.slice();
        self.newMemberListByRoleWise.sort(function (a, b) {
          if (a.roleData.name.toLowerCase() < b.roleData.name.toLowerCase())
            return -1;
          if (a.roleData.name.toLowerCase() > b.roleData.name.toLowerCase())
            return 1;
          return 0;
        });
      }
      // else {
      //   alert(0)
      //   console.log("IVIVIVIVIVI",this.selectedGroupMembers);
      //   this.registeredPdgRoles.forEach(element => {
      //   if(element == roleId){
      //     alert(1)

      //   if(this.selectedGroupMembers && this.selectedGroupMembers.length > 0){

      //       this.selectedGroupMembers = this.selectedGroupMembers.filter((members)=>
      //      {
      //       console.log(members.role +"!="+ roleId)
      //      // && members.seconRoleId != roleId
      //       if (members.role != roleId ) {
      //         return true;
      //       }
      //     });

      //   }

      //  }

      // });

      //  }
    });
    self.userListChatwith.sort(function (a, b) {
      if (a.displayname.toLowerCase() < b.displayname.toLowerCase()) return -1;
      if (a.displayname.toLowerCase() > b.displayname.toLowerCase()) return 1;
      return 0;
    });
    self.selectedGroupMembers = self.selectedGroupMembers.slice();
  }
  removeRoleUsersFromExistingMember(roleId) {
    if (this.selectedGroupMembers && this.selectedGroupMembers.length > 0) {
      console.log('deleted role isssssss===>' + roleId);
      this.selectedGroupMembers.forEach((value) => {
        console.log('selectedGroupMembers value.role.id====>' + value.role.id);
        console.log('selectedGroupMembers value ==========>', value);
        if (value.role == roleId) {
          console.log('remove user==>' + value.displayName);
          this.removeMember(value.id);
        } else if (value.dualRoles && value.dualRoles.length > 1) {
          value.dualRoles.forEach((element) => {
            if (!element.isPrimary && element.tenantUsersRoleId == roleId) {
              console.log(
                'dualRoles element.tenantUsersRoleId ===>' +
                  element.tenantUsersRoleId
              );
              var removeMember = true;
              this.checkedRoleIds.forEach((rolevalue, key) => {
                console.log('deleted role isssssss===>' + roleId);
                console.log(
                  'selectedGroupMembers value.role.id====>' + value.role.id
                );
                console.log(
                  'dualRoles element.tenantUsersRoleId ===>' +
                    element.tenantUsersRoleId
                );
                console.log('rolevalue==>' + rolevalue.id);
                console.log(rolevalue.id + '==' + element.tenantUsersRoleId);
                if (removeMember && rolevalue.id == value.role.id) {
                  removeMember = false;
                }
              });
              if (removeMember) {
                console.log('dual role remove user==>' + value.displayName);
                this.removeMember(value.id);
              }
            }
          });
        }
      });
    }
  }
  getNursingAgencyTags() {
    const userData: any = this._structureService.getUserdata();
    const tagGetData =
      '?userId=' + userData.userId + '&tenantId=' + userData.tenantId;
    const tagTypes = ['2']; // Message Tag =1, User Tag =2 , Document Tag =3
    this._structureService
      .getNursingAgencyTagsByGroup(tagGetData, tagTypes)
      .then((data: any) => {
        this.nursingAgencyTags = data;
      });
  }
}
