<!-- START: dashboard alpha -->
<!-- START: dashboard alpha -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Message Group 1 (Wireframe)</strong>
        </span>
        
    </div>
    <div class="card-block">        
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/dashboard']" >Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/message']" >Message Settings</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/message']" >Group Message</a></li>
            <li class="breadcrumb-item">Members</li>
        </ol> 

        <div class="row">
            <div class="col-lg-12">
                <div class="mb-5">
                    <form action="#" class="form-horizontal">
                        <div class="form-body">
                            <div class="form-group row">
                                <div class="col-md-12">
                                    <input class="form-control" id="message_group_name" placeholder="Message Group Name" type="text" value="Message Group 1">
                                </div>
                            </div>
                    
                            <div class="form-actions">
                                <div class="form-group row">
                                    <h5 class="text-black col-md-9"><strong>Existing Group Members</strong></h5>
                                    <div class="col-md-3 ">
                                    </div>
                                </div>
                            </div>

                            <table class="table table-hover nowrap" id="example1" width="100%">
                                <thead>
                                    <th>#</th>
                                    <th>Name</th>
                                    <th>Action</th>
                                </thead>
                                <tbody>
                                    <!--<tr class="odd gradeX">-->
                                    <tr class="odd gradeX">
                                        <td> 1</td>
                                        <td> James Antony </td>
                                        <td><a href="javascript: void(0);" class="cat__core__link--underlined"><small><i class="icmn-cross"><!-- --></i></small> Remove</a></td>
                                        
                                    </tr>
                                    <tr class="odd gradeX">
                                        <td> 2</td>
                                        <td> James George </td>
                                        <td><a href="javascript: void(0);" class="cat__core__link--underlined"><small><i class="icmn-cross"><!-- --></i></small> Remove</a></td>
                                        
                                    </tr>
                                    <tr class="odd gradeX">
                                        <td> 3</td>
                                        <td> Prince George </td>
                                        <td><a href="javascript: void(0);" class="cat__core__link--underlined"><small><i class="icmn-cross"><!-- --></i></small> Remove</a></td>
                                        
                                    </tr>
                                    
                                </tbody>
                            </table>

                            <div class="form-actions">
                                <div class="form-group row">
                                    <h5 class="text-black col-md-9"><strong>New Group Members</strong></h5>
                                    <div class="col-md-3 ">
                                        <button [routerLink]="['/message/addgroupmember']" type="button" class="btn btn-sm btn-primary pull-right">Add Group Member</button>
                                        
                                    </div>
                                </div>
                            </div>

                            <table class="table table-hover nowrap" id="example1" width="100%">
                                <thead>
                                    <th>#</th>
                                    <th>Name</th>
                                    <th>Action</th>
                                </thead>
                                <tbody>
                                    <!--<tr class="odd gradeX">-->
                                    <tr class="odd gradeX">
                                        <td> 1</td>
                                        <td> James Antony </td>
                                        <td><a href="javascript: void(0);" class="cat__core__link--underlined"><small><i class="icmn-cross"><!-- --></i></small> Remove</a></td>
                                        
                                    </tr>
                                    <tr class="odd gradeX">
                                        <td> 2</td>
                                        <td> James George </td>
                                        <td><a href="javascript: void(0);" class="cat__core__link--underlined"><small><i class="icmn-cross"><!-- --></i></small> Remove</a></td>
                                        
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" class="btn btn-primary">Save</button>
                            <button type="button" [routerLink]="['/message/message']" class="btn btn-default">Cancel</button>
                        </div>
                    

                    </form>
                    <!-- End Horizontal Form -->
                </div>
            </div>
        </div>
    </div>
</section>
<!-- END: forms/basic-forms-elements -->
<!-- END: dashboard alpha -->
