import { Component, OnInit, ViewChild,ElementRef } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { InboxService } from './../inbox/inbox.service';
import { filterMessageGroupPipe } from './messagegroup-search.pipes';
import { SharedService } from './../shared/sharedServices';
import { userFilterPipe } from './../../structure/inbox/inbox-modal-filter';
import { GlobalDataShareService } from './../../structure/shared/global-data-share.service';
import { Subject } from 'rxjs';
import { GroupsListingRequest, MessageGroupRequest } from '../../models/message-center/messageCenter';
import { MessageService } from 'app/services/message-center/message.service';
import { GroupType, CONSTANTS } from 'app/constants/constants';
import { Location } from '@angular/common';

declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;
declare var swal: any;

var async = require("async");
import each from 'async/each';
import { isBlank } from 'app/utils/utils';
@Component({
  selector: 'app-message',
  templateUrl: './message.html',
  styleUrls: ['./message-common-component.scss']
})
 
export class MessageComponent implements OnInit {
  @ViewChild('userSearch') srch:ElementRef;
  @ViewChild('newMembers') srchs:ElementRef;


  //editGroup = false;
  //addGroup = false;
  editGroupMessage = false;
  searchInboxkeyword;
  addGroupMessage = false;
  groupList = [];
  staffList = [];
  prevText;
  otherTenantStaffList = [];
  memberList = [];
  otherTenantMemberList = [];
  selected;
  loadingGroups = true;
  offset=0;
  limit = 25;
  branchValue;
  allMessageGroups = [];
  hideLoadMore = false;
  selectedGroupDetails;
  newMemberList = [];
  newMemberOtherTenantList = [];
  newallUserDetailsInMsgGroup = [];
  groupMemberList = [];
  newMessageGroups;
  groupListNames = [];
  newMember;
  existingMemberIds = [];
  selectedGroupMembers = [];
  selectedGroupMemberIds = [];
  selectedGroupNewMembers = [];
  checkedIds = [];
  selectedisPublic;
  selectedAllowMultiThread;
  messageGroup: FormGroup;
  updateParams;
  userDetails:any;
  userData;
  allowSave = true;
  privilegeManageAllMessageGroup=false;
  addMemberError=false;
  noMemberError=false;
  disableButton = false;
  newMemberListByRoleWise = [];
  newMemberOtherTenantListByRoleWise = [];
  createdPatientIdIndex: any;
  messageGroups: any = 'groups';
  optionShow: any = 'staff';
  userListChatwith: any;
  memberDataGroupWise: any;
  MessageGroupWithLoader:any = {
    groups: true,
    staffs: true,
    partner:true,
    otherTenantstaff: true
  }
  ispdgs;

  config:any;
  configData:any = {};
  column3 = false
  allUserDetailsInMsgGroup = [];
  searchText = '';
  searchTexts =  '';
  UsergroupIds;
  crossTenantOptions = [];
  crossTenantName:String;
  enableCommunicationWithInternalstaffs = false;
  internalSiteId: any;
  crossTenatsForBranch = [];
  isNursingAgencyEnabled = 0;
  nursingAgencyTags: any = [];
  canEdit = true;
  crossTenantId: any;
  staffUserList:any=[];
  usersListByRoleId:any;
  usersList:any;
  staffTenantFilter:any = {
    selectedTenant : null,
    tenants: [],
    filterEnabled: false,
    enabledReset: false
};
loadMoreSearchValue:any = undefined;
callFromInitialCountChatWithUserlist:boolean = false;
chatwithPageCount = {
  staffs: 0,
  partner:0
}
noMoreItemsAvailable = {
  users: false
};
chatWithUsersLoading = true;
staffLoader:any = {
  groups: true,
  staffs: true,
  partner: true,
  otherTenantstaff: true,
}

loadMoremessage = {
  users: 'Load more'
};
scheduledPrimaryCar:any;
defaultNurses:any;
clinicianLoad:any=false;
selectedTenant:any;
crossTenantOptionsChatWith = [];
clinicalUserDetails;
selectedExistingMembers = [];
staffRolesList=[];
isloadingTime=true;   
tenantIDForInviteList;
clickedRoleId;
staffsUnderRolesList = [];
userList=[];
membersLoaded =true;
dummyCheckedRoleIds=[];
roleBasedStaffs =[];
selectedroleBasedStaffs =[];
checkedRoleIds = [];
checkedIdsWithRole=[];
removedRoleUsers=[];
MemberListByRoleWiseStaff = [];

chatWithLoader:any = {
  groups: true,
  staffs: true,
  partner:true,
  otherTenantstaff: false,
  patients: true,
  otherTenantPatients: true
}
messageGroupMemberIds;
privileges = this._structureService.getCookie('userPrivileges');
organizationSwitchPrivilege = false;
isLoadingRoles = false;
isStaffsloadingTime = false;
clickedRole=0;
MemberListByRoleWisepartner = [];
testArr = [];
clickedGroup;
isGrouploadingTime = false;
dummyCheckedGroupMembers=[];
messageGroupId:any;
nursing_agency_on = false;
usersInRole = true;
selectSiteId: any;
messageSiteId: any;
eventsSubject: Subject<void> = new Subject<void>();
expandedRoles = [];
hideSiteSelection:boolean;
hideSiteSelectionApply:boolean;
editedSite: any;
singleSelection: boolean = false;
  multiSiteEnable: boolean;
  enableCrossSite: boolean;
multiTenant: boolean;
selectedBranchId: any;
preventMultipleCall = true;
apply = false;
tempMessageSiteId : any;
tempEditedSiteId : any;
selectedGroupRoles = [];
resetPdgSelect = false;
  constructor(private route: ActivatedRoute,
    private router: Router,
    private _structureService: StructureService,
    private _ToolTipService: ToolTipService,
    private _inboxService: InboxService,
    private _formBuild: FormBuilder,
    private MessageGroupSearchFilter: filterMessageGroupPipe,
    public _SharedService:SharedService,
    public modalFilter:userFilterPipe,
    public _GlobalDataShareService:GlobalDataShareService,
    private messageService: MessageService,
    private location: Location
  ) { this.config = this._structureService.userDataConfig;
      this.configData = JSON.parse(this.config); }

  ngOnInit() {
  
  $('body').on('keypress',function(e) {
    var code = (e.keyCode ? e.keyCode : e.which);
    if(code == 13) {
      return false;
    }
  })

    this.userDetails = this._structureService.userDetails;
    this.userData = JSON.parse(this.userDetails);
    this.multiSiteEnable = this.userData.config.enable_multisite == 1 ? true : false;

    // if (this.userData.config.enable_nursing_agencies_visibility_restrictions == 1 && this.userData.nursing_agencies != "") {
    //   this.nursing_agency_on = true;
    //   this.optionShow = 'partner';
		// }
    this.crossTenantId =(this._structureService.getCookie('crossTenantId')&& this._structureService.getCookie('crossTenantId') !="")?this._structureService.getCookie('crossTenantId'):this.userData.tenantId;
		console.log("crossTenantId: =========="+this.crossTenantId);

   this.enableCrossSite= this.userData.enable_cross_site == 1 ?true :false;
    if(this._structureService.getCookie('userPrivileges').indexOf('manageAllMessageGroup')!=-1 || this._structureService.getCookie('userPrivileges').indexOf('manageTenants')!=-1){
      this.privilegeManageAllMessageGroup = true;
    }
    // this.hideSiteSelection = this.userData.mySites.length == 1 ? true : false;
    if(this.userData.organizationMasterId !=0 && this.userData.crossTenantsDetails && this.userData.crossTenantsDetails.length && this.userData.masterEnabled == "0"){
      if(this.userData.crossTenantsDetails.length > 1 
        && this._structureService.getCookie('userPrivileges').indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
        && this.configData.allow_multiple_organization==1 
        && (this.configData.enable_nursing_agencies_visibility_restrictions != 1 || (this.configData.enable_nursing_agencies_visibility_restrictions == 1 && this.userData.nursing_agencies == ""))){
        this.column3 = true;
        this.crossTenantOptions = this.userData.crossTenantsDetails;
        this.crossTenantOptions.forEach((tenant)=> {
          if(tenant.id == this._structureService.getCookie("crossTenantId")) {
              this.crossTenantName = tenant.tenantName;
              this.staffTenantFilter.selectedTenant = tenant.id;
              this._GlobalDataShareService.setSelectedTenantDetails(tenant);
             this.staffTenantFilter.tenants = this.userData.crossTenantsDetails;
          }
        });

        this.crossTenantOptions.forEach((tenant) => {
          if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
              this.selectedTenant = tenant;
              this.staffTenantFilter.selectedTenant = tenant.id;
              this._GlobalDataShareService.setSelectedTenantDetails(tenant);
            this.staffTenantFilter.tenants = this.userData.crossTenantsDetails;
            this.crossTenantOptions.forEach((tenant) => {
              if (tenant.id == this._structureService.getCookie("crossTenantId")) {
                  this.crossTenantName = tenant.tenantName;
              }
          });
          }
        });
      }else{
        this.crossTenantOptions = [];
        this.column3 = false;
      }
      
    } else if (this.userData.masterEnabled == "1" && this.userData.isMaster == "1") {
      this.column3 = true;
      this.crossTenantOptions = this.userData.crossTenantsDetails;
      this.crossTenantOptions.forEach((tenant)=> {
        if(tenant.id == this._structureService.getCookie("crossTenantId")) {
            this.crossTenantName = tenant.tenantName;
            this.selectedTenant = tenant;
            this.staffTenantFilter.selectedTenant = tenant.id;
            this._GlobalDataShareService.setSelectedTenantDetails(tenant);
            this.staffTenantFilter.tenants = this.userData.crossTenantsDetails;
        }
      });

    } else if(this.userData.masterEnabled == "1" && this.userData.isMaster == "0") {
      this.column3 = true;
      this.crossTenantOptions = this.userData.crossTenantsDetails;
      this.crossTenantOptions.forEach((tenant)=> {
        if(tenant.id == this._structureService.getCookie("crossTenantId")) {
            this.crossTenantName = tenant.tenantName;
            this.staffTenantFilter.selectedTenant = tenant.id;
            this._GlobalDataShareService.setSelectedTenantDetails(tenant);
            this.selectedTenant = tenant;
            this.staffTenantFilter.tenants = this.userData.crossTenantsDetails;
        }        
      });
      this.enableCommunicationWithInternalstaffs = true;
      this.internalSiteId = this.userData.master_details.id;
     
      if (this.userData.organizationMasterId != 0 
        && this.userData.crossTenantsDetails 
        && this.userData.crossTenantsDetails.length > 1) {
        this.crossTenantOptionsChatWith = this.userData.crossTenantsDetails;
        this.staffTenantFilter.tenants = this.crossTenantOptions;
        this.crossTenantOptions.forEach((tenant) => {
            if (tenant.id == this._structureService.getCookie("crossTenantId")) {
                this.crossTenantName = tenant.tenantName;
            }
        });
        if ((this.crossTenantOptions.length > 1
            && this.privileges.indexOf('allowOrganizationSwitching') != -1
            && this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication') !== -1 
            && this.configData.allow_multiple_organization == 1
            && this.userData.masterEnabled == '0'
            && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
        || (this.crossTenantOptions.length > 1 
            && this.userData.isMaster =='1' 
            && this.userData.masterEnabled == '1')
        || (this.crossTenantOptions.length > 1 
            && this.userData.isMaster =='0' 
            && this.userData.masterEnabled == '1'
            && this.userData.group !='3')
        || (this.crossTenantOptions.length > 1
            && this.privileges.indexOf('allowOrganizationSwitching') != -1
            && this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication') !== -1 
            && this.configData.allow_multiple_organization == 1
            && this.userData.masterEnabled == '0'
            && this.configData.enable_nursing_agencies_visibility_restrictions == 1
            && this.userData.nursing_agencies == "")
        ) {
           if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1') {
                this.internalSiteId = this.userData.master_details.id;
            }
            this.staffLoader.otherTenantstaff = true;
            this.staffTenantFilter.tenants = this.staffTenantFilter.tenants.filter((tenant)=> {
                if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1'){
                    if(this.internalSiteId == tenant.id) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return true;
                }
            });
            if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1') {
                if(this.userData.master_details.id != this.userData.tenantId) {
                    this.crossTenantOptions.map((tenant)=> {
                        if(tenant.id == this.userData.tenantId) {
                            this.staffTenantFilter.tenants.unshift(tenant);
                        }
                    });
                }
            }
            
        } else { 
            this.crossTenantOptionsChatWith = [];
            this.staffTenantFilter.tenants = [];
            
        }
    }else {
        this.crossTenantOptionsChatWith = [];
        this.staffTenantFilter.tenants = [];
        
    }   
    
    } else {
      this.column3 = false
      this.crossTenantOptions = [];
    }
    var page = "message-groups";
    $(".refresh-page").tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGGP00001') });
    $(".new-message-group").tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGGP00002') });
    $(".make-public").tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGGP00003') });
    $(".multi-chat-thread").tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGGP00006') });
    $(".clear-btn-img").tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGGP00004') });
    $(".toolSelect").tooltip({ title: this._ToolTipService.getToolTip(page, 'MSGGP00005') });
    $('body').on("keypress","input[type=text]",function(e){
      var code = (e.keyCode ? e.keyCode : e.which);
      if (code == 13) { 
          e.preventDefault();
      }
    });

    if (this.userData.nursing_agencies != '') {
			this.messageGroup = this._formBuild.group({
        messageGroupName: ['', [Validators.required, this.noWhitespaceValidator]],
        nursingAgencyUserTag: [''],
      });
		} else {
			this.messageGroup = this._formBuild.group({
        messageGroupName: ['', [Validators.required, this.noWhitespaceValidator]],
        nursingAgencyUserTag: [''],        
      });
    }
    
    this._structureService.previousUrl = 'message/editmessagegroup';
    this.setChatWithTenantFilterSelect();
   setTimeout( ()=> {
    $('#staffTenantFilter').select2({
        dropdownParent: $("#staffListblock")
    });
  // $("#staffTenantFilter").val(this.staffTenantFilter.selectedTenant).trigger("change");
    $("#staffTenantFilter").css("text-overflow", "ellipsis");
});
// if(this.optionShow == 'staff'){
//   this.staffUserList = [];
//   this.loadMoreUsers(this.optionShow, this.srch.nativeElement.value.trim());
//   }
    this.getGroups(true);
    this.preventMultipleCall = false;
    
  // this.assignChatWithTenantFilterData();
  $('body').on('change','#staffTenantFilter',(event)=>{
    if(event.target.value)  {
    var previousSelectedTenant = this.staffTenantFilter.tenants.find((tenant) => tenant.id == this.staffTenantFilter.selectedTenant);
     var currentSelectedTenant = this.staffTenantFilter.tenants.find((tenant) => tenant.id == this.staffTenantFilter.selectedTenant);
            var activityData = {
                activityName: "Message Group " + ((this.optionShow == 'staff') ? 'Staff ' : ((this.optionShow == 'partner') ? 'partner ' : ((this.optionShow == 'patient') ? 'Patient ' : ' '))) +"Tenant Switching",
                activityType: "messaging",
                activityDescription: this.userData.displayName + " (" + this.userData.userId + ") has selected tenant " + currentSelectedTenant.tenantName + '(' + currentSelectedTenant.id +')' + ((previousSelectedTenant) ? (' from tenant '+ previousSelectedTenant.tenantName + '(' + previousSelectedTenant.id +')') : ''),
                tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
            };
            this._structureService.trackActivity(activityData);
        this.filterBranch(event);
        
    }
});
    /*setTimeout(()=>{ 
       $('.dataTable').DataTable({
         responsive: true,
         retrieve: true
       
       });
     }, 1);

     setTimeout(()=>{ 
       $('#example2').DataTable({
         responsive: true,
         retrieve: true
       
       });
     }, 1);*/

	/**
      * Get User tags with tag type (Nursing Agency) BioMatrix -  Nursing tags.
      */

		if (this.userData.config.enable_nursing_agencies_visibility_restrictions == 1) {
			this.getNursingAgencyTags();
			this.isNursingAgencyEnabled = this.userData.config.enable_nursing_agencies_visibility_restrictions;
		}
  }
	hideDropdown(hideItem : any){
    this.hideSiteSelection = hideItem.hideItem;
    }
    hideDropdownApply(hideItem : any){
        this.hideSiteSelectionApply = hideItem.hideItem;
    }
    getBranchIds(data:any) {
			if(this.optionShow !='groups' && this.optionShow !='msggroups' && this.staffTenantFilter.filterEnabled && this.staffTenantFilter.tenants.length && 
			this.userData.config.enable_multisite != 1) {
			this.multiTenant = true;
			this.selectedBranchId = data['siteId'].toString();
			this.search(this.optionShow);
			}
        }
        getSiteIds(data:any,siteselect=0){
          var self = this;
          if(this.userData.mySites && this.userData.mySites.length == 1 && !this.enableCrossSite) {
            this.selectSiteId = this.userData.mySites[0].id;
            } else {
              this.selectSiteId = data['siteId'].toString();
            }
            this.tempMessageSiteId = this.messageSiteId;
            this.tempEditedSiteId = this.editedSite;
            if(siteselect == 1){
              this.messageSiteId = this.selectSiteId;
            }
            if(siteselect == 0){
                this.editedSite = this.selectSiteId;
            }
           
      
          const fromSite = self.userData.mySites.length == 1 ? self.userData.mySites[0]['id'].toString() : 
                  data['siteId'].toString();
            self.disableButton = fromSite == 0 ? true : false;
          if(self.userData.mySites.length != 1 &&self.editedSite && fromSite != 0 && fromSite != undefined && fromSite.split(',').sort().toString() != self.editedSite.toString().split(',').sort().toString()) {
            swal(
              {
                title: 'Are you sure?',
                text: this._ToolTipService.getTranslateData('MESSAGES.SITE_CHANGE'),
                type: 'warning',
                showCancelButton: true,
                cancelButtonClass: 'btn-default',
                confirmButtonClass: 'btn-warning',
                confirmButtonText: 'Continue Anyway',
                closeOnConfirm: true
              },
              function(isConfirm) {
                if (isConfirm) {
                  self.selectSiteId = fromSite;
                  if(siteselect == 1){
                    self.messageSiteId = self.selectSiteId;
                    self.editedSite = self.selectSiteId;
                  }
                  self.reloadEditData(self.selectSiteId);
      
              } else {
                self.messageSiteId = self.tempMessageSiteId;
                self.editedSite = [];
                self.editedSite = self.tempEditedSiteId; 
                self.emitEventToSelectSites('selectPreviousSite');
                self.reloadEditData(self.editedSite.toString());
                }
              }
            );
      
            } else {
              self.selectSiteId = data['siteId'].toString();
              if(this.preventMultipleCall){
                    self.reloadEditData(self.editedSite.toString());
              }
              
            }
            if (self.optionShow == 'staffroles' || self.optionShow == 'partnerroles') {
              self.usersInRole = true;
        
              self.expandedRoles.forEach((roleID) => {
              $(".expand-icon-" + roleID).removeClass("fa-minus");
              $(".expand-icon-" + roleID).addClass("fa-plus");
              $(".sub-item-panel-" + roleID).removeClass("showall");
              $(".sub-item-panel-" + roleID + " li").remove();  
              });
            } else if(self.optionShow == 'msggroups' || self.optionShow ==='groups') {
              self.expandedRoles.forEach((roleID) => {
                $(".expand-icon-" + roleID).removeClass("fa-minus");
                $(".expand-icon-" + roleID).addClass("fa-plus");
                $(".sub-item-panel-" + roleID).removeClass("showall");
                $(".sub-item-panel-" + roleID + " li").remove();
        
                });
            }
            
          }
  getSiteIdsApply(data:any,siteselect=0){
    var self = this;
     const fromSite = self.userData.mySites.length == 1 ? self.userData.mySites[0]['id'].toString() : 
			data['siteId'].toString();
      self.disableButton = fromSite == 0 ? true : false;

        this.selectSiteId = data['siteId'].toString();
        if(this.preventMultipleCall && this.apply){
            this.search(this.optionShow);
            this.preventMultipleCall = false;
        }else if(!this.preventMultipleCall && this.apply){
            if(this.selectSiteId !== this.messageSiteId && this.selectSiteId !=='0'){  
                this.search(this.optionShow);
            }
        }
       
       this.apply = true;
      if (self.optionShow == 'staffroles' || self.optionShow == 'partnerroles') {
        self.usersInRole = true;
  
        self.expandedRoles.forEach((roleID) => {
        $(".expand-icon-" + roleID).removeClass("fa-minus");
        $(".expand-icon-" + roleID).addClass("fa-plus");
        $(".sub-item-panel-" + roleID).removeClass("showall");
        $(".sub-item-panel-" + roleID + " li").remove();  
        });
      } else if(self.optionShow == 'msggroups' || self.optionShow ==='groups') {
        self.expandedRoles.forEach((roleID) => {
          $(".expand-icon-" + roleID).removeClass("fa-minus");
          $(".expand-icon-" + roleID).addClass("fa-plus");
          $(".sub-item-panel-" + roleID).removeClass("showall");
          $(".sub-item-panel-" + roleID + " li").remove();
  
          });
      }
      
    }
  showDataStaff(data){
    this.staffUserList = [];
    this.clinicalUserDetails = [];
    this.usersList = [];
    this.srch.nativeElement.value = "";
    var initialData = data;
    var tabSelectedFrom = '';
    if(data) {
        tabSelectedFrom = this.optionShow;
    }
    this.staffTenantFilter.enabledReset = true;
   
    this.optionShow=data;
    
    if(this.optionShow == 'staff' || this.optionShow == 'partner') {
        this.staffTenantFilter.enabledReset = false;
        this.setChatWithTenantFilterSelect();
    }
    console.log("this.optionShow", this.optionShow);
    if(this.optionShow=='staff' || this.optionShow=='partner'){
        this.initialiseStaffOrPatientList(this.optionShow);
    }     
    var activityData = {
        activityName: "Add Message Group With Tab Switching",
        activityType: "messaging",
        activityDescription: this.userData.displayName + " (" + this.userData.userId + ") has selected tab " + this.optionShow + ((tabSelectedFrom) ? (' from tab '+ tabSelectedFrom) : ''),
        tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
    };
    this._structureService.trackActivity(activityData);
}

filterBranch(event){
  $("#userSearchTxtRoles").val('');
  $("#notFoundRoles").hide();
  console.log(event);
  if(event.target.value)  {
    var previousSelectedTenant = this.staffTenantFilter.tenants.find((tenant) => tenant.id == this.staffTenantFilter.selectedTenant);
    this.staffTenantFilter.selectedTenant = event.target.value;
    this.staffTenantFilter.selectedTenantName = event.target.selectedOptions.text;
    if(this.staffTenantFilter.filterEnabled) {
      if(this.optionShow =='staff' || this.optionShow =='partner' ){
        this.reset(this.optionShow);
      } 
      if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
        this.chatWithLoader.otherTenantstaff =true;
        this.isLoadingRoles = true;

        this.getStaffRoles(this.optionShow);
    } 
       
        var currentSelectedTenant = this.staffTenantFilter.tenants.find((tenant) => tenant.id == this.staffTenantFilter.selectedTenant);
        var activityData = {
            activityName: "Chat With " + ((this.optionShow == 'staff') ? 'Staff ' : ((this.optionShow == 'partner') ? 'partner ' : ((this.optionShow == 'patient') ? 'Patient ' : ' '))) +"Tenant Switching",
            activityType: "messaging",
            activityDescription: this.userData.displayName + " (" + this.userData.userId + ") has selected tenant " + currentSelectedTenant.tenantName + '(' + currentSelectedTenant.id +')' + ((previousSelectedTenant) ? (' from tenant '+ previousSelectedTenant.tenantName + '(' + previousSelectedTenant.id +')') : ''),
            tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
        };
        this._structureService.trackActivity(activityData);
    } else {
        this.staffTenantFilter.enabledReset = true;
    }
}



}


initialiseStaffOrPatientList(optionShow) {
  if(this.staffTenantFilter.setTenantDropDown && this.staffTenantFilter.selectedTenant) {
      this.staffTenantFilter.setTenantDropDown = false;
      setTimeout( ()=> {
          $('#staffTenantFilter').select2({
              dropdownParent: $("#staffListblock")
          });
         // $("#staffTenantFilter").val(this.staffTenantFilter.selectedTenant).trigger("change");
          $("#staffTenantFilter").css("text-overflow", "ellipsis");
      });
  }
  console.log('initialiseStaffOrPatientList.optionShow',optionShow);
  this.loadMoreSearchValue = undefined;
  this.callFromInitialCountChatWithUserlist = true;
  this.chatwithPageCount = {
      staffs: 0,
     partner:0
  }
  this.noMoreItemsAvailable = {
      users: false
  };
  this.staffUserList = [];
  this.clinicalUserDetails = [];
  this.usersList = [];
  var isRoleAvailable = true;
  var clinicianRolesAvaiable = null;
  var setCliniciansRoleAvailableResponse;
   
      if(isRoleAvailable) {
          this.getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,true,false, optionShow, undefined)
      } else {
          this.chatWithUsersLoading = false;
      }
  
}

getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,init, loadMore, optionShow, searchValue) {
  var self=this;
 let chosenSiteId;
  let chatWithFilterTenantId = null;
  this.UsergroupIds = clinicianRolesAvaiable;
  if(init) {
     if(optionShow=='staff') {
          this.staffLoader.staffs = true;
          this.UsergroupIds = 3; 
      }
      if(optionShow=='partner') {
          this.staffLoader.partner = true; 
          this.UsergroupIds = 20;
      }
  }else{
    if(optionShow=='staff') { 
      this.UsergroupIds = 3;
    }
    if(optionShow=='partner') { 
        this.UsergroupIds = 20;
    }
  }
  if(optionShow == 'staff' || optionShow == 'partner' ) {
      chatWithFilterTenantId = this.staffTenantFilter.selectedTenant;
     var memberIds='';
		  if(this.messageGroupMemberIds != '')
		  { 
			memberIds = this.messageGroupMemberIds;		
		} 
  }
  // alert(memberIds)
  this.loadMoremessage.users = "Loading ....";
  chosenSiteId = this.multiTenant ? this.selectedBranchId : (this.selectSiteId == undefined || this.selectSiteId == 0 || isBlank(this.selectSiteId)) ? this.editedSite : this.selectSiteId;
  this._inboxService.getUsersListByRoleId((this.UsergroupIds ? this.UsergroupIds : 3), 0, null,null,(init ? 0 : ((optionShow == 'staff') ? this.chatwithPageCount.staffs : ((optionShow == 'partner') ? this.chatwithPageCount.partner :'') )),((optionShow == 'staff' && !clinicianRolesAvaiable) ? true : ((optionShow == 'staff' && clinicianRolesAvaiable) ? undefined : false)),searchValue, chatWithFilterTenantId,undefined,memberIds,optionShow, chosenSiteId).then((data:any) => {
      this.chatWithUsersLoading = false;
      this.loadMoreSearchValue = searchValue;
      if(loadMore) {
          this.clinicalUserDetails = [...this.clinicalUserDetails, ...data];
      } else {
          this.clinicalUserDetails=data;
      }
      if(data.length != 20)
          this.noMoreItemsAvailable.users = true;
  
      if(optionShow=='staff') {
        console.log("inside**************************************************")
          this.chatwithPageCount.staffs += 1;
      }
      if(optionShow=='partner') { 
          this.chatwithPageCount.partner += 1;
      }
  
      for(var _i=0;_i<this.clinicalUserDetails.length;_i++){
          this.clinicalUserDetails[_i].isScheduled=1;
          if(this._inboxService.scheduleSelectionFilter(this.clinicalUserDetails[_i])){
              this.clinicalUserDetails[_i].isScheduled=1;
          }else{
              this.clinicalUserDetails[_i].isScheduled=0;
          }
      }
      this.loadMoremessage.users = "Load more";        
      this.staffUserList = this.modalFilter.transform(this.clinicalUserDetails, optionShow);
      
         if(this.selectedGroupMembers.length != 0){

         this.staffUserList = this.staffUserList.filter(
          (x) => !this.selectedGroupMembers.some((y) => y.id == x.userid)
        ); 
         }
        
      if(init) {
       if(optionShow=='staff') {
              this.staffLoader.staffs = false;
          }
        if(optionShow=='partner') {
            this.staffLoader.partner = false;
        }
      }
  }).catch((ex) => {
      if(init) {
          if(optionShow=='staff') {
              this.staffLoader.staffs = false;
          }
          if(optionShow=='partner') {
            this.staffLoader.partner = false;
        }
      }
  });
  }



setChatWithTenantFilterSelect() {
  if(this.selectedTenant) {
      this.staffTenantFilter.selectedTenant =  this.selectedTenant.id;
  } else {
      this.staffTenantFilter.selectedTenant = this._structureService.getCookie('tenantId');
  }
  switch(this.optionShow) {
      case 'staff': {
          if((this.userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
          && this.configData.allow_multiple_organization==1 
          && this.userData.crossTenantsDetails.length > 1 
          && this.userData.masterEnabled == '0'
          && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
      || (this.userData.masterEnabled == '1' 
          && this.userData.isMaster == '1')
      || (this.userData.masterEnabled == '1' 
          && this.userData.isMaster == '0'
          && this.userData.group !='3')
      || (this.userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
          && this.configData.allow_multiple_organization==1 
          && this.userData.crossTenantsDetails.length > 1 
          && this.userData.masterEnabled == '0'
          && this.configData.enable_nursing_agencies_visibility_restrictions == '1'
          && this.userData.nursing_agencies == '')) {
             this.assignChatWithTenantFilterData(); 
          } else {
              this.staffTenantFilter.filterEnabled = false;
              this.staffTenantFilter.tenants = [];
          }
          break;
      }
      case 'partner': {
        if((this.userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
        && this.configData.allow_multiple_organization==1 
        && this.userData.crossTenantsDetails.length > 1 
        && this.userData.masterEnabled == '0'
        && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
    || (this.userData.masterEnabled == '1' 
        && this.userData.isMaster == '1')
    || (this.userData.masterEnabled == '1' 
        && this.userData.isMaster == '0'
        && this.userData.group !='3')
    || (this.userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
        && this.configData.allow_multiple_organization==1 
        && this.userData.crossTenantsDetails.length > 1 
        && this.userData.masterEnabled == '0'
        && this.configData.enable_nursing_agencies_visibility_restrictions == '1'
        && this.userData.nursing_agencies == '')) {
           this.assignChatWithTenantFilterData(); 
        } else {
            this.staffTenantFilter.filterEnabled = false;
            this.staffTenantFilter.tenants = [];
        }
        break;
    }
      case 'staffroles': {
        if((this.userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
        && this.configData.allow_multiple_organization==1 
        && this.userData.crossTenantsDetails.length > 1 
        && this.userData.masterEnabled == '0'
        && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
    || (this.userData.masterEnabled == '1' 
        && this.userData.isMaster == '1')
    || (this.userData.masterEnabled == '1' 
        && this.userData.isMaster == '0'
        && this.userData.group !='3')
    || (this.userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
        && this.configData.allow_multiple_organization==1 
        && this.userData.crossTenantsDetails.length > 1 
        && this.userData.masterEnabled == '0'
        && this.configData.enable_nursing_agencies_visibility_restrictions == '1'
        && this.userData.nursing_agencies == '')) {
           this.assignChatWithTenantFilterData(); 
        } else {
            this.staffTenantFilter.filterEnabled = false;
            this.staffTenantFilter.tenants = [];
        }
        break;
    }
    case 'partnerroles': {
      if((this.userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
      && this.configData.allow_multiple_organization==1 
      && this.userData.crossTenantsDetails.length > 1 
      && this.userData.masterEnabled == '0'
      && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
  || (this.userData.masterEnabled == '1' 
      && this.userData.isMaster == '1')
  || (this.userData.masterEnabled == '1' 
      && this.userData.isMaster == '0'
      && this.userData.group !='3')
  || (this.userData.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
      && this.configData.allow_multiple_organization==1 
      && this.userData.crossTenantsDetails.length > 1 
      && this.userData.masterEnabled == '0'
      && this.configData.enable_nursing_agencies_visibility_restrictions == '1'
      && this.userData.nursing_agencies == '')) {
         this.assignChatWithTenantFilterData(); 
      } else {
          this.staffTenantFilter.filterEnabled = false;
          this.staffTenantFilter.tenants = [];
      }
      break;
  }      

      default: {
          this.staffTenantFilter.filterEnabled = false;
          this.staffTenantFilter.tenants = [];
          break;
      } 
  }
}

assignChatWithTenantFilterData() {
  this.staffTenantFilter.filterEnabled = true;

  if($("#staffTenantFilter").length) {
      setTimeout( ()=> {
          $('#staffTenantFilter').select2({
              dropdownParent: $("#staffListblock")
          });
          $("#staffTenantFilter").css("text-overflow", "ellipsis");
          if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles'){
           // $("#staffTenantFilter").val(this.staffTenantFilter.selectedTenant).trigger("change");
          }
      });
  } else {
      this.staffTenantFilter.setTenantDropDown = true;
  }
}
searchOnEnter(event,optionShow) {
  if(optionShow == 'staff' || optionShow == 'partner' || optionShow =='groups' || optionShow =='msggroups'){
    
       if(event.keyCode == 13) {
           if(this.srch.nativeElement.value.trim()){

               this.search(optionShow);
           } else {
               this.reset(optionShow)
           }
       }
     }
}


loadMoreUsers(optionShow, searchKeyword=undefined, notInit=false) {
  if(!this.loadMoreSearchValue) {
      if(this.srch.nativeElement.value.trim()){
          this.loadMoreSearchValue = undefined;
          this.callFromInitialCountChatWithUserlist = true;
          this.chatwithPageCount = {
              staffs: 0,
              partner:0
          }
          this.noMoreItemsAvailable = {
              users: false
          };
          notInit = false;
          this.staffUserList = [];
          this.clinicalUserDetails = [];
          this.usersList = [];
          searchKeyword = this.srch.nativeElement.value.trim();
      }
  } else if(this.loadMoreSearchValue && !this.srch.nativeElement.value.trim()) {
      this.reset(optionShow);
      return false;
  } else if(this.loadMoreSearchValue == this.srch.nativeElement.value.trim()) {
  } else {
      this.loadMoreSearchValue = undefined;
      this.callFromInitialCountChatWithUserlist = true;
      this.chatwithPageCount = {
          staffs: 0,
          partner:0
      }
      this.noMoreItemsAvailable = {
          users: false
      };
      notInit = false;
      this.staffUserList = [];
      this.clinicalUserDetails = [];
      this.usersList = [];
      searchKeyword = this.srch.nativeElement.value.trim();
  }
  var isRoleAvailable = true;
  var clinicianRolesAvaiable = null;
  var setCliniciansRoleAvailableResponse;
  
      if(isRoleAvailable) {
          this.getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,(!notInit ? true : false),(!notInit ? false : true), optionShow,searchKeyword)
      } else {
          this.chatWithUsersLoading = false;
      }
  

}

checkboxChanged(event,indexing) {
  this.addMemberError = false;
  var $this = $(event.target),
    checked = $this.prop("checked"),
    container = $this.parent(),
    siblings = container.siblings();
    console.log("event.target.name->",event.target.name);
    if(event.target.name=="messageGroup"){
      this.clickedGroup=String(event.target.value);
      this.membersLoaded =false;
      if(event.target.checked){
        var value= JSON.parse(event.target.value);
        var messageGroupId =String(value);
        this.messageGroupId=messageGroupId;
        if(this.dummyCheckedGroupMembers.indexOf(messageGroupId)==-1){
          this.dummyCheckedGroupMembers.push(+messageGroupId);
        }
        if(this.dummyCheckedGroupMembers && this.dummyCheckedGroupMembers.length > 0){
          this.membersLoaded = true;
        }
        }else{
          var value= JSON.parse(event.target.value);
          var messageGroupId =String(value);  
          let index = this.dummyCheckedGroupMembers.indexOf(messageGroupId);
          this.dummyCheckedGroupMembers.splice(index, 1);     
          this.membersLoaded =true;
      }
    }
    if(event.target.name=="staffRoleWise"){
      var value= JSON.parse(event.target.value);
      if(event.target.checked){
        if(this.dummyCheckedRoleIds.indexOf(value.id)==-1)
          this.dummyCheckedRoleIds.push(value.id);
         var  roleID =value.id;
         this.clickedRole=roleID;
      }else{
        let index = this.dummyCheckedRoleIds.indexOf(value.id);
        this.dummyCheckedRoleIds.splice(index, 1);
        var  roleID =value.id;

        this.newMemberListByRoleWise[indexing]['userList'] = [];

          this.selectedroleBasedStaffs = this.selectedroleBasedStaffs.filter(function (selectedRole) {
            return selectedRole.selectedRoleid != roleID;
          });
          
          
       
        this.membersLoaded =true;

      }
    }

  container.find('input[type="checkbox"]')
    .prop({
      indeterminate: false,
      checked: checked
    })
    .siblings('label')
    .removeClass('custom-checked custom-unchecked custom-indeterminate')
    .addClass(checked ? 'custom-checked' : 'custom-unchecked');

  this.checkSiblings(container, checked);
}

  checkSiblings($el, checked) {
    var parent = $el.parent().parent(),
      all = true,
      indeterminate = false;

    $el.siblings().each(function () {
      return all = ($(this).children('input[type="checkbox"]').prop("checked") === checked);
    });

    if (all && checked) {
      parent.children('input[type="checkbox"]')
        .prop({
          indeterminate: false,
          checked: checked
        })
        .siblings('label')
        .removeClass('custom-checked custom-unchecked custom-indeterminate')
        .addClass(checked ? 'custom-checked' : 'custom-unchecked');

      this.checkSiblings(parent, checked);
    }
    else if (all && !checked) {
      indeterminate = parent.find('input[type="checkbox"]:checked').length > 0;

      parent.children('input[type="checkbox"]')
        .prop("checked", checked)
        .prop("indeterminate", indeterminate)
        .siblings('label')
        .removeClass('custom-checked custom-unchecked custom-indeterminate')
        .addClass(indeterminate ? 'custom-indeterminate' : (checked ? 'custom-checked' : 'custom-unchecked'));

      this.checkSiblings(parent, checked);
    }else {
      $el.parents("li").children('input[type="checkbox"]')
        .prop({
          indeterminate: true,
          checked: false
        })
        .siblings('label')
        .removeClass('custom-checked custom-unchecked custom-indeterminate')
        .addClass('custom-indeterminate');
    }
  }
  callAccordion(roleID, eve, list) {

    if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles'){
      console.log("-->staffroles/partnerroles");
      this.membersLoaded =false;
      this.isStaffsloadingTime = true;
      this.clickedRole=roleID;
      console.log(this.newMemberListByRoleWise, 'new members');
    }
    if(this.optionShow == 'msggroups' || this.optionShow ==='groups') {
      console.log("-->msggroups/groups");
      this.membersLoaded =false;
      this.isGrouploadingTime = true;
      this.clickedGroup=roleID;
    }
    if ($(".expand-icon-" + roleID).hasClass("fa-plus")) {
      console.log("-->expand-icon"); 
      if (this.expandedRoles.length>0) {
        this.expandedRoles.map((elem, i) => {
          if( elem != roleID) {
            this.expandedRoles.push(roleID)
          }
        })
      } else {
        this.expandedRoles.push(roleID); 
      }
      if(list == "selectedRoles"){
        this.isStaffsloadingTime = true;
				this.getRoleBasedStaff(roleID,eve,true,list);
				this.clickedRole=roleID;
			} else if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles'){
        this.getRoleBasedStaff(roleID,eve,true);
      } else if(this.optionShow == 'msggroups' || this.optionShow ==='groups'){
        const groupType =  this.optionShow === 'groups' ? GroupType.PATIENTGROUP : GroupType.MSGGROUP;
        this.getGroupMembersByGroupId(roleID, true, groupType);
      }   
      $(".expand-icon-" + roleID).removeClass("fa-plus");
      $(".expand-icon-" + roleID).addClass("fa-minus");
      $(".sub-item-panel-" + roleID).addClass("showall");
    } else {
      console.log("-->fa-minus");
      this.isStaffsloadingTime = false;
      this.isGrouploadingTime = false;
      this.usersInRole = true;
      this.membersLoaded = true;

      $(".expand-icon-" + roleID).removeClass("fa-minus");
      $(".expand-icon-" + roleID).addClass("fa-plus");
      $(".sub-item-panel-" + roleID).removeClass("showall");
    }
  }

  searchOnKeyPress(event){
    var search_key_word=$("#userSearchTxtRoles").val().trim().toLowerCase();
    var key = event.keyCode || event.charCode;
    if( key == 8 || key == 46 ){
      
          $("li.roleslisting").filter(function() {
              $(this).toggle($(this).find("label").text().toLowerCase().indexOf(search_key_word) > -1);
              
            });
            if($("li.roleslisting").find("label").text().toLowerCase().indexOf(search_key_word)==-1) $("#notFoundRoles").show();
              else $("#notFoundRoles").hide();
    }else{
      $("li.roleslisting").filter(function() {
          $(this).toggle($(this).find("label").text().toLowerCase().indexOf(search_key_word) > -1)
          
        });
        if($("li.roleslisting").find("label").text().toLowerCase().indexOf(search_key_word)==-1) $("#notFoundRoles").show();
        else $("#notFoundRoles").hide();
        
    }
  }

  getRoleBasedStaff(roleId,eve,flagmembersappend=false, list = ''){
    this.usersInRole=true;
    let chosenSiteId;
    const roleList = list === 'selectedRoles' ? this.checkedRoleIds : this.newMemberListByRoleWise;

        if (!('userList' in roleList[eve])) {
          roleList[eve]['userList'] = [];
        }
           this.membersLoaded =false;
           chosenSiteId = this.multiTenant ? this.selectedBranchId : (this.selectSiteId == undefined || this.selectSiteId == 0 || isBlank(this.selectSiteId)) ? this.editedSite : this.selectSiteId;
           this._structureService.getRoleBasedStaffs(roleId, 0,1,false,this.staffTenantFilter.selectedTenant, chosenSiteId).then(( data ) => {
          this.membersLoaded =true;

          let parsedResponceData = JSON.parse(JSON.stringify(data));
          this.roleBasedStaffs = parsedResponceData.getSessionTenant['roleBasedStaffs'];
          if(this.roleBasedStaffs == null){
            this.roleBasedStaffs = [];
            this.usersInRole=false;
          }
          if(this.configData.enable_nursing_agencies_visibility_restrictions == '1' && this.userData.nursing_agencies != '' && this.configData.na_staffs_chat_with_pharmacy == '0'){
            var nur_login = this.userData.nursing_agencies.split(',');
    
            nur_login.forEach(element => {                
                      this.roleBasedStaffs = this.roleBasedStaffs.filter(function (members) {
                        if(members.naTags != null && members.naTags != '' && members.naTags.split(',').indexOf(element) != -1){
                           return true;                   
                           }
                       
                      });           
                      
              });
              if(this.roleBasedStaffs.length == 0){
                this.usersInRole=false;
              }
       
        } else if(this.configData.enable_nursing_agencies_visibility_restrictions == '1' && this.userData.nursing_agencies != '' && this.configData.na_staffs_chat_with_pharmacy == '1'){
          var nur_login = this.userData.nursing_agencies.split(',');
           nur_login.forEach(element => {                
            this.roleBasedStaffs = this.roleBasedStaffs.filter(function (members) {
              if(members.naTags == null || members.naTags == '' || members.naTags.split(',').indexOf(element) != -1){
                 return true;                   
                 }else{
                   return false;
                 }
             
            });           
            
        });
        }
          this.roleBasedStaffs.forEach(value => {  
            value.selectedRoleid = roleId; 
          });
          if(roleList[eve]['userList'].length == 0){
          this.roleBasedStaffs.forEach(value => {  
            value.selectedRoleid = roleId; 
            var user = { id: value.id, name: value.displayName, selectedRoleid:value.selectedRoleid, naTags:value.naTags,naTagNames:value.naTagNames };
            if(!flagmembersappend){
             // if (this.checkedIds.indexOf(value.id) === -1) this.checkedIds.push(value.id);  
              }
                roleList[eve]['userList'].push(user);        
          });          
        } else {
          roleList[eve]['userList'] = [];
          this.roleBasedStaffs.forEach(value => {  
            value.selectedRoleid = roleId; 
            var user = { id: value.id, name: value.displayName, selectedRoleid:value.selectedRoleid, naTags:value.naTags,naTagNames:value.naTagNames };
            if(!flagmembersappend){
             // if (this.checkedIds.indexOf(value.id) === -1) this.checkedIds.push(value.id);  
              }
                roleList[eve]['userList'].push(user);  
          });    
        }

        if(!flagmembersappend){           
          this.selectedroleBasedStaffs = [...this.selectedroleBasedStaffs, ...this.roleBasedStaffs];
        }
        if(list === 'selectedRoles'){
          this.checkedRoleIds = roleList;
        } else {
          this.newMemberListByRoleWise = roleList;
        }
        console.log(this.selectedroleBasedStaffs.length+"RRRRRRolE")
        this.isStaffsloadingTime = false;

        
        });   
    
      }
      getGroupMembersByGroupId(groupId, choose = false, groupType) {
        const groupIds = typeof(groupId) === 'string' ? [+groupId] : groupId;
        let parameter : GroupsListingRequest = {
          data: {
            groupIds: groupIds,
          }
        };
        if(groupType === GroupType.PATIENTGROUP) {
          const data: any = { patients: groupIds };
          parameter = {
            data
          };
        }
        parameter['siteIds'] = this.getSelectedSiteIds();
        this.messageService.fetchGroupData(parameter, groupType).subscribe((messageGroups) => {
          let groupMembers = messageGroups.data["memberDetails"];
          const groupRoles = messageGroups.data["selectedRoles"] || [];
          if(groupMembers && groupMembers.length){
            groupMembers.sort((a, b) => {
                if (a.displayName.toLowerCase() < b.displayName.toLowerCase()) return -1;
                if (a.displayName.toLowerCase() > b.displayName.toLowerCase()) return 1;
                return 0;
            });
          }
          if(!choose) {
            this.selectedGroupRoles.push(...groupRoles);
            const selectedRoleIds = [...this.selectedGroupRoles.map(x=> (+x.roleId)), ...this.checkedRoleIds.map(x=> +(x.id))];
            let memberDetails = groupMembers.filter(obj => {
              const roles = JSON.parse(obj.roleIds) as number[];
              return !roles.some(role => selectedRoleIds.includes(+role));
            });
            if(groupMembers.length !== memberDetails.length) {
              this._structureService.notifyMessage({
                messge: this._ToolTipService.getTranslateData('WARNING.MEMBER_WITH_SAME_ROLE_EXISTS'),
                type: CONSTANTS.notificationTypes.warning
              });
            }
            this.selectedGroupMembers.push(...memberDetails);
          }
          this.isGrouploadingTime = false;
          if ((groupMembers && groupMembers.length) || (groupRoles && groupRoles.length)) {
            if (typeof groupId === "object" && groupId && groupId.length) {
              this.addMemberToList(groupId);
            } else {
              let selectedMessageGroupIndex = this.memberDataGroupWise.findIndex(group => group.groupId === String(groupId));
              if(groupType === GroupType.PATIENTGROUP) {
                selectedMessageGroupIndex = this.memberDataGroupWise.findIndex(group => group.patientId === String(groupId));
              }
              this.memberDataGroupWise[selectedMessageGroupIndex].userList = groupMembers;
              this.memberDataGroupWise[selectedMessageGroupIndex].selectedRoles = groupRoles;
            }
            this.membersLoaded = true;
          }
        });      
      }
      showData(data) {
        this.optionShow = data;
        this.dummyCheckedGroupMembers=[];
        this.searchText = this.searchTexts =  '';
        if(data=='groups' || data=='msggroups') {
          this.reset(this.optionShow);
        }
        if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
          this.chatWithLoader.otherTenantstaff = true;
    
          this.setChatWithTenantFilterSelect();
          // this.staffTenantFilter.filterEnabled = true;
          this.getStaffRoles(this.optionShow);
      } 
      }

      getStaffRoles(optionShow='') { 
        
        var selectedtenant = this.staffTenantFilter.selectedTenant ? this.staffTenantFilter.selectedTenant :this.userData.tenantId;
        var selectedTenantName = this.staffTenantFilter.selectedTenantName ? this.staffTenantFilter.selectedTenantName :this.userData.tenantName;
        this.staffList = [];
       
      this._inboxService.getStaffRolesListByTenantId(selectedtenant,optionShow,this.selectedBranchId).then((data)=> {
          if (data) {
            this.isLoadingRoles = false;

            //this.groupList = data['getSessionTenant'].messageGroupsPagination.data;
    
           // this.groupListNames = this.groupList;
           this.staffRolesList = Array.isArray(data)?data:[];
           this.staffRolesList.forEach(value => {
            var staffrollData = { id: value.RoleID, displayName: value.RoleName, tenantId:selectedtenant, tenantName:value.TenantName, citusRoleId:value.citus_role_id};
            this.staffList.push(staffrollData);
           });
            this.staffList.slice();       
            this.newMemberListByRoleWise = [];       
            this.newMemberList = [];     
            console.log('this.staffList',this.staffList); 
            if(this.staffList.length > 0){
              this.staffList.forEach(value => {
                if (value.id) {
                  this.newMember = {
                    id: "",
                    displayName: "",
                    role: {},
                    tenantId: null,
                    tenantName: "",
                    otherTenantUser: ((value.tenantId && (this.userData.tenantId != value.tenantId)) ? true : false)
                  }
                  
                  console.log('this.staffList',this.staffList); 
                  this.newMember.tenantId = value.tenantId;
                  this.newMember.tenantName = value.tenantName;              
                  var roleData = { id: value.id, name: value.displayName, tenantId:value.tenantId, tenantName:value.tenantName, tenantRoleId:value.tenantId, citusRoleId:value.citusRoleId};            
                  console.log('this.roleData',roleData); 
                  
                  if (!this.newMemberListByRoleWise[value.id]) {
                    this.newMemberListByRoleWise[value.id] = {};
                  }
                  this.newMemberListByRoleWise[value.id]['roleData'] = roleData;
                  this.newMemberListByRoleWise[value.id]['tenantId'] = value.tenantId;
                  this.newMemberListByRoleWise[value.id]['tenantName'] = value.tenantName;              
                }
              });
            }  
           console.log('this.newMemberListByRoleWise',this.newMemberListByRoleWise);
            this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter( function(a) {
              return (a && a.tenantId);
            });
            if(optionShow == 'staffroles'){
              this.MemberListByRoleWiseStaff =this.newMemberListByRoleWise;
            } else if(optionShow == 'partnerroles'){
              this.MemberListByRoleWisepartner =this.newMemberListByRoleWise;
            }
            if(this.checkedRoleIds.length > 0){
              this.checkedRoleIds.forEach(value => {                     
                  if (value.id) {
                    this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter(function (members) {
                      return members.roleData.id != value.id;
                      });    
                  }
                });
              }
              // if(optionShow == 'staffroles'){
              //   this.MemberListByRoleWiseStaff =this.newMemberListByRoleWise;
              // } else if(optionShow == 'partnerroles'){
              //   this.MemberListByRoleWisepartner =this.newMemberListByRoleWise;
              // }

            this.newMemberListByRoleWise.sort(function (a, b) {
              if (a.roleData.name < b.roleData.name) return -1;
              if (a.roleData.name > b.roleData.name) return 1;
              return 0;
            });       
            this.chatWithLoader.otherTenantstaff = false;
    
          } else {
            this.chatWithLoader.otherTenantstaff = false;
    
            this._structureService.deleteCookie('authenticationToken');
            this.router.navigate(['/login']);
          }
        });
      }
  removeDuplicates( arr, prop ) {
    let obj = {};
    console.log("arrarrarrarrarrarrarr",arr);
    return Object.keys(arr.reduce((prev, next) => {
       // console.log("objobjobjobjobjobjobjobjobjobj",obj,!obj,prev,next,prop,!obj[next],!obj[next[prop]]);
      if(next && !obj[next[prop]]) obj[next[prop]] = next; 
      return obj;
    }, obj)).map((i) => obj[i]);
  }

  initGroups(){
    this.offset = 0;
    this.allMessageGroups = [];
    this.memberDataGroupWise = [];
    this.hideLoadMore = false;
    this.prevText = "";
}

reset(optionShow ='groups') {
  this.srch.nativeElement.value = "";
  if(optionShow == 'groups' || optionShow == 'msggroups') {
    this.initGroups();    
    this.searchTexts = "";
    this.srch.nativeElement.value = "";
    this.getAllMessageGroupDetails("",optionShow);
  }else if(optionShow == 'staff' || optionShow == 'partner' ) { 
    this.loadMoreSearchValue = undefined;
    this.callFromInitialCountChatWithUserlist = true;
    this.chatwithPageCount = {
        staffs: 0,
        partner: 0,
    }
    this.noMoreItemsAvailable = {
        users: false
    };
    this.staffUserList = [];
    this.clinicalUserDetails = [];
    this.usersList = [];
    this.loadMoreUsers(optionShow)
}
}

 search(optionShow = 'groups') {
  if(optionShow === 'msggroups') {
    this.initGroups();
    if(this.srch.nativeElement.value.trim() || this.selectSiteId.toString() !== '0' || this.multiTenant){
      this.getAllMessageGroupDetails(this.srch.nativeElement.value.trim(),optionShow);    
    }
    }
 if(this.optionShow === 'staffroles' || this.optionShow === 'partnerroles') {
   this.getStaffRoles(this.optionShow);
   } else if(optionShow === 'staff' || optionShow === 'partner') {
    this.callFromInitialCountChatWithUserlist = true;
    this.chatwithPageCount = {
    staffs: 0,
    partner:0
   }
    this.noMoreItemsAvailable = {
    users: false
   };
    this.staffUserList = [];
    this.clinicalUserDetails = [];
    this.usersList = [];
    this.loadMoreUsers(optionShow, this.srch.nativeElement.value.trim())
}  
}

  loadMoreGroups(optionShow='') {
    if(!this.prevText) {
      if(this.srch.nativeElement.value.trim()){
        this.search();      
      } else {
        this.offset = this.offset+25;
        this.getAllMessageGroupDetails('',optionShow);
      }
    } else if(this.prevText && !this.srch.nativeElement.value.trim()) {
      this.reset(optionShow);
    } else if(this.prevText == this.srch.nativeElement.value.trim()) {
      this.offset = this.offset+25;
      this.getAllMessageGroupDetails(this.srch.nativeElement.value.trim(),optionShow);
    } else {
      this.search();
    }
  }
  getSelectedSiteIds(){
    if (this.multiSiteEnable && !isBlank(this.messageSiteId) && +this.messageSiteId !== 0) {
      return typeof this.messageSiteId === 'string' ? this.messageSiteId.split(',').map((num) => {return +num}) : [this.messageSiteId];
    } else {
      return [this.userData.mySites[0].id];
    }
  }
  getAllMessageGroupDetails(searchKeyword="",optionShow="") {
    let groupType = '';
    if(optionShow == 'groups') {
      this.ispdgs = '1';
      groupType = GroupType.PATIENTGROUP;
    } else if(optionShow == 'msggroups') {
      this.ispdgs = '0';
      groupType = GroupType.MSGGROUP;
    }
    this.loadingGroups = true;
    this.MessageGroupWithLoader.groups = true;
    const page = this.offset/this.limit;
    let parameter : GroupsListingRequest = {
      data: {
        pagination: {
            page: page > 0  ? page + 1 : 1,
            limit: this.limit
        }
      }
    };
    if(!isBlank(searchKeyword)) {
      parameter['data'].filter = {
        search : searchKeyword,
        
      };
    }
    parameter['siteIds'] = this.getSelectedSiteIds();
    this.messageService.fetchGroupData(parameter, groupType).subscribe((messageGroups) => {
      if(messageGroups.success){
        this.memberDataGroupWise = groupType === 'messageGroup' ? messageGroups.data['messageGroups'] : messageGroups.data['patientGroups'];
        if(searchKeyword) {
          this.prevText = searchKeyword;
        }
        this.loadingGroups = false;
        if(isBlank(this.memberDataGroupWise)) {
          this.hideLoadMore = true;
        } else {
          if(this.memberDataGroupWise.length === 25) {
            this.hideLoadMore = false;
          } else {
            this.hideLoadMore = true;
          }
          this.allMessageGroups = [...this.allMessageGroups, ...this.memberDataGroupWise];
        }
        this.messageGroups = this.allMessageGroups;
        this.newMessageGroups = this.allMessageGroups;
        this.newallUserDetailsInMsgGroup = this.allUserDetailsInMsgGroup;    
        this.memberDataGroupWise = this.allMessageGroups;
        this.memberDataGroupWise.sort(function (a, b) {
          if (a.groupName < b.groupName) return -1;
            if (a.groupName > b.groupName) return 1;
              return 0;
        });
        this.MessageGroupWithLoader.groups = false;
      } else {
        this.loadingGroups = false;
      }
    });
  }
  getGroups(refresh) {
    let isRoleAvailable = true;
    let clinicianRolesAvaiable = null;
    if (this.userData.group == 3) {
      if (this._inboxService.checkInfusionHours(false).isWorkingHours) {
        if (this.configData.clinician_roles_on_working_hours) {
          clinicianRolesAvaiable = this.configData.clinician_roles_on_working_hours;
        } else {
          isRoleAvailable = false;
        }
      } else {
        if (this.configData.clinician_roles_beyond_working_hours) {
          clinicianRolesAvaiable = this.configData.clinician_roles_beyond_working_hours;
        } else {
          isRoleAvailable = false;
        }
      }
    }
    let members;
    if(this.selectedGroupDetails && this.selectedGroupDetails.memberIds != undefined)
    {
      members = this.selectedGroupDetails.memberIds;       
    } else {
      members = '';
    }
    this.selectSiteId = (this.selectSiteId == undefined || this.selectSiteId == 0 || isBlank(this.selectSiteId)) ? this.editedSite : this.selectSiteId;
    let parameter : GroupsListingRequest = {
      data: { 
        groupId: Number(this._structureService.getCookie('message-grp-id'))
      }
    };
    this.messageService.fetchGroupData(parameter, GroupType.MSGGROUP).subscribe((response) => {
      if(response.success) {
        if(this.optionShow == 'staff' || this.optionShow == 'partner'){
          if(response.data.sites) {
            const siteIds = JSON.parse(response.data.sites);
            this.editedSite = siteIds.map(x=> x.id);
          }
          this.staffUserList = [];
          this.setChatWithTenantFilterSelect();
          this.messageGroupMemberIds = response.data.memberDetails.map((x=> x.id)).toString(',');
          this.loadMoreUsers(this.optionShow, this.srch.nativeElement.value.trim());
        }
        this.setGroupList(response.data);
        this.disableButton = false;
        this.addMemberError = false;
        this.checkedIds = [];
      }
    });   
  }
  noWhitespaceValidator(control: FormControl) {
    let isWhitespace = (control.value || '').trim().length === 0;
    let isValid = !isWhitespace;
    return isValid ? null : { 'whitespace': true }
  }

  setGroupList(group){
    this.selected = group;
    if(this.userData.config.enable_nursing_agencies_visibility_restrictions == 1 && this.userData.nursing_agencies && this.userData.nursing_agencies != "") {
			if(group.createdBy != this.userData.userId) {
				this.canEdit = false;
			}
		}
    this.allowSave = ((this.selected.createdBy||this.selected.createdUser.id )!= this.userData.userId) ? false : true;
    this.messageGroup.reset();
    this.selectedGroupMembers = [];
    this.selectedGroupDetails = this.selected;
    this.selectedisPublic = (this.selected.isPublic === "1") ? true : false;
    this.selectedAllowMultiThread = (this.selected.allowMultiThreadChat === "1") ? true : false;
    if (this.selected.name) {
      this.messageGroup.patchValue({
        messageGroupName: this.selected.name,
        nursingAgencyUserTag: this.selected.tagId ? this.selected.tagId:"0"
      });
    }
    if (this.selected.memberDetails) {
      this.selected.memberDetails.forEach(element1 => {
        console.log("element===========> ",element1);
        this.newMember = {
          id: element1.id,
          displayName: element1.displayName,
          role: {},
          tenantId: element1.tenantId,
          tenantName: element1.tenantName,
          otherTenantStaff: ((element1.tenantId && (this.userData.tenantId != element1.tenantId )) ? true : false), 
          naTags: "", 
          naTagNames: "",
          citusRoleId:"",
          status: element1.status,
          roleIds: element1.roleIds
        }
        if (element1.naTags && element1.naTags != null && element1.naTags != 'null') {
            this.newMember.naTags = element1.naTags;
            this.newMember.naTagNames = element1.naTagNames;
        }
        this.selectedGroupMembers.push(this.newMember);
      });
    }
    if (this.selected.selectedRoles) {
      this.selected.selectedRoles.forEach((role)=>{
        let roleIndex = this.checkedRoleIds.findIndex(r => r.id === role.roleId);
        if(roleIndex < 0) {
          this.checkedRoleIds.push({id: role.roleId, name: role.name, tenantId: role.tenant_id});
          this.removeRoleStaffs({id: role.roleId, name: role.name, tenantId: role.tenant_id});
        }
      });
    }
    this.disableSubmit();
  }

  

  setEditcheckedRoleIds(role){
		if(this.selected.selectedRoles&& this.selected.selectedRoles.length >0){
			if(this.selected.selectedRoles.indexOf(String(role.id))>-1){
				var exist =false;
				this.checkedRoleIds.forEach((value)=>{
					if(value.id == role.id){
						exist = true;
					}
				});		
				if(!exist){
          this.checkedRoleIds.push(role);
          this.removeRoleStaffs(role);
				}		
			}			
		}
	}

  refreshPage() {
    this.getGroups(true);
  }

  selectGroup(group) {
    this.selected = group;

  }
  clearMessageGroupSelection(){
    this.checkedIds = [];
    $('input[type="checkbox"]').prop({
      indeterminate: false,
      checked: false
    }).siblings('label')
    .removeClass('custom-checked custom-unchecked custom-indeterminate')
    .addClass('custom-unchecked');
  }
  messageGroupSelectionAll()
  {
    $('input[type="checkbox"]').prop({
      indeterminate: true,
      checked: true
    }).siblings('label')
      .removeClass('custom-checked custom-unchecked custom-indeterminate')
      .addClass('custom-checked');
  }
  // updateCheckedOptions(id, event) {
    
  //   if (event.target.checked) {
  //     this.addMemberError=false;
  //     this.disableButton = false;
  //     //this.noMemberError = false;
  //     this.checkedIds.push(id);
  //   } else {
  //     /*var index = this.checkedIds.indexOf(id);
  //     if (index > -1) {
  //         this.checkedIds.splice(index, 1);
  //     }*/
  //     this.checkedIds = this.checkedIds.filter(function (ids) {
  //       return ids != id;
  //     });

  //   }


  // }

  removeMember(id) {
          var self = this;
          var i = 0;
          this.selectedGroupMembers = this.selectedGroupMembers.filter(function (members) {   
            if (members.id != id) {
              return true;
            } else {
              self.selectedGroupMembers = self.selectedGroupMembers.filter(function (users) {
                return users.id != String(members.id);
              });
              self.dummyCheckedGroupMembers = self.selectedGroupMemberIds.filter(function (id) {
                return id != members.id;
              });
              var memberIds = self.selectedGroupDetails.memberDetails.map(x=> x.id);
              memberIds = memberIds.filter(memberId => memberId!= members.id);
              memberIds = memberIds.join();
              self.selectedGroupDetails.memberIds = memberIds;
              
              self.messageGroupMemberIds = self.selectedGroupDetails.memberIds;
          if(self.checkedRoleIds.length>0){
            
            var user = self.clinicalUserDetails.find((user) => user.userid == members.id);
             if (user && user.userid) {
              self.staffUserList.push(user);
              self.staffUserList = self.staffUserList.slice();
            }                     
            self.staffUserList.sort(function (a, b) {              
              if (a.displayname.toLowerCase() < b.displayname.toLowerCase()) return -1;
              if (a.displayname.toLowerCase() > b.displayname.toLowerCase()) return 1;
              return 0;
            });
            

            if( members.dualRoles && typeof members.dualRoles == "string"){
              members.dualRoles = JSON.parse(members.dualRoles);
            }
            self.checkedRoleIds.forEach((value)=>{
              if(members.role && members.role.id && value.id==members.role.id){
                self.removedRoleUsers.push({id:id,roleId:value.id});
              }else if(members.dualRoles && members.dualRoles.length>1){
                members.dualRoles.forEach((dual)=>{
                  if(dual.tenantUsersRoleId == value.id){
                    self.removedRoleUsers.push({id:id,roleId:value.id});
                  }
                });
              } 
              
            });
            
          }
          if(self.checkedIdsWithRole && self.checkedIdsWithRole[id]){
            delete self.checkedIdsWithRole[id];
          }

             
              if (typeof(members) == 'object' && members.hasOwnProperty("otherTenantStaff") && !members.otherTenantStaff) {
               if(self.checkedRoleIds.length == 0){

                var user = self.clinicalUserDetails.find((user) => user.userid == members.id);
                if (user && user.userid) {
                  self.staffUserList.push(user);
                  self.staffUserList = self.staffUserList.slice();
                }                     
                self.staffUserList.sort(function (a, b) {              
                  if (a.displayname.toLowerCase() < b.displayname.toLowerCase()) return -1;
                  if (a.displayname.toLowerCase() > b.displayname.toLowerCase()) return 1;
                  return 0;
                });
              }
                var user = self.memberList.find((user)=>user.id==members.id);
                if(user && user.id) {
                  self.newMemberList.push(user);
                  self.newMemberList = self.newMemberList.slice();
                }

                self.newMemberList.forEach(value => {
                  if (value.role && value.role.id) {
                    var user = { id: value.id, name: value.displayName, naTags:"", naTagNames: ""};
                    if (value.naTags && value.naTags != null && value.naTags != 'null') {
	              user.naTags = value.naTags;
	              user.naTagNames = value.naTagNames;
	            }
                    var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id };
                    if (!self.newMemberListByRoleWise[value.role.id]) {
                      self.newMemberListByRoleWise[value.role.id] = {};
                    }
                    if (!('userList' in self.newMemberListByRoleWise[value.role.id])) {
                      self.newMemberListByRoleWise[value.role.id]['userList'] = [];
                    }
                    if (!('roleData' in self.newMemberListByRoleWise[value.role.id])) {
                      self.newMemberListByRoleWise[value.role.id]['roleData'] = {};
                    }
                    if(value.dualRoles && value.dualRoles.length>1){
                      value.dualRoles.forEach(dualEach => {
                        console.log("dualEach======================");
                        console.log(dualEach);
                        if(value.role.id !=dualEach.tenantUsersRoleId){
                          if (!self.newMemberListByRoleWise[dualEach.tenantUsersRoleId]) {
                            self.newMemberListByRoleWise[dualEach.tenantUsersRoleId] = {};                    
                          }
                          if (!('userList' in self.newMemberListByRoleWise[dualEach.tenantUsersRoleId])) {
                            self.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'] = [];
                          }
                          self.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'].push(user);
                        }
                      })
                    }
                 //   self.newMemberListByRoleWise[value.role.id]['roleData'] = roleData;
                    self.newMemberListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
                    self.newMemberListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
                    self.newMemberListByRoleWise[value.role.id]['userList'].push(user);
                  }
                });
                self.newMemberListByRoleWise = self.newMemberListByRoleWise.filter( function(a) {
                  return (a && a.tenantId);
                });
                self.newMemberListByRoleWise.sort(function (a, b) {
                  if (a.roleData.name < b.roleData.name) return -1;
                  if (a.roleData.name > b.roleData.name) return 1;
                  return 0;
                });
                
              } else if(typeof(members) == 'object' && members.hasOwnProperty("otherTenantStaff") && members.otherTenantStaff) {
                console.log("removeMember Enter second iffffffff")
                var user = self.otherTenantMemberList.find((user)=>user.id==members.id);
                if(user.id) {
                  self.newMemberOtherTenantList.push(user);
                  self.newMemberOtherTenantList = self.newMemberOtherTenantList.slice();
                }
                self.newMemberOtherTenantListByRoleWise = [];
                self.newMemberOtherTenantList.forEach(value => {
                  if (value.role && value.role.id) {
                    var user = { id: value.id, name: value.displayName };
                    var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id };
                    if (!self.newMemberOtherTenantListByRoleWise[value.role.id]) {
                      self.newMemberOtherTenantListByRoleWise[value.role.id] = {};
                    }
                    if (!('userList' in self.newMemberOtherTenantListByRoleWise[value.role.id])) {
                      self.newMemberOtherTenantListByRoleWise[value.role.id]['userList'] = [];
                    }
                    if (!('roleData' in self.newMemberOtherTenantListByRoleWise[value.role.id])) {
                      self.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = {};
                    }
                    if(value.dualRoles && value.dualRoles.length>1){
                      console.log("Enter dual Role condition=============================================");
                      value.dualRoles.forEach(dualEach => {
                        console.log("dualEach======================");
                        console.log(dualEach);
                        if(value.role.id !=dualEach.tenantUsersRoleId){
                          if (!self.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]) {
                            self.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId] = {};                    
                          }
                          if (!('userList' in self.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId])) {
                            self.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]['userList'] = [];
                          }
                          self.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]['userList'].push(user);
                        }
                      })
                    }
                    self.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = roleData;
                    self.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
                    self.newMemberOtherTenantListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
                    self.newMemberOtherTenantListByRoleWise[value.role.id]['userList'].push(user);
                  }
                });
                self.newMemberOtherTenantListByRoleWise = self.newMemberOtherTenantListByRoleWise.filter( function(a) {
                  return (a && a.tenantId);
                });
                self.newMemberOtherTenantListByRoleWise.sort(function (a, b) {
                  if (a.roleData.name < b.roleData.name) return -1;
                  if (a.roleData.name > b.roleData.name) return 1;
                  return 0;
                });
              }else {
                // var user = self.clinicalUserDetails.find((user) => user.userid == members.id);
                // console.log(user);
                // if (user && user.userid) {
                //   self.staffUserList.push(user);
                //   self.staffUserList = self.staffUserList.slice();
                // }                     
                // self.staffUserList.sort(function (a, b) {              
                //   if (a.displayname.toLowerCase() < b.displayname.toLowerCase()) return -1;
                //   if (a.displayname.toLowerCase() > b.displayname.toLowerCase()) return 1;
                //   return 0;
                // });
                
                console.log("remove elseeeeeeeeeee");
                // var newGrpoupMember = {
                //   id: "",
                //   name: "",
                //   role: {},
                //   tenantId: null,
                //   tenantName: ""
                // };
                // newGrpoupMember.id = members.id;
                // newGrpoupMember.name = members.displayName;
                // newGrpoupMember.role = members.role;
                // newGrpoupMember.tenantId = members.tenantId;
                // newGrpoupMember.tenantName = members.tenantName;
                // self.newallUserDetailsInMsgGroup.push(newGrpoupMember);
                let setCurrentTenant = false;
                let setOtherTenant = false;
                // self.memberDataGroupWise = [];
                if (self.newallUserDetailsInMsgGroup.length) {
                  self.memberDataGroupWise = [];
                  console.log(self.newMessageGroups);
                  self.newMessageGroups.forEach(value => {
                    self.memberDataGroupWise[value.id] = {};
                    self.memberDataGroupWise[value.id]['groupData'] = { id: value.id, name: value.name };
                    self.memberDataGroupWise[value.id]['userList'] = [];
                    self.newallUserDetailsInMsgGroup.forEach((user, key) => {
                      if (user.id != self.userData.userId) {
                        if (value.memberIds.split(',').indexOf(user.id + '') > -1) {
                          if(self.selectedGroupDetails.memberIds.split(',').indexOf(user.id + '') == -1) {
                            var role = { id: user.id, name: user.name, tenantId: user.tenantId, tenantName: user.tenantName };
                            self.memberDataGroupWise[value.id]['userList'].push(role);
                          }
                        }
                      }
                    });
                  });
                  self.memberDataGroupWise = self.memberDataGroupWise.filter(function (item) {
                    /**For remove group from list when there is no users in that group */
                    if (item.userList.length != 0) {
                      return true;
                    } else {
                      return false;
                    }
          
                  });
                  self.memberDataGroupWise.sort(function (a, b) {
                    if (a.groupData.name.toLowerCase() < b.groupData.name.toLowerCase()) return -1;
                    if (a.groupData.name.toLowerCase() > b.groupData.name.toLowerCase()) return 1;
                    return 0;
                  });
                  self.newallUserDetailsInMsgGroup.forEach(element => {
                    if(element.id == id) {
                      console.log(element.tenantId,self.userData.tenantId)
                      if(element.tenantId == self.selectedTenant) {
                        setCurrentTenant = true
                        console.log('---current tenant---')
                        console.log(members);
                        var user = self.memberList.find((user)=>user.id==members.id);
                        if(user) {
                          self.newMemberList.push(user);
                          self.newMemberList = self.newMemberList.slice();           
                        }
                      } 
                      // else {
                      //   setOtherTenant = true;
                      //   console.log('---other tenant---')
                      //   console.log(members);
                      //   var user = self.otherTenantMemberList.find((user)=>user.id==members.id);
                      //   if(user) {
                      //     self.newMemberOtherTenantList.push(user);
                      //     self.newMemberOtherTenantList = self.newMemberOtherTenantList.slice();
                      //   }
                      // }
                    }                
                  })
                  if(setCurrentTenant) {
                   // self.newMemberListByRoleWise = [];
                    self.newMemberList.forEach(value => {
                      if (value.role && value.role.id) {
                        var user = { id: value.id, name: value.displayName };
                        var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id };        
                        if (!self.newMemberListByRoleWise[value.role.id]) {
                          self.newMemberListByRoleWise[value.role.id] = {};
                        }
                        if (!('userList' in self.newMemberListByRoleWise[value.role.id])) {
                          self.newMemberListByRoleWise[value.role.id]['userList'] = [];
                        }
                        if (!('roleData' in self.newMemberListByRoleWise[value.role.id])) {
                          self.newMemberListByRoleWise[value.role.id]['roleData'] = {};
                        }
                        if(value.dualRoles && value.dualRoles.length>1){
                          console.log("Enter dual Role condition=============================================");
                          value.dualRoles.forEach(dualEach => {
                            console.log("dualEach======================");
                            console.log(dualEach);
                            if(value.role.id !=dualEach.tenantUsersRoleId){
                              if (!self.newMemberListByRoleWise[dualEach.tenantUsersRoleId]) {
                                self.newMemberListByRoleWise[dualEach.tenantUsersRoleId] = {};                    
                              }
                              if (!('userList' in self.newMemberListByRoleWise[dualEach.tenantUsersRoleId])) {
                                self.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'] = [];
                              }
                              self.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'].push(user);
                            }
                          })
                        }
                        self.newMemberListByRoleWise[value.role.id]['roleData'] = roleData;
                        self.newMemberListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
                        self.newMemberListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
                        self.newMemberListByRoleWise[value.role.id]['userList'].push(user);
                      }
                    });
                    self.newMemberListByRoleWise = self.newMemberListByRoleWise.filter( function(a) {
                      return (a && a.tenantId);
                    });
                    self.newMemberListByRoleWise.sort(function (a, b) {
                      if (a.roleData.name < b.roleData.name) return -1;
                      if (a.roleData.name > b.roleData.name) return 1;
                      return 0;
                    });
                  }
                  if(setOtherTenant) {
                    self.newMemberOtherTenantListByRoleWise = [];
                    self.newMemberOtherTenantList.forEach(value => {
                      if (value.role && value.role.id) {
                        var user = { id: value.id, name: value.displayName };
                        var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id };        
                        if (!self.newMemberOtherTenantListByRoleWise[value.role.id]) {
                          self.newMemberOtherTenantListByRoleWise[value.role.id] = {};
                        }
                        if (!('userList' in self.newMemberOtherTenantListByRoleWise[value.role.id])) {
                          self.newMemberOtherTenantListByRoleWise[value.role.id]['userList'] = [];
                        }
                        if (!('roleData' in self.newMemberOtherTenantListByRoleWise[value.role.id])) {
                          self.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = {};
                        }
                        if(value.dualRoles && value.dualRoles.length>1){
                          console.log("Enter dual Role condition=============================================");
                          value.dualRoles.forEach(dualEach => {
                            console.log("dualEach======================");
                            console.log(dualEach);
                            if(value.role.id !=dualEach.tenantUsersRoleId){
                              if (!self.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]) {
                                self.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId] = {};                    
                              }
                              if (!('userList' in self.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId])) {
                                self.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]['userList'] = [];
                              }
                              self.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]['userList'].push(user);
                            }
                          })
                        }
                        self.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = roleData;
                        self.newMemberOtherTenantListByRoleWise[value.role.id]['userList'].push(user);
                        self.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
                        self.newMemberOtherTenantListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
                        
                      }
                    });
                
                    self.newMemberOtherTenantListByRoleWise = self.newMemberOtherTenantListByRoleWise.filter( function(a) {
                      return (a && a.tenantId);
                    });
                    self.newMemberOtherTenantListByRoleWise.sort(function (a, b) {
                      if (a.roleData.name < b.roleData.name) return -1;
                      if (a.roleData.name > b.roleData.name) return 1;
                      return 0;
                    });
                  }
                  
                }            
              }
              
              return false;
            }
          })
          this.disableSubmit();
          
        //});
       /* if(this.optionShow=='groups' || this.optionShow=='msggroups') {
          this.reset(this.optionShow);
        }*/
      }

      removeExistingRole(roleID){
        if(this.checkedRoleIds.length){
          this.checkedRoleIds.forEach((value,key) => {
          if(value.id==roleID){
            this.checkedRoleIds.splice(key,1);
            this.removeRoleFromExistingMember(roleID,value.citusRoleId);
          }    
          })
        }
      }
      removeRoleFromExistingMember(roleId,citusRoleId){ 
        var self = this;   
        if(citusRoleId != 20){
          var MemberListByRoleWiseArr =  self.MemberListByRoleWiseStaff;
          } else if((citusRoleId == 20)){
          var MemberListByRoleWiseArr =  self.MemberListByRoleWisepartner;
         }
        if(MemberListByRoleWiseArr.length > 0){
          MemberListByRoleWiseArr.forEach((value,key) => {
            if(value.roleData.id==roleId){
              if(self.selectedGroupMembers && self.selectedGroupMembers.length > 0){  
                if( value.userList == undefined) {
                  value.userList =[]; 
                  let i=0;
                  this.testArr.forEach(element => {
                    if(element.role == roleId){
                      value.userList[i]={ id: element.id, name: element.name};
                      }
                    i++;
                  }); 
    
                }
                console.log("userList?????????????????????????",value.userList)
              if(value.userList){  
              value.userList.map(user => {            
              self.selectedGroupMembers = self.selectedGroupMembers.filter((members)=>
               { 
              if (members.id != user.id) {              
                return true;
              } else{
                
                var users = self.clinicalUserDetails.find((users) => users.userid == members.id);            
                console.log(users); 
                if (users && users.userid) {
                self.staffUserList.push(users);
                self.staffUserList = self.staffUserList.slice();
                }  
              }
              });

              self.selectedGroupMembers = self.selectedGroupMembers.filter((members)=>
              {
             console.log("memberrrrr", typeof members.seconRoleId,members.seconRoleId,typeof roleId,roleId ); 
             
               if (members.role && members.role.id != roleId && members.seconRoleId != roleId ) {              
               return true;
               } 
            });

              self.selectedroleBasedStaffs = self.selectedroleBasedStaffs.filter(function (selectedmembers) { console.log(selectedmembers.id +"!="+ user.id);
              if (selectedmembers.id != user.id) {              
                return true;
              } 
              });
              
            });
      
          } else {
            console.log(self.selectedGroupMembers); 
            
            self.selectedGroupMembers = self.selectedGroupMembers.filter((members)=>
            {
              console.log(members); 
               
             if (members.role.id != roleId) {              
               return true;
             } 
             });
             console.log(self.selectedGroupMembers); 	
            }
      
      
            }
            var memberRole = value;        
            if(this.optionShow == 'staffroles' && citusRoleId != 20){
              self.newMemberListByRoleWise.push(memberRole);
             } else if(this.optionShow == 'partnerroles' && citusRoleId == 20){
              self.newMemberListByRoleWise.push(memberRole);
             }    
                self.newMemberListByRoleWise = self.newMemberListByRoleWise.slice();                               
                self.newMemberListByRoleWise.sort(function (a, b) {              
                if (a.roleData.name.toLowerCase() < b.roleData.name.toLowerCase()) return -1;
                if (a.roleData.name.toLowerCase() > b.roleData.name.toLowerCase()) return 1;
                return 0;
               
                });
 
                this.newMemberListByRoleWise.forEach(element => {
                  if(element.roleData.id == roleId){
                   element.userList = [];
                  }
                });


                }
               }); 
          
        }
        
      
          
            self.staffUserList.sort(function (a, b) {              
            if (a.displayname.toLowerCase() < b.displayname.toLowerCase()) return -1;
            if (a.displayname.toLowerCase() > b.displayname.toLowerCase()) return 1;
            return 0;
          }); 
            self.selectedGroupMembers = self.selectedGroupMembers.slice();      
        }
  addMember() {
    
    var self = this;
    this.addMemberError=false;
    $(':checkbox:checked').each(function(i) {
      var name = this.name;
      if(name == "cliniciansRoleUser[]"){
        self.checkedIds.push($(this).val());
      }
      else if(name=="staffRoleWise"){
       
        var selectedRole =JSON.parse($(this).val());       
        console.log(this.checkedIds);
        if(self.dummyCheckedRoleIds.indexOf(selectedRole.id)>-1){
          var exist =false;
          if(self.checkedRoleIds.length){
            self.checkedRoleIds.forEach((value)=>{             
              if(value.id ==selectedRole.id){
                exist = true;
              }
            });
          }          
          if(!exist){
            self.checkedRoleIds.push(selectedRole);
            self.removeRoleStaffs(selectedRole);
           
            
          }
        }      
      } 
    });

    if (this.checkedIds.length > 0) {
      console.log(">> this.checkedIds",this.checkedIds);
      //this.disableButton = false;
      this.addMemberToList(this.checkedIds);

      if(this.optionShow=='groups' || this.optionShow=='msggroups') {
      var liLength = $('#treeview li').length;
      this.offset = 25 - liLength;
      this.getAllMessageGroupDetails('',this.optionShow);
      this.srch.nativeElement.value = "";

      }
      this.noMemberError = false;
      this.checkedIds = [];
      this.addMemberError=false;
    }else if (this.checkedRoleIds.length > 0 && this.optionShow != 'groups' && this.optionShow != 'msggroups') {
      console.log(">> this.checkedRoleIds",this.checkedRoleIds);
      console.log(this.checkedRoleIds + "CHHHHHHHHHHHHH")
      this.addMemberToList(this.checkedRoleIds);

    } else if(this.optionShow == 'groups' || this.optionShow == 'msggroups') {
      console.log("-> after add button",this.dummyCheckedGroupMembers);
        this.clearMessageGroupSelection(); 
        if(this.dummyCheckedGroupMembers && this.dummyCheckedGroupMembers.length > 0){
          this.resetPdgSelect = true;
          const groupType = this.optionShow === 'groups' ? GroupType.PATIENTGROUP : GroupType.MSGGROUP;
          this.getGroupMembersByGroupId(this.dummyCheckedGroupMembers, false, groupType);
          var liLength = $('#treeview li').length;
          this.offset = 25 - liLength;
          this.dummyCheckedGroupMembers = [];
        }
     } else {
      this.addMemberError=true;
    }
  }

  addMemberToList(checkedIds) {
    if(this.optionShow == 'staff' || this.optionShow == 'partner') {
      let staffInSelectedRole = false;
      this.clinicalUserDetails.forEach(element => {
        if (this.checkedIds.includes(element.userid)) {
          let memberRoleIds = JSON.parse(element.dualRoles).map(item => item.tenantUsersRoleId);
          if(!this.checkedRoleIds.some(item => memberRoleIds.includes(+item.id))) {
            this.newMember = {
              id: "",
              displayName: "",
              role: {},
              tenantId: null,
              tenantName: "",
              otherTenantStaff: false,
              naTags: "",
              naTagNames: "",
              roleIds: ""
            };
            this.newMember.id = element.userid;
            this.newMember.displayName = element.displayname;
            this.newMember.role = element.role;
            this.newMember.dualRoles =element.dualRoles;
            this.newMember.tenantId = element.tenantId;
            this.newMember.tenantName = element.tenantName;
            this.newMember.roleIds = JSON.parse(element.dualRoles).map(item => item.tenantUsersRoleId);

            if (element.naTags && element.naTags != null && element.naTags != 'null') {
                this.newMember.naTags = element.naTags;
                this.newMember.naTagNames = element.naTagNames;
            }
            if ( this.selectedGroupMembers.findIndex(x => x.id === element.id) === -1){
              this.selectedGroupMembers.push(this.newMember);
              this.selectedGroupMemberIds.push(this.newMember.id);
            }
            this.selectedGroupMembers = this.selectedGroupMembers.slice();
            this.selectedGroupMemberIds = this.selectedGroupMemberIds.slice();
            this.newallUserDetailsInMsgGroup = this.newallUserDetailsInMsgGroup.filter(function (members) {
              return members.userid != element.userid;
            });

            this.newMemberList = this.newMemberList.filter(function (members) {
              return members.id != element.userid;
            });

            this.staffUserList = this.staffUserList.filter(function (members) {
              return members.userid != element.userid;
            });
            this.selectedGroupMembers.sort(function (a, b) {
              if (a.displayName < b.displayName) return -1;
              if (a.displayName > b.displayName) return 1;
              return 0;
            });
          } else {
            staffInSelectedRole = true;
          }
        }
      });
      if (staffInSelectedRole) {
        this._structureService.notifyMessage({
          messge: this._ToolTipService.getTranslateData('WARNING.MEMBER_WITH_SAME_ROLE_EXISTS'),
          type: CONSTANTS.notificationTypes.warning
        });
      }
      this.newMemberListByRoleWise = [];
      this.newMemberList.forEach(value => {
        if (value.role && value.role.id) {
          var user = { id: value.id, name: value.displayName };
          var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id };
          if (!this.newMemberListByRoleWise[value.role.id]) {
            this.newMemberListByRoleWise[value.role.id] = {};
          }
          if (!('userList' in this.newMemberListByRoleWise[value.role.id])) {
            this.newMemberListByRoleWise[value.role.id]['userList'] = [];
          }
          if (!('roleData' in this.newMemberListByRoleWise[value.role.id])) {
            this.newMemberListByRoleWise[value.role.id]['roleData'] = {};
          }
          this.newMemberListByRoleWise[value.role.id]['roleData'] = roleData;
          this.newMemberListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
          this.newMemberListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
          this.newMemberListByRoleWise[value.role.id]['userList'].push(user);
        }
      });
      this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter( function(a) {
        return (a && a.tenantId);
      });
      this.newMemberListByRoleWise.sort(function (a, b) {
        if (a.roleData.name < b.roleData.name) return -1;
        if (a.roleData.name > b.roleData.name) return 1;
        return 0;
      });

      // this.memberDataGroupWise.map(group => {
      //   group.userList = group.userList.filter(user=> (this.checkedIds.indexOf(user.id) == -1));
      // });
      // this.memberDataGroupWise = this.memberDataGroupWise.filter(item=>item.userList.length != 0);
    } else if (this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
        this.staffList.forEach((element) => {
          this.checkedRoleIds.forEach((rolevalue,key) => {          
            if(rolevalue.id ==element.id){
              this.selectedroleBasedStaffs.forEach(users => {
                console.log(this.selectedGroupMembers);
                if (this.selectedGroupMembers.findIndex(x => x.id === users.id) === -1) {
                  this.newMember = {
                  id: "",
                  displayName: "",
                  role: {},
                  tenantId: null,
                  tenantName: "",
                  naTags: "",
                  naTagNames: ""
                  };            
                  this.newMember.role = element.id;           
                  this.newMember.tenantId = element.tenantId;
                  this.newMember.tenantName = element.tenantName; 
                  this.newMember.id = users.id;
                        this.newMember.displayName = users.displayName;    
                              this.newMember.naTags = users.naTags; 
                              this.newMember.naTagNames = users.naTagNames;  
                              var user = { id: this.newMember.id, name: this.newMember.displayName, role:this.newMember.role};
 
                  console.log("x.id ==users.id",users.id);
                  this.selectedGroupMembers.push(this.newMember);
                  this.selectedGroupMemberIds.push(this.newMember.id);
                  this.testArr.push(user);
                console.log("testArray......................................",this.testArr)
                }
                
                this.selectedGroupMembers = this.selectedGroupMembers.slice();
                this.selectedGroupMemberIds = this.selectedGroupMemberIds.slice();
                })
        
                
  
                console.log("newMemberListByRoleWise========before filter========>");
                console.log(this.newMemberListByRoleWise);   
                console.log(this.selectedGroupMembers);
   
          
          
              this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter(function (members) {
                return members.roleData.id != element.id;
              });
              console.log("newMemberListByRoleWise========after filter========>");
              console.log(this.newMemberListByRoleWise);
  
  
            //Delete a user if the user is added to GP members when user role is checked 
            if(this.removedRoleUsers.length>0){
            this.removedRoleUsers.forEach((value,key)=>{
              if(value.id ==element.id && value.roleId==element.role.id){
              this.removedRoleUsers.splice(key,1);
            }
          })
          }
  
  
          
        }
      
          })
          //Delete user from removedRoleUsers if its role is unchecked
          if(this.removedRoleUsers.length >0 && this.removedRoleUsers.indexOf(element.id)>-1){
            var detect = false;
            this.checkedRoleIds.forEach((value)=>{
            if(value.id ==element.role.id){
              detect =true;
            }
            });
            if(!detect){
            this.removedRoleUsers.splice(this.removedRoleUsers.indexOf(element.id),1);
            }
          }				
        });
        console.log("selectedGroupMembers================>");
        console.log(this.selectedGroupMembers);
        
        this.newMemberListByRoleWise.sort(function (a, b) {
          if (a.roleData.name < b.roleData.name) return -1;
          if (a.roleData.name > b.roleData.name) return 1;
          return 0;
          });
          if(this.memberDataGroupWise != undefined){
                 this.memberDataGroupWise.map((group) => {
          if(group && group.userList && group.userList.length > 0){
          group.userList = group.userList.filter((user) => this.checkedIds.indexOf(user.id) == -1);
          }
        });
          }
   
        
      } else if(this.optionShow == 'otherTenantRoles') {
      this.otherTenantStaffList.forEach(element => {
        if (this.checkedIds.includes(element.id)) {
          this.newMember = {
            id: "",
            displayName: "",
            role: {},
            dualRoles:{},
            tenantId: null,
            tenantName: "",
            otherTenantStaff: true,
            naTags: "",
            naTagNames: ""
          };
          this.newMember.id = element.id;
          this.newMember.displayName = element.displayName;
          this.newMember.role = element.role;
          this.newMember.dualRoles =element.dualRoles;
          this.newMember.tenantId = element.role.tenantId;
          this.newMember.tenantName = element.role.tenantName;

          if (element.naTags && element.naTags != null && element.naTags != 'null') {
              this.newMember.naTags = element.naTags;
              this.newMember.naTagNames = element.naTagNames;
          }
          
          if ( this.selectedGroupMembers.findIndex(x => x.id === element.id) === -1){
            this.selectedGroupMembers.push(this.newMember);
            var memberIds = this.selectedGroupDetails.memberIds.split(',');
            memberIds.push(this.newMember.id);
            memberIds = memberIds.join();
            this.selectedGroupDetails.memberIds = memberIds;
          }
          this.selectedGroupMembers = this.selectedGroupMembers.slice();

          this.newMemberOtherTenantList = this.newMemberOtherTenantList.filter( function(a) {
            return (a && a.tenantId);
          });
          this.newMemberOtherTenantList = this.newMemberOtherTenantList.filter(function (members) {
            return members.id != element.id;
          });
        }
      });
      this.newMemberOtherTenantListByRoleWise = [];
      this.newMemberOtherTenantList.forEach(value => {
        if (value.role && value.role.id) {
          var user = { id: value.id, name: value.displayName };
          var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id };        
          if (!this.newMemberOtherTenantListByRoleWise[value.role.id]) {
            this.newMemberOtherTenantListByRoleWise[value.role.id] = {};
          }
          if (!('userList' in this.newMemberOtherTenantListByRoleWise[value.role.id])) {
            this.newMemberOtherTenantListByRoleWise[value.role.id]['userList'] = [];
          }
          if (!('roleData' in this.newMemberOtherTenantListByRoleWise[value.role.id])) {
            this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = {};
          }
          this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = roleData;
          this.newMemberOtherTenantListByRoleWise[value.role.id]['userList'].push(user);
          this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
          this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
          
        }
      });
  
      this.newMemberOtherTenantListByRoleWise = this.newMemberOtherTenantListByRoleWise.filter( function(a) {
        return (a && a.tenantId);
      });
      this.newMemberOtherTenantListByRoleWise.sort(function (a, b) {
        if (a.roleData.name < b.roleData.name) return -1;
        if (a.roleData.name > b.roleData.name) return 1;
        return 0;
      });
      if(this.memberDataGroupWise != undefined){
        this.memberDataGroupWise.map(group => {
          group.userList = group.userList.filter(user=> (this.checkedIds.indexOf(user.id) == -1));
        });
      }
    } else {
      console.log("-> groups add data on left",checkedIds);
      let setCurrentTenant = false;
      let setOtherTenant = false;
      console.log("-> this.allUserDetailsInMsgGroup",this.allUserDetailsInMsgGroup);
      console.log("addMemberToList -> this.selectedGroupMembers",this.selectedGroupMembers);
      let filtered:any = [];
      this.selectedGroupMembers.forEach(value => {
        if (value && value.id && filtered.indexOf(value.id) == -1 && value.id != null && value.id != undefined) {
            filtered.push(value.id);
        }
      });
      console.log("filtered -> ",filtered);
      this.allUserDetailsInMsgGroup.forEach(element => {
        console.log("includesincludes->",this.selectedGroupMembers.includes(element.id))
        if (filtered.includes(element.id)) {
          console.log(element.tenantId,this.userData.tenantId)
          this.newMember = {
            id: "",
            displayName: "",
            role: {},
            dualRoles:{},
            tenantId: null,
            tenantName: "",
            otherTenantStaff: ((element.tenantId && (this.userData.tenantId != element.tenantId )) ? true : false),
            naTags: "",
            naTagNames: ""
          };
          this.newMember.id = element.id;
          this.newMember.displayName = element.name;
          this.newMember.role = element.role ? element.role: [];
          this.newMember.dualRoles =element.dualRoles;
          this.newMember.tenantId = element.tenantId;
          this.newMember.tenantName = element.tenantName;

          if (element.naTags && element.naTags != null && element.naTags != 'null') {
              this.newMember.naTags = element.naTags;
              this.newMember.naTagNames = element.naTagNames;
          }
          console.log("this.selectedGroupMembers.findIndex(x => x.id === value.id)",this.selectedGroupMembers.findIndex(x => x.id === element.id));
          console.log(this.selectedGroupMembers)
          if ( this.selectedGroupMembers.findIndex(x => x.id === element.id) === -1){
            this.selectedGroupMembers.push(this.newMember);
            var memberIds = this.selectedGroupDetails.memberIds.split(',');
            memberIds.push(this.newMember.id);
            memberIds = memberIds.join();
            this.selectedGroupDetails.memberIds = memberIds;
            // this.newallUserDetailsInMsgGroup = this.newallUserDetailsInMsgGroup.filter(function (members) {
            //   return members.id != element.id;
            // });
          }
          this.selectedGroupMembers = this.selectedGroupMembers.slice();
          
          if(element.tenantId == this.userData.tenantId) {
            setCurrentTenant = true
            this.newMemberList = this.newMemberList.filter(function (members) {
              return members.id != element.id;
            });            
          } else {
            setOtherTenant = true;
            this.newMemberOtherTenantList = this.newMemberOtherTenantList.filter(function (members) {
              return members.id != element.id;
            });
          }
        }
      });
      if(setCurrentTenant) {
        this.newMemberListByRoleWise = [];
        this.newMemberList.forEach(value => {
          if (value.role && value.role.id) {
            var user = { id: value.id, name: value.displayName };
            var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id };        
            if (!this.newMemberListByRoleWise[value.role.id]) {
              this.newMemberListByRoleWise[value.role.id] = {};
            }
            if (!('userList' in this.newMemberListByRoleWise[value.role.id])) {
              this.newMemberListByRoleWise[value.role.id]['userList'] = [];
            }
            if (!('roleData' in this.newMemberListByRoleWise[value.role.id])) {
              this.newMemberListByRoleWise[value.role.id]['roleData'] = {};
            }
            this.newMemberListByRoleWise[value.role.id]['roleData'] = roleData;
            this.newMemberListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
            this.newMemberListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
            this.newMemberListByRoleWise[value.role.id]['userList'].push(user);
          }
        });
        this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter( function(a) {
          return (a && a.tenantId);
        });
        this.newMemberListByRoleWise.sort(function (a, b) {
          if (a.roleData.name < b.roleData.name) return -1;
          if (a.roleData.name > b.roleData.name) return 1;
          return 0;
        });
      }
      if(setOtherTenant) {
        this.newMemberOtherTenantListByRoleWise = [];
        this.newMemberOtherTenantList.forEach(value => {
          if (value.role && value.role.id) {
            var user = { id: value.id, name: value.displayName };
            var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id };        
            if (!this.newMemberOtherTenantListByRoleWise[value.role.id]) {
              this.newMemberOtherTenantListByRoleWise[value.role.id] = {};
            }
            if (!('userList' in this.newMemberOtherTenantListByRoleWise[value.role.id])) {
              this.newMemberOtherTenantListByRoleWise[value.role.id]['userList'] = [];
            }
            if (!('roleData' in this.newMemberOtherTenantListByRoleWise[value.role.id])) {
              this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = {};
            }
            this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = roleData;
            this.newMemberOtherTenantListByRoleWise[value.role.id]['userList'].push(user);
            this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
            this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
            
          }
        });
    
        this.newMemberOtherTenantListByRoleWise = this.newMemberOtherTenantListByRoleWise.filter( function(a) {
          return (a && a.tenantId);
        });
        this.newMemberOtherTenantListByRoleWise.sort(function (a, b) {
          if (a.roleData.name < b.roleData.name) return -1;
          if (a.roleData.name > b.roleData.name) return 1;
          return 0;
        });
      }
      this.selectedGroupMembers = this.removeDuplicates(this.selectedGroupMembers,"id");
      this.selectedGroupRoles.forEach((role)=>{
        let roleIndex = this.checkedRoleIds.findIndex(r => r.id === role.roleId);
        if(roleIndex < 0) {
          this.checkedRoleIds.push({id: role.roleId, name: role.name, tenantId: role.tenant_id});
          this.removeRoleStaffs({id: role.roleId, name: role.name, tenantId: role.tenant_id});
        }
      });
    }
    this.disableSubmit();
  }
  removeRoleStaffs(role) {
    this.selectedGroupMembers = this.selectedGroupMembers.filter((member)=>{
      let memberRoleIds = (typeof member.roleIds === 'string') ? JSON.parse(member.roleIds) : member.roleIds;
      if(Array.isArray(memberRoleIds)){
        if(!memberRoleIds.includes(+role.id)) {
          return member;
        } else {
          this.removeMember(member.id);
        }
      } else{
        if(memberRoleIds !== +role.id) {
          return member;
        } else {
          this.removeMember(member.id);
        }
      }
    });
  }
  updateMessageGroup(id,f) {
    if(!f.valid || this.noMemberError) {
      if(!f.valid) {
        $('input.ng-invalid').focus();
      } else if(this.noMemberError) {
        $('html, body').animate({
          scrollTop: $(".card.existing .card-block").offset().top
        });
      }
      return false;
    }    
    this.disableButton = true;
    if (this.selectedGroupMembers.length > 0 || this.checkedRoleIds.length > 0) {
      let selectedRoleIds = []; 
			if(this.checkedRoleIds.length){
			  this.checkedRoleIds.forEach((value,key) => {
				selectedRoleIds.push(Number(value.id));  
			  })
			}
      this.selectedGroupMembers = this.selectedGroupMembers.map((member)=>{
        return { id: member.id, displayName: member.displayName, tenantId: parseInt(member.tenantId), tenantName:member.tenantName };
      })
      this.messageSiteId = (this.messageSiteId == 0 || isBlank(this.messageSiteId)) ? this.editedSite : this.messageSiteId;
      let updateParams : MessageGroupRequest = {
        data :{
          name: this.messageGroup.controls['messageGroupName'].value,
          isPublic: this.selectedisPublic,
          allowMultiThreadChat : this.selectedAllowMultiThread,
          members: this.selectedGroupMembers.map((member)=> { return +member.id}),
          roles: selectedRoleIds
        },
        id: +id,
        siteIds: typeof this.messageSiteId === 'string' ? this.messageSiteId.split(',').map((num) => {return +num}) : [+this.messageSiteId]
      }
      const isPublic = this.selectedisPublic ? "public" : "not public";
      const allowMultiThreadChat = this.selectedAllowMultiThread ? "multi thread chat is allowed" : "multi thread chat is not allowed";
      const selectedGroupMembersNames = this.selectedGroupMembers.map(x=> x.displayName).toString();     
      this.messageService.updateMessageGroup(updateParams).subscribe(data => {
        if(data.success) {
          let newMessageGroupData = {
            status:"up",
            id: id,
            name: this.messageGroup.controls['messageGroupName'].value,
            createdBy: '',
            tenantId: this.userData.tenantId,
            isPublic: this.selectedisPublic,
            allowMultiThreadChat : this.selectedAllowMultiThread,
            pdgroup: "0",
            members: "",
            memberIds: "",
            cmisIds: ""
          };
          const activityLogMessage = `${this.userData.displayName} updated Message Group("${this.messageGroup.controls['messageGroupName'].value}"), members are ${selectedGroupMembersNames.toString()}, branch is ${this.userData.tenantId} and this group is ${isPublic} and ${allowMultiThreadChat} for this group`;
          const activityData = {
            activityName: "Update Message Group",
            activityType: "manage group messaging",
            activityDescription: activityLogMessage
          };
          this._structureService.trackActivity(activityData);
          this._SharedService.newMessageGroup.emit(newMessageGroupData);
          this.getGroups(false);
          this._structureService.notifyMessage({
            messge: this._ToolTipService.getTranslateData('SUCCESS_MESSAGES.MSGGROUP_UPDATE'),
            type: CONSTANTS.notificationTypes.success
          });
          this.disableButton = false;
          setTimeout(() => {
            this.router.navigate(['/message/message']);         
          }, 1000); 
        } else {
          this.disableSubmit();
        }
      }, error => {
        this.disableSubmit();
      });
    } else {
       this.noMemberError = true;
    }
  }
  togglePublic(value) {
    this.selectedisPublic = value;
  }
  toggleMultiThreadOption(value) {
    this.selectedAllowMultiThread = value;
  }

	getNursingAgencyTags() {
		const userData: any = this._structureService.getUserdata();
		const tagGetData = '?userId=' + userData.userId + '&tenantId=' + userData.tenantId;
		const tagTypes = [ '2' ]; // Message Tag =1, User Tag =2 , Document Tag =3
		this._structureService.getNursingAgencyTagsByGroup(tagGetData, tagTypes).then((data: any) => {
			this.nursingAgencyTags = data;
		});
	} 
  emitEventToSelectSites(status) {
		this.eventsSubject.next(status);
	}
  reloadEditData(chosedSite, apply=false) {
    let setRoles = [];
    this.selectedGroupMembers = [];
    if(!apply){
      this.selectSiteId = chosedSite;
    }
    let members;
    if(this.selectedGroupDetails && this.selectedGroupDetails.memberIds != undefined) {
      members = this.selectedGroupDetails.memberIds;
    } else {
      members = '';
    }
    let parameter : GroupsListingRequest = {
      data: { 
        groupId: Number(this._structureService.getCookie('message-grp-id'))
      }
    };
    this.messageService.fetchGroupData(parameter, 'messageGroup').subscribe((response) => {
      if (response.success) {
        this.setGroupList(response.data);
        this.search(this.optionShow);
      }     
      const selectedRoles = response.data.selectedRoles.map(x => x.roleId);
      setRoles = this.checkedRoleIds.filter(elem => selectedRoles.includes((elem.id).toString()));
      this.checkedRoleIds = setRoles;
    });
  }
  disableSubmit() {
    if (this.selectedGroupMembers.length || this.checkedRoleIds.length) {
      this.disableButton = false;
      this.noMemberError = false;
    } else {
      this.disableButton = true;
      this.noMemberError = true;
    }
  }
  goToList() {
    this.location.back();
  }

  setSelectedPDGs(pdgData: any) {
		this.dummyCheckedGroupMembers = pdgData && pdgData.map(item => ({ id: +item.id,
      ...(this._structureService.isMultiAdmissionsEnabled && { admissionId: item.admissionId }) }));
    this.resetPdgSelect = false;
	}
}

