<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
          <strong>Edit Message Tag Types <i class="message-tag icmn-info" data-toggle="tooltip" data-placement="right"></i></strong>            
          <!-- <a [routerLink]="['/supplies/add-inventory-type']" class="pull-right btn btn-sm btn-primary">Add New Staff <i class="ml-1"></i></a> -->
      </span>

    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item"><a>Message Settings</a></li>
            <li class="breadcrumb-item">Edit Message Tag Types</li>
        </ol>
        <div class="row">
            <div class="col-lg-12">
                <!-- <h5 class="text-black"><strong>Tag Definitions</strong></h5>
              <p class="text-muted">Element: read <a href="https://datatables.net/" target="_blank">official documentation<small><i class="icmn-link ml-1"></i></small></a></p> -->
                <div class="mb-5">
                    <form class="form-horizontal" (ngSubmit)="updateMessageTagType(f)" [formGroup]="tagTypeAdd" novalidate #f="ngForm">
                        <div class="form-body">
                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Message Tag Type Name * </label>
                                </div>
                                <div class="col-md-6">
                                    <input class="form-control" type="text" placeholder="Message Tag Type Name " [formControl]="tagTypeAdd.controls['tagTypeName']" name="tagTypeName" id="tagTypeName" [(ngModel)]="tagTypeName">
                                    <div class="alert alert-danger" *ngIf="!tagTypeAdd.controls.tagTypeName.valid && (tagTypeAdd.controls.tagTypeName.dirty || tagTypeAdd.controls.tagTypeName.touched || f.submitted)">
                                        Message Tag Type cannot be empty.
                                    </div>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Tag Type Description </label>
                                </div>
                                <div class="col-md-6">
                                    <textarea class="form-control" [formControl]="tagTypeAdd.controls['tagTypeDesc']" placeholder="Tag Type Description" [(ngModel)]="tagTypeDesc"></textarea>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">User Type * </label>
                                    <i class="message-type-tt icmn-info"></i>
                                </div>
                                <div class="col-md-6">
                                    <select class="form-control" [formControl]="tagTypeAdd.controls['tagUserType']"  id="tagUserType" >
                                        <option value="">Select User Type</option>
                                        <option [selected]="tagTypeAdd.controls['tagUserType'].value == 'staff-facing'" value="staff-facing">Staff Facing</option>
                                        <option [selected]="tagTypeAdd.controls['tagUserType'].value == 'patient-facing'" value="patient-facing">Patient Facing</option>
                                        <option [selected]="tagTypeAdd.controls['tagUserType'].value == 'staff-patient-facing'" value="staff-patient-facing">Both Staff & Patient Facing</option>
                                    </select>
                                    <div *ngIf="tagTypeAdd.controls['tagUserType'].hasError('required')&&(tagTypeAdd.controls.tagUserType?.dirty ||tagTypeAdd.controls.tagUserType?.touched || f.submitted)" class="alert alert-danger">
                                        User Type cannot be empty
                                    </div>
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Background Color</label>
                                </div>
                                <div class="col-md-6">
                                    <input class="form-control" [formControl]="tagTypeAdd.controls['bgColor']" [(colorPicker)]="bgColour" [cpWidth]="'230px'" [cpHeight]="'197px'" [cpPositionRelativeToArrow]="false" [cpAlphaChannel]="'disabled'" [cpOutputFormat]="'hex'" style="width:50px;"
                                        [style.background]="bgColour" [value]="" />
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Font Color</label>
                                </div>
                                <div class="col-md-6">
                                    <input class="form-control" [formControl]="tagTypeAdd.controls['fontColor']" [(colorPicker)]="fontColour" [cpWidth]="'230px'" [cpHeight]="'197px'" [cpPositionRelativeToArrow]="false" [cpAlphaChannel]="'disabled'" [cpOutputFormat]="'hex'" style="width:50px;"
                                        [style.background]="fontColour" [value]="" />
                                </div>
                            </div>

                            <div class="form-group row">
                                <div class="col-md-6">
                                    <button class="btn btn-primary">Update</button>
                                    <button type="button" (click)="naviBack()" class="btn btn-default">Cancel</button>
                                    <button type="reset" style="display:none;" id="hideReset" class="btn btn-success">Reset</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- END: tables/datatables -->