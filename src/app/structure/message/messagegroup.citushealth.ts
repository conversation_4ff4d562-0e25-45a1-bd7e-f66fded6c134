import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';

declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Component({
  selector: 'app-message-group',
  templateUrl: './messagegroup.html'
})

export class MessageGroupComponent implements OnInit {
  constructor(private route: ActivatedRoute,
    private router: Router,
    private _structureService: StructureService

  ) { }
  ngOnInit() {
    this._structureService.previousUrl = 'message/messagegroup';

  }
  addGroupMember() {
    this.router.navigate(['/message/addgroupmember']);
  }
}

