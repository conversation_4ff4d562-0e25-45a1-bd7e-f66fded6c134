<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Logs (List Works [Read], Create, Update, Delete TODO [Write])</strong>
            <!-- <a [routerLink]="['/supplies/add-inventory-type']" class="pull-right btn btn-sm btn-primary">Add New Staff <i class="ml-1"></i></a> -->
        </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/dashboard']" >Admin</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/logs']" >Messages</a></li>
            <li class="breadcrumb-item">Logs</li>
        </ol>
        <div class="row">
            <div class="col-lg-12">
                <!-- <h5 class="text-black"><strong>Basic Responsive Table</strong></h5>
                <p class="text-muted">Element: read <a href="https://datatables.net/" target="_blank">official documentation<small><i class="icmn-link ml-1"></i></small></a></p> -->
                <div class="mb-5">
                    <div id="example1_wrapper" class="dataTables_wrapper tbl-filter container-fluid dt-bootstrap4 no-footer">
                        <div class="row">
                            <div class="col-sm-12 col-md-6">
                                <!--<div class="dataTables_length" id="example1_length"><label>Show 
                                    <select name="example1_length" aria-controls="example1" class="form-control input-sm">
                                        <option value="Document">Document</option>
                                        <option value="From User">From User</option>
                                        <option value="To User">To User</option>
                                        <option value="Sent">Sent</option>
                                    </select> entries
                                    </label>
                                </div>-->
                            </div>
                            <div class="col-sm-12 col-md-4">
                                
                            </div>
                            <div class="col-sm-12 col-md-2">
                                <div class="" id="example1_length col-sm-3 pull-right "> 
                                    <select name="example1_length" aria-controls="example1" class="form-control pull-right">
                                        <option value="initiator">Search an Initiator</option>
                                        <option value="member">Search a Member</option>
                                        <option value="group-name">Search a Group Name</option>
                                        <option value="date">Search by Date</option>
                                    </select> 
                                    
                                </div>
                            </div>
                        </div>
                    </div>

                    <table class="table table-hover nowrap"  width="100%">
                        <thead>
                        <tr>
                            <th style="width:10%"> # </th>
                            <th style="width:30%"> Initiator  </th>
                            <th style="width:40%"> Chat With </th>
                            <th style="width:10%"> Count </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr  routerLink="/message/logs/chat-logs/{{message.id}}" class="odd gradeX" *ngFor="let message of logMessages index as i">
                            
                            <td> {{i+1}} </td>
                            <td> {{message.initiator}} </td>
                            <td > {{message.chatMembers}}</td>
                            <td> <span class="badge badge-pill badge-info mr-2 mb-2">{{message.messageCount}}</span> </td>
                            
                            
                        </tr>
                       
                        
                    </tbody>
                    </table>
                </div>
            </div>
        </div>        
    </div>
</section>
<!-- END: tables/datatables -->
