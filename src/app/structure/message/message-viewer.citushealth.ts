import { Component, OnInit, Input, Renderer, ElementRef } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { Router, ActivatedRoute } from '@angular/router';
import { Location } from '@angular/common';
import { Subject } from 'rxjs';
import { CONSTANTS, IntegrationType, MessagePriority, msgHistoryDateTimeFormat } from 'app/constants/constants';
import { DomSanitizer } from '@angular/platform-browser';
import { PdfViewService } from 'app/structure/pdfview.service';
import { UserService } from 'app/services/user/user.service';
import { MessageService } from 'app/services/message-center/message.service';
import { HttpService } from 'app/services/http/http.service';
import { APIs } from 'app/constants/apis';
import { ChatService } from '../inbox/chat.service';
import { InboxService } from '../inbox/inbox.service';
import { ToolTipService } from '../tool-tip.service';
import { StructureService } from '../structure.service';
import { MessageViewerService } from './message-viewer.service';
import { StaticDataService } from '../static-data.service';
import { Modal } from '../shared/commonModal';

declare const $: any;
declare const swal: any;
const moment = require('moment/moment');
const jstz = require('jstz');

const timezone = jstz.determine();
declare const NProgress: any;
@Component({
  selector: 'app-message-viewer',
  templateUrl: './message-viewer.html',
  styleUrls: ['./message-viewer.component.scss']
})
export class MessageViewerComponent implements OnInit {
  chatRoomId;
  initiatorId;
  getPatientId; 
  getTagId;
  initiatorDetails: any;
  initiatorType: any;
  groupName: any;
  assocSiteId : any;
  siteIdEmpty = false;
  chats:any = [];
  dataLoadingMsg=true;
  members = [];
  signUrl:any;
  taggedMessageDetails:any;
  messageTagForm: FormGroup;
  messageCommentForm: FormGroup;
  availableTags;
  hideSiteSelection: boolean;
  eventsSubject: Subject<void> = new Subject<void>();
  userNotSelected: any = false;
  tagNotSelected: any = false;
  selectedTags;
  chatMessageText;
  messageId;
  editIndex;
  approverRowId;
  profileImageUrl;
  selectedTagsIndex:any=[];
  selectedTagsList:any='';
  availableTagsOp:any;
  privileges = this._structureService.getCookie('userPrivileges');
  pdgAssosiatedPatientArray:any = [];
  config:any;
  userDetails:any;
  configData:any = {};
  userData:any = {};
  userDataConfig:any = {};
  patientFace: any = false;
  usersListForTagMessage = [];
  selectedUserForTag;
  approver: any;
  formApproverUsersListSelection: any;
  hideElement = false;
  download = 0;
  associatePatientLoading=false;
  selectedPatientId: any = '';
  assosiatedPatients: any = [];
  selectedAssosiatePatient: any = '';
  selectedPatient: any = '';
  selectedAssosiatePatientName:any='';
  queryParams:any = {};
  @Input() chatDetails: any;
  patientIdentity:any =0;
  patientTenantId:any= (this.userData && this.userData.crossTenantId) ? this.userData.crossTenantId : "";
  MessagePriorityData = this.staticDataService.getPriorities();
  MESSAGE_PRIORITY = MessagePriority;
  pdfOrDocSrc = '';
  pdfOrDocUrl = '';
  pdfOrDoc = '';
  showIframe = false;
  isModalOpen = false;
  modalBody;
  cancelLabel = false;
  showVideoFrame = false;
  showHeader = false;
  title = '';
  subTitle = '';
  avatarImagePath = CONSTANTS.avatarImagePath;
  defaultAvatarImageUrl = CONSTANTS.defaultAvatarImageUrl;
  newWindow: Window;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private _structureService: StructureService,
    private _messageViewerService: MessageViewerService,
    private _location: Location,
    private _formBuild: FormBuilder,
    private _inboxService: InboxService,
    public _chatService: ChatService,
    private staticDataService: StaticDataService,
    elementRef: ElementRef,
    renderer: Renderer,
    private modals: Modal,
    private sanitizer: DomSanitizer,
    private pdfViewService: PdfViewService,
    private _http: HttpClient,
    private userService: UserService,
    private toolTipService: ToolTipService,
    private messageService: MessageService,
    private httpService: HttpService
    ) { 
      this.config = this._structureService.userDataConfig;
      this.userDetails = this._structureService.userDetails;
      this.configData = JSON.parse(this.config);
      this.userData = JSON.parse(this.userDetails);
      this.userDataConfig = JSON.parse(this._structureService.userDataConfig);
      renderer.listen(elementRef.nativeElement, 'click', (event) => {
        const ngClick = $(event.target).attr('ng-click');
        if (ngClick !== 'undefined') {
          const mimeType = $(event.target).attr('data-mediatype');
          if (typeof ngClick !== 'undefined' && ngClick.indexOf('showPdf') !== -1 && mimeType === 'pdf') {
            this.showPdf(event);
          } else if (typeof ngClick !== 'undefined' && (ngClick.indexOf('showPdfOrDocs') !== -1 || ngClick.indexOf('showCmisPdfOrDocs') !== -1)) {
            this.showPdf(event);
          } else if (typeof ngClick !== 'undefined' && (ngClick.indexOf('showImage') !== -1 || ngClick.indexOf('showCmisImage') !== -1)) {
            this.showImage(event);
          } else if (typeof ngClick !== 'undefined' && ngClick.indexOf('showVideo') !== -1) {
            this.showVideo(event);
          }
        }
      });
      }

  ngOnInit() {
    setTimeout(function () {
            $('body').tooltip({selector: '[data-toggle="tooltip"]'});
            $('[data-toggle="tooltip"]').tooltip();
    },100);
    $('.enterKeyCheck').popover();

    this.profileImageUrl = this._structureService.getCookie('profileImageUrl');
    this.messageTagForm = this._formBuild.group({
            chatMessage:[''],
            users: [''],
            messageTags:['']
    });
    this.messageCommentForm =  this._formBuild.group({
            chatMessage:['']
    });
    console.log('Chat log === ', JSON.parse(localStorage.getItem('chatLogDetails')));
    let chatroomDeatils = JSON.parse(localStorage.getItem('chatLogDetails'));
    if (this.chatDetails) {
      chatroomDeatils = this.chatDetails;
    }
    let patientTenantIdValue = 0;
    let callChatRoomUsers = false;
    this._chatService.getChatroomMessages(chatroomDeatils['id'], true, 0,0, "",'1', 0, 0, [], [], 'loadPdgData').then((data) => {
      if(data && JSON.parse(JSON.stringify(data))){
        if (JSON.parse(JSON.stringify(data)).chatroomid == chatroomDeatils['id']){
          var result = JSON.parse(JSON.stringify(data));
          if(result && "isPdg" in result && result.isPdg == 1) {
            if(result && "patient_data" in result && result.patient_data && result.patient_data.length && result.patient_data[0]) {
              patientTenantIdValue = ("tenantid" in result.patient_data[0] && result.patient_data[0].tenantid) ? result.patient_data[0].tenantid : 0;
              this.setPatientDetails(result.patient_data[0])
            }
            this.getAllTags(patientTenantIdValue);
          } else {
            callChatRoomUsers = true;
          }
        }
      } else{
        callChatRoomUsers = true;
      }
      if(callChatRoomUsers) {
        this._chatService.getUsersListByRoom(chatroomDeatils['id'],'All').then((result:any) => {
          result.data.allParticipants.forEach((element) => {
              if (+element.roleId === 3) {
                  patientTenantIdValue = element.tenantid;
                  this.setPatientDetails(element)
              }
          });
          this.getAllTags(patientTenantIdValue);
      });
      }
    }).catch((ex) => {
      this._chatService.getUsersListByRoom(chatroomDeatils['id'],'All').then((result:any) => {
        result.data.allParticipants.forEach((element) => {
            if (+element.roleId === 3) {
                patientTenantIdValue = element.tenantid;
                this.setPatientDetails(element)
            }
        });
        this.getAllTags(patientTenantIdValue);
    });
    });
    $('#messageTags').on('change', (e) => {  
      console.log(e.target.value)
      if (e.target.value !== '') {
          this.tagNotSelected = false;
      } 
    });
    var $eventSelect = $("#messageTags");
    $('#messageTags').select2({
        placeholder: "Select one or more tags"
    });
    this.userService.getAssociatedPatientsLists([]).subscribe((users: any) => {
      this.usersListForTagMessage = users;
    });
    this._inboxService.getTenantClinicians(this.userData.tenantId).then((tenantUsers) => {
      this.formApproverUsersListSelection = tenantUsers;
    });
    var self = this;
        var checkPatientFacig = [];
        $eventSelect.on("change", function (e) {
            checkPatientFacig = [];
            var messageTag = $("#messageTags").val(); 
            if (messageTag.length) {
                messageTag.forEach(element => {
                    var member = { id: "" };
                    var id = element.substr(element.indexOf(":") + 1);
                    id = id.replace(/'/g, "");
                    member.id = id.replace(/\s/g, '');
                  var tagId = Number(member.id);
                  if (tagId) {
                   
                        self.availableTags.map(function (tags) {
                            if (tags.id == tagId && tags.meta) {
                                let meta = JSON.parse(tags.meta);
                                if (meta.patientFacing == true) {
                                    checkPatientFacig.push(1);
                                } else {
                                    checkPatientFacig.push(0);
                              }
                             
                            }
                        });
                    }
                });
                if (checkPatientFacig.indexOf(1) != -1) {
                    self.patientFace = true;
                } else {
                    self.patientFace = false;
                }
            } else {
                self.patientFace = false;
            }
            self.enableOrDisableUiLI(false,false);
        });


    $(function () {
      $('.select2-tags').select2({
        tags: true,
        tokenSeparators: [',', ' ']
      });
      $('.datepicker-only-init').datetimepicker({
        widgetPositioning: {
          horizontal: 'left'
        },
        icons: {
          time: "fa fa-clock-o",
          date: "fa fa-calendar",
          up: "fa fa-arrow-up",
          down: "fa fa-arrow-down",
          previous: 'fa fa-arrow-left',
          next: 'fa fa-arrow-right'
        },
        format: 'LL'
      });
     })
     this.queryParams = JSON.parse(localStorage.getItem('chatLogDetails'));
     if(this.queryParams) {
      this.chatRoomId = this.queryParams['id'];
      this.initiatorId = this.queryParams['initiator'];
      this.initiatorType = this.queryParams['type'];
      this.groupName = this.queryParams['groupname'];
     }
     if (this.chatDetails) {
        this.chatRoomId = this.chatDetails['id'];
        this.initiatorId = this.chatDetails['initiator'];
        this.initiatorType = this.chatDetails['type'];
        this.groupName = this.chatDetails['groupname'];
        this.hideElement = true;
     }     
     if(this.chatRoomId) {
     this.getChatMessages(this.chatRoomId);
     } else {
       this.dataLoadingMsg = false;
       if(!this.chatDetails && !this.queryParams) {
        this.router.navigate(['/message/logs']);
       }
     }

  }
  ngOnDestroy() {
    localStorage.removeItem('chatLogDetails');
  }
  setPatientDetails(element){
    this.selectedAssosiatePatient = (element.caregiver_userid && element.caregiver_userid != '' && element.caregiver_userid != '0')?element.caregiver_userid:element.userId;
    var dob_formatted ='';
    if(element.caregiver_dob){
      dob_formatted = moment(element.caregiver_dob).format('MM/DD/YYYY');
    } else {
      dob_formatted = moment(element.dob).format('MM/DD/YYYY');
    }
    if (this.userData.config.default_patients_workflow != 'alternate_contacts') {
      var patientDisplayName = (element.caregiver_displayname)?((dob_formatted)?(element.caregiver_displayname+' ('+element.displayName +')'+' - ' + dob_formatted):(element.caregiver_displayname+' ('+element.displayName +')')):((dob_formatted)?(element.displayName+ ' - ' +dob_formatted):element.displayName);
    }else{
      var patientDisplayName = (element.caregiver_displayname)?((dob_formatted)?(element.caregiver_displayname+' - ' + dob_formatted):(element.caregiver_displayname+' ('+element.displayName +')')):((dob_formatted)?(element.displayName+ ' - ' +dob_formatted):element.displayName);
    }
    patientDisplayName = patientDisplayName+((element.IdentityValue == null || element.IdentityValue == "") && (element.caregiver_identityvalue != null && element.caregiver_identityvalue != "") ?' [MRN: '+element.caregiver_identityvalue+']':'') ;

    patientDisplayName = patientDisplayName +((element.IdentityValue != null && element.IdentityValue != "") ?' [MRN: '+element.IdentityValue+']':'') ;
    var userStatus = element.caregiver_displayname ? element.caregiver_passwordStatus == 'true' ? " (Enrolled)" : " (Virtual)" :(element.passwordStatus == 1 ||  element.passwordStatus == '1')  ? " (Enrolled)" : " (Virtual)";
    patientDisplayName = patientDisplayName +userStatus;
    this.patientIdentity = element.patient_identity;
    this.patientTenantId = (element.tenantid) ? element.tenantid  : this.userData.crossTenantId;
    $("input#associate-search-input").val(patientDisplayName);
  }
  closeModal() {
    this.modals.hide();
    this.isModalOpen = false;
    this.modalBody = '';
    this.pdfOrDocSrc = '';
    this.pdfOrDocUrl = '';
    this.showIframe = false;
    this.showVideoFrame = false;
    this.cancelLabel = false;
}
  showImage(targetEvent) {
    let elName = $(targetEvent.target).attr('src');
    if (!elName) {
        elName = $(targetEvent.target).attr('data-viewsrc');
    }
    elName = elName.split('?')[0] + "?type='image'";
    if (elName.indexOf('thumb_180x116/') > -1)
        elName = elName.replace('thumb_180x116/', '');
    this.isModalOpen = true;
    this.cancelLabel = true;
    this.modalBody = '<div class="chat-image-viewer"><img src="' + elName + '" class="imagepreview modal-img-viewer"></div>';
    this.modals.show();
  }
  showPdf(targetEvent) {
    this.pdfOrDoc = $(targetEvent.target).attr('data-src') || $(targetEvent.target).attr('data-viewsrc');
    this.isModalOpen = true;
    this.cancelLabel = true;
    this.showIframe = true;
    this.pdfViewService.showPdfViewer(this.pdfOrDoc).then((modalBody) => {
      this.modalBody = modalBody;
      this.modals.show();
    });
  }
  showVideo(targetEvent) {
    let videoUrl = $(targetEvent.target).attr('data-src');
    if (!videoUrl) {
      videoUrl = $(targetEvent.target).attr('data-viewsrc');
    }
    if (videoUrl.includes(this.userData.cmisFileBaseUrl)) {
      this._http.get(videoUrl,{responseType: 'arraybuffer'}).toPromise().then(response => {
          const video = new Blob([new Uint8Array(response)], { type: $(targetEvent.target).attr('data-mediatype') });
          this.modalBody = this.sanitizer.bypassSecurityTrustResourceUrl(window.URL.createObjectURL(video));
        });
    } else {
      this.modalBody = this.sanitizer.bypassSecurityTrustResourceUrl(videoUrl);
    }
    this.isModalOpen = true;
    this.cancelLabel = true;
    this.showVideoFrame = true;
    this.modals.show();
  }
  hideDropdown(hideItem: any) {
    this.hideSiteSelection = hideItem.hideItem;
  }
  getChatMessages(id) {
    this._structureService.getChatMessages(id,this.initiatorId).then((data)=>{
        this.dataLoadingMsg = false;
        this.chats = JSON.parse(JSON.stringify(data['getSessionTenant']['chatRoomMessages']));
        this.chats.forEach((chat) => {
          if (chat.message.messageStatus === 0 && Array.isArray(chat.message.deleteUndoHistory)) {
              chat.message.deleteUndoHistory.forEach((item) => {
                  const actionTime = moment(item.actionTime).format(msgHistoryDateTimeFormat);
                  const action =  this.toolTipService.getTranslateData(item.actionType === 0 ? 'MESSAGES.MSG_HISTORY.DELETED': 'MESSAGES.MSG_HISTORY.RESTORED');
                  item.actionHistory = this.toolTipService.getTranslateDataWithParam('MESSAGES.MSG_HISTORY.MESSAGE', { actionTime, action });
              });
          }          
        });
        this.initiatorDetails = data['getSessionTenant']['chatRoomInitiators'];
        this.signUrl =  this._structureService.apiBaseUrl + 'writable/filetransfer/uploads/';       
    });
  }
  backClicked() {
    this._location.back();
  }
  editChatMessageAndTags(mDetails,index){
    this.availableTagsOp = [];
    this.selectedTagsIndex = [];
    this.userNotSelected = false;
        this.tagNotSelected = false;
    this.chats[index].attachedTags.forEach(res => {
          this.selectedTagsIndex.push(res.tagId.toString());
    });
    this.chats[index].approvedDetails.approverTags.forEach(res => {
          this.selectedTagsIndex.push(res.id.toString());
    });
    setTimeout(function () {
      $("#messageTags").trigger("change");
      this.selectedPatient ='';
  }, 600);
    this.availableTagsOp = this.availableTags.filter(res => {
      if(!this.selectedTagsIndex.includes(res.id)){
           return true;
      }else{
          return false;
      }
    });
    if ($('#messageTagModal').hasClass('show')) {
        $('#messageTagModal').modal('hide');
    }
    $('#messageTagModal').modal('show');
    $('#chat-message').val='';
    var preSelectedTags = [];
    // var selectedPatient='';
    this.taggedMessageDetails = mDetails;
    this.messageId = this.taggedMessageDetails.message.id;
    this.getPatientId = this.taggedMessageDetails.patientId
    this.approverRowId = this.taggedMessageDetails.approvedDetails.approverId;//Pass data to db and update with new rows
    this.editIndex = index;
    preSelectedTags=this.selectedTagsIndex;
    // selectedPatient=this.taggedMessageDetails.patientIdNew;
    this.selectedPatient =(this.taggedMessageDetails.caregiverDisplayName ? this.taggedMessageDetails.patientDisplayName + " (" + this.taggedMessageDetails.caregiverDisplayName + ")" : this.taggedMessageDetails.patientDisplayName);
    this.taggedMessageDetails.attachedTags.forEach(tagData => {
      // preSelectedTags.push(tagData.id);
    });
    if(preSelectedTags.length > 0){
      this.messageTagForm = this._formBuild.group({ 
        chatMessage:[''],
        users: [this.selectedPatient],
        messageTags:[preSelectedTags]
      }); 
      $("input#associate-search-input").val(this.selectedPatient);
        setTimeout(function() {
          $("#messageTags").select2({ placeholder: "Select one or more tags" });
                // $("#users").select2({ placeholder: "Select a patient" });
        }, 50);
     }else{
        this.messageTagForm = this._formBuild.group({ 
            chatMessage:[''],
            users: [''],
            messageTags:['']
        });            
        setTimeout(function() {
            $("#messageTags").select2({ placeholder: "Select one or more tags"});
            //$("#chatMessage").select2({placeholder: "Comment"});
            $.fn.select2.amd.require(
              ['select2/data/array', 'select2/utils'],
              function (ArrayData, Utils) {
        
                interface CustomDataFN {
                  (this: any, $element: any, options: any): any;
                  __super__: any;
              }
            
              var CustomData: CustomDataFN = <CustomDataFN>function ($element, options): any {
                CustomData.__super__.constructor.call(this, $element, options);
            }
                
                function contains(str1, str2) {
                  return new RegExp(str2, "i").test(str1);
                }
            
                Utils.Extend(CustomData, ArrayData);
                
                CustomData.prototype.query = function (params, callback) {
                  if (!("page" in params)) {
                    params.page = 1;
                  }
                  var pageSize = 20;
                  var results = this.$element.children().map(function(i, elem) {
                    
                    if (contains(elem.innerText, params.term)) {
                      return {
                        id:elem.value,
                        text:elem.innerText
                      };
                    }
                  });
                  callback({
                    results:results.slice((params.page - 1) * pageSize, params.page * pageSize),
                    pagination:{
                      more:results.length >= params.page * pageSize
                    }
                  });
                };
            
                $("#users").select2({
                  ajax:{},
                  allowClear:true,
                  width:"element",
                  placeholder: "Select a patient",
                  dataAdapter:CustomData
                });
              });
        }, 50);
     }
  }
  addCommentForMessage(mDetails, i){
    $('#messageCommentModal').modal('show');
    this.taggedMessageDetails = mDetails;
    this.messageId = this.taggedMessageDetails.message.id;
  }
  openAssociateList(){
    if(!this.associatePatientLoading){
      if(this.assosiatedPatients.length){
        this.enableOrDisableUiLI(true,false);
      }
    }
  }
  enableOrDisableUiLI(condition,clear:boolean=true){
    if(condition){
      $("ul.associate-ul").css('display','block');
      $("input#associate-search-input").addClass("active");
    }else{
     $("ul.associate-ul").css('display','none');
     $("input#associate-search-input").removeClass("active");
    }
    if(clear){
      $("input#associate-search-input").val('');
    }
  }
  searchOnEnter(event) {
    if(event.keyCode == 13) {
      let searchTerm =  $("#associate-search-input").val();
      if(searchTerm!=""){
        this.getAssociatePatients('',searchTerm);
      }
    }
  }
  getAssocSiteIds(data: any, page :string){
    this.assocSiteId = data.siteId;
    if (page == 'Associate Patient') {
      if (this.assocSiteId !== "0") {
        this.assocSiteId = this.assocSiteId.toString();
        this.siteIdEmpty = false;
      }
      this.checkAssociatePatientWithTems();
    }
}
  checkAssociatePatientWithTems(){
    let searchTerm =  $("#associate-search-input").val();
    if(searchTerm!=""){
      this.getAssociatePatients('',searchTerm,this.assocSiteId);
    }
  }
  getAssociatePatients(form='', search:string="",assocSiteId=0){   
    $("#associate-search").text(" ").text("Loading");
    this.associatePatientLoading = true;
    this.userService.getAssociatedPatientsLists(form, search,0,this.assocSiteId).subscribe((result: any) => {
      this.associatePatientLoading = false;
      $("#select2-assosiatedPatients-container .select2-selection__placeholder").html("").html("Select Associated Patient");
      $("#assosiatedPatients").attr("disabled", false);
      this.assosiatedPatients = result.filter((result) => {
        let date = "";
        if (result.caregiver_dob) {
          date = result.caregiver_dob;
          let dobDay = new Date(date).getDay();
          if (date && !isNaN(dobDay)) {
            date = date.replace(/-/g, '/');
            try {
              date = moment(date).format('MM/DD/YYYY');
            }
            catch (e) {
              date = '';
            }
          } else {
            date = "";
          }
        } else {
          date = "";
        }
        result.caregiver_dob_formatted = date;
        date = "";
        if (result.dob) {
          date = result.dob;
          let dobDay = new Date(date).getDay();
          if (date && !isNaN(dobDay)) {
            date = date.replace(/-/g, '/');
            try {
              date = moment(date).format('MM/DD/YYYY');
            }
            catch (e) {
              date = '';
            }
          } else {
            date = "";
          }
        } else {
          date = "";
        }
        result.dob_formatted = date;
        var listDisplayName =((result.caregiver_displayname && (result.relation_type &&  result.relation_type!=2))? result.displayname + " (" + result.caregiver_displayname + ")" : result.displayname);
        result.listDisplayName = listDisplayName+(result.passwordStatus == 'true' ? " (Enrolled)" : " (Virtual)");
        return true;
      });
      $("#associate-search").text("").text("Search");
      if(search !=""){        
        this.enableOrDisableUiLI(true,false);
      }
    });
  }
  closeSelectedAssociatePatient(){
    if(this.selectedAssosiatePatient){
      this.enableOrDisableUiLI(false,true);
      this.selectedAssosiatePatientName="";
    }else if($("input#associate-search-input").val() !=""){
      $("input#associate-search-input").val("");
      $("#associate-search").text(" ").text("Search");
      this.associatePatientLoading = false;      
      this.selectedPatientId = '';
    }
    this.assosiatedPatients = []; 
    this.selectedAssosiatePatient = '';
  }
  selectAssosiatePatient(selectedAssociatePatientId, selectedAssociatePatientName) {
    this.getPatientId = selectedAssociatePatientId;
    this.selectedAssosiatePatient = selectedAssociatePatientId;//$("#assosiatedPatients").val();
    this.selectedAssosiatePatientName = selectedAssociatePatientName;
    $("input#associate-search-input").val(selectedAssociatePatientName);
    this.enableOrDisableUiLI(false,false);
    var activityData = {
      activityName: "Select Associate Patient",
      activityType: "structured forms",
      activityDescription: this.userData.displayName+"("+this.userData.userId + ") selected Associate Patient -"+this.selectedAssosiatePatientName+"("+ this.selectedAssosiatePatient +")" 
    };
    this._structureService.trackActivity(activityData);

  }
  submitComment(){
    var self=this;
    this.chatMessageText = $('#chat-message').val();
    let saveDetails:any = {};
    if(this.chatMessageText){
       saveDetails.comment = this.chatMessageText;
       saveDetails.messageId = this.messageId;
       saveDetails.userId = this.userData.userId;
       saveDetails.tenantId = this.userData.tenantId;
       saveDetails.createdAt = new Date().getTime();
       this._messageViewerService.saveMessageComment(saveDetails).then((data)=>{
        $('#messageCommentModal').modal('hide');
        self._structureService.notifyMessage({
            messge: 'Comment added successfully',
            type: 'success'
        });
        var activityLogreRouteMessage = this.userData.displayName + "(" + this.userData.userId + ") added comment for the message ("+this.messageId+") in chat log ("+this.chatRoomId+")";
          var activityData = {
              activityName: "Comment Message",
              activityType: "forms",
              activityDescription: activityLogreRouteMessage
          };
          this._structureService.trackActivity(activityData);            
       }); 
    }else{
     $('#messageCommentModal').modal('hide');
    }

  }
  submitText() {
 
    this.userNotSelected = false;
    this.tagNotSelected = false;
    this.selectedUserForTag = this.selectedAssosiatePatient ? this.selectedAssosiatePatient : 0;
    var selectedUser = $("input#associate-search-input").val();
    var selectedPatientName = selectedUser;
    if (!selectedUser && this.patientFace) {
      this.userNotSelected = true;
      return;
    }
    var autoApprove = true;
    $('#messageTagModal').modal('hide');
    let self = this;
    let selectedTags = [];
    this.selectedTags = $('#messageTags').val();
    let multiple = '';
    let tagNames: any = [];
    if (this.selectedTags) {
      this.selectedTags.forEach(element => {
        let member = { id: "" };
        let id = element.substr(element.indexOf(":") + 1);
        id = id.replace(/'/g, "");
        member.id = id.replace(/\s/g, '');
        selectedTags.push(Number(member.id));
        multiple = multiple + member.id + ";";
        let tId = member.id;
        this.availableTagsOp.forEach(res => {
          if (res.id == tId) {
            if (res.meta) {
              var metaTag = JSON.parse(res.meta);
              if (metaTag.approvalRequired) {
                autoApprove = false;
              }
            }
            let getItems = { "id": tId, "name": res.name, "patient": !this.patientFace ? 0 : this.selectedUserForTag, "tagName": res.name, "bgColor": res.bgColor, "fontColor": res.fontColor, "typeName": res.typeName };
            tagNames.push(getItems);
          }
        });
      });
    }
    let saveDetails: any = {};
    saveDetails.tags = tagNames;
    saveDetails.messageId = this.messageId;
    saveDetails.userId = this.userData.userId;
    if (this.patientTenantId) {
      saveDetails.tenantId = this.patientTenantId;
    }
    else {
      saveDetails.tenantId = this.userData.tenantId;
    }
    saveDetails.createdAt = new Date().getTime();
    saveDetails.patient = '';
    saveDetails.approverRowId = this.approverRowId;
    saveDetails.fromUser = this.userData.displayName;
    saveDetails.createdBy = this.userData.userId;
    saveDetails.id = this.taggedMessageDetails.id;
    saveDetails.single = !this.patientFace ? 0 : this.selectedUserForTag;
    saveDetails.multiple = multiple; 
    if (this.selectedTags != null && selectedUser != null) {
      this.getTagId = selectedTags.toString();
      if (tagNames.length !=0 ) {
        if (tagNames[0].patient !== 0) {
          this.getPatientId = tagNames[0].patient;
        }
      }
      const msgTagIntegrationBody = {
        "message_tag_id": this.getTagId,
        "patient_id": this.getPatientId,
        action: IntegrationType.ADD_MESSAGE_TAG
      };
      this.messageService.checkMessageIntegrationStatus(msgTagIntegrationBody).subscribe((data) => {
        if (data.success) {
          this.onSubmitText(saveDetails, tagNames, autoApprove);
        }
      }), () => {
        const message = this._structureService.isMultiAdmissionsEnabled ?
        this.toolTipService.getTranslateData('ERROR_MESSAGES.USER_NO_ACCESS_TO_PATIENT_SITE') : this.toolTipService.getTranslateData('ERROR_MESSAGES.COMMON_ERROR_MSG');
        this._structureService.notifyMessage({ message });
      };
    }
    else { this.onSubmitText(saveDetails, tagNames, autoApprove); }
  }
  onSubmitText(saveDetails, tagNames, autoApprove) {
    let self = this;
    if ((this.patientFace && saveDetails.single && saveDetails.multiple) || (!this.patientFace && saveDetails.multiple)) {
      NProgress.start();
      this._messageViewerService.updateMessageDetails(saveDetails,this.patientIdentity).then((data) => {
        let result: any = data;
        result = JSON.parse(result);
        var messageData = '';
        this.chats[this.editIndex].approvedDetails.approverId = result.id;
        this.chats[this.editIndex].approvedDetails.approverTaggedUser = this.userData.displayName;
        let currentTime: any = new Date().getTime() / 1000;
        currentTime = currentTime.toString();
        currentTime = currentTime.split(".");
        let cretedTimeDisplay = currentTime[0];
        if (result.success == true) {
          self.approver = 'the approver';
          var formApproverUserid = [];
          var formApproverUseridDisplayName = [];
          var formApprover = [];
          if (self.configData.form_approver_clinician && self.configData.form_approver_clinician != "") {
            var formApproverData = self.configData.form_approver_clinician.split(",");

            formApproverData.forEach(value => {
              var userIds = { userid: value };
              formApproverUserid.push(userIds);
              formApproverUseridDisplayName.push(value);
            });
            self.formApproverUsersListSelection.filter(function (userData) {
              if (formApproverUseridDisplayName.indexOf(userData.userid) != -1) {
                formApprover.push(userData.displayname);
              }
            });
            self.approver = formApprover.toString();
          }
          NProgress.done();
          cretedTimeDisplay = result.created_at ? result.created_at : currentTime;
          if (this.userDataConfig.enable_progress_note_integration != 1 &&
            (result.identity_value_Patient != '' || result.identity_value_Patient != null) &&
            (result.CPRUSERNO != '' || result.CPRUSERNO != null) &&
            (result.CPRUSERNAME != '' || result.CPRUSERNAME != null)) {
            var activityDat = {
              activityName: "Copied the Tagged Message to Filing center",
              activityType: "Filing Center",
              activityDescription: this.userData.displayName + " approved message tagged by " + this.userData.userId + " copied to filing center"
            };
            this._structureService.trackActivity(activityDat);
          }
         
          if (self.userDataConfig.enable_progress_note_integration == 1 && result.enableIntegration == true && ((result.triggerOn == "add-tag") || (result.triggerOn == true && result.tagapprovalRequired != true))) {
            if (result.identity_value_Patient == "" || result.identity_value_Patient == null
              || result.CPRUSERNO == '' || result.CPRUSERNO == null || result.integrationFC == ''
              || result.CPRUSERNAME == '' || result.CPRUSERNAME == null) {
              var messageData = '';
              var messageData1 = '';
              if (result.integrationFC == '' || result.integrationFC == null) {
                messageData1 += "there is no folder selection in Default Outgoing Filing Center for Integration";
              }
              if (result.identity_value_Patient == '' || result.identity_value_Patient == null) {
                messageData += ", MRN ";
                if (result.patient_name && result.patient_name != "") {
                  messageData += " of " + result.patient_name;
                }
              }
              let f1 = 0;
              let f2 = 0;
              if (result.CPRUSERNO == '' || result.CPRUSERNO == null) {
                f1 = 1;
                messageData += ", USERNO";
              }
              if (result.CPRUSERNAME == '' || result.CPRUSERNAME == null) {
                f2 = 1;
                messageData += ", USERNAME";
              }
              if ((f1 == 1 || f2 == 1) && result.staff_name && result.staff_name != "") {
                messageData += " of " + result.staff_name;
              }
              var removeComa = messageData.charAt(0);
              if (removeComa == ',') {
                messageData = messageData.substring(1);
              }
              if (messageData1) {
                var finalMessage = 'Generating Progress Note failed due to missing (' + messageData1 + ')';
              }
              else if (messageData) {
                var finalMessage = 'Generating Progress Note failed due to missing (' + messageData + ')';
              }
              else {
                var finalMessage = '';
              }
              if (finalMessage) {
              
                var activityDatas = {
              
                  activityDescription: this.userData.displayName + " approved message tagged by " + this.userData.userId + "failed copied to filing center due to " + finalMessage + ""
                };
                self._structureService.trackActivity(activityDatas);
              }
            }
          }
          if (!autoApprove) {
            let tagMessageSuccess = 'Your message has been tagged and sent to ' + self.approver + ' for approval';
            self._structureService.notifyMessage({
              messge: tagMessageSuccess,
              type: 'success'
            });
            if (formApproverUserid.length) {
              self._structureService.sentPushNotification(formApproverUserid, 0, 'You have a new message for approval', '', '', '', '');
            } else {
              self._structureService.sentPushNotification(0, 0, 'You have a new message for approval', 'formApprover', '', '', '');
            }
          } else {
            let tagMessageSuccess = 'Your message has been tagged and auto approved';
            self._structureService.notifyMessage({
              messge: tagMessageSuccess,
              type: 'success'
            });
          }
          let filterArray = this.assosiatedPatients.find(x => x.userId === this.selectedAssosiatePatient);
          for (var i = 0; i < this.taggedMessageDetails.length; i++) {
            if (this.selectedTags.indexOf(this.taggedMessageDetails[i].id) > -1) {
              this.taggedMessageDetails[i].tag = true;
              this.taggedMessageDetails[i].getitems = tagNames;
              this.taggedMessageDetails[i].tagSign = (autoApprove) ? "true" : "false";
              if (this.selectedTags.includes(this.taggedMessageDetails[i].id) && filterArray) {
                this.taggedMessageDetails[i].patientFirstName = filterArray.firstname,
                  this.taggedMessageDetails[i].patientLastName = filterArray.lastname,
                  this.taggedMessageDetails[i].patientDisplayName = filterArray.displayname,
                  this.taggedMessageDetails[i].caregiverDisplayName = filterArray.caregiver_displayname,
                  this.taggedMessageDetails[i].patientIdNew = filterArray.userId
              }
            }
          }
        }else if(this._structureService.isMultiAdmissionsEnabled) {
          this._structureService.notifyMessage({
            type: 'error',
            messge: this.toolTipService.getTranslateData('ERROR_MESSAGES.USER_NO_ACCESS_TO_PATIENT_SITE')
          });
        }

        tagNames.forEach(res => {
          let getItems = { "id": res.id, "tagName": res.tagName, "createdAt": cretedTimeDisplay, "type": { "bgColor": res.bgColor, "fontColor": res.fontColor, "typeName": res.typeName }, "createdBy": { "displayName": this.userData.displayName, "avatar": this.profileImageUrl } };
          this.chats[this.editIndex].approvedDetails.approverTags.push(getItems);
        });

        let filterArray = this.assosiatedPatients.find(x => x.userId === this.selectedAssosiatePatient);
        this.chats[this.editIndex].patientIdNew = saveDetails.single;
        if(filterArray){
          this.chats[this.editIndex].patientFirstName = filterArray.firstname;
        this.chats[this.editIndex].patientLastName = filterArray.lastname;
        this.chats[this.editIndex].patientDisplayName = filterArray.displayname;
        this.chats[this.editIndex].caregiverDisplayName = filterArray.caregiver_displaynam;
        }
        this.approverRowId = '';
        //Activity Tracking 
        var activityLogreRouteMessage = this.userData.displayName + "(" + this.userData.userId + ") added tag for the message (" + this.messageId + ") in chat log (" + this.chatRoomId + ")";
        var activityData = {
          activityName: "Tag Message",
          activityType: "forms",
          activityDescription: activityLogreRouteMessage
        };
        this._structureService.trackActivity(activityData);

      });
    } else {
      var message = "Select Values";
      if (!saveDetails.single && this.patientFace) {
        this.tagNotSelected = false;
        this.userNotSelected = true;
        message = "Select a Patient";
      }
      else {
        this.userNotSelected = false;
        this.tagNotSelected = true;
        message = "Select one or more Tags";
      }
    }
  }
  showComments(index){
    $('#comment-disp-'+index).toggle();
  }
  removeTagsFromMessage(tagId,messageId,userId,index,self,i,type,apId){
    swal({
            title: "",
            text:'Are you sure you want to remove this tag',
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonText: "Ok ",
            confirmButtonClass: "btn-warning",
            cancelButtonText: "Cancel",
            closeOnConfirm: true,
            closeOnCancel: true
        },
            function (isConfirm) {
                if (isConfirm) {
                    self.removeTagsFromMessageConfirm(tagId,messageId,userId,index,self,i,type,apId);
                }else{
                    $('.overlay-hidden-scroll').hide();
                }
        });
  }
  removeTagsFromMessageConfirm(tagId,messageId,userId,index,self,i,type,apId){
    let data:any={};
    data.tagid=tagId;
    data.messageId=messageId;
    data.userId=userId;
    data.type=type;
    data.apId=apId;
    data.tenantId = this.userData.tenantId;
    data.user = this.userData.displayName;
    data.createdBy = this.userData.userId;
    if(type==0){
          self.chats[i].attachedTags.forEach(res => {
            if(res.tagId == tagId){
              data.tagName = self.chats[i].attachedTags.tagName;
              let tagName = res.tagName;
              self.chats[i].attachedTags.splice(index, 1);
              this.deleteMessageTag(data, {tagName, messageId});     
            }            
          });
    }else{
      self.chats[i].approvedDetails.approverTags.forEach(res => {
            if(res.id == tagId){
              data.tagName = self.chats[i].approvedDetails.approverTags.tagName;
              let tagName = res.tagName;
              self.chats[i].approvedDetails.approverTags.splice(index, 1);
              this.deleteMessageTag(data, {tagName, messageId});
            }            
          });
    }
  }

  deleteMessageTag(data, params:any) :void {
    this._messageViewerService.deleteMessageTags(data).then((response: any)=>{
      if(response.success) {
        const activityLogReRouteMessage = `${this.userData.displayName} (${this.userData.userId}) removed tag(${params.tagName}) for the message (${params.messageId}) in chat log (${this.chatRoomId})`;
        const activityData = {
            activityName: "Remove Tag Message",
            activityType: "forms",
            activityDescription: activityLogReRouteMessage
        };
        this._structureService.trackActivity(activityData);     
      } else if(this._structureService.isMultiAdmissionsEnabled) {
        this._structureService.notifyMessage({
          messge: this.toolTipService.getTranslateData('ERROR_MESSAGES.USER_NO_ACCESS_TO_PATIENT_SITE'),
          type: CONSTANTS.notificationTypes.error,
          delay: 3000
        });
       }
    }).catch((error:any) => {
      let message = this.toolTipService.getTranslateData('ERROR_MESSAGES.SOMETHING_WENT_WRONG');
      if(error.status.code === 422 && this._structureService.isMultiAdmissionsEnabled) {
        message = this.toolTipService.getTranslateData('ERROR_MESSAGES.USER_NO_ACCESS_TO_PATIENT_SITE');
      } else if(error.data.errors){
        message = error.data.errors[0].message || message;
      }
      this._structureService.notifyMessage({
        messge: message,
        type: CONSTANTS.notificationTypes.error,
        delay: 3000
      });
    });
  }
  /**
   * Method to generate chat report
   * @param redacted Boolean value to check whether the chat log is redacted or not
   * @param moveToFC Boolean value to check whether the chat log is moved to filing center or not
   */
  generateChatReport(redacted: boolean, moveToFC: boolean) {
    NProgress.start();
    const activityData: any = { activityType: 'Filing Center' };
    activityData.activityName = redacted ? 'Copied the Redacted downloaded Chat Log' : 'Copied the downloaded Chat Log';
    this.httpService.doPost(APIs.generateChatReport, { chatRoomId: +this.chatRoomId, redacted, moveToFC }).subscribe(
      (response) => {
        if (response.success && response.data) {
          let timeout = 0;
          const fcFailedStatus = moveToFC && !response.data.moveToFCStatus;
          if (fcFailedStatus) {
            this._structureService.notifyMessage({ message: response.status && response.status.message, type: 'warning' });
            timeout = 1200;
          }
          setTimeout(() => {
            this.handleSuccessfulReportGeneration(`${response.data.fileName}.pdf`, redacted, fcFailedStatus, moveToFC, activityData);
          }, timeout);
        } else {
          this.handleFailedReportGeneration(response.data.errors.message, redacted, activityData);
        }
      },
      () => {
        this.handleFailedReportGeneration(null, redacted, activityData);
      }
    );
  }

  private handleSuccessfulReportGeneration(fileName: string, redacted: boolean, fcFailedStatus: boolean, moveToFC: boolean, activityData: any) {
    NProgress.done();
    const chatReportFileUrl = `${this._structureService.apiBaseUrl}writable/filetransfer/uploads/chat-reports/${fileName}`;
    this.newWindow = window.open(`${this._structureService.serverBaseUrl}/webapp/www/img/gif-loader.gif`);
    (this.newWindow as any).location = chatReportFileUrl;
    const activityDataUpdated = { ...activityData };
    if (moveToFC) {
      activityDataUpdated.activityDescription = `${this.userData.displayName} downloaded the ${redacted ? 'redacted ' : ''}file ${
      !fcFailedStatus ? `and moved to Filing center as ${fileName}` : 'but not moved to Filing center'
      } `;
    } else {
      activityDataUpdated.activityDescription = `${this.userData.displayName} downloaded the ${redacted ? 'redacted ' : ''}file ${fileName}`;
    }
    this._structureService.trackActivity(activityDataUpdated);
  }

  private handleFailedReportGeneration(message: string, redacted: boolean, activityData: any) {
    NProgress.done();
    this._structureService.notifyMessage({
      messge: message || this.toolTipService.getTranslateData('ERROR_MESSAGES.FAILED_TO_GEN_CHAT_REPORT')
    });
    const activityDataUpdated = { ...activityData };
    activityDataUpdated.activityDescription = `${this.userData.displayName} failed to download the ${redacted ? 'redacted ' : ''}file`;
    this._structureService.trackActivity(activityDataUpdated);
  }

  updateImageUrl(target) {
        target.src = this._structureService.imageBaseUrl+'profile-pic-clinician.png';
  }
  
  over(i,t,data,tagList,id){
    let classname='';
    if(id){
      classname = 'enterKeyCheck_'+i;
    }else{
       classname = 'enterKeyCheck__'+i;
    }
    // Add tag creation date and time ******
    // Add tag created by - profile icon and name *****  
    let createdUser='';
    createdUser = tagList.createdBy.displayName;
    let img = this._structureService.imageBaseUrl+tagList.createdBy.avatar;
    if(tagList.createdBy.avatar==""){
      img = this._structureService.imageBaseUrl+'profile-pic-clinician.png';
    }
    if(tagList.createdBy.avatar.indexOf('http')!=-1){
        img = tagList.createdBy.avatar;
    }
    let taggedTime:any = '';
    if(!tagList.createdAt){
      let currentTime:any = new Date().getTime()/1000;
      currentTime = currentTime.toString();
      currentTime = currentTime.split(".");
      tagList.createdAt = currentTime[0];
    }
    if(tagList.createdAt.toString().indexOf('-')!=-1){
      taggedTime = moment(Date.parse(tagList.createdAt)).fromNow();
    } else{
      if(tagList.createdAt.toString().length<=10){
        taggedTime = moment(tagList.createdAt*1000).fromNow();
      }else{
        taggedTime = moment(tagList.createdAt*1).fromNow();
      }
    }  
    let typeNameDisplay='';
    if(tagList.type.typeName && tagList.type.typeName!=null && tagList.type.typeName!='null'){
      typeNameDisplay = ' of type '+tagList.type.typeName;
    }
    let msg = '<div class="popover-chatlog-container">'
    +'<div class="popover-chatlog-img"><img src="'+img+'"></div>'
    +'<div class="popover-chatlog-profile">'
    +'<span class="popover-tag-name-display">'+createdUser+'</span>'
    +'<span class="popover-tag-type-display"> '+tagList.tagName+' '
    + typeNameDisplay+' '
    +' added '+taggedTime+'</span>'
    +'</div>'
    +'</div>';
    $('.'+classname+t).popover({
        html:true,
        content:msg,
        placement:'top'
    });
    
  }

  downloadPdf(event) {
    const value = +event.target.value;
    if (value === 1) {
      this.generateChatReport(false, false); // unredacted, not move to filing center
    } else if (value === 2) {
      this.generateChatReport(true, false); // redacted, not move to filing center
    } else if (value === 3) {
      this.generateChatReport(false, true); // unreacated, move to filing center
    } else if (value === 4) {
      this.generateChatReport(true, true); // redacted, move to filing center
    }
    let self = this;
    setTimeout(function () {
      self.download = 0;
    },100);
  }
  closeTagModal(){
    $('#messageTagModal').modal('hide');
  }
  getAllTags(patitentTenantId){
        this._inboxService.getTagoptions("1",false,patitentTenantId).then((result) => {
            if (result) {
                this.availableTags = result;
                console.log(this.availableTags);
            }
    }).catch((ex) => {
    });
  }
}

