<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Add Message Tag </strong>
        </span>

    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/message']">Message Settings</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/tag-definitions']">Message Tags</a></li>
            <li class="breadcrumb-item">Add Message Tag</li>
        </ol>
        <div class="row">
            <div class="col-lg-12">
                <!-- <h5 class="text-black"><strong>Tag Definitions</strong></h5>
                <p class="text-muted">Element: read <a href="https://datatables.net/" target="_blank">official documentation<small><i class="icmn-link ml-1"></i></small></a></p> -->
                <div class="mb-5">
                    <form class="form-horizontal" [formGroup]="tagDefenitionAdd" novalidate #f="ngForm">
                        <div class="form-body">
                                <div class="form-group row">
                                        <div class="col-md-3">
                                            <label class="control-label">{{'LABELS.MESSAGE_TAG_NAME' | translate}} * <i class="message-tag-name icmn-info" data-toggle="tooltip" data-placement="right"></i></label>
                                        </div>
                                        <div class="col-md-6">
                                            <input  xssInputValidate="{{'LABELS.MESSAGE_TAG_NAME' | translate}}" class="form-control" type="text" placeholder="Message Tag Name"
                                                name="messageTagName" [formControl]="tagDefenitionAdd.controls['tagName']"
                                                id="tag-name">
                                            <div class="alert alert-danger"
                                                *ngIf="!tagDefenitionAdd.controls.tagName.valid && (tagDefenitionAdd.controls.tagName.dirty || tagDefenitionAdd.controls.tagName.touched || f.submitted)">
                                                Message Tag Name cannot be empty.
                                            </div>
        
                                        </div>
                                    </div>

                                    <div class="form-group row">
                                        <div class="col-md-3">
                                            <label class="control-label"
                                            >{{ 'LABELS.ENABLE_NOTIFICATION' | translate }} <i class="default-enableNotification icmn-info" data-toggle="tooltip" data-placement="right"></i
                                            ></label>
                                        </div>
                                        <div class="btn-group col-md-6">
                                            <button
                                            aria-pressed="true"
                                            class="btn btn-outline-success btn-sm"
                                            [ngClass]="{ active: enableNotification }"
                                            (click)="togglePreference('enableNotification', true)"
                                            >
                                            {{ 'BUTTONS.YES' | translate }}
                                            </button>
                                            <button
                                            aria-pressed="false"
                                            class="btn btn-outline-default btn-sm"
                                            [ngClass]="{ active: !enableNotification }"
                                            (click)="togglePreference('enableNotification', false)"
                                            >
                                            {{ 'BUTTONS.NO' | translate }}
                                            </button>
                                        </div>
                                    </div>
                            <!-- ==================================================== -->
                                <div class="form-group row"
                                    [hidden]=" !userData.config.enable_progress_note_integration || userData.config.enable_progress_note_integration == 0">
                                    <div class="col-md-3">
                                        <label class="control-label">Enable Integration
                                                <i
                                                class="default-enableIntegration icmn-info" data-toggle="tooltip"
                                                data-placement="right"></i></label>
                                    </div>
                                    <div class="btn-group col-md-6">
                                        <button aria-pressed="true" class="btn btn-outline-success btn-sm"
                                            [ngClass]="{'active': enableIntegration}"
                                            (click)="togglePreference('enableIntegration',true)">
                                            Yes
                                        </button>
                                        <button aria-pressed="true" class="btn btn-outline-default btn-sm"
                                            [ngClass]="{'active': !enableIntegration}"
                                            (click)="togglePreference('enableIntegration',false)">
                                            No
                                        </button>
                                    </div>
                                </div>
    
                                <div class="form-group row" [hidden]="!enableIntegration || ((!isEnableMessageType || !isEnableMessageCategory) && (!isEnableApiBasedIntegrationCategory  && !isEnableApiBasedIntegrationType)) || (!isEnableApiBasedIntegrationCategory && !isEnableMessageCategory && !isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && !isEnableMessageCategory && !isEnableApiBasedIntegrationType) || (!isEnableApiBasedIntegrationCategory && !isEnableMessageCategory && isEnableApiBasedIntegrationType)">
                                    <div class="col-md-3">
                                        <label class="control-label">{{catLabel }} <i class="default-messagetagcategory icmn-info"
                                                data-toggle="tooltip" data-placement="right"></i> </label>
                                    </div>
                                    <div class="col-md-6">
                                        <input class="form-control" type="text" [readonly] = "(isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType)" placeholder="{{catLabel }}" name="messageTagCategory"
                                            [formControl]="tagDefenitionAdd.controls['tagCategory']" id="tag-category">
                                    </div>                                
                                </div>
    
    
                                <div class="form-group row" [hidden]="!enableIntegration ||((!isEnableMessageType || !isEnableMessageCategory) && (!isEnableApiBasedIntegrationCategory  && !isEnableApiBasedIntegrationType)) || !isEnableMessageCategory || (isEnableApiBasedIntegrationCategory && !isEnableMessageCategory && isEnableApiBasedIntegrationType) || (!isEnableApiBasedIntegrationCategory && !isEnableMessageCategory && isEnableApiBasedIntegrationType) || (!isEnableApiBasedIntegrationCategory && !isEnableMessageCategory && !isEnableApiBasedIntegrationType ) || (isEnableApiBasedIntegrationCategory && !isEnableMessageCategory && !isEnableApiBasedIntegrationType ) || (isEnableApiBasedIntegrationCategory && isEnableMessageCategory && isEnableApiBasedIntegrationType)">
                                    <div class="col-md-9">
                                    <div class="exterLabel">
                                        <label class="control-label">External Message Tag Category Id <i class="default-messagetagcategoryid icmn-info"
                                                data-toggle="tooltip" data-placement="right"></i> </label>
                                    </div>
                                    <div class="exterContent">
                                        <input class="form-control" type="text"  placeholder="External Message Tag Category Id" name="messageTagCategoryId"
                                            [formControl]="tagDefenitionAdd.controls['tagCategoryId']" id="tag-category-id">
                                    </div>
                                    </div>
                                </div>
                                
                                <div class="form-group row" [hidden]="!enableIntegration ||(!isEnableApiBasedIntegrationCategory && !isEnableMessageType && !isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && !isEnableMessageType && !isEnableApiBasedIntegrationType) || (!isEnableApiBasedIntegrationCategory && !isEnableMessageType && isEnableApiBasedIntegrationType)">
                                    <div class="col-md-9">
                                    <div class="exterLabel">
                                        <label class="control-label">{{typeLabel }} <i class="default-messagetagtype icmn-info"
                                                data-toggle="tooltip" data-placement="right"></i></label>
                                    </div>
                                    <div class="exterContent">
                                        <input class="form-control" [readonly] = "(isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType)" type="text" placeholder="{{typeLabel }}" name="messageTagType"
                                            [formControl]="tagDefenitionAdd.controls['tagTypeId']" id="tag-type">
                                                                        
                                    </div>
                                    </div>
                                    <div class="col-md-2 lookup"  *ngIf="enableIntegration && (isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType)">
                                            <button  type="button" (click) = "getExtDocData(1)"><i title="Lookup the message tag types from external systems" class="fa fa-search handButton" ></i></button >
                                            <button type= "button"  (click) = "clearExtDocData()"><i class="fa fa-close handButton" title="Reset the value of external message tag type"></i></button>
                                        </div>
                                </div>
    
                                    <div class="form-group row" [hidden]="!enableIntegration || !isEnableMessageType || (isEnableApiBasedIntegrationCategory && !isEnableMessageType && isEnableApiBasedIntegrationType) || (!isEnableApiBasedIntegrationCategory && !isEnableMessageType && isEnableApiBasedIntegrationType ) || (!isEnableApiBasedIntegrationCategory && !isEnableMessageType && !isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && !isEnableMessageType && !isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && isEnableMessageType && isEnableApiBasedIntegrationType)">
                                        <div class="col-md-3">
                                            <label class="control-label">External Message Tag Type Id <i class="default-messagetagtypeid icmn-info"
                                                    data-toggle="tooltip" data-placement="right"></i></label>
                                        </div>
                                        <div class="col-md-6">
                                            <input class="form-control" type="text" placeholder="External Message Tag Type Id" name="messageTagTypeId"
                                                [formControl]="tagDefenitionAdd.controls['tagType']" id="tag-type-id">                                                                        
                                        </div>
                                    
                                     </div>

                                     <div class="form-group row" [hidden]="!enableIntegration || ((!isEnableMessageCategory && isEnableMessageType) && ( (!isEnableApiBasedIntegrationCategory && !isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType) || (!isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && !isEnableApiBasedIntegrationType)) ||
                                     (!isEnableMessageCategory && !isEnableMessageType) && ( (!isEnableApiBasedIntegrationCategory && !isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType) || (!isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && !isEnableApiBasedIntegrationType)) ||
                                     (isEnableMessageCategory && isEnableMessageType) && ( (!isEnableApiBasedIntegrationCategory && !isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType) || (!isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && !isEnableApiBasedIntegrationType)))">
                                        <div class="col-md-3">
                                            <label class="control-label">External Message Type ID <i class="default-messagecatetypeid icmn-info"
                                                    data-toggle="tooltip" data-placement="right"></i> </label>
                                        </div>
                                        <div class="col-md-6">
                                            <input class="form-control" type="text"  placeholder="External Message Type ID" name="messagecatetypeid" [formControl]="tagDefenitionAdd.controls['messagecatetypeid']" id="messagecatetypeid">
                                        </div>                                
                                    </div>
        
        
                                    <div class="form-group row" [hidden]="!enableIntegration || ((!isEnableMessageCategory && isEnableMessageType) && ( (!isEnableApiBasedIntegrationCategory && !isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType) || (!isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && !isEnableApiBasedIntegrationType)) ||
                                    (!isEnableMessageCategory && !isEnableMessageType) && ( (!isEnableApiBasedIntegrationCategory && !isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType) || (!isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && !isEnableApiBasedIntegrationType)) ||
                                    (isEnableMessageCategory && isEnableMessageType) && ( (!isEnableApiBasedIntegrationCategory && !isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType) || (!isEnableApiBasedIntegrationCategory && isEnableApiBasedIntegrationType) || (isEnableApiBasedIntegrationCategory && !isEnableApiBasedIntegrationType)))">
                                        <div class="col-md-9">
                                        <div class="exterLabel">
                                            <label class="control-label">External Message Category Code <i class="default-messagecatecode icmn-info"
                                                    data-toggle="tooltip" data-placement="right"></i> </label>
                                        </div>
                                        <div class="exterContent">
                                            <input class="form-control" type="text"  placeholder="External Message Category Code" name="messagecatecode"
                                                [formControl]="tagDefenitionAdd.controls['messagecatecode']" id="messagecatecode">
                                        </div>
                                        </div>
                                    </div>
                                
                          
                            <div class="form-group row"
                                [hidden]="userData.config.progress_note_integration_mode === 'webhook' || !userData.config.enable_progress_note_integration || userData.config.enable_progress_note_integration == 0 || !enableIntegration">
                                <div class="col-md-3">
                                    <label class="control-label">Default Outgoing Filing Center for Integration<i
                                            class="default-filename icmn-info" data-toggle="tooltip"
                                            data-placement="right"></i></label>
                                </div>
                                <div class="col-md-6">
                                    <input type="hidden" id="sfilingCenterjson" value="" style="width:300px;" />
                                    <i *ngIf="showCloseInt" title="Clear" (click)="clearFilingCenterjson()"
                                        style="position:absolute;top:14px;right:37px;font-size: 9px;cursor: pointer;"
                                        class="icmn-cross"></i>
                                    <select style="display:none;" class="form-control" [(ngModel)]="folderName"
                                        [formControl]="tagDefenitionAdd.controls['filingcenter']"
                                        name="filingcenterjson" id="filingcenterjson">
                                        <option *ngFor="let g of folderLists;" [ngValue]="g">
                                            {{g.folderName}}
                                        </option>
                                    </select>


                                </div>
                            </div>

                            <div class="form-group row"
                                [hidden]=" userData.config.progress_note_integration_mode === 'webhook' || !userData.config.enable_progress_note_integration || userData.config.enable_progress_note_integration == 0 || !enableIntegration">
                                <div class="col-md-3">
                                    <label class="control-label">Default File Name format for Integration<i
                                            class="default-filename icmn-info" data-toggle="tooltip"
                                            data-placement="right"></i></label>
                                </div>
                                <div class="col-md-6">
                                    <input class="form-control" type="text" placeholder="{MRN}-{UniqueNO}"
                                        name="fileSaveFormatIntegration"
                                        [formControl]="tagDefenitionAdd.controls['fileSaveFormatIntegration']"
                                        id="fileSaveFormatIntegration">
                                </div>
                            </div>
                            <div class="form-group row" style="display:none;"
                                [hidden]="!userData.config.enable_progress_note_integration || userData.config.enable_progress_note_integration == 0 || !enableIntegration">
                                <div class="col-md-3">
                                    <label class="control-label">Trigger On </label>
                                    <i class="message-type-tt icmn-info"></i>
                                </div>
                                <div class="col-md-6">
                                    <select class="form-control" [formControl]="" id="triggerOn">
                                        <option value="" [selected]="true">Select Event</option>
                                        <option [selected]="" value="add-tag">Create</option>
                                        <option [selected]="" value="approve-tag">Approve</option>
                                    </select>

                                </div>
                            </div>

                            <!-- ==================================================== -->
                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Message Tag Type <i class="default-tagtype icmn-info"
                                            data-toggle="tooltip" data-placement="right"></i></label>
                                </div>


                                <div class="col-md-6">
                                    <input type="hidden" id="tagtypesss" value="" style="width:300px;" />
                                    <i *ngIf="showClosetag" title="Clear" (click)="cleartagtype()"
                                        style="z-index:9999; position:absolute;top:14px;right:37px;font-size: 9px;cursor: pointer;"
                                        class="icmn-cross"></i>

                                    <select class="form-control selectdd" id="tagtype"> Select Tag Type
                                        <option></option>
                                        <option *ngFor="let type of tagtypeList" data-value='{{type.tagTypeId}}'>
                                            {{type.typeName}} </option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row" *ngIf="isNursingAgencyEnabled==1">
                                <label class="col-md-3 control-label">User Tags
                                    <!-- <i chToolTip="PatientType"></i> -->
                                </label>
                                <div class="col-md-6">
                                    <select name="nursingAgencyUserTag" formControlName="nursingAgencyUserTag"
                                        id="nursing-agency-user-tags" class="form-control select2"
                                        style="display: block;" multiple>
                                        <option value="" disabled>Select User Tag </option>
                                        <option *ngFor="let tag of nursingAgencyTags" value="{{tag.id}}">
                                            {{tag.tag_name }}
                                        </option>
                                    </select>
                                </div>
                            </div>


                            <div class="form-group row" style="display:none;">
                                <div class="col-md-3">
                                    <label class="control-label">Summarize as Outcome Measure <i
                                            class="default-outcomeMeasure icmn-info" data-toggle="tooltip"
                                            data-placement="right"></i></label>
                                </div>
                                <div class="btn-group col-md-6">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': outcomeMeasures}"
                                        (click)="togglePreference('outcomeMeasures',true)">
                                        Yes
                                    </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !outcomeMeasures}"
                                        (click)="togglePreference('outcomeMeasures',false)">
                                        No
                                    </button>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Approval Required <i
                                            class="default-approvalRequired icmn-info" data-toggle="tooltip"
                                            data-placement="right"></i></label>
                                </div>
                                <div class="btn-group col-md-6">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': approvalRequired}"
                                        (click)="togglePreference('approvalRequired',true)">
                                        Yes
                                    </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !approvalRequired}"
                                        (click)="togglePreference('approvalRequired',false)">
                                        No
                                    </button>
                                </div>
                            </div>
                            <div class="form-group row" [hidden]="userTagType =='patient-facing'">
                                <div class="col-md-3">
                                    <label class="control-label">Patient Association <i
                                            class="default-patientFacing icmn-info" data-toggle="tooltip"
                                            data-placement="right"></i></label>
                                </div>
                                <div class="btn-group col-md-6">
                                    <button aria-pressed="true" class="btn btn-outline-success btn-sm"
                                        [ngClass]="{'active': patientFacing}"
                                        (click)="togglePreference('patientFacing',true)">
                                        Yes
                                    </button>
                                    <button aria-pressed="true" class="btn btn-outline-default btn-sm"
                                        [ngClass]="{'active': !patientFacing}"
                                        (click)="togglePreference('patientFacing',false)">
                                        No
                                    </button>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-6">
                                    <button type="submit" (click)="addMessageTag(f)"
                                        class="addmessagetag btn btn-primary">Add</button>
                                    <a [routerLink]="['/message/tag-definitions']" class="btn btn-default">Cancel</a>
                                    <button type="reset" style="display:none;" id="hideReset"
                                        class="btn btn-success">Reset</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade bulk-edit" id="bulk-edit" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
    aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="exampleModalLabel">Message Tag</h4>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close" (click)="closeDynamicModal()" style="margin-right: 7px;"
            >
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                    <div *ngIf="showLoader" class="loading-container">
                            <div class="lds-roller"><div></div><div></div><div></div><div></div><div></div><div></div><div></div><div></div></div>
                            <div class="loading-text">Loading Data...</div>
                        </div>
                <form>
                    <div *ngIf="!showLoader && (noteCount != 0)" class="card-block">
                        <div class="cat__core__card-sidebar">
                        <label><strong>Message Tag Category</strong></label>
                            <div class="doc-cat-tab" >
                                <select class="form-control doc-cat-class"  (change)="onSelectCategory($event)"  id="doc-category" >
                                    <option value="">Select</option>
                                    <option *ngFor="let noteType of noteTypes;let i = index" value="{{noteType.id}}">
                                        {{noteType.value}} </option>
                                </select>
                                <button type= "button" (click) = "getExtDocData(0)"><i class="fa fa-refresh get-ext-data-class handButton" title="Refresh the data from external system"></i></button>

                            </div>
                            <div class=""  *ngIf="msgCount != 0">
                                    <label><strong>Message Tag Type</strong></label>
                                    <select [disabled] = "!enableDocType"  class="form-control doc-type-class" (change)="onSelectType($event)" id="doc-type" >
                                        <option value="">Select</option>
                                        <option *ngFor="let msgTypeSet of msgTypeSet;let i = index" value="{{msgTypeSet.id}}">
                                            {{msgTypeSet.name}}</option>
                                    </select>
                            </div>
                        </div>
                    </div>
                </form>
                <div style="text-align:center;width:100%;" *ngIf="!showLoader && (noteTypesCount == 0 || msgTagTypeCount == 0)">
                    <p>We are experiencing issues with getting response from a third-party service. Please try after some time or check the end point url configured.</p>
                </div>
                <div style="text-align:center;width:100%;" *ngIf="!showLoader && (noteTypesCount == 2 || msgTagTypeCount == 2)">
                    <p>Authentication failed, please check the authentication token configured.</p>
                </div>
                <div style="text-align:center;width:100%;" *ngIf="!showLoader && (noteTypesCount == 1 || msgTagTypeCount == 1)">
                    <p>Invalid or missing required parameters in the partner token to connect the third party application</p>
                 </div>
                 <div style="text-align:center;width:100%;" *ngIf="!showLoader && (noteTypesCount == 3 || msgTagTypeCount == 3)">
                    <p>Invalid or missing required parameters to connect the third party application.</p>
                </div>
                <div style="text-align:center;width:100%;" *ngIf="!showLoader && (noteTypesCount == 4 || msgTagTypeCount == 4)">
                    <p>Invalid or missing required client id to connect the third party application.</p>
                </div>
                
            </div>
            <div class="modal-footer">
                <div class="footer-padding">
                    <button type="button" [disabled] = "!enableSubData" class="btn btn-primary" (click)="setSelectedData()">Ok</button>
                    <button type="button" class="btn btn-primary" (click)="closeDynamicModal()">Close</button>
                </div>
                  </div>
        </div>
    </div>
</div>
</section>
<!-- END: tables/datatables -->
