import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MessageViewerComponent } from './message-viewer.citushealth';
import { FormBuilder, FormsModule } from '@angular/forms';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { StructureService } from '../structure.service';
import { MessageViewerService } from './message-viewer.service';
import { ChatService } from '../inbox/chat.service';
import { StaticDataService } from '../static-data.service';
import { UserService } from 'app/services/user/user.service';
import { ToolTipService } from '../tool-tip.service';
import { MessageService } from 'app/services/message-center/message.service';
import { ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '../shared/sharedModule';
import { chatLogdateshortFilterPipe } from './chatlog-date-filter.pipes';
import { CommonTestingModule } from 'app/utils/common-testing.module';
import { CommonModule } from '@angular/common';
import { BrowserModule } from '@angular/platform-browser';
import { NgUploaderModule } from 'ngx-uploader';
import { InboxModule } from '../inbox/inbox.module';
import { InboxService } from '../inbox/inbox.service';
import { PdfViewService } from 'app/structure/pdfview.service';
import { Modal } from '../shared/commonModal';
import { ManageSitesService } from '../manage-sites/manage-sites.service';
import { GlobalDataShareService } from '../../structure/shared/global-data-share.service';
import { of } from 'rxjs/observable/of';
import { APIs } from 'app/constants/apis';
import { HttpService } from 'app/services/http/http.service';

describe('MessageViewerComponent', () => {
  let component: MessageViewerComponent;
  let fixture: ComponentFixture<MessageViewerComponent>;
  let mockStructureService;
  let mockMessageViewerService;
  let mockChatService;
  let mockStaticDataService;
  let mockUserService;
  let mockToolTipService;
  let mockMessageService;
  let mockInboxService;
  let mockPdfViewService;
  let mockModal;
  let mockHttpService;
  beforeEach(() => {
    localStorage.setItem('chatLogDetails', JSON.stringify({ id: '123' }));
    mockStructureService = {
      getCookie: jasmine.createSpy('getCookie'),
      userDataConfig: JSON.stringify({ tenantId: 1 }),
      userDetails: JSON.stringify({ userId: 1, enable_cross_site: '1', config: { enable_multisite: '1' } }),
      apiBaseUrl: jasmine.createSpy('apiBaseUrl'),
      serverBaseUrl: jasmine.createSpy('serverBaseUrl'),
      imageBaseUrl: jasmine.createSpy('imageBaseUrl'),
      notifyMessage: jasmine.createSpy('notifyMessage'),
      trackActivity: jasmine.createSpy('trackActivity').and.returnValue(of({})),
      isMultiAdmissionsEnabled: jasmine.createSpy('isMultiAdmissionsEnabled'),
      getChatMessages: jasmine.createSpy('getChatMessages').and.returnValue(Promise.resolve({})),
      subscribeSocketEvent: jasmine.createSpy('subscribeSocketEvent').and.returnValue(of({}))
    };
    mockChatService = {
      getChatroomMessages: jasmine.createSpy('getChatroomMessages').and.returnValue(Promise.resolve({})),
      getUsersListByRoom: jasmine.createSpy('getUsersListByRoom')
    };
    mockUserService = {
      getAssociatedPatientsLists: jasmine.createSpy('getAssociatedPatientsLists').and.returnValue(of({}))
    };
    mockInboxService = {
      getChatroomMessages: jasmine.createSpy('getChatroomMessages').and.returnValue(Promise.resolve({})),
      getUsersListByRoom: jasmine.createSpy('getUsersListByRoom'),
      getTenantClinicians: jasmine.createSpy('getTenantClinicians').and.returnValue(Promise.resolve({}))
    };
    mockMessageViewerService = {
      getChatMessages: jasmine.createSpy('getChatMessages').and.returnValue(Promise.resolve({})),
      saveMessageComment: jasmine.createSpy('saveMessageComment').and.returnValue(Promise.resolve({})),
      updateMessageDetails: jasmine.createSpy('updateMessageDetails').and.returnValue(Promise.resolve({})),
      deleteMessageTags: jasmine.createSpy('deleteMessageTags').and.returnValue(Promise.resolve({})),
      generateChatReportPdf: jasmine.createSpy('generateChatReportPdf').and.returnValue(Promise.resolve({})),
      generateChatReportMovefilingCenter: jasmine.createSpy('generateChatReportMovefilingCenter').and.returnValue(Promise.resolve({}))
    };
    mockModal = {
      show: jasmine.createSpy('show').and.returnValue({})
    };
    mockPdfViewService = {
      showPdfViewer: jasmine.createSpy('showPdfViewer').and.returnValue(Promise.resolve({}))
    };
    mockStaticDataService = jasmine.createSpyObj('StaticDataService', ['getPriorities']);
    mockToolTipService = jasmine.createSpyObj('ToolTipService', ['getTranslateData', 'getTranslateDataWithParam']);
    mockMessageService = jasmine.createSpyObj('MessageService', ['checkMessageIntegrationStatus']);
    mockHttpService = { doPost: jasmine.createSpy('doPost').and.returnValue(of({})) };
    TestBed.configureTestingModule({
      declarations: [MessageViewerComponent, chatLogdateshortFilterPipe],
      imports: [
        NgUploaderModule,
        InboxModule,
        BrowserModule,
        HttpClientTestingModule,
        CommonModule,
        RouterTestingModule,
        ReactiveFormsModule,
        SharedModule,
        FormsModule,
        CommonTestingModule
      ],
      providers: [
        FormBuilder,
        ManageSitesService,
        GlobalDataShareService,
        { provide: StructureService, useValue: mockStructureService },
        { provide: MessageViewerService, useValue: mockMessageViewerService },
        { provide: ChatService, useValue: mockChatService },
        { provide: StaticDataService, useValue: mockStaticDataService },
        { provide: UserService, useValue: mockUserService },
        { provide: ToolTipService, useValue: mockToolTipService },
        { provide: MessageService, useValue: mockMessageService },
        { provide: InboxService, useValue: mockInboxService },
        { provide: PdfViewService, useValue: mockPdfViewService },
        { provide: Modal, useValue: mockModal },
        { provide: HttpService, useValue: mockHttpService }
      ]
    }).compileComponents();
    fixture = TestBed.createComponent(MessageViewerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });
  beforeEach(() => {
    window.open = jasmine.createSpy('open').and.returnValue({ location: '' } as any);
  });
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize component', () => {
    spyOn(component, 'ngOnInit').and.callThrough();
    component.ngOnInit();
    expect(component.ngOnInit).toHaveBeenCalled();
  });

  it('should call getChatMessages on init', () => {
    const chatRoomId = '123';
    component.chatRoomId = chatRoomId;
    spyOn(component, 'getChatMessages');
    component.ngOnInit();
    expect(component.getChatMessages).toHaveBeenCalledWith(chatRoomId);
  });

  it('should call setPatientDetails', () => {
    const element = { userId: '1', displayName: 'John Doe', dob: '01/01/2000' };
    spyOn(component, 'setPatientDetails').and.callThrough();
    component.setPatientDetails(element);
    expect(component.setPatientDetails).toHaveBeenCalledWith(element);
  });

  it('should call showImage', () => {
    const event = { target: { src: 'image.jpg', getAttribute: () => 'image.jpg' } };
    spyOn(component, 'showImage').and.callThrough();
    component.showImage(event);
    expect(component.showImage).toHaveBeenCalledWith(event);
  });

  it('should call showPdf', () => {
    const event = { target: { getAttribute: () => 'pdf.pdf' } };
    spyOn(component, 'showPdf').and.callThrough();
    component.showPdf(event);
    expect(component.showPdf).toHaveBeenCalledWith(event);
  });

  it('should call showVideo', () => {
    const event = { target: { getAttribute: () => 'video.mp4' } };
    spyOn(component, 'showVideo').and.callThrough();
    component.showVideo(event);
    expect(component.showVideo).toHaveBeenCalledWith(event);
  });

  it('should call getChatMessages', () => {
    const id = '123';
    spyOn(component, 'getChatMessages').and.returnValue(Promise.resolve({getSessionTenant: {chatRoomMessages: []}}));
    component.getChatMessages(id);
    expect(component.getChatMessages).toHaveBeenCalledWith(id);
  });

  it('should call backClicked', () => {
    spyOn(component, 'backClicked').and.callThrough();
    component.backClicked();
    expect(component.backClicked).toHaveBeenCalled();
  });

  it('should call submitComment', () => {
    spyOn(component, 'submitComment').and.callThrough();
    component.submitComment();
    expect(component.submitComment).toHaveBeenCalled();
  });

  it('should call removeTagsFromMessage', () => {
    const tagId = 1;
    const messageId = 1;
    const userId = 1;
    const index = 0;
    const self = component;
    const i = 0;
    const type = 0;
    const apId = 1;
    spyOn(component, 'removeTagsFromMessage').and.callThrough();
    component.removeTagsFromMessage(tagId, messageId, userId, index, self, i, type, apId);
    expect(component.removeTagsFromMessage).toHaveBeenCalledWith(tagId, messageId, userId, index, self, i, type, apId);
  });

  it('should call updateImageUrl', () => {
    const target = { src: '' };
    spyOn(component, 'updateImageUrl').and.callThrough();
    component.updateImageUrl(target);
    expect(component.updateImageUrl).toHaveBeenCalledWith(target);
  });

  xit('should call downloadPdf', () => {
    const event = { target: { value: 1 } };
    spyOn(component, 'downloadPdf').and.callThrough();
    component.downloadPdf(event);
    expect(component.downloadPdf).toHaveBeenCalledWith(event);
  });

  it('should call closeTagModal', () => {
    spyOn(component, 'closeTagModal').and.callThrough();
    component.closeTagModal();
    expect(component.closeTagModal).toHaveBeenCalled();
  });
  it('should generate chat report successfully', () => {
    const response = { success: true, data: { fileName: 'test-file', moveToFCStatus: true } };
    mockHttpService.doPost.and.returnValue(of(response));
    mockStructureService.trackActivity.and.returnValue(of({}));
    component.generateChatReport(false, false);

    expect(mockHttpService.doPost).toHaveBeenCalledWith(APIs.generateChatReport, {
      chatRoomId: +component.chatRoomId,
      redacted: false,
      moveToFC: false
    });
  });

  it('should handle failed chat report generation', () => {
    const response = { success: false, data: { errors: { message: 'Error message' } } };
    mockHttpService.doPost.and.returnValue(of(response));

    component.generateChatReport(false, false);

    expect(mockHttpService.doPost).toHaveBeenCalledWith(APIs.generateChatReport, {
      chatRoomId: +component.chatRoomId,
      redacted: false,
      moveToFC: false
    });
  });

  it('should handle chat report generation with filing center failure', () => {
    const response = { success: true, data: { fileName: 'test-file', moveToFCStatus: false }, status: { message: 'Filing center error' } };
    mockHttpService.doPost.and.returnValue(of(response));
    component.generateChatReport(false, true);

    expect(mockHttpService.doPost).toHaveBeenCalledWith(APIs.generateChatReport, {
      chatRoomId: +component.chatRoomId,
      redacted: false,
      moveToFC: true
    });
  });
});
