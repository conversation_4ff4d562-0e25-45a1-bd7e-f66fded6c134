<!-- START: dashboard alpha -->
<!-- START: dashboard alpha -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Send Broadcast Messages</strong>
        </span>

    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <!--<li class="breadcrumb-item"><a [routerLink]="['/inbox']">Messaging Settings</a></li>-->
            <li class="breadcrumb-item">Send Broadcast Messages</li>
        </ol>
        <div class="row">
            <div class="col-lg-12">
                <div class="mb-5">
                    <!-- <h5 class="text-black"><strong>Add Inventory Subcategory</strong></h5>
                    <p class="text-muted">Element: read <a href="https://v4-alpha.getbootstrap.com/components/forms/" target="_blank">official documentation<small><i class="icmn-link ml-1"></i></small></a></p> -->
                    <!-- Horizontal Form -->

                    <form #messageBroacastForm="ngForm" action="#" [formGroup]="messageBroacast" class="form-horizontal" id="message-broadcast-form">
                        <div class="form-body">
                            <!--<div class="form-group row">
                                <label class="col-md-3 control-label">Make this group public</label>
                                <div class="col-md-3">
                                    
                                    <div class="btn-group" data-toggle="buttons" style="margin-left:25px;">
                                        <label aria-pressed="true" class="btn btn-outline-success btn-sm active">
                                            <input name="example5" type="radio"> Yes
                                        </label>
                                        <label aria-pressed="true" class="btn btn-outline-default btn-sm">
                                            <input name="example5" type="radio"> No
                                        </label>
                                    </div>
                                </div>
                            </div>-->
                            <div class="form-group row" [hidden]="!hideSiteSelection">
                                <label class="col-md-3 control-label">Sites  <i class="site-tooltip icmn-info" ></i></label>

                                <div class="col-md-6">
                                    <app-select-sites [events]="eventsSubject.asObservable()" [hideApplyFilter]=true [filterType]=true (siteIds)="getSiteIds($event)" (hideDropdown)="hideDropdown($event)">
                                    </app-select-sites>
                                </div>
                            </div>
                            <div class="form-group row">
                                <div class="col-md-3">
                                    <label class="control-label">Choose Recipient(s)* </label>
                                    <i class="view-tooltip icmn-info" ></i>
                                </div>

                                <div class="col-md-6">
                                    <select class="form-control select2-tags selectUser" formControlName="userRoles" id="taggedUser" multiple required>
                                            <option *ngFor="let teanntRole of tananntRolesAndTags | caregiverOrAlternate : caregiverOrAlternateArgs" value="{{(teanntRole.role_name ? teanntRole.id : 'tag-'+teanntRole.id)}}"> {{(teanntRole.role_name ? teanntRole.name : teanntRole.tag_name+' [User Tag]')}} </option>
                                    </select>

                                </div>
                            </div>

                            <div class="form-group row">
                                <label class="col-md-3 control-label">Message</label>

                                <div class="col-md-6">
                                    <textarea class="form-control" formControlName="messageText" id="messageText" rows="3" placeholder="Type your broadcast message here">{{selectedOptions}}</textarea>
                                </div>
                                <app-message-priority [messagePriority]= "prioritySelected" (selectedPriority)="selectedPriority($event)"></app-message-priority>
                            </div>
                            
                            <div class="form-group row">
                                <label class="col-md-3 control-label">Attach File</label>
                                <div class="col-md-6">

                                    <!-- <div class="drop-container" ngFileDrop (uploadOutput)="onUploadOutput($event)" [uploadInput]="uploadInput" [ngClass]="{ 'is-drop-over': dragOver }">
                                        <h1>Drag & Drop</h1>
                                    </div> -->
                                    <div class="uploads" *ngIf="files?.length">
                                        <div class="upload-item" *ngFor="let f of files; let i = index;">
                                            <div class="upload-item-top">
                                                <span class="filename">{{ f.name }}</span>
                                                <div class="doc-file-remove">
                                                    <span class="x" (click)="removeFile(f.id)">x</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <label class="upload-button">
                                    <input type="file" class="dropify" ngFileSelect (uploadOutput)="onUploadOutput($event)" [uploadInput]="uploadInput" multiple data-allowed-file-extensions="gif png jpg jpeg jpe mpeg bmp mpg mpe avi mp4 movie qt mov pdf mp3 mp2 doc docx word xl xls xlsx aac amr" data-max-file-size="20M" >
                                    
                                    </label>
                                    <!-- dropify -->
                                    <!-- <button type="button" class="start-upload-btn" (click)="startUpload()">
                                    Start Upload
                                    </button> -->


                                </div>
                            </div>
                        </div>
                        <!-- app.component.html -->
                        <div class="form-actions">
                            <button type="submit" (click)="sendBroadCastMessage()" class="btn btn-primary swal-btn-info" id="send_broadcast">Send</button>
                            <button type="button" (click)="resetBroadCastForm('data')" class="btn btn-default" id="cancel_broadcast" >Cancel</button> </div>
                    </form>
                    <!-- End Horizontal Form -->
                </div>
            </div>
        </div>
    </div>
</section>
<!-- END: forms/basic-forms-elements -->
<!-- END: dashboard alpha -->
