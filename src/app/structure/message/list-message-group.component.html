<!-- START: tables/datatables -->
<section class="card">
  <div class="card-header row">
      <span class="cat__core__title col-md-4">
          <strong>Message Groups</strong>
        </span>
        <div class="col-md-5">
            <div class="col-md-12" style="float: right"[hidden]="!hideSiteSelection">
          <div class="filter-site row">
            <div class="site-label">
    <span>{{ 'LABELS.FILTER_BY_SITES' | translate }}</span>
    </div>
    <div class="col-md-8" style="width: 73%">
    <app-select-sites  [events]="eventsSubject.asObservable()" [filterType]=true (siteIds)="getSiteIds($event)" (hideDropdown)="hideDropdown($event)">
    </app-select-sites>
    </div>
    </div>
    </div>
</div>
<div style="padding-top: 7px;">
          <a [routerLink]="['/message/add-message-groups']" class="pull-right btn btn-sm btn-primary" id="add_msg_gp_lnk">Add Message Group<i class="ml-1"></i></a>
  </div>
  </div>
  <div class="card-block">
      <ol class="breadcrumb">
          <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
          <li class="breadcrumb-item"><a [routerLink]="['/message/message']">Message Center</a></li>
          <li class="breadcrumb-item">Message Groups</li>
      </ol>
      <div class="row">
          <div class="col-lg-12">
              <div class="mb-5 pdg-list">
                    <a class="pull-right btn btn-sm btn-primary" id="archivemg" disabled="disabled">Delete Message Group(s)</a>
                      <div class="wait-loading" *ngIf="dataLoadingMsg">
                          <img src="./assets/img/loader/loading.gif" />
                      </div>
                      <div class="exportData" *ngIf="!dataLoadingMsg" [hidden]="userConfig.enable_export_data != '1'">
                        <button daterangepicker [options]="_SharedService.exportOptions" (selected)="selectedDate($event, daterange)" class="btn btn-sm btn-default load-more">Export</button>
                        </div>

                      <table class="table table-hover" id="dtMsgGroups" width="100%"></table>
              </div>
          </div>
      </div>
  </div>
</section>
<!-- END: tables/datatables -->