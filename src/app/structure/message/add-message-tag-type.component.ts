import { Component, OnInit,ElementRef, Renderer } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { ColorPickerService } from 'ngx-color-picker';
import { isNull } from "util";
declare var $:any;
@Component({
  selector: 'app-add-message-tag-type',
  templateUrl: './add-message-tag-type.component.html'
})
export class AddMessageTagTypeComponent implements OnInit {
   bgColour: string = "#889ba0";
   fontColour: string = "#FFF";
  tagTypeAdd: FormGroup;
  userData;
  tagUserType:any='';
  constructor(

    private route: ActivatedRoute,
    private router: Router,
    private _formBuild: FormBuilder,
    private _structureService: StructureService,
    private _ToolTipService: ToolTipService,
    private cpService: ColorPickerService,
  ) {
    this.tagTypeAdd = new FormGroup({
      tagTypeName: new FormControl(null, Validators.required),
      tagTypeDesc: new FormControl(null),
      tagUserType: new FormControl('', Validators.required),
      bgColor: new FormControl(),
      fontColor: new FormControl(),
    });
   }

  ngOnInit() {

    this.userData = this._structureService.getUserdata();  
    var page = 'message-tag-definitions';
    $(".message-type-tt").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSGRP000010') });

  }


  addMessageTagType(form){
    $('.addmsgtagtype').attr( "disabled", "disabled" );
   console.log(form.value);
   if(!form.valid){    
    
      $('.addmsgtagtype').removeAttr("disabled");
       return false;
   }    
     this._structureService.createMessageTagType(form.value,this.fontColour,this.bgColour).then(
         (res) => {
            let result:any=res;
            if(!isNull(result.createMessageTagType)){
           console.log(res);
       if(res['createMessageTagType'].typeName == "exist"){
        var notify = $.notify('Message Tag Type Name already exists');
        setTimeout(()=> {
            notify.update({'type': 'danger', 'message': '<strong>Message Tag Type Name already exists</strong>'});   
        }, 1000);
       }else{
        var notify = $.notify('Success! Message Tag Type Created');
        setTimeout(()=> {
            notify.update({'type': 'success', 'message': '<strong>Success! Message Tag Type Created</strong>'});   
        }, 1000);

        var activityData = {
          activityName: "add message tag type",
          activityType: "manage message tag type",
          activityDescription: this.userData.displayName+" created message tag type - "+res['createMessageTagType'].typeName
        };    
        this._structureService.trackActivity(activityData);
        this.router.navigate(['/message/tag-type']);
       }
        $('.addmsgtagtype').removeAttr("disabled");
        $('#hideReset').trigger('click');
    }else {
        let activityData = {
            activityName: "Error Create Message Tag Type",
            activityType: "messagetagtype",
            activityDescription: `Error occured while Creating Message Tag Type due to invalid input.`
          };
           $('.addmsgtagtype').removeAttr("disabled");
          this._structureService.notifyMessage({
            messge: this._ToolTipService.getTranslateData('MESSAGES.ERROR_MSG_INVALID_INPUT'),
            delay: 1000,
            type: 'warning'
          });
          this._structureService.trackActivity(activityData);
    }
        
     });
          
   
   
   
 }

}
