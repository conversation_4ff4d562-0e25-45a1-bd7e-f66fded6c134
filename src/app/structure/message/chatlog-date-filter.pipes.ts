import { Component, OnInit, Pipe, PipeTransform } from '@angular/core';
import { DatePipe } from '@angular/common';
declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Pipe({
  name: 'dateshortFilter'
})
export class chatLogdateshortFilterPipe implements PipeTransform {
    transform(value: any): any {
    var shortFormat:any = (new Date(value).toDateString() === new Date().toDateString()) ? 'hh:mm a' : (new Date(value).getFullYear() === new Date().getFullYear()) ? 'MMM dd hh:mm a' : 'MMM dd, y hh:mm a';
    var datePipe = new DatePipe("en-US"); 
    value = datePipe.transform(value, shortFormat);
    return value;
    }
}
 
