import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';

declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Component({
  selector: 'app-messagegroup',
  templateUrl: './addgroupmember.html'
})

export class MessageGroupMemberComponent implements OnInit {
  constructor(private route: ActivatedRoute,
    private router: Router,
    private _structureService: StructureService
  ) { }
  ngOnInit() {

  }
  linkToInbox()
  {
    //console.log("hii clicke meeeeeeeeeeeeeeeee");
    this._structureService.inboxClicked(true);
  }
  gotToMessageGroup() {

    this.router.navigate([this._structureService.previousUrl]);
  }
}

