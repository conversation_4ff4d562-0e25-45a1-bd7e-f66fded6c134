import { NO_ERRORS_SCHEMA } from '@angular/core';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpModule } from '@angular/http';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { Apollo, ApolloModule } from 'apollo-angular';
import { PermissionService } from 'app/services/permission/permission.service';
import { ColorPickerService } from 'ngx-color-picker';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { provideClients } from 'test-utils';
import { AuthService } from '../shared/auth.service';
import { SharedService } from '../shared/sharedServices';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { WorklistIndexdbService } from '../worklists/worklist-indexdb.service';
import { MessageTagTypeEditComponent } from './message-tag-type-edit.component';
import { StoreService } from '../shared/storeService';

describe('MessageTagTypeEditComponent', () => {
  let component: MessageTagTypeEditComponent;
  let fixture: ComponentFixture<MessageTagTypeEditComponent>;
  beforeEach(async(() => {
    TestBed.configureTestingModule({
      providers: [
        StructureService,
        Apollo,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        TranslateService,
        ColorPickerService,
        PermissionService,
        StoreService
      ],
      declarations: [MessageTagTypeEditComponent],
      imports: [
        FormsModule,
        ReactiveFormsModule,
        RouterTestingModule,
        HttpModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF,
        }),
        TranslateModule.forRoot(),
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MessageTagTypeEditComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
});
