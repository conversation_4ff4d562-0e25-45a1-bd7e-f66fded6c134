import { Component, OnInit } from '@angular/core';
import { NavigationExtras, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { CONSTANTS, NOTIFY_DELAY_TIME_COMMON } from 'app/constants/constants';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { SharedService } from '../shared/sharedServices';
import { AssociatePatientModalConfig } from '../shared/associate-patient-modal/associate-patient-modal.interface';
import { SelectedItem } from '../shared/patient-dropdown/patient-dropdown.component.interface';
import { StaticDataService } from '../static-data.service';
import { StoreService, Store } from '../shared/storeService';

declare const $: any;
declare const NProgress: any;

@Component({
  selector: 'app-list-patient-discussion-group',
  templateUrl: './list-patient-discussion-group.component.html',
  styleUrls: ['./logs.css']
})
export class ListPatientDiscussionGroupComponent implements OnInit {
  userConfig; // Required
  activeGroup = null;
  addPatientConfig: AssociatePatientModalConfig = { actions: { showOnlyDetailView: true } }; // Required
  userData: any = {};
  selectSiteId; // Required
  enableMultisite: boolean;
  eventsSubject: Subject<void> = new Subject<void>();
  hideSiteSelection = false;
  selectedSearchFields = [];
  private patientSiteId = '0';
  private patientData = {
    email: '',
    firstname: '',
    lastname: '',
    dob: '',
    cell: '',
    zipcode: '',
    tenantId: '',
    operation: 0,
    dday: null,
    dmonth: null,
    dyear: null,
    mrn: null
  };
  selectedPatientId = '';
  admissionId = '';
  labelSiteFilter = '';
  constructor(
    private router: Router,
    private structureService: StructureService,
    public sharedService: SharedService,
    private toolTipService: ToolTipService,
    private staticDataService: StaticDataService,
    private storeService: StoreService
  ) {
    this.userConfig = this.structureService.getUserdata().config;
    this.userData = JSON.parse(this.structureService.userDetails);
    this.labelSiteFilter = this.staticDataService.getSiteLabelBasedOnMultiAdmission('FILTER_BY_SITES');
  }

  ngOnInit() {
    this.enableMultisite = +this.userData.config.enable_multisite === 1;
    this.hideSiteSelection = !((this.enableMultisite && this.userData.mySites.length === 1) || !this.enableMultisite);
  }

  getSiteIds(data: any) {
    this.selectSiteId = data['siteId'].toString();
  }

  hideDropdown(hideItem: any) {
    this.hideSiteSelection = hideItem.hideItem;
  }

  openPatientCreationModal() {
    $('#assocModal').modal({ backdrop: 'static', keyboard: false });
  }

  handleModalClose(isModalClose): void {
    if (isModalClose) {
      this.closePatientCreationModal();
    }
  }
  closePatientCreationModal() {
    $('#assocModal').modal('hide');
  }

  getPatientData(patientDetails) {
    if (patientDetails.siteId != 0) {
      const formData = patientDetails.formData.value;
      this.patientSiteId = patientDetails.siteId || '0';
      this.patientData.dmonth = formData.dobMonth;
      this.patientData.dday = formData.dobDay;
      this.patientData.dyear = formData.dobYear;
      this.createNewPatient(patientDetails.formData);
    }
  }

  createNewPatient(formData) {
    const form = formData.value;
    const monthNames = ['01', '02', '03', '04', '05', '06', '07', '8', '09', '10', '11', '12'];
    const date = `${monthNames[this.patientData.dmonth]}/${this.patientData.dday}/${this.patientData.dyear}`;
    let mrnNew = `${form.firstName} ${form.lastName} - ${date}`;
    NProgress.start();
    if (this.patientSiteId.toString() !== '0') {
      this.patientSiteId = this.patientSiteId.toString();
    }
    const data = {
      email: form.email,
      firstname: form.firstName,
      lastname: form.lastName,
      dob: $('#dob-date-picker').val(),
      cell: form.cellno,
      zipcode: form.zipcode,
      operation: 0,
      mrn: form.mrn,
      createdBy: this.userData.userId,
      siteIds: +this.patientSiteId
    };
    this.structureService.createAssocpatient(data).subscribe(
      (res) => {
        NProgress.done();
        const result = JSON.parse(res.text());
        if (result.status === 1 && result.already && result.message === 1) {
          setTimeout(() => {
            this.structureService.notifyMessage({
              type: CONSTANTS.notificationTypes.warning,
              messge: `<strong>${this.toolTipService.getTranslateData('MESSAGES.PATIENT_ALREADY_CREATED')}</strong>`
            });
            this.openPatientCreationModal();
          }, NOTIFY_DELAY_TIME_COMMON);
        } else if (result.status === 1 && result.already && result.message === 0) {
          this.structureService.notifyMessage({
            type: CONSTANTS.notificationTypes.warning,
            messge: `<strong>${this.toolTipService.getTranslateData('MESSAGES.EMAIL_USED_BY_ANOTHER_USER')}</strong>`
          });
          this.openPatientCreationModal();
        } else if (result.status === 1 && !result.already) {
          $('#resetpform').trigger('click');
          $('.month, .day, .year').select2();
          $('.month').on('select2:select', (e) => {
            this.patientData.dmonth = $(e.target).val();
            $('.day').select2('open');
          });
          $('.day').on('select2:select', (e) => {
            $('.year').select2('open');
            this.patientData.dday = $(e.target).val();
          });
          $('.year').on('select2:select', (e) => {
            this.patientData.dyear = $(e.target).val();
          });
          if (form && form.mrn) mrnNew += ` [MRN: ${form.mrn}] (Virtual)`;
          this.structureService.notifyMessage({
            type: CONSTANTS.notificationTypes.success,
            messge: `<strong>${this.toolTipService.getTranslateData('SUCCESS_MESSAGES.PATIENT_CREATED')}</strong>`
          });
          const activityData = {
            activityType: 'user creation',
            activityName: 'On the Fly Patient Registration',
            activityDescription: `New on the fly user registered (${mrnNew} ${data.dob}) by ${this.userData.displayName} (${this.userData.userId})`
          };
          this.structureService.trackActivity(activityData);
        }
      },
      () => {
        const activityData = {
          activityType: 'user creation',
          activityName: 'On the Fly Patient Registration failed',
          activityDescription: `New on the fly user registration failed (${mrnNew} ${data.dob}) by ${this.userData.displayName} (${this.userData.userId})`
        };
        this.structureService.trackActivity(activityData);
        this.structureService.notifyMessage({ messge: `<strong>${this.toolTipService.getTranslateData('WARNING.DATA_SAVE_FAILED')}</strong>` });
        this.openPatientCreationModal();
      }
    );
  }

  setPdgIdAndAdmissionId(item: SelectedItem) {
    if (+this.userConfig.enable_multi_admissions && item && item.admissions && item.admissions.length && item.users && item.users.length) {
      this.admissionId = item.admissions[0].id;
      this.selectedPatientId = item.users[0].id;
    } else if (item && item.users && item.users.length && !+this.userConfig.enable_multi_admissions) {
      this.selectedPatientId = item.users[0].id;
    } else {
      this.selectedPatientId = '';
      this.admissionId = '';
    }
  }

  doEdit() {
    this.sharedService.goToInnerPage = true;
    this.sharedService.innerPageFilter = true;
    this.storeService.storeData(Store.PDG_EDIT, { patientId: this.selectedPatientId, admissionId: this.admissionId });
    this.router.navigate(['/message/edit-patient-discussion-groups']);
  }
}
