import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { StructureService } from '../structure.service';

declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;

@Component({
  selector: 'app-edit-message',
  templateUrl: './editmessagegroup.html'
})

export class EditMessageGroupComponent implements OnInit {
  constructor(private route: ActivatedRoute,
    private router: Router,
    private _structureService: StructureService

  ) { }
  ngOnInit() {
    this._structureService.previousUrl = 'message/editmessagegroup';

  }
  addGroupMember() {
    this.router.navigate(['/message/addgroupmember']);
  }
}

