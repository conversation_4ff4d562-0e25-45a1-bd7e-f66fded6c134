import { Component, OnInit,ElementRef, Renderer } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { ColorPickerService } from 'ngx-color-picker';
import { SharedService } from './../shared/sharedServices';
import { StoreService, Store } from '../shared/storeService';
import { isBlank } from 'app/utils/utils';
declare var $:any;
declare var swal:any;
@Component({
  selector: 'app-message-tag-type',
  templateUrl: './message-tag-type.component.html'
})
export class MessageTagTypeComponent implements OnInit {
  dTable;
  tagList = [];
  dataLoadingMsg=false;
  userData;
  private bgColour: string = "#889ba0";
  private fontColour: string = "#FFF";
  tagTypeAdd: FormGroup;
  activeMessageTagType = null;
  crossTenantChangeSubscriber:any;
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private _formBuild: FormBuilder,
    private _structureService: StructureService,
    private _ToolTipService: ToolTipService,
    public _SharedService:SharedService,
    elementRef: ElementRef,
    renderer: Renderer,
    private cpService: ColorPickerService,
    private storeService: StoreService
  ) {
    this.tagTypeAdd = new FormGroup({
      tagTypeName: new FormControl(null, Validators.required),
      tagTypeDesc: new FormControl(null),
      bgColor: new FormControl(),
      fontColor: new FormControl(),
    });

    renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
      console.log(event.target.id );
      if(event.target.id == 'editMsgTypeTag') {
        console.log(this.activeMessageTagType);
        //this.router.navigate(['message/edit-tag-type/'+this.activeMessageTag.id]);
       this.router.navigate(['message/edit-tag-type/'+this.activeMessageTagType.tagTypeId]);
      }
      if(event.target.id == 'deleteMsgTypeTag'){
          swal({
            title: "Are you sure?",
            text: "You are going to remove this message tag type",
            type: "warning",
            showCancelButton: true,
            cancelButtonClass: "btn-default",
            confirmButtonClass: "btn-warning",
            confirmButtonText: "Ok",
            closeOnConfirm: true
        }, () => {

        this.deleteMessageTagType(this.activeMessageTagType.tagTypeId);
        });
      }
          
    });
    this.crossTenantChangeSubscriber = this._SharedService.crossTenantChange.subscribe((onInboxData) => {
      if(this.router.url.indexOf('/message/tag-type') > -1) {
        this.ngOnInit();
      }
    });

   }

  ngOnInit() {
    this.userData = this._structureService.getUserdata();  
    var page = 'message-tag-definitions';
    $(".message-tag").tooltip({ title:  this._ToolTipService.getToolTip(page,'MSGRP00006') });
            this.getMsgTagTypes();
  }
  ngOnDestroy() {
    if(this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
  }

  getMsgTagTypes(){
    this._structureService.getMessageTagtype().then(( data ) => {
      if(data['getSessionTenant']['messageTagTypes']) {
          this.tagList=data['getSessionTenant']['messageTagTypes'];
           this.loadMsgTagTypes();
          }
      })
  }

  // getMsgTagTypes() {
  //   this._structureService.getMessageTagtypeNew().then((data: MessageTagTypesResponse) => {
  //       console.log("--- Message tag type >>>>>>>>> ", data);
        
  //       if (data && 
  //           data.getSessionTenant && 
  //           data.getSessionTenant.messageTagTypesPaginated) {
            
  //           const paginatedData = data.getSessionTenant.messageTagTypesPaginated;
            
  //           // Store the total count (266 in your example)
  //           // this.totalCount = paginatedData.totalCount;
  //           // console.log("TOTAL COUNT------> : ",this.totalCount);
            
  //           if (paginatedData.data) {
  //               this.tagList = paginatedData.data;
  //               this.loadMsgTagTypes();
  //           } else {
  //               console.warn("No data array in response");
  //               this.tagList = [];
  //               this.loadMsgTagTypes();
  //           }
  //       } else {
  //           console.warn("Unexpected data structure received:", data);
  //           this.tagList = [];
  //           this.loadMsgTagTypes();
  //       }
  //   }).catch((error) => {
  //       console.error("Error fetching message tag types:", error);
  //       this.tagList = [];
  //       this.loadMsgTagTypes();
  //   });
  // }

  addMessageTagType(form){
     $('.addmsgtagtype').attr( "disabled", "disabled" );
    console.log(form.value);
    if(!form.valid){    
     
       $('.addmsgtagtype').removeAttr("disabled");
        return false;
    }
    console.log(form.value);
    console.log(this.bgColour);
    console.log(this.fontColour);
    let inArray=[];
    inArray = this.tagList.filter((row)=>{
      if(row.typeName.toLowerCase()==form.value.tagTypeName.toLowerCase()){
        return true;
      }
    });
    console.log(inArray);
    if(inArray && inArray.length>0){
      var notify = $.notify('Message Tag Type already exists');
      setTimeout(()=> {
          notify.update({'type': 'warning', 'message': '<strong>Message Tag Type already exists</strong>'});   
      }, 1000); 
         $('.addmsgtagtype').removeAttr("disabled");
    }else{
      
      this._structureService.createMessageTagType(form.value,this.fontColour,this.bgColour).then(
          (res) => {
        console.log(res);
        // this.tagList.push({tagName:form.value.tagTypeName,tagDesc:form.value.tagTypeDesc,bgColor:this.bgColour,fontColor:this.fontColour});
        this.getMsgTagTypes();
        var notify = $.notify('Success! Message Tag Type Created');
        setTimeout(()=> {
            notify.update({'type': 'success', 'message': '<strong>Success! Message Tag Type Created</strong>'});   
        }, 1000);
         $('.addmsgtagtype').removeAttr("disabled");
         $('#hideReset').trigger('click');
        // this.bgColour = "#889ba0";
        // this.fontColour = "#FFF";
        // $('#hideReset').trigger('click');
      });
           
    }
    
    
  }
  
  deleteMessageTagType(id){
    var params ={};

    this._structureService.updateMessageTagType(params,id,1).subscribe(
          (res) => {
      console.log(res);
      this.getMsgTagTypes();
              var notify = $.notify('Success! Message Tag Type Removed');
        setTimeout(()=> {
            notify.update({'type': 'success', 'message': '<strong>Success! Message Tag Type Removed</strong>'});   
        }, 1000);

        var activityData = {
          activityName: "delete message tag type",
          activityType: "manage message tag type",
          activityDescription: this.userData.displayName+" deleted message tag type "+this.activeMessageTagType.typeName+'('+id+')'
        };    
        this._structureService.trackActivity(activityData);
      
    });
  }

  loadMsgTagTypes(){
  

    if (this.dTable) {
      this.dTable.destroy();
    }else{
      if(this._structureService.previousUrlNow.indexOf("/message/edit-tag-type/")==-1 && this._structureService.previousUrlNow.indexOf("/message/add-message-tag-type")==-1){
        console.log("loadDataSignedDocs : this._structureService.previousUrlNow Enter condition")
        this._structureService.resetDataTable();      }      
    }
    var isTrue = false;    
    if(this.tagList.length > 99){
      isTrue = true;
    }
  this.dTable = $('#dtMessageTagTypes').DataTable({           
    autoWidth: false,
    "order": [[ 1, "asc" ]],
    responsive: true,
    //bDeferRender:true,
    //processing: true,
    // oLanguage: {
    // sLoadingRecords: "Please wait - loading..."
    // },
    retrieve: true,
    //pagination: true,
    serching: true,  
    paging: isTrue,
    stateSave: true,
    bInfo: isTrue,
    lengthMenu: [[100, 250, 500, 1000, -1], [100, 250, 500, 1000, 'All']],            
    data: this.tagList,
    fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
      $(nRow).on('click', () => {
        console.log(aData);
        this.activeMessageTagType = aData;
      });
    },
    columns: [
      {title: "#"},
      {title: "Message Tag Type Name ", data: 'typeName'},
      {title: "Tag Type Description", data: 'typeDescription'},
      {title: "Background Color", data: 'bgColor'},
      {title: "Font Color", data: 'fontColor'},
      {title: "Tag Preview", data: 'fontColor'},
      {title: "Message Tag Type Id ", data: 'tagTypeId'},
      {title: "Actions"}
    ],
    columnDefs: [
    {
      data: null,
      orderable: false,
      width: "5%",
      targets: 0
     },
     { 
       data: null,
       targets: 1,
       width: "20%",
    },
    { 
      data: null,
      targets: 2,
      width: "20%",     
    },
    { 
      data: null,
      targets: 3,
      width: "10%",
      orderable: false, 
      render:(document, type, row)=>{
        return '<div style="border: 1px solid #f5f5f5;width:20px;height:20px;background-color:'+document+'"></div>';
      }
    },
    { 
      data: null,
      targets: 4,
      width: "10%", 
       orderable: false, 
      render:(document, type, row)=>{
        return '<div style="border: 1px solid #f5f5f5;width:20px;height:20px;background-color:'+document+'"></div>';
      }  
    },
    { 
      data: null,
      targets: 5,
      width: "15%",  
       orderable: false, 
      render:(document, type, row)=>{
        if(row.bgColor =="" && row.fontColor ==""){
          return `<div class="tag-list">•
          <span>Progress Note</span>
          </div>`;
        }else{
        return `<div class="tag-type-listing"><div class="tag-list tag-list-log"  >
    <i class="fa fa-caret-left" style="color:${row.bgColor};" aria-hidden="true"><span>.</span></i>
    <span class="message-tag-list-text" style="background-color: ${row.bgColor};color:${row.fontColor};" >Tag name</span>
</div></div>  `;

        }
        
        
      }  
    },
     { 
      data: null,
      targets: 6,
       orderable: false,
      width: "20%",   
      "visible": false,  
    }
    ,{
        data: null,
        orderable: false,
        render: function (document, type, row) {
        let actions = '';                
        actions +=`
        <a id="editMsgTypeTag" href="javascript: void(0);" class="cat__core__link--underlined mr-3"><i id="editMsgTypeTag" class="icmn-pencil"></i> Edit</a>
        <a id="deleteMsgTypeTag" href="javascript: void(0);" class="cat__core__link--underlined mr-3"><i id="deleteMsgTypeTag" class="icmn-cross"></i> Delete</a>
        `
        return actions;
      },
      width: "15%",
      targets: 7
    }]
  });
  if(this.storeService.getStoredData(Store.SEARCH_MESSAGE_TAG_TYPE_LIST) && !isBlank(this.storeService.getStoredData(Store.SEARCH_MESSAGE_TAG_TYPE_LIST))) {
    this.dTable.search(this.storeService.getStoredData(Store.SEARCH_MESSAGE_TAG_TYPE_LIST)).draw();
    this._structureService.notifySearchFilterApplied(true);
   }

   this.dTable.on( 'search.dt', () => {
    const searchValue = this.dTable.search(); 
    this.storeService.storeData(Store.SEARCH_MESSAGE_TAG_TYPE_LIST, searchValue);
    if(isBlank(searchValue)) {
      this.storeService.removeData(Store.SEARCH_MESSAGE_TAG_TYPE_LIST);
      this._structureService.notifySearchFilterApplied(false);
    }
   }).draw();

  this.dTable.on( 'order.dt search.dt', () => {
    if(this.dTable.column(0, {search:'applied', order:'applied'}).nodes() && this.dTable.column(0, {search:'applied', order:'applied'}).nodes().length) {
      this.dTable.column(0, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
          cell.innerHTML = i+1;
      });
    }
  }).draw();
  this.dataLoadingMsg=false;
  }

}
