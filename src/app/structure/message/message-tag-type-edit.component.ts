import { <PERSON>mpo<PERSON>, On<PERSON>ni<PERSON>,<PERSON>ementRef, Renderer } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { ColorPickerService } from 'ngx-color-picker';
// import { SidebarService } from '../../../app/components/menu-sidebar/sidebar.service';
import { isNull } from "util";
// import { SharedStateService } from '../../../app/components/menu-sidebar/shared-state.service';
declare var $:any;

interface MessageTagType {
  typeName: string;
  typeDescription: string;
  userType: string;
  fontColor: string;
  bgColor: string;
  tagTypeId: number;
  createdBy?: number;
  createdOn?: string;
  __typename?: string;
}

interface GraphQLResponse {
  data?: {
      getSessionTenant?: {
          messageTagTypesPaginated?: {
              data: MessageTagType[];
              totalCount?: number;
              __typename?: string;
          };
          __typename?: string;
      };
  };
  errors?: any[];
}

@Component({
  selector: 'app-message-tag-type-edit',
  templateUrl: './message-tag-type-edit.component.html'
})
export class MessageTagTypeEditComponent implements OnInit {
   bgColour: string = "#889ba0";
  fontColour: string = "#FFF";
  tagTypeAdd: FormGroup;
  updateId: number = 0;

  tagList;
  landingFlow = false;
  userData;
  tagTypeName;
  tagTypeDesc;
  tagDetailsList=[];
  tagUserType:any='';
  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private _formBuild: FormBuilder,
    private _structureService: StructureService,
    private _ToolTipService: ToolTipService,
    elementRef: ElementRef,
    renderer: Renderer,
    // private sharedState: SharedStateService,
    private cpService: ColorPickerService) {
      this.tagTypeAdd = new FormGroup({
        tagTypeName: new FormControl(null, Validators.required),
        tagTypeDesc: new FormControl(null),
        tagUserType: new FormControl('', Validators.required),
        bgColor: new FormControl(),
        fontColor: new FormControl(),
      });
     }


ngOnInit() {
  this.getMsgTagTypesDetails();
  this.route.params.subscribe((params: Params) => {
    this.updateId = params['id']; 
    console.log('Update ID:', this.updateId);

    if (this.updateId) {
      this._structureService.getSessionTenantSingleObj(this.updateId)
        .then((data: any) => {
          console.log('API Data:', data);
          
        //   // Simplified null checks with optional chaining
        const tagDetails = 
        data && 
        data.getSessionTenant && 
        data.getSessionTenant.messageTagTypesPaginated && 
        data.getSessionTenant.messageTagTypesPaginated.data && 
        data.getSessionTenant.messageTagTypesPaginated.data[0];
          
           if (tagDetails) {
            console.log('Tag Details:', tagDetails);
            
            // Update component properties
            this.tagTypeName = tagDetails.typeName;
            this.tagTypeDesc = tagDetails.typeDescription || '';
            this.tagUserType = tagDetails.userType;
            this.bgColour = tagDetails.bgColor;
            this.fontColour = tagDetails.fontColor;
           }
        //     // Safely patch form value
            if (this.tagTypeAdd && tagDetails.userType) {
              this.tagTypeAdd.patchValue({
                tagUserType: tagDetails.userType
              });
            }
            
        //     // Initialize tooltip safely
            // if (typeof $ !== 'undefined') {
            //   const page = 'message-tag-definitions';
            //   setTimeout(() => {
            //     $(".message-type-tt").tooltip({ 
            //       title: this._ToolTipService?.getToolTip(page, 'MSGRP000010') || ''
            //     });
            //   }, 0);
            // }
        //   } else {
        //     console.warn('No tag details found in response');
        //   }
        // })
        // .catch((error: any) => {
        //   console.error('Error fetching tag details:', error);
         });
    }
  });
}


  getMsgTagTypesDetails(){
   /* this._structureService.getMessageTagType({}).then((res)=>{

    });*/
  }
  
  naviBack()
  {
    this.router.navigate(['/message/tag-type']);
  }
  updateMessageTagType(form){
    console.log(form.value);
    console.log(this.bgColour);
    console.log(this.fontColour);
    if(!form.valid){    
      return false;
    }
   var params ={"typeName":"","typeDescription":"","userType":"","bgColor":"","fontColor":""};
   params.typeName =form.value.tagTypeName;
   params.typeDescription =form.value.tagTypeDesc;
   params.userType =form.value.tagUserType;
   params.bgColor =this.bgColour;
   params.fontColor=this.fontColour;
    this._structureService.updateMessageTagType(params,this.updateId,0).subscribe(
          (res) => {
            if(!isNull(res.data.updateMessageTagType)){
            console.log(res);
            console.log(res.data);
            console.log(res.data.updateMessageTagType);
            console.log(res.data.updateMessageTagType.typeName);
            if(res.data.updateMessageTagType.typeName=="exist"){
              var notify = $.notify('Error! Message Tag Type already Exists');
              setTimeout(()=> {
                  notify.update({'type': 'danger', 'message': '<strong>Success!  Message Tag Type already Exists</strong>'});   
              }, 1000);
              var activityData = {
                activityName: "update message tag type failed",
                activityType: "manage message tag type",
                activityDescription: this.userData.displayName+" failed the update message tag type- "+form.value.tagTypeName
              };    
              this._structureService.trackActivity(activityData);
            }
            else{
              var notify = $.notify('Success! Message Tag Type Updated');
              setTimeout(()=> {
                  notify.update({'type': 'success', 'message': '<strong>Success! Message Tag Type Updated</strong>'});   
              }, 1000);
        
        var activityData = {
          activityName: "update message tag type",
          activityType: "manage message tag type",
          activityDescription: this.userData.displayName+" updated message tag type- "+form.value.tagTypeName
        };    
        this._structureService.trackActivity(activityData);
        this.router.navigate(['/message/tag-type']);
      }
    } else {
        let activityData = {
            activityName: "Error Update Mssge Tag Type",
            activityType: "messagetagtype",
            activityDescription: `Error occured while updating Message Tag Type due to invalid input.`
          };
          this._structureService.notifyMessage({
            messge: this._ToolTipService.getTranslateData('MESSAGES.ERROR_MSG_INVALID_INPUT'),
            delay: 1000,
            type: 'warning'
          }); 
          this._structureService.trackActivity(activityData);
    }
     
    });
  }

}
