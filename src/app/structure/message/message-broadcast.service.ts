import { Injectable } from '@angular/core';
import { Http, Headers, RequestOptions } from '@angular/http';
import { Subject } from 'rxjs/Subject';
import 'rxjs/add/operator/toPromise';
import { StructureService } from '../structure.service';
import { replaceSingleQuoteWithEmpty } from 'app/utils/utils';
@Injectable()
export class MessageBroadcastService {
data;
sendBroadcastUrl;
getTenantRoles;
iconPath;
getUserTags
  constructor(
    private _http: Http,
    private _structureService:StructureService
  ) {
    
    this.sendBroadcastUrl = _structureService.apiBaseUrl+"citus-health/v4/send-broadcast-message.php";
  }
  getAllTenantRolesForActiveUser(){
    this.getTenantRoles = this._structureService.apiBaseUrl+"citus-health/v4/get-tenant-roles-for-active-users.php";
    var apiConfig = {};
    apiConfig = {url: this.getTenantRoles, requestType: 'http'};
    return this._structureService.requestData(apiConfig);
  }
  getActiveUserTags() {
    this.getUserTags = this._structureService.apiBaseUrl+"citus-health/v4/get-active-user-tags.php";
    var apiConfig = {};
    var userData = this._structureService.getUserdata();
    var apiUrl = this.getUserTags;
    console.log(userData.nursingAgencies);
    apiConfig = {url: apiUrl, requestType: 'http'};
    return this._structureService.requestData(apiConfig);
  }
  sendBroadcastMessage(data){
    /* let headers = new Headers();
    headers.append('Content-Type', 'application/x-www-form-urlencoded');
    headers.append('Authentication-Token', this._structureService.getCookie('authenticationToken'));
    let options = new RequestOptions({ headers: headers });
    const promise = new Promise((resolve, reject) => {
    const apiURL = this.sendBroadcastUrl;
       this._http.post(apiURL,data,options)
        .toPromise()
        .then(
        res => {
          const result = res.json();
          resolve(result);
        }
        );
    });
    return promise; */
    if(this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !== 'undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')){
      data.tenantId = this._structureService.getCookie('crossTenantId');
    }
    data.language = (navigator.language ? navigator.language : '');
    var apiConfig = { url: this.sendBroadcastUrl, requestType: 'http', data: data };
    return this._structureService.requestData(apiConfig);


  }

  fileFormatTypeTofileTag(file, url, name, format, type){
    var tagData = this.fileTypetoFileFormat(url, name, type);
    return tagData;
  }
  fileFormatTypeTofileTagSendBroadcast(file, url, name, format, type, callBack){
    var fileType = type.toLowerCase();
    if (this.is_image(fileType)) {
      this.CreateTagUsingImageMsgBC(url, name, type, function(result, fileData) {
        if(file.name) {
          callBack(result, fileData, 'image', file);
        } else {
          callBack(result, fileData, 'image');
        }  
      });
    } else if (this.is_video(fileType)) {
      this.CreateTagUsingVideoMsgBC(url, name, type, function(result, fileData) {
        if(file.name) {
          callBack(result, fileData, 'video', file);
        } else {
          callBack(result, fileData, 'video');
        }  
      });
    } else if (this.is_audio(fileType)) {
      this.CreateTagUsingAudioMsgBC(url, name, type, function(result, fileData) {
        if(file.name) {
          callBack(result, fileData, 'audio', file);
        } else {
          callBack(result, fileData, 'audio');
        }  
      });
    } else if (this.is_document(fileType)) {
      this.CreateTagUsingDocumentMsgBC(url, name, type, function(result, fileData) {
        if(file.name) {
          callBack(result, fileData, 'document', file);
        } else {
          callBack(result, fileData, 'document');
        }  
      });
    } else if (this.is_pdf(fileType)) {
      this.CreateTagUsingPdfMsgBC(url, name, type, function(result, fileData) {
        if(file.name) {
          callBack(result, fileData, 'pdf', file);
        } else {
          callBack(result, fileData, 'pdf');
        }  
      });
    } else {
      return false;
    }
  }
  fileTypetoFileFormat(url, name, type) {
    var fileType = type.toLowerCase();
    if (this.is_image(fileType)) {
      var tagRes =  this.CreateTagUsingImage(url, name, type);
      return tagRes;
      //return "image";
    } else if (this.is_video(fileType)) {
      var tagRes =  this.CreateTagUsingVideo(url, name, type);
      return tagRes;
      //return "video";
    } else if (this.is_audio(fileType)) {
      var tagRes =  this.CreateTagUsingAudio(url, name, type);
      return tagRes;
      //return "audio";
    } else if (this.is_document(fileType)) {
      var tagRes =  this.CreateTagUsingDocument(url, name, type);
      return tagRes;
      //return "document"
    } else if (this.is_pdf(fileType)) {
      var tagRes =  this.CreateTagUsingPdf(url, name, type);
      return tagRes;
      //return "pdf"
    } else {
      return false;
    }
  }
  //************* check file with File type start***********************//
  is_image(fileType) {
    var pngMimes = ['image/x-png'];
    var jpegMimes = ['image/jpg', 'image/jpe', 'image/jpeg', 'image/pjpeg'];
    var imgMimes = ['image/gif', 'image/jpeg', 'image/png'];
    if (pngMimes.indexOf(fileType) >= 1) {
      fileType = 'image/png';
    }
    if (jpegMimes.indexOf(fileType) >= 1) {
      fileType = 'image/jpeg';
    }
    if (imgMimes.indexOf(fileType) >= 1) {
      return true;
    } else {
      return false;
    }
  }

  is_video(fileType) {
    if (fileType) {
      var type = fileType.split("/");
      if (type[0] === "video") {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }

  }

  is_audio(fileType) {
    if (fileType) {
      var type = fileType.split("/");
      if (type[0] === "audio") {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  is_document(fileType) {
    var mimeDoc = ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/zip', 'application/msword', 'application/octet-stream', 'application/excel', 'application/vnd.ms-excel', 'application/msexcel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
    if (fileType) {
      if (mimeDoc.indexOf(fileType) >= 0) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  is_pdf(fileType) {
    var mimePdf = ['application/pdf', 'application/x-download'];
    if (fileType) {
      if (mimePdf.indexOf(fileType) >= 0) {
        return true;
      } else {
        return false;
      }
    } else {
      return false;
    }
  }

  CreateTagUsingImage(URL, name, type) {
    var url;
    var callback = [];
    
    var filename = URL.split("/").pop();
    if (URL.indexOf('thumb_180x116/') < 0)
        URL = URL.replace(filename, "thumb_180x116/" + filename);
    var fileDatas = {
        id: "id" + (new Date()).getTime(),
        artist: name,
        title: name,
        duration: "",
        mime: type,
        url: URL
    }
    var data = { type: "image", message: '<img data-mediaType="image" ng-src="' + URL + '" src="' + URL + '" ng-click="showImage($event)" title="' + name + '" src="' + URL + '"/>' };
    callback.push(data);
    return callback;
  }
  CreateTagUsingImageMsgBC(URL, name, type, callback) {
    var url;
    
    var fileDatas = {
        id: "id" + (new Date()).getTime(),
        artist: name,
        title: name,
        duration: "",
        mime: type,
        url: URL
    }
    var data = { type: "image", message: '<img data-mediaType="image" ng-src="' + URL + '" src="' + URL + '" ng-click="showCmisImage($event)" title="' + name + '" src="' + URL + '"/>' };
    callback(data, fileDatas)
  }

  CreateTagUsingVideo(url, name, type) {
      var fileURL, videoTag;
      var callback = [];
      var control = "";
      var mask = "";
      var fileDatas = {};
      
          fileURL = url;
          var duration = "";
          // getFileDuration(file, function(result) {
          //     duration = result;
          // });
          var filename = fileURL.split("/").pop();
          var filenameNotExt = filename.split(".");
          filenameNotExt = filenameNotExt.shift();
          var imageUrl = fileURL.replace(filename, "videothumb/" + filenameNotExt + ".jpg");
          mask = "<img data-mediaType='video' data-src='" + fileURL + "' src='" + imageUrl + "'  ng-click='showVideo($event)' class=''/>";
          
          
          fileDatas = {
              id: "id" + (new Date()).getTime(),
              artist: name,
              title: name,
              duration: duration,
              mime: type,
              url: url,
              imgFile: imageUrl
          }
          var data = { type: "video", fileName: "videothumb/" + filenameNotExt + ".jpg", message: mask };
          callback.push(data);
          return callback;
  }

  CreateTagUsingVideoMsgBC(url, name, type, callback) {
    var fileURL, videoTag;
    var control = "";
    var mask = "";
    var fileDatas = {};
    
        fileURL = url;
        var duration = "";
        // getFileDuration(file, function(result) {
        //     duration = result;
        // });
        var filename = fileURL.split("/").pop();
        var filenameNotExt = filename.split(".");
        filenameNotExt = filenameNotExt.shift();
        var imageUrl = url + '?type=thumbnail';
        mask = "<img data-mediaType='video' data-src='" + fileURL + "' src='" + imageUrl + "'  ng-click='showVideo($event)' class=''/>";
        
        
        fileDatas = {
            id: "id" + (new Date()).getTime(),
            artist: name,
            title: name,
            duration: duration,
            mime: type,
            url: url,
            imgFile: imageUrl
        }
        var data = { type: "video", fileName: "videothumb/" + filenameNotExt + ".jpg", message: mask };
        callback(data, fileDatas);
}
 
  CreateTagUsingAudio(url, name, type) {
      var fileURL;
      var control;
      var fileDatas = {};
      var callback = [];
      fileURL = url;
      var duration = "";
      // getFileDuration(file, function(result) {
      //     duration = result;
      // });
      fileDatas = {
          id: "id" + (new Date()).getTime(),
          artist: name,
          title: name,
          duration: duration,
          mime: type,
          url: fileURL
      }
      var data = {
          type: "audio",
          message: "<audio controls>" + "<source src='" + fileURL + "' type='" + type + "'>" + "<i class='ion-headphone'></i> Audio" + "</audio>"
      };
      
      callback.push(data);
      return callback;
     
  }
  
  CreateTagUsingAudioMsgBC(url, name, type, callback) {
    var fileURL;
    var control;
    var fileDatas = {};
    fileURL = url;
    var duration = "";
    // getFileDuration(file, function(result) {
    //     duration = result;
    // });
    fileDatas = {
        id: "id" + (new Date()).getTime(),
        artist: name,
        title: name,
        duration: duration,
        mime: type,
        url: fileURL
    }
    var data = {
        type: "audio",
        message: "<audio controls>" + "<source src='" + fileURL + "' type='" + type + "'>" + "<i class='ion-headphone'></i> Audio" + "</audio>"
    };
    callback(data, fileDatas);
}
  CreateTagUsingDocument(url, title, type) {
    const name = replaceSingleQuoteWithEmpty(title);
    var callback = [];
    var random = '2.png';
    //this.iconPath = "http://192.168.1.90/call-bell-hybrid-communication/webapp/www/img/";
    this.iconPath = this._structureService.iconPath;
    var filename = url.substring(url.lastIndexOf('/') + 1);
    filename = filename.substring(filename.lastIndexOf('/') + 1);
    filename = filename.substring(filename.lastIndexOf('.') + 1);
    if (["xl", "xls", "xlsx"].indexOf(filename) > -1) {
        random = '4.png';
    }
    var fileDatas = {
        id: "id" + (new Date()).getTime(),
        artist: name,
        title: name,
        duration: "",
        mime: type,
        url: url
    }
    var data = { type: "document", message: "<img data-mediaType='document' data-src='" + url + "' ng-click='showPdfOrDocs($event)' ng-src='./img/doc/" + random + "' title='" + name + "' class='file-thumbnail' src='"+this.iconPath+"doc/" + random + "'/>" };
    callback.push(data);
    return callback;
  }

  CreateTagUsingDocumentMsgBC(url, title, type, callback) {
    const name = replaceSingleQuoteWithEmpty(title);
    var random = '2.png';
    //this.iconPath = "http://192.168.1.90/call-bell-hybrid-communication/webapp/www/img/";
    this.iconPath = this._structureService.iconPath;
    var filename = url.substring(url.lastIndexOf('/') + 1);
    filename = filename.substring(filename.lastIndexOf('/') + 1);
    filename = filename.substring(filename.lastIndexOf('.') + 1);
    if (["xl", "xls", "xlsx"].indexOf(filename) > -1) {
        random = '4.png';
    }
    var fileDatas = {
        id: "id" + (new Date()).getTime(),
        artist: name,
        title: name,
        duration: "",
        mime: type,
        url: url
    }
    var data = { type: "document", message: "<img data-mediaType='document' data-src='" + url + "' ng-click='showCmisPdfOrDocs($event)' ng-src='./img/doc/" + random + "' title='" + name + "' class='file-thumbnail' src='"+this.iconPath+"doc/" + random + "'/>" };
    callback(data, fileDatas);
  }
  
  CreateTagUsingPdfMsgBC(url, title, type, callback) {
    const name = replaceSingleQuoteWithEmpty(title);
    //this.iconPath = "http://192.168.1.90/call-bell-hybrid-communication/webapp/www/img/"; 
    this.iconPath = this._structureService.iconPath;
    console.log('iconPath:---- ',this.iconPath);
    var fileDatas = {
        id: "id" + (new Date()).getTime(),
        artist: name,
        title: name,
        duration: "",
        mime: type,
        url: url
    }
    var data = { type: "pdf", message: "<img data-mediaType='pdf' data-cmisFile='true' data-src='" + url + "' ng-click='showCmisPdfOrDocs($event)' ng-src='./img/pdf/1.png' title='" + name + "' class='file-thumbnail' src='"+this.iconPath+"pdf/1.png'/>" };
    callback(data, fileDatas);
  }
  
  CreateTagUsingPdf(url, title, type) {
    const name = replaceSingleQuoteWithEmpty(title);
    //this.iconPath = "http://192.168.1.90/call-bell-hybrid-communication/webapp/www/img/"; 
    this.iconPath = this._structureService.iconPath;
    console.log('iconPath:---- ',this.iconPath);
    var callback = [];  
    var fileDatas = {
        id: "id" + (new Date()).getTime(),
        artist: name,
        title: name,
        duration: "",
        mime: type,
        url: url
    }
    var data = { type: "pdf", message: "<img data-mediaType='pdf' data-src='" + url + "' ng-click='showPdfOrDocs($event)' ng-src='./img/pdf/1.png' title='" + name + "' class='file-thumbnail' src='"+this.iconPath+"pdf/1.png'/>" };
    callback.push(data);
    return callback;
  }
}