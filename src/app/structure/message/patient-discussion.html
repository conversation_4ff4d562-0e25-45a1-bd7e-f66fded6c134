<!-- START: tables/datatables -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Patient Discussion Groups </strong>
            <!--<a [routerLink]="['/message/messagegroup']" class="pull-right btn btn-sm btn-primary">Add Message Group<i class="ml-1"></i></a>-->
        </span>
    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/dashboard']" >Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message']" >Message Settings </a></li>
            <li class="breadcrumb-item">Patient Discussion Groups</li>
        </ol>
        
    </div>
</section>
<section class="row">
    <div class="col-lg-4">
        <section class="card">
            <!--<div class="card-header">
                <span class="cat__core__title">
                    <strong>Message Groups</strong>
                </span>
            </div>-->
            <div class="card-block">
                <div class="cat__core__card-sidebar">
                    
                    <div class="row">
                        <div class="col-md-8">
                        
                        <div class="cat__apps__messaging__header">
                            <input class="form-control cat__apps__messaging__header__input" placeholder="Search..." />
                            <i class="icmn-search"></i>
                            <button></button>
                        </div>
                        </div>
                         <div class="col-md-4">
                                <button class="btn btn-icon pull-right btn-sm btn-primary mr-2 mb-2" type="button" (click)="addMessageGroup()"><i aria-hidden="true" class="icmn-plus"></i></button>
                                <button class="btn btn-icon pull-right btn-sm btn-default mr-2 mb-2" type="button" (click)="cancel()"><i aria-hidden="true" class="icmn-spinner11"></i></button>
                             
                        </div>
                    </div>
                    <div class="cat__apps__messaging__list">
                        <div *ngFor="let group of groupList">
                        <div class="cat__apps__messaging__tab  messages-tab" (click)="selectGroup(group)" [ngClass]="{'cat__apps__messaging__tab--selected': selected.id===group.id}">
                            <div class="cat__apps__messaging__tab__avatar">
                                <a class="cat__core__avatar cat__core__avatar--50" href="javascript:void(0);">
                                    <img src="{{_structureService.imageBaseUrl}}/thumbs/{{group.createdUser.avatar}}" alt="Alternative text to the image">
                                </a>
                            </div>
                            <div class="cat__apps__messaging__tab__content">
                                <a href="javascript: void(0);" (click)="deleteGroup(group.id);"><small class="cat__apps__messaging__tab__time" ><i class="icmn-cross"></i></small></a>
                                <a href="javascript: void(0);" (click)="editGroupName(group)"><small class="cat__apps__messaging__tab__time mr-3"><i class="icmn-pencil"></i></small></a>
                                <div class="cat__apps__messaging__tab__name">{{group.name}}</div>
                                <div *ngIf="group.isPublic" class="cat__apps__messaging__tab__text">Public Group</div>
                                
                            </div>
                        </div>
                        </div>

                        
                        
                    
                    </div>
                </div>

            </div>
            
        </section>
    </div>
    <div class="col-lg-8">
           <section  class="card">
            <div class="card-header">
                <span *ngIf="editGroup||addGroup" class="cat__core__title">
                     <strong *ngIf="editGroup">Edit Patient Discussion Group</strong>
		     <strong *ngIf="addGroup">Add New Patient Discussion Group</strong>
                </span>
		
                <span *ngIf="!editGroup&&!addGroup" class="cat__core__title">
                    <strong>{{selected.name}} </strong>
                   
                </span>
                <span *ngIf="!editGroup&&selectedisPublic" class="badge badge-success mr-2 mb-2">Public Group</span>
             
                
                
                
            </div>
            <div class="card-block">

            <form class="form-horizontal" [formGroup]="messageGroup">

                
                            
                <div *ngIf="editGroup||addGroup" class="form-group row">
                    <div class="col-md-12">
                        <input  type="text" class="form-control" formControlName="messageGroupName" placeholder="Message Group Name">
                    </div>
                </div>
                <div *ngIf="editGroup||addGroup" class="form-group row">
                    <label class="col-md-3 control-label">Make this Group Public</label>
                    <div class="col-md-3">
                        
                        <div class="btn-group" data-toggle="buttons" style="margin-left:25px;">
                            <button aria-pressed="true" class="btn btn-outline-success btn-sm"  [ngClass]="{'active': selectedisPublic}" (click)="togglePublic(true)">
                                  Yes
                            </button>
                            <button aria-pressed="true" class="btn btn-outline-default btn-sm" [ngClass]="{'active': !selectedisPublic}" (click)="togglePublic(false)" >
                                No
                            </button>
                        </div>
                    </div>
                </div>

                            
                    
                        
                <div class="row">
                    <div class="col-lg-5" >
                            <br>
                        
                        <section class="card">
                            <div class="card-header">
                                <span class="cat__core__title">
                                    <strong>Existing Members</strong>
                                </span>
                            </div>
                            <div class="card-block">
                                <div class="cat__apps__messaging__header">
                                    <input class="form-control cat__apps__messaging__header__input" placeholder="Search..." />
                                    <i class="icmn-search"></i>
                                    <button></button>
                                </div>
                                <div class="member-list">
                                <table class="table table-hover nowrap" id="example1">
                                    
                                    <tbody>
                                        <tr *ngFor="let member of selectedGroupMembers;let i=index">
                                            <td>{{member.displayName}} </td>
                                            <td *ngIf="editGroup"> <a href="javascript: void(0);" (click)="removeMember(i);"><small><i class="icmn-cross"></i></small></a></td>
                                        </tr>
                                    
                                        
                                    </tbody>
                                </table>
                            </div>
                            </div>
                        </section>
                    </div>
                    <div *ngIf="editGroup||addGroup" class="col-lg-5 pull-right" >
                            <br>
                        
                        <section class="card">

                            <div class="card-header">
                                <span class="cat__core__title">
                                    <strong>New Members</strong>
                                </span>
                            </div>
                            <div class="card-block">

                                <div class="cat__apps__messaging__header">
                                    <input class="form-control cat__apps__messaging__header__input" placeholder="Search..." />
                                    <i class="icmn-search"></i>
                                    <button></button>
                                </div>

                                <table class="table table-hover nowrap" id="example1">
                                
                                    <tbody>

                                
                                        <tr *ngFor="let newMember of newMemberList">
                                            <td> {{newMember.displayName}} </td>
                                            <td> <input name="" type="checkbox" (change)="updateCheckedOptions(newMember.id, $event)"> </td>
                                        </tr>
                                            
                                    </tbody>
                                </table>
                            </div>
                        </section>
                            <!-- <input type="button" class="btn btn-sm btn-primary" (click)="addMember();" name="" value="Add"/> -->
                        
                    </div>
                    <div class="col-lg-2 pull-right" >
                        <input style="margin-top:18px;" type="button" class="btn btn-sm btn-primary" (click)="addMember();" name="" value="Add"/>
                    </div>
                </div>

                <div *ngIf="editGroup||addGroup" class="form-actions">
                    <button  type="button" (click)="updateMessageGroup(selected.id)" class="btn btn-primary">Update</button>
		           
                    <button type="button" (click)="cancel()" class="btn btn-default">Cancel</button>
                </div>
                 <div *ngIf="editGroupMessage" class="form-actions"class="alert alert-success">
                                    Updated 
                 </div>
            
                
            </form> 
            
            </div>
        </section>
        
    </div>
</section>
<!-- END: tables/datatables -->
