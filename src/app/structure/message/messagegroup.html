<!-- START: dashboard alpha -->
<!-- START: dashboard alpha -->
<section class="card">
    <div class="card-header">
        <span class="cat__core__title">
            <strong>Add New Message Group</strong>
            <!--<a [routerLink]="['/message/addgroupmember']" class="pull-right btn btn-sm btn-primary">Add Gorup Member <i class="ml-1"></i></a>-->
        </span>

    </div>
    <div class="card-block">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a [routerLink]="['/inbox']">Home</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/message']">Message Settings</a></li>
            <li class="breadcrumb-item"><a [routerLink]="['/message/message']" >Group Messages</a></li>
            <li class="breadcrumb-item">Add Message Group</li>
            
        </ol>
        <div class="row">
               <div class="col-lg-12">
                <div class="mb-5">
                    <!-- <h5 class="text-black"><strong>Add Inventory Subcategory</strong></h5>
                    <p class="text-muted">Element: read <a href="https://v4-alpha.getbootstrap.com/components/forms/" target="_blank">official documentation<small><i class="icmn-link ml-1"></i></small></a></p> -->
                    <!-- Horizontal Form -->

                    <form action="#" class="form-horizontal">
                        <div class="form-body">
                            
                            <div class="form-group row">
                                <div class="col-md-12">
                                    <input class="form-control" placeholder="Message Group Name" type="text">
                                </div>
                            </div>
                            <div class="form-group row">
                                <label class="col-md-3 control-label">Make this Group Public</label>
                                <div class="col-md-3">
                                    
                                    <div class="btn-group" data-toggle="buttons" style="margin-left:25px;">
                                        <label aria-pressed="true" class="btn btn-outline-success btn-sm ">
                                            <input name="example5" type="radio"> Yes
                                        </label>
                                        <label aria-pressed="true" class="btn btn-outline-default btn-sm active">
                                            <input name="example5" type="radio"> No
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-actions">
                                <div class="form-group row">
                                    <h5 class="text-black col-md-9"><strong>Group Members</strong></h5>
                                    <div class="col-md-3 ">
                                        <button [routerLink]="['/message/addgroupmember']" type="button" class="btn btn-sm btn-primary pull-right">Add Group Member</button>
                                        
                                    </div>
                                </div>
                            </div>

                            <table class="table table-hover nowrap" id="example1" width="100%">
                                <thead>
                                    <th>#</th>
                                    <th>Name</th>
                                    <th>Action</th>
                                </thead>
                                <tbody>
                                    <!--<tr class="odd gradeX">-->
                                    <tr class="odd gradeX">
                                        <td> 1</td>
                                        <td> James Antony </td>
                                        <td><a href="javascript: void(0);" class="cat__core__link--underlined"><small><i class="icmn-cross"><!-- --></i></small> Remove</a></td>
                                        
                                    </tr>
                                    <tr class="odd gradeX">
                                        <td> 2</td>
                                        <td> James George </td>
                                        <td><a href="javascript: void(0);" class="cat__core__link--underlined"><small><i class="icmn-cross"><!-- --></i></small> Remove</a></td>
                                        
                                    </tr>
                                </tbody>
                            </table>

                        </div>
                    
                        <div class="form-actions">
                            <button type="button" class="btn btn-primary">Save</button>
                            <button type="button" [routerLink]="['/message/message']" class="btn btn-default">Cancel</button>
                        </div>
                    </form>
                    <!-- End Horizontal Form -->
                </div>
            </div>
        </div>
    </div>
</section>
<!-- END: forms/basic-forms-elements -->
<!-- END: dashboard alpha -->