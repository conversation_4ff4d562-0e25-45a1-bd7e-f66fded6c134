import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { RegistrationService } from '../registration/registration.service';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { InboxService } from './../inbox/inbox.service';
import { filterMessageGroupPipe } from './messagegroup-search.pipes';
import { SharedService } from './../shared/sharedServices';
import { GlobalDataShareService } from './../shared/global-data-share.service';
import { 
	FormBuilder,
	FormGroup,
	FormControl
} from '@angular/forms';
import { Subject } from 'rxjs/Subject';
import { isBlank } from 'app/utils/utils';
import { FilterItems, GroupData, GroupItems, GroupListItem, GroupsListingRequest, MessageGroup, PaginationItems, PatientDiscussionGroup, PdgItems, SortItems } from 'app/models/message-center/messageCenter';
import { MessageService } from 'app/services/message-center/message.service';
import { CONSTANTS } from 'app/constants/constants';
import { Location } from '@angular/common';
import { StoreService, Store } from '../shared/storeService';

declare const $: any;
declare const swal: any;
declare const NProgress: any;
/**
 * TODO: Scope for revamp
 */
@Component({
	selector: 'app-discussiongroup',
	templateUrl: './discussion-group-list.html',
	styleUrls: ['./patient-discussion-group-add-edit.css', './message-common-component.scss' ]
})
export class DiscussionGroupComponent implements OnInit {
  @ViewChild('userSearch') srch:ElementRef;
	editGroupMessage = false;
	addGroupMessage = false;
	searchInboxkeyword;
	membersLoaded =true;
	groupList = [];
	staffList = [];
	staffRolesList =[];
	offset = 0;
	limit = 25;
	allMessageGroups = [];
	selectedGroupMemberIds = [];
	loadingGroups = true;
	hideLoadMore = false;
	otherTenantStaffList = [];
	memberList = [];
	otherTenantMemberList = [];
	selected;
	selectedGroupDetails;
	newMemberList = [];
	newMemberOtherTenantList = [];
	newallUserDetailsInMsgGroup = [];
	groupMemberList = [];
	messageGroupMemberIds
	newMessageGroups;
	newMemberListByRoleWise = [];
	newMemberOtherTenantListByRoleWise = [];
	groupListNames = [];
	newMember;
	existingMemberIds = [];
	disablePatient = false;
	selectedGroupMembers = [];
	selectedGroupNewMembers = [];
	checkedIds = [];
	checkedRoleIds = [];
	removedRoleUsers = [];
	checkedIdsWithRole = [];
	prevText;
	selectedisPublic;
	selectedAllowMultiThread;
	messageGroup: FormGroup;
	updateParams;
	userDetails: any;
	userData;
	allowSave = true;
	privilegeManageAllMessageGroup = false;
	addMemberError = false;
	noMemberError = false;
	blockConfirm = true;
	disableButton = true;
	patientUser = true;
	createdPatientIdIndex: any;
	messageGroups: any = [];
	optionShow: any = 'staffs';
	userListChatwith: any =[];
	memberDataGroupWise: any = [];
	selectedroleBasedStaffs =[];
	MemberListByRoleWise = [];
	config:any = {};
	configData:any = {};
  	clinicalUserDetails;
	allUserDetailsInMsgGroup = [];
	searchText = '';
	searchTexts = '';
	crossTenantOptions = [];
	roleBasedStaffs =[];
	crossTenantName: String;
	newPDGSubscriber: any;
	crossTenantId: any;
	getAllMessageGroupsLoading: Boolean = false;
	getPatientDiscussionGroupsLoading: Boolean = false;
	branchValue;
	selectedTenant: any;
	MessageGroupWithLoader: any = {
		groups: true,
		staffs: true,
		partner: true,
		otherTenantstaff: true
	};
	ispdgs;
    UsergroupIds;
  	chatWithTenantFilter:any = {
    	selectedTenant : null,
    	tenants: [],
    	filterEnabled: false,
    	enabledReset: false
	};
chatWithUsersLoading = true;
clinicianRolesAvaiable = null;
chatWithModalShown:boolean = false;
callFromInitialCountChatWithUserlist:boolean = false;
noMoreItemsAvailable = {
    users: false
};
chatwithPageCount = {
	staffs: 0,
	partner: 0,
    patients: 0
}
loadMoremessage = {
    users: 'Load more'
};
chatWithLoader:any = {
  groups: true,
  staffs: true,
  partner: true,
  otherTenantstaff: true,
  patients: true,
  otherTenantPatients: true
}
usersList:any;
loadMoreSearchValue:any = undefined;
privileges = this._structureService.getCookie('userPrivileges');
crossTenantOptionsChatWith = [];
	enableCommunicationWithInternalstaffs = false;
	internalSiteId: any;
	crossTenatsForBranch = [];
	column3 = false;
	isNursingAgencyEnabled = 0;
	nursingAgencyTags: any = [];
	canEdit = true;
	testArr = [];
	MemberListByRoleWisepartner = [];
	MemberListByRoleWiseStaff = [];
	primaryId ;
	primaryRolename;
  	secondaryId;
	clickedGroup;
	isGrouploadingTime = false;
	dummyCheckedGroupMembers=[];
	messageGroupId:any;
	member_role_detail1:any = [];
	memberRoleStaffs = [];
	nursing_agency_on = false;
	clickedRole=0;
	usersInRole = true;
	replaceText;
	forField;
	updatedId;
	eventsSubject: Subject<void> = new Subject<void>();
	hideSiteSelection:boolean;
	selectSiteId;
	editedSite: any;
	singleSelection: boolean = true;
	multiTenant: boolean;
	selectedBranchId: any;
	multiSiteEnable: boolean;
    crossTenant: boolean;
    preventMultipleCall : boolean = true;
	siteIdHide= false;
	loadMore_flag : boolean = false;
	selectedSiteName: string;
	clickOnAddMemberBtn = false;
	isStaffsloadingTime = false;
	pdgData: { patientId: number, admissionId: string };
	selectedRolesFromGroup: any[];
	pdgName = '';
	resetPdgMembers = false;
		constructor(
		private route: ActivatedRoute,
		private router: Router,
		private _structureService: StructureService,
		private _ToolTipService: ToolTipService,
		private _inboxService: InboxService,
		private _formBuild: FormBuilder,
    public _GlobalDataShareService:GlobalDataShareService,
		public _SharedService: SharedService,
		private messageService: MessageService,
		private location: Location,
		private storeService: StoreService
	) { 
		this.config = this._structureService.userDataConfig;
	    this.configData = JSON.parse(this.config);
		}

	ngOnInit() {
		$("#notFoundRoles").hide();
		this.pdgData = this.storeService.getStoredData(Store.PDG_EDIT) || null;
        this.userDetails = this._structureService.userDetails;
		this.userData = JSON.parse(this.userDetails);		
		this.multiSiteEnable = this.userData.config.enable_multisite == 1 ? true : false;
		this.crossTenantId =(this._structureService.getCookie('crossTenantId')&& this._structureService.getCookie('crossTenantId') !="")?this._structureService.getCookie('crossTenantId'):this.userData.tenantId;
		console.log("crossTenantId: =========="+this.crossTenantId);
		if (
			this._structureService.getCookie('userPrivileges').indexOf('manageAllMessageGroup') != -1 ||
			this._structureService.getCookie('userPrivileges').indexOf('manageTenants') != -1
		) {
			this.privilegeManageAllMessageGroup = true;
		}
		if((this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
		&& this.configData.allow_multiple_organization==1 
		&& this.userData.crossTenantsDetails.length > 1 
		&& this.userData.masterEnabled == '0'
		&& this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
	|| (this.userData.masterEnabled == '1' 
		&& this.userData.isMaster == '1')
	|| (this.userData.masterEnabled == '1' 
		&& this.userData.isMaster == '0'
		&& this.userData.group !='3')
	|| (this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
		&& this.configData.allow_multiple_organization==1 
		&& this.userData.crossTenantsDetails.length > 1 
		&& this.userData.masterEnabled == '0'
		&& this.configData.enable_nursing_agencies_visibility_restrictions == '1'
		&& this.userData.nursing_agencies == '')) {
		  this.chatWithTenantFilter.filterEnabled = true;
		  this.chatWithTenantFilter.setTenantDropDown = true;
		}else {
		  this.chatWithTenantFilter.filterEnabled = false;
		  this.chatWithTenantFilter.tenants = [];
	  }
		if (
			this.userData.organizationMasterId != 0 &&
			this.userData.crossTenantsDetails &&
			this.userData.crossTenantsDetails.length &&
			this.userData.masterEnabled == '0'
		) {
			if (this.userData.crossTenantsDetails.length > 1 
				&& this._structureService.getCookie('userPrivileges').indexOf('allowMultipleOrganizationsStaffCommunication') !== -1 
				&& this.configData.allow_multiple_organization == 1
				&& (this.configData.enable_nursing_agencies_visibility_restrictions != 1 || (this.configData.enable_nursing_agencies_visibility_restrictions == 1 && this.userData.nursing_agencies == ""))
			) {
				this.column3 = true;
				this.crossTenatsForBranch = this.crossTenantOptions = this.userData.crossTenantsDetails;
        this.crossTenantOptionsChatWith = this.userData.crossTenantsDetails;
        this.chatWithTenantFilter.tenants = this.crossTenantOptionsChatWith;
				this.crossTenantOptions.forEach((tenant) => {
					if (tenant.id == this._structureService.getCookie('crossTenantId')) {
						this.crossTenantName = tenant.tenantName;
            this.selectedTenant = tenant;
            this.chatWithTenantFilter.selectedTenant = tenant.id;
            this._GlobalDataShareService.setSelectedTenantDetails(tenant);
					}
				});
			} else {
				this.crossTenatsForBranch = this.crossTenantOptions = [];
				this.column3 = false;
			}
		} else if (this.userData.masterEnabled == '1' && this.userData.isMaster == '1') {
			this.column3 = true;
			this.crossTenatsForBranch = this.crossTenantOptions = this.userData.crossTenantsDetails;
      this.crossTenantOptionsChatWith = this.userData.crossTenantsDetails;
      this.chatWithTenantFilter.tenants = this.crossTenantOptionsChatWith;
			this.crossTenantOptions.forEach((tenant) => {
				if (tenant.id == this._structureService.getCookie('crossTenantId')) {
					this.crossTenantName = tenant.tenantName;
          this.selectedTenant = tenant;
          this.chatWithTenantFilter.selectedTenant = tenant.id;
          this._GlobalDataShareService.setSelectedTenantDetails(tenant);
				}
			});
		} else if (this.userData.masterEnabled == '1' && this.userData.isMaster == '0') {
			this.column3 = true;
			this.crossTenantOptions = this.userData.crossTenantsDetails;
			this.crossTenantOptions.forEach((tenant) => {
				if (tenant.id == this._structureService.getCookie('crossTenantId')) {
					this.crossTenantName = tenant.tenantName;
            this.chatWithTenantFilter.selectedTenant = tenant.id;
            this._GlobalDataShareService.setSelectedTenantDetails(tenant);
          this.selectedTenant = tenant;
            this.chatWithTenantFilter.tenants = this.userData.crossTenantsDetails;
            
        }
      });
      this.crossTenantOptions.forEach((tenant) => {
        if (tenant.id == (((this._structureService.getCookie('crossTenantId')) && (this._structureService.getCookie('crossTenantId') != 'undefined')) ? (this._structureService.getCookie('crossTenantId')) : this.userData.tenantId)) {
            this.selectedTenant = tenant;
            this.chatWithTenantFilter.selectedTenant = tenant.id;
            this._GlobalDataShareService.setSelectedTenantDetails(tenant);
            this.crossTenantOptions = this.userData.crossTenantsDetails;
            this.chatWithTenantFilter.tenants = this.crossTenantOptions;
            this.crossTenantOptions.forEach((tenant) => {
              if (tenant.id == this._structureService.getCookie("crossTenantId")) {
                  this.crossTenantName = tenant.tenantName;
              }
          });
        }
      });
      this.enableCommunicationWithInternalstaffs = true;
      this.internalSiteId = this.userData.master_details.id;
      
     
      if (this.userData.organizationMasterId != 0 
        && this.userData.crossTenantsDetails 
        && this.userData.crossTenantsDetails.length > 1) {
        this.crossTenantOptionsChatWith = this.userData.crossTenantsDetails;
        this.chatWithTenantFilter.tenants = this.crossTenantOptions;
        this.crossTenantOptions.forEach((tenant) => {
            if (tenant.id == this._structureService.getCookie("crossTenantId")) {
                this.crossTenantName = tenant.tenantName;
            }
        });
        if ((this.crossTenantOptions.length > 1
            && this.privileges.indexOf('allowOrganizationSwitching') != -1
            && this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication') !== -1 
            && this.configData.allow_multiple_organization == 1
            && this.userData.masterEnabled == '0'
            && this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
        || (this.crossTenantOptions.length > 1 
            && this.userData.isMaster =='1' 
            && this.userData.masterEnabled == '1')
        || (this.crossTenantOptions.length > 1 
            && this.userData.isMaster =='0' 
            && this.userData.masterEnabled == '1'
            && this.userData.group !='3')
        || (this.crossTenantOptions.length > 1
            && this.privileges.indexOf('allowOrganizationSwitching') != -1
            && this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication') !== -1 
            && this.configData.allow_multiple_organization == 1
            && this.userData.masterEnabled == '0'
            && this.configData.enable_nursing_agencies_visibility_restrictions == 1
            && this.userData.nursing_agencies == "")
        ) {
           if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1') {
                this.internalSiteId = this.userData.master_details.id;
            }
            this.chatWithLoader.otherTenantstaff = true;
            this.chatWithTenantFilter.tenants = this.chatWithTenantFilter.tenants.filter((tenant)=> {
                if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1'){
                    if(this.internalSiteId == tenant.id) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return true;
                }
            });
            if(this.userData.isMaster =='0' && this.userData.masterEnabled == '1') {
                if(this.userData.master_details.id != this.userData.tenantId) {
                    this.crossTenantOptions.map((tenant)=> {
                        if(tenant.id == this.userData.tenantId) {
                            this.chatWithTenantFilter.tenants.unshift(tenant);
                        }
                    });
                }
            }
            
        } else { 
            this.crossTenantOptionsChatWith = [];
            this.chatWithTenantFilter.tenants = [];
            
        }
      }
    } else {
      this.column3 = false;
      this.crossTenatsForBranch = this.crossTenantOptions = [];
    }
    console.log(this.privilegeManageAllMessageGroup);
		if (this.userData.nursing_agencies != '') {
			this.messageGroup = this._formBuild.group({
				messageGroupName: [ '' ],
				messageGroupLastName: [ ''],
				messageGroupDob: [ '' ],
				messageGroupBranch: [ '0' ],
				nursingAgencyUserTag: [''],
				patientUniqueId: [ '' ]
			});
		} else {
			this.messageGroup = this._formBuild.group({
				messageGroupName: [ ''],
				messageGroupLastName: [ '' ],
				messageGroupDob: [ '' ],
				messageGroupBranch: [ '0' ],
				nursingAgencyUserTag: [''],
				patientUniqueId: [ '' ]
			});
		}
				
		

		this._structureService.previousUrl = 'message/editmessagegroup';
        this.getGroups(true);

      setTimeout( ()=> {
        if($("#messageTenantFilter").length) {
            
              $('#messageTenantFilter').select2({
               dropdownParent: $("#staffListblock")
             });
              $("#messageTenantFilter").css("text-overflow", "ellipsis");
             // this.selectElementTenant?$("#chatWithTenantFilter-invite").val(this.selectElementTenant).trigger("change"):$("#chatWithTenantFilter-invite").val(this.chatWithTenantFilter.selectedTenant).trigger("change");
                
           
        } else {
          // this.chatWithTenantFilter.setTenantDropDown = true;
        }
    });
    $('body').on('change','#messageTenantFilter',(event)=>{
		if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles'){
			$("#userSearchTxtRoles").val("");
			$("#notFoundRoles").hide();
		}	
      if(event.target.value)  {
      var previousSelectedTenant = this.chatWithTenantFilter.tenants.find((tenant) => tenant.id == this.chatWithTenantFilter.selectedTenant);
       var currentSelectedTenant = this.chatWithTenantFilter.tenants.find((tenant) => tenant.id == this.chatWithTenantFilter.selectedTenant);
              var activityData = {
                  activityName: "patientdiscussion Group " + ((this.optionShow == 'staffs') ? 'Staff ' : (this.optionShow == 'partner') ? 'partner' : ((this.optionShow == 'patient') ? 'Patient ' : ' ')) +"Tenant Switching",
                  activityType: "messaging",
                  activityDescription: this.userData.displayName + " (" + this.userData.userId + ") has selected tenant " + ((currentSelectedTenant && currentSelectedTenant.tenantName) ? currentSelectedTenant.tenantName + '(' + currentSelectedTenant.id +')' + ((previousSelectedTenant) ? (' from tenant '+ previousSelectedTenant.tenantName + '(' + previousSelectedTenant.id +')') : ''): ''),
                  tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
              };
              this._structureService.trackActivity(activityData);
          this.filterBranch(event);
          
      }
  });
		var page = 'patient-discussion-groups';
		$('.refresh-page').tooltip({ title: this._ToolTipService.getToolTip(page, 'PTDGP00001') });
		$('.new-discussion-group').tooltip({ title: this._ToolTipService.getToolTip(page, 'PTDGP00002') });
		$('.make-public').tooltip({ title: this._ToolTipService.getToolTip(page, 'PTDGP00003') });
		$(".multi-chat-thread").tooltip({ title: this._ToolTipService.getToolTip(page, 'PTDGP00006') });
		$('.clear-btn-img').tooltip({ title: this._ToolTipService.getToolTip(page, 'PTDGP00004') });
		$('.toolSelect').tooltip({ title: this._ToolTipService.getToolTip(page, 'PTDGP00005') });
		$('body').on('keypress', 'input[type=text]', function(e) {
			var code = e.keyCode ? e.keyCode : e.which;
			if (code == 13) {
				e.preventDefault();
			}
		});
		this.newPDGSubscriber = this._SharedService.patientDiscussionGroup.subscribe((clickedItem) => {
			this.getGroups(false);
		});

		/**
      * Get User tags with tag type (Nursing Agency) BioMatrix -  Nursing tags.
      */

		if (this.userData.config.enable_nursing_agencies_visibility_restrictions == 1) {
			//data += "&nursingAgencies=" + userData.nursing_agencies;
			this.getNursingAgencyTags();
			this.isNursingAgencyEnabled = this.userData.config.enable_nursing_agencies_visibility_restrictions;
		}
		this.crossTenant = this.userData.crossTenantsDetails.length > 1 ? true : false;
	}
	emitEventToUserTags(): void {
		this.eventsSubject.next();
		}
		hideDropdown(hideItem : any){
		  this.hideSiteSelection = hideItem.hideItem;
		  }
		  getSiteRoles(inital=false){
			console.log("2222222222222222222222222222222222222")
			var roleslist = this.selected.selectedRoles.toString();
			var status = 0;
			var currentTenant = this._structureService.getCookie('tenantId');
			this._inboxService.getRoleDetailsInit(roleslist,(inital) ? "" : this.selectSiteId).then((roles)=> {
				if (roles) {
				  this.member_role_detail1 = Array.isArray(roles)?roles:[];
				  console.log(this.member_role_detail1,"___________________________")
				 
				  this.member_role_detail1.forEach(role => {
					
					this._structureService.getRoleBasedStaffs(role['id'], status,1,false,role['tenant_id'],this.selectSiteId).then(( data ) => {
					  let parsedResponceData = JSON.parse(JSON.stringify(data));
					  this.memberRoleStaffs = parsedResponceData.getSessionTenant['roleBasedStaffs'];    
					  console.log('this.memberRoleStaffs',this.memberRoleStaffs)
					  
					 if (this.memberRoleStaffs) {
					  this.memberRoleStaffs.forEach(ele => {
						  if(ele != null){
						  ele.assignRoles.forEach(e => {
							console.log("innnnn")
						   
							if(e['isPrimary'] == true){
							  this.primaryId = e['tenantUsersRoleId'];
							  this.primaryRolename = e['roleName'];
							}
							if(e['isPrimary'] == false){
							  this.secondaryId = e['tenantUsersRoleId'];
							}
							
						  });
						  var ten_id;
						  var ten_name;
						  if(currentTenant == role['tenant_id']){
							ten_id = currentTenant;
							ten_name = "";
			  
						  }else{
							ten_id = role['tenant_id'];
							ten_name = role['name'];
						  }
						  console.log("element===========> ",ele);
						  console.log("element===========> ",role['id']," == ",this.secondaryId);
						  // if(role['id']==this.secondaryId){
						  this.newMember = {
								id: "",
								displayName: "",
								role: {},
								tenantId: null,
								tenantName: "",
								seconRoleId:"",
								primaryId:"",
								otherTenantStaff: false,
								naTags: "",
								naTagNames: ""
						  };            
						  this.newMember.role = role['id'];           
						  this.newMember.tenantId = ten_id;
						  this.newMember.tenantName = ten_name;
						  this.newMember.seconRoleId = this.secondaryId;
						  this.newMember.primaryId = this.primaryId;
						  this.newMember.id = ele.id;
						  this.newMember.displayName = ele.displayName;
						  this.newMember.displayname = ele.displayName;
						  this.newMember.citusRoleId = role['citus_role_id'];
		  
						  console.log("this.newMemberthis.newMemberthis.newMember",this.newMember)
		  
						  let y = this.selectedGroupMembers.find(x => x.id != this.newMember.id)
						  console.log("yyyyyyyyyyyyyyyyyyy",y,this.selectedGroupMembers);
					//	  this.selectedGroupMembers.push(this.newMember);
						  if(this.selectedGroupMembers.length > 0){
							  this.noMemberError = false;
							  this.disableButton = false;
							  this.selectedGroupMembers = this.removeDuplicates(this.selectedGroupMembers,"id");
						  }
							  if(y && !y.id){
							  this.userListChatwith = this.userListChatwith.filter(function (members) {
								return members.userid != ele.id;
							  });				
							  }
										  
			  
							var roleData = {
							  id: this.newMember.role,
							  name: role['role_name'],
							  tenantRoleId: this.newMember.role,
							  tenantId:ten_id,
							  tenantName:ten_name,
							  citusRoleId:role['citus_role_id']
							 
							};
		  
							console.log("roleData",roleData)
		  
		  
						   this.setEditcheckedRoleIdsChange(roleData);	
		  
							var user = { id: this.newMember.id, name: this.newMember.displayName, role:this.newMember.role,seconRoleId: this.newMember.seconRoleId};
							this.testArr.push(user);
						  // }
						  } else {
							var roleData = {
							  id: role['id'],
							  name: role['role_name'],
							  tenantRoleId: role['id'],
							  tenantId:role['tenant_id'],
							  tenantName:role['name'],
							  citusRoleId:role['citus_role_id']
							 
							};
		  
							console.log("roleData",roleData)
		  
		  
						   this.setEditcheckedRoleIdsChange(roleData);	
		  
						  }
		  
						});
		  
					  
					} else{
					  var roleData = {
						id: role['id'],
						name: role['role_name'],
						tenantRoleId: role['id'],
						tenantId:role['tenant_id'],
						tenantName:role['name'],
						citusRoleId:role['citus_role_id'],
						role:role['id']			 
					  };
		  
					  console.log("roleData",roleData)
		  
					 this.setEditcheckedRoleIdsChange(roleData);
					 }				
				   
					this.memberRoleStaffs = [];
					});		  
				});   
			  }
			 });
		  }
		  getBranchIds(data:any) {
			if(this.optionShow !='groups' && this.optionShow !='msggroups' && this.chatWithTenantFilter.filterEnabled && this.chatWithTenantFilter.tenants.length && 
			this.userData.config.enable_multisite != 1) {
			this.multiTenant = true;
			this.selectedBranchId = data['siteId'].toString();
			this.search(this.optionShow);
			}
		}
		  getSiteIds(data:any){
			var self = this;
			var fromSite;
			fromSite = self.userData.mySites.length == 1 ? self.userData.mySites[0]['id'].toString() : 
			data['siteId'].toString();
			this.disableButton = (fromSite == 0 || isBlank(fromSite)) ? true : false;
			  if(self.userData.mySites.length != 1 &&self.editedSite && fromSite != 0 && fromSite!= self.editedSite.toString()) {
                  if(this.multiSiteEnable){
				swal(
					{
						title: 'Are you sure?',
						text: this._ToolTipService.getTranslateData('MESSAGES.SITE_CHANGE'),
						type: 'warning',
						showCancelButton: true,
						cancelButtonClass: 'btn-default',
						confirmButtonClass: 'btn-warning',
						confirmButtonText: 'Continue Anyway',
						closeOnConfirm: true
					},
					function(isConfirm) {
						if (isConfirm) {
							self.selectSiteId = data['siteId'].toString();
							console.log("select-site-confirm");
							self.getSiteRoles();
							//to do CHPR-1107
							if(self.optionShow != 'msggroups'){
							self.reloadEditData(self.selectSiteId);
							}
						} else {
							console.log("select-site-second");
							self.emitEventToSelectSites('selectPreviousSite');
							self.getSiteRoles(true);
							//to do CHPR-1107
							if(self.optionShow != 'msggroups'){
							self.reloadEditData(self.editedSite.toString());
							}
						}
					}
                );
              }else{
                self.selectSiteId = data['siteId'].toString();
                console.log("select-site-confirm");
                 self.getSiteRoles();
                 self.reloadEditData(self.selectSiteId);
              }
			  } else {
                  self.selectSiteId = data['siteId'].toString();
                  if(this.preventMultipleCall){
					  console.log("select-site-first");
					  if(!this.multiSiteEnable){
						  if(this.siteIdHide){
					  		self.getSiteRoles(false);
						  }else{
						 	self.getSiteRoles(true);
							 this.siteIdHide = true
						  }
					  }else{
					  self.getSiteRoles(true);
					  }
                    self.reloadEditData(data['siteId'].toString());
                  }
				  
			  }
			}
	onBlurMethodForId(id, f) {
		NProgress.start();
		console.log('blur', this.messageGroup.controls['messageGroupName'].value);
		console.log('blur', this.messageGroup.controls['messageGroupLastName'].value);
		console.log('blur', this.messageGroup.controls['messageGroupDob'].value);
		console.log('blur', this.messageGroup.controls['patientUniqueId'].value);
		var self = this;
		var messageGroupName;
		var messageGroupLastName;
		var messageGroupDob;
		var patientUniqueId;
		var messageGroup;
		var branch;
		messageGroupName = this.messageGroup.controls['messageGroupName'].value;
		messageGroupLastName = this.messageGroup.controls['messageGroupLastName'].value;
		messageGroupDob = this.messageGroup.controls['messageGroupDob'].value;
		patientUniqueId = this.messageGroup.controls['patientUniqueId'].value;
		console.log("patientUniqueId: ", patientUniqueId,this.replaceText,this.forField);
    console.log("patientUniqueId  array: ",this.messageGroup.controls['patientUniqueId'].value);
				patientUniqueId = this.replaceText.replace(this.forField,patientUniqueId);
				this.updatedId = patientUniqueId;
    console.log("patientUniqueId  replaceUniqueId: ",patientUniqueId);
		branch = this.messageGroup.controls['messageGroupBranch'].value;
		var idValue = this.userData.config.field_label_of_patient_association_in_pdg
			? this.userData.config.field_label_of_patient_association_in_pdg
			: 'ID#';
			this.updateMessageGroup(id, f);
	}
	ngOnDestroy() {
		if (this.newPDGSubscriber) {
			this.newPDGSubscriber.unsubscribe();
		}
	}
	checkboxChanged(event,indexing) {
		this.addMemberError = false;
		var $this = $(event.target),
			checked = $this.prop('checked'),
			container = $this.parent(),
			siblings = container.siblings();
			console.log("event.target.name->",event.target.name);
			if(event.target.name=="messageGroup"){
			  // this.isGrouploadingTime = true;
			  this.clickedGroup=String(event.target.value);
			  this.membersLoaded =false;
			  console.log("event messageGroup ->");
			  console.log("----event.target.checked  ->",event.target.checked);
			  if(event.target.checked){
				console.log("Enter event.target.checked ");
				console.log("----event.target.value  ->",event.target.value);
				var value= JSON.parse(event.target.value);
				var messageGroupId =String(value);
				this.messageGroupId=messageGroupId;
				console.log("->",value,typeof value,messageGroupId, typeof messageGroupId);         
				if(this.dummyCheckedGroupMembers.indexOf(messageGroupId)==-1){
				this.dummyCheckedGroupMembers.push(messageGroupId);
				console.log("messageGroupId",messageGroupId);
				}
				if(this.dummyCheckedGroupMembers && this.dummyCheckedGroupMembers.length > 0){
				  this.membersLoaded = true;
				  console.log("->dummyCheckedGroupMembers",this.dummyCheckedGroupMembers); 
				}
				// this.getMessageGroupMembersByGroupId( this.messageGroupId);
				// var selectedMessageGroup = this.memberDataGroupWise.findIndex(group => group.id === String(messageGroupId));
				// console.log("-> after add button",selectedMessageGroup,this.memberDataGroupWise,this.memberDataGroupWise[selectedMessageGroup]);         
				// console.log("->this.selectedGroupMembers",this.selectedGroupMembers);         
				// console.log("->this.selectedGroupMembers",this.selectedGroupMembers);         
				}else{
				  // this.isGrouploadingTime = false;
				  console.log("Enter not event.target.checked ");
				  console.log("->",this.memberDataGroupWise);   
				  var value= JSON.parse(event.target.value);
				  var messageGroupId =String(value);  
				  console.log("this.dummyCheckedGroupMembers.indexOf(messageGroupId)",this.dummyCheckedGroupMembers.indexOf(messageGroupId));
				  let index = this.dummyCheckedGroupMembers.indexOf(messageGroupId);
				  this.dummyCheckedGroupMembers.splice(index, 1);     
				  this.membersLoaded =true;
			  }
			}
		if (event.target.name == "staffRoleWise") {
			var value = JSON.parse(event.target.value);
		    var roleID = value.id;
			if (event.target.checked) {
				this.clickedRole=roleID;
				this.membersLoaded = false;
				this.getRoleBasedStaff(roleID, indexing);
			} else {
          this.newMemberListByRoleWise[indexing]['userList'] = [];         
				console.log("--roleID-",roleID);
				console.log("-------------------------------selectedroleBasedStaffs---before filter--------------------");
				console.log(this.selectedroleBasedStaffs);
				this.selectedroleBasedStaffs = this.selectedroleBasedStaffs.filter(function (selectedRole) {
					return selectedRole.selectedRoleid != roleID;
				});
				console.log("-------------------------------selectedroleBasedStaffs---after filter--------------------");
				console.log(this.selectedroleBasedStaffs);
				this.membersLoaded = true;
			}
		}
		container
			.find('input[type="checkbox"]')
			.prop({
				indeterminate: false,
				checked: checked
			})
			.siblings('label')
			.removeClass('custom-checked custom-unchecked custom-indeterminate')
			.addClass(checked ? 'custom-checked' : 'custom-unchecked');

		this.checkSiblings(container, checked);
	}

	checkSiblings($el, checked) {
		var parent = $el.parent().parent(),
			all = true,
			indeterminate = false;

		$el.siblings().each(function() {
			return (all = $(this).children('input[type="checkbox"]').prop('checked') === checked);
		});

		if (all && checked) {
			parent
				.children('input[type="checkbox"]')
				.prop({
					indeterminate: false,
					checked: checked
				})
				.siblings('label')
				.removeClass('custom-checked custom-unchecked custom-indeterminate')
				.addClass(checked ? 'custom-checked' : 'custom-unchecked');

			this.checkSiblings(parent, checked);
		} else if (all && !checked) {
			indeterminate = parent.find('input[type="checkbox"]:checked').length > 0;

			parent
				.children('input[type="checkbox"]')
				.prop('checked', checked)
				.prop('indeterminate', indeterminate)
				.siblings('label')
				.removeClass('custom-checked custom-unchecked custom-indeterminate')
				.addClass(indeterminate ? 'custom-indeterminate' : checked ? 'custom-checked' : 'custom-unchecked');

			this.checkSiblings(parent, checked);
		} else {
			$el
				.parents('li')
				.children('input[type="checkbox"]')
				.prop({
					indeterminate: true,
					checked: false
				})
				.siblings('label')
				.removeClass('custom-checked custom-unchecked custom-indeterminate')
				.addClass('custom-indeterminate');
		}
	}
	callAccordion(roleID, eve, list) {
		if(this.optionShow == 'msggroups' || this.optionShow ==='groups') {
			this.membersLoaded =false;
			this.isGrouploadingTime = true;
			this.clickedGroup=roleID;
		}
		if ($('.expand-icon-' + roleID).hasClass('fa-plus')) {
			if( list === 'selectedRoles') {
				this.isStaffsloadingTime = true;
				this.getRoleBasedStaff(roleID, eve, true, list);
				this.clickedRole=roleID;
			} else if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles'){
				this.getRoleBasedStaff(roleID,eve,true);
				this.clickedRole=roleID;
			} else if(this.optionShow == 'msggroups' || this.optionShow ==='groups'){
				var messageGroupId = roleID;
				this.getMessageGroupMembersByGroupId(messageGroupId,true);
			  }  
			$('.expand-icon-' + roleID).removeClass('fa-plus');
			$('.expand-icon-' + roleID).addClass('fa-minus');
			$('.sub-item-panel-' + roleID).addClass('showall');
			
		} else {
			this.membersLoaded =true;
			this.isGrouploadingTime = false;	
			this.usersInRole=true;
			$('.expand-icon-' + roleID).removeClass('fa-minus');
			$('.expand-icon-' + roleID).addClass('fa-plus');
			$('.sub-item-panel-' + roleID).removeClass('showall');
		}
	}
	searchOnKeyPress(event){
		var search_key_word=$("#userSearchTxtRoles").val().trim().toLowerCase();
		var key = event.keyCode || event.charCode;
		if( key == 8 || key == 46 ){
		  
			  $("li.roleslisting").filter(function() {
				  $(this).toggle($(this).find("label").text().toLowerCase().indexOf(search_key_word) > -1);
				  
				});
				if($("li.roleslisting").find("label").text().toLowerCase().indexOf(search_key_word)==-1) $("#notFoundRoles").show();
				  else $("#notFoundRoles").hide();
		}else{
		  $("li.roleslisting").filter(function() {
			  $(this).toggle($(this).find("label").text().toLowerCase().indexOf(search_key_word) > -1)
			  
			});
			if($("li.roleslisting").find("label").text().toLowerCase().indexOf(search_key_word)==-1) $("#notFoundRoles").show();
			else $("#notFoundRoles").hide();
			
		}
	}
initialiseStaffOrPatientList(optionShow) {
	if(this.chatWithTenantFilter.setTenantDropDown && this.chatWithTenantFilter.selectedTenant) {
		this.chatWithTenantFilter.setTenantDropDown = false;
		setTimeout( ()=> {
			$('#messageTenantFilter').select2({
				dropdownParent: $("#staffListblock")
			});
			$("#messageTenantFilter").val(this.chatWithTenantFilter.selectedTenant).trigger("change");
			$("#messageTenantFilter").css("text-overflow", "ellipsis");
		});
	}
    console.log('initialiseStaffOrPatientList.optionShow',optionShow);
    this.loadMoreSearchValue = undefined;
    this.callFromInitialCountChatWithUserlist = true;
    this.chatwithPageCount = {
		staffs: 0,
		partner: 0,
        patients: 0
    }
    this.noMoreItemsAvailable = {
        users: false
    };
    this.userListChatwith = [];
    this.clinicalUserDetails = [];
    this.usersList = [];
    var isRoleAvailable = true;
    var clinicianRolesAvaiable = null;
    var setCliniciansRoleAvailableResponse;
   
            if(isRoleAvailable) {
                this.getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,true,false, optionShow, undefined)
            } else {
                this.chatWithUsersLoading = false;
            }
                   
        /** ***********************End Section ****************** */
    
  }
  	getRoleBasedStaff(roleId, eve, populate=false, list = ''){
		let chosenSiteId;
		this.usersInRole=true;
		const roleList = list === 'selectedRoles' ? this.checkedRoleIds : this.newMemberListByRoleWise;
		if (!('userList' in roleList[eve])) {
			roleList[eve]['userList'] = [];
		}   
		chosenSiteId = this.multiTenant ? this.selectedBranchId : (this.selectSiteId == undefined || this.selectSiteId == 0 || isBlank(this.selectSiteId)) ? this.editedSite.toString() : this.selectSiteId;
		this._structureService.getRoleBasedStaffs(roleId, 0, 1,false,this.chatWithTenantFilter.selectedTenant,chosenSiteId).then(( data ) => {
			let parsedResponceData = JSON.parse(JSON.stringify(data));
			this.roleBasedStaffs = parsedResponceData.getSessionTenant['roleBasedStaffs'];    
			if(this.roleBasedStaffs == null){
				this.roleBasedStaffs = [];
				this.usersInRole=false;
			}
			if(this.configData.enable_nursing_agencies_visibility_restrictions == '1' && this.userData.nursing_agencies != '' && this.configData.na_staffs_chat_with_pharmacy == '0'){
				let nur_login = this.userData.nursing_agencies.split(',');
				nur_login.forEach(element => {                
					this.roleBasedStaffs = this.roleBasedStaffs.filter(function (members) {
						if(members.naTags != null && members.naTags != '' && members.naTags.split(',').indexOf(element) != -1){
							return true;                   
						}
					});           	
				});
				if(this.roleBasedStaffs.length == 0){
					this.usersInRole=false;
				}
			} else if(this.configData.enable_nursing_agencies_visibility_restrictions == '1' && this.userData.nursing_agencies != '' && this.configData.na_staffs_chat_with_pharmacy == '1'){
				var nur_login = this.userData.nursing_agencies.split(',');
				nur_login.forEach(element => {                
					this.roleBasedStaffs = this.roleBasedStaffs.filter(function (members) {
						if (members.naTags == null || members.naTags == '' || members.naTags.split(',').indexOf(element) != -1) {
							return true;                   
						} else {
							return false;
						}
					});           
				});
			}
			if(roleList[eve]['userList'].length == 0){  
				this.roleBasedStaffs.forEach(value => {  
				value.selectedRoleid = roleId; 
					var user = { id: value.id, name: value.displayName,naTags:value.naTags,naTagNames:value.naTagNames };
					if (this.checkedIds.indexOf(value.id) === -1) this.checkedIds.push(value.id);  
					roleList[eve]['userList'].push(user);  
				});
			}
			if(!populate){
				this.selectedroleBasedStaffs = [...this.selectedroleBasedStaffs, ...this.roleBasedStaffs];
			}
			if (list === 'selectedRoles') {
				this.checkedRoleIds = roleList;
			} else {
				this.newMemberListByRoleWise = roleList;
			}
			this.membersLoaded =true;
			this.isStaffsloadingTime = false;

		});   
	}
  getStaffRoles(optionShow='') { 
	$("#notFoundRoles").hide();
    /* if (this._structureService.messageGroupsWithPrivilege && this._structureService.messageGroupsWithPrivilege.length) {
      console.log("Enter first ifffffffff");
      console.log(this._structureService.messageGroupsWithPrivilege);
      this.setmessageGroup(this._structureService.messageGroupsWithPrivilege); */
    //} else {
     //this.getAllMessageGroupDetails(this.userData.tenantId, this.userData.userId);
    //}
    var selectedtenant = this.chatWithTenantFilter.selectedTenant ? this.chatWithTenantFilter.selectedTenant :this.userData.tenantId;
    //var selectedTenantName = this.chatWithTenantFilter.selectedTenantName ? this.chatWithTenantFilter.selectedTenantName :this.userData.tenantName;
    this.staffList = [];
   
  this._inboxService.getStaffRolesListByTenantId(selectedtenant,optionShow,this.selectedBranchId).then((data)=> {
      if (data) {
        //this.groupList = data['getSessionTenant'].messageGroupsPagination.data;

       // this.groupListNames = this.groupList;
       this.staffRolesList = Array.isArray(data)?data:[];
       this.staffRolesList.forEach(value => {
        var staffrollData = { id: value.RoleID, displayName: value.RoleName, tenantId:selectedtenant, tenantName:value.TenantName,naTags:value.naTags,naTagNames:value.naTagNames,citusRoleId:value.citus_role_id};
        this.staffList.push(staffrollData);
       });
        this.staffList.slice();       
        this.newMemberListByRoleWise = [];       
        this.newMemberList = [];     
        console.log('this.staffList',this.staffList); 
        if(this.staffList.length > 0){
          this.staffList.forEach(value => {
            if (value.id) {
              this.newMember = {
                id: "",
                displayName: "",
                role: {},
                tenantId: null,
                tenantName: "",
                otherTenantUser: ((value.tenantId && (this.userData.tenantId != value.tenantId)) ? true : false)
              }
              
              console.log('this.staffList',this.staffList); 
              this.newMember.tenantId = value.tenantId;
              this.newMember.tenantName = value.tenantName;              
              var roleData = { id: value.id, name: value.displayName, tenantId:value.tenantId, tenantName:value.tenantName, tenantRoleId:value.tenantId, citusRoleId:value.citusRoleId};            
              console.log('this.roleData',roleData); 
              
              if (!this.newMemberListByRoleWise[value.id]) {
                this.newMemberListByRoleWise[value.id] = {};
              }
              this.newMemberListByRoleWise[value.id]['roleData'] = roleData;
              this.newMemberListByRoleWise[value.id]['tenantId'] = value.tenantId;
              this.newMemberListByRoleWise[value.id]['tenantName'] = value.tenantName;              
            }
          //  this.MemberListByRoleWise =this.newMemberListByRoleWise;
          });
        }  
       console.log('this.newMemberListByRoleWise',this.newMemberListByRoleWise);
        this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter( function(a) {
          return (a && a.tenantId);
		});
		if(optionShow == 'staffroles'){
			this.MemberListByRoleWiseStaff =this.newMemberListByRoleWise;
		  } else if(optionShow == 'partnerroles'){
			this.MemberListByRoleWisepartner =this.newMemberListByRoleWise;
		  }
		console.log("MemberListByRoleWisepartner..............................",this.MemberListByRoleWisepartner)
		if(this.checkedRoleIds.length){
			this.checkedRoleIds.forEach((value)=>{   
			  console.log('this.checkedRoleIdsvalue.id',value.id);
			  console.log('this.newMemberListByRoleWise',this.newMemberListByRoleWise);
		   this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter(function (members) {
			console.log('this.members.roleData.id.id',members.roleData.id);
			console.log('this.members',members);
			return members.roleData.id != value.id;
		  });
		});
	  }

	  
        this.newMemberListByRoleWise.sort(function (a, b) {
          if (a.roleData.name < b.roleData.name) return -1;
          if (a.roleData.name > b.roleData.name) return 1;
          return 0;
        });       
        this.chatWithLoader.otherTenantstaff = false;
      } else {
        this.chatWithLoader.otherTenantstaff = false;
        this._structureService.deleteCookie('authenticationToken');
        this.router.navigate(['/login']);
      }
    });
  }
  getMessageGroupMembersByGroupId(messageGroupId,choose=false){
	this.membersLoaded = false;
	let selectedGroupIds = [];
	let groupItems: GroupItems = {};
    switch (this.optionShow) {
      case 'msggroups':
		if (typeof messageGroupId === 'string') {
			selectedGroupIds.push(+messageGroupId);
			groupItems.groupIds = selectedGroupIds;
		} else {
			if (messageGroupId.length) {
				groupItems.groupIds = messageGroupId.map(item => Number(item));
			}
		}
        break;
      case 'groups':
		if (typeof messageGroupId === 'string') {
			selectedGroupIds.push(+messageGroupId);
			groupItems.patientIds = selectedGroupIds;
		} else {
			if (messageGroupId.length) {
				groupItems.patients = messageGroupId;
			}
		}
        break;
    }
    const reqObj: GroupsListingRequest = {
      data: groupItems
    }
    switch (this.optionShow) {
      case 'msggroups':
        this.messageService.fetchGroupData(reqObj,'messageGroup').subscribe((resp: GroupListItem<MessageGroup>) => {
            this.executeGroupDataById(messageGroupId,resp.data,choose);
        },() => {
          this.MessageGroupWithLoader.groups = false;
        });
        break;
      case 'groups':
        this.messageService.fetchGroupData(reqObj,'patientGroup').subscribe((resp: GroupListItem<MessageGroup>) => {
            this.executeGroupDataById(messageGroupId,resp.data,choose);
        },() => {
            this.MessageGroupWithLoader.groups = false;
        });
        break;
    }
	
}
	showData(data) {
		this.membersLoaded = true;
		$("#notFoundRoles").hide();
		this.dummyCheckedGroupMembers=[];
		this.optionShow = data;
		this.chatWithTenantFilter.selectedTenant="";
    this.searchTexts = "";
    this.srch.nativeElement.value = "";
		this.searchText = this.searchTexts = '';
	if(data=='groups' || data=='msggroups') {
			this.reset(this.optionShow);
	}
    if(this.optionShow == 'staffs' || this.optionShow == 'partner') {
      this.chatWithTenantFilter.enabledReset = false;
      this.setChatWithTenantFilterSelect();
      this.initialiseStaffOrPatientList(this.optionShow);
  }
  if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
	this.chatWithLoader.otherTenantstaff = true;
	 console.log( this.chatWithTenantFilter.filterEnabled);
	  this.setChatWithTenantFilterSelect();    
	  console.log( this.chatWithTenantFilter.filterEnabled);
	  this.getStaffRoles(this.optionShow);    
  }
		//this.setCheckBoxListner();
	}
	assignChatWithTenantFilterData() {
		this.chatWithTenantFilter.filterEnabled = true;
		this.chatWithTenantFilter.enabledReset = true;
		if($("#messageTenantFilter").length) {
			setTimeout( ()=> {
				$('#messageTenantFilter').select2({
					dropdownParent: $("#staffListblock")
				});
				$("#messageTenantFilter").css("text-overflow", "ellipsis");
				if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
				$("#messageTenantFilter").val(this.chatWithTenantFilter.selectedTenant).trigger("change");
				}
			});
		} else {
			this.chatWithTenantFilter.setTenantDropDown = true;
		}
	}
	setChatWithTenantFilterSelect() {
		if(this.selectedTenant) {
			this.chatWithTenantFilter.selectedTenant =  this.selectedTenant.id;
		} else {
			this.chatWithTenantFilter.selectedTenant = this._structureService.getCookie('tenantId');
		}
		switch(this.optionShow) {
			case 'staffs': {
				if((this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
				&& this.configData.allow_multiple_organization==1 
				&& this.userData.crossTenantsDetails.length > 1 
				&& this.userData.masterEnabled == '0'
				&& this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
			|| (this.userData.masterEnabled == '1' 
				&& this.userData.isMaster == '1')
			|| (this.userData.masterEnabled == '1' 
				&& this.userData.isMaster == '0'
				&& this.userData.group !='3')
			|| (this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
				&& this.configData.allow_multiple_organization==1 
				&& this.userData.crossTenantsDetails.length > 1 
				&& this.userData.masterEnabled == '0'
				&& this.configData.enable_nursing_agencies_visibility_restrictions == '1'
				&& this.userData.nursing_agencies == '')) {
				   this.assignChatWithTenantFilterData(); 
				} else {
					this.chatWithTenantFilter.filterEnabled = false;
					this.chatWithTenantFilter.tenants = [];
				}
				break;
			} 
			case 'partner': {
				if((this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
				&& this.configData.allow_multiple_organization==1 
				&& this.userData.crossTenantsDetails.length > 1 
				&& this.userData.masterEnabled == '0'
				&& this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
			|| (this.userData.masterEnabled == '1' 
				&& this.userData.isMaster == '1')
			|| (this.userData.masterEnabled == '1' 
				&& this.userData.isMaster == '0'
				&& this.userData.group !='3')
			|| (this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
				&& this.configData.allow_multiple_organization==1 
				&& this.userData.crossTenantsDetails.length > 1 
				&& this.userData.masterEnabled == '0'
				&& this.configData.enable_nursing_agencies_visibility_restrictions == '1'
				&& this.userData.nursing_agencies == '')) {
				   this.assignChatWithTenantFilterData(); 
				} else {
					this.chatWithTenantFilter.filterEnabled = false;
					this.chatWithTenantFilter.tenants = [];
				}
				break;
			}
			case 'staffroles': {
				if((this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
				&& this.configData.allow_multiple_organization==1 
				&& this.userData.crossTenantsDetails.length > 1 
				&& this.userData.masterEnabled == '0'
				&& this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
			|| (this.userData.masterEnabled == '1' 
				&& this.userData.isMaster == '1')
			|| (this.userData.masterEnabled == '1' 
				&& this.userData.isMaster == '0'
				&& this.userData.group !='3')
			|| (this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
				&& this.configData.allow_multiple_organization==1 
				&& this.userData.crossTenantsDetails.length > 1 
				&& this.userData.masterEnabled == '0'
				&& this.configData.enable_nursing_agencies_visibility_restrictions == '1'
				&& this.userData.nursing_agencies == '')) {
				   this.assignChatWithTenantFilterData(); 
				} else {
					this.chatWithTenantFilter.filterEnabled = false;
					this.chatWithTenantFilter.tenants = [];
				}
				break;
			} 
			case 'partnerroles': {
				if((this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
				&& this.configData.allow_multiple_organization==1 
				&& this.userData.crossTenantsDetails.length > 1 
				&& this.userData.masterEnabled == '0'
				&& this.configData.enable_nursing_agencies_visibility_restrictions != 1) 
			|| (this.userData.masterEnabled == '1' 
				&& this.userData.isMaster == '1')
			|| (this.userData.masterEnabled == '1' 
				&& this.userData.isMaster == '0'
				&& this.userData.group !='3')
			|| (this.privileges.indexOf('allowMultipleOrganizationsStaffCommunication')!==-1 
				&& this.configData.allow_multiple_organization==1 
				&& this.userData.crossTenantsDetails.length > 1 
				&& this.userData.masterEnabled == '0'
				&& this.configData.enable_nursing_agencies_visibility_restrictions == '1'
				&& this.userData.nursing_agencies == '')) {
				   this.assignChatWithTenantFilterData(); 
				} else {
					this.chatWithTenantFilter.filterEnabled = false;
					this.chatWithTenantFilter.tenants = [];
				}
				break;
			}      
			default: {
				this.chatWithTenantFilter.filterEnabled = false;
				this.chatWithTenantFilter.tenants = [];
				break;
			} 
		}
	}
	filterBranch(event){
		console.log('event');
		console.log(event.target.value);
		if(event.target.value)  {
			var previousSelectedTenant = this.chatWithTenantFilter.tenants.find((tenant) => tenant.id == this.chatWithTenantFilter.selectedTenant);
			this.chatWithTenantFilter.selectedTenant = event.target.value;
			console.log(this.chatWithTenantFilter);console.log(this.chatWithTenantFilter.enabledReset);
			if(this.chatWithTenantFilter.enabledReset) {
				console.log(this.chatWithTenantFilter.selectedTenant);
				console.log(this.chatWithTenantFilter.tenants);
				if(this.optionShow =='staffs' || this.optionShow =='partner'){
					this.reset(this.optionShow);
				  } 
				  if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
					this.chatWithLoader.otherTenantstaff =true;
					this.getStaffRoles(this.optionShow);
				} 
				var currentSelectedTenant = this.chatWithTenantFilter.tenants.find((tenant) => tenant.id == this.chatWithTenantFilter.selectedTenant);
				var activityData = {
					activityName: "Chat With " + ((this.optionShow == 'staffs') ? 'Staff ' : (this.optionShow == 'partner') ? 'partner' : ((this.optionShow == 'patient') ? 'Patient ' : ' ')) +"Tenant Switching",
					activityType: "messaging",
					activityDescription: this.userData.displayName + " (" + this.userData.userId + ") has selected tenant " + currentSelectedTenant.tenantName + '(' + currentSelectedTenant.id +')' + ((previousSelectedTenant) ? (' from tenant '+ previousSelectedTenant.tenantName + '(' + previousSelectedTenant.id +')') : ''),
					tenantId : (this._structureService.getCookie('crossTenantId') && this._structureService.getCookie('crossTenantId') !='undefined' && this._structureService.getCookie('tenantId')!==this._structureService.getCookie('crossTenantId')) ? this._structureService.getCookie('crossTenantId') : this.userData.tenantId
				};
				this._structureService.trackActivity(activityData);
			} else {
				this.chatWithTenantFilter.enabledReset = true;
			}
		}
	}
	removeDuplicates(arr, prop) {
		let obj = {};
		console.log("arrarrarrarrarrarrarr",arr);
		return Object.keys(
			arr.reduce((prev, next) => {
				// console.log("objobjobjobjobjobjobjobjobjobj",obj,!obj,prev,next,prop,!obj[next],!obj[next[prop]]);
				if (next && !obj[next[prop]]) obj[next[prop]] = next;
				return obj;
			}, obj)
		).map((i) => obj[i]);
	}
	getGroups(refresh) {
		let isRoleAvailable = true;
		let clinicianRolesAvaiable = null;
		this.getAllMessageGroupsLoading = true;
		this.getPatientDiscussionGroupsLoading =true;
		if (this.userData.group == 3) {
			if (this._inboxService.checkInfusionHours(false).isWorkingHours) {
				if (this.configData.clinician_roles_on_working_hours) {
					clinicianRolesAvaiable = this.configData.clinician_roles_on_working_hours;
				} else {
					isRoleAvailable = false;
				}
			} else {
				if (this.configData.clinician_roles_beyond_working_hours) {
					clinicianRolesAvaiable = this.configData.clinician_roles_beyond_working_hours;
				} else {
					isRoleAvailable = false;
				}
			}
		}
		let members;
		if (this.selectedGroupDetails && this.selectedGroupDetails.memberIds != undefined) {
			members = this.selectedGroupDetails.memberIds;
		} else {
			members = '';
		}
		  const reqObj: GroupsListingRequest = {
			data: {patients: [{id: +this.pdgData.patientId, ...(this._structureService.isMultiAdmissionsEnabled && { admissionId: this.pdgData.admissionId })}]}
		  }
		  this.messageService.fetchGroupData(reqObj,'patientGroup').subscribe((resp: GroupListItem<MessageGroup>) => {
			this.executeSelectedPdgData(resp.data);
		  });
		
	}

	initGroups() {
		this.offset = 0;
		this.allMessageGroups = [];
		this.memberDataGroupWise = [];
		this.hideLoadMore = false;
		this.prevText = '';
	}

	reset(optionShow ='groups') {
	 this.srch.nativeElement.value = "";
    if(optionShow == 'groups' || optionShow == 'msggroups') {
		this.initGroups();
		this.searchTexts = '';
		this.getAllMessageGroupDetails(this.userData.tenantId, this.userData.userId,"",optionShow);
		    }else {
      this.loadMoreSearchValue = undefined;
      this.callFromInitialCountChatWithUserlist = true;
      this.chatwithPageCount = {
		  staffs: 0,
		  partner: 0,
          patients: 0
      }
      this.noMoreItemsAvailable = {
          users: false
      };
      this.userListChatwith = [];
      this.clinicalUserDetails = [];
      this.usersList = [];
      this.loadMoreUsers(optionShow)
  }
	}

	search(optionShow='groups') {
		if(optionShow == 'groups' || optionShow == 'msggroups') {
		this.initGroups();
      if(this.srch.nativeElement.value.trim() || this.selectSiteId != 0 || this.multiTenant){
        this.getAllMessageGroupDetails(this.userData.tenantId, this.userData.userId,this.srch.nativeElement.value.trim(),optionShow);    
    }
  
}else if(this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
	this.getStaffRoles(this.optionShow);
  } else if(optionShow == 'staffs' || optionShow == 'partner') {
  this.callFromInitialCountChatWithUserlist = true;
  this.chatwithPageCount = {
	  staffs: 0,
	  partner: 0,
      patients: 0
  }
  this.noMoreItemsAvailable = {
      users: false
  };
  this.userListChatwith = [];
  this.clinicalUserDetails = [];
  this.usersList = [];
  this.loadMoreUsers(optionShow, this.srch.nativeElement.value.trim())
}  	
	}

	searchOnEnter(event, optionShow) {
		if (optionShow == 'staffs' || optionShow == 'partner' || optionShow == 'groups' || optionShow =='msggroups') {
		  if (event.keyCode == 13) {
			event.preventDefault();
	
			if (this.srch.nativeElement.value.trim()) {
			  this.search(optionShow);
			} else {
			  this.reset(optionShow)
			}
		  }
		}
	}
	  loadMoreUsers(optionShow, searchKeyword=undefined, notInit=false) {
		
    if(!this.loadMoreSearchValue) {
        if(this.srch.nativeElement.value.trim()){
            this.loadMoreSearchValue = undefined;
            this.callFromInitialCountChatWithUserlist = true;
            this.chatwithPageCount = {
				staffs: 0,
				partner: 0,
                patients: 0
            }
            this.noMoreItemsAvailable = {
                users: false
            };
            notInit = false;
            this.userListChatwith = [];
            this.clinicalUserDetails = [];
            this.usersList = [];
            searchKeyword = this.srch.nativeElement.value.trim();
        }
    } else if(this.loadMoreSearchValue && !this.srch.nativeElement.value.trim()) {
        this.reset(optionShow)
        return false;
    } else if(this.loadMoreSearchValue == this.srch.nativeElement.value.trim()) {
    } else {
        this.loadMoreSearchValue = undefined;
        this.callFromInitialCountChatWithUserlist = true;
        this.chatwithPageCount = {
			staffs: 0,
			partner: 0,
            patients: 0
        }
        this.noMoreItemsAvailable = {
            users: false
        };
        notInit = false;
        this.userListChatwith = [];
        this.clinicalUserDetails = [];
        this.usersList = [];
        searchKeyword = this.srch.nativeElement.value.trim();
    }
    var isRoleAvailable = true;
    var clinicianRolesAvaiable = null;
    var setCliniciansRoleAvailableResponse;  
        if(isRoleAvailable) {
            this.getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,(!notInit ? true : false),(!notInit ? false : true), optionShow,searchKeyword)
        } else {
            this.chatWithUsersLoading = false;
        }
    

}
getUsersListByRoleIdAvailableOnClick(clinicianRolesAvaiable,init, loadMore, optionShow, searchValue) {
  let chatWithFilterTenantId = null;
  let chosenSiteId;
  if(init) {
       if(optionShow=='staffs') {
		  this.chatWithLoader.staffs = true;
		  this.UsergroupIds = 3; 
	  }
	  if(optionShow=='partner') {
		this.chatWithLoader.partner = true;
		this.UsergroupIds = 20;
	}
  }else{
    if(optionShow=='staffs') { 
      this.UsergroupIds = 3;
    }
    if(optionShow=='partner') { 
        this.UsergroupIds = 20;
    }
  }
  if(optionShow == 'staffs' || optionShow == 'partner' ) {
	  chatWithFilterTenantId = this.chatWithTenantFilter.selectedTenant;
	  var memberIds='';
		  if(this.messageGroupMemberIds != '')
		  { 
			memberIds = this.messageGroupMemberIds;		
		} 
		
  }
  this.loadMoremessage.users = "Loading ....";
  chosenSiteId = this.multiTenant ? this.selectedBranchId : (this.selectSiteId == undefined || this.selectSiteId == 0 || isBlank(this.selectSiteId)) ? this.editedSite : this.selectSiteId;

  this._inboxService.getUsersListByRoleId((this.UsergroupIds ? this.UsergroupIds : 3), 0, null,null,(init ? 0 : ((optionShow == 'staffs') ? this.chatwithPageCount.staffs : (optionShow == 'partner') ? this.chatwithPageCount.partner : this.chatwithPageCount.patients)),((optionShow == 'staffs' && !clinicianRolesAvaiable) ? true : ((optionShow == 'staffs' && clinicianRolesAvaiable) ? undefined : false)),searchValue, chatWithFilterTenantId, undefined,memberIds,optionShow,chosenSiteId).then((data:any) => {

    console.log('data',data);
    
    this.chatWithUsersLoading = false;
      this.loadMoreSearchValue = searchValue;
      if(loadMore) {
				 
		  this.clinicalUserDetails = [...this.clinicalUserDetails, ...data];
		  
      } else {
          this.clinicalUserDetails=data;
      }
      if(data.length != 20)
          this.noMoreItemsAvailable.users = true;
  
      if(optionShow=='staffs') {
          this.chatwithPageCount.staffs += 1;
	  }
	  if(optionShow=='partner') {
		this.chatwithPageCount.partner += 1;
	}
      console.log(this.clinicalUserDetails);
      for(var _i=0;_i<this.clinicalUserDetails.length;_i++){
          this.clinicalUserDetails[_i].isScheduled=1;
          if(this._inboxService.scheduleSelectionFilter(this.clinicalUserDetails[_i])){
              this.clinicalUserDetails[_i].isScheduled=1;
          }else{
              this.clinicalUserDetails[_i].isScheduled=0;
          }
      }
      console.log(this.clinicalUserDetails);
	  this.loadMoremessage.users = "Load more";  
	  this.userListChatwith =this.clinicalUserDetails;

      this.userListChatwith = this.userListChatwith.filter(
        (x) => !this.selectedGroupMembers.some((y) => y.id == x.userid)
      );     
	 
	 
      if(init) {
           if(optionShow=='staffs') {
              this.chatWithLoader.staffs = false;
		  }
		  if(optionShow=='partner') {
			this.chatWithLoader.partner = false;
		}
      }
  }).catch((ex) => {
      if(init) {
           if(optionShow=='staffs') {
              this.chatWithLoader.staffs = false;
		  }
		  if(optionShow=='partner') {
			this.chatWithLoader.partner = false;
		}
      }
  });
 
  console.log(this.userListChatwith);
  }

	loadMoreGroups(optionShow='') {
		this.loadMore_flag = true;
		if (!this.prevText) {
			if (this.srch.nativeElement.value.trim()) {
				console.log('Initialise search...');
				this.search();
			} else {
				console.log('Continue with pagination');
				this.offset = this.offset + 25;
				this.getAllMessageGroupDetails(this.userData.tenantId, this.userData.userId,'',optionShow);
			}
		} else if (this.prevText && !this.srch.nativeElement.value.trim()) {
			console.log('Reset called');
			this.reset();
		} else if (this.prevText == this.srch.nativeElement.value.trim()) {
			console.log('Continue pagination with search');
			this.offset = this.offset + 25;
			this.getAllMessageGroupDetails(this.userData.tenantId, this.userData.userId, this.srch.nativeElement.value.trim(),optionShow);
		} else {
			console.log('Initialise search...');
			this.search();
		}
	}

	getAllMessageGroupDetails(tenantId, userId, searchKeyword = '',optionShow='') {
		let chosenSiteId;
		console.log('KKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKKK');
		if(optionShow == 'groups') {
			this.ispdgs = '1';
		}else if(optionShow == 'msggroups'){
			this.ispdgs = '0';
		}

		this.loadingGroups = true;
		this.MessageGroupWithLoader.groups = true;
		var members;
		if (this.selectedGroupDetails && this.selectedGroupDetails.memberIds != undefined) {
			console.log('this.selectedGroupDetails', this.selectedGroupDetails);
			members = this.selectedGroupDetails.memberIds;
		} else {
			members = '';
		}
		chosenSiteId = this.editedSite;
		const page = this.offset/this.limit;
		const paginationItems: PaginationItems = {
			limit: this.limit,
			page: page > 0  ? page + 1 : 1,
		}
		const SortItems: SortItems = {
			order: 'asc'
		}
		const groupItems: GroupItems = {
			pagination: paginationItems,
			sort: SortItems			
		}
		if (searchKeyword) {
			const fiilterItems: FilterItems = {
			  search: searchKeyword
			}
			groupItems.filter = fiilterItems;
		}
	  
		const reqObj: GroupsListingRequest = {
			data: groupItems,
			siteIds: [+chosenSiteId]
		}
		switch (this.optionShow) {
			case 'msggroups':
				this.messageService.fetchGroupData(reqObj,'messageGroup').subscribe((resp: GroupListItem<GroupData>) => {
					const data = resp.data.messageGroups.map(item => {
						return new MessageGroup(item);
					});
				this.executeGroupData(searchKeyword,data);
				},error => {
				  this.MessageGroupWithLoader.groups = false;
				});
				break;
			case 'groups':
				this.messageService.fetchGroupData(reqObj,'patientGroup').subscribe((resp: GroupListItem<GroupData>) => {
					const data = resp.data.patientGroups.map(item => {
						return new MessageGroup(item);
					});
					this.executeGroupData(searchKeyword,data);
				},error => {
				  this.MessageGroupWithLoader.groups = false;
				});
				break;
		}
	}

	noWhitespaceValidator(control: FormControl) {
		let isWhitespace = (control.value || '').trim().length === 0;
		let isValid = !isWhitespace;
		return isValid ? null : { whitespace: true };
	}
	setGroupList(group) {
		if (+this.userData.config.enable_nursing_agencies_visibility_restrictions === 1 && !isBlank(this.userData.nursing_agencies)) {
			if (group.createdBy != this.userData.userId) {
				this.canEdit = false;
			}
		}
		this.selected = group;
		this.selectedGroupDetails = this.selected;
		this.messageGroup.reset();
		this.selectedGroupMembers = [];
		this.checkedRoleIds =[];
		if(this.selected.siteId) {
			this.selectedSiteName = this.userData.mySites.find(item => item.id == this.selected.siteId).name;
			this.editedSite = this.selected.siteId.split(',').map(Number);;
		}
		this.selected.patientUser = JSON.parse(this.selected.patientUser);
		this.selectedisPublic = this.selected.isPublic == '1' ? true : false;
		this.selectedAllowMultiThread = this.selected.allowMultiThreadChat == '1' ? true : false;
		this.branchValue = this.selected.branch;
		if (
			this.selected.branch == '' ||
			this.selected.branch == 'null' ||
			this.selected.branch == null ||
			this.selected.branch == '0'
		) {
			this.branchValue = this._structureService.getCookie('crossTenantId');
		}
		if (this.selected.patientUser) {
			let dateFormated;
			if (this.selected.patientUser.DOB) {
				let dateData;
				if (this.selected.patientUser.DOB.indexOf('-') > -1) {
					dateData = this.selected.patientUser.DOB.split('-');
				} else {
					dateData = this.selected.patientUser.DOB.split('/');
				}
				const year1 = dateData[0];
				let month1 = dateData[1];
				let day1 = dateData[2];
				if (month1.toString().length == 1) {
					month1 = '0' + month1;
				}
				if (day1.toString().length == 1) {
					day1 = '0' + day1;
				}
				dateFormated = month1 + '/' + day1 + '/' + year1;
			}
			this.replaceText = this.selected.MRN || '';
			this.forField = this.replaceText.match(/\(([^)]*)\)[^(]*$/) ? this.replaceText.match(/\(([^)]*)\)[^(]*$/)[1] : this.replaceText;
			this.messageGroup.patchValue({
				messageGroupName: this.selected.patientUser.firstName,
				messageGroupLastName: this.selected.patientUser.lastName,
				messageGroupDob: dateFormated,
				messageGroupBranch: this.branchValue,
				patientUniqueId: this.forField,
				nursingAgencyUserTag: this.selected.tagId ? this.selected.tagId : '0'
			});
		} 
/*  ******* display roles with no users in existing roles***********
 */
	let roleslist = this.selected.selectedRoles.toString();
	let status = 0;
	let currentTenant = this._structureService.getCookie('tenantId');

   if(roleslist != '' && (this.userData.crossTenantsDetails.length < 2 && !this.multiSiteEnable)){
	this._inboxService.getRoleDetailsInit(roleslist).then((roles)=> {
	  if (roles) {
		this.member_role_detail1 = Array.isArray(roles)?roles:[];
	   
		this.member_role_detail1.forEach(role => {
		  
		  this._structureService.getRoleBasedStaffs(role['id'], status,1,false,role['tenant_id'],(this.selected.siteId) ? this.selected.siteId : "0").then(( data ) => {
			let parsedResponceData = JSON.parse(JSON.stringify(data));
			this.memberRoleStaffs = parsedResponceData.getSessionTenant['roleBasedStaffs']; 
		    
		   if (this.memberRoleStaffs) {
			this.memberRoleStaffs.forEach(ele => {
				if(ele != null){
				ele.assignRoles.forEach(e => {
				 
				  if(e['isPrimary'] == true){
					this.primaryId = e['tenantUsersRoleId'];
					this.primaryRolename = e['roleName'];
				  }
				  if(e['isPrimary'] == false){
					this.secondaryId = e['tenantUsersRoleId'];
				  }
				  
				});
				let ten_id;
				let ten_name;
				if(currentTenant == role['tenant_id']){
				  ten_id = currentTenant;
				  ten_name = "";
	
				}else{
				  ten_id = role['tenant_id'];
				  ten_name = role['name'];
				}
				
				this.newMember = {
					  id: "",
					  displayName: "",
					  role: {},
					  tenantId: null,
					  tenantName: "",
					  seconRoleId:"",
					  primaryId:"",
					  otherTenantStaff: false,
					  naTags: "",
					  naTagNames: ""
				};            
				this.newMember.role = role['id'];           
				this.newMember.tenantId = ten_id;
				this.newMember.tenantName = ten_name;
				this.newMember.seconRoleId = this.secondaryId;
				this.newMember.primaryId = this.primaryId;
				this.newMember.id = ele.id;
				this.newMember.displayName = ele.displayName;
				this.newMember.displayname = ele.displayName;
				this.newMember.citusRoleId = role['citus_role_id'];
				let y = this.selectedGroupMembers.find(x => x.id != this.newMember.id);
				if(this.selectedGroupMembers.length > 0){
					this.noMemberError = false;
					this.disableButton = false;
					this.selectedGroupMembers = this.removeDuplicates(this.selectedGroupMembers,"id");
				}
					if(y && !y.id){
					this.userListChatwith = this.userListChatwith.filter(function (members) {
					  return members.userid != ele.id;
					});				
					}
								
	
				  const roleData = {
					id: this.newMember.role,
					name: role['role_name'],
					tenantRoleId: this.newMember.role,
					tenantId:ten_id,
					tenantName:ten_name,
					citusRoleId:role['citus_role_id']
				   
				  };

				 this.setEditcheckedRoleIds(roleData);	

				  const user = { id: this.newMember.id, name: this.newMember.displayName, role:this.newMember.role,seconRoleId: this.newMember.seconRoleId};
				  this.testArr.push(user);
				} else {
				  const roleData = {
					id: role['id'],
					name: role['role_name'],
					tenantRoleId: role['id'],
					tenantId:role['tenant_id'],
					tenantName:role['name'],
					citusRoleId:role['citus_role_id']
				   
				  };
				 this.setEditcheckedRoleIds(roleData);	

				}

			  });

			
		  } else{
			const roleData = {
			  id: role['id'],
			  name: role['role_name'],
			  tenantRoleId: role['id'],
			  tenantId:role['tenant_id'],
			  tenantName:role['name'],
			  citusRoleId:role['citus_role_id'],
			  role:role['id']			 
			};

		   this.setEditcheckedRoleIds(roleData);
		   }				
		 
		  this.memberRoleStaffs = [];
		  });		  
	  });   
    }
   });
  }

		if (this.selected.memberDetails) {
			this.selected.memberDetails.forEach((element) => {
				this.newMember = {
					id: '',
					displayName: '',
					role: {},
					dualRoles: {},
					tenantId: null,
					status:'',
					tenantName: '',
					otherTenantStaff: element.tenantId && this.crossTenantId != element.tenantId ? true : false,
					naTagNames: '',​​​
					naTags: '',
					roleIds: ''
				};
				if (!this.selected.patientUser || element.id !== this.selected.patientUser.id) {
					this.newMember.id = element.id;
					this.newMember.displayName = element.displayName; //element.name;
					this.newMember.role = {id:element.tenantRoleId,displayName:element.roles};
					this.newMember.dualRoles = element.dualRoles;
					this.newMember.tenantId = element.tenantId;
					this.newMember.status = element.status;
					this.newMember.tenantName = element.tenantName;
					this.newMember.naTagNames = element.naTagNames;
					this.newMember.naTags = element.naTags;
					this.newMember.citusRoleId = element.roleId;
					this.newMember.seconRoleId = element.msgRoleid;
					this.newMember.roleIds = element.roleIds;

					
					if(this.selectedGroupMembers.indexOf(this.newMember.id)===-1){
						this.selectedGroupMembers.push(this.newMember);
						
					}

					

				}
				const roleData = {
					id: this.newMember.role.id,
					name: this.newMember.role.displayName,
					tenantRoleId: this.newMember.role.id,
					tenantId:this.newMember.tenantId, 
					tenantName:this.newMember.tenantName,
					naTagNames:this.newMember.naTagNames,
					naTags:this.newMember.naTags,
					citusRoleId:this.newMember.citusRoleId,
					role:this.newMember.role.id,
					seconRoleId: this.newMember.seconRoleId
				};
				const user = { id: this.newMember.id, name: this.newMember.displayName, role:this.newMember.role.id,seconRoleId: this.newMember.seconRoleId};
                this.testArr.push(user);			
				
			});
		}
		if (this.selected.selectedRoles) {
			this.selected.selectedRoles = this.selected.selectedRoles.map(({ tenant_id, roleId, ...roleValue }) => ({
				tenantId: tenant_id,
				id: roleId,
				...roleValue
			}));
			this.selected.selectedRoles.forEach(item => {
				this.setEditcheckedRoleIds(item);
			});
		}
		if (!this.selectedGroupMembers.length && !this.checkedRoleIds.length) {
			this.disableButton = true;
			this.noMemberError = true;
		} else {
			this.noMemberError = false;
			this.disableButton = false;
		}
		 
	}
	setEditcheckedRoleIdsChange(role){
				var exist =false;
				this.checkedRoleIds.forEach((value)=>{
					if(value.id == role.id){
						exist = true;
					}
				});		
				if(!exist){
					this.checkedRoleIds.push(role);
					this.removeRoleStaffs(role);
				  }		
	}
	setEditcheckedRoleIds(role){
		if(Array.isArray(this.selected.selectedRoles)) {
			if (this.selected.selectedRoles&& this.selected.selectedRoles.length > 0) {
				if (this.selected.selectedRoles.indexOf(role)>-1) {
					let exist = false;
					this.checkedRoleIds.forEach((value)=>{
						if(value.id == role.id){
							exist = true;
						}
					});		
					if (!exist) {
						this.checkedRoleIds.push(role);
						this.removeRoleStaffs(role);
					}		
				}			
			}
		}
	}
	editGroupName(group, i, setSelected) {

		this.disableButton = false;
		this.addMemberError = false;
		this.checkedIds = [];
		this.newMemberList = [];
		this.newMemberOtherTenantList = [];
		this.newMemberListByRoleWise = [];
		this.newMemberOtherTenantListByRoleWise = [];
		this.createdPatientIdIndex = i;
		console.log('group.patientUser============>', typeof this.selected.patientUser);
		this.staffList.forEach((value) => {
			if (this.selected.memberDetails) {
				var index = this.selected.memberDetails.findIndex((x) => x.id == value.id);
				if (index === -1) {
					if (value.role && value.role.id) {
						this.newMember = {
							id: '',
							displayName: '',
							role: {},
							dualRoles: {},
							tenantId: null,
							tenantName: '',
							otherTenantStaff:
								value.role.tenantId && this.userData.tenantId != value.role.tenantId ? true : false,
								naTags:"",
								naTagNames:""
						};
						this.newMember.id = value.id;
						this.newMember.displayName = value.displayName;
						this.newMember.role = value.role;
						this.newMember.tenantId = value.role.tenantId;
						this.newMember.tenantName = value.role.tenantName;
						this.newMember.dualRoles = value.dualRoles;

						if(value.naTags && value.naTags != null && value.naTags != 'null') {
							this.newMember.naTags = value.naTags;
							this.newMember.naTagNames = value.naTagNames;
						}
						
						this.newMemberList.push(this.newMember);
						this.memberList.push(this.newMember);

						var user = { id: value.id, name: value.displayName, naTags: "", naTagNames: "" };

						if(value.naTags && value.naTags != null && value.naTags != 'null') {
							user.naTags = value.naTags;
							user.naTagNames = value.naTagNames;
						}

						var roleData = {
							id: this.newMember.role.id,
							name: this.newMember.role.displayName,
							tenantRoleId: this.newMember.role.id,tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles
						};
						this.setEditcheckedRoleIds(roleData);
						//if (this.newMember.role.id) {
						if (!this.newMemberListByRoleWise[this.newMember.role.id]) {
							this.newMemberListByRoleWise[this.newMember.role.id] = {};
						}
						if (!('userList' in this.newMemberListByRoleWise[this.newMember.role.id])) {
							this.newMemberListByRoleWise[this.newMember.role.id]['userList'] = [];
						}
						if (!('roleData' in this.newMemberListByRoleWise[this.newMember.role.id])) {
							this.newMemberListByRoleWise[this.newMember.role.id]['roleData'] = {};
						}
						if (value.dualRoles && value.dualRoles.length > 1) {
							console.log('Enter dual Role condition=============================================');
							value.dualRoles.forEach((dualEach) => {
								console.log('dualEach======================');
								console.log(dualEach);
								if (value.role.id != dualEach.tenantUsersRoleId) {
									if (!this.newMemberListByRoleWise[dualEach.tenantUsersRoleId]) {
										this.newMemberListByRoleWise[dualEach.tenantUsersRoleId] = {};
										var dualRoleData ={
												id: dualEach.tenantUsersRoleId,
												name: dualEach.roleName,
												tenantRoleId: dualEach.tenantUsersRoleId,
												tenantId:dualEach.tenantId, tenantName:dualEach.tenantName,dualRoles:value.dualRoles
										}
										this.setEditcheckedRoleIds(dualRoleData);
									}
									if (!('userList' in this.newMemberListByRoleWise[dualEach.tenantUsersRoleId])) {
										this.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'] = [];
									}
									this.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'].push(user);
								}
							});
						}
						this.newMemberListByRoleWise[this.newMember.role.id]['roleData'] = roleData;
						this.newMemberListByRoleWise[this.newMember.role.id]['tenantId'] = this.newMember.role.tenantId;
						this.newMemberListByRoleWise[this.newMember.role.id]['tenantName'] = this.newMember.role.tenantName;						
						this.newMemberListByRoleWise[this.newMember.role.id]['userList'].push(user);
												
						//}
					}
				} else {
					console.log("elseeeeeeeeeeeee");
					console.log(value);
					if (value.role && value.role.id) {
						this.newMember = {
							id: '',
							displayName: '',
							role: {},
							dualRoles: {},
							tenantId: null,
							tenantName: '',
							otherTenantStaff:
								value.role.tenantId && this.userData.tenantId != value.role.tenantId ? true : false,
								naTags:"",
            					naTagNames: ""
						};
						this.newMember.id = value.id;
						this.newMember.displayName = value.displayName;
						this.newMember.role = value.role;
						this.newMember.dualRoles = value.dualRoles;
						this.newMember.tenantId = value.role.tenantId;
						this.newMember.tenantName = value.role.tenantName;

						if(value.naTags && value.naTags != null && value.naTags != 'null') {
							this.newMember.naTags = value.naTags;
							this.newMember.naTagNames = value.naTagNames;
						}
						
						this.memberList.push(this.newMember);
						var roleData = {
							id: this.newMember.role.id,
							name: this.newMember.role.displayName,
							tenantRoleId: this.newMember.role.id,
							tenantId:value.role.tenantId, 
							tenantName:value.role.tenantName,
							dualRoles:value.dualRoles
						};

						var selectedIndex = this.selectedGroupMembers.findIndex((x) => x.id == value.id);

						if(selectedIndex >- 1)
							this.selectedGroupMembers[selectedIndex].dualRoles = value.dualRoles
						this.setEditcheckedRoleIds(roleData);
						if (value.dualRoles && value.dualRoles.length > 1) {
							console.log('else Enter dual Role condition=============================================');
							value.dualRoles.forEach((dualEach) => {
								console.log('dualEach======================');
								console.log(dualEach);
								if (value.role.id != dualEach.tenantUsersRoleId) {
									var dualRoleData ={
											id: dualEach.tenantUsersRoleId,
											name: dualEach.roleName,
											tenantRoleId: dualEach.tenantUsersRoleId,
											tenantId:dualEach.tenantId, tenantName:dualEach.tenantName,dualRoles:value.dualRoles
									}
									this.setEditcheckedRoleIds(dualRoleData);
								}
							});
						}
					}
				}
			} else {
				if (value.role && value.role.id) {
					this.newMember = {
						id: '',
						displayName: '',
						role: {},
						dualRoles: {},
						tenantId: null,
						tenantName: '',
						otherTenantStaff:
							value.role.tenantId && this.userData.tenantId != value.role.tenantId ? true : false,
							naTags:"",
							naTagNames: ""
					};
					this.newMember.id = value.id;
					this.newMember.displayName = value.displayName;
					this.newMember.role = value.role;
					this.newMember.dualRoles = value.dualRoles;

					if(value.naTags && value.naTags != null && value.naTags != 'null') {
						this.newMember.naTags = value.naTags;
						this.newMember.naTagNames = value.naTagNames;
					}

					this.newMemberList.push(this.newMember);
					this.memberList.push(this.newMember);

					user = { id: value.id, name: value.displayName, naTags: "", naTagNames: "" };

					if(value.naTags && value.naTags != null && value.naTags != 'null') {
						user.naTags = value.naTags;
						user.naTagNames = value.naTagNames;
					}
					var roleData = {
						id: this.newMember.role.id,
						name: this.newMember.role.displayName,
						tenantRoleId: this.newMember.role.id,tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles
					};
					this.setEditcheckedRoleIds(roleData);
					//if (this.newMember.role.id) {
					if (!this.newMemberListByRoleWise[this.newMember.role.id]) {
						this.newMemberListByRoleWise[this.newMember.role.id] = {};
					}
					if (!('userList' in this.newMemberListByRoleWise[this.newMember.role.id])) {
						this.newMemberListByRoleWise[this.newMember.role.id]['userList'] = [];
					}
					if (!('roleData' in this.newMemberListByRoleWise[this.newMember.role.id])) {
						this.newMemberListByRoleWise[this.newMember.role.id]['roleData'] = {};
					}

					this.newMemberListByRoleWise[this.newMember.role.id]['roleData'] = roleData;
					this.newMemberListByRoleWise[this.newMember.role.id]['userList'].push(user);
					
					if (value.dualRoles && value.dualRoles.length > 1) {
						console.log('Enter dual Role condition=============================================');
						value.dualRoles.forEach((dualEach) => {
							console.log('dualEach======================');
							console.log(dualEach);
							if (value.role.id != dualEach.tenantUsersRoleId) {
								if (!this.newMemberListByRoleWise[dualEach.tenantUsersRoleId]) {
									this.newMemberListByRoleWise[dualEach.tenantUsersRoleId] = {};
									var dualRoleData ={
										id: dualEach.tenantUsersRoleId,
										name: dualEach.roleName,
										tenantRoleId: dualEach.tenantUsersRoleId,
										tenantId:dualEach.tenantId, tenantName:dualEach.tenantName,dualRoles:value.dualRoles
								}
								this.setEditcheckedRoleIds(dualRoleData);
								}
								if (!('userList' in this.newMemberListByRoleWise[dualEach.tenantUsersRoleId])) {
									this.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'] = [];
								}

								this.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'].push(user);
						
							}
						});
					}
					//}
				}
			}
		});

		this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter(function(a) {
			return a && a.tenantId;
		});

		this.newMemberListByRoleWise.sort(function(a, b) {
			if (a.roleData.name < b.roleData.name) return -1;
			if (a.roleData.name > b.roleData.name) return 1;
			return 0;
		});

		this.otherTenantStaffList.forEach((value) => {
			if (this.selected.memberDetails) {
				var index = this.selected.memberDetails.findIndex((x) => x.id == value.id);
				if (index === -1) {
					if (value.role && value.role.id) {
						if (this.enableCommunicationWithInternalstaffs == true) {
							if (this.internalSiteId == value.role.tenantId) {
								this.newMember = {
									id: '',
									displayName: '',
									role: {},
									dualRoles: {},
									tenantId: null,
									tenantName: '',
									otherTenantStaff:
										value.role.tenantId && this.crossTenantId != value.role.tenantId
											? true
											: false
								};
								this.newMember.id = value.id;
								this.newMember.displayName = value.displayName;
								this.newMember.role = value.role;
								this.newMember.tenantId = value.role.tenantId;
								this.newMember.dualRoles = value.dualRoles;
								this.newMember.tenantName = value.role.tenantName;
								this.newMemberOtherTenantList.push(this.newMember);
								this.otherTenantMemberList.push(this.newMember);
								var user = { id: this.newMember.id, name: this.newMember.displayName };
								var roleData = {
									id: this.newMember.role.id,
									name: this.newMember.role.displayName,
									tenantRoleId: this.newMember.role.id,tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles
								};
								this.setEditcheckedRoleIds(roleData);
								//if (this.newMember.role.id) {
								if (!this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]) {
									this.newMemberOtherTenantListByRoleWise[this.newMember.role.id] = {};
								}
								if (!('userList' in this.newMemberOtherTenantListByRoleWise[this.newMember.role.id])) {
									this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['userList'] = [];
								}
								if (!('roleData' in this.newMemberOtherTenantListByRoleWise[this.newMember.role.id])) {
									this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['roleData'] = {};
								}
								if (value.dualRoles && value.dualRoles.length > 1) {
									console.log(
										'Enter dual Role condition============================================='
									);
									value.dualRoles.forEach((dualEach) => {
										console.log('dualEach======================');
										console.log(dualEach);
										if (value.role.id != dualEach.tenantUsersRoleId) {
											if (!this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]) {
												this.newMemberOtherTenantListByRoleWise[
													dualEach.tenantUsersRoleId
												] = {};
												var dualRoleData ={
													id: dualEach.tenantUsersRoleId,
													name: dualEach.roleName,
													tenantRoleId: dualEach.tenantUsersRoleId,
													tenantId:dualEach.tenantId, tenantName:dualEach.tenantName,dualRoles:value.dualRoles
											}
											this.setEditcheckedRoleIds(dualRoleData);
											}
											if (!('userList' in this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]) ) {
												this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]['userList'] = [];
											}
											this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]['userList'].push(user);
										}
									});
								}
								this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['roleData'] = roleData;
								this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['userList'].push(user);
								this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['tenantId'] = this.newMember.role.tenantId;
								this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['tenantName'] = this.newMember.role.tenantName;
							}
						} else {
							this.newMember = {
								id: '',
								displayName: '',
								role: {},
								dualRoles: {},
								tenantId: null,
								tenantName: '',
								otherTenantStaff:
									value.role.tenantId && this.crossTenantId != value.role.tenantId
										? true
										: false
							};
							this.newMember.id = value.id;
							this.newMember.displayName = value.displayName;
							this.newMember.role = value.role;
							this.newMember.tenantId = value.role.tenantId;
							this.newMember.dualRoles = value.dualRoles;
							this.newMember.tenantName = value.role.tenantName;
							this.newMemberOtherTenantList.push(this.newMember);
							this.otherTenantMemberList.push(this.newMember);
							var user = { id: this.newMember.id, name: this.newMember.displayName };
							var roleData = {
								id: this.newMember.role.id,
								name: this.newMember.role.displayName,
								tenantRoleId: this.newMember.role.id,tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles
							};
							//if (this.newMember.role.id) {
							if (!this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]) {
								this.newMemberOtherTenantListByRoleWise[this.newMember.role.id] = {};
							}
							if (!('userList' in this.newMemberOtherTenantListByRoleWise[this.newMember.role.id])) {
								this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['userList'] = [];
							}
							if (!('roleData' in this.newMemberOtherTenantListByRoleWise[this.newMember.role.id])) {
								this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['roleData'] = {};
							}
							this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['roleData'] = roleData;
							this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['userList'].push(user);
							this.newMemberOtherTenantListByRoleWise[this.newMember.role.id][
								'tenantId'
							] = this.newMember.role.tenantId;
							this.newMemberOtherTenantListByRoleWise[this.newMember.role.id][
								'tenantName'
							] = this.newMember.role.tenantName;
						}
						//}
					}
				} else {
					if (value.role && value.role.id) {
						if(index>-1)
							this.selectedGroupMembers[index].dualRoles = value.dualRoles						
						if (this.enableCommunicationWithInternalstaffs == true) {
							if (this.internalSiteId == value.role.tenantId) {
								this.newMember = {
									id: '',
									displayName: '',
									role: {},
									dualRoles: {},
									tenantId: null,
									tenantName: '',
									otherTenantStaff:
										value.role.tenantId && this.crossTenantId != value.role.tenantId
											? true
											: false
								};
								this.newMember.id = value.id;
								this.newMember.displayName = value.displayName;
								this.newMember.role = value.role;
								this.newMember.dualRoles = value.dualRoles;
								this.newMember.tenantId = value.role.tenantId;
								this.newMember.tenantName = value.role.tenantName;
								this.otherTenantMemberList.push(this.newMember);
								var roleData = {
									id: this.newMember.role.id,
									name: this.newMember.role.displayName,
									tenantRoleId: this.newMember.role.id,tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles
								};
								this.setEditcheckedRoleIds(roleData);
								
							}
						} else {
							this.newMember = {
								id: '',
								displayName: '',
								role: {},
								dualRoles: {},
								tenantId: null,
								tenantName: '',
								otherTenantStaff:
									value.role.tenantId && this.crossTenantId != value.role.tenantId
										? true
										: false
							};
							this.newMember.id = value.id;
							this.newMember.displayName = value.displayName;
							this.newMember.role = value.role;
							this.newMember.dualRoles = value.dualRoles;
							this.newMember.tenantId = value.role.tenantId;
							this.newMember.tenantName = value.role.tenantName;
							this.otherTenantMemberList.push(this.newMember);
							var roleData = {
								id: this.newMember.role.id,
								name: this.newMember.role.displayName,
								tenantRoleId: this.newMember.role.id,tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles
							};
							this.setEditcheckedRoleIds(roleData);
						}
						if (value.dualRoles && value.dualRoles.length > 1) {
							console.log('othertenant else Enter dual Role condition=============================================');
							value.dualRoles.forEach((dualEach) => {
								console.log('dualEach======================');
								console.log(dualEach);
								if (value.role.id != dualEach.tenantUsersRoleId) {
									var dualRoleData ={
											id: dualEach.tenantUsersRoleId,
											name: dualEach.roleName,
											tenantRoleId: dualEach.tenantUsersRoleId,
											tenantId:dualEach.tenantId, tenantName:dualEach.tenantName,dualRoles:value.dualRoles
									}
									this.setEditcheckedRoleIds(dualRoleData);
								}
							});
						}
					}
				}
			} else {
				if (value.role && value.role.id) {
					if (this.enableCommunicationWithInternalstaffs == true) {
						if (this.internalSiteId == value.role.tenantId) {
							this.newMember = {
								id: '',
								displayName: '',
								role: {},
								dualRoles: {},
								tenantId: null,
								tenantName: '',
								otherTenantStaff:
									value.role.tenantId && this.crossTenantId != value.role.tenantId
										? true
										: false
							};
							this.newMember.id = value.id;
							this.newMember.displayName = value.displayName;
							this.newMember.role = value.role;
							this.newMember.tenantId = value.role.tenantId;
							this.newMember.dualRoles = value.dualRoles;
							this.newMember.tenantName = value.role.tenantName;
							this.newMemberOtherTenantList.push(this.newMember);
							this.otherTenantMemberList.push(this.newMember);
							var user = { id: this.newMember.id, name: this.newMember.displayName };
							var roleData = {
								id: this.newMember.role.id,
								name: this.newMember.role.displayName,
								tenantRoleId: this.newMember.role.id,tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles
							};
							this.setEditcheckedRoleIds(roleData);
							//if (this.newMember.role.id) {
							if (!this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]) {
								this.newMemberOtherTenantListByRoleWise[this.newMember.role.id] = {};
							}
							if (!('userList' in this.newMemberOtherTenantListByRoleWise[this.newMember.role.id])) {
								this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['userList'] = [];
							}
							if (!('roleData' in this.newMemberOtherTenantListByRoleWise[this.newMember.role.id])) {
								this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['roleData'] = {};
							}
							this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['roleData'] = roleData;
							this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['userList'].push(user);
							this.newMemberOtherTenantListByRoleWise[this.newMember.role.id][
								'tenantId'
							] = this.newMember.role.tenantId;
							this.newMemberOtherTenantListByRoleWise[this.newMember.role.id][
								'tenantName'
							] = this.newMember.role.tenantName;
						}
					} else {
						this.newMember = {
							id: '',
							displayName: '',
							role: {},
							dualRoles: {},
							tenantId: null,
							tenantName: '',
							otherTenantStaff:
								value.role.tenantId && this.crossTenantId != value.role.tenantId ? true : false
						};
						this.newMember.id = value.id;
						this.newMember.displayName = value.displayName;
						this.newMember.role = value.role;
						this.newMember.dualRoles = value.dualRoles;
						this.newMember.tenantId = value.role.tenantId;
						this.newMember.tenantName = value.role.tenantName;
						this.newMemberOtherTenantList.push(this.newMember);
						this.otherTenantMemberList.push(this.newMember);
						var user = { id: this.newMember.id, name: this.newMember.displayName };
						var roleData = {
							id: this.newMember.role.id,
							name: this.newMember.role.displayName,
							tenantRoleId: this.newMember.role.id,tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles
						};
						this.setEditcheckedRoleIds(roleData);
						//if (this.newMember.role.id) {
						if (!this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]) {
							this.newMemberOtherTenantListByRoleWise[this.newMember.role.id] = {};
						}
						if (!('userList' in this.newMemberOtherTenantListByRoleWise[this.newMember.role.id])) {
							this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['userList'] = [];
						}
						if (!('roleData' in this.newMemberOtherTenantListByRoleWise[this.newMember.role.id])) {
							this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['roleData'] = {};
						}
						this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['roleData'] = roleData;
						this.newMemberOtherTenantListByRoleWise[this.newMember.role.id]['userList'].push(user);
						this.newMemberOtherTenantListByRoleWise[this.newMember.role.id][
							'tenantId'
						] = this.newMember.role.tenantId;
						this.newMemberOtherTenantListByRoleWise[this.newMember.role.id][
							'tenantName'
						] = this.newMember.role.tenantName;
					}
					//}
				}
			}
		});
		this.newMemberOtherTenantListByRoleWise = this.newMemberOtherTenantListByRoleWise.filter(function(a) {
			return a && a.tenantId;
		});
		this.newMemberOtherTenantListByRoleWise.sort(function(a, b) {
			if (a.roleData.name < b.roleData.name) return -1;
			if (a.roleData.name > b.roleData.name) return 1;
			return 0;
		});
		console.log("this.selected.removedUsers");
		console.log(this.selected.removedUsers);
		if(this.selected.removedUsers && this.selected.removedUsers.length>0){
			this.removedRoleUsers = this.selected.removedUsers;
		}
		/**update group list after changing edit group -start */

		/**update group list after changing edit group -start */
	}
	refreshPage() {
		this.getGroups(true);
	}
	selectGroup(group) {
		this.selected = group;
	}
	clearPatientGroupSelection() {
		this.checkedIds = [];
		//this.checkedRoleIds=[];
		$('input[type="checkbox"]')
			.prop({
				indeterminate: false,
				checked: false
			})
			.siblings('label')
			.removeClass('custom-checked custom-unchecked custom-indeterminate')
			.addClass('custom-unchecked');
	}
	patientGroupSelectionAll()
	{
	  $('input[type="checkbox"]').prop({
		indeterminate: true,
		checked: true
	  }).siblings('label')
		.removeClass('custom-checked custom-unchecked custom-indeterminate')
		.addClass('custom-checked');
  
	}
	updateCheckedOptions(id, event) {
		if (event.target.checked) {
			this.addMemberError = false;
			this.disableButton = false;
			//this.noMemberError = false;
			this.checkedIds.push(id);
		} else {
			/*var index = this.checkedIds.indexOf(id);
      if (index > -1) {
          this.checkedIds.splice(index, 1);
      }*/
			this.checkedIds = this.checkedIds.filter(function(ids) {
				return ids != id;
			});
		}
	}

	removeMember(id) {
		let self = this;
		self.selectedGroupMembers = self.selectedGroupMembers.filter(function(members) {
			
			if (members.id != id) {
				return true;
			} else {
				
				if( members.dualRoles && typeof members.dualRoles == "string"){
					members.dualRoles = JSON.parse(members.dualRoles);
				}
				if(self.checkedRoleIds.length > 0){
					self.checkedRoleIds.forEach((value)=>{
						if(members.role && members.role.id && value.id==members.role.id){
							self.removedRoleUsers.push({id:id,roleId:value.id});
						} else if(members.dualRoles && members.dualRoles.length>1) {
							members.dualRoles.forEach((dual)=>{
								if(dual.tenantUsersRoleId == value.id){
									self.removedRoleUsers.push({id:id,roleId:value.id});
								}
							});
						}
					})
				}

				if(self.checkedIdsWithRole && self.checkedIdsWithRole[id]){
					delete self.checkedIdsWithRole[id];
				}

				
				 if (typeof members == 'object' && (self.optionShow == 'staffs' ||  self.optionShow == 'partner')) {
					let user = self.clinicalUserDetails.find((user) => user.userid == members.id);
					if (user && user.userid) {
						self.userListChatwith.push(user);
						self.userListChatwith = self.userListChatwith.slice();
					}	
						
					
					self.userListChatwith.sort(function(a, b) {
						if (a.displayname.toLowerCase() < b.displayname.toLowerCase()) return -1;
						if (a.displayname.toLowerCase() > b.displayname.toLowerCase()) return 1;
						return 0;
					});
				}
				else if (
					typeof members == 'object' &&
					members.hasOwnProperty('otherTenantStaff') &&
					!members.otherTenantStaff
				) {
					console.log('=============================================');
					if (members.tenantId ==  self.selectedTenant) {
						
						var user = self.clinicalUserDetails.find((user) => user.userid == members.id);
						if (user.id) {
							self.newMemberList.push(user);
							self.newMemberList = self.newMemberList.slice();
						}
					  }

					
					  self.newMemberList.forEach((value) => {
						if (value.role && value.role.id) {
							var user = {
								id: value.id,
								name: value.displayName,
								naTags: "",
								naTagNames: ""
							};

							if (value.naTags && value.naTags != null && value.naTags != 'null') {
								user.naTags = value.naTags;
								user.naTagNames = value.naTagNames;
							}

							var roleData = {
								id: value.role.id,
								name: value.role.displayName,
								tenantRoleId: value.role.id,
								tenantId: value.role.tenantId,
								tenantName: value.role.tenantName,
								dualRoles: value.dualRoles
							};
							if (!self.newMemberListByRoleWise[value.role.id]) {
								self.newMemberListByRoleWise[value.role.id] = {};
							}
							if (!('userList' in self.newMemberListByRoleWise[value.role.id])) {
								self.newMemberListByRoleWise[value.role.id]['userList'] = [];
							}
							if (!('roleData' in self.newMemberListByRoleWise[value.role.id])) {
								self.newMemberListByRoleWise[value.role.id]['roleData'] = {};
							}
							if (value.dualRoles && value.dualRoles.length > 1) {
								value.dualRoles.forEach((dualEach) => {
									if (value.role.id != dualEach.tenantUsersRoleId) {
										if (!self.newMemberListByRoleWise[dualEach.tenantUsersRoleId]) {
											self.newMemberListByRoleWise[dualEach.tenantUsersRoleId] = {};
										}
										if (!('userList' in self.newMemberListByRoleWise[dualEach.tenantUsersRoleId])) {
											self.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'] = [];
										}
										
										if (self.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'].findIndex(x => x.id == user.id) == -1) {
											self.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'].push(user);
										}
									}
								})
							}
                            self.newMemberListByRoleWise[value.role.id]['roleData'] = roleData;
							self.newMemberListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
							self.newMemberListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;

							if (self.newMemberListByRoleWise[value.role.id]['userList'].findIndex(x => x.id == user.id) == -1) {
								self.newMemberListByRoleWise[value.role.id]['userList'].push(user);
							}

						}
					});
					self.newMemberListByRoleWise = self.newMemberListByRoleWise.filter(function(a) {
						return a && a.tenantId;
					});
					self.newMemberListByRoleWise.sort(function (a, b) {
						if (a.roleData.name < b.roleData.name) return -1;
						if (a.roleData.name > b.roleData.name) return 1;
						return 0;
					});
				} else if (
					typeof members == 'object' &&
					members.hasOwnProperty('otherTenantStaff') &&
					members.otherTenantStaff
				) {
					let user = self.clinicalUserDetails.find((user) => user.userid == members.id);
					if (user && user.userid) {
						self.userListChatwith.push(user);
						self.userListChatwith = self.userListChatwith.slice();
					} else {
						let memberuser = self.selectedGroupMembers.find((memberuser) => memberuser.id == members.id);	
						if (memberuser.id) {
						memberuser.userId = memberuser.id;
						memberuser.tenantid = memberuser.tenantId;
						memberuser.displayname = memberuser.displayName;
						memberuser.role = memberuser.role.displayName;
						memberuser.roleId = memberuser.role.id;	
						self.userListChatwith.push(memberuser);
						self.userListChatwith = self.userListChatwith.slice();
						}
					}	
						
					
					self.userListChatwith.sort(function(a, b) {
						if (a.displayname.toLowerCase() < b.displayname.toLowerCase()) return -1;
						if (a.displayname.toLowerCase() > b.displayname.toLowerCase()) return 1;
						return 0;
					});
				} else {
					
					let setCurrentTenant = false;
					let setOtherTenant = false;
					if (self.newallUserDetailsInMsgGroup.length) {
						self.memberDataGroupWise = [];
						self.newMessageGroups.forEach((value) => {
							self.memberDataGroupWise[value.id] = {};
							self.memberDataGroupWise[value.id]['groupData'] = { id: value.id, name: value.name };
							self.memberDataGroupWise[value.id]['userList'] = [];
							self.newallUserDetailsInMsgGroup.forEach((user, key) => {
								if (user.id != self.userData.userId) {
									if (value.memberIds.split(',').indexOf(user.id + '') > -1) {
										if (
											self.selectedGroupDetails.memberIds.split(',').indexOf(user.id + '') == -1
										) {
											const role = {
												id: user.id,
												name: user.name,
												tenantId: user.tenantId,
												tenantName: user.tenantName
											};
											self.memberDataGroupWise[value.id]['userList'].push(role);
										}
									}
								}
							});
						});
						self.memberDataGroupWise = self.memberDataGroupWise.filter(function(item) {
							/**For remove group from list when there is no users in that group */
							if (item.userList.length != 0) {
								return true;
							} else {
								return false;
							}
						});
						self.memberDataGroupWise.sort(function(a, b) {
							if (a.groupData.name < b.groupData.name) return -1;
							if (a.groupData.name > b.groupData.name) return 1;
							return 0;
						});
						self.newallUserDetailsInMsgGroup.forEach((element) => {
							if (element.id == id) {
								if (element.tenantId == self.userData.tenantId) {
									setCurrentTenant = true;
									var user = self.memberList.find((user) => user.id == members.id);
									if (user) {
										self.newMemberList.push(user);
										self.newMemberList = self.newMemberList.slice();
									}
								} else {
									setOtherTenant = true;
									let user = self.otherTenantMemberList.find((user) => user.id == members.id);
									if (user) {
										self.newMemberOtherTenantList.push(user);
										self.newMemberOtherTenantList = self.newMemberOtherTenantList.slice();
									}
								}
							}
						});

						if (setCurrentTenant) {
							self.newMemberListByRoleWise = [];
							self.newMemberList.forEach((value) => {
									if (value.role && value.role.id) {
										let user = {
										id: value.id,
										name: value.displayName,
										naTags: "",
										naTagNames: ""
									};
									let roleData = {
										id: value.role.id,
										name: value.role.displayName,
										tenantRoleId: value.role.id, tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles
									};
									if (!self.newMemberListByRoleWise[value.role.id]) {
										self.newMemberListByRoleWise[value.role.id] = {};
									}
									if (!('userList' in self.newMemberListByRoleWise[value.role.id])) {
										self.newMemberListByRoleWise[value.role.id]['userList'] = [];
									}
									if (!('roleData' in self.newMemberListByRoleWise[value.role.id])) {
										self.newMemberListByRoleWise[value.role.id]['roleData'] = {};
									}
									if (value.dualRoles && value.dualRoles.length > 1) {
										value.dualRoles.forEach(dualEach => {
											if (value.role.id != dualEach.tenantUsersRoleId) {
												if (!self.newMemberListByRoleWise[dualEach.tenantUsersRoleId]) {
													self.newMemberListByRoleWise[dualEach.tenantUsersRoleId] = {};
												}
												if (!('userList' in self.newMemberListByRoleWise[dualEach.tenantUsersRoleId])) {
													self.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'] = [];
												}

												if (self.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'].findIndex(x => x.id == user.id) == -1) {
													self.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'].push(user);
												}
											}
										});
									}
									self.newMemberListByRoleWise[value.role.id]['roleData'] = roleData;
									self.newMemberListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
									self.newMemberListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
									self.newMemberListByRoleWise[value.role.id]['userList'].push(user);
								}
							});
							self.newMemberListByRoleWise = self.newMemberListByRoleWise.filter(function(a) {
								return a && a.tenantId;
							});
							self.newMemberListByRoleWise.sort(function(a, b) {
								if (a.roleData.name < b.roleData.name) return -1;
								if (a.roleData.name > b.roleData.name) return 1;
								return 0;
							});
						}
						if (setOtherTenant) {
							self.newMemberOtherTenantListByRoleWise = [];
							self.newMemberOtherTenantList.forEach((value) => {
								if (value.role && value.role.id) {
									var user = {
										id: value.id,
										name: value.displayName,
										naTags: "",
										naTagNames: ""
									};

									if (value.naTags && value.naTags != null && value.naTags != 'null') {
										user.naTags = value.naTags;
										user.naTagNames = value.naTagNames;
									}
									
									var roleData = {
										id: value.role.id,
										name: value.role.displayName,
										tenantRoleId: value.role.id, tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles
									};
									if (!self.newMemberOtherTenantListByRoleWise[value.role.id]) {
										self.newMemberOtherTenantListByRoleWise[value.role.id] = {};
									}
									if (!('userList' in self.newMemberOtherTenantListByRoleWise[value.role.id])) {
										self.newMemberOtherTenantListByRoleWise[value.role.id]['userList'] = [];
									}
									if (!('roleData' in self.newMemberOtherTenantListByRoleWise[value.role.id])) {
										self.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = {};
									}
									if (value.dualRoles && value.dualRoles.length > 1) {
										
										value.dualRoles.forEach((dualEach) => {
											if (value.role.id != dualEach.tenantUsersRoleId) {
												if (
													!self.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]
												) {
													self.newMemberOtherTenantListByRoleWise[
														dualEach.tenantUsersRoleId
													] = {};
												}
												if (
													!(
														'userList' in
														self.newMemberOtherTenantListByRoleWise[
															dualEach.tenantUsersRoleId
														]
													)
												) {
													self.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId][
														'userList'
													] = [];
												}
												self.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId][
													'userList'
												].push(user);
											}
										});
									}
									self.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = roleData;
									self.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
									self.newMemberOtherTenantListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
									self.newMemberOtherTenantListByRoleWise[value.role.id]['userList'].push(user);
									self.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] =
										value.role.tenantId;
										self.newMemberOtherTenantListByRoleWise[value.role.id]['tenantName'] =
										value.role.tenantName;
								}
							});

							self.newMemberOtherTenantListByRoleWise = self.newMemberOtherTenantListByRoleWise.filter(
								function(a) {
									return a && a.tenantId;
								}
							);
							self.newMemberOtherTenantListByRoleWise.sort(function(a, b) {
								if (a.roleData.name < b.roleData.name) return -1;
								if (a.roleData.name > b.roleData.name) return 1;
								return 0;
							});
						}
					}
				}
				return false;
			}
		});
		if (!self.selectedGroupMembers.length && !self.checkedRoleIds.length) {
			self.disableButton = true;
			self.noMemberError = true;
		} else {
			if (!self.patientUser) {
				self.disableButton = true;
			} else {
				self.disableButton = false;
			}
		}

		
	}
	removeRoleStaffs(role) {
		this.selectedGroupMembers = this.selectedGroupMembers.filter((member)=>{
		let memberRoleIds = (typeof member.roleIds === 'string') ? JSON.parse(member.roleIds) : member.roleIds;
		  if(Array.isArray(memberRoleIds)){
			if(!memberRoleIds.includes(+role.id)) {
			  return member;
			} 
		  } else if(memberRoleIds !== +role.id) {
			  return member
		  }
		});
	  }
	addMember() {
		var self = this;
		this.addMemberError = false;
		this.clickOnAddMemberBtn = true;
		$(':checkbox:checked').each(function(i) {
			var name = this.name;
			if (name == 'cliniciansRoleUser[]') {
				var index = self.selectedGroupMembers.findIndex((x) => x.id == $(this).val());
				if (index === -1) {
					self.checkedIds.push($(this).val());
					if($(this)[0].attributes['data-roleset'] && $(this)[0].attributes['data-roleset'].value)
						self.checkedIdsWithRole[$(this).val()]=$(this)[0].attributes['data-roleset'].value;
				}
			}else if(name=="staffRoleWise"){
				console.log("Addmember=====>");console.log($(this).val());
				var selectedRole =JSON.parse($(this).val());
				console.log("selectedRole=======>",selectedRole);
				console.log("typeof selectedRole==>"+typeof selectedRole);
				console.log("selectedRole")
				var exist =false;
				if(self.checkedRoleIds.length){
				  self.checkedRoleIds.forEach((value)=>{
					console.log("value==========>",value);
					console.log("typeof value==> "+typeof value);
					console.log("parse===>",);
					console.log("value.id ==selectedRole.id===>"+value.id +"=="+selectedRole.id)
					if(value.id ==selectedRole.id){
					  exist = true;
					}
				  });
				}
				console.log("addMember exist",exist);
				if(!exist){
				  self.checkedRoleIds.push(selectedRole);
				  self.removeRoleStaffs(selectedRole);
				}
				console.log("addMember : self.checkedRoleIds=========");
				console.log(self.checkedRoleIds)
			} else if(name=="messageGroup"){
       
				var selectedMessageGroup =JSON.parse($(this).val());       
				console.log(" addMember- messageGroup - >> ",selectedMessageGroup,self.dummyCheckedGroupMembers,self.selectedGroupMembers);
				if(self.dummyCheckedGroupMembers.indexOf(selectedMessageGroup)>-1){
				  var exist =false;
				  if(self.checkedIds.length){
					self.checkedIds.forEach((value)=>{             
					  if(value.id ==selectedMessageGroup){
						exist = true;
					  }
					});
				  }          
				  if(!exist){
					self.checkedIds.push(selectedMessageGroup);
				  }
				}         
			}   
		});
		if (this.checkedIds.length > 0) {
			this.addMemberToList(this.checkedIds);
			this.noMemberError = false;
			this.checkedIds = [];
			this.addMemberError = false;
			this.disableButton = false;
		} else if(this.optionShow === 'groups' || this.optionShow === 'msggroups') {
			  this.resetPdgMembers = true;
			  this.clearPatientGroupSelection();    
			  if(this.dummyCheckedGroupMembers && this.dummyCheckedGroupMembers.length > 0){
			  self.getMessageGroupMembersByGroupId(this.dummyCheckedGroupMembers);
			  var liLength = $('#treeview li').length;
			  this.offset = 25-liLength;
			  this.srch.nativeElement.value = "";
			  this.dummyCheckedGroupMembers = [];
			  }
		} 
		else if (this.checkedRoleIds.length > 0   && this.optionShow !== 'groups' && this.optionShow !== 'msggroups') {
			this.addMemberToList(this.checkedRoleIds);
		   }else {
			this.clickOnAddMemberBtn = false;
			this.checkedIds = [];
		}
	}
	addMemberToList(checkedIds) {
		this.clickOnAddMemberBtn = false;
		if (this.optionShow == 'staffroles' || this.optionShow == 'partnerroles') {
			this.staffList.forEach((element) => {
				console.log("addMemberToList ",element)
				this.checkedRoleIds.forEach((rolevalue,key) => {          
					if(rolevalue.id ==element.id){
						this.selectedroleBasedStaffs.forEach(users => {
							console.log(this.selectedGroupMembers);
							if (this.selectedGroupMembers.findIndex(x => x.id === users.id) === -1) {
							  this.newMember = {
								id: "",
								displayName: "",
								role: {},
								tenantId: null,
								tenantName: "",
								naTags: "",
								naTagNames: ""
							  }; 
							  users.assignRoles.forEach(e => {
								console.log("innnnn")
							   
								if(e['isPrimary'] == true){
								  this.primaryId = e['tenantUsersRoleId'];
								  this.primaryRolename = e['roleName'];
								}
								if(e['isPrimary'] == false){
								  this.secondaryId = e['tenantUsersRoleId'];
								}
								
							  });
							  
							  	
								this.newMember.role = this.primaryId;           
								this.newMember.tenantId = element.tenantId;
								this.newMember.tenantName = element.tenantName; 
								this.newMember.id = users.id;
							        this.newMember.displayName = users.displayName;    
						                this.newMember.naTags = users.naTags; 
										this.newMember.naTagNames = users.naTagNames; 
										var user = { id: this.newMember.id, name: this.newMember.displayName, role:this.newMember.role};
  
							  
							  this.selectedGroupMemberIds.push(this.newMember.id);
							  this.testArr.push(user);

							}
						   
							this.selectedGroupMembers = this.selectedGroupMembers.slice();
							this.selectedGroupMemberIds = this.selectedGroupMemberIds.slice();
						  })

						  console.log("newMemberListByRoleWise========before filter========>");
						  console.log(this.newMemberListByRoleWise);      
			  
			  
						this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter(function (members) {
						  return members.roleData.id != element.id;
						});
						console.log("newMemberListByRoleWise========after filter========>");
						console.log(this.newMemberListByRoleWise);


					//Delete a user if the user is added to GP members when user role is checked 
					if(this.removedRoleUsers.length>0){
					this.removedRoleUsers.forEach((value,key)=>{
						if(value.id ==element.id && value.roleId==element.role.id){
						this.removedRoleUsers.splice(key,1);
					}
				})
			  }


			  
			}
		
        })
				//Delete user from removedRoleUsers if its role is unchecked
				if(this.removedRoleUsers.length >0 && this.removedRoleUsers.indexOf(element.id)>-1){
					var detect = false;
					this.checkedRoleIds.forEach((value)=>{
					if(value.id ==element.role.id){
						detect =true;
					}
					});
					if(!detect){
					this.removedRoleUsers.splice(this.removedRoleUsers.indexOf(element.id),1);
					}
				}				
			});
			console.log("selectedGroupMembers================>");
			console.log(this.selectedGroupMembers);
			this.newMemberListByRoleWise.sort(function (a, b) {
				if (a.roleData.name < b.roleData.name) return -1;
				if (a.roleData.name > b.roleData.name) return 1;
				return 0;
			  });
			  console.log("newMemberListByRoleWise================>");
			console.log(this.newMemberListByRoleWise);
		} else if (this.optionShow == 'staffs' || this.optionShow == 'partner') {
			let staffInSelectedRole = false;
			this.clinicalUserDetails.forEach((element) => {
				if (this.checkedIds.includes(element.userid)) {
					let memberRoleIds = JSON.parse(element.dualRoles).map(item => item.tenantUsersRoleId);
					if(!this.checkedRoleIds.some(item => memberRoleIds.includes(+item.id))) {
						this.newMember = {
							id: '',
							displayName: '',
							role: {},
							dualRoles: {},
							tenantId: null,
							tenantName: '',
							otherTenantStaff: true,	naTags: "",
							naTagNames: "",
              						roleIds: ""
						};
						this.newMember.roleIds = JSON.parse(element.dualRoles).map(item => item.tenantUsersRoleId);
						this.newMember.id = element.userid;
						this.newMember.displayName = element.displayname;
						this.newMember.role = element.role;
						this.newMember.dualRoles = element.dualRoles;
						this.newMember.tenantId = element.tenantid;
						this.newMember.tenantName = element.tenantName;
							this.newMember.naTags = element.naTags; 
							this.newMember.naTagNames = element.naTagNames;   
						if (this.selectedGroupMembers.findIndex((x) => x.id === element.userid) === -1) {
							this.selectedGroupMembers.push(this.newMember);
							
						}
						this.selectedGroupMembers = this.selectedGroupMembers.slice();
						this.userListChatwith = this.userListChatwith.filter((members)=> {
							return members.userid != element.userid;
						});
					} else {
						staffInSelectedRole = true;
					}
				}
			});
			if (staffInSelectedRole) {
				this._structureService.notifyMessage({
				  messge: this._ToolTipService.getTranslateData('WARNING.MEMBER_WITH_SAME_ROLE_EXISTS'),
				  type: CONSTANTS.notificationTypes.warning
				});
			}
			this.newMemberOtherTenantListByRoleWise = [];
			this.newMemberOtherTenantList.forEach((value) => {
				if (value.role && value.role.id) {
					var user = { id: value.id, name: value.displayName,naTags: "",naTagNames: "" };
					var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id, tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles};
					if (!this.newMemberOtherTenantListByRoleWise[value.role.id]) {
						this.newMemberOtherTenantListByRoleWise[value.role.id] = {};
					}
					if (!('userList' in this.newMemberOtherTenantListByRoleWise[value.role.id])) {
						this.newMemberOtherTenantListByRoleWise[value.role.id]['userList'] = [];
					}
					if (!('roleData' in this.newMemberOtherTenantListByRoleWise[value.role.id])) {
						this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = {};
					}
					this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = roleData;
					this.newMemberOtherTenantListByRoleWise[value.role.id]['userList'].push(user);
					this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
					this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;

					if (value.dualRoles && value.dualRoles.length > 1) {
						console.log("Enter dual Role condition=============================================");
						value.dualRoles.forEach(dualEach => {
							console.log("dualEach======================");
							console.log(dualEach);
							if (value.role.id != dualEach.tenantUsersRoleId) {
								if (!this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]) {
									this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId] = {};
								}
								if (!('userList' in this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId])) {
									this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]['userList'] = [];
								}
								this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]['userList'].push(user);
							}
						})
					}

				}
			});

			this.newMemberOtherTenantListByRoleWise = this.newMemberOtherTenantListByRoleWise.filter(function (a) {
				return a && a.tenantId;
			});
			this.newMemberOtherTenantListByRoleWise.sort(function (a, b) {
				if (a.roleData.name < b.roleData.name) return -1;
				if (a.roleData.name > b.roleData.name) return 1;
				return 0;
			});
			/*this.memberDataGroupWise.map((group) => {
				group.userList = group.userList.filter((user) => this.checkedIds.indexOf(user.id) == -1);
			});*/
		} else {
			let setCurrentTenant = false;
			let setOtherTenant = false;
			this.allUserDetailsInMsgGroup.forEach((element) => {
				if (this.checkedIds.includes(element.id)) {
					this.newMember = {
						id: '',
						displayName: '',
						role: {},
						dualRoles: {},
						tenantId: null,
						tenantName: '',	naTags: "",
						naTagNames: ""
					};
					this.newMember.id = element.id;
					this.newMember.displayName = element.name;
					this.newMember.role = element.role ? element.role : [];
					this.newMember.dualRoles = element.dualRoles;
					this.newMember.tenantId = element.tenantId;
				        this.newMember.naTags = element.naTags; 
				        this.newMember.naTagNames = element.naTagNames;  
					if (this.selectedGroupMembers.findIndex((x) => x.id === element.id) === -1) {
						var memberIds = this.selectedGroupDetails.memberIds.split(',');
						memberIds.push(this.newMember.id);
						memberIds = memberIds.join();
						this.selectedGroupDetails.memberIds = memberIds;
					}
					this.selectedGroupMembers = this.selectedGroupMembers.slice();

					if (element.tenantId == this.userData.tenantId) {
						setCurrentTenant = true;
						this.newMemberList = this.newMemberList.filter(function(members) {
							return members.id != element.id;
						});
					} else {
						setOtherTenant = true;
						this.newMemberOtherTenantList = this.newMemberOtherTenantList.filter(function(members) {
							return members.id != element.id;
						});
					}
				}
			});
			if (setCurrentTenant) {
				this.newMemberListByRoleWise = [];
				this.newMemberList.forEach((value) => {
					if (value.role && value.role.id) {
						var user = { id: value.id, name: value.displayName,naTags:value.naTags,naTagNames:value.naTagNames };
						var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id, tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles };
						if (!this.newMemberListByRoleWise[value.role.id]) {
							this.newMemberListByRoleWise[value.role.id] = {};
						}
						if (!('userList' in this.newMemberListByRoleWise[value.role.id])) {
							this.newMemberListByRoleWise[value.role.id]['userList'] = [];
						}
						if (!('roleData' in this.newMemberListByRoleWise[value.role.id])) {
							this.newMemberListByRoleWise[value.role.id]['roleData'] = {};
						}

						if (value.dualRoles && value.dualRoles.length > 1) {
							console.log("Enter dual Role condition=============================================");
							value.dualRoles.forEach(dualEach => {
								console.log("dualEach======================");
								console.log(dualEach);
								if (value.role.id != dualEach.tenantUsersRoleId) {
									if (!this.newMemberListByRoleWise[dualEach.tenantUsersRoleId]) {
										this.newMemberListByRoleWise[dualEach.tenantUsersRoleId] = {};
									}
									if (!('userList' in this.newMemberListByRoleWise[dualEach.tenantUsersRoleId])) {
										this.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'] = [];
									}
									if (this.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'].findIndex(x => x.id == user.id) == -1) {
										this.newMemberListByRoleWise[dualEach.tenantUsersRoleId]['userList'].push(user);
									}
								}
							})
						}

						this.newMemberListByRoleWise[value.role.id]['roleData'] = roleData;
						this.newMemberListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
						this.newMemberListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;

						if (this.newMemberListByRoleWise[value.role.id]['userList'].findIndex(x => x.id == user.id) == -1) {
							this.newMemberListByRoleWise[value.role.id]['userList'].push(user);
						}
					}
				});

				this.newMemberListByRoleWise = this.newMemberListByRoleWise.filter(function (a) {
					return (a && a.tenantId);
				});

				this.newMemberListByRoleWise.sort(function (a, b) {
					if (a.roleData.name < b.roleData.name) return -1;
					if (a.roleData.name > b.roleData.name) return 1;
					return 0;
				});
			}

			if (setOtherTenant) {
				this.newMemberOtherTenantListByRoleWise = [];
				this.newMemberOtherTenantList.forEach((value) => {
					if (value.role && value.role.id) {
						var user = { id: value.id, name: value.displayName,naTags : value.naTags,naTagNames : value.naTagNames };
						var roleData = { id: value.role.id, name: value.role.displayName, tenantRoleId: value.role.id, tenantId:value.role.tenantId, tenantName:value.role.tenantName,dualRoles:value.dualRoles };
						if (!this.newMemberOtherTenantListByRoleWise[value.role.id]) {
							this.newMemberOtherTenantListByRoleWise[value.role.id] = {};
						}
						if (!('userList' in this.newMemberOtherTenantListByRoleWise[value.role.id])) {
							this.newMemberOtherTenantListByRoleWise[value.role.id]['userList'] = [];
						}
						if (!('roleData' in this.newMemberOtherTenantListByRoleWise[value.role.id])) {
							this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = {};
						}

						if (value.dualRoles && value.dualRoles.length > 1) {
							console.log("Enter dual Role condition=============================================");
							value.dualRoles.forEach(dualEach => {
								console.log("dualEach======================");
								console.log(dualEach);
								if (value.role.id != dualEach.tenantUsersRoleId) {
									if (!this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]) {
										this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId] = {};
									}
									if (!('userList' in this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId])) {
										this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]['userList'] = [];
									}
									this.newMemberOtherTenantListByRoleWise[dualEach.tenantUsersRoleId]['userList'].push(user);
								}
							})
						}

						this.newMemberOtherTenantListByRoleWise[value.role.id]['roleData'] = roleData;
						this.newMemberOtherTenantListByRoleWise[value.role.id]['userList'].push(user);
						this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantId'] = value.role.tenantId;
						this.newMemberOtherTenantListByRoleWise[value.role.id]['tenantName'] = value.role.tenantName;
					}
				});

				this.newMemberOtherTenantListByRoleWise = this.newMemberOtherTenantListByRoleWise.filter(function (a) {
					return (a && a.tenantId);
				});
				this.newMemberOtherTenantListByRoleWise.sort(function (a, b) {
					if (a.roleData.name < b.roleData.name) return -1;
					if (a.roleData.name > b.roleData.name) return 1;
					return 0;
				});
			}

			// this.memberDataGroupWise = [];
			if (this.newallUserDetailsInMsgGroup.length) {
				this.memberDataGroupWise = [];
				this.newMessageGroups.forEach(value => {
					this.memberDataGroupWise[value.id] = {}
					this.memberDataGroupWise[value.id]['groupData'] = {
						id: value.id,
						name: value.name
					};
					this.memberDataGroupWise[value.id]['userList'] = [];
					this.newallUserDetailsInMsgGroup.forEach((user, key) => {
						if (user.id != this.userData.userId) {
							if (value.memberIds.split(',').indexOf(user.id + '') > -1) {
								if (this.selectedGroupDetails.memberIds.split(',').indexOf(user.id + '') == -1) {
								var role = {
									id: user.id,
									name: user.name,
									tenantId: user.tenantId,
									tenantName: user.tenantName,
									natags: user.naTags,
									naTagNames: user.naTagNames
								};

								this.memberDataGroupWise[value.id]['userList'].push(role);
								}
							}
						}
					});
				});
				this.memberDataGroupWise = this.memberDataGroupWise.filter(function (item) {
					if (item.userList.length != 0) {
						return true;
					} else {
						return false;
					}
				});
				this.memberDataGroupWise.sort(function (a, b) {
					if (a.groupData.name < b.groupData.name) return -1;
					if (a.groupData.name > b.groupData.name) return 1;
					return 0;
				});
			}
		}
		this.selectedGroupMembers = this.removeDuplicates(this.selectedGroupMembers,"id");
		console.log("this.newMemberListByRoleWise", this.newMemberListByRoleWise);
		console.log("this.newMemberOtherTenantListByRoleWise", this.newMemberOtherTenantListByRoleWise);
		console.log("this.memberDataGroupWise", this.memberDataGroupWise);
		var isPublic = (this.selectedisPublic) ? "Public" : " Not public";
		var allowMultiThreadChat = (this.selectedAllowMultiThread) ? "Multi thread chat is allowed" : "Multi thread chat is not allowed";
		var activityLogMessage = this.userData.displayName + " Added users ( " + JSON.stringify(this.checkedIds) + " ) to Group(" + this.messageGroup.controls['messageGroupName'].value + " " + this.messageGroup.controls['messageGroupLastName'].value + " DOB " + this.messageGroup.controls['messageGroupDob'].value + "),branch is " + this.messageGroup.controls['messageGroupBranch'].value + " and this group is " + isPublic + " and " + allowMultiThreadChat + " for the group";

		var activityData = {
			activityName: "Add Users to Discussion Group",
			activityType: "manage group messaging",
			activityDescription: activityLogMessage
		};
		console.log("activityDate :::", activityData);
		this._structureService.trackActivity(activityData);
		if (!this.selectedGroupMembers.length && !this.checkedRoleIds.length) {
			this.disableButton = true;
			this.noMemberError = true;
		} else {
			if (!this.patientUser) {
				this.disableButton = true;
			} else {
				this.disableButton = false;
			}
			this.noMemberError = false;
		}
	}
	updateMessageGroup(id, f) {
		this.disableButton = true;
		const patientUniqueId = this.messageGroup.controls['patientUniqueId'].value.trim();
		const tagMRN = (!isBlank(patientUniqueId))? ' [MRN: '+patientUniqueId+']': '';
		if (this.selectedGroupMembers.length || this.checkedRoleIds.length ) {
			let selectedRoleIds = [];
			if(this.checkedRoleIds.length){
			  this.checkedRoleIds.forEach((value,key) => {
				if(isBlank(value.defaultRole) || (!isBlank(value.defaultRole) && value.defaultRole === '0')) selectedRoleIds.push(value.id ? value.id.toString() : value.roleId.toString());  
			  })
			}
			var selectedGroupMembers = this.selectedGroupMembers.map((member) => {
				return { id: member.id, displayName: member.displayName, tenantId: parseInt(member.tenantId), tenantName: member.tenantName };
			  });
			this.noMemberError = false;
			let newName =
					this.messageGroup.controls['messageGroupName'].value +
					' ' +
					this.messageGroup.controls['messageGroupLastName'].value +
					' DOB ' +
					this.messageGroup.controls['messageGroupDob'].value;
				if (this.selected.patientUser) {
					console.log('Enter updateUser options.................');
					var grpId = null;
					if (this.messageGroup.controls['messageGroupBranch'].value != 0) {
						grpId = 3;
					}
					var index = this.groupListNames.findIndex(
						(x) => x.name.replace(/\s/g, '').toLowerCase() == newName.replace(/\s/g, '').toLowerCase()
					);
					if (index === -1) {
						var inputdata;
						var bRanchh = '';
						if(this.messageGroup.controls['messageGroupBranch'].value == "0"){
						bRanchh = this.userData.crossTenantId;
						}else{
						bRanchh = this.messageGroup.controls['messageGroupBranch'].value;
						}
						inputdata = {
							firstname: this.messageGroup.controls['messageGroupName'].value,
							lastname: this.messageGroup.controls['messageGroupLastName'].value,
							dob: this.messageGroup.controls['messageGroupDob'].value,
							operation: 'checking'
						};
						//If MRN is found, do MRN check
						if(!isBlank(patientUniqueId)){
							inputdata.MRNumber = patientUniqueId;
							inputdata.operation = 'mrnChecking';
						}
						const pdgItem : PdgItems = {
							isPublic: this.selectedisPublic,
							allowMultiThreadChat: this.selectedAllowMultiThread,
							members: selectedGroupMembers.map(item => {return Number(item.id)}),
							roles: selectedRoleIds.map(item => {return Number(item)}),
						}
						const reqParam : PatientDiscussionGroup = {
							data: pdgItem,
							id: +id,
							admissionId: this._structureService.isMultiAdmissionsEnabled ? this.pdgData.admissionId : undefined
						}
						this.messageService.updatePdg(reqParam).subscribe((res) => {
							if (res.success) {
								this.getGroups(false);
								let newMessageGroupData = {
									status: 'up',
									id: id,
									name: newName,
									citusRoleId: 3,
									userId: this.userData.userid,
									tenantId: this.userData.tenantId,
									type: 'group'
								};
								this._structureService.socket.emit('newUserSignUp', newMessageGroupData);
								for (let i = 0; i < this.messageGroups.length; i++) {
									if (this.messageGroups[i].id == newMessageGroupData.id) {
										this.messageGroups[i].name = newMessageGroupData.name;
									}
								}
								const activityLogMessage =
											this.userData.displayName +
											' updated Discussion Group(' +
											this.messageGroup.controls['messageGroupName'].value +
											' ' +
											this.messageGroup.controls['messageGroupLastName'].value +
											' DOB ' +
											this.messageGroup.controls['messageGroupDob'].value +
											'), members are ' +
											  +
											' ,branch is ' +
											this.userData.tenantId +
											' and self group is ' +
											this.selectedisPublic +
											' and ' + this.selectedAllowMultiThread + ' for the group';

										const activityData = {
											activityName: 'Update Discussion Group',
											activityType: 'manage group messaging',
											activityDescription: activityLogMessage
										};
										this._structureService.trackActivity(activityData);
										NProgress.done();
										this.disableButton = false;
										this._structureService.notifyMessage({
											messge: this._ToolTipService.getTranslateData('SUCCESS_MESSAGES.PDG_UPDATE'),
											type: CONSTANTS.notificationTypes.success
										});
										setTimeout(()=> { 
											this.router.navigate([ '/message/patient-discussion-groups' ]);
										}, 1000);
							}
						});
					}
				}
		} else {
			if (!this.selectedGroupMembers.length && !this.checkedRoleIds.length) {
				this.disableButton = true;
				this.noMemberError = true;
			}
		}
	}
	async patientExistsConfirmationBeforeSshifting() {
		return await new Promise(resolve => {
			let selectedBranch = this.crossTenatsForBranch.find( (tenant)=> tenant.id == this.messageGroup.controls['messageGroupBranch'].value);
			let confirmMesssage = this.messageGroup.controls['messageGroupName'].value + ' ' + this.messageGroup.controls['messageGroupLastName'].value + 
			' DOB(' + this.messageGroup.controls['messageGroupDob'].value + ') already exists.';
			let confirmText = "Patient with " + this.messageGroup.controls['messageGroupName'].value + " " + this.messageGroup.controls['messageGroupLastName'].value + 
			" " + this.messageGroup.controls['messageGroupDob'].value + " already exist in " + selectedBranch.tenantName + ". This update will create duplicate patient in " + selectedBranch.tenantName + ". Do you want to continue?";
			swal({
				title: confirmMesssage,
				text: confirmText,
				type: 'warning',
				showCancelButton: true,
				cancelButtonClass: 'btn-default',
				confirmButtonClass: 'btn-warning',
				confirmButtonText: 'Ok',
				closeOnConfirm: true
			}, (isConfirm)=>{
				if(isConfirm) {
					resolve(true);
				} else {
					resolve(false);
				}
			});
		});
	}
	setMemberDataGroupWise() {
		let messageGroup = this.messageGroups;
		const ids = this.pdgData.patientId || '';
		this.memberDataGroupWise = [];
		messageGroup.forEach((value) => {
			if (value.id != ids) {
				this.memberDataGroupWise[value.id] = {};
				this.memberDataGroupWise[value.id]['groupData'] = { id: value.id, name: value.name };
				this.memberDataGroupWise[value.id]['userList'] = value.memberDetails;
				this.memberDataGroupWise[value.id]['selectedRoles'] = (value.selectedRoles)?value.selectedRoles:[];
				this.memberDataGroupWise[value.id]['userList'] = this.memberDataGroupWise[value.id]['userList'].filter(
					(x) => !this.selectedGroupMembers.some((y) => y.id == x.id)
				);
			}
		});
		this.memberDataGroupWise = this.memberDataGroupWise.filter(function(item) {
			if (item.userList) {
				return true;
			}
			else {
				return false;
			}
		});
		this.memberDataGroupWise = this.memberDataGroupWise.slice();

		this.memberDataGroupWise.sort(function(a, b) {
			if (a.groupData.name.toLowerCase() < b.groupData.name.toLowerCase()) return -1;
			if (a.groupData.name.toLowerCase() > b.groupData.name.toLowerCase()) return 1;
			return 0;
		});
		this.getAllMessageGroupsLoading = false;
	}
	createMessageGroup() {
		console.log('createMessageGroup=====================> ');

		console.log(this.updateParams);
		let updateSiteId = (this.selectSiteId == undefined || this.selectSiteId == 0 || isBlank(this.selectSiteId) && !isBlank(this.editedSite)) ? this.editedSite.toString() : this.selectSiteId;
		this._structureService.createMessageGroup(this.updateParams,true,updateSiteId).then((response) => {
			let msgId: any = response;
			if (msgId.createMessageGroup.id != 0) {
				let newMessageGroupData = {
					status: 'new',
					id: msgId.createMessageGroup.id,
					name:
						this.messageGroup.controls['messageGroupName'].value +
						' ' +
						this.messageGroup.controls['messageGroupLastName'].value +
						' DOB ' +
						this.messageGroup.controls['messageGroupDob'].value,
					createdBy: this.userData.userId,
					tenantId:
						this._structureService.getCookie('crossTenantId') &&
						this._structureService.getCookie('crossTenantId') != 'undefined' &&
						this._structureService.getCookie('tenantId') !==
							this._structureService.getCookie('crossTenantId')
							? this._structureService.getCookie('crossTenantId')
							: this.userData.tenantId,
					isPublic: this.selectedisPublic,
					allowMultiThreadChat: this.selectedAllowMultiThread,
					branch: this.messageGroup.controls['messageGroupBranch'].value,
					patientUniqueId: this.updatedId,
					pdgroup: '1',
					members: '',
					memberIds: '',
					cmisIds: '',
					citusRoleId: 3,
					type: 'group'
				};
				console.log('this.messageGroups', this.messageGroups);
				console.log('this.messageGroups', newMessageGroupData);

				console.log('typeof this.messageGroups', typeof this.messageGroups, this.messageGroups.length);
				this.messageGroups.unshift(newMessageGroupData);
				this.messageGroups = this.messageGroups.slice();
				this.setMemberDataGroupWise();
				this._SharedService.newMessageGroup.emit(newMessageGroupData);
				this._structureService.socket.emit('newUserSignUp', newMessageGroupData);
				//this.getGroups(true);

				var activityLogMessage =
					this.userData.displayName +
					' created Discussion Group(' +
					this.messageGroup.controls['messageGroupName'].value +
					' ' +
					this.messageGroup.controls['messageGroupLastName'].value +
					' DOB ' +
					this.messageGroup.controls['messageGroupDob'].value +
					') ,branch is ' +
					this.userData.tenantId +
					' .This group is ' +
					this.selectedisPublic +
					' and the multi thread chat is ' + this.selectedAllowMultiThread;

				var activityData = {
					activityName: 'Create Discussion Group',
					activityType: 'manage group messaging',
					activityDescription: activityLogMessage
				};
				this._structureService.trackActivity(activityData);

				this.disableButton = false;
				var notify = $.notify('Success! Patient Discussion Group created');
				setTimeout(function() {
					notify.update({
						type: 'success',
						message: '<strong>Success! Patient Discussion Group created</strong>'
					});
				}, 1000);
				this.router.navigate([ '/message/patient-discussion-groups' ]);
			} else {
				var index = this.selectedGroupMembers.indexOf(this.newMember);
				if (index > -1) {
					this.selectedGroupMembers.splice(index, 1);
				}
				this.disableButton = false;
				var notify = $.notify('Error! Patient Discussion Group already exists');
				setTimeout(function() {
					notify.update({
						type: 'danger',
						message: '<strong>Error! Patient Discussion Group already exists</strong>'
					});
				}, 1000);
			}
		});
	}
	togglePublic(value) {
		this.selectedisPublic = value;
	}
	toggleMultiThreadOption(value) {
		this.selectedAllowMultiThread = value;
	}
	deleteGroup(id) {
		console.log('delete group......111111111.........');
		swal(
			{
				title: 'Are you sure?',
				text: 'You will not be able to recover this patient discussion group',
				type: 'warning',
				showCancelButton: true,
				cancelButtonClass: 'btn-default',
				confirmButtonClass: 'btn-warning',
				confirmButtonText: 'Ok',
				closeOnConfirm: true
			},
			() => {
				console.log('delete group...............');
				this._structureService.deleteMessageGroup(id).subscribe((data) => {
					let msgId: any = data;
					let newMessageGroupData = {
						status: 'delete',
						id: msgId.data.deleteMessageGroup.id,
						citusRoleId: 3,
						userId: this.userData.userid,
						tenantId: this.userData.tenantId,
						type: 'group'
					};
					//this._SharedService.newMessageGroup.emit(newMessageGroupData);
					// this.getGroups(true);
					console.log('emit dta=======>', newMessageGroupData);
					this._structureService.socket.emit('newUserSignUp', newMessageGroupData);
					for (let i = 0; i < this.messageGroups.length; i++) {
						if (this.messageGroups[i] != undefined && newMessageGroupData.id == this.messageGroups[i].id) {
							delete this.messageGroups[i];
						}
					}
					this.setMemberDataGroupWise();
				});
			}
		);
	}
	removeExistingRole(roleID){
		if(this.checkedRoleIds.length){
		  this.checkedRoleIds.forEach((value,key) => {
			if(value.id==roleID){
			  this.checkedRoleIds.splice(key,1);
			  this.removeRoleFromExistingMember(roleID,value.citusRoleId);
			}
		  });
		  if (!this.selectedGroupMembers.length && !this.checkedRoleIds.length) {
			this.noMemberError = true;
			this.disableButton = true
		  }
		}
	}
	removeRoleFromExistingMember(roleId,citusRoleId){ 
		var self = this;   
		console.log(roleId); 
		if(citusRoleId != 20){
			var MemberListByRoleWiseArr =  self.MemberListByRoleWiseStaff;
			} else if((citusRoleId == 20)){
			var MemberListByRoleWiseArr =  self.MemberListByRoleWisepartner;
		   }
		
		console.log("selectedmembers................................",self.selectedGroupMembers); 
		console.log("MemberListByRoleWiseArr....................",MemberListByRoleWiseArr); 
		if(MemberListByRoleWiseArr.length > 0){
			MemberListByRoleWiseArr.forEach((value,key) => {
			
				if(value.roleData.id==roleId){ 
				  if(self.selectedGroupMembers && self.selectedGroupMembers.length > 0){  
					if( value.userList == undefined) {
						value.userList =[]; 
						console.log("testArray?????????????????????????",this.testArr)
	  
						let i=0;
						this.testArr.forEach(element => {
						  if(element.role == roleId){
							value.userList[i]={ id: element.id, name: element.name};
							}
						  i++;
						}); 
		  
					  }
					if(value.userList){  
				  value.userList.map(user => {            
					self.selectedGroupMembers = self.selectedGroupMembers.filter((members)=>
				   { 
					if (members.id != user.id) {              
					  return true;
					} else{
					  
					  var users = self.clinicalUserDetails.find((users) => users.userid == members.id);            
					  console.log(users); 
					  if (users && users.userid) {
						self.userListChatwith.push(users);
						self.userListChatwith = self.userListChatwith.slice();
					  }  
					}
				  });
				  self.selectedGroupMembers = self.selectedGroupMembers.filter((members)=>
                  {             
				   if (members.role && members.role.id != roleId && members.seconRoleId != roleId ) {              
					return true;
                  } 
                  });
				  self.selectedroleBasedStaffs = self.selectedroleBasedStaffs.filter(function (selectedmembers) { console.log(selectedmembers.id +"!="+ user.id);
					if (selectedmembers.id != user.id) {              
					  return true;
					} 
				  });
				  
				});
	
			} else {
				console.log(self.selectedGroupMembers); 
				
				self.selectedGroupMembers = self.selectedGroupMembers.filter((members)=>
				{
					console.log(members); 
					 
				 if (members.role.id != roleId) {              
				   return true;
				 } 
			   });
			   console.log(self.selectedGroupMembers); 	
			  }
	
	
			  }
			  var memberRole = value;       
			  if(this.optionShow == 'staffroles' && citusRoleId != 20){
				self.newMemberListByRoleWise.push(memberRole);
			   } else if(this.optionShow == 'partnerroles' && citusRoleId == 20){
				self.newMemberListByRoleWise.push(memberRole);
			   }        
			 // self.newMemberListByRoleWise.push(memberRole);
      self.newMemberListByRoleWise.forEach(mem => {
      
        console.log(mem.roleData.id);
         if(mem.roleData.id==roleId){ 
            mem['userList']=[];           
         }
      });
					  self.newMemberListByRoleWise = self.newMemberListByRoleWise.slice();                               
					  self.newMemberListByRoleWise.sort(function (a, b) {              
						if (a.roleData.name.toLowerCase() < b.roleData.name.toLowerCase()) return -1;
						if (a.roleData.name.toLowerCase() > b.roleData.name.toLowerCase()) return 1;
						return 0;
					 
					  });
					  } else{
						    this.selectedGroupMembers = this.selectedGroupMembers.filter((members)=>
							   { 
								let memberRoleIds = (typeof members.roleIds === 'string') ? JSON.parse(members.roleIds) : members.roleIds;
								if (!memberRoleIds.includes(roleId) && members.seconRoleId != roleId) {              
								  return true;
								} 
							  });  
					  }
					 }); 
			
		} else {
			console.log(self.selectedGroupMembers); 
			
			self.selectedGroupMembers = self.selectedGroupMembers.filter((members)=>
                  {             
				   if (members.role.id != roleId && members.seconRoleId != roleId ) {              
					return true;
                  } 
                  });
		   console.log(self.selectedGroupMembers); 	
		  }
		
	
		  
				 self.userListChatwith.sort(function (a, b) {              
				  if (a.displayname.toLowerCase() < b.displayname.toLowerCase()) return -1;
				  if (a.displayname.toLowerCase() > b.displayname.toLowerCase()) return 1;
				  return 0;
				}); 
				 self.selectedGroupMembers = self.selectedGroupMembers.slice();      
	   
	  }
	removeRoleUsersFromExistingMember(roleId){
		if(this.selectedGroupMembers && this.selectedGroupMembers.length > 0){
		console.log("deleted role isssssss===>"+roleId);
		this.selectedGroupMembers.forEach((value)=>{
			console.log("selectedGroupMembers value.role.id====>"+value.role.id);
			console.log("selectedGroupMembers value ==========>",value);
          if(value.role == roleId){
				console.log("remove user==>"+value.displayName);
				this.removeMember(value.id);
			}else if(value.dualRoles && value.dualRoles.length>1){
				value.dualRoles.forEach(element => {
					if(!element.isPrimary && element.tenantUsersRoleId ==roleId){
						console.log("dualRoles element.tenantUsersRoleId ===>"+element.tenantUsersRoleId);                
						var removeMember = true;
						this.checkedRoleIds.forEach((rolevalue,key) => {
							console.log("deleted role isssssss===>"+roleId);
							console.log("selectedGroupMembers value.role.id====>"+value.role.id);
							console.log("dualRoles element.tenantUsersRoleId ===>"+element.tenantUsersRoleId);  
							console.log("rolevalue==>"+rolevalue.id);
							console.log(rolevalue.id+"=="+element.tenantUsersRoleId);
							if(removeMember && rolevalue.id==value.role.id){
								removeMember = false;
							}
						}); 
						if(removeMember){
							console.log("dual role remove user==>"+value.displayName);
							this.removeMember(value.id);
						}
					}
				});
			}
		})
		}
	}
	getNursingAgencyTags() {
		const userData: any = this._structureService.getUserdata();
		const tagGetData = '?userId=' + userData.userId + '&tenantId=' + userData.tenantId;
		const tagTypes = [ '2' ]; // Message Tag =1, User Tag =2 , Document Tag =3
		this._structureService.getNursingAgencyTagsByGroup(tagGetData, tagTypes).then((data: any) => {
			this.nursingAgencyTags = data;
		});
	}
	emitEventToSelectSites(status) {
		this.eventsSubject.next(status);
	}
	reloadEditData(siteId) {
		this.preventMultipleCall = true;
		let setRoles;
		console.log(this.selectSiteId, siteId, 'reloaddddddddddddd');
		this.selectSiteId = siteId;
		this.selectedGroupMembers = [];
							var members;
							if (this.selectedGroupDetails && this.selectedGroupDetails.memberIds != undefined) {
								members = this.selectedGroupDetails.memberIds;
							} else {
								members = '';
							}
							this._inboxService.getAllMessageGroups(
								this.userData.tenantId,
								this.userData.userId,
								true,
								this.pdgData.patientId.toString(),
								members,
								"","",[],false,"","",
								siteId
							).then((editgrpdata: any) => {
								if(editgrpdata && editgrpdata.editedData && editgrpdata.editedData.memberDetails) {
										this.noMemberError = false;
										this.disableButton = false;
										editgrpdata.editedData.memberDetails.forEach((element) => {
											this.newMember = {
												id: '',
												displayName: '',
												role: {},
												dualRoles: {},
												tenantId: null,
												status:'',
												tenantName: '',
												otherTenantStaff: element.tenantId && this.crossTenantId != element.tenantId ? true : false,
												naTagNames: '',​​​
												naTags: ''
											};
											this.newMember.id = element.id;
											this.newMember.displayName = element.name; //element.displayName;
											this.newMember.role = {id:element.tenantRoleId,displayName:element.roles};
											this.newMember.dualRoles = element.dualRoles;
											this.newMember.tenantId = element.tenantId;
											this.newMember.status = element.status;
											this.newMember.tenantName = element.tenantName;
											this.newMember.naTagNames = element.naTagNames;
											this.newMember.naTags = element.naTags;
											this.newMember.citusRoleId = element.roleId;
											this.newMember.seconRoleId = element.msgRoleid;
											this.selectedGroupMembers.push(this.newMember);
							});	
							this.search(this.optionShow);
					}
					setRoles = this.checkedRoleIds.filter(elem => editgrpdata.editedData.selectedRoles.includes((elem.id).toString()))
					this.checkedRoleIds = setRoles;
							});
	}
	executeGroupData(searchKeyword,data) {
		if (searchKeyword) {
			this.prevText = searchKeyword;
		}
		this.loadingGroups = false;
		if (!data || (data && data.length == 0)) {
			this.hideLoadMore = true;
		} else {
			if (data.length == 25) {
				this.hideLoadMore = false;
			} else {
				this.hideLoadMore = true;
			}
			if(!this.loadMore_flag){
				this.allMessageGroups = data;
			} else {
				this.allMessageGroups = [ ...this.allMessageGroups, ...data ];
				this.loadMore_flag = false;
			}

			this._structureService.messageGroupsWithPrivilege = this.allMessageGroups;
		}
		let grpdata = this.allMessageGroups;
		this.messageGroups = grpdata;
		this.newMessageGroups = grpdata;
		let allUserDetailsInMsgGroup: any = grpdata;
		this.newallUserDetailsInMsgGroup = this.allUserDetailsInMsgGroup;
		let messageGroup: any;
		messageGroup = grpdata;
		this.memberDataGroupWise = messageGroup;
		this.memberDataGroupWise.sort(function (a, b) {
		if (a.name < b.name) return -1;
			if (a.name > b.name) return 1;
			return 0;
		});
		this.createdPatientIdIndex = this.createdPatientIdIndex ? this.createdPatientIdIndex : 0;
		if (messageGroup.length) {
			this.groupList = this.messageGroups;
		} else {
			this.getAllMessageGroupsLoading = false;
		}
		this.MessageGroupWithLoader.groups = false;
	}
	executeSelectedPdgData(editgrpdata: MessageGroup) {
		this.disableButton = false;
		if(this.optionShow === 'staffs' || this.optionShow === 'partner'){
			if(editgrpdata.siteId) {
				this.editedSite = editgrpdata.siteId.split(',').map(Number);
			}
			this.userListChatwith = [];
			this.setChatWithTenantFilterSelect();
		  	this.loadMoreUsers(this.optionShow, this.srch.nativeElement.value.trim());
		}
		this.setPDGName(editgrpdata);
		this.setGroupList(editgrpdata);
	}
	executeGroupDataById(messageGroupId: string,resp: MessageGroup,choose: boolean): void {
		this.membersLoaded = true;
		let responseData = resp.memberDetails;
		if(responseData && responseData.length){
		responseData.sort((a, b) => {
			if (a.displayName.toLowerCase() < b.displayName.toLowerCase()) return -1;
			if (a.displayName.toLowerCase() > b.displayName.toLowerCase()) return 1;
			return 0;
		});
		}
		if(!choose) {

			const newData = responseData.filter((responseItem) =>
  				!this.selectedGroupMembers.some((selectedItem) => selectedItem.id === responseItem.id));
			const groupRoles = resp.selectedRoles || [];
			const selectedRoleIds = [...groupRoles.map(x=> (+x.roleId)), ...this.checkedRoleIds.map(x=> +(x.id))];
			let memberDetails = newData.filter(obj => {
			  const roles = JSON.parse(obj.roleIds) as number[];
			  return !roles.some(role => selectedRoleIds.includes(+role));
			});
			if(newData.length !== memberDetails.length) {
			  this._structureService.notifyMessage({
				messge: this._ToolTipService.getTranslateData('WARNING.MEMBER_WITH_SAME_ROLE_EXISTS'),
				type: CONSTANTS.notificationTypes.warning
			  });
			}
			this.selectedGroupMembers.push(...memberDetails);
		}
		
		  this.isGrouploadingTime = false;
		  if ((responseData && responseData.length) || (resp.selectedRoles && resp.selectedRoles.length)) {
			
			if(this.clickOnAddMemberBtn) {
				resp.selectedRoles = resp.selectedRoles.map(({ tenant_id, roleId, ...roleValue }) => {
					const transformedItem = {
						tenantId: tenant_id,
						id: roleId,
						...roleValue
					};
					if (this.checkedRoleIds.findIndex(existingItem => existingItem.id === roleId) === -1) {
						this.checkedRoleIds.push(transformedItem);
						this.removeRoleStaffs(transformedItem);
					}
					return transformedItem;
				});
				
				this.clickOnAddMemberBtn = false;
			}
			if (this.selectedGroupMembers) {
				this.noMemberError = false;
				this.disableButton = false;
			}
			if (typeof messageGroupId === 'string') {
				let selectedMessageGroupIndex = this.memberDataGroupWise.findIndex(group => (group.groupId ? group.groupId : group.patientId) === messageGroupId);
				this.memberDataGroupWise[selectedMessageGroupIndex].userList = responseData;
				this.memberDataGroupWise[selectedMessageGroupIndex].selectedRoles = resp.selectedRoles;
			}
		  }
	}
	goToList() {
		this.location.back();
	}
	private setPDGName(pdgData) { 
		this.pdgName = pdgData.name;
		if (this._structureService.isMultiAdmissionsEnabled) {
			this.pdgName += ` [${this._ToolTipService.getTranslateData('ADMISSION.LABELS.ADMISSION')}: ${ pdgData.admissionName || '' }]`;
		}
	}

	setSelectedPDGs(pdgData: any) {
		this.dummyCheckedGroupMembers = pdgData && pdgData.map(item => ({ id: +item.id, ...(this._structureService.isMultiAdmissionsEnabled && { admissionId: item.admissionId }) }));
		this.resetPdgMembers = false;
	}
}
