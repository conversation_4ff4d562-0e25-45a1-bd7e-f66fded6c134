@import '../../../assets/css/message';

.associate-search {
    position: relative;
}
input#associate-search-input{
    width: 70%;
}
input#associate-search-input.active {
    border: 1px solid #1790fe;
    /* border-bottom: none; */
    border-radius: 4px 4px 0 0px;
}
  ul.associate-ul {
    list-style-type: none;
    padding: 0;
    display: none;
    margin: 0;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #0190fe;
    width: 66.7%;
    border-radius: 0 0px 4px 4px;
    border-top: none;
    position: absolute;
    background: #fff;
    z-index: 99;
}

ul li.associate-li {
    /* border: 1px solid #ddd; */
    margin-top: -1px; /* Prevent double borders */
    padding: 0.42rem 1.14rem;
    text-decoration: none;
    position: relative;
    cursor: pointer;
}
ul li.associate-li:hover {
    background: #d2d9e5 !important;
    color: #222034 !important;
}
ul li.associate-li.selected {
    background: #d2d9e5 !important;
    color: #222034 !important;
}
ul li.associate-li.selected {
    background: #d2d9e5 !important;
    color: #222034 !important;
}
.asscoiate-actions{
    position: absolute;
    /*right: -10px;*/
    left: 71%;
    top: 65px;
    width: 31%;
}
.associate-search {
    position: relative;
}
input#associate-search-input{
    width: 70%;
}
input#associate-search-input.active {
    border: 1px solid #1790fe;
    /* border-bottom: none; */
    border-radius: 4px 4px 0 0px;
}
.associate-close {
    display: none;
    cursor: pointer;
    position: absolute;
    top: 50%;
    right: 21%;
    padding: 4px 16px;
    transform: translate(0%, -50%);
    font-size: 18px;
}
.associate-close:hover {background: #bbb;}
.associate-create{
    z-index: 1;
}