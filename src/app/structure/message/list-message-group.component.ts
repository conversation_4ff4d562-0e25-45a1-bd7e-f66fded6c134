import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>ement<PERSON>ef, Renderer } from '@angular/core';
import { Router, ActivatedRoute, Params } from '@angular/router';
import { RegistrationService } from '../registration/registration.service';
import { StructureService } from '../structure.service';
import { ToolTipService } from '../tool-tip.service';
import { filterMessageGroupPipe } from './messagegroup-search.pipes';
import { SharedService } from './../shared/sharedServices';
import { DatePipe } from '@angular/common';
import { DateTimePipe } from '../forms/formp.pipe';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { Subject } from 'rxjs';
import { forEach } from '@angular/router/src/utils/collection';
import { filter } from 'rxjs/operator/filter';
import { isBlank } from 'app/utils/utils';
import { GroupsListingRequest } from '../../models/message-center/messageCenter';
import { MessageService } from 'app/services/message-center/message.service';
import { GroupType } from 'app/constants/constants';

declare var $: any;
declare var jQuery: any;
declare var autosize: any;
declare var Ladda: any;
declare var Chartist: any;
declare var swal: any;
declare var moment: any;
declare let NProgress: any;

@Component({
  selector: 'app-list-message-group',
  templateUrl: './list-message-group.component.html',
  styleUrls: ['./logs.css'],
})
export class ListMessageGroupComponent implements OnInit {
  userConfig;
  contentLimit = 25;
  groupList = [];
  dTable;
  datam;
  activeGroup = null;
  dataLoadingMsg = true;
  searchResetFlag = 0;
  totalCountDataTable;

  userDetails:any;
  userData:any = {};
  crossTenantChangeSubscriber: any;
  selectedExport=false;
  selectSiteId;
  public daterange: any = {};
  public options: any = {};
  hideSiteSelection = false;
  enableMultisite:boolean;
  eventsSubject: Subject<void> = new Subject<void>();
  public selectedSearchFields = [];
  public filterEnabledFields = [];
  public filteredFields = [];
  public selectedFields = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private _structureService: StructureService,
    private _formBuild: FormBuilder,
    private datePipe: DatePipe,
    private dateTimePipe: DateTimePipe,
    elementRef: ElementRef,
    public _SharedService: SharedService,
    private messageService: MessageService,
    renderer: Renderer
  ) {
    this.userDetails = this._structureService.userDetails;
    this.userData = JSON.parse(this.userDetails);
    this.selectedFields = sessionStorage.getItem('selectedMemberField') ? JSON.parse(sessionStorage.getItem('selectedMemberField')) : [];
    this.filterEnabledFields = [{
        fieldId: 1,
        enableAutoSelect: this.selectedFields.indexOf(1) !== -1 ? true : false,
        headerName: 'Member Name',
    }, {
        fieldId: 2,
        enableAutoSelect: this.selectedFields.indexOf(2) !== -1 ? true : false,
        headerName: 'Member Role Name',
    },];
    this.filterEnabledFields.map(field => { if (field.enableAutoSelect) this.selectedSearchFields.push(field) });

    // defaults; below is example of intention except imagine left side as @Inputs are overridden i.e. in [] brackets and right side is being provided by wrapping parent class
    this.filteredFields = this.filterEnabledFields;
    renderer.listen(elementRef.nativeElement, 'click', (event, target) => {
        if(event.target.id == 'more_recipients') {
            $("body .openmore_recipients").css("display","block");
            $("body .more-recipients").css("display","none");
            $("body .closemore_recipients").css("display","none");
    
            var attributes =$(event.target).attr('data-moreId');
            $("body #"+attributes).css("display","block");
            $('table tr td').find('.more-recipients#'+attributes).attr('style','display:block')
            $("body .openmore_recipients"+attributes).css("display","none");
            $("body .closemore_recipients"+attributes).css("display","block");
          }else if(event.target.id == "closemore_recipients"){
            var attributes =$(event.target).attr('data-moreId');
            $("body #"+attributes).css("display","none");
            $('table tr td').find('.more-recipients#'+attributes).attr('style','display:none');
            $("body .openmore_recipients"+attributes).css("display","block");
            $("body .closemore_recipients"+attributes).css("display","none");
          } 
      if (event.target.id == 'editgrp') {
        // Condition for keeping site filter selected when returning from edit page
        this._SharedService.goToInnerPage = true;
        this._SharedService.innerPageFilter = true;
        this._structureService.setCookie('message-grp-id', this.activeGroup.groupId, 1);
        this.router.navigate(['/message/edit-message-group']);
      } else if (event.target.id == 'deletegrp') {
        this.deleteGroup(this.activeGroup.groupId);
      }
      // Handle click on "Select all" control       
      if (event.target.id == 'mg-select-all') {

        var rows = this.dTable.api().rows({ 'search': 'applied' }).nodes();
        $('input[type="checkbox"]', rows).not(":disabled").prop('checked', event.target.checked);
      }
      if (event.target.className.split(' ').indexOf('mg-select-cls') != -1) {
        let count = 0;
        $('.mg-select-cls').each(function () {
          if ($(this).prop('checked') == true) {
            count = 1;
          }
        });
        if (count > 0) {
          document.getElementById("archivemg").setAttribute("disabled", "false");
        } else {
          document.getElementById("archivemg").setAttribute("disabled", "disabled");
        }
      }
      //Handle Archive all
      if (event.target.id == 'archivemg') {

        swal({
          title: "Are you sure?",
          text: "You will not be able to recover this message group(s)",
          type: "warning",
          showCancelButton: true,
          cancelButtonClass: "btn-default",
          confirmButtonClass: "btn-warning",
          confirmButtonText: "Ok",
          closeOnConfirm: true
        }, () => {

          var allIds = [];
          var unreadCountList = false;

          $('input[name="mgid[]"]').each(function () {
            if (this.checked) {
              allIds.push(parseInt($(this).val()));
              unreadCountList = true;
            }
          });
          if (!unreadCountList) {
            this._structureService.notifyMessage({
              messge: 'Select at least one message group(s)',
              delay: 1000,
              type: 'warning'
            });
          } else {

            this.archiveMGItems(allIds);

          }
        });
      }
      let selectedCheckbox;
      $('input[name="member"]:checked').each(function () {
          selectedCheckbox = $.map($('input[name="member"]:checked'), function (c) { return Number(c.value); });
      });
      this.checkboxSelection(selectedCheckbox);

    });
    this.crossTenantChangeSubscriber = this._SharedService.crossTenantChange.subscribe(
      (onInboxData) => {
        if (this.router.url.indexOf('/message/message') > -1) {
          this.ngOnInit();
        }
      }
    );

  }

  ngOnInit() {
    this.userConfig = this._structureService.getUserdata().config; 
   // this.getGroups();
    this.enableMultisite = parseInt(this.userData.config.enable_multisite) == 1;
    this.hideSiteSelection = ((this.enableMultisite &&  this.userData.mySites.length == 1) || (!this.enableMultisite))? false : true;
  }
  ngOnDestroy() {
    if (this.crossTenantChangeSubscriber) {
      this.crossTenantChangeSubscriber.unsubscribe();
    }
    if(!this._SharedService.goToInnerPage) {
      sessionStorage.removeItem('selectedMemberField');
    }
  }
  loadRecords(val) {
    this.getGroups();
  }
  getSiteIds(data:any){
    this.selectSiteId = data['siteId'].toString();
    this.getGroups();
  }
    hideDropdown(hideItem : any){
      this.hideSiteSelection = hideItem.hideItem;
  }
  
  archiveMGItems(allIds) {
    this._structureService.deleteMessageGroups(allIds).subscribe(
      data => {
        let msgId: any = data;
        let count  = msgId.data.deleteMessageGroups.id.length;
        if(this.totalCountDataTable)
        this.totalCountDataTable = this.totalCountDataTable - count;
        let newMessageGroupData = {
          "status": "delete",
          "id": msgId.data.deleteMessageGroups.id
        }
        this._SharedService.newMessageGroup.emit(newMessageGroupData);
        this.getGroups(allIds);

        var activityLogMessage = this.userData.displayName + " deleted Message Group with id " + msgId.data.deleteMessageGroups.id;
        var activityData = {
          activityName: "Delete Message Group",
          activityType: "manage group messaging",
          activityDescription: activityLogMessage
        };
        this._structureService.trackActivity(activityData);

        var notify = $.notify('Success! Message Group deleted');
        setTimeout(function () {
          notify.update({ 'type': 'success', 'message': '<strong>Success! Message Group(s) deleted</strong>' });
        }, 1000);
        document.getElementById("archivemg").setAttribute("disabled", "disabled");
      });
  }

  getGroups(allIds = '') {
    /* this._structureService.getMessageGroups('',this.contentLimit).then(( data ) => {
       if(data['getSessionTenant']){
         this.groupList = data['getSessionTenant'].messageGroups;
         this.populateDt();
         console.log(this.groupList);
       }
       else{
         this._structureService.deleteCookie('authenticationToken');
         this.router.navigate(['/login']);
       }
     });*/
    this.populateDt(allIds);
  }
  deleteGroup(id) {


    swal({
      title: "Are you sure?",
      text: "You will not be able to recover this message group",
      type: "warning",
      showCancelButton: true,
      cancelButtonClass: "btn-default",
      confirmButtonClass: "btn-warning",
      confirmButtonText: "Ok",
      closeOnConfirm: true
    }, () => {
      this._structureService.deleteMessageGroup(id).subscribe(
        data => {
          let msgId: any = data;
          if(this.totalCountDataTable)
              {
                this.totalCountDataTable = this.totalCountDataTable - 1;
              }
          let newMessageGroupData = {
            "status": "delete",
            "id": msgId.data.deleteMessageGroup.id
          }
          this._SharedService.newMessageGroup.emit(newMessageGroupData);
          this.getGroups();

          var activityLogMessage = this.userData.displayName + " deleted Message Group with id " + msgId.data.deleteMessageGroup.id;
          var activityData = {
            activityName: "Delete Message Group",
            activityType: "manage group messaging",
            activityDescription: activityLogMessage
          };
          this._structureService.trackActivity(activityData);

          var notify = $.notify('Success! Message Group deleted');
          setTimeout(function () {
            notify.update({ 'type': 'success', 'message': '<strong>Success! Message Group deleted</strong>' });
          }, 1000);
          document.getElementById("archivemg").setAttribute("disabled", "disabled");
        });
    });

  }
  selectedDate(value: any, datepicker?: any) {
    // this is the date  selected
    // console.log("selectedDate",value);
    // return false;
  this.selectedExport = true;
    // any object can be passed to the selected event and it will be passed back here
    datepicker.start = value.start;
    datepicker.end = value.end;
 
    // use passed valuable to update state
    this.daterange.start = value.start;
    this.daterange.end = value.end;
    this.daterange.label = value.label;
    var table = $('#dtMsgGroups').DataTable();
    if(this.selectedExport == true)
    table.button('.buttons-excel').trigger();
  }
   toTimestamp(strDate){
    return moment(strDate).format('YYYY-MM-DD HH:mm:ss');
   }
  populateDt(allIds=''){
   
    var self = this;
    
    let datas: any;
    if (this.dTable) {
      this.dTable.fnDestroy();
    } else {
      if (this._structureService.previousUrlNow.indexOf("/message/edit-message-group") == -1) {
        this._structureService.resetDataTable();
      }
    }
    var isTrue = false;
    if (this.groupList.length > 99) {
      isTrue = true;
    }
    $(()=>{

    this.dTable = $('#dtMsgGroups').dataTable({
      autoWidth: false,
      "order": [[4, "desc"]],
      responsive: true,
      bprocessing: true,
      bServerSide: true,
      bpagination: true,
      bsorting: true,
      retrieve: true,
      bsearching: true,
      stateSave: true,
      search: {
        
        // Start with a cleared search filter (despite stateSave above) (prior to the addition of filter dropdown, the search was clearing upon navigation, not sure why the stateSave option wasn't kicking in, but now that it is we want to start with clean slate again)
        search: '',
      },
      bInfo: true,
      lengthMenu: [[25, 50,], [25, 50]],
      data: this.groupList,
      fnDrawCallback: function (oSettings) {
        if (oSettings._iRecordsTotal == 0 || oSettings._iRecordsTotal < oSettings._iDisplayLength || oSettings.aoData.length == 0) {
          $('.dataTables_paginate').hide();
        }
        else {
          $('.dataTables_paginate').show();
      }
      if(oSettings.aoData.length ==0)
      {
        $('.dataTables_info').hide();
      }
    },
    fnRowCallback: (nRow, aData, iDisplayIndex, iDisplayIndexFull) => {
      $(nRow).on('click', () => {
        this.activeGroup = aData;
      });
    },
    dom:
    "<'row'<'col-sm-5'l><'col-sm-3'f><'col-sm-2 searchButton'>>" +
    "<'row'<'col-sm-12'tr>>" +
    "<'row'<'col-sm-5'i><'col-sm-7'p>>",
      buttons: [
      {
        extend: 'excel',
        text: 'All Pages',
        title: 'Message Groups',
        exportOptions: {
        columns:[0,1,2,3]
        },
        action: function ( e, dt, node, config ) {
        var selfButton = this;
        var oldStart = dt.settings()[0]._iDisplayStart;
        dt.one('preXhr', function (e, s, data) {
          data.start = 0;
          data.length = 2147483647;
          dt.one('preDraw', function (e, settings) {
            $.fn.dataTable.ext.buttons.excelHtml5.action.call(selfButton, e, dt, node, config);
            dt.one('preXhr', function (e, s, data) {
              settings._iDisplayStart = oldStart;
              data.start = oldStart;
            });
            setTimeout(dt.ajax.reload, 0);
            return false;
          });
        });
        dt.ajax.reload();
        }
    }],
    initComplete: function(){
    
      // datatable builds the following (pseudo emmet code): label > input[type=search], the filter as found in worklists is label{Search} + space + label{filter dropdown} + label > input[type=search], so we have to rearrange and add space as needed
  
      const $searchInput = $('.dataTables_filter label input');
      $searchInput.attr('style', 'padding-top: .33rem;padding-bottom: .50rem;margin-left: -5px;border-bottom-left-radius: 0px;border-top-left-radius: 0px;border-color:#d8d8d8;');
      $searchInput.attr('placeholder','Search');
      $searchInput.attr('id','Searchmsggp');
      $searchInput.unbind();
      $searchInput.wrap('<label id="searchInput" class="custom-search dtMsgGroups__filter"></label>');
      let temp = `<button type="button" class="btn btn-default-outline btn-sm dropdown-toggle"
      style="border-top-right-radius:0px;border-bottom-right-radius:0px;"
      data-toggle="dropdown"><span class="fa fa-search"></span> <span
      class="caret"></span>
      </button><ul class="dropdown-menu filter-worklist">`;
      self.filteredFields.forEach((field) => {
        temp =
          `${temp} <li><span id="checkboxSelection"><label><input class="form-check-input" type="checkbox" id="dtMsgGroupsFilter__checkbox${field.fieldId}" 
          value="${field.fieldId}" name="member" ${(field.enableAutoSelect ? 'checked' : '')}>&nbsp ${field.headerName}</label></span></li>`;
      });
      temp = temp + '</ul>';
      $('#searchInput').prepend(temp);
      $searchInput.on('keydown', function(e) {
        if (e.which == 13) {
          var value = $searchInput.val();
          if(value)
          {
          value = value.replace('”','"');
          value = value.replace("‘","'");
          value = value.replace("’","'");
          self.searchResetFlag = 0;
          self.dTable.fnFilter(value);
        }
        else
        {
            self.dTable.fnFilter("");
          }
        }
      });
      $searchInput.on('keypress', function(e) {
        $(".searchBMg").prop('disabled', false);
      });
      $searchInput.on('keyup', function(e) {
        const value = $searchInput.val();
        if(!value)
        $(".searchBMg").prop('disabled', true);
      });
      $("div.searchButton")
      .html('<button disabled="true" class="btn btn-sm btn-info searchBMg" title="Search" type="submit" id="searchmsg">Search</button>'+
      '<button style="margin-left:10px;" class="btn btn-sm btn-default resetBMg" title="Reset" type="submit" id="reset">Reset</button>');
      const value =  $searchInput.val();
      if(value)
      {
        $(".searchBMg").prop('disabled', false);
      }
      $searchInput.on('paste', function(event) {
        var element = this;
        var text ;
        setTimeout(function () {
          text = $(element).val();
          if(text)
          {
            $(".searchBMg").prop('disabled', false);
          }
      }, 100);
      });
      $(".buttons-collection").click(function(event) {
        setTimeout(function () {
          if($(".buttons-collection").attr("aria-expanded") === "true" && $(".dt-button-collection").find("button").length == 0){
          $(".dt-button-collection").remove();
          $(".dt-button-background").remove();
          $(".buttons-collection").attr("aria-expanded","false");
          }
        },500);
      });
   },
    ajax: function (dat, callback, settings) {
      let orderData;
      let  searchText;
      let orderby;
      let limit;
      let startDate = '';
      let endDate = '';
      let label = '';
      let filterType;
      
      // Maintain user's choice of sort order, "asc" or "desc"
      orderby =dat.order[0].dir ? dat.order[0].dir : '';
      
      // Maintain user's choice of which data to sort by, *.column below returns the column index which the DataTable is being sorted by
      const i = dat.order[0].column ? dat.order[0].column : '';
      if(i != 0)
      {
       
       // the code name for the above column's header, e.g. "Public Group" is "isPublic"
       orderData =dat.columns[i].data ? dat.columns[i].data : '';
      }
      else
      {
       orderData='';
      }
      
      // @deprecated dat.search expects there to be only one input, so now it pulls the first checkbox in the filter
      // searchText =dat.search.value ? dat.search.value : '';
      searchText = dat.search.value || '' ;
      
      if (!isBlank(self.selectedFields)) {
         filterType = self.selectedFields.length > 1 ? 0 : self.selectedFields[0];
      }
      
      if(searchText &&  self.searchResetFlag == 0){
        dat.start = 0;
        settings.oAjaxData.search.value = searchText;
        settings.oAjaxData.start = 0;
        settings._iDisplayStart = 0;
        self.searchResetFlag = 1;
      }
      if(self.selectedExport == true){
        startDate = self.toTimestamp(self.daterange.start);
        endDate = self.toTimestamp(self.daterange.end);
        label = self.daterange.label;
        self.selectedExport = false;
      }
      if(settings.oAjaxData.start !=0 && self.datam && self.datam.aaData && self.datam.aaData.length == 1 && settings._iDisplayStart !=0  && searchText == '') 
      { 
        settings.oAjaxData.start= settings.oAjaxData.start-settings.oAjaxData.length;
        settings._iDisplayStart= settings.oAjaxData.start;
      }    
      // 2 <= 5
      if(allIds && allIds.length > 0 && settings.oAjaxData.start !=0 && self.datam && self.datam.aaData  && allIds.length >= self.datam.aaData.length && settings._iDisplayStart !=0  && searchText == '')
      {
        settings.oAjaxData.start= settings.oAjaxData.start-settings.oAjaxData.length;
        settings._iDisplayStart= settings.oAjaxData.start;
      }
      const page = dat.start/dat.length;
      let parameter: GroupsListingRequest = {
        data: {
          pagination: {
            page: page > 0 ? page + 1 : 1,
            limit: dat.length
          },
          sort: {
            order: orderby,
            field: orderData
          },
          filter: {
            showMemberLessGroup: true
          }
        }
      };
      if(!isBlank(self.selectSiteId) && +self.selectSiteId !== 0) {
        parameter['siteIds'] = [self.selectSiteId];
      }
      if (searchText !== '') {
        parameter['data'].filter.search = searchText;
        if (!isBlank(self.selectedFields)) {
          parameter['data'].filter.searchType = filterType;
        }
      }
      NProgress.start();
      self.messageService.fetchGroupData(parameter, GroupType.MSGGROUP).subscribe((messageGroups) => {
        datas = {};
        self.datam = {};
        if (dat.start == 0) {
          this.totalCt = Number(messageGroups.data.totalMessageGroupsCount);
          self.totalCountDataTable = this.totalCt;
        } else {
          if(self.totalCountDataTable) {
            self._structureService.setCookie('totalCountMessage', this.totalCt, 1);
            this.totalCt = self.totalCountDataTable;
          } else {
            this.totalCt = self._structureService.getCookie('totalCountMessage');
            self.totalCountDataTable = this.totalCt;
          }
        }
        this.groupList = messageGroups.data.messageGroups ? messageGroups.data.messageGroups : [];
        let draw;
        let total;
        if (this.groupList && this.groupList.length === 0 && searchText === '') {
          draw =  0;
          total = 0;
        } else {
          draw = dat.draw;
          total = this.totalCt;
        }
        self.datam = {
          draw: draw,
          recordsTotal: total,
          recordsFiltered: total,
          aaData: this.groupList
        };
        NProgress.done();
        $('#mg-select-all').prop("checked", false);
        callback(self.datam)
        });
      },
      columns: [
        { title: '<input type="checkbox" name="select_all" value="1" style="float:left" class="mg-select-all-indicator mg-select-cls" id="mg-select-all" title="Select all for Delete">' },
        { title: "Message Group Name", data: 'groupName' },
        { title: "Site", data: 'site' },
        { title: "Public Group", data: 'isPublic' },
        { title: "Created On", data: 'createdAt' },
        { title: "Actions" }
      ],
      columnDefs: [
        {
          targets: 0,
          searchable: false,
          orderable: false,
          width: '3%',
          className: 'dt-body-center',
          'render': function (document, type, row) {
            if (self.userData.config.enable_nursing_agencies_visibility_restrictions == 1 && self.userData.nursing_agencies && self.userData.nursing_agencies != "") {
              if (row.createdUser && row.createdUser.id == self.userData.userId) {
                return '<input type="checkbox" name="mgid[]" class="mg-select-all-indicator mg-select-cls" value="' + row.groupId + '">';
              } else {
                return '<input disabled="disabled" type="checkbox" name="mgid[]" class="mg-select-all-indicator mg-select-cls" value="' + row.groupId + '">';
              }
            } else {
              return '<input type="checkbox" name="mgid[]" class="mg-select-all-indicator mg-select-cls" value="' + row.groupId + '">';
            }
          }
        },
        {
          data: null,
          targets: 1,
          width: "30%",
          render: function (document, type, row) {
            return document.trim();
          }
        },
        {
          data: null,
          targets: 2,
          visible: ((self.userData.config.enable_multisite === "1" && self.userData.mySites.length <= 1) || (self.userData.config.enable_multisite !== "1")) ? false : true,
          render: function (data, type, row) {
            let labelHtml = '-';
              if (!isBlank(row.sites)) {
                let sitesArray = JSON.parse(row.sites);
                sitesArray.forEach((site, index) => {
                  let siteName = site.name;
                  if (index === 0) {
                    labelHtml = siteName;
                  }
                  if (index === 1 && sitesArray.length > 1) {
                    labelHtml += `<a class="pull-right btn btn-sm openmore_recipients openmore_recipients${row.groupId}" href="javascript: void(0);" title="More recipients" data-moreId=${row.groupId} id="more_recipients"  (click)="moreRecipients()"><i id="more_recipients" data-moreId=
                      ${row.groupId} class="fa fa-plus" aria-hidden="true"></i></a>`;
                    labelHtml += `<a class="pull-right btn btn-sm closemore_recipients closemore_recipients${row.groupId}" href="javascript: void(0);" title="Close recipients" style="display:none" data-moreId=${row.groupId} id="closemore_recipients"  (click)="moreRecipients()"><i id="closemore_recipients" data-moreId=
                      ${row.groupId} class="fa fa-close" aria-hidden="true"></i></a>`;
                    labelHtml += `<div class="more-recipients" style="display:none" id="${row.groupId}">`;
                  }
                  if (index >= 1) {
                    labelHtml += `<label>${siteName}</label></br>`;
                  }
                  if (index === sitesArray.length - 1) {
                    labelHtml += `</div>`;
                  }
                });
              }
              return labelHtml;
          },
          width: "20%",
            orderable: true,

        },
        {
          data: null,
          targets: 3,
          width: "10%",
          render: function (document, type, row) {
            if (document === '1') {
              return '<span class="badge badge-success mr-2 mb-2">Yes</span>';
            } else {
              return '<span class="badge badge-danger mr-2 mb-2">No</span>';
            }
          }
        },
        {
          data: null,
          targets: 4,
          //orderData:5,
          orderable: true,
          width: "10%",
          render: (document, type, row) => {
            if (row.createdAt == "2019-02-11T06:38:09.000Z" || row.createdAt == "2019-02-13T04:09:06.000Z" || row.createdAt == "2019-02-20T12:02:05.000Z") {
              return "-";
            } else {
              return this.dateTimePipe.transform(row.createdAt);
            }

          }
        },        
        {
          data: null,
          orderable: false,
          render: function (document, type, row) {
            let actions = '';
            if (self.userData.config.enable_nursing_agencies_visibility_restrictions == 1 && self.userData.nursing_agencies && self.userData.nursing_agencies != "") {
              if (row.createdUser && row.createdUser.id == self.userData.userId) {
                actions += `<a id="editgrp" href="javascript: void(0);" class="cat__core__link--underlined mr-3"><i id="editgrp" class="icmn-pencil"></i> Edit</a>
          <a id="deletegrp" href="javascript: void(0);" class="cat__core__link--underlined mr-3"><i id="deletegrp" class="icmn-cross"></i> Delete</a>
          `;
              } else {
                actions += `<a id="editgrp" href="javascript: void(0);" class="cat__core__link--underlined mr-3"><i id="editgrp" class="icmn-eye"></i> View</a>
                `;
              }
            } else {
              actions += `<a id="editgrp" href="javascript: void(0);" class="cat__core__link--underlined mr-3"><i id="editgrp" class="icmn-pencil"></i> Edit</a>
        <a id="deletegrp" href="javascript: void(0);" class="cat__core__link--underlined mr-3"><i id="deletegrp" class="icmn-cross"></i> Delete</a>
        `;
            }
            return actions;
          },
          width: "15%",
          targets: 5
        }]
    });
    $(document).on('click', '.resetBMg',(event)=> {
      this.dTable.fnFilter("");
      $(".searchBMg").prop('disabled', true);
      // To clear checkboxes while reset search
      self.filteredFields.forEach((field) => {
        let checkbox = document.querySelector<HTMLInputElement>(`#dtMsgGroupsFilter__checkbox${field.fieldId}`);
        if (checkbox) {
          checkbox.checked = false;
        }
      });
    });
    $(document).on('click', '.searchBMg',(event)=> {
    var value = $('#Searchmsggp').val();
    if(value)
    {
    value = value.replace('”','"');
    value = value.replace("‘","'");
    value = value.replace("’","'");
    self.searchResetFlag = 0;
    this.dTable.fnFilter(value);
    }
    else
    {
      this.dTable.fnFilter("");
    }
     });
    /*
    this.dTable.on( 'order.dt search.dt', () => {
      if(this.dTable.column(0, {search:'applied', order:'applied'}).nodes() && this.dTable.column(0, {search:'applied', order:'applied'}).nodes().length) {
        this.dTable.column(0, {search:'applied', order:'applied'}).nodes().each( function (cell, i) {
          cell.innerHTML = i+1;
        });
      }
    }).draw();*/
    this.dataLoadingMsg = false;
  
  });


  }
  checkboxSelection(field) {
      this.selectedFields = field;
      if(!isBlank(this.selectedFields)) {
        sessionStorage.setItem('selectedMemberField', JSON.stringify(this.selectedFields));
      } else {
        sessionStorage.setItem('selectedMemberField', '[]');
      }
  }
}
