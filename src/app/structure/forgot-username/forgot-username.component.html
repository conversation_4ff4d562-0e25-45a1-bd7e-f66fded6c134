<div class="cat__pages__login__block__form forgot-password-form">
  <h4 class="text-uppercase">
    <strong>{{ 'LABELS.FORGOT_USERNAME' | translate }}</strong>
  </h4>
  <br />
  <span class="resetpass-text">
    {{ 'MESSAGES.FORGOT_USERNAME_INFO' | translate }}
  </span>
  <br />
  <form class="new-form forgotpass-form" [formGroup]="form">
    <div class="row form-group align-items-end">
      <div class="col-12 mt-3">
        <label for="userType" class="form-label">{{ 'LABELS.TELL_US_WHO_YOU_ARE' | translate }} *</label>
        <select
          class="form-select select-filter w-100"
          name="userType"
          [(ngModel)]="userType"
          [ngModelOptions]="{ standalone: true }"
          attr.aria-label="{{ 'LABELS.TELL_US_WHO_YOU_ARE' | translate }}"
          (change)="onUserTypeChange()"
        >
          <option [ngValue]="''">{{ 'OPTIONS.SELECT' | translate }}</option>
          <option [ngValue]="'1'">{{ 'LABELS.PATIENT' | translate }}</option>
          <option [ngValue]="'0'">{{ 'LABELS.OTHER' | translate }}</option>
        </select>
      </div>
      <ng-container *ngIf="userType">
        <div class="col-6 mt-3">
          <label for="lastName" class="form-label">{{ 'LABELS.USER_PROFILE_LAST_NAME' | translate }} *</label>
          <input class="form-control" name="lastName" id="lastName" type="text" formControlName="lastName" />
        </div>
        <div class="col-6 mt-3">
          <label for="firstName" class="form-label">{{ 'LABELS.USER_PROFILE_FIRST_NAME' | translate }} *</label>
          <input class="form-control" name="firstName" id="firstName" type="text" formControlName="firstName" />
        </div>
        <ng-container *ngIf="userType === '1'">
          <div class="col-6 mt-3">
            <label for="dob" class="form-label">{{ 'LABELS.DATE_OF_BIRTH' | translate }} *</label>
            <div class="input-group">
              <input
                class="form-control"
                placeholder="mm/dd/yyyy"
                id="dob"
                name="dob"
                formControlName="dob"
                ngbDatepicker
                #d="ngbDatepicker"
                [minDate]="minDate"
                [maxDate]="maxDate"
              />
              <span
                class="input-group-addon"
                (click)="d.toggle(); $event.stopPropagation()"
                (document:click)="closeDatePickerOnOutsideClick($event, d)"
              >
                <span class="fa fa-calendar"></span>
              </span>
            </div>
          </div>
        </ng-container>
        <ng-container *ngIf="userType === '0'">
          <div class="col-6 mt-3">
            <label class="form-label" for="email"
              >{{ 'LABELS.USER_PROFILE_EMAIL' | translate }} <span *ngIf="isRequiredField('email')">*</span></label
            >
            <input class="form-control" name="email" id="email" type="text" formControlName="email" (input)="onChange()" />
          </div>
          <div class="col-6 mt-3">
            <label class="form-label" for="mobile">{{ 'LABELS.MOBILE_NUMBER' | translate }} <span *ngIf="isRequiredField('mobile')">*</span></label>
            <input class="form-control" name="mobile" id="mobile" type="text" formControlName="mobile" (input)="onChange()" />
          </div>
        </ng-container>
        <div class="col-6 mt-3">
          <button type="button" [disabled]="!form.valid" (click)="lookupUserData()" class="btn btn-primary">
            <span *ngIf="lookupLoading"><i class="fa fa-spinner fa-spin"></i> </span>{{ 'LABELS.DATA_LOOKUP' | translate }}
          </button>
        </div>
      </ng-container>
    </div>
    <div *ngIf="sendUsernameSuccessMsg" class="alert alert-success">
      {{ sendUsernameSuccessMsg }}
    </div>
    <div *ngIf="userLookupErrorMsg" class="alert alert-danger">
      {{ userLookupErrorMsg }}
    </div>
    <br />
    <div class="form-actions">
      <div class="accountMessage">
        {{ 'MESSAGES.NOT_WHAT_YOU_WERE_LOOKING_FOR' | translate }}&nbsp;<a
          href="javascript: void(0);"
          class="cat__core__link--blue"
          (click)="goToLogin()"
          >{{ 'LABELS.LOG_IN' | translate }}</a
        >
      </div>
    </div>
  </form>
</div>
