import { HttpParams } from '@angular/common/http';

import { ChangeDetectorRef, ChangeDetectionStrategy, Component, Input, Output, EventEmitter } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbDateParserFormatter } from '@ng-bootstrap/ng-bootstrap';
import { isBlank } from 'app/utils/utils';
import { APIs } from '../../constants/apis';
import { HttpService } from '../../services/http/http.service';
import { CustomDateParserFormatter } from '../../services/custom-date-parser-formatter/custom-date-parser-formatter.service';
import { userDobCalendarMinDate} from 'app/constants/constants';

@Component({
  selector: 'app-forgot-username',
  templateUrl: './forgot-username.component.html',
  styleUrls: ['./forgot-username.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NgbDateParserFormatter,
      useClass: CustomDateParserFormatter
    }
  ]
})
export class ForgotUsernameComponent {
  @Input() enableForgotUsername: boolean;
  @Output() backToLogin = new EventEmitter<boolean>();
  lookupLoading = false;
  sendUsernameLoading = false;
  date = new Date();
  minDate = userDobCalendarMinDate;
  maxDate = { year: this.date.getFullYear(), month: this.date.getMonth() + 1, day: this.date.getDate() };
  sendUsernameSuccessMsg = '';
  userLookupErrorMsg = '';
  userType = '';

  form: FormGroup = this.fb.group({
    lastName: ['', Validators.required],
    firstName: ['', Validators.required],
    dob: '',
    email: '',
    mobile: ''
  });

  constructor(private fb: FormBuilder, private httpService: HttpService, private cd: ChangeDetectorRef) {}

  goToLogin() {
    this.backToLogin.emit(false);
  }

  get formData() {
    const formData = this.form.value;
    let dob = this.form.get('dob').value;
    if (typeof dob === 'object') {
      dob = dob ? `${dob.month}/${dob.day}/${dob.year}` : '';
    }
    formData.dob = dob;
    if (!isBlank(formData.mobile)) {
      formData.mobile = this.mobileNumber;
    }
    return formData;
  }

  get mobileNumber() {
    return this.form.get('mobile').value.match(/\d/g).join('');
  }

  onUserTypeChange() {
    this.form.reset();
    this.userLookupErrorMsg = '';
    ['dob', 'email', 'mobile'].forEach((key) => {
      this.form.get(key).clearValidators();
      this.form.get(key).updateValueAndValidity();
    });
    if (this.userType === '1') {
      this.form.get('dob').setValidators([Validators.required]);
      this.form.get('dob').updateValueAndValidity();
    } else if (this.userType === '0') {
      this.setEmailMobileValidations();
    }
    this.cd.markForCheck();
  }

  setEmailMobileValidations() {
    this.form.get('email').setValidators([Validators.required, Validators.email]);
    this.form.get('email').updateValueAndValidity();
    this.form.get('mobile').setValidators([Validators.required]);
    this.form.get('mobile').updateValueAndValidity();
  }

  onChange() {
    if (!isBlank(this.form.get('email').value)) {
      this.form.get('mobile').clearValidators();
      this.form.get('mobile').updateValueAndValidity();
    } else if (!isBlank(this.form.get('mobile').value)) {
      this.form.get('email').clearValidators();
      this.form.get('email').updateValueAndValidity();
    } else {
      this.setEmailMobileValidations();
    }
    this.cd.markForCheck();
  }

  isRequiredField(field: string) {
    const formField = this.form.get(field);
    if (!formField.validator) {
      return false;
    }

    const validator = formField.validator({} as AbstractControl);
    return validator && validator.required;
  }

  lookupUserData() {
    if (this.form.valid) {
      this.sendUsernameSuccessMsg = '';
      this.userLookupErrorMsg = '';
      this.lookupLoading = true;
      let params = new HttpParams().set('firstName', this.formData.firstName).set('lastName', this.formData.lastName);
      if (this.userType === '1') {
        params = params.set('dob', this.formData.dob);
      } else {
        if (!isBlank(this.formData.email)) {
          params = params.set('email', encodeURIComponent(this.formData.email));
        }
        if (!isBlank(this.formData.mobile)) {
          params = params.set('mobile', this.mobileNumber);
        }
      }
      this.httpService
        .doGet(APIs.userLookupEndpoint, {
          params
        })
        .finally(() => {
          this.lookupLoading = false;
          this.cd.markForCheck();
        })
        .subscribe(
          (res) => {
            this.sendUsernameSuccessMsg = res.message;
            this.cd.markForCheck();
          },
          (error) => {
            if (this.httpService.isValidErrorCode(error.status)) {
              this.userLookupErrorMsg = this.httpService.getErrorMsg(error);
            }
          }
        );
    }
  }

  closeDatePickerOnOutsideClick(event, datePicker) {
    if (event.target.offsetParent == null) datePicker.close();
    else if (event.target.offsetParent.nodeName !== 'NGB-DATEPICKER') datePicker.close();
  }
}
