
<div class="nav-tabs-horizontal">
    <ul class="nav nav-tabs filing-icons" role="tablist">
            <li class="nav-item" (click)="loadDocumentActivity()">
                <a class="nav-link" id="signatureItems" href="javascript: void(0);" data-toggle="tab" data-target="#SignatureRequestsTabSummary" role="tab" aria-expanded="true">
                    <i class="icmn-pen" aria-hidden="true"></i>
                    <span>Document Center Summary</span>
                </a>
            </li>  
            <li class="nav-item"  (click)="loadDocumentActivityTrends()">
                <a class="nav-link" id="signatureTrends" href="javascript: void(0);" data-toggle="tab" data-target="#SignatureRequestsTabTrends" role="tab" aria-expanded="true">
                    <i class="fa fa-industry" aria-hidden="true"></i>
                    <span>Document Center Trends</span>
                </a>
            </li>          
    </ul>
</div>
<div class="tab-content">
    <div class="tab-pane active mt-4" id="SignatureRequestsTabSummary" role="tabcard">
<div class="row" >
    <div class="col-lg-12">
            <div class="" id="left-col">
                <div style="width:100%" class="row cat__core__sortable overflow_hidden">                   
                    <!-- Start of widget document center activity by workflow-->
                    <div class="col-lg-6">
                        <section class="card" order-id="card-1">
                            <div class="card-header">
                                <div class="pull-right cat__core__sortable__control">
                                    <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                    <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                    <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                </div>
                                <div class="pull-right cat__core__sortable__control">
                                    <div class="load-more" daterangepicker [options]="_sharedService.dashboardSummaryChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'DOCUMENT_CENTER_ACTIVITY_BY_WORKFLOW')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                                </div>
                                <h5 class="mb-0 text-black">
                                    <strong>{{activityByWorkFlowData.widgetTitle}}</strong>
                                    <small style="font-weight:bold" class="badge badge-primary">{{activityByWorkFlowData.filterLabel}}</small>
                                </h5>
                            </div>
                            <div class="card-block">
                                <div class="row">
                                        <div class="col-lg-10" style="padding-bottom:5px">   
                                            <strong>{{activityByWorkFlowData.totalCount}} {{activityByWorkFlowData.totalCountLabel}}</strong> 
                                        </div>   
                                </div>
                                <div class="row">
                                    <div id="chart-legend-activity-by-workflow" class="col-lg-4">
                                    </div>                                   
                                    <div class="col-lg-8">
                                        <canvas id="chart-pie-activity-by-workflow"></canvas>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                    <!-- End of widget document center activity by workflow-->
                    <!-- Start of widget document center activity by status-->
                    <div class="col-lg-6">
                        <section class="card" order-id="card-1">
                            <div class="card-header">
                                <div class="pull-right cat__core__sortable__control">
                                    <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                    <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                    <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                </div>
                                <div class="pull-right cat__core__sortable__control">
                                    <div class="load-more" daterangepicker [options]="_sharedService.dashboardSummaryChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'DOCUMENT_CENTER_ACTIVITY_BY_STATUS')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                                </div>
                                <h5 class="mb-0 text-black">
                                    <strong>{{activityByStatusData.widgetTitle}}</strong>
                                    <small style="font-weight:bold" class="badge badge-primary">{{activityByStatusData.filterLabel}}</small>
                                </h5>
                            </div>
                            <div class="card-block">
                                <div class="row">
                                        <div class="col-lg-10" style="padding-bottom:5px">   
                                            <strong>{{activityByStatusData.totalCount}} {{activityByStatusData.totalCountLabel}}</strong> 
                                        </div>   
                                </div>
                                <div class="row">
                                    <div id="chart-legend-activity-by-status" class="col-lg-4">                         
                                    </div>
                                    <div class="col-lg-8">
                                        <canvas id="chart-pie-activity-by-status"></canvas>
                                    </div>                                     
                                </div>
                            </div>
                        </section>
                    </div>
                    <!-- End of widget document center activity by status-->
                    <!-- Start of widget document center activity by staff-->
                    <div class="col-lg-6">
                        <section class="card" order-id="card-1">
                            <div class="card-header">
                                <div class="pull-right cat__core__sortable__control">
                                    <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                    <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                    <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                </div>
                                <div class="pull-right cat__core__sortable__control">
                                    <div class="load-more" daterangepicker [options]="_sharedService.dashboardSummaryChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'DOCUMENT_CENTER_ACTIVITY_BY_STAFF')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                                </div>
                                <h5 class="mb-0 text-black">
                                    <strong>{{activityByStaffData.widgetTitle}}</strong>
                                    <small style="font-weight:bold" class="badge badge-primary">{{activityByStaffData.filterLabel}}</small>
                                </h5>
                            </div>
                            <div class="card-block">
                                <div class="row">
                                        <div class="col-lg-10" style="padding-bottom:5px">   
                                            <strong>{{activityByStaffData.totalCount}} {{activityByStaffData.totalCountLabel}}</strong> 
                                        </div>   
                                </div>
                                <div class="row">
                                   <div id="chart-legend-activity-by-staff" class="col-lg-4">                                   

                                    </div>
                                    <div class="col-lg-8">
                                        <canvas id="chart-pie-activity-by-staff"></canvas>
                                    </div>
                                     
                                </div>
                            </div>
                        </section>
                    </div>
                    <!-- End of widget document center activity by staff-->
                     <!-- Start of widget document center activity by patient-->
                    <div class="col-lg-6">
                        <section class="card" order-id="card-1">
                            <div class="card-header">
                                <div class="pull-right cat__core__sortable__control">
                                    <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                    <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                    <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                </div>
                                <div class="pull-right cat__core__sortable__control">
                                    <div class="load-more" daterangepicker [options]="_sharedService.dashboardSummaryChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'DOCUMENT_CENTER_ENGAGEMENT_BY_PATIENT')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                                </div>
                                <h5 class="mb-0 text-black">
                                    <strong>{{activityByPatientData.widgetTitle}}</strong>
                                    <small style="font-weight:bold" class="badge badge-primary">{{activityByPatientData.filterLabel}}</small>
                                </h5>
                            </div>
                            <div class="card-block">
                                <div class="row">
                                        <div class="col-lg-10" style="padding-bottom:5px">   
                                            <strong>{{activityByPatientData.totalCount}} {{activityByPatientData.totalCountLabel}}</strong> 
                                        </div>   
                                </div>
                                <div class="row">
                                   <div id="chart-legend-activity-by-patient" class="col-lg-4">                                   

                                    </div>
                                    <div class="col-lg-8">
                                        <canvas id="chart-pie-activity-by-patient"></canvas>
                                    </div>
                                     
                                </div>
                            </div>
                        </section>
                    </div>
                    <!-- End of widget document center activity by patient-->
                      <!-- Start of widget document center activity by document type-->
                    <div class="col-lg-6">
                        <section class="card" order-id="card-1">
                            <div class="card-header">
                                <div class="pull-right cat__core__sortable__control">
                                    <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                    <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                    <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                </div>
                                <div class="pull-right cat__core__sortable__control">
                                    <div class="load-more" daterangepicker [options]="_sharedService.dashboardSummaryChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'DOCUMENT_CENTER_ENGAGEMENT_BY_DOCUMENT_TYPE')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                                </div>
                                <h5 class="mb-0 text-black">
                                    <strong>{{activityByDocumentTypeData.widgetTitle}}</strong>
                                    <small style="font-weight:bold" class="badge badge-primary">{{activityByDocumentTypeData.filterLabel}}</small>
                                </h5>
                            </div>
                            <div class="card-block">
                                <div class="row">
                                        <div class="col-lg-10" style="padding-bottom:5px">   
                                            <strong>{{activityByDocumentTypeData.totalCount}} {{activityByDocumentTypeData.totalCountLabel}}</strong> 
                                        </div>   
                                </div>
                                <div class="row">
                                    <div id="chart-legend-activity-by-document-type" class="col-lg-4">                                   

                                    </div>
                                    <div class="col-lg-8">
                                        <canvas id="chart-pie-activity-by-document-type"></canvas>
                                    </div>
                                    
                                </div>
                            </div>
                        </section>
                    </div>
                    <!-- End of widget document center activity by document type-->
                </div>
            </div>
    </div>
    
 </div>
 </div> 
    <div class="tab-pane mt-4" id="SignatureRequestsTabTrends" role="tabcard">
        <div class="row" >
            <div class="col-lg-12">
                <div class="" id="left-col">
                    <div style="width:100%" class="row cat__core__sortable overflow_hidden">
                        <div class="col-lg-8">
                            <section class="card" order-id="card-1">
                                <div class="card-header">
                                    <div class="pull-right cat__core__sortable__control">
                                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                    </div>
                                    <div class="pull-right cat__core__sortable__control">
                                        <div class="load-more" daterangepicker [options]="_sharedService.dashboardTrendChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'DOCUMENT_CENTER_ACTIVITY_BY_WORKFLOW','trend')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                                        </div>
                                    </div>
                                    <h5 class="mb-0 text-black">
                                    <strong>{{activityTrendByWorkFlowData.widgetTitle}}</strong>
                                    <small style="font-weight:bold" class="badge badge-primary">{{ activityTrendByWorkFlowData.filterLabel }}</small>
                                    </h5>
                                </div>
                                <div class="card-block">                 
                                    <div class="row">
                                        <div id="chart-activity-trend-workflow-legend" class="col-lg-4">
                                        </div>
                                        <div class="col-lg-8">
                                        <canvas id="chart-activity-trend-workflow-line" height="200"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-12">
                <div class="" id="left-col">
                    <div style="width:100%" class="row cat__core__sortable overflow_hidden">
                        <div class="col-lg-8">
                            <section class="card" order-id="card-1">
                                <div class="card-header">
                                    <div class="pull-right cat__core__sortable__control">
                                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                    </div>
                                    <div class="pull-right cat__core__sortable__control">
                                        <div class="load-more" daterangepicker [options]="_sharedService.dashboardTrendChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'DOCUMENT_CENTER_ACTIVITY_BY_STATUS','trend')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                                        </div>
                                    </div>
                                    <h5 class="mb-0 text-black">
                                    <strong>{{activityTrendByStatusData.widgetTitle}}</strong>
                                    <small style="font-weight:bold" class="badge badge-primary">{{ activityTrendByStatusData.filterLabel }}</small>
                                    </h5>
                                </div>
                                <div class="card-block">                 
                                    <div class="row">
                                        <div id="chart-activity-trend-status-legend" class="col-lg-4">
                                        </div>
                                        <div class="col-lg-8">
                                        <canvas id="chart-activity-trend-status-line" height="200"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-12">
                <div class="" id="left-col">
                    <div style="width:100%" class="row cat__core__sortable overflow_hidden">
                        <div class="col-lg-8">
                            <section class="card" order-id="card-1">
                                <div class="card-header">
                                    <div class="pull-right cat__core__sortable__control">
                                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                    </div>
                                    <div class="pull-right cat__core__sortable__control">
                                        <div class="load-more" daterangepicker [options]="_sharedService.dashboardTrendChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'DOCUMENT_CENTER_ACTIVITY_BY_STAFF','trend')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                                        </div>
                                    </div>
                                    <h5 class="mb-0 text-black">
                                    <strong>{{activityTrendByStaffData.widgetTitle}}</strong>
                                    <small style="font-weight:bold" class="badge badge-primary">{{ activityTrendByStaffData.filterLabel }}</small>
                                    </h5>
                                </div>
                                <div class="card-block">                 
                                    <div class="row">
                                        <div id="chart-activity-trend-staff-legend" class="col-lg-4">
                                        </div>
                                        <div class="col-lg-8">
                                        <canvas id="chart-activity-trend-staff-line" height="200"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-12">
                <div class="" id="left-col">
                    <div style="width:100%" class="row cat__core__sortable overflow_hidden">
                        <div class="col-lg-8">
                            <section class="card" order-id="card-1">
                                <div class="card-header">
                                    <div class="pull-right cat__core__sortable__control">
                                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                    </div>
                                    <div class="pull-right cat__core__sortable__control">
                                        <div class="load-more" daterangepicker [options]="_sharedService.dashboardTrendChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'DOCUMENT_CENTER_ENGAGEMENT_BY_PATIENT','trend')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                                        </div>
                                    </div>
                                    <h5 class="mb-0 text-black">
                                    <strong>{{activityTrendByPatientData.widgetTitle}}</strong>
                                    <small style="font-weight:bold" class="badge badge-primary">{{ activityTrendByPatientData.filterLabel }}</small>
                                    </h5>
                                </div>
                                <div class="card-block">                 
                                    <div class="row">
                                        <div id="chart-activity-trend-patient-legend" class="col-lg-4">
                                        </div>
                                        <div class="col-lg-8">
                                        <canvas id="chart-activity-trend-patient-line" height="200"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-12">
                <div class="" id="left-col">
                    <div style="width:100%" class="row cat__core__sortable overflow_hidden">
                        <div class="col-lg-8">
                            <section class="card" order-id="card-1">
                                <div class="card-header">
                                    <div class="pull-right cat__core__sortable__control">
                                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                    </div>
                                    <div class="pull-right cat__core__sortable__control">
                                        <div class="load-more" daterangepicker [options]="_sharedService.dashboardTrendChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'DOCUMENT_CENTER_ENGAGEMENT_BY_DOCUMENT_TYPE','trend')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                                        </div>
                                    </div>
                                    <h5 class="mb-0 text-black">
                                    <strong>{{activityTrendByDocumentTypeData.widgetTitle}}</strong>
                                    <small style="font-weight:bold" class="badge badge-primary">{{ activityTrendByDocumentTypeData.filterLabel }}</small>
                                    </h5>
                                </div>
                                <div class="card-block">                 
                                    <div class="row">
                                        <div id="chart-activity-trend-document-type-legend" class="col-lg-4">
                                        </div>
                                        <div class="col-lg-8">
                                        <canvas id="chart-activity-trend-document-type-line" height="200"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </div>
        </div> 
    </div>
</div>

