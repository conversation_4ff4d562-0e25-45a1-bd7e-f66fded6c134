<style>
   /* tbody,th,td,tr{
   border: 1px solid;
   } */
   .form-trends table{
   width: 100%;
   }
   .form-trends th {
   color: #52b3aa;
   height: 35px;
   padding: 5px;
   } 
   .form-trends td {
   color: #555;
   border: 1px solid #acb7bf;
   padding: 5px;
   }
   .row.form-trends {
   margin-top: 25px;
   margin-bottom: 10px;
   }
   .filter-reset-section {
   margin-top: 15px;
   }
   .formtype-tooltip {
   position: absolute;
   width: auto;
   right: 10px;
   }
   .formtype-section-tooltip {
   top: 5px;
   }
   .staff-details-section span.select2.select2-container.select2-container--default {
   width: 98% !important;
   }
   .formtype-section-tooltip {
   top: 5px;
   right: 5px;
   }
   .filter-reset-field .select2 {
   margin-left: 4px;
   margin-top: 20px;
   margin-bottom: 5px;
   }
   /* span.select2.select2-container.select2-container--default {
   margin-left: 3px;
   margin-top: 20px;
   margin-bottom: 7px;
   } */
   #staffs .recipient-actions{
      width:29%;
   }
   .filter-class{
    float: right;width: 100px;
    margin-top: -6px;
}
</style>
<section class="card">
   <div class="card-header">
      <div class="row">
         <div class="col-md-12">
            <div class="col-md-2" style="float: left;">
               <span class="cat__core__title">
                  <strong>Dashboard</strong>
                  <!-- <button (click)="refreshDashboard()" class="pull-right mr-2 mb-2 btn btn-sm btn-outline-default ladda-button">Update<span class="hidden-sm-down"> Dashboard</span></button> -->
               </span>
            </div>
            <!--      <div class="fetch-entries message-load-dd dasboard-top" [hidden]="(viewotherbranch!=1)">
               <span>Show:&nbsp;</span>
               <span>
                   <select id="forms-to-show" aria-controls="message-worklist" class="form-control input-sm"   (change)="changeTeanant($event.target.value,$event)">
                        <option *ngFor="let crosstenant of crosstenants" value="{{crosstenant.id}}"> {{crosstenant.tenantName}} </option>
                   </select>
               </span>
               </div> --> 
            <!-- <div class="col-md-10" style="float: right;">
               <div class=" message-load-dd dasboard-top"  [hidden]="(!viewobranchFilter || viewotherbranch!=1)" >
                  <div class="row">
                     <div class="col col-md-12 text-left domain_name">
                        <div class="dropdown-menu keep-open"  > </div>
                        <div class="col-md-12" style="float: right;">
                           <div class="row">
                              <div class="col-md-12 selectd-branches" >
                                 <div class="selected-branches"><b>Selected Branch(es):</b> {{selectedBranches}}</div>
                                 <div class="filter-div">  <button  class="btn btn-default btn-sm btn-rounded filter-branch" (click)="showBranchList()"> Filter Branch</button></div>
                              </div>
                                <a style= "border: 1px solid  rgba(0,0,0,.15);border-radius: .25rem;padding:10px" href="javascript: void(0);" class="dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                 {{selectElementTenant}}
                                 </a>
                           </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div> -->
   <!--          <div class="col-md-10" style="float: right;">
               <div class="message-load-dd dasboard-top" >
                  <div class="row">
                     <div class="col col-md-12 text-left domain_name">
                        <div class="dropdown-menu keep-open"  > </div>
                        <div class="col-md-12" style="float: right;">
                           <div class="row"> -->
                              <!-- <div class="col-md-12 selectd-branches" >
                                 <div class="filter-class">  <button  class="btn btn-default btn-sm btn-rounded filter-branch" (click)="showSiteList()"> {{ 'LABELS.FILTER_BY_SITES' | translate }}</button></div>
                              </div> -->
                              <!--   <a style= "border: 1px solid  rgba(0,0,0,.15);border-radius: .25rem;padding:10px" href="javascript: void(0);" class="dropdown-toggle" data-toggle="dropdown" aria-expanded="false">
                                 {{selectElementTenant}}
                                 </a> -->
                                 <!--<div class="dashboard-filter-site"  [hidden]="(!multiSiteEnable && (!viewobranchFilter || viewotherbranch!=1)) || (multiSiteEnable && singleSiteLength)">
                                    <div class="col-md-12" style="float: right">
                                    <div class="row">
                                        <div class="site-label">
                                <span>{{ 'LABELS.FILTER_BY_SITES' | translate }}</span>
                                </div>
                                <div class="col-md-8" style="width: 73%">
                                <app-select-sites [events]="eventsSubject.asObservable()" [filterType]=true (siteIds)="getSiteIds($event)"  (siteName)="getSiteName($event)" [crossSite]=true [fromDashboard]=true (hideDropdown)="hideDropdown($event)">
                                </app-select-sites>
                            </div>
                              </div>
                              </div>
                                </div> --> 
                                <div class="filter-sites-wrapper" [hidden]="(!multiSiteEnable && (!viewobranchFilter || viewotherbranch!=1)) || (multiSiteEnable && singleSiteLength)" style="float: right">
                                 <div class="col-md-12" style="float: right">
                                     <div class="filter-site row">
                                         <div class="site-label">
                                         <span>{{ 'LABELS.FILTER_BY_SITES' | translate }}</span>
                                         </div>
                                         <div class="col-md-8" style="width: 73%">
                                             <app-select-sites [events]="eventsSubject.asObservable()" (siteName)="getSiteName($event)" [crossSite]=true [fromDashboard]=true [filterType]=true (hideDropdown)="hideDropdown($event)" (siteIds)="getSiteIds($event)">
                                             </app-select-sites>
                                         </div>
                                     </div>
                                 </div>
                             </div>
                          <!--  </div>
                        </div>
                     </div>
                  </div>
               </div>
            </div> -->
         </div>
      </div>
   </div>
   <div class="card-block  dashboard-component-section">
      <div class="row">
         <div class="col-md-12 from-to-btn-sec" style="margin-bottom: 14px;">
            <div class="nav-tabs-horizontal">
               <ul class="nav nav-tabs filing-icons" role="tablist">
                  <li class="nav-item"(click)="loadHome()">
                     <a class="nav-link  active" id="homefirst" href="javascript: void(0);" data-toggle="tab" data-target="#home1" role="tab" aria-expanded="true">
                     <i class="fa fa-home" aria-hidden="true"></i>
                     <span>Home</span></a>
                  </li>
                  <li class="nav-item" (click)="loadloggedIn()">
                     <a class="nav-link" id="whologgedinid" href="javascript: void(0);" data-toggle="tab" data-target="#whologgedin" role="tab" aria-expanded="true">
                     <i class="icmn-users" aria-hidden="true"></i>
                     <span>Who is logged In</span></a>
                  </li>
                  <li class="nav-item" (click)="loadEnrollments()">
                     <a class="nav-link" id="newitemsid" href="javascript: void(0);" data-toggle="tab" data-target="#newitems" role="tab" aria-expanded="true">
                     <i class="fa fa-bell" aria-hidden="true"></i>
                     <span>Enrollments</span></a>
                  </li>
                  <li class="nav-item" (click)="loadForms()">
                     <a class="nav-link" id="formitemsid" href="javascript: void(0);" data-toggle="tab" data-target="#formitems" role="tab" aria-expanded="true">
                     <i class="fa fa-list-alt" aria-hidden="true"></i>
                     <span>Forms Center</span></a>
                  </li>
                  <li class="nav-item" (click)="loadMessage()">
                     <a class="nav-link" id="chatitemsid" href="javascript: void(0);" data-toggle="tab" data-target="#chatitems" role="tab" aria-expanded="true">
                     <i class="fa fa-comments-o" aria-hidden="true"></i>
                     <span>Message Center</span></a>
                  </li>
                  <li class="nav-item" (click)="loadDocuments()">
                     <a class="nav-link" id="sigantureitemsid" href="javascript: void(0);" data-toggle="tab" data-target="#sigantureitems" role="tab" aria-expanded="true">
                     <i class=" icmn-pen" aria-hidden="true"></i>
                     <span>Documents Center</span></a>
                  </li>
                   <li class="nav-item" [hidden]="(!showIntegration)" (click)="loadIntegration()">
                     <a class="nav-link" id="integrationid" href="javascript: void(0);" data-toggle="tab" data-target="#integrationitems" role="tab" aria-expanded="true">
                     <i class="fa fa-address-card" aria-hidden="true"></i>
                     <span>Integration Center</span></a>
                  </li>                   
                  <!-- <li class="nav-item" (click)="loadFormsTrends()">
                     <a class="nav-link" id="formsTrends" href="javascript: void(0);" data-toggle="tab" data-target="#formsTrendsitems" role="tab" aria-expanded="true">
                         <i class=" icmn-pen" aria-hidden="true"></i>
                         <span>Form Trends</span></a>
                     </li> -->
                  <!-- <li class="nav-item" (click)="loadEnrollTrends()">
                     <a class="nav-link" id="enrollTrends" href="javascript: void(0);" data-toggle="tab" data-target="#enrollTrendsitems" role="tab" aria-expanded="true">
                         <i class=" icmn-pen" aria-hidden="true"></i>
                         <span>Enrollment Trends</span></a>
                     </li> -->
               </ul>
            </div>
         </div>
         <div class="col-md-12">
            <div class="tab-content">
               <div class="tab-pane active" id="home1" role="tabcard">
                  <div class="row">
                     <div class="col-lg-3">
                        <div class="cat__core__widget " (click)="loadloggedInblock()">
                           <div class="cat__core__step cat__core__step--success">
                              <span class="cat__core__step__digit">
                                 <i class="icmn-users">
                                    <!-- -->
                                 </i>
                              </span>
                              <div class="cat__core__step__desc">
                                 <span class="cat__core__step__title">Logged In Users</span>
                                 <p>Total : {{ total.loggedinusers }}</p>
                              </div>
                           </div>
                        </div>
                     </div>
                     <!-- <div class="col-lg-3">
                        <div class="cat__core__widget">
                            <div class="cat__core__step cat__core__step--primary">
                                <span class="cat__core__step__digit">
                                    <img class="img-responsive" src="./assets/img/dashboard/patients.png" />
                                </span>
                                <div class="cat__core__step__desc">
                                    <span class="cat__core__step__title">Patients</span>
                                    <p>Total: {{ total.patients }}</p>
                                </div>
                            </div>
                        </div>
                        </div>
                        <div class="col-lg-3">
                        <div class="cat__core__widget">
                            <div class="cat__core__step cat__core__step--danger">
                                <span class="cat__core__step__digit">
                                    <img class="img-responsive" src="./assets/img/dashboard/staff.png" />
                                </span>
                                <div class="cat__core__step__desc">
                                    <span class="cat__core__step__title">Staff</span>
                                    <p>Total: {{ total.staff }}</p>
                                </div>
                            </div>
                        </div>
                        </div>
                        <div class="col-lg-3">
                        <div class="cat__core__widget">
                            <div class="cat__core__step cat__core__step--warning">
                                <span class="cat__core__step__digit">
                                    <i class="icmn-user-tie"></i>
                                </span>
                                <div class="cat__core__step__desc">
                                    <span class="cat__core__step__title">Partners</span>
                                    <p>Total: {{ total.partner }}</p>
                                </div>
                            </div>
                        </div>
                        </div> -->
                     <div class="col-lg-3">
                        <div class="cat__core__widget" (click)="loadEnrollmentsblock()">
                           <div class="cat__core__step cat__core__step--danger">
                              <span class="cat__core__step__digit">
                                 <i class="fa fa-bell ">
                                    <!-- -->
                                 </i>
                              </span>
                              <div class="cat__core__step__desc">
                                 <span class="cat__core__step__title">Patient Enrollments</span>
                                 <p>Total: {{ total.patientInvites }}</p>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="col-lg-3">
                        <div class="cat__core__widget" (click)="loadEnrollmentsblock()">
                           <div class="cat__core__step cat__core__step--success">
                              <span class="cat__core__step__digit">
                                 <i class="fa fa-bell ">
                                    <!-- -->
                                 </i>
                              </span>
                              <div class="cat__core__step__desc">
                                 <span class="cat__core__step__title">Staff Enrollments</span>
                                 <p>Total: {{ total.staffInvites }}</p>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="col-lg-3">
                        <div class="cat__core__widget" (click)="loadEnrollmentsblock()">
                           <div class="cat__core__step cat__core__step--primary">
                              <span class="cat__core__step__digit">
                                 <i class="fa fa-bell ">
                                    <!-- -->
                                 </i>
                              </span>
                              <div class="cat__core__step__desc">
                                 <span class="cat__core__step__title">Partner Enrollments</span>
                                 <p>Total: {{ total.partnerInvites }}</p>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="col-lg-3">
                        <div class="cat__core__widget" (click)="loadFormsblock()">
                           <div class="cat__core__step cat__core__step--warning">
                              <span class="cat__core__step__digit">
                                 <i class="icmn-file-text">
                                    <!-- -->
                                 </i>
                              </span>
                              <div class="cat__core__step__desc">
                                 <span class="cat__core__step__title">Staff Forms Completed</span>
                                 <p>Total: {{ total.staffFormsSubmitted }}</p>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="col-lg-3">
                        <div class="cat__core__widget" (click)="loadFormsblock()">
                           <div class="cat__core__step cat__core__step--primary">
                              <span class="cat__core__step__digit">
                                 <i class="icmn-file-text">
                                    <!-- -->
                                 </i>
                              </span>
                              <div class="cat__core__step__desc">
                                 <span class="cat__core__step__title">Patient Forms Completed</span>
                                 <p>Total: {{ total.patientFormsSubmitted }}</p>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="col-lg-3">
                        <div class="cat__core__widget" (click)="loadFormsblock()">
                           <div class="cat__core__step cat__core__step--danger">
                              <span class="cat__core__step__digit">
                                 <i class="icmn-file-text">
                                    <!-- -->
                                 </i>
                              </span>
                              <div class="cat__core__step__desc">
                                 <span class="cat__core__step__title">Practitioner Forms Completed</span>
                                 <p>Total: {{ total.practitioneformsSubmitted }}</p>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="col-lg-3">
                        <div class="cat__core__widget" (click)="loadMessageblock()">
                           <div class="cat__core__step cat__core__step--warning">
                              <span class="cat__core__step__digit">
                                 <i class="fa fa-comments-o">
                                    <!-- -->
                                 </i>
                              </span>
                              <div class="cat__core__step__desc">
                                 <span class="cat__core__step__title">Patient Messages</span>
                                 <p>Total: {{ total.messages }}</p>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="col-lg-3">
                        <div class="cat__core__widget" (click)="loadDocumentsblock()">
                           <div class="cat__core__step cat__core__step--success">
                              <span class="cat__core__step__digit">
                                 <i class="icmn-pen">
                                    <!-- -->
                                 </i>
                              </span>
                              <div class="cat__core__step__desc">
                                 <span class="cat__core__step__title">Documents Submitted</span>
                                 <p>Total: {{ total.signatureSubmitted }}</p>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="col-sm-12">
                        <!--here............-->
                        <!-- <section class="card" order-id="card-1">
                           <div class="card-header">
                               <div class="pull-right cat__core__sortable__control">
                                   <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                   <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                   <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                               </div>
                           
                               <div class="pull-right cat__core__sortable__control">
                                   <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'outcomemeasures')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                               </div>
                               <h5 class="mb-0 text-black">
                                   <strong>Outcome Measures</strong>
                                   <small style="font-weight:bold" class="badge badge-primary">{{ description.messages }}</small>
                               </h5>
                           </div>
                           <div class="card-block">
                               <div class="row">
                                   <div class="col-lg-12">
                                       <div class="table-responsive">
                                           <table class="table table-borderless table-hover item-num-name">
                                               <tbody>
                                                   <tr class="tit-outcome">
                                                       <td>General</td>
                                                       <td></td>
                                                   </tr>
                                                   <tr *ngFor="let item of outcomeMeasures">
                                                       <td align="right" class="item-total-num">{{ item.total | number }}</td>
                                                       <td align="left" class="item-total-text">{{ item.name }}</td>
                                                   </tr>
                                                   <tr class="tit-outcome">
                                                       <td>By Staff</td>
                                                       <td></td>
                                                   </tr>
                                                   <tr *ngFor="let item of outcomeMeasuresStaff">
                                                       <td align="right" class="item-total-num">{{ item.total | number }}</td>
                                                       <td align="left" class="item-total-text">{{ item.name }}</td>
                                                   </tr>
                                                   <tr class="tit-outcome">
                                                       <td>By Patients</td>
                                                       <td></td>
                                                   </tr>
                                                   <tr *ngFor="let item of outcomeMeasuresPatients">
                                                       <td align="right" class="item-total-num">{{ item.total | number }}</td>
                                                       <td align="left" class="item-total-text">{{ item.name }}</td>
                                                   </tr>
                                                   <tr class="tit-outcome">
                                                       <td>By Pending Revenue Accelerated based on Document Type in Signature Request </td>
                                                       <td></td>
                                                   </tr>
                                                   <tr *ngFor="let item of outcomeMeasuresSignedDocs">
                                                       <td align="right" class="item-total-num">{{ item.total | number }}</td>
                                                       <td align="left" class="item-total-text">{{ item.name }}</td>
                                                   </tr>
                                               </tbody>
                                           </table>
                                       </div>
                                   </div>
                               </div>
                           </div>
                           </section> -->
                        <!--here.......-->
                     </div>
                     <!--start-->
                     <!-- <div class="col-sm-12">
                        <section class="card" order-id="card-1">
                            <div class="card-header">
                                <div class="pull-right cat__core__sortable__control">
                                    <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                    <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                    <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                </div>
                        
                                <div class="pull-right cat__core__sortable__control">
                                    <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'outcomemeasuresstaff')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                                </div>
                                <h5 class="mb-0 text-black">
                                    <strong>Outcome Measures By Message Tag Types</strong>
                                    <small style="font-weight:bold" class="badge badge-primary">{{ description.outcomemeasuresstaff }}</small>
                                </h5>
                            </div>
                            <div class="card-block">
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="table-responsive">
                                            <table class="table table-borderless table-hover item-num-name">
                                                <tbody>
                                                   
                                                    <tr [hidden]="showOutcomeTagsEmptyMessage">
                                                        <td><span>No Outcome Measures By Message Tag Types To Show</span></td>
                                                    </tr>
                        
                                                    <ng-container *ngFor="let item of outcomeMeasuresGroup">
                                                        <tr class="tit-outcome">
                                                            <td>{{item}}</td>
                                                        </tr>
                                                        <ng-container *ngFor="let data of outcomeMeasuresTagsbtGroup[item]">
                                                            <tr>
                                                                <td align="right" class="item-total-num">{{ data.total | number }}</td>
                                                                <td align="left" class="item-total-text">{{ data.name }}</td>
                                                            </tr>
                                                        </ng-container>
                                                    </ng-container>
                        
                                                  
                        
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </section>
                        
                        </div> -->
                     <!--end-->
                  </div>
               </div>
               <div class="tab-pane" id="faq1" role="tabcard">
                  <div class="row">
                     <div class="col-lg-3">
                        <div class="cat__core__widget">
                           <div class="cat__core__step cat__core__step--primary">
                              <span class="cat__core__step__digit">
                              <i class="fa fa-question-circle" aria-hidden="true"></i>
                              </span>
                              <div class="cat__core__step__desc">
                                 <span class="cat__core__step__title">FAQs</span>
                                 <p>Total Visits: {{ total.faq_visit }}</p>
                              </div>
                           </div>
                        </div>
                     </div>
                     <div class="col-lg-3">
                        <div class="cat__core__widget">
                           <div class="cat__core__step cat__core__step--success">
                              <span class="cat__core__step__digit">
                              <i class="fa fa-question-circle" aria-hidden="true"></i>
                              </span>
                              <div class="cat__core__step__desc">
                                 <span class="cat__core__step__title"> FAQ Responses</span>
                                 <p>Total : {{ total.faq_response }}</p>
                              </div>
                           </div>
                        </div>
                     </div>
                  </div>
                  <div class="row">
                     <div class="col-lg-12">
                        <div class="" id="left-col">
                           <div style="width:100%" class="row cat__core__sortable">
                              <div class="col-lg-6">
                                 <section class="card" order-id="card-1">
                                    <div class="card-header">
                                       <div class="pull-right cat__core__sortable__control">
                                          <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                          <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                          <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                       </div>
                                       <div class="pull-right cat__core__sortable__control">
                                          <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'faq')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                                       </div>
                                    </div>
                                    <h5 class="mb-0 text-black">
                                       <strong>FAQ Visits to Initial and Inner Pages</strong>
                                       <small style="font-weight:bold" class="badge badge-success">{{ total.faq }}</small>
                                       <small style="font-weight:bold" class="badge badge-primary">{{ description.faq }}</small>
                                    </h5>
                              </div>
                              <div class="card-block">
                              <div class="row">
                              <div class="col-lg-12">
                              <canvas id="chart-faq-bar"></canvas>
                              </div>
                              </div>
                              </div>
                              </section>
                           </div>
                           <div class="col-lg-6">
                              <section class="card" order-id="card-1">
                                 <div class="card-header">
                                    <div class="pull-right cat__core__sortable__control">
                                       <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                       <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                       <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                    </div>
                                    <div class="pull-right cat__core__sortable__control">
                                       <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'faq_visit')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                                    </div>
                                 </div>
                                 <h5 class="mb-0 text-black">
                                    <strong>FAQ Visits to Initial Pages</strong>
                                    <small style="font-weight:bold" class="badge badge-success">{{ total.faq_visit_initial }}</small>
                                    <small style="font-weight:bold" class="badge badge-primary">{{ description.faq_visit }}</small>
                                 </h5>
                           </div>
                           <div class="card-block">
                           <div class="row">
                           <div class="col-lg-12">
                           <canvas id="chart-faq-visit-bar"></canvas>
                           </div>
                           </div>
                           </div>
                           </section>
                        </div>
                        <div class="col-lg-6">
                           <section class="card" order-id="card-1">
                              <div class="card-header">
                                 <div class="pull-right cat__core__sortable__control">
                                    <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                    <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                    <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                 </div>
                                 <div class="pull-right cat__core__sortable__control">
                                    <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'faq_problem')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                                 </div>
                              </div>
                              <h5 class="mb-0 text-black">
                                 <strong>FAQ Responses</strong>
                                 <small style="font-weight:bold" class="badge badge-success">{{ total.faq_problem }}</small>
                                 <small style="font-weight:bold" class="badge badge-primary">{{ description.faq_problem }}</small>
                              </h5>
                        </div>
                        <div class="card-block">
                        <div class="row">
                        <div class="col-lg-12">
                        <canvas id="chart-faq-problem-bar"></canvas>
                        </div>
                        </div>
                        </div>
                        </section>
                     </div>
                     <div class="col-lg-6">
                        <section class="card" order-id="card-1">
                           <div class="card-header">
                              <div class="pull-right cat__core__sortable__control">
                                 <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                 <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                 <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                              </div>
                              <div class="pull-right cat__core__sortable__control">
                                 <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'faq_patients')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                              </div>
                           </div>
                           <h5 class="mb-0 text-black">
                              <strong>Patients FAQ Visits to Initial Pages</strong>
                              <small style="font-weight:bold" class="badge badge-success">{{ total.faq_patient_visit }}</small>
                              <small style="font-weight:bold" class="badge badge-primary">{{ description.faq_patients }}</small>
                           </h5>
                     </div>
                     <div class="card-block">
                     <div class="row">
                     <div class="col-lg-12">
                     <canvas id="chart-faq-patients-bar"></canvas>
                     </div>
                     </div>
                     </div>
                     </section>
                  </div>
                  <div class="col-lg-6">
                     <section class="card" order-id="card-1">
                        <div class="card-header">
                           <div class="pull-right cat__core__sortable__control">
                              <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                              <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                              <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                           </div>
                           <div class="pull-right cat__core__sortable__control">
                              <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'faq_pump')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                           </div>
                        </div>
                        <h5 class="mb-0 text-black">
                           <strong>Pump Alarming FAQs Total Visits</strong>
                           <small style="font-weight:bold" class="badge badge-success">{{ total.faq_pump_visit }}</small>
                           <small style="font-weight:bold" class="badge badge-primary">{{ description.faq_pump }}</small>
                        </h5>
                  </div>
                  <div class="card-block">
                  <div class="row">
                  <div class="col-lg-12">
                  <canvas id="chart-faq-pump-bar"></canvas>
                  </div>
                  </div>
                  </div>
                  </section>
               </div>
               <div class="col-lg-6">
                  <section class="card" order-id="card-1">
                     <div class="card-header">
                        <div class="pull-right cat__core__sortable__control">
                           <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                           <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                           <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                        </div>
                        <div class="pull-right cat__core__sortable__control">
                           <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'faq_pump_patients')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                        </div>
                     </div>
                     <h5 class="mb-0 text-black">
                        <strong>Pump Alarming FAQs Patients Visit</strong>
                        <small style="font-weight:bold" class="badge badge-success">{{ total.faq_pump_patient_visit }}</small>
                        <small style="font-weight:bold" class="badge badge-primary">{{ description.faq_pump_patients }}</small>
                     </h5>
               </div>
               <div class="card-block">
               <div class="row">
               <div class="col-lg-12">
               <canvas id="chart-faq-pump-patients-bar"></canvas>
               </div>
               </div>
               </div>
               </section>
            </div>
         </div>
      </div>
   </div>
   </div>
   </div>
   <div class="tab-pane" id="message1" role="tabcard">
      <div class="row">
         <div class="col-lg-3">
            <div class="cat__core__widget">
               <div class="cat__core__step cat__core__step--warning">
                  <span class="cat__core__step__digit">
                  <img class="img-responsive" src="./assets/img/dashboard/messages.png" />
                  </span>
                  <div class="cat__core__step__desc">
                     <span class="cat__core__step__title">Messages</span>
                     <p>Total: {{ total.messages }}</p>
                  </div>
               </div>
            </div>
         </div>
      </div>
      <div class="row">
         <div class="col-lg-12">
            <div class="" id="left-col">
               <div style="width:100%" class="row cat__core__sortable">
                  <div class="col-lg-6">
                     <section class="card" order-id="card-1">
                        <div class="card-header">
                           <div class="pull-right cat__core__sortable__control">
                              <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                              <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                              <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                           </div>
                           <div class="pull-right cat__core__sortable__control">
                              <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'messages')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                           </div>
                        </div>
                        <h5 class="mb-0 text-black">
                           <strong>Messages</strong>
                           <small style="font-weight:bold" class="badge badge-primary">{{ description.messages }}</small>
                        </h5>
                  </div>
                  <div class="card-block">
                  <!--<div class="row">
                     <div id="chart-message-legend" class="col-lg-7">
                     
                     </div>
                     <div class="col-lg-5">
                         <canvas id="chart-message-doughnut"></canvas>
                     </div>
                     </div>-->
                  <div class="row">
                  <div class="col-lg-12">
                  <div class="table-responsive">
                  <table class="table table-borderless table-hover">
                  <tbody>
                  <tr *ngFor="let item of messageChart">
                  <td align="right" class="item-total-num">{{ item.total | number }} </td>
                  <td align="left" class="item-total-text"> {{ item.name }}</td>
                  </tr>
                  </tbody>
                  </table>
                  </div>
                  </div>
                  </div>
                  </div>
                  </section>
               </div>
               <div class="col-lg-6">
                  <section class="card" order-id="card-1">
                     <div class="card-header">
                        <div class="pull-right cat__core__sortable__control">
                           <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                           <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                           <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                        </div>
                        <div class="pull-right cat__core__sortable__control">
                           <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'message_by_role')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                        </div>
                     </div>
                     <h5 class="mb-0 text-black">
                        <strong>Message by User Roles</strong>
                        <small style="font-weight:bold" class="badge badge-primary">{{ description.message_by_role }}</small>
                     </h5>
               </div>
               <div class="card-block">
               <div class="row">
               <div class="col-lg-12">
               <canvas id="chart-message-by-role-bar"></canvas>
               </div>
               </div>
               </div>
               </section>
            </div>
            <div class="col-lg-6">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <!--<div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'messages')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                        </div>-->
                     <h5 class="mb-0 text-black">
                        <strong>Message Escalations</strong>
                        <!--<small style="font-weight:bold" class="badge badge-primary">{{ description.messages }}</small>-->
                     </h5>
                  </div>
                  <div class="card-block">
                     <div class="row">
                        <div class="col-lg-12">
                           <canvas id="chart-message-escalation-bar"></canvas>
                        </div>
                     </div>
                  </div>
               </section>
            </div>
            <div class="col-lg-6">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'message_translated')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                     <strong>Message Translations</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.message_translated }}</small>
                  </h5>
            </div>
            <div class="card-block">
            <div class="row">
            <div class="col-lg-12">
            <canvas id="chart-message-translated-bar"></canvas>
            </div>
            </div>
            </div>
            </section>
         </div>
         <div class="col-lg-6">
            <section class="card" order-id="card-1">
               <div class="card-header">
                  <div class="pull-right cat__core__sortable__control">
                     <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                     <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                     <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                  </div>
                  <div class="pull-right cat__core__sortable__control">
                     <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'message_tag')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                  </div>
               </div>
               <h5 class="mb-0 text-black">
                  <strong>Message Tags Usage</strong>
                  <small style="font-weight:bold" class="badge badge-primary">{{ description.message_tag }}</small>
               </h5>
         </div>
         <div class="card-block">
         <div class="row">
         <div class="col-lg-12">
         <canvas id="chart-message-tag-bar"></canvas>
         </div>
         </div>
         </div>
         </section>
      </div>
      <div class="col-lg-6">
         <section class="card" order-id="card-1">
            <div class="card-header">
               <div class="pull-right cat__core__sortable__control">
                  <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                  <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                  <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
               </div>
               <div class="pull-right cat__core__sortable__control">
                  <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'message_tag_role')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
               </div>
            </div>
            <h5 class="mb-0 text-black">
               <strong>Message Tags by User Roles</strong>
               <small style="font-weight:bold" class="badge badge-primary">{{ description.message_tag_role }}</small>
            </h5>
      </div>
      <div class="card-block">
      <div class="row">
      <div class="col-lg-12">
      <canvas id="chart-message-tag-role-bar"></canvas>
      </div>
      </div>
      </div>
      </section>
   </div>
   <div class="col-lg-6">
      <section class="card" order-id="card-1">
         <div class="card-header">
            <div class="pull-right cat__core__sortable__control">
               <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
               <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
               <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
            </div>
            <div class="pull-right cat__core__sortable__control">
               <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'chat_initiated')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
            </div>
         </div>
         <h5 class="mb-0 text-black">
            <strong>Common Patient Requests</strong>
            <small style="font-weight:bold" class="badge badge-primary">{{ description.chat_initiated }}</small>
         </h5>
   </div>
   <div class="card-block">
   <div class="row">
   <div class="col-lg-12">
   <canvas id="chart-chat-initiated-bar"></canvas>
   </div>
   </div>
   </div>
   </section>
   </div>
   </div>
   </div>
   </div>
   </div>
   </div>
   <div class="tab-pane" id="signature1" role="tabcard">
      <div class="row">
         <div class="col-lg-3">
            <div class="cat__core__widget">
               <div class="cat__core__step cat__core__step--success">
                  <span class="cat__core__step__digit">
                     <i class="icmn-users">
                        <!-- -->
                     </i>
                  </span>
                  <div class="cat__core__step__desc">
                     <span class="cat__core__step__title">Signature Requests</span>
                     <p>Total: {{ total.signature }}</p>
                  </div>
               </div>
            </div>
         </div>
      </div>
      <div class="row">
         <div class="col-lg-12">
            <div class="" id="left-col">
               <div style="width:100%" class="row cat__core__sortable">
                  <div class="col-lg-6">
                     <section class="card" order-id="card-1">
                        <div class="card-header">
                           <div class="pull-right cat__core__sortable__control">
                              <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                              <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                              <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                           </div>
                           <div class="pull-right cat__core__sortable__control">
                              <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'signature')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                           </div>
                        </div>
                        <h5 class="mb-0 text-black">
                           <strong>Signature Requests</strong>
                           <small style="font-weight:bold" class="badge badge-primary">{{ description.signature }}</small>
                        </h5>
                  </div>
                  <div class="card-block">
                  <div class="row">
                  <div id="chart-signature-legend" class="col-lg-7">
                  </div>
                  <div class="col-lg-5">
                  <canvas id="chart-signature-doughnut"></canvas>
                  </div>
                  </div>
                  </div>
                  </section>
               </div>
               <div class="col-lg-6">
                  <section class="card" order-id="card-1">
                     <div class="card-header">
                        <div class="pull-right cat__core__sortable__control">
                           <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                           <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                           <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                        </div>
                        <div class="pull-right cat__core__sortable__control">
                           <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'document')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                        </div>
                     </div>
                     <h5 class="mb-0 text-black">
                        <strong>Signature Requests by Document Types</strong>
                        <small style="font-weight:bold" class="badge badge-primary">{{ description.document }}</small>
                     </h5>
               </div>
               <div class="card-block">
               <div class="row">
               <div class="col-lg-12">
               <canvas id="chart-document-bar"></canvas>
               </div>
               </div>
               </div>
               </section>
            </div>
         </div>
      </div>
   </div>
   </div>
   </div>
   <div class="tab-pane" id="users1" role="tabcard">
      <div class="row">
         <div class="col-lg-3">
            <div class="cat__core__widget">
               <div class="cat__core__step cat__core__step--success">
                  <span class="cat__core__step__digit">
                     <i class="icmn-users">
                        <!-- -->
                     </i>
                  </span>
                  <div class="cat__core__step__desc">
                     <span class="cat__core__step__title">Users</span>
                     <p>Total: {{ total.patients + total.staff }}</p>
                  </div>
               </div>
            </div>
         </div>
         <div class="col-lg-3">
            <div class="cat__core__widget">
               <div class="cat__core__step cat__core__step--primary">
                  <span class="cat__core__step__digit">
                     <!--<i class="icmn-users"></i>-->
                     <img class="img-responsive" src="./assets/img/dashboard/patients.png" />
                  </span>
                  <div class="cat__core__step__desc">
                     <span class="cat__core__step__title">Patients</span>
                     <p>Total: {{ total.patients }}</p>
                  </div>
               </div>
            </div>
         </div>
         <div class="col-lg-3">
            <div class="cat__core__widget">
               <div class="cat__core__step cat__core__step--danger">
                  <span class="cat__core__step__digit">
                  <img class="img-responsive" src="./assets/img/dashboard/staff.png" />
                  </span>
                  <div class="cat__core__step__desc">
                     <span class="cat__core__step__title">Staff</span>
                     <p>Total: {{ total.staff }}</p>
                  </div>
               </div>
            </div>
         </div>
         <div class="row">
            <div class="col-lg-12">
               <div class="" id="left-col">
                  <div style="width:100%" class="row cat__core__sortable">
                     <div class="col-lg-6">
                        <section class="card" order-id="card-1">
                           <div class="card-header">
                              <div class="pull-right cat__core__sortable__control">
                                 <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                 <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                 <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                              </div>
                              <!--<div class="pull-right cat__core__sortable__control">
                                 <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'patients')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                                 </div>-->
                              <h5 class="mb-0 text-black">
                                 <strong>Patients</strong>
                                 <!--<small style="font-weight:bold" class="badge badge-primary">{{ description.patients }}</small>-->
                              </h5>
                           </div>
                           <div class="card-block">
                              <div class="row">
                                 <div id="chart-patient-legend" class="col-lg-7">
                                 </div>
                                 <div class="col-lg-5">
                                    <canvas id="chart-patient-doughnut"></canvas>
                                 </div>
                              </div>
                           </div>
                        </section>
                     </div>
                     <div class="col-lg-6">
                        <section class="card" order-id="card-1">
                           <div class="card-header">
                              <div class="pull-right cat__core__sortable__control">
                                 <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                 <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                 <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                              </div>
                              <!--<div class="pull-right cat__core__sortable__control">
                                 <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'staff')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                                 </div>-->
                              <h5 class="mb-0 text-black">
                                 <strong>Staff</strong>
                                 <!--<small style="font-weight:bold" class="badge badge-primary">{{ description.staff }}</small>-->
                              </h5>
                           </div>
                           <div class="card-block">
                              <div class="row">
                                 <div id="chart-staff-legend" class="col-lg-7">
                                 </div>
                                 <div class="col-lg-5">
                                    <canvas id="chart-staff-doughnut"></canvas>
                                 </div>
                              </div>
                           </div>
                        </section>
                     </div>
                     <div class="col-lg-12">
                        <section class="card" order-id="card-1">
                           <div class="card-header">
                              <div class="pull-right cat__core__sortable__control">
                                 <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                 <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                 <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                              </div>
                              <h5 class="mb-0 text-black">
                                 <strong>User Roles</strong>
                              </h5>
                           </div>
                           <div class="card-block">
                              <div class="row">
                                 <div class="col-lg-12">
                                    <canvas id="chart-role-bar"></canvas>
                                 </div>
                              </div>
                           </div>
                        </section>
                     </div>
                  </div>
               </div>
            </div>
         </div>
      </div>
   </div>
   <div class="tab-pane" id="devices1" role="tabcard">
      <div class="row">
         <div class="col-lg-3">
            <div class="cat__core__widget">
               <div class="cat__core__step cat__core__step--success">
                  <span class="cat__core__step__digit">
                  <img class="img-responsive" src="./assets/img/dashboard/devices.png" />
                  </span>
                  <div class="cat__core__step__desc">
                     <span class="cat__core__step__title">Devices</span>
                     <p>Total: {{ total.devices }}</p>
                  </div>
               </div>
            </div>
         </div>
      </div>
      <div class="row">
         <div class="col-lg-12">
            <div class="" id="left-col">
               <div style="width:100%" class="row cat__core__sortable">
                  <div class="col-lg-6">
                     <section class="card" order-id="card-1">
                        <div class="card-header">
                           <div class="pull-right cat__core__sortable__control">
                              <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                              <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                              <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                           </div>
                           <div class="pull-right cat__core__sortable__control">
                              <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'platform')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                           </div>
                        </div>
                        <h5 class="mb-0 text-black">
                           <strong>Devices</strong>
                           <small style="font-weight:bold" class="badge badge-primary">{{ description.platform }}</small>
                        </h5>
                  </div>
                  <div class="card-block">
                  <div class="row">
                  <div id="chart-platform-legend" class="col-lg-4">
                  </div>
                  <div class="col-lg-8">
                  <canvas id="chart-platform-pie"></canvas>
                  </div>
                  </div>
                  </div>
                  </section>
               </div>
            </div>
         </div>
      </div>
   </div>
   </div>
   <!-- ========================invite====================================== -->
   <div class="tab-pane" id="newitems" role="tabcard">
      <div class="row">
         <!-- <div class="col-lg-3">
            <div class="cat__core__widget">
                <div class="cat__core__step cat__core__step--primary">
                    <span class="cat__core__step__digit">
                        <i class="fa fa-bell"></i>
                    </span>
                    <div class="cat__core__step__desc">
                        <span class="cat__core__step__title">Total Invitations Send</span>
                        <p>Total: {{ total.inviteSent }}</p>
                    </div>
                </div>
            </div>
            </div>
            <div class="col-lg-3">
            <div class="cat__core__widget">
                <div class="cat__core__step cat__core__step--success">
                    <span class="cat__core__step__digit">
                        <i class="fa fa-globe"></i>
                    </span>
                    <div class="cat__core__step__desc">
                        <span class="cat__core__step__title">Total Logged In </span>
                        <p>Total: {{ total.inviteLoggedIn }}</p>
                    </div>
                </div>
            </div>
            </div> -->
      </div>
      <div class="nav-tabs-horizontal"  >
         <ul class="nav nav-tabs filing-icons" role="tablist">
            <li class="nav-item" (click)="loadEnrollments()">
               <a class="nav-link" id="newitemsidN" href="javascript: void(0);" data-toggle="tab" data-target="#newitems" role="tab" aria-expanded="true">
               <i class="fa fa-bell" aria-hidden="true"></i>
               <span>Enrollments Summary</span></a>
            </li>
            <li class="nav-item" (click)="loadEnrollTrends()">
               <a class="nav-link" id="enrollTrends" href="javascript: void(0);" data-toggle="tab" data-target="#enrollTrendsitems" role="tab" aria-expanded="true">
               <i class="fa fa-industry" aria-hidden="true"></i>
               <span>Enrollment Trends</span></a>
            </li>
            <li class="nav-item" (click)="loadEnrollmentsReports()">
               <a class="nav-link" id="EnrollmentsReports" href="javascript: void(0);" data-toggle="tab" data-target="#enrollmentsReports" role="tab" aria-expanded="true">
               <i class="fa fa-file" aria-hidden="true"></i>
               <span>Enrollment Reports</span></a>
            </li>
         </ul>
      </div>
      <div class="row">
         <div class="col-lg-8">
            <div class="" id="left-col">
               <div style="width:100% ;margin-top: 30px;" class="row cat__core__sortable">
                  <div class="col-lg-12">
                  </div>
               </div>
            </div>
         </div>
      </div>
      <div class="row">
         <div class="col-lg-6">
            <div class="" id="left-col">
               <div style="width:100%" class="row cat__core__sortable">
                  <div class="col-lg-12">
                     <section class="card" order-id="card-1">
                        <div class="card-header">
                           <div class="pull-right cat__core__sortable__control">
                              <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                              <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                              <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                           </div>
                           <div class="pull-right cat__core__sortable__control">
                              <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'totalenrollment')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                           </div>
                        </div>
                        <h5 class="mb-0 text-black">
                           <strong>Total Enrollments by User Category</strong>
                           <small style="font-weight:bold" class="badge badge-primary">{{ description.totalenrollment }}</small>
                        </h5>
                  </div>
                  <div class="card-block">
                  <div class="row">
                  <div class="col-lg-10" style="padding-bottom:5px">    
                  <strong>{{total.userEnrolled}} Total Enrollments </strong>
                  </div>    
                  </div>
                  <div class="row">
                  <div id="chart-totalenrollment-legend" class="col-lg-4">
                  </div>
                  <div class="col-lg-8">
                  <canvas id="chart-totalenrollment-pie" style="height: 200px!important;"></canvas>
                  <!-- <canvas id="canvas" width=300 height=300></canvas> -->
                  </div>
                  </div>
                  </div>
                  </section>   
               </div>
            </div>
         </div>
      </div>
      <div class="col-lg-6">
         <div class="" id="left-col">
            <div style="width:100%" class="row cat__core__sortable">
               <div class="col-lg-12">
                  <section class="card" order-id="card-1">
                     <div class="card-header">
                        <div class="pull-right cat__core__sortable__control">
                           <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                           <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                           <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                        </div>
                        <div class="pull-right cat__core__sortable__control">
                           <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'totalinvite')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                        </div>
                     </div>
                     <h5 class="mb-0 text-black">
                        <strong>Total User Enrollment Status</strong>
                        <small style="font-weight:bold" class="badge badge-primary">{{ description.totalinvite }}</small>
                     </h5>
               </div>
               <div class="card-block">
               <div class="row">
               <div class="col-lg-10" style="padding-bottom:5px">    
               <strong>{{total.userInvited}} Total User Enrollments</strong>
               </div>    
               </div>
               <div class="row">
               <div id="chart-totalinvite-legend" class="col-lg-4">
               </div>
               <div class="col-lg-8">
               <canvas id="chart-totalinvite-pie" style="height: 200px!important;"></canvas>
               <!-- <canvas id="canvas" width=300 height=300></canvas> -->
               </div>
               </div>
               </div>
               </section>
            </div>
         </div>
      </div>
   </div>
   </div>
   <div class="row">
      <div class="col-lg-6">
         <div class="" id="left-col">
            <div style="width:100%" class="row cat__core__sortable">
               <div class="col-lg-12">
                  <section class="card" order-id="card-1">
                     <div class="card-header">
                        <div class="pull-right cat__core__sortable__control">
                           <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                           <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                           <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                        </div>
                        <div class="pull-right cat__core__sortable__control">
                           <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'invite')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                        </div>
                     </div>
                     <h5 class="mb-0 text-black">
                        <strong>Patient Enrollment Status</strong>
                        <small style="font-weight:bold" class="badge badge-primary">{{ description.invite }}</small>
                     </h5>
               </div>
               <div class="card-block">
               <div class="row">
               <div class="col-lg-10" style="padding-bottom:5px">    
               <strong>{{total.patientInvited}} Total Patient Enrollments</strong>    
               </div>
               </div>
               <div class="row">
               <div id="chart-invite-legend" class="col-lg-4">
               </div>
               <div class="col-lg-8">
               <canvas id="chart-invite-pie"></canvas>
               </div>
               </div>
               </div>
               </section>
            </div>
         </div>
      </div>
   </div>
   <div class="col-lg-6">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-12">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'partnerinvite')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                     <strong>Partner Enrollment Status</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.partnerinvite }}</small>
                  </h5>
            </div>
            <div class="card-block">
            <div class="row">
            <div class="col-lg-10" style="padding-bottom:5px">   
            <strong>{{total.partnerInvited}} Total Partner Enrollments</strong> 
            </div>       
            </div>
            <div class="row">
            <div id="chart-invite-partner-legend" class="col-lg-4">
            </div>
            <div class="col-lg-8">
            <canvas id="chart-invite-partner-pie"></canvas>
            </div>
            </div>
            </div>
            </section>
         </div>
      </div>
   </div>
   </div>
   </div>
   <div class="row">
      <div class="col-lg-6">
         <div class="" id="left-col">
            <div style="width:100%" class="row cat__core__sortable">
               <div class="col-lg-12">
                  <section class="card" order-id="card-1">
                     <div class="card-header">
                        <div class="pull-right cat__core__sortable__control">
                           <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                           <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                           <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                        </div>
                        <div class="pull-right cat__core__sortable__control">
                           <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'staffinvite')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                        </div>
                     </div>
                     <h5 class="mb-0 text-black">
                        <strong>Staff Enrollment Status</strong>
                        <small style="font-weight:bold" class="badge badge-primary">{{ description.staffinvite }}</small>
                     </h5>
               </div>
               <div class="card-block">
               <div class="row">
               <div class="col-lg-10" style="padding-bottom:5px">   
               <strong>{{total.staffInvited}} Total Staff Enrollments</strong> 
               </div>   
               </div>
               <div class="row">
               <div id="chart-invite-staff-legend" class="col-lg-4">
               </div>
               <div class="col-lg-8">
               <canvas id="chart-invite-staff-pie"></canvas>
               </div>
               </div>
               </div>
               </section>
            </div>
         </div>
      </div>
   </div>
   <div class="col-lg-6">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-12">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'patientInvites')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                     <strong>Source of Patient Record Creation</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.patientInvites }}</small>
                  </h5>
            </div>
            <div class="card-block">
            <div class="row">
            <div class="col-lg-10" style="padding-bottom:5px">   
            <strong>{{total.reistrationTypes}} Total Patient Records Created</strong> 
            </div>   
            </div>
            <div class="row">
            <div id="chart-patientInvites-legend" class="col-lg-4">
            </div>
            <div class="col-lg-8">
            <canvas id="chart-patientInvites-pie"></canvas>
            </div>
            </div>
            </div>
            </section>
         </div>
      </div>
   </div>
   </div>
   </div>
   </div>
   <div class="tab-pane" id="enrollmentsReports" role="tabcard">
      <div class="nav-tabs-horizontal">
         <ul class="nav nav-tabs filing-icons" role="tablist">
            <li class="nav-item" (click)="loadEnrollments()">
               <a class="nav-link" id="newitemsidN" href="javascript: void(0);" data-toggle="tab" data-target="#newitems" role="tab" aria-expanded="true">
               <i class="fa fa-bell" aria-hidden="true"></i>
               <span>Enrollments Summary</span></a>
            </li>
            <li class="nav-item" (click)="loadEnrollTrends()">
               <a class="nav-link" id="enrollTrends" href="javascript: void(0);" data-toggle="tab" data-target="#enrollTrendsitems" role="tab" aria-expanded="true">
               <i class="fa fa-industry" aria-hidden="true"></i>
               <span>Enrollment Trends</span></a>
            </li>
            <li class="nav-item" (click)="loadEnrollmentsReports()">
               <a class="nav-link" id="EnrollmentsReportsR" href="javascript: void(0);" data-toggle="tab" data-target="#enrollmentsReports" role="tab" aria-expanded="true">
               <i class="fa fa-file" aria-hidden="true"></i>
               <span>Enrollment Reports</span></a>
            </li>
         </ul>
      </div>
      <div class="row dashboard-report">
         <div class="col-lg-4">User Enrollment Report
            <a class="icmn-info" data-toggle="tooltip" title="Report of all Invited Users (Patients, Staff, Partners) in the current Tenant or Selected Branch(es)."></a>
         </div>
         <div class="col-lg-4">
            <button [disabled]="downloadLoader" type="button" class="btn btn-secondary "
               (click)=generateCSVfrontEnd()>Generate
               CSV</button>
            <span *ngIf="downloadLoader" role="status" aria-hidden="true" class="fa fa-spinner fa-spin fa-2x"></span>
      
         </div>
      </div>
    
   </div>
   <!-- ==============================forms======================================== -->
   <!-- <div class="tab-pane" id="formitems1111" role="tabcard">
      <div class="nav-tabs-horizontal">
          <ul class="nav nav-tabs filing-icons" role="tablist">
              <li class="nav-item" (click)="loadForms()">
                  <a class="nav-link" id="formitemsid" href="javascript: void(0);" data-toggle="tab" data-target="#formitems11" role="tab" aria-expanded="true">
                      <i class="fa fa-list-alt" aria-hidden="true"></i>
                      <span>Forms Summary</span></a>
              </li>
      <li class="nav-item" (click)="loadFormsTrends()">
          <a class="nav-link" id="formsTrends" href="javascript: void(0);" data-toggle="tab" data-target="#formsTrendsitems" role="tab" aria-expanded="true">
              <i class=" icmn-pen" aria-hidden="true"></i>
              <span>Form Trends</span></a>
      </li>
          </ul>
      </div>
      
      
      
      
      
      
      </div> -->
   <!-- ======================new 30/12==================================== -->
   <div class="tab-pane" id="formitems" role="tabcard">
      <div class="nav-tabs-horizontal">
         <ul class="nav nav-tabs filing-icons" role="tablist">
            <li class="nav-item" (click)="loadForms()">
               <a class="nav-link" id="formitemsidN" href="javascript: void(0);" data-toggle="tab" data-target="#formitems" role="tab" aria-expanded="true">
               <i class="fa fa-list-alt" aria-hidden="true"></i>
               <span>Forms Summary</span></a>
            </li>
            <li class="nav-item" (click)="loadFormsTrends()">
               <a class="nav-link" id="formsTrends" href="javascript: void(0);" data-toggle="tab" data-target="#formsTrendsitems" role="tab" aria-expanded="true">
               <i class="fa fa-industry" aria-hidden="true"></i>
               <span>Form Engagement Trends</span></a>
            </li>
            <li class="nav-item" (click)="loadFormsReports()">
               <a class="nav-link" id="formsReports" href="javascript: void(0);" data-toggle="tab" data-target="#formsReportsitems" role="tab" aria-expanded="true">
               <i class="fa fa-file" aria-hidden="true"></i>
               <span>Form Reports</span></a>
            </li>
         </ul>
      </div>
      <!--      <div class="row" > <div class="col-lg-8">
         <div class="formboarder" id="left-col" >
             <div class="row" style="margin-top: 10px;" *ngIf="formSummery && selectedtab == 'formsSummary'" >    <div class="col-md-8">                              
                            <div id="staffs" class="noboarder" >
                              <div class="recipient-search-area">
         
                                <div class="input-dropdown" style ="width : 330px !important">
         
                                  <input type="text" class="form-control" id="staffsInput" autocomplete="off" value=""
                                    placeholder="Search Staff"
                                  />
         
                                  <ul class="associate-ul recipient-ul" id="recipient-ul">
         
                                    <li
                                      id="recipient-li"
                                      class="associate-li recipient-li"
                                      *ngIf="userListDisaplay && userListDisaplay.length == 0"  >
                                      No item found
                                    </li>
         
                                    <li
                                      id="li-{{ user.userid }}"
                                      class="associate-li recipient-li"
                                      [ngClass]="{
                                        'li-selected': checkUserExist(user.userid)
                                      }"
                                      *ngFor="let user of userListDisaplay"
                                      (click)="setSelectedRecipients(user, user.userid)"
                                    >
                                      {{ user.displayname }}
                                    </li>
         
                                    <li
                                      class="render-manipulate"
                                      *ngIf="userListDisaplay && userListDisaplay.length > 0"
                                    >
                                      <input
                                        type="button"
                                        class="recipient-select-all btn"
                                        (click)="selectAllRecipients()"
                                        value="Select All"
                                      />
         
                                      <input
                                        type="button"
                                        class="recipient-class-clear btn"
                                        (click)="closeSelectedRecipient(true)"
                                        value="Clear All"
                                      />
         
                                      <input
                                        type="button"
                                        class="recipient-class-done btn"
                                        *ngIf="selectedRecipients && selectedRecipients.length > 0"
                                        (click)="doneSelectedRecipient()"
                                        value="Done"
                                      />
         
                                    </li>
                                  </ul>
                                </div>
         
                                <div >
         
                                  <button type="button" [disabled]="recipientLoading"
                                    id="recipient-search"
                                    (click)="checkRecipientWithTems()"
                                    class="recipient-search-button btn btn-sm btn-primary"
                                  >
                                    Search1
                                  </button>
         
                                  <button
                                    type="button"
                                    [disabled]="recipientLoading"
                                    id="recipient-close"
                                    (click)="closeSelectedRecipient()"
                                    class="recipient-search-button btn btn-sm btn-default recipient-close"
                                  >
                                    Reset1
                                  </button>
         
                                </div>
                              </div>
                            </div>
                           
                         
                
                </div></div>
             <div style="width:100%" class="row cat__core__sortable">
                 <div class="col-lg-12">
                         <section  order-id="card-1">
                             
                                 <div class=" form-name-block form-inline form-type-online"> 
                                             
                                                 <div class="input-group mb-2 mr-sm-2 ">
                                                     <select class="form-control select2-formsType" id="filterFormTypes" data-placeholder="Select Form Type" multiple>
                                                         <option *ngFor="let formtag of formsTagList; let i = index" [attr.data-index]="i" id="{{i}}"
                                                             value="{{formtag.id}}"> {{formtag.tagName}} </option>
                                             
                                                     </select>
                                             
                                                 </div>
                                                 <div class="input-group mb-2 mr-sm-2">
                                                     <select class="form-control select2-forms" id="filterForms" data-placeholder="Select Form Name" multiple>
                                                         <option *ngFor="let form of forms" value="{{form.id}}"> {{form.name}} </option>
                                                         
                                                     </select>
                                             
                                                 </div>
                                 </div> 
                                        
                                    
                             </section> 
                 </div>
             </div>
         </div>
         </div>
         <div class="col-lg-4  align-self-end"> 
                <button  type="button" class="btn btn-secondary  mb-2" (click)="applyFormFilter('formfilter')"  >Filter</button> 
                <button  type="button" class="btn btn-secondary  mb-2" (click)="resetFilter()"  >Reset</button>   
         </div>
         </div> -->
      <div class="row patient-forms">
         <div class="col-lg-6">
            <div class="" id="left-col">
               <div style="width:100%" class="row cat__core__sortable">
                  <div class="col-lg-12">
                     <section class="card" order-id="card-1">
                        <div class="card-header">
                           <div class="pull-right cat__core__sortable__control">
                              <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                              <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                              <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                           </div>
                           <div class="pull-right cat__core__sortable__control">
                              <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsTotal')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                           </div>
                        </div>
                        <h5 class="mb-0 text-black">
                           <strong>Total Completed Forms by User Type</strong>
                           <small style="font-weight:bold" class="badge badge-primary">{{ description.formsTotal }}</small>
                        </h5>
                  </div>
                  <div class="card-block">
                  <div class="row">
                  <div class="col-lg-8"><b>{{formsCompletestotal}} Total Forms</b></div>
                  </div>
                  <div class="row">
                  <div id="chart-forms-legend-t" class="col-lg-4">
                  </div>
                  <div class="col-lg-6">
                  <canvas id="chart-forms-pie-t"></canvas>
                  </div>
                  </div>
                  </div>
                  </section>
               </div>
            </div>
         </div>
      </div>
      <!-- </div>
         <div class="row patient-forms"> -->
      <div class="col-lg-6">
         <div class="" id="left-col">
            <div style="width:100%" class="row cat__core__sortable">
               <div class="col-lg-12">
                  <section class="card" order-id="card-1">
                     <div class="card-header">
                        <div class="pull-right cat__core__sortable__control">
                           <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                           <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                           <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                        </div>
                        <div class="pull-right cat__core__sortable__control">
                           <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'forms')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                        </div>
                     </div>
                     <h5 class="mb-0 text-black">
                        <strong>Total Patient Engagement by Form Status</strong>
                        <small style="font-weight:bold" class="badge badge-primary">{{ description.forms }}</small>
                     </h5>
               </div>
               <div class="card-block">
               <div class="row">
               <div class="col-lg-8"><b>{{formsendtotal}} Total Forms</b></div>
               </div>
               <div class="row">
               <div id="chart-forms-legend" class="col-lg-4">
               </div>
               <div class="col-lg-6">
               <canvas id="chart-forms-pie"></canvas>
               </div>
               </div>
               </div>
               </section>
            </div>
         </div>
      </div>
   </div>
   </div>
   <!-- ======================= -->
   <div class="row">
      <div class="col-lg-6">
         <div class="" id="left-col">
            <div style="width:100%" class="row cat__core__sortable">
               <div class="col-lg-12">
                  <section class="card" order-id="card-1">
                     <div class="card-header">
                        <div class="pull-right cat__core__sortable__control">
                           <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                           <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                           <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                        </div>
                        <div class="pull-right cat__core__sortable__control">
                           <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'StaffFacingFormsSubmitted')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                        </div>
                     </div>
                     <h5 class="mb-0 text-black">
                        <strong>Total Staff/Partner Facing Forms by Status</strong>
                        <small style="font-weight:bold" class="badge badge-primary">{{ description.StaffFacingFormsSubmitted }}</small>
                     </h5>
               </div>
               <div class="card-block">
               <div class="row">
               <div class="col-lg-8"><b>{{totalFormsSubmittedStaffFacing}} Total Forms</b></div>
               </div>
               <div class="row">
               <div id="chart-forms-legend-s" class="col-lg-4">
               </div>
               <div class="col-lg-6">
               <canvas id="chart-forms-pie-s"></canvas>
               </div>
               </div>
               </div>
               </section>
            </div>
         </div>
      </div>
   </div>
   <!--  </div> -->
   <!-- ======================= -->
   <!--  <div class="row"> -->
   <div class="col-lg-6">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-12">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'formspractiotioner')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                     <strong>Total Staff-Practitioner Facing Forms by Status</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.formsp }}</small>
                  </h5>
            </div>
            <div class="card-block">
            <div class="row">
            <div class="col-lg-8"><b>{{formsendtotalp}} Total Forms</b></div>
            </div>
            <div class="row">
            <div id="chart-forms-legend-p" class="col-lg-4">
            </div>
            <div class="col-lg-6">
            <canvas id="chart-forms-pie-p"></canvas>
            </div>
            </div>
            </div>
            </section>
         </div>
      </div>
   </div>
   </div>
   </div>
   <!-- ======================== -->
   <div class="row">
      <div class="col-lg-12">
         <div class="" id="left-col">
            <div style="width:100%" class="row cat__core__sortable">
               <div class="col-lg-12">
                  <section class="card" order-id="card-1">
                     <div class="card-header">
                        <div class="pull-right cat__core__sortable__control">
                           <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                           <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                           <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                        </div>
                        <div class="pull-right cat__core__sortable__control">
                           <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'patientFormsAvgTime')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                        </div>
                     </div>
                     <h5 class="mb-0 text-black">
                        <strong>Patient Forms Engagement Average Completion Time</strong>
                        <small style="font-weight:bold" class="badge badge-primary">{{ description.patientFormsAvgTime }}</small>
                     </h5>
               </div>
               <div class="card-block">
               <div class="row  form-name-block">
               <div class="col-md-4 ">                                                                                
               <div class="large-tooltip show-msg-tooltip  dashboard-tooltip" ><i class="show-form-type-filter icmn-info  dashboard-tooltip-icon"></i></div>
               <select class="form-control select2-formsType" id="filterFormTypes" data-placeholder="All" multiple>
               <option *ngFor="let formtag of formsTagList; let i = index" [attr.data-index]="i" id="{{i}}"
               value="{{formtag.id}}"> {{formtag.tagName}} </option>
               </select>
               </div>
               <div class="col-md-4">
               <div class="large-tooltip show-msg-tooltip dashboard-tooltip " ><i class="show-form-name-filter icmn-info dashboard-tooltip-icon"></i></div>  
               <select class="form-control select2-forms" id="filterForms" data-placeholder="All" multiple>
               <option *ngFor="let form of forms" value="{{form.id}}"> {{form.formName}} </option>
               </select>
               </div>
               <div class="col-lg-4  align-self-end"> 
               <button  type="button" class="btn btn-secondary  mb-2" (click)="applyFormFilter('patientFormsAvgTime')"  >Filter</button> 
               <button  type="button" class="btn btn-secondary  mb-2" (click)="resetFilter()"  >Reset</button>   
               </div></div>
               <div class="row">
               <div class="col-lg-12">
                                             <label style="display: block" *ngIf="patientFormAvgTime.lessHour =='' " ><b> Average Time : {{patientFormAvgTime.avrgTime | number}} hour(s)</b></label>
               <label style="display: block" *ngIf="patientFormAvgTime.lessHour !='' && patientFormAvgTime.avrgTime < 1 " ><b> Average Time : {{patientFormAvgTime.lessHour}} hour(s)</b></label>
               </div>
               </div>
               </div>
               </section> 
            </div>
         </div>
      </div>
   </div>
   </div>
   <div class="row">
      <div class="col-lg-12">
         <div class="" id="left-col">
            <div style="width:100%" class="row cat__core__sortable">
               <div class="col-lg-8">
                  <section class="card" order-id="card-1">
                     <div class="card-header">
                        <div class="pull-right cat__core__sortable__control">
                           <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                           <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                           <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                        </div>
                        <div class="pull-right cat__core__sortable__control">
                           <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsend')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                        </div>
                     </div>
                     <h5 class="mb-0 text-black">
                        <strong>Forms Sent</strong>
                        <small style="font-weight:bold" class="badge badge-primary">{{ description.messages }}</small>
                     </h5>
               </div>
               <div class="card-block">
               <!--<div class="row">
                  <div id="chart-message-legend" class="col-lg-7">
                  
                  </div>
                  <div class="col-lg-5">
                      <canvas id="chart-message-doughnut"></canvas>
                  </div>
                  </div>-->
               <div class="row">
               <div class="col-lg-12">
               <div class="table-responsive">
               <table class="table table-borderless table-hover">
               <tbody>
               <tr *ngFor="let item of messageChartSend">
               <td align="right" class="item-total-num">{{ item.total | number }} </td>
               <td align="left" class="item-total-text"> {{ item.name }}</td>
               </tr>
               </tbody>
               </table>
               </div>
               </div>
               </div>
               </div>
               </section>
            </div>
         </div>
      </div>
   </div>
   </div>
   <div class="row">
      <div class="col-lg-12">
         <div class="" id="left-col">
            <div style="width:100%" class="row cat__core__sortable">
               <div class="col-lg-8">
                  <section class="card" order-id="card-1">
                     <div class="card-header">
                        <div class="pull-right cat__core__sortable__control">
                           <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                           <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                           <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                        </div>
                        <div class="pull-right cat__core__sortable__control">
                           <!-- <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsend')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div> -->
                        </div>
                        <h5 class="mb-0 text-black">
                           <strong>Forms that are not Sent</strong>
                           <small style="font-weight:bold" class="badge badge-primary"></small>
                        </h5>
                     </div>
                     <div class="card-block">
                        <div class="row">
                           <div class="col-lg-12">
                              <div class="table-responsive">
                                 <table class="table table-borderless table-hover">
                                    <tbody>
                                       <tr *ngFor="let item of formsNotSend">
                                          <td align="left" class="item-total-text"> {{ item.formName }}</td>
                                       </tr>
                                    </tbody>
                                 </table>
                              </div>
                           </div>
                        </div>
                     </div>
                  </section>
               </div>
            </div>
         </div>
      </div>
   </div>
   <div class="row">
      <div class="col-lg-12">
         <div class="" id="left-col">
            <div style="width:100%" class="row cat__core__sortable">
               <div class="col-lg-8">
                  <section class="card" order-id="card-1">
                     <div class="card-header">
                        <div class="pull-right cat__core__sortable__control">
                           <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                           <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                           <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                        </div>
                        <div class="pull-right cat__core__sortable__control">
                           <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsubmitted')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                        </div>
                     </div>
                     <h5 class="mb-0 text-black">
                        <strong>Forms Submitted</strong>
                        <small style="font-weight:bold" class="badge badge-primary">{{ description.messageChartSubmitted }}</small>
                     </h5>
               </div>
               <div class="card-block">
               <!--<div class="row">
                  <div id="chart-message-legend" class="col-lg-7">
                  
                  </div>
                  <div class="col-lg-5">
                      <canvas id="chart-message-doughnut"></canvas>
                  </div>
                  </div>-->
               <div class="row">
               <div class="col-lg-12">
               <div class="table-responsive">
               <table class="table table-borderless table-hover">
               <tbody>
               <tr *ngFor="let item of messageChartSubmitted">
               <td align="right" class="item-total-num">{{ item.total | number }} </td>
               <td align="left" class="item-total-text"> {{ item.name }}</td>
               </tr>
               </tbody>
               </table>
               </div>
               </div>
               </div>
               </div>
               </section>
            </div>
         </div>
      </div>
   </div>
   </div>
   </div>
   <!-- ======================new 30/12==================================== -->
   <div class="tab-pane" id="formsTrendsitems" role="tabcard">
      <div class="nav-tabs-horizontal">
         <ul class="nav nav-tabs filing-icons" role="tablist">
            <li class="nav-item" (click)="loadForms()">
               <a class="nav-link" id="formitemsid" href="javascript: void(0);" data-toggle="tab" data-target="#formitems" role="tab" aria-expanded="true">
               <i class="fa fa-list-alt" aria-hidden="true"></i>
               <span>Forms Summary</span></a>
            </li>
            <li class="nav-item" (click)="loadFormsTrends()">
               <a class="nav-link" id="formsTrendsN" href="javascript: void(0);" data-toggle="tab" data-target="#formsTrendsitems" role="tab" aria-expanded="true">
               <i class="fa fa-industry" aria-hidden="true"></i>
               <span>Form Engagement Trends</span></a>
            </li>
            <li class="nav-item" (click)="loadFormsReports()">
               <a class="nav-link" id="formsReportsT" href="javascript: void(0);" data-toggle="tab" data-target="#formsReportsitems" role="tab" aria-expanded="true">
               <i class="fa fa-file" aria-hidden="true"></i>
               <span>Form Reports</span></a>
            </li>
         </ul>
      </div>
      <div class="row" style="margin-bottom: 20px;" >
         <div class="col-lg-12">
         </div>
      </div>
      <div class="row">
         <div class="col-lg-12">
            <div class="" id="left-col">
               <div style="width:100%" class="row cat__core__sortable">
                  <div class="col-lg-8">
                     <section class="card" order-id="card-1">
                        <div class="card-header">
                           <div class="pull-right cat__core__sortable__control">
                              <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                              <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                              <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                           </div>
                           <div class="pull-right cat__core__sortable__control">
                              <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsTrendsitems')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                           </div>
                        </div>
                        <h5 class="mb-0 text-black">
                           <strong>Form Engagement Trends by User Type</strong>
                           <small style="font-weight:bold" class="badge badge-primary">{{ description.formsTrendsitems }}</small>
                        </h5>
                  </div>
                  <div class="card-block">
                  <div class="row">
                  <div class="col-lg-10" style="padding-bottom:5px">   
                  <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->
                  </div>   
                  </div>
                  <div class="row">
                  <div id="chart-formsTrendsitemschat-legend" class="col-lg-4">
                  </div>
                  <div class="col-lg-8">
                  <canvas id="chart-formsTrendsitemschat-pie" height="200"></canvas>
                  </div>
                  </div>
                  <!-- <div class="row form-trends"> 
                     <div class="col-lg-12">
                     <table >
                       <tbody><tr>
                         <th>Element</th>
                         <th>Week1</th>
                         <th>Week2</th><th>Week3</th><th>Week4</th><th>Week5</th><th>Week6</th>
                       </tr>
                       <tr>
                         <td 
                         
                     >Completed Patient Facing Forms</td>
                         <td>18</td>
                         <td>19</td>
                         <td>8</td>
                         <td>20</td>
                         <td>46</td>
                         <td>17</td>
                       </tr>
                       <tr>
                         <td>Completed Staff/Partner Facing Forms</td>
                         <td>1</td>
                         <td>5</td>
                         <td>2</td>
                         <td>10</td>
                         <td>11</td>
                         <td>5</td>
                         
                       </tr>
                       <tr>
                         <td>Completed Staff/Practitioner Facing Forms</td>
                         <td>1</td>
                         <td>2</td>
                         <td>0</td>
                         <td>2</td>
                         <td>5</td>
                         <td>2</td>
                         
                       </tr>
                     </tbody></table>
                     </div>
                     </div> -->
                  </div>
                  </section>
               </div>
            </div>
         </div>
      </div>
      <div class="col-lg-12">
         <div class="" id="left-col">
            <div style="width:100%" class="row cat__core__sortable">
               <div class="col-lg-8">
                  <section class="card" order-id="card-1">
                     <div class="card-header">
                        <div class="pull-right cat__core__sortable__control">
                           <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                           <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                           <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                        </div>
                        <div class="pull-right cat__core__sortable__control">
                           <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsTrendsitems1')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                        </div>
                     </div>
                     <h5 class="mb-0 text-black">
                        <strong>Patient Engagement Form Trends by Status</strong>
                        <small style="font-weight:bold" class="badge badge-primary">{{ description.formsTrendsitems1 }}</small>
                     </h5>
               </div>
               <div class="card-block">
               <div class="row">
               <div class="col-lg-10" style="padding-bottom:5px">   
               <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->
               </div>   
               </div>
               <div class="row">
               <div id="chart-formsTrendsitemschat1-legend" class="col-lg-4">
               </div>
               <div class="col-lg-8">
               <canvas id="chart-formsTrendsitemschat1-pie" height="200"></canvas>
               </div>
               </div>
               <!-- <div class="row form-trends"> 
                  <div class="col-lg-12">
                  <table >
                    <tbody><tr>
                      <th>Element</th>
                      <th>Week1</th>
                      <th>Week2</th><th>Week3</th><th>Week4</th><th>Week5</th><th>Week6</th>
                    </tr>
                    <tr>
                      <td 
                      
                  ><b>Total Patient Facing Forms</b></td>
                      <td><b>25</b></td>
                      <td><b>15</b></td>
                      <td><b>15</b></td>
                      <td><b>15</b></td>
                      <td><b>20</b></td>
                      <td><b>26</b></td>
                    </tr>
                    <tr>
                      <td>Completed or Archived together Forms</td>
                      <td>20</td>
                      <td>10</td>
                      <td>5</td>
                      <td>10</td>
                      <td>5</td>
                      <td>16</td>
                      
                    </tr>
                    <tr>
                      <td>PendingForms</td>
                      <td>5</td>
                      <td>5</td>
                      <td>10</td>
                      <td>5</td>
                      <td>15</td>
                      <td>10</td>
                      
                    </tr>
                  </tbody></table>
                  </div>
                  </div> -->
               </div>
               </section>
            </div>
         </div>
      </div>
   </div>
   <div class="col-lg-12">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-8">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsTrendsitems2')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                     <strong>Staff/Partner Facing Form Trends</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.formsTrendsitems2 }}</small>
                  </h5>
            </div>
            <div class="card-block">
            <div class="row">
            <div class="col-lg-10" style="padding-bottom:5px">   
            <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->
            </div>   
            </div>
            <div class="row">
            <div id="chart-formsTrendsitemschat2-legend" class="col-lg-4">
            </div>
            <div class="col-lg-8">
            <canvas id="chart-formsTrendsitemschat2-pie" height="200"></canvas>
            </div>
            </div>
            </div>
            </section>
         </div>
      </div>
   </div>
   </div>
   <div class="col-lg-12">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-8">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsTrendsitems3')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                     <strong>Staff-Practitioner Facing Form Trends</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.formsTrendsitems3 }}</small>
                  </h5>
            </div>
            <div class="card-block">
            <div class="row">
            <div class="col-lg-10" style="padding-bottom:5px">   
            <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->
            </div>   
            </div>
            <div class="row">
            <div id="chart-formsTrendsitemschat3-legend" class="col-lg-4">
            </div>
            <div class="col-lg-8">
            <canvas id="chart-formsTrendsitemschat3-pie" height="200"></canvas>
            </div>
            </div>
            </div>
            </section>
         </div>
      </div>
   </div>
   </div>
   <div class="col-lg-12">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-8">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsTrendsitems4')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                     <strong>Patient Facing Forms Trends Sent by Staff</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.formsTrendsitems4 }}</small>
                  </h5>
            </div>
            <div class="card-block formPatient">
            <div class="row">
            <div class="col-sm-9 staff-details-section" style="padding-bottom:5px">   
            <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->

            <div id="staffs">

               <div class="recipient-search-area">
               
                  <div class="input-dropdown">
                        <input type="text" class="form-control" id="staffsInput" autocomplete="off" name="formPatient" value=""placeholder="Search Staff"/>
                        <ul class="associate-ul recipient-ul" id="recipient-ul">
                        <li id="recipient-li"
                        class="associate-li recipient-li"
                        *ngIf="userListDisaplay && userListDisaplay.length == 0"  >
                        No item found
                        </li>
                        <li
                        id="li-{{ user.userid }}"
                        class="associate-li recipient-li"
                        [ngClass]="{
                        'li-selected': checkUserExist(user.userid)
                        }"
                        *ngFor="let user of userListDisaplay"
                        (click)="setSelectedRecipients(user, user.userid,'formPatient')"
                        >
                        {{ user.displayname }}
                        </li>
                        <li
                        class="render-manipulate"
                        *ngIf="userListDisaplay && userListDisaplay.length > 0"
                        >
                        <input
                        type="button"
                        class="recipient-select-all btn"
                        (click)="selectAllRecipients('formPatient')"
                        value="Select All"
                        />
                        <input
                        type="button"
                        class="recipient-class-clear btn"
                        (click)="closeSelectedRecipient(true,'formPatient')"
                        value="Clear All"
                        />
                        <input
                        type="button"
                        class="recipient-class-done btn"
                        *ngIf="formPatient && formPatient.length > 0"
                        (click)="doneSelectedRecipient('formPatient')"
                        value="Done"
                        />
                        </li>
                        </ul>
                  </div>

                  <div class="recipient-actions">
                     <button type="button" [disabled]="recipientLoading" id="recipient-search" (click)="checkRecipientWithTems('formPatient')" class="recipient-search-button btn btn-sm btn-primary">
                     Search
                     </button>
                     <button type="button" [disabled]="recipientLoading" id="recipient-close" (click)="closeSelectedRecipient(false,'formPatient')" class="recipient-search-button btn btn-sm btn-default recipient-close">
                     Reset
                     </button>
                  </div>

               </div>


               <div class="filter-reset-field" >
                  <div class="">

                     <div class="large-tooltip show-msg-tooltip  dashboard-tooltip formtype-tooltip" >
                        <i class="default-Scheduled icmn-info  dashboard-tooltip-icon formtype-section-tooltip"></i>
                     </div>

                     <select class="form-control select2-formsType" id="filterFormTypesTrends4" multiple>
                        <option *ngFor="let formtag of formsTtypesPatient; let i = index" [attr.data-index]="i" id="{{i}}"
                        value="{{formtag.id}}"> {{formtag.tagName}} </option>
                     </select>

                  </div>
               </div>

            </div>

            </div> 

            <div class="col-sm-3 filter-reset-section" style="" > 
            <button  type="button" class="btn btn-secondary btn-sm" (click)="applyFilter('formsTrendsitems4')"  >Filter</button> 
            <button  type="button" class="btn btn-secondary  btn-sm" (click)="resetFilter('formsTrendsitems4','formPatient')"  >Reset</button>                                                                       
            </div>
            </div>
            <div class="row" style="margin-top: 30px;">
            <div id="chart-formsTrendsitemschat4-legend" class="col-lg-4">
            </div>
            <div class="col-lg-8">
            <canvas id="chart-formsTrendsitemschat4-pie" height="200"></canvas>
            </div>
            </div>
            
            </div>
            </section>
         </div>
      </div>
   </div>
   </div>
   <!-- ======================================03-feb========================================================== -->
   <div class="col-lg-12">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-8">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsTrendsitems7')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                     <strong>Staff/Partner Facing Form Trends by Staff</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.formsTrendsitems7 }}</small>
                  </h5>
            </div>
            <div class="card-block formsPartner">
            <div class="row">
            <div class="col-sm-9  staff-details-section" style="padding-bottom:5px">   
            

            <div id="staffs">

               <div class="recipient-search-area">
               <div class="input-dropdown">
               <input type="text" class="form-control" id="staffsInput" name="formsPartner" autocomplete="off" value=""
                  placeholder="Search Staff"
                  />
               <ul class="associate-ul recipient-ul" id="recipient-ul">
               <li
                  id="recipient-li"
                  class="associate-li recipient-li"
                  *ngIf="userListDisaplay && userListDisaplay.length == 0"  >
               No item found
               </li>
               <li
               id="li-{{ user.userid }}"
               class="associate-li recipient-li"
               [ngClass]="{
               'li-selected': checkUserExist(user.userid)
               }"
               *ngFor="let user of userListDisaplay"
               (click)="setSelectedRecipients(user, user.userid,'formsPartner')"
               >
               {{ user.displayname }}
               </li>
               <li
                  class="render-manipulate"
                  *ngIf="userListDisaplay && userListDisaplay.length > 0"
                  >
               <input
                  type="button"
                  class="recipient-select-all btn"
                  (click)="selectAllRecipients('formsPartner')"
                  value="Select All"
                  />
               <input
                  type="button"
                  class="recipient-class-clear btn"
                  (click)="closeSelectedRecipient(true,'formsPartner')"
                  value="Clear All"
                  />
               <input
               type="button"
               class="recipient-class-done btn"
            *ngIf="formsPartner && formsPartner.length > 0"
               (click)="doneSelectedRecipient('formsPartner')"
               value="Done"
               />
               </li>
               </ul>
               </div>
               <div class="recipient-actions">
               <button type="button" [disabled]="recipientLoading"
               id="recipient-search"
               (click)="checkRecipientWithTems('formsPartner')"
               class="recipient-search-button btn btn-sm btn-primary"
               >
               Search
               </button>
               <button
               type="button"
               [disabled]="recipientLoading"
               id="recipient-close"
               (click)="closeSelectedRecipient(false,'formsPartner')"
               class="recipient-search-button btn btn-sm btn-default recipient-close"
               >
               Reset
               </button>
               </div>
               </div>

               <div class="filter-reset-field" >
                  <div class="">
                     <div class="large-tooltip show-msg-tooltip  dashboard-tooltip formtype-tooltip" ><i class="default-Scheduled icmn-info  dashboard-tooltip-icon formtype-section-tooltip"></i></div>   
            <select class="form-control select2-formsType" id="filterFormTypesTrends7" multiple>
                     <option *ngFor="let formtag of formsTtypesStaff; let i = index" [attr.data-index]="i" id="{{i}}"
                     value="{{formtag.id}}"> {{formtag.tagName}} </option>
                     </select>
                  </div>
               </div>
            </div>


            </div>
            <div class="col-sm-3 filter-reset-section" style="" > 
            <button  type="button" class="btn btn-secondary btn-sm" (click)="applyFilter('formsTrendsitems7')"  >Filter</button> 
            <button  type="button" class="btn btn-secondary btn-sm" (click)="resetFilter('formsTrendsitems7','formsPartner')"  >Reset</button>                                                                       
            </div>
            </div>
            <div class="row" style="margin-top: 30px;">
            <div id="chart-formsTrendsitemschat7-legend" class="col-lg-4">
            </div>
            <div class="col-lg-8">
            <canvas id="chart-formsTrendsitemschat7-pie" height="200"></canvas>
            </div>
            </div>
            </div>
            </section>
         </div>
      </div>
   </div>
   </div>
   <!-- ============================================================================================================ -->
   <div class="col-lg-12">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-8">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsTrendsitems8')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                     <strong>Staff-Practitioner Facing Form Trends by Staff</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.formsTrendsitems8 }}</small>
                  </h5>
            </div>
            <div class="card-block formsPractitioner">
            <div class="row">
            <div class="col-sm-9 staff-details-section" style="padding-bottom:5px">   
            <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->

            <div id="staffs">

            <div class="recipient-search-area">

               <div class="input-dropdown">
               <input type="text" class="form-control" id="staffsInput" name="formsPractitioner"  autocomplete="off" value=""
                  placeholder="Search Staff"
                  />
               <ul class="associate-ul recipient-ul" id="recipient-ul">
               <li
                  id="recipient-li"
                  class="associate-li recipient-li"
                  *ngIf="userListDisaplay && userListDisaplay.length == 0"  >
               No item found
               </li>
               <li
               id="li-{{ user.userid }}"
               class="associate-li recipient-li"
               [ngClass]="{
               'li-selected': checkUserExist(user.userid)
               }"
               *ngFor="let user of userListDisaplay"
               (click)="setSelectedRecipients(user, user.userid,'formsPractitioner')"
               >
               {{ user.displayname }}
               </li>
               <li
                  class="render-manipulate"
                  *ngIf="userListDisaplay && userListDisaplay.length > 0"
                  >
               <input
                  type="button"
                  class="recipient-select-all btn"
                  (click)="selectAllRecipients('formsPractitioner')"
                  value="Select All"
                  />
               <input
                  type="button"
                  class="recipient-class-clear btn"
                  (click)="closeSelectedRecipient(true,'formsPractitioner')"
                  value="Clear All"
                  />
               <input
               type="button"
               class="recipient-class-done btn"
            *ngIf="formsPractitioner && formsPractitioner.length > 0"
               (click)="doneSelectedRecipient('formsPractitioner')"
               value="Done"
               />
               </li>
               </ul>
               </div>

               <div class="recipient-actions">
                  <button type="button" [disabled]="recipientLoading"
                  id="recipient-search"
                  (click)="checkRecipientWithTems('formsPractitioner')"
                  class="recipient-search-button btn btn-sm btn-primary"
                  >
                  Search
                  </button>
                  <button
                  type="button"
                  [disabled]="recipientLoading"
                  id="recipient-close"
                  (click)="closeSelectedRecipient(false,'formsPractitioner')"
                  class="recipient-search-button btn btn-sm btn-default recipient-close"
                  >
                  Reset
                  </button>
               </div>
            </div>
            <div class="filter-reset-field" >
            <div class="">
            <div class="large-tooltip show-msg-tooltip  dashboard-tooltip formtype-tooltip" ><i class="default-Scheduled icmn-info  dashboard-tooltip-icon formtype-section-tooltip"></i></div>
            <select class="form-control select2-formsType" id="filterFormTypesTrends8" multiple>
            <option *ngFor="let formtag of formsTtypesPract; let i = index" [attr.data-index]="i" id="{{i}}"
            value="{{formtag.id}}"> {{formtag.tagName}} </option>
            </select>
            </div>
            <!-- 
               <div class="col-lg-10" style="padding-bottom:5px">    
               <strong>Select Form Types</strong>
               </div>    -->                                                               
            </div>
            </div>
            </div>
            <div class="col-sm-3 filter-reset-section"> 
            <button  type="button" class="btn btn-secondary btn-sm" (click)="applyFilter('formsTrendsitems8')"  >Filter</button> 
            <button  type="button" class="btn btn-secondary btn-sm" (click)="resetFilter('formsTrendsitems8','formsPractitioner')"  >Reset</button>                                                                       
            </div> 
            </div>
            <div class="row" style="margin-top: 30px;">
            <div id="chart-formsTrendsitemschat8-legend" class="col-lg-4">
            </div>
            <div class="col-lg-8">
            <canvas id="chart-formsTrendsitemschat8-pie" height="200"></canvas>
            </div>
            </div>
            </div>
            </section>
         </div>
      </div>
   </div>
   </div>
   <!-- =========================================03 feb============================================================== -->
   <div class="col-lg-12">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-8">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsTrendsitems6')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                     <strong>Form Trends by Form Type</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.formsTrendsitems6 }}</small>
                  </h5>
            </div>
            <div class="card-block">
            <div class="row">
            <div class="col-lg-10" style="padding-bottom:5px">   
            <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->
            <div class="row">
            <div class="col-md-8">
            <div class="large-tooltip show-msg-tooltip  dashboard-tooltip" ><i class="default-Scheduled icmn-info  dashboard-tooltip-icon"></i></div>
            <select class="form-control select2-formsType" id="filterFormTypesTrends1" multiple>
            <option *ngFor="let formtag of formsTtypes; let i = index" [attr.data-index]="i" id="{{i}}"
            value="{{formtag.id}}"> {{formtag.tagName}} </option>
            </select>
            </div>
            <!-- 
               <div class="col-lg-10" style="padding-bottom:5px">    
                   <strong>Select Form Types</strong>
               </div>    -->                                                               
            <div class="col-md-4 align-self-end" > 
            <button  type="button" class="btn btn-secondary " (click)="applyFilterFormType('formsTrendsitems6')"  >Filter</button> 
            <button  type="button" class="btn btn-secondary " (click)="resetFilter('formsTrendsitems6')"  >Reset</button>                                                                       
            </div>
            </div>
            </div>   
            </div>
            <div class="row" style="margin-top: 30px;">
            <div id="chart-formsTrendsitemschat6-legend" class="col-lg-4">
            </div>
            <div class="col-lg-8">
            <canvas id="chart-formsTrendsitemschat6-pie" height="200"></canvas>
            </div>
            </div>
            <!-- <div class="row form-trends"> 
               <table >
                 <tbody><tr>
                   <th>Element</th>
                   <th>Week1</th>
                   <th>Week2</th><th>Week3</th><th>Week4</th>
                 </tr>
                 <tr>
                   <td 
                   
               ><b>Total</b></td>
                   <td><b>16</b></td>
                   <td><b>19</b></td>
                   <td><b>24</b></td>
                   <td><b>26</b></td>
                  
                 </tr>
               
               
                 <tr>
                       <td 
                       
                   >Staff PHI Forms</td>
                       <td>2</td>
                       <td>5</td>
                       <td>7</td>
                       <td>6</td>
                      
                     </tr>
                 <tr>
                   <td>Partner PHI Forms</td>
                   <td>6</td>
                   <td>4</td>
                   <td>6</td>
                   <td>8</td>
                   
                   
                 </tr>
                 <tr>
                   <td>Staff Nursing Notes</td>
                   <td>3</td>
                   <td>5</td>
                   <td>4</td>
                   <td>6</td>
                   
                   
                 </tr>
                 <tr>
                       <td>Patient Refill - Progress Note</td>
                       <td>5</td>
                       <td>6</td>
                       <td>5</td>
                       <td>6</td>
                       
                       
                     </tr>
               </tbody></table>
               </div> -->
            </div>
            </section>
         </div>
      </div>
   </div>
   </div>
   <div class="col-lg-12">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-8">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsTrendsitems5')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                                                        <strong>Form Trends by Form Name</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.formsTrendsitems5 }}</small>
                  </h5>
            </div>
            <div class="card-block">
            <div class="row">
            <div class="col-lg-10" style="padding-bottom:5px">   
            <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->
            <div class="row">
            <div class="col-md-8"> 
                                                                            <div class="large-tooltip show-msg-tooltip  dashboard-tooltip" ><i class="default-Scheduled-title icmn-info  dashboard-tooltip-icon"></i></div>
            <select class="form-control select2-formsType" id="filterFormTypesTrends" multiple>
            <option *ngFor="let form of this.formsTrends; let i = index" [attr.data-index]="i" id="{{i}}" value="{{form.id}}"> {{form.formName}} </option>                                                                    
            </select>
            </div>
            <!-- 
               <div class="col-lg-10" style="padding-bottom:5px">    
                   <strong>Select Form Types</strong>
               </div>    -->                                                               
            <div class="col-md-4 align-self-end" > 
            <button  type="button" class="btn btn-secondary " (click)="applyFilterFormTitle('formsTrendsitems5')"  >Filter</button> 
            <button  type="button" class="btn btn-secondary " (click)="resetFilter('formsTrendsitems5')"  >Reset</button>                                                                       
            </div>
            </div>
            </div>   
            </div>
            <div class="row" style="margin-top: 30px;">
            <div id="chart-formsTrendsitemschat5-legend" class="col-lg-4">
            </div>
            <div class="col-lg-8">
            <canvas id="chart-formsTrendsitemschat5-pie" height="200"></canvas>
            </div>
            </div>
            <!-- <div class="row form-trends"> 
               <table >
                 <tbody><tr>
                   <th>Element</th>
                   <th>Week1</th>
                   <th>Week2</th><th>Week3</th><th>Week4</th>
                 </tr>
                 <tr>
                   <td 
                   
               ><b>Total</b></td>
                   <td><b>16</b></td>
                   <td><b>19</b></td>
                   <td><b>24</b></td>
                   <td><b>26</b></td>
                  
                 </tr>
               
               
                 <tr>
                       <td 
                       
                   >Antibiotic Refill</td>
                       <td>2</td>
                       <td>5</td>
                       <td>7</td>
                       <td>6</td>
                      
                     </tr>
                 <tr>
                   <td>Advance PHI</td>
                   <td>6</td>
                   <td>4</td>
                   <td>6</td>
                   <td>8</td>
                   
                   
                 </tr>
                 <tr>
                   <td>Nursing Visit Note V1</td>
                   <td>3</td>
                   <td>5</td>
                   <td>4</td>
                   <td>6</td>
                   
                   
                 </tr>
                 <tr>
                       <td>Nursing Visit Note V2</td>
                       <td>5</td>
                       <td>6</td>
                       <td>5</td>
                       <td>6</td>
                       
                       
                     </tr>
               </tbody></table>
               </div> -->
            </div>
            </section>
         </div>
      </div>
   </div>
   </div>
   </div>
   </div>
   <!-- ======================new 30/12==================================== -->
   <!--  ===========================================form Reports======================================== -->
   <div class="tab-pane" id="formsReportsitems" role="tabcard">
      <div class="nav-tabs-horizontal">
         <ul class="nav nav-tabs filing-icons" role="tablist">
            <li class="nav-item" (click)="loadForms()">
               <a class="nav-link" id="formitemsid" href="javascript: void(0);" data-toggle="tab" data-target="#formitems" role="tab" aria-expanded="true">
               <i class="fa fa-list-alt" aria-hidden="true"></i>
               <span>Forms Summary</span></a>
            </li>
            <li class="nav-item" (click)="loadFormsTrends()">
               <a class="nav-link" id="formsTrendsN" href="javascript: void(0);" data-toggle="tab" data-target="#formsTrendsitems" role="tab" aria-expanded="true">
               <i class="fa fa-industry" aria-hidden="true"></i>
               <span>Form Engagement Trends</span></a>
            </li>
            <li class="nav-item" (click)="loadFormsReports()">
               <a class="nav-link" id="formsReportsR" href="javascript: void(0);" data-toggle="tab" data-target="#formsReportsitems" role="tab" aria-expanded="true">
               <i class="fa fa-file" aria-hidden="true"></i>
               <span>Form Reports</span></a>
            </li>
         </ul>
      </div>
      <div class="row dashboard-report" >
         <div class="col-lg-4">Staff Facing Forms Submitted</div>
         <div class="col-lg-4">
            <button type="button" class="btn btn-secondary " (click)="generateCSV('staffFacing','Staff Facing Forms Submitted')">Generate CSV</button>
               <span *ngIf="downloadLoader" role="status" aria-hidden="true" class="fa fa-spinner fa-spin fa-2x" ></span>

         </div>
      </div>
      <div class="row dashboard-report">
         <div class="col-lg-4">Patient Forms Sent by Staff </div>
         <div class="col-lg-4">
            <button type="button" class="btn btn-secondary " (click)="generateCSV('patientSent','Patient Forms Sent by Staff')">Generate CSV</button>
         </div>
      </div>
      <div class="row dashboard-report">
         <div class="col-lg-4">Forms Received by Patients </div>
         <div class="col-lg-4">
            <button type="button" class="btn btn-secondary " (click)="generateCSV('patientReceived','Forms Received by Patients')">Generate CSV</button>                               
         </div>
      </div>
      <div class="row dashboard-report">
         <div class="col-lg-4">Patient Facing Forms Submitted </div>
         <div class="col-lg-4">
            <button type="button" class="btn btn-secondary " (click)="generateCSV('patientFacing','Patient Facing Forms Submitted')">Generate CSV</button>
         </div>
      </div>

      <div class="row dashboard-report">
         <div class="col-lg-4">All Forms Worklist 
            <a class="icmn-info" data-toggle="tooltip" title="This Report provides the most recent 1,000 Forms in the All Forms Worklist(s) of the current Tenant or Selected Branch(es)."></a>
         </div>
         <div class="col-lg-4">
            <button [disabled]="downloadLoader" type="button" class="btn btn-secondary " (click)=generateCSVfrontEndForms()>Generate CSV</button>
         </div>
      </div>
   </div>
   <!-- ===========================================form Reports End ======================================== -->
   <!-- ======================================chat==================================== -->
   <div class="tab-pane" id="chatitems" role="tabcard">
      <dashboard-message-center [loadMessageActivityData]="loadMessageActivityData" [loadMessageActivitySiteData]="loadMessageActivitySiteData"></dashboard-message-center>
      <!--<div class="row">
         <div id="chart-message-legend" class="col-lg-7">
         
         </div>
         <div class="col-lg-5">
             <canvas id="chart-message-doughnut"></canvas>
         </div>
         </div>-->
   </div>
   <!-- ======================================whologgedin==================================== -->
   <div class="tab-pane" id="whologgedin" role="tabcard">
      <div class="row">
         <div class="col-lg-12">
            <div class="tab-content">
               <table class="table table-hover" id="dtOnlineUsers" width="100%"></table>
            </div>
         </div>
      </div>
   </div>
   <!-- ======================================Document Center==================================== -->
   <div class="tab-pane" id="sigantureitems" role="tabcard">
                        <dashboard-signature-request [siteIdDetails]="siteIdDetails" [loadDocumentActivityData]="loadDocumentActivityData"></dashboard-signature-request>
                        <!-- <div class="row">
                            <div class="col-lg-12">
                                <div class="" id="left-col">
                                    <div style="width:100%" class="row cat__core__sortable">
                                        <div class="col-lg-6">
                                            <section class="card" order-id="card-1">
                                                <div class="card-header">
                                                    <div class="pull-right cat__core__sortable__control">
                                                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                                    </div>
                                                    <div class="pull-right cat__core__sortable__control">
                                                        <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'sigantureitems')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                                                    </div>
                                                    <h5 class="mb-0 text-black">
                                                        <strong>Signature Requests</strong>
                                                        <small style="font-weight:bold" class="badge badge-primary">{{ description.sigantureitems }}</small>
                                                    </h5>
                                                </div>
                                                <div class="card-block">
                                                        <div class="row">
                                                                <div class="col-lg-10" style="padding-bottom:5px">   
                                                                    <strong>{{total.signatureRequests}} Total Signature Requests</strong> 
                                                                </div>   
                                                            </div>
                                                    <div class="row">
                                                        <div id="chart-sigantureitemschat-legend" class="col-lg-4">

                                                        </div>
                                                        <div class="col-lg-8">
                                                            <canvas id="chart-sigantureitemschat-pie"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                            </section>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div> -->


                    </div>

                    <!-- ============================================================== -->
   <!-- ======================================Forms Trends==================================== -->
   <div class="tab-pane" id="enrollTrendsitems" role="tabcard">
      <div class="nav-tabs-horizontal">
         <ul class="nav nav-tabs filing-icons" role="tablist">
            <li class="nav-item" (click)="loadEnrollments()">
               <a class="nav-link" id="newitemsid" href="javascript: void(0);" data-toggle="tab" data-target="#newitems" role="tab" aria-expanded="true">
               <i class="fa fa-bell" aria-hidden="true"></i>
               <span>Enrollments Summary</span></a>
            </li>
            <li class="nav-item" (click)="loadEnrollTrends()">
               <a class="nav-link" id="enrollTrendsN" href="javascript: void(0);" data-toggle="tab" data-target="#enrollTrendsitems" role="tab" aria-expanded="true">
               <i class="fa fa-industry" aria-hidden="true"></i>
               <span>Enrollment Trends</span></a>
            </li>
            <li class="nav-item" (click)="loadEnrollmentsReports()">
               <a class="nav-link" id="EnrollmentsReports" href="javascript: void(0);" data-toggle="tab" data-target="#enrollmentsReports" role="tab" aria-expanded="true">
               <i class="fa fa-file" aria-hidden="true"></i>
               <span>Enrollment Reports</span></a>
            </li>
         </ul>
      </div>
      <div class="row">
         <div class="col-lg-8">
            <div class="" id="left-col">
               <div style="width:100% ;margin-top: 30px;" class="row cat__core__sortable">
                  <div class="col-lg-12">
                  </div>
               </div>
            </div>
         </div>
      </div>
      <div class="row">
         <div class="col-lg-12">
            <div class="" id="left-col">
               <div style="width:100%" class="row cat__core__sortable">
                  <div class="col-lg-8">
                     <section class="card" order-id="card-1">
                        <div class="card-header">
                           <div class="pull-right cat__core__sortable__control">
                              <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                              <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                              <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                           </div>
                           <div class="pull-right cat__core__sortable__control">
                              <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'enrollTrendsitems')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                           </div>
                        </div>
                        <h5 class="mb-0 text-black">
                           <strong>Enrollments Trends</strong>
                           <small style="font-weight:bold" class="badge badge-primary">{{ description.enrollTrendsitems }}</small>
                        </h5>
                  </div>
                  <div class="card-block">
                  <div class="row">
                  <div class="col-lg-10" style="padding-bottom:5px">   
                  <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->
                  </div>   
                  </div>
                  <div class="row">
                  <div id="chart-enrollTrendsitemschat-legend" class="col-lg-4">
                  </div>
                  <div class="col-lg-8">
                  <canvas id="chart-enrollTrendsitemschat-pie" height="200"></canvas>
                  </div>
                  </div>
                  </div>
                  </section>
               </div>
            </div>
         </div>
      </div>
      <div class="col-lg-12">
         <div class="" id="left-col">
            <div style="width:100%" class="row cat__core__sortable">
               <div class="col-lg-8">
                  <section class="card" order-id="card-1">
                     <div class="card-header">
                        <div class="pull-right cat__core__sortable__control">
                           <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                           <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                           <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                        </div>
                        <div class="pull-right cat__core__sortable__control">
                           <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'enrollTrendsitems1')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                        </div>
                     </div>
                     <h5 class="mb-0 text-black">
                        <strong>Total Patient Enrollments Trends</strong>
                        <small style="font-weight:bold" class="badge badge-primary">{{ description.enrollTrendsitems1 }}</small>
                     </h5>
               </div>
               <div class="card-block">
               <div class="row">
               <div class="col-lg-10" style="padding-bottom:5px">   
               <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->
               </div>   
               </div>
               <div class="row">
               <div id="chart-enrollTrendsitemschat1-legend" class="col-lg-4">
               </div>
               <div class="col-lg-8">
               <canvas id="chart-enrollTrendsitemschat1-pie" height="200"></canvas>
               </div>
               </div>
               </div>
               </section>
            </div>
         </div>
      </div>
   </div>
   <div class="col-lg-12">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-8">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'enrollTrendsitems2')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                     <strong>Total Staff Enrollments Trends</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.enrollTrendsitems2 }}</small>
                  </h5>
            </div>
            <div class="card-block">
            <div class="row">
            <div class="col-lg-10" style="padding-bottom:5px">   
            <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->
            </div>   
            </div>
            <div class="row">
            <div id="chart-enrollTrendsitemschat2-legend" class="col-lg-4">
            </div>
            <div class="col-lg-8">
            <canvas id="chart-enrollTrendsitemschat2-pie" height="200"></canvas>
            </div>
            </div>
            </div>
            </section>
         </div>
      </div>
   </div>
   </div>
   <div class="col-lg-12">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-8">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'enrollTrendsitems3')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                     <strong>Total Partner Enrollments Trends</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.enrollTrendsitems3 }}</small>
                  </h5>
            </div>
            <div class="card-block">
            <div class="row">
            <div class="col-lg-10" style="padding-bottom:5px">   
            <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->
            </div>   
            </div>
            <div class="row">
            <div id="chart-enrollTrendsitemschat3-legend" class="col-lg-4">
            </div>
            <div class="col-lg-8">
            <canvas id="chart-enrollTrendsitemschat3-pie" height="200"></canvas>
            </div>
            </div>
            </div>
            </section>
         </div>
      </div>
   </div>
   </div>
   <div class="col-lg-12">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-8">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'enrollTrendsitems4')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                     <strong>Total Patient Enrollments Trends by Staff</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.enrollTrendsitems4 }}</small>
                  </h5>
            </div>
            <div class="card-block enrollmentFilter" >
            <div class="row">
            <div class="col-lg-10" style="padding-bottom:5px">   
            <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->
            </div>   
            </div>
            <div class="row" style="margin-top: 10px;margin-bottom: 10px;" *ngIf="selectedtab  === 'enrollTrends'" >  
            <div class="col-md-9">
            <div id="staffs" class="boarderblock">
            <div class="recipient-search-area">
            <div class="input-dropdown">
            <input type="text" class="form-control" id="staffsInput" name="enrollmentFilter"  autocomplete="off" value=""
               placeholder="Search Staff"
               />
            <ul class="associate-ul recipient-ul" id="recipient-ul">
            <li
               id="recipient-li"
               class="associate-li recipient-li"
               *ngIf="userListDisaplay && userListDisaplay.length == 0"  >
            No item found
            </li>
            <li
            id="li-{{ user.userid }}"
            class="associate-li recipient-li"
            [ngClass]="{
            'li-selected': checkUserExist(user.name)
            }"
            *ngFor="let user of userListDisaplay"
            (click)="setSelectedRecipients(user,user.name,'enrollmentFilter')"
            >
            {{ user.displayname }}
            </li>
            <li
               class="render-manipulate"
               *ngIf="userListDisaplay && userListDisaplay.length > 0"
               >
            <input
               type="button"
               class="recipient-select-all btn"
               (click)="selectAllRecipientEmails('enrollmentFilter')"
               value="Select All"
               />
            <input
               type="button"
               class="recipient-class-clear btn"
               (click)="closeSelectedRecipient(true)"
               value="Clear All"
               />
            <input
            type="button"
            class="recipient-class-done btn"
            *ngIf="selectedRecipients && selectedRecipients.length > 0"
            (click)="doneSelectedRecipient('enrollmentFilter')"
            value="Done"
            />
            </li>
            </ul>
            </div>
            <div >
            <button type="button" [disabled]="recipientLoading"
            id="recipient-search"
            (click)="checkRecipientWithTems('enrollmentFilter')"
            class="recipient-search-button btn btn-sm btn-primary"
            >
            Search
            </button>
            <button
            type="button"
            [disabled]="recipientLoading"
            id="recipient-close"
            (click)="closeSelectedRecipient('enrollmentFilter')"
            class="recipient-search-button btn btn-sm btn-default recipient-close"
            >
            Reset
            </button>
            </div>
            </div>
            </div>
            </div> 
            <div class="col-md-3" style="margin-top: 15px;margin-bottom: 2px;" > 
            <button  type="button" class="btn btn-secondary btn-sm" (click)="applyFilter('enrollTrendsitems4')"  >Filter</button> 
            <button  type="button" class="btn btn-secondary btn-sm" (click)="resetEnrollFilter('enrollmentFilter','enrollTrendsitems4')"  >Reset</button>                                                                       
            </div>
            </div>
            <div class="row">
            <div id="chart-enrollTrendsitemschat4-legend" class="col-lg-4">
            </div>
            <div class="col-lg-8">
            <canvas id="chart-enrollTrendsitemschat4-pie" height="200"></canvas>
            </div>
            </div>
            </div>
            </section>
         </div>
      </div>
   </div>
   </div>
   </div>
   </div>
   <!-- ======================================Forms Trends==================================== -->
   <div class="tab-pane" id="formsTrendsitems2222" role="tabcard">
      <div class="row">
         <div class="col-lg-12">
            <div class="" id="left-col">
               <div style="width:100%" class="row cat__core__sortable">
                  <div class="col-lg-8">
                     <section class="card" order-id="card-1">
                        <div class="card-header">
                           <div class="pull-right cat__core__sortable__control">
                              <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                              <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                              <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                           </div>
                           <div class="pull-right cat__core__sortable__control">
                              <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsTrendsitems')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                           </div>
                        </div>
                        <h5 class="mb-0 text-black">
                           <strong>Forms Trends</strong>
                           <small style="font-weight:bold" class="badge badge-primary">{{ description.formsTrendsitems }}</small>
                        </h5>
                  </div>
                  <div class="card-block">
                  <div class="row">
                  <div class="col-lg-10" style="padding-bottom:5px">   
                  <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->
                  </div>   
                  </div>
                  <div class="row">
                  <div id="chart-formsTrendsitemschat-legend" class="col-lg-4">
                  </div>
                  <div class="col-lg-8">
                  <canvas id="chart-formsTrendsitemschat-pie" height="200"></canvas>
                  </div>
                  </div>
                  </div>
                  </section>
               </div>
            </div>
         </div>
      </div>
      <div class="col-lg-12">
         <div class="" id="left-col">
            <div style="width:100%" class="row cat__core__sortable">
               <div class="col-lg-8">
                  <section class="card" order-id="card-1">
                     <div class="card-header">
                        <div class="pull-right cat__core__sortable__control">
                           <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                           <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                           <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                        </div>
                        <div class="pull-right cat__core__sortable__control">
                           <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsTrendsitems1')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                        </div>
                     </div>
                     <h5 class="mb-0 text-black">
                        <strong>Patient Engagement Form Trends by Status</strong>
                        <small style="font-weight:bold" class="badge badge-primary">{{ description.formsTrendsitems1 }}</small>
                     </h5>
               </div>
               <div class="card-block">
               <div class="row">
               <div class="col-lg-10" style="padding-bottom:5px">   
               <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->
               </div>   
               </div>
               <div class="row">
               <div id="chart-formsTrendsitemschat1-legend" class="col-lg-4">
               </div>
               <div class="col-lg-8">
               <canvas id="chart-formsTrendsitemschat1-pie" height="200"></canvas>
               </div>
               </div>
               <!-- <div class="row"> 
                  <table >
                    <tbody><tr>
                      <th>Element</th>
                      <th>Week1</th>
                      <th>Week2</th><th>Week3</th><th>Week4</th><th>Week5</th><th>Week6</th>
                    </tr>
                    <tr>
                      <td 
                      
                  ><b>Total Patient Facing Forms</b></td>
                      <td><b>25</b></td>
                      <td><b>15</b></td>
                      <td><b>15</b></td>
                      <td><b>15</b></td>
                      <td><b>20</b></td>
                      <td><b>26</b></td>
                    </tr>
                    <tr>
                      <td>Completed or Archived together Forms</td>
                      <td>20</td>
                      <td>10</td>
                      <td>5</td>
                      <td>10</td>
                      <td>5</td>
                      <td>16</td>
                      
                    </tr>
                    <tr>
                      <td>PendingForms</td>
                      <td>5</td>
                      <td>5</td>
                      <td>10</td>
                      <td>5</td>
                      <td>15</td>
                      <td>10</td>
                      
                    </tr>
                  </tbody></table>
                  </div>
                  <div>
                  <table >
                    <tbody><tr>
                      <th>Element</th>
                      <th>Week1</th>
                      <th>Week2</th><th>Week3</th><th>Week4</th><th>Week5</th><th>Week6</th>
                    </tr>
                    <tr>
                      <td 
                      
                  ><b>Total Patient Facing Forms</b></td>
                      <td><b>25</b></td>
                      <td><b>15</b></td>
                      <td><b>15</b></td>
                      <td><b>15</b></td>
                      <td><b>20</b></td>
                      <td><b>26</b></td>
                    </tr>
                    <tr>
                      <td>Completed or Archived together Forms</td>
                      <td>20</td>
                      <td>10</td>
                      <td>5</td>
                      <td>10</td>
                      <td>5</td>
                      <td>16</td>
                      
                    </tr>
                    <tr>
                      <td>PendingForms</td>
                      <td>5</td>
                      <td>5</td>
                      <td>10</td>
                      <td>5</td>
                      <td>15</td>
                      <td>10</td>
                      
                    </tr>
                  </tbody></table>
                  </div> -->
               </div>
               </section>
            </div>
         </div>
      </div>
   </div>
   <div class="col-lg-12">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-8">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsTrendsitems2')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                     <strong>Staff/Partner Facing Form Trends</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.formsTrendsitems2 }}</small>
                  </h5>
            </div>
            <div class="card-block">
            <div class="row">
            <div class="col-lg-10" style="padding-bottom:5px">   
            <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->
            </div>   
            </div>
            <div class="row">
            <div id="chart-formsTrendsitemschat2-legend" class="col-lg-4">
            </div>
            <div class="col-lg-8">
            <canvas id="chart-formsTrendsitemschat2-pie" height="200"></canvas>
            </div>
            </div>
            </div>
            </section>
         </div>
      </div>
   </div>
   </div>
   <div class="col-lg-12">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-8">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsTrendsitems3')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                     <strong>Staff-Practitioner Facing Form Trends</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.formsTrendsitems3 }}</small>
                  </h5>
            </div>
            <div class="card-block">
            <div class="row">
            <div class="col-lg-10" style="padding-bottom:5px">   
            <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->
            </div>   
            </div>
            <div class="row">
            <div id="chart-formsTrendsitemschat3-legend" class="col-lg-4">
            </div>
            <div class="col-lg-8">
            <canvas id="chart-formsTrendsitemschat3-pie" height="200"></canvas>
            </div>
            </div>
            </div>
            </section>
         </div>
      </div>
   </div>
   </div>
   <div class="col-lg-12">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-8">
               <section class="card" order-id="card-1">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="yesrOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'formsTrendsitems3')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div>
                  <h5 class="mb-0 text-black">
                     <strong>Total Patient Facing Forms Trending by Week per Staff member</strong>
                     <small style="font-weight:bold" class="badge badge-primary">{{ description.formsTrendsitems4 }}</small>
                  </h5>
            </div>
            <div class="card-block">
            <div class="row">
            <div class="col-lg-10" style="padding-bottom:5px">   
            <!-- <strong>{{total.signatureRequests}} Total Signature Requests</strong>  -->
            </div>   
            </div>
            <div class="row">
            <div id="chart-formsTrendsitemschat4-legend" class="col-lg-4">
            </div>
            <div class="col-lg-8">
            <canvas id="chart-formsTrendsitemschat4-pie" height="200"></canvas>
            </div>
            </div>
            </div>
            </section>
         </div>
      </div>
   </div>
   </div>
   </div>
   </div>
 <!--   ================================Integration Center====================== -->
 <div class="tab-pane" id="integrationitems" role="tabcard">
   <div class="row">    
   </div>
   <div class="nav-tabs-horizontal"  >
      <ul class="nav nav-tabs filing-icons" role="tablist">
         <li class="nav-item" (click)="loadIntegration()">
            <a class="nav-link" id="integration" href="javascript: void(0);" data-toggle="tab" data-target="#integrationitems" role="tab" aria-expanded="true">
            <i class="fa fa-address-card" aria-hidden="true"></i>
            <span>Integration Center Summary</span></a>
         </li>
         <!-- <li class="nav-item" (click)="loadIntegrationTrends()">
            <a class="nav-link" id="integrationTrends" href="javascript: void(0);" data-toggle="tab" data-target="#integrationTrendsitems" role="tab" aria-expanded="true">
            <i class="fa fa-industry" aria-hidden="true"></i>
            <span>Integration Trends</span><sup>Beta</sup></a>
         </li>-->
         <li class="nav-item" (click)="loadIntegrationReports()">
            <a class="nav-link" id="IntegrationReports" href="javascript: void(0);" data-toggle="tab" data-target="#integrationsReports" role="tab" aria-expanded="true">
            <i class="fa fa-file" aria-hidden="true"></i>
            <span>Integration Center Reports</span></a>
         </li> 
      </ul>
   </div>
   <div class="row">
      <div class="col-lg-8">
         <div class="" id="left-col">
            <div style="width:100% ;margin-top: 30px;" class="row cat__core__sortable">
               <div class="col-lg-12">
               </div>
            </div>
         </div>
      </div>
   </div>
   <div class="row">
      <div class="col-lg-6">
         <div class="" id="left-col">
            <div style="width:100%" class="row cat__core__sortable">
               <div class="col-lg-12">
                  <section class="card" order-id="card-1" [hidden]="total.enableIntegation ==0">
                     <div class="card-header">
                        <div class="pull-right cat__core__sortable__control">
                           <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                           <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                           <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                        </div>
                        <!-- <div class="pull-right cat__core__sortable__control">
                           <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'totalintegration')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                        </div>
                     </div> -->
                     <h5 class="mb-0 text-black">
                        <strong>Total Patients Missing MRN/Pt Id</strong>                        
                     </h5>
               </div>
               <div class="card-block">
               <div class="row">
               <div class="col-lg-10" style="padding-bottom:5px">    
               <strong>{{total.integrationPatients}} Total Patients by MRN/Pt Id Status</strong>
               </div>    
               </div>
               <div class="row">
               <div id="chart-totalPatientIntegration-legend" class="col-lg-4">
               </div>
               <div class="col-lg-8">
               <canvas id="chart-totalPatientIntegration-pie" style="height: 200px!important;"></canvas>
               <!-- <canvas id="canvas" width=300 height=300></canvas> -->
               </div>
               </div>
               </div>
               </section>   
               <section class="card" order-id="card-1"  [hidden]="total.enableIntegation !=0">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
                     <!-- <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'totalintegration')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div> -->                  
            </div>
            <div class="card-block" style="margin-top: 60px;margin-bottom: 65px;text-align: center">
            <div class="row">
            <div class="col-lg-10" style="padding-bottom:5px">    
            <strong>External Integration not enabled for this tenant </strong>
            </div>    
            </div>
            
            </div>
            </section> 
            </div>
         </div>
      </div>
   </div>
   <div class="col-lg-6">
      <div class="" id="left-col">
         <div style="width:100%" class="row cat__core__sortable">
            <div class="col-lg-12">
               <section class="card" order-id="card-1" [hidden]="total.enableIntegation ==0">
                  <div class="card-header">
                     <div class="pull-right cat__core__sortable__control">
                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                     </div>
<!--                      <div class="pull-right cat__core__sortable__control">
                        <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'totalinvite')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                     </div>
                  </div> -->
                  <h5 class="mb-0 text-black">
                     <strong>Total Staff Missing Staff Id</strong>                    
                  </h5>
            </div>
            <div class="card-block">
            <div class="row">
            <div class="col-lg-10" style="padding-bottom:5px">    
            <strong>{{total.integrationStaffs}} Total Staff by Staff Id Status</strong>
            </div>    
            </div>
            <div class="row">
            <div id="chart-totalStaffIntegration-legend" class="col-lg-4">
            </div>
            <div class="col-lg-8">
            <canvas id="chart-totalStaffIntegration-pie" style="height: 200px!important;"></canvas>
            <!-- <canvas id="canvas" width=300 height=300></canvas> -->
            </div>
            </div>
            </div>
            </section>
            <section class="card" order-id="card-1"  [hidden]="total.enableIntegation !=0">
               <div class="card-header">
                  <div class="pull-right cat__core__sortable__control">
                     <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                     <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                     <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                  </div>
                  <!-- <div class="pull-right cat__core__sortable__control">
                     <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'totalintegration')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                  </div>
               </div> -->                  
         </div>
         <div class="card-block" style="margin-top: 60px;margin-bottom: 65px;text-align: center">
         <div class="row">
         <div class="col-lg-10" style="padding-bottom:5px">    
         <strong>External Integration not enabled for this tenant </strong>
         </div>    
         </div>
         
         </div>
         </section>
         </div>
      </div>
   </div>
</div>
</div>

</div>
<div class="tab-pane" id="integrationsReports" role="tabcard">
   <div class="nav-tabs-horizontal"  >
      <ul class="nav nav-tabs filing-icons" role="tablist">
         <li class="nav-item" (click)="loadIntegration()">
            <a class="nav-link" id="integrationR" href="javascript: void(0);" data-toggle="tab" data-target="#integrationitems" role="tab" aria-expanded="true">
            <i class="fa fa-address-card" aria-hidden="true"></i>
            <span>Integration Center Summary</span></a>
         </li>
         <!-- <li class="nav-item" (click)="loadIntegrationTrends()">
            <a class="nav-link" id="integrationTrends" href="javascript: void(0);" data-toggle="tab" data-target="#integrationTrendsitems" role="tab" aria-expanded="true">
            <i class="fa fa-industry" aria-hidden="true"></i>
            <span>Integration Trends</span></a>
         </li>-->
         <li class="nav-item" (click)="loadIntegrationReports()">
            <a class="nav-link" id="IntegrationReportsR" href="javascript: void(0);" data-toggle="tab" data-target="#integrationsReports" role="tab" aria-expanded="true">
            <i class="fa fa-file" aria-hidden="true"></i>
            <span>Integration Center Reports</span></a>
         </li> 
      </ul>
   </div>
   <div class="row dashboard-report" >
      <div class="col-lg-4">Patients Missing Patient MRN/Pt Id</div>
      <div class="col-lg-4">
         <button type="button" class="btn btn-secondary " (click)="generateCSV('','Patients Missing Patient MRN/Pt Id','patientIntegrationReports')">Generate CSV</button>
            <span *ngIf="downloadLoader" role="status" aria-hidden="true" class="fa fa-spinner fa-spin fa-2x" ></span>

      </div>
   </div>
   <div class="row dashboard-report" >
      <div class="col-lg-4">Staff Missing Staff Id</div>
      <div class="col-lg-4">
         <button type="button" class="btn btn-secondary " (click)="generateCSV('','Staff Missing Staff Id','staffIntegrationReports')">Generate CSV</button>
            <span *ngIf="downloadLoader2" role="status" aria-hidden="true" class="fa fa-spinner fa-spin fa-2x" ></span>

      </div>
   </div>
 
</div>
 <!--   ================================================================= -->
   <!-- ============================================================== -->
   <!--<div class="tab-pane" id="notification1" role="tabcard">
      <div class="row">
          <div class="col-lg-3">
              <div class="cat__core__widget">
                  <div class="cat__core__step cat__core__step--primary">
                      <span class="cat__core__step__digit">
                          <i class="fa fa-bell"></i>
                      </span>
                      <div class="cat__core__step__desc">
                          <span class="cat__core__step__title">Notification Enabled</span>
                          <p>Total: {{ total.notification }}</p>
                      </div>
                  </div>
              </div>
          </div>
          <div class="col-lg-3">
              <div class="cat__core__widget">
                  <div class="cat__core__step cat__core__step--success">
                      <span class="cat__core__step__digit">
                          <i class="fa fa-globe"></i>
                      </span>
                      <div class="cat__core__step__desc">
                          <span class="cat__core__step__title">Location Enabled</span>
                          <p>Total: {{ total.location }}</p>
                      </div>
                  </div>
              </div>
          </div>
      </div>
      <div class="row">
          <div class="col-lg-12">
              <div class="" id="left-col">
                  <div style="width:100%" class="row cat__core__sortable">
                      <div class="col-lg-6">
                          <section class="card" order-id="card-1">
                              <div class="card-header">
                                  <div class="pull-right cat__core__sortable__control">
                                      <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                      <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                      <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                  </div>
                                  <div class="pull-right cat__core__sortable__control">
                                      <div class="load-more" daterangepicker [options]="options" class="load-more" (applyDaterangepicker)="selectedDate($event,'notification')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                                  </div>
                                  <h5 class="mb-0 text-black">
                                      <strong>Notification and Location Settings Usage</strong>
                                      <small style="font-weight:bold" class="badge badge-primary">{{ description.notification }}</small>
                                  </h5>
                              </div>
                              <div class="card-block">
                                  <div class="row">
                                      <div id="chart-notification-legend" class="col-lg-7">
      
                                      </div>
                                      <div class="col-lg-5">
                                          <canvas id="chart-notification-pie"></canvas>
                                      </div>
                                  </div>
                              </div>
                          </section>
                      </div>
                  </div>
              </div>
          </div>
      </div>
      </div>-->
   </div>
   </div>
   </div>
   </div>
</section>
<div class="modal fade forward-modal" id="branchListModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
   <div class="modal-dialog modal-md">
      <div class="modal-content">
         <div class="modal-header">
            <h4 class="modal-title" id="exampleModalLabel">Filter Branch</h4>
            <button type="button" class="close" (click)="closeBranchModal()" aria-label="Close">
            <span aria-hidden="true">&times;</span>
            </button>
         </div>
         <div class="modal-body">
            <form>
               <ul class="treeview treeview-section-ui" style="overflow-y: unset !important;" role="menu" id="crossTenant">
                  <li style="background: none;border: none;" ><input type="checkbox"   name="alltenantName"   id="alltenantKey"  value="all" (change)="selectAll($event)">
                     <label for="alltenantKey" class="branch-label"   >All</label> </li>
                  <li  *ngFor="let crosstenant of crossTenantOptions"
                     value="{{crosstenant.id}}"  style="background: none;border: none;">
                     <input type="checkbox" [checked]="(selectedTenant.id == crosstenant.id || userData.tenantId == crosstenant.id)" name="{{crosstenant.tenantName}}"   id="{{crosstenant.tenantKey+'-'+crosstenant.id}}" (change)="checkboxChanged($event,i)" value="{{crosstenant.id}}" >
                     <label for="{{crosstenant.tenantKey+'-'+crosstenant.id}}" class="branch-label" [ngClass]="(selectedTenant.id == crosstenant.id || userData.tenantId == crosstenant.id ) ? 'custom-checked' : 'custom-unchecked'"  > {{crosstenant.tenantName}}</label>
                  </li>
                  <!-- <input type="button" class="btn btn-sm btn-primary" style="margin-top: 10px;" (click)="changeTeanant()" name="" value="Ok"/>  -->
               </ul>
            </form>
         </div>
         <div class="modal-footer">               
            <button  type="button" class="btn btn-secondary" (click)="clearBranchModal()" [disabled]="!checkedBranchIds.length" >Reset</button>
            <button  type="button" class="btn btn-secondary" (click)="changeTeanant()" [disabled]="!checkedBranchIds.length" >Ok</button>
            <button type="button" class="btn btn-secondary" (click)="closeBranchModal()" >Close</button>
         </div>
      </div>
   </div>
</div>
<!-- START: dashboard alpha -->
<!--<nav class="cat__core__top-sidebar cat__core__top-sidebar--bg">
   <div class="pull-right">
       <button (click)="ngOnInit()" class="btn btn-sm btn-outline-default ladda-button">Update<span class="hidden-sm-down"> Dashboard</span></button>
       <button href="javascript: void(0);" onClick="window.location.reload()" class="btn btn-sm btn-outline-default ladda-button reset-button">Reset<span class="hidden-sm-down"> Order</span></button>
   </div>
   <span class="cat__core__title d-block mb-2">
       <strong>Dashboard</strong>
   </span>
   </nav>-->
<!-- END: dashboard alpha -->