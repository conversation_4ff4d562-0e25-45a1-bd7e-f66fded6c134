import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { Daterangepicker } from 'ng2-daterangepicker';

import { AccountAdminDashboardComponent } from './account-admin-dashboard.citushealth';
//import { LeadsDashboardComponent } from './leads-dashboard.citushealth';
import { DashboardDocumentCenterComponent } from './dashboard-document-center';
import { DashboardMessageCenterComponent } from './dashboard-message-center';
import { SharedModule } from '../shared/sharedModule';
import { FilterSitesComponent } from './filter-sites/filter-sites.component';
import { AngularMultiSelectModule } from 'angular2-multiselect-dropdown';
import { FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { AuthGuard } from '../../guard/auth.guard';
export const routes: Routes = [
  { path: 'dashboard', component: AccountAdminDashboardComponent, canActivate:[AuthGuard],data:{checkRoutingPrivileges:'showDashboard'} },
];

@NgModule({
  imports: [
    CommonModule,
    Daterangepicker,
    SharedModule,
    AngularMultiSelectModule,
    TranslateModule.forChild(), 
    FormsModule,
    RouterModule.forChild(routes)
  ],
  declarations: [
    AccountAdminDashboardComponent,
    DashboardDocumentCenterComponent, 
    DashboardMessageCenterComponent, FilterSitesComponent,
    
    // SelectSitesComponent
    //LeadsDashboardComponent
  ],
  exports: [TranslateModule]

})

export class DashboardModule { }
