import { Injectable } from '@angular/core';

@Injectable()
export class DashboardService {

  constructor() { }

  downloadFile(data, tenantName) {
    let csvData = this.ConvertToCSV(data, [
      "User_First_Name",
      "User_Last_Name",
      "MRN/Staff_ID",
      "Invite_date",
      "UserStatus",
      "Invited_by",
      "Email",
      "Mobile",
      "UserTags",
      "Invitation_Open_Date",
      "Invitation_Completed_Date",
      "First_Sign_In_Date",
      "UserType",
      "SMS-Tracking-Status",
      "Sms-Delivery-Status-Message",
      "Email-Tracking-Status",
      "Email-Delivery-Status-Message",
      "Last_Reminder_Sent_On",
      "Site_Name"

    ]);

    let today = new Date().toLocaleDateString()
    let filename = 'Enrollment Report ' + tenantName + " ";
    filename += today;
    //console.log(csvData)
    let blob = new Blob(['\ufeff' + csvData], { type: 'text/csv;charset=utf-8;' });
    let dwldLink = document.createElement("a");
    let url = URL.createObjectURL(blob);
    let isSafariBrowser = navigator.userAgent.indexOf('Safari') != -1 && navigator.userAgent.indexOf('Chrome') == -1;
    if (isSafariBrowser) {  //if Safari open in new window to save file with random filename.
      dwldLink.setAttribute("target", "_blank");
    }
    dwldLink.setAttribute("href", url);
    dwldLink.setAttribute("download", filename + ".csv");
    dwldLink.style.visibility = "hidden";
    document.body.appendChild(dwldLink);
    dwldLink.click();
    document.body.removeChild(dwldLink);
  }

  ConvertToCSV(objArray, headerList) {

  
   
    let array = typeof objArray != 'object' ? JSON.parse(objArray) : objArray;
    let str = '';
    let row = '';

    for (let index in headerList) {
      row += headerList[index] + ',';
    }

    row = row.slice(0, -1);
    str += row + '\r\n';

    for (let i = 0; i < array.length; i++) {
      let line = '';
      for (let index in headerList) {

        let head = headerList[index]; //
        if ((array[i][head]) && (array[i][head]!=="Invalid Date") && (array[i][head]!=="\\undefined\\") && (array[i][head]!=="\"undefined\"") && (array[i][head]!=="\"null\"")) {
          line += array[i][head] + ',';
        }
        else {
          line += " " + ',';
        }
      }
      str += line + '\r\n';
    }
    return str;
  }
  parseinvitesData(data, tenantName) {
    var invitList = [];
    var userStatus;

    for (var key of Object.keys(data["data"])) {
      //console.log("data type "+ typeof(Number (data["data"][key]["wpcf-date-of-birth"])));
      if (data["data"][key]["wpcf-invite-open"] && data["data"][key]["wpcf-submission-date"]) {
        //console.log("Registered" + data["data"][key]["wpcf-json-firstname"])
        userStatus = "Ready To Engage";
      }
      else {
        // console.log("NOT___Registered" + data["data"][key]["wpcf-json-firstname"])
        if (data["data"][key]["wpcf-invite-open"]) {
          userStatus = "Invitation Opened But Never Registered"
        }
        else {
          userStatus = "Invitation Never Opened"
        }
      }

      var invites = {
        "User_First_Name": "\"" + data["data"][key]["wpcf-json-firstname"] + "\"",
        "User_Last_Name": "\"" + data["data"][key]["wpcf-json-lastname"] + "\"",
        "MRN/Staff_ID": data["data"][key]["wpcf-mrn-number"],
        "DOB": this.convertUnixTime(data["data"][key]["wpcf-date-of-birth"]),
        "Invited_by": "\"" + data["data"][key]["wpcf-name-of-assignee"] + "\"",
        "First_Sign_In_Date": this.convertUnixTime(data["data"][key]["wpcf-first-sign-in-datetime"]),
        "Email": data["data"][key]["wpcf-user-email"],
        "Mobile": data["data"][key]["wpcf-user-mobile-number"],
        "UserTags": "\"" + data["data"][key]["wpcf-user-tags"] + "\"",
        "Invitation_Open_Date": this.convertUnixTime(data["data"][key]["wpcf-invite-open"]),
        "Invitation_Completed_Date": this.convertUnixTime(data["data"][key]["wpcf-submission-date"]),
        "UserType": data["data"][key]["wpcf-json-user-type"],
        "Invite_date": this.convertUnixTime(data["data"][key]["wpcf-referred-date"]),
        "SMS-Tracking-Status": data["data"][key]["wpcf-sms-tracking-status"],
        "Sms-Delivery-Status-Message": data["data"][key]["wpcf-sms-delivery-status-message"],
        "Email-Tracking-Status": (data["data"][key]["wpcf-email-tracking-status"]),
        "Email-Delivery-Status-Message": (data["data"][key]["wpcf-email-delivery-status-message"] == null ? undefined : "" + " " + (data["data"][key]["wpcf-email-delivery-status-message"])),
        "UserStatus": userStatus,
        "Last_Reminder_Sent_On": this.convertUnixTime((data["data"][key]["wpcf-last-reminder-sent-at"])), 
        "Tenant_Name" : tenantName,
        "Site_Name" : (data["data"][key]["wpcf-site-names"] ? data["data"][key]["wpcf-site-names"].replace(/,/g,"/") : "")
      }
      invitList.push((invites));
    }
    return invitList;
  }
  convertUnixTime(unixTime) {
    if (unixTime) {
      const date = new Date(unixTime * 1000);
      return date.toLocaleDateString("en-US")+ " " +date.toLocaleTimeString("en-US");
    }
    else
      return '';
  }


  parseFormsData(data, tenantName) {
    var allForms = [];
    var arvhievedBy = "";
    var workflow = "";
    var sentBy='Sent By';
    var ptName="";
    var practionerName="";
    var formSubmitted="";
    var reminder =" ";
    var formSentDate ="";
    var modifiedAT="";
    var modifiedby = "";
    //var reviewed= data["response"][key]["reviewed"];
    //var savedSubmitted='Form Submitted On';
    for (var key of Object.keys(data["response"])) {
      if (typeof (data["response"][key]["deletedName"]) != 'undefined') {
        arvhievedBy = data["response"][key]["deletedName"]
      }
      if (((data["response"][key]["facing_new"])==0)) {
        workflow = "Patient Facing"
      }
      if (((data["response"][key]["facing_new"])==1)) {
        workflow = "Staff Facing"
      }
      if (((data["response"][key]["facing_new"])==2)) {
        workflow = "Staff-Practitioner Facing"
      }
      if (((data["response"][key]["draftid"]))) {
        sentBy='Created By'
        //savedSubmitted = "Saved On"
      }
      if (((data["response"][key]["facing_new"])==2)) {
        practionerName=data["response"][key]["patientName"]
        ptName=data["response"][key]["caregiver_displayname"]; 
      }
      else
      {
        practionerName="";
        ptName=data["response"][key]["patientName"]; 
      }
      if (((data["response"][key]["formStatus"])==="pending")) {
        formSubmitted = "";
        reminder= this.convertUnixTime((data["response"][key]["lastRemindedOn"]));
      }
      else
      {
        if(data["response"][key]["senton"]){
        formSubmitted = this.convertUnixTime(data["response"][key]["senton"]);
      }
      }

      if(data["response"][key]["sentDate"]){
        formSentDate = this.convertUnixTime(data["response"][key]["sentDate"]);
      }

      if(data["response"][key]["formStatus"]=="Draft"){
       formSentDate = data["response"][key]["sentDate"];
       formSubmitted = data["response"][key]["senton"];
       modifiedAT = data["response"][key]["modifiedAT"];
       modifiedby = data["response"][key]["modifiedbyname"];
      }
      
      var oneForm =
      {
        [sentBy]: "\"" + data["response"][key]["createdUser"]  + "\"",
        "Patient Email": "\"" + data["response"][key]["username"] + "\"",
        "Patient Name": "\"" + ptName + "\"",///patientName
        "Patient DOB": "\"" + data["response"][key]["patientDob"] + "\"",
        "MRN": data["response"][key]["patientIdentityValue"],
        "Mobile": data["response"][key]["mobile"],
        "Practitioner Name": "\"" + practionerName+ "\"",
        "Form Name": "\"" + data["response"][key]["form_name"] + "\"",
        "Workflow": "\"" +workflow + "\"",
        "Review Complete": data["response"][key]["reviewed"] == 1 ? "Reviewed" : "Not Reviewed",
        "Scheduled": data["response"][key]["scheduledstatus"],
        "Sent From Via": data["response"][key]["applessMode"] == 0 ? "In-App" : "App-Less",//0 = in app 1 =appless
        "Form Sent Date": "\"" +  formSentDate + "\"",
        "Form Tags": "\"" + data["response"][key]["form_tags"] + "\"",
        "Form Status": data["response"][key]["formStatus"],
        "Form Submitted On": formSubmitted ,
        "Archived By": "\"" + arvhievedBy + "\"",
        "Archived On": this.convertUnixTime(data["response"][key]["deletedOn"]),
        "Saved On": this.convertUnixTime(data["response"][key]["createdtimestamp"]),
        "Updated On": this.convertUnixTime(data["response"][key]["updatedtimestamp"]),
        "Modified On": "\"" + modifiedAT + "\"",
        "Modified By" : "\"" + modifiedby + "\"",
        "Last_Reminder_Sent_On": "\"" + reminder + "\"",
        "Tenant_Name": "\"" + tenantName + "\"",
        "Site Name":  data["response"][key]["siteName"]
      } 
      allForms.push(oneForm);
    }
    //console.log("Array Local");
   // console.log(allForms);
    return allForms;
  }

  downloadFile2(data, tenantName) {
    let csvData = this.ConvertToCSV(data, [
      "Sent By",
      'Created By',
      "Patient Name",
      "Patient DOB",
      "Patient Email",
      "MRN",
      "Mobile",
      "Practitioner Name",
      "Form Name",
      "Workflow",
      "Review Complete",
      "Scheduled",
      "Sent From Via",//0 = in app 1 =appless
      "Form Sent Date",
      "Form Tags",
      "Form Status",
      "Form Submitted On",
      "Archived By",
      "Archived On",
      "Saved On",
      "Updated On",
      "Last_Reminder_Sent_On",
      "Modified On",
      "Modified By",
      "Site Name"
    ]);

    let today = new Date().toLocaleDateString()
    let filename = 'All Forms Worklist Report ' + tenantName + " ";
    filename += today;
    let blob = new Blob(['\ufeff' + csvData], { type: 'text/csv;charset=utf-8;' });
    let dwldLink = document.createElement("a");
    let url = URL.createObjectURL(blob);
    let isSafariBrowser = navigator.userAgent.indexOf('Safari') != -1 && navigator.userAgent.indexOf('Chrome') == -1;
    if (isSafariBrowser) {  //if Safari open in new window to save file with random filename.
      dwldLink.setAttribute("target", "_blank");
    }
    dwldLink.setAttribute("href", url);
    dwldLink.setAttribute("download", filename + ".csv");
    dwldLink.style.visibility = "hidden";
    document.body.appendChild(dwldLink);
    dwldLink.click();
    document.body.removeChild(dwldLink);
  }
}
