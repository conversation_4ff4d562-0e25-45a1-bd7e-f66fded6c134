
<div class="nav-tabs-horizontal">
    <ul class="nav nav-tabs filing-icons" role="tablist">
            <li class="nav-item" (click)="loadMessageActivity('messageSummary')">
                <a class="nav-link" id="messageCenterActivitySummary" href="javascript: void(0);" data-toggle="tab" data-target="#MessageCenterTabSummary" role="tab" aria-expanded="true">
                    <i class="fa fa-comments-o" aria-hidden="true"></i>
                    <span>Message Center Summary</span>
                </a>
            </li>  
            <li class="nav-item"  (click)="loadMessageActivityTrends('messageTrends')">
                <a class="nav-link" id="messageCenterActivityTrend" href="javascript: void(0);" data-toggle="tab" data-target="#MessageCenterTabTrends" role="tab" aria-expanded="true">
                    <i class="fa fa-industry" aria-hidden="true"></i>
                    <span>Message Engagement Trends</span>
                </a>
            </li>          
    </ul>
</div>
<div class="tab-content">
    <div class="tab-pane active mt-4" id="MessageCenterTabSummary" role="tabcard">
        <div class="row" >
            <div class="col-lg-12">
                    <div class="" id="left-col">
                        <div style="width:100%" class="row cat__core__sortable overflow_hidden">                   
                            <!-- Start of widget Message center -->
                            <div class="col-lg-6">
                                <section class="card" order-id="card-1">
                                    <div class="card-header">
                                        <div class="pull-right cat__core__sortable__control">
                                            <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                            <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                            <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                        </div>
                                        <div class="pull-right cat__core__sortable__control">
                                            <div class="load-more" daterangepicker [options]="_sharedService.dashboardSummaryChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'TOTAL_MESSAGES_BY_MESSAGE_TYPE')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                                        </div>
                                        <h5 class="mb-0 text-black">
                                            <strong>{{messageByMessageType.widgetTitle}}</strong>
                                            <small style="font-weight:bold" class="badge badge-primary">{{messageByMessageType.filterLabel}}</small>
                                        </h5>
                                    </div>
                                    <div class="card-block">
                                        <div class="row">
                                                <div class="col-lg-10" style="padding-bottom:5px">   
                                                    <strong>{{messageByMessageType.totalCount}} {{messageByMessageType.totalCountLabel}}</strong> 
                                                </div>   
                                        </div>
                                        <div class="row">
                                            <div id="chart-legend-activity-by-message-type" class="col-lg-4">
                                            </div>                                   
                                            <div class="col-lg-8">
                                                <canvas id="chart-pie-activity-by-message-type"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </section>
                            </div>
                            <!-- End of widget Message center -->
                            <!-- Start of widget Message center -->
                            <div class="col-lg-6">
                                <section class="card" order-id="card-1">
                                    <div class="card-header">
                                        <div class="pull-right cat__core__sortable__control">
                                            <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                            <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                            <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                        </div>
                                        <div class="pull-right cat__core__sortable__control">
                                            <div class="load-more" daterangepicker [options]="_sharedService.dashboardSummaryChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'TOTAL_ACTIVE_MESSAGE_THREADS_BY_INITIATOR')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                                        </div>
                                        <h5 class="mb-0 text-black">
                                            <strong>{{messageByInitiator.widgetTitle}}</strong>
                                            <small style="font-weight:bold" class="badge badge-primary">{{messageByInitiator.filterLabel}}</small>
                                        </h5>
                                    </div>
                                    <div class="card-block">
                                        <div class="row">
                                                <div class="col-lg-10" style="padding-bottom:5px">   
                                                    <strong>{{messageByInitiator.totalCount}} {{messageByInitiator.totalCountLabel}}</strong> 
                                                </div>   
                                        </div>
                                        <div class="row">
                                        <div id="chart-legend-activity-by-initiator" class="col-lg-4">                                   

                                            </div>
                                            <div class="col-lg-8">
                                                <canvas id="chart-pie-activity-by-initiator"></canvas>
                                            </div>
                                            
                                        </div>
                                    </div>
                                </section>
                            </div>
                            <!-- End of widget Message center activity -->
                            <!-- Start of widget Message center activity -->
                            <div class="col-lg-6">
                                <section class="card" order-id="card-1">
                                    <div class="card-header">
                                        <div class="pull-right cat__core__sortable__control">
                                            <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                            <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                            <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                        </div>
                                        <div class="pull-right cat__core__sortable__control">
                                            <div class="load-more" daterangepicker [options]="_sharedService.dashboardSummaryChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'TOTAL_ACTIVE_MESSAGE_THREADS')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                                        </div>
                                        <h5 class="mb-0 text-black">
                                            <strong>{{messageByMessageThread.widgetTitle}}</strong>
                                            <small style="font-weight:bold" class="badge badge-primary">{{messageByMessageThread.filterLabel}}</small>
                                        </h5>
                                    </div>
                                    <div class="card-block">
                                        <div class="row">
                                                <div class="col-lg-10" style="padding-bottom:5px">   
                                                    <strong>{{messageByMessageThread.totalCount}} {{messageByMessageThread.totalCountLabel}}</strong> 
                                                </div>   
                                        </div>
                                        <div class="row">
                                            <div id="chart-legend-activity-by-message-thread" class="col-lg-4">                                   

                                            </div>
                                            <div class="col-lg-8">
                                                <canvas id="chart-pie-activity-by-message-thread"></canvas>
                                            </div>
                                            
                                        </div>
                                    </div>
                                </section>
                            </div>
                            <!-- End of widget Message center activitye-->
                            <!-- Start of widget Message center -->
                            <!-- <div class="col-lg-6">
                                <section class="card" order-id="card-1">
                                    <div class="card-header">
                                        <div class="pull-right cat__core__sortable__control">
                                            <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                            <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                            <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                        </div>
                                        <div class="pull-right cat__core__sortable__control">
                                            <div class="load-more" daterangepicker [options]="_sharedService.dashboardSummaryChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'TOTAL_PATIENT_ENGAGEMENT_MISSED_OPPORTUNITIES')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                                        </div>
                                        <h5 class="mb-0 text-black">
                                            <strong>{{messageByPatientEngagement.widgetTitle}}</strong>
                                            <small style="font-weight:bold" class="badge badge-primary">{{messageByPatientEngagement.filterLabel}}</small>
                                        </h5>
                                    </div>
                                    <div class="card-block">
                                        <div class="row">                                            
                                                <div class="col-lg-12" style="padding-bottom:5px">  
                                                    <div class="table-responsive">
                                                        <table class="table table-borderless table-hover">
                                                        <tbody>
                                                        <tr *ngFor="let item of messageChartSend">
                                                        <td align="right" class="item-total-num">{{messageByPatientEngagement.totalCount}} </td>
                                                        <td align="left" class="item-total-text">Total Message Threads Sent by Patient/Caregiver Without Staff Responses</td>
                                                        </tr>
                                                        </tbody>
                                                        </table>
                                                    </div>                                                    
                                                </div>   
                                        </div>
                                        <div class="row">
                                            <div id="chart-legend-activity-by-patient-engagement" class="col-lg-4">                         
                                            </div>
                                            <div class="col-lg-8">
                                                <canvas id="chart-pie-activity-by-patient-engagement"></canvas>
                                            </div>                                     
                                        </div>
                                    </div>
                                </section>
                            </div> -->
                            <!-- End of widget Message center -->
                            <!-- Start of widget Message center -->
                            <div class="col-lg-6">
                                <section class="card" order-id="card-1">
                                    <div class="card-header">
                                        <div class="pull-right cat__core__sortable__control">
                                            <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                            <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                            <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                        </div>
                                        <div class="pull-right cat__core__sortable__control">
                                            <div class="load-more" daterangepicker [options]="_sharedService.dashboardSummaryChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'TAGGED_MESSAGES_BY_MESSAGE_TAGS')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                                        </div>
                                        <h5 class="mb-0 text-black">
                                            <strong>{{messageByMessageTags.widgetTitle}}</strong>
                                            <small style="font-weight:bold" class="badge badge-primary">{{messageByMessageTags.filterLabel}}</small>
                                        </h5>
                                    </div>
                                    <div class="card-block">
                                        <div class="row">
                                                <div class="col-lg-10" style="padding-bottom:5px">   
                                                    <strong>{{messageByMessageTags.totalCount}} {{messageByMessageTags.totalCountLabel}}</strong> 
                                                </div>   
                                        </div>
                                        <div class="row">
                                        <div id="chart-legend-activity-by-message-tags" class="col-lg-4">                                   

                                            </div>
                                            <div class="col-lg-8">
                                                <canvas id="chart-pie-activity-by-message-tags"></canvas>
                                            </div>
                                            
                                        </div>
                                    </div>
                                </section>
                            </div>
                            <!-- End of widget Message center -->
                            
                            
                        </div>
                    </div>
            </div>
            
        </div>
        <!---Start of patient missed opportunities-->
         <div class="row">
            <div class="col-lg-12">
                <div class="" id="left-col">
                    <div style="width:100%" class="row cat__core__sortable overflow_hidden">
                        <div class="col-lg-8">
                            <section class="card" order-id="card-1">
                                <div class="card-header">
                                            <div class="pull-right cat__core__sortable__control">
                                                <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                                <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                                <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                            </div>
                                            <div class="pull-right cat__core__sortable__control">
                                                <div class="load-more" daterangepicker [options]="_sharedService.dashboardSummaryChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'TOTAL_PATIENT_ENGAGEMENT_MISSED_OPPORTUNITIES')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span></div>
                                            </div>
                                            <h5 class="mb-0 text-black">
                                                <strong>{{messageByPatientEngagement.widgetTitle}}</strong>
                                                <small style="font-weight:bold" class="badge badge-primary">{{messageByPatientEngagement.filterLabel}}</small>
                                            </h5>
                                </div>
                                <div class="card-block">
                                    <div class="row">                                            
                                            <div class="col-lg-12" style="padding-bottom:5px">  
                                                <div class="table-responsive">
                                                    <table class="table table-borderless table-hover">
                                                    <tbody>
                                                    <tr >
                                                    <td align="right" class="item-total-num">{{messageByPatientEngagement.totalCount}} </td>
                                                    <td align="left" class="item-total-text">Total Message Threads Sent by Patient/Caregiver Without Staff Responses</td>
                                                    </tr>
                                                    </tbody>
                                                    </table>
                                                </div>                                                    
                                            </div>   
                                    </div>
                                    <!-- <div class="row">
                                        <div id="chart-legend-activity-by-patient-engagement" class="col-lg-4">                         
                                        </div>
                                        <div class="col-lg-8">
                                            <canvas id="chart-pie-activity-by-patient-engagement"></canvas>
                                        </div>                                     
                                    </div> -->
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!---End of patient missed opportunities-->
    </div> 
    <div class="tab-pane mt-4" id="MessageCenterTabTrends" role="tabcard">
        <div class="row" >
             <div class="col-lg-12">
                <div class="" id="left-col">
                    <div style="width:100%" class="row cat__core__sortable overflow_hidden">
                        <div class="col-lg-8">
                            <section class="card" order-id="card-1">
                                <div class="card-header">
                                    <div class="pull-right cat__core__sortable__control">
                                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                    </div>
                                    <div class="pull-right cat__core__sortable__control">
                                        <div class="load-more" daterangepicker [options]="_sharedService.dashboardTrendChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'TAGGED_MESSAGES_BY_MESSAGE_TAGS','trend')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                                        </div>
                                    </div>
                                    <h5 class="mb-0 text-black">
                                    <strong>{{messageTrendByMessageTags.widgetTitle}}</strong>
                                    <small style="font-weight:bold" class="badge badge-primary">{{ messageTrendByMessageTags.filterLabel }}</small>
                                    </h5>
                                </div>
                                <div class="card-block">                 
                                    <div class="row">
                                        <div id="chart-legend-activity-trend-by-message-tags" class="col-lg-4">
                                        </div>
                                        <div class="col-lg-8">
                                        <canvas id="chart-pie-activity-trend-by-message-tags" height="200"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-12">
                <div class="" id="left-col">
                    <div style="width:100%" class="row cat__core__sortable overflow_hidden">
                        <div class="col-lg-8">
                            <section class="card" order-id="card-1">
                                <div class="card-header">
                                    <div class="pull-right cat__core__sortable__control">
                                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                    </div>
                                    <div class="pull-right cat__core__sortable__control">
                                        <div class="load-more" daterangepicker [options]="_sharedService.dashboardTrendChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'TOTAL_ACTIVE_MESSAGE_THREADS','trend')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                                        </div>
                                    </div>
                                    <h5 class="mb-0 text-black">
                                    <strong>{{messageTrendByMessageThread.widgetTitle}}</strong>
                                    <small style="font-weight:bold" class="badge badge-primary">{{ messageTrendByMessageThread.filterLabel }}</small>
                                    </h5>
                                </div>
                                <div class="card-block">                 
                                    <div class="row">
                                        <div id="chart-legend-activity-trend-by-message-thread" class="col-lg-4">
                                        </div>
                                        <div class="col-lg-8">
                                        <canvas id="chart-pie-activity-trend-by-message-thread" height="200"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-12">
                <div class="" id="left-col">
                    <div style="width:100%" class="row cat__core__sortable overflow_hidden">
                        <div class="col-lg-8">
                            <section class="card" order-id="card-1">
                                <div class="card-header">
                                    <div class="pull-right cat__core__sortable__control">
                                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                    </div>
                                    <div class="pull-right cat__core__sortable__control">
                                        <div class="load-more" daterangepicker [options]="_sharedService.dashboardTrendChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'TOTAL_ACTIVE_MESSAGE_THREADS_BY_INITIATOR','trend')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                                        </div>
                                    </div>
                                    <h5 class="mb-0 text-black">
                                    <strong>{{messageTrendByInitiator.widgetTitle}}</strong>
                                    <small style="font-weight:bold" class="badge badge-primary">{{ messageTrendByInitiator.filterLabel }}</small>
                                    </h5>
                                </div>
                                <div class="card-block">                 
                                    <div class="row">
                                        <div id="chart-legend-activity-trend-trend-by-initiator" class="col-lg-4">
                                        </div>
                                        <div class="col-lg-8">
                                        <canvas id="chart-pie-activity-trend-by-initiator" height="200"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="col-lg-12">
                <div class="" id="left-col">
                    <div style="width:100%" class="row cat__core__sortable">
                        <div class="col-lg-8">
                            <section class="card" order-id="card-1">
                                <div class="card-header">
                                    <div class="pull-right cat__core__sortable__control">
                                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                    </div>
                                    <div class="pull-right cat__core__sortable__control">
                                        <div class="load-more" daterangepicker [options]="_sharedService.dashboardTrendChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'TOTAL_PATIENT_ENGAGEMENT_MISSED_OPPORTUNITIES','trend')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                                        </div>
                                    </div>
                                    <h5 class="mb-0 text-black">
                                    <strong>{{messageTrendByPatientEngagement.widgetTitle}}</strong>
                                    <small style="font-weight:bold" class="badge badge-primary">{{ messageTrendByPatientEngagement.filterLabel }}</small>
                                    </h5>
                                </div>
                                <div class="card-block">                 
                                    <div class="row">
                                        <div id="chart-legend-activity-trend-by-patient-engagement" class="col-lg-4">
                                        </div>
                                        <div class="col-lg-8">
                                        <canvas id="chart-pie-activity-trend-by-patient-engagement" height="200"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </div> -->
           
            <div class="col-lg-12">
                <div class="" id="left-col">
                    <div style="width:100%" class="row cat__core__sortable overflow_hidden">
                        <div class="col-lg-8">
                            <section class="card" order-id="card-1">
                                <div class="card-header">
                                    <div class="pull-right cat__core__sortable__control">
                                        <i class="icmn-minus mr-2 cat__core__sortable__collapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Collapse"></i>
                                        <i class="icmn-plus mr-2 cat__core__sortable__uncollapse" data-toggle="tooltip" data-placement="left" title="" data-original-title="Uncollapse"></i>
                                        <i class="icmn-cross cat__core__sortable__close" data-toggle="tooltip" data-placement="left" title="" data-original-title="Remove"></i>
                                    </div>
                                    <div class="pull-right cat__core__sortable__control">
                                        <div class="load-more" daterangepicker [options]="_sharedService.dashboardTrendChartDateFilterOptions" class="load-more" (applyDaterangepicker)="selectedDate($event,'TOTAL_MESSAGES_BY_MESSAGE_TYPE','trend')"><span><i class="fa fa-ellipsis-h" aria-hidden="true"></i></span>
                                        </div>
                                    </div>
                                    <h5 class="mb-0 text-black">
                                    <strong>{{messageTrendByMessageType.widgetTitle}}</strong>
                                    <small style="font-weight:bold" class="badge badge-primary">{{ messageTrendByMessageType.filterLabel }}</small>
                                    </h5>
                                </div>
                                <div class="card-block">                 
                                    <div class="row">
                                        <div id="chart-legend-activity-trend-by-message-type" class="col-lg-4">
                                        </div>
                                        <div class="col-lg-8">
                                        <canvas id="chart-pie-activity-trend-by-message-type" height="200"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </div>
         
            
        </div> 
    </div>
</div>

