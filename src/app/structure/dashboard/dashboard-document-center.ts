import { Component, OnInit, ElementRef, Renderer, Input, EventEmitter } from '@angular/core';
import { StructureService } from '../structure.service';
import { SharedService } from '../shared/sharedServices';
declare var moment: any;
declare var $: any;
declare var Chart: any;

@Component({
    selector: 'dashboard-signature-request',
    templateUrl: './dashboard-document-center.html',
    styleUrls: ['./dashboard.scss'],
})
export class DashboardDocumentCenterComponent implements OnInit {     
    @Input() private loadDocumentActivityData: EventEmitter<boolean>;
    @Input() private siteIdDetails: EventEmitter<boolean>;

    public pieChartBackground = [
        "#FF6384", "#36A2EB", "#FFCE56", "#46BE8A", "#823a03", "#3D01A4"
        
    ];
    public activityByWorkFlowData = {
        widgetTitle: 'Document Center Activity by Workflow',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Total Document Center Activity by Workflow',       
    }
    public activityByStatusData = {
        widgetTitle: 'Document Center Activity by Status',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Total Document Center Activity by Status',
    }
    public activityByStaffData = {
        widgetTitle: 'Document Center Activity by Staff',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Total Document Center Activity by Staff',       
    }
    public activityByPatientData = {
        widgetTitle: 'Document Center Engagement by Patients',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Total Document Center Engagement with Patients',
    }
    public activityByDocumentTypeData = {
        widgetTitle: 'Document Center Engagement by Document Type',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Total Patient Engagement with Document Type',
    }
    public activityTrendByWorkFlowData = {
        widgetTitle: 'Total Document Center Activity Trends by Workflow',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Total Document Center Activity Trends by Workflow',
    }
    public activityTrendByStatusData = {
        widgetTitle: 'Total Document Center Activity Trends by Status',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Total Document Center Activity Trends by Status',
    }
    public activityTrendByStaffData = {
        widgetTitle: 'Document Center Activity by Staff',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Total Document Center Activity by Staff',
    }
    public activityTrendByPatientData = {
        widgetTitle: 'Document Center Engagement by Patients Trends',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Total Document Center Engagement by Patients Trends',
    }
    public activityTrendByDocumentTypeData = {
        widgetTitle: 'Document Center Engagement Trends by Document Type',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Total Document Center Engagement Trends by Document Type',
    }
    public statusChart: any;
    public workflowChart: any;
    public staffChart: any;
    public patientChart: any;
    public documentTypeChart: any;
    
    public daterange: any = {};
    siteIds:any;
    
    constructor(public _structureService: StructureService, elementRef: ElementRef,
        renderer: Renderer, public _sharedService: SharedService) {
        
    }
    ngOnInit() {
        if (this.loadDocumentActivityData) {
            this.loadDocumentActivityData.subscribe(data => {
                this.siteIdDetails.subscribe(siteId => {
                    this.siteIds = siteId;
                this.loadDocumentActivity();  
                });
            });
        }
        
    }
    loadDocumentActivityTrends() {
        $("#signatureTrends").trigger("click");
        let trendFilterObject = {
            start: moment().startOf('day').subtract(29, 'days').unix(),
            end: moment().endOf('day').unix(),
            label: 'Last 30 days by Week',
            chartType: '',
            legendDiv: '',
            chartDiv: '',
        }
        trendFilterObject.chartType = 'DOCUMENT_CENTER_ACTIVITY_BY_WORKFLOW';
        trendFilterObject.legendDiv = '#chart-activity-trend-workflow-legend';
        trendFilterObject.chartDiv = '#chart-activity-trend-workflow-line';
        this.filterDocumentActivityTrend(trendFilterObject);
        trendFilterObject.chartType = 'DOCUMENT_CENTER_ACTIVITY_BY_STATUS';
        trendFilterObject.legendDiv = '#chart-activity-trend-status-legend';
        trendFilterObject.chartDiv = '#chart-activity-trend-status-line';
        this.filterDocumentActivityTrend(trendFilterObject);
        trendFilterObject.chartType = 'DOCUMENT_CENTER_ENGAGEMENT_BY_PATIENT';
        trendFilterObject.legendDiv = '#chart-activity-trend-patient-legend';
        trendFilterObject.chartDiv = '#chart-activity-trend-patient-line';
        this.filterDocumentActivityTrend(trendFilterObject);
        trendFilterObject.chartType = 'DOCUMENT_CENTER_ACTIVITY_BY_STAFF';
        trendFilterObject.legendDiv = '#chart-activity-trend-staff-legend';
        trendFilterObject.chartDiv = '#chart-activity-trend-staff-line';
        this.filterDocumentActivityTrend(trendFilterObject);
        trendFilterObject.chartType = 'DOCUMENT_CENTER_ENGAGEMENT_BY_DOCUMENT_TYPE';
        trendFilterObject.legendDiv = '#chart-activity-trend-document-type-legend';
        trendFilterObject.chartDiv = '#chart-activity-trend-document-type-line';
        this.filterDocumentActivityTrend(trendFilterObject);
    }
    loadDocumentActivity() {
        $("#signatureItems").trigger("click");
        let filterObject = {
            start: moment().startOf('day').subtract(29, 'days').unix(),
            end: moment().endOf('day').unix(),
            label: 'Last 30 Days',
            chartType: '',
            legendDiv: '',
            chartDiv: '',
        }      
        filterObject.chartType = 'DOCUMENT_CENTER_ACTIVITY_BY_WORKFLOW';
        filterObject.legendDiv = '#chart-legend-activity-by-workflow';
        filterObject.chartDiv = '#chart-pie-activity-by-workflow';
        this.filterDocumentActivity(filterObject);
        filterObject.chartType = 'DOCUMENT_CENTER_ACTIVITY_BY_STATUS';
        filterObject.legendDiv = '#chart-legend-activity-by-status';
        filterObject.chartDiv = '#chart-pie-activity-by-status';
        this.filterDocumentActivity(filterObject);
        filterObject.chartType = 'DOCUMENT_CENTER_ENGAGEMENT_BY_PATIENT';
        filterObject.legendDiv = '#chart-legend-activity-by-patient';
        filterObject.chartDiv = '#chart-pie-activity-by-patient';
        this.filterDocumentActivity(filterObject);
        filterObject.chartType = 'DOCUMENT_CENTER_ACTIVITY_BY_STAFF';
        filterObject.legendDiv = '#chart-legend-activity-by-staff';
        filterObject.chartDiv = '#chart-pie-activity-by-staff';
        this.filterDocumentActivity(filterObject);
        filterObject.chartType = 'DOCUMENT_CENTER_ENGAGEMENT_BY_DOCUMENT_TYPE';
        filterObject.legendDiv = '#chart-legend-activity-by-document-type';
        filterObject.chartDiv = '#chart-pie-activity-by-document-type';
        this.filterDocumentActivity(filterObject);
    }

   
    filterDocumentActivity(filterObject) {
        let type = filterObject.chartType;
        let filterArguments = {
            start: filterObject.start, 
            end: filterObject.end,       
            type: filterObject.chartType,
            site_id:this.siteIds
        }
        let chartData = {
            type: filterObject.chartType,
            legendDiv: filterObject.legendDiv,
            chartDiv: filterObject.chartDiv,
            chartData: [],
            chartLabels: [],
            backgroundColor: []        
        }
        let chartWidgetFilterLabel = '';
        if (filterObject.label == 'Custom Range')
            chartWidgetFilterLabel = moment.unix(filterObject.start).format("MMM Do, YYYY") + ' to ' + moment.unix(filterObject.end).format("MMM Do, YYYY");
        else
            chartWidgetFilterLabel = filterObject.label;
        this._structureService.getSignatureRequestPieChartData(filterArguments).then((data) => {  
            console.log(data);
            let activityByWorkFlow = JSON.parse(JSON.stringify(data['dashboardPieChart']));
            
            let chartWidgetTotalCount = activityByWorkFlow.values.reduce((sum, current) => sum + parseInt(current), 0);
            chartData.chartData = activityByWorkFlow.values;
            if (activityByWorkFlow.labels.length > 0) {
                chartData.chartLabels = activityByWorkFlow.labels;
            } else {
                chartData.chartLabels.push('No Data Found');
            }
            chartData.backgroundColor = activityByWorkFlow.fillColors; 
            switch (type) {
                case 'DOCUMENT_CENTER_ACTIVITY_BY_WORKFLOW':
                    this.activityByWorkFlowData['filterLabel'] = chartWidgetFilterLabel;
                    this.activityByWorkFlowData['totalCount'] = chartWidgetTotalCount;         
                    break;
                case 'DOCUMENT_CENTER_ACTIVITY_BY_STATUS':
                    this.activityByStatusData['filterLabel'] = chartWidgetFilterLabel;
                    this.activityByStatusData['totalCount'] = chartWidgetTotalCount;         
                    break;
                case 'DOCUMENT_CENTER_ACTIVITY_BY_STAFF':
                    this.activityByStaffData['filterLabel'] = chartWidgetFilterLabel;
                    this.activityByStaffData['totalCount'] = chartWidgetTotalCount;         
                    break;
                case 'DOCUMENT_CENTER_ENGAGEMENT_BY_PATIENT':
                    this.activityByPatientData['filterLabel'] = chartWidgetFilterLabel;
                    this.activityByPatientData['totalCount'] = chartWidgetTotalCount;         
                   break;
                case 'DOCUMENT_CENTER_ENGAGEMENT_BY_DOCUMENT_TYPE':
                    this.activityByDocumentTypeData['filterLabel'] = chartWidgetFilterLabel;
                    this.activityByDocumentTypeData['totalCount'] = chartWidgetTotalCount;         
                    break;
                default:
                    break;
            }
            console.log(chartData);
            this.renderChart(chartData);
        });
    }  
    preparePieChartBackgroundColorArray(arrayLength) {
        if (arrayLength < this.pieChartBackground.length) {
            return this.pieChartBackground;
        } else {
            let times = arrayLength / this.pieChartBackground.length;
            let newBackgroundArray = [];
            for (let i = 0; i < times; i++) {
                newBackgroundArray = [...this.pieChartBackground, ...newBackgroundArray];
            }
            return newBackgroundArray;
        }
    }
    renderChart(chartData) {
        var pieCtx = $(chartData.chartDiv)[0].getContext('2d');
        pieCtx.height = 300;
        var options = {
            tooltips: {
                enabled: true,
                callbacks: {
                    label: function (tooltipItems, data) {
                        console.log(tooltipItems);
                        console.log(data);
                        if (data.labels[tooltipItems.index].length > 40){
                            return data.labels[tooltipItems.index].substring(0,40) + '...';
                        } else {
                            return data.labels[tooltipItems.index];
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true
            },
            legend: {
                display: false
            },
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                datalabels: {
                    formatter: (value, ctx) => {
                        console.log(value);
                        let sum = 0;
                        let dataArr = ctx.chart.data.datasets[0].data;
                        dataArr.map(data => {
                            sum += parseInt(data);
                        });
                        let percentage = Math.round(value * 100 / sum) + "%";
                        if (Math.round(value * 100 / sum) > 0 ) { return percentage; } else { return ''; }


                    },
                    color: '#fff',
                }
            },
            legendCallback: (chart) => {
                var text = [];
                text.push('<ul style="list-style: none; padding: 0;" class="' + chart.id + '-legend">');
                for (var i = 0; i < chartData.chartData.length; i++) {
                    // text.push('<li  stext.push('<li  style="display: flex;"  ><span style="height: 10px;min-width: 10px;margin-top: 5px;display: inline-block;margin-right: 10px;background-color:' + chart.data.datasets[0].backgroundColor[i] + '"></span>');
                    text.push('<li  style="display: flex;"  ><span style="height: 10px;min-width: 10px;margin-top: 5px;display: inline-block;margin-right: 10px;background-color:' + chart.data.datasets[0].backgroundColor[i] + '"></span>');
                    if (chart.data.labels[i]) {
                        text.push(chartData.chartData[i] +' '+chartData.chartLabels[i]);
                    }
                    text.push('</li>');
                }
                text.push('</ul>');
                return text.join("");
            }
        }
        var chartPie = {
            labels: chartData.chartLabels,
            datasets: [
                {
                    data: chartData.chartData,
                    backgroundColor: chartData.backgroundColor,
                    hoverBackgroundColor: chartData.backgroundColor,
                    borderWidth:0
                }]
        };
        let chartConfig = new Chart(pieCtx, {
            type: 'pie',
            data: chartPie,
            options: options
        });
        this.destroyPreviousChart(chartData.type, chartConfig);
        $(chartData.legendDiv).html(chartConfig.generateLegend());
    }

    selectedDate(value: any, type: any, widgetType: any="") {
        value.event.target.value = value.picker.startDate.format('YYYY-MM-DD') + ' - ' + value.picker.endDate.format('YYYY-MM-DD');
        let filterObject = {
            start: value.picker.startDate.unix(),
            end: value.picker.endDate.unix(),
            label: value.picker.chosenLabel,
            chartType: type,
            legendDiv: '',
            chartDiv: '',
        };    
        if (widgetType && widgetType !="") {
            switch (type) {
                case 'DOCUMENT_CENTER_ACTIVITY_BY_WORKFLOW':
                    filterObject.legendDiv = '#chart-activity-trend-workflow-legend';
                    filterObject.chartDiv = '#chart-activity-trend-workflow-line';
                    break;
                case 'DOCUMENT_CENTER_ACTIVITY_BY_STATUS':
                    filterObject.legendDiv = '#chart-activity-trend-status-legend';
                    filterObject.chartDiv = '#chart-activity-trend-status-line';
                    break;
                case 'DOCUMENT_CENTER_ACTIVITY_BY_STAFF':
                    filterObject.legendDiv = '#chart-activity-trend-staff-legend';
                    filterObject.chartDiv = '#chart-activity-trend-staff-line';
                    break;
                case 'DOCUMENT_CENTER_ENGAGEMENT_BY_PATIENT':
                    filterObject.legendDiv = '#chart-activity-trend-patient-legend';
                    filterObject.chartDiv = '#chart-activity-trend-patient-line';
                    break;
                case 'DOCUMENT_CENTER_ENGAGEMENT_BY_DOCUMENT_TYPE':
                    filterObject.legendDiv = '#chart-activity-trend-document-type-legend';
                    filterObject.chartDiv = '#chart-activity-trend-document-type-line';
                    break;
                default:
                    filterObject.legendDiv = '#chart-activity-trend-workflow-legend';
                    filterObject.chartDiv = '#chart-activity-trend-workflow-line';
                    break;
            }
            this.filterDocumentActivityTrend(filterObject); 
        } else {
        switch (type) {
            case 'DOCUMENT_CENTER_ACTIVITY_BY_WORKFLOW':
                filterObject.legendDiv = '#chart-legend-activity-by-workflow';
                filterObject.chartDiv = '#chart-pie-activity-by-workflow';
                break;
            case 'DOCUMENT_CENTER_ACTIVITY_BY_STATUS':
                filterObject.legendDiv = '#chart-legend-activity-by-status';
                filterObject.chartDiv = '#chart-pie-activity-by-status';
                break;
            case 'DOCUMENT_CENTER_ACTIVITY_BY_STAFF':
                filterObject.legendDiv = '#chart-legend-activity-by-staff';
                filterObject.chartDiv = '#chart-pie-activity-by-staff';
                break;
            case 'DOCUMENT_CENTER_ENGAGEMENT_BY_PATIENT':
                filterObject.legendDiv = '#chart-legend-activity-by-patient';
                filterObject.chartDiv = '#chart-pie-activity-by-patient';
                break;
            case 'DOCUMENT_CENTER_ENGAGEMENT_BY_DOCUMENT_TYPE':
                filterObject.legendDiv = '#chart-legend-activity-by-document-type';
                filterObject.chartDiv = '#chart-pie-activity-by-document-type';
                break;
            default:
                filterObject.legendDiv = '#chart-legend-activity-by-workflow';
                filterObject.chartDiv = '#chart-pie-activity-by-workflow';
                break;
        }
        this.filterDocumentActivity(filterObject);
        }      
        
    }
    filterDocumentActivityTrend(filterObject) {
        let type = filterObject.chartType;        
        let filterArguments = {
            start: filterObject.start,
            end: filterObject.end,
            type: filterObject.chartType,
            filterType: '',
            siteIds:this.siteIds
        }        
        
        let chartWidgetFilterLabel = '';
        if (filterObject.label == 'Custom Range')
            chartWidgetFilterLabel = moment.unix(filterObject.start).format("MMM Do, YYYY") + ' to ' + moment.unix(filterObject.end).format("MMM Do, YYYY");
        else
            chartWidgetFilterLabel = filterObject.label;
        if (chartWidgetFilterLabel == "Last 7 days by Day") {
            filterArguments.filterType = "DAILY";
        }
        else if (chartWidgetFilterLabel == "Last Quarter by Month" || chartWidgetFilterLabel == "Year to Date by Month") {
            filterArguments.filterType = "MONTHLY";
        } else if (chartWidgetFilterLabel == "Last Year by Quarter" || chartWidgetFilterLabel == "Year to Date by Quarter") {
            filterArguments.filterType = "QUARTERLY";
        }
        else {
            filterArguments.filterType = "WEEKLY";
        }
        let chartData = {
            type: filterObject.chartType,
            labelType:filterArguments.filterType,
            legendDiv: filterObject.legendDiv,
            chartDiv: filterObject.chartDiv,
            chartData: [],
            chartLabels: [],
            backgroundColor: [],
            chartText: '',

        }
        this._structureService.getSignatureRequestLineChartData(filterArguments).then((data) => {            
            let activityByWorkFlow = JSON.parse(JSON.stringify(data['dashboardLineChart']));
            console.log('arrayLength'+activityByWorkFlow.length);
            if (activityByWorkFlow.length > 0) {
                chartData.chartData = activityByWorkFlow;
                switch (type) {
                    case 'DOCUMENT_CENTER_ACTIVITY_BY_WORKFLOW':
                        this.activityTrendByWorkFlowData['filterLabel'] = chartWidgetFilterLabel;
                        chartData.chartText = this.activityTrendByWorkFlowData.totalCountLabel;
                        break;
                    case 'DOCUMENT_CENTER_ACTIVITY_BY_STATUS':
                        this.activityTrendByStatusData['filterLabel'] = chartWidgetFilterLabel;
                        chartData.chartText = this.activityTrendByStatusData.totalCountLabel;
                        break;
                    case 'DOCUMENT_CENTER_ACTIVITY_BY_STAFF':
                        this.activityTrendByStaffData['filterLabel'] = chartWidgetFilterLabel;
                        chartData.chartText = this.activityTrendByStaffData.totalCountLabel;
                        break;
                    case 'DOCUMENT_CENTER_ENGAGEMENT_BY_PATIENT':
                        this.activityTrendByPatientData['filterLabel'] = chartWidgetFilterLabel;
                        chartData.chartText = this.activityTrendByPatientData.totalCountLabel;
                        break;
                    case 'DOCUMENT_CENTER_ENGAGEMENT_BY_DOCUMENT_TYPE':
                        this.activityTrendByDocumentTypeData['filterLabel'] = chartWidgetFilterLabel;
                        chartData.chartText = this.activityTrendByDocumentTypeData.totalCountLabel;
                        break;
                    default:
                        break;
                }
                this.renderLineChart(chartData);
            }
        });
    }
    renderLineChart(chartData) {
        var ctx = $(chartData.chartDiv)[0].getContext('2d');
        ctx.height = 300;
        var labels = [];
        var dataset = [];        
        var chartLabels = [];
        if (chartData.labelType === 'WEEKLY') {             
                    let previousNumber;
                    let previousYear;
                    let previousLabelNumber;
                    chartData.chartData[0]['xAxisValues'].forEach((a,index) => {  
                        if (a) {
                            let labelElements = a.split(':');
                            let newLabelNumber;
                            let currentNumber = parseInt(labelElements[0].replace("Wk", ""));
                            let currentYear = parseInt(labelElements[1]);                           
                            if (index != 1) {
                                let difference = 0; 
                                if (currentYear === previousYear) {
                                    difference = currentNumber - previousNumber;  
                                    newLabelNumber = previousLabelNumber + difference;
                                    chartLabels.push(`Wk${newLabelNumber}`);
                                } else {
                                    console.log(previousNumber);
                                    console.log(currentNumber);
                                    console.log(previousYear);
                                    console.log(moment(previousYear).isoWeeksInYear() == previousNumber);
                                    console.log(currentNumber == 1);
                                    if (moment().year(currentYear).weekday() === 0) {
                                        difference = (moment(previousYear).isoWeeksInYear() - previousNumber)
                                            + currentNumber;
                                        newLabelNumber = previousLabelNumber + difference;
                                        chartLabels.push(`Wk${newLabelNumber}`);
                                    } else {
                                        if (moment(previousYear).isoWeeksInYear() == previousNumber && currentNumber == 1) { 
                                            chartData.chartData.forEach((y, itemIndex) => {
                                                y.yAxisValues[index - 1] = y.yAxisValues[index - 1] + y.yAxisValues[index];
                                                chartData.chartData[itemIndex].yAxisValues.splice(index, 1);
                                            });
                                            newLabelNumber = previousLabelNumber;
                                        } else {
                                            difference = (moment(previousYear).isoWeeksInYear() - previousNumber)
                                                + currentNumber; 
                                            newLabelNumber = previousLabelNumber + difference-1;
                                            chartLabels.push(`Wk${newLabelNumber}`);
                                        }
                                    }                                 
                                }
                                
                            } else {
                                newLabelNumber = 1;
                                chartLabels.push(`Wk${newLabelNumber}`);
                            }
                            previousNumber = currentNumber;
                            previousYear = currentYear;
                            previousLabelNumber = newLabelNumber;                          
                        } else {
                            chartLabels.push(a);
                        }                        
                    });              
        } else {
            chartLabels = chartData.chartData[0]['xAxisValues'].map(a => { return a.split(':')[0]; });
        }
        chartData.chartData.forEach(element => {
            let dataObject = {};
            dataObject['label'] = element.label;
            dataObject['fill'] = "false";
            dataObject['borderColor'] = element.strokeColor;
            dataObject['data'] = element.yAxisValues;
            dataset.push(dataObject);
        });
        var textqu = chartData.chartText;;
        var options = {
            title: {
                display: true,
                text: textqu,
            },
            animation: {
                animateRotate: true,
                animateScale: true
            },
            legend: {
                display: false
            },           
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                datalabels: {
                    formatter: (value, ctx) => {
                        let sum = 0;
                        let dataArr = ctx.chart.data.datasets[0].data;
                        dataArr.map(data => {
                            sum += data;
                        });
                        let percentage = value;
                        if (percentage != "0%") { return percentage; } else { return ''; }
                    },
                    color: '#000',
                }
            },
            legendCallback:  (chart) => {
                var text = [];
                //if (this.getSumOfArray(chartData.chartData[0].yAxisValues) > 0) {
                    text.push('<ul style="list-style: none; padding: 0;" class="' + chart.id + '-legend">');
                    for (var i = 0; i < dataset.length; i++) {
                        text.push('<li  style="display: flex;"  ><span style="height: 10px;min-width: 10px;margin-top: 5px;margin-top: 5px;display: inline-block;margin-right: 10px;background-color:' + chart.data.datasets[i].borderColor + '"></span>');
                        console.log('*********');
                        if (i === 0) {
                            text.push('<b>' + this.getSumOfArray(chartData.chartData[i].yAxisValues) + ' ' + chart.data.datasets[i].label + '</b>');
                        } else {
                            text.push(this.getSumOfArray(chartData.chartData[i].yAxisValues) + ' ' + chart.data.datasets[i].label);
                        }
                        text.push('</li>');
                    }
                    text.push('</ul>');
                //}
                return text.join("");
            }
        }
        let chartLine = {
            labels: chartLabels,
            datasets: dataset
        };
        let lineChartConfig = new Chart(ctx, {
            type: 'line',
            data: chartLine,
            options: options
        });
        this.destroyPreviousChart(chartData.type, lineChartConfig);
        $(chartData.legendDiv).html(lineChartConfig.generateLegend());
        lineChartConfig.resize();        
    }
    dynamicColors() {
        var r = Math.floor(Math.random() * 255);
        var g = Math.floor(Math.random() * 255);
        var b = Math.floor(Math.random() * 255);
        return "rgb(" + r + "," + g + "," + b + ")";        
    };
    destroyPreviousChart(type,config) {
        switch (type) {
            case 'DOCUMENT_CENTER_ACTIVITY_BY_WORKFLOW':
                if (this.workflowChart) {
                    this.workflowChart.destroy();
                }
                this.workflowChart = config;
                break;
            case 'DOCUMENT_CENTER_ACTIVITY_BY_STATUS':
                if (this.statusChart) {
                    this.statusChart.destroy();
                }
                this.statusChart = config;
                break;
            case 'DOCUMENT_CENTER_ACTIVITY_BY_STAFF':
                if (this.staffChart) {
                    this.staffChart.destroy();
                }
                this.staffChart = config;
                break;
            case 'DOCUMENT_CENTER_ENGAGEMENT_BY_PATIENT':
                if (this.patientChart) {
                    this.patientChart.destroy();
                }
                this.patientChart = config;
                break;
            case 'DOCUMENT_CENTER_ENGAGEMENT_BY_DOCUMENT_TYPE':
                if (this.documentTypeChart) {
                    this.documentTypeChart.destroy();
                }
                this.documentTypeChart = config;
                break;
            default:
                break;
        }   
    }
    getSumOfArray(valueArray) {
        let sum = 0;
        for (var j = 0; j < valueArray.length; j++) {
            console.log(valueArray[j]);
            if (valueArray[j]) {                
                sum = sum + parseInt(valueArray[j]);
            }
        }
        return sum;
    }
}
