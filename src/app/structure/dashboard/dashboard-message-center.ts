import { Component, OnInit, ElementRef, Renderer, Input, EventEmitter } from '@angular/core';
import { StructureService } from '../structure.service';
import { SharedService } from '../shared/sharedServices';
import { isBlank } from 'app/utils/utils';
declare var moment: any;
declare var $: any;
declare var Chart: any;

@Component({
    selector: 'dashboard-message-center',
    templateUrl: './dashboard-message-center.html',
    styleUrls: ['./dashboard.scss'],
})
export class DashboardMessageCenterComponent implements OnInit {
    selectSiteId;
    @Input() private loadMessageActivityData: EventEmitter<boolean>;
    @Input() private loadMessageActivitySiteData: EventEmitter<any>;
    public pieChartBackground = [
        "#FF6384", "#36A2EB", "#FFCE56", "#46BE8A", "#823a03", "#3D01A4"

    ];
    public messageByMessageType = {
        widgetTitle: 'Total Messages by Message Type',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Total Messages',
    }
    public messageByInitiator = {
        widgetTitle: 'Total Active Message Threads by Initiator',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Total Active Messages by Initiator',
    }
    public messageByMessageThread= {
        widgetTitle: 'Total Active Message Threads ',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Total Active Message Threads ',
    }
    public messageByPatientEngagement  = {
        widgetTitle: 'Patient Initiated Chat, Delays/Missed Responding',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Patient Initiated Chat, Delays/Missed Responding',
    }
    public messageByMessageTags  = {
        widgetTitle: 'Tagged Messages by Message Tags',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Total Tagged Messages by Message Tag',
    }
    public messageTrendByMessageType = {
        widgetTitle: 'Total Active Messages Trends by Message Type',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Total Active Messages Trends by Message Type',
    }
    public messageTrendByPatientEngagement = {
        widgetTitle: 'Patient Engagement Missed Opportunities Trends ',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Patient Engagement Missed Opportunities Trends',
    }
    public messageTrendByMessageTags = {
        widgetTitle: 'Tagged Messages Trends by Message Tags',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Tagged Messages Trends by Message Tags',
    }
    public messageTrendByInitiator = {
        widgetTitle: 'Active Message Threads Trends by Initiator',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Active Message Threads Trends by Initiator ',
    }
    public messageTrendByMessageThread = {
        widgetTitle: 'Total Active Message Threads Trends',
        filterLabel: '',
        totalCount: 0,
        totalCountLabel: 'Total Active Message Threads Trends',
    } 
    public messageTypeChart: any;
    public initiatorChart: any;
    public messageTagChart: any;
    public patientEngagementChart: any;
    public activeThreadChart: any;
    public daterange: any = {};
    emittedSiteIds: any;
    constructor(public _structureService: StructureService, elementRef: ElementRef,
        renderer: Renderer, public _sharedService: SharedService) {

    }
    ngOnInit() {
        if (this.loadMessageActivityData) {
            this.loadMessageActivityData.subscribe(data => {
                    this.loadMessageActivitySiteData.subscribe(data => {
                        this.emittedSiteIds = data;
                        if(this._sharedService.dashboadselectedTab == 'messageTrends') {
                            this.loadMessageActivityTrends(this._sharedService.dashboadselectedTab);
                        }else {
                            this.loadMessageActivity(this._sharedService.dashboadselectedTab);
                        }
                    });
            });
        }
    }
    loadMessageActivityTrends(tabName) {
        this._sharedService.dashboadselectedTab = tabName;
        $("#signatureTrends").trigger("click");
        let trendFilterObject = {
            start: moment().startOf('day').subtract(29, 'days').unix(),
            end: moment().endOf('day').unix(),
            label: 'Last 30 days by Week',
            chartType: '',
            legendDiv: '',
            chartDiv: '',
        } 
        trendFilterObject.chartType = 'TOTAL_MESSAGES_BY_MESSAGE_TYPE';
        trendFilterObject.legendDiv = '#chart-legend-activity-trend-by-message-type';
        trendFilterObject.chartDiv = '#chart-pie-activity-trend-by-message-type';
        this.filterMessageActivityTrend(trendFilterObject);
        // trendFilterObject.chartType = 'TOTAL_PATIENT_ENGAGEMENT_MISSED_OPPORTUNITIES';
        // trendFilterObject.legendDiv = '#chart-legend-activity-trend-by-patient-engagement';
        // trendFilterObject.chartDiv = '#chart-pie-activity-trend-by-patient-engagement';
        this.filterMessageActivityTrend(trendFilterObject);
        trendFilterObject.chartType = 'TAGGED_MESSAGES_BY_MESSAGE_TAGS';
        trendFilterObject.legendDiv = '#chart-legend-activity-trend-by-message-tags';
        trendFilterObject.chartDiv = '#chart-pie-activity-trend-by-message-tags';
        this.filterMessageActivityTrend(trendFilterObject);
        trendFilterObject.chartType = 'TOTAL_ACTIVE_MESSAGE_THREADS_BY_INITIATOR';
        trendFilterObject.legendDiv = '#chart-legend-activity-trend-trend-by-initiator';
        trendFilterObject.chartDiv = '#chart-pie-activity-trend-by-initiator';
        this.filterMessageActivityTrend(trendFilterObject);
        trendFilterObject.chartType = 'TOTAL_ACTIVE_MESSAGE_THREADS';
        trendFilterObject.legendDiv = '#chart-legend-activity-trend-by-message-thread';
        trendFilterObject.chartDiv = '#chart-pie-activity-trend-by-message-thread';
        this.filterMessageActivityTrend(trendFilterObject);
    }
    loadMessageActivity(tabName) {
        this._sharedService.dashboadselectedTab = tabName;
        $("#messageCenterActivitySummary").trigger("click");
        let filterObject = {
            start: moment().startOf('day').subtract(29, 'days').unix(),
            end: moment().endOf('day').unix(),
            label: 'Last 30 Days',
            chartType: '',
            legendDiv: '',
            chartDiv: '',
        }
        filterObject.chartType = 'TOTAL_MESSAGES_BY_MESSAGE_TYPE';
        filterObject.legendDiv = '#chart-legend-activity-by-message-type';
        filterObject.chartDiv = '#chart-pie-activity-by-message-type';
        this.filterMessageActivity(filterObject);
        filterObject.chartType = 'TOTAL_PATIENT_ENGAGEMENT_MISSED_OPPORTUNITIES';
        filterObject.legendDiv = '#chart-legend-activity-by-patient-engagement';
        filterObject.chartDiv = '#chart-pie-activity-by-patient-engagement';
        this.filterMessageActivity(filterObject);
        filterObject.chartType = 'TAGGED_MESSAGES_BY_MESSAGE_TAGS';
        filterObject.legendDiv = '#chart-legend-activity-by-message-tags';
        filterObject.chartDiv = '#chart-pie-activity-by-message-tags';
        this.filterMessageActivity(filterObject);
        filterObject.chartType = 'TOTAL_ACTIVE_MESSAGE_THREADS_BY_INITIATOR';
        filterObject.legendDiv = '#chart-legend-activity-by-initiator';
        filterObject.chartDiv = '#chart-pie-activity-by-initiator';
        this.filterMessageActivity(filterObject);
        filterObject.chartType = 'TOTAL_ACTIVE_MESSAGE_THREADS';
        filterObject.legendDiv = '#chart-legend-activity-by-message-thread';
        filterObject.chartDiv = '#chart-pie-activity-by-message-thread';
        this.filterMessageActivity(filterObject);
    }

    filterMessageActivity(filterObject) {
        let type = filterObject.chartType;
        let filterArguments = {
            start: filterObject.start,
            end: filterObject.end,
            type: filterObject.chartType,
            siteId:this.emittedSiteIds
        }
        console.log(filterArguments);
        let chartData = {
            legendDiv: filterObject.legendDiv,
            chartDiv: filterObject.chartDiv,
            chartData: [],
            chartLabels: [],
            backgroundColor: [],
            type: filterObject.chartType
        }
        let chartWidgetFilterLabel = '';
        if (filterObject.label == 'Custom Range')
            chartWidgetFilterLabel = moment.unix(filterObject.start).format("MMM Do, YYYY") + ' to ' + moment.unix(filterObject.end).format("MMM Do, YYYY");
        else
            chartWidgetFilterLabel = filterObject.label;
        this._structureService.getMessageCenterPieChartData(filterArguments).then((data) => {
            console.log(data);
            let activityByWorkFlow = JSON.parse(JSON.stringify(data['messageCenterPieChart']));
            let chartWidgetTotalCount = activityByWorkFlow.values.reduce((sum, current) => sum + parseInt(current), 0);
            chartData.chartData = activityByWorkFlow.values;
            if (activityByWorkFlow.labels.length > 0) {
                chartData.chartLabels = activityByWorkFlow.labels;
            } 
            chartData.backgroundColor = activityByWorkFlow.fillColors;
            switch (type) {
                case 'TOTAL_MESSAGES_BY_MESSAGE_TYPE':
                    this.messageByMessageType['filterLabel'] = chartWidgetFilterLabel;
                    this.messageByMessageType['totalCount'] = chartWidgetTotalCount;
                    break;
                case 'TOTAL_PATIENT_ENGAGEMENT_MISSED_OPPORTUNITIES':
                    this.messageByPatientEngagement['filterLabel'] = chartWidgetFilterLabel;
                    this.messageByPatientEngagement['totalCount'] = chartWidgetTotalCount;

                    break;
                case 'TAGGED_MESSAGES_BY_MESSAGE_TAGS':
                    this.messageByMessageTags['filterLabel'] = chartWidgetFilterLabel;
                    this.messageByMessageTags['totalCount'] = chartWidgetTotalCount;
                    break;
                case 'TOTAL_ACTIVE_MESSAGE_THREADS_BY_INITIATOR':
                    this.messageByInitiator['filterLabel'] = chartWidgetFilterLabel;
                    this.messageByInitiator['totalCount'] = chartWidgetTotalCount;
                    break;
                case 'TOTAL_ACTIVE_MESSAGE_THREADS':
                    this.messageByMessageThread['filterLabel'] = chartWidgetFilterLabel;
                    this.messageByMessageThread['totalCount'] = chartWidgetTotalCount;
                    break;
                default:
                    break;
            }
            console.log(chartData);
            if (type !== 'TOTAL_PATIENT_ENGAGEMENT_MISSED_OPPORTUNITIES') {
                this.renderChart(chartData);
            }
            
        });
    }
    preparePieChartBackgroundColorArray(arrayLength) {
        if (arrayLength < this.pieChartBackground.length) {
            return this.pieChartBackground;
        } else {
            let times = arrayLength / this.pieChartBackground.length;
            let newBackgroundArray = [];
            for (let i = 0; i < times; i++) {
                newBackgroundArray = [...this.pieChartBackground, ...newBackgroundArray];
            }
            return newBackgroundArray;
        }       
    }
    renderChart(chartData) {
        var pieCtx = $(chartData.chartDiv)[0].getContext('2d');
        pieCtx.height = 300;
        var options = {
            tooltips: {
                enabled: true,
                callbacks: {
                    label: function (tooltipItems, data) {
                        console.log(tooltipItems);
                        console.log(data);
                        if (data.labels[tooltipItems.index].length > 40) {
                            return data.labels[tooltipItems.index].substring(0, 40) + '...';
                        } else {
                            return data.labels[tooltipItems.index];
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true
            },
            legend: {
                display: false
            },
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                datalabels: {
                    formatter: (value, ctx) => {
                        console.log(value);
                        let sum = 0;
                        let dataArr = ctx.chart.data.datasets[0].data;
                        dataArr.map(data => {
                            sum += parseInt(data);
                        });
                        let percentage = Math.round(value * 100 / sum) + "%";
                        if (Math.round(value * 100 / sum) > 0) { return percentage; } else { return ''; }


                    },
                    color: '#fff',
                }
            },
            legendCallback: (chart) => {
                var text = [];
                text.push('<ul style="list-style: none; padding: 0;" class="' + chart.id + '-legend">');
                for (var i = 0; i < chartData.chartData.length; i++) {
                    // text.push('<li  stext.push('<li  style="display: flex;"  ><span style="height: 10px;min-width: 10px;margin-top: 5px;display: inline-block;margin-right: 10px;background-color:' + chart.data.datasets[0].backgroundColor[i] + '"></span>');
                    text.push('<li  style="display: flex;"  ><span style="height: 10px;min-width: 10px;margin-top: 5px;display: inline-block;margin-right: 10px;background-color:' + chart.data.datasets[0].backgroundColor[i] + '"></span>');
                    if (chart.data.labels[i]) {
                        text.push(chartData.chartData[i] + ' ' + chartData.chartLabels[i]);
                    }
                    text.push('</li>');
                }
                text.push('</ul>');
                return text.join("");
            }
        }
        var chartPie = {
            labels: chartData.chartLabels,
            datasets: [
                {
                    data: chartData.chartData,
                    backgroundColor: chartData.backgroundColor,
                    hoverBackgroundColor: chartData.backgroundColor,
                    borderWidth: 0
                }]
        };
        let chartConfig = new Chart(pieCtx, {
            type: 'pie',
            data: chartPie,
            options: options
        });
        this.destroyPreviousChart(chartData.type, chartConfig);
        $(chartData.legendDiv).html(chartConfig.generateLegend());
    }

    selectedDate(value: any, type: any, widgetType: any="") {
        value.event.target.value = value.picker.startDate.format('YYYY-MM-DD') + ' - ' + value.picker.endDate.format('YYYY-MM-DD');
        let filterObject = {
            start: value.picker.startDate.unix(),
            end: value.picker.endDate.unix(),
            label: value.picker.chosenLabel,
            chartType: type,
            legendDiv: '',
            chartDiv: '',
        };
        if (widgetType && widgetType!="") {
            switch (type) {
                case 'TOTAL_MESSAGES_BY_MESSAGE_TYPE':
                    filterObject.legendDiv = '#chart-legend-activity-trend-by-message-type';
                    filterObject.chartDiv = '#chart-pie-activity-trend-by-message-type';
                    break;
                case 'TOTAL_PATIENT_ENGAGEMENT_MISSED_OPPORTUNITIES':
                    filterObject.legendDiv = '#chart-legend-activity-trend-by-patient-engagement';
                    filterObject.chartDiv = '#chart-pie-activity-trend-by-patient-engagement';
                    break;
                case 'TAGGED_MESSAGES_BY_MESSAGE_TAGS':
                    filterObject.legendDiv = '#chart-legend-activity-trend-by-message-tags';
                    filterObject.chartDiv = '#chart-pie-activity-trend-by-message-tags';
                    break;
                case 'TOTAL_ACTIVE_MESSAGE_THREADS_BY_INITIATOR':
                    filterObject.legendDiv = '#chart-legend-activity-trend-trend-by-initiator';
                    filterObject.chartDiv = '#chart-pie-activity-trend-by-initiator';
                    break;
                case 'TOTAL_ACTIVE_MESSAGE_THREADS':
                    filterObject.legendDiv = '#chart-legend-activity-trend-by-message-thread';
                    filterObject.chartDiv = '#chart-pie-activity-trend-by-message-thread';
                    break;
                default:
                    filterObject.legendDiv = '#chart-legend-activity-trend-by-message-type';
                    filterObject.chartDiv = '#chart-pie-activity-trend-by-message-type';
                    break;
            }
            this.filterMessageActivityTrend(filterObject);
        } else {
            switch (type) {
                case 'TOTAL_MESSAGES_BY_MESSAGE_TYPE':
                    filterObject.legendDiv = '#chart-legend-activity-by-message-type';
                    filterObject.chartDiv = '#chart-pie-activity-by-message-type';
                    break;
                case 'TOTAL_PATIENT_ENGAGEMENT_MISSED_OPPORTUNITIES':
                    filterObject.legendDiv = '#chart-legend-activity-by-patient-engagement';
                    filterObject.chartDiv = '#chart-pie-activity-by-patient-engagement';
                    break;
                case 'TAGGED_MESSAGES_BY_MESSAGE_TAGS':
                    filterObject.legendDiv = '#chart-legend-activity-by-message-tags';
                    filterObject.chartDiv = '#chart-pie-activity-by-message-tags';
                    break;
                case 'TOTAL_ACTIVE_MESSAGE_THREADS_BY_INITIATOR':
                    filterObject.legendDiv = '#chart-legend-activity-by-initiator';
                    filterObject.chartDiv = '#chart-pie-activity-by-initiator';
                    break;
                case 'TOTAL_ACTIVE_MESSAGE_THREADS':
                    filterObject.legendDiv = '#chart-legend-activity-by-message-thread';
                    filterObject.chartDiv = '#chart-pie-activity-by-message-thread';
                    break;
                default:
                    filterObject.legendDiv = '#chart-legend-activity-by-message-type';
                    filterObject.chartDiv = '#chart-pie-activity-by-message-type';
                    break;
            }
            this.filterMessageActivity(filterObject);
        }

    }
    filterMessageActivityTrend(filterObject) {
        let type = filterObject.chartType;
        let filterArguments = {
            start: filterObject.start,
            end: filterObject.end,
            type: filterObject.chartType,
            siteId:this.emittedSiteIds,
            filterType: ''
        }
        
        let chartWidgetFilterLabel = '';
        if (filterObject.label == 'Custom Range')
            chartWidgetFilterLabel = moment.unix(filterObject.start).format("MMM Do, YYYY") + ' to ' + moment.unix(filterObject.end).format("MMM Do, YYYY");
        else
            chartWidgetFilterLabel = filterObject.label;
        if (chartWidgetFilterLabel == "Last 7 days by Day") {
            filterArguments.filterType = "DAILY";
        }
        else if (chartWidgetFilterLabel == "Last Quarter by Month" || chartWidgetFilterLabel == "Year to Date by Month") {
            filterArguments.filterType = "MONTHLY";
        } else if (chartWidgetFilterLabel == "Last Year by Quarter" || chartWidgetFilterLabel == "Year to Date by Quarter") {
            filterArguments.filterType = "QUARTERLY";
        }
        else {
            filterArguments.filterType = "WEEKLY";
        }
        let chartData = {
            legendDiv: filterObject.legendDiv,
            chartDiv: filterObject.chartDiv,
            chartData: [],
            chartLabels: [],
            labelType: filterArguments.filterType,
                backgroundColor: [],
            chartText: '',
            type: filterObject.chartType,
        }
        this._structureService.getMessageCenterLineChartData(filterArguments).then((data) => {
            let activityByWorkFlow = JSON.parse(JSON.stringify(data['messageCenterLineChart']));
            console.log('arrayLength' + activityByWorkFlow.length);
            if (activityByWorkFlow.length > 0) {
                chartData.chartData = activityByWorkFlow;
                switch (type) {
                    case 'TOTAL_MESSAGES_BY_MESSAGE_TYPE':
                        this.messageTrendByMessageType['filterLabel'] = chartWidgetFilterLabel;
                        chartData.chartText = this.messageTrendByMessageType.totalCountLabel;
                        break;
                    case 'TOTAL_PATIENT_ENGAGEMENT_MISSED_OPPORTUNITIES':
                        this.messageTrendByPatientEngagement['filterLabel'] = chartWidgetFilterLabel;
                        chartData.chartText = this.messageTrendByPatientEngagement.totalCountLabel;
                        break;
                    case 'TAGGED_MESSAGES_BY_MESSAGE_TAGS':
                        this.messageTrendByMessageTags['filterLabel'] = chartWidgetFilterLabel;
                        chartData.chartText = this.messageTrendByMessageTags.totalCountLabel;
                        break;
                    case 'TOTAL_ACTIVE_MESSAGE_THREADS_BY_INITIATOR':
                        this.messageTrendByInitiator['filterLabel'] = chartWidgetFilterLabel;
                        chartData.chartText = this.messageTrendByInitiator.totalCountLabel;
                        break;
                    case 'TOTAL_ACTIVE_MESSAGE_THREADS':
                        this.messageTrendByMessageThread['filterLabel'] = chartWidgetFilterLabel;
                        chartData.chartText = this.messageTrendByMessageThread.totalCountLabel;
                        break;
                    default:
                        break;
                }
                this.renderLineChart(chartData);
            }
        });
    }
    renderLineChart(chartData) {
        var ctx = $(chartData.chartDiv)[0].getContext('2d');
        ctx.height = 300;
        var labels = [];
        var dataset = [];
        if (chartData.labelType === 'WEEKLY') {
            //if (chartLabels.length > 2) {
            //let firstLabelValue = chartLabels[1];
            //if (firstLabelValue) {
            //let firstLabelNumber = parseInt(firstLabelValue.replace("Wk", ""));   
            let previousNumber;
            let previousYear;
            let previousLabelNumber;
            // chartLabels = chartData.chartData[0]['xAxisValues'].map((a,index) => {
            var chartLabels = [];
            chartData.chartData[0]['xAxisValues'].forEach((a, index) => {
                if (a) {
                    let labelElements = a.split(':');
                    let newLabelNumber;
                    let currentNumber = parseInt(labelElements[0].replace("Wk", ""));
                    let currentYear = parseInt(labelElements[1]);
                    if (index != 1) {
                        let difference = 0;
                        if (currentYear === previousYear) {
                            difference = currentNumber - previousNumber;
                            newLabelNumber = previousLabelNumber + difference;                            
                            chartLabels.push(`Wk${newLabelNumber}`);
                        } else {                           
                            if (moment().year(currentYear).weekday() === 0) {
                                difference = (moment(previousYear).isoWeeksInYear() - 1 - previousNumber)
                                    + currentNumber + 1;
                                newLabelNumber = previousLabelNumber + difference;
                                chartLabels.push(`Wk${newLabelNumber}`);
                            } else {
                               if (moment(previousYear).isoWeeksInYear()-1 == previousNumber && currentNumber == 0) {
                                    console.log("in here");
                                    chartData.chartData.forEach((y, itemIndex) => {
                                        y.yAxisValues[index - 1] = parseInt(y.yAxisValues[index - 1])
                                            + parseInt(y.yAxisValues[index]);
                                        chartData.chartData[itemIndex].yAxisValues.splice(index, 1);
                                    });
                                    newLabelNumber = previousLabelNumber;
                                    
                                } else {
                                    difference = (moment(previousYear).isoWeeksInYear()-1 - previousNumber)
                                        + currentNumber + 1;
                                    newLabelNumber = previousLabelNumber + difference ;
                                    chartLabels.push(`Wk${newLabelNumber}`);
                                }
                            }                           
                        }

                    } else {
                        newLabelNumber = 1;
                        chartLabels.push(`Wk${newLabelNumber}`);
                    }
                    previousNumber = currentNumber;
                    previousYear = currentYear;
                    previousLabelNumber = newLabelNumber;

                    // let xAxisNumber = parseInt(a.replace("Wk", ""));                            
                    // let newLabel = xAxisNumber - firstLabelNumber + 1;
                    // return 'Wk' + newLabel;
                } else {
                    chartLabels.push(a);
                }
            });
            //}
            //}
        } else {
            chartLabels = chartData.chartData[0]['xAxisValues'].map(a => { return a.split(':')[0]; });
        }
        chartData.chartData.forEach(element => {
            let dataObject = {};
            dataObject['label'] = element.label;
            dataObject['fill'] = "false";
            dataObject['borderColor'] = element.strokeColor;
            dataObject['data'] = element.yAxisValues;
            dataset.push(dataObject);
        });
        
        var textqu = chartData.chartText;;
        var options = {
            title: {
                display: true,
                text: textqu,
            },
            animation: {
                animateRotate: true,
                animateScale: true
            },
            legend: {
                display: false
            },
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                datalabels: {
                    formatter: (value, ctx) => {
                        let sum = 0;
                        let dataArr = ctx.chart.data.datasets[0].data;
                        dataArr.map(data => {
                            sum += data;
                        });
                        let percentage = value;
                        if (percentage != "0%") { return percentage; } else { return ''; }
                    },
                    color: '#000',
                }
            },
            legendCallback: (chart) => {
                var text = [];
                //if (this.getSumOfArray(chartData.chartData[0].yAxisValues) > 0) {
                    text.push('<ul style="list-style: none; padding: 0;" class="' + chart.id + '-legend">');
                
                    for (var i = 0; i < dataset.length; i++) {
                        text.push('<li  style="display: flex;"  ><span style="height: 10px;min-width: 10px;margin-top: 5px;margin-top: 5px;display: inline-block;margin-right: 10px;background-color:' + chart.data.datasets[i].borderColor + '"></span>');
                        console.log('*********');
                        if (i === 0) {
                            text.push('<b>' + this.getSumOfArray(chartData.chartData[i].yAxisValues) + ' ' + chart.data.datasets[i].label + '</b>');
                        } else {
                            text.push(this.getSumOfArray(chartData.chartData[i].yAxisValues) + ' ' + chart.data.datasets[i].label);

                        }
                        text.push('</li>');
                    }

                    text.push('</ul>');
                //}
                return text.join("");
            }
        
        }
        let chartLine = {
            labels: chartLabels,
            datasets: dataset
        };
        let lineChartConfig = new Chart(ctx, {
            type: 'line',
            data: chartLine,
            options: options
        });
        this.destroyPreviousChart(chartData.type, lineChartConfig);
        $(chartData.legendDiv).html(lineChartConfig.generateLegend());
    }
    dynamicColors() {
        var r = Math.floor(Math.random() * 255);
        var g = Math.floor(Math.random() * 255);
        var b = Math.floor(Math.random() * 255);
        return "rgb(" + r + "," + g + "," + b + ")";
    };
    destroyPreviousChart(type, config) {
        switch (type) {
            case 'TOTAL_MESSAGES_BY_MESSAGE_TYPE':
                if (this.messageTypeChart) {
                    this.messageTypeChart.destroy();
                }
                this.messageTypeChart = config;
                break;
            case 'TOTAL_ACTIVE_MESSAGE_THREADS_BY_INITIATOR':
                if (this.initiatorChart) {
                    this.initiatorChart.destroy();
                }
                this.initiatorChart = config;
                break;
            case 'TAGGED_MESSAGES_BY_MESSAGE_TAGS':
                if (this.messageTagChart) {
                    this.messageTagChart.destroy();
                }
                this.messageTagChart = config;
                break;
            case 'TOTAL_PATIENT_ENGAGEMENT_MISSED_OPPORTUNITIES':
                if (this.patientEngagementChart) {
                    this.patientEngagementChart.destroy();
                }
                this.patientEngagementChart = config;
                break;
            case 'TOTAL_ACTIVE_MESSAGE_THREADS':
                if (this.activeThreadChart) {
                    this.activeThreadChart.destroy();
                }
                this.activeThreadChart = config;
                break;
            default:
                break;
        }
    }
    getSumOfArray(valueArray) {
        let sum = 0;
        for (var j = 0; j < valueArray.length; j++) {
            if (valueArray[j]) {
                sum = sum + parseInt(valueArray[j]);
            }
        }
        return sum;
    }
    getLabelArray(type) {
        let labelArray = [];
        switch (type) {
            case 'TOTAL_MESSAGES_BY_MESSAGE_TYPE':
                labelArray = ['Active Staff/Patient Message Threads', 'Active Staff/Staff Message Threads','Active Patient Discussion Groups','Active Masked Messages', 'Active Broadcast Messages',  'Active Message Groups'];

                break;
            case 'TOTAL_ACTIVE_MESSAGE_THREADS_BY_INITIATOR':
                labelArray = ['Active Patient Initiated Threads', 'Active Staff - Patient Initiated Threads']; break;
            case 'TOTAL_PATIENT_ENGAGEMENT_MISSED_OPPORTUNITIES':
                labelArray = [
                    'Message Threads Sent by Patient/Caregiver Without Staff Responses',
                    'Messages Escalated'
                ];
                break;
            case 'TOTAL_ACTIVE_MESSAGE_THREADS':
                labelArray = [
                   
                    'Active Staff/Patient Message Threads', 'Active Staff/Staff Message Threads',
                    'Active Patient Discussion Groups', 'Active Masked Messages', 'Active Broadcast Messages', 'Active Message Groups'
                ];

                break;
            default:
                break;
        }
        return labelArray;
    }

}
