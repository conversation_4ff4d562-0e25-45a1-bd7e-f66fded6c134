import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { NO_ERRORS_SCHEMA } from '@angular/core';
import { FilterSitesComponent } from './filter-sites.component';
import { RouterTestingModule } from '@angular/router/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ManageSitesService } from 'app/structure/manage-sites/manage-sites.service';
import { HttpModule } from '@angular/http';
import { Apollo, ApolloModule } from 'apollo-angular';
import { StructureService } from 'app/structure/structure.service';
import { AuthService } from 'app/structure/shared/auth.service';
import { SharedService } from 'app/structure/shared/sharedServices';
import { LoggerModule, NgxLoggerLevel } from 'ngx-logger';
import { WorklistIndexdbService } from 'app/structure/worklists/worklist-indexdb.service';
import { ToolTipService } from 'app/structure/tool-tip.service';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { GlobalDataShareService } from 'app/structure/shared/global-data-share.service';
import { provideClients } from 'test-utils';
import { PermissionService } from 'app/services/permission/permission.service';
import { StoreService } from 'app/structure/shared/storeService';

describe('FilterSitesComponent', () => {
  let component: FilterSitesComponent;
  let fixture: ComponentFixture<FilterSitesComponent>;
  beforeEach(async(() => {
    TestBed.configureTestingModule({
      providers: [
        ManageSitesService,
        Apollo,
        StructureService,
        AuthService,
        SharedService,
        WorklistIndexdbService,
        ToolTipService,
        TranslateService,
        GlobalDataShareService,
        PermissionService,
        StoreService
      ],
      declarations: [FilterSitesComponent],
      imports: [
        RouterTestingModule,
        ReactiveFormsModule,
        FormsModule,
        HttpModule,
        ApolloModule.forRoot(provideClients),
        LoggerModule.forRoot({
          serverLoggingUrl: '',
          level: NgxLoggerLevel.OFF,
          serverLogLevel: NgxLoggerLevel.OFF,
        }),
        TranslateModule.forRoot(),
      ],
      schemas: [NO_ERRORS_SCHEMA],
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(FilterSitesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });
});
