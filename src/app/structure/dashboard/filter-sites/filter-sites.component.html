<form class="form-horizontal" id="site-list" name="site-list" #f="ngForm">
  <div class="form-body">
      <div class="form-group row">
          <div class="col-md-12">
              <angular2-multiselect [data]="siteDetails" [(ngModel)]="selectedItem" name="site"
                  [settings]="dropdownSettings" (onSelect)="onItemSelect()" (onDeSelect)="OnItemDeSelect()"
                  (onSelectAll)="onSelectAll()" (onDeSelectAll)="onDeSelectAll()"
                  (onFilterSelectAll)="onFilterSelectAll()" (onFilterDeSelectAll)="onFilterDeSelectAll()">
              </angular2-multiselect>
          </div>
      </div>
  </div>
</form>
