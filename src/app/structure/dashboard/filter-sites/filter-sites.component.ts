import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormsModule, FormBuilder, FormGroup, Validators, FormControl, FormArray, ReactiveFormsModule } from '@angular/forms';
import { ManageSitesService } from '../../manage-sites/manage-sites.service';
declare var $: any;
@Component({
  selector: 'app-filter-sites',
  templateUrl: './filter-sites.component.html',
  styleUrls: ['./filter-sites.component.css']
})
export class FilterSitesComponent implements OnInit {
  @Output() readonly siteIds: EventEmitter<any> = new EventEmitter<any>();
  @Output() readonly siteName: EventEmitter<any> = new EventEmitter<any>();
  @Input() singleSelection: boolean;
  @Input() userId: Number;
  @Input() filterType: boolean;
  siteList: FormGroup;
  siteDetails = [];
  selectedItem = [];
  dropdownSettings = {};
  items = [];
  constructor(
      private _formBuild: FormBuilder,
      private _managesitesService: ManageSitesService
  ) {

  }
  ngOnInit() {
      this.siteList = this._formBuild.group({
          site: ['']
      });
          this._managesitesService.getSitesByUserId(this.userId).then((res) => {
              const siteData = res['siteListByUserId'];
              if (siteData) {
                  const response = siteData['data'];
                  response.forEach((item) => {
                      this.siteDetails.push({ id: item.id, itemName: item.name });
                      this.filterType ? this.items.push({ id: item.id, itemName: item.name }) : '';
                  });
                  if (this.filterType) {
                      this.selectedItem = this.items;
                      this.emitItems();
                  }
              }
          });
      this.dropdownSettings = {
          singleSelection: this.singleSelection,
          text: "Select Site",
          selectAllText: 'All my sites',
          unSelectAllText: 'UnSelect all sites',
          classes: "myclass custom-class",
          enableSearchFilter: true,
          enableFilterSelectAll: (this.singleSelection) ? false : true
      };
  }
  onItemSelect(): void {
      this.emitItems();
  }
  OnItemDeSelect(): void {
      this.emitItems();
  }
  onSelectAll(): void {
      this.emitItems();
  }
  onDeSelectAll(): void {
      this.selectedItem = [];
      this.emitItems();
  }
  onFilterSelectAll(): void {
      this.emitItems();
  }
  onFilterDeSelectAll(): void {
      this.emitItems();
  }
  emitItems(): void {
      const ids = [];
      const name = [];
      this.selectedItem.forEach((value) => {
        console.log(value);
          ids.push(`${value.id}`);
          name.push(`${value.itemName}`);
      });
      this.siteIds.emit({ siteId: ids });
      this.siteName.emit({siteNames: name});
  }
}

