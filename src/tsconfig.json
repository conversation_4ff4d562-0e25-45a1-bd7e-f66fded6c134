{"compilerOptions": {"baseUrl": "", "declaration": false, "emitDecoratorMetadata": true, "experimentalDecorators": true, "removeComments": true, "skipLibCheck": true, "lib": ["es2016", "dom", "esnext.asynciterable"], "mapRoot": "./", "module": "es2015", "moduleResolution": "node", "outDir": "../dist/out-tsc", "sourceMap": true, "target": "es5", "typeRoots": ["../node_modules/@types"], "paths": {"exceljs": ["../node_modules/exceljs/dist/es5/exceljs.browser"]}}, "include": ["../node_modules/ngx-uploader/src/ngx-uploader/**/*.ts", "../node_modules/ngx-uploader/index.ts", "main.ts", "polyfills.ts", "test.ts", "test-utils.ts", "**/*.spec.ts"], "exclude": ["test.ts", "**/*.spec.ts"]}