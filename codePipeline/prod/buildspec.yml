version: 0.2

env:
  parameter-store:
    ssh_key: id_rsa_resmed
    ssh_pub: id_rsa_resmed.pub
  variables:
    git_url: "**************:resmed/citushealth-desktop-client.git"
    NODE_AUTH_TOKEN: $NODE_AUTH_TOKEN
phases:
  build:
    commands:
      - "npm install"      
      - "node --max_old_space_size=10240 ./node_modules/@angular/cli/bin/ng build --env=prod --prod=true --aot=true --buildOptimizer=true --extractCss=true  --outputHashing=all" 
  pre_build:
    commands:
      - "curl -sL https://deb.nodesource.com/setup_12.x | bash -"
      - "apt-get install -y nodejs zip tree"  
      - "node --version"
      - "echo authToken=$NODE_AUTH_TOKEN >> file.txt"
      - "cat file.txt"
      - aws s3 cp s3://ch-microservices-config-files/us-env/desktop/.npmrc .
      - "chmod +x .npmrc"
      - "cat .npmrc"
      - "ls -la /home/"
      - "mv .npmrc /root/" 
      - "pwd"
      - "whoami"
      - "npm install"
      - "npm i @angular/cli@1.6.3"
      - "cat /etc/*release"  
      - "echo \"$CODEBUILD_GIT_BRANCH\""
      - "echo \"$CODEBUILD_GIT_TAG\""
      - "mkdir -p ~/.ssh"
      - "echo \"$ssh_key\" > ~/.ssh/id_rsa"
      - "echo \"$ssh_pub\" > ~/.ssh/id_rsa.pub"
      - "chmod 600 ~/.ssh/id_rsa"
      - "eval \"$(ssh-agent -s)\""
      - "ssh-keygen -R github.com" 
      - "echo \"HOST *\" > ~/.ssh/config"
      - "echo \"StrictHostKeyChecking no\" >> ~/.ssh/config"
      - "git clone --recurse-submodules \"$git_url\" desktop"
      - "cd desktop"
      - aws s3 cp s3://ch-microservices-config-files/us-env/desktop/custom-configs.ts src/custom-configs.ts
      - "echo \"$git_url\""
      - "git remote -v"
      - "git branch"
      - "git checkout -f \"$CODEBUILD_RESOLVED_SOURCE_VERSION\""
      - "git submodule update --remote --recursive"
      - "echo \"$CODEBUILD_RESOLVED_SOURCE_VERSION\""
artifacts:
  files:
     - '**/*'
  base-directory: '$CODEBUILD_SRC_DIR/desktop/dist'
