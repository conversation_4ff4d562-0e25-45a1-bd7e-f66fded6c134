version: 0.2

env:
  parameter-store:
    ssh_key: id_rsa
    ssh_pub: id_rsa.pub
  variables:
    git_url: "**************:CitusHealth/citus-health-ui-desktop-all.git"
phases:
  build:
    commands:
      - "npm install"      
      - "node --max_old_space_size=10240 ./node_modules/@angular/cli/bin/ng build --env=prod --prod=true --aot=true --buildOptimizer=true --extractCss=true  --outputHashing=all" 
  pre_build:
    commands:
      - "curl -sL https://deb.nodesource.com/setup_12.x | bash -"
      - "apt-get install -y nodejs zip tree"  
      - "node --version"
      - "npm i @angular/cli@1.6.3"
      - "cat /etc/*release"  
      - "echo \"$CODEBUILD_GIT_BRANCH\""
      - "echo \"$CODEBUILD_GIT_TAG\""
      - "mkdir -p ~/.ssh"
      - "echo \"$ssh_key\" > ~/.ssh/id_rsa"
      - "echo \"$ssh_pub\" > ~/.ssh/id_rsa.pub"
      - "chmod 600 ~/.ssh/id_rsa"
      - "eval \"$(ssh-agent -s)\""
      - "git clone --recurse-submodules \"$git_url\" desktop"
      - "cd desktop"
      - "echo \"$git_url\""
      - "git remote -v"
      - "git branch"
      - "git checkout -f \"$CODEBUILD_RESOLVED_SOURCE_VERSION\""
      - "git submodule update --init"
      - "echo \"$CODEBUILD_RESOLVED_SOURCE_VERSION\""
  post_build:
    commands:
      - "zip -r -q \"$CODEBUILD_RESOLVED_SOURCE_VERSION\".zip dist/*"
      - "ls *.zip"
      - "aws s3 ls"  
      - "aws s3 cp *.zip s3://citushealthbuildartifacts/angular-desktop/"
#      - "aws s3 sync \"$CODEBUILD_RESOLVED_SOURCE_VERSION\".zip s3://citushealth-build-artifacts/desktop/"
artifacts:
  files:
    - '*.zip'
    - 'appspec.yml'
    - 'script/*'
    - 'dist/*'
  base-directory: '$CODEBUILD_SRC_DIR/desktop'
