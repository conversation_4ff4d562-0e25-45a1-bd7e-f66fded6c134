# This workflow will build and push a new container image to Amazon ECR,
# and then will deploy a new task definition to Amazon ECS, when a release is created
#
# To use this workflow, you will need to complete the following set-up steps:
#
# 1. Create an ECR repository to store your images.
#    For example: `aws ecr create-repository --repository-name my-ecr-repo --region us-east-2`.
#    Replace the value of `ECR_REPOSITORY` in the workflow below with your repository's name.
#    Replace the value of `aws-region` in the workflow below with your repository's region.
#
# 2. Create an ECS task definition, an ECS cluster, and an ECS service.
#    For example, follow the Getting Started guide on the ECS console:
#      https://us-east-2.console.aws.amazon.com/ecs/home?region=us-east-2#/firstRun
#    Replace the values for `service` and `cluster` in the workflow below with your service and cluster names.
#
# 3. Store your ECS task definition as a JSON file in your repository.
#    The format should follow the output of `aws ecs register-task-definition --generate-cli-skeleton`.
#    Replace the value of `task-definition` in the workflow below with your JSON file's name.
#    Replace the value of `container-name` in the workflow below with the name of the container
#    in the `containerDefinitions` section of the task definition.
#
# 4. Store an IAM user access key in GitHub Actions secrets named `AWS_ACCESS_KEY_ID` and `AWS_SECRET_ACCESS_KEY`.
#    See the documentation for each action used below for the recommended IAM policies for this IAM user,
#    and best practices on handling the access key credentials.

on:
  push:
    tags: kbf-releases/[1-9]+.[0-9]+.[0-9]+

name: KBF Production Docker Image Build

jobs:
  deploy:
    name: Deploy
    runs-on: ubuntu-latest

    steps:
    - name: Fix up git URLs
      run: |
        git config --global --add url."https://${{ secrets.GIT_ACTION_ACCESS_TOKEN }}:<EMAIL>/".insteadOf "https://github.com/"
        git config --global --add url."https://${{ secrets.GIT_ACTION_ACCESS_TOKEN }}:<EMAIL>/".insteadOf "**************:"
    - name: Checkout
      uses: actions/checkout@v1
      with:
        submodules: true
        token: ${{ secrets.GIT_ACTION_ACCESS_TOKEN }}
      
    - name: Download environment.ts file from S3.      
      uses: prewk/s3-cp-action@v1
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_US_EAST_KBF }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_KEY_ID_US_EAST_KBF }}
        AWS_REGION: 'us-east-1' # optional: defaults to us-east-1
        SOURCE: 's3://kbf-webapp-desktop-config/environment.ts'
        DEST: './src/environments/environment.ts'
        
    - name: Download favicon.ico file from S3.      
      uses: prewk/s3-cp-action@v1
      env:
        AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_US_EAST_KBF }}
        AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_KEY_ID_US_EAST_KBF }}
        AWS_REGION: 'us-east-1' # optional: defaults to us-east-1
        SOURCE: 's3://kbf-webapp-desktop-config/favicon.ico'
        DEST: './src/favicon.ico'

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_US_EAST_KBF }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_KEY_ID_US_EAST_KBF }}
        aws-region: us-east-1

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build, tag, and push image to Amazon ECR
      id: build-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        ECR_REPOSITORY: webapp-desktop
        IMAGE_TAG: ${{ github.sha }}
      run: |
        # Build a docker container and
        # push it to ECR so that it can
        # be deployed to ECS.
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -f './Dockerfile-kbf' .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"
