name: Code Quality Check

on:
  push:
  pull_request:

jobs:
  code-quality-check:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 14.x
          scope: '@resmed'
          registry-url: 'https://npm.pkg.github.com'

      - name: Install dependencies
        run: npm install --force
        env:
          NODE_AUTH_TOKEN: ${{ secrets.CITUS_NPM_AUTH_TOKEN }}
      - name: Install Google Chrome
        run: |
          sudo apt-get update
          sudo apt-get install -y wget
          wget https://dl.google.com/linux/direct/google-chrome-stable_current_amd64.deb
          sudo apt install -y ./google-chrome-stable_current_amd64.deb
      - name: Download custom-configs.ts
        timeout-minutes: 5
        run: sudo cp src/mocks/custom-configs.ts src/custom-configs.ts

      - name: Run unit tests
        run: npm run test:coverage:ci
        env:
          NODE_OPTIONS: --max_old_space_size=4096
          BROWSER_CONSOLE_LOG_TERMINAL: 'false'
      - name: Upload coverage report
        uses: actions/upload-artifact@v4
        if: success() || failure()
        with:
          name: coverage-report
          path: coverage/
          retention-days: 5
      - name: Compute SonarCloud project key
        id: compute-default
        uses: resmed/devx-action-sonarcloud-compute-project-key@main
      - name: Get changed files
        id: changed-files
        uses: tj-actions/changed-files@v42
        with:
          files: src/**
      - name: Format changed files for SonarCloud
        id: format-changed-files
        run: |
          # Join the changed files with commas
          CHANGED_FILES=$(echo "${{ steps.changed-files.outputs.all_changed_files }}" | tr ' ' ',')
          echo "Formatted changed files: $CHANGED_FILES"
          echo "::set-output name=formatted_files::$CHANGED_FILES"
      - name: SonarCloud Scan
        uses: SonarSource/sonarcloud-github-action@master
        with:
          args: >
            -Dsonar.organization=resmed
            -Dsonar.projectKey=${{ steps.compute-default.outputs.computed-project-key }}
            -Dsonar.sources=src
            -Dsonar.inclusions=${{ steps.format-changed-files.outputs.formatted_files }}
            -Dsonar.exclusions=src/assets/**,src/mocks/**,src/main.ts,src/polyfills.ts,src/test.ts,src/environments/**,src/*.json,src/*.js
            -Dsonar.tests=src/app
            -Dsonar.test.inclusions=src/**/*.spec.ts
            -Dsonar.javascript.lcov.reportPaths=coverage/lcov.info
            -Dsonar.typescript.tsconfigPath=src/tsconfig.json
            -Dsonar.sourceEncoding=UTF-8
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        timeout-minutes: 120
