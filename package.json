{"name": "clean-ui", "version": "2.1.0", "license": "MIT", "angular-cli": {}, "scripts": {"ng": "ng", "start": "webpack-dev-server --port=4200", "start:qa6": "node start-qa6.js", "start-ssl": "node --trace-deprecation --max_old_space_size=10240 ./node_modules/@angular/cli/bin/ng serve --env=qa6 --ssl true --ssl-key ssl/mdiachieve.key --ssl-cert ssl/mdiachieve.cer --host devcorp.mdiachieve.com --port 4201", "test": "karma start ./karma.conf.js", "test:coverage": "ng test --code-coverage", "test:coverage:ci": "ng test --code-coverage --no-watch --no-progress --browsers=ChromeHeadless", "lint": "eslint . --ext .ts", "e2e": "protractor ./protractor.conf.js", "build": "webpack", "pree2e": "webdriver-manager update --standalone false --gecko false --quiet"}, "private": true, "dependencies": {"@angular/common": "^5.2.10", "@angular/compiler": "^5.2.10", "@angular/core": "^5.2.10", "@angular/forms": "^5.2.10", "@angular/http": "^5.2.10", "@angular/platform-browser": "^5.2.10", "@angular/platform-browser-dynamic": "^5.2.10", "@angular/router": "^5.2.10", "@ng-bootstrap/ng-bootstrap": "1.1.2", "@ng-idle/core": "^2.0.0-beta.15", "@ng-idle/keepalive": "^2.0.0-beta.15", "@ngx-translate/core": "^9.1.1", "@ngx-translate/http-loader": "^2.0.1", "@okta/okta-angular": "^1.4.0", "@okta/okta-signin-widget": "^5.16.1", "@syncfusion/ej2-angular-notifications": "^17.3.14", "@types/intro.js": "^2.4.3", "ag-grid-angular": "^20.1.0", "ag-grid-community": "^20.1.0", "ag-grid-enterprise": "^20.1.0", "angular-file-uploader": "^7.0.1", "angular-mentions": "1.0.3", "angular-notifier": "^6.0.1", "angular-resizable-element": "^1.2.2", "angular2-customdraggable": "^2.1.0", "angular2-multiselect-dropdown": "4.6.6", "angular2-signaturepad": "^2.6.1", "angular2-text-mask": "^8.0.4", "apollo-angular": "^0.13.3", "apollo-client": "^1.9.2", "async": "^2.5.0", "autolinker": "^3.16.2", "core-js": "^2.6.12", "create-hmac": "^1.1.7", "crypto-js": "^4.2.0", "dexie": "3.0.2", "elevio": "^1.0.2", "exceljs": "1.12.0", "file-saver": "^2.0.5", "graphql-tag": "2.11.0", "guid-typescript": "^1.0.9", "intl": "^1.2.5", "intl-tel-input": "^19.2.16", "intro.js": "^2.9.3", "jspdf": "^1.3.5", "jspdf-autotable": "^2.3.2", "jstz": "^2.1.1", "moment": "^2.30.1", "moment-timezone": "^0.5.43", "mydatepicker": "^2.6.6", "ng-multiselect-dropdown": "0.2.5", "ng2-archwizard": "^2.1.0", "ng2-daterangepicker": "^2.0.12", "ngx-color-picker": "^4.5.3", "ngx-datetime-picker": "2.1.3", "ngx-doc-viewer": "^1.3.1", "ngx-icon-picker": "0.0.10", "ngx-logger": "3.0.5", "ngx-papaparse": "^1.2.5", "ngx-uploader": "^3.3.11", "rxjs": "^5.5.10", "sass": "^1.89.2", "socket.io-client": "4.2.0", "text-mask-addons": "^3.7.1", "ts-optchain": "^0.1.8", "zone.js": "^0.11.2"}, "devDependencies": {"@angular-devkit/core": "0.3.2", "@angular-eslint/eslint-plugin": "^15.2.1", "@angular-eslint/eslint-plugin-template": "^15.2.1", "@angular-eslint/template-parser": "^15.2.1", "@angular/cli": "^1.7.4", "@angular/compiler-cli": "5.2.10", "@ngtools/webpack": "1.10.2", "@types/crypto-js": "^3.1.47", "@types/jasmine": "2.5.38", "@types/node": "~6.0.60", "@types/semver": "7.5.3", "@typescript-eslint/eslint-plugin": "^5.60.0", "@typescript-eslint/parser": "^5.16.0", "autoprefixer": "^7.2.3", "circular-dependency-plugin": "^4.2.1", "codelyzer": "~2.0.0-beta.4", "copy-webpack-plugin": "~4.4.1", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^14.2.1", "eslint-config-prettier": "^8.7.0", "eslint-plugin-diff": "^2.0.3", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.2.1", "file-loader": "^1.1.5", "html-webpack-plugin": "^2.29.0", "husky": "^9.1.7", "istanbul-instrumenter-loader": "^3.0.0", "jasmine-core": "~2.5.2", "jasmine-spec-reporter": "~3.2.0", "karma": "~1.4.1", "karma-chrome-launcher": "~2.0.0", "karma-coverage-istanbul-reporter": "^0.2.0", "karma-jasmine": "~1.1.0", "karma-jasmine-html-reporter": "^0.2.2", "less-loader": "^4.0.5", "lint-staged": "^15.2.11", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.10", "postcss-url": "^7.1.2", "prettier": "^2.8.5", "protractor": "~5.1.0", "raw-loader": "^0.5.1", "sass-loader": "^7.3.1", "style-loader": "^0.19.1", "stylus-loader": "^3.0.1", "ts-node": "latest", "typescript": "^2.6.2", "uglifyjs-webpack-plugin": "^1.1.8", "url-loader": "^0.6.2", "webpack": "~3.11.0", "webpack-dev-server": "~2.11.0"}}