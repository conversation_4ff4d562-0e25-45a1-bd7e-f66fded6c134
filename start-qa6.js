#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// Read the webpack config
const webpackConfigPath = path.join(__dirname, 'webpack.config.js');
let webpackConfig = fs.readFileSync(webpackConfigPath, 'utf8');

// Replace the environment file mapping for qa6
const originalMapping = '"environments/environment.ts": "environments/environment.ts"';
const qa6Mapping = '"environments/environment.ts": "environments/environment.qa6.ts"';

// Create a backup of the original config
const backupPath = webpackConfigPath + '.backup';
if (!fs.existsSync(backupPath)) {
  fs.writeFileSync(backupPath, webpackConfig);
}

// Replace the environment mapping
const modifiedConfig = webpackConfig.replace(originalMapping, qa6Mapping);
fs.writeFileSync(webpackConfigPath, modifiedConfig);

console.log('✅ Updated webpack config for QA6 environment');
console.log('🚀 Starting webpack-dev-server...');

// Start webpack-dev-server
const webpackDevServer = spawn('./node_modules/.bin/webpack-dev-server', ['--port=4200'], {
  stdio: 'inherit',
  cwd: __dirname
});

// Handle cleanup on exit
process.on('SIGINT', () => {
  console.log('\n🔄 Restoring original webpack config...');
  const originalConfig = fs.readFileSync(backupPath, 'utf8');
  fs.writeFileSync(webpackConfigPath, originalConfig);
  console.log('✅ Webpack config restored');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🔄 Restoring original webpack config...');
  const originalConfig = fs.readFileSync(backupPath, 'utf8');
  fs.writeFileSync(webpackConfigPath, originalConfig);
  console.log('✅ Webpack config restored');
  process.exit(0);
});

webpackDevServer.on('close', (code) => {
  console.log('\n🔄 Restoring original webpack config...');
  const originalConfig = fs.readFileSync(backupPath, 'utf8');
  fs.writeFileSync(webpackConfigPath, originalConfig);
  console.log('✅ Webpack config restored');
  process.exit(code);
});
