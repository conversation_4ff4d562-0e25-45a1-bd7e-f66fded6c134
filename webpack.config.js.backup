const fs = require('fs');
const path = require('path');
const CopyWebpackPlugin = require('copy-webpack-plugin');
const ProgressPlugin = require('webpack/lib/ProgressPlugin');
const CircularDependencyPlugin = require('circular-dependency-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const rxPaths = require('rxjs/_esm5/path-mapping');
const autoprefixer = require('autoprefixer');
const postcssUrl = require('postcss-url');
const postcssImports = require('postcss-import');

const { NoEmitOnErrorsPlugin, SourceMapDevToolPlugin, NamedModulesPlugin } = require('webpack');
const { ScriptsWebpackPlugin, NamedLazyChunksWebpackPlugin, BaseHrefWebpackPlugin, PostcssCliResources } = require('@angular/cli/plugins/webpack');
const { CommonsChunkPlugin } = require('webpack').optimize;
const { AngularCompilerPlugin } = require('@ngtools/webpack');

const nodeModules = path.join(process.cwd(), 'node_modules');
const realNodeModules = fs.realpathSync(nodeModules);
const genDirNodeModules = path.join(process.cwd(), 'src', '$$_gendir', 'node_modules');
const entryPoints = ["inline","polyfills","sw-register","styles","scripts","vendor","main"];
const hashFormat = {"chunk":"","extract":"","file":".[hash:20]","script":""};
const baseHref = "";
const deployUrl = "";
const projectRoot = process.cwd();
const maximumInlineSize = 10;
const postcssPlugins = function (loader) {
        return [
            postcssImports({
                resolve: (url, context) => {
                    return new Promise((resolve, reject) => {
                        let hadTilde = false;
                        if (url && url.startsWith('~')) {
                            url = url.substr(1);
                            hadTilde = true;
                        }
                        loader.resolve(context, (hadTilde ? '' : './') + url, (err, result) => {
                            if (err) {
                                if (hadTilde) {
                                    reject(err);
                                    return;
                                }
                                loader.resolve(context, url, (err, result) => {
                                    if (err) {
                                        reject(err);
                                    }
                                    else {
                                        resolve(result);
                                    }
                                });
                            }
                            else {
                                resolve(result);
                            }
                        });
                    });
                },
                load: (filename) => {
                    return new Promise((resolve, reject) => {
                        loader.fs.readFile(filename, (err, data) => {
                            if (err) {
                                reject(err);
                                return;
                            }
                            const content = data.toString();
                            resolve(content);
                        });
                    });
                }
            }),
            postcssUrl({
                filter: ({ url }) => url.startsWith('~'),
                url: ({ url }) => {
                    const fullPath = path.join(projectRoot, 'node_modules', url.substr(1));
                    return path.relative(loader.context, fullPath).replace(/\\/g, '/');
                }
            }),
            postcssUrl([
                {
                    // Only convert root relative URLs, which CSS-Loader won't process into require().
                    filter: ({ url }) => url.startsWith('/') && !url.startsWith('//'),
                    url: ({ url }) => {
                        if (deployUrl.match(/:\/\//) || deployUrl.startsWith('/')) {
                            // If deployUrl is absolute or root relative, ignore baseHref & use deployUrl as is.
                            return `${deployUrl.replace(/\/$/, '')}${url}`;
                        }
                        else if (baseHref.match(/:\/\//)) {
                            // If baseHref contains a scheme, include it as is.
                            return baseHref.replace(/\/$/, '') +
                                `/${deployUrl}/${url}`.replace(/\/\/+/g, '/');
                        }
                        else {
                            // Join together base-href, deploy-url and the original URL.
                            // Also dedupe multiple slashes into single ones.
                            return `/${baseHref}/${deployUrl}/${url}`.replace(/\/\/+/g, '/');
                        }
                    }
                },
                {
                    // TODO: inline .cur if not supporting IE (use browserslist to check)
                    filter: (asset) => {
                        return maximumInlineSize > 0 && !asset.hash && !asset.absolutePath.endsWith('.cur');
                    },
                    url: 'inline',
                    // NOTE: maxSize is in KB
                    maxSize: maximumInlineSize,
                    fallback: 'rebase',
                },
                { url: 'rebase' },
            ]),
            PostcssCliResources({
                deployUrl: loader.loaders[loader.loaderIndex].options.ident == 'extracted' ? '' : deployUrl,
                loader,
                filename: `[name]${hashFormat.file}.[ext]`,
            }),
            autoprefixer({ grid: true }),
        ];
    };




module.exports = {
  "resolve": {
    "extensions": [
      ".ts",
      ".js"
    ],
    "symlinks": true,
    "modules": [
      "./src",
      "./node_modules"
    ],
    "alias": rxPaths(),
    "mainFields": [
      "browser",
      "module",
      "main"
    ]
  },
  "resolveLoader": {
    "modules": [
      "./node_modules",
      "./node_modules/@angular/cli/node_modules"
    ],
    "alias": rxPaths()
  },
  "entry": {
    "main": [
      "./src/main.ts"
    ],
    "polyfills": [
      "./src/polyfills.ts"
    ],
    "styles": [
      "./src/assets/css/icons.scss",
      "./src/assets/preloader.css",
      "./src/assets/vendors/by_bower/bootstrap/dist/css/bootstrap.min.css",
      "./src/assets/vendors/by_bower/jscrollpane/style/jquery.jscrollpane.css",
      "./src/assets/vendors/by_bower/ladda/dist/ladda-themeless.min.css",
      "./src/assets/vendors/by_bower/bootstrap-select/dist/css/bootstrap-select.min.css",
      "./src/assets/vendors/by_bower/select2/dist/css/select2.min.css",
      "./src/assets/vendors/by_bower/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css",
      "./src/assets/vendors/by_bower/fullcalendar/dist/fullcalendar.css",
      "./src/assets/vendors/by_bower/bootstrap-sweetalert/dist/sweetalert.css",
      "./src/assets/vendors/by_bower/summernote/dist/summernote.css",
      "./src/assets/vendors/by_bower/owl.carousel/dist/assets/owl.carousel.min.css",
      "./src/assets/vendors/by_bower/ionrangeslider/css/ion.rangeSlider.css",
      "./src/assets/vendors/by_bower/datatables/media/css/dataTables.bootstrap5.min.css",
      "./src/assets/vendors/by_bower/c3/c3.min.css",
      "./src/assets/vendors/by_bower/chartist/dist/chartist.min.css",
      "./src/assets/vendors/by_bower/nprogress/nprogress.css",
      "./src/assets/vendors/by_bower/jquery-steps/demo/css/jquery.steps.css",
      "./src/assets/vendors/by_bower/dropify/dist/css/dropify.min.css",
      "./src/assets/vendors/by_hands/font-linearicons/style.css",
      "./src/assets/vendors/by_hands/font-icomoon/style.css",
      "./src/assets/vendors/by_hands/font-awesome/css/font-awesome.min.css",
      "./src/assets/vendors/by_hands/cleanhtmlaudioplayer/src/player.css",
      "./src/assets/vendors/by_hands/cleanhtmlvideoplayer/src/player.css",
      "./src/assets/modules/core/common/core.cleanui.css",
      "./src/assets/modules/vendors/common/vendors.cleanui.css",
      "./src/assets/modules/layouts/common/layouts-pack.cleanui.css",
      "./src/assets/modules/themes/common/themes.cleanui.css",
      "./src/assets/modules/menu-left/common/menu-left.cleanui.css",
      "./src/assets/modules/menu-right/common/menu-right.cleanui.css",
      "./src/assets/modules/top-bar/common/top-bar.cleanui.css",
      "./src/assets/modules/footer/common/footer.cleanui.css",
      "./src/assets/modules/pages/common/pages.cleanui.css",
      "./src/assets/modules/ecommerce/common/ecommerce.cleanui.css",
      "./src/assets/modules/apps/common/apps.cleanui.css",
      "./src/assets/lib/emoji/emojionearea.css",
      "./node_modules/intro.js/introjs.css",
      "./node_modules/ag-grid-community/dist/styles/ag-grid.css",
      "./node_modules/ag-grid-community/dist/styles/ag-theme-balham.css",
      "./node_modules/@syncfusion/ej2-base/styles/fabric.css",
      "./node_modules/@syncfusion/ej2-buttons/styles/fabric.css",
      "./node_modules/@syncfusion/ej2-popups/styles/fabric.css",
      "./node_modules/intl-tel-input/build/css/intlTelInput.css",
      "./node_modules/@syncfusion/ej2-angular-notifications/styles/fabric.css",
      "./src/assets/css/site-selection-style.css",
      "./src/assets/css/ngx-datetime-picker.css",
      "./node_modules/@okta/okta-signin-widget/dist/css/okta-sign-in.min.css",
      "./src/assets/css/okta-login-style.scss"
    ]
  },
  "output": {
    "path": path.join(process.cwd(), "dist"),
    "filename": "[name].bundle.js",
    "chunkFilename": "[id].chunk.js",
    "crossOriginLoading": false
  },
  "module": {
    "rules": [
      {
        "test": /\.html$/,
        "loader": "raw-loader"
      },
      {
        "test": /\.(eot|svg|cur)$/,
        "loader": "file-loader",
        "options": {
          "name": "[name].[hash:20].[ext]",
          "limit": 10000
        }
      },
      {
        "test": /\.(jpg|png|webp|gif|otf|ttf|woff|woff2|ani)$/,
        "loader": "url-loader",
        "options": {
          "name": "[name].[hash:20].[ext]",
          "limit": 10000
        }
      },
      {
        "exclude": [
          path.join(process.cwd(), "src/assets/css/icons.scss"),
          path.join(process.cwd(), "src/assets/preloader.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap/dist/css/bootstrap.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/jscrollpane/style/jquery.jscrollpane.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/ladda/dist/ladda-themeless.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap-select/dist/css/bootstrap-select.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/select2/dist/css/select2.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/fullcalendar/dist/fullcalendar.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap-sweetalert/dist/sweetalert.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/summernote/dist/summernote.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/owl.carousel/dist/assets/owl.carousel.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/ionrangeslider/css/ion.rangeSlider.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/datatables/media/css/dataTables.bootstrap5.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/c3/c3.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/chartist/dist/chartist.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/nprogress/nprogress.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/jquery-steps/demo/css/jquery.steps.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/dropify/dist/css/dropify.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-linearicons/style.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-icomoon/style.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-awesome/css/font-awesome.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/cleanhtmlaudioplayer/src/player.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/cleanhtmlvideoplayer/src/player.css"),
          path.join(process.cwd(), "src/assets/modules/core/common/core.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/vendors/common/vendors.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/layouts/common/layouts-pack.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/themes/common/themes.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/menu-left/common/menu-left.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/menu-right/common/menu-right.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/top-bar/common/top-bar.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/footer/common/footer.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/pages/common/pages.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/ecommerce/common/ecommerce.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/apps/common/apps.cleanui.css"),
          path.join(process.cwd(), "src/assets/lib/emoji/emojionearea.css"),
          path.join(process.cwd(), "node_modules/intro.js/introjs.css"),
          path.join(process.cwd(), "node_modules/ag-grid-community/dist/styles/ag-grid.css"),
          path.join(process.cwd(), "node_modules/ag-grid-community/dist/styles/ag-theme-balham.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-base/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-buttons/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-popups/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/intl-tel-input/build/css/intlTelInput.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-angular-notifications/styles/fabric.css"),
          path.join(process.cwd(), "src/assets/css/site-selection-style.css"),
          path.join(process.cwd(), "src/assets/css/ngx-datetime-picker.css"),
          path.join(process.cwd(), "node_modules/@okta/okta-signin-widget/dist/css/okta-sign-in.min.css"),
          path.join(process.cwd(), "src/assets/css/okta-login-style.scss")
        ],
        "test": /\.css$/,
        "use": [
          {
            "loader": "raw-loader"
          },
          {
            "loader": "postcss-loader",
            "options": {
              "ident": "embedded",
              "plugins": postcssPlugins,
              "sourceMap": true
            }
          }
        ]
      },
      {
        "exclude": [
          path.join(process.cwd(), "src/assets/css/icons.scss"),
          path.join(process.cwd(), "src/assets/preloader.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap/dist/css/bootstrap.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/jscrollpane/style/jquery.jscrollpane.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/ladda/dist/ladda-themeless.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap-select/dist/css/bootstrap-select.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/select2/dist/css/select2.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/fullcalendar/dist/fullcalendar.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap-sweetalert/dist/sweetalert.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/summernote/dist/summernote.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/owl.carousel/dist/assets/owl.carousel.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/ionrangeslider/css/ion.rangeSlider.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/datatables/media/css/dataTables.bootstrap5.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/c3/c3.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/chartist/dist/chartist.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/nprogress/nprogress.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/jquery-steps/demo/css/jquery.steps.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/dropify/dist/css/dropify.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-linearicons/style.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-icomoon/style.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-awesome/css/font-awesome.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/cleanhtmlaudioplayer/src/player.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/cleanhtmlvideoplayer/src/player.css"),
          path.join(process.cwd(), "src/assets/modules/core/common/core.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/vendors/common/vendors.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/layouts/common/layouts-pack.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/themes/common/themes.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/menu-left/common/menu-left.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/menu-right/common/menu-right.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/top-bar/common/top-bar.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/footer/common/footer.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/pages/common/pages.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/ecommerce/common/ecommerce.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/apps/common/apps.cleanui.css"),
          path.join(process.cwd(), "src/assets/lib/emoji/emojionearea.css"),
          path.join(process.cwd(), "node_modules/intro.js/introjs.css"),
          path.join(process.cwd(), "node_modules/ag-grid-community/dist/styles/ag-grid.css"),
          path.join(process.cwd(), "node_modules/ag-grid-community/dist/styles/ag-theme-balham.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-base/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-buttons/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-popups/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/intl-tel-input/build/css/intlTelInput.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-angular-notifications/styles/fabric.css"),
          path.join(process.cwd(), "src/assets/css/site-selection-style.css"),
          path.join(process.cwd(), "src/assets/css/ngx-datetime-picker.css"),
          path.join(process.cwd(), "node_modules/@okta/okta-signin-widget/dist/css/okta-sign-in.min.css"),
          path.join(process.cwd(), "src/assets/css/okta-login-style.scss")
        ],
        "test": /\.scss$|\.sass$/,
        "use": [
          {
            "loader": "raw-loader"
          },
          {
            "loader": "postcss-loader",
            "options": {
              "ident": "embedded",
              "plugins": postcssPlugins,
              "sourceMap": true
            }
          },
          {
            "loader": "sass-loader",
            "options": {
              "implementation": require("sass"),
              "sourceMap": true,
              "precision": 8,
              "includePaths": []
            }
          }
        ]
      },
      {
        "exclude": [
          path.join(process.cwd(), "src/assets/css/icons.scss"),
          path.join(process.cwd(), "src/assets/preloader.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap/dist/css/bootstrap.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/jscrollpane/style/jquery.jscrollpane.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/ladda/dist/ladda-themeless.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap-select/dist/css/bootstrap-select.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/select2/dist/css/select2.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/fullcalendar/dist/fullcalendar.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap-sweetalert/dist/sweetalert.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/summernote/dist/summernote.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/owl.carousel/dist/assets/owl.carousel.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/ionrangeslider/css/ion.rangeSlider.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/datatables/media/css/dataTables.bootstrap5.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/c3/c3.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/chartist/dist/chartist.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/nprogress/nprogress.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/jquery-steps/demo/css/jquery.steps.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/dropify/dist/css/dropify.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-linearicons/style.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-icomoon/style.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-awesome/css/font-awesome.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/cleanhtmlaudioplayer/src/player.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/cleanhtmlvideoplayer/src/player.css"),
          path.join(process.cwd(), "src/assets/modules/core/common/core.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/vendors/common/vendors.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/layouts/common/layouts-pack.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/themes/common/themes.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/menu-left/common/menu-left.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/menu-right/common/menu-right.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/top-bar/common/top-bar.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/footer/common/footer.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/pages/common/pages.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/ecommerce/common/ecommerce.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/apps/common/apps.cleanui.css"),
          path.join(process.cwd(), "src/assets/lib/emoji/emojionearea.css"),
          path.join(process.cwd(), "node_modules/intro.js/introjs.css"),
          path.join(process.cwd(), "node_modules/ag-grid-community/dist/styles/ag-grid.css"),
          path.join(process.cwd(), "node_modules/ag-grid-community/dist/styles/ag-theme-balham.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-base/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-buttons/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-popups/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/intl-tel-input/build/css/intlTelInput.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-angular-notifications/styles/fabric.css"),
          path.join(process.cwd(), "src/assets/css/site-selection-style.css"),
          path.join(process.cwd(), "src/assets/css/ngx-datetime-picker.css"),
          path.join(process.cwd(), "node_modules/@okta/okta-signin-widget/dist/css/okta-sign-in.min.css"),
          path.join(process.cwd(), "src/assets/css/okta-login-style.scss")
        ],
        "test": /\.less$/,
        "use": [
          {
            "loader": "raw-loader"
          },
          {
            "loader": "postcss-loader",
            "options": {
              "ident": "embedded",
              "plugins": postcssPlugins,
              "sourceMap": true
            }
          },
          {
            "loader": "less-loader",
            "options": {
              "sourceMap": true
            }
          }
        ]
      },
      {
        "exclude": [
          path.join(process.cwd(), "src/assets/css/icons.scss"),
          path.join(process.cwd(), "src/assets/preloader.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap/dist/css/bootstrap.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/jscrollpane/style/jquery.jscrollpane.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/ladda/dist/ladda-themeless.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap-select/dist/css/bootstrap-select.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/select2/dist/css/select2.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/fullcalendar/dist/fullcalendar.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap-sweetalert/dist/sweetalert.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/summernote/dist/summernote.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/owl.carousel/dist/assets/owl.carousel.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/ionrangeslider/css/ion.rangeSlider.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/datatables/media/css/dataTables.bootstrap5.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/c3/c3.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/chartist/dist/chartist.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/nprogress/nprogress.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/jquery-steps/demo/css/jquery.steps.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/dropify/dist/css/dropify.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-linearicons/style.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-icomoon/style.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-awesome/css/font-awesome.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/cleanhtmlaudioplayer/src/player.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/cleanhtmlvideoplayer/src/player.css"),
          path.join(process.cwd(), "src/assets/modules/core/common/core.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/vendors/common/vendors.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/layouts/common/layouts-pack.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/themes/common/themes.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/menu-left/common/menu-left.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/menu-right/common/menu-right.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/top-bar/common/top-bar.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/footer/common/footer.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/pages/common/pages.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/ecommerce/common/ecommerce.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/apps/common/apps.cleanui.css"),
          path.join(process.cwd(), "src/assets/lib/emoji/emojionearea.css"),
          path.join(process.cwd(), "node_modules/intro.js/introjs.css"),
          path.join(process.cwd(), "node_modules/ag-grid-community/dist/styles/ag-grid.css"),
          path.join(process.cwd(), "node_modules/ag-grid-community/dist/styles/ag-theme-balham.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-base/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-buttons/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-popups/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/intl-tel-input/build/css/intlTelInput.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-angular-notifications/styles/fabric.css"),
          path.join(process.cwd(), "src/assets/css/site-selection-style.css"),
          path.join(process.cwd(), "src/assets/css/ngx-datetime-picker.css"),
          path.join(process.cwd(), "node_modules/@okta/okta-signin-widget/dist/css/okta-sign-in.min.css"),
          path.join(process.cwd(), "src/assets/css/okta-login-style.scss")
        ],
        "test": /\.styl$/,
        "use": [
          {
            "loader": "raw-loader"
          },
          {
            "loader": "postcss-loader",
            "options": {
              "ident": "embedded",
              "plugins": postcssPlugins,
              "sourceMap": true
            }
          },
          {
            "loader": "stylus-loader",
            "options": {
              "sourceMap": true,
              "paths": []
            }
          }
        ]
      },
      {
        "include": [
          path.join(process.cwd(), "src/assets/css/icons.scss"),
          path.join(process.cwd(), "src/assets/preloader.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap/dist/css/bootstrap.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/jscrollpane/style/jquery.jscrollpane.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/ladda/dist/ladda-themeless.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap-select/dist/css/bootstrap-select.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/select2/dist/css/select2.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/fullcalendar/dist/fullcalendar.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap-sweetalert/dist/sweetalert.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/summernote/dist/summernote.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/owl.carousel/dist/assets/owl.carousel.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/ionrangeslider/css/ion.rangeSlider.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/datatables/media/css/dataTables.bootstrap5.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/c3/c3.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/chartist/dist/chartist.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/nprogress/nprogress.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/jquery-steps/demo/css/jquery.steps.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/dropify/dist/css/dropify.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-linearicons/style.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-icomoon/style.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-awesome/css/font-awesome.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/cleanhtmlaudioplayer/src/player.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/cleanhtmlvideoplayer/src/player.css"),
          path.join(process.cwd(), "src/assets/modules/core/common/core.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/vendors/common/vendors.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/layouts/common/layouts-pack.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/themes/common/themes.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/menu-left/common/menu-left.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/menu-right/common/menu-right.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/top-bar/common/top-bar.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/footer/common/footer.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/pages/common/pages.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/ecommerce/common/ecommerce.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/apps/common/apps.cleanui.css"),
          path.join(process.cwd(), "src/assets/lib/emoji/emojionearea.css"),
          path.join(process.cwd(), "node_modules/intro.js/introjs.css"),
          path.join(process.cwd(), "node_modules/ag-grid-community/dist/styles/ag-grid.css"),
          path.join(process.cwd(), "node_modules/ag-grid-community/dist/styles/ag-theme-balham.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-base/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-buttons/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-popups/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/intl-tel-input/build/css/intlTelInput.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-angular-notifications/styles/fabric.css"),
          path.join(process.cwd(), "src/assets/css/site-selection-style.css"),
          path.join(process.cwd(), "src/assets/css/ngx-datetime-picker.css"),
          path.join(process.cwd(), "node_modules/@okta/okta-signin-widget/dist/css/okta-sign-in.min.css"),
          path.join(process.cwd(), "src/assets/css/okta-login-style.scss")
        ],
        "test": /\.css$/,
        "use": [
          "style-loader",
          {
            "loader": "raw-loader"
          },
          {
            "loader": "postcss-loader",
            "options": {
              "ident": "embedded",
              "plugins": postcssPlugins,
              "sourceMap": true
            }
          }
        ]
      },
      {
        "include": [
          path.join(process.cwd(), "src/assets/css/icons.scss"),
          path.join(process.cwd(), "src/assets/preloader.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap/dist/css/bootstrap.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/jscrollpane/style/jquery.jscrollpane.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/ladda/dist/ladda-themeless.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap-select/dist/css/bootstrap-select.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/select2/dist/css/select2.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/fullcalendar/dist/fullcalendar.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap-sweetalert/dist/sweetalert.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/summernote/dist/summernote.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/owl.carousel/dist/assets/owl.carousel.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/ionrangeslider/css/ion.rangeSlider.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/datatables/media/css/dataTables.bootstrap5.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/c3/c3.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/chartist/dist/chartist.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/nprogress/nprogress.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/jquery-steps/demo/css/jquery.steps.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/dropify/dist/css/dropify.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-linearicons/style.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-icomoon/style.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-awesome/css/font-awesome.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/cleanhtmlaudioplayer/src/player.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/cleanhtmlvideoplayer/src/player.css"),
          path.join(process.cwd(), "src/assets/modules/core/common/core.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/vendors/common/vendors.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/layouts/common/layouts-pack.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/themes/common/themes.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/menu-left/common/menu-left.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/menu-right/common/menu-right.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/top-bar/common/top-bar.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/footer/common/footer.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/pages/common/pages.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/ecommerce/common/ecommerce.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/apps/common/apps.cleanui.css"),
          path.join(process.cwd(), "src/assets/lib/emoji/emojionearea.css"),
          path.join(process.cwd(), "node_modules/intro.js/introjs.css"),
          path.join(process.cwd(), "node_modules/ag-grid-community/dist/styles/ag-grid.css"),
          path.join(process.cwd(), "node_modules/ag-grid-community/dist/styles/ag-theme-balham.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-base/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-buttons/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-popups/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/intl-tel-input/build/css/intlTelInput.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-angular-notifications/styles/fabric.css"),
          path.join(process.cwd(), "src/assets/css/site-selection-style.css"),
          path.join(process.cwd(), "src/assets/css/ngx-datetime-picker.css"),
          path.join(process.cwd(), "node_modules/@okta/okta-signin-widget/dist/css/okta-sign-in.min.css"),
          path.join(process.cwd(), "src/assets/css/okta-login-style.scss")
        ],
        "test": /\.scss$|\.sass$/,
        "use": [
          "style-loader",
          {
            "loader": "raw-loader"
          },
          {
            "loader": "postcss-loader",
            "options": {
              "ident": "embedded",
              "plugins": postcssPlugins,
              "sourceMap": true
            }
          },
          {
            "loader": "sass-loader",
            "options": {
              "implementation": require("sass"),
              "sourceMap": true,
              "precision": 8,
              "includePaths": []
            }
          }
        ]
      },
      {
        "include": [
          path.join(process.cwd(), "src/assets/css/icons.scss"),
          path.join(process.cwd(), "src/assets/preloader.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap/dist/css/bootstrap.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/jscrollpane/style/jquery.jscrollpane.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/ladda/dist/ladda-themeless.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap-select/dist/css/bootstrap-select.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/select2/dist/css/select2.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/fullcalendar/dist/fullcalendar.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap-sweetalert/dist/sweetalert.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/summernote/dist/summernote.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/owl.carousel/dist/assets/owl.carousel.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/ionrangeslider/css/ion.rangeSlider.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/datatables/media/css/dataTables.bootstrap5.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/c3/c3.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/chartist/dist/chartist.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/nprogress/nprogress.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/jquery-steps/demo/css/jquery.steps.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/dropify/dist/css/dropify.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-linearicons/style.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-icomoon/style.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-awesome/css/font-awesome.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/cleanhtmlaudioplayer/src/player.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/cleanhtmlvideoplayer/src/player.css"),
          path.join(process.cwd(), "src/assets/modules/core/common/core.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/vendors/common/vendors.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/layouts/common/layouts-pack.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/themes/common/themes.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/menu-left/common/menu-left.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/menu-right/common/menu-right.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/top-bar/common/top-bar.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/footer/common/footer.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/pages/common/pages.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/ecommerce/common/ecommerce.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/apps/common/apps.cleanui.css"),
          path.join(process.cwd(), "src/assets/lib/emoji/emojionearea.css"),
          path.join(process.cwd(), "node_modules/intro.js/introjs.css"),
          path.join(process.cwd(), "node_modules/ag-grid-community/dist/styles/ag-grid.css"),
          path.join(process.cwd(), "node_modules/ag-grid-community/dist/styles/ag-theme-balham.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-base/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-buttons/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-popups/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/intl-tel-input/build/css/intlTelInput.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-angular-notifications/styles/fabric.css"),
          path.join(process.cwd(), "src/assets/css/site-selection-style.css"),
          path.join(process.cwd(), "src/assets/css/ngx-datetime-picker.css"),
          path.join(process.cwd(), "node_modules/@okta/okta-signin-widget/dist/css/okta-sign-in.min.css"),
          path.join(process.cwd(), "src/assets/css/okta-login-style.scss")
        ],
        "test": /\.less$/,
        "use": [
          "style-loader",
          {
            "loader": "raw-loader"
          },
          {
            "loader": "postcss-loader",
            "options": {
              "ident": "embedded",
              "plugins": postcssPlugins,
              "sourceMap": true
            }
          },
          {
            "loader": "less-loader",
            "options": {
              "sourceMap": true
            }
          }
        ]
      },
      {
        "include": [
          path.join(process.cwd(), "src/assets/css/icons.scss"),
          path.join(process.cwd(), "src/assets/preloader.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap/dist/css/bootstrap.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/jscrollpane/style/jquery.jscrollpane.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/ladda/dist/ladda-themeless.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap-select/dist/css/bootstrap-select.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/select2/dist/css/select2.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/fullcalendar/dist/fullcalendar.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/bootstrap-sweetalert/dist/sweetalert.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/summernote/dist/summernote.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/owl.carousel/dist/assets/owl.carousel.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/ionrangeslider/css/ion.rangeSlider.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/datatables/media/css/dataTables.bootstrap5.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/c3/c3.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/chartist/dist/chartist.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/nprogress/nprogress.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/jquery-steps/demo/css/jquery.steps.css"),
          path.join(process.cwd(), "src/assets/vendors/by_bower/dropify/dist/css/dropify.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-linearicons/style.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-icomoon/style.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/font-awesome/css/font-awesome.min.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/cleanhtmlaudioplayer/src/player.css"),
          path.join(process.cwd(), "src/assets/vendors/by_hands/cleanhtmlvideoplayer/src/player.css"),
          path.join(process.cwd(), "src/assets/modules/core/common/core.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/vendors/common/vendors.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/layouts/common/layouts-pack.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/themes/common/themes.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/menu-left/common/menu-left.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/menu-right/common/menu-right.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/top-bar/common/top-bar.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/footer/common/footer.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/pages/common/pages.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/ecommerce/common/ecommerce.cleanui.css"),
          path.join(process.cwd(), "src/assets/modules/apps/common/apps.cleanui.css"),
          path.join(process.cwd(), "src/assets/lib/emoji/emojionearea.css"),
          path.join(process.cwd(), "node_modules/intro.js/introjs.css"),
          path.join(process.cwd(), "node_modules/ag-grid-community/dist/styles/ag-grid.css"),
          path.join(process.cwd(), "node_modules/ag-grid-community/dist/styles/ag-theme-balham.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-base/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-buttons/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-popups/styles/fabric.css"),
          path.join(process.cwd(), "node_modules/intl-tel-input/build/css/intlTelInput.css"),
          path.join(process.cwd(), "node_modules/@syncfusion/ej2-angular-notifications/styles/fabric.css"),
          path.join(process.cwd(), "src/assets/css/site-selection-style.css"),
          path.join(process.cwd(), "src/assets/css/ngx-datetime-picker.css"),
          path.join(process.cwd(), "node_modules/@okta/okta-signin-widget/dist/css/okta-sign-in.min.css"),
          path.join(process.cwd(), "src/assets/css/okta-login-style.scss")
        ],
        "test": /\.styl$/,
        "use": [
          "style-loader",
          {
            "loader": "raw-loader"
          },
          {
            "loader": "postcss-loader",
            "options": {
              "ident": "embedded",
              "plugins": postcssPlugins,
              "sourceMap": true
            }
          },
          {
            "loader": "stylus-loader",
            "options": {
              "sourceMap": true,
              "paths": []
            }
          }
        ]
      },
      {
        "test": /\.ts$/,
        "loader": "@ngtools/webpack"
      }
    ]
  },
  "plugins": [
    new NoEmitOnErrorsPlugin(),
    new ScriptsWebpackPlugin({
      "name": "scripts",
      "sourceMap": true,
      "filename": "scripts.bundle.js",
      "scripts": [
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/jquery/dist/jquery.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/tether/dist/js/tether.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/jquery-ui/jquery-ui.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/popper/popper.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/bootstrap/dist/js/bootstrap.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/jquery-mousewheel/jquery.mousewheel.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/jscrollpane/script/jquery.jscrollpane.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/spin.js/spin.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/ladda/dist/ladda.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/bootstrap-select/dist/js/bootstrap-select.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/select2/dist/js/select2.full.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/html5-form-validation/dist/jquery.validation.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/jquery-typeahead/dist/jquery.typeahead.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/jquery-mask-plugin/dist/jquery.mask.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/autosize/dist/autosize.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/bootstrap-show-password/bootstrap-show-password.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/lib/emoji/emojionearea.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/moment/min/moment.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/eonasdan-bootstrap-datetimepicker/build/js/bootstrap-datetimepicker.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/fullcalendar/dist/fullcalendar.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/bootstrap-sweetalert/dist/sweetalert.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/remarkable-bootstrap-notify/dist/bootstrap-notify.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/summernote/dist/summernote.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/owl.carousel/dist/owl.carousel.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/ionrangeslider/js/ion.rangeSlider.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/nestable/jquery.nestable.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/datatables/media/js/jquery.dataTables.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/datatables/media/js/dataTables.bootstrap5.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/datatables-fixedcolumns/js/dataTables.fixedColumns.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/datatables-responsive/js/dataTables.responsive.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_hands/editable-table/mindmup-editabletable.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/d3/d3.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/c3/c3.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/chartist/dist/chartist.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/peity/jquery.peity.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/chartist-plugin-tooltip/dist/chartist-plugin-tooltip.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/jquery-countTo/jquery.countTo.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/nprogress/nprogress.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/jquery-steps/build/jquery.steps.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/chart.js/dist/Chart.bundle.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/chart.js/dist/chart-datalabels.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_bower/dropify/dist/js/dropify.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_hands/cleanhtmlaudioplayer/src/jquery.cleanaudioplayer.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/vendors/by_hands/cleanhtmlvideoplayer/src/jquery.cleanvideoplayer.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/node_modules/jspdf/dist/jspdf.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/node_modules/jspdf-autotable/dist/jspdf.plugin.autotable.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/lib/combodate.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/node_modules/intro.js/minified/intro.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/telemetry-event-widget/lib/event-widget.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/telemetry_agent/dist/agent.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/telemetry_agent/dist/agent_config.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/telemetry_agent/dist/agent_pagedata.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/telemetry_agent/dist/agent_support.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/lib/detect-private-browsing.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/lib/oidc-client/oidc-client.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/lib/okta-branding.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/lib/tenant-white-labeling.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/country/js/intlTelInput.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/country/js/verimail.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/lib/jquery.blockUI.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/lib/signaturepad/json2.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/lib/signaturepad/jquery.signaturepad.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/data-table-export/js/dataTables.buttons.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/data-table-export/js/buttons.flash.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/data-table-export/js/pdfmake.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/data-table-export/js/jszip.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/data-table-export/js/buttons.html5.min.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/src/assets/window_validation.js",
        "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25/node_modules/intl-tel-input/build/js/intlTelInput.js"
      ],
      "basePath": "/Users/<USER>/Downloads/src-desktop-client-11022025-1:7:25"
    }),
    new CopyWebpackPlugin([
      {
        "context": "src",
        "to": "",
        "from": {
          "glob": "assets/**/*",
          "dot": true
        }
      },
      {
        "context": "src",
        "to": "",
        "from": {
          "glob": "favicon.ico",
          "dot": true
        }
      },
      {
        "context": "src",
        "to": "",
        "from": {
          "glob": "service-worker.js",
          "dot": true
        }
      },
      {
        "context": "src",
        "to": "",
        "from": {
          "glob": "custom-configs.ts",
          "dot": true
        }
      }
    ], {
      "ignore": [
        ".gitkeep",
        "**/.DS_Store",
        "**/Thumbs.db"
      ],
      "debug": "warning"
    }),
    new ProgressPlugin(),
    new CircularDependencyPlugin({
      "exclude": /(\\|\/)node_modules(\\|\/)/,
      "failOnError": false,
      "onDetected": false,
      "cwd": projectRoot
    }),
    new NamedLazyChunksWebpackPlugin(),
    new HtmlWebpackPlugin({
      "template": "./src/index.html",
      "filename": "./index.html",
      "hash": false,
      "inject": true,
      "compile": true,
      "favicon": false,
      "minify": false,
      "cache": true,
      "showErrors": true,
      "chunks": "all",
      "excludeChunks": [],
      "title": "Webpack App",
      "xhtml": true,
      "chunksSortMode": function sort(left, right) {
        let leftIndex = entryPoints.indexOf(left.names[0]);
        let rightIndex = entryPoints.indexOf(right.names[0]);
        if (leftIndex > rightIndex) {
            return 1;
        }
        else if (leftIndex < rightIndex) {
            return -1;
        }
        else {
            return 0;
        }
    }
    }),
    new BaseHrefWebpackPlugin({}),
    new CommonsChunkPlugin({
      "name": [
        "inline"
      ],
      "minChunks": null
    }),
    new CommonsChunkPlugin({
      "name": [
        "vendor"
      ],
      "minChunks": (module) => {
                return module.resource
                    && (module.resource.startsWith(nodeModules)
                        || module.resource.startsWith(genDirNodeModules)
                        || module.resource.startsWith(realNodeModules));
            },
      "chunks": [
        "main"
      ]
    }),
    new SourceMapDevToolPlugin({
      "filename": "[file].map[query]",
      "moduleFilenameTemplate": "[resource-path]",
      "fallbackModuleFilenameTemplate": "[resource-path]?[hash]",
      "sourceRoot": "webpack:///"
    }),
    new CommonsChunkPlugin({
      "name": [
        "main"
      ],
      "minChunks": 2,
      "async": "common"
    }),
    new NamedModulesPlugin({}),
    new AngularCompilerPlugin({
      "mainPath": "main.ts",
      "platform": 0,
      "hostReplacementPaths": {
        "environments/environment.ts": "environments/environment.ts"
      },
      "sourceMap": true,
      "tsConfigPath": "src/tsconfig.json",
      "skipCodeGeneration": true,
      "compilerOptions": {}
    })
  ],
  "node": {
    "fs": "empty",
    "global": true,
    "crypto": "empty",
    "tls": "empty",
    "net": "empty",
    "process": true,
    "module": false,
    "clearImmediate": false,
    "setImmediate": false
  },
  "devServer": {
    "historyApiFallback": true
  }
};
